----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class ActivityTopicPopWindow : LuaUIBaseView
local ActivityTopicPopWindow = ui("ActivityTopicPopWindow")
local ActivityLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"
local EConsumeMouseWheel = import "EConsumeMouseWheel"
local EDescendantScrollDestination = import "EDescendantScrollDestination"
local AUTO_SCROLL_TIME = 5

function ActivityTopicPopWindow:Ctor()
    self._carouselIdx = 1
    self._carouselOldIdx = 1
    --root
    self._wtRootWindow = self:Wnd("WBP_CommonPopWindowsV2", CommonPopWindows)
    --页面图片滚动框
    self._wtImgScrollBox = UIUtil.WndScrollBox(self, "DFScrollBox_1", self._OnGetImgCount, self._OnProcessImgWidget)
    --切换按钮
    self._wtLeftbtn = self:Wnd("DFButton_167", UIButton)
    self._wtLeftbtn:Event("OnClicked", self._OnLeftbtnClick, self)
    self._wtRightbtn = self:Wnd("DFButton", UIButton)
    self._wtRightbtn:Event("OnClicked", self._OnRightbtnClick, self)

    self._wtIconBg  = self:Wnd("DFImage_101", UIImage)
    --滚动按钮
    self._wtCarousel1 = self:Wnd("WBP_DFCommonButtonCarousel", UIWidgetBase)
    self._wtCarouselbtn1 = self._wtCarousel1:Wnd("DFButton_Icon", UIButton)
    self._wtCarousel2 = self:Wnd("WBP_DFCommonButtonCarousel_1", UIWidgetBase)
    self._wtCarouselbtn2 = self._wtCarousel2:Wnd("DFButton_Icon", UIButton)
    self._wtCarousel3 = self:Wnd("WBP_DFCommonButtonCarousel_2", UIWidgetBase)
    self._wtCarouselbtn3 = self._wtCarousel3:Wnd("DFButton_Icon", UIButton)
    self._wtCarousel4 = self:Wnd("WBP_DFCommonButtonCarousel_3", UIWidgetBase)
    self._wtCarouselbtn4 = self._wtCarousel4:Wnd("DFButton_Icon", UIButton)
    self._wtCarousel5 = self:Wnd("WBP_DFCommonButtonCarousel_4", UIWidgetBase)
    self._wtCarouselbtn5 = self._wtCarousel5:Wnd("DFButton_Icon", UIButton)
    self.carouselTable = {
        self._wtCarousel1,
        self._wtCarousel2,
        self._wtCarousel3,
        self._wtCarousel4,
        self._wtCarousel5
    }
    self._carouselBtnTable = {
        self._wtCarouselbtn1,
        self._wtCarouselbtn2,
        self._wtCarouselbtn3,
        self._wtCarouselbtn4,
        self._wtCarouselbtn5
    }
    --绑定事件
    self._wtCarouselbtn1:Event("OnHovered", self.OnHoverCarousel, self, 1)
    self._wtCarouselbtn2:Event("OnHovered", self.OnHoverCarousel, self, 2)
    self._wtCarouselbtn3:Event("OnHovered", self.OnHoverCarousel, self, 3)
    self._wtCarouselbtn4:Event("OnHovered", self.OnHoverCarousel, self, 4)
    self._wtCarouselbtn5:Event("OnHovered", self.OnHoverCarousel, self, 5)
    self._wtCarouselbtn1:Event("OnUnHovered", self.OnUnHoverCarousel, self, 1)
    self._wtCarouselbtn2:Event("OnUnHovered", self.OnUnHoverCarousel, self, 2)
    self._wtCarouselbtn3:Event("OnUnHovered", self.OnUnHoverCarousel, self, 3)
    self._wtCarouselbtn4:Event("OnUnHovered", self.OnUnHoverCarousel, self, 4)
    self._wtCarouselbtn5:Event("OnUnHovered", self.OnUnHoverCarousel, self, 5)

    if DFHD_LUA == 1 then
        --PC端统一禁用掉用户的滚轮滑动
        self._wtImgScrollBox:SetConsumeMouseWheel(EConsumeMouseWheel.Never)
    end
end

-----------------------------------------------------生命周期-----------------------------------------------------
---@param activityID number
---@param descs table 弹窗描述
---@param PicUrls table 弹窗图片
---@param titles table 弹窗标题
---@param extraParam table 额外参数
function ActivityTopicPopWindow:OnInitExtraData(activityID, descs, PicUrls, titles, extraParam)
    self._activityID = activityID or 0
    self._descs = descs or {}
    self._PicUrls = PicUrls or {}
    self._titles = titles or {}
    self._extraParam = extraParam or {}
end

function ActivityTopicPopWindow:OnOpen()
    self._wtRootWindow:BindCloseCallBack(function()
        Facade.UIManager:CloseUI(self)
    end)
    self._wtImgScrollBox:RefreshAllItems()
    self:RefreshUI()
end

function ActivityTopicPopWindow:OnClose()
    if #self._PicUrls > 1 then
        Timer.FastRemoveObjTimer(self)
    end
    self.carouselTable = nil
    self._carouselBtnTable = nil
end

-------------------------------------------------------UI刷新-----------------------------------------------------
function ActivityTopicPopWindow:RefreshUI()
    --额外参数
    self:_SetScrollBoxSize(self._extraParam)

    if #self._PicUrls > 1 then
        self:ShowCarouselBtn(true, #self._PicUrls)
        self._wtLeftbtn:Visible()
        self._wtRightbtn:Visible()
        for i = 1, #self.carouselTable, 1 do
            self.carouselTable[i]:SetStyle(0)
        end
        self.carouselTable[self._carouselIdx]:SetStyle(1)
        self._wtImgScrollBox:SetScrollOffset(0)
        Timer.FastAddObjTimer(self, AUTO_SCROLL_TIME, 0, self.Update)
    else
        self:ShowCarouselBtn(false)
        self._wtLeftbtn:Collapsed()
        self._wtRightbtn:Collapsed()
    end
end

function ActivityTopicPopWindow:_SetScrollBoxSize(data)
    if data then
        --标题
        if data.title then
            self._wtRootWindow:SetTitle(data.title)
        end

        if data.iconX and data.iconY then
            local canvasSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(self._wtImgScrollBox)
            if canvasSlot then
                canvasSlot:SetSize(FVector2D(data.iconX, data.iconY))
            end
        end

        if data.frameX and data.frameY then
            if self._wtIconBg then
                local canvasSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(self._wtIconBg)
                if canvasSlot then
                    canvasSlot:SetSize(FVector2D(data.frameX, data.frameY))
                end
            end
        end
    end
end

function ActivityTopicPopWindow:Update()
    self._carouselOldIdx = self._carouselIdx
    if self._carouselIdx < #self._PicUrls then
        self:RefreshCarouselBtn(self._carouselIdx + 1)
    else
        self:RefreshCarouselBtn(1)
    end
end

function ActivityTopicPopWindow:ResetTimerHandle()
    Timer.FastRemoveObjTimer(self)
    Timer.FastAddObjTimer(self, AUTO_SCROLL_TIME, 0, self.Update)
end

function ActivityTopicPopWindow:RefreshCarouselBtn(tab)
    if ActivityLogic.RemoveObjTimerByWidget(self, self._wtImgScrollBox) then
        return
    end
    self._carouselOldIdx = self._carouselIdx
    self._carouselIdx = tab
    self.carouselTable[self._carouselOldIdx]:SetStyle(0)
    self.carouselTable[self._carouselIdx]:SetStyle(1)
    local child = self._wtImgScrollBox:GetItemByIndex(self._carouselIdx - 1)
    if child then
        -- self._wtImgScrollBox:EndInertialScrolling()
        self._wtImgScrollBox:ScrollWidgetIntoView(child, true, EDescendantScrollDestination.IntoView)
    end
end

function ActivityTopicPopWindow:ShowCarouselBtn(bIsShow, num)
    if bIsShow then
        for i = 1, #self.carouselTable, 1 do
            if i <= num then
                self.carouselTable[i]:Visible()
            else
                self.carouselTable[i]:Collapsed()
            end
        end
    else
        for i = 1, #self.carouselTable, 1 do
            self.carouselTable[i]:Collapsed()
        end
    end
end

------------------------------------------------------按钮逻辑-----------------------------------------------------
function ActivityTopicPopWindow:OnHoverCarousel(tab)
    self:RefreshCarouselBtn(tab)
    Timer.FastRemoveObjTimer(self)
end

function ActivityTopicPopWindow:OnUnHoverCarousel(tab)
    Timer.FastAddObjTimer(self, AUTO_SCROLL_TIME, 0, self.Update)
end

function ActivityTopicPopWindow:_OnLeftbtnClick()
    local tab = self._carouselIdx - 1
    if self._carouselIdx == 1 then
        tab = #self._PicUrls
    end
    self:RefreshCarouselBtn(tab)
    self:ResetTimerHandle()
end

function ActivityTopicPopWindow:_OnRightbtnClick()
    local tab = self._carouselIdx + 1
    if self._carouselIdx == #self._PicUrls then
        tab = 1
    end
    self:RefreshCarouselBtn(tab)
    self:ResetTimerHandle()
end

-------------------------------------------------------滚动框------------------------------------------------------
function ActivityTopicPopWindow:_OnGetImgCount()
    return #self._PicUrls
end

function ActivityTopicPopWindow:_OnProcessImgWidget(position, itemWidget)
    local index = position + 1
    itemWidget:SetTopicItem(self._extraParam)
    itemWidget:RefreshItemWidget(self._descs[index], self._PicUrls[index], self._titles[index])
end

function ActivityTopicPopWindow:OnNavBack()
    return false
end

return ActivityTopicPopWindow