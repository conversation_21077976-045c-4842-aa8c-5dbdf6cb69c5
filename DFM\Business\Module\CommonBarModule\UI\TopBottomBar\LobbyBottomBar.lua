----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCommonBar)
----- LOG FUNCTION AUTO GENERATE END -----------

local FAnchors = import "Anchors"
---@class LobbyBottomBar : LuaUIBaseView
local LobbyBottomBar = ui("LobbyBottomBar")
local CommonBarRegLogic = require "DFM.Business.Module.CommonBarModule.Logic.CommonBarRegLogic"
local CommonBarReddotLogic = require "DFM.Business.Module.CommonBarModule.Logic.CommonBarReddotLogic"
local Announcement = require "DFM.Business.Module.ChatModule.UI.Announcement"

local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"
local ButtonIdConfig  = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.ButtonIdConfig"
local ButtonIdEnum = ButtonIdConfig.Enum
local UILockData = require "DFM.Business.DataStruct.UIDataStruct.UILockData"
local CommonBarUnlockLogic = require "DFM.Business.Module.CommonBarModule.Logic.CommonBarUnlockLogic"
function LobbyBottomBar:Ctor()
	--聊天栏
    self._wtHallBtn = self:Wnd("WBP_Hall_Button",LuaUIBaseView)
	self._wtMossPanel=self:Wnd("WBP_Moss_Main_62", LuaUIBaseView)
	self._wtLine = self:Wnd("DFImage_3", UIWidgetBase)
    if IsHD() then
        self._wtHallBtn:Collapsed()
		if self._wtMossPanel then
			self._wtMossPanel:Collapsed()
			self._wtMossPanel:SetNotNeedInit(true)
		end
    end
	self._wtBtnsPanel=self:Wnd("DFHorizontalBox_74", UIWidgetBase)
	self._wtRecruitBtn=self:Wnd("WBP_CommonIconButton_Recruit", DFCommonButtonOnly)
	self._wtRecruitBtn:Event("OnClicked",self._OnRecuitBtnClicked,self)
	self._wtInviteBtn=self:Wnd("WBP_CommonIconButton_Invite", DFCommonButtonOnly)
	self._wtInviteBtn:Event("OnClicked",self._OnInviteBtnClicked,self)
	self._wtRankBtn=self:Wnd("WBP_CommonIconButton_Rank", DFCommonButtonOnly)
	self._wtRankBtn:Event("OnClicked",self._OnRankListBtnClicked,self)
	self._wtHbBtn = self:Wnd("DFHorizontalBox_btn", UIWidgetBase)

	self._reddotProxyTable = {}

	self._lockDataCacheList = {}
end

function LobbyBottomBar:OnInitExtraData()
end

local function _IsValidTabInfo(info)
    return info and info.tabTxtList and next(info.tabTxtList)
end

function LobbyBottomBar:UpdateBarByCurStackUI(curUIInst)
	if not hasdestroy(curUIInst) then
        local tabGroupRegInfo = CommonBarRegLogic.GetStackUIBottomBarTabGroupRegInfoProcess(curUIInst)
        if _IsValidTabInfo(tabGroupRegInfo) then
            self:Show(false)
            self:SetBottomTabGroup(tabGroupRegInfo)
        else
            self:Hide(true, true)
        end
	else
		self:Hide(true, true)
	end
end

local function _IsValidTabInfo(info)
    return info and info.tabTxtList and next(info.tabTxtList)
end

function LobbyBottomBar:OnOpen()
	loginfo("LobbyBottomBar:OnOpen")
end

function LobbyBottomBar:OnClose()
    loginfo("LobbyBottomBar:OnClose")
	self:ReleaseLockData()
    self:RemoveAllLuaEvent()
	if self._wtRankingListReddot then
		Module.ReddotTrie:UnRegisterStaticReddotDot(self._wtRankingListReddot)
	end
	self._wtRankingListReddot = nil
end

function LobbyBottomBar:OnShow()
	loginfo("LobbyBottomBar:OnShow")
	if not IsHD() then
		self:AddLuaEvent(Module.Moss.Config.evtOnMossClose,self._OnMossClose,self)
		if self._wtMossPanel then
			self._wtMossPanel:Show()
		end
	end
	self:_CheckRankingListOpen()

	if not self._wtRankingListReddot then
		local RankingListData = Module.ReddotTrie:GetReddotData(EReddotTrieObserverType.RankingList, "RankingListMessage_Type0")
		if RankingListData then
			self._wtRankingListReddot = Module.ReddotTrie:RegisterStaticReddotDot(self._wtRankBtn, {{reddotData=RankingListData,reddotStyle={
				placeMode=EReddotPlaceMode.LeftTop}}})
			loginfo("TopBarAvatarMainHD:RegisterReddots Rankinglist Flag = ", RankingListData.reddotFlag)
		else
			logerror("TopBarAvatarMainHD:RegisterReddots RankingListdata is nil")
		end
	end

end

function LobbyBottomBar:OnHide()
    loginfo("LobbyBottomBar:OnHide")
	if not IsHD() then
		self:RemoveLuaEvent(Module.Moss.Config.evtOnMossClose)
	end
    self:ReleaseReddotProxies()
end

function LobbyBottomBar:_CheckRankingListOpen()
    if Facade.ModuleManager:IsModuleValid("RankingList") then
        if Module.RankingList:CheckRankingListOpen() then
            self._wtRankBtn:Visible()
        else
            self._wtRankBtn:Collapsed()
        end
    end
end

function LobbyBottomBar:ReleaseReddotProxies()
    for _, proxy in pairs(self._reddotProxyTable) do
        if proxy.bNewReddotTrie then
            Module.ReddotTrie:UnRegisterStaticReddotDot(proxy[2])
        else
			-- 已废弃2024/6/25
            -- Module.Reddot:UnRegisterIndirect(proxy[1], proxy[2])
			logerror('LobbyBottomBar:ReleaseReddotProxies 旧红点遗址')
        end
    end
    self._reddotProxyTable = {}
end

---@param topTabGroupRegInfo table
function LobbyBottomBar:SetBottomTabGroup(topTabGroupRegInfo, overrideIndex, bInvoke)
	topTabGroupRegInfo = CommonBarReddotLogic.NormalizeTopTabGroupRegInfo(topTabGroupRegInfo)
	local curCacheRegInfo = self._cacheBottomRegInfo
	Facade.UIManager:RemoveSubUIByParent(self, self._wtHbBtn)
	self:ReleaseReddotProxies()

	local maxVisibleBtn
	if _IsValidTabInfo(topTabGroupRegInfo) then
		if curCacheRegInfo ~= topTabGroupRegInfo then
			self:ReleaseLockData()

			topTabGroupRegInfo.imgPathList = topTabGroupRegInfo.imgPathList or {}
			local idx = 0
			for k, tabIdx in ipairs(topTabGroupRegInfo.tabIdxListGen) do
				local tabTxt = topTabGroupRegInfo.tabTxtList[k]
				local weakUIIns = Facade.UIManager:AddSubUI(self, UIName2ID.BottomBarButton, self._wtHbBtn, k)
				local uiIns = getfromweak(weakUIIns)
				if uiIns then
					uiIns:SetBtnTitle(tabTxt)
					if topTabGroupRegInfo.tabTxtEnList and topTabGroupRegInfo.tabTxtEnList[k] then
						uiIns:SetBtnEnTitle(topTabGroupRegInfo.tabTxtEnList[k])
					end
					local fSuccessOperaCallbackIns = CreateCallBack(function (self)
						if topTabGroupRegInfo.fCallbackIns then
							topTabGroupRegInfo.fCallbackIns(k, 0)
						end
					end, self)

					if topTabGroupRegInfo.lockDataList then
						local lockData = topTabGroupRegInfo.lockDataList[k]
						local firstLockID = lockData.firstLockID or false
						local secondLockID = lockData.secondLockID or false
						local uiNavID = lockData.uiNavID
						logwarning('Init Lock State: tab idx:', k, 'firstLockID:', firstLockID, 'secondLockID:', secondLockID, 'uiNavID:', uiNavID)
					   
						CommonBarUnlockLogic.InitBarBtnClickedState(uiIns, fSuccessOperaCallbackIns, lockData)
					end
					if uiIns:IsVisible() and uiIns:GetRenderOpacity() > 0 then
						maxVisibleBtn = uiIns
					end

					local placeOperation1 = function(reddot)
						local anchors = FAnchors()
						anchors.Minimum = FVector2D(1, 0)
						anchors.Maximum = FVector2D(1, 0)
						local canvasSlot = reddot.Slot
						canvasSlot:SetAnchors(anchors)
						canvasSlot:SetPosition(FVector2D(0, 0))
						canvasSlot:SetAlignment(FVector2D(1, 0))
						-- reddot:SetRenderScale(FVector2D(0.8,0.8))
					end
					---@type ReddotTrieTabRegItem
					local reddotTrieRegItem = topTabGroupRegInfo.reddotTrieRegItemList[k]
					if topTabGroupRegInfo.reddotTrieRegItemList and next(topTabGroupRegInfo.reddotTrieRegItemList) and not self._reddotProxyTable[k] then
						if next(reddotTrieRegItem) then
							local newProxy =
								Module.ReddotTrie:RegisterStaticReddotDotWithConfig(
								uiIns:BP_GetReddotPanel(),
								reddotTrieRegItem.reddotDataConfigWithStyleList
							)
							newProxy:SetDotPlaceMode(
								EReddotPlaceMode.Custom,
								placeOperation1
							)
							self._reddotProxyTable[k] = {
								reddotTrieRegItem.uiNavId,
								newProxy,
								bNewReddotTrie = topTabGroupRegInfo.bNewReddotTrie
							}
						end
					end
					uiIns:SetLastBtnShow(false)
				end

				--- 锁定状态刷新，新增隐藏
				local lockDataCache = self._lockDataCacheList
				if not lockDataCache[k] then
					if topTabGroupRegInfo.lockDataList and topTabGroupRegInfo.lockDataList[k] then
						local lockData = topTabGroupRegInfo.lockDataList[k]
						if next(lockData) then
							local newLockData =
								UILockData:NewIns(
								nil,
								lockData.firstLockID,
								lockData.secondLockID,
								lockData.uiNavID,
								lockData.fFailedCallback,
								lockData.failedCallbackCaller,
								lockData.fCustomLockChecker,
								lockData.customLockCheckerCaller
							)
							lockDataCache[k] = newLockData
						end
					end
				end
				CommonBarUnlockLogic.TryUpdateWithLockData(self._lockDataCacheList, k, uiIns)

				Module.Guide:AddGuideWidgetProxy(Module.Guide.Config.EGuideProxyWidget[string.format("guideProxyBottomBarPrimary%d", idx)], uiIns)
				idx = idx + 1
			end
			if maxVisibleBtn and maxVisibleBtn.SetLastBtnShow then
				maxVisibleBtn:SetLastBtnShow(true)
			end
		end
		self._wtHbBtn:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self._cacheBottomRegInfo = topTabGroupRegInfo
	else
		self._wtHbBtn:SetVisibility(ESlateVisibility.Collapsed)
		self._cacheBottomRegInfo = nil
	end
end

--显示隐藏聊天按钮
function LobbyBottomBar:ShowChatPanel(bShow)
    bShow=setdefault(bShow,true)
	if bShow then
		self._wtHallBtn:SelfHitTestInvisible()
	else
		self._wtHallBtn:Collapsed()
	end
end

function LobbyBottomBar:_OnRecuitBtnClicked()
	Module.Recruit:ShowMainPanel()
	local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
	if DFHD_LUA == 0 then
		if armedForceMode == EArmedForceMode.SOL then
			LogAnalysisTool.SignButtonClicked(ButtonIdEnum.SOLLobbyRecruitment)
		else
			LogAnalysisTool.SignButtonClicked(ButtonIdEnum.MPLobbyRecruitment)
		end
	else
		--- TODO 暂无经分
	end
end

function LobbyBottomBar:_OnInviteBtnClicked()
	Module.Social:OpenInvitePanel()
	local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
	if DFHD_LUA == 0 then
		if armedForceMode == EArmedForceMode.SOL then
			LogAnalysisTool.SignButtonClicked(ButtonIdEnum.SOLLobbyTeam)
		else
			LogAnalysisTool.SignButtonClicked(ButtonIdEnum.MPLobbyTeam)
		end
	else
		--- TODO 暂无经分
	end
end

function LobbyBottomBar:_OnRankListBtnClicked()
	Module.RankingList:ShowRankListMainPanel()
end

function LobbyBottomBar:ShowBtnsPanel(bShow)
	bShow = setdefault(bShow,true)
	if bShow then
		self._wtBtnsPanel:SelfHitTestInvisible()
	else
		self._wtBtnsPanel:Collapsed()
	end
end

function LobbyBottomBar:ReleaseLockData()
	self._lockDataCacheList = {}
end

function LobbyBottomBar:_OnMossClose(bClose)
	if bClose then
		self._wtLine:Collapsed()
	else
		self._wtLine:SelfHitTestInvisible()
	end
end

return LobbyBottomBar