local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMInventory)
-- 该文件的命名与位置是临时的
-- 该文件用来单独抽象出所有跨模块的仓库背包相关的导航逻辑
-- she2版本没有时间完全重构，只将新feature写入
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local ItemBaseTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemBaseTool"

-- local WarehouseWithTab = require "DFM.Business.Module.InventoryModule.UI.MainV2.WarehouseWithTab"
local WidgetUtil = require("DFM.YxFramework.Util.WidgetUtil")
-- local WarehouseEquipPanel = require "DFM.Business.Module.InventoryModule.UI.MainV2.WarehouseEquipPanel"
--背包
-- local WarehouseEquipPanel_HD = require "DFM.Business.Module.InventoryModule.UIHD.WarehouseEquipPanel_HD"

local InventoryNavManager = {}
-- 读表时检查值的SlotType与key是否一致
local mt = {
    __index = function(t, key)
        local value = rawget(t, key)
        if value and value.itemSlot and value.itemSlot.SlotType == key and not hasdestroy(value) then
            return value
        else
            rawset(t, key, nil)
            return nil
        end
    end
}

setmetatable(InventoryNavManager, mt)

InventoryNavManager.allInvSlotView = {
}

-- 尸体中的SlotView根据SlotGroup进行分类，不能合入AllInvSlotView
InventoryNavManager.deadBodyView = {
}
-- 散点搜索背包胸挂是也会有和身上一样的背包胸挂slot，不能合入AllInvSlotView
InventoryNavManager.nearbyView = {
}

InventoryNavManager.pickUpType = nil
InventoryNavManager.interactors = nil

function InventoryNavManager.Reset()
    InventoryNavManager.allInvSlotView = {
    }
    
    
    -- 尸体中的SlotView根据SlotGroup进行分类，不能合入AllInvSlotView
    InventoryNavManager.deadBodyView = {
    }
    -- 散点搜索背包胸挂是也会有和身上一样的背包胸挂slot，不能合入AllInvSlotView
    InventoryNavManager.nearbyView = {
    }
    ---------------------- 抽象导航组 ----------------------
    InventoryNavManager.equipNavGroup = nil
    InventoryNavManager.backpackNavGroup = nil
    InventoryNavManager.warehouseOptionTabNavGroup = nil
    InventoryNavManager.warehouseNavGroup = nil
    -- 局内
    InventoryNavManager.deadBodyNavGroup = nil
    InventoryNavManager.litterNavGroup = nil
    InventoryNavManager.nearbyNavGroup = nil
    InventoryNavManager.discardNavGroup = nil
    
    ---------------------- 抽象控件 ----------------------
    -- 通用
    InventoryNavManager.equipPanel = nil
    InventoryNavManager.backpackPanel = nil
    -- 局外右侧
    InventoryNavManager.warehouseWithTabPanel = nil
    InventoryNavManager.warehouseTabPanel = nil
    InventoryNavManager.warehousePanel = nil
    -- 局内右侧
    InventoryNavManager.deadbodyPanel = nil
    InventoryNavManager.litterPanel = nil
    -- 收藏室展示台
    InventoryNavManager.DIYPanel = nil
    
    -----
    InventoryNavManager.defaultNavTarget = nil
    -- 区分当前的物品转移操作是否是快速转移，如果为假，则是拖拽
    InventoryNavManager.bIsQuickCarry = false
    -- 当前聚焦的ItemView（可能已经被回收）
    -- todo 现有代码中，在Inventory Field里也有一些情况下聚焦控件的引用，之后统一成这里
    InventoryNavManager._CurrentFocusItemView = nil
    -- 当前拖拽的dragdrop中的item
    InventoryNavManager._CurrentDragItemView = nil
    -- 使用快速转移/快速装备后的下一个焦点 
    InventoryNavManager.nextTargetItem = nil
    InventoryNavManager.pickUpType = nil
    InventoryNavManager.interactors = nil
end

-- region 设置变量
function InventoryNavManager.SetCurrentDragItem(targetItem)
    if targetItem == nil then
        InventoryNavManager._CurrentDragItemView = nil
        return
    end
    InventoryNavManager._CurrentDragItemView = makeweak(targetItem)
end

function InventoryNavManager.GetCurrentDragItem()
    return getfromweak(InventoryNavManager._CurrentDragItemView)
end

function InventoryNavManager.SetCurrentFocusItemView(targetItem)
    if targetItem == nil then
        InventoryNavManager._CurrentFocusItemView = nil
        return
    end
    InventoryNavManager._CurrentFocusItemView = makeweak(targetItem)
end

function InventoryNavManager.GetCurrentFocusItemView()
    return getfromweak(InventoryNavManager._CurrentFocusItemView)
end
-- endregion

--region 导航方法

--- 聚焦到当前页面的默认焦点，一般只在首次打开页面时和特定聚焦行为失效退化时调用
---@return boolean bSuccess # 成功调用任意FocusWithTimer时返回true
function InventoryNavManager.FocusToDefaultFocusPoint()
    if not IsHD() then
        return false
    end
    local InvSlotViewType = Module.Inventory:GetInvSlotViewType()

    if ItemOperaTool.CheckIsDIYCabinetPanel() then
        ---------------------------------------收藏室---------------------------------------
        -- 收藏室只有DIY页面会用到仓库道具导航，优先聚焦当前仓库页的第一个道具，失败则聚焦到展示台第一格
        InventoryNavManager.FocusFirstItemBySlotView(InventoryNavManager.allInvSlotView[ESlotType.MainContainer])
        return true
    end

    if ItemOperaTool.CheckRunWarehouseLogic() then
        ---------------------------------------仓库---------------------------------------
        if InvSlotViewType == Module.Inventory.Config.EInvSlotViewType.Default or InvSlotViewType == Module.Inventory.Config.EInvSlotViewType.Assembly_QuickOperation  then
            -- 对于局外仓库和配装，默认聚焦到胸挂槽位
            if InventoryNavManager.allInvSlotView[ESlotType.ChestHanging] and InventoryNavManager.backpackNavGroup then
                InventoryNavManager.FocusWithTimer(InventoryNavManager.allInvSlotView[ESlotType.ChestHanging])
                return true;
            end
        end
    else
            ---------------------------------------Looting---------------------------------------
            -- 对于局内，会根据当前都有哪些槽位，及其中是否有道具，动态进行选择
            -- 如果是尸体，聚焦到主武器位
                if InventoryNavManager.CheckIsSearchingDeadBody() then
                -- 第一次打开死亡盒的时候，需要等待拉取一些数据才能聚焦到主武器位
                InventoryNavManager.FocusWithTimer(InventoryNavManager.deadBodyView[ESlotType.MainWeaponLeft],0.25)
                return true;
            else
                -- 如果是容器，则确认是否有道具，尝试聚焦到第一个道具。
                local TragetPanel = InventoryNavManager.CheckIsSearchinLitter()
                if TragetPanel and InventoryNavManager.litterNavGroup then
                    -- 这部分功能已确定移入she3
                    -- local GID = TragetPanel.itemSlot:GetFirstItem() 
                    -- if GID ~= nil then
                    --     local TargetView = TragetPanel.FindItemViewByGID(GID)
                    --     InventoryNavManager.FocusFirstItemBySlotView(TargetView)
                    --     return true;
                    -- end
                else
                    -- 不是散点道具就是地上的背包胸挂
                    local TragetPanel = InventoryNavManager.nearbyView[ESlotType.ChestHanging] or InventoryNavManager.nearbyView[ESlotType.Bag]
                    if  TragetPanel and InventoryNavManager.litterNavGroup then
                        InventoryNavManager.FocusWithTimer(TragetPanel)
                        return true;
                    end
                end
            end
            -- 如果是单纯开背包或容器为空，则聚焦自身胸挂
            if InventoryNavManager.allInvSlotView[ESlotType.ChestHanging] and InventoryNavManager.backpackNavGroup then
                InventoryNavManager.FocusWithTimer(InventoryNavManager.allInvSlotView[ESlotType.ChestHanging])
                return true;
            end
    end
    logerror("InventoryNavManager.FocusToDefaultFocusPoint: failed to focus to default focus point")
    return false;
end

--- 聚焦到当前页面的默认焦点，一般只在首次打开页面时和特定聚焦行为失效退化时调用
---@param itemView target # 聚焦目标，可以是itemView或blankItem
---@param number overrideSec # 覆写聚焦延迟时间
---@param boolean hideAfterFocus # 如果target隐藏，是否先聚焦后再重新隐藏
function InventoryNavManager.FocusWithTimer(target,overrideSec,hideAfterFocus)
    hideAfterFocus = setdefault(hideAfterFocus, false)
    if not IsHD() then
        return
    end
    -- 先继承she2的笨方法写，之后改闭包
    InventoryNavManager.defaultNavTarget = target
    InventoryNavManager.hideAfterFocus = hideAfterFocus
    if InventoryNavManager._checkTimerHandle then
        InventoryNavManager._checkTimerHandle:Stop()
        InventoryNavManager._checkTimerHandle = nil
    end
    if overrideSec ~=nil then
        InventoryNavManager._checkTimerHandle = Timer:NewIns(overrideSec, 0)
    else
        InventoryNavManager._checkTimerHandle = Timer:NewIns(0.034, 0)
    end

    InventoryNavManager._checkTimerHandle:AddListener(InventoryNavManager._TryFocusToItem,InventoryNavManager)
    InventoryNavManager._checkTimerHandle:Start()
    -- WidgetUtil.SetUserFocusToWidget(targetItemView, true)
end

function InventoryNavManager.FocusFirstItemBySlotView(inSlotView)
    if not IsHD() then
        return
    end
    local GID = inSlotView.itemSlot:GetFirstItem()
    if GID ~= 0 then
        local targetItem = Server.InventoryServer:GetItemByGid(GID,inSlotView.itemSlot:GetSlotGroup())
        if targetItem then
                targetItemView = inSlotView:GetNavigationItemView(targetItem)
                if targetItemView then
                    InventoryNavManager.FocusWithTimer(targetItemView)
                    return true
                end
        end
    end
    return false
end

function InventoryNavManager.FocusFirstBlankItem(inSlotView)
    if not IsHD() then
        return
    end
    local FirstBlankItem = inSlotView:GetFirstBlankItem()
    if FirstBlankItem then
        InventoryNavManager.FocusWithTimer(FirstBlankItem,0.034)
        return true
    end
    return false
end


function InventoryNavManager:_TryFocusToItem()
    if not IsHD() then
        return
    end
	if InventoryNavManager.defaultNavTarget ~= nil then
        loginfo("InventoryNavManager:_TryFocusToItem:", InventoryNavManager.defaultNavTarget._debugName)
        if InventoryNavManager.hideAfterFocus then
            InventoryNavManager.defaultNavTarget:SetVisibility(ESlateVisibility.Visible)
        end
		WidgetUtil.SetUserFocusToWidget(InventoryNavManager.defaultNavTarget, true)
        if InventoryNavManager.hideAfterFocus then
            InventoryNavManager.defaultNavTarget:SetVisibility(ESlateVisibility.Collapsed)
        end
	end
	if InventoryNavManager._checkTimerHandle then
		InventoryNavManager._checkTimerHandle:Stop()
        InventoryNavManager._checkTimerHandle = nil
    end
end


-- 传入ItemBase，聚焦到ItemView
function InventoryNavManager.FocusByItem(targetItem,overrideSec)
    if not IsHD() then
        return
    end
    logerror("TAPD -- InventoryNavManager.lua:136 -- Hot Fix")
    if targetItem == nil or targetItem.InSlot == nil then
        InventoryNavManager.FocusToDefaultFocusPoint()
        return false;
    end
    local SlotType = targetItem.InSlot.SlotType
    local SlotGroup = targetItem.InSlot:GetSlotGroup()
    if SlotGroup == ESlotGroup.DeadBody then
        -- 导航道具在尸体上
        local SlotView = InventoryNavManager.deadBodyView[SlotType]
        if SlotView then
            local ItemView = SlotView:GetViewByItem(targetItem)
            if ItemView then
                InventoryNavManager.FocusWithTimer(ItemView,overrideSec)
            end
            return true;
        end
    else
        if SlotGroup == ESlotGroup.Nearby then
            local SlotView = InventoryNavManager.nearbyView[SlotType]
            if SlotView then
                local ItemView = SlotView:GetViewByItem(targetItem)
                if ItemView then
                    InventoryNavManager.FocusWithTimer(ItemView,overrideSec)
                end
                return true;
            end
        else
            local SlotView = InventoryNavManager.allInvSlotView[SlotType]
            if SlotView then
                local ItemView = nil
                if ItemOperaTool.CheckRunWarehouseLogic() then
                    logerror("TAPD -- InventoryNavManager.lua:163 -- Hot Fix")
                    if not (SlotType < ESlotType.DepositoryStart or SlotType > ESlotType.DepositoryEnd) then
                        ItemView = SlotView:GetNavigationItemView(targetItem)
                    else
                        ItemView = SlotView:GetViewByItem(targetItem)
                    end
                else
                    ItemView = SlotView:GetViewByItem(targetItem)
                end
                if ItemView then
                    InventoryNavManager.FocusWithTimer(ItemView,overrideSec)
                end
                return true;
            end
        end
    end
    -- 保底，聚焦回默认焦点
    InventoryNavManager.FocusToDefaultFocusPoint()
end

-- 武器护甲槽的遍历顺序
InventoryNavManager.WeaponSort = 
{
    [ESlotType.Helmet]=true,
    [ESlotType.BreastPlate]=true,
    [ESlotType.Pistrol]=true,
    [ESlotType.MainWeaponLeft]=true,
    [ESlotType.MainWeaponRight]=true,

}

-- 从容器格起始时的遍历顺序
InventoryNavManager.BackpackSort = 
{
    [ESlotType.ChestHangingContainer]=ESlotType.ChestHanging,
    [ESlotType.ChestHanging]=ESlotType.Pocket,
    [ESlotType.Pocket]=ESlotType.BagContainer,
    [ESlotType.BagContainer]=ESlotType.Bag,
    [ESlotType.Bag]=ESlotType.SafeBoxContainer,
    [ESlotType.SafeBoxContainer]=ESlotType.SafeBox,
}


--LT/RT切换到背包分页时的聚焦
function InventoryNavManager.FoucsBackPackAfterSwitchTab(startItemSlot)
    if not IsHD() then
        return
    end
    local bIsInYmode = Module.Inventory.Field:GetIsYMode()
    loginfo("InventoryNavManager.FoucsBackPackAfterSwitchTab: startItemSlot: ", startItemSlot.name,"bIsInYmode: ", bIsInYmode)
    local currentSlotType = ESlotType.ChestHangingContainer
    local startGroup = startItemSlot.GetSlotGroup and startItemSlot:GetSlotGroup()
    local targetSlotViews = InventoryNavManager.allInvSlotView
    -- 只有局内拖拽模式，并且导航起点不是第一栏中的slot时，才会从同类容器开始查找
    if not ItemOperaTool.CheckRunWarehouseLogic() and not InventoryNavManager.WeaponSort[startItemSlot.SlotType] then
        currentSlotType = startItemSlot.SlotType
        loginfo("InventoryNavManager.FoucsBackPackAfterSwitchTab: currentSlotType: ", currentSlotType)
    end


    while targetSlotViews[currentSlotType] or InventoryNavManager.BackpackSort[currentSlotType] do
        if targetSlotViews[currentSlotType] then
            local currentSlotView = targetSlotViews[currentSlotType]
            local currentSlot = Server.InventoryServer:GetSlot(currentSlotType,ESlotGroup.Player)
            -- 分别处理装备槽位和网格槽位
            if currentSlot:IsEquipSlot() then
                -- 需要判断是否有装备道具
                if currentSlot:GetEquipItem() then
                    InventoryNavManager.FocusWithTimer(currentSlotView)
                    return true
                end
            end
            if currentSlot:IsContainerSlot() then
                if bIsInYmode then
                    loginfo("InventoryNavManager.FoucsBackPackAfterSwitchTab: FocusFirstBlankItem")
                    if InventoryNavManager.FocusFirstBlankItem(currentSlotView) then
                        return true
                    end
                else
                    loginfo("InventoryNavManager.FoucsBackPackAfterSwitchTab: FocusFirstItemBySlotView")
                    if InventoryNavManager.FocusFirstItemBySlotView(currentSlotView) then
                        return true
                    end
                end
                
            end
        end
        currentSlotType = InventoryNavManager.BackpackSort[currentSlotType]
    end
    logerror("InventoryNavManager.TryFocusAfterSwitchTab:Failed target navgroup: backpack")
end

--LT/RT切换到死亡箱分页时的聚焦
function InventoryNavManager.FoucsDeadBodyAfterSwitchTab(startItemSlot)
    if not IsHD() then
        return
    end
    -- 先处理拖拽
    local bIsInYmode = Module.Inventory.Field:GetIsYMode()
    loginfo("InventoryNavManager.FoucsBackPackAfterSwitchTab: startItemSlot: ", startItemSlot.name,"bIsInYmode: ", bIsInYmode)

    local item = InventoryNavManager.GetCurrentDragItem()
    if item ~= nil then
        local itemFeatureEquipment = item and item:GetFeature(EFeatureType.Equipment)
        local isPlayerEquipment = itemFeatureEquipment and itemFeatureEquipment:IsPlayerEquipment()
        local itemFeatureWeapon = item and item:GetFeature(EFeatureType.Weapon)
        local isWeapon = itemFeatureWeapon and itemFeatureWeapon:IsWeapon()
        if isPlayerEquipment then
            if itemFeatureEquipment:IsHelmet() then
                InventoryNavManager.FocusWithTimer(InventoryNavManager.deadBodyView[ESlotType.Helmet])
                return true
            end
            if itemFeatureEquipment:IsBreastPlate() then
                InventoryNavManager.FocusWithTimer(InventoryNavManager.deadBodyView[ESlotType.BreastPlate])
                return true
            end

        end
        if isWeapon then
            InventoryNavManager.FocusWithTimer(InventoryNavManager.deadBodyView[ESlotType.MainWeaponLeft])
            return true
        end
    end

    local currentSlotType = ESlotType.ChestHangingContainer
    local startGroup = startItemSlot.GetSlotGroup and startItemSlot:GetSlotGroup()
    local targetSlotViews = InventoryNavManager.deadBodyView

    if not ItemOperaTool.CheckRunWarehouseLogic() then
        currentSlotType = startItemSlot.SlotType
        loginfo("InventoryNavManager.FoucsBackPackAfterSwitchTab: currentSlotType: ", currentSlotType)
    end

    if not ItemOperaTool.CheckRunWarehouseLogic() then
        if startGroup == ESlotGroup.Player then
            targetSlotViews = InventoryNavManager.deadBodyView
        end
    end


    while targetSlotViews[currentSlotType] or InventoryNavManager.BackpackSort[currentSlotType] do
        if targetSlotViews[currentSlotType] then
            local currentSlotView = targetSlotViews[currentSlotType]
            local currentSlot = Server.InventoryServer:GetSlot(currentSlotType,ESlotGroup.Player)
            -- 分别处理装备槽位和网格槽位
            if currentSlot:IsEquipSlot() then
                -- 需要判断是否有装备道具
                if currentSlot:GetEquipItem() then
                    InventoryNavManager.FocusWithTimer(currentSlotView)
                    return true
                end
            end
            if currentSlot:IsContainerSlot() then
                if bIsInYmode then
                    loginfo("InventoryNavManager.FoucsBackPackAfterSwitchTab: FocusFirstBlankItem")
                    if InventoryNavManager.FocusFirstBlankItem(currentSlotView) then
                        return true
                    end
                else
                    loginfo("InventoryNavManager.FoucsBackPackAfterSwitchTab: FocusFirstItemBySlotView")
                    if InventoryNavManager.FocusFirstItemBySlotView(currentSlotView) then
                        return true
                    end
                end
                
            end
        end
        currentSlotType = InventoryNavManager.BackpackSort[currentSlotType]
    end
    logerror("InventoryNavManager.TryFocusAfterSwitchTab:Failed target navgroup: DeadBody")
end


function InventoryNavManager.FoucsWarehouseAfterSwitchTab(startItemSlot)
    if not IsHD() then
        return
    end
    if InventoryNavManager.warehouseNavGroup then
        WidgetUtil.TryFocusDefaultWidgetByGroup(InventoryNavManager.warehouseNavGroup)
    end
end

function InventoryNavManager.FoucsEquipmentAfterSwitchTab(startItemSlot)
    if not IsHD() then
        return
    end
    -- 先处理拖拽
    local item = InventoryNavManager.GetCurrentDragItem()
    if item ~= nil then
        local itemFeatureEquipment = item and item:GetFeature(EFeatureType.Equipment)
        local isPlayerEquipment = itemFeatureEquipment and itemFeatureEquipment:IsPlayerEquipment()
        local itemFeatureWeapon = item and item:GetFeature(EFeatureType.Weapon)
        local isWeapon = itemFeatureWeapon and itemFeatureWeapon:IsWeapon()
        if isPlayerEquipment then
            if itemFeatureEquipment:IsHelmet() then
                InventoryNavManager.FocusWithTimer(InventoryNavManager.allInvSlotView[ESlotType.Helmet])
                return true
            end
            if itemFeatureEquipment:IsBreastPlate() then
                InventoryNavManager.FocusWithTimer(InventoryNavManager.allInvSlotView[ESlotType.BreastPlate])
                return true
            end

        end
        if isWeapon then
            InventoryNavManager.FocusWithTimer(InventoryNavManager.allInvSlotView[ESlotType.MainWeaponLeft])
            return true
        end
    end

    local currentSlotType = ESlotType.Helmet
    if not ItemOperaTool.CheckRunWarehouseLogic() then
        if InventoryNavManager.WeaponSort[startItemSlot.SlotType] then
            currentSlotType = startItemSlot.SlotType
            loginfo("InventoryNavManager.FoucsBackPackAfterSwitchTab: currentSlotType: ", currentSlotType)
            InventoryNavManager.FocusWithTimer(InventoryNavManager.allInvSlotView[currentSlotType])
            return true
        end 
        
    end

    if InventoryNavManager.equipNavGroup then
        InventoryNavManager.FocusWithTimer(InventoryNavManager.allInvSlotView[currentSlotType])
        return true
    end
end

--LT/RT切换分页时的默认聚焦
function InventoryNavManager.TryFocusAfterSwitchTab(navGroup)
    -- 获取起点itemSlot
    local startItemView = InventoryNavManager.GetCurrentFocusItemView()
    local bUsingBlankItem = startItemView.bIsBlankItem
    -- todo方便调试，有问题时直接失焦
    if startItemView == nil then
        -- WidgetUtil.TryFocusDefaultWidgetByGroup(navGroup)
        return
    end
    local startItemSlot = nil
    
    if bUsingBlankItem then
        startItemSlot = startItemView.ownerItemSlot
    else
        startItemSlot = startItemView.item.InSlot
    end

    if ItemOperaTool.CheckRunWarehouseLogic() then
        ---------------------------------------仓库---------------------------------------
        -- 根据目标NavGroup不同，分别处理
        if InventoryNavManager.CheckIsSelfBackpackNavGroup(navGroup) then
            InventoryNavManager.FoucsBackPackAfterSwitchTab(startItemSlot)
        end
        if InventoryNavManager.CheckIsWarehouseNavGroup(navGroup) then
            InventoryNavManager.FoucsWarehouseAfterSwitchTab(startItemSlot)
        end
        if InventoryNavManager.CheckIsSelfEquipmentNavGroup(navGroup) then
            InventoryNavManager.FoucsEquipmentAfterSwitchTab(startItemSlot)
        end
        

    else
        if ItemOperaTool.CheckIsDIYCabinetPanel() then
            WidgetUtil.TryFocusDefaultWidgetByGroup(navGroup)
        else
                -- 根据目标NavGroup不同，分别处理
                if InventoryNavManager.CheckIsSelfBackpackNavGroup(navGroup) then
                    InventoryNavManager.FoucsBackPackAfterSwitchTab(startItemSlot)
                end
                if InventoryNavManager.CheckIsDeadBodyNavGroup(navGroup) then
                    InventoryNavManager.FoucsDeadBodyAfterSwitchTab(startItemSlot)
                    -- WidgetUtil.TryFocusDefaultWidgetByGroup(navGroup)
                end
                if InventoryNavManager.CheckIsSelfEquipmentNavGroup(navGroup) then
                    InventoryNavManager.FoucsEquipmentAfterSwitchTab(startItemSlot)
                    -- WidgetUtil.TryFocusDefaultWidgetByGroup(navGroup)
                end
                if InventoryNavManager.CheckIsDiscardNavGroup(navGroup) then
                    WidgetUtil.TryFocusDefaultWidgetByGroup(navGroup)
                end
        end
    end
end


-- 聚焦到安全箱
function InventoryNavManager.FocusToSafeBox(bNeedScroll)
    if not IsHD() then
        return
    end
    if bNeedScroll and InventoryNavManager.backpackPanel then
        InventoryNavManager.backpackPanel._wtMainScrollBox:ScrollToEnd()
    end
    if InventoryNavManager.allInvSlotView[ESlotType.SafeBox] and InventoryNavManager.backpackNavGroup  then
        InventoryNavManager.FocusWithTimer(InventoryNavManager.allInvSlotView[ESlotType.SafeBox],0.2)
    end
end
--endregion

--region 判断
function InventoryNavManager.CheckIsSelfEquipmentNavGroup(navGroup)
    if InventoryNavManager.equipNavGroup == navGroup then
        return true
    end
    return false
end

function InventoryNavManager.CheckIsSelfBackpackNavGroup(navGroup)
    if InventoryNavManager.backpackNavGroup == navGroup then
        return true
    end
    return false
end

function InventoryNavManager.CheckIsWarehouseNavGroup(navGroup)
    if InventoryNavManager.warehouseNavGroup == navGroup then
        return true
    end
    return false
end

function InventoryNavManager.CheckIsDiscardNavGroup(navGroup)
    if InventoryNavManager.discardNavGroup == navGroup then
        return true
    end
    return false
end


function InventoryNavManager.CheckIsNearbyNavGroup(navGroup)
    if InventoryNavManager.nearbyNavGroup == navGroup then
        return true
    end
    return false
end

function InventoryNavManager.CheckIsDeadBodyNavGroup(navGroup)
    if InventoryNavManager.deadBodyNavGroup == navGroup then
        return true
    end
    return false
end



function InventoryNavManager.CheckIsPlayerGroupSlotbyView(slotView)
    local InSlotGroup = nil
    if slotView.itemSlot then
        InSlotGroup = slotView.itemSlot:GetSlotGroup()
    else
        InSlotGroup = slotView._slotGroup
    end
    return InSlotGroup == ESlotGroup.Player
end

function InventoryNavManager.CheckIsDeadbodyGroupSlotbyView(slotView)
    local InSlotGroup = nil
    if slotView.itemSlot then
        InSlotGroup = slotView.itemSlot:GetSlotGroup()
    else
        InSlotGroup = slotView._slotGroup
    end
    return InSlotGroup == ESlotGroup.DeadBody
end

function InventoryNavManager.CheckIsNearbyGroupSlotbyView(slotView)
    local InSlotGroup = nil
    if slotView.itemSlot then
        InSlotGroup = slotView.itemSlot:GetSlotGroup()
    else
        InSlotGroup = slotView._slotGroup
    end
    return InSlotGroup == ESlotGroup.Nearby
end

--endregion

-- 设置Looting Pickup Type
function InventoryNavManager.SetPickUpType(pickUpType)
    InventoryNavManager.pickUpType = pickUpType
end

-- 设置Interactors
function InventoryNavManager.SetInteractors(interactors)
    InventoryNavManager.interactors = interactors
end

function InventoryNavManager.CheckIsSearchingDeadBody()
    if InventoryNavManager.deadBodyView[ESlotType.MainWeaponLeft] and InventoryNavManager.deadBodyNavGroup and InventoryNavManager.deadbodyPanel and InventoryNavManager.pickUpType == EGamePickupType.NearbyDeadBody  then
        return InventoryNavManager.deadBodyView[ESlotType.MainWeaponLeft]
    end
    return nil
end

-- 检查是否在搜索散点/容器
function InventoryNavManager.CheckIsSearchinLitter()
    return InventoryNavManager.CheckIsSearchinContainer() or InventoryNavManager.CheckIsSearchingPickup() or InventoryNavManager.CheckIsSearchingDropContainer()
end

function InventoryNavManager.CheckIsSearchinContainer()
    if InventoryNavManager.nearbyView[ESlotType.NearbyContainer] and InventoryNavManager.litterNavGroup and InventoryNavManager.litterPanel and InventoryNavManager.pickUpType == EGamePickupType.SceneBox then
        return InventoryNavManager.nearbyView[ESlotType.NearbyContainer]
    end
    return nil
end

function InventoryNavManager.CheckIsSearchingPickup()
    logerror("CheckIsSearchingPickup - she2 hotfix")
    if InventoryNavManager.nearbyView[ESlotType.NearbyPickups] and InventoryNavManager.litterNavGroup and InventoryNavManager.litterPanel and InventoryNavManager.pickUpType == EGamePickupType.NearbyPickups then
        return InventoryNavManager.nearbyView[ESlotType.NearbyPickups]
    end
    return nil
end

function InventoryNavManager.CheckIsSearchingDropContainer()
    logerror("CheckIsSearchingDropContainer - she2 hotfix")
    if InventoryNavManager.nearbyView[ESlotType.NearbyPickups] and InventoryNavManager.litterNavGroup and InventoryNavManager.litterPanel and InventoryNavManager.pickUpType == EGamePickupType.DropContainer then
        local dropContainerSlot = Server.LootingServer.dropContainerSlot
        if dropContainerSlot then
            return InventoryNavManager.nearbyView[dropContainerSlot.SlotType]
        end
    end
    return nil
end


-- 下列代码记录重构思路，暂时没调用
function InventoryNavManager.IsEnableInteractivity(item)
    if item and item.slot then
        return not (slot.SlotType == ESlotType.MeleeWeapon or slot.SlotType == ESlotType.KeyChain or slot.SlotType == ESlotType.SafeBox)
    end
end

function InventoryNavManager.RegisterSlotView(slotView)
    local InSlotGroup = nil
    local InSlotType = nil
    if slotView.itemSlot then
        InSlotGroup = slotView.itemSlot:GetSlotGroup()
        InSlotType = slotView.itemSlot.SlotType
    else
        InSlotGroup = slotView:GetSlotGroup()
        InSlotType = slotView:GetSlotType()
    end
    if InSlotGroup == nil or InSlotType == nil then
        return
    end
    if IsHD() then
        if InSlotGroup == ESlotGroup.DeadBody then
            InventoryNavManager.deadBodyView[InSlotType] = slotView
        else
            if InSlotGroup == ESlotGroup.Nearby then
                -- -- 如果是散点的背包胸挂容器，进入NearbyView，如果是散点物资，进入AllInvSlotView
                -- if SlotView._slotType ~= ESlotType.NearbyContainer and SlotView._slotType ~= ESlotType.NearbyPickups then
                --     InventoryNavManager.NearbyView[SlotView._slotType] = SlotView
                -- else
                --     InventoryNavManager.AllInvSlotView[SlotView._slotType] = seSlotViewlf
                -- end
                InventoryNavManager.nearbyView[InSlotType] = slotView
            else
                InventoryNavManager.allInvSlotView[InSlotType] = slotView
            end
        end
    end
end
    

return InventoryNavManager