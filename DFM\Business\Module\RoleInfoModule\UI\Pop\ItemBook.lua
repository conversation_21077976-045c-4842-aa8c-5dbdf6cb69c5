----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRoleInfo)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class ItemBook : LuaUIBaseView
local ItemBook = ui("ItemBook")
local CommonPopWindows = require "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindows"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local EComp = CommonWidgetConfig.EIVWarehouseTempComponent
local EIVSlotPos = CommonWidgetConfig.EIVSlotPos
local EIVItemViewMode = CommonWidgetConfig.EIVItemViewMode

local Config = Module.RoleInfo.Config
--- BEGIN MODIFICATION @ VIRTUOS
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
--- END MODIFICATION

function ItemBook:Ctor()
    self._wtRootWindow = self:Wnd("WBP_CommonPopWindowsV2", CommonPopWindows)
    self._wtTitleTxt = self:Wnd("DFRichTextBlock_61", UITextBlock)
    self._wtItemScrollBox = UIUtil.WndWaterfallScrollBox(self, "DFWaterfallScrollView_135",
        self._OnGetItemCount, self._OnProcessItemWidget)

    self._wtCommonDropDownBox = UIUtil.WndDropDownBox(self, "WBP_DFCommonDropDownBox", self.OnOptionChanged)

    self:InitData()
end

function ItemBook:InitData()
    self.propInfos = {}
    self.type = 1
    self.curPropInfos = {}

    local EWeaponItemType = ItemConfig.EWeaponItemType
    self.curWeaponItemType = EWeaponItemType.Unknown

    self.weaponItemTypes = {
        EWeaponItemType.Unknown,
        EWeaponItemType.Rifle,
        EWeaponItemType.Submachine,
        EWeaponItemType.Shotgun,
        EWeaponItemType.LightMachine,
        EWeaponItemType.PrecisionShootingRifle,
        EWeaponItemType.Sniper,
        EWeaponItemType.Pistol,
        EWeaponItemType.Universal
    }

    self.weaponTyps = Module.Gunsmith.Config.WeaponTabTxtList
end

function ItemBook:OnOpen()
    local fCallbackIns = CreateCallBack(self._OnClosePopUI, self)
    self._wtRootWindow:BindCloseCallBack(fCallbackIns)
    self._wtRootWindow:SetTitle(self.type == 1 and Config.Loc.MPStarDesc or Config.Loc.SOLRoomBook)

    UIUtil.InitDropDownBox(self._wtCommonDropDownBox, Module.Gunsmith.Config.WeaponTabTxtList, {}, self.curWeaponItemType)
end

function ItemBook:OnClose()

end

--- BEGIN MODIFICATION @ VIRTUOS
function ItemBook:OnShowBegin()
    if IsHD() then
        self:_InitGamepadInputs()
    end
end

function ItemBook:OnHideBegin()
    if IsHD() then
        self:_DisableGamepadInputs()
    end
end

--- END MODIFICATION

function ItemBook:OnInitExtraData(type, propInfos, roomData)
    self.type = type

    self:SetType(type)
    self.propInfos = propInfos
    self.roomData = roomData

    self:GetPropInfos()

    self:RefreshUI()
end

function ItemBook:GetAllCollectRoom()
    self.gridId2LevelMap = {}

    local collectionListItems = Facade.TableManager:GetTable("CollectionRoomCollection")
    for index, config in ipairs(collectionListItems) do
        local itemInfo = {}
        itemInfo.id = tonumber(config.ItemID)
        itemInfo.gridID = config.SlotID
        itemInfo.SlotType = config.SlotType
        self.gridId2LevelMap[self:GetGridKey(itemInfo.gridID, itemInfo.SlotType)] = 0
        table.insert(self.curPropInfos, itemInfo)
    end
end

function ItemBook:GetGridKey(gridId, slotType)
    return slotType .. "_" .. gridId
end

function ItemBook:GetPropInfos()
    self.curPropInfos = {}
    if self.type == 1 then
        for key, info in pairs(self.propInfos) do
            if self.curWeaponItemType == 0 or ItemHelperTool.GetSubTypeById(info.id) == self.curWeaponItemType then
                table.insert(self.curPropInfos, info)
            end
        end
    else
        self.haveGridMap = {}
        self:GetAllCollectRoom()
        local function func(cabinet)
            local gridId = 0
            if cabinet and cabinet.locked == false and cabinet.grids then
                for _, grid in pairs(cabinet.grids) do
                    if grid.level > 0 then
                        gridId = grid.grid_id
                        local key = self:GetGridKey(gridId, cabinet.type)
                        self.haveGridMap[key] = true
                        self.gridId2LevelMap[key] = grid.level
                    end
                end
            end
        end

        if self.roomData.display_cabinet then
            func(self.roomData.display_cabinet)
        end

        if self.roomData.special_cabinet then
            func(self.roomData.special_cabinet)
        end

        table.sort(self.curPropInfos, function(a, b)
            return self.haveGridMap[self:GetGridKey(a.gridID, a.SlotType)] and
                not self.haveGridMap[self:GetGridKey(b.gridID, b.SlotType)]
        end)
    end
end

function ItemBook:OnOptionChanged(type)
    self.curWeaponItemType = self.weaponItemTypes[type + 1]
    self:GetPropInfos()

    self:RefreshUI()
end

function ItemBook:RefreshUI()
    self:SetItemTypeTitle()
    self:_RefreshScrollBox()
end

function ItemBook:SetItemTypeTitle()
    if self.type == 1 then
        self._wtTitleTxt:SetText(Config.Loc.BookTitles[1])
    else
        self._wtTitleTxt:SetText(string.format(Module.RoleInfo.Config.Loc.CollectionNumFormat,
            table.getkeynum(self.haveGridMap)))
    end
end

function ItemBook:_RefreshScrollBox()
    self._wtItemScrollBox:RefreshAllItems()
end

function ItemBook:_OnGetItemCount()
    return #self.curPropInfos
end

function ItemBook:_OnProcessItemWidget(index, widget)
    local propInfo = self.curPropInfos[index]
    local itemId = propInfo.id
    local item = ItemBase:NewIns(itemId, 0)
    -- propInfo.weapon.pendant_id = propInfo.id
    -- propInfo.weapon.pendant_gid = propInfo.gid
    -- local modularDesc = WeaponAssemblyTool.PropInfo_To_Desc(propInfo)
    -- item:SetRawDescObj(modularDesc) -- 不知道干嘛的
    widget:EnableComponent(EComp.ItemQuality, false)
    widget:ShowItemNameComp(false)
    if self.type == 1 then -- 武器
        item:SetRawPropInfo(propInfo)
        widget:InitItem(item)
        widget:ShowGunStarComp(true)
        widget:ShowStrByPos(false)
    else -- 收藏室
        widget:InitItem(item)

        local key = self:GetGridKey(propInfo.gridID, propInfo.SlotType)

        widget:ShowStrByPos(true,
            string.format(Module.CollectionRoom.Config.Loc.DisplayGridLevel, self.gridId2LevelMap[key]))
        widget:ShowLockComp(not self.haveGridMap[key])
    end
    widget:ChangeDefaultMode(EIVItemViewMode.ShopItemView)
    local iconTextNameComp = widget:FindOrAdd(EComp.TopLeftIconText, UIName2ID.IVTextQualityComponent, EIVSlotPos.TopLeft)
    iconTextNameComp:RefreshComponent()
    iconTextNameComp:SetVisibility(ESlateVisibility.HitTestSelfOnly)

    widget:BindCustomOnClicked(function() end, self)
    local bgComponent = widget:GetComponent(EComp.ItemBg)
    if bgComponent then
        bgComponent:Settype(0)
    end
    widget:SetAnchorIfOverFlow()
end

function ItemBook:_OnClosePopUI()
    Facade.UIManager:CloseUI(self)
end

function ItemBook:OnNavBack()
    return false
end

--- BEGIN MODIFICATION @ VIRTUOS
function ItemBook:_InitGamepadInputs()
    if not self:IsVisible() then
        return
    end

    if not self._navGroup then
        -- Replace common pop window nav group
        self._wtRootWindow:RemoveNavGroup()

        self._navGroup = WidgetUtil.RegisterNavigationGroup(self, self, "Hittest")
        if self._navGroup then
            self._navGroup:AddNavWidgetToArray(self._wtItemScrollBox)
            self._navGroup:SetScrollRecipient(self._wtItemScrollBox)
            self._navGroup:MarkIsStackControlGroup()
        end

        WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroup)
    end
end

function ItemBook:_DisableGamepadInputs()
    if self._navGroup then
        WidgetUtil.RemoveNavigationGroup(self)
        self._navGroup = nil
    end
end

--- END MODIFICATION

return ItemBook
