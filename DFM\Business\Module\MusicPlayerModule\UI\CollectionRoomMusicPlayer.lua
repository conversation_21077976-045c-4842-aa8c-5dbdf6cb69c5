----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LogMusicPlayer)
----- LOG FUNCTION AUTO GENERATE END -----------

local MusicPlayerWidgetLogic = require "DFM.Business.Module.MusicPlayerModule.Logic.MusicPlayerWidgetLogic"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"

---@class CollectionRoomMusicPlayer : LuaUIBaseView
local CollectionRoomMusicPlayer = ui("CollectionRoomMusicPlayer")

function CollectionRoomMusicPlayer:Ctor()
    self._wtPreviewBtn = self:Wnd("DFButton_Previous", UIButton)
    self._wtPreviewBtn:Event("OnClicked", self.OnClickedPreviewBtn, self)
    self._wtPauseOrPlayBtn = self:Wnd("DFButton_PauseOrPlay", UIButton)
    self._wtPauseOrPlayBtn:Event("OnClicked", self.OnClickedPauseOrPlayBtn, self)
    self._wtPauseOrPlayIcon = self:Wnd("DFImage_Icon_01", UIImage)
    self._wtNextBtn = self:Wnd("DFButton_Next", UIButton)
    self._wtNextBtn:Event("OnClicked", self.OnClickedNextBtn, self)
    self._wtListBtn = self:Wnd("DFButton_List", UIButton)
    self._wtListBtn:Event("OnClicked", self.OnClickedListBtn, self)
    self._wtMainIcon = self:Wnd("Image_Icon", UIImage)
    self._wtCollapsedState = self:Wnd("DFCanvasPos_Collapsed", UIWidgetBase)
    self._wtExpandState = self:Wnd("DFCanvasPos_Expand", UIWidgetBase)
    self._wtCollapsedStateIcon = self:Wnd("DFImage_Icon", UIImage)
    self._wtHoverBtn = self:Wnd("DFButton_Hover", UIButton)
    if IsHD() then
        self._wtHoverBtn:Event("OnHovered", self._OnHovered, self)
        self._wtHoverBtn:Event("OnUnHovered", self._OnUnHovered, self)
    end
    self._wtCoverImage = self:Wnd("MI_UI_CollectionHome_MusicPlayer_001", UIImage)
    self._parentWidget = nil
    self._handle = nil
    self._bInCollapsedState = true
    self._bVisible = false
end

function CollectionRoomMusicPlayer:OnShowBegin()
    self._bVisible = true
    if not IsHD() then
        UDFMGameHudDelegates.Get(GetGameInstance()).OnHandleMouseButtonDownEvent:Add(self._OnHandleMouseButtonDownEvent, self)
    end
    self:PlayWidgetAnim(self.WBP_CollectionHome_MusicPlayer_closed)
    self:RegisterEvents()
    self:SetToCollapsedState()
    self:RefreshView()
end

function CollectionRoomMusicPlayer:OnHide()
    self:AddOrRemoveMouseButtonMoveEvent(false)
    if not IsHD() then
        UDFMGameHudDelegates.Get(GetGameInstance()).OnHandleMouseButtonDownEvent:Remove(self._OnHandleMouseButtonDownEvent, self)
    end
    self:UnregisterEvents()
    self._parentWidget = nil
    self._bVisible = false
end

function CollectionRoomMusicPlayer:AddOrRemoveMouseButtonMoveEvent(bAdd)
    if IsHD() then
        if bAdd and not self._handle and self._bVisible then
            self._handle = UDFMGameHudDelegates.Get(GetGameInstance()).OnHandleMouseMoveEvent:Add(self._OnHandleMouseButtonMoveEvent, self)
        elseif not bAdd and self._handle then
            UDFMGameHudDelegates.Get(GetGameInstance()).OnHandleMouseMoveEvent:Remove(self._handle)
            self._handle = nil
        end
    end
end

function CollectionRoomMusicPlayer:RegisterEvents()
    self:AddLuaEvent(Module.MusicPlayer.Config.Events.evtOnMusicPlayerPlayStateChange, self.OnMusicPlayStateChanged, self)
    self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadProgress,
            self.OnDownloadedChanged, self)
    self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult, self.OnDownloadedChanged, self)
end

function CollectionRoomMusicPlayer:UnregisterEvents()
    self:RemoveLuaEvent(Module.MusicPlayer.Config.Events.evtOnMusicPlayerPlayStateChange)
    self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadProgress)
    self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult)
end

function CollectionRoomMusicPlayer:OnDownloadedChanged(packageName)
    local musicModuleName = LiteDownloadManager:GetSystemDownloadModuleName(EDownloadSystem.MusicPlayer)
    if packageName == musicModuleName then
        self:_RefreshIcon()
    end
end

function CollectionRoomMusicPlayer:OnMusicPlayStateChanged(evtName,evtState)
    self:RefreshView()
end

function CollectionRoomMusicPlayer:RefreshView()
    local curMusicData = MusicPlayerWidgetLogic.GetCurMusicData()

    if not curMusicData then
        logerror("[CollectionRoomMusicPlayer:OnShowBegin] curMusicData is nil")
        return
    end
    self._wtMainIcon:AsyncSetImagePath(curMusicData.Icon)
    local imageAssetCache = Facade.UIManager.stubUIImageRes:TryGetResByPath(curMusicData.Icon)
    if imageAssetCache == nil or not isvalid(imageAssetCache) then
        local fOnImageLoadFinished = function(mapPath2ResIns)
            local _,asset=next(mapPath2ResIns)
            if asset and isvalid(asset) then
                local MatIns = self._wtCoverImage:GetDynamicMaterial()
                if MatIns then
                    MatIns:SetTextureParameterValue("Texture_Main", asset)
                end
            end
        end
        Facade.ResourceManager:AsyncLoadResource(Facade.UIManager.stubUIImageRes, curMusicData.Icon, fOnImageLoadFinished)
    else
        local MatIns = self._wtCoverImage:GetDynamicMaterial()
        if MatIns then
            MatIns:SetTextureParameterValue("Texture_Main",imageAssetCache)
        end
    end
    self:_RefreshIcon()
    if Module.MusicPlayer:IsPlayingMusic() then
        self:PlayWidgetAnim(self.WBP_CollectionHome_MusicPlayer_loop, 0)
    else
        self:StopWidgetAnim(self.WBP_CollectionHome_MusicPlayer_loop)
    end
end

function CollectionRoomMusicPlayer:SetParentWidget(inParentWidget)
    self._parentWidget = inParentWidget
end

function CollectionRoomMusicPlayer:OnClickedPreviewBtn()
    MusicPlayerWidgetLogic.PreMusic()
    self:_RefreshIcon()
end

function CollectionRoomMusicPlayer:OnClickedPauseOrPlayBtn()
    local curEvtName = MusicPlayerWidgetLogic.GetCurMusicEvtName()
    MusicPlayerWidgetLogic.ClickedControlBtn_Implement(curEvtName)
    self:_RefreshIcon()
end

function CollectionRoomMusicPlayer:OnClickedNextBtn()
    MusicPlayerWidgetLogic.NextMusic()
    self:_RefreshIcon()
end

function CollectionRoomMusicPlayer:OnClickedListBtn()
    local fOnCreatedMusicPlayerPop = CreateCallBack(function(self)
        if self and self._parentWidget then
            local isHd = IsHD()
            if isHd then
                Facade.UIManager:CloseUI(self._parentWidget)
            end
        end
    end, self)
    MusicPlayerWidgetLogic.ShowMusicDetailPanel(fOnCreatedMusicPlayerPop)
end

function CollectionRoomMusicPlayer:_RefreshIcon()
    local curMusicEvtName = MusicPlayerWidgetLogic.GetCurMusicEvtName()
    if not curMusicEvtName then
        logerror("[CollectionRoomMusicPlayer:OnShowBegin] curMusicEvtName is nil")
        return
    end
    MusicPlayerWidgetLogic.SetPlayAndPauseIcon(self._wtPauseOrPlayIcon,curMusicEvtName)
end

function CollectionRoomMusicPlayer:SetToCollapsedState()
    if self:IsAnimationPlayingExactly(self.WBP_CollectionHome_MusicPlayer_close) then
        self:StopWidgetAnim(self.WBP_CollectionHome_MusicPlayer_close)
    end
    if self:IsAnimationPlayingExactly(self.WBP_CollectionHome_MusicPlayer_open) then
        self:StopWidgetAnim(self.WBP_CollectionHome_MusicPlayer_open)
    end
    self:SkipAnimation(self.WBP_CollectionHome_MusicPlayer_close)
    self._bInCollapsedState = true
end

function CollectionRoomMusicPlayer:SwitchState(bCollapsed)
    if bCollapsed and not self._bInCollapsedState then
        --self._wtCollapsedState:SelfHitTestInvisible()
        --self._wtExpandState:Collapsed()
        self:AddOrRemoveMouseButtonMoveEvent(false)
        if self:IsAnimationPlayingExactly(self.WBP_CollectionHome_MusicPlayer_open) then
            self:StopWidgetAnim(self.WBP_CollectionHome_MusicPlayer_open)
        end
        if not self:IsAnimationPlayingExactly(self.WBP_CollectionHome_MusicPlayer_close) then
            self:PlayWidgetAnim(self.WBP_CollectionHome_MusicPlayer_close)
        end
        self._bInCollapsedState = true
    elseif not bCollapsed and self._bInCollapsedState then
        --self._wtExpandState:SelfHitTestInvisible()
        --self._wtCollapsedState:Collapsed()
        self:AddOrRemoveMouseButtonMoveEvent(true)
        if self:IsAnimationPlayingExactly(self.WBP_CollectionHome_MusicPlayer_close) then
            self:StopWidgetAnim(self.WBP_CollectionHome_MusicPlayer_close)
        end
        if not self:IsAnimationPlayingExactly(self.WBP_CollectionHome_MusicPlayer_open) then
            self:PlayWidgetAnim(self.WBP_CollectionHome_MusicPlayer_open)
        end
        self._bInCollapsedState = false
    end
end

function CollectionRoomMusicPlayer:OnAnimStarted(animation)
    if animation == self.WBP_CollectionHome_MusicPlayer_close then
        self._wtCollapsedStateIcon:SelfHitTestInvisible()
        self._wtCoverImage:SelfHitTestInvisible()
        Module.CollectionRoom.Config.Events.evtOnMusicPlayerStartClosing:Invoke()
    end
    if animation == self.WBP_CollectionHome_MusicPlayer_open then
        self._wtExpandState:SelfHitTestInvisible()
        self:SetHoverBtn(false)
    end
end

function CollectionRoomMusicPlayer:OnAnimFinished(animation)
    if animation == self.WBP_CollectionHome_MusicPlayer_open then
        self._wtCollapsedStateIcon:Collapsed()
        self._wtCoverImage:Collapsed()
        Module.CollectionRoom.Config.Events.evtOnMusicPlayerEndOpening:Invoke()
    end
    if animation == self.WBP_CollectionHome_MusicPlayer_close then
        self._wtExpandState:Collapsed()
        self:SetHoverBtn(true)
    end
end

function CollectionRoomMusicPlayer:_CheckIfSwitchState(mousePos)
    if not self._bVisible then
        return
    end
    mousePos = mousePos or UWidgetLayoutLibrary.GetMousePositionOnPlatform()
    local hoverBtnGeometry = self._wtHoverBtn:GetCachedGeometry()
    local isUnderHoverBtn = USlateBlueprintLibrary.IsUnderLocation(hoverBtnGeometry, mousePos)
    self:SwitchState(not isUnderHoverBtn)
end

function CollectionRoomMusicPlayer:GetDefaultFocusWidget()
    if IsHD() then
        return self._wtPauseOrPlayBtn  
    end
end

if IsHD() then
    function CollectionRoomMusicPlayer:_OnHovered()
        if WidgetUtil.IsGamepad() and not WidgetUtil.IsUsingFreeAnalogCursor() then
            return
        end
        self:SwitchState(false)
    end

    function CollectionRoomMusicPlayer:_OnUnHovered()
        self:_CheckIfSwitchState()
    end

    function CollectionRoomMusicPlayer:_OnHandleMouseButtonMoveEvent(mouseEvent)
        self:_CheckIfSwitchState(mouseEvent:GetScreenSpacePosition())
    end
else
    function CollectionRoomMusicPlayer:_OnHandleMouseButtonDownEvent(mouseEvent)
        self:_CheckIfSwitchState(mouseEvent:GetScreenSpacePosition())
    end
end

return CollectionRoomMusicPlayer