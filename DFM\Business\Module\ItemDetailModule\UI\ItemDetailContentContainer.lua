----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMItemDetail)
----- LOG FUNCTION AUTO GENERATE END -----------



local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local ItemDetailConfig = require("DFM.Business.Module.ItemDetailModule.ItemDetailConfig")
local JumpConfig = require "DFM.Business.Module.JumpModule.JumpConfig"
-- local SlotConfig = require "DFM.Business.DataStruct.InventoryStruct.SlotConfig"

local ItemDetailContentBase = require("DFM.Business.Module.ItemDetailModule.UI.ItemDetailContentBase")

---@class ItemDetailContentContainer : ItemDetailContentBase
local ItemDetailContentContainer = ui("ItemDetailContentContainer", ItemDetailContentBase)

function ItemDetailContentContainer:Ctor()
    self._itemInfo = nil

	self._wtUpgratePanel = self:Wnd("wUpgratePanel", UIWidgetBase)
	if self._wtUpgratePanel then
		self._wtUpgrateBtn = self._wtUpgratePanel:Wnd("wGiftRandomDetailBtn", DFCommonCheckButtonOnly)
		self._wtUpgrateBtn:Event("OnCheckedBoxStateChangedNative", self._CheckedBoxStateChanged, self)
	end

	-- self._wtStoreItemsBox = self:Wnd("wtStoreItemsBox", UIScrollBox)

	self._bigTipsAlignWidget = nil
end

-- 设置按钮所需要的信息
function ItemDetailContentContainer:_SetDescriptionWindowInfo(dataList, name, subTitle, alignWidget, openCallback)
	self._dataList = dataList
    self._alignWidget = alignWidget
    self._name = name
    self._subTitle = subTitle
	self._openCallback = openCallback
end

-- 按钮点击事件
function ItemDetailContentContainer:_CheckedBoxStateChanged()
	-- Module.CommonTips:ShowDescriptionWindow(self._wtExchangeHigher, self._name, nil, self._dataList, self._alignWidget, self._subTitle)
	local handle = Module.CommonTips:ShowDescriptionWindow(self._wtUpgrateBtn, self._name, nil, self._dataList, self._alignWidget, self._subTitle)
	if handle == nil then
        return
    end
	if self._openCallback then
        safecall(self._openCallback)
    end
end

---@param item ItemBase
function ItemDetailContentContainer:SetItem(item, exArgs)
	self._itemInfo = item
	-- local slotConfig = SlotConfig:New(item.id, item.DisplayName)
	local itemFeature = self._itemInfo:GetFeature()
	local isContainer = not itemFeature:IsExtendItem()
	if isContainer then
		local equipmentFeature = self._itemInfo:GetFeature(EFeatureType.Equipment)
		if equipmentFeature:IsBag() or equipmentFeature:IsChestHanging() then
			self:_SetBagAndChestDetail()
		else
    		self:_SetContainerDetail()
		end
	else
		-- 钥匙包不会绑这个脚本
		self:_SetDepotDetailNew()
	end
end

-- 安全箱
function ItemDetailContentContainer:_SetContainerDetail()
	-- 可放入
	self._wtStoreTypePanel = self:Wnd("wStoreTypePanel", UIWidgetBase)
	self._wtStoreTypePanel:Collapsed()
	-- 无法放入
	self._wtForbidTypePanel = self:Wnd("wForbidTypePanel", UIWidgetBase)
	self._wtForbidTypeText = self:Wnd("wForbidTypeText", UITextBlock)
	-- 容量
	self._wtContainerSizeText = self:Wnd("wDesc", UITextBlock)
	-- 特性
	self._wtContainerFeature = self:Wnd("wContainerFeature", UIWidgetBase)


	---@type EquipmentFeature
	local equipmentFeature = self._itemInfo:GetFeature(EFeatureType.Equipment)
	local propSlotConfig = Facade.TableManager:GetTable("PropSlotConfig")
	local length, height = equipmentFeature:GetRealLengthAndHeight()
	self._wtContainerSizeText:SetText(string.format("%d<customstyle color=\"C002\">(%dx%d)</>",equipmentFeature:GetContainerSize(), length, height))

	local slotTbl = {}
	if equipmentFeature and equipmentFeature:IsSafeBox() then
		-- TODO 判断是否满级安全箱
		-- self._wtContainerFeature:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		-- if self._wtUpgratePanel then
		-- 	self._wtUpgratePanel:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		-- end
		-- local limitSlotStr = propSlotConfig[109001].IgnorePropTypes
		-- slotTbl = string.split(limitSlotStr, ",")
		-- 安全箱只显示容量属性
		self._wtContainerFeature:SetVisibility(ESlateVisibility.Collapsed)
		if self._wtUpgratePanel then
			self._wtUpgratePanel:SetVisibility(ESlateVisibility.Collapsed)
		end
	else
		self._wtContainerFeature:SetVisibility(ESlateVisibility.Collapsed)
		if self._wtUpgratePanel then
			self._wtUpgratePanel:SetVisibility(ESlateVisibility.Collapsed)
		end
	end
	if #slotTbl > 0 then
		if self._wtForbidTypePanel then
			self._wtForbidTypePanel:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		end
		local iconTable = {}
		for _, id in ipairs(slotTbl) do
			local depositSortClassTable = Facade.TableManager:GetTable("DepositSortClass")
			for _, row in pairs(depositSortClassTable) do
				local isFind = false
				for _, value in ipairs(row.ItemTypeID) do
					if value == tonumber(id) then
						iconTable[row.ClassName] = row.ClassIconPath
						isFind = true
						break
					end
					if isFind then
						break
					end
				end
			end
		end
		local str = ""
		local isOne = true
		for name, _ in pairs(iconTable) do
			if isOne then
				str = name
				isOne = false
			else
				str = StringUtil.Key2StrFormat(ItemDetailConfig.Loc.forbidTypeText, {["itemcategory_1"] = str, ["itemcategory_2"] = name})
			end
		end
		self._wtForbidTypeText:SetText(str)
	else
		if self._wtForbidTypePanel then
			self._wtForbidTypePanel:SetVisibility(ESlateVisibility.Collapsed)
		end
	end

	self:_SetExchangeBtn()
end

-- 背包胸挂
function ItemDetailContentContainer:_SetBagAndChestDetail()
	-- 容量
	self._wtBagCapacityPanel = self:Wnd("WBP_ItenDetail_BagCapacity", UIWidgetBase)
	self._wtBagCapacity = self._wtBagCapacityPanel:Wnd("wDesc", UITextBlock)

	-- buf效果
	self._wtBuffWrapPanel = self:Wnd("wBackpack", UIWidgetBase)
	self._wtBuffWrapPanel:Collapsed()
	self._wtwBuffWrap = self:Wnd("wBuffWrap", UIWidgetBase)

	local funcCfg
	local equipmentFeature = self._itemInfo:GetFeature(EFeatureType.Equipment)
	if equipmentFeature:IsBag() then
		self._wtBagCapacityPanel:BP_SetType(0)
		funcCfg = ItemConfigTool.GetBagFuncTable(self._itemInfo.id)
	else
		self._wtBagCapacityPanel:BP_SetType(1)
		funcCfg = ItemConfigTool.GetChestHangingFuncTable(self._itemInfo.id)
	end

	self._wtBagCapacity:SetText(string.format("%d", equipmentFeature:GetContainerSize()))
	self:_ComparetoEquipped(equipmentFeature)

	if funcCfg and Server.ArmedForceServer:GetCurArmedForceMode() ~= EArmedForceMode.MP then
		-- 获取buffInfos
		local buffInfos = {}
		if funcCfg.AddBuffs then
			for insertIDX, value in pairs(funcCfg.AddBuffs) do
				local buffCfg = ItemConfigTool.GetBuffById(value.BuffId)
				if buffCfg then
					table.insert(buffInfos, {buffId = value.BuffId, buffDebuffType = buffCfg.BuffDebuffType, insertIDX = insertIDX})
				end
			end
		end
		if buffInfos and #buffInfos > 0 then
			self._wtBuffWrapPanel:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
			Facade.UIManager:RemoveSubUIByParent(self, self._wtwBuffWrap)

			table.sort(buffInfos, ItemConfigTool.SortByBuffDebuffType)
			for _, buffInfo in ipairs(buffInfos) do
				local buffCfg = ItemConfigTool.GetBuffById(buffInfo.buffId)
				local extraDesc = nil
				if buffCfg then
					if buffCfg.Duration > 0 then
						extraDesc = buffCfg.Duration.."s"
					end
					Facade.UIManager:AddSubUI(self, UIName2ID.ItemDetailBufferItem, self._wtwBuffWrap, nil, buffCfg, extraDesc)
				end
			end
		end
	end
end

function ItemDetailContentContainer:_ComparetoEquipped(equipmentFeature)
	-- 获取已装备的背包胸挂信息
	local equippedment
	local equipSlot
	-- 判断当前的道具是背包还是胸挂
	if equipmentFeature:IsBag() then
		equipSlot = Server.InventoryServer:GetSlot(ESlotType.Bag, ESlotGroup.Player)
		equippedment = equipSlot:GetEquipItem()
	elseif equipmentFeature:IsChestHanging() then
		equipSlot = Server.InventoryServer:GetSlot(ESlotType.ChestHanging, ESlotGroup.Player)
		equippedment = equipSlot:GetEquipItem()
	end

	if equippedment then
		local equippedmentFeature = equippedment:GetFeature(EFeatureType.Equipment)
		if equipmentFeature:GetContainerSize() > equippedmentFeature:GetContainerSize() then
			-- self._wtContainerLevelIcon:SetVisibility(ESlateVisibility.Visible)
			-- self._wtContainerLevelIcon:AsyncSetImagePath(ItemDetailConfig.PromoteIcon, false)
			Module.Inventory.Config.Events.evtShowEquipCompareIcon:Invoke(equipSlot.SlotType, false)
		elseif equipmentFeature:GetContainerSize() < equippedmentFeature:GetContainerSize() then
			-- self._wtContainerLevelIcon:SetVisibility(ESlateVisibility.Visible)
			-- self._wtContainerLevelIcon:AsyncSetImagePath(ItemDetailConfig.DeclineIcon, false)
			Module.Inventory.Config.Events.evtShowEquipCompareIcon:Invoke(equipSlot.SlotType, true)
		else
			-- self._wtContainerLevelIcon:SetVisibility(ESlateVisibility.Collapsed)
		end
	else
		-- self._wtContainerLevelIcon:SetVisibility(ESlateVisibility.Collapsed)
	end
end

-- 扩容道具
function ItemDetailContentContainer:_SetDepotDetailNew()
	-- 容量
	self._wtBagCapacityPanel = self:Wnd("Capacity", UIWidgetBase)
	self._wtBagCapacity = self._wtBagCapacityPanel:Wnd("wDesc", UITextBlock)
	-- 可放入
	self._wtStoreTypePanel = self:Wnd("wStoreTypePanel", UIWidgetBase)
	self._wtStoreTypeText = self:Wnd("wStoreTypeText", UITextBlock)
	-- 特性
	self._wtBuffWrapPanel = self:Wnd("wContainerFeature", UIWidgetBase)
	self._wtBuffWrapPanel:Collapsed()

	-- ---@type EquipmentFeature
	-- local equipmentFeature = self._itemInfo:GetFeature(EFeatureType.Equipment)
	-- local length, height = equipmentFeature:GetRealLengthAndHeight()
	-- self._wtBagCapacity:SetText(string.format("%d<customstyle color=\"C002\">(%dx%d)</>",equipmentFeature:GetContainerSize(), length, height))

	local extDesRow = ItemConfigTool.GetExtentionBoxDescRowById(self._itemInfo.id)
	if extDesRow then
		self._wtBagCapacity:SetText(string.format("%d<customstyle color=\"C002\"> (%dx%d)</>",extDesRow.GainPosLength * extDesRow.GainPosWidth, extDesRow.GainPosLength, extDesRow.GainPosWidth))
		-- TODO 补上【全部类型】
		local typeIconPaths, typeTipsTexts = ItemHelperTool.GetTypeIconAndTipsByExtItemId(self._itemInfo.id)
		if #typeIconPaths == 0 then
			-- self._wtStoreItemsBox:Collapsed()
		else
			-- self._wtStoreItemsBox:SelfHitTestInvisible()
			local str = ""
			local isOne = true
			for _, name in ipairs(typeTipsTexts) do
				if isOne then
					str = StringUtil.SequentialFormat("{0}", name)
					isOne = false
				else
					str = StringUtil.SequentialFormat("{0}/{1}", str, name)
				end
			end
			self._wtStoreTypeText:SetText(str)
			self._wtStoreTypePanel:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		end
	else
		self._wtStoreTypePanel:SetVisibility(ESlateVisibility.Collapsed)
	end

	self:_SetExchangeBtn()

	-- 局内不显示兑换高级
	if not Facade.GameFlowManager:CheckIsInFrontEnd() then
		--self._wtUpgrateCanvasPanel:SetVisibility(ESlateVisibility.Collapsed)
		return
	end

	-- -- 暂时不显示兑换高级
	-- local nextLvlItemId = ItemConfigTool.GetExtItemLevelUpItemId(self._itemInfo.id)
	-- if nextLvlItemId > 0 then
	-- 	if self._wtUpgratePanel then
	-- 		self._wtUpgratePanel:SelfHitTestInvisible()
	-- 	end
	-- else
	-- 	if self._wtUpgratePanel then
	-- 		self._wtUpgratePanel:Collapsed()
	-- 	end
	-- end
end

function ItemDetailContentContainer:_SetExchangeBtn()
	-- 局内不显示兑换高级
	if not Facade.GameFlowManager:CheckIsInFrontEnd() then
		if self._wtUpgratePanel then
			self._wtUpgratePanel:SetVisibility(ESlateVisibility.Collapsed)
		end
		return
	end

	-- 跳转商人兑换
	local gotoShopExchangeCallback = SafeCallBack(function (caller, data)
		local jumpCallback = function(exchangeId)
			local ret = Module.Shop:JumpToShopItemViewByExchangeId(exchangeId, self._itemInfo.gid)
			LogAnalysisTool.DoSendJumpItemUsePlaceUILog(ItemDetailConfig.EItemUsePlace.Exchange)
			return ret
		end
		Module.ItemDetail:JumpTo(JumpConfig.EJumpToModule.Shop, data.jumpArgs, jumpCallback)
	end)

	local usePlaceInfo = {}
	local bHaveUsePlace = false
	usePlaceInfo.sourceType = ItemDetailConfig.EItemUsePlace.Exchange
	usePlaceInfo.dataList = {}
	local jumpToExchangeData = Module.Shop:GetCanExchangeIdListById(self._itemInfo.id)
	if jumpToExchangeData then
		for _, exchangeId in ipairs(jumpToExchangeData) do
			local data = {}
			local shopItem = Server.ShopServer:GetSItemDetailStructByExchangeId(exchangeId)
			data.title = shopItem:GetItemName()
			local bUnlock = shopItem:GetIsUnlock()
			if bUnlock then
				local bEnough = shopItem:GetIsCostEnough(1)
				if bEnough then
					data.descContent = ItemDetailConfig.Loc.itemEnough
					data.btnText = ItemDetailConfig.Loc.gotoExchange
				else
					data.descContent = ItemDetailConfig.Loc.itemNotEnough
					data.btnText = ItemDetailConfig.Loc.gotoLook
				end
				data.showBtn = true
				data.btnCallback = gotoShopExchangeCallback
				data.jumpArgs = exchangeId
			else
				data.descContent = ItemDetailConfig.Loc.curLock
				data.showBtn = false
			end
			bHaveUsePlace = true
			table.insert(usePlaceInfo.dataList, data)
		end
	end
	local clickBtnCallback = function()
		LogAnalysisTool.SetCurItemDetailItemId(self._itemInfo.id)
		LogAnalysisTool.DoSendOpenItemSourceUILog(usePlaceInfo.sourceType)
	end

	local upgradeWayText = {}
	table.insert(upgradeWayText, Module.ItemDetail.Config.Loc.exchangeHigher)
	if self._wtUpgratePanel then
		self._wtUpgratePanel:SetItemText(upgradeWayText)
	end

	self:_SetDescriptionWindowInfo(usePlaceInfo.dataList, Module.ItemDetail.Config.Loc.exchangeHigher, Module.ItemDetail.Config.Loc.titleUsePlace, self._bigTipsAlignWidget, clickBtnCallback)
	
	if self._wtUpgratePanel then
		if bHaveUsePlace then
			self._wtUpgratePanel:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		else
			self._wtUpgratePanel:SetVisibility(ESlateVisibility.Collapsed)
		end
	end
end

function ItemDetailContentContainer:SetBigTipsAlignWidget(widget)
	self._bigTipsAlignWidget = widget
	if self._wtExchangeHigher then
		-- self._wtExchangeHigher:SetAlignWidget(widget)
		self._alignWidget = widget
	end
end

function ItemDetailContentContainer:PlayInAnim()
	self:PlayWidgetAnim(self.WBP_ContentContainer_in)
end

function ItemDetailContentContainer:OnClose()
	Facade.UIManager:ClearSubUIByParent(self,self._wtwBuffWrap)
end

return ItemDetailContentContainer