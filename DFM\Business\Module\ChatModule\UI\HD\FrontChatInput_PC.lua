----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMChat)
----- LOG FUNCTION AUTO GENERATE END -----------

--BEGIN MODIFICATION @ VIRTUOS :
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
--- END MODIFICATION

local FrontChatInputBase = require "DFM.Business.Module.ChatModule.UI.Base.FrontChatInputBase"

---PC端局外输入框相关逻辑
---@class FrontChatInput_PC : FrontChatInputBase
local FrontChatInput_PC = class("FrontChatInput_PC", FrontChatInputBase)

function FrontChatInput_PC:Ctor()
    self._maxChatLen = Module.Chat.Config.MAXCHATLEN_HD
end

function FrontChatInput_PC:ActivateState(UIInst)
    FrontChatInputBase.ActivateState(self, UIInst)
    self:OnInit()
end

function FrontChatInput_PC:UnActivateState()
    FrontChatInputBase.UnActivateState(self)
    self:OnReset()
end

--------------------------------------------------------------------------
--- 界面状态初始化、重置、销毁相关
--------------------------------------------------------------------------

function FrontChatInput_PC:OnInit()
    FrontChatInputBase.OnInit(self)
    local UIInst = self._UIInst
    -- 表情按钮
    self._wtCheckBoxEmoji = UIInst:Wnd("DFCheckBox_1", UICheckBox)
    self._wtCheckBoxEmoji:SetCallback(self._EmojiCheckStateChanged,self)
    -- 输入频道
    self._wtChatChannel = UIInst:Wnd("DFRichTextBlock_124", UITextBlock)
    -- 阻挡输入控制
    self._wtBlockInputControl = UIInst:Wnd("BlockInputControl", UIWidgetBase)

    self._wtDFCanvasPanel_1 = UIInst:Wnd("DFCanvasPanel_1", UIWidgetBase)

    self:AddLuaEvent(Module.Chat.Config.evtInputEmoji, self.OnInputEmoji, self)
    -- BEGIN MODIFICATION @ VIRTUOS : Focus to input panel when click translate button
    if IsHD() then
        self:AddLuaEvent(Module.Chat.Config.evtChatTranslateText, self.FocusKeyboard, self)
    end
    -- END MODIFICATION
    -- -- 切换到激活态时聚焦到输入框
    -- self:FocusKeyboard()
end

function FrontChatInput_PC:OnReset()
    FrontChatInputBase.OnReset(self)
    self._wtCheckBoxEmoji:SetCallback(self.EmptyEmojiCheckCallback,self)
    -- BEGIN MODIFICATION @ VIRTUOS : Focus to input panel when click translate button
    if IsHD() then
        self:RemoveLuaEvent(Module.Chat.Config.evtChatTranslateText)
    end
    -- END MODIFICATION
end

function FrontChatInput_PC:_CloseEmojiPanel()
    --BEGIN MODIFICATION @ VIRTUOS :
    if WidgetUtil.IsGamepad() == true then
        self:FocusKeyboard()
    end
    --END MODIFICATION
    self:ShouldClear()
end

function FrontChatInput_PC:ShouldClear()
    self._wtCheckBoxEmoji:SetIsChecked(false)
    if self._wtDFCanvasPanel_1 then
        -- self._wtDFCanvasPanel_1:SetVisibility(ESlateVisibility.Collapsed)
        self._wtDFCanvasPanel_1:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    end
    if self._EmojiPanel then
        if self._EmojiPanel.Collapsed then
            self._EmojiPanel:Collapsed()
        end

        if self._EmojiPanel.OnEventCallRemove then
            self._EmojiPanel:OnEventCallRemove()
        end
    end
end

--------------------------------------------------------------------------
--- 事件监听、控件事件相关
--------------------------------------------------------------------------

function FrontChatInput_PC:_EmojiCheckStateChanged(bIsChecked)
    if not bIsChecked then
        if self._EmojiPanel then
            self._EmojiPanel:Collapsed()
            self._EmojiPanel:OnEventCallRemove()
        end

        if self._wtDFCanvasPanel_1 then
            self._wtDFCanvasPanel_1:SetVisibility(ESlateVisibility.Collapsed)
        end
    else
        if self._EmojiPanel then
            self._EmojiPanel:Visible()
            self._EmojiPanel:OnEventCallAdd()
            -- 动画会改这个控件透明度
            if self._UIInst and self._UIInst._wtDFCanvasPanel_1 then
                self._UIInst._wtDFCanvasPanel_1:SetRenderOpacity(1.0)
            end
        else
            Facade.UIManager:RemoveSubUIByParent(self._UIInst, self._UIInst._wtNamedSlot)
            local uiIns = Facade.UIManager:AddSubUI(
                self._UIInst, UIName2ID.ChatEmojiPanel_PC, self._UIInst._wtNamedSlot,
                nil, self, self._wtCheckBoxEmoji)
            self._EmojiPanel = getfromweak(uiIns)
            self._EmojiPanel:Visible()
        end

        if self._wtDFCanvasPanel_1 then
            self._wtDFCanvasPanel_1:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        end
    end
end

function FrontChatInput_PC:EmptyEmojiCheckCallback(bIsChecked)
end

--添加表情
function FrontChatInput_PC:OnInputEmoji(emojiId)
    local msg = self._wtEditTxtInput:GetText()
    local newMsg = msg .. emojiId
    local _,fixMsg = StringUtil.GetRealWidth(newMsg,self._maxChatLen)
    if newMsg ~= fixMsg then
        Module.CommonTips:ShowSimpleTip(Module.Chat.Config.Loc.ExceedChatMaxLen)
    else
        self._wtEditTxtInput:SetText(newMsg)
        Module.Chat.Field:SetChatInput(newMsg)
        self:FocusKeyboard()
    end
end

--------------------------------------------------------------------------
--- 界面数据、具体的业务逻辑相关
--------------------------------------------------------------------------

-- 私聊当前发言是否可用
function FrontChatInput_PC:PrivateSpeakValid()
    if #Module.Chat.Field.PrivateMsgViewModel:GetPrivateInfoCache_PC() <= 0 then
        return false, nil
    end

    local nameTxt = "null"
    local lastChatplayerInfo = Server.FrontEndChatServer:GetChatPlayerInfo()
    if not lastChatplayerInfo then
        Module.Chat.Field.PrivateMsgViewModel:PresetTheChatPlayer_PC(nil, true)
    end

    lastChatplayerInfo = Server.FrontEndChatServer:GetChatPlayerInfo()
    if lastChatplayerInfo then
        nameTxt = lastChatplayerInfo.nick_name
    end

    return true, nameTxt
end

-- 初始化私聊发言状态
function FrontChatInput_PC:PrivateSpeakInit(isPreCall)
    isPreCall = setdefault(isPreCall, false)
    local valid, nameTxt = self:PrivateSpeakValid()
    -- 这里为了tab切换聊天频道特殊逻辑
    if isPreCall and not valid then
        return false
    end

    Module.Chat.Field:SetChatChannel_PC(ChatChannelType.PrivateChatFriend)

    if valid then
        local text = Module.Chat.Config.Loc.SwitchChannelTip_PC
        --BEGIN MODIFICATION @ VIRTUOS :不同平台显示不同提示
        if IsPS5() then
            text = Module.Chat.Config.Loc.SwitchChannelTip_Sony
        elseif IsXSX() or WidgetUtil.IsGamepad() then
            text = Module.Chat.Config.Loc.SwitchChannelTip_XSX
        end
        -- END MODIFICATION
        if #Module.Chat.Field.PrivateMsgViewModel:GetPrivateInfoCache_PC() <= 1 then
            text = ""
        end

        self:SetInputValid(text)
        self:MultiHintTextUpdate()		
        -- BEGIN MODIFICATION - liuyi_b
        -- 平台通信权限检测后根据结果决定是否聚焦
        if (not IsXSX() and not IsPS5()) then
            -- 切换到激活态时聚焦到输入框
            self:FocusKeyboard()
        end
        -- END MODIFICATION - VIRTUOS
    else
        local text = Module.Chat.Config.Loc.NotHavePrivateChatTip_PC
        self:SetInputInValid(text)
    end

    self._wtChatChannel:SetText(self:PackageChannelPrefix(ChatChannelType.PrivateChatFriend, nameTxt))
    -- BEGIN MODIFICATION @ VIRTUOS : TRC:replace chat name by online id
    if IsPS5() then
        local tempInfo = Server.FrontEndChatServer:GetChatPlayerInfo()
        if not tempInfo then
            Module.Chat.Field.PrivateMsgViewModel:PresetTheChatPlayer_PC(nil, true)
        end
        tempInfo = Server.FrontEndChatServer:GetChatPlayerInfo()
        local callback = function(onlineID)
            if tempInfo and not string.isempty(onlineID) then
                self._wtChatChannel:SetText(self:PackageChannelPrefix(ChatChannelType.PrivateChatFriend, onlineID))
            end
        end
        if tempInfo then
            Module.Social:AsyncGetPS5OnlineIdByUID(tempInfo.player_id, callback, self)
        end
    end
    -- END MODIFICATION
    -- BEGIN MODIFICATION - liuyi_b
    -- 平台通信权限检测
    if IsConsole() then
        if Server.ChatServer.hasCommunicationPrivilege == false then
            local text = IsXSX() and Module.Chat.Config.Loc.NotHavePlatformPrivilegeTip or Module.Chat.Config.Loc.NotHavePlatformPrivilegeTip_Sony
            self:SetInputInValid(text)
        else
            --私聊需要根据聊天对象的平台与社交关系进行一对一的通信权限检测
            local chatPlayerInfo = Server.FrontEndChatServer:GetChatPlayerInfo()
            if chatPlayerInfo == nil then
                --没有私聊对象
                self:FocusKeyboard()
                return valid
            end
            local chatPlayerId = chatPlayerInfo.player_id
            local chatPlayerPlat = chatPlayerInfo.plat_id
            if chatPlayerPlat ~= Server.AccountServer:GetPlatIdType() then
                --私聊对象来自其他平台
                Server.ChatServer:CheckAnonymousUserCommunicationPermissions(chatPlayerId, function(isAllowed)
                    if isAllowed then
                        self:FocusKeyboard()
                    else
                        local text = IsXSX() and Module.Chat.Config.Loc.NotHavePlatformPrivilegeTip or Module.Chat.Config.Loc.UnableToPrivateChatWithTargetPlayer
                        self:SetInputInValid(text)
                    end
                end)
            else
                local checkList = {}
                table.insert(checkList, chatPlayerId)
                Server.ChatServer:CheckUsersPermissionsByOpenIdList(EPlatformUserPermissionType.CommunicateUsingText, true, checkList, function(allowedList)
                    if Module.Chat.Field:GetChatChannel_PC() == ChatChannelType.PrivateChatFriend or
                        Module.Chat.Field:GetChatChannel_PC() == ChatChannelType.PrivateChatStanger then
                        if #allowedList > 0 then
                            self:FocusKeyboard()
                        else
                            local text = IsXSX() and Module.Chat.Config.Loc.NotHavePlatformPrivilegeTip or Module.Chat.Config.Loc.UnableToPrivateChatWithTargetPlayer
                            self:SetInputInValid(text)
                        end
                    end
                end)
            end  
       end
    end
    -- END MODIFICATION - VIRTUOS
    return valid
end

-- 初始化队伍发言状态
function FrontChatInput_PC:TeamSpeakInit(isPreCall)
    local result = Module.Chat.Field.TeamViewModel:IsInTeam()
    -- 这里为了tab切换聊天频道特殊逻辑
    if isPreCall and not result then
        return false
    end

    Module.Chat.Field:SetChatChannel_PC(ChatChannelType.TeamChat)

    if result then
        local text = ""
        self:SetInputValid(text)
        self:MultiHintTextUpdate()
        -- BEGIN MODIFICATION - liuyi_b
        -- 平台通信权限检测后根据结果决定是否聚焦
        if not IsConsole() then
            -- 切换到激活态时聚焦到输入框
            self:FocusKeyboard()
        end
        -- END MODIFICATION - VIRTUOS
    else
        local text = Module.Chat.Config.Loc.NotInTeamTip_PC
        self:SetInputInValid(text)
    end

    self._wtChatChannel:SetText(self:PackageChannelPrefix(ChatChannelType.TeamChat))

    -- BEGIN MODIFICATION - liuyi_b
    -- 平台通信权限检测
    if IsConsole() then
        if Server.ChatServer.hasCommunicationPrivilege == false then
            local text = IsXSX() and Module.Chat.Config.Loc.NotHavePlatformPrivilegeTip or Module.Chat.Config.Loc.NotHavePlatformPrivilegeTip_Sony
            self:SetInputInValid(text)
        else
            self:FocusKeyboard()
       end
    end
    -- END MODIFICATION - VIRTUOS
    return result
end

-- 初始化世界发言状态
function FrontChatInput_PC:WorldSpeakInit()
    Module.Chat.Field:SetChatChannel_PC(ChatChannelType.WorldChat)
    local text = ""
    self:SetInputValid(text)
    self._wtChatChannel:SetText(self:PackageChannelPrefix(ChatChannelType.WorldChat))
    -- BEGIN MODIFICATION - liuyi_b
    if IsConsole() then
        -- 平台通信权限检测
        if Server.ChatServer.hasCommunicationPrivilege == false then
            local text = IsXSX() and Module.Chat.Config.Loc.NotHavePlatformPrivilegeTip or Module.Chat.Config.Loc.NotHavePlatformPrivilegeTip_Sony
            self:SetInputInValid(text)
        else
            self:FocusKeyboard()
       end
    else
        -- 切换到激活态时聚焦到输入框
        self:FocusKeyboard()
    end
    -- END MODIFICATION - VIRTUOS
    return true
end

-- 初始化高校发言状态
function FrontChatInput_PC:CollegeSpeakInit()
    Module.Chat.Field:SetChatChannel_PC(ChatChannelType.CollegeChat)
    local text = ""
    self:SetInputValid(text)
    self._wtChatChannel:SetText(self:PackageChannelPrefix(ChatChannelType.CollegeChat))
    -- BEGIN MODIFICATION - liuyi_b
    if IsConsole() then
        -- 平台通信权限检测
        if Server.ChatServer.hasCommunicationPrivilege == false then
            local text = IsXSX() and Module.Chat.Config.Loc.NotHavePlatformPrivilegeTip or Module.Chat.Config.Loc.NotHavePlatformPrivilegeTip_Sony
            self:SetInputInValid(text)
        else
            self:FocusKeyboard()
       end
    else
        -- 切换到激活态时聚焦到输入框
        self:FocusKeyboard()
    end
    -- END MODIFICATION - VIRTUOS
    return true
end

-- 初始化综合发言状态
function FrontChatInput_PC:MultiSpeakInit()
    Module.Chat.Field:SetChatChannel_PC(ChatChannelType.WorldChat)
    local text = Module.Chat.Config.Loc.SwitchChannelTip_PC
    --BEGIN MODIFICATION @ VIRTUOS :不同平台显示不同提示
    if IsPS5() then
        text = Module.Chat.Config.Loc.SwitchChannelTip_Sony
    elseif IsXSX() or WidgetUtil.IsGamepad() then
        text = Module.Chat.Config.Loc.SwitchChannelTip_XSX
    end
    -- END MODIFICATION
    -- 这里判断下 如果没有可以切换tab的情形 就不提示Tab切换发言频道
    local canSwitch = (#Module.Chat.Field.PrivateMsgViewModel:GetPrivateInfoCache_PC() > 0) or (Module.Chat.Field.TeamViewModel:IsInTeam())
    if canSwitch then
        self:SetInputValid(text)
    else
        self:SetInputValid("")
    end
    self._wtChatChannel:SetText(self:PackageChannelPrefix(ChatChannelType.MultiChat))
    -- BEGIN MODIFICATION - liuyi_b
    if IsConsole() then
        -- 平台通信权限检测
       if Server.ChatServer.hasCommunicationPrivilege == false then
            local text = IsXSX() and Module.Chat.Config.Loc.NotHavePlatformPrivilegeTip or Module.Chat.Config.Loc.NotHavePlatformPrivilegeTip_Sony
            self:SetInputInValid(text)
        else
            self:FocusKeyboard()
       end
    else
        -- 切换到激活态时聚焦到输入框
        self:FocusKeyboard()
    end
    -- END MODIFICATION - VIRTUOS
    return true
end

-- 初始化招募发言状态
function FrontChatInput_PC:RecuritSpeakInit()
    self._wtChatChannel:SetText(self:PackageChannelPrefix(ChatChannelType.RecruitmentChat))
    self._wtEditTxtInput:SetIsReadOnly(true)
    self._wtEditTxtInput:SetHintText("")
    self._wtBlockInputControl:Collapsed()

    -- BEGIN MODIFICATION - VIRTUOS
    if IsConsole() then
        -- 平台通信权限检测
        if Server.ChatServer.hasCommunicationPrivilege == false then
            local text = IsXSX() and Module.Chat.Config.Loc.NotHavePlatformPrivilegeTip or Module.Chat.Config.Loc.NotHavePlatformPrivilegeTip_Sony
            self:SetInputInValid(text)
        else
            self:FocusKeyboard()
       end
    end
    -- END MODIFICATION - VIRTUOS
end

-- 设置输入框不可用样式
function FrontChatInput_PC:SetInputInValid(text)
    self._wtEditTxtInput:SetIsReadOnly(true)
    self._wtEditTxtInput:SetHintText(text)
    self._wtBlockInputControl:Visible()

    --BEGIN MODIFICATION @ VIRTUOS :
    if WidgetUtil.IsGamepad() == true then
        -- 聊天框不能输入时，尝试聚焦到其它地方
        if self._UIInst then
            self._wtBlockInputControl:SetKeyboardFocus()
        end
    end
    --END MODIFICATION
end

-- 设置输入框可用样式
function FrontChatInput_PC:SetInputValid(text)
    self._wtEditTxtInput:SetIsReadOnly(false)
    self._wtEditTxtInput:SetHintText(text)
    self._wtBlockInputControl:Collapsed()
    --BEGIN MODIFICATION @ VIRTUOS :
    if WidgetUtil.IsGamepad() == true then
        -- 聊天框不能输入时，通过尝试聚焦到不可聚焦控件的方式移除聚焦效果
        self._wtBlockInputControl:SetKeyboardFocus()
    end
    --END MODIFICATION
end

function FrontChatInput_PC:PackageChannelPrefix(chatChannel, nameTxt)
    local channelTxt = Module.Chat.Config.Loc.ChatNoChannelPCLocText

    local PackagePriavateTxt = function(originTxt)
        if chatChannel == ChatChannelType.PrivateChatFriend or (
            chatChannel == ChatChannelType.PrivateChatStanger) then

            if nameTxt then
                originTxt = nameTxt
            end
        end

        return string.format("[%s] :", originTxt)
    end

    if chatChannel then
        channelTxt = Module.Chat.Config.EChannel2CNName[chatChannel] or channelTxt
        channelTxt = PackagePriavateTxt(channelTxt)
        channelTxt = string.format(Module.Chat.Config.EChatChannel2XML[chatChannel], channelTxt)
    end

    return channelTxt
end

--------------------------------------- 快捷键绑定的频道切换功能 ------------------------------------
function FrontChatInput_PC:OnExecuteTabClick()
    if not self._UIInst then
        return
    end

    -- 目前仅综合频道和私聊频道可以切换发言频道
    local curChannelType = self._UIInst:GetCurUIChannelType()
    if (curChannelType ~= ChatChannelType.MultiChat) and (curChannelType ~= ChatChannelType.PrivateChatFriend) then
        return
    end

    if curChannelType == ChatChannelType.MultiChat then
        self:ExecuteUIMultiSwitch()
    elseif curChannelType == ChatChannelType.PrivateChatFriend then
        self:ExecuteUIPrivateSwitch()
    elseif curChannelType == ChatChannelType.PrivateChatStanger then
        self:ExecuteUIPrivateSwitch()
    end

end

-- 综合频道仍然需要刷新下输入框hint文本
function FrontChatInput_PC:MultiHintTextUpdate()
    local curChannelType = self._UIInst:GetCurUIChannelType()
    if curChannelType ~= ChatChannelType.MultiChat then
        return
    end

    -- 这里加个补丁 综合频道只要能切换频道就需要灰字提示
    local canSwitch = (#Module.Chat.Field.PrivateMsgViewModel:GetPrivateInfoCache_PC() > 0) or (Module.Chat.Field.TeamViewModel:IsInTeam())
    local text = Module.Chat.Config.Loc.SwitchChannelTip_PC
    --BEGIN MODIFICATION @ VIRTUOS :
    -- 不同平台显示不同提示
    if IsPS5() then
        text = Module.Chat.Config.Loc.SwitchChannelTip_Sony
    elseif IsXSX() or WidgetUtil.IsGamepad() then
        text = Module.Chat.Config.Loc.SwitchChannelTip_XSX
    end
    --END MODIFICATION
    if canSwitch then
        self._wtEditTxtInput:SetHintText(text)
    else
        self._wtEditTxtInput:SetHintText((""))
    end
end

-- TODO 这一段代码等转测期有空的时候优化下 现在一坨
function FrontChatInput_PC:ExecuteUIMultiSwitch()
    local curChatChannel =  Module.Chat.Field:GetChatChannel_PC()
    if curChatChannel == ChatChannelType.WorldChat then
        local result = self:PrivateSpeakInit(true)
        if not result then
            self:TeamSpeakInit(true)
        end
    elseif curChatChannel == ChatChannelType.PrivateChatFriend then
        local result = self:TeamSpeakInit(true)
        if not result then
            self:MultiSpeakInit()
        end
    elseif curChatChannel == ChatChannelType.PrivateChatStanger then
        local result = self:TeamSpeakInit(true)
        if not result then
            self:MultiSpeakInit()
        end
    elseif curChatChannel == ChatChannelType.TeamChat then
        self:MultiSpeakInit()
    end
end

function FrontChatInput_PC:ExecuteUIPrivateSwitch()
    Module.Chat.Field.PrivateMsgViewModel:OnTabSwitchChatPlayer_PC()
    self:PrivateSpeakInit(true)
end

--------------------------------------- End ------------------------------------

return FrontChatInput_PC