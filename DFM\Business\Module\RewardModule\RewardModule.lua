----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMReward)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class RewardModule : ModuleBase
local RewardModule = class("RewardModule", require("DFM.YxFramework.Managers.Module.ModuleBase"))
local RewardEventLogic = require "DFM.Business.Module.RewardModule.Logic.RewardEventLogic"
local RewardViewLogic = require "DFM.Business.Module.RewardModule.Logic.RewardViewLogic"
local SettlementDefine = require "DFM.Business.DataStruct.SettlementStruct.SettlementDefine"

function RewardModule:Ctor()
end

function RewardModule:OnInitModule()
	RewardEventLogic.AddRewardListeners()
	RewardEventLogic.AddBlindBoxListeners()
end

function RewardModule:OnLoadModule()

end

function RewardModule:OnDestroyModule()
	RewardEventLogic.RemoveRewardListeners()
	RewardEventLogic.RemoveBlindBoxListeners()
	self:CloseSyetemOpenPanel()
	self:RemoveAllLuaEvent()
end

---@param gameFlowType EGameFlowStageType
function RewardModule:OnGameFlowChangeLeave(gameFlowType)
	--[[
	if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then
		RewardEventLogic.RemoveRewardListeners()
		self:CloseRewardPanel()
		self:CloseSyetemOpenPanel()
	end
	--]]
	self:CloseSyetemOpenPanel()
end

---@param gameFlowType EGameFlowStageType
function RewardModule:OnGameFlowChangeEnter(gameFlowType)
	--[[
	if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then
        RewardEventLogic.AddRewardListeners()
    end
    --]]
end

---------------public api--------------------
-- 打开武器升级解锁和普通获得奖励界面
-- title 标题
-- tip 内容提示语
-- itemList:itembase[] 奖励列表
-- qualityList:{id, num}[] 角色品质列表
-- bShowOnlyInMP 仅在MP模式中展示
-- bUseCustomSort 保持传入的自定义排序
-- bShowSpecialPanels 是否显示各种道具对应的特殊获得界面
-- ignoreSpecialPanelTypes 无需展示的特殊获得界面类型
-- backgroundType 自定义背景类型  不传则用默认
function RewardModule:OpenRewardPanel(title, tip, items, qualities, bShowOnlyInMP, bUseCustomSort, bShowSpecialPanels, ignoreSpecialPanelTypes, backgroundType)
	if items and #items > 0 then
		local legalItems = {}
		for index, item in ipairs(items) do
			if RewardViewLogic.CheckIslegalItemId(item.id) then
				table.insert(legalItems, item)
			end
		end
		if bShowSpecialPanels == true then
			self:OpenSpecialRewardPanel(legalItems, ignoreSpecialPanelTypes)
		end
		RewardViewLogic.ShowRewardPanel(title, tip, legalItems, qualities, bShowOnlyInMP, bUseCustomSort, backgroundType)
	end
end

function RewardModule:OpenSpecialRewardPanel(items, ignoreSpecialPanelTypes)
	if items and #items > 0 then
		local bHasMoreWeaponSkin = false
		table.sort(items, RewardViewLogic.UniversalSort)
		for index, item in ipairs(items) do
			if not ignoreSpecialPanelTypes or not table.contains(ignoreSpecialPanelTypes, Module.Reward.Config.EShowPopPanelType.WeaponSkinGain) then
				self:OpenWeaponSkinGainPop(item, bHasMoreWeaponSkin)
				if RewardViewLogic.CheckIsWeaponSkin(item.id) then
					bHasMoreWeaponSkin = true
				end
			end
			if not ignoreSpecialPanelTypes or not table.contains(ignoreSpecialPanelTypes, Module.Reward.Config.EShowPopPanelType.VehicleSkinGain) then
				self:OpenVehicleSkinGainPop(item)
			end
			if not ignoreSpecialPanelTypes or not table.contains(ignoreSpecialPanelTypes, Module.Reward.Config.EShowPopPanelType.PendantGain) then
				self:OpenPendantGainPop(item)
			end
			if not ignoreSpecialPanelTypes or not table.contains(ignoreSpecialPanelTypes, Module.Reward.Config.EShowPopPanelType.HeroUnlock) then
				self:OpenHeroStuffUnlockPanel(item.id)
			end
			if not ignoreSpecialPanelTypes or not table.contains(ignoreSpecialPanelTypes, Module.Reward.Config.EShowPopPanelType.WatchUnlock) then
				self:OpenWatchUnlockPanel(item.id)
			end
			if not ignoreSpecialPanelTypes or not table.contains(ignoreSpecialPanelTypes, Module.Reward.Config.EShowPopPanelType.StuffUnlock) then
				self:OpenHeroCardUnlockPanel(item.id)
				self:OpenProfilePhotoUnlockPanel(item.id)
				self:OpenExecutionUnlockPanel(item.id)
				self:OpenGestureUnlockPanel(item.id)
				self:OpenKillLineUnlockPanel(item.id)
				self:OpenBrandUnlockPanel(item.id)
				self:OpenSprayPaintUnlockPanel(item.id)
			end
			if not ignoreSpecialPanelTypes or not table.contains(ignoreSpecialPanelTypes, Module.Reward.Config.EShowPopPanelType.VehicleGain) then
				self:OpenVehicleGainPop(item)
			end
			if not ignoreSpecialPanelTypes or not table.contains(ignoreSpecialPanelTypes, Module.Reward.Config.EShowPopPanelType.SafeBoxGain) then
				self:OpenSafeBoxGainPop(item.id)
			end
		end
	end
end

-- 打开账号、安全屋升级奖励界面
-- deviceId 设备id
-- fCallbackIns 页面打开后的回调函数
function RewardModule:OpenSafehouseLevelUpRewardPanel(deviceId, fCallbackIns)
	RewardViewLogic.ShowSafehouseLevelUpRewardPanel(deviceId, fCallbackIns)
end


-- 打开解锁奖励界面
-- 自定义解锁项
function RewardModule:OpenModuleUnlockPanel(moduleId)
	RewardViewLogic.ShowModuleUnlockPanel(moduleId)
end

function RewardModule:OpenBHDUnlockPop()
	RewardViewLogic.ShowBHDUnlockPop()
end

-- 打开商店声望升级界面
-- unlockedItems  解锁商店道具列表
-- merchantId  商人ID
function RewardModule:OpenReputationLevelUpPanel(unlockedItems, merchantId)
	RewardViewLogic.ShowReputationLevelUpPanel(unlockedItems, merchantId)
end



-- 打开系统开启界面
-- title:		String 		界面标题
-- name:		String 		开启系统名称
-- systemImg:	String 		系统图片路径
-- fCloseCb:	Function 	关闭回调，【可选参数】
function RewardModule:OpenSyetemOpenPanel(title, name, systemImg, fCloseCb, questLineInfo)
--(title, name, nameBgStyle, systemImg, typeImg, fCloseCb)
	RewardViewLogic.ShowSystemOpenPanelProcess(title, name, systemImg, fCloseCb, questLineInfo)
end

function RewardModule:OpenQuestDetailNewPanel(title, fCloseCb, questInfos)
	RewardViewLogic.ShowQuestDetailOpenPanelProcess(title, fCloseCb, questInfos)
end

function RewardModule:CloseSyetemOpenPanel()
	RewardViewLogic.CloseSystemOpenPanelProcess()
end

--打开SOL成长之路升级界面
function RewardModule:OpenSOLPathOfGrowthLevelUpPanel(currentLv, preLv)
	if Server.SettlementServer:GetSettlementInfoSource() == SettlementDefine.ESettlementInfoSource.SOL then
		local totalExp = 0
		local seasonExpDetailList = Server.RoleInfoServer.seasonExpDetail or {}
		for index, expDetail in ipairs(seasonExpDetailList) do
			if expDetail.reason == EAddExpReason.kAddExpReasonKill 
			or expDetail.reason == EAddExpReason.kAddExpReasonKillAI 
			or expDetail.reason == EAddExpReason.kAddExpReasonKillEliteAI
			or expDetail.reason == EAddExpReason.kAddExpReasonKillBoss
			or expDetail.reason == EAddExpReason.AddExpReasonProduce 
			or expDetail.reason == EAddExpReason.kAddExpReasonFirstLoot 
			or expDetail.reason == EAddExpReason.kAddExpReasonFirstArea 
			or expDetail.reason == EAddExpReason.kAddExpReasonProduce
			or expDetail.reason == EAddExpReason.kAddExpReasonContract
			or expDetail.reason == EAddExpReason.kAddExpReasonBluePrint
			or expDetail.reason == EAddExpReason.kAddExpReasonGameEscape
			or expDetail.reason == EAddExpReason.kAddExpReasonFix then
				totalExp = totalExp + expDetail.add_exp
			end
		end
		if totalExp > 0 and Server.RoleInfoServer.deltaExp > 0 then
			RewardViewLogic.ShowSOLPathOfGrowthLevelUpPanel(currentLv, preLv)
		else
			Module.Settlement:OpenSettlementUI(UIName2ID.PathOfGrowthLevelUpPanel)
		end
	else
		RewardViewLogic.ShowSOLPathOfGrowthLevelUpPanel(currentLv, preLv)
	end
end

--打开MP成长之路升级界面
function RewardModule:OpenMPPathOfGrowthLevelUpPanel(currentLv, preLv)
	RewardViewLogic.ShowMPPathOfGrowthLevelUpPanel(currentLv, preLv)
end

--打开BP结算界面
function RewardModule:OpenBPSettlementPanel(iBeginLevel, iUpToLevel, fBeginExp, fUpToExp, iExpSourceType, iExpChgNum)
	RewardViewLogic.OpenBPSettlementPanel(iBeginLevel, iUpToLevel, fBeginExp, fUpToExp, iExpSourceType, iExpChgNum)
end

--打开BP解锁展示界面
function RewardModule:OpenBPUnlockPanel(eType)
	RewardViewLogic.OpenBPUnlockPanel(eType)
end

-- 打开英雄与枪械解锁界面
-- title:		String 		界面标题
-- name:		String 		英雄/枪械名称
-- thingImg:	String 		英雄/枪械图片路径
-- fCloseCb:	Function 	关闭回调，【可选参数】
function RewardModule:OpenHeroStuffUnlockPanel(heroStuffId)
	if RewardViewLogic.CheckIsHero(heroStuffId) or RewardViewLogic.CheckIsHeroFashion(heroStuffId) or RewardViewLogic.CheckIsHeroAnim(heroStuffId) then
		RewardViewLogic.ShowHeroStuffUnlockPanel(heroStuffId)
	end
end

function RewardModule:OpenWatchUnlockPanel(watchId)
	if RewardViewLogic.CheckIsWatch(watchId) then
		RewardViewLogic.ShowWatchUnlockPanel(watchId)
	end
end

function RewardModule:OpenHeroCardUnlockPanel(heroCardId)
	if RewardViewLogic.CheckIsHeroCard(heroCardId) then
	    RewardViewLogic.ShowStuffGainPop(0, heroCardId)
	end
end

function RewardModule:OpenKillLineUnlockPanel(killLineId)
	if RewardViewLogic.CheckIsKillline(killLineId) then
		RewardViewLogic.ShowStuffGainPop(1, killLineId)
	end
end

function RewardModule:OpenSprayPaintUnlockPanel(sprayPaintId)
	if RewardViewLogic.CheckIsSprayPaint(sprayPaintId) then
	    RewardViewLogic.ShowStuffGainPop(2, sprayPaintId)
	end
end

function RewardModule:OpenProfilePhotoUnlockPanel(profilePhotoId)
	if RewardViewLogic.CheckIsProfilePhoto(profilePhotoId) then
	    RewardViewLogic.ShowStuffGainPop(3, profilePhotoId)
	end
end

function RewardModule:OpenBrandUnlockPanel(brandId)
	if RewardViewLogic.CheckIsBrand(brandId) then
	    RewardViewLogic.ShowStuffGainPop(4, brandId)
	end
end


function RewardModule:OpenGestureUnlockPanel(gestureId)
	if RewardViewLogic.CheckIsGesture(gestureId) then
	    RewardViewLogic.ShowStuffGainPop(6, gestureId)
	end
end

function RewardModule:OpenExecutionUnlockPanel(executionId)
	if RewardViewLogic.CheckIsExecution(executionId) then
	    RewardViewLogic.ShowStuffGainPop(6, executionId)
	end
end


function RewardModule:OpenMandelBrickGainPop(mandelBrickId)
	if RewardViewLogic.CheckIsMandelBrick(mandelBrickId) then
		RewardViewLogic.ShowMandelBrickGainPop(mandelBrickId)
	end
end

function RewardModule:OpenPendantGainPop(pendantItem)
	if RewardViewLogic.CheckIsPendant(pendantItem.id) then
		RewardViewLogic.ShowPendantGainPop(pendantItem)
	end
end

function RewardModule:OpenWeaponSkinGainPop(weaponSkinItem, bHasMore)
	if RewardViewLogic.CheckIsWeaponSkin(weaponSkinItem.id) then
    	RewardViewLogic.ShowWeaponSkinGainPop(weaponSkinItem, bHasMore)	
	end
end

function RewardModule:OpenWeaponSkinRenamedPage(weaponSkinItem)
	if RewardViewLogic.CheckIsWeaponSkin(weaponSkinItem.id) then
	    RewardViewLogic.ShowWeaponSkinRenamedPage(weaponSkinItem)
	end
end

function RewardModule:OpenWeaponGainPop(weaponItems)
	local validWeaponItems = {}
	for index, weaponItem in ipairs(weaponItems) do
		if RewardViewLogic.CheckIsWeapon(weaponItem.id) then
			table.insert(validWeaponItems, weaponItem)
		end
	end
	RewardViewLogic.ShowWeaponGainPop(validWeaponItems)
end

function RewardModule:OpenWeaponAdapterGainPop(adapterItem)
	if RewardViewLogic.CheckIsWeaponAdapter(adapterItem.id) then
	    RewardViewLogic.ShowWeaponAdapterGainPop(adapterItem)
	end
end

function RewardModule:OpenVehicleGainPop(vehicleItem)
	if RewardViewLogic.CheckIsVehicle(vehicleItem.id) then
		RewardViewLogic.OpenVehicleSkinGainPop(vehicleItem)
	end
end

function RewardModule:OpenSafeBoxGainPop(safeBoxId)
	if RewardViewLogic.CheckIsSafeBox(safeBoxId) then
	    RewardViewLogic.ShowSafeBoxGainPop(safeBoxId)
	end
end

--- 弹出客户端本地缓存的弹窗
function RewardModule:DoCachePopAction(delayTime)
	delayTime = setdefault(delayTime, 1)
	local fCallBack = function()
		RewardViewLogic.DoCachePopAction()
	end
	if delayTime and delayTime > 0 then
		Timer.DelayCall(delayTime, function ()
			if fCallBack then
				fCallBack()
			end
		end)
	else
		if fCallBack then
			fCallBack()
		end
	end
end

function RewardModule:ClearCachePopAction()
	RewardViewLogic.ClearCachePopAction()
end


---检查并Pop队列中储存的弹窗
function RewardModule:ShowRewardsIfPossible()
	Module.ItemDetail:CloseAllPopUI()
	RewardViewLogic.ShowRewardsIfPossible()
end

function RewardModule:ShowNextRewards(bStopPopup)
	Module.ItemDetail:CloseAllPopUI()
	RewardViewLogic.ShowNextRewards(bStopPopup)
end

function RewardModule:CloseAndStopPopUp()
	RewardViewLogic.ShowNextRewards(true)
end

function RewardModule:ClearActionByType(showPopPanelType)
	RewardViewLogic.ClearActionByType(showPopPanelType)
end

function RewardModule:ContainsSpecialPanelInQueue()
	return RewardViewLogic.ContainsSpecialPanelInQueue()
end

function RewardModule:HasActionInQueue(showPopPanelType)
	return RewardViewLogic.HasActionInQueue(showPopPanelType)
end

function RewardModule:SkipAllSpecialPanels()
	RewardViewLogic.SkipAllSpecialPanels()
end

--action要返回UIhandle
function RewardModule:PushShowPopUIAction(showPopPanelType, action)
	Module.Reward.Field:PushShowPopUIAction(showPopPanelType, action)
end


function RewardModule:GetPendingPopUINum()
	return RewardViewLogic.GetPendingPopUINum()
end

function RewardModule:GetPendingPopUINumByType(showPopPanelType)
	return RewardViewLogic.GetPendingPopUINumByType(showPopPanelType)
end

function RewardModule:OpenRewardSceneView(fCallback, itemTable, bTenDraws, iType)
	if IsHD() or Module.Reward.Field:GetShowTenSceneInMobile() then
		if bTenDraws then
			Facade.UIManager:AsyncShowUI(UIName2ID.RewardSceneViewTen, nil, nil, fCallback, itemTable, bTenDraws, iType)
		else
			Facade.UIManager:AsyncShowUI(UIName2ID.RewardSceneView, nil, nil, fCallback, itemTable, bTenDraws, iType)
		end
	else
		Facade.UIManager:AsyncShowUI(UIName2ID.RewardSceneView, nil, nil, fCallback, itemTable, bTenDraws, iType)
	end
end


function RewardModule:EnableNTFCall(key, bEnable)
	RewardViewLogic.EnableNTFCall(key, bEnable)
end

function RewardModule:IsNTFCallEnabled(key)
    return RewardViewLogic.IsNTFCallEnabled(key)
end

function RewardModule:EnablePopUI(bEnable)
	RewardViewLogic.EnablePopUI(bEnable)
end

function RewardModule:IsShowingPopUI()
	logerror("RewardModule:IsShowingPopUI",RewardViewLogic.GetPendingPopUINum() > 0, self.Field:IsCurrentPanelHandleValid())
	return RewardViewLogic.GetPendingPopUINum() > 0 or self.Field:IsCurrentPanelHandleValid()
end

-- 是否正在开启曼德尔砖场景接口 start-- 
function RewardModule:SetShowRewardScene(bShowRewardScene)
	Module.Reward.Field:SetShowRewardScene(bShowRewardScene)
end

function RewardModule:GetShowRewardScene()
	return Module.Reward.Field:GetShowRewardScene()
end
-- 是否正在开启曼德尔砖场景接口 End-- 

function RewardModule:OpenVehicleSkinGainPop(vehicleItem)
	if RewardViewLogic.CheckIsVehicleSkin(vehicleItem.id) then
		RewardViewLogic.OpenVehicleSkinGainPop(vehicleItem)
	end
end

function RewardModule:MysticalUnboxingAudioEvent(stage)
	Module.Reward.Config.Events.evtMysticalUnboxingAudio:Invoke(stage)
end

function RewardModule:ToggleStackUI(bEnable)
	RewardViewLogic.ToggleStackUI(bEnable)
end

function RewardModule:ToggleStackUILayer(bEnable)
	RewardViewLogic.ToggleStackUILayer(bEnable)
end

function RewardModule:HasStoreStackUILevel()
	return RewardViewLogic.HasStoreStackUILevel()
end

return RewardModule