---------- LOG FUNCTION AUTO GENERATE -------------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
-------- LOG FUNCTION AUTO GENERATE END -----------

---对应蓝图:WBP_Example
---@class MorgenRewardItem : LuaUIBaseView
local MorgenRewardItem = ui("MorgenRewardItem")
local ActivityConfig = Module.Activity.Config

function MorgenRewardItem:Ctor()
	--- TODO btn onclicked
	self._wBtn = self:Wnd("DFButton_45", UIButton)
	self._wtMask = self:Wnd("DFImage_306", UIImage)
	self._wtImage = self:Wnd("DFImage_67", UIImage)
	self._wtText = self:Wnd("DFTextBlock_60", UITextBlock)
end

--- 透传oncliked事件
function MorgenRewardItem:Event(delegateName, fCallback, caller, ...)
    if self._wtBtn then
        self._wtBtn:Event(delegateName, fCallback, caller,  ...)
    end
end

function MorgenRewardItem:RefreshInfo(rewardInfo)
	self._wtImage:AsyncSetImagePath(rewardInfo.image)
	
	if rewardInfo.received then
		self._wtMask:Visible()
	else
		self._wtMask:Collapsed()
	end
end

function MorgenRewardItem:SetText(text)
	self._wtText:SetText(text)
end

function MorgenRewardItem:RefreshUI()
end

return MorgenRewardItem