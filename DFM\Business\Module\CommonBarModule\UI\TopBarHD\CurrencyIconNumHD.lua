----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCommonBar)
----- LOG FUNCTION AUTO GENERATE END -----------
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"

--BEGIN MODIFICATION @ VIRTUOS : 
local EGPInputType = import"EGPInputType"
local UGPInputDelegates = import "GPInputDelegates"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
--END MODIFICATION

---@class CurrencyIconNumHD : LuaUIBaseView
local CurrencyIconNumHD = ui("CurrencyIconNumHD")
local ShopHelperTool = require "DFM.StandaloneLua.BusinessTool.ShopHelperTool"

function CurrencyIconNumHD:Ctor()
    --self._currencyId = ECurrencyClientId.Tina
    self._wtImgCurrency = self:Wnd("Image_Currency", UIImage)
    self._wtTbCurrencyValue = self:Wnd("wtTextBlock_value", UITextBlock)
    self._wtTbCurrencyValue:SetText("")
    -- self._wtBtnCurrencyTip = self:Wnd("ButtonCurrency", UIButton)
    -- self._wtBtnCurrencyTip:Event("OnClicked", self.OnCurrencyTipBtnClick, self)
    -- self._wtBtnCurrencyAdd = self:Wnd("Button_44", UIButton)
    -- self._wtBtnCurrencyAdd:Event("OnClicked", self.OnCurrencyAddBtnClick, self)
    self._wtAddBtn = self:Wnd("_wtAddBtn", UIButton)
    self._wtAddBtn:Event("OnClicked", self.OnCharge, self)

    --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() then
        -- 不同币种对应不同的按键
        self._gamePadActionNames = {
            [ECurrencyClientId.Diamond] = "RechargeCurrency_Gamepad",
            [ECurrencyClientId.UnbindDiamond] = "RechargeCurrency_Gamepad",
            [ECurrencyClientId.MandelCoins] = "RechargeCurrency2_Gamepad",
            [ECurrencyClientId.QuantumKey] = "RechargeCurrency3_Gamepad",
        }
        --she2适配不同按钮(国服/海外)
        if IsBuildRegionCN() then
            self._gamePadActionNames[ECurrencyClientId.LogisticsVoucher] = "RechargeCurrency_Gamepad"
            self._gamePadActionNames[ECurrencyClientId.ArkCoin] = "RechargeCurrency2_Gamepad"
            self._gamePadActionNames[ECurrencyClientId.QixiCoin] = "OpenBHDChat"
            self._gamePadActionNames[ECurrencyClientId.SummerCoin] = "OpenBHDChat"
            self._gamePadActionNames[ECurrencyClientId.AppearanceCoin] = "OpenBHDChat"
            self._gamePadActionNames[ECurrencyClientId.SpecialCoin] = "RevokeBind"
            self._gamePadActionNames[ECurrencyClientId.CelebrationCoin] = "OpenBHDChat"
        else
            self._gamePadActionNames[ECurrencyClientId.LogisticsVoucher] = "RechargeCurrency_Gamepad"
            self._gamePadActionNames[ECurrencyClientId.HeroCoin] = "OpenBHDChat"
            self._gamePadActionNames[ECurrencyClientId.ArkCoin] = "RechargeCurrency2_Gamepad"
            self._gamePadActionNames[ECurrencyClientId.AtSeaCoin] = "RevokeBind"
            self._gamePadActionNames[ECurrencyClientId.GlobalCoin] = "RevokeBind"
            self._gamePadActionNames[ECurrencyClientId.CelebrationCoin] = "RevokeBind"
        end
        self._wtCurrencyKeyIcon = self:Wnd("CurrencyKeyIcon", UIWidgetBase)
        self._wtCurrencyKeyIcon:SetOnlyDisplayOnGamepad(true)
    end
    --- END MODIFICATION    
end

function CurrencyIconNumHD:OnOpen()
    if rawget(Server, "CurrencyServer") then
        self:RemoveLuaEvent(Server.CurrencyServer.Events.evtCurrencyNumChanged)
        self:AddLuaEvent(Server.CurrencyServer.Events.evtCurrencyNumChanged, self.OnEvtCurrencyNumChanged, self)
    end
    if rawget(Server, "RankingServer") then
        self:AddLuaEvent(Server.RankingServer.Events.evtSeasonMallCurrencyUpdated, self.OnEvtCurrencyNumChanged, self)
    end
end

function CurrencyIconNumHD:OnClose()
    self:RemoveAllLuaEvent()
end

function CurrencyIconNumHD:OnEvtCurrencyNumChanged()
    local num = Module.Currency:GetRealNum(self._currencyId)
    self:SetCurrencyInfoById(self._currencyId, num)
end

function CurrencyIconNumHD:SetCurrencyInfoById(currencyId, num)
    self._currencyId = currencyId
    local numStr = ShopHelperTool.GetCurrencyNumFormatStr(num)

    self._wtTbCurrencyValue:SetText(numStr)
    if Facade.ModuleManager:IsModuleValid("Currency") then
        local currencyImgPath = Module.Currency:GetImgPath(self._currencyId)
        self._wtImgCurrency:AsyncSetImagePath(currencyImgPath)
    end

    if Module.CommonBar.Config.MapCurrencyCID2CanRecharge[self._currencyId] then
        self._wtAddBtn:Visible()
        --- BEGIN MODIFICATION @ VIRTUOS 
        if IsHD() and WidgetUtil.GetCurrentInputType() == EGPInputType.Gamepad then
            self._wtCurrencyKeyIcon:SelfHitTestInvisible()
        end
        --- END MODIFICATION
    else
        self._wtAddBtn:Collapsed()
        --- BEGIN MODIFICATION @ VIRTUOS 
        if IsHD() then
            self._wtCurrencyKeyIcon:Collapsed()
        end
        --- END MODIFICATION
    end
end

--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
function CurrencyIconNumHD:OnHideBegin()
    if IsHD() then
        self:_DisableGamepadFeature()
    end
end
--- END MODIFICATION

function CurrencyIconNumHD:OnHide()
    -- @todo 不清空有bug
    self._wtImgCurrency.usingImagePath = nil
    self._wtImgCurrency.loadingImagePath = nil
end

-- function CurrencyIconNumHD:OnCurrencyTipBtnClick()
-- 	--点券不弹tips
-- 	if self._currencyId == ECurrencyClientId.Diamond then
-- 		Module.Store:ShowStoreRechargeMainPanle()
-- 	else
-- 		Module.CommonBar.Config.evtShowCurrencyTip:Invoke(self, self._currencyId)
-- 	end
-- end

-- function CurrencyIconNumHD:OnCurrencyAddBtnClick()
-- 	if self._currencyId == ECurrencyClientId.Diamond then
-- 		Module.Store:ShowStoreRechargeMainPanle()
-- 	end
-- end

function CurrencyIconNumHD:OnCharge()
    Module.CommonBar.Config.evtOnChangeButtonClicked:Invoke()
    if Server.ArmedForceServer:GetCurArmedForceMode() == EArmedForceMode.MP then
        LogAnalysisTool.SignButtonClicked(10120025)
    else
        LogAnalysisTool.SignButtonClicked(10030023)
    end
    
    if self._currencyId == ECurrencyClientId.Diamond or self._currencyId == ECurrencyClientId.UnbindDiamond then
        Module.Store:ShowStoreRechargeMainPanle()
    elseif self._currencyId == ECurrencyClientId.Lucky then
        Module.Store:ShowBuyLuckyCoinWindow()
    elseif self._currencyId == ECurrencyClientId.MandelCoins then -- 曼德尔币
        Facade.UIManager:AsyncShowUI(UIName2ID.ExchangeMandelCoins, nil, nil, 17888808887)
    elseif self._currencyId == ECurrencyClientId.QuantumKey then
		local buyManderKeysConfig = Server.StoreServer:GetSpecialItemInfoByPresentItemId(32320000001)
		Module.Store.OpenStoreMandelBuyKeysPanel(nil, buyManderKeysConfig, 0, 0, 1, 1)
    elseif self._currencyId == ECurrencyClientId.Reinvention then
		local mysticalLotteryId = Module.Hero:GetCurShowHeroPropsMysticalLotteryId()
		if mysticalLotteryId then
			Facade.UIManager:AsyncShowUI(UIName2ID.HeroPropMysticalSkinPurchasePop, nil, nil,nil,  ECurrencyItemId.Reinvention, 1 , mysticalLotteryId)
		end
    else
        if self:_ActTestCoin(self._currencyId) then
			return
		end
        --- MS21 开发中
        Module.CommonTips:ShowSimpleTip(Module.CommonTips.Config.Loc.CommingSoon)
    end
end

--活动货币逻辑(策划某改)
function CurrencyIconNumHD:_ActTestCoin(itemId)
	--货币转化
    local curkey = nil
    for key, value in pairs(ECurrencyClientId or {}) do
        if itemId == value then
            curkey = key
            break
        end
    end
    for key, value in pairs(ECurrencyItemId or {}) do
        if curkey == key then
            Module.Activity:OpenLogisticsVoucherPanel(value)
            return true
        end
    end
    return false
end

--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
function CurrencyIconNumHD:SetGamepadFeature()
    if not IsHD() then
        return 
    end

    if self._wtCurrencyKeyIcon then
        self._wtCurrencyKeyIcon:Collapsed()
        --并不是所有的货币都需要使用手柄快捷键
        self._wtCurrencyKeyIcon:SetOnlyDisplayOnGamepad(false)        
    end

    if self._gamePadActionNames == nil then
        return
    end

    if self._gamePadActionNames[self._currencyId] == nil then
        return
    end

    self:_DisableGamepadFeature()

    if self._wtCurrencyKeyIcon then
        local actionName = self._gamePadActionNames[self._currencyId]
        if actionName == "RechargeCurrency3_Gamepad" or actionName == "RechargeCurrency_Gamepad" then
            self._wtCurrencyKeyIcon:SetOnlyDisplayOnGamepad(true)
            self._wtCurrencyKeyIcon:InitByDisplayInputActionName(actionName, true, 0, true)
            if self._CurrencyPress == nil then
                self._CurrencyPress = self:AddInputActionBinding(
                    actionName,
                    EInputEvent.IE_Pressed,
                    self.OnCharge,
                    self,
                    EDisplayInputActionPriority.UI_Stack
                )
            end
        else
            self:_SetLongPressAction(actionName)
        end

        if not self._OnNotifyInputTypeChangedHandle then
            self._OnNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._HandleInputTypeChanged, self))
        end

        self:_HandleInputTypeChanged(WidgetUtil.GetCurrentInputType())
    end

end

function CurrencyIconNumHD:_SetLongPressAction(InActionName)
    if not IsHD() then
        return 
    end

    self._wtCurrencyKeyIcon:SetOnlyDisplayOnGamepad(true)
    self._wtCurrencyKeyIcon:InitByDisplayInputActionName(InActionName, true, 0, true)
    self._wtCurrencyKeyIcon:BP_ShowHoldProgressBarTips(true)

    if not self._CurrencyLongPress then
        self._CurrencyLongPress = self:AddHoldInputActionBinding(InActionName, self.OnCharge, self, EDisplayInputActionPriority.UI_Stack)
        self:AddHoldInputActionProgressedBinding(self._CurrencyLongPress, self.OnConfirmBtnInLongPressing, self)
        self:AddHoldInputActionReleaseBinding(self._CurrencyLongPress, self.OnConfirmBtnLongPressFinished, self)
    end 
end

function CurrencyIconNumHD:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end

    if self._CurrencyLongPress then
        self:RemoveHoldInputActionBinding(self._CurrencyLongPress)
        self._CurrencyLongPress = nil
    end

    if self._CurrencyPress then
        self:RemoveInputActionBinding(self._CurrencyPress)
        self._CurrencyPress = nil
    end

    if self._wtCurrencyKeyIcon then
        self._wtCurrencyKeyIcon:BP_ShowHoldProgressBarTips(false)
    end
end

function CurrencyIconNumHD:OnConfirmBtnInLongPressing(Percent)
    if self._wtCurrencyKeyIcon then
        self._wtCurrencyKeyIcon:BP_UpdateProgressBar(Percent) 
    end
end

function CurrencyIconNumHD:OnConfirmBtnLongPressFinished()
    if self._wtCurrencyKeyIcon then
        self._wtCurrencyKeyIcon:BP_UpdateProgressBar(0)
    end
end

function CurrencyIconNumHD:_HandleInputTypeChanged(inputType)
    if not IsHD() then
        return 
    end

    if inputType == EGPInputType.Gamepad and self._gamePadActionNames and self._gamePadActionNames[self._currencyId] then
        self._wtCurrencyKeyIcon:SelfHitTestInvisible()
        self._wtCurrencyKeyIcon:InitAllKeyIcons()
    else
        self._wtCurrencyKeyIcon:Collapsed()
    end
end
--- END MODIFICATION

return CurrencyIconNumHD
