---------- LOG FUNCTION AUTO GENERATE -------------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
-------- LOG FUNCTION AUTO GENERATE END -----------

--#region引用
--- 对应蓝图:WBP_MorgenGame
--- @class MorgenMain : LuaUIBaseView @这是注释
local MorgenMain = ui("MorgenMain")
local ActivityConfig = Module.Activity.Config
local ActivityLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"
local MorgenBtn = require "DFM.Business.Module.ActivityModule.UI.MorgenGame.MorgenBtn"

local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import "EGPUINavWidgetFocusedAction"
--#endregion


function MorgenMain:Ctor()

    self._wtRewardBtn = self:Wnd("WBP_CommonIconButton", DFCommonButtonOnly)
    self._wtRewardBtn:Event("OnClicked", self.ShowRewardPop, self)
    self._wtBestRound = self:Wnd("DFTextBlock_72", UITextBlock)
    self._wtBestLocation = self:Wnd("DFTextBlock", UITextBlock)

    --- 右侧按钮
    self._wtPopBtn1 = self:Wnd("WBP_MorgenGame_Btn", MorgenBtn)
    self._wtPopBtn2 = self:Wnd("WBP_MorgenGame_Btn_1", MorgenBtn)
    self._wtPopBtn3 = self:Wnd("WBP_MorgenGame_Btn_2", MorgenBtn)
    self._wtPopBtn4 = self:Wnd("WBP_MorgenGame_Btn_3", MorgenBtn)
    self._wtPopBtn4:Collapsed()

    self.popBtnTabel = {
        [1] = self._wtPopBtn1,
        [2] = self._wtPopBtn2,
        [3] = self._wtPopBtn3,
    }
    setmetatable(self.popBtnTabel, weakmeta)

    --- 点击回调
    self.PopBtnCallbacks = {
        [1] = CreateCallBack(self.ShowExploreLogPop, self),
        [2] = CreateCallBack(self.ShowEventLogPop, self),
        [3] = CreateCallBack(self.ShowSanityLogPop, self),
    }

    --- 理智值部分
    self._wtSanityNum = self:Wnd("DFRichTextBlock_82", UITextBlock)

    self._wtSanityRuleBox = self:Wnd("wtCommonCheckInstruction", UIWidgetBase)
    self._wtSanityRuleBtn = self._wtSanityRuleBox:Wnd("DFCheckBox_Icon", UICheckBox)
    self._wtDFTipsAnchor = UIUtil.WndTipsAnchor(self, "DFTipsAnchor_95", self._OnHovered, self._OnUnhovered)

    --- 主要按钮
    self._wtExploreBtn = self:Wnd("wtCommonButtonV1S2", DFCommonButtonOnly)
    self._wtStartGameBtn = self:Wnd("WBP_DFCommonButtonV1S3", DFCommonButtonOnly)
    self._wtExploreBtn:Event("OnClicked", self.OnExploreBtnClicked, self)
    self._wtStartGameBtn:Event("OnClicked", self.OnStartGameClicked, self)

    -------------------------------------------- 以上数据/以下控件 ------------------------------------------------

    -- 禁用滚动控件集合
    self._wtHotzone = {
        self._wtExploreBtn,
        self._wtStartGameBtn,
    }
    setmetatable(self._wtHotzone, weakmeta)
end

-----------------------------------------------------生命周期-----------------------------------------------------
--#region

function MorgenMain:OnInitExtraData(activityID)
    self._activityID = activityID
end

function MorgenMain:OnOpen()
    self:SetAllText()
    self:SetPopBtnData()
end

function MorgenMain:OnShowBegin()
    self:InitData()
    self:InitEvent()
	self:InitGamepad()
    -- Module.CommonBar:BindPersistentBackHandler(self.NavBack)
end

function MorgenMain:OnShow()
	self:ResetToMain()
	self:ShowSanityGainTip()
end

function MorgenMain:OnHideBegin()
	self:DisableGamepad()
	self:RemoveAllLuaEvent()
    -- Module.CommonBar:BindPersistentBackHandler()
end

function MorgenMain:OnHide()
end

function MorgenMain:OnClose()
    self._wtHotzone = {}
    self.popBtnTabel = {}
end
--#endregion

-----------------------------------------------------数据处理-----------------------------------------------------
--#region

function MorgenMain:InitData()
    local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(self._activityID)
    if activityInfo == nil or next(activityInfo) == nil then return end

    -- 基础数据
    self.allInfo = activityInfo.arknights_game_info or {}
    self.curCycle = 1
    if self.allInfo.best_match_record.result and self.allInfo.best_match_record.result == 1 then
        self.curCycle = self.allInfo.best_match_record.cycle + 1
    elseif self.allInfo.best_match_record.time ~= 0 then
        self.curCycle = self.allInfo.best_match_record.cycle
    end

    self:RefreshUI()
end

--- 分数转化理智提示
function MorgenMain:ShowSanityGainTip()
    -- local info = {time = 1748422094, score = 1000, san_num = 0, game_mode = 1}
    -- table.insert(self.allInfo.san_records, info)

    --- 去空
    for index, info in ipairs(self.allInfo.san_records) do
        if info.san_num == 0 then
            table.remove(self.allInfo.san_records, index)
        end
    end
    if self.allInfo.san_records and next(self.allInfo.san_records) then

        table.sort(self.allInfo.san_records, function(a, b) return a.time > b.time end)

        local lastUpdateTime = self.allInfo.last_san_update_time or 0
        local lastMoneyTime = self.allInfo.san_records[1].time or 0
        local totalSans = 0

        if lastUpdateTime < lastMoneyTime then
            for index, sanInfo in ipairs(self.allInfo.san_records) do
                if lastUpdateTime < sanInfo.time then
                    totalSans = totalSans + sanInfo.san_num
                else
                    break
                end
            end
        end
    
        if totalSans > 0 then
            local StoneImg = "ArknightsCurrenc2"
            local MoneyText = StringUtil.Key2StrFormat(Module.Activity.Config.Loc.CurrencyStr, {img = StoneImg, num = totalSans})
            Module.CommonTips:ShowSimpleTip(ActivityConfig.Loc.ArkGainStone..MoneyText)
            Server.ActivityServer:SendActivityTriggerReq(self.activityID)
        end
    end
end

function MorgenMain:SetPopBtnData()
    for i, Btn in ipairs(self.popBtnTabel) do
        Btn:RefreshInfo(self.PopBtnCallbacks[i], ActivityConfig.Loc.MogenPopBtnText[i], ActivityConfig.MogenPopBtnIcon[i])
    end
end
--#endregion

----------------------------------------------------- UI表现 -----------------------------------------------------
--#region

function MorgenMain:ResetToMain()
end

function MorgenMain:RefreshUI()
    self:SetStyle(self.curCycle - 1) -- 0头像，1军牌，2记录

	-- self._wtBestRound:SetText(string.format(ActivityConfig.Loc.MorgenSanity, self.allInfo.san_num))
	-- self._wtBestLocation:SetText(string.format(ActivityConfig.Loc.MorgenSanity, self.allInfo.san_num))

	self._wtSanityNum:SetText(string.format(ActivityConfig.Loc.MorgenSanity, self.allInfo.san_num))
    --- 理智大于零才可用
    self._wtStartGameBtn:SetBtnEnable(self.allInfo.san_num > 0)
    --- 进行一次游戏后才有快速探索
    if self.allInfo.best_match_record.time ~= 0 then
        self._wtExploreBtn:SetBtnEnable(self.allInfo.san_num > 0)
    else
        self._wtExploreBtn:SetIsLocked(false)
    end
end

function MorgenMain:RefreshReddot()
end
--#endregion

-----------------------------------------------------响应操作-----------------------------------------------------
--#region

function MorgenMain:InitEvent()
    self:AddLuaEvent(Server.ActivityServer.Events.evtActivityInfoUpdate, self.InitData, self)
    self:AddLuaEvent(Server.ActivityServer.Events.evtActivityTaskChange, self.InitData, self)
    self:AddLuaEvent(Server.ActivityServer.Events.evtActivityTriggerRes, self.UpdateSanityTime, self)
end

--- 打开界面时的理智获取提示
function MorgenMain:UpdateSanityTime(activityID, activity_info)
    if self._activityID == activityID then
        self.allInfo.last_san_update_time = activity_info.last_san_update_time
    end
end

--- 开始游戏
function MorgenMain:OnStartGameClicked()
    Module.Activity:OpenMagicTowerLevelLoading(self._activityID)
end

--- 快速探索
function MorgenMain:OnExploreBtnClicked()
    Facade.UIManager:AsyncShowUI(UIName2ID.MorgenExploreQuickPop, function() end, self, self._activityID, self.allInfo.san_num)
end
    
function MorgenMain:ShowSanityRuleTip(bShow)
    if bShow then
        self._tipHandle = Module.CommonTips:ShowCommonMessageWithAnchor(ActivityConfig.Loc.MogenGameBtnText[3], self._wtDFTipsAnchor)
    else
        if self._tipHandle then
            Module.CommonTips:RemoveCommonMessageWithAnchor(self._tipHandle, self._wtDFTipsAnchor)
            self._tipHandle = nil
        end
    end
end

function MorgenMain:_OnHovered()
    self:ShowSanityRuleTip(true)
end

function MorgenMain:_OnUnhovered()
    self:ShowSanityRuleTip(false)
end

--- 四个pop界面
function MorgenMain:ShowRewardPop()
    if not next(self.allInfo.rewards) and not VersionUtil.IsShipping() then
        Module.CommonTips:ShowSimpleTip("rewards not valid")
        return
    end
    Facade.UIManager:AsyncShowUI(UIName2ID.MorgenRewardPop, function() end, self, self._activityID, self.allInfo.rewards, self.curCycle)
end

function MorgenMain:ShowExploreLogPop()
    Facade.UIManager:AsyncShowUI(UIName2ID.MorgenExploreLogPop, function() end, self, self.allInfo.match_records)
end

function MorgenMain:ShowEventLogPop()
    Facade.UIManager:AsyncShowUI(UIName2ID.MorgenEventLogPop, function() end, self, self.curCycle)
end

function MorgenMain:ShowSanityLogPop()
    Facade.UIManager:AsyncShowUI(UIName2ID.MorgenSanityPop, function() end, self, self.allInfo.san_records)
end
--#endregion

-----------------------------------------------------其他函数-----------------------------------------------------
--#region

function MorgenMain:SetAllText()
    self._wtExploreBtn:BP_SetMainTitle(ActivityConfig.Loc.MogenGameBtnText[1])
    self._wtStartGameBtn:BP_SetMainTitle(ActivityConfig.Loc.MogenGameBtnText[2])
end

function MorgenMain:NavBack()
end

function MorgenMain:InitGamepad()
    if not IsHD() then return end
    self:DisableGamepad()

    if not self._bookScrollNavGroup then
        self._bookScrollNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtBookScrollBox, self, "Hittest")
        if self._bookScrollNavGroup then
            self._bookScrollNavGroup:AddNavWidgetToArray(self._wtBookScrollBox)
            self._bookScrollNavGroup:SetScrollRecipient(self._wtBookScrollBox)
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._bookScrollNavGroup)
        end
    end

    if not self._startGameHandle then
        self._startGameHandle = self:AddInputActionBinding("Common_ButtonLeft_Gamepad", EInputEvent.IE_Pressed, self.OnStartGameClicked, self, EDisplayInputActionPriority.UI_Stack)
    	self._wtStartGameBtn:SetDisplayInputAction("Common_ButtonLeft_Gamepad", true, nil, true)
    end

    --- 长按
    if not self._exploreHandle then
        self._exploreHandle = self:AddInputActionBinding("Common_ButtonLeft_Gamepad", EInputEvent.IE_Pressed, self.OnExploreBtnClicked, self, EDisplayInputActionPriority.UI_Stack)
    	self._wtExploreBtn:SetDisplayInputActionWithLongPress(self._exploreHandle, self, "Common_ButtonLeft_Gamepad", true, nil, true)
    end
end

function MorgenMain:DisableGamepad()
    if not IsHD() then return end

    if self._bookScrollNavGroup then
        WidgetUtil.RemoveNavigationGroup(self)
        self._bookScrollNavGroup = nil
    end

    if self._startGameHandle then
        self:RemoveInputActionBinding(self._startGameHandle)
        self._startGameHandle = nil
    end

    if self._exploreHandle then
        self:RemoveInputActionBinding(self._exploreHandle)
        self._exploreHandle = nil
    end

end

--#endregion
-----------------------------------------------------  END  -----------------------------------------------------

return MorgenMain