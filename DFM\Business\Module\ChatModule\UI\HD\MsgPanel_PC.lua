----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMChat)
----- LOG FUNCTION AUTO GENERATE END -----------



local MsgPanelHDBase = require "DFM.Business.Module.ChatModule.UI.HD.MsgPanelHDBase"
local MsgPanel_PC = hud("MsgPanel_PC", MsgPanelHDBase)

local EGameHUDState = import "GameHUDSate"
local UDFMGameplayDelegates = import "DFMGameplayDelegates"
local EBreakthroughStage = import("EBreakthroughStage")
local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"
local EDFMGamePlayMode = import "EDFMGamePlayMode"

--- BEGIN MODIFICATION @ VIRTUOS
local UGameFlowDelegates = import "GameFlowDelegates"
-- 输入设备相关
local UGPInputDelegates = import "GPInputDelegates"
local EGPInputType = import "EGPInputType"
local UGPInputHelper = import "GPInputHelper"
--- END MODIFICATION

local function GetInGameController()
    return InGameController:Get()
end


function MsgPanel_PC:Ctor()
    -- self:Super().Ctor(self)

    self.uiName = "MsgPanel_PC"
    self._maxChatLen = 81

    -- self.bInMPKillMarkerViewPosition = false
    -- self.killerMarkOffsetY = 230

    --- BEGIN MODIFICATION @ VIRTUOS
    -- 是否在局内
    self._bIsInGame = false 
    self._switchChatWindowHandle = -1
    --- END MODIFICATION

    self._wtSlotForContent = self:Wnd("DFNamedSlot_MakeAppointment", UIWidgetBase)
end

function MsgPanel_PC:OnOpen()
    MsgPanelHDBase.OnOpen(self)

    --- BEGIN MODIFICATION @ VIRTUOS
    -- 手动触发一次GameFlow刷新，防止进入到局内了还没有加载出来（SOL模式会存在这个问题）
    local _curGameFlow = UGameFlowDelegates.GetGameFlowDelegates(GetGameInstance()):GetCurGameFlowStage()
    self:_GameFlowChangeEnter(_curGameFlow)
    --- END MODIFICATION

    -- self:AddStateToInVisibleGameHudState(EGameHUDState.GHS_Redeploy)
    self:AddStateToInVisibleGameHudState(EGameHUDState.GHS_ObserverMode)
    self:AddStateToInVisibleGameHudState(EGameHUDState.GHS_CutScene)

    self:RegisterCppActorDelegate()
end

function MsgPanel_PC:_BindEvent()
    MsgPanelHDBase._BindEvent(self)
    --- BEGIN MODIFICATION @ VIRTUOS: 输入绑定处理
    -- 若在局内，先不进行输入绑定，而是根据输入类型来决定是否启用输入
    if not self._bIsInGame then
        self:RemoveInputActionBinding(self._switchChatWindowHandle)
        self._switchChatWindowHandle = self:AddInputActionBinding("OpenChat", EInputEvent.IE_Pressed, self._OnSwitchChatWindow, self, EDisplayInputActionPriority.UI_Chat)
    end
    --- END MODIFICATION
    
    LuaGlobalEvents.evtGameFlowChangeEnter:AddListener(self._GameFlowChangeEnter, self)

    --- BEGIN MODIFICATION @ VIRTUOS
    LuaGlobalEvents.evtGameFlowChangeLeave:AddListener(self._GameFlowChangeLeave, self)
    -- 绑定多输入设备切换事件
    self._OnNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._OnInputTypeChanged, self))
    --- END MODIFICATION
    
end

function MsgPanel_PC:_UnbindEvent()
    MsgPanelHDBase._UnbindEvent(self)
    
    self:RemoveInputActionBinding(self._switchChatWindowHandle)

    LuaGlobalEvents.evtGameFlowChangeEnter:RemoveListener(self._GameFlowChangeEnter, self)

    --- BEGIN MODIFICATION @ VIRTUOS
    LuaGlobalEvents.evtGameFlowChangeLeave:RemoveListener(self._GameFlowChangeLeave, self)

    if self._OnNotifyInputTypeChangedHandle then
        UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._OnNotifyInputTypeChangedHandle)
        self._OnNotifyInputTypeChangedHandle = nil
    end
    --- END MODIFICATION
end

function MsgPanel_PC:OnGameHUDStateChanged()
    MsgPanelHDBase.OnGameHUDStateChanged(self)

    -- local hudStateManager = UE.HUDStateManager.Get(GetWorld())
    -- if isvalid(hudStateManager) then
    --     local stateArray = hudStateManager.Get(GetWorld()):GetStateArray()
    --     if not self.bInMPKillMarkerViewPosition and table.contains(stateArray, EGameHUDState.GHS_KillerMark) then
    --         self.bInMPKillMarkerViewPosition = true
    --         local offset = FVector2D(0, 0 - self.killerMarkOffsetY)
    --         self:SetOffset(offset)
    --     elseif self.bInMPKillMarkerViewPosition and not table.contains(stateArray, EGameHUDState.GHS_KillerMark) then
    --         self.bInMPKillMarkerViewPosition = false
    --         local offset = FVector2D(0, self.killerMarkOffsetY)
    --         self:SetOffset(offset)
    --     end
    -- end
end

--- BEGIN MODIFICATION @ VIRTUOS: 进入游戏后，也触发一次InputBinding判断
function MsgPanel_PC:_GameFlowChangeEnter(gameFlowType)
    if gameFlowType == EGameFlowStageType.Game or gameFlowType == EGameFlowStageType.GameSettlement then
        self._bIsInGame = true

        local curInpurtType = UGPInputHelper.GetCurrentInputType(GetGameInstance())
        self:_OnInputTypeChanged(curInpurtType)
    end
end

-- 离开游戏，Chat重新绑定
function MsgPanel_PC:_GameFlowChangeLeave(gameFlowType)
    if gameFlowType == EGameFlowStageType.Game or gameFlowType == EGameFlowStageType.GameSettlement then
        self._bIsInGame = false
        self:RemoveInputActionBinding(self._switchChatWindowHandle)
        self._switchChatWindowHandle = self:AddInputActionBinding("OpenChat", EInputEvent.IE_Pressed, self._OnSwitchChatWindow, self, EDisplayInputActionPriority.UI_Chat)
    end
end
------ END MODIFICATION

function MsgPanel_PC:SetOffset(offset)
    self._wtMsgOperationPanel:SetRenderTranslation(offset)
    self._wtMsgHistoryPanel:SetRenderTranslation(offset)
end

function MsgPanel_PC:_OnTextChanged(inText)
    inText = tostring(inText)
    -- 字符长度限制
    local _,fixText = StringUtil.GetRealWidth(inText, self._maxChatLen)
    if fixText ~= inText then
        Module.CommonTips:ShowSimpleTip(Module.Chat.Config.Loc.ExceedChatMaxLen)
        self._wtMsgInputTB:SetText(fixText)
    end
    self.oldText = fixText
end

--- BEGIN MODIFICATION @ VIRTUOS
function MsgPanel_PC:_OnInputTypeChanged(InputType)
    -- 局内 + 手柄操作时，不能打开聊天窗口
    if self._bIsInGame then
        if InputType == EGPInputType.Gamepad then
            self:RemoveInputActionBinding(self._switchChatWindowHandle)
        else
            self:RemoveInputActionBinding(self._switchChatWindowHandle)
            self._switchChatWindowHandle = self:AddInputActionBinding("OpenChat", EInputEvent.IE_Pressed, self._OnSwitchChatWindow, self, EDisplayInputActionPriority.UI_Chat)
        end
    end
end
--- END MODIFICATION

function MsgPanel_PC:OnShow()
    MsgPanelHDBase.OnShow(self)
    self:AddLuaEvent(Module.Chat.Config.evtInGameTeamInviteUpdate, self.SomethingInviteStateChange, self)
end

function MsgPanel_PC:OnHide()
    MsgPanelHDBase.OnHide(self)
    self:RemoveLuaEvent(Module.Chat.Config.evtInGameTeamInviteUpdate, self.SomethingInviteStateChange)
    self:_RemoveChatMakeAppointContent()
end

function MsgPanel_PC:OnClose()
    MsgPanelHDBase.OnClose(self)

    self:UnRegisterCppActorDelegate()
end

function MsgPanel_PC:RegisterCppActorDelegate()
    local gameState = GetInGameController():GetGameState()
    if gameState == nil then
        return
    end

    if GetInGameController():GetGamePlayerMode() ~= EDFMGamePlayMode.GamePlayMode_Breakthrough then
        return
    end

    --注册回调：PreMatch阶段变化
    gameState.onCurStage:Add(self.OnCurStageChanged,self)
end

function MsgPanel_PC:UnRegisterCppActorDelegate()
    local gameState = GetInGameController():GetGameState()
    if gameState == nil then
        return
    end

    if GetInGameController():GetGamePlayerMode() ~= EDFMGamePlayMode.GamePlayMode_Breakthrough then
        return
    end

    --注册回调：PreMatch阶段变化
    gameState.onCurStage:Remove(self.OnCurStageChanged,self)
end

---阶段变化
---@param stage any
function MsgPanel_PC:OnCurStageChanged(stage)
    if stage == EBreakthroughStage.PreElection then  -- 抢候选资格
        self:SetType(3)  -- 蓝图函数
    elseif stage == EBreakthroughStage.PreMatch then -- 首次部署
        self:SetType(0)  -- 蓝图函数
    end
end

function MsgPanel_PC:HideMsgOperationPanel()
    MsgPanelHDBase.HideMsgOperationPanel(self)
    self:_RemoveChatMakeAppointContent()
end

function MsgPanel_PC:ShowMsgOperationPanel()
    MsgPanelHDBase.ShowMsgOperationPanel(self)
    self:UpdateChatMakeAppointState()
end

function MsgPanel_PC:SomethingInviteStateChange(reason, startIndexVal)
    if Module.Team.Field.GameTeamInviteViewModel:GetNoReceiveSignal() then
        self._wtSlotForContent:Collapsed()
        return
    end

    Module.Chat.Config.evtInGamePreBookCardUpdate:Invoke(reason)
    if Module.Team.Field.GameTeamInviteViewModel:GetPendingSum() <= 0 then
        self._wtSlotForContent:Collapsed()
    else
        if not self.chatMakeAppoint and (self.bInOperation) then
            self:UpdateChatMakeAppointState()
        end

        if (self.bInOperation) then
            self._wtSlotForContent:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        end
    end
end

function MsgPanel_PC:UpdateChatMakeAppointState()
    self:_RemoveChatMakeAppointContent()
    if Module.Team.Field.GameTeamInviteViewModel:GetPendingSum() > 0 then
        local dataValue, index = Module.Team.Field.GameTeamInviteViewModel:GetTheOldestPendingReq()
        if dataValue then
            self.chatMakeAppoint = Facade.UIManager:AddSubUI(self, UIName2ID.ChatMakeAppoint, self._wtSlotForContent)
            self._wtSlotForContent:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        end
    end
end

function MsgPanel_PC:_RemoveChatMakeAppointContent()
    self.chatMakeAppoint = nil
    Facade.UIManager:RemoveSubUIByParent(self, self._wtSlotForContent)
end

function MsgPanel_PC:_ClearChatMakeAppointContent()
    Facade.UIManager:ClearSubUIByParent(self, self._wtSlotForContent)
end

function MsgPanel_PC:_OnMouseButtonDown(mouseEvent)
    if self.bInOperation then
        local sceenPos = mouseEvent:GetScreenSpacePosition()
        local geometry = self._wtMsgOperationPanel:GetCachedGeometry()
        local isUnder = USlateBlueprintLibrary.IsUnderLocation(geometry, sceenPos)

        local geometry2 = self._wtSlotForContent:GetCachedGeometry()
        local isUnder2 = USlateBlueprintLibrary.IsUnderLocation(geometry2, sceenPos)

        -- 点击聊天框外 关闭聊天框
        if (not isUnder) and (not isUnder2) then
            loginfo("MsgPanelHDBase:_OnMouseButtonDown, Close input panel")
            self:HideMsgOperationPanel()
        end
    end
end

return MsgPanel_PC
