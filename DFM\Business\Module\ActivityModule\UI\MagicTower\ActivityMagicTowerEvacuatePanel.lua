----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class ActivityMagicTowerEvacuatePanel : LuaUIBaseView
local ActivityMagicTowerEvacuatePanel = ui("ActivityMagicTowerEvacuatePanel")
local ActivityConfig = Module.Activity.Config
local RuntimeIconTool = require "DFM.StandaloneLua.BusinessTool.RuntimeIconTool"
local ConfigManager = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerConfigManager"
local Logic = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerLogic"

local function testLog(...)
    loginfo("[Magic_Tower_Cuate_Panel] ", ...)
end

local function testWarningLog(...)
    logwarning("[Magic_Tower_Cuate_Panel] ", ...)
end

local function testErrorLog(...)
    logerror("[Magic_Tower_Cuate_Panel] ", ...)
end

function ActivityMagicTowerEvacuatePanel:Ctor()
    self._activityID = 0
    self._levelInfo = {}
    self._isEvacuate = false
    self._killer = nil

    -- 标题
    self._wtTitleTxt = self:Wnd("DFTextBlock_90", UITextBlock)
    -- 关卡全称
    self._wtLevelNameTxt = self:Wnd("DFTextBlock_190", UITextBlock)
    -- 台词
    self._wtLineTxt = self:Wnd("DFTextBlock_249", UITextBlock)
    -- 奖励数量
    self._wtItemNum = self:Wnd("DFTextBlock_76", UITextBlock)

    -- 失败隐藏界面
    self._wtRewardPanel = self:Wnd("PlatformPaddingBox_0", UIWidgetBase)

    -- 端手通用的全屏响应
    self._wtContinueBtn = self:Wnd("DFButton_70", UIButton)
    self._wtContinueBtn:Event("OnClicked", self.OnContinueClicked, self)
end

-----------------------------------------------------生命周期-----------------------------------------------------
function ActivityMagicTowerEvacuatePanel:OnInitExtraData(activityID, levelInfo, isEvacuate, killer, FinishCallBack, money, evacuateCondition, extraInfo)
    self._activityID = activityID or 0
    self._levelInfo = levelInfo or {}
    self._isEvacuate = isEvacuate or false
    self._killer = killer
    self._money = money or 0
    self._evacuateCondition = evacuateCondition or 0
    self.FinishCallBack = FinishCallBack

    -- 给关卡加载界面用的打包参数
    self._extraInfo = extraInfo or {}

    self:_InitData()
end

function ActivityMagicTowerEvacuatePanel:_InitEvent()
    self:AddLuaEvent(Server.ActivityServer.Events.evtActivityMagicTowerLmbChange, self.SetLmbChangeNum, self)
end

function ActivityMagicTowerEvacuatePanel:SetLmbChangeNum(lmbNum)
    if not lmbNum then
        testErrorLog("未接收到来自后台的数据更新，将使用局内获得龙门币作为显示！")
        return
    end

    testLog(string.format("接收到来自后台的数据更新，龙门币：%s", lmbNum))

    self._isUpdateLmb = true
    self._wtItemNum:SetText(Logic.roundDownToInteger(lmbNum))
end

function ActivityMagicTowerEvacuatePanel:OnShowBegin()
    self:_InitEvent()

    local exeFunc = function()
        self:BindBackAction()
        self:RefreshUI()
        self:PlayAnimation(self.WBP_DoctorGames_EvacuatePanel_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
        self:PlayAnimation(self.WBP_DoctorGames_EvacuatePanel_loop, 0, 0, EUMGSequencePlayMode.Forward, 1, true)
    end

    if not self._extraInfo or not self._extraInfo.exitType or not self._extraInfo.curState or not self._extraInfo.newState then
        exeFunc()
        return
    end

    local exitType = self._extraInfo.exitType
    local curState = self._extraInfo.curState
    local newState = self._extraInfo.newState

    local sceneID

    if curState < newState then
        if newState == 4 then -- 处于一周目（状态123），选了毁灭超算机（状态4）
            sceneID = 2
        elseif newState == 5 then -- 处于一周目（状态123），选了保留超算机（状态5）
            sceneID = 3
        end
    end

    if exitType == 3 then
        if curState == 4 or curState == 5 then -- 状态45通关
            sceneID = 5
        end
        if curState == 6 then -- 状态6通关
            sceneID = 7
        end
    end
    
    if sceneID then
        testWarningLog(string.format("撤离界面显示前传给关卡加载界面的sceneID：%s", tostring(sceneID)))

        local fOnCombatLoadingFinished = CreateCallBack(function()
            exeFunc()
        end, self)

        Facade.UIManager:AsyncShowUI(UIName2ID.ActivityMagicTowerLevelLoading, nil, nil, self._activityID, sceneID, true, true, fOnCombatLoadingFinished)
    else
        exeFunc()
    end
end

function ActivityMagicTowerEvacuatePanel:OnHideBegin()
    self._isUpdateLmb = nil

    self:RemoveAllActions()
    self:RemoveAllLuaEvent()
    self:StopAnimation(self.WBP_DoctorGames_EvacuatePanel_loop)
end

function ActivityMagicTowerEvacuatePanel:OnNavBack()
    if self.FinishCallBack then
        self:FinishCallBack()
    end
    
    Facade.UIManager:CloseUI(self)
end

function ActivityMagicTowerEvacuatePanel:BindBackAction()
    self:RemoveAllActions()

    self._JumpOverHandle = self:AddInputActionBinding(
        "JumpOver", 
        EInputEvent.IE_Pressed, 
        self.OnNavBack, 
        self,
        EDisplayInputActionPriority.UI_Pop
    )
end

function ActivityMagicTowerEvacuatePanel:RemoveAllActions()
    if self._JumpOverHandle then
        self:RemoveInputActionBinding(self._JumpOverHandle)
        self._JumpOverHandle = nil
    end
end

function ActivityMagicTowerEvacuatePanel:OnClose()
    self.FinishCallBack = nil
end

-----------------------------------------------------数据区-----------------------------------------------------
function ActivityMagicTowerEvacuatePanel:_InitData()
    self._deathLinesConfig = ConfigManager.GetDeathLinesConfigTable()
end

-----------------------------------------------------UI刷新-----------------------------------------------------\
function ActivityMagicTowerEvacuatePanel:RefreshUI()
    -- 设置关卡名称
    local levelName = (self._levelInfo.name or "") .. "-" .. (self._levelInfo.subName or "")
    self._wtLevelNameTxt:SetText(levelName)

    -- 筛选符合条件的台词
    local validLines = {}
    for _, lineConfig in pairs(self._deathLinesConfig or {}) do
        if self._isEvacuate then
            if lineConfig.evacuateCondition and lineConfig.evacuateCondition ~= 0 and lineConfig.evacuateCondition == self._evacuateCondition then
                table.insert(validLines, lineConfig)
            end
        else
            if lineConfig.evacuateCondition == 0 and lineConfig.killer and self._killer and lineConfig.killer == self._killer then
                table.insert(validLines, lineConfig)
            end
        end
    end

    if not self._isEvacuate and #validLines == 0 then
        for _, lineConfig in pairs(self._deathLinesConfig or {}) do
            if lineConfig.evacuateCondition == 0 and lineConfig.killer == 0 then
                table.insert(validLines, lineConfig)
            end
        end
    end

    -- 随机选择一条台词
    local selectedLine = nil
    if #validLines > 0 then
        selectedLine = validLines[math.random(1, #validLines)]
    end

    -- 设置标题和台词
    local titleTxt = ""
    local lineTxt = ""
    if selectedLine then
        titleTxt = selectedLine.lineTitle or ""
        lineTxt = selectedLine.lineDesc or ""
    end
    self._wtTitleTxt:SetText(titleTxt)
    self._wtLineTxt:SetText(lineTxt)

    -- 设置奖励数量
    local colorType = 1
    if self._isEvacuate then
        colorType = 0
        self._wtRewardPanel:SelfHitTestInvisible()

        if not self._isUpdateLmb then
            local lmbNum = Logic.roundDownToInteger(self._money)
            self._wtItemNum:SetText(lmbNum)
            testLog(string.format("更新龙门币数量：%s", lmbNum))
        else
            testWarningLog("后台已更新龙门币数量，本次不更新！")
        end
    else
        self._wtRewardPanel:Collapsed()
    end
    if self.SetColor then
        self:SetColor(colorType)
    end
end

function ActivityMagicTowerEvacuatePanel:OnContinueClicked()
    self:OnNavBack()
end

return ActivityMagicTowerEvacuatePanel