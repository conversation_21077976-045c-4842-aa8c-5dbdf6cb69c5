----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMVersionUpdate)
----- LOG FUNCTION AUTO GENERATE END -----------



--------------------------------------------------------------------------
--- UI路径映射配置示例（UILayer表示需要加入的层级）
--------------------------------------------------------------------------
-- UITable[UIName2ID.VersionUpdateMainPanel] = {
--     UILayer = EUILayer.Stack,
--     LuaPath = "DFM.Business.Module.VersionUpdateModule.UI.VersionUpdateMainPanel",
--     BPKey = "WBP_VersionUpdateMainPanel_VersionUpdate"
    -- SubUIs = {
    --     UIName2ID.VersionUpdateSubView,
    -- },
    -- Anim = {
    --     FlowInAni = "In_Anim",
    -- }
-- }

-- UITable[UIName2ID.VersionUpdateSubView] = {
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.VersionUpdateModule.UI.VersionUpdateSubView",
--     BPKey = "VersionUpdateSubView",
-- }

local VersionUpdateConfig = {
    --通知更新流程结束
    flowEvtVersionUpdateFinished = LuaGameFlowEvent:NewIns("flowEvtVersionUpdateFinished", "VersionUpdate"),

    EVersionUpdateState = {
		None = 0,
		BeginUpdate = 1,
		VersionCheck = 2,
		ConfirmingToDownload = 3,
		Downloading = 4,
		Installing = 5,
		Failed = 6,
		Succeeded = 7,
		WaitingToRetry = 8,
		FirstExtractResource = 9,
		ConfirmingToAnnounce = 10,
        Finishing = 11,
		Finished = 12
	},

    --错误类型
	EErrorType = {
		Init = 0,
		Network = 1,                            --网络错误，提示玩家在网络正常状态重启游戏更新
		Timeout = 2,                            --网络超时，游戏侧先重试，提示玩家在网络正常状态重启游戏更新
		NoSpace = 3,                            --磁盘空间不足，提示玩家提供充足的sd卡空间，然后再启动游戏
		System = 4,                             --其他系统错误，没权限，或磁盘问题，提示玩家重启手机
		OtherError = 5,                         --模块其他错误，提示玩家重启手机
		NotSupportUpdate = 6,                   --当前版本不支持更新，提示玩家市场上下载最新版本
		CurNetNotSupportUpdate = 8,		        --当前的环境下载的apk异常（运营商缓存），提示玩家到市场下载最新版本
	},

    --Dolphin更新stage
    EVersionUpdateDolphinStage = {
        ApkUpdateDownDiffFile = 72, --下载apk diff文件
        ApkUpdateDownFullApk = 73,  --下载apk 整包
        SourceDownload = 94,        --下载资源文件
        SourceExtract = 95,         --解压资源文件
    },

    Loc = {
        --资源非强更
        UpdateNewResVersionOptionalUpdate = NSLOCTEXT("VersionUpdateModule", "UpdateNewResVersionOptionalUpdate", "发现新版本<customstyle color=\"Color_Highlight02\">%s(下载大小：%s)</>，现在开始下载？"),
        --资源强更
        UpdateNewResVersionForceUpdate = NSLOCTEXT("VersionUpdateModule", "UpdateNewResVersionForceUpdate", "发现新版本<customstyle color=\"Color_Highlight02\">%s(下载大小：%s)</>，不更新将无法进行游戏。现在开始下载？"),
        --App非强更
        UpdateNewAppVersionOptionalUpdate = NSLOCTEXT("VersionUpdateModule", "UpdateNewAppVersionOptionalUpdate", "发现新版本<customstyle color=\"Color_Highlight02\">%s(下载大小：%s)</>，完成更新可领取<customstyle color=\"Color_Highlight02\">额外奖励</>！"),
        --AppStore非强更
        UpdateNewAppVersionOptionalAppStoreUpdate = NSLOCTEXT("VersionUpdateModule", "UpdateNewAppVersionOptionalAppStoreUpdate", "发现新版本<customstyle color=\"Color_Highlight02\">%s</>，完成更新可领取<customstyle color=\"Color_Highlight02\">额外奖励</>！"),
        --App强更
        UpdateNewAppVersionForceUpdate = NSLOCTEXT("VersionUpdateModule", "UpdateNewAppVersionForceUpdate", "发现新版本<customstyle color=\"Color_Highlight02\">%s(下载大小：%s)</>，不更新将无法进行游戏。现在开始下载？"),
        --AppStore强更
        UpdateNewAppVersionForceAppStoreUpdate = NSLOCTEXT("VersionUpdateModule", "UpdateNewAppVersionForceAppStoreUpdate", "发现新版本<customstyle color=\"Color_Highlight02\">%s</>，不更新将无法进行游戏。现在开始下载？"),

        UpdateDownloadWifi = NSLOCTEXT("VersionUpdateModule", "UpdateDownloadWifi", "(推荐使用Wi-Fi更新，如遇问题请前往应用商店下载)"),

        UpdateConfirmWindowTittle = NSLOCTEXT("VersionUpdateModule", "UpdateConfirmWindowTittle", "版本更新"),
        UpdateConfirmWindowDesc = NSLOCTEXT("VersionUpdateModule", "UpdateConfirmWindowDesc", "版本信息:%s\n是否更新?"),
        UpdateConfirmWindowConfirm = NSLOCTEXT("VersionUpdateModule", "UpdateConfirmWindowConfirm", "确认"),
        UpdateConfirmWindowCancel = NSLOCTEXT("VersionUpdateModule", "UpdateConfirmWindowCancel", "取消"),

        UpdateFailWindowTittle = NSLOCTEXT("VersionUpdateModule", "UpdateFailWindowTittle", "版本更新失败"),
        UpdateFailWindowConfirm = NSLOCTEXT("VersionUpdateModule", "UpdateFailWindowConfirm", "确认"),
        UpdateFailWindowCancel = NSLOCTEXT("VersionUpdateModule", "UpdateFailWindowCancel", "取消"),
        
        UpdateFailWindowFailFinishDesc = NSLOCTEXT("VersionUpdateModule", "UpdateFailWindowFailFinishDesc", "更新失败，请退出游戏重试"),
        UpdateFailWindowFailFinishDebugDesc = NSLOCTEXT("VersionUpdateModule", "UpdateFailWindowFailFinishDebugDesc", "[当前是测试版本，可以进入游戏，但是无法使用资源更新功能]"),

        DebugUpdateConfirmWindowTittle = NSLOCTEXT("VersionUpdateModule", "DebugUpdateConfirmWindowTittle", "版本更新"),
        DebugUpdateConfirmWindowDesc = NSLOCTEXT("VersionUpdateModule", "DebugUpdateConfirmWindowDesc", "是否跳过版本更新流程?"),
        DebugUpdateConfirmWindowConfirm = NSLOCTEXT("VersionUpdateModule", "DebugUpdateConfirmWindowConfirm", "确认"),
        DebugUpdateConfirmWindowCancel = NSLOCTEXT("VersionUpdateModule", "DebugUpdateConfirmWindowCancel", "取消"),

        UpdateProgressInfo = NSLOCTEXT("VersionUpdateModule", "UpdateProgressInfo", "已下载:%s  剩余时间:%s  下载速度:%s/秒"),
        UpdateProgressDesc = NSLOCTEXT("VersionUpdateModule", "UpdateProgressDesc", "(当前步骤:%s 当前状态:%s 当前阶段:%s)"),
        UpdateSizeMB = NSLOCTEXT("VersionUpdateModule", "UpdateSizeMB", "兆字节"),
        UpdateSizeKB = NSLOCTEXT("VersionUpdateModule", "UpdateSizeKB", "千字节"),

        UpdateProgressStepDesc = {
            [1] = NSLOCTEXT("VersionUpdateModule", "UpdateProgressStepDesc_App", "检测程序更新..."),
            [2] = NSLOCTEXT("VersionUpdateModule", "UpdateProgressStepDesc_Extract", "检测资源解压..."),
            [3] = NSLOCTEXT("VersionUpdateModule", "UpdateProgressStepDesc_Resource", "检测资源更新..."),
            [4] = NSLOCTEXT("VersionUpdateModule", "UpdateProgressStepDesc_CheckResource", "检测资源完整性..."),
        },
        UpdateProgressStateDesc = {
            [2] = NSLOCTEXT("VersionUpdateModule", "UpdateProgressStateDesc_Version", "正在获取版本信息..."),
            [4] = NSLOCTEXT("VersionUpdateModule", "UpdateProgressStateDesc_Downloading", "下载中..."),
            [5] = NSLOCTEXT("VersionUpdateModule", "UpdateProgressStateDesc_Installing", "正在安装程序"),
            [6] = NSLOCTEXT("VersionUpdateModule", "UpdateProgressStateDesc_Failed", "更新失败"),
            [7] = NSLOCTEXT("VersionUpdateModule", "UpdateProgressStateDesc_Succeeded", "更新成功"),
            [8] = NSLOCTEXT("VersionUpdateModule", "UpdateProgressStateDesc_WaitingToRetry", "等待重试..."),
        },
        UpdateProgressStageDesc = {
            [10] = NSLOCTEXT("VersionUpdateModule", "UpdateProgressStageDesc_FirstExtract", "首包解压"),

            [31] = NSLOCTEXT("VersionUpdateModule", "UpdateProgressStageDesc_Filelist_Check", "检测资源完整性"),

            [69] = NSLOCTEXT("VersionUpdateModule", "UpdateProgressStageDesc_Dolphin_Version", "正在获取版本信息"),
            [70] = NSLOCTEXT("VersionUpdateModule", "UpdateProgressStageDesc_ApkUpdate", "程序下载"),
            [71] = NSLOCTEXT("VersionUpdateModule", "UpdateProgressStageDesc_ApkUpdateDownConfig", "下载程序配置文件"),
            [72] = NSLOCTEXT("VersionUpdateModule", "UpdateProgressStageDesc_ApkUpdateDownDiffFile", "下载程序差异文件"),
            [73] = NSLOCTEXT("VersionUpdateModule", "UpdateProgressStageDesc_ApkUpdateDownFullApk", "下载程序文件"),
            [74] = NSLOCTEXT("VersionUpdateModule", "UpdateProgressStageDesc_ApkUpdateCheckCompletedApk", "校验程序文件"),
            [75] = NSLOCTEXT("VersionUpdateModule", "UpdateProgressStageDesc_ApkUpdateCheckLocalApk", "校验程序文件"),
            [76] = NSLOCTEXT("VersionUpdateModule", "UpdateProgressStageDesc_ApkUpdateCheckConfig", "校验程序文件"),
            [77] = NSLOCTEXT("VersionUpdateModule", "UpdateProgressStageDesc_ApkUpdateCheckDiff", "校验程序文件"),
            [78] = NSLOCTEXT("VersionUpdateModule", "UpdateProgressStageDesc_ApkUpdateMergeDiff", "程序更新包下载已完成，资源合并中（不会耗费流量）"),
            [79] = NSLOCTEXT("VersionUpdateModule", "UpdateProgressStageDesc_ApkUpdateCheckFull", "校验程序文件"),
            [80] = NSLOCTEXT("VersionUpdateModule", "UpdateProgressStageDesc_ApkUpdateCheckPredownloadApk", "校验预下载程序文件"),

            [91] = NSLOCTEXT("VersionUpdateModule", "UpdateProgressStageDesc_SourceUpdateDownloadList", "下载资源配置文件"),
            [92] = NSLOCTEXT("VersionUpdateModule", "UpdateProgressStageDesc_SourcePrepareUpdate", "准备资源更新"),
            [93] = NSLOCTEXT("VersionUpdateModule", "UpdateProgressStageDesc_SourceAnalyseDiff", "分析资源差异"),
            [94] = NSLOCTEXT("VersionUpdateModule", "UpdateProgressStageDesc_SourceDownload", "下载资源文件"),
            [95] = NSLOCTEXT("VersionUpdateModule", "UpdateProgressStageDesc_SourceExtract", "解压资源文件"),

            [97] = NSLOCTEXT("VersionUpdateModule", "UpdateProgressStageDesc_SourceCheckPredownloadPatch", "校验预下载资源文件"),
            [98] = NSLOCTEXT("VersionUpdateModule", "UpdateProgressStageDesc_ExtractPredownloadPatch", "解压预下载资源文件"),
        },

        ErrorCodeText = {
            [10001] = NSLOCTEXT("VersionUpdateModule", "VersionUpdateErrorCode_10001", "更新模块创建失败"),
            [10002] = NSLOCTEXT("VersionUpdateModule", "VersionUpdateErrorCode_10002", "更新模块初始化失败"),
            [10003] = NSLOCTEXT("VersionUpdateModule", "VersionUpdateErrorCode_10003", "下载目录创建失败，请检查文件读写权限"),
            [10004] = NSLOCTEXT("VersionUpdateModule", "VersionUpdateErrorCode_10004", "下载目录不存在"),
            [10005] = NSLOCTEXT("VersionUpdateModule", "VersionUpdateErrorCode_10005", "下载路径不能包含中文，请重新安装"),
            [10006] = NSLOCTEXT("VersionUpdateModule", "VersionUpdateErrorCode_10006", "存储空间不足，请清理空间后重试"),

            [154140673] = NSLOCTEXT("VersionUpdateModule", "VersionUpdateErrorCode_154140673", "无法连接到更新服务器，请检查网络"),

            [154140709] = NSLOCTEXT("VersionUpdateModule", "VersionUpdateErrorCode_154140709", "版本信息不存在"), --上传的版本号不可用 ，根据配置也无法找到可用版本更新

            [289407004] = NSLOCTEXT("VersionUpdateModule", "VersionUpdateErrorCode_289407004", "资源解压失败，设备可用空间不足"),

            [353501190] = NSLOCTEXT("VersionUpdateModule", "VersionUpdateErrorCode_353501190", "网络中断，请确保有一个稳定的网络连接"),

            [422576143] = NSLOCTEXT("VersionUpdateModule", "VersionUpdateErrorCode_422576143", "资源解压失败"),

            [554696704] = NSLOCTEXT("VersionUpdateModule", "VersionUpdateErrorCode_554696704", "设备网络错误，请切换网络环境后重试"),

            [555745297] = NSLOCTEXT("VersionUpdateModule", "VersionUpdateErrorCode_555745297", "文件系统错误，未授权文件管理权限或磁盘空间不足"),

            [158334977] = NSLOCTEXT("VersionUpdateModule", "VersionUpdateErrorCode_158334977", "连接版本服务器失败"), --原本的154140709
        },

        ErrorTypeText = {
            [1] = NSLOCTEXT("VersionUpdateModule", "VersionUpdateErrorType_1", "更新失败，请确保网络连接稳定"),
            [2] = NSLOCTEXT("VersionUpdateModule", "VersionUpdateErrorType_2", "更新失败，网络超时请确保网络连接稳定"),
            [3] = NSLOCTEXT("VersionUpdateModule", "VersionUpdateErrorType_3", "更新失败，设备可用空间不足"),
            [4] = NSLOCTEXT("VersionUpdateModule", "VersionUpdateErrorType_4", "没有足够的空间进行更新请腾出更多空间再试一次"),
            [5] = NSLOCTEXT("VersionUpdateModule", "VersionUpdateErrorType_5", "更新失败，请重启游戏"),
            [6] = NSLOCTEXT("VersionUpdateModule", "VersionUpdateErrorType_6", "游戏版本过时，请到官网重新下载最新版本"),
            [8] = NSLOCTEXT("VersionUpdateModule", "VersionUpdateErrorType_8", "当前安装包异常，无法下载最新的更新，请从官网重新下载最新版本"),
        },

        ErrorDefaultText = NSLOCTEXT("VersionUpdateModule", "VersionUpdateErrorCode_ErrorDefault", "未知错误，请重启设备后重试"),
        NumFormat = NSLOCTEXT("VersionUpdateModule", "Lua_VersionUpdate_NumFormat", "{countNum}/{maxNum}"),
    }
}

return VersionUpdateConfig
