----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMTournament)
----- LOG FUNCTION AUTO GENERATE END -----------



--积分赛模块
---@class TournamentModule : ModuleBase
    local TournamentModule = class("TournamentModule", require("DFM.YxFramework.Managers.Module.ModuleBase"))
    local TournamentLogic = require "DFM.Business.Module.TournamentModule.Logic.TournamentLogic"
    local TournamentLoadLogic = require "DFM.Business.Module.TournamentModule.Logic.TournamentLoadLogic"
    local TournamentEventLogic = require "DFM.Business.Module.TournamentModule.Logic.TournamentEventLogic"

    function TournamentModule:Ctor()
        loginfo("TournamentModule:Ctor")
    end
    
    ---------------------------------------------------------------------------------
    --- Module 生命周期
    ---------------------------------------------------------------------------------
    --- 模块Init回调，用于初始化一些数据
    ---@overload fun(ModuleBase, OnInitModule)
    function TournamentModule:OnInitModule()
        loginfo("TournamentModule:OnInitModule")
        TournamentEventLogic.AddListeners()
        TournamentLogic.PrintTables()
    end
    
    --- 若为非懒加载模块，则在Init后调用;对应每个OnGameFlowChangeEnter
    --- 模块默认[常驻]加载资源（预加载UI蓝图、需要用到的图片等等
    ---@overload fun(ModuleBase, OnLoadModule)
    function TournamentModule:OnLoadModule()
        loginfo("TournamentModule:OnLoadModule")
    end

    --- 无论是否懒加载都会调用，对应每个OnGameFlowChangeLeave
    --- 模块默认卸载资源
    ---@overload fun(ModuleBase, OnUnloadModule)
    function TournamentModule:OnUnloadModule()
        loginfo("TournamentModule:OnUnloadModule")
    end

    --- 注销LuaEvent、Timer监听
    ---@overload fun(ModuleBase, OnDestroyModule)
    function TournamentModule:OnDestroyModule()
        loginfo("TournamentModule:OnDestroyModule")
        self:CloseMainPanel()
        self:RemoveAllLuaEvent()
        TournamentEventLogic.RemoveListeners()

    end

    ---@overload fun(ModuleBase, OnGameFlowChangeLeave)
    function TournamentModule:OnGameFlowChangeLeave(gameFlowType)
        loginfo("TournamentModule:OnGameFlowChangeLeave",gameFlowType)
        if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then
            --- TournamentEventLogic.RemoveLobbyListeners()
        end
    end

    ---@overload fun(ModuleBase, OnGameFlowChangeEnter)
    function TournamentModule:OnGameFlowChangeEnter(gameFlowType)
        loginfo("TournamentModule:OnGameFlowChangeEnter",gameFlowType)
        if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then
            --- TournamentEventLogic.AddLobbyListeners()
        end
    end

    ---------------------------------------------------------------------------------
    --- Loading 生命周期
    --- 设置bAutoLoading = true则下列生命周期有效
    --- 模块[Loading]加载资源，区分局内外
    ---------------------------------------------------------------------------------
    ---@overload fun(ModuleBase, OnLoadingLogin2Frontend)
    function TournamentModule:OnLoadingLogin2Frontend(gameFlowType)
        loginfo("TournamentModule:OnLoadingLogin2Frontend",gameFlowType)
        TournamentLoadLogic.OnLoadingLogin2FrontendProcess()
    end

    ---@overload fun(ModuleBase, OnLoadingGame2Frontend)
    function TournamentModule:OnLoadingGame2Frontend(gameFlowType)
        loginfo("TournamentModule:OnLoadingGame2Frontend",gameFlowType)
        TournamentLoadLogic.OnLoadingGame2FrontendProcess()

    end

    ---@overload fun(ModuleBase, OnLoadingFrontend2Game)
    function TournamentModule:OnLoadingFrontend2Game(gameFlowType)
        loginfo("TournamentModule:OnLoadingFrontend2Game",gameFlowType)
    end
    
    ---------------------------------------------------------------------------------
    --- Module Public API
    ---------------------------------------------------------------------------------
    function TournamentModule:ShowMainPanel(fCallback,...)
        return TournamentLogic.ShowMainPanelProcess(fCallback,...)
    end

    function TournamentModule:ShowRewardPanel(selectedMajorLevel,rankMode)
        return TournamentLogic.ShowRewardPanel(selectedMajorLevel,rankMode)
    end

    function TournamentModule:CloseMainPanel()
        return TournamentLogic.CloseMainPanelProcess()
    end

    function TournamentModule:IsTournamentUnlocked()
        return TournamentLogic.IsTournamentUnlocked()
    end
    
    function TournamentModule:DoSomeThing(...)
        return TournamentLogic.DoSomeThingProcess(...)
    end
    
    function TournamentModule:GetRankDataByMinor(minorLevel,serial)
        return TournamentLogic.GetRankDataByMinor(minorLevel,serial)
    end

    function TournamentModule:GetRankDataByMajor(majorLevel,serial)
        return TournamentLogic.GetRankDataByMajor(majorLevel,serial)
    end

    function TournamentModule:GetRankDataByScore(score,serial)
        return TournamentLogic.GetRankDataByScore(score,serial)
    end

    function TournamentModule:GetMinorIndexByScore(score,serial)--获取小段位等级(青铜I、II还是III)
        return TournamentLogic.GetMinorIndexByScore(score,serial)
    end

    function TournamentModule:GetMinorIndexByMinor(minorLevel,serial)
        return TournamentLogic.GetMinorIndexByMinor(minorLevel,serial)
    end

    function TournamentModule:GetStarNumByScore(score,serial)
        return TournamentLogic.GetStarNumByScore(score,serial)
    end

    function TournamentModule:GetLastStarPercentByScore(score,serial)--返回最后一颗星的百分比
        return TournamentLogic.GetLastStarPercentByScore(score,serial)
    end

    function TournamentModule:GetNextRankDataByScore(score,serial)
        return TournamentLogic.GetNextRankDataByScore(score,serial)
    end

    function TournamentModule:GetSortedMinorTable(serial)
        return TournamentLogic.GetSortedMinorTable(serial)
    end

    function TournamentModule:GetRankIconByScore(score,serial)
        return TournamentLogic.GetRankIconByScore(score,serial)
    end

    function TournamentModule:GetTitleInfoBySubScore(subScore)
        return TournamentLogic.GetTitleInfoBySubScore(subScore)
    end

    function TournamentModule:GetLevelRewardsInfo(majorLevel)
        return TournamentLogic.GetLevelRewardsInfo(majorLevel)
    end

    function TournamentModule:GetCurSeasonRewardsTable()
        return TournamentLogic.GetCurSeasonRewardsTable()
    end

    function TournamentModule:GetSortedRewardsTable()
        return TournamentLogic.GetSortedRewardsTable()
    end

    function TournamentModule:GetMaxRewardsInfo()
        return TournamentLogic.GetMaxRewardsInfo()
    end

    function TournamentModule:ParseRewardStr(rewardStr)
        return TournamentLogic.ParseRewardStr(rewardStr)
    end

    function TournamentModule:GetDefinedMaxMajorLevel(serial)--获取赛季定义的最大段位
        return TournamentLogic.GetDefinedMaxMajorLevel(serial)
    end

    function TournamentModule:GetRankConstant()
        return TournamentLogic.GetRankConstant()
    end

    function TournamentModule:GetSeasonConfigBySerial(serial)
        return TournamentLogic.GetSeasonConfigBySerial(serial)
    end

    function TournamentModule:GetCommanderRankDataByMinor(minorLevel,serial)
        return TournamentLogic.GetCommanderRankDataByMinor(minorLevel,serial)
    end

    function TournamentModule:GetCommanderRankDataByMajor(majorLevel,serial)
        return TournamentLogic.GetCommanderRankDataByMajor(majorLevel,serial)
    end

    function TournamentModule:GetCommanderRankDataByScore(score,serial)
        return TournamentLogic.GetCommanderRankDataByScore(score,serial)
    end

    function TournamentModule:GetCommanderMinorIndexByMinor(minorLevel,serial)
        return TournamentLogic.GetCommanderMinorIndexByMinor(minorLevel,serial)
    end

    function TournamentModule:GetCommanderStarNumByScore(score,serial)
        return TournamentLogic.GetCommanderStarNumByScore(score,serial)
    end

    function TournamentModule:GetCommanderLastStarPercentByScore(score,serial)--返回最后一颗星的百分比
        return TournamentLogic.GetCommanderLastStarPercentByScore(score,serial)
    end

    function TournamentModule:GetCommanderNextRankDataByScore(score,serial)
        return TournamentLogic.GetCommanderNextRankDataByScore(score,serial)
    end

    function TournamentModule:GetCommanderSortedMinorTable(serial)
        return TournamentLogic.GetCommanderSortedMinorTable(serial)
    end

    function TournamentModule:GetCommanderRankIconByScore(score,serial)
        return TournamentLogic.GetCommanderRankIconByScore(score,serial)
    end

    function TournamentModule:GetCommanderLevelRewardsInfo(majorLevel)
        return TournamentLogic.GetCommanderLevelRewardsInfo(majorLevel)
    end

    function TournamentModule:GetCommanderCurSeasonRewardsTable()
        return TournamentLogic.GetCommanderCurSeasonRewardsTable()
    end

    function TournamentModule:GetCommanderSortedRewardsTable()
        return TournamentLogic.GetCommanderSortedRewardsTable()
    end

    function TournamentModule:GetCommanderDefinedMaxMajorLevel(serial)--获取赛季定义的最大段位
        return TournamentLogic.GetCommanderDefinedMaxMajorLevel(serial)
    end

    function TournamentModule:GetCommanderRankConstant()
        return TournamentLogic.GetCommanderRankConstant()
    end

    function TournamentModule:GetCommanderAbilityLevelParam()--获取能力品阶参数
        return TournamentLogic.GetCommanderAbilityLevelParam()
    end

    function TournamentModule:GetCommanderWordParamByKey(paramKey)
        return TournamentLogic.GetCommanderWordParamByKey(paramKey)
    end

    function TournamentModule:GetCommanderSeasonConfigBySerial(serial)
        return TournamentLogic.GetCommanderSeasonConfigBySerial(serial)
    end

    function TournamentModule:ChecktoShowSeasonRestartWindow(fFinishCallback)
        return TournamentEventLogic.ChecktoShowSeasonRestartWindow(fFinishCallback)
    end

    function TournamentModule:IsInSeasonInitProtectTime()
        return TournamentLogic.IsInSeasonInitProtectTime()
    end

    function TournamentModule:GetIsShowingSeasonRestartWindow()
        return TournamentLogic.GetIsShowingSeasonRestartWindow()
    end

    function TournamentModule:SetIsShowingSeasonRestartWindow(isShowingSeasonRestartWindow)
        TournamentLogic.SetIsShowingSeasonRestartWindow(isShowingSeasonRestartWindow)
    end
    
    return TournamentModule
