----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------



local UIWaterfallScrollBox      = require "DFM.Business.DataStruct.UIDataStruct.UIWaterfallScrollBox"
local Sort                      = require "DFM.Business.DataStruct.Common.Base.Sort"
local Table                     = require "DFM.Business.DataStruct.Common.Base.Table"
local NavigationAgent         = require "DFM.Business.DataStruct.Common.Agent.NavigationAgent"
local AnimManager               = require "DFM.Business.DataStruct.Common.Base.DFUtil.AnimManager"
local ActivityFrontPageBtn      = require "DFM.Business.Module.ActivityModule.UI.FrontPage.ActivityFrontPageBtn"
local Deep                         = require "DFM.Business.DataStruct.Common.Base.Deep"

---@class ActivityFrontPageCrossoverLayout : LuaUIBaseView
local ActivityFrontPageCrossoverLayout = ui("ActivityFrontPageNormalLayout")

local function CheckURL(url)
    return url ~= nil and url ~= ""
end

function ActivityFrontPageCrossoverLayout:Ctor()
    ---@type ActivityFrontPageBtn[]
    self._wtActivityButtons = {}

    for i = 1, 4 do
        self._wtActivityButtons[i] = self:Wnd("WBP_Activity_EntranceBtn_"..tostring(i), ActivityFrontPageBtn)
    end

    self._navMgr = NavigationAgent.Create(self)
end

---@param activityInfos pb_ActivityInfo[]
function ActivityFrontPageCrossoverLayout:_DoLayout(activityInfos)
    local layout = {}

    -- 添加一个大按钮
    local activityInfo = table.remove(activityInfos, 1)
    table.insert(layout,
        {
            style = EActivityFrontPageBtnStyle.ArkBig,
            activityInfo = activityInfo,
            themeID = 1,
        }
    )

    -- 添加3个小按钮
    for i = 1, 3 do
        local activityInfo = table.remove(activityInfos, 1)
        table.insert(layout,
            {
                style = EActivityFrontPageBtnStyle.ArkSmall,
                activityInfo = activityInfo,
                themeID = 1,
            }
        )
    end

    return layout
end

---@param groupInfo ActivityGroupPresentationInfo
function ActivityFrontPageCrossoverLayout:SetData(groupInfo)
    groupInfo = groupInfo or {groupID = 0, activities = {}, themeID = 0}
    local activityIdList = groupInfo.activities or {}

    -- 排序活动
    local activityInfos = Table.CollectValues(Server.ActivityServer.AllActivityInfos, activityIdList) ---@type pb_ActivityInfo[]
    Sort.MergeSort(activityInfos, 
        Sort.Preferenced({
            -- 优先已开启活动
            Sort.MatchCriteria(function(activityInfo) return Server.ActivityServer:IsActivityStarted(activityInfo.actv_id) end),
            -- 然后按活动权重排序(倒序 大权重 < 小权重)
            Sort.ByField("order_weight" , nil, true),
    }))
    self._sortedActivities = Deep.DeepCopy(activityInfos, 1)

    local layoutData = self:_DoLayout(activityInfos)
    self:UpdateDisplay(layoutData, true)
end

function ActivityFrontPageCrossoverLayout:GetSortedActivities()
    return self._sortedActivities
end

function ActivityFrontPageCrossoverLayout:OnShowBegin()
    self._navMgr:CreateGroup({
        id          = "MainGroup",
        rootWidget  = self:Wnd("CanvasPanel_0"),
        members     = self._wtActivityButtons,
        bSimClick   = false,
        bStack      = true,
        analogCursorSpeedFactor = 1.0,
    })
    self._navMgr:SetWrapBoundaryRule("MainGroup", {EUINavigation.Left, EUINavigation.Right})
    self._navMgr:FocusGroup("MainGroup")
end

function ActivityFrontPageCrossoverLayout:OnHideBegin()
    self._navMgr:RemoveAllGroups()
end

function ActivityFrontPageCrossoverLayout:UpdateDisplay(layoutData, bAnim)
    for i = 1, 4 do
        self._wtActivityButtons[i]:SetItemData(layoutData[i], true)
        self._wtActivityButtons[i]:Hide()
    end

    local function FadeIn1stRow()
        self._wtActivityButtons[1]:FadeIn()
    end

    local function FadeIn2ndRow()
        for i = 2, 4 do
            self._wtActivityButtons[i]:FadeIn()
        end
    end

    Timer.DelayCall(0, FadeIn1stRow, self)
    Timer.DelayCall(Module.Activity.Config.FrontPageAnimationRowInterval, FadeIn2ndRow, self)
end

return ActivityFrontPageCrossoverLayout