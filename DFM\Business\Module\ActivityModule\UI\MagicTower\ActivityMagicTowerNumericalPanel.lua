----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class ActivityMagicTowerNumericalPanel : LuaUIBaseView
local ActivityMagicTowerNumericalPanel = ui("ActivityMagicTowerNumericalPanel")
local ActivityMagicTowerNumericalBox = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerNumericalBox"

-- 数值框索引枚举
local BoxIndex = {
    HP = 1,    -- 生命
    ATK = 2,   -- 攻击
    DEF = 3,   -- 防御
    CRIT = 4   -- 暴击
}

-- 变化类型枚举
local ChangeType = {
    GreenUp = 0,   -- 绿涨
    RedDown = 1,   -- 红跌
    YellowUp = 2   -- 黄涨
}

function ActivityMagicTowerNumericalPanel:Ctor()
    self._isInitialized = false
    self._lastValues = {
        hp = 0,
        atk = 0,
        def = 0,
        crit = 0
    }

    self._wtHeadIcon = self:Wnd("DFImage_116", UIImage)

    self._wtNumericalBoxes = {
        self:Wnd("WBP_MorgenGame_NumericalBox_202", ActivityMagicTowerNumericalBox),  -- 生命
        self:Wnd("WBP_MorgenGame_NumericalBox", ActivityMagicTowerNumericalBox),      -- 攻击
        self:Wnd("WBP_MorgenGame_NumericalBox_62", ActivityMagicTowerNumericalBox),   -- 防御
        self:Wnd("WBP_MorgenGame_NumericalBox_126", ActivityMagicTowerNumericalBox)   -- 暴击
    }
end

function ActivityMagicTowerNumericalPanel:OnOpen()
    self:_InitNumericalBoxes()
end

function ActivityMagicTowerNumericalPanel:OnClose()
    self._lastValues = nil
    self._wtNumericalBoxes = nil
end

---------------Private Function--------------
function ActivityMagicTowerNumericalPanel:_InitNumericalBoxes()
    for i, box in ipairs(self._wtNumericalBoxes) do
        box:SetBpType(i - 1)
    end
end

function ActivityMagicTowerNumericalPanel:_GetBox(index)
    if index >= BoxIndex.HP and index <= BoxIndex.CRIT then
        return self._wtNumericalBoxes[index]
    end

    return nil
end

function ActivityMagicTowerNumericalPanel:_DetermineChangeType(index, newValue)
    if not self._isInitialized then
        return nil
    end
    
    local lastValue = self._lastValues[({
        [BoxIndex.HP] = "hp",
        [BoxIndex.ATK] = "atk",
        [BoxIndex.DEF] = "def",
        [BoxIndex.CRIT] = "crit"
    })[index]]
    
    if not lastValue or newValue == lastValue then
        return nil
    end
    
    if newValue > lastValue then
        return index == BoxIndex.HP and ChangeType.GreenUp or ChangeType.YellowUp
    else
        return ChangeType.RedDown
    end
end

function ActivityMagicTowerNumericalPanel:_UpdateBoxData(index, value, changeType, bAutoPlay)
    local box = self:_GetBox(index)
    if not box then return end
    
    local appendStr = ""
    if index == BoxIndex.CRIT then
        appendStr = "%"
    end
    box:SetValueTxt(value, appendStr)

    if bAutoPlay then
        changeType = self:_DetermineChangeType(index, value)
    end
    
    if changeType and changeType >= 0 then
        box:SetBpStyle(changeType)
        box:PlayChangeAnimation()
    end
    
    self._lastValues[({
        [BoxIndex.HP] = "hp",
        [BoxIndex.ATK] = "atk",
        [BoxIndex.DEF] = "def",
        [BoxIndex.CRIT] = "crit"
    })[index]] = value
end

---------------Public Function--------------
-- 初始化+全更
---@param heroStauts table 包含hp, atk, def, crit的数值
---@param changes table 对应数值的变化类型 (0:绿涨 1:红跌 2:黄涨)
---@param bAutoPlay boolean 是否自动播放变化动画
function ActivityMagicTowerNumericalPanel:UpdateAllValues(heroStauts, changes, bAutoPlay)
    if not heroStauts then return end
    
    self:_UpdateBoxData(BoxIndex.HP, heroStauts.hp, changes and changes.hp, bAutoPlay)
    self:_UpdateBoxData(BoxIndex.ATK, heroStauts.atk, changes and changes.atk, bAutoPlay)
    self:_UpdateBoxData(BoxIndex.DEF, heroStauts.def, changes and changes.def, bAutoPlay)
    self:_UpdateBoxData(BoxIndex.CRIT, heroStauts.crit, changes and changes.crit, bAutoPlay)

    if not self._isInitialized then
        self._isInitialized = true
    end
end

-- 单点更新
---@param boxIndex number 要更新的数值框索引
---@param value number 更新的数值
---@param changeType ChangeType 变化效果类型(可选)
function ActivityMagicTowerNumericalPanel:UpdateValue(boxIndex, value, changeType)
    if not value or not tonumber(value) then return end

    self:_UpdateBoxData(boxIndex, value, changeType)
end

-- 设置头像
---@param headImgPath string 头像路径
function ActivityMagicTowerNumericalPanel:SetHeadIconImg(headImgPath)
    if headImgPath then
        self._wtHeadIcon:AsyncSetImagePath(headImgPath)
    else
        logerror("[Magic_Tower] SetHeadIconImg failed, headImgPath: ", tostring(headImgPath))
    end
end

return ActivityMagicTowerNumericalPanel