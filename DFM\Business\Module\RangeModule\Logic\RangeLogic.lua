----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRange)
----- LOG FUNCTION AUTO GENERATE END -----------



local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"
local RangeConfig = require "DFM.Business.Module.RangeModule.RangeConfig"
local RangeEquipLogic = require "DFM.Business.Module.RangeModule.Logic.RangeEquipLogic"
local RangeTargetLogic = require "DFM.Business.Module.RangeModule.Logic.RangeTargetLogic"
local RangeAnalysisLogic = require "DFM.Business.Module.RangeModule.Logic.RangeAnalysisLogic"
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local MemoryUtil = require "DFM.YxFramework.Util.MemoryUtil"

local UGameplayStatics = import "GameplayStatics"
local ABaseHUD = import "BaseHUD"
local ATeamSystem = import "TeamSystem"
local UKismetSystemLibrary = import "KismetSystemLibrary"
local USafeHouseRangeUtil = import "SafeHouseRangeUtil"
local ASafeHouseRangeData = import "SafeHouseRangeData"
local UDFMGameplayDelegates = import "DFMGameplayDelegates"
local UGPGameplayDelegates = import "GPGameplayDelegates"
local EViewTargetBlendFunction = import "EViewTargetBlendFunction"
local EDFMGamePlayMode = import "EDFMGamePlayMode"
local EWeaponAmmoLogicType = import "EWeaponAmmoLogicType"
local ESafeHouseHUDMode = import "ESafeHouseHUDMode"
local UCharacterMovementComponent = import "CharacterMovementComponent"
local UGPGameHudDelegates = import "GPGameHudDelegates"
local UGPInputHelper = import "GPInputHelper"
local EInputActionType = import "EInputActionType"
local UGPInputHelper = import "GPInputHelper"
local UDFHDDisplayInputActionManager = import "DFHDDisplayInputActionManager"
local EGPFSMTransitionEvent = import "EGPFSMTransitionEvent"
local EProcessInputCompReason = import "EProcessInputCompReason"
local DFMGameDataKeyForStoryState = import "DFMGameDataKeyForStoryState"
local UDFMGameGPM = import "DFMGameGPM"
--- BEGIN MODIFICATION @ VIRTUOS
local WidgetUtil                    = require "DFM.YxFramework.Util.WidgetUtil"
local UGPInputDelegates             = import  "GPInputDelegates"
local EGPInputType                  = import  "EGPInputType"
--- END MODIFICATION

local function log(...)
    loginfo("[RangeLogic]", ...)
end

-- 管理特勤处靶场相关的逻辑
local RangeLogic = {}

RangeLogic.MAP_SCENE_NAME = "ShootingArea"
RangeLogic.SCENE_LOAD_TIMEOUT = 120

RangeLogic.actionBackHandle = nil
RangeLogic.loadState = 0
RangeLogic.weaponDirty = false
RangeLogic.bFromStackUI = false
RangeLogic.bFromStackUIIndex = -1
RangeLogic.FromSafeHouseLoc = nil
RangeLogic.enterContext = {}
--- BEGIN MODIFICATION @ VIRTUOS
RangeLogic._OnNotifyInputTypeChangedHandle = nil
--- END MODIFICATION

function RangeLogic.AddEvents()
    -- 2.10 键盘仍需保留
    local inputMonitor = Facade.UIManager:GetInputMonitor()
    --- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        if not RangeLogic._OnNotifyInputTypeChangedHandle then
            RangeLogic._OnNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(RangeLogic._OnInputTypeChanged)
            -- 初始化
            local curInputType = WidgetUtil.GetCurrentInputType()
            RangeLogic._OnInputTypeChanged(curInputType)
        end
        -- HD requires a long press on the special right key.
        UGPGameplayDelegates.Get(GetGameInstance()).OnOpenSystemSettingHDEntrance:Bind(RangeLogic.ToggleSettingEntrance, self)
    end
    -- END MODIFICATION

    -- LuaGlobalEvents.evtGameFlowChangeLeave:AddListener(RangeLogic.ResetRangeState)
    -- DFMGlobalEvents.evtWorldCleanUp:AddListener(RangeLogic.ResetRangeState)
    Module.Gunsmith.Config.Events.evtGunsmithOnRangeAddOrUpdateAssembleWeapon:AddListener(RangeEquipLogic.OnRangeAddOrUpdateAssembleWeapon)
    Facade.UIManager.Events.evtStackUIChanged:AddListener(RangeLogic.OnStackUIChanged)
    Server.HeroServer.Events.evtSOLUsedHeroIdChanged:AddListener(RangeLogic.OnSOLUsedHeroIdChanged)
    Server.HeroServer.Events.evtMPUsedHeroIdChanged:AddListener(RangeLogic.OnSOLUsedHeroIdChanged)
    -- Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult:AddListener(RangeLogic.OnPakDownloadResult)

    -- local character = InGameController:Get():GetGPCharacter()
    -- character.OnCharacterReloadAmmo:Add(RangeEquipLogic.OnPlayerReload)

    UDFMGameplayDelegates.Get(GetWorld()).OnRangeTargetHit:Add(RangeAnalysisLogic.OnRangeTargetHit)
    UGPGameplayDelegates.Get(GetWorld()).OnWeaponPostFire:Add(RangeAnalysisLogic.OnWeaponPostFire)
    UDFMGameplayDelegates.Get(GetWorld()).OnProcessRangeManageWeapon:Add(RangeLogic.OnProcessRangeManageWeapon)
    UGPGameHudDelegates.Get(GetWorld()).PostOnGameHudStateChanged:Add(RangeLogic.PostOnGameHudStateChanged)

end

function RangeLogic.RemoveEvents()
    if RangeLogic.actionBackHandle then
        local inputMonitor = Facade.UIManager:GetInputMonitor()
        inputMonitor:RemoveDisplayActoinBingingForHandle(RangeLogic.actionBackHandle)
        RangeLogic.actionBackHandle = nil
    end

    -- LuaGlobalEvents.evtGameFlowChangeLeave:RemoveAllListener(RangeLogic.ResetRangeState)
    -- DFMGlobalEvents.evtWorldCleanUp:RemoveAllListener(RangeLogic.ResetRangeState)
    Module.Gunsmith.Config.Events.evtGunsmithOnRangeAddOrUpdateAssembleWeapon:RemoveAllListener(RangeEquipLogic.OnRangeAddOrUpdateAssembleWeapon)
    Facade.UIManager.Events.evtStackUIChanged:RemoveListener(RangeLogic.OnStackUIChanged)
    Server.HeroServer.Events.evtSOLUsedHeroIdChanged:RemoveListener(RangeLogic.OnSOLUsedHeroIdChanged)
    Server.HeroServer.Events.evtMPUsedHeroIdChanged:RemoveListener(RangeLogic.OnSOLUsedHeroIdChanged)
    -- Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult:RemoveListener(RangeLogic.OnPakDownloadResult)

    -- local character = InGameController:Get():GetGPCharacter()
    -- character.OnCharacterReloadAmmo:Remove(RangeEquipLogic.OnPlayerReload)
    
    -- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        -- HD requires a long press on the special left key.
        UGPGameplayDelegates.Get(GetGameInstance()).OnOpenSystemSettingHDEntrance:Clear()
        if RangeLogic._OnNotifyInputTypeChangedHandle then
            UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(RangeLogic._OnNotifyInputTypeChangedHandle)
            RangeLogic._OnNotifyInputTypeChangedHandle = nil
        end
    end
    -- END MODIFICATION

    UDFMGameplayDelegates.Get(GetWorld()).OnRangeTargetHit:Remove(RangeAnalysisLogic.OnRangeTargetHit)
    UGPGameplayDelegates.Get(GetWorld()).OnWeaponPostFire:Remove(RangeAnalysisLogic.OnWeaponPostFire)
    UDFMGameplayDelegates.Get(GetWorld()).OnProcessRangeManageWeapon:Remove(RangeLogic.OnProcessRangeManageWeapon)
    UGPGameHudDelegates.Get(GetWorld()).PostOnGameHudStateChanged:Remove(RangeLogic.PostOnGameHudStateChanged)

end

function RangeLogic.OnGameFlowChangeEnter(gameFlowType)
    
end

function RangeLogic.OnGameFlowChangeLeave(gameFlowType)
    if gameFlowType == EGameFlowStageType.SafeHouse
    or gameFlowType == EGameFlowStageType.Lobby then
        log("_OnMatchStateChanged CheckLeaveRange on game flow leave.")
        RangeLogic.CheckLeaveRange(false)
        RangeLogic.CheckRangeHUDShouldReset()
    end
end

function RangeLogic.OnSubStageChangeEnter(enterSubStageType)
    if enterSubStageType == ESubStage.Range then
        RangeLogic.OnEnterRangeStage()
    end
end

function RangeLogic.OnSubStageChangeLeave(leaveSubStageType)
    if leaveSubStageType == ESubStage.Range then
        RangeLogic.OnLeaveRangeStage()
    end
end

function RangeLogic.OnEnterRangeStage()
    log("OnEnterRange")
    -- @fixme 临时加的清理
    if DFHD_LUA == 1 then
        UGPInputHelper.ClearInputMode(GetGameInstance(), false)
    end
    -- 切MiniWorld
    LightUtil.SetMiniWorld(true)

    -- 切HUDState
    RangeLogic._SetHUDState(true)
    RangeLogic._SetCharacter(true)
    RangeLogic._SetController(true)
    RangeLogic._SetConsoleVars(true)
    -- 处理声音
    RangeLogic._SetSound(true)

    RangeEquipLogic.OnEnterRangeSubStage(true)
    RangeTargetLogic.OnEnterRangeStage(true)

    RangeLogic._NotifyEnterRange(true)

    RangeLogic._ReportTGPA(true)

    RangeLogic._DebugPrintDisplayInput()

    -- 重置假人
    RangeTargetLogic.InitRangeTargets()
end

function RangeLogic.OnLeaveRangeStage()
    log("OnLeaveRange")

    LightUtil.SetMiniWorld(false)

    -- 切HUDState
    RangeLogic._SetHUDState(false)
    RangeLogic._SetCharacter(false)
    RangeLogic._SetController(false)
    RangeLogic._SetConsoleVars(false)
    RangeLogic._SetSound(false)

    RangeEquipLogic.OnEnterRangeSubStage(false)
    RangeTargetLogic.OnEnterRangeStage(false)

    RangeLogic._NotifyEnterRange(false)

    RangeLogic._ReportTGPA(false)

end

function RangeLogic.ToggleSettingEntrance()
    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIBack)
    if Facade.UIManager:GetStackUICount() > 0 then
        return
    end
    -- Module.SystemSetting:ShowOrHideSystemSettingHDEntrance()
    -- 采用新版侧滑菜单
    Facade.UIManager:AsyncShowUI(UIName2ID.SafeHouse3DExitPanel)
end

-----------------------------------------------------------------------
--region Download

function RangeLogic.CheckRangePakDownload()
	-- PC不关心小包
	if IsHD() then
		return true
	end

    local safehouseModuleName = LiteDownloadManager:GetSystemDownloadModuleName(EDownloadSystem.SafeHouse)
	local bDownload = LiteDownloadManager:IsDownloadedByModuleName(safehouseModuleName)
	if bDownload then
		return true
	end

	local bDownloading = LiteDownloadManager:IsDownloadedByModuleName(safehouseModuleName)
	log("CheckRangePakDownload", bDownloading)
	if bDownloading then
		Module.LitePackage:ShowMainPanel()
	else
		local function fOnConfirm()
			log("CheckRangePakDownload Confirm download.")
			LiteDownloadManager:DownloadByModuleName(safehouseModuleName)

			Module.LitePackage:ShowMainPanel()
		end

		local pakTotalSize = LiteDownloadManager:GetTotalSizeByModuleName(safehouseModuleName)
		local pakNowSize = LiteDownloadManager:GetNowSizeByModuleName(safehouseModuleName)
		local pakTotalSizeMBStr = string.format("%.1f", (pakTotalSize - pakNowSize) / 1024 / 1024)
		local showStr = StringUtil.Key2StrFormat(RangeConfig.Loc.RangePakDownloadConfirmText, {["PakSize"] = pakTotalSizeMBStr})
		Module.CommonTips:ShowConfirmWindow(showStr, fOnConfirm)
	end

	return false
end

function RangeLogic.OnPakDownloadResult(moduleName, bSuccess)
    local safehouseModuleName = LiteDownloadManager:GetSystemDownloadModuleName(EDownloadSystem.SafeHouse)
    if moduleName == safehouseModuleName and bSuccess then
		log("OnPakDownloadResult success.")

		local worldComposition = GetWorld().WorldComposition
		if worldComposition then
			-- worldComposition:RequestReinitialize()
			worldComposition:Reinitialize()
		end
	end
end

--endregion
-----------------------------------------------------------------------

function RangeLogic.CheckRangeLevelLoaded()
    local needLoadLevels, noExistSceneSubLevelNames = GetMapSceneName2SceneSubLevelNames(RangeLogic.MAP_SCENE_NAME)
    for _, needLoadLevel in ipairs(needLoadLevels) do
        if not Facade.LevelLoadManager:CheckIsStreamLevelLoadedByName(needLoadLevel) then
            return false
        end
    end

    return true
end

function RangeLogic.CheckRangeLevelConfig()
    local needLoadLevels, noExistSceneSubLevelNames = GetMapSceneName2SceneSubLevelNames(RangeLogic.MAP_SCENE_NAME)
    for _, needLoadLevel in ipairs(needLoadLevels) do
        if UGameplayStatics.GetStreamingLevel(GetWorld(), needLoadLevel) == nil then
            return false
        end
    end
    return true
end

function RangeLogic.LoadRangeLvls(bFromStackUI, weaponProp, selectHeroId)
    if RangeLogic.loadState == 1 or RangeLogic.loadState == 2 then
        return
    end
    RangeLogic.loadState = 1

    -- 加载场景
    local function fAllFinishCallback(list, reason)
        log("LoadRangeLvls fAllFinishCallback", #list, reason)

        if reason == EOperateStreamLevelReason.Success then
            if RangeLogic.loadState ~= 2 then
                RangeLogic.loadState = 2

                -- 给一个0.1s的黑幕，掩盖一下镜头问题
                Facade.UIManager:CommitTransition(true)
                Timer.DelayCall(0.1, function()
                    Facade.UIManager:CommitTransition(false)
                end)
        
                RangeLogic.InitRangeSceneConfig()
                RangeTargetLogic.SpawnRangeTargets()
                RangeLogic.EnterRange(bFromStackUI, weaponProp, selectHeroId)
            end
        else
            RangeLogic.loadState = 0
        end
    end
    Facade.LevelLoadManager:AsyncOperateStreamLevelsByMapSceneName(RangeLogic.MAP_SCENE_NAME, true, nil, 
        fAllFinishCallback, false, RangeLogic.SCENE_LOAD_TIMEOUT)
end

function RangeLogic.ShouldUnloadRange()
    local isLowMemoryDevice = MemoryUtil.IsLogicLowMemoryDevice()
	return MemoryUtil.IsLogicLowMemoryDevice()
end

function RangeLogic.UnloadRangeLvls()
    log("UnloadRangeLvls")

    -- 卸载场景
    RangeLogic.loadState = 0
    local function fAllFinishCallback(list, reason)
        log("UnloadRangeLvls fAllFinishCallback")
    end
    Facade.LevelLoadManager:AsyncOperateStreamLevelsByMapSceneName(RangeLogic.MAP_SCENE_NAME, false, nil, 
        fAllFinishCallback, false, RangeLogic.SCENE_LOAD_TIMEOUT)

    -- 卸载HUD
    local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
	if isvalid(localCtrl) then
		localCtrl:SetSafeHouseHUDMode(ESafeHouseHUDMode.None, true)
	end
end

function RangeLogic.CheckCanEnterRange(bShowTip)
    local bReady, readyState = Server.MatchServer:GetIsReadytoGo()
    if bReady then
        if bShowTip then
            if readyState == 1 then
                Module.CommonTips:ShowSimpleTip(RangeConfig.Loc.CannotEnterRange_InMatching)
            elseif readyState == 2 then
                Module.CommonTips:ShowSimpleTip(RangeConfig.Loc.CannotEnterRange_InReadying)
            end
        end
        return false
    end

    return true
end

function RangeLogic.ResetRangeState()
    log("ResetRangeState")

    if RangeLogic.actionBackHandle then
        local inputMonitor = Facade.UIManager:GetInputMonitor()
        inputMonitor:RemoveDisplayActoinBingingForHandle(RangeLogic.actionBackHandle)
        RangeLogic.actionBackHandle = nil
    end
    RangeLogic.loadState = 0
    RangeLogic.weaponDirty = false
    RangeLogic.bFromStackUI = false
    RangeLogic.bFromStackUIIndex = -1
    RangeLogic.FromSafeHouseLoc = nil
    RangeLogic.enterContext = {}

    Server.IrisSafeHouseServer:ResetRangeData(true)
end

function RangeLogic.EnterRange(bFromStackUI, weaponProp, selectHeroId)
    log("EnterRange", bFromStackUI)

    local context = {
        weaponProp = weaponProp,
        selectHeroId = selectHeroId,
        currentMPBagID = -1,
    }

    if Server.IrisSafeHouseServer.bIsInRange then
        logwarning("RangeLogic.EnterRange already in range.")
        return
    end

    if not RangeLogic.CheckRangePakDownload() then
		log("EnterRange range pak not download, don't enter.")
		return
	end

    if not RangeLogic.CheckCanEnterRange(true) then
        log("EnterRange not allowed, don't enter.")
        return
    end

    if not RangeLogic.CheckRangeLevelLoaded() and RangeLogic.loadState == 2 then
        log("EnterRange reset loadState = 0 while levels not loaded")
        RangeLogic.loadState = 0
    end

    if not RangeLogic.CheckRangeLevelConfig() then
        log("EnterRange range level config not correct(not streaming level), should rescan.")
		local worldComposition = GetWorld().WorldComposition
		if worldComposition then
			-- worldComposition:RequestReinitialize()
			worldComposition:Reinitialize()
		end
    end

    -- 预加载武器蓝图
    Module.IrisSafeHouse:PreloadWeaponBlueprints("Range")

    local loadState = RangeLogic.loadState
    if RangeLogic.loadState == 0 then
        RangeLogic.LoadRangeLvls(bFromStackUI, weaponProp, selectHeroId)
    end
    ItemOperaTool.AddUseCharacterBusiness(ItemOperaTool.SafeHouseBusiness.Range)
    -- 创建主角
    local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
    if localCtrl and not localCtrl.bHasSpawnCharacter then
        localCtrl:SpawnDefaultCharacter()
    end
    if loadState == 0 or loadState == 1 then
        return
    end

    -- 修改全局状态
    local currentGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    if currentGameFlow == EGameFlowStageType.SafeHouse then
        Server.IrisSafeHouseServer.bRangeSOLMode = true
    elseif currentGameFlow == EGameFlowStageType.Lobby then
        Server.IrisSafeHouseServer.bRangeSOLMode = false
    else
        logerror(string.format("RangeLogic.EnterRange Invalid game flow=%d, please check!!!", currentGameFlow))
    end
    Server.IrisSafeHouseServer.bIsInRange = true

    if Server.IrisSafeHouseServer.bRangeSOLMode then
        RangeLogic.EnterRange_SOLConfig()
    else
        RangeLogic.EnterRange_MPConfig()

        local bagID, groupID = Server.ArmedForceServer:GetBagIDFromUIContext()
        context.currentMPBagID = bagID
    end
    RangeAnalysisLogic.ResetData()

    RangeLogic.bFromStackUI = bFromStackUI
    if bFromStackUI then
        -- RangeLogic.bFromStackUIIndex = Module.CommonBar:GetCurrentTopTabIndex(1)

        log("SaveBlackBoxWithTransition")
        Facade.UIManager:SaveBlackBoxWithTransition()
    end
    Facade.GameFlowManager:EnterSubStage(ESubStage.Range)

    -- 事件注册
    RangeLogic.AddEvents()

    -- 设置伤害跳字
    RangeTargetLogic.InitShowDamage()

    Facade.UIManager:SetStackBottomKeepMode(false, "Range")

    -- 切HUD
    RangeLogic._InitRangeHUD()

    RangeLogic._SetBGM(true)

    -- -- 有些HUD需要特殊处理下
    -- local hud = ABaseHUD.GetHUD(GetWorld())
    -- -- 1. 通知UDFMSkillProcessBarActionView更新可见性
    -- local barViews = hud:GetPanel("WBP_TipsPro_Action")
    -- local barView = #barViews > 0 and barViews[1]
    -- if barView then
    --     barView.bUpdateBar = true
    -- end

    -- 初始化靶场假人
    RangeTargetLogic.InitRangeTargets()

    -- 传送到靶场
    RangeLogic.Teleport(true)

    -- 初始化玩家
    RangeEquipLogic.InitPlayer(context)

    -- 状态机切换 She3 7.15 需晚于InitPlayer
    local character = InGameController:Get():GetGPCharacter()
    character:SafeHouseEnterRange(true)

    -- Save context
    RangeLogic.enterContext = context

    -- 事件通知
    RangeConfig.evtEnterRange:Invoke(true)
end

function RangeLogic.InitRangeSceneConfig()
    local outActors
    outActors = UGameplayStatics.GetAllActorsOfClass(GetWorld(), ASafeHouseRangeData, outActors)
    ensure(#outActors == 1)
    Server.IrisSafeHouseServer.rangeDataCfg = outActors[1]
end

function RangeLogic.EnterRange_SOLConfig()
    local gameState = InGameController:Get():GetGameState()
    -- gameState:SetDFMGamePlayerMode(EDFMGamePlayMode.GamePlayMode_SOL)
    gameState.DFMGamePlayerMode = EDFMGamePlayMode.GamePlayMode_SOL
    gameState:UpdateClientGameModeConfig(EDFMGamePlayMode.GamePlayMode_SOL, "EnterExitRange")

    local gameMode = UGameplayStatics.GetGameMode(GetWorld())
    gameMode.WeaponAmmoLogic = EWeaponAmmoLogicType.ItemAmmo

    USafeHouseRangeUtil.SetRangeDamageMode(GetWorld(), true)

    Module.HUD:UpdateBaseLayout(MatchGameRule.SOLGameRule)
end

function RangeLogic.EnterRange_MPConfig()
    local gameState = InGameController:Get():GetGameState()
    -- gameState:SetDFMGamePlayerMode(EDFMGamePlayMode.GamePlayMode_Conquest)
    gameState.DFMGamePlayerMode = EDFMGamePlayMode.GamePlayMode_Conquest
    gameState:UpdateClientGameModeConfig(EDFMGamePlayMode.GamePlayMode_Conquest, "EnterExitRange")

    local gameMode = UGameplayStatics.GetGameMode(GetWorld())
    gameMode.WeaponAmmoLogic = EWeaponAmmoLogicType.CommonAmmo

    USafeHouseRangeUtil.SetRangeDamageMode(GetWorld(), false)

    Module.HUD:UpdateBaseLayout(MatchGameRule.TDMODGameRule)
end

function RangeLogic.LeaveRange_Config()
    local gameState = InGameController:Get():GetGameState()
    -- gameState:SetDFMGamePlayerMode(EDFMGamePlayMode.GamePlayMode_SafeHouse)
    gameState.DFMGamePlayerMode = EDFMGamePlayMode.GamePlayMode_SafeHouse
    gameState:UpdateClientGameModeConfig(EDFMGamePlayMode.GamePlayMode_SafeHouse, "EnterExitRange")
end

function RangeLogic.CheckRangeHUDShouldReset()
    local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
    if localCtrl.CurrentHUDMode == ESafeHouseHUDMode.Range then
        log("CheckRangeHUDShouldReset force set hud to none.")
        
        localCtrl:SetSafeHouseHUDMode(ESafeHouseHUDMode.None, true)
    end
end

function RangeLogic.CheckLeaveRange(bNormalExit)
    if Server.IrisSafeHouseServer.bIsInRange then
        RangeLogic.LeaveRange(bNormalExit)
        return true
    end

    return false
end

function RangeLogic.LeaveRange(bNormalExit)
    bNormalExit = setdefault(bNormalExit, true)
    log("LeaveRange", bNormalExit)

    if not Server.IrisSafeHouseServer.bIsInRange then
        logwarning("RangeLogic.EnterRange not in range.")
        return
    end
    -- 切换全局状态
    Server.IrisSafeHouseServer.bIsInRange = false

    -- 由改枪台进入靶场情况下，返回改枪台时需要保留枪械数据
    if RangeLogic.bFromStackUI then
        RangeLogic.ProcessSavePropInfoByGunsmithEnter()
    end

    local character = InGameController:Get():GetGPCharacter()
    local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)

    RangeLogic.LeaveRange_Config()
    if Facade.GameFlowManager:GetCurrentSubStage() == ESubStage.Range then
		Facade.GameFlowManager:EnterSubStage(ESubStage.None)
	end
    if not RangeLogic.bFromStackUI then
        if Server.IrisSafeHouseServer.bRangeSOLMode then
            Facade.GameFlowManager:EnterSubStage(ESubStage.SafeHouse3D)
        end
    end

    -- 事件注销
    RangeLogic.RemoveEvents()

    -- 释放武器蓝图
    Module.IrisSafeHouse:ReleaseWeaponBlueprints("Range")

    -- -- 关闭友伤
    -- ATeamSystem.GetTeamSystem(GetWorld()).bForceTeamDamage = false

    -- 恢复伤害跳字
    RangeTargetLogic.SetShowDamage(false, false)

    -- 恢复无限弹药
    RangeEquipLogic.SetUnlimitedAmmo(true)

    -- 状态机切换
    character:SafeHouseEnterRange(false)

    if RangeLogic.bFromStackUI then
        Facade.UIManager:SetStackBottomKeepMode(true, "Range")
    end

    RangeLogic._SetBGM(false)

    if bNormalExit then
        -- 切HUD
        if RangeLogic.bFromStackUI then
            localCtrl:SetSafeHouseHUDMode(ESafeHouseHUDMode.None, false)
        end

        -- 传送回特勤处
        RangeLogic.Teleport(false)
        local weaponProp = RangeLogic.enterContext.weaponProp
        -- if weaponProp then
        --     local bagId, groupId = 0, 0
        --     if Server.IrisSafeHouseServer.bRangeSOLMode then
        --         groupId = ESlotGroup.Player
        --     else
        --         bagId = RangeLogic.enterContext.currentMPBagID
        --         groupId = ESlotGroup.MPApply
        --     end

        --     log("LeaveRange OpenFromPropInfo", weaponProp.id, groupId)
        --     Module.Gunsmith:OpenFromPropInfo(weaponProp, groupId)
        -- end

        -- 角色恢复
        if RangeLogic.bFromStackUI then
            Timer.DelayCall(0, RangeEquipLogic.Reset3DSafehousePlayer)
        else
            RangeEquipLogic.Reset3DSafehousePlayer()
        end
    else
        -- 切HUD
        localCtrl.bKeepLastHUD = false
        localCtrl:SetSafeHouseHUDMode(ESafeHouseHUDMode.None, false)
    end

    -- 最后再修改全局状态
    Server.IrisSafeHouseServer:ResetRangeData(false)

    -- 事件通知
    RangeConfig.evtEnterRange:Invoke(false)

    if RangeLogic.bFromStackUI then
        log("RecoverBlackBoxWithTransition")
        local fLoadFinCallback = function()
            RangeLogic.RecoverBlackBoxWithTransitionLoadFinish()
        end
        Facade.UIManager:RecoverBlackBoxWithTransition(nil, fLoadFinCallback)
    end

    if RangeLogic.ShouldUnloadRange() then
        RangeLogic.UnloadRangeLvls()
    end

    -- 释放对Character的使用
    ItemOperaTool.RemoveUseCharacterBusiness(ItemOperaTool.SafeHouseBusiness.Range)
end

--- 靶场返回改枪台处理 Start -----
function RangeLogic.RecoverBlackBoxWithTransitionLoadFinish()
    RangeLogic.ProcessRecoverBlackBoxWithTransitionLoadFinishByGunsmithEnter()
end

function RangeLogic.ProcessSavePropInfoByGunsmithEnter()
    local bIsGunsmithEnter = RangeLogic.IsGunsmithEnter()
    if not bIsGunsmithEnter then
        return
    end

    local groupID = RangeLogic.GetRangeGroupID()
    local propInfo = RangeLogic.GetRangeWeaponPropInfo(groupID)
    local bIsSameWeapon = RangeLogic.IsSameWeaponWithEnterRange(propInfo)
    Module.Gunsmith:SavePropInfoFromRange(propInfo, groupID)
    Module.Gunsmith:SetIsSameWeaponWithEnterRange(bIsSameWeapon)
end

function RangeLogic.ProcessRecoverBlackBoxWithTransitionLoadFinishByGunsmithEnter()
    local bIsGunsmithEnter = RangeLogic.IsGunsmithEnter()
    if not bIsGunsmithEnter then
        return
    end
    Module.Gunsmith:SavePropInfoFromRange()
end

function RangeLogic.IsGunsmithEnter()
    return isvalid(RangeLogic.enterContext) and isvalid(RangeLogic.enterContext.weaponProp)
end

function RangeLogic.IsSameWeaponWithEnterRange(inPropInfo)
    local bIsGunsmithEnter = RangeLogic.IsGunsmithEnter()
    if not bIsGunsmithEnter then
        return false
    end
    local fGetWeaponID = function(propInfo)
        if isinvalid(propInfo) and propInfo.id and propInfo.id ~= 0 then
            return 0
        end
        return propInfo.id
    end

    local enterWeaponID = fGetWeaponID(RangeLogic.enterContext.weaponProp)
    local inWeaponID = fGetWeaponID(inPropInfo)

    return enterWeaponID == inWeaponID
end

function RangeLogic.GetRangeWeaponPropInfo(groupID)
    local propInfo = RangeLogic.GetCurrentPlayerWeaponPropinfo()
    if groupID == ESlotGroup.Player then
        return RangeLogic._InternalGetSOLRangeWeaponPropInfo(propInfo, groupID)
    end
    return RangeLogic._InternalGetMPRangeWeaponPropInfo(propInfo, groupID)
end

function RangeLogic._InternalGetSOLRangeWeaponPropInfo(propInfo, groupID)
    local bIsNotValid = isinvalid(propInfo)
    if bIsNotValid then
        propInfo = RangeLogic.enterContext.weaponProp
    end

    bIsNotValid = isinvalid(propInfo)
    if bIsNotValid then
        logerror("RangeLogic.GetRangeWeaponPropInfo -- propInfo: ", propInfo, " is not valid")
        return propInfo
    end

    local weaponGUID = propInfo.gid
    propInfo = WeaponAssemblyTool.RemovePropInfoGID(propInfo)
    local itembase = Server.InventoryServer:GetItemByGid(weaponGUID, groupID)
    bIsNotValid = isinvalid(itembase)
    if bIsNotValid then
        propInfo = WeaponAssemblyTool.ResetSkinInfo(propInfo)
        propInfo = WeaponAssemblyTool.ResetPendantInfo(propInfo)
        return propInfo
    end
    RangeLogic.SetPropInfoWeaponGID(propInfo, weaponGUID)
    local serverPropInfo = itembase:GetRawPropInfo()
    WeaponAssemblyTool.OverrideAppearanceInfoByPropInfo(propInfo, serverPropInfo)
    return propInfo
end

function RangeLogic._InternalGetMPRangeWeaponPropInfo(propInfo, groupID)
    local bIsNotValid = isinvalid(propInfo) or (RangeLogic._InternalIsLockedWithMP(propInfo))
    if bIsNotValid then
        propInfo = RangeLogic.enterContext.weaponProp
    end

    bIsNotValid = isinvalid(propInfo)
    if bIsNotValid then
        logerror("RangeLogic.GetRangeWeaponPropInfo -- propInfo: ", propInfo, " is not valid")
        return propInfo
    end
    local weaponGUID = propInfo.gid
    local itembase = Server.InventoryServer:GetItemByGid(weaponGUID, groupID)
    bIsNotValid = isinvalid(itembase)
    if bIsNotValid then
        weaponGUID = RangeLogic._InternalGetMPWeaponGUIDByPropInfo(propInfo, groupID)
    end
    itembase = Server.InventoryServer:GetItemByGid(weaponGUID, groupID)
    bIsNotValid = isinvalid(itembase)
    if bIsNotValid then
        logerror("RangeLogic.GetRangeWeaponPropInfo -- weaponGUID: ", weaponGUID, ", not in Inventory!!!")
        return nil
    end
    propInfo = WeaponAssemblyTool.RemovePropInfoGID(propInfo)
    RangeLogic.SetPropInfoWeaponGID(propInfo, weaponGUID)
    local serverPropInfo = itembase:GetRawPropInfo()
    WeaponAssemblyTool.OverrideAppearanceInfoByPropInfo(propInfo, serverPropInfo)
    return propInfo
end

-- function RangeLogic._InternalSetGUID4PropInfo(curPropInfo, targetPropInfo)
--     if not curPropInfo or not curPropInfo.id or not targetPropInfo or not targetPropInfo.id then
--         return false
--     end

-- end

-- 获取靶场玩家手上武器的propinfo(不包括近战武器)
function RangeLogic.GetCurrentPlayerWeaponPropinfo()
    local currentWeaponItem = RangeEquipLogic.GetCurrentPlayerWeaponItem(true)
    local bIsValid = isvalid(currentWeaponItem) and currentWeaponItem.GetRawPropInfo
    if not bIsValid then
        return nil
    end
    return currentWeaponItem:GetRawPropInfo()
end

function RangeLogic._InternalIsLockedWithMP(propInfo)
    local bIsValid = isvalid(propInfo)
    if not bIsValid then
        return true
    end
    local weaponID = propInfo.id
    local weapon = Server.InventoryServer:GetItemsById(tonumber(weaponID), ESlotGroup.MPApply)
    return table.isempty(weapon)
end

function RangeLogic.SetPropInfoWeaponGID(propInfo, weaponGUID)
    local bIsValid = isvalid(propInfo)
    if not bIsValid then
        return propInfo
    end
    propInfo.gid = weaponGUID
    return propInfo
end

function RangeLogic._InternalGetMPWeaponGUIDByPropInfo(propInfo, groupID)
    local weaponGUID = 0
    local bIsValid = isvalid(propInfo)
    local bIsMP = (groupID == ESlotGroup.MPApply)
    if (not bIsValid) or (not bIsMP) then
        return weaponGUID
    end
    local weaponID = propInfo.id
    local weapon = Server.InventoryServer:GetItemsById(tonumber(weaponID), ESlotGroup.MPApply)
    bIsValid = isvalid(weapon) and (#weapon > 0) and isvalid(weapon[1])
    if not bIsValid then
        return weaponGUID
    end
    local serverPropInfo = weapon[1]:GetRawPropInfo()
    bIsValid = isvalid(serverPropInfo) and serverPropInfo.gid and (serverPropInfo.gid > 0)
    if not bIsValid then
        return weaponGUID
    end
    weaponGUID = serverPropInfo.gid
    return weaponGUID
end

function RangeLogic.GetRangeGroupID()
    if Server.IrisSafeHouseServer.bRangeSOLMode then
        return ESlotGroup.Player
    end
    return ESlotGroup.MPApply
end
--- 靶场返回改枪台处理 end -----

-- 传送
function RangeLogic.Teleport(bEnter)
    local character = InGameController:Get():GetGPCharacter()
    local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)

    -- 如果是从3D特勤处进来的，需要记录下来之前的位置
    if bEnter then
        local fromSafeHouseLoc = character:K2_GetActorLocation()
        fromSafeHouseLoc = UGameplayStatics.RebaseLocalOriginOntoZero(localCtrl, fromSafeHouseLoc)
        RangeLogic.FromSafeHouseLoc = fromSafeHouseLoc
        log("RangeLogic.Teleport record current location before enter", fromSafeHouseLoc.X, fromSafeHouseLoc.Y, fromSafeHouseLoc.Z)
    end

    local PSTag = bEnter and "SafeHouseRangePS" or "SafeHousePS0"
    local outActors
    outActors = UGameplayStatics.GetAllActorsWithTag(localCtrl, PSTag, outActors)
    if outActors:Num() == 1 then
        local startPoint = outActors:Get(0)
        local loc = startPoint:K2_GetActorLocation()
        local rot = startPoint:K2_GetActorRotation()
        loc = UGameplayStatics.RebaseLocalOriginOntoZero(localCtrl, loc)

        -- loc = FVector(30000, 30000, 30000)
        if bEnter then
            -- localCtrl:ExecTeleport(loc)
        else
            if RangeLogic.FromSafeHouseLoc then
                -- loc = RangeLogic.FromSafeHouseLoc
                -- loc = UGameplayStatics.RebaseLocalOriginOntoZero(localCtrl, RangeLogic.FromSafeHouseLoc)
                loc = RangeLogic.FromSafeHouseLoc
            else
                loc = Module.IrisSafeHouse:Get3DSafeHouseDefaultLoc()
            end
        end

        localCtrl:ExecTeleport(loc)

        character:K2_SetActorRotation(rot, false)
    end
end

function RangeLogic.OnProcessRangeManageWeapon()
    RangeEquipLogic.ShowRangeEquipProcess()
end

function RangeLogic.OnStackUIChanged()
    local curView = Facade.UIManager:GetCurrentStackUI()
	local curStackUICount = Facade.UIManager:GetStackUICount()
	if curStackUICount == 0 then
        Facade.GameFlowManager:EnterSubStage(ESubStage.Range)
    else
        if Facade.GameFlowManager:GetCurrentSubStage() == ESubStage.Range then
            Facade.GameFlowManager:LeaveSubStage()
        end
    end
end

function RangeLogic.OnSOLUsedHeroIdChanged()
    local currentHeroId = tonumber(Server.HeroServer:GetCurUsedHeroId())
    local playerState = InGameController:Get():GetPlayerState()
    playerState.HeroID = currentHeroId
end

function RangeLogic.EnterRangeLane(bEnter)
    -- RangeAnalysisLogic.ResetData()

    -- local hudStateManager = UE.HUDStateManager.Get(GetWorld())
    -- if bEnter then
    --     hudStateManager:AddState(UE.GameHUDSate.GHS_SafeHouseRangeInLane, true)
    -- else
    --     hudStateManager:RemoveState(UE.GameHUDSate.GHS_SafeHouseRangeInLane, true)
    -- end

    -- RangeAnalysisLogic.NotifyUpdateView()
end

function RangeLogic.ShouldShowGunsmithEntrance()
    ---@type ItemBase
    local currentWeaponItem = RangeEquipLogic.GetCurrentPlayerWeaponItem(true)
    if not currentWeaponItem then
        return false
    end

    return true
end

function RangeLogic.EnterGunsmith(slotType)
    ---@type ItemBase
    local currentWeaponItem
    if slotType then
        local slot = Server.InventoryServer:GetSlot(slotType, RangeEquipLogic.GetRangeSlotGroup())
        currentWeaponItem = slot:GetEquipItem()
    else
        currentWeaponItem = RangeEquipLogic.GetCurrentPlayerWeaponItem(true)
        slotType = currentWeaponItem and currentWeaponItem.InSlot.SlotType
    end

    if currentWeaponItem then
        -- 需要检查是否是可改的枪
        if currentWeaponItem.itemSubType == ItemConfig.EWeaponItemType.Melee then
            return
        end

        Module.Gunsmith:OpenRangeFromItemBase(currentWeaponItem, RangeEquipLogic.GetRangeSlotGroup(), slotType)
    end
end

function RangeLogic.EnterGunsmithWeaponSelection(weaponSlotType)
    if Server.IrisSafeHouseServer.bRangeSOLMode then
        Module.Gunsmith:OpenRangeInspectorMainUI(RangeEquipLogic.GetRangeSlotGroup(), weaponSlotType)
    else

    end
end

-----------------------------------------------------------------------
--region Private

function RangeLogic._InitRangeHUD()
    -- 切到RangeHUD
    local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
    localCtrl:SetSafeHouseHUDMode(ESafeHouseHUDMode.Range, false)

    -- -- 创建靶场专属的HUD
    -- local hudLayerController = Facade.UIManager:GetLayerControllerByType(EUILayer.HUD)
    -- if hudLayerController then
    --     hudLayerController:ShowHudByID(UIName2ID.RangeAnalysisView)
    --     hudLayerController:ShowHudByID(UIName2ID.SafeHouseInteractorCommon)

    --     if IsHD() then

    --     else
    --         hudLayerController:ShowHudByID(UIName2ID.RangeEquipHUD)
    --     end
    -- end

    -- 一些特殊fix
    -- 1. 有时交互组件会残留（可能跟它的判定有关）
    Module.IrisSafeHouse.Field:ClearAllOperators()
end

function RangeLogic.PostOnGameHudStateChanged()
    log("PostOnGameHudStateChanged")

    local hud = ABaseHUD.GetHUD(GetWorld())
    local localPlayerInfoHuds = hud:GetPanel("LocalPlayerInfo")
    local localPlayerInfoHud = #localPlayerInfoHuds > 0 and localPlayerInfoHuds[1]
    if localPlayerInfoHud then
        log("_SetHUDState set localPlayerInfoHud.bShowTalkingCanvas = false")

        localPlayerInfoHud:Reset()
        localPlayerInfoHud.bShowTalkingCanvas = false
        localPlayerInfoHud:RefreshGVoiceIcon()

        localPlayerInfoHud.wtHelmetStatusItem:SetVisibility(ESlateVisibility.Collapsed)
        localPlayerInfoHud.wtBodyArmorStatusItem:SetVisibility(ESlateVisibility.Collapsed)
    end

    ---@type HUDLayerController
    local hudLayerController = Facade.UIManager:GetLayerControllerByType(EUILayer.HUD)
    local buffAndStaminaViewPC = hudLayerController:GetHudByName("BuffAndStaminaViewPC")
    if buffAndStaminaViewPC then
        buffAndStaminaViewPC.bAlwayseShowHealthTipBox = false
    end
end

function RangeLogic._SetHUDState(bEnterRange)
    local hudStateManager = UE.HUDStateManager.Get(GetWorld())
	if hudStateManager then
		if bEnterRange then
			hudStateManager:RemoveState(UE.GameHUDSate.GHS_GlobalHideAllHUD, false)
			hudStateManager:AddState(UE.GameHUDSate.GHS_SafeHouseRange, false)
			hudStateManager:AddState(UE.GameHUDSate.GHS_SafeHouseRangeInLane, false)
		else
			hudStateManager:AddState(UE.GameHUDSate.GHS_GlobalHideAllHUD, false)
			hudStateManager:RemoveState(UE.GameHUDSate.GHS_SafeHouseRange, false)
			hudStateManager:RemoveState(UE.GameHUDSate.GHS_SafeHouseRangeInLane, false)
		end
	end
end

function RangeLogic._SetController(bEnterRange)
    local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
	if localCtrl then
		localCtrl:ChangeTouchControlMode(bEnterRange)
		if bEnterRange then
			localCtrl:GPSetViewTargetWithBlend(localCtrl:GetPawn(), false, true, 0, EViewTargetBlendFunction.VTBlend_Linear, 0, false)
            localCtrl:SetProcessInputComp(EProcessInputCompReason.EProcessInputReason_Range, true)
        else
			localCtrl:PlayerStopRequest()
            localCtrl:SetProcessInputComp(EProcessInputCompReason.EProcessInputReason_Range, false)
		end
	end

    -- 重置InputAxis
    local inputMgr = localCtrl.GPInputManager
    if inputMgr then
        -- 确保停止开火..
        inputMgr:OnHudInputAction(EInputActionType.EInputAction_RightFireBtnUp, true)
        inputMgr:OnHudInputAction(EInputActionType.EInputAction_LeftFireBtnUp, true)

        inputMgr:OnInputActionMoveForwardReleased()
    end

    -- --bug=********* [【WW】【PC】海外靶场-偶现一直自动向右移动（重新按下右键恢复）](https://tapd.woa.com/r/t?id=*********&type=bug) 
    -- 挪到进出改枪台时开关Player禁止输入
    -- if IsHD() then
    --     if bEnterRange then
    --         localCtrl:SetClientDisableProcessPlayerInput(false, "Range")
    --     else
    --         if Server.IrisSafeHouseServer.bIsInRange then
    --             localCtrl:SetClientDisableProcessPlayerInput(true, "Range")
    --         else
    --             localCtrl:SetClientDisableProcessPlayerInput(false, "Range")
    --         end
    --     end
    -- end
end

function RangeLogic._SetCharacter(bEnterRange)
    local dfmCharacter = Facade.GameFlowManager:GetCharacter()

    local bb = dfmCharacter.Blackboard
    if bb then
        bb:SetIsInRange(bEnterRange) 
    end

	local movementComp = dfmCharacter:GetComponentByClass(UCharacterMovementComponent)
	if movementComp then
		movementComp.Velocity = FVector.ZeroVector
        movementComp:SetDisablePlayerPerformMovement(not bEnterRange, "RangeLogicSetCharacter")
	end

    -- if bEnterRange then
    --     dfmCharacter:SetActorHiddenInGame(false)
    -- else
    --     -- 如果是靶场里打开界面（人还在靶场），不隐藏主角，避免因为不渲染导致动画组件不Tick，骨骼不更新
    --     if Server.IrisSafeHouseServer.bIsInRange then
    --         dfmCharacter:SetActorHiddenInGame(false)
    --     else
    --         dfmCharacter:SetActorHiddenInGame(true)
    --     end
    -- end
    dfmCharacter:SetActorHiddenInGame(not bEnterRange)

    if bEnterRange then
        dfmCharacter:SendFSMTransitionEvent(EGPFSMTransitionEvent.EGPFSMTransitionEvent_EndWeaponInspect, true, false)
        -- dfmCharacter:PlayerWantStand()
    else
        if isvalid(dfmCharacter) then
            -- 取消开镜
            USafeHouseRangeUtil.CancelWeaponZoom(dfmCharacter, true)
        end
    end
end

function RangeLogic:_SetBGM(bEnterRange)
    if bEnterRange then
        -- 关闭局外BGM
        Facade.SoundManager:StopBGM()
    else
        -- 开启局外BGM
        local currentGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
        if currentGameFlow == EGameFlowStageType.SafeHouse then
            Facade.SoundManager:PlaySafeHouseStartBGM()
        elseif currentGameFlow == EGameFlowStageType.Lobby then
            Facade.SoundManager:PlayBattlefieldStartBGM()
        end
    end
end

function RangeLogic._SetSound(bEnterRange)
    if bEnterRange then
        -- 开启局内音效
        Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.IngameSoundOn)
    else
        -- 关闭局内音效
        Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.IngameSoundOff)
    end
end

function RangeLogic._SetConsoleVars(bEnterRange)
    local cmds = {}
    -- 关闭武器合模
    -- table.insert(cmds, string.format("ModularWeapon.MergeFallbackDynamic %d", bEnterRange and 1 or 0))
    -- table.insert(cmds, string.format("ModularWeapon.MeshMerge %d", bEnterRange and 0 or 1))

    -- 打开武器手电筒爆闪
    table.insert(cmds, string.format("fx.PowerFlashFunction %d", bEnterRange and 1 or 0))

    -- 取消屏幕空间裁剪
    table.insert(cmds, string.format("r.CullScreenSizeRatio %f", bEnterRange and 0 or 0.5))

    table.insert(cmds, string.format("r.SeparateTranslucency %f", bEnterRange and 0 or 1))

    for _, cmd in ipairs(cmds) do
        UKismetSystemLibrary.ExecuteConsoleCommand(
            GetGameInstance(),
            cmd,
            nil
        )
    end
end

function RangeLogic._NotifyEnterRange(bEnterRange)
    USafeHouseRangeUtil.NotifyEnterRange(GetWorld(), bEnterRange)
end

function RangeLogic._ReportTGPA(bEnterRange)
    local targetState = bEnterRange and DFMGameDataKeyForStoryState.CommonGame or DFMGameDataKeyForStoryState.Lobby
    UDFMGameGPM.UpdateGameInfo(targetState)
end

function RangeLogic._DebugPrintDisplayInput()
    if IsHD() then
        local DFHDDisplayInputActionMgr = UDFHDDisplayInputActionManager.Get(GetGameInstance())
        local actionHandler = DFHDDisplayInputActionMgr.ActionHandler
        if actionHandler then
            local displayInputStr = actionHandler:GetStringFromActionBindings()
            logerror("RangeLogic._DebugPrintDisplayInput()\n", displayInputStr)
        end
    end
end

--- BEGIN MODIFICATION @ VIRTUOS
function RangeLogic._OnInputTypeChanged(InputType)
    if not IsHD() then
       return
    end

    local inputMonitor = Facade.UIManager:GetInputMonitor()
    -- Hide side scroll box if gamepad active.
    if InputType == EGPInputType.Gamepad then
        if RangeLogic.actionBackHandle then
            inputMonitor:RemoveDisplayActoinBingingForHandle(RangeLogic.actionBackHandle)
            RangeLogic.actionBackHandle = nil
        end
    else
        RangeLogic.actionBackHandle =
        inputMonitor:AddDisplayActionBinding(
        "Back",
        EInputEvent.IE_Pressed,
        RangeLogic.ToggleSettingEntrance,
        self,
        EDisplayInputActionPriority.UI_HUD
        )
    end
end
--- END MODIFICATION

--endregion
-----------------------------------------------------------------------

return RangeLogic