----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

local SystemSettingHDHeroListTopPanel = ui("SystemSettingHDHeroListTopPanel")
local UDFMGameHudDelegates = import("DFMGameHudDelegates")
local USlateBlueprintLibrary = import "SlateBlueprintLibrary"
local HeroHelperTool = require "DFM.StandaloneLua.BusinessTool.HeroHelperTool"
local HeroConfig=require "DFM.Business.Module.HeroModule.HeroConfig"
local HeroDataTable = Facade.TableManager:GetTable("Hero/HeroData")
local SkillHelperTool = require "DFM.StandaloneLua.BusinessTool.SkillHelperTool"
local EConsumeMouseWheel = import "EConsumeMouseWheel"
local BattleHeroSkillIcon = require"DFM.Business.Module.HeroModule.UI.HeroMain.BattleHeroSkillIcon"
local HeroPropItem = require"DFM.Business.Module.HeroModule.UI.HeroMain.HeroPropItem"

local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"
local ButtonIdConfig  = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.ButtonIdConfig"
local ButtonIdEnum = ButtonIdConfig.Enum
local SystemSettingLogic = require "DFM.Business.Module.SystemSettingModule.Logic.SystemSettingLogic"

---@enum EHeroListTopPanelMode 界面复用多用途区分
local EHeroListTopPanelMode  = {
    Normal = 1,
    Recruitment = 2, ---招募专属
}

local ArmedForceDataTable = Facade.TableManager:GetTable("ArmedForceData")

function SystemSettingHDHeroListTopPanel:Ctor()
    -- HeroItem行数
    -- self._CELL_ROW_COUNT = 1
    -- self._PLAY_SHOW_ANIM_DELTA_TIME = 0.05
    -- self._animStartDelayTime = 0.05
    -- self._animDeltaTime = 0.03
    -- self._showAnimFinishList = {}

    -- self._wtProcessBtn = self:Wnd("WBP_DFCommonButtonV1S1", DFCommonButtonOnly)
    -- self._wtProcessBtn:Event("OnClicked", self._OnProcessBtnClicked, self)
    -- self._wtProcessBtn:Event("OnDeClicked", self._OnProcessBtnDeClicked, self)

    -- self._wtCanvasHeroList = self:Wnd("DFCanvasPanel_HeroList", UIWidgetBase)
    -- self.heroIdList = {}
    -- self._mapIdx2ItemWidget = setmetatable({}, weakmeta_value)
    -- self._wtWaterFallList = UIUtil.WndWaterfallScrollBox(self, "DFWaterfallBox_Hero", self._OnGetItemCount, self._OnProcessItemWidget)
    self._wtScrollGridList = UIUtil.WndScrollGridBox(self, "DFScrollGridBox_Hero", self._OnGetItemCount, self._OnProcessItemWidget)


    self._wtTbHeroName = self:Wnd("DFTextBlock_hName", UITextBlock)
    self._wtTbArmTitle = self:Wnd("DFTextBlock_armTitle", UITextBlock)
    self._wtImgArmIcon = self:Wnd("DFMImage_armIcon", UIImage)

    --- 技能相关
    self._wtSkillIcon1 = self:Wnd("HeroSkill1", BattleHeroSkillIcon)
    self._wtSkillIcon2 = self:Wnd("HeroSkill2", BattleHeroSkillIcon)
    self._wtHeroPropItem1 = self:Wnd("WBP_Hero_PropsItem1", HeroPropItem)
    self._wtHeroPropItem2 = self:Wnd("WBP_Hero_PropsItem2", HeroPropItem)
    self._cachedHeroId = nil


    self:AddLuaEvent(Facade.UIManager.Events.evtStackUIChanged, self._OnCloseHeroListPanel, self)


    -- 使得此UI的进入动画播完之后才能进行隐藏
    self.bInEnd = false

    if DFHD_LUA == 1 then
        --PC端统一禁用掉用户的滚轮滑动
        -- self._wtWaterFallList:SetConsumeMouseWheel(EConsumeMouseWheel.Never)
        -- self._wtScrollGridList :SetConsumeMouseWheel(EConsumeMouseWheel.Never)
    else
        -- self._wtWaterFallList:SetConsumeMouseWheel(EConsumeMouseWheel.WhenScrollingPossible)
        -- self._wtScrollGridList :SetConsumeMouseWheel(EConsumeMouseWheel.WhenScrollingPossible)
    end

	self:Wnd("wtGlobalText", UITextBlock):SetText(Module.SystemSetting.Config.Loc.HDSetting.HeroSpecificInput.GlobalTitle)
    self._wtHeroPanel = self:Wnd("wtHeroPanel", UIWidgetBase)
    self._wtGlobalPanel = self:Wnd("wtGlobalPanel", UIWidgetBase)

	self._wtExitBtn = self:Wnd("wtExitBtn", UIButton)
	self._wtExitBtn:Event("OnClicked", self._OnCloseHeroListPanel, self)

	self._hoveredHero = nil
    self._armedForceGroups = {}

    local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
	self._UltimateSkillFunc = armedForceMode == EArmedForceMode.SOL and SkillHelperTool.GetSOLUltimateSkillID or
		SkillHelperTool.GetMPUltimateSkillID
	self._SupportSkillFunc = armedForceMode == EArmedForceMode.SOL and SkillHelperTool.GetSOLSupportSkillID or
		SkillHelperTool.GetMPSupportSkillID
	self._ActiveSkillFunc = armedForceMode == EArmedForceMode.SOL and SkillHelperTool.GetSOLActiveSkillID or
		SkillHelperTool.GetMPActiveSkillID
	self._PassiveSkillFunc = armedForceMode == EArmedForceMode.SOL and SkillHelperTool.GetSOLPassiveSkillID or
		SkillHelperTool.GetMPPassiveSkillID
end

function SystemSettingHDHeroListTopPanel:OnInitExtraData(herodesc, fOnSelectedHero)
    self._herodesc = herodesc
    self._fOnSelectedHero = fOnSelectedHero
end

function SystemSettingHDHeroListTopPanel:OnOpen()
    self._wtScrollGridList:RefreshAllItems()
end

-- ApplyGeneral 与 Prev02按键相同
function SystemSettingHDHeroListTopPanel:_RegisterHeroPanelShortCuts(bForHero)
    local blockPrev02 = false
	if bForHero and self._hoveredHero then
        if SystemSettingLogic.HDIsHeroHasSetting(self._hoveredHero) then
            Module.CommonBar:SetBottomBarTempInputSummaryList({
                {actionName = "Back",func = self._OnCloseHeroListPanel, caller = self, bUIOnly = false, bHideIcon = false},
                {actionName = "SelectHero1", caller = self, bUIOnly = true, bHideIcon = false},
                {actionName = "ApplyGeneral",func = self._OnApplyGlobal, caller = self, bUIOnly = false, bHideIcon = false},
                {actionName = "Reset",func = self._OnReset, caller = self, bUIOnly = false, bHideIcon = false},
            }, true, true)
            blockPrev02 = false
        else
            Module.CommonBar:SetBottomBarTempInputSummaryList({
                {actionName = "Back",func = self._OnCloseHeroListPanel, caller = self, bUIOnly = false, bHideIcon = false},
                {actionName = "SelectHero1", caller = self, bUIOnly = true, bHideIcon = false},
                {actionName = "ApplyGeneral",func = self._OnApplyGlobal, caller = self, bHideIcon = false, disable = true},
                {actionName = "Reset",func = self._OnReset, caller = self, bUIOnly = false, bHideIcon = false},
            }, true, true)
            blockPrev02 = true
        end
	else
		Module.CommonBar:SetBottomBarTempInputSummaryList({
			{actionName = "Back",func = self._OnCloseHeroListPanel, caller = self, bUIOnly = false, bHideIcon = false},
			{actionName = "SelectHero1", caller = self, bUIOnly = true, bHideIcon = false},
            {actionName = "ApplyGeneral",func = self._OnApplyGlobal, caller = self, bHideIcon = false, disable = true},
            {actionName = "Reset",func = self._OnReset, caller = self, bHideIcon = false, disable = true},
		}, true, true)
        blockPrev02 = true
    end

    -- ApplyGeneral和Prev02都是A键，这时要确保Prev02不会生效
    if blockPrev02 then
        if not self._handlePrev02 then
            self._handlePrev02 = self:AddInputActionBinding("Prev02", EInputEvent.IE_Pressed, self._DoNothing, self, EDisplayInputActionPriority.UI_Tip)
        end
    else
        if self._handlePrev02 then
            self:RemoveInputActionBinding(self._handlePrev02)
            self._handlePrev02 = nil
        end
    end
end

function SystemSettingHDHeroListTopPanel:_DoNothing()
    -- 占住快捷键
end

function SystemSettingHDHeroListTopPanel:_OnCloseHeroListPanel(heroId)
    if self._fOnSelectedHero then
		self._fOnSelectedHero(heroId)
	end
	self._registerShortCuts = 0
    Facade.UIManager:CloseUI(self)
end

function SystemSettingHDHeroListTopPanel:_OnApplyGlobal()
	if self._hoveredHero then
        local fApplyGlobal = function()
            SystemSettingLogic.HDHeroSettingConfigApplyGlobal(self._hoveredHero)
            for _, widget in pairs(self._armedForceGroups) do
                widget:RefreshView()
            end
            self._herodesc:Init()
        end
        local fCancel = function()
        end
        Module.CommonTips:ShowConfirmWindow(Module.SystemSetting.Config.Loc.HDSetting.HeroSpecificInput.ApplyGlobal, CreateCallBack(fApplyGlobal, self), CreateCallBack(fCancel, self))
	end
end

function SystemSettingHDHeroListTopPanel:_OnReset()
	if self._hoveredHero then
        local fReset = function()
            SystemSettingLogic.HDHeroSettingConfigResetToDefault(self._hoveredHero)
            for _, widget in pairs(self._armedForceGroups) do
                widget:RefreshView()
            end
            self._herodesc:Init()
        end
        local fCancel = function()
        end
        Module.CommonTips:ShowConfirmWindow(Module.SystemSetting.Config.Loc.HDSetting.HeroSpecificInput.Reset, CreateCallBack(fReset, self), CreateCallBack(fCancel, self))
	end
end

function SystemSettingHDHeroListTopPanel:_UnregisterHeroPanelShortCuts()
	if self._hSnapshot then
		Module.CommonBar:ApplyBottomBarTempInputSummarySnapshot(self._hSnapshot, true)
		self._hSnapshot = nil
	end
end

function SystemSettingHDHeroListTopPanel:OnShowBegin()
    local curShowHeroId = Module.SystemSetting.Field:GetCurrentHero()
    self:_FreshHeroSkillInfo(curShowHeroId)
    -- self._showAnimFinishList = {}
	self._hSnapshot = Module.CommonBar:TakeBottomBarTempInputSummarySnapshot()
    self:_RegisterHeroPanelShortCuts(false)

    -- 屏蔽设置界面按键
    self._handlePrev01 = self:AddInputActionBinding("Prev01", EInputEvent.IE_Pressed, self._DoNothing, self, EDisplayInputActionPriority.UI_Tip)
    self._handleNext01 = self:AddInputActionBinding("Next01", EInputEvent.IE_Pressed, self._DoNothing, self, EDisplayInputActionPriority.UI_Tip)
    self._handleNext02 = self:AddInputActionBinding("Next02", EInputEvent.IE_Pressed, self._DoNothing, self, EDisplayInputActionPriority.UI_Tip)

    --self:BindBackAction(self._OnBackBtnClicked, self)

    -- -- BEGIN MODIFICATION @ VIRTUOS : 添加导航组
    -- if IsHD() then
    --     self:_EnableNavigation(true)
    -- end
    -- -- END MODIFICATION
end

function SystemSettingHDHeroListTopPanel:_OnBackBtnClicked()
    if self.bInEnd then
        Facade.UIManager:CloseUI(self)
        self.bInEnd = false
    end
end

function SystemSettingHDHeroListTopPanel:OnHideBegin()
    self._cachedHeroId = nil
    -- self._showAnimFinishList = {}
	self:_UnregisterHeroPanelShortCuts()

    -- 屏蔽设置界面按键
    self:RemoveInputActionBinding(self._handlePrev01)
    if self._handlePrev02 then
        self:RemoveInputActionBinding(self._handlePrev02)
        self._handlePrev02 = nil
    end
    self:RemoveInputActionBinding(self._handleNext01)
    self:RemoveInputActionBinding(self._handleNext02)

    -- if self._fOnHeroPanelHideBegin then
    --     self._fOnHeroPanelHideBegin()
    -- end
    --self:UnBindBackAction()

    -- -- BEGIN MODIFICATION @ VIRTUOS :
    -- if IsHD() then
    --     self:_EnableNavigation(false)
    -- end
    -- -- END MODIFICATION
end

function SystemSettingHDHeroListTopPanel:OnClose()
end


function SystemSettingHDHeroListTopPanel:OnAnimFinished(anim)
    -- if anim ==  self.WBP_Hero_ListPop_in then
    --     self.bInEnd = true
    -- end
end

function SystemSettingHDHeroListTopPanel:_OnHandleMouseButtonUpEvent(mouseEvent)
    -- local sceenPos = mouseEvent:GetScreenSpacePosition()
    -- local geometry = self._wtCanvasHeroList:GetCachedGeometry()
    -- local isUnder = USlateBlueprintLibrary.IsUnderLocation(geometry, sceenPos)
    -- if not isUnder then
    --     if self.bInEnd then
    --         Facade.UIManager:CloseUI(self)
    --         self.bInEnd = false
    --     end
    -- end
end

function SystemSettingHDHeroListTopPanel:_OnGetItemCount()
    local count = 0
    for _ in pairs(ArmedForceDataTable) do count = count + 1 end
    return count + 1
end

function SystemSettingHDHeroListTopPanel:_OnProcessItemWidget(position, itemWidget)
    local dataIdx = position + 1

    local function onSelected(heroId)
        if self._OnCloseHeroListPanel then
            self:_OnCloseHeroListPanel(heroId)
        end
        Facade.UIManager:CloseUI(self)
    end

    local function onHover(heroId, bIn)
		if bIn then
			self:_FreshHeroSkillInfo(heroId)
		else
			self:_FreshHeroSkillInfo(Module.SystemSetting.Field:GetCurrentHero())
		end

		self._hoveredHero = heroId
		self:_RegisterHeroPanelShortCuts(bIn)
    end

    local function getIsConfiged(heroId)
        if heroId == nil or heroId == "0" then
            return false
        end
        return SystemSettingLogic.HDIsHeroHasSetting(heroId)
    end

    -- 0~4, 0是全局，1~4是ArmedForceId
    itemWidget:InitData(position, onSelected, onHover, getIsConfiged)
    table.insert(self._armedForceGroups, itemWidget)
end


function SystemSettingHDHeroListTopPanel:_OnProcessBtnClicked()
    -- if self._curHeroListTopPanelMode == EHeroListTopPanelMode.Recruitment then
    --     local curShowHeroId = Module.Hero:GetCurShowHeroId()
    --     local curShowRecruitHeroId = Module.Hero:GetCurShowRecruitmentHeroId()
    --     Module.Hero:SetCurShowRecruitmentHeroId(curShowHeroId)
    --     Facade.UIManager:CloseUI(self)
    --     -- if tonumber(curShowHeroId)  ~= curShowRecruitHeroId then
    --     -- else
    --     -- end
    -- else
    --     local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
    --     if armedForceMode == EArmedForceMode.SOL then
    --         --- TODO 暂无经分
    --     else
    --         LogAnalysisTool.SignButtonClicked(ButtonIdEnum.MPUseHeroAsBattle)
    --     end
    --     local curShowHeroId = Module.Hero:GetCurShowHeroId()
    --     local curHeroId = Server.HeroServer:GetCurUsedHeroId()
    --     if curShowHeroId ~= curHeroId then
    --         Module.Hero:UseHeroById(curShowHeroId)
    --     else
    --         Facade.UIManager:CloseUI(self)
    --     end
    -- end

end

function SystemSettingHDHeroListTopPanel:_OnProcessBtnDeClicked()
    -- local curShowHeroId = Module.Hero:GetCurShowHeroId()
    -- local curHeroId = Server.HeroServer:GetCurUsedHeroId()
    -- if curShowHeroId ~= curHeroId then
    --     Module.Hero:UseHeroById(curShowHeroId)
    -- else
    --     Facade.UIManager:CloseUI(self)
    -- end
end

function SystemSettingHDHeroListTopPanel:_FreshHeroSkillInfo(heroId)
    if self._cachedHeroId ~= heroId then
        local heroInfo = HeroDataTable[tostring(heroId)]
        if heroInfo then
			self._wtHeroPanel:SetVisibility(ESlateVisibility.HitTestInvisible)
			self._wtGlobalPanel:SetVisibility(ESlateVisibility.Collapsed)
            self._wtTbHeroName:SetText(heroInfo.Name)
            --self._wtTbHeroName:SetColorAndOpacity(Facade.ColorManager:GetSlateColorByRowName("C001"))
            self._wtTbArmTitle:SetText(heroInfo.Title)
            self._wtImgArmIcon:AsyncSetImagePath(heroInfo.ExpertIcon)
            -- self._wtImgHeroIcon:AsyncSetImagePath(heroInfo.Icon)

            local armedForceInfo = ArmedForceDataTable[tostring(heroInfo.armedForceId)]
            self._wtImgArmIcon:AsyncSetImagePath(armedForceInfo.Icon)
            self._wtImgArmIcon:SetColorAndOpacity(armedForceInfo.RedeployHeroViewColor)
            local textColor = Facade.ColorManager:GetSlateColorByRowName(armedForceInfo.RedeployHeroViewColorType)
            self._wtTbArmTitle:SetColorAndOpacity(textColor)
			self:_RefreshSkillAndArmPropInfo(heroId)
        else
			self._wtGlobalPanel:SetVisibility(ESlateVisibility.HitTestInvisible)
			self._wtHeroPanel:SetVisibility(ESlateVisibility.Collapsed)
        end

        self._cachedHeroId = heroId
    end
end


function SystemSettingHDHeroListTopPanel:_RefreshSkillAndArmPropInfo(heroId)
	if heroId == "" then
		return
	end

    -- local rawData = Server.HeroServer:GetCurModeExpertDataByHeroId(heroId)
    -- if rawData then
        if SkillHelperTool.CheckIsSkillDataValid(heroId) then
            local fSkillIconClickCallback = function (SkillId,Idx)
            end
            local PassiveSkillId = self._PassiveSkillFunc(heroId)
            self._wtSkillIcon1:SetExtraData(PassiveSkillId, function ()
                fSkillIconClickCallback(PassiveSkillId, 1)
            end)
            self._wtSkillIcon1:SetSkillText(SkillHelperTool.GetSkillNameById(PassiveSkillId), SkillHelperTool.GetSkillBuffNameById(PassiveSkillId))

            local armId = HeroHelperTool.GetHeroCategoryId(heroId)

			local armedForceInfo = ArmedForceDataTable[tostring(armId)]
            self._wtSkillIcon2:SetIconColor(armedForceInfo.RedeployHeroViewColor)
            local textColor = Facade.ColorManager:GetSlateColorByRowName(armedForceInfo.RedeployHeroViewColorType)
            self._wtSkillIcon2:SetTextColor(textColor)

            local UltimateSkillId = self._UltimateSkillFunc(heroId)
            self._wtSkillIcon2:SetExtraData(UltimateSkillId, function ()
                fSkillIconClickCallback(UltimateSkillId, 2)
            end)
            self._wtSkillIcon2:SetSkillText(SkillHelperTool.GetSkillNameById(UltimateSkillId), SkillHelperTool.GetSkillBuffNameById(UltimateSkillId))


            local fHeroPropIconClickCallback = function (SkillId,Idx)
            end
            local SkillId = self._SupportSkillFunc(heroId)
            self._wtHeroPropItem1:SetExtraData(SkillId, function ()
                fHeroPropIconClickCallback(SkillId,3)
            end )
            self._wtHeroPropItem1:SetSkillText(SkillHelperTool.GetSkillNameById(SkillId),SkillHelperTool.GetSkillBuffNameById(SkillId))

            local SkillId2 = self._ActiveSkillFunc(heroId)
            self._wtHeroPropItem2:SetExtraData(SkillId2, function ()
                fHeroPropIconClickCallback(SkillId2,4)
            end )
            self._wtHeroPropItem2:SetSkillText(SkillHelperTool.GetSkillNameById(SkillId2),SkillHelperTool.GetSkillBuffNameById(SkillId2))
        else
            logerror('SystemSettingHDHeroListTopPanel:_RefreshSkillAndArmPropInfo(heroId) - invalid heroId' .. heroId)
        end
    -- end
end
---------------------------------------------------------------------------------
--- ProcessBtn刷新 - 跟随干员
---------------------------------------------------------------------------------
function SystemSettingHDHeroListTopPanel:_BuildHeroIdList()
    -- self.heroIdList = {}
    -- if self._curHeroListTopPanelMode == EHeroListTopPanelMode.Normal then
    --     for _, v in pairs(Server.HeroServer:GetHeroData()) do
    --         if self._needFlitArmId == 0 then
    --             table.insert(self.heroIdList, v.hero_id)
    --         else
    --             local vHeroData = HeroDataTable[tostring(v.hero_id)]
    --             if self._needFlitArmId == vHeroData.ArmedForceID then
    --                 table.insert(self.heroIdList, v.hero_id)
    --             end
    --         end
    --     end
    -- elseif self._curHeroListTopPanelMode == EHeroListTopPanelMode.Recruitment then
    --     for _, v in pairs(Server.HeroServer:GetHeroData()) do
    --         if self._needFlitArmId == 0 then
    --             if HeroHelperTool.IsRecruitableHero(v.hero_id) and not v.is_unlock then
    --                 table.insert(self.heroIdList, v.hero_id)
    --             end
    --         else
    --             local vHeroData = HeroDataTable[tostring(v.hero_id)]
    --             if self._needFlitArmId == vHeroData.ArmedForceID and HeroHelperTool.IsRecruitableHero(v.hero_id) and not not v.is_unlock then
    --                 table.insert(self.heroIdList, v.hero_id)
    --             end
    --         end

    --     end
    -- end

    -- table.sort(self.heroIdList, function(a, b)
    --     local heroInfoA = HeroDataTable[tostring(a)]
    --     local heroInfoB = HeroDataTable[tostring(b)]
    --     if heroInfoA.ArmedForceID == heroInfoB.ArmedForceID then
    --         return heroInfoA.ExpertID < heroInfoB.ExpertID
    --     else
    --         return heroInfoA.ArmedForceID < heroInfoB.ArmedForceID
    --     end
    -- end)
end

-- BEGIN MODIFICATION @ VIRTUOS : 添加导航组
function SystemSettingHDHeroListTopPanel:_EnableNavigation(bEnable)
    -- if not IsHD() then
    --     return
    -- end

    -- if bEnable then
    --     self._wtNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtScrollGridList, self, "Hittest")
    --     self._wtNavGroup:AddNavWidgetToArray(self._wtScrollGridList)
    --     self._wtNavGroup:MarkIsStackControlGroup()
    --     WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup)

    --     -- 当前页面需要A键点击
    --     WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default, self)
    -- else
    --     WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    --     WidgetUtil.RemoveNavigationGroup(self)
    -- end
end
-- END MODIFICATION

return SystemSettingHDHeroListTopPanel