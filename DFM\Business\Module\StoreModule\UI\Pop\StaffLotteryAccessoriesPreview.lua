----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMStore)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
-- END MODIFICATION

---@class StaffLotteryAccessoriesPreview : LuaUIBaseView
local StaffLotteryAccessoriesPreview = ui("StaffLotteryAccessoriesPreview")
local StoreConfig = Module.Store.Config
local HeroFashionDataTable = Facade.TableManager:GetTable("Hero/HeroFashionData")
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"

function StaffLotteryAccessoriesPreview:Ctor()
    self._wtTitleImg = self:Wnd("DFImage_Title", UIImage) -- 文字图片，调用一个蓝图函数，传奖池id过去，然后需要换图时，重构同学修改蓝图就可以

    self._wtAccessoriesNameTxt = self:Wnd("wtTextWeaponName", UITextBlock) -- 配件名称

    self._wtQualityIconImg = self:Wnd("wtQualityIcon", UIImage) -- 品质图标
    
    self._wtAccessoriesDescTxt = self:Wnd("wtWeaponDesc", UITextBlock) -- 配件描述信息
    
    self._wtHeroSkinWidget = self:Wnd("WBP_Store_PacksItem", UIWidgetBase) -- 角色皮肤控件

    self._wtWaterFallList = UIUtil.WndWaterfallScrollBox(self, "wtWaterfallView", self._OnGetItemCount, self._OnProcessItemWidget) -- 配件列表

	self._executionBG = self:Wnd("WBP_Common_ScaleBg", UIWidgetBase)

    --兵种道具效果预览按钮
    self._wtPreviewHeroItemBtn = self:Wnd("WBP_DFCommonButtonV1S4", DFCommonButtonOnly)
    self._wtPreviewHeroItemBtn:Collapsed()
    self._wtPreviewHeroItemBtn:Event("OnClicked", self._PreviewHeroItemVideo, self)

    --跳转到涂装定制
    self._wtHeroItemCustomBtn = self:Wnd("WBP_DFCommonButtonV1S3", DFCommonButtonOnly)
    self._wtHeroItemCustomBtn:Collapsed()
    self._wtHeroItemCustomBtn:Event("OnClicked", self._JumpToHeroItemCustomPanel, self)
    self._wtHeroItemCustomBtn:BP_SetMainTitle(Module.Hero.Config.Loc.HeroPropsCustomize)

    --涂装定制提示文本
    self._wtWBP_AssemblyMapNeed = self:Wnd("wtWBP_AssemblyMapNeed", UIWidgetBase)


    self._wtPlatformPaddingBox0 = self:Wnd("PlatformPaddingBox_0", UIWidgetBase) -- 图片文字根节点
    self._wtPlatformPaddingBox1 = self:Wnd("PlatformPaddingBox_1", UIWidgetBase) -- 奖励详情根节点
    self._wtPlatformPaddingBox2 = self:Wnd("PlatformPaddingBox_2", UIWidgetBase) -- itemlist根节点

    --视频专用
    ---@type CommonVideoComponent
    self._mediaComponent = self:Wnd("WBP_CommonVideoComponent", CommonVideoComponent)
    self._mediaComponent:InitComponent(false)

    self:AddLuaEvent(StoreConfig.evtStoreProductViewBundleItemClick, self._OnAccessoriesClicked, self)

    -- 返回按钮
    Module.CommonBar:RegStackUITopBarTitle(self, StoreConfig.Loc.StaffLotteryAccessoriesPreviewTitle)
    -- 隐藏货币栏    
    Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.HideInfo)
    self.AccessoriesItemIDTable = {}
    if IsHD() then
        self._RotationVec = FVector2D(0, 0)
        self._WeaponRotationZoom = 600
        self._WeaponScaleZoom = 30
    end
end

function StaffLotteryAccessoriesPreview:_OnGetItemCount()
    return #self.AccessoriesItemIDTable
end

function StaffLotteryAccessoriesPreview:_OnProcessItemWidget(position, itemWidget)
    local AccessoriesItemID = self.AccessoriesItemIDTable[position]
    local goodInfo = {id = AccessoriesItemID, count = 1}
    itemWidget:SetItemInfo(position, goodInfo, self.currentSelectedAccessoriesIndex )
end

function StaffLotteryAccessoriesPreview:OnInitExtraData(fashionId, lotteryId, customtitleimage)
    if not fashionId then
        logerror("StaffLotteryAccessoriesPreview:OnInitExtraData() error, fashionId is nil")
        Facade.UIManager:CloseUI(self)
        return
    end
    self.lotteryId = lotteryId
    -- 获取角色皮肤数据
    self.rewardItemId = fashionId
   
    self.FashionData = HeroFashionDataTable[tostring(self.rewardItemId)]
    self._wtPlatformPaddingBox1:Hidden()
    self.customtitleimage = customtitleimage

    -- 设置文字图片
    local titleImgPath = "Texture2D'/Game/UI/UIAtlas/Texture/SkinWordArt/SkinWordArt_01.SkinWordArt_01'" -- 蚀金玫瑰
    if self.rewardItemId == 30000060001 then -- 蚀金玫瑰
        titleImgPath = "Texture2D'/Game/UI/UIAtlas/Texture/SkinWordArt/SkinWordArt_01.SkinWordArt_01'"
    elseif self.rewardItemId == 30000050002 then -- 铁面判官
        titleImgPath = "Texture2D'/Game/UI/UIAtlas/Texture/SkinWordArt/SkinWordArt_02.SkinWordArt_02'" -- 铁面判官
    elseif self.rewardItemId == 30000050009 then -- 街头之王
        titleImgPath = "Texture2D'/Game/UI/UIAtlas/Texture/SkinWordArt/SkinWordArt_22.SkinWordArt_22'" -- 街头之王
    else
        if self.customtitleimage ~= nil then
            titleImgPath = self.customtitleimage
        end
	end
    if self.FashionData and self.FashionData.BrandIcon then
        self._wtTitleImg:AsyncSetImagePath(self.FashionData.BrandIcon)
    end
end

function StaffLotteryAccessoriesPreview:OnShowBegin() 
    
    self._displayCtrlActor = Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.HallHero)

     -- 获取配件数据
     local AccessoriesItemIDs = nil
     
     if self.FashionData and self.FashionData.HeroItemIDs then
         AccessoriesItemIDs = self.FashionData.HeroItemIDs
     end
     if not AccessoriesItemIDs then -- 没有配件数据
         logerror("StaffLotteryAccessoriesPreview:OnInitExtraData() error, HeroItemIDs is nil")
         Facade.UIManager:CloseUI(self)
         return
     end
     for _, itemID in pairs(AccessoriesItemIDs or {}) do
         table.insert(self.AccessoriesItemIDTable, itemID)
     end
    -- 设置角色皮肤信息
    self.bSelectedFashion = false -- 是否选中角色皮肤
    self:SetFashionInfo(self.bSelectedFashion)
    -- 设置配件列表信息
    self.currentSelectedAccessoriesIndex = 1 -- 默认选中第一个配件
    self._wtWaterFallList:RefreshAllItems()
    -- 设置选中配件的信息
    self:_ShowSelectedAccessoriesInfo()

    -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
    if IsHD() then
        self:_EnableGamepadFeature(true)
    end
    -- END MODIFICATION
end

function StaffLotteryAccessoriesPreview:OnShow()
    self:RegisterEvents()
end

function StaffLotteryAccessoriesPreview:RegisterEvents()
    self:AddLuaEvent(StoreConfig.evtStoreProductViewBundleItemClick, self._OnAccessoriesClicked, self)
end
function StaffLotteryAccessoriesPreview:UnregisterEvents()
    self:RemoveLuaEvent(StoreConfig.evtStoreProductViewBundleItemClick)
end


-- 设置角色皮肤信息
function StaffLotteryAccessoriesPreview:SetFashionInfo(bSelectedFashion)
    -- 设置角色皮肤信息
    if self.rewardItemId then
        self.fashionGoodInfo = {id = self.rewardItemId, count = 1}
        self._wtHeroSkinWidget:SetItemInfo(1, self.fashionGoodInfo, bSelectedFashion and 1 or 0)

        self:_EnableTouchedHideApperance(bSelectedFashion) -- 监听皮肤旋转事件

        -- 设置描述信息    
        local itemData = ItemBase:New(self.rewardItemId)
        -- 设置品质图标
        if itemData then
            self._wtQualityIconImg:AsyncSetImagePath(Module.Collection.Config.QualityIconMapping[itemData.quality])
            self._wtQualityIconImg:SetColorAndOpacity(ItemConfigTool.GetItemQualityLinearColor(itemData.quality))
        end
        -- 设置配件名字
        if itemData then
            self._wtAccessoriesNameTxt:SetText(itemData.name)
        end
        -- 设置配件描述
        if self.FashionData and self.FashionData.Desc then
            self._wtAccessoriesDescTxt:SetText(self.FashionData.Desc)
        end
        -- 显示配件信息根节点    
        self._wtPlatformPaddingBox1:Visible()

        -- self:ShowRewardFashion(self.rewardItemId)
        -- 隐藏处决相关内容
        self._mediaComponent:Stop()
		self._executionBG:Collapsed()
    end
end

-- 展示所选中的配件信息
function StaffLotteryAccessoriesPreview:_ShowSelectedAccessoriesInfo()
    local AccessoriesItemID = self.AccessoriesItemIDTable[self.currentSelectedAccessoriesIndex]
    if not AccessoriesItemID then
        AccessoriesItemID = self.rewardItemId
        -- return
    end
    self:_EnableTouchedHideApperance(false)

    local itemData = ItemBase:New(AccessoriesItemID)
    self._wtPreviewHeroItemBtn:Collapsed()
    -- 设置品质图标
    if itemData then
        self._wtQualityIconImg:AsyncSetImagePath(Module.Collection.Config.QualityIconMapping[itemData.quality])
        self._wtQualityIconImg:SetColorAndOpacity(ItemConfigTool.GetItemQualityLinearColor(itemData.quality))
    end
    -- 设置配件名字
    if itemData then
        self._wtAccessoriesNameTxt:SetText(itemData.name)
    end
    -- 设置配件描述
    if self.FashionData and self.FashionData.Desc then
        self._wtAccessoriesDescTxt:SetText(self.FashionData.Desc)
    end
    -- 角色时装
    if itemData and itemData.itemMainType == EItemType.Fashion then -- 角色时装
        self:ShowRewardFashion(itemData.id)
    end
    -- 显示配件模型
    if itemData and itemData.itemMainType == EItemType.HeroAccessory and itemData.itemSubType == EHeroAccessroy.Execution then -- 处决
        self._executionBG:Visible()
		self._mediaComponent:Play(itemData.id)
	else
        self._mediaComponent:Stop()
		self._executionBG:Collapsed()
    end
    if itemData and itemData.itemMainType == EItemType.HeroAccessory and itemData.itemSubType == EHeroAccessroy.SoldierProp then -- 兵种道具
        self:ShowRewardAccessories(itemData)

        if Module.Hero:DoesHeroItemHasVideo(itemData.id) then
            self._wtPreviewHeroItemBtn:Visible()
        else
            self._wtPreviewHeroItemBtn:Collapsed()
        end
    end

    local bIsMystical =  Server.HeroServer:GetHeroPropsMysticalLotteryData(itemData.id)
	if bIsMystical then
        local bIsUnlocked = Server.HeroServer:IsHeroFashionUnlocked(self.rewardItemId)
        self._wtHeroItemCustomBtn:SelfHitTestInvisible()
        self._wtHeroItemCustomBtn:SetIsEnabledStyle(bIsUnlocked)
        if bIsUnlocked then
            self._wtWBP_AssemblyMapNeed:Collapsed()
        else
            self._wtWBP_AssemblyMapNeed:Visible()
        end
	else
		self._wtHeroItemCustomBtn:Collapsed()
        self._wtWBP_AssemblyMapNeed:Collapsed()
	end

    -- 显示配件信息根节点    
    self._wtPlatformPaddingBox1:Visible()

    -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
    if IsHD() then
        self:_InitShortcuts()
    end
    -- END MODIFICATION
end
-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function StaffLotteryAccessoriesPreview:OnHide()
    self:UnregisterEvents()
    self.AccessoriesItemIDTable = {}
    self._mediaComponent:Stop()
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.OperatorItem, "SetCurrentSchemeId", "None")
    -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
    if IsHD() then 
        self:_EnableGamepadFeature(false)
        self:_RemoveShortcuts()
    end
    -- END MODIFICATION
end

-- 配件点击事件
function StaffLotteryAccessoriesPreview:_OnAccessoriesClicked(index, goodInfo)
    if goodInfo.id == self.fashionGoodInfo.id then -- 点击的是角色皮肤    
        self.currentSelectedAccessoriesIndex = 0
        self.bSelectedFashion = true
    else -- 点击的是配件    
        self.currentSelectedAccessoriesIndex = index
        self.bSelectedFashion = false
    end
    -- 更新列表显示
    self._wtWaterFallList:RefreshAllItems()
    -- 设置角色皮肤选中状态    
    self:SetFashionInfo(self.bSelectedFashion)
    -- 展示所选中的配件信息
    self:_ShowSelectedAccessoriesInfo()
end


function StaffLotteryAccessoriesPreview:ShowRewardFashion(itemId)
    local fAllLevelFinishCallback = function()
        self._displayCtrlActor = Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.HallHero)
        Facade.UIManager:CommitTransition(false)
        self.SetCharacterSuitAvatarWithSequence = "State2"
        local HeroId = Server.HeroServer:GetBelongedHeroIdByHeroFashionId(tostring(itemId))
        Module.Hero:PlayCharacterAnimSeqByFashionId(tostring(HeroId), itemId, true, self.SetCharacterSuitAvatarWithSequence, true)
        --设置当前展示的角色
        Module.Hero:SetCurShowHeroById(HeroId)
    end
   
    Module.Store:EnterSubLevel(itemId, ESubStage.HallHero, fAllLevelFinishCallback)
end


function StaffLotteryAccessoriesPreview:ShowRewardAccessories(item)
    local fAllLevelFinishCallback = function()     
        self._displayCtrlActor = Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.OperatorItem)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.OperatorItem, "SetDisplayType", "Main")
        local AccessoriesItemID = self.AccessoriesItemIDTable[self.currentSelectedAccessoriesIndex]
        if AccessoriesItemID then
            local curHeroPropsInfo =  Server.HeroServer:GetCurHeroPropsFashionInfo(tonumber(AccessoriesItemID))
            if curHeroPropsInfo then
                Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.OperatorItem, "SetCurrentSchemeId", curHeroPropsInfo.fashion_id)
            else
                Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.OperatorItem, "SetCurrentSchemeId", "None")
            end
        end
      
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.OperatorItem, "SetDisplayItem", item.id)
        
        local BGId = self.lotteryId
        local isBundle = true
        if self.lotteryId == nil or self.lotteryId == 0 then
            BGId = item.id
            isBundle = false
        end
        -- 背景设置    
        if BGId then
            Facade.HallSceneManager:SetDisplayBackground(tonumber(BGId), isBundle, "OperatorItemBG")
        end
        --设置背景缩放
        Facade.HallSceneManager:SetBackgroundScale("OperatorItemBG")
        Facade.UIManager:CommitTransition(false) 
    end
   
    Module.Store:EnterSubLevel(BGId, ESubStage.OperatorItem, fAllLevelFinishCallback)
end

function StaffLotteryAccessoriesPreview:_HideAppearancePanel(isBool)
    if self._wtPlatformPaddingBox0 and self._wtPlatformPaddingBox1 and self._wtPlatformPaddingBox2 then
        if isBool then -- 需要隐藏
            self._wtPlatformPaddingBox0:Hidden()
            self._wtPlatformPaddingBox1:Hidden()
            self._wtPlatformPaddingBox2:Hidden()
        else -- 需要显示
            self._wtPlatformPaddingBox0:Visible()
            self._wtPlatformPaddingBox1:Visible()
            self._wtPlatformPaddingBox2:Visible()
        end
    end
end

function StaffLotteryAccessoriesPreview:_EnableTouchedHideApperance(bIsEnable)
    local curSubStage = Facade.GameFlowManager:GetCurrentSubStage()
    if curSubStage ~= ESubStage.HallHero then
        return
    end

	if not isvalid(self._displayCtrlActor) then
		return
	end

	if bIsEnable then
		self._displayCtrlActor.OnTargetActorTouchedDelegate:Remove(self._HideAppearancePanel, self)
		self._displayCtrlActor.OnTargetActorTouchedDelegate:Add(self._HideAppearancePanel, self)
	else
		self._displayCtrlActor.OnTargetActorTouchedDelegate:Remove(self._HideAppearancePanel, self)
	end
end

function StaffLotteryAccessoriesPreview:_PreviewHeroItemVideo()
    local fOnHeroItemVideoPanelCreated = function (uiIns)
        
    end
    local AccessoriesItemID = self.AccessoriesItemIDTable[self.currentSelectedAccessoriesIndex]
    local itemData = ItemBase:New(AccessoriesItemID)
    Module.Hero:ShowHeroItemVideoPanel(fOnHeroItemVideoPanelCreated, itemData.id, self.AccessoriesItemIDTable)
end

function StaffLotteryAccessoriesPreview:_JumpToHeroItemCustomPanel()
    local AccessoriesItemID = self.AccessoriesItemIDTable[self.currentSelectedAccessoriesIndex]
    Module.Hero.ShowHeroMysticalItemPanel(nil,nil, AccessoriesItemID)
end


-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
function StaffLotteryAccessoriesPreview:_EnableGamepadFeature(bEnable)
    if not IsHD() then
        return
    end
    if bEnable then

        if not self._wtNavGroup then
            self._wtCanvasPanel = self:Wnd("DFHorizontalBox_1", UIWidgetBase) -- 顶层面板            
            self._wtNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtCanvasPanel, self, "Hittest")
            if self._wtNavGroup then
                self._wtNavGroup:AddNavWidgetToArray(self._wtCanvasPanel)
                self._wtNavGroup:SetScrollRecipient(self._wtCanvasPanel)
                self._wtNavGroup:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
                self._wtNavGroup:MarkIsStackControlGroup()
            end                       
        end
        if self._wtNavGroup then
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup) 
        end
        -- self._wtBtnUnlockBP:SetDisplayInputAction("BattlePass_Unlock_Gamepad", true, nil, true)
        -- if not self._wtBtnUnlockBPHandle then
        --     self._wtBtnUnlockBPHandle = self:AddInputActionBinding("BattlePass_Unlock_Gamepad", EInputEvent.IE_Pressed, self.OnBtnUnlockBtnClick,self, EDisplayInputActionPriority.UI_Stack)
        -- end

        self:_UpdateInputSummaryList()

        --武器旋转的输入
        if self._RotationX == nil then
            self._RotationX = self:AddAxisInputActionBinding("Common_Right_X", self._WeaponRotationX, self, EDisplayInputActionPriority.UI_Stack)
        end
        if self._RotationY == nil then
            self._RotationY = self:AddAxisInputActionBinding("Common_Right_Y", self._WeaponRotationY, self, EDisplayInputActionPriority.UI_Stack)
        end

        if self._ScaleUp == nil then
            self._ScaleUp = self:AddAxisInputActionBinding("Common_Right_Trigger_Axis", self._WeaponScaleUp, self, EDisplayInputActionPriority.UI_Stack)
        end

        if self._ScaleDown == nil then
            self._ScaleDown = self:AddAxisInputActionBinding("Common_Left_Trigger_Axis", self._WeaponScaleDown, self, EDisplayInputActionPriority.UI_Stack)
        end

    else

        if self._wtNavGroup then 
            WidgetUtil.RemoveNavigationGroup(self)
            self._wtNavGroup = nil
        end

        if self._RotationX then
            self:RemoveInputActionBinding(self._RotationX)
            self._RotationX = nil
        end
  
        if self._RotationY then
            self:RemoveInputActionBinding(self._RotationY)
            self._RotationY = nil
        end

        if self._ScaleUp then
            self:RemoveInputActionBinding(self._ScaleUp)
            self._ScaleUp = nil
        end

        if self._ScaleDown then
            self:RemoveInputActionBinding(self._ScaleDown)
            self._ScaleDown = nil
        end
    end
end

function StaffLotteryAccessoriesPreview:_UpdateInputSummaryList()
    if not IsHD() then
        return
    end
    -- local summaryList = {}
    -- table.insert(summaryList, {actionName = "Select",func = nil, caller = nil ,bUIOnly = true, bHideIcon = false}) --选择     
    -- Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, true, false)
end

function StaffLotteryAccessoriesPreview:_InitShortcuts()
    self:_RemoveShortcuts()
    if self._wtPreviewHeroItemBtn:IsVisible() and not self._previewHandle then
        self._previewHandle = self:AddInputActionBinding("StaffLottery_Preview_Gamepad", EInputEvent.IE_Pressed, self._PreviewHeroItemVideo, self, EDisplayInputActionPriority.UI_Stack)
        self._wtPreviewHeroItemBtn:SetDisplayInputAction("StaffLottery_Preview_Gamepad", true, nil, true)
    end
    if self._wtHeroItemCustomBtn:IsVisible() and not self._customeHandle then
        self._customeHandle = self:AddInputActionBinding("StaffLottery_HeroCustom_Gamepad", EInputEvent.IE_Pressed, self._JumpToHeroItemCustomPanel, self, EDisplayInputActionPriority.UI_Stack)
        self._wtHeroItemCustomBtn:SetDisplayInputAction("StaffLottery_HeroCustom_Gamepad", true, nil, true)
    end
end

function StaffLotteryAccessoriesPreview:_RemoveShortcuts()
    if self._previewHandle then
        self:RemoveInputActionBinding(self._previewHandle)
        self._previewHandle= nil
    end
    if self._customeHandle then
        self:RemoveInputActionBinding(self._customeHandle)
        self._customeHandle= nil
    end
end
-- END MODIFICATION


function StaffLotteryAccessoriesPreview:_WeaponRotationX(value)
    if not IsHD() then
        return
    end

    self._RotationVec.X = value * -1 * self._WeaponRotationZoom * LuaTickController:Get():GetDeltaTime()
    if isvalid(self._displayCtrlActor) then
        self._displayCtrlActor:HandleRotationByGamepad(self._RotationVec)
    end

end
function StaffLotteryAccessoriesPreview:_WeaponRotationY(value)
    if not IsHD() then
        return
    end

    self._RotationVec.Y = value * self._WeaponRotationZoom * LuaTickController:Get():GetDeltaTime()
    if isvalid(self._displayCtrlActor) then
        self._displayCtrlActor:HandleRotationByGamepad(self._RotationVec)
    end
end

function StaffLotteryAccessoriesPreview:_WeaponScaleUp(value)
    if not IsHD() then
        return
    end
    local scaleValue = value * self._WeaponScaleZoom * LuaTickController:Get():GetDeltaTime()
    if isvalid(self._displayCtrlActor) then
        self._displayCtrlActor:HandleScaleByGamepad(scaleValue)
    end
end

function StaffLotteryAccessoriesPreview:_WeaponScaleDown(value)
    if not IsHD() then
        return
    end
    local scaleValue = value * -1 * self._WeaponScaleZoom * LuaTickController:Get():GetDeltaTime()

    if isvalid(self._displayCtrlActor) then
        self._displayCtrlActor:HandleScaleByGamepad(scaleValue)
    end
end

return StaffLotteryAccessoriesPreview