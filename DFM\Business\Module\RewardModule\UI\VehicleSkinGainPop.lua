----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMReward)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class VehicleSkinGainPop : LuaUIBaseView
local RewardBaseView = require "DFM.Business.Module.RewardModule.UI.RewardBaseView"
local VehicleSkinGainPop = ui("VehicleSkinGainPop", RewardBaseView)
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local CollectionLogic = require "DFM.Business.Module.CollectionModule.Logic.CollectionLogic"
local EGPInputModeType = import "EGPInputModeType"
local VehicleHelperTool = require "DFM.StandaloneLua.BusinessTool.VehicleHelperTool"
local RewardDetail = require "DFM.Business.Module.RewardModule.UI.RewardDetail"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"

function VehicleSkinGainPop:Ctor()
    self._wtSkipBtn = self:Wnd("wtCommonButtonV1S2", DFCommonButtonOnly)
    self._wtSkipBtn:Event("OnClicked", self._OnSkipBtnClick, self)
    self._wtSkipAllBtn = self:Wnd("WBP_CommonButtonV3S1", DFCommonButtonOnly)
    self._wtSkipAllBtn:Event("OnClicked", self._OnSkipAllBtnClick, self)
    self._wtDetail = self:Wnd("WBP_CommonSpecialAcquisition", RewardDetail)
    self._wtApplyBtn = self:Wnd("wtCommonButtonV1S1", CommonButton)
    self._wtApplyBtn:Event("OnClicked", self._ApplyVehicleSkin, self)
    self._wtShareBtn = self:Wnd("ShareBtn", UIButton)
    self._wtShareBtn:Collapsed()
    -- Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.ETopBarStyleFlag.None)
    -- Module.CommonBar:SetTopAndBottomBarVisible(false)
end

function VehicleSkinGainPop:OnInitExtraData(vehicleskinItem)
    if vehicleskinItem then 
        self._vehicleInfo = vehicleskinItem
    else
        self:Collapsed()
    end
end


function VehicleSkinGainPop:OnOpen()
    self:_AddListeners()
end

function VehicleSkinGainPop:OnClose()
    -- if self._hActionSkip then
    --     self:RemoveInputActionBinding(self._hActionSkip)
    --     self._hActionSkip = nil
    -- end
    self:RemoveAllLuaEvent()
    if self._bExecuteClose ~= true then
        self._bExecuteClose = true
        Module.Reward:ShowNextRewards()
    end
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallVehicle, "ResetGeneralLevelSequence")
end

function VehicleSkinGainPop:OnShow()
    -- if not self._hActionSkip then
    --     self._hActionSkip =
    --         self:AddInputActionBinding(
    --         "JumpOver",
    --         EInputEvent.IE_Pressed,
    --         self._OnSkipBtnClick,
    --         self,
    --         EDisplayInputActionPriority.UI_POP
    --     )
    -- end
    -- self:SetCPPValue("WantedInputMode", EGPInputModeType.GameAndUI)
    self._wtShareBtn:Collapsed()
    self:_OnRefreshItemDetail()
    self:SetCPPValue("WantedInputMode", EGPInputModeType.GameAndUI)
    if  self._vehicleInfo then
        local isDownload = Module.Vehicle:IsDownloadedVehiclePak(self._vehicleInfo.id)
        if not isDownload then
            Module.CommonTips:ShowSimpleTip(Module.Reward.Config.Loc.NotDownloadVehicleSkin)
        end
    end
end


function VehicleSkinGainPop:OnHide()
    Module.Vehicle:ProcessDisposeByActorID(1)
end


function VehicleSkinGainPop:OnShowBegin()
    -- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() then
        self:_EnableGamepadFeature(true)
    end 
    -- END MODIFICATION
end


function VehicleSkinGainPop:OnHideBegin()
    -- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() then
        self:_EnableGamepadFeature(false)
    end 
    -- END MODIFICATION
end


function VehicleSkinGainPop:OnAnimFinished(anim)
    if anim == self.WBP_Vehicle_Obtain_in then
       
    elseif anim == self.WBP_Vehicle_Obtain_out then
        if self._bExecuteClose ~= true then
            self._bExecuteClose = true
            Module.Reward:ShowNextRewards(self._bTabPressed == true)
        end
    end
end


function VehicleSkinGainPop:_AddListeners()
    self:AddLuaEvent(Server.VehicleServer.Events.evtVehicleUpdate, self._OnAppliedVehicleSkin, self)
    self:AddLuaEvent(LuaGlobalEvents.evtSceneLoaded, self._OnRefreshModel, self)
end



function VehicleSkinGainPop:_OnRefreshItemDetail()
    if self._vehicleInfo then 
        self._wtSkipBtn:Collapsed()
        self._wtSkipAllBtn:Collapsed()
        self._wtApplyBtn:Collapsed()
        self._wtApplyBtn:Collapsed()
        self._wtDetail:SetInfoPanelEnable(false)
        local vehicleSkinId = self._vehicleInfo.id
        local vehicleId = VehicleHelperTool.GetVehicleIdBySkinID(vehicleSkinId)
        if vehicleSkinId then 
            local bIsUnLock = Server.VehicleServer:IsUnlockMPVehicleByID(vehicleId)
            if bIsLock then
                self._wtApplyBtn:SetMainTitle(Module.Collection.Config.Loc.ProceedToUnlock)
            else
                if VehicleHelperTool.IsEquipSkin(vehicleSkinId) then
                    self._wtApplyBtn:SetMainTitle(Module.Collection.Config.Loc.AppearanceApplied)
                    self._wtApplyBtn:SetIsEnabled(false)
                else
                    self._wtApplyBtn:SetMainTitle(Module.Collection.Config.Loc.ApplyAppearance)
                end
            end
            self:_SetDisplayVehicle(vehicleSkinId)
        else
            self:Collapsed()
            return
        end
        self:PlayAnimation(self.WBP_Vehicle_Obtain_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
        local vehicleName = VehicleHelperTool.GetVehicleSkinNameByID(vehicleSkinId)
        local vehicleDesc = VehicleHelperTool.GetVehicleDescByID(vehicleSkinId)
        self._wtDetail:SetRewardInfo(Module.Reward.Config.RewardDetailType.VEHICLEANDCARD,Module.Reward.Config.Loc.GainVehicleSkin,vehicleName,vehicleDesc,ItemConfigTool.GetItemQuality(vehicleSkinId),true,true,self._ShowNormalView,self,true)
    end
end

function VehicleSkinGainPop:_SetDisplayVehicle(id)
    local curHallDisplaySubStage = Facade.HallSceneManager:GetCurSubStageType()
    if curHallDisplaySubStage == ESubStage.HallVehicle then
        local ctrl = Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.HallVehicle)
        if not ctrl then 
            return
        end
        if self._displayId == id then 
            return
        end
        self._displayId = id
        Module.Vehicle:SetQualityEnv(id)
        Module.Vehicle:ProcessDisposeByActorID(1)
        local vehicleId = VehicleHelperTool.GetVehicleIdBySkinID(id)
        local vehicleItemId = VehicleHelperTool.GetItemIdBySkinID(id)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallVehicle, "LoadVehicleLevel", id, vehicleItemId)
        local quality = ItemConfigTool.GetItemQuality(self._vehicleInfo.id)
        if quality >= ItemConfig.EWeaponSkinQualityType.Orange then 
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallVehicle, "PlaySequence",id,vehicleId == 0 and id or vehicleId)
        else
            self:_ShowNormalView()
        end
        self._displayCtrlActor = Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.HallVehicle)
        if isvalid(self._displayCtrlActor) then 
            self._displayCtrlActor.GeneralLevelSequenceCtrlComponent.OnGeneralSeqFinished:Add(self._OnGeneralSeqFinished, self)
        end
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallVehicle, "SetVehicleDisplayType", vehicleItemId, 1)

        local bIsVehicle = self:_InternalIsVehicle()
        if bIsVehicle then
            Module.Vehicle:LoadStandardVehicleFromVehicleID(id, 1)
            return
        end
        Module.Vehicle:LoadVehicleFromSkinID(id, 1)
    end
end

function VehicleSkinGainPop:_InternalIsVehicle()
    local itemMainType = ItemHelperTool.GetMainTypeById(id)
    return itemMainType == EItemType.Vehicle
end

function VehicleSkinGainPop:_OnGeneralSeqFinished()
    self:_ShowNormalView()
end

function VehicleSkinGainPop:_ShowNormalView()
    if self._vehicleInfo then
        self._wtSkipBtn:SetMainTitle(Module.Hero.Config.Loc.HeroContinue)
        self._wtSkipBtn:SetIsEnabled(true)
        self._wtSkipBtn:SelfHitTestInvisible()
        self._wtDetail:SetInfoPanelEnable(true)
        self._wtSkipAllBtn:Collapsed()
        self._wtApplyBtn:Collapsed()
        self._wtApplyBtn:SetMainTitle(Module.Collection.Config.Loc.ApplyAppearance)
        self._wtApplyBtn:SetIsEnabled(true)
        local vehicleSkinId = self._vehicleInfo.id
        local vehicleId = VehicleHelperTool.GetVehicleIdBySkinID(vehicleSkinId)
        -- Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallVehicle, "SetVehicleDisplayType", vehicleId, 1)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallVehicle, "SequenceGoToEndAndStop")
        if vehicleSkinId then 
            local bIsUnLock = Server.VehicleServer:IsUnlockMPVehicleByID(vehicleId)
            if bIsLock then
                self._wtApplyBtn:SetMainTitle(Module.Collection.Config.Loc.ProceedToUnlock)
            else
                if VehicleHelperTool.IsEquipSkin(vehicleSkinId) then
                    self._wtApplyBtn:SetMainTitle(Module.Collection.Config.Loc.AppearanceApplied)
                    self._wtApplyBtn:SetIsEnabled(false)
                else
                    self._wtApplyBtn:SetMainTitle(Module.Collection.Config.Loc.ApplyAppearance)
                end
            end
        else
            self:Collapsed()
            return
        end
        self._wtApplyBtn:SelfHitTestInvisible()
        self:PlayAnimation(self.WBP_Vehicle_Obtain_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
        local vehicleName = VehicleHelperTool.GetVehicleSkinNameByID(vehicleSkinId)
        local vehicleDesc = VehicleHelperTool.GetVehicleDescByID(vehicleSkinId)
        self._wtDetail:SetRewardInfo(Module.Reward.Config.RewardDetailType.VEHICLEANDCARD,Module.Reward.Config.Loc.GainVehicleSkin,vehicleName,vehicleDesc,ItemConfigTool.GetItemQuality(vehicleSkinId),false,false  ,self._OnSkipBtnClick,self)
    end
end

function VehicleSkinGainPop:_OnRefreshModel(curSubStageType)
    -- if curSubStageType == ESubStage.HallVehicle then
        if self._vehicleInfo then
            self:_SetDisplayVehicle(self._vehicleInfo.id)
        end
    -- end
end


function VehicleSkinGainPop:_OnSkipBtnClick()
    self:HandleTransition(true)
    Facade.SoundManager:StopUIAudioEvent("UI_Common_Popup")
    self:PlayAnimation(self.WBP_Vehicle_Obtain_out, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
end



function VehicleSkinGainPop:_OnSkipAllBtnClick()
    self._vehicleInfo = {}
    Module.Reward:SkipAllSpecialPanels()
    self:SkipAnimation(self.WBP_Vehicle_Obtain_in)
    Facade.SoundManager:StopUIAudioEvent("UI_Common_Popup")
    self:PlayAnimation(self.WBP_Vehicle_Obtain_out, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
end


function VehicleSkinGainPop:_ApplyVehicleSkin()
    if self._vehicleInfo then
        local skinId = self._vehicleInfo.id
        local vehicleId = VehicleHelperTool.GetVehicleIdBySkinID(skinId)
        local itemId = VehicleHelperTool.GetItemIdBySkinID(skinId)
        local bIsUnLock = Server.VehicleServer:IsUnlockMPVehicleByID(vehicleId)
        if bIsUnLock then
        else
            Server.VehicleServer:CSMPChangeVehicleSkinReq(itemId,skinId)
        end
    end
end



function VehicleSkinGainPop:_OnAppliedVehicleSkin()
    if self._vehicleInfo then
        local skinId = self._vehicleInfo.id
        if VehicleHelperTool.IsEquipSkin(skinId) then
            local name = VehicleHelperTool.GetVehicleSkinNameByID(skinId)
            local tip = string.format(Module.Collection.Config.Loc.SuccessfullyAppliedVehicleSkin, name)
            Module.CommonTips:ShowSimpleTip(tip)
            self._wtApplyBtn:SetMainTitle(Module.Collection.Config.Loc.AppearanceApplied)
            self._wtApplyBtn:SetIsEnabled(false)
        end
    end
end

function VehicleSkinGainPop:OnNavBack()
    self:_OnSkipBtnClick()
    return true
end


--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
function VehicleSkinGainPop:_EnableGamepadFeature(bEnable)
    if not IsHD() then
        return 
    end
    if bEnable then
        if self._wtSkipBtn then
            self._wtSkipBtn:SetDisplayInputAction("Reward_Continue_Gamepad", true, nil, true)
        end

        if self._wtApplyBtn then
            self._wtApplyBtn:SetDisplayInputAction("Common_ButtonLeft", true, nil, true)
        end

        -- 跳过按键响应
        if not self._skipHandle then
            self._skipHandle = self:AddInputActionBinding("Reward_Continue_Gamepad", EInputEvent.IE_Pressed, self._OnSkipBtnClick, self, EDisplayInputActionPriority.UI_Pop)
        end

        -- 应用按键响应
        if not self._applyHandle then
            self._applyHandle = self:AddInputActionBinding("Common_ButtonLeft", EInputEvent.IE_Pressed, self._ApplyVehicleSkin, self, EDisplayInputActionPriority.UI_Pop)
        end
    else 
        if self._skipHandle then
            self:RemoveInputActionBinding(self._skipHandle)
            self._skipHandle = nil
        end

        if self._applyHandle then
            self:RemoveInputActionBinding(self._applyHandle)
            self._applyHandle = nil
        end
    end 

end
--- END MODIFICATION

-- function VehicleSkinGainPop:SetQualityEnv()
--     local quality = ItemHelperTool.GetQualityTypeById(self._vehicleInfo.id)
--     local data = EVehicleQualityColor[quality]
--     if data then 
--         Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallVehicle, "SetScalarParameterValue","MandeerEmissiveIntensity",3)
--         Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallVehicle, "SetVectorParameterValue","MandeerEmissiveColor",data.Color)
--         if IsHD() then 
--             local cppDisplayTypeStr = SubStaget2DisplayType[ESubStage.HallVehicle]
--             local lightGroupName = string.format("%s_Main", cppDisplayTypeStr)
--             LightUtil.ActiveLightGroupOnly(lightGroupName)
--             LightUtil.ActiveLightGroup(data.Light)
--         end
--     end
-- end

return VehicleSkinGainPop
