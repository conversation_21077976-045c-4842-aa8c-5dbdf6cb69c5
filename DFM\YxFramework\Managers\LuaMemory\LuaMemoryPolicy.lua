----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaFManager)
----- LOG FUNCTION AUTO GENERATE END -----------




local LuaMemoryPolicy = {}

LuaMemoryPolicy.ResourceTypes = {
     Level         = 1,
     LevelNoMatch  = 1 << 1,
     RTIRT         = 1 << 2,
     RTCapture     = 1 << 3,
     UI            = 1 << 4
}

--- UI 可能是异步的
--- 所以 UI 需要在最后做，GC 跟着 UI 做
LuaMemoryPolicy.ResourceTypesPriorityList = {
     LuaMemoryPolicy.ResourceTypes.Level,
     LuaMemoryPolicy.ResourceTypes.LevelNoMatch,
     LuaMemoryPolicy.ResourceTypes.RTIRT,
     LuaMemoryPolicy.ResourceTypes.RTCapture,
     LuaMemoryPolicy.ResourceTypes.UI,
}

LuaMemoryPolicy.GCTypes = {
     None                      = 0,
     CheckFullGC               = 1,
     ImmediateFullGC           = 1 << 1,
     ImmediateUEGC             = 1 << 2,
}

LuaMemoryPolicy.EReleaseResourceGroup = {
     MemoryWarning = 1,
     UObjectWarning = 2,
     PreUEGC = 3,
     SOLMatchSuccess = 4,
     SOLHeroReady = 5,
     ForceBackConfirm = 6,
     GMDebug = 7
}

LuaMemoryPolicy.ReleaseResourceGroupCongigs = {

     [LuaMemoryPolicy.EReleaseResourceGroup.MemoryWarning] = {
          ResourceType = LuaMemoryPolicy.ResourceTypes.Level |
                         LuaMemoryPolicy.ResourceTypes.RTIRT |
                         LuaMemoryPolicy.ResourceTypes.UI,

          GCType = LuaMemoryPolicy.GCTypes.CheckFullGC
     },

     [LuaMemoryPolicy.EReleaseResourceGroup.UObjectWarning] = {
          ResourceType = LuaMemoryPolicy.ResourceTypes.Level |
                         LuaMemoryPolicy.ResourceTypes.RTIRT |
                         LuaMemoryPolicy.ResourceTypes.UI,

          GCType = LuaMemoryPolicy.GCTypes.CheckFullGC
     },

     [LuaMemoryPolicy.EReleaseResourceGroup.PreUEGC] = {
          ResourceType = LuaMemoryPolicy.ResourceTypes.UI,
          GCType = LuaMemoryPolicy.GCTypes.None
     },

     [LuaMemoryPolicy.EReleaseResourceGroup.SOLMatchSuccess] = {
          ResourceType = LuaMemoryPolicy.ResourceTypes.LevelNoMatch |
                         LuaMemoryPolicy.ResourceTypes.RTIRT |
                         LuaMemoryPolicy.ResourceTypes.RTCapture |
                         LuaMemoryPolicy.ResourceTypes.UI,

          GCType = LuaMemoryPolicy.GCTypes.ImmediateFullGC | 
                   LuaMemoryPolicy.GCTypes.ImmediateUEGC 
     },

     [LuaMemoryPolicy.EReleaseResourceGroup.SOLHeroReady] = {
          ResourceType = LuaMemoryPolicy.ResourceTypes.Level |
                         LuaMemoryPolicy.ResourceTypes.RTIRT |
                         LuaMemoryPolicy.ResourceTypes.RTCapture |
                         LuaMemoryPolicy.ResourceTypes.UI,

          GCType = LuaMemoryPolicy.GCTypes.ImmediateFullGC | 
                   LuaMemoryPolicy.GCTypes.ImmediateUEGC 
     },

     [LuaMemoryPolicy.EReleaseResourceGroup.ForceBackConfirm] = {
          ResourceType = LuaMemoryPolicy.ResourceTypes.Level |
                         LuaMemoryPolicy.ResourceTypes.RTIRT |
                         LuaMemoryPolicy.ResourceTypes.UI,

          GCType = LuaMemoryPolicy.GCTypes.CheckFullGC
     },

     [LuaMemoryPolicy.EReleaseResourceGroup.GMDebug] = {
          ResourceType = LuaMemoryPolicy.ResourceTypes.Level |
                         LuaMemoryPolicy.ResourceTypes.RTIRT |
                         LuaMemoryPolicy.ResourceTypes.RTCapture |
                         LuaMemoryPolicy.ResourceTypes.UI,

          GCType = LuaMemoryPolicy.GCTypes.ImmediateFullGC | 
                   LuaMemoryPolicy.GCTypes.ImmediateUEGC 
     },
}

return LuaMemoryPolicy