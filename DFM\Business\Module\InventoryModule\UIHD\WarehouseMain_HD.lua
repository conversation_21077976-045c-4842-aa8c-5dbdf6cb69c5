----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMInventory)
----- LOG FUNCTION AUTO GENERATE END -----------



local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local InventoryConfig = require "DFM.Business.Module.InventoryModule.InventoryConfig"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local ItemBaseTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemBaseTool"
local WarehouseWithTab = require "DFM.Business.Module.InventoryModule.UI.MainV2.WarehouseWithTab"
local WarehouseEquipPanel = require "DFM.Business.Module.InventoryModule.UI.MainV2.WarehouseEquipPanel"
local InventoryLogic = require "DFM.Business.Module.InventoryModule.InventoryLogic"
local WarehouseEquipPanel_HD = require "DFM.Business.Module.InventoryModule.UIHD.WarehouseEquipPanel_HD"
local EDescendantScrollDestination = import "EDescendantScrollDestination"
local ShopHelperTool = require "DFM.StandaloneLua.BusinessTool.ShopHelperTool"
local InventoryNavManager = require "DFM.Business.Module.InventoryModule.Logic.InventoryNavManager"

local UWidgetLayoutLibrary = import "WidgetLayoutLibrary"

--BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
local GPUINavigationStrategy_Hittest = import("GPUINavigationStrategy_Hittest")
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local UGPInputHelper = import "GPInputHelper"
local EGPInputType = import"EGPInputType"
local UGPInputDelegates = import "GPInputDelegates"
local EGPUINavHittestFallbackType = import "EGPUINavHittestFallbackType"
local UGPUINavigationUtils = import("GPUINavigationUtils")
local EDescendantScrollDestination = import "EDescendantScrollDestination"
local EGPUINavGroupTransSimpleStrategy = import "EGPUINavGroupTransSimpleStrategy"
local EGPUINavGroupBestDefaultType = import "EGPUINavGroupBestDefaultType"
local UDFNavigationSelectorBase = import("DFNavigationSelectorBase")
local UGPUINavigationManager = import("GPUINavigationManager")
--END MODIFICATION

---@class WarehouseMain_HD : LuaUIBaseView
local WarehouseMain_HD = ui("WarehouseMain_HD")

local function log(...)
    loginfo("[WarehouseMain_HD]", ...)
end

function WarehouseMain_HD:Ctor()
    self._bOpenFromArmedForce = false
    self._bInRentalState = false

    self._wtRootCanvas = self:Wnd("wtRootCanvas", UIWidgetBase)
    self._wtWarehousePanel = self:Wnd("wtWarehousePanel", WarehouseWithTab)

    self._wtEquipPanel = self:Wnd("wtEquipPanel", WarehouseEquipPanel_HD)
    self._wtAutoSnapArea = self:Wnd("wtAutoSnapArea", UIWidgetBase)
    self._autoSnapSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(self._wtAutoSnapArea)
    self._defaultOffset = self._autoSnapSlot:GetOffsets()
    self._wtDepositValue = self:Wnd("wtDepositValue", UITextBlock)
    self._wtPriceBox = self:Wnd("CanvasPanel_1", UIWidgetBase)

    self:_CreateCaptureUtil()
    Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.DefaultSecondary)
    -- self._wtDFTipsAnchor = UIUtil.WndTipsAnchor(self, "DFTipsAnchor_1", self._ShowValueTips, self._HideValueTips)

    self._wtRentalTips = self:Wnd("WBP_EquipmentOnLoan", UIWidgetBase)
    self._wtRentalTipsText = self._wtRentalTips:Wnd("DFTextBlock_127", UITextBlock)
    self._wtRentalTipsText:SetText(Module.ArmedForce.Config.Loc.EquipmentRentalStr)
    self._wtRentalCancelBtn = self._wtRentalTips:Wnd("DFButton_124", UIButton)
    self._wtRentalCancelBtn:Event("OnClicked", self._OnRentalCancelBtnClicked, self)

    self._wtArrangeTipsTxt = self:Wnd("wtArrangeTipText", UITextBlock)
    self._wtPanelMask = self:Wnd("WBP_Common_ScaleBg", UIWidgetBase)

    -- 定义开关，判断是否打开仓库
    self._bIsOpenWarehouse = false
    self._wtExtSubPanel = nil
    self._isJumptoMerchant = false

    self._wtTipsAnchor = UIUtil.WndTipsAnchor(self, "DFTipsAnchor", self._ShowExtManageTips, self._HideExtManageTips)

    -- 是否允许手柄按键轮播Tips
    self._EnableTipsInEquipBox = false
    -- 附加DisplayAction
    self._AddtionalDisplayAction = {}
    -- 肩键提示
    self._wtLeftShoulderKey = self:Wnd("KeyIcon_02", HDKeyIconBox)
    self._wtRightShoulderKey = self:Wnd("KeyIcon_01", HDKeyIconBox)

    if IsHD() then
        self._wtLeftShoulderKey:InitByDisplayInputActionName("Common_SwitchToPrevTab_Trigger", true, 0.0, false)
        self._wtRightShoulderKey:InitByDisplayInputActionName("Common_SwitchToNextTab_Trigger", true, 0.0, false)
    end

end

--==================================================
--region Life function
function WarehouseMain_HD:OnOpen()
    -- local handle = inputMonitor:AddDisplayActionBinding(name, EInputEvent.IE_Pressed, callback, self, EDisplayInputActionPriority.UI_Stack)
end
function WarehouseMain_HD:OnClose()
    self:RemoveAllLuaEvent()

    -- force close detail tip
    Module.ItemDetail:CloseItemDetailPanel()
    InventoryNavManager.Reset()

end

function WarehouseMain_HD:OnShowBegin()
    InventoryNavManager.Reset()
    local _wtWarehouse = self._wtWarehousePanel:Wnd("wtWarehouse", UIWidgetBase)
	InventoryNavManager.RegisterSlotView(_wtWarehouse._wtDepositSlotView)
    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIWHOpen)
    self:_SetInputSummary()
    self:_RefreshWarehouseValue()
    self:_CheckRentalStatus()
    Module.CommonWidget:SetSelectedMode(false)

    --BEGIN MODIFICATION @ VIRTUOS : UI Navigation
    if IsHD() then
        self:_RegisterNavGroup()
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default, self)
        local InvSlotViewType = Module.Inventory:GetInvSlotViewType()
        Module.CommonWidget:SetGamepadSwapItem(false)
        self:_EnableInputTypeChangedHandle(true)
        self:_EnableSimulatedMouseDragHandle(true)

        -- 开启手柄移动物品功能
        Module.CommonWidget:EnableGamepadSwapItem(true)
        Module.CommonWidget:EnableGamepadCarryItemFromPop(true)
        Module.CommonWidget:EnableGamepadItemShortcutKey(true)

        self:_SetEnableHeroInfoBtns(true)
        Module.Inventory:SetInvSlotViewType(Module.Inventory.Config.EInvSlotViewType.Default)

        local navMgr =  WidgetUtil.GetNavigationMgr()
        if self._usingFreeAnalogCursorStateChangedEventHandle then
            navMgr.OnUsingFreeAnalogCursorStateChanged:Remove(self._usingFreeAnalogCursorStateChangedEventHandle)
            self._usingFreeAnalogCursorStateChangedEventHandle = nil
        end
        self._usingFreeAnalogCursorStateChangedEventHandle = navMgr.OnUsingFreeAnalogCursorStateChanged:Add(CreateCPlusCallBack(self._OnUsingFreeAnalogCursorStateChanged,self))
        self:_BindAdditonDisplayAction()

    end
    --END MODIFICATION
end

function WarehouseMain_HD:OnShow()
    Server.TipsRecordServer:SetBoolean(Server.TipsRecordServer.keys.GuideWarehouseOpened, true)
    InventoryNavManager.FocusToDefaultFocusPoint()
    Module.CommonWidget:SetAutoSnapEnabled(true)
    Module.CommonWidget:BindCommonIVInputLogic()
    Module.Inventory:RegisterCommonClickBehavior()

    self:AddLuaEvent(CommonWidgetConfig.Events.evtGlobalItemDragStart, self._OnGlobalItemDragStart, self)
    self:AddLuaEvent(CommonWidgetConfig.Events.evtGlobalItemDrop, self._OnGlobalItemDrop, self)
    self:AddLuaEvent(CommonWidgetConfig.Events.evtGlobalItemDragCancelled, self._OnGlobalItemDragCancelled, self)
    self:AddLuaEvent(Server.InventoryServer.Events.evtItemMove, self._OnItemMove, self)
    self:AddLuaEvent(CommonWidgetConfig.Events.evtItemDoubleClicked, self._OnItemDoubleClicked, self)
    self:AddLuaEvent(InventoryConfig.Events.evtListItemDoubleClicked, self._OnItemDoubleClicked, self)
    self:AddLuaEvent(InventoryConfig.Events.evtEquipSlotViewDoubleClicked, self._OnItemDoubleClicked, self)
    self:AddLuaEvent(InventoryConfig.Events.evtEnterExtManageMode, self._OnInterExtManageMode, self)
    self:AddLuaEvent(InventoryConfig.Events.evtEnterExtArrangeMode, self._OnEnterExtArrangeMode, self)
    self:AddLuaEvent(InventoryConfig.Events.evtEnterSellMode, self._OnEnterSellMode, self)
    self:AddLuaEvent(InventoryConfig.Events.evtChangeTopBarStyle, self._SetTopBarTitle, self)
    self:AddLuaEvent(InventoryConfig.Events.evtJumpToShopMain, self._JumptoMerchantMain, self)
    self:AddLuaEvent(InventoryConfig.Events.evtRefreshCharacter, self._RefreshCharacterAppearance, self)
    self:AddLuaEvent(Server.ArmedForceServer.Events.evtArmedForceRentalStatusChanged, self._CheckRentalStatus, self)
    -- L3相关功能延期到she3
    -- self:AddLuaEvent(InventoryConfig.Events.evtSellModeFocusFirstItemFailed, self._OnSellModeFocusFirstItemFailed, self)
    -- self:AddLuaEvent(InventoryConfig.Events.evtCreateWarehouseNavgroupOnly, self._RegisterWarehouseNavGroup, self)
    
    

    -- BottomBar设置
    self:AddLuaEvent(InventoryConfig.Events.evtBottomBarSetting, self._SetInputSummary, self)

    --BEGIN MODIFICATION @ VIRTUOS : 
    self:AddLuaEvent(CommonWidgetConfig.Events.evtGlobalItemPopViewOpened, self._OnGlobalItemPopViewOpened, self)
    --END MODIFICATION



    self:_InitView()

    -- 经分上报
    LogAnalysisTool.DoSendOpenUILog(LogAnalysisTool.EItemDetailFromUIType.Inventory)

    self._bIsOpenWarehouse = true
    Module.Inventory.Field:SetIsShowWarehouse(self._bIsOpenWarehouse)
    self:_InitSpecialOperate()
    Module.Inventory:CheckShowNotifyReissueWindow()

    logerror("WarehouseMain_HD:OnShow")
end
function WarehouseMain_HD:OnHide()
    Module.CommonWidget:SetAutoSnapEnabled(false)
    Module.CommonWidget:UnbindCommonIVInputLogic()
    Module.Inventory:UnregisterCommonClickBehavior()
    Module.Inventory.Config.Events.evtUpdateFirstRowBlankItemVisibility:Invoke(false)

    self:RemoveAllLuaEvent()
    --LogAnalysisTool.ClearCurHaveItemDetailUI()

    self._bIsOpenWarehouse = false
    Module.Inventory.Field:SetIsShowWarehouse(self._bIsOpenWarehouse)
    self:_UpdateAllItemGids()
    Module.Inventory.Field:CleanMissionItemArray()
    Module.Inventory.Field:RemoveCommonHoverTipsHD()
    Module.CommonWidget:CancelDragDrop()
    self:_FlushInputSummary()
end

function WarehouseMain_HD:OnHideBegin()
    Module.Inventory.Config.Events.evtWareHouseMainOnHideBegin:Invoke()

    --BEGIN MODIFICATION @ VIRTUOS : UI Navigation
    if IsHD() then
        self:_RemoveNavGroup()
        -- 页面关闭时清除物品交换的记录 （每一个需要物品交换功能的主页面都需要）
        Module.CommonWidget:SetGamepadSwapItem(false)
        Module.CommonWidget:StopFocusItemView()
        WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
        self:_EnableInputTypeChangedHandle(false)
        self:_EnableSimulatedMouseDragHandle(false)
        
        -- 关闭手柄移动物品功能
        Module.CommonWidget:EnableGamepadSwapItem(false)
        Module.CommonWidget:EnableGamepadCarryItemFromPop(false)
        Module.CommonWidget:EnableGamepadItemShortcutKey(false)

        -- 关闭手柄打开的HoverTips
        if self._wtEquipPanel and self._wtEquipPanel.CloseHoverTips then
            self._wtEquipPanel:CloseHoverTips()
        end

        if self._wtWarehousePanel.RemoveArrangeNavGroup then
            self._wtWarehousePanel:RemoveArrangeNavGroup()
        end

        if self._wtWarehousePanel.RemoveExtManageNavGroup then
            self._wtWarehousePanel:RemoveExtManageNavGroup()
        end

        local navMgr =  WidgetUtil.GetNavigationMgr()
        if self._usingFreeAnalogCursorStateChangedEventHandle then
            navMgr.OnUsingFreeAnalogCursorStateChanged:Remove(self._usingFreeAnalogCursorStateChangedEventHandle)
            self._usingFreeAnalogCursorStateChangedEventHandle = nil
        end
        self:_UnBindAdditonDisplayAction()

    end
    --END MODIFICATIO
end


function WarehouseMain_HD:OnInitExtraData(bOpenFromArmedForce, navTabIndex,targetItem)
    self._bOpenFromArmedForce = bOpenFromArmedForce
    self._navTabIndex = navTabIndex
    self._targetItem=targetItem
    if self._bOpenFromArmedForce then
        Module.CommonBar:RegStackUITopBarTitle(self, Module.ArmedForce.Config.Loc.BagTitle)
    else
        Module.CommonBar:RegStackUITopBarTitle(self, InventoryConfig.Loc.WarehouseBackBtnTitle)
    end
end

function WarehouseMain_HD:OnAnimFinished(anim)
    if anim == self.WBP_WarehouseMain_in then
        Module.Inventory.Config.Events.evtWareHouseMainOpenFinish:Invoke()
    end
end

--endregion
--==================================================

--==================================================
--region Public API

function WarehouseMain_HD:PlayInAnim()
    self:PlayWidgetAnim(self.Ani_in)
    self._wtWarehousePanel:PlayInAnim()
    -- self._wtEquipPanel:PlayInAnim()
    -- self:PlayWidgetAnim(self.WBP_WarehouseMain_HD_in)
end

function WarehouseMain_HD:ShowFilterBtn(bShow)
    self._wtWarehousePanel:ShowFilterBtn(bShow)
end

function WarehouseMain_HD:BindCustomItemDoubleClick()
    
end

function WarehouseMain_HD:GetWarehousePanel()
    return self._wtWarehousePanel
end

function WarehouseMain_HD:GetWarehouseEquipPanel()
    return self._wtEquipPanel
end

--endregion
--==================================================

--==================================================
--region Private API

function WarehouseMain_HD:_InitView()
    self:_RefreshWarehouseValue()
    self:_RefreshWarehouseLevel()

    -- 需要延迟一会，直接选中位置不对
    local function fDelaySetNavTab(self, index)
        -- self._wtEquipPanel:ForceSetNavTabByIndex(index)
        self._wtEquipPanel:ScrollToStart()
    end
    if self._bOpenFromArmedForce then
        if self._navTabIndex then
            Timer.DelayCall(0.1, fDelaySetNavTab, self, self._navTabIndex)
        else
            Timer.DelayCall(0.1, fDelaySetNavTab, self, 2) -- Bag
        end
    elseif self._targetItem then
        -- self._wtEquipPanel:ScrolltoItem(self._targetItem)
    else
        Timer.DelayCall(0.1, fDelaySetNavTab, self, 1) -- Default
    end
    --local scroll=self._wtEquipPanel:Wnd("wtMainScrollBox",UIScrollBox)

    --local EDescendantScrollDestination = import "EDescendantScrollDestination"

    --scroll:ScrollWidgetIntoView(self._targetItem, true, EDescendantScrollDestination.Center)
end

function WarehouseMain_HD:_MerchantBackToWarehouse()
    if self._isJumptoMerchant then
        self._isJumptoMerchant = false
        self._wtWarehousePanel:BackToWarehouse()
    end
end

function WarehouseMain_HD:_RefreshWarehouseValue()
    local curDepositValue = Server.InventoryServer:GetWarehouseValue()
    curDepositValue = math.round(curDepositValue)
    -- self._wtDepositValue:SetText(MathUtil.GetTheThreePositionGradingMethod(curDepositValue))
    self._wtDepositValue:SetText(ShopHelperTool.GetCurrencyNumFormatStr(curDepositValue))
end

function WarehouseMain_HD:_RefreshWarehouseLevel()
    local warehouseDevice = Module.BlackSite:GetDeviceData(1010)
    local level = warehouseDevice and warehouseDevice:GetLevel() or 0
    --Module.CommonBar:ShowTopBarLevel(level)
    Module.CommonBar:ShowTopBarUpgradeBtn(warehouseDevice)
    -- self._wtWarehouseLevel:SetText("Lv" .. tostring(level))
end

function WarehouseMain_HD:_OnItemMove(itemMoveInfo)
    if itemMoveInfo.Reason ~= PropChangeType.Move then
        self:_RefreshWarehouseValue()
    end
end


---@param itemWidget ItemView
function WarehouseMain_HD:_OnItemDoubleClicked(itemWidget)
    local item = itemWidget.item
    local depositId = self._wtWarehousePanel:GetCurrentShowDepositId()

    -- 获取当前所有的 depositId
    local alldepositIds = Server.InventoryServer:GetAllDepositIds()

    -- if self._bOpenFromArmedForce then
    --     Module.Inventory:CommonItemDoubleClickedInWHForCarry(item, depositId)
    -- else
        -- Module.Inventory:CommonItemDoubleClickedInWH(item, depositId)
        Module.Inventory:CommonItemDoubleClickedInWH2Slot(item, depositId, alldepositIds)
    -- end
end

function WarehouseMain_HD:_OnInterExtManageMode(bEnter, bManagerMode)
    if bManagerMode then
        self._wtArrangeTipsTxt:SetText(Module.Inventory.Config.Loc.WarehouseExtWindowTitle)
    else
        self._wtArrangeTipsTxt:SetText(Module.Inventory.Config.Loc.ExtReplaceTitle)
    end

    --BEGIN MODIFICATION @ VIRTUOS : Reset the UI Navigation Group
    if bEnter == true then
        --移除基础导航组
        self:_RemoveNavGroup()
        --创建管理扩容箱导航组
        if self._wtWarehousePanel.RegisterExtManageNavGroup then
            self._wtWarehousePanel:RegisterExtManageNavGroup()
        end
    else
        --移除管理扩容箱导航组
        if self._wtWarehousePanel.RemoveExtManageNavGroup then
            self._wtWarehousePanel:RemoveExtManageNavGroup()
        end
        --创建基础导航组
        if self:IsVisible() then
            self:_RegisterNavGroup()
        end
    end
    --END MODIFICATION
    if bEnter then
        self._wtPanelMask:Visible()
    else
        self._wtPanelMask:Collapsed()
    end
    self:SetExtManage(bEnter)
    self._wtEquipPanel:ShowMask(bEnter)
    -- local function ShowExtManagerTips()
    --     if self.hoverHandle then
    --         Module.CommonTips:RemoveAssembledTips(self.hoverHandle, self._wtTipsAnchor)
    --         self.hoverHandle = nil
    --     end
    
    --     local contents = {}
    --     -- 标题
    --     table.insert(contents, {id = UIName2ID.Assembled_CommonMessageTips_V1, data = {textContent = InventoryConfig.Loc.ExtManagerTitle}})
    --     -- tips内容
    --     table.insert(contents, {id = UIName2ID.Assembled_CommonMessageTips_V2, data = {textContent = InventoryConfig.Loc.ExtManagerText1}})
    --     table.insert(contents, {id = UIName2ID.Assembled_CommonMessageTips_V2, data = {textContent = InventoryConfig.Loc.ExtManagerText2}})
    
    --     self.hoverHandle = Module.CommonTips:ShowAssembledTips(contents, self._wtTipsAnchor)
    -- end
    -- local function HideExtManagerTips()
    --     if self.hoverHandle then
    --         Module.CommonTips:RemoveAssembledTips(self.hoverHandle, self._wtTipsAnchor)
    --         self.hoverHandle = nil
    --     end
    -- end
end

function WarehouseMain_HD:_ShowExtManageTips()
    self:_HideExtManageTips()

    local contents = {}
    -- 标题
    table.insert(contents, {id = UIName2ID.Assembled_CommonMessageTips_V1, data = {textContent = InventoryConfig.Loc.ExtManagerTitle}})
    -- tips内容
    table.insert(contents, {id = UIName2ID.Assembled_CommonMessageTips_V2, data = {textContent = InventoryConfig.Loc.ExtManagerText1}})
    table.insert(contents, {id = UIName2ID.Assembled_CommonMessageTips_V2, data = {textContent = InventoryConfig.Loc.ExtManagerText2}})

    self.hoverHandle = Module.CommonTips:ShowAssembledTips(contents, self._wtTipsAnchor)
end

function WarehouseMain_HD:_HideExtManageTips()
    if self.hoverHandle then
        Module.CommonTips:RemoveAssembledTips(self.hoverHandle, self._wtTipsAnchor)
        self.hoverHandle = nil
    end
end

function WarehouseMain_HD:_OnEnterExtArrangeMode(bEnter)
    self:SetArrange(bEnter)
    self._wtEquipPanel:ShowMask(bEnter)
    if bEnter then
        self._wtPanelMask:Visible()
    else
        self._wtPanelMask:Collapsed()
    end

    --BEGIN MODIFICATION @ VIRTUOS : Reset the UI Navigation Group
    if bEnter == true then
        --移除基础导航组
        self:_RemoveNavGroup()
        --创建整理扩容箱导航组
        if self._wtWarehousePanel.RegisterArrangeNavGroup then
            self._wtWarehousePanel:RegisterArrangeNavGroup()
        end
    else
        --移除管理扩容箱导航组
        if self._wtWarehousePanel.RemoveArrangeNavGroup then
            self._wtWarehousePanel:RemoveArrangeNavGroup()
        end
        --创建基础导航组

        if self:IsVisible() then
            self:_RegisterNavGroup()
        end
    end
    --END MODIFICATION
end

function WarehouseMain_HD:_OnEnterSellMode(bEnter)
    self:SetSell(bEnter)
    self._wtEquipPanel:ShowMask(bEnter)
    -- 进入出售模式时，要注销掉所有其他导航组
    if bEnter then
        self:_RemoveNavGroup()
        self:CreateWarhouseTabNavGroup()
        self:_RegisterWarehouseNavGroup()
        self._wtPanelMask:Visible()
    else
        self:_RemoveNavGroup()
        self:_RegisterNavGroup()
        self._wtPanelMask:Collapsed()
    end
end

-- 如果进入出售模式时，没有item可以聚焦，那么仅注册Tab导航组并聚焦回sell按钮
function WarehouseMain_HD:_OnSellModeFocusFirstItemFailed()
    -- self:_RemoveNavGroup()
    -- self:CreateWarhouseTabNavGroup()
    -- self._wtPanelMask:Visible()
    -- if self._CreateTabNavGroupTimerHandle then
    --     self._CreateTabNavGroupTimerHandle:Start()
    --     return
    -- end
    -- self._CreateTabNavGroupTimerHandle = Timer:NewIns(0.02, 0)
    -- self._CreateTabNavGroupTimerHandle:AddListener(self._CreateWarhouseTabNavGroupProxy,self)
    -- self._CreateTabNavGroupTimerHandle:Start()
    -- WidgetUtil.SetUserFocusToWidget(targetItemView, true)
    -- return true
    
end

function WarehouseMain_HD:_GetExtSubPanel()
    if not self._wtExtSubPanel then
        self._wtExtSubPanelRoot = self:Wnd("wtExtSubPanelRoot", UIWidgetBase)
        local extSubWeakIns = Facade.UIManager:AddSubUI(self, UIName2ID.WarehouseExtSubPanel, self._wtExtSubPanelRoot)
        local extSubPanel = getfromweak(extSubWeakIns)
        if extSubPanel then
            self._wtExtSubPanel = extSubPanel
            UIUtil.SetWidgetToParent_Full(extSubPanel, self._wtExtSubPanelRoot)
        end
    end

    return self._wtExtSubPanel
end

function WarehouseMain_HD:_OnChangeWarehouseTitle(name)
    local titleName = name
    -- 更改仓库标题名
    self._wtWarehouseTitle:SetText(titleName)
end

---@param dragDropInfo ItemDragDropInfo
function WarehouseMain_HD:_OnGlobalItemDragStart(dragDropInfo, operation, pointerEvent)
    Module.CommonWidget:RegisterAutoSnapArea(self._wtAutoSnapArea)

    local item = dragDropInfo.item
    local inSlot = item.InSlot
    if not inSlot then
        local rootGunItem = item:TryGetRootGunItem()
        if rootGunItem then
            inSlot = rootGunItem.InSlot
        end
    end
    if not inSlot then
        return
    end

    local bInLeft
    if inSlot:IsDepositorySlot() then
        bInLeft = false
    else
        bInLeft = true
    end

    if bInLeft then
        local geometry = self._wtEquipPanel:GetCachedGeometry()
        local right = geometry:GetAbsolutePositionAtCoordinates(LuaGlobalConst.CENTER_RIGHT_VECTOR)
        local rootGeometry = self._wtRootCanvas:GetCachedGeometry()
        local localRight = rootGeometry:AbsoluteToLocal(right)

        local size = geometry:GetAbsoluteSize()
        self._defaultOffset.Right = 0
        self._defaultOffset.Left = localRight.X
    else
        local warehouseWidget = self._wtWarehousePanel:GetWarehouseWidget()
        local geometry = warehouseWidget:GetCachedGeometry()
        local left = geometry:GetAbsolutePositionAtCoordinates(LuaGlobalConst.CENTER_LEFT_VECTOR)
        local rootGeometry = self._wtRootCanvas:GetCachedGeometry()
        local localLeft = rootGeometry:AbsoluteToLocal(left)
        local size = rootGeometry:GetLocalSize()
        self._defaultOffset.Right = size.X - localLeft.X
        self._defaultOffset.Left = 0
    end

    self._autoSnapSlot:SetOffsets(self._defaultOffset)

    --BEGIN MODIFICATION @ VIRTUOS : 
    if IsHD() then
        self:_UpdateInputSummaryOnDrag(true, true)
        Module.CommonWidget:SetShouldTryFocusPostDrag(true)
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default,self)
    end
    --END MODIFICATION
end

function WarehouseMain_HD:_OnGlobalItemDrop()
    Module.CommonWidget:UnregisterAutoSnapArea(self._wtAutoSnapArea)

    --BEGIN MODIFICATION @ VIRTUOS : 
    if IsHD() then
        Module.CommonWidget:SetShouldTryFocusPostDrag(false)
    end
    --END MODIFICATION
end

function WarehouseMain_HD:_OnGlobalItemDragCancelled()
    Module.CommonWidget:UnregisterAutoSnapArea(self._wtAutoSnapArea)

    --BEGIN MODIFICATION @ VIRTUOS : 
    if IsHD() then
        Module.CommonWidget:SetShouldTryFocusPostDrag(false)
    end
    --END MODIFICATION
end

function WarehouseMain_HD:_AutoScrollVisible(bDirection, bVisible)
    if bDirection then
        self._wtMainTitleItem = self:Wnd("DFHorizontalBox_378", UIWidgetBase)
        if bVisible then
            self._wtMainTitleItem:Visible()
            Module.CommonBar:SetCurrencyVisible(true)
        else
            self._wtMainTitleItem:Collapsed()
            Module.CommonBar:SetCurrencyVisible(false)
        end
    else
        -- self._wtValuePanel = self:Wnd("value", UIWidgetBase)
        -- if bVisible then
        --     self._wtValuePanel:Visible()
        -- else
        --     self._wtValuePanel:Collapsed()
        -- end
    end
end

function WarehouseMain_HD:_OnReconnected()
    -- 断线重连后会重新拉取仓库数据，此时需要退出仓库，避免状态不一致
    Facade.UIManager:PopStackUI()
end

function WarehouseMain_HD:_UpdateAllItemGids()
    -- 首先获取本地中玩家获得的道具
    local allItemGids = Server.InventoryServer:GetAllItemGids()
	local tempItemGids = Server.InventoryServer.newItemGids

	for _, gid in ipairs(tempItemGids) do
		-- 将所有新添加的道具Gid且未加入AllItemGids的道具Gid添加至AllItemGids中
        table.insert(allItemGids, gid)
	end
    Server.InventoryServer:SetAllItemGids(allItemGids)
	-- 清空
	Server.InventoryServer.newItemGids = {}

    Facade.ConfigManager:SetUserBoolean("bFirstInWH", false)
end

function WarehouseMain_HD:_SetTopBarTitle()
    if self._bOpenFromArmedForce then
        Module.CommonBar:ChangeBackBtnText(Module.ArmedForce.Config.Loc.BagTitle)
    else
        Module.CommonBar:ChangeBackBtnText(InventoryConfig.Loc.WarehouseBackBtnTitle)
    end
    self:_RefreshWarehouseLevel()
end

-- function WarehouseMain_HD:_ShowHeroIcon()
--     local heroIdStr = Server.HeroServer:GetCurUsedHeroId()
--     self._wtHeroIcon:AsyncSetImagePath(InventoryConfig.HeroId2Image[heroIdStr])
-- end

function WarehouseMain_HD:_JumptoMerchantMain()
    self._isJumptoMerchant = true
end

function WarehouseMain_HD:_CreateCaptureUtil()
    self._wtCaptureImgRoot = self:Wnd("wtCaptureImgRoot", UIWidgetBase)
    local uiWeakIns = Facade.UIManager:AddSubUI(self, UIName2ID.CharacterCaptureUtil, self._wtCaptureImgRoot)
    local ui = getfromweak(uiWeakIns)
    if ui then
        ---@type CharacterCaptureUtil
        self._wtCaptureImg = ui
        UIUtil.SetWidgetToParent_Full(self._wtCaptureImg, self._wtCaptureImgRoot)
    end
end

function WarehouseMain_HD:_RefreshCharacterAppearance()
    self._wtCaptureImg:RefreshCharacterAppearance()
end

-- 转移全部按钮
function WarehouseMain_HD:_OnTransferAllBtnClicked()
    LogAnalysisTool.SignButtonClicked(10060006)
    LogAnalysisTool.DoSendItemOperateReport(ItemOperateActionMethod.ItemOperateActionMethod_CtrlF)
    local items2Transfer = self._wtEquipPanel:_GetItems2Transfer()
    if #items2Transfer == 0 then
        Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.WarehouseNothing2Transfer)
        InventoryConfig.Events.evtWareHouseTransferEnd:Invoke()
        return
    end

    Module.Inventory.Config.Events.evtSaveExpansionNum:Invoke()
    local toDepositIds = Server.InventoryServer:GetAllDepositIds()
    local toDepositMainSlot = Server.InventoryServer:GetSlot(ESlotType.MainContainer)
    local isTransferSeccess
    ItemMoveCmd.LastMoveOperation = ItemMoveCmd.EFromOperation.ClearBag
    for _, item in ipairs(items2Transfer) do
        local bCanTrans = ItemOperaTool.DoPlaceItem(item, toDepositMainSlot, false)
        if not bCanTrans then
            -- 寻找其他扩容箱
            for _, depositId in pairs(toDepositIds) do
                local otherDepositSlot = Server.InventoryServer:GetSlot(depositId)
                if ItemOperaTool.DoPlaceItem(item, otherDepositSlot, false) then
                    isTransferSeccess = true
                end
            end
        else
            isTransferSeccess = true
        end
    end

    if not isTransferSeccess then
        local bReady, readyState = Server.MatchServer:GetIsReadytoGo()
        if bReady and not bValidInMatch then
            if readyState == 1 then
                Module.CommonTips:ShowSimpleTip(ItemMoveCmd.allItemMoveTip.InMatching)
            elseif readyState == 2 then
                Module.CommonTips:ShowSimpleTip(ItemMoveCmd.allItemMoveTip.InReadying)
            end
            return false
        else
            Module.CommonTips:ShowSimpleTip(Module.Inventory.Config.Loc.InventorySpaceLacking)
        end
        return
    end

    local fTransferCallback = function(res)
        if res.result == 0 then
            Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.WarehouseTransferAllSuccess)
            Module.Inventory.Config.Events.evtCompareExpansionNum:Invoke()
        else
            Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.WarehouseTransferAllFail)
        end
        InventoryConfig.Events.evtWareHouseTransferEnd:Invoke(res)
    end
    Server.InventoryServer:SyncInventoryChanged(fTransferCallback, {allow_partly = true, is_transfer_all = true})
end

function WarehouseMain_HD:_SetInputSummary(inputSummaryList)
    if self._delayInputSummaryListHandle then
        Timer.CancelDelay(self._delayInputSummaryListHandle)
        self._delayInputSummaryListHandle = nil
    end
    self.inputSummaryList = inputSummaryList
    self._delayInputSummaryListHandle = Timer.DelayCall(0.01, self._InternalSetInputSummary, self, inputSummaryList)
end

function WarehouseMain_HD:_InitSpecialOperate()
    Timer.DelayCall(0.4, self._EnterExtArrangeMode, self)
end

function WarehouseMain_HD:_EnterExtArrangeMode()
    local bEnterSetting = Module.Inventory.Field:IsEnterSetting()
    if bEnterSetting then
        -- 从设置界面返回会播放WBP_WarehouseMain_in动效导致整理状态的不透明度异常，所以需要延时处理
        self._wtWarehousePanel:_StartExtArrange()
        Module.Inventory.Field:SetInSettingPanel(false)
    end
end

function WarehouseMain_HD:_FlushInputSummary()
    if self._delayInputSummaryListHandle then
        Timer.CancelDelay(self._delayInputSummaryListHandle)
        self._delayInputSummaryListHandle = nil
    end
    if self._inputSummaryList then

        self:_InternalSetInputSummary(self._inputSummaryList)
    end
end

function WarehouseMain_HD:_BindAdditonDisplayAction()
    if IsHD() then
    local inputMonitor = Facade.UIManager:GetInputMonitor()
    local handle = inputMonitor:AddDisplayActionBinding("Warehouse_MoveFocusArea_L", EInputEvent.IE_Pressed, self.MoveFocusAreaToLeft, self, EDisplayInputActionPriority.UI_Stack)
    table.insert(self._AddtionalDisplayAction, handle)
    handle = inputMonitor:AddDisplayActionBinding("Warehouse_MoveFocusArea_R", EInputEvent.IE_Pressed, self.MoveFocusAreaToRight, self, EDisplayInputActionPriority.UI_Stack)
    table.insert(self._AddtionalDisplayAction, handle)
    end
end

function WarehouseMain_HD:_UnBindAdditonDisplayAction()
    if IsHD() then
        local inputMonitor = Facade.UIManager:GetInputMonitor()
        for _, handle in ipairs(self._AddtionalDisplayAction) do
            inputMonitor:RemoveDisplayActoinBingingForHandle(handle)
        end
        self._AddtionalDisplayAction = {}
    end
end
    
-- 单帧调用多次
function WarehouseMain_HD:_InternalSetInputSummary(inputSummaryList)
    --BEGIN MODIFICATION @ VIRTUOS : 用手柄 整理 和 交换物品时不通过此方法更新按键提示
    if WidgetUtil.IsGamepad() then
        local bIsInExtArrangeMode = self._wtWarehousePanel._bInExtArrangeMode == true
        local bIsInSwapingItem = Module.CommonWidget:IsInSwapingItem() == true
        if bIsInExtArrangeMode or bIsInSwapingItem then
            if inputSummaryList then
                -- 不更新但记录最后传入的数据
                self.inputSummaryList = inputSummaryList
            end

            return
        end
        -- 出售模式直接传递给子面板更新
        if self._wtWarehousePanel._wtWarehouse:GetIsInMultiSellMode() then
            local summaryList = {}
            -- table.insert(summaryList, {actionName = "FocusWarehouseTab", func = self.FocusWarehouseTab, caller = self ,bUIOnly = false, bHideIcon = false})
            self._wtWarehousePanel:SetSellModeInputSummary(summaryList)
            return
        end
    end

    --END MODIFICATION
    Module.CommonBar:RecoverBottomBarInputSummaryList()

    local summaryList = {}
    local bSkipByPop = false
    bSkipByPop =not (Module.CommonWidget:GetOpenedPopItemView() ~= nil or Module.ItemDetail:GetIsSplitPanelOpened())
    --BEGIN MODIFICATION @ VIRTUOS : 手柄增加整理按键提示
    if Module.CommonWidget.GetOpenedPopItemView() == nil then
        -- 有popui时不提示该项目
        if not (Module.CommonWidget:GetOpenedPopItemView() ~= nil or Module.ItemDetail:GetIsSplitPanelOpened()) then
            table.insert(summaryList, {actionName = "ConfirmArrange_Gamepad", func = self._wtWarehousePanel._OnExtArrangeBtnPress, caller = self._wtWarehousePanel ,bUIOnly = false, bHideIcon = false})
            
            -- table.insert(summaryList, {actionName = "Warehouse_MoveFocusArea_L", func = self.MoveFocusAreaToLeft, caller = self ,bUIOnly = false, bHideIcon = false})
            -- table.insert(summaryList, {actionName = "Warehouse_MoveFocusArea_R", func = self.MoveFocusAreaToRight, caller = self ,bUIOnly = false, bHideIcon = false})
            -- 仓库L3聚焦
            -- table.insert(summaryList, {actionName = "FocusWarehouseTab", func = self.FocusWarehouseTab, caller = self ,bUIOnly = false, bHideIcon = false})
            -- 清空背包
            table.insert(summaryList, {actionName = "TransferAll_Gamepad", func = self._OnTransferAllBtnClicked, caller = self ,bUIOnly = false, bHideIcon = false})

            -- if self._EnableTipsInEquipBox ==true then
            --     table.insert(summaryList, {actionName = "Inventory_ToggleTips", func = self._wtEquipPanel.ShowHoverTips, caller = self._wtEquipPanel ,bUIOnly = false, bHideIcon = false})
            -- end
        else
            -- 有popui时直接返回，放置虚拟光标悬停触发的更新逻辑影响到popui创建的快捷键
             return
        end

    else
        table.insert(summaryList, {actionName = "Back_Gamepad", func = nil, caller = self ,bUIOnly = true, bHideIcon = false})
        table.insert(summaryList, {actionName = "Select_Gamepad", func = nil, caller = self ,bUIOnly = true, bHideIcon = false})
        -- table.insert(summaryList, {actionName = "CarryItem", func = nil, caller = self ,bUIOnly = true, bHideIcon = false})
    end
    --END MODIFICATION

    -- 清空背包
    -- 临时屏蔽出售模式itemview unhovered 时会添加转移全部快捷键的逻辑
    if not self._wtWarehousePanel._wtWarehouse:GetIsInMultiSellMode() then
        table.insert(summaryList, {actionName = "TransferAll", func = self._OnTransferAllBtnClicked, caller = self ,bUIOnly = false, bHideIcon = false})
    else
        -- 【【ALL】【PC】【必现】仓库出售界面不应该能使用仓库内的所有快捷键，左下角快捷键提示错误】https://tapd.woa.com/tapd_fe/20421949/bug/detail/1020421949143770533
        --  临时处理手柄动态更新提示，影响了出售页面的静态提示
        if not WidgetUtil.IsGamepad() then
            self._wtWarehousePanel:SetSellModeInputSummary()
            return
        end
    end

    if inputSummaryList then
        for _, summary in pairs(inputSummaryList) do
            table.insert(summaryList, summary)
        end
    end
    self.inputSummaryList = nil
    Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, false)
end

function WarehouseMain_HD:_ShowValueTips()

    self:_HideValueTips()

    local contents = {}
    table.insert(contents, {id = UIName2ID.Assembled_CommonMessageTips_V2, data = {textContent = Module.Inventory.Config.Loc.AllValueText}})

    self.hoverHandle = Module.CommonTips:ShowAssembledTips(contents, self._wtDFTipsAnchor)
end
function WarehouseMain_HD:_HideValueTips()
    if self.hoverHandle then
        Module.CommonTips:RemoveAssembledTips(self.hoverHandle, self._wtDFTipsAnchor)
        self.hoverHandle = nil
    end
end

function WarehouseMain_HD:_CheckRentalStatus()
    local bIsRental = Server.ArmedForceServer:CheckIsRentalStatus()
    -- 因为遮罩需要根据不同的格子进行更新，所以需要每换一次物资券就刷新一次
    -- if self._bInRentalState == bIsRental then
    --     return
    -- end
    self._bInRentalState = bIsRental

    if bIsRental then
        self._wtRentalTips:SelfHitTestInvisible()
        self._wtRentalTips:PlayAnimation(self._wtRentalTips.Anim_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)

        self._wtCaptureImg:BindSlotGroup(ESlotGroup.MainRental)
    else
        self._wtRentalTips:Collapsed()

        self._wtCaptureImg:BindSlotGroup(ESlotGroup.Player)
    end
    self._wtEquipPanel:SetRentalState(bIsRental)
end

function WarehouseMain_HD:_OnRentalCancelBtnClicked()
    Module.ArmedForce:CancelRental()
end

--BEGIN MODIFICATION @ VIRTUOS : UI Navigation
function WarehouseMain_HD:_RegisterNavGroup()
    if not IsHD() then
        return
    end

    self._navGroupsMap = {}
    self._CurFocusNavGroupID = 0

    -- 角色装备
    local _wtPlayerEquipCanvasPanel = self._wtEquipPanel:Wnd("DFCanvasPanel_0", UIWidgetBase)
    if not self._navGroupPlayerEquip then
        self._navGroupPlayerEquip = WidgetUtil.RegisterNavigationGroup(self._wtRootCanvas, self, "Hittest")
        self._navGroupPlayerEquip.OnNavGroupFocusReceivedEvent:Add(self._OnPlayerEquipNavGroupFocusReceived, self)
    end
    InventoryNavManager.equipPanel = self._navGroupPlayerEquip
    if self._navGroupPlayerEquip then
        InventoryNavManager.equipNavGroup = self._navGroupPlayerEquip
        local wtEuqipSlotView_3 = self._wtEquipPanel:Wnd("WBP_WarehouseEquipSlotView_3", UIWidgetBase)
        local wtEuqipSlotView_5 = self._wtEquipPanel:Wnd("WBP_WarehouseEquipSlotView_5", UIWidgetBase)
        local wtEuqipSlotView_8 = self._wtEquipPanel:Wnd("WBP_WarehouseEquipSlotView_8", UIWidgetBase)
        local wtEuqipSlotView_9 = self._wtEquipPanel:Wnd("WBP_WarehouseEquipSlotView_9", UIWidgetBase)
        local wtEuqipSlotView_1 = self._wtEquipPanel:Wnd("WBP_WarehouseEquipSlotView_1", UIWidgetBase)
        local wtEuqipSlotView = self._wtEquipPanel:Wnd("WBP_WarehouseEquipSlotView", UIWidgetBase)

        self._navGroupPlayerEquip:AddNavWidgetToArray(wtEuqipSlotView_3)
        self._navGroupPlayerEquip:AddNavWidgetToArray(wtEuqipSlotView_5)
        self._navGroupPlayerEquip:AddNavWidgetToArray(wtEuqipSlotView_8)
        self._navGroupPlayerEquip:AddNavWidgetToArray(wtEuqipSlotView_9)
        self._navGroupPlayerEquip:AddNavWidgetToArray(wtEuqipSlotView_1)
        self._navGroupPlayerEquip:AddNavWidgetToArray(wtEuqipSlotView)

        -- 角色提示
        local wtDFCommonCheckBoxWithText = self._wtEquipPanel:Wnd("wtDFCommonCheckBoxWithText", UIWidgetBase)
        local wtSlotCompLoadBtn = self._wtEquipPanel:Wnd("WBP_SlotCompLoadBtn_PC", UIWidgetBase)

        self._navGroupPlayerEquip:AddNavWidgetToArray(wtDFCommonCheckBoxWithText)
        self._navGroupPlayerEquip:AddNavWidgetToArray(wtSlotCompLoadBtn)

        -- 制式套装提示
        self._navGroupPlayerEquip:AddNavWidgetToArray(self._wtRentalTips)

        self._navGroupPlayerEquip:SetAutoUseRootGeometrySize(false)
        -- self._navGroupPlayerEquip:SetCustomSimulateMouseKeyConfigName("LootingKeyConfig")
        self._navGroupPlayerEquip:SetNavSelectorWidgetVisibility(true)

        local PlayerEquipNavStrategy = self._navGroupPlayerEquip:GetOwnerNavStrategy()
        if PlayerEquipNavStrategy then
            PlayerEquipNavStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.HittestOrDistance)
        end

        table.insert(self._navGroupsMap, {navGroup = self._navGroupPlayerEquip, rootWidget = _wtPlayerEquipCanvasPanel})
    end

    -- 装备容器
    local _wtEquipVerticalBox = self._wtEquipPanel:Wnd("DFVerticalBox_0", UIWidgetBase)
    local _wtEuqipMainScrollBox = self._wtEquipPanel:Wnd("wtMainScrollBox", UIWidgetBase)
    if not self._navGroupEquipBox then
        self._navGroupEquipBox = WidgetUtil.RegisterNavigationGroup(_wtEquipVerticalBox, self, "Hittest")
        self._navGroupEquipBox.OnNavGroupFocusReceivedEvent:Add(self._OnEquipBoxNavGroupFocusReceived, self)
        self._navGroupEquipBox:SetAnalogCursorStickySlowdownFactor(0.8)
    end

    InventoryNavManager.backpackNavGroup = self._navGroupEquipBox
    if self._navGroupEquipBox then
        self._navGroupEquipBox:AddNavWidgetToArray(_wtEquipVerticalBox)
        self._navGroupEquipBox:SetScrollRecipient(_wtEuqipMainScrollBox)
        -- self._navGroupEquipBox:SetCustomSimulateMouseKeyConfigName("LootingKeyConfig")
        self._navGroupEquipBox:SetNavSelectorWidgetVisibility(true)
        
        local equipBoxNavStrategy = self._navGroupEquipBox:GetOwnerNavStrategy()
        if equipBoxNavStrategy then
            equipBoxNavStrategy:SetHitPadding(2.0)
            -- 关闭导航组中水平方向的保底导航
            equipBoxNavStrategy:SetHittestFallbackConfig(EGPUINavHittestFallbackType.Horizental, false, false)
            equipBoxNavStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.HittestOrDistance)
        end

        -- WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroupEquipBox)

        table.insert(self._navGroupsMap, {navGroup = self._navGroupEquipBox, rootWidget = _wtEquipVerticalBox})
    end

    self:CreateWarhouseTabNavGroup()

    -- 仓库
    local _wtWarehouse = self._wtWarehousePanel:Wnd("wtWarehouse", UIWidgetBase)
    local _wtWarehouseScrollBox = _wtWarehouse:Wnd("wtDepositScrollBox", UIWidgetBase)

    if not self._NavGroupWareHouse then
        self._NavGroupWareHouse = WidgetUtil.RegisterNavigationGroup(_wtWarehouse, self, "LuaExtension")
        -- self._NavGroupWareHouse = WidgetUtil.RegisterNavigationGroup(_wtWarehouse, self, "Hittest")
        self._NavGroupWareHouse.OnNavGroupFocusReceivedEvent:Add(self._OnEquipWareHouseFocusReceived, self)
        self._NavGroupWareHouse:SetAnalogCursorStickySlowdownFactor(0.8)
    end
    InventoryNavManager.warehousePanel = self._NavGroupWareHouse
    if self._NavGroupWareHouse then
        InventoryNavManager.warehouseNavGroup = self._NavGroupWareHouse
        self._NavGroupWareHouse:AddNavWidgetToArray(_wtWarehouse)
        self._NavGroupWareHouse:SetScrollRecipient(_wtWarehouseScrollBox)
        self._NavGroupWareHouse:SetNavSelectorWidgetVisibility(true)
        -- self._NavGroupWareHouse:SetCustomSimulateMouseKeyConfigName("LootingKeyConfig")
        
        local wareHouseNavStrategy = self._NavGroupWareHouse:GetOwnerNavStrategy()
        if wareHouseNavStrategy then
            wareHouseNavStrategy:SetHitPadding(2.0)
            wareHouseNavStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.HittestOrDistance)
            -- 关闭导航组中水平方向的保底导航
            -- wareHouseNavStrategy:SetHittestFallbackConfig(EGPUINavHittestFallbackType.Horizental, false, false)
            -- 启用TopLeft类型的默认聚焦位置
            wareHouseNavStrategy:SetBestFocusWidgetDefaultType(EGPUINavGroupBestDefaultType.TopLeft)
        end

        table.insert(self._navGroupsMap, {navGroup = self._NavGroupWareHouse, rootWidget = self._wtWarehousePanel})
    end
    -- local navMgr = UGPUINavigationManager.Get(GetGameInstance())
    -- if self._hNavigationChangedFocus == nil then
    --     self._hNavigationChangedFocus = navMgr.OnNavigationChangedFocusEvent:Add(self._OnNavigationChangedFocus, self)
    -- end
end

-- 注册单warehouse导航组
function WarehouseMain_HD:_RegisterWarehouseNavGroup()
 -- 仓库
    local _wtWarehouse = self._wtWarehousePanel:Wnd("wtWarehouse", UIWidgetBase)
    local _wtWarehouseScrollBox = _wtWarehouse:Wnd("wtDepositScrollBox", UIWidgetBase)

    if not self._NavGroupWareHouse then
        self._NavGroupWareHouse = WidgetUtil.RegisterNavigationGroup(_wtWarehouse, self, "LuaExtension")
        -- self._NavGroupWareHouse = WidgetUtil.RegisterNavigationGroup(_wtWarehouse, self, "Hittest")
        self._NavGroupWareHouse.OnNavGroupFocusReceivedEvent:Add(self._OnEquipWareHouseFocusReceived, self)
        self._NavGroupWareHouse:SetAnalogCursorStickySlowdownFactor(0.8)
    end
    InventoryNavManager.warehousePanel = self._NavGroupWareHouse
    if self._NavGroupWareHouse then
        InventoryNavManager.warehouseNavGroup = self._NavGroupWareHouse
        self._NavGroupWareHouse:AddNavWidgetToArray(_wtWarehouse)
        self._NavGroupWareHouse:SetScrollRecipient(_wtWarehouseScrollBox)
        self._NavGroupWareHouse:SetNavSelectorWidgetVisibility(true)
        -- self._NavGroupWareHouse:SetCustomSimulateMouseKeyConfigName("LootingKeyConfig")
        
        local wareHouseNavStrategy = self._NavGroupWareHouse:GetOwnerNavStrategy()
        if wareHouseNavStrategy then
            wareHouseNavStrategy:SetHitPadding(2.0)
            wareHouseNavStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.HittestOrDistance)
            -- 关闭导航组中水平方向的保底导航
            wareHouseNavStrategy:SetHittestFallbackConfig(EGPUINavHittestFallbackType.Horizental, false, false)
            -- 启用TopLeft类型的默认聚焦位置
            wareHouseNavStrategy:SetBestFocusWidgetDefaultType(EGPUINavGroupBestDefaultType.TopLeft)
        end

        table.insert(self._navGroupsMap, {navGroup = self._NavGroupWareHouse, rootWidget = self._wtWarehousePanel})
    end
    -- local navMgr = UGPUINavigationManager.Get(GetGameInstance())
    -- if self._hNavigationChangedFocus == nil then
    --     self._hNavigationChangedFocus = navMgr.OnNavigationChangedFocusEvent:Add(self._OnNavigationChangedFocus, self)
    -- end
    _wtWarehouse.WarehouseNavGroup = self._NavGroupWareHouse
    if self._NavGroupWareHouse ~= nil then
        -- WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroupWareHouse)
    end
end

function WarehouseMain_HD:_RemoveNavGroup()
    if not IsHD() then
        return 
    end

    if self._navGroupPlayerEquip then
        self._navGroupPlayerEquip:ClearCustomSimulateMouseKeyConfigName()
        self._navGroupPlayerEquip = nil
    end

    if self._NavGroupPlayerTips then
        self._NavGroupPlayerTips = nil
    end

    if self._navGroupEquipBox then
        self._navGroupEquipBox:ClearCustomSimulateMouseKeyConfigName()
        self._navGroupEquipBox = nil
    end

    if self._navGroupWarehouseTab then
        self._navGroupWarehouseTab = nil
    end

    if self._NavGroupWareHouse then
        self._NavGroupWareHouse:ClearCustomSimulateMouseKeyConfigName()
        self._NavGroupWareHouse = nil
    end

    local navMgr = UGPUINavigationManager.Get(GetGameInstance())

    -- if self._hNavigationChangedFocus then
    --     navMgr.OnNavigationChangedFocusEvent:Remove(self._hNavigationChangedFocus)
    --     self._hNavigationChangedFocus = nil
    -- end

    WidgetUtil.RemoveNavigationGroup(self)
end

function WarehouseMain_HD:_IsUsingGamepad()
    local curInputType = UGPInputHelper.GetCurrentInputType(self)
    local bIsGamepad = (curInputType == EGPInputType.Gamepad)
    return bIsGamepad
end

function WarehouseMain_HD:_OnGlobalSwapItem(bIsSwapItem)
    if not IsHD() then
        return 
    end
    self:_UpdateInputSummaryOnDrag(bIsSwapItem, false)

    UDFNavigationSelectorBase.SetForceHideSelectorRoot(bIsSwapItem)

    if WidgetUtil.IsGamepad() and bIsSwapItem == true then
        -- 在手柄拖拽时不希望导航到这些按钮
        self:_SetEnableHeroInfoBtns(false)
    else
        self:_SetEnableHeroInfoBtns(true)
    end
end

function WarehouseMain_HD:_UpdateInputSummaryOnDrag(bIsDragging, bIsItemDragStart)
    -- logwarning("yanmingjing_UpdateInputSummaryOnDrag bIsDragging: " .. tostring(bIsDragging) .. " bIsItemDragStart: " .. tostring(bIsItemDragStart))
    if not IsHD() then
        return 
    end

    if bIsDragging then
        Module.CommonWidget:UnBindHoverViewDisplayInput()

        local summaryList = {}
        -- 交换物品
        table.insert(summaryList, {actionName = "SimulateDropItem_Gamepad", func = nil, caller = self ,bUIOnly = true, bHideIcon = false})
        table.insert(summaryList, {actionName = "WareHouseExitSwapItem", func = self._ExitSwapItemMode, caller = self ,bUIOnly = false, bHideIcon = false})

        if bIsItemDragStart then
            table.insert(summaryList, {actionName = "RotateItemOnDrag_Gamepad", func = self.ToggleDragItemSpinByGamepad, caller = self ,bUIOnly = false, bHideIcon = false})
            table.insert(summaryList, {actionName = "SimulateDropItem_Gamepad", func = WidgetUtil.StopSimulateMouseDrag, caller = self ,bUIOnly = false, bHideIcon = false})
        end

        -- table.insert(summaryList, {actionName = "Warehouse_MoveFocusArea_L", func = self.MoveFocusAreaToLeft, caller = self ,bUIOnly = false, bHideIcon = false})
        -- table.insert(summaryList, {actionName = "Warehouse_MoveFocusArea_R", func = self.MoveFocusAreaToRight, caller = self ,bUIOnly = false, bHideIcon = false})

        Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, true, false)
    else
        Module.CommonWidget:BindHoverViewDisplayInput()
    end
end

function WarehouseMain_HD:_OnGlobalItemPopViewOpened()
    self:_InternalSetInputSummary()
end


function WarehouseMain_HD:_ExitSwapItemMode()
    if Module.CommonWidget:IsInSwapingItem() then
        Module.CommonWidget:SetGamepadSwapItem(false)
    end
    -- she3移入InventoryNavManager统一控制
    Module.Inventory.Config.Events.evtRemoveYModeBlankItemVisibility:Invoke()
    Module.Inventory.Config.Events.evtUpdateFirstRowBlankItemVisibility:Invoke(false)
    Module.Inventory.Config.Events.evtUpdateAllBlankItemVisibility:Invoke(false)
end

-- 绑定输入类型切换事件
function WarehouseMain_HD:_EnableInputTypeChangedHandle(bEnable)
    if not IsHD() then
        return 
    end

    if bEnable then
        if not self._onNotifyInputTypeChangedHandle then
            self._onNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._HandleInputTypeChanged, self))
        end
    else
        if self._onNotifyInputTypeChangedHandle then
            UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._onNotifyInputTypeChangedHandle)
            self._onNotifyInputTypeChangedHandle = nil
        end
    end
end

-- 切换到非手柄时直接隐藏Tip
function WarehouseMain_HD:_HandleInputTypeChanged(inputType)
    if not IsHD() then
        return 
    end

    -- 从手柄切到键鼠
    if not (inputType == EGPInputType.Gamepad) then
        -- 退出手柄移动物品状态
        self:_ExitSwapItemMode()

        -- 关闭手柄打开的HoverTips
        if self._wtEquipPanel and self._wtEquipPanel.CloseHoverTips then
            self._wtEquipPanel:CloseHoverTips()
        end
    end

    -- WarehousePanel
    self._wtWarehousePanel:OnInputTypeChanged(inputType)
end

-- 绑定手柄模拟鼠标左键拖拽事件
function WarehouseMain_HD:_EnableSimulatedMouseDragHandle(bEnable)
    logwarning("yanmingjing:_EnableSimulatedMouseDragHandle:", bEnable)
    if not IsHD() then
        return 
    end

    if bEnable then
        if not self._onNotifySimulatedMouseDragHandle then
            self._onNotifySimulatedMouseDragHandle = WidgetUtil.BindSimulatedMouseDragEvent(self._OnGlobalSwapItem, self)
        end
    else
        if self._onNotifySimulatedMouseDragHandle then
            WidgetUtil.UnBindSimulatedMouseDragEvent(self._onNotifySimulatedMouseDragHandle)
            self._onNotifySimulatedMouseDragHandle = nil
        end
    end
end

-- 手柄快速切换聚焦区域
function WarehouseMain_HD:MoveFocusAreaToLeft()
    self:MoveFocusAreaByIndex(true)
end

function WarehouseMain_HD:MoveFocusAreaToRight()
    self:MoveFocusAreaByIndex(false)
end

function WarehouseMain_HD:MoveFocusAreaByIndex(bIsLeft)
    if not IsHD() then
        return 
    end
    
    -- 有pop弹窗时不可用
    if Module.CommonWidget:GetOpenedPopItemView() ~= nil or Module.ItemDetail:GetIsSplitPanelOpened() == true then
        return
    end

    -- 使用虚拟光标时不可用
    if WidgetUtil.IsUsingFreeAnalogCursor() == true then
        return
    end

    local enableNavGroupMap = {}

    local curSelectNavGroupIndex = 0
    local bCurrentFocusInMainNavGroup = false
    for index, value in ipairs(self._navGroupsMap) do
        local navGroup = value.navGroup
        if navGroup and navGroup:IsGroupEnabled() == true then

            table.insert(enableNavGroupMap, value)

            local navGroupRootWidget = value.rootWidget
            if navGroupRootWidget then
                if UGPUINavigationUtils.IsWidgetInFocusPath(0, navGroupRootWidget) == true then
                    curSelectNavGroupIndex = #enableNavGroupMap - 1
                    bCurrentFocusInMainNavGroup = true
                end
            end
        end

    end

    local nextNavStep = 0
    if bCurrentFocusInMainNavGroup == true then
        if bIsLeft == true then
            nextNavStep = -1
        else
            nextNavStep = 1
        end
    end
    
    local nextTargetGroupIndex = (curSelectNavGroupIndex + nextNavStep + #enableNavGroupMap) % #enableNavGroupMap
    nextTargetGroupIndex = nextTargetGroupIndex + 1

    local nextNavGroupMap = enableNavGroupMap[nextTargetGroupIndex]
    if nextNavGroupMap and nextNavGroupMap.navGroup then
        -- WidgetUtil.TryFocusDefaultWidgetByGroup(nextNavGroupMap.navGroup)
        InventoryNavManager.TryFocusAfterSwitchTab(nextNavGroupMap.navGroup)
    end
end

function WarehouseMain_HD:ToggleDragItemSpinByGamepad()
    if not IsHD() then
        return 
    end

    if Module.CommonWidget:IsInSwapingItem() == true then
        Module.CommonWidget:ToggleDragItemSpin()
    end
end

function WarehouseMain_HD:_SetEnableHeroInfoBtns(bEnable)
    if not IsHD() then
        return
    end

    local wtDFCommonCheckBoxWithText = self._wtEquipPanel:Wnd("wtDFCommonCheckBoxWithText", UIWidgetBase)
    local wtSlotCompLoadBtn = self._wtEquipPanel:Wnd("WBP_SlotCompLoadBtn_PC", UIWidgetBase)

    if wtDFCommonCheckBoxWithText then
        wtDFCommonCheckBoxWithText:SetIsEnabled(bEnable)
    end

    if wtSlotCompLoadBtn then
        wtSlotCompLoadBtn:SetIsEnabled(bEnable)
    end
end
--END MODIFICATION

--//TODO 尝试处理长按

function WarehouseMain_HD:GetIconWidgetByAction(actionName)
    if actionName == nil then
        return nil
    end

    for _, config in ipairs(self.defaultKeyBindings) do
        if config.ActionOrAxisName == actionName then
            return self.mapCfg2KeyIconWidgets[config]
        end
    end

    return nil
end

function WarehouseMain_HD:BindIconWidgetHolding(actionName, bDisplayIpnut, actionHandle)
    if actionName == nil or actionHandle == nil then
        return
    end

    if bDisplayIpnut == true then
        -- local targetIconWidget = self:GetIconWidgetByAction(actionName)
        -- if targetIconWidget and targetIconWidget.SetLongPressBindingByInputMonitor then
        --     targetIconWidget:SetLongPressBindingByInputMonitor(actionHandle)
        -- end
        targetIconWidget:SetLongPressBindingByInputMonitor(actionHandle)
    end
end

function  WarehouseMain_HD:UnBindIconWidgetHolding(actionName, bDisplayIpnut)
    if actionName == nil then
        return
    end

    if bDisplayIpnut == true then
        -- local targetIconWidget = self:GetIconWidgetByAction(actionName)
        -- if targetIconWidget and targetIconWidget.HideLongPressKeyIconState then
        --     targetIconWidget:HideLongPressKeyIconState()
        -- end
        targetIconWidget:HideLongPressKeyIconState()
    end
end

function WarehouseMain_HD:_DisableDynamicNavConfig()
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
end

function WarehouseMain_HD:_EnableShowTips()
    -- 当位于第二区时，才会显示Tips
    self._EnableTipsInEquipBox = true
end

function WarehouseMain_HD:_DisableShowTips()
    self._EnableTipsInEquipBox = false
end

function WarehouseMain_HD:_DisableShoulderTips()
    self._wtEquipPanel:ShowShoulderTips(false)
    self._wtEquipPanel:ShowShoulderTipsInEquipValue(false)
    self:ShowShoulderTips(false)
end

function WarehouseMain_HD:ShowShoulderTips(bShouldShow)
    if bShouldShow == true and not WidgetUtil.IsUsingFreeAnalogCursor() then
        self._wtLeftShoulderKey:SelfHitTestInvisible()
        self._wtLeftShoulderKey:SetOnlyDisplayOnGamepad(true)
        
        self._wtRightShoulderKey:SelfHitTestInvisible()
        self._wtRightShoulderKey:SetOnlyDisplayOnGamepad(true)
        
    else
        self._wtLeftShoulderKey:Collapsed()
        self._wtRightShoulderKey:Collapsed()
    end
end

function  WarehouseMain_HD:_OnPlayerEquipNavGroupFocusReceived()
    self:_DisableDynamicNavConfig()
    self:_DisableShoulderTips()
    self._wtEquipPanel:ShowShoulderTips(true)
    self._wtWarehousePanel:OnFocusTab(false)
    WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default,self)
    --  -- 焦点移出Tab时，立刻禁用Tab导航组
    --  if self._navGroupWarehouseTab then
    --     WidgetUtil.RemoveOneNavigationGroup(self._navGroupWarehouseTab)
    --     self._navGroupWarehouseTab = nil
    -- end
    self:_DisableShowTips()
end

function  WarehouseMain_HD:_OnEquipBoxNavGroupFocusReceived()
    self:_DisableDynamicNavConfig()
    self:_DisableShoulderTips()
    self._wtEquipPanel:ShowShoulderTipsInEquipValue(true)
    -- NoA导航影响拖拽放下，不好通过调用OnClicked模拟
    if Module.CommonWidget:GetIsDragging() or self._wtWarehousePanel._wtWarehouse:GetIsInMultiSellMode() then
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default,self)
    else
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction,self)
    end
    self._wtWarehousePanel:OnFocusTab(false)
    --  -- 焦点移出Tab时，立刻禁用Tab导航组
    --  if self._navGroupWarehouseTab then
    --     WidgetUtil.RemoveOneNavigationGroup(self._navGroupWarehouseTab)
    --     self._navGroupWarehouseTab = nil
    -- end
    self:_EnableShowTips()
end

function  WarehouseMain_HD:_OnWarehouseTabNavGroupFocusReceived()
    self:_DisableDynamicNavConfig()
    self:_DisableShoulderTips()
    -- WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoLR,self)
    self._wtWarehousePanel:OnFocusTab(true)
    self:_DisableShowTips()
end

function  WarehouseMain_HD:_OnEquipWareHouseFocusReceived()
    self:_DisableDynamicNavConfig()
    self:_DisableShoulderTips()
    self:ShowShoulderTips(true)
    self._wtWarehousePanel:OnFocusTab(false)
    
    if Module.CommonWidget:IsInMultiSelectedMode() then
        -- 多选模式下使用默认按键响应点击
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default,self)
    else
        -- NoA导航影响拖拽放下，不好通过调用OnClicked模拟
        if Module.CommonWidget:GetIsDragging() then
            WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default,self)
        else
            WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction,self)
        end
    end

    -- 焦点移出Tab时，立刻禁用Tab导航组
    -- if self._navGroupWarehouseTab then
    --     WidgetUtil.RemoveOneNavigationGroup(self._navGroupWarehouseTab)
    --     self._navGroupWarehouseTab = nil
    -- end
    
    self:_DisableShowTips()
end

function  WarehouseMain_HD:FocusWarehouseTab()
    -- 当需要激活仓库选项栏时，激活导航组
    -- if self._navGroupWarehouseTab == nil then
    --     self:CreateWarhouseTabNavGroup()
    -- else
    --     if self._navGroupWarehouseTab then
    --         WidgetUtil.RemoveOneNavigationGroup(self._navGroupWarehouseTab)
    --         self._navGroupWarehouseTab = nil
    --     end
    -- end
end

function  WarehouseMain_HD:CreateWarhouseTabNavGroup()
    local _wtWarehouseTab = self._wtWarehousePanel:Wnd("wtWarehouseTab", UIWidgetBase)
    if not self._navGroupWarehouseTab then
        self._navGroupWarehouseTab = WidgetUtil.RegisterNavigationGroup(_wtWarehouseTab, self, "Hittest")
        self._navGroupWarehouseTab.OnNavGroupFocusReceivedEvent:Add(self._OnWarehouseTabNavGroupFocusReceived, self)
        self._navGroupWarehouseTab:SetAnalogCursorStickySlowdownFactor(0.8)
    end
    InventoryNavManager.warehouseTabPanel = self._navGroupWarehouseTab
    if self._navGroupWarehouseTab then
        WidgetUtil.BindCustomBoundaryNavRule(self._navGroupWarehouseTab, self._CustomBoundaryRule, self)
        local wtWarehouseTabDepositScrollBox = self._wtWarehousePanel:Wnd("wtDepositTabScrollBox", UIWidgetBase)
        self._navGroupWarehouseTab:AddNavWidgetToArray(_wtWarehouseTab)
        self._navGroupWarehouseTab:SetNavSelectorWidgetVisibility(true)
        self._navGroupWarehouseTab:SetScrollRecipient(wtWarehouseTabDepositScrollBox)
        
        local warehouseTabNavStrategy = self._navGroupWarehouseTab:GetOwnerNavStrategy()
        if warehouseTabNavStrategy then
            warehouseTabNavStrategy:SetHitPadding(2.0)
            warehouseTabNavStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.HittestOrDistance)
        end
        -- WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroupWarehouseTab)
    end
end

function  WarehouseMain_HD:_CreateWarhouseTabNavGroupProxy()
    if targetItemView ~= nil then
		WidgetUtil.SetUserFocusToWidget(self._wtWarehousePanel._wtSellBtn, true)
	end
	if self._CreateTabNavGroupTimerHandle then
        self._CreateTabNavGroupTimerHandle:Stop()
        self._CreateTabNavGroupTimerHandle = nil
    end
end

function  WarehouseMain_HD:_OnNavigationChangedFocus()
    self._wtEquipPanel:CloseHoverTips()
end

function  WarehouseMain_HD:_OnUsingFreeAnalogCursorStateChanged(bNewState)
    if bNewState == true then
        self:_DisableShoulderTips()
    end
end

function  WarehouseMain_HD:_CustomBoundaryRule(direction)
    -- local _wtWarehouseTab = self._wtWarehousePanel:Wnd("wtWarehouseTab", UIWidgetBase)
    local _wtWarehouseTab = self._wtWarehousePanel:Wnd("WBP_WarehouseExpansionButton_Pc", self)
    if direction ==  EUINavigation.Down then
        return _wtWarehouseTab
    elseif direction == EUINavigation.Up then
        return self._wtWarehousePanel._wtSellBtn
    else
        return nil
    end
end



-- function WarehouseEquipPanel_HD:BindDisplayInputs()
--     if WarehouseEquipPanel_HD.bBindDisplayAction then
--         CommonIVInputLogic.UnbindDisplayInputs()
--     end
--     handle = inputMonitor:AddDisplayActionBinding("SelectAndCheckItem", EInputEvent.IE_Pressed, CommonIVInputLogic.ProcessSimClick, nil, EDisplayInputActionPriority.UI_Stack)
--     table.insert(CommonIVInputLogic.DisplayInputHandles, handle)
-- end


--endregion
--==================================================

return WarehouseMain_HD
