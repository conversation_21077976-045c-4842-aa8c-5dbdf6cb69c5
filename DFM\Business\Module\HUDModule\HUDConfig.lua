----- <PERSON>O<PERSON> FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMHUD)
----- LOG FUNCTION AUTO GENERATE END -----------



--------------------------------------------------------------------------
--- HUD配置
--------------------------------------------------------------------------
HUDTable[HUDName2ID.BaseHUD] = {
    HUDTablePath = "",
    HUDConfigIDs = {
        UIName2ID.ChatMsgWidget,
        UIName2ID.GVoiceSpeakerWidget,
        UIName2ID.GVoiceMicWidget,
        UIName2ID.SpeakerListPanel,
        UIName2ID.AutoMsgMentionPanel,
        UIName2ID.SystemSettingButton,
        UIName2ID.PlayerBuffTips,
        -- UIName2ID.SoundVisualizationPanel,
        UIName2ID.VehicleEmptyRootView,
        UIName2ID.CommonSystemInfoView, -- 系统信息 Wifi和电量
        -- UIName2ID.StingerMissileAimingView_Igla, -- 针式导弹瞄准ui
        --UIName2ID.WeaponHoldBreathView, -- 屏息UI
        UIName2ID.StingerMissileCommonView, --毒刺不瞄准的另一个uis
        UIName2ID.PlayerBuffRemainTimeTip,
        UIName2ID.ItemDetailView,
        UIName2ID.ItemDetailViewEquippedInGame,
    }
}

HUDTable[HUDName2ID.Breakthrough] = {
    HUDTablePath = "",
    HUDConfigIDs = {
        -- UIName2ID.DeployPointPanelBreakthrough,
        -- UIName2ID.CapturePointCapView,
        -- UIName2ID.BattleFieldKillMarkerView,
        -- UIName2ID.BattleFieldDeployView,
        -- UIName2ID.BreakthroughRedeployViewGesture, --没有对应的定义，先注释掉
        -- UIName2ID.ArmdedForceSlot1View,
        -- UIName2ID.ArmdedForceSlot2View,
        -- UIName2ID.BattleFieldKillFeecbackMainView,--积分反馈
        -- UIName2ID.BreakthroughWaitPlayerTipsView,--攻防 Prematch 等待 %d 名玩家加入
        -- UIName2ID.BreakthroughPreMatchTeamInfoHUDView,--PreMatch队伍信息
        -- UIName2ID.BreakthroughScoreboardDetailsPanel, -- 计分板
    }
}

HUDTable[HUDName2ID.Conquest] = {
    HUDTablePath = "",
    HUDConfigIDs = {
        -- UIName2ID.CapturePointCapView,
        -- UIName2ID.BattleFieldDeployView,
        -- UIName2ID.ArmdedForceSlot1View,
        -- UIName2ID.ArmdedForceSlot2View,
        -- UIName2ID.BreakthroughWaitPlayerTipsView,--攻防 Prematch 等待 %d 名玩家加入
        -- UIName2ID.BreakthroughPreMatchTeamInfoHUDView,--PreMatch队伍信息
        -- UIName2ID.ConquestScoreboardDetailsPanel, -- 计分板
    }
}

--------------------------------------------------------------------------
--- PC HudTable 配置
--------------------------------------------------------------------------

if DFHD_LUA == 1 then
    HUDTable[HUDName2ID.BaseHUD] = {
        HUDTablePath = "",
        HUDConfigIDs = {
            -- UIName2ID.DeployPointPanelBattleField,
            -- UIName2ID.WeaponBarHudView_PC,
            UIName2ID.TipsReload_PC,
            UIName2ID.MsgPanel_PC,            
            UIName2ID.PlayerBuffTips_PC,
            UIName2ID.PlayerBuffRemainTimeTip,
            UIName2ID.VehicleEmptyRootView,
            -- UIName2ID.StingerMissileAimingView_Igla, -- 针式导弹瞄准ui
            -- UIName2ID.WeaponHoldBreathView, -- 屏息UI
            UIName2ID.BuffAndStaminaViewPC,
            UIName2ID.ItemDetailView,
            UIName2ID.ItemDetailViewEquippedInGame,
            UIName2ID.HDKeyIconBoxText,
        }
    }
    
    HUDTable[HUDName2ID.Breakthrough] = {
        HUDTablePath = "",
        HUDConfigIDs = {
            -- UIName2ID.DeployPointPanelBreakthrough,
            -- UIName2ID.CapturePointCapView,
            -- UIName2ID.BattleFieldKillMarkerView,
            -- UIName2ID.BattleFieldDeployView,
            -- UIName2ID.BreakthroughRedeployView,
            -- UIName2ID.BreakthroughRedeployViewGesture,
            --region 道具快捷轮盘
            -- UIName2ID.RouletteMain,
            --UIName2ID.ArmdedForceItemViewPC,
            --endregion
            -- UIName2ID.BattleFieldKillFeecbackMainView,--积分反馈
            -- UIName2ID.BreakthroughPreMatchTeamInfoHUDView,--PreMatch队伍信息
            -- UIName2ID.BreakthroughScoreboardDetailsPanel, -- 计分板
            
        }
    }
    
    HUDTable[HUDName2ID.Conquest] = {
        HUDTablePath = "",
        HUDConfigIDs = {
            -- UIName2ID.CapturePointCapView,
            -- UIName2ID.BattleFieldDeployView,
            -- UIName2ID.BreakthroughRedeployView,
            -- UIName2ID.BreakthroughPreMatchTeamInfoHUDView,--PreMatch队伍信息
            -- UIName2ID.ConquestScoreboardDetailsPanel, -- 计分板
        }
    }
end 


--------------------------------------------------------------------------
--- UI配置
--------------------------------------------------------------------------
UITable[UIName2ID.Jump] = {
    UILayer = EUILayer.HUD,
    LuaPath = "DFM.Business.Module.HUDModule.UI.MobileJumpButtonView",
    BPKey = "WBP_ControllerButtonBase",
    HUDName = "MobileJump",
    ZOrderBase = EHUDBaseZorder.Common,
    ZOrderOffset = 0,
}

--UITable[UIName2ID.UsingItemTipsHudView] = {
--    UILayer = EUILayer.HUD_Hint,
--    LuaPath = "DFM.Business.Module.HUDModule.UI.UsingItemTipsHudView",
--    BPKey = "WBP_TipsPro_UseItem",
--}

----------------Character Hud
UITable[UIName2ID.CharacterArmorView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.ArmorView.CharacterArmorView",
    BPKey = "H_Common_Panel_Armor",
    HUDName = "BP_MobileHUD_CharacterArmor",
    ZOrderBase = EHUDBaseZorder.Common,
    ZOrderOffset = 0,
}

UITable[UIName2ID.CharacterArmorHudDetailView] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.HUDModule.UI.ArmorView.CharacterArmorHudDetailView",
    BPKey = "WBP_ToolbarArmorDetails_ToolBar",
    SubUIs={
        UIName2ID.CharacterArmorHudDetailItem
    }
}

UITable[UIName2ID.CharacterArmorHudDetailItem] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.HUDModule.UI.ArmorView.CharacterArmorHudDetailItem",
    BPKey = "WBP_ToolbarArmorDetailItem_ToolBar",
}

-- UITable[UIName2ID.ArmdedForceItemViewPC] = {
--     UILayer = EUILayer.HUD,
--     LuaPath = "DFM.Business.Module.HUDModule.UI.Common.ArmdedForceItemViewPC",
--     BPKey = "WBP_ControllerRouletteEquip_PC_NotUse",
--     HUDName = "ArmdedForceItemViewPC",
--     ZOrderBase = EHUDBaseZorder.Common,
--     ZOrderOffset = 100, -- 高于武器栏
--     bInSafeZone = false,
--     bCreateOnDemand = false,
--     Desc = "PC Armed Force Item View"
-- }

-- UITable[UIName2ID.SkillItemViewPC] = {
--     UILayer = EUILayer.HUD,
--     LuaPath = "DFM.Business.Module.HUDModule.UI.Common.SkillItemViewPC",
--     BPKey = "WBP_ControllerRouletteKill_PC",
--     HUDName = "SkillItemViewPC",
--     ZOrderBase = EHUDBaseZorder.Common,
--     ZOrderOffset = 100, -- 高于武器栏
--     bInSafeZone = false,
--     bCreateOnDemand = false,
--     Desc = "PC Armed Force Item View"
-- }

-- UITable[UIName2ID.ArmdedForceSlot2ViewPC] = {
--     UILayer = EUILayer.HUD,
--     LuaPath = "DFM.Business.Module.HUDModule.UI.Common.ArmdedForceSlot2ViewPC",
--     BPKey = "WBP_ControllerRouletteEquip2_PC_NotUse",
--     HUDName = "ArmdedForceSlot2ViewPC",
--     ZOrderBase = EHUDBaseZorder.Common,
--     ZOrderOffset = 100, -- 高于武器栏
--     bInSafeZone = false,
--     bCreateOnDemand = false,
--     Desc = "PC Armed Force Item View"
-- }

-- UITable[UIName2ID.StingerMissileAimingView] = {
--     UILayer = EUILayer.HUD,
--     LuaPath = "DFM.Business.Module.HUDModule.UI.WeaponBar.StingerMissileAimingView",
--     BPKey = "WBP_SpecialAiming_stinger",
--     HUDName = "WBP_StingerMissileAimingView",
--     ZOrderBase = EHUDBaseZorder.Common,
--     ZOrderOffset = 0,
--     bInSafeZone = false,
--     bCreateOnDemand = true,
--     IsControlByUIState = true,
--     Anim = {
--         FlowInAni = "WBP_SpecialAiming_stinger_in",
--         FlowOutAni = "WBP_SpecialAiming_stinger_out"
--     }
-- }
-- UITable[UIName2ID.StingerMissileAimingView_Igla] = {
--     UILayer = EUILayer.HUD,
--     LuaPath = "DFM.Business.Module.HUDModule.UI.WeaponBar.StingerMissileAimingView",
--     BPKey = "WBP_SpecialAiming_stinger",

--     HUDName = "WBP_StingerMissileAimingView_Igla",
--     ZOrderBase = EHUDBaseZorder.Common,
--     ZOrderOffset = 0,
--     bInSafeZone = false,
--     bCreateOnDemand = false,
--     IsControlByUIState = true,
--     Anim = {
--         FlowInAni = "WBP_SpecialAiming_stinger_in",
--         FlowOutAni = "WBP_SpecialAiming_stinger_out"
--     }
-- }

--UITable[UIName2ID.WeaponHoldBreathView] = {
--    UILayer = EUILayer.HUD,
--    LuaPath = "DFM.Business.Module.HUDModule.UI.WeaponBar.WeaponHoldBreathView",
--    BPKey = "WBP_ControllerButtun_HoldBreath",
--
--    HUDName = "WBP_ControllerButtun_HoldBreath",
--    ZOrderBase = EHUDBaseZorder.Common,
--    ZOrderOffset = 0,
--    bInSafeZone = true,
--    bCreateOnDemand = false,
--    IsControlByUIState = true,
--    Anim = {
--        FlowInAni = "WBP_ControllerButtun_HoldBreath_in",
--        FlowOutAni = "WBP_ControllerButtun_HoldBreath_out"
--    }
--}

UITable[UIName2ID.StingerMissileCommonView] = {
    UILayer = EUILayer.HUD,
    LuaPath = "DFM.Business.Module.HUDModule.UI.WeaponBar.StingerMissileAimingView",
    BPKey = "WBP_Common_stinger",
    HUDName = "WBP_StingerMissileCommonView",
    ZOrderBase = EHUDBaseZorder.Common,
    ZOrderOffset = 0,
    bInSafeZone = false,
    bCreateOnDemand = true,
    IsControlByUIState = true,
    Anim = {
        FlowInAni = "WBP_SpecialAiming_stinger_in",
        FlowOutAni = "WBP_SpecialAiming_stinger_out"
    }
}

--Buff获得Tip
UITable[UIName2ID.PlayerBuffTips] = {
    UILayer = EUILayer.HUD,
    LuaPath = "DFM.Business.Module.HUDModule.UI.CharacterStatus.BuffTips.PlayerGetBuffTips",
    BPKey = "WBP_TipsNonres_Buffer",
    HUDName = "WBP_PlayerBuffTips",
    ZOrderBase = EHUDBaseZorder.Hint,
    ZOrderOffset = 0,
    bInSafeZone = true,
    bCreateOnDemand = false,
    SubUIs={
        
    }
}

UITable[UIName2ID.PlayerBuffTips_PC] = {
    UILayer = EUILayer.HUD,
    LuaPath = "DFM.Business.Module.HUDModule.UI.CharacterStatus.BuffTips.PlayerGetBuffTips",
    BPKey = "WBP_TipsNonres_BufferPC",
    HUDName = "WBP_PlayerBuffTips",
    ZOrderBase = EHUDBaseZorder.Feedback,
    ZOrderOffset = 0,
    bInSafeZone = true,
    bCreateOnDemand = false,
    SubUIs={
        
    },
    Desc = "PC Player Buff Tips"
}

--Buff倒计时Tip
UITable[UIName2ID.PlayerBuffRemainTimeTip] = {
    UILayer = EUILayer.HUD,
    LuaPath = "DFM.Business.Module.HUDModule.UI.CharacterStatus.BuffTips.PlayerGetBuffRemainTimeTip",
    BPKey = "WBP_PlayerBuffRemainTimeTip",
    HUDName = "WBP_PlayerBuffRemainTimeTip",
    ZOrderBase = EHUDBaseZorder.Common,
    ZOrderOffset = 0,
    bInSafeZone = true,
    bCreateOnDemand = true,
    IsControlByUIState = true,
    SubUIs={
        
    },
    Desc = "Buff Remain Time"
}

-- UITable[UIName2ID.CountDownTimerTextView] = {
--     UILayer = EUILayer.HUD,
--     LuaPath = "DFM.Business.Module.HUDModule.UI.BattleField.CountDownTimerTextView",
--     BPKey = "WBP_Breakthrough_CountDownTimeText",
--     HUDName = "CountDownTimerTextView",
--     ZOrderBase = EHUDBaseZorder.Common,
--     ZOrderOffset = 0,
-- }

-- Battlefield
UITable[UIName2ID.PlayerCountDownTimerView] = {
    UILayer = EUILayer.HUD,
    LuaPath = "DFM.Business.Module.HUDModule.UI.BattleField.PlayerCountDownTimerView",
    BPKey = "WBP_Battle_Time_2",
    HUDName = "WBP_Battle_Time_2",
    ZOrderBase = EHUDBaseZorder.Common,
    ZOrderOffset = 0,
    Desc = "Player Count Down Timer View"
}

UITable[UIName2ID.SectorAnchorCapView] = {
    UILayer = EUILayer.HUD,
    LuaPath = "DFM.Business.Module.HUDModule.UI.BattleField.SectorAnchorCapView",
    BPKey = "WBP_TipsPro_Occupy_2",
    HUDName = "WBP_TipsPro_Occupy_2",
    ZOrderBase = EHUDBaseZorder.Hint,
    ZOrderOffset = 0,
    Desc = "Sector Anchor Cap View"
    -- bCreateOnDemand = true,
}


UITable[UIName2ID.ScoreboardView] = {
    UILayer = EUILayer.HUD,
    LuaPath = "DFM.Business.Module.HUDModule.UI.BattleField.ScoreboardView",
    BPKey = "WBP_TipsPro_Score",
    HUDName = "WBP_TipsPro_Score",
    ZOrderBase = EHUDBaseZorder.Common,
    ZOrderOffset = 0,
    Desc = "Score Board View"
}

--SOL积分板
UITable[UIName2ID.SOLKillFeedbackMainView] = {
    UILayer = EUILayer.HUD,
    LuaPath = "DFM.Business.Module.HUDModule.UI.SOL.SOLKillFeedbackMainView",
    BPKey = "WBP_KillFeedBackSOLMain",
    HUDName = "WBP_KillFeedBackSOLMain",
    ZOrderBase = EHUDBaseZorder.Common,
    ZOrderOffset = 0,
    SubUIs={
        UIName2ID.SOLKillFeedbackIconView,
        UIName2ID.SOLKillFeedbackInfoView,
        UIName2ID.SOLKillFeedbackSpecialInfoView
    }
}

--SOL 获取积分动效 顶部Icon
UITable[UIName2ID.SOLKillFeedbackIconView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.SOL.SOLKillFeedbackIconView",
    BPKey = "WBP_KillFeedbackSOLIcon",
}
--SOL 获取积分动效 中间击杀信息
UITable[UIName2ID.SOLKillFeedbackInfoView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.SOL.SOLKillFeedbackInfoView",
    BPKey = "WBP_KillFeedBackDetailed",
}
--SOL 获取积分动效 下方击杀特殊信息
UITable[UIName2ID.SOLKillFeedbackSpecialInfoView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.SOL.SOLKillFeedbackSpecialInfoView",
    BPKey = "WBP_KillFeedBackSpecial",
}

--大战场 获取积分动效
-- UITable[UIName2ID.BattleFieldKillFeecbackMainView] = {
--     UILayer = EUILayer.HUD,
--     LuaPath = "DFM.Business.Module.HUDModule.UI.BattleField.BattleFieldKillFeecbackMainView",
--     BPKey = "WBP_KillFeedbackMain_KillFeedBack",
--     HUDName = "WBP_KillFeedbackMain",
--     ZOrderBase = EHUDBaseZorder.Common,
--     ZOrderOffset = 0,
--     SubUIs={
--         -- UIName2ID.BattleFieldKillFeecbackIconView,
--         UIName2ID.BattleFieldKillFeecbackNumView,
--         -- UIName2ID.BattleFieldKillFeecbackInfoView
--     }, 
--     Desc = "BattleField Kill Feecback Main View"
-- }
--大战场 获取积分动效 顶部Icon
-- UITable[UIName2ID.BattleFieldKillFeecbackIconView] = {
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.HUDModule.UI.BattleField.BattleFieldKillFeecbackIconView",
--     BPKey = "WBP_KillFeedbackIcon",
-- }
--大战场 获取积分动效 中间积分数字
UITable[UIName2ID.BattleFieldKillFeecbackNumView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.BattleField.BattleFieldKillFeecbackNumView",
    BPKey = "WBP_KillFeedbackNum",
}
--大战场 获取积分动效 下方击杀信息条
-- UITable[UIName2ID.BattleFieldKillFeecbackInfoView] = {
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.HUDModule.UI.BattleField.BattleFieldKillFeecbackInfoView",
--     BPKey = "WBP_KillFeedbackInfo",
-- }

UITable[UIName2ID.ScoreTipsView] = {
    UILayer = EUILayer.HUD,
    LuaPath = "DFM.Business.Module.HUDModule.UI.BattleField.ScoreTipsView",
    BPKey = "BattleField_ScoreTipsView",
    HUDName = "BattleField_ScoreTipsView",
    ZOrderBase = EHUDBaseZorder.Common,
    ZOrderOffset = 0,
}

UITable[UIName2ID.ScoreTipsView_PC] = {
    UILayer = EUILayer.HUD,
    LuaPath = "DFM.Business.Module.HUDModule.UI.BattleField.ScoreTipsView",
    BPKey = "BattleField_ScoreTipsView_PC",
    HUDName = "BattleField_ScoreTipsView",
    ZOrderBase = EHUDBaseZorder.Hint,
    ZOrderOffset = 0,
}

UITable[UIName2ID.BattleReportTipsView] = {
    UILayer = EUILayer.HUD,
    LuaPath = "DFM.Business.Module.HUDModule.UI.BattleField.BattleReportTipsView",
    BPKey = "WBP_TipsNonres_BattleReport",
    HUDName = "WBP_TipsNonres_BattleReport",
    ZOrderBase = EHUDBaseZorder.Common,
    ZOrderOffset = 0,
    Desc = "Battle Report Tips View"
}

UITable[UIName2ID.BattleReportTipsViewLittle] = {
    UILayer = EUILayer.HUD,
    LuaPath = "DFM.Business.Module.HUDModule.UI.BattleField.BattleReportTipsViewLittle",
    BPKey = "WBP_TipsNonres_BattleReport_Little",
    HUDName = "WBP_TipsNonres_BattleReport_Little",
    ZOrderBase = EHUDBaseZorder.Common,
    ZOrderOffset = 0,
    Desc = "Battlefield Report Tips Little"
}

-- UITable[UIName2ID.BattleFieldKillMarkerView] = {
--     UILayer = EUILayer.HUD,
--     LuaPath = "DFM.Business.Module.HUDModule.UI.BattleField.BattleFieldKillMarkerView",
--     BPKey = "WBP_BattleKillInformation",
--     HUDName = "WBP_BattleKillInformation",
--     ZOrderBase = EHUDBaseZorder.Common,
--     ZOrderOffset = 1,
--     Desc = "Battlefield Kill Marker View"
-- } 

UITable[UIName2ID.BattleFieldDeployView] = {
    UILayer = EUILayer.HUD,
    LuaPath = "DFM.Business.Module.HUDModule.UI.BattleField.BattleFieldRedeployView",
    -- BPKey = "WBP_Battle_Deploy_Battle",
    BPKey = "WBP_BattleField_Battle_Deploy",
    HUDName = "BattleFieldDeployView",
    ZOrderBase = EHUDBaseZorder.Mark,
    ZOrderOffset = 0,
    bCreateOnDemand = true,
    SubUIs = {
        
    },
    Desc = "Battlefield Deploy View"
}

-- 基础Marker
-- UITable[UIName2ID.DeployPointPanelBase] = {
--     UILayer = EUILayer.HUD,
--     LuaPath = "DFM.Business.Module.HUDModule.UI.Marker.DeployPointPanel",
--     BPKey = "WBP_DeployPointPanel",
--     HUDName = "DeployPointPanel",
--     ZOrderBase = EHUDBaseZorder.Mark,
--     ZOrderOffset = 0,
--     SubUIs = {
--         UIName2ID.CapturePointMarker,
--         UIName2ID.SafeHouseNPCMarker,
--     }
-- }

-- 声音可视化
-- UITable[UIName2ID.SoundVisualizationPanel] = {
--     UILayer = EUILayer.Tip,
--     LuaPath = "DFM.Business.Module.HUDModule.UI.Common.SoundVisualizationPanel",
--     BPKey = "WBP_SoundVisualizationPanel",
-- }

-- 大战场经典还原Marker
-- UITable[UIName2ID.DeployPointPanelBattleField] = {
--     UILayer = EUILayer.HUD,
--     LuaPath = "DFM.Business.Module.HUDModule.UI.Marker.DeployPointPanel",
--     BPKey = "WBP_DeployPointPanel",
--     HUDName = "DeployPointPanel",
--     ZOrderBase = EHUDBaseZorder.Mark,
--     ZOrderOffset = 0,
--     Desc = "PC Deploy Point Panel",
--     SubUIs = {
--         UIName2ID.CapturePointMarker,
--         UIName2ID.RedeployPointView,
--         UIName2ID.RedeployCapturePointView,
--         UIName2ID.SafeHouseNPCMarker,
--         UIName2ID.BattleSectorAnchorMarker,
--         UIName2ID.ReDeploySectorAnchorMarker,
--     },

--     RedeployMarkerOrderOffset = {
--         ReDeployPoint = 10,--据点、营地

--         SameCampRebornFlag = 21,--己方信标
--         EnemyCampRebornFlag=20,--敌方信标

--         TeamDie=32,--阵亡
--         SameCampDie=31,
--         EnemyCampDie=30,

        
--         NeutralVehiclePoint=40,--中立载具
--         EnemyCampVehiclePoint=41,--敌人载具
--         SameCampVehiclePoint=42,--同阵营载具
--         TeamVehiclePoint=43,--队友载具

--         EnemyCampPoint=50,--敌人
--         SameCampPoint=51,--同阵营
--         TeammatePoint=52,--队友

--         SelectedPoint = 100,--选中的据点、营地、队友、载具等
--     }
-- }

-- Breakthrough Marker
-- UITable[UIName2ID.DeployPointPanelBreakthrough] = {
--     UILayer = EUILayer.HUD,
--     LuaPath = "DFM.Business.Module.HUDModule.UI.Marker.DeployPointPanel",
--     BPKey = "WBP_DeployPointPanel",
--     HUDName = "DeployPointPanel",
--     ZOrderBase = EHUDBaseZorder.Mark,
--     ZOrderOffset = 0,
--     Desc = "Breakthrough Deploy Marker",
--     SubUIs = {
--         UIName2ID.CapturePointMarker,
--         UIName2ID.BattleSectorAnchorMarker,
--         UIName2ID.BreakthroughRebornOnTowerView
--     }
-- }

UITable[UIName2ID.CapturePointMarker] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.BattleField.CapturePointMarker",
    BPKey = "WBP_ScreenMarkePoint",
}

UITable[UIName2ID.RedeployPointView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.BattleField.RedeployPointView",
    BPKey = "WBP_Battle_Deploy_RedployPoint_Battle",
    HUDName = "WBP_Battle_Deploy_RedployPoint",
    ZOrderOffset = 7
}

UITable[UIName2ID.RedeployCapturePointView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.BattleField.RedeployCapturePointView",
    BPKey = "WBP_Battle_Deploy_CapturePoint",
    HUDName = "WBP_Battle_Deploy_CapturePoint",
    ZOrderOffset = 4
}

UITable[UIName2ID.SafeHouseNPCMarker] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.SafeHouse.SafeHouseNPCMarker",
    BPKey = "WBP_ScreenMarkeNPC_SafeHouse",
    HUDName = "SafeHouseNPCMarker",
}

UITable[UIName2ID.WeaponChangeView] = {
    UILayer = EUILayer.HUD,
    LuaPath = "DFM.Business.Module.HUDModule.UI.BattleField.WeaponChangeView",
    BPKey = "WBP_Battle_Arsenal",
    HUDName = "WBP_Battle_Arsenal",
    ZOrderBase = EHUDBaseZorder.Common,
    ZOrderOffset = 100,
    SubUIs = 
    {
        UIName2ID.CommonCheckTabS1,
    }
}

if DFHD_LUA == 1 then
    --UITable[UIName2ID.WeaponBarHudView_PC] = {
    --    UILayer = EUILayer.HUD,
    --    LuaPath = "DFM.Business.Module.HUDModule.UI.WeaponBar.WeaponBarHudView_PC",
    --    BPKey = "WBP_WeaponBar_PC",
    --    HUDName = "WBP_ToolbarWeapon",
    --    ZOrderBase = EHUDBaseZorder.Common,
    --    ZOrderOffset = 0,
    --    bInSafeZone = true,
    --    SubUIs={
    --        UIName2ID.CharacterArmorHudDetailItem
    --    },
    --    Desc = "PC Weapon Bar"
    --}

    UITable[UIName2ID.BuffAndStaminaViewPC] = {
        UILayer = EUILayer.HUD,
        LuaPath = "DFM.Business.Module.HUDModule.UI.CharacterStatus.BuffAndStaminaViewPC",
        BPKey = "WBP_CharacterStaminaBarPC_2",
        HUDName = "BuffAndStaminaViewPC",
        ZOrderBase = EHUDBaseZorder.Common,
        ZOrderOffset = 10,
        bInSafeZone = true,
        bCreateOnDemand = false,
        SubUIs={
            
        }
    }
        
    UITable[UIName2ID.BuffDetailTipWindow] = {
        UILayer = EUILayer.Tip,
        LuaPath = "DFM.Business.Module.HUDModule.UI.CharacterStatus.CharacterBuffDetailTip",
        BPKey = "WBP_BuffDetailTipWindow",
        HUDName = "BuffDetailTipWindow",
        --ZOrderBase = EHUDBaseZorder.Common,
        ZOrderOffset = 10,
        bInSafeZone = true,
        bCreateOnDemand = false,
        SubUIs={
            
        }
    }

    UITable[UIName2ID.TipsReload_PC] = {
        UILayer = EUILayer.HUD,
        LuaPath = "DFM.Business.Module.HUDModule.UI.SOLTips.TipsReload_PC",
        BPKey = "WBP_TipsReload_PC",
        HUDName = "WBP_TipsReload",
        ZOrderBase = EHUDBaseZorder.Feedback,
        ZOrderOffset = 0,
        bInSafeZone = true,
        bCreateOnDemand = false,
        IsControlByUIState = true,
        SubUIs={
            
        },
        Desc = "PC Reload Tips"
    }

    --UITable[UIName2ID.UsingItemTipsHudView_PC] = {
    --    UILayer = EUILayer.HUD_Hint,
    --    LuaPath = "DFM.Business.Module.HUDModule.UI.UsingItemTipsHudView",
    --    BPKey = "WBP_TipsPro_Mini_ProgressBar",
    --    bCreateOnDemand = true,
    --    Anim = {
    --        FlowInAni = "WBP_TipsPro_Mini_in",
    --        FlowOutAni = "WBP_TipsPro_Mini_out"
    --    },
    --}

    -- UITable[UIName2ID.SoundVisualizationPanel_PC] = {
    --     UILayer = EUILayer.Tip,
    --     LuaPath = "DFM.Business.Module.HUDModule.UI.Common.SoundVisualizationPanel",
    --     BPKey = "WBP_SoundVisualizationPanel_PC",
    -- }
end

-- Vehicle
UITable[UIName2ID.VehicleEmptyRootView] = {
    UILayer = EUILayer.HUD,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleEmptyRootView",
    BPKey = "WBP_VehicleEmptyRootView",
    HUDName = "VehicleEmptyRootView",
    ZOrderBase = EHUDBaseZorder.Feedback,
    ZOrderOffset = 10,
    bCreateOnDemand = false,
    bInSafeZone = true,
}

UITable[UIName2ID.VehicleCommonSubView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
    BPKey = "WBP_VehicleCommonView",
    HUDName = "VehicleCommonSubView",
    ZOrderOffset = 100,
}

UITable[UIName2ID.VehicleDriverSubView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
    BPKey = "WBP_VehicleDriverSubView",
    HUDName = "VehicleDriverSubView",
}

UITable[UIName2ID.TankDriverSubView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
    BPKey = "WBP_TankDriveView",
    HUDName = "TankDriverSubView",
}

UITable[UIName2ID.HelicopterDriverSubView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.HelicopterDriverView",
    BPKey = "WBP_HelicopterDriveView",
    HUDName = "HelicopterDriverSubView",
}

UITable[UIName2ID.HelicopterBirdDriverSubView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.HelicopterDriverView",
    BPKey = "WBP_HelicopterBirdDriveView",
    HUDName = "HelicopterBirdDriverSubView",
}

UITable[UIName2ID.VehicleLockSubView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
    BPKey = "WBP_VehicleLock",
    HUDName = "VehicleLockSubView",
}

UITable[UIName2ID.VehicleSupplyTipView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
    BPKey = "WBP_TipsPro_VehicleSupply",
    HUDName = "VehicleSupplyTipView",
}


-- 根据设置选择展示的控制类型
UITable[UIName2ID.VehicleControllerRootSubView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleControllerRootView",
    BPKey = "WBP_VehicleControllerRoot",
    HUDName = "VehicleControllerRootSubView",
}

UITable[UIName2ID.TankControllerRootSubView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.TankControllerRootView",
    BPKey = "WBP_TankControllerRoot",
    HUDName = "TankControllerRootSubView",
}

UITable[UIName2ID.WeaponVehicleControllerRootSubView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.WeaponVehicleControllerRootView",
    BPKey = "WBP_WeaponVehicleControllerRoot",
    HUDName = "WeaponVehicleControllerRootSubView",
}

UITable[UIName2ID.VehicleButtonStyleControllerSubView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
    BPKey = "WBP_VehicleButton_Style1",
    HUDName = "VehicleButtonStyleControllerSubView",
}

UITable[UIName2ID.VehicleJoystickStyleControllerSubView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
    BPKey = "WBP_ControllerVehicleMovement",
    HUDName = "VehicleJoystickStyleControllerSubView",
}

UITable[UIName2ID.VehicleSliderStyleControllerSubView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
    BPKey = "WBP_VehicleButton_Style2",
    HUDName = "VehicleSliderStyleControllerSubView",
}

UITable[UIName2ID.VehicleTankJoystickSubView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
    BPKey = "WBP_ArmedDriverView_Movement",
    HUDName = "VehicleTankJoystickSubView",
}

UITable[UIName2ID.WeaponVehicleJoystickStyleControllerSubView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
    BPKey = "WBP_WeaponVehicleJoystickStyleController",
    HUDName = "WeaponVehicleJoystickStyleControllerSubView",
}

UITable[UIName2ID.WeaponVehicleButtonStyleControllerSubView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
    BPKey = "WBP_WeaponButtonStyleController",
    HUDName = "WeaponVehicleButtonStyleControllerSubView",
}

UITable[UIName2ID.WeaponVehicleGliderStyleControllerSubView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
    BPKey = "WBP_WeaponGliderStyleController",
    HUDName = "WeaponVehicleGliderStyleControllerSubView",
}

UITable[UIName2ID.VehicleGunSubView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
    BPKey = "WBP_VehicleGun",
    HUDName = "VehicleGunSubView",
}

UITable[UIName2ID.HelicopterGunSubView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
    BPKey = "WBP_HelicopterGun",
    HUDName = "HelicopterGunSubView",
}

UITable[UIName2ID.HelicopterStateSubView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
    BPKey = "WBP_MilitaryHelicopter_State",
    HUDName = "HelicopterStateSubView",
}

UITable[UIName2ID.HelicopterBirdStateSubView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
    BPKey = "WBP_Vehicle_Icon_BirdHelicopter",
    HUDName = "HelicopterBirdStateSubView",
}

UITable[UIName2ID.JetF35StateSubView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
    BPKey = "WBP_Vehicle_Icon_F35",
    HUDName = "JetF35StateSubView",
}

UITable[UIName2ID.VehicleCommonStateSubView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
    BPKey = "WBP_CommonVehicleWidget",
    HUDName = "VehicleCommonStateSubView",
}

UITable[UIName2ID.VehicleTankStateSubView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
    BPKey = "WBP_Tank_TankState",
    HUDName = "VehicleTankStateSubView",
}

UITable[UIName2ID.VehicleGTQTankStateSubView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
    BPKey = "WBP_Vehicle_Icon_GTQ_U",
    HUDName = "VehicleGTQTankStateSubView",
}

UITable[UIName2ID.VehicleTPPWeaponCommonAimWidget] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
    BPKey = "WBP_VehicleTPPWeaponCommonAim",
    HUDName = "VehicleTPPWeaponCommonAimWidget",
}

UITable[UIName2ID.VehicleAimAssistView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
    BPKey = "WBP_VehicleAimAssistView",
    HUDName = "VehicleAimAssistView",
}

UITable[UIName2ID.VehicleFreeCameraView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
    BPKey = "WBP_VehicleFreeCameraView",
    HUDName = "VehicleFreeCameraView",
}

UITable[UIName2ID.VehicleInfantryStateSubView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
    BPKey = "WBP_Tank_InfantryVehicleState",
    HUDName = "VehicleInfantryStateSubView",
}

UITable[UIName2ID.VehicleWheeledTankStateSubView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
    BPKey = "WBP_Tank_WheeledAssaultGunState",
    HUDName = "VehicleWheeledTankStateSubView",
}

UITable[UIName2ID.VehicleAAStateSubView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
    BPKey = "WBP_Tank_AAVehicleState",
    HUDName = "VehicleAAStateSubView",
}

UITable[UIName2ID.VehicleDetectionSkillWidget] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
    BPKey = "WBP_VehicleDetectionSkillWidget",
    HUDName = "VehicleDetectionSkillWidget",
}

-- PC的载具HUD
if DFHD_LUA == 1 then
    UITable[UIName2ID.VehicleBloodBarSubView] = {
        UILayer = EUILayer.Sub,
        LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
        BPKey = "WBP_VehicleBloodBar_PC",
        HUDName = "VehicleBloodBarSubView",
    }

    UITable[UIName2ID.VehicleWeaponBarSubView] = {
        UILayer = EUILayer.Sub,
        LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
        BPKey = "WBP_VehicleWeaponBar_PC",
        HUDName = "VehicleWeaponBarSubView",
    }

    -- PC的坦克主炮瞄准镜
    UITable[UIName2ID.TankWeaponMirrorWidgetSubView] = {
        UILayer = EUILayer.Sub,
        LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
        BPKey = "WBP_TankMirrorWidget_PC",
        HUDName = "TankWeaponMirrorWidgetSubView",
        ZOrderOffset = -1,
    }

    -- PC的坦克主炮准心
    UITable[UIName2ID.TankWeaponAimWidgetSubView] = {
        UILayer = EUILayer.Sub,
        LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
        BPKey = "WBP_TankAimWidget_PC",
        HUDName = "TankWeaponAimWidgetSubView",
    }

    UITable[UIName2ID.HelicopterDriverSubViewPC] = {
        UILayer = EUILayer.Sub,
        LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.HelicopterDriverView",
        BPKey = "WBP_HelicopterDriveViewPC",
        HUDName = "HelicopterDriverSubViewPC",
    }

    UITable[UIName2ID.JetDriverSubViewPC] = {
        UILayer = EUILayer.Sub,
        LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.HelicopterDriverView",
        BPKey = "WBP_HelicopterF35DriveView",
        HUDName = "JetDriverSubViewPC",
    }

    UITable[UIName2ID.HelicopterBirdDriverSubViewPC] = {
        UILayer = EUILayer.Sub,
        LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.HelicopterDriverView",
        BPKey = "WBP_HelicopterBirdDriveViewPC",
        HUDName = "HelicopterBirdDriverSubViewPC",
    }

    UITable[UIName2ID.VehicleKeyTipsSubView] = {
        UILayer = EUILayer.Sub,
        LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
        BPKey = "WBP_VehicleKeyTipsWidget_PC",
        HUDName = "VehicleKeyTipsSubView",
    }

else
    -- 手游专属HUD
    UITable[UIName2ID.VehicleSkillPanelSubView] = {
        UILayer = EUILayer.Sub,
        LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
        BPKey = "WBP_CommonVehicleSkillPanel",
        HUDName = "VehicleSkillPanelSubView",
    }

    UITable[UIName2ID.VehicleSwitchWeaponSubView] = {
        UILayer = EUILayer.Sub,
        LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
        BPKey = "WBP_VehicleSwitchWeaponWidget",
        HUDName = "VehicleSwitchWeaponSubView",
    }

    UITable[UIName2ID.VehicleBloodBarSubView] = {
        UILayer = EUILayer.Sub,
        LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
        BPKey = "WBP_VehicleBloodBar_CommonView",
        HUDName = "VehicleBloodBarSubView",
    }

    UITable[UIName2ID.VehicleJetMovementSubView] = {
        UILayer = EUILayer.Sub,
        LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.VehicleSubView",
        BPKey = "WBP_FixedWingController",
        HUDName = "VehicleJetMovementSubView",
    }

    UITable[UIName2ID.JetDriverSubView] = {
        UILayer = EUILayer.Sub,
        LuaPath = "DFM.Business.Module.HUDModule.UI.Vehicle.HelicopterDriverView",
        BPKey = "WBP_HelicopterF35DriveView",
        HUDName = "JetDriverSubView",
    }
end

-- Breakthrough

---PreMatch选择兵种界面
UITable[UIName2ID.BreakthroughPreMatchChooseArmView] = {
    UILayer = EUILayer.HUD,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Breakthrough.BreakthroughPreMatchChooseArmView",
    BPKey = "WBP_Breakthrough_PreMatchChooseArmView",
    HUDName = "BreakthroughPreMatchChooseArmView",
    ZOrderBase = EHUDBaseZorder.LargePopup,
    ZOrderOffset = 0,
    bCreateOnDemand = true,
    SubUIs = {
        
    }
}

---PreMatch倒计时
-- UITable[UIName2ID.BreakthroughPreMatchCountDownView] = {
--     UILayer = EUILayer.HUD,
--     LuaPath = "DFM.Business.Module.HUDModule.UI.Breakthrough.BreakthroughPreMatchCountDownView",
--     BPKey = "WBP_DeployFirstDeployment_Progress",
--     HUDName = "BreakthroughPreMatchCountDownView",
--     ZOrderBase = EHUDBaseZorder.Hint,
--     ZOrderOffset = 10,
-- }

--PreMatch队伍信息
-- UITable[UIName2ID.BreakthroughPreMatchTeamInfoHUDView] = {
--     UILayer = EUILayer.HUD,
--     LuaPath = "DFM.Business.Module.HUDModule.UI.Breakthrough.BreakthroughPreMatchTeamInfoHUDView",
--     BPKey = "WBP_DeployFirstDeployment_TeammateList",
--     HUDName = "WBP_DeployFirstDeployment_TeammateList",
--     ZOrderBase = EHUDBaseZorder.LargePopup,
--     ZOrderOffset = 1,
--     SubUIs = {
--         UIName2ID.BreakthroughPreMatchTeamMemberInfoHUDView,
--     },
--     Desc = "MP PreMatch TeamInfo HUD View"
-- }

--PreMatch队伍信息Item
-- UITable[UIName2ID.BreakthroughPreMatchTeamMemberInfoHUDView] = {
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.HUDModule.UI.Breakthrough.BreakthroughPreMatchTeamMemberInfoHUDView",
--     BPKey = "WBP_DeployFirstDeployment_Teammate",
--     HUDName = "WBP_DeployFirstDeployment_Teammate",
-- }

-- --大战场 重新部署界面
-- UITable[UIName2ID.BreakthroughRedeployView] = {
--     UILayer = EUILayer.HUD,
--     LuaPath = "DFM.Business.Module.HUDModule.UI.BattleField.BattleFieldRedeployView",
--     BPKey = "WBP_Breakthrough_Battle_Deploy_Reimport",
--     HUDName = "BreakthroughRedeployView",
--     ZOrderBase = EHUDBaseZorder.Mark,
--     ZOrderOffset = 0,
--     bCreateOnDemand = true,
--     SubUIs = {
        
--     }
-- }

--大战场 重新部署界面
-- UITable[UIName2ID.BreakthroughRedeployViewGesture] = {
--     UILayer = EUILayer.HUD,
--     LuaPath = "DFM.Business.Module.HUDModule.UI.BattleField.BattleFieldRedeployViewGesture",
--     BPKey = "WBP_Breakthrough_Battle_Deploy_Gesture",
--     HUDName = "BreakthroughRedeployViewGesture",
--     ZOrderBase = EHUDBaseZorder.Popup,
--     ZOrderOffset = 0,
--     bCreateOnDemand = true,
--     SubUIs = {
--         UIName2ID.BreakthroughRedeployVoteTipsView
--     }
-- }

UITable[UIName2ID.BattleSectorAnchorMarker] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Breakthrough.BattleSectorAnchorMarker",
    BPKey = "WBP_BattleSectorAnchorMarker",
    ZOrderOffset = 15,
}

UITable[UIName2ID.ReDeploySectorAnchorMarker] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Breakthrough.ReDeploySectorAnchorMarker",
    BPKey = "WBP_ReDeploySectorAnchorMarker",
    HUDName = "WBP_ReDeploySectorAnchorMarker",
}

--攻防重新部署界面 可获取兵力提示
-- UITable[UIName2ID.BreakthroughRedeployVoteTipsView] = {
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.HUDModule.UI.Breakthrough.BreakthroughRedeployVoteTipsView",
--     BPKey = "WBP_Breakthrough_Redeploy_VoteTips",
--     HUDName = "WBP_Breakthrough_Redeploy_VoteTips",
--     ZOrderOffset = 5
-- }

--重新部署界面据点推进进度信息
UITable[UIName2ID.BreakthroughRedeploySectorAnchorInfo] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Breakthrough.BreakthroughSectorBattleProgressSectorAnchorInfo",
    -- BPKey = "WBP_TipsPro_OccupyPoint"
    BPKey = "WBP_Marker_Map_CampS2"
}

--强提示
UITable[UIName2ID.BreakthroughBattleReportTipsView] = {
    UILayer = EUILayer.HUD,
    LuaPath = "DFM.Business.Module.HUDModule.UI.BattleField.BattleReportTipsView",
    BPKey = "WBP_Breakthrough_BattleReport",
    HUDName = "WBP_Breakthrough_BattleReport",
    ZOrderBase = EHUDBaseZorder.Common,
    ZOrderOffset = 0
}

--弱提示
UITable[UIName2ID.BreakthroughBattleReportTipsViewLittle] = {
    UILayer = EUILayer.HUD,
    LuaPath = "DFM.Business.Module.HUDModule.UI.BattleField.BattleReportTipsViewLittle",
    BPKey = "WBP_Breakthrough_BattleReport_Little",
    HUDName = "WBP_Breakthrough_BattleReport_Little",
    ZOrderBase = EHUDBaseZorder.Common,
    ZOrderOffset = 0
}

-- --计分板列表条目
-- UITable[UIName2ID.BreakthroughScoreboardDetailsPanelItem] = {
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.HUDModule.UI.Breakthrough.BreakthroughScoreboardDetailsPanelItem",
--     BPKey = "WBP_BattleScoreDetailtem",
-- }

-- --计分板界面
-- UITable[UIName2ID.BreakthroughScoreboardDetailsPanel] = {
--     UILayer = EUILayer.HUD,
--     LuaPath = "DFM.Business.Module.HUDModule.UI.Breakthrough.BreakthroughScoreboardDetailsPanel",
--     BPKey = "WBP_BattleScoreDetail",
--     HUDName = "WBP_BattleScoreDetail",
--     ZOrderBase = EHUDBaseZorder.LargePopup,
--     ZOrderOffset = 20,
--     -- Anim = {
--     --     FlowInAni = "Anima_AutoIn",
--     --     FlowOutAni = "Anima_AutoOut"
--     -- },
--     IsControlByUIState = true,
-- }

-- UITable[UIName2ID.ConquestScoreboardDetailsPanelItem] = {
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.HUDModule.UI.Conquest.ConquestScoreboardDetailsPanelItem",
--     BPKey = "WBP_BattleScoreDetailtem",
-- }

-- UITable[UIName2ID.ConquestScoreboardDetailsPanel] = {
--     UILayer = EUILayer.HUD,
--     LuaPath = "DFM.Business.Module.HUDModule.UI.Conquest.ConquestScoreboardDetailsPanel",
--     BPKey = "WBP_BattleScoreDetail",
--     HUDName = "WBP_BattleScoreDetail",
--     ZOrderBase = EHUDBaseZorder.LargePopup,
--     ZOrderOffset = 20,
--     IsControlByUIState = true,
-- }

-- 大战场重新部署界面 切换兵种Tab
UITable[UIName2ID.RedeploySoliderChangeTabView] = {
    UILayer = EUILayer.HUD,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Breakthrough.RedeploySoliderChangeTabView",
    BPKey = "WBP_Battle_DeploySoliderChangeTabView",
    HUDName = "WBP_Battle_DeployHeroListView",
    ZOrderBase = EHUDBaseZorder.Common,
    ZOrderOffset = 10
}



--大战场重新部署界面 当前兵种配置的背包信息
UITable[UIName2ID.RedeployArmSelectedPackageView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Breakthrough.RedeployPackageView",
    BPKey = "WBP_RedeployArmSelectedPackageView"
}

--大战场 重新部署巨塔
UITable[UIName2ID.BreakthroughRebornOnTowerView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Breakthrough.BreakthroughRebornOnTowerView",
    BPKey = "WBP_Battle_Deploy_RebornTower",
    HUDName = "BreakthroughRebornOnTowerView",
    ZOrderOffset = 2,
}


--攻防 Prematch 等待弱网玩家
-- UITable[UIName2ID.BreakthroughWaitPlayerTipsView] = {
--     UILayer = EUILayer.HUD,
--     LuaPath = "DFM.Business.Module.HUDModule.UI.Breakthrough.BreakthroughWaitPlayerTipsView",
--     BPKey = "WBP_DeployFirstDeployment_Progress",
--     HUDName = "WBP_Breakthrough_WaitPlayerTips",
--     ZOrderBase = EHUDBaseZorder.Common,
--     ZOrderOffset = 0,
--     Desc = "Breakthrough Wait Player Tips View"
-- }

-- Common Hud
-- UITable[UIName2ID.CommonSystemInfoView] = {
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.HUDModule.UI.Common.SystemInfoView",
--     BPKey = "WBP_TipsRes_PowerAndSignal",
--     HUDName = "WBP_TipsRes_PowerAndSignal",
--     ZOrderBase = EHUDBaseZorder.LargePopup,
--     ZOrderOffset = 10,
--     -- bCreateOnDemand = false,
--     -- IsControlByUIState = false
--     Desc = "Common System Info View"
-- }

-- 走c++ hudtable配置了
-- UITable[UIName2ID.CommonSystemInfoView_PC] = {
--     UILayer = EUILayer.HUD,
--     LuaPath = "DFM.Business.Module.HUDModule.UI.Common.SystemInfoView_PC",
--     BPKey = "WBP_NetworkStatus_PC",
--     HUDName = "WBP_NetworkStatus_PC",
--     ZOrderBase = EHUDBaseZorder.Common,
--     ZOrderOffset = 0,
--     -- bCreateOnDemand = false,
--     -- IsControlByUIState = false
--     Desc = "PC Common System Info View"
-- }

-----------------------------
local GameHUDState = import "GameHUDSate"
local HUDConfig = {
    NormalAmmoColor = ColorUtil.GetSlateColorByHex("000000FF"),
    NullAmmoColor = ColorUtil.GetSlateColorByHex("FF7A7EE5"),
    NullCarriedAmmoColor = ColorUtil.GetSlateColorByHex("FF7A7E99"),
    NormalAmmoColor_PC = ColorUtil.GetSlateColorByHex("FFFFFFFF"),
    NullCarriedAmmoColor_PC = ColorUtil.GetSlateColorByHex("FF0565"),
    -- AbnormalGunColor_PC = FLinearColor(1, 0, 0, 1.0),
    AbnormalGunColor_PC = ColorUtil.GetSlateColorByHex("7A0B0E"),
    NormalGunColor_PC = FLinearColor(1.0, 1.0, 1.0, 1.0), 

    
    -- 局内装备租借Start------------------------------------------------------------------------------------------------
    evtOnInGameRentalWeaponBulletChanged = LuaEvent:NewIns("evtOnInGameRentalWeaponBulletChanged"),

    -- 租借系统tab
    InGameRentalTabTxtList = {
        NSLOCTEXT("HUDModule","Lua_HUD_InGameRentalTab01","新兵"),
        NSLOCTEXT("HUDModule","Lua_HUD_InGameRentalTab02","标准"),
        NSLOCTEXT("HUDModule","Lua_HUD_InGameRentalTab03","精锐"),
    },

    -- 局内装备租借End-------------------------------------------------------------------------------------------------

    SOLKillFeedBackPosSetting = {
        IconYOffset = -570, -- 49
        InfoYOffset = -521, -- 81
        SpecialInfoYOffset = -440
    },
    SOLKillFeedBackPosSetting_PC = {
        IconYOffset = -790, -- 55
        InfoYOffset = -735, -- 95
        SpecialInfoYOffset = -640,
        ScaleToMobile = 1,
        FontScaleToMobile = 1.2,
    },

    ResListWeaponBar = {
        NotEquipPath = "PaperSprite'/Game/UI/UIAtlas/System/HUD/BakedSprite/HUD_ToolBar_Bg_06.HUD_ToolBar_Bg_06'",
        EquipPath = "PaperSprite'/Game/UI/UIAtlas/System/HUD/BakedSprite/HUD_ToolBar_Bg_07.HUD_ToolBar_Bg_07'",
        WeaponUnChosenPath = "PaperSprite'/Game/UI/UIAtlas/System/HUD/BakedSprite/HUD_ToolBar_Bg_04.HUD_ToolBar_Bg_04'",
        WeaponChosenPath = "PaperSprite'/Game/UI/UIAtlas/System/HUD/BakedSprite/HUD_ToolBar_Bg_05.HUD_ToolBar_Bg_05'",
        WeaponInvalidPath = "PaperSprite'/Game/UI/UIAtlas/System/HUD/BakedSprite/HUD_ToolBar_Bg_10.HUD_ToolBar_Bg_10'",
        EmptyWeaponImagePath = "PaperSprite'/Game/UI/UIAtlas/System/HUD/BakedSprite/HUD_ToolBar_Icon_0004.HUD_ToolBar_Icon_0004'",
    },

    Loc = {
        FireModeSingle = NSLOCTEXT("HUDModule", "Lua_Weapon_FireModeSingle", "单发"),
        FireModeBurst = NSLOCTEXT("HUDModule", "Lua_Weapon_FireModeBurst", "连发"),
        FireModeAuto = NSLOCTEXT("HUDModule", "Lua_Weapon_FireModeAuto", "自动"),
        TipsChangeFov = NSLOCTEXT("HUDModule", "Lua_Weapon_ChangeFov", "瞄具倍率：%s倍"),

        -- Using item tip
        ItemUsingCountDown = NSLOCTEXT("HUDModule", "Lua_HUD_ItemUsingCountDown", "%.1f秒"),
        ItemUsingCountDown_PC = NSLOCTEXT("HUDModule", "Lua_HUD_ItemUsingCountDown_PC", "%.1f秒"),

        BuffDetailTipDesc01 = NSLOCTEXT("HUDModule", "BuffDetailTipDesc01", "<customstyle color=\"Color_Buffer_01\">%s：</>%s"),
        BuffDetailTipDesc02 = NSLOCTEXT("HUDModule", "BuffDetailTipDesc02", "<customstyle color=\"Color_Buffer_02\">%s：</>%s"),
        BuffDetailTipDesc03 = NSLOCTEXT("HUDModule", "BuffDetailTipDesc03", "<customstyle color=\"Color_Buffer_03\">%s：</>%s"),

        LatencyText = NSLOCTEXT("HUDModule", "Lua_HUD_LatencyText", "%sms"),
        AttackSideRemainArmy = NSLOCTEXT("HUDModule", "Lua_HUD_AttackSideRemainArmy", "进攻方剩余兵力: %i"),
        AvalibleArmy = NSLOCTEXT("HUDModule", "Lua_HUD_AvalibleArmy", "可获得兵力数: %i"),
        BackToBattlefieldImmediatly = NSLOCTEXT("HUDModule", "Lua_HUD_BackToBattlefieldImmediatly", "立刻返回作战区域: %.2f"),
        DefenderEvacuteImmediatly = NSLOCTEXT("HUDModule", "Lua_HUD_DefenderEvacuteImmediatly", "守方立刻撤退: %.2f"),
        DiscountPermission = NSLOCTEXT("HUDModule", "Lua_HUD_DiscountPermission", "折扣权限：%s(%s/%s)"),
        Reload = NSLOCTEXT("HUDModule", "Lua_HUD_Reload", "装弹"),
        LowAmmo = NSLOCTEXT("HUDModule", "Lua_HUD_LowAmmo", "弹药量低"),
        NoAmmo = NSLOCTEXT("HUDModule", "Lua_HUD_NoAmmo", "无备用弹药"),
        NeedAmmo = NSLOCTEXT("HUDModule", "Lua_HUD_NeedAmmo", "需要弹药"),
        NoTarget = NSLOCTEXT("HUDModule", "Lua_HUD_NoTarget", "未锁定目标"),
        DistanceText = NSLOCTEXT("HUDModule", "Lua_HUD_DistanceText", "%.0f米"),
        PenetrateLevel = NSLOCTEXT("HUDModule", "Lua_HUD_PenetrateLevel", "穿透等级 %s"),
        ArmorLevel = NSLOCTEXT("HUDModule", "Lua_HUD_ArmorLevel", "%s级"),

        
        -- 局内装备租借
        InGameEquipmentRentalTitle     = NSLOCTEXT("HUDModule", "Lua_HUD_InGameEquipmentRentalTitle", "制式套装"),
        InGameEquipmentRentalUsing     = NSLOCTEXT("HUDModule", "Lua_HUD_InGameEquipmentRentalUsing", "制式套装使用中"),
        InGameRentalRefreshConfirmTips     = NSLOCTEXT("HUDModule", "Lua_HUD_InGameRentalRefreshConfirmTips", "刷新方案会花费%s积分，确认刷新套装方案？"),
        InGameRentalScoreNotEnough     = NSLOCTEXT("HUDModule", "Lua_HUD_InGameRentalScoreNotEnough", "积分不足"),
        InGameEquipmentCountDownTimer     = NSLOCTEXT("HUDModule", "Lua_HUD_InGameEquipmentCountDownTimer", "-准备阶段:%s-"),
        InGameEquipmentFreeRefreshText     = NSLOCTEXT("HUDModule", "Lua_HUD_InGameEquipmentFreeRefreshText", "免费刷新次数:%s"),
        InGameRentalFreeRefreshConfirmTips     = NSLOCTEXT("HUDModule", "Lua_HUD_InGameRentalFreeRefreshConfirmTips", "剩余免费刷新次数%s，确认刷新套装方案？"),
        InGameEquipmentRentalCountMaxTips     = NSLOCTEXT("HUDModule", "Lua_HUD_InGameEquipmentRentalCountMaxTips", "每回合最多购买%s次套装"),
        
    },

    HUDGroup = {
        BaseGroup = {
            GameHUDState.GHS_PrepareTime,
            GameHUDState.GHS_Settlement,
            GameHUDState.GHS_CutScene,
            GameHUDState.GHS_Operating3DUI,
            GameHUDState.GHS_RescueSomeOne,
            GameHUDState.GHS_ForbidFire,
            GameHUDState.GHS_PVEQuestPanelOnly,
            GameHUDState.GHS_SOLQuestOperation,
            GameHUDState.GHS_OpenMap,
            GameHUDState.GHS_KillerMark,
            GameHUDState.GHS_BattleScoreDetails,
            GameHUDState.GHS_Dead,
            GameHUDState.GHS_Tutorial
        },
    }
}



-- UITable[UIName2ID.ArmdedForceSlot1View] = {
--     UILayer = EUILayer.HUD,
--     LuaPath = "DFM.Business.Module.HUDModule.UI.Common.ArmdedForceItemView",
--     BPKey = "WBP_ControllerButtonSkill_Props",
--     HUDName = "WBP_ControllerButtonSkill_Props",
--     ZOrderBase = EHUDBaseZorder.Common,
--     ZOrderOffset = 0,
-- }

-- UITable[UIName2ID.ArmdedForceSlot2View] = {
--     UILayer = EUILayer.HUD,
--     LuaPath = "DFM.Business.Module.HUDModule.UI.Common.ArmdedForceSlot2View",
--     BPKey = "WBP_ControllerButton_Props",
--     HUDName = "WBP_ControllerButton_Props",
--     ZOrderBase = EHUDBaseZorder.Common,
--     ZOrderOffset = 0,
-- }

UITable[UIName2ID.SeamlessEntryMicrophoneButton] = {
    UILayer = EUILayer.HUD,
    LuaPath = "DFM.Business.Module.HUDModule.UI.SafeHouse.SeamlessEntryMicrophoneButton",
    BPKey = "WBP_SeamlessEntry_ButtonMic",
    HUDName = "WBP_SeamlessEntry_ButtonMic",
    ZOrderBase = EHUDBaseZorder.Common,
    ZOrderOffset = 20
}

UITable[UIName2ID.SeamlessEntrySpeakerButton] = {
    UILayer = EUILayer.HUD,
    LuaPath = "DFM.Business.Module.HUDModule.UI.SafeHouse.SeamlessEntrySpeakerButton",
    BPKey = "WBP_SeamlessEntry_ButtonSpeaker",
    HUDName = "WBP_SeamlessEntry_ButtonSpeaker",
    ZOrderBase = EHUDBaseZorder.Common,
    ZOrderOffset = 20
}

UITable[UIName2ID.SeamlessEntryMainKeyView] = {
    UILayer = EUILayer.HUD,
    LuaPath = "DFM.Business.Module.HUDModule.UI.SafeHouse.SeamlessEntryMainKeyView",
    BPKey = "WBP_SeamlessEntry_MainKey",
    HUDName = "WBP_SeamlessEntry_MainKey",
    ZOrderBase = EHUDBaseZorder.Common,
    ZOrderOffset = 20
}

UITable[UIName2ID.SeamlessEntryTaskListView] = {
    UILayer = EUILayer.HUD,
    LuaPath = "DFM.Business.Module.HUDModule.UI.SafeHouse.SeamlessEntryTaskListView",
    BPKey = "WBP_SeamlessEntry_TaskList",
    HUDName = "WBP_SeamlessEntry_TaskList",
    ZOrderBase = EHUDBaseZorder.Common,
    ZOrderOffset = 20,
    SubUIs = {
        UIName2ID.SeamlessEntryTaskView 
    }
}

UITable[UIName2ID.SeamlessEntryTaskView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HUDModule.UI.SafeHouse.SeamlessEntryTaskView",
    BPKey = "WBP_SeamlessEntry_Task",
}

UITable[UIName2ID.DFHDInteractorPanelItemDescText] = {
    UILayer = EUILayer.HUD,
    LuaPath = "DFM.Business.Module.HUDModule.UI.SOL.DFHDInteractorPanelItemDescText",
    BPKey = "WBP_InteractPanelItem_Desc_PC",
    HUDName = "WBP_InteractPanelItem_Desc_PC",
    ZOrderBase = EHUDBaseZorder.Common,
    ZOrderOffset = 20
}

UITable[UIName2ID.SOLContractRewardPropsView] = {
    UILayer = EUILayer.HUD,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Contract.SOLContractRewardPropsView",
    BPKey = "WBP_Contract_ProbabilityOutput",
    HUDName = "WBP_Contract_ProbabilityOutput",
    ZOrderBase = EHUDBaseZorder.Common,
    ZOrderOffset = 20
}

UITable[UIName2ID.BanView] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.HUDModule.UI.Ban.BanView",
    BPKey = "WBP_HudBan",
    HUDName = "WBP_HudBan",
    ZOrderBase = EHUDBaseZorder.LargePopup,
    ZOrderOffset = 20
}


if DFHD_LUA == 1 then
    -- 局内装备租借
    UITable[UIName2ID.InGameEquipmentRentalMainPanel] = {
        UILayer = EUILayer.HUD,
        ZOrderBase = EHUDBaseZorder.Popup,
        ZOrderOffset = 0,
        LuaPath = "DFM.Business.Module.HUDModule.UI.InGameEquipmentRental.InGameEquipmentRentalMainPanel",
        BPKey = "WBP_InGameEquipmentRentalMainView_PC",
        ReConfig = {
            IsPoolEnable = true,
        },
        SubUIs = {
            UIName2ID.InGameEquipmentRentalItemView,
            UIName2ID.IVWarehouseTemplate,
        },
    }
    UITable[UIName2ID.InGameEquipmentRentalItemView] = {
        UILayer = EUILayer.Sub,
        LuaPath = "DFM.Business.Module.HUDModule.UI.InGameEquipmentRental.InGameEquipmentRentalItemView",
        BPKey = "WBP_InGameEquipmentRentalItemView_PC",
        SubUIs = {
            UIName2ID.WareHouseContainerBox_HD,
        }
    }
else
    -- 局内装备租借
    UITable[UIName2ID.InGameEquipmentRentalMainPanel] = {
        UILayer = EUILayer.HUD,
        ZOrderBase = EHUDBaseZorder.Popup,
        ZOrderOffset = 0,
        LuaPath = "DFM.Business.Module.HUDModule.UI.InGameEquipmentRental.InGameEquipmentRentalMainPanel",
        BPKey = "WBP_InGameEquipmentRentalMainView",
        ReConfig = {
            IsPoolEnable = true,
        },
        SubUIs = {
            UIName2ID.InGameEquipmentRentalItemView,
            UIName2ID.IVWarehouseTemplate,
        },
    }
    UITable[UIName2ID.InGameEquipmentRentalItemView] = {
        UILayer = EUILayer.Sub,
        LuaPath = "DFM.Business.Module.HUDModule.UI.InGameEquipmentRental.InGameEquipmentRentalItemView",
        BPKey = "WBP_InGameEquipmentRentalItemView",
        SubUIs = {
            UIName2ID.WareHouseContainerBox,
        }
    }
end

return HUDConfig