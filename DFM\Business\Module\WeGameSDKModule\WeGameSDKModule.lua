----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMWeGameSDK)
----- LOG FUNCTION AUTO GENERATE END -----------

local UBlueprintPathsLibrary = import "BlueprintPathsLibrary"
local LuaExtension = import "LuaExtension"

--local LitePackageDownloadTableItem = require "DFM.Business.DataStruct.LitePackageStruct.LitePackageDownloadTableItem"
local FWeGameExpansionPackInfo = nil
local FWeGameExpansionPackList = nil
local LiteDownloadDataTable = nil
local ExpansionPackCategoryList = {}
local MapIDToModuleNameMap = {}
local MBNum = 1024 * 1024
local GBNum = 1024 * 1024 * 1024
---@class WeGameSDKModule : ModuleBase
local WeGameSDKModule = class("WeGameSDKModule", require("DFM.YxFramework.Managers.Module.ModuleBase"))
if PLATFORM_WINDOWS then
    UWeGameManager = import "WeGameSDKManager"
    WeGameManager = UWeGameManager.Get(GetGameInstance())
    FWeGameExpansionPackInfo = import "WeGameExpansionPackInfo"
    FWeGameExpansionPackList = import "WeGameExpansionPackList"
end
local UFlibPakHelper = import "FlibPakHelper"

function WeGameSDKModule:Ctor()
end

---------------------------------------------------------------------------------
--- Module 生命周期
---------------------------------------------------------------------------------
--- 模块Init回调，用于初始化一些数据
---@overload fun(ModuleBase, OnInitModule)
function WeGameSDKModule:OnInitModule()
    self._curInstallingPacks = {}
    self._weGamePackInfosOnQuery = {}
    self._curInstallingPacks = {}
    self._mountedPacks = {}
    if PLATFORM_WINDOWS then
        self:InitExpansionPackData()
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then

            else
                if self:IsSupportLitePackage() then
                    --这里Lua模块初始化的时候再次调用Query，以防万一
                    self:QueryExpansionPackList()
                    self:AddExpansionPackListeners()
                end
            end
        end
    end
end

--- 若为非懒加载模块，则在Init后调用;对应每个OnGameFlowChangeEnter
--- 模块默认[常驻]加载资源（预加载UI蓝图、需要用到的图片等等
---@overload fun(ModuleBase, OnLoadModule)
function WeGameSDKModule:OnLoadModule()
end

--- 无论是否懒加载都会调用，对应每个OnGameFlowChangeLeave
--- 模块默认卸载资源
---@overload fun(ModuleBase, OnUnloadModule)
function WeGameSDKModule:OnUnloadModule()
end

--- 注销LuaEvent、Timer监听
---@overload fun(ModuleBase, OnDestroyModule)
function WeGameSDKModule:OnDestroyModule()
    self:RemoveExpansionPackListeners()
end

---@overload fun(ModuleBase, OnGameFlowChangeLeave)
function WeGameSDKModule:OnGameFlowChangeLeave(gameFlowType)
    if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then
    --- WeGameSDKEventLogic.RemoveLobbyListeners()
    end
end

---@overload fun(ModuleBase, OnGameFlowChangeEnter)
function WeGameSDKModule:OnGameFlowChangeEnter(gameFlowType)
    if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then
    --- WeGameSDKEventLogic.AddLobbyListeners()
    end
end

---------------------------------------------------------------------------------
--- Loading 生命周期
--- 设置bAutoLoading = true则下列生命周期有效
--- 模块[Loading]加载资源，区分局内外
---------------------------------------------------------------------------------
---@overload fun(ModuleBase, OnLoadingLogin2Frontend)
function WeGameSDKModule:OnLoadingLogin2Frontend(gameFlowType)
end

---@overload fun(ModuleBase, OnLoadingGame2Frontend)
function WeGameSDKModule:OnLoadingGame2Frontend(gameFlowType)
end

---@overload fun(ModuleBase, OnLoadingFrontend2Game)
function WeGameSDKModule:OnLoadingFrontend2Game(gameFlowType)
end

---------------------------------------------------------------------------------
--- Module Public API
---------------------------------------------------------------------------------

---WeGameDLCResult事件
function WeGameSDKModule:GetWeGameDLCResultDelegate()
    if WeGameManager then
        return WeGameManager.OnWeGameDLCUniversalResult
    end
end

---主动刷新当前DLC状态
function WeGameSDKModule:AsyncCheckAllDlcsStateReady()
    if WeGameManager and WeGameManager.AsyncCheckAllDlcsStateReady then
        return WeGameManager:AsyncCheckAllDlcsStateReady()
    else
        logerror("[WeGameSDKModule] WeGameManager not valid")
    end
    return false
end

--获取DLC是否下载完成
---@param downloadPath string 下载地址(容器)
---@return boolean 是否拥有
---@return dlcID
---@return downloadPath 下载地址
function WeGameSDKModule:GetIsBHDDLCInstalled(downloadPath)
    local dlcID = VersionUtil.GetBHDDlcId()
    downloadPath = setdefault(downloadPath, "")
    if WeGameManager and WeGameManager.IsDLCInstalled then
        local isInstalled, downloadPath = WeGameManager:IsDLCInstalled(dlcID, downloadPath)
        return isInstalled, dlcID, downloadPath
    else
        logerror("[WeGameSDKModule] WeGameManager not valid")
    end
    return false, dlcID, downloadPath
end

--【废弃】获取DLC信息
---@param DLCInfo
---@return boolean 是否成功获取信息
---@return DLCInfo
function WeGameSDKModule:GetBHDDLCInfo()
    local dlcID = VersionUtil.GetBHDDlcId()
    local FWeGameDLCInfo = import "WeGameDLCInfo"
    local dlcInfo = FWeGameDLCInfo()
    if WeGameManager and WeGameManager.GetDLCInfo then
        local isDlcExist, dlcInfo = WeGameManager:GetDLCInfo(dlcID, dlcInfo)
        return isDlcExist, dlcInfo
    else
        logerror("[WeGameSDKModule] WeGameManager not valid")
    end
    return false, dlcInfo
end

function WeGameSDKModule:IsBHDDlcNeedUpdate()
    local dlcID = VersionUtil.GetBHDDlcId()
    if WeGameManager and WeGameManager.IsDlcNeedUpdate then
        local bNeedUpdate = WeGameManager:IsDlcNeedUpdate(dlcID)
        return bNeedUpdate
    else
        logerror("[WeGameSDKModule] WeGameManager not valid")
    end
end

----异步下载DLC
---@return boolean 是否成功
---@return dlcID
function WeGameSDKModule:AsyncDownloadBHDDLC()
    local dlcID = VersionUtil.GetBHDDlcId()
    if WeGameManager and WeGameManager.AsyncInstallDLC then
        return WeGameManager:AsyncInstallDLC(dlcID)
    else
        logerror("[WeGameSDKModule] WeGameManager not valid")
    end
    return false, dlcID
end

--异步暂停下载DLC
---@return boolean 是否成功
---@return dlcID
function WeGameSDKModule:AsyncPauseInstallBHDDLC()
    local dlcID = VersionUtil.GetBHDDlcId()
    if WeGameManager and WeGameManager.AsyncPauseInstallDLC then
        return WeGameManager:AsyncPauseInstallDLC(dlcID)
    else
        logerror("[WeGameSDKModule] WeGameManager not valid")
    end
    return false, dlcID
end

--异步恢复下载DLC
---@return boolean 是否成功
---@return dlcID
function WeGameSDKModule:AsyncResumeInstallBHDDLC()
    local dlcID = VersionUtil.GetBHDDlcId()
    if WeGameManager and WeGameManager.AsyncResumeInstallDLC then
        return WeGameManager:AsyncResumeInstallDLC(dlcID)
    else
        logerror("[WeGameSDKModule] WeGameManager not valid")
    end
    return false, dlcID
end

--异步卸载DLC
---@return boolean 是否成功
---@return dlcID
function WeGameSDKModule:AsyncUninstalBHDDLC()
    local dlcID = VersionUtil.GetBHDDlcId()
    if WeGameManager and WeGameManager.AsyncUninstalDLC then
        return WeGameManager:AsyncUninstalDLC(dlcID)
    else
        logerror("[WeGameSDKModule] WeGameManager not valid")
    end
    return false, dlcID
end

---异步显示DLC管理Window
---@return boolean 是否成功
---@return dlcID
function WeGameSDKModule:AsyncShowDLCManageWindow()
    if WeGameManager and WeGameManager.AsyncShowDLCManageWindow then
        return WeGameManager:AsyncShowDLCManageWindow()
    else
        logerror("[WeGameSDKModule] WeGameManager not valid")
    end
    return false
end

---小包功能

function WeGameSDKModule:InitExpansionPackData()
    LiteDownloadDataTable = Facade.TableManager:GetTable("LitePackageDownload", true)
    local ShowCategoryDataTable = Facade.TableManager:GetTable("LitePackageCategory")

    if not LiteDownloadDataTable then
        logerror("[WeGameSDKModule] LitePackageDownload not valid")
        return
    end

    local bIsShipping = VersionUtil.IsShipping()
    for k, v in pairs(LiteDownloadDataTable) do
        local bAdd = true
        if bIsShipping and v.MapOrModeDownload == 3 then
            bAdd = false
        end

        if bAdd then
            if v.IsShow == 1 then
                if ExpansionPackCategoryList[v.ShowCategory] == nil then
                    local categoryData = ShowCategoryDataTable[v.ShowCategory]
                    if categoryData then
                        ExpansionPackCategoryList[v.ShowCategory] = {Priority=categoryData.Priority,ShowCategoryID=categoryData.ShowCategoryID, CategoryTitle = categoryData.CategoryTitle,Quests={}}
                    end
                end
                if ExpansionPackCategoryList[v.ShowCategory] and type(ExpansionPackCategoryList[v.ShowCategory]["Quests"]) == "table" then
                    table.insert(ExpansionPackCategoryList[v.ShowCategory]["Quests"], { QuestID = v.QuestID, Priority = v.Priority})
                end
            end
            if v.MapOrModeDownload == 1 or v.MapOrModeDownload == 2 then
                MapIDToModuleNameMap[v.MapOrModeParams] = v.ModuleKey
                local arr = {}
                for mapid in string.gmatch(v.MapOrModeParams, "[^,]+") do
                    table.insert(arr, mapid)
                end

                for i, mapid in ipairs(arr) do
                    MapIDToModuleNameMap[mapid] = v.ModuleKey
                end
            end
        end
    end

    table.sort(ExpansionPackCategoryList,function(a, b)
                                                    if a == nil or b == nil then
                                                        return false
                                                    end
                                                    return a.Priority > b.Priority
                                                end)


    for k, v in pairs(ExpansionPackCategoryList) do
        if ExpansionPackCategoryList[k] and type(ExpansionPackCategoryList[k]["Quests"]) == "table" then
            table.sort(ExpansionPackCategoryList[k]["Quests"],function(a, b) return a.Priority > b.Priority end)
        end
    end

    loginfo("MapIDToModuleNameMap")
    LogUtil.LogTable(MapIDToModuleNameMap)

    loginfo("ExpansionPackCategoryList")
    LogUtil.LogTable(ExpansionPackCategoryList)
    self._weGamePackInfosOnQuery = {}
    self._curInstallingPacks = {}
    self._mountedPacks = {}
end

function WeGameSDKModule:GetExpansionPackTableDataList()
    return ExpansionPackCategoryList
end

function WeGameSDKModule:GetExpansionPackTableDataListCnt()
    local cnt = 0
    for k, v in pairs(ExpansionPackCategoryList) do
        if v then
            cnt = cnt + 1
        end
    end
    return cnt
end

function WeGameSDKModule:GetExpansionPackTableDataByQuestID(questID)
    return LiteDownloadDataTable[questID]
end

function WeGameSDKModule:GetExpansionPackTableDataByPackID(packID)
    for k, v in pairs(LiteDownloadDataTable) do
        if v.ModuleKey == packID then
            return v
        end
    end
    return nil
end

function WeGameSDKModule:AddExpansionPackListeners()
    if self:IsCPPManagerAvaiable() then
        WeGameManager.OnWeGamePackUniversalResult:Clear()
        WeGameManager.OnWeGamePackUniversalResult:Add(CreateCPlusCallBack(self.OnWeGamePackResult, self))
    end
end

function WeGameSDKModule:RemoveExpansionPackListeners()
    if self:IsCPPManagerAvaiable() then
        WeGameManager.OnWeGamePackUniversalResult:Clear()
    end
end

function WeGameSDKModule:OnWeGamePackResult(eventID, eventData)
    loginfo('WeGameSDKModule.OnWeGamePackResult eventID = '..eventID)
    if eventID == UE.EWeGamePackResultType.PackQueryExpansionPackListResult then
        self._curInstallingPacks = {}
        if not self:CheckAndPopErrorCodeTips(eventData.ErrorCode) then
            local WeGameExpansionPackList = self:GetWeGameExpansionPackList()
            if WeGameExpansionPackList then
                for i, v in pairs(WeGameExpansionPackList.ExpansionPackList) do
                    if v.InstallState == UE.EWeGamePackInstallState.PackInstallStateInstalling then
                        self:AddInstallingPackCache(v.PackID)
                    elseif v.InstallState == UE.EWeGamePackInstallState.PackInstallStateInstalled then
                        if not self:IsPackMounted(v.packId) then
                            self:MountPakFilesByPackId(v.PackID)
                        end
                    end
                end

                if #WeGameExpansionPackList.ExpansionPackList == 0 then
                    --如果扩展包个数为0，也判断为扩展包功能没有开启，因为正常情况下 扩展包的数量一定是大于0的
                    Module.WeGameSDK:MountAllExpansionPaks()
                end
            end
        end
    elseif eventID == UE.EWeGamePackResultType.PackInstallExpansionPackStart then
        if not self:CheckAndPopErrorCodeTips(eventData.ErrorCode) then
            self:AddInstallingPackCache(eventData.PackID)
            Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged:Invoke(eventData.PackID, nil)
        end
    elseif eventID == UE.EWeGamePackResultType.PackInstallExpansionPackProgress then
        self:AddInstallingPackCache(eventData.PackID)
        Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackInstallProgress:Invoke(self:_MakeLuaTypeProgressInfo(eventData))
    elseif eventID == UE.EWeGamePackResultType.PackPauseInstallExpansionPack then
        self:CleanInstallingPackCache(eventData.PackID)
        if not self:CheckAndPopErrorCodeTips(eventData.ErrorCode) then
            Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged:Invoke(eventData.PackID, nil)
        end
    elseif eventID == UE.EWeGamePackResultType.PackResumeInstallExpansionPack then
        self:AddInstallingPackCache(eventData.PackID)
        if not self:CheckAndPopErrorCodeTips(eventData.ErrorCode) then
            Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged:Invoke(eventData.PackID, nil)
        end
    elseif eventID == UE.EWeGamePackResultType.PackInstallExpansionPackResult then
        self:CleanInstallingPackCache(eventData.PackID)
        local retState = nil
        if not self:CheckAndPopErrorCodeTips(eventData.ErrorCode, eventData.ErrorMsg) then
            retState = Module.ExpansionPackCoordinator.Config.PackState.Installed
            if not self:IsPackMounted(eventData.packId) then
                self:MountPakFilesByPackId(eventData.PackID)
            end
        elseif eventData.ErrorCode == 1048003 then
            retState = Module.ExpansionPackCoordinator.Config.PackState.NotInstalled
        end
        Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged:Invoke(eventData.PackID, retState)
    elseif eventID == UE.EWeGamePackResultType.PackUninstallExpansionPackResult then
        self:CleanInstallingPackCache(eventData.PackID)
        if not self:CheckAndPopErrorCodeTips(eventData.ErrorCode) then
            Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged:Invoke(eventData.PackID, nil)
        end
    end
end

function WeGameSDKModule:CheckAndPopErrorCodeTips(errorCode, errorMsg)
    logerror("[WeGameSDKModule:CheckAndPopErrorCodeTips]:"..tostring(errorCode))
    if errorCode ~= 0 then
        local ErrorMsgs = {}
        ErrorMsgs[1048001] = Module.WeGameSDK.Config.Loc.ExpansionPackIDNotFound
        ErrorMsgs[1048002] = Module.WeGameSDK.Config.Loc.ExpansionPackOperationInvalid
        ErrorMsgs[1048003] = Module.WeGameSDK.Config.Loc.ExpansionPackCancelInstall
        ErrorMsgs[1048004] = Module.WeGameSDK.Config.Loc.ExpansionPackInstallFailed
        --ErrorMsgs[1048005] = Module.WeGameSDK.Config.Loc.ExpansionPackQueryTimeout
        if errorCode == 1048005 then
            --该环节是为了支持wegame/launcher版本低于支持扩展包的版本要求时，全量加载所有扩张包目录下pak,因为游戏目录已经按照新规则进行了规划
            Module.WeGameSDK:MountAllExpansionPaks()
            return true
        end

        local msg = errorMsg or ""
        if string.len(msg) > 0 or ErrorMsgs[errorCode] == nil then
            Module.CommonTips:ShowSimpleTip(StringUtil.Key2StrFormat(Module.WeGameSDK.Config.Loc.ExpansionPackErrorCodeMsg, {["errCode"]=errorCode, ["errMsg"]=msg}))
        else
            Module.CommonTips:ShowSimpleTip(ErrorMsgs[errorCode])
        end

        return true
    end
    return false
end

function WeGameSDKModule:AddInstallingPackCache(packID)
    for k, v in pairs(self._curInstallingPacks) do
        if packID == v then
            return
        end
    end
    table.insert(self._curInstallingPacks, packID)
end

function WeGameSDKModule:CleanInstallingPackCache(packID)
    for i=#self._curInstallingPacks,1,-1 do
        if self._curInstallingPacks[i] == packID then
            table.remove(self._curInstallingPacks, i)
        end
    end
end

function WeGameSDKModule:HaveInstallingQuests()
    return self:GetInstallingQuestsCount() > 0
end

function WeGameSDKModule:GetInstallingQuestsCount()
    loginfo("WeGameSDKModule:GetInstallingQuestsCount: "..#self._curInstallingPacks)
    return #self._curInstallingPacks
end

function WeGameSDKModule:GetInstallingQuestPackIdByIdx(index)
    loginfo("WeGameSDKModule:GetInstallingQuestPackIdByIdx: "..index)
    LogUtil.LogTable(self._curInstallingPacks)
    if self._curInstallingPacks[index] then
        return self._curInstallingPacks[index]
    end
    return nil
end

function WeGameSDKModule:IsCPPManagerAvaiable()
    if WeGameManager then
        return true
    else
        logerror("[WeGameSDKModule] WeGameManager not valid")
        return false
    end
end

function WeGameSDKModule:QueryExpansionPackList()
    if self:IsCPPManagerAvaiable() then
        return WeGameManager:AsyncQueryExpansionPackList()
    end
    return false
end

function WeGameSDKModule:GetExpansionPackCount()
    if self:IsCPPManagerAvaiable() then
        return WeGameManager:GetExpansionPackCount()
    end
    return 0
end

function WeGameSDKModule:GetWeGameExpansionPackInfoByIndex(idx)
    local PackInfo = nil
    if self:IsCPPManagerAvaiable() then
        PackInfo = FWeGameExpansionPackInfo()
        local ret = false
        ret, PackInfo = WeGameManager:GetExpansionPackInfo(idx, PackInfo)
    end
    return PackInfo
end

function WeGameSDKModule:AsyncInstallExpansionPack(PackID)
    if self:IsCPPManagerAvaiable() then
        return WeGameManager:AsyncInstallExpansionPack(PackID)
    end
    return false
end

function WeGameSDKModule:AsyncPauseInstallExpansionPack(PackID)
    if self:IsCPPManagerAvaiable() then
        return WeGameManager:AsyncPauseInstallExpansionPack(PackID)
    end
    return false
end

function WeGameSDKModule:AsyncResumeInstallExpansionPack(PackID)
    if self:IsCPPManagerAvaiable() then
        return WeGameManager:AsyncResumeInstallExpansionPack(PackID)
    end
    return false
end

function WeGameSDKModule:AsyncCancelInstallExpansionPack(PackID)
    if self:IsCPPManagerAvaiable() then
        return WeGameManager:AsyncCancelInstallExpansionPack(PackID)
    end
    return false
end

function WeGameSDKModule:AsyncRemoveExpansionPack(PackID)
    if self:IsCPPManagerAvaiable() then
        return WeGameManager:AsyncRemoveExpansionPack(PackID)
    end
    return false
end

function WeGameSDKModule:GetWeGameExpansionPackInfoByPackID(packID)
    local packInfo = nil
    if self:IsCPPManagerAvaiable() then
        if self._weGamePackInfosOnQuery[packID] then
            packInfo = self:GetWeGameExpansionPackInfoByIndex(self._weGamePackInfosOnQuery[packID].Index)
        else
            packInfo = WeGameManager:GetPackInfo(packID)
        end
    end
    return packInfo
end

function WeGameSDKModule:_MakeLuaTypePackInfo(cppData)
    local luaData = {PackID="",PackName="",PackDes="",PackInstallSize="",DownloadSize="",DownloadedSize="",InstallState=0,Index=0}
    if cppData then
        luaData.PackID = cppData.PackID
        luaData.PackName = cppData.PackName
        luaData.PackDes = cppData.PackDes
        luaData.PackInstallSize = cppData.PackInstallSize
        luaData.DownloadSize = cppData.DownloadSize
        luaData.DownloadedSize = cppData.DownloadedSize
        luaData.InstallState = cppData.InstallState
        luaData.Index = cppData.Index
    end
    return luaData
end

function WeGameSDKModule:_MakeLuaTypeProgressInfo(cppData)
    local luaData = {PackID="",Progress=0,FinishedBytes="",TotalBytes="",Speed=0}
    if cppData then
        luaData.PackID = cppData.PackID
        luaData.Progress = cppData.Progress
        luaData.FinishedBytes = cppData.FinishedBytes
        luaData.TotalBytes = cppData.TotalBytes
        luaData.Speed = cppData.Speed
    end
    return luaData
end

function WeGameSDKModule:GetWeGameExpansionPackList()
    if self:IsCPPManagerAvaiable() then
        local PackList = WeGameManager:GetExpansionPackList()
        for i, v in pairs(PackList.ExpansionPackList) do
            self._weGamePackInfosOnQuery[v.PackID] = self:_MakeLuaTypePackInfo(v)
        end

        loginfo("WeGameSDKModule:GetWeGameExpansionPackList")
        LogUtil.LogTable(self._weGamePackInfosOnQuery)

        return PackList
    end
    return nil
end

function WeGameSDKModule:IsSupportLitePackage()
    if self:IsCPPManagerAvaiable() then
        return WeGameManager:IsSupportLitePackage()
    end
    return false
end

function WeGameSDKModule:IsMapInstalled(mapID)
    return false
end

function WeGameSDKModule:GetModuleNameByMapId(mapID)
    --moduleName equals packId
    local mapIDStr = tostring(mapID)
    local moduleName = MapIDToModuleNameMap[mapIDStr]
    if not moduleName then
        --assert(mapIDStr ~= "6" and mapIDStr ~= "14" and mapIDStr ~= "21" and mapIDStr ~= "22")
        logerror("[WeGameSDKModule] GetModuleNameByMapId:"..mapID..", MapIDToModuleNameMap has no content.")
    end
    return moduleName
end

function WeGameSDKModule:GetQuestNameByMapId(mapID)
    local packId = self:GetModuleNameByMapId(mapID)
    if packId then
        local packData = self:GetExpansionPackTableDataByPackID(packId)
        if packData then
            return packData.QuestName
        end
    end
    return ""
end

function WeGameSDKModule:GetQuestIdByMapId(mapID)
    local packId = self:GetModuleNameByMapId(mapID)
    if packId then
        local packData = self:GetExpansionPackTableDataByPackID(packId)
        if packData then
            return packData.QuestID
        end
    end
    return 0
end

function WeGameSDKModule:IsDownloadedByModuleName(moduleName)
    if moduleName == nil then
        return true
    end
    local packInfo = self:GetWeGameExpansionPackInfoByPackID(moduleName)
    if packInfo and string.len(packInfo.PackID) > 0 then
        return packInfo.InstallState == UE.EWeGamePackInstallState.PackInstallStateInstalled
    end
    return true
end

function WeGameSDKModule:IsDownloadingByModuleName(moduleName)
    local packInfo = self:GetWeGameExpansionPackInfoByPackID(moduleName)
    if packInfo and string.len(packInfo.PackID) > 0 then
        return packInfo.InstallState == UE.EWeGamePackInstallState.PackInstallStateInstalling
    end
    return false
end

function WeGameSDKModule:IsWaitingByModuleName(moduleName)
    local packInfo = self:GetWeGameExpansionPackInfoByPackID(moduleName)
    if packInfo and string.len(packInfo.PackID) > 0 then
        return packInfo.InstallState == UE.EWeGamePackInstallState.PackInstallStateWaitForInstallation
    end
    return false
end

function WeGameSDKModule:CancelByModuleName(moduleName)
    local packInfo = self:GetWeGameExpansionPackInfoByPackID(moduleName)
    if packInfo then
        if packInfo.InstallState == UE.EWeGamePackInstallState.PackInstallStateWaitForInstallation or
            packInfo.InstallState == UE.EWeGamePackInstallState.PackInstallStateInstalling or
            packInfo.InstallState == UE.EWeGamePackInstallState.PackInstallStatePauseInstallation then
            return self:AsyncCancelInstallExpansionPack(moduleName)
        elseif packInfo.InstallState == UE.EWeGamePackInstallState.PackInstallStateInstalled then
            logerror("[WeGameSDKModule] packid:", moduleName, ", is already installed.")
        end
    end
    return false
end

function WeGameSDKModule:PauseByModuleName(moduleName)
    local packInfo = self:GetWeGameExpansionPackInfoByPackID(moduleName)
    if packInfo then
        if packInfo.InstallState == UE.EWeGamePackInstallState.PackInstallStateWaitForInstallation or
                packInfo.InstallState == UE.EWeGamePackInstallState.PackInstallStateInstalling then
            return self:AsyncPauseInstallExpansionPack(moduleName)
        elseif packInfo.InstallState == UE.EWeGamePackInstallState.PackInstallStateInstalled then
            logerror("[WeGameSDKModule] packid:", moduleName, ", is already installed.")
        end
    end
    return false
end

function WeGameSDKModule:DownloadByModuleName(moduleName)
    local packInfo = self:GetWeGameExpansionPackInfoByPackID(moduleName)
    if packInfo then
        if packInfo.InstallState == UE.EWeGamePackInstallState.PackInstallStateWaitForInstallation or
                packInfo.InstallState == UE.EWeGamePackInstallState.PackInstallStateNotInstalled or
                packInfo.InstallState == UE.EWeGamePackInstallState.PackInstallStateInstallationFailed then
            return self:AsyncInstallExpansionPack(moduleName)
        elseif packInfo.InstallState == UE.EWeGamePackInstallState.PackInstallStatePauseInstallation then
            return self:AsyncResumeInstallExpansionPack(moduleName)
        else
            logerror("[WeGameSDKModule] DownloadByModuleName "..moduleName.."State:".. packInfo.InstallState)
        end
    end
    return false
end

function WeGameSDKModule:MakeDownloadProgressInfoByPackInfo(info)
    loginfo("WeGameSDKModule.MakeDownloadProgressInfoByPackInfo")
    LogUtil.LogTable(info)
    local downloadInfo = {}
    downloadInfo["FormatSize"] = "- / -MB"
    downloadInfo["PercentValue"] = 0.0
    downloadInfo["FormatNowSize"] = "-MB"
    downloadInfo["FormatTotal2SubSize"] = "-MB"

    if info then
        local nowSize = 0
        local totalSize = 0
        local percent = 0.0
        if info.FinishedBytes then
            nowSize = tonumber(info.FinishedBytes) or 0
            totalSize = tonumber(info.TotalBytes) or 0
            if totalSize > 0 then
                percent = info.Progress /100.0
            end
        elseif info.DownloadedSize then
            loginfo("info.DownloadedSize:"..tostring(info.DownloadedSize))
            loginfo("info.DownloadSize:"..tostring(info.DownloadSize))
            loginfo("info.PackInstallSize:"..tostring(info.PackInstallSize))
            nowSize = tonumber(info.DownloadedSize) or 0
            loginfo("nowSize:"..nowSize)
            totalSize = tonumber(info.PackInstallSize) or 0
            --if info.InstallState == UE.EWeGamePackInstallState.PackInstallStateNotInstalled then
            --    totalSize = tonumber(info.PackInstallSize) or 0
            --end
            loginfo("totalSize:"..totalSize)
            if totalSize > 0 then
                percent = math.round((nowSize / totalSize) * 1000) / 10
            end
        end
        --local showStr = string.format("%.1f / %.1fMB (%.1f%%)", nowSize / MBNum,
        --        totalSize / MBNum, percent)
        local showStr = string.format("%.1f / %.1fMB", nowSize / MBNum, totalSize / MBNum)

        downloadInfo["FormatSize"] = showStr
        downloadInfo["PercentValue"] = percent
        downloadInfo["FormatNowSize"] = string.format("%.1fMB", nowSize / MBNum)
        downloadInfo["FormatTotal2SubSize"] = string.format("%.1fMB", (totalSize - nowSize) / MBNum)
    end
    return downloadInfo
end

function WeGameSDKModule:GetDownloadProgressInfoByModuleName(moduleName)
    return self:MakeDownloadProgressInfoByPackInfo(self:GetWeGameExpansionPackInfoByPackID(moduleName))
end

function WeGameSDKModule:GetNowSizeByModuleName(moduleName)
    local packInfo = self:GetWeGameExpansionPackInfoByPackID(moduleName)
    if packInfo then
        return tonumber(packInfo.DownloadedSize)
    end
    return 0
end

function WeGameSDKModule:GetTotalSizeByModuleName(moduleName)
    local packInfo = self:GetWeGameExpansionPackInfoByPackID(moduleName)
    if packInfo then
        return tonumber(packInfo.PackInstallSize)
    end
    return 0
end

function WeGameSDKModule:GetRemainderSizeByModuleName(moduleName)
    local remainderSize = 0
    local packInfo = self:GetWeGameExpansionPackInfoByPackID(moduleName)
    if packInfo then
        remainderSize = tonumber(packInfo.PackInstallSize) - tonumber(packInfo.DownloadedSize)
        remainderSize = math.max(remainderSize,0)
    end
    return remainderSize
end

function WeGameSDKModule:MakeRemainderSizeByPackInfo(packInfo)
    local remainderSize = 0
    if packInfo then
        local totalSize = tonumber(packInfo.PackInstallSize) or 0
        local nowSize = tonumber(packInfo.DownloadedSize) or 0
        if packInfo.FinishedBytes then
            totalSize = tonumber(packInfo.TotalBytes) or 0
            nowSize = tonumber(packInfo.FinishedBytes) or 0
        end
        remainderSize = totalSize - nowSize
        remainderSize = math.max(remainderSize,0)
    end
    return string.format("%.1fMB", remainderSize / MBNum)
end

function WeGameSDKModule:PauseAll()
    local WeGameExpansionPackList = self:GetWeGameExpansionPackList()
    if WeGameExpansionPackList then
        for i, v in pairs(WeGameExpansionPackList.ExpansionPackList) do
            if v.InstallState == UE.EWeGamePackInstallState.PackInstallStateInstalling or
                    v.InstallState == UE.EWeGamePackInstallState.PackInstallStateWaitForInstallation then
                self:AsyncPauseInstallExpansionPack(v.PackID)
            end
        end
    end
end

function WeGameSDKModule:ScanFilesWithExtenAndFilter(folderPath, extension, filter)
    loginfo("[WeGameSDKModule:ScanFilesWithExtenAndFilter] folderPath:"..folderPath.." extension:"..extension)
    if filter ~= nil then
        loginfo("[WeGameSDKModule:ScanFilesWithExtenAndFilter] filter:"..filter)
    end
    local filterFiles = {}
    local files = {}
    files = LuaExtension.Ext_FindFilesRecursive(files, folderPath, "*"..extension, true, false, false)
    if #files > 0 then
        if filter and type(filter) == "string" then
            for k, v in pairs(files) do
                loginfo("[WeGameSDKModule:ScanFilesWithExtenAndFilter] found files:"..v)
                if string.match(v, filter) then
                    table.insert(filterFiles, v)
                end
            end
            loginfo("[WeGameSDKModule:ScanFilesWithExtenAndFilter] fit filterFiles num:",(#filterFiles))
            return filterFiles
        else
            return files
        end
    end

    return {}
end

function WeGameSDKModule:MountAllExpansionPaks()
    logerror("[WeGameSDKModule:MountAllExpansionPaks]")
    --加载能找到的全部pak表明现在扩展包功能无法正常工作
    if self:IsCPPManagerAvaiable() then
        WeGameManager:SetLitePackageFeature(false)
    end

    local folderPath = FPaths.ProjectDir() .."PackContent/Paks/"
    local expansionFolderPath = UBlueprintPathsLibrary.ConvertRelativePathToFull(folderPath, "")
    loginfo("[WeGameSDKModule:MountAllExpansionPaks] expansionFolderPath:"..expansionFolderPath)
    if not LuaExtension.Ext_DirectoryExists(expansionFolderPath) then
        logerror("WeGameSDKModule:MountAllExpansionPaks() path:"..expansionFolderPath.." not exists")
        return
    end

    local foundPakFiles = self:ScanFilesWithExtenAndFilter(expansionFolderPath, ".pak", nil)

    for k, v in pairs(foundPakFiles) do
        --loginfo("[WeGameSDKModule:MountAllExpansionPaks] pak filepath:"..v)
        local ret = UFlibPakHelper.MountPak(v, 5, "")
        if not ret then
            logerror("[WeGameSDKModule MountAllExpansionPaks] mount failed file:"..v)
        end
    end
end

function WeGameSDKModule:MountPakFilesByPackId(packID)
    loginfo("[WeGameSDKModule:MountPakFilesByPackId] :"..packID)
    local packDataInfo = self:GetExpansionPackTableDataByPackID(packID)
    if not packDataInfo then
        logerror("[WeGameSDKModule:MountPakFilesByPackId] not such package:"..packID)
        return
    end

    local folderPath = FPaths.ProjectDir() .."PackContent/Paks/"
    local expansionFolderPath = UBlueprintPathsLibrary.ConvertRelativePathToFull(folderPath, "")
    if not LuaExtension.Ext_DirectoryExists(expansionFolderPath) then
        logerror("WeGameSDKModule:MountPakFilesByPackId() path:"..expansionFolderPath.." not exists")
        return
    end

    local chunkIDs = string.split(packDataInfo.ChunkID, ",")
    local pakFilesList = {}
    if #chunkIDs > 0 then
        for k, v in pairs(chunkIDs) do
            local curChunkPaks = self:ScanFilesWithExtenAndFilter(expansionFolderPath, ".pak", "pakchunk"..tostring(v))
            table.append(pakFilesList,curChunkPaks)
        end
    end

    if #pakFilesList == 0 then
        --未找到该pack对应的文件配置
        logerror("[WeGameSDKModule] MountPackByPackId not files found, packID:"..packID)
        return
    end

    local mountSucceedFiles = {}
    local mountFailedFiles = {}
    for k, v in pairs(pakFilesList) do
        if not self:IsFileMounted(v) then
            local ret = UFlibPakHelper.MountPak(v, 5, "")
            if ret then
                table.insert(mountSucceedFiles, v)
            else
                logerror("[WeGameSDKModule] mount failed file:"..v)
                table.insert(mountFailedFiles, v)
            end
        end
    end
    if #mountFailedFiles > 0 then
        --有文件加载失败，需要将加载成功的卸载了，之后是否需要弹tip提示呢？
        for k, v in pairs(mountSucceedFiles) do
            local ret = UFlibPakHelper.UnMountPak(v)
            loginfo("[WeGameSDKModule] UnMountPak file:"..v.." ret:"..tostring(ret))
        end
    else
        table.insert(self._mountedPacks,packID)
        if Server.LitePackServer ~= nil and Server.LitePackServer.bValid == true then
            Server.LitePackServer:AddWeGameInstalledPackQuestIDs(packDataInfo.QuestID)
        end
    end
end

function WeGameSDKModule:IsPackMounted(packID)
    for k, v in pairs(self._mountedPacks) do
        if packID == v then
            return true
        end
    end
    return false
end

function WeGameSDKModule:IsFileMounted(filePath)
    local targetFileName = self:getFilenameFromPath(filePath)
    loginfo("[WeGameSDKModule] IsFileMounted targetFileName:"..targetFileName)
    if string.len(targetFileName) > 0 then
        local allMountedFiles = UFlibPakHelper.GetAllMountedPaks()
        for k, v in pairs(allMountedFiles) do
            --loginfo("[WeGameSDKModule] IsFileMounted GetAllMountedPaks file:"..v)
            local fileName = self:getFilenameFromPath(v)
            loginfo("[WeGameSDKModule] IsFileMounted fileName:"..fileName)
            if string.len(fileName) > 0 and targetFileName == fileName then
                loginfo("[WeGameSDKModule] IsFileMounted file: "..fileName.." mounted for sure!")
                return true
            end
        end
    end
    return false
end

--从路径中获取[文件名.后缀]
function WeGameSDKModule:getFilenameFromPath(filePath)
    if filePath == nil then
        return '';
    end

    return string.match(filePath, "([^/]*)$");
end

function WeGameSDKModule:CheckIsMapsDownload(mapList,bFromRoom)
    loginfo("WeGameSDKModule:CheckIsMapsDownload",bFromRoom)
    logtable(mapList, true)
    if self:IsSupportLitePackage() then
        for key, value in pairs(mapList or {}) do
            local moduleName = self:GetModuleNameByMapId(value)
            local isDownload = self:IsDownloadedByModuleName(moduleName)
            if isDownload then
                loginfo("WeGameSDKModule:CheckIsMapsDownload isDownload")
                return true
            end
        end
        Module.Social:ShowNotDownloadTips(mapList[1],nil,bFromRoom)
        return false

    else
        loginfo("WeGameSDKModule:CheckIsMapsDownload not support litepackage")
        return true
    end
end

function WeGameSDKModule:GetQuestNameByModuleName(packId)
    local packData = self:GetExpansionPackTableDataByPackID(packId)
    if packData then
        return packData.QuestName
    end
    return ""
end

function WeGameSDKModule:GetAllPaksSizeByPath(folderPath)
    local pakTotalSize = 0
    local expansionFolderPath = UBlueprintPathsLibrary.ConvertRelativePathToFull(folderPath, "")
    if not LuaExtension.Ext_DirectoryExists(expansionFolderPath) then
        logerror("[WeGameSDKModule:GetAllPaksSizeByPath] path not exist:"..expansionFolderPath)
        return pakTotalSize
    end
    local foundPakFiles = Module.WeGameSDK:ScanFilesWithExtenAndFilter(expansionFolderPath, ".pak", nil)
    if foundPakFiles and #foundPakFiles > 0 then
        for k, v in pairs(foundPakFiles) do
            if v and v ~= "" then
                local size = UFlibPakHelper.GetExtenFileSize(v)
                pakTotalSize = pakTotalSize + size
            end
        end
    end
    return pakTotalSize
end

function WeGameSDKModule:GetDownloadCategary(iItemID)
    local tItem = ItemBase:NewIns(iItemID)
    local ret = "None"
    loginfo("[WeGameSDKModule:GetDownloadCategary] iItemID:"..iItemID..", tItem Type:"..tostring(tItem.itemMainType))
    if tItem.itemMainType == EItemType.WeaponSkin or tItem.itemMainType == EItemType.VehicleSkin then
        local pakSelectorTableAllDataTable = Facade.TableManager:GetTable("PakSelectorTable")
        if isinvalid(pakSelectorTableAllDataTable) then
            logerror("[WeGameSDKModule.GetDownloadCategary] PakSelectorTable is nil iItemID:"..iItemID)
            return ret
        end
        local numberItemID = tonumber(iItemID)
        for _, itemPakInfo in pairs(pakSelectorTableAllDataTable) do
            local id = tonumber(itemPakInfo.Id)
            if id == numberItemID then
                -- 武器皮肤
                ret = itemPakInfo.MultiModuleName
                loginfo("[WeGameSDKModule:GetDownloadCategary] iItemID:"..iItemID..", modulename ret:"..ret)
                return ret
            end
        end
        logerror("[WeGameSDKModule.GetDownloadCategary] PakSelectorTable item not found:"..iItemID)
    end
    return ret
end

return WeGameSDKModule
