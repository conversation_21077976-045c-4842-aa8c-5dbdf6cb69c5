local mapping = {
ABTagQueryFlow = [[tglog]],
AIAnalysisInfo = [[tglog]],
AIBaseInfo = [[ds_tglog]],
AIDropFlow = [[ds_tglog]],
AIFightDesignatedPlayer = [[ds_tglog]],
AIFightFlow = [[ds_tglog]],
AIHurtWeakPlayerFlow = [[ds_tglog]],
AILabAnalysisInfo = [[ds_tglog]],
AILabBattleInfo = [[ds_tglog]],
AILabDeliver = [[ds_tglog]],
AILabDropInfo = [[ds_tglog]],
AILabGameStatis = [[ds_tglog]],
AILabLootInfo = [[ds_tglog]],
AILabMPMonitorInfo = [[tglog]],
AILabMpShootBehaviorFeature = [[tglog]],
AILabPlayerStatis = [[ds_tglog]],
AILabRoundFlow = [[tglog]],
AILabSDKInfo = [[ds_common]],
AILabSDKRouteInfo = [[ds_common]],
AILabSingleFightInfo = [[tglog]],
AIPlayerData = [[common]],
AIRoomArg = [[ds_common]],
AIStuckInfo = [[ds_tglog]],
ASAIdInfo = [[tglog]],
ASMIadInfo = [[tglog]],
AccountCapability = [[tglog]],
AccountExpChange = [[common]],
AccountExpChangeInfo = [[cs_account]],
AccountExpFlow = [[tglog]],
AccountExpSettlementFlow = [[tglog]],
AccountLoginFlow = [[tglog]],
AccountPunishItem = [[common]],
AchievementFlow = [[tglog]],
AchievementStateFlow = [[tglog]],
ActThemeEavesdroppingCase = [[cs_activity]],
ActThemeEavesdroppingInfo = [[cs_activity]],
ActThemeEavesdroppingSpecialAnswerAward = [[cs_activity]],
ActThemeSOCShopCost = [[cs_activity]],
ActThemeSOCShopItem = [[cs_activity]],
ActThemeSheOneCardInfo = [[cs_activity]],
ActThemeSheOneCardKeyValue = [[cs_activity]],
ActThemeSheOneCardMPFlow = [[cs_activity]],
ActThemeSheOneCardRecvCard = [[cs_activity]],
ActThemeSheOneCardSendRecvFlow = [[cs_activity]],
ActThemeSheOneCardShopBuyRecord = [[cs_activity]],
ActThemeStarInfo = [[cs_activity]],
ActivityAward = [[cs_activity]],
ActivityClientReport = [[tglog]],
ActivityClientReportNew = [[tglog]],
ActivityClientSBCReport = [[tglog]],
ActivityCurrencyInfo = [[cs_activity]],
ActivityCurrencyJumpInfo = [[cs_activity]],
ActivityExchangePage = [[cs_activity]],
ActivityExchangePlan = [[cs_activity]],
ActivityExchangeProp = [[cs_activity]],
ActivityFlow = [[tglog]],
ActivityInfo = [[cs_activity]],
ActivityItemReport = [[tglog]],
ActivityJumpConfig = [[cs_activity]],
ActivityJumpPair = [[cs_activity]],
ActivityMilestoneAward = [[cs_activity]],
ActivityObjective = [[cs_activity]],
ActivityObjectiveConditions = [[cs_activity]],
ActivityPreviewInfo = [[cs_activity]],
ActivityPropJumpInfo = [[cs_activity]],
ActivityQuestionAnswer = [[cs_activity]],
ActivityRewardExpandInfo = [[cs_activity]],
ActivityServerReport = [[tglog]],
ActivityTaskInfo = [[cs_activity]],
AddButtonStatus = [[cs_playerinfo]],
AddEquipPoolFlow = [[tglog]],
AdjustScoreFlow = [[tglog]],
AfkPunishInfo = [[zone2dsa]],
AhsarahTravelHeroInfo = [[cs_activity]],
AhsarahTravelInfo = [[cs_activity]],
AhsarahTravelLineInfo = [[cs_activity]],
AhsarahTravelMilestoneMessage = [[cs_activity]],
AhsarahTravelMilestoneReply = [[cs_activity]],
AhsarahTravelPlayerReply = [[cs_activity]],
AhsarahTravelScoreDesc = [[cs_activity]],
AiLabBotConfig = [[cs_robot]],
AllocMandelResultFlow = [[tglog]],
Any2DsAckNtf = [[zone2dsa]],
Any2DsBeginMatchReq = [[zone2dsa]],
Any2DsBeginRaidReq = [[zone2dsa]],
Any2DsCanHalfJoinReq = [[zone2dsa]],
Any2DsCanHalfJoinRes = [[zone2dsa]],
Any2DsFatherForkChildReq = [[zone2dsa]],
Any2DsFatherQuitNtf = [[zone2dsa]],
Any2DsMatchSettlementRes = [[zone2dsa]],
Any2DsPVESettlementRes = [[zone2dsa]],
Any2DsPlayerAbandonMatchNtf = [[zone2dsa]],
Any2DsPlayerCsvTableReq = [[zone2dsa]],
Any2DsPlayerLeaveSafeHouseNtf = [[zone2dsa]],
Any2DsPlayerSettlementRes = [[zone2dsa]],
Any2DsPropGuidePriceNtf = [[zone2dsa]],
Any2DsSyncPlayerInfoReq = [[zone2dsa]],
Any2DsSyncPlayerInfoRes = [[zone2dsa]],
Any2DsSyncSafeHousePlayerInfoReq = [[zone2dsa]],
Any2DsSyncTDMWeaponDesignReq = [[zone2dsa]],
Any2DsSyncTDMWeaponStoreReq = [[zone2dsa]],
Any2DsTkvSettlementRes = [[zone2dsa]],
Any2DsagentSendToZoneReq = [[zone2dsa]],
ArchiveEnemyInfo = [[cs_activity]],
ArchiveInfo = [[cs_activity]],
ArchiveSequenceInfo = [[cs_activity]],
ArchiveTaskLineInfo = [[cs_activity]],
ArenaPlayer = [[ds_common]],
ArenaPlayerMatchCalInfo = [[common]],
ArenaRound = [[ds_common]],
ArenaRoundFlow = [[tglog]],
ArenaSettlementData = [[ds_common]],
ArenaTeam = [[ds_common]],
ArknightsExchangeInfo = [[cs_activity]],
ArknightsExchangePageInfo = [[cs_activity]],
ArknightsGameExploreRecord = [[cs_activity]],
ArknightsGameInfo = [[cs_activity]],
ArknightsGameMatchRecord = [[cs_activity]],
ArknightsGameNpcRecord = [[cs_activity]],
ArknightsGameReward = [[cs_activity]],
ArknightsGameSanRecord = [[cs_activity]],
ArknightsRecruitAdInfo = [[cs_activity]],
ArknightsRecruitBoardInfo = [[cs_activity]],
ArknightsRecruitCollectInfo = [[cs_activity]],
ArknightsRecruitHeroInfo = [[cs_activity]],
ArknightsRecruitHeroLevelInfo = [[cs_activity]],
ArknightsRecruitInfo = [[cs_activity]],
ArknightsRecruitMPMoneyInfo = [[cs_activity]],
ArmedForceNumeralData = [[ds_common]],
ArmedForceOutfitUpdate = [[ds_tglog]],
ArmedForceTalent = [[ds_common]],
ArmedforceDataFlow = [[ds_tglog]],
ArmedforceDataFlowAssault = [[ds_tglog]],
ArmedforceDataFlowClaire = [[ds_tglog]],
ArmedforceDataFlowDavid = [[ds_tglog]],
ArmedforceDataFlowEngineer = [[ds_tglog]],
ArmedforceDataFlowHackclaw = [[ds_tglog]],
ArmedforceDataFlowOldDeng = [[ds_tglog]],
ArmedforceDataFlowScout = [[ds_tglog]],
ArmedforceDataFlowSineva = [[ds_tglog]],
ArmedforceDataFlowSupport = [[ds_tglog]],
ArmedforceDataFlowWolfWarriors = [[ds_tglog]],
ArmedforceDataFlowWuMing = [[ds_tglog]],
ArmedforceDataFlowZoya = [[ds_tglog]],
ArmedforceProp = [[common]],
ArmoryFinishUnlockItemFlow = [[tglog]],
ArmoryQuestChangeFlow = [[tglog]],
AssembleInfo = [[common]],
AttendInfo = [[cs_activity]],
AttendState = [[cs_activity]],
AttrBuff = [[ds_common]],
AttrSkill = [[common]],
AttrValue = [[common]],
AttributeFlow = [[tglog]],
AuctionAutoBuyFlow = [[tglog]],
AuctionAutoFillFlow = [[tglog]],
AuctionAutoRackStrategyTriggerFlow = [[tglog]],
AuctionAutoRackTaskCompleteFlow = [[tglog]],
AuctionAutoRackTaskDetailFlow = [[tglog]],
AuctionAutoRackTaskListingFlow = [[tglog]],
AuctionAutoRackTaskPickUpFlow = [[tglog]],
AuctionAutoRackTaskPublishFlow = [[tglog]],
AuctionAveragePriceChangeFlow = [[tglog]],
AuctionBuyPropFlow = [[tglog]],
AuctionEnterCountFlow = [[tglog]],
AuctionErrorFlow = [[tglog]],
AuctionGMOrderOptFlow = [[tglog]],
AuctionGuidePriceChangeFlow = [[tglog]],
AuctionListingCacheSoldOutFlow = [[tglog]],
AuctionNotAllowUnderBuyFlow = [[tglog]],
AuctionPlayerBuyFailedFlow = [[tglog]],
AuctionPlayerOrderFlow = [[tglog]],
AuctionPlayerQuerySaleListFlow = [[tglog]],
AuctionPlayerSearchPropFlow = [[tglog]],
AuctionPlayerSellFailedFlow = [[tglog]],
AuctionPropDepositChangeFlow = [[tglog]],
AuctionPropStatFlow = [[tglog]],
AuctionPulloffPropFlow = [[tglog]],
AuctionPurchaseBatchFlow = [[tglog]],
AuctionSaleListingCacheFlow = [[tglog]],
AuctionSellPropFlow = [[tglog]],
AuctionSoldFlow = [[tglog]],
AuctionTaxConfig = [[cs_auction]],
AuctionWithdrawFlow = [[tglog]],
AuthInfo = [[cs_hope]],
AutoPurchaseSkuFlow = [[tglog]],
AvatarsShowTimeInfo = [[cs_collection]],
BankruptcyFlow = [[tglog]],
Banner = [[cs_activity]],
BannerConfig = [[cs_playerinfo]],
BatchItemInfo = [[cs_shop]],
BattlePassArchive = [[cs_battlepass]],
BattlePassClue = [[cs_battlepass]],
BattlePassExprFlow = [[tglog]],
BattlePassInfo = [[cs_battlepass]],
BattlePassLevelInfo = [[cs_battlepass]],
BattlePassLoginFlow = [[tglog]],
BattlePassMainLine = [[cs_battlepass]],
BattlePassPackInfo = [[cs_battlepass]],
BattlePassPurchaseFlow = [[tglog]],
BattlePassRefreshFlow = [[tglog]],
BattlePassRewardFlow = [[tglog]],
BattlePassSeasonInfo = [[cs_battlepass]],
BattlefieldWeaponScoreFlow = [[ds_tglog]],
BestIdcChosenPlayerResult = [[tglog]],
BestIdcChosenResult = [[tglog]],
BestIdcDsGate = [[tglog]],
BestIdcDsNetwork = [[tglog]],
BhdChapterInfo = [[cs_bhd]],
BhdMissionRecord = [[ds_common]],
BhdRewardInfo = [[cs_bhd]],
BhdSettlementData = [[ds_common]],
BhdSettlementInfo = [[tglog]],
BhdSpecData = [[common]],
BlueprintDescInfo = [[cs_collection]],
BlueprintNumeralFlow = [[tglog]],
BoardMatchInfo = [[cs_solo]],
BodyPosPropSnapshot = [[cs_deposit]],
BotHalfJoinAlloc = [[tglog]],
BotJoin = [[tglog]],
BreakthroughBigEventInfoFlow = [[ds_tglog]],
BuffStatusFlow = [[tglog]],
BulletFXMismatchFlow = [[ds_tglog]],
BulletOperateCmd = [[cs_deposit]],
ButtonClickFlow = [[tglog]],
BuyGoodsInfo = [[cs_shop]],
BuyHotRecommendationPropRecord = [[cs_shop]],
BuyLotteryItemInfo = [[cs_shop]],
BuyLotteryItemRecord = [[cs_shop]],
BuyMallGiftPropRecord = [[cs_shop]],
CSABTQueryReq = [[cs_abt]],
CSABTQueryRes = [[cs_abt]],
CSAccountAllowRealTimeVoiceReq = [[cs_account]],
CSAccountAllowRealTimeVoiceRes = [[cs_account]],
CSAccountAutoUnlockMPGunsReq = [[cs_account]],
CSAccountAutoUnlockMPGunsRes = [[cs_account]],
CSAccountBhdClientVersionUpdateReq = [[cs_account]],
CSAccountBhdClientVersionUpdateRes = [[cs_account]],
CSAccountExpChangeNtf = [[cs_account]],
CSAccountGetPlayerProfileReq = [[cs_account]],
CSAccountGetPlayerProfileRes = [[cs_account]],
CSAccountGetUnicodeConfReq = [[cs_account]],
CSAccountGetUnicodeConfRes = [[cs_account]],
CSAccountLogTgLogNtf = [[cs_account]],
CSAccountLoginReq = [[cs_account]],
CSAccountLoginRes = [[cs_account]],
CSAccountRandNickReq = [[cs_account]],
CSAccountRandNickRes = [[cs_account]],
CSAccountRealTimeVoiceNtf = [[cs_account]],
CSAccountRegisterReq = [[cs_account]],
CSAccountRegisterRes = [[cs_account]],
CSAccountSetCrossPlatPlayReq = [[cs_account]],
CSAccountSetCrossPlatPlayRes = [[cs_account]],
CSAccountUpdateAvatarReq = [[cs_account]],
CSAccountUpdateAvatarRes = [[cs_account]],
CSAccountUpdateGwalletInfoReq = [[cs_account]],
CSAccountUpdateGwalletInfoRes = [[cs_account]],
CSAccountUpdatePayTokenReq = [[cs_account]],
CSAccountUpdatePayTokenRes = [[cs_account]],
CSAccountUpdateProfileReq = [[cs_account]],
CSAccountUpdateProfileRes = [[cs_account]],
CSAccountUpdateRiskctlInfoReq = [[cs_account]],
CSAccountUpdateRiskctlInfoRes = [[cs_account]],
CSAccountValidateNickReq = [[cs_account]],
CSAccountValidateNickRes = [[cs_account]],
CSAceAntiDataTransferNtf = [[cscpp_security]],
CSAceSendAntiDataNtf = [[cscpp_security]],
CSAceSendLightFeatureDataNtf = [[cscpp_security]],
CSAchieveBatchQueryProgressReq = [[cs_achieve]],
CSAchieveBatchQueryProgressRes = [[cs_achieve]],
CSAchieveProgressUpdatedNtf = [[cs_achieve]],
CSAchieveQueryProgressReq = [[cs_achieve]],
CSAchieveQueryProgressRes = [[cs_achieve]],
CSAchieveQuerySteamProgressReq = [[cs_achieve]],
CSAchieveQuerySteamProgressRes = [[cs_achieve]],
CSAchieveUpdateSteamProgressNtf = [[cs_achieve]],
CSAchievementData = [[cs_achieve]],
CSAchievementGoal = [[cs_achieve]],
CSAchievementItem = [[cs_achieve]],
CSAchievementTask = [[cs_achieve]],
CSActivityAhsarahTravelLikeReq = [[cs_activity]],
CSActivityAhsarahTravelLikeRes = [[cs_activity]],
CSActivityAhsarahTravelReceiveLineRewardReq = [[cs_activity]],
CSActivityAhsarahTravelReceiveLineRewardRes = [[cs_activity]],
CSActivityAhsarahTravelReceiveReplyRewardReq = [[cs_activity]],
CSActivityAhsarahTravelReceiveReplyRewardRes = [[cs_activity]],
CSActivityAhsarahTravelReplyReq = [[cs_activity]],
CSActivityAhsarahTravelReplyRes = [[cs_activity]],
CSActivityAhsarahTravelTrackReq = [[cs_activity]],
CSActivityAhsarahTravelTrackRes = [[cs_activity]],
CSActivityAnimationUpdateReq = [[cs_activity]],
CSActivityAnimationUpdateRes = [[cs_activity]],
CSActivityAnswerReq = [[cs_activity]],
CSActivityAnswerRes = [[cs_activity]],
CSActivityArchiveAudioFinishReq = [[cs_activity]],
CSActivityArchiveAudioFinishRes = [[cs_activity]],
CSActivityArchiveFirstOpenReq = [[cs_activity]],
CSActivityArchiveFirstOpenRes = [[cs_activity]],
CSActivityArknightsChooseHeroReq = [[cs_activity]],
CSActivityArknightsChooseHeroRes = [[cs_activity]],
CSActivityArknightsGameEndReq = [[cs_activity]],
CSActivityArknightsGameEndRes = [[cs_activity]],
CSActivityArknightsGameExploreEndReq = [[cs_activity]],
CSActivityArknightsGameExploreEndRes = [[cs_activity]],
CSActivityArknightsGameExploreStartReq = [[cs_activity]],
CSActivityArknightsGameExploreStartRes = [[cs_activity]],
CSActivityArknightsGameReceiveCycleRewardReq = [[cs_activity]],
CSActivityArknightsGameReceiveCycleRewardRes = [[cs_activity]],
CSActivityArknightsGameStartReq = [[cs_activity]],
CSActivityArknightsGameStartRes = [[cs_activity]],
CSActivityArknightsReceiveCollectRewardReq = [[cs_activity]],
CSActivityArknightsReceiveCollectRewardRes = [[cs_activity]],
CSActivityArknightsRecruitReq = [[cs_activity]],
CSActivityArknightsRecruitRes = [[cs_activity]],
CSActivityArknightsRefreshAdReq = [[cs_activity]],
CSActivityArknightsRefreshAdRes = [[cs_activity]],
CSActivityArknightsUpdateMPMoneyNoticeReq = [[cs_activity]],
CSActivityArknightsUpdateMPMoneyNoticeRes = [[cs_activity]],
CSActivityAttendReq = [[cs_activity]],
CSActivityAttendRes = [[cs_activity]],
CSActivityChangeExchangePlanReq = [[cs_activity]],
CSActivityChangeExchangePlanRes = [[cs_activity]],
CSActivityChangeMilestoneAwardReq = [[cs_activity]],
CSActivityChangeMilestoneAwardRes = [[cs_activity]],
CSActivityCollectionSubmitPropsReq = [[cs_activity]],
CSActivityCollectionSubmitPropsRes = [[cs_activity]],
CSActivityEavesdroppingAnswerReq = [[cs_activity]],
CSActivityEavesdroppingAnswerRes = [[cs_activity]],
CSActivityEavesdroppingReceiveCaseAwardReq = [[cs_activity]],
CSActivityEavesdroppingReceiveCaseAwardRes = [[cs_activity]],
CSActivityEavesdroppingReceiveSpecialAnswerAwardReq = [[cs_activity]],
CSActivityEavesdroppingReceiveSpecialAnswerAwardRes = [[cs_activity]],
CSActivityExchange = [[cs_activity]],
CSActivityExchangeMilestoneAwardReq = [[cs_activity]],
CSActivityExchangeMilestoneAwardRes = [[cs_activity]],
CSActivityExchangePasswordReq = [[cs_activity]],
CSActivityExchangePasswordRes = [[cs_activity]],
CSActivityExchangePropsReq = [[cs_activity]],
CSActivityExchangePropsRes = [[cs_activity]],
CSActivityExchangeReq = [[cs_activity]],
CSActivityExchangeRes = [[cs_activity]],
CSActivityFocusExchangeReq = [[cs_activity]],
CSActivityFocusExchangeRes = [[cs_activity]],
CSActivityGetAcceptedActivityReq = [[cs_activity]],
CSActivityGetAcceptedActivityRes = [[cs_activity]],
CSActivityGetBannersReq = [[cs_activity]],
CSActivityGetBannersRes = [[cs_activity]],
CSActivityGetCurrencyReq = [[cs_activity]],
CSActivityGetCurrencyRes = [[cs_activity]],
CSActivityGetPropJumpReq = [[cs_activity]],
CSActivityGetPropJumpRes = [[cs_activity]],
CSActivityGetReq = [[cs_activity]],
CSActivityGetRes = [[cs_activity]],
CSActivityInstallPropReq = [[cs_activity]],
CSActivityInstallPropRes = [[cs_activity]],
CSActivityMakeDrinkReq = [[cs_activity]],
CSActivityMakeDrinkRes = [[cs_activity]],
CSActivityMilestone = [[cs_activity]],
CSActivityMossReceiveMSAwardReq = [[cs_activity]],
CSActivityMossReceiveMSAwardRes = [[cs_activity]],
CSActivityOneClickRewardClaimReq = [[cs_activity]],
CSActivityOneClickRewardClaimRes = [[cs_activity]],
CSActivityPropExchangeReq = [[cs_activity]],
CSActivityPropExchangeRes = [[cs_activity]],
CSActivityReceiveFinalAwardReq = [[cs_activity]],
CSActivityReceiveFinalAwardRes = [[cs_activity]],
CSActivityReceiveMilestoneAwardReq = [[cs_activity]],
CSActivityReceiveMilestoneAwardRes = [[cs_activity]],
CSActivityReceiveSequenceAwardReq = [[cs_activity]],
CSActivityReceiveSequenceAwardRes = [[cs_activity]],
CSActivityRecvAttendAwardReq = [[cs_activity]],
CSActivityRecvAttendAwardRes = [[cs_activity]],
CSActivityRefreshDailyActivityReq = [[cs_activity]],
CSActivityRefreshDailyActivityRes = [[cs_activity]],
CSActivityRelinkExchangeReq = [[cs_activity]],
CSActivityRelinkExchangeRes = [[cs_activity]],
CSActivityRelinkReceiveMSAwardReq = [[cs_activity]],
CSActivityRelinkReceiveMSAwardRes = [[cs_activity]],
CSActivityReturnGetReq = [[cs_activity]],
CSActivityReturnGetRes = [[cs_activity]],
CSActivitySBCAdjustReq = [[cs_activity]],
CSActivitySBCAdjustRes = [[cs_activity]],
CSActivitySBCChangeRewardReq = [[cs_activity]],
CSActivitySBCChangeRewardRes = [[cs_activity]],
CSActivitySOCCompositeReq = [[cs_activity]],
CSActivitySOCCompositeRes = [[cs_activity]],
CSActivitySOCDrawReq = [[cs_activity]],
CSActivitySOCDrawRes = [[cs_activity]],
CSActivitySOCExchangeReq = [[cs_activity]],
CSActivitySOCExchangeRes = [[cs_activity]],
CSActivitySOCGetRecvCardsReq = [[cs_activity]],
CSActivitySOCGetRecvCardsRes = [[cs_activity]],
CSActivitySOCGetShopReq = [[cs_activity]],
CSActivitySOCGetShopRes = [[cs_activity]],
CSActivitySOCMPFlowsReq = [[cs_activity]],
CSActivitySOCMPFlowsRes = [[cs_activity]],
CSActivitySOCNewCardNtf = [[cs_activity]],
CSActivitySOCNewMPPackNtf = [[cs_activity]],
CSActivitySOCSendCardReq = [[cs_activity]],
CSActivitySOCSendCardRes = [[cs_activity]],
CSActivitySOCSendRecvFlowsReq = [[cs_activity]],
CSActivitySOCSendRecvFlowsRes = [[cs_activity]],
CSActivityStarDecryptReq = [[cs_activity]],
CSActivityStarDecryptRes = [[cs_activity]],
CSActivityStarFireChargeTowerReq = [[cs_activity]],
CSActivityStarFireChargeTowerRes = [[cs_activity]],
CSActivityTask = [[cs_activity]],
CSActivityTaskAcceptReq = [[cs_activity]],
CSActivityTaskAcceptRes = [[cs_activity]],
CSActivityTaskReceiveAwardReq = [[cs_activity]],
CSActivityTaskReceiveAwardRes = [[cs_activity]],
CSActivityTrackReq = [[cs_activity]],
CSActivityTrackRes = [[cs_activity]],
CSActivityTriggerReq = [[cs_activity]],
CSActivityTriggerRes = [[cs_activity]],
CSActivityUninstallPropReq = [[cs_activity]],
CSActivityUninstallPropRes = [[cs_activity]],
CSActivityUnlockExchangeReq = [[cs_activity]],
CSActivityUnlockExchangeRes = [[cs_activity]],
CSActivityUntrackReq = [[cs_activity]],
CSActivityUntrackRes = [[cs_activity]],
CSActivityUpdateProgressNtf = [[cs_activity]],
CSArmedForceCancelEquipmentRentalReq = [[cs_armedforce]],
CSArmedForceCancelEquipmentRentalRes = [[cs_armedforce]],
CSArmedForceClearStateNtf = [[cs_armedforce]],
CSArmedForceEquipmentRentalReq = [[cs_armedforce]],
CSArmedForceEquipmentRentalRes = [[cs_armedforce]],
CSArmedForceGetRentalReq = [[cs_armedforce]],
CSArmedForceGetRentalRes = [[cs_armedforce]],
CSArmedForceGetSOLOutfitSettingReq = [[cs_armedforce]],
CSArmedForceGetSOLOutfitSettingRes = [[cs_armedforce]],
CSArmedForceLoadLastEquipmentReq = [[cs_armedforce]],
CSArmedForceLoadLastEquipmentRes = [[cs_armedforce]],
CSArmedForceRefreshRentalReq = [[cs_armedforce]],
CSArmedForceRefreshRentalRes = [[cs_armedforce]],
CSArmedForceReportOutfitReq = [[cs_armedforce]],
CSArmedForceReportOutfitRes = [[cs_armedforce]],
CSArmedForceSetOutfitCheckItemsReq = [[cs_armedforce]],
CSArmedForceSetOutfitCheckItemsRes = [[cs_armedforce]],
CSArmedForceSetStyleInfoReq = [[cs_armedforce]],
CSArmedForceSetStyleInfoRes = [[cs_armedforce]],
CSArmedForceUpdateLastEquipmentReq = [[cs_armedforce]],
CSArmedForceUpdateLastEquipmentRes = [[cs_armedforce]],
CSArmedforceHeroRecord = [[cs_hero]],
CSArmedforceLoadOutfitReq = [[cs_armedforce]],
CSArmedforceLoadOutfitRes = [[cs_armedforce]],
CSArmedforceSaveOutfitNameReq = [[cs_armedforce]],
CSArmedforceSaveOutfitNameRes = [[cs_armedforce]],
CSArmedforceUpdateOutfitReq = [[cs_armedforce]],
CSArmedforceUpdateOutfitRes = [[cs_armedforce]],
CSArmoryGetPlayerDataReq = [[cs_quest]],
CSArmoryGetPlayerDataRes = [[cs_quest]],
CSArmoryReddotEliminateNtf = [[cs_quest]],
CSAttrGetAllQualityReq = [[cs_attribute]],
CSAttrGetAllQualityRes = [[cs_attribute]],
CSAttrGetPlayerInfoReq = [[cs_attribute]],
CSAttrGetPlayerInfoRes = [[cs_attribute]],
CSAttrQualityValueChangeNtf = [[cs_attribute]],
CSAttrQuickHealReq = [[cs_attribute]],
CSAttrQuickHealRes = [[cs_attribute]],
CSAttributeGetConfigReq = [[cs_attribute]],
CSAttributeGetConfigRes = [[cs_attribute]],
CSAuctionAutoLoadGuidePriceReq = [[cs_auctionauto]],
CSAuctionAutoLoadGuidePriceRes = [[cs_auctionauto]],
CSAuctionBatchBuyReq = [[cs_auction]],
CSAuctionBatchBuyReqInfo = [[cs_auction]],
CSAuctionBatchBuyRes = [[cs_auction]],
CSAuctionBuyLocation = [[cs_auction]],
CSAuctionBuyTReq = [[cs_auction]],
CSAuctionBuyTRes = [[cs_auction]],
CSAuctionFeeArgs = [[cs_auction]],
CSAuctionGMNtf = [[cs_auction]],
CSAuctionGetGameItemSellPriceReq = [[cs_auction]],
CSAuctionGetGameItemSellPriceRes = [[cs_auction]],
CSAuctionGetPlayerInfoReq = [[cs_auction]],
CSAuctionGetPlayerInfoRes = [[cs_auction]],
CSAuctionGetSaleListBatchReq = [[cs_auction]],
CSAuctionGetSaleListBatchRes = [[cs_auction]],
CSAuctionGetSaleListInfo = [[cs_auction]],
CSAuctionGetSaleListReq = [[cs_auction]],
CSAuctionGetSaleListRes = [[cs_auction]],
CSAuctionGetTypeListReq = [[cs_auction]],
CSAuctionGetTypeListRes = [[cs_auction]],
CSAuctionOrder = [[cs_auction]],
CSAuctionOrderChangeNtf = [[cs_auction]],
CSAuctionProps = [[cs_auction]],
CSAuctionPullOffReq = [[cs_auction]],
CSAuctionPullOffRes = [[cs_auction]],
CSAuctionQueryRackSlotsInfoReq = [[cs_auction]],
CSAuctionQueryRackSlotsInfoRes = [[cs_auction]],
CSAuctionRackSlotInfo = [[cs_auction]],
CSAuctionRenewReq = [[cs_auction]],
CSAuctionRenewRes = [[cs_auction]],
CSAuctionSaleListInfo = [[cs_auction]],
CSAuctionSellReq = [[cs_auction]],
CSAuctionSellRes = [[cs_auction]],
CSAuctionStagedTaxFee = [[cs_auction]],
CSAuctionWithdrawReq = [[cs_auction]],
CSAuctionWithdrawRes = [[cs_auction]],
CSBattlePassBpCountryPriceReq = [[cs_battlepass]],
CSBattlePassBpCountryPriceRes = [[cs_battlepass]],
CSBattlePassBpGetSeasonIdReq = [[cs_battlepass]],
CSBattlePassBpGetSeasonIdRes = [[cs_battlepass]],
CSBattlePassBuyClueReq = [[cs_battlepass]],
CSBattlePassBuyClueRes = [[cs_battlepass]],
CSBattlePassBuyLevelReq = [[cs_battlepass]],
CSBattlePassBuyLevelRes = [[cs_battlepass]],
CSBattlePassBuyPackReq = [[cs_battlepass]],
CSBattlePassBuyPackRes = [[cs_battlepass]],
CSBattlePassBuyReq = [[cs_battlepass]],
CSBattlePassBuyRes = [[cs_battlepass]],
CSBattlePassGetInfoReq = [[cs_battlepass]],
CSBattlePassGetInfoRes = [[cs_battlepass]],
CSBattlePassLevelUpNtf = [[cs_battlepass]],
CSBattlePassReceiveArchiveRewardReq = [[cs_battlepass]],
CSBattlePassReceiveArchiveRewardRes = [[cs_battlepass]],
CSBattlePassReceiveClueRewardReq = [[cs_battlepass]],
CSBattlePassReceiveClueRewardRes = [[cs_battlepass]],
CSBattlePassUseExprCardReq = [[cs_battlepass]],
CSBattlePassUseExprCardRes = [[cs_battlepass]],
CSBattlePassUseUnlockCardReq = [[cs_battlepass]],
CSBattlePassUseUnlockCardRes = [[cs_battlepass]],
CSBhdCancelQuickJoinReq = [[cs_bhd]],
CSBhdCancelQuickJoinRes = [[cs_bhd]],
CSBhdCancelRedDotReq = [[cs_bhd]],
CSBhdCancelRedDotRes = [[cs_bhd]],
CSBhdGetChapterListReq = [[cs_bhd]],
CSBhdGetChapterListRes = [[cs_bhd]],
CSBhdGetLastSettInfoReq = [[cs_bhd]],
CSBhdGetLastSettInfoRes = [[cs_bhd]],
CSBhdGetRewardInfoReq = [[cs_bhd]],
CSBhdGetRewardInfoRes = [[cs_bhd]],
CSBhdGetTimeCostWRReq = [[cs_bhd]],
CSBhdGetTimeCostWRRes = [[cs_bhd]],
CSBhdNextLevelReadyReq = [[cs_bhd]],
CSBhdNextLevelReadyRes = [[cs_bhd]],
CSBhdQuickJoinReq = [[cs_bhd]],
CSBhdQuickJoinRes = [[cs_bhd]],
CSBhdSingleBeginMatchReq = [[cs_bhd]],
CSBhdSingleBeginMatchRes = [[cs_bhd]],
CSBhdTeamBeginMatchTReq = [[cs_team]],
CSBhdTeamBeginMatchTRes = [[cs_team]],
CSBhdTeamInfoUpdateReq = [[cs_bhd]],
CSBhdTeamInfoUpdateRes = [[cs_bhd]],
CSBlueprintSendBackNtf = [[cs_settlement]],
CSBuyGoodsReq = [[cs_pay]],
CSBuyGoodsRes = [[cs_pay]],
CSChatAppointmentReq = [[cs_chat]],
CSChatAppointmentRes = [[cs_chat]],
CSChatAppointmentRespondReq = [[cs_chat]],
CSChatAppointmentRespondRes = [[cs_chat]],
CSChatDSChatNtf = [[cs_chat]],
CSChatDSChatTReq = [[cs_chat]],
CSChatDSChatTRes = [[cs_chat]],
CSChatDSMarkNtf = [[cs_chat]],
CSChatDSMarkTReq = [[cs_chat]],
CSChatDSMarkTRes = [[cs_chat]],
CSChatForbidInfo = [[cs_chat]],
CSChatGetMsgSumyAllChannelReq = [[cs_chat]],
CSChatGetMsgSumyAllChannelRes = [[cs_chat]],
CSChatGetStangerInfoReq = [[cs_chat]],
CSChatGetStangerInfoRes = [[cs_chat]],
CSChatMsgNtf = [[cs_chat]],
CSChatPrivateLoadReq = [[cs_chat]],
CSChatPrivateLoadRes = [[cs_chat]],
CSChatPrivateReadStatUpdateReq = [[cs_chat]],
CSChatPrivateReadStatUpdateRes = [[cs_chat]],
CSChatPrivateSendReq = [[cs_chat]],
CSChatPrivateSendRes = [[cs_chat]],
CSChatRemoveSpeechNtf = [[cs_chat]],
CSChatStangerInfo = [[cs_chat]],
CSChatStrangerChatSwitchReq = [[cs_chat]],
CSChatStrangerChatSwitchRes = [[cs_chat]],
CSChatTeamLoadTReq = [[cs_chat]],
CSChatTeamLoadTRes = [[cs_chat]],
CSChatTeamReadStatUpdateTReq = [[cs_chat]],
CSChatTeamReadStatUpdateTRes = [[cs_chat]],
CSChatTeamSendTReq = [[cs_chat]],
CSChatTeamSendTRes = [[cs_chat]],
CSChatWorldLoadTReq = [[cs_chat]],
CSChatWorldLoadTRes = [[cs_chat]],
CSChatWorldSendTReq = [[cs_chat]],
CSChatWorldSendTRes = [[cs_chat]],
CSCheapBuyReq = [[cs_reconciliation]],
CSCheapBuyRes = [[cs_reconciliation]],
CSClientEnterHallModeReq = [[cs_playerinfo]],
CSClientEnterHallModeRes = [[cs_playerinfo]],
CSClientGameCenterSelectedNtf = [[cs_playerinfo]],
CSClientInfo = [[common]],
CSClientShareReportNtf = [[cs_playerinfo]],
CSCoPlayerFriendListReq = [[cs_friend]],
CSCoPlayerFriendListRes = [[cs_friend]],
CSCollectionAutoDistributionReq = [[cs_collection]],
CSCollectionAutoDistributionRes = [[cs_collection]],
CSCollectionBattleConsumeChangeNtf = [[cs_collection]],
CSCollectionChangeActivatedGunSkinPatternReq = [[cs_collection]],
CSCollectionChangeActivatedGunSkinPatternRes = [[cs_collection]],
CSCollectionGunSkinRewardsReq = [[cs_collection]],
CSCollectionGunSkinRewardsRes = [[cs_collection]],
CSCollectionLoadGunSkinRewardsStatusReq = [[cs_collection]],
CSCollectionLoadGunSkinRewardsStatusRes = [[cs_collection]],
CSCollectionLoadMysticalPendantPropsReq = [[cs_collection]],
CSCollectionLoadMysticalPendantPropsRes = [[cs_collection]],
CSCollectionLoadMysticalSkinPropsReq = [[cs_collection]],
CSCollectionLoadMysticalSkinPropsRes = [[cs_collection]],
CSCollectionLoadPropsReq = [[cs_collection]],
CSCollectionLoadPropsRes = [[cs_collection]],
CSCollectionMysticalPendantActiveReq = [[cs_collection]],
CSCollectionMysticalPendantActiveRes = [[cs_collection]],
CSCollectionMysticalPendantCombineReq = [[cs_collection]],
CSCollectionMysticalPendantCombineRes = [[cs_collection]],
CSCollectionMysticalPendantRenameReq = [[cs_collection]],
CSCollectionMysticalPendantRenameRes = [[cs_collection]],
CSCollectionMysticalPendantSuitInfoReq = [[cs_collection]],
CSCollectionMysticalPendantSuitInfoRes = [[cs_collection]],
CSCollectionMysticalPendantSuitNtf = [[cs_collection]],
CSCollectionMysticalSkinCombineReq = [[cs_collection]],
CSCollectionMysticalSkinCombineRes = [[cs_collection]],
CSCollectionMysticalSkinRenameReq = [[cs_collection]],
CSCollectionMysticalSkinRenameRes = [[cs_collection]],
CSCollectionPropChangeNtf = [[cs_collection]],
CSCollectionPropExpireTimeRecord = [[cs_collection]],
CSCollectionPropExpireTimeRecordItem = [[cs_collection]],
CSCollectionRandSkin = [[cs_collection]],
CSCollectionRandSkinPoolChangeNtf = [[cs_collection]],
CSCollectionRandSkinSwitchReq = [[cs_collection]],
CSCollectionRandSkinSwitchRes = [[cs_collection]],
CSCollectionRecvBlueprintReq = [[cs_collection]],
CSCollectionRecvBlueprintRes = [[cs_collection]],
CSCollectionSafeboxSkinEquipReq = [[cs_collection]],
CSCollectionSafeboxSkinEquipRes = [[cs_collection]],
CSCollectionTrySyncMeleeSkinReq = [[cs_collection]],
CSCollectionUnlockAvatarsReq = [[cs_collection]],
CSCollectionUnlockAvatarsRes = [[cs_collection]],
CSCollectionUnlockGunSkinRewardsReq = [[cs_collection]],
CSCollectionUnlockGunSkinRewardsRes = [[cs_collection]],
CSCollectionUnlockMasterGunSkinReq = [[cs_collection]],
CSCollectionUnlockMasterGunSkinRes = [[cs_collection]],
CSCollectionUpdateRandSkinPoolReq = [[cs_collection]],
CSCollectionUpdateRandSkinPoolRes = [[cs_collection]],
CSCollectionUpdateRedPointReq = [[cs_collection]],
CSCollectionUpdateRedPointRes = [[cs_collection]],
CSCollectionUsePropReq = [[cs_collection]],
CSCollectionUsePropRes = [[cs_collection]],
CSCollectionWeaponSkinUnApplyAllReq = [[cs_collection]],
CSCollectionWeaponSkinUnApplyAllRes = [[cs_collection]],
CSCurrencyChangeNtf = [[cs_currency]],
CSCurrencyExchangeReq = [[cs_currency]],
CSCurrencyExchangeRes = [[cs_currency]],
CSCustomOutfit = [[cs_armedforce]],
CSDebugResultReportReq = [[cs_debug]],
CSDebugResultReportRes = [[cs_debug]],
CSDebugRunCmdNtf = [[cs_debug]],
CSDepositActiveRecoveryAmountReq = [[cs_deposit]],
CSDepositActiveRecoveryAmountRes = [[cs_deposit]],
CSDepositAssemblySyncBodyContainerReq = [[cs_deposit]],
CSDepositAssemblySyncBodyContainerRes = [[cs_deposit]],
CSDepositBuySafeAndCardPackPermReq = [[cs_deposit]],
CSDepositBuySafeAndCardPackPermRes = [[cs_deposit]],
CSDepositChangeNtf = [[cs_deposit]],
CSDepositClearCarryOutTempPropsReq = [[cs_deposit]],
CSDepositClearCarryOutTempPropsRes = [[cs_deposit]],
CSDepositEquipPropReq = [[cs_deposit]],
CSDepositEquipPropRes = [[cs_deposit]],
CSDepositGetExtensionPropsReq = [[cs_deposit]],
CSDepositGetExtensionPropsRes = [[cs_deposit]],
CSDepositGetOutfitValueReq = [[cs_deposit]],
CSDepositGetOutfitValueRes = [[cs_deposit]],
CSDepositGetPlayerShowReq = [[cs_deposit]],
CSDepositGetPlayerShowRes = [[cs_deposit]],
CSDepositGetPropsReq = [[cs_deposit]],
CSDepositGetPropsRes = [[cs_deposit]],
CSDepositGetRecoveryAmountReq = [[cs_deposit]],
CSDepositGetRecoveryAmountRes = [[cs_deposit]],
CSDepositGetShowCabinetReq = [[cs_deposit]],
CSDepositGetShowCabinetRes = [[cs_deposit]],
CSDepositGetShowRoomReq = [[cs_deposit]],
CSDepositGetShowRoomRes = [[cs_deposit]],
CSDepositLevelUpShowCabinetReq = [[cs_deposit]],
CSDepositLevelUpShowCabinetRes = [[cs_deposit]],
CSDepositMoveShowCabinetReq = [[cs_deposit]],
CSDepositMoveShowCabinetRes = [[cs_deposit]],
CSDepositOperateBulletReq = [[cs_deposit]],
CSDepositOperateBulletRes = [[cs_deposit]],
CSDepositOperateExtensionBoxReq = [[cs_deposit]],
CSDepositOperateExtensionBoxRes = [[cs_deposit]],
CSDepositPositionModifyReq = [[cs_deposit]],
CSDepositPositionModifyRes = [[cs_deposit]],
CSDepositQueryShowRoomReq = [[cs_deposit]],
CSDepositQueryShowRoomRes = [[cs_deposit]],
CSDepositRepairReq = [[cs_deposit]],
CSDepositRepairRes = [[cs_deposit]],
CSDepositSendBackTeammatePropsReq = [[cs_deposit]],
CSDepositSendBackTeammatePropsRes = [[cs_deposit]],
CSDepositSetCommonConfigReq = [[cs_deposit]],
CSDepositSetCommonConfigRes = [[cs_deposit]],
CSDepositSetSortConfigReq = [[cs_deposit]],
CSDepositSetSortConfigRes = [[cs_deposit]],
CSDepositShelveShowCabinetReq = [[cs_deposit]],
CSDepositShelveShowCabinetRes = [[cs_deposit]],
CSDepositShowGridPropInfo = [[cs_deposit]],
CSDepositSortMultiplePosReq = [[cs_deposit]],
CSDepositSortMultiplePosRes = [[cs_deposit]],
CSDepositSortPositionReq = [[cs_deposit]],
CSDepositSortPositionRes = [[cs_deposit]],
CSDepositSubmitShowCabinetCostItemsReq = [[cs_deposit]],
CSDepositSubmitShowCabinetCostItemsRes = [[cs_deposit]],
CSDepositSwapDIYCabinetReq = [[cs_deposit]],
CSDepositSwapDIYCabinetRes = [[cs_deposit]],
CSDepositSwitchReq = [[cs_deposit]],
CSDepositSwitchRes = [[cs_deposit]],
CSDepositTestReplaceExtensionBoxReq = [[cs_deposit]],
CSDepositTestReplaceExtensionBoxRes = [[cs_deposit]],
CSDepositUpgradeExtensionBoxReq = [[cs_deposit]],
CSDepositUpgradeExtensionBoxRes = [[cs_deposit]],
CSDepositUsePropReq = [[cs_deposit]],
CSDepositUsePropRes = [[cs_deposit]],
CSDepositWithdrawShowCabinetReq = [[cs_deposit]],
CSDepositWithdrawShowCabinetRes = [[cs_deposit]],
CSDirectPurchaseShippingNtf = [[cs_directpurchase]],
CSEmptySettlementNtf = [[zone2dsa]],
CSFontRenderReq = [[cs_fontrender]],
CSFontRenderRes = [[cs_fontrender]],
CSFriendApplyAddReq = [[cs_friend]],
CSFriendApplyAddRes = [[cs_friend]],
CSFriendArkTeamReq = [[cs_friend]],
CSFriendArkTeamRes = [[cs_friend]],
CSFriendBlockPlayerReq = [[cs_friend]],
CSFriendBlockPlayerRes = [[cs_friend]],
CSFriendChangeNtf = [[cs_friend]],
CSFriendClientArkTeamReq = [[cs_friend]],
CSFriendClientArkTeamRes = [[cs_friend]],
CSFriendDelReq = [[cs_friend]],
CSFriendDelRes = [[cs_friend]],
CSFriendFavChangeNtf = [[cs_friend]],
CSFriendGameEndsRecommendReq = [[cs_friend]],
CSFriendGameEndsRecommendRes = [[cs_friend]],
CSFriendGetApplyListReq = [[cs_friend]],
CSFriendGetApplyListRes = [[cs_friend]],
CSFriendGetBlackListReq = [[cs_friend]],
CSFriendGetBlackListRes = [[cs_friend]],
CSFriendGetDiscordMateCacheReq = [[cs_friend]],
CSFriendGetDiscordMateCacheRes = [[cs_friend]],
CSFriendGetFriendCardReq = [[cs_friend]],
CSFriendGetFriendCardRes = [[cs_friend]],
CSFriendGetOnlineNumberReq = [[cs_friend]],
CSFriendGetOnlineNumberRes = [[cs_friend]],
CSFriendListReq = [[cs_friend]],
CSFriendListRes = [[cs_friend]],
CSFriendOnlinePlayerListReq = [[cs_friend]],
CSFriendOnlinePlayerListRes = [[cs_friend]],
CSFriendPraiseDailyReq = [[cs_friend]],
CSFriendPraiseDailyRes = [[cs_friend]],
CSFriendRecommendReq = [[cs_friend]],
CSFriendRecommendRes = [[cs_friend]],
CSFriendRemarksModifyReq = [[cs_friend]],
CSFriendRemarksModifyRes = [[cs_friend]],
CSFriendRemoveBlackListReq = [[cs_friend]],
CSFriendRemoveBlackListRes = [[cs_friend]],
CSFriendResponseAddReq = [[cs_friend]],
CSFriendResponseAddRes = [[cs_friend]],
CSFriendResponseAllAddReq = [[cs_friend]],
CSFriendResponseAllAddRes = [[cs_friend]],
CSFriendSearchReq = [[cs_friend]],
CSFriendSearchRes = [[cs_friend]],
CSFriendSpeechJudgmentReq = [[cs_friend]],
CSFriendSpeechJudgmentRes = [[cs_friend]],
CSFriendUpdateFriendCardReq = [[cs_friend]],
CSFriendUpdateFriendCardRes = [[cs_friend]],
CSFriendUploadDiscordMateListReq = [[cs_friend]],
CSFriendUploadDiscordMateListRes = [[cs_friend]],
CSFriendVoiceDurationReportReq = [[cs_friend]],
CSFriendVoiceDurationReportRes = [[cs_friend]],
CSGMNotifyNtf = [[cs_gm]],
CSGMReq = [[cs_gm]],
CSGMRes = [[cs_gm]],
CSGatewayKickPlayerNtf = [[cs_gateway]],
CSGeneralSkillGetInfoReq = [[cs_generalskill]],
CSGeneralSkillGetInfoRes = [[cs_generalskill]],
CSGetArenaSettlementInfoReq = [[cs_settlement]],
CSGetArenaSettlementInfoRes = [[cs_settlement]],
CSGetBoxInfoReq = [[cs_lottery]],
CSGetBoxInfoRes = [[cs_lottery]],
CSGetCurrencyReq = [[cs_currency]],
CSGetCurrencyRes = [[cs_currency]],
CSGetExchangeInfoReq = [[cs_currency]],
CSGetExchangeInfoRes = [[cs_currency]],
CSGetMatchAreaRttReq = [[cs_idcchosen]],
CSGetMatchAreaRttRes = [[cs_idcchosen]],
CSGetNetbarPrivNtf = [[cs_account]],
CSGetPlayerPrivacyDataReq = [[cs_playerinfo]],
CSGetPlayerPrivacyDataRes = [[cs_playerinfo]],
CSGetPlayerStructTriggerGuideDataReq = [[cs_guide]],
CSGetPlayerStructTriggerGuideDataRes = [[cs_guide]],
CSGetRaidEvaluatePrizeInfoReq = [[cs_settlement]],
CSGetRaidEvaluatePrizeInfoRes = [[cs_settlement]],
CSGetRaidSettlementInfoReq = [[cs_settlement]],
CSGetRaidSettlementInfoRes = [[cs_settlement]],
CSGetRechargeConfigReq = [[cs_pay]],
CSGetRechargeConfigRes = [[cs_pay]],
CSGetReputationHistoryReq = [[cs_playerinfo]],
CSGetReputationHistoryRes = [[cs_playerinfo]],
CSGetReputationInfoReq = [[cs_playerinfo]],
CSGetReputationInfoRes = [[cs_playerinfo]],
CSGetReputationRankAwardReq = [[cs_playerinfo]],
CSGetReputationRankAwardRes = [[cs_playerinfo]],
CSGetRoleQualityConfigReq = [[cs_attribute]],
CSGetRoleQualityConfigRes = [[cs_attribute]],
CSGetStuPrivNtf = [[cs_account]],
CSGetTDMSettlementInfoReq = [[cs_settlement]],
CSGetTDMSettlementInfoRes = [[cs_settlement]],
CSGuideBeginMatchReq = [[cs_guide]],
CSGuideBeginMatchRes = [[cs_guide]],
CSGuideGetDataReq = [[cs_guide]],
CSGuideGetDataRes = [[cs_guide]],
CSGuideGetMatchCountReq = [[cs_guide]],
CSGuideGetMatchCountRes = [[cs_guide]],
CSGuideGetPlayerGuideReq = [[cs_guide]],
CSGuideGetPlayerGuideRes = [[cs_guide]],
CSGuidePassedReq = [[cs_guide]],
CSGuidePassedRes = [[cs_guide]],
CSGuideResetPrepareReq = [[cs_guide]],
CSGuideResetPrepareRes = [[cs_guide]],
CSGuideSetDataReq = [[cs_guide]],
CSGuideSetDataRes = [[cs_guide]],
CSGuideSkipReq = [[cs_guide]],
CSGuideSkipRes = [[cs_guide]],
CSGuideSkipStageNtf = [[cs_guide]],
CSGuildPassedNtf = [[cs_guide]],
CSHero = [[common]],
CSHeroAccessory = [[common]],
CSHeroArmedPropFashionLotteryReq = [[cs_hero]],
CSHeroArmedPropFashionLotteryRes = [[cs_hero]],
CSHeroArmedPropFashionRollbackReq = [[cs_hero]],
CSHeroArmedPropFashionRollbackRes = [[cs_hero]],
CSHeroArmedPropFashionStoreReq = [[cs_hero]],
CSHeroArmedPropFashionStoreRes = [[cs_hero]],
CSHeroArmedPropInfo = [[common]],
CSHeroArmedPropStore = [[common]],
CSHeroBuyCurrencyInfo = [[cs_hero]],
CSHeroBuyPropFashionLotteryItemReq = [[cs_hero]],
CSHeroBuyPropFashionLotteryItemRes = [[cs_hero]],
CSHeroChallengeData = [[common]],
CSHeroChallengeInfo = [[cs_hero]],
CSHeroChangeAccessoryReadStatReq = [[cs_hero]],
CSHeroChangeAccessoryReadStatRes = [[cs_hero]],
CSHeroChangeChallengeNtf = [[cs_hero]],
CSHeroChangeChallengeReq = [[cs_hero]],
CSHeroChangeChallengeRes = [[cs_hero]],
CSHeroChangeFashionPriorReq = [[cs_hero]],
CSHeroChangeFashionPriorRes = [[cs_hero]],
CSHeroEquipFashionReq = [[cs_hero]],
CSHeroEquipFashionRes = [[cs_hero]],
CSHeroGetBadgeShowReq = [[cs_hero]],
CSHeroGetBadgeShowRes = [[cs_hero]],
CSHeroGetSelectedHeroReq = [[cs_hero]],
CSHeroGetSelectedHeroRes = [[cs_hero]],
CSHeroGetUnlockInfoReq = [[cs_hero]],
CSHeroGetUnlockInfoRes = [[cs_hero]],
CSHeroGrowLine = [[common]],
CSHeroGrowLineArchive = [[common]],
CSHeroGrowLineData = [[common]],
CSHeroGrowLineGetRewardsReq = [[cs_hero]],
CSHeroGrowLineGetRewardsRes = [[cs_hero]],
CSHeroGrowLineGoal = [[common]],
CSHeroGrowLineGoalsNtf = [[cs_hero]],
CSHeroGrowLineReward = [[cs_hero]],
CSHeroGrowLineRewardViewReq = [[cs_hero]],
CSHeroGrowLineRewardViewRes = [[cs_hero]],
CSHeroGrowLineUpdateRedPointReq = [[cs_hero]],
CSHeroGrowLineUpdateRedPointRes = [[cs_hero]],
CSHeroLimitedTimeRange = [[cs_hero]],
CSHeroLoadHeroListReq = [[cs_hero]],
CSHeroLoadHeroListRes = [[cs_hero]],
CSHeroLoadLvlUpgradeNtf = [[cs_hero]],
CSHeroReddotReq = [[cs_hero]],
CSHeroReddotRes = [[cs_hero]],
CSHeroSelectAccessoryReq = [[cs_hero]],
CSHeroSelectAccessoryRes = [[cs_hero]],
CSHeroSelectArmedPropReq = [[cs_hero]],
CSHeroSelectArmedPropRes = [[cs_hero]],
CSHeroSelectHeroReq = [[cs_hero]],
CSHeroSelectHeroRes = [[cs_hero]],
CSHeroSetBadgeShowReq = [[cs_hero]],
CSHeroSetBadgeShowRes = [[cs_hero]],
CSHeroUnlockArmedPropFashionStoreReq = [[cs_hero]],
CSHeroUnlockArmedPropFashionStoreRes = [[cs_hero]],
CSHeroUnlockInfo = [[cs_hero]],
CSHeroUnlockNtf = [[cs_hero]],
CSHeroWithLoadLvl = [[cs_hero]],
CSHopeCheckParentControlReq = [[cs_hope]],
CSHopeCheckParentControlRes = [[cs_hope]],
CSHopeExecInstructionNtf = [[cs_hope]],
CSHopeReportExecuteBatchNtf = [[cs_hope]],
CSHopeVerifyCertificationReq = [[cs_hope]],
CSHopeVerifyCertificationRes = [[cs_hope]],
CSLeaveTDMSettlementNtf = [[cs_settlement]],
CSLeaveTDMSettlementReq = [[cs_settlement]],
CSLeaveTDMSettlementRes = [[cs_settlement]],
CSLiveGetConfigReq = [[cs_live]],
CSLiveGetConfigRes = [[cs_live]],
CSLobbyAdConfigReq = [[cs_solo]],
CSLobbyAdConfigRes = [[cs_solo]],
CSLobbyConnectLocalDsNtf = [[cs_account]],
CSLobbyHeartbeatReq = [[cs_state]],
CSLobbyHeartbeatRes = [[cs_state]],
CSLobbyLaunchLocalDsNtf = [[cs_account]],
CSLotteryBlindBoxDrawReq = [[cs_lottery]],
CSLotteryBlindBoxDrawRes = [[cs_lottery]],
CSLotteryPropDrawReq = [[cs_lottery]],
CSLotteryPropDrawRes = [[cs_lottery]],
CSMPBagChange = [[cs_mpdeposit]],
CSMPChangeVehicleSkinReq = [[cs_mpdeposit]],
CSMPChangeVehicleSkinRes = [[cs_mpdeposit]],
CSMPDataChange = [[cs_mpdeposit]],
CSMPDepositDataChangeNtf = [[cs_mpdeposit]],
CSMPDepositGetBagsReq = [[cs_mpdeposit]],
CSMPDepositGetBagsRes = [[cs_mpdeposit]],
CSMPDepositGetPlayerShowReq = [[cs_mpdeposit]],
CSMPDepositGetPlayerShowRes = [[cs_mpdeposit]],
CSMPDepositGetPresetsReq = [[cs_mpdeposit]],
CSMPDepositGetPresetsRes = [[cs_mpdeposit]],
CSMPDepositGetPropsReq = [[cs_mpdeposit]],
CSMPDepositGetPropsRes = [[cs_mpdeposit]],
CSMPDepositSaveBagNameReq = [[cs_mpdeposit]],
CSMPDepositSaveBagNameRes = [[cs_mpdeposit]],
CSMPDepositSetDefaultBagReq = [[cs_mpdeposit]],
CSMPDepositSetDefaultBagRes = [[cs_mpdeposit]],
CSMPDepositUpdateAllPresetReq = [[cs_mpdeposit]],
CSMPDepositUpdateAllPresetRes = [[cs_mpdeposit]],
CSMPDepositUpdateBagReq = [[cs_mpdeposit]],
CSMPDepositUpdateBagRes = [[cs_mpdeposit]],
CSMPDepositUpdatePresetReq = [[cs_mpdeposit]],
CSMPDepositUpdatePresetRes = [[cs_mpdeposit]],
CSMPDepositUsePropReq = [[cs_mpdeposit]],
CSMPDepositUsePropRes = [[cs_mpdeposit]],
CSMPGetAllStarWeaponReq = [[cs_mpdeposit]],
CSMPGetAllStarWeaponRes = [[cs_mpdeposit]],
CSMPGetCsvTablesReq = [[cs_mpdeposit]],
CSMPGetCsvTablesRes = [[cs_mpdeposit]],
CSMPGetVehicleDataReq = [[cs_mpdeposit]],
CSMPGetVehicleDataRes = [[cs_mpdeposit]],
CSMPModifyVehicleReq = [[cs_mpdeposit]],
CSMPModifyVehicleRes = [[cs_mpdeposit]],
CSMPPropChange = [[cs_mpdeposit]],
CSMPRadarData = [[cs_season]],
CSMPSeasonData = [[cs_season]],
CSMailAddSystemMailReq = [[cs_mail]],
CSMailAddSystemMailRes = [[cs_mail]],
CSMailAttachmentInfo = [[cs_mail]],
CSMailDeleteAllReq = [[cs_mail]],
CSMailDeleteAllRes = [[cs_mail]],
CSMailDeleteReq = [[cs_mail]],
CSMailDeleteRes = [[cs_mail]],
CSMailGetBaseInfoReq = [[cs_mail]],
CSMailGetBaseInfoRes = [[cs_mail]],
CSMailGetListReq = [[cs_mail]],
CSMailGetListRes = [[cs_mail]],
CSMailInfo = [[cs_mail]],
CSMailNewMailNtf = [[cs_mail]],
CSMailReadReq = [[cs_mail]],
CSMailReadRes = [[cs_mail]],
CSMailReceiveBatchReq = [[cs_mail]],
CSMailReceiveBatchRes = [[cs_mail]],
CSMailReceiveReq = [[cs_mail]],
CSMailReceiveRes = [[cs_mail]],
CSMallBatchBuyReq = [[cs_mall]],
CSMallBatchBuyRes = [[cs_mall]],
CSMallBuyPlayerDesignWeaponReq = [[cs_mall]],
CSMallBuyPlayerDesignWeaponRes = [[cs_mall]],
CSMallBuyReq = [[cs_mall]],
CSMallBuyRes = [[cs_mall]],
CSMallDelUnlockExchangeIdReq = [[cs_mall]],
CSMallDelUnlockExchangeIdRes = [[cs_mall]],
CSMallGetBuyGoodsReq = [[cs_mall]],
CSMallGetBuyGoodsRes = [[cs_mall]],
CSMallGetClickedExchangeIdReq = [[cs_mall]],
CSMallGetClickedExchangeIdRes = [[cs_mall]],
CSMallGetGoodsReq = [[cs_mall]],
CSMallGetGoodsRes = [[cs_mall]],
CSMallGetLabelNo1ConfigReq = [[cs_mall]],
CSMallGetLabelNo1ConfigRes = [[cs_mall]],
CSMallGetMerchantGiftReq = [[cs_mall]],
CSMallGetMerchantGiftRes = [[cs_mall]],
CSMallGetMerchantsReq = [[cs_mall]],
CSMallGetMerchantsRes = [[cs_mall]],
CSMallGetMysteryShopItemsReq = [[cs_mall]],
CSMallGetMysteryShopItemsRes = [[cs_mall]],
CSMallGetPanicBuyStateReq = [[cs_mall]],
CSMallGetPanicBuyStateRes = [[cs_mall]],
CSMallGetPlayerDailyLimitGoodsReq = [[cs_mall]],
CSMallGetPlayerDailyLimitGoodsRes = [[cs_mall]],
CSMallGetPricesReq = [[cs_mall]],
CSMallGetPricesRes = [[cs_mall]],
CSMallGetRecycleGoodsReq = [[cs_mall]],
CSMallGetRecycleGoodsRes = [[cs_mall]],
CSMallGetSubGoodsReq = [[cs_mall]],
CSMallGetSubGoodsRes = [[cs_mall]],
CSMallGetUnlockDepartmentReq = [[cs_mall]],
CSMallGetUnlockDepartmentRes = [[cs_mall]],
CSMallGetUnlockExchangeIdReq = [[cs_mall]],
CSMallGetUnlockExchangeIdRes = [[cs_mall]],
CSMallGiftReq = [[cs_mall]],
CSMallGiftRes = [[cs_mall]],
CSMallMerchantChangeNtf = [[cs_mall]],
CSMallMerchantGiftNtf = [[cs_mall]],
CSMallMysteryShopBuyReq = [[cs_mall]],
CSMallMysteryShopBuyRes = [[cs_mall]],
CSMallMysteryShopRaffleReq = [[cs_mall]],
CSMallMysteryShopRaffleRes = [[cs_mall]],
CSMallPanicBuyNtf = [[cs_mall]],
CSMallQueryPriceReq = [[cs_mall]],
CSMallQueryPriceRes = [[cs_mall]],
CSMallReceiveGiftReq = [[cs_mall]],
CSMallReceiveGiftRes = [[cs_mall]],
CSMallSellReq = [[cs_mall]],
CSMallSellRes = [[cs_mall]],
CSMapBoardEasterEggReq = [[cs_solo]],
CSMapBoardEasterEggRes = [[cs_solo]],
CSMarketBatchBuyReq = [[cs_market]],
CSMarketBatchBuyReqInfo = [[cs_market]],
CSMarketBatchBuyRes = [[cs_market]],
CSMarketBuyLocation = [[cs_market]],
CSMarketBuyTReq = [[cs_market]],
CSMarketBuyTRes = [[cs_market]],
CSMarketGMNtf = [[cs_market]],
CSMarketGetAndUpdatePreBuyOrderReq = [[cs_market]],
CSMarketGetAndUpdatePreBuyOrderRes = [[cs_market]],
CSMarketGetAndUpdateWatchListReq = [[cs_market]],
CSMarketGetAndUpdateWatchListRes = [[cs_market]],
CSMarketGetEventReq = [[cs_market]],
CSMarketGetEventRes = [[cs_market]],
CSMarketGetGameItemSellPriceReq = [[cs_market]],
CSMarketGetGameItemSellPriceRes = [[cs_market]],
CSMarketGetPlayerInfoReq = [[cs_market]],
CSMarketGetPlayerInfoRes = [[cs_market]],
CSMarketGetSaleHistoryReq = [[cs_market]],
CSMarketGetSaleHistoryRes = [[cs_market]],
CSMarketGetSaleListBatchReq = [[cs_market]],
CSMarketGetSaleListBatchRes = [[cs_market]],
CSMarketGetSaleListInfo = [[cs_market]],
CSMarketGetSaleListReq = [[cs_market]],
CSMarketGetSaleListRes = [[cs_market]],
CSMarketGetTypeListReq = [[cs_market]],
CSMarketGetTypeListRes = [[cs_market]],
CSMarketOrder = [[cs_market]],
CSMarketOrderChangeNtf = [[cs_market]],
CSMarketPreBuyNtf = [[cs_market]],
CSMarketPreBuyReq = [[cs_market]],
CSMarketPreBuyRes = [[cs_market]],
CSMarketProps = [[cs_market]],
CSMarketPullOffPreBuyReq = [[cs_market]],
CSMarketPullOffPreBuyRes = [[cs_market]],
CSMarketPullOffReq = [[cs_market]],
CSMarketPullOffRes = [[cs_market]],
CSMarketRackFee = [[cs_market]],
CSMarketRenewReq = [[cs_market]],
CSMarketRenewRes = [[cs_market]],
CSMarketSaleListInfo = [[cs_market]],
CSMarketSaleProp = [[cs_market]],
CSMarketSaleType = [[cs_market]],
CSMarketSellReq = [[cs_market]],
CSMarketSellRes = [[cs_market]],
CSMarketWatchReq = [[cs_market]],
CSMarketWatchRes = [[cs_market]],
CSMarketWithdrawReq = [[cs_market]],
CSMarketWithdrawRes = [[cs_market]],
CSMatchCheckTReq = [[cs_match]],
CSMatchCheckTRes = [[cs_match]],
CSMatchDataGetPlayerMatchAreaReq = [[cs_matchdata]],
CSMatchDataGetPlayerMatchAreaRes = [[cs_matchdata]],
CSMatchDataSetPlayerMatchAreaReq = [[cs_matchdata]],
CSMatchDataSetPlayerMatchAreaRes = [[cs_matchdata]],
CSMatchGateIsRankEnableReq = [[cs_matchgate]],
CSMatchGateIsRankEnableRes = [[cs_matchgate]],
CSMatchRoomCallNumeralNtf = [[cs_matchroom]],
CSMatchRoomCheckCallNumeralReq = [[cs_matchroom]],
CSMatchRoomCheckCallNumeralRes = [[cs_matchroom]],
CSMatchRoomGetPlayStationMatchIdTReq = [[cs_matchroom]],
CSMatchRoomGetPlayStationMatchIdTRes = [[cs_matchroom]],
CSMatchRoomGetSolRoomTeamTReq = [[cs_matchroom]],
CSMatchRoomGetSolRoomTeamTRes = [[cs_matchroom]],
CSMatchRoomGetTdmRoomArmedForceTReq = [[cs_matchroom]],
CSMatchRoomGetTdmRoomArmedForceTRes = [[cs_matchroom]],
CSMatchRoomMatchEndNtf = [[cs_matchroom]],
CSMatchRoomPickStageReconnectReq = [[cs_matchroom]],
CSMatchRoomPickStageReconnectRes = [[cs_matchroom]],
CSMatchRoomReconnectReq = [[cs_matchroom]],
CSMatchRoomReconnectRes = [[cs_matchroom]],
CSMatchRoomSetPlayStationMatchIdTReq = [[cs_matchroom]],
CSMatchRoomSetPlayStationMatchIdTRes = [[cs_matchroom]],
CSMatchRoomSetSolRoomHeroTReq = [[cs_matchroom]],
CSMatchRoomSetSolRoomHeroTRes = [[cs_matchroom]],
CSMatchRoomSetTdmRoomArmedForceTReq = [[cs_matchroom]],
CSMatchRoomSetTdmRoomArmedForceTRes = [[cs_matchroom]],
CSMatchRoomSolChangeHeroNtf = [[cs_matchroom]],
CSMatchRoomSolReadyNtf = [[cs_matchroom]],
CSMatchRoomSolReadyTReq = [[cs_matchroom]],
CSMatchRoomSolReadyTRes = [[cs_matchroom]],
CSMatchRoomStartMatchTglogTReq = [[cs_matchroom]],
CSMatchRoomStartMatchTglogTRes = [[cs_matchroom]],
CSMatchRoomSyncMemberOptionNtf = [[cs_matchroom]],
CSMatchRoomSyncMemberOptionReq = [[cs_matchroom]],
CSMatchRoomSyncMemberOptionRes = [[cs_matchroom]],
CSMatchRoomTdmArmedForceShowInfo = [[cs_matchroom]],
CSMatchRoomTdmChangeArmedForceNtf = [[cs_matchroom]],
CSMatchTicketReturnNtf = [[cs_match]],
CSMatchWaitDSNtf = [[cs_match]],
CSMeleeUnlockChangeNtf = [[cs_deposit]],
CSMossaiChatAutoChatReq = [[cs_mossai]],
CSMossaiChatAutoChatRes = [[cs_mossai]],
CSMossaiChatFuncInfoReq = [[cs_mossai]],
CSMossaiChatFuncInfoRes = [[cs_mossai]],
CSMossaiChatHeartBeatReq = [[cs_mossai]],
CSMossaiChatHeartBeatRes = [[cs_mossai]],
CSMossaiChatInterruptReq = [[cs_mossai]],
CSMossaiChatInterruptRes = [[cs_mossai]],
CSMossaiChatStartReq = [[cs_mossai]],
CSMossaiChatStartRes = [[cs_mossai]],
CSMossaiChatStopReq = [[cs_mossai]],
CSMossaiChatStopRes = [[cs_mossai]],
CSMossaiChatSwitchRoleReq = [[cs_mossai]],
CSMossaiChatSwitchRoleRes = [[cs_mossai]],
CSMpdepositBagsSyncNtf = [[cs_mpdeposit]],
CSMysticalPendantCntInfo = [[cs_collection]],
CSMysticalPendantSuitInfo = [[cs_collection]],
CSNotifyNtf = [[cs_state]],
CSOnlineHeartbeatReq = [[cscpp_online]],
CSOnlineHeartbeatRes = [[cscpp_online]],
CSOpenBoxReq = [[cs_lottery]],
CSOpenBoxRes = [[cs_lottery]],
CSPVEChapterInfoReq = [[cs_pve]],
CSPVEChapterInfoRes = [[cs_pve]],
CSPatchCheckVersionReq = [[cs_patch]],
CSPatchCheckVersionRes = [[cs_patch]],
CSPatchLocalizationTextGetReq = [[cs_patch]],
CSPatchLocalizationTextGetRes = [[cs_patch]],
CSPatchQuickPatchReq = [[cs_patch]],
CSPatchQuickPatchRes = [[cs_patch]],
CSPayDoMidasReprovideReq = [[cs_pay]],
CSPayDoMidasReprovideRes = [[cs_pay]],
CSPayQueryMidasRiskInfoReq = [[cs_pay]],
CSPayQueryMidasRiskInfoRes = [[cs_pay]],
CSPayQueryRechargeGoodListReq = [[cs_pay]],
CSPayQueryRechargeGoodListRes = [[cs_pay]],
CSPkg = [[cs_pkg]],
CSPkgHead = [[cs_pkg]],
CSPlayerAcceptArmoryTaskReq = [[cs_quest]],
CSPlayerAcceptArmoryTaskRes = [[cs_quest]],
CSPlayerArmoryAddQueueReq = [[cs_quest]],
CSPlayerArmoryAddQueueRes = [[cs_quest]],
CSPlayerArmoryCancelQueueReq = [[cs_quest]],
CSPlayerArmoryCancelQueueRes = [[cs_quest]],
CSPlayerArmoryTakeRewardReq = [[cs_quest]],
CSPlayerArmoryTakeRewardRes = [[cs_quest]],
CSPlayerAuxDataMatchInfo = [[cs_rank]],
CSPlayerCheckGameCenterSubscriptionStatusReq = [[cs_playerinfo]],
CSPlayerCheckGameCenterSubscriptionStatusRes = [[cs_playerinfo]],
CSPlayerClickPopUpReq = [[cs_playerinfo]],
CSPlayerCommanderModePraiseReq = [[cs_playerinfo]],
CSPlayerCommanderModePraiseRes = [[cs_playerinfo]],
CSPlayerCopyGetCardWindowInfoReq = [[cs_playercopy]],
CSPlayerCopyGetCardWindowInfoRes = [[cs_playercopy]],
CSPlayerGetBannersReq = [[cs_playerinfo]],
CSPlayerGetBannersRes = [[cs_playerinfo]],
CSPlayerGetBasicInfoReq = [[cs_playerinfo]],
CSPlayerGetBasicInfoRes = [[cs_playerinfo]],
CSPlayerGetEntranceImageReq = [[cs_playerinfo]],
CSPlayerGetEntranceImageRes = [[cs_playerinfo]],
CSPlayerGetJumpConfigDataReq = [[cs_playerinfo]],
CSPlayerGetJumpConfigDataRes = [[cs_playerinfo]],
CSPlayerGetLobbyBannerDataReq = [[cs_playerinfo]],
CSPlayerGetLobbyBannerDataRes = [[cs_playerinfo]],
CSPlayerGetMPDropLimitDataReq = [[cs_playerinfo]],
CSPlayerGetMPDropLimitDataRes = [[cs_playerinfo]],
CSPlayerInfoAddButtonChangedNtf = [[cs_playerinfo]],
CSPlayerInfoAddButtonHasBeenClickedReq = [[cs_playerinfo]],
CSPlayerInfoAddButtonHasBeenClickedRes = [[cs_playerinfo]],
CSPlayerInfoBePraisedNtf = [[cs_playerinfo]],
CSPlayerInfoCommanderBePraisedNtf = [[cs_playerinfo]],
CSPlayerInfoGetAppearanceReq = [[cs_playerinfo]],
CSPlayerInfoGetAppearanceRes = [[cs_playerinfo]],
CSPlayerInfoGetDownloadCosParamsReq = [[cs_playerinfo]],
CSPlayerInfoGetDownloadCosParamsRes = [[cs_playerinfo]],
CSPlayerInfoGetLogReportStatusNtf = [[cs_playerinfo]],
CSPlayerInfoGetMarkPropsReq = [[cs_playerinfo]],
CSPlayerInfoGetMarkPropsRes = [[cs_playerinfo]],
CSPlayerInfoMarkChangeReq = [[cs_playerinfo]],
CSPlayerInfoMarkChangeRes = [[cs_playerinfo]],
CSPlayerInfoPraiseReq = [[cs_playerinfo]],
CSPlayerInfoPraiseRes = [[cs_playerinfo]],
CSPlayerInfoQueryMetaPerfCondReq = [[cs_playerinfo]],
CSPlayerInfoQueryMetaPerfCondRes = [[cs_playerinfo]],
CSPlayerInfoSaveAppearanceReq = [[cs_playerinfo]],
CSPlayerInfoSaveAppearanceRes = [[cs_playerinfo]],
CSPlayerInfoUploadLogReq = [[cs_playerinfo]],
CSPlayerInfoUploadLogRes = [[cs_playerinfo]],
CSPlayerInfoUploadMetaPerfReq = [[cs_playerinfo]],
CSPlayerInfoUploadMetaPerfRes = [[cs_playerinfo]],
CSPlayerInfoWishListChangedNtf = [[cs_playerinfo]],
CSPlayerJoinDsStatusReq = [[cs_matchroom]],
CSPlayerJoinDsStatusRes = [[cs_matchroom]],
CSPlayerJoinMatchNtf = [[cs_matchroom]],
CSPlayerJoinSafehouseNtf = [[cs_matchroom]],
CSPlayerLogReportNtf = [[cs_playerinfo]],
CSPlayerPromptGetReq = [[cs_playerinfo]],
CSPlayerPromptGetRes = [[cs_playerinfo]],
CSPlayerPromptShowReq = [[cs_playerinfo]],
CSPlayerPromptShowRes = [[cs_playerinfo]],
CSPlayerStructTriggerGuideNtf = [[cs_guide]],
CSPlayerStructedGuideCrowdOutNtf = [[cs_guide]],
CSPlayerStructedTriggerGuideDisplayReq = [[cs_guide]],
CSPlayerStructedTriggerGuideDisplayRes = [[cs_guide]],
CSPlayerSubscribeGameCenterReq = [[cs_playerinfo]],
CSPlayerSubscribeGameCenterRes = [[cs_playerinfo]],
CSPlayerUpdateBasicInfoReq = [[cs_playerinfo]],
CSPlayerUpdateBasicInfoRes = [[cs_playerinfo]],
CSPlayerUpdateDisplayMpRankPointsReq = [[cs_playerinfo]],
CSPlayerUpdateDisplayMpRankPointsRes = [[cs_playerinfo]],
CSPlayerUpdateMilitaryTagReq = [[cs_playerinfo]],
CSPlayerUpdateMilitaryTagRes = [[cs_playerinfo]],
CSPlayerUpdateTitleReq = [[cs_playerinfo]],
CSPlayerUpdateTitleRes = [[cs_playerinfo]],
CSPopularityNtf = [[common]],
CSPrepareJoinMatchNtf = [[cs_matchroom]],
CSPrepareMapBoardReq = [[cs_solo]],
CSPrepareMapBoardRes = [[cs_solo]],
CSPrepareTDMMapBoardReq = [[cs_solo]],
CSPrepareTDMMapBoardRes = [[cs_solo]],
CSPunishNtf = [[common]],
CSQuestAcceptReq = [[cs_quest]],
CSQuestAcceptRes = [[cs_quest]],
CSQuestCollectorSetStatusReq = [[cs_quest]],
CSQuestCollectorSetStatusRes = [[cs_quest]],
CSQuestCollectorSubmitReq = [[cs_quest]],
CSQuestCollectorSubmitRes = [[cs_quest]],
CSQuestCollectorTakeRewardReq = [[cs_quest]],
CSQuestCollectorTakeRewardRes = [[cs_quest]],
CSQuestCollectorTaskRefreshReq = [[cs_quest]],
CSQuestCollectorTaskRefreshRes = [[cs_quest]],
CSQuestCompleteReq = [[cs_quest]],
CSQuestCompleteRes = [[cs_quest]],
CSQuestDataChangeNtf = [[cs_quest]],
CSQuestFateContractAcceptReq = [[cs_quest]],
CSQuestFateContractAcceptRes = [[cs_quest]],
CSQuestFinishStandaloneObjectivesReq = [[cs_quest]],
CSQuestFinishStandaloneObjectivesRes = [[cs_quest]],
CSQuestGetAcceptedQuestsReq = [[cs_quest]],
CSQuestGetAcceptedQuestsRes = [[cs_quest]],
CSQuestGetCsvTablesNtf = [[cs_quest]],
CSQuestGetCsvTablesReq = [[cs_quest]],
CSQuestGetCsvTablesRes = [[cs_quest]],
CSQuestGetPlayerDataReq = [[cs_quest]],
CSQuestGetPlayerDataRes = [[cs_quest]],
CSQuestGetQuestLineExtraInfoReq = [[cs_quest]],
CSQuestGetQuestLineExtraInfoRes = [[cs_quest]],
CSQuestGetRewardReq = [[cs_quest]],
CSQuestGetRewardRes = [[cs_quest]],
CSQuestGiveUpReq = [[cs_quest]],
CSQuestGiveUpRes = [[cs_quest]],
CSQuestHashTable = [[cs_quest]],
CSQuestPauseReq = [[cs_quest]],
CSQuestPauseRes = [[cs_quest]],
CSQuestPlayCGCompletedReq = [[cs_quest]],
CSQuestPlayCGCompletedRes = [[cs_quest]],
CSQuestSetObjectiveMarkValueReq = [[cs_quest]],
CSQuestSetObjectiveMarkValueRes = [[cs_quest]],
CSQuestSubmitPropReq = [[cs_quest]],
CSQuestSubmitPropRes = [[cs_quest]],
CSQuestTakeOverTimeRewardReq = [[cs_quest]],
CSQuestTakeOverTimeRewardRes = [[cs_quest]],
CSRackFee = [[cs_auction]],
CSRadarDim = [[cs_season]],
CSRaidBuyConditionReq = [[cs_raid]],
CSRaidBuyConditionRes = [[cs_raid]],
CSRankBatchGetAuxDataReq = [[cs_rank]],
CSRankBatchGetAuxDataRes = [[cs_rank]],
CSRankCheckPopUpWindowReq = [[cs_rank]],
CSRankCheckPopUpWindowRes = [[cs_rank]],
CSRankGetHandbookReq = [[cs_rank]],
CSRankGetHandbookRes = [[cs_rank]],
CSRankGetListReq = [[cs_rank]],
CSRankGetListRes = [[cs_rank]],
CSRankGetPlayerHistoricalRecordReq = [[cs_rank]],
CSRankGetPlayerHistoricalRecordRes = [[cs_rank]],
CSRankGetPlayerRankAuxDataDetailReq = [[cs_rank]],
CSRankGetPlayerRankAuxDataDetailRes = [[cs_rank]],
CSRankGetPlayerRankAuxDataReq = [[cs_rank]],
CSRankGetPlayerRankAuxDataRes = [[cs_rank]],
CSRankGetPlayerRankPercentageReq = [[cs_rank]],
CSRankGetPlayerRankPercentageRes = [[cs_rank]],
CSRankItemExData = [[cs_rank]],
CSRankPlayerAuxData = [[common]],
CSRankPlayerHandBookData = [[cs_rank]],
CSRankPlayerHandBookDataTypeInfo = [[cs_rank]],
CSRankPrivacyGetReq = [[cs_rank]],
CSRankPrivacyGetRes = [[cs_rank]],
CSRankPrivacySetReq = [[cs_rank]],
CSRankPrivacySetRes = [[cs_rank]],
CSRankZoneGetReq = [[cs_rank]],
CSRankZoneGetRes = [[cs_rank]],
CSRankZoneSetReq = [[cs_rank]],
CSRankZoneSetRes = [[cs_rank]],
CSRecoveryAmountHistory = [[cs_deposit]],
CSRecruitmentEditTReq = [[cs_recruitment]],
CSRecruitmentEditTRes = [[cs_recruitment]],
CSRecruitmentEndTReq = [[cs_recruitment]],
CSRecruitmentEndTRes = [[cs_recruitment]],
CSRecruitmentFetchTReq = [[cs_recruitment]],
CSRecruitmentFetchTRes = [[cs_recruitment]],
CSRecruitmentPostTReq = [[cs_recruitment]],
CSRecruitmentPostTRes = [[cs_recruitment]],
CSRecruitmentRecommendTReq = [[cs_recruitment]],
CSRecruitmentRecommendTRes = [[cs_recruitment]],
CSRecruitmentStateChangeNtf = [[cs_recruitment]],
CSRecruitmentUpdateStateTReq = [[cs_recruitment]],
CSRecruitmentUpdateStateTRes = [[cs_recruitment]],
CSReportIDCReq = [[cs_idcchosen]],
CSReportIDCRes = [[cs_idcchosen]],
CSResourceUpdateReq = [[cs_resource]],
CSResourceUpdateRes = [[cs_resource]],
CSRoomApplyLeaderNtf = [[cs_room]],
CSRoomApplyLeaderResponseNtf = [[cs_room]],
CSRoomApplyLeaderResponseTReq = [[cs_room]],
CSRoomApplyLeaderResponseTRes = [[cs_room]],
CSRoomApplyLeaderTReq = [[cs_room]],
CSRoomApplyLeaderTRes = [[cs_room]],
CSRoomBatchChangeTeamNameTReq = [[cs_room]],
CSRoomBatchChangeTeamNameTRes = [[cs_room]],
CSRoomBeInvitedTipsNtf = [[cs_room]],
CSRoomBeginMatchTReq = [[cs_room]],
CSRoomBeginMatchTRes = [[cs_room]],
CSRoomChangeCampTReq = [[cs_room]],
CSRoomChangeCampTRes = [[cs_room]],
CSRoomChangeEditModeTReq = [[cs_room]],
CSRoomChangeEditModeTRes = [[cs_room]],
CSRoomChangeLeaderTReq = [[cs_room]],
CSRoomChangeLeaderTRes = [[cs_room]],
CSRoomChangeNtf = [[cs_room]],
CSRoomChangeOwnerTReq = [[cs_room]],
CSRoomChangeOwnerTRes = [[cs_room]],
CSRoomChangeReadyStateTReq = [[cs_room]],
CSRoomChangeReadyStateTRes = [[cs_room]],
CSRoomChangeSeatTReq = [[cs_room]],
CSRoomChangeSeatTRes = [[cs_room]],
CSRoomChangeSettingTReq = [[cs_room]],
CSRoomChangeSettingTRes = [[cs_room]],
CSRoomChangeTeamInfoTReq = [[cs_room]],
CSRoomChangeTeamInfoTRes = [[cs_room]],
CSRoomChangeTeamLeaderTReq = [[cs_room]],
CSRoomChangeTeamLeaderTRes = [[cs_room]],
CSRoomChangeTeamNameTReq = [[cs_room]],
CSRoomChangeTeamNameTRes = [[cs_room]],
CSRoomCreateReq = [[cs_room]],
CSRoomCreateRes = [[cs_room]],
CSRoomGetBaseInfoTReq = [[cs_room]],
CSRoomGetBaseInfoTRes = [[cs_room]],
CSRoomGetInfoTReq = [[cs_room]],
CSRoomGetInfoTRes = [[cs_room]],
CSRoomGetListReq = [[cs_room]],
CSRoomGetListRes = [[cs_room]],
CSRoomGetMatchModeListReq = [[cs_room]],
CSRoomGetMatchModeListRes = [[cs_room]],
CSRoomInviteTReq = [[cs_room]],
CSRoomInviteTRes = [[cs_room]],
CSRoomJoinTReq = [[cs_room]],
CSRoomJoinTRes = [[cs_room]],
CSRoomKickMemberNtf = [[cs_room]],
CSRoomKickMemberTReq = [[cs_room]],
CSRoomKickMemberTRes = [[cs_room]],
CSRoomMatchInvalidEndNtf = [[cs_match]],
CSRoomMatchQuitAllocReq = [[cs_matchgate]],
CSRoomMatchQuitAllocRes = [[cs_matchgate]],
CSRoomMatchStartAllocReq = [[cs_matchgate]],
CSRoomMatchStartAllocRes = [[cs_matchgate]],
CSRoomMatchStartFailNtf = [[cs_matchinginfo]],
CSRoomMatchTimeoutNtf = [[cs_match]],
CSRoomQuitTReq = [[cs_room]],
CSRoomQuitTRes = [[cs_room]],
CSRoomResponseInviteNtf = [[cs_room]],
CSRoomResponseInviteTReq = [[cs_room]],
CSRoomResponseInviteTRes = [[cs_room]],
CSRoundtripDirReq = [[cs_idcchosen]],
CSRoundtripDirRes = [[cs_idcchosen]],
CSRoundtripReq = [[ds_roundtrip]],
CSRoundtripRes = [[ds_roundtrip]],
CSSOLOGetSCAVInfoReq = [[cs_solo]],
CSSOLOGetSCAVInfoRes = [[cs_solo]],
CSSOLORerollSCAVReq = [[cs_solo]],
CSSOLORerollSCAVRes = [[cs_solo]],
CSSOLOSelectSCAVReq = [[cs_solo]],
CSSOLOSelectSCAVRes = [[cs_solo]],
CSSOLRadarData = [[cs_season]],
CSSOLSeasonData = [[cs_season]],
CSSafehouseBlueprintProduceReq = [[cs_safehouse]],
CSSafehouseBlueprintProduceRes = [[cs_safehouse]],
CSSafehouseFormulaFavoriteReq = [[cs_safehouse]],
CSSafehouseFormulaFavoriteRes = [[cs_safehouse]],
CSSafehouseFuncIsUnlockReq = [[cs_safehouse]],
CSSafehouseFuncIsUnlockRes = [[cs_safehouse]],
CSSafehouseFuncUnlockParams = [[cs_safehouse]],
CSSafehouseGetAuctionTaxDiscountReq = [[cs_safehouse]],
CSSafehouseGetAuctionTaxDiscountRes = [[cs_safehouse]],
CSSafehouseGetBlueprintInfoReq = [[cs_safehouse]],
CSSafehouseGetBlueprintInfoRes = [[cs_safehouse]],
CSSafehouseGetConfigReq = [[cs_safehouse]],
CSSafehouseGetConfigRes = [[cs_safehouse]],
CSSafehouseGetInfoReq = [[cs_safehouse]],
CSSafehouseGetInfoRes = [[cs_safehouse]],
CSSafehouseGetPlayerDeviceReq = [[cs_safehouse]],
CSSafehouseGetPlayerDeviceRes = [[cs_safehouse]],
CSSafehouseGetUnlockSystemReq = [[cs_safehouse]],
CSSafehouseGetUnlockSystemRes = [[cs_safehouse]],
CSSafehouseGetVisitorReq = [[cs_safehouse]],
CSSafehouseGetVisitorRes = [[cs_safehouse]],
CSSafehouseLeaderDeviceChangeNtf = [[cs_safehouse]],
CSSafehouseLoopEfficiencyChangeReq = [[cs_safehouse]],
CSSafehouseLoopEfficiencyChangeRes = [[cs_safehouse]],
CSSafehouseProduceReq = [[cs_safehouse]],
CSSafehouseProduceRes = [[cs_safehouse]],
CSSafehouseProduceTerminateReq = [[cs_safehouse]],
CSSafehouseProduceTerminateRes = [[cs_safehouse]],
CSSafehouseReceiveAllLoopProduceReq = [[cs_safehouse]],
CSSafehouseReceiveAllLoopProduceRes = [[cs_safehouse]],
CSSafehouseReceiveAwardReq = [[cs_safehouse]],
CSSafehouseReceiveAwardRes = [[cs_safehouse]],
CSSafehouseReceiveSupplyReq = [[cs_safehouse]],
CSSafehouseReceiveSupplyRes = [[cs_safehouse]],
CSSafehouseSellToVisitorReq = [[cs_safehouse]],
CSSafehouseSellToVisitorRes = [[cs_safehouse]],
CSSafehouseUpgradeReq = [[cs_safehouse]],
CSSafehouseUpgradeRes = [[cs_safehouse]],
CSSafehouseUpgradeTerminateReq = [[cs_safehouse]],
CSSafehouseUpgradeTerminateRes = [[cs_safehouse]],
CSSafehouseVisitorReplaceReq = [[cs_safehouse]],
CSSafehouseVisitorReplaceRes = [[cs_safehouse]],
CSSaleProp = [[cs_auction]],
CSSaleType = [[cs_auction]],
CSSeasonBuyMallPropsReq = [[cs_season]],
CSSeasonBuyMallPropsRes = [[cs_season]],
CSSeasonCommanderRecvAwardReq = [[cs_season]],
CSSeasonCommanderRecvAwardRes = [[cs_season]],
CSSeasonGetDetailDataReq = [[cs_season]],
CSSeasonGetDetailDataRes = [[cs_season]],
CSSeasonGetInfoReq = [[cs_season]],
CSSeasonGetInfoRes = [[cs_season]],
CSSeasonGetMallPropsReq = [[cs_season]],
CSSeasonGetMallPropsRes = [[cs_season]],
CSSeasonGetMatchDeathHurtDataReq = [[cs_season]],
CSSeasonGetMatchDeathHurtDataRes = [[cs_season]],
CSSeasonGetMatchGainedDataReq = [[cs_season]],
CSSeasonGetMatchGainedDataRes = [[cs_season]],
CSSeasonGetMatchKillDataReq = [[cs_season]],
CSSeasonGetMatchKillDataRes = [[cs_season]],
CSSeasonGetMatchScoreDataReq = [[cs_season]],
CSSeasonGetMatchScoreDataRes = [[cs_season]],
CSSeasonGetRecordListReq = [[cs_season]],
CSSeasonGetRecordListRes = [[cs_season]],
CSSeasonGetRecordReq = [[cs_season]],
CSSeasonGetRecordRes = [[cs_season]],
CSSeasonGetTotalDataReq = [[cs_season]],
CSSeasonGetTotalDataRes = [[cs_season]],
CSSeasonLoadTotalDataReq = [[cs_season]],
CSSeasonLoadTotalDataRes = [[cs_season]],
CSSeasonRaidMapListReq = [[cs_season]],
CSSeasonRaidMapListRes = [[cs_season]],
CSSeasonRankRecvAwardReq = [[cs_season]],
CSSeasonRankRecvAwardRes = [[cs_season]],
CSSeasonRankScoreChangedNtf = [[cs_season]],
CSSerialCheapBuyReq = [[cs_reconciliation]],
CSSerialCheapBuyRes = [[cs_reconciliation]],
CSSetPlayerPrivacyDataReq = [[cs_playerinfo]],
CSSetPlayerPrivacyDataRes = [[cs_playerinfo]],
CSSettingGenSharCodeReq = [[cs_setting]],
CSSettingGenSharCodeRes = [[cs_setting]],
CSSettingGetSharedValueReq = [[cs_setting]],
CSSettingGetSharedValueRes = [[cs_setting]],
CSSettingGetValueByKeyReq = [[cs_setting]],
CSSettingGetValueByKeyRes = [[cs_setting]],
CSSettingGetValuesByTypeReq = [[cs_setting]],
CSSettingGetValuesByTypeRes = [[cs_setting]],
CSSettingPutKeyValueReq = [[cs_setting]],
CSSettingPutKeyValueRes = [[cs_setting]],
CSSettlementEmptyNtf = [[zone2dsa]],
CSSettlementGetHasUnsettledMatchReq = [[cs_settlement]],
CSSettlementGetHasUnsettledMatchRes = [[cs_settlement]],
CSSettlementGetNewSettlementInfoReq = [[cs_settlement]],
CSSettlementGetNewSettlementInfoRes = [[cs_settlement]],
CSSettlementGetPVESettlementInfoReq = [[cs_settlement]],
CSSettlementGetPVESettlementInfoRes = [[cs_settlement]],
CSSettlementGetPlayerGameStatReq = [[cs_settlement]],
CSSettlementGetPlayerGameStatRes = [[cs_settlement]],
CSSettlementGetRecentCoPlayerListReq = [[cs_settlement]],
CSSettlementGetRecentCoPlayerListRes = [[cs_settlement]],
CSSettlementGetTdmMandelBrickReq = [[cs_settlement]],
CSSettlementGetTdmMandelBrickRes = [[cs_settlement]],
CSSettlementNewSettlementInfoNtf = [[cs_settlement]],
CSSettlementPVESettlementInfoNtf = [[cs_settlement]],
CSShareRewardTakeReq = [[cs_playerinfo]],
CSShareRewardTakeRes = [[cs_playerinfo]],
CSShareRewardTakeStatusReq = [[cs_playerinfo]],
CSShareRewardTakeStatusRes = [[cs_playerinfo]],
CSShopAddPVPlayListReq = [[cs_shop]],
CSShopAddPVPlayListRes = [[cs_shop]],
CSShopBuyHotRecommendationReq = [[cs_shop]],
CSShopBuyHotRecommendationRes = [[cs_shop]],
CSShopBuyLotteryItemReq = [[cs_shop]],
CSShopBuyLotteryItemRes = [[cs_shop]],
CSShopBuyLuckyNestItemReq = [[cs_shop]],
CSShopBuyLuckyNestItemRes = [[cs_shop]],
CSShopBuyMallBHDReq = [[cs_shop]],
CSShopBuyMallBHDRes = [[cs_shop]],
CSShopBuyMallGiftReq = [[cs_shop]],
CSShopBuyMallGiftRes = [[cs_shop]],
CSShopBuyPermissionCardReq = [[cs_shop]],
CSShopBuyPermissionCardRes = [[cs_shop]],
CSShopBuyReq = [[cs_shop]],
CSShopBuyRes = [[cs_shop]],
CSShopBuySpecialBackItemReq = [[cs_shop]],
CSShopBuySpecialBackItemRes = [[cs_shop]],
CSShopClickRedDotReq = [[cs_shop]],
CSShopClickRedDotRes = [[cs_shop]],
CSShopFreePickReq = [[cs_shop]],
CSShopFreePickRes = [[cs_shop]],
CSShopGetBuyRecordReq = [[cs_shop]],
CSShopGetBuyRecordRes = [[cs_shop]],
CSShopGetClickedRedDotReq = [[cs_shop]],
CSShopGetClickedRedDotRes = [[cs_shop]],
CSShopGetConfigReq = [[cs_shop]],
CSShopGetConfigRes = [[cs_shop]],
CSShopGetLotteryInfoReq = [[cs_shop]],
CSShopGetLotteryInfoRes = [[cs_shop]],
CSShopGetLuckyNestConfigReq = [[cs_shop]],
CSShopGetLuckyNestConfigRes = [[cs_shop]],
CSShopGetMandelOpenedRewardReq = [[cs_shop]],
CSShopGetMandelOpenedRewardRes = [[cs_shop]],
CSShopGetPresentConfigReq = [[cs_shop]],
CSShopGetPresentConfigRes = [[cs_shop]],
CSShopGetPresentRecordReq = [[cs_shop]],
CSShopGetPresentRecordRes = [[cs_shop]],
CSShopGetRecordReq = [[cs_shop]],
CSShopGetRecordRes = [[cs_shop]],
CSShopGetSpecialBackInfoReq = [[cs_shop]],
CSShopGetSpecialBackInfoRes = [[cs_shop]],
CSShopGetThemeBundleTimeConfigReq = [[cs_shop]],
CSShopGetThemeBundleTimeConfigRes = [[cs_shop]],
CSShopNewGetConfigReq = [[cs_shop]],
CSShopNewGetConfigRes = [[cs_shop]],
CSShopOpenLotteryItemReq = [[cs_shop]],
CSShopOpenLotteryItemRes = [[cs_shop]],
CSShopPresentHotRecommendationReq = [[cs_shop]],
CSShopPresentHotRecommendationRes = [[cs_shop]],
CSShopPresentMallGiftReq = [[cs_shop]],
CSShopPresentMallGiftRes = [[cs_shop]],
CSShopRaffleLuckyNestReq = [[cs_shop]],
CSShopRaffleLuckyNestRes = [[cs_shop]],
CSShopSetMandelBoxUpReq = [[cs_shop]],
CSShopSetMandelBoxUpRes = [[cs_shop]],
CSShopSetSlippageReq = [[cs_shop]],
CSShopSetSlippageRes = [[cs_shop]],
CSShowCabinet = [[cs_deposit]],
CSShowGrid = [[cs_deposit]],
CSShowGridCostItems = [[cs_deposit]],
CSStateBatchGetInfoReq = [[cscpp_online]],
CSStateBatchGetInfoRes = [[cscpp_online]],
CSStateGetInfoReq = [[cscpp_online]],
CSStateGetInfoRes = [[cscpp_online]],
CSStateQuitMatchReq = [[cs_state]],
CSStateQuitMatchRes = [[cs_state]],
CSStateReconnectMatchNtf = [[cs_state]],
CSStateReconnectMatchReq = [[cs_state]],
CSStateReconnectMatchRes = [[cs_state]],
CSStateRevengeReq = [[cs_state]],
CSStateRevengeRes = [[cs_state]],
CSStressTestActivityCurrencyChangeNtf = [[cs_stresstest]],
CSStressTestActivityMatchingChangeNtf = [[cs_stresstest]],
CSStressTestActivityPlayerLoginNtf = [[cs_stresstest]],
CSStressTestAddDepositCapacityReq = [[cs_stresstest]],
CSStressTestAddDepositCapacityRes = [[cs_stresstest]],
CSStressTestArmoryTaskGoalsProgressReq = [[cs_stresstest]],
CSStressTestArmoryTaskGoalsProgressRes = [[cs_stresstest]],
CSStressTestBypassReq = [[cs_stresstest]],
CSStressTestBypassRes = [[cs_stresstest]],
CSStressTestClearDepositReq = [[cs_stresstest]],
CSStressTestClearDepositRes = [[cs_stresstest]],
CSStressTestCreateDSRoomReq = [[cs_stresstest]],
CSStressTestCreateDSRoomRes = [[cs_stresstest]],
CSStressTestCurrencyChangeReq = [[cs_stresstest]],
CSStressTestCurrencyChangeRes = [[cs_stresstest]],
CSStressTestDepositChangeNtf = [[cs_stresstest]],
CSStressTestDepositExchangeReq = [[cs_stresstest]],
CSStressTestDepositExchangeRes = [[cs_stresstest]],
CSStressTestEditDBDepositReq = [[cs_stresstest]],
CSStressTestEditDBDepositRes = [[cs_stresstest]],
CSStressTestGetRankListReq = [[cs_stresstest]],
CSStressTestGetRankListRes = [[cs_stresstest]],
CSStressTestGuideInstructionNtf = [[cs_stresstest]],
CSStressTestKickPlayerNtf = [[cs_stresstest]],
CSStressTestMatchStartAllocReq = [[cs_stresstest]],
CSStressTestMatchStartAllocRes = [[cs_stresstest]],
CSStressTestRoomChangeReq = [[cs_stresstest]],
CSStressTestRoomChangeRes = [[cs_stresstest]],
CSStressTestSettlementTestReq = [[cs_stresstest]],
CSStressTestSettlementTestRes = [[cs_stresstest]],
CSStressTestTaskGoalsQueryAllReq = [[cs_stresstest]],
CSStressTestTaskGoalsQueryAllRes = [[cs_stresstest]],
CSStressTestUpdateRankScoreReq = [[cs_stresstest]],
CSStressTestUpdateRankScoreRes = [[cs_stresstest]],
CSStressTestUpdateReputationReq = [[cs_stresstest]],
CSStressTestUpdateReputationRes = [[cs_stresstest]],
CSStressTestUpdateReputationStarReq = [[cs_stresstest]],
CSStressTestUpdateReputationStarRes = [[cs_stresstest]],
CSSwitchGetAccountUnlockItemsReq = [[cs_switch]],
CSSwitchGetAccountUnlockItemsRes = [[cs_switch]],
CSSwitchLoadModuleStatusReq = [[cs_switch]],
CSSwitchLoadModuleStatusRes = [[cs_switch]],
CSSwitchLoadSystemUnlockInfoReq = [[cs_switch]],
CSSwitchLoadSystemUnlockInfoRes = [[cs_switch]],
CSSwitchMoudleUnlockNtf = [[cs_switch]],
CSTSSPlayerQueryScnLimitReq = [[cs_tss]],
CSTSSPlayerQueryScnLimitRes = [[cs_tss]],
CSTSSQueryPlayerCreditScoreReq = [[cs_tss]],
CSTSSQueryPlayerCreditScoreRes = [[cs_tss]],
CSTeamApplyJoinNtf = [[cs_team]],
CSTeamApplyJoinTReq = [[cs_team]],
CSTeamApplyJoinTRes = [[cs_team]],
CSTeamApplyLeaderNtf = [[cs_team]],
CSTeamApplyLeaderResponseNtf = [[cs_team]],
CSTeamApplyLeaderResponseTReq = [[cs_team]],
CSTeamApplyLeaderResponseTRes = [[cs_team]],
CSTeamApplyLeaderTReq = [[cs_team]],
CSTeamApplyLeaderTRes = [[cs_team]],
CSTeamBaseInfoChangeNtf = [[cs_team]],
CSTeamBhdNextLevelReadyTReq = [[cs_team]],
CSTeamBhdNextLevelReadyTRes = [[cs_team]],
CSTeamBroadcastNtf = [[cs_team]],
CSTeamBroadcastTReq = [[cs_team]],
CSTeamBroadcastTRes = [[cs_team]],
CSTeamCancelMatchTReq = [[cs_team]],
CSTeamCancelMatchTRes = [[cs_team]],
CSTeamChangeIsOpenQuickJoinTReq = [[cs_team]],
CSTeamChangeIsOpenQuickJoinTRes = [[cs_team]],
CSTeamChangeIsRankTReq = [[cs_team]],
CSTeamChangeIsRankTRes = [[cs_team]],
CSTeamChangeLeaderTReq = [[cs_team]],
CSTeamChangeLeaderTRes = [[cs_team]],
CSTeamChangeMatchIDTReq = [[cs_team]],
CSTeamChangeMatchIDTRes = [[cs_team]],
CSTeamChangeStateTReq = [[cs_team]],
CSTeamChangeStateTRes = [[cs_team]],
CSTeamCreateReq = [[cs_team]],
CSTeamCreateRes = [[cs_team]],
CSTeamDismissTReq = [[cs_team]],
CSTeamDismissTRes = [[cs_team]],
CSTeamEnterWorldTReq = [[cs_team]],
CSTeamEnterWorldTRes = [[cs_team]],
CSTeamEquipPositionChangeTNtf = [[cs_team]],
CSTeamExitTReq = [[cs_team]],
CSTeamExitTRes = [[cs_team]],
CSTeamGetInviteListReq = [[cs_team]],
CSTeamGetInviteListRes = [[cs_team]],
CSTeamGetShortNumTReq = [[cs_team]],
CSTeamGetShortNumTRes = [[cs_team]],
CSTeamInfoTReq = [[cs_team]],
CSTeamInfoTRes = [[cs_team]],
CSTeamIntoEquipTNtf = [[cs_team]],
CSTeamInviteTReq = [[cs_team]],
CSTeamInviteTRes = [[cs_team]],
CSTeamJoinByShortNumReq = [[cs_team]],
CSTeamJoinByShortNumRes = [[cs_team]],
CSTeamJoinFromMiniProgramTReq = [[cs_team]],
CSTeamJoinFromMiniProgramTRes = [[cs_team]],
CSTeamJoinFromRecruitmentTReq = [[cs_team]],
CSTeamJoinFromRecruitmentTRes = [[cs_team]],
CSTeamKickTReq = [[cs_team]],
CSTeamKickTRes = [[cs_team]],
CSTeamMemberBatchChangeNtf = [[cs_team]],
CSTeamMemberChangeNtf = [[cs_team]],
CSTeamNotifyPrepareTReq = [[cs_team]],
CSTeamNotifyPrepareTRes = [[cs_team]],
CSTeamPrepareEventNtf = [[cs_team]],
CSTeamPrepareNtf = [[cs_team]],
CSTeamPrepareTReq = [[cs_team]],
CSTeamPrepareTRes = [[cs_team]],
CSTeamRecruitmentTReq = [[cs_team]],
CSTeamRecruitmentTRes = [[cs_team]],
CSTeamResponseInviteNtf = [[cs_team]],
CSTeamResponseInviteTReq = [[cs_team]],
CSTeamResponseInviteTRes = [[cs_team]],
CSTeamResponseJoinNtf = [[cs_team]],
CSTeamResponseJoinTReq = [[cs_team]],
CSTeamResponseJoinTRes = [[cs_team]],
CSTeamSaveInviteListReq = [[cs_team]],
CSTeamSaveInviteListRes = [[cs_team]],
CSTeamSessionUpdateTReq = [[cs_team]],
CSTeamSessionUpdateTRes = [[cs_team]],
CSTeamSetReadyTReq = [[cs_team]],
CSTeamSetReadyTRes = [[cs_team]],
CSTeamStartMatchTReq = [[cs_team]],
CSTeamStartMatchTRes = [[cs_team]],
CSTeamToInviteeNtf = [[cs_team]],
CSTeamUnreadyNtf = [[cs_team]],
CSTeamUpdatePackQuestTReq = [[cs_team]],
CSTeamUpdatePackQuestTRes = [[cs_team]],
CSTlogAgentTglogReq = [[cscpp_tlogagent]],
CSTssAntiDataTransferNtf = [[cscpp_tss]],
CSTssFaceVerifyStatusChangeNtf = [[cs_tss]],
CSTssHeartbeatNtf = [[cs_tss]],
CSTssLoadReportConfigReq = [[cs_tss]],
CSTssLoadReportConfigRes = [[cs_tss]],
CSTssReportReq = [[cs_tss]],
CSTssReportRes = [[cs_tss]],
CSTssSendAntiDataNtf = [[cscpp_tss]],
CSUpdatePlayerBalanceNtf = [[common]],
CSUpdateReputationNtf = [[cs_playerinfo]],
CSVRowContent = [[cs_resource]],
CSVTableInfo = [[cs_resource]],
CSVehicle = [[cs_mpdeposit]],
CSVehiclePart = [[cs_mpdeposit]],
CSVehicleUnlockTask = [[cs_mpdeposit]],
CSWAssemblyApplySkinReq = [[cs_weaponassembly]],
CSWAssemblyApplySkinRes = [[cs_weaponassembly]],
CSWAssemblyDepositChangeReq = [[cs_weaponassembly]],
CSWAssemblyDepositChangeRes = [[cs_weaponassembly]],
CSWAssemblyDepositCompoundReq = [[cs_weaponassembly]],
CSWAssemblyDepositCompoundRes = [[cs_weaponassembly]],
CSWAssemblyDepositEquipReq = [[cs_weaponassembly]],
CSWAssemblyDepositEquipRes = [[cs_weaponassembly]],
CSWAssemblyDepositPropUpdateReq = [[cs_weaponassembly]],
CSWAssemblyDepositPropUpdateRes = [[cs_weaponassembly]],
CSWAssemblyDepositUnEquipReq = [[cs_weaponassembly]],
CSWAssemblyDepositUnEquipRes = [[cs_weaponassembly]],
CSWAssemblyDesignEquipReq = [[cs_weaponassembly]],
CSWAssemblyDesignEquipRes = [[cs_weaponassembly]],
CSWAssemblyDesignUnEquipReq = [[cs_weaponassembly]],
CSWAssemblyDesignUnEquipRes = [[cs_weaponassembly]],
CSWAssemblyEquipPropStyleReq = [[cs_weaponassembly]],
CSWAssemblyEquipPropStyleRes = [[cs_weaponassembly]],
CSWAssemblyGetDesignReq = [[cs_weaponassembly]],
CSWAssemblyGetDesignRes = [[cs_weaponassembly]],
CSWAssemblyGetUnlockInfoReq = [[cs_weaponassembly]],
CSWAssemblyGetUnlockInfoRes = [[cs_weaponassembly]],
CSWAssemblyGetUnlockedStylesReq = [[cs_weaponassembly]],
CSWAssemblyGetUnlockedStylesRes = [[cs_weaponassembly]],
CSWAssemblyModifySkinReq = [[cs_weaponassembly]],
CSWAssemblyModifySkinRes = [[cs_weaponassembly]],
CSWAssemblyPerkEquipReq = [[cs_weaponassembly]],
CSWAssemblyPerkEquipRes = [[cs_weaponassembly]],
CSWAssemblyPerkSwapReq = [[cs_weaponassembly]],
CSWAssemblyPerkSwapRes = [[cs_weaponassembly]],
CSWAssemblyPerkUnEquipReq = [[cs_weaponassembly]],
CSWAssemblyPerkUnEquipRes = [[cs_weaponassembly]],
CSWAssemblySkinInfoGetReq = [[cs_weaponassembly]],
CSWAssemblySkinInfoGetRes = [[cs_weaponassembly]],
CSWAssemblyUnlockFancyReq = [[cs_weaponassembly]],
CSWAssemblyUnlockFancyRes = [[cs_weaponassembly]],
CSWAssemblyUnlockStyleByConsumeReq = [[cs_weaponassembly]],
CSWAssemblyUnlockStyleByConsumeRes = [[cs_weaponassembly]],
CSWAssemblyUnlockStyleReq = [[cs_weaponassembly]],
CSWAssemblyUnlockStyleRes = [[cs_weaponassembly]],
CSWAssemblyUpdateDesignReq = [[cs_weaponassembly]],
CSWAssemblyUpdateDesignRes = [[cs_weaponassembly]],
CSWeaponAssemblyGetSharingCodeReq = [[cs_weaponassembly]],
CSWeaponAssemblyGetSharingCodeRes = [[cs_weaponassembly]],
CSWeaponAssemblyMPUpdateWeaponAndEquipReq = [[cs_weaponassembly]],
CSWeaponAssemblyMPUpdateWeaponAndEquipRes = [[cs_weaponassembly]],
CSWeaponAssemblyParseSharingCodeReq = [[cs_weaponassembly]],
CSWeaponAssemblyParseSharingCodeRes = [[cs_weaponassembly]],
CSWeaponDesign = [[cs_weaponassembly]],
CSWeaponGetRecommendSharingCodeReq = [[cs_weaponassembly]],
CSWeaponGetRecommendSharingCodeRes = [[cs_weaponassembly]],
CSWeaponUnlockInfo = [[cs_mpdeposit]],
CSWorldChatGetRoomIDReq = [[cs_worldchat]],
CSWorldChatGetRoomIDRes = [[cs_worldchat]],
CampBotConfig = [[cs_robot]],
CapitalFlow = [[tglog]],
CarryInItemFlow = [[tglog]],
CarryOutItemFlow = [[tglog]],
CdpStatDetailFlow = [[tglog]],
CdpStatFlow = [[tglog]],
ChangedPropInfo = [[cs_playerinfo]],
CharData = [[cs_fontrender]],
CharMsg = [[cs_fontrender]],
Character1PState = [[ds_tglog]],
Character3PState = [[ds_tglog]],
ChatBehavior = [[tglog]],
ChatMsg = [[cs_chat]],
ChatMsgDSPreset = [[cs_chat]],
ChatMsgSummaryGroup = [[cs_chat]],
ChatMsgSummaryPlayer = [[cs_chat]],
ChatVoiceData = [[cs_chat]],
CheckPlayerSeasonDetailRecord = [[tglog]],
CheckPlayerSeasonRecord = [[tglog]],
ClientCOSUploadFlow = [[tglog]],
ClientCheckRule = [[cs_patch]],
ClientCommonEvent = [[tglog]],
ClientGameLobbyFlow = [[tglog]],
ClientGwalletInfo = [[common]],
ClientIntlInfo = [[common]],
ClientMapBoardFlow = [[tglog]],
ClientPingConf = [[cs_idcchosen]],
ClientSaveInviteInfo = [[cs_team]],
ClientShareFlow = [[tglog]],
CoPlayerBFRecord = [[common]],
CoPlayerRaidRecord = [[common]],
CoPlayerSOLRecord = [[common]],
CoPlayerTDMRecord = [[common]],
CollectionBattleConsume = [[cs_collection]],
CollectionBattleConsumeChange = [[cs_collection]],
CollectionBattleConsumeFlow = [[tglog]],
CollectionFlow = [[clientds_tglog]],
CollectionGunSkinRewardsInfo = [[cs_collection]],
CollectionGunSkinRewardsPatternProgress = [[cs_collection]],
CollectionGunSkinRewardsProgress = [[cs_collection]],
CollectionIdipControlFlow = [[tglog]],
CollectionMasterGunSkinRewardsInfo = [[cs_collection]],
CollectionOpenFlow = [[tglog]],
CollectionPropChange = [[cs_collection]],
CollectionPropExpireFlow = [[tglog]],
CollectionPropFlow = [[tglog]],
CollectionPropInfo = [[common]],
CollectionQuest = [[cs_quest]],
CollectionRankTitleInfo = [[common]],
CollectionRedPoint = [[cs_collection]],
CollectionRentalForceUseFlow = [[tglog]],
CollectionRightsChange = [[cs_collection]],
CollectionRightsFlow = [[tglog]],
CollectionRightsInfo = [[cs_collection]],
CollectionRightsLogout = [[tglog]],
CollectionRoomButtonClickedFlow = [[clientds_tglog]],
CollectionRoomQuitFlow = [[clientds_tglog]],
CollectionServerFlow = [[tglog]],
CollectionViewFlow = [[tglog]],
CombatRoleAbility = [[ds_common]],
CombatRoleSpecialItem = [[ds_common]],
CommanderExtra = [[common]],
CommanderNumeral = [[common]],
CommanderRequireConditionInfo = [[cs_solo]],
CommanderSummary = [[common]],
CommonMail = [[cs_mail]],
CommonMailAttachment = [[cs_mail]],
Component = [[ds_common]],
CompoundCommand = [[common]],
ConfigVersionInfo = [[common]],
ContainerCheckItems = [[common]],
CouponConsumeFlow = [[tglog]],
CreditScoreFeedback = [[tglog]],
CsvClientHashTable = [[common]],
CsvNumeralData = [[ds_common]],
CsvTableData = [[ds_common]],
CsvTableRow = [[ds_common]],
CsvZipPkg = [[ds_common]],
CsvZipTable = [[ds_common]],
CurrencyChange = [[common]],
CurrencyDesc = [[common]],
CurrencyExchangeInfo = [[common]],
CurrencyExchangeRule = [[common]],
CurrencyInfo = [[common]],
CurrencyLimitInfo = [[cs_currency]],
CustomLoadBullet = [[cs_armedforce]],
CustomOutfitItem = [[cs_armedforce]],
CustomOutfitPositionItems = [[cs_armedforce]],
DBtoolOpRecord = [[tglog]],
DFMPGameTlogBase = [[tglog]],
DFRoundBotTlogScriptBase = [[tglog]],
DS2AnyReportMonitorNtf = [[zone2dsa]],
DS2QuestChangeObjectiveNtf = [[ds_quest]],
DS2QuestCleanObjectivesNtf = [[ds_quest]],
DS2QuestRemoveQuestNtf = [[ds_quest]],
DS2QuestSetGameStateMapNtf = [[ds_quest]],
DS2QuestSetQuestVarNtf = [[ds_quest]],
DSActorReplicate = [[tglog]],
DSArmedforceDataFlow = [[ds_tglog]],
DSAttrValue = [[ds_common]],
DSBulletDamageInfo = [[tglog]],
DSBulletRouletee = [[tglog]],
DSCharacterArmorTakeDamage = [[ds_tglog]],
DSCharacterArmorTakeHealthDebuff = [[tglog]],
DSCharacterUseItemHealth = [[ds_tglog]],
DSChatRealTimeVoiceFlow = [[tglog]],
DSDamageValidate = [[ds_tglog]],
DSDoorOpenInfoFlow = [[ds_tglog]],
DSDropPointVisitFlow = [[ds_tglog]],
DSEndGameWeaponUsedTime = [[ds_tglog]],
DSEventReport = [[ds_tglog]],
DSEveryShootFlow = [[ds_tglog]],
DSHero = [[ds_common]],
DSIncomingWeaponFlow = [[tglog]],
DSLogin = [[ds_tglog]],
DSLogout = [[ds_tglog]],
DSMPLabAILogNtf = [[zone2dsa]],
DSMachineLoad = [[common]],
DSOpenBoxPropDropFlow = [[ds_tglog]],
DSPlayerSyncLogin = [[ds_tglog]],
DSPropertyReplicate = [[tglog]],
DSQuestAcceptReq = [[ds_quest]],
DSQuestAcceptRes = [[ds_quest]],
DSQuestDataChangeNtf = [[ds_quest]],
DSQuestDesc = [[ds_common]],
DSQuestGetPlayerDataReq = [[ds_quest]],
DSQuestGetPlayerDataRes = [[ds_quest]],
DSQuestObjectiveDesc = [[ds_common]],
DSQuestRewardData = [[ds_quest]],
DSQuestUpdateObjectiveValueNtf = [[ds_quest]],
DSRoomFlow = [[tglog]],
DSRpc = [[tglog]],
DSSOLPlayerHealthStateReport = [[ds_tglog]],
DSSOLPlayerRepairReport = [[ds_tglog]],
DSSOLPlayerTreatmentReport = [[ds_tglog]],
DSStatus = [[tglog]],
DSSvrLoadStat = [[tglog]],
DSSvrStat = [[tglog]],
DSSyncLoginState = [[ds_tglog]],
DSTDMWinterActivity = [[ds_tglog]],
DSTimingSkillDataFlow = [[ds_tglog]],
DSVehicleAttackInfoFlow = [[ds_tglog]],
DSVehicleDamageInfoFlow = [[ds_tglog]],
DSVehiclePhysicsReplicationFlow = [[ds_tglog]],
DSWeaponDataWhenKillFlow = [[ds_tglog]],
DataChange = [[common]],
DayAndTimeIntervalInfo = [[cs_solo]],
DeadDiscardItemFlow = [[ds_tglog]],
DepositCapacityFlow = [[tglog]],
DepositCarryInFeeFlow = [[tglog]],
DepositDetailPropsFlow = [[tglog]],
DepositDisposableSafePackPermFlow = [[tglog]],
DepositMailItemFlow = [[tglog]],
DepositMeleeWeaponFlow = [[tglog]],
DepositRenewSafePackPermFlow = [[tglog]],
DepositRepairFlow = [[tglog]],
DepositSafePackPermFlow = [[tglog]],
DepositSetSortConfigFlow = [[tglog]],
DepositShowGridMoveFlow = [[tglog]],
DepositShowRoomItemFlow = [[tglog]],
DepositSortConfig = [[common]],
DepositSortFlow = [[tglog]],
DepositSpaceNotEnoughFlow = [[tglog]],
DetailOnlineCountFlow = [[tglog]],
DetailOnlineCountFlowByArea = [[tglog]],
DetailOnlineFlow = [[tglog]],
DetailPagesClickFlow = [[clientds_tglog]],
DiscordFriend = [[cs_friend]],
DiyPresetSave = [[tglog]],
DownloadSettingInfo = [[tglog]],
DrinkInfo = [[cs_activity]],
DrinkMaterialInfo = [[cs_activity]],
DropPickupItemInfo = [[ds_common]],
DrugCheckItems = [[common]],
Ds2AnyBeginMatchRes = [[zone2dsa]],
Ds2AnyBeginRaidRes = [[zone2dsa]],
Ds2AnyDeathAndHurtDetailInfoNtf = [[zone2dsa]],
Ds2AnyExitNtf = [[zone2dsa]],
Ds2AnyFramerateStatNtf = [[zone2dsa]],
Ds2AnyHeartbeatNtf = [[zone2dsa]],
Ds2AnyLogTgLogNtf = [[zone2dsa]],
Ds2AnyMatchEndNtf = [[zone2dsa]],
Ds2AnyMatchInfoNtf = [[zone2dsa]],
Ds2AnyMatchSettlementReq = [[zone2dsa]],
Ds2AnyMultiRoomExitNtf = [[zone2dsa]],
Ds2AnyPVESettlementReq = [[zone2dsa]],
Ds2AnyPlayerLoginDsNtf = [[zone2dsa]],
Ds2AnyPlayerLogoutDsNtf = [[zone2dsa]],
Ds2AnyPlayerSettlementReq = [[zone2dsa]],
Ds2AnyRoomHeartbeat = [[zone2dsa]],
Ds2AnyRoomInfoNtf = [[zone2dsa]],
Ds2AnyRoomStatisticsNtf = [[zone2dsa]],
Ds2AnySyncPlayerInfoRes = [[zone2dsa]],
Ds2AnySyncSafeHousePlayerInfoRes = [[zone2dsa]],
Ds2AnyTkvSettlementReq = [[zone2dsa]],
Ds2AnyTssReportNtf = [[zone2dsa]],
Ds2DsaPkgStatis = [[ds_tglog]],
Ds2RawLogTglogNtf = [[zone2dsa]],
Ds2RoomRecommendFriendNtf = [[zone2dsa]],
DsAIBasicInfo = [[ds_common]],
DsAccidentInfo = [[ds_common]],
DsAchievementInfo = [[ds_common]],
DsAllocFlow = [[tglog]],
DsArenaKnockDownFlow = [[ds_tglog]],
DsArenaRoundFlow = [[ds_tglog]],
DsBattleFieldWeaponKillInfos = [[ds_tglog]],
DsBhdRoundFlow = [[ds_tglog]],
DsBoxExtraDropFlow = [[ds_tglog]],
DsCharacterTakeDamageEveryGame = [[ds_tglog]],
DsConnectFailedFlow = [[tglog]],
DsConnectFlow = [[tglog]],
DsContractReportData = [[ds_tglog]],
DsDamageInfo = [[ds_common]],
DsDeathDetailedInfo = [[ds_common]],
DsElapsedTimeFlow = [[tglog]],
DsEndGamePlayerStatus = [[ds_common]],
DsFather2AnyChildExitNtf = [[zone2dsa]],
DsFather2AnyForkChildRes = [[zone2dsa]],
DsFather2AnyHeartbeatNtf = [[zone2dsa]],
DsFather2AnyLaunchNtf = [[zone2dsa]],
DsFatherExitNtf = [[zone2dsa]],
DsFightBehitedFlow = [[ds_tglog]],
DsFightHPGainedFlow = [[ds_tglog]],
DsFightHitFlow = [[ds_tglog]],
DsFixedWeaponDamageRepair = [[ds_tglog]],
DsFixedWeaponToCharacter = [[ds_tglog]],
DsFixedWeaponToVehicle = [[ds_tglog]],
DsGameAchievement = [[ds_common]],
DsGameAchievementTask = [[ds_common]],
DsGameGlobalItemFlow = [[ds_tglog]],
DsGameMedal = [[ds_common]],
DsHostInfo = [[common]],
DsHurtDetailedInfo = [[ds_common]],
DsIpInfo = [[common]],
DsKillInfos = [[ds_tglog]],
DsMPAiStat = [[ds_tglog]],
DsMPRoundFlow = [[ds_tglog]],
DsMPTransportFlagResultFlow = [[ds_tglog]],
DsMPVehicleFlow = [[ds_tglog]],
DsMatchBaseRecord = [[ds_common]],
DsMatchBaseRecordIrisSOL = [[ds_common]],
DsMatchEvent = [[ds_common]],
DsMatchInfoNew = [[ds_common]],
DsMatchModeStatistcs = [[tglog]],
DsMeleeWeaponPerformance = [[ds_tglog]],
DsNeverConnectionFlow = [[ds_tglog]],
DsPVEEndGamePlayerStatus = [[ds_common]],
DsPVEMissionInfo = [[ds_common]],
DsPVEMissionStat = [[ds_common]],
DsPVEPlayerStatus = [[ds_common]],
DsPVEPlayingGamePlayerStatus = [[ds_common]],
DsPVEQuitGamePlayerStatus = [[ds_common]],
DsPerShotCounter = [[ds_tglog]],
DsPkg = [[zone2dsa]],
DsPkgHead = [[zone2dsa]],
DsPlayerAddr = [[zone2dsa]],
DsPlayerBasicInfo = [[ds_common]],
DsPlayerDeathInfo = [[ds_common]],
DsPlayerGameStatus = [[ds_common]],
DsPlayerInOrOutPOITriggerData = [[ds_tglog]],
DsPlayerInfo = [[zone2dsa]],
DsPlayerKillInfo = [[ds_common]],
DsPlayerMatchStatNew = [[ds_common]],
DsPlayerPosition = [[ds_common]],
DsPlayingPlayerStatus = [[ds_common]],
DsQuestData = [[ds_common]],
DsQuestOperatePointFlow = [[ds_tglog]],
DsQuitGamePlayerStatus = [[ds_common]],
DsRaidPlayerGameStatus = [[ds_common]],
DsReportInvalidItem = [[ds_tglog]],
DsRoomStatistic = [[tglog]],
DsRoundFlow = [[ds_tglog]],
DsSOLMapOperate = [[clientds_tglog]],
DsSOLNightEventFlow = [[ds_tglog]],
DsSafeHousePlayerInfo = [[zone2dsa]],
DsSeasonMatchSettlementAINtf = [[zone2dsa]],
DsShopStationBuyRecord = [[ds_tglog]],
DsSimplePlayerInfo = [[zone2dsa]],
DsSolBlueprintWeaponAnalysisData = [[ds_tglog]],
DsSolCharacterRebornLogData = [[ds_tglog]],
DsSolInteractorAnalysisData = [[ds_tglog]],
DsStat = [[ds_common]],
DsSyncPlayerInfoFlow = [[tglog]],
DsTODInfo = [[zone2dsa]],
DsTeamInfo = [[ds_common]],
DsTlogStatist = [[tglog]],
DsTssReportData = [[ds_common]],
DsWeaponKillInfos = [[ds_tglog]],
DsaRouterMeta = [[zone2dsa]],
Dsagent2AnySendToZoneRes = [[zone2dsa]],
DsgateAllocIDCFlow = [[tglog]],
DsgateBeginMatchFlow = [[tglog]],
DslogmoveDeleteFileFlow = [[tglog]],
DstLogNameList = [[tglog]],
DynamicAuctionOptPriceCalcFlow = [[tglog]],
DynamicGuidePriceCalcFlow = [[tglog]],
DynamicGuidePriceEffectFlow = [[tglog]],
DynamicPriceCSUploaderFlow = [[tglog]],
DynamicPriceGMForceUpdateFlow = [[tglog]],
DynamicPriceReleaseFlow = [[tglog]],
DynamicPriceSyncFlow = [[tglog]],
Emoji = [[cs_chat]],
Empty = [[common]],
EndRecruitFlow = [[tglog]],
EnterMapItemLimitInfo = [[cs_solo]],
EnterRecruitFlow = [[tglog]],
EntranceImageConfig = [[cs_playerinfo]],
EquipDurabilityLvl = [[cs_armedforce]],
EquipHealth = [[ds_common]],
EquipPosition = [[ds_common]],
EquipPropCommand = [[cs_deposit]],
EquipRentalFlow = [[tglog]],
EquipRentalRefreshFlow = [[tglog]],
EquipUseTime = [[ds_common]],
EquipmentCheckItems = [[common]],
EquipmentRentalPlan = [[common]],
EquipmentRentalPlanDetail = [[common]],
EquipmentRentalProp = [[common]],
ErrSettlementNtf = [[tglog]],
EvacuationRecords = [[ds_tglog]],
EventInfo = [[cs_market]],
EventSpringCardReport = [[tglog]],
ExpertData = [[common]],
ExpertSkill = [[ds_common]],
ExtensionBoxOperateFlow = [[tglog]],
ExtensionBoxSlotUnlockNumFlow = [[tglog]],
FailAddEquipPoolFlow = [[tglog]],
FashionItem = [[common]],
FashionPriorSetting = [[cs_hero]],
FavorabilityFlow = [[tglog]],
FeedBackPlayerChoiceFlow = [[tglog]],
FightRewardBackImage = [[cs_activity]],
FilterRecruitFlow = [[tglog]],
ForbiddenCarryOutItemFlow = [[tglog]],
FriendApplyFlow = [[tglog]],
FriendApplyInfo = [[cs_friend]],
FriendCardInfo = [[cs_friend]],
FriendCardOnline = [[cs_friend]],
FriendDeleteFlow = [[tglog]],
FriendInfo = [[cs_friend]],
FriendRecommendFlow = [[tglog]],
FriendRelationListFlow = [[tglog]],
FriendRespondApplyFlow = [[tglog]],
GMCommandFlow = [[tglog]],
GamePingFlow = [[tglog]],
GameRuleOpenInfo = [[cs_solo]],
GameSvrState = [[tglog]],
GameViewReq = [[cs_gameview]],
GameViewRes = [[cs_gameview]],
GemHotMapInfo = [[ds_tglog]],
GenReconciliationFlow = [[tglog]],
GeneralSkill = [[ds_common]],
GlobalItemFlow = [[tglog]],
GlobalPlayerStateInfo = [[common]],
GreatEarnGoods = [[common]],
GridSize = [[ds_common]],
GroupChatMsg = [[cs_chat]],
GrowthLevelUpRewardsFlow = [[tglog]],
GrowthViewFlow = [[tglog]],
GspAIBasicInfo = [[common]],
GspAccidentInfo = [[common]],
GspAchievementInfo = [[common]],
GspDamageInfo = [[common]],
GspEndGamePlayerStatus = [[common]],
GspEndGameTeamMateStatus = [[common]],
GspMatchEvent = [[common]],
GspMatchInfoNew = [[common]],
GspPVEEndGamePlayerStatus = [[common]],
GspPVEEndGameTeamMateStatus = [[common]],
GspPVEMissionInfo = [[common]],
GspPVEMissionStat = [[common]],
GspPVEPlayerStatus = [[common]],
GspPVEPlayingGamePlayerStatus = [[common]],
GspPVEPlayingGameTeamMateStatus = [[common]],
GspPVEQuitGamePlayerStatus = [[common]],
GspPVEQuitGameTeamMateStatus = [[common]],
GspPVETeamMateStatus = [[common]],
GspPlayerBasicInfo = [[common]],
GspPlayerDeathInfo = [[common]],
GspPlayerGameStatus = [[common]],
GspPlayerKillInfo = [[common]],
GspPlayerMatchStatNew = [[common]],
GspPlayerPosition = [[common]],
GspPlayingPlayerStatus = [[common]],
GspPlayingTeamMateStatus = [[common]],
GspQuitGamePlayerStatus = [[common]],
GspQuitGameTeamMateStatus = [[common]],
GspTeamInfo = [[common]],
GspTeamMateGameStatus = [[common]],
GuideCustomData = [[cs_guide]],
GuideEvacuationPointInstructReport = [[tglog]],
GuideFlow = [[tglog]],
GuideTriggerInfo = [[cs_guide]],
GuildFlow = [[tglog]],
GunSkinUpdateGoalsFlow = [[tglog]],
GunSmithCodeReportFlow = [[tglog]],
GunSmithCodeUsedFlow = [[tglog]],
GunsmithClientActionReport = [[tglog]],
HalfJoinConfig = [[ds_common]],
HandleReconciliationFlow = [[tglog]],
HelicopterCrash = [[ds_tglog]],
HelpTeammateCarryOutInfo = [[ds_common]],
Hero = [[ds_common]],
HeroAccessoryClientClick = [[tglog]],
HeroAccessoryEmoteSlotUseFlow = [[ds_tglog]],
HeroAccessoryItem = [[ds_common]],
HeroAccessoryNumeralFlow = [[tglog]],
HeroAccessorySelectedFlow = [[tglog]],
HeroArmedPropFashion = [[ds_common]],
HeroArmedPropFashionInfo = [[ds_common]],
HeroArmedPropInfo = [[common]],
HeroAttr = [[ds_common]],
HeroBanInfo = [[cs_room]],
HeroClickFlow = [[tglog]],
HeroFashion = [[ds_common]],
HeroGrowLineLevelFlow = [[tglog]],
HeroGrowLineLogout = [[tglog]],
HeroGrowLineMayLevelUpFlow = [[tglog]],
HeroGrowLineUpdateGoalFlow = [[tglog]],
HeroInfoOnLogout = [[tglog]],
HeroItemBuyCon = [[tglog]],
HeroItemSaveFlow = [[tglog]],
HeroItemSlotUnlockFlow = [[tglog]],
HeroItemUseCon = [[tglog]],
HeroOpItemSysExpose = [[tglog]],
HeroPlayerSocialPropsOutFlow = [[tglog]],
HeroPropsUnlockFlow = [[tglog]],
HeroReddotInfo = [[common]],
HeroSelectedFlow = [[tglog]],
HiddenScoreAdjustFlow = [[tglog]],
HostInfo = [[common]],
HotRecommendationPropDesc = [[cs_shop]],
IDCDSLoad = [[common]],
IDCDSLoadStat = [[tglog]],
IDCDSRoomLoad = [[common]],
IDCDSStat = [[tglog]],
IDCDSTypeLoad = [[common]],
IDCRoundtripTime = [[common]],
IDIPFlow = [[tglog]],
InstalledProp = [[cs_activity]],
IntimacyFlow = [[tglog]],
IntlParentControlInfo = [[common]],
InventoryCheckFlow = [[tglog]],
InventoryExecFlow = [[tglog]],
IpCityFlow = [[ds_tglog]],
IrisRaidData = [[common]],
IrisSOLData = [[common]],
IsolationInfo = [[common]],
ItemFlow = [[tglog]],
ItemLimitInfo = [[cs_shop]],
ItemOperateReport = [[clientds_tglog]],
JumpConfigData = [[cs_playerinfo]],
KOLWeaponTasteConfig = [[cs_activity]],
KeyChainUsageInfo = [[zone2dsa]],
KickDownInfo = [[ds_common]],
KillAssistantInfo = [[ds_common]],
LOGIDAILABRECMD10001 = [[tglog]],
LOGIDAILABRECMD10001DEV = [[tglog]],
LOGIDAILABRECMD10002 = [[tglog]],
LOGIDAILABRECMD10002DEV = [[tglog]],
LOGIDAILABRECMD10003 = [[tglog]],
LOGIDAILABRECMD10003DEV = [[tglog]],
LOGIDAILABRECMD10021 = [[tglog]],
LOGIDAILABRECMD10021DEV = [[tglog]],
LOGIDAILABRECMD10022 = [[tglog]],
LOGIDAILABRECMD10022DEV = [[tglog]],
LOGIDAILABRECMD10023 = [[tglog]],
LOGIDAILABRECMD10023DEV = [[tglog]],
LadderArena = [[tglog]],
LanDunTraceFlow = [[tglog]],
LimitBindTicket = [[cs_shop]],
LimitSocialInfo = [[common]],
LimitSocialProp = [[cs_collection]],
LiteDownloadEvent = [[tglog]],
LiveRadioButtonFlow = [[tglog]],
LiveRadioConfig = [[cs_live]],
LiveRadioExternalLinkFlow = [[tglog]],
LiveRadioGroupConfig = [[cs_live]],
LiveRadioGroupList = [[cs_live]],
LiveRadioInterfaceNoticeFlow = [[tglog]],
LiveRadioNotice = [[cs_live]],
LiveRadioNoticeList = [[cs_live]],
LiveRadioRetentionTimeFlow = [[tglog]],
LobbyAdConfig = [[cs_solo]],
LobbyBannerInfo = [[cs_playerinfo]],
LobbyBannersBehaviorClient = [[tglog]],
LobbyTeamEndFlow = [[tglog]],
LobbyTeamInMatchFlow = [[tglog]],
LobbyTeamJoinFlow = [[tglog]],
LobbyTeamLeaderVaryFlow = [[tglog]],
LobbyTeamMenVaryFlow = [[tglog]],
LobbyTeamOptionFlow = [[tglog]],
LobbyTeamReleaseInviteFlow = [[tglog]],
LobbyTeamResponseInviteFlow = [[tglog]],
LobbyTeamShortNumCreateFlow = [[tglog]],
LobbyTeamStartFlow = [[tglog]],
LocaliaztionTextData = [[cs_patch]],
LoginAgreementStatus = [[tglog]],
LootFlow = [[ds_tglog]],
LootItemFlow = [[ds_tglog]],
LotteryBoxConfig = [[cs_lottery]],
LotteryBoxData = [[common]],
LotteryBoxFlow = [[tglog]],
LotteryBoxGroupConfig = [[cs_lottery]],
LotteryBoxGroupInfo = [[cs_lottery]],
LotteryBoxInfo = [[cs_lottery]],
LotteryBoxPropConfig = [[cs_lottery]],
LotteryBoxPropInfo = [[cs_lottery]],
LotteryBoxPropSimpleInfo = [[cs_shop]],
LotteryFlow = [[tglog]],
LotteryGetCurrencyInfo = [[cs_activity]],
LotteryInfo = [[cs_activity]],
LotteryItemConfig = [[cs_shop]],
LotteryPoolRecord = [[cs_shop]],
LotteryResultInfo = [[cs_lottery]],
LuckyNestGoods = [[cs_shop]],
MPBackJudgeFlow = [[tglog]],
MPBagArmedPresetCS = [[common]],
MPBagCS = [[common]],
MPBagUpdateFlow = [[tglog]],
MPCommanderInfo = [[cs_activity]],
MPCommanderScoreFlow = [[tglog]],
MPData = [[common]],
MPDepositNewbieWeaponFlow = [[tglog]],
MPDepositPropChangeFlow = [[tglog]],
MPDepositPropRule = [[cs_mpdeposit]],
MPDropMandelBrickFlow = [[tglog]],
MPGrowthViewClickFlow = [[tglog]],
MPLevelDesc = [[ds_common]],
MPLevels = [[ds_common]],
MPLoadingPointFlow = [[tglog]],
MPNumeralFlow = [[tglog]],
MPPresetCS = [[ds_common]],
MPPropPosCS = [[ds_common]],
MPRankMajorSummary = [[common]],
MPRankReset = [[tglog]],
MPRankSummary = [[common]],
MPRedeployExpertBagFlow = [[ds_tglog]],
MPRoundFlow = [[tglog]],
MPSeasonInfo = [[common]],
MPSkinComponents = [[ds_common]],
MPVehicleExpFlow = [[tglog]],
MPVehicleModificationFlow = [[tglog]],
MPVehicleUnlockFlow = [[tglog]],
MPWeaponSettlement = [[ds_common]],
MPWeaponStarChangeFlow = [[tglog]],
MPWeaponStatistics = [[ds_common]],
MPWeaponStoreCS = [[common]],
MailAttachment = [[cs_mail]],
MailAttachmentsValueFlow = [[tglog]],
MailDataFlow = [[tglog]],
MailHyperlink = [[cs_mail]],
MailUnreadInfo = [[cs_mail]],
MailUpdateInfo = [[cs_mail]],
MakeDrinkInfo = [[cs_activity]],
MallBHDItemConfig = [[cs_shop]],
MallBuyFlow = [[tglog]],
MallExchangeInfo = [[common]],
MallExchangeRule = [[common]],
MallGiftPropConfig = [[cs_shop]],
MallLabelProps = [[common]],
MallMysteryShopBuyFlow = [[tglog]],
MallMysteryShopFetchDataFlow = [[tglog]],
MallMysteryShopItem = [[common]],
MallMysteryShopRaffleFlow = [[tglog]],
MallPropGridPage = [[common]],
MallPropInfo = [[common]],
MallRecycleFlow = [[tglog]],
MandelRewardInfo = [[cs_lottery]],
MapBoardActTimeTuple = [[cs_solo]],
MapBoardEasterEggFlow = [[tglog]],
MapBoardInfo = [[cs_solo]],
MapBoardInfoDesc = [[cs_solo]],
MapBoardRaidInfo = [[cs_solo]],
MapBoardTOD = [[cs_solo]],
MapInfo = [[cs_activity]],
MarkChangeInfo = [[cs_playerinfo]],
MarkPropInfo = [[ds_common]],
MarketAuditFlow = [[tglog]],
MarketAuditResultFlow = [[tglog]],
MarketAveragePriceChangeFlow = [[tglog]],
MarketBuyFailedFlow = [[tglog]],
MarketBuyPropFlow = [[tglog]],
MarketEnterCountFlow = [[tglog]],
MarketGuidePriceChangeFlow = [[tglog]],
MarketOrderShadowFlow = [[tglog]],
MarketPlayerOrderFlow = [[tglog]],
MarketPlayerSearchPropFlow = [[tglog]],
MarketPreBuyOrderDetail = [[cs_market]],
MarketPreBuyOrderFlow = [[tglog]],
MarketPreBuyOrderStatis = [[tglog]],
MarketPreBuySummaryOrderFlow = [[tglog]],
MarketPropDepositChangeFlow = [[tglog]],
MarketPropStatFlow = [[tglog]],
MarketPulloffPropFlow = [[tglog]],
MarketRPCResultStaticFlow = [[tglog]],
MarketRollBackMoney = [[tglog]],
MarketSellPropFlow = [[tglog]],
MarketShadowPriceChangeFlow = [[tglog]],
MarketSoldFlow = [[tglog]],
MarketWithdrawFlow = [[tglog]],
MatchAreaRtt = [[cs_idcchosen]],
MatchAreaSetFlow = [[ds_tglog]],
MatchArenaSettlementFlow = [[tglog]],
MatchBaseRecord = [[common]],
MatchBaseRecordArena = [[common]],
MatchBaseRecordIrisRaid = [[common]],
MatchBaseRecordIrisSOL = [[common]],
MatchBaseRecordMP = [[common]],
MatchBaseRecordMPCamp = [[common]],
MatchBaseRecordMPTeam = [[common]],
MatchBaseRecordMPTournament = [[common]],
MatchBotAllocInfo = [[cs_robot]],
MatchCourse = [[tglog]],
MatchEndInfo = [[ds_tglog]],
MatchEquipIsolationInfo = [[tglog]],
MatchFlow = [[tglog]],
MatchGateAllocFlow = [[tglog]],
MatchIngShowInfo = [[cs_matchinginfo]],
MatchLanguageFlow = [[tglog]],
MatchModeInfo = [[ds_common]],
MatchPlayerInfo = [[common]],
MatchRoomInfo = [[tglog]],
MatchRoomPlayerPlatInfo = [[cs_matchroom]],
MatchRoomRandEventFlow = [[tglog]],
MatchRoomTeamMemberInfo = [[ds_common]],
MatchSOLDropCounter = [[ds_common]],
MatchSOLDropFlow = [[ds_tglog]],
MatchSOLPlayerBackFlow = [[tglog]],
MatchSolCourseSelectFlow = [[tglog]],
MatchSolSchemeModelFlow = [[tglog]],
MatchTickFlow = [[ds_tglog]],
MatchTitleCounter = [[common]],
MatchingAILabWarmInfo = [[tglog]],
MatchingCreateTeam = [[tglog]],
MatchingInfo = [[tglog]],
MatchingSchemeInfo = [[tglog]],
MeleeChangeFlow = [[tglog]],
MeleeSkinDescInfo = [[cs_collection]],
MerChantGifts = [[cs_mall]],
Merchant = [[common]],
MerchantChange = [[common]],
MerchantGiftInfo = [[common]],
MidasAccountFlow = [[tglog]],
MidasChargeFlow = [[tglog]],
MidasMonthFlow = [[tglog]],
MidasPayFlow = [[tglog]],
MidasRechargeChannelInfo = [[cs_pay]],
MidasRechargeProductItem = [[cs_pay]],
MidasResulinfo = [[cs_pay]],
MidasShippingFlow = [[tglog]],
MidasToken = [[common]],
ModelDisplayFlow = [[tglog]],
ModuleUnlockFlow = [[tglog]],
MoneyFlow = [[tglog]],
MossAIClientReport = [[tglog]],
MossCalculateInfo = [[cs_activity]],
MossConstructInfo = [[cs_activity]],
MossDescInfo = [[cs_activity]],
MpMatchRecords = [[tglog]],
MpRadarData = [[common]],
MpRankNumeral = [[common]],
MsdkCheckInfoResult = [[cs_account]],
MsdkLoginCheckInfo = [[tglog]],
MultiDsTraceFlow = [[tglog]],
MysticalAppearanceInfo = [[ds_common]],
MysticalPendantFlow = [[tglog]],
MysticalPendantInfo = [[ds_common]],
MysticalPendantLogout = [[tglog]],
MysticalPendantShowInfo = [[ds_common]],
MysticalPendantSuitInfo = [[ds_common]],
MysticalSkinFlow = [[tglog]],
MysticalSkinInfo = [[ds_common]],
MysticalSkinLogout = [[tglog]],
NetbarPrivFlow = [[tglog]],
NetworkNakStat = [[tglog]],
NewDSBulletDamageInfo = [[ds_tglog]],
NewDSBulletRouletee = [[ds_tglog]],
NewDSCharacterArmorTakeHealthDebuff = [[ds_tglog]],
NewShopBuyFlow = [[tglog]],
NewbieChallengeFinalAward = [[cs_activity]],
NewbieChallengeInfo = [[cs_activity]],
NewbieGoalTypesClassifyConfig = [[cs_activity]],
NumeralActivityData = [[ds_common]],
NumeralActivityObjective = [[ds_common]],
NumeralActivityPasswordBox = [[ds_common]],
ODTdmCamp = [[ds_common]],
OdTdmPlayer = [[ds_common]],
OnlinecntFlow = [[tglog]],
OpExeInGameUsage = [[ds_tglog]],
OpenLotteryItemRecord = [[cs_shop]],
OptPropPrice = [[common]],
OutfitCheckEntry = [[common]],
OutfitCheckItems = [[common]],
PLayerFightEvent = [[ds_tglog]],
PVEMissionInfo = [[cs_pve]],
PVEMissionReward = [[cs_pve]],
PageJumpFlow = [[tglog]],
PagesMayWithDetailClickFlow = [[tglog]],
PandoraInfo = [[cs_activity]],
PasswordBox = [[cs_activity]],
PasswordDigit = [[cs_activity]],
PatchQuickPatch = [[cs_patch]],
PaymentRecord = [[common]],
PcSettingsInfo = [[tglog]],
PerkInfo = [[ds_common]],
PlayerAIScore = [[tglog]],
PlayerAppearance = [[cs_playerinfo]],
PlayerArmoryClientSelectFlow = [[tglog]],
PlayerArmoryData = [[cs_quest]],
PlayerArmoryObjective = [[cs_quest]],
PlayerBasicInfo = [[cs_playerinfo]],
PlayerBeginMatch = [[tglog]],
PlayerBlackSiteInteractionFlow = [[tglog]],
PlayerBoxSearchBebavior = [[clientds_tglog]],
PlayerCDPLabelFlow = [[tglog]],
PlayerCancleMatching = [[tglog]],
PlayerCarryoutProps = [[common]],
PlayerDeadbodySearchBebavior = [[clientds_tglog]],
PlayerDepositSnapShoot = [[tglog]],
PlayerDepositSummary = [[tglog]],
PlayerEnterEvacuationRecords = [[ds_tglog]],
PlayerExpFlow = [[tglog]],
PlayerExpProduceFlow = [[tglog]],
PlayerFriendsList = [[tglog]],
PlayerGuideCompensationFlow = [[ds_tglog]],
PlayerHalfJoin = [[tglog]],
PlayerHalfJoinMatchNtf = [[cs_match]],
PlayerInfoSubscriptionFlow = [[tglog]],
PlayerInfoSubscriptionServerFlow = [[tglog]],
PlayerJoinDsStatus = [[tglog]],
PlayerJoinMatchNtf = [[cs_matchroom]],
PlayerKickFlow = [[tglog]],
PlayerLabel = [[common]],
PlayerLeftEvacuationRecords = [[ds_tglog]],
PlayerLimitMallItem = [[common]],
PlayerLobbyNetworkStatus = [[tglog]],
PlayerLogin = [[tglog]],
PlayerLoginIPLimit = [[tglog]],
PlayerLoginNetbarRights = [[tglog]],
PlayerLogout = [[tglog]],
PlayerLootBehavior = [[clientds_tglog]],
PlayerLostItems = [[zone2dsa]],
PlayerMachineMemory = [[tglog]],
PlayerMatchError = [[tglog]],
PlayerMatchFlow = [[tglog]],
PlayerMatchRecord = [[common]],
PlayerMatchingFail = [[tglog]],
PlayerMatchingSucc = [[tglog]],
PlayerMovementStat = [[ds_tglog]],
PlayerNetworkOneConnection = [[ds_tglog]],
PlayerNetworkOverallView = [[ds_tglog]],
PlayerNetworkStat = [[ds_tglog]],
PlayerObjectiveExtraData = [[ds_common]],
PlayerOutfitApplyFlow = [[tglog]],
PlayerOutfitModifyAGameFlow = [[tglog]],
PlayerOutfitViewFlow = [[tglog]],
PlayerPreRegisterFlow = [[tglog]],
PlayerPrivacyData = [[cs_playerinfo]],
PlayerQuestData = [[ds_common]],
PlayerQuestObjectiveData = [[ds_common]],
PlayerRankHistoricalRecordInfo = [[common]],
PlayerRankHistoricalSeasonInfo = [[common]],
PlayerRankHistoricalTitleInfo = [[common]],
PlayerRankPercentageInfo = [[common]],
PlayerRegister = [[tglog]],
PlayerRegisterTssFailed = [[tglog]],
PlayerRttData = [[ds_common]],
PlayerSCAVInfo = [[cs_solo]],
PlayerSafehouseInteractionFlow = [[tglog]],
PlayerSafehouseMoveFlow = [[tglog]],
PlayerSimpleInfo = [[common]],
PlayerStat = [[ds_tglog]],
PlayerStateSimpleInfo = [[cscpp_online]],
PlayerStructedGuideDisplayFlow = [[tglog]],
PlayerStructedGuideResetFlow = [[tglog]],
PlayerStructedGuideTriggerFlow = [[tglog]],
PlayerUsedVoiceAudioFlow = [[tglog]],
PlayerWeaponTweakReportFlow = [[tglog]],
PoiInfo = [[ds_common]],
PoiStayInfo = [[ds_common]],
PoolStayInfo = [[tglog]],
PopupClickFlow = [[tglog]],
PositionChange = [[common]],
PositionEquipmentBind = [[cs_armedforce]],
PraiseItem = [[common]],
PresentHotRecommendationPropRecord = [[cs_shop]],
PresentItem = [[cs_pay]],
PresentLimitDesc = [[cs_shop]],
PresentMallGiftPropRecord = [[cs_shop]],
PresetEquipmentPos = [[tglog]],
PresetMatchBeginFlow = [[tglog]],
PresetModuleUse = [[tglog]],
PresetPriceFluctuationFlow = [[tglog]],
PresetTip = [[tglog]],
PresetUsage = [[tglog]],
ProfileSeasonInfo = [[common]],
Prompt = [[cs_playerinfo]],
PromptBehaviorClientFlow = [[tglog]],
PropBuyInfo = [[cs_reconciliation]],
PropChange = [[common]],
PropDropFlow = [[ds_tglog]],
PropGoldEgg = [[ds_common]],
PropGridPage = [[common]],
PropGuidePrice = [[ds_common]],
PropInfo = [[ds_common]],
PropLocation = [[ds_common]],
PropPrice = [[common]],
PropRecord = [[ds_common]],
PropSource = [[ds_common]],
PullOffOrderInfo = [[cs_market]],
QualityChange = [[common]],
QualityChangeInfo = [[cs_attribute]],
Quest2DSQuestAcceptNtf = [[ds_quest]],
Quest2DSQuestCompleteNtf = [[ds_quest]],
Quest2DSQuestSendRewardNtf = [[ds_quest]],
QuestChangeFlow = [[ds_tglog]],
QuestConsumePropFlow = [[ds_tglog]],
QuestDSGameState = [[ds_common]],
QuestFinishFlow = [[tglog]],
QuestLineSeason = [[cs_quest]],
QuestObjectiveInfo = [[cs_quest]],
QuestPlayerCollectorData = [[cs_quest]],
QuestRewardFlow = [[tglog]],
QuestSubmitEntry = [[cs_quest]],
QuestVarEntry = [[ds_common]],
RaidEvaluatePrizeInfo = [[cs_settlement]],
RaidKillBotFlow = [[tglog]],
RaidKillEnemyInfo = [[ds_common]],
RaidMapItem = [[cs_season]],
RaidMatchRecords = [[tglog]],
RaidPlayerData = [[common]],
RaidRewardDailyInfo = [[cs_solo]],
RaidRoundFlow = [[tglog]],
RaidRoundFlowNew = [[tglog]],
RaidSeasonInfo = [[common]],
RaidSettlementData = [[common]],
RandEventFlow = [[tglog]],
RankBoardAwardFlow = [[tglog]],
RankBoardExposureFlow = [[tglog]],
RankBoardPhotoFlow = [[tglog]],
RankBoardPlayerFlow = [[tglog]],
RankBoardRegionChangeFlow = [[tglog]],
RankCurrencyChange = [[common]],
RankCurrencyResult = [[common]],
RankEvent = [[common]],
RankHandBookStateInfo = [[common]],
RankHeroSummary = [[common]],
RankItem = [[cs_rank]],
RankList = [[cs_rank]],
RankListItemFlow = [[tglog]],
RankMajorSummary = [[common]],
RankMatchMallProp = [[cs_season]],
RankMatchScoreChange = [[common]],
RankShield = [[common]],
RankSingleHandBookData = [[common]],
RankSummary = [[common]],
ReadEquipPoolFlow = [[tglog]],
RecentCoPlayerInfo = [[common]],
RechargeConfig = [[cs_pay]],
RechargeInfo = [[cs_pay]],
RecommendPreset = [[tglog]],
RecoveryAmountChange = [[common]],
RecoveryAmountChangeFlow = [[ds_tglog]],
RecruitmentDisplayInfo = [[cs_recruitment]],
RecruitmentFilter = [[cs_recruitment]],
RecruitmentInfo = [[cs_chat]],
RecruitmentPlayerInfo = [[cs_recruitment]],
RecruitmentUpdateKey = [[cs_recruitment]],
RedDotInfo = [[cs_shop]],
RefreshMandelInventoryFlow = [[tglog]],
ReleaseRecruitFlow = [[tglog]],
RelinkInfo = [[cs_activity]],
RelinkMaterialInfo = [[cs_activity]],
RepCondition = [[common]],
RepLevelConfig = [[common]],
ReportInfo = [[cs_hope]],
ReportInvisibleFlow = [[ds_tglog]],
ReportMonitorLabel = [[zone2dsa]],
ReportMonitorMetric = [[zone2dsa]],
ReputationFlow = [[tglog]],
ReputationHistoryRecord = [[cs_playerinfo]],
ReputationLogoutFlow = [[tglog]],
ReputationStarFlow = [[tglog]],
ReputationStarHistoryRecord = [[cs_playerinfo]],
RequestTable = [[cs_resource]],
RescueInfo = [[ds_common]],
ReturnInfo = [[cs_activity]],
ReturnNewContentPartInfo = [[cs_activity]],
ReturningUserFlow = [[tglog]],
ReturningUserTagTimeoutFlow = [[tglog]],
RobotAIFlow = [[tglog]],
RobotBaseInfo = [[cs_robot]],
RobotJoinMatchNtf = [[cs_robot]],
RobotWeapon = [[cs_robot]],
RoleAttrValue = [[common]],
RoleInformationCareerFlow = [[tglog]],
RoleInformationSeasonFlow = [[tglog]],
RoleLoadValue = [[ds_common]],
RoleQualityCard = [[common]],
RoleQualityConfig = [[cs_attribute]],
RoleQualityUsage = [[cs_attribute]],
RollingNotice = [[cs_chat]],
RoomAttr = [[ds_common]],
RoomBaseInfo = [[cs_room]],
RoomInfo = [[cs_room]],
RoomMatchMemberInfo = [[cs_room]],
RoomMatchModeInfo = [[cs_room]],
RoomMatchRoundMemberInfo = [[cs_room]],
RoomMatchRoundTeamInfo = [[cs_room]],
RoomMatchTeamInfo = [[cs_room]],
RoomMemberInfo = [[cs_room]],
RoomMidSupplyFlow = [[tglog]],
RoomParam = [[cs_room]],
RoomParamKeyValue = [[cs_room]],
RoomTdmParam = [[cs_room]],
RoomTeamInfo = [[cs_room]],
RoomsvrNumeral = [[ds_common]],
RoomsvrParam = [[ds_common]],
RoundEquipUseFlow = [[tglog]],
RoundFlow = [[tglog]],
RoundtripAddr = [[common]],
RoundtripIDC = [[common]],
RoundtripNodeList = [[tglog]],
RoundtripPkg = [[ds_roundtrip]],
RoundtripPkgHead = [[ds_roundtrip]],
RoundtripReportTime = [[tglog]],
RoundtripTime = [[common]],
RttBestIdc = [[ds_common]],
RuleItem = [[cs_pay]],
SBCAward = [[cs_activity]],
SBCInfo = [[cs_activity]],
SCAV = [[cs_solo]],
SOLDepositLogin = [[tglog]],
SOLDepositLogout = [[tglog]],
SOLGuideInstructionInfo = [[ds_common]],
SOLHeroFlow = [[tglog]],
SOLItemSysJudgeOperFlow = [[tglog]],
SOLPlayerMatchCalInfo = [[common]],
SOLRadarData = [[common]],
SOLRankReset = [[tglog]],
SOLSeasonInfo = [[common]],
SOLTeamFlow = [[ds_tglog]],
SSAchieveSetSteamProgressReq = [[cs_achieve]],
SSAchieveSetSteamProgressRes = [[cs_achieve]],
SSCurrencyChangeNtf = [[common]],
SSCurrencyExchangeReq = [[cs_currency]],
SSCurrencyExchangeRes = [[cs_currency]],
SSHelloReq = [[common]],
SSQuestCheckQuestsCompletedReq = [[s_quest]],
SSQuestCheckQuestsCompletedRes = [[s_quest]],
SSQuestDeletePlayerDataReq = [[s_quest]],
SSQuestDeletePlayerDataRes = [[s_quest]],
SSQuestDumpPlayerQuestDBReq = [[s_quest]],
SSQuestDumpPlayerQuestDBRes = [[s_quest]],
SSQuestForceAcceptAndFinishAllReq = [[s_quest]],
SSQuestForceAcceptAndFinishAllRes = [[s_quest]],
SSQuestForceAcceptReq = [[s_quest]],
SSQuestForceAcceptRes = [[s_quest]],
SSQuestForceFailQuestReq = [[s_quest]],
SSQuestForceFailQuestRes = [[s_quest]],
SSQuestForceFinishAllAcceptedReq = [[s_quest]],
SSQuestForceFinishAllAcceptedRes = [[s_quest]],
SSQuestForceFinishReq = [[s_quest]],
SSQuestForceFinishRes = [[s_quest]],
SSQuestForceSetObjectiveValueReq = [[s_quest]],
SSQuestForceSetObjectiveValueRes = [[s_quest]],
SSQuestForceSetQuestObjectiveSpentSecondsReq = [[s_quest]],
SSQuestForceSetQuestObjectiveSpentSecondsRes = [[s_quest]],
SSQuestForceSetQuestVarReq = [[s_quest]],
SSQuestForceSetQuestVarRes = [[s_quest]],
SSQuestGMGetQuestUnlockStampReq = [[s_quest]],
SSQuestGMGetQuestUnlockStampRes = [[s_quest]],
SSQuestGMGetRootLineDataReq = [[s_quest]],
SSQuestGMGetRootLineDataRes = [[s_quest]],
SSQuestGetCompleteInfoReq = [[s_quest]],
SSQuestGetCompleteInfoRes = [[s_quest]],
SSQuestGetNumeralDataReq = [[s_quest]],
SSQuestGetNumeralDataRes = [[s_quest]],
SSQuestGetQuestsStateReq = [[s_quest]],
SSQuestGetQuestsStateRes = [[s_quest]],
SSQuestTaskCompletedNtf = [[s_quest]],
SSShopAddOpenCoreCountReq = [[cs_shop]],
SSShopAddOpenCoreCountRes = [[cs_shop]],
SSTeamGetLeaderInfoReq = [[cs_team]],
SSTeamGetLeaderInfoRes = [[cs_team]],
SSUpdatePlayerTotalPropertyDataPullNtf = [[s_quest]],
SSUpdateResDBNtf = [[common]],
SafehouseBlackSiteInteractiveFlow = [[tglog]],
SafehouseBlueprintSlotInfo = [[cs_safehouse]],
SafehouseClickEventFlow = [[tglog]],
SafehouseDeviceBuff = [[cs_safehouse]],
SafehouseDeviceFormulaCfg = [[cs_safehouse]],
SafehouseDeviceInfo = [[cs_safehouse]],
SafehouseDeviceLevel = [[common]],
SafehouseDeviceUpgradeCfg = [[cs_safehouse]],
SafehouseFormulaFavoriteFlow = [[tglog]],
SafehouseInfoFlow = [[tglog]],
SafehouseItemInfo = [[cs_safehouse]],
SafehouseLoopEfficiencyInfo = [[cs_safehouse]],
SafehouseNumeralInfo = [[ds_common]],
SafehouseProbItemInfo = [[cs_safehouse]],
SafehouseProduceFlow = [[tglog]],
SafehouseProduceInfo = [[cs_safehouse]],
SafehousePropChangeFlow = [[tglog]],
SafehousePropFlow = [[tglog]],
SafehousePropLostFlow = [[tglog]],
SafehouseRecycleItem = [[cs_safehouse]],
SafehouseReplaceFormula = [[cs_safehouse]],
SafehouseSubDeviceInfo = [[cs_safehouse]],
SafehouseTerminateFlow = [[tglog]],
SafehouseUnlockSystemFlow = [[tglog]],
SafehouseUpgradeNewFlow = [[tglog]],
SafehouseVisitor = [[cs_safehouse]],
SafehouseVisitorFlow = [[tglog]],
SeamlessEntryOperate = [[clientds_tglog]],
SeasonChangeInfo = [[common]],
SeasonFightRecord = [[tglog]],
SeasonInfo = [[common]],
SeasonMPTournamentClientFlow = [[tglog]],
SeasonMPTournamentFlow = [[tglog]],
SeasonSOLRankClientFlow = [[tglog]],
SecAntiDataFlow = [[tss_tglog]],
SecAttackFlow = [[tss_tglog]],
SecChatFlow = [[tss_tglog]],
SecEditFlow = [[tss_tglog]],
SecFriendFlow = [[tss_tglog]],
SecHurtFlow = [[tss_tglog]],
SecItemFlow = [[tss_tglog]],
SecLogin = [[tss_tglog]],
SecLogout = [[tss_tglog]],
SecMatchFlow = [[tss_tglog]],
SecMoneyFlow = [[tss_tglog]],
SecMoveReport = [[ds_tglog]],
SecPaymentFlow = [[tglog]],
SecRankListFlow = [[tss_tglog]],
SecRegister = [[tss_tglog]],
SecReportFlow = [[tss_tglog]],
SecRoundEndFlow = [[tss_tglog]],
SecRoundStartFlow = [[tss_tglog]],
SecSafeStatusFlow = [[tss_tglog]],
SecSocialFlow = [[tss_tglog]],
SecTLogHeadComm = [[ds_common]],
SecTLogHeadDevice = [[ds_common]],
SecTLogHeadRole = [[ds_common]],
SecTaskFlow = [[tss_tglog]],
SecTriggerClickFlow = [[ds_tglog]],
SecWatchingFlow = [[tss_tglog]],
SecurityCompensatePriceFlow = [[tglog]],
SecurityCompensatePropsFlow = [[tglog]],
SetMandelBoxUpFlow = [[tglog]],
SetUpMobile = [[tglog]],
SettingKeyValue = [[cs_setting]],
SettingSensitivity = [[tglog]],
SettingsInfo = [[tglog]],
SettlementDepositItemFlow = [[tglog]],
SettlementGuideVideoClientReport = [[tglog]],
SettlementMailItemFlow = [[tglog]],
SettlementNtf = [[ds_common]],
SettlementReqFlow = [[tglog]],
ShareRewardFlow = [[tglog]],
ShareRewardInfo = [[cs_playerinfo]],
ShopBundleItem = [[cs_shop]],
ShopBuyFlow = [[tglog]],
ShopBuyRecord = [[common]],
ShopConfig = [[cs_shop]],
ShopEnterMandelBoxUpUIFlow = [[tglog]],
ShopEnterMandelOpenUIFlow = [[tglog]],
ShopGoodsInfo = [[cs_shop]],
ShopLotteryPool = [[cs_shop]],
ShopLotteryRewardInfo = [[cs_shop]],
ShopLuckyNestClickFlow = [[tglog]],
ShopLuckyNestPageFlow = [[tglog]],
ShopLuckyNestRaffleFlow = [[tglog]],
ShopPaymentInfo = [[cs_shop]],
ShopRoleSkinLotteryFlow = [[tglog]],
ShopSetMandelBoxUpUIFlow = [[tglog]],
ShopSpecialBackRefreshItemFlow = [[tglog]],
ShowParameter = [[cs_matchinginfo]],
SimpleMilestoneInfo = [[cs_activity]],
SimpleMilestoneScoreDesc = [[cs_activity]],
SingleMatchAchievementCounter = [[common]],
SinglePlayerAuxData = [[common]],
SnsFlow = [[tglog]],
SocialAccountsFlow = [[tglog]],
SocialAvatarUseFlow = [[tglog]],
SolCurrencyResultFlow = [[tglog]],
SolMandelBrickAnalysisData = [[ds_tglog]],
SolMatchRecords = [[tglog]],
SolMatchRoomInfo = [[tglog]],
SolRoomTeamShowInfo = [[cs_matchroom]],
SolSettlementData = [[ds_common]],
SpatialPrecomputedVisibility = [[ds_tglog]],
SpecialBackItemInfo = [[cs_shop]],
SpecialBackRoundInfo = [[cs_shop]],
SpecialItemInfo = [[cs_shop]],
SpecificModeData = [[common]],
StarFireAreaConfig = [[cs_activity]],
StarFireInfo = [[cs_activity]],
StarFireTaskInfo = [[cs_activity]],
StartFireDailyInfo = [[cs_activity]],
StateChangeFlow = [[tglog]],
StealthStatusFlow = [[tglog]],
SteamAchievementItem = [[cs_achieve]],
StoreBannerEventFlow = [[tglog]],
StorePaddedGoodsEvent = [[tglog]],
StoreRechargePageEventFlow = [[tglog]],
StoreVideoEventFlow = [[tglog]],
StoreViewDetailEventFlow = [[tglog]],
StoreViewPageFlow = [[tglog]],
StreakInfo = [[common]],
StreakInfoTopRecord = [[common]],
StreakTopRecord = [[common]],
StrongholdInfo = [[ds_common]],
StrongholdReward = [[ds_common]],
StuPrivFlow = [[tglog]],
StyleCheckItems = [[common]],
Summary = [[common]],
SupplyConfigInfo = [[common]],
SupplyNumsInfo = [[common]],
SurveyBossExchangeInfo = [[cs_activity]],
SurveyBossInfo = [[cs_activity]],
SwitchAccountRewards = [[cs_switch]],
SwitchAccountUnlockItems = [[cs_switch]],
SwitchConditionParam = [[common]],
SwitchModuleStatus = [[cs_switch]],
SwitchSystemUnlockInfo = [[cs_switch]],
SyncPlayer = [[ds_tglog]],
SyncPlayerInfoFlow = [[tglog]],
SyncPlayerRespond = [[tglog]],
TDMAILabSectorStatis = [[ds_tglog]],
TDMBagArmedPresetDS = [[ds_common]],
TDMBagDS = [[ds_common]],
TDMBattleFieldCommanderModeVoteFlow = [[ds_tglog]],
TDMBattleFieldCommanderPlayerInfoFlow = [[ds_tglog]],
TDMBreakthroughCommanderFastRedeployFlow = [[ds_tglog]],
TDMBreakthroughRedeployLongStayGuideFlow = [[ds_tglog]],
TDMBreakthroughReviveGiveUpGuideFlow = [[ds_tglog]],
TDMBreakthroughScoreFlow = [[ds_tglog]],
TDMCamp = [[ds_common]],
TDMCarryoutBag = [[ds_common]],
TDMDSRoundFlow = [[ds_tglog]],
TDMDsDetail = [[ds_common]],
TDMDynamicNotGoodFlow = [[tglog]],
TDMDynamicPoolInfo = [[tglog]],
TDMEachSectorPlayerFlow = [[tglog]],
TDMGameMVPEvaluated = [[ds_common]],
TDMHitBackActivity = [[ds_tglog]],
TDMMapBoardInfoDesc = [[cs_solo]],
TDMMatchTickFlow = [[ds_tglog]],
TDMMatchingAILabWarmInfo = [[tglog]],
TDMMidWithdrawalFlow = [[ds_tglog]],
TDMMvp = [[ds_common]],
TDMNumeral = [[ds_common]],
TDMNumeralFlow = [[tglog]],
TDMNumeralProp = [[ds_common]],
TDMPlayer = [[ds_common]],
TDMPlayerFlow = [[ds_tglog]],
TDMPlayerMVPEvaluated = [[ds_common]],
TDMPlayerMedalScorePointFlow = [[ds_tglog]],
TDMPlayerPointFlow = [[ds_tglog]],
TDMPointWeight = [[ds_common]],
TDMRankSkillFlow = [[ds_tglog]],
TDMRedeployBagFlow = [[ds_tglog]],
TDMReportAILabBattleInfo = [[tglog]],
TDMRescueTLogFlow = [[ds_tglog]],
TDMSectorBattleAssistFlow = [[ds_tglog]],
TDMSectorBattleKillFlow = [[ds_tglog]],
TDMSectorCarrierDestroyedFlow = [[ds_tglog]],
TDMSectorCarrierOccupiedFlow = [[ds_tglog]],
TDMSectorCarrierRefreshFlow = [[ds_tglog]],
TDMSectorCarrierStatusFlow = [[ds_tglog]],
TDMSectorDeployFlow = [[ds_tglog]],
TDMSectorFlow = [[ds_tglog]],
TDMSectorOccupiedStatusFlow = [[ds_tglog]],
TDMSectorRedeploySwitchArmItemFlow = [[ds_tglog]],
TDMSectorSupportFlow = [[ds_tglog]],
TDMSettlementAccountExp = [[ds_common]],
TDMSettlementData = [[ds_common]],
TDMStarFireActivityQuest = [[ds_tglog]],
TDMStrongholdInfo = [[ds_common]],
TDMTacticalConquestEvolutionRebornFlow = [[ds_tglog]],
TDMTeam = [[ds_common]],
TDMTournamentScore = [[ds_common]],
TDMTournamentScoreFlow = [[tglog]],
TDMVehicleAddExp = [[ds_common]],
TDMVehicleExp = [[ds_common]],
TDMWeaponDesign = [[ds_common]],
TDMWeaponExpFlow = [[tglog]],
TDMWinterActivity = [[tglog]],
TaskFlow = [[tglog]],
TaskGoalItem = [[common]],
TdmBreakthroughAIFireDataFlow = [[ds_tglog]],
TdmBreakthroughDataForAIFlow = [[ds_tglog]],
TdmCommanderScore = [[ds_common]],
TdmMandelBrickDrop = [[cs_settlement]],
TdmMatchRoomInfo = [[tglog]],
TdmRebalanceCampTagFlow = [[tglog]],
TdmRebalanceGroupTagFlow = [[tglog]],
TdmRoomCampState = [[tglog]],
TdmTeamTagInfo = [[ds_common]],
TeamBeginMatch = [[tglog]],
TeamInfo = [[cs_team]],
TeamMemberInfo = [[cs_team]],
TgLogEntry = [[ds_common]],
ThemeBundleTimeConfig = [[cs_shop]],
ThumbFlow = [[tglog]],
TimeInterval = [[cs_solo]],
TournamentRankDoubleInfo = [[ds_common]],
TournamentRankShield = [[ds_common]],
TraceLog = [[tglog]],
TradeChange = [[common]],
TraderPageConfig = [[cs_mall]],
TssReportCategoryEntry = [[cs_tss]],
TssReportConfig = [[cs_tss]],
TssReportReason = [[cs_tss]],
TssReportSceneEntry = [[cs_tss]],
TssTLogHeader = [[ds_common]],
TssTLogsPkg = [[common]],
UGCJudgeFlow = [[tglog]],
UObjectMonitor = [[clientds_tglog]],
UnEquipPosition = [[cs_weaponassembly]],
UnicodeCharCountItem = [[cs_account]],
UnicodeWhiteListRangeItem = [[cs_account]],
UnitPoolStayInfo = [[tglog]],
UnlockCostItem = [[cs_weaponassembly]],
UnlockDepartment = [[common]],
UnlockStyleCondition = [[cs_weaponassembly]],
UnlockedDigit = [[ds_common]],
UnmarkByReplaceInfo = [[cs_quest]],
UsePropCmd = [[common]],
Vehicle = [[ds_common]],
VehicleDeadBySelfInfoFlow = [[ds_tglog]],
VehicleDeadInfoFlow = [[ds_tglog]],
VehicleInGameDataFlow = [[ds_tglog]],
VehicleModificationRule = [[ds_common]],
VehiclePart = [[ds_common]],
VehicleSlot = [[ds_common]],
VehicleSlotCfg = [[ds_common]],
VehicleSlotPartCfg = [[ds_common]],
VehicleUsagePreferenceFlow = [[ds_tglog]],
VehicleWeaponKillInfoFlow = [[tglog]],
VehicleWeaponUsagePreferenceFlow = [[ds_tglog]],
ViewExpertSkillInfoFlow = [[tglog]],
VipLevelFlow = [[tglog]],
WSharingCodeDesc = [[cs_weaponassembly]],
WarmResultInfo = [[tglog]],
WatchOrder = [[cs_market]],
WeaponApplySkinCmd = [[common]],
WeaponAssemblyPointInfo = [[cs_weaponassembly]],
WeaponAttrInfo = [[ds_common]],
WeaponBlueprintUnlockFlow = [[tglog]],
WeaponChange = [[ds_common]],
WeaponCheckItems = [[common]],
WeaponChoiceFlow = [[ds_tglog]],
WeaponClickFlow = [[tglog]],
WeaponComponentFlow = [[tglog]],
WeaponComponents = [[cs_mpdeposit]],
WeaponDataFlow = [[ds_tglog]],
WeaponDeliveryInfo = [[cs_activity]],
WeaponDetailPageClickFlow = [[tglog]],
WeaponExpAdd = [[ds_common]],
WeaponExpCardFlow = [[tglog]],
WeaponExpSettlementFlow = [[tglog]],
WeaponHarmFlow = [[ds_tglog]],
WeaponInfo = [[ds_common]],
WeaponKillVehicleInfoFlow = [[ds_tglog]],
WeaponSkinApplyFlow = [[tglog]],
WeaponSkinInfo = [[ds_common]],
WeaponSkinSetup = [[ds_common]],
WeaponStarInfo = [[ds_common]],
WeaponStyleInfo = [[ds_common]],
WeaponUnlockedComps = [[cs_mpdeposit]],
WorldActorContent = [[ds_common]],
WorldActorInfo = [[ds_common]],
WorldCharRoomCreateFlow = [[tglog]],
WorldCharRoomInfoFlow = [[tglog]],
WorldChatRoomInfo = [[common]],
XPPChangeInfo = [[ds_tglog]],
XunYouMailFlow = [[tglog]],
XunYouSpeedFlow = [[tglog]],
XunYouWindowsFlow = [[tglog]],
ZKInstruction = [[cs_hope]],
}
return mapping
