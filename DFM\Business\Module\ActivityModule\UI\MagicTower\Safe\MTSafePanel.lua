----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------


--- @class MTSafePanel : LuaUIBaseView
local MTSafePanel = ui("MTSafePanel")
local InputSummaryItemHD = require "DFM.Business.Module.CommonBarModule.UI.BottomBarHD.InputSummaryItemHD"
local ActivityLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"

function MTSafePanel:Ctor()
    --- 解锁奖励
    self._wtItemGridBox = UIUtil.WndScrollGridBox(self, "DFScrollGridBox_0", self._OnGetItemCount, self._OnProcessItemWidget, self._OnProcessItemFinished)

    self._wtGetTipBtn = self:Wnd("wtAcceptBtn_PC", InputSummaryItemHD)
    self._wtEscBtn = self:Wnd("wtRefuseBtn_PC", InputSummaryItemHD)
    self._wtTitleText = self:Wnd("wtMainText_1", UITextBlock)

    if IsHD() then
        if self._wtGetTipBtn and not self._hActionAccept then
            self._wtGetTipBtn:SelfHitTestInvisible()
            self._wtGetTipBtn:SetData("UseItem")
            self._hActionAccept =
            self:AddInputActionBinding(
                "UseItem",
                EInputEvent.IE_Pressed,
                acceptEventProxy,
                nil,
                EDisplayInputActionPriority.UI_Pop
            )
        end
        if self._wtEscBtn and not self._hActionQuit then
            self._wtEscBtn:SelfHitTestInvisible()
            local acceptEventProxy = SafeCallBack(self.OnCloseBtnClicked, self)
            self._wtEscBtn:SetData("Looting_CloseBag", acceptEventProxy)
            self._hActionQuit =
            self:AddInputActionBinding(
                "Looting_CloseBag",
                EInputEvent.IE_Pressed,
                acceptEventProxy,
                nil,
                EDisplayInputActionPriority.UI_Pop
            )
        end
    else
        self._wtGetTipBtn:Collapsed()
        self._wtEscBtn:Collapsed()
    end
end

-----------------------------------------------------生命周期-----------------------------------------------------
--#region

--- 初始化从UIManager传入的外部参数
function MTSafePanel:OnInitExtraData(items, desc, safaId, fCloseCallBack)
    self._desc = desc
    self._itemInfos = items
    self._safaId = safaId
    self.fCloseCallBack = fCloseCallBack

    self._items = {}
    self.curSearchIndex = 0

end

function MTSafePanel:OnOpen()
    self:SetAllText()
    self._wtItemGridBox:RefreshAllItems()
end

function MTSafePanel:OnShowBegin()
    self:InitGamepad()
end

function MTSafePanel:OnHideBegin()
    self:DisableGamepad()
end

function MTSafePanel:OnClose()
    self._items = {}
end
--#endregion

-----------------------------------------------------响应操作-----------------------------------------------------
--#region

function MTSafePanel:PreSearch()
    for i, item in ipairs(self._itemInfos) do
        if not item.bIsSearched then
            self.curSearchIndex = i
            return
        end
    end
end

function MTSafePanel:StartSearch()
    if self.curSearchIndex == 0 or self.curSearchIndex > #self._items then
        return
    end
    local item = self._items[self.curSearchIndex]
    local itemInfo = self._itemInfos[self.curSearchIndex]

    item:SearchBegin()

    local function SearchEnd()
        self._itemInfos[self.curSearchIndex].bIsSearched = true
        self.curSearchIndex = self.curSearchIndex + 1

        item:SearchEnd()
        self:StartSearch()
    end
    Timer.DelayCall(itemInfo.detectTime, SearchEnd, self)
end


function MTSafePanel:_OnGetItemCount()
    return #self._itemInfos or 0
end

function MTSafePanel:_OnProcessItemWidget(i, widget)
    local index = i + 1
    local item = self._itemInfos[index]
    self._items[index] = widget
    widget:SetInfo(item, index)
end

function MTSafePanel:_OnProcessItemFinished()
    self:PreSearch()
    self:StartSearch()
end

--#endregion

-----------------------------------------------------其他函数-----------------------------------------------------
--#region

function MTSafePanel:SetAllText()
    self._wtTitleText:SetText(ActivityLogic.HandleLocalizeText(self._desc))
end

function MTSafePanel:OnNavBack()
    self:OnCloseBtnClicked()
    return true
end

function MTSafePanel:OnCloseBtnClicked()
    if self.fCloseCallBack then
        self.fCloseCallBack()
    end
    Facade.UIManager:CloseUI(self)
end

function MTSafePanel:InitGamepad()
    if not IsHD() then return end
    self:DisableGamepad()
end

function MTSafePanel:DisableGamepad()
    if not IsHD() then return end
end
--#endregion

return MTSafePanel