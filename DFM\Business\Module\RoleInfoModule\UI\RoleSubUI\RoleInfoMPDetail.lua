----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRoleInfo)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

local RoleInfoLogic = require "DFM.Business.Module.RoleInfoModule.Logic.RoleInfoLogic"

local RoleInfoMPDetail = ui("RoleInfoMPDetail")
---@class RoleInfoMPDetail : LuaUIBaseView

function RoleInfoMPDetail:Ctor()
    self:InitSeasonData()

    self:SetCppValue("Type", 1)

    self._wtMpDetailData1 = self:Wnd("WBP_RoleInfo_DetailList01_MP", UIWidgetBase)
    self._wtMpDetailData2 = self:Wnd("WBP_RoleInfo_DetailList02_MP", UIWidgetBase)
    self._wtMpDetailData3 = self:Wnd("WBP_RoleInfo_DetailList03_MP", UIWidgetBase)
    self._wtSeasonText = self:Wnd("wtSeasonNum", UITextBlock)
    self._wtTotalFight = self:Wnd("DFTextBlockGame_Num", UITextBlock)
    self._wtTotalGameTime = self:Wnd("DFTextBlockTime_Num", UITextBlock)
    self._wtDetailPentagon = self:Wnd("WBP_RoleInfo_DetailPentagon", UIWidgetBase)
    self._wtPentagonSeasonPanel = self._wtDetailPentagon:Wnd("DFCanvasPanel_Season", UIWidgetBase)
    self._wtOnlyRankCheck = self:Wnd("wtDFCommonCheckBoxWithText", UIWidgetBase)
    self._wtOnlyRankCheck:Event("OnCheckStateChanged", self._OnlyRankCheckClick, self)
    self._wtRankStar = self:Wnd("DFImage_Star", UIWidgetBase)
    self._wtRankStar:Collapsed()
    self._wtRankStarNum = self:Wnd("DFTextBlock_NewRankPoint", UIWidgetBase)
    self._wtRankStarNum:Collapsed()
    self._wtRankScore = self:Wnd("DFTextBlock_76", UITextBlock)
    self._wtRank = self:Wnd("DFTextBlock_NewRankName", UIWidgetBase)
    self._wtRankIcon = self:Wnd("wtRankIcon", UIWidgetBase)
    self._wtCareerTxt = self:Wnd("wtCareerTxt", UIWidgetBase)
    self._seasonIndex = Server.TournamentServer:GetCurSerial()

    self._index = #self._seasonTxtTbl - 1
    self._wtSortDropDown = UIUtil.WndDropDownBox(self, "wtSortDropDown", self._ChangeSeasonDrop)
    self._wtDataDropDown = UIUtil.WndDropDownBox(self, "wtSortDropDown_2", self._ChangeRankDrop)

    self._seasonAvgTbl = { 0, 0, 0, 0, 0 }
    self._recentAvgTbl = { 0, 0, 0, 0, 0 }

    self.bCommandant = false -- 胜者为王模式，并且担任指挥官
    self.bVictoryUnite = false -- 胜者为王模式
    self.bPath = false -- 晋升之路模式
    self._rankIndex = 0
end

function RoleInfoMPDetail:InitSeasonData()
    self._seasonTxtTbl = { Module.RoleInfo.Config.Loc.SeasonOverview }
    self._seasonIndexTbl = { 0 }
    local serialNumber = Server.TournamentServer:GetCurSerial()

    if serialNumber then
        for index = 1, serialNumber do
            if IsBuildRegionCN() then
                local seasonConfig = Module.Tournament:GetSeasonConfigBySerial(index)
                table.insert(self._seasonTxtTbl, seasonConfig.Name)
                table.insert(self._seasonIndexTbl, index)
            else
                if index ~= 1 then
                    local seasonConfig = Module.Tournament:GetSeasonConfigBySerial(index)
                    table.insert(self._seasonTxtTbl, seasonConfig.Name)
                    table.insert(self._seasonIndexTbl, index)
                end
            end
        end
    end
end

function RoleInfoMPDetail:OnInitExtraData(playerId)
    self._playerId = playerId
    self._seasonIndex = Server.TournamentServer:GetCurSerial()
    self.bCommandant = false
    self._index = #self._seasonTxtTbl - 1
    self._wtOnlyRankCheck:SetIsChecked(false)
    self._wtSeasonText:SetText(self._seasonTxtTbl[#self._seasonTxtTbl])
    self:LimitClick()
    self:ReqSeasonDeailData()
    self:RefreshRankChange()

    -- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        if self._wtSortDropDown.MenuAnchor then
            self._wtSortDropDown:Event("PostOnMenuOpenChanged_GamepadUsed", self._OnSeasonDropToggled, self, true)
        end

        local wtKeyIconBox = self._wtSortDropDown:Wnd("DFCommonCheckButton", UIWidgetBase):Wnd("wtKeyIcon", HDKeyIconBox)
        if wtKeyIconBox then
            wtKeyIconBox:Visible()
            wtKeyIconBox:SetOnlyDisplayOnGamepad(true)
            wtKeyIconBox:InitByDisplayInputActionName("Common_LeftStickClick_Gamepad", true, 0, true)
        end
    end
    -- END MODIFICATION
end

-- BEGIN MODIFICATION @ VIRTUOS
function RoleInfoMPDetail:OnShowBegin()
    if IsHD() then
        self:_InitGamepadInputs()
    end
    UIUtil.InitDropDownBox(self._wtSortDropDown, self._seasonTxtTbl, {}, self._index)

    local Loc = Module.RoleInfo.Config.Loc
    local txtArr = { Loc.DataAll, Loc.PathToPromotionData, Loc.WinnerTakesAllData }
    UIUtil.InitDropDownBox(self._wtDataDropDown, txtArr, {}, self._rankIndex)
end

function RoleInfoMPDetail:OnHideBegin()
    if IsHD() then
        self:_DisableGamepadInputs()
    end
end

-- END MODIFICATION
function RoleInfoMPDetail:OnOpen()
    self._wtDetailPentagon:SetTextType(false)
end

function RoleInfoMPDetail:OnClose()

end

function RoleInfoMPDetail:RefreshMPDetailData(mpData)

    self._mpData = mpData

    local data1 = self._mpData.total_win / (self._mpData.total_fight == 0 and 1 or self._mpData.total_fight)

    local dataTile1 = RoleInfoLogic.GetRoleInfoNumberStr(self._mpData.total_score /
        ((self._mpData.total_game_time == 0 and 1 or self._mpData.total_game_time) / 60))

    local dataDetail11 = MathUtil.GetNumberFormatStr(data1 * 100, 1) .. "%"
    local dataDetail12 = self._mpData.total_capture_point
    local dataDetail13 = self._mpData.total_help or 0

    local data2 = self._mpData.total_shoot_hit / (self._mpData.total_shoot == 0 and 1 or self._mpData.total_shoot)
    local data3 = self._mpData.total_kill_head / (self._mpData.total_kill == 0 and 1 or self._mpData.total_kill)
    local dataTile2 = MathUtil.GetNumberFormatStr(self._mpData.total_kill /
        ((self._mpData.total_game_time == 0 and 1 or self._mpData.total_game_time) / 60), 1)
    local dataDetail21 = self._mpData.total_kill
    local dataDetail22 = MathUtil.GetNumberFormatStr(data2 * 100, 1) .. "%"
    local dataDetail23 = MathUtil.GetNumberFormatStr(data3 * 100, 1) .. "%"

    local dataTile3 = MathUtil.GetNumberFormatStr(self._mpData.total_vehicle_use_time / 3600, 1) ..
        Module.RoleInfo.Config.Loc.RecordHour
    local dataDetail31 = self._mpData.total_vehicle_kill
    local dataDetail32 = RoleInfoLogic.GetRoleInfoNumberStr(self._mpData.total_damage_to_vehicle)
    local dataDetail33 = RoleInfoLogic.GetRoleInfoNumberStr(self._mpData.total_vehicle_destroyed)

    local totalFight = self._mpData.total_fight
    local gameTime = ""
    if self._mpData.total_game_time < 3600 then
        gameTime = RoleInfoLogic.GetRoleInfoNumberStr(self._mpData.total_game_time / 60) ..
            Module.RoleInfo.Config.Loc.RecordMin
    else
        gameTime = RoleInfoLogic.GetRoleInfoNumberStr(self._mpData.total_game_time / 3600) ..
            Module.RoleInfo.Config.Loc.RecordHour
    end

    self._wtMpDetailData1:BP_SetDataText(dataTile1, dataDetail11, dataDetail12, dataDetail13)
    self._wtMpDetailData2:BP_SetDataText(dataTile2, dataDetail21, dataDetail22, dataDetail23)
    self._wtMpDetailData3:BP_SetDataText(dataTile3, dataDetail31, dataDetail32, dataDetail33)
    self._wtTotalFight:SetText(totalFight)
    self._wtTotalGameTime:SetText(gameTime)

    if not self._mpData.season_not_enough then
        self:_RefreshAvgData(self._seasonAvgTbl, self._mpData.season_avg_radar_dims)
        self._wtDetailPentagon:SetAverageData(
            self._seasonAvgTbl[1],
            self._seasonAvgTbl[2],
            self._seasonAvgTbl[3],
            self._seasonAvgTbl[4],
            self._seasonAvgTbl[5]
        )
    else
        self._wtDetailPentagon:SetAverageData(0, 0, 0, 0, 0)
    end


    if #self._mpData.recent_avg_radar_dims >= 5 and (not self._mpData.recent_not_enough) then
        self:_RefreshAvgData(self._recentAvgTbl, self._mpData.recent_avg_radar_dims)
        self._wtDetailPentagon:SetRecentVisible(true)
        self._wtDetailPentagon:SetRecentData(
            self._recentAvgTbl[1],
            self._recentAvgTbl[2],
            self._recentAvgTbl[3],
            self._recentAvgTbl[4],
            self._recentAvgTbl[5]
        )
    else
        self._wtDetailPentagon:SetRecentVisible(false)
        self._wtDetailPentagon:SetRecentData(0, 0, 0, 0, 0)
    end

    local isWinner = self.bCommandant == true or self.bVictoryUnite == true -- 胜者为王模式
    if self._rankIndex == 0 then -- 全部时默认选中玩家想展示的
        local basicInfo = Server.RoleInfoServer:GetPlayerBasicInfo()
        if basicInfo then
            if basicInfo.show_commander_rank_points == 1 then
                isWinner = true
            end
        end
    end

    local rankAttended = false
    local rankScoreMax = 0
    local maxSeasonNo = 0

    if isWinner == false then
        rankAttended = self._mpData.rank_attended
        rankScoreMax = self._mpData.rank_score_max
        maxSeasonNo = self._mpData.rank_max_season_no
    else
        rankAttended = self._mpData.commander_attended
        rankScoreMax = self._mpData.commander_score_max
        maxSeasonNo = self._mpData.commander_max_season_no
    end

    local rankName = ""
    if not rankAttended then
        if isWinner == false then
            self._wtRankIcon:SetTournamentIconNone()
        else
            self._wtRankIcon:SetCommanderIconNone()
        end

        self._wtRank:SetText(Module.Tournament.Config.Loc.NoneLevel)
        rankName = Module.Tournament.Config.Loc.NoneLevel
        self._wtRankStar:Collapsed()
        self._wtRankStarNum:Collapsed()
        self._wtRankScore:Collapsed()
    else
        local rankInfo
        if self._index == 0 then
            local maxSeason = maxSeasonNo ~= 0 and maxSeasonNo or 1

            rankInfo = isWinner and Module.Tournament:GetCommanderRankDataByScore(rankScoreMax, maxSeason) or
                Module.Tournament:GetRankDataByScore(rankScoreMax, maxSeason)

            if isWinner then
                self._wtRankIcon:SetCommanderIconByScore(rankScoreMax, maxSeason) -- 胜者为王
            else
                self._wtRankIcon:SetTournamentIconByScore(rankScoreMax, maxSeason)
            end

            self._wtRankStarNum:SetText(Module.Tournament:GetStarNumByScore(rankScoreMax,
                maxSeason) or "?")
        else
            local curSeason = self._seasonIndexTbl[self._index + 1]
            rankInfo = isWinner and Module.Tournament:GetCommanderRankDataByScore(rankScoreMax, curSeason) or
                Module.Tournament:GetRankDataByScore(rankScoreMax, maxcurSeasonSeason)

            if isWinner then
                self._wtRankIcon:SetCommanderIconByScore(rankScoreMax, curSeason) -- 胜者为王
            else
                self._wtRankIcon:SetTournamentIconByScore(rankScoreMax, curSeason)
            end

            self._wtRankStarNum:SetText(Module.Tournament:GetStarNumByScore(rankScoreMax, curSeason) or "?")
        end

        rankName = rankInfo.Name
        self._wtRank:SetText(rankName)
        self._wtRankScore:SetText(rankScoreMax)
        self._wtRankScore:SelfHitTestInvisible()
        self._wtRankStar:SelfHitTestInvisible()
        self._wtRankStarNum:SelfHitTestInvisible()
    end

    if self._seasonIndex == Server.TournamentServer:GetCurSerial() and (not self._mpData.recent_not_enough) then
        self._wtPentagonSeasonPanel:Visible()
    else
        self._wtPentagonSeasonPanel:Collapsed()
    end

    LogAnalysisTool.DoSendCheckPlayerSeasonDetailRecord(ULuautils.GetUInt64String(self._playerId), "mp", rankName,
        gameTime, totalFight,
        dataTile1,
        dataDetail11,
        dataDetail12,
        dataDetail13,
        dataTile2,
        dataDetail21,
        dataDetail22,
        dataDetail23,
        dataTile3,
        dataDetail31,
        dataDetail32,
        dataDetail33
    )
end

function RoleInfoMPDetail:ReqSeasonDeailData()
    -- seasonindex 0 代表数据总览
    Server.RoleInfoServer:FetchMPSeasonDetailData(self._playerId, self._seasonIndex, self.bPath, self.bVictoryUnite,
        self.bCommandant,
        CreateCallBack(self.RefreshMPDetailData, self))
end

function RoleInfoMPDetail:_ChangeRankDrop(index)
    self._rankIndex = index

    self:RefreshRankChange()
    self:PlayAnimation(self.WBP_RoleInfo_Detail_In, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    self:ReqSeasonDeailData()
    self:LimitClick()
end

function RoleInfoMPDetail:RefreshRankChange()
    if self._rankIndex == 0 then
        self.bPath = false
        self.bVictoryUnite = false
        self.bCommandant = false
        self._wtOnlyRankCheck:Collapsed()
    elseif self._rankIndex == 1 then
        self.bPath = true
        self.bVictoryUnite = false
        self.bCommandant = false
        self._wtOnlyRankCheck:Collapsed()
    elseif self._rankIndex == 2 then
        self.bPath = false
        self.bVictoryUnite = true
        self.bCommandant = false
        self._wtOnlyRankCheck:Visible()
        self._wtOnlyRankCheck:SetIsChecked(false)
    end
end

function RoleInfoMPDetail:_ChangeSeasonDrop(index)
    self._index = index
    if self._seasonIndexTbl[index + 1] then
        self:PlayAnimation(self.WBP_RoleInfo_Detail_In, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
        self._wtSeasonText:SetText(self._seasonTxtTbl[index + 1])
        self._seasonIndex = self._seasonIndexTbl[index + 1]
        self:ReqSeasonDeailData()
        self:LimitClick()

        if index == 0 then
            self._wtCareerTxt:SetText(Module.RoleInfo.Config.Loc.CareerTop)
        else
            self._wtCareerTxt:SetText(Module.RoleInfo.Config.Loc.SeasonTop)
        end
    end
end

function RoleInfoMPDetail:_OnlyRankCheckClick(isCheck)
    if isCheck then
        self.bPath = false
        self.bVictoryUnite = false
        self.bCommandant = true
    else
        self.bPath = false
        self.bVictoryUnite = true
        self.bCommandant = false
    end

    self:PlayAnimation(self.WBP_RoleInfo_Detail_In, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    self:ReqSeasonDeailData()
    self:LimitClick()
end

function RoleInfoMPDetail:_RefreshAvgData(avgTbl, avgData)
    for index, value in ipairs(avgTbl) do
        avgTbl[index] = 0
    end

    for index, info in ipairs(avgData) do
        avgTbl[info.dim] = info.score
    end
end

function RoleInfoMPDetail:_RefreshCdTimer()
    self._wtSortDropDown:SetIsEnabled(true)
    self._wtDataDropDown:SetIsEnabled(true)
    self._wtOnlyRankCheck:SetIsEnabled(true)
    if self._canRefreshHandle then
        self._canRefreshHandle:Release()
        self._canRefreshHandle = nil
    end
end

function RoleInfoMPDetail:LimitClick()
    if self._canRefreshHandle then
        self._canRefreshHandle:Release()
        self._canRefreshHandle = nil
    end
    self._wtSortDropDown:SetIsEnabled(false)
    self._wtDataDropDown:SetIsEnabled(false)
    self._wtOnlyRankCheck:SetIsEnabled(false)
    self._canRefreshHandle = Timer:NewIns(1, 1)
    self._canRefreshHandle:AddListener(self._RefreshCdTimer, self)
    self._canRefreshHandle:Start()
end

--- BEGIN MODIFICATION @ VIRTUOS
function RoleInfoMPDetail:_InitGamepadInputs()
    if not self:IsVisible() then
        return
    end

    -- Informal fix to inputs missing issue caused by unexpected "OnShowBegin" calls from RoleInfoMainPanel and disordered lifecycle.
    self:_DisableGamepadInputs()

    if not self._gamepadSummaryList then
        local onlyRankCheckEvent = SafeCallBack(
            function()
                if self._wtOnlyRankCheck:GetIsEnabled() then
                    self._wtOnlyRankCheck:SelfClick()
                end
            end, self)

        self._gamepadSummaryList =
        {
            { actionName = "RoleInfo_MPPointsRace", func = onlyRankCheckEvent, caller = self, bUIOnly = false },
        }
        Module.CommonBar:SetBottomBarTempInputSummaryList(self._gamepadSummaryList, false, false)
    end

    if not self._switchSeasonHandle then
        local switchSeasonEvent = SafeCallBack(
            function()
                if self._wtSortDropDown:GetIsEnabled() then
                    self._wtSortDropDown:OpenMenu()
                end
            end, self)

        self._switchSeasonHandle = self:AddInputActionBinding("Common_LeftStickClick_Gamepad", EInputEvent.IE_Pressed,
            switchSeasonEvent, self, EDisplayInputActionPriority.UI_Pop)
    end
end

function RoleInfoMPDetail:_DisableGamepadInputs()
    if self._gamepadSummaryList then
        Module.CommonBar:RecoverBottomBarInputSummaryList()
        self._gamepadSummaryList = nil
    end

    if self._switchSeasonHandle then
        self:RemoveInputActionBinding(self._switchSeasonHandle)
        self._switchSeasonHandle = nil
    end

    self:_OnSeasonDropToggled(false)
end

function RoleInfoMPDetail:_OnSeasonDropToggled(bOpen)
    if bOpen then
        if not self._seasonNavGroup then
            self._seasonNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtSortDropDown.ScrollGridBox,
                self._wtSortDropDown, "Hittest")
            if self._seasonNavGroup then
                self._seasonNavGroup:AddNavWidgetToArray(self._wtSortDropDown.ScrollGridBox)
                self._seasonNavGroup:SetScrollRecipient(self._wtSortDropDown.ScrollGridBox)
                self._seasonNavGroup:MarkIsStackControlGroup()
            end

            WidgetUtil.TryFocusDefaultWidgetByGroup(self._seasonNavGroup)
        end

        if not self._closeSeasonDropHandle then
            self._closeSeasonDropHandle = self:AddInputActionBinding("Back_Gamepad", EInputEvent.IE_Pressed,
                self._wtSortDropDown.CloseMenu, self._wtSortDropDown, EDisplayInputActionPriority.UI_Pop)
        end
    else
        if self._seasonNavGroup then
            WidgetUtil.RemoveNavigationGroup(self._wtSortDropDown)
            self._seasonNavGroup = nil
        end

        if self._closeSeasonDropHandle then
            self:RemoveInputActionBinding(self._closeSeasonDropHandle)
            self._closeSeasonDropHandle = nil
        end
    end
end

--- END MODIFICATION

return RoleInfoMPDetail
