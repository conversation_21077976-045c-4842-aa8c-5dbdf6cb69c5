----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMLooting)
----- LOG FUNCTION AUTO GENERATE END -----------




local InvSlotView = require "DFM.Business.Module.InventoryModule.UI.Common.InvSlotView"
local LootingLogic = require "DFM.Business.Module.LootingModule.LootingLogic"
local LootingConfig = require "DFM.Business.Module.LootingModule.LootingConfig"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local LootingLogic = require "DFM.Business.Module.LootingModule.LootingLogic"

local UWidgetLayoutLibrary = import "WidgetLayoutLibrary"

---@class LootSafeBoxSlotPanel : LuaUIBaseView
local LootSafeBoxSlotPanel = ui("LootSafeBoxSlotPanel")

local function log(...)
    loginfo("[LootSafeBoxSlotPanel]", ...)
end

ETabIndex = {
	SafeBox = 1,
	KeyChain = 2
}
LootSafeBoxSlotPanel.ETabIndex = ETabIndex

function LootSafeBoxSlotPanel:Ctor()
    self._tabIndex = 0
	self._tabChangedCallback = nil
	self._tabChangedCaller = nil
	self._wtMainCanvas = self:Wnd("wtMainCanvas", UIWidgetBase)
	self._wtKeyChainRoot = self:Wnd("wtKeyChainRoot", UIScrollBox)
	self._wtKetChainSizeBox = self:Wnd("DFSizeBox_232", UISizeBox)

	self._wtMidBtnRoot = self:Wnd("wtMidBtnRoot", UIWidgetBase)
	self._wtSafeBoxBtnBp = self:Wnd("wtSafeBoxBtn", UIWidgetBase)
	self._wtSafeBoxBtn = self._wtSafeBoxBtnBp:Wnd("DFMButton_78", UIButton)
	self._wtSafeBoxBtn:Event("OnClicked", self._OnSafeBoxBtnClick, self)
	self._wtKeyChainBtnBp = self:Wnd("wtKeyChainBtn", UIWidgetBase)
	self._wtKeyChainBtn = self._wtKeyChainBtnBp:Wnd("DFMButton_78", UIButton)
	self._wtKeyChainBtn:Event("OnClicked", self._OnKeyChainBtnClick, self)

    self:_InitAllSlot()

	self._keyContainerViewInsID = nil
end

-----------------------------------------------------------------------
--region Life function

function LootSafeBoxSlotPanel:OnOpen()

end

function LootSafeBoxSlotPanel:OnShowBegin()
	self:_SetSlotHeight()

	self:AddLuaEvent(Server.LootingServer.Events.evtLootingItemMove, self._OnLootingItemMove, self)

	self:RefreshView()
end

function LootSafeBoxSlotPanel:OnHide()
	self:RemoveAllLuaEvent()
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Public

function LootSafeBoxSlotPanel:RefreshView()
	-- 默认切到安全箱,安全箱过期时默认切到门禁卡包
	local safeBoxSlot = Server.InventoryServer:GetSlot(ESlotType.SafeBox)
	local bHasSafeBox = safeBoxSlot and safeBoxSlot:GetEquipItem()
	local keyChainSlot = Server.InventoryServer:GetSlot(ESlotType.KeyChain)
	-- 检查LootingLogic.CheckCurrentMapHasKeyChain()是因为新手关不需要显示卡包
	local bHasKeyChain =  LootingLogic.CheckCurrentMapHasKeyChain() and keyChainSlot and keyChainSlot:GetEquipItem()
	if not bHasSafeBox and not bHasKeyChain then
		self._wtMainCanvas:Hidden()
		return
	end
	self._wtMainCanvas:SelfHitTestInvisible()
	if not bHasSafeBox then
		self._wtSafeBoxBtnBp:Collapsed()
	end
	if not bHasKeyChain then
		self._wtKeyChainBtnBp:Collapsed()
	end
	if bHasSafeBox then
		if self._tabIndex ~= LootSafeBoxSlotPanel.ETabIndex.SafeBox then
			self._tabIndex = LootSafeBoxSlotPanel.ETabIndex.SafeBox
		end
	else
		if self._tabIndex ~= LootSafeBoxSlotPanel.ETabIndex.KeyChain then
			self._tabIndex = LootSafeBoxSlotPanel.ETabIndex.KeyChain
		end
	end
	self:_RefreshTab()
end

function LootSafeBoxSlotPanel:BindTabChangedCallback(callback, caller)
	self._tabChangedCallback = callback
	self._tabChangedCaller = caller
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Private

function LootSafeBoxSlotPanel:_InitAllSlot()
    self._wtSafeBoxSlotView = self:Wnd("wtSafeBoxSlotView", InvSlotView)
	self._wtSafeBoxSlotView:InitServerSlot(ESlotType.SafeBoxContainer, ESlotGroup.Player)
	self._wtSafeBoxSlotView:PrecreateItemViews(LootingConfig.ITEMVIEW_PRELOAD_NUM[ESlotType.SafeBoxContainer])
	self._wtSafeBoxSlotView:SetSlotMaxPreviewSize(3, 0)
end

function LootSafeBoxSlotPanel:_SetSlotHeight()
	local finalLength, finalHeight = self._wtSafeBoxSlotView:GetViewSize()
	self._wtKetChainSizeBox:SetHeightOverride(finalHeight+1)
	self._wtKetChainSizeBox:SetWidthOverride(finalLength+15)
end

function LootSafeBoxSlotPanel:_OnSafeBoxBtnClick()
	if self._tabIndex ~= LootSafeBoxSlotPanel.ETabIndex.SafeBox then
		self._tabIndex = LootSafeBoxSlotPanel.ETabIndex.SafeBox

		self:_RefreshTab()
	end
end

function LootSafeBoxSlotPanel:_OnKeyChainBtnClick()
	if self._tabIndex ~= LootSafeBoxSlotPanel.ETabIndex.KeyChain then
		self._tabIndex = LootSafeBoxSlotPanel.ETabIndex.KeyChain

		self:_RefreshTab()
	end
end

function LootSafeBoxSlotPanel:_RefreshTab()
	local type = self._tabIndex == LootSafeBoxSlotPanel.ETabIndex.SafeBox and 1 or 0
	self._wtSafeBoxBtnBp:SetCppValue("Type", type)
	self._wtKeyChainBtnBp:SetCppValue("Type", 1 - type)
	self._wtSafeBoxBtnBp:BP_SetStyle()
	self._wtKeyChainBtnBp:BP_SetStyle()

	if self._tabIndex == LootSafeBoxSlotPanel.ETabIndex.SafeBox then
		self._wtSafeBoxSlotView:Visible()
		local keyContainerView = self:_GetKeyContainerView(false)
		if keyContainerView then
			keyContainerView:Collapsed()
		end

		self._wtSafeBoxSlotView:RefreshView()
	else
		self._wtSafeBoxSlotView:Collapsed()
		local keyContainerView = self:_GetKeyContainerView()
		if keyContainerView then
			keyContainerView:Visible()
			keyContainerView:RefreshView()
		end
	end

    LootingConfig.Events.evtEquipTabChanged:Invoke(self._tabIndex)
	if self._tabChangedCallback then
		if self._tabChangedCaller then
			self._tabChangedCallback(self._tabChangedCaller, self._tabIndex)
		else
			self._tabChangedCallback(self._tabIndex)
		end
	end
	self:_RefreshSafeBoxCapacity()
	self:_RefreshKeyChainCapacity()
end

---@param bCreateIfNotExist boolean
---@return KeyContainerSubView
function LootSafeBoxSlotPanel:_GetKeyContainerView(bCreateIfNotExist)
	bCreateIfNotExist = setdefault(bCreateIfNotExist, true)
	local UINavID = UIName2ID.KeyContainerViewItem
	if not self._keyContainerViewInsID and bCreateIfNotExist then
		local keyChainContainer = Server.InventoryServer:GetSlot(ESlotType.KeyChainContainer)
		if keyChainContainer:GetSpacesNum() == 0 then
			loginfo("LootSafeBoxSlotPanel:_GetKeyContainerView fail to create KeyContainerSubView because KeyChainContainer has no space")
			return
		end

		---@type KeyContainerSubView
		local keyContainerView, keyContainerViewInsID = Facade.UIManager:AddSubUI(self, UINavID, self._wtKeyChainRoot)
		keyContainerView = getfromweak(keyContainerView)
		self._keyContainerViewInsID = keyContainerViewInsID

		if keyContainerView then
			keyContainerView:Collapsed()
			local allSpaces = keyChainContainer:GetAllSpaces()
			for index, space in pairs(allSpaces) do
				if ItemOperaTool.CheckKeyIndexMatchCurrentMapId(index) then
					keyContainerView:SelfHitTestInvisible()
					keyContainerView:InitByIndex(index)
					keyContainerView:RefreshView()
					break
				end
			end
		end
	end
	local keyContainerView = Facade.UIManager:GetSubUI(self, UINavID, self._keyContainerViewInsID)
	keyContainerView = getfromweak(keyContainerView)
	return keyContainerView
end

---@param moveItemInfo itemMoveInfo
function LootSafeBoxSlotPanel:_OnLootingItemMove(moveItemInfo)
	if not moveItemInfo or not moveItemInfo.item then
        return
    end
    local newLoc = moveItemInfo.NewLoc
	if newLoc and newLoc.ItemSlot:GetSlotGroup() == ESlotGroup.Player then
		local newSlot = newLoc.ItemSlot
		if newSlot.SlotType == ESlotType.KeyChainContainer then
			-- 切到钥匙包
			self:_OnKeyChainBtnClick()
		elseif newSlot.SlotType == ESlotType.SafeBoxContainer then
			-- 切到安全箱
			self:_OnSafeBoxBtnClick()
		end
	end


	self:_RefreshSafeBoxCapacity()
	self:_RefreshKeyChainCapacity()
end

function LootSafeBoxSlotPanel:_RefreshSafeBoxCapacity()
	local safeBoxSlot = Server.InventoryServer:GetSlot(ESlotType.SafeBoxContainer)
	local txt = string.format("%d/%d", safeBoxSlot:GetUsedCapacity(), safeBoxSlot:GetTotalCapacity())
	self._wtSafeBoxBtnBp:BP_SetName(txt)
end

function LootSafeBoxSlotPanel:_RefreshKeyChainCapacity()
	local keyChainContainer = Server.InventoryServer:GetSlot(ESlotType.KeyChainContainer)
	local allSpaces = keyChainContainer:GetAllSpaces()
	local usedCapacity = 0
	for index, _ in pairs(allSpaces) do
		if ItemOperaTool.CheckKeyIndexMatchCurrentMapId(index) then
			for _, itemLoc in pairs(keyChainContainer:GetItemsPosition()) do
				if itemLoc.SubIndex == index then
					usedCapacity = usedCapacity + 1
				end
			end
			break
		end
	end
	self._wtKeyChainBtnBp:BP_SetName(tostring(usedCapacity))
end

function LootSafeBoxSlotPanel:GetTabIndex()
	return self._tabIndex
end

--endregion
-----------------------------------------------------------------------

return LootSafeBoxSlotPanel