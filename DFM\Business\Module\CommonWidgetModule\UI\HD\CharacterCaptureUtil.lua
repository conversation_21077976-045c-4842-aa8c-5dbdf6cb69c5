----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCommonWidget)
----- LOG FUNCTION AUTO GENERATE END -----------



local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"

local UCharacterEquipComponent = import "CharacterEquipComponent"
local UCaptureStudioManager = import "CaptureStudioManager"
local UKismetInputLibrary = import "KismetInputLibrary"
local UWidgetBlueprintLibrary = import "WidgetBlueprintLibrary"
local ETeleportType = import "ETeleportType"
local FHitResult = import "HitResult"
local UDFMGameplayGlobalDelegates = import "DFMGameplayGlobalDelegates"
local UWidgetLayoutLibrary = import "WidgetLayoutLibrary"
local ULuaExtension = import "LuaExtension"
local EItemInfoUpdatedReason = import "EItemInfoUpdatedReason"
local UDFMCharacterFashionComponent = import "DFMCharacterFashionComponent"
local HeroFashionDataTable = Facade.TableManager:GetTable("Hero/HeroFashionData")

local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"

---@class CharacterCaptureUtil : LuaUIBaseView
local CharacterCaptureUtil = ui("CharacterCaptureUtil")

local state = {
    stop = 0,
    start = 1,
    waiting = 2
}

local BACK_UP_AVATAR_ID = ***********
local DEFAULT_FOV_GAME = 30
local DEFAULT_FOV_WAREHOUSE = 24
local FOLLOW_FASHION_MODE = true

local function log(...)
    loginfo("[CharacterCaptureUtil]", ...)
end

function CharacterCaptureUtil:Ctor()
    self._captureCharacter = nil
    self.state = state.stop
    self.slotGroup = ESlotGroup.Player
    if Module.Looting:IsCreatingUIForCollectionRoom() then
        self.slotGroup = ESlotGroup.CollectionRoom
    end
    self.x = 0
    self.y = 0
    self.fov = 0
    if ItemOperaTool.CheckRunWarehouseLogic() then
        self.fov = DEFAULT_FOV_WAREHOUSE 
    else
        self.fov = DEFAULT_FOV_GAME
    end
    self.captureEnvPath = ""
    if ItemOperaTool.CheckRunWarehouseLogic() then
        self.captureEnvPath = Module.CommonWidget.Config.DEFAULT_CAPTURE_ENV_PATH_WAREHOUSE 
    else
        self.captureEnvPath = Module.CommonWidget.Config.DEFAULT_CAPTURE_ENV_PATH_GAME
    end
    self.rotateSpeed = 1

    self._captureEnv = nil
    self.bMouseCaptured = false

    self.cacheEquipments = {}

    self._taskId = 0

    self._wtCaptureImg = self:Wnd("wtCaptureImg", UIImage)
    log(">>>> xhz >>>>>>>>>>>>>> CharacterCaptureUtil: softpath ", Module.CommonWidget.Config.DEFAULT_CAPTURE_MAT_PATH)
    self._bUseSimpleFaceAnim = false
    -- self._orgEVLocalLightScale = 1
    self:InjectLua()
end

function CharacterCaptureUtil:OnInitExtraData(bUseSimpleFaceAnim)
    bUseSimpleFaceAnim = setdefault(bUseSimpleFaceAnim, false)
    self._bUseSimpleFaceAnim = bUseSimpleFaceAnim
end

function CharacterCaptureUtil:OnOpen()
    logerror(">>>> xhz >>>>>>>>>>>>>> CharacterCaptureUtil:OnOpen", self, self._captureEnv)
    if not self._captureEnv then
        self:_InitCaptureEnv()
    end
end

function CharacterCaptureUtil:OnClose()
    logerror(">>>> xhz >>>>>>>>>>>>>> CharacterCaptureUtil:OnClose", self)
    -- if isvalid(self._captureEnv) then
        -- self._captureEnv:Clear()
        self:_CancelDelayCapture()
        if isvalid(self._captureEnv) then
            -- self._captureEnv:StopCapture()
            -- self._captureEnv:Clear()
        end
        self._captureEnv = nil
    -- end
end

function CharacterCaptureUtil:OnShowBegin()
    logerror(">>>> xhz >>>>>>>>>>>>>> CharacterCaptureUtil:OnShowBegin")
    -- self._orgEVLocalLightScale = LightUtil.GetMaxEVLocalLightScale()
    -- LightUtil.SetMaxEVLocalLightScale(1.0)
    LuaTickController:Get():RegisterTick(self)
    self:_CheckCaptureSize()
    self:StartCapture()
end

function CharacterCaptureUtil:OnHide()
    logerror(">>>> xhz >>>>>>>>>>>>>> CharacterCaptureUtil:OnHide")
    LuaTickController:Get():RemoveTick(self)
    self:_UnRegisterSizeCheckTimer()
    self:StopCapture()
    -- Modify by CharacterCaptureUtil, restore original value
    -- if LightUtil.GetMaxEVLocalLightScale() == 1 and self._orgEVLocalLightScale ~= 1 then
        -- LightUtil.SetMaxEVLocalLightScale(self._orgEVLocalLightScale)
    -- end
end

-- function CharacterCaptureUtil:Update(dt)
    -- local lightScale = LightUtil.GetMaxEVLocalLightScale()
    -- if lightScale ~= 1 then
    --     self._orgEVLocalLightScale = lightScale
    -- end
    -- LightUtil.SetMaxEVLocalLightScale(1.0)
-- end

function CharacterCaptureUtil:BindSlotGroup(slotGroup)
    if self.slotGroup ~= slotGroup then
        self.slotGroup = slotGroup

        self:_InitCharacterAppearance()
    end
end

function CharacterCaptureUtil:Init(x, y, fov)
    if x then self.x = math.floor(x) end
    if y then self.y = math.floor(y) end
    if fov then self.fov = math.floor(fov) end
end

function CharacterCaptureUtil:StartCapture()
    logerror(">>>> xhz >>>>>>>>>>>>>> CharacterCaptureUtil:StartCapture", self._taskId)
    if self._taskId == 0 then
        self._taskId = Facade.LuaFramingManager:RegisterFrameTask(self._InternalStartCapture, self, {}, nil, EFrameTaskPriority.Low)
    end
end

function CharacterCaptureUtil:StopCapture()
    logerror(">>>> xhz >>>>>>>>>>>>>> CharacterCaptureUtil:StopCapture", self._taskId, self.state, self._cacheDefaultRT)
    if self._taskId > 0 then
        Facade.LuaFramingManager:CancelFrameTask(self._taskId)
        self._taskId = 0
    end

    if self.state == state.stop then return end

    if self.state == state.start then
        self:_CancelDelayCapture()
        if isvalid(self._captureEnv) then
            self._captureEnv:StopCapture()
        end
        if isvalid(self._captureCharacter) then
            self._captureCharacter:SetSkeletalMeshTickEnabled(false)
        end

        self:RemoveAllLuaEvent()
        -- UDFMGameplayGlobalDelegates.Get(GetGameInstance()).OnNotifyActiveWeaponChanged:Remove(self._OnActiveWeaponChanged, self)
    end

    self.state = state.stop

    if not ItemOperaTool.CheckRunWarehouseLogic() then
        local playerState = Facade.GameFlowManager:GetPlayerState()
        if isvalid(playerState) then
            loginfo("CharacterCaptureUtil:StopCapture playerState.OnJailBreakPlayerStateChanged Remove self._OnJailBreakPlayerStateChanged")
            playerState.OnJailBreakPlayerStateChanged:Remove(self._OnJailBreakPlayerStateChanged, self)
        end
    end

    -- 设置可见性，不响应交互
    self:SelfHitTestInvisible()

    local material = self._wtCaptureImg:GetDynamicMaterial()
    if material then
        material:SetTextureParameterValue("CharacterRT", self._cacheDefaultRT)
        self._wtCaptureImg:SetRenderOpacity(0)
    end

    log(">>>> xhz >>>>>>>>>>>>>> CharacterCaptureUtil:StopCapture reset mat ")
    local SoftPath = FSoftObjectPath()
    SoftPath:SetPath(Module.CommonWidget.Config.DEFAULT_CAPTURE_MAT_PATH)
    self._wtCaptureImg:AsyncSetImageBySoftObjectPath(SoftPath, false, false)
end

function CharacterCaptureUtil:SetUseSimpleFaceAnim(bUseSimpleFaceAnim)
    bUseSimpleFaceAnim = setdefault(bUseSimpleFaceAnim, false)
    self._bUseSimpleFaceAnim = bUseSimpleFaceAnim
end

function CharacterCaptureUtil:_InternalStartCapture()
    logerror(">>>> xhz >>>>>>>>>>>>>> CharacterCaptureUtil:_InternalStartCapture", self._taskId, self.state)
    if self._taskId > 0 then
        self._taskId = 0
    end

    if self.state == state.start then return end
    if not self._wtCaptureImg then return end

    self:_CheckCaptureEnvLatest()

    if not isvalid(self._captureEnv) then
        if self.state == state.stop then
            self.state = state.waiting
            self:_InitCaptureEnv()
        end
        return
    end

    if self.x == 0 or self.y == 0 then return end

    self.state = state.start

    -- 设置可见性，响应交互
    self:Visible()

    -- 设置相机参数
    self:_UpdateCameraParams()

    -- 创建角色
    if not isvalid(self._captureCharacter) and isvalid(self._captureEnv) then
        -- 调用SpawnCharacter会创建rendertarget，size必须在之前就设好
        self._captureCharacter = self._captureEnv:SpawnCharacter()
        self._captureCharacter:SetUseSimpleFaceAnim(self._bUseSimpleFaceAnim)
    end
    self._captureCharacter:SetSkeletalMeshTickEnabled(true)
    -- 初始化角色外显
    self:_InitCharacterAppearance()

    -- 监听道具移动
    self:AddLuaEvent(Server.LootingServer.Events.evtLootingItemMove, self._OnLootingItemMove, self)
    self:AddLuaEvent(Server.InventoryServer.Events.evtItemMove, self._OnLootingItemMove, self)
    if not ItemOperaTool.CheckRunWarehouseLogic() then
        local playerState = Facade.GameFlowManager:GetPlayerState()
        if isvalid(playerState) then
            loginfo("CharacterCaptureUtil:_InternalStartCapture playerState.OnJailBreakPlayerStateChanged Add self._OnJailBreakPlayerStateChanged")
            playerState.OnJailBreakPlayerStateChanged:Add(self._OnJailBreakPlayerStateChanged, self)
        end
    end

    -- 开始拍摄
    local fDelayCapture = function()
        if isvalid(self._captureEnv) then
            local currentGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
            self._captureEnv:ShowSkylight(currentGameFlow ~= EGameFlowStageType.Game)
            local renderTarget = self._captureEnv:Capture(true)
            local material = self._wtCaptureImg:GetDynamicMaterial()
            if material and isvalid(material) and renderTarget and isvalid(renderTarget) then
                self._cacheDefaultRT = material:K2_GetTextureParameterValue("CharacterRT")
                material:SetTextureParameterValue("CharacterRT",renderTarget)
                self._wtCaptureImg:SetRenderOpacity(1.0)
                logerror(">>>> xhz >>>>>>>>>>>>>> CharacterCaptureUtil:fDelayCapture", renderTarget, material, self._cacheDefaultRT)
            end
        end
    end

    self:_CancelDelayCapture()
    self.delayHandle = Timer.DelayCall(0.1, fDelayCapture)
end

function CharacterCaptureUtil:_CancelDelayCapture()
    if self.delayHandle then
        Timer.CancelDelay(self.delayHandle)
        self.delayHandle = nil
    end
end

function CharacterCaptureUtil:_CheckCaptureSize()
    local geometry = self._wtCaptureImg:GetCachedGeometry()
    local localSize = geometry:GetLocalSize()
    local size = geometry:GetAbsoluteSize()

    local tolerance = 10 ^ -4
    if size:IsNearlyZero(tolerance) then
        self.state = state.waiting
        self:_RegisterSizeCheckTimer()
    else
        self:_UnRegisterSizeCheckTimer()

        local dpi = UWidgetLayoutLibrary.GetViewportScale(GetWorld())
        dpi = math.clamp(dpi, 0, 0.67) -- 最高使用2k的dpi，避免创建过大的RT
        local scale = self._wtCaptureImg.RenderTransform.Scale
        local finalX = localSize.X * dpi * scale.X
        local finalY = localSize.Y * dpi * scale.Y
        log("_CheckCaptureSize local size :", localSize.X, localSize.Y, " dpi :", dpi, " scale :", scale.X, scale.Y)

        -- local finalX = size.X
        -- local finalY = size.Y
        -- log("_CheckCaptureSize [0] initial absolute size:", finalX, finalY)
        -- local localScale = self._wtCaptureImg.RenderTransform.Scale
        -- size = size * localScale
        -- log("_CheckCaptureSize [1] size after considering local scale:", finalX, finalY, localScale.X, localScale.Y)
        -- local finalScale = 1
        -- -- local viewportClient = world and world:GetGameViewport() or nil
        -- -- local viewportSize = viewportClient and viewportClient:GetViewportSize() or nil
        -- local bufferSizeX, bufferSizeY = ULuaExtension.GetMainSceneRenderBufferSize(GetWorld(), 0, 0)
        -- local scaleX = bufferSizeX / finalX
        -- local scaleY = bufferSizeY / finalY
        -- finalScale = math.min(scaleX, scaleY)
        -- finalX = finalX * finalScale
        -- finalY = finalY * finalScale
        -- log("_CheckCaptureSize [2] size after considering MainSceneRenderBufferSize:", finalX, finalY, bufferSizeX, bufferSizeY, scaleX, scaleY)

        log("_CheckCaptureSize [3] final RT size:", finalX, finalY)
        self:Init(finalX, finalY)

        if self.state == state.waiting then
            self:StartCapture()
        end
    end
end

function CharacterCaptureUtil:_UpdateCameraParams()
    if isvalid(self._captureEnv) then
        log(self.x, self.y)
        self._captureEnv.RenderTargetSizeX = self.x
        self._captureEnv.RenderTargetSizeY = self.y
        -- self._captureEnv.Capture2DCom.FOVAngle = self.fov
    end
end

function CharacterCaptureUtil:_RegisterSizeCheckTimer()
    if not self._timerHandle then
        self._timerHandle = Timer:NewIns(0.01, 0)
        self._timerHandle:AddListener(self._CheckCaptureSize, self)
        self._timerHandle:Start()
    end
end

function CharacterCaptureUtil:_UnRegisterSizeCheckTimer()
    if self._timerHandle then
        self._timerHandle:Release()
        self._timerHandle = nil
    end
end

function CharacterCaptureUtil:_CheckCaptureEnvLatest()
    local captureStudioManager = UCaptureStudioManager.Get(GetWorld())
    local currentCaptureEnv = captureStudioManager:GetCaptureEnv()
    if self._captureEnv ~= currentCaptureEnv then
        logwarning("CharacterCaptureUtil:_CheckCaptureEnvLatest not latest", currentCaptureEnv, self._captureEnv)
        self._captureEnv = currentCaptureEnv
        self._captureCharacter = nil
    end
end

function CharacterCaptureUtil:_InitCaptureEnv()
    local captureStudioManager = UCaptureStudioManager.Get(GetWorld())
    self._captureEnv = captureStudioManager:GetCaptureEnv()
    if not self._captureEnv then
        self:_LoadCaptureEnv()
    end
end

function CharacterCaptureUtil:_LoadCaptureEnv()
    local captureStudioManager = UCaptureStudioManager.Get(GetWorld())
    captureStudioManager:LoadCaptureEnv(self.captureEnvPath)

    captureStudioManager.OnCaptureEnvLoaded:Add(self._OnCaptureEnvLoaded, self)
end

function CharacterCaptureUtil:_OnCaptureEnvLoaded()
    local captureStudioManager = UCaptureStudioManager.Get(GetWorld())
    self._captureEnv = captureStudioManager:GetCaptureEnv()

    captureStudioManager.OnCaptureEnvLoaded:Remove(self._OnCaptureEnvLoaded, self)

    if self.state == state.waiting then
        self:StartCapture()
    end
end

-----------------------------------------------------------------------
--region Character Appearance

local targetSlot2Appear = {
    [ESlotType.Helmet] = true,
    [ESlotType.BreastPlate] = true,
    [ESlotType.Bag] = true,
    [ESlotType.ChestHanging] = true,
}
local targetWeaponSlot2Apprer = {
    [ESlotType.MainWeaponLeft] = true,
    [ESlotType.MainWeaponRight] = true,
}

---@param itemMoveInfo itemMoveInfo
function CharacterCaptureUtil:_OnLootingItemMove(itemMoveInfo)
    if not itemMoveInfo or not itemMoveInfo.item then
        return
    end
    local item = itemMoveInfo.item
    local newLoc = itemMoveInfo.NewLoc
    local oldLoc = itemMoveInfo.OldLoc
    local newSlotGroup = newLoc and newLoc.ItemSlot:GetSlotGroup()
    local oldSlotGroup = oldLoc and oldLoc.ItemSlot:GetSlotGroup()
    local newSlotType = newLoc and newLoc.ItemSlot.SlotType
    local oldSlotType = oldLoc and oldLoc.ItemSlot.SlotType
    if newSlotType and targetSlot2Appear[newSlotType] and newSlotGroup == ESlotGroup.Player then
        self:_InitCharacterEquipAppearance()
    elseif newSlotType and targetWeaponSlot2Apprer[newSlotType] and newSlotGroup == ESlotGroup.Player then
        local lastModifyReason = item:GetLastModifyReason()
        if ItemOperaTool.CheckRunWarehouseLogic() or itemMoveInfo.Reason ~= PropChangeType.Modify
                or (lastModifyReason ~= EItemInfoUpdatedReason.ItemState and lastModifyReason ~= EItemInfoUpdatedReason.WeaponDesc) 
                or (lastModifyReason == EItemInfoUpdatedReason.WeaponDesc
                and item.modifyTimeStamp >= Module.Looting:GetOpenBagTime() + 0.2) then
            -- self:_InitCharacterWeaponAppearance()
            self:_DelayCheckWeapon()
        end
    elseif oldSlotType and targetSlot2Appear[oldSlotType] and oldSlotGroup == ESlotGroup.Player then
        self:_InitCharacterEquipAppearance()
    elseif oldSlotType and targetWeaponSlot2Apprer[oldSlotType] and oldSlotGroup == ESlotGroup.Player then
        local lastModifyReason = item:GetLastModifyReason()
        if ItemOperaTool.CheckRunWarehouseLogic() or itemMoveInfo.Reason ~= PropChangeType.Modify
                or (lastModifyReason ~= EItemInfoUpdatedReason.ItemState and lastModifyReason ~= EItemInfoUpdatedReason.WeaponDesc)
                or (lastModifyReason == EItemInfoUpdatedReason.WeaponDesc
                and item.modifyTimeStamp >= Module.Looting:GetOpenBagTime() + 0.2) then
            -- self:_InitCharacterWeaponAppearance()
            self:_DelayCheckWeapon()
        end
    end
end

function CharacterCaptureUtil:_OnJailBreakPlayerStateChanged()
    loginfo("CharacterCaptureUtil:_OnJailBreakPlayerStateChanged")
    self:_InitCharacterAvatar()
end

function CharacterCaptureUtil:_DelayCheckWeapon()
    if self._delayCheckWeaponHandle then
        Timer.CancelDelay(self._delayCheckWeaponHandle)
        self._delayCheckWeaponHandle = nil
    end
    self._delayCheckWeaponHandle = Timer.DelayCall(0.1, self._InitCharacterWeaponAppearance, self)
end

function CharacterCaptureUtil:_OnActiveWeaponChanged()
    self:_InitCharacterWeaponAppearance()
end

function CharacterCaptureUtil:_InitCharacterAppearance()
    if not isvalid(self._captureCharacter) then return end
    self.cacheEquipments = {}
    self._captureCharacter:ResetAppearance()

    self:_InitCharacterAvatar()

    self:_InitCharacterEquipAppearance()

    self:_InitCharacterWeaponAppearance()
end

function CharacterCaptureUtil:_InitCharacterAvatar()
    if not isvalid(self._captureCharacter) then return end

    if Facade.UIManager:GetCurrentStackUIId() == UIName2ID.WarehouseMain then
        -- 只有在仓库界面是从HeroServer取外观数据
        if self:IsShowFashion() then
            -- 只有开启了展示时装功能并且玩家开启了展示时装设置才展示时装
            local heroFashionSuitIdStr = tostring(Server.HeroServer:GetCurUsedHeroFashionId())
            self._captureCharacter:SetAvatarByFString(heroFashionSuitIdStr, "0")
            self._captureCharacter:ResetBodyShapeShiftType() -- 重置素体类型
        else
            -- 否则展示装备
            local heroIdStr = Server.HeroServer:GetCurUsedHeroId()
            self._captureCharacter:SetAvatarByFString(heroIdStr, "0")
            self._captureCharacter:SetBodyShapeShiftTypeByFashionId(tostring(Server.HeroServer:GetCurUsedHeroFashionId())) -- 根据时装id设置素体类型
        end
    else
        -- 局内和结算都是从DFMCharacterAppearanceFPP取外观数据
        local avatarId, fashionId = self:_GetAvatarIDFromAppearanceFPP()
        if avatarId == "0" and IsInEditor() then
            avatarId = BACK_UP_AVATAR_ID
        end
        local bInJailState = false
        local playerState = Facade.GameFlowManager:GetPlayerState()
        if isvalid(playerState) and playerState.bJailBreak then
            bInJailState = true
            loginfo("CharacterCaptureUtil:_InitCharacterAvatar bInJailState=true")
        end
        if avatarId ~= "" then
            if bInJailState then
                avatarId = self:_GenCharacterPureAvatarId(avatarId)
                avatarId = tostring(avatarId)
            end
            self._captureCharacter:SetAvatar(avatarId, "")
            if self:IsShowFashion() then
                self._captureCharacter:ResetBodyShapeShiftType()
            else
                self._captureCharacter:SetBodyShapeShiftTypeByFashionId(fashionId)
            end
        end
        if bInJailState then
            self._captureCharacter:SetNotUseFaceAnim(true)
            self._captureCharacter:SetPrisonerState(true)
        else
            self._captureCharacter:SetNotUseFaceAnim(false)
            self._captureCharacter:SetPrisonerState(false)
        end
    end
end

function CharacterCaptureUtil:_GenCharacterPureAvatarId(inAvatarId)
    local result = inAvatarId
    local heroFashionData = HeroFashionDataTable[tostring(inAvatarId)]
	if heroFashionData then
		result = heroFashionData.BelongedHeroID
	end
    return result
end

function CharacterCaptureUtil:_CheckAvatarIDValid(avatarId)
    local avatarTable = Facade.TableManager:GetTable("CharacterAvatarData")
    local avatarCfg = avatarTable[tostring(avatarId)]
    if not avatarCfg then
        return BACK_UP_AVATAR_ID
    end

    if #avatarCfg.UI.CoreParts == 0 then
        return BACK_UP_AVATAR_ID
    end

    return avatarId
end

function CharacterCaptureUtil:_InitCharacterEquipAppearance()
    if not isvalid(self._captureCharacter) then return end

    local currentEquipments = {}
    for slotType, _ in pairs(targetSlot2Appear) do
        local slot = Server.InventoryServer:GetSlot(slotType, self.slotGroup)
        local item = slot:GetEquipItem()
        if item then
            -- self._captureCharacter:Equip(item.id)
            currentEquipments[item.id] = true
        end
    end

    for id, _ in pairs(self.cacheEquipments) do
        if not currentEquipments[id] then
            self._captureCharacter:Unequip(id)
        end
    end
    if self:IsShowFashion() then
        self.cacheEquipments = {}
        return
    end
    for id, _ in pairs(currentEquipments) do
        if not self.cacheEquipments[id] then
            self._captureCharacter:Equip(id)
        end
    end
    self.cacheEquipments = currentEquipments
end

function CharacterCaptureUtil:_InitCharacterWeaponAppearance()
    self._delayCheckWeaponHandle = nil
    if not isvalid(self._captureCharacter) then return end

    local slot1 = Server.InventoryServer:GetSlot(ESlotType.MainWeaponLeft, self.slotGroup)
    local slot2 = Server.InventoryServer:GetSlot(ESlotType.MainWeaponRight, self.slotGroup)
    local desc = nil
    local weapon = slot1:GetEquipItem()
    if not weapon then
        weapon = slot2:GetEquipItem()
    end
    if weapon then
        desc = weapon:GetRawDescObj()
    end

    if desc then
        -- 如果当前能拿到武器的GunDesc，说明需要重新安装
        self._captureCharacter:EquipWeapon(desc)
    else
        -- 如果当前拿不到武器的GunDesc，说明需要卸载武器
        self._captureCharacter:UnequipWeapon()
    end
end

-- return: avatarId, fashionId
function CharacterCaptureUtil:_GetAvatarIDFromAppearanceFPP()
    local avatarId, fashionId
    local obInvMgr = Facade.GameFlowManager:GetOBInvMgr()
    if isvalid(obInvMgr) then
        avatarId = obInvMgr.CharacterAvatarId
        fashionId = obInvMgr.CharacterFashionId
    end
    if avatarId == nil then
        local playerCharacter = InGameController:Get():GetGPCharacter()
        local cmp = playerCharacter and playerCharacter:GetComponentByClass(UDFMCharacterFashionComponent)
        if cmp then
            fashionId = cmp:GetCurrentFashionSuitId()
            if self:IsShowFashion() then
                avatarId = cmp:GetCurrentFashionSuitId()
            else
                avatarId = cmp:GetCurrentHeroId()
            end
        end
    end
    if avatarId ~= nil then
        log(string.format("_InitCharacterAvatar Use Character AvatarId = %s", avatarId))
        return avatarId, fashionId
    end
    return "", ""
end

function CharacterCaptureUtil:IsShowFashion()
    return FOLLOW_FASHION_MODE and not Module.LobbyDisplay:GetCharacterDisplayEquipment() and not Facade.GameFlowManager:IsInOBMode()
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Drag Rotate

function CharacterCaptureUtil:Imp_OnMouseButtonDown(inGeometry, inGestureEvent)
    log("Imp_OnMouseButtonDown")

    self.bMouseCaptured = true

    local reply = UWidgetBlueprintLibrary.Handled()
    return UWidgetBlueprintLibrary.CaptureMouse(reply, self)
end

function CharacterCaptureUtil:Imp_OnMouseMove(inGeometry, inGestureEvent)
    if not self.bMouseCaptured then
        return UWidgetBlueprintLibrary.Unhandled()
    end

    log("Imp_OnMouseMove")
    local delta = UKismetInputLibrary.PointerEvent_GetCursorDelta(inGestureEvent)

    local deltaYaw = delta.X
    if isvalid(self._captureEnv) then
        local root = self._captureEnv:GetRootPoint()
        if root then
            local relativeRotation = root:GetRelativeRotation()
            relativeRotation.Yaw = relativeRotation.Yaw - deltaYaw
            root:K2_SetRelativeRotation(relativeRotation, false, FHitResult(), false)
        end
    end

    local reply = UWidgetBlueprintLibrary.Handled()
    return UWidgetBlueprintLibrary.CaptureMouse(reply, self)
end

function CharacterCaptureUtil:Imp_OnMouseButtonUp(inGeometry, inGestureEvent)
    log("Imp_OnMouseButtonUp")

    self.bMouseCaptured = false

    local reply = UWidgetBlueprintLibrary.Handled()
    reply = UWidgetBlueprintLibrary.ReleaseMouseCapture(reply)
    return reply
end

function CharacterCaptureUtil:RefreshCharacterAppearance()
    self:_InitCharacterAppearance()
end

--endregion
-----------------------------------------------------------------------

return CharacterCaptureUtil