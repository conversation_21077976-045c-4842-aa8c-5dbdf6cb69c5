----- <PERSON>OG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRankingList)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class RankingListItem : UIWidgetBase
local RankingListItem = ui("RankingListItem")
local RoleInfoLogic = require "DFM.Business.Module.RoleInfoModule.Logic.RoleInfoLogic"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"

local RankTypeLogic = {
    [RankDataType.SOL_SCORE] = false,
    [RankDataType.SOL_BRINGOUT_VALUE] = true,
    [RankDataType.SOL_KILL] = false,
    -- [RankDataType.SOL_REWARD] = true,
    -- [RankDataType.SOL_KILL_BOSS] = false,
    -- [RankDataType.SOL_MANDEL] = false,
    [RankDataType.TDM_SCORE] = false,
    [RankDataType.TDM_KILL] = false,
    [RankDataType.TDM_CAPTURE_ZONE_POINT] = false,
    [RankDataType.TDM_RESCUE_POINT] = false,
    -- [RankDataType.TDM_ENGINEER_POINT] = false,
    -- [RankDataType.TDM_TACTICAL_POINT] = false,
    [RankDataType.TDM_VICTORY_UNIT_SCORE] = false,
}


function RankingListItem:Ctor()
    self._wtRankNumImage = self:Wnd("wtRankNumImage", UIImage)
    self._wtRankNum = self:Wnd("wtRankNum", UITextBlock)
    self._wtPlayerName = self:Wnd("wtPlayerName", UITextBlock)
    self._wtCommonHeadIcon = self:Wnd("wtCommonHeadIcon", UIWidgetBase)
    self._wtRankIcon = self:Wnd("wtRankIcon", UIWidgetBase)
    self._wtScoreText = self:Wnd("wtScoreText", UITextBlock)
    self._wtRankName = self:Wnd("wtRankName", UITextBlock)
    self._wtRankStar = self:Wnd("wtRankStar", UIImage)
    self._wtRankStarNum = self:Wnd("wtRankStarNum", UITextBlock)
    self._wtSelect = self:Wnd("WBP_SlotCompSelected_1", UIWidgetBase)

    self._wtSelectBtn = self:Wnd("DFButton_0", UIButton)
    --- BEGIN MODIFICATION @ VIRTUOS
    if IsConsole() then
        self._wtPlatformIcon = self:Wnd("wtPlatformIcon", UIImage)
        self:Event("OnMouseEnterEvent", self._OnSelectClick, self)
        self:Event("OnMouseLeaveEvent", self.NoSelect, self)
        self._wtSelectBtn:Event("OnClicked",self.OnClicked, self)
    else
        self._wtSelectBtn:Event("OnClicked",self._OnSelectClick, self)
    end
    --- END MODIFICATION
end

function RankingListItem:OnInitExtraData()

end

function RankingListItem:RefreshRankWidget(info, rankNum, rankType, isMp, parent, IsInRank)
    if info.score then
        if RankTypeLogic[rankType] then
            self._wtScoreText:SetText(RoleInfoLogic.GetRoleInfoNumberStr(info.score))
        else
            self._wtScoreText:SetText(info.score)
        end
    else
        self._wtScoreText:SetText(0)
    end
    self._parent = parent
    self:RefreshWidget(info, rankNum, rankType, isMp, IsInRank)
end

function RankingListItem:RefreshFriendWidget(info, rankNum, rankType, isMp, parent, IsInRank, IsInstruction)
    self._parent = parent
    if info.rank_player_aux_data[rankType] then
        if RankTypeLogic[rankType] then
            self._wtScoreText:SetText(RoleInfoLogic.GetRoleInfoNumberStr(info.rank_player_aux_data[rankType]))
        else
            self._wtScoreText:SetText(info.rank_player_aux_data[rankType])
        end
    else
        self._wtScoreText:SetText(0)
    end
    self:RefreshWidget(info, rankNum, rankType, isMp, IsInRank, IsInstruction)
end

function RankingListItem:RefreshWidget(info, rankNum, rankType, isMp, IsInRank, IsInstruction)
    if IsConsole() then
        self._playIconInfo = {
            pic_url = info.pic_url,
            player_id = info.player_id,
            --- BEGIN MODIFICATION @ VIRTUOS
            plat_id = info.plat_id or info.plat,
            --- END MODIFICATION
            level = 0,
            nick_name = info.nick_name,
        }
    else
        self._playIconInfo = {
            pic_url = info.pic_url,
            player_id = info.player_id,
            level = 0,
            nick_name = info.nick_name,
        }
    end


    self._index = rankNum
    if not isMp then
        self._playIconInfo.level = info.season_level
    else
        self._playIconInfo.level = info.level
    end

    --- BEGIN MODIFICATION @ VIRTUOS
    if IsConsole() and self._wtPlatformIcon then
        local platID = info.plat_id or info.plat
        local platIconPath = Module.Friend:GetPlatformIconPath(platID, false, false)
        if platIconPath then
            self._wtPlatformIcon:AsyncSetImagePath(platIconPath, false)
            self._wtPlatformIcon:SelfHitTestInvisible()
        else
            self._wtPlatformIcon:Collapsed()
        end
    end
    --- END MODIFICATION

    local btnTbl = {
        HeadButtonType.PlayerInformat,
        HeadButtonType.AddFriend,
        HeadButtonType.Report,
    }
    if info.is_privacy then
        self._wtPlayerName:SetText(Module.RankingList.Config.Loc.PrivacyName)
        self._playIconInfo.pic_url = ""
        self._wtCommonHeadIcon:InitPortrait(self._playIconInfo, HeadIconType.HeadNore)
    else
        local remarkName = Server.FriendServer:GetFriendRemarkById(info.player_id)
        if remarkName ~= "" then
            self._wtPlayerName:SetText(string.format(Module.Friend.Config.QQFriend, remarkName, info.nick_name))
        else
            self._wtPlayerName:SetText(info.nick_name)
        end
        self._wtCommonHeadIcon:InitPortrait(self._playIconInfo, HeadIconType.HeadList, btnTbl)
    end

    -- BEGIN MODIFICATION @ VIRTUOS : TRC: replace player name with ps5 online id
    if IsPS5() and info.plat_id==PlatIDType.Plat_Playstation then
        local callback = function(onlineID)
            self._wtPlayerName:SetText(onlineID)
        end
        Module.Social:AsyncGetPS5OnlineIdByUID(info.player_id, callback, self)
    end
    -- END MODIFICATION
    
    if IsInstruction ~= nil and IsInstruction == true then
        self._wtRankNum:SetText(Module.RankingList.Config.Loc.RankingDifferentDevice)
        self:SetType(0, true)
    else
        if rankNum == 0 then
            if IsInRank then
                self._wtRankNum:SetText("--")
                self:SetType(0, false)
            else
                self._wtRankNum:SetText(Module.RankingList.Config.Loc.NoInThisArea)
                self:SetType(0, true)
            end
        else
            self._wtRankNum:SetText(rankNum)
            if rankNum <= 3 then
                self:SetType(rankNum, false)
            else
                self:SetType(0, false)
            end
        end
    end
    self._wtRankStar:Visible()
    self._wtRankStarNum:Visible()
    if isMp then
        if info.mp_attended then
            local rankInfo = Module.Tournament:GetRankDataByScore(info.mp_rank_score)
            if rankInfo ~= nil then
                self._wtRankName:SetText(rankInfo.Name)
                self._wtRankIcon:SetTournamentIconByScore(info.mp_rank_score)
                self._wtRankStarNum:SetText(Module.Tournament:GetStarNumByScore(info.mp_rank_score) or "?")
            end
        else
            self._wtRankIcon:SetRankIconNone()
            self._wtRankName:SetText(Module.RoleInfo.Config.Loc.NoRankDataTxt)
            self._wtRankStar:Collapsed()
            self._wtRankStarNum:Collapsed()
        end
    else
        if info.sol_attended then
            local rankInfo = Module.Ranking:GetMinorDataByScore(info.sol_rank_score)
            if rankInfo ~= nil then
                self._wtRankName:SetText(rankInfo.RankName)
                self._wtRankIcon:SetRankingIconByScore(info.sol_rank_score)
                self._wtRankStarNum:SetText(Module.Ranking:GetStarNumByScore(info.sol_rank_score) or "?")
            end
        else
            self._wtRankIcon:SetRankIconNone()
            self._wtRankName:SetText(Module.RoleInfo.Config.Loc.NoRankDataTxt)
            self._wtRankStar:Collapsed()
            self._wtRankStarNum:Collapsed()
        end
    end
end


function RankingListItem:OnOpen()

end

function RankingListItem:_OnSelectClick()
    if not self._parent then
        return
    end
    self:IsSelect()
    self._parent:_selectIndexChange(self._index, self)
end

function RankingListItem:IsSelect()
    self._wtSelect:HitTestInvisible()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        self._wtSelectBtn:SelfHitTestInvisible()
    end
end

function RankingListItem:NoSelect()
    self._wtSelect:Collapsed()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        self._wtSelectBtn:Visible()
    end
end

function RankingListItem:OnClicked()
    --- BEGIN MODIFICATION @ VIRTUOS
    if IsConsole() then
        if self._wtCommonHeadIcon.ActiveClick then
            self._wtCommonHeadIcon:ActiveClick()
        end
    else
        self:_OnSelectClick()
    end
    --- END MODIFICATION
end

function RankingListItem:SetButtonEnable(bEnable)
    self._wtSelectBtn:SetVisibility(bEnable and ESlateVisibility.Visible or ESlateVisibility.HitTestInvisible)
end

return RankingListItem
