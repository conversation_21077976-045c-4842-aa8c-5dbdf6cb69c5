----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMFriend)
----- LOG FUNCTION AUTO GENERATE END -----------
local FriendLogic = require "DFM.Business.Module.FriendModule.Logic.FriendLogic"


---@class FriendAddBlackBox : LuaUIBaseView
local FriendAddBlackBox = ui("FriendAddBlackBox")

function FriendAddBlackBox:Ctor()
    loginfo("FriendAddBlackBox:Ctor")
    self._wtPlayerName = self:Wnd("wtPlayerName", UITextBlock)
    self._wtPlayerIcon = self:Wnd("WBP_CommonHeadIcon", CommonHeadIcon)
    self._playerInfo = {}
    self._wtCancelBlack = self:Wnd("wtCancelBlack", UIWidgetBase)
    self._wtCancelBlack:Event("OnClicked",self._OnUnBlockPlayerClick, self)

    self._wtRankIcon = self:Wnd("wtRankIcon" ,UIWidgetBase)
    if IsHD() then
        self._wtBHDMark = self:Wnd("WBP_BHD_OwnMark",UIWidgetBase)
        self._wtDFTipsAnchor = UIUtil.WndTipsAnchor(self._wtBHDMark, "DFTipsAnchor", self.ShowTips,self.CloseTips)
    end
    self._wtRankDivision = self:Wnd("wtRankDivision", UITextBlock)
end

function FriendAddBlackBox:ShowUI(data, index, scollBoxTbl, widgetSizeY, widgetSpacingY)
    self._playerInfo = data
    self._wtPlayerName:SetText(self._playerInfo.nick_name)
    --self._wtPlayerIcon:InitPortrait(self._playerInfo, HeadIconType.HeadPerInformat)
    --- BEGIN MODIFICATION @ VIRTUOS using Platform Online ID as player name.
    if IsPS5() then
        local callback = function(onlineID)
            self._wtPlayerName:SetText(onlineID)
        end
        Module.Social:AsyncGetPS5OnlineIdByUID(self._playerInfo.player_id, callback, self)
    end
    --- END MODIFICATION
    if self._wtApplyFrom then
        self._wtApplyFrom:SetText(self._source ~= 0 and Module.Friend.Config.friendApplyType[self._source] or "")
    end
    if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.LobbyBHD then--BHD走单独的显示逻辑
        self:SetStyleInBHD()
        return
    end

    -- self._playIconInfo = {
    --     pic_url = self._playerInfo.pic_url,
    --     player_id = self._playerInfo.player_id,
    --     nick_name = self._playerInfo.nick_name,
    --     level = 0
    -- }

    self._playIconInfo = self._playerInfo

    if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse then
        if self._playerInfo.season_level then
            self._playIconInfo.level = self._playerInfo.season_level
        elseif self._playerInfo.season_lvl then
            self._playIconInfo.level = self._playerInfo.season_lvl
        end
        self._playIconInfo.rank_attended = self._playerInfo.sol_rank_attended
        self._playIconInfo.rank_score = self._playerInfo.sol_rank_score
    elseif Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Lobby then
        self._playIconInfo.level = self._playerInfo.level
        self._playIconInfo.rank_attended = self._playerInfo.mp_rank_attended
        self._playIconInfo.rank_score = self._playerInfo.mp_rank_score
    end
    FriendLogic.InitRankByIconInfo(self._wtRankIcon, self._wtRankDivision, self._playIconInfo)

    local btnTbl = {
        HeadButtonType.PlayerInformat,
        HeadButtonType.AddBlack,
    }

    self._wtPlayerIcon:InitPortrait(self._playIconInfo, HeadIconType.HeadList, btnTbl)
    if IsHD() then
        --self:SetBHDOwnMark(self._playerInfo)
    end
end

function FriendAddBlackBox:_PlayBoxAnimation()
    self:Visible()
end

function FriendAddBlackBox:_OnUnBlockPlayerClick()
    Server.FriendServer:FetchFriendRemoveBlackList(self._playerInfo.player_id, false)
end

function FriendAddBlackBox:OnClose()
    self:RemoveAllLuaEvent()
end

function FriendAddBlackBox:SetStyleInBHD()
    self._playIconInfo = {
        pic_url = self._playerInfo.pic_url,
        player_id = self._playerInfo.player_id,
        nick_name = self._playerInfo.nick_name,
    }
    FriendLogic.InitRankByIconInfo(self._wtRankIcon, self._wtRankDivision, self._playIconInfo)
    self._wtRankIcon:Collapsed()
    self._wtRankDivision:Collapsed()
    local btnTbl = {
        HeadButtonType.AddBlack,
    }
    self._wtPlayerIcon:InitPortrait(self._playIconInfo, HeadIconType.HeadList,btnTbl)

end

function FriendAddBlackBox:SetBHDOwnMark(info)
    --info.bhd_is_purchased = true
    if info.bhd_is_purchased then
        self._wtBHDMark:SelfHitTestInvisible()
    else
        self._wtBHDMark:Collapsed()
    end
end

function FriendAddBlackBox:ShowTips()
    local datas = {}
    if self._tipsInfo ~= ""  then
        table.insert(datas, {textContent = Module.LobbyBHD.Config.Loc.BHDOwned})
        self._tipHandle = Module.CommonTips:ShowCommonMessagesWithAnchor(datas,self._wtDFTipsAnchor)
    end

end

function FriendAddBlackBox:CloseTips()
    if self._tipHandle then
        Module.CommonTips:RemoveCommonMessageWithAnchor(self._tipHandle, self._wtTipAnchor)
        self._tipHandle = nil
    end
end


return FriendAddBlackBox