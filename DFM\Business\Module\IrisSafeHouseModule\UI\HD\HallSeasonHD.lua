----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMIrisSafeHouse)
----- LOG FUNCTION AUTO GENERATE END -----------


local HallSeasonHD=ui("HallSeasonHD")

function HallSeasonHD:Ctor()
    self._wtSeasonBtn = self:Wnd("DFButton_0", UIButton)
    self._wtSeasonBtn:Event("OnClicked",self._OnSeasonBtnClicked,self)
    self._wtSeasonText = self:Wnd("DFTextBlock_38", UITextBlock)
    self._wtSeasonImage = self:Wnd("Image_Bg", UIImage)
    self._wtReddotCanvas=self:Wnd("DFCanvasPanel_110",UIWidgetBase)
    self._wtSeasonTipText = self:Wnd("DFTextBlock_227", UITextBlock)
    self._bIsMp=false

    -- BEGIN MODIFICATION @ VIRTUOS : 绑定图片组件
    self._wtHallSeasonKeyIcon = self:Wnd("HallSeasonKeyIcon", UIWidgetBase)
    -- END MODIFICATION
end

-- BEGIN MODIFICATION @ VIRTUOS : 绑定图片组件
function HallSeasonHD:OnShowBegin()
    loginfo("HallSeasonHD:OnShowBegin")
    
    if self._wtHallSeasonKeyIcon then
        self._wtHallSeasonKeyIcon:SetOnlyDisplayOnGamepad(true)
        self._wtHallSeasonKeyIcon:InitByDisplayInputActionName("MainMenu_OpenSeason", true, 0, true)
        self._wtHallSeasonKeyIcon:BP_ShowHoldProgressBarTips(true)
    end
end
-- END MODIFICATION

function HallSeasonHD:OnClose()
    loginfo("HallSeasonHD:OnClose")
    if self._rankReddot then
        Module.ReddotTrie:UnRegisterStaticReddotDot(self._rankReddot)
    end
    self:RemoveAllLuaEvent()
end

function HallSeasonHD:_OnSeasonBtnClicked()
    if self._bIsMp then
        loginfo("HallSeasonHD:_OnTournamentEntranceBtnClicked")
        LogAnalysisTool.SignButtonClicked(10120023)
        if not Server.ModuleUnlockServer:IsModuleUnlock(SwitchModuleID.ModuleScoreMP) then
            local unlockInfo=Server.ModuleUnlockServer:GetModuleUnlockInfoById(SwitchModuleID.ModuleScoreMP)
            Module.CommonTips:ShowSimpleTip(unlockInfo and unlockInfo.unlocktips)
            return
        end
        local defaultIndex=Module.BattlefieldEntry:CheckIsCommanderMode() and 2 or 1
        Module.Tournament:ShowMainPanel(nil,defaultIndex)
    else
        loginfo("HallSeasonHD:_OnRankingEntranceBtnClicked")
        LogAnalysisTool.SignButtonClicked(10030021)
        if not Server.ModuleUnlockServer:IsModuleUnlock(SwitchModuleID.ModuleRankSOL) then
            local unlockInfo=Server.ModuleUnlockServer:GetModuleUnlockInfoById(SwitchModuleID.ModuleRankSOL)
            Module.CommonTips:ShowSimpleTip(unlockInfo and unlockInfo.unlocktips)
            return
        end
        Module.Ranking:ShowMainPanel()
    end
end

-- BEGIN MODIFICATION @ VIRTUOS : 
function HallSeasonHD:ClickSeasonBtnbyGamepad()
    self:_OnSeasonBtnClicked()
end
-- END MODIFICATION

function HallSeasonHD:SetTitleAndImage(title, imagePath)
    self._wtSeasonText:SetText(title)
    self._wtSeasonImage:AsyncSetImagePath(imagePath,false)
end

function HallSeasonHD:SetIsMp(bIsMP)
    loginfo("HallSeasonHD:SetIsMp",bIsMP)
    local gameFlow=Facade.GameFlowManager:GetCurrentGameFlow()
    if (bIsMP and gameFlow~=EGameFlowStageType.Lobby) or (not bIsMP and gameFlow==EGameFlowStageType.Lobby) then
        logerror("HallSeason:SetIsMp, error type!!!","bIsMP",bIsMP,"gameFlow",gameFlow)
        --bIsMP=gameFlow==EGameFlowStageType.Lobby
    end
    self._bIsMp=bIsMP
    if bIsMP then
        local fSetSeasonName=CreateCallBack(function(self)
            self:RefreshSeasonName(true)
            local reddotData=Module.ReddotTrie:GetReddotData(EReddotTrieObserverType.Tournament,"")
            if reddotData then
                if self._wtReddotCanvas then
                    self._rankReddot=Module.ReddotTrie:RegisterStaticReddotDot(self._wtReddotCanvas,{{reddotData=reddotData}})
                end
            else
                logerror("HallSeason:SetIsMp reddotData is nil!!!")
            end
            
        end,self)
        if not Server.TournamentServer:GetCurSerial() then
            Server.TournamentServer:ReqSeasonRankInfo(nil,fSetSeasonName)
        else
            fSetSeasonName()
        end

    else
        local fSetSeasonName=CreateCallBack(function(self)
            self:RefreshSeasonName(false)
            local reddotData=Module.ReddotTrie:GetReddotData(EReddotTrieObserverType.Ranking,"")
            if reddotData then
                if self._wtReddotCanvas then
                    self._rankReddot=Module.ReddotTrie:RegisterStaticReddotDot(self._wtReddotCanvas,{{reddotData=reddotData}})
                end
            else
                logerror("HallSeason:SetIsMp reddotData is nil!!!")
            end
        end,self)
        if not Server.RankingServer:GetCurSerial() then
            Server.RankingServer:ReqSeasonRankInfo(nil,fSetSeasonName)
        else
            fSetSeasonName()
        end
        
    end
    if bIsMP then
        self:AddLuaEvent(Server.TournamentServer.Events.evtTournamentInfoUpdated,self.RefreshSeasonName,self)
    else
        self:AddLuaEvent(Server.RankingServer.Events.evtSeasonRankInfoUpdated,self.RefreshSeasonName,self)
    end
    
end

function HallSeasonHD:RefreshSeasonName(bIsMP)
    loginfo("HallSeasonHD:RefreshSeasonName",bIsMP)
    bIsMP=setdefault(bIsMP,self._bIsMp) 
    if bIsMP then
        local seasonConfig=Module.Tournament:GetSeasonConfigBySerial(Server.TournamentServer:GetCurSerial())
        if seasonConfig then
            self._wtSeasonText:SetText(seasonConfig.Name)
        end
    else
        local seasonConfig=Module.Ranking:GetRankSeasonConfigBySerial(Server.RankingServer:GetCurSerial())
        if seasonConfig then
            self._wtSeasonText:SetText(seasonConfig.SeasonName)
        end
    end
end

function HallSeasonHD:ShowSeasonTip(bShow)
    bShow=setdefault(bShow,true)
    if self._bIsMp then
        self._wtSeasonTipText:Collapsed()
    else
        if bShow then
            self._wtSeasonTipText:SelfHitTestInvisible()
        else
            self._wtSeasonTipText:Collapsed()
        end
    end
    
end


return HallSeasonHD