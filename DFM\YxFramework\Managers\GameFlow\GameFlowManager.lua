----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaFGameFlowManager)
----- LOG FUNCTION AUTO GENERATE END -----------


local EGameFlowDefine = require "DFM.YxFramework.Managers.GameFlow.Processor.EGameFlowDefine"
---@class GameFlowManager : ManagerBase
local GameFlowManager = class("GameFlowManager", require"DFM.YxFramework.Managers.ManagerBase")
require "DFM.YxFramework.Plugin.Memory.MemTool"
---------------------------------------------------------------------------------------------------------------
--- GameFlow管理器，负责通知各个模块当前的GameFlow切换变化
--- [GameFlowManager]

--- * 单[GameFlow]通知：
---
--- 负责流转GameFlow切换的通知到Manager层
--- GameFlowChangeLeave和GameFlowChangeEnter包含每一个[过程态GF]和[目标态GF]
--- 每个GameFlowLeave触发所有Manager Reset,用于单GameFlow小清理
--- Reset时清理和重置子阶段到None
---
--- * 抽象[GameFlowProcessor]通知（捏合多个GF，目前用于处理Loading）:
---
--- 基于大部分[过程态GF]抽象了状态机管理的GameFlowState状态和GameFlowTransition连线
--- Module和Server配置了bAutoLoading = true时，接收以下生命周期：
---     OnLoadingLogin2Frontend,OnLoadingGame2Frontend,OnLoadingFrontend2Game
---     Module资源分为[常驻]|[Loading]|[运行]
---     Server协议也分为不同Loading时拉取|清理
----------------------------------------------------------------------------------------------------------------

require "DFM.YxFramework.Managers.GameFlow.GameFlowGlobalConst"
local StageSignalEmitter = require "DFM.YxFramework.Managers.GameFlow.Stage.StageSignalEmitter"
local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"
local BHDGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.BHDGameController"
local GameFlowProcessor = require "DFM.YxFramework.Managers.GameFlow.Processor.GameFlowProcessor"
local UGameFlowDelegates = import "GameFlowDelegates"
local UGameplayStatics = import "GameplayStatics"
local UDFMGameLoadingManager = import("DFMGameLoadingManager")

local DFMGameLoadingManager = import("DFMGameLoadingManager").GetGameLoadingManager(GetGameInstance())
local bEnablePostProcessGame2FrontEnd = true

function GameFlowManager:Ctor()
    local gameInst = GetGameInstance()
    local gameflowDelegatesIns = UGameFlowDelegates.GetGameFlowDelegates(gameInst)
    ---@type EGameFlowStageType
    self._curGameFlow = gameflowDelegatesIns:GetCurGameFlowStage()
    self._preGameFlow = gameflowDelegatesIns:GetPreGameFlowStage()

    self._stageSignalEmitter = StageSignalEmitter:Get()
    self._inGameController = InGameController:Get()
    if DFHD_LUA == 1 then
        self._bhdGameController = BHDGameController:Get()
    end

    self._enterHandler = gameflowDelegatesIns.OnEnterGameFlowStage:Add(CreateCPlusCallBack(self.OnEnterGameFlowStage, self))
    self._leaveHandler = gameflowDelegatesIns.OnLeaveGameFlowStage:Add(CreateCPlusCallBack(self.OnLeaveGameFlowStage, self))

    self._procssor = GameFlowProcessor:NewIns()

    self._bIsWaitingForLoading = false

    if DFMGameLoadingManager then
        self._enterTargetLevelHandler = DFMGameLoadingManager.OnEnterTargetLevel:Add(CreateCPlusCallBack(self.OnEnterTargetLevel, self))
    end
end

function GameFlowManager:Destroy()
    ---@type EGameFlowStageType
    self._curGameFlow = EGameFlowStageType.None
    self._preGameFlow = EGameFlowStageType.None
    local gameInst = GetGameInstance()
    if isvalid(self._enterHandler) then
        UGameFlowDelegates.GetGameFlowDelegates(gameInst).OnEnterGameFlowStage:Remove(self._enterHandler)
    end
    if isvalid(self._leaveHandler) then
        UGameFlowDelegates.GetGameFlowDelegates(gameInst).OnLeaveGameFlowStage:Remove(self._leaveHandler)
    end
    self._stageSignalEmitter:Release()
    self._inGameController:Release()
    if DFHD_LUA == 1 then
        self._bhdGameController:Release()
    end
    
    releaseobject(self._procssor)

    if isvalid(self._enterTargetLevelHandler) then
        DFMGameLoadingManager.OnEnterTargetLevel:Remove(self._enterTargetLevelHandler)
    end
end

--------------------------------------------------------------------------
--- Game Flow (API)
--------------------------------------------------------------------------
--- 获取当前所在的GameFlow
function GameFlowManager:GetCurrentGameFlow()
    return self._curGameFlow
end

--- 获取上一个已经leave的GameFlow
function GameFlowManager:GetPreGameFlow()
    return self._preGameFlow
end

--------------------------------------------------------------------------
--- Game Flow (Event Invoke)
--------------------------------------------------------------------------
__DebugOnly_GameFlowName = function(gameFlowType)
if gameFlowType == EGameFlowStageType.None then return "EGameFlowStageType.None" end
if gameFlowType == EGameFlowStageType.Hotfix then return "EGameFlowStageType.Hotfix" end
if gameFlowType == EGameFlowStageType.LaunchToLogin then return "EGameFlowStageType.LaunchToLogin" end
if gameFlowType == EGameFlowStageType.Login then return "EGameFlowStageType.Login" end
if gameFlowType == EGameFlowStageType.LoginToLobby then return "EGameFlowStageType.LoginToLobby" end
if gameFlowType == EGameFlowStageType.Lobby then return "EGameFlowStageType.Lobby" end
if gameFlowType == EGameFlowStageType.LobbyToGame then return "EGameFlowStageType.LobbyToGame" end
if gameFlowType == EGameFlowStageType.Game then return "EGameFlowStageType.Game" end
if gameFlowType == EGameFlowStageType.GameToLobby then return "EGameFlowStageType.GameToLobby" end
if gameFlowType == EGameFlowStageType.Discovery then return "EGameFlowStageType.Discovery" end
if gameFlowType == EGameFlowStageType.SafeHouse then return "EGameFlowStageType.SafeHouse" end
if gameFlowType == EGameFlowStageType.LoginToModeHall then return "EGameFlowStageType.LoginToModeHall" end
if gameFlowType == EGameFlowStageType.ModeHall then return "EGameFlowStageType.ModeHall" end
if gameFlowType == EGameFlowStageType.ModeHallToLobby then return "EGameFlowStageType.ModeHallToLobby" end
if gameFlowType == EGameFlowStageType.ModeHallToSafeHouse then return "EGameFlowStageType.ModeHallToSafeHouse" end
if gameFlowType == EGameFlowStageType.SafeHouseToGame then return "EGameFlowStageType.SafeHouseToGame" end
if gameFlowType == EGameFlowStageType.GameToSafeHouse then return "EGameFlowStageType.GameToSafeHouse" end
if gameFlowType == EGameFlowStageType.GameToGame then return "EGameFlowStageType.GameToGame" end
if gameFlowType == EGameFlowStageType.LobbyToLogin then return "EGameFlowStageType.LobbyToLogin" end
if gameFlowType == EGameFlowStageType.LoginToIntro then return "EGameFlowStageType.LoginToIntro" end
if gameFlowType == EGameFlowStageType.Intro then return "EGameFlowStageType.Intro" end
if gameFlowType == EGameFlowStageType.IntroToSafeHouse then return "EGameFlowStageType.IntroToSafeHouse" end
if gameFlowType == EGameFlowStageType.GameSettlement then return "EGameFlowStageType.GameSettlement" end
if gameFlowType == EGameFlowStageType.PostLaunch then return "EGameFlowStageType.PostLaunch" end
return tostring(gameFlowType)
end

--- 兼容旧逻辑，暂时仅分为Game和FrontEnd，之后再细分支持
function GameFlowManager:CheckIsInFrontEnd(gameFlowType)
    gameFlowType = setdefault(gameFlowType, self:GetCurrentGameFlow())
    local bInFrontEnd = InGameFlowTypes[gameFlowType] == nil
    --- 尝试兼容旧逻辑
    if bInFrontEnd then
        return true
    else
        return false
    end
    -- logframe('[ GameFlow Debug ] ******* GameFlowManager.CheckIsInFrontEnd', bInFrontEnd, '-------CurrentFlowType: ', __DebugOnly_GameFlowName(gameFlowType))
    -- return bInFrontEnd
end

function GameFlowManager:CheckIsInLoadingGameFlow()
    return GF_CheckIsLoadingFlowType(self._curGameFlow)
end

function GameFlowManager:CheckIsInBHDGame()
    if DFHD_LUA == 1 then
        if self._bhdGameController then
            return GF_CheckIsBHDInGameConnectType(self._bhdGameController.connectBHDState)
        end
    end
end

function GameFlowManager:IsInFrontendFromProcessor()
    if isinvalid(self._procssor) then
        return false
    end
    local bResult = self._procssor:IsInFrontEnd()
    return bResult
end

--- 通知先到达Manager层，再由Manager向业务层分发
--- 一次主流程切换包含一个Leave和一个Enter
---@param gameFlowType EGameFlowStageType
function GameFlowManager:OnLeaveGameFlowStage(gameFlowType)
    if self._preGameFlow ~= gameFlowType then
        logframe('[ GameFlow Debug ] ******* GameFlowManager.OnLeaveGameFlowStage', __DebugOnly_GameFlowName(gameFlowType), '-------Leave')
        ---@type EGameFlowStageType
        self._preGameFlow = clone(self._curGameFlow)
        self._curGameFlow = EGameFlowStageType.None
        LuaGlobalEvents.evtGameFlowChangeLeave:Invoke(gameFlowType)

        self._procssor:ProcessLeave(gameFlowType)
    end
end

function GameFlowManager:OnEnterTargetLevel()
    logwarning("GameFlowManager:OnEnterTargetLevel()")
    if bEnablePostProcessGame2FrontEnd then
        if self._bIsWaitingForLoading then
            self._bIsWaitingForLoading = false

            logwarning("GameFlowManager: Process GameflowEnter After TargetLevelEnter!")
            if isvalid(self) and isvalid(self._procssor) then
                self._procssor:ProcessEnter(self._curGameFlow)
            else
                logerror("GameFlowManager:OnEnterTargetLevel _procssor is invalid!")
            end
        end
    end
end

---@param gameFlowType EGameFlowStageType
function GameFlowManager:OnEnterGameFlowStage(gameFlowType)
    if self._curGameFlow ~= gameFlowType then
        logframe('[ GameFlow Debug ] ******* GameFlowManager.OnEnterGameFlowStage', __DebugOnly_GameFlowName(gameFlowType), '-------Enter')
        ---@type EGameFlowStageType
        self._curGameFlow = gameFlowType
        LuaGlobalEvents.evtGameFlowChangeEnter:Invoke(gameFlowType)

        if self:CanProcessFlowStage(gameFlowType) then
            self._procssor:ProcessEnter(gameFlowType)
        end

        -- if bEnablePostProcessGame2FrontEnd then
        --     if gameFlowType == EGameFlowStageType.GameToLobby or gameFlowType == EGameFlowStageType.GameToSafeHouse then
        --         -- [aidenliao] 出局时不能立即触发，将会导致触发大量预加载请求，使得内存峰值过高造成崩溃
        --         self._bIsWaitingForLoading = true
        --         logwarning("GameFlowManager: Is waiting for EnterTargetLevel broadcast!")
        --     else
        --         -- 不属于登录流程的话可以直接触发
        --         self._procssor:ProcessEnter(gameFlowType)
        --     end
        -- else
        --     self._procssor:ProcessEnter(gameFlowType)
        -- end

    end
end

-- 判断是否能process
function GameFlowManager:CanProcessFlowStage(gameFlowType)
    if bEnablePostProcessGame2FrontEnd then
        if gameFlowType == EGameFlowStageType.GameToLobby or gameFlowType == EGameFlowStageType.GameToSafeHouse then
            -- [aidenliao] 出局时不能立即触发，将会导致触发大量预加载请求，使得内存峰值过高造成崩溃
            self._bIsWaitingForLoading = true
            logwarning("GameFlowManager: Is waiting for EnterTargetLevel broadcast!")
            return false
        end
    end

    if DFMGameLoadingManager and DFMGameLoadingManager:IsEnablePostProcessLogin2FrontEnd() then
        if gameFlowType == EGameFlowStageType.LoginToModeHall or gameFlowType == EGameFlowStageType.LoginToLobbyBHD then
            if self._preGameFlow == EGameFlowStageType.Game then
                -- 从局内返回到大厅的特殊情况
                self._bIsWaitingForLoading = true
            end
            logwarning("GameFlowManager: Is waiting for Login Finish And EnterTargetLevel broadcast!")
            return false
        end
    end

    -- 除了以上两种特殊情况外，其余情况均可以正常触发Enter
    return true
end

-- 流程侧主动触发这个流转，依赖登录成功
-- 注意，这里需要再触发一次LoginToModeHall，主要是因为前序的流程后注册的业务实际上是没有监听到的，需要在这里补上，否则会导致流程资源异常
function GameFlowManager:ProcessLogin2FrontEndManually()
    logwarning("GameFlowManager:ProcessLogin2FrontEnd()")
    -- 再触发一次GameFlow的流转事件存在风险，先不这么做
    -- 使用当前目标流程类型进行处理，兼容 LoginToModeHall / LoginToLobbyBHD 等
    local targetFlow = self._curGameFlow or EGameFlowStageType.LoginToModeHall
    self._procssor:ProcessEnter(targetFlow) -- 主动触发登录到前端流程，此时大厅业务理论上已经初始化完毕了
end

--------------------------------------------------------------------------
--- InGameController (API)
--------------------------------------------------------------------------
function GameFlowManager:GetPlayerController()
    local gameInst = GetGameInstance()
    if self._inGameController:GetIsInited() then
        return self._inGameController:GetGPPlayerController()
    else
        -- return UGameplayStatics.GetPlayerController(gameInst, 0)
        return nil
    end
end

function GameFlowManager:GetCharacter()
    if self._inGameController:GetIsInited() then
        return self._inGameController:GetGPCharacter()
    else
        return nil
    end
end

function GameFlowManager:GetPlayerState()
    local pc = self:GetPlayerController()
    if isvalid(pc) and isvalid(pc.PlayerState) then
        return pc.PlayerState
    end

    return nil
end

function GameFlowManager:GetPlayerUin()
    local ps = self:GetPlayerState()
    if ps then
        return ps.Uin
    end

    return 0
end

function GameFlowManager:GetOBPlayerUin()
    local obInvMgr = Facade.GameFlowManager:GetOBInvMgr()
    if isvalid(obInvMgr) then
        return obInvMgr.PlayerUin
    end

    return 0
end

function GameFlowManager:GetCharacterInventoryMgr()
    if self._inGameController:GetIsInited() then
        return self._inGameController:GetGPCharacterInventoryMgr()
    else
        return nil
    end
end

function GameFlowManager:GetPlayerSettlementComponent()
    if self._inGameController:GetIsInited() then
        return self._inGameController:GetPlayerSettlementComponent()
    else
        return nil
    end
end

function GameFlowManager:GetOBInvMgr()
    local controller = Facade.GameFlowManager:GetPlayerController()
    if isvalid(controller) and controller.bIsInObserverMode then
        return controller.OBInventoryManager
    end
end

function GameFlowManager:IsInOBMode()
    local controller = Facade.GameFlowManager:GetPlayerController()
    if isvalid(controller) then
        return controller.bIsInObserverMode == true
    end
    return false
end

function GameFlowManager:GetInputManager()
    return self._inGameController and self._inGameController:GetInputManager()
end

function GameFlowManager:GetIsInGameControlInited()
    return self._inGameController:GetIsInited()
end

--------------------------------------------------------------------------
--- Sub Stage (API) 分开接口，当前未启用
--------------------------------------------------------------------------
--- 一次子阶段切换包含一个Leave和一个Enter
--- 准备开始做当前子阶段的预准备事情前,调用此接口
function GameFlowManager:LeaveSubStage()
    return self._stageSignalEmitter:StartLeavingStage()
end

--- 做完当前子阶段的预准备事情后,调用此接口
-- -@param nextStageType ESubStage
-- function GameFlowManager:EnterSubStage(nextStageType)
--     if nextStageType ~= self:GetCurrentSubStage() then
--         self._stageSignalEmitter:FinishEnteringStage(nextStageType)
--     end
-- end

--------------------------------------------------------------------------
--- Sub Stage (API) 合并接口，当前生效
--------------------------------------------------------------------------
---@param nextStageType ESubStage
function GameFlowManager:EnterSubStage(nextStageType)
    if nextStageType ~= self:GetCurrentSubStage() then
        self._stageSignalEmitter:StartLeavingStage()
        self._stageSignalEmitter:FinishEnteringStage(nextStageType)
    end
end

--- 获取当前所在的子阶段
function GameFlowManager:GetCurrentSubStage()
    return self._stageSignalEmitter:GetCurrentStage()
end

--- 获取上一个已经leave的子阶段
function GameFlowManager:GetPreSubStage()
    return self._stageSignalEmitter:GetPreStage()
end


--------------------------------------------------------------------------
--- Game Check (API)
--------------------------------------------------------------------------
function GameFlowManager:CheckIsInGame(bIncludeLoading, bIncludeBHD)
    bIncludeLoading = setdefault(bIncludeLoading, false)
    bIncludeBHD = setdefault(bIncludeBHD, false)
    
    local bFinalResult = true
    local bInGame = self:GetCurrentGameFlow() == EGameFlowStageType.Game
    if bIncludeLoading then
        local DFMGameLoadingManager = UDFMGameLoadingManager.GetGameLoadingManager(GetGameInstance())
        local bInDFLoading = false
        if DFMGameLoadingManager then
            bInDFLoading = DFMGameLoadingManager:IsLoading()
        end
        bFinalResult = bFinalResult or bInDFLoading
    end
    if bIncludeBHD then
        local bInBHDGame = self:CheckIsInBHDGame()
        bFinalResult = bFinalResult or bIncludeBHD
    end
    bFinalResult = bFinalResult or bInGame
end

--------------------------------------------------------------------------
--- BHDGameController (API)
--------------------------------------------------------------------------
function GameFlowManager:GetBHDGameController()
    return self._bhdGameController
end

function GameFlowManager:CheckIsInBHDGame()
    local bInBHDGame = false
    if DFHD_LUA == 1 then
        local bhdGameController = self._bhdGameController
        if bhdGameController then
            if bhdGameController.connectBHDState == EDFConnectBHDState.WaitBHDStart or bhdGameController.connectBHDState == EDFConnectBHDState.BHDStartSuccess then
                bInBHDGame = true
            end
            logwarning("GameFlowManager:CheckIsInBHDGame() connectBHDState, bInBHDGame:", __DebugOnly_ConnectBHDStateName(bhdGameController.connectBHDState), bInBHDGame)
            return bInBHDGame
        end
    end
    -- logwarning("GameFlowManager:CheckIsInBHDGame() bInBHDGame:", bInBHDGame)
    return bInBHDGame
end

--------------------------------------------------------------------------
--- Reset
--------------------------------------------------------------------------
function GameFlowManager:Reset(bResetIGC)
    self._stageSignalEmitter:ResetStage()
end

---@param gameFlowType EGameFlowStageType
function GameFlowManager:OnGameFlowChangeLeave(gameFlowType)
    self:Reset()

    -- 垃圾回收通过【MemTool.CleanAllCppObjects】驱动，GameFlowChange不再调用GC
    -- collectgarbage("collect")
    -- collectgarbage("collect")
end

---@param gameFlowType EGameFlowStageType
function GameFlowManager:OnGameFlowChangeEnter(gameFlowType)
end

-- 当从Loading重启虚拟机后，需要恢复当前GameFlow的状态，对应数据从C++读取
function GameFlowManager:RecoveryState(bEnableDispatch)
    local gameInst = GetGameInstance()
    self._curGameFlow = UGameFlowDelegates.GetGameFlowDelegates(gameInst):GetCurGameFlowStage()
    self._preGameFlow = UGameFlowDelegates.GetGameFlowDelegates(gameInst):GetPreGameFlowStage()

    if (bEnableDispatch) then
        LuaGlobalEvents.evtGameFlowChangeEnter:Invoke(self._curGameFlow)
        self._procssor:ProcessEnter(self._curGameFlow)
    end
end

function GameFlowManager:OnPostManagerCleanUp()
	self:Reset()
end

function GameFlowManager:OnLoadingFrontend2Game(gameFlowType, context)
    logframe('#[ GFChange VS WorldCleanUp ]----------------------------------------CleanAllCppObjects【OnLoadingFrontend2Game】',
    ' gameFlowType:', __DebugOnly_GameFlowName(gameFlowType),
    'worldName:', UKismetSystemLibrary.GetObjectName(GetWorld()))
    MemTool.CleanAllCppObjects()
end

function GameFlowManager:OnLoadingGame2Frontend(gameFlowType, context)
    logframe('#[ GFChange VS WorldCleanUp ]----------------------------------------CleanAllCppObjects【OnLoadingGame2Frontend】',
    ' gameFlowType:', __DebugOnly_GameFlowName(gameFlowType),
    'worldName:', UKismetSystemLibrary.GetObjectName(GetWorld()))
    trycall(MemTool.CleanAllCppObjects, MemTool)
end

return GameFlowManager
