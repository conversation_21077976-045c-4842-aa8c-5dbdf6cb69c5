----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCommonWidget)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
--输入设备相关
local UGPInputDelegates = import "GPInputDelegates"
local EGPInputType = import"EGPInputType"
local UGPInputHelper = import "GPInputHelper"

-- Console查看用户档案
local UDFMGameUrlGenerator = import "DFMGameUrlGenerator"
local UDFMPlatformUtils = import "DFMPlatformUtils"
-- END MODIFICATION
local UGameplayStatics = import "GameplayStatics"

--------------------------------------------------------------------------
--- 绑定通用头像组件
--------------------------------------------------------------------------
---@class CommonTipsPlayer : LuaUIBaseView
local CommonTipsPlayer = ui('CommonTipsPlayer')
local TeamSvr = Server.TeamServer
local CommonConfig = Module.CommonWidget.Config
local FAnchors = import "Anchors"

function CommonTipsPlayer:Ctor()
    self._wtPlayerName = self:Wnd("wtPlayerName", UITextBlock)
    self._wtBox = self:Wnd("wtBtnListBox", UIWidgetBase)
    self.m_Root = self:Wnd("wtRoot", UIWidgetBase)
    self._wtCanvasPanel = self:Wnd("wtMainPanel", UIImage)
    self._wtHeadIcon = self:Wnd("WBP_CommonHeadIcon", UIWidgetBase)
    self._wtMilitaryImage = self:Wnd("DFImage_201", UIImage)
    self._wtRankIcon = self:Wnd("wtRankIcon", UIImage)
    self._wtRankText = self:Wnd("DFTextBlock_89", UITextBlock)
    ---@type RoleInfoTitleItem_Small
    self._wtTitle = self:Wnd("WBP_RoleInfo_TitleItem_Small", UIWidgetBase)
    self._wtRankStarIcon=self:Wnd("DFImage_Star",UIImage)
    self._wtRankStarNum=self:Wnd("DFTextBlock",UITextBlock)

    self._wtRemark = self:Wnd("wtRemark", UIButton)
    self._wtRemarkImg = self:Wnd("wtRemarkImg", UIButton)
    --- BEGIN MODIFICATION @ VIRTUOS
    if IsConsole() then
        self._wtPlatformIcon = self:Wnd("wtPlatformIcon", UIImage)
    end
    --- END MODIFICATION
    
    self._fCallbackIns = nil
    --- BEGIN MODIFICATION @ VIRTUOS
    -- 点赞按钮
    if IsHD() then
        self._wtMatesPraiseBtn = nil
    end
    --- END MODIFICATION
    self._createTbl = {
        [HeadButtonType.AddFriend] = self.CreateFriendBtn,
        [HeadButtonType.InviteAppTeam] = self.CreateInviteTeamBtn,
        [HeadButtonType.PlayerInformat] = self.CreatePlayerInformaBtn,
        [HeadButtonType.KickTeam] = self.CreateKickTeamBtn,
        [HeadButtonType.Chat] = self.CreateChatBtn,
        [HeadButtonType.AddBlack] = self.CreateAddBlackBtn,
        [HeadButtonType.Report] = self.CreateReportBtn,
        [HeadButtonType.PromotTeam] = self.CreatePromoTeamBtn,
        [HeadButtonType.KickRoom] = self.CreateKickRoomBtn,
        [HeadButtonType.ReportVoice] = self.CreateReportVoiceBtn,
        [HeadButtonType.DelFriend] = self.CreateDelFriendBtn,
        [HeadButtonType.Transferor] = self.CreateTransferorBtn, 
	    --- BEGIN MODIFICATION @ VIRTUOS
        [HeadButtonType.MatesPraise] = self.CreateMatesPraiseBtn,
        --- END MODIFICATION     
        [HeadButtonType.IngameSilence] = self.CreateIngameSilenceBtn,
        [HeadButtonType.IngameGiveLeader] = self.CreateIngameGiveLeaderBtn,
        [HeadButtonType.IngameGetLeader] = self.CreateIngameGetLeaderBtn,
        [HeadButtonType.IngameImpeachLeader] = self.CreateIngameImpeachLeaderBtn,
        [HeadButtonType.IngameGetCommander] = self.CreateIngameGetCommanderBtn,
        [HeadButtonType.IngameImpeachCommander] = self.CreateIngameImpeachCommanderBtn,
        [HeadButtonType.IngameAddFriend] = self.CreateIngameAddFriendBtn,
        [HeadButtonType.IngameReport] = self.CreateIngameReportBtn,
        [HeadButtonType.IngameCommanderChannel] = self.CreateIngameCommanderChannelBtn,
        [HeadButtonType.Tribute] = self.CreateTributeBtn,
    }

    --- BEGIN MODIFICATION @ VIRTUOS
    if IsConsole() then
        -- Console平台查看用户档案
        self._createTbl[HeadButtonType.OpenPlatformProfile] = self.CreatePlatformProfileBtn
    end
    --- END MODIFICATION


    self._sortBtnList = {
        HeadButtonType.Tribute,
        HeadButtonType.PlayerInformat,
        HeadButtonType.AddFriend,
        HeadButtonType.InviteAppTeam,
        HeadButtonType.KickTeam,
        HeadButtonType.PromotTeam,
        HeadButtonType.KickRoom,
        HeadButtonType.Transferor,
        HeadButtonType.DelFriend,
        HeadButtonType.Chat,
        HeadButtonType.AddBlack,
        HeadButtonType.Report,
        HeadButtonType.ReportVoice,
	    --- BEGIN MODIFICATION @ VIRTUOS
        HeadButtonType.MatesPraise,
        --- END MODIFICATION
        HeadButtonType.IngameSilence,
        HeadButtonType.IngameGiveLeader,
        HeadButtonType.IngameGetLeader,
        HeadButtonType.IngameImpeachLeader,
        HeadButtonType.IngameGetCommander,
        HeadButtonType.IngameImpeachCommander,
        HeadButtonType.IngameAddFriend,
        HeadButtonType.IngameReport,
        HeadButtonType.IngameCommanderChannel,
        
    }

    --- BEGIN MODIFICATION @ VIRTUOS
    if IsConsole() then
        -- Console平台查看用户档案
        table.insert(self._sortBtnList, HeadButtonType.OpenPlatformProfile)
    end
    --- END MODIFICATION
end


---@type btnList用以定义动态生成的按钮
--[[
local btnTbl =  {
    HeadButtonType.AddFriend,
    HeadButtonType.InviteAppTeam,
    HeadButtonType.PlayerInformat,
    HeadButtonType.KickTeam,
    HeadButtonType.Chat,
    HeadButtonType.AddBlack,
    HeadButtonType.Report,
    HeadButtonType.PromotTeam,
    HeadButtonType.KickRoom,
}
]]
function CommonTipsPlayer:OnInitExtraData(info, parent, btnList, friendSource, teamSource, reportStr, isSimple, createPoint, CallbackIns)
    self._hashBtnTbl = {}
    self._btnTbl = {}
    if btnList then
        --- BEGIN MODIFICATION @ VIRTUOS: Console平台查看用户档案
        if IsConsole() then
            table.insert(btnList, HeadButtonType.OpenPlatformProfile)
        end
        --- END MODIFICATION
        
        for index, value in ipairs(btnList) do
            self._hashBtnTbl[value] = true
        end
    end
    for _, btnType in ipairs(self._sortBtnList) do
        if self._hashBtnTbl[btnType] then
            table.insert(self._btnTbl, btnType)
        end
    end
    self._isSimple = setdefault(isSimple, false)
    self._reportStr = reportStr
    self._parent = parent
    self._btnHashTbl = {}
    self._position = createPoint
    self._need_fake_plat = info.need_fake_plat
    if CallbackIns then
        self._fCallbackIns = CallbackIns
    end
    for index, value in ipairs(self._btnTbl) do
        self._btnHashTbl[value] = true
    end
    self._friendSource = friendSource or FriendApplySource.InvalidApply
    self._teamSource = teamSource or TeamInviteSource.FromFriend
    self:SetInfo(info, parent, btnList)

    if not isSimple then
        if Module.Friend:CheckIsFriend(info.player_id) then
            self._wtRemark:Event("OnClicked",self._OnClickRemark ,self)
            self._wtRemark:Visible()
            self._wtRemarkImg:Visible()
        else
            self._wtRemark:HitTestInvisible()
            self._wtRemarkImg:Collapsed()
        end
    end
end

--- BEGIN MODIFICATION @ VIRTUOS
function CommonTipsPlayer:OnShowBegin()
    self:AddLuaEvent(Server.TeamServer.Events.evtTeammateExitTeam, self._OnTeammateExitTeam, self)
end

function CommonTipsPlayer:OnHideBegin()
    self:RemoveAllLuaEvent()
end

function CommonTipsPlayer:_OnTeammateExitTeam(playerID)
    if not self._playerInfo or self._playerInfo.player_id ~= playerID then
        return
    end

    Facade.UIManager:CloseUI(self)
end

function CommonTipsPlayer:_OnCanceled()
    Facade.UIManager:CloseUI(self)
end

function CommonTipsPlayer:_OnClickRemark()
    Facade.UIManager:AsyncShowUI(UIName2ID.FriendChangeNote, nil, nil,self._playerInfo)
    Facade.UIManager:CloseUI(self)
end
--- END MODIFICATION

function CommonTipsPlayer:SetInfo(info)
    self._reportType = setdefault(info.report_type, 1) 
    self._playerInfo = {
        player_id = info.player_id,
        nick_name = info.nick_name or "",
        level = info.level or 1,
        pic_url = info.pic_url or "",
        safehouse_degree = info.safehouse_degree or 0,
        state = info.state or GlobalPlayerStateEnums.EPlayerState_Online,
        military_tag = info.military_tag or 0,
        rank_score = info.rank_score or 0,
        rank_attended = info.rank_attended ~= nil and info.rank_attended or false,
        title = info.title and info.title or 0,
	    rank_title_adcode = info.rank_title_adcode and info.rank_title_adcode or 0,
        rank_title_rank_no = info.rank_title_rank_no and info.rank_title_rank_no or 0,
        bIsPraise = info.bIsPraise ~= nil and info.bIsPraise or false,
        friendPanelType = info.friendPanelType or 1,
        show_commander_rank_points = info.show_commander_rank_points,
        mp_commander_score = info.mp_commander_score
    }
    --- BEGIN MODIFICATION @ VIRTUOS
    -- 增加平台logo
    if IsConsole() then
        self._playerInfo.plat_id = info.plat or info.plat_id or PlatIDType.Plat_Invalid
    end
    --- END MODIFICATION
    self:Collapsed()
    self:RefreshSocialPanel()
    if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.GameSettlement then
        self:CreateButtonList()
    else
        self:UpdatePlayerState()
    end
end

function CommonTipsPlayer:UpdatePlayerState()
    local tipHandle = CreateCallBack(self.GetPlayerState, self)
    Server.SocialServer:ReqTipsPlayerState(self._playerInfo.player_id,tipHandle)
end

function CommonTipsPlayer:RefreshSocialPanel()
    if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.LobbyBHD then--BHD名片小窗特殊逻辑
        self._wtRankIcon:Collapsed()
        self._wtRankText:Collapsed()
        self._wtRankStarIcon:Collapsed()
        self._wtRankStarNum:Collapsed()
        self._playerInfo.level = nil
    end

    if not self._isSimple then
        self._wtHeadIcon:InitPortrait(self._playerInfo, HeadIconType.HeadNore)
        self._wtHeadIcon:HitTestInvisible()
        if self._playerInfo.nick_name then
            local remarkName = Server.FriendServer:GetFriendRemarkById(self._playerInfo.player_id)
            if remarkName ~= "" then
                self._wtPlayerName:SetText(string.format(Module.Friend.Config.QQFriend, remarkName, self._playerInfo.nick_name))
            else
                self._wtPlayerName:SetText(self._playerInfo.nick_name)
            end
        end
        -- BEGIN MODIFICATION @ VIRTUOS : OnlineId
        if IsPS5Family() and self._playerInfo.plat_id == PlatIDType.Plat_Playstation then
            local callback = function(PS5OnlineId)
                self._wtPlayerName:SetText(PS5OnlineId)
            end
            Module.Social:AsyncGetPS5OnlineIdByUID(self._playerInfo.player_id, callback, self)
        end
        -- END MODIFICATION
        self._wtMilitaryImage:AsyncSetImagePath(Module.RoleInfo:GetMilitary(self._playerInfo.military_tag), true)
        if self._playerInfo.title and self._playerInfo.title > 0 then
            self._wtTitle:HitTestInvisible()
            self._wtTitle:UpdateByTitleId(self._playerInfo.title, self._playerInfo.rank_title_adcode, self._playerInfo.rank_title_rank_no)
        else
            self._wtTitle:Collapsed()
        end

        --- BEGIN MODIFICATION @ VIRTUOS
        if IsConsole() and self._wtPlatformIcon then
            local platID = self._playerInfo.plat_id

            local platIconPath = Module.Friend:GetPlatformIconPath(platID, false, false)
            if self._need_fake_plat then
                platIconPath = Module.Friend:GetPlatformIconPath(platID)
            end

            if platIconPath then
                self._wtPlatformIcon:AsyncSetImagePath(platIconPath, false)
                self._wtPlatformIcon:SelfHitTestInvisible()
            else
                self._wtPlatformIcon:Collapsed()
            end
        end
        --- END MODIFICATION

        self:RankInfoInit()
    end
end

function CommonTipsPlayer:RankInfoInit()
    if Facade.GameFlowManager:GetCurrentGameFlow() ~= EGameFlowStageType.SafeHouse then
        local showCommander = self._playerInfo.show_commander_rank_points
        -- 基于该变量是否为1判断，此时展示指挥官模式段位信息
        if showCommander and showCommander == 1 then
            self:SetCommanderRankInfo()
            return
        end
    end


    if self._playerInfo.rank_attended then
        self._wtRankIcon:Visible()
        self._wtRankStarIcon:SelfHitTestInvisible()
        self._wtRankStarNum:SelfHitTestInvisible()
        if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse then
            self._wtRankIcon:SetRankingIconByScore(self._playerInfo.rank_score)
            local rankInfo = Module.Ranking:GetMinorDataByScore(self._playerInfo.rank_score)
            if rankInfo then
                self._wtRankText:SetText(rankInfo.RankName and rankInfo.RankName or Module.RoleInfo.Config.Loc.NoRankDataTxt)
            else
                self._wtRankText:SetText(Module.RoleInfo.Config.Loc.NoRankDataTxt)
            end
            self._wtRankStarNum:SetText(Module.Ranking:GetStarNumByScore(self._playerInfo.rank_score) or "?")
        else
            self._wtRankIcon:SetTournamentIconByScore(self._playerInfo.rank_score)
            local rankInfo =  Module.Tournament:GetRankDataByScore(self._playerInfo.rank_score)
            if rankInfo then
                self._wtRankText:SetText(rankInfo.Name)
            else
                self._wtRankText:SetText(Module.RoleInfo.Config.Loc.NoRankDataTxt)
            end
            self._wtRankStarNum:SetText(Module.Tournament:GetStarNumByScore(self._playerInfo.rank_score) or "?")
        end
    else
        self._wtRankIcon:SetRankIconNone()
        self._wtRankText:SetText(Module.RoleInfo.Config.Loc.NoRankDataTxt)
        self._wtRankStarIcon:Collapsed()
        self._wtRankStarNum:Collapsed()
    end
end

function CommonTipsPlayer:SetCommanderRankInfo()
    local mpCommanderScore = self._playerInfo.mp_commander_score
    if mpCommanderScore then
        self._wtRankIcon:Visible()
        self._wtRankStarIcon:SelfHitTestInvisible()
        self._wtRankStarNum:SelfHitTestInvisible()
        self._wtRankIcon:SetCommanderIconByScore(mpCommanderScore)
        local rankInfo = Module.Tournament:GetCommanderRankDataByScore(mpCommanderScore)
        if rankInfo then
            self._wtRankText:SetText(rankInfo.Name and rankInfo.Name or Module.RoleInfo.Config.Loc.NoRankDataTxt)
        else
            self._wtRankText:SetText(Module.RoleInfo.Config.Loc.NoRankDataTxt)
        end
        self._wtRankStarNum:SetText(Module.Tournament:GetCommanderStarNumByScore(mpCommanderScore) or "?")
    else
        self._wtRankIcon:SetRankIconNone()
        self._wtRankText:SetText(Module.RoleInfo.Config.Loc.NoRankDataTxt)
        self._wtRankStarIcon:Collapsed()
        self._wtRankStarNum:Collapsed()
    end
end

function CommonTipsPlayer:GetPlayerState(info)
    if  info.state_info then
        self._playerInfo.state = info.state_info.state
        local inTeam = (self._playerInfo.state & GlobalPlayerStateEnums.EPlayerState_InTeam) ~= 0
        self._playerInfo.team_id = inTeam and info.state_info.team_id or 0
        self._playerInfo.member_num = inTeam and info.state_info.member_num or 0
    end

    if info.nick_name ~= "" then
        self._playerInfo.nick_name = info.nick_name
    end
    self._playerInfo.pic_url = info.pic_url
    self._playerInfo.military_tag = info.military_tag
    self._playerInfo.title = info.title and info.title or 0
    self._playerInfo.rank_title_adcode = info.rank_title_adcode and info.rank_title_adcode or 0
    self._playerInfo.rank_title_rank_no = info.rank_title_rank_no and info.rank_title_rank_no or 0

    if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse then
        self._playerInfo.rank_score = info.sol_rank_score
        self._playerInfo.rank_attended = info.sol_rank_attended
        if info.season_level ~= 0 then
            self._playerInfo.level = info.season_level 
        end
    else
        self._playerInfo.rank_score = info.mp_rank_score
        self._playerInfo.rank_attended = info.mp_rank_attended
        if info.level ~= 0 then
            self._playerInfo.level = info.level 
        end
    end
    self:RefreshSocialPanel()
    self:CreateButtonList()
end

function CommonTipsPlayer:CreateButtonList()
    loginfo("CommonTipsPlayer:CreateButtonList")
    for _, value in ipairs(self._btnTbl) do
        if value==HeadButtonType.Report then
            if Module.ModuleSwitcher:CheckModuleSwitcher(SwitchSystemID.SwitchSyetemTssReport) == EFirstLockResult.Open then
                self._createTbl[value](self)
            end
        else
            self._createTbl[value](self)
        end
    end
    --- BEGIN MODIFICATION @ VIRTUOS: Update navigation focus
    if IsHD() and self._navGroup then
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroup)
    end
    --- END MODIFICATION
    self:DelayFreshMiniDetailPos()
end

function CommonTipsPlayer:DelayFreshMiniDetailPos()
    if self._position then
        Timer.DelayCall(0.1, self._UpdatePosition, self, self._position)
    else
        Timer.DelayCall(0.1, self._UpdateDetailViewPosition, self)
    end
end

function CommonTipsPlayer:_OnMouseButtonDown(mouseEvent)
    local sceenPos = mouseEvent:GetScreenSpacePosition()
    local geometry = self._wtCanvasPanel:GetCachedGeometry()
    local isUnder = USlateBlueprintLibrary.IsUnderLocation(geometry, sceenPos)
    loginfo("CommonTipsPlayer:_OnMouseButtonDown")
    if not isUnder then
        loginfo("CommonTipsPlayer:_OnMouseButtonDown is In")
        Facade.UIManager:CloseUI(self)
    end
end


--------------------------------------------
--创建按钮 绑定事件
--------------------------------------------

function CommonTipsPlayer:CreateBtn(txt, image, func)
    if not self._isSimple then

        local weakUIIns = Facade.UIManager:AddSubUI(self, UIName2ID.CommonTipsPlayerTextBtn, self._wtBox, nil, txt, image, func)
--[[
        local weakUIIns = Facade.UIManager:AddSubUI(self, UIName2ID.DFCommonIconButton, self._wtBox)

        local btn = getfromweak(weakUIIns)
        btn:AsyncSetImageIconPathAllState(image, true)
        btn:Event("OnClicked", func, self)
        ]]
        local btn = getfromweak(weakUIIns)
        return btn
    else
        local weakUIIns = Facade.UIManager:AddSubUI(self, UIName2ID.CommonTipsPlayerBtn, self._wtBox)
        local btn = getfromweak(weakUIIns)
        btn:SetBtn(txt, image, func)
        return btn
    end
end

--创建添加好友按钮
function CommonTipsPlayer:CreateFriendBtn()
    if Module.Friend:CheckIsGameFriend(self._playerInfo.player_id) then
        return
    end
    local function AddFriend()
        loginfo("CommonTipsPlayerBtn:AddFriend")
        Module.Friend:AddFriend(self._playerInfo.player_id, self._friendSource, nil, nil, nil, self._playerInfo.plat_id)
        Facade.UIManager:CloseUI(self)
    end
    self._AddFriendHandle = CreateCallBack(AddFriend, self)
    self:CreateBtn(CommonConfig.Loc.AddFriendBtnText, CommonConfig.AddFriendBtnImage, self._AddFriendHandle)
end

--创建邀请组队申请入队按钮
function CommonTipsPlayer:CreateInviteTeamBtn()
    -- local playerState = Module.Social:GetOnlinePlayerStateFromStateCode(self._playerInfo.state)
    -- if playerState == GlobalPlayerStateEnums.EPlayerState_Offline or
    -- (self._playerInfo.team_id ~= 0 and self._playerInfo.team_id == Server.TeamServer:GetTeamID()) then
    --     return
    -- end
    -- local bInvite = Module.Social:IsInvite(self._playerInfo)
    -- local btnTxt = bInvite and CommonConfig.Loc.InviteTeamBtnText or CommonConfig.Loc.AppTeamBtnText
    -- local teamImage = bInvite and CommonConfig.InviteTeamBtnImage or CommonConfig.JoinTeamBtnImage

    -- local function OnClickTeam()
    --     loginfo("CommonTipsPlayerBtn:OnClickTeam")
    --     if Server.MatchServer:GetIsMatching() == true then
    --         Module.CommonTips:ShowSimpleTip(Module.Team.Config.Loc.CannotInviteDuringMatching)
    --     else
    --         Module.Social:TeamInvite(self._playerInfo, self._teamSource)
    --     end
    --     Facade.UIManager:CloseUI(self)
    -- end
    -- self._TeamHandle = CreateCallBack(OnClickTeam, self)
    -- self:CreateBtn(btnTxt, teamImage, self._TeamHandle)

    -- azhengzheng:新增预约功能
    self._playerState = Module.Social:GetOnlinePlayerStateFromStateCode(self._playerInfo.state)

    -- 如果玩家处于离线状态或者和自己处于一个队伍，则不创建按钮
    if Module.Friend:IsFriendOffLineOrInvisible(self._playerState, self._playerInfo.player_id) or (self._playerInfo.team_id ~= 0 and self._playerInfo.team_id == Server.TeamServer:GetTeamID()) then
        return
    end

    -- azhengzheng:同玩非好友玩家，在局内时，不允许显示预约按钮
    if self._playerState == GlobalPlayerStateEnums.EPlayerState_InMatch and not Module.Friend:CheckIsFriend(self._playerInfo.player_id) then
        return
    end

    local btnTxt, btnImg
    local isInvite = Module.Social:IsInvite(self._playerInfo)

    local callBack = function()
        -- 如果自己正在匹配中，则弹出提示并关闭本界面
        if Server.MatchServer:GetIsMatching() then
            Module.CommonTips:ShowSimpleTip(Module.Team.Config.Loc.CannotInviteDuringMatching)
            Facade.UIManager:CloseUI(self)
            return
        end

        local playerState = Module.Social:GetOnlinePlayerStateFromStateCode(self._playerInfo.state)

        -- 判断状态是否发生变化
        if self._playerState ~= playerState then
            Facade.UIManager:CloseUI(self)
            return
        end

        -- 在正式发出请求前，先判断CD是否冷却完毕
        local coolDownPlayerTime = Module.Social:GetCoolDownPlayerTime(self._playerInfo.player_id)

        if coolDownPlayerTime > 0 then
            Module.CommonTips:ShowSimpleTip(string.format(Module.Friend.Config.FriendOperatingFrequencyTooHighTip, coolDownPlayerTime))
            Facade.UIManager:CloseUI(self)
            return
        end

        -- 预约跟旧的组队逻辑是同一套
        Module.Social:TeamInvite(self._playerInfo, self._teamSource)
        Facade.UIManager:CloseUI(self)
    end

    if self._playerState == GlobalPlayerStateEnums.EPlayerState_InMatch then
        btnTxt = isInvite and CommonConfig.Loc.BookAteamAppointment or CommonConfig.Loc.MakeAnAppointmentToJoinTheTeam
        btnImg = CommonConfig.ReservationTeamUpImg
    else
        btnTxt = isInvite and CommonConfig.Loc.InviteTeamBtnText or CommonConfig.Loc.AppTeamBtnText
        btnImg = isInvite and CommonConfig.InviteTeamBtnImage or CommonConfig.JoinTeamBtnImage
    end

    self:CreateBtn(btnTxt, btnImg, callBack)
end

--创建查看个人信息按钮
function CommonTipsPlayer:CreatePlayerInformaBtn()
    local function OnClickInforma()
        Module.RoleInfo:ShowMainPanel(self._playerInfo.player_id)
        Facade.UIManager:CloseUI(self)
    end
    self._InformaHandle = CreateCallBack(OnClickInforma, self)
    self:CreateBtn(CommonConfig.Loc.ViewInformaBtnText, CommonConfig.ViewInformaImage, self._InformaHandle)
end

--创建踢出队伍按钮
function CommonTipsPlayer:CreateKickTeamBtn()
    if not TeamSvr:IsCaptial() then
        return
    end
    local function OnClickKickTeam()
        TeamSvr:KickMem(self._playerInfo.player_id)
        Facade.UIManager:CloseUI(self)
    end
    self._KickTeamHandle = CreateCallBack(OnClickKickTeam, self)
    self:CreateBtn(CommonConfig.Loc.KickTeamBtnText, CommonConfig.KickPlayerImage, self._KickTeamHandle)
end

--创建私聊按钮
function CommonTipsPlayer:CreateChatBtn()
    local function OnClickChat()
        Module.Chat:ChatWithPlayer(self._playerInfo)
        Facade.UIManager:CloseUI(self)
    end
    self._ChatHandle = CreateCallBack(OnClickChat, self)
    self:CreateBtn(CommonConfig.Loc.ChatBtnText, CommonConfig.ChatImage, self._ChatHandle)
end

--创建拉入黑名单按钮
function CommonTipsPlayer:CreateAddBlackBtn()
    --平台好友无法拉黑
    if Server.FriendServer:CheckIsOpenFriend(self._playerInfo.player_id) then
        return
    end
    local OnClickBlack = nil
    local blackTxt = nil
    if Server.FriendServer:CheckIsBlack(self._playerInfo.player_id) then
        blackTxt = CommonConfig.Loc.RomoveBlackBtnText
        OnClickBlack = function ()
            Server.FriendServer:FetchFriendRemoveBlackList(self._playerInfo.player_id, false)
            Facade.UIManager:CloseUI(self)
        end
    else
        blackTxt = CommonConfig.Loc.AddBlackBtnText
        OnClickBlack = function ()
            Server.FriendServer:AddPlayerToBlackList(self._playerInfo.player_id)
            Facade.UIManager:CloseUI(self)
        end
    end
    self._BlackHandle = CreateCallBack(OnClickBlack, self)
    self:CreateBtn(blackTxt , CommonConfig.AddBlackImage, self._BlackHandle)
end

--创建举报按钮
function CommonTipsPlayer:CreateReportBtn()
    local function OnClickReport()
        loginfo("CommonTipsPlayerBtn:OnClickReport")
        Module.Report:ReportPlayer(self._playerInfo, self._reportType, nil, self._reportStr,nil ,ReportEntrance.ReportMeteriral)
        Facade.UIManager:CloseUI(self)
    end
    self._ReportHandle = CreateCallBack(OnClickReport, self)
    self:CreateBtn(CommonConfig.Loc.ReportBtnText , CommonConfig.ReportImage, self._ReportHandle)
end

--创建装让队长按钮
function CommonTipsPlayer:CreatePromoTeamBtn()
    if not TeamSvr:IsCaptial() then
        return
    end
    local function OnClickPromoTeam()
        if not Server.MatchServer:GetIsMatching() then
            TeamSvr:ChangeCaptial(self._playerInfo.player_id)
        else
            Module.CommonTips:ShowSimpleTip(Module.Preparation.Config.Tips.CantTransferOwnerWhenMatching)
        end
        Facade.UIManager:CloseUI(self)
    end
    self._PromoTeamHandle = CreateCallBack(OnClickPromoTeam, self)
    self:CreateBtn(CommonConfig.Loc.PromoTeamBtnText , CommonConfig.PromoTeamImage, self._PromoTeamHandle)
end

--创建踢出房间按钮
function CommonTipsPlayer:CreateKickRoomBtn()
    if not Server.RoomServer:GetIsRoomOwner() then
        return
    end
    local function OnClickKickRoom()
        Server.RoomServer:ReqKickPlayer(self._playerInfo.player_id)
        Facade.UIManager:CloseUI(self)
    end
    self._KickRoomHandle = CreateCallBack(OnClickKickRoom, self)
    self:CreateBtn(CommonConfig.Loc.KickRoomBtnText , CommonConfig.KickPlayerImage, self._KickRoomHandle)
end

--创建语音举报按钮
function CommonTipsPlayer:CreateReportVoiceBtn()
    local function OnClickReportVoice()
        Module.GVoice:ReportPlayer({self._playerInfo.player_id}, "")
        Facade.UIManager:CloseUI(self)
    end
    self._ReportVoiceHandle = CreateCallBack(OnClickReportVoice, self)
    self:CreateBtn(CommonConfig.Loc.ReportVoiceBtnText , CommonConfig.ReportVoiceImage, self._ReportVoiceHandle)
end

--创建删除好友按钮
function CommonTipsPlayer:CreateDelFriendBtn()
    local function OnClickDelFriend()
        local function fSecondProcess()
            Server.FriendServer:DeleteFriend(self._playerInfo.player_id)
        end
        Module.CommonTips:ShowConfirmWindow(string.format(Module.Friend.Config.delFriendMsg, self._playerInfo.nick_name),
        fSecondProcess, nil,
        Module.Friend.Config.backTxt,
        Module.Friend.Config.delFriendTxt)
        Facade.UIManager:CloseUI(self)
    end
    self._DelFriendHandle = CreateCallBack(OnClickDelFriend, self)
    self:CreateBtn(CommonConfig.Loc.DelFriendBtnText , CommonConfig.DelFriendImage, self._DelFriendHandle)
end

--创建转让房主按钮
function CommonTipsPlayer:CreateTransferorBtn()
    if not Server.RoomServer:GetIsRoomOwner() then
        return
    end
    local function OnClickTransferorBtn()
        local newOwnerID = self._playerInfo.player_id
        Server.RoomServer:ReqRoomChangeOwner(newOwnerID)
        Facade.UIManager:CloseUI(self)
    end
    self._TransferorHandle = CreateCallBack(OnClickTransferorBtn, self)
    self:CreateBtn(CommonConfig.Loc.PromoRoomBtnText , CommonConfig.TransferorImage, self._TransferorHandle)
end

--- BEGIN MODIFICATION @ VIRTUOS：创建好友点赞按钮
function CommonTipsPlayer:CreateMatesPraiseBtn()
    if IsHD() and self._playerInfo.bIsPraise ~= nil and self._playerInfo.friendPanelType ~= nil then
        local function praiseClick()
            if self._wtMatesPraiseBtn ~= nil and self._wtMatesPraiseBtn._wtBtn then
                self._wtMatesPraiseBtn._wtBtn:SetIsEnabled(false)
            end
            self._playerInfo.bIsPraise = true
        end
    
        local function OnClickMatesPraiseBtn()
            local newOwnerID = self._playerInfo.player_id
            if self._playerInfo.friendPanelType == 1 then
                Server.FriendServer:PlayerPraise(newOwnerID, CreateCallBack(praiseClick, self), 2)
            else
                Server.FriendServer:PlayerPraise(newOwnerID, CreateCallBack(praiseClick, self), 3)
            end
        end
    
        self._MatesPraiseHandle = CreateCallBack(OnClickMatesPraiseBtn, self)
        self._wtMatesPraiseBtn = self:CreateBtn(CommonConfig.Loc.MatesPraiseBtnText , CommonConfig.MatesPraiseImage, self._MatesPraiseHandle)

        -- 创建时判定输入方式，如果不是手柄输入则隐藏点赞按钮
        local curInputType = UGPInputHelper.GetCurrentInputType(GetGameInstance())
        if curInputType ~= EGPInputType.Gamepad then
            self._wtMatesPraiseBtn:Collapsed()
        end
    
        if self._playerInfo.bIsPraise then
            -- 已经点过赞了就关闭
            if self._wtMatesPraiseBtn._wtBtn then
                self._wtMatesPraiseBtn._wtBtn:SetIsEnabled(false)
            end
        else
            if self._wtMatesPraiseBtn._wtBtn then
                self._wtMatesPraiseBtn._wtBtn:SetIsEnabled(true)
            end
        end
    end
end

function CommonTipsPlayer:CreateTributeBtn()
    if self._playerInfo.bIsPraise ~= nil then
        local function praiseClick()
            if self._wtTributeBtn ~= nil and self._wtTributeBtn._wtBtn then
                self._wtTributeBtn._wtBtn:SetIsEnabled(false)
            end
            self._playerInfo.bIsPraise = true
        end
    
        local function OnClickCommanderPraiseBtn()
            local newOwnerID = self._playerInfo.player_id
            Server.SettlementServer:PlayerCommanderModePraiseReq(newOwnerID, CreateCallBack(praiseClick, self))
        end
    
        self._CommanderPraiseHandle = CreateCallBack(OnClickCommanderPraiseBtn, self)
        self._wtTributeBtn = self:CreateBtn(CommonConfig.Loc.TributeBtnText , CommonConfig.TributeImage, self._CommanderPraiseHandle)
    
        if self._playerInfo.bIsPraise then
            -- 已经点过赞了就关闭
            if self._wtTributeBtn._wtBtn then
                self._wtTributeBtn._wtBtn:SetIsEnabled(false)
            end
        else
            if self._wtTributeBtn._wtBtn then
                self._wtTributeBtn._wtBtn:SetIsEnabled(true)
            end
        end
    end
end

-- Console平台查看用户档案,创建查看平台档案按钮
function CommonTipsPlayer:CreatePlatformProfileBtn()
    if not IsConsole() then
        return
    end

    if self._playerInfo.plat_id ~= Server.AccountServer:GetPlatIdType() then
        return
    end

    local OnReqQueryRes = function(HttpRequest, HttpResponse, bSucceeded)
        local UDFMGameUrlGeneratorIns = UDFMGameUrlGenerator.Get(GetGameInstance())
        if bSucceeded and UDFMGameUrlGeneratorIns then
            -- 判断是否绑定了同平台的账号
            -- 如果开启了假平台，则不进行判断
            queryRes = UDFMGameUrlGeneratorIns:GenerateQueryRes(HttpResponse:GetContentAsString())
            if not queryRes.bHasUID then
                return
            end
            -- 反复快速开关名片页面的话，会导致该按钮多次创建
            -- 临时解决方案：如果self._playerInfo.platformId有值则代表按钮已经创建过了
            if self._playerInfo.platformId ~= nil then
                return
            end

            if queryRes.bHasUID then
                self._playerInfo.platformId = queryRes.UID
            else
                self._playerInfo.platformId = nil
            end

            logerror("CreatePlatformProfileBtn", self._playerInfo.platformId, self._playerInfo.plat_id, Server.AccountServer:GetPlatIdType())

            local function OnClickPlayerProfileBtn() 
                if self._playerInfo.platformId ~= nil then 
                    UDFMPlatformUtils.ShowPlatformProfileUI(self._playerInfo.platformId)
                end
                Facade.UIManager:CloseUI(self)
            end
            self._ShowPlayerProfile = CreateCallBack(OnClickPlayerProfileBtn, self)

            if IsPS5Family() then
                self:CreateBtn(CommonConfig.Loc.PlatformProfilePSBtnText, CommonConfig.PlatformProfilePSImage, self._ShowPlayerProfile)
            elseif IsXboxSeries() then
                self:CreateBtn(CommonConfig.Loc.PlatformProfileXSXBtnText, CommonConfig.PlatformProfileXSXImage, self._ShowPlayerProfile)
            end
        end
    end

    Server.SocialServer:ReqQueryUID(ULuautils.GetOverInt64String(self._playerInfo.player_id), OnReqQueryRes)
end
--- END MODIFICATION

function CommonTipsPlayer:CloseSelf()
    Facade.UIManager:CloseUI(self)
end

--------------------------------------------
--end
--------------------------------------------
-- 局内静音
function CommonTipsPlayer:CreateIngameSilenceBtn()
    local function OnIngameSilenceClicked()
        self:OnIngameBtnClicked(0)
        Facade.UIManager:CloseUI(self)
    end
    textNames = Module.CommonWidget.Field:GetRegisteredBtnTextNames()
    rightIcons = Module.CommonWidget.Field:GetRegisteredBtnIconNames()
    rightIcon = Module.CommonWidget.Config.pcRightIconList[rightIcons[1]]
    self._IngameSilenceHandle = CreateCallBack(OnIngameSilenceClicked, self)
    self:CreateBtn(textNames[1] , rightIcon, self._IngameSilenceHandle)
end
-- 局内转移队长
function CommonTipsPlayer:CreateIngameGiveLeaderBtn()
    local function OnIngameGiveLeaderClicked()
        self:OnIngameBtnClicked(1)
        Facade.UIManager:CloseUI(self)
    end
    textNames = Module.CommonWidget.Field:GetRegisteredBtnTextNames()
    rightIcons = Module.CommonWidget.Field:GetRegisteredBtnIconNames()
    rightIcon = Module.CommonWidget.Config.pcRightIconList[rightIcons[2]]
    self._IngameGiveLeaderHandle = CreateCallBack(OnIngameGiveLeaderClicked, self)
    self:CreateBtn(textNames[2] , rightIcon, self._IngameGiveLeaderHandle)
end
-- 局内申请队长
function CommonTipsPlayer:CreateIngameGetLeaderBtn()
    local function OnIngameGetLeaderClicked()
        self:OnIngameBtnClicked(2)
        Facade.UIManager:CloseUI(self)
    end
    textNames = Module.CommonWidget.Field:GetRegisteredBtnTextNames()
    rightIcons = Module.CommonWidget.Field:GetRegisteredBtnIconNames()
    rightIcon = Module.CommonWidget.Config.pcRightIconList[rightIcons[3]]
    self._IngameGetLeaderHandle = CreateCallBack(OnIngameGetLeaderClicked, self)
    self:CreateBtn(textNames[3] , rightIcon, self._IngameGetLeaderHandle)
end
-- 局内强制接管队长
function CommonTipsPlayer:CreateIngameImpeachLeaderBtn()
    local function OnIngameImpeachLeaderClicked()
        self:OnIngameBtnClicked(3)
        Facade.UIManager:CloseUI(self)
    end
    textNames = Module.CommonWidget.Field:GetRegisteredBtnTextNames()
    rightIcons = Module.CommonWidget.Field:GetRegisteredBtnIconNames()
    rightIcon = Module.CommonWidget.Config.pcRightIconList[rightIcons[4]]
    self._IngameImpeachLeaderHandle = CreateCallBack(OnIngameImpeachLeaderClicked, self)
    self:CreateBtn(textNames[4] , rightIcon, self._IngameImpeachLeaderHandle)
end
-- 局内申请指挥官
function CommonTipsPlayer:CreateIngameGetCommanderBtn()
    local function OnIngameGetCommanderClicked()
        self:OnIngameBtnClicked(4)
        Facade.UIManager:CloseUI(self)
    end
    textNames = Module.CommonWidget.Field:GetRegisteredBtnTextNames()
    rightIcons = Module.CommonWidget.Field:GetRegisteredBtnIconNames()
    rightIcon = Module.CommonWidget.Config.pcRightIconList[rightIcons[5]]
    self._IngameGetCommanderHandle = CreateCallBack(OnIngameGetCommanderClicked, self)
    self:CreateBtn(textNames[5] , rightIcon, self._IngameGetCommanderHandle)
end
-- 局内弹劾指挥官
function CommonTipsPlayer:CreateIngameImpeachCommanderBtn()
    local function OnIngameImpeachCommanderClicked()
        self:OnIngameBtnClicked(5)
        Facade.UIManager:CloseUI(self)
    end
    textNames = Module.CommonWidget.Field:GetRegisteredBtnTextNames()
    rightIcons = Module.CommonWidget.Field:GetRegisteredBtnIconNames()
    rightIcon = Module.CommonWidget.Config.pcRightIconList[rightIcons[6]]
    self._IngameImpeachCommanderHandle = CreateCallBack(OnIngameImpeachCommanderClicked, self)
    self:CreateBtn(textNames[6] , rightIcon, self._IngameImpeachCommanderHandle)
end
-- 局内加好友
function CommonTipsPlayer:CreateIngameAddFriendBtn()
    local function OnIngameAddFriendClicked()
        self:OnIngameBtnClicked(6)
        Facade.UIManager:CloseUI(self)
    end
    textNames = Module.CommonWidget.Field:GetRegisteredBtnTextNames()
    rightIcons = Module.CommonWidget.Field:GetRegisteredBtnIconNames()
    rightIcon = Module.CommonWidget.Config.pcRightIconList[rightIcons[7]]
    self._IngameAddFriendHandle = CreateCallBack(OnIngameAddFriendClicked, self)
    self:CreateBtn(textNames[7] , rightIcon, self._IngameAddFriendHandle)
end
-- 局内举报
function CommonTipsPlayer:CreateIngameReportBtn()
    local function OnIngameReportClicked()
        self:OnIngameBtnClicked(7)
        Facade.UIManager:CloseUI(self)
    end
    textNames = Module.CommonWidget.Field:GetRegisteredBtnTextNames()
    rightIcons = Module.CommonWidget.Field:GetRegisteredBtnIconNames()
    rightIcon = Module.CommonWidget.Config.pcRightIconList[rightIcons[8]]
    self._IngameReportHandle = CreateCallBack(OnIngameReportClicked, self)
    self:CreateBtn(textNames[8] , rightIcon, self._IngameReportHandle)
end
-- 移入移出指挥官频道
function CommonTipsPlayer:CreateIngameCommanderChannelBtn()
    local function OnIngameCommanderChannelClicked()
        self:OnIngameBtnClicked(8)
        Facade.UIManager:CloseUI(self)
    end
    textNames = Module.CommonWidget.Field:GetRegisteredBtnTextNames()
    rightIcons = Module.CommonWidget.Field:GetRegisteredBtnIconNames()
    rightIcon = Module.CommonWidget.Config.pcRightIconList[rightIcons[9]]
    self._IngameCommanderChannelHandle = CreateCallBack(OnIngameCommanderChannelClicked, self)
    self:CreateBtn(textNames[9] , rightIcon, self._IngameCommanderChannelHandle)
end
-- 局内按钮处理
function CommonTipsPlayer:OnIngameBtnClicked(index)
    if not UGameplayStatics then
        return
    end
    local gameState = UGameplayStatics.GetGameState(GetWorld())
    if not gameState then
        return
    end
    if gameState.GetCommanderComponent == nil then
        return
    end
    local battleFieldCommanderDataComponent = gameState:GetCommanderComponent()
    if not battleFieldCommanderDataComponent then
        return
    end
    loginfo("CommonWidgetModule:OpenBattleFieldItemBtnListPanel Operated", index)
    battleFieldCommanderDataComponent:OnCommanderPanelOperate(index, 0, self._playerInfo.player_id)
end



function CommonTipsPlayer:_UpdateDetailViewPosition()
	if self._parent == nil then
		return
	end
	local viewportGeometry = UE.WidgetLayoutLibrary.GetViewportWidgetGeometry(self)
	local viewportLocalSize = viewportGeometry:GetLocalSize()
	local itemScreenPos = self._parent:GetCachedGeometry():GetAbsolutePositionAtCoordinates(FVector2D(0.5, 0.5))
    local safePadding = Facade.UIManager:GetFrameSafeZonePadding()
	local itemLocalPos = viewportGeometry:AbsoluteToLocal(itemScreenPos + FVector2D(safePadding.Left, safePadding.Bottom))

	-- TODO 临时解决，DetailPanel的真实大小（DesiredSize）需要按钮(异步)挂载完之后下一帧才能拿到真实大小，时间不可控
	local detailViewX = self.m_Root:GetDesiredSize().X
	local detailViewY = self.m_Root:GetDesiredSize().Y

    logwarning("CommonTipsPlayer viewportLocalSize ：", viewportLocalSize.X, viewportLocalSize.Y)
    logwarning("CommonTipsPlayer itemLocalPos ：", itemLocalPos.X, itemLocalPos.Y)

    logwarning("CommonTipsPlayer detailView ：", detailViewX, detailViewY)

    local marginHD = 50


	-- 附着对象左边
	if itemLocalPos.X > viewportLocalSize.X * 0.5 then
		local targetAbsolutePos = nil
        self._detailViewAlignment = FVector2D(1, 0)
        if itemLocalPos.Y < viewportLocalSize.Y * 0.5 then
            targetAbsolutePos = self._parent:GetCachedGeometry():GetAbsolutePositionAtCoordinates(FVector2D(0, 0))
            self._detailViewAlignment.Y = 0
        else
            targetAbsolutePos = self._parent:GetCachedGeometry():GetAbsolutePositionAtCoordinates(FVector2D(0, 1))
            self._detailViewAlignment.Y = 1
        end

		local targetLocalPosition = viewportGeometry:AbsoluteToLocal(targetAbsolutePos)
		local xPos = math.max(detailViewX, targetLocalPosition.X - 10)
		local yPos = 0
        if DFHD_LUA == 1 then
            yPos = math.min(viewportLocalSize.Y - marginHD - detailViewY - 20, targetLocalPosition.Y)
        else
            yPos = math.min(viewportLocalSize.Y - detailViewY - 20, targetLocalPosition.Y)
        end
		self._wtDetailViewPos = FVector2D(xPos, yPos)
	-- 右边
	else

        local targetAbsolutePos =  nil
        self._detailViewAlignment = FVector2D(0, 0)
        if itemLocalPos.Y < viewportLocalSize.Y * 0.5 then
            targetAbsolutePos = self._parent:GetCachedGeometry():GetAbsolutePositionAtCoordinates(FVector2D(1, 0))
            self._detailViewAlignment.Y = 0
        else
            targetAbsolutePos = self._parent:GetCachedGeometry():GetAbsolutePositionAtCoordinates(FVector2D(1, 1))
            self._detailViewAlignment.Y = 1
        end
		local targetLocalPosition = viewportGeometry:AbsoluteToLocal(targetAbsolutePos)
        logwarning("CommonTipsPlayer targetLocalPosition ：", targetLocalPosition.X, targetLocalPosition.Y)
		local xPos = math.min(viewportLocalSize.X - detailViewX, targetLocalPosition.X)
		local yPos = math.min(viewportLocalSize.Y - detailViewY - 30, targetLocalPosition.Y)
        local yPos = 0
        if DFHD_LUA == 1 then
            yPos = math.min(viewportLocalSize.Y - marginHD - detailViewY - 30, targetLocalPosition.Y)
        else
            yPos = math.min(viewportLocalSize.Y - detailViewY - 30, targetLocalPosition.Y)
        end
		self._wtDetailViewPos = FVector2D(xPos, yPos)
	end

    logwarning("CommonTipsPlayer self._wtDetailViewPos ：", self._wtDetailViewPos.X, self._wtDetailViewPos.Y)

    local ViewSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(self.m_Root)
    ViewSlot:SetAlignment(self._detailViewAlignment)
    ViewSlot:SetPosition(self._wtDetailViewPos)

    self:Visible()
end

function CommonTipsPlayer:_UpdatePosition(position)
    local ViewSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(self.m_Root)
    ViewSlot:SetPosition(position)

    local viewportGeometry = UE.WidgetLayoutLibrary.GetViewportWidgetGeometry(self)
	local viewportLocalSize = viewportGeometry:GetLocalSize()
    local itemScreenPos = self._parent:GetCachedGeometry():GetAbsolutePositionAtCoordinates(FVector2D(0.5, 0.5))
	local itemLocalPos = viewportGeometry:AbsoluteToLocal(itemScreenPos)

    self._detailViewAlignment = FVector2D(0, 0)
    if position.X > viewportLocalSize.X * 0.5 then
        self._detailViewAlignment.X = 1
        if position.Y < viewportLocalSize.Y * 0.5 then
            self._detailViewAlignment.Y = 0
        else
            self._detailViewAlignment.Y = 1
        end
    else
        self._detailViewAlignment.X = 0
        if position.Y < viewportLocalSize.Y * 0.5 then
            self._detailViewAlignment.Y = 0
        else
            self._detailViewAlignment.Y = 1
        end
    end
    local ViewSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(self.m_Root)
    ViewSlot:SetAlignment(self._detailViewAlignment)
    --UIUtil.CorrectPositionInContainer(self.m_Root, self)
    self:SelfHitTestInvisible()
end

function CommonTipsPlayer:OnBgBtnClick()
    Facade.UIManager:CloseUI(self)
end

function CommonTipsPlayer:SetCallback(fCallback, inst)
    self._fCallbackIns = SafeCallBack(fCallback, inst)
end

function CommonTipsPlayer:OnNavBack()
    return false
end

function CommonTipsPlayer:OnOpen()
    local gameInst = GetGameInstance()
    self._buttonDownHandle = UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonDownEvent:Add(self._OnMouseButtonDown, self)
    -- BEGIN MODIFICATION @ VIRTUOS : Navigation
    if IsHD() then
        self:_EnableGamepadFeature()
    end
    -- END MODIFICATION
end

function CommonTipsPlayer:OnClose()
    if self._fCallbackIns then
		self._fCallbackIns()
    end
    self:RemoveAllLuaEvent()
    local gameInst = GetGameInstance()
    UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonDownEvent:Remove(self._buttonDownHandle)
    -- BEGIN MODIFICATION @ VIRTUOS : 放hidebegin的话，UIManager:_OnPopViewBeforeChange会后调用，导致聚焦到viewport
    if IsHD() then
        self:_DisableGamepadFeature()
    end
    -- END MODIFICATION
end

--- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
function CommonTipsPlayer:_EnableGamepadFeature()
    ---PC版本注册导航和输入
    if not IsHD() then
        return 
    end

    if not self._navGroup then
        self._navGroup = WidgetUtil.RegisterNavigationGroup(self._wtBox, self, "Hittest")
        self._navGroup:AddNavWidgetToArray(self._wtBox)
        self._navGroup:SetScrollRecipient(self._wtBox)
        self._navGroup:MarkIsStackControlGroup()
        -- 触发默认Focus
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroup)
        -- 由于 btn list 会延迟创建, Focus还需在 CreateButtonList 处刷新
    end

    if not self._ClosePanelHandler then
        -- 输入绑定
        self._ClosePanelHandler = self:AddInputActionBinding(
            "Back", 
            EInputEvent.IE_Pressed, 
            self._OnCanceled, 
            self, 
            EDisplayInputActionPriority.UI_Pop
        )
    end

    if not self._OnNotifyInputTypeChangedHandle then
        -- 绑定多输入设备切换事件
        self._OnNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._OnInputTypeChanged, self))
        -- 初始化：根据当前输入设备来设置点赞按钮的可见性
        local curInputType = UGPInputHelper.GetCurrentInputType(GetGameInstance())
        self:_OnInputTypeChanged(curInputType)
    
        -- 现有的DisplayAction系统无法在弹窗开启时Block其他UI界面的输入，因此这里做临时处理，将有冲突的DisplayAction给吃掉
        self._PagePrevFriendList = self:AddInputActionBinding("Common_SwitchToPrevTab", EInputEvent.IE_Pressed, self._TempBlockDisplayAction, self, EDisplayInputActionPriority.UI_Pop)
        self._PageNextFriendList = self:AddInputActionBinding("Common_SwitchToNextTab", EInputEvent.IE_Pressed, self._TempBlockDisplayAction, self, EDisplayInputActionPriority.UI_Pop)
    end
end

function CommonTipsPlayer:_DisableGamepadFeature()
    ---PC版本注销导航和输入
    if not IsHD() then
        return 
    end

    -- 注销导航组
    if self._navGroup then
        WidgetUtil.RemoveNavigationGroup(self)
        self._navGroup = nil
    end

    -- 注销输入绑定
    if self._ClosePanelHandler then
		self:RemoveInputActionBinding(self._ClosePanelHandler)
		self._ClosePanelHandler = nil
	end

    if self._PagePrevFriendList then
        self:RemoveInputActionBinding(self._PagePrevFriendList)
        self._PagePrevFriendList = nil
    end

    if self._PageNextFriendList then
        self:RemoveInputActionBinding(self._PageNextFriendList)
        self._PageNextFriendList = nil
    end

    if self._OnNotifyInputTypeChangedHandle then
        UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._OnNotifyInputTypeChangedHandle)
        self._OnNotifyInputTypeChangedHandle = nil
    end
end

function CommonTipsPlayer:_OnInputTypeChanged(InputType)
    if not IsHD() then
        return 
    end

    -- 仅在手柄操作时显示点赞按钮，切换为键鼠时隐藏
    if self._wtMatesPraiseBtn then
        if InputType == EGPInputType.Gamepad then
            self._wtMatesPraiseBtn:SelfHitTestInvisible()
        else
            self._wtMatesPraiseBtn:Collapsed()
        end
    end
end
--- END MODIFICATION

-- BEGIN MODIFICATION @ VIRTUOS (temp)
function CommonTipsPlayer:_TempBlockDisplayAction()
    logwarning("CommonTipsPlayer temp block display action: Common_SwitchToPrevTab, Common_SwitchToNextTab")
end
--- END MODIFICATION

return CommonTipsPlayer