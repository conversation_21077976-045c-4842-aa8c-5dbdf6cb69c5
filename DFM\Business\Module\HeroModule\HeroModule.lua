----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMHero)
----- LOG FUNCTION AUTO GENERATE END -----------


---@class HeroModule : ModuleBase
---@field Field HeroField
local HeroModule = class("HeroModule", require("DFM.YxFramework.Managers.Module.ModuleBase"))
local HeroLogic = require "DFM.Business.Module.HeroModule.Logic.HeroLogic"
local HeroLoadLogic = require "DFM.Business.Module.HeroModule.Logic.HeroLoadLogic"
local HeroConfig    = require "DFM.Business.Module.HeroModule.HeroConfig"
local HeroField     = require "DFM.Business.Module.HeroModule.HeroField"
local UHallFunctionStatics = import "HallFunctionStatics"
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
local ItemHelperTool          = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local UHallLevelSequenceManager = import "HallLevelSequenceManager"

local ShowHeroCommercialPanel_CD = 2

function HeroModule:Ctor()
end

---模块Init回调，用于初始化一些数据
function HeroModule:OnInitModule()
	HeroLogic.AddListeners()

	Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult:AddListener(self.OnPakDownloadResult, self)
end

function HeroModule:OnLoadModule()
end

function HeroModule:OnDestroyModule()
	HeroLogic.RemoveListeners()

	Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult:RemoveListener(self.OnPakDownloadResult, self)
end

-- 热更内容
function HeroModule:OnPakDownloadResult(moduleName, bSuccess)
    loginfo("[gorden]HeroModule.OnPakDownloadResult", moduleName, " ", bSuccess)
	if LiteDownloadManager:GetModuleStateByModuleName("MiniWorldLong1") or LiteDownloadManager:GetModuleStateByModuleName("OtherSupplement") then
		local worldcomposition = GetWorld().Worldcomposition
		if worldcomposition then
			-- worldcomposition:RequestReinitialize()
			worldcomposition:Reinitialize()
		end
		logwarning("[gorden] HeroModule:OnPakDownloadResult worldcomposition:Reinitialize")
	end
end

---------------------------------------------------------------------------------
--- Loading 生命周期
--- 设置bAutoLoading = true则下列生命周期有效
---------------------------------------------------------------------------------
---@overload fun(ModuleBase, OnLoadingLogin2Frontend)
function HeroModule:OnLoadingLogin2Frontend(gameFlowType)
    HeroLoadLogic.OnLoadingLogin2FrontendProcess(gameFlowType)
end

---@overload fun(ModuleBase, OnLoadingGame2Frontend)
function HeroModule:OnLoadingGame2Frontend(gameFlowType)
    HeroLoadLogic.OnLoadingGame2FrontendProcess(gameFlowType)
end

---@overload fun(ModuleBase, OnLoadingFrontend2Game)
function HeroModule:OnLoadingFrontend2Game(gameFlowType)
    HeroLoadLogic.OnLoadingFrontend2GameProcess(gameFlowType)
end

---------------------------------------------------------------------------------
--- Public API
---------------------------------------------------------------------------------
function HeroModule:ShowMainPanel()
end

function HeroModule:GetCurShowHeroId()
	return self.Field:GetCurSelectHeroId()
end

function HeroModule:GetCurShowHeroFashionSuitId()
	return self.Field:GetCurSelectHeroFashionSuitId()
end

function HeroModule:SetCurShowHeroFashionSuitId(inFashionId)
	self.Field:SetCurSelectHeroFashionSuitId(inFashionId)
end

--- 界面需要刷新临时英雄展示时，调用此方法
--- 当该界面OnClose时：
-------	如果需要回到槽位上装备的英雄时，调用此方法传入nil；
-------	如果不需要回到槽位上装备的英雄，仍需要展示临时英雄时，不调用此方法
function HeroModule:ShowHeroById(heroItemId)
	return HeroLogic.ShowHeroByIdProcess(heroItemId)
end

--- 界面需要更换当前游戏模式选中的英雄时，调用此方法
--- 模块默认会处理好不同模式对应的不同协议数据，外部只需要调用即可
function HeroModule:UseHeroById(heroItemId)
	return HeroLogic.UseHeroByIdProcess(heroItemId)
end

--设置当前展示的角色ID
function HeroModule:SetCurShowHeroById(heroItemId)
	local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
	if armedForceMode == EArmedForceMode.MP then
		HeroLogic._OnMPUsedHeroIdChanged(heroItemId)
	else
		HeroLogic._OnSOLUsedHeroIdChanged(heroItemId)
	end
end

function HeroModule:ResetShowHeroId()
	self.Field:ClearSelectedHero()
end
------------------------------------------------------干员商业化---------------------------------------------------------

--- 界面需要更换当前游戏模式选中的英雄时，调用此方法
--- 模块默认会处理好不同模式对应的不同协议数据，外部只需要调用即可
function HeroModule:UseAccessoryById(heroId, accessoryId, slotId, isUnequipped)
	return HeroLogic.UseAccessoryByIdProcess(tonumber(heroId), tonumber(accessoryId), slotId, isUnequipped)
end

function HeroModule:GetUsedAccessory(heroIdStr, type)
	HeroLogic.GetUsedAccessory(tonumber(heroIdStr), type)
end

function HeroModule:GetIsAccessoryUsing(heroId, accessoryId)
	return HeroLogic.IsAccessroyIdSelectedWithHeroId(heroId, accessoryId)
end

--获取当前选择的商业化道具
function HeroModule:GetSelectedAccessory(heroId, accessoryId)
	return HeroLogic.GetSelectedAccessory(heroId, tonumber(accessoryId))
end

--获取某干员某类型周边
function HeroModule:GetAllAccessoryByType(heroIdStr, type)
    return HeroLogic.GetAllAccessoryByType(heroIdStr, type)
end

--获取在轮盘中的商业化道具信息
function HeroModule:GetAccessoriesInRoulette(heroId)
	return HeroLogic.GetAccessoriesInRoulette(heroId)
end

--获取此道具是否解锁
function HeroModule.IsAccessoryUnlocked(heroId, accessoryId)
	return HeroLogic.IsAccessoryUnlocked(heroId, accessoryId)
end

--字符串超过多少位截取为...
function HeroModule:CalculateString(str)
	return HeroLogic.CalculateString(str)
end

--是否为红皮
function HeroModule:IsHighLevel(inFashionId)
	return HeroLogic.IsHighLevel(inFashionId)
end

--干员商业化场景相关 start--
--播放干员出场动画
---@param heroId            string 干员ID str
---@param shouldToEnd       bool 是否直接播到静帧状态
---@param MarkedFramePlayTo string 播放到哪个MarkedFrameState
function HeroModule:PlayCharacterAnimSeq(heroId, shouldToEnd, MarkedFramePlayTo, shouldPlayVoice)
	shouldPlayVoice = setdefault(shouldPlayVoice, true)
	MarkedFramePlayTo = setdefault(MarkedFramePlayTo, "None")
	local heroFashionSuitIdStr = tostring(Server.HeroServer:GetFashionSuitIdByHeroId(heroId))
	self:PlayCharacterAnimSeqByFashionId(heroId, heroFashionSuitIdStr, shouldToEnd, MarkedFramePlayTo, shouldPlayVoice)
end

--根据fashionId播放动画
function HeroModule:PlayCharacterAnimSeqByFashionId(heroId, heroFashionSuitIdStr, shouldToEnd, MarkedFramePlayTo, shouldPlayVoice)
	
	local shouldPlay = true
	if not IsHD() then
		local fashionQuality = ItemHelperTool.GetQualityTypeById(tonumber(heroFashionSuitIdStr))
		local downloadType = LiteDownloadManager:GetDownloadCategary(heroFashionSuitIdStr)
		local bDownloaded = LiteDownloadManager:IsDownloadedByModuleName(downloadType)
		shouldPlay = bDownloaded
		loginfo("[echewzhu]HeroModule:PlayCharacterAnimSeqByFashionId shouldPlay = ", shouldPlay, ", bDownloaded = ", bDownloaded, ", downloadType = ",downloadType)
	else
		shouldPlay = true
	end

	if heroId ~= 0 then
		--根据品阶播放视频
		HeroLogic.PlayVideoByLevel(tostring(heroId), heroFashionSuitIdStr)
	end
	
	shouldToEnd = setdefault(shouldToEnd, false)
	MarkedFramePlayTo = setdefault(MarkedFramePlayTo, "None")
	shouldPlayVoice = setdefault(shouldPlayVoice, true)
	self:SetCurShowHeroFashionSuitId(heroFashionSuitIdStr)
	Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "SetDisplayType", "CharacterFar")
	--重设旋转
	Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "ResetCharacterRotation")
	Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "ResetActionShowCharacter")
	
	Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero,"ResetCharacterSequence")

	if UHallLevelSequenceManager then
		UHallLevelSequenceManager.Get(GetWorld()):SetLevelLoaded(false)
		UHallLevelSequenceManager.Get(GetWorld()):SetSeqLoaded(false)
	end
	
	if shouldPlay then
		--播动画
		Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero,"SetCharacterSuitAvatarWithSequence", heroFashionSuitIdStr, MarkedFramePlayTo)
		if shouldToEnd then
			--直接切换到idle状态
			Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero,"SetCurrentCharacterSeqEnd")
		end
		Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero,"StopPlaySequenceVoice")
		Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero,"StopPlay2DVoice")
		--播放干员语音
		if shouldPlayVoice then
			Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero,"PlaySequenceVoice",heroFashionSuitIdStr,"None")
		end
	end
	
	--设置子关卡加载卸载
	HeroLogic.SetSceneSubStageLoad(heroFashionSuitIdStr, true)
	-- local heroId = Server.HeroServer:GetBelongedHeroIdByHeroFashionId(heroFashionSuitIdStr)
	
end

--重置干员sequence
function HeroModule:ResetOperatorSequence()
	Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "ResetCharacterSequence")
	Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "ResetCharacterSequence")
	Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "StopPlaySequenceVoice")
end

--设置干员出场动画角色可旋转
function HeroModule:SetCanRotate(bCanRotate)
	Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero,"SetCharacterInteractEnable", bCanRotate)
end

--播放干员动作动画(通过HeroId)
---@param heroId string 干员ID str
---@param actionId string	干员动作ID str
function HeroModule:PlayActionShowAnim(heroId, actionId)
	local heroFashionSuitIdStr = tostring(Server.HeroServer:GetFashionSuitIdByHeroId(tostring(heroId)))
	Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero,"SetExtraLightEnabled", true)
	--设置子关卡加载卸载
	HeroLogic.SetSceneSubStageLoad(heroFashionSuitIdStr, true)
	--重设旋转
	Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "ResetCharacterRotation")
	Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "ResetCharacterSequence")
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "ResetActionShowCharacter")
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "StopCameraFade")--停止摄像机淡入淡出
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "SetCameraAppearanceDisplayType", "CharacterShow", heroFashionSuitIdStr)--设置镜头
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "SetCharacterActionShow", heroFashionSuitIdStr, actionId)--播放动作动画
	--根据品阶播放视频
	local isVideoStopped = Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "IsVideoStopped")
	if not isVideoStopped then
		local curShowHeroId = self:GetCurShowHeroId()
    	local curShowFashionId = self:GetCurShowHeroFashionSuitId()
		if tostring(curShowFashionId) == heroFashionSuitIdStr then
			return
		end
		self:SetCurShowHeroFashionSuitId(heroFashionSuitIdStr)
    	-- if tostring(curShowHeroId) == tostring(heroId) then
		-- 	return
		-- end
	end
	
	Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "SetCurCharacterAvatarId", heroFashionSuitIdStr)
	HeroLogic.PlayVideoByLevel(heroId, heroFashionSuitIdStr)
end

--播放干员动作动画(通过FashionId)
---@param fashionId string 时装id
---@param actionId string	动作id
function HeroModule:PlayActionShowAnimByFashionId(fashionId, actionId)
    local HeroId = Server.HeroServer:GetBelongedHeroIdByHeroFashionId(tostring(fashionId))
	--设置子关卡加载卸载
	HeroLogic.SetSceneSubStageLoad(fashionId, true)
    --重设旋转
	Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "ResetCharacterRotation")
	Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "ResetCharacterSequence")
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "ResetActionShowCharacter")
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "StopCameraFade")--停止摄像机淡入淡出
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "SetCameraAppearanceDisplayType", "CharacterShow", fashionId)--设置镜头
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "SetCharacterActionShow", fashionId, actionId)--播放动作动画
	--根据品阶播放视频
	local isVideoStopped = Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "IsVideoStopped")
	if not isVideoStopped then
		local curShowHeroId = self:GetCurShowHeroId()
    	local curShowFashionId = self:GetCurShowHeroFashionSuitId()
		if tostring(curShowFashionId) == fashionId then
			return
		end
		if tostring(curShowHeroId) == HeroId then
			return
		end
	end
	
	HeroLogic.PlayVideoByLevel(HeroId, fashionId)
end


--切换背景视频
function HeroModule:SwitchBackgroundVideo(heroId)
	local heroFashionSuitIdStr = tostring(Server.HeroServer:GetFashionSuitIdByHeroId(heroId))
	--设置子关卡加载卸载
	HeroLogic.SetSceneSubStageLoad(heroFashionSuitIdStr, true)
	--根据品阶播放视频
	HeroLogic.PlayVideoByLevel(heroId, heroFashionSuitIdStr)
end

--展示手表
function HeroModule:ShowOperatorWatch(itemId,checkSubLevel)

	--设置镜头
	Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.OperatorWatch,"SetDisplayType", "Main")
	--展示模型
	HeroLogic.ShowOperatorWatch(itemId,checkSubLevel)
end

--获取干员背景音乐名称
function HeroModule:GetFashionMusicName(inFashionId)
	return HeroLogic.GetMusicName(inFashionId)
end

--干员商业化场景相关 end--

------------------------------------------------------干员商业化---------------------------------------------------------

--获取在轮盘中的图片和文本
function HeroModule:GetImagesAndTextsInRoulette(heroId)
	return HeroLogic.GetImagesAndTextsInRoulette(heroId)
end

function HeroModule:GetSortedHeroListByMode(eArmedForceMode)
return HeroLogic.GetSortedHeroListByMode(eArmedForceMode)
end

function HeroModule:Jump()
    local openUINavIdList = {} -- 跳转接口返回 按顺序实际需要打开/重新显示的所有界面id
	self:ShowHeroMainPanel()
    table.insert(openUINavIdList, UIName2ID.HeroMainPanel)
    return openUINavIdList
end

function HeroModule:ShowHeroMainPanel()
	Module.ModuleSwitcher:ModuleOpenByCheck(SwitchSystemID.SwitchSystemHeroLobby, UIName2ID.HeroMainPanel)
end

function HeroModule:ShowHeroSkillPanel(fOnSkillPanelCreateCallback, heroId, fOnSkillPanelHideBegin,bHideTopBar)
	return HeroLogic.ShowHeroSkillPanel(fOnSkillPanelCreateCallback, heroId, fOnSkillPanelHideBegin,bHideTopBar)
end

---@param heroListTopPanelMode EHeroListTopPanelMode
function HeroModule:ShowHeroListPanel(fOnHeroPanelCreateCallback, fOnHeroPanelHideBegin, needFlitArmId , heroListTopPanelMode)
	return HeroLogic.ShowHeroListPanel(fOnHeroPanelCreateCallback, fOnHeroPanelHideBegin, needFlitArmId, heroListTopPanelMode)
end

function HeroModule:ShowHeroCommercialPanel(subViewType,...)
	subViewType = setdefault(subViewType, HeroConfig.ESubViewType.HeroAppearance)
	local params =  {n = select('#',...),...}

    local fReqFinishCallbackFun = function()
		HeroLogic.ShowHeroCommercialPanel(subViewType,unpack(params,1,params.n))
    end
    if self._showHeroCommercialPanelCDTimer then
        fReqFinishCallbackFun()
	else
		Server.HeroServer:DoLoadHeroListReq(fReqFinishCallbackFun) --进入自定义界面前刷新资源列表
		self._showHeroCommercialPanelCDTimer = Timer.DelayCall(ShowHeroCommercialPanel_CD,function ()
			if self then
				self._showHeroCommercialPanelCDTimer = nil
			end
		end)
    end
    

end

function HeroModule:CloseHeroCommercialPanel()
	 HeroLogic.CloseHeroCommercialPanel()
end

function HeroModule:ShowHeroEmotionRoulette(itemId, icon, level) -- data:{itemId, index}
	itemId = setdefault(itemId, "0000")
	level = setdefault(level, 0)
	return HeroLogic.ShowHeroEmotionRoulette(itemId, icon, level)
end

function HeroModule:ShowHeroFilePanel(heroId, fOnHeroFileOpenCallback)
	local isUnlock = Server.HeroServer:IsCanUseHero(heroId)
	if not isUnlock then
		Module.CommonTips:ShowSimpleTip(HeroConfig.Loc.TipHeroLock)
	else
		return HeroLogic.ShowHeroProfile(heroId, fOnHeroFileOpenCallback)
	end
end

--打开干员解锁界面
function HeroModule:ShowUnLockHeroPanel(heroInfo, heroData)
	Facade.UIManager:AsyncShowUI(UIName2ID.HeroUnLockPanel, nil, nil, heroInfo, heroData)
end

---设置是否禁止点击屏幕关闭干员技能介绍界面
function HeroModule:SetFlagDisableClickBgCloseHeroSkill(newState)
	self.Field.bDisableCloseHeroSkillPanel = newState
end

--播放干员2D语音
function HeroModule:PlayHero2DVoice(heroId, voiceId)
	loginfo("[echewzhu]PlayHero2DVoice: heroId = ", heroId, "voiceId = ",voiceId)
	UHallFunctionStatics.Play2DHeroAudio(GetWorld(), heroId, voiceId)
end

--停止播放干员2D语音
function HeroModule:StopPlayHero2DVoice()
	loginfo("[echewzhu]StopPlayHero2DVoice")
	UHallFunctionStatics.Stop2DHeroAudio()
end

--------------------------------------- 获取干员对应分享资源CDNKey值 ------------------------------------

-- 这个heroId有可能是角色ID 也可能是时装Id
function HeroModule:GetShareCDNIdByHeroId(fashionId)
	local configData = self:ShareIsFashionId(fashionId)
	local targetCdnId = configData.SocialShareCDN or nil
	if targetCdnId then
		return targetCdnId
	end

	return ""
end

-- 判断是否时装Id 如果是则返回对应表中某一行数据 否则返回nil
function HeroModule:ShareIsFashionId(fashionId)
	if not self.mShareHeroFashionData then
		self.mShareHeroFashionData = Facade.TableManager:GetTable("Hero/HeroFashionData")
	end

	for key, value in pairs(self.mShareHeroFashionData or {}) do
		if value and fashionId and value.FashionID then
			if tonumber(value.FashionID) == tonumber(fashionId) then
				return value
			end
		end
	end

	return nil
end
--end
-----------------------------------------------------------------------

function HeroModule:SetFirstEnterUsedHeroId(firstHeroId)
	self.Field:SetFirstEnterUsedHeroId(firstHeroId)
end

function HeroModule:GetFirstEnterUsedHeroId()
	return self.Field:GetFirstEnterUsedHeroId()
end

function HeroModule.ShowHeroRecruitmentPanel(fOnHeroPanelCreateCallback, fOnHeroPanelHideBegin,inHeroId)
   	return HeroLogic.ShowHeroRecruitmentPanel(fOnHeroPanelCreateCallback, fOnHeroPanelHideBegin,inHeroId)
end

---@param inHeroId string | number
function  HeroModule:SetCurShowRecruitmentHeroId(inHeroId)
	HeroField:SetCurShowRecruitmentHeroId(inHeroId)
end

function  HeroModule:GetCurShowRecruitmentHeroId()
   return HeroField:GetCurShowRecruitmentHeroId()
end

---打开玄学道具系统
function HeroModule.ShowHeroMysticalItemPanel(fOnHeroPanelCreateCallback, fOnHeroPanelHideBegin,inHeroPropsId)
	return HeroLogic.ShowHeroMysticalItemPanel(fOnHeroPanelCreateCallback, fOnHeroPanelHideBegin,inHeroPropsId)
end


---打开兵种道具玄学槽位保存弹窗
function HeroModule.ShowHeroMysticalItemStorageSavePanel(fOnHeroPanelCreateCallback, fOnHeroPanelHideBegin,inHeroPropsId)
	return HeroLogic.ShowHeroMysticalItemStorageSavePanel(fOnHeroPanelCreateCallback, fOnHeroPanelHideBegin,inHeroPropsId)
end

---@param itemId Any itemID(3811开头的)
---@param	ItemList table 兵种ID列表（玄学可以不用传这个）
---@param	isMystical bool 是否为玄学
function HeroModule:ShowHeroItemVideoPanel(fOnHeroItemVideoPanelCreateCallback, itemId, ItemList, isMystical)
	HeroLogic.ShowHeroItemVideoPanel(fOnHeroItemVideoPanelCreateCallback, itemId, ItemList, isMystical)
end

-- 获取干员默认装配的名片图片
function HeroModule:GetHeroCardIDByHeroIdStr(heroIdStr)
    return HeroLogic.GetHeroCardIDByHeroIdStr(heroIdStr)
end

--该干员兵种道具是否有视频
function HeroModule:DoesHeroItemHasVideo(itemId)
    return HeroLogic.DoesHeroItemHasVideo(itemId)
end

--获取当前展示的兵种道具玄学奖池id
function HeroModule:GetCurShowHeroPropsMysticalLotteryId()
    return HeroField:GetCurShowHeroPropsMysticalLotteryId()
end

--设置当前展示的兵种道具玄学奖池id
function HeroModule:SetCurShowHeroPropsMysticalLotteryId(inLotteryId)
    HeroField:SetCurShowHeroPropsMysticalLotteryId(inLotteryId)
end

return HeroModule