----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRoleInfo)
----- LOG FUNCTION AUTO GENERATE END -----------
---@class RoleLevelIcon : LuaUIBaseView
local RoleLevelIcon = ui("RoleLevelIcon")
local RoleInfoLogic = require "DFM.Business.Module.RoleInfoModule.Logic.RoleInfoLogic"

local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local EComp = CommonWidgetConfig.EIVWarehouseTempComponent
local EIVSlotPos = CommonWidgetConfig.EIVSlotPos
local EIVItemViewMode = CommonWidgetConfig.EIVItemViewMode

function RoleLevelIcon:Ctor()
    self._wtSlideShow = UIUtil.WndCarouselPro(self, "ExCommonWidgetCarouselPro_137",
        self._OnGetPosterCount, self._OnProcessPosterWidget, self._OnCarouselIndexChanged)

    self._wtSlideShow:SetCppValue("bIsAutoScroll", true)
    self._wtSlideShow:SetCppValue("bIsSupportDrag", true)
    self._wtSlideShow:SetCppValue("ScrollInterval", 6)
    self._wtSlideShow:SetCppValue("bIsSupportClick", true)

    UIUtil.BindCarouselProClickEvent(self._wtSlideShow, self, self.OnCommonItemClick)

    self.wtCollectionValue = self:Wnd("wtCollectionValue", UITextBlock)
    self.wtCollectionProgress = self:Wnd("wtCollectionProgress", UITextBlock)

    self._wtCarousel1 = self:Wnd("WBP_DFCommonButtonCarousel", UIWidgetBase)
    self._wtCarousel2 = self:Wnd("WBP_DFCommonButtonCarousel_1", UIWidgetBase)
    self._wtCarousel3 = self:Wnd("WBP_DFCommonButtonCarousel_2", UIWidgetBase)
    self._wtHorizontalBox = self:Wnd("DFHorizontalBox", UIWidgetBase)
    self._wtSpacer = self:Wnd("DFSpacer_88", UIWidgetBase)

    self.CarouselBtns = {}
    for i = 1, 3 do
        self.CarouselBtns[i] = self["_wtCarousel" .. i]
    end
    if IsHD() then
        self._wtCarouselKeyIcon = self:Wnd("CarouselKeyIcon", UIWidgetBase)
        self._wtCarouselKeyIcon:InitByDisplayInputActionName("SwitchCarousel", true, 0.0, true)
    end

    self:InitData()
end

function RoleLevelIcon:InitData()
    self.starWeapons = {}
    self.curShowId = 1
end

function RoleLevelIcon:OnOpen()
end

function RoleLevelIcon:OnClose()
    self:RemoveAllLuaEvent()
end

function RoleLevelIcon:RefreshCarouselBtns()
    local count = self:_OnGetPosterCount()
    if count == 1 then
        Timer.DelayCall(0, function()
            self._wtHorizontalBox:Collapsed()
        end, self)

        self._wtSpacer:Collapsed()

        if IsHD() then
            self._wtCarouselKeyIcon:Collapsed()
        end

        for i = 1, 3 do
            self.CarouselBtns[i]:Collapsed()
        end
    else
        Timer.DelayCall(0, function()
            self._wtHorizontalBox:Visible()
        end, self)
        self._wtSpacer:Visible()
        if IsHD() then
            self._wtCarouselKeyIcon:Visible()
        end
        for i = 1, 3 do
            if i <= count then
                self.CarouselBtns[i]:Visible()
            else
                self.CarouselBtns[i]:Collapsed()
            end
        end
    end
end

function RoleLevelIcon:RefreshCarousel(starWeapons)
    table.sort(starWeapons, function(a, b)
        return a.weapon.weapon_star_info.star_num > b.weapon.weapon_star_info.star_num
    end)

    -- if starWeapons then -- todo
    --     self.starWeapons = { starWeapons[1] }
    -- end

    self.starWeapons = starWeapons
    self._wtSlideShow:RefreshAllItems()

    self:RefreshCarouselBtns()
    self:RefreshSlideShowInfo()

    self:EnableGamepad()
end

------------- 收藏室 --------------
function RoleLevelIcon:RefreshCollectRoom(roomData)
    self.roomData = roomData

    local count = 0
    local function func(cabinet)
        local itemId = 0
        if cabinet and cabinet.locked == false and cabinet.grids then
            for _, grid in pairs(cabinet.grids) do
                if grid.price > 0 and grid.item.id > 0 then -- 解锁
                    count = count + 1
                end
            end
        end
    end

    if roomData.display_cabinet then
        func(roomData.display_cabinet)
    end

    if roomData.special_cabinet then
        func(roomData.special_cabinet)
    end

    local allPrice = RoleInfoLogic.GetRoleInfoNumberStr(roomData.show_room_capital + roomData.non_current_assets)
    self.wtCollectionValue:SetText(allPrice)
    self.wtCollectionProgress:SetText(string.format(Module.RoleInfo.Config.Loc.CollectionNumFormat, count))
end

----------------------------------------------------- 轮播图相关 -----------------------------------------------------
function RoleLevelIcon:_OnGetPosterCount()
    local num = #self.starWeapons
    if num > 3 then num = 3 end
    return num
end

function RoleLevelIcon:_OnProcessPosterWidget(carouselProWidget, index, widget)
    local propInfo = self.starWeapons[index + 1]

    if propInfo == nil then return end

    local item = ItemBase:NewIns(propInfo.id, 0)
    local weaponDesc = item:GetRawDescObj()
    item:SetRawPropInfo(propInfo)
    widget:InitItem(item)
    widget:ShowItemNameComp(false)
    widget:ShowGunStarComp(true)
    widget:EnableComponent(EComp.ItemQuality, false)
    widget:EnableComponent(EComp.ItemBg, false)
    -- 品质名字
    widget:ChangeDefaultMode(EIVItemViewMode.ShopItemView)
    local iconTextNameComp = widget:FindOrAdd(EComp.TopLeftIconText, UIName2ID.IVTextQualityComponent, EIVSlotPos.TopLeft)
    if iconTextNameComp then
        iconTextNameComp:RefreshComponent()
        iconTextNameComp:SetVisibility(ESlateVisibility.HitTestSelfOnly)
    end

    widget:SetAnchorIfOverFlow()

    widget:SelfHitTestInvisible()
    widget:SetCppValue("bIsFocusable", false)
end

function RoleLevelIcon:OnCommonItemClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.ItemBook, nil, nil, 1, self.starWeapons)
end

function RoleLevelIcon:_OnCarouselIndexChanged(carouselProWidget, preIdx, curIdx, itemWidget)
    self.curShowId = curIdx + 1
    self:RefreshSlideShowInfo()
end

--- 刷新显示信息
function RoleLevelIcon:RefreshSlideShowInfo()
    for index, btn in ipairs(self.CarouselBtns) do
        btn:SetStyle(0)
    end
    self.CarouselBtns[self.curShowId]:SetStyle(1)
end

function RoleLevelIcon:GoForward()
    if self._wtSlideShow:IsVisible() then
        self._wtSlideShow:PreviousPage()
    end
end

function RoleLevelIcon:GoBackward()
    if self._wtSlideShow:IsVisible() then
        self._wtSlideShow:NextPage()
    end
end

-- 轮播图手柄
function RoleLevelIcon:EnableGamepad()
    if not IsHD() then return end

    if not self._switchTabHandlers then
        self._switchTabHandlers =
        {
            self:AddInputActionBinding("Common_RightStickRight_Gamepad", EInputEvent.IE_Pressed, self.GoBackward, self,
                EDisplayInputActionPriority.UI_Pop),
            self:AddInputActionBinding("Common_RightStickLeft_Gamepad", EInputEvent.IE_Pressed, self.GoForward, self,
                EDisplayInputActionPriority.UI_Pop),
        }
    end
end

function RoleLevelIcon:DisableGamepad()
    if not IsHD() then return end

    if self._switchTabHandlers then
        for key, value in pairs(self._switchTabHandlers) do
            self:RemoveInputActionBinding(value)
        end
        self._switchTabHandlers = nil
    end
end

return RoleLevelIcon
