----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMStore)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

---@class RecommendBuyPacks : LuaUIBaseView
local RecommendBuyPacks = ui("RecommendBuyPacks")
local StoreConfig = Module.Store.Config
local StoreLogic = require "DFM.Business.Module.StoreModule.Logic.StoreLogic"
local CommonPopWindows = require "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindows"


function RecommendBuyPacks:Ctor()
    self._wtCommonPopWin = self:Wnd("WBP_CommonPopWindowsV2", CommonPopWindows)
    local fCallbackIns = CreateCallBack(self._OnCloseBtnClicked, self)
    self._wtCommonPopWin:BindCloseCallBack(fCallbackIns)
    self._wtCommonPopWin:SetBackgroudClickable(true)

    self._wtRichTextBlock = self:Wnd("wtRichTextBlock", UITextBlock)

    local btnIns = self._wtCommonPopWin:SetConfirmBtnType(CommonPopWindows.EConfirmBtnType.CenterConfirm,
        {
            { btnText = StoreConfig.Loc.Purchase, fClickCallback = self.OnBtnBunBundleClick, caller = self,
                bNeedClose = false },
        }, true)

    self._wtScrollBox =
    UIUtil.WndScrollGridBox(
        self,
        "wtScrollGridBox",
        self._OnToBuyGoodsCount,
        self._OnProcessToBuyGoodsWidget
    )

    -- 消费券信息
    self._wtConsumerCouponPanel = self:Wnd("DFCanvasPanel_89", UIWidgetBase)
    self._wtConsumerCouponRichTextBlock = self:Wnd("wtRichTextBlock_1", UITextBlock)
end

function RecommendBuyPacks:_OnToBuyGoodsCount()
    self:SetSort(#self.toShowGoods or 0)
    return #self.toShowGoods or 0
end

function RecommendBuyPacks:_OnProcessToBuyGoodsWidget(position, itemWidget)
    local goodItem = self.toShowGoods[position + 1]
    if goodItem ~= nil then
        if self.shopData ~= nil then
            local currencyItemId = 0
            local owned = General.IsHaved(goodItem.id)
            if self.isMallGift then
                currencyItemId = self.shopData.CurrencyType
            else
                currencyItemId = self.shopData.BundleCurrencyType
            end

            local bSingleBuy = #self.toShowGoods == 1
            itemWidget:SetItemInfo(goodItem, currencyItemId, owned, self.isMallGift, bSingleBuy)
        end
    end
end

function RecommendBuyPacks:OnBtnBunBundleClick()
    if self.shopData == nil then
        Module.CommonTips:ShowSimpleTip(StoreConfig.Loc.NotSelectItem)
        return
    end

    if self.isMallGift then
        self:DoBuyMallGifts()
    else
        self:DoBuyBundles()
    end
end

function RecommendBuyPacks:DoBuyMallGifts()
    if IsMobile() and IsBuildRegionGA() then
        if Module.Pay:IsGetProductInfoLoading() then
            logerror("RecommendBuyPacks:DoBuyMallGifts IsGetProductInfoLoading")
            return
        end
    end
    if self.shopData.IsCash > 0 then
        local bLimited = self:_getIsGiftBuyLimited()
        if bLimited == false then
            local payCallBack = CreateCallBack(function()
                if self.shopData.ProductID ~= nil then
                    local bGoStoreServer = true
                    if Server.PayServer:IsGarenaEnable() then
                        Module.Pay:PayGoods(self.shopData.ProductID)
                        bGoStoreServer = false
                    end
                    if Server.PayServer:IsCentauriEnable() and Server.SDKInfoServer:IsAndroidSelfPublishChannel() then
                        Module.Pay:Pay(self.shopData.ProductID)
                        bGoStoreServer = false
                    end

                    if bGoStoreServer then
                        local goods_id = self.shopData.GoodsId
                        local product_id = self.shopData.ProductID
                        local businessID = Module.Pay:GetBusinessId()
                        local payChannel = Server.PayServer:GetPayChannel()

                        Module.Reward:EnableNTFCall("Hero", false)
                        Server.StoreServer:SendShopBuyBuyMallGiftUseCashReq(goods_id, product_id, businessID, payChannel)
                    end
                end
            end, self)

            -- 判断是否支付柔性限制
            Server.PayServer:ReqPayQueryMidasRiskInfo(EFlexiblePaymentLimitType.Store, payCallBack)
        else
            Module.CommonTips:ShowSimpleTip(StoreConfig.Loc.BuyLimit)
        end
    else
        local goods_id = self.shopData.GoodsId
        local price = self.shopData.Price

        local currency_type = self.shopData.CurrencyType
        local currecny = Module.Currency:GetNumByItemId(currency_type)
        local consumerCouponNum = 0
        if currency_type == ECurrencyItemId.BindDiamond then
            consumerCouponNum = StoreLogic.GetConsumerCouponNum()
        end
        local currency_type_sub = StoreLogic.GetSubstituteCurrencyItemID()
        local currecny_sub = Module.Currency:GetNumByItemId(currency_type_sub)

        local fee = 0
        local fee_sub = 0
        local bMoneyEnough = false
        if currecny + consumerCouponNum >= price then
            bMoneyEnough = true
            if currecny >= price then
                fee = price
            else
                fee = currecny
                fee_sub = price - currecny
            end
        else
            --use currency_type_sub
            if currency_type ~= currency_type_sub then
                if currecny + currecny_sub + consumerCouponNum >= price then
                    bMoneyEnough = true
                    fee = currecny
                    fee_sub = price - currecny
                end
            end
        end

        if bMoneyEnough then
            local payCallBack = CreateCallBack(function()
                Module.Reward:EnableNTFCall("Hero", false)
                Server.StoreServer:SendShopBuyBuyMallGiftReq(goods_id, currency_type, fee, currency_type_sub, fee_sub, 1)
                self:_OnCloseBtnClicked()
            end, self)
            
            -- 如果有三角币支付，则判断是否柔性限制
            if (currency_type == ECurrencyItemId.Diamond and fee > 0) or
                    (currency_type_sub == ECurrencyItemId.Diamond and fee_sub > 0) then
                -- 判断是否支付柔性限制
                Server.PayServer:ReqPayQueryMidasRiskInfo(EFlexiblePaymentLimitType.Store, payCallBack)
            else
                payCallBack()
            end
        else
            -- Module.CommonTips:ShowSimpleTip(StoreConfig.Loc.NotEnoughMoney)
            loginfo("[ProductPreview] DoBuyMallGifts NotEnoughMoney currecny:" .. currecny)
            local showStr = StoreConfig.Loc.RechargeTipsWindowGoToChargeView
            local confirmStr = StoreConfig.Loc.StoreMainTabRecharge

            local _rechargeCancelHandle = function()
                self:_OnCloseBtnClicked()
            end

            local _rechargeConfirmHandle = function()
                Module.Store:ShowStoreRechargeMainPanle()
                self:_OnCloseBtnClicked()
            end

            Module.CommonTips:ShowConfirmWindow(showStr, _rechargeConfirmHandle, _rechargeCancelHandle, nil, confirmStr)
            return
        end

    end
end

function RecommendBuyPacks:_getIsGiftBuyLimited()
    return StoreLogic.GetMallGiftIsBuyLimitedByShopData(self.shopData)
end

function RecommendBuyPacks:_OnMidasPayFinished(ret)
    loginfo("[RecommendBuyPacks] _OnMidasPayFinished ret:" .. ret)
end

function RecommendBuyPacks:DoBuyBundles()

    local tab_id = self.shopData.TabId
    local banner_type = self.shopData.BannerType
    local item_ids = {}

    local price = 0 --self.shopData.DisBundlePrice

    if self.toBuyGoods ~= nil and #self.toBuyGoods <= 1 then
        local to_buy_good = self.toBuyGoods[1]
        price = to_buy_good.price
        if self.isBundleBuyAll == false then
            table.insert(item_ids, to_buy_good.id)
        end
    else
        price = self:_getBuyBundlePrice()
        if self.isBundleBuyAll == false then
            for k, v in pairs(self.toBuyGoods) do
                table.insert(item_ids, v.id)
            end
        end
    end

    local currency_type = self.shopData.BundleCurrencyType
    local currecny = Module.Currency:GetNumByItemId(currency_type)
    local consumerCouponNum = 0
    if currency_type == ECurrencyItemId.BindDiamond then
        consumerCouponNum = StoreLogic.GetConsumerCouponNum()
    end
    local currency_type_sub = StoreLogic.GetSubstituteCurrencyItemID()
    local currecny_sub = Module.Currency:GetNumByItemId(currency_type_sub)

    local fee = 0
    local fee_sub = 0
    local bMoneyEnough = false
    if currecny + consumerCouponNum >= price then
        bMoneyEnough = true
        if currecny >= price then
            fee = price
        else
            fee = currecny
            fee_sub = price - currecny
        end
    else
        --use currency_type_sub
        if currency_type ~= currency_type_sub then
            if currecny + currecny_sub + consumerCouponNum >= price then
                bMoneyEnough = true
                fee = currecny
                fee_sub = price - currecny
            end
        end
    end

    if bMoneyEnough then
        local payCallBack = CreateCallBack(function()
            Module.Reward:EnableNTFCall("Hero", false)
            Server.StoreServer:SendShopBuyHotRecommendationReq(tab_id, banner_type, item_ids, currency_type, fee,
                    currency_type_sub, fee_sub)
            self:_OnCloseBtnClicked()
        end, self)

        -- 如果有三角币支付，则判断是否柔性限制
        if (currency_type == ECurrencyItemId.Diamond and fee > 0) or
                (currency_type_sub == ECurrencyItemId.Diamond and fee_sub > 0) then
            -- 判断是否支付柔性限制
            Server.PayServer:ReqPayQueryMidasRiskInfo(EFlexiblePaymentLimitType.Store, payCallBack)
        else
            payCallBack()
        end
    else
        loginfo("[ProductPreview] DoBuyMallGifts NotEnoughMoney currecny:" .. currecny)
        local _rechargeCancelHandle = function()
            self:_OnCloseBtnClicked()
        end

        local _rechargeConfirmHandle = function()
            Module.Store:ShowStoreRechargeMainPanle()
            self:_OnCloseBtnClicked()
        end

        local showStr = StoreConfig.Loc.RechargeTipsWindowGoToChargeView
        local confirmStr = StoreConfig.Loc.StoreMainTabRecharge
        Module.CommonTips:ShowConfirmWindow(showStr, _rechargeConfirmHandle, _rechargeCancelHandle, nil, confirmStr)
        return
    end
end

function RecommendBuyPacks:CalcBuyPrice()

end

function RecommendBuyPacks:_OnCloseBtnClicked()
    Facade.UIManager:CloseUI(self)
end

function RecommendBuyPacks:OnNavBack()
    self:_OnCloseBtnClicked()
    return true
end

function RecommendBuyPacks:_OnTabPressed()
    Facade.UIManager:CloseUI(self)
end

-- UI构造时触发, 用于初始化外部传参UI数据逻辑，由UIManager触发（仅触发一次）
-- 子节点蓝图不会触发该方法
-- Module加载好子控件所需的图片后，将数据传入主UI界面
function RecommendBuyPacks:OnInitExtraData(shopData, toShowGoods, toBuyGoods, isMallGift, isBundleBuyAll)
    self.isMallGift = isMallGift

    self.shopData = shopData
    self.toShowGoods = toShowGoods
    self.toBuyGoods = toBuyGoods
    self.isBundleBuyAll = isBundleBuyAll

    if self.isMallGift then
        self.goodsItems = self.shopData.BundleItems
        self.buyRecord = Server.StoreServer:GetGetMallGiftRecordByGoodsId(self.shopData.GoodsId)
    else
        self.goodsItems = self.shopData.BundleItems
        self.buyRecord = Server.StoreServer:GetRecommondBuyRecordByTabId(self.shopData.TabId)
    end
end

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function RecommendBuyPacks:OnClose()
    self:RemoveAllLuaEvent()
end

function RecommendBuyPacks:OnOpen()

    local count = self:_OnToBuyGoodsCount()
    if count <= 1 then
        self._wtScrollBox:SetColumn(1)
    elseif count <= 2 then
        self._wtScrollBox:SetColumn(2)
    else
        self._wtScrollBox:SetColumn(3)
    end

    self._wtScrollBox:RefreshAllItems()
    if self.isMallGift then
        self:ShowMallGiftInfo()
    else
        self:ShowBundleInfo()
    end
end

function RecommendBuyPacks:ShowMallGiftInfo()
    if self._wtRichTextBlock ~= nil then

        self.buyRecord = Server.StoreServer:GetGetMallGiftRecordByGoodsId(self.shopData.GoodsId)

        if self.shopData.IsCash > 0 then
            self._wtRichTextBlock:SetText("")
        else
            local price = self.shopData.Price

            local currency_type = self.shopData.CurrencyType
            local currecny = Module.Currency:GetNumByItemId(currency_type)
            local currency_type_sub = StoreLogic.GetSubstituteCurrencyItemID()
            local currecny_sub = Module.Currency:GetNumByItemId(currency_type_sub)

            if currecny >= price then
                local priceStr = string.format(StoreConfig.Loc.BundlePurchaseTipMoneyEnough,
                    Module.Currency:GetRichTxtImgByItemId(currency_type),
                    MathUtil.GetNumberFormatStr(price))

                self._wtRichTextBlock:SetText(priceStr)
            else
                --use currency_type_sub
                if currency_type ~= currency_type_sub then
                    if currecny + currecny_sub >= price then
                        local priceStr = string.format(StoreConfig.Loc.BundlePurchaseTipMoneyAndBindEnough,
                            Module.Currency:GetRichTxtImgByItemId(currency_type),
                            MathUtil.GetNumberFormatStr(currecny),
                            Module.Currency:GetRichTxtImgByItemId(currency_type_sub),
                            MathUtil.GetNumberFormatStr(price - currecny))

                        self._wtRichTextBlock:SetText(priceStr)
                    else
                        --currecny + currecny_sub not enough
                        local priceStrNotEnough = string.format(StoreConfig.Loc.BundlePurchaseTipBindMoneyNotEnough,
                            Module.Currency:GetRichTxtImgByItemId(currency_type),
                            MathUtil.GetNumberFormatStr(currecny),
                            Module.Currency:GetRichTxtImgByItemId(currency_type_sub),
                            MathUtil.GetNumberFormatStr(price - currecny),
                            Module.Currency:GetRichTxtImgByItemId(currency_type_sub),
                            MathUtil.GetNumberFormatStr(price - currecny - currecny_sub))

                        self._wtRichTextBlock:SetText(priceStrNotEnough)
                    end
                else
                    --money not enough
                    local priceStrNotEnough = string.format(StoreConfig.Loc.BundlePurchaseTipMoneyNotEnough,
                        Module.Currency:GetRichTxtImgByItemId(currency_type),
                        MathUtil.GetNumberFormatStr(price), Module.Currency:GetRichTxtImgByItemId(currency_type),
                        MathUtil.GetNumberFormatStr(price - currecny))

                    self._wtRichTextBlock:SetText(priceStrNotEnough)
                end
            end

        end
    end
end

function RecommendBuyPacks:ShowBundleInfo()
    if self._wtRichTextBlock ~= nil then

        local price = 0
        if self.toBuyGoods ~= nil and #self.toBuyGoods <= 1 then
            local to_buy_good = self.toBuyGoods[1]
            price = to_buy_good.price
        else
            price = self:_getBuyBundlePrice()
        end

        local currency_type = self.shopData.BundleCurrencyType
        local currecny = Module.Currency:GetNumByItemId(currency_type)
        local currency_type_sub = StoreLogic.GetSubstituteCurrencyItemID()
        local currecny_sub = Module.Currency:GetNumByItemId(currency_type_sub)

        if currecny >= price then
            local priceStr = string.format(StoreConfig.Loc.BundlePurchaseTipMoneyEnough,
                Module.Currency:GetRichTxtImgByItemId(currency_type),
                MathUtil.GetNumberFormatStr(price))

            self._wtRichTextBlock:SetText(priceStr)
        else
            --use currency_type_sub
            if currency_type ~= currency_type_sub then
                if currecny + currecny_sub >= price then
                    local priceStr = string.format(StoreConfig.Loc.BundlePurchaseTipMoneyAndBindEnough,
                        Module.Currency:GetRichTxtImgByItemId(currency_type),
                        MathUtil.GetNumberFormatStr(currecny),
                        Module.Currency:GetRichTxtImgByItemId(currency_type_sub),
                        MathUtil.GetNumberFormatStr(price - currecny))

                    self._wtRichTextBlock:SetText(priceStr)
                else
                    --currecny + currecny_sub not enough
                    local priceStrNotEnough = string.format(StoreConfig.Loc.BundlePurchaseTipBindMoneyNotEnough,
                        Module.Currency:GetRichTxtImgByItemId(currency_type),
                        MathUtil.GetNumberFormatStr(currecny),
                        Module.Currency:GetRichTxtImgByItemId(currency_type_sub),
                        MathUtil.GetNumberFormatStr(price - currecny),
                        Module.Currency:GetRichTxtImgByItemId(currency_type_sub),
                        MathUtil.GetNumberFormatStr(price - currecny - currecny_sub))

                    self._wtRichTextBlock:SetText(priceStrNotEnough)
                end
            else
                --money not enough
                local priceStrNotEnough = string.format(StoreConfig.Loc.BundlePurchaseTipMoneyNotEnough,
                    Module.Currency:GetRichTxtImgByItemId(currency_type),
                    MathUtil.GetNumberFormatStr(price), Module.Currency:GetRichTxtImgByItemId(currency_type),
                    MathUtil.GetNumberFormatStr(price - currecny))

                self._wtRichTextBlock:SetText(priceStrNotEnough)
            end
        end
    end
end

function RecommendBuyPacks:_getBuyBundlePrice()
    --  bundleItem {
    --     uint64 id = 1;
    --     int64 num = 2;
    --     int64 dis_price = 3; // 折后价
    --     int64 price = 4; // 原价
    --   }
    local price = 0
    local priceOrign = 0

    local needBuyCount = 0
    if self.goodsItems ~= nil then
        for k, v in pairs(self.goodsItems) do
            if v.dis_price > 0 then
                local isSell = self:CheckIsSellBuyGoodId(v.id)
                local collectionItemInfo = Server.CollectionServer:GetWeaponSkinIfOwned(v.id)
                local bIsOwned = collectionItemInfo ~= nil and collectionItemInfo.num > 0
                if isSell == false and bIsOwned == false then
                    needBuyCount = needBuyCount + 1
                    price = price + v.dis_price
                    priceOrign = priceOrign + v.price
                end
            end
        end
    end

    if needBuyCount <= 1 then
        price = priceOrign
    end

    return price
end

function RecommendBuyPacks:CheckIsSellBuyGoodId(id)
    local isOwned = false

    if self.buyRecord ~= nil then

        if self.isMallGift then
            local bLimited = self:_getIsGiftBuyLimited()
            if bLimited == true or (self.shopData.LimitType ~= nil and self.shopData.LimitType > 0 and self.buyRecord.num >= self.shopData.LimitAmount) then
                isOwned = true
            end
        else
            if self.buyRecord.is_sold_out then
                isOwned = true
            else
                for key, value in pairs(self.buyRecord.item_ids) do
                    if value == id then
                        isOwned = true
                        break
                    end
                end

                local collectionItemInfo = Server.CollectionServer:GetWeaponSkinIfOwned(id)
                local bIsOwned = collectionItemInfo ~= nil and collectionItemInfo.num > 0
                if bIsOwned then
                    isOwned = true
                end
            end
        end
    end

    return isOwned
end

function RecommendBuyPacks:ConsumerCouponCountDownFunc()
    -- 三角券和限时消费券具有同等效益
    local currency_type
    if self.isMallGift then
        currency_type = self.shopData.CurrencyType
    else
        currency_type = self.shopData.BundleCurrencyType
    end
    if currency_type ~= ECurrencyItemId.BindDiamond then
        self._wtConsumerCouponPanel:Collapsed()
        return
    end
    local consumerCouponInfoStr = StoreLogic.GetConsumerCouponInfoStr()
    if consumerCouponInfoStr then
        self._wtConsumerCouponRichTextBlock:SetText(consumerCouponInfoStr)
        self._wtConsumerCouponPanel:SelfHitTestInvisible()
    else
        self._wtConsumerCouponPanel:Collapsed()
    end
end

-- UI OnOpen后、调用Show()显示时触发
-- 做表现
---@overload fun(LuaUIBaseView, OnShow)
function RecommendBuyPacks:OnShow()
    Module.Login.Config.Events.evtOnRefreshToken:Invoke()
    self:ConsumerCouponCountDownFunc()
    if self._countDownTimer == nil then
        self._countDownTimer = Timer:NewIns(1, 0)
        self._countDownTimer:AddListener(self.ConsumerCouponCountDownFunc, self)
        self._countDownTimer:Start()
    end
end

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function RecommendBuyPacks:OnHide()
    if self._countDownTimer ~= nil then
        self._countDownTimer:Stop()
        self._countDownTimer:Release()
        self._countDownTimer = nil
    end
end

--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
function RecommendBuyPacks:OnShowBegin()
    if IsHD() then
        self:_EnableGamepadFeature()
    end
end

function RecommendBuyPacks:OnHideBegin()
    if IsHD() then
        self:_DisableGamepadFeature()
    end
end

function RecommendBuyPacks:_EnableGamepadFeature()
    if not IsHD() then
        return 
    end

    -- 手柄模式下，确认为长按X键
    if self._wtCommonPopWin then
        self._wtCommonPopWin:OverrideGamepadSetting("MandelBrickBuyComfirm_Gamepad", nil, WidgetUtil.EUINavDynamicType.NoA_Direction, false)
    end

    if not self._NavGroup then
        --配置导航
         self._NavGroup = WidgetUtil.RegisterNavigationGroup(self._wtScrollBox, self, "Hittest")
         self._NavGroup:AddNavWidgetToArray(self._wtScrollBox)
         WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup)
         self._NavGroup:SetScrollRecipient(self._wtScrollBox)
     end
end

function RecommendBuyPacks:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end

    -- 移除导航组
    WidgetUtil.RemoveNavigationGroup(self)
    self._NavGroup = nil
end
--- END MODIFICATION

return RecommendBuyPacks
