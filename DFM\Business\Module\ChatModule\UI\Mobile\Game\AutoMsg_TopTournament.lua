----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMChat)
----- LOG FUNCTION AUTO GENERATE END -----------



local AutoMsg_TopTournament = ui("AutoMsg_TopTournament")

local EMsgPanelType = Module.Chat.Config.EMsgPanelType
local GameHUDState = import "GameHUDSate"
local UIManager = Facade.UIManager
local ChatLogic = require "DFM.Business.Module.ChatModule.Logic.ChatLogic"

local function log(...)
    print("[AutoMsgMentionPanel]",...)
end

function AutoMsg_TopTournament:Ctor()
    log("AutoMsgMentionPanel:Ctor")

    self._wtMaxChatHistoryCount = 4
    self.localChatHistoryCount = self._wtMaxChatHistoryCount
    self._wtContents = {0,0,0,0}
    self._wtTempMsgHistorySB = self:Wnd("TempMsgHistorySB", UIScrollBox)
    for i = 1,self.localChatHistoryCount do
        self._wtContents[i] = self:Wnd("RichTextBlock_"..i,UITextBlock)
        self._wtContents[i]:SetText("")
    end

    self._wtTempMsgHistorySB:Collapsed()

    --设置显隐规则
    self:AddStateToInVisibleGameHudState(GameHUDState.GHS_PrepareTime)
    self:AddStateToInVisibleGameHudState(GameHUDState.GHS_Settlement)
    self:AddStateToInVisibleGameHudState(GameHUDState.GHS_CutScene)
    self:AddStateToInVisibleGameHudState(GameHUDState.GHS_Operating3DUI)
	self:AddStateToInVisibleGameHudState(GameHUDState.GHS_RescueSomeOne)
	self:AddStateToInVisibleGameHudState(GameHUDState.GHS_ForbidFire)
	self:AddStateToInVisibleGameHudState(GameHUDState.GHS_UseTelescope)
	self:AddStateToInVisibleGameHudState(GameHUDState.GHS_PVEQuestPanelOnly)
	self:AddStateToInVisibleGameHudState(GameHUDState.GHS_SOLQuestOperation)

    self:AddLuaEvent(Server.ChatServer.evtChatDSUpdated, self._OnChatDSUpdated, self)

    Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.Default)

end

function AutoMsg_TopTournament:OnInitExtraData(...)
    log("AutoMsgMentionPanel:OnInitExtraData")
end

function AutoMsg_TopTournament:OnOpen()
    log("AutoMsgMentionPanel:OnOpen")
end

function AutoMsg_TopTournament:OnClose()
    log("AutoMsgMentionPanel:OnClose")
    self:RemoveAllLuaEvent()
    if self._msgHistoryTimerHandle then
        self._msgHistoryTimerHandle:Release()
        self._msgHistoryTimerHandle = nil
    end
end

function AutoMsg_TopTournament:_OnChatDSUpdated(bRefreshMsgHistorySGB)
    log("AutoMsgMentionPanel:_OnChatDSUpdated")

    local CollapseMsgHistorySGB = function()
        -- 因为这函数存在异步调用 所以有概率对应UI实例已清理
        if self._wtTempMsgHistorySB and self._wtTempMsgHistorySB.Collapsed then
            self._wtTempMsgHistorySB:Collapsed()
        end

        if self._msgHistoryTimerHandle then
            self._msgHistoryTimerHandle:Release()
            self._msgHistoryTimerHandle = nil
        end
    end

    if bRefreshMsgHistorySGB then
        self:_RefreshHistoryMsgPanel()
        if self._msgHistoryTimerHandle then
            self._msgHistoryTimerHandle:Release()
        end
        self._msgHistoryTimerHandle = Timer:NewIns(6, 1)
        self._msgHistoryTimerHandle:AddListener(CollapseMsgHistorySGB)
        self._msgHistoryTimerHandle:Start()
    end
end


function AutoMsg_TopTournament:_RefreshHistoryMsgPanel()
    log("Panel:_RefreshHistoryMsgPanel")

    -- local OnLoadSubItemUIFin = function(ui)
    --     self._wtTempMsgHistorySB:AddChild(ui)
    --     ui:SetIsEnabled(false)
    --     self._wtTempMsgHistorySB:ScrollToEnd()
    -- end
    self._wtTempMsgHistorySB:SetVisibility(ESlateVisibility.HitTestInvisible)
    local contentsNum = #Server.ChatServer.chatHistory
    if contentsNum >= self._wtMaxChatHistoryCount then
        for i = 1,self._wtMaxChatHistoryCount do
            local txtInfo = Server.ChatServer.chatHistory[#Server.ChatServer.chatHistory + i - self._wtMaxChatHistoryCount]
            self:SetSingleItemContent(txtInfo, i)
        end
    else

        for i = 1,contentsNum do
            local txtInfo = Server.ChatServer.chatHistory[i]
            self:SetSingleItemContent(txtInfo, i)
        end


        for i = contentsNum+1,self._wtMaxChatHistoryCount do
            self._wtContents[i]:Collapsed()
            self._wtContents[i]:SetText("")
        end

    end
    self._wtTempMsgHistorySB:ScrollToEnd()
end

function AutoMsg_TopTournament:SetSingleItemContent(txtInfo, index)
    if txtInfo.msgType == ChatMsgType.TeamInvite then
        local dataValue = Module.Team.Field.GameTeamInviteViewModel:GetCacheData(txtInfo.mapIndex)
        local showTxt = ChatLogic.InGameInviteChatMsgMake(dataValue, true)
        self._wtContents[index]:SetText(showTxt)
        self._wtContents[index]:Visible()
        return
    end

    local senderId = txtInfo.senderId
    local senderName = txtInfo.senderName
    local content = txtInfo.content
    local showTxt = ""
    if txtInfo.msg.msg_type == ChatMsgType.PresetStr then
        content = ChatLogic.ResolvePresetChat(txtInfo.msg.preset_msg)
    end
    showTxt = self:GetInfoStyle_InGame(content, txtInfo.chatType, senderName, senderId)
    self._wtContents[index]:SetText(showTxt)
    self._wtContents[index]:Visible()
end

function AutoMsg_TopTournament:GetInfoStyle_InGame(content, chatType, senderName, senderId)
    local showTxt = ""
    senderName = ChatLogic.GetPlayerName(senderId)

    -- TODO 后面颜色代码改成枚举
    if chatType == DSChatType.DSChatCamp then
        showTxt = string.format("<customstyle color=\"Color_CampNPC\">%s：</>%s", senderName, content)
    else
        showTxt = string.format("<customstyle color=\"Color_Chat_Team\">%s：</>%s", senderName, content)
    end

    showTxt = ChatLogic.AddCommanderPrefix(senderId, showTxt, chatType == DSChatType.DSChatCamp)

    return showTxt
end

return AutoMsg_TopTournament