----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMInsurance)
----- LOG FUNCTION AUTO GENERATE END -----------



UITable[UIName2ID.InsuranceMainView] = {
    UILayer = EUILayer.Stack,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "InsuranceModule.UI.InsuranceMain"),
    BPKey = "WBP_PropRecoveryMain",
}
UITable[UIName2ID.InsuranceMatchCell] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "InsuranceModule.UI.InsuranceMatchCell"),
    BPKey = "WBP_PropRecoveryList",
}
UITable[UIName2ID.InsuranceItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "InsuranceModule.UI.InsuranceItem"),
    BPKey = "WBP_PropRecoveryBox",
}
--[[UITable[UIName2ID.InsuranceTagSub] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "InsuranceModule.UI.InsuranceTagSub"),
    BPKey = "WBP_CommonDroDownBox_Btn",
}--]]

InsuranceConfig = {
    MapTable = Facade.TableManager:GetTable("MapConfig")
}
local InsuranceConfig = {
    evtInsuranceItemSelected = LuaEvent:NewIns("evtInsuranceItemSelected"),
    evtInsuranceBroadCast2ItemDetailView = LuaEvent:NewIns("evtInsuranceBroadCast2ItemDetailView")
}
InsuranceConfig.Loc = {
    Title = NSLOCTEXT("InsuranceModule", "Lua_InsuranceMallMain", "装备找回"),
    ConfirmTxt = NSLOCTEXT("InsuranceModule", "ConfirmTxt", "清空之后的物品将无法购回，是否清空？"),
    LeftTabTxt = NSLOCTEXT("InsuranceModule", "LeftTabTxt", "遗失物"),
    ClearBtnTxt = NSLOCTEXT("InsuranceModule", "ClearBtnTxt", "清空"),
    BuyBtnTxt = NSLOCTEXT("InsuranceModule", "BuyBtnTxt", "购买"),
    MaxStackCount = NSLOCTEXT("InsuranceModule", "MaxStackCount", "数量 "),
    BuyHint = NSLOCTEXT("InsuranceModule", "BuyHint", "所选物品包含%s，确认以%s总价购买吗？"),
    BuyHintEtc = NSLOCTEXT("InsuranceModule", "BuyHintEtc", "所选物品包含%s等道具，确认以%s总价购买吗？"),
}
return InsuranceConfig
