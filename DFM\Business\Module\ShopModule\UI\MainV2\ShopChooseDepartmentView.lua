----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMShop)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class ShopChooseDepartmentView : LuaUIBaseView
local ShopChooseDepartmentView = ui("ShopChooseDepartmentView")
local EDepartmentType = Module.Shop.Config.EDepartmentType
local FAnchors = import "Anchors"

function ShopChooseDepartmentView:Ctor()
    -- 军需处
    self._wtShopChooseMerchantScrollBox =
        UIUtil.WndScrollGridBox(
        self,
        "ScrollGridBox_merchant",
        self.OnGetMerchantItemCount,
        self.OnProcessMerchantItemWidget,
        self.OnMerchantItemsUpdateFinished
    )
    -- 任务
    self._wtTaskChooseLinePanel = self:Wnd("TaskPanel", UIWidgetBase)
    self._wtChapterScrollBox =
        UIUtil.WndScrollGridBox(self, "wChapterScrollBox", self.OnGetTaskItemCount, self.OnProcessTaskItemWidget)
    -- self._wtChapterScrollBox:SetCppValue("ItemFixedSize", FVector2D(21, 50)) --TODO:不确定加不加，可以取消注释试试效果

    self._wtCanvasPanel = self:Wnd("CanvasPanel_0", UIWidgetBase)

    if DFHD_LUA == 0 then
        -- Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.HideInfo)
        -- self._wtChapterScrollBox:SetCppValue("ItemFixedSize", FVector2D(21, 50))
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.DefaultSecondary)
    else
        logerror("出问题！ShopChooseDepartmentView 文件在PC端打开！")
    end

    --动画播放相关
    self._PLAY_SHOW_ANIM_MAX_COUNT = 5
    self._CELL_ROW_COUNT = 1
    self._animStartDelayTime = self.BP_merchantInStartDelay or 0.05
    self._animDeltaTime = self.BP_merchantInDelta or 0.05
    self._showAnimFinishList = {}
    self._hideAnimFinishList = {}

    self._curSelectTab = EDepartmentType.Quest
end

function ShopChooseDepartmentView:OnInitExtraData(selectTabIndex, questType)
    self._curSelectTab = Module.Shop.Field:GetCurSelectedDepartmentType()
    if selectTabIndex and selectTabIndex ~= self._curSelectTab then
        Module.Shop.Field:SetCurSelectedDepartmentType(selectTabIndex)
        self._curSelectTab = selectTabIndex
    end
    --军需处
    self._merchantIdList = Server.ShopServer:GetMerchantIdList()
    --任务
    self:_UpdateTaskChapter(questType)
    -- reset offset
    self._wtShopChooseMerchantScrollBox:SetScrollOffset(0, false, true, 15)
    self._wtChapterScrollBox:SetScrollOffset(0, false, true, 15)
end

function ShopChooseDepartmentView:_UpdateTaskChapter(questType)
    self._questType = setdefault(questType, QuestType.Mission)
    self._questLineChapInfos = {}
    local questLineInfos = Server.QuestServer:GetQuestLineInfosByQuestType(self._questType)
    for _, lineInfo in ipairs(questLineInfos) do
        if lineInfo:IsLineOpend() or lineInfo.bUnopenShow then
            table.insert(self._questLineChapInfos, lineInfo)
        end
    end
    self._PLAY_SHOW_ANIM_MAX_COUNT = #self._questLineChapInfos
end

function ShopChooseDepartmentView:OnShowBegin()
    self:AddLuaEvent(Server.ShopServer.Events.evtMerchantDataChanged, self.OnMerchantDataFresh, self)
    self:AddLuaEvent(Server.ShopServer.Events.evtTimeChangeFresh, self.OnTimeChangeFresh, self)
    self:AddLuaEvent(Server.QuestServer.Events.evtLockedQuestLineOpen, self._OnLockedQuestLineOpen, self)

    -- 清理一下之前注册的topbar的节点
    Module.Guide:RemoveGuideWidgetProxy(Module.Guide.Config.EGuideProxyWidget.guideProxyTopBar_0)
    Module.Guide:RemoveGuideWidgetProxy(Module.Guide.Config.EGuideProxyWidget.guideProxyTopBar_1)

    --注册顶bar
    self:InitGroupTab()

    self._showAnimFinishList = {}
    self._hideAnimFinishList = {}

    --准备商人数据+刷新界面
    self:_FreshMerchantDataList()
    Server.ShopServer:FreshMerchantsOnly(
        CreateCallBack(
            function(self)
                self:_FreshMerchantDataList()
            end,
            self
        )
    )

    --注册事件

    -- 新手引导期间特殊处理，禁止滑动
    if Module.Guide:IsNewPlayerGuiding() then
        self.oriOffset = self._wtChapterScrollBox:GetScrollOffset()
        self._wtChapterScrollBox:Event("OnUserScrolled", self.OnScrollBoxScrolled, self)
        self._wtChapterScrollBox:SetAllowOverscroll(false)
    else
        self._wtChapterScrollBox:Event("OnUserScrolled", nil, nil)
        self._wtChapterScrollBox:SetAllowOverscroll(true)
    end
    -- Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIShopChooseOpen) --TODO:这里不一定播放这个音效
    -- Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIQuestOpen) --TODO:这里到底播放哪个啊

    self:SwitchDepartment(self._curSelectTab)
    Server.QuestServer:GetQuestLineSeasonDataReq()
end

function ShopChooseDepartmentView:OnShow()
    if self._curSelectTab == EDepartmentType.Quest then
        Server.TipsRecordServer:SetBoolean(Server.TipsRecordServer.keys.GuideDepartmentOpened, true)
        self:_OpenQusetOpenPanel()
    elseif self._curSelectTab == EDepartmentType.Shop then
    end
end

function ShopChooseDepartmentView:OnHideBegin()
    self:RemoveLuaEvent(Server.ShopServer.Events.evtMerchantDataChanged, self.OnMerchantDataFresh)
    self:RemoveLuaEvent(Server.ShopServer.Events.evtTimeChangeFresh, self.OnTimeChangeFresh)
    self:RemoveLuaEvent(Server.QuestServer.Events.evtLockedQuestLineOpen)

    self._showAnimFinishList = {}
    self._hideAnimFinishList = {}
    self.bHideProcess = true
    self._wtShopChooseMerchantScrollBox:RefreshVisibleItems()
    self._wtChapterScrollBox:RefreshVisibleItems()
    Module.Shop.Field:SetCurSelectedDepartmentType(self._curSelectTab)

    if Module.Quest:GetToDetailFromSource() ~= Module.Quest.Config.EEnterToDetailType.Chapter then
        Server.QuestServer.Events.evtQuestExitDetailView:Invoke()
        loginfo("ShopChooseDepartmentView:OnHide Clear")
    end

    if self._seasonPanel then 
        Facade.UIManager:RemoveSubUI(self,UIName2ID.QuestSeasonMainPanel,self._seasonPanel)
        self._seasonPanel = nil
    end
end

function ShopChooseDepartmentView:InitGroupTab()
    Module.CommonBar:RegStackUITopBarTitle(self, Module.Shop.Config.Loc.DepartmentTitle)
    -- 注册红点
    local reddotTrieRegItemList = {
        [1] = {
            uiNavId = UIName2ID.TopBar,
            reddotDataConfigWithStyleList = {{obType = EReddotTrieObserverType.Quest, key = ""}}
        },
        [2] = {
            uiNavId = UIName2ID.TopBar,
            reddotDataConfigWithStyleList = {{obType = EReddotTrieObserverType.Quest, key = ""}}
        },
        [3] = {
            uiNavId = UIName2ID.TopBar,
            reddotDataConfigWithStyleList = {{obType = EReddotTrieObserverType.Shop, key = ""}}
        }
    }
    --注册2级tab
    local topTabGroupRegInfo = {
        tabTxtList = {Module.Quest.Config.Loc.quest, Module.Quest.Config.Loc.QuestSeason, Module.Shop.Config.Loc.MerchantTitle},
        imgPathList = Module.Shop.Config.DepartmentImgList,
        fCallbackIns = SafeCallBack(self._OnTabChanged, self),
        defalutIdx = self._curSelectTab,
        tabGroupSize = FVector2D(1904, 96),
        tabSpaceMargin = FMargin(0, 0, 16, 0),
        bTriggerCallback = true,
        bNewReddotTrie = true,
        reddotTrieRegItemList = reddotTrieRegItemList
    }
    Module.CommonBar:RegStackUITopBarTabGroupRegInfo(self, topTabGroupRegInfo)
end

function ShopChooseDepartmentView:SwitchDepartment(selectTabIndex)
    if selectTabIndex == EDepartmentType.Shop then
        self._wtShopChooseMerchantScrollBox:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self._wtTaskChooseLinePanel:SetVisibility(ESlateVisibility.Collapsed)
        if self._seasonPanel then 
            Facade.UIManager:RemoveSubUI(self,UIName2ID.QuestSeasonMainPanel,self._seasonPanel)
            self._seasonPanel = nil
        end
        self._hideAnimFinishList = {}
        self._showAnimFinishList = {}
        self._wtShopChooseMerchantScrollBox:RefreshVisibleItems()
        Module.Shop.Config.evtShopChooseOpenFinish:Invoke()
    elseif selectTabIndex == EDepartmentType.Quest then
        self:_UpdateTaskChapter()
        self._wtShopChooseMerchantScrollBox:SetVisibility(ESlateVisibility.Collapsed)
        if self._seasonPanel then 
            Facade.UIManager:RemoveSubUI(self,UIName2ID.QuestSeasonMainPanel,self._seasonPanel)
            self._seasonPanel = nil
        end
        self._wtTaskChooseLinePanel:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self._wtChapterScrollBox:RefreshVisibleItems()
    elseif selectTabIndex == EDepartmentType.QuestSeason then
        self._wtShopChooseMerchantScrollBox:SetVisibility(ESlateVisibility.Collapsed)
        self._wtTaskChooseLinePanel:SetVisibility(ESlateVisibility.Collapsed)
        local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self,UIName2ID.QuestSeasonMainPanel,self._wtCanvasPanel)
        
        local uiIns = getfromweak(weakUIIns)
        local canvasSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(uiIns)
        if not canvasSlot then return end
        local commonAnchor = FAnchors()
        local commonOffset = FMargin(0, 0, 0, 0)
        commonAnchor.Minimum = LuaGlobalConst.TOP_LEFT_VECTOR
        commonAnchor.Maximum = LuaGlobalConst.BOTTOM_RIGHT_VECTOR
        canvasSlot:SetAnchors(commonAnchor)
        canvasSlot:SetOffsets(commonOffset)

        self._seasonPanel = instanceId
    end
    Module.Shop.Config.evtSelectedDepartmentIdChanged:Invoke(selectTabIndex)
end

function ShopChooseDepartmentView:_OnTabChanged(curActiveIndex, lastActiveIndex)
    if self._curSelectTab ~= curActiveIndex then
        self._curSelectTab = curActiveIndex
        -- self:ResetAnimVariables()
        self:SwitchDepartment(self._curSelectTab)
        if self._curSelectTab == EDepartmentType.Quest then
            self._wtChapterScrollBox:ScrollToStart(false, false, 0)
        end
    end
end

function ShopChooseDepartmentView:OnGetMerchantItemCount()
    local count = #self._merchantIdList
    return count
end

function ShopChooseDepartmentView:OnGetTaskItemCount()
    return #self._questLineChapInfos
end

function ShopChooseDepartmentView:OnProcessMerchantItemWidget(position, itemWidget)
    loginfo("ShopChooseMerchantView:OnProcessMerchantItemWidget", position, itemWidget)
    local mid = self._merchantIdList[position + 1]
    if mid then
        self._curSelectedIndex = position
        itemWidget:FreshMerchantByMid(mid)

        -- 动画相关
        local rowIdx = position / self._CELL_ROW_COUNT
        if self.bHideProcess then
            if rowIdx < self._PLAY_SHOW_ANIM_MAX_COUNT then
                if not self._showAnimFinishList[position] then
                    itemWidget:PlayHideAni(rowIdx * self._animDeltaTime + self._animStartDelayTime)
                    self._showAnimFinishList[position] = true
                end
            end
        else
            if rowIdx < self._PLAY_SHOW_ANIM_MAX_COUNT then
                if not self._hideAnimFinishList[position] then
                    itemWidget:PlayShowAni(rowIdx * self._animDeltaTime + self._animStartDelayTime)
                    self._hideAnimFinishList[position] = true
                end
            end
        end

        -- 注册一下新手引导中会引导到的商人对应的ui
        local specGuideItemInfo =
            Module.Guide:GetNewPlayerGuideItemInfo(Module.Guide.Config.NewPlayerGuideSpecItem.merchantId)
        if specGuideItemInfo and mid == tonumber(specGuideItemInfo[1]) then
            Module.Guide:AddGuideWidgetProxy(Module.Guide.Config.EGuideProxyWidget.guideProxyShopChoose, itemWidget)
        end
    else
        logwarning("Missing type info for position", position)
    end
end

function ShopChooseDepartmentView:OnProcessTaskItemWidget(position, itemWidget)
    local idx = position + 1
    itemWidget:SetQuestLineChapInfo(idx, self._questLineChapInfos[idx])

    -- 动画相关
    if idx <= self._PLAY_SHOW_ANIM_MAX_COUNT then
        itemWidget:PlayShowAni(self._animStartDelayTime + (idx - 1) * self._animDeltaTime)
    end

    -- 注册一下新手引导中会引导到的任务npc对应的ui
    local specGuideItemInfo =
        Module.Guide:GetNewPlayerGuideItemInfo(Module.Guide.Config.NewPlayerGuideSpecItem.questChapter)
    if specGuideItemInfo and itemWidget:GetIndex() == tonumber(specGuideItemInfo[1]) then
        Module.Guide:AddGuideWidgetProxy(
            Module.Guide.Config.EGuideProxyWidget.guideProxyQuestChapter,
            itemWidget._wtQuestChapButton
        )
    end
    local specGuideItemInfo =
        Module.Guide:GetNewPlayerGuideItemInfo(Module.Guide.Config.NewPlayerGuideSpecItem.questChapter2)
    if specGuideItemInfo and itemWidget:GetIndex() == tonumber(specGuideItemInfo[1]) then
        Module.Guide:AddGuideWidgetProxy(
            Module.Guide.Config.EGuideProxyWidget.guideProxyQuestChapter2,
            itemWidget._wtQuestChapButton
        )
    end
end

function ShopChooseDepartmentView:OnMerchantItemsUpdateFinished()
    self:ScrollToMerchantReddot()
end

function ShopChooseDepartmentView:ScrollToMerchantReddot()
    if self._merchantIdList then
        local data2 = Module.ReddotTrie:GetReddotData(EReddotTrieObserverType.Shop, "DoRaffle")
        for idx, id in ipairs(self._merchantIdList) do
            local key = string.format("NewUnlock_Merchant%s", id)
            local data = Module.ReddotTrie:GetReddotData(EReddotTrieObserverType.Shop, key)
            if data and data:GetReddotFlag() then
                self._wtShopChooseMerchantScrollBox:ScrollToItem(idx - 1, true, true, 10, 0, false)
                return
            end
        end
        if data2 and data2:GetReddotFlag() then
            self._wtShopChooseMerchantScrollBox:ScrollToItem(Module.Shop.Config.ResearchMerchantId - 1, true, true, 10, 0, false)
        end
    end
end

function ShopChooseDepartmentView:OnTimeChangeFresh()
    self:_FreshMerchantDataList()
end

function ShopChooseDepartmentView:OnMerchantDataFresh()
    self:_FreshMerchantDataList()
end

function ShopChooseDepartmentView:_OnLockedQuestLineOpen(questLineInfos)
    self:_UpdateTaskChapter()
    self._wtChapterScrollBox:RefreshVisibleItems()
end

function ShopChooseDepartmentView:_FreshMerchantDataList()
    self._merchantIdList = Server.ShopServer:GetMerchantIdList()
    self.bHideProcess = false
    self._wtShopChooseMerchantScrollBox:RefreshVisibleItems()
end

function ShopChooseDepartmentView:_OpenQusetOpenPanel()
    local newlyOpenLineInfos = Module.Quest:PopNewlyOpenQuestLine()
    if newlyOpenLineInfos and #newlyOpenLineInfos > 0 then
        for _, newlyOpenQuestLine in ipairs(newlyOpenLineInfos) do
            local title = Module.Quest.Config.Loc.showQuestLineSucc
            local nameBgStyle = 0
            if newlyOpenQuestLine.bUnopenShow then
                title = Module.Quest.Config.Loc.openQuestLineSucc
                nameBgStyle = 1
            end
            Module.Reward:OpenSyetemOpenPanel(
                title,
                newlyOpenQuestLine.lineName,
                newlyOpenQuestLine.lineCover,
                CreateCallBack(self._OpenQusetOpenPanel, self),
                newlyOpenQuestLine
            )
        end
        Module.Quest:ResetNewlyOpenQuestLine()
    end
end

-- 新手引导期间对_wtNormalScrollBox做特殊处理，让其只能在指定区域滑动
function ShopChooseDepartmentView:OnScrollBoxScrolled()
    if self._curSelectTab == EDepartmentType.Quest then
        if Module.Guide:IsNewPlayerGuiding() then
            self._wtChapterScrollBox:SetScrollOffset(self.oriOffset)
        else
            self._wtChapterScrollBox:SetAllowOverscroll(true)
            self._wtChapterScrollBox:Event("OnUserScrolled", nil, nil)
        end
    end
end

return ShopChooseDepartmentView
