----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGuide)
----- LOG FUNCTION AUTO GENERATE END -----------



local InputSummaryItemHD = require "DFM.Business.Module.CommonBarModule.UI.BottomBarHD.InputSummaryItemHD"
local GuideConfig        = require "DFM.Business.Module.GuideModule.GuideConfig"
local WidgetUtil         = require "DFM.YxFramework.Util.WidgetUtil"
local UGPInputHelper     = import("GPInputHelper")
local GuideLogic         = require "DFM.Business.Module.GuideModule.GuideLogic"

---@class GuideHDWeakClickUI : LuaUIBaseView
local GuideHDWeakClickUI = ui("GuideHDWeakClickUI")

local function log(...)
    loginfo("[GuideHDWeakClickUI]", ...)
end

local function warning(...)
    logwarning("[GuideHDWeakClickUI]", ...)
end

function GuideHDWeakClickUI:Ctor()
    self._rootParent = self:Wnd("CanvasPanel_0", UIWidgetBase)
    self._wtWeakClickEffectPanel = self:Wnd("_wtWeakClickEffectPanel", UIWidgetBase)
    ---@type GuideHDClickBtnUI
    self._wtClickItem = self:Wnd("_wtClickBtnBig", UIWidgetBase)
    ---@type GuideHDClickBtnUI[]
    self._btnList = {}
    self._btnList[1] = self._wtClickItem
    self:Reset()
end

--==================================================
--region Life function
function GuideHDWeakClickUI:Reset()
    self._showData = nil
    self._bSelfActive = false
    self._callbackOnEnd = nil
    self._isFirstFrame = true
    self._bUsing = false
    self._wtClickItem:SetRenderOpacity(0)
    self:_ResetTargetUI()

    if self._inputTypeChangedHandle then
        UE.GPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._inputTypeChangedHandle)
        self._inputTypeChangedHandle = nil
    end


end

function GuideHDWeakClickUI:RecycleSelf()
    self:Hide()
    if self._bSelfActive then
        self._bSelfActive = false

        -- 如果没能正常归还目标ui，则直接放弃这个弱引导界面的复用，等待其他回收时机
        if self._bUsing then
            warning("_ResetTargetUI fail, targetWidget:", self._targetWidget, self:_IsValidWidget(self._targetWidget),
                "targetParent:", self._targetOldParent, self:_IsValidWidget(self._targetOldParent))
            Facade.UIManager:CloseUI(self)
        else
            Module.Guide.Field:ReturnCacheSubUI(UIName2ID.GuideHDWeakClickUI, self)
        end
    end
end

-- 解除对widget的抓取
function GuideHDWeakClickUI:_ResetTargetUI()
    if self:_IsValidWidget(self._targetWidget) then
        if self:_IsValidWidget(self._targetOldParent) then
            -- 都有效，开始归还widget
            self._targetWidget:RemoveFromParent()
            self._wtWeakClickEffectPanel:RemoveFromParent()
            self._targetOldParent:AddChild(self._targetWidget)
            if self._targetWidget.Slot.SetZorder then
                self._targetWidget.Slot:SetZorder(self._oldZorder)
            end
            if self._targetWidget.Slot.SetSize then
                if self._slotSize then
                    -- which was a cpp val
                    self._targetWidget.Slot:SetSize(self._slotSize)
                else
                    self._targetWidget.Slot:SetSize(self._localSize)
                end
            end
            if self._targetWidget.Slot.SetPosition then
                self._targetWidget.Slot:SetPosition(self._localPos)
            end
            self._targetWidget:SetVisibility(self._ordVisibility)

            self._rootParent:AddChild(self._wtWeakClickEffectPanel)

            self._bUsing = false
        end
    end
    self._targetWidget = nil
    self._targetOldParent = nil
    self._oldZorder = 0
    self._localSize = nil
    self._localPos = nil
    self._ordVisibility = nil
end

function GuideHDWeakClickUI:_IsValidWidget(widget)
    if not widget or not isvalid(widget) then return false end
    if type(widget) == "table" and widget.__cppinst and not slua.isValid(widget.__cppinst) then
        return false
    end
    if hasdestroy(widget) then
        return false
    end
    return true
end

function GuideHDWeakClickUI:OnOpen()
end

function GuideHDWeakClickUI:OnClose()
    self._bClose = true
    self:_RemoveTickTimer()
end

function GuideHDWeakClickUI:SetData(showData)
    if not showData then return end
    ---@type GuideHDWeakClickUI_ShowData
    self._showData = showData
    self._callbackOnEnd = showData.callbackOnEnd
end

function GuideHDWeakClickUI:OnShowBegin()
    self._bSelfActive = true
    self:_UpdateShow()
    self:_AddTickTimer()
    self:_PlayAudio()

    if not self._inputTypeChangedHandle then
        self._inputTypeChangedHandle = UE.GPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(
            CreateCPlusCallBack(self._OnInputTypeChanged, self))
    end

    self:_OnInputTypeChanged()
end

function GuideHDWeakClickUI:_OnInputTypeChanged()
    log("_OnInputTypeChanged", WidgetUtil.GetCurrentInputType())

    if not IsHD() then return end

    if self._showData then
        local clickCfg = Module.Guide.Config.TableGuideClickConfig[self._showData.clickID]
        if clickCfg then
            self._showData.tipsText = GuideLogic.GetRealClickTipsText(clickCfg)
        end
    end

    self:_UpdateShow()


    if WidgetUtil.IsGamepad() then
        -- Q: WG需要强制聚焦? 
        -- A: GuideMainUI.navGroup 采用 Grid1D 的方式，不能移动到别的navGroup，采用Hittest呢?
        -- A2: 手柄上 FG HDClick 只聚焦不遮罩不设置InputGate, WeakClick（不聚焦也不遮罩也不启用InputGate)
        -- if not hasdestroy(self._wtClickItem) then
        --     Module.Guide.Config.EGuideEvent.evtGuideNavChanged:Invoke("add", "GuideHDWeakClickUI", {
        --         self._wtClickItem
        --     })
        -- end
    else
        -- Module.Guide.Config.EGuideEvent.evtGuideNavChanged:Invoke("remove", "GuideHDWeakClickUI")
    end
end

function GuideHDWeakClickUI:OnHide()
    Module.Guide.Config.EGuideEvent.evtGuideNavChanged:Invoke("remove", "GuideHDWeakClickUI")
    -- local mainui =  Module.Guide.Field:GetMainUI()

    if self._inputTypeChangedHandle then
        UE.GPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._inputTypeChangedHandle)
        self._inputTypeChangedHandle = nil
    end

    self:_PauseTickTimer()
    self:RemoveAllLuaEvent()
    self:_ResetTargetUI()
end

function GuideHDWeakClickUI:OnInitExtraData(showData)
    self:SetData(showData)
end

function GuideHDWeakClickUI:_UpdateShow()
    if not self._showData then return end
    self:_UpdateClickItemPosAndSize()

    local bShowClickText = self._showData.tipsText and self._showData.tipsText ~= ""

    self._wtClickItem:SetRenderOpacity(1)
    self._wtClickItem:IconVisible(ESlateVisibility.SelfHitTestInvisible) -- depends on bShowClickText


    -- if self._showData.tipsTextPolicy == 11 then
    --     self._wtClickItem:IconVisible(ESlateVisibility.Collapsed)
    -- end
    local bHDNoDefaultIcon = (self._showData.options & GuideConfig.EGuideClickConfigOption.HDNoDefaultIcon) ~= 0
    if
        bHDNoDefaultIcon or
        WidgetUtil.IsGamepad() -- 手柄不显示鼠标Icon, 采用富文本
    then
        self._wtClickItem:IconVisible(ESlateVisibility.Collapsed)
    else
        self._wtClickItem:IconVisible(ESlateVisibility.HitTestInvisible)
    end


    self._wtClickItem:TextVisible(bShowClickText)
    self._wtClickItem:SetTipsText(self._showData.tipsText)
end

function GuideHDWeakClickUI:_UpdateClickItemPosAndSize()
    if not self._bSelfActive then return end
    if not self._showData then return end
    if self._isFirstFrame then
        self._isFirstFrame = false
        return
    end
    -- if self._bUsing then return end

    if self._bUsing then
        -- 有辅助计算尺寸的ui的话，按照辅助计算的刷新位置和尺寸
        if self._showData.uiForCalSizeAndPosData then
            local curBtn = self._btnList[1]
            local data = self._showData.uiForCalSizeAndPosData
            -- 记录目标节点相关数据
            local targetWidget = data.targetWidget
            if not GuideLogic.IsValidWidget(targetWidget) then
                warning("_UpdateClickItemPosAndSize targetWidget has been destroyed, skip")
                return
            end

            -- 实际目标ui的全局尺寸和位置
            local globalPos, globalSize = GuideLogic.GetGlobalPosAndSizeByWidget(targetWidget)

            -- 计算最新的本地坐标和尺寸
            local localPos, localSize = GuideLogic.GetLocalPosAndSize(curBtn:GetParent(), globalPos, globalSize)
            local offsetLeft, offsetRight, offsetTop, offsetBottom = 0, 0, 0, 0
            if data.uiOffset then
                offsetTop = data.uiOffset[1] or 0
                offsetBottom = data.uiOffset[2] or 0
                offsetLeft = data.uiOffset[3] or 0
                offsetRight = data.uiOffset[4] or 0
            end
            local triangleOffsetX, triangleOffsetY = 0, 0
            if data.triangleUIOffset then
                triangleOffsetX = data.triangleUIOffset[1] or 0
                triangleOffsetY = data.triangleUIOffset[2] or 0
            end
            local clickUILocalPos, clickUILocalSize = FVector2D(0, 0), FVector2D(0, 0)
            clickUILocalPos.X = localPos.X - offsetLeft + triangleOffsetX
            clickUILocalPos.Y = localPos.Y - offsetTop + triangleOffsetY
            clickUILocalSize.X = localSize.X + offsetLeft + offsetRight
            clickUILocalSize.Y = localSize.Y + offsetTop + offsetBottom

            -- 设置高亮节点的数据
            curBtn:SetPos(clickUILocalPos + FVector2D(clickUILocalSize.X * 0.5, clickUILocalSize.Y * 0.5))
            curBtn:SetSize(clickUILocalSize)
            curBtn:SetScale(data.triangleScale)
            curBtn:Set_Multiple()

            -- logerror("GuideWeakClickBigUI:_UpdateClickItemPosAndSize tick for cal size", clickUILocalPos.X, clickUILocalPos.Y, clickUILocalSize.X, clickUILocalSize.Y)
        else
            return
        end
    else
        local clipDataList = self._showData.clipDataList
        for idx, data in ipairs(clipDataList) do
            -- 先只处理一个
            if idx > 1 then
                return
            end

            local curBtn = self._btnList[idx]

            -- 记录目标节点相关数据
            local targetWidget = data.targetWidget
            if not self:_IsValidWidget(targetWidget) then
                warning("_UpdateClickItemPosAndSize targetWidget has been destroyed, skip")
                return
            end

            local targetParent = targetWidget:GetParent()
            self._targetWidget = targetWidget
            self._targetOldParent = targetParent
            self._ordVisibility = targetWidget.Visibility
            if targetWidget.Slot.GetZorder then
                self._oldZorder = targetWidget.Slot:GetZorder()
            end

            self._slotSize = nil

            -- Horizontal box slot
            if targetWidget.Slot.Padding ~= nil and targetWidget.Slot.SetVerticalAlignment ~= nil and targetWidget.Slot.SetHorizontalAlignment ~= nil then
                ---@class FSlateChildSize
                self._slotSize = targetWidget.Slot.Size
                -- Canvas panel slot
            elseif targetWidget.Slot.bAutoSize ~= nil and targetWidget.Slot.SetOffsets ~= nil then
                -- do nothing now
            end


            local alignWidgetGeometry = targetWidget:GetCachedGeometry()
            self._localPos = FVector2D(alignWidgetGeometry.Position.X, alignWidgetGeometry.Position.Y)
            self._localSize = FVector2D(alignWidgetGeometry.Size.X, alignWidgetGeometry.Size.Y)

            -- 实际目标ui的全局尺寸和位置
            local globalPos, globalSize = GuideLogic.GetGlobalPosAndSizeByWidget(targetWidget)
            self._globalSize = globalSize
            self._globalPos = globalPos

            -- 切换各自父节点
            self._targetWidget:RemoveFromParent()
            self._wtWeakClickEffectPanel:AddChild(self._targetWidget)
            self._wtWeakClickEffectPanel:RemoveFromParent()
            targetParent:AddChild(self._wtWeakClickEffectPanel)
            if self._wtWeakClickEffectPanel.Slot.SetZorder then
                self._wtWeakClickEffectPanel.Slot:SetZorder(self._oldZorder)
            end
            self._bUsing = true

            -- 计算最新的本地坐标和尺寸
            local localPos, localSize = GuideLogic.GetLocalPosAndSize(self._targetOldParent, globalPos, globalSize)
            local offsetLeft, offsetRight, offsetTop, offsetBottom = 0, 0, 0, 0
            if data.uiOffset then
                offsetTop = data.uiOffset[1] or 0
                offsetBottom = data.uiOffset[2] or 0
                offsetLeft = data.uiOffset[3] or 0
                offsetRight = data.uiOffset[4] or 0
            end
            local triangleOffsetX, triangleOffsetY = 0, 0
            if data.triangleUIOffset then
                triangleOffsetX = data.triangleUIOffset[1] or 0
                triangleOffsetY = data.triangleUIOffset[2] or 0
            end
            local clickUILocalPos, clickUILocalSize = FVector2D(0, 0), FVector2D(0, 0)
            clickUILocalPos.X = localPos.X - offsetLeft + triangleOffsetX
            clickUILocalPos.Y = localPos.Y - offsetTop + triangleOffsetY
            clickUILocalSize.X = localSize.X + offsetLeft + offsetRight
            clickUILocalSize.Y = localSize.Y + offsetTop + offsetBottom

            -- 设置目标节点的最新位置
            self._targetWidget.Slot:SetSize(localSize)
            self._targetWidget.Slot:SetPosition(localPos)

            -- 设置高亮节点的数据
            curBtn:SetPos(clickUILocalPos + FVector2D(clickUILocalSize.X * 0.5, clickUILocalSize.Y * 0.5))
            curBtn:SetSize(clickUILocalSize)
            curBtn:SetScale(data.triangleScale)
            curBtn:SetUIType(data.uiType)
            curBtn:Set_Multiple()
            curBtn:SetRenderOpacity(1)

            -- 设置下层级
            targetWidget.Slot:SetZorder(0)
            curBtn.Slot:SetZorder(10)

            curBtn:PlayInAnim()
        end
    end
end

function GuideHDWeakClickUI:_AddTickTimer()
    log("_AddTickTimer", self._checkTimerHandle)
    if self._checkTimerHandle then
        self._checkTimerHandle:Start()
        return
    end
    self._checkTimerHandle = Timer:NewIns(0.01, 0)
    self._checkTimerHandle:AddListener(self._UpdateClickItemPosAndSize, self)
    self._checkTimerHandle:Start()
end

function GuideHDWeakClickUI:_PauseTickTimer()
    log("_PauseTickTimer", self._checkTimerHandle)
    if self._checkTimerHandle then
        self._checkTimerHandle:Stop()
    end
end

function GuideHDWeakClickUI:_RemoveTickTimer()
    log("_RemoveTickTimer", self._checkTimerHandle)
    if self._checkTimerHandle then
        self._checkTimerHandle:Stop()
        self._checkTimerHandle:Release()
        self._checkTimerHandle = nil
    end
end

-- TODO 未来如果接入消失动画的话，消失动画结束再调用这个
function GuideHDWeakClickUI:_OnRealClose()
    if self._callbackOnEnd then
        self:_callbackOnEnd()
        return
    end

    self:RecycleSelf()
end

function GuideHDWeakClickUI:_PlayAudio()
    if not self._showData then return end
    local audioEvent = self._showData.tipsAudio
    if audioEvent then
        Facade.SoundManager:PlayGuideAudio(audioEvent)
        self._curAudio = audioEvent
    end
end

return GuideHDWeakClickUI
