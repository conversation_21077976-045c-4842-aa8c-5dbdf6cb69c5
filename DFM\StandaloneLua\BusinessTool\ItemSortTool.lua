---------------------------------------------------------------------------------
--- 此处放置Server、Module都可能用得到的道具排序方法
---------------------------------------------------------------------------------
local ItemSortTool = {}
local ItemHelperTool = require"DFM.StandaloneLua.BusinessTool.ItemHelperTool"

local bUseCacheToSpeedUp = false
local cacheItemTotalSellPrice = {}

--- 道具排序方法
---@param a ItemBase
---@param b ItemBase
ItemSortTool.SortItemDefault = function(a, b)
    local aIsEquipped, bIsEquipped = a:IsEquipped(), b:IsEquipped()
    if aIsEquipped and not bIsEquipped then
        return true
    elseif not aIsEquipped and bIsEquipped then
        return false
    end

    if a.size ~= b.size then
        return a.size > b.size
    end
    if a.itemMainType ~= b.itemMainType then
        return a.itemMainType < b.itemMainType
    end
    if a.quality ~= b.quality then
        return a.quality > b.quality
    end

    return a.instanceId < b.instanceId
end

ItemSortTool.EnableUseCacheToSpeedUp = function(bUse)
    bUseCacheToSpeedUp = bUse
    if not bUse then
        cacheItemTotalSellPrice = {}
    end
end

--- 通用道具排序规则
--- 1. 类型
--- 2. 品阶
--- 3. 出售价格
--- 4. 占格大小
--- 5. 剩余耐久度
--- 6. 绑定非绑定
--- 7. GID（默认）
---@param a ItemBase
---@param b ItemBase
ItemSortTool.CommonSortItem = function(a, b)
    -- 1. 类型
    local aPriority = a.priority
    local bPriority = b.priority
    if aPriority ~= bPriority then
        return aPriority > bPriority
    end

    -- 2. Quality
    if a.quality ~= b.quality then
        return a.quality > b.quality
    end

    -- 3. Sell Price
    local bReverse = false
    local aPrice, bPrice
    if bUseCacheToSpeedUp then
        aPrice = cacheItemTotalSellPrice[a] or a:GetTotalSellPrice()
        bPrice = cacheItemTotalSellPrice[b] or b:GetTotalSellPrice()
        if not cacheItemTotalSellPrice[a] then
            cacheItemTotalSellPrice[a] = aPrice
        end
        if not cacheItemTotalSellPrice[b] then
            cacheItemTotalSellPrice[b] = bPrice
        end
    else
        aPrice = a:GetTotalSellPrice()
        bPrice = b:GetTotalSellPrice()
    end

    if aPrice ~= bPrice then
        if bReverse then
            return aPrice < bPrice
        else
            return aPrice > bPrice
        end
    end

    -- 4. Size
    local aSize = a.length * a.width
    local bSize = b.length * b.width
    if aSize ~= bSize then
        return aSize > bSize
    end
    local curDurability = 0
    local curDurability1 = 0
    local itemFeature = a:GetFeature()
    local itemFeature1 = b:GetFeature()

    if itemFeature and itemFeature.GetDurabilityCurValue then
        curDurability = itemFeature:GetDurabilityCurValue()
    end
    if itemFeature1 and itemFeature1.GetDurabilityCurValue then
        curDurability1 = itemFeature1:GetDurabilityCurValue()
    end
    -- 5. Durability
    if curDurability ~= curDurability1 then
        return curDurability > curDurability1
    end

    -- 6. Binding
    local aIsBind = a:CheckIsBind()
    local bIsBind = b:CheckIsBind()
    if aIsBind ~= bIsBind then
        if aIsBind then
            return true
        else
            return false 
        end
    end

    -- 7. Other
    return a.instanceId < b.instanceId
end

function ItemSortTool.GetSizeFirstSortFunction()
    local CustomSortFunc = ItemSortTool.CommonSortItem

    local function f(a, b)
        if a.size ~= b.size then
            return a.size > b.size
        else
            local Result = CustomSortFunc(a, b)
            if Result ~= nil then
                return Result
            else
                return ItemSortTool.SortItemDefault(a, b)
            end
        end
    end

    return f
end

function ItemSortTool.GetSizeOnlySortFunction()
    local function f(a, b)
        if a.size ~= b.size then
            return a.size > b.size
        end
        return a.instanceId < b.instanceId
    end

    return f
end

-- 仓库左侧界面专用的排序算法
function ItemSortTool.GetWHSortFunction()
    ---@param a ItemBase
    ---@param b ItemBase
    local function f(a, b)
        if a.size ~= b.size then
            return a.size > b.size
        end
        if a.length ~= b.length then
            return a.length < b.length
        end
        if a.id ~= b.id then
            return a.id < b.id
        end
        return a.instanceId < b.instanceId
    end

    return f
end

---@desc 保持与DS一致的排序策略（见PakingItem::operator<）
function ItemSortTool.GetLootingSortFunction()
    ---@param a ItemBase
    ---@param b ItemBase
    local function f(a, b)
        -- 1. 大小（大的优先）
        local aSize = a.length * a.width
        local bSize = b.length * b.width
        if aSize ~= bSize then
            return aSize > bSize
        end
        -- 一样大的情况下长的优先（优先让物品横着摆放增大容积率）
        if a.length ~= b.length then
            return a.length > b.length
        end

        -- 2. 售价（高的优先）
        local aPrice, bPrice
        if bUseCacheToSpeedUp then
            aPrice = cacheItemTotalSellPrice[a] or a:GetTotalSellPrice()
            bPrice = cacheItemTotalSellPrice[b] or b:GetTotalSellPrice()
            if not cacheItemTotalSellPrice[a] then
                cacheItemTotalSellPrice[a] = aPrice
            end
            if not cacheItemTotalSellPrice[b] then
                cacheItemTotalSellPrice[b] = bPrice
            end
        else
            aPrice = a:GetTotalSellPrice()
            bPrice = b:GetTotalSellPrice()
        end

        if aPrice ~= bPrice then
            return aPrice > bPrice
        end

        -- 3. 类型（小的优先）
        local aMainType = ItemHelperTool.GetMainTypeById(a.id)
        local bMainType = ItemHelperTool.GetMainTypeById(b.id)

        if aMainType ~= bMainType then
            return aMainType < bMainType
        end

        -- 4. 品质（高的优先）
        if a.quality ~= b.quality then
            return a.quality > b.quality
        end

        -- 5. GID（保证Stable）
        return a.instanceId < b.instanceId
    end
    return f
end

function ItemSortTool.GetStrictSortFunction()
    local CustomSortFunc = ItemSortTool.CommonSortItem

    local function f(a, b)
        if a.size ~= b.size then
            return a.size > b.size
        else
            local Result = CustomSortFunc(a, b)
            if Result ~= nil then
                return Result
            else
                return ItemSortTool.SortItemDefault(a, b)
            end
        end
    end

    return f
end

function ItemSortTool.GetWeakSortFunction()
    local CustomSortFunc = ItemSortTool.CommonSortItem

    local function f(a, b)
        local Result = CustomSortFunc(a, b)
        if Result ~= nil then
            return Result
        else
            return ItemSortTool.SortItemDefault(a, b)
        end
    end

    return f
end


--- 先排类型，再排价值
---@param LhsCommonData CommonDataInfo
---@param RhsCommonData CommonDataInfo
ItemSortTool.SortSelectionCommonDatas = function(LhsCommonData, RhsCommonData)
    if LhsCommonData.bSample ~= RhsCommonData.bSample then
        return LhsCommonData.bSample == true
    end
    if LhsCommonData.type ~= RhsCommonData.type then
        return LhsCommonData.type < RhsCommonData.type
    end

    if LhsCommonData:GetIsDeposData() and RhsCommonData:GetIsDeposData() then
        --- 道具解锁逻辑优先
        local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
        if armedForceMode == EArmedForceMode.MP then
            local bLhsLocked = LhsCommonData:CheckIsLocked()
            local bRhsLocked = RhsCommonData:CheckIsLocked()
            if bLhsLocked ~= bRhsLocked then
                return bLhsLocked == false
            end

            local lhsLockedInfo = Server.InventoryServer:GetMPWeaponLockedInfoById(LhsCommonData.itemId)
            local rhsLockedInfo = Server.InventoryServer:GetMPWeaponLockedInfoById(RhsCommonData.itemId)
            if lhsLockedInfo.level and rhsLockedInfo.level and lhsLockedInfo.level ~= rhsLockedInfo.level then
                return lhsLockedInfo.level < rhsLockedInfo.level
            end
        end

        --- 正常仓库排序
        return ItemSortTool.SortSelectionDeposItem(LhsCommonData:GetRepresentativeItem(), RhsCommonData:GetRepresentativeItem())
    else
        --- 正常商店排序
        return ItemSortTool.SortSelectionShopSItem(LhsCommonData.shopItem, RhsCommonData.shopItem)
    end
    return false
end

--- 先排类型，再排价值
---@param LhsCommonData AssemblySelectionDataStruct
---@param RhsCommonData AssemblySelectionDataStruct
ItemSortTool.SortAssemblySelectionDatas = function(LhsCommonData, RhsCommonData)
    if LhsCommonData.type ~= RhsCommonData.type then
        return LhsCommonData.type < RhsCommonData.type
    end

    if LhsCommonData:GetIsDeposData() and RhsCommonData:GetIsDeposData() then
        --- 正常仓库排序
        return ItemSortTool.SortSelectionDeposItem(LhsCommonData:GetRepresentativeItem(), RhsCommonData:GetRepresentativeItem())
    else
        --- 正常商店排序
        return ItemSortTool.SortSelectionGoodsItem(LhsCommonData, RhsCommonData)
    end
    return false
end

--- 先排类型，再排价值
---@param LhsCommonData CommonDataInfo
---@param RhsCommonData CommonDataInfo
ItemSortTool.SortSelectionGunDatas = function(LhsCommonData, RhsCommonData)
    if LhsCommonData.bSample ~= RhsCommonData.bSample then
        return LhsCommonData.bSample == true
    end
    if LhsCommonData.type ~= RhsCommonData.type then
        return LhsCommonData.type < RhsCommonData.type
    end
    if LhsCommonData:GetIsDeposData() and RhsCommonData:GetIsDeposData() then
        return ItemSortTool.SortSelectionDeposItem(LhsCommonData:GetRepresentativeItem(), RhsCommonData:GetRepresentativeItem(), true)
    elseif LhsCommonData:GetIsShopData() and RhsCommonData:GetIsShopData() then
        return ItemSortTool.SortSelectionShopSItem(LhsCommonData:GetSItem(), RhsCommonData:GetSItem())
    elseif LhsCommonData:GetIsBuleprintData() and RhsCommonData:GetIsBuleprintData() then
        return ItemSortTool.SortSelectionBulePrintSItem(LhsCommonData:GetBItem(), RhsCommonData:GetBItem())
    end
    return false
end

--- 先排类型，再排价值
---@param LhsCommonData AssemblySelectionDataStruct
---@param RhsCommonData AssemblySelectionDataStruct
ItemSortTool.SortAssemblySelectionGunDatas = function(LhsCommonData, RhsCommonData)
    if LhsCommonData.type ~= RhsCommonData.type then
        return LhsCommonData.type < RhsCommonData.type
    end

    if LhsCommonData:GetIsDeposData() and RhsCommonData:GetIsDeposData() then
        --- 正常仓库排序
        return ItemSortTool.SortSelectionDeposItem(LhsCommonData:GetRepresentativeItem(), RhsCommonData:GetRepresentativeItem(), true)
    else
        --- 正常商店排序
        return ItemSortTool.SortSelectionGoodsItem(LhsCommonData, RhsCommonData)
    end
    return false
end

--- 先排类型，再排价值
---@param Lhs ItemBase
---@param Rhs ItemBase
ItemSortTool.SortSelectionDeposItem = function(Lhs, Rhs, isGun)
    local EWeaponItemTypeSort = {
        [ItemConfig.EWeaponItemType.Rifle] = 1,
        [ItemConfig.EWeaponItemType.Submachine] = 2,
        [ItemConfig.EWeaponItemType.Sniper] = 3,
        [ItemConfig.EWeaponItemType.Shotgun] = 4
    }
    isGun = setdefault(isGun, false)

    --第一优先级：是否装备
    local bLhsInUsed = Lhs:IsAdapterInGun() or (Lhs.InSlot == nil or Lhs.InSlot:IsEquipSlot())
    local bRhsInUsed = Rhs:IsAdapterInGun() or (Rhs.InSlot == nil or Rhs.InSlot:IsEquipSlot())
    if bLhsInUsed ~= bRhsInUsed then
        if bLhsInUsed then
            return true
        else
            return false
        end
    end

    local weaponFeature = Lhs:GetFeature(EFeatureType.Weapon)
    if weaponFeature then
        local bLhsFitArm = Module.ArmedForce:CheckWeaponItemIsFitCurrentArm(Lhs)
        local bRhsFitArm = Module.ArmedForce:CheckWeaponItemIsFitCurrentArm(Rhs)
        if bLhsFitArm ~= bRhsFitArm then
            if bLhsFitArm then
                return true
            else
                return false
            end
        end
    end
        
    --第二优先级：枪系
    local isTrueWeapon = ItemSortTool.IsTrueWeapon(Lhs)
    local isTrueWeapon1 = ItemSortTool.IsTrueWeapon(Rhs)
    if isTrueWeapon and isTrueWeapon1 then
        if ItemSortTool.GetGunClassOrder(Lhs) ~= ItemSortTool.GetGunClassOrder(Rhs) then
            if isGun then -- 按照枪械设定排序
                local LhsSortType = EWeaponItemTypeSort[ItemSortTool.GetGunClassOrder(Lhs)]
                local RhsSortType = EWeaponItemTypeSort[ItemSortTool.GetGunClassOrder(Rhs)]
                if LhsSortType and RhsSortType then
                    return LhsSortType < RhsSortType
                elseif LhsSortType and not RhsSortType then
                    return true
                elseif not LhsSortType and RhsSortType then
                    return false
                else
                    return ItemSortTool.GetGunClassOrder(Lhs) < ItemSortTool.GetGunClassOrder(Rhs)
                end
            else
                return ItemSortTool.GetGunClassOrder(Lhs) < ItemSortTool.GetGunClassOrder(Rhs)
            end
        end
    end

    local bAscending = true
    --第三优先级：品质
    if Lhs.quality ~= Rhs.quality then
        if bAscending then
            return Lhs.quality < Rhs.quality
        else
            return Lhs.quality > Rhs.quality
        end
    end

    --第四优先级：道具优先级
    local aPriority = Lhs.priority
    local bPriority = Rhs.priority
    if aPriority ~= bPriority then
        return aPriority > bPriority
    end 

    --第五优先级：价值(机匣即可，不然价值随着配件变化的话，跳转到改枪台操作配件回影响返回后的排序，返回后选中记录的idx会出问题)
    local aPrice = Server.ShopServer:GetShopDynamicGuidePrice(Lhs.id) --Lhs:GetSingleSellPrice()
    local bPrice = Server.ShopServer:GetShopDynamicGuidePrice(Rhs.id) --Rhs:GetSingleSellPrice()
    if aPrice ~= bPrice then
        if bAscending then
            return aPrice < bPrice
        else
            return aPrice > bPrice
        end
    end

    -- 第六优先级：Binding
    local aIsBind = Lhs:CheckIsBind()
    local bIsBind = Rhs:CheckIsBind()
    if aIsBind ~= bIsBind then
        if aIsBind then
            return true
        else
            return false 
        end
    end

    --第七优先级：Gid
    if Lhs.instanceId ~= Rhs.instanceId then
        return Lhs.instanceId < Rhs.instanceId
    end

    return false
end

--- 先排类型，再排价值
---@param LhsSItem ItemBase
---@param RhsSItem ItemBase
ItemSortTool.SortSelectionBulePrintSItem = function(Lhs, Rhs)
    --第一优先级：是否解锁可购买,等级大小
    local LhsGainedTime = Server.CollectionServer:GetCollectionPropById(Lhs.id).gained_time or 0
    local RhsGainedTime = Server.CollectionServer:GetCollectionPropById(Rhs.id).gained_time or 0

    if LhsGainedTime ~= RhsGainedTime then
        if LhsGainedTime < RhsGainedTime then
            return true
        else
            return false
        end
    else
        return Lhs.id < Rhs.id
    end
    return false
end

--- 先排类型，再排价值
---@param LhsSItem ShopItem
---@param RhsSItem ShopItem
ItemSortTool.SortSelectionShopSItem = function(LhsSItem, RhsSItem)
    local Lhs = LhsSItem.item
    local Rhs = RhsSItem.item
    --第一优先级：是否解锁可购买,等级大小
    local bLhsCanBuy = LhsSItem:GetIsUnlock() and true or false
    local bRhsCanBuy = RhsSItem:GetIsUnlock() and true or false
    if bLhsCanBuy ~= bRhsCanBuy then
        if bLhsCanBuy then
            return true
        else
            return false
        end
    end

    local weaponFeature = Lhs:GetFeature(EFeatureType.Weapon)
    if weaponFeature then
        local bLhsFitArm = Module.ArmedForce:CheckWeaponItemIsFitCurrentArm(Lhs)
        local bRhsFitArm = Module.ArmedForce:CheckWeaponItemIsFitCurrentArm(Rhs)
        if bLhsFitArm ~= bRhsFitArm then
            if bLhsFitArm then
                return true
            else
                return false
            end
        end
    end
    
    local UnlockLvLhs = LhsSItem:GetSafeHouseUnlockInfo().unlockLv
    local UnlockLvRhs = RhsSItem:GetSafeHouseUnlockInfo().unlockLv
    if UnlockLvLhs and UnlockLvRhs and UnlockLvLhs ~= UnlockLvRhs then
        return UnlockLvLhs < UnlockLvRhs
    end

    --第二优先级：枪系
    local isTrueWeapon = ItemSortTool.IsTrueWeapon(Lhs)
    local isTrueWeapon1 = ItemSortTool.IsTrueWeapon(Rhs)
    if isTrueWeapon and isTrueWeapon1 then
        if ItemSortTool.GetGunClassOrder(Lhs) ~= ItemSortTool.GetGunClassOrder(Rhs) then
            return ItemSortTool.GetGunClassOrder(Lhs) < ItemSortTool.GetGunClassOrder(Rhs)
        end
    end

    local bAscending = true
    --第三优先级：品质
    if Lhs.quality ~= Rhs.quality then
        if bAscending then
            return Lhs.quality < Rhs.quality
        else
            return Lhs.quality > Rhs.quality
        end
    end

    --第四优先级：道具优先级
    local aPriority = Lhs.priority
    local bPriority = Rhs.priority
    if aPriority ~= bPriority then
        return aPriority > bPriority
    end 

    --第五优先级：价值
    local aPrice = Lhs:GetSingleSellPrice()
    local bPrice = Rhs:GetSingleSellPrice()
    if aPrice ~= bPrice then
        if bAscending then
            return aPrice < bPrice
        else
            return aPrice > bPrice
        end
    end

    --第六优先级：Order
    if LhsSItem.order ~= RhsSItem.order then
        return LhsSItem.order < RhsSItem.order
    end

    --第七优先级：Gid
    if Lhs.instanceId ~= Rhs.instanceId then
        return Lhs.instanceId < Rhs.instanceId
    end

    return false
end

--- 先排类型，再排价值
---@param LhsCommonData MergeDataStruct
---@param RhsCommonData MergeDataStruct
ItemSortTool.SortSelectionGoodsItem = function(LhsCommonData, RhsCommonData)
    local LhsSItem = LhsCommonData:GetSItem()
    local LhsSaleInfo = LhsCommonData:GetSaleInfo()
    local RhsSItem = RhsCommonData:GetSItem()
    local RhsSaleInfo = RhsCommonData:GetSaleInfo()
    
    local Lhs = LhsCommonData:GetRepresentativeItem()
    local Rhs = RhsCommonData:GetRepresentativeItem()

    --第一优先级：是否解锁可购买,等级大小
    local bLhsCanBuy = ((LhsSItem and LhsSItem:GetIsUnlock()) or Module.ModuleUnlock:IsModuleUnlock(SwitchModuleID.ModuleAuctionBuy)) and true or false
    local bRhsCanBuy = ((RhsSItem and RhsSItem:GetIsUnlock()) or Module.ModuleUnlock:IsModuleUnlock(SwitchModuleID.ModuleAuctionBuy)) and true or false
    if bLhsCanBuy ~= bRhsCanBuy then
        if bLhsCanBuy then
            return true
        else
            return false
        end
    end

    local weaponFeature = Lhs:GetFeature(EFeatureType.Weapon)
    if weaponFeature then
        local bLhsFitArm = Module.ArmedForce:CheckWeaponItemIsFitCurrentArm(Lhs)
        local bRhsFitArm = Module.ArmedForce:CheckWeaponItemIsFitCurrentArm(Rhs)
        if bLhsFitArm ~= bRhsFitArm then
            if bLhsFitArm then
                return true
            else
                return false
            end
        end
    end

    -- local auctionBuyUnlockInfo = Module.ModuleUnlock:GetModuleUnlockInfoById(SwitchModuleID.ModuleAuctionBuy)
    -- local UnlockLvLhs = auctionBuyUnlockInfo.bIsUnlocked and 0 or auctionBuyUnlockInfo.unlockLv
    -- local UnlockLvRhs = auctionBuyUnlockInfo.bIsUnlocked and 0 or auctionBuyUnlockInfo.unlockLv

    -- UnlockLvLhs = LhsSItem and math.min(LhsSItem:GetSafeHouseUnlockInfo().unlockLv, UnlockLvLhs) or UnlockLvLhs
    -- UnlockLvRhs = RhsSItem and math.min(RhsSItem:GetSafeHouseUnlockInfo().unlockLv, UnlockLvLhs) or UnlockLvRhs

    -- if UnlockLvLhs and UnlockLvRhs and UnlockLvLhs ~= UnlockLvRhs then
    --     return UnlockLvLhs < UnlockLvRhs
    -- end

    --第二优先级：枪系
    local isTrueWeapon = ItemSortTool.IsTrueWeapon(Lhs)
    local isTrueWeapon1 = ItemSortTool.IsTrueWeapon(Rhs)
    if isTrueWeapon and isTrueWeapon1 then
        if ItemSortTool.GetGunClassOrder(Lhs) ~= ItemSortTool.GetGunClassOrder(Rhs) then
            return ItemSortTool.GetGunClassOrder(Lhs) < ItemSortTool.GetGunClassOrder(Rhs)
        end
    end

    local bAscending = true
    --第三优先级：品质
    if Lhs.quality ~= Rhs.quality then
        if bAscending then
            return Lhs.quality < Rhs.quality
        else
            return Lhs.quality > Rhs.quality
        end
    end

    --第四优先级：道具优先级
    local aPriority = Lhs.priority
    local bPriority = Rhs.priority
    if aPriority ~= bPriority then
        return aPriority > bPriority
    end 

    --第五优先级：价值
    local aPrice = Lhs:GetSingleSellPrice()
    local bPrice = Rhs:GetSingleSellPrice()
    if aPrice ~= bPrice then
        if bAscending then
            return aPrice < bPrice
        else
            return aPrice > bPrice
        end
    end
    --第六优先级：Order
    if LhsSItem and RhsSItem then
        if LhsSItem.order ~= RhsSItem.order then
            return LhsSItem.order < RhsSItem.order
        end
    end

    --第七优先级：Gid
    if Lhs.instanceId ~= Rhs.instanceId then
        return Lhs.instanceId < Rhs.instanceId
    end

    return false
end

--- 先排类型，再排价值
---@param LhsSItem ShopItem
---@param RhsSItem ShopItem
ItemSortTool.SortMerchantShopSItem = function(LhsSItem, RhsSItem)
    local Lhs = LhsSItem.item
    local Rhs = RhsSItem.item
    --第一优先级：是否是最新解锁可购买且未读
    local bLhsUnlockNew = Server.ShopServer:GetIsUnlockNewByExchangeId(LhsSItem.exchangeId)
    local bRhsUnlockNew = Server.ShopServer:GetIsUnlockNewByExchangeId(RhsSItem.exchangeId)
    if bLhsUnlockNew ~= bRhsUnlockNew then
        if bLhsUnlockNew then
            return true
        else
            return false
        end
    end

    --第一优先级：是否解锁可购买
    local bLhsCanBuy = LhsSItem:GetIsUnlock() and true or false
    local bRhsCanBuy = RhsSItem:GetIsUnlock() and true or false
    if bLhsCanBuy ~= bRhsCanBuy then
        if bLhsCanBuy then
            return true
        else
            return false
        end
    end

    --第二优先级：价值
    -- local aPrice = Lhs:GetSingleSellPrice()
    -- local bPrice = Rhs:GetSingleSellPrice()
    -- local aPrice = LhsSItem:GetShopPropBuyPrice()
    -- local bPrice = RhsSItem:GetShopPropBuyPrice()
    -- if aPrice ~= bPrice then
    --     return aPrice < bPrice
    -- end

    -- local bLhsPurchase = LhsSItem:GetExchangeType() == EShopOptType.Purchase and true or false
    -- local bRhsPurchase = RhsSItem:GetExchangeType() == EShopOptType.Purchase and true or false
    -- if bLhsPurchase ~= bRhsPurchase then
    --     if bLhsPurchase then
    --         return true
    --     else
    --         return false
    --     end
    -- end

    if not LhsSItem:GetIsUnlock() and not RhsSItem:GetIsUnlock() then
        if LhsSItem.unlockSeasonLv ~= 0 and RhsSItem.unlockSeasonLv ~= 0 and LhsSItem.unlockSeasonLv ~= RhsSItem.unlockSeasonLv then
            return LhsSItem.unlockSeasonLv < RhsSItem.unlockSeasonLv
        end
        if LhsSItem.unlockLv ~= 0 and RhsSItem.unlockLv ~= 0 and LhsSItem.unlockLv ~= RhsSItem.unlockLv then
            return LhsSItem.unlockLv < RhsSItem.unlockLv
        end
        if LhsSItem.unlockQuestId ~= 0 and RhsSItem.unlockQuestId ~= 0 and LhsSItem.unlockQuestId ~= RhsSItem.unlockQuestId then
            return LhsSItem.unlockQuestId < RhsSItem.unlockQuestId
        end
    end

    --第三优先级：Order
    if LhsSItem.order ~= RhsSItem.order then
        return LhsSItem.order < RhsSItem.order
    end

    --第四优先级：Gid
    if Lhs.instanceId ~= Rhs.instanceId then
        return Lhs.instanceId < Rhs.instanceId
    end

    return false
end

ItemSortTool.GetGunClassOrder = function(GunItem)
    local SubType = ItemHelperTool.GetSubTypeById(GunItem.id)
    return SubType
end

ItemSortTool.IsTrueWeapon = function(item)
    local weaponFeature = item:GetFeature(EFeatureType.Weapon)
    if weaponFeature then
        return weaponFeature:IsTrueWeapon()
    end
    return false
end

ItemSortTool.SortBindPriority = function(aItem, bItem)
    local aIsBind = aItem:CheckIsBind()
    local bIsBind = bItem:CheckIsBind()
    if aIsBind ~= bIsBind then
        return aIsBind
    end
end

ItemSortTool.SortMainContainerPriority = function(LI, RI)
    if LI.InSlot and LI.InSlot.SlotType and RI.InSlot and RI.InSlot.SlotType then
        if LI.InSlot.SlotType == ESlotType.MainContainer and RI.InSlot.SlotType ~= ESlotType.MainContainer then
            return LI
        end
    end
end

ItemSortTool.SortShopCostItems = function (aItem, bItem)
    local aIsBind = aItem:CheckIsBind()
    local bIsBind = bItem:CheckIsBind()
    if aIsBind ~= bIsBind then
        return aIsBind
    end

    if aItem.InSlot and aItem.InSlot.SlotType and bItem.InSlot and bItem.InSlot.SlotType then
        if aItem.InSlot.SlotType == ESlotType.MainContainer and bItem.InSlot.SlotType ~= ESlotType.MainContainer then
            return aItem
        end
    end
end

return ItemSortTool