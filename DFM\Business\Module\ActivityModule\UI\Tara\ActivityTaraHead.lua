----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMWeaponAssembly)
----- LOG FUNCTION AUTO GENERATE END -----------

--头像
---@class ActivityTaraHead : LuaUIBaseView
local ActivityTaraHead = ui("ActivityTaraHead")
local Config = Module.Activity.Config

function ActivityTaraHead:Ctor()
    self._wtHead = self:Wnd("DFImage_37"        , UIImage)
    self._wtHcdn = self:Wnd("DFCDNImage_0"      , DFCDNImage)
    self._wtIcon = self:Wnd("WBP_CommonHeadIcon", CommonHeadIcon)
    self._wtDBtn = self:Wnd("DFButton_15" , UIButton)
    self._wtDBtn:Event("OnClicked", self._OnClicked, self)
    self._wtDBtn:Event("OnHovered", self._OnHovered, self)
    self._wtDBtn:Event("OnUnHovered", self._OnUnHovered, self)
    --新增
    self._wtRedFrame   = self:Wnd("DFCanvasPanel_61", UIWidgetBase)
    self._wtWhiteFrame = self:Wnd("WBP_CommonHover" , UIWidgetBase)
    self._wtWhiteFrame:Collapsed()
end

function ActivityTaraHead:_OnHovered()
    self._wtWhiteFrame:SelfHitTestInvisible()
end
function ActivityTaraHead:_OnUnHovered()
    self._wtWhiteFrame:Collapsed()
end

function ActivityTaraHead:_OnClicked()
    if self._jumpId == 1 then
        --切换干员
        Config.evtOpenTaraPanel:Invoke(self._activityID, self._data, 1)
    elseif self._jumpId == 2 then
        --打开图鉴
        local descs  = {}
        local icons  = {}
        local titles = {}
        local extraParam = {
            isIcon = false,
            title = Config.Loc.SharePicture,
            iconX = 1604,
            iconY = 902,
            frameX = 1564,
            frameY = 862,
            bAutoResize = true,
        }
        for index, path in ipairs(self._paths or {}) do
            if path then
                table.insert(descs  , "")
                table.insert(icons  , tostring(path))
                table.insert(titles , "")
            end
        end
        if self._activityID and #icons > 0 then
            Facade.UIManager:AsyncShowUI(UIName2ID.ActivityTopicPopWindow, nil, self, self._activityID, descs, icons, titles, extraParam)
        end
    end
end

function ActivityTaraHead:SetHeadBtn(isbool)
    if isbool then
        self._wtDBtn:Visible()
    else
        self._wtDBtn:Collapsed()
    end
end

function ActivityTaraHead:SetRedFrame(isbool)
    if isbool then
        self._wtRedFrame:SelfHitTestInvisible()
    else
        self._wtRedFrame:Collapsed()
    end
end

function ActivityTaraHead:AddImagePath(paths)
    self._paths = paths
end

function ActivityTaraHead:InitData(activityID, data, index, jumpId)
    self._activityID = activityID
    self._data   = data
    self._jumpId = jumpId
    if data then
        local path = tostring(data.path or "")
        if self:_LoadPlayerHeadIcon(path) then
            self._wtHcdn:SetVisibility(ESlateVisibility.Collapsed)
            self._wtHead:SetVisibility(ESlateVisibility.Collapsed)
        elseif string.starts_with(path, "C=") then
            local url = string.format("Resource/Texture/Activity/%s", path)
            self._wtHcdn:SetCDNImage(url, true, Module.CDNIcon.Config.ECdnTagEnum.Activity)
            self._wtHcdn:SetVisibility(ESlateVisibility.HitTestSelfOnly)
            self._wtHead:SetVisibility(ESlateVisibility.Collapsed)
        else
            local id = tonumber(path)
            if type(id) == "number" then
                local itemData = ItemBase:New(id)
                if itemData then
                    path = itemData.itemIconPath
                end
            end
            self._wtHead:AsyncSetImagePath(path, true)
            self._wtHead:SetVisibility(ESlateVisibility.HitTestSelfOnly)
            self._wtHcdn:SetVisibility(ESlateVisibility.Collapsed)
        end
        self:SetImgStroke(false)
        self:SetRedFrame(false)
    end
end

function ActivityTaraHead:SetImgStroke(isBool)
    if self.SetStroke then
        self:SetStroke(isBool == true and 1 or 0)
    end
end

function ActivityTaraHead:_LoadPlayerHeadIcon(path)
    --验证数据是否合法
    local GetUrl = function()
        local url = Server.RoleInfoServer.picUrl
        if url == "" then
            return Server.SDKInfoServer:GetPictureUrl()
        else
            return url
        end
    end
    local url = tostring(path or "")
    if string.starts_with(url, "http") then
        local headIcon = {
            pic_url = GetUrl(),
            level   = Server.RoleInfoServer.accountLevel,
            gender  = Server.RoleInfoServer.gender,
            player_id  = Server.AccountServer:GetPlayerId(),
            nick_name  = Server.RoleInfoServer.nickName or "",
            season_lvl = Server.RoleInfoServer.seasonLevel,
            safehouse_degree = 0,
            sol_rank_attended = false,
            mp_rank_attended = false,
            mp_rank_score = 0,
            sol_rank_score = 1000,
            rank_attended = false,
            rank_score = 1000,
        }
        self._wtIcon:InitPortrait(headIcon, HeadIconType.HeadNore)
        self._wtIcon:CollapsedAllLevel()
        self._wtIcon:HitTestInvisible()
        return true
    else
        self._wtIcon:Collapsed()
        return false
    end
end

function ActivityTaraHead:OnShowBegin()
    self:_AddEventListener()
end

function ActivityTaraHead:OnHideBegin()
    self:RemoveAllLuaEvent()
end

function ActivityTaraHead:SetIndex(index)
    self._index = index
end

function ActivityTaraHead:_AddEventListener()
    self:AddLuaEvent(Config.evtAddTaraHandleAdaptation, self._OnAddTaraHandleAdaptation, self)
end

--发起器
function ActivityTaraHead:_OnAddTaraHandleAdaptation(handleType, ...)
    if handleType == ETaraHandleType.Anim2 then
        self:_OnPlayInAnim(...)
    end
end

function ActivityTaraHead:_OnPlayInAnim(time)
    local index = self._index
    if index and time then
        local func = function()
            self:Visible()
            self:PlayAnimation(self.WBP_PatrolAsala_HeadShot_Q1ZNK_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
        end
        self:Hidden()
        if index == 1 then
            time = 0
        else
            time = time * (index - 1)
        end
        Timer.DelayCall(time, func, self)
    end
end

function ActivityTaraHead:OnClose()
end

return ActivityTaraHead