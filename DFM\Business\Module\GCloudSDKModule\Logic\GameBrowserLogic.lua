----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGCloudSDK)
----- LOG FUNCTION AUTO GENERATE END -----------

local function log(...)
    loginfo("[GameBrowserLogic]",...)
end

local GameBrowserLogic = {}
local UIManager = Facade.UIManager
local UDFMGameBrowser = import "DFMGameBrowser"
local LocalizeTool = require "DFM.StandaloneLua.BusinessTool.LocalizeTool"

--region --MSDK/INTL WebView

GameBrowserLogic.OpenUrl = function(url, screenType, isFullScreen, isUseURLEncode, extraJson, isBrowser)
    screenType = screenType or 1
    --windows screenType是为了区分cef和webview，这里使用webview
    if PLATFORM_WINDOWS then
        screenType = 2
    end
    isFullScreen = isFullScreen or false
    isUseURLEncode = isUseURLEncode or true
    isBrowser = isBrowser or false
    UDFMGameBrowser.OpenUrl(url, screenType, isFullScreen, isUseURLEncode, extraJson, isBrowser)
end

GameBrowserLogic.SimpleOpenUrl = function(url)
    UDFMGameBrowser.SimpleOpenUrl(url)
end

GameBrowserLogic.OnWebViewOptNotify = function(type, jsonData)
    Module.GCloudSDK.Config.Events.evtOnGCloudSDKWebBrowserCallback:Invoke(type, jsonData)
end

GameBrowserLogic.LaunchURL = function(url)
    return UDFMGameBrowser.LaunchURL(url, "", "")
end

GameBrowserLogic.GetEncodeUrl = function(url)
    return UDFMGameBrowser.GetEncodeUrl(url)
end

--endregion


---@class WebBrowserParams
---@field url string 要打开的网址
---@field title string 标题名称
---@field width number 窗口的长度(px)，配置时width_scale无效
---@field height number 窗口的高度(px)，配置时height_scale无效
---@field width_scale number 窗口的长度[0.27-1]
---@field height_scale number 窗口的高度[0.43-1]
---@field showBackBtn boolean 是否展示后退按钮，默认值false
---@field showFowardBtn boolean 是否展示前进按钮，默认值false
---@field showRefreshBtn boolean 是否展示刷新按钮，默认值false
---@field showOutsideWebBrowserBtn boolean 是否展示外部浏览器按钮，默认值false
---@field useSDKWebBrowser boolean 是否使用SDK的浏览器 默认值false
---@field webBrowserOrientation number 浏览器的朝向，Portrait=2, Landscape=3
---@field useURLEncode boolean 是否要添加INTL/MSDK的登录态
---@field additionalParams string 需要额外添加的参数，使用#分割,目前支持的参数包括openid,language,regioncode,zoneid

--region DF SDK WebBrowser
--- DF SDK WebBrowser OpenUrl
---@param params WebBrowserParams 额外参数
GameBrowserLogic.OpenSDKWebBrowserUrl = function (params)
    loginfo("GameBrowserLogic.OpenSDKWebBrowserUrl url:", params.url or "")

    params.title = params.title or ""
    if params.width_scale then
        params.width_scale = math.clamp(params.width_scale, 0.27, 1)
    elseif params.width == nil then
        params.width_scale = 1
    end
    if params.height_scale then
        params.height_scale = math.clamp(params.height_scale, 0.43, 1)
    elseif params.height == nil then
        params.height_scale = 1
    end
    params.showBackBtn = params.showBackBtn or false
    params.showFowardBtn = params.showFowardBtn or false
    params.showRefreshBtn = params.showRefreshBtn or false
    params.showOutsideWebBrowserBtn = params.showOutsideWebBrowserBtn or false

    Facade.UIManager:AsyncShowUI(UIName2ID.SDKWebBrowserPanel, GameBrowserLogic.OnSDKWebBrowserPanelCreateFinished, nil, params)
end

---@desc 从url中获取WebBrowserData
---@param url string url链接
---@return table
GameBrowserLogic.GetDataFromUrl = function(url)
    local data = {}
    local index = string.find(url, "?")
    local paramstr = ""
    if index then
        paramstr = string.sub(url, index + 1)
    end
    for params in string.gmatch(paramstr, "[^%&%?]+") do
        for key, value in string.gmatch(params, "(.+)=(.*)") do
            data[key] = value
        end
    end
    return data
end

---@desc 从table生成Url参数
---@param data table
---@return string
GameBrowserLogic.GetUrlFromData = function(data)
    local str = ""
    local bfirstTime = true
    for key, value in pairs(data) do
        if bfirstTime then
            bfirstTime = false
            str = str .. string.format("?%s=%s", key, value) 
        else
            str = str .. string.format("&%s=%s", key, value) 
        end
    end
    return str
end

---@desc urlDecode 实现
GameBrowserLogic.urlDecode = function(str)
    str = string.gsub (str, "+", " ")
    str = string.gsub (str, "%%(%x%x)", function(h)
        return string.char(tonumber(h, 16))
    end)
    return str
end

---@desc 从url中获取WebBrowserData
---@param url string url链接
---@return WebBrowserParams
GameBrowserLogic.GetWebBrowserDataFromUrl = function(url)
    local data = GameBrowserLogic.GetDataFromUrl(url)
    if data.WebBrowserData and data.WebBrowserData ~= "" then
        data.WebBrowserData = GameBrowserLogic.urlDecode(data.WebBrowserData)
        local JsonFactory = require "DFM.YxFramework.Plugin.Json.Json"
        local Json = JsonFactory.createJson()
        local urlData = Json.decode(data.WebBrowserData, 1)
        return urlData
    end
    return {}
end

---@desc 给url添加额外信息
---@param webBrowserData WebBrowserParams
---@return string
GameBrowserLogic.GetAdditionalInfo = function(webBrowserData)
    local url = webBrowserData.url or ""
    local data = GameBrowserLogic.GetDataFromUrl(url)
    if webBrowserData.additionalParams then
        for param in string.gmatch(webBrowserData.additionalParams, "[^%#]+") do
            if param == "openid" then
                data.openid = Server.SDKInfoServer:GetOpenIdStr()
            elseif param == "language" then
                data.laugue = LocalizeTool.GetCurrentCulture()
            elseif param == "zoneid" then
                data.zoneid = Server.SDKInfoServer:GetZoneId()
            elseif param == "area_id" then
                data.area_id = Server.PayServer:GetRegistrationnumericCode()
            elseif param == "regioncode" then
                data.regioncode = Server.SDKInfoServer:GetFirstLoginRegion()
            elseif param == "access_token" then
                data.access_token = Server.SDKInfoServer:GetAccessToken()
            end
        end
        data.info = GameBrowserLogic.GenerateMD5Hash(data)
    end
    local index = string.find(url, "?")
    if index then
        url = string.sub(url, 1, index - 1)
    end
    url = url .. GameBrowserLogic.GetUrlFromData(data)
    return url
end

GameBrowserLogic.OnSDKWebBrowserPanelCreateFinished = function(uiIns)
    Module.GCloudSDK.Field:SetSDKWebBrowserPanel(uiIns)
end

GameBrowserLogic.CloseSDKWebBrowserPanel = function()
    local panel = Module.GCloudSDK.Field:GetSDKWebBrowserPanel()
    if panel then
        Facade.UIManager:CloseUI(panel)
        Module.GCloudSDK.Field:SetSDKWebBrowserPanel(nil)
    end
end

GameBrowserLogic.GenerateMD5Hash = function(data, secret)
    local sortByAsicData = {
        {
            key = "appSecret",
            value = secret or "ed7xWDmA"
        }
    }

    for _key, _value in pairs(data) do
        sortByAsicData[#sortByAsicData + 1] = {
            key = _key,
            value = _value
        }
    end
    table.sort(sortByAsicData, function(A, B) 
        return A.key < B.key
    end)
    
    local origin = ""
    for i, v in ipairs(sortByAsicData) do
        if v.value ~= "" then
            origin = origin .. v.key .. v.value
        end
    end

    local ret = ""
    if UDFMGameBrowser and UDFMGameBrowser.GetMD5Hash then
        ret = UDFMGameBrowser.GetMD5Hash(origin)
    end
    return ret
end

GameBrowserLogic.CallJS = function(jsonJSParams)
    if UDFMGameBrowser and UDFMGameBrowser.CallJS then
        UDFMGameBrowser.CallJS(jsonJSParams)
    end
end

--endregion

return GameBrowserLogic