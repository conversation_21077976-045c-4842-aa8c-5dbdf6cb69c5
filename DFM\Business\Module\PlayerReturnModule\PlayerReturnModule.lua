----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMPlayerReturn)
----- LOG FUNCTION AUTO GENERATE END -----------

---@diagnostic disable: missing-parameter

-- 玩家回流活动模块
local ReddotTrieLogic               = require "DFM.Business.Module.ReddotTrieModule.Logic.ReddotTrieLogic"
local IPlayerReturnSubActivity      = require "DFM.Business.Module.PlayerReturnModule.SubActivities.PlayerReturnSubActivities"
local PlayerReturnConfig            = require "DFM.Business.Module.PlayerReturnModule.PlayerReturnConfig"
local Promise                       = require "DFM.Business.DataStruct.Common.Base.Promise"
local PlayerReturnStatistics        = require "DFM.Business.Module.PlayerReturnModule.PlayerReturnStatistics"
local Filter                        = require "DFM.Business.DataStruct.Common.Base.Filter"
local F                             = require "DFM.Business.DataStruct.Common.Base.Functional"

---@class PlayerReturnModule: ModuleBase
PlayerReturnModule = class("PlayerReturnModule", require("DFM.YxFramework.Managers.Module.ModuleBase"))

function PlayerReturnModule:Ctor()
    self.EVENT_DURATION_DAYS = 14
    self.SECONDS_IN_DAY = 60 * 60 * 24
    self.EVENT_REFRESH_TIME_IN_DAY_LOCAL = 60*60*4 --活动每天刷新的时间是凌晨4点

    if IsBuildRegionCN() then                                           -- 国内版刷新时间为 UTC+8 4:00
        self.EVENT_REFRESH_TIME_IN_DAY = (self.EVENT_REFRESH_TIME_IN_DAY_LOCAL - (8*60*60)) % self.SECONDS_IN_DAY
    else
        self.EVENT_REFRESH_TIME_IN_DAY = self.EVENT_REFRESH_TIME_IN_DAY_LOCAL     -- 海外版刷新时间为 UTC+0 4:00
    end
end

-- 回流模块调用入口点: 
-- 1. SOL/MP大厅蓝图（包含回流入口控件）
-- 2. SOL/MP大厅加载完成事件: 签到引导弹窗
function PlayerReturnModule:OnInitModule()

    self._stringTableResStub = ResStub:NewIns("ResStub")
    Facade.ResourceManager:AsyncLoadResources(self._stringTableResStub, {"/Game/StringTables/STForDataTable/Common/Base/LOC_ActivityReturnNewContent.LOC_ActivityReturnNewContent"}, nil)

    Module.Settlement.Config.Events.evtEndAllSettlementPop:AddListener(self._OnPostSettlement, self)
    Server.ActivityServer.Events.evtActivityTaskChange:AddListener(self._OnActivityTaskChange, self)
    Server.ActivityServer.Events.evtActivityInfoUpdate:AddListener(self._OnActivityInfoUpdate, self)

    Module.PopupSequencer:RegisterPopupProvider({
        id                  = "PlayerReturnLobbyPopup",
        priority            = 0,
        cancelConditions    = {
                                "ForcedGuide", 
                                "ImportantGuide"
                            },
        interruptEvents     = {
                                "ForcedGuide", 
                                "ImportantGuide"
                            },

        ---@return Promise2? popupPromise
        fPopup = function(timingID, extraParam, interruptPromise)
            local currMode = Server.ArmedForceServer:GetCurArmedForceMode()
            ---@type PlayerReturnSignInImpl
            local actv = IPlayerReturnSubActivity.Get(ReturnActivityType.ReturnActivityTypeAttend, currMode)

            --- 处理额外参数
            extraParam = extraParam or {}

            --- 是否忽略当天已弹出的状态强制弹出
            local bIsForced = extraParam.bForceReturnPopup
            if bIsForced then
                actv:SetHasShownLobbyPopupToday(false)
            end

            local needSignInPopup = Module.PlayerReturn:IsActive(currMode) and actv:GetRedDotState() and (not actv:HasShownLobbyPopupToday())
            if not needSignInPopup then
                -- 弹窗流程继续
                return Promise.NewResolved({reply = EPopupReply.Continue})
            else
                local popupPromise = Promise.New()
                -- 进入弹窗
                Facade.UIManager:AsyncShowUI(UIName2ID.PlayerReturnLobbyPopup,
                    ---@param ins PlayerReturnLobbyPopup
                    function(ins)
                        Module.PlayerReturn.Field._lobbyPopupUiIns = ins
                        actv:SetHasShownLobbyPopupToday(true)
                        ins:SetPopupPromises(popupPromise, interruptPromise)
                    end
                )
                return popupPromise
            end
        end
    })

    Module.PopupSequencer:RegisterPopupProvider({
        id                  = "PlayerReturnDailyMatchPopup",
        priority            = 0,
        cancelConditions    = {
                                "ForcedGuide",
                                "ImportantGuide",
                              },
        interruptEvents     = {
                                "ForcedGuide",
                                "ImportantGuide",
                              },
    
        ---@return Promise2? popupPromise
        fPopup = function(timingID, extraParam, interruptPromise)
            local bForced = (extraParam or {}).bForceReturnPopup

            local currMode = Server.ArmedForceServer:GetCurArmedForceMode()
            ---@type PlayerReturnDailyMatchImpl
            local actv = IPlayerReturnSubActivity.Get(ReturnActivityType.ReturnActivityTypeFight, currMode)
            local activityInfo = actv:GetActivityInfo()
            
            -- 对局爽打弹窗有两个途径进入
            -- 1. 回流签到弹窗关闭之后
            -- 2. 对局结束之后
            -- 由于无法保证两个事件的时间关系，可能出现重复弹窗
            -- 现在先限制对局爽打弹窗两次之间必须间隔一分钟以上
            local now = Facade.ClockManager:GetLocalTimestamp()
            if (now - actv:GetLastPopupTime() <= 60) and not bForced then
                return
            end

            -- 要触发弹窗，必须要有可进行的对局爽打任务
            local infoCheck = activityInfo and 
                              Filter.AnyMatch(activityInfo.task_info, F.Get("state"):Eq(F.V(ActivityTaskState.ActivityTaskStateAccepted))) and
                              Module.PlayerReturn:IsActive(currMode)
            if not infoCheck then return end
    
            local popupPromise = Promise.New()
            Facade.UIManager:AsyncShowUI(UIName2ID.PlayerReturnDailyMatchPopup,
                ---@param ins PlayerReturnDailyMatchPopup
                function(ins)
                    ins:SetPopupPromises(popupPromise, interruptPromise)
                end
            )
    
            return popupPromise
        end
    })
end

function PlayerReturnModule:GetPlayerTag()
    return Server.RoleInfoServer.return_user_tag or 0
end

function PlayerReturnModule:_OnActivityTaskChange(activityID)
    local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(activityID)
    if (not activityInfo) or (not activityInfo.return_info) or (activityInfo.return_info.type == 0) then return end

    local impl = IPlayerReturnSubActivity.Get(activityInfo.return_info.type, activityInfo.mode_tag)
    impl:UpdateRedDot(ReddotTrieLogic.GetObserver(EReddotTrieObserverType.PlayerReturn).manager)
end

function PlayerReturnModule:_OnActivityInfoUpdate(activityID)
    ReddotTrieLogic.GetObserver(EReddotTrieObserverType.PlayerReturn):Update()
    self:_debug_DumpDailyMatchInfo()
end

function PlayerReturnModule:_OnPostSettlement()
    logwarning("[finnywxu] PlayerReturnModule:_OnPostSettlement")
    Module.PopupSequencer:ShowPopup("PlayerReturnDailyMatchPopup")
end

---@return RedDotAgent?
function PlayerReturnModule:GetRedDotAgent()
    ---@type PlayerReturnDataObserver
    local observer = ReddotTrieLogic.GetObserver(EReddotTrieObserverType.PlayerReturn)
    if observer then
        return observer.manager
    end
end

function PlayerReturnModule:UpdateRedDot()
    ---@type PlayerReturnDataObserver
    local observer = ReddotTrieLogic.GetObserver(EReddotTrieObserverType.PlayerReturn)
    if observer then
        observer:Update()
    end
end

function PlayerReturnModule:OnDestroyModule()
    self:RemoveAllLuaEvent()
end

function PlayerReturnModule:OnActivityTaskReward(name, dataChange)
    if dataChange ~= nil and dataChange.prop_changes ~= nil and #dataChange.prop_changes > 0 or dataChange.currency_changes ~= nil and #dataChange.currency_changes > 0 then
        local itemList = {}
        local propNum = #dataChange.prop_changes
        for i = 1, propNum, 1 do
            local propInfo = dataChange.prop_changes[i].prop
            local giftItem = ItemBase:New(propInfo.id, propInfo.num, propInfo.gid, false)
            giftItem:SetRawPropInfo(propInfo)
            table.insert(itemList, giftItem)
        end
        local currency_changes = dataChange.currency_changes
        for _, currencyChange in ipairs(currency_changes) do
            if currencyChange.delta > 0 then
                local item = ItemBase:New(currencyChange.currency_id, currencyChange.delta)
                table.insert(itemList, item)
            end
        end
        Module.Reward:OpenRewardPanel(name, nil, itemList, nil, nil, nil, true)
    end
end

function PlayerReturnModule:OnMilestoneAward(name,dataChange, failed)
    if dataChange ~= nil and dataChange.prop_changes ~= nil and #dataChange.prop_changes > 0 or dataChange.currency_changes ~= nil and #dataChange.currency_changes > 0 then
        local itemList = {}
        local propNum = #dataChange.prop_changes
        for i = 1, propNum, 1 do
            local propInfo = dataChange.prop_changes[i].prop
            local giftItem = ItemBase:New(propInfo.id, propInfo.num, propInfo.gid, false)
            giftItem:SetRawPropInfo(propInfo)
            table.insert(itemList, giftItem)
        end
        local currency_changes = dataChange.currency_changes
        for _, currencyChange in ipairs(currency_changes) do
            if currencyChange.delta > 0 then
                local item = ItemBase:New(currencyChange.currency_id, currencyChange.delta)
                table.insert(itemList, item)
            end
        end
        Module.Reward:OpenRewardPanel(name, nil, itemList, nil, nil, nil, true)
    end
    if failed and #failed > 0 then
        for i = 1, #failed, 1 do
            local commonErrorConfig = Module.CommonTips.Config.GetErrorTable()
            local errorCode = failed[i].result
            if commonErrorConfig and commonErrorConfig[tostring(errorCode)] then
                local tipStr = commonErrorConfig[tostring(errorCode)].BaseDesc
                Module.CommonTips:ShowSimpleTip(string.format("%s", tipStr))
            end
        end
    end
end

local UIValid = function (maybeUIIns)
    if maybeUIIns and maybeUIIns.IsValid and maybeUIIns:IsValid() then
        return true
    end
    return false
end

function PlayerReturnModule:IsLobbyPopupOpen()
    return UIValid(Module.PlayerReturn.Field._lobbyPopupUiIns)
end

function PlayerReturnModule:IsMainPanelOpen()
    return UIValid(Module.PlayerReturn.Field._mainPanelUiIns)
end

function PlayerReturnModule:IsLobbyEntranceOpen()
    return UIValid(Module.PlayerReturn.Field._lobbyEntranceUiIns)
end

function PlayerReturnModule:OnPlayerReturnTagDropped()
    local field = Module.PlayerReturn.Field

    -- 不用判断大厅入口，直接收起来就行，有其他主要面板打开的时候弹个框，但是不关闭，等玩家自己出来
    local bShouldNotifyPlayer = self:IsLobbyPopupOpen() or self:IsMainPanelOpen()
    if bShouldNotifyPlayer then
        Module.CommonTips:ShowSimpleTip(Module.PlayerReturn.Config.Localization.ActivityEndPopMessage)
    end
end

function PlayerReturnModule:OnRequestFailedFromExpiration()
    Module.CommonTips:ShowSimpleTip(Module.PlayerReturn.Config.Localization.ActivityEndPopMessage)
end

-- 以下全部条件满足视为玩家正在该模式回流：
-- 1. 玩家回流Tag不为0
-- 2. 当前时间小于回流过期时间 (RoleInfoServer提供)
-- 3. 主活动信息存在
-- 4. 活动没有被全部完成（完成所有签到、任务等并领取完奖励）
function PlayerReturnModule:IsActive(optArmedForceMode)
    if not optArmedForceMode then
        optArmedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
    end

    local returnInterface = IPlayerReturnSubActivity.Get(ReturnActivityType.ReturnActivityTypeMainPage, optArmedForceMode)
    return     (returnInterface:GetActivityInfo() ~= nil)
           and (not returnInterface:IsFinished())
           and (Module.PlayerReturn:GetPlayerTag() > 0)
           and (Module.PlayerReturn:GetExpireTime() > Facade.ClockManager:GetLocalTimestamp())
end

local EVENT_DURATION_DAYS = 14
local SECONDS_IN_DAY = 60 * 60 * 24
local EVENT_REFRESH_TIME_IN_DAY_LOCAL = 60*60*4 --活动每天刷新的时间是凌晨4点

if IsBuildRegionCN() then                                           -- 国内版刷新时间为 UTC+8 4:00
    EVENT_REFRESH_TIME_IN_DAY = (EVENT_REFRESH_TIME_IN_DAY_LOCAL - (8*60*60)) % SECONDS_IN_DAY
else
    EVENT_REFRESH_TIME_IN_DAY = EVENT_REFRESH_TIME_IN_DAY_LOCAL     -- 海外版刷新时间为 UTC+0 4:00
end

--设置今天已弹大厅弹窗
function PlayerReturnModule:SetShownLobbyPopupToday()
    local tNow = Facade.ClockManager:GetLocalTimestamp()
    local eventDay = (tNow - EVENT_REFRESH_TIME_IN_DAY) // SECONDS_IN_DAY
    Server.TipsRecordServer:SetNumber(Server.TipsRecordServer.keys.PlayerReturnLastDayLobbyPopupShown, eventDay)
    loginfo(6, "SetShownLobbyPopupToday")
end

function PlayerReturnModule:GetExpireTime()
    return Server.RoleInfoServer.return_user_expire_time
end

-- Hint only
if false then
    ---@type PlayerReturnModule
    Module.PlayerReturn = nil
end

function PlayerReturnModule:GetDayText(i)
    return StringUtil.FTextFormat(PlayerReturnConfig.Localization.DailyMatch.DayFormat, {day = i})
end


function PlayerReturnModule:ShowLobbyPopup()
    Module.PopupSequencer:ShowPopup("PlayerReturnLobbyPopup")
end

function PlayerReturnModule:CloseLobbyPopup()
    if Module.PlayerReturn.Field._lobbyPopupUiIns then
        Facade.UIManager:CloseUI(Module.PlayerReturn.Field._lobbyPopupUiIns)
        Module.PlayerReturn.Field._lobbyPopupUiIns = nil
    end
end

function PlayerReturnModule:ShowMainPanel(bIsFromLobbyEntrance, enterPanel)
    if Module.PlayerReturn:IsActive() then
        Server.ActivityServer:InitActivityInfo(nil, nil)
        Facade.UIManager:AsyncShowUI(UIName2ID.PlayerReturnMainPanel, function(ins) Module.PlayerReturn.Field._mainPanelUiIns = ins end, nil, enterPanel)
        if bIsFromLobbyEntrance then
            PlayerReturnStatistics:TrackEnteredActivityFromLobbyEntrance()
        end
    end
end

function PlayerReturnModule:CloseMainPanel(opt_bClearSavedSubPanelSelection)
    local field = Module.PlayerReturn.Field
    if not field._mainPanelUiIns then return end

    if opt_bClearSavedSubPanelSelection == nil then opt_bClearSavedSubPanelSelection = true end
    
    Facade.UIManager:CloseUI(field._mainPanelUiIns)
    field._mainPanelUiIns = nil

    if opt_bClearSavedSubPanelSelection then
        field._mainPanel.restoreSubPanelSelection = field.mainPanelSubPanelIndex.Main
    end
end


local function GetRemainTimeDHMSAsArray(endTimestamp, currentTimestamp) --> {D, H, M, S}
    local remainTime = endTimestamp - currentTimestamp
    if remainTime <= 0 then return {0,0,0,0} end
    local M = 60
    local H = M * 60
    local D = H * 24 
    return {remainTime//D, (remainTime%D)//H, (remainTime%H)//M, (remainTime%M)}
end

function PlayerReturnModule:GetCountdownText(bExcludeMinute)
    local expireTime = Server.RoleInfoServer.return_user_expire_time
    local dhms = GetRemainTimeDHMSAsArray(expireTime, Facade.ClockManager:GetLocalTimestamp())

    if not bExcludeMinute then
        return StringUtil.FTextFormat(Module.PlayerReturn.Config.Localization.CountDownDHMFmtStr, 
            {
                d = dhms[1],
                h = dhms[2],
                m = dhms[3]
            }
        )
    else
        return StringUtil.FTextFormat(Module.PlayerReturn.Config.Localization.CountDownDHFmtStr, 
            {
                d = dhms[1],
                h = dhms[2],
            }
        )
    end
end

-- 从活动领奖结果提取奖励物品用于展示
function PlayerReturnModule:ActivityAwardResultToItemBaseArr(CSActivityTaskReceiveAwardRes)
    local res = CSActivityTaskReceiveAwardRes
    local result = {}
    for _, prop_change in ipairs(res.data_change.prop_changes) do  
        table.insert(result, ItemBase:NewIns(prop_change.prop.id, prop_change.prop.num, prop_change.prop.gid))
    end
    return result
end

-- 获取已完成的任务数/总共任务数 return progress, total
function PlayerReturnModule:GetActivityTaskProgress(activityInfo)
    local progress = 0
    local total = 0

    if activityInfo then
        for _, taskInfo in ipairs(activityInfo.task_info) do
            total = total + 1

            local countProgress = 
                taskInfo.state == ActivityTaskState.ActivityTaskStateCompleted
                or taskInfo.state == ActivityTaskState.ActivityTaskStateRewarded

            progress = progress + (countProgress and 1 or 0)
        end
    end

    return progress, total
end

---@param activityInfo pb_ActivityInfo
function PlayerReturnModule:JumpToDailyMatch(activityInfo, day)
    if day == 1 then
        -- 首日
        if activityInfo.mode_tag == EArmedForceMode.SOL then
            -- SOL: 地图选择，自动配装，高亮引导

            -- 选择目标地图模式
            local matchModeID = activityInfo.return_info.match_mode_id
            local matchModeInfo
            if Server.GameModeServer:CheckMatchModeIsLocked(matchModeID) then
                -- 如果配置的地图模式未解锁，用默认SOL模式
                _, matchModeInfo = Server.GameModeServer:GenMatchModeByWorldEntrancCfg(1, MatchSubMode.SOLPMC)
            else
                -- 使用配置的地图模式
                matchModeInfo = Server.GameModeServer:GenMatchModeByMatchModeID(matchModeID)
            end

            Promise.New(
                function (resolve, reject)
                    if not Server.TeamServer:IsInTeam() then
                        -- 单人，设置地图后跳转
                        
                        Server.GameModeServer:SetMatchMode(matchModeInfo)
                        Server.GameModeServer:SetMatchModes({matchModeInfo})
                        resolve()
                    elseif Server.TeamServer:IsCaptial() then
                        -- 多人，队长，设置队伍地图后跳转
                        resolve(Server.TeamServer:AsyncChangeTeamMatchMode(
                            Server.TeamServer:GetTeamID(),
                            {matchModeInfo},
                            Server.TeamServer:GetAutoMatchTeamMates()
                        ))
                    else -- 非队长，只能跳转
                        resolve()
                    end
                end
            ):Then(
                function()
                    Module.SandBoxMap:Jump(Module.GameMode:GetMapIdByMatchModeId(matchModeID))
                    Module.ArmedForce:ReflowSupport(activityInfo.return_info.type_id)
                    Module.Guide.Config.EGuideEvent.evtOnGuideMsg:Invoke(EGuideMsgSig.PlayerReturnDailyMatchPopupJumpToSolArmedForce)
                end
            )
        else
            -- MP: 跳转到大厅，高亮引导
            Module.Jump:JumpByID(activityInfo.task_info[day].jump_id)
            Module.Guide.Config.EGuideEvent.evtOnGuideMsg:Invoke(EGuideMsgSig.PlayerReturnDailyMatchPopupJumpToMpHall)
        end
    else
        -- 非首日只跳转
        Module.Jump:JumpByID(activityInfo.task_info[day].jump_id)
    end
end

-- 具体说明见 ActivityPickRewardPop:OnShowBegin
function PlayerReturnModule:CheckIsPlayerReturnTaskAndDoStatisticsReport(activityID)
    for gameMode = 1, 2 do
        local actv = IPlayerReturnSubActivity.Get(ReturnActivityType.ReturnActivityTypeNormalTask, gameMode)
        local activityInfo = actv:GetActivityInfo()
        if activityInfo and activityInfo.actv_id == activityID then
            LogAnalysisTool.SignButtonClicked(PlayerReturnStatistics:GetMainPanelUIStatID(ReturnActivityType.ReturnActivityTypeNormalTask) + 1)
        end
    end
end

function PlayerReturnModule:_debug_DumpDailyMatchInfo()
    local str = "PlayerReturnModule:_debug_DumpDailyMatchInfo\n"
    for gameMode = 1, 2 do
        local actv = IPlayerReturnSubActivity.Get(ReturnActivityType.ReturnActivityTypeFight, gameMode)
        local activityInfo = actv:GetActivityInfo()
        if activityInfo then
            str = str..tostring(activityInfo.actv_id).."\n"
            for _, taskInfo in ipairs(activityInfo.task_info) do
                str = str.."  "..tostring(taskInfo.task_id).." state="..tostring(taskInfo.state).."\n"
                for _, award in ipairs(taskInfo.awards) do
                    str = str.."    "..tostring(ItemBase:NewIns(award.prop.id).name).."\n"
                end
                if #taskInfo.awards == 0 then
                    assert(false, "对局爽打任务未配置奖励！"..tostring(activityInfo.actv_id).." "..tostring(taskInfo.task_id))
                end
            end
        end
    end
    logwarning(str)
end

function PlayerReturnModule:Debug_TestLobbyPopup()
    Module.PopupSequencer:ShowPopup("PlayerReturnLobbyPopup", 0, {bForceReturnPopup = true})
end

function PlayerReturnModule:Debug_TestDailyMatchPopup()
    Module.PopupSequencer:ShowPopup("PlayerReturnDailyMatchPopup", 0, {bForceReturnPopup = true})
end

---@param dataChange pb_DataChange
function PlayerReturnModule:ShowTipsIfBoostReceived(dataChange)
    local bExpBoostReceived = false
    local bMeritsBoostReceived = false
    for _, propChange in pairs(dataChange.prop_changes) do
        if propChange.prop.id == 32260000004 then
            bExpBoostReceived = true
        elseif propChange.prop.id == 32280000004 then
            bMeritsBoostReceived = true
        end
    end

    if bExpBoostReceived or bMeritsBoostReceived then
        local msg = ""
        local fmtExpBoostReceived = PlayerReturnConfig.Localization.DailyMatch.ExpBoostReceived
        local fmtMeritsBoostReceived = PlayerReturnConfig.Localization.DailyMatch.MeritsBoostReceived

        if bExpBoostReceived then
            msg = StringUtil.PluralTextFormat(fmtExpBoostReceived, {count = 5})
            if bMeritsBoostReceived then
                msg = msg .. "\n" .. StringUtil.PluralTextFormat(fmtMeritsBoostReceived, {count = 5})
            end
        else
            msg = StringUtil.PluralTextFormat(fmtMeritsBoostReceived, {count = 5})
        end

        Module.CommonTips:ShowSimpleTip(msg)

        -- 需要刷新藏品数据，否则藏品界面显示buff剩余场次的数据的地方会脏
        Server.CollectionServer:FetchCollectionData()
    end
end

return PlayerReturnModule
