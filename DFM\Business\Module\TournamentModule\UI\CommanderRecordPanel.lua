----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRanking)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class CommanderRecordPanel : LuaUIBaseView
local CommanderRecordPanel = ui("CommanderRecordPanel")
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"

function CommanderRecordPanel:Ctor()
    loginfo("CommanderRecordPanel:Ctor")
    self._wtRankWidget=self:Wnd("WBP_Common_BigFlatRank",UIWidgetBase)
    self._wtLevelName=self:Wnd("DFTextBlock_143",UITextBlock)
    self._wtRankEventsBox=self:Wnd("DFScrollBox_153",UIWidgetBase)--赛季事件列表
    self._wtRankDataItemGameCount=self:Wnd("WBP_WinnnerTakesAll_Record_Item02",UIWidgetBase)
    self._wtRankDataItemVehicleKill=self:Wnd("WBP_WinnnerTakesAll_Record_Item02_1",UIWidgetBase)
    self._wtRankDataItemCommanderCount=self:Wnd("WBP_WinnnerTakesAll_Record_Item02_2",UIWidgetBase)
    self._wtRankDataItemInfantryKill=self:Wnd("WBP_WinnnerTakesAll_Record_Item02_3",UIWidgetBase)
    self._wtRankDataItemCommanderWinRate=self:Wnd("WBP_WinnnerTakesAll_Record_Item02_4",UIWidgetBase)
    self._wtRankDataItemRescue=self:Wnd("WBP_WinnnerTakesAll_Record_Item02_5",UIWidgetBase)
    self._wtRankAchieveItemCommander=self:Wnd("WBP_WinnnerTakesAll_Record_Item03_01",UIWidgetBase)
    self._wtRankAchieveItemKiller=self:Wnd("WBP_WinnnerTakesAll_Record_Item03_02",UIWidgetBase)
    self._wtRankAchieveItemStronghold=self:Wnd("WBP_WinnnerTakesAll_Record_Item03_03",UIWidgetBase)
    self._wtRankAchieveItemPilot=self:Wnd("WBP_WinnnerTakesAll_Record_Item03_04",UIWidgetBase)
    self._wtRankAchieveItemTactic=self:Wnd("WBP_WinnnerTakesAll_Record_Item03_05",UIWidgetBase)
    self._wtRankAchieveItemDoctor=self:Wnd("WBP_WinnnerTakesAll_Record_Item03_06",UIWidgetBase)
    self._wtRankAchieveItemPanzer=self:Wnd("WBP_WinnnerTakesAll_Record_Item03_07",UIWidgetBase)
    self._wtRankAchieveItemDeVehicle=self:Wnd("WBP_WinnnerTakesAll_Record_Item03_08",UIWidgetBase)
    self._wtPlayerName=self:Wnd("DFTextBlock_269",UITextBlock)
    self._wtSeasonDurationText=self:Wnd("DFTextBlock_34",UITextBlock)
    self._wtRankWidgetPanel=self:Wnd("DFCanvasPanel_504",UIWidgetBase)
    self._wtRankSummaryPanel=self:Wnd("DFCanvasPanel_396",UIWidgetBase)
    self._wtRanEventsPanel=self:Wnd("DFCanvasPanel_515",UIWidgetBase)
    self._wtRankAchievePanel=self:Wnd("DFCanvasPanel_594",UIWidgetBase)
    self._wtEmptyPanel=self:Wnd("WBP_Common_NoAnything_1",UIWidgetBase)
    self._wtRankYearList=self:Wnd("wtDFCommonSecondTab",DFCommonSecondTabList)--赛年tab列表


    Module.CommonBar:RegStackUITopBarTitle(self,Module.Tournament.Config.Loc.RecordTitle)
end

function CommanderRecordPanel:OnInitExtraData(params)
    loginfo("CommanderRecordPanel:OnInitExtraData")
end

function CommanderRecordPanel:OnOpen()
    loginfo("CommanderRecordPanel:OnOpen")
    self:InitRankYearList()
end

function CommanderRecordPanel:OnShowBegin()
    loginfo("CommanderRecordPanel:OnShowBegin")
    self:_InitGamepadInputs()
end

function CommanderRecordPanel:OnShow()
    loginfo("CommanderRecordPanel:OnShow")
end

function CommanderRecordPanel:OnHideBegin()
    loginfo("CommanderRecordPanel:OnHideBegin")
    self:_DisableGamepadInputs()
end

function CommanderRecordPanel:OnHide()
    loginfo("CommanderRecordPanel:OnHide")
end

function CommanderRecordPanel:OnClose()
    loginfo("CommanderRecordPanel:OnClose")
    Timer.CancelDelay(self._RefreshTabYearListDelayHandle)
    Facade.UIManager:ClearSubUIByParent(self,self._wtRankEventsBox)

end

function CommanderRecordPanel:_InitGamepadInputs()
    if not self._navGroup then
        self._navGroup = WidgetUtil.RegisterNavigationGroup(self, self, "Hittest")
        if self._navGroup  then
            self._navGroup:AddNavWidgetToArray(self._wtRankYearList)
            self._navGroup:AddNavWidgetToArray(self._wtRankEventsBox)

            self._navGroup:SetScrollRecipient(self._wtRankEventsBox)
            self._navGroup:MarkIsStackControlGroup()
            self._navGroup:SetNavSelectorWidgetVisibility(true)
        end
        
    end
end

function CommanderRecordPanel:_DisableGamepadInputs()
    WidgetUtil.RemoveNavigationGroup(self)
    self._navGroup=nil
    
end

function CommanderRecordPanel:SwitchEmptyPanel(bEmpty)
    loginfo("CommanderRecordPanel:SetEmptyRankDetail",bEmpty)
    if bEmpty then
        self._wtRankWidgetPanel:Collapsed()
        self._wtRankSummaryPanel:Collapsed()
        self._wtRanEventsPanel:Collapsed()
        self._wtRankAchievePanel:Collapsed()
        self._wtEmptyPanel:SelfHitTestInvisible()
    else
        self._wtRankWidgetPanel:SelfHitTestInvisible()
        self._wtRankSummaryPanel:SelfHitTestInvisible()
        self._wtRanEventsPanel:SelfHitTestInvisible()
        self._wtRankAchievePanel:SelfHitTestInvisible()
        self._wtEmptyPanel:Collapsed()
    end
    
end

function CommanderRecordPanel:SetPlayerNameAndSeasonDuration(serial)
    loginfo("CommanderRecordPanel:SetPlayerNameAndSeasonDuration",serial)
    self._wtPlayerName:SetText(Server.RoleInfoServer.nickName or "?")
    local durationData=self:GetSeasonDurationBySerial(serial)
    local durationText=StringUtil.Key2StrFormat(Module.Tournament.Config.Loc.SeasonDurationCommanderRecord, 
    {serial=serial,startYear=durationData.startYear or "?",startMonth=durationData.startMonth or "?",endYear=durationData.endYear or "?",endMonth=durationData.endMonth or "?"})
    self._wtSeasonDurationText:SetText(durationText)
end

function CommanderRecordPanel:SetRankDetail(serial)
    loginfo("CommanderRecordPanel:SetRankDetail",serial)
    if not Server.TournamentServer:GetCommanderHasAttended(serial) then
        self:SwitchEmptyPanel(true)
    else
        self:SwitchEmptyPanel(false)
        
        local minorLevel=Server.TournamentServer:GetCommanderMinorLevel(serial)
        local minorLevelData=Module.Tournament:GetCommanderRankDataByMinor(minorLevel,serial)
        local score=Server.TournamentServer:GetCommanderRankScore(serial)
        self._wtRankWidget:SetCommanderIconByScore(score,serial)
        self._wtLevelName:SetText(minorLevelData and minorLevelData.Name or "?")

        self:SetRankStats(serial)--赛季数据
        self:SetRankEvents(serial)--赛季事件
        self:SetRankAchievements(serial)--赛季成就

    end
        
end

function CommanderRecordPanel:GetSeasonDurationBySerial(serial)
    loginfo("CommanderRecordPanel:GetSeasonDurationBySerial",serial)
    local seasonConfig=Module.Tournament:GetSeasonConfigBySerial(serial)
    local uiStartTime=seasonConfig and seasonConfig.uiStartTime
    local uiEndTime=seasonConfig and seasonConfig.uiEndTime
    local startTimeList={}
    for time in string.gmatch(tostring(uiStartTime or ""), "%d+") do
        table.insert(startTimeList,time)
    end
    local endTimeList={}
    for time in string.gmatch(tostring(uiEndTime or ""), "%d+") do
        table.insert(endTimeList,time)
    end
    return {startYear=startTimeList[1],startMonth=startTimeList[2],endYear=endTimeList[1],endMonth=endTimeList[2]}
end

function CommanderRecordPanel:SetRankStats(serial)
    loginfo("CommanderRecordPanel:SetRankStats",serial)
    local abilityData=Server.TournamentServer:GetCommanderAbilityData(serial)
    if abilityData then
        local totalGameCount=abilityData.TotalFight
        local totalGameCountAsCommander=abilityData.TotalFightAsCommander
        local winRateAsCommander=abilityData.TotalFightAsCommander~=0 and abilityData.TotalWinAsCommander/abilityData.TotalFightAsCommander or 0
        local vehicleKillPerMin=abilityData.TotalVehicleUseTime~=0 and abilityData.TotalVehicleKill/(abilityData.TotalVehicleUseTime/60) or 0
        local infantryKillPerMin=abilityData.TotalGameTime~=abilityData.TotalVehicleUseTime and (abilityData.TotalKill-abilityData.TotalVehicleKill)/((abilityData.TotalGameTime-abilityData.TotalVehicleUseTime)/60) or 0
        local rescuePerMin=abilityData.TotalGameTime~=abilityData.TotalVehicleUseTime and abilityData.TotalHelp/((abilityData.TotalGameTime-abilityData.TotalVehicleUseTime)/60) or 0
        local titleRankCountAsCommander=Module.Tournament:GetCommanderWordParamByKey("SeasonRankCountAsCommande") or "?"
        local titleWinRateAsCommander=Module.Tournament:GetCommanderWordParamByKey("WinRateAsCommander") or "?"
        local titleVehicleKillPerMin=Module.Tournament:GetCommanderWordParamByKey("VehicleKillPerMin") or "?"
        local titleInfantryKillPerMin=Module.Tournament:GetCommanderWordParamByKey("InfantryKillPerMin") or "?"
        local titleRescuePerMin=Module.Tournament:GetCommanderWordParamByKey("RescuePerMin") or "?"

        self._wtRankDataItemGameCount:InitAbilityData({title=Module.Tournament.Config.Loc.SeasonRankCount,value=totalGameCount})
        self._wtRankDataItemCommanderCount:InitAbilityData({title=titleRankCountAsCommander,value=totalGameCountAsCommander})
        self._wtRankDataItemCommanderWinRate:InitAbilityData({title=titleWinRateAsCommander,value=string.format("%.0f%%",winRateAsCommander*100)})
        self._wtRankDataItemVehicleKill:InitAbilityData({title=titleVehicleKillPerMin,value=string.format("%.1f",vehicleKillPerMin)})
        self._wtRankDataItemInfantryKill:InitAbilityData({title=titleInfantryKillPerMin,value=string.format("%.1f",infantryKillPerMin)})
        self._wtRankDataItemRescue:InitAbilityData({title=titleRescuePerMin,value=string.format("%.1f",rescuePerMin)})
    end
    
end

function CommanderRecordPanel:SetRankEvents(serial)
    loginfo("CommanderRecordPanel:SetRankEvents",serial)
    Facade.UIManager:RemoveSubUIByParent(self,self._wtRankEventsBox)
    for k,v in pairs(Server.TournamentServer:GetCommanderRankEvents(serial) or {}) do
        local weakIns=Facade.UIManager:AddSubUI(self,UIName2ID.CommanderRecordPanelEventsItem,self._wtRankEventsBox)
        local uiIns=getfromweak(weakIns)
        if uiIns then
            uiIns:InitRankEvent(v,serial)
        end
    end
end

function CommanderRecordPanel:SetRankAchievements(serial)
    loginfo("CommanderRecordPanel:SetRankAchievements",serial)
    local titleMap=Server.TournamentServer:GetCommanderTitleCountMap(serial)
    if titleMap then
        local commanderCount=titleMap[EOutStandingContributionType.EOutStandingContributionType_Commander] or 0
        local killerCount=titleMap[EOutStandingContributionType.EOutStandingContributionType_Kill] or 0
        local strongholdCount=titleMap[EOutStandingContributionType.EOutStandingContributionType_Capture] or 0
        local pilotCount=titleMap[EOutStandingContributionType.EOutStandingContributionType_Piot] or 0
        local tacticCount=titleMap[EOutStandingContributionType.EOutStandingContributionType_Tactical] or 0
        local doctorCount=titleMap[EOutStandingContributionType.EOutStandingContributionType_Doctor] or 0
        local panzerCount=titleMap[EOutStandingContributionType.EOutStandingContributionType_Armor] or 0
        local deVehicleCount=titleMap[EOutStandingContributionType.EOutStandingContributionType_VehicleKiller] or 0
        local commanderTitle=Module.Tournament:GetCommanderWordParamByKey("Achievecommander") or "?"
        local killerTitle=Module.Tournament:GetCommanderWordParamByKey("AchieveKiller") or "?"
        local strongholdTitle=Module.Tournament:GetCommanderWordParamByKey("AchieveStronghold") or "?"
        local pilotTitle=Module.Tournament:GetCommanderWordParamByKey("AchievePilot") or "?"
        local tacticTitle=Module.Tournament:GetCommanderWordParamByKey("AchieveTactic") or "?"
        local doctorTitle=Module.Tournament:GetCommanderWordParamByKey("AchieveDoctor") or "?"
        local panzerTitle=Module.Tournament:GetCommanderWordParamByKey("AchievePanzer") or "?"
        local devehicleTitle=Module.Tournament:GetCommanderWordParamByKey("AchieveDevehicle") or "?"

        local AchieveIconMap=Module.Tournament.Config.AchieveIconMap
        local ConfigLoc=Module.Tournament.Config.Loc
        self._wtRankAchieveItemCommander:SetAchieveData({icon=AchieveIconMap.Commander,name=commanderTitle,count=commanderCount})
        self._wtRankAchieveItemKiller:SetAchieveData({icon=AchieveIconMap.Killer,name=killerTitle,count=killerCount})
        self._wtRankAchieveItemStronghold:SetAchieveData({icon=AchieveIconMap.Stronghold,name=strongholdTitle,count=strongholdCount})
        self._wtRankAchieveItemPilot:SetAchieveData({icon=AchieveIconMap.Pilot,name=pilotTitle,count=pilotCount})
        self._wtRankAchieveItemTactic:SetAchieveData({icon=AchieveIconMap.Tactic,name=tacticTitle,count=tacticCount})
        self._wtRankAchieveItemDoctor:SetAchieveData({icon=AchieveIconMap.Doctor,name=doctorTitle,count=doctorCount})
        self._wtRankAchieveItemPanzer:SetAchieveData({icon=AchieveIconMap.Panzer,name=panzerTitle,count=panzerCount})
        self._wtRankAchieveItemDeVehicle:SetAchieveData({icon=AchieveIconMap.DeVehicle,name=devehicleTitle,count=deVehicleCount})

    end
end

function CommanderRecordPanel:InitRankYearList()
    loginfo("CommanderRecordPanel:InitRankYearList")
    self._mainSubTabGroupDataList={}
    
    local beginSerial=Server.TournamentServer:GetBeginSerial()
    local curSerial=Server.TournamentServer:GetCurSerial()
    local iteratedYears={}
    local mainSubTabGroupData={}
    if IsBuildRegionGlobal() or IsBuildRegionGA() then
        beginSerial=2
    else
        beginSerial=1
    end    
    for i=beginSerial,curSerial do
        local serialConfig=Module.Tournament:GetSeasonConfigBySerial(i)
        if serialConfig then
            local timeStr=tostring(serialConfig.StartTime or "2000")
            local year= tonumber(timeStr:match("%d+"))
            if not table.contains(iteratedYears,year) then
                table.insert(iteratedYears,year)
                mainSubTabGroupData={}
                mainSubTabGroupData.mainTabText=string.format(Module.Ranking.Config.Loc.RankYearTitle,year)
                mainSubTabGroupData.subTabDataList={}
                table.insert(self._mainSubTabGroupDataList,mainSubTabGroupData)
            end
            table.insert(mainSubTabGroupData.subTabDataList,{subTabText=serialConfig.Name or "?",serial=i})
            
        end
    end    
            
    logtable(self._mainSubTabGroupDataList,true)
    self._wtRankYearList:FreshMainTabByDataList(self._mainSubTabGroupDataList,CreateCallBack(self._OnProcessSubTab, self))--赛年列表
    self._wtRankYearList:RefreshTabItem()
    self._wtRankYearList:SetIndexChangeEvent(self.OnRankTabIdxChanged, self)
    self._defaultMainTabIdx=self._mainSubTabGroupDataList and #self._mainSubTabGroupDataList
    local curMainSubTabGroupData=self._mainSubTabGroupDataList[self._defaultMainTabIdx]
    self._defaultSubTabIdx=curMainSubTabGroupData and curMainSubTabGroupData.subTabDataList and #curMainSubTabGroupData.subTabDataList
    self._wtRankYearList:SetTabIndex(self._defaultMainTabIdx or 1,self._defaultSubTabIdx or 1)--选中当前赛年
    self._RefreshTabYearListDelayHandle=Timer.DelayCall(0.1,function(self)--delay刷新一下tab，否则多语言环境下会出现间距过大的问题
        self._wtRankYearList:RefreshTabItem()
    end,self)
end

--下标从1开始
function CommanderRecordPanel:OnRankTabIdxChanged(mainTabIdx, subTabIdx)
    loginfo("CommanderRecordPanel:OnRankSeasonTabIdxChanged","mainTabIdx",mainTabIdx,"subTabIdx",subTabIdx)
    local mainSubTabGroupData=self._mainSubTabGroupDataList and self._mainSubTabGroupDataList[mainTabIdx]
    local subTabDataList=mainSubTabGroupData and mainSubTabGroupData.subTabDataList
    local subTabData=subTabDataList and subTabDataList[subTabIdx]
    self._selectedSerial=subTabData and subTabData.serial
    self:SwitchEmptyPanel(true)
    self:SetPlayerNameAndSeasonDuration(self._selectedSerial)
    if not Server.TournamentServer:GetSeasonRankInfo(self._selectedSerial) then
        Server.TournamentServer:ReqSeasonRankInfo(self._selectedSerial,CreateCallBack(self.SetRankDetail,self,self._selectedSerial))
    else
        self:SetRankDetail(self._selectedSerial)
    end

end

function CommanderRecordPanel:_OnProcessSubTab(mainIndex,subIndex,subTab)
    loginfo("CommanderRecordPanel:_OnProcessSubTab",mainIndex,subIndex)
    if mainIndex==self._defaultMainTabIdx and subIndex==self._defaultSubTabIdx and not self._isFocusedSubTabOnce then
        if subTab then
            self._focusDefaultSubTabDelayHandle=Timer.DelayCall(0.5,function(self,subTabWeakIns)
                local subTabIns=getfromweak(subTabWeakIns)
                if subTabIns then
                    self._isFocusedSubTabOnce=true
                    subTabIns:SetCppValue("bIsFocusable", true)
                    WidgetUtil.SetUserFocusToWidget(subTabIns,false)
                else
                    logerror("CommanderRecordPanel:_OnProcessSubTab, subTabIns was destroied!!!")
                end
                
            end,self,makeweak(subTab))
        end
    end
end

return CommanderRecordPanel    
