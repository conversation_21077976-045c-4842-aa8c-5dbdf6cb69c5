----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMHero)
----- LOG FUNCTION AUTO GENERATE END -----------

local SkillHelperTool = require "DFM.StandaloneLua.BusinessTool.SkillHelperTool"
local HeroDataTable = Facade.TableManager:GetTable("Hero/HeroData")
local MediaResTable = Facade.TableManager:GetTable("MediaResTable")--视频资源表
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"

---@class HeroSkillVideoButton1:UIWidgetBase
local HeroSkillVideoButton1 = ui("HeroSkillVideoButton1")

function HeroSkillVideoButton1:Ctor()
    self._wtBgImg = self:Wnd("DFImage", UIImage)
    self._wtImageIcon = self:Wnd("DFImage_53", UIImage)
    self._wtSkillName = self:Wnd("DFTextBlock_30", UITextBlock)
    self._wtSkillButton = self:Wnd("DFButton_0", UIButton)
    self._wtSkillButton:Event("OnClicked", self._OnClicked, self)
    self._wtSkillButton:Event("OnHovered", self._OnHovered, self)
    self._wtCompSelected = self:Wnd("WBP_SlotCompSelected", UIWidgetBase)
    self._wtCompSelected:Collapsed()
    self._wtCommonHover = self:Wnd("WBP_CommonHover", UIWidgetBase)
    self._wtCommonHover:Collapsed()
end

function HeroSkillVideoButton1:OnInitExtraData()
	
end

function HeroSkillVideoButton1:InitData(data)
  self._widgetIndex = data.widgetIndex
  self._fOnClicked = data.fOnClicked
  self._skillId = data.skillId
  self._heroId = data.heroId
  self:_RefreshView()

end

function HeroSkillVideoButton1:OnShowBegin()
    self:_RefreshView()
end

function HeroSkillVideoButton1:_RefreshView()
    local heroInfo = HeroDataTable[tostring(self._heroId)]
    -- local nameColor
    -- if heroInfo then
    --     nameColor = Module.Hero.Config.ExpertNameColorMapping[heroInfo.ArmedForceId]
    -- end
    -- if nameColor then
    --     self._wtSkillName:SetColorAndOpacity(nameColor)
    -- end
    
    local skillVideoDataRow = self._skillId and MediaResTable[self._skillId]
    if skillVideoDataRow then
        self._wtBgImg:AsyncSetImagePath(FLuaHelper.SoftObjectPtrToString(skillVideoDataRow.MediaResList[1].MediaTexRes) or "")
    end
    self._wtImageIcon:AsyncSetImagePath(SkillHelperTool.GetSkillIconById(self._skillId))
    self._wtSkillName:SetText(SkillHelperTool.GetSkillNameById(self._skillId))

    
end

function HeroSkillVideoButton1:_OnClicked()
    if self._fOnClicked then
        self._fOnClicked(self._widgetIndex)
    end
end

function HeroSkillVideoButton1:_OnHovered()
    if WidgetUtil.IsGamepad() then
        if self._fOnClicked then
            self._fOnClicked(self._widgetIndex)
        end
    end
end

function HeroSkillVideoButton1:SetSelected(inSelected)
    if inSelected then
        self._wtCompSelected:Visible()
    else
        self._wtCompSelected:Collapsed()
    end
end

return HeroSkillVideoButton1