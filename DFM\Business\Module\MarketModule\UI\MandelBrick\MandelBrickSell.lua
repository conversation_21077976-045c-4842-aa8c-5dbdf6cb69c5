----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMMarket)
----- LOG FUNCTION AUTO GENERATE END -----------



local MandelBrickSell = ui("MandelBrickSell")
local IVShopItemTemplate = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.IVShopItemTemplate"
local MarketConfig = require "DFM.Business.Module.MarketModule.MarketConfig"
local MarketLogic = require "DFM.Business.Module.MarketModule.Logic.MarketLogic"
local CommonPopWindows = require "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindows"
local ShopHelperTool = require "DFM.StandaloneLua.BusinessTool.ShopHelperTool"
local AuctionBarChart = require "DFM.Business.Module.AuctionModule.UI.Buy.AuctionBarChart"
local AuctionEstimateDetails = require "DFM.Business.Module.AuctionModule.UI.Sell.AuctionEstimateDetails"
local BARCHARTVISIBLECOUNT = 5
local SORTMODE = {
    ASC = 0,
    DESC = 1,
}

--- BEGIN MODIFICATION @VIRTUOS : UI Navigation 
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- 输入设备相关
local UGPInputDelegates = import "GPInputDelegates"
local EGPInputType = import "EGPInputType"
local UGPInputHelper = import "GPInputHelper"
--- END MODIFICATION

function MandelBrickSell:Ctor()
    self._itemId = nil
    self._saleInfo = nil
    self._curSellPrice = nil
    self._curSellNum = nil
    self._curSellDays = nil
    self._maxSellPrice = 0
    self._minSellPrice = 0
    self._sortMode = SORTMODE.ASC
    self._wtItemView = self:Wnd("WBP_ShopItemTemplate", IVShopItemTemplate)
    self._wtCommonPopWinV2 = self:Wnd("WBP_CommonPopWindowsV2", CommonPopWindows)
    local fCallbackIns = CreateCallBack(self.OnCloseBtnClicked,self)
    self._wtCommonPopWinV2:BindCloseCallBack(fCallbackIns)
    local fCloseBeginCallbackIns = CreateCallBack(self.OnCloseBeginBtnClicked,self)
    self._wtCommonPopWinV2:BindCloseBeginCallBack(fCloseBeginCallbackIns)
    self.dfCommonButtons = self._wtCommonPopWinV2:SetConfirmBtnType(CommonPopWindows.EConfirmBtnType.CenterConfirm, {
        {btnText = Module.Market.Config.Loc.SellBtnText, fClickCallback = self._OnConfirmBtnClicked, caller = self}
    })

    if self.dfCommonButtons then
        self._wtConfirmBtn = self.dfCommonButtons[CommonPopWindows.EHandleBtnType.Confirm]
        self._wtConfirmBtn:Event("OnDeClicked", self._OnConfirmBtnClicked, self)
    end
    -- 柱状图
    self._wtBarChart = self:Wnd("WBP_Auction_BarChart", AuctionBarChart)
    self._wtSalesPanel = self:Wnd("ItemListPanel", UIWidgetBase)
    self._wtEmptyBgSlot = self:Wnd("EmptySlot", UIWidgetBase)
    self._wtDataProportionDes = self:Wnd("DFTextBlock_107", UITextBlock)
    -- 出售数量滑条
    self._wtSellNumTextBlock = self:Wnd("TextBlock_2", UITextBlock)
    self._wtOccupiedSlotTextBlock = self:Wnd("TextBlock_3", UITextBlock)
    self._wtSellNumSelector = self:Wnd("WBP_AddDecSlider_1", DFCommonAddDecSlider)
    if self._wtSellNumSelector then
        self._wtSellNumSelector:Event("OnAddDecSliderCurNumChanged", self._OnSellNumChanged, self)
    end
    -- 挂售天数滑条
    self._wtDaysOnSaleTextBlock = self:Wnd("TextBlock_1", UITextBlock)
    self._wtDaysOnSaleSelector = self:Wnd("WBP_AddDecSlider", DFCommonAddDecSlider)
    if self._wtDaysOnSaleSelector then
        self._wtDaysOnSaleSelector:Event("OnAddDecSliderCurNumChanged", self._OnDaysOnSaleChanged, self)
    end
    -- 价格输入框
    self._wtGuidePriceTextBlock = self:Wnd("DFRichTextBlock", UITextBlock)
    self._wtDFCommonAddDecInputBoxFactor = self:Wnd("WBP_CommonAddDecPrice_139", DFCommonAddDecInputBox)
    self._wtDFCommonAddDecInputBoxFactor:Event("OnAddDecInputBoxCurNumChanged", self._OnAddDecInputBoxFactorCurNumChanged, self)
    self._wtDFCommonAddDecInputBoxFactor:Event("OnAddDecInputBoxCurNumCommitted", self._OnAddDecInputBoxCurNumCommitted, self)
    self._addFactor = 1.1
    self._decFactor = 0.9
    self._wtDFCommonAddDecInputBoxFactor:BindCustomAddOnClicked(self.OnCustomFactorAddClicked, self)
    self._wtDFCommonAddDecInputBoxFactor:BindCustomDecOnClicked(self.OnCustomFactorDecClicked, self)
    -- 收益详情
    self._wtEstimateDetails = self:Wnd("12", AuctionEstimateDetails)
    self._wtTipsAnchorInstruction = UIUtil.WndTipsAnchor(self, "DFTipsAnchor_161", self._OnShowInstruction, self._OnHideInstruction)
    self._wtAveragePriceTextBlock = self:Wnd("DFRichTextBlock_67", UITextBlock)

    --- BEGIN MODIFICATION @VIRTUOS 
    self._wtEstimateDetailsButtonTips = self._wtEstimateDetails:Wnd("Button_Info", UIWidgetBase) 
    --- END MODIFICATION
end

function MandelBrickSell:OnInitExtraData(itemId)
    self._itemId = itemId
    self._subPageType = Server.MarketServer:GetMarketItemType(itemId)
end

--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
function MandelBrickSell:OnShowBegin()
    if IsHD() then
        self:_EnableGamepadFeature()

        if not self._OnNotifyInputTypeChangedHandle then
            -- 绑定多输入设备切换事件
            self._OnNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._OnInputTypeChanged, self))
        end

        -- 初始化
        local curInpurtType = UGPInputHelper.GetCurrentInputType(GetGameInstance())
        self:_OnInputTypeChanged(curInpurtType)
    end
end

function MandelBrickSell:OnHideBegin()
    if IsHD() then
        self:_DisableGamepadFeature()

        if self._OnNotifyInputTypeChangedHandle then
            UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._OnNotifyInputTypeChangedHandle)
            self._OnNotifyInputTypeChangedHandle = nil
        end
    end
end
--- END MODIFICATION

function MandelBrickSell:OnShow()

end

function MandelBrickSell:OnHide()

end

function MandelBrickSell:OnOpen()
    self:AddLuaEvent(Server.MarketServer.Events.evtMarketSaleListChanged, self.Init, self)
    self:AddLuaEvent(Module.Reward.Config.Events.evtOpenWeaponSkinGainPop,self._SetVisible, self)
    self:AddLuaEvent(Module.Reward.Config.Events.evtOpenCollectionDetailPage, self._SetVisible, self)
    self:AddLuaEvent(Server.CollectionServer.Events.evtFetchCollectionData, self.RefreshRightPanel, self)
end

function MandelBrickSell:OnClose()
    Module.ItemDetail:CloseAllPopUI()
    Facade.UIManager:ClearSubUIByParent(self, self._wtEmptyBgSlot)
    self:RemoveAllLuaEvent()
end

function MandelBrickSell:OnNavBack()
    return false
end

function MandelBrickSell:OnCloseBtnClicked()
    Module.ItemDetail:CloseItemDetailPanel()
    Facade.UIManager:CloseUI(self)
end

function MandelBrickSell:OnCloseBeginBtnClicked()

end

function MandelBrickSell:_OnAddDecInputBoxFactorCurNumChanged(curNum, changeNum)
    self.changeNum = changeNum
end

function MandelBrickSell:_OnAddDecInputBoxCurNumCommitted(curNum, changeNum)
    if self._curSellPrice ~= curNum then
        self._curSellPrice = curNum
        self:RefreshFee()
    end
    -- 通用控件初始化会触发两次commit
    self._bCommittedFromInit = setdefault(self._bCommittedFromInit, 0)
    if self._bCommittedFromInit < 2 then
        self._bCommittedFromInit = self._bCommittedFromInit + 1
        return
    end
    if self._maxSellPrice ~= 0 and self._maxSellPrice == curNum and self.changeNum == 0 then
        Module.CommonTips:ShowSimpleTip(MarketConfig.Loc.PriceCannotHigherThanThreshold)
    end
    if self._minSellPrice ~= 0 and self._minSellPrice == curNum and self.changeNum == 0 then
        Module.CommonTips:ShowSimpleTip(MarketConfig.Loc.PriceCannotLowerThanThreshold)
    end
end

function MandelBrickSell:OnCustomFactorAddClicked(curNum)
    return math.ceil(curNum * self._addFactor)
end

function MandelBrickSell:OnCustomFactorDecClicked(curNum)
    return math.floor(curNum * self._decFactor)
end

function MandelBrickSell:_OnSellNumChanged(curNum, stepNum)
    if self._itemId and self._saleInfo and self._saleInfo.max_stack_num then
        self._curSellNum = curNum
        local ownNumber = MarketLogic.GetMarketItemNumById(self._itemId)
        local remainRackCnt = Server.MarketServer:GetMaxMandelBrickRackCnt() - #Server.MarketServer:GetMandelBrickSellingList()
        local maxShelveNum = remainRackCnt * self._saleInfo.max_stack_num
        local maxSellNum = math.min(maxShelveNum, ownNumber)
        self._wtSellNumTextBlock:SetText(string.format(MarketConfig.Loc.SellNum, self._curSellNum, maxSellNum))
        self._wtOccupiedSlotTextBlock:SetText(string.format(MarketConfig.Loc.OccupiedSlot, math.ceil(self._curSellNum / self._saleInfo.max_stack_num), remainRackCnt))
        self:RefreshFee()
    end
end

function MandelBrickSell:_OnDaysOnSaleChanged(curNum, stepNum)
    self._curSellDays = curNum
    if self._saleInfo and self._saleInfo.rack_max_time then
        local rackMaxDays = math.floor(self._saleInfo.rack_max_time / 24) > 0 and math.floor(self._saleInfo.rack_max_time / 24) or 1
        self._wtDaysOnSaleTextBlock:SetText(string.format(MarketConfig.Loc.SellDays, self._curSellDays, rackMaxDays))
    end
end

function MandelBrickSell:Init(saleListInfo)
    if self._itemId then
        if saleListInfo then
            self._saleInfo = {}
            deepcopy(self._saleInfo, saleListInfo)
        end
    end
    if self._saleInfo then
        self:_SortFun()
        self:RefreshLeftPanel()
        self:RefreshRightPanel()
    end
end

function MandelBrickSell:RefreshLeftPanel()
    self:RefreshDataChartPanel()
    self:RefreshDataProportionDes()
end

function MandelBrickSell:RefreshRightPanel()
    if self._itemId and self._saleInfo and self._saleInfo.max_stack_num then
        -- 刷新itemView
        local item = ItemBase:NewIns(self._itemId)
        self._wtItemView:InitMarketItem(item, self._saleInfo)
        self._wtItemView:EnableComponent(Module.CommonWidget.Config.EIVWarehouseTempComponent.BottomLeftIconText, false)
        self._wtItemView:EnableComponent(Module.CommonWidget.Config.EIVWarehouseTempComponent.BottomRightIconText, false)
        self._wtItemView:BindClickCallback(CreateCallBack(self._OnItemViewClicked, self))
        self._wtItemView:SelfHitTestInvisible()
        -- -- 刷新购买数量输入框
        -- if self._saleInfo.cur_num then
        --     self._maxSellPrice = self._saleInfo.cur_num
        --     self._minSellPrice = self._saleInfo.cur_num > 0 and 1 or 0
        --     self._wtDFCommonAddDecInputBoxFactor:InitNum(1, 1, self._minSellPrice, self._maxSellPrice)
        --     self:RefreshFee()
        -- end
        -- 刷新出售数量滑条
        if self._wtSellNumSelector then
            local ownNumber = MarketLogic.GetMarketItemNumById(self._itemId)
            local remainRackCnt = Server.MarketServer:GetMaxMandelBrickRackCnt() - #Server.MarketServer:GetMandelBrickSellingList()
            local maxShelveNum = remainRackCnt * self._saleInfo.max_stack_num
            local maxSellNum = math.min(maxShelveNum, ownNumber)
            self._curSellNum = 1
            self._wtSellNumTextBlock:SetText(string.format(MarketConfig.Loc.SellNum, self._curSellNum, maxSellNum))
            self._wtOccupiedSlotTextBlock:SetText(string.format(MarketConfig.Loc.OccupiedSlot, self._curSellNum, maxSellNum))
            self._wtSellNumSelector:InitNum(1, 1, 1, maxSellNum)
        end

        -- 刷新挂售天数滑条
        if self._wtDaysOnSaleTextBlock and self._wtDaysOnSaleSelector and self._saleInfo.rack_min_time and self._saleInfo.rack_max_time then
            local rackMinDays = math.floor(self._saleInfo.rack_min_time / 24) > 0 and math.floor(self._saleInfo.rack_min_time / 24) or 1
            local rackMaxDays = math.floor(self._saleInfo.rack_max_time / 24) > 0 and math.floor(self._saleInfo.rack_max_time / 24) or 1
            self._curSellDays = 1
            self._wtDaysOnSaleTextBlock:SetText(string.format(MarketConfig.Loc.SellDays, self._curSellDays, rackMaxDays))
            self._wtDaysOnSaleSelector:InitNum(self._curSellDays, 1, rackMinDays, rackMaxDays)
        end
        -- 刷新挂售单价输入框
        if self._wtGuidePriceTextBlock and self._wtDFCommonAddDecInputBoxFactor and self._saleInfo.guide_price and
         self._saleInfo.price_range_begin and self._saleInfo.price_range_end and self._saleInfo.sell_currency_id then
            local currencyId = Module.Currency:ConvertCurrencyIdByItemId(self._saleInfo.sell_currency_id)
            local currencyIcon = Module.Currency:GetRichTxtImgId(currencyId)
            self._wtGuidePriceTextBlock:SetText(string.format(MarketConfig.Loc.GuidePrice, currencyIcon, MathUtil.GetNumberFormatStr(self._saleInfo.guide_price + 0.00001)))
            self._maxSellPrice = self._saleInfo.price_range_end
            self._minSellPrice = self._saleInfo.price_range_begin
            self._wtDFCommonAddDecInputBoxFactor:InitNum(self._saleInfo.guide_price, 1, self._minSellPrice, self._maxSellPrice)
        end
    end
end

function MandelBrickSell:RefreshDataChartPanel()
    if self._wtBarChart and self._saleInfo and self._saleInfo.sale_lists then
        if not table.isempty(self._saleInfo.sale_lists) and self._saleInfo.buy_currency_id then
            self._wtSalesPanel:SelfHitTestInvisible()
            if self._wtEmptyBgSlot then
                self._wtEmptyBgSlot:Collapsed()
            end
            local dataPoints = {}
            for k, v in ipairs(self._saleInfo.sale_lists) do
                if v.price and v.selling_num then
                    local dataPoint = {v.price, v.selling_num}
                    table.insert(dataPoints, dataPoint)
                end
            end
            local newDataPoints = MarketLogic.dataPointsProcessingForBarChar(dataPoints, 5)
            self._wtBarChart:SelfHitTestInvisible()
            local bShowComponentParams = {
                yellowBarParams = {bShowYellowBar = false, specialBarChartData = {}},
                greenBarParams = {bShowGreenBar = false, specialBarChartData = {}},
            }
            self._wtBarChart:InitStaticCharts(newDataPoints, self._saleInfo.buy_currency_id, BARCHARTVISIBLECOUNT, bShowComponentParams, nil, true)
        else
            self._wtSalesPanel:Collapsed()
            if self._wtEmptyBgSlot then
                Facade.UIManager:RemoveSubUIByParent(self, self._wtEmptyBgSlot)
                self._wtEmptyBgSlot:SelfHitTestInvisible()
                local weakUIIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.CommonEmptyContent, self._wtEmptyBgSlot)
                local emptyBg = getfromweak(weakUIIns)
                if emptyBg then
                    emptyBg:BP_SetText(Module.Market.Config.Loc.NoSaleInMarket)
                    emptyBg:BP_SetTypeWithParam(1)
                    emptyBg:Visible()
                end
            end
        end
    end
end

function MandelBrickSell:RefreshDataProportionDes()
    if self._wtDataProportionDes then
        if self._saleInfo and self._saleInfo.sale_lists then
            if not table.isempty(self._saleInfo.sale_lists) then
                local saleNum = 0
                for _, saleList in ipairs(self._saleInfo.sale_lists) do
                    saleNum = saleNum + saleList.selling_num
                end
                self._wtDataProportionDes:SelfHitTestInvisible()
                if self._saleInfo.cur_num and self._saleInfo.cur_num > 0 then
                    self._wtDataProportionDes:SetText(string.format(MarketConfig.Loc.DataProportionDes, string.format("%.2f", saleNum / self._saleInfo.cur_num * 100)))
                else
                    logerror("MandelBrickBuy:RefreshDataProportionDes sale_lists selling_num is not zero but saleInfo cur_num is zero")
                    self._wtDataProportionDes:SetText(string.format(MarketConfig.Loc.DataProportionDes, string.format("%.2f", 1 * 100)))
                end
            else
                self._wtDataProportionDes:Collapsed()
            end
        end
    end 
end

function MandelBrickSell:RefreshFee()
    if self._saleInfo and self._saleInfo.sell_currency_id and self._saleInfo.tax_rate then
        if self._wtAveragePriceTextBlock and self._saleInfo.average_price and self._saleInfo.buy_currency_id then
            local currencyId = Module.Currency:ConvertCurrencyIdByItemId(self._saleInfo.buy_currency_id)
            local currencyIcon = Module.Currency:GetRichTxtImgId(currencyId)
            self._wtAveragePriceTextBlock:SetText(string.format(Module.Market.Config.Loc.CurrencyNum, 52, 52, currencyIcon,
            self._saleInfo.average_price and MathUtil.GetNumberFormatStr(self._saleInfo.average_price + 0.00001) or "--"))
            self._wtAveragePriceTextBlock:SelfHitTestInvisible()
        end
        if self._curSellNum and self._curSellPrice then
            if self._wtEstimateDetails then
                local totalPrice = math.ceil(self._curSellNum * self._curSellPrice)
                local taxFee = math.ceil(totalPrice * tonumber(string.format("%.2f", self._saleInfo.tax_rate)))
                self._wtEstimateDetails:SelfHitTestInvisible()
                self._wtEstimateDetails:Init(taxFee, managementFee, totalPrice, totalPrice - taxFee, self._saleInfo.sell_currency_id)
            end
        else
            if self._wtEstimateDetails then
                self._wtEstimateDetails:Collapsed()
            end      
        end
    else
        if self._wtEstimateDetails then
            self._wtEstimateDetails:Collapsed()
        end
    end
end

function MandelBrickSell:_OnItemViewClicked()
    if self._itemId then
        local item = ItemBase:NewIns(self._itemId)
        local function fOnDetailPanelLoaded(detailIns)
            -- if detailIns then
            --     detailIns:SetBlindBoxCallBack(CreateCallBack(self._SetVisible, self))
            -- end
        end
        Module.ItemDetail:OpenItemDetailPanel(item, self._wtItemView, false, false, nil, nil, fOnDetailPanelLoaded, nil, nil, nil)
    end
end

function MandelBrickSell:_OnConfirmBtnClicked()
    -- Module.Auction.Config.Events.evtAuctionRealSellAuctionClicked:Invoke() -- todo 类拍卖行，播放点击声音之类的
    if self._itemId and self._saleInfo and self._saleInfo.guide_price then
        if #Server.MarketServer:GetMandelBrickSellingList() >= Server.MarketServer:GetMaxMandelBrickRackCnt() then
            Module.CommonTips:ShowSimpleTip(MarketConfig.Loc.RemainingSlotNotEnough)
            return
        end
        local curSinglePriceRatio = math.ceil((self._curSellPrice / self._saleInfo.guide_price) * 100)
        MarketLogic.DoSellMandelBrickProcess(self._itemId, self._curSellNum, self._curSellPrice, self._curSellDays, curSinglePriceRatio)
    end
end

function MandelBrickSell:FetchSaleData()
    if self._itemId then
        Server.MarketServer:FetchSaleList(self._itemId)
    end
end

function MandelBrickSell:_SortFun()
    local function ASCSort(a, b)
        return a.price < b.price
    end
    local function DESCSort(a, b)
        return a.price > b.price
    end
    if self._saleInfo and self._saleInfo.sale_lists then
        -- 直接对server里的数据做排序影响到原始的数据，会导致每次都会根据排序后的数据再排序
        if self._sortMode == SORTMODE.ASC then
            table.insertionSort(self._saleInfo.sale_lists, ASCSort)
            -- table.sort(self._saleList, ASCSort)
        elseif self._sortMode == SORTMODE.DESC then
            table.insertionSort(self._saleInfo.sale_lists, DESCSort)
            -- table.sort(self._saleList, DESCSort)
        end
    end
end


function MandelBrickSell:_SetVisible(visible,eType)
    if not visible then
        self:Show(false)
    else
        self:Hide(true)
    end
end

function MandelBrickSell:_OnShowInstruction()
    self:_OnHideInstruction()
    if self._wtTipsAnchorInstruction then
        local contents = {}
        table.insert(contents, {
            id = UIName2ID.Assembled_CommonMessageTips_V1,
            data = {
                textContent = self._subPageType == EMarketSubPageType.MandelBrick and MarketConfig.Loc.MandelBrickSellDes or MarketConfig.Loc.MysticalSkinSellDes
            }
        })
        self._tipHandle = Module.CommonTips:ShowAssembledTips(contents, self._wtTipsAnchorInstruction)
    end
end

function MandelBrickSell:_OnHideInstruction(reason)
    if self._tipHandle then
        if self._wtTipsAnchorInstruction then
            Module.CommonTips:RemoveAssembledTips(self._tipHandle, self._wtTipsAnchorInstruction)
        end
        self._tipHandle = nil
    end
end

--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
function MandelBrickSell:_EnableGamepadFeature()
    if not IsHD() then
        return 
    end

    if not self._NavGroup then
        self._NavGroup = WidgetUtil.RegisterNavigationGroup(self._wtItemView, self, "Hittest")
        if self._NavGroup then
            self._NavGroup:AddNavWidgetToArray(self._wtItemView)
            self._NavGroup:MarkIsStackControlGroup()
        end
        if not self._NavChangedFocusItemView then
            self._NavChangedFocusItemView = self._NavGroup.OnNavGroupFocusReceivedEvent:Add(self._OnItemViewFocus, self)
        end
    end

    if not self._NavGroup1 then
        self._NavGroup1 = WidgetUtil.RegisterNavigationGroup(self._wtSellNumSelector, self, "Hittest")
        if self._NavGroup1 then
            self._NavGroup1:AddNavWidgetToArray(self._wtSellNumSelector)
        end
        if not self._NavChangedFocusSellNumSelector then
            self._NavChangedFocusSellNumSelector = self._NavGroup1.OnNavGroupFocusReceivedEvent:Add(self._OnSellNumSelectorFocus, self)
        end
    end

    if not self._NavGroup2 then
        self._NavGroup2 = WidgetUtil.RegisterNavigationGroup(self._wtDaysOnSaleSelector, self, "Hittest")
        if self._NavGroup2 then
            self._NavGroup2:AddNavWidgetToArray(self._wtDaysOnSaleSelector)
        end
        if not self._NavChangedFocusDaysSelector then
            self._NavChangedFocusDaysSelector = self._NavGroup2.OnNavGroupFocusReceivedEvent:Add(self._OnDaysSelectorFocus, self)
        end
    end

    if not self._NavGroup3 then
        self._NavGroup3 = WidgetUtil.RegisterNavigationGroup(self._wtDFCommonAddDecInputBoxFactor, self, "Hittest")
        if self._NavGroup3 then
            self._NavGroup3:AddNavWidgetToArray(self._wtDFCommonAddDecInputBoxFactor)
        end
        if not self._NavChangedFocusInputBox then
            self._NavChangedFocusInputBox = self._NavGroup3.OnNavGroupFocusReceivedEvent:Add(self._OnInputBoxFocus, self)
        end
    end

    WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup3)
    self:_InitShortcuts()
    self._ShouldShowTip = true
end

function MandelBrickSell:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end
    WidgetUtil.RemoveNavigationGroup(self)
    if self._NavGroup then
        if self._NavChangedFocusItemView then
            self._NavGroup.OnNavGroupFocusReceivedEvent:Remove(self._NavChangedFocusItemView)
            self._NavChangedFocusItemView = nil
        end
        self._NavGroup = nil
    end
    if self._NavGroup1 then
        if self._NavChangedFocusSellNumSelector then
            self._NavGroup1.OnNavGroupFocusReceivedEvent:Remove(self._NavChangedFocusSellNumSelector)
            self._NavChangedFocusSellNumSelector = nil
        end
        self._NavGroup1 = nil
    end
    if self._NavGroup2 then
        if self._NavChangedFocusDaysSelector then
            self._NavGroup2.OnNavGroupFocusReceivedEvent:Remove(self._NavChangedFocusDaysSelector)
            self._NavChangedFocusDaysSelector = nil
        end
        self._NavGroup2 = nil
    end
    if self._NavGroup3 then
        if self._NavChangedFocusInputBox then
            self._NavGroup3.OnNavGroupFocusReceivedEvent:Remove(self._NavChangedFocusInputBox)
            self._NavChangedFocusInputBox = nil
        end
        self._NavGroup3 = nil
    end
    self._focusingGroup = ni
    self:_RemoveShortcuts()
end

function MandelBrickSell:_InitShortcuts()
    -- 手柄模式下，确认为长按X键
    self._wtCommonPopWinV2:OverrideGamepadSetting("MandelBrickSellComfirm_Gamepad", nil, WidgetUtil.EUINavDynamicType.Default, true)
    self._ToggleTip = self:AddInputActionBinding("Common_ToggleTip", EInputEvent.IE_Pressed, self._ToggleTips,self, EDisplayInputActionPriority.UI_Pop)
    self._wtCommonPopWinV2:AddSummaries({"Common_ToggleTip"})
end

function MandelBrickSell:_RemoveShortcuts()
    if not IsHD() then
        return 
    end
    if self._ToggleTip then
        self:RemoveInputActionBinding(self._ToggleTip)
        self._ToggleTip = nil
    end
end

function MandelBrickSell:_ToggleTips()
    if not IsHD() then
        return 
    end
    if self._wtEstimateDetails then
        self._wtEstimateDetails:SwitchDetailPanel(self._ShouldShowTip)
        if self._ShouldShowTip then
            WidgetUtil.SetUserFocusToGameViewport()
        else
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._focusingGroup)
        end
        self._ShouldShowTip = not self._ShouldShowTip
    end
end

function MandelBrickSell:_OnInputTypeChanged(InputType)
    if InputType == EGPInputType.Gamepad then
        -- 刷新导航
        if self._navGroup then
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroup)
        end
    else
    end
end

function MandelBrickSell:_OnItemViewFocus()
    if not IsHD() then
        return 
    end
    self._focusingGroup = self._NavGroup
end

function MandelBrickSell:_OnSellNumSelectorFocus()
    if not IsHD() then
        return 
    end
    self._focusingGroup = self._NavGroup1
end

function MandelBrickSell:_OnDaysSelectorFocus()
    if not IsHD() then
        return 
    end
    self._focusingGroup = self._NavGroup2
end

function MandelBrickSell:_OnInputBoxFocus()
    if not IsHD() then
        return 
    end
    self._focusingGroup = self._NavGroup3
end

--- END MODIFICATION

return MandelBrickSell