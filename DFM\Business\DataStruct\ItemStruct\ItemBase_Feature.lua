local FeatureBase = require "DFM.Business.DataStruct.ItemStruct.FeatureBase"
local DefaultFeature = require "DFM.Business.DataStruct.ItemStruct.DefaultFeature"
local WeaponFeature = require "DFM.Business.DataStruct.ItemStruct.WeaponFeature"
local EquipmentFeature = require "DFM.Business.DataStruct.ItemStruct.EquipmentFeature"
local HealthFeature = require "DFM.Business.DataStruct.ItemStruct.HealthFeature"
local HeroFeature = require "DFM.Business.DataStruct.ItemStruct.HeroFeature"
local GadgetFeature = require "DFM.Business.DataStruct.ItemStruct.GadgetFeature"
local RewardFeature = require "DFM.Business.DataStruct.ItemStruct.RewardFeature"
local AdapterFeature = require "DFM.Business.DataStruct.ItemStruct.AdapterFeature"
local WeaponPerkFeature = require "DFM.Business.DataStruct.ItemStruct.WeaponPerkFeature"
local KeyFeature = require "DFM.Business.DataStruct.ItemStruct.KeyFeature"
local KeyBoxUnlockItemFeature = require "DFM.Business.DataStruct.ItemStruct.KeyBoxUnlockItemFeature"
local BulletFeature = require "DFM.Business.DataStruct.ItemStruct.BulletFeature"
local VehicleFeature = require "DFM.Business.DataStruct.ItemStruct.VehicleFeature"
local HeroPropsFeature = require "DFM.Business.DataStruct.ItemStruct.HeroPropsFeature"

local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"

---@class ItemBase
local ItemBase = {}

-----------------------------------------------------------------------
--region Private

function ItemBase:_InitFeatureById(id)
    local itemMainType = ItemHelperTool.GetMainTypeById(id)
    local itemSubType = ItemHelperTool.GetSubTypeById(id)
    local keyBoxUnlockItemSubType = 11

    local targetFeatureClass
    if itemMainType == EItemType.Weapon or itemMainType == EItemType.Receiver or itemMainType == EItemType.PoorWeapon or itemMainType == EItemType.WeaponSkin then
        targetFeatureClass = WeaponFeature
    elseif itemMainType == EItemType.ExtendItem and itemSubType == keyBoxUnlockItemSubType then
        targetFeatureClass = KeyBoxUnlockItemFeature
    elseif itemMainType == EItemType.Equipment 
    or (itemMainType == EItemType.ExtendItem and itemSubType ~= keyBoxUnlockItemSubType) then
        targetFeatureClass = EquipmentFeature
    elseif itemMainType == EItemType.Medicine then
    --or (itemMainType == EItemType.UsableItem and itemSubType == 1) then
        targetFeatureClass = HealthFeature
    elseif itemMainType == EItemType.Hero then
        targetFeatureClass = HeroFeature
    elseif itemMainType == EItemType.GadgetItem then
        targetFeatureClass = GadgetFeature
    elseif itemMainType == EItemType.SkillItem then
        targetFeatureClass = GadgetFeature
    elseif itemMainType == EItemType.Gift then
        targetFeatureClass = RewardFeature
    elseif itemMainType == EItemType.Adapter then
        targetFeatureClass = AdapterFeature
    elseif itemMainType == EItemType.WeaponPerk then
        targetFeatureClass = WeaponPerkFeature
    elseif itemMainType == EItemType.CollectableItem and itemSubType == ECollectableType.Key then
        targetFeatureClass = KeyFeature
    elseif itemMainType == EItemType.Bullet then
        targetFeatureClass = BulletFeature
    elseif itemMainType == EItemType.Vehicle or itemMainType == EItemType.VehicleAdapter or itemMainType == EItemType.VehicleSkin then
        targetFeatureClass = VehicleFeature
    elseif itemMainType == EItemType.HeroAccessory and itemSubType == EHeroAccessroy.SoldierProp then
        targetFeatureClass = HeroPropsFeature
    else
        targetFeatureClass = DefaultFeature
    end

    self._feature = targetFeatureClass:NewIns()
    self._feature:SetParentItem(self)
    self._feature:InitFromItemId(id)

    self.bUseRenderTexture = self:_GetbUseRenderTexture(itemMainType)
end

---@param propData pb_PropInfo
function ItemBase:_InitFeatureByProp(propData)
    self._feature:InitFromProp(propData)
end

function ItemBase:_InitFeatureByCppInfo(cppInfo)
    self._feature:InitFromCppInfo(cppInfo)
end

--endregion
-----------------------------------------------------------------------

---@param featureType EFeatureType|integer
---@return FeatureBase|nil
function ItemBase:GetFeature(featureType)
    if featureType == nil then
        return self._feature
    end

    if self._feature and self._feature:GetFeatureType() == featureType then
        return self._feature
    else
        -- logwarning(string.format("Item [ID: %s] doesn't have feature of type %d", self.id, featureType))
    end
    return nil
end

-- function ItemBase:ForceSetFeature(feature)
--     self._feature = feature
-- end

function ItemBase:GetFeatureType()
    if self._feature then
        return self._feature:GetFeatureType()
    end

    return EFeatureType.None
end

function ItemBase:RefreshIsUseRenderTexture()
    self.bUseRenderTexture = self:_GetbUseRenderTexture(ItemHelperTool.GetMainTypeById(self._id))
end

function ItemBase:_GetbUseRenderTexture(itemType)
    if self._feature.IsWeapon and self._feature:IsWeapon() then
        return true
    end

    if self._feature.IsAdapter and self._feature:IsAdapter() then
        return true
    end

    if self._feature.IsPendant and self._feature:IsPendant() then
        return true
    end

    if itemType == EItemType.WeaponSkin then
        return true
    end

    local heroPropsFeature = self:GetFeature(EFeatureType.HeroProps)
    if heroPropsFeature and heroPropsFeature:IsNeedRHI() then
        return true
    end
    -- 载具动态图标
    if itemType == EItemType.Vehicle or itemType == EItemType.VehicleAdapter or itemType == EItemType.VehicleSkin then
        return true
    end


    return false
end

return ItemBase