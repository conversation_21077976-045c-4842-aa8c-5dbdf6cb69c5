----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMWeaponAssembly)
----- LOG FUNCTION AUTO GENERATE END -----------

--点赞
---@class ActivityTaraItem2 : LuaUIBaseView
local ActivityTaraItem2 = ui("ActivityTaraItem2")
local ActivityLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"
local Config = Module.Activity.Config
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPInputType = import"EGPInputType"

function ActivityTaraItem2:Ctor()
    self._wtFImg = self:Wnd("DFImage_79"     , UIImage)
    self._wtFNum = self:Wnd("DFTextBlock_113", UITextBlock)
    self._wtPanl = self:Wnd("DFCanvasPanel_85", UIWidgetBase)
    self._wtGold = self:Wnd("DFImage_155"    , UIImage)
    self._wtGNum = self:Wnd("DFTextBlock_64" , UITextBlock)
    self._wtFBox = UIUtil.WndWaterfallScrollBox(self, "DFScrollGridBox_44", self._OnWaterfallCount, self._OnWaterfallWidget)
    self._wtFBtn = self:Wnd("DFButton_3"     , UIButton)--XIAO
    self._wtGBtn = self:Wnd("DFButton_58"    , UIButton)--DA
    self._wtFBtn:Event("OnClicked", self._OnClicked, self)
    self._wtFBtn:Event("OnHovered", self._OnHovered, self)
    self._wtGBtn:Event("OnClicked", self._OnMaxClicked, self)
    self._wtGBtn:Event("OnHovered", self._OnMaxHovered, self)
end

--是否手柄
function ActivityTaraItem2:IsGamepad()
    return IsHD() and WidgetUtil.IsGamepad()
end

function ActivityTaraItem2:_OnMaxHovered()
    if self:IsGamepad() then
        local isbool = self:_IsLiked()
        Config.evtAddTaraHandleAdaptation:Invoke(ETaraHandleType.Comment, self, nil, nil, self._index, isbool)
    end
end

function ActivityTaraItem2:_OnMaxClicked()
    if self:IsGamepad() then
        self:_OnClicked()
    end
end

function ActivityTaraItem2:_OnHovered()
end

function ActivityTaraItem2:_OnClicked()
    if self._isBool == true then
        return
    end
    --请求点赞
    if self._activityID and self._data then
        self._isGoldCoin = true
        Server.ActivityServer:SendTaraLikeReq(self._activityID, self._data.id)
    end
end

function ActivityTaraItem2:_OnPlayGoldCoinAnim()
    self:_OnStopGoldCoinAnim()
    local goldCoin = self._goldCoin
    if goldCoin and self._isGoldCoin then
        self._wtPanl:HitTestInvisible()
        self._isGoldCoin = false
        local itemData = ActivityLogic.GetWeaponSkinItem({id = goldCoin.id})
        if itemData then
            self._wtGold:AsyncSetImagePath(itemData.itemIconPath or "", true)
            self._wtGNum:SetText(ActivityLogic.GetNumStr(goldCoin.num or 0) or "")
            local animName = self.WBP_PatrolAsala_LikeItem_Q1ZNK_pick
            if animName then
                self:PlayAnimation(animName, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
            end
        end
    end
end

function ActivityTaraItem2:_OnStopGoldCoinAnim()
    self._wtPanl:Collapsed()
end

function ActivityTaraItem2:InitData(activityID, data, index, list, count)
    self._activityID = activityID
    self._data  = data
    self._index = index
    self._list  = list
    if data then
        self._goldCoin = {
            id  = data.goldId,
            num = data.goldNum,
        }
        self._likes = data.likes
        if self._likes then
            self._wtFNum:SetText(#self._likes)
        end
        local isBool = false
        for _, id in ipairs(self._likes or {}) do
            if id == data.playerId then
                isBool = true
                break
            end
        end
        self._isBool = isBool
        if self.SetColor then
            self:SetColor(isBool and 1 or 0)
        end
        self._wtFBox:SetVisibility(ESlateVisibility.HitTestSelfOnly)
        self._wtFBox:RefreshAllItems()
        --播放金币动效(放弃动效)
        self:_OnStopGoldCoinAnim()
        Timer.CancelDelay(self._timerIns)
    end
end

function ActivityTaraItem2:_IsLiked()
    local data = self._data
    if data then
        for _, id in ipairs(data.likes or {}) do
            if id == data.playerId then
                return false
            end
        end
    end
    return true
end

function ActivityTaraItem2:OnShowBegin()
    self:_AddEventListener()
end

function ActivityTaraItem2:OnHideBegin()
    self:RemoveAllLuaEvent()
end

function ActivityTaraItem2:OnClose()
    self._timerIns = nil
end

function ActivityTaraItem2:SetIndex(index)
    self._index = index
end

function ActivityTaraItem2:SetIsPlayAnim(isAnim)
    self._isAnim = isAnim
end

function ActivityTaraItem2:_AddEventListener()
    self:AddLuaEvent(Config.evtAddTaraHandleAdaptation, self._OnAddTaraHandleAdaptation, self)
end

--发起器
function ActivityTaraItem2:_OnAddTaraHandleAdaptation(handleType, ...)
    if handleType == ETaraHandleType.Anim then
        self:_OnPlayInAnim(...)
    end
end

function ActivityTaraItem2:_OnPlayInAnim(time, count)
    local index = self._index
    if index and time and count and index <= count then

        local func = function()
            Facade.SoundManager:PlayUIAudioEvent("UI_FriendCircl_Info_Popup")
            self:Visible()
            self:PlayAnimation(self.WBP_PatrolAsala_LikeItem_Q1ZNK_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
            self:_PublishAnimation()
        end
        self:Hidden()
        self._timerIns = Timer.DelayCall(time * index, func, self)
    end
end

function ActivityTaraItem2:_PublishAnimation()
    local func = function()
        Config.evtAddTaraHandleAdaptation:Invoke(ETaraHandleType.Anim2, 0.15)
    end
    func()
end

function ActivityTaraItem2:_GetHeroHeadUrl(heroId)
    local data = self._data
    if data and data.heroHeads then
        local head = data.heroHeads[heroId]
        if head then
            return head.image
        end
    end
    return nil
end

function ActivityTaraItem2:_OnWaterfallCount()
    if self._likes then
        return #self._likes
    end
    return 0
end

function ActivityTaraItem2:_OnWaterfallWidget(position, itemWidget)
    local index = position + 1
    local data = self._data
    if self._likes and itemWidget and data then
        local heroId = self._likes[index]
        local hero = ActivityLogic.GetListData(self._list, "heroId", heroId)
        if hero.path == nil then
            hero.path = self:_GetHeroHeadUrl(heroId)
        end
        if heroId == data.playerId and hero then
            hero = {
                path = data.playerPath
            }
        end
        itemWidget:InitData(self._activityID, hero)
        itemWidget:SetIndex(index)
    end
end

return ActivityTaraItem2