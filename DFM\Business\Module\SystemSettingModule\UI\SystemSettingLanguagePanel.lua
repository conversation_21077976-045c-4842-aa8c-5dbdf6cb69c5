----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class SystemSettingLanguagePanel

local SystemSettingLanguagePanel = ui("SystemSettingLanguagePanel")
local LanguageSettingLogic = require "DFM.Business.Module.SystemSettingModule.Logic.LanguageSettingLogic"
local UGPAudioStatics = import"GPAudioStatics"
local UDFMLocalizationManager = import("DFMLocalizationManager")
local LocalizeTool = require "DFM.StandaloneLua.BusinessTool.LocalizeTool"
local EVersionType= import"EVersionType"
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"

local function log(...)
    loginfo("[SystemSettingLanguagePanel]", ...)
end

function SystemSettingLanguagePanel:Ctor()
    self._curLanguage = LanguageSettingLogic.GetCurrentCulture() -- 初始化时获取当前语言环境
    self._preLanguage = self._curLanguage -- 保存上一个选中的语言码

    local locMgr = UDFMLocalizationManager.Get(GetGameInstance())
    self._curVoiceLanguage = locMgr:GetCurrentVoiceCulture()  -- 初始化时获取当前语言环境
    self._preVoiceLanguage = self._curVoiceLanguage -- 保存上一个选中的语言码

    self._checkBoxMap = {} -- checkBoxMap，其中保存checkBox对象，以语言码为索引

    self._checkVoiceBoxMap = {}
    
    self._cultureKeyList = {} -- 保存当前语言的配置信息的Key，存在先后顺序

    self._cultureWwise = {}
    self._wwiseDisplayNameTable = {}

    self._languageLine = self:Wnd("WBP_TitleWithLine_C_0", UIWidgetBase)

    -- 国服包隐藏语言设置
    if IsBuildRegionCN() and (not IsInEditor()) then
        self._languageLine:Collapsed() -- 隐藏语言设置，保留语音设置
    else
        self:InitCultureList()
    end
    self:InitVoiceList()
end

function SystemSettingLanguagePanel:BuildCNCultureData()
    log("SystemSettingLanguagePanel:BuildCNCultureData()")
    self._cultureWwise = {}
    self._wwiseDisplayNameTable = Module.SystemSetting.Config.CNWwiseLanguageList

    for key, data in ipairs(self._wwiseDisplayNameTable) do
        local voInfo = {
            cultureWwise = data.Culture,
            downloadKey = "",
            displayName = data.DisplayName,
            SortNum = key
        }
        table.insert(self._cultureWwise, voInfo)
        self._wwiseDisplayNameTable[data.Culture] = data
    end
end

function SystemSettingLanguagePanel:InitVoiceList()
    self._voiceConfig = Facade.TableManager:GetTable("Localize/LocalizeVoiceConfig")
    for _,cultureInfo in pairs(self._voiceConfig) do
        if not self._wwiseDisplayNameTable[cultureInfo.CultureWwise] then
            if VersionUtil.IsShipping() then
                if table.contains(cultureInfo.Versions, EVersionType.Shipping) and LocalizeTool.CheckPlatform(cultureInfo.Platforms) then
                   self._wwiseDisplayNameTable[cultureInfo.CultureWwise] = cultureInfo
                end
            else
                if table.contains(cultureInfo.Versions, EVersionType.Test) and LocalizeTool.CheckPlatform(cultureInfo.Platforms) then
                    self._wwiseDisplayNameTable[cultureInfo.CultureWwise] = cultureInfo
                end
            end
        end
    end
    
    for key, value in pairs(self._wwiseDisplayNameTable) do
        local voInfo = {
            cultureWwise = key,
            downloadKey = value.DownloadKey,
            displayName = value.CultureWwiseDisplayName,
            SortNum = value.SortNum
        }
        table.insert(self._cultureWwise, voInfo)
    end
    table.sort(self._cultureWwise, function(a, b) return a.SortNum < b.SortNum end)
end

function SystemSettingLanguagePanel:InitCultureList()
    log("InitCultureList()")
    self._cultureConfig = Facade.TableManager:GetTable("Localize/LocalizeCultureConfig")
    for cultureKey, cultureInfo in pairs(self._cultureConfig) do
        if self._curVoiceLanguage == "" and cultureInfo.CultureSign == self._curLanguage then
            self._curVoiceLanguage = cultureInfo.CultureWwise
        end
        if not self._cultureKeyList[cultureKey] then
            if VersionUtil.IsShipping() then
                if table.contains(cultureInfo.Versions, EVersionType.Shipping) and LocalizeTool.CheckPlatform(cultureInfo.Platforms) then
                    table.insert(self._cultureKeyList, cultureKey)
                end
            else
                if table.contains(cultureInfo.Versions, EVersionType.Test) and LocalizeTool.CheckPlatform(cultureInfo.Platforms) then
                    table.insert(self._cultureKeyList, cultureKey)
                end
            end
        end
        -- table.insert(self._cultureKeyList, cultureKey)
    end
    -- 排序，目前是乱序
    table.sort(self._cultureKeyList, function(a, b)
        return a < b
    end)
end

function SystemSettingLanguagePanel:OnOpen()
    self:_InitScrollGridBox()
end

function SystemSettingLanguagePanel:_InitScrollGridBox()
    log("LanguagePopView:_InitScrollGridBox()")
    -- 国服隐藏语言设置，但要保留语音设置
    self._wtCultureList = UIUtil.WndScrollGridBox(self, "CultureList", self.OnGetItemCount, self.OnProcessItemWidget)
    if IsBuildRegionCN() and (not IsInEditor()) then
        self._wtCultureList:Collapsed()
    else
        self._wtCultureList:RefreshAllItems()
    end

    self._wtCultureVoiceList = UIUtil.WndScrollGridBox(self, "CultureVoiceList", self.OnGetVoiceItemCount, self.OnProcessVoiceItemWidget)
    self._wtCultureVoiceList:RefreshAllItems()
end

function SystemSettingLanguagePanel:OnGetItemCount()
    log("LanguagePopView:OnGetItemCount()", #self._cultureKeyList)
    return #self._cultureKeyList
end

function SystemSettingLanguagePanel:OnGetVoiceItemCount()
    log("LanguagePopView:OnGetVoiceItemCount()", #self._cultureWwise)
    return #self._cultureWwise
end

---@param itemWidget SystemSettingLanguageCell
function SystemSettingLanguagePanel:OnProcessItemWidget(position, itemWidget)
    log("LanguagePopView:OnProcessItemWidget()", position, itemWidget)
    local cultureInfo = self._cultureConfig[self._cultureKeyList[position + 1]]
    if cultureInfo then
        local cultureSign = cultureInfo.CultureSign -- 获取语言码
        local function OnWidgetClicked()
            self._curLanguage = cultureSign
            self:RefrshView(cultureSign, self._checkBoxMap)--刷新当前选中效果
            self:ShowConfirmWindow()
        end

        -- 替换为图片
        local displayLanguageText = string.format("<dfmrichtext type=\"img\" id=\"SetUpText_%s\"/>", cultureSign)
        if cultureSign == "zh-Hans" then
            displayLanguageText = "<dfmrichtext type=\"img\" id=\"SetUpText_zh\"/>" -- 海外主机支持简体中文
        end

        local cultureDownloadInfo = {
            cultureWwise = cultureSign,
            downloadKey = "",
            displayName = displayLanguageText
        }

        if cultureSign ~= "en" and cultureSign ~= "zh-Hans" then
            local pakCulture = string.lower(cultureSign)
            pakCulture = string.gsub(pakCulture, "-", "_")
            -- 手游需要额外下载资源
            if not IsHD() and not IsInEditor() then
                cultureDownloadInfo.downloadKey = "Loc_" .. pakCulture
            end
        end
        
        -- 下载完毕回调，提示用户
        local function onCultureResDownloadFinished()
            loginfo("Culture res download finished!", cultureSign, cultureDownloadInfo.downloadKey)
            -- 弹窗提示即可
            Module.CommonTips:ShowSimpleTip(StringUtil.Key2StrFormat(Module.CommonWidget.Config.Loc.CultureResDownloadFinished, {["CultureName"] = displayLanguageText}))
        end

        itemWidget:InitItem(cultureSign, cultureDownloadInfo, OnWidgetClicked, nil, onCultureResDownloadFinished)
        if string.lower(cultureSign) == string.lower(self._curLanguage) then
            itemWidget:SetSelected(true)
        else
            itemWidget:SetSelected(false)
        end
        self._checkBoxMap[cultureSign] = itemWidget
    end
end

function SystemSettingLanguagePanel:RefrshView(selectedCulture, checkBoxMap)
    for culture, itemWidget in pairs(checkBoxMap) do
        if string.lower(culture) == string.lower(selectedCulture) then
            itemWidget:SetSelected(true)
        else
            itemWidget:SetSelected(false)
        end
    end
    -- 可以少访问一次
    --self._checkBoxMap[selectedCulture]:SetSelected(true) -- 可以保证一定存在
end

-- 显示二次确认界面
function SystemSettingLanguagePanel:ShowConfirmWindow()
    if self._curLanguage == self._preLanguage then
        return -- 当重复点击时直接返回
    end

    -- 绑定回调，显示二次确认界面
    local fOnConfirm = CreateCallBack(function()
        loginfo("OnLanguageChange Confirm")
        self:OnLanguageChangeConfirm()
    end, self)

    local fOnCancel = CreateCallBack(function()
        loginfo("OnLanguageChange Cancel")
        self:OnLanguageChangeCancel()
    end, self)

    LanguageSettingLogic.ShowConfirmWindow(self._curLanguage, fOnConfirm, fOnCancel)
end

-- 点击确认后回调
function SystemSettingLanguagePanel:OnLanguageChangeConfirm()
    local retVal = LocalizeTool.SetCurrentCulture(self._curLanguage) -- 更改语言
    if retVal then
        -- 此时说明语言更改成功，将返回登录界面
        self._preLanguage = self._curLanguage -- 确认更改当前语言

        -- 刷新音频本地化
        local language2VoiceCulture = LocalizeTool.GetLanguageVoiceCulture()
        LocalizeTool.SetCurrentAudioCulture(language2VoiceCulture)
        self._curVoiceLanguage = language2VoiceCulture

        self:RefrshView(language2VoiceCulture, self._checkVoiceBoxMap)--刷新当前选中效果

        -- TODO:返回登录界面
        -- LanguageSettingLogic.BackToLogin()
    else
        -- 切换语言失败后需要恢复到上一个状态
        self._curLanguage = self._preLanguage
        self:RefrshView(self._curLanguage, self._checkBoxMap)
    end    
end

-- 点击取消后回调
function SystemSettingLanguagePanel:OnLanguageChangeCancel()
    self._curLanguage = self._preLanguage
    self:RefrshView(self._curLanguage, self._checkBoxMap) -- 取消时将选项回归到上一个选项
end

--语音设置

---@param itemWidget SystemSettingLanguageCell
function SystemSettingLanguagePanel:OnProcessVoiceItemWidget(position, itemWidget)
    log("LanguagePopView:OnProcessItemWidget()", position, itemWidget)
    -- 根据语音包下载状态 设置表现状态（已下载，未下载，下载中）
    local voInfo = self._cultureWwise[position + 1]
    local curCultureWwise = voInfo.cultureWwise
    local function OnWidgetClicked()
        self._curVoiceLanguage = curCultureWwise
        self._CurVoiceAudioKey = voInfo.downloadKey
        LocalizeTool.SetCurrentAudioCulture(curCultureWwise)
        self:RefrshView(curCultureWwise, self._checkVoiceBoxMap)--刷新当前选中效果
    end

    local function onDownloadingPaused()
        local confirm_func = function()
            Module.SystemSetting:CloseMainPanel()
        end
        local cancel_func = function ()
            itemWidget:OnWaitDownClicked() -- 取消就继续恢复下载
        end
        Module.IrisSafeHouse:ShowVoiceDownBreakTipWindow(confirm_func, cancel_func)
    end

    local function onVoDownloadFinished()
        Module.IrisSafeHouse:ShowVoDownFinishTipsWindow() 
        itemWidget:OnItemClicked()
    end

    itemWidget:InitItem(curCultureWwise, voInfo or "INVALID", OnWidgetClicked, onDownloadingPaused, onVoDownloadFinished)
    if curCultureWwise == self._curVoiceLanguage then
        itemWidget:SetSelected(true)
    else
        itemWidget:SetSelected(false)
    end
    self._checkVoiceBoxMap[curCultureWwise] = itemWidget
end

-- 点击取消后回调
function SystemSettingLanguagePanel:OnVoiceLanguageChangeCancel()
    self._curVoiceLanguage = self._preVoiceLanguage
    self:RefrshView(self._curVoiceLanguage, self._checkVoiceBoxMap) -- 取消时将选项回归到上一个选项
end

--
function SystemSettingLanguagePanel:OnClose()
    if self._CurVoiceAudioKey and self._CurVoiceAudioKey ~= "" then
        LogAnalysisTool.DoSendPlayerUsedGameVoiceLog(self._CurVoiceAudioKey)
    end
end

return SystemSettingLanguagePanel