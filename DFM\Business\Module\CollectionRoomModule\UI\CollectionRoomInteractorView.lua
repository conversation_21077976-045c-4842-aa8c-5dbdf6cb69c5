----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollectionRoom)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class CollectionRoomInteractorView : LuaHudBaseView
local CollectionRoomInteractorView = hud("CollectionRoomInteractorView")
local GameHUDState = import "GameHUDSate"
local LuaUIHUDBaseView = require "DFM.YxFramework.Managers.UI.Layer.HUD.LuaUIHUDBaseView"
local UDFMGameplayDelegates = import "DFMGameplayDelegates"
--- BEGIN MODIFICATION @ VIRTUOS
local HDKeyIconBox = require("DFM.Business.Module.CommonWidgetModule.UI.HD.HDKeyIconBox")
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- 输入设备相关
local UGPInputDelegates = import "GPInputDelegates"
local EGPInputType = import"EGPInputType"
local UDFMGameplayInputManager = import "DFMGameplayInputManager"
--- END MODIFICATION

function CollectionRoomInteractorView:Ctor()
    -- 进行控件绑定
    self._wtSizeBox = self:Wnd("DFSizeBox_0", UIWidgetBase)
    if IsHD() then
        self._wtSizeBox:Collapsed()
        self._wtPCInteractTip = self:Wnd("Pan1", UIWidgetBase)
        self._wtPCInteractTxt = self:Wnd("ZoomText1", UITextBlock)
        self._wtKeyIconBox = self:Wnd("WBP_CommonKeyIconBox", HDKeyIconBox)
        --- BEGIN MODIFICATION @ VIRTUOS
        -- 是否开启Gamepad交互KeyIcon
        self.bIsOpenGamepadInteractTip = false;
        UDFMGameplayDelegates.Get(GetGameInstance()):TryCallSafeHouseInteractTipsShow(false)
        --- END MODIFICATION
        self._wtPCInteractTip:Collapsed()

        self._wtPCInteractTip1 = self:Wnd("Pan1_1", UIWidgetBase)
        self._wtPCInteractTxt1 = self:Wnd("ZoomText1_1", UITextBlock)
        self._wtKeyIconBox1 = self:Wnd("WBP_CommonKeyIconBox1", HDKeyIconBox)
        UDFMGameplayDelegates.Get(GetGameInstance()):TryCallSafeHouseInteractSecondaryTipsShow(false)
        self._wtPCInteractTip1:Collapsed()
    else
        self._wtMobileInteratPanel = self:Wnd("wtMobileInteratPanel",UIWidgetBase)
        self._wtMobileInteratPanel:Collapsed()
        self._wtMobileInteractBtn = self:Wnd("wtMobileInteractBtn",UIWidgetBase)
        self._wtMobileInteractBtn:Event("OnClicked", self._StartInteract, self)
        self._wtInteractionText = self:Wnd("wtInteractionText", UITextBlock)
        self._wtIconImg = self:Wnd("wtIconImg",UIImage)
        self._wtMobileInteratPanel1 = self:Wnd("wtMobileInteratPanel_1",UIWidgetBase)
        self._wtMobileInteratPanel1:Collapsed()
        self._wtMobileInteractBtn1 = self:Wnd("wtMobileInteractBtn_1",UIWidgetBase)
        self._wtMobileInteractBtn1:Event("OnClicked", self._StartInteractSecondary, self)
        self._wtInteractionText1 = self:Wnd("wtInteractionText_1", UITextBlock)
        self._wtIconImg1 = self:Wnd("wtIconImg_1",UIImage)
    end
    --设置显隐规则
    self:AddStateToInVisibleGameHudState(GameHUDState.GHS_PlayingSubTitle)
end


function CollectionRoomInteractorView:OnOpen()
    Module.IrisSafeHouse.Field:ClearAllOperators()
    Module.IrisSafeHouse.Field:SetOperatingOperator(nil)
    self:AddLuaEvent(Module.IrisSafeHouse.Config.evtUpdateInteractor, self._UpdateOperate, self)
end

function CollectionRoomInteractorView:OnClose()
    self:RemoveAllLuaEvent()
    Module.IrisSafeHouse.Field:ClearAllOperators()
end

function CollectionRoomInteractorView:OnShow()
    if not self._OnNotifyInputTypeChangedHandle then
        self._OnNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._OnInputTypeChanged, self))
    end
    self:EnableGamepadFeature()
    -- 刷新KeyIcon
    if not hasdestroy(self._wtKeyIconBox) then
        self._wtKeyIconBox:InitAllKeyIcons()
    end
    if not hasdestroy(self._wtKeyIconBox1) then
        self._wtKeyIconBox1:InitAllKeyIcons()
    end
end

function CollectionRoomInteractorView:OnHide()
    if self._OnNotifyInputTypeChangedHandle then
        UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._OnNotifyInputTypeChangedHandle)
        self._OnNotifyInputTypeChangedHandle = nil
    end
    self:DisableGamepadFeature()
    -- if self._hActionInteract then
    --     self:RemoveInputActionBinding(self._hActionInteract)
    --     self._hActionInteract = nil
    -- end
end

function CollectionRoomInteractorView:OnAnimFinished(anim)
    if anim == self.WBP_Interact_SafeHouse_2_out then
        self._wtMobileInteratPanel:Collapsed()
    end
end

function CollectionRoomInteractorView:EnableGamepadFeature()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    self.bIsOpenGamepadInteractTip = true
    --- BEGIN MODIFICATION @ VIRTUOS
    -- 绑定Gamepad输入相关事件
    if not self._OnNotifyInputHoldingHandle then
        self._OnNotifyInputHoldingHandle = UDFMGameplayInputManager.Get(GetGameInstance()).IAHoldPercentDelegate:Add(CreateCPlusCallBack(self._OnInputHolding, self))
    end

    if not self._OnNotifyInputFinishHandle then
        self._OnNotifyInputFinishHandle = UDFMGameplayInputManager.Get(GetGameInstance()).IAHoldFinishedDelegate:Add(CreateCPlusCallBack(self._OnInputHoldFinished, self))
    end

    if not self._OnNotifyInputCancelled then
        self._OnNotifyInputCancelled = UDFMGameplayInputManager.Get(GetGameInstance()).IAHoldCancelledDelegate:Add(CreateCPlusCallBack(self._OnInputHoldFinished, self))
    end
    --- END MODIFICATION
    if not hasdestroy(self._wtKeyIconBox) then
        -- self._wtKeyIconBox:BP_ShowHoldProgressBarTips(self.bIsOpenGamepadInteractTip)
        self._wtKeyIconBox:BP_ShowHoldProgressBarTips(false)
    end
end


function CollectionRoomInteractorView:DisableGamepadFeature()
    if not IsHD() then
        return
    end
    --- BEGIN MODIFICATION @ VIRTUOS
    -- 注销输入事件
    self.bIsOpenGamepadInteractTip = false
    if self._OnNotifyInputHoldingHandle then
        UDFMGameplayInputManager.Get(GetGameInstance()).IAHoldPercentDelegate:Remove(self._OnNotifyInputHoldingHandle)
        self._OnNotifyInputHoldingHandle = nil
    end
    if self._OnNotifyInputFinishHandle then
        UDFMGameplayInputManager.Get(GetGameInstance()).IAHoldFinishedDelegate:Remove(self._OnNotifyInputFinishHandle)
        self._OnNotifyInputFinishHandle = nil
    end
    if self._OnNotifyInputCancelled then
        UDFMGameplayInputManager.Get(GetGameInstance()).IAHoldCancelledDelegate:Remove(self._OnNotifyInputCancelled)
        self._OnNotifyInputCancelled = nil
    end
    --- END MODIFICATION
    if not hasdestroy(self._wtKeyIconBox) then
        self._wtKeyIconBox:BP_ShowHoldProgressBarTips(false)
    end
end

function CollectionRoomInteractorView:GetInjectionList()
    local list = LuaUIHUDBaseView.GetInjectionList(self)
    table.append(list, {
    })
    return list
end


function CollectionRoomInteractorView:_StartInteract()
    local operatingOperator = Module.IrisSafeHouse.Field:GetOperatingOperator()
    local operators = Module.IrisSafeHouse.Field:GetOperators()
    if isvalid(operators[operatingOperator]) then
        operatingOperator:StartInteract()
    end
end


function CollectionRoomInteractorView:_StartInteractSecondary()
    local operatingOperator = Module.IrisSafeHouse.Field:GetOperatingOperator()
    local operators = Module.IrisSafeHouse.Field:GetOperators()
    if isvalid(operators[operatingOperator]) and operatingOperator.StartSecondaryInteract then
        operatingOperator:StartSecondaryInteract()
    end
end


function CollectionRoomInteractorView:_UpdateOperate()
    local operators = Module.IrisSafeHouse.Field:GetOperators()
    local targetOperator = nil
    local minCenterDistance = nil
    local customTitle = nil
    for operator, info in pairs(operators) do
        if not hasdestroy(operator) then
            if minCenterDistance == nil then
                minCenterDistance = info.distanceToScreenCenter
                customTitle = info.customTitle
                targetOperator = operator
            else
                if info.distanceToScreenCenter < minCenterDistance then
                    minCenterDistance = info.distanceToScreenCenter
                    customTitle = info.customTitle
                    targetOperator = operator
                end
            end
        end
    end
    if targetOperator ~= nil then
        local operatingOperator = Module.IrisSafeHouse.Field:GetOperatingOperator()
        if operatingOperator ~= targetOperator then
            local operatorType = operators[targetOperator].type
            if IsHD() then
                self._wtPCInteractTip:SelfHitTestInvisible()
                UDFMGameplayDelegates.Get(GetGameInstance()).SafeHouseInteractTip:Clear()
                UDFMGameplayDelegates.Get(GetGameInstance()).SafeHouseInteractTip:Add(self._StartInteract, self)
                if WidgetUtil.IsGamepad() then
                    self.bIsOpenGamepadInteractTip = true
                    UDFMGameplayDelegates.Get(GetGameInstance()):TryCallSafeHouseInteractTipsShow(true)
                end
                if operatorType == Module.IrisSafeHouse.Config.EInteractorType.CollectionRoomExit then
                    self._wtPCInteractTxt:SetText(Module.CollectionRoom.Config.Loc.LeaveCollectionRoom)
                elseif operatorType == Module.IrisSafeHouse.Config.EInteractorType.CollectionRoomManage then
                    self._wtPCInteractTxt:SetText(Module.CollectionRoom.Config.Loc.ManageCollection)
                else
                    self._wtPCInteractTip:Collapsed()
                    UDFMGameplayDelegates.Get(GetGameInstance()).SafeHouseInteractTip:Clear()
                    if WidgetUtil.IsGamepad() then
                        self.bIsOpenGamepadInteractTip = false
                        UDFMGameplayDelegates.Get(GetGameInstance()):TryCallSafeHouseInteractTipsShow(false)
                    end
                end

                self._wtPCInteractTip1:SelfHitTestInvisible()
                UDFMGameplayDelegates.Get(GetGameInstance()).SafeHouseInteractSecondaryTip:Clear()
                UDFMGameplayDelegates.Get(GetGameInstance()).SafeHouseInteractSecondaryTip:Add(self._StartInteractSecondary, self)
                if WidgetUtil.IsGamepad() then
                    UDFMGameplayDelegates.Get(GetGameInstance()):TryCallSafeHouseInteractSecondaryTipsShow(true)
                end
                if operatorType == Module.IrisSafeHouse.Config.EInteractorType.CollectionRoomManage then
                    self._wtPCInteractTxt1:SetText(Module.CollectionRoom.Config.Loc.ManageBGM)
                else
                    self._wtPCInteractTip1:Collapsed()
                    UDFMGameplayDelegates.Get(GetGameInstance()).SafeHouseInteractSecondaryTip:Clear()
                    if WidgetUtil.IsGamepad() then
                        UDFMGameplayDelegates.Get(GetGameInstance()):TryCallSafeHouseInteractSecondaryTipsShow(false)
                    end
                end
            else
                self._wtMobileInteratPanel:SelfHitTestInvisible()
                if operatorType == Module.IrisSafeHouse.Config.EInteractorType.CollectionRoomExit then
                    self._wtIconImg:AsyncSetImagePath("PaperSprite'/Game/UI/UIAtlas/System/SaferoomHUD/BakedSprite/SaferoomHUD_Icon_0304.SaferoomHUD_Icon_0304'")
                    self._wtInteractionText:SetText(Module.CollectionRoom.Config.Loc.LeaveCollectionRoom)
                elseif operatorType == Module.IrisSafeHouse.Config.EInteractorType.CollectionRoomManage then
                    self._wtIconImg:AsyncSetImagePath("PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_CollectionRoom_Icon_0001.CommonHall_CollectionRoom_Icon_0001'")
                    self._wtInteractionText:SetText(Module.CollectionRoom.Config.Loc.ManageCollection)
                else
                    self._wtMobileInteratPanel:Collapsed()
                end
                self:StopAllAnimations()
                self:PlayAnimation(self.WBP_Interact_SafeHouse_2_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)

                self._wtMobileInteratPanel1:SelfHitTestInvisible()
                if operatorType == Module.IrisSafeHouse.Config.EInteractorType.CollectionRoomManage then
                    self._wtIconImg1:AsyncSetImagePath("PaperSprite'/Game/UI/UIAtlas/System/CollectionRoom/BakedSprite/CollectionRoom_Icon_004.CollectionRoom_Icon_004'")
                    self._wtInteractionText1:SetText(Module.CollectionRoom.Config.Loc.ManageBGM)
                else
                    self._wtMobileInteratPanel1:Collapsed()
                end
            end
        end
        Module.IrisSafeHouse.Field:SetOperatingOperator(targetOperator)
    else
        if IsHD() then
            self._wtPCInteractTip:Collapsed()
            UDFMGameplayDelegates.Get(GetGameInstance()).SafeHouseInteractTip:Clear()
            --- BEGIN MODIFICATION @ VIRTUOS
            UDFMGameplayDelegates.Get(GetGameInstance()):TryCallSafeHouseInteractTipsShow(false)
            self.bIsOpenGamepadInteractTip = false
            --- END MODIFICATION
            self._wtPCInteractTip1:Collapsed()
            UDFMGameplayDelegates.Get(GetGameInstance()).SafeHouseInteractSecondaryTip:Clear()
            UDFMGameplayDelegates.Get(GetGameInstance()):TryCallSafeHouseInteractSecondaryTipsShow(false)
        else
            self:StopAllAnimations()
            self._wtMobileInteratPanel:SelfHitTestInvisible()
            self:PlayAnimation(self.WBP_Interact_SafeHouse_2_out, 0, 1, EUMGSequencePlayMode.Forward, 1, false)

            self._wtMobileInteratPanel1:Collapsed()
        end
        Module.IrisSafeHouse.Field:SetOperatingOperator(nil)
    end
end

--- BEGIN MODIFICATION @ VIRTUOS
function CollectionRoomInteractorView:_OnInputTypeChanged(inputType)
    if IsHD() and inputType == EGPInputType.Gamepad then
        self:EnableGamepadFeature()
    else
        self:DisableGamepadFeature()
    end
    if not hasdestroy(self._wtKeyIconBox) then
        self._wtKeyIconBox:InitAllKeyIcons()
    end
    if not hasdestroy(self._wtKeyIconBox1) then
        self._wtKeyIconBox1:InitAllKeyIcons()
    end
end

function CollectionRoomInteractorView:_OnInputHolding(ActionOrAxisName, InPercent)
    if ActionOrAxisName == "Interact" and not hasdestroy(self._wtKeyIconBox) and self.bIsOpenGamepadInteractTip then
        self._wtKeyIconBox:BP_UpdateProgressBar(InPercent)
    end
end

function CollectionRoomInteractorView:_OnInputHoldFinished(ActionOrAxisName)
    if ActionOrAxisName == "Interact" and not hasdestroy(self._wtKeyIconBox) and self.bIsOpenGamepadInteractTip then
        self._wtKeyIconBox:BP_UpdateProgressBar(0)
    end
end
--- END MODIFICATION

return CollectionRoomInteractorView