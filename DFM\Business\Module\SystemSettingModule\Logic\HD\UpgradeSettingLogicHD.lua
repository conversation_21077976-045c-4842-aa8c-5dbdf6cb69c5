----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------

local UpgradeSettingLogicHD = {}
local _bhdSetting = import("ClientBHDSetting").Get()
local _baseSettingHD = import("ClientBaseSettingHD").Get()
local _sensitivitySetting = import("ClientSensitivitySettingHD").Get()
local _videoSetting = import("ClientVideoSettingHD").Get()
local _audioSetting = import("ClientAudioSettingHD").Get()
local _cameraSetting = import("ClientCameraSettingHD").Get()
local _displaySetting = import("ClientDisplaySettingHD").Get()
local _gameSetting = import("ClientGameSettingHD").Get()
local _vehicleSetting = import("ClientVehicleSettingHD").Get()
local _privacySetting = import("ClientPrivacySettingHD").Get()
local UGPGConfigUtils = import "GPGConfigUtils"

local _debugMode = isexist("DFM.Business.Module.SystemSettingModule.Logic.HD.Debug.SystemSettingDebug_DO_NOT_SUBMIT")
if _debugMode then
    UGPGConfigUtils.SetInt("ClientSettingHD", "SaveVersion", 0, "UserSystemSettingHD")
end

-- 添加升级逻辑步骤：
-- Step 1 仿照_From_Unknow_To_She2_1函数写一个升级函数
-- Step 2 将此函数加到upgradeFunctions中

local dirtySettings = {}
local bLocalUpgraded = false

function UpgradeSettingLogicHD._From_Unknow_To_She2_1(bForIni, settingData)
    logerror("not error: will upgrade in function From_Unknow_To_She2_1")
    if bForIni then -- 本地数据升级逻辑
        -- local rawData = _videoSetting.XXX
        -- rawData = rawData * 100
        -- _videoSetting.XXX = rawData
        -- -- 本地被改了的设置，要记下来
        -- dirtySettings[_videoSetting] = true
    elseif settingData then -- 远端数据升级逻辑
        -- local data = settingData["xx"]
        -- if data ~= nil then
        --     data = data * 100
        -- else
        --     data = xxx
        -- end
        -- settingData["xx"] = data
        return settingData
    end
end

function UpgradeSettingLogicHD._From_She2_1_To_She2_2(bForIni, settingData)
    logerror("not error: will upgrade in function From_She2_1_To_She2_2")
    -- do nothing
end

-- 为设置升级的函数
local upgradeFunctions = {
    UpgradeSettingLogicHD._From_Unknow_To_She2_1,
    UpgradeSettingLogicHD._From_She2_1_To_She2_2,
    -- 这是Step 2要求的位置



    -- 最后一个是报告完成的，不进行实际的升级
    function(_, settingData)
        logerror("not error: setting upgrade done")
    end
}

-- 获取当前的正确版本号
function UpgradeSettingLogicHD.GetSaveVersion()
    return #upgradeFunctions + 1
end

-- 获取从本地读取的版本号
function UpgradeSettingLogicHD.GetLocalSaveVersion()
    local currentVersion = 1
    local result = false
    result, currentVersion = UGPGConfigUtils.GetInt("ClientSettingHD", "SaveVersion", nil, "UserSystemSettingHD")
    logerror("not error: read ini version", result, currentVersion)
    if currentVersion == nil or currentVersion < 1 then
        currentVersion = 1
    end
    return currentVersion
end

-- 获取从云端解析的版本号
function UpgradeSettingLogicHD.GetCloudSettingVersion(settingData)
    if _debugMode and settingData then
        settingData["SaveVersion"] = nil
    end

    if settingData == nil then
        logerror("not error: UpgradeSettingLogicHD.Update_Cloud, nil settingData.")
        return 1
    end
    local currentVersion = settingData["SaveVersion"]
    logerror("not error: read cloud version", currentVersion)
    if currentVersion == nil or currentVersion < 1 then
        currentVersion = 1
    end
    return currentVersion
end

-- 更新ini的设置版本到最新的格式
function UpgradeSettingLogicHD.UpgradeForIni()
    if bLocalUpgraded then
        return
    end
    local currentVersion = UpgradeSettingLogicHD.GetLocalSaveVersion()

    while true do
        local UpgradeFunction = upgradeFunctions[currentVersion]
        if UpgradeFunction then
            logerror("not error: will upgrade ini to version " .. (currentVersion + 1))
            UpgradeFunction(true)
        else
            UGPGConfigUtils.SetInt("ClientSettingHD", "SaveVersion", UpgradeSettingLogicHD.GetSaveVersion(), "UserSystemSettingHD")
            for setting, _ in pairs(dirtySettings) do
                setting.SaveToSaved()
            end
            bLocalUpgraded = true
            return
        end
        currentVersion = currentVersion + 1
    end
end

-- 更新云端的设置版本到最新的格式
function UpgradeSettingLogicHD.UpgradeForCloud(settingData)
    local currentVersion = UpgradeSettingLogicHD.GetCloudSettingVersion(settingData)
    local result = settingData
    while result do
        local UpgradeFunction = upgradeFunctions[currentVersion]
        if UpgradeFunction then
            logerror("not error: will upgrade cloud to version " .. (currentVersion + 1))
            result = UpgradeFunction(false, settingData) or settingData
        else
            result["SaveVersion"] = UpgradeSettingLogicHD.GetSaveVersion()
            return result
        end
        currentVersion = currentVersion + 1
    end
end

-- 为云端数据填充版本号
function UpgradeSettingLogicHD.FillSaveVersion(settingData)
    if not bLocalUpgraded then
        UpgradeSettingLogicHD.UpgradeForIni()
    end
    if settingData then
        settingData["SaveVersion"] = UpgradeSettingLogicHD.GetSaveVersion()
    end
    return settingData
end

function UpgradeSettingLogicHD.UpgradeForCloudMeta(meta)
    return meta
end

return UpgradeSettingLogicHD
