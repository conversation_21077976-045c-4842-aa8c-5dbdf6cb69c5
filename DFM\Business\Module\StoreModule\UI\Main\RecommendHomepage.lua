----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMStore)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import "EGPUINavWidgetFocusedAction"
-- 输入设备相关
local UGPInputDelegates = import "GPInputDelegates"
local EGPInputType = import"EGPInputType"
local UGPInputHelper = import "GPInputHelper"
-- END MODIFICATION

---@class RecommendHomepage : LuaUIBaseView
local RecommendHomepage = ui("RecommendHomepage")
local StoreConfig = Module.Store.Config
local StoreLogic = require "DFM.Business.Module.StoreModule.Logic.StoreLogic"
local CommonSkipOverBg = require "DFM.Business.Module.CommonWidgetModule.UI.FullScreen.CommonSkipOverBg"
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"
local EDescendantScrollDestination = import "EDescendantScrollDestination"
local UKismetInputLibrary = import "KismetInputLibrary"
local UDFCommonMediaView = import "DFCommonMediaView"

function RecommendHomepage:Ctor()
    self.skipAndNeedSelect = false
    self.tickCountDown = 0
    self.SortedStoreHotRecommendation = {}
    self.curSelectIndex = 1
    self.isCancelTickNext = 0
    self:RefreshRecommonedData()

    self._wtWaterFallList = UIUtil.WndWaterfallScrollBox(self, "wtDFWaterfallScrollView_2", self._OnGetItemCount,
        self._OnProcessItemWidget)

    self._wtTextBundleName = self:Wnd("wtTextBundleName", UITextBlock)
    self._wtTextGoodsTip = self:Wnd("wtTextGoodsTip", UITextBlock)
    self._wtTextPrice = self:Wnd("RichTextBlock", UITextBlock)
    self._wtCountWidget = self:Wnd("wtCountDown", UIWidgetBase)
    if self._wtCountWidget ~= nil then
        self._wtTextCountDown = self._wtCountWidget:Wnd("wtTextCountDown", UITextBlock)
    end

    self._wtDiscountAndCountDownRoot = self:Wnd("DFHorizontalBox_0", UIWidgetBase)
    self._wtMoneyRoot = self:Wnd("DFHorizontalBox_2", UIWidgetBase)

    self._wtDiscountRoot = self:Wnd("DFCanvasPanel_54", UIWidgetBase)
    self._wtTextDiscount = self:Wnd("wtTextDiscount", UITextBlock)
    self._wtTextPriceOrign = self:Wnd("wtTextPriceOrign", UITextBlock)
    self._wtBGEfxSwitcher =self:Wnd("DFWidgetSwitcher_100",UIWidgetSwitcher)--背景特效切换控件

    self._wtItemBg = self:Wnd("DFImage_266", UIImage)

    self._wtVFXRoot = self:Wnd("VFX", UIWidgetBase)
    self._wtLoadingCoverRoot = self:Wnd("wt_loadingCover", UIWidgetBase)
    self._wtNameIcon = self:Wnd("wt_NameIcon", UIImage)
    self._wtNameIconRoot = self:Wnd("DFScaleBox_1", UIImage)

    self._wtVideoBtn = self:Wnd("WBP_CommonIconButton", UIButton)
    self._wtVideoBtn:Event("OnClicked", self._OnButtonVideoClick, self)

    self._wtVideoJump = self:Wnd("WBP_Common_JumpOver", CommonSkipOverBg)
    self._wtVideoJump:SetKeepSelfOnClick(true)
    -- self._wtVideoJump:BindClickEvent(self._OnButtonVideoJumpClick, self)

    -- ---@type DFMCommonMediaImage 视频播放控件
    -- self._mediaImage = self:Wnd("DFMCommonMediaImage_336", UIMediaImage)
    -- self._mediaImage:Collapsed()

    --视频专用
    ---@type CommonVideoComponent
    self._mediaComponent = self:Wnd("WBP_CommonVideoComponent", CommonVideoComponent)
    self._mediaComponent:InitComponent(false)

    --背景循环视频
    ---@type CommonVideoComponent
    self._mediaBackground = self:Wnd("WBP_CommonVideoComponent_1", CommonVideoComponent)
    self._mediaBackground:InitComponent(false)
    self._mediaBackground:HitTestInvisible()

    -- if self._wtVideoJump ~= nil then
    --     self._wtPCSkipTxt = self._wtVideoJump:Wnd("wtSkipText_PCOnly", UITextBlock)
    --     self._wtAnSkipTxt = self._wtVideoJump:Wnd("wtSkipText", UITextBlock)
    --     --初始化[跳过]文本
    --     if DFHD_LUA == 1 then
    --         if self._wtPCSkipTxt then
    --             self._wtPCSkipTxt:SetText(StoreConfig.Loc.StoreVideoSkip)
    --         end
    --     else
    --         if self._wtAnSkipTxt then
    --             self._wtAnSkipTxt:SetText(StoreConfig.Loc.StoreVideoSkipMobile)
    --         end
    --     end
    -- end

    if self._wtVideoJump ~= nil then
        self._wtVideoJump:Collapsed()
    end

    self._wtHotAreaBtn = self:Wnd("DFButton_37", UIButton)
    self._wtHotAreaBtn:Event("OnClicked", self._OnButtonHotAreaClick, self)

    if DFHD_LUA then
        --禁用滚动控件集合
        self._wtHotzone = {
            self._wtCountWidget,
            self._wtNameIcon,
            self._wtWaterFallList,
        }
        self:InjectLua()
    end

    self.enablePlayFullScreenCG = UDFCommonMediaView.EnableCGMediaPlay()
end

function RecommendHomepage:GetBundleName(str)
    local result = str:match("([^/]+)_([^/']+)'$")
    return result
end

function RecommendHomepage:OnCloseBtnClick()

end
-- UI构造时触发, 用于初始化外部传参UI数据逻辑，由UIManager触发（仅触发一次）
-- 子节点蓝图不会触发该方法
-- Module加载好子控件所需的图片后，将数据传入主UI界面
function RecommendHomepage:OnInitExtraData(param, tabId, isCollabration)    
    self.isPlayVideo = 0
    self.skipAndNeedSelect = false
    self.isCollabration = isCollabration
    local bHasChecked = false
    if param ~= nil then
        for k, v in pairs(self.SortedStoreHotRecommendation) do
            if v.TabId == param then
                bHasChecked = true
                self.curSelectIndex = k
                self.skipAndNeedSelect = true
            end
        end
    end

    if bHasChecked == false then
        self.curSelectIndex = 1 -- to default
        for idx, value in pairs(self.SortedStoreHotRecommendation) do
            local buyRecord = Server.StoreServer:GetRecommondBuyRecordByTabId(value.TabId)
            if buyRecord == nil or buyRecord.is_sold_out == false then
                self.curSelectIndex = idx
                break
            end
        end

        local curdata = self.SortedStoreHotRecommendation[self.curSelectIndex]
        if curdata ~= nil and self._wtTextBundleName ~= nil then
            self._wtTextBundleName:SetText(curdata.BannerName)
        end
    end

    self:RefreshRecommonedData(param, isCollabration)


    if self.nameToIndex == nil then
        local cnt = 1
        self.nameToIndex = {}
        for k, v in pairs(self.SortedStoreHotRecommendation) do
            if v.ImageSourceLgo then
                local bundleName = "VFX" .. v.ImageSourceLogo:match(".+_([%w_]+)'$")
                v.bName = v.ImageSourceLogo:match(".+_([%w_]+)'$")
                if UIName2ID[bundleName] then
                    Facade.UIManager:AddSubUI(self, UIName2ID[bundleName], self._wtBGEfxSwitcher)
                end
            end
        end

        for i = 1, self._wtBGEfxSwitcher:GetNumWidgets(), 1 do
            self._wtBGEfxSwitcher:SetActiveWidgetIndex(i)
            local bgWidget = self._wtBGEfxSwitcher:GetActiveWidget()
            if bgWidget and bgWidget.uiSettings then
                self.nameToIndex[bgWidget.uiSettings.BPKey:match("([^_]+)$")] = i
            end
        end
    end


    self._wtWaterFallList:RefreshAllItems()
    Timer.DelayCall(0.1, function() 
        self._wtWaterFallList:ScrollToIndexIfNotInScreen(self.curSelectIndex, true)
    end)

    self:RefreshSelectBundleText()
end

-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function RecommendHomepage:OnOpen()
    -- self._wtWaterFallList:RefreshAllItems()
    self:RefreshSelectBundleText()

    if self.bNowFirstShow ~= true then
        self.bNowFirstShow = true
        local fakePath = "Texture2D'/Game/UI/UIAtlas/Texture/SkinWordArt/SkinWordArt_05.SkinWordArt_05'"
        self._wtNameIcon:AsyncSetImagePath(fakePath, true)

        Timer.DelayCall(0.1, function()
            self:RefreshSelectBundleText()
        end)
    else
        self:RefreshSelectBundleText()
    end
end

function RecommendHomepage:OnShowBegin()
    self.tickDeltaTime = 0
    self.excuteTime = 10
    self.tickDeltaTimeCountDown = 0

    -- 如果当前选中的不是捆绑包页签，则不注册timer
    local nowTabId = Module.Store.Field:GetNowTabID()
    if nowTabId == Server.StoreServer:GetRecommendTabId() or self.isCollabration == true then
        LuaTickController:Get():RemoveTick(self)
        LuaTickController:Get():RegisterTick(self)
    else
        --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
        -- 如果不是捆绑包页签，移除掉jump over注册的导航规则
        if IsHD() and self._wtVideoJump then
            self._wtVideoJump:DisableNavConfig()
        end
        --- END MODIFICATION
    end

    self:RegisterEventListeners()

    --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() then
        self:_EnableGamepadFeature()
    end
    --- END MODIFICATION

    local curdata = self.SortedStoreHotRecommendation[self.curSelectIndex]
    if curdata ~= nil then
        local tableData = Server.StoreServer:GetHotRecommendationClientByTabID(curdata.TabId)
        if tableData then
            if tableData.BGVideoName == "None" or tableData.BGVideoName == "" or tableData.BGVideoName == nil then
                self._mediaBackground:Stop()
            else
                self._mediaBackground:Play(tableData.BGVideoName)
            end
        end
    end
end
-- UI OnOpen后、调用Show()显示时触发
-- 做表现
---@overload fun(LuaUIBaseView, OnShow)
function RecommendHomepage:OnShow()
    self.isCancelTickNext = 0
    self.skipAndNeedSelect = false

    self:ReprotBannerEvent(1)
     -- 刷新货币列表
    -- Module.CommonBar:RefreshCurrencyTypeList({ ECurrencyClientId.BindDiamond, ECurrencyClientId.UnbindDiamond }, self)
end

function RecommendHomepage:RegisterEventListeners()
    self:AddLuaEvent(StoreConfig.evtStoreRecommendItemClick, self._OnStoreRecommendItemClick, self)
    self:AddLuaEvent(StoreConfig.evtStoreRecommendItemJumpClick, self._OnStoreRecommendItemJumpClick, self)

    self:AddLuaEvent(StoreConfig.evtStoreRecommendItemHover, self._OnStoreRecommendItemHover, self)
    self:AddLuaEvent(StoreConfig.evtStoreRecommendItemUnHover, self._OnStoreRecommendItemUnHover, self)
end

function RecommendHomepage:UnRegisterEventListeners()
    self:RemoveLuaEvent(StoreConfig.evtStoreRecommendItemClick)
    self:RemoveLuaEvent(StoreConfig.evtStoreRecommendItemJumpClick)

    self:RemoveLuaEvent(StoreConfig.evtStoreRecommendItemHover)
    self:RemoveLuaEvent(StoreConfig.evtStoreRecommendItemUnHover)
end

function RecommendHomepage:RefreshRecommonedData(param, isCollabration)
    local recommondata = {}
    if isCollabration then
        LogAnalysisTool.DoSendStoreViewPageReportLog(12, 0, 0, 0)
        recommondata = Server.StoreServer:GetCollabBundleInfo()
        self:SetLocation(true)
    else
        LogAnalysisTool.DoSendStoreViewPageReportLog(1, 0, 0, 0)
        recommondata = Server.StoreServer:GetHotRecommendationDatas()
        self:SetLocation(false)
    end
    local i = 0
    self.SortedStoreHotRecommendation = {}
    for k, v in pairs(recommondata) do
        i = i + 1
        self.SortedStoreHotRecommendation[i] = v
    end

    table.sort(self.SortedStoreHotRecommendation, function(a, b)
        local limitedA = false
        local limitedB =false

        local buyRecordA = Server.StoreServer:GetRecommondBuyRecordByTabId(a.TabId)
        local buyRecordB = Server.StoreServer:GetRecommondBuyRecordByTabId(b.TabId)
        limitedA = buyRecordA ~= nil and buyRecordA.is_sold_out == true
        limitedB = buyRecordB ~= nil and buyRecordB.is_sold_out == true

        if limitedA == limitedB then
            return a.SortIndex < b.SortIndex
        else
            if limitedA then
                return false
            else
                return true
            end
        end
    end)

    local dataCount = #self.SortedStoreHotRecommendation
    -- self:SetStyle(dataCount - 1)
end

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function RecommendHomepage:OnClose()
    LuaTickController:Get():RemoveTick(self, 1)
    self:RemoveLuaEvent(Module.Reward.Config.Events.evtTogglePCInput)
    self.CurSelectMovies = nil

    local allLayers = {}
    for k, v in pairs(EUILayer) do
        table.insert(allLayers, v)
    end
    Facade.UIManager:LayersOnly(allLayers, ELayerRuleChangeReason.BusinessPending)
    self._mediaBackground:Stop()
end

function RecommendHomepage:OnHideBegin()
    --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() then
        self:_DisableGamepadFeature()
    end
    --- END MODIFICATION
end

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function RecommendHomepage:OnHide()
    -- self:_OnButtonVideoJumpClick()

    self:UnRegisterEventListeners()
    LuaTickController:Get():RemoveTick(self)

    if IsHD() and self._inputMonitor ~= nil and self._handle ~= nil then
        self._inputMonitor:RemoveDisplayActoinBingingForHandle(self._handle)
    end
    self._mediaBackground:Stop()
end

function RecommendHomepage:Update(dt)

    self.tickDeltaTimeCountDown = self.tickDeltaTimeCountDown + dt
    if self.tickCountDown == 1 and self.tickDeltaTimeCountDown >= 1 then
        self.tickDeltaTimeCountDown = 0
        --update countdown
        self.offlineSeconds = self.offlineSeconds - 1
        if self.offlineSeconds < 0 then
            self.offlineSeconds = 0
        end
        local showStr = ""
        local day, hour, min, sec = TimeUtil.GetSecondsFormatDDHHMMSS(self.offlineSeconds)
        if self.offlineSeconds > 3600 * 24 then
            showStr = TimeUtil.GetSecondsFormatDDHHMMSSString(self.offlineSeconds, "DDHH_Second")
        elseif self.offlineSeconds > 3600 then
            showStr = string.format(StoreConfig.Loc.StoreBuyLimitTimeHourTip, hour, min)
        else
            showStr = string.format(StoreConfig.Loc.StoreBuyLimitTimeMinSecTip, min, sec)
        end

        self._wtTextCountDown:SetText(showStr)
    end

    if self.isCancelTickNext <= 0 and self.isPlayVideo <= 0 then
        self.tickDeltaTime = self.tickDeltaTime + dt
        if self.tickDeltaTime > self.excuteTime then
            self.tickDeltaTime = 0
            self.curSelectIndex = self.curSelectIndex + 1

            if self.curSelectIndex > #self.SortedStoreHotRecommendation then
                self.curSelectIndex = 1
            end

            -- self._wtWaterFallList:RefreshAllItems()
            self._wtWaterFallList:ScrollToIndexIfNotInScreen(self.curSelectIndex, true)
            self:RefreshSelectBundleText()
            self:ReprotBannerEvent(1)
            self:PlayAnimation(self.WBP_Store_RecommendHomepage_in_2, 0, 1, EUMGSequencePlayMode.Forward, 1, false)

            StoreConfig.evtStoreRecommendItemChangedSelect:Invoke(self.curSelectIndex)
        end
    end
end

function RecommendHomepage:_OnGetItemCount()
    loginfo("[RecommendHomepage] _OnGetItemCount #self.SortedStoreHotRecommendation:" ..
        #self.SortedStoreHotRecommendation)
    return #self.SortedStoreHotRecommendation or 0
end

function RecommendHomepage:_OnProcessItemWidget(position, itemWidget)
    local idx = position
    local curdata = self.SortedStoreHotRecommendation[idx]
    if curdata ~= nil then
        -- local OnSelectionDataItemViewClicked = CreateCallBack(self.OnWidgetClicked, self)
        itemWidget:Visible()
        itemWidget:SetItemInfo(idx, curdata, self.curSelectIndex)

        -- if DFHD_LUA == 1 then -- 只在PC上才会有事件，手游体验不好
        --     weakUIIns:Event("OnHovered", self.OnHoverCarousel, self, i)
        --     weakUIIns:Event("OnUnHovered", self.OnUnHoverCarousel, self, i)
        -- end
    else
        itemWidget:Collapsed()
    end

    loginfo("[RecommendHomepage] _OnProcessItemWidget posIdx:" .. position)
end

-- 点击itemview
function RecommendHomepage:OnWidgetClicked(posIdx)
    loginfo("[RecommendHomepage] OnWidgetClicked posIdx:" .. posIdx)
end

function RecommendHomepage:_OnButtonHotAreaClick()
    self:_OnStoreRecommendItemJumpClick(self.curSelectIndex)
end

function RecommendHomepage:Imp_OnMouseWheel(inGeometry, inGestureEvent)
    if DFHD_LUA == 1 and self:IsInHotZone(inGeometry, inGestureEvent) then
        local delta = UKismetInputLibrary.PointerEvent_GetWheelDelta(inGestureEvent)
        local targetIndex = self.curSelectIndex
        if delta < 0 then
            targetIndex = self.curSelectIndex + 1
            if targetIndex > #self.SortedStoreHotRecommendation then
                targetIndex = #self.SortedStoreHotRecommendation
            end
        else
            targetIndex = self.curSelectIndex - 1
            if targetIndex <= 1 then
                targetIndex = 1
            end
        end

        loginfo("allardwang --> targetIndex:"..targetIndex)
        self:_OnStoreRecommendItemHover(targetIndex)
        self._wtWaterFallList:ScrollToIndexIfNotInScreen(targetIndex, true)
        -- self._wtWaterFallList:ScrollToItem(targetIndex, true, true, 10, 0, false)
    end
end

function RecommendHomepage:IsInHotZone(inGeometry, inGestureEvent)
    local sceenPos = inGestureEvent:GetScreenSpacePosition()
    local isUnder = false
    for _, widget in ipairs(self._wtHotzone) do
        local geometry = widget:GetCachedGeometry()
        isUnder = geometry:IsUnderLocation(inGestureEvent:GetScreenSpacePosition())
        if isUnder then
            break
        end
    end
    return not isUnder
end

function RecommendHomepage:_OnButtonVideoClick()
    self:PlayVideo(2)
end

function RecommendHomepage:PlayVideo(playStyle)
    if self.CurSelectMovies ~= nil and self.CurSelectMovies ~= "" then
        local needProgressBar = false
        local needSkipBtn = true
        local curdata = self.SortedStoreHotRecommendation[self.curSelectIndex]
        if curdata then
            Server.StoreServer:AddPlayedPV2List(curdata.TabId)
        end
        Module.Store.Field:SetVideoAutoPlay(self.CurSelectMovies)
        self.isCancelTickNext = 1
        self.isPlayVideo = 1

        -- 屏蔽光标
        if IsHD() then
            WidgetUtil.SetFreeAnalogCursorIsBlocked(self, true, false)
        end

        -- 如果是全屏播放的视频，调用CommonVideoFullScreenView
        -- Facade.UIManager:AsyncShowUI(UIName2ID.CommonVideoFullScreenView, nil, nil, self.CurSelectMovies, needProgressBar, needSkipBtn)
        Module.CommonWidget:ShowFullScreenVideoView(self.CurSelectMovies, needProgressBar, needSkipBtn, self.OnCommonFullVideoMediaPlayEnd, self, 1, 0)

        LogAnalysisTool.DoSendStoreVideoEventReportLog(1, self.CurSelectMovies, playStyle)
    end
end

function RecommendHomepage:OnCommonFullVideoMediaPlayEnd()
    -- 恢复光标
    if IsHD() then
        WidgetUtil.SetFreeAnalogCursorIsBlocked(self, false)
    end

    self.isCancelTickNext = 0
    self.isPlayVideo = 0
end

function RecommendHomepage:_OnStoreRecommendItemHover(posIdx)
    if self.skipAndNeedSelect == true then
        self.skipAndNeedSelect = false
        return
    end

    self.isCancelTickNext = 1
    self.tickDeltaTime = 0

    -- loginfo("[RecommendHomepage] _OnStoreRecommendItemHover posIdx:" .. posIdx)
    if self.curSelectIndex == posIdx then
        return
    end

    self._mediaComponent:Collapsed()
    self._wtVideoJump:Collapsed()

    self.curSelectIndex = posIdx
    -- self._wtWaterFallList:RefreshAllItems()

    self:RefreshSelectBundleText()

    self:ReprotBannerEvent(2)
    self:PlayAnimation(self.WBP_Store_RecommendHomepage_in_2, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    StoreConfig.evtStoreRecommendItemChangedSelect:Invoke(self.curSelectIndex)

    --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() then
        self:_RefreshBottomBar()
    end
    --- END MODIFICATION
end

function RecommendHomepage:_OnStoreRecommendItemUnHover(posIdx)
    -- loginfo("[RecommendHomepage] _OnStoreRecommendItemUnHover posIdx:" .. posIdx)
    self.isCancelTickNext = 0
end

function RecommendHomepage:SplitString(str)
    local part1, part2 = str:match("([^|]+)|([^|]+)")  
    if part1 and part2 then
        return part1, part2 
    else
        return str, nil
    end
end

function RecommendHomepage:_OnStoreRecommendItemJumpClick(posIdx)
    loginfo("[RecommendHomepage] _OnStoreRecommendItemClick posIdx:" .. posIdx)

    if posIdx == self.curSelectIndex then
        --show detail
        local curdata = self.SortedStoreHotRecommendation[self.curSelectIndex]
        self:_ClickJumpFunc(curdata)
    else
        self.curSelectIndex = posIdx
        self:RefreshSelectBundleText()

        self._wtWaterFallList:RefreshAllItems()
    end

    self:ReprotBannerEvent(3)
end

function RecommendHomepage:_ClickJumpFunc(curdata)
    if curdata then
        -- Module.CommonTips:ShowSimpleTip("showdetail" .. curdata.BannerName)
        if curdata.BannerType == 1 then
            local isMallGift = false
            Facade.UIManager:AsyncShowUI(UIName2ID.StoreProductPreview, nil, nil, curdata, isMallGift, true)
        elseif curdata.BannerType == 2 then
            --mandel
            StoreConfig.evtStoreSwitchMainTabByIndex:Invoke(EStoreTab.MandelLottery, curdata.JumpTo)
        elseif curdata.BannerType == 3 then
            loginfo("[RecommendHomepage] curdata.JumpToId" .. curdata.JumpTo)
            local param1, param2 = self:SplitString(curdata.JumpTo)
            local res = Module.Gamelet:ShowGameletStackCBStoreHR(param1, param2)
            if res == -1 then
                Module.CommonTips:ShowSimpleTip(StoreConfig.Loc.StoreAppNotReadyTip)
            elseif res == -2 then
                Module.CommonTips:ShowSimpleTip(StoreConfig.Loc.StoreAppHasOpenedTip)
            end
        elseif curdata.BannerType == 4 then
            Module.GCloudSDK:OpenUrl(curdata.JumpTo, 3, false, false, "", false)
        end
    end    
end

function RecommendHomepage:_OnStoreRecommendItemClick(posIdx)
    loginfo("[RecommendHomepage] _OnStoreRecommendItemClick posIdx:" .. posIdx)
    self.tickDeltaTime = 0

    if self.curSelectIndex == posIdx then
        self:_OnStoreRecommendItemJumpClick(posIdx)
        return
    end

    self._mediaComponent:Collapsed()
    self._wtVideoJump:Collapsed()

    self.curSelectIndex = posIdx
    if DFHD_LUA == 1 then
        self._wtWaterFallList:RefreshAllItems()
    else
        self._wtWaterFallList:ScrollToIndexIfNotInScreen(self.curSelectIndex, true)
        StoreConfig.evtStoreRecommendItemChangedSelect:Invoke(self.curSelectIndex)
    end

    self:RefreshSelectBundleText()

    self:ReprotBannerEvent(2)
    self:PlayAnimation(self.WBP_Store_RecommendHomepage_in_2, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
end

function RecommendHomepage:ReprotBannerEvent(eventType)
    local bannerID = 0
    local bannerName = ""
    local curdata = self.SortedStoreHotRecommendation[self.curSelectIndex]
    if curdata ~= nil then
        bannerName = curdata.BannerName
        bannerID = curdata.TabId
    end

    LogAnalysisTool.DoSendStoreBannerEventReportLog(1, bannerID, bannerName, eventType)
end

function RecommendHomepage:RefreshSelectBundleText()
    local curdata = self.SortedStoreHotRecommendation[self.curSelectIndex]
    if curdata ~= nil then

        local nameIconPath = curdata.ImageSourceLogo
        -- if curdata.TabId == 10200001 then
        --     --test
        --     nameIconPath = "Texture2D'/Game/UI/UIAtlas/Texture/SkinWordArt/SkinWordArt_05.SkinWordArt_05'"
        -- end

        self.bUseIcon = false
        if nameIconPath == nil or nameIconPath == "" then
            self._wtTextBundleName:Visible()
            self._wtNameIconRoot:Collapsed()

            self._wtTextBundleName:SetText(curdata.BannerName)
        else
            self.bUseIcon = true
            self._wtTextBundleName:Collapsed()
            self._wtNameIconRoot:Visible()
            self._wtNameIcon:AsyncSetImagePath(nameIconPath, true)
        end

        self._wtItemBg:AsyncSetImagePath(curdata.IamgeSourceBig, false)

        --播放背景视频
        local tableData = Server.StoreServer:GetHotRecommendationClientByTabID(curdata.TabId)
        if tableData then
            if tableData.BGVideoName == "None" or tableData.BGVideoName == "" or tableData.BGVideoName == nil then
                self._mediaBackground:Stop()
            else
                self._mediaBackground:Play(tableData.BGVideoName)
            end
        end
        
        local nowprice, noworignprice = self:_getBuyBundlePrice(curdata)
        if curdata.BannerType ~= 2 then
            self.curSelectBundleGoods = curdata.BundleItems --self:GetItemsByGoodsString(curdata.BundleItem)
            if nowprice > 0 then
                local priceStr = string.format(StoreConfig.Loc.ItemPriceNormalBig,
                Module.Currency:GetRichTxtImgByItemId(curdata.BundleCurrencyType),
                MathUtil.GetNumberFormatStr(nowprice))

                self._wtTextPrice:Visible()
                self._wtTextPrice:SetText(priceStr)
            else
                self._wtTextPrice:Collapsed()
            end

            if self.bUseIcon == true then
                self._wtTextGoodsTip:Visible()
                self._wtTextGoodsTip:SetText(curdata.BannerName)
            else
                self._wtTextGoodsTip:Collapsed()
            end
        else
            self._wtTextPrice:Collapsed()
            self._wtTextGoodsTip:Collapsed()
        end

        if self._wtCountWidget ~= nil then
            self._wtCountWidget:Hidden()
        end

        local bShowDiscount = curdata.IsDiscountIntensity ~= nil and curdata.IsDiscountIntensity > 0
        if self._wtDiscountRoot ~= nil then
            if bShowDiscount then
                self._wtDiscountRoot:Visible()
                local disCount = (curdata.BundlePrice - curdata.DisBundlePrice) / curdata.BundlePrice
                local showDiscountStr = string.format("%.0f", disCount * 100)
                self._wtTextDiscount:SetText(StringUtil.SequentialFormat(StoreConfig.Loc.ItemDiscount, showDiscountStr))
            else
                self._wtDiscountRoot:Collapsed()
            end
        end

        if self._wtTextPriceOrign ~= nil then
            if nowprice > 0 then
                self._wtTextPriceOrign:Visible()
                self._wtTextPriceOrign:SetText(noworignprice)
            else
                self._wtTextPriceOrign:Collapsed()
            end
            -- if bShowDiscount then
            --     self._wtTextPriceOrign:Visible()
            --     self._wtTextPriceOrign:SetText(curdata.BundlePrice)
            -- else
            --     self._wtTextPriceOrign:Collapsed()
            -- end
        end

        if self._wtMoneyRoot ~= nil then
            if nowprice > 0 then
                self._wtMoneyRoot:Visible()
            else
                self._wtMoneyRoot:Collapsed()
            end
        end

        local bShowCountDown = curdata.IsDisplayCountdown ~= nil and curdata.IsDisplayCountdown > 0
        if self._wtCountWidget ~= nil and self._wtTextCountDown ~= nil then
            if bShowCountDown then
                self._wtCountWidget:Visible()
                local timestamp = curdata.OfflineTime
                if timestamp > 0 then
                    self.tickCountDown = 0
                    self.tickDeltaTimeCountDown = 0
                    self.offlineSeconds = TimeUtil.GetLocalRemainTime2Seconds(timestamp)
                    -- self.offlineSeconds = self.offlineSeconds - 27203*24*60*60 - 9*60*60
                    local showStr = ""
                    local day, hour, min, sec = TimeUtil.GetSecondsFormatDDHHMMSS(self.offlineSeconds)
                    if self.offlineSeconds > 3600 * 24 then
                        showStr = TimeUtil.GetSecondsFormatDDHHMMSSString(self.offlineSeconds, "DDHH_Second")
                    elseif self.offlineSeconds > 3600 then
                        showStr = string.format(StoreConfig.Loc.StoreBuyLimitTimeHourTip, hour, min)
                    else
                        self.tickCountDown = 1
                        showStr = string.format(StoreConfig.Loc.StoreBuyLimitTimeMinSecTip, min, sec)
                    end

                    self._wtTextCountDown:SetText(showStr)
                end
            else
                self.tickCountDown = 0
                self._wtCountWidget:Collapsed()
            end
        end

        if self._wtDiscountAndCountDownRoot ~= nil then
            if bShowDiscount or bShowCountDown then
                self._wtDiscountAndCountDownRoot:Visible()
            else
                self._wtDiscountAndCountDownRoot:Collapsed()
            end
        end

        if self._wtVideoBtn ~= nil then
            self.CurSelectMovies = curdata.Movies

            if self.CurSelectMovies == nil or self.CurSelectMovies == "" or self.enablePlayFullScreenCG == false then
                self._wtVideoBtn:Collapsed()
            else
                self._wtVideoBtn:Visible()
                self.ISAutoPlayVideo = curdata.MoviesAuto
                self.tabId = curdata.TabId
                local buyRecord = Server.StoreServer:GetRecommondBuyRecordByTabId(curdata.TabId)
                local isOwned = false
                --bundle已经购买，就不播放视频
                if buyRecord then
                    isOwned = StoreLogic.GetIsItemOwned(self.CurSelectMovies, buyRecord.item_ids, buyRecord.is_sold_out)
                end
               
                local hasPlayed = Server.StoreServer:GetIsStorePvPlayed(curdata.TabId)
                if isOwned then
                    hasPlayed = true
                end
                local needAutoPlay = Module.Store.Field:CheckVideoNeedAutoPlay(self.CurSelectMovies)
                if (not hasPlayed or hasPlayed == nil) and self.ISAutoPlayVideo ~= nil and self.ISAutoPlayVideo > 0 then
                    self.nowCheckAutoPlayVideo = self.CurSelectMovies
                    self:CheckAndPlayAutoVideo()
                end
            end
        end

        --show vfx
        local idnexkey = -1
        local strshopid =  tostring(curdata.TabId)
        for key, value in pairs(self.ID) do
            if  strshopid == value then
                idnexkey = key
                break
            end
        end

        -- self:SetVfx(curdata.TabId)
        -- if idnexkey >= 0 then
        if self.nameToIndex and self.nameToIndex[curdata.bName] then
            self._wtBGEfxSwitcher:Visible()
            self._wtBGEfxSwitcher:SetActiveWidgetIndex(self.nameToIndex[curdata.bName])
            local bgWidget = self._wtBGEfxSwitcher:GetActiveWidget()
            if bgWidget ~= nil then
                if bgWidget.ResetSpineAnim then
                    bgWidget:ResetSpineAnim()
                end
                if bgWidget.Anim_loop then
                    bgWidget:PlayAnimation(bgWidget.Anim_loop, 0, 0, EUMGSequencePlayMode.Forward, 1, false)
                end
            end
        else
            self._wtBGEfxSwitcher:Hidden()
        end
    end
end


function RecommendHomepage:CheckAndPlayAutoVideo()
     Timer.DelayCall(0.1, function()
        local curdata = self.SortedStoreHotRecommendation[self.curSelectIndex]
        if curdata ~= nil then
            if self.CurSelectMovies ~= nil and self.CurSelectMovies ~= "" and self.nowCheckAutoPlayVideo == self.CurSelectMovies then
                local needAutoPlay = Module.Store.Field:CheckVideoNeedAutoPlay(self.CurSelectMovies)
                if needAutoPlay and self.ISAutoPlayVideo ~= nil and self.ISAutoPlayVideo > 0 then
                    self:PlayVideo(1)
                end
            end
        end
    end)
end

function RecommendHomepage:GetItemsByGoodsString(goodsStr)
    local items = {}
    local bGoodsFormat = false
    if string.sub(goodsStr, 1, 2) == "{{" and string.sub(goodsStr, -2) == "}}" then
        bGoodsFormat = true
    end

    if bGoodsFormat == false then
        return items
    end

    goodsStr = goodsStr:sub(2, -2)
    local items = {}

    for item_str in string.gmatch(goodsStr, "{[^}]+}") do
        item_str = item_str:sub(2, -2)
        local item = {}

        local data = {}
        for value in string.gmatch(item_str, "[^,]+") do
            value = tonumber(value)
            table.insert(data, value)
        end

        item.id          = data[1]
        item.count       = data[2]
        item.price       = data[3]
        item.originPrice = data[4]

        table.insert(items, item)
    end

    for i = 1, #items do
        loginfo(string.format("[RecommendHomepage] GetItemsByGoodsString Item %d: id=%d, quantity=%d, price=%d", i,
            items[i].id, items[i].count, items[i].price))
    end

    return items
end

function RecommendHomepage:_getBuyBundlePrice(shopData)
    local price = 0
    local priceOrign = 0

    local needBuyCount = 0
    local gooditems = shopData.BundleItems
    local buyRecord = Server.StoreServer:GetRecommondBuyRecordByTabId(shopData.TabId)

    for k, v in pairs(gooditems) do
        if v.dis_price > 0 then
            local isSell = self:CheckIsSellBuyGoodId(v.id, buyRecord)
            if isSell == false then
                needBuyCount = needBuyCount + 1
                price = price + v.dis_price
                priceOrign = priceOrign + v.price
            end
        end
    end

    if needBuyCount <= 1 then
        price = priceOrign
    end

    return price, priceOrign
end

function RecommendHomepage:CheckIsSellBuyGoodId(id, buyRecord)
    local isOwned = false

    if buyRecord ~= nil then
        if buyRecord.is_sold_out then
            isOwned = true
        else
            for key, value in pairs(buyRecord.item_ids) do
                if value == id then
                    isOwned = true
                    break
                end
            end
        end
    end

    return isOwned
end

-----------------------------------------------------按钮点击事件-----------------------------------------------------
function RecommendHomepage:_OnGroupTabClick(index)

end

--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
function RecommendHomepage:_EnableGamepadFeature()
    if not IsHD() then
        return 
    end

    -- 切换页签的时候也会触发onshowbegin，要确保visible的时候才初始化手柄功能
    if not self:IsVisible() then
        return
    end

    if not self._NavGroup then
        --配置导航
        self._NavGroup = WidgetUtil.RegisterNavigationGroup(self._wtWaterFallList, self, "Hittest")
        self._NavGroup:AddNavWidgetToArray(self._wtWaterFallList)
        self._NavGroup:SetScrollRecipient(self._wtWaterFallList)
    end

    --启用NoA配置
    if not self._WidgetNavConfig then
        self._WidgetNavConfig =  WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction,self)
    end

    if self._NavGroup then
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup)
    end

    -- 绑定多输入设备切换事件
    if not self._OnNotifyInputTypeChangedHandle then
        self._OnNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._OnInputTypeChanged, self))
        -- 初始化
        local curInputType = WidgetUtil.GetCurrentInputType()
        self:_OnInputTypeChanged(curInputType)
    end
end

function RecommendHomepage:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end

    -- 移除导航组
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    self._WidgetNavConfig = nil
    WidgetUtil.RemoveNavigationGroup(self)
    self._NavGroup = nil
    Module.CommonBar:RecoverBottomBarInputSummaryList()

    if self._OnNotifyInputTypeChangedHandle then
        UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._OnNotifyInputTypeChangedHandle)
        self._OnNotifyInputTypeChangedHandle = nil
    end
end

function RecommendHomepage:_RefreshBottomBar()
    if not IsHD() then
        return 
    end

    -- 只在手柄的情况下刷新
    local curInputType = WidgetUtil.GetCurrentInputType()
    if curInputType == EGPInputType.Gamepad then
        self:_SetBottomBarInputSummary()
    end
end

function RecommendHomepage:_SetBottomBarInputSummary()
    if not IsHD() then
        return 
    end

    Module.CommonBar:RecoverBottomBarInputSummaryList()
    local summaryList = {}
    table.insert(summaryList, {actionName = "SelectForStore", func = self._OnGamepadSelectItem, caller = self, bUIOnly = false, bHideIcon = false})
    if self._wtVideoBtn and self._wtVideoBtn:IsVisible() then
        table.insert(summaryList, {actionName = "MallDemo", func = self._OnButtonVideoClick, caller = self, bUIOnly = false, bHideIcon = false}) 
    end  
    Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, false, true)
end

function RecommendHomepage:_OnInputTypeChanged(InputType)
    if not IsHD() then
        return 
    end

    if InputType == EGPInputType.Gamepad then
        -- 设置底部按键提示
        self:_SetBottomBarInputSummary()
    else
        -- 清空底部按键提示
        local summaryList = {}
        Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, false, true)
    end
end

function RecommendHomepage:_OnGamepadSelectItem()
    if not IsHD() then
        return 
    end

    if self.curSelectIndex < #self.SortedStoreHotRecommendation +1 and self.curSelectIndex > 0 then
        local curdata = self.SortedStoreHotRecommendation[self.curSelectIndex]
        self:_ClickJumpFunc(curdata)
    end
end
--- END MODIFICATION

return RecommendHomepage
