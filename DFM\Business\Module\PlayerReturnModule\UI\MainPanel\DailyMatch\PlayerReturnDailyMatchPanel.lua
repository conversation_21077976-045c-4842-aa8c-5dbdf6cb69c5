----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMPlayerReturn)
----- LOG FUNCTION AUTO GENERATE END -----------



local IPlayerReturnSubActivity      = require "DFM.Business.Module.PlayerReturnModule.SubActivities.IPlayerReturnSubActivity"
local IPlayerReturnSubActivityPanel = require "DFM.Business.Module.PlayerReturnModule.UI.SubPanels.IPlayerReturnSubActivityPanel"
local PlayerReturnDailyMatchItem    = require "DFM.Business.Module.PlayerReturnModule.UI.MainPanel.DailyMatch.PlayerReturnDailyMatchItem"
local NavigationAgent             = require "DFM.Business.DataStruct.Common.Agent.NavigationAgent"
local PlayerReturnConfig = require "DFM.Business.Module.PlayerReturnModule.PlayerReturnConfig"
local WaterfallBase = require "DFM.Business.DataStruct.UIDataStruct.WaterfallBase"


---@class PlayerReturnDailyMatchPanel: IPlayerReturnSubActivityPanel
local PlayerReturnDailyMatchPanel = ui("PlayerReturnDailyMatchPanel", IPlayerReturnSubActivityPanel)

function PlayerReturnDailyMatchPanel:Ctor()
    self._wtLoginDaysLabel = self:Wnd("DFTextBlock_88", UITextBlock)
    self._wtLoginDaysCount = self:Wnd("DFTextBlock", UITextBlock)

    self._wtTaskList = WaterfallBase:Wnd(self, "DFScrollBox_92")
    self._wtTaskList:ToggleAutoRefresh(false)
    self._wtTaskList.indexBase = 0 -- DFScrollBox uses C style index

    self._wtBuffCount1 = self:Wnd("DFTextBlock_3", UITextBlock)
    self._wtBuffCount2 = self:Wnd("DFTextBlock_4", UITextBlock)
    self._wtBuffCountBox1 = self:Wnd("PlatformPaddingBox_18", UITextBlock)
    self._wtBuffCountBox2 = self:Wnd("DFCanvasPanel_3", UITextBlock)

    ---@param widget PlayerReturnDailyMatchItem
    self._wtTaskList.defProcessWidgetFunc =function (widget, data)
        widget:SetState(data.day, data.activityInfo, data.clickCallback, data.bExpand)
    end

    self._navMgr = NavigationAgent.Create(self)
    self._updateEventHandle = nil
end

function PlayerReturnDailyMatchPanel:OnPlayerReturnEnter(from)
    IPlayerReturnSubActivityPanel.OnPlayerReturnEnter(self, from)

    if not self:UpdateDisplay() then return end

    if IsHD() then
        self._navMgr:CreateGroup({
            id = "TaskList",
            rootWidget = self._wtTaskList,
            members = {self._wtTaskList},
            scrollRecipient = self._wtTaskList,
            bStack = true,
        })
        self._navMgr:BuildGroupTree()

        self._deferredOnShow = function()
            for i = 1, self._wtTaskList:Len() do
                local widget = self._wtTaskList:GetWidgetAt(i)
                if widget and widget:GetExpandedOrExpanding() then
                    widget:FocusFirstItemIcon()
                    return
                end
            end
        end
    end

    self._updateEventHandle = Server.ActivityServer.Events.evtActivityTaskChange:AddListener(self._OnActivityTaskChange, self)
end

function PlayerReturnDailyMatchPanel:OnShow()
    if self._deferredOnShow then self._deferredOnShow() end
    self._deferredOnShow = nil
end

function PlayerReturnDailyMatchPanel:_OnActivityTaskChange(activityID)
    if activityID == self._activityInfo.actv_id then
        self:UpdateDisplay()
    end
end

function PlayerReturnDailyMatchPanel:UpdateDisplay()
    ---@type PlayerReturnDailyMatchImpl
    local actv = IPlayerReturnSubActivity.Get(ReturnActivityType.ReturnActivityTypeFight, Server.ArmedForceServer:GetCurArmedForceMode())
    local activityInfo = actv:GetActivityInfo()
    self._activityInfo = activityInfo
    self._wtTitle:SetText(activityInfo.name)

    if not activityInfo then return false end

    local itemClickCallback = CreateCallBack(self._OnItemClicked, self)
    self._wtLoginDaysCount:SetText(tostring(actv:GetUnlockedDays()))
    self._wtTaskList:ClearItems()

    local firstActiveDay = nil
    local lastUnlockedDay = nil
    for idx, taskInfo in ipairs(activityInfo.task_info) do
        if (not firstActiveDay) and (taskInfo.state == ActivityTaskState.ActivityTaskStateAccepted) then
            firstActiveDay = idx
        end

        if taskInfo.state >= ActivityTaskState.ActivityTaskStateAccepted then
            lastUnlockedDay = idx
        end
    end
    local expandIdx = firstActiveDay or lastUnlockedDay

    for idx, taskInfo in ipairs(activityInfo.task_info) do
        self._wtTaskList:InsertItem({day = idx, activityInfo = activityInfo, clickCallback = itemClickCallback, bExpand = (idx == expandIdx)})
    end

    self._wtTaskList:RefreshAllItems()
    Timer.DelayCall(0.5, function() 
        self._wtTaskList:ScrollWidgetIntoView(self._wtTaskList:GetWidgetAt(expandIdx), true, 2)
    end)

    local doubleExp, doubleMerits = actv:GetMPBuffState()
    self._wtBuffCount1:SetText(StringUtil.FTextFormat(PlayerReturnConfig.Localization.DoubleExperienceCount, {count = activityInfo.return_info.tdm_use_double_exp_card_num, total = 5}))
    self._wtBuffCount2:SetText(StringUtil.FTextFormat(PlayerReturnConfig.Localization.DoubleMeritsCount, {count = activityInfo.return_info.tdm_use_double_score_card_num, total = 5}))
    if doubleExp > 0 then
        self._wtBuffCountBox1:SelfHitTestInvisible()
    else
        self._wtBuffCountBox1:Collapsed()
    end
    if doubleMerits > 0 then
        self._wtBuffCountBox2:SelfHitTestInvisible()
    else
        self._wtBuffCountBox2:Collapsed()
    end

    return true
end

function PlayerReturnDailyMatchPanel:_OnItemClicked(itemWidget)
    local expandIdx = self._wtTaskList:WidgetToIndex(itemWidget)

    for idx = 1, self._wtTaskList:Len() do
        local bExpand = (idx == expandIdx)
        if self._wtTaskList:GetWidgetAt(idx) then
            self._wtTaskList:GetWidgetAt(idx):SetExpanded(bExpand)
        end
        self._wtTaskList:DataAt(idx).bExpand = bExpand
    end
end

function PlayerReturnDailyMatchPanel:OnPlayerReturnLeave(to)
    IPlayerReturnSubActivityPanel.OnPlayerReturnLeave(self, to)

    if IsHD() then
        self._navMgr:RemoveAllGroups()
    end

    if self._updateEventHandle then
        Server.ActivityServer.Events.evtActivityTaskChange:RemoveListenerByHandle(self._updateEventHandle)
        self._updateEventHandle = nil
    end
end

return PlayerReturnDailyMatchPanel