----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMExpansionPackCoordinator)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class ExpansionPackCoordinatorModule : ModuleBase
local ExpansionPackCoordinatorModule = class("ExpansionPackCoordinatorModule", require("DFM.YxFramework.Managers.Module.ModuleBase"))
--if PLATFORM_WINDOWS then
--    UWeGameManager = import "WeGameSDKManager"
--    WeGameManager = UWeGameManager.Get(GetGameInstance())
--end

function ExpansionPackCoordinatorModule:Ctor()
end

---------------------------------------------------------------------------------
--- Module 生命周期
---------------------------------------------------------------------------------
--- 模块Init回调，用于初始化一些数据
---@overload fun(ModuleBase, OnInitModule)
function ExpansionPackCoordinatorModule:OnInitModule()
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then
                self:AddListeners()
            else
                self:AddListeners()
            end
        end
    end
    self:CheckIfShouldMountAllPaks()
end

--- 若为非懒加载模块，则在Init后调用;对应每个OnGameFlowChangeEnter
--- 模块默认[常驻]加载资源（预加载UI蓝图、需要用到的图片等等
---@overload fun(ModuleBase, OnLoadModule)
function ExpansionPackCoordinatorModule:OnLoadModule()
end

--- 无论是否懒加载都会调用，对应每个OnGameFlowChangeLeave
--- 模块默认卸载资源
---@overload fun(ModuleBase, OnUnloadModule)
function ExpansionPackCoordinatorModule:OnUnloadModule()
end

--- 注销LuaEvent、Timer监听
---@overload fun(ModuleBase, OnDestroyModule)
function ExpansionPackCoordinatorModule:OnDestroyModule()
    self:RemoveAllLuaEvent()
end

---@overload fun(ModuleBase, OnGameFlowChangeLeave)
function ExpansionPackCoordinatorModule:OnGameFlowChangeLeave(gameFlowType)
    if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then
    --- WeGameSDKEventLogic.RemoveLobbyListeners()
    end
end

---@overload fun(ModuleBase, OnGameFlowChangeEnter)
function ExpansionPackCoordinatorModule:OnGameFlowChangeEnter(gameFlowType)
    if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then
    --- WeGameSDKEventLogic.AddLobbyListeners()
    end
end

---------------------------------------------------------------------------------
--- Loading 生命周期
--- 设置bAutoLoading = true则下列生命周期有效
--- 模块[Loading]加载资源，区分局内外
---------------------------------------------------------------------------------
---@overload fun(ModuleBase, OnLoadingLogin2Frontend)
function ExpansionPackCoordinatorModule:OnLoadingLogin2Frontend(gameFlowType)
end

---@overload fun(ModuleBase, OnLoadingGame2Frontend)
function ExpansionPackCoordinatorModule:OnLoadingGame2Frontend(gameFlowType)
end

---@overload fun(ModuleBase, OnLoadingFrontend2Game)
function ExpansionPackCoordinatorModule:OnLoadingFrontend2Game(gameFlowType)
end

---------------------------------------------------------------------------------
--- Module Public API
---------------------------------------------------------------------------------
---

function ExpansionPackCoordinatorModule:AddListeners()
    self:AddLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged, self._OnPackStateChanged, self)
    --self:AddLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackInstallProgress, self._OnInstallStart, self)
end

function ExpansionPackCoordinatorModule:CheckIfShouldMountAllPaks()
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then
                Module.WeGameSDK:MountAllExpansionPaks()
            else
                --wegame和launcher包在关闭扩张包功能的时候 mount所有扩张包里的内容
                if not Module.WeGameSDK:IsSupportLitePackage() then
                    Module.WeGameSDK:MountAllExpansionPaks()
                end
            end
        else
            --蓝盾自启动包
            Module.WeGameSDK:MountAllExpansionPaks()
        end
    end
end
--
--function ExpansionPackCoordinatorModule:MountAllExpansionPaks()
--    if PLATFORM_WINDOWS then
--        if IsWeGameEnabled() then
--            if VersionUtil.IsGameChannelSteam() then
--
--            else
--                Module.WeGameSDK:MountAllExpansionPaks()
--            end
--        end
--    end
--end

function ExpansionPackCoordinatorModule:IsSupportLitePackage()
    local isSupport = false
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then
                isSupport = Module.DFOnlineService:IsSupportLitePackage()
            else
                isSupport = Module.WeGameSDK:IsSupportLitePackage()
            end
        elseif IsInEditor() and not IsHD() then
            isSupport = LiteDownloadManager:IsSupportLitePackage()
        end
    elseif IsMobile() or IsConsole() then
        isSupport = LiteDownloadManager:IsSupportLitePackage()
    end
    return isSupport
end

function ExpansionPackCoordinatorModule:IsDownloadedByMapID(mapID)
    --if not self:IsSupportLitePackage() then
    --    return true
    --end
    local isDownloaded = true
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then

            else
                isDownloaded = Module.WeGameSDK:IsMapInstalled(tostring(mapID))
            end
        elseif IsInEditor() and not IsHD() then
            local moduleName = LiteDownloadManager:GetModuleNameByMapId(tostring(mapID))
            isDownloaded = LiteDownloadManager:IsDownloadedByModuleName(moduleName)
        end
    elseif IsMobile() then
        local moduleName = LiteDownloadManager:GetModuleNameByMapId(tostring(mapID))
        isDownloaded = LiteDownloadManager:IsDownloadedByModuleName(moduleName)
    elseif IsConsole() then
        if IsPS5() then
            isDownloaded = LiteDownloadManager:CheckMapInPlayGoInitialChunk(mapID)
        else
            local moduleName = LiteDownloadManager:GetModuleNameByMapId(tostring(mapID))
            isDownloaded = LiteDownloadManager:IsDownloadedByModuleName(moduleName)
        end
    end
    return isDownloaded
end

function ExpansionPackCoordinatorModule:GetModuleNameByMapId(mapID)
    local moduleName = ""
    if PLATFORM_WINDOWS then
        moduleName = Module.WeGameSDK:GetModuleNameByMapId(tostring(mapID))
        --if IsWeGameEnabled() then
        --    if VersionUtil.IsGameChannelSteam() then
        --
        --    else
        --    end
        --end
        if IsInEditor() and not IsHD() then
            moduleName = LiteDownloadManager:GetModuleNameByMapId(tostring(mapID))
        end
    elseif IsMobile() or IsConsole() then
        moduleName = LiteDownloadManager:GetModuleNameByMapId(tostring(mapID))
    end
    return moduleName
end

function ExpansionPackCoordinatorModule:GetQuestNameByMapId(mapID)
    local questName = ""
    if PLATFORM_WINDOWS then
        questName = Module.WeGameSDK:GetQuestNameByMapId(mapID)
        --if IsWeGameEnabled() then
        --    if VersionUtil.IsGameChannelSteam() then
        --
        --    else
        --        questName = Module.WeGameSDK:GetQuestNameByMapId(mapID)
        --    end
        --end
        if IsInEditor() and not IsHD() then
            questName = LiteDownloadManager:GetQuestNameByMapId(mapID)
        end
    elseif IsMobile() or IsConsole() then
        questName = LiteDownloadManager:GetQuestNameByMapId(mapID)
    end
    return questName
end

function ExpansionPackCoordinatorModule:GetQuestIdByMapId(mapID)
    local questName = ""
    if PLATFORM_WINDOWS then
        questName = Module.WeGameSDK:GetQuestIdByMapId(mapID)
        --if IsWeGameEnabled() then
        --    if VersionUtil.IsGameChannelSteam() then
        --
        --    else
        --        questName = Module.WeGameSDK:GetQuestIdByMapId(mapID)
        --    end
        --end
        if IsInEditor() and not IsHD() then
            questName = LiteDownloadManager:GetQuestIdByMapId(mapID)
        end
    elseif IsMobile() or IsConsole() then
        questName = LiteDownloadManager:GetQuestIdByMapId(mapID)
    end
    return questName
end

function ExpansionPackCoordinatorModule:IsDownloadedByModuleName(moduleName)
    local isDownloaded = true
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then

            else
                isDownloaded = Module.WeGameSDK:IsDownloadedByModuleName(moduleName)
            end
        elseif IsInEditor() and not IsHD() then
            isDownloaded = LiteDownloadManager:IsDownloadedByModuleName(moduleName)
        end
    elseif IsMobile() or IsConsole() then
        isDownloaded = LiteDownloadManager:IsDownloadedByModuleName(moduleName)
    end
    return isDownloaded
end

function ExpansionPackCoordinatorModule:IsDownloadingByModuleName(moduleName)
    local isDownloading = false
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then

            else
                isDownloading = Module.WeGameSDK:IsDownloadingByModuleName(moduleName)
            end
        elseif IsInEditor() and not IsHD() then
            isDownloading = LiteDownloadManager:IsDownloadingByModuleName(moduleName)
        end
    elseif IsMobile() then
        isDownloading = LiteDownloadManager:IsDownloadingByModuleName(moduleName)
    end
    return isDownloading
end

function ExpansionPackCoordinatorModule:IsWaitingByModuleName(moduleName)
    local isWaiting = false
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then

            else
                isWaiting = Module.WeGameSDK:IsWaitingByModuleName(moduleName)
            end
        elseif IsInEditor() and not IsHD() then
            isWaiting = LiteDownloadManager:IsWaitingByModuleName(moduleName)
        end
    elseif IsMobile() then
        isWaiting = LiteDownloadManager:IsWaitingByModuleName(moduleName)
    end
    return isWaiting
end
--cancel means cancel,means delete already downloaded files,means quit the download mission!!
--function ExpansionPackCoordinatorModule:CancelByModuleName(moduleName)
--    local ret = false
--    if PLATFORM_WINDOWS then
--        if IsWeGameEnabled() then
--            if VersionUtil.IsGameChannelSteam() then

--            else
--                ret = Module.WeGameSDK:CancelByModuleName(moduleName)
--            end
--        elseif IsInEditor() and not IsHD() then
--          LiteDownloadManager:CancelByModuleName(moduleName)
--          ret = true
--        end
--    elseif IsMobile() then
--        LiteDownloadManager:CancelByModuleName(moduleName)
--        ret = true
--    end
--    return ret
--end

function ExpansionPackCoordinatorModule:PauseByModuleName(moduleName)
    local ret = false
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then

            else
                ret = Module.WeGameSDK:PauseByModuleName(moduleName)
            end
        elseif IsInEditor() and not IsHD() then
            LiteDownloadManager:CancelByModuleName(moduleName)
            ret = true
        end
    elseif IsMobile() then
        LiteDownloadManager:CancelByModuleName(moduleName)
        ret = true
    end
    return ret
end

function ExpansionPackCoordinatorModule:DownloadByModuleName(moduleName)
    local ret = false
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then

            else
                ret = Module.WeGameSDK:DownloadByModuleName(moduleName)
            end
        elseif IsInEditor() and not IsHD() then
            LiteDownloadManager:DownloadByModuleName(moduleName)
            ret = true
        end
    elseif IsMobile() then
        LiteDownloadManager:DownloadByModuleName(moduleName)
        ret = true
    end
    return ret
end

function ExpansionPackCoordinatorModule:GetDownloadProgressInfoByModuleName(moduleName)
    local info = nil
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then

            else
                info = Module.WeGameSDK:GetDownloadProgressInfoByModuleName(moduleName)
            end
        elseif IsInEditor() and not IsHD() then
            info = LiteDownloadManager:GetDownloadProgressInfoByModuleName(moduleName)
        end
    elseif IsMobile() then
        info = LiteDownloadManager:GetDownloadProgressInfoByModuleName(moduleName)
    end
    return info
end

function ExpansionPackCoordinatorModule:GetNowSizeByModuleName(moduleName)
    local nowSize = 0
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then

            else
                nowSize = Module.WeGameSDK:GetNowSizeByModuleName(moduleName)
            end
        elseif IsInEditor() and not IsHD() then
            nowSize = LiteDownloadManager:GetNowSizeByModuleName(moduleName)
        end
    elseif IsMobile() then
        nowSize = LiteDownloadManager:GetNowSizeByModuleName(moduleName)
    end
    return nowSize
end

function ExpansionPackCoordinatorModule:GetTotalSizeByModuleName(moduleName)
    local totalSize = 0
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then

            else
                totalSize = Module.WeGameSDK:GetTotalSizeByModuleName(moduleName)
            end
        elseif IsInEditor() and not IsHD() then
            totalSize = LiteDownloadManager:GetTotalSizeByModuleName(moduleName)
        end
    elseif IsMobile() then
        totalSize = LiteDownloadManager:GetTotalSizeByModuleName(moduleName)
    end
    return totalSize
end

function ExpansionPackCoordinatorModule:GetRemainderSizeByModuleName(moduleName)
    local remainderSize = 0
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then

            else
                remainderSize = Module.WeGameSDK:GetRemainderSizeByModuleName(moduleName)
            end
        elseif IsInEditor() and not IsHD() then
            remainderSize = LiteDownloadManager:GetRemainderSizeByModuleName(moduleName)
        end
    elseif IsMobile() then
        remainderSize = LiteDownloadManager:GetRemainderSizeByModuleName(moduleName)
    end
    return remainderSize
end

function ExpansionPackCoordinatorModule:MakeRemainderSizeByPackInfo(packInfo)
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then

            else
                return Module.WeGameSDK:MakeRemainderSizeByPackInfo(packInfo)
            end
        elseif IsInEditor() and not IsHD() then

        end
    elseif IsMobile() then

    end
    return ""
end

function ExpansionPackCoordinatorModule:HaveInstallingQuests()
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then
                return false
            else
                return Module.WeGameSDK:HaveInstallingQuests()
            end
        end
    end
    return false
end

function ExpansionPackCoordinatorModule:GetInstallingQuestsCount()
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then
                return 0
            else
                return Module.WeGameSDK:GetInstallingQuestsCount()
            end
        end
    end
    return 0
end

function ExpansionPackCoordinatorModule:GetInstallingQuestPackIdByIdx(index)
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then
                return nil
            else
                return Module.WeGameSDK:GetInstallingQuestPackIdByIdx(index)
            end
        end
    end
    return nil
end

function ExpansionPackCoordinatorModule:GetExpansionPackTableDataByQuestID(questID)
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then
                return nil
            else
                return Module.WeGameSDK:GetExpansionPackTableDataByQuestID(questID)
            end
        end
    end
    return nil
end

function ExpansionPackCoordinatorModule:GetDownloadConfigByModuleName(packID)
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then
                return nil
            else
                return Module.WeGameSDK:GetExpansionPackTableDataByPackID(packID)
            end
        elseif IsInEditor() and not IsHD() then
            return LiteDownloadManager:GetDownloadConfigByModuleName(packID)
        end
    elseif IsMobile() then
        return LiteDownloadManager:GetDownloadConfigByModuleName(packID)
    end
    return nil
end

function ExpansionPackCoordinatorModule:PauseAll()
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then

            else
                Module.WeGameSDK:PauseAll()
            end
        elseif IsInEditor() and not IsHD() then
            LiteDownloadManager:CancelAll()
        end
    elseif IsMobile() then
        LiteDownloadManager:CancelAll()
    end
    return nil
end

function ExpansionPackCoordinatorModule:GetExpansionPackTableDataList()
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then
                return {}
            else
                return Module.WeGameSDK:GetExpansionPackTableDataList()
            end
        end
    end
    return {}
end

function ExpansionPackCoordinatorModule:GetExpansionPackTableDataListCnt()
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then
                return 0
            else
                return Module.WeGameSDK:GetExpansionPackTableDataListCnt()
            end
        end
    end
    return 0
end

function ExpansionPackCoordinatorModule:GetPlatformExpansionPackInfoByPackID(packID)
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then
                return nil
            else
                return Module.WeGameSDK:GetWeGameExpansionPackInfoByPackID(packID)
            end
        end
    end
    return nil

end

function ExpansionPackCoordinatorModule:AsyncInstallExpansionPack(packID)
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then
                return false
            else
                return Module.WeGameSDK:AsyncInstallExpansionPack(packID)
            end
        end
    end
    return false
end

function ExpansionPackCoordinatorModule:AsyncPauseInstallExpansionPack(packID)
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then
                return false
            else
                return Module.WeGameSDK:AsyncPauseInstallExpansionPack(packID)
            end
        end
    end
    return false
end

function ExpansionPackCoordinatorModule:AsyncResumeInstallExpansionPack(packID)
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then
                return false
            else
                return Module.WeGameSDK:AsyncResumeInstallExpansionPack(packID)
            end
        end
    end
    return false
end

function ExpansionPackCoordinatorModule:MakeDownloadProgressInfoByPackInfo(packInfo)
    local downloadInfo = {}
    downloadInfo["FormatSize"] = "- / -MB"
    downloadInfo["PercentValue"] = 0.0
    downloadInfo["FormatNowSize"] = "-MB"
    downloadInfo["FormatTotal2SubSize"] = "-MB"
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then
                return downloadInfo
            else
                return Module.WeGameSDK:MakeDownloadProgressInfoByPackInfo(packInfo)
            end
        end
    end
    return downloadInfo

end

function ExpansionPackCoordinatorModule:_OnPackStateChanged(packId,packState)
    if packState == Module.ExpansionPackCoordinator.Config.PackState.Installed then
        --self:_MountPackByPackId(packId)
    end
end

function ExpansionPackCoordinatorModule:_MountPackByPackId(packId)
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then
                loginfo("do nothing")
            else
                if not Module.WeGameSDK:IsPackMounted(packId) then
                    Module.WeGameSDK:MountPakFilesByPackId(packId)
                end
            end
        end
    end
end

function ExpansionPackCoordinatorModule:IsPackMounted(packID)
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then
                loginfo("do nothing")
            else
                return Module.WeGameSDK:IsPackMounted(packID)
            end
        end
    end
    return false
end

function ExpansionPackCoordinatorModule:CheckIsMapsDownload(mapList,bFromRoom)
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then

            else
                return Module.WeGameSDK:CheckIsMapsDownload(mapList, bFromRoom)
            end
        elseif IsInEditor() and not IsHD() then
            return LiteDownloadManager:CheckIsMapsDownload(mapList, bFromRoom)
        end
    elseif IsMobile() then
        return LiteDownloadManager:CheckIsMapsDownload(mapList, bFromRoom)
    end
    return true
end

function ExpansionPackCoordinatorModule:GetQuestNameByModuleName(packID)
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then

            else
                return Module.WeGameSDK:GetQuestNameByModuleName(packID)
            end
        elseif IsInEditor() and not IsHD() then
            return LiteDownloadManager:GetQuestNameByModuleName(packID)
        end
    elseif IsMobile() then
        return LiteDownloadManager:GetQuestNameByModuleName(packID)
    end
    return ""
end

function ExpansionPackCoordinatorModule:GetWeaponPartItemResDownloaded()
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then

            else

            end
        elseif IsInEditor() and not IsHD() then
            return LiteDownloadManager:GetWeaponPartItemResDownloaded()
        end
    elseif IsMobile() then
        return LiteDownloadManager:GetWeaponPartItemResDownloaded()
    end
    return true
end

function ExpansionPackCoordinatorModule:GetDownloadCategary(ItemID)
    if PLATFORM_WINDOWS then
        if IsWeGameEnabled() then
            if VersionUtil.IsGameChannelSteam() then

            else
                return Module.WeGameSDK:GetDownloadCategary(ItemID)
            end
        elseif IsInEditor() and not IsHD() then
            return LiteDownloadManager:GetDownloadCategary(ItemID)
        end
    elseif IsMobile() then
        return LiteDownloadManager:GetDownloadCategary(ItemID)
    end
    return "None"
end



return ExpansionPackCoordinatorModule
