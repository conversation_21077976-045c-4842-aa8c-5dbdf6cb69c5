----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaFManager)
----- LOG FUNCTION AUTO GENERATE END -----------

local TableWhitelistInGame = require "DFM.YxFramework.Managers.Table.TableWhitelistInGame"
local TableWhiteList = require "DFM.YxFramework.Managers.Table.TableWhitelist"

local ManagerBase = require("DFM.YxFramework.Managers.ManagerBase")
local LuaPerfTool = require "DFM.YxFramework.Plugin.Performance.LuaPerfTool"

---@class TableManager : ManagerBase
local TableManager = class("TableManager", ManagerBase)

---------------------------------------------------------------------------------------------------------------
--- 表格管理器，负责加载表格，并且默认Cache转换成LuaTable的表格
--- [TableManager]
---------------------------------------------------------------------------------------------------------------

--local UDataTableManager = import "DataTableSystemImpl"
--local UDataTableSystemHelper = import "DataTableSystemHelper"



function TableManager:Ctor()

    self._cachedTable = {}
    self._cachedDelegate = {}

    self._fAllTablesLoaded = nil
    self._caller = nil

    self._loadingNum = 0
    -- self._all
    -- self._originData = {}

    LuaPerfTool.AddObjFuncCache(self, "GetTable", "_InternalGetTable")
end
 
function TableManager:Destroy()
    self:_RemoveWaitingTimer()
end

local function _idx(t, k)
    local originData = rawget(t, "_Dat")
    local v = originData[k]
    if type(v) == GlobalStr.USERDATA and type(v.Table) == GlobalStr.FUNCTION then
        t[k] = v:TableExtend()
    else
        t[k] = v
    end
    return v
end
local dataTableItemMeta = {__index = _idx}

local DataTableManagerInstance = nil

function TableManager:GetDataTableManager()
    if DataTableManagerInstance ~= nil then
        return DataTableManagerInstance
    end
    local UDataTableManager = import "DataTableSystemImpl"
    local useLiteTableManager = UDataTableManager and UDataTableManager:IsUseLiteTableManager()
    local DataTableManager = import "DataTableSystemManagerLite"
    DataTableManagerInstance = DataTableManager:Get()
    DataTableManagerInstance.OnTableReloadDelegate:Add(self._OnReloadTable, self)
    DataTableManagerInstance.OnPreloadFinishDelegate:Add(self._OnPreloadTableFinish, self)
    if DataTableManagerInstance.OnTableResetDelegate then
        DataTableManagerInstance.OnTableResetDelegate:Add(self._OnResetTable, self)
    end
    return DataTableManagerInstance
end

function TableManager:_OnReloadTable(tableName)
    loginfo("GameCoreDataTable: [TableManager _OnReloadTable] tableName:", tableName)
    self:EmptyTable(tableName)
end

function TableManager:_OnResetTable(tableName)
    loginfo("GameCoreDataTable: [TableManager _OnResetTable] tableName:", tableName)
    self:EmptyTable(tableName)
end

---@param useSceneType EDataTableUseScene
---@param asyncLoadingNum number 
function TableManager:_OnPreloadTableFinish(useSceneType, asyncLoadingNum)
    loginfo("GameCoreDataTable: [TableManager _OnPreloadTableFinish] useSceneType and num: ", useSceneType, asyncLoadingNum)

    self:_DoPreloadCallback()
end

function TableManager:_OnPreloadTimeout()
    logerror("GameCoreDataTable: [TableManager _OnPreloadTimeout] preload timeout!")
    for path, delegate in pairs(self._cachedDelegate) do
        logerror("GameCoreDataTable: [TableManager _OnPreloadTimeout] Datatable not loaded for some reason: ", path)
    end

    self:_DoPreloadCallback()
end

function TableManager:_DoPreloadCallback()
    if self._fAllTablesLoaded then
        self._fAllTablesLoaded(self._caller)
        self._fAllTablesLoaded = nil
    end
    self:_RemoveWaitingTimer()
end

function TableManager:_RemoveWaitingTimer()
    if self._waitingTimer then
        Timer.CancelDelay(self._waitingTimer)
        self._waitingTimer = nil
    end
end

---@param path string 表格名称，表格名称是存放在DataTableAssetInfo里的RowName，文档：https://iwiki.woa.com/pages/viewpage.action?pageId=4007622998
---TODO:服务器表格不一致时热更新表格
function TableManager:GetTable(path)
    return self:_InternalGetTable(path)
end

function TableManager:_InternalGetTable(path, bReinit)
    local luaTable = self._cachedTable[path]
    if luaTable then
        if not bReinit then
            return luaTable
        end
    else
        luaTable = {}
    end

    loginfo("GameCoreDataTable: [TableManager _InternalGetTable] cant find table [", path,"]  in cache, try to load from c++")
    local dataTableMgr = self:GetDataTableManager()
    if not dataTableMgr then
        return nil
    end

    local originData = dataTableMgr:GetDataTable(path)
    if originData ~= nil then
        local dataTableForLua = FLuaDataTableHelper.UnfoldDataTable(originData)
        local bTableUseLessGCPairs = TableWhiteList.IsTableUseLessGCPairs(path)
        if bTableUseLessGCPairs then
            loginfo("GameCoreDataTable: [TableManager _InternalGetTable] use less gc pairs", path) 
        end
        dataTableForLua:GetAllRows(luaTable, bTableUseLessGCPairs, true)
        self._cachedTable[path] = luaTable
        return luaTable
    else
        LogUtil.LogMsgBox("[TableManager] load table fail: ", path)
    end
    return nil
end

function TableManager:_ClearLuaTableRowCache()
    loginfo("GameCoreDataTable: [TableManager _ClearLuaTableRowCache]")
    for k,v in pairs(self._cachedTable) do
        v:ClearAllRowsCache()
    end
end

function TableManager:EmptyTable(tableName)
    local lowerCaseTableName = string.lower(tableName)
    for k,v in pairs(self._cachedTable) do
        if string.lower(k) == lowerCaseTableName then
            self:_InternalEmptyTable(v, k)
        end
    end
end

local accessTableAfterClear = function(t)
    local mt = getmetatable(t)
    local path = mt.__path
    assert(path)

    setmetatable(t, nil)
    local ret = Facade.TableManager:_InternalGetTable(path, true)
    
    assert(ret == t)
end

local metaIndexAfterClear = function(t, k)
    accessTableAfterClear(t)
    return t[k]
end

local metaPairsAfterClear = function(t, k)
    accessTableAfterClear(t)
    return pairs(t)
end

local metaIpairsAfterClear = function(t, k)
    accessTableAfterClear(t)
    return ipairs(t)
end

function TableManager:_InternalEmptyTable(t, path)
    loginfo("GameCoreDataTable: [TableManager _InternalEmptyTable]", t, path)

    local oldMt = getmetatable(t)
    local newMt = {}
    newMt.__oldMt = oldMt
    newMt.__path = path
    newMt.__index = metaIndexAfterClear
    newMt.__pairs = metaPairsAfterClear
    newMt.__ipairs = metaIpairsAfterClear

    table.clear(t)
    setmetatable(t, newMt)
end

function UECall_EmptyTable(path)
    if DataTableManagerInstance.OnTableResetDelegate == nil then
        logformat("GameCoreDataTable: [TableManager UECall_EmptyTable] path: %s", path)
        Facade.TableManager:EmptyTable(path)
    end
end

---@param path string 表格名称，表格名称是存放在DataTableAssetInfo里的RowName，文档：https://iwiki.woa.com/pages/viewpage.action?pageId=4007622998
---@param key string 表格键

local emptyRow = {}
function TableManager:GetRowByKey(path, key)
    local dataTable = self:GetTable(path)
    local dataRow = emptyRow
    if dataTable then
        dataRow = dataTable[key]
    else
        logerror("GameCoreDataTable: [TableManager GetRowByKey] cant find table: ", path, key)
    end
    return dataRow
end

function TableManager:GetRowColumnsByKey(path, key, ...)
    local dataTable = self:GetTable(path)
    assert(dataTable)
    return dataTable:GetRowMultiFields(key, ...)
end

---@param gameFlowType EGameFlowStageType
function TableManager:OnGameFlowChangeLeave(gameFlowType)
end

---@param gameFlowType EGameFlowStageType
function TableManager:OnGameFlowChangeEnter(gameFlowType)
    self:_ClearLuaTableRowCache()
end

function TableManager:OnLoadingFrontend2Game(gameFlowType)
    self:_OnLoadTablesInGame()
end

function TableManager:_OnLoadTablesInGame()
    if self._cachedTable == nil then
        return
    end
    loginfo("GameCoreDataTable: [TableManager _OnLoadTablesInGame]")
    for key, value in pairs(self._cachedTable) do
        local bWhiteListInGame = TableWhitelistInGame.IsTableWhitelistInGame(key)
        if not bWhiteListInGame and key ~= nil then
            self:EmptyTable(key)
        end
    end
end

return TableManager
