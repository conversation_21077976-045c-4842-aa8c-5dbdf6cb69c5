----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaFUIManagerHUD)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class LuaUIHUDBaseView : LuaUIBaseView
local LuaUIHUDBaseView = ui("LuaUIHUDBaseView")

local ExtendHUDState = require("DFM.YxFramework.Managers.UI.Layer.HUD.ExtendHUDState")
local GameHUDState = import "GameHUDSate"
local ULuautils = import "Luautils"

function LuaUIHUDBaseView:Ctor()
    loginfo("LuaUIHUDBaseView:Ctor ", self._cname)
    
    self.bIsHud = true
    local uiSetting = self:GetUISettings()
    if uiSetting then
        self:SetSafeZone(uiSetting.bNeedHUDSafeZone)
    end
end

local luaNeedInjectFuncsMap = {}
luaNeedInjectFuncsMap.Imp_Update = "Update"
luaNeedInjectFuncsMap.Imp_BeforeOnStartLocalFocus = "BeforeOnStartLocalFocus"
luaNeedInjectFuncsMap.Imp_BeforeOnStopLocalFocus = "BeforeOnStopLocalFocus"
luaNeedInjectFuncsMap.Imp_OnStartLocalFocus = "OnStartLocalFocus"
luaNeedInjectFuncsMap.Imp_OnStopLocalFocus = "OnStopLocalFocus"
luaNeedInjectFuncsMap.Imp_InitAlwaysShowGameHUDState = "InitAlwaysShowGameHUDState"
luaNeedInjectFuncsMap.Imp_InitVisibleGameHudState = "InitVisibleGameHudState"
luaNeedInjectFuncsMap.Imp_InitInVisibleGameHudState = "InitInVisibleGameHudState"
luaNeedInjectFuncsMap.Imp_OnGameHudStateChanged = "OnGameHudStateChanged"

function LuaUIHUDBaseView:GetInjectionList()
    local luaInjectFuncsNames = {
        "Show_Lua",
        "Hide_Lua",
        "IsInShowState_Lua"
    }
    local class = self:Class()
    for luaFuncName, cppFuncName in pairs(luaNeedInjectFuncsMap) do
        if rawget(class, luaFuncName) then
            table.insert(luaInjectFuncsNames, luaFuncName)
        end
    end
    return luaInjectFuncsNames
end

function LuaUIHUDBaseView:TryOpen()
    --在open前的逻辑，在addtoviewport后最先做的逻辑
    if isvalid(self.__cppinst) and self.__cppinst.OnPreOpen then
        self.__cppinst:OnPreOpen()
    end
    --Open
    self._shouldRecoveryVisibility = true
    LuaUIBaseView.Open(self)
    --StartLocalFocus
    local baseHud = UE.BaseHUD.GetHUD(GetWorld())
    if isvalid(baseHud) and isvalid(baseHud:GetLocalFocusActor()) then
        self:StartLocalFocus(baseHud:GetLocalFocusActor())
    end

    local uiSetting = self:GetUISettings()
    if uiSetting and uiSetting.UILayer ~= EUILayer.HUD then
        if uiSetting.UINavID and IsInEditor() and not VersionUtil.IsShipping() then
            logbox("层级配置错误会导致显示问题，继承自LuaUIHUDBaseView的UI需要配置为EUILayer.HUD", UIName2ID.GetBPFullPathByID(uiSetting.UINavID), debug.traceback())
        end
    end

    --根据当前hudstate判断是否需要show
    local hudStateManager = UE.HUDStateManager.Get(GetWorld())
    if isvalid(hudStateManager) then
        local bIsVisible = hudStateManager:IsVisible_Lua(self)
        if bIsVisible then
            LuaUIBaseView.Show(self, false, true)
        else
            LuaUIBaseView.Hide(self, false, true)
        end
    end
end

function LuaUIHUDBaseView:Close()
    local hud = UE.BaseHUD.GetHUD(GetWorld())
    if isvalid(hud) then
        local cppIns = self.__cppinst
        if isvalid(cppIns) then
            hud:RemoveUI(cppIns)
        else
            logwarning("LuaUIHUDBaseView:Close BaseHUD:RemoveUI but cppIns is nil", self._cname)
        end
    end
    LuaUIBaseView.Close(self)
end

------------------------------------由UIManager触发 start------------------------------------
function LuaUIHUDBaseView:OnOpen()
    if isvalid(self.__cppinst) and self.__cppinst.OnOpen then
        self.__cppinst:OnOpen()
    end
end

function LuaUIHUDBaseView:OnClose()
    if isvalid(self.__cppinst) and self.__cppinst.OnClose then
        self.__cppinst:OnClose()
    end
end

function LuaUIHUDBaseView:OnShow()
    if isvalid(self.__cppinst) and self.__cppinst.OnShow then        
        self.__cppinst:OnShow()
    end
end

function LuaUIHUDBaseView:OnHide()
    if isvalid(self.__cppinst) and self.__cppinst.OnHide then
        self.__cppinst:OnHide()
    end
end
------------------------------------由UIManager触发 stop------------------------------------

function LuaUIHUDBaseView:Imp_Show_Lua()
    self:Show(false, true)
end

function LuaUIHUDBaseView:Imp_Hide_Lua()
    self:Hide(false, true)
end

function LuaUIHUDBaseView:Imp_IsInShowState_Lua()
    return self:IsInShowState()
end

function LuaUIHUDBaseView:Destroy()
    if IsHD() then
        self:CheckWantedInputMode(false)
    end

    --- Long 1.0
    -- local cppInst = self:GetCppInst()
	-- if cppInst then
    --     FLuaSimpleBind.DynamicUnbind(cppInst)
    --     loginfo("FLuaSimpleBind.DynamicUnbind(cppInst)")
	-- end
    -- if cppInst and self:HasInjectLua() then
	-- 	ULuautils.ClearLuaInject(self)
	-- end

    loginfo("LuaUIHUDBaseView:Destroy ", self._cname)
end

function LuaUIHUDBaseView:AddStatesToAlwaysShowGameHudState(stateList)
    for _, state in pairs(stateList) do
        self:AddStateToAlwaysShowGameHudState(state)
    end
end

function LuaUIHUDBaseView:AddStatesToVisibleGameHudState(stateList)
    for _, state in pairs(stateList) do
        self:AddStateToVisibleGameHudState(state)
    end
end

function LuaUIHUDBaseView:AddStatesToInVisibleGameHudState(stateList)
    for _, state in pairs(stateList) do
        self:AddStateToInVisibleGameHudState(state)
    end
end

return LuaUIHUDBaseView