----- <PERSON><PERSON><PERSON> FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollection)
----- LOG FUNCTION AUTO GENERATE END -----------


---@class CollectionsHall_Panel_S2 : LuaUIBaseView
local CollectionsHall_Panel_S2 = ui("CollectionsHall_Panel_S2")

function CollectionsHall_Panel_S2:Ctor()
    -- 挂饰
    self._pendantUIList = self:MultiWnd("PlatformPaddingBox_", UIWidgetBase)
    self._pendantUnlockList = {}
    self._pendantUnlockAnimaList = {}
end

function CollectionsHall_Panel_S2:OnInitExtraData(pendantUnlockList, pendantUnlockAnimaList, index)
    self._pendantUnlockList = setdefault(pendantUnlockList, {})
    self._pendantUnlockAnimaList = setdefault(pendantUnlockAnimaList, {})
    self._uiIdex = setdefault(index, 1)
end

local animaList = {
    [1] = {
        "WBP_CollectionsHall_Panel_02_in_01",
        "WBP_CollectionsHall_Panel_02_in_02",
        "WBP_CollectionsHall_Panel_02_in_03",
        "WBP_CollectionsHall_Panel_02_in_04",
    },
    [2] = {
        "WBP_CollectionsHall_Panel_01_in_01",
        "WBP_CollectionsHall_Panel_01_in_02",
        "WBP_CollectionsHall_Panel_01_in_03",
    },
    [3] = {
        "WBP_CollectionsHall_Panel_03_in_01",
        "WBP_CollectionsHall_Panel_03_in_02",
        "WBP_CollectionsHall_Panel_03_in_03",
    },
    [4] = {
        "WBP_CollectionsHall_Panel_04_in_01",
        "WBP_CollectionsHall_Panel_04_in_02",
        "WBP_CollectionsHall_Panel_04_in_03",
    },
}

function CollectionsHall_Panel_S2:UpdateUnlockStyle()
    for index, ui in ipairs(self._pendantUIList) do
        if self._pendantUnlockList[index] then
            ui:HitTestInvisible()
        else
            ui:Collapsed()
        end

        if self._pendantUnlockAnimaList[index] then
            if animaList[self._uiIdex] then
                if animaList[self._uiIdex][index] and self[animaList[self._uiIdex][index]] then
                    self:PlayAnimation(self[animaList[self._uiIdex][index]], 0, 1, EUMGSequencePlayMode.Forward, 1, false)
                else
                    logerror(" CollectionsHall_Panel_S2:UpdateUnlockStyle Anima Error: ", self._uiIdex, animaList[self._uiIdex][index])
                end
            else
                logerror(" CollectionsHall_Panel_S2:UpdateUnlockStyle self._uiIdex Error: ", self._uiIdex)
            end
        end
    end
end

return CollectionsHall_Panel_S2