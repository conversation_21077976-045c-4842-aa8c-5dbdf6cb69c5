----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMJump)
----- LOG FUNCTION AUTO GENERATE END -----------

local JumpInfoStruct = require "DFM.Business.Module.JumpModule.Data.JumpInfoStruct"
local JumpSingleStruct = require "DFM.Business.Module.JumpModule.Data.JumpSingleStruct"
local JumpConfig = require "DFM.Business.Module.JumpModule.JumpConfig"
local SystemUtil = require "DFM.YxFramework.Managers.UI.Util.UISystemUtil"
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
local ActivityLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"
local EDepartmentType        = Module.Shop.Config.EDepartmentType
local function log(...)
    loginfo("[JumpLogic]", ...)
end

local JumpLogic = {}

JumpLogic.JumpTo = function(moduleName, jumpArgs, jumpCallback, jumpType, backType)
    jumpType = setdefault(jumpType, JumpConfig.EJumpType.KeepModuleSingle)
    backType = setdefault(backType, JumpConfig.EJumpBackType.CloseThisModule)

    -- 触发跳转事件，需要自行管理的界面监听事件，进行关闭
    JumpConfig.evtJumpTo:Invoke()

    -- 跳转时确保所有pop层ui关闭
    Module.ItemDetail:CloseAllPopUI()
    Module.ItemDetail:CloseAllCacheItemDetailPanelHandle()
    Facade.UIManager:GetLayerControllerByType(EUILayer.Pop):Reset()

    local stackUIController = Facade.UIManager:GetLayerControllerByType(EUILayer.Stack)
    local beginStackDepth = stackUIController:GetStackUICount()
    local uiNavIdList = {}
    local jumpInfo = JumpInfoStruct:NewIns(moduleName, beginStackDepth, uiNavIdList, jumpType, backType)
    -- 检查跳转策略
    local forceNotCloseUI = false -- TODO 特殊处理逻辑
    local bJumpToShop = false
    local bJumpToSandBox = false
    local lastSameJumpInfo = Module.Jump.Field:PopSameJumpInfo(jumpInfo)
    if moduleName == JumpConfig.EJumpToModule.Shop then
        bJumpToShop = true
    elseif moduleName == JumpConfig.EJumpToModule.SandBoxMap then
        bJumpToSandBox = true
    end
    if lastSameJumpInfo then
        -- TODO 对商人的跳转加特殊判断逻辑，如果UI已存在，不帮她关，由商人内部自己处理
        --      之后开单对stackUIContriller的pop逻辑补充处理，加上不播关闭动画的pop逻辑
        local curUINavIdList = lastSameJumpInfo.uiNavIdList
        for _, navId in ipairs(curUINavIdList) do
            if navId == UIName2ID.ShopMainPanel then
                forceNotCloseUI = true
            end
        end

        if forceNotCloseUI then
            local needPopNum = beginStackDepth - lastSameJumpInfo.endStackUIDepth
            log("JumpTo:", moduleName, "need close stack ui num:", needPopNum)
            if needPopNum < 0 then
                log(needPopNum, "JumpLogic.JumpTo pop old ui error, check jump logic")
            else
                Module.Jump.Field:SetIngnoreStackUIPopState(true)
                while needPopNum > 0 do
                    stackUIController:PopUI()
                    needPopNum = needPopNum - 1
                end
                Module.Jump.Field:SetIngnoreStackUIPopState(false)
            end
        else
            local needPopNum = beginStackDepth - lastSameJumpInfo.beginStackUIDepth
            log(
                "JumpTo:",
                moduleName,
                "need close stack ui num:",
                needPopNum,
                beginStackDepth,
                lastSameJumpInfo.beginStackUIDepth
            )
            if needPopNum < 0 then
                log(needPopNum, "JumpLogic.JumpTo pop old ui error, check jump logic")
            else
                Module.Jump.Field:SetIngnoreStackUIPopState(true)
                while needPopNum > 0 do
                    stackUIController:PopUI()
                    needPopNum = needPopNum - 1
                end
                Module.Jump.Field:SetIngnoreStackUIPopState(false)
            end
        end
    else
        local targetUIInst
        if bJumpToShop then
            -- TODO 在跳转之前已经打开商人界面了，则关闭所有跳转路径上的ui
            targetUIInst = Facade.UIManager:GetStackUIByUINavId(UIName2ID.ShopMainPanel)
        elseif bJumpToSandBox then
            targetUIInst = Facade.UIManager:GetStackUIByUINavId(UIName2ID.SandBoxMapView)
        end

        if targetUIInst then
            forceNotCloseUI = true
            Module.Jump.Field:SetIngnoreStackUIPopState(true)
            local topJumpInfo = Module.Jump.Field:GetTopInfo()
            while topJumpInfo ~= nil do
                beginStackDepth = stackUIController:GetStackUICount()
                local needPopNum = beginStackDepth - topJumpInfo.beginStackUIDepth
                if needPopNum < 0 then
                    log(needPopNum, "JumpLogic.JumpTo pop old ui error, check jump logic")
                else
                    while needPopNum > 0 do
                        stackUIController:PopUI()
                        needPopNum = needPopNum - 1
                    end
                end

                Module.Jump.Field:Pop()
                topJumpInfo = Module.Jump.Field:GetTopInfo()
            end
            Module.Jump.Field:SetIngnoreStackUIPopState(false)
        end

        if bJumpToSandBox then
            -- 关闭至最早的一个sandBoxMap
            Module.Jump.Field:SetIngnoreStackUIPopState(true)
            while Facade.UIManager:GetStackUIByUINavId(UIName2ID.SandBoxMapView) ~= nil do
                stackUIController:PopUI()
            end
            Module.Jump.Field:SetIngnoreStackUIPopState(false)
        end
    end

    beginStackDepth = stackUIController:GetStackUICount()
    -- 打开新的界面
    if jumpCallback then
        if jumpArgs then
            if type(jumpArgs) == "table" then
                uiNavIdList = jumpCallback(table.unpack(jumpArgs))
            else
                uiNavIdList = jumpCallback(jumpArgs)
            end
        else
            uiNavIdList = jumpCallback()
        end
    else
        if jumpArgs then
            if type(jumpArgs) == "table" then
                uiNavIdList = Module[moduleName]:Jump(table.unpack(jumpArgs))
            else
                uiNavIdList = Module[moduleName]:Jump(jumpArgs)
            end
        else
            uiNavIdList = Module[moduleName]:Jump()
        end
    end
    if not uiNavIdList or #uiNavIdList == 0 then
        log("jump fail, jump open uiNavIdList is empty")
        return
    end
    --beginStackDepth = stackUIController:GetStackUICount()
    if forceNotCloseUI then
        beginStackDepth = beginStackDepth - #uiNavIdList
    end
    jumpInfo = JumpInfoStruct:NewIns(moduleName, beginStackDepth, uiNavIdList, jumpType, backType)
    Module.Jump.Field:PushJumpInfo(jumpInfo)
    log("JumpTo:", moduleName)
end

JumpLogic.ProcessOnStackUIPop = function(uiNavId, operationType)
    if operationType ~= EStackAction.Pop then
        return
    end
    if Module.Jump.Field:GetIngnoreStackUIPopState() then
        return
    end

    local stackUIController = Facade.UIManager:GetLayerControllerByType(EUILayer.Stack)
    local beginStackDepth = stackUIController:GetStackUICount()
    local topJumpInfo = Module.Jump.Field:GetTopInfo()
    if topJumpInfo then
        if topJumpInfo.endStackUIDepth > beginStackDepth then
            if topJumpInfo.backType == JumpConfig.EJumpBackType.CloseThisModule then
                log("ProcessOnStackUIPop, close jump module:", topJumpInfo.jumpToModule)
                Module.Jump.Field:SetIngnoreStackUIPopState(true)
                local needPopNum = beginStackDepth - topJumpInfo.beginStackUIDepth
                while needPopNum > 0 do
                    stackUIController:PopUI()
                    needPopNum = needPopNum - 1
                end
                Module.Jump.Field:Pop()
                Module.Jump.Field:SetIngnoreStackUIPopState(false)
            end
        end
    end
    JumpLogic.SpecialProcessOnStackUIPopForWeaponAssembly()
end

JumpLogic.SpecialProcessOnStackUIPopForWeaponAssembly = function()
end

JumpLogic.CheckCanJumpByID = function(jumpID, checkJumpArgs, needTip)
    log("JumpLogic.checkcanJumpByID:", jumpID)
    jumpID = tonumber(jumpID)
    needTip = setdefault(needTip, false)
    checkJumpArgs = setdefault(checkJumpArgs, {})
    local cfg = Server.JumpServer:GetJumpConfigById(jumpID)
    if cfg == nil then
        if needTip then
            Module.CommonTips:ShowSimpleTip(Module.Jump.Config.Loc.CantFindJumpCfg)
        end
        log("JumpLogic.checkCanJumpByID, CantFindJumpcfg:", jumpID)
        return false
    end
    --检查配置合法\跳转是否可行
    if cfg.StartTime > 0 and cfg.EndTime > 0 then
        local startTime = cfg.StartTime
        local endTime = cfg.EndTime
        local curTime = Facade.ClockManager:GetLocalTimestamp()
        if curTime < startTime or curTime > endTime then
            if needTip then
                Module.CommonTips:ShowSimpleTip(Module.Jump.Config.Loc.OutTimeToJump)
            end
            log("JumpLogic.checkcanJumpByID,outTime:", jumpID, startTime, endTime)
            return false
        end
    end

    local firstEnum = cfg.JumpFirstEnum
    local secondEnum = cfg.JumpSecendEnum
    local thridEnum = cfg.JumpThridEnum
    local fourthEnum = cfg.JumpFourthEnum

    local arr = {}
    if cfg.ExParams ~= "" then
        arr = string.split(cfg.ExParams, ",")
    end

    local checkModuleSwitcherIsOpen = function(switchSystemID)
        if Module.ModuleSwitcher:CheckModuleSwitcher(switchSystemID) == EFirstLockResult.Open then
            return true
        end
        return false
    end

    local checkModuleUnlock = function(switchModuleID)
        local moduleUnlockInfo = Module.ModuleUnlock:GetModuleUnlockInfoById(switchModuleID)
        if not moduleUnlockInfo.bIsUnlocked then
            --Module.CommonTips:ShowSimpleTip(moduleUnlockInfo.unlocktips)
            return false
        end
        return true
    end

    local isNotInSafeZone = function()
        if
            (firstEnum == Module.Jump.Config.EJumpFirstEnum.KeepCurrent or
                firstEnum == Module.Jump.Config.EJumpFirstEnum.KeepCurrentAllPop) and
                Facade.GameFlowManager:GetCurrentGameFlow() ~= EGameFlowStageType.SafeHouse
         then
            log("JumpLogic.checkCanJumpByID, isNotInSafeZone:", jumpID)
            return false
        end
        return true
    end

    local isNotInMP = function()
        if
            (firstEnum == Module.Jump.Config.EJumpFirstEnum.KeepCurrent or
                firstEnum == Module.Jump.Config.EJumpFirstEnum.KeepCurrentAllPop) and
                Facade.GameFlowManager:GetCurrentGameFlow() ~= EGameFlowStageType.Lobby
         then
            log("JumpLogic.checkCanJumpByID, isNotInMP:", jumpID)
            return false
        end
        return true
    end

    local currentStage = Facade.GameFlowManager:GetCurrentGameFlow()
    if firstEnum == Module.Jump.Config.EJumpFirstEnum.SOL or firstEnum == Module.Jump.Config.EJumpFirstEnum.SOLAllPop then
        if not checkModuleSwitcherIsOpen(SwitchSystemID.SwitchSyetemLobbyEntranceSOL) then
            if needTip then
                Module.CommonTips:ShowSimpleTip(Module.Jump.Config.Loc.CantJumpToMode)
            end
            return false
        end

        local solModuelName = LiteDownloadManager:GetSystemDownloadModuleName(EDownloadSystem.SOLModule)
        local bDownloaded = LiteDownloadManager:IsDownloadedByModuleName(solModuelName)
        if (not checkJumpArgs.bNoShowLoadWindow) and (not bDownloaded) then
            local fConfirm = function()
                LiteDownloadManager:DownloadByModuleName(solModuelName)
            end
            local mainTitle =
                StringUtil.SequentialFormat(
                Module.LitePackage.Config.Loc.ModeResourceDownloadTip,
                Module.IrisSafeHouse.Config.Loc.IrisModeTitle
            )
            local showStr =
                string.format("%.1fMB", LiteDownloadManager:GetRemainderSizeByModuleName(solModuelName) / (1024 * 1024))
            local secondTitle =
                StringUtil.SequentialFormat(Module.LitePackage.Config.Loc.ResourceDownloadDetail, showStr)
            Facade.UIManager:AsyncShowUI(
                UIName2ID.SandBoxMapSOLMoneyLimitWindow,
                nil,
                nil,
                mainTitle,
                secondTitle,
                false,
                fConfirm
            )
            return false
        end
    elseif
        (firstEnum == Module.Jump.Config.EJumpFirstEnum.MP or firstEnum == Module.Jump.Config.EJumpFirstEnum.MPAllPop) and
            not checkModuleSwitcherIsOpen(SwitchSystemID.SwitchSyetemLobbyEntranceMP)
     then
        if needTip then
            Module.CommonTips:ShowSimpleTip(Module.Jump.Config.Loc.CantJumpToMode)
        end
        return false
    elseif
        (firstEnum == Module.Jump.Config.EJumpFirstEnum.BHD or firstEnum == Module.Jump.Config.EJumpFirstEnum.BHDAllPop) and
            not checkModuleSwitcherIsOpen(SwitchSystemID.SwitchSystemBHD)
     then
        if needTip then
            Module.CommonTips:ShowSimpleTip(Module.Jump.Config.Loc.CantJumpToMode)
        end
        return false
    end
    local _CheckCanJump = {
        [Module.Jump.Config.EJumpSecondEnum.Activity] = function(firstEnum, thridEnum, fourthEnum, arr)
            if not checkModuleSwitcherIsOpen(SwitchSystemID.SwitchSyetemActivity) then
                return false, Module.Jump.Config.Loc.CantJumpToModule
            end
            if #arr > 0 and arr[1] then
                log("JumpLogic.checkCanJumpByID ActivityID:", tonumber(arr[1]))
                local ret = Server.ActivityServer:CheckActivityExistsByID(tonumber(arr[1]))
                if not ret then
                    return false, Module.Jump.Config.Loc.CantJumpToActivity
                end
                return ret
            end
            return true
        end,
        [Module.Jump.Config.EJumpSecondEnum.Store] = function(firstEnum, thridEnum, fourthEnum, arr)
            if #arr > 0 and arr[1] then
                local ret = Server.StoreServer:CheckStorePropExist(tonumber(arr[1]))
                if not ret then
                    log("JumpLogic.checkCanJumpByID, CantJumpToStore:", tonumber(arr[1]))
                    return false, Module.Jump.Config.Loc.CantJumpToStore
                end
                return ret
            end
            if not checkModuleSwitcherIsOpen(SwitchSystemID.SwitchSyetemStore) then
                return false, Module.Jump.Config.Loc.CantJumpToModule
            end
            if not checkModuleUnlock(SwitchModuleID.ModuleShop) then
                return false, Module.ModuleUnlock:GetModuleUnlockInfoById(SwitchModuleID.ModuleShop).unlocktips
            end
            return true
        end,
        [Module.Jump.Config.EJumpSecondEnum.BattlePass] = function(firstEnum, thridEnum, fourthEnum, arr)
            if not checkModuleSwitcherIsOpen(SwitchSystemID.SwitchSyetemBattlePass) then
                return false, Module.Jump.Config.Loc.CantJumpToModule
            end
            if not checkModuleUnlock(SwitchModuleID.ModuleBattlePass) then
                return false, Module.ModuleUnlock:GetModuleUnlockInfoById(SwitchModuleID.ModuleBattlePass).unlocktips
            end
            if #arr > 2 and arr[3] then
                local ret = Server.BattlePassServer:IsRewardIDExist(tonumber(arr[3]))
                if not ret then
                    return false, Module.Jump.Config.Loc.CantJumpToBP
                end
                return ret
            end
            return true
        end,
        [Module.Jump.Config.EJumpSecondEnum.Hero] = function(firstEnum, thridEnum, fourthEnum, arr)
            if not checkModuleSwitcherIsOpen(SwitchSystemID.SwitchSystemHeroLobby) then
                return false, Module.Jump.Config.Loc.CantJumpToModule
            end
            return true
        end,
        [Module.Jump.Config.EJumpSecondEnum.Market] = function(firstEnum, thridEnum, fourthEnum, arr)
            if not checkModuleSwitcherIsOpen(SwitchSystemID.SwitchSystemMarket) then
                return false, Module.Jump.Config.Loc.CantJumpToModule
            end
            if not checkModuleUnlock(SwitchModuleID.ModuleMarket) then
                return false, Module.ModuleUnlock:GetModuleUnlockInfoById(SwitchModuleID.ModuleMarket).unlocktips
            end
            if not isNotInSafeZone() then
                return false, Module.Jump.Config.Loc.CantJumpToMP
            end
            return true
        end,
        [Module.Jump.Config.EJumpSecondEnum.Rank] = function(firstEnum, thridEnum, fourthEnum, arr)
            if not checkModuleUnlock(SwitchModuleID.ModuleRankSOL) then
                return false, Module.ModuleUnlock:GetModuleUnlockInfoById(SwitchModuleID.ModuleRankSOL).unlocktips
            end
            if not isNotInSafeZone() then
                return false, Module.Jump.Config.Loc.CantJumpToMP
            end
            return true
        end,
        [Module.Jump.Config.EJumpSecondEnum.SandBoxMap] = function(firstEnum, thridEnum, fourthEnum, arr)
            if not isNotInSafeZone() then
                return false, Module.Jump.Config.Loc.CantJumpToMP
            end
            return true
        end,
        [Module.Jump.Config.EJumpSecondEnum.MPModeSelect] = function(firstEnum, thridEnum, fourthEnum, arr)
            if not isNotInSafeZone() then
                return false, Module.Jump.Config.Loc.CantJumpToMP
            end
            return true
        end,
        [Module.Jump.Config.EJumpSecondEnum.Quest] = function(firstEnum, thridEnum, fourthEnum, arr)
            if thridEnum or (thridEnum and fourthEnum) then
                local lineInfo = Server.QuestServer:GetQuestLineInfoById(thridEnum)
                if thridEnum and lineInfo and not lineInfo:IsLineOpend() then
                    return false, Module.Jump.Config.Loc.QuestLineLockToJump
                end
                local questInfo = Server.QuestServer:GetQuestInfoById(fourthEnum)
                if fourthEnum and questInfo and not Server.QuestServer:IsQuestAcceptable(questInfo) then
                    return false, Module.Jump.Config.Loc.QuestLockToJump
                end
            end
            if not isNotInSafeZone() then
                return false, Module.Jump.Config.Loc.CantJumpToMP
            end
            return true
        end,
        [Module.Jump.Config.EJumpSecondEnum.QuestSeason] = function(firstEnum, thridEnum, fourthEnum, arr)
            if not isNotInSafeZone() then
                return false, Module.Jump.Config.Loc.CantJumpToMP
            elseif not checkModuleSwitcherIsOpen(SwitchSystemID.SwitchSystemSeasonTask) then
                return false, Module.Jump.Config.Loc.OutTimeToJump
            end
            return true
        end,
        [Module.Jump.Config.EJumpSecondEnum.LiveRadio] = function(firstEnum, thridEnum, fourthEnum, arr)
            return Module.LiveRadio:GetIsShowEntrance()
        end,
        [Module.Jump.Config.EJumpSecondEnum.Tournament] = function(firstEnum, thridEnum, fourthEnum, arr)
            if not isNotInMP() then
                return false, Module.Jump.Config.Loc.CantJumpToSOL
            end
            return Module.Tournament:IsTournamentUnlocked()
        end,
        [Module.Jump.Config.EJumpSecondEnum.Shop] = function(firstEnum, thridEnum, fourthEnum, arr)
            if not isNotInSafeZone() then
                return false, Module.Jump.Config.Loc.CantJumpToMP
            end
            return true
        end,
        [Module.Jump.Config.EJumpSecondEnum.Gunsmith] = function(firstEnum, thridEnum, fourthEnum, arr)
            if not isNotInMP() then
                return false, Module.Jump.Config.Loc.CantJumpToSOL
            end
            return true
        end,
        [Module.Jump.Config.EJumpSecondEnum.BlackSite] = function(firstEnum, thridEnum, fourthEnum, arr)
            if not isNotInSafeZone() then
                return false, Module.Jump.Config.Loc.CantJumpToMP
            end
            return true
        end
    }

    local checkCb = _CheckCanJump[secondEnum]
    if checkCb then
        local result, resultTB = checkCb(firstEnum, thridEnum, fourthEnum, arr)
        if not result then
            if needTip and resultTB then
                Module.CommonTips:ShowSimpleTip(resultTB)
            end
            return false
        end
    end

    return true
end

--@param jumpArgs: 外部可变参数，将会替换表格数据中的可变参数
JumpLogic.JumpByID = function(jumpID, jumpArgs, jumpCallback, jumpSource)
    if Server.MatchServer:GetIsMatching() then
        Module.CommonTips:ShowSimpleTip(Module.Social.Config.Loc.InMatching)
        return
    end

    log("JumpLogic JumpByID:", jumpID)
    jumpID = tonumber(jumpID)
    local canJump = JumpLogic.CheckCanJumpByID(jumpID, nil, true)
    if canJump == false then
        log("JumpLogic CheckCanJumpByID: false ", jumpID)
        if jumpCallback then
            jumpCallback(Module.Jump.Config.EJumpResultType.Failed)
        end
        return
    end

    local cfg = Server.JumpServer:GetJumpConfigById(jumpID)
    if cfg == nil then
        log("JumpLogic cfg = nil ", jumpID)
        return
    end
    --保存初始化数据
    local jumpSingleData = JumpSingleStruct:NewIns(Module.Jump.Field:GetNextIndex(), cfg, jumpArgs, jumpCallback)
    jumpSingleData.jumpSource = jumpSource
    Module.Jump.Field:PushJumpSingleData(jumpSingleData)
    Module.Jump.Field:SetCurrentJumpIndex(jumpSingleData:GetIndexID())

    JumpLogic.JumpMode()
end

JumpLogic.JumpMode = function()
    --处理一级跳转
    local currentJumpIndex = Module.Jump.Field:GetCurrentJumpIndex()
    local cfg = Module.Jump.Field:GetJumpSingleCfg(currentJumpIndex)
    if cfg == nil then
        logwarning("数值保存有问题 / 不能直接外部模块调用改接口")
        Module.Jump.Field:ClearJumpSingle(currentJumpIndex)
        Module.Jump.Field:SetCurrentJumpIndex(0)
        return
    end
    local firstEnum = cfg.JumpFirstEnum
    local secondEnum = cfg.JumpSecendEnum
    local currentStage = Facade.GameFlowManager:GetCurrentGameFlow()

    if firstEnum == Module.Jump.Config.EJumpFirstEnum.SOL or firstEnum == Module.Jump.Config.EJumpFirstEnum.SOLAllPop then
        if currentStage == EGameFlowStageType.ModeHall then
            log("JumpLogic JumpMode = ModeHall ")
            Module.Jump.Field:SetJumpSingleStage(currentJumpIndex, Module.Jump.Config.EJumpSingleStage.Wait)
            Module.IrisSafeHouse:SetForceEnter3DSafeHouse(false)
            Module.IrisSafeHouse.Config.flowEvtToEnterSafeHouse:Invoke()
        elseif currentStage == EGameFlowStageType.SafeHouse then
            if firstEnum == Module.Jump.Config.EJumpFirstEnum.SOLAllPop then
                Facade.UIManager:CloseAllPopUI()
                Facade.UIManager:PopAllUI(false, false, MapPopAllUIReason2Str.JumpSingleStage)
            end
            if secondEnum == 0 then
                Module.Jump.Field:SetJumpSingleStage(currentJumpIndex, Module.Jump.Config.EJumpSingleStage.End)
                Module.Jump.Field:ClearJumpSingle(currentJumpIndex)
                Module.Jump.Field:SetCurrentJumpIndex(0)
            else
                Module.Jump.Field:SetJumpSingleStage(currentJumpIndex, Module.Jump.Config.EJumpSingleStage.Jumping)
                JumpLogic.JumpSub()
            end
        else
            if currentStage == EGameFlowStageType.Lobby then
                local function fOnConfim()
                    Module.Jump.Field:SetJumpSingleStage(currentJumpIndex, Module.Jump.Config.EJumpSingleStage.Wait)
                    Module.GameMode:TrySwitchMode(Module.GameMode.Config.ESwitchMode.SOL)
                end

                local function fOnCancel()
                    Module.Jump.Field:ClearJumpSingle(currentJumpIndex)
                    Module.Jump.Field:SetCurrentJumpIndex(0)
                end

                Module.CommonTips:ShowConfirmWindow(Module.Jump.Config.Loc.CanMPToSOL, fOnConfim, fOnCancel)
            else
                Module.Jump.Field:SetJumpSingleStage(currentJumpIndex, Module.Jump.Config.EJumpSingleStage.Wait)
                Module.GameMode:TrySwitchMode(Module.GameMode.Config.ESwitchMode.SOL)
            end
        end
    elseif firstEnum == Module.Jump.Config.EJumpFirstEnum.MP or firstEnum == Module.Jump.Config.EJumpFirstEnum.MPAllPop then
        if currentStage == EGameFlowStageType.ModeHall then
            Module.Jump.Field:SetJumpSingleStage(currentJumpIndex, Module.Jump.Config.EJumpSingleStage.Wait)
            Module.IrisSafeHouse.Config.flowEvtToEnterLobby:Invoke()
        elseif currentStage == EGameFlowStageType.Lobby then
            if firstEnum == Module.Jump.Config.EJumpFirstEnum.MPAllPop then
                Facade.UIManager:CloseAllPopUI()
                Facade.UIManager:PopAllUI(false, false, MapPopAllUIReason2Str.JumpSingleStage)
            end

            if secondEnum == 0 then
                Module.Jump.Field:SetJumpSingleStage(currentJumpIndex, Module.Jump.Config.EJumpSingleStage.End)
                Module.Jump.Field:ClearJumpSingle(currentJumpIndex)
                Module.Jump.Field:SetCurrentJumpIndex(0)
            else
                Module.Jump.Field:SetJumpSingleStage(currentJumpIndex, Module.Jump.Config.EJumpSingleStage.Jumping)
                JumpLogic.JumpSub()
            end
        else
            if currentStage == EGameFlowStageType.SafeHouse then
                local function fOnConfim()
                    Module.Jump.Field:SetJumpSingleStage(currentJumpIndex, Module.Jump.Config.EJumpSingleStage.Wait)
                    Module.GameMode:TrySwitchMode(Module.GameMode.Config.ESwitchMode.MP)
                end
                local function fOnCancel()
                    Module.Jump.Field:ClearJumpSingle(currentJumpIndex)
                    Module.Jump.Field:SetCurrentJumpIndex(0)
                end
                Module.CommonTips:ShowConfirmWindow(Module.Jump.Config.Loc.CanSOLToMP, fOnConfim, fOnCancel)
            else
                Module.Jump.Field:SetJumpSingleStage(currentJumpIndex, Module.Jump.Config.EJumpSingleStage.Wait)
                Module.GameMode:TrySwitchMode(Module.GameMode.Config.ESwitchMode.MP)
            end
        end
    elseif
        firstEnum == Module.Jump.Config.EJumpFirstEnum.BHD or firstEnum == Module.Jump.Config.EJumpFirstEnum.BHDAllPop
     then
        if currentStage == EGameFlowStageType.ModeHall then
            Module.Jump.Field:SetJumpSingleStage(currentJumpIndex, Module.Jump.Config.EJumpSingleStage.Wait)
            Module.IrisSafeHouse:EnterBHDHallFlow()
        elseif currentStage == EGameFlowStageType.LobbyBHD then
            if firstEnum == Module.Jump.Config.EJumpFirstEnum.BHDAllPop then
                Facade.UIManager:CloseAllPopUI()
                Facade.UIManager:PopAllUI(false, false, MapPopAllUIReason2Str.JumpSingleStage)
            end
            if secondEnum == 0 then
                Module.Jump.Field:SetJumpSingleStage(currentJumpIndex, Module.Jump.Config.EJumpSingleStage.End)
                Module.Jump.Field:ClearJumpSingle(currentJumpIndex)
                Module.Jump.Field:SetCurrentJumpIndex(0)
            else
                Module.Jump.Field:SetJumpSingleStage(currentJumpIndex, Module.Jump.Config.EJumpSingleStage.Jumping)
                JumpLogic.JumpSub()
            end
        else
            if currentStage == EGameFlowStageType.Lobby or currentStage == EGameFlowStageType.SafeHouse then
                local function fOnConfim()
                    Module.Jump.Field:SetJumpSingleStage(currentJumpIndex, Module.Jump.Config.EJumpSingleStage.Wait)
                    Module.GameMode:TrySwitchMode(Module.GameMode.Config.ESwitchMode.BHD)
                end

                local function fOnCancel()
                    Module.Jump.Field:ClearJumpSingle(currentJumpIndex)
                    Module.Jump.Field:SetCurrentJumpIndex(0)
                end
                local title = Module.Jump.Config.Loc.CanSOLToBHD
                if currentStage == EGameFlowStageType.Lobby then
                    title = Module.Jump.Config.Loc.CanMPToBHD
                end
                Module.CommonTips:ShowConfirmWindow(title, fOnConfim, fOnCancel)
            else
                Module.Jump.Field:SetJumpSingleStage(currentJumpIndex, Module.Jump.Config.EJumpSingleStage.Wait)
                Module.GameMode:TrySwitchMode(Module.GameMode.Config.ESwitchMode.BHD)
            end
        end
    elseif
        firstEnum == Module.Jump.Config.EJumpFirstEnum.KeepCurrent or
            firstEnum == Module.Jump.Config.EJumpFirstEnum.KeepCurrentAllPop or
            (firstEnum == Module.Jump.Config.EJumpFirstEnum.SOL and currentStage == EGameFlowStageType.SafeHouse) or
            (firstEnum == Module.Jump.Config.EJumpFirstEnum.MP and currentStage == EGameFlowStageType.Lobby) or
            (firstEnum == Module.Jump.Config.EJumpFirstEnum.BHD and currentStage == EGameFlowStageType.LobbyBHD)
     then
        if firstEnum == Module.Jump.Config.EJumpFirstEnum.KeepCurrentAllPop then
            Facade.UIManager:CloseAllPopUI()
            Facade.UIManager:PopAllUI(false, false, MapPopAllUIReason2Str.JumpSingleStage)
        end
        log("JumpLogic JumpMode = KeepCurrent ")
        Module.Jump.Field:SetJumpSingleStage(currentJumpIndex, Module.Jump.Config.EJumpSingleStage.Jumping)
        JumpLogic.JumpSub()
    elseif firstEnum == Module.Jump.Config.EJumpFirstEnum.Url then
        Module.Jump.Field:SetJumpSingleStage(currentJumpIndex, Module.Jump.Config.EJumpSingleStage.End)
        if cfg.ExParams and string.len(cfg.ExParams) > 0 then
            local JsonFactory = require "DFM.YxFramework.Plugin.Json.Json"
            local Json = JsonFactory.createJson()
            local urlData = Json.decode(cfg.ExParams, 1)

            if urlData and urlData.url then
                Module.GCloudSDK:OpenUrl(urlData.url, nil, nil, nil, nil, nil, urlData)
            end
        end
        Module.Jump.Field:ClearJumpSingle(currentJumpIndex)
        Module.Jump.Field:SetCurrentJumpIndex(0)
    end
end

JumpLogic.JumpSub = function()
    -- 处理2级跳转 二级跳转需要响应模块提供跳转所需的调用接口
    local currentJumpIndex = Module.Jump.Field:GetCurrentJumpIndex()
    local cfg = Module.Jump.Field:GetJumpSingleCfg(currentJumpIndex)
    if cfg == nil then
        logwarning("数值保存有问题 / 不能直接外部模块调用改接口")
        Module.Jump.Field:ClearJumpSingle(currentJumpIndex)
        Module.Jump.Field:SetCurrentJumpIndex(0)
        return
    end
    local data = Module.Jump.Field:GetCurrentJumpSingleData(currentJumpIndex)
    local firstEnum = cfg.JumpFirstEnum
    local secondEnum = cfg.JumpSecendEnum
    local thridEnum = cfg.JumpThridEnum
    local fourthEnum = cfg.JumpFourthEnum
    local jumpSource = data.jumpSource
    local arr = {}
    local jumpArgs = data:GetJumpArgs()
    if jumpArgs then
        if type(jumpArgs) == "table" then
            for _, arg in pairs(jumpArgs) do
                table.insert(arr, arg)
            end
        else
            table.insert(arr, jumpArgs)
        end
    else
        if cfg.ExParams ~= "" then
            arr = string.split(cfg.ExParams, ",")
        end
    end
    local result = Module.Jump.Config.EJumpResultType.Failed

    local _JumpSubCallback = {
        [Module.Jump.Config.EJumpSecondEnum.Activity] = function(thirdEnum, fourthEnum, arr)
            local targetActivityGroupID = thirdEnum
            local targetActivityID = arr[1] and tonumber(arr[1]) or nil
            log("JumpLogic._JumpSubCallback ActivityID:", targetActivityID)
            if targetActivityID then
                -- 跳转进入活动
                targetActivityGroupID = nil
                if ActivityLogic.IsActivityIDStackUI(targetActivityID) then
                    ActivityLogic.OpenStackUIActivity(targetActivityID)
                    return Module.Jump.Config.EJumpResultType.Success
                end

                Module.Activity:ShowActivityPanel(targetActivityGroupID, targetActivityID)
                return Module.Jump.Config.EJumpResultType.Success
            else
                -- 跳转进入活动分组列表
                if targetActivityGroupID == 0 then
                    targetActivityGroupID = nil
                end
                targetActivityID = nil
                return Module.Jump.Config.EJumpResultType.Success
            end
        end,
        [Module.Jump.Config.EJumpSecondEnum.Store] = function(thridEnum, fourthEnum, arr)
            if thridEnum == 0 then
                Module.Store:ShowMainPanel(EStoreTab.Recommend, 0, jumpSource)
                return Module.Jump.Config.EJumpResultType.Success
            else
                local id = 0
                if thridEnum > 0 and fourthEnum == 0 and #arr > 0 and arr[1] then
                    id = tonumber(arr[1])
                else
                    if fourthEnum > 0 then
                        id = fourthEnum
                    end
                end
                Module.Store:ShowMainPanel(thridEnum, id, jumpSource)
                return Module.Jump.Config.EJumpResultType.Success
            end
        end,
        [Module.Jump.Config.EJumpSecondEnum.BattlePass] = function(thridEnum, fourthEnum, arr)
            --TODO:等BP开放接口
            if thridEnum == 0 and #arr <= 0 then
                Module.BattlePass:ShowBattleProcess(UIName2ID.BattlePassMain)
                return Module.Jump.Config.EJumpResultType.Success
            else
                local uiNameID = UIName2ID.BattlePassMain
                if thridEnum == Module.Jump.Config.EJumpBattlePassThridEnum.BattlePass_MainTitle then
                    uiNameID = UIName2ID.BattlePassMain
                elseif thridEnum == Module.Jump.Config.EJumpBattlePassThridEnum.BattlePass_MainTitle then
                    uiNameID = UIName2ID.BattlePassBranch
                elseif thridEnum == Module.Jump.Config.EJumpBattlePassThridEnum.BattlePass_MainTitle then
                    uiNameID = UIName2ID.BattlePassReward
                end
                local param1 = nil
                local param2 = nil
                if fourthEnum == 0 and #arr > 0 then
                    param1 = arr[1] and tonumber(arr[1]) or nil
                    param2 = arr[2] and tonumber(arr[2]) or nil
                end
                Module.BattlePass:ShowBattleProcess(
                    UIName2ID.BattlePassMain,
                    Module.BattlePass.Field.eBPOriginType.None,
                    param1,
                    param2
                )
                return Module.Jump.Config.EJumpResultType.Success
            end
        end,
        [Module.Jump.Config.EJumpSecondEnum.Hero] = function(thridEnum, fourthEnum, arr)
            if thridEnum == 0 and #arr <= 0 then
                Module.Hero:ShowHeroMainPanel()
                return Module.Jump.Config.EJumpResultType.Success
            else
                if thridEnum == 0 and #arr > 0 and arr[1] then
                    if fourthEnum == Module.Jump.Config.EJumpHeroFourthEnum.Hero_OperatorFiles then
                        Module.Hero:ShowHeroFilePanel(tonumber(arr[1]), nil)
                        return Module.Jump.Config.EJumpResultType.Success
                    end
                    if fourthEnum == Module.Jump.Config.EJumpHeroFourthEnum.Hero_Recruitment then
                        Module.Hero.ShowHeroRecruitmentPanel(nil, nil, tonumber(arr[1]))
                        return Module.Jump.Config.EJumpResultType.Success
                    end
                    if fourthEnum == Module.Jump.Config.EJumpHeroFourthEnum.Hero_MysticalItem then
                        Module.Hero.ShowHeroMysticalItemPanel(nil, nil, tonumber(arr[1]))
                        return Module.Jump.Config.EJumpResultType.Success
                    end
                end
                return Module.Jump.Config.EJumpResultType.Failed
            end
        end,
        [Module.Jump.Config.EJumpSecondEnum.Market] = function(thridEnum, fourthEnum, arr)
            local currentStage = Facade.GameFlowManager:GetCurrentGameFlow()
            if currentStage == EGameFlowStageType.SafeHouse then
                Module.Market:ShowMainPanel(thridEnum > 0 and thridEnum or nil)
                return Module.Jump.Config.EJumpResultType.Success
            end
            return Module.Jump.Config.EJumpResultType.Failed
        end,
        [Module.Jump.Config.EJumpSecondEnum.Rank] = function(thridEnum, fourthEnum, arr)
            local currentStage = Facade.GameFlowManager:GetCurrentGameFlow()
            if currentStage == EGameFlowStageType.SafeHouse then
                if thridEnum == 0 and #arr <= 0 then
                    Module.Ranking:ShowMainPanel()
                    return Module.Jump.Config.EJumpResultType.Success
                else
                    if thridEnum == Module.Jump.Config.EJumpRankThridEnum.Rank_Reward then
                        local majorLevel = nil
                        if fourthEnum == 0 and #arr > 0 and arr[1] then
                            majorLevel = tonumber(arr[1])
                        end
                        Module.Ranking:ShowRankRewardPanel(majorLevel)
                        return Module.Jump.Config.EJumpResultType.Success
                    else
                        Module.Ranking:ShowMainPanel()
                        return Module.Jump.Config.EJumpResultType.Success
                    end
                end
            end
            return Module.Jump.Config.EJumpResultType.Failed
        end,
        [Module.Jump.Config.EJumpSecondEnum.SandBoxMap] = function(thridEnum, fourthEnum, arr)
            local currentStage = Facade.GameFlowManager:GetCurrentGameFlow()
            if currentStage == EGameFlowStageType.SafeHouse then
                local hasExist = false
                if arr[1] then
                    local worldEntranceTable = Facade.TableManager:GetTable("WorldEntranceConfig")
                    for _, cfg in pairs(worldEntranceTable) do
                        if
                            Server.GameModeServer:CheckMapIDByEntranceId(
                                cfg.EntranceIdx,
                                arr[1] and tonumber(arr[1]) or nil
                            )
                         then
                            hasExist = true
                        end
                    end
                end
                if hasExist then
                    Module.SandBoxMap:Jump(arr[1] and tonumber(arr[1]) or nil)
                else
                    Module.SandBoxMap:Jump()
                end
                return Module.Jump.Config.EJumpResultType.Success
            end
            return Module.Jump.Config.EJumpResultType.Failed
        end,
        [Module.Jump.Config.EJumpSecondEnum.MPModeSelect] = function(thridEnum, fourthEnum, arr)
            local currentStage = Facade.GameFlowManager:GetCurrentGameFlow()
            if currentStage == EGameFlowStageType.Lobby then
                if thridEnum == 0 and arr[1] then
                    Module.BattlefieldEntry:ChangeTDMMapGroup(tonumber(arr[1]))
                else
                    Module.BattlefieldEntry:OpenModeSelector()
                end
                return Module.Jump.Config.EJumpResultType.Success
            end
            return Module.Jump.Config.EJumpResultType.Failed
        end,
        [Module.Jump.Config.EJumpSecondEnum.Quest] = function(thridEnum, fourthEnum, arr)
            if thridEnum or (thridEnum and fourthEnum) then
                local lineInfo = Server.QuestServer:GetQuestLineInfoById(thridEnum)
                local questInfo = Server.QuestServer:GetQuestInfoById(fourthEnum)
                
                if arr ~= nil and #arr > 0 then
                    questInfo = Server.QuestServer:GetQuestInfoById(arr[1])
                end

                if lineInfo then
                    Module.Quest:SetToDetailFromSource(Module.Quest.Config.EEnterToDetailType.Jump)
                    Module.Quest:OpenSOLQuestDetailView(lineInfo, questInfo)
                else
                    if IsHD() then
                        Module.Quest:OpenSOLQuestMainPanel()
                    else
                        Module.Shop:OpenChooseDepartmentView()
                    end
                end
                return Module.Jump.Config.EJumpResultType.Success
            end
            return Module.Jump.Config.EJumpResultType.Failed
        end,
        [Module.Jump.Config.EJumpSecondEnum.QuestSeason] = function(thridEnum, fourthEnum, arr)
            local seasonLineInfo = Server.QuestServer:GetCurrentSeasonLine()
            
            if seasonLineInfo == nil or not seasonLineInfo:IsSeasonLineOpen() or thridEnum == nil then
                if IsHD() then
                    Module.Quest:OpenSeasonMainPanel()
                else
                    Module.Shop:OpenChooseDepartmentView(EDepartmentType.QuestSeason)
                end
                return Module.Jump.Config.EJumpResultType.Failed
            end

            if thridEnum and thridEnum == 0 then
                if IsHD() then
                    Module.Quest:OpenSeasonMainPanel()
                else
                    Module.Shop:OpenChooseDepartmentView(EDepartmentType.QuestSeason)
                end
                return Module.Jump.Config.EJumpResultType.Success
            end
            if thridEnum == Module.Jump.Config.EJumpQuestSeasonThridEnum.SeasonPlan then
                local questID = arr[1]
                local stageID = seasonLineInfo:GetStageIDByQuestID(questID)

                if seasonLineInfo and stageID > 0 then
                    Module.Quest:OpenSeasonListPanel(seasonLineInfo, stageID, questID)
                else
                    if IsHD() then
                        Module.Quest:OpenSeasonMainPanel()
                    else
                        Module.Shop:OpenChooseDepartmentView(EDepartmentType.QuestSeason)
                    end
                end

                return Module.Jump.Config.EJumpResultType.Success
            end

            if thridEnum == Module.Jump.Config.EJumpQuestSeasonThridEnum.Collector then
                Module.Quest:OpenQuestSeasonCollector()
                return Module.Jump.Config.EJumpResultType.Success
            end
            if thridEnum == Module.Jump.Config.EJumpQuestSeasonThridEnum.Fact then
                if arr ~= nil and #arr > 0 then
                    local questId = arr[1]
                    local questInfo = Server.QuestServer:GetQuestInfoById(questId)
                    local factId = Server.QuestServer._questFactData:GetFactQuestIDByQuestID(questId)
                    if factId > 0 and Server.QuestServer._questFactData:GetRemainToAcceptTime(factId) <= 0 then
                        Module.Quest:OpenSeasonFactDetail(factId, questInfo)
                    else
                        Module.Quest:OpenQuestSeasonFact()
                        return Module.Jump.Config.EJumpResultType.Failed 
                    end
                else
                    Module.Quest:OpenQuestSeasonFact()
                    return Module.Jump.Config.EJumpResultType.Success
                end
            end
        end,
        [Module.Jump.Config.EJumpSecondEnum.SOLSandBoxMapOrMPModeSelect] = function(thridEnum, fourthEnum, arr)
            local currentStage = Facade.GameFlowManager:GetCurrentGameFlow()
            if currentStage == EGameFlowStageType.SafeHouse then
                if arr[1] then
                    local worldEntranceTable = Facade.TableManager:GetTable("WorldEntranceConfig")
                    local hasExist = false
                    for _, cfg in pairs(worldEntranceTable) do
                        if
                            Server.GameModeServer:CheckMapIDByEntranceId(
                                cfg.EntranceIdx,
                                arr[1] and tonumber(arr[1]) or nil
                            )
                         then
                            hasExist = true
                        end
                    end
                end
                if hasExist then
                    Module.SandBoxMap:Jump(arr[1] and tonumber(arr[1]) or nil)
                else
                    Module.SandBoxMap:Jump()
                end
                return Module.Jump.Config.EJumpResultType.Success
            elseif currentStage == EGameFlowStageType.Lobby then
                if thridEnum == 0 and arr[1] then
                    Module.BattlefieldEntry:ChangeTDMMapGroup(tonumber(arr[1]))
                else
                    Module.BattlefieldEntry:OpenModeSelector()
                end
                return Module.Jump.Config.EJumpResultType.Success
            end
            return Module.Jump.Config.EJumpResultType.Failed
        end,
        [Module.Jump.Config.EJumpSecondEnum.LiveRadio] = function(thridEnum, fourthEnum, arr)
            Module.LiveRadio:ShowMainPanel()
            return Module.Jump.Config.EJumpResultType.Success
        end,
        [Module.Jump.Config.EJumpSecondEnum.Tournament] = function(thridEnum, fourthEnum, arr)
            Module.Tournament:ShowMainPanel()
            return Module.Jump.Config.EJumpResultType.Success
        end,
        [Module.Jump.Config.EJumpSecondEnum.Shop] = function(thridEnum, fourthEnum, arr)
            if arr[1] then
                Module.Shop:JumpToShopItemViewByExchangeId(tonumber(arr[1]), nil, false)
                return Module.Jump.Config.EJumpResultType.Success
            end
            return Module.Jump.Config.EJumpResultType.Failed
        end,
        [Module.Jump.Config.EJumpSecondEnum.Armory] = function(thridEnum, fourthEnum, arr)
            Module.Armory:OpenArmoryWindow(arr[1] and tonumber(arr[1]) or nil, arr[2] and tonumber(arr[2]) or nil)
            return Module.Jump.Config.EJumpResultType.Success
        end,
        [Module.Jump.Config.EJumpSecondEnum.Gunsmith] = function(thridEnum, fourthEnum, arr)
            Module.Gunsmith:OpenSOLMainUIByItemID()
            return Module.Jump.Config.EJumpResultType.Success
        end,
        [Module.Jump.Config.EJumpSecondEnum.BlackSite] = function(thridEnum, fourthEnum, arr)
            if thridEnum == Module.Jump.Config.EJumpBlackSiteThridEnum.BlackSite_Equipment then
                Module.BlackSite:Jump(arr[1] and tonumber(arr[1]) or nil, true, true)
            end
            if thridEnum == Module.Jump.Config.EJumpBlackSiteThridEnum.BlackSite_Formula then
                Module.BlackSite:Jump(arr[1] and tonumber(arr[1]) or nil, nil, true)
            end
            return Module.Jump.Config.EJumpResultType.Success
        end
    }

    local jumpSubCb = _JumpSubCallback[secondEnum]
    if jumpSubCb then
        result = jumpSubCb(thridEnum, fourthEnum, arr)
    else
        result = Module.Jump.Config.EJumpResultType.Success
    end

    Module.Jump.Field:SetJumpSingleStage(currentJumpIndex, Module.Jump.Config.EJumpSingleStage.End)
    if data._jumpCallback then
        data._jumpCallback(result)
    end
    if result == Module.Jump.Config.EJumpResultType.Failed then
    --Module.CommonTips:ShowSimpleTip(Module.Jump.Config.Loc.CantFindJumpCfg)
    end

    Module.Jump.Field:ClearJumpSingle(currentJumpIndex)
    Module.Jump.Field:SetCurrentJumpIndex(0)
end

JumpLogic.JumpModeSucess = function()
    local currentJumpIndex = Module.Jump.Field:GetCurrentJumpIndex()
    local data = Module.Jump.Field:GetCurrentJumpSingleData(currentJumpIndex)
    local bWaitGuide = Module.Guide:IsGuidingOrWaitGuide()
    if data and data:InWaitStage() and not bWaitGuide then
        Module.Jump.Field:SetJumpSingleStage(currentJumpIndex, Module.Jump.Config.EJumpSingleStage.Jumping)
        JumpLogic.JumpSub()
    else
        Module.Jump.Field:SetJumpSingleStage(currentJumpIndex, Module.Jump.Config.EJumpSingleStage.End)
        Module.Jump.Field:ClearJumpSingle(currentJumpIndex)
        Module.Jump.Field:SetCurrentJumpIndex(0)
    end
end

return JumpLogic
