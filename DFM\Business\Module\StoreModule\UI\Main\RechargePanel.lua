﻿----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMStore)
----- LOG FUNCTION AUTO GENERATE END -----------

--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- 显示store icon
local UDFMPlatformUtils = import "DFMPlatformUtils"
--- END MODIFICATION

---@class RechargePanel : LuaUIBaseView
local RechargePanel = ui("RechargePanel")
local StoreConfig = Module.Store.Config
local StoreLogic = require "DFM.Business.Module.StoreModule.Logic.StoreLogic"
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"
local StoreRechargeItem = require "DFM.Business.Module.StoreModule.UI.Sub.StoreRechargeItem"



function RechargePanel:Ctor()
    self._wtWrapBox = self:Wnd("DFWrapBox_162", UIWidgetBase)
    self.skipStyle = 2
    self.clientexpect = 0
    self.rechargebalance = 0

    if DFHD_LUA == 1 then
        Module.CommonBar:RegStackUITopBarCurrencyTypeList(self,
            { ECurrencyClientId.BindDiamond, ECurrencyClientId.UnbindDiamond })
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.DefaultSecondary)
    else
        Module.CommonBar:RegStackUITopBarCurrencyTypeList(self,
            { ECurrencyClientId.BindDiamond, ECurrencyClientId.UnbindDiamond })
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.Custom)

        logwarning("SystemSettingMainView RechargePage Services")
        local CustomerServicesEntranceType = import "ECustomerServicesEntranceType"
        if Module.CustomerServices:CheckEntranceEnable(CustomerServicesEntranceType.RechargePage) then
            local UIID = IsBuildRegionCN() and UIName2ID.CustomerServicesBtn or UIName2ID.CustomerServicesOverSeaBtn
            logwarning("SystemSettingMainView Customer Services UIID " .. UIID)
            Module.CommonBar:RegStackUITopBarCustomInfo(self, {
                InfoUINavID = UIID,
                InfoUICacheInfo = UIUtil.CreateCacheInfo(UIID, CustomerServicesEntranceType.RechargePage)
            })
        end
    end
    Module.CommonBar:RegStackUITopBarTitle(self, StoreConfig.Loc.StoreMainTabRecharge)
    self:AddLuaEvent(Server.PayServer.Events.evtRechargeDataChanged, self._OnServerRechargeInfoUpdate, self)
    if IsMobile() and IsBuildRegionGA() then
       
    else
        Server.PayServer:ReqRecharegeItemList()
    end

    self._wtRechargeItem1 = self:Wnd("RechargeItem1", StoreRechargeItem)
    self._wtRechargeItem2 = self:Wnd("RechargeItem2", StoreRechargeItem)
    self._wtRechargeItem3 = self:Wnd("RechargeItem3", StoreRechargeItem)
    self._wtRechargeItem4 = self:Wnd("RechargeItem4", StoreRechargeItem)
    self._wtRechargeItem5 = self:Wnd("RechargeItem5", StoreRechargeItem)
    self._wtRechargeItem6 = self:Wnd("RechargeItem6", StoreRechargeItem)

    self._wtJapaneseBtn1 = self:Wnd("WBP_CommonButtonV3S1", UIButton)
    self._wtJapaneseBtn2 = self:Wnd("WBP_CommonButtonV3S1_1", UIButton)

    -- 不需要本地化
    self._wtJapaneseBtn1:SetMainTitle("特定商取引法に基づく表示")
    self._wtJapaneseBtn2:SetMainTitle("資金決済法に基づく表示")

    self._wtJapaneseBtn1:Event("OnClicked", self._OnJapaneseBtn1Click, self)
    self._wtJapaneseBtn2:Event("OnClicked", self._OnJapaneseBtn2Click, self)

    self.RechargeItems = { self._wtRechargeItem1, self._wtRechargeItem2, self._wtRechargeItem3, self._wtRechargeItem4,
        self._wtRechargeItem5, self._wtRechargeItem6 }

    -- 美国加州合规超链接文本
    self._wtAmericanHyperLink = self:Wnd("DFRichTextBlock_PlatformCompliance", UITextBlock)

    --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() then
        -- 米大师获取物品价格后刷新ui
        if IsConsole() then
            self:AddLuaEvent(Server.PayServer.Events.evtOnGetProductInfo, self._OnServerRechargeInfoUpdate, self)
        end

        self._wtNavRoot = self:Wnd("DFCanvasPanel_1", UIWidgetBase)
    end
    --- END MODIFICATION
end


function RechargePanel:_OnServerRechargeInfoUpdate()
    self:RefreshUIInfo()
end

-- UI构造时触发, 用于初始化外部传参UI数据逻辑，由UIManager触发（仅触发一次）
-- 子节点蓝图不会触发该方法
-- Module加载好子控件所需的图片后，将数据传入主UI界面
function RechargePanel:OnInitExtraData(skipStyle)
    if skipStyle ~= nil then
        self.skipStyle = skipStyle
    end
end

-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function RechargePanel:OnOpen()
    if Server.PayServer:IsGoogleEnable() then
        Module.Pay:ReapplyReceipt()
    end
    Server.CurrencyServer:ReqAllCurrencyData()
    Module.Login.Config.Events.evtOnRefreshToken:Invoke()
    self:RefreshUIInfo()
end

function RechargePanel:RefreshUIInfo()
    self.sortedRechargeItems = Server.PayServer:GetRechargeItemList()

    for _,itemWidget in ipairs(self.RechargeItems) do
        itemWidget:Collapsed()
    end

    local rechargeIdx = 0
    for _, item in ipairs(self.sortedRechargeItems) do
        rechargeIdx = rechargeIdx + 1
        local newItemWidget = self.RechargeItems[rechargeIdx]
        if newItemWidget ~= nil then
            newItemWidget:Visible()
            newItemWidget:InitItem(item, rechargeIdx)
        end
    end

    -- for anim don't use dynamic create item
    -- Facade.UIManager:ClearSubUIByParent(self, self._wtWrapBox)
    -- for _, item in ipairs(self.sortedRechargeItems) do
    --     local weakUIIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.StoreRechargeItem, self._wtWrapBox)
    --     local newItemWidget = getfromweak(weakUIIns)
    --     rechargeIdx = rechargeIdx + 1
    --     if newItemWidget ~= nil then
    --         newItemWidget:InitItem(item, rechargeIdx)
    --     end
    -- end

    if IsBuildRegionGlobal() then
        local region = Server.SDKInfoServer:GetRegionNumericCode()
        if tonumber(region) == 392 then
            self._wtJapaneseBtn1:Visible()
            self._wtJapaneseBtn2:Visible()
        else
            self._wtJapaneseBtn1:Collapsed()
            self._wtJapaneseBtn2:Collapsed()
        end
    else
        self._wtJapaneseBtn1:Collapsed()
        self._wtJapaneseBtn2:Collapsed()
    end


    if Server.SDKInfoServer:IsAnyRegionAmerica() then
        self._wtAmericanHyperLink:Visible()
        local decoratorIns = self._wtAmericanHyperLink:GetDecoratorByClass(self.BpTextBlockDecorator)
        if decoratorIns then
            loginfo('decoratorIns.OnClicked:Add')
            decoratorIns.OnClicked:Add(self._OnAmericanHyperLinkClicked, self)
        end
        if IsPS5Family() then
            local link = "You are purchasing the right to use the selected item(s) and any content obtained from them, subject to the terms <dfmrichtext style=\"PlatformCompliance\" link=\"https://www.playdeltaforce.com/sony/terms-of-use.html\">here</>."
            self._wtAmericanHyperLink:SetText(link)
        else
            local link = "You are purchasing the right to use the selected item(s) and any content obtained from them, subject to the terms <dfmrichtext style=\"PlatformCompliance\" link=\"https://www.playdeltaforce.com/terms-of-use.html\">here</>."
            self._wtAmericanHyperLink:SetText(link)
        end
    else
        self._wtAmericanHyperLink:Collapsed()
    end
end

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function RechargePanel:OnClose()
    Server.CurrencyServer:ReqAllCurrencyData()
    Server.StoreServer:SendShopGetBuyRecordReq()
    self:RemoveLuaEvent(Server.StoreServer.Events.evtStoreDataChanged)
end

function RechargePanel:OnShowBegin()
    self:AddLuaEvent(Module.Pay.Config.Events.evtOnPayFinished, self._OnPayFinishedEvent, self)
    self:AddLuaEvent(Module.Store.Config.evtStoreRechargeItemClick, self.ToRecharge, self)
    --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsPS5Family() then
        UDFMPlatformUtils.ShowPlatformStoreUI()
    end
    --- END MODIFICATION
end

-- UI OnOpen后、调用Show()显示时触发
-- 做表现
---@overload fun(LuaUIBaseView, OnShow)
function RechargePanel:OnShow()
    LogAnalysisTool.DoSendStoreViewPageReportLog(5, self.skipStyle, 0, 0)
    Module.GCloudSDK:OnPayConsiderReport()

    --- BEGIN MODIFICATION @ VIRTUOS
    -- 针对有通用弹窗的情况，导航注册放在OnShowBegin里时机太早了，会导致导航组刚注册完就被通用弹窗销毁了
    -- 因此将导航的注册放到了OnShow内
    if IsHD() then
        self:_EnableGamepadFeature()
    end
    --- END MODIFICATION
end

--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
function RechargePanel:OnHideBegin()
    if IsHD() then
        if IsConsole() then
            self:RemoveLuaEvent(Server.PayServer.Events.evtOnGetProductInfo)
        end
        self:_DisableGamepadFeature()
    end

    if IsPS5Family() then
        UDFMPlatformUtils.HidePlatformStoreUI()
    end
end
--- END MODIFICATION

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function RechargePanel:OnHide()
    self:RemoveLuaEvent(Module.Pay.Config.Events.evtOnPayFinished)
    self:RemoveLuaEvent(Module.Store.Config.evtStoreRechargeItemClick)
end

function RechargePanel:ToRecharge(gear, clientexpect, productId, rechargebalance)
    if IsMobile() and IsBuildRegionGA() then
        if Module.Pay:IsGetProductInfoLoading() then
            logerror("RechargePanel:ToRecharge IsGetProductInfoLoading")
            return
        end
    end
    local isOpen = Server.PayServer:IsEnable()
    if isOpen == false then
        Module.CommonTips:ShowSimpleTip(Module.Pay.Config.Loc.RechargeNotOpen)
        return
    end

    self._gear = gear
    self.clientexpect = clientexpect
    self.rechargebalance = rechargebalance

    Module.Pay:Recharge(productId)
    --- BEGIN MODIFICATION @ VIRTUOS 修复log proto的lua报错
    LogAnalysisTool.DoSendStoreRechargePageEventReportLog(math.floor(self._gear), productId)
    --- END MODIFICATION
end

function RechargePanel:_OnPayFinishedEvent(resultCode)
    local currency_type = self:GetCurrencyItemID()
    local currecny = Module.Currency:GetNumByItemId(currency_type)
    local playerLevel = Server.RoleInfoServer.accountLevel
    if self._gear then
        LogAnalysisTool.DoSendStoreMoneyFlow(currecny + self.clientexpect, self.clientexpect, self.rechargebalance, playerLevel, resultCode)
    end
end

function RechargePanel:GetCurrencyItemID()
    return ***********
end

function RechargePanel:_OnJapaneseBtn1Click()
    Module.GCloudSDK:OpenUrl("https://www.playdeltaforce.com/ja/specified-commercial-transaction-act.html",
            3, false, false, "", false)
end

function RechargePanel:_OnJapaneseBtn2Click()
    Module.GCloudSDK:OpenUrl("https://www.playdeltaforce.com/ja/payment-services-act.html",
            3, false, false, "", false)
end

-- 美国加州合规超链接文本点击事件
function RechargePanel:_OnAmericanHyperLinkClicked(metadataStruct)
    loginfo('RechargePanel:_OnAmericanHyperLinkClicked()')
    for key, value in pairs(metadataStruct.Metadata) do
        if key == "link" then
            loginfo('link address '..value)
            --- Windows的外部浏览器打开需要调用别的接口
            if PLATFORM_WINDOWS then
                Module.GCloudSDK:LaunchURL(value)
            else
                Module.GCloudSDK:OpenUrl(value, 3, false,false, "",false)
            end
        end
    end
end

--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
function RechargePanel:_EnableGamepadFeature()
    if not IsHD() then
        return 
    end

    if self._wtNavRoot then
        if not self._NavGroup then
            self._NavGroup = WidgetUtil.RegisterNavigationGroup(self._wtNavRoot, self, "Hittest")
        end
        self._NavGroup:AddNavWidgetToArray(self._wtNavRoot) 
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup)
    end
end

function RechargePanel:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end
    
    WidgetUtil.RemoveNavigationGroup(self)
    self._NavGroup = nil
end
--- END MODIFICATION

return RechargePanel
