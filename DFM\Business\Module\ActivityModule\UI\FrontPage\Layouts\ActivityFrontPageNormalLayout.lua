----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------



local UIWaterfallScrollBox      = require "DFM.Business.DataStruct.UIDataStruct.UIWaterfallScrollBox"
local Sort                      = require "DFM.Business.DataStruct.Common.Base.Sort"
local Table                     = require "DFM.Business.DataStruct.Common.Base.Table"
local NavigationAgent         = require "DFM.Business.DataStruct.Common.Agent.NavigationAgent"
local AnimManager               = require "DFM.Business.DataStruct.Common.Base.DFUtil.AnimManager"
local ActivityFrontPageRow      = require "DFM.Business.Module.ActivityModule.UI.FrontPage.Layouts.ActivityFrontPageRow"
local ActivityFrontPageBtn      = require "DFM.Business.Module.ActivityModule.UI.FrontPage.ActivityFrontPageBtn"
local Deep                         = require "DFM.Business.DataStruct.Common.Base.Deep"

---@class ActivityFrontPageNormalLayout
local ActivityFrontPageNormalLayout = ui("ActivityFrontPageNormalLayout")

local function CheckURL(url)
    return url ~= nil and url ~= ""
end

function ActivityFrontPageNormalLayout:Ctor()
    self._wtScrollView = UIWaterfallScrollBox:Wnd(self, "DFWaterfallScrollView_57")
    self._wtScrollView:ToggleAutoRefresh(false)

    self._navMgr = NavigationAgent.Create(self)
end

---@param activityInfos   pb_ActivityInfo[]
---@param layoutRowsInfo  ActivityFrontPageRowState[]
function ActivityFrontPageNormalLayout:_DoFirstRowLayout(activityInfos, layoutRowsInfo)
    if #activityInfos == 0 then return false end

    -- 如果第一个活动没有配置大图或者视频，则第一行也变成普通行
    local activityInfo = activityInfos[1]
    if not (CheckURL(activityInfo.important_pic) or CheckURL(activityInfo.important_video)) then
        return self:_DoOtherRowLayout(activityInfos, layoutRowsInfo)
    end

    -- 添加一行
    local newRow = {}
    table.insert(layoutRowsInfo, newRow)

    -- 添加一个大按钮
    local activityInfo = table.remove(activityInfos, 1)
    table.insert(newRow,
        {
            style = EActivityFrontPageBtnStyle.NormalBig,
            activityInfo = activityInfo,
            themeID = 0,
        }
    )

    -- 添加一个中按钮
    local activityInfo = table.remove(activityInfos, 1)
    table.insert(newRow,
        {
            style = EActivityFrontPageBtnStyle.NormalMedium,
            activityInfo = activityInfo,
            themeID = 0,
        }
    )

    return true
end

function ActivityFrontPageNormalLayout:_DoOtherRowLayout(activityInfos, layoutRowsInfo)
    if #activityInfos == 0 then return false end

    -- 添加一行
    local newRow = {}
    table.insert(layoutRowsInfo, newRow)

    -- 添加3个小按钮
    for i = 1, 3 do
        local activityInfo = table.remove(activityInfos, 1)
        table.insert(newRow,
            {
                style = EActivityFrontPageBtnStyle.NormalSmall,
                activityInfo = activityInfo,
                themeID = 0,
            }
        )
    end

    return true
end

function ActivityFrontPageNormalLayout:SortActivities(activityInfos)
    Sort.MergeSort(activityInfos, 
        Sort.Preferenced({
            Sort.ByField("is_important" , nil, true),    -- 重磅活动排前面(倒序 true < false)
            Sort.ByField("finished"     , nil, false),   -- 已完成活动排后面(正序 false < true)
            Sort.ByField("order_weight" , nil, true),    -- 活动权重(倒序 大权重 < 小权重)
    }))
end

---@param groupInfo ActivityGroupPresentationInfo
function ActivityFrontPageNormalLayout:SetData(groupInfo)
    groupInfo = groupInfo or {groupID = 0, activities = {}, themeID = 0}
    local activityIdList = groupInfo.activities or {}

    -- 排序活动
    local activityInfos = Table.CollectValues(Server.ActivityServer.AllActivityInfos, activityIdList) ---@type pb_ActivityInfo[]
    self:SortActivities(activityInfos)
    self._sortedActivities = Deep.DeepCopy(activityInfos, 1)

    local layoutRowsInfo = {}
    
    -- 首行排版
    self:_DoFirstRowLayout(activityInfos, layoutRowsInfo)
    
    -- 其余行排版
    while self:_DoOtherRowLayout(activityInfos, layoutRowsInfo) do
    end

    local layoutData = {
        layourRowsInfo = layoutRowsInfo,
        themeID        = groupInfo.themeID,
    }
    self:UpdateDisplay(layoutData, true)
end

function ActivityFrontPageNormalLayout:GetSortedActivities()
    return self._sortedActivities
end

function ActivityFrontPageNormalLayout:UpdateDisplay(layoutData, bAnim)
    local tListRefresh = os.clock()

    self._wtScrollView:ClearItems()
    for rowIdx, rowData in ipairs(layoutData.layourRowsInfo) do
        ---@type ActivityFrontPageRowState
        local rowState = {rowIdx = rowIdx, rowData = rowData, bAnim = bAnim, tListRefresh = tListRefresh, themeID = layoutData.themeID}
        self._wtScrollView:InsertItem(rowState, nil, ActivityFrontPageRow.SetData, ActivityFrontPageRow.OnRecycle)
    end

    self._wtScrollView:RefreshAllItems()
end

function ActivityFrontPageNormalLayout:OnShowBegin()
    self._navMgr:CreateGroup({
        id              = "MainGroup",
        rootWidget      = self._wtScrollView,
        members         = {self._wtScrollView},
        scrollRecipient = self._wtScrollView,
        bSimClick       = false,
        bStack          = true,
        analogCursorSpeedFactor = 1.0,
    })
    self._navMgr:SetWrapBoundaryRule("MainGroup", {EUINavigation.Left, EUINavigation.Right})
    self._navMgr:FocusGroup("MainGroup")
end

function ActivityFrontPageNormalLayout:OnHideBegin()
    self._navMgr:RemoveAllGroups()
end


return ActivityFrontPageNormalLayout