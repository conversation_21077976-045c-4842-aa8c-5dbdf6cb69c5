----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCommonUILibrary)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- 导入C++定义的枚举类型
local EGPUINavWidgetFocusedAction = import "EGPUINavWidgetFocusedAction"
-- END MODIFICATION

--------------------------------------------------------------------------
--- 
--- 父页签组合列表
---
--- Library : DFCommonMainTabList
--- 包含以DFTabGroupMainSubBox为Item的ScrollGridBox
--------------------------------------------------------------------------
---@class DFCommonMainTabList : DFCommonCheckButtonOnly
DFCommonCheckButtonOnly = require"DFM.Business.Module.CommonUILibraryModule.UI.Library.DFCommonCheckButtonOnly"
DFCommonMainTabList = ui('DFCommonMainTabList')
ECheckButtonState = import"ECheckButtonState"
local DefaultIdx = 0

function DFCommonMainTabList:Ctor()
    self._wtTabGroupMainBox = UIUtil.WndTabGroupBox(self, "wtDFTabGroupMainBox", self._OnGetMainTabItemCount, self._OnProcessMainTabItemWidget, self.OnCheckedTabIndexChanged)

    ---@type mainTabData[]
    self.mainTabDataList = {}
    self.curSelectedMainIdx = DefaultIdx
    self.curSelectedSubIdx = -1
    -- 是否开启默认聚焦
    self._bDefaultFocus = true

    self._idxChangedCallbackIns = nil
end

function DFCommonMainTabList:OnInitExtraData(mainTabDataList)
    if mainTabDataList then
        self:InitTabGroupData(mainTabDataList)
    end
end

function DFCommonMainTabList:OnOpen()
    self:FreshMainTabByDataList(self.mainTabDataList)
end

--- BEGIN MODIFICATION @ VIRTUOS
function DFCommonMainTabList:OnShowBegin()
    if IsHD() then
        self:_EnableGamepadFeature()
    end
end

function DFCommonMainTabList:_EnableGamepadFeature()
    if not IsHD() then
        return 
    end

    if not self._wtNavGroup then

        self._wtNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtTabGroupMainBox, self, "Hittest")
        if self._wtNavGroup then
            self._wtNavGroup:AddNavWidgetToArray(self._wtTabGroupMainBox)
            -- self._wtNavGroup:MarkIsStackControlGroup() -- 注释掉，这个不是弹窗，而且他会导致弹窗关闭之后又默认导航到第一个
            -- 设置NavGroup的子控件被Focus时，采用的行行为方式，这里为执行自动鼠标左键事件
            self._wtNavGroup:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
        end
        -- 避免一些自动聚焦导致的问题（业务自己聚焦）
        if self._bDefaultFocus then
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup)
        end
    end
end

function DFCommonMainTabList:OnHideBegin()
    if IsHD() then
        self:_DisableGamepadFeature()
    end
end

function DFCommonMainTabList:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end
    if self._wtNavGroup then
        WidgetUtil.RemoveNavigationGroup(self)
        self._wtNavGroup = nil
    end
end
--- END MODIFICATION

function DFCommonMainTabList:SetCallback(fCallback, caller, ...)
    if fCallback then
        self._idxChangedCallbackIns = CreateCallBack(fCallback, caller, ...)
    end
end


function DFCommonMainTabList:InitTabGroupData(mainTabDataList)
    self.mainTabDataList = mainTabDataList
end

function DFCommonMainTabList:FreshMainTabByDataList(mainTabDataList, fProcessMainTabCallback)
    if self.mainTabDataList ~= mainTabDataList then
        self.mainTabDataList = mainTabDataList
    end
    self._fProcessMainTabCallback = fProcessMainTabCallback
    self._wtTabGroupMainBox:RefreshTab()
end

function DFCommonMainTabList:OnClose()
    self._idxChangedCallbackIns = nil
end

function DFCommonMainTabList:_OnGetMainTabItemCount()
    return #self.mainTabDataList or 0
end

function DFCommonMainTabList:_OnProcessMainTabItemWidget(position, mainTabItem)
    logframe('DFCommonMainTabList:_OnProcessMainTabItemWidget(position, topTabItem)', position, mainTabItem)
    local dataIdx = position + 1
    ---@type mainTabData
    local mainTabData = self.mainTabDataList[dataIdx]
    if not mainTabData then
        return
    end
    mainTabItem.DFCommonCheckButton:SetMainTitleText4AllState(mainTabData.keyText or Module.CommonUILibrary.Config.Loc.NoParam)
    if mainTabData.moduleUnlockData then
        Module.ModuleUnlock:SetCheckButtonModuleUnlock(mainTabItem.DFCommonCheckButton, mainTabData.moduleUnlockData)
    end
    if self._fProcessMainTabCallback then
        self._fProcessMainTabCallback(position, mainTabItem)
    end
end

function DFCommonMainTabList:OnCheckedTabIndexChanged(idx)
    logwarning('DFCommonMainTabList:OnTabIndexChanged(tabItem)', idx)
    self.curSelectedMainIdx = idx
    if self._idxChangedCallbackIns then
        self._idxChangedCallbackIns(idx, self.curSelectedSubIdx)
    end
end

function DFCommonMainTabList:SetTabIndex(idx, bCallUserDelegate)
    self._wtTabGroupMainBox:SetTabIndex(idx, bCallUserDelegate)
end

function DFCommonMainTabList:GetCurSelectedIndex()
    return self._wtTabGroupMainBox:GetCurSelectedIndex()
end

--- 删除此方法, 用Delay瀑布流并不方便
--- 这个接口不可靠,列表特性为刷新下一帧可以拿到
--- 存在拿到空的情况，建议使用回调
--- BEGIN MODIFICATION @ VIRTUOS
-- function DFCommonMainTabList:GetCurSelectedWidget()
--     return self._wtTabGroupMainBox:GetItemWidgetByIndex(self.curSelectedMainIdx)
-- end
--- END MODIFICATION

function DFCommonMainTabList:SetDefaultFocus(bDefaultFocus)
    self._bDefaultFocus = bDefaultFocus
end

function DFCommonMainTabList:GetTabGroupMainBox()
    return self._wtTabGroupMainBox
end
return DFCommonMainTabList