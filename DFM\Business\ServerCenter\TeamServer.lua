----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaSTeam)
----- LOG FUNCTION AUTO GENERATE END -----------



--- 队伍的数据中心
---@class TeamServer : ServerBase
local UDFMIrisEnterSeamlessGameplayHelper = import "DFMIrisEnterSeamlessGameplayHelper"
--------------------------------------------------------------------------
--- 依赖MatchServer/GameModeServer/AccountServer
--------------------------------------------------------------------------
local TeamServer = class("TeamServer", require("DFM.YxFramework.Managers.Server.ServerBase"))
TeamServer.MAX_TEAM_MEMBER_NUM = 4
TeamServer.MAX_TEAMMATE_NUM = TeamServer.MAX_TEAM_MEMBER_NUM - 1

local UIManager = Facade.UIManager
local GameModeSvr = Server.GameModeServer
local HeroHelperTool = require "DFM.StandaloneLua.BusinessTool.HeroHelperTool"
local BHDHelperTool = require "DFM.StandaloneLua.BusinessTool.BHDHelperTool"
local Luautils = import "Luautils"

--- BEGIN MODIFICATION - VIRTUOS
local UDFMPlatformFriendManager = import("DFMPlatformFriendManager")
local DFMPlatformFriendManager = UDFMPlatformFriendManager.Get(GetWorld())
local UDFMGameUrlGenerator = import "DFMGameUrlGenerator"
local UDFMGameUrlGeneratorIns = UDFMGameUrlGenerator.Get(GetGameInstance())
local UDFMOnlineIdentityManager = import "DFMOnlineIdentityManager"
local UDFMOnlineIdentityProxy_CheckUserPrivilege = import "DFMOnlineIdentityProxy_CheckUserPrivilege"
local UDFMOnlineSessionManager = import("DFMOnlineSessionManager")
--- END MODIFICATION - VIRTUOS

local function log(...)
    print("", "[TeamServer]", ...)
end

local function printtable(t, prefix)
    log(prefix)
    logtable(t)
end

local function changeTypetoString(changeType)
    for k,v in pairs(TeamChangeType)do
        if v==changeType then
            return k
        end
    end
    return "others"
end

function TeamServer:GetTeamId()
    return self.teamInfos.TeamID
end

function TeamServer:Ctor()
    self.Loc = {
        TeammateCantStart = NSLOCTEXT("ServerTipCode", "Lua_TeammateCantStart", "%s未准备，无法开始游戏"),
        TeammateValueLack = NSLOCTEXT("ServerTipCode", "Lua_TeammateValueLack", "%s的配装价值不满足地图要求"),
        TeammateNotAllowedProp = NSLOCTEXT("ServerTipCode", "Lua_TeammateNotAllowedProp", "%s携带了不可入局道具"),
        TeammatePackQuestMiss = NSLOCTEXT("ServerTipCode", "Lua_TeammatePackQuestMiss", "队伍中没有可用地图"),
        SeparateSymbol = NSLOCTEXT("ServerTipCode", "Lua_SeparateSymbol", "、"),
    }

    self.bIsInvited = false
    self.matchingCountTime = 0
    self.matchingInterval = 1
    self.RefuseMsg = ""
    self.bIsAutoMatchTeamMates = true
    self.UseSeamlessTravel = false
    self._invitePlayerID = {}
    self._bEnableRankMatch=true
    self._bEnableMPRankMatch=true
    self._bEnableSOLRankMatch=true
    --邀请者玩家的信息
    self._inviterPlayerInfo = {}
    --受邀者玩家的信息
    self._inviteePlayerInfo = {}
    self:ResetTeamInfos()
    self:InitHandlers()
    self:InitEvents()
end

function TeamServer:OnInitServer()
    self:AddListeners()
end

function TeamServer:OnDestroyServer()
    self:RmListeners()
    Timer.CancelDelay(self._isSendingPersonMatchingDelayHandle)
    Timer.CancelDelay(self._isSendingTeamMatchingDelayHandle)
    Timer.CancelDelay(self._isSendingCancelMatchingDelayHandle)
end

function TeamServer:AddListeners()
    local AddNtfListener = function(...)
        Facade.ProtoManager:AddNtfListener(...)
    end
    --备战相关
    AddNtfListener("CSTeamPrepareEventNtf", self.OnCSTeamPrepareEventNtf, self) --备战状态切换的通知(等待中和已拒绝)
    AddNtfListener("CSTeamPrepareNtf", self.OnCSTeamPrepareNtf, self) --邀请队员进入备战

    AddNtfListener("CSTeamToInviteeNtf", self.OnCSTeamToInviteeNtf, self) -- 收到邀请弹窗
    AddNtfListener("CSTeamResponseInviteNtf", self.OnCSTeamResponseInviteNtf, self) -- 对方回应你的邀请
    AddNtfListener("CSRoomMatchTimeoutNtf", self.OnCSRoomMatchTimeoutNtf, self) -- 比赛匹配超时
    AddNtfListener("CSTeamApplyLeaderNtf", self.OnCSTeamApplyLeaderNtf, self) -- 申请当队长
    AddNtfListener("CSTeamApplyLeaderResponseNtf", self.OnCSTeamApplyLeaderResponseNtf, self) -- 队长回应队员申请
    AddNtfListener("CSMatchWaitDSNtf", self.OnCSMatchWaitDSNtf, self) -- 等待DS拉起
    AddNtfListener("CSMatchCheckReadyNtf", self.OnCSMatchCheckReadyNtf, self) -- 对局撮合成功, 等待玩家确认
    AddNtfListener("CSMatchUnreadyNtf", self.OnCSMatchUnreadyNtf, self) -- 有玩家未准备, 对局被取消
    AddNtfListener("CSTeamBaseInfoChangeNtf", self.OnCSTeamBaseInfoChangeNtf, self) -- 队伍基础信息改变
    AddNtfListener("CSTeamMemberChangeNtf", self.OnCSTeamMemberChangeNtf, self) -- 队伍成员改变
    AddNtfListener("CSTeamMemberBatchChangeNtf", self.OnCSTeamMemberBatchChangeNtf, self)  -- 批量修改队伍成员状态
    AddNtfListener("CSTeamApplyJoinNtf", self.OnCSTeamApplyJoinNtf, self) -- 申请入队
    AddNtfListener("CSTeamResponseJoinNtf", self.OnCSTeamResponseJoinNtf, self) -- 入队申请结果
    AddNtfListener("CSTeamUnreadyNtf", self.OnCSTeamUnreadyNtf, self) -- 队友未准备提示
    AddNtfListener("CSRoomMatchStartFailNtf", self.OnCSRoomMatchStartFailNtf, self) -- 队伍中有人被封禁
    Server.MatchServer.Events.evtStartMatching:AddListener(self._SetIsMatching,self,true)
    Server.MatchServer.Events.evtEndMatching:AddListener(self._SetIsMatching,self,false)
    self:AddLuaEvent(Facade.ProtoManager.Events.evtOnRelayConnected, self._OnRelayConnected, self)  --断线重连

    --安全屋ds
    --退出联机安全屋
    self:AddLuaEvent(self.Events.evtYouAreKicked, self._OnLeaveTeam, self)
    self:AddLuaEvent(self.Events.evtYouLeaveTeam, self._OnLeaveTeam, self)
    --进入联机安全屋
    Facade.ProtoManager:AddNtfListener("CSPlayerJoinSafehouseNtf", self._OnPlayerJoinSafehouseNtf, self)
    self:AddLuaEvent(self.Events.evtTeamCreated, self.TryEnterSafeHouseDS, self)
    self:AddLuaEvent(self.Events.evtJoinTeam, self.TryEnterSafeHouseDS, self)
    self:AddLuaEvent(Server.ModuleUnlockServer.Events.evtSwitchMoudleUnlock,self._ShowModuleNtf,self)
    AddNtfListener("CSDepositChangeNtf", self._OnCSDepositChangeNtf, self)--监听返还哈夫币和sku门票
    if IsConsole() then
        self:AddLuaEvent(self.Events.evtJoinTeam, self._OnJoinTeam, self)
    end
end

function TeamServer:_OnJoinTeam()
    --BEGIN MODIFICATION
    --加入平台队伍会话
    if IsConsole() then
        self.Events.evtJoinPlatformTeamSession:Invoke()
    end   
    --END MODIFICATION 
end

function TeamServer:_OnLeaveTeam(zero, oldTeamId)
    Server.MatchServer:LeaveSafeHouseDS(zero, oldTeamId)
    self._bSendEnableRankMatch=nil
    -- BEGIN MODIFICATION - VIRTUOS
    if IsPS5() then
        if Server.AccountServer:IsPlayerInGame() or Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Game then
            return
        end
        local identityManager = UDFMOnlineIdentityManager.Get(GetGameInstance())
        identityManager:SetUsingPlatformPremiumFeatures(EPlatformPremiumFeaturesType.RealtimeMultiplay, false)
    end
    -- END MODIFICATION - VIRTUOS
end

function TeamServer:_OnPlayerJoinSafehouseNtf(ntf)
    Server.MatchServer:EnterSafeHouseDS(ntf)
    --收到playerjoinsafehouse后，刷新一下teaminfos
    local updateTeamInfoFunc = nil
    updateTeamInfoFunc = function()
        local teamInfo = self:GetTeamInfos()
        log("TeamServer:_OnPlayerJoinSafehouseNtf ", 
            tostring(teamInfo.TeamID), "|",
            tostring(teamInfo.DSRoomID), "|",
            tostring(teamInfo.Token), "|",
            tostring(teamInfo.DSTextualIP), "|",
            tostring(teamInfo.DSPort), "|"
        )
        --如果有队伍id和DSRoomID，但没有Token、DSTextualIP、DSPort其中之一是代表此时服务器信息还没写入，需要重复拉取
        if teamInfo.TeamID ~= 0 and teamInfo.DSRoomID ~= 0 and 
            (string.isempty(teamInfo.Token) or string.isempty(teamInfo.DSTextualIP) or teamInfo.DSPort == 0)  then
            Timer.DelayCall(2, function()
                self:UpdateTeamInfos(teamInfo.TeamID, updateTeamInfoFunc)            
            end)
        end
        --尝试加入安全屋ds
        self:TryEnterSafeHouseDS()
    end
    self:UpdateTeamInfos(ntf.sys_team_id, updateTeamInfoFunc)
end

function TeamServer:TryEnterSafeHouseDS()
    -- MS24先屏蔽该功能
    if true then
        return
    end

    local teamInfo = self:GetTeamInfos()
    --有队伍，且有ds信息
    if teamInfo.TeamID ~= 0 and teamInfo.DSRoomID and #(teamInfo.Token) ~= 0 and 
        string.isempty(teamInfo.DSTextualIP) == false and teamInfo.DSPort ~= 0
    then
        --封装一个假的ntf结构
        local ntf = {}
        ntf.ds_textual_ip = teamInfo.DSTextualIP
        ntf.ds_port = teamInfo.DSPort
        ntf.map_id = 100 --特勤处
        ntf.ds_token = teamInfo.Token
        ntf.ds_room_id = teamInfo.DSRoomID
        ntf.ds_domain = teamInfo.DSDomain
        ntf.sys_team_id = teamInfo.TeamID
        Server.MatchServer:EnterSafeHouseDS(ntf)
    else
        log("TeamServer:TryEnterSafeHouseDS ", 
            tostring(teamInfo.TeamID), "|",
            tostring(teamInfo.DSRoomID), "|",
            tostring(teamInfo.Token), "|",
            tostring(teamInfo.DSTextualIP), "|",
            tostring(teamInfo.DSPort), "|"
        )
    end
end

function TeamServer:CheckPlayerJoinSafehouseNtfValid(ntf)
    if ntf.sys_team_id == 0 or ntf.ds_room_id==0 or string.isempty(ntf.ds_token) or string.isempty(ntf.ds_textual_ip) or ntf.ds_port == 0 then
        logerror("TeamServer:CheckPlayerJoinSafehouseNtfValid, ntf not valid!!!","teamId",ntf.sys_team_id,"dsRoomId",ntf.ds_room_id,"token",ntf.ds_token,"ip",ntf.ds_textual_ip,"port",ntf.ds_port)
        return false
    end
    return true
end

function TeamServer:RmListeners()
    Facade.ProtoManager:RemoveAllNtfListenerByCaller(self)
    Server.MatchServer.Events.evtStartMatching:RemoveListener(self._SetIsMatching, self)
    Server.MatchServer.Events.evtEndMatching:RemoveListener(self._SetIsMatching, self)
    Facade.ProtoManager.Events.evtOnRelayConnected:RemoveListener(self._OnRelayConnected,self)
    self:RemoveAllLuaEvent()
end

function TeamServer:InitHandlers()
    self.baseInfoChangedHandlers = {}
    self.baseInfoChangedHandlers[TeamChangeType.TeamMatchIDChange] = self.HandleCSTeamChangeMatchIDNtf -- 比赛模式改变
    self.baseInfoChangedHandlers[TeamChangeType.TeamStateChange] = self.HandleCSTeamInfoChangeNtf -- 队伍状态改变
    self.baseInfoChangedHandlers[TeamChangeType.TeamAllChange] = self.HandleCSTeamUpdateAllInfo -- 全量更新, 拉取队伍信息
    self.baseInfoChangedHandlers[TeamChangeType.TeamFace2FaceCreate] = self.HandleCSFace2FaceCreate -- 面对面创建队伍，全量更新
    self.baseInfoChangedHandlers[TeamChangeType.TeamLeader] = self.HandleCSTeamChangeLeaderNtf -- 转让队长
    self.baseInfoChangedHandlers[TeamChangeType.QuickJoinChange] = self.HandleCSTeamChangeQuickJoinNtf -- 转让队长

    self.memberChangedHandlers = {}
    self.memberChangedHandlers[TeamChangeType.MemberJoin] =  self.HandleCSTeamJoinBCNtf -- 新队员加入队伍
    self.memberChangedHandlers[TeamChangeType.MemberLeave] = self.HandleCSTeamExitBCNtf -- 有队员退出队伍
    self.memberChangedHandlers[TeamChangeType.MemberKick] = self.HandleCSTeamKickMemBCNtf -- 有队员被踢出队伍
    self.memberChangedHandlers[TeamChangeType.MemberState] = self.HandleTeamMemberStateChange -- 队员state信息变化
    self.memberChangedHandlers[TeamChangeType.MemberEquipPosition] = self.HandleCSTeamEquipPositionChangeNtf -- 队员装备改变
    self.memberChangedHandlers[TeamChangeType.MemberLevel] = self.HandleCSTeamMemberLevelNtf -- 队员等级改变
    self.memberChangedHandlers[TeamChangeType.MemberPackUpdate] = self.HandleCSTeamMemberPackUpdateNtf -- 队员大小包更新
    self.memberChangedHandlers[TeamChangeType.MemberPackDownloadFinish] = self.HandleCSTeamMemberPackDownloadFinishNtf -- 队员大小包下载完成
    self.memberChangedHandlers[TeamChangeType.TeamDismiss] = self.HandleCSTeamDismissNtf

    self.baseInfoChangedHandlers[TeamChangeType.TeamChangeIsRankedMatch] = self.HandleCSTeamChangeRankMatchNtf -- 排位赛模式改变

end

function TeamServer:InitEvents()
    self.Events =
    {
        evtTeamInfosUpdated = LuaEvent:NewIns("TeamServer.evtTeamInfosUpdated"),
        evtMatchingStateChanged = LuaEvent:NewIns("TeamServer.evtMatchingStateChanged"),
        evtTeammateJoined = LuaEvent:NewIns("TeamServer.evtTeammateJoined"),
        evtTeammateLeft = LuaEvent:NewIns("TeamServer.evtTeammateLeft"),
        evtYouAreKicked = LuaEvent:NewIns("TeamServer.evtYouAreKicked"),
        evtYouLeaveTeam = LuaEvent:NewIns("TeamServer.evtYouLeaveTeam"),
        evtCaptialChanged = LuaEvent:NewIns("TeamServer.evtCaptialChanged"),
        evtReadyStateChanged = LuaEvent:NewIns("TeamServer.evtReadyStateChanged"),
        evtAutoMatchChanged = LuaEvent:NewIns("TeamServer.evtAutoMatchChanged"),
        evtTeammateStateChanged = LuaEvent:NewIns("TeamServer.evtTeammateStateChanged"),
        evtTeammateLevelChanged = LuaEvent:NewIns("TeamServer.evtTeammateLevelChanged"),
        evtTeammatePackUpdateChanged = LuaEvent:NewIns("TeamServer.evtTeammatePackUpdateChanged"),
        evtMatchGatePunishFailed = LuaEvent:NewIns("TeamServer.evtMatchGatePunishFailed"),
        evtMatchGatePackQuestMiss = LuaEvent:NewIns("TeamServer.evtMatchGatePackQuestMiss"),

        evtTeamCreated = LuaEvent:NewIns("TeamServer.evtTeamCreated"),
        evtJoinTeam = LuaEvent:NewIns("TeamServer.evtJoinTeam"),
        evtOnTeamRecruitmentStateChanged = LuaEvent:NewIns("evtOnTeamRecruitmentStateChanged"),

        evtRefreshMatchRemainTime = LuaEvent:NewIns("TeamServer.evtRefreshMatchRemainTime"),
        -- evtSetIsMatching = LuaEvent:NewIns("TeamServer.evtSetIsMatching"),

        evtTeammateDisplayChanged = LuaEvent:NewIns("TeamServer.evtTeammateDisplayChanged"),
        evtTeammateEquipChange = LuaEvent:NewIns("TeamServer.evtTeammateEquipChange"),

        evtReceiveTeamInvite = LuaEvent:NewIns("evtReceiveTeamInvite"),

        evtReceiveTeamPrepare = LuaEvent:NewIns("evtReceiveTeamPrepare"),

        evtTeamFace2FaceCreate = LuaEvent:NewIns("evtTeamFace2FaceCreate"),

        evtTeamRecievePlayerRes = LuaEvent:NewIns("evtTeamRecievePlayerRes"),  --收到玩家的对邀请/申请回应

        evtTeamModesChanged = LuaEvent:NewIns("evtTeamModesChanged"), -- 队伍模式变更

        evtMatchAllocTriggerReconnect=LuaEvent:NewIns("evtMatchAllocTriggerReconnect"),--弹出重连弹窗

        evtRankMatchChanged=LuaEvent:NewIns("evtRankMatchChanged"),--排位赛改变
        evtMatchFailedTip=LuaEvent:NewIns("evtMatchFailedTip"),--跑刀入局提示，不允许加队友
        evtMatchRankUnlockNtf=LuaEvent:NewIns("evtMatchRankUnlockNtf"),--排位赛积分赛解锁

        --BHD
        evtLaunchUE5 = LuaEvent:NewIns("evtLaunchUE5"),--拉起BHD
        evtQuickJoinModeChange = LuaEvent:NewIns("TeamServer.evtQuickJoinModeChange"),--合作模式队伍是否公开模式改变事件

		--- Steam自动登录
        evtOnSteamAutoLogin = LuaEvent:NewIns("evtOnSteamAutoLogin"),

        evtEndMatchReturnSOLMoney=LuaEvent:NewIns("evtEndMatchReturnSOLMoney"),--取消匹配返还哈夫币检查
        evtFaceCheck=LuaEvent:NewIns("evtFaceCheck"),--面部识别

        evtJoinPlatformTeamSession = LuaEvent:NewIns("evtJoinPlatformTeamSession"),
        evtUpdatePlatformTeamSession = LuaEvent:NewIns("evtUpdatePlatformTeamSession"),
        evtSendTeamSessionInviteToUser = LuaEvent:NewIns("evtSendTeamSessionInviteToUser"),
        evtCreatePlatformTeamSession = LuaEvent:NewIns("evtCreatePlatformTeamSession"),
        evtDestroyPlatformTeamSession = LuaEvent:NewIns("evtDestroyPlatformTeamSession"),
        evtCanTeamWithPlayerViaCrossPlayPermission = LuaEvent:NewIns("evtCanTeamWithPlayerViaCrossPlayPermission"),
        evtCanSendTeamMatchWitchCurrentTeamMembers = LuaEvent:NewIns("evtCanSendTeamMatchWitchCurrentTeamMembers"),
        evtProcessPlatformSession = LuaEvent:NewIns("evtProcessPlatformSession"),

        evtSendSecondLanguage = LuaEvent:NewIns("evtSendSecondLanguage"),

        evtAlreadyEnterMatch = LuaEvent:NewIns("evtAlreadyEnterMatch"),
        evtWhenGamingReceiveTeamInvite = LuaEvent:NewIns("evtWhenGamingReceiveTeamInvite"),             ---局内收到入队申请
    }
end

function TeamServer:ResetTeamInfos()
    self.teamInfos = {
        TeamID = 0,
        CapitalID = 0,
        MatchID = 0,
        Modes = {},
        AddMemberType = nil,
        TeamState = 0,
        ---@type TeamServerPlayerInfo[] 队伍成员
        Members = {},
        Slots = {},
        --BEGIN VIRTUOS MODE @Zhang Yingqi
        Session = "",
        --END VIRTUOS MODE
    }
end

-------------查询接口 Start-------------
function TeamServer:IsReady(teammateID)
    teammateID = teammateID or self:GetMyID()
    local readyState = self:GetReadyState(teammateID)
    return (readyState & TeamMemberState.MemReady == TeamMemberState.MemReady)
end

function TeamServer:IsInMatch(teammateID)
    teammateID = teammateID or self:GetMyID()
    local readyState = self:GetReadyState(teammateID)
    return (readyState & TeamMemberState.MemInMatch == TeamMemberState.MemInMatch)
end

function TeamServer:FindMember(targetPlayerID)
    local teamMates = self.teamInfos.Members
    for _, V in pairs(teamMates) do
        if tostring(V.PlayerID) == tostring(targetPlayerID) then return V end
    end
    return nil
end

function TeamServer:IsInTeam()
    return Server.AccountServer:IsInTeam() and self:GetTeamID() ~= 0
end

function TeamServer:IsCaptial(teammateID)
    teammateID = teammateID or self:GetMyID()
    return self:IsInTeam() and (self:GetCapitalID() == teammateID)
end

function TeamServer:IsMember()
    return self:IsInTeam() and (not self:IsCaptial())
end

function TeamServer:HasOtherMembers()
    return (self:GetTeamMatesNum() > 1)
end

function TeamServer:IsMe(playerID)
    return Server.AccountServer:GetPlayerId() == playerID
end

function TeamServer:IsAllTeamMatesReady()
    local members = self.teamInfos.Members
    for _, v in pairs(members) do
        if not self:IsMe(v.PlayerID) and not self:IsCaptial(v.PlayerID) and v.State & TeamMemberState.MemReady == 0 then
            return false
        end
    end
    return true
end

function TeamServer:IsMatching()
    return self.teamInfos.TeamState == TeamState.TeamAllocMatch
end
-------------查询接口 end-------------

-------------Get Set函数 Start-------------
function TeamServer:GetTeamID()
    return self.teamInfos.TeamID
end

function TeamServer:SetTeamID(teamID)
    self.teamInfos.TeamID = teamID
    logerror("[v_dzhanshen] TeamID="..tostring(teamID))
end

function TeamServer:SetSessionId(sessionId)
    self.teamInfos.Session = sessionId
end

function TeamServer:GetCapitalID()
    return self.teamInfos.CapitalID
end

function TeamServer:SetCapitalID(capitalID)
    self.teamInfos.CapitalID = capitalID
end

function TeamServer:SetTeamState(newTeamState)
    self.teamInfos.TeamState = newTeamState
end

function TeamServer:GetTeamState()
    return self.teamInfos.TeamState
end

--根据服务器上的排序来获取成员信息,即按入队顺序排序
function TeamServer:GetTeamInfoByServerSeat(index)
    for _,info in pairs(self.teamInfos.Members) do
        if info.Seat + 1 == index then
            return info
        end
    end
    return nil
end

function TeamServer:SetTeamMatchID(newMatchID)
    self.teamInfos.MatchID = newMatchID
end

function TeamServer:SetTeamMatchMode(newModes, addMemberType, spawnPoint, groupId, bShouldShowTip)
    bShouldShowTip = setdefault(bShouldShowTip, true)
    local newMode = self:_FetchLegacyMode(newModes)
    self.teamInfos.Mode = newMode
    self.teamInfos.Modes = newModes
    self.teamInfos.AddMemberType = addMemberType
    self.teamInfos.spawn_point = spawnPoint
    if newModes ~= nil then
        if not self:IsMember()then
            Server.GameModeServer:SetMatchMode(newMode, bShouldShowTip)
            Server.GameModeServer:SetMatchModes(newModes)
        end
        self.Events.evtTeamModesChanged:Invoke(newModes, groupId)
    end
end

-- 获取选择的模式列表
---@return table
function TeamServer:GetTeamMatchModes()
    return self.teamInfos.Modes
end

function TeamServer:GetTeamMatchMode()
    if self.teamInfos.Modes then
        return self.teamInfos.Modes[1] or self.teamInfos.Mode
    else
        return self.teamInfos.Mode
    end
end

function TeamServer:GetTeamMatchID()
    return self.teamInfos.MatchID
end

function TeamServer:GetMembers()
    return self.teamInfos.Members
end

function TeamServer:GetTeamNum()
    local n = #(table.keys(self.teamInfos.Members))
    log("TeamServer:GetTeamNum",n)
    return n
end


function TeamServer:GetAllMemberID()
    local members = self:GetMembers()
    local memberID = {}
    for _, V in pairs(members) do
        table.insert(memberID, V.PlayerID)
    end
    return memberID
end

function TeamServer:GetTeamMatesNum()
    return table.nums(self.teamInfos.Members)
end

function TeamServer:SetAutoMatchTeamMates(bIsAutoMatch)    
    self.bIsAutoMatchTeamMates = bIsAutoMatch
    self.Events.evtAutoMatchChanged:Invoke(bIsAutoMatch)
end

function TeamServer:GetAutoMatchTeamMates()
    return self.bIsAutoMatchTeamMates
end

function TeamServer:GetMyInfo()
    local myPlayerID = Server.AccountServer:GetPlayerId()
    local myInfo = self:FindMember(myPlayerID) or {}
    return myInfo
end

function TeamServer:GetMyID()
    local myInfo = self:GetMyInfo()
    return myInfo.PlayerID
end

function TeamServer:GetTeamInfos()
    return self.teamInfos
end

function TeamServer:SetIsBeInvited(bIsInvited)
    self.bIsInvited = bIsInvited
end

function TeamServer:GetIsBeInvited()
    return self.bIsInvited
end

function TeamServer:GetReadyState(teammateID) -- My Ready State
    teammateID = teammateID or self:GetMyID()
    local member = self:FindMember(teammateID)
    return member and member.State or TeamMemberState.MemUnReady
end

function TeamServer:SetReadyState(newState) -- My Ready State
    local myPlayerID = Server.AccountServer:GetPlayerId()
    self.teamInfos.Members[myPlayerID].State = newState
end

function TeamServer:SetHero(teammateID, heroID)
    local teammateInfo = self:FindMember(teammateID)
    if teammateInfo then
        teammateInfo.HeroID = heroID or ""
        teammateInfo.HeroName = HeroHelperTool.GetHeroName(heroID) or ""
    end
end

function TeamServer:_SetIsMatching(bMatchingState)
    if bMatchingState then
        self:SetTeamState(TeamState.TeamAllocMatch)
    else
        self:SetTeamState(TeamState.TeamIdle)
    end
end

-- 提取遗留下来的MatchID
---@param modes pb_MatchModeInfo[]
---@return integer
function TeamServer:_FetchLegacyModeID(modes)
    local mode = self:_FetchLegacyMode(modes)
    if mode ~= nil then
        return mode.match_mode_id
    else
        return 0
    end
end

-- 提取遗留下来的MatchMode
---@param modes pb_MatchModeInfo[]
---@return table mode 返回值有两种类型
function TeamServer:_FetchLegacyMode(modes)
    modes = setdefault(modes, {})
    local _, mode = next(modes)
    return setdefault(mode, {})
end
-------------Get Set函数 End-------------

-------------队伍基础信息变动通知 Start-------------
function TeamServer:HandleCSTeamChangeMatchIDNtf(ntf)
    loginfo("TeamServer:HandleCSTeamChangeMatchIDNtf",ntf.MatchID,ntf.GroupID)
    logtable(ntf.Mode,true)
    logtable(ntf.Modes,true)

    local matchId = self:_FetchLegacyModeID(ntf.Modes)
    GameModeSvr:SetMatchID(matchId)
    self:SetTeamMatchID(matchId)
    local matchMode = {}
    deepcopy(matchMode, ntf.Modes[1])
    matchMode.add_member_type = ntf.AddMemberType
    matchMode.spawn_point = ntf.spawn_point
    self:SetTeamMatchMode(ntf.Modes, ntf.AddMemberType, ntf.spawn_point, ntf.GroupID)
    GameModeSvr:SetMatchMode(matchMode)
    GameModeSvr:SetMatchModes(ntf.Modes)

    local modes = {}
    deepcopy(modes, ntf.Modes)
    self.Events.evtTeamModesChanged:Invoke(modes, ntf.GroupID, true) --补充第三关参数，bBaseInfoChanged - TeamChangeMatchIDNtf

    self:SetAutoMatchTeamMates(ntf.IsAddMember)

    --队伍模式改变的时候，刷新玩家位置
    self:UpdateTeamSeat()
end

function TeamServer:HandleCSTeamChangeRankMatchNtf(ntf)
    if self:GetTeamMatchMode() and self:GetTeamMatchMode().game_mode==MatchGameMode.TDMGameMode then
        loginfo(string.format("[HallPrepareRegion] TeamServer HandleCSTeamChangeRankMatchNtf EnableMPRankMatch:%s",tostring(ntf.IsRankedMatch)))
        self:EnableMPRankMatch(ntf.IsRankedMatch)
    else
        loginfo(string.format("[HallPrepareRegion] TeamServer HandleCSTeamChangeRankMatchNtf EnableSOLRankMatch:%s",tostring(ntf.IsRankedMatch)))
        self:EnableSOLRankMatch(ntf.IsRankedMatch)
    end
    if self:IsMember() then
        loginfo(string.format("[HallPrepareRegion] TeamServer HandleCSTeamChangeRankMatchNtf player:%s Set RankcheckBox:%s",Server.AccountServer:GetPlayerIdStr(),tostring(self._bEnableRankMatch)))
        self.Events.evtRankMatchChanged:Invoke(self._bEnableRankMatch)
    end
end

function TeamServer:UpdateTeamSeat()
    local function onCSTeamInfoTRes(res)
        if not res or res.result ~= 0 then
            return
        end
        --先destroy所有的charcter
        for _,memberInfo in pairs(self.teamInfos.Members) do
            self.Events.evtTeammateDisplayChanged:Invoke(memberInfo, EDisplayChangeType.Distroy)
        end
        --再重新生成charcter
        self:SetMembers(res.Info.Members)
        --更新玩家信息卡
        self.Events.evtTeamInfosUpdated:Invoke(self:GetTeamID())
    end
    local req = pb.CSTeamInfoTReq:New()
    req.TeamID = self:GetTeamID()
    req:Request(onCSTeamInfoTRes)
    if req.TeamID==0 then
        logerror("TeamServer:UpdateTeamSeat teamId is 0",debug.traceback())
    end
end

function TeamServer:HandleCSTeamInfoChangeNtf(ntf) -- message CSTeamBaseInfoChangeNtf
    local currTeamState = ntf.State
    loginfo("TeamState Changed to", currTeamState)
    if currTeamState == TeamState.TeamIdle then
        self:HandleTeamIdle(ntf)
    elseif currTeamState == TeamState.TeamAllocMatch then
        self:HandleTeamAllocMatch(ntf)
    elseif  currTeamState == TeamState.TeamInMatch then
        self:HandleTeamInMatch(ntf)
    end
end

function TeamServer:HandleTeamIdle(_)
    Server.MatchServer:EndMatching()
    -- self:_SetIsMatching(false)
    self:SetTeamState(TeamState.TeamIdle)
    if not self:IsCaptial() then
        DFMGlobalEvents.evtReportEvent:Invoke(EReportCategoryName.Match, EMatchFlow.MatchRes,  EReportStatusCode.Failed,  -1, "")
        -- self.Events.evtEndMatchReturnSOLMoney:Invoke()
    end
end

function TeamServer:HandleTeamAllocMatch(ntf)
    if not self:IsCaptial() then
        Server.MatchServer:SetMatchTimeInfo(ntf)
        Server.MatchServer:StartMatching()
        -- self:_SetIsMatching(true)
        self:SetTeamState(TeamState.TeamAllocMatch)
        DFMGlobalEvents.evtReportEvent:Invoke(EReportCategoryName.Match, EMatchFlow.MatchRes,  EReportStatusCode.Success,  0, "", true)
    end
end

function TeamServer:HandleTeamInMatch(_)
    self:SetTeamState(TeamState.TeamInMatch)
end

function TeamServer:HandleCSFace2FaceCreate(ntf)
    logwarning("TeamServer:HandleCSFace2FaceCreate")
    self:SetTeamID(ntf.TeamID)
    local fOnTeamInfosUpdated=function()
        self.Events.evtTeamFace2FaceCreate:Invoke()

    end
    self:UpdateTeamInfos(ntf.TeamID,fOnTeamInfosUpdated)
    
end

function TeamServer:HandleCSTeamUpdateAllInfo(ntf)
    self:UpdateTeamInfos(ntf.TeamID)
end

function TeamServer:HandleCSTeamChangeLeaderNtf(ntf)
    self:UpdateCaptial(ntf.LeaderID)
    local newCaptialInfo = self:FindMember(ntf.LeaderID)
    if newCaptialInfo then
        local newCaptialNick = newCaptialInfo.PlayerName
        local bFrontEnd = Facade.GameFlowManager:CheckIsInFrontEnd()
        if bFrontEnd then
            LuaGlobalEvents.evtServerShowTip:Invoke(string.format(ServerTipCode.TeamSwitchCaptain,newCaptialNick))
        end
    end
end

function TeamServer:HandleCSTeamChangeQuickJoinNtf(ntf)
    self:SetBHDOpenQuickJoinMode(ntf.IsOpenQuickJoin)
end

--弱网恢复全量拉队伍信息
function TeamServer:_OnRelayConnected()
    local onCSStateBatchGetInfoReq = function(res)
        if res.result == 0 and not table.isempty(res.player_info) then
            local playerState = table.first(res.player_info)
            local teamId = playerState.team_id
            if self:GetTeamId()==0 then return end
            if playerState.state & GlobalPlayerStateEnums.EPlayerState_InTeam == 0 then
                local teamId = self:GetTeamID()
                self:ClearTeamInfos()
                self.Events.evtYouLeaveTeam:Invoke(self:GetTeamID(), teamId)
            else
                self:UpdateTeamInfos(teamId)
            end
        end
    end

    local req = pb.CSStateBatchGetInfoReq:New()
    req.player_id = {Server.AccountServer:GetPlayerId()}
    req:Request(onCSStateBatchGetInfoReq)
end


-------------队伍基础信息变动通知 End-------------

-------------成员变动通知 Start-------------
function TeamServer:HandleCSTeamJoinBCNtf(ntf) -- message CSTeamMemberChangeNtf
    logwarning("TeamServer:HandleCSTeamJoinBCNtf","Player",ntf.Member.PlayerID,"Player",ntf.Member.Nick)
    --不是自己才处理
    if not self:IsMe(ntf.Member.PlayerID) then
        local memberPbInfo = ntf.Member
        local newMember = self:BuildTeamMemberInfo(memberPbInfo)
        self:AddMember(newMember.PlayerID, newMember)

        self.Events.evtTeammateJoined:Invoke(newMember.PlayerID)
        if Facade.GameFlowManager:CheckIsInFrontEnd() then
            LuaGlobalEvents.evtServerShowTip:Invoke(string.format(ServerTipCode.TeamMemJoin,newMember.PlayerName))
        end
    else
        self:ReqUpdatePackQuest(ntf.TeamID)
        LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.TeamSelfJoin)
    end
    -- BEGIN MODIFICATION - VIRTUOS
    if IsPS5() and self:IsInTeam() and self:GetTeamNum() > 1 then
        local identityManager = UDFMOnlineIdentityManager.Get(GetGameInstance())
        identityManager:SetUsingPlatformPremiumFeatures(EPlatformPremiumFeaturesType.RealtimeMultiplay, true)
    end
    -- END MODIFICATION - VIRTUOS
end

function TeamServer:HandleCSTeamExitBCNtf(ntf) -- message CSTeamMemberChangeNtf
    local bFrontEnd = Facade.GameFlowManager:CheckIsInFrontEnd()
    local teamId = self:GetTeamID()
    if teamId ~= ntf.TeamID then
        return
    end
    --不是自己才处理
    if not self:IsMe(ntf.Member.PlayerID) then
        local memberPbInfo = ntf.Member
        local xxitMemberID = memberPbInfo.PlayerID
        local xxitMemberInfo = self:FindMember(xxitMemberID)
        local xxitMemberNick = xxitMemberInfo and xxitMemberInfo.PlayerName or ""
        local newCaptialID = ntf.LeaderID -- 最新的leader_id
        loginfo("TeamServer:HandleCSTeamExitBCNtf")
        local bCaptialChanged = newCaptialID ~= self:GetCapitalID()
        if bCaptialChanged then
            self:UpdateCaptial(newCaptialID)
        end
        local seat = self:FindSeat(xxitMemberID)
        self:RemoveMember(xxitMemberID)
        self.Events.evtTeammateLeft:Invoke(xxitMemberID,xxitMemberNick,teamId,seat)
        if self:GetTeamMatesNum() > 1 then
            if not bCaptialChanged then
                if bFrontEnd then
                    LuaGlobalEvents.evtServerShowTip:Invoke(string.format(ServerTipCode.TeamMemExit,xxitMemberNick))
                end
            else
                local newCaptialNick = Server.SDKInfoServer:GetUserName()
                if newCaptialID ~= Server.AccountServer:GetPlayerId() then
                    local newCaptialInfo = self:FindMember(newCaptialID)
                    newCaptialNick = newCaptialInfo.PlayerName
                end
                if bFrontEnd then
                    LuaGlobalEvents.evtServerShowTip:Invoke(string.format(ServerTipCode.TeamChangeCaptain,xxitMemberNick,newCaptialNick))
                end
            end
        elseif bFrontEnd then
            LuaGlobalEvents.evtServerShowTip:Invoke(string.format(ServerTipCode.TeamMemExit,xxitMemberNick))
        end
    else
        --如果在队伍中，则需要退队
        if self:IsInTeam() then
            self:ClearTeamInfos()
            self.Events.evtYouLeaveTeam:Invoke(self:GetTeamID(), teamId,true)
            LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.TeamSelfExit)
        end
    end
end

function TeamServer:HandleCSTeamDismissNtf(ntf)
    loginfo('TeamServer:HandleCSTeamDismissNtf(ntf)')
    if Server.MatchServer:GetIsWaitForGotoGame() then
        LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.TeamCantExitOnMatchSuccess)
        return
    end
    local teamId = self:GetTeamID()
    self:ClearTeamInfos(true)
    self.Events.evtYouLeaveTeam:Invoke(self:GetTeamID(), teamId)
    LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.TeamOwnerDismiss)
    if Server.MatchServer:GetIsMatching()then
        Server.MatchServer:EndMatching()
    end
end

function TeamServer:HandleCSTeamKickMemBCNtf(ntf) -- message CSTeamMemberChangeNtf
    local memberPbInfo = ntf.Member
    local kickedMemberID = memberPbInfo.PlayerID
    if kickedMemberID == Server.AccountServer:GetPlayerId() then
        local teamId = self:GetTeamID()
        self:ClearTeamInfos(true)
        self.Events.evtYouAreKicked:Invoke(self:GetTeamID(), teamId)
        LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.RemoveFromTeam)
    else
        local exitPlayer = self:FindMember(kickedMemberID)
        local seat = self:FindSeat(kickedMemberID)
        self:RemoveMember(kickedMemberID)
        local teamId = self:GetTeamID()
        self.Events.evtTeammateLeft:Invoke(kickedMemberID,exitPlayer.PlayerName,teamId,seat)
        LuaGlobalEvents.evtServerShowTip:Invoke(string.format(ServerTipCode.TeamMateIsKicked,exitPlayer.PlayerName))
    end
end

function TeamServer:HandleTeamMemberStateChange(ntf) --- message CSTeamMemberChangeNtf
    logwarning("TeamServer:HandleTeamMemberStateChange","playerId",ntf.Member.PlayerID,"state",ntf.Member.State)
    local memberPbInfo = ntf.Member
    local memberID = memberPbInfo.PlayerID
    local newMemberState = ntf.Member.State
    local memberInfo = self:FindMember(memberID)
    if memberInfo then
        self:SetState(memberID, newMemberState)
        self.Events.evtTeammateStateChanged:Invoke(memberID)
    end
end

function TeamServer:HandleCSTeamMemberLevelNtf(ntf) --- message CSTeamMemberChangeNtf
    logwarning("TeamServer:HandleCSTeamMemberLevelNtf")
    local memberPbInfo = ntf.Member
    local memberID = memberPbInfo.PlayerID
    local memberInfo = self:FindMember(memberID)
    if memberInfo then
        memberInfo.Level = memberPbInfo.Info.level
        memberInfo.SeasonLevel = memberPbInfo.Info.season_lvl
        self.Events.evtTeammateLevelChanged:Invoke(memberID)
    end
end

function TeamServer:HandleCSTeamMemberPackUpdateNtf(ntf) --- message CSTeamMemberChangeNtf 同步已下载的地图
    loginfo("TeamServer:HandleCSTeamMemberPackUpdateNtf",ntf.Member.PlayerID,ntf.Member.Nick)
    logtable(ntf.Member.PackQuestID,true)
    local foundMember=self:FindMember(ntf.Member.PlayerID)
    if foundMember then
        foundMember.PackQuestID=ntf.Member.PackQuestID
    else
        logerror("TeamServer:HandleCSTeamMemberPackUpdateNtf member not found!!!")
        
    end
end

function TeamServer:HandleCSTeamMemberPackDownloadFinishNtf(ntf) --- message CSTeamMemberChangeNtf 下载完地图
    loginfo("TeamServer:HandleCSTeamMemberPackDownloadFinishNtf",ntf.Member.PlayerID,ntf.Member.Nick)
    logtable(ntf.Member.PackQuestID,true)
    local foundMember=self:FindMember(ntf.Member.PlayerID)
    if foundMember then
        local litePackageTable=Facade.TableManager:GetTable("LitePackageDownload")
        local packId=ntf.Member.PackQuestID[1]
        if litePackageTable[packId] and litePackageTable[packId].MapOrModeDownload==1 then--1是地图资源包
            if not self:IsMe(ntf.Member.PlayerID) then
                self.Events.evtTeammatePackUpdateChanged:Invoke(ntf.Member.Nick,litePackageTable[packId].QuestName)
            end
        end
    else
        logerror("TeamServer:HandleCSTeamMemberPackDownloadFinishNtf member not found!!!")
        
    end
end

function TeamServer:HandleCSTeamEquipPositionChangeNtf(ntf)
    loginfo("TeamServer:HandleCSTeamEquipPositionChangeNtf",ntf.Member.PlayerID,ntf.Member.Nick,ntf.Member.EquipPrice)
    
    -- if Server.MatchServer:GetIsMatching() then
    --     return
    -- end
    local memberPbInfo = ntf.Member
    local memberId = memberPbInfo.PlayerID

    if self:FindMember(memberId) then
        local oldMemberInfo = self:FindMember(memberId)
        memberPbInfo.PackQuestID=oldMemberInfo.PackQuestID--只保留除packId以外的信息
        local memberInfo = self:BuildTeamMemberInfo(memberPbInfo)
        self:UpdateMember(memberId, memberInfo)
    end
end
-------------成员变动通知 End-------------

--------------------------------------------------------------------------
--- 服务器请求
--------------------------------------------------------------------------
function TeamServer:ChangeCaptial(newCaptialID)
    if not newCaptialID or not self:IsInTeam() then
        return
    end
    local req = pb.CSTeamChangeLeaderTReq:New()
    req.TeamID = self:GetTeamID()
    req.NewLeaderID = newCaptialID
    req:Request()
    
end

function TeamServer:SyncMatchID()
    if not self:IsCaptial() then
        return
    end
    local teamId = self:GetTeamID()
    local matchID = GameModeSvr:GetMatchID()
    local mode = GameModeSvr:GetMatchMode()
    if not mode then
        mode=GameModeSvr:GetUnknownGameMode()
    end
    local bAutoMatchTeamMates = self:GetAutoMatchTeamMates()
    self:AsyncChangeTeamMatchMode(teamId, { mode }, bAutoMatchTeamMates)
end

---@deprecated see TeamServer:AsyncChangeTeamMatchMode
function TeamServer:ReqChangeMatchId(teamId, matchId, mode, bIsAddMember, fCallback)
    log("TeamServer:ReqChangeMatchId ", matchId)
    local fCSTeamChangeMatchIDTRes = function(res)
        if res.result == 0 then
            -- TODO:应该还需要同步数据 -- 好像是在ntf里处理 -- 理论上两个都要做处理
        elseif res.result == Err.TeamMemberSCAVInvalid then
            LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.TeamScavInCD)
        elseif res.result == Err.TeamFull then
            LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.TeamCantSwitchMap)
        elseif res.result == Err.TeamMatchIDInvalid then
            local tip = string.format(ServerTipCode.TeamMatchIDIllegal, tostring(matchId))
            LuaGlobalEvents.evtServerShowTip:Invoke(tip)
        end
        if fCallback then fCallback(res) end
    end
    local req = pb.CSTeamChangeMatchIDTReq:New()
    req.TeamID = teamId
    req.MatchID = matchId
    req.IsAddMember = bIsAddMember
    req.Mode = mode
    req.AddMemberType = mode.add_member_type
    req.spawn_point = mode.spawn_point
    req:Request(fCSTeamChangeMatchIDTRes)
end

-- 异步修改(因为要发req)队伍匹配模式
---@param teamId integer
---@param modes pb_MatchModeInfo[]
---@param bIsAddMember boolean
---@return Promise
function TeamServer:AsyncChangeTeamMatchMode(teamId, modes, bIsAddMember, groupId, fCallback)
    loginfo("TeamServer:AsyncChangeTeamMatchMode")
    logtable(modes,true)
    teamId = setdefault(teamId, 0)
    modes = setdefault(modes, {})
    bIsAddMember = setdefault(bIsAddMember, false)
    local mode = self:_FetchLegacyMode(modes)

    local req = pb.CSTeamChangeMatchIDTReq:New()
    req.TeamID = teamId
    req.Modes = modes
    req.IsAddMember = bIsAddMember
    req.Mode = mode
    req.AddMemberType = mode.add_member_type
    req.spawn_point = mode.spawn_point
    req.GroupId = groupId

    return Promise:NewIns(function(fReslove, fReject)
        ---@param res pb_CSTeamChangeMatchIDTRes
        req:Request(function(res)
            if res.result == 0 then
                fReslove(res)
                if fCallback then fCallback() end
                return
            end
            -- 失败逻辑
            if res.result == Err.TeamMemberSCAVInvalid then
                LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.TeamScavInCD)
            elseif res.result == Err.TeamFull then
                LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.TeamCantSwitchMap)
            elseif res.result == Err.TeamMatchIDInvalid then
                local tip = string.format(ServerTipCode.TeamMatchIDIllegal, tostring(matchId))
                LuaGlobalEvents.evtServerShowTip:Invoke(tip)
            end
            if fCallback then fCallback() end
            fReject(res)
        end,{bEnableHighFrequency = true})
    end):Then(function()
        -- 成功了就设置一下状态
        -- self:SetTeamMatchMode(modes, bIsAddMember, mode.spawn_point, groupId)
    end)
end

-- 发送修改当前队伍模式的请求
---@param newModes pb_MatchModeInfo[] 新的地图列表
---@param groupId integer|nil 用于区分不同地图组的Id(目前只有大战场在用)
---@param bIsAddMember boolean 是否自动匹配队友
---@param fSuccess fun(...) 变更成功后的回调
function TeamServer:SendTeamChangeModesRequest(newModes, groupId, bIsAddMember, fSuccess, ...)
    log("TeamServer:SendTeamChangeModesRequest",groupId, bIsAddMember)
    if type(newModes)=="table" then
        logtable(newModes,true)
    end
    local params = ...
    local req = pb.CSTeamChangeMatchIDTReq:New()
    req.TeamID = self:GetTeamID()
    req.Modes = setdefault(newModes, {})
    req.GroupID = setdefault(groupId, 1)
    req.IsAddMember = bIsAddMember
    req:Request(function (res)
        if res.result == 0 then
            if fSuccess then fSuccess(res, params) end
        else
            -- TODO 显示提示
        end
    end)
end

-- 检查是否可以开始匹配
---@return boolean
function TeamServer:CheckCanStartMatch()
    return not Server.MatchServer:GetIsMatching() and self:IsCaptial() and self:IsAllTeamMatesReady()
end


function TeamServer:ExitTeam(fCallBack)
    logwarning("TeamServer:ExitTeam")
    
    if Server.MatchServer:GetIsWaitForGotoGame() then
        LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.TeamCantExitOnMatchSuccess)
        return
    end

    --BEGIN VIRTUOS MODE @Zhang Yingqi
    if PLATFORM_PS5 == 1 then
        local otherPS5PlayersNum = 0
        local ps5Plat = self:GetMyInfo().PlayerSimpleInfo.plat
        local otherTeamMembers = self:GetOtherMembers()
        for _,memberInfo in pairs(otherTeamMembers)do
            if memberInfo.PlayerSimpleInfo.plat == ps5Plat then
                otherPS5PlayersNum = otherPS5PlayersNum + 1
            end
        end
        if otherPS5PlayersNum == 0 then
            self:ReqUpdatePlatformSessionID(self:GetTeamID(), "")
        end
    end
    --END VIRTUOS MODE
    self.isExitting = true
    local teamNum = self:GetTeamNum() --记录退出前队伍成员数量，用于下面判断显示提示
    local req = pb.CSTeamExitTReq:New()
    req.TeamID = self:GetTeamID()
    req:Request(function(res)
        loginfo("TeamServer:ExitTeam res",res.result)
        if res.result == 0 then
            local teamId = self:GetTeamID()
            self:ClearTeamInfos(true)
            self.Events.evtYouLeaveTeam:Invoke(self:GetTeamID(), teamId)
            if teamNum > 1 then --队伍成员大于1时才弹提示
                LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.TeamSelfExit)
            end
            if Server.MatchServer:GetIsMatching()then
                Server.MatchServer:EndMatching()
                self.Events.evtEndMatchReturnSOLMoney:Invoke()
            end
            if fCallBack then fCallBack() end
        else
            Server.AccountServer:GetStateInfo()
            self:UpdateTeamInfos()
        end
        self.isExitting = false
    end)
    if req.TeamID==0 then
        logerror("TeamServer:ExitTeam teamId is 0",debug.traceback())
    end
end
function TeamServer:CreateTeam(matchModes, bNotarget, createType, fCallback, ...)
    matchModes = setdefault(matchModes, {})
    loginfo("CreateTeam, matchmodes = ", #matchModes)

    -- if self._isCreatingTeam then --防止重复创建
    --     return
    -- end
    bNotarget = setdefault(bNotarget, true)
    local params = ...
    local req = pb.CSTeamCreateReq:New()
    req.IsRankedMatch=self._bEnableRankMatch
    loginfo("TeamServer:CreateTeam EnableRankMatch:"..tostring(self._bEnableRankMatch))
    -- req.MatchID = self:_FetchLegacyModeID(matchModes)
    -- if bNotarget then
    --     req.Mode = Server.GameModeServer:GetUnknownGameMode()
    -- else
        -- req.Mode = Server.GameModeServer:GetTeamMatchMode()
    local modes = matchModes

    if table.isempty(modes) then
        modes = Server.GameModeServer:GetTeamMatchModes()

    end
    req.Modes = modes
    req.IsAddMember=self.bIsAutoMatchTeamMates
    req.PackQuestID = Server.LitePackServer:GetAllDownloadedQuestIDs()
    req.CreateType = createType or 99
    logwarning("TeamServer:CreateTeam 上报的QuestID个数",#req.PackQuestID)
    --end
    -- self._isCreatingTeam = true
    req:Request(function(res)
        -- self._isCreatingTeam = false
        if res.result ~= 0 then
            if res.result == Err.TeamMemberSCAVInvalid then
                LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.TeamScavInCD)
            end
            --BEGIN VIRTUOS MODE @Zhang Yingqi
            if PLATFORM_PS5 then
                self.Events.evtDestroyPlatformTeamSession:Invoke()
            end
            --END VIRTUOS MODE
            return
        end


        local newTeamInfo = res.Info

        self:ResetTeamInfos()
        Server.AccountServer:SetPlayerStateCode(GlobalPlayerStateEnums.EPlayerState_InTeam)
        self:SetTeamID(newTeamInfo.TeamID)
        self:SetCapitalID(newTeamInfo.LeaderID)
        local MatchId = self:_FetchLegacyModeID(newTeamInfo.Modes)
        self:SetMembers(newTeamInfo.Members)
        self:SetTeamMatchID(MatchId)
        self:SetTeamMatchMode(newTeamInfo.Modes, newTeamInfo.AddMemberType, newTeamInfo.spawn_point)
        self:SetTeamState(newTeamInfo.State)
        self:SetTeamDSInfo(newTeamInfo.DSTextualIP, newTeamInfo.DSPort, newTeamInfo.DSRoomID, newTeamInfo.Token)
        self:SendChangeEquipNtf()
        self:SetBHDOpenQuickJoinMode(newTeamInfo.IsOpenQuickJoin)
        --BEGIN VIRTUOS MODE @Zhang Yingqi
        if PLATFORM_PS5 == 1 then 
            self.Events.evtUpdatePlatformTeamSession:Invoke() --刷新Session，上传队伍信息

            local sessionId = self:GetSessionID()
            Server.TeamServer:ReqUpdatePlatformSessionID(newTeamInfo.TeamID, sessionId) --上传Session ID到服务器
        end        
        --END VIRTUOS MODE
        self.Events.evtTeamCreated:Invoke(self:GetTeamID())
        if fCallback then fCallback(params) end
    end)
end
---@param modeList pb_MatchModeInfo[] 模式列表
---@param groupId number
---@param addMemberType integer 添加
function TeamServer:CreateTeamWithModeList(modeList, groupId, addMemberType, createType, fCallback, ...)

    loginfo("TeamServer:CreateTeamWithModeList")
    -- if self._isCreatingTeam then --防止重复创建
    --     return
    -- end
    local params = ...
    local req = pb.CSTeamCreateReq:New()
    req.IsRankedMatch=self._bEnableRankMatch
    loginfo("TeamServer:CreateTeamWithModeList EnableRankMatch:"..tostring(self._bEnableRankMatch))
    req.Modes = setdefault(modeList, {})
    req.GroupID = groupId
    req.AddMemberType = setdefault(addMemberType, nil)
    req.PackQuestID = Server.LitePackServer:GetAllDownloadedQuestIDs()
    req.CreateType = createType or 99
    logwarning("TeamServer:CreateTeamWithModeList 上报的QuestID个数",#req.PackQuestID)
    -- self._isCreatingTeam = true
    ---@param res pb_CSTeamCreateRes
    req:Request(function(res)
        -- self._isCreatingTeam = false
        if res.result ~= 0 then
            -- TODO 错误判断
            --BEGIN VIRTUOS MODE @Zhang Yingqi
            if PLATFORM_PS5 then
                self.Events.evtDestroyPlatformTeamSession:Invoke()
            end
            --END VIRTUOS MODE
            return
        end


        local newTeamInfo = res.Info

        self:ResetTeamInfos()
        self:SetTeamID(newTeamInfo.TeamID)
        self:SetCapitalID(newTeamInfo.LeaderID)
        local matchId = self:_FetchLegacyModeID(newTeamInfo.Modes)
        self:SetMembers(newTeamInfo.Members)
        self:SetTeamMatchID(matchId)
        self:SetTeamMatchMode(newTeamInfo.Modes, newTeamInfo.AddMemberType, newTeamInfo.spawn_point, newTeamInfo.GroupID, false)
        self:SetTeamState(newTeamInfo.State)
        self:SendChangeEquipNtf()
        self:SetBHDOpenQuickJoinMode(newTeamInfo.IsOpenQuickJoin) --BHD
        Server.AccountServer:SetPlayerStateCode(GlobalPlayerStateEnums.EPlayerState_InTeam)
        --BEGIN VIRTUOS MODE @Zhang Yingqi
        if PLATFORM_PS5 == 1 then --刷新Session，上传队伍信息
            self.Events.evtUpdatePlatformTeamSession:Invoke()
                        
            local sessionId = self:GetSessionID()
            Server.TeamServer:ReqUpdatePlatformSessionID(newTeamInfo.TeamID, sessionId) --上传Session ID到服务器
        end
        --END VIRTUOS MODE
        self.Events.evtTeamCreated:Invoke(self:GetTeamID())
        if fCallback then fCallback(params) end
    end)
end

---面对面创建队伍
function TeamServer:FaceToFaceCreateTeam(key, fCallback)
    local onCSFaceToFaceCreateTeamTRes = function(res)
        if res.result == 0 and fCallback then
            fCallback()
            self.Events.evtTeamCreated:Invoke(self:GetTeamID())
        end
    end

    local req = pb.CSFaceToFaceCreateTeamTReq:New()
    req.key = tonumber(key)
    req.is_add_member=Server.TeamServer.bIsAutoMatchTeamMates
    req:Request(onCSFaceToFaceCreateTeamTRes)
end

--发起组队邀请
function TeamServer:ReqInvite(matchId, targetPlayerID, playerNick, teamInviteSource, lastRoomId, matchModeInfo, fCallback, customSuccessPrompt)
    local function onCSTeamInviteTRes(res)
        if res ~= nil then
            if res.result == 0 then
                -- BEGIN MODIFICATION - VIRTUOS
                -- 主机平台发起游戏内组队邀请需要同时发送平台邀请
                if PLATFORM_GEN9 == 1 then
                    local UDFMOnlineSessionManager = import("DFMOnlineSessionManager")
                    local DFMOnlineSessionManager = UDFMOnlineSessionManager.Get(GetWorld())
                    if DFMOnlineSessionManager:IsInTeamSession() == false then
                        -- 平台会话还未创建
                        DFMOnlineSessionManager.OnCreateTeamSessionCompleteDelegate:Add(function(res)
                            self.Events.evtSendTeamSessionInviteToUser:Invoke(targetPlayerID)
                        end)
                        self.Events.evtCreatePlatformTeamSession:Invoke()
                    else
                        if DFMOnlineSessionManager:IsTeamSessionCreating() then
                            -- 平台队伍会话正在创建
                            DFMOnlineSessionManager.OnCreateTeamSessionCompleteDelegate:Add(function(res)
                                self.Events.evtSendTeamSessionInviteToUser:Invoke(targetPlayerID)
                            end)
                        else
                            -- 判断邀请对象是否来自Xbox平台
                            self.Events.evtSendTeamSessionInviteToUser:Invoke(targetPlayerID)
                        end
                    end
                end
                -- END MODIFICATION - VIRTUOS
                Server.AccountServer:RecordInvitePlayerColdTab(targetPlayerID)
                LuaGlobalEvents.evtServerShowTip:Invoke(customSuccessPrompt or ServerTipCode.TeamSendInviteSuccess)
                --记录邀请人，超时未回复显示忽略提示
                if isvalid(self._delayTimerHandle) then
                    if isvalid(self._delayTimerHandle[targetPlayerID]) then
                        self._delayTimerHandle[targetPlayerID]:Stop()
                        self._delayTimerHandle[targetPlayerID]:Release()
                        self._delayTimerHandle[targetPlayerID] = nil
                    end
                else
                    self._delayTimerHandle = {}
                end
                self._invitePlayerID[targetPlayerID] = true
                self._delayTimerHandle[targetPlayerID] = Timer:NewIns(16)
				self._delayTimerHandle[targetPlayerID]:AddListener(function ()
                    if self._invitePlayerID[targetPlayerID] then
                        self._invitePlayerID[targetPlayerID] = nil
                        -- azhengzheng:临时干掉忽视请求提示
                        -- LuaGlobalEvents.evtServerShowTip:Invoke(string.format(ServerTipCode.TeamIngoreReq, playerNick))
                    end
				end, self)
				self._delayTimerHandle[targetPlayerID]:Start()
                self._inviteePlayerInfo[targetPlayerID] = {playerId = targetPlayerID, playerSource = teamInviteSource, roomId = lastRoomId}
            elseif res.result == Err.TeamPlatGroupNotMatch then
                
            end
        end
        if fCallback then
            fCallback(res)
        end
    end


    local req = pb.CSTeamInviteTReq:New()
    req.TeamID = self:GetTeamID()
    req.MatchID = matchId
    req.InviteeID = targetPlayerID
    req.source = teamInviteSource
    req.Mode = matchModeInfo
    req:Request(onCSTeamInviteTRes, {bEnableHighFrequency = true})
    if req.TeamID==0 then
        logerror("TeamServer:ReqInvite teamId is 0",debug.traceback())
    end
    -- 经分上报
    LogAnalysisTool.DoSendTeamInviteAndApplyJoinReqLog(teamInviteSource)
end


--发起申请入队
function TeamServer:ApplyJoin(team_id, targetPlayerID, playerNick, teamInviteSource, fCallback, customSuccessPrompt)
    local function onCSTeamApplyJoinTRes(res)
        if res and res.result == 0 then
            Server.AccountServer:RecordInvitePlayerColdTab(targetPlayerID)
            LuaGlobalEvents.evtServerShowTip:Invoke(customSuccessPrompt or ServerTipCode.TeamSendApplySuccess)
            --记录申请对象，超时未回复显示忽略提示
            if isvalid(self._delayTimerHandle) then
                if isvalid(self._delayTimerHandle[team_id]) then
                    self._delayTimerHandle[team_id]:Stop()
                    self._delayTimerHandle[team_id]:Release()
                    self._delayTimerHandle[team_id] = nil
                end
            else
                self._delayTimerHandle = {}
            end
            self._invitePlayerID[team_id] = true
            self._delayTimerHandle[team_id] = Timer:NewIns(16)
            self._delayTimerHandle[team_id]:AddListener(function ()
                if self._invitePlayerID[team_id] then
                    self._invitePlayerID[team_id] = nil
                    -- azhengzheng:临时干掉忽视请求提示
                    -- LuaGlobalEvents.evtServerShowTip:Invoke(string.format(ServerTipCode.TeamIngoreReq, playerNick))
                end
            end, self)
            self._delayTimerHandle[team_id]:Start()
        end
        if fCallback then
            fCallback(res)
        end
    end

    local req = pb.CSTeamApplyJoinTReq:New()
    req.team_id = team_id
    req.source = teamInviteSource
    req.PackQuestID = Server.LitePackServer:GetAllDownloadedQuestIDs()
    req.target_player_id = targetPlayerID
    req.apply_player_game_mode = Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Lobby and 2 or 1
    logwarning("TeamServer:ApplyJoin 上报的QuestID个数",#req.PackQuestID)
    req:Request(onCSTeamApplyJoinTRes)
    -- 经分上报
    LogAnalysisTool.DoSendTeamInviteAndApplyJoinReqLog(teamInviteSource)
end

--通过平台邀请/申请入队
function TeamServer:ApplyJoinByPlatformInvite(team_id, targetPlayerID, playerNick, teamInviteSource, fCallback)
    if IsConsole() == false then
        return
    end

    local function onCSTeamApplyJoinByPlayformRes(res)
        if fCallback then
            fCallback(res)
        end
    end

    local function onCheckCrossPlayPrivilegeCompleted(res)
        if res then
            local req = pb.CSTeamApplyJoinTReq:New()
            req.team_id = team_id
            req.source = teamInviteSource
            req.PackQuestID = Server.LitePackServer:GetAllDownloadedQuestIDs()
            req.target_player_id = targetPlayerID
            req.apply_player_game_mode = Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Lobby and 2 or 1
            --平台特殊渠道标识
            req.SpecialChannel = 1
            logwarning("TeamServer:ApplyJoinByPlatformInvite 上报的QuestID个数", #req.PackQuestID)
            req:Request(onCSTeamApplyJoinByPlayformRes)
            -- 经分上报
            LogAnalysisTool.DoSendTeamInviteAndApplyJoinReqLog(teamInviteSource)
        else
            -- 双方跨平台权限不一致，无法一起组队
            loginfo("TeamServer:ApplyJoinByPlatformInvite CannotTeamUpDueToCrossNetworkPrivilegeMismatch")
            if PLATFORM_PS5 == 1 then
                if not self:IsInTeam() then
                    self.Events.evtDestroyPlatformTeamSession:Invoke()
                end
            end
            LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.CannotTeamUpDueToNetworkMismatch)
        end
    end
    self.Events.evtCanTeamWithPlayerViaCrossPlayPermission:Invoke(targetPlayerID, onCheckCrossPlayPrivilegeCompleted)
end

--从招募申请入队
function TeamServer:ApplyJoinFromRecruitment(team_id, fCallback)
    loginfo("TeamServer:ApplyJoinFromRecruitment",team_id)
    local function onCSTeamApplyJoinFromRecruitmentTRes(res)
        if res and res.result == 0 then
            --房间相关处理
            if Server.AccountServer:IsInRoom() then
                -- 如果已经通过同意请求退出房间数据 -- 那么直接清空房间数据 -- 关闭房间
                Server.RoomServer.Events.evtLeaveRoom:Invoke(Server.RoomServer:GetRoomId())
            end
            --如果在队伍中，则需要退队
            if self:IsInTeam() then
                local teamId = self:GetTeamID()
                self:ClearTeamInfos()
                self.Events.evtYouLeaveTeam:Invoke(self:GetTeamID(), teamId,true)
            end
            --如果正在匹配，则取消匹配
            if Server.MatchServer:GetIsMatching() then
                self:SendCancelMatching()
            end
            self:SetTeamInfos(res.Info)
            self.Events.evtJoinTeam:Invoke(res.Info.TeamID)
        end
        if fCallback then
            fCallback(res)
        end
    end

    local req = pb.CSTeamJoinFromRecruitmentTReq:New()
    req.team_id = team_id
    req.PackQuestID=Server.LitePackServer:GetAllDownloadedQuestIDs()
    logwarning("TeamServer:ApplyJoinFromRecruitment 上报的QuestID个数",#req.PackQuestID)
    req:Request(onCSTeamApplyJoinFromRecruitmentTRes)
end


--从小程序申请入队
function TeamServer:ApplyJoinFromMiniProgram(team_id, fCallback)
    loginfo("TeamServer:ApplyJoinFromMiniProgram",team_id)
    local function onCSTeamJoinFromMiniProgramTRes(res)
        if res and res.result == 0 then
            --房间相关处理
            if Server.AccountServer:IsInRoom() then
                -- 如果已经通过同意请求退出房间数据 -- 那么直接清空房间数据 -- 关闭房间
                Server.RoomServer.Events.evtLeaveRoom:Invoke(Server.RoomServer:GetRoomId())
            end
            --如果在队伍中，则需要退队
            if self:IsInTeam() then
                local teamId = self:GetTeamID()
                self:ClearTeamInfos()
                self.Events.evtYouLeaveTeam:Invoke(self:GetTeamID(), teamId,true)
            end
            --如果正在匹配，则取消匹配
            if Server.MatchServer:GetIsMatching() then
                self:SendCancelMatching()
            end
            self:SetTeamInfos(res.Info)
            self.Events.evtJoinTeam:Invoke(res.Info.TeamID)
        end
        if fCallback then
            fCallback(res)
        end
    end
    local req = pb.CSTeamJoinFromMiniProgramTReq:New()
    req.team_id = team_id
    req.PackQuestID=Server.LitePackServer:GetAllDownloadedQuestIDs()
    logwarning("TeamServer:ApplyJoinFromMiniProgram 上报的QuestID个数",#req.PackQuestID)
    req:Request(onCSTeamJoinFromMiniProgramTRes)
end


-- 释放或持久化队伍状态
---@param bIsSet boolean 是否是设置上, false=释放
---@param state integer 要设置的状态
function TeamServer:PinTeamStates(bIsSet, state)
    log("TeamServer:PinTeamStates", bIsSet, state)
    local req = pb.CSTeamChangeStateTReq:New()
    req.TeamID = self:GetTeamID()
    req.Set = setdefault(bIsSet, false)
    req.State = setdefault(state, nil)
    req:Request(LogUtil.LogTable)
end

--队长收到入队申请
function TeamServer:OnCSTeamApplyJoinNtf(ntf) --CSTeamApplyJoinNtf
    log("TeamServer:OnCSTeamApplyJoinNtf")
    local applyerInfo = ntf.apply_player
    local applyerName = applyerInfo.nick_name
    local teamID = self:GetTeamID()
    local playerID = applyerInfo.player_id
    -- BEGIN MODIFICATION - VIRTUOS
    local function handleTeamApplyJoinNtf()
    -- END MODIFICATION - VIRTUOS
        local fConfirmFunction = function()
            self:Response2ApplyJoinReq(teamID,playerID, ntf.source, ntf.apply_player_game_mode, true)
        end
    
        local fRefuseFunction = function(refuseMsg)
            self:Response2ApplyJoinReq(teamID,playerID, ntf.source, ntf.apply_player_game_mode, false,refuseMsg)
        end
    
        if Facade.GameFlowManager:CheckIsInFrontEnd() then
            ntf.inviteType = "TeamApply"
            self.Events.evtReceiveTeamInvite:Invoke(ntf,fConfirmFunction,fRefuseFunction)
        elseif Server.AccountServer:IsPlayerInGame() then
            ntf.inviteType = "TeamApply"
            self.Events.evtWhenGamingReceiveTeamInvite:Invoke(ntf,fConfirmFunction,fRefuseFunction)
        end
    end
    -- BEGIN MODIFICATION - VIRTUOS
    if PLATFORM_GEN9 ~= 1 then
        handleTeamApplyJoinNtf()
    else
        if ntf.SpecialChannel ~= 1 and Server.ChatServer.hasCommunicationPrivilege == false then
            -- 如果用户没有平台通信权限,忽视入队申请
            return
        end
        -- 申请者来自其他平台
        if applyerInfo.plat ~= Server.AccountServer:GetPlatIdType() then
            Server.ChatServer:CheckAnonymousUserCommunicationPermissions(playerID, function(isAllowed)
                if isAllowed then
                    handleTeamApplyJoinNtf()
                end
            end)
        else
            -- 申请者来自相同平台
            if ntf.SpecialChannel == 1 then
                -- 来自相同平台玩家发送的平台渠道入队申请
                handleTeamApplyJoinNtf()
                return
            end

            local checkList = {}
            table.insert(checkList, playerID)
            Server.ChatServer:CheckUsersPermissionsByOpenIdList(EPlatformUserPermissionType.CommunicateUsingText, true, checkList, function(allowedList)
                if #allowedList > 0 then
                    handleTeamApplyJoinNtf()
                end
            end)
        end
    end
    -- END MODIFICATION - VIRTUOS
end

--入队申请结果
function TeamServer:OnCSTeamResponseJoinNtf(ntf) --CSTeamResponseJoinNtf
    self._invitePlayerID[ntf.team_id] = nil
    self.Events.evtTeamRecievePlayerRes:Invoke(ntf.team_id)
    if ntf.agree then
        logwarning("TeamServer:OnCSTeamResponseJoinNtf",ntf.result)
        if ntf.result==0 then
            for k,v in pairs(ntf.info.Members)do--比对一下packId和服务器上是否匹配，不匹配则重新上报
                if v.PlayerID==Server.AccountServer:GetPlayerId() then
                    local downloadedQuestIds=Server.LitePackServer:GetAllDownloadedQuestIDs()
                    local isEqual=self:Compare2Table(downloadedQuestIds,v.PackQuestID)
                    if not isEqual then
                        logwarning("TeamServer:OnCSTeamResponseJoinNtf packQuestId not equal!")
                        Server.TeamServer:ReqUpdatePackQuest(ntf.team_id)
                    end

                end
            end
            local function f()
                self:SetReadyState(TeamMemberState.MemUnReady)
            end
            if Server.MatchServer:GetIsMatching() then
                self:SendCancelMatching()
            end
            self:SetTeamInfos(ntf.info, f)
            self.Events.evtJoinTeam:Invoke(ntf.team_id)
        end
    else
        local playerName = ntf.leader.nick_name
        LuaGlobalEvents.evtServerShowTip:Invoke(string.format(ServerTipCode.TeamApplyRefuse, playerName))
    end
end

--比较两个table的值是否一致
function TeamServer:Compare2Table(table1,table2)
    if table1 == nil then
        return table2 == nil
    elseif table2 == nil then
        return table1 == nil
    end
    for k,v in pairs(table1)do
        if not table.contains(table2,v)then
            return false
        end
    end
    for k,v in pairs(table2)do
        if not table.contains(table1,v)then
            return false
        end
    end
    return true
end

--处理入队申请
function TeamServer:Response2ApplyJoinReq(teamId,applyId, source, gameMode, bAgree,msg)
    log("TeamServer:Response2ApplyJoinReq","teamId",teamId,"applyId",applyId,"bAgree",bAgree)
    local req = pb.CSTeamResponseJoinTReq:New()
    req.team_id = teamId
    req.agree = bAgree
    req.refuse_message = msg or ""
    req.apply_player_id = applyId
    req.source = source
    req.apply_player_game_mode = gameMode
    req:Request()
end

--队友未准备
---@field ntf.result TeamUnReadyType
function TeamServer:OnCSTeamUnreadyNtf(ntf)
    logwarning("TeamServer:OnCSTeamUnreadyNtf",ntf.result)
    local fGetUnreadyTips=function(ntf)
        local tip = ""
        for index, teammateId in ipairs(ntf.unready_member_array) do
            if self.teamInfos.Members[teammateId] then
                if index > 1 then
                    tip = tip..self.Loc.SeparateSymbol
                end
                tip = tip..self.teamInfos.Members[teammateId].PlayerName
            end
        end
        if ntf.result==TeamUnReadyType.MemberUnReady then
            tip = string.format(self.Loc.TeammateCantStart, tip)
        elseif ntf.result==TeamUnReadyType.MemberEquipNotEnough then
            tip = string.format(self.Loc.TeammateValueLack, tip)
        elseif ntf.result==TeamUnReadyType.MemberPropNotAllowedIntoDS then
            tip = string.format(self.Loc.TeammateNotAllowedProp, tip)
        elseif ntf.result==TeamUnReadyType.MemberPackQuestMiss then
            tip = self.Loc.TeammatePackQuestMiss
            self.Events.evtMatchGatePackQuestMiss:Invoke()
        end
        return tip
        
    end

    local tip = fGetUnreadyTips(ntf)
    LuaGlobalEvents.evtServerShowTip:Invoke(tip)
    
end

function TeamServer:OnCSRoomMatchStartFailNtf(ntf)
    logwarning("TeamServer:OnCSRoomMatchStartFailNtf")
    self.Events.evtMatchGatePunishFailed:Invoke(ntf)
end

---@param ntf pb_CSTeamMemberBatchChangeNtf
function TeamServer:OnBatchChangeTeammateState(ntf)
    if ntf.TeamID ~= self:GetTeamID() then
        logwarning("[darc]", ntf.LeaderID)
    end
end

function TeamServer:KickMem(targetPlayerID)
    if Server.MatchServer:GetIsMatching() or Server.MatchServer:GetIsWaitForGotoGame() then
        LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.TeamMatchingRefuseLeave)
        return
    end

    local fCSTeamKickTRes=function(res)
        if res.result==Err.TeamPlayerNotExist then--12005
            self:UpdateTeamInfos()
        end
    end

    local req = pb.CSTeamKickTReq:New()
    req.TeamID = self:GetTeamID()
    req.KickPlayerID = targetPlayerID
    req:Request(fCSTeamKickTRes)
end

function TeamServer:UpdateTeamInfos(targetTeamID, fCallback, bShowErrTip)
    logwarning("TeamServer:UpdateTeamInfos Req")
    local req = pb.CSTeamInfoTReq:New()
    req.TeamID = targetTeamID or self:GetTeamID()
    req:Request(function(res)
        if res.result ~= 0 then
            if res.result == -1 then
                return self:UpdateTeamInfos(targetTeamID, fCallback)
            else
                self:ClearTeamInfos(true)
            end
        end
        logwarning("TeamServer:UpdateTeamInfos CSTeamInfoTRes","result",res.result)
        self:SetTeamInfos(res.Info, fCallback)

        -- BEGIN MODIFICATION - VIRTUOS
        if PLATFORM_XSX == 1 then
            local UDFMOnlineSessionManager = import("DFMOnlineSessionManager")
            local DFMOnlineSessionManager = UDFMOnlineSessionManager.Get(GetWorld())
            if DFMOnlineSessionManager:IsInTeamSession() == false and Server.TeamServer:IsInTeam() == true then
                self.Events.evtCreatePlatformTeamSession:Invoke()
            end
        elseif PLATFORM_PS5 == 1 then
            local UDFMOnlineSessionManager = import("DFMOnlineSessionManager")
            local DFMOnlineSessionManager = UDFMOnlineSessionManager.Get(GetWorld())
            if DFMOnlineSessionManager:IsInTeamSession() == false and Server.TeamServer:IsInTeam() == true and DFMOnlineSessionManager:IsTeamSessionIdle() == true then
                local sessionId = res.Info.Session

                local restriction = EPlatformSessionRestriction.Public
                if DFMOnlineSessionManager then
                    if sessionId and sessionId ~= "" then
                        local callback = function(bSuccessful)
                            if not bSuccessful then
                                loginfo("TeamServer:UpdateTeamInfo, DFMOnlineSessionManager:JoinTeamSession: Failed!")                        
                            end
                        end
                        DFMOnlineSessionManager.OnJoinTeamSessionCompleteDelegate:Add(callback)
                        local maxPlayersNum = 0 --useless
                        local customSessionInfo = "" --useless
                        DFMOnlineSessionManager:JoinTeamSession(restriction, maxPlayersNum, sessionId, customSessionInfo)
                    else
                        local callback = function(bSuccessful)
                            if bSuccessful then
                                local newSessionId = self:GetSessionID()
                                self:ReqUpdatePlatformSessionID(res.Info.TeamID,newSessionId)
                            else
                                loginfo("TeamServer:UpdateTeamInfo, DFMOnlineSessionManager:JoinTeamSession: Failed!")                                                        
                            end
                        end
                        self.Events.evtProcessPlatformSession:Invoke(restriction, callback)
                    end                    
                end
            end
        end
        -- END MODIFICATION - VIRTUOS
        
    end, {bShowErrTip = bShowErrTip == nil and bShowErrTip or true})
    if req.TeamID==0 then
        logerror("TeamServer:UpdateTeamInfos teamId is 0",debug.traceback())
    end
end

--BEGIN VIRTUOS MODE @Zhang Yingqi
function TeamServer:ReqUpdatePlatformSessionID(teamId, sessionId)
    loginfo("TeamServer:ReqUpdatePlatformSessionID","teamId",teamId,"sessionId",sessionId)

    local fCSTeamSessionUpdateTRes = function(res)
        if res.result ~= 0 then
            loginfo("TeamServer:ReqUpdatePlatformSessionID failed","result",res.result,"sessionId",sessionId)
        else
            loginfo("TeamServer:ReqUpdatePlatformSessionID success","teamId",teamId,"sessionId",sessionId)
        end
    end
    
    local req = pb.CSTeamSessionUpdateTReq:New()
    req.team_id = teamId
    req.session = sessionId
    req:Request(fCSTeamSessionUpdateTRes, {bEnableHighFrequency = true})
end
--END VIRTUOS MODE

function TeamServer:ReqUpdatePackQuest(teamId,updateType,packIds)
    loginfo("TeamServer:ReqUpdatePackQuest","teamId",teamId,"updateType",updateType)
    if packIds then logtable(packIds,true) end
    local req=pb.CSTeamUpdatePackQuestTReq:New()
    req.team_id=teamId or Server.TeamServer:GetTeamID()
    req.PackQuestID=packIds or Server.LitePackServer:GetAllDownloadedQuestIDs()
    req.update_pack_quest_type=updateType or TeamUpdatePackQuestType.TeamUpdatePackQuestJoinTeam
    logwarning("TeamServer:ReqUpdatePackQuest 上报的QuestID个数",#req.PackQuestID)
    req:Request(nil,{bEnableHighFrequency = true})
end

function TeamServer:CancelTeamMatching()
    loginfo("TeamServer:CancelTeamMatching")
    local req = pb.CSTeamCancelMatchTReq:New()
    req.TeamID = self:GetTeamID()
    req:Request(function(res)
        if res.result ~= 0 then
            return
        end
        Server.MatchServer:EndMatching()
        -- self:_SetIsMatching(false)
    end)
end

function TeamServer:CancelPersonMatching()
    loginfo("TeamServer:CancelPersonMatching")
    local req = pb.CSRoomMatchQuitAllocReq:New()
    req.is_timeout = false
    req:Request(function(res)
        if res.result ~= 0 then
            return
        end
        Server.MatchServer:EndMatching()
        -- self:_SetIsMatching(false)
    end)
end

function TeamServer:SendPersonMatching()
end

-- 单人匹配请求
---@param spawnPoint integer 大世界据点
---@param mode_infos pb_MatchModeInfo[] 地图列表 用于适配大战场地图多选
---@param groupId integer 大战场地图分组
function TeamServer:SendPersonMatchingWithParams(mode_infos, isAddMember, spawnPoint, groupId)
    logwarning("TeamServer:SendPersonMatchingWithParams")
    if self._isSendingPersonMatching or self._isSendingCancelMatching then
        logerror("TeamServer:SendPersonMatchingWithParams, please wait req response!!!","isSendingPersonMatching",self._isSendingPersonMatching,"isSendingCancelMatching",self._isSendingCancelMatching)
        return
    end
    self.Events.evtSendSecondLanguage:Invoke()

    self._isSendingPersonMatching=true
    self._isSendingPersonMatchingDelayHandle=Timer.DelayCall(3,function()
        self._isSendingPersonMatching=nil 
    end)

    if mode_infos == nil or #mode_infos == 0 then
    log("传入的地图列表不应为空")
        return
    end
    logtable(mode_infos,true)


    local whiteListErrorShow={
        Err.TeamEquipNotEnough,
        Err.DepositPropNotAllowedIntoDS,
        Err.TeamMemberNotReady,
        Err.TeamPackQuestMiss,

    }
    local modeInfo=mode_infos[1]
    local addMemberType=modeInfo.add_member_type
    if addMemberType==MapBoardAddMemberType.MustAddMember then
        isAddMember=true
    elseif addMemberType==MapBoardAddMemberType.MustNotAddMember then
        isAddMember=false
    end

    local req = pb.CSRoomMatchStartAllocReq:New()
    req.mode_infos = mode_infos
    req.is_add_member = isAddMember
    req.group_id = groupId
    req.is_ranked_match = self._bEnableRankMatch
    loginfo("TeamServer:SendPersonMatchingWithParams EnableRankMatch:"..tostring(self._bEnableRankMatch))
    req.pack_quest_id = Server.LitePackServer:GetAllDownloadedQuestIDs()

    req:Request(
        ---@param res pb_CSRoomMatchStartAllocRes
        function(res)
            Timer.CancelDelay(self._isSendingPersonMatchingDelayHandle)
            self._isSendingPersonMatching=nil
            if res.result == 0 then
                logwarning("TeamServer:SendPersonMatchingWithParams_Success")
                Server.MatchServer:SetMatchTimeInfo(res)
                Server.MatchServer:StartMatching()
                local myInfo = self:GetMyInfo()
                self.Events.evtReadyStateChanged:Invoke(myInfo.PlayerID)
                DFMGlobalEvents.evtReportEvent:Invoke(EReportCategoryName.Match, EMatchFlow.MatchRes,  EReportStatusCode.Success,  res.result, "")
            else
                logerror("TeamServer:SendPersonMatchingWithParams_Failed","result",res.result)
                if not res.result then
                    LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.NetInvalidOutTime, 3)
                elseif res.result == -1 then
                    LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.ReqTimeOut..res.result, 3)
                elseif res.result == Err.StateInMatch then--22001
                    self:ForceTriggerReconnect()
                elseif res.result == Err.MatchGateCheckEquipPriceFailed then
                    self:ForceTriggerReconnect()
                elseif res.result == Err.MatchGatePunishFailed and res.ntf then
                    logwarning("TeamServer:SendPersonMatchingWithParams MatchGatePunishFailed", res.result)
                    self.Events.evtMatchGatePunishFailed:Invoke(res.ntf)
                elseif res.result==Err.MatchGateRankedMatchNotUnlock then
                    self.Events.evtMatchFailedTip:Invoke(EMatchFailedReason.RankLevelLimit)
                elseif res.result==Err.MatchGateAddMemberNotMeetCondition then
                    self.Events.evtMatchFailedTip:Invoke(EMatchFailedReason.CantAddMember)
                elseif res.result==Err.MatchGateEquip15CollectionUpLimit then
                    self.Events.evtMatchFailedTip:Invoke(EMatchFailedReason.CantTakeHeavyValueProps)
                elseif res.result==Err.MatchGateRankedMatchStopped then
                    self.Events.evtMatchFailedTip:Invoke(EMatchFailedReason.SeasonStopped)
                elseif res.result==Err.MatchGateCheckTickPriceFailed then
                    self.Events.evtMatchFailedTip:Invoke(EMatchFailedReason.MoneyNotEnough)
                elseif res.result==Err.DepositEntryPropLevelLimit then
                    self.Events.evtMatchFailedTip:Invoke(EMatchFailedReason.EquipLevelLimit)
                elseif res.result==Err.DepositEntryPropNightVisionLimit or res.result==Err.DepositEntryPropThermalImagingLimit then
                    self.Events.evtMatchFailedTip:Invoke(EMatchFailedReason.TakeNotAllowNightEquip)
                elseif res.result == Err.MatchGateForbidRankByReputation then
                    self:TriggerReputationErrorTipsIfNeed(res.result, {}, true)
                elseif res.result == Err.MatchGateForbidTeamByReputation then
                    self:TriggerReputationErrorTipsIfNeed(res.result, {}, true)
                elseif res.result == Err.MatchGatePackQuestMiss then
                    self.Events.evtMatchGatePackQuestMiss:Invoke()
                elseif res.result == Err.MatchGateFaceVerifyRequired then
                    self.Events.evtFaceCheck:Invoke(res.face_verify_url)
                elseif res.result == Err.MatchRepeatedAlloc then
                    --重复匹配，说明已经成功进入匹配
                    self.Events.evtAlreadyEnterMatch:Invoke()
                end
                -- 发起匹配请求失败，认为匹配失败
                DFMGlobalEvents.evtReportEvent:Invoke(EReportCategoryName.Match, EMatchFlow.MatchRes,  EReportStatusCode.Failed,  res.result, "")
            end
           
        end
        , {bBuildEmptyRecv = true, iDefaultSendNum = 1, bNeedResendAfterReconnected = false,bShowErrTip = true,whiteListErrorShow=whiteListErrorShow,bEnableHighFrequency=true})
        -- 开始匹配
        DFMGlobalEvents.evtReportEvent:Invoke(EReportCategoryName.Match, EMatchFlow.Start,  EReportStatusCode.Success,  0, "")
end

-- 是否强制开始匹配，传入false是会先由服务器做一次判断，看是否可以开始匹配，在不清楚用途的情况下默认传false
function TeamServer:SendTeamMatching(bIsForceStartMatch)
    logwarning("TeamServer:SendTeamMatching")
    -- 非强制发送下才需要判断频率
    if (self._isSendingTeamMatching or self._isSendingCancelMatching)and (not bIsForceStartMatch) then
        logerror("TeamServer:SendTeamMatching, please wait req response!!!","isSendingTeamMatching",self._isSendingTeamMatching,"isSendingCancelMatching",self._isSendingCancelMatching)
        return
    end
    self.Events.evtSendSecondLanguage:Invoke()
    
    self._isSendingTeamMatching=true
    self._isSendingTeamMatchingDelayHandle=Timer.DelayCall(3,function()
        self._isSendingTeamMatching=nil 
    end)

    if not self:IsCaptial() then
        logerror("TeamServer:SendTeamMatching, not captial!!!")
        local fOnGetStateInfo=CreateCallBack(function(self,res)
            if res.result==0 then
                if not Server.AccountServer:IsInTeam() then
                    self:ClearTeamInfos()
                end
            end
        end,self)
        Server.AccountServer:GetStateInfo(fOnGetStateInfo)
        return
    end

    bIsForceStartMatch = bIsForceStartMatch or false

    local whiteListErrorShow={
        Err.TeamEquipNotEnough,
        Err.DepositPropNotAllowedIntoDS,
        Err.TeamMemberNotReady,
        Err.TeamPackQuestMiss,

    }
    local matchMode = GameModeSvr:GetMatchMode()
    local worldEntCfg = GameModeSvr:GetWorldEntranceCfgByGameMode()

    local isAddMember=self:GetAutoMatchTeamMates()
    local modeInfo=matchMode
    local addMemberType=modeInfo.add_member_type
    if addMemberType==MapBoardAddMemberType.MustAddMember then
        isAddMember=true
    elseif addMemberType==MapBoardAddMemberType.MustNotAddMember then
        isAddMember=false
    end

        -- BEGIN MODIFICATION - VIRTUOS
    local handleSendTeamMatching = function()
    -- END MODIFICATION - VIRTUOS
        local req = pb.CSTeamStartMatchTReq:New()
        req.TeamID = self:GetTeamID()
        req.IsAddMember = isAddMember
        req.Raid_ID = matchMode.raid_id
        req.rule = matchMode.game_rule
        req.spawn_point = worldEntCfg and worldEntCfg.StartSpotGroup or 0
        req.IsRankedMatch = self._bEnableRankMatch
        req.IsForceStartMatch = bIsForceStartMatch
        loginfo("TeamServer:SendTeamMatching EnableRankMatch:" .. tostring(self._bEnableRankMatch))
        req:Request(function(res)
            Timer.CancelDelay(self._isSendingTeamMatchingDelayHandle)
            self._isSendingTeamMatching = nil
            if res.result == 0 then
                logwarning("TeamServer:SendTeamMatching Success")
                Server.MatchServer:SetMatchTimeInfo(res)
                Server.MatchServer:StartMatching()
                local myInfo = self:GetMyInfo()
                self.Events.evtReadyStateChanged:Invoke(myInfo.PlayerID)
                return
            else
                logerror("TeamServer:SendTeamMatching Failed", res.result)
                if not res.result then
                    LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.NetInvalidOutTime, 3)
                elseif res.result == -1 then
                    LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.ReqTimeOut .. res.result, 3)
                elseif res.result == Err.StateInMatch then --22001
                    self:ForceTriggerReconnect()
                elseif res.result == Err.MatchGateCheckEquipPriceFailed then
                    self:ForceTriggerReconnect()
                elseif res.result == Err.TeamStateInvalid then
                    self:ForceTriggerReconnect()
                elseif res.result == Err.MatchGateRankedMatchNotUnlock then
                    self.Events.evtMatchFailedTip:Invoke(EMatchFailedReason.RankLevelLimit, res.CheckFailedPlayerIds)
                elseif res.result == Err.MatchGateAddMemberNotMeetCondition then
                    self.Events.evtMatchFailedTip:Invoke(EMatchFailedReason.CantAddMember, res.CheckFailedPlayerIds)
                elseif res.result == Err.MatchGateEquip15CollectionUpLimit then
                    self.Events.evtMatchFailedTip:Invoke(EMatchFailedReason.CantTakeHeavyValueProps, res.CheckFailedPlayerIds)
                elseif res.result == Err.MatchGateCheckPlayerAreaNotEqual then
                    -- 玩家来自不同区域，需要弹窗提示后重新开始匹配
                    self.Events.evtMatchFailedTip:Invoke(EMatchFailedReason.PlayerAreaLimit, res.CheckFailedPlayerIds)
                elseif res.result == Err.MatchGateRankedMatchStopped then
                    self.Events.evtMatchFailedTip:Invoke(EMatchFailedReason.SeasonStopped)
                elseif res.result == Err.MatchGateCheckTickPriceFailed then
                    self.Events.evtMatchFailedTip:Invoke(EMatchFailedReason.MoneyNotEnough, res.CheckFailedPlayerIds)
                elseif res.result == Err.DepositEntryPropLevelLimit then
                    self.Events.evtMatchFailedTip:Invoke(EMatchFailedReason.EquipLevelLimit, res.CheckFailedPlayerIds)
 		        elseif res.result==Err.DepositEntryPropNightVisionLimit or res.result==Err.DepositEntryPropThermalImagingLimit then
                    self.Events.evtMatchFailedTip:Invoke(EMatchFailedReason.TakeNotAllowNightEquip,res.CheckFailedPlayerIds)
                elseif res.result == Err.MatchGateForbidRankByReputation then
                    self:TriggerReputationErrorTipsIfNeed(res.result, res.CheckReputationFailedPlayerIds)
                elseif res.result == Err.MatchGateForbidTeamByReputation then
                    self:TriggerReputationErrorTipsIfNeed(res.result, res.CheckReputationFailedPlayerIds)
 		        elseif res.result == Err.MatchGatePackQuestMiss then
                    self.Events.evtMatchGatePackQuestMiss:Invoke()
                elseif res.result == Err.MatchGateFaceVerifyRequired then
                    self.Events.evtFaceCheck:Invoke(res.face_verify_url)
                elseif res.result == Err.TeamNeedFaceVerify then
                    self.Events.evtFaceCheck:Invoke(res.face_verify_url)
                elseif res.result == Err.MatchRepeatedAlloc then
                    --重复匹配，说明已经成功进入匹配
                    self.Events.evtAlreadyEnterMatch:Invoke()
                end
                if res.result == Err.TeamPlayerNotExist or res.result == Err.TeamNotExist then
                    -- 当错误码是玩家不在队伍中或队伍不存在，清空队伍信息并重新创建
                    self:ClearTeamInfos()
                    if not self:IsInTeam() then
                        self:CreateTeam()
                    end
                end

                if res.result == Err.MatchGatePunishFailed and res.ntf then
                    logwarning("TeamServer:SendTeamMatching MatchGatePunishFailed", res.result)
                    self.Events.evtMatchGatePunishFailed:Invoke(res.ntf)
                end
                -- 发起组队请求失败
                DFMGlobalEvents.evtReportEvent:Invoke(EReportCategoryName.Match, EMatchFlow.MatchRes, EReportStatusCode.Failed, res.result, "")
            end
        end, { bBuildEmptyRecv = true, iDefaultSendNum = 1, bNeedResendAfterReconnected = false, maxWaitTime = 3, bShowLoading = true, bShowErrTip = true, whiteListErrorShow = whiteListErrorShow, bEnableHighFrequency = true })
        DFMGlobalEvents.evtReportEvent:Invoke(EReportCategoryName.Match, EMatchFlow.Start, EReportStatusCode.Success, 0, "")
    end
    -- BEGIN MODIFICATION - VIRTUOS
    if IsHD() then
        self.Events.evtCanSendTeamMatchWitchCurrentTeamMembers:Invoke(function(res)
            if res then
                handleSendTeamMatching()
            end
        end)
    else
        handleSendTeamMatching()
    end
    -- END MODIFICATION - VIRTUOS
end

function TeamServer:IsSendingMatching()
    loginfo("TeamServer:IsSendingMatching")
    return self._isSendingPersonMatching or self._isSendingTeamMatching
end

function TeamServer:TriggerReputationErrorTipsIfNeed(resResult, resFailedPlayerIds, isSingle)
    isSingle = setdefault(isSingle, false)

    local targetLoc = ServerTipCode.CanNotParticipateRank
    if resResult == Err.MatchGateForbidTeamByReputation then
        targetLoc = ServerTipCode.CanNotParticipateTeamGame
    end
    local targetDisplayText = ""

    if isSingle then
        targetDisplayText = StringUtil.Key2StrFormat(
            targetLoc,
            {
                ["firstName"] = "",
                ["secondName"] = "",
                ["thirdName"] = "",
                ["suffixSpace"] = "",
            }
        )

        LuaGlobalEvents.evtServerShowTip:Invoke(targetDisplayText)
        return
    end


    logerror("TriggerReputationErrorTipsIfNeed", resResult)
    local playerNum = #resFailedPlayerIds
    if playerNum <= 0 then
        return
    end

    local firstPlayerName = ""
    local secondPlayerName = ""
    local thirdPlayerName = ""


    if playerNum == 1 then
        local playerId = resFailedPlayerIds[1]
        local detailName = self.teamInfos.Members[playerId].PlayerName or ""
        firstPlayerName = detailName or ""
    else
        -- 这里写法是先拿到所有人名字 然后基于下一个人名字是否存在补一个顿号分割
        firstPlayerName = self.teamInfos.Members[resFailedPlayerIds[1]].PlayerName or ""
        secondPlayerName = self.teamInfos.Members[resFailedPlayerIds[2]].PlayerName or ""
        thirdPlayerName = self.teamInfos.Members[resFailedPlayerIds[3]].PlayerName or ""
        if firstPlayerName and secondPlayerName then
            firstPlayerName = firstPlayerName.."、"
        end

        if secondPlayerName and thirdPlayerName then
            secondPlayerName = secondPlayerName.."、"
        end
    end

    targetDisplayText = StringUtil.Key2StrFormat(
        targetLoc,
        {
            ["firstName"] = firstPlayerName,
            ["secondName"] = secondPlayerName,
            ["thirdName"] = thirdPlayerName,
            ["suffixSpace"] = "  ",
        }
    )

    LuaGlobalEvents.evtServerShowTip:Invoke(targetDisplayText)
end

function TeamServer:SendMatchReady(bIsReady,fCallback)
    if (not self:IsInTeam()) or self:IsCaptial() then
        return
    end

    local req = pb.CSTeamSetReadyTReq:New()
    req.TeamID = self:GetTeamID()
    req.Ready = bIsReady
    ---@param res pb_CSTeamSetReadyTRes
    req:Request(function(res)
        if res.result == 0 then
            if fCallback then
                fCallback()
            end
            --self:SetState(self:GetMyID(), bIsReady and TeamMemberState.MemReady or TeamMemberState.MemUnReady)
        elseif res.result == Err.TeamNeedFaceVerify then
            self.Events.evtFaceCheck:Invoke(res.face_verify_url)
        end
    end, {bEnableHighFrequency = true})
end

function TeamServer:SendCancelMatching(fCallback)
    loginfo("TeamServer:SendCancelMatching")
    if self._isSendingPersonMatching or self._isSendingTeamMatching or self._isSendingCancelMatching then
        logerror("TeamServer:SendCancelMatching, please wait req response!!!","isSendingPersonMatching",self._isSendingPersonMatching,"isSendingTeamMatching",self._isSendingTeamMatching,"isSendingCancelMatching",self._isSendingCancelMatching)
        return
    end
    self._isSendingCancelMatching=true
    self._isSendingCancelMatchingDelayHandle=Timer.DelayCall(2,function()
        self._isSendingCancelMatching=nil
    end)
    local function fCancalMatching(res)
        logwarning("TeamServer:SendCancelMatching CSRoomMatchQuitAllocRes","result",res.result)
        Timer.CancelDelay(self._isSendingCancelMatchingDelayHandle)
        self._isSendingCancelMatching=nil
        if res.result == 0 then
            -- 取消匹配
            DFMGlobalEvents.evtReportEvent:Invoke(EReportCategoryName.Match, EMatchFlow.MatchRes,  EReportStatusCode.Failed,  -1, "")
            Server.MatchServer:EndMatching()
            if fCallback then fCallback(res.result) end
            -- self.Events.evtEndMatchReturnSOLMoney:Invoke()
        --elseif res.result == -1 then
            --LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.RequestTimeOut, 3)
        elseif res.result == Err.MatchCannotFindPlayer then
            Server.MatchServer:EndMatching()
        elseif res.result == Err.MatchCannotFindTeam then
            Server.MatchServer:EndMatching()
        elseif res.result == Err.StateNotMatching then
            Server.MatchServer:EndMatching()
        elseif res.result == Err.StateInMatch then
            
            self:ForceTriggerReconnect()
            Server.MatchServer:EndMatching()
        end
        
    end

    if not self:IsInTeam() then
        -- 发送个人取消匹配
        local req = pb.CSRoomMatchQuitAllocReq:New()
        req.is_timeout = false
        req.match_module = 2
        req:Request(fCancalMatching,{bBuildEmptyRecv = true,iDefaultSendNum = 1, bNeedResendAfterReconnected = false,bShowErrTip = true,bEnableHighFrequency=true})
    elseif self:IsCaptial() then
        -- 发送取消组队开赛
        local req = pb.CSTeamCancelMatchTReq:New()
        req.TeamID = self:GetTeamId()
        req:Request(fCancalMatching,{bBuildEmptyRecv = true,iDefaultSendNum = 1, bNeedResendAfterReconnected = false,bShowErrTip = true,bEnableHighFrequency=true})
    end
end

function TeamServer:_CheckedNotify(dataChange)
    if not self:IsInTeam() then
        return
    end

    local bNeedsNotify = false
    local function NeedsNotify(SlotType)
        return (ESlotType.MainWeaponLeft <= SlotType and SlotType <= ESlotType.MeleeWeapon) or
            (ESlotType.EquipmentStart < SlotType and SlotType < ESlotType.EquipmentEnd)
    end
    for _, Prop in pairs(dataChange.prop_changes) do
        if NeedsNotify(Prop.src.pos) or NeedsNotify(Prop.dest.pos) then
            bNeedsNotify = true
            break
        end
    end

    if bNeedsNotify then
        self:SendChangeEquipNtf()
    end
end

function TeamServer:SendChangeEquipNtf()
    if self:IsInTeam() then
        local ntf = pb.CSTeamEquipPositionChangeTNtf:New()
        ntf.TeamID = self:GetTeamID()
        ntf:SendNtf({bEnableHighFrequency = true})
        if ntf.TeamID==0 then
            logerror("TeamServer:SendChangeEquipNtf teamId is 0",debug.traceback())
        end
    end
end

function TeamServer:SendTeamNotifyPrepareTReq(playerID)
    local req = pb.CSTeamNotifyPrepareTReq:New()
    req.TeamID = self:GetTeamID()
    req.MemberID = playerID
    req:Request()
end

function TeamServer:SendTeamNotifyPrepareTReqToAll()
    for playerID, _ in pairs(self.teamInfos.Members) do
        if playerID ~= self:GetMyID() then
            self:SendTeamNotifyPrepareTReq(playerID)
        end
    end
end

function TeamServer:SendTeamPrepare_Refuse(callback)
    local req = pb.CSTeamPrepareTReq:New()
    req.TeamID = self:GetTeamID()
    req.Prepare = true
    req.Refuse = true
    req:Request(callback)
end

function TeamServer:SendTeamPrepare_Prepare(bIsPrepare, callback)
    if Server.MatchServer:GetIsMatching() then
        return
    end
    local req = pb.CSTeamPrepareTReq:New()
    req.TeamID = self:GetTeamID()
    req.Prepare = bIsPrepare
    req.Refuse = false
    req:Request(callback, {
        bShowErrTip = false
    })
    --队员离开匹配界面，需要取消准备状态
     if bIsPrepare == false and self:IsInTeam() and not self:IsCaptial() then
        self:SendMatchReady(false)
    end
end

--------------------------------------------------------------------------
--- 服务器回包
--------------------------------------------------------------------------
function TeamServer:OnCSMatchUnreadyNtf(_)
end

function TeamServer:OnCSMatchCheckReadyNtf(ntf)
end

function TeamServer:OnCSTeamToInviteeNtf(ntf)
    log("TeamServer:OnCSTeamToInviteeNtf")
     local inviterID = ntf.InviterID
    local teamID = ntf.TeamID
    local inviterName = ntf.InviterNick
    -- BEGIN MODIFICATION - VIRTUOS
    local function handleTeamToInviteeNtf()
        self._inviterPlayerInfo[inviterID] = {playerId = ntf.InviterID, playerSource = ntf.source, roomId = ntf.RoomID }

        local fConfirmFunction = function(_teamID,_inviterID,source)
            self:Response2InviteReq(_teamID,_inviterID,true,"",source)
            -- 经分上报
            LogAnalysisTool.DoSendTeamInviteAndApplyJoinResLog(source)
        end

        local fRefuseFunction = function(_teamID,_inviterID,refuseMsg,source)
            self:Response2InviteReq(_teamID,_inviterID,false,refuseMsg,source)
            -- 经分上报
            LogAnalysisTool.DoSendTeamInviteAndApplyJoinResLog(source)
        end

        if inviterID == Server.AccountServer:GetPlayerId() or teamID == self:GetTeamID() then
            return
        end

        log("TeamServer:OnCSTeamToInviteeNtf TeamServer bFrontEnd", Facade.GameFlowManager:CheckIsInFrontEnd())
        if Server.AccountServer:IsPlayerInGame() then
            ntf.inviteType = "TeamInvite"
            self.Events.evtWhenGamingReceiveTeamInvite:Invoke(ntf,fConfirmFunction,fRefuseFunction)
        elseif not Facade.GameFlowManager:CheckIsInFrontEnd() then
            self.Events.evtReceiveTeamInvite:Invoke(ntf)
        else
            ntf.inviteType = "TeamInvite"
            self.Events.evtReceiveTeamInvite:Invoke(ntf,fConfirmFunction,fRefuseFunction)
        end
    end
    -- END MODIFICATION - VIRTUOS
    
    -- BEGIN MODIFICATION - VIRTUOS
    if not IsConsole() then
        handleTeamToInviteeNtf()
    else
        local inviterInfo = ntf.Inviter
        if inviterInfo.plat ~= Server.AccountServer:GetPlatIdType() then
            -- 邀请来自其他平台
            Server.ChatServer:CheckAnonymousUserCommunicationPermissions(inviterID, function(res)
                if res then
                    handleTeamToInviteeNtf()
                end
            end)
        else
            -- 邀请来自相同平台
            if IsXSX() then
                local identityManager = UDFMOnlineIdentityManager.Get(GetGameInstance())
                local proxy = UDFMOnlineIdentityProxy_CheckUserPrivilege.CreateInstance(identityManager)
                if proxy then
                    proxy.OnGetUserPrivilegeCompleteDelegate:Add(function(res)
                        if res == false then
                            -- 如果用户没有网络游戏权限，忽视邀请
                            return
                        end
                        if Server.ChatServer.hasCommunicationPrivilege == false then
                            -- 如果用户没有平台通信权限,忽视邀请
                            return
                        end
                        local checkList = {}
                        table.insert(checkList, inviterID)
                        Server.ChatServer:CheckUsersPermissionsByOpenIdList(EPlatformUserPermissionType.CommunicateUsingText,
                            true, checkList, function(allowedList)
                            if #allowedList > 0 then
                                handleTeamToInviteeNtf()
                            end
                        end)
                    end)
                    proxy:CheckUserPrivilege(EPlatformUserPrivileges.CanPlayOnline, false)
                end
            else -- PS5
                handleTeamToInviteeNtf()
            end
        end
    end
    -- END MODIFICATION - VIRTUOS
end

function TeamServer:Response2InviteReq(teamId,inviterId,bAgree,msg,source)
    loginfo("TeamServer:Response2InviteReq",teamId,inviterId,bAgree,msg,source)
    local function f()
        if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Lobby then
            -- 以下操作 = 大厅+组队界面
            -- local cacheInfo1 = UIUtil.CreateCacheInfo(UIName2ID.HallView)
            -- local cacheInfo2 = UIUtil.CreateCacheInfo(UIName2ID.PreparationView)
            -- local UICacheList = {
            --     cacheInfo1,
            --     cacheInfo2
            -- }
            -- UIManager:BatchShowStackUI(UICacheList)
        end
    end

    local OnCSTeamResponseInviteTRes = function(res)
        loginfo("TeamServer:Response2InviteReq, OnCSTeamResponseInviteTRes", res.result)
        local result = res.result
        if bAgree and result == 0 then

            local gameFlowTable = {
                EGameFlowStageType.SafeHouse,
                EGameFlowStageType.Lobby,
                EGameFlowStageType.LobbyBHD,
                EGameFlowStageType.ModeHall,

            }
            local curGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
            if not table.contains(gameFlowTable,curGameFlow) then
                return
            end
             --房间相关处理
             if Server.AccountServer:IsInRoom() then
                -- 如果已经通过同意请求退出房间数据 -- 那么直接清空房间数据 -- 关闭房间
                Server.RoomServer.Events.evtLeaveRoom:Invoke(Server.RoomServer:GetRoomId())
            end
            --如果在队伍中，则需要退队
            if self:IsInTeam() then
                local teamId = self:GetTeamID()
                self:ClearTeamInfos()
                self.Events.evtYouLeaveTeam:Invoke(self:GetTeamID(), teamId,true)
            end
            --如果正在匹配，则取消匹配
            if Server.MatchServer:GetIsMatching() then
                self:SendCancelMatching()
            end
            --入队
            self:SetTeamInfos(res.Info, f)
            self.Events.evtJoinTeam:Invoke(self:GetTeamID())
        elseif result==Err.TeamAlreadyInTeam then--12010
            self:UpdateTeamInfos(teamId)
        --BEGIN VIRTUOS MODE
        else
            if PLATFORM_PS5 == 1 then
                self.Events.evtDestroyPlatformTeamSession:Invoke()
            end
        --END VIRTUOS MODE
        end
    end

    local req = pb.CSTeamResponseInviteTReq:New()
    req.TeamID = teamId
    req.InviterID = inviterId
    req.IsAgree = bAgree
    req.RefuseMessage = msg or ""
    req.source = source or 0
    req.PackQuestID = Server.LitePackServer:GetAllDownloadedQuestIDs()
    req.InviteeGameMode = Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Lobby and 2 or 1
    if self._inviterPlayerInfo[inviterId] then
        req.RoomID = self._inviterPlayerInfo[inviterId].roomId
        self._inviterPlayerInfo[inviterId] = nil
    end 
    logwarning("TeamServer:Response2InviteReq 上报的QuestID个数",#req.PackQuestID)
    
    req:Request(OnCSTeamResponseInviteTRes,{bNeedResendAfterReconnected = true})
end

function TeamServer:OnCSTeamPrepareNtf(ntf)
    local teamMates = self.teamInfos.Members
    for _, memberInfo in pairs(teamMates) do
        if not self:IsCaptial(memberInfo.PlayerID) then
            --如果不在备战状态，则需要添加等待准备状态
            if not self:HasState(memberInfo.State, TeamMemberState.MemPrepare) then
                self:AddWaitPrepareState(memberInfo.PlayerID)
                self.Events.evtTeammateStateChanged:Invoke(memberInfo.PlayerID)
            end
        end
    end
    if not self:IsCaptial() and ntf.Prepare then
        self.Events.evtReceiveTeamPrepare:Invoke()
    end
end

function TeamServer:OnCSTeamPrepareEventNtf(ntf)
    if ntf.event == TeamEvent.TeamNotifyPrepare then
        local memberInfo = self:FindMember(ntf.MemberID)
        if memberInfo then
            if not self:HasState(memberInfo.State, TeamMemberState.MemPrepare) then
                self:AddWaitPrepareState(memberInfo.PlayerID)
                self.Events.evtTeammateStateChanged:Invoke(memberInfo.PlayerID)
            end
            --如果是自己收到这个通知，需要弹窗（要不要整合到evtTeammateStateChanged，待定）
            if self:GetMyID() == ntf.MemberID then
                self.Events.evtReceiveTeamPrepare:Invoke(self:IsCaptial(memberInfo.PlayerID) and memberInfo.PlayerName or nil)
            end
        end
    elseif ntf.event == TeamEvent.TeamRefusePrepare then
        local memberInfo = self:FindMember(ntf.MemberID)
        if memberInfo then
            self:RemoveWaitPrepareState(ntf.MemberID)
            self.Events.evtTeammateStateChanged:Invoke(ntf.MemberID)
            if self:GetMyID() ~= ntf.MemberID then
                LuaGlobalEvents.evtServerShowTip:Invoke(memberInfo.PlayerName .. ServerTipCode.RefuseJoinPrepare)
            end
        end
    end
end

function TeamServer:AddTeammateState(memberID, teamstate)
    local memberInfo = self:FindMember(memberID)
    if memberInfo then
        memberInfo.State = memberInfo.State | teamstate
        self.Events.evtTeammateStateChanged:Invoke(memberInfo.PlayerID)
    end
end

function TeamServer:OnCSTeamResponseInviteNtf(ntf) -- to Inviting
    log("TeamServer:OnCSTeamResponseInviteNtf")
    local inviteID = ntf.Invitee.player_id
    if isvalid(self._delayTimerHandle) then
        if isvalid(self._delayTimerHandle[inviteID]) then
            self._delayTimerHandle[inviteID]:Stop()
            self._delayTimerHandle[inviteID]:Release()
            self._delayTimerHandle[inviteID] = nil
        end
    end
    if self._invitePlayerID[inviteID] then
        self._invitePlayerID[inviteID] = nil
        self.Events.evtTeamRecievePlayerRes:Invoke(inviteID)
    end
    local playerInfo = self._inviteePlayerInfo[inviteID]
    if playerInfo then
        if ntf.IsAgree then
            LogAnalysisTool.DoSendLobbyTeamJoinLog(Server.AccountServer:GetPlayerId(), playerInfo.playerSource, playerInfo.roomId)
        end
        self._inviteePlayerInfo[inviteID] = nil
    end
    if ntf.IsAgree then
        return
    end

    local playerName = ntf.InviteeNick
    LuaGlobalEvents.evtServerShowTip:Invoke(string.format(ServerTipCode.TeamRefuse, playerName))
end

function TeamServer:OnCSTeamBaseInfoChangeNtf(ntf)
    local changeType = ntf.Type
    if self.baseInfoChangedHandlers[changeType] then
        self.baseInfoChangedHandlers[changeType](self, ntf)
    end
end

function TeamServer:OnCSTeamMemberChangeNtf(ntf)
    local typeString=changeTypetoString(ntf.Type)
    logwarning("TeamServer:OnCSTeamMemberChangeNtf","type",typeString,"Player",ntf.Member.PlayerID)
    
    local changeType = ntf.Type
    if self.memberChangedHandlers[changeType] then
        self.memberChangedHandlers[changeType](self, ntf)
    end
end

---@param ntf pb_CSTeamMemberBatchChangeNtf
function TeamServer:OnCSTeamMemberBatchChangeNtf(ntf)
    local typeString=changeTypetoString(ntf.Type)
    logwarning("TeamServer:OnCSTeamMemberBatchChangeNtf","type",typeString)
    -- 确认一下ntf是否还有效
    if ntf.TeamID == self:GetTeamID() then
        local newMemberInfos = ntf.MemberInfos
        local changeType = ntf.Type
        logwarning("[darc]", "TeamServer:OnCSTeamMemberBatchChangeNtf", changeType)
        if self.memberChangedHandlers[changeType] then
            local fHandler = self.memberChangedHandlers[changeType]
            for _, member in ipairs(newMemberInfos) do
                local customNtf = {
                    Member = member,
                    LeaderID = ntf.LeaderID,
                    TeamID = ntf.TeamID,
                    Type = ntf.Type,
                }
                fHandler(self, customNtf)
            end
        end
    end
end

function TeamServer:OnCSMatchWaitDSNtf(_)
    --LuaGlobalEvents.evtServerShowTip:Invoke("匹配成功， 等待入局")
    --1UDFMIrisEnterSeamlessGameplayHelper.RealMatchSucceed(GetWorld(),999,"QStage_999_1",3)
end

function TeamServer:OnCSTeamApplyLeaderNtf()--没有申请队长功能
    self.Events.evtCaptialChanged:Invoke()
end

function TeamServer:OnCSTeamApplyLeaderResponseNtf(ntf)--没有申请队长功能
    local agreeYouToBeCaptial = ntf.IsAgree
    if agreeYouToBeCaptial then
        self:SetCapitalID(Server.AccountServer:GetPlayerId())
        self.Events.evtCaptialChanged:Invoke()
    end
end

function TeamServer:OnCSRoomMatchTimeoutNtf(ntf)
    logwarning("TeamServer:CSRoomMatchTimeoutNtf",ntf.result)
    Facade.ProtoManager:ManuelHandleErrCode(ntf)
    Server.MatchServer:EndMatching()
    self.Events.evtEndMatchReturnSOLMoney:Invoke()
    -- self:_SetIsMatching(false)
    DFMGlobalEvents.evtReportEvent:Invoke(EReportCategoryName.Match, EMatchFlow.MatchIng,  EReportStatusCode.Failed,  ntf.result, "")
end

function TeamServer:OnCSDepositChangeNtf(ntf)
    self:_CheckedNotify(ntf.deposit_change)
end

--监听哈夫币和sku门票返还
function TeamServer:_OnCSDepositChangeNtf(ntf)
    if ntf.deposit_change.reason ==ePropChangeReason.DepositCancelMatchReturnFee then
        if #ntf.deposit_change.currency_changes >0 then
            --返还哈夫币
            self.Events.evtEndMatchReturnSOLMoney:Invoke()
        end
        if #ntf.deposit_change.prop_changes >0 then
            --返还sku门票
            if ntf.deposit_change.prop_changes[1].change_type==PropChangeType.Add then
                self.Events.evtEndMatchReturnSOLMoney:Invoke(false)
            elseif ntf.deposit_change.prop_changes[1].change_type==PropChangeType.ConflictToMail then
                self.Events.evtEndMatchReturnSOLMoney:Invoke()
            end
        end
    end
end

function TeamServer:CheckedDepositData(data)
    self:_CheckedNotify(data)
end
-------------服务器回包 End-------------

--------------------------------------------------------------------------
--- 本地化数据变更
--------------------------------------------------------------------------
function TeamServer:AddMember(newPlayerID, newPlayerInfo)
    if not self:FindMember(newPlayerID) then
        local myPlayerID = Server.AccountServer:GetPlayerId()
        self.teamInfos.Members[newPlayerID] = newPlayerInfo
        if myPlayerID ~= newPlayerID then
            if not next(self.teamInfos.Slots) then 
                table.insert(self.teamInfos.Slots,myPlayerID)
            end
            if not self:FindSeat(newPlayerID) then
                local isAdd = false
                for k,v in ipairs(self.teamInfos.Slots) do 
                    if v == 0 then 
                        isAdd = true
                        self.teamInfos.Slots[k] = newPlayerID
                        break
                    end
                end
                if not isAdd then 
                    table.insert(self.teamInfos.Slots,newPlayerID)
                end
            end
            self.Events.evtTeammateDisplayChanged:Invoke(newPlayerInfo, EDisplayChangeType.Spawn)
        else
            self.teamInfos.Slots[1] = newPlayerID
        end
    else
        logerror("Try to Add a existed member ", newPlayerID)
    end
end

function TeamServer:compareTables(A, B)
    local addedFields = {}
    local removedFields = {}

    -- 找出在 B 中新增的字段
    for key, value in pairs(B) do
        if A[key] == nil then
            table.insert(addedFields, value)
        end
        if A[key] ~= nil and B[key] ~= nil then 
            table.insert(addedFields, value)
        end
    end

    -- 找出在 A 中删除的字段
    for key, value in pairs(A) do
        if B[key] == nil then
            table.insert(removedFields, value)
        end
    end

    return addedFields, removedFields
end

function TeamServer:UpdateMember(newPlayerID, newPlayerInfo)
    loginfo("TeamServer:UpdateMember",newPlayerID)
    if self:FindMember(newPlayerID) then
        local reName = false
        if self.teamInfos.Members[newPlayerID].PlayerName ~= newPlayerInfo.PlayerName then 
            reName = true
        end
        local oldHeroID = self:GetPlayerHeroID(self.teamInfos.Members[newPlayerID])
        local newHeroID = self:GetPlayerHeroID(newPlayerInfo)
        local oldFashionID =self:GetPlayerFashionID(self.teamInfos.Members[newPlayerID])
        local newFashionID = self:GetPlayerFashionID(newPlayerInfo)
        local addEquips,removeEquips = self:compareTables( self.teamInfos.Members[newPlayerID].Equips,newPlayerInfo.Equips)
        self.teamInfos.Members[newPlayerID] = newPlayerInfo

        local myPlayerID = Server.AccountServer:GetPlayerId()
        --if myPlayerID ~= newPlayerID then
            self.Events.evtTeammateEquipChange:Invoke(newPlayerInfo,addEquips,removeEquips,not (oldHeroID == newHeroID and oldFashionID == newFashionID),reName)
        --end
    else
        logerror("Try to Update a nonexistent member ", newPlayerID)
    end
end


function TeamServer:GetPlayerHeroID(memberInfo)
    if memberInfo then
        return memberInfo.HeroID
    end
    return 0
end

function TeamServer:GetPlayerFashionID(memberInfo)
    if memberInfo then
        for k,v in pairs(memberInfo.fashion)do
            if v.slot == eHeroFashionPosition.FashionSuit then
                return v.id
            end
        end
    end
    return 0
end

function TeamServer:RemoveMember(targetPlayerID)
    loginfo("TeamServer:RemoveMember",targetPlayerID)
    if self:FindMember(targetPlayerID) then
        local myPlayerID = Server.AccountServer:GetPlayerId()
        if myPlayerID ~= targetPlayerID then
            local memberInfo = self.teamInfos.Members[targetPlayerID]
            self.teamInfos.Members[targetPlayerID] = nil
            local seat = self:FindSeat(targetPlayerID)
            self.teamInfos.Slots[seat] = 0
            self.Events.evtTeammateDisplayChanged:Invoke(memberInfo, EDisplayChangeType.Distroy)
        else
            self:ClearTeamInfos(true)
        end
    else
        logerror("Try to Remove a nonexistent member ", targetPlayerID)
    end
end

---@class TeamMemberInfo 玩家信息数据类
---@field PlayerID integer
---@field State integer
---@field PlayerName string
---@field HeroID integer
---@field HeroName string
---@field Index integer

function TeamServer:BuildTeamMemberInfo(memberPbInfo)
    local memberEquips = {}
    for _, equipProp in pairs(memberPbInfo.Props) do
        for _, prop in pairs(equipProp.load_props) do
            local Item = ItemBase:New(prop.id, prop.num, prop.gid)
            Item:SetRawPropInfo(prop)
            memberEquips[equipProp.position] = Item
        end
    end

    local newMember = {}
    newMember.PlayerID = memberPbInfo.PlayerID
    newMember.State = memberPbInfo.State
    newMember.PlayerSimpleInfo = memberPbInfo.Info
    newMember.PlayerName = memberPbInfo.Info and memberPbInfo.Info.nick_name or ""
    newMember.HeroID = self:_ExtractMemeberHeroId(memberPbInfo.Props) or 0
    newMember.HeroName = HeroHelperTool.GetHeroName(newMember.HeroID) or ""
    newMember.Seat = memberPbInfo.Seat
    newMember.JoinTimestamp = memberPbInfo.JoinTimestamp
    newMember.EquipPrice=memberPbInfo.EquipPrice
    newMember.PackQuestID=memberPbInfo.PackQuestID
    newMember.fashion=memberPbInfo.fashion
    newMember.accessories=memberPbInfo.accessorise

    --队员装备更新时，不需要重新获取Slot
    --[[local oldMember = self:FindMember(memberPbInfo.PlayerID)
    if oldMember and oldMember.Slot then
        newMember.Slot = oldMember.Slot
    else
        newMember.Slot = self:GetTeamMemberSlot(memberPbInfo.PlayerID, memberPbInfo.Seat)
    end]]

    newMember.Equips = memberEquips
    newMember.PicUrl = memberPbInfo.Info.pic_url
    newMember.Level = memberPbInfo.Info.level
    newMember.SeasonLevel = memberPbInfo.Info.season_lvl
    return newMember
end

--[[function TeamServer:GetTeamMemberSlot(memberId, memberIndex)
    local myPlayerID = Server.AccountServer:GetPlayerId()
    local capitalID = self:GetCapitalID()
	local teamNumLimit = self:GetTeamMatchMode().team_mode
    local slotLimit = math.min(3, teamNumLimit - 1)
    if myPlayerID == memberId then--如果是玩家id直接放0位
        return 0
    elseif capitalID == memberId then--如果是队长直接放2位
        return 1
    else--如果都不是，自己去寻空位
        for slot = 1,slotLimit do
            local member = self:GetMemberBySlot(slot)
            if member and member.PlayerID == memberId then
                return slot
            elseif member ==nil then
                return slot
            end
        end
    end
    logerror(string.format("GetTeamMemberSlot Team slots are all occupied!==>capitalID:%s,myPlayerID%s,GetMembers num:%s,teamNumLimit:%s",capitalID,myPlayerID,#self.teamInfos.Members,teamNumLimit))
end]]

function TeamServer:SetTeamDSInfo(ip, port, roomId, token)
    self.teamInfos.DSTextualIP = ip
    self.teamInfos.DSPort = port
    self.teamInfos.DSRoomID = roomId
    self.teamInfos.Token = token
end

function TeamServer:SetTeamInfos(newTeamInfos, fCallback)
    if not newTeamInfos then
        self:ClearTeamInfos()
        return
    end
    local info = "dstextualip "..tostring(newTeamInfos.DSTextualIP)..","
    info = info.."port "..tostring(newTeamInfos.DSPort)..","
    info = info.."roomId "..tostring(newTeamInfos.DSRoomID)..","
    info = info.."Token "..tostring(newTeamInfos.Token)..","
    --logwarning("MatchServer: ", info)
    self:ResetTeamInfos()
    self:SetTeamID(newTeamInfos.TeamID)
    self:SetSessionId(newTeamInfos.Session)
    self:SetCapitalID(newTeamInfos.LeaderID)
    local matchId = self:_FetchLegacyModeID(newTeamInfos.Modes)
    self:SetTeamMatchID(matchId)
    self:SetMembers(newTeamInfos.Members)
    Server.AccountServer:SetPlayerStateCode(GlobalPlayerStateEnums.EPlayerState_InTeam)
    self:SetTeamMatchMode(newTeamInfos.Modes, newTeamInfos.AddMemberType, newTeamInfos.spawn_point, newTeamInfos.GroupID)
    self:SetTeamState(newTeamInfos.State)
    self:SetAutoMatchTeamMates(newTeamInfos.IsAddMember)
    self:SetTeamDSInfo(newTeamInfos.DSTextualIP, newTeamInfos.DSPort, newTeamInfos.DSRoomID, newTeamInfos.Token)
    self:SetBHDOpenQuickJoinMode(newTeamInfos.IsOpenQuickJoin)
    GameModeSvr:SetMatchID(matchId or 0)

    if self.teamInfos.Mode and self.teamInfos.Mode.game_mode==MatchGameMode.TDMGameMode then
        loginfo(string.format("[HallPrepareRegion] TeamServer SetTeamInfos EnableMPRankMatch:%s",tostring(newTeamInfos.IsRankedMatch)))
        self:EnableMPRankMatch(newTeamInfos.IsRankedMatch)
    else
        loginfo(string.format("[HallPrepareRegion] TeamServer SetTeamInfos EnableSOLRankMatch :%s",tostring(newTeamInfos.IsRankedMatch)))
        self:EnableSOLRankMatch(newTeamInfos.IsRankedMatch)
    end
    if self:IsMember() then
        loginfo(string.format("[HallPrepareRegion] TeamServer SetTeamInfos player:%s Set RankcheckBox:%s",Server.AccountServer:GetPlayerIdStr(),tostring(self._bEnableRankMatch)))
        self.Events.evtRankMatchChanged:Invoke(self._bEnableRankMatch)
    end

    local matchMode = {}
    deepcopy(matchMode, newTeamInfos.Modes[1] or {})
    matchMode.add_member_type = newTeamInfos.AddMemberType
    matchMode.spawn_point = newTeamInfos.spawn_point
    GameModeSvr:SetMatchMode(matchMode)
    GameModeSvr:SetMatchModes(newTeamInfos.Modes)
    self:SendChangeEquipNtf()
    self.Events.evtTeamInfosUpdated:Invoke(newTeamInfos.TeamID)
    if newTeamInfos.RecruitmentState and newTeamInfos.Filter then
        self.Events.evtOnTeamRecruitmentStateChanged:Invoke(newTeamInfos.RecruitmentState, newTeamInfos.Filter)
    end
    if fCallback then fCallback() end
end

function TeamServer:ClearTeamInfos(keepMatchModes)
    loginfo("TeamServer:ClearTeamInfos",keepMatchModes)
    local teamMemberInfos =  self.teamInfos.Members
    local myPlayerID = Server.AccountServer:GetPlayerId()
    for _, memberInfo in pairs(teamMemberInfos) do
        if myPlayerID ~= memberInfo.Id then
            self.Events.evtTeammateDisplayChanged:Invoke(memberInfo, EDisplayChangeType.Distroy)
        end
    end
    self:ResetTeamInfos()
    if not keepMatchModes then
        Server.GameModeServer:SetMatchMode(Server.GameModeServer:GetUnknownGameMode())
        Server.GameModeServer:SetMatchModes({})
    end
    self:SetBHDOpenQuickJoinMode(false)
    self.InTeamWidgetTab = {}
    Server.AccountServer:SetPlayerStateCode(GlobalPlayerStateEnums.EPlayerState_Online)
    self.Events.evtOnTeamRecruitmentStateChanged:Invoke(TeamRecruitmentState.TeamRecruitmentStateEnd, nil)
end

function TeamServer.TeamSeatSortFunc(a, b)
    --自己坐最前面
    if Server.AccountServer:GetPlayerId() == a.PlayerID then
        return true
    elseif Server.AccountServer:GetPlayerId() == b.PlayerID then
        return false
    end
    --队长坐第二位
    if Server.TeamServer:GetCapitalID() == a.PlayerID then
        return true
    elseif Server.TeamServer:GetCapitalID() == b.PlayerID then
        return false
    end
    --剩下的先用Index排
    return a.Seat < b.Seat
end

function TeamServer:SetMembers(members)
    self.teamInfos.Members = {}
    --队长要最先去找位置，然后按照入队顺序去找slot
    table.sort(members, function(A,B)
                if A.PlayerID == self:GetCapitalID() then
                    return true
                elseif B.PlayerID == self:GetCapitalID() then
                    return false
                end
                return A.Seat < B.Seat
            end)
    for _, v in ipairs(members) do
        local newMember = self:BuildTeamMemberInfo(v)
        self:AddMember(v.PlayerID, newMember)
    end
end

--[[function TeamServer:GetMemberBySlot(memberSlot)
    for _, memberInfo in pairs(self.teamInfos.Members) do
        if memberInfo.Slot == memberSlot then
            return memberInfo
        end
    end
    return nil
end]]

---@return TeamServerPlayerInfo?
function TeamServer:GetMemberById(memberId)
    memberId = tonumber(memberId or "")
    for _, memberInfo in pairs(self.teamInfos.Members) do
        if memberInfo.PlayerID == memberId then
            return memberInfo
        end
    end
    return nil
end

--通过string类型的id去比较才是对的 uint64在lua中会越界
--获取队伍成员信息
function TeamServer:GetMemberByIdStr(memberId)
    for _, memberInfo in pairs(self.teamInfos.Members) do
        if Luautils.GetOverInt64String(memberInfo.PlayerID) == memberId then
            return memberInfo
        end
    end
    return nil
end

function TeamServer:GetOtherMembers()
    local otherMembers = {}
    local myID = self:GetMyID()
    for _, memberInfo in pairs(self.teamInfos.Members) do
        if memberInfo.PlayerID ~= myID then
            table.insert(otherMembers, memberInfo)
        end
    end
    return otherMembers
end

function TeamServer:_ExtractMemeberHeroId(props)
    for _, prop in pairs(props) do
        local loadProps = prop.load_props
        for _, loadProp in pairs(loadProps) do
            local heroId = tostring(loadProp.id)
            if HeroHelperTool.IsHeroId(heroId) then return heroId end
        end
    end
end

function TeamServer:UpdateCaptial(newCaptialID)
    self:SetCapitalID(newCaptialID)
    self.Events.evtCaptialChanged:Invoke()
end

--来自邮件系统 消息中心的组队邀请
function TeamServer:TeamToInviteByMsgCenter(inviterID,teamID)
    log("TeamServer:TeamToInviteByMsgCenter")
    if Server.TeamServer:GetTeamNum()>1 then
        LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.AlreadyInTeam)
        return
    end

    local onCSTeamResponseInviteTRes = function(res)
        local result = res.result
        if result and result == 0 then


            --房间相关处理
            if Server.AccountServer:IsInRoom() then
                -- 如果已经通过同意请求退出房间数据 -- 那么直接清空房间数据 -- 关闭房间
                Server.RoomServer.Events.evtLeaveRoom:Invoke(Server.RoomServer:GetRoomId())
            end
            --如果在队伍中，则需要退队
            if self:IsInTeam() then
                local teamId = self:GetTeamID()
                self:ClearTeamInfos()
                self.Events.evtYouLeaveTeam:Invoke(self:GetTeamID(), teamId,true)
            end
            if Server.MatchServer:GetIsMatching() then
                Server.MatchServer:EndMatching()
            end
            --入队
            self:SetTeamInfos(res.Info)
            self.Events.evtJoinTeam:Invoke(self:GetTeamID())
        elseif result==Err.TeamAlreadyInTeam then--12010
            self:UpdateTeamInfos(teamID)
        end
    end

    local req = pb.CSTeamResponseInviteTReq:New()
    req.TeamID = teamID
    req.InviterID = inviterID
    req.IsAgree = true
    req.RefuseMessage = ""
    req.PackQuestID = Server.LitePackServer:GetAllDownloadedQuestIDs()
    req.InviteeGameMode = Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Lobby and 2 or 1
    if self._inviterPlayerInfo[inviterID] then
        req.RoomID = self._inviterPlayerInfo[inviterID].roomId
        self._inviterPlayerInfo[inviterID] = nil
    end 
    logwarning("TeamServer:TeamToInviteByMsgCenter 上报的QuestID个数",#req.PackQuestID)

    req:Request(onCSTeamResponseInviteTRes)
end

--来自邮件系统 消息中心的入队申请
function TeamServer:TeamJoinByMsgCenter(apply_player_id,teamID, source, gameMode)
    log("TeamServer:TeamJoinByMsgCenter")
    local req = pb.CSTeamResponseJoinTReq:New()
    req.team_id = teamID
    req.apply_player_id = apply_player_id
    req.agree = true
    req.refuse_message = ""
    req.source = source
    req.apply_player_game_mode = gameMode
    req:Request()
    if req.team_id==0 then
        logerror("TeamServer:TeamJoinByMsgCenter teamId is 0",debug.traceback())
    end
end

function TeamServer:DoSendEquipingStateNtf(IsIntoEquiping)
    if Server.MatchServer:GetIsMatching() then
        return
    end
    if self:IsInTeam() and self:HasOtherMembers() then
        local ntf = pb.CSTeamIntoEquipTNtf:New()
        ntf.TeamID = self:GetTeamID()
        ntf.IsIntoState = IsIntoEquiping or false
       -- ntf:SendNtf()
    end
    --队员进入配装状态时，需要取消准备状态
    if IsIntoEquiping == true and self:IsInTeam() and not self:IsCaptial() then
       -- self:SendMatchReady(false)
    end
end

function TeamServer:AddWaitPrepareState(playerId)
    self:AddState(playerId, TeamMemberState.MemWaitPrepare)
    local memberInfo = self:FindMember(playerId)
    if not memberInfo then
        return
    end
    memberInfo.startWaitPrepareStateTime = TimeUtil.GetCurrentTime()
end

function TeamServer:RemoveWaitPrepareState(playerId)
    self:RemoveState(playerId, TeamMemberState.MemWaitPrepare)
    local memberInfo = self:FindMember(playerId)
    if not memberInfo then
        return
    end
    memberInfo.startWaitPrepareStateTime = 0
end

--专门给后台下发的状态用
function TeamServer:SetState(playerId, states)
    local memberInfo = self:FindMember(playerId)
    if not memberInfo then
        return
    end
    if self:HasState(states, TeamMemberState.MemPrepare) then
        memberInfo.State = states
    else
        local hasWaitPrepare = self:HasState(states, TeamMemberState.MemWaitPrepare)
        memberInfo.State = states
        if hasWaitPrepare then
            self:AddState(playerId, TeamMemberState.MemWaitPrepare)
        end
    end

    self.Events.evtReadyStateChanged:Invoke(playerId)
end

function TeamServer:AddState(playerId, state)
    local memberInfo = self:FindMember(playerId)
    if memberInfo and not self:HasState(memberInfo.State, state) then
        memberInfo.State = memberInfo.State | state
        return true
    end
    return false
end

function TeamServer:RemoveState(playerId, state)
    local memberInfo = self:FindMember(playerId)
    if memberInfo and self:HasState(memberInfo.State, state) then
        memberInfo.State = memberInfo.State - state
    end
end

function TeamServer:HasState(states, state)
    return states & state == state
end

function TeamServer:EnableSOLRankMatch(bEnable)
    self._bEnableRankMatch=bEnable
    self._bEnableSOLRankMatch=bEnable
    local tipsRecordServer=Server.TipsRecordServer
    tipsRecordServer:SetBoolean(tipsRecordServer.keys.CloseSOLRankMatch,not bEnable)
    loginfo("TeamServer:EnableSOLRankMatch:"..tostring(bEnable))
end

function TeamServer:EnableMPRankMatch(bEnable)
    self._bEnableRankMatch=bEnable
    self._bEnableMPRankMatch=bEnable
    local tipsRecordServer=Server.TipsRecordServer
    tipsRecordServer:SetBoolean(tipsRecordServer.keys.CloseMPScoreMatch,not bEnable)
    loginfo("TeamServer:EnableMPRankMatch:"..tostring(bEnable))
end

function TeamServer:GetEnableMPRankMatch()
    local tipsRecordServer=Server.TipsRecordServer
    self._bEnableMPRankMatch=not tipsRecordServer:GetBoolean(tipsRecordServer.keys.CloseMPScoreMatch)
    loginfo("TeamServer:Get EnableMPRankMatch:"..tostring(self._bEnableMPRankMatch))
    self._bEnableRankMatch=self._bEnableMPRankMatch
    return self._bEnableRankMatch
end

function TeamServer:GetEnableSOLRankMatch()
    local tipsRecordServer=Server.TipsRecordServer
    self._bEnableSOLRankMatch=not tipsRecordServer:GetBoolean(tipsRecordServer.keys.CloseSOLRankMatch)
    loginfo("TeamServer:Get EnableSOLRankMatch:"..tostring(self._bEnableSOLRankMatch))
    self._bEnableRankMatch=self._bEnableSOLRankMatch
    return self._bEnableRankMatch
end

function TeamServer:SendEnableRankMatch()
    if self._bSendEnableRankMatch==self._bEnableRankMatch then
        loginfo("TeamServer:SendEnableRankMatch already send same state!")
        return
    end
    loginfo("TeamServer:SendEnableRankMatch",self._bEnableRankMatch)
    local req = pb.CSTeamChangeIsRankTReq:New()
    req.TeamId=self:GetTeamID()
    req.isRankedMatch=self._bEnableRankMatch
    local fSendCallback=function(res)
        if res.result==0 then
            loginfo("TeamServer:SendEnableRankMatch return success!")
        else
            loginfo("TeamServer:SendEnableRankMatch return failed!")
        end
    end
    req:Request(fSendCallback,{bEnableHighFrequency = true})
    self._bSendEnableRankMatch=self._bEnableRankMatch
end

function TeamServer:IsInTeamAndMultiPerson()
    if self:IsInTeam() then
        if self:GetTeamNum()>1 then
            return true
        end
    end
    return false
end

function TeamServer:_ShowModuleNtf(moduleId,bIsUnlock,bNeedPop)
    if moduleId==SwitchModuleID.ModuleRankSOL and bIsUnlock then
        loginfo("[TeamServer] unlock sol rankMatch!")
        if not self:IsMember() then
            self:EnableSOLRankMatch(true)
        end
        if self:IsCaptial() then
            self:SendEnableRankMatch()
        end
        self.Events.evtMatchRankUnlockNtf:Invoke()
    end
    if moduleId==SwitchModuleID.ModuleScoreMP and bIsUnlock then
        loginfo("[TeamServer] unlock mp scoreMatch!")
        if not self:IsMember() then
            self:EnableMPRankMatch(true)
        end
        if self:IsCaptial() then
            self:SendEnableRankMatch()
        end
        self.Events.evtMatchRankUnlockNtf:Invoke()
    end
end

function TeamServer:FindSeat(playerId)
    for k,v in pairs(self.teamInfos.Slots) do 
        if v == playerId then 
            return k
        end
    end
end

function TeamServer:GetPlayerIdBySeat(slot)
    return self.teamInfos.Slots[slot]
end

-----------------------------------------------------------------------
--region BHD
--合作模式开赛协议
function TeamServer:ReqBHDTeamBeginMatch(fCallback)
    loginfo('TeamServer:ReqBHDTeamBeginMatch(fCallback)')
    local teamId = self:GetTeamId()
    if teamId == 0 then
        logerror('TeamServer:ReqBHDTeamBeginMatch 当前没有队伍teamid = 0')
        return
    end
    local fBhdTeamBeginMatchTRes = function(res)
        if res.result == 0 then
            Server.MatchServer:StartMatching()
            --- 旧预拉起入口
            -- self.Events.evtLaunchUE5:Invoke() --MatchServer PrepareJoinMatchNtf
        else
            logerror('TeamServer:ReqBHDTeamBeginMatch(fCallback) BHD开赛失败')
        end
        if fCallback then fCallback(res) end
    end
    local req = pb.CSBhdTeamBeginMatchTReq:New()
    req.team_id = self:GetTeamId()
    if DFHD_LUA == 1 then
        req.client_version = BHDHelperTool.GetLocalBHDClientVersion()
    else
        req.client_version = ""
    end
    loginfo('【BHDHelper】Start Match')
    req:Request(fBhdTeamBeginMatchTRes)
end

--解散队伍协议 BHD专属！！！
function TeamServer:ReqBHDTeamDissmiss(fCallback)
    loginfo('TeamServer:ReqBHDTeamDissmiss(fCallback)')
    local teamId = self:GetTeamId()
    if teamId == 0 then
        logerror('TeamServer:ReqBHDTeamBeginMatch 当前没有队伍teamid = 0')
        return
    end
    local fBhdTeamDismissTRes = function(res)
        if fCallback then fCallback(res) end
    end
    local req = pb.CSTeamDismissTReq:New()
    req.team_id = self:GetTeamId()
    req:Request(fBhdTeamDismissTRes)
end

--修改BHD队伍是否公开
function TeamServer:ReqSetIsOpenQuickJoinMode(isOpen, fCallback)
    loginfo('TeamServer:ReqSetIsOpenQuickJoinMode isOpen = ', isOpen)
    if not self:IsCaptial() then loginfo('TeamServer:ReqSetIsOpenQuickJoinMode 只有队长可以修改BHD房间是否公开') return end
    local fCSTeamChangeIsOpenQuickJoinTRes = function(res)
        -- if res.result == 0 then
        --     LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.ChangeBHDIsOpenQuickJoinModeSucceed)
        -- else
        --     LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.ChangeBHDIsOpenQuickJoinModeFailed)
        -- end
        self:SetBHDOpenQuickJoinMode(isOpen)
        if fCallback then fCallback(res) end
    end
    local req = pb.CSTeamChangeIsOpenQuickJoinTReq:New()
    req.TeamId = self:GetTeamId()
    req.isOpenQuickJoin = isOpen
    req:Request(fCSTeamChangeIsOpenQuickJoinTRes, {bEnableHighFrequency = true}) --以防玩家疯狂check
end

function TeamServer:SetBHDOpenQuickJoinMode(isOpen)
    loginfo('TeamServer:SetBHDOpenQuickJoinMode isOpen = ',isOpen)
    isOpen = setdefault(isOpen, true)
    local matchMode = self:GetTeamMatchMode()
    if matchMode and matchMode.game_mode ~= MatchGameMode.BlackHawkDown then return end
    if isOpen ~= self.isOpenQuickJoinMode then
        self.isOpenQuickJoinMode = isOpen
        self.Events.evtQuickJoinModeChange:Invoke()
    end
end

function TeamServer:GetOpenQuickJoinMode()
    return self.isOpenQuickJoinMode
end

function TeamServer:CheckIsInBHDMode()
    return self:IsInTeam() and self:GetTeamMatchMode() and self:GetTeamMatchMode().game_mode == MatchGameMode.BlackHawkDown
end

function TeamServer:GetIsAllMembersPurchasedBHD()
    local members = self:GetMembers()
    if not members or not next(members) then return true end
    for _, member in pairs(members) do
        if member.PlayerSimpleInfo and not member.PlayerSimpleInfo.bhd_is_purchased then
            return false
        end
    end
    return true
end

--endregion
-----------------------------------------------------------------------

function TeamServer:ForceTriggerReconnect()
    self.Events.evtMatchAllocTriggerReconnect:Invoke(true)
end

function TeamServer:GetSessionID()
    loginfo("TeamServer:GetSessionID")
    local dfmOnlineSessionManager = UDFMOnlineSessionManager.Get(GetWorld())
    return dfmOnlineSessionManager and dfmOnlineSessionManager:GetSessionID() or ""
end

return TeamServer
