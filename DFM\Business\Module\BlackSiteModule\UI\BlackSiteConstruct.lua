----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMBlackSite)
----- LOG FUNCTION AUTO GENERATE END -----------



local BlackSiteConstruct = ui("BlackSiteConstruct")

local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"

function BlackSiteConstruct:Ctor()
    self._wtEnter3DSafeHouseWB = self:Wnd("wtEnter3DSafeHouseWB", DFCommonButtonOnly)
    self._wtEnter3DSafeHouseWB:Event("OnClicked", self._OnEnter3DSafeHouseBtnClicked, self)

    self:_CtorPc()
    self:_CtorMobile()
end

function BlackSiteConstruct:_CtorPc()
    if not IsHD() then
        return
    end

    self._wtEnterDeviceWBList = {}
    self._wtDeviceContentCP = self:Wnd("wtDeviceContentCP", UIWidgetBase)

    for idx = 1, 10 do
        local wtEnterDeviceWB = self:Wnd("wtEnterDeviceWB_" .. idx, UIWidgetBase)

        if wtEnterDeviceWB then
            table.insert(self._wtEnterDeviceWBList, wtEnterDeviceWB)
        end
    end
end

function BlackSiteConstruct:_CtorMobile()
    if IsHD() then
        return
    end

    self._wtSubscribeWB = self:Wnd("wtSubscribeWB", DFCommonButtonOnly)
    self._wtSubscribeWB:Event("OnClicked", self._OnSubscribeBtnClicked, self)
end

function BlackSiteConstruct:OnOpen()
    self:_DoStartThing()
end

function BlackSiteConstruct:OnActivate()
    self:_DoStartThing()
end

function BlackSiteConstruct:OnShowBegin()
    self:_EnableGamepadFeature()
end

function BlackSiteConstruct:OnHideBegin()
    self:_DisableGamepadFeature()
    Module.Guide.Config.EGuideEvent.evtOnGuideMsg:Invoke(EGuideMsgSig.BlackSiteConstructOnHideBegin)
end

function BlackSiteConstruct:_DoStartThing()
    self:_CheckMobileSubscribeBtnState()
    self:PlayAnimation(self.WBP_BlackSite_Construct_in_manual, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
end

function BlackSiteConstruct:_OnEnter3DSafeHouseBtnClicked()
    Module.IrisSafeHouse:Enter3DSafeHouse()

    -- azhengzheng:特勤处交互数据上报
    LogAnalysisTool.DoSendBlackSiteInteractiveData(21)
end

-- azhengzheng:Gamepad Start
function BlackSiteConstruct:_EnableGamepadFeature()
    if not IsHD() then
        return
    end

    self._wtNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtDeviceContentCP, self, "Hittest")

    if self._wtNavGroup then
        for _, value in pairs(self._wtEnterDeviceWBList) do
            self._wtNavGroup:AddNavWidgetToArray(value)
            value:SetCppValue("bIsFocusable", true)
        end

        local lastFocusWidget = Module.BlackSite.Field:GetLastFocusWidget()

        if lastFocusWidget then
            WidgetUtil.SetUserFocusToWidget(lastFocusWidget, false)
        else
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup)
        end
    end

    self._enter3DSafeHouseInputAction = self:AddInputActionBinding("BlackSite_Enter3D_Gamepad", EInputEvent.IE_Pressed, self._OnEnter3DSafeHouseBtnClicked, self, EDisplayInputActionPriority.UI_Stack)
    self._wtEnter3DSafeHouseWB:SetDisplayInputAction("BlackSite_Enter3D_Gamepad", true, nil, true)
end

function BlackSiteConstruct:_DisableGamepadFeature()
    if not IsHD() then
        return
    end

    if self._wtNavGroup then
        for _, value in pairs(self._wtEnterDeviceWBList) do
            value:SetCppValue("bIsFocusable", false)
        end

        WidgetUtil.RemoveNavigationGroup(self)
        self._wtNavGroup = nil
    end

    if self._enter3DSafeHouseInputAction then
        self:RemoveInputActionBinding(self._enter3DSafeHouseInputAction)
        self._enter3DSafeHouseInputAction = nil
    end
end
-- azhengzheng:Gamepad End

function BlackSiteConstruct:_CheckMobileSubscribeBtnState()
    if IsHD() then
        return
    end

    -- azhengzheng:目前订阅按钮只在手微显示
    local channel = Server.SDKInfoServer:GetChannel()

    if not channel or channel ~= EChannelType.kChannelWechat then
        self._wtSubscribeWB:Collapsed()
        return
    end

    -- azhengzheng:再检查订阅功能是否开放
    if Module.ModuleSwitcher:CheckModuleSwitcher(SwitchSystemID.SwitchSystemSecretServiceSubscription, nil, nil, nil) ~= EFirstLockResult.Open then
        self._wtSubscribeWB:Collapsed()
        return
    end

    self._wtSubscribeWB:Visible()
end

function BlackSiteConstruct:_OnSubscribeBtnClicked()
    if not Module.BlackSite:CheckBtnCDFinishByType("Subscribe", Module.BlackSite.Config.Loc.BlackSiteClickCollectBtnFastTip) then
        return
    end

    Module.Subscribe:SubscribeGameEvent("FE7EF0A020A3EB823072513A3D074DD174359DABDE430B0A69CFF87D05636DD7", nil)
end

return BlackSiteConstruct