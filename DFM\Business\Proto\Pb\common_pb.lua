require("DFM.Business.Proto.Pb.ds_common_pb")
require "DFM.Business.Proto.ProtoBaseHint"

pb = pb or {}

if __proto_require_editor_file == true then
    require "DFM.Business.Proto.pb.common_editor_pb"
end

MatchClientType = {
Client_Invalid = 0,
Client_PC = 11,
Client_Windows = 12,
Client_Editor = 13,
Client_PC_Editor = 14,
Client_Simulator = 21,
Client_Controller = 31,
Client_IOS = 41,
Client_Android = 42,
Client_Max = 60,
}
MatchClientGroup = {
ClientGroup_Invalid = 0,
ClientGroup_PC = 1,
ClientGroup_Mobile = 2,
ClientGroup_Unknown = 3,
ClientGroup_Xbox = 4,
ClientGroup_PS = 5,
}
MatchIsolateSpecialType = {
Isolate_Normal = 0,
Isolate_Simulator = 1,
Isolate_Negative = 2,
Isolate_Cheat = 3,
Isolate_Under_Trade = 4,
Isolate_RedList = 5,
Isolate_GrayList = 6,
}
ExperimentGroup = {
NoExperiment = 0,
TreatmentGroup = 1,
ControlGroup = 2,
BlankGroup = 3,
}
PlatIDType = {
Plat_Invalid = 0,
Plat_IOS = 100,
Plat_Android = 101,
Plat_PC = 102,
Plat_Web = 103,
Plat_Linux = 104,
Plat_Windows = 105,
Plat_Switch = 106,
Plat_Mac = 107,
Plat_Playstation = 108,
Plat_XBox = 109,
Plat_Harmony = 112,
Plat_FAKE_GOOGLE_PC = 1002,
}
DeviceType = {
Device_Invalid = 0,
Device_PC = 1,
Device_Mobile = 2,
Device_Console = 3,
Device_Other = 1000,
}
AccountIDType = {
ACCOUNT_NONE = 0,
ACCOUNT_WECHAT = 1,
ACCOUNT_QQ = 2,
ACCOUNT_Guest = 3,
ACCOUNT_Fackebook = 4,
ACCOUNT_GameCenter = 5,
ACCOUNT_Google = 6,
ACCOUNT_Twitter = 9,
ACCOUNT_Garena = 10,
ACCOUNT_Switch = 13,
ACCOUNT_Line = 14,
ACCOUNT_Apple = 15,
ACCOUNT_VK = 19,
ACCOUNT_Xbox = 20,
ACCOUNT_Steam = 21,
ACCOUNT_Epic = 24,
ACCOUNT_Discord = 26,
ACCOUNT_PS5 = 27,
ACCOUNT_Twitch = 28,
ACCOUNT_DMM = 30,
ACCOUNT_SquareEnix = 32,
ACCOUNT_Kakao = 35,
ACCOUNT_Ubisoft = 37,
ACCOUNT_VNG = 38,
ACCOUNT_LevelInfinite = 131,
ACCOUNT_WX_OPENID = 4098,
ACCOUNT_QQ_OPENID = 4099,
}
ChannelIDType = {
Channel_None = 0,
Channel_WX = 1,
Channel_QQ = 2,
}
GameCenterType = {
GameCenter_None = 0,
GameCenter_WX = 1,
GameCenter_QQ = 2,
}
ClientGwalletReqType = {
From_Mobile = 0,
From_Pc = 1,
}
UserLogoutDSReason = {
UserLogoutDS_Normal = 0,
UserLogoutDS_KickByDS = 1,
UserLogoutDS_KickByZone = 2,
UserLogoutDS_ClientQuit = 3,
UserLogoutDS_Max = 4,
}
GIDKeyType = {
KEYInvalidType = 0,
Deposit = 1,
AuctionOrder = 2,
WeaponAssembly = 3,
MPDeposit = 4,
DSRoom = 5,
WorldChatType = 6,
MPDepositDSProp = 7,
TeamGuid = 8,
RoomGuid = 9,
ReconciliationFlow = 10,
MatchSnapShootGuid = 11,
CollectionType = 12,
MarketPreBuyOrder = 13,
}
MapBoardAddMemberType = {
AutoAddMember = 0,
MustAddMember = 1,
MustNotAddMember = 2,
}
EDSMatchType = {
kDSMatchTypeNone = 0,
kDSMatchTypeMatch = 1,
kDSMatchTypeLadder = 2,
kDSMatchTypeRoom = 3,
kDSMatchTypeWorld = 4,
kDSMatchTypeGuide = 5,
kDSMatchTypeSafehouse = 6,
}
EDSMatchSubType = {
kDSMatchSubTypeNone = 0,
kDSMatchSubTypeSOL = 1,
kDSMatchSubTypePVE = 2,
kDSMatchSubTypeWorld = 3,
kDSMatchSubTypeTDM = 4,
}
EDSMatchSOLOMode = {
kDSMatchSOLOModeNone = 0,
kDSMatchModePMC = 1,
kDSMatchModeSCAV = 2,
}
EDSMatchSubMode = {
kDSMatchSubModeNone = 0,
kDSMatchSubModeSolo = 1,
kDSMatchSubModeDouble = 2,
kDSMatchSubModeThreeMember = 3,
kDSMatchSubModeFourMember = 4,
kDSMatchSubModeTwentyMember = 7,
}
EDSPVEMissionLevel = {
PVELevelNone = 0,
Easy = 1,
Normal = 2,
Difficult = 3,
Actual = 4,
}
EMatchPoolType = {
kMatchPoolTypeNone = 0,
kMatchPoolTypeWarm = 1,
kMatchPoolTypeLow = 2,
kMatchPoolTypeMiddle = 3,
kMatchPoolTypeHigh = 4,
}
EGameRuleType = {
NoneGameRule = 0,
SafehouseRule = 1,
DiscoveryRule = 2,
PVERaidRule = 3,
SOLRule = 4,
RaidRule = 5,
}
EDsExitReason = {
DsExitReason_Normal = 0,
DsExitReason_Overtime = 1,
DsExitReason_Coredump = 2,
DsExitReason_Signaled = 3,
DsExitReason_NeverForked = 4,
DsExitReason_OutOfMemory = 5,
DsExitReason_Status = 6,
DsExitReason_FatherSwap = 7,
DsExitReason_HeartTimeout = 8,
DsExitReason_Multi = 9,
DsExitReason_Unknown = 9999,
}
TDMCampType = {
Regular = 0,
Rebel = 1,
}
eEquipPosition = {
NonePos = 0,
GridPage_Begin = 1,
GridPage_Container = 2,
GridPage_Extension_Vip = 98,
GridPage_End = 99,
Body_Begin = 100,
Helmet = 101,
ArmedForceBaseProp = 103,
BreastPlate = 105,
ChestHanging = 107,
Bag = 108,
SafeBox = 109,
MainWeaponLeft = 111,
MainWeaponRight = 112,
MeleeWeapon = 113,
Pistol = 114,
KeyChain = 116,
ArmedforceProp1 = 115,
ArmedforceProp2 = 117,
Role = 118,
PVEMainWeaponLeft = 120,
PVEMainWeaponRight = 121,
Medicine = 122,
BulletLeft = 131,
BulletRight = 132,
Body_End = 139,
MP_Begin = 140,
MP_ArmedForceTDMProp = 142,
MP_MainWeapon = 143,
MP_SecondaryWeapon = 144,
MP_MeleeWeapon = 145,
MP_ArmedforceProp1 = 146,
MP_ArmedforceProp2 = 147,
MP_End = 148,
CarryOutPropsPos = 1999,
Extension_Begin = 2000,
Extension_End = 99000,
Box_Begin = 100000,
Box_InChestBag = 107001,
Box_InBag = 108001,
Box_InSafeBox = 109001,
Box_InKeyChain = 116001,
Box_End = 189999,
Pocket = 199997,
BagSpaceContainer = 199998,
Pos_With_Capacity_End = 2010000,
}
PositionTag = {
PositionNone = 0,
PositionWeapon = 1,
PositionEquipment = 2,
PositionComsumable = 3,
PositionOther = 4,
}
PropBindingType = {
BindingBinded = 0,
BindingNotBind = 1,
BindingTeammate = 2,
}
PropAgingType = {
AgingNone = 0,
AgingDuration = 1,
AgingPeriodOfTime = 2,
}
PropLimitDef = {
LimitNone = 0,
LimitAgingDuration = 1,
LimitAgingPeriodOfTime = 2,
LimitUseCnt = 3,
LimitNotBind = 4,
}
PropTag = {
PropTagNone = 0,
}
PropClassType = {
ClassNone = 0,
ClassGreen = 2,
ClassBlue = 3,
ClassPurple = 4,
ClassOrange = 5,
ClassRed = 6,
}
InventoryType = {
InventoryInvalid = 0,
InventorySOL = 1,
InventoryMP = 2,
InventoryCollect = 3,
InventoryCurrency = 4,
InventoryHero = 5,
InventoryMail = 6,
InventoryActivity = 7,
InventoryAccountExp = 10,
InventorySeasonExp = 11,
InventoryQuality = 12,
NoInventory = 99,
}
DepositReissueSourceType = {
Common = 0,
Rollback = 1,
Auto = 2,
}
PropChangeType = {
PropChangeNone = 0,
Add = 1,
Del = 2,
Modify = 3,
ModifyWithMove = 4,
Move = 5,
SendBuyMail = 6,
AddWithInstall = 7,
AutoTakenOut = 8,
ReissueToMail = 9,
ConflictToMail = 10,
}
ePropChangeReason = {
NotSetted = 0,
Init = 3,
Repair = 5,
Split = 7,
Break = 9,
GM = 10,
Mall = 11,
InsuranceStore = 12,
SuccEscape = 13,
FailEscape = 14,
Sort = 15,
PVEReward = 16,
AuctionSell = 17,
AuctionBuy = 18,
AuctionRenew = 19,
QuestReward = 20,
FromSystemMail = 21,
RaidReward = 22,
CurrencyExchange = 23,
ShopBuy = 24,
Lottery = 25,
TeammateReturn = 26,
RaidCarryOut = 27,
MallSvrBuy = 28,
MallSvrSell = 29,
MallSvrGift = 30,
AuctionPulloff = 31,
ShopMidasPayRefund = 32,
ShopMidasPayDelivery = 33,
PayDeliveryByEmailAsAbnormal = 34,
GuideGive = 50,
Safehouse = 51,
QuestSubmit = 52,
Decomposition = 53,
TeammatePropSendBack = 54,
NumeralCarryInToDS = 55,
OneClickUnEquip = 56,
EquipRental = 57,
Reissue = 58,
ReissueGetFromMail = 48,
BroadMove = 59,
SettlementTkvMoneyPaper = 60,
SettlementRaidEvaluatePrize = 61,
SettlementRaidTeammateProps = 62,
SettlementTkvWassThawWeapon = 63,
SettlementBankruptCompensation = 64,
FromMarketSell = 65,
FromMarketBuy = 66,
FromMarketRenew = 67,
FromMarketPulloff = 68,
ReconciliationReissue = 69,
AccountLeveUpAddProps = 70,
AccountLeveUpReward = 71,
ShareReward = 72,
AccountLeveAutoUnlock = 73,
WeaponCompound = 80,
WeaponApplySkin = 81,
WeaponNewSkin = 82,
WeaponStyle = 83,
WeaponGetGunByNewSkin = 84,
WeaponCheckCollectionSkin = 85,
WeaponRandSkinApply = 86,
ActivityReason = 90,
IDIPReason = 100,
IDIPCallDebt = 101,
IDIPResetCurrency = 102,
IDIPMidasBuyProvide = 103,
SeasonLevelUPReward = 110,
SeasonLevelUPSOLGrowthReward = 111,
SeasonSolRankRecvAwards = 112,
SeasonMpRankRecvAwards = 113,
SeasonSecurityCompensateCarryInProps = 114,
SeasonSecurityCompensatedSpecificProps = 115,
SeasonSecurityCompensateProps = 116,
SeasonSolRankMallBuy = 117,
SeasonSolRankMatch = 118,
HeroInit = 201,
HeroShopBuy = 202,
HeroLottery = 203,
HeroCurrencyExchange = 204,
HeroActivity = 205,
CollectReasonUsed = 301,
CollectReasonRollback = 302,
CollectReasonInit = 303,
CollectReasonActivity = 304,
CollectReasonBlueprintRecv = 305,
CollectReasonMail = 306,
CollectionCombineDelSkin = 307,
CollectionCombineAddSkin = 308,
CollectionSkinRename = 309,
CollectionDelPropPreprocess = 310,
CollectionSkinUnApplyAll = 311,
CollectionSettlementCarryOutBySOL = 312,
CollectionSettlementCarryOutByMP = 313,
CollectionSeasonPropExpire = 314,
CollectionSkinKillCnterChg = 315,
CollectionSkinPreUnlock = 316,
CollectionAutoDistribution = 317,
CollectionPendantRename = 318,
CollectionCombineDelPendant = 319,
CollectionCombineAddPendant = 320,
CollectionPendantSuitActive = 321,
CollectionGMShopMandelLottery = 322,
CollectionPropExpired = 323,
CollectionNetbarRight = 324,
CollectionFastUse = 325,
CollectionMeleeSync = 326,
VehicleAccountLevelUp = 401,
VehicleLevelUp = 402,
VehicleGMAdd = 403,
VehicleDefaultUnlock = 404,
VehicleConfAddSlot = 405,
VehicleConfRefreshUnlock = 406,
VehicleTaskDone = 407,
VehicleTaskUpdate = 408,
EasterEggAward = 504,
MPChangeReason_Init = 600,
MPChangeReason_WeaponModify = 601,
MPChangeReason_WeaponUnlock = 602,
MPChangeReason_Used = 603,
MPChangeReason_Settlement = 604,
MPChangeReason_GM = 605,
MPChangeReason_Sync = 606,
MPChangeReason_WeaponDel = 607,
BattlePassReward = 701,
MPMandelBrickDrop = 801,
FromMarketSold = 802,
FromMarketPreBuy = 803,
FromMarketPullOffPreBuy = 804,
FromMarketBuyFailRollBack = 810,
MidasBuyGoods = 888,
EquipmentRental_Used = 901,
EquipmentRental_Refresh_Used = 902,
DepositOperBullet = 1001,
DepositSyncBodyContainer = 1002,
DepositEquipVipExtensionBox = 1003,
DepositSettlementConflictMail = 1004,
DepositEquipSafeBoxOrCardPack = 1005,
DepositOperateExtensionBox = 1006,
DepositFull = 1007,
DepositSettlementConflict = 1008,
DepositGetProps = 1009,
DepositNumeralRollback = 1010,
DepositReissueAuto = 1011,
DepositInvalidPropDel = 1012,
DepositCheckSkinValid = 1013,
DepositJoinMatchDeductFee = 1014,
DepositCancelMatchReturnFee = 1015,
DepositTimeoutReturnFeeToMail = 1016,
DepositTimeoutReturnFeeFromMail = 1017,
DepositActiveRecoveryAmount = 1018,
DepositSendTeammateReturnMail = 1019,
DepositShelveShowCabinet = 1020,
DepositWithdrawShowCabinet = 1021,
DepositMoveShowCabinet = 1022,
DepositSwapShowCabinet = 1023,
DepositLevelUpShowGrid = 1024,
DepositUnlockShowCabinet = 1025,
DepositSetShowGridLevel = 1026,
DepositDelCarryInInfo = 1027,
DepositCancelMatchSysMailReturnFee = 1028,
DepositSwapDIYCabinet = 1029,
DepositSubmitShowGridCostItem = 1030,
DepositUpdateNetbarPriv = 1031,
AchievementUnlock = 1100,
ShopLottery = 1200,
ShopMandelLottery = 1201,
ShopPresentInGame = 1202,
ShopPresentInWeb = 1203,
ShopBuyInWeb = 1204,
ShopMandelOpenedReward = 1205,
ShopBuyProvide = 1206,
HeroGrowLineReward = 1300,
HeroGrowLineRewardGM = 1301,
NetbarPrivTrialUnlock = 1302,
HeroRecriuit = 1303,
HeroRecriuitGM = 1304,
HeroArmedPropFashionStoreExpand = 1305,
HeroArmedPropFashionLottery = 1306,
HeroArmedPropQualityReward = 1307,
GameCenterDailyLoginReward = 1400,
PlayerInfoReputationReward = 1401,
ArmoryUnlock = 1450,
BhdGiveReward = 1500,
RankGiveCommonReward = 1550,
RankGiveTitleReward = 1551,
RoomsvrMidSupply = 1600,
AcceleratorExpired = 1700,
}
ePropChangeSubReason = {
InvalidSubReason = 0,
SafehouseUpgradeCost = 5101,
SafehouseProduceCost = 5102,
SafehouseProduceAward = 5103,
AuctionSellCost = 1701,
AuctionSellAward = 1702,
MallBuyByHafCoin = 2801,
MallBuyByExchangeProp = 2802,
}
PositionChangeType = {
PosChangeNone = 0,
PosChangeAdd = 1,
PosChangeDel = 2,
PosChangeModify = 3,
}
CompoundType = {
CompoundNone = 0,
CompoundProp = 1,
BreakProp = 2,
CompoundReplace = 3,
CompoundUpgrade = 4,
CompoundModify = 5,
EquipPerk = 6,
UnEquipPerk = 7,
SwapPerk = 8,
CompoundMove = 9,
UpdateAttr = 10,
SwapPart = 11,
}
eDepositSortStyle = {
space_first = 0,
class_first = 1,
}
eDepositSortClass = {
none = 0,
weapon = 1,
parts = 2,
equipment = 3,
consumable = 4,
key = 5,
collection = 6,
containerBox = 7,
others = 8,
}
eGameItemType = {
GTypeNormal = 0,
GTypeArmedProp1 = 101,
GTypeArmedProp2 = 102,
GTypeRandomLottery = 201,
GTypeLotteryBox = 211,
GTypeWeaponExpCard = 221,
GTypeRenameCard = 222,
GTypeActCardSafeBox = 230,
GTypeActCardPack = 231,
GTypeTimeLimitRightsCard = 236,
GTypeBattleConsumeExp = 226,
GTypeBattleConsumeScore = 228,
GTypeBattleConsumeKeepScore = 238,
}
IntimacyChangeReason = {
invalid_reason = 0,
gift_award = 1,
week_descend = 2,
gm = 3,
}
eExchangeType = {
eExchangeType_DefaultMoney = 0,
eExchangeType_OtherMoney = 1,
eExchangeType_Prop = 2,
}
eMallPropLimitTime = {
MallPropLimitTime_NoUsed = 0,
MallPropLimitTime_Used = 1,
}
eTradeType = {
TradeType_Buy = 0,
TradeType_Sell = 1,
}
ETournamentPrimaryID = {
ET_PrimaryID_NONE = 0,
ET_PrimaryID_SHOOT = 1,
ET_PrimaryID_VEHICLE = 2,
ET_PrimaryID_TACTIC = 3,
ET_PrimaryID_TOTAL = 999,
}
EventType = {
EventTypeInvalid = 0,
EventTypeMajorIDLevelUp = 1,
EventTypeMinorIDLevelUp = 2,
EventTypeSeasonFirstPlay = 3,
EventTypeWinStreak = 4,
EventTypeWinAfterLoseStreak = 5,
EventTypePlay10Matches = 6,
EventTypePlay50Matches = 7,
EventTypePlay100Matches = 8,
}
EAddRankScoreReason = {
EAddRankScoreReason_None = 0,
EAddRankScoreReason_KillAI = 1,
EAddRankScoreReason_KillPlayer = 2,
EAddRankScoreReason_LootingBox = 3,
EAddRankScoreReason_Escape_Failed = 4,
EAddRankScoreReason_Contract = 5,
EAddRankScoreReason_BluePrint = 6,
EAddRankScoreReason_CarryOutPrice = 7,
EAddRankScoreReason_AssistKillPlayer = 8,
EAddRankScoreReason_HighQualityPrice = 9,
EAddRankScoreReason_ReputationAward = 10,
}
EAddExpReason = {
kAddExpReasonDefault = 0,
kAddExpReasonGameEscape = 1,
kAddExpReasonProduce = 2,
kAddExpReasonKill = 3,
kAddExpReasonQuest = 4,
kAddExpReasonKillAI = 5,
kAddExpReasonKillEliteAI = 6,
kAddExpReasonKillBoss = 7,
kAddExpReasonComboKill = 8,
kAddExpReasonHeadShot = 9,
kAddExpReasonCure = 10,
kAddExpReasonFirstLoot = 11,
kAddExpReasonFirstArea = 12,
kAddExpReasonFix = 13,
kAddExpReasonBluePrint = 14,
kAddExpReasonContract = 15,
kAddExpReasonShareReward = 16,
kAddExpReasonHighQualityProduce = 17,
kAddExpReasonArenaKillPlayer = 18,
kAddExpReasonArenaResult = 19,
kAddExpReasonGM = 981,
}
EInviteResult = {
EInviteResult_None = 0,
EInviteResult_Agree = 1,
EInviteResult_Refuse = 2,
}
KickPlayerReason = {
KickReasonNone = 0,
KickReasonNeedRelogin = 1,
KickReasonRepeatLogin = 2,
KickReasonNeedLogin = 3,
KickReasonZK = 4,
KickReasonDisableGateway = 5,
KickReasonAccountLimit = 6,
KickReasonAccountDeregister = 7,
KickReasonGM = 8,
}
AccountPunishReason = {
PunishDefault = 0,
PunishDirty = 1,
PunishGM = 2,
PunishExternalAccountInnerIPLimit = 3,
}
AccountPunishType = {
INVALID = 0,
MODIFY_NICKNAME_FORBIDDEN = 1,
SOCIAL_FORBIDDEN = 2,
CHAT_SILENT = 3,
CHAT_MSG_FORBIDDEN = 4,
CHAT_VOICE_FORBIDDEN = 5,
MODIFY_AVATAR_FORBIDDEN = 6,
AUCTION_FORBIDDEN = 7,
LOGIN_FORBIDDEN = 8,
MODIFY_IDIOGRAPH_FORBIDDEN = 9,
CLEAN_UP_HISTORY = 10,
REAL_TIME_VOICE_FORBIDDEN = 11,
MATCH_FORBIDDEN = 12,
MATCH_ISOLATION = 13,
SOL_MATCH_FORBIDDEN = 14,
TDM_MATCH_FORBIDDEN = 15,
RANK_FORBIDDEN = 16,
MOSSAI_FORBIDDEN = 17,
AUCTION_BUY_LIMIT = 18,
UPLOAD_REPLAY = 19,
CHANGE_WEAPON_DESIGN_NAME_FORBIDDEN = 20,
FRIENDS_GIVE_GIFTS_FORBIDDEN = 21,
}
EAttrType = {
EAttrType_None = 0,
EAttrType_MaxStamina = 9,
EAttrType_MaxHeadHealth = 26,
EAttrType_HeadHealth = 27,
EAttrType_MaxThoraxHealth = 28,
EAttrType_ThoraxHealth = 29,
EAttrType_MaxStomachHealth = 30,
EAttrType_StomachHealth = 31,
EAttrType_MaxRightArmHealth = 32,
EAttrType_RightArmHealth = 33,
EAttrType_MaxLeftArmHealth = 34,
EAttrType_LeftArmHealth = 35,
EAttrType_MaxRightLegHealth = 36,
EAttrType_RightLegHealth = 37,
EAttrType_MaxLeftLegHealth = 38,
EAttrType_LeftLegHealth = 39,
EAttrType_MaxHealth = 63,
EAttrType_Health = 64,
}
EAttrBuffType = {
EAttrBuffType_None = 0,
EAttrBuffType_Bleeding = 1101,
EAttrBuffType_Fracture = 1201,
EAttrBuffType_Pain = 1301,
EAttrBuffType_AIBehaviorFactor_Defensive = 1401,
EAttrBuffType_TemporaryPainkiller = 1501,
EAttrBuffType_DurationAddHP = 1601,
EAttrBuffType_DurationAddHPAuto = 1602,
EAttrBuffType_FixFracture = 1701,
EAttrBuffType_FixBleeding = 1801,
EAttrBuffType_EatFull = 1901,
EAttrBuffType_Spicy = 2001,
EAttrBuffType_Excited = 2101,
EAttrBuffType_Drunk = 2201,
}
EAttrBodyPart = {
EAttrBodyPart_All = 0,
EAttrBodyPart_Head = 1,
EAttrBodyPart_Thorax = 2,
EAttrBodyPart_Stomach = 3,
EAttrBodyPart_RightArm = 4,
EAttrBodyPart_LeftArm = 5,
EAttrBodyPart_RightLeg = 6,
EAttrBodyPart_LeftLeg = 7,
EAttrBodyPart_Auto = 100,
}
GlobalPlayerStateEnums = {
EPlayerState_Offline = 0,
EPlayerState_Online = 1,
EPlayerState_InMatch = 2,
EPlayerState_Matching = 4,
EPlayerState_Face2FaceTeamRoom = 8,
EPlayerState_WaitDS = 16,
EPlayerState_PickHeroStage = 32,
EPlayerState_InTeam = 256,
EPlayerState_InRoom = 512,
}
MatchType = {
MatchTypeNone = 0,
MatchTypeMatch = 1,
MatchTypeLadder = 2,
MatchTypeRoom = 3,
MatchTypeWorld = 4,
MatchTypeGuide = 5,
MatchTypeSafehouse = 6,
}
MatchSubType = {
MatchSubTypeNone = 0,
MatchSubTypeSOL = 1,
MatchSubTypePVE = 2,
MatchSubTypeWorld = 3,
MatchSubTypeTDM = 4,
}
MatchMode = {
MatchModeNone = 0,
MatchModeTKV = 1,
MatchModeTraining = 2,
}
MatchPoolType = {
MatchPoolTypeNone = 0,
MatchPoolTypeWarm = 1,
MatchPoolTypeLow = 2,
MatchPoolTypeMiddle = 3,
MatchPoolTypeHigh = 4,
}
EGspGender = {
kGspGenderMale = 0,
kGspGenderFemale = 1,
}
EGspPlayerGameStatusType = {
kGspPlayerGameStatusTypePlaying = 0,
kGspPlayerGameStatusTypeEndGame = 1,
kGspPlayerGameStatusTypeQuitGame = 2,
}
EGspExtractionPoint = {
kGspExtractionPointZeroDamHighway2 = 0,
kGspExtractionPointZeroDamUnderground = 1,
kGspExtractionPointZeroDamPort = 2,
kGspExtractionPointZeroDamTop = 3,
kGspExtractionPointZeroDamTunnel = 4,
kGspExtractionPointZeroDamHighway1 = 5,
kGspExtractionPointZeroDamInside = 6,
}
EGspPlayerDeathReason = {
kGspPlayerDeathReasonKilledByPlayer = 0,
kGspPlayerDeathReasonKilledByAI = 1,
kGspPlayerDeathReasonKilledByBoss = 2,
kGspPlayerDeathReasonKilledByAccident = 3,
kGspPlayerDeathReasonKilledByAIPlayer = 4,
}
EGspBodyPart = {
kGspBodyPartHead = 0,
kGspBodyPartChest = 1,
kGspBodyPartStomach = 2,
kGspBodyPartLeftArm = 3,
kGspBodyPartRightArm = 4,
kGspBodyPartLeftLeg = 5,
kGspBodyPartRightLeg = 6,
}
EGspAccidentType = {
kGspAccidentFall = 0,
kGspAccidentBleeding = 1,
kGspAccidentTraffic = 2,
kGspAccidentExplosive = 3,
kGspAccidentBurn = 4,
kGspAccidentDrowning = 5,
kGspAccidentSkill = 6,
kGspAccidentMissing = 7,
kGspAccidentUnKnown = 9999,
}
EGspPlayerQuitReason = {
kGspPlayerQuitReasonDirectQuit = 0,
kGspPlayerQuitReasonRefuseReconnect = 1,
}
GspMatchEventType = {
kMatchEventTypeJoinGame = 0,
kMatchEventTypeNthKill = 1,
kMatchEventTypeComboKill = 2,
kMatchEventTypeLootItem = 3,
kMatchEventTypeKillBoss = 4,
kMatchEventTypeExceedPrice = 5,
kMatchEventTypeEndGame = 6,
}
EGspPlayerKillType = {
kGspPlayerKillTypeKill = 0,
kGspPlayerKillTypeAssist = 1,
}
EGspEnemyType = {
kGspEnemyTypePlayer = 0,
kGspEnemyTypeAI = 1,
kGspEnemyTypeBoss = 2,
kGspEnemyTypePlayerAI = 3,
}
EGspBluePrintType = {
kGspBluePrintTypeNone = 0,
kGspLowBluePrintType = 1,
kGspNormalBluePrintType = 2,
kGspHighBluePrintType = 3,
}
EGspPVEResultType = {
kGspPVEResultComplete = 0,
kGspPVEResultFailed = 1,
}
DsDamageStatus = {
invalid_status = 0,
infantry = 11,
tank_crash = 21,
tank_machine_gun = 22,
tank_main_gun = 23,
gunship_black_hawk_machine_gun = 31,
hummer_crash = 41,
humvee_armored_vehicle_machine_gun = 42,
stationary_machine_guns_for_positions = 51,
the_A4_gun_in_the_position = 52,
}
RaidLevel = {
rank_zero = 0,
rank_c = 1,
rank_b = 2,
rank_a = 3,
rank_s = 4,
}
MatchBotType = {
InvalidBotType = 0,
ForAILab = 1,
ForBehTree = 2,
}
MatchBotSubType = {
InvalidBotSubType = 0,
ForReplace = 1,
ForNormal = 2,
}
TDMResult = {
Match_Loss = 0,
Match_Win = 1,
Match_Tie = 2,
}
MPPropChangeType = {
MPPropChangeNone = 0,
MPPropChangeAdd = 1,
MPPropChangeModify = 3,
MPPropChangeDel = 4,
}
MPBagChageType = {
MPBagChangeNone = 0,
MPBagChangeAdd = 1,
MPBagChangeDel = 2,
MPBagChangeModify = 3,
}
ePraiseType = {
ePraiseType_Default = 0,
}
HeroAccessoryEquipReason = {
HeroAccEquipReason_None = 0,
HeroAccEquipReason_ClientRequest = 1,
HeroAccEquipReason_MgrInit = 2,
}
HeroRedDotInfoType = {
RedDotInfoTypeNone = 0,
RedDotNewGrowlineLevel = 1,
}
SystemUnlockID = {
SU_None = 0,
SU_System = 1,
SU_DepositExpansion = 2,
SU_DepositAddSlot = 3,
SU_GeneralSkill = 4,
SU_SeasonLvlBonusPercent = 5,
SU_SCAVReduceCD = 6,
SU_SCAVAddSlot = 7,
SU_AttrOperate = 8,
SU_SafehouseVisitor = 9,
SU_DailySupply = 10,
SU_AuctionTaxDiscount = 12,
SU_LoopEfficiency = 13,
SU_AuctionSlotCnt = 14,
SU_DepositRepair = 15,
SU_RoleLoad = 21,
SU_ShowRoom = 22,
}
GameSystemID = {
GameSystemID_None = 0,
GameSystemID_WeaponAssembly = 1,
GameSystemID_Deposit = 2,
GameSystemID_Talent = 3,
GameSystemID_Max = 4,
}
GeneralSkillID = {
GeneralSkillID_None = 0,
GeneralSkillID_Hack = 1,
GeneralSkillID_Unlock = 2,
GeneralSkillID_Max = 3,
}
QuestType = {
UnknownQuestType = 0,
Mission = 1,
Branch = 2,
Trader = 3,
Challenge = 4,
Activity = 5,
Team = 6,
Repeated = 7,
BattlePass = 8,
SeasonQuest = 9,
ImportantQuest = 10,
}
QuestObjectiveType = {
UnknownQuestObjectiveType = 0,
Submit = 1,
Currency = 2,
Gather = 3,
Escaped = 4,
Kill = 5,
UpgradeSafehouseDeviceLevel = 6,
UseSafehouseDeviceProduceProp = 7,
ExploreMapPosition = 8,
PlaceProp = 9,
DSRoomChallange = 10,
SubmitWeapon = 11,
DSGameInteractive = 12,
DSGameEvent = 13,
WorldQuestObjective = 14,
PlayerCG = 15,
CarryOutProps = 16,
DSUserProp = 17,
DSPlayerAttributeChange = 18,
DSPickUpProp = 19,
MallBuy = 20,
ReputationLevel = 21,
MandelBrickObjective = 22,
LootOpenBox = 23,
KeepActionTime = 24,
QAObjective = 25,
AuctionBuyProps = 26,
AuctionSellProps = 27,
SpecificExpertIntoDS = 28,
ObjectiveRepair = 29,
EquipSpecExtensionBox = 30,
EquipBodyProps = 31,
EscapeWithMates = 32,
ObjectiveArmedForceSkill = 33,
ObjectiveContract = 34,
CarryBodyDoSomething = 36,
Damage = 37,
Assist = 38,
UseRankSkill = 39,
Movement = 40,
BeenAtLocation = 41,
LootingItem = 42,
MPOccupySector = 43,
AmmoSupply = 44,
TimeLimitGoal = 45,
UseRebornFlag = 46,
CharcterChecking = 47,
Rescue = 48,
TotalProperty = 55,
}
QuestObjectiveKillType = {
UnknownType = 0,
Npc = 1,
Player = 2,
}
QuestRewardType = {
UnknownQuestRewardType = 0,
RewardProp = 1,
RewardExp = 2,
RewardCurrency = 3,
RewardQualityValue = 4,
RewardCollection = 5,
RewardHero = 6,
}
QuestState = {
Locked = 0,
Unread = 1,
Unaccepted = 2,
Accepted = 3,
Failed = 4,
Paused = 5,
Completed = 6,
Rewarded = 7,
Expired = 8,
}
ArmoryState = {
ArmoryAccepted = 0,
ArmoryFinish = 1,
ArmoryUnAccepted = 2,
ArmoryTakeReward = 3,
}
QuestSpecialType = {
UnknownQuestSpecialType = 0,
DSMatchChallenge = 1,
}
QuestClass = {
UnknownQuestClass = 0,
SOL = 1,
World = 2,
}
QuestToolType = {
UnknownQuestToolType = 0,
DeleteQuest = 1,
DumpQuestDB = 2,
ForceAccept = 3,
ForceFinish = 4,
ForceSetObjectiveValue = 5,
ForceFinishAllAcceptQuests = 6,
ForceAcceptAndFinishAll = 7,
ForceSetQuestVar = 8,
ForceSetQuestSpentSeconds = 9,
ForceFailQuest = 10,
}
OrderState = {
OrderNone = 0,
OrderRacked = 1,
OrderExpire = 5,
OrderPullOff = 2,
OrderSold = 3,
OrderBuy = 4,
OrderNotFoundInSQL = 6,
}
CSCheapBuyScene = {
CheapBuyScene_None = 0,
CheapBuyScene_SystemBuy = 1,
CheapBuyScene_Auction = 101,
CheapBuyScene_Gunsmith = 201,
CheapBuyScene_Safehouse = 301,
CheapBuyScene_Mall = 401,
CheapBuyScene_PresetRecommend = 501,
CheapBuyScene_PresetEquipment = 502,
CheapBuyScene_PresetBullet = 503,
CheapBuyScene_PresetDrug = 504,
CheapBuyScene_Activity = 601,
CheapBuyScene_Match = 701,
}
FriendType = {
Self = 0,
OpenFriend = 1,
GameFriend = 2,
OpenGameFriend = 3,
}
FriendApplySource = {
InvalidApply = 0,
Face2FaceApply = 1,
SystemApply = 2,
TeamApply = 3,
SearchApply = 4,
NearbyApply = 5,
WorldChatApply = 6,
PrivateChatApply = 7,
RecentPlayApply = 8,
SolSettlementApply = 9,
MpSettlementApply = 10,
HistoricalReultApply = 11,
PlayerInformationApply = 12,
SettlementRecommendation = 13,
TeamRelationshipApply = 14,
OtherApply = 99,
}
TeamInviteSource = {
FromAll = 0,
FromFriend = 1,
FromRecent = 2,
FromNeighbor = 3,
FromFace2Face = 4,
FromWorldChat = 5,
FromOther = 999,
}
FavChangeReason = {
FavChangeInvalid = 0,
FavChangeDailyGift = 1,
FavChangePlayGame = 2,
FavChangePraise = 3,
}
WorldChatRoomState = {
WorldChatRoomInvalid = 0,
WorldChatRoomNormal = 1,
WorldChatRoomBanJoin = 2,
WorldChatRoomToDestroy = 3,
WorldChatRoomDisable = 4,
}
WorldChatRoomType = {
ChatRoomTypeNormal = 0,
ChatRoomTypeCollege = 1,
}
WorldChatMemberType = {
ChatMemberTypeNormal = 0,
ChatMemberTypeTemp = 1,
ChatMemberTypeGMFake = 2,
}
SafehouseProduceType = {
Once = 0,
Loop = 1,
}
SafehouseDailySupplyStatus = {
NotUnlock = 0,
Existing = 1,
Received = 2,
}
RaidPlayerResultType = {
kTaskSucc = 0,
kTaskFailed = 1,
kRaidPlayerMissing = 2,
}
TDMPlayerResultType = {
kTDMVictory = 0,
kTDMFailed = 1,
kTDMPlayerMissing = 2,
kTDMTie = 3,
}
DSRoomEvent = {
DSRoomEventNone = 0,
ReceiveBeginMatchReq = 1,
AllocateDS = 2,
GetDSPort = 3,
CreateDSStartArgs = 4,
AllocatorAllocate = 5,
GetDSPublicIPPort = 6,
InsertDSPool = 7,
ChecAllocateArgs = 8,
ForkDSFather = 9,
UseExecAllocator = 10,
ForkDSChild = 11,
StartDSProcess = 12,
DelDSFromDB = 13,
FoundUsedLargeMemoryDS = 14,
PurgeExitedDS = 15,
KillUsedLargeMemoryDS = 16,
NotifyPlayerDSPurged = 17,
DeletePlayerCacheData = 18,
SendMatchEndNtf = 19,
ReleaseInnerPort = 20,
ReleaseOuterPort = 21,
FoundTimeoutDS = 22,
FoundOverTimeDS = 23,
FoundNoHeartbeatDS = 24,
FoundInactiveDSFather = 25,
DeleteDSFatherFromLocalDB = 26,
KillDSFather = 27,
ForceAbortDSFather = 28,
PuregeInactiveDS = 29,
DupDSFather = 30,
NumeralSyncToDSA = 31,
GenerateDSRoomID = 100,
GetIDCAgentFromIDCLBSvr = 101,
ForwardBeginMatchToIDCAgentSvr = 102,
ForwardBeginMatchToDSAgentSvr = 103,
AllocateIDCAgentSvr = 104,
AllocateDSAgentSvr = 105,
QueryRecommendIDCList = 106,
DSSendBeginMatchRes = 200,
ReceiveSettlementNtf = 201,
DsgateGrayMatch = 202,
IDCAgentGrayMatch = 203,
}
RttTimeResult = {
RttTime_OK = 0,
RttTime_Timeout = 1,
RttTime_Exceed_Limit = 2,
RttTime_Client_Ping_Fail = 3,
}
SwitchModuleID = {
ModuleNone = 0,
ModuleSafehouse = 1000,
ModuleSafehouseMatFill = 1001,
ModuleSafehouseProduce = 1002,
ModuleSafehouseUpgrade = 1003,
ModuleAuctionBuy = 2000,
ModuleAuctionSell = 2001,
ModuleArmedLow = 3000,
ModuleArmedMiddle = 3001,
ModuleArmedHigh = 3002,
ModuleRecommendPreset = 3003,
ModuleABTLobbyMP = 4000,
ModuleMallBattleStore = 5000,
ModuleMallHealStore = 5001,
ModuleMallSupportStore = 5002,
ModuleMallTacticsStore = 5003,
ModuleMallResearchStore = 5004,
ModuleRankSOL = 6000,
ModuleScoreMP = 6001,
ModuleDailyTaskSOL = 7000,
ModuleDailyTaskMP = 7001,
ModuleBattlePass = 8000,
ModuleShop = 9000,
ModuleMarket = 10000,
ModulePromptModeLobby = 11000,
ModulePromptSOL = 11001,
ModulePromptMP = 11002,
ModuleMPAttackDefend = 12000,
ModuleMPOccupy = 12001,
ModuleMPOccupyQuick = 12002,
ModuleWorldChat = 14000,
}
SwitchConditionType = {
ConditionNone = 0,
ConditionLevel = 1,
ConditionAccountLevel = 2,
ConditionQuest = 3,
}
SwitchSystemID = {
SwitchSystemNone = 0,
SwitchSyetemActivity = 10000,
SwitchSyetemStore = 11000,
SwitchSyetemBattlePass = 12000,
SwitchSyetemTssReport = 13000,
SwitchSyetemSocialShare = 14000,
SwitchSyetemLobbyEntranceSOL = 15000,
SwitchSyetemLobbyEntranceMP = 15001,
SwitchSystemBHD = 15002,
SwitchSystemFacePop = 16000,
SwitchSystemSOLRoom = 17000,
SwitchSystemMPRoom = 17001,
SwitchSystemHeroLobby = 18000,
SwitchSystemMarket = 19000,
SwitchSystemLive = 20000,
SwitchSystemCustomerService = 21000,
SwitchSystemMapBoardSnake = 22000,
SwitchSystemMapBoardValkyr = 23000,
SwitchSystemScanCodeLogin = 24000,
SwitchSandBoxMapPoint1 = 25001,
SwitchSandBoxMapPoint2 = 25002,
SwitchSandBoxMapPoint3 = 25003,
SwitchSandBoxMapPoint4 = 25004,
SwitchSandBoxMapPoint5 = 25005,
SwitchSandBoxMapPoint6 = 25006,
SwitchSandBoxMapPoint7 = 25007,
SwitchSystemOpActWeChatWelfare = 26001,
SwitchSystemOpActWeChatPrivilege = 26002,
SwitchSystemOpActWeChatVideoChannel = 26003,
SwitchSystemOpActQQChannel = 26004,
SwitchSystemOpActQQPrivilege = 26005,
SwitchSystemOpActQQBonusCenter = 26006,
SwitchSystemOpActQQGameHub = 26007,
SwitchSystemOpActXYClub = 26008,
SwitchSystemOpActMinorProtection = 26009,
SwitchSystemOpActWeChatGameHub = 26010,
SwitchSystemMicroOfficialWeb = 27001,
SwitchSystemOpenFriend = 28001,
SwitchSystemLiveRadio = 29001,
SwitchSystemCollection = 30001,
SwitchSystemWeaponAssembly = 31000,
SwitchSystemVehicle = 32000,
SwitchSystemReputationScore = 33000,
SwitchSystemOpActPerks = 34000,
SwitchSystemAgeRestrictionSign = 35000,
SwitchSystemRankingList = 36000,
SwitchSystemAIData = 37000,
SwitchSystemGarenaAccountBind = 38000,
SwitchSystemSecretServiceSubscription = 39000,
SubShareScreenshot = ********,
SubShareBasicInformation = ********,
SubShareHistoricalResultsList = ********,
SubShareObtainOperator = ********,
SubShareGetOperatorSkin = ********,
SubShareOperatorSkinAssets = ********,
SubShareGetMeleeWeapons = ********,
SubShareMeleeWeaponAssets = ********,
SubShareObtainMetaphysicalSkin = ********,
SubShareMetaphysicalGunSkin = ********,
SubShareAdvancedGunSkin = ********,
SubShareAdvancedGunSkinAssets = ********,
SubShareSObtainLowGunSkin = ********,
SubShareLowGunSkinAssets = ********,
SubShareSOLSettlementRecord = ********,
SubShareSOLSettlementHighlightFace = ********,
SubShareMPSettlementPersonalTeam = ********,
SubShareQQDirectionTeam = ********,
SubShareQQNonDirectionTeam = ********,
SubShareWXDirectionTeam = ********,
SubShareWXNonDirectionTeam = ********,
SubShareHangingOrnaments = ********,
SwitchSystemBHDSolo = ********,
MapBoardSnakePointForest = ********,
MapBoardSnakePointDam = 22000002,
MapBoardSnakePointAerospace = 22000003,
MapBoardSnakePointCoda = 22000004,
MapBoardSnakePointBuck = 22000005,
MapBoardSnakePointVal = 22000006,
QQPrivilegeSettlementBonus = 26005001,
QQPrivilegePersonalHomepageDisplay = 26005002,
QQPrivilegeFriendsInterfaceDisplay = 26005003,
QQPrivilegeRankingListDisplay = 26005004,
WeChatPrivilegeSettlementBonus = 26002001,
WeChatPrivilegePersonalHomepageDisplay = 26002002,
WeChatPrivilegeFriendsInterfaceDisplay = 26002003,
WeChatPrivilegeRankingListDisplay = 26002004,
SwitchSystemWeaponAssemblySkin = 31000001,
SwitchSystemVehicleSkin = 32000002,
}
TssContentCategory = {
TssContentCategoryDefault = 0,
Meteriral = 100,
GunPreset = 101,
TssRegister = 102,
SettingRecord = 103,
RenameCard = 104,
MPBagName = 105,
RoomName = 106,
OutfitName = 107,
MysticalName = 108,
AvatarPicCheck = 109,
NonAvatarPicCheck = 110,
PreCreatedRole = 111,
Message = 1000,
MessageWorld = 1001,
MessagePrivate = 1002,
MessageTeam = 1003,
MessageBattle = 1004,
TeamInviteReject = 1005,
MossVoice = 1007,
MossAIChatContent = 1008,
CampaignSlogan = 1009,
RoomTeamName = 1010,
Social = 2000,
FriendSearch = 4004,
}
MysteryMallPlayerTag = {
Freshman = 0,
Stable = 1,
BigLose = 2,
}
MysteryMallPlayerFortune = {
Little = 0,
Small = 1,
Large = 2,
Great = 3,
}
UpdatePlayerBalanceReason = {
UpdateBalanceDefault = 0,
UpdateBalanceRechargeCurrency = 1,
}
VehicleUnlockID = {
VU_None = 0,
VU_TDM_Level = 1,
VU_Activity = 2,
VU_Armory = 3,
}
VehiclePartUnlockID = {
VP_None = 0,
VP_TDM_Level = 1,
VP_Vehicle_Level = 2,
}
EnumTSSCreditScnLimitType = {
Chat = 0,
AddFriend = 1,
ModifyPersonalInfo = 2,
TeamOperation = 3,
}
EnumAccountRewardType = {
Switch = 0,
InventoryItem = 1,
UnlockMPGun = 2,
}
MPDropLimitType = {
TypeNone = 0,
MandelBrick = 161100,
MandelBrickIron = 161101,
}
RankListType = {
Rank_Invalid = 0,
Rank_Sol_Global = 1,
Rank_Mp_Global = 2,
Rank_Test_Global = 3,
Rank_Raid_Play_Time = 4,
Rank_Bhd_Play_Time = 5,
Rank_Bhd_Play_Time_SPEC = 6,
Rank_Sol_Friends = 1001,
Rank_Mp_Friends = 1002,
Rank_Test_Friends = 1003,
}
CollectionRightsType = {
Rights_Invalid = 0,
Rights_AuctionSellList = 1001,
Rights_HighRepair = 2001,
Rights_VIPExtensionBox = 3001,
}
CollectionFunctionalType = {
CollectionFunctional_Invalid = 0,
CollectionFunctional_MPExp = 11,
CollectionFunctional_MPWeapon = 12,
CollectionFunctional_MPScore = 13,
CollectionFunctional_MPScore_Protect = 14,
}
CollectionBattleConsumeReason = {
BattleConsumeReason_Invalid = 0,
BattleConsumeReason_UseProp = 1,
BattleConsumeReason_Numeral = 2,
BattleConsumeReason_RollBack = 3,
}
PrivacySetting = {
Privacy_None = 0,
Privacy_HistoryVisibility = 1,
Privacy_WarZoneVisibility = 2,
Privacy_LocationVisibility = 3,
Privacy_AllowFriendsToSpectate = 4,
Privacy_AllowRankingPrivacy = 5,
Privacy_AllowPrivateChatStrangers = 6,
Privacy_AllowStrangerFriendApplication = 7,
Privacy_FriendInvisibleLogin = 8,
}
StuPrivType = {
StuPriv_None = 0,
StuPriv_Certified = 1,
StuPriv_Certifying = 2,
StuPriv_NotCertified = 3,
}
ReturningUserTag = {
TAG_NONE = 0,
TAG_FIRST_SOL = 1,
TAG_FIRST_TDM = 2,
TAG_SECOND_SOL = 3,
TAG_SECOND_TDM = 4,
}
IOSReviewPopUpType = {
PopUP_None = 0,
PopUP_SOL_Price = 1,
PopUP_SOL_Rank = 2,
PopUP_MP_LEVEL = 3,
PopUP_MP_Mandle_Brick = 4,
PopUP_Mandle_Brick_Prize = 5,
}
LimitSocialTypeDef = {
NONE = 0,
DURATION = 1,
PERIOD = 2,
WEEKLYRESET = 3,
}
ExtraLoginMethod = {
by_no_care = 0,
by_steam = 1,
by_epic = 2,
}
MapBoardStartType = {
PERMANENTLY = 0,
TIMECONFIGURED = 1,
CYCLICALLY = 2,
}
TcaConst = {
Unknown = 0,
MaxBatchOpSize = 1024,
}
ReputationLevelType = {
DEFAULT = 0,
LOW = 1,
MEDIUM_LOW = 2,
MEDIUM = 3,
MEDIUM_HIGH = 4,
HIGH = 5,
}
ReputationStarType = {
NONE_STAR = 0,
ONE_STAR = 1,
TWO_STAR = 2,
THREE_STAR = 3,
FOUR_STAR = 4,
FIVE_STAR = 5,
}
PrivBonusType = {
PRIVNONE = 0,
NETBAR = 1,
STUPRIV = 2,
}
SkinTaskMode = {
SKIN_TASK_MODE_NONE = 0,
SKIN_TASK_MODE_SOL = 1,
SKIN_TASK_MODE_BF = 2,
}
AccountLoginReason = {
LoginDefault = 0,
LoginReconnect = 1,
}
RecoveryAmountEventID = {
Recovery_None = 0,
Recovery_Idip_Set = 1,
Recovery_Acitve = 2,
Recovery_Passive = 3,
}
RankBoardHandbookState = {
STATE_NONE = 0,
STATE_COPPER = 1,
STATE_SLIVER = 2,
STATE_GOLD = 3,
}
RankDataType = {
RANK_TYPE_UNKNOWN = 0,
SOL_SCORE = 1001,
SOL_BRINGOUT_VALUE = 1002,
SOL_KILL = 1003,
TDM_SCORE = 2001,
TDM_KILL = 2002,
TDM_CAPTURE_ZONE_POINT = 2003,
TDM_RESCUE_POINT = 2004,
TDM_VICTORY_UNIT_SCORE = 2005,
TDM_VICTORY_UNIT_COMMAND_SCORE = 2101,
TDM_VICTORY_UNIT_INFANTRY_KILL_SCORE = 2102,
TDM_VICTORY_UNIT_VEHICLE_KILL_SCORE = 2103,
TDM_VICTORY_UNIT_INFANTRY_RESCUE_SCORE = 2104,
RAID_PLAY_TIME = 3001,
BHD_PLAY_TIME = 3002,
BHD_PLAY_TIME_REVERSE = 3003,
ACTIVITY_SCORE = 3004,
ACTIVITY_SCORE_SHE2 = 3005,
RANK_TYPE_MAX = 5000,
}
RankBoardRegion = {
REGION_NONE = 0,
REGION_ALL = 1,
REGION_PREVINCE = 2,
REGION_CITY = 3,
REGION_COUNTY = 4,
}
OpenRankType = {
OPEN_RANK_TYPE_NONE = 0,
OPEN_RANK_TYPE_PREVINCE = 1,
OPEN_RANK_TYPE_CITY = 2,
OPEN_RANK_TYPE_COUNTY = 3,
}
RankCycleType = {
CYCLE_TYPE_NONE = 0,
CYCLE_TYPE_DAY = 1,
CYCLE_TYPE_WEEK = 2,
CYCLE_TYPE_MONTH = 3,
}
RankScoreCalcType = {
CALC_TYPE_NONE = 0,
CALC_TYPE_REAL_TIME_VALUE = 1,
CALC_TYPE_TOP_ROUND = 2,
CALC_TYPE_TOP_TIME = 3,
CALC_TYPE_SORT_OVERRITE = 4,
CALC_TYPE_LAST_N_DAY = 5,
}
RankBoardType = {
Unknown_Rank = 0,
General_Rank = 1,
Fuzzy_Rank = 2,
}
RankSortType = {
SORT_TYPE_NONE = 0,
SORT_TYPE_SEQUENCE = 1,
SORT_TYPE_REVERSE = 2,
}
RankPlatType = {
RANK_NONE = 0,
RANK_MOBILE = 100,
RANK_PC = 102,
}
ShowCabinetType = {
Show_Cabinet_None = 0,
Show_Cabinet_Display = 1,
Show_Cabinet_Special = 2,
Show_Cabinet_DIY = 3,
}
RoomParamType = {
Param_Type_None = 0,
Param_Type_Match_Sequence = 1,
Param_Type_Boss_Spawn = 2,
Param_Type_Match_Player_Num = 4,
Param_Type_Event_ID = 5,
Param_Type_Is_Open_Match_Retreat = 8,
Param_Type_Match_Time_Scale = 9,
Param_Type_Room_Info_Show = 10,
Param_Type_Hero_BP = 11,
Param_Type_Spawn_Point_Type = 12,
Param_Type_Manderal_Brick_Reward = 13,
Param_Type_Match_Mid_Supply = 14,
Param_Type_Mander_Brick_Win_Num = 15,
Param_Type_Match_Fail_SUPPLY = 16,
Param_Type_Match_Fail_Kill_REWARD = 17,
Param_Type_Match_Kill_REWARD = 18,
Param_Type_Is_Open_Mid_Supply = 20,
}
ActivityPropCostSource = {
ActivityPropCostSourceInvalid = 0,
ActivityPropCostSourceAuction = 1,
}
SecSocialOperationMode = {
SecSocialOperationModeInvalid = 0,
SecSocialOperationModeApplyJoinTeam = 1,
SecSocialOperationModeInviteJoinTeam = 2,
SecSocialOperationModeQuickJoinTeam = 3,
SecSocialOperationModePostRecruitment = 4,
}
pb.__pb_Empty = {
}
pb.__pb_Empty.__name = "Empty"
pb.__pb_Empty.__index = pb.__pb_Empty
pb.__pb_Empty.__pairs = __pb_pairs

pb.Empty = { __name = "Empty", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Empty : ProtoBase

---@return pb_Empty
function pb.Empty:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_IsolationInfo = {
    type = 0,
    duration = 0,
    start_time = 0,
}
pb.__pb_IsolationInfo.__name = "IsolationInfo"
pb.__pb_IsolationInfo.__index = pb.__pb_IsolationInfo
pb.__pb_IsolationInfo.__pairs = __pb_pairs

pb.IsolationInfo = { __name = "IsolationInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_IsolationInfo : ProtoBase
---@field public type number
---@field public duration number
---@field public start_time number

---@return pb_IsolationInfo
function pb.IsolationInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSClientInfo = {
    system_sofrware = "",
    system_hardward = "",
    telecom_oper = "",
    network = "",
    screen_width = 0,
    screen_height = 0,
    denisty = 0,
    login_channel = 0,
    cpu_hardware = "",
    memory = 0,
    gl_render = "",
    gl_version = "",
    device_id = "",
    client_ip = "",
    client_ip_v6 = "",
    equipment_type = 0,
    build_info = "",
    appsflyer_device_id = "",
    oaid = "",
    xid = "",
    caid = "",
    hotfix = "",
    client_version = "",
    match_client_type = 0,
    cpu_brand = "",
    cpu_chipset = "",
    user_agent = "",
    system_language = "",
    game_language = "",
    game_center = 0,
    core_user_id = "",
    mobile_network = "",
    vendor_id = "",
    device_imei = "",
    idfv = "",
}
pb.__pb_CSClientInfo.__name = "CSClientInfo"
pb.__pb_CSClientInfo.__index = pb.__pb_CSClientInfo
pb.__pb_CSClientInfo.__pairs = __pb_pairs

pb.CSClientInfo = { __name = "CSClientInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSClientInfo : ProtoBase
---@field public system_sofrware string
---@field public system_hardward string
---@field public telecom_oper string
---@field public network string
---@field public screen_width number
---@field public screen_height number
---@field public denisty number
---@field public login_channel number
---@field public cpu_hardware string
---@field public memory number
---@field public gl_render string
---@field public gl_version string
---@field public device_id string
---@field public client_ip string
---@field public client_ip_v6 string
---@field public equipment_type number
---@field public build_info string
---@field public appsflyer_device_id string
---@field public oaid string
---@field public xid string
---@field public caid string
---@field public hotfix string
---@field public client_version string
---@field public match_client_type number
---@field public cpu_brand string
---@field public cpu_chipset string
---@field public user_agent string
---@field public system_language string
---@field public game_language string
---@field public game_center number
---@field public core_user_id string
---@field public mobile_network string
---@field public vendor_id string
---@field public device_imei string
---@field public idfv string

---@return pb_CSClientInfo
function pb.CSClientInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ClientIntlInfo = {
    channel_id = 0,
    sdk_version = "",
    token = "",
    intl_country_belonging = 0,
}
pb.__pb_ClientIntlInfo.__name = "ClientIntlInfo"
pb.__pb_ClientIntlInfo.__index = pb.__pb_ClientIntlInfo
pb.__pb_ClientIntlInfo.__pairs = __pb_pairs

pb.ClientIntlInfo = { __name = "ClientIntlInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ClientIntlInfo : ProtoBase
---@field public channel_id number
---@field public sdk_version string
---@field public token string
---@field public intl_country_belonging number

---@return pb_ClientIntlInfo
function pb.ClientIntlInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_IntlParentControlInfo = {
    enable = false,
    weekday_time_start = "",
    weekday_time_end = "",
    weekday_time_limit = 0,
    weekend_time_start = "",
    weekend_time_end = "",
    weekend_time_limit = 0,
}
pb.__pb_IntlParentControlInfo.__name = "IntlParentControlInfo"
pb.__pb_IntlParentControlInfo.__index = pb.__pb_IntlParentControlInfo
pb.__pb_IntlParentControlInfo.__pairs = __pb_pairs

pb.IntlParentControlInfo = { __name = "IntlParentControlInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_IntlParentControlInfo : ProtoBase
---@field public enable boolean
---@field public weekday_time_start string
---@field public weekday_time_end string
---@field public weekday_time_limit number
---@field public weekend_time_start string
---@field public weekend_time_end string
---@field public weekend_time_limit number

---@return pb_IntlParentControlInfo
function pb.IntlParentControlInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ClientGwalletInfo = {
    req_from = 0,
    user_ip = "",
    language_code = "",
    user_access_token = "",
}
pb.__pb_ClientGwalletInfo.__name = "ClientGwalletInfo"
pb.__pb_ClientGwalletInfo.__index = pb.__pb_ClientGwalletInfo
pb.__pb_ClientGwalletInfo.__pairs = __pb_pairs

pb.ClientGwalletInfo = { __name = "ClientGwalletInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ClientGwalletInfo : ProtoBase
---@field public req_from number
---@field public user_ip string
---@field public language_code string
---@field public user_access_token string

---@return pb_ClientGwalletInfo
function pb.ClientGwalletInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_HostInfo = {
    ds_domain = "",
}
pb.__pb_HostInfo.__name = "HostInfo"
pb.__pb_HostInfo.__index = pb.__pb_HostInfo
pb.__pb_HostInfo.__pairs = __pb_pairs

pb.HostInfo = { __name = "HostInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_HostInfo : ProtoBase
---@field public ds_domain string
---@field public ds_ip_info pb_DsIpInfo[]

---@return pb_HostInfo
function pb.HostInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsIpInfo = {
    ds_textual_ip = "",
    ds_port = 0,
}
pb.__pb_DsIpInfo.__name = "DsIpInfo"
pb.__pb_DsIpInfo.__index = pb.__pb_DsIpInfo
pb.__pb_DsIpInfo.__pairs = __pb_pairs

pb.DsIpInfo = { __name = "DsIpInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsIpInfo : ProtoBase
---@field public ds_textual_ip string
---@field public ds_port number

---@return pb_DsIpInfo
function pb.DsIpInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsHostInfo = {
    ds_ip = 0,
    ds_port = 0,
    ds_isp = 0,
}
pb.__pb_DsHostInfo.__name = "DsHostInfo"
pb.__pb_DsHostInfo.__index = pb.__pb_DsHostInfo
pb.__pb_DsHostInfo.__pairs = __pb_pairs

pb.DsHostInfo = { __name = "DsHostInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsHostInfo : ProtoBase
---@field public ds_ip number
---@field public ds_port number
---@field public ds_isp number

---@return pb_DsHostInfo
function pb.DsHostInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PropGridPage = {
    grid_page_id = 0,
    tag = 0,
    grid_length = 0,
    grid_width = 0,
    create_time = 0,
}
pb.__pb_PropGridPage.__name = "PropGridPage"
pb.__pb_PropGridPage.__index = pb.__pb_PropGridPage
pb.__pb_PropGridPage.__pairs = __pb_pairs

pb.PropGridPage = { __name = "PropGridPage", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PropGridPage : ProtoBase
---@field public grid_page_id number
---@field public tag number
---@field public grid_length number
---@field public grid_width number
---@field public create_time number
---@field public props pb_PropInfo[]
---@field public prop_types number[]

---@return pb_PropGridPage
function pb.PropGridPage:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_UsePropCmd = {
    prop_id = 0,
    prop_gid = 0,
    num = 0,
    target_id = 0,
    target_gid = 0,
}
pb.__pb_UsePropCmd.__name = "UsePropCmd"
pb.__pb_UsePropCmd.__index = pb.__pb_UsePropCmd
pb.__pb_UsePropCmd.__pairs = __pb_pairs

pb.UsePropCmd = { __name = "UsePropCmd", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_UsePropCmd : ProtoBase
---@field public prop_id number
---@field public prop_gid number
---@field public num number
---@field public target_id number
---@field public target_gid number

---@return pb_UsePropCmd
function pb.UsePropCmd:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CurrencyDesc = {
    id = 0,
    num = 0,
    last_update_time = 0,
    src_id = 0,
    src_num = 0,
}
pb.__pb_CurrencyDesc.__name = "CurrencyDesc"
pb.__pb_CurrencyDesc.__index = pb.__pb_CurrencyDesc
pb.__pb_CurrencyDesc.__pairs = __pb_pairs

pb.CurrencyDesc = { __name = "CurrencyDesc", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CurrencyDesc : ProtoBase
---@field public id number
---@field public num number
---@field public last_update_time number
---@field public src_id number
---@field public src_num number

---@return pb_CurrencyDesc
function pb.CurrencyDesc:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CurrencyInfo = {
    id = 0,
    num = 0,
    src_id = 0,
    src_num = 0,
    reason = 0,
}
pb.__pb_CurrencyInfo.__name = "CurrencyInfo"
pb.__pb_CurrencyInfo.__index = pb.__pb_CurrencyInfo
pb.__pb_CurrencyInfo.__pairs = __pb_pairs

pb.CurrencyInfo = { __name = "CurrencyInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CurrencyInfo : ProtoBase
---@field public id number
---@field public num number
---@field public src_id number
---@field public src_num number
---@field public reason number

---@return pb_CurrencyInfo
function pb.CurrencyInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PositionChange = {
    pos_id = 0,
    tag = 0,
    create_time = 0,
    change_type = 0,
    src_prop_id = 0,
}
pb.__pb_PositionChange.__name = "PositionChange"
pb.__pb_PositionChange.__index = pb.__pb_PositionChange
pb.__pb_PositionChange.__pairs = __pb_pairs

pb.PositionChange = { __name = "PositionChange", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PositionChange : ProtoBase
---@field public pos_id number
---@field public tag number
---@field public create_time number
---@field public space pb_GridSize[]
---@field public change_type number
---@field public src_prop_id number

---@return pb_PositionChange
function pb.PositionChange:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PropChange = {
    change_type = 0,
    channel = 0,
    delta = 0,
    exceed_percent = 0,
    MatchUUID = "",
    is_presented = false,
}
pb.__pb_PropChange.__name = "PropChange"
pb.__pb_PropChange.__index = pb.__pb_PropChange
pb.__pb_PropChange.__pairs = __pb_pairs

pb.PropChange = { __name = "PropChange", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PropChange : ProtoBase
---@field public change_type number
---@field public src pb_PropLocation
---@field public dest pb_PropLocation
---@field public prop pb_PropInfo
---@field public channel number
---@field public delta number
---@field public exceed_percent number
---@field public MatchUUID string
---@field public is_presented boolean

---@return pb_PropChange
function pb.PropChange:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CurrencyChange = {
    currency_id = 0,
    delta = 0,
    current_num = 0,
    src_id = 0,
    src_num = 0,
}
pb.__pb_CurrencyChange.__name = "CurrencyChange"
pb.__pb_CurrencyChange.__index = pb.__pb_CurrencyChange
pb.__pb_CurrencyChange.__pairs = __pb_pairs

pb.CurrencyChange = { __name = "CurrencyChange", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CurrencyChange : ProtoBase
---@field public currency_id number
---@field public delta number
---@field public current_num number
---@field public src_id number
---@field public src_num number

---@return pb_CurrencyChange
function pb.CurrencyChange:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DataChange = {
    reason = 0,
    source = 0,
    cur_extension_num = 0,
    max_extension_num = 0,
    upperlimit_extension_num = 0,
    mall_sell_subreason = "",
    room_id = 0,
    match_uuid = "",
    sub_reason = "",
}
pb.__pb_DataChange.__name = "DataChange"
pb.__pb_DataChange.__index = pb.__pb_DataChange
pb.__pb_DataChange.__pairs = __pb_pairs

pb.DataChange = { __name = "DataChange", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DataChange : ProtoBase
---@field public prop_changes pb_PropChange[]
---@field public currency_changes pb_CurrencyChange[]
---@field public reason number
---@field public source number
---@field public pos_changes pb_PositionChange[]
---@field public cur_extension_num number
---@field public max_extension_num number
---@field public upperlimit_extension_num number
---@field public weapon_skin_setup pb_WeaponSkinSetup[]
---@field public mall_sell_subreason string
---@field public room_id number
---@field public match_uuid string
---@field public recovery_amount_changes pb_RecoveryAmountChange[]
---@field public sub_reason string

---@return pb_DataChange
function pb.DataChange:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CompoundCommand = {
    compound_type = 0,
    root_id = 0,
    root_gid = 0,
    node_id = 0,
    node_gid = 0,
    slot = 0,
    target_id = 0,
    target_gid = 0,
    target_num = 0,
}
pb.__pb_CompoundCommand.__name = "CompoundCommand"
pb.__pb_CompoundCommand.__index = pb.__pb_CompoundCommand
pb.__pb_CompoundCommand.__pairs = __pb_pairs

pb.CompoundCommand = { __name = "CompoundCommand", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CompoundCommand : ProtoBase
---@field public compound_type number
---@field public root_id number
---@field public root_gid number
---@field public node_id number
---@field public node_gid number
---@field public slot number
---@field public target_id number
---@field public target_gid number
---@field public target_num number
---@field public styles pb_WeaponStyleInfo[]
---@field public loc pb_PropLocation
---@field public swap_gun pb_PropInfo
---@field public peer_swap_gun pb_PropInfo
---@field public update_attr_props pb_PropInfo[]

---@return pb_CompoundCommand
function pb.CompoundCommand:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DepositSortConfig = {
    sort_style = 0,
    sort_every_enter = false,
    has_sorted = false,
}
pb.__pb_DepositSortConfig.__name = "DepositSortConfig"
pb.__pb_DepositSortConfig.__index = pb.__pb_DepositSortConfig
pb.__pb_DepositSortConfig.__pairs = __pb_pairs

pb.DepositSortConfig = { __name = "DepositSortConfig", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DepositSortConfig : ProtoBase
---@field public sort_style number
---@field public sort_class_order number[]
---@field public extension_first_class number[]
---@field public sort_every_enter boolean
---@field public has_sorted boolean

---@return pb_DepositSortConfig
function pb.DepositSortConfig:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_WeaponApplySkinCmd = {
    weapon_id = 0,
    weapon_gid = 0,
    skin_id = 0,
    apply_all = false,
    bag_id = 0,
    skin_gid = 0,
    pendant_id = 0,
    pendant_apply_all = false,
    pendant_gid = 0,
}
pb.__pb_WeaponApplySkinCmd.__name = "WeaponApplySkinCmd"
pb.__pb_WeaponApplySkinCmd.__index = pb.__pb_WeaponApplySkinCmd
pb.__pb_WeaponApplySkinCmd.__pairs = __pb_pairs

pb.WeaponApplySkinCmd = { __name = "WeaponApplySkinCmd", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_WeaponApplySkinCmd : ProtoBase
---@field public weapon_id number
---@field public weapon_gid number
---@field public skin_id number
---@field public apply_all boolean
---@field public bag_id number
---@field public skin_gid number
---@field public pendant_id number
---@field public pendant_apply_all boolean
---@field public pendant_gid number

---@return pb_WeaponApplySkinCmd
function pb.WeaponApplySkinCmd:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ConfigVersionInfo = {
    ver = "",
    load_time = 0,
}
pb.__pb_ConfigVersionInfo.__name = "ConfigVersionInfo"
pb.__pb_ConfigVersionInfo.__index = pb.__pb_ConfigVersionInfo
pb.__pb_ConfigVersionInfo.__pairs = __pb_pairs

pb.ConfigVersionInfo = { __name = "ConfigVersionInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ConfigVersionInfo : ProtoBase
---@field public ver string
---@field public load_time number

---@return pb_ConfigVersionInfo
function pb.ConfigVersionInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MerchantGiftInfo = {
    intimacy_lvl = 0,
    gift_times = 0,
    gift_timestamp = 0,
}
pb.__pb_MerchantGiftInfo.__name = "MerchantGiftInfo"
pb.__pb_MerchantGiftInfo.__index = pb.__pb_MerchantGiftInfo
pb.__pb_MerchantGiftInfo.__pairs = __pb_pairs

pb.MerchantGiftInfo = { __name = "MerchantGiftInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MerchantGiftInfo : ProtoBase
---@field public intimacy_lvl number
---@field public gift_times number
---@field public gift_timestamp number

---@return pb_MerchantGiftInfo
function pb.MerchantGiftInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RepCondition = {
    rep_type = 0,
    rep_lvl = 0,
}
pb.__pb_RepCondition.__name = "RepCondition"
pb.__pb_RepCondition.__index = pb.__pb_RepCondition
pb.__pb_RepCondition.__pairs = __pb_pairs

pb.RepCondition = { __name = "RepCondition", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RepCondition : ProtoBase
---@field public rep_type number
---@field public rep_lvl number

---@return pb_RepCondition
function pb.RepCondition:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Merchant = {
    id = 0,
    vip_score = 0,
    vip_lvl = 0,
    intimacy = 0,
    intimacy_lvl = 0,
    today_intimacy = 0,
    intimacy_timestamp = 0,
}
pb.__pb_Merchant.__name = "Merchant"
pb.__pb_Merchant.__index = pb.__pb_Merchant
pb.__pb_Merchant.__pairs = __pb_pairs

pb.Merchant = { __name = "Merchant", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Merchant : ProtoBase
---@field public id number
---@field public vip_score number
---@field public vip_lvl number
---@field public intimacy number
---@field public intimacy_lvl number
---@field public today_intimacy number
---@field public intimacy_timestamp number
---@field public gifts pb_MallPropInfo[]
---@field public gift_info pb_MerchantGiftInfo
---@field public gm_gifts pb_MallPropInfo[]
---@field public unlock_conditions pb_RepCondition[]

---@return pb_Merchant
function pb.Merchant:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PropPrice = {
    money_type = 0,
    price = 0,
    gid = 0,
}
pb.__pb_PropPrice.__name = "PropPrice"
pb.__pb_PropPrice.__index = pb.__pb_PropPrice
pb.__pb_PropPrice.__pairs = __pb_pairs

pb.PropPrice = { __name = "PropPrice", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PropPrice : ProtoBase
---@field public money_type number
---@field public price number
---@field public gid number

---@return pb_PropPrice
function pb.PropPrice:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_OptPropPrice = {
    exchange_type = 0,
    exchange_id = 0,
}
pb.__pb_OptPropPrice.__name = "OptPropPrice"
pb.__pb_OptPropPrice.__index = pb.__pb_OptPropPrice
pb.__pb_OptPropPrice.__pairs = __pb_pairs

pb.OptPropPrice = { __name = "OptPropPrice", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_OptPropPrice : ProtoBase
---@field public prices pb_PropPrice[]
---@field public exchange_type number
---@field public exchange_id number

---@return pb_OptPropPrice
function pb.OptPropPrice:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CurrencyExchangeRule = {
    item_id = 0,
    ratio = 0,
}
pb.__pb_CurrencyExchangeRule.__name = "CurrencyExchangeRule"
pb.__pb_CurrencyExchangeRule.__index = pb.__pb_CurrencyExchangeRule
pb.__pb_CurrencyExchangeRule.__pairs = __pb_pairs

pb.CurrencyExchangeRule = { __name = "CurrencyExchangeRule", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CurrencyExchangeRule : ProtoBase
---@field public item_id number
---@field public ratio number

---@return pb_CurrencyExchangeRule
function pb.CurrencyExchangeRule:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CurrencyExchangeInfo = {
    currency_id = 0,
    item_id = 0,
    enable_exchg = false,
}
pb.__pb_CurrencyExchangeInfo.__name = "CurrencyExchangeInfo"
pb.__pb_CurrencyExchangeInfo.__index = pb.__pb_CurrencyExchangeInfo
pb.__pb_CurrencyExchangeInfo.__pairs = __pb_pairs

pb.CurrencyExchangeInfo = { __name = "CurrencyExchangeInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CurrencyExchangeInfo : ProtoBase
---@field public currency_id number
---@field public item_id number
---@field public enable_exchg boolean
---@field public rules pb_CurrencyExchangeRule[]

---@return pb_CurrencyExchangeInfo
function pb.CurrencyExchangeInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MallExchangeRule = {
    item_id = 0,
    ratio = 0,
}
pb.__pb_MallExchangeRule.__name = "MallExchangeRule"
pb.__pb_MallExchangeRule.__index = pb.__pb_MallExchangeRule
pb.__pb_MallExchangeRule.__pairs = __pb_pairs

pb.MallExchangeRule = { __name = "MallExchangeRule", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MallExchangeRule : ProtoBase
---@field public item_id number
---@field public ratio number

---@return pb_MallExchangeRule
function pb.MallExchangeRule:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MallExchangeInfo = {
    currency_id = 0,
    item_id = 0,
    enable_exchg = false,
}
pb.__pb_MallExchangeInfo.__name = "MallExchangeInfo"
pb.__pb_MallExchangeInfo.__index = pb.__pb_MallExchangeInfo
pb.__pb_MallExchangeInfo.__pairs = __pb_pairs

pb.MallExchangeInfo = { __name = "MallExchangeInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MallExchangeInfo : ProtoBase
---@field public currency_id number
---@field public item_id number
---@field public enable_exchg boolean
---@field public rules pb_MallExchangeRule[]

---@return pb_MallExchangeInfo
function pb.MallExchangeInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_AssembleInfo = {
    pos_guid = 0,
    id = 0,
    target_gid = 0,
    num = 0,
    target_id = 0,
    target_buy_gid = 0,
}
pb.__pb_AssembleInfo.__name = "AssembleInfo"
pb.__pb_AssembleInfo.__index = pb.__pb_AssembleInfo
pb.__pb_AssembleInfo.__pairs = __pb_pairs

pb.AssembleInfo = { __name = "AssembleInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_AssembleInfo : ProtoBase
---@field public pos_guid number
---@field public id number
---@field public target_gid number
---@field public num number
---@field public target_id number
---@field public target_buy_gid number

---@return pb_AssembleInfo
function pb.AssembleInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MallPropInfo = {
    exchange_id = 0,
    exchange_type = 0,
    merchant_id = 0,
    preset_id = 0,
    unlock_condition = "",
    order = 0,
    for_sale = false,
    is_panic_buying = false,
    purchase_limit = 0,
    label_no1 = 0,
    label_no2 = 0,
    zero_dur_recycle_price = 0,
    unlock_lv = 0,
    exchange_num = 0,
    cannot_recycle = false,
    mall_recycle_price = 0,
    zero_dur_mall_recycle_price = 0,
    step_size = 0,
    auto_sort_on_cheapbuy = false,
    unlock_quest_id = 0,
    recycle_price_percent = 0,
    limit_time = 0,
    start_limit_unixtime = 0,
    end_limit_unixtime = 0,
    isitbound = "",
    isitbound_val = 0,
    start_limit_unix = 0,
    end_limit_unix = 0,
    unlock_round = 0,
}
pb.__pb_MallPropInfo.__name = "MallPropInfo"
pb.__pb_MallPropInfo.__index = pb.__pb_MallPropInfo
pb.__pb_MallPropInfo.__pairs = __pb_pairs

pb.MallPropInfo = { __name = "MallPropInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MallPropInfo : ProtoBase
---@field public prop_info pb_PropInfo
---@field public exchange_id number
---@field public exchange_type number
---@field public merchant_id number
---@field public preset_id number
---@field public prices pb_PropPrice[]
---@field public unlock_condition string
---@field public order number
---@field public for_sale boolean
---@field public is_panic_buying boolean
---@field public purchase_limit number
---@field public label_no1 number
---@field public label_no2 number
---@field public zero_dur_recycle_price number
---@field public unlock_lv number
---@field public exchange_num number
---@field public cannot_recycle boolean
---@field public mall_recycle_price number
---@field public zero_dur_mall_recycle_price number
---@field public assemble_info pb_AssembleInfo
---@field public step_size number
---@field public auto_sort_on_cheapbuy boolean
---@field public unlock_quest_id number
---@field public recycle_price_percent number
---@field public limit_time number
---@field public start_limit_unixtime number
---@field public end_limit_unixtime number
---@field public isitbound string
---@field public isitbound_val number
---@field public start_limit_unix number
---@field public end_limit_unix number
---@field public unlock_round number

---@return pb_MallPropInfo
function pb.MallPropInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GreatEarnGoods = {
    item_id = 0,
    group_num = 0,
    num = 0,
}
pb.__pb_GreatEarnGoods.__name = "GreatEarnGoods"
pb.__pb_GreatEarnGoods.__index = pb.__pb_GreatEarnGoods
pb.__pb_GreatEarnGoods.__pairs = __pb_pairs

pb.GreatEarnGoods = { __name = "GreatEarnGoods", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GreatEarnGoods : ProtoBase
---@field public item_id number
---@field public group_num number
---@field public num number

---@return pb_GreatEarnGoods
function pb.GreatEarnGoods:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MallMysteryShopItem = {
    exchange_num = 0,
    bought = false,
    iden = "",
    item_id = 0,
    price = 0,
}
pb.__pb_MallMysteryShopItem.__name = "MallMysteryShopItem"
pb.__pb_MallMysteryShopItem.__index = pb.__pb_MallMysteryShopItem
pb.__pb_MallMysteryShopItem.__pairs = __pb_pairs

pb.MallMysteryShopItem = { __name = "MallMysteryShopItem", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MallMysteryShopItem : ProtoBase
---@field public item pb_PropInfo
---@field public exchanged_props pb_PropInfo[]
---@field public exchange_num number
---@field public bought boolean
---@field public iden string
---@field public item_id number
---@field public price number

---@return pb_MallMysteryShopItem
function pb.MallMysteryShopItem:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MallPropGridPage = {
    slot_sub_id = "",
}
pb.__pb_MallPropGridPage.__name = "MallPropGridPage"
pb.__pb_MallPropGridPage.__index = pb.__pb_MallPropGridPage
pb.__pb_MallPropGridPage.__pairs = __pb_pairs

pb.MallPropGridPage = { __name = "MallPropGridPage", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MallPropGridPage : ProtoBase
---@field public slot_sub_id string
---@field public props pb_MallPropInfo[]

---@return pb_MallPropGridPage
function pb.MallPropGridPage:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MallLabelProps = {
    label_id = 0,
}
pb.__pb_MallLabelProps.__name = "MallLabelProps"
pb.__pb_MallLabelProps.__index = pb.__pb_MallLabelProps
pb.__pb_MallLabelProps.__pairs = __pb_pairs

pb.MallLabelProps = { __name = "MallLabelProps", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MallLabelProps : ProtoBase
---@field public label_id number
---@field public props pb_MallPropInfo[]

---@return pb_MallLabelProps
function pb.MallLabelProps:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MerchantChange = {
    old_vip_level = 0,
    old_vip_score = 0,
    old_intimacy = 0,
    old_intimacy_level = 0,
}
pb.__pb_MerchantChange.__name = "MerchantChange"
pb.__pb_MerchantChange.__index = pb.__pb_MerchantChange
pb.__pb_MerchantChange.__pairs = __pb_pairs

pb.MerchantChange = { __name = "MerchantChange", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MerchantChange : ProtoBase
---@field public merchant pb_Merchant
---@field public old_vip_level number
---@field public old_vip_score number
---@field public old_intimacy number
---@field public old_intimacy_level number

---@return pb_MerchantChange
function pb.MerchantChange:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TradeChange = {
    trade_type = 0,
}
pb.__pb_TradeChange.__name = "TradeChange"
pb.__pb_TradeChange.__index = pb.__pb_TradeChange
pb.__pb_TradeChange.__pairs = __pb_pairs

pb.TradeChange = { __name = "TradeChange", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TradeChange : ProtoBase
---@field public trade_type number
---@field public add_props pb_MallPropInfo[]
---@field public del_props pb_MallPropInfo[]

---@return pb_TradeChange
function pb.TradeChange:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PlayerLimitMallItem = {
    exchange_id = 0,
    id = 0,
    recycle_num_daily = 0,
    first_buy_time = 0,
    curr_buy_limit = 0,
    next_period_start = 0,
    limit_type = 0,
    buy_num = 0,
}
pb.__pb_PlayerLimitMallItem.__name = "PlayerLimitMallItem"
pb.__pb_PlayerLimitMallItem.__index = pb.__pb_PlayerLimitMallItem
pb.__pb_PlayerLimitMallItem.__pairs = __pb_pairs

pb.PlayerLimitMallItem = { __name = "PlayerLimitMallItem", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PlayerLimitMallItem : ProtoBase
---@field public exchange_id number
---@field public id number
---@field public recycle_num_daily number
---@field public first_buy_time number
---@field public curr_buy_limit number
---@field public next_period_start number
---@field public limit_type number
---@field public buy_num number

---@return pb_PlayerLimitMallItem
function pb.PlayerLimitMallItem:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ProfileSeasonInfo = {
    level = 0,
    exp = 0,
    next_level_exp = 0,
    serial = 0,
    total_exp = 0,
    last_update_time = 0,
    boon_exp_remain = 0,
    day_exp = 0,
    bonus_percent = 0,
    total_price = 0,
    avg_survival_time = 0,
    max_survival_time = 0,
    max_collection_price = 0,
    settled = false,
    kd = 0,
    damage = 0,
    collection_price = 0,
    support = 0,
    winning_percentage = 0,
    total_game_time = 0,
    total_fight = 0,
    total_escape = 0,
    max_escape_streak = 0,
    cur_escape_streak = 0,
    total_fracture = 0,
    total_bleed = 0,
    total_pain = 0,
    total_food_consumed = 0,
    total_drug_consumed = 0,
    total_healing = 0,
    max_healing = 0,
    avg_healing = 0,
    total_resurrect = 0,
    max_resurrect = 0,
    avg_resurrect = 0,
    total_death = 0,
    total_missing = 0,
    total_move_distance = 0,
    max_move_distance = 0,
    avg_move_distance = 0,
    max_jump = 0,
    total_walk_distance = 0,
    max_walk_distance = 0,
    avg_walk_distance = 0,
    total_loot_corpse = 0,
    max_loot_corpse = 0,
    avg_loot_corpse = 0,
    total_strongbox_open = 0,
    max_strongbox_open = 0,
    avg_strongbox_open = 0,
    total_unlock_door = 0,
    max_unlock_door = 0,
    avg_unlock_door = 0,
    total_game_egg = 0,
    max_game_egg = 0,
    avg_game_egg = 0,
    total_loot_container = 0,
    max_loot_container = 0,
    avg_loot_container = 0,
    total_kd = 0,
    max_kd = 0,
    avg_kd = 0,
    total_damage = 0,
    max_damage = 0,
    avg_damage = 0,
    total_shoot_down = 0,
    max_shoot_down = 0,
    avg_shoot_down = 0,
    total_kill = 0,
    max_kill = 0,
    avg_kill = 0,
    total_kill_alone = 0,
    max_kill_alone = 0,
    avg_kill_alone = 0,
    total_head_shot = 0,
    max_head_shot = 0,
    avg_head_shot = 0,
    total_hit = 0,
    max_hit = 0,
    avg_float = 0,
    max_hit_distance = 0,
    max_kill_distance = 0,
    total_kill_boss = 0,
    max_kill_boss = 0,
    avg_kill_boss = 0,
    total_kill_player = 0,
    max_kill_player = 0,
    avg_kill_player = 0,
    total_kill_robot = 0,
    max_kill_robot = 0,
    avg_kill_robot = 0,
    total_pistol_kill = 0,
    max_pistol_kill = 0,
    avg_pistol_kill = 0,
    total_melee_kill = 0,
    max_melee_kill = 0,
    avg_melee_kill = 0,
    total_sniper_gun_kill = 0,
    max_sniper_gun_kill = 0,
    avg_sniper_gun_kill = 0,
    total_shot_gun_kill = 0,
    max_shot_gun_kill = 0,
    avg_shot_gun_kill = 0,
    total_rifle_kill = 0,
    max_rifle_kill = 0,
    avg_rifle_kill = 0,
    total_machine_gun_kill = 0,
    max_machine_gun_kill = 0,
    avg_machine_gun_kill = 0,
    total_grenade_kill = 0,
    max_grenade_kill = 0,
    avg_grenade_kill = 0,
    total_carrier_kill = 0,
    max_carrier_kill = 0,
    avg_carrier_kill = 0,
}
pb.__pb_ProfileSeasonInfo.__name = "ProfileSeasonInfo"
pb.__pb_ProfileSeasonInfo.__index = pb.__pb_ProfileSeasonInfo
pb.__pb_ProfileSeasonInfo.__pairs = __pb_pairs

pb.ProfileSeasonInfo = { __name = "ProfileSeasonInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ProfileSeasonInfo : ProtoBase
---@field public level number
---@field public exp number
---@field public next_level_exp number
---@field public serial number
---@field public total_exp number
---@field public last_update_time number
---@field public boon_exp_remain number
---@field public day_exp number
---@field public bonus_percent number
---@field public total_price number
---@field public avg_survival_time number
---@field public max_survival_time number
---@field public max_collection_price number
---@field public settled boolean
---@field public kd number
---@field public damage number
---@field public collection_price number
---@field public support number
---@field public winning_percentage number
---@field public total_game_time number
---@field public total_fight number
---@field public total_escape number
---@field public max_escape_streak number
---@field public cur_escape_streak number
---@field public total_fracture number
---@field public total_bleed number
---@field public total_pain number
---@field public total_food_consumed number
---@field public total_drug_consumed number
---@field public total_healing number
---@field public max_healing number
---@field public avg_healing number
---@field public total_resurrect number
---@field public max_resurrect number
---@field public avg_resurrect number
---@field public total_death number
---@field public total_missing number
---@field public total_move_distance number
---@field public max_move_distance number
---@field public avg_move_distance number
---@field public max_jump number
---@field public total_walk_distance number
---@field public max_walk_distance number
---@field public avg_walk_distance number
---@field public total_loot_corpse number
---@field public max_loot_corpse number
---@field public avg_loot_corpse number
---@field public total_strongbox_open number
---@field public max_strongbox_open number
---@field public avg_strongbox_open number
---@field public total_unlock_door number
---@field public max_unlock_door number
---@field public avg_unlock_door number
---@field public total_game_egg number
---@field public max_game_egg number
---@field public avg_game_egg number
---@field public total_loot_container number
---@field public max_loot_container number
---@field public avg_loot_container number
---@field public total_kd number
---@field public max_kd number
---@field public avg_kd number
---@field public total_damage number
---@field public max_damage number
---@field public avg_damage number
---@field public total_shoot_down number
---@field public max_shoot_down number
---@field public avg_shoot_down number
---@field public total_kill number
---@field public max_kill number
---@field public avg_kill number
---@field public total_kill_alone number
---@field public max_kill_alone number
---@field public avg_kill_alone number
---@field public total_head_shot number
---@field public max_head_shot number
---@field public avg_head_shot number
---@field public total_hit number
---@field public max_hit number
---@field public avg_float number
---@field public max_hit_distance number
---@field public max_kill_distance number
---@field public total_kill_boss number
---@field public max_kill_boss number
---@field public avg_kill_boss number
---@field public total_kill_player number
---@field public max_kill_player number
---@field public avg_kill_player number
---@field public total_kill_robot number
---@field public max_kill_robot number
---@field public avg_kill_robot number
---@field public total_pistol_kill number
---@field public max_pistol_kill number
---@field public avg_pistol_kill number
---@field public total_melee_kill number
---@field public max_melee_kill number
---@field public avg_melee_kill number
---@field public total_sniper_gun_kill number
---@field public max_sniper_gun_kill number
---@field public avg_sniper_gun_kill number
---@field public total_shot_gun_kill number
---@field public max_shot_gun_kill number
---@field public avg_shot_gun_kill number
---@field public total_rifle_kill number
---@field public max_rifle_kill number
---@field public avg_rifle_kill number
---@field public total_machine_gun_kill number
---@field public max_machine_gun_kill number
---@field public avg_machine_gun_kill number
---@field public total_grenade_kill number
---@field public max_grenade_kill number
---@field public avg_grenade_kill number
---@field public total_carrier_kill number
---@field public max_carrier_kill number
---@field public avg_carrier_kill number
---@field public summary pb_Summary
---@field public rank_summary pb_RankSummary
---@field public mp_rank_summary pb_MPRankSummary
---@field public achievements_counters pb_SingleMatchAchievementCounter[]
---@field public mp_achievements_counters pb_SingleMatchAchievementCounter[]
---@field public commander_summary pb_CommanderSummary
---@field public commander_extra pb_CommanderExtra

---@return pb_ProfileSeasonInfo
function pb.ProfileSeasonInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CommanderExtra = {
    total_fight = 0,
    total_fight_as_commander = 0,
    win_ratio = 0,
    kill_by_vehicle_per_minute = 0,
    defeat_by_per_minute = 0,
    help_per_minute = 0,
}
pb.__pb_CommanderExtra.__name = "CommanderExtra"
pb.__pb_CommanderExtra.__index = pb.__pb_CommanderExtra
pb.__pb_CommanderExtra.__pairs = __pb_pairs

pb.CommanderExtra = { __name = "CommanderExtra", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CommanderExtra : ProtoBase
---@field public total_fight number
---@field public total_fight_as_commander number
---@field public win_ratio number
---@field public kill_by_vehicle_per_minute number
---@field public defeat_by_per_minute number
---@field public help_per_minute number

---@return pb_CommanderExtra
function pb.CommanderExtra:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_SingleMatchAchievementCounter = {
    id = 0,
    times = 0,
}
pb.__pb_SingleMatchAchievementCounter.__name = "SingleMatchAchievementCounter"
pb.__pb_SingleMatchAchievementCounter.__index = pb.__pb_SingleMatchAchievementCounter
pb.__pb_SingleMatchAchievementCounter.__pairs = __pb_pairs

pb.SingleMatchAchievementCounter = { __name = "SingleMatchAchievementCounter", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_SingleMatchAchievementCounter : ProtoBase
---@field public id number
---@field public times number

---@return pb_SingleMatchAchievementCounter
function pb.SingleMatchAchievementCounter:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MatchTitleCounter = {
    id = 0,
    times = 0,
}
pb.__pb_MatchTitleCounter.__name = "MatchTitleCounter"
pb.__pb_MatchTitleCounter.__index = pb.__pb_MatchTitleCounter
pb.__pb_MatchTitleCounter.__pairs = __pb_pairs

pb.MatchTitleCounter = { __name = "MatchTitleCounter", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MatchTitleCounter : ProtoBase
---@field public id number
---@field public times number

---@return pb_MatchTitleCounter
function pb.MatchTitleCounter:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PlayerMatchRecord = {
    player_id = 0,
    nick = "",
    plat_id = 0,
    account_type = 0,
    game_time = 0,
    game_result = 0,
    kill = 0,
    damage = 0,
    mvp = false,
    collection_price = 0,
    killPlayer = 0,
    killAI = 0,
    killBoss = 0,
    team_id = 0,
    is_ai = false,
    display_ai = false,
}
pb.__pb_PlayerMatchRecord.__name = "PlayerMatchRecord"
pb.__pb_PlayerMatchRecord.__index = pb.__pb_PlayerMatchRecord
pb.__pb_PlayerMatchRecord.__pairs = __pb_pairs

pb.PlayerMatchRecord = { __name = "PlayerMatchRecord", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PlayerMatchRecord : ProtoBase
---@field public player_id number
---@field public nick string
---@field public plat_id number
---@field public account_type number
---@field public game_time number
---@field public game_result number
---@field public kill number
---@field public damage number
---@field public mvp boolean
---@field public collection_price number
---@field public killPlayer number
---@field public killAI number
---@field public killBoss number
---@field public sol_data pb_MatchBaseRecordIrisSOL
---@field public raid_data pb_MatchBaseRecordIrisRaid
---@field public mp_data pb_MatchBaseRecordMP
---@field public arena_data pb_MatchBaseRecordArena
---@field public team_id number
---@field public achievements pb_DsGameAchievement[]
---@field public is_ai boolean
---@field public display_ai boolean

---@return pb_PlayerMatchRecord
function pb.PlayerMatchRecord:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MatchBaseRecord = {
    key = "",
    match_type = 0,
    game_time = 0,
    game_result = 0,
    match_time = 0,
    room_id = 0,
    hero_id = 0,
    match_start_time = 0,
    military_rank = 0,
    armedforce_id = 0,
    is_ranked_match = false,
    ranked_score_delta = 0,
    ranked_score = 0,
    ranked_level = 0,
    ranked_level_old = 0,
    ranked_score_season_no = 0,
    ranked_coins = 0,
    ranked_score_shoot = 0,
    ranked_score_tactics = 0,
    ranked_score_vehicle = 0,
    ranked_score_shoot_delta = 0,
    ranked_score_tactics_delta = 0,
    ranked_score_vehicle_delta = 0,
    client_group = 0,
    is_victory_unite_match = false,
}
pb.__pb_MatchBaseRecord.__name = "MatchBaseRecord"
pb.__pb_MatchBaseRecord.__index = pb.__pb_MatchBaseRecord
pb.__pb_MatchBaseRecord.__pairs = __pb_pairs

pb.MatchBaseRecord = { __name = "MatchBaseRecord", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MatchBaseRecord : ProtoBase
---@field public key string
---@field public match_type number
---@field public game_time number
---@field public game_result number
---@field public match_time number
---@field public match_mode pb_MatchModeInfo
---@field public room_id number
---@field public achievements pb_DsGameAchievement[]
---@field public sol pb_MatchBaseRecordIrisSOL
---@field public raid pb_MatchBaseRecordIrisRaid
---@field public mp pb_MatchBaseRecordMP
---@field public arena pb_MatchBaseRecordArena
---@field public ai_data pb_AIPlayerData[]
---@field public hero_id number
---@field public match_start_time number
---@field public military_rank number
---@field public armedforce_id number
---@field public is_ranked_match boolean
---@field public ranked_score_delta number
---@field public ranked_score number
---@field public ranked_level number
---@field public ranked_level_old number
---@field public ranked_score_season_no number
---@field public ranked_coins number
---@field public ranked_score_shoot number
---@field public ranked_score_tactics number
---@field public ranked_score_vehicle number
---@field public ranked_score_shoot_delta number
---@field public ranked_score_tactics_delta number
---@field public ranked_score_vehicle_delta number
---@field public tournament_data pb_MatchBaseRecordMPTournament
---@field public client_group number
---@field public is_victory_unite_match boolean

---@return pb_MatchBaseRecord
function pb.MatchBaseRecord:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Summary = {
    total_collection_price = 0,
    total_damage = 0,
    total_rescue = 0,
    total_healing = 0,
    total_kill_player = 0,
    total_kill_ai = 0,
    total_kill_boss = 0,
    total_teammate_price = 0,
    total_leave = 0,
    sol_total_game_cnt = 0,
    sol_total_battle = 0,
    sol_total_sneak = 0,
}
pb.__pb_Summary.__name = "Summary"
pb.__pb_Summary.__index = pb.__pb_Summary
pb.__pb_Summary.__pairs = __pb_pairs

pb.Summary = { __name = "Summary", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Summary : ProtoBase
---@field public total_collection_price number
---@field public total_damage number
---@field public total_rescue number
---@field public total_healing number
---@field public total_kill_player number
---@field public total_kill_ai number
---@field public total_kill_boss number
---@field public total_teammate_price number
---@field public total_leave number
---@field public sol_total_game_cnt number
---@field public sol_total_battle number
---@field public sol_total_sneak number

---@return pb_Summary
function pb.Summary:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RankEvent = {
    timestamp = 0,
    event_type = 0,
    event_value = 0,
    event_value_extra1 = 0,
    event_value_extra2 = 0,
    event_value_extra3 = 0,
}
pb.__pb_RankEvent.__name = "RankEvent"
pb.__pb_RankEvent.__index = pb.__pb_RankEvent
pb.__pb_RankEvent.__pairs = __pb_pairs

pb.RankEvent = { __name = "RankEvent", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RankEvent : ProtoBase
---@field public timestamp number
---@field public event_type number
---@field public event_value number
---@field public event_value_extra1 number
---@field public event_value_extra2 number
---@field public event_value_extra3 number

---@return pb_RankEvent
function pb.RankEvent:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RankHeroSummary = {
    heroid = 0,
    used_cnt = 0,
}
pb.__pb_RankHeroSummary.__name = "RankHeroSummary"
pb.__pb_RankHeroSummary.__index = pb.__pb_RankHeroSummary
pb.__pb_RankHeroSummary.__pairs = __pb_pairs

pb.RankHeroSummary = { __name = "RankHeroSummary", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RankHeroSummary : ProtoBase
---@field public heroid number
---@field public used_cnt number

---@return pb_RankHeroSummary
function pb.RankHeroSummary:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RankMajorSummary = {
    majorid = 0,
    total_game_cnt = 0,
    total_game_escaped = 0,
    total_collection_price = 0,
    total_kill = 0,
    total_death = 0,
}
pb.__pb_RankMajorSummary.__name = "RankMajorSummary"
pb.__pb_RankMajorSummary.__index = pb.__pb_RankMajorSummary
pb.__pb_RankMajorSummary.__pairs = __pb_pairs

pb.RankMajorSummary = { __name = "RankMajorSummary", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RankMajorSummary : ProtoBase
---@field public majorid number
---@field public total_game_cnt number
---@field public total_game_escaped number
---@field public total_collection_price number
---@field public total_kill number
---@field public total_death number
---@field public heros_used pb_RankHeroSummary[]

---@return pb_RankMajorSummary
function pb.RankMajorSummary:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RankSummary = {
    score = 0,
    score_max = 0,
    score_old = 0,
    score_max_old = 0,
    score_delta_by_mobile = 0,
    score_delta_by_pc = 0,
    level = 0,
    level_max = 0,
    major_level = 0,
    major_level_max = 0,
    level_score = 0,
    levelup_need_score = 0,
    win_streak = 0,
    lose_streak = 0,
    total_play = 0,
    score_max_season_no = 0,
    has_attended = false,
}
pb.__pb_RankSummary.__name = "RankSummary"
pb.__pb_RankSummary.__index = pb.__pb_RankSummary
pb.__pb_RankSummary.__pairs = __pb_pairs

pb.RankSummary = { __name = "RankSummary", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RankSummary : ProtoBase
---@field public score number
---@field public score_max number
---@field public score_old number
---@field public score_max_old number
---@field public score_delta_by_mobile number
---@field public score_delta_by_pc number
---@field public levels_rewarded number[]
---@field public level number
---@field public level_max number
---@field public major_level number
---@field public major_level_max number
---@field public level_score number
---@field public levelup_need_score number
---@field public rank_events pb_RankEvent[]
---@field public win_streak number
---@field public lose_streak number
---@field public total_play number
---@field public score_max_season_no number
---@field public major_summaries pb_RankMajorSummary[]
---@field public has_attended boolean

---@return pb_RankSummary
function pb.RankSummary:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CommanderSummary = {
    score = 0,
    score_max = 0,
    score_delta_by_mobile = 0,
    score_delta_by_pc = 0,
    old_score = 0,
    old_score_max = 0,
    level = 0,
    level_max = 0,
    major_level = 0,
    major_level_max = 0,
    level_score = 0,
    levelup_need_score = 0,
    win_streak = 0,
    lose_streak = 0,
    total_play = 0,
    score_max_season_no = 0,
    has_attended = false,
    total_win = 0,
    total_kill = 0,
    total_killed = 0,
    total_game_time = 0,
    acked_times_as_commander = 0,
    acked_times_as_soldier = 0,
}
pb.__pb_CommanderSummary.__name = "CommanderSummary"
pb.__pb_CommanderSummary.__index = pb.__pb_CommanderSummary
pb.__pb_CommanderSummary.__pairs = __pb_pairs

pb.CommanderSummary = { __name = "CommanderSummary", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CommanderSummary : ProtoBase
---@field public info pb_MPSeasonInfo
---@field public score number
---@field public score_max number
---@field public score_delta_by_mobile number
---@field public score_delta_by_pc number
---@field public levels_rewarded number[]
---@field public old_score number
---@field public old_score_max number
---@field public level number
---@field public level_max number
---@field public major_level number
---@field public major_level_max number
---@field public level_score number
---@field public levelup_need_score number
---@field public rank_events pb_RankEvent[]
---@field public win_streak number
---@field public lose_streak number
---@field public total_play number
---@field public score_max_season_no number
---@field public major_summaries pb_MPRankMajorSummary[]
---@field public has_attended boolean
---@field public total_win number
---@field public total_kill number
---@field public total_killed number
---@field public total_game_time number
---@field public acked_times_as_commander number
---@field public acked_times_as_soldier number
---@field public last_days pb_MPSeasonInfo[]
---@field public titles pb_MatchTitleCounter[]

---@return pb_CommanderSummary
function pb.CommanderSummary:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RankMatchScoreChange = {
    reason = 0,
    score = 0,
    limit = 0,
}
pb.__pb_RankMatchScoreChange.__name = "RankMatchScoreChange"
pb.__pb_RankMatchScoreChange.__index = pb.__pb_RankMatchScoreChange
pb.__pb_RankMatchScoreChange.__pairs = __pb_pairs

pb.RankMatchScoreChange = { __name = "RankMatchScoreChange", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RankMatchScoreChange : ProtoBase
---@field public reason number
---@field public score number
---@field public limit number

---@return pb_RankMatchScoreChange
function pb.RankMatchScoreChange:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MPRankMajorSummary = {
    majorid = 0,
    total_game_cnt = 0,
    total_kill = 0,
    total_death = 0,
}
pb.__pb_MPRankMajorSummary.__name = "MPRankMajorSummary"
pb.__pb_MPRankMajorSummary.__index = pb.__pb_MPRankMajorSummary
pb.__pb_MPRankMajorSummary.__pairs = __pb_pairs

pb.MPRankMajorSummary = { __name = "MPRankMajorSummary", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MPRankMajorSummary : ProtoBase
---@field public majorid number
---@field public total_game_cnt number
---@field public total_kill number
---@field public total_death number
---@field public heros_used pb_RankHeroSummary[]

---@return pb_MPRankMajorSummary
function pb.MPRankMajorSummary:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MPRankSummary = {
    score = 0,
    score_max = 0,
    score_shoot = 0,
    score_tactics = 0,
    score_vehicle = 0,
    score_delta_by_mobile = 0,
    score_delta_by_pc = 0,
    old_score = 0,
    old_score_max = 0,
    old_score_shoot = 0,
    old_score_tactics = 0,
    old_score_vehicle = 0,
    level = 0,
    level_max = 0,
    major_level = 0,
    major_level_max = 0,
    level_score = 0,
    levelup_need_score = 0,
    win_streak = 0,
    lose_streak = 0,
    total_play = 0,
    score_max_season_no = 0,
    has_attended = false,
    total_win = 0,
    total_kill = 0,
    total_killed = 0,
    total_game_time = 0,
}
pb.__pb_MPRankSummary.__name = "MPRankSummary"
pb.__pb_MPRankSummary.__index = pb.__pb_MPRankSummary
pb.__pb_MPRankSummary.__pairs = __pb_pairs

pb.MPRankSummary = { __name = "MPRankSummary", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MPRankSummary : ProtoBase
---@field public score number
---@field public score_max number
---@field public score_shoot number
---@field public score_tactics number
---@field public score_vehicle number
---@field public score_delta_by_mobile number
---@field public score_delta_by_pc number
---@field public levels_rewarded number[]
---@field public old_score number
---@field public old_score_max number
---@field public old_score_shoot number
---@field public old_score_tactics number
---@field public old_score_vehicle number
---@field public level number
---@field public level_max number
---@field public major_level number
---@field public major_level_max number
---@field public level_score number
---@field public levelup_need_score number
---@field public rank_events pb_RankEvent[]
---@field public win_streak number
---@field public lose_streak number
---@field public total_play number
---@field public score_max_season_no number
---@field public major_summaries pb_MPRankMajorSummary[]
---@field public has_attended boolean
---@field public total_win number
---@field public total_kill number
---@field public total_killed number
---@field public total_game_time number

---@return pb_MPRankSummary
function pb.MPRankSummary:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_AIPlayerData = {
    player_id = 0,
    plat_id = 0,
    account_type = 0,
}
pb.__pb_AIPlayerData.__name = "AIPlayerData"
pb.__pb_AIPlayerData.__index = pb.__pb_AIPlayerData
pb.__pb_AIPlayerData.__pairs = __pb_pairs

pb.AIPlayerData = { __name = "AIPlayerData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_AIPlayerData : ProtoBase
---@field public player_id number
---@field public player_data pb_SpecificModeData
---@field public game_achievements pb_DsGameAchievement[]
---@field public plat_id number
---@field public account_type number

---@return pb_AIPlayerData
function pb.AIPlayerData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_SpecificModeData = {
}
pb.__pb_SpecificModeData.__name = "SpecificModeData"
pb.__pb_SpecificModeData.__index = pb.__pb_SpecificModeData
pb.__pb_SpecificModeData.__pairs = __pb_pairs

pb.SpecificModeData = { __name = "SpecificModeData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_SpecificModeData : ProtoBase
---@field public sol pb_MatchBaseRecordIrisSOL
---@field public raid pb_MatchBaseRecordIrisRaid
---@field public mp pb_MatchBaseRecordMP
---@field public arena pb_MatchBaseRecordArena

---@return pb_SpecificModeData
function pb.SpecificModeData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MatchBaseRecordIrisSOL = {
    collection_price = 0,
    damage = 0,
    rescue = 0,
    healing = 0,
    killPlayer = 0,
    killAI = 0,
    killBoss = 0,
    team_mate_price = 0,
    leave = false,
    total_price = 0,
    gained_price = 0,
    hero_id = 0,
    killer_type = 0,
    weapon = 0,
    revive = 0,
    assist_cnt = 0,
    total_shoot = 0,
    total_shoot_hit = 0,
    total_shoot_down = 0,
    total_shoot_head_down = 0,
    total_contract_price = 0,
    total_bring_mandel_brick = 0,
    total_bring_gold_sku = 0,
    total_bring_red_sku = 0,
    total_search_cnt = 0,
    total_mileage = 0,
    begin_game_price = 0,
    safebox_skin_id = 0,
}
pb.__pb_MatchBaseRecordIrisSOL.__name = "MatchBaseRecordIrisSOL"
pb.__pb_MatchBaseRecordIrisSOL.__index = pb.__pb_MatchBaseRecordIrisSOL
pb.__pb_MatchBaseRecordIrisSOL.__pairs = __pb_pairs

pb.MatchBaseRecordIrisSOL = { __name = "MatchBaseRecordIrisSOL", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MatchBaseRecordIrisSOL : ProtoBase
---@field public collection_price number
---@field public damage number
---@field public rescue number
---@field public healing number
---@field public killPlayer number
---@field public killAI number
---@field public killBoss number
---@field public team_mate_price number
---@field public leave boolean
---@field public total_price number
---@field public gained_price number
---@field public hero_id number
---@field public killer_type number
---@field public player pb_DsPlayerBasicInfo
---@field public ai pb_DsAIBasicInfo
---@field public boss pb_DsAIBasicInfo
---@field public weapon number
---@field public revive number
---@field public assist_cnt number
---@field public total_shoot number
---@field public total_shoot_hit number
---@field public total_shoot_down number
---@field public total_shoot_head_down number
---@field public total_contract_price number
---@field public total_bring_mandel_brick number
---@field public total_bring_gold_sku number
---@field public total_bring_red_sku number
---@field public total_search_cnt number
---@field public total_mileage number
---@field public begin_game_price number
---@field public safebox_skin_id number

---@return pb_MatchBaseRecordIrisSOL
function pb.MatchBaseRecordIrisSOL:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MatchBaseRecordMPTeam = {
    team_id = 0,
}
pb.__pb_MatchBaseRecordMPTeam.__name = "MatchBaseRecordMPTeam"
pb.__pb_MatchBaseRecordMPTeam.__index = pb.__pb_MatchBaseRecordMPTeam
pb.__pb_MatchBaseRecordMPTeam.__pairs = __pb_pairs

pb.MatchBaseRecordMPTeam = { __name = "MatchBaseRecordMPTeam", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MatchBaseRecordMPTeam : ProtoBase
---@field public team_id number
---@field public player_list number[]

---@return pb_MatchBaseRecordMPTeam
function pb.MatchBaseRecordMPTeam:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MatchBaseRecordMPCamp = {
    color = 0,
    attacker = false,
}
pb.__pb_MatchBaseRecordMPCamp.__name = "MatchBaseRecordMPCamp"
pb.__pb_MatchBaseRecordMPCamp.__index = pb.__pb_MatchBaseRecordMPCamp
pb.__pb_MatchBaseRecordMPCamp.__pairs = __pb_pairs

pb.MatchBaseRecordMPCamp = { __name = "MatchBaseRecordMPCamp", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MatchBaseRecordMPCamp : ProtoBase
---@field public color number
---@field public team_list pb_MatchBaseRecordMPTeam[]
---@field public attacker boolean

---@return pb_MatchBaseRecordMPCamp
function pb.MatchBaseRecordMPCamp:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MatchBaseRecordIrisRaid = {
    score = 0,
    level = 0,
    lives = 0,
    kill_num = 0,
    play_time = 0,
    leave = false,
    assit_num = 0,
    success = false,
    total_damage = 0,
    death_count = 0,
    cost_value = 0,
    hero_id = 0,
}
pb.__pb_MatchBaseRecordIrisRaid.__name = "MatchBaseRecordIrisRaid"
pb.__pb_MatchBaseRecordIrisRaid.__index = pb.__pb_MatchBaseRecordIrisRaid
pb.__pb_MatchBaseRecordIrisRaid.__pairs = __pb_pairs

pb.MatchBaseRecordIrisRaid = { __name = "MatchBaseRecordIrisRaid", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MatchBaseRecordIrisRaid : ProtoBase
---@field public score number
---@field public level number
---@field public lives number
---@field public kill_num number
---@field public play_time number
---@field public leave boolean
---@field public player_list number[]
---@field public assit_num number
---@field public success boolean
---@field public total_damage number
---@field public death_count number
---@field public cost_value number
---@field public hero_id number

---@return pb_MatchBaseRecordIrisRaid
function pb.MatchBaseRecordIrisRaid:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MatchBaseRecordMPTournament = {
    is_ranked_match = false,
    rank_match_score = 0,
    ranked_score_leave_penalty = 0,
    ranked_score_half_join_score = 0,
    time_factor = 0,
    raw_total_ranked_score = 0,
    real_total_ranked_score = 0,
    self_ranking = 0,
    rank_score_spm = 0,
    rank_score_spm_score = 0,
    rank_score_result_score = 0,
    rank_score_defeated_extra_score = 0,
    rank_score_camps_gap_extra_score = 0,
    rank_score_origin_spm_point = 0,
    rank_score_my_camp_point = 0,
    rank_score_enemy_camp_point = 0,
    rank_score_reputation_extra_score = 0,
    play_time = 0,
    rank_score_discount_rate = 0,
    rank_score_discount_ceiling = 0,
}
pb.__pb_MatchBaseRecordMPTournament.__name = "MatchBaseRecordMPTournament"
pb.__pb_MatchBaseRecordMPTournament.__index = pb.__pb_MatchBaseRecordMPTournament
pb.__pb_MatchBaseRecordMPTournament.__pairs = __pb_pairs

pb.MatchBaseRecordMPTournament = { __name = "MatchBaseRecordMPTournament", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MatchBaseRecordMPTournament : ProtoBase
---@field public is_ranked_match boolean
---@field public rank_match_score number
---@field public rank_shields pb_TournamentRankShield[]
---@field public ranked_score_leave_penalty number
---@field public ranked_score_half_join_score number
---@field public time_factor number
---@field public raw_total_ranked_score number
---@field public real_total_ranked_score number
---@field public self_ranking number
---@field public rank_score_spm number
---@field public rank_score_spm_score number
---@field public rank_score_result_score number
---@field public rank_score_defeated_extra_score number
---@field public rank_score_camps_gap_extra_score number
---@field public rank_score_origin_spm_point number
---@field public rank_score_my_camp_point number
---@field public rank_score_enemy_camp_point number
---@field public rank_score_reputation_extra_score number
---@field public play_time number
---@field public double_rank_info pb_TournamentRankDoubleInfo
---@field public rank_score_discount_rate number
---@field public rank_score_discount_ceiling number

---@return pb_MatchBaseRecordMPTournament
function pb.MatchBaseRecordMPTournament:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MatchBaseRecordMP = {
    kill = 0,
    death = 0,
    assist = 0,
    score = 0,
    mvp = false,
    leave = false,
    occupy = 0,
    color = 0,
    is_winner = false,
    hero_id = 0,
    military_rank = 0,
    attacker = false,
    armedforce_id = 0,
    rescue = 0,
    total_shoot = 0,
    total_shoot_hit = 0,
    total_shoot_head_down = 0,
    total_kill_long_range = 0,
    total_capture_point_time = 0,
    total_vehicle_use_time = 0,
    total_damage_to_vehicle = 0,
    total_vehicle_kill = 0,
    total_vehicle_destroyed = 0,
    total_vehicle_repair = 0,
    capture_flag_num = 0,
    rescue_contrib = 0,
    build_and_destroy_contrib = 0,
    capture_contrib = 0,
    tactics_contrib = 0,
    commander_score_delta = 0,
    commander_score = 0,
    vehicle_kill_count = 0,
    vehicle_play_time = 0,
    infantry_kill_count = 0,
    infantry_play_time = 0,
    commander_contributor_title = 0,
}
pb.__pb_MatchBaseRecordMP.__name = "MatchBaseRecordMP"
pb.__pb_MatchBaseRecordMP.__index = pb.__pb_MatchBaseRecordMP
pb.__pb_MatchBaseRecordMP.__pairs = __pb_pairs

pb.MatchBaseRecordMP = { __name = "MatchBaseRecordMP", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MatchBaseRecordMP : ProtoBase
---@field public kill number
---@field public death number
---@field public assist number
---@field public score number
---@field public mvp boolean
---@field public leave boolean
---@field public occupy number
---@field public camp_list pb_MatchBaseRecordMPCamp[]
---@field public color number
---@field public is_winner boolean
---@field public hero_id number
---@field public military_rank number
---@field public attacker boolean
---@field public armedforce_id number
---@field public rescue number
---@field public total_shoot number
---@field public total_shoot_hit number
---@field public total_shoot_head_down number
---@field public total_kill_long_range number
---@field public total_capture_point_time number
---@field public total_vehicle_use_time number
---@field public total_damage_to_vehicle number
---@field public total_vehicle_kill number
---@field public total_vehicle_destroyed number
---@field public total_vehicle_repair number
---@field public game_medal pb_DsGameMedal[]
---@field public capture_flag_num number
---@field public rescue_contrib number
---@field public build_and_destroy_contrib number
---@field public capture_contrib number
---@field public tactics_contrib number
---@field public commander_score_delta number
---@field public commander_score number
---@field public vehicle_kill_count number
---@field public vehicle_play_time number
---@field public infantry_kill_count number
---@field public infantry_play_time number
---@field public commander_contributor_title number

---@return pb_MatchBaseRecordMP
function pb.MatchBaseRecordMP:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MatchBaseRecordArena = {
    kill = 0,
    death = 0,
    assist = 0,
    result = 0,
    team_score = 0,
    hero_id = 0,
    rank = 0,
}
pb.__pb_MatchBaseRecordArena.__name = "MatchBaseRecordArena"
pb.__pb_MatchBaseRecordArena.__index = pb.__pb_MatchBaseRecordArena
pb.__pb_MatchBaseRecordArena.__pairs = __pb_pairs

pb.MatchBaseRecordArena = { __name = "MatchBaseRecordArena", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MatchBaseRecordArena : ProtoBase
---@field public kill number
---@field public death number
---@field public assist number
---@field public result number
---@field public team_score number
---@field public hero_id number
---@field public rank number
---@field public final_props pb_EquipPosition[]

---@return pb_MatchBaseRecordArena
function pb.MatchBaseRecordArena:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_SeasonChangeInfo = {
    level = 0,
    exp = 0,
    next_level_exp = 0,
    old_level = 0,
    old_exp = 0,
    old_next_level_exp = 0,
    delta_exp = 0,
    bonus_exp = 0,
    room_id = 0,
    map_id = 0,
    old_season_no = 0,
    season_no = 0,
    old_total_exp = 0,
    total_exp = 0,
}
pb.__pb_SeasonChangeInfo.__name = "SeasonChangeInfo"
pb.__pb_SeasonChangeInfo.__index = pb.__pb_SeasonChangeInfo
pb.__pb_SeasonChangeInfo.__pairs = __pb_pairs

pb.SeasonChangeInfo = { __name = "SeasonChangeInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_SeasonChangeInfo : ProtoBase
---@field public level number
---@field public exp number
---@field public next_level_exp number
---@field public old_level number
---@field public old_exp number
---@field public old_next_level_exp number
---@field public delta_exp number
---@field public add_exps pb_AccountExpChange[]
---@field public bonus_exp number
---@field public room_id number
---@field public map_id number
---@field public old_season_no number
---@field public season_no number
---@field public old_total_exp number
---@field public total_exp number

---@return pb_SeasonChangeInfo
function pb.SeasonChangeInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_AccountExpChange = {
    add_exp = 0,
    reason = 0,
}
pb.__pb_AccountExpChange.__name = "AccountExpChange"
pb.__pb_AccountExpChange.__index = pb.__pb_AccountExpChange
pb.__pb_AccountExpChange.__pairs = __pb_pairs

pb.AccountExpChange = { __name = "AccountExpChange", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_AccountExpChange : ProtoBase
---@field public add_exp number
---@field public reason number

---@return pb_AccountExpChange
function pb.AccountExpChange:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_SeasonInfo = {
    serial = 0,
    level = 0,
    exp = 0,
    total_exp = 0,
    last_update_time = 0,
    boon_exp_remain = 0,
    day_exp = 0,
    bonus_percent = 0,
}
pb.__pb_SeasonInfo.__name = "SeasonInfo"
pb.__pb_SeasonInfo.__index = pb.__pb_SeasonInfo
pb.__pb_SeasonInfo.__pairs = __pb_pairs

pb.SeasonInfo = { __name = "SeasonInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_SeasonInfo : ProtoBase
---@field public serial number
---@field public level number
---@field public exp number
---@field public total_exp number
---@field public last_update_time number
---@field public boon_exp_remain number
---@field public day_exp number
---@field public bonus_percent number

---@return pb_SeasonInfo
function pb.SeasonInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_AccountPunishItem = {
    type = 0,
    reason = 0,
    begin = 0,
    _end = 0,
    custom_reason = "",
    match_punish_id = 0,
    punish_end = 0,
    reporting_mode = 0,
    number_reports = 0,
}
pb.__pb_AccountPunishItem.__name = "AccountPunishItem"
pb.__pb_AccountPunishItem.__index = pb.__pb_AccountPunishItem
pb.__pb_AccountPunishItem.__pairs = __pb_pairs

pb.AccountPunishItem = { __name = "AccountPunishItem", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_AccountPunishItem : ProtoBase
---@field public type number
---@field public reason number
---@field public begin number
---@field public _end number
---@field public custom_reason string
---@field public match_punish_id number
---@field public punish_end number
---@field public reporting_mode number
---@field public number_reports number

---@return pb_AccountPunishItem
function pb.AccountPunishItem:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_IrisSOLData = {
    total_fight = 0,
    total_escape = 0,
    total_game_time = 0,
    total_kill = 0,
    total_killed = 0,
    carry_teammate_assets = 0,
    total_collection_price = 0,
    total_gained_price = 0,
    total_miss = 0,
    total_price = 0,
    total_fight_all = 0,
    total_quit = 0,
    total_giveup = 0,
    kill_low_stakes = 0,
    kill_med_stakes = 0,
    kill_high_stakes = 0,
    killed_low_stakes = 0,
    killed_med_stakes = 0,
    killed_high_stakes = 0,
    current_asset = 0,
    non_current_assets = 0,
    rank_attended = false,
    rank_score = 0,
    rank_score_max = 0,
    rank_max_season_no = 0,
}
pb.__pb_IrisSOLData.__name = "IrisSOLData"
pb.__pb_IrisSOLData.__index = pb.__pb_IrisSOLData
pb.__pb_IrisSOLData.__pairs = __pb_pairs

pb.IrisSOLData = { __name = "IrisSOLData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_IrisSOLData : ProtoBase
---@field public total_fight number
---@field public total_escape number
---@field public total_game_time number
---@field public total_kill number
---@field public total_killed number
---@field public carry_teammate_assets number
---@field public total_collection_price number
---@field public total_gained_price number
---@field public total_miss number
---@field public total_price number
---@field public total_fight_all number
---@field public total_quit number
---@field public total_giveup number
---@field public kill_low_stakes number
---@field public kill_med_stakes number
---@field public kill_high_stakes number
---@field public killed_low_stakes number
---@field public killed_med_stakes number
---@field public killed_high_stakes number
---@field public current_asset number
---@field public non_current_assets number
---@field public rank_attended boolean
---@field public rank_score number
---@field public rank_score_max number
---@field public rank_max_season_no number

---@return pb_IrisSOLData
function pb.IrisSOLData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_IrisRaidData = {
    total_fight = 0,
    total_pass = 0,
    total_pass_perfect = 0,
    hell_mode_1st_pass = 0,
    hell_mode_fastest_pass = 0,
}
pb.__pb_IrisRaidData.__name = "IrisRaidData"
pb.__pb_IrisRaidData.__index = pb.__pb_IrisRaidData
pb.__pb_IrisRaidData.__pairs = __pb_pairs

pb.IrisRaidData = { __name = "IrisRaidData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_IrisRaidData : ProtoBase
---@field public total_fight number
---@field public total_pass number
---@field public total_pass_perfect number
---@field public hell_mode_1st_pass number
---@field public hell_mode_fastest_pass number

---@return pb_IrisRaidData
function pb.IrisRaidData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MPData = {
    total_fight = 0,
    total_win = 0,
    total_kill = 0,
    total_killed = 0,
    total_mvp = 0,
    total_game_time = 0,
    total_score = 0,
    total_quit = 0,
    rank_attended = false,
    rank_score = 0,
    rank_score_max = 0,
    rank_max_season_no = 0,
    commander_score = 0,
}
pb.__pb_MPData.__name = "MPData"
pb.__pb_MPData.__index = pb.__pb_MPData
pb.__pb_MPData.__pairs = __pb_pairs

pb.MPData = { __name = "MPData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MPData : ProtoBase
---@field public total_fight number
---@field public total_win number
---@field public total_kill number
---@field public total_killed number
---@field public total_mvp number
---@field public total_game_time number
---@field public total_score number
---@field public total_quit number
---@field public rank_attended boolean
---@field public rank_score number
---@field public rank_score_max number
---@field public rank_max_season_no number
---@field public commander_score number

---@return pb_MPData
function pb.MPData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_AttrValue = {
    type = 0,
    value = 0,
}
pb.__pb_AttrValue.__name = "AttrValue"
pb.__pb_AttrValue.__index = pb.__pb_AttrValue
pb.__pb_AttrValue.__pairs = __pb_pairs

pb.AttrValue = { __name = "AttrValue", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_AttrValue : ProtoBase
---@field public type number
---@field public value number

---@return pb_AttrValue
function pb.AttrValue:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RoleAttrValue = {
    type = 0,
}
pb.__pb_RoleAttrValue.__name = "RoleAttrValue"
pb.__pb_RoleAttrValue.__index = pb.__pb_RoleAttrValue
pb.__pb_RoleAttrValue.__pairs = __pb_pairs

pb.RoleAttrValue = { __name = "RoleAttrValue", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RoleAttrValue : ProtoBase
---@field public type number
---@field public base_attr pb_AttrValue
---@field public buff_attr_add pb_AttrValue
---@field public buff_attr_add_percent pb_AttrValue

---@return pb_RoleAttrValue
function pb.RoleAttrValue:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RepLevelConfig = {
    level = 0,
    score = 0,
}
pb.__pb_RepLevelConfig.__name = "RepLevelConfig"
pb.__pb_RepLevelConfig.__index = pb.__pb_RepLevelConfig
pb.__pb_RepLevelConfig.__pairs = __pb_pairs

pb.RepLevelConfig = { __name = "RepLevelConfig", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RepLevelConfig : ProtoBase
---@field public level number
---@field public score number

---@return pb_RepLevelConfig
function pb.RepLevelConfig:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RoleQualityCard = {
    card_id = 0,
    quality_value = 0,
    is_unlock = false,
    unlock_time = 0,
    is_disabled = false,
    level = 0,
}
pb.__pb_RoleQualityCard.__name = "RoleQualityCard"
pb.__pb_RoleQualityCard.__index = pb.__pb_RoleQualityCard
pb.__pb_RoleQualityCard.__pairs = __pb_pairs

pb.RoleQualityCard = { __name = "RoleQualityCard", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RoleQualityCard : ProtoBase
---@field public card_id number
---@field public quality_value number
---@field public is_unlock boolean
---@field public unlock_time number
---@field public is_disabled boolean
---@field public level number

---@return pb_RoleQualityCard
function pb.RoleQualityCard:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_QualityChange = {
    card_id = 0,
    value_delta = 0,
    prop_id = 0,
}
pb.__pb_QualityChange.__name = "QualityChange"
pb.__pb_QualityChange.__index = pb.__pb_QualityChange
pb.__pb_QualityChange.__pairs = __pb_pairs

pb.QualityChange = { __name = "QualityChange", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_QualityChange : ProtoBase
---@field public card_id number
---@field public value_delta number
---@field public prop_id number

---@return pb_QualityChange
function pb.QualityChange:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_AttrSkill = {
    level = 0,
    exp = 0,
}
pb.__pb_AttrSkill.__name = "AttrSkill"
pb.__pb_AttrSkill.__index = pb.__pb_AttrSkill
pb.__pb_AttrSkill.__pairs = __pb_pairs

pb.AttrSkill = { __name = "AttrSkill", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_AttrSkill : ProtoBase
---@field public level number
---@field public exp number

---@return pb_AttrSkill
function pb.AttrSkill:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GlobalPlayerStateInfo = {
    player_id = 0,
    state = 0,
    map_id = 0,
    team_id = 0,
    team_zone_id = 0,
    team_svr_id = 0,
    conn_bus_id = 0,
}
pb.__pb_GlobalPlayerStateInfo.__name = "GlobalPlayerStateInfo"
pb.__pb_GlobalPlayerStateInfo.__index = pb.__pb_GlobalPlayerStateInfo
pb.__pb_GlobalPlayerStateInfo.__pairs = __pb_pairs

pb.GlobalPlayerStateInfo = { __name = "GlobalPlayerStateInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GlobalPlayerStateInfo : ProtoBase
---@field public player_id number
---@field public state number
---@field public map_id number
---@field public team_id number
---@field public team_zone_id number
---@field public team_svr_id number
---@field public conn_bus_id number

---@return pb_GlobalPlayerStateInfo
function pb.GlobalPlayerStateInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PlayerSimpleInfo = {
    player_id = 0,
    nick_name = "",
    pic_url = "",
    level = 0,
    gender = 0,
    register_time = 0,
    safehouse_degree = 0,
    season_lvl = 0,
    sol_rank_score = 0,
    sol_rank_attended = false,
    mp_rank_score = 0,
    mp_rank_attended = false,
    mp_commander_score = 0,
    show_commander_rank_points = 0,
    military_tag = 0,
    title = 0,
    rank_title_adcode = 0,
    rank_title_rank_no = 0,
    game_center = 0,
    vip_lvl = 0,
    svip_lvl = 0,
    monthcard_level = 0,
    ex_vip_flag = 0,
    fight_point = 0,
    role_id = 0,
    headwear_id = 0,
    backdecoration_id = 0,
    head_frame = 0,
    btype = 0,
    account_lvl = 0,
    account_type = 0,
    login_conn_index = 0,
    login_gateway = "",
    login_tconnd = 0,
    plat = 0,
    last_login_version = 0,
    can_cross_plat_play = false,
    bhd_is_purchased = false,
    country_code = 0,
    area_code = 0,
    ps_can_cross_plat_play = false,
    xbox_can_cross_plat_play = false,
    login_time = 0,
}
pb.__pb_PlayerSimpleInfo.__name = "PlayerSimpleInfo"
pb.__pb_PlayerSimpleInfo.__index = pb.__pb_PlayerSimpleInfo
pb.__pb_PlayerSimpleInfo.__pairs = __pb_pairs

pb.PlayerSimpleInfo = { __name = "PlayerSimpleInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PlayerSimpleInfo : ProtoBase
---@field public player_id number
---@field public nick_name string
---@field public pic_url string
---@field public level number
---@field public gender number
---@field public register_time number
---@field public safehouse_degree number
---@field public season_lvl number
---@field public sol_rank_score number
---@field public sol_rank_attended boolean
---@field public mp_rank_score number
---@field public mp_rank_attended boolean
---@field public mp_commander_score number
---@field public show_commander_rank_points number
---@field public military_tag number
---@field public title number
---@field public rank_title_adcode number
---@field public rank_title_rank_no number
---@field public game_center number
---@field public vip_lvl number
---@field public svip_lvl number
---@field public monthcard_level number
---@field public ex_vip_flag number
---@field public fight_point number
---@field public role_id number
---@field public headwear_id number
---@field public backdecoration_id number
---@field public head_frame number
---@field public btype number
---@field public account_lvl number
---@field public account_type number
---@field public login_conn_index number
---@field public login_gateway string
---@field public login_tconnd number
---@field public plat number
---@field public last_login_version number
---@field public exp_tag_id number[]
---@field public can_cross_plat_play boolean
---@field public bhd_is_purchased boolean
---@field public country_code number
---@field public area_code number
---@field public ps_can_cross_plat_play boolean
---@field public xbox_can_cross_plat_play boolean
---@field public login_time number

---@return pb_PlayerSimpleInfo
function pb.PlayerSimpleInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MatchPlayerInfo = {
    player_id = 0,
    camp = 0,
    seat_index = 0,
    zone_bus_id = 0,
    is_robot = false,
    ai_level = 0,
    is_halfway_join = false,
    is_team = false,
    is_rand_map = false,
    zone_id = 0,
    team_id = 0,
    team_member_count = 0,
    team_forbid_stranger = false,
    roomsvr_team_id = 0,
    is_dead = false,
    group_id = 0,
    ai_robot = 0,
    ds_seat_index = 0,
    observer_type = 0,
    observer_info = 0,
    ds_token = "",
    match_pool_id = 0,
    score = 0,
    tag = 0,
    exp = 0,
    hidden_score = 0,
    match_uuid = "",
    is_ranked_match = false,
    tdm_scheme_id = 0,
    is_finish_tdm_scheme = false,
    is_observer = false,
    observer_id = 0,
    sol_rank_score = 0,
    sol_rank_level = 0,
    sol_attended = false,
    isolation_tag = 0,
    sol_season_match_count = 0,
    sol_season_rank_count = 0,
    use_rental_props = false,
    enable_replay = false,
    tdm_rank_score = 0,
    tdm_rank_level = 0,
    reputation_level = 0,
    tdm_rank_max_score = 0,
    punish_replay_begin_time = 0,
    punish_replay_report_cnts = 0,
    tdm_lost_days = 0,
    tdm_last_rank_max_score = 0,
}
pb.__pb_MatchPlayerInfo.__name = "MatchPlayerInfo"
pb.__pb_MatchPlayerInfo.__index = pb.__pb_MatchPlayerInfo
pb.__pb_MatchPlayerInfo.__pairs = __pb_pairs

pb.MatchPlayerInfo = { __name = "MatchPlayerInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MatchPlayerInfo : ProtoBase
---@field public player_id number
---@field public camp number
---@field public seat_index number
---@field public zone_bus_id number
---@field public is_robot boolean
---@field public profile pb_PlayerSimpleInfo
---@field public ai_level number
---@field public is_halfway_join boolean
---@field public is_team boolean
---@field public is_rand_map boolean
---@field public zone_id number
---@field public team_id number
---@field public team_member_count number
---@field public team_forbid_stranger boolean
---@field public roomsvr_team_id number
---@field public is_dead boolean
---@field public group_id number
---@field public ai_robot number
---@field public ds_seat_index number
---@field public observer_type number
---@field public observer_info number
---@field public ds_token string
---@field public match_pool_id number
---@field public score number
---@field public tag number
---@field public sol_cal_info pb_SOLPlayerMatchCalInfo
---@field public exp_tag_id number[]
---@field public exp number
---@field public hidden_score number
---@field public match_uuid string
---@field public is_ranked_match boolean
---@field public tdm_scheme_id number
---@field public is_finish_tdm_scheme boolean
---@field public is_observer boolean
---@field public observer_id number
---@field public sol_rank_score number
---@field public sol_rank_level number
---@field public sol_attended boolean
---@field public isolation_tag number
---@field public sol_season_match_count number
---@field public sol_season_rank_count number
---@field public use_rental_props boolean
---@field public enable_replay boolean
---@field public tdm_rank_score number
---@field public tdm_rank_level number
---@field public player_label pb_PlayerLabel
---@field public reputation_level number
---@field public tdm_detail pb_TDMDsDetail
---@field public arena_cal_info pb_ArenaPlayerMatchCalInfo
---@field public tdm_rank_max_score number
---@field public punish_replay_begin_time number
---@field public punish_replay_report_cnts number
---@field public tdm_lost_days number
---@field public tdm_last_rank_max_score number

---@return pb_MatchPlayerInfo
function pb.MatchPlayerInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PlayerLabel = {
    sol_match_rank_preference_pc = 0,
    sol_match_rank_preference_mobile = 0,
    sol_combat_density_pc = 0,
    sol_combat_density_mobile = 0,
    sol_combat_density_player_pc = 0,
    sol_combat_density_player_mobile = 0,
    sol_combat_density_ai_pc = 0,
    sol_combat_density_ai_mobile = 0,
    social_preference_pc = 0,
    social_preference_mobile = 0,
    sol_team_match_preference_pc = 0,
    sol_team_match_preference_mobile = 0,
    sol_collaborative_action_preference_pc = 0,
    sol_collaborative_action_preference_mobile = 0,
    sol_microphone_motive = 0,
    sol_microphone_time = 0,
    sol_typing_prefernce = 0,
    sol_rescue_ability = 0,
    sol_revive_ability = 0,
    sol_carry_in_equip_level = 0,
}
pb.__pb_PlayerLabel.__name = "PlayerLabel"
pb.__pb_PlayerLabel.__index = pb.__pb_PlayerLabel
pb.__pb_PlayerLabel.__pairs = __pb_pairs

pb.PlayerLabel = { __name = "PlayerLabel", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PlayerLabel : ProtoBase
---@field public sol_match_rank_preference_pc number
---@field public sol_match_rank_preference_mobile number
---@field public sol_combat_density_pc number
---@field public sol_combat_density_mobile number
---@field public sol_combat_density_player_pc number
---@field public sol_combat_density_player_mobile number
---@field public sol_combat_density_ai_pc number
---@field public sol_combat_density_ai_mobile number
---@field public social_preference_pc number
---@field public social_preference_mobile number
---@field public sol_team_match_preference_pc number
---@field public sol_team_match_preference_mobile number
---@field public sol_collaborative_action_preference_pc number
---@field public sol_collaborative_action_preference_mobile number
---@field public sol_microphone_motive number
---@field public sol_microphone_time number
---@field public sol_typing_prefernce number
---@field public sol_rescue_ability number
---@field public sol_revive_ability number
---@field public sol_carry_in_equip_level number

---@return pb_PlayerLabel
function pb.PlayerLabel:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_SOLPlayerMatchCalInfo = {
    player_id = 0,
    course_id = 0,
    scheme_id = 0,
    skill_score = 0,
    gm_loot_style = 0,
    glicko_rating = 0,
    glicko_rating_dev = 0,
    deposit_asset = 0,
    sol_match_cnt = 0,
    streak_count = 0,
    streak_loot_count = 0,
    sol_match_course_cnt = 0,
    sol_total_cnt = 0,
    gm_course_id = 0,
    gm_scheme_id = 0,
    gm_model_score = 0,
    sol_course_difficult_cnt = 0,
    deposit_asset_no_virtual_prop = 0,
    total_lose_match_count = 0,
    system_control_lose_count = 0,
    old_glicko_rating = 0,
    old_glicko_rating_dev = 0,
    gm_force_course_id = 0,
    sol_rank_match_count = 0,
    gm_course_type = 0,
    gm_force_course_type = 0,
    glicko_after_adjust = 0,
    rank_score_after_adjust = 0,
    gm_force_scheme_id = 0,
    gm_force_max_wait_time = false,
    glicko_adjust_value_by_rule = 0,
    rank_adjust_value_by_rule = 0,
    glicko_adjust_value_by_back = 0,
    rank_adjust_value_by_back = 0,
    gm_glicko_adjust_value = 0,
    gm_rank_adjust_value = 0,
    gm_medium_scheme_id = 0,
    last_bankrupt_timestamp = 0,
    avg_begin_game_price = 0,
    lose_price = 0,
    afk = 0,
    last_lose_price = 0,
    warmMatchCountToday = 0,
    isReachWarmLevel2Limit = false,
    loss_contact_accepted = false,
    continues_no_brick_match_cnt = 0,
    enable_replay = false,
    punish_replay_begin_time = 0,
    avg_loot_price = 0,
    avg_loot_expected_price = 0,
    avg_contract_num = 0,
    equip_price = 0,
    avg_fighting_with_player_num = 0,
    avg_fighting_with_ai_num = 0,
    Last24HourTotalChangePrice = 0,
    outfit_price = 0,
    equip_level = 0,
    easy_equip_isolation_for_newbie = false,
    easy_equip_isolation_for_back = false,
    player_back_type = 0,
    player_type = 0,
    tide_type = 0,
    commonly_used_plat = 0,
    drop_logic_id = 0,
    drop_buff_id = 0,
}
pb.__pb_SOLPlayerMatchCalInfo.__name = "SOLPlayerMatchCalInfo"
pb.__pb_SOLPlayerMatchCalInfo.__index = pb.__pb_SOLPlayerMatchCalInfo
pb.__pb_SOLPlayerMatchCalInfo.__pairs = __pb_pairs

pb.SOLPlayerMatchCalInfo = { __name = "SOLPlayerMatchCalInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_SOLPlayerMatchCalInfo : ProtoBase
---@field public player_id number
---@field public course_id number
---@field public scheme_id number
---@field public skill_score number
---@field public gm_loot_style number
---@field public glicko_rating number
---@field public glicko_rating_dev number
---@field public deposit_asset number
---@field public sol_match_cnt number
---@field public streak_count number
---@field public streak_loot_count number
---@field public sol_match_course_cnt number
---@field public sol_total_cnt number
---@field public gm_course_id number
---@field public gm_scheme_id number
---@field public gm_model_score number
---@field public sol_course_difficult_cnt number
---@field public deposit_asset_no_virtual_prop number
---@field public total_lose_match_count number
---@field public system_control_lose_count number
---@field public old_glicko_rating number
---@field public old_glicko_rating_dev number
---@field public gm_force_course_id number
---@field public sol_rank_match_count number
---@field public gm_course_type number
---@field public gm_force_course_type number
---@field public glicko_after_adjust number
---@field public rank_score_after_adjust number
---@field public gm_force_scheme_id number
---@field public gm_force_max_wait_time boolean
---@field public glicko_adjust_value_by_rule number
---@field public rank_adjust_value_by_rule number
---@field public glicko_adjust_value_by_back number
---@field public rank_adjust_value_by_back number
---@field public gm_glicko_adjust_value number
---@field public gm_rank_adjust_value number
---@field public gm_medium_scheme_id number
---@field public last_bankrupt_timestamp number
---@field public model_is_scheme_list boolean[]
---@field public model_kill_boss_list number[]
---@field public model_kill_player_list number[]
---@field public model_game_time_list number[]
---@field public model_total_loot_price_list number[]
---@field public model_price_change_list number[]
---@field public model_escaped_list boolean[]
---@field public model_begin_game_price_list number[]
---@field public price_change number[]
---@field public avg_begin_game_price number
---@field public lose_price number
---@field public afk number
---@field public last_lose_price number
---@field public scheme_cnt_tab pb_SOLPlayerMatchCalInfo.SchemeCntTabEntry[]
---@field public warmMatchCountToday number
---@field public isReachWarmLevel2Limit boolean
---@field public loss_contact_accepted boolean
---@field public continues_no_brick_match_cnt number
---@field public cdp_stat_map pb_SOLPlayerMatchCalInfo.CdpStatMapEntry[]
---@field public enable_replay boolean
---@field public punish_replay_begin_time number
---@field public avg_loot_price number
---@field public avg_loot_expected_price number
---@field public avg_contract_num number
---@field public equip_price number
---@field public avg_fighting_with_player_num number
---@field public avg_fighting_with_ai_num number
---@field public Last24HourTotalChangePrice number
---@field public outfit_price number
---@field public equip_level number
---@field public easy_equip_isolation_for_newbie boolean
---@field public easy_equip_isolation_for_back boolean
---@field public player_back_type number
---@field public player_type number
---@field public tide_type number
---@field public commonly_used_plat number
---@field public drop_logic_id number
---@field public drop_buff_id number
---@field public drop_counters pb_MatchSOLDropCounter[]

---@return pb_SOLPlayerMatchCalInfo
function pb.SOLPlayerMatchCalInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ArenaPlayerMatchCalInfo = {
    glicko_rating_pc = 0,
    glicko_rating_dev_pc = 0,
    glicko_rating_mobile = 0,
    glicko_rating_dev_mobile = 0,
    glicko_rating_console = 0,
    glicko_rating_dev_console = 0,
    winlose_elo_pc = 0,
    winlose_elo_mobile = 0,
    winlose_elo_console = 0,
    fight_elo_pc = 0,
    fight_elo_mobile = 0,
    fight_elo_console = 0,
    fight_elo_with_hotstart_pc = 0,
    fight_elo_with_hotstart_mobile = 0,
    fight_elo_with_hotstart_console = 0,
    sys_team_id = 0,
}
pb.__pb_ArenaPlayerMatchCalInfo.__name = "ArenaPlayerMatchCalInfo"
pb.__pb_ArenaPlayerMatchCalInfo.__index = pb.__pb_ArenaPlayerMatchCalInfo
pb.__pb_ArenaPlayerMatchCalInfo.__pairs = __pb_pairs

pb.ArenaPlayerMatchCalInfo = { __name = "ArenaPlayerMatchCalInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ArenaPlayerMatchCalInfo : ProtoBase
---@field public glicko_rating_pc number
---@field public glicko_rating_dev_pc number
---@field public glicko_rating_mobile number
---@field public glicko_rating_dev_mobile number
---@field public glicko_rating_console number
---@field public glicko_rating_dev_console number
---@field public winlose_elo_pc number
---@field public winlose_elo_mobile number
---@field public winlose_elo_console number
---@field public fight_elo_pc number
---@field public fight_elo_mobile number
---@field public fight_elo_console number
---@field public fight_elo_with_hotstart_pc number
---@field public fight_elo_with_hotstart_mobile number
---@field public fight_elo_with_hotstart_console number
---@field public sys_team_id number

---@return pb_ArenaPlayerMatchCalInfo
function pb.ArenaPlayerMatchCalInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_SupplyNumsInfo = {
    prop_id = 0,
    num = 0,
}
pb.__pb_SupplyNumsInfo.__name = "SupplyNumsInfo"
pb.__pb_SupplyNumsInfo.__index = pb.__pb_SupplyNumsInfo
pb.__pb_SupplyNumsInfo.__pairs = __pb_pairs

pb.SupplyNumsInfo = { __name = "SupplyNumsInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_SupplyNumsInfo : ProtoBase
---@field public prop_id number
---@field public num number

---@return pb_SupplyNumsInfo
function pb.SupplyNumsInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_SupplyConfigInfo = {
    config_id = 0,
    config_name = "",
    config_type = 0,
    modify_time = 0,
}
pb.__pb_SupplyConfigInfo.__name = "SupplyConfigInfo"
pb.__pb_SupplyConfigInfo.__index = pb.__pb_SupplyConfigInfo
pb.__pb_SupplyConfigInfo.__pairs = __pb_pairs

pb.SupplyConfigInfo = { __name = "SupplyConfigInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_SupplyConfigInfo : ProtoBase
---@field public config_id number
---@field public config_name string
---@field public config_type number
---@field public modify_time number
---@field public supplys pb_SupplyNumsInfo[]

---@return pb_SupplyConfigInfo
function pb.SupplyConfigInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GspAchievementInfo = {
    id = 0,
}
pb.__pb_GspAchievementInfo.__name = "GspAchievementInfo"
pb.__pb_GspAchievementInfo.__index = pb.__pb_GspAchievementInfo
pb.__pb_GspAchievementInfo.__pairs = __pb_pairs

pb.GspAchievementInfo = { __name = "GspAchievementInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GspAchievementInfo : ProtoBase
---@field public id number
---@field public score_map_key_array string[]
---@field public score_map_value_array string[]

---@return pb_GspAchievementInfo
function pb.GspAchievementInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GspMatchInfoNew = {
    room_id = 0,
    map_id = 0,
    match_type = 0,
    match_subtype = 0,
    match_mode = 0,
    team_size = 0,
    start_time = 0,
    player_count = 0,
    ai_count = 0,
    boss_count = 0,
    is_guide = false,
}
pb.__pb_GspMatchInfoNew.__name = "GspMatchInfoNew"
pb.__pb_GspMatchInfoNew.__index = pb.__pb_GspMatchInfoNew
pb.__pb_GspMatchInfoNew.__pairs = __pb_pairs

pb.GspMatchInfoNew = { __name = "GspMatchInfoNew", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GspMatchInfoNew : ProtoBase
---@field public room_id number
---@field public map_id number
---@field public match_type number
---@field public match_subtype number
---@field public match_mode number
---@field public team_size number
---@field public start_time number
---@field public player_count number
---@field public ai_count number
---@field public boss_count number
---@field public is_guide boolean
---@field public mode_info pb_MatchModeInfo

---@return pb_GspMatchInfoNew
function pb.GspMatchInfoNew:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GspTeamInfo = {
    team_id = 0,
    teammate_count = 0,
    team_rank = 0,
}
pb.__pb_GspTeamInfo.__name = "GspTeamInfo"
pb.__pb_GspTeamInfo.__index = pb.__pb_GspTeamInfo
pb.__pb_GspTeamInfo.__pairs = __pb_pairs

pb.GspTeamInfo = { __name = "GspTeamInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GspTeamInfo : ProtoBase
---@field public team_id number
---@field public teammate_count number
---@field public team_rank number
---@field public team_achievement_array pb_GspAchievementInfo[]

---@return pb_GspTeamInfo
function pb.GspTeamInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GspPlayerBasicInfo = {
    player_id = 0,
    game_nick = "",
    gender = 0,
    avatar = "",
    background = "",
    level = 0,
    grade = 0,
    is_scav = false,
    team_seqno = 0,
    has_title = false,
    title = 0,
    be_praised_num = 0,
    rank_match_score = 0,
    plat_id = 0,
    account_type = 0,
}
pb.__pb_GspPlayerBasicInfo.__name = "GspPlayerBasicInfo"
pb.__pb_GspPlayerBasicInfo.__index = pb.__pb_GspPlayerBasicInfo
pb.__pb_GspPlayerBasicInfo.__pairs = __pb_pairs

pb.GspPlayerBasicInfo = { __name = "GspPlayerBasicInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GspPlayerBasicInfo : ProtoBase
---@field public player_id number
---@field public game_nick string
---@field public gender number
---@field public avatar string
---@field public background string
---@field public level number
---@field public grade number
---@field public is_scav boolean
---@field public team_seqno number
---@field public has_title boolean
---@field public title number
---@field public be_praised_num number
---@field public rank_match_score number
---@field public plat_id number
---@field public account_type number

---@return pb_GspPlayerBasicInfo
function pb.GspPlayerBasicInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GspAIBasicInfo = {
    player_id = 0,
    game_nick = "",
    gender = 0,
    level = 0,
    ai_lab_level = 0,
    rank_match_score = 0,
}
pb.__pb_GspAIBasicInfo.__name = "GspAIBasicInfo"
pb.__pb_GspAIBasicInfo.__index = pb.__pb_GspAIBasicInfo
pb.__pb_GspAIBasicInfo.__pairs = __pb_pairs

pb.GspAIBasicInfo = { __name = "GspAIBasicInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GspAIBasicInfo : ProtoBase
---@field public player_id number
---@field public game_nick string
---@field public gender number
---@field public tag_array string[]
---@field public level number
---@field public ai_lab_level number
---@field public rank_match_score number

---@return pb_GspAIBasicInfo
function pb.GspAIBasicInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GspPlayerPosition = {
    x = 0,
    y = 0,
    z = 0,
}
pb.__pb_GspPlayerPosition.__name = "GspPlayerPosition"
pb.__pb_GspPlayerPosition.__index = pb.__pb_GspPlayerPosition
pb.__pb_GspPlayerPosition.__pairs = __pb_pairs

pb.GspPlayerPosition = { __name = "GspPlayerPosition", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GspPlayerPosition : ProtoBase
---@field public x number
---@field public y number
---@field public z number

---@return pb_GspPlayerPosition
function pb.GspPlayerPosition:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GspDamageInfo = {
    timestamp = 0,
    equipment_recorded = false,
    equipment_price = 0,
    has_weapon = false,
    weapon = 0,
    has_ammo = false,
    ammo = 0,
    hit_distance = 0,
    damage = 0,
    body_part = 0,
    enemy_equipment_id = 0,
    health_max = 0,
    health = 0,
}
pb.__pb_GspDamageInfo.__name = "GspDamageInfo"
pb.__pb_GspDamageInfo.__index = pb.__pb_GspDamageInfo
pb.__pb_GspDamageInfo.__pairs = __pb_pairs

pb.GspDamageInfo = { __name = "GspDamageInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GspDamageInfo : ProtoBase
---@field public timestamp number
---@field public position pb_GspPlayerPosition
---@field public equipment_recorded boolean
---@field public equipment pb_EquipPosition[]
---@field public equipment_price number
---@field public has_weapon boolean
---@field public weapon number
---@field public weapon_prop pb_PropInfo
---@field public has_ammo boolean
---@field public ammo number
---@field public hit_distance number
---@field public damage number
---@field public body_part number
---@field public enemy_equipment_id number
---@field public health_max number
---@field public health number

---@return pb_GspDamageInfo
function pb.GspDamageInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GspAccidentInfo = {
    timestamp = 0,
    accident_type = 0,
    body_part = 0,
}
pb.__pb_GspAccidentInfo.__name = "GspAccidentInfo"
pb.__pb_GspAccidentInfo.__index = pb.__pb_GspAccidentInfo
pb.__pb_GspAccidentInfo.__pairs = __pb_pairs

pb.GspAccidentInfo = { __name = "GspAccidentInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GspAccidentInfo : ProtoBase
---@field public timestamp number
---@field public position pb_GspPlayerPosition
---@field public accident_type number
---@field public body_part number

---@return pb_GspAccidentInfo
function pb.GspAccidentInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GspPlayerDeathInfo = {
    reason = 0,
    timestamp = 0,
}
pb.__pb_GspPlayerDeathInfo.__name = "GspPlayerDeathInfo"
pb.__pb_GspPlayerDeathInfo.__index = pb.__pb_GspPlayerDeathInfo
pb.__pb_GspPlayerDeathInfo.__pairs = __pb_pairs

pb.GspPlayerDeathInfo = { __name = "GspPlayerDeathInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GspPlayerDeathInfo : ProtoBase
---@field public reason number
---@field public timestamp number
---@field public position pb_GspPlayerPosition
---@field public damage pb_GspDamageInfo
---@field public accident pb_GspAccidentInfo
---@field public player pb_GspPlayerBasicInfo
---@field public ai pb_GspAIBasicInfo
---@field public boss pb_GspAIBasicInfo

---@return pb_GspPlayerDeathInfo
function pb.GspPlayerDeathInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GspMatchEvent = {
    type = 0,
    timestamp = 0,
    kill_count = 0,
    total_price = 0,
    produced_price = 0,
    KM_traveled = 0,
}
pb.__pb_GspMatchEvent.__name = "GspMatchEvent"
pb.__pb_GspMatchEvent.__index = pb.__pb_GspMatchEvent
pb.__pb_GspMatchEvent.__pairs = __pb_pairs

pb.GspMatchEvent = { __name = "GspMatchEvent", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GspMatchEvent : ProtoBase
---@field public type number
---@field public timestamp number
---@field public position pb_GspPlayerPosition
---@field public param_map_key_array string[]
---@field public param_map_value_array string[]
---@field public kill_count number
---@field public total_price number
---@field public produced_price number
---@field public KM_traveled number

---@return pb_GspMatchEvent
function pb.GspMatchEvent:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GspPlayerKillInfo = {
    type = 0,
    timestamp = 0,
    total_damage = 0,
    enemy_type = 0,
}
pb.__pb_GspPlayerKillInfo.__name = "GspPlayerKillInfo"
pb.__pb_GspPlayerKillInfo.__index = pb.__pb_GspPlayerKillInfo
pb.__pb_GspPlayerKillInfo.__pairs = __pb_pairs

pb.GspPlayerKillInfo = { __name = "GspPlayerKillInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GspPlayerKillInfo : ProtoBase
---@field public type number
---@field public timestamp number
---@field public position pb_GspPlayerPosition
---@field public damage pb_GspDamageInfo
---@field public total_damage number
---@field public enemy_type number
---@field public player pb_GspPlayerBasicInfo
---@field public ai pb_GspAIBasicInfo
---@field public boss pb_GspAIBasicInfo
---@field public damage_list pb_GspDamageInfo[]
---@field public enemy_damage_list pb_GspDamageInfo[]

---@return pb_GspPlayerKillInfo
function pb.GspPlayerKillInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GspPlayerMatchStatNew = {
    blood_loss = 0,
    HP_healed = 0,
    dehydration_times = 0,
    drink_consumed = 0,
    food_consumed = 0,
    KM_traveled = 0,
    bodies_looted = 0,
    weapons_found = 0,
    accessories_found = 0,
    provisions_found = 0,
    rescue_count = 0,
    revive_count = 0,
    unlock_times = 0,
    total_damage = 0,
    total_price = 0,
    safebox_price = 0,
    produced_price = 0,
    actual_produced_price = 0,
    resurgence_count = 0,
}
pb.__pb_GspPlayerMatchStatNew.__name = "GspPlayerMatchStatNew"
pb.__pb_GspPlayerMatchStatNew.__index = pb.__pb_GspPlayerMatchStatNew
pb.__pb_GspPlayerMatchStatNew.__pairs = __pb_pairs

pb.GspPlayerMatchStatNew = { __name = "GspPlayerMatchStatNew", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GspPlayerMatchStatNew : ProtoBase
---@field public blood_loss number
---@field public HP_healed number
---@field public dehydration_times number
---@field public drink_consumed number
---@field public food_consumed number
---@field public KM_traveled number
---@field public bodies_looted number
---@field public weapons_found number
---@field public accessories_found number
---@field public provisions_found number
---@field public rescue_count number
---@field public revive_count number
---@field public unlock_times number
---@field public total_damage number
---@field public total_price number
---@field public safebox_price number
---@field public produced_price number
---@field public actual_produced_price number
---@field public resurgence_count number

---@return pb_GspPlayerMatchStatNew
function pb.GspPlayerMatchStatNew:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GspPlayingPlayerStatus = {
}
pb.__pb_GspPlayingPlayerStatus.__name = "GspPlayingPlayerStatus"
pb.__pb_GspPlayingPlayerStatus.__index = pb.__pb_GspPlayingPlayerStatus
pb.__pb_GspPlayingPlayerStatus.__pairs = __pb_pairs

pb.GspPlayingPlayerStatus = { __name = "GspPlayingPlayerStatus", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GspPlayingPlayerStatus : ProtoBase

---@return pb_GspPlayingPlayerStatus
function pb.GspPlayingPlayerStatus:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GspEndGamePlayerStatus = {
    type = 0,
    extraction_point = 0,
    extraction_location = "",
    play_time = 0,
    has_main_weapon = false,
    main_weapon = 0,
    performance_score = 0,
    individual_rank = 0,
    success_escape_index = 0,
    blue_print_special_id = 0,
    blue_print_type = 0,
    blue_print_price = 0,
    contract_quest_price = 0,
    carry_out_new_props_price = 0,
    friend_add_exp_buf = 0,
    safehouse_add_exp_buf = 0,
    cost_price = 0,
    carry_out_profit_price = 0,
    account_add_exp_buf = 0,
    cybercafe_settlement_buff = 0,
    carry_out_without_teammate_price = 0,
    university_settlement_buff = 0,
}
pb.__pb_GspEndGamePlayerStatus.__name = "GspEndGamePlayerStatus"
pb.__pb_GspEndGamePlayerStatus.__index = pb.__pb_GspEndGamePlayerStatus
pb.__pb_GspEndGamePlayerStatus.__pairs = __pb_pairs

pb.GspEndGamePlayerStatus = { __name = "GspEndGamePlayerStatus", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GspEndGamePlayerStatus : ProtoBase
---@field public type number
---@field public extraction_point number
---@field public death_info pb_GspPlayerDeathInfo
---@field public extraction_location string
---@field public play_time number
---@field public has_main_weapon boolean
---@field public main_weapon number
---@field public match_log pb_GspMatchEvent[]
---@field public match_stat pb_GspPlayerMatchStatNew
---@field public performance_score number
---@field public individual_rank number
---@field public attr_array pb_DSAttrValue[]
---@field public buff_array pb_AttrBuff[]
---@field public carry_in_props pb_EquipPosition[]
---@field public carry_out_props pb_EquipPosition[]
---@field public kill_array pb_GspPlayerKillInfo[]
---@field public achievement_array pb_GspAchievementInfo[]
---@field public C_price_values number[]
---@field public season_change_info pb_SeasonChangeInfo
---@field public shoot_down_by_enemy_array pb_GspPlayerKillInfo[]
---@field public success_escape_index number
---@field public weapon_change pb_WeaponChange[]
---@field public carry_out_health_list pb_EquipHealth[]
---@field public hero pb_DSHero
---@field public blue_print_special_id number
---@field public blue_print_type number
---@field public blue_print_price number
---@field public contract_quest_price number
---@field public carry_out_new_props_price number
---@field public friend_add_exp_buf number
---@field public safehouse_add_exp_buf number
---@field public cost_price number
---@field public carry_out_profit_price number
---@field public account_add_exp_buf number
---@field public cybercafe_settlement_buff number
---@field public carry_out_without_teammate_price number
---@field public university_settlement_buff number

---@return pb_GspEndGamePlayerStatus
function pb.GspEndGamePlayerStatus:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GspQuitGamePlayerStatus = {
    reason = 0,
    play_time = 0,
    has_main_weapon = false,
    main_weapon = 0,
    performance_score = 0,
    individual_rank = 0,
    friend_add_exp_buf = 0,
    safehouse_add_exp_buf = 0,
    rescue_count = 0,
    resurgence_count = 0,
    carry_out_profit_price = 0,
    cost_price = 0,
    account_add_exp_buf = 0,
    cybercafe_settlement_buff = 0,
    carry_out_without_teammate_price = 0,
    university_settlement_buff = 0,
}
pb.__pb_GspQuitGamePlayerStatus.__name = "GspQuitGamePlayerStatus"
pb.__pb_GspQuitGamePlayerStatus.__index = pb.__pb_GspQuitGamePlayerStatus
pb.__pb_GspQuitGamePlayerStatus.__pairs = __pb_pairs

pb.GspQuitGamePlayerStatus = { __name = "GspQuitGamePlayerStatus", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GspQuitGamePlayerStatus : ProtoBase
---@field public reason number
---@field public season_change_info pb_SeasonChangeInfo
---@field public play_time number
---@field public has_main_weapon boolean
---@field public main_weapon number
---@field public match_stat pb_GspPlayerMatchStatNew
---@field public performance_score number
---@field public individual_rank number
---@field public carry_in_props pb_EquipPosition[]
---@field public carry_out_props pb_EquipPosition[]
---@field public kill_array pb_GspPlayerKillInfo[]
---@field public achievement_array pb_GspAchievementInfo[]
---@field public weapon_change pb_WeaponChange[]
---@field public carry_out_health_list pb_EquipHealth[]
---@field public friend_add_exp_buf number
---@field public safehouse_add_exp_buf number
---@field public hero pb_DSHero
---@field public rescue_count number
---@field public resurgence_count number
---@field public carry_out_profit_price number
---@field public cost_price number
---@field public account_add_exp_buf number
---@field public cybercafe_settlement_buff number
---@field public carry_out_without_teammate_price number
---@field public university_settlement_buff number

---@return pb_GspQuitGamePlayerStatus
function pb.GspQuitGamePlayerStatus:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RankCurrencyChange = {
    reason = 0,
    score = 0,
}
pb.__pb_RankCurrencyChange.__name = "RankCurrencyChange"
pb.__pb_RankCurrencyChange.__index = pb.__pb_RankCurrencyChange
pb.__pb_RankCurrencyChange.__pairs = __pb_pairs

pb.RankCurrencyChange = { __name = "RankCurrencyChange", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RankCurrencyChange : ProtoBase
---@field public reason number
---@field public score number

---@return pb_RankCurrencyChange
function pb.RankCurrencyChange:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RankCurrencyResult = {
    coefficient = 0,
    raw_value = 0,
    conf_limit_pre_match = 0,
    pre_weekly_value = 0,
    conf_limit_pre_weekly = 0,
    conf_daily_first_escape = 0,
    actual_value = 0,
    is_daily_first_escape = false,
    is_limit_pre_match = false,
    is_pre_week_limit = false,
}
pb.__pb_RankCurrencyResult.__name = "RankCurrencyResult"
pb.__pb_RankCurrencyResult.__index = pb.__pb_RankCurrencyResult
pb.__pb_RankCurrencyResult.__pairs = __pb_pairs

pb.RankCurrencyResult = { __name = "RankCurrencyResult", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RankCurrencyResult : ProtoBase
---@field public coefficient number
---@field public raw_value number
---@field public conf_limit_pre_match number
---@field public pre_weekly_value number
---@field public conf_limit_pre_weekly number
---@field public conf_daily_first_escape number
---@field public actual_value number
---@field public currency_changes pb_RankCurrencyChange[]
---@field public is_daily_first_escape boolean
---@field public is_limit_pre_match boolean
---@field public is_pre_week_limit boolean

---@return pb_RankCurrencyResult
function pb.RankCurrencyResult:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GspPlayerGameStatus = {
    type = 0,
    mode_id = 0,
    money_paper = 0,
    rank_match_score = 0,
    is_ranked_match = false,
    rank_match_score_delta = 0,
    result = 0,
    trigger_rank_shield = false,
    sol_double_rank_multiple_value = 0,
    team_mate_price = 0,
    assist_cnt = 0,
    exactly_leave = 0,
    safebox_skin_id = 0,
}
pb.__pb_GspPlayerGameStatus.__name = "GspPlayerGameStatus"
pb.__pb_GspPlayerGameStatus.__index = pb.__pb_GspPlayerGameStatus
pb.__pb_GspPlayerGameStatus.__pairs = __pb_pairs

pb.GspPlayerGameStatus = { __name = "GspPlayerGameStatus", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GspPlayerGameStatus : ProtoBase
---@field public type number
---@field public basic_info pb_GspPlayerBasicInfo
---@field public mode_id number
---@field public playing pb_GspPlayingPlayerStatus
---@field public end_game pb_GspEndGamePlayerStatus
---@field public quit_game pb_GspQuitGamePlayerStatus
---@field public money_paper number
---@field public rank_score_changes pb_RankMatchScoreChange[]
---@field public rank_match_score number
---@field public is_ranked_match boolean
---@field public rank_match_score_delta number
---@field public game_achievements pb_DsGameAchievement[]
---@field public unlock_mp_guns number[]
---@field public result number
---@field public mystical_skins pb_PropInfo[]
---@field public trigger_rank_shield boolean
---@field public rank_match_shield pb_RankShield
---@field public sol_double_rank_multiple_value number
---@field public team_mate_price number
---@field public assist_cnt number
---@field public mystical_pendants pb_PropInfo[]
---@field public rank_currency_result pb_RankCurrencyResult
---@field public exactly_leave number
---@field public safebox_skin_id number

---@return pb_GspPlayerGameStatus
function pb.GspPlayerGameStatus:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GspPlayingTeamMateStatus = {
    play_time = 0,
}
pb.__pb_GspPlayingTeamMateStatus.__name = "GspPlayingTeamMateStatus"
pb.__pb_GspPlayingTeamMateStatus.__index = pb.__pb_GspPlayingTeamMateStatus
pb.__pb_GspPlayingTeamMateStatus.__pairs = __pb_pairs

pb.GspPlayingTeamMateStatus = { __name = "GspPlayingTeamMateStatus", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GspPlayingTeamMateStatus : ProtoBase
---@field public play_time number
---@field public carry_in_props pb_EquipPosition[]
---@field public current_props pb_EquipPosition[]
---@field public hero pb_DSHero

---@return pb_GspPlayingTeamMateStatus
function pb.GspPlayingTeamMateStatus:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GspEndGameTeamMateStatus = {
    type = 0,
    play_time = 0,
    has_main_weapon = false,
    main_weapon = 0,
    performance_score = 0,
    individual_rank = 0,
    rescue_count = 0,
    resurgence_count = 0,
    carry_out_profit_price = 0,
    cost_price = 0,
}
pb.__pb_GspEndGameTeamMateStatus.__name = "GspEndGameTeamMateStatus"
pb.__pb_GspEndGameTeamMateStatus.__index = pb.__pb_GspEndGameTeamMateStatus
pb.__pb_GspEndGameTeamMateStatus.__pairs = __pb_pairs

pb.GspEndGameTeamMateStatus = { __name = "GspEndGameTeamMateStatus", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GspEndGameTeamMateStatus : ProtoBase
---@field public type number
---@field public play_time number
---@field public has_main_weapon boolean
---@field public main_weapon number
---@field public match_stat pb_GspPlayerMatchStatNew
---@field public performance_score number
---@field public individual_rank number
---@field public carry_in_props pb_EquipPosition[]
---@field public carry_out_props pb_EquipPosition[]
---@field public kill_array pb_GspPlayerKillInfo[]
---@field public achievement_array pb_GspAchievementInfo[]
---@field public hero pb_DSHero
---@field public rescue_count number
---@field public resurgence_count number
---@field public carry_out_profit_price number
---@field public cost_price number

---@return pb_GspEndGameTeamMateStatus
function pb.GspEndGameTeamMateStatus:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GspQuitGameTeamMateStatus = {
    reason = 0,
    play_time = 0,
    has_main_weapon = false,
    main_weapon = 0,
    performance_score = 0,
    individual_rank = 0,
    rescue_count = 0,
    resurgence_count = 0,
    carry_out_profit_price = 0,
    cost_price = 0,
}
pb.__pb_GspQuitGameTeamMateStatus.__name = "GspQuitGameTeamMateStatus"
pb.__pb_GspQuitGameTeamMateStatus.__index = pb.__pb_GspQuitGameTeamMateStatus
pb.__pb_GspQuitGameTeamMateStatus.__pairs = __pb_pairs

pb.GspQuitGameTeamMateStatus = { __name = "GspQuitGameTeamMateStatus", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GspQuitGameTeamMateStatus : ProtoBase
---@field public reason number
---@field public play_time number
---@field public has_main_weapon boolean
---@field public main_weapon number
---@field public match_stat pb_GspPlayerMatchStatNew
---@field public performance_score number
---@field public individual_rank number
---@field public carry_in_props pb_EquipPosition[]
---@field public carry_out_props pb_EquipPosition[]
---@field public kill_array pb_GspPlayerKillInfo[]
---@field public achievement_array pb_GspAchievementInfo[]
---@field public hero pb_DSHero
---@field public rescue_count number
---@field public resurgence_count number
---@field public carry_out_profit_price number
---@field public cost_price number

---@return pb_GspQuitGameTeamMateStatus
function pb.GspQuitGameTeamMateStatus:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GspTeamMateGameStatus = {
    type = 0,
    mode_id = 0,
}
pb.__pb_GspTeamMateGameStatus.__name = "GspTeamMateGameStatus"
pb.__pb_GspTeamMateGameStatus.__index = pb.__pb_GspTeamMateGameStatus
pb.__pb_GspTeamMateGameStatus.__pairs = __pb_pairs

pb.GspTeamMateGameStatus = { __name = "GspTeamMateGameStatus", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GspTeamMateGameStatus : ProtoBase
---@field public type number
---@field public basic_info pb_GspPlayerBasicInfo
---@field public mode_id number
---@field public playing pb_GspPlayingTeamMateStatus
---@field public end_game pb_GspEndGameTeamMateStatus
---@field public quit_game pb_GspQuitGameTeamMateStatus
---@field public ai_info pb_GspAIBasicInfo

---@return pb_GspTeamMateGameStatus
function pb.GspTeamMateGameStatus:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GspPVEMissionInfo = {
    room_id = 0,
    match_type = 0,
    match_subtype = 0,
    match_mode = 0,
    team_size = 0,
    map_id = 0,
    start_time = 0,
    player_count = 0,
}
pb.__pb_GspPVEMissionInfo.__name = "GspPVEMissionInfo"
pb.__pb_GspPVEMissionInfo.__index = pb.__pb_GspPVEMissionInfo
pb.__pb_GspPVEMissionInfo.__pairs = __pb_pairs

pb.GspPVEMissionInfo = { __name = "GspPVEMissionInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GspPVEMissionInfo : ProtoBase
---@field public room_id number
---@field public match_type number
---@field public match_subtype number
---@field public match_mode number
---@field public team_size number
---@field public map_id number
---@field public start_time number
---@field public player_count number

---@return pb_GspPVEMissionInfo
function pb.GspPVEMissionInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GspPVEMissionStat = {
    kill_count = 0,
    total_damage = 0,
    death_count = 0,
    task_count = 0,
}
pb.__pb_GspPVEMissionStat.__name = "GspPVEMissionStat"
pb.__pb_GspPVEMissionStat.__index = pb.__pb_GspPVEMissionStat
pb.__pb_GspPVEMissionStat.__pairs = __pb_pairs

pb.GspPVEMissionStat = { __name = "GspPVEMissionStat", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GspPVEMissionStat : ProtoBase
---@field public kill_count number
---@field public total_damage number
---@field public death_count number
---@field public task_count number

---@return pb_GspPVEMissionStat
function pb.GspPVEMissionStat:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GspPVEEndGamePlayerStatus = {
    type = 0,
    play_time = 0,
    performance_score = 0,
    individual_rank = 0,
}
pb.__pb_GspPVEEndGamePlayerStatus.__name = "GspPVEEndGamePlayerStatus"
pb.__pb_GspPVEEndGamePlayerStatus.__index = pb.__pb_GspPVEEndGamePlayerStatus
pb.__pb_GspPVEEndGamePlayerStatus.__pairs = __pb_pairs

pb.GspPVEEndGamePlayerStatus = { __name = "GspPVEEndGamePlayerStatus", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GspPVEEndGamePlayerStatus : ProtoBase
---@field public type number
---@field public play_time number
---@field public performance_score number
---@field public individual_rank number
---@field public kill_array pb_GspPlayerKillInfo[]
---@field public mission_stat pb_GspPVEMissionStat
---@field public achievement_array pb_GspAchievementInfo[]
---@field public first_time_award_array pb_PropInfo[]
---@field public daily_award_array pb_PropInfo[]
---@field public season_change_info pb_SeasonChangeInfo

---@return pb_GspPVEEndGamePlayerStatus
function pb.GspPVEEndGamePlayerStatus:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GspPVEQuitGamePlayerStatus = {
    play_time = 0,
}
pb.__pb_GspPVEQuitGamePlayerStatus.__name = "GspPVEQuitGamePlayerStatus"
pb.__pb_GspPVEQuitGamePlayerStatus.__index = pb.__pb_GspPVEQuitGamePlayerStatus
pb.__pb_GspPVEQuitGamePlayerStatus.__pairs = __pb_pairs

pb.GspPVEQuitGamePlayerStatus = { __name = "GspPVEQuitGamePlayerStatus", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GspPVEQuitGamePlayerStatus : ProtoBase
---@field public play_time number
---@field public achievement_array pb_GspAchievementInfo[]
---@field public season_change_info pb_SeasonChangeInfo

---@return pb_GspPVEQuitGamePlayerStatus
function pb.GspPVEQuitGamePlayerStatus:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GspPVEPlayingGamePlayerStatus = {
}
pb.__pb_GspPVEPlayingGamePlayerStatus.__name = "GspPVEPlayingGamePlayerStatus"
pb.__pb_GspPVEPlayingGamePlayerStatus.__index = pb.__pb_GspPVEPlayingGamePlayerStatus
pb.__pb_GspPVEPlayingGamePlayerStatus.__pairs = __pb_pairs

pb.GspPVEPlayingGamePlayerStatus = { __name = "GspPVEPlayingGamePlayerStatus", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GspPVEPlayingGamePlayerStatus : ProtoBase

---@return pb_GspPVEPlayingGamePlayerStatus
function pb.GspPVEPlayingGamePlayerStatus:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GspPVEPlayerStatus = {
    type = 0,
}
pb.__pb_GspPVEPlayerStatus.__name = "GspPVEPlayerStatus"
pb.__pb_GspPVEPlayerStatus.__index = pb.__pb_GspPVEPlayerStatus
pb.__pb_GspPVEPlayerStatus.__pairs = __pb_pairs

pb.GspPVEPlayerStatus = { __name = "GspPVEPlayerStatus", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GspPVEPlayerStatus : ProtoBase
---@field public type number
---@field public basic_info pb_GspPlayerBasicInfo
---@field public equiped_props pb_EquipPosition[]
---@field public end_game pb_GspPVEEndGamePlayerStatus
---@field public quit_game pb_GspPVEQuitGamePlayerStatus
---@field public playing pb_GspPVEPlayingGamePlayerStatus

---@return pb_GspPVEPlayerStatus
function pb.GspPVEPlayerStatus:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GspPVEEndGameTeamMateStatus = {
    type = 0,
    play_time = 0,
    performance_score = 0,
    individual_rank = 0,
}
pb.__pb_GspPVEEndGameTeamMateStatus.__name = "GspPVEEndGameTeamMateStatus"
pb.__pb_GspPVEEndGameTeamMateStatus.__index = pb.__pb_GspPVEEndGameTeamMateStatus
pb.__pb_GspPVEEndGameTeamMateStatus.__pairs = __pb_pairs

pb.GspPVEEndGameTeamMateStatus = { __name = "GspPVEEndGameTeamMateStatus", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GspPVEEndGameTeamMateStatus : ProtoBase
---@field public type number
---@field public play_time number
---@field public performance_score number
---@field public individual_rank number
---@field public kill_array pb_GspPlayerKillInfo[]
---@field public mission_stat pb_GspPVEMissionStat
---@field public achievement_array pb_GspAchievementInfo[]

---@return pb_GspPVEEndGameTeamMateStatus
function pb.GspPVEEndGameTeamMateStatus:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GspPVEQuitGameTeamMateStatus = {
    play_time = 0,
    performance_score = 0,
    individual_rank = 0,
}
pb.__pb_GspPVEQuitGameTeamMateStatus.__name = "GspPVEQuitGameTeamMateStatus"
pb.__pb_GspPVEQuitGameTeamMateStatus.__index = pb.__pb_GspPVEQuitGameTeamMateStatus
pb.__pb_GspPVEQuitGameTeamMateStatus.__pairs = __pb_pairs

pb.GspPVEQuitGameTeamMateStatus = { __name = "GspPVEQuitGameTeamMateStatus", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GspPVEQuitGameTeamMateStatus : ProtoBase
---@field public play_time number
---@field public performance_score number
---@field public individual_rank number
---@field public kill_array pb_GspPlayerKillInfo[]
---@field public mission_stat pb_GspPVEMissionStat
---@field public achievement_array pb_GspAchievementInfo[]

---@return pb_GspPVEQuitGameTeamMateStatus
function pb.GspPVEQuitGameTeamMateStatus:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GspPVEPlayingGameTeamMateStatus = {
}
pb.__pb_GspPVEPlayingGameTeamMateStatus.__name = "GspPVEPlayingGameTeamMateStatus"
pb.__pb_GspPVEPlayingGameTeamMateStatus.__index = pb.__pb_GspPVEPlayingGameTeamMateStatus
pb.__pb_GspPVEPlayingGameTeamMateStatus.__pairs = __pb_pairs

pb.GspPVEPlayingGameTeamMateStatus = { __name = "GspPVEPlayingGameTeamMateStatus", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GspPVEPlayingGameTeamMateStatus : ProtoBase

---@return pb_GspPVEPlayingGameTeamMateStatus
function pb.GspPVEPlayingGameTeamMateStatus:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GspPVETeamMateStatus = {
    type = 0,
}
pb.__pb_GspPVETeamMateStatus.__name = "GspPVETeamMateStatus"
pb.__pb_GspPVETeamMateStatus.__index = pb.__pb_GspPVETeamMateStatus
pb.__pb_GspPVETeamMateStatus.__pairs = __pb_pairs

pb.GspPVETeamMateStatus = { __name = "GspPVETeamMateStatus", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GspPVETeamMateStatus : ProtoBase
---@field public type number
---@field public basic_info pb_GspPlayerBasicInfo
---@field public equiped_props pb_EquipPosition[]
---@field public end_game pb_GspPVEEndGameTeamMateStatus
---@field public quit_game pb_GspPVEQuitGameTeamMateStatus
---@field public playing pb_GspPVEPlayingGameTeamMateStatus

---@return pb_GspPVETeamMateStatus
function pb.GspPVETeamMateStatus:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RaidPlayerData = {
    player_id = 0,
    raid_score = 0,
    raid_level = 0,
    raid_kd = 0,
    kill_num = 0,
}
pb.__pb_RaidPlayerData.__name = "RaidPlayerData"
pb.__pb_RaidPlayerData.__index = pb.__pb_RaidPlayerData
pb.__pb_RaidPlayerData.__pairs = __pb_pairs

pb.RaidPlayerData = { __name = "RaidPlayerData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RaidPlayerData : ProtoBase
---@field public player_id number
---@field public raid_score number
---@field public raid_level number
---@field public raid_kd number
---@field public kill_num number
---@field public evaluate_prop_list pb_PropInfo[]
---@field public evaluate_prize_ids number[]

---@return pb_RaidPlayerData
function pb.RaidPlayerData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RaidSettlementData = {
    play_time = 0,
}
pb.__pb_RaidSettlementData.__name = "RaidSettlementData"
pb.__pb_RaidSettlementData.__index = pb.__pb_RaidSettlementData
pb.__pb_RaidSettlementData.__pairs = __pb_pairs

pb.RaidSettlementData = { __name = "RaidSettlementData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RaidSettlementData : ProtoBase
---@field public player_array pb_RaidPlayerData[]
---@field public play_time number
---@field public weapon_change pb_WeaponChange[]

---@return pb_RaidSettlementData
function pb.RaidSettlementData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MPBagCS = {
    bag_id = 0,
    armedforce_id = 0,
    name = "",
    expert_id = 0,
    expert_bag_id = 0,
}
pb.__pb_MPBagCS.__name = "MPBagCS"
pb.__pb_MPBagCS.__index = pb.__pb_MPBagCS
pb.__pb_MPBagCS.__pairs = __pb_pairs

pb.MPBagCS = { __name = "MPBagCS", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MPBagCS : ProtoBase
---@field public bag_id number
---@field public armedforce_id number
---@field public props pb_MPPropPosCS[]
---@field public modified_props pb_PropInfo[]
---@field public name string
---@field public expert_id number
---@field public expert_bag_id number

---@return pb_MPBagCS
function pb.MPBagCS:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MPWeaponStoreCS = {
    weapon_id = 0,
}
pb.__pb_MPWeaponStoreCS.__name = "MPWeaponStoreCS"
pb.__pb_MPWeaponStoreCS.__index = pb.__pb_MPWeaponStoreCS
pb.__pb_MPWeaponStoreCS.__pairs = __pb_pairs

pb.MPWeaponStoreCS = { __name = "MPWeaponStoreCS", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MPWeaponStoreCS : ProtoBase
---@field public weapon_id number
---@field public skin_comps pb_MPSkinComponents[]
---@field public unlocked_comps number[]
---@field public global_comps number[]

---@return pb_MPWeaponStoreCS
function pb.MPWeaponStoreCS:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MPBagArmedPresetCS = {
    armedforce_id = 0,
    expert_id = 0,
    default_bag_id = 0,
    def_bag_id = 0,
    max_bag_num = 0,
}
pb.__pb_MPBagArmedPresetCS.__name = "MPBagArmedPresetCS"
pb.__pb_MPBagArmedPresetCS.__index = pb.__pb_MPBagArmedPresetCS
pb.__pb_MPBagArmedPresetCS.__pairs = __pb_pairs

pb.MPBagArmedPresetCS = { __name = "MPBagArmedPresetCS", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MPBagArmedPresetCS : ProtoBase
---@field public armedforce_id number
---@field public expert_id number
---@field public default_bag_id number
---@field public def_bag_id number
---@field public bags pb_MPBagCS[]
---@field public max_bag_num number

---@return pb_MPBagArmedPresetCS
function pb.MPBagArmedPresetCS:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PlayerCarryoutProps = {
    player_id = 0,
}
pb.__pb_PlayerCarryoutProps.__name = "PlayerCarryoutProps"
pb.__pb_PlayerCarryoutProps.__index = pb.__pb_PlayerCarryoutProps
pb.__pb_PlayerCarryoutProps.__pairs = __pb_pairs

pb.PlayerCarryoutProps = { __name = "PlayerCarryoutProps", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PlayerCarryoutProps : ProtoBase
---@field public player_id number
---@field public final_props pb_PropInfo[]

---@return pb_PlayerCarryoutProps
function pb.PlayerCarryoutProps:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PraiseItem = {
    player_id = 0,
    praise_time = 0,
}
pb.__pb_PraiseItem.__name = "PraiseItem"
pb.__pb_PraiseItem.__index = pb.__pb_PraiseItem
pb.__pb_PraiseItem.__pairs = __pb_pairs

pb.PraiseItem = { __name = "PraiseItem", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PraiseItem : ProtoBase
---@field public player_id number
---@field public praise_time number

---@return pb_PraiseItem
function pb.PraiseItem:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSHeroArmedPropStore = {
    position = 0,
}
pb.__pb_CSHeroArmedPropStore.__name = "CSHeroArmedPropStore"
pb.__pb_CSHeroArmedPropStore.__index = pb.__pb_CSHeroArmedPropStore
pb.__pb_CSHeroArmedPropStore.__pairs = __pb_pairs

pb.CSHeroArmedPropStore = { __name = "CSHeroArmedPropStore", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSHeroArmedPropStore : ProtoBase
---@field public position number
---@field public fashion pb_HeroArmedPropFashion

---@return pb_CSHeroArmedPropStore
function pb.CSHeroArmedPropStore:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSHeroArmedPropInfo = {
    store_fashion_max_num = 0,
    armed_prop_id = 0,
    cur_lottery_times = 0,
    cur_lottery_quality = 0,
    total_lottery_times = 0,
}
pb.__pb_CSHeroArmedPropInfo.__name = "CSHeroArmedPropInfo"
pb.__pb_CSHeroArmedPropInfo.__index = pb.__pb_CSHeroArmedPropInfo
pb.__pb_CSHeroArmedPropInfo.__pairs = __pb_pairs

pb.CSHeroArmedPropInfo = { __name = "CSHeroArmedPropInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSHeroArmedPropInfo : ProtoBase
---@field public cur_fashion pb_HeroArmedPropFashion
---@field public init_fashion pb_HeroArmedPropFashion
---@field public last_fashion pb_HeroArmedPropFashion
---@field public store_fashion_max_num number
---@field public store_fashion pb_CSHeroArmedPropStore[]
---@field public armed_prop_id number
---@field public cur_lottery_times number
---@field public cur_lottery_quality number
---@field public total_lottery_times number

---@return pb_CSHeroArmedPropInfo
function pb.CSHeroArmedPropInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSHero = {
    hero_id = 0,
    can_use = false,
    is_unlock = false,
    is_netbar_unlock = false,
    armed_force_id = 0,
    expert_id = 0,
    load_level = 0,
}
pb.__pb_CSHero.__name = "CSHero"
pb.__pb_CSHero.__index = pb.__pb_CSHero
pb.__pb_CSHero.__pairs = __pb_pairs

pb.CSHero = { __name = "CSHero", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSHero : ProtoBase
---@field public hero_id number
---@field public can_use boolean
---@field public is_unlock boolean
---@field public is_netbar_unlock boolean
---@field public armed_force_id number
---@field public expert_id number
---@field public hero_body pb_PropInfo
---@field public load_level number
---@field public fashion_list pb_FashionItem[]
---@field public fashion_equipped pb_HeroFashion[]
---@field public accessories pb_CSHeroAccessory[]
---@field public sol_expert_data pb_ExpertData
---@field public mp_expert_data pb_ExpertData
---@field public grow_line_data pb_CSHeroGrowLineData
---@field public challenge_data pb_CSHeroChallengeData
---@field public armed_prop_fashion_info pb_CSHeroArmedPropInfo[]
---@field public red_dot_info pb_HeroReddotInfo[]

---@return pb_CSHero
function pb.CSHero:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSHeroChallengeData = {
    seletced_num = 0,
    is_cur_selected = false,
}
pb.__pb_CSHeroChallengeData.__name = "CSHeroChallengeData"
pb.__pb_CSHeroChallengeData.__index = pb.__pb_CSHeroChallengeData
pb.__pb_CSHeroChallengeData.__pairs = __pb_pairs

pb.CSHeroChallengeData = { __name = "CSHeroChallengeData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSHeroChallengeData : ProtoBase
---@field public goal_list pb_CSHeroGrowLineGoal[]
---@field public seletced_num number
---@field public is_cur_selected boolean

---@return pb_CSHeroChallengeData
function pb.CSHeroChallengeData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSHeroAccessory = {
    is_unlock = false,
    is_selected = false,
    is_read = false,
    unlocked_timestamp = 0,
}
pb.__pb_CSHeroAccessory.__name = "CSHeroAccessory"
pb.__pb_CSHeroAccessory.__index = pb.__pb_CSHeroAccessory
pb.__pb_CSHeroAccessory.__pairs = __pb_pairs

pb.CSHeroAccessory = { __name = "CSHeroAccessory", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSHeroAccessory : ProtoBase
---@field public item pb_HeroAccessoryItem
---@field public is_unlock boolean
---@field public is_selected boolean
---@field public is_read boolean
---@field public unlocked_timestamp number

---@return pb_CSHeroAccessory
function pb.CSHeroAccessory:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ExpertData = {
}
pb.__pb_ExpertData.__name = "ExpertData"
pb.__pb_ExpertData.__index = pb.__pb_ExpertData
pb.__pb_ExpertData.__pairs = __pb_pairs

pb.ExpertData = { __name = "ExpertData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ExpertData : ProtoBase
---@field public skill pb_ExpertSkill
---@field public armed_prop1 pb_PropInfo
---@field public armed_prop2 pb_PropInfo
---@field public armed_prop_pool1 pb_ArmedforceProp[]
---@field public armed_prop_pool2 pb_ArmedforceProp[]

---@return pb_ExpertData
function pb.ExpertData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ArmedforceProp = {
    is_unlock = false,
}
pb.__pb_ArmedforceProp.__name = "ArmedforceProp"
pb.__pb_ArmedforceProp.__index = pb.__pb_ArmedforceProp
pb.__pb_ArmedforceProp.__pairs = __pb_pairs

pb.ArmedforceProp = { __name = "ArmedforceProp", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ArmedforceProp : ProtoBase
---@field public prop pb_PropInfo
---@field public is_unlock boolean

---@return pb_ArmedforceProp
function pb.ArmedforceProp:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_FashionItem = {
    is_unlock = false,
    is_def = false,
    is_read = false,
    is_netbar_unlock = false,
}
pb.__pb_FashionItem.__name = "FashionItem"
pb.__pb_FashionItem.__index = pb.__pb_FashionItem
pb.__pb_FashionItem.__pairs = __pb_pairs

pb.FashionItem = { __name = "FashionItem", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_FashionItem : ProtoBase
---@field public fashion pb_HeroFashion
---@field public is_unlock boolean
---@field public is_def boolean
---@field public is_read boolean
---@field public is_netbar_unlock boolean

---@return pb_FashionItem
function pb.FashionItem:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSHeroGrowLineGoal = {
    goal_id = 0,
    cur_value = 0,
    max_value = 0,
}
pb.__pb_CSHeroGrowLineGoal.__name = "CSHeroGrowLineGoal"
pb.__pb_CSHeroGrowLineGoal.__index = pb.__pb_CSHeroGrowLineGoal
pb.__pb_CSHeroGrowLineGoal.__pairs = __pb_pairs

pb.CSHeroGrowLineGoal = { __name = "CSHeroGrowLineGoal", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSHeroGrowLineGoal : ProtoBase
---@field public goal_id number
---@field public cur_value number
---@field public max_value number

---@return pb_CSHeroGrowLineGoal
function pb.CSHeroGrowLineGoal:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSHeroGrowLineArchive = {
    archive_type = 0,
    red_point = false,
}
pb.__pb_CSHeroGrowLineArchive.__name = "CSHeroGrowLineArchive"
pb.__pb_CSHeroGrowLineArchive.__index = pb.__pb_CSHeroGrowLineArchive
pb.__pb_CSHeroGrowLineArchive.__pairs = __pb_pairs

pb.CSHeroGrowLineArchive = { __name = "CSHeroGrowLineArchive", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSHeroGrowLineArchive : ProtoBase
---@field public archive_type number
---@field public archive_ids number[]
---@field public red_point boolean

---@return pb_CSHeroGrowLineArchive
function pb.CSHeroGrowLineArchive:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSHeroGrowLineData = {
    level = 0,
    seletced_num = 0,
}
pb.__pb_CSHeroGrowLineData.__name = "CSHeroGrowLineData"
pb.__pb_CSHeroGrowLineData.__index = pb.__pb_CSHeroGrowLineData
pb.__pb_CSHeroGrowLineData.__pairs = __pb_pairs

pb.CSHeroGrowLineData = { __name = "CSHeroGrowLineData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSHeroGrowLineData : ProtoBase
---@field public level number
---@field public goal_list pb_CSHeroGrowLineGoal[]
---@field public seletced_num number
---@field public rewards number[]
---@field public archives pb_CSHeroGrowLineArchive[]

---@return pb_CSHeroGrowLineData
function pb.CSHeroGrowLineData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSHeroGrowLine = {
    hero_id = 0,
}
pb.__pb_CSHeroGrowLine.__name = "CSHeroGrowLine"
pb.__pb_CSHeroGrowLine.__index = pb.__pb_CSHeroGrowLine
pb.__pb_CSHeroGrowLine.__pairs = __pb_pairs

pb.CSHeroGrowLine = { __name = "CSHeroGrowLine", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSHeroGrowLine : ProtoBase
---@field public hero_id number
---@field public grow_line_data pb_CSHeroGrowLineData

---@return pb_CSHeroGrowLine
function pb.CSHeroGrowLine:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_HeroArmedPropInfo = {
    store_fashion_max_num = 0,
    cur_lottery_times = 0,
    cur_quality = 0,
    total_lottery_num = 0,
}
pb.__pb_HeroArmedPropInfo.__name = "HeroArmedPropInfo"
pb.__pb_HeroArmedPropInfo.__index = pb.__pb_HeroArmedPropInfo
pb.__pb_HeroArmedPropInfo.__pairs = __pb_pairs

pb.HeroArmedPropInfo = { __name = "HeroArmedPropInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_HeroArmedPropInfo : ProtoBase
---@field public cur_fashion pb_HeroArmedPropFashion
---@field public init_fashion pb_HeroArmedPropFashion
---@field public history_fashion pb_HeroArmedPropFashion[]
---@field public store_fashion_max_num number
---@field public store_fashion pb_HeroArmedPropInfo.StoreFashionEntry[]
---@field public cur_lottery_times number
---@field public cur_quality number
---@field public fashionid_history number[]
---@field public total_lottery_num number

---@return pb_HeroArmedPropInfo
function pb.HeroArmedPropInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_HeroReddotInfo = {
    type = 0,
}
pb.__pb_HeroReddotInfo.__name = "HeroReddotInfo"
pb.__pb_HeroReddotInfo.__index = pb.__pb_HeroReddotInfo
pb.__pb_HeroReddotInfo.__pairs = __pb_pairs

pb.HeroReddotInfo = { __name = "HeroReddotInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_HeroReddotInfo : ProtoBase
---@field public type number
---@field public value number[]

---@return pb_HeroReddotInfo
function pb.HeroReddotInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_WorldChatRoomInfo = {
    room_id = 0,
    type = 0,
    state = 0,
    cur_member_num = 0,
    created_time = 0,
    last_active_time = 0,
    info_record_time = 0,
    SourceNode = "",
    group_id = "",
}
pb.__pb_WorldChatRoomInfo.__name = "WorldChatRoomInfo"
pb.__pb_WorldChatRoomInfo.__index = pb.__pb_WorldChatRoomInfo
pb.__pb_WorldChatRoomInfo.__pairs = __pb_pairs

pb.WorldChatRoomInfo = { __name = "WorldChatRoomInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_WorldChatRoomInfo : ProtoBase
---@field public room_id number
---@field public type number
---@field public state number
---@field public cur_member_num number
---@field public created_time number
---@field public last_active_time number
---@field public info_record_time number
---@field public SourceNode string
---@field public group_id string

---@return pb_WorldChatRoomInfo
function pb.WorldChatRoomInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_SafehouseDeviceLevel = {
    device_id = 0,
    level = 0,
}
pb.__pb_SafehouseDeviceLevel.__name = "SafehouseDeviceLevel"
pb.__pb_SafehouseDeviceLevel.__index = pb.__pb_SafehouseDeviceLevel
pb.__pb_SafehouseDeviceLevel.__pairs = __pb_pairs

pb.SafehouseDeviceLevel = { __name = "SafehouseDeviceLevel", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_SafehouseDeviceLevel : ProtoBase
---@field public device_id number
---@field public level number

---@return pb_SafehouseDeviceLevel
function pb.SafehouseDeviceLevel:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_StreakInfo = {
    match_type = 0,
    match_sub_type = 0,
    match_mode = 0,
    victory_streak = 0,
    defeat_streak = 0,
    kill_player_count = 0,
    kill_ai_count = 0,
    map_id = 0,
}
pb.__pb_StreakInfo.__name = "StreakInfo"
pb.__pb_StreakInfo.__index = pb.__pb_StreakInfo
pb.__pb_StreakInfo.__pairs = __pb_pairs

pb.StreakInfo = { __name = "StreakInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_StreakInfo : ProtoBase
---@field public match_type number
---@field public match_sub_type number
---@field public match_mode number
---@field public victory_streak number
---@field public defeat_streak number
---@field public kill_player_count number
---@field public kill_ai_count number
---@field public map_id number
---@field public mode_info pb_MatchModeInfo

---@return pb_StreakInfo
function pb.StreakInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_StreakTopRecord = {
    streak_count = 0,
    kill_player_count = 0,
    kill_ai_count = 0,
}
pb.__pb_StreakTopRecord.__name = "StreakTopRecord"
pb.__pb_StreakTopRecord.__index = pb.__pb_StreakTopRecord
pb.__pb_StreakTopRecord.__pairs = __pb_pairs

pb.StreakTopRecord = { __name = "StreakTopRecord", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_StreakTopRecord : ProtoBase
---@field public streak_count number
---@field public kill_player_count number
---@field public kill_ai_count number

---@return pb_StreakTopRecord
function pb.StreakTopRecord:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_StreakInfoTopRecord = {
    match_type = 0,
    match_sub_type = 0,
    match_mode = 0,
    map_id = 0,
}
pb.__pb_StreakInfoTopRecord.__name = "StreakInfoTopRecord"
pb.__pb_StreakInfoTopRecord.__index = pb.__pb_StreakInfoTopRecord
pb.__pb_StreakInfoTopRecord.__pairs = __pb_pairs

pb.StreakInfoTopRecord = { __name = "StreakInfoTopRecord", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_StreakInfoTopRecord : ProtoBase
---@field public match_type number
---@field public match_sub_type number
---@field public match_mode number
---@field public victory_record pb_StreakTopRecord
---@field public defeat_record pb_StreakTopRecord
---@field public map_id number
---@field public mode_info pb_MatchModeInfo

---@return pb_StreakInfoTopRecord
function pb.StreakInfoTopRecord:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CoPlayerSOLRecord = {
    result = 0,
    actual_produced_price = 0,
    kill_ai_count = 0,
    kill_boss_count = 0,
    kill_player_count = 0,
    kill_scav_count = 0,
}
pb.__pb_CoPlayerSOLRecord.__name = "CoPlayerSOLRecord"
pb.__pb_CoPlayerSOLRecord.__index = pb.__pb_CoPlayerSOLRecord
pb.__pb_CoPlayerSOLRecord.__pairs = __pb_pairs

pb.CoPlayerSOLRecord = { __name = "CoPlayerSOLRecord", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CoPlayerSOLRecord : ProtoBase
---@field public result number
---@field public actual_produced_price number
---@field public kill_ai_count number
---@field public kill_boss_count number
---@field public kill_player_count number
---@field public kill_scav_count number

---@return pb_CoPlayerSOLRecord
function pb.CoPlayerSOLRecord:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CoPlayerRaidRecord = {
    result = 0,
}
pb.__pb_CoPlayerRaidRecord.__name = "CoPlayerRaidRecord"
pb.__pb_CoPlayerRaidRecord.__index = pb.__pb_CoPlayerRaidRecord
pb.__pb_CoPlayerRaidRecord.__pairs = __pb_pairs

pb.CoPlayerRaidRecord = { __name = "CoPlayerRaidRecord", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CoPlayerRaidRecord : ProtoBase
---@field public result number

---@return pb_CoPlayerRaidRecord
function pb.CoPlayerRaidRecord:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CoPlayerBFRecord = {
}
pb.__pb_CoPlayerBFRecord.__name = "CoPlayerBFRecord"
pb.__pb_CoPlayerBFRecord.__index = pb.__pb_CoPlayerBFRecord
pb.__pb_CoPlayerBFRecord.__pairs = __pb_pairs

pb.CoPlayerBFRecord = { __name = "CoPlayerBFRecord", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CoPlayerBFRecord : ProtoBase

---@return pb_CoPlayerBFRecord
function pb.CoPlayerBFRecord:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CoPlayerTDMRecord = {
    result = 0,
}
pb.__pb_CoPlayerTDMRecord.__name = "CoPlayerTDMRecord"
pb.__pb_CoPlayerTDMRecord.__index = pb.__pb_CoPlayerTDMRecord
pb.__pb_CoPlayerTDMRecord.__pairs = __pb_pairs

pb.CoPlayerTDMRecord = { __name = "CoPlayerTDMRecord", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CoPlayerTDMRecord : ProtoBase
---@field public result number

---@return pb_CoPlayerTDMRecord
function pb.CoPlayerTDMRecord:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RecentCoPlayerInfo = {
    match_type = 0,
    match_subtype = 0,
    match_mode = 0,
    match_submode = 0,
    room_id = 0,
    finish_time = 0,
    map_id = 0,
    game_mode = 0,
    game_rule = 0,
}
pb.__pb_RecentCoPlayerInfo.__name = "RecentCoPlayerInfo"
pb.__pb_RecentCoPlayerInfo.__index = pb.__pb_RecentCoPlayerInfo
pb.__pb_RecentCoPlayerInfo.__pairs = __pb_pairs

pb.RecentCoPlayerInfo = { __name = "RecentCoPlayerInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RecentCoPlayerInfo : ProtoBase
---@field public match_type number
---@field public match_subtype number
---@field public match_mode number
---@field public match_submode number
---@field public room_id number
---@field public finish_time number
---@field public map_id number
---@field public game_mode number
---@field public game_rule number
---@field public player_info pb_PlayerSimpleInfo
---@field public sol_record pb_CoPlayerSOLRecord
---@field public bf_record pb_CoPlayerBFRecord
---@field public tdm_record pb_CoPlayerTDMRecord
---@field public raid_record pb_CoPlayerRaidRecord

---@return pb_RecentCoPlayerInfo
function pb.RecentCoPlayerInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DSMachineLoad = {
    ds_count = 0,
    ds_max_num = 0,
    ds_father_count = 0,
    ds_room_count = 0,
    support_ds_type = 0,
    single_room_ds_count = 0,
    multi_room_ds_count = 0,
    is_disabled = false,
    multi_room_ds_room_count = 0,
    total_score = 0,
    used_score = 0,
    cpu_cores_count = 0,
    cpu_idle_percent = 0,
    cpu_usage_percent = 0,
    cpu_reserved_idle_percent = 0,
    mem_total_mb = 0,
    mem_available_mb = 0,
    mem_shared_mb = 0,
    mem_ds_father_used_mb = 0,
    mem_ds_used_mb = 0,
    mem_reserved_in_mb = 0,
    player_num = 0,
    ai_num = 0,
    robot_num = 0,
}
pb.__pb_DSMachineLoad.__name = "DSMachineLoad"
pb.__pb_DSMachineLoad.__index = pb.__pb_DSMachineLoad
pb.__pb_DSMachineLoad.__pairs = __pb_pairs

pb.DSMachineLoad = { __name = "DSMachineLoad", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DSMachineLoad : ProtoBase
---@field public ds_count number
---@field public ds_max_num number
---@field public ds_father_count number
---@field public ds_room_count number
---@field public support_ds_type number
---@field public single_room_ds_count number
---@field public multi_room_ds_count number
---@field public is_disabled boolean
---@field public multi_room_ds_room_count number
---@field public total_score number
---@field public used_score number
---@field public cpu_cores_count number
---@field public cpu_idle_percent number
---@field public cpu_usage_percent number
---@field public cpu_reserved_idle_percent number
---@field public mem_total_mb number
---@field public mem_available_mb number
---@field public mem_shared_mb number
---@field public mem_ds_father_used_mb number
---@field public mem_ds_used_mb number
---@field public mem_reserved_in_mb number
---@field public player_num number
---@field public ai_num number
---@field public robot_num number

---@return pb_DSMachineLoad
function pb.DSMachineLoad:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_WeaponCheckItems = {
    should_check = false,
}
pb.__pb_WeaponCheckItems.__name = "WeaponCheckItems"
pb.__pb_WeaponCheckItems.__index = pb.__pb_WeaponCheckItems
pb.__pb_WeaponCheckItems.__pairs = __pb_pairs

pb.WeaponCheckItems = { __name = "WeaponCheckItems", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_WeaponCheckItems : ProtoBase
---@field public should_check boolean

---@return pb_WeaponCheckItems
function pb.WeaponCheckItems:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DrugCheckItems = {
    should_check = false,
    total_treatment_threshold = 0,
}
pb.__pb_DrugCheckItems.__name = "DrugCheckItems"
pb.__pb_DrugCheckItems.__index = pb.__pb_DrugCheckItems
pb.__pb_DrugCheckItems.__pairs = __pb_pairs

pb.DrugCheckItems = { __name = "DrugCheckItems", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DrugCheckItems : ProtoBase
---@field public should_check boolean
---@field public total_treatment_threshold number
---@field public clean_buffs number[]

---@return pb_DrugCheckItems
function pb.DrugCheckItems:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_EquipmentCheckItems = {
    helmet_durability_threshold = 0,
    armor_durability_threshold = 0,
    should_check_helmet = false,
    should_check_armor = false,
}
pb.__pb_EquipmentCheckItems.__name = "EquipmentCheckItems"
pb.__pb_EquipmentCheckItems.__index = pb.__pb_EquipmentCheckItems
pb.__pb_EquipmentCheckItems.__pairs = __pb_pairs

pb.EquipmentCheckItems = { __name = "EquipmentCheckItems", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_EquipmentCheckItems : ProtoBase
---@field public helmet_durability_threshold number
---@field public armor_durability_threshold number
---@field public should_check_helmet boolean
---@field public should_check_armor boolean

---@return pb_EquipmentCheckItems
function pb.EquipmentCheckItems:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ContainerCheckItems = {
    should_check = false,
    total_volume_threshold = 0,
    should_check_remains = false,
}
pb.__pb_ContainerCheckItems.__name = "ContainerCheckItems"
pb.__pb_ContainerCheckItems.__index = pb.__pb_ContainerCheckItems
pb.__pb_ContainerCheckItems.__pairs = __pb_pairs

pb.ContainerCheckItems = { __name = "ContainerCheckItems", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ContainerCheckItems : ProtoBase
---@field public should_check boolean
---@field public total_volume_threshold number
---@field public should_check_remains boolean

---@return pb_ContainerCheckItems
function pb.ContainerCheckItems:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_StyleCheckItems = {
    default_not_recommond = false,
    reserve_setting_pos = false,
}
pb.__pb_StyleCheckItems.__name = "StyleCheckItems"
pb.__pb_StyleCheckItems.__index = pb.__pb_StyleCheckItems
pb.__pb_StyleCheckItems.__pairs = __pb_pairs

pb.StyleCheckItems = { __name = "StyleCheckItems", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_StyleCheckItems : ProtoBase
---@field public default_not_recommond boolean
---@field public reserve_setting_pos boolean

---@return pb_StyleCheckItems
function pb.StyleCheckItems:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_OutfitCheckEntry = {
    panel_type = 0,
}
pb.__pb_OutfitCheckEntry.__name = "OutfitCheckEntry"
pb.__pb_OutfitCheckEntry.__index = pb.__pb_OutfitCheckEntry
pb.__pb_OutfitCheckEntry.__pairs = __pb_pairs

pb.OutfitCheckEntry = { __name = "OutfitCheckEntry", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_OutfitCheckEntry : ProtoBase
---@field public panel_type number
---@field public values number[]

---@return pb_OutfitCheckEntry
function pb.OutfitCheckEntry:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_OutfitCheckItems = {
}
pb.__pb_OutfitCheckItems.__name = "OutfitCheckItems"
pb.__pb_OutfitCheckItems.__index = pb.__pb_OutfitCheckItems
pb.__pb_OutfitCheckItems.__pairs = __pb_pairs

pb.OutfitCheckItems = { __name = "OutfitCheckItems", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_OutfitCheckItems : ProtoBase
---@field public weapon pb_WeaponCheckItems
---@field public drug pb_DrugCheckItems
---@field public container pb_ContainerCheckItems
---@field public equipment pb_EquipmentCheckItems
---@field public outfit_style pb_StyleCheckItems
---@field public check_entries pb_OutfitCheckEntry[]

---@return pb_OutfitCheckItems
function pb.OutfitCheckItems:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_EquipmentRentalPlan = {
    type_id = 0,
    preset_id = 0,
    refresh_ts = 0,
    consumable_id = 0,
}
pb.__pb_EquipmentRentalPlan.__name = "EquipmentRentalPlan"
pb.__pb_EquipmentRentalPlan.__index = pb.__pb_EquipmentRentalPlan
pb.__pb_EquipmentRentalPlan.__pairs = __pb_pairs

pb.EquipmentRentalPlan = { __name = "EquipmentRentalPlan", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_EquipmentRentalPlan : ProtoBase
---@field public type_id number
---@field public preset_id number
---@field public refresh_ts number
---@field public consumable_id number

---@return pb_EquipmentRentalPlan
function pb.EquipmentRentalPlan:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_EquipmentRentalPlanDetail = {
    consumable_id = 0,
    type_id = 0,
    preset_id = 0,
    preset_title = "",
    refresh_price = 0,
    refresh_enable = false,
    preset_price = 0,
    consumable_type = 0,
}
pb.__pb_EquipmentRentalPlanDetail.__name = "EquipmentRentalPlanDetail"
pb.__pb_EquipmentRentalPlanDetail.__index = pb.__pb_EquipmentRentalPlanDetail
pb.__pb_EquipmentRentalPlanDetail.__pairs = __pb_pairs

pb.EquipmentRentalPlanDetail = { __name = "EquipmentRentalPlanDetail", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_EquipmentRentalPlanDetail : ProtoBase
---@field public consumable_id number
---@field public type_id number
---@field public preset_id number
---@field public details pb_EquipmentRentalProp[]
---@field public preset_title string
---@field public refresh_price number
---@field public refresh_enable boolean
---@field public preset_price number
---@field public consumable_type number

---@return pb_EquipmentRentalPlanDetail
function pb.EquipmentRentalPlanDetail:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_EquipmentRentalProp = {
    position = 0,
    item_id = 0,
    item_num = 0,
}
pb.__pb_EquipmentRentalProp.__name = "EquipmentRentalProp"
pb.__pb_EquipmentRentalProp.__index = pb.__pb_EquipmentRentalProp
pb.__pb_EquipmentRentalProp.__pairs = __pb_pairs

pb.EquipmentRentalProp = { __name = "EquipmentRentalProp", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_EquipmentRentalProp : ProtoBase
---@field public position number
---@field public item_id number
---@field public item_num number

---@return pb_EquipmentRentalProp
function pb.EquipmentRentalProp:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_IDCDSRoomLoad = {
    ds_room_type = 0,
    unused_ds_room_num = 0,
    max_ds_room_num = 0,
    used_ds_num = 0,
}
pb.__pb_IDCDSRoomLoad.__name = "IDCDSRoomLoad"
pb.__pb_IDCDSRoomLoad.__index = pb.__pb_IDCDSRoomLoad
pb.__pb_IDCDSRoomLoad.__pairs = __pb_pairs

pb.IDCDSRoomLoad = { __name = "IDCDSRoomLoad", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_IDCDSRoomLoad : ProtoBase
---@field public ds_room_type number
---@field public unused_ds_room_num number
---@field public max_ds_room_num number
---@field public used_ds_num number

---@return pb_IDCDSRoomLoad
function pb.IDCDSRoomLoad:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_IDCDSLoad = {
    ds_svr_count = 0,
    used_ds_num = 0,
    max_ds_num = 0,
    ds_room_count = 0,
    ds_svr_enabled_count = 0,
    ds_svr_disabled_count = 0,
    ds_svr_full_load_count = 0,
    ds_svr_availabled_count = 0,
    idc_total_score = 0,
    idc_used_score = 0,
    is_idc_score_load = false,
    curr_used_ds_num = 0,
}
pb.__pb_IDCDSLoad.__name = "IDCDSLoad"
pb.__pb_IDCDSLoad.__index = pb.__pb_IDCDSLoad
pb.__pb_IDCDSLoad.__pairs = __pb_pairs

pb.IDCDSLoad = { __name = "IDCDSLoad", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_IDCDSLoad : ProtoBase
---@field public ds_svr_count number
---@field public used_ds_num number
---@field public max_ds_num number
---@field public ds_room_count number
---@field public single_room_type pb_IDCDSTypeLoad
---@field public multi_room_type pb_IDCDSTypeLoad
---@field public ds_svr_enabled_count number
---@field public ds_svr_disabled_count number
---@field public ds_svr_full_load_count number
---@field public ds_svr_availabled_count number
---@field public bhd_room_type pb_IDCDSTypeLoad
---@field public idc_total_score number
---@field public idc_used_score number
---@field public is_idc_score_load boolean
---@field public curr_used_ds_num number

---@return pb_IDCDSLoad
function pb.IDCDSLoad:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_IDCDSTypeLoad = {
    ds_type = 0,
    effective_weight = 0,
    max_ds_num = 0,
    used_ds_num = 0,
    unused_ds_num = 0,
    used_ds_room_count = 0,
}
pb.__pb_IDCDSTypeLoad.__name = "IDCDSTypeLoad"
pb.__pb_IDCDSTypeLoad.__index = pb.__pb_IDCDSTypeLoad
pb.__pb_IDCDSTypeLoad.__pairs = __pb_pairs

pb.IDCDSTypeLoad = { __name = "IDCDSTypeLoad", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_IDCDSTypeLoad : ProtoBase
---@field public ds_type number
---@field public effective_weight number
---@field public max_ds_num number
---@field public used_ds_num number
---@field public unused_ds_num number
---@field public used_ds_room_count number

---@return pb_IDCDSTypeLoad
function pb.IDCDSTypeLoad:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_SSHelloReq = {
}
pb.__pb_SSHelloReq.__name = "SSHelloReq"
pb.__pb_SSHelloReq.__index = pb.__pb_SSHelloReq
pb.__pb_SSHelloReq.__pairs = __pb_pairs

pb.SSHelloReq = { __name = "SSHelloReq", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_SSHelloReq : ProtoBase

---@return pb_SSHelloReq
function pb.SSHelloReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RoundtripAddr = {
    domain = "",
    vip = "",
    vport = 0,
    idc = "",
    access_point = "",
}
pb.__pb_RoundtripAddr.__name = "RoundtripAddr"
pb.__pb_RoundtripAddr.__index = pb.__pb_RoundtripAddr
pb.__pb_RoundtripAddr.__pairs = __pb_pairs

pb.RoundtripAddr = { __name = "RoundtripAddr", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RoundtripAddr : ProtoBase
---@field public domain string
---@field public vip string
---@field public vport number
---@field public idc string
---@field public access_point string

---@return pb_RoundtripAddr
function pb.RoundtripAddr:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RoundtripIDC = {
    idc = "",
    access_point = "",
}
pb.__pb_RoundtripIDC.__name = "RoundtripIDC"
pb.__pb_RoundtripIDC.__index = pb.__pb_RoundtripIDC
pb.__pb_RoundtripIDC.__pairs = __pb_pairs

pb.RoundtripIDC = { __name = "RoundtripIDC", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RoundtripIDC : ProtoBase
---@field public idc string
---@field public services pb_RoundtripAddr[]
---@field public access_point string

---@return pb_RoundtripIDC
function pb.RoundtripIDC:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RoundtripTime = {
    rtt = 0,
    result = 0,
    packet_loss = 0,
}
pb.__pb_RoundtripTime.__name = "RoundtripTime"
pb.__pb_RoundtripTime.__index = pb.__pb_RoundtripTime
pb.__pb_RoundtripTime.__pairs = __pb_pairs

pb.RoundtripTime = { __name = "RoundtripTime", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RoundtripTime : ProtoBase
---@field public rtt number
---@field public addr pb_RoundtripAddr
---@field public result number
---@field public packet_loss number

---@return pb_RoundtripTime
function pb.RoundtripTime:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_IDCRoundtripTime = {
    idc = "",
    access_point = "",
    rtt = 0,
    packet_loss = 0,
}
pb.__pb_IDCRoundtripTime.__name = "IDCRoundtripTime"
pb.__pb_IDCRoundtripTime.__index = pb.__pb_IDCRoundtripTime
pb.__pb_IDCRoundtripTime.__pairs = __pb_pairs

pb.IDCRoundtripTime = { __name = "IDCRoundtripTime", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_IDCRoundtripTime : ProtoBase
---@field public idc string
---@field public access_point string
---@field public services pb_RoundtripTime[]
---@field public rtt number
---@field public packet_loss number

---@return pb_IDCRoundtripTime
function pb.IDCRoundtripTime:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_SwitchConditionParam = {
    type = 0,
    param = 0,
}
pb.__pb_SwitchConditionParam.__name = "SwitchConditionParam"
pb.__pb_SwitchConditionParam.__index = pb.__pb_SwitchConditionParam
pb.__pb_SwitchConditionParam.__pairs = __pb_pairs

pb.SwitchConditionParam = { __name = "SwitchConditionParam", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_SwitchConditionParam : ProtoBase
---@field public type number
---@field public param number

---@return pb_SwitchConditionParam
function pb.SwitchConditionParam:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MidasToken = {
    openid = "",
    openkey = "",
    session_id = "",
    session_type = "",
    pf = "",
    pfkey = "",
    reason = "",
    pay_channel = "",
    sub_channel = "",
    region = "",
    currency_type = "",
    cancel_url = "",
    return_url = "",
}
pb.__pb_MidasToken.__name = "MidasToken"
pb.__pb_MidasToken.__index = pb.__pb_MidasToken
pb.__pb_MidasToken.__pairs = __pb_pairs

pb.MidasToken = { __name = "MidasToken", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MidasToken : ProtoBase
---@field public openid string
---@field public openkey string
---@field public session_id string
---@field public session_type string
---@field public pf string
---@field public pfkey string
---@field public reason string
---@field public pay_channel string
---@field public sub_channel string
---@field public region string
---@field public currency_type string
---@field public cancel_url string
---@field public return_url string

---@return pb_MidasToken
function pb.MidasToken:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_LotteryBoxData = {
    box_id = 0,
    num = 0,
    gid = 0,
    group_id = 0,
    prop_id = 0,
    opened_prop_id = 0,
    opened_prop_gid = 0,
    opened_prop_num = 0,
}
pb.__pb_LotteryBoxData.__name = "LotteryBoxData"
pb.__pb_LotteryBoxData.__index = pb.__pb_LotteryBoxData
pb.__pb_LotteryBoxData.__pairs = __pb_pairs

pb.LotteryBoxData = { __name = "LotteryBoxData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_LotteryBoxData : ProtoBase
---@field public box_id number
---@field public num number
---@field public gid number
---@field public group_id number
---@field public prop_id number
---@field public opened_prop_id number
---@field public opened_prop_gid number
---@field public opened_prop_num number

---@return pb_LotteryBoxData
function pb.LotteryBoxData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PaymentRecord = {
    payment_idx = 0,
    bought_num = 0,
}
pb.__pb_PaymentRecord.__name = "PaymentRecord"
pb.__pb_PaymentRecord.__index = pb.__pb_PaymentRecord
pb.__pb_PaymentRecord.__pairs = __pb_pairs

pb.PaymentRecord = { __name = "PaymentRecord", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PaymentRecord : ProtoBase
---@field public payment_idx number
---@field public bought_num number
---@field public batch_item_list number[]

---@return pb_PaymentRecord
function pb.PaymentRecord:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ShopBuyRecord = {
    shop_id = 0,
    bought_time = 0,
    bought_num = 0,
}
pb.__pb_ShopBuyRecord.__name = "ShopBuyRecord"
pb.__pb_ShopBuyRecord.__index = pb.__pb_ShopBuyRecord
pb.__pb_ShopBuyRecord.__pairs = __pb_pairs

pb.ShopBuyRecord = { __name = "ShopBuyRecord", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ShopBuyRecord : ProtoBase
---@field public shop_id number
---@field public bought_time number
---@field public pay_list pb_PaymentRecord[]
---@field public bought_num number

---@return pb_ShopBuyRecord
function pb.ShopBuyRecord:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSPunishNtf = {
    over_time = 0,
    reason = 0,
    custom_reason = "",
    punish_start_time = 0,
}
pb.__pb_CSPunishNtf.__name = "CSPunishNtf"
pb.__pb_CSPunishNtf.__index = pb.__pb_CSPunishNtf
pb.__pb_CSPunishNtf.__pairs = __pb_pairs

pb.CSPunishNtf = { __name = "CSPunishNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSPunishNtf : ProtoBase
---@field public over_time number
---@field public reason number
---@field public custom_reason string
---@field public punish_start_time number

---@return pb_CSPunishNtf
function pb.CSPunishNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TssTLogsPkg = {
    player_id = 0,
}
pb.__pb_TssTLogsPkg.__name = "TssTLogsPkg"
pb.__pb_TssTLogsPkg.__index = pb.__pb_TssTLogsPkg
pb.__pb_TssTLogsPkg.__pairs = __pb_pairs

pb.TssTLogsPkg = { __name = "TssTLogsPkg", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TssTLogsPkg : ProtoBase
---@field public player_id number
---@field public logs pb_TssTLogsPkg.LogItem[]

---@return pb_TssTLogsPkg
function pb.TssTLogsPkg:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_SSUpdateResDBNtf = {
    name = "",
}
pb.__pb_SSUpdateResDBNtf.__name = "SSUpdateResDBNtf"
pb.__pb_SSUpdateResDBNtf.__index = pb.__pb_SSUpdateResDBNtf
pb.__pb_SSUpdateResDBNtf.__pairs = __pb_pairs

pb.SSUpdateResDBNtf = { __name = "SSUpdateResDBNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_SSUpdateResDBNtf : ProtoBase
---@field public name string

---@return pb_SSUpdateResDBNtf
function pb.SSUpdateResDBNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSUpdatePlayerBalanceNtf = {
    notify_time = 0,
    reason = 0,
}
pb.__pb_CSUpdatePlayerBalanceNtf.__name = "CSUpdatePlayerBalanceNtf"
pb.__pb_CSUpdatePlayerBalanceNtf.__index = pb.__pb_CSUpdatePlayerBalanceNtf
pb.__pb_CSUpdatePlayerBalanceNtf.__pairs = __pb_pairs

pb.CSUpdatePlayerBalanceNtf = { __name = "CSUpdatePlayerBalanceNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSUpdatePlayerBalanceNtf : ProtoBase
---@field public notify_time number
---@field public reason number

---@return pb_CSUpdatePlayerBalanceNtf
function pb.CSUpdatePlayerBalanceNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CollectionPropInfo = {
    id = 0,
    gid = 0,
    num = 0,
    rights_id = 0,
}
pb.__pb_CollectionPropInfo.__name = "CollectionPropInfo"
pb.__pb_CollectionPropInfo.__index = pb.__pb_CollectionPropInfo
pb.__pb_CollectionPropInfo.__pairs = __pb_pairs

pb.CollectionPropInfo = { __name = "CollectionPropInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CollectionPropInfo : ProtoBase
---@field public id number
---@field public gid number
---@field public num number
---@field public rights_id number

---@return pb_CollectionPropInfo
function pb.CollectionPropInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CollectionRankTitleInfo = {
    prop_id = 0,
    adcode = 0,
    rank_no = 0,
}
pb.__pb_CollectionRankTitleInfo.__name = "CollectionRankTitleInfo"
pb.__pb_CollectionRankTitleInfo.__index = pb.__pb_CollectionRankTitleInfo
pb.__pb_CollectionRankTitleInfo.__pairs = __pb_pairs

pb.CollectionRankTitleInfo = { __name = "CollectionRankTitleInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CollectionRankTitleInfo : ProtoBase
---@field public prop_id number
---@field public adcode number
---@field public rank_no number

---@return pb_CollectionRankTitleInfo
function pb.CollectionRankTitleInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_UnlockDepartment = {
    id = 0,
    is_unlock = false,
}
pb.__pb_UnlockDepartment.__name = "UnlockDepartment"
pb.__pb_UnlockDepartment.__index = pb.__pb_UnlockDepartment
pb.__pb_UnlockDepartment.__pairs = __pb_pairs

pb.UnlockDepartment = { __name = "UnlockDepartment", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_UnlockDepartment : ProtoBase
---@field public id number
---@field public is_unlock boolean

---@return pb_UnlockDepartment
function pb.UnlockDepartment:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RankShield = {
    rank_id = 0,
    used_shied = 0,
    total_shied = 0,
}
pb.__pb_RankShield.__name = "RankShield"
pb.__pb_RankShield.__index = pb.__pb_RankShield
pb.__pb_RankShield.__pairs = __pb_pairs

pb.RankShield = { __name = "RankShield", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RankShield : ProtoBase
---@field public rank_id number
---@field public used_shied number
---@field public total_shied number

---@return pb_RankShield
function pb.RankShield:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CsvClientHashTable = {
    csv_names = "",
    csv_hash = "",
}
pb.__pb_CsvClientHashTable.__name = "CsvClientHashTable"
pb.__pb_CsvClientHashTable.__index = pb.__pb_CsvClientHashTable
pb.__pb_CsvClientHashTable.__pairs = __pb_pairs

pb.CsvClientHashTable = { __name = "CsvClientHashTable", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CsvClientHashTable : ProtoBase
---@field public csv_names string
---@field public csv_hash string

---@return pb_CsvClientHashTable
function pb.CsvClientHashTable:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_SOLSeasonInfo = {
    total_fight = 0,
    total_escape = 0,
    total_game_time = 0,
    total_kill = 0,
    total_killed = 0,
    carry_teammate_assets = 0,
    total_collection_price = 0,
    total_gained_price = 0,
    total_miss = 0,
    total_price = 0,
    total_fight_all = 0,
    total_quit = 0,
    total_giveup = 0,
    kill_low_stakes = 0,
    kill_med_stakes = 0,
    kill_high_stakes = 0,
    killed_low_stakes = 0,
    killed_med_stakes = 0,
    killed_high_stakes = 0,
    total_shoot = 0,
    total_shoot_hit = 0,
    total_shoot_down = 0,
    total_shoot_head_down = 0,
    total_kill_ai = 0,
    total_kill_boss = 0,
    total_assist_kill = 0,
    total_contract_price = 0,
    total_bring_mandel_brick = 0,
    total_bring_gold_sku = 0,
    total_bring_red_sku = 0,
    total_pickup_teammate = 0,
    total_revive_teammate = 0,
    total_move = 0,
    total_search_cnt = 0,
}
pb.__pb_SOLSeasonInfo.__name = "SOLSeasonInfo"
pb.__pb_SOLSeasonInfo.__index = pb.__pb_SOLSeasonInfo
pb.__pb_SOLSeasonInfo.__pairs = __pb_pairs

pb.SOLSeasonInfo = { __name = "SOLSeasonInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_SOLSeasonInfo : ProtoBase
---@field public total_fight number
---@field public total_escape number
---@field public total_game_time number
---@field public total_kill number
---@field public total_killed number
---@field public carry_teammate_assets number
---@field public total_collection_price number
---@field public total_gained_price number
---@field public total_miss number
---@field public total_price number
---@field public total_fight_all number
---@field public total_quit number
---@field public total_giveup number
---@field public kill_low_stakes number
---@field public kill_med_stakes number
---@field public kill_high_stakes number
---@field public killed_low_stakes number
---@field public killed_med_stakes number
---@field public killed_high_stakes number
---@field public total_shoot number
---@field public total_shoot_hit number
---@field public total_shoot_down number
---@field public total_shoot_head_down number
---@field public total_kill_ai number
---@field public total_kill_boss number
---@field public total_assist_kill number
---@field public total_contract_price number
---@field public total_bring_mandel_brick number
---@field public total_bring_gold_sku number
---@field public total_bring_red_sku number
---@field public total_pickup_teammate number
---@field public total_revive_teammate number
---@field public total_move number
---@field public total_search_cnt number
---@field public recent_match_data pb_SOLRadarData[]

---@return pb_SOLSeasonInfo
function pb.SOLSeasonInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_SOLRadarData = {
    match_time = 0,
    total_pickup_teammate = 0,
    total_revive_teammate = 0,
    total_assist_kill = 0,
    total_kill_player = 0,
    total_kill_ai = 0,
    total_kill_boss = 0,
    total_move = 0,
    total_search_cnt = 0,
    total_contract_price = 0,
    total_escape = 0,
    total_game_time = 0,
    total_gained_price = 0,
    total_bring_mandel_brick = 0,
    total_bring_gold_sku = 0,
    total_bring_red_sku = 0,
}
pb.__pb_SOLRadarData.__name = "SOLRadarData"
pb.__pb_SOLRadarData.__index = pb.__pb_SOLRadarData
pb.__pb_SOLRadarData.__pairs = __pb_pairs

pb.SOLRadarData = { __name = "SOLRadarData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_SOLRadarData : ProtoBase
---@field public match_time number
---@field public total_pickup_teammate number
---@field public total_revive_teammate number
---@field public total_assist_kill number
---@field public total_kill_player number
---@field public total_kill_ai number
---@field public total_kill_boss number
---@field public total_move number
---@field public total_search_cnt number
---@field public total_contract_price number
---@field public total_escape number
---@field public total_game_time number
---@field public total_gained_price number
---@field public total_bring_mandel_brick number
---@field public total_bring_gold_sku number
---@field public total_bring_red_sku number

---@return pb_SOLRadarData
function pb.SOLRadarData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RaidSeasonInfo = {
    total_fight = 0,
    total_pass = 0,
    total_pass_perfect = 0,
    hell_mode_1st_pass = 0,
    hell_mode_fastest_pass = 0,
}
pb.__pb_RaidSeasonInfo.__name = "RaidSeasonInfo"
pb.__pb_RaidSeasonInfo.__index = pb.__pb_RaidSeasonInfo
pb.__pb_RaidSeasonInfo.__pairs = __pb_pairs

pb.RaidSeasonInfo = { __name = "RaidSeasonInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RaidSeasonInfo : ProtoBase
---@field public total_fight number
---@field public total_pass number
---@field public total_pass_perfect number
---@field public hell_mode_1st_pass number
---@field public hell_mode_fastest_pass number

---@return pb_RaidSeasonInfo
function pb.RaidSeasonInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MPSeasonInfo = {
    total_fight = 0,
    total_win = 0,
    total_kill = 0,
    total_killed = 0,
    total_mvp = 0,
    total_game_time = 0,
    total_score = 0,
    total_quit = 0,
    total_help = 0,
    total_assist = 0,
    total_fight_as_commander = 0,
    total_win_as_commander = 0,
    total_shoot = 0,
    total_shoot_hit = 0,
    total_kill_head = 0,
    total_kill_long_range = 0,
    total_capture_point = 0,
    total_capture_point_time = 0,
    total_vehicle_use_time = 0,
    total_damage_to_vehicle = 0,
    total_vehicle_kill = 0,
    total_vehicle_destroyed = 0,
    total_vehicle_repair = 0,
    stat_by_day = false,
    start_time = 0,
    end_time = 0,
}
pb.__pb_MPSeasonInfo.__name = "MPSeasonInfo"
pb.__pb_MPSeasonInfo.__index = pb.__pb_MPSeasonInfo
pb.__pb_MPSeasonInfo.__pairs = __pb_pairs

pb.MPSeasonInfo = { __name = "MPSeasonInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MPSeasonInfo : ProtoBase
---@field public total_fight number
---@field public total_win number
---@field public total_kill number
---@field public total_killed number
---@field public total_mvp number
---@field public total_game_time number
---@field public total_score number
---@field public total_quit number
---@field public total_help number
---@field public total_assist number
---@field public total_fight_as_commander number
---@field public total_win_as_commander number
---@field public total_shoot number
---@field public total_shoot_hit number
---@field public total_kill_head number
---@field public total_kill_long_range number
---@field public total_capture_point number
---@field public total_capture_point_time number
---@field public total_vehicle_use_time number
---@field public total_damage_to_vehicle number
---@field public total_vehicle_kill number
---@field public total_vehicle_destroyed number
---@field public total_vehicle_repair number
---@field public recent_match_data pb_MpRadarData[]
---@field public stat_by_day boolean
---@field public start_time number
---@field public end_time number

---@return pb_MPSeasonInfo
function pb.MPSeasonInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MpRadarData = {
    match_time = 0,
    total_vehicle_kill = 0,
    total_vehicle_destroyed = 0,
    total_damage_to_vehicle = 0,
    total_kill = 0,
    total_kill_head = 0,
    total_kill_long_range = 0,
    total_vehicle_repair = 0,
    total_game_time = 0,
    total_killed = 0,
    total_capture_point_time = 0,
    total_capture_point = 0,
    total_help = 0,
    total_assist = 0,
}
pb.__pb_MpRadarData.__name = "MpRadarData"
pb.__pb_MpRadarData.__index = pb.__pb_MpRadarData
pb.__pb_MpRadarData.__pairs = __pb_pairs

pb.MpRadarData = { __name = "MpRadarData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MpRadarData : ProtoBase
---@field public match_time number
---@field public total_vehicle_kill number
---@field public total_vehicle_destroyed number
---@field public total_damage_to_vehicle number
---@field public total_kill number
---@field public total_kill_head number
---@field public total_kill_long_range number
---@field public total_vehicle_repair number
---@field public total_game_time number
---@field public total_killed number
---@field public total_capture_point_time number
---@field public total_capture_point number
---@field public total_help number
---@field public total_assist number

---@return pb_MpRadarData
function pb.MpRadarData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TaskGoalItem = {
    task_id = 0,
    goal_id = 0,
    cur_value = 0,
    max_value = 0,
    type = 0,
}
pb.__pb_TaskGoalItem.__name = "TaskGoalItem"
pb.__pb_TaskGoalItem.__index = pb.__pb_TaskGoalItem
pb.__pb_TaskGoalItem.__pairs = __pb_pairs

pb.TaskGoalItem = { __name = "TaskGoalItem", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TaskGoalItem : ProtoBase
---@field public task_id number
---@field public goal_id number
---@field public cur_value number
---@field public max_value number
---@field public type number

---@return pb_TaskGoalItem
function pb.TaskGoalItem:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSPopularityNtf = {
    trigger_type = 0,
}
pb.__pb_CSPopularityNtf.__name = "CSPopularityNtf"
pb.__pb_CSPopularityNtf.__index = pb.__pb_CSPopularityNtf
pb.__pb_CSPopularityNtf.__pairs = __pb_pairs

pb.CSPopularityNtf = { __name = "CSPopularityNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSPopularityNtf : ProtoBase
---@field public trigger_type number

---@return pb_CSPopularityNtf
function pb.CSPopularityNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_SSCurrencyChangeNtf = {
}
pb.__pb_SSCurrencyChangeNtf.__name = "SSCurrencyChangeNtf"
pb.__pb_SSCurrencyChangeNtf.__index = pb.__pb_SSCurrencyChangeNtf
pb.__pb_SSCurrencyChangeNtf.__pairs = __pb_pairs

pb.SSCurrencyChangeNtf = { __name = "SSCurrencyChangeNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_SSCurrencyChangeNtf : ProtoBase
---@field public deposit_change pb_DataChange

---@return pb_SSCurrencyChangeNtf
function pb.SSCurrencyChangeNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_LimitSocialInfo = {
    type = 0,
    effective_start_time = 0,
    expired_time = 0,
    prop_id = 0,
    rank_title_adcode = 0,
    rank_title_rank_no = 0,
}
pb.__pb_LimitSocialInfo.__name = "LimitSocialInfo"
pb.__pb_LimitSocialInfo.__index = pb.__pb_LimitSocialInfo
pb.__pb_LimitSocialInfo.__pairs = __pb_pairs

pb.LimitSocialInfo = { __name = "LimitSocialInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_LimitSocialInfo : ProtoBase
---@field public type number
---@field public effective_start_time number
---@field public expired_time number
---@field public prop_id number
---@field public rank_title_adcode number
---@field public rank_title_rank_no number

---@return pb_LimitSocialInfo
function pb.LimitSocialInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_BhdSpecData = {
    bhd_special_hp = 0,
    bhd_special_ammo = 0,
}
pb.__pb_BhdSpecData.__name = "BhdSpecData"
pb.__pb_BhdSpecData.__index = pb.__pb_BhdSpecData
pb.__pb_BhdSpecData.__pairs = __pb_pairs

pb.BhdSpecData = { __name = "BhdSpecData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_BhdSpecData : ProtoBase
---@field public bhd_special_hp number
---@field public bhd_special_ammo number

---@return pb_BhdSpecData
function pb.BhdSpecData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RecoveryAmountChange = {
    time = 0,
    event_id = 0,
    reason = 0,
    money_paper = 0,
    recovery_level = 0,
    recovery_rate = 0,
    recovery_amount = 0,
    amount = 0,
    old_amount = 0,
    old_level = 0,
    old_rate = 0,
}
pb.__pb_RecoveryAmountChange.__name = "RecoveryAmountChange"
pb.__pb_RecoveryAmountChange.__index = pb.__pb_RecoveryAmountChange
pb.__pb_RecoveryAmountChange.__pairs = __pb_pairs

pb.RecoveryAmountChange = { __name = "RecoveryAmountChange", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RecoveryAmountChange : ProtoBase
---@field public time number
---@field public event_id number
---@field public reason number
---@field public money_paper number
---@field public recovery_level number
---@field public recovery_rate number
---@field public recovery_amount number
---@field public amount number
---@field public old_amount number
---@field public old_level number
---@field public old_rate number

---@return pb_RecoveryAmountChange
function pb.RecoveryAmountChange:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RankHandBookStateInfo = {
    state = 0,
    unlock_time = 0,
}
pb.__pb_RankHandBookStateInfo.__name = "RankHandBookStateInfo"
pb.__pb_RankHandBookStateInfo.__index = pb.__pb_RankHandBookStateInfo
pb.__pb_RankHandBookStateInfo.__pairs = __pb_pairs

pb.RankHandBookStateInfo = { __name = "RankHandBookStateInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RankHandBookStateInfo : ProtoBase
---@field public state number
---@field public unlock_time number

---@return pb_RankHandBookStateInfo
function pb.RankHandBookStateInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RankSingleHandBookData = {
}
pb.__pb_RankSingleHandBookData.__name = "RankSingleHandBookData"
pb.__pb_RankSingleHandBookData.__index = pb.__pb_RankSingleHandBookData
pb.__pb_RankSingleHandBookData.__pairs = __pb_pairs

pb.RankSingleHandBookData = { __name = "RankSingleHandBookData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RankSingleHandBookData : ProtoBase
---@field public hand_book_state pb_RankHandBookStateInfo[]

---@return pb_RankSingleHandBookData
function pb.RankSingleHandBookData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRankPlayerAuxData = {
    rank_data_type = 0,
    score = 0,
}
pb.__pb_CSRankPlayerAuxData.__name = "CSRankPlayerAuxData"
pb.__pb_CSRankPlayerAuxData.__index = pb.__pb_CSRankPlayerAuxData
pb.__pb_CSRankPlayerAuxData.__pairs = __pb_pairs

pb.CSRankPlayerAuxData = { __name = "CSRankPlayerAuxData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRankPlayerAuxData : ProtoBase
---@field public rank_data_type number
---@field public score number

---@return pb_CSRankPlayerAuxData
function pb.CSRankPlayerAuxData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PlayerRankPercentageInfo = {
    rank_data_type = 0,
    percentage = 0,
}
pb.__pb_PlayerRankPercentageInfo.__name = "PlayerRankPercentageInfo"
pb.__pb_PlayerRankPercentageInfo.__index = pb.__pb_PlayerRankPercentageInfo
pb.__pb_PlayerRankPercentageInfo.__pairs = __pb_pairs

pb.PlayerRankPercentageInfo = { __name = "PlayerRankPercentageInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PlayerRankPercentageInfo : ProtoBase
---@field public rank_data_type number
---@field public percentage number

---@return pb_PlayerRankPercentageInfo
function pb.PlayerRankPercentageInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PlayerRankHistoricalTitleInfo = {
    rank_data_type = 0,
    title_id = 0,
    adCode = 0,
    ranking_no = 0,
    timestamp = 0,
    region = 0,
    cycleinfo = "",
}
pb.__pb_PlayerRankHistoricalTitleInfo.__name = "PlayerRankHistoricalTitleInfo"
pb.__pb_PlayerRankHistoricalTitleInfo.__index = pb.__pb_PlayerRankHistoricalTitleInfo
pb.__pb_PlayerRankHistoricalTitleInfo.__pairs = __pb_pairs

pb.PlayerRankHistoricalTitleInfo = { __name = "PlayerRankHistoricalTitleInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PlayerRankHistoricalTitleInfo : ProtoBase
---@field public rank_data_type number
---@field public title_id number
---@field public adCode number
---@field public ranking_no number
---@field public timestamp number
---@field public region number
---@field public cycleinfo string

---@return pb_PlayerRankHistoricalTitleInfo
function pb.PlayerRankHistoricalTitleInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PlayerRankHistoricalSeasonInfo = {
    season_no = 0,
}
pb.__pb_PlayerRankHistoricalSeasonInfo.__name = "PlayerRankHistoricalSeasonInfo"
pb.__pb_PlayerRankHistoricalSeasonInfo.__index = pb.__pb_PlayerRankHistoricalSeasonInfo
pb.__pb_PlayerRankHistoricalSeasonInfo.__pairs = __pb_pairs

pb.PlayerRankHistoricalSeasonInfo = { __name = "PlayerRankHistoricalSeasonInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PlayerRankHistoricalSeasonInfo : ProtoBase
---@field public season_no number
---@field public title_list pb_PlayerRankHistoricalTitleInfo[]

---@return pb_PlayerRankHistoricalSeasonInfo
function pb.PlayerRankHistoricalSeasonInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PlayerRankHistoricalRecordInfo = {
}
pb.__pb_PlayerRankHistoricalRecordInfo.__name = "PlayerRankHistoricalRecordInfo"
pb.__pb_PlayerRankHistoricalRecordInfo.__index = pb.__pb_PlayerRankHistoricalRecordInfo
pb.__pb_PlayerRankHistoricalRecordInfo.__pairs = __pb_pairs

pb.PlayerRankHistoricalRecordInfo = { __name = "PlayerRankHistoricalRecordInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PlayerRankHistoricalRecordInfo : ProtoBase
---@field public season_list pb_PlayerRankHistoricalSeasonInfo[]

---@return pb_PlayerRankHistoricalRecordInfo
function pb.PlayerRankHistoricalRecordInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_SinglePlayerAuxData = {
    player_id = 0,
}
pb.__pb_SinglePlayerAuxData.__name = "SinglePlayerAuxData"
pb.__pb_SinglePlayerAuxData.__index = pb.__pb_SinglePlayerAuxData
pb.__pb_SinglePlayerAuxData.__pairs = __pb_pairs

pb.SinglePlayerAuxData = { __name = "SinglePlayerAuxData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_SinglePlayerAuxData : ProtoBase
---@field public player_id number
---@field public rank_aux_data_list pb_CSRankPlayerAuxData[]

---@return pb_SinglePlayerAuxData
function pb.SinglePlayerAuxData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MpRankNumeral = {
    mp_rank_play = 0,
    mp_rank_win = 0,
    mp_rank_gametime = 0,
    mp_rank_score = 0,
    mp_rank_kill = 0,
    mp_rank_death = 0,
}
pb.__pb_MpRankNumeral.__name = "MpRankNumeral"
pb.__pb_MpRankNumeral.__index = pb.__pb_MpRankNumeral
pb.__pb_MpRankNumeral.__pairs = __pb_pairs

pb.MpRankNumeral = { __name = "MpRankNumeral", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MpRankNumeral : ProtoBase
---@field public mp_rank_play number
---@field public mp_rank_win number
---@field public mp_rank_gametime number
---@field public mp_rank_score number
---@field public mp_rank_kill number
---@field public mp_rank_death number

---@return pb_MpRankNumeral
function pb.MpRankNumeral:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CommanderNumeral = {
    mp_commander_play = 0,
    mp_commander_win = 0,
    mp_commander_gametime = 0,
    mp_commander_score = 0,
    mp_commander_kill = 0,
    mp_commander_death = 0,
    mp_commander_like = 0,
    mp_commander_recommend = 0,
    total_fight_as_commander = 0,
    win_ratio = 0,
    percentile_of_win_ratio = 0,
    kill_by_vehicle_per_minute = 0,
    percentile_of_kill_by_vehicle_per_minute = 0,
    defeat_by_per_minute = 0,
    percentile_of_defeat_per_minute = 0,
    help_per_minute = 0,
    percentile_of_help_per_minute = 0,
}
pb.__pb_CommanderNumeral.__name = "CommanderNumeral"
pb.__pb_CommanderNumeral.__index = pb.__pb_CommanderNumeral
pb.__pb_CommanderNumeral.__pairs = __pb_pairs

pb.CommanderNumeral = { __name = "CommanderNumeral", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CommanderNumeral : ProtoBase
---@field public mp_commander_play number
---@field public mp_commander_win number
---@field public mp_commander_gametime number
---@field public mp_commander_score number
---@field public mp_commander_kill number
---@field public mp_commander_death number
---@field public mp_commander_like number
---@field public mp_commander_recommend number
---@field public total_fight_as_commander number
---@field public win_ratio number
---@field public percentile_of_win_ratio number
---@field public kill_by_vehicle_per_minute number
---@field public percentile_of_kill_by_vehicle_per_minute number
---@field public defeat_by_per_minute number
---@field public percentile_of_defeat_per_minute number
---@field public help_per_minute number
---@field public percentile_of_help_per_minute number

---@return pb_CommanderNumeral
function pb.CommanderNumeral:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


