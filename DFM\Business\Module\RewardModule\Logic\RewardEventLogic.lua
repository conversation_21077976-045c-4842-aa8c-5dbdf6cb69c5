----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMReward)
----- LOG FUNCTION AUTO GENERATE END -----------



local RewardEventLogic = {}
local SettlementDefine = require "DFM.Business.DataStruct.SettlementStruct.SettlementDefine"
--------------------------------------------------------------------------
--- Reward模块相关的事件（即使UI不打开也需要监听的事件）
--------------------------------------------------------------------------
RewardEventLogic.AddRewardListeners = function()
    Server.RewardServer.Events.evtUseGiftSuccess:AddListener(RewardEventLogic.OnBuyAutoUseGiftSuccess)
    Server.RewardServer.Events.evtShowRewardMainView:AddListener(RewardEventLogic.OnShowRewardMainView)
    Server.RoleInfoServer.Events.evtSeasonLevelUpdate:AddListener(RewardEventLogic.OnSeasonLevelUp)
    Server.RoleInfoServer.Events.evtAccountLevelUpdate:AddListener(RewardEventLogic.OnAccountLevelUp)
    Server.CollectionServer.Events.evtWeaponSkinRenamed:AddListener(RewardEventLogic.OnWeaponSkinRenamed)
    Server.CollectionServer.Events.evtReceivedCollectionProps:AddListener(RewardEventLogic.OnReceivedCollectionProps)
    Server.RewardServer.Events.evtOpenRewardSceneView:AddListener(RewardEventLogic.OnOpenRewardSceneView)
    Server.ModuleUnlockServer.Events.evtSwitchMoudleUnlock:AddListener(RewardEventLogic.OnModuleUnlock)
    -- Server.RewardServer.Events.evtCloseRewardSceneView:AddListener(RewardEventLogic.OnCloseRewardSceneView)
    Server.RewardServer.Events.evtSetShowRewardScene:AddListener(RewardEventLogic.OnSetShowRewardScene)
    -- Server.HeroServer.Events.evtHeroUnlockNtf:AddListener(RewardEventLogic.OnHeroUnlock)
    Server.RewardServer.Events.evtClearCache:AddListener(RewardEventLogic.OnClearCache)
    Server.CollectionServer.Events.evtCollectionRetrieveWeaponSkin:AddListener(RewardEventLogic.OnRetrieveWeaponSkin)
    Server.CollectionServer.Events.evtCollectionRetrieveAllWeaponSkinsOfPattern:AddListener(RewardEventLogic.OnRetrieveAllWeaponSkinsOfPattern)
    Server.VehicleServer.Events.evtVehicleUnlock:AddListener(RewardEventLogic.OnUnlockVechile)
    Module.Settlement.Config.Events.evtEndMpSettlementPop:AddListener(RewardEventLogic.OnSettlementEnd)
    Module.Settlement.Config.Events.evtEndSolSettlementPop:AddListener(RewardEventLogic.OnSettlementEnd)
    Server.CollectionServer.Events.evtCollectionUseMissionFileExpCard:AddListener(RewardEventLogic.OnUseMissionFileExpCard)
    Server.CollectionServer.Events.evtCollectionUseMissionFileActivateCard:AddListener(RewardEventLogic.OnUseMissionFileActivateCard)
    Server.MailServer.Events.evtEnableRewardNTFCall:AddListener(RewardEventLogic.EnableNTFCall)
end

RewardEventLogic.RemoveRewardListeners = function()
    Server.RewardServer.Events.evtUseGiftSuccess:RemoveListener(RewardEventLogic.OnBuyAutoUseGiftSuccess)
    Server.RewardServer.Events.evtShowRewardMainView:RemoveListener(RewardEventLogic.OnShowRewardMainView)
    Server.RoleInfoServer.Events.evtSeasonLevelUpdate:RemoveListener(RewardEventLogic.OnSeasonLevelUp)
    Server.RoleInfoServer.Events.evtAccountLevelUpdate:RemoveListener(RewardEventLogic.OnAccountLevelUp)
    Server.CollectionServer.Events.evtWeaponSkinRenamed:RemoveListener(RewardEventLogic.OnWeaponSkinRenamed)
    Server.CollectionServer.Events.evtReceivedCollectionProps:RemoveListener(RewardEventLogic.OnReceivedCollectionProps)
    Server.RewardServer.Events.evtOpenRewardSceneView:RemoveListener(RewardEventLogic.OnOpenRewardSceneView)
    Server.ModuleUnlockServer.Events.evtSwitchMoudleUnlock:RemoveListener(RewardEventLogic.OnModuleUnlock)
    -- Server.RewardServer.Events.evtCloseRewardSceneView:RemoveListener(RewardEventLogic.OnCloseRewardSceneView)
    Server.RewardServer.Events.evtSetShowRewardScene:RemoveListener(RewardEventLogic.OnSetShowRewardScene)
    -- Server.HeroServer.Events.evtHeroUnlockNtf:RemoveListener(RewardEventLogic.OnHeroUnlock)
    Server.RewardServer.Events.evtClearCache:RemoveListener(RewardEventLogic.OnClearCache)
    Server.CollectionServer.Events.evtCollectionRetrieveWeaponSkin:RemoveListener(RewardEventLogic.OnRetrieveWeaponSkin)
    Server.CollectionServer.Events.evtCollectionRetrieveAllWeaponSkinsOfPattern:RemoveListener(RewardEventLogic.OnRetrieveAllWeaponSkinsOfPattern)
    Server.VehicleServer.Events.evtVehicleUnlock:RemoveListener(RewardEventLogic.OnUnlockVechile)
    Module.Settlement.Config.Events.evtEndMpSettlementPop:RemoveListener(RewardEventLogic.OnSettlementEnd)
    Module.Settlement.Config.Events.evtEndSolSettlementPop:RemoveListener(RewardEventLogic.OnSettlementEnd)
    Server.CollectionServer.Events.evtCollectionUseMissionFileExpCard:RemoveListener(RewardEventLogic.OnUseMissionFileExpCard)
    Server.CollectionServer.Events.evtCollectionUseMissionFileActivateCard:RemoveListener(RewardEventLogic.OnUseMissionFileActivateCard)
    Server.MailServer.Events.evtEnableRewardNTFCall:RemoveListener(RewardEventLogic.EnableNTFCall)
end

RewardEventLogic.AddBlindBoxListeners = function()

end


RewardEventLogic.RemoveBlindBoxListeners = function()

end


RewardEventLogic.OnBuyAutoUseGiftSuccess = function(rewardData)
    Module.CommonTips:ShowSimpleTip(Module.Shop.Config.Loc.BuyAutoUseGiftSuccess)
end


RewardEventLogic.OnShowRewardMainView = function(title, itemList)
    Module.Reward:OpenRewardPanel(title, nil, itemList, nil, false, false, true)
end


RewardEventLogic.OnSeasonLevelUp = function(seasonLv, preSeasonLv, bActive)
    if Server.SettlementServer:GetSettlementInfoSource() ~= SettlementDefine.ESettlementInfoSource.SOL and seasonLv > preSeasonLv and bActive == true and  Server.RoleInfoServer.deltaExp > 0 then
        Module.Reward:OpenSOLPathOfGrowthLevelUpPanel(seasonLv, preSeasonLv)
    end
end


RewardEventLogic.OnAccountLevelUp = function(accountLv, preaccountLv)
    if Server.SettlementServer:GetSettlementInfoSource() ~= SettlementDefine.ESettlementInfoSource.MP and accountLv > preaccountLv and Server.RoleInfoServer.accountDeltaExp > 0 then
        Module.Reward:OpenMPPathOfGrowthLevelUpPanel(accountLv, preaccountLv)
    end
end


RewardEventLogic.OnSafehouseDeviceUpgrade = function(deviceID)
    Module.Reward:OpenSafehouseLevelUpRewardPanel(deviceID, nil)
end

RewardEventLogic.OnWeaponSkinRenamed = function(weaponSkinItem)
    Module.Reward:OpenWeaponSkinRenamedPage(weaponSkinItem)
end

RewardEventLogic.OnReceivedCollectionProps = function(items)
    Module.Reward:OpenRewardPanel(Module.Reward.Config.Loc.PropReward, nil, items, nil, false, false, true)
end

RewardEventLogic.OnModuleUnlock = function(moduleId, bIsInitUnlock, bIsNeedPopup)
    if bIsInitUnlock == true and bIsNeedPopup == true then
        --Module.Reward:OpenModuleUnlockPanel(moduleId)
    end
end

RewardEventLogic.OnHeroUnlock = function(heroIds)
    --展示干员奖励(版本迭代)
    for index, heroId in ipairs(heroIds) do
        Module.Reward:OpenHeroStuffUnlockPanel(heroId)
    end
end

RewardEventLogic.OnOpenRewardSceneView = function(fCallback, itemTable, bTenDraws, iType)
    Module.Reward:OpenRewardSceneView(fCallback, itemTable, bTenDraws, iType)
end

-- 在曼德尔砖抽卡期间不能弹藏品皮肤第一次获取弹窗,所以这里需要手动弹窗
-- RewardEventLogic.OnCloseRewardSceneView = function(...)
--     Module.Reward:DoCachePopAction(0)
-- end

RewardEventLogic.OnSetShowRewardScene = function(bShowRewardScene)
    Module.Reward:SetShowRewardScene(bShowRewardScene)
end

RewardEventLogic.OnClearCache = function()
    Module.Reward:ClearCachePopAction()
end

RewardEventLogic.OnRetrieveWeaponSkin = function(skinItem)
    if skinItem then
        Module.Reward:OpenRewardPanel(ServerTipCode.GetItemTitle, nil, {skinItem}, nil, false, false, true)
    end
end

RewardEventLogic.OnRetrieveAllWeaponSkinsOfPattern = function(skinItems)
    Module.Reward:OpenRewardPanel(ServerTipCode.GetItemTitle, nil, skinItems, nil, false, false, true)
end

RewardEventLogic.OnUnlockVechile = function(vehicleItem)
    Module.Reward:OpenRewardPanel(ServerTipCode.GetItemTitle, nil, vehicleItem, nil, false, false, true)
end

RewardEventLogic.OnSettlementEnd = function()
    Module.Reward:DoCachePopAction(1)
end

RewardEventLogic.OnUseMissionFileExpCard = function(res)
    if res.result == 0 and res.info and res.info.main_line then
        Server.BattlePassServer:BattlePassInfoChangeSettleByLevelInfo(Server.BattlePassServer:GetOldLeveInfo(), res.info.main_line.level_info)
    end
end

RewardEventLogic.OnUseMissionFileActivateCard = function(res)
    if res.result == 0 and res.info and res.info.type then
        Module.Reward:OpenBPUnlockPanel(res.info.type)
    end
end

RewardEventLogic.EnableNTFCall = function(key, bEnable)
    Module.Reward:EnableNTFCall(key, bEnable)
end

return RewardEventLogic