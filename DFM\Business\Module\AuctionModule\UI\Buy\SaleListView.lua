----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMAuction)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local GPUINavigationStrategy_Hittest = import("GPUINavigationStrategy_Hittest")
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

local SaleListView = ui("SaleListView")
local ItemDetailView = require "DFM.Business.Module.ItemDetailModule.UI.ItemDetailView"
local CommonAddDecSlider = require "DFM.Business.Module.CommonWidgetModule.UI.CommonAddDecSlider"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local AuctionLogic = require("DFM.Business.Module.AuctionModule.Logic.AuctionLogic")
local AuctionConfig = require("DFM.Business.Module.AuctionModule.AuctionConfig")
local ShopHelperTool = require "DFM.StandaloneLua.BusinessTool.ShopHelperTool"
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local WeaponHelperTool   = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local JumpConfig = require "DFM.Business.Module.JumpModule.JumpConfig"
local FWeaponDataAttribute = import "WeaponDataAttribute"
local UDFMGameHudDelegates = import("DFMGameHudDelegates")
local gameInst = GetGameInstance()
ECheckButtonState = import"ECheckButtonState"
local EComp = CommonWidgetConfig.EIVWarehouseTempComponent
local EIVSlotPos = CommonWidgetConfig.EIVSlotPos
local EIVCompOrder = CommonWidgetConfig.EIVCompOrder
local MAX_VISIBLE_CELL_COUNT = 10
if DFHD_LUA == 1 then
    MAX_VISIBLE_CELL_COUNT = 10
else
    MAX_VISIBLE_CELL_COUNT = 7
end
local LINE_ADAPTATION_PARAM = 3
local PRICE_LINE_ADAPTATION_RATIO = 0.1
local NUMS_POSITION_ADAPTATION_RATIO = 0.06
local BARCHARTVISIBLECOUNT = 8
local SORTMODE = {
    ASC = 0,
    DESC = 1,
}

function SaleListView:Ctor()
    Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.Default)
    self._item = nil
    self._sale = nil
    self._saleList = {}
    self._selectedPos = -1
    self._selectedCell = nil

    self._wtHbSeasonLock = self:Wnd("wtDFHbSeasonlock", UIWidgetBase)
    self._wtSeasonLockText = self:Wnd("DFTextBlock_141", UITextBlock)

    self._wtItemDetailView = self:Wnd("WBP_ItemDetailView", ItemDetailView)
    self._wtDetailViewBuyBtn = nil
    self._wtScrollGridBoxIns = self:Wnd("ScrollGridBox_1", UIWidgetBase)
    if self._wtScrollGridBoxIns then
        self._wtScrollGridBox = UIUtil.WndScrollGridBox(self, "ScrollGridBox_1", self._OnGetItemCount, self._OnProcessItemWidget)
    end
    self._wtAveragePriceIcon = self:Wnd("wtAveragePriceIcon", UIImage)
    self._wtAveragePriceText = self:Wnd("wtAveragePriceText", UITextBlock)
    -- self._wtShopPriceIcon = self:Wnd("Image", UIImage)
    -- self._wtShopPriceText = self:Wnd("TextBlock_3", UITextBlock)
    -- self._wtShopPriceWrapper = self:Wnd("HorizontalBox_0", UIWidgetBase)
    self._wtRefreshButton = self:Wnd("RefreshButton", UIButton)
    if self._wtRefreshButton then
        self._wtRefreshButton:Event("OnClicked", self._OnRefreshBtnClicked, self)
        self._wtRefreshButton:Event("OnDeClicked", self._OnRefreshBtnClicked, self)
    end
    --self._wtCollectCheckBox = self:Wnd("CollectCheckBox", UICheckBox)
    --self._wtCollectCheckBox:Event("OnCheckStateChanged", self._OnCollectStateChanged, self)
    self._wtEmptyBgSlot = self:Wnd("EmptySlot", UIWidgetBase)
    self._wtLowestPriceTextBlock = self:Wnd("DFRichTextBlock", UITextBlock)
    self._wtLowestPriceText = self:Wnd("DFTextBlock_2", UITextBlock)
    self._wtTransactionOpenTime = self:Wnd("DFTextBlock_TradingTime", UITextBlock)

    -- 折线图内容
    self._wtBuyPriceLine = self:Wnd("WBP_Auction_BuyPriceLine", AuctionBuyPriceLine)

    -- 柱状图内容
    self._wtBarChartBox = self:Wnd("DFHorizontalBox_List", UIWidgetBase)

    -- 新下拉筛选框
    if self.WBP_DFCommonDropDownBox then
        self._wtZoomRateDroDown = UIUtil.WndDropDownBox(self, "WBP_DFCommonDropDownBox", self.OnZoomRateCheckedTabIndexChanged)
    end


    self._DEFAULT_REFRESH_BUTTON_CD_TIME = nil

    -- self._wtItemDetailViewSliderBtn = self:Wnd("WBP_ItemDetailViewBtnSliderBtn_1", UIWidgetBase)
    -- self._wtBuyNumSelector = self._wtItemDetailViewSliderBtn:Wnd("wtAddMinusWidget", CommonAddDecSlider)
    -- self._wtBuyNum = self._wtItemDetailViewSliderBtn:Wnd("_wtNumText", UITextBlock)
    -- self._wtBuyBtn = self._wtItemDetailViewSliderBtn:Wnd("_wtBtn", UIWidgetBase)
    -- self._wtBuyBtn:Wnd("Button_Common", UIButton):Event("OnClicked", self._OnBuyBtnClicked, self)
    -- self._wtBuyPriceText = self._wtBuyBtn:Wnd("RichTextBlock_Common", UITextBlock)
    -- self._wtBuyNumDesc = self._wtItemDetailViewSliderBtn:Wnd("wtDescText", UIWidgetBase)

    self._PLAY_SHOW_ANIM_MAX_COUNT = 10
    self._PLAY_SHOW_ANIM_DELTA_TIME = 0.05
    self._showAnimFinishList = {}

    self._BUYFAIL_CD_TIME = 2
    self._bDisabledBuy = false

    if DFHD_LUA == 1 then
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.DefaultSecondary)
    end
    Module.CommonBar:RegStackUITopBarTitle(self, Module.Auction.Config.Loc.AuctionSaleListTitle)
end

function SaleListView:OnInitExtraData(saleType, item, equipPos, callback, durabilityLvl)
    if not item then
        return
    end
    loginfo("SaleListView:OnInitExtraData() itemId:", item.id)
    self._saleType = saleType
    self._item = item
    self._curNum = 1
    local weaponFeature = self._item:GetFeature(EFeatureType.Weapon)
    local equipmentFeature = self._item:GetFeature(EFeatureType.Equipment)
    self._canClick = (weaponFeature and weaponFeature:IsWeapon()) or (equipmentFeature and (equipmentFeature:IsHelmet() or equipmentFeature:IsBreastPlate()))
    self._equipPos = equipPos
    self._callback = callback
    self._durabilityLvl = durabilityLvl
    self._sortMode = SORTMODE.ASC
    self._bFirstInit = true
    self._bCanExchange = nil
    self:ResetViewPanel()
end

function SaleListView:OnOpen()

end

function SaleListView:OnShowBegin()
    Module.CommonBar:BindPersistentBackHandler(self.OnBack, self)
    self:AddListeners()
    self:_TryFocusToDefaultWidgetGamepad()
    Server.ShopServer:SetLimitClicker(true)
end

function SaleListView:OnHideBegin()
    Module.CommonBar:BindPersistentBackHandler()
    self:ReleaseTimerHandle()
    if self._callback then
        self._callback()
    end
    self:RemoveAllLuaEvent()
    Server.ShopServer:SetLimitClicker(false)
end

function SaleListView:OnBack()
    if IsHD() then
        self:_RemoveNavigation()
        self:_RemoveShortcuts()
    end
    if Server.AuctionServer:CheckIsInSaleList(self:GetPropId()) then
        --- 先拉取数据
        Server.AuctionServer:FetchSaleList(self:GetPropId())
    end
    Facade.UIManager:CloseUI(self)
end

function SaleListView:OnShow()

end

function SaleListView:OnHide()

end

function SaleListView:OnClose()
    if self._buttonUpHandle then
		UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonUpEvent:Remove(self._buttonUpHandle)
	end
    Facade.UIManager:ClearSubUIByParent(self, self._wtEmptyBgSlot)
    Facade.UIManager:ClearSubUIByParent(self, self._wtBarChartBox)
end

function SaleListView:AddListeners()
    self:AddLuaEvent(Server.AuctionServer.Events.evtSaleListChanged, self.Init, self)
    self:AddLuaEvent(Module.ItemDetail.Config.evtItemDetailSilderValueChanged, self._OnBuyNumChanged, self)
    self:AddLuaEvent(Server.AuctionServer.Events.evtBuyPerformanceProcess, self._OnBuyPerformanceProcess, self)
    self:AddLuaEvent(Server.ModuleUnlockServer.Events.evtSwitchMoudleUnlock, self.UpdateItemDetailView, self)
    self:AddLuaEvent(Server.ShopServer.Events.evtShopItemReGenerated, self.CheckExchangeChannel, self)
end

function SaleListView:ReleaseTimerHandle()
    self:ReleasTransactionStateChangeTimerHandle()
    if self._timerHandle then
        self._timerHandle:Release()
        self._timerHandle = nil
    end
end

function SaleListView:ReleasTransactionStateChangeTimerHandle()
    if self._transactionStateChangeTimerHandle then
        Timer.CancelDelay(self._transactionStateChangeTimerHandle)
        self._transactionStateChangeTimerHandle = nil
    end
end

function SaleListView:GetPropId()
    if not self._item then
        return
    end
    -- local weaponFeature = self._item:GetFeature(EFeatureType.Weapon)
    -- local type = ItemHelperTool.GetMainTypeById(self._item.id)
    -- --非标准预设枪械
    -- if weaponFeature and weaponFeature:IsWeapon() and type ~= EItemType.Weapon then
    --     return weaponFeature:GetWeaponRecId()
    -- end
    return self._item.id
end

---@return pb_CSSaleProp
function SaleListView:GetSelectedCellInfo()
    return self._saleList and self._saleList[self._selectedPos + 1] or nil
end

function SaleListView:ResetViewPanel()
    if self._wtZoomRateDroDown then
        local zoomRateDroDownLoc = Module.Auction.Config.ZoomRateDroDownNewLoc
        UIUtil.InitDropDownBox(self._wtZoomRateDroDown, zoomRateDroDownLoc, {}, 0)
    end
    if self._wtRefreshButton then
        self._DEFAULT_REFRESH_BUTTON_CD_TIME = nil
        if self._wtRefreshButton then
            self._wtRefreshButton:SetIsEnabledStyle(true)
        end
    end
    if self._wtLowestPriceTextBlock then
        self._wtLowestPriceTextBlock:Collapsed()
    end
    if self._wtTransactionOpenTime then
        self._wtTransactionOpenTime:Collapsed()
    end
    if self._wtEmptyBgSlot then
        self._wtEmptyBgSlot:Collapsed()
    end
    if self._wtItemDetailView then
        self._wtItemDetailView:Collapsed()
    end
    if isvalid(self._wtExchangeTips) and self._wtExchangeTips.Collapsed then
        self._wtExchangeTips:Collapsed()
    end
    self:UpdateDataChart({})
end

function SaleListView:InitSelectedCell()
    -- local saleInfo = self._sale
    -- if saleInfo and saleInfo.sale_lists and next(saleInfo.sale_lists) then
    if self._saleList and next(self._saleList) then
        self._selectedPos = 0
    else
        self._selectedPos = -1
    end
end

function SaleListView:Init(saleInfos)
    loginfo("SaleListView:Init()")
    if saleInfos then
        local saleInfoId = nil
        for itemId, saleInfo in pairs(saleInfos) do -- 此处只会请求一个id，saleInfos只有一个键值对
            saleInfoId = itemId
            break
        end
        if saleInfoId ~= self:GetPropId() then
            return
        end
    end
    local saleInfo = Server.AuctionServer:GetPropSaleInfo(self:GetPropId(), self._durabilityLvl, saleInfos)
    self._sale = saleInfo
    self._saleList = saleInfo and saleInfo.sale_lists or {}
    self:InitSelectedCell()
    self:_SortFun()
    self:UpdateView()
    if self._wtLowestPriceTextBlock then
        if not table.isempty(self._saleList) then
            local lowestPriceSaleList = self._saleList[1]
            local currencyClientType = MapCurrencyId2ClientType[lowestPriceSaleList.price_currency]
            local currencyIcon = ECurrencyClientType2RichIconTxtV3[currencyClientType]
            self._wtLowestPriceTextBlock:SetText(currencyIcon .. MathUtil.GetNumberFormatStr(math.floor(lowestPriceSaleList.price + 0.00001)))
            self._wtLowestPriceText:SelfHitTestInvisible()
            self._wtLowestPriceTextBlock:SelfHitTestInvisible()
        else
            self._wtLowestPriceText:Collapsed()
            self._wtLowestPriceTextBlock:Collapsed()
        end
    end
    self:UpdateTransactionOpenState()
    self:UpdateDataChart()
end

function SaleListView:UpdateTransactionOpenState()
    if self._sale and self._wtTransactionOpenTime then
        if self._sale.auction_vaild_time_begin and self._sale.auction_vaild_time_end then
            if self._sale.auction_vaild_time_begin > 0 then
                local nextChangeTime = nil
                if self._sale.auction_vaild_time_end > 0 then
                    self._wtTransactionOpenTime:SelfHitTestInvisible()
                    local timeBeginStr = TimeUtil.TransTimestamp2MMDDHHMMAUCTIONStr(self._sale.auction_vaild_time_begin)
                    local timeEndStr = TimeUtil.TransTimestamp2MMDDHHMMAUCTIONStr(self._sale.auction_vaild_time_end)
                    self._wtTransactionOpenTime:SetText(string.format(AuctionConfig.Loc.TransactionOpenTime, timeBeginStr, timeEndStr))
                    if self._sale.auction_vaild_time_begin <= Facade.ClockManager:GetLocalTimestamp() and Facade.ClockManager:GetLocalTimestamp() <= self._sale.auction_vaild_time_end then
                        nextChangeTime = self._sale.auction_vaild_time_end - Facade.ClockManager:GetLocalTimestamp() + 1
                    elseif Facade.ClockManager:GetLocalTimestamp() < self._sale.auction_vaild_time_begin then
                        nextChangeTime = self._sale.auction_vaild_time_begin - Facade.ClockManager:GetLocalTimestamp() + 1
                    else
                        nextChangeTime = nil
                    end

                else
                    self._wtTransactionOpenTime:SelfHitTestInvisible()
                    local timeBeginStr = TimeUtil.TransTimestamp2MMDDHHMMAUCTIONStr(self._sale.auction_vaild_time_begin)
                    self._wtTransactionOpenTime:SetText(string.format(AuctionConfig.Loc.TransactionOpenTimeBegin, timeBeginStr))
                    if self._sale.auction_vaild_time_begin <= Facade.ClockManager:GetLocalTimestamp() then
                        nextChangeTime = nil
                    else
                        nextChangeTime = self._sale.auction_vaild_time_begin - Facade.ClockManager:GetLocalTimestamp() + 1
                    end
                end
                self:UpdateItemDetailView()
                self:ReleasTransactionStateChangeTimerHandle()
                if nextChangeTime and nextChangeTime > 0 then
                    self._transactionStateChangeTimerHandle = Timer.DelayCall(nextChangeTime, function ()
                        self:UpdateTransactionOpenState()
                    end, self)
                end
            else
                self._wtTransactionOpenTime:Collapsed()
            end
        else
            self._wtTransactionOpenTime:Collapsed()
        end
    end
end

function SaleListView:GetTransactionIsOpen()
    if self._sale then
        if self._sale.auction_vaild_time_begin and self._sale.auction_vaild_time_end then
            if self._sale.auction_vaild_time_begin > 0 then
                if self._sale.auction_vaild_time_end > 0 then
                    return self._sale.auction_vaild_time_begin <= Facade.ClockManager:GetLocalTimestamp() and Facade.ClockManager:GetLocalTimestamp() <= self._sale.auction_vaild_time_end
                else
                    return self._sale.auction_vaild_time_begin <= Facade.ClockManager:GetLocalTimestamp()
                end
            else
                return true
            end
        else
            return true
        end
    end
    return true
end

function SaleListView:UpdateDataChart(givenDataPoints)
    self:UpdateBarChart(givenDataPoints)
end

function SaleListView:UpdateBarChart(givenDataPoints)
    if not self._wtBarChartBox then
        return
    end
    if self._sale and self._saleList and not table.isempty(self._saleList) then
        loginfo("SaleListView:UpdateBarChart()", self._item.id, 'DataChartInit #saleList', #self._saleList)
        self._dataPoints = {}
        local max, min
        if givenDataPoints and type(givenDataPoints) == 'table' then
            self._dataPoints = givenDataPoints
        else
            for k, v in ipairs(self._saleList) do
                if k <= BARCHARTVISIBLECOUNT and v.price and v.selling_num then
                    local dataPoint = {v.price, v.selling_num}
                    table.insert(self._dataPoints, dataPoint)
                end
            end
        end
        for k, v in ipairs(self._dataPoints) do
            if min == nil or v[2] < min then
                min = v[2]
            end
            if max == nil or v[2] > max then
                max = v[2]
            end
        end
        if max and min then
            max, min = AuctionLogic.MaxAndMinValueYProcess(max, min, LINE_ADAPTATION_PARAM)
        end
        Facade.UIManager:RemoveSubUIByParent(self, self._wtBarChartBox)
        self._wtBarChartBox:SelfHitTestInvisible()
        for k, v in ipairs(self._dataPoints) do
            if k <= BARCHARTVISIBLECOUNT then
                local weakUIIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.AuctionBarChartCell, self._wtBarChartBox)
                local barChartCell = getfromweak(weakUIIns)
                if barChartCell then
                    local uiSlot = UWidgetLayoutLibrary.SlotAsHorizontalBoxSlot(barChartCell)
                    if uiSlot then
                        uiSlot:SetSize(FSlateChildSize(ESlateSizeRule.Fill))
                    end
                    barChartCell:InitSignleTotalSellNumBar(v[2], max)
                    barChartCell:SetTotalSellNum(v[2])
                    barChartCell:SetCurrencyNum(v[1], self._sale.guide_currency)
                    barChartCell:SetCurrencyNumVisibility(false, true, false)
                    barChartCell:SelfHitTestInvisible()
                end
            end
        end
        local emptyCellCount = #self._dataPoints > 0 and BARCHARTVISIBLECOUNT - #self._dataPoints or 0
        if emptyCellCount > 0 then
            for i = 1, emptyCellCount do
                local weakUIIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.AuctionBarChartCell, self._wtBarChartBox)
                local barChartCell = getfromweak(weakUIIns)
                if barChartCell then
                    local uiSlot = UWidgetLayoutLibrary.SlotAsHorizontalBoxSlot(barChartCell)
                    if uiSlot then
                        uiSlot:SetSize(FSlateChildSize(ESlateSizeRule.Fill))
                    end
                    barChartCell:InitEmptyCell()
                    barChartCell:SetCurrencyNumVisibility(false, true, false)
                    barChartCell:SelfHitTestInvisible()
                end
            end
        end
        self:RefreshSpecialBarChart()
    else
        self._wtBarChartBox:Collapsed()
    end
end

function SaleListView:UpdatePriceLine(givenDataPoints)
    if self._sale and self._wtBuyPriceLine then
        loginfo("SaleListView:UpdatePriceLine()", self._item.id, 'DataChartInit #saleList', #self._saleList)
        local dataPoints = {}
        if givenDataPoints and type(givenDataPoints) == 'table' then
            dataPoints = givenDataPoints
        else
            for k, v in ipairs(self._saleList) do
                if v.price and v.selling_num then
                    local dataPoint = {v.price, v.selling_num}
                    table.insert(dataPoints, dataPoint)
                end
            end
        end
        local baseAdaptationParams =
        {
            visibleCellCount = MAX_VISIBLE_CELL_COUNT,
            lineAdaptationParam = LINE_ADAPTATION_PARAM,
            priceLineAdaptationRatio = PRICE_LINE_ADAPTATION_RATIO,
            numsPositionAdaptationRatio = NUMS_POSITION_ADAPTATION_RATIO
        }
        local bShowComponentParams =
        {
            bShowPriceLine = false,
            bShowNumsPanel = true,
            bShowNumsImage = true,
            numPanelInvisiblePos = {}
        }
        self._wtBuyPriceLine:Init(dataPoints, self._sale.guide_price, self._sale.guide_currency,
        baseAdaptationParams, bShowComponentParams)
    end
end

function SaleListView:_OnGetItemCount()
    local count = 0
    if self._saleList then
        count = #self._saleList
    end
    return count
end

---@param ui SaleListCell
function SaleListView:_OnProcessItemWidget(position, ui)
    local saleInfo = self._sale
    local cellInfo = self._saleList[position + 1]
    if cellInfo then
        ui:SelfHitTestInvisible()
        if self._saleType == Module.Auction.Config.ESaleType.Weapon then
            self:FreshSaleInfoShow(ui, cellInfo)
            ui:BindClickCallback(function()
                self:_OnSaleCellClicked(ui, position)
            end)
            if self._selectedPos and position == self._selectedPos then
                self._selectedCell = ui
                ui:SetSelected(ui.item, true)
            else
                ui:SetSelected(ui.item, false)
            end

        else
            ui:InitCell(self._saleType, cellInfo, saleInfo.guide_price)
            ui:BindClicked(self._OnSaleCellClicked, self, position)
            if self._selectedPos and position == self._selectedPos then
                self._selectedCell = ui
                if ui.SetSelect then
                    ui:SetCppValue("Select", true)
                    ui:SetSelect()
                end
            else
                if ui.SetSelect then
                    ui:SetCppValue("Select", false)
                    ui:SetSelect()
                end
            end
            if self._wtScrollGridBox then
                local visibleItemCount = self._wtScrollGridBox:GetVisibleItemCount()
            end
            local bvisible = visibleItemCount >= position + 1
            -- 动画相关
            -- if bvisible then
            --     if not self._showAnimFinishList[position] then
            --         ui:PlayShowAnimation(position * self._PLAY_SHOW_ANIM_DELTA_TIME)
            --         self._showAnimFinishList[position] = true
            --     end
            -- end
        end
    end
end

function SaleListView:FreshSaleInfoShow(itemWidget, cellInfo)
    local item = ItemBase:NewIns(cellInfo.prop.id)
    item:SetRawPropInfo(cellInfo.prop)
    local info = 
    {
        prop_id = cellInfo.prop.id,
        cur_num = cellInfo.selling_num,
        guide_currency = cellInfo.price_currency,
        min_price = cellInfo.price
    }
    itemWidget:InitAuctionItem(item, info, false)
end

function SaleListView:UpdateView()
    if not self._item then
        return
    end
    self:UpdateItemDetailView()
    -- 判断是否开发交易
    if self._wtAveragePriceText and self._wtAveragePriceIcon and self._sale then
        self._wtAveragePriceText:SetText((self._sale.average_price and self._sale.average_price >0) and MathUtil.GetNumberFormatStr(self._sale.average_price) or "--")
        local dynamicGuidePrice = Server.ShopServer:GetShopSingleDynamicGuidePriceByItem(self._item, 1)
        -- if dynamicGuidePrice == 0 then
        --     self._wtShopPriceWrapper:Collapsed()
        -- else
        --     self._wtShopPriceWrapper:SelfHitTestInvisible()
        -- end
        -- self._wtShopPriceText:SetText(MathUtil.GetNumberFormatStr(dynamicGuidePrice))
        local currencyClientType = MapCurrencyId2ClientType[self._sale.guide_currency]
        local currencyIconPath = ECurrencyClientType2ImgPath[currencyClientType]
        if currencyIconPath then
            -- self._wtShopPriceIcon:AsyncSetImagePath(currencyIconPath, true)
            self._wtAveragePriceIcon:AsyncSetImagePath(currencyIconPath, false)
        end
    else
        if self._wtAveragePriceText then
            self._wtAveragePriceText:SetText("--")
        end
    end
    self._showAnimFinishList = {}
    if self._wtScrollGridBox then
        self._wtScrollGridBox:RefreshAllItems()
    end
    --self._wtItemDetailView:SetLabelMarkBtnVisible(false)
end

function SaleListView:UpdateItemDetailView()
    if not self._item then
        return
    end
    local auctionBuyModuleUnlockInfo = Module.ModuleUnlock:GetModuleUnlockInfoById(SwitchModuleID.ModuleAuctionBuy)
    self._bAuctionBuyUnlock = true
    if auctionBuyModuleUnlockInfo then
        self._bAuctionBuyUnlock = auctionBuyModuleUnlockInfo.bIsUnlocked
    end
    if self._sale and auctionBuyModuleUnlockInfo and not auctionBuyModuleUnlockInfo.bIsUnlocked then
        if self._wtSeasonLockText then
            self._wtSeasonLockText:SetText(auctionBuyModuleUnlockInfo.unlocktips)
        end
        if self._wtHbSeasonLock then
            self._wtHbSeasonLock:SelfHitTestInvisible()
        end
    else
        if self._wtHbSeasonLock then
            self._wtHbSeasonLock:Collapsed()
        end
    end
    local fGetShopBtnTxt = CreateCallBack(function(self, itemStuct, curSliderNum)
        local btnTxt = AuctionConfig.Loc.ItemSoldOutBtnText
        local cellInfo = self:GetSelectedCellInfo()
        if cellInfo then 
            local props, totalPrice
            if self._saleType == Module.Auction.Config.ESaleType.Weapon then
                totalPrice = cellInfo.price
            else
                props, totalPrice = AuctionLogic.checkSaleListToBuy(self._saleList, self._curNum)
            end
            local currencyClientType = MapCurrencyId2ClientType[cellInfo.price_currency]
            local currencyNum = Server.InventoryServer:GetPlayerCurrencyNum(currencyClientType) 
            local currencyIconTxt = ECurrencyClientType2RichIconTxtV2[currencyClientType] or ""
            if currencyNum < totalPrice and self._bAuctionBuyUnlock then
                btnTxt = string.format(AuctionConfig.Loc.AuctionGoodBuyLimitedPrice, currencyIconTxt, MathUtil.GetNumberFormatStr(totalPrice))
            else
                local param = {
                    ["currencyIconTxt"] = currencyIconTxt,
                    ["totalPrice"] = MathUtil.GetNumberFormatStr(totalPrice)
                }
                btnTxt = StringUtil.Key2StrFormat(AuctionConfig.Loc.AuctionGoodBuyPrice,param)
            end
            if isvalid(self._wtDetailViewBuyBtn) and self._wtDetailViewBuyBtn.SetPricePanel and self._wtDetailViewBuyBtn.UINavID == UIName2ID.ItemDetailViewBtnSliderBtn then
                if self._sale and self._sale.max_buy_num ~= 1 then
                    local averagePriceText = currencyIconTxt .. MathUtil.GetNumberFormatStr(math.floor(totalPrice / self._curNum))
                    self._wtDetailViewBuyBtn:SetPricePanel(AuctionConfig.Loc.AverageSinglePrice, averagePriceText)
                end 
            end
        else
            if isvalid(self._wtDetailViewBuyBtn) and self._wtDetailViewBuyBtn.SetPricePanel and self._wtDetailViewBuyBtn.UINavID == UIName2ID.ItemDetailViewBtnSliderBtn then
                if self._sale and self._sale.max_buy_num ~= 1 then
                    self._wtDetailViewBuyBtn:SetPricePanel()
                end 
            end
        end
        return btnTxt
    end, self)
    local btnNavId = UIName2ID.ItemDetailViewBtnSliderBtn
    if self._sale and self._sale.max_buy_num == 1 then
        btnNavId = UIName2ID.ItemDetailViewBtnOneBtn
    end
    -- BEGIN MODIFICATION @ VIRTUOS : 
    if IsHD() then
        self._DetailViewBuyBtnNavId = btnNavId
    end
    -- END MODIFICATION    
    -- if self._saleType == Module.Auction.Config.ESaleType.Armor or self._saleType == Module.Auction.Config.ESaleType.Weapon then
    --      btnNavId = UIName2ID.ItemDetailViewBtnOneBtn
    -- end
    local fBuyBtnClicked = CreateCallBack(self._OnBuyBtnClicked, self)
    local bBtnEnabled = self:GetBuyBtnIsEnable()
    local bBtnGrey = self:GetBuyBtnIsGrey()
    local styleIndex = self:GetBuyBtnStyleIdx()
    local cellInfo = self:GetSelectedCellInfo()
    local loadFinishCallback = function (btnIns)
        if btnIns then
            if self._sale and self._sale.max_buy_num ~= 1 and cellInfo then
                if cellInfo then
                    local props, totalPrice
                    if self._saleType == Module.Auction.Config.ESaleType.Weapon then
                        totalPrice = cellInfo.price
                    else
                        props, totalPrice = AuctionLogic.checkSaleListToBuy(self._saleList, self._curNum)
                    end
                    local currencyClientType = MapCurrencyId2ClientType[self._sale.guide_currency]
                    local currencyNum = Server.InventoryServer:GetPlayerCurrencyNum(currencyClientType) 
                    local currencyIconTxt = ECurrencyClientType2RichIconTxtV2[currencyClientType] or ""
                    local averagePriceText = currencyIconTxt .. MathUtil.GetNumberFormatStr(math.floor(totalPrice / self._curNum))
                    if btnIns.SetPricePanel then
                        btnIns:SetPricePanel(AuctionConfig.Loc.AverageSinglePrice, averagePriceText)
                    end
                else
                    if btnIns.SetPricePanel then
                        btnIns:SetPricePanel()
                    end
                end
            end
            local _wtBg = btnIns:Wnd("DFImage_BG", UIImag)
            if _wtBg then
                _wtBg:Collapsed()
            end
            if IsHD() then
                self:_RemoveNavigation()
                self._wtDetailViewBuyBtn = btnIns -- 在Button组件重新赋值前，需要清除掉旧Button的快捷键
                self:_RegisterNavigation()
            end
            -- self:CheckExchangeChannel() -- 依赖Server.ShopServer:SetLimitClicker(true)触发回调check，此处不再重复调用
        end
    end
    self._wtItemDetailView:SetIsOutBtnSlot(true) -- 先配置购买按钮于下方
    self._wtItemDetailView:SetStatePlayAnimOnShow(false) -- 配置动效不展示
    if cellInfo then
        self._oldPropGid = self._oldPropGid or cellInfo.prop.gid
        self._item.num = cellInfo.selling_num
        if not self._curNum or self._curNum == 0 or self._oldPropGid ~= cellInfo.prop.gid then
            self._curNum = 1
        end
        self._oldPropGid = cellInfo.prop.gid
        local selectItem  = ItemBase:NewIns(cellInfo.prop.id)
        local propInfo = {}
        deepcopy(propInfo, cellInfo.prop)
        if self._saleType == Module.Auction.Config.ESaleType.Armor then
            propInfo.health_max = cellInfo.prop.health -- 因为要满耐久才能上架，护具聚合了所以最大耐久就是当前耐久
        end
        propInfo.num = 1
        -- 标准预设枪械处理
        local weaponFeature = selectItem:GetFeature(EFeatureType.Weapon)
        local type = ItemHelperTool.GetMainTypeById(selectItem.id)
        if weaponFeature and weaponFeature:IsWeapon() and type == EItemType.Weapon then
            self:ChangePresetWeaponName(selectItem)
        else
            selectItem:SetRawPropInfo(propInfo)
        end
        local totalSellingNum = 0
        if self._saleList and #self._saleList > 0 then
            for _, v in ipairs(self._saleList) do
                totalSellingNum = totalSellingNum + v.selling_num
            end
        end
        local sliderMaxNum = math.min(totalSellingNum, (self._sale and self._sale.max_buy_num) and self._sale.max_buy_num or 1)  -- 为了限制滑动条最大数量
        self:RefreshItemDetailView(selectItem, true,
        {{fOnClick = fBuyBtnClicked,
            txt = fGetShopBtnTxt,
            bEnable = bBtnEnabled,
            bBtnGrey = bBtnGrey,
            styleIndex = styleIndex,
            PressedSoundNameAudioRes = DFMAudioRes.UIAHBuy,
            currencyClientType = self._sale.guide_currency}},
        btnNavId, nil, loadFinishCallback, self._curNum, nil, sliderMaxNum)
        if self._wtBuyPriceLine then
            self._wtBuyPriceLine:SelfHitTestInvisible()
        end
        if self._wtEmptyBgSlot then
            self._wtEmptyBgSlot:Collapsed()
        end
    else
        self._item.num = 1
        self._curNum = 0
        -- 判断是否开发交易
        if self._sale then
            -- 标准预设枪械处理
            local showItem = self._item
            local weaponFeature = self._item:GetFeature(EFeatureType.Weapon)
            local itemMainType = ItemHelperTool.GetMainTypeById(self._item.id)
            local itemSubType = ItemHelperTool.GetSubTypeById(self._item.id)
            if weaponFeature and weaponFeature:IsWeapon() and itemMainType == EItemType.Weapon then
                self:ChangePresetWeaponName(showItem)
            end
            -- 非满耐久护甲耐久度显示适配
            if itemMainType == EItemType.Equipment and (itemSubType == EEquipmentType.Helmet or itemSubType == EEquipmentType.BreastPlate) then
                if self._durabilityLvl ~= 1 then
                    local itemFeature = showItem:GetFeature()
                    local curDurabilityRatio = self._sale.durability_ratio
                    itemFeature.maxDurability = math.floor(itemFeature.maxDurability * curDurabilityRatio / 100)
                    itemFeature.curDurability = math.floor(itemFeature.curDurability * curDurabilityRatio / 100)
                end
            end
            self:RefreshItemDetailView(showItem, true,
            {{fOnClick = fBuyBtnClicked,
                txt = fGetShopBtnTxt,
                bEnable = bBtnEnabled,
                bBtnGrey = bBtnGrey,
                styleIndex = styleIndex}},
            btnNavId, nil, loadFinishCallback, self._curNum)
        else
            self:RefreshItemDetailView(self._item, false)
        end
        if self._wtEmptyBgSlot then
            Facade.UIManager:RemoveSubUIByParent(self, self._wtEmptyBgSlot)
            self._wtEmptyBgSlot:SelfHitTestInvisible()
            local weakUIIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.CommonEmptyContent, self._wtEmptyBgSlot)
            local emptyBg = getfromweak(weakUIIns)
            if emptyBg then
                if self._sale then
                    emptyBg:BP_SetText(Module.Auction.Config.Loc.NoSuchItemForSale)
                else
                    emptyBg:BP_SetText(Module.Auction.Config.Loc.NoOpenTransaction)
                end
                emptyBg:BP_SetTypeWithParam(1)
                emptyBg:Visible()
            end
        end
        if self._wtBuyPriceLine then
            self._wtBuyPriceLine:Collapsed()
        end
    end
    -- 设置道具详情面板中的更多来源呼吸灯
	local shopData = Module.Shop:GetJumpBuyDataById(self._item.id)
    -- 判断是否开发交易
	if shopData and self._sale then
		local shopItem = Server.ShopServer:GetShopItemByExchangeId(shopData.exchangeId)
		local bUnlock = shopItem:GetIsUnlock()
		if bUnlock then
            local buyPrice, currencyClientType = Server.ShopServer:GetShopBuyPriceByItem(shopItem.item)
            if buyPrice > 0 and buyPrice < self._sale.min_price then
                self._wtItemDetailView:PlayBreathAnimSource(Module.ItemDetail.Config.EItemSource.More)
            end
		end
    end

    self._wtItemDetailView:SetPriceVisible(false)
    self._wtItemDetailView:SetItemBindBtnInVisible()
    self._wtItemDetailView:SetCloseBtnVisible(false)
    self._wtItemDetailView:SetWeaponBulletVisible(false) -- 配置武器子弹不显示
    self._wtItemDetailView:SelfHitTestInvisible()
end

function SaleListView:GetBuyBtnStyleIdx()
    local styleIndex = 1
    local cellInfo = self:GetSelectedCellInfo()
    if cellInfo then
        local props, totalPrice
        if self._saleType == Module.Auction.Config.ESaleType.Weapon then
            totalPrice = cellInfo.price
        else
            props, totalPrice = AuctionLogic.checkSaleListToBuy(self._saleList, self._curNum)
        end
        local currencyClientType = MapCurrencyId2ClientType[cellInfo.price_currency]
        local currencyNum = Server.InventoryServer:GetPlayerCurrencyNum(currencyClientType)
        if currencyNum > totalPrice and self._bAuctionBuyUnlock and not self._bDisabledBuy and self:GetTransactionIsOpen() then
            styleIndex = 0
        else
            styleIndex = 1
        end
    end
    return styleIndex
end

function SaleListView:GetBuyBtnIsGrey()
    local bBtnGrey = true
    local cellInfo = self:GetSelectedCellInfo()
    if cellInfo then
        if self._bAuctionBuyUnlock and self:GetTransactionIsOpen() then
            bBtnGrey = false
        end
    else
        bBtnGrey = true
    end
    return bBtnGrey
end

function SaleListView:GetBuyBtnIsEnable()
    -- local bBtnEnabled = false
    -- local cellInfo = self:GetSelectedCellInfo()
    -- if cellInfo then
    --     if self._bAuctionBuyUnlock then
    --         bBtnEnabled = true
    --     end
    -- else
    --     bBtnEnabled = false
    -- end
    -- return bBtnEnabled
    return true -- She2改为任何状态均需点击反馈
end

function SaleListView:ChangePresetWeaponName(item)
    local weaponDesc = WeaponAssemblyTool.PresetRow_to_Desc(item.id)
    if weaponDesc then
        item:SetRawDescObj(weaponDesc)
    end
    local functionId = WeaponAssemblyTool.GetFunctionIdByPresetID(item.id, EArmedForceMode.SOL)
    if functionId then
        local functionItem = ItemBase:NewIns(functionId)
        item.name = functionItem.name
    end
end

function SaleListView:RefreshItemDetailView(...)
    if self._bFirstInit then
        self._bFirstInit = false
        self:InitItemDetailView(...)
    else
        self:ResetItemDetailView(...)
    end
end

function SaleListView:InitItemDetailView(...)
    self._wtItemDetailView:UpdateItem(...)
end

function SaleListView:ResetItemDetailView(itemStruct, bShowBtn, btnTypeList, btnNavId, addtionPanelSlot, loadFinishCallback, ...)
    if bShowBtn then
        self._wtItemDetailView:UpdateDetailViewOutContentSlotByAuctionBuyBtn(itemStruct, btnTypeList, addtionPanelSlot, loadFinishCallback, ...)
    end
end

function SaleListView:RefreshSpecialBarChart()
    if self._wtBarChartBox then
        local specialBarChartData = self:GetSpecialBarChartData()
        if specialBarChartData and not table.isempty(specialBarChartData) then
            for k, v in ipairs(self._wtBarChartBox:GetAllChildren()) do
                if specialBarChartData[k] and specialBarChartData[k].value then
                    v:InitGreenBar(specialBarChartData[k].value)
                end
            end
        end
    end
end

function SaleListView:GetSpecialBarChartData()
    if self._dataPoints and self._curNum then
        local specialBarChartData = {}
        local remainNum = self._curNum
        if remainNum then
            for k, v in ipairs(self._dataPoints) do
                if v[2] > 0 then
                    if remainNum >= v[2] then
                        table.insert(specialBarChartData, {position = k, value = v[2]})
                        remainNum = remainNum - v[2]
                    else
                        table.insert(specialBarChartData, {position = k, value = remainNum})
                        remainNum = 0
                    end
                else
                    table.insert(specialBarChartData, {position = k, value = 0})
                end
            end
        end
        return specialBarChartData
    end
end

function SaleListView:_OnBuyNumChanged(curNum, minNum, maxNum)
    if self._selectedPos == -1 then
        return
    end
    self._curNum = curNum
    self:RefreshSpecialBarChart()
end

function SaleListView:_OnBuyPerformanceProcess(res)
    -- Long1策划需求不再需要购买失败2秒禁售，因此注释掉
    -- if res.result ~= 0 then
    --     -- 购买失败，需要禁用2秒
    --     self._bDisabledBuy = true
    --     self:UpdateItemDetailView()
    --     Timer.DelayCall(self._BUYFAIL_CD_TIME, function()
            
    --         self._bDisabledBuy = false
    --         local param = {
    --             skip = nil,
    --             limit = nil
    --         }
    --         -- if self._saleType == Module.Auction.Config.ESaleType.Weapon then
    --         --     param.scope = nil
    --         --     param.attackModule = self._armorpiercingIndex - 1
    --         -- end
    --         Server.AuctionServer:FetchSaleList(self:GetPropId(), param)
    --     end, self)
    -- end
end

---@param ui SaleListCell
function SaleListView:_OnSaleCellClicked(ui, pos)
    if self._selectedCell == ui then
        return
    end

    if not self._canClick then
        Module.CommonTips:ShowSimpleTip(Module.Auction.Config.Loc.OnlyBuyLowestProp)
        return
    end

    if self._saleType == Module.Auction.Config.ESaleType.Armor then
        if self._selectedCell then
            self._selectedCell:SetCppValue("Select", false)
            self._selectedCell:SetSelect()
        end
        self._selectedPos = pos
        self._selectedCell = ui
        self._selectedCell:SetCppValue("Select", true)
        self._selectedCell:SetSelect()
    elseif self._saleType == Module.Auction.Config.ESaleType.Weapon then
        if self._selectedCell then
            self._selectedCell:SetSelected(self._selectedCell.item, false)
        end
        self._selectedPos = pos
        self._selectedCell = ui
        self._selectedCell:SetSelected(self._selectedCell.item, true)
    end
    self:UpdateItemDetailView()
end

function SaleListView:_OnBuyBtnClicked()
    if not self._item then
        return
    end
    if not self._bAuctionBuyUnlock then
        local auctionBuyModuleUnlockInfo = Module.ModuleUnlock:GetModuleUnlockInfoById(SwitchModuleID.ModuleAuctionBuy)
        if auctionBuyModuleUnlockInfo.unlocktips then
            Module.CommonTips:ShowSimpleTip(auctionBuyModuleUnlockInfo.unlocktips)
        end
        return
    end
    if not self:GetTransactionIsOpen() then
        Module.CommonTips:ShowSimpleTip(AuctionConfig.Loc.TypeListSubView_Nothing)
        return
    end
    if self._bDisabledBuy or self._bDelayDisabled then
        Module.CommonTips:ShowSimpleTip(AuctionConfig.Loc.ClickTooQuicklyToBuy)
        return
    end
    if self._selectedPos < 0 then
        local itemInfo = ItemConfigTool.GetItemConfigById(self._item.id)
        --标准预设枪处理
        local type = ItemHelperTool.GetMainTypeById(self._item.id)
        if type == EItemType.Weapon then
            local functionId = WeaponAssemblyTool.GetFunctionIdByPresetID(self._item.id, EArmedForceMode.SOL)
            itemInfo = ItemConfigTool.GetItemConfigById(functionId)
        end
        Module.CommonTips:ShowSimpleTip(string.format(AuctionConfig.Loc.ItemHasBeenSoldOut, itemInfo.Name))
        return
    end
    local cellInfo = self:GetSelectedCellInfo()
    if cellInfo then
        local buyNum = self._curNum or 0
        local props, totalPrice
        if self._saleType == Module.Auction.Config.ESaleType.Weapon then
            totalPrice = cellInfo.price
        else
            props, totalPrice = AuctionLogic.checkSaleListToBuy(self._saleList, self._curNum)
        end
        local currencyClientType = MapCurrencyId2ClientType[cellInfo.price_currency]
        local currencyNum = Server.InventoryServer:GetPlayerCurrencyNum(currencyClientType)
        if currencyNum < totalPrice then
            -- ShopHelperTool.ShowNotEnoughTipByCurrencyType(cellInfo.price_currency)
            ShopHelperTool.ShowNotEnoughTipByCurrencyId(cellInfo.price_currency)
        else
            if self._saleType == Module.Auction.Config.ESaleType.Weapon then
                AuctionLogic.DoBuyGoodProcess(cellInfo, buyNum, self._equipPos)
            else
                local function ChangeSilderValue(value)
                    Module.ItemDetail.Config.evtItemDetailSilderValueChanged:Invoke(value)
                end
                AuctionLogic.DoMultiPriceBuyProcess(self._item.id, buyNum, props, totalPrice, ChangeSilderValue)
            end
            self._bDelayDisabled = true
            Timer.DelayCall(0.5, function()
                self._bDelayDisabled = false
            end, self)
        end
    end


    -- local cellInfo = self:GetSelectedCellInfo()
    -- local buyNum = self._curNum or 0
    -- local totalPrice = cellInfo.price * buyNum
    -- local currencyClientType = MapCurrencyId2ClientType[cellInfo.price_currency]
    -- local currencyNum = Server.InventoryServer:GetPlayerCurrencyNum(currencyClientType)
    -- if currencyNum < totalPrice then
    --     ShopHelperTool.ShowNotEnoughConfirmByAuctionProp(cellInfo, buyNum)
    -- else
    --     AuctionLogic.DoBuyGoodProcess(cellInfo, buyNum, self._equipPos)
    --     self._bDelayDisabled = true
    --     Timer.DelayCall(0.5, function()
    --         self._bDelayDisabled = false
    --     end, self)
    -- end
end

function SaleListView:_OnRefreshBtnClicked()
    if not self._DEFAULT_REFRESH_BUTTON_CD_TIME then
        self._DEFAULT_REFRESH_BUTTON_CD_TIME = 5
        self:RefreshBtnCD()

        if self._timerHandle then
            self._timerHandle:Release()
            self._timerHandle = nil
        end
        self._timerHandle = Timer:NewIns(1, self._DEFAULT_REFRESH_BUTTON_CD_TIME + 1)
        self._timerHandle:AddListener(self.RefreshBtnCD, self)
        self._timerHandle:Start()
        -- 判断是否开发交易
        if self._sale then
            local param = {
                skip = nil,
                limit = nil
            }
            -- if self._saleType == Module.Auction.Config.ESaleType.Weapon then
            --     param.scope = nil
            --     param.attackModule = self._armorpiercingIndex - 1
            -- end
            Server.AuctionServer:FetchSaleList(self:GetPropId(), param)
        end
    else
        Module.CommonTips:ShowSimpleTip(
            string.format(AuctionConfig.Loc.TimeToRefresh, self._DEFAULT_REFRESH_BUTTON_CD_TIME .. AuctionConfig.Loc.SellListView_TimeSec)
        )
    end
end

function SaleListView:_SortFun()
    local function ASCSort(a, b)
        if self._saleType == Module.Auction.Config.ESaleType.Armor then
            local aDurability = a.prop.health / (a.prop.health_max + 1e-6)
            local bDurability = b.prop.health / (b.prop.health_max + 1e-6)
            if aDurability ~= bDurability then
                return aDurability < bDurability
            end
        else
            return a.price < b.price
        end
    end
    local function DESCSort(a, b)
        if self._saleType == Module.Auction.Config.ESaleType.Armor then
            local aDurability = a.prop.health / (a.prop.health_max + 1e-6)
            local bDurability = b.prop.health / (b.prop.health_max + 1e-6)
            if aDurability ~= bDurability then
                return aDurability > bDurability
            end
        else
            return a.price > b.price
        end
    end
    if self._saleList and not table.isempty(self._saleList) then
        -- 直接对server里的数据做排序影响到原始的数据，会导致每次都会根据排序后的数据再排序
        if self._sortMode == SORTMODE.ASC then
            table.insertionSort(self._saleList, ASCSort)
            -- table.sort(self._saleList, ASCSort)
        elseif self._sortMode == SORTMODE.DESC then
            table.insertionSort(self._saleList, DESCSort)
            -- table.sort(self._saleList, DESCSort)
        end
    end
end

function SaleListView:RefreshBtnCD()
    if not self._DEFAULT_REFRESH_BUTTON_CD_TIME then
        return
    end
    if self._DEFAULT_REFRESH_BUTTON_CD_TIME > 0 then
        if self._wtRefreshButton then
            self._wtRefreshButton:SetIsEnabledStyle(false)
        end
        self._DEFAULT_REFRESH_BUTTON_CD_TIME = self._DEFAULT_REFRESH_BUTTON_CD_TIME - 1
    else
        self._DEFAULT_REFRESH_BUTTON_CD_TIME = nil
        if self._wtRefreshButton then
            self._wtRefreshButton:SetIsEnabledStyle(true)
        end
    end
end

function SaleListView:_OnHandleMouseButtonUpEvent(mouseEvent)
    if self and not self:IsRelease() then
        local absolutePoint = mouseEvent:GetScreenSpacePosition()
        local bZoomRateInside = UIUtil.CheckAbsolutePointInsideWidget(self._wtZoomRateDroDown, absolutePoint)
        if not bZoomRateInside then
            self._wtZoomRateDroDown:BP_SwitchCheckButtonState(ECheckButtonState.Unchecked)
        end

        -- local bArmorpiercingInside = UIUtil.CheckAbsolutePointInsideWidget(self._wtArmorpiercingDroDown, absolutePoint)
        -- if not bArmorpiercingInside then
        --     self._wtArmorpiercingDroDown:BP_SwitchCheckButtonState(ECheckButtonState.Unchecked)
        -- end
    end
end

function SaleListView:OnGetZoomRateTabItemCount()
    local zoomRateDroDownLoc = Module.Auction.Config.ZoomRateDroDownLoc
    return #zoomRateDroDownLoc or 0
end

function SaleListView:OnZoomRateProcessTabItemWidget(position, droItem)
    local zoomRateDroDownLoc = Module.Auction.Config.ZoomRateDroDownLoc
    local dataIdx = position + 1
    ---@type DroDownData
    local droDownData = zoomRateDroDownLoc[dataIdx]
    if not droDownData then
        return
    end
    droItem:InitDroItem(droDownData, dataIdx)
end

function SaleListView:OnZoomRateCheckedTabIndexChanged(idx)
    loginfo("SaleListView:OnZoomRateCheckedTabIndexChanged idx =", idx)
    if self._zoomRateIndex ~= idx then
        self._saleList = {}
        self._zoomRateIndex = idx
        -- 判断是否开发交易
        if self._sale then
            local param = {
                skip = nil,
                limit = nil
            }
            if self._saleType == Module.Auction.Config.ESaleType.Weapon then
                param.scope = self._zoomRateIndex
                param.attackModule = self._armorpiercingIndex
            end
            Server.AuctionServer:FetchSaleList(self:GetPropId(), param)
            local zoomRateDroDownLoc = Module.Auction.Config.ZoomRateDroDownLoc
            self._wtZoomRateDroDown:BP_SetMainTabText(zoomRateDroDownLoc[self._zoomRateIndex + 1].keyText)
        end
    end
end

function SaleListView:OnGetArmorpiercingTabItemCount()
    local armorpiercingDroDownLoc = Module.Auction.Config.ArmorpiercingDroDownLoc
    return #armorpiercingDroDownLoc or 0
end

function SaleListView:OnArmorpiercingProcessTabItemWidget(position, droItem)
    local armorpiercingDroDownLoc = Module.Auction.Config.ArmorpiercingDroDownLoc
    local dataIdx = position + 1
    ---@type DroDownData
    local droDownData = armorpiercingDroDownLoc[dataIdx]
    if not droDownData then
        return
    end
    droItem:InitDroItem(droDownData, dataIdx)
end

function SaleListView:OnArmorpiercingCheckedTabIndexChanged(idx)
    if self._armorpiercingIndex ~= idx then
        self._saleList = {}
        self._armorpiercingIndex = idx
        -- 判断是否开发交易
        if self._sale then
            local param = {
                skip = nil,
                limit = nil
            }
            if self._saleType == Module.Auction.Config.ESaleType.Weapon then
                param.scope = self._zoomRateIndex
                param.attackModule = self._armorpiercingIndex
            end
            Server.AuctionServer:FetchSaleList(self:GetPropId(), param)
            local armorpiercingDroDownLoc = Module.Auction.Config.ArmorpiercingDroDownLoc
            self._wtArmorpiercingDroDown:BP_SetMainTabText(armorpiercingDroDownLoc[self._armorpiercingIndex + 1].keyText)
        end
    end
end

function SaleListView:CheckWeaponSaleListoBuy()
    local props = {}
    local totalPrice = 0
    local data = {}
    local cellInfo = self:GetSelectedCellInfo()
    if cellInfo then
        data[1] = cellInfo
        data[2] = self._curNum
        table.insert(props, data)
        totalPrice = cellInfo.price
    end
    return props, totalPrice
end

function SaleListView:CheckShopItemList()
    local bPass, bCanBuyOrExchange, hasAnyUnlockItem, hasAnyNotSufficientItem = Server.ShopServer:CheckShopItemListById(self:GetExchangeItemId())
    if self._item and self._item.id then
        local itemMainType = ItemHelperTool.GetMainTypeById(self._item.id)
        local itemSubType = ItemHelperTool.GetSubTypeById(self._item.id)
        -- 非满耐久护甲耐久度显示适配
        if itemMainType == EItemType.Equipment and (itemSubType == EEquipmentType.Helmet or itemSubType == EEquipmentType.BreastPlate) then
            if self._durabilityLvl ~= 1 then
                bCanBuyOrExchange = false
            end
        end
    end
    return bPass, bCanBuyOrExchange, hasAnyUnlockItem, hasAnyNotSufficientItem
end

function SaleListView:CheckExchangeChannel()
    local itemDataList = self:GetExchangeDataList()
    local fOnExchangeClickedCheckExchangeChannelCallback = CreateCallBack(function(self, bCanExchange, bIsCheaper, mapId2cheapestShopInfo)
        self._bCanExchange = bCanExchange
        if isvalid(self._wtDetailViewBuyBtn) then
            local bPass, bCanBuyOrExchange, hasAnyUnlockItem, hasAnyNotSufficientItem = self:CheckShopItemList()
            if self._wtDetailViewBuyBtn.InitBtn2Content then
                self._wtDetailViewBuyBtn:InitBtn2Content(bCanBuyOrExchange, self._bCanExchange, Module.Auction.Config.Loc.Exchange,
                 CreateCallBack(self._OnExchangeClicked, self, mapId2cheapestShopInfo), CreateCallBack(self._OnExchangeDeClicked, self))
            end
            if self._wtDetailViewBuyBtn.Wnd then
                self._wtExchangeTips = self._wtDetailViewBuyBtn:Wnd("DFCanvasPanel_126", UIWidgetBase)
            end
        end
        if bIsCheaper then
            if isvalid(self._wtExchangeTips) and self._wtExchangeTips.SelfHitTestInvisible then
                self._wtExchangeTips:SelfHitTestInvisible()
                if isvalid(self._wtDetailViewBuyBtn) and self._wtDetailViewBuyBtn.PlayAnimation then
                    self._wtDetailViewBuyBtn:PlayAnimation(self._wtDetailViewBuyBtn.WBP_CommonTipsBg_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
                end
            end
        else
            if isvalid(self._wtExchangeTips) and self._wtExchangeTips.Collapsed then
                self._wtExchangeTips:Collapsed()
            end
        end
    end, self)
    Module.Shop:CheckExchangeChannel(itemDataList, fOnExchangeClickedCheckExchangeChannelCallback, true)
end

function SaleListView:GetExchangeDataList()
    local exchangeItemId = self:GetExchangeItemId()
    if not exchangeItemId then
        return
    end
    local itemDataList = {}
    local itemData = {
        id = exchangeItemId,
        num = self._curNum,
    } 
    table.insert(itemDataList, itemData)
    return itemDataList
end

function SaleListView:GetExchangeItemId()
    local itemId = self._item.id
    if not itemId then
        return
    end
    local type = ItemHelperTool.GetMainTypeById(itemId)
    if type == EItemType.Weapon then
        local functionId = WeaponAssemblyTool.GetFunctionIdByPresetID(itemId, EArmedForceMode.SOL)
        if functionId then
            return functionId
        end
    end
    return itemId
end

function SaleListView:_OnExchangeClicked(mapId2cheapestShopInfo)
    local bPass, bCanBuyOrExchange, hasAnyUnlockItem, hasAnyNotSufficientItem = self:CheckShopItemList()
    if not bCanBuyOrExchange then
        return
    end
    local exchangeItemId = self:GetExchangeItemId()
    if not exchangeItemId then
        return
    end
    local shopItem = Server.ShopServer:GetPurchaseShopItemById(exchangeItemId)
    if shopItem and shopItem.exchangeId then
        local function fCallback(exchangeId)
            Module.Shop:JumpToShopItemViewByExchangeId(exchangeId)
            Module.Shop:SetShopEntranceType(LogAnalysisTool.EMallExtranceType.Auction)
        end
        Module.Jump:JumpTo(JumpConfig.EJumpToModule.Shop, shopItem.exchangeId, fCallback)
    else
        local itemDataList = self:GetExchangeDataList()
        local function fExchangeFinished(isFullySucceed)
            -- self:CheckExchangeChannel()
        end
        Module.Shop:ShowShopPopWindow(itemDataList, fExchangeFinished, LogAnalysisTool.EMallExtranceType.Auction)
    end
end

function SaleListView:_OnExchangeDeClicked()
    local bPass, bCanBuyOrExchange, hasAnyUnlockItem, hasAnyNotSufficientItem = self:CheckShopItemList()
    if bPass then
        return
    end
    -- 无该道具ID的直售/兑换途径，“该物资无法兑换”
    if not bCanBuyOrExchange then
        Module.CommonTips:ShowSimpleTip(Module.Auction.Config.Loc.CanNotBuyOrExchange)
        return
    end
    -- 玩家未解锁该物资的军需处兑换，即不满足等级或任务或声望等解锁条件，“该物资兑换暂未解锁”
    if hasAnyUnlockItem then
        Module.CommonTips:ShowSimpleTip(Module.Auction.Config.Loc.ExchangeLocked)
        return
    end
    -- 兑换途径的限购次数当前已达上限时，“当前兑换次数已达上限”
    if hasAnyNotSufficientItem then
        Module.CommonTips:ShowSimpleTip(Module.Auction.Config.Loc.ExchangeNotSufficient)
        return
    end
end

-- BEGIN MODIFICATION @ VIRTUOS : 新增手柄快捷键（左右滑动价格曲线滑动条）
function SaleListView:_RegisterNavigation()
    if not IsHD() then
        return
    end
    if not self._wtRefreshGroup then
        self._wtRefreshGroup = WidgetUtil.RegisterNavigationGroup(self._wtRefreshButton, self, "Hittest")
        if self._wtRefreshGroup then
            self._wtRefreshGroup:AddNavWidgetToArray(self._wtRefreshButton)
        end
    end

    -- 购买物品详细信息: 详细信息组件较复杂，需要获取其中的控件分别创建导航组
    -- 靠上的Icon部分
    if not self._wtRootGroup then
        self._wtRootGroup = WidgetUtil.RegisterNavigationGroup(self._wtItemDetailView, self, "Hittest")
        local Detail_IconCanvas = self._wtItemDetailView:Wnd("CanvasPanel_Icon", UIWidgetBase)
        if not self._wtNavGroup_Detail_IconCanvas then
            self._wtNavGroup_Detail_IconCanvas = WidgetUtil.RegisterNavigationGroup(Detail_IconCanvas, self, "Hittest")
            if self._wtNavGroup_Detail_IconCanvas then
                self._wtNavGroup_Detail_IconCanvas:AddNavWidgetToArray(Detail_IconCanvas)
            end
        end
        -- ScrollBox组件
        local Detail_ScrollBox = self._wtItemDetailView:Wnd("wItemDetailContent", UIWidgetBase):Wnd("wScrollBoxContent", UIWidgetBase)
        -- ScrollBox 上侧的按钮
        local Detail_ScrollBox_Buttons = self._wtItemDetailView:Wnd("wItemDetailContent", UIWidgetBase):Wnd("WBP_ItemDetailPanelBtn", UIWidgetBase)
        if not self._wtNavGroup_Detail_Buttons then
            self._wtNavGroup_Detail_Buttons = WidgetUtil.RegisterNavigationGroup(Detail_ScrollBox_Buttons, self, "Hittest")
            if self._wtNavGroup_Detail_Buttons then
                self._wtNavGroup_Detail_Buttons:AddNavWidgetToArray(Detail_ScrollBox_Buttons)
                self._wtNavGroup_Detail_Buttons:SetScrollRecipient(self._wtItemDetailView._wtScrollBoxContent)
            end
        end
        -- ScrollBox 其他部分
        local Detail_ScrollBox_Other = self._wtItemDetailView:Wnd("wItemDetailContent", UIWidgetBase):Wnd("_wRealContentSlot", UIWidgetBase)
        if not self._wtNavGroup_Detail_Other then
            self._wtNavGroup_Detail_Other = WidgetUtil.RegisterNavigationGroup(Detail_ScrollBox_Other, self, "Hittest")
            if self._wtNavGroup_Detail_Other then
                self._wtNavGroup_Detail_Other:AddNavWidgetToArray(Detail_ScrollBox_Other)
                self._wtNavGroup_Detail_Other:SetScrollRecipient(self._wtItemDetailView._wtScrollBoxContent)
                -- 这个Group内部分组件距离太近，需要对重叠的部分进行处理
                local SafeBoxNavStrategy = self._wtNavGroup_Detail_Other:GetOwnerNavStrategy()
                if SafeBoxNavStrategy then
                    SafeBoxNavStrategy:SetHitPadding(5.0)
                end
            end
        end

        -- 靠下的Slot
        local Detail_UnderSlot = self._wtItemDetailView:Wnd("wUnderContentSlot", UIWidgetBase)
        if not self._wtNavGroup_Detail_UnderSlot then
            self._wtNavGroup_Detail_UnderSlot = WidgetUtil.RegisterNavigationGroup(Detail_UnderSlot, self, "Hittest")
            -- 这里只把滑动条本身加进去
            if self._wtNavGroup_Detail_UnderSlot and self._wtDetailViewBuyBtn and self._wtDetailViewBuyBtn._wtDFCommonAddDecSliderV1
                and self._wtDetailViewBuyBtn._wtDFCommonAddDecSliderV1.wtDFCommonAddDecSlider then
                self._wtNavGroup_Detail_UnderSlot:AddNavWidgetToArray(self._wtDetailViewBuyBtn._wtDFCommonAddDecSliderV1.wtDFCommonAddDecSlider.DFCommonAddDecHolder.SlotContainer)
            end
            WidgetUtil.BuildGroupTree(self._wtItemDetailView)
            self:_TryFocusToDefaultWidgetGamepad()
        end
    end
    self:_InitShortcuts()
end

function SaleListView:_TryFocusToDefaultWidgetGamepad()
    if not IsHD() then
        return
    end
    Timer.DelayCall(0.02, function()
        if self._wtNavGroup_Detail_UnderSlot and self._wtDetailViewBuyBtn and self._wtDetailViewBuyBtn._wtDFCommonAddDecSliderV1
         and self._wtDetailViewBuyBtn._wtDFCommonAddDecSliderV1.wtDFCommonAddDecSlider then
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup_Detail_UnderSlot)
            return
        end
        if self._wtNavGroup_Detail_Buttons then
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup_Detail_Buttons)
            return
        end
        if self._wtNavGroup_Detail_IconCanvas then
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup_Detail_IconCanvas)
            return
        end
        if self._wtRefreshGroup then
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtRefreshGroup)
            return
        end
    end, self)
end

function SaleListView:_RemoveNavigation()
    if not IsHD() then
        return
    end
    self:_RemoveShortcuts()
    if self._wtRefreshGroup or self._wtRootGroup or self._wtNavGroup_Detail_Buttons or self._wtNavGroup_Detail_IconCanvas 
    or self._wtNavGroup_Detail_Othe or self._wtNavGroup_Detail_UnderSlot then
        WidgetUtil.RemoveNavigationGroup(self)
        self._wtRefreshGroup = nil
        self._wtRootGroup = nil
        self._wtNavGroup_Detail_Buttons = nil
        self._wtNavGroup_Detail_IconCanvas = nil
        self._wtNavGroup_Detail_Other = nil
        self._wtNavGroup_Detail_UnderSlot = nil
    end
end

function SaleListView:_InitShortcuts()
    if not IsHD() then
        return
    end
    if not isvalid(self._wtDetailViewBuyBtn) then
        return
    end
    if self._DetailViewBuyBtnNavId == UIName2ID.ItemDetailViewBtnSliderBtn then
        if self._wtDetailViewBuyBtn.InitShortcuts then
            self._Purchase = self._wtDetailViewBuyBtn:InitShortcuts()
        end
        if self._wtDetailViewBuyBtn.SetBtn2DisplayInputAction then
            self._wtDetailViewBuyBtn:SetBtn2DisplayInputAction("Common_ButtonTop", true, nil, true)
            self._Exchange = self:AddInputActionBinding("Common_ButtonTop", EInputEvent.IE_Pressed, self._OnExchange, self, EDisplayInputActionPriority.UI_Stack) -- 因self._OnExchangeClicked暂时用不到mapId2cheapestShopInfo，可直接绑定，否则得存一下mapId2cheapestShopInfo写闭包或存一下之前写的闭包
        end
    elseif self._DetailViewBuyBtnNavId == UIName2ID.ItemDetailViewBtnOneBtn then
        self._Purchase = self:AddInputActionBinding("ArmedForce_Purchase_Gamepad", EInputEvent.IE_Pressed, self._OnBuyBtnClicked, self, EDisplayInputActionPriority.UI_Stack)
        if self._wtDetailViewBuyBtn.SetDisplayInputAction then
            loginfo("SaleListView:_InitShortcuts self._wtDetailViewBuyBtn.SetDisplayInputAction is not nil")
            self._wtDetailViewBuyBtn:SetDisplayInputAction("Common_ButtonTop", true, nil, true)
            self._Exchange = self:AddInputActionBinding("Common_ButtonTop", EInputEvent.IE_Pressed, self._OnExchange, self, EDisplayInputActionPriority.UI_Stack) -- 因self._OnExchangeClicked暂时用不到mapId2cheapestShopInfo，可直接绑定，否则得存一下mapId2cheapestShopInfo写闭包或存一下之前写的闭包
        end
    else
        if self._wtDetailViewBuyBtn.SetBtn2DisplayInputAction then
            self._wtDetailViewBuyBtn:SetBtn2DisplayInputAction("Common_ButtonTop", true, nil, true)
            self._Exchange = self:AddInputActionBinding("Common_ButtonTop", EInputEvent.IE_Pressed, self._OnExchange, self, EDisplayInputActionPriority.UI_Stack) -- 因self._OnExchangeClicked暂时用不到mapId2cheapestShopInfo，可直接绑定，否则得存一下mapId2cheapestShopInfo写闭包或存一下之前写的闭包
        end
    end
end

function SaleListView:_RemoveShortcuts()
    if not IsHD() then
        return
    end
    Module.CommonBar:RecoverBottomBarInputSummaryList()

    if isvalid(self._wtDetailViewBuyBtn) and self._wtDetailViewBuyBtn.RemoveShortcuts then
        self._wtDetailViewBuyBtn:RemoveShortcuts(self._Purchase)
    end
    if self._Purchase then
        self:RemoveInputActionBinding(self._Purchase)
        self._Purchase = nil
    end
    if self._Exchange then
        self:RemoveInputActionBinding(self._Exchange)
        self._Exchange = nil
    end
end

function SaleListView:_OnExchange()
    if not IsHD() then
        return
    end
    local bPass, bCanBuyOrExchange, hasAnyUnlockItem, hasAnyNotSufficientItem = self:CheckShopItemList()
    if not bCanBuyOrExchange then
        return
    end
    if bCanBuyOrExchange and self._bCanExchange then
        self:_OnExchangeClicked()
        return
    end
    if bCanBuyOrExchange and not self._bCanExchange then
        self:_OnExchangeDeClicked()
        return
    end
end

-- END MODIFICATION

return SaleListView