----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMFriend)
local FriendLogic = require "DFM.Business.Module.FriendModule.Logic.FriendLogic"
----- LOG FUNCTION AUTO GENERATE END -----------



---@class FriendBoxHD : LuaUIBaseView
local FriendBoxHD = ui("FriendBoxHD")
local CommonConfig = Module.CommonWidget.Config
require("DFM.YxFramework.Managers.Resource.Util.ResImageUtil")
local EButtonState = import "EButtonState"

--- BEGIN MODIFICATION @ VIRTUOS
-- 输入设备相关
local UGPInputDelegates = import "GPInputDelegates"
local EGPInputType = import"EGPInputType"
local UGPInputHelper = import "GPInputHelper"
--- END MODIFICATION

function FriendBoxHD:Ctor()
    loginfo("FriendBoxHD:Ctor")
    self._wtPlayerNameText = self:Wnd("wtPlayerNameText", UITextBlock)
    self._wtIntimacyText = self:Wnd("wtIntimacyText", UITextBlock)
    self._wtPlayerIcon = self:Wnd("WBP_CommonHeadIcon", CommonHeadIcon)
    self._wtPlayerState = self:Wnd("wtPlayerState", UITextBlock)
    self._wtTeamFriend = self:Wnd("TeamBtn", UIWidgetBase)
    self._wtTeamFriend:Event("OnClicked", self._OnBtnSendFriendOnTeam, self)
    self._wtRoomInvite = self:Wnd("TeamBtn_1", UIWidgetBase)
    self._wtRoomInvite:Event("OnClicked", self._OnBtnSendFriendOnRoom, self)
    self._wtOffLineImage = self:Wnd("Masks", UIWidgetBase)
    self._wtplatIcon = self:Wnd("wtplatIcon", UIImage)
    self._wtRankIcon = self:Wnd("wtRankIcon" ,UIWidgetBase)
    self._wtRankDivision = self:Wnd("wtRankDivision", UITextBlock)
    self._wtMilitary = self:Wnd("WBP_Friend_BrandAvatar", UIWidgetBase)
    self._wtPTPanel = self:Wnd("DFCanvasPanel", UIWidgetBase)
    self._wtPraise = self:Wnd("WBP_TeamMatesPraise", UIWidgetBase)
    self._wtPraise:Event("OnClicked", self._OnPraiseClick, self)
    self._wtBHDMark = self:Wnd("WBP_BHD_OwnMark",UIWidgetBase)
    self._wtDFTipsAnchor = UIUtil.WndTipsAnchor(self._wtBHDMark, "DFTipsAnchor", self.ShowTips,self.CloseTips)
    self._wtPraise:Collapsed()
    self._friendInfo = {}
    Module.Social.Config.Events.evtOnPlayerIdCoolDownTimeRefresh:AddListener(self.RefreshInviteColdDownTime, self)
    self:AddLuaEvent(Module.Friend.Config.Events.evtFriendSuccessfullySentAnInvitationToFriends, self.RefreshInviteColdDownTime, self)

    --- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self._wtTeamBtnContainer = self:Wnd("DFHorizontalBox_1", UIWidgetBase)
    end
    --- END MODIFICATION
end

function FriendBoxHD:OnInitExtraData(data, panelType)
    self:ShowUI(data, panelType)
end


function FriendBoxHD:ShowUI(data, panelType)
    self._panelType = panelType
    self:Event("OnHovered",self._OnHovered,self)
    self:Event("OnUnHovered",self._OnUnHovered,self)
    self._wtplatIcon:Visible()
    self._wtPTPanel:Visible()
    self._friendInfo = data
    loginfo("FriendData")
    logtable(self._friendInfo,true)
    if self._wtMilitary then
        self._wtMilitary:SetMilitary(data.military_tag)
    end
    if self._friendInfo.nick_name then
        self._wtPlayerNameText:SetText(self._friendInfo.nick_name)
    end
    self._wtIntimacyText:SetText(self._friendInfo.favorability)
    FriendLogic.SetPlayerStateCode(self._wtPlayerState, self._friendInfo, self._wtplatIcon)
    --- BEGIN MODIFICATION @ VIRTUOS
    self._wtplatIcon:AsyncSetImagePath(FriendLogic.GetPlatformIconPath(self._friendInfo.plat, false, false), false)
    --- END MODIFICATION
    self._playIconInfo = {
        pic_url = self._friendInfo.pic_url,
        player_id = self._friendInfo.player_id,
        level = 0,
        nick_name = self._friendInfo.nick_name,
        military_tag = self._friendInfo.military_tag,
        --- BEGIN MODIFICATION @ VIRTUOS
        -- 好友是否已经被点赞
        bIsPraise = false,
        -- 显示平台好友
        bIsPlatformOnlyFriend = self._friendInfo.bIsPlatformOnlyFriend,
        platformId = self._friendInfo.PlatformId,
        -- 增加平台logo
        plat = self._friendInfo.plat,
        --- END MODIFICATION
    }

    local isSol = Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse
    self:RefreshInviteColdDownTime(self._friendInfo.player_id)

    --- BEGIN MODIFICATION
    -- 好友是否已经被点赞
    if IsHD() then
        self._playIconInfo.bIsPraise = self._friendInfo.IsPraise
        self._playIconInfo.friendPanelType = panelType
    end
    --- END MODIFICATION

    if self._friendInfo.IsPraise then
        self._wtPraise:SetIsEnabledStyle(false)
    else
        self._wtPraise:SetIsEnabledStyle(true)
    end

    if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse then
        self._playIconInfo.level = self._friendInfo.season_level
        self._playIconInfo.rank_attended = self._friendInfo.sol_attended
        self._playIconInfo.rank_score = self._friendInfo.sol_rank_score
    elseif Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Lobby then
        self._playIconInfo.level = self._friendInfo.level
        self._playIconInfo.rank_attended = self._friendInfo.mp_attended
        self._playIconInfo.rank_score = self._friendInfo.mp_rank_score

        -- azhengzheng:传入胜者为王段位信息
        self._playIconInfo.show_commander_rank_points = self._friendInfo.show_commander_rank_points
        self._playIconInfo.mp_commander_score = self._friendInfo.mp_commander_score
    end
    FriendLogic.InitFriendRank(self._wtRankIcon, self._wtRankDivision, self._friendInfo)

    --- 主机平台以及discord好友不支持功能
    --- BEGIN MODIFICATION @ VIRTUOS: 显示平台好友
    if self._friendInfo.bIsPlatformOnlyFriend then
        -- 关闭段位图标
        self._wtRankIcon:Collapsed()
        -- 关闭段位文本
        self._wtRankDivision:Collapsed()
        self._wtplatIcon:Collapsed()

        -- 关闭点赞按钮
        self._wtPraise:Collapsed()
    else
        self._wtRankIcon:Visible()
        self._wtRankDivision:Visible()
    end
    --- END MODIFICATION

    local btnTbl = {
        HeadButtonType.PlayerInformat,
        HeadButtonType.Chat,
    }

    if FriendLogic.IsTheSamePlatformWithMe(self._friendInfo) then
        table.insert(btnTbl, HeadButtonType.InviteAppTeam)
    end

    --- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        table.insert(btnTbl, HeadButtonType.MatesPraise)
    end
    --- END MODIFICATION
    
    if panelType == 1 then
        self._wtPTPanel:Visible()
        table.insert(btnTbl, HeadButtonType.DelFriend)
    else
        self._wtPTPanel:Collapsed()
        table.insert(btnTbl, HeadButtonType.AddFriend)
    end

    self._wtPlayerIcon:InitPortrait(self._playIconInfo, HeadIconType.HeadList, btnTbl)

    if self._friendInfo.remarks ~= "" then
        local nameStr = string.format(Module.Friend.Config.QQFriend, self._friendInfo.remarks, self._friendInfo.nick_name)
        local width,newStr = StringUtil.GetShowWidthV2(nameStr, 20)
        if tostring(nameStr) == newStr then
            self._wtPlayerNameText:SetText(newStr)
        else
            self._wtPlayerNameText:SetText(newStr .. "...")
        end
    else
        if self._friendInfo.friend_type == FriendType.OpenGameFriend or self._panelType == 3 then   -- Discord好友特殊处理
            self._wtPlayerNameText:SetText(string.format(Module.Friend.Config.QQFriend, self._friendInfo.nick_name, self._friendInfo.open_nick_name))
        elseif self._friendInfo.friend_type == FriendType.OpenFriend then
            self._wtPlayerNameText:SetText(string.format(Module.Friend.Config.QQFriend, self._friendInfo.nick_name, self._friendInfo.open_nick_name))
        end
    end

    --- BEGIN MODIFICATION @ VIRTUOS: 显示平台好友
    if self._friendInfo.bIsPlatformOnlyFriend then
        self._wtPlayerNameText:SetText(self._friendInfo.open_nick_name)

        -- Set PS5 platform icon z-order higher than the mask for TRC.
        if IsPS5Family() and self._playIconInfo.plat == PlatIDType.Plat_Playstation then
            self._wtPlayerIcon.Slot:SetZorder(3)
        end
    end
    -- TRC: using Platform Online ID as player name.
    if IsPS5Family() then
        local callback = function(onlineID)
            self._wtPlayerNameText:SetText(onlineID)
        end
        Module.Social:AsyncGetPS5OnlineIdByUID(self._friendInfo.player_id, callback, self)
    end
    --- END MODIFICATION

    local FriendTeamId = self._friendInfo.team_id
    local SelfFriendId = Server.TeamServer:GetTeamId()
    local state = Module.Social:GetOnlinePlayerStateFromStateCode(self._friendInfo.state)

    if Module.Friend:IsFriendOffLineOrInvisible(state, self._friendInfo.player_id) then
        self._wtOffLineImage:HitTestInvisible()
    else
        self._wtOffLineImage:Collapsed()
    end

    --- BEGIN MODIFICATION @ VIRTUOS: 显示平台好友
    if IsXboxSeries() then
        self._wtTeamBtnContainer:Collapsed()
    end
--     if state == GlobalPlayerStateEnums.EPlayerState_Offline or state == GlobalPlayerStateEnums.EPlayerState_InMatch or 
--     (FriendTeamId ~= 0 and SelfFriendId ~= 0 and FriendTeamId == SelfFriendId) or self._friendInfo.bIsPlatformOnlyFriend then
-- --- END MODIFICATION
--         self._wtTeamFriend:Collapsed()
--         self._wtRoomInvite:Collapsed()
--     else
--         if Module.Room:GetIsInRoom() then
--             self._wtRoomInvite:Visible()
--             self._wtTeamFriend:Collapsed()
--         else
--             self._wtRoomInvite:Collapsed()
--             self._wtTeamFriend:Visible()
--             local bInvite = Module.Social:IsInvite(self._friendInfo)
--             local teamImage = bInvite and CommonConfig.InviteTeamBtnImage or CommonConfig.JoinTeamBtnImage
--             self._wtTeamFriend:AsyncSetImageIconPathAllState(teamImage)
--         end
--     end

    -- azhengzheng:覆写旧逻辑，新增预约功能
    if (not FriendLogic.IsTheSamePlatformWithMe(self._friendInfo)) or Module.Friend:IsFriendOffLineOrInvisible(state, self._friendInfo.player_id) or (FriendTeamId ~= 0 and SelfFriendId ~= 0 and FriendTeamId == SelfFriendId) or self._friendInfo.bIsPlatformOnlyFriend then
        self._wtRoomInvite:Collapsed()
        self._wtTeamFriend:Collapsed()
    elseif Module.Room:GetIsInRoom() then
        self._wtRoomInvite:Visible()
        self._wtTeamFriend:Collapsed()
    else
        self._wtRoomInvite:Collapsed()
        self._wtTeamFriend:Visible()

        local funCallBack = function(loadCaller, imageAsset, bAutoResize)
            self._wtTeamFriend:SetImageIconState(imageAsset, EButtonState.Disabled)
        end

        if state == GlobalPlayerStateEnums.EPlayerState_InMatch then
            self._wtTeamFriend:AsyncSetImageIconPathAllState(CommonConfig.ReservationTeamUpImg)
            ResImageUtil.AsyncLoadImgObjByPath(CommonConfig.TeamUpFinishImg, true, funCallBack, self)
        else
            if Module.Social:IsInvite(self._friendInfo) then
                self._wtTeamFriend:AsyncSetImageIconPathAllState(CommonConfig.InviteTeamBtnImage)
            else
                self._wtTeamFriend:AsyncSetImageIconPathAllState(CommonConfig.JoinTeamBtnImage)
            end

            ResImageUtil.AsyncLoadImgObjByPath(CommonConfig.TeamInviteFinishImg, true, funCallBack, self)
        end
    end

    --self:SetBHDOwnMark(self._friendInfo)

    --如果在房间中，显示邀请进入房间按钮
end

--- BEGIN MODIFICATION @ VIRTUOS
function FriendBoxHD:OnShowBegin()
    if IsHD() then
        self:_EnableGamepadFeature()
    end
end

function FriendBoxHD:OnHideBegin()
    if IsHD() then
        self:_DisableGamepadFeature()
    end
end

function FriendBoxHD:_EnableGamepadFeature()
    if not IsHD() then
        return 
    end

    -- 绑定多输入设备切换事件
    self._OnNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._OnInputTypeChanged, self))
    -- 初始化：根据当前输入设备来设置TeamButtons的可见性
    local curInpurtType = UGPInputHelper.GetCurrentInputType(GetGameInstance())
    self:_OnInputTypeChanged(curInpurtType)
end

function FriendBoxHD:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end

    if self._OnNotifyInputTypeChangedHandle then
        UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._OnNotifyInputTypeChangedHandle)
        self._OnNotifyInputTypeChangedHandle = nil
    end
end

--- END MODIFICATION

function FriendBoxHD:RefreshInviteColdDownTime(playerId)
    if not self._friendInfo or not self._friendInfo.player_id or self._friendInfo.player_id ~= playerId then
        return
    end

    local coolDownTime = Module.Social:GetCoolDownPlayerTime(playerId)
    self:_SetColdTimeImage(coolDownTime > 0)
    self:_SetRoomBtnEnbaleState(coolDownTime > 0)
end

function FriendBoxHD:_SetColdTimeImage(isCool)
    if isCool then
        self._wtTeamFriend:SetBtnEnable(false)
    else
        self._wtTeamFriend:SetBtnEnable(true)
    end
end

function FriendBoxHD:_SetRoomBtnEnbaleState(bDisabled)
    if self._wtRoomInvite then
        self._wtRoomInvite:SetBtnEnable(not bDisabled)
    end
end

function FriendBoxHD:_OnBtnSendFriendOnTeam()
    if Server.MatchServer:GetIsMatching() then
        Module.CommonTips:ShowSimpleTip(Module.Social.Config.Loc.InMatching)
        return
    end

    -- 预约跟旧的组队逻辑是同一套
    Module.Social:TeamInvite(self._friendInfo,TeamInviteSource.FromFriend,true)
    self:_SetColdTimeImage(true)
end

function FriendBoxHD:OnClose()
    self:RemoveAllLuaEvent()
end

function FriendBoxHD:OnOpen()
    if self._friendInfo.IsPraise then
        self._wtPraise:SetIsEnabledStyle(false)
    else
        self._wtPraise:SetIsEnabledStyle(true)
    end
end

function FriendBoxHD:_OnBtnSendFriendOnRoom()
    Module.Social:RoomInvite(self._friendInfo.player_id, TeamInviteSource.FromFriend)
    self:_SetRoomBtnEnbaleState(false)
end

function FriendBoxHD:_OnPraiseClick()
    local function praiseClick()
        self._wtPraise:SetIsEnabledStyle(false)
        Server.FriendServer:UpdatePraiseData(self._friendInfo.player_id)
    end
    if self._panelType == 1 then
        Server.FriendServer:PlayerPraise(self._friendInfo.player_id, CreateCallBack(praiseClick, self), 2)
    else
        Server.FriendServer:PlayerPraise(self._friendInfo.player_id, CreateCallBack(praiseClick, self), 3)
    end
end

function FriendBoxHD:_OnHovered()
    --- Discord好友同样不支持点赞
    --- BEGIN MODIFICATION @ VIRTUOS: 显示平台好友
    if self._friendInfo.bIsPlatformOnlyFriend or self._panelType == 3 then
        return
    end
    --- END MODIFICATION
    self._wtPraise:Visible()
end

function FriendBoxHD:_OnUnHovered()
    self._wtPraise:Collapsed()
end

function FriendBoxHD:SetBHDOwnMark(info)
    --info.bhd_is_purchased = true
    if info.bhd_is_purchased then
        self._wtBHDMark:SelfHitTestInvisible()
    else
        self._wtBHDMark:Collapsed()
    end
end

function FriendBoxHD:ShowTips()
    local datas = {}
    if self._tipsInfo ~= ""  then
        table.insert(datas, {textContent = Module.LobbyBHD.Config.Loc.BHDOwned})
        self._tipHandle = Module.CommonTips:ShowCommonMessagesWithAnchor(datas,self._wtDFTipsAnchor)
    end

end

function FriendBoxHD:CloseTips()
    if self._tipHandle then
        Module.CommonTips:RemoveCommonMessageWithAnchor(self._tipHandle, self._wtTipAnchor)
        self._tipHandle = nil
    end
end

function FriendBoxHD:OnRoomInviting(inviteeId)
    if inviteeId and inviteeId == self._friendInfo.player_id then
        Module.CommonTips:ShowSimpleTip(Module.Social.Config.Loc.Inviting)
    end
end

--- BEGIN MODIFICATION @ VIRTUOS
function FriendBoxHD:_OnInputTypeChanged(InputType)
    if not IsHD() then
        return 
    end

    if not isvalid(self._wtTeamBtnContainer) then
        return
    end

    -- 手柄操作，TeamButton将显示在名片内，不会显示在FriendInfo中，故隐藏
    if InputType == EGPInputType.Gamepad then
        self._wtTeamBtnContainer:Collapsed()
    else
        self._wtTeamBtnContainer:SelfHitTestInvisible()
    end
end
--- END MODIFICATION

return FriendBoxHD