----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollection)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
local EGPInputType = import "EGPInputType"
local EGPUINavGroupTransSimpleStrategy = import "EGPUINavGroupTransSimpleStrategy"
-- END MODIFICATION

---@class CollectionsPagePanel : LuaUIBaseView
local CollectionsPagePanel = ui("CollectionsPagePanel")
local CollectionLogic = require "DFM.Business.Module.CollectionModule.Logic.CollectionLogic"
local CollectionConfig = Module.Collection.Config
local ItemDetailViewEquip = require "DFM.Business.Module.ItemDetailModule.UI.ItemDetailType3.ItemDetailViewEquip"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local EComp = CommonWidgetConfig.EIVWarehouseTempComponent
local EIVSlotPos = CommonWidgetConfig.EIVSlotPos
local EIVCompOrder = CommonWidgetConfig.EIVCompOrder
local EIVItemViewMode = Module.CommonWidget.Config.EIVItemViewMode
local ETipsTriggerReason = import("ETipsTriggerReason")


function CollectionsPagePanel:Ctor()
    self._wtShareBtn = self:Wnd("wtShareBtn", UIButton)
    self._wtShareBtn:Event("OnClicked", self._OnShareBtnClick, self)
    self._wtActionBtn = self:Wnd("wtUseBtn", DFCommonButtonOnly)
    self._wtActionBtn:Event("OnClicked", self._OnActionBtnClick, self)
    if not IsHD() then
        self._wtSortDropDown = UIUtil.WndDropDownBox(self, "wtSortDropDown", self._OnCheckedTabIndexChanged)    
    end
    self._wtPropGridBox = UIUtil.WndScrollGridBox(self, "wtPropGridBox", self._OnGetItemCount, self._OnProcessItemWidget)
    self._wtBoxArea = self:Wnd("wtBoxArea", UIWidgetBase)
    self._wtPrizeTitleText = self:Wnd("wtPrizeTitleText", UITextBlock)
    self._wtLine2Img = self:Wnd("wtLine2Img", UIImage)
    self._wtPrizeBox = self:Wnd("wtPrizeBox", UIWidgetBase)
    self._wtPrizeScrollBox = self:Wnd("wtPrizeScrollBox", UIWidgetBase)
    self._wtInfoPanel = self:Wnd("wtInfoPanel", UIWidgetBase)
    self._wtItemDetailView = self:Wnd("wtItemDetailView", ItemDetailViewEquip)
    self._wtItemDetailView:SetWeaponDetailIsHideBtn(true)
    self._wtItemDetailView:SetShowWeaponDetailCheckBox(false)
    self._wtProbabilityCheckBtn = self:Wnd("wtProbabilityCheckBtn", DFCheckBoxOnly)
    self._wtProbabilityCheckBtn:Event("OnCheckStateChanged", self._OnProbabilityCheckBoxStateChanged, self)
    self._wtProbabilityCheckBtn:SetIsChecked(false, false)
    self._wtEquipCheckBox = self:Wnd("wtEquipCheckBox", DFCheckBoxWithText)
    self._wtEquipCheckBox:Event("OnCheckStateChanged", self._OnEquipCheckBoxStateChanged, self)
    self._wtEquipCheckBox:SetIsChecked(false, false)
    self._wtAlertHintBox = self:Wnd("wtAlertHintBox", UIWidgetBase)
    self._wtAlertHintText = self:Wnd("wtAlertHintText", UITextBlock)
    self._wtQuestionHintBox = self:Wnd("wtQuestionHintBox", UIWidgetBase)
    self._wtQuestionHintText = self:Wnd("wtQuestionHintText", UITextBlock)
    self._wtTipCheckBtn = self:Wnd("wtTipCheckBtn", DFCheckBoxOnly)
    self._wtTipCheckBtn:Event("OnCheckStateChanged", self._OnShowTipCheckBoxStateChanged, self)
    self._wtExpirationHintBox = self:Wnd("wtExpirationHintBox", UIWidgetBase)
    self._wtExpirationHintText = self:Wnd("wtExpirationHintText", UITextBlock)
    self._wtExpireTipCheckBtn = self:Wnd("wtExpireTipCheckBtn", DFCheckBoxOnly)
    self._wtExpireTipCheckBtn:Event("OnCheckStateChanged", self._OnShowExpirationTipCheckBoxStateChanged, self)
    self._wtTipAnchor = UIUtil.WndTipsAnchor(self, "wtTipAnchor", self._OnShowInstruction, self._OnHideInstruction)
    self._wtExpirationTipAnchor = UIUtil.WndTipsAnchor(self, "wtExpirationTipAnchor", self._OnShowExpirationTip, self._OnHideExpirationTip)
    self._wtEmptyBg = self:Wnd("wtEmptyBg", UIWidgetBase)
    self._wtEmptySlot = self:Wnd("wtEmptySlot", UIWidgetBase)
    if self._wtEmptySlot then
        Facade.UIManager:RemoveSubUIByParent(self, self._wtEmptySlot)
        local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.CommonEmptyContent, self._wtEmptySlot, nil, nil)
        self._wtEmptyHint = getfromweak(weakUIIns)
    end
    self._wtEmptyHint:BP_SetText(CollectionConfig.Loc.NoItems)
    self._wtEmptyHint:SetCppValue("Set_Type", 1)
    self._wtEmptyHint:BP_Set_Type()
    self._collectionItems = {}
    self._selectedPos = -1
    self._selectedCell = nil
    self._mainTabIndex = -1
    self._subTabIndex = -1
    self._dropDownDataList = {}
    self._prizes = {}
    self._animType = 1
    self._propRedDotHandle = {}
    self._bEquipWhenUnboxing = false
    self._redDotInsMap = setmetatable({}, weakmeta_key)
end

-- UI构造时触发, 用于初始化外部传参UI数据逻辑，由UIManager触发（仅触发一次）
-- 子节点蓝图不会触发该方法
-- Module加载好子控件所需的图片后，将数据传入主UI界面
---@overload fun(LuaUIBaseView, OnInitExtraData)
function CollectionsPagePanel:OnInitExtraData()
end

---@overload fun(LuaUIBaseView, OnOpen)
function CollectionsPagePanel:OnOpen()
end


-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
function CollectionsPagePanel:EnableGamepadFeature()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if not self._wtNavGroupPropList then 
        self._ScrollGridBox = self:Wnd("wtPropGridBox", UIWidgetBase)
        self._wtNavGroupPropList = WidgetUtil.RegisterNavigationGroup(self._ScrollGridBox, self, "Hittest")
        if self._wtNavGroupPropList then
            self._wtNavGroupPropList:AddNavWidgetToArray(self._ScrollGridBox)
            self._wtNavGroupPropList:SetScrollRecipient(self._ScrollGridBox)
            self._wtNavGroupPropList:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
        end
    end 
    if not self._wtNavGroupPrizeBox then         
        self._wtNavGroupPrizeBox = WidgetUtil.RegisterNavigationGroup(self._wtPrizeBox, self, "Hittest")
        if self._wtNavGroupPrizeBox then
            self._wtNavGroupPrizeBox:AddNavWidgetToArray(self._wtPrizeBox)
            self._wtNavGroupPrizeBox:SetScrollRecipient(self._wtPrizeScrollBox)
            local navStrategy = self._wtNavGroupPrizeBox:GetOwnerNavStrategy()
            if navStrategy then
                navStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.LastFocusedOrHittestOrDistance)
            end
        end
    end 
    self._wtActionBtn:SetDisplayInputAction("Collection_Apply_Gamepad", true, nil, true)  
    self:_AddInputActionForActionBtn()
    self._bIsPrizeBoxFocused = false
    if not self._hNavigationChangedFocus then
        self._hNavigationChangedFocus = self._wtNavGroupPropList.OnNavGroupFocusReceivedEvent:Add(self._PropListFocus, self)
    end
    if not self._hPrizeBoxNavigationChangedFocus then
        self._hPrizeBoxNavigationChangedFocus = self._wtNavGroupPrizeBox.OnNavGroupFocusReceivedEvent:Add(self._PrizeBoxFocus, self)
    end
    if not hasdestroy(self._selectedCell) then
        WidgetUtil.SetUserFocusToWidget(self._selectedCell, true)
    end
end

function CollectionsPagePanel:_PropListFocus()
    self._bIsPrizeBoxFocused = false
    if self._NavConfigHandler then
        WidgetUtil.DisableDynamicNavConfig(self._NavConfigHandler)
        self._NavConfigHandler = nil
    end
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    self._NavConfigHandler = WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)
end

function CollectionsPagePanel:_PrizeBoxFocus()
    self._bIsPrizeBoxFocused = true
    if self._NavConfigHandler then
        WidgetUtil.DisableDynamicNavConfig(self._NavConfigHandler)
        self._NavConfigHandler = nil
    end
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    self._NavConfigHandler = WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.X_Accept, self)
    self._shortcutList = {}
    if self._wtEquipCheckBox:IsVisible() then
        table.insert(self._shortcutList, {actionName = "Collection_ImmediatelyEquipped_Gamepad",func = self._OnClickEquipCheckBox, caller = self ,bUIOnly = false, bHideIcon = false})
    end
    if #self._prizes > 0 then
        table.insert(self._shortcutList, {actionName = "GunSkin_ShowDetail",func = nil, caller = self ,bUIOnly = true, bHideIcon = false})
    end
    CollectionLogic.RegStackUIInputSummary(self._shortcutList, false)
end

function CollectionsPagePanel:_OnClickEquipCheckBox()
    self._wtEquipCheckBox:SelfClick()
end

function CollectionsPagePanel:DisableGamepadFeature()
    if not IsHD() then
        return
    end
    self:_RemoveInputActionForActionBtn()
    if self._hNavigationChangedFocus then
        self._wtNavGroupPropList.OnNavGroupFocusReceivedEvent:Remove(self._hNavigationChangedFocus)
    end
    if self._hPrizeBoxNavigationChangedFocus then
        self._wtNavGroupPrizeBox.OnNavGroupFocusReceivedEvent:Remove(self._hPrizeBoxNavigationChangedFocus)
    end
    if self._NavConfigHandler then
        WidgetUtil.DisableDynamicNavConfig(self._NavConfigHandler)
    end
    self._wtNavGroupPropList = nil 
    self._wtNavGroupPrizeBox = nil
    self._NavConfigHandler = nil
    self._hNavigationChangedFocus = nil
    self._hPrizeBoxNavigationChangedFocus = nil
    self._NavConfigHandler = nil
    WidgetUtil.RemoveNavigationGroup(self)
end

function CollectionsPagePanel:_SetDefaultGamepadFocus()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if self._wtNavGroupPropList then
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroupPropList)
    end
end

function CollectionsPagePanel:_AddInputActionForActionBtn()
    if not self._wtActionBtnHandle then
        self._wtActionBtnHandle = self:AddInputActionBinding("Collection_Apply_Gamepad", EInputEvent.IE_Pressed, self._OnActionBtnClick,self, EDisplayInputActionPriority.UI_Stack)
    end   
end

function CollectionsPagePanel:_RemoveInputActionForActionBtn()
    if self._wtActionBtnHandle then
        self:RemoveInputActionBinding(self._wtActionBtnHandle)
        self._wtActionBtnHandle = nil 
    end
end

-- END MODIFICATION

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function CollectionsPagePanel:OnClose()
    self._selectedCell = nil
    Facade.UIManager:ClearSubUIByParent(self, self._wtEmptySlot)
    Facade.UIManager:ClearSubUIByParent(self, self._wtItemDetailView)
    Facade.UIManager:ClearSubUIByParent(self, self._wtPrizeBox)
    table.empty(self._redDotInsMap)
    Module.ReddotTrie:UnRegisterDynamicReddot(EReddotTrieObserverType.Collection, ECollectionDynamicDataType.CommonProp)
end

function CollectionsPagePanel:OnShowBegin()
    self:EnableGamepadFeature()
end

function CollectionsPagePanel:OnHideBegin()
    self:DisableGamepadFeature()
    self:ClosePopup()
end

---@overload fun(LuaUIBaseView, OnShow)
function CollectionsPagePanel:OnShow()
    if self._showDefaultBackgroundCallback then 
        self._showDefaultBackgroundCallback()
    end
end

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function CollectionsPagePanel:OnHide()
    self:_StopExpireInfoTimer()
    self:_OnHideInstruction()
    self:_OnHideExpirationTip()
    self._bIsLoadingPrize = false
end

function CollectionsPagePanel:OnAnimFinished(anim)
    if anim == self.WBP_CollectionsPage_in then
        if not hasdestroy(self._selectedCell) then
            WidgetUtil.SetUserFocusToWidget(self._selectedCell, true)
        end
    end
end

function CollectionsPagePanel:ToggleControlAndListeners(bEnable, bFullReset)
    if bEnable == true then
        self:AddListeners()
        self._scrollStopHandle = UIUtil.AddScrollBoxClickStopScroll(self._wtPropGridBox, self)
        CollectionLogic.RegStackUIInputSummary(self._shortcutList, false)
    else
        if bFullReset == true then
            self._selectedCell = nil
            self._selectedPos = -1
        end
        self:RemoveAllLuaEvent()
        if self._scrollStopHandle then
            UIUtil.RemoveScrollBoxClickStopScroll(self._scrollStopHandle)
            self._scrollStopHandle = nil
        end
    end
end

function CollectionsPagePanel:AddListeners()
    self:AddLuaEvent(Server.CollectionServer.Events.evtFetchPropExpireInfo, self._OnFetchPropExpireInfo, self)
    self:AddLuaEvent(LuaGlobalEvents.evtSceneLoaded, self.OnRefreshModel, self)
end


function CollectionsPagePanel:RefreshView(mainTabIndex, bResetList, bResetTab, bUseTertiaryTab)
    self._mainTabIndex = mainTabIndex or self._mainTabIndex 
    if not bUseTertiaryTab and bResetTab then
        local tabInfo = Module.Collection.Field:GetTabInfo()
        local mainTabInfo = tabInfo[self._mainTabIndex]
        if mainTabInfo then
            self._dropDownDataList = {}
            for key, info in pairs(mainTabInfo.subTabList) do
                table.insert(self._dropDownDataList, info.subTabName)
            end
        end
    end
    if bResetList then
        Module.ReddotTrie:UnRegisterDynamicReddot(EReddotTrieObserverType.Collection, ECollectionDynamicDataType.CommonProp)
    end 
    if bResetTab then
        self._subTabIndex = 1
        if not bUseTertiaryTab then
            UIUtil.InitDropDownBox(self._wtSortDropDown, self._dropDownDataList, {}, 0)
        end
    else
        self._subTabIndex = self._subTabIndex > 0 and self._subTabIndex or 1  
    end
    self._bIsLoadingPrize = false
    self:_OnRefreshPropItems(bResetList)
end

function CollectionsPagePanel:_OnGetItemCount()
    return #self._collectionItems
end


function CollectionsPagePanel:_OnProcessItemWidget(position, itemWidget)
    local item = self._collectionItems[position + 1]
    if item then
        itemWidget:ChangeDefaultMode(EIVItemViewMode.ListItemView)
        local nameComp = itemWidget:FindOrAdd(EComp.TopLeftIconText, UIName2ID.IVTextQualityComponent, EIVSlotPos.TopLeft)
        local maskComp =  itemWidget:FindOrAdd(EComp.GreyMask, UIName2ID.IVGreyMask, EIVSlotPos.MaskLayer)
        itemWidget:EnableComponent(EComp.GreyMask, CollectionLogic.IsPropAvailableInCurrentMode(item) == false)
        local fCheckFunc = CreateCallBack(function(self, curUpdateReddotObType)
            return Server.CollectionServer:IsPropWithRedDot(item.id, item.gid)
        end,self)
        if isvalid(self._redDotInsMap[itemWidget]) and self._redDotInsMap[itemWidget]:GetIsValid() then
            Module.ReddotTrie:UpdateDynamicReddot(itemWidget, EReddotTrieObserverType.Collection, ECollectionDynamicDataType.CommonProp, self._redDotInsMap[itemWidget], fCheckFunc, nil)
        else
            self._redDotInsMap[itemWidget] = Module.ReddotTrie:RegisterDynamicReddot(EReddotTrieObserverType.Collection, ECollectionDynamicDataType.CommonProp, fCheckFunc, nil ,itemWidget, {reddotType=EReddotType.Normal, zOrder=10})

        end
        local fClickCb = CreateCallBack(self._OnCollectionItemClick, self,itemWidget, position)
        itemWidget:BindCustomOnClicked(fClickCb)
        if not IsHD() then
            itemWidget:SetCppValue("bPreciseClick" , true)
        end
        itemWidget:InitItem(item)
        local expireInfo = CollectionLogic.GetProcessedPropExpireInfo(item.id)
        if expireInfo and #expireInfo > 0 then
            local expirationHintComp = itemWidget:FindOrAdd(EComp.BottomLeftIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomLeft)
            expirationHintComp:ShowIconAndText(
			"PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0011.Common_ItemProp_Icon_0011'", 
			expireInfo[1].timeLeftText
		    )
            expirationHintComp:SetCornerStyle()
            expirationHintComp._wtMainIcon:SetColorAndOpacity(Facade.ColorManager:GetLinerColor("Highlight01"))
            expirationHintComp:SetTextColorAndOpacity(Facade.ColorManager:GetSlateColor("Highlight01"))
        end
        itemWidget:EnableComponent(EComp.BottomLeftIconText, expireInfo ~= nil and expireInfo[1] ~= nil)
    end
    itemWidget:SetAnchorIfOverFlow()
    itemWidget:SetSelected(item, self._selectedPos == position)
    if self._selectedPos == position then
        self._selectedCell = itemWidget
        WidgetUtil.SetUserFocusToWidget(self._selectedCell, true)
    end
end



function CollectionsPagePanel:_OnRefreshPropItems(bResetList)
    if not IsHD() then
        self._wtSortDropDown:BP_SwitchCheckButtonState(ECheckButtonState.Unchecked)
        self._wtSortDropDown:BP_SetMainTabText(self._dropDownDataList[self._subTabIndex] or "")
        self._wtSortDropDown:Visible()
    end
    local selectedItem = self._collectionItems[self._selectedPos+1]
    self._collectionItems = CollectionLogic.GetCollectionItems(self._mainTabIndex, self._subTabIndex)
    table.sort(self._collectionItems, CollectionLogic.DefaultSort)
    if bResetList then
        self._selectedCell = nil
        self._selectedPos = -1
        if #self._collectionItems > 0 then
            self._selectedPos = 0
        end
        self._wtPropGridBox:ScrollToStart(false, false, 0)
        self._wtPropGridBox:RefreshAllItems()
    else
        if #self._collectionItems == 0 then
            self._wtPropGridBox:ScrollToStart(false, false, 0)
            self._wtPropGridBox:RefreshAllItems()
        else
            if selectedItem then
                for index, item in ipairs(self._collectionItems) do
                    if selectedItem.id == item.id then
                        self._selectedPos = index - 1
                        self._wtPropGridBox:ScrollToItem(index - 1, false, false, 10, 0, false)
                        break
                    end
                end
            end
            self._wtPropGridBox:RefreshVisibleItems()
        end
    end
    local item = self._collectionItems[self._selectedPos+1]
    if isvalid(item) and Server.CollectionServer:IsPropWithRedDot(item.id) then
        Timer.DelayCall(1, function ()
            CollectionLogic.RemoveRedDots({item})
        end)
    end
    if #self._collectionItems == 0 then
        self._wtEmptyBg:SelfHitTestInvisible()
        self._wtEmptySlot:SelfHitTestInvisible()
    else
        self._wtEmptyBg:Collapsed()
        self._wtEmptySlot:Collapsed()
    end
    self:_RefreshItemUI(bResetList)
end



function CollectionsPagePanel:OnRefreshModel(curSubStageType)
    if not curSubStageType or curSubStageType == ESubStage.HallCollectionNew then
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallCollectionNew, "ResetDisplayItem")
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallCollectionNew, "ResetWeapon")
    end
end


function CollectionsPagePanel:_OnCollectionItemClick(itemCell, position)
    Module.ItemDetail:CloseAllPopUI()
    if self._selectedPos ~= position then
        local item = self._collectionItems[position+1]
        if self._selectedCell and self._selectedPos > -1 then
            self._selectedCell:SetSelected(self._collectionItems[self._selectedPos+1], false)
            if IsHD() and WidgetUtil.IsGamepad() then 
                self._selectedCell:SetCppValue("bHandleClick" , true)
            end
        end
        if isvalid(item) and Server.CollectionServer:IsPropWithRedDot(item.id) then
            CollectionLogic.RemoveRedDots({item})
        end
        self._selectedCell = itemCell
        self._selectedCell:SetSelected(item, true)
        if IsHD() and WidgetUtil.IsGamepad() then 
            self._selectedCell:SetCppValue("bHandleClick" , false)
        end
        self._selectedPos = position
        Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIItemSelect)
        self._bIsLoadingPrize = false
        self:_RefreshItemUI(true)
        self:OnRefreshModel(ESubStage.HallCollectionNew)
    end
end


function CollectionsPagePanel:_RefreshItemUI(bReset)
    local item = self._collectionItems[self._selectedPos+1]
    if bReset or #self._collectionItems == 0 then
        self:_OnHideInstruction()
        self:_OnHideExpirationTip()
        Facade.UIManager:RemoveSubUIByParent(self, self._wtPrizeBox)
        self._wtInfoPanel:Collapsed()
        self._wtShareBtn:Collapsed()
        self._wtLine2Img:Collapsed()
        self._wtTipCheckBtn:SetIsChecked(false, false)
        self._wtActionBtn:Collapsed()
        self._bEnableActionBtn = false
        self._wtPrizeTitleText:Collapsed()
        self._wtProbabilityCheckBtn:Collapsed()
        self._wtPrizeBox:Collapsed()
        self._wtEquipCheckBox:SetIsChecked(false, true)
        self._wtQuestionHintBox:Collapsed()
        self._wtExpirationHintBox:Collapsed()
        self._bEquipWhenUnboxing = false
        self._wtAlertHintBox:Collapsed()
        self._wtEquipCheckBox:Collapsed()
        self._prizes = {}
        self._equipItemGroups = {}
        self._loadPrizeId = nil
        if isvalid(item) then
            local bDisableButton = false
            local bUseDefaultButtonDescription = true
            if item.itemSubType == ECollectableType.RightsCard then
                bDisableButton = Server.CollectionServer:CheckIsRightsEffectingByCardId(item.id)
                if bDisableButton == true then
                    bUseDefaultButtonDescription = false
                    self._wtActionBtn:SetMainTitle(CollectionConfig.Loc.Effecting)
                end
            --自选包、全装包
            elseif item.itemSubType == ECollectableType.SelectivePackage 
                or item.itemSubType == ECollectableType.FullArmedPackage 
                or item.itemSubType == ECollectableType.RandomPackage
                or item.itemSubType == ECollectableType.SuitPackage 
                or item.itemSubType == ECollectableType.RandomLottery then
                self._loadPrizeId = item.id
                local prizeData = Module.Collection.Field:GetBoxPrizeData(item.id)
                if prizeData then
                    self:_loadPrizes(prizeData, item.id)
                elseif not self._bIsLoadingPrize then
                    self._bIsLoadingPrize = true
                    local loadPrizeCallback = CreateCallBack(self._loadPrizes, self)
                    Server.StoreServer:GetStoreBoxItemWithCallback(item.id, loadPrizeCallback) 
                end
                if item.itemSubType == ECollectableType.RandomPackage then
                    self._wtPrizeTitleText:SetText(CollectionConfig.Loc.RandomPrize)
                else
                    self._wtPrizeTitleText:SetText(item.itemSubType == ECollectableType.SelectivePackage and CollectionConfig.Loc.ChoiceOfPrize or CollectionConfig.Loc.IncludePrize)
                end
            end
            if CollectionLogic.IsPropAvailableInCurrentMode(item) then
                if item.itemSubType == ECollectableType.FullArmedPackage then
                    self._wtEquipCheckBox:SelfHitTestInvisible()
                end
                if item.itemSubType == ECollectableType.MissionFileExpCard
                    or item.itemSubType == ECollectableType.MissionFileActivateCard 
                    or item.itemSubType == ECollectableType.BattlePointProtectiveCard then
                    self._wtAlertHintBox:SelfHitTestInvisible()
                    self._wtAlertHintText:SetText(CollectionConfig.Loc.ClearWhenSeasonEnd)
                elseif item.itemSubType == ECollectableType.ActCardSafeBox
                    or item.itemSubType == ECollectableType.ActCardPack then
                    local activatingTable = Facade.TableManager:GetTable("SafeAndCardPackActivatingConfig")
                    if activatingTable then
                        for key, activatingData in pairs(activatingTable) do
                            if item.id == tonumber(activatingData.TheActivating) and activatingData.SeasonID ~= 0 and activatingData.SeasonID ~= Server.BattlePassServer._iServerInfoSeasonID then
                                self._wtQuestionHintBox:SelfHitTestInvisible()
                                self._wtQuestionHintText:SetText(CollectionConfig.Loc.OnlyAvailableInCurrentSeason)
                                break
                            end
                        end
                    end
                end
                self._wtActionBtn:SetIsEnabled(not bDisableButton)
            else
                self._wtAlertHintBox:SelfHitTestInvisible()
                self._wtAlertHintText:SetText(CollectionConfig.Loc.PropNotAvailableInCurrentMode)
                self._wtActionBtn:SetIsEnabled(false)
            end
            local itemConfigRow = ItemConfigTool.GetItemConfigById(item.id)
            if itemConfigRow then
                if itemConfigRow.ButtonDes ~= nil and itemConfigRow.ButtonDes ~= "" then
                    if bUseDefaultButtonDescription then
                        self._wtActionBtn:SetMainTitle(itemConfigRow.ButtonDes)
                    end
                    self._bEnableActionBtn = not bDisableButton and IsHD() and WidgetUtil.IsGamepad()
                    self._wtActionBtn:SelfHitTestInvisible()
                end
            end
        end
    end
    if isvalid(item) then
        self._wtItemDetailView:UpdateItem(item)
        self._wtInfoPanel:SelfHitTestInvisible()
        if item.itemSubType == ECollectableType.BattleDoubleLevelExpCard 
            or item.itemSubType == ECollectableType.BattleDoubleWeaponExpCard 
            or item.itemSubType == ECollectableType.BattleDoubleTournamentExpCard 
            or item.itemSubType == ECollectableType.BattlePointProtectiveCard then
            local functionId = 11
            if item.itemSubType == ECollectableType.BattleDoubleTournamentExpCard then
                functionId = 13
            elseif item.itemSubType == ECollectableType.BattlePointProtectiveCard then
                functionId = 14
            end
            local battleConsumeChange = Server.CollectionServer:GetBattleConsumeChangeByFunctionId(functionId)
            self._wtQuestionHintText:SetText(string.format(CollectionConfig.Loc.ActivationMatchRemain, tostring(battleConsumeChange and battleConsumeChange.newCurCount or 0)))
            self._wtQuestionHintBox:SelfHitTestInvisible()
        end
        --如果该道具有过期时间
        local expireInfo = CollectionLogic.GetProcessedPropExpireInfo(item.id)
        if expireInfo and #expireInfo > 0 then
            self._wtExpirationHintText:SetText(expireInfo[1].timeLeftText_2)
            self._wtExpirationHintBox:SelfHitTestInvisible()
        end
        self:_StartExpireInfoTimer()
    end
    if self._bEnableActionBtn == true then
        self:_AddInputActionForActionBtn()
    else
        self:_RemoveInputActionForActionBtn()
    end
    self:UpdateBackground()
    self:OnRefreshModel(ESubStage.HallCollectionNew)
    self._shortcutList = {}
    if IsHD() and WidgetUtil.IsGamepad() then
        if self._wtEquipCheckBox:IsVisible() then
            table.insert(self._shortcutList, {actionName = "Collection_ImmediatelyEquipped_Gamepad",func = self._OnClickEquipCheckBox, caller = self ,bUIOnly = false, bHideIcon = false})
        end
    end
    CollectionLogic.RegStackUIInputSummary(self._shortcutList, false)
end

function CollectionsPagePanel:OnTertiaryTabIndexChanged(TabIndex, LastTabIndex)
    self._subTabIndex = TabIndex
    self:_OnRefreshPropItems(true)
end

function CollectionsPagePanel:_OnCheckedTabIndexChanged(position, lastPosition)
    if self._subTabIndex ~= position + 1 then
        self._subTabIndex = position + 1
        self:_OnRefreshPropItems(true)
    end
end

function CollectionsPagePanel:_OnEquipCheckBoxStateChanged(bChecked)
    self._bEquipWhenUnboxing = bChecked
end

function CollectionsPagePanel:_OnProbabilityCheckBoxStateChanged(bChecked)
    if self._prizeGroups then
        Facade.UIManager:AsyncShowUI(UIName2ID.CollectionProbabilityPop, nil, self, self._prizeGroups)
    end
    self._wtProbabilityCheckBtn:SetIsChecked(false, false)
end

function CollectionsPagePanel:_OnActionBtnClick()
    local item = self._collectionItems[self._selectedPos + 1]
    if isvalid(item) and CollectionLogic.IsPropAvailableInCurrentMode(item) then
        local itemConfigRow = ItemConfigTool.GetItemConfigById(item.id)
        if itemConfigRow and itemConfigRow.JumpID ~= nil and itemConfigRow.JumpID ~= 0 then
            local bHasTipInConfig = itemConfigRow.UnlockTip ~= nil and itemConfigRow.UnlockTip ~= ""
            local bCanJump = Module.Jump:CheckCanJumpByID(itemConfigRow.JumpID)
            if bCanJump then
                Module.Jump:JumpByID(itemConfigRow.JumpID)
            elseif bHasTipInConfig then
                Module.CommonTips:ShowSimpleTip(itemConfigRow.UnlockTip)
            else
                Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.UnlockTip)
            end
        else
            if self._loadPrizeId == item.id and Module.Collection.Field:GetBoxPrizeData(item.id) or not self._loadPrizeId then
                if item.itemSubType == ECollectableType.FullArmedPackage then
                    local bIsReady2Go, readyState = Server.MatchServer:GetIsReadytoGo()
                    if bIsReady2Go then
                        if readyState == 1 then
                            Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.InMatching)
                        else
                            Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.InReadying)
                        end
                        return
                    end
                    if self._bEquipWhenUnboxing == true then
                        if Server.ArmedForceServer:CheckIsRentalStatus() then
                            Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.EquipForbidTip)
                        else
                            local fCallbackIns = CreateCallBack(function(self, data, itemId)
                                self:_loadPrizes(data, itemId)
                                CollectionLogic.UseCollectionProp(item, self._prizes, self._bEquipWhenUnboxing, self._equipItemGroups) 
                            end,self)
                            self._loadPrizeId = item.id
                            if itemConfigRow then
                                Server.StoreServer:ClearBoxInfoCache(itemConfigRow.ConnectedPool or 0)
                            end
                            Server.StoreServer:GetStoreBoxItemWithCallback(item.id, fCallbackIns) 
                        end
                        return
                    end
                end
                CollectionLogic.UseCollectionProp(item, self._prizes, self._bEquipWhenUnboxing, self._equipItemGroups)
            else
                Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.LoadingTip)
                if not self._bIsLoadingPrize then
                    local loadPrizeCallback = CreateCallBack(self._loadPrizes, self)
                    Server.StoreServer:GetStoreBoxItemWithCallback(item.id, loadPrizeCallback) 
                end
            end
        end
    end
end


function CollectionsPagePanel:_OnShareBtnClick()
    Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.ShareTip)
end


function CollectionsPagePanel:_loadPrizes(data, itemId)
    if self._loadPrizeId ~= itemId then
        return
    end
    if data == nil or hasdestroy(self._wtPrizeBox) then
        self._bIsLoadingPrize = false
        return
    end
    self._prizes = {}
    self._prizeGroups = {}
    self._equipItemGroups = {}
    Module.Collection.Field:SetBoxPrizeData(itemId, data)
    local lastItem
    local sumPercentProp = 0
    Facade.UIManager:RemoveSubUIByParent(self, self._wtPrizeBox)
    self._wtPrizeTitleText:SelfHitTestInvisible()
    self._wtPrizeBox:SelfHitTestInvisible()
    local dropItems = {}
    local dropItemsIds = {}
    if data ~= nil then
        if data.collide_props ~= nil then
            for index, propInfo in ipairs(data.collide_props) do
                local item = ItemBase:NewIns(propInfo.id, propInfo.num)
                item:SetRawPropInfo(propInfo)
                table.insert(dropItems, item)
                table.insert(dropItemsIds, item.id)
            end
        end
        if data ~= nil and data.group_list ~= nil then
            local weakUIIns, instanceId
            for index, giftGroupInfo in ipairs(data.group_list) do
                table.insert(self._prizeGroups, 
                    {
                        core_flag = giftGroupInfo.core_flag,
                        title = Module.Store.Config.Loc.PrizeCurrentProb,
                        items = {}
                    }
                )
                for index2, propInfo in ipairs(giftGroupInfo.prop_list) do
                    local item = ItemBase:NewIns(propInfo.prop_id, propInfo.num)
                    item:SetRawPropInfo(propInfo.prop_info)
                    item.percentDisplayProb = math.floor(propInfo.real_prob *10000 + 0.5)/100
                    sumPercentProp = sumPercentProp + item.percentDisplayProb
                    item.group_id = giftGroupInfo.group_id
                    item.owned = not propInfo.restore_flag and propInfo.hit_sum > 0
                    item.restore_flag = propInfo.restore_flag
                    item.prob_showed =  propInfo.prob_showed ~= nil and propInfo.prob_showed * 100 or nil
                    item.acquisition_guaranteed = propInfo.acquisition_guaranteed
                    item.bound_flag = propInfo.bound_flag
                    item.is_chosen = propInfo.is_chosen
                    item.num_id = propInfo.num_id
                    if item.id == data.show_id1  then
                        item.show_id1 = true
                    end
                    if item.id == data.show_id2 then
                        item.show_id2 = true
                    end
                    table.insert(self._prizes, item)
                    table.insert(self._prizeGroups[#self._prizeGroups].items, item)
                    weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.IVCommonItemTemplate, self._wtPrizeBox)
                    local itemUI = getfromweak(weakUIIns)
                    local bindingComp = itemUI:FindOrAdd(EComp.BottomLeftIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomLeft, EIVCompOrder.MaskLayerOrder)
                    bindingComp:SetStyle(CommonWidgetConfig.EIVIconTextStyle.Binding)
                    itemUI:SetRootSize(160, 160)
                    itemUI:InitItem(item)
                    if index2 == #giftGroupInfo.prop_list and giftGroupInfo.core_flag == true then
                        lastItem = item
                    end
                end
            end
        end
        if self._collectionItems[self._selectedPos+1] and self._collectionItems[self._selectedPos+1].itemSubType == ECollectableType.RandomPackage then
            self._wtProbabilityCheckBtn:SelfHitTestInvisible()
        end
    end
    if lastItem then
        lastItem.percentDisplayProb = 100-(sumPercentProp - lastItem.percentDisplayProb)  --向上取整导致概率超1合规修正
        lastItem.percentDisplayProb = lastItem.percentDisplayProb < 0 and 0 or lastItem.percentDisplayProb
    end
    table.sort(self._prizeGroups, function(a, b)
        return a.core_flag > b.core_flag
    end)
    if #self._prizes > 0 then
        table.insert(self._equipItemGroups, {title=CollectionConfig.Loc.ReadyToEquip ,items=self._prizes})
    end
    if #dropItems > 0 then
        table.insert(self._equipItemGroups, {title=CollectionConfig.Loc.DropWhenConflict ,items=dropItems})
    end
    self._wtLine2Img:SelfHitTestInvisible()
    self._bIsLoadingPrize = false
    if IsHD() and WidgetUtil.IsGamepad() and self._bIsPrizeBoxFocused then
        self._shortcutList = {}
        if self._wtEquipCheckBox:IsVisible() then
            table.insert(self._shortcutList, {actionName = "Collection_ImmediatelyEquipped_Gamepad",func = self._OnClickEquipCheckBox, caller = self ,bUIOnly = false, bHideIcon = false})
        end
        if #self._prizes > 0 then
            table.insert(self._shortcutList, {actionName = "GunSkin_ShowDetail",func = nil, caller = self ,bUIOnly = true, bHideIcon = false})
        end
        CollectionLogic.RegStackUIInputSummary(self._shortcutList, false)
    end
end

function CollectionsPagePanel:_OnShowTipCheckBoxStateChanged(bChecked)
    if not IsHD() then
        if bChecked then
            self:_OnShowInstruction()
        else
            self:_OnHideInstruction()
        end
    end
end

function CollectionsPagePanel:_OnShowInstruction()
    local item = self._collectionItems[self._selectedPos+1]
    if isvalid(item) then
        local datas = {}
        local tipText = ""
        if item.itemSubType == ECollectableType.BattleDoubleLevelExpCard
            or item.itemSubType == ECollectableType.BattleDoubleWeaponExpCard then
            tipText = CollectionConfig.Loc.DoubleExpCardTip
        elseif item.itemSubType == ECollectableType.BattleDoubleTournamentExpCard then
            tipText = CollectionConfig.Loc.DoubleTournamentExpCardTip
        elseif item.itemSubType == ECollectableType.BattlePointProtectiveCard then
            tipText = CollectionConfig.Loc.PointProtectiveCardTip
        end
        table.insert(datas, {
            textContent = tipText,
            styleRowId = "C000"
        })
        self._tipHandle = Module.CommonTips:ShowCommonMessagesWithAnchor(datas, self._wtTipAnchor)
    end
end

function CollectionsPagePanel:_OnHideInstruction(reason)
    if self._tipHandle then
        Module.CommonTips:RemoveCommonMessageWithAnchor(self._tipHandle, self._wtTipAnchor)
        self._tipHandle = nil
        if reason ~= ETipsTriggerReason.Click then
            self._wtTipCheckBtn:SetSelectState(false, false)
        end
    end
end

function CollectionsPagePanel:_OnShowExpirationTipCheckBoxStateChanged(bChecked)
    if not IsHD() then
        if bChecked then
            self:_OnShowExpirationTip()
        else
            self:_OnHideExpirationTip()
        end
    end
end

function CollectionsPagePanel:_OnShowExpirationTip()
    self:_OnHideExpirationTip()
    local item = self._collectionItems[self._selectedPos+1]
    if isvalid(item) then
        local datas = {}
        local tipText = ""
        local instruction = {
            {id = UIName2ID.Assembled_CommonMessageTips_V6, data = {name = StringUtil.Key2StrFormat(Module.Collection.Config.Loc.ExpirationOwnedNum,{["Amount"] = item.num or 1})}}
        }
        local expireInfo = CollectionLogic.GetProcessedPropExpireInfo(item.id)
        if expireInfo then
            for index, info in ipairs(expireInfo) do
                if index == 1 then
                    table.insert(instruction,
                        {id = UIName2ID.Assembled_CommonMessageTips_V2, data = {textContent = string.format("<customstyle color=\"Color_Highlight01\">%s</>", info.timeLeftText_1)}}
                    )
                else
                    table.insert(instruction,
                        {id = UIName2ID.Assembled_CommonMessageTips_V2, data = {textContent = info.timeLeftText_1}}
                    )
                end
                if index >= 10 then
                    break
                end
            end
        end
        self._expirationTipHandle = Module.CommonTips:ShowAssembledTips(instruction, self._wtExpirationTipAnchor)
    end
end

function CollectionsPagePanel:_OnHideExpirationTip(reason)
    if self._expirationTipHandle then
        Module.CommonTips:RemoveCommonMessageWithAnchor(self._expirationTipHandle, self._wtExpirationTipAnchor)
        self._expirationTipHandle = nil
        if reason ~= ETipsTriggerReason.Click then
            self._wtExpireTipCheckBtn:SetSelectState(false, false)
        end
    end
end


function CollectionsPagePanel:_StartExpireInfoTimer()
    self:_StopExpireInfoTimer()
    local rawExpireInfo = Server.CollectionServer:GetPropExpireInfoList()
    if rawExpireInfo and table.nums(rawExpireInfo) > 0 then
        self._expireInfoTimer = Timer:NewIns(60, 0)
        self._expireInfoTimer:AddListener(self._RefreshExpireInfo, self)
        self._expireInfoTimer:Start()
    end
end

function CollectionsPagePanel:_StopExpireInfoTimer()
    if isvalid(self._expireInfoTimer) then
        self._expireInfoTimer:Release()
    end
    self._expireInfoTimer = nil
end

function CollectionsPagePanel:_RefreshExpireInfo()
    local item = self._collectionItems[self._selectedPos+1]
    if isvalid(item) then
        local rawExpireInfo = Server.CollectionServer:GetPropExpireInfo(item.id)
        if rawExpireInfo and #rawExpireInfo > 0 then
            local currentTimestamp = Facade.ClockManager:GetServerTimestamp()
            local bPropExpired = true
            for index, info in ipairs(rawExpireInfo) do
                if info.expireTime > currentTimestamp then
                    bPropExpired = false
                    break
                end
            end
            if bPropExpired == true then
                Server.CollectionServer:RemoveExpiredPropLocally(item, false)
                table.remove(self._collectionItems, self._selectedPos+1)
                self:_RefreshItemUI(true)
            else
                local expireInfo = CollectionLogic.GetProcessedPropExpireInfo(item.id)
                self._wtExpirationHintText:SetText(expireInfo and expireInfo[1].timeLeftText_2 or "")
                self._wtExpirationHintBox:SelfHitTestInvisible() 
                if self._expirationTipHandle then
                    self:_OnShowExpirationTip()
                end
            end
            self._wtPropGridBox:RefreshVisibleItems()
        end
    end
end


function CollectionsPagePanel:_OnFetchPropExpireInfo()
    self:_OnRefreshPropItems(true)
end

function CollectionsPagePanel:ClosePopup()
    self:_OnHideInstruction()
    self:_OnHideExpirationTip()
end

function CollectionsPagePanel:OnInputTypeChanged(inputType)
    if IsHD() and self._selectedCell then 
        self._selectedCell:SetCppValue("bHandleClick" , inputType ~= EGPInputType.Gamepad)
    end
end

function CollectionsPagePanel:BindSetBackgourndCallback(callback, caller)
    self._setBackgourndCallback = SafeCallBack(callback, caller)
end

function CollectionsPagePanel:BindShowDefaultBackgroundCallBack(callback, caller)
    self._showDefaultBackgroundCallback = SafeCallBack(callback, caller)
end

function CollectionsPagePanel:UpdateBackground()
    if self._setBackgourndCallback then
        local item = self._collectionItems[self._selectedPos+1]
        if item ~= nil then
            self._setBackgourndCallback(item.id, true)
        else
            self._setBackgourndCallback(nil, true)
        end
    end
end

return CollectionsPagePanel
