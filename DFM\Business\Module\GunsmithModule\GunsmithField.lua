----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGunsmith)
----- LOG FUNCTION AUTO GENERATE END -----------



local GunsmithEnvironmentData = require "DFM.Business.Module.GunsmithModule.Data.GunsmithEnvironmentData"
local GunsmithUIContext = require "DFM.Business.Module.GunsmithModule.Data.GunsmithUIContext"
local GunsmithServerPrecessor = require "DFM.Business.Module.GunsmithModule.Data.GunsmithServerPrecessor"
local EGunsmithUIState = require "DFM.Business.Module.GunsmithModule.Define.EGunsmithUIState"
local GunsmithUIContextProcessor4Default = require "DFM.Business.Module.GunsmithModule.Logic.UIContext.GunsmithUIContextProcessor4Default"
local GunsmithUIContextProcessor4Simulate = require "DFM.Business.Module.GunsmithModule.Logic.UIContext.GunsmithUIContextProcessor4Simulate"
local GunsmithUIContextProcessor4Range = require "DFM.Business.Module.GunsmithModule.Logic.UIContext.GunsmithUIContextProcessor4Range"
local GunsmithSOLInspectorItemBaseAlloctorData = require "DFM.Business.Module.GunsmithModule.Data.SOLInspector.GunsmithSOLInspectorItemBaseAlloctorData"
local GunsmithShortcutProcessor = require "DFM.Business.Module.GunsmithModule.Logic.Shortcut.GunsmithShortcutProcessor"

---@class GunsmithField : FieldBase
local GunsmithField = class("GunsmithField", require"DFM.YxFramework.Managers.Module.FieldBase")

function GunsmithField:Ctor()
    self._enviromentData = GunsmithEnvironmentData:NewIns()             ---@type GunsmithEnvironmentData
    self._uiContext = GunsmithUIContext:NewIns()                        ---@type GunsmithUIContext
    self._serverProcessor = GunsmithServerPrecessor:NewIns()            ---@type GunsmithServerPrecessor

    self._uiContextProcessors = {}
    self._uiContextProcessors[EGunsmithUIState.Default] = GunsmithUIContextProcessor4Default:NewIns()
    self._uiContextProcessors[EGunsmithUIState.Simulate] = GunsmithUIContextProcessor4Simulate:NewIns()
    self._uiContextProcessors[EGunsmithUIState.Range] = GunsmithUIContextProcessor4Range:NewIns()

    self._itemBaseAlloctor = GunsmithSOLInspectorItemBaseAlloctorData:NewIns()

    self._shortcutProcessor = GunsmithShortcutProcessor:NewIns()                ---@type GunsmithShortcutProcessor
end

function GunsmithField:OnDeclareField()
end

function GunsmithField:OnClearField()
end

function GunsmithField:OnDestroyField()
    releaseobject(self._enviromentData)
    self._enviromentData = nil

    releaseobject(self._uiContext)
    self._uiContext = nil

    releaseobject(self._serverProcessor)
    self._serverProcessor = nil

    for index, uiContextProcessor in pairs(self._uiContextProcessors) do
        releaseobject(uiContextProcessor)
        self._uiContextProcessors[index] = nil
    end
    self._uiContextProcessors = nil

    releaseobject(self._itemBaseAlloctor)
    self._itemBaseAlloctor = nil

    releaseobject(self._shortcutProcessor)
    self._shortcutProcessor = nil
end

function GunsmithField:GetEnvironmentData()
    return self._enviromentData
end

function GunsmithField:GetUIContext()
    return self._uiContext
end

function GunsmithField:GetServerProcessor()
    return self._serverProcessor
end

function GunsmithField:GetUIContextProcessor(state)
    return self._uiContextProcessors[state]
end

function GunsmithField:GetItemBaseAlloctor()
    return self._itemBaseAlloctor
end

function GunsmithField:OnItemBaseAlloctorReset()
    self._itemBaseAlloctor:Reset()
end

function GunsmithField:ItemBaseAlloctorAdd(itemID)
    return self._itemBaseAlloctor:Add(itemID)
end

function GunsmithField:GetRangeGUID()
    return self._uiContext:GetRangeGUID()
end

function GunsmithField:GetShortcutProcessor()
    return self._shortcutProcessor
end

function GunsmithField:GetOldWeaponExp(oldExp)
    if oldExp == nil then
        local exp = self._oldExp
        self._oldExp = nil
        return exp
    end
    self._oldExp = oldExp
end

function GunsmithField:IsWeaponPartId(weaponLevel, weaponPartId, isBool)
    weaponLevel = tonumber(weaponLevel)
    weaponPartId = tonumber(weaponPartId)
    if isBool then
        self._weaponLevel = weaponLevel
        self._weaponPartId = weaponPartId
    else
        if self._weaponLevel == weaponLevel and self._weaponPartId == weaponPartId then
            self._weaponLevel = nil
            self._weaponPartId = nil
            return true
        else
            return false
        end
    end
end

function GunsmithField:SetUpgradeLockParts(upgradeLockParts)
    self._uiContext:SetUpgradeLockParts(upgradeLockParts)
end

function GunsmithField:GetUpgradeLockParts()
    return self._uiContext:GetUpgradeLockParts()
end

function GunsmithField:IsUpgradeLockPart(inPartItemID)
    return self._uiContext:IsUpgradeLockPart(inPartItemID)
end

function GunsmithField:SetPropinfoFromRange(propinfo)
    self._uiContext:SetPropinfoFromRange(propinfo)
end

function GunsmithField:GetPropinfoFromRange()
    return self._uiContext:GetPropinfoFromRange()
end

function GunsmithField:SetGroupIDFromRange(groupID)
    self._uiContext:SetGroupIDFromRange(groupID)
end

function GunsmithField:GetGroupIDFromRange()
    return self._uiContext:GetGroupIDFromRange()
end

function GunsmithField:SetUnSycnRangeSolution(bUnSycnRangeSolution)
    self._uiContext:SetUnSycnRangeSolution(bUnSycnRangeSolution)
end

function GunsmithField:GetUnSycnRangeSolution()
    return self._uiContext:GetUnSycnRangeSolution()
end

return GunsmithField