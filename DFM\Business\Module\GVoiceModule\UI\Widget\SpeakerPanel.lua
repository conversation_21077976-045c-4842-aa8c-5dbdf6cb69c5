----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGVoice)
----- LOG FUNCTION AUTO GENERATE END -----------



local function log(...)
    loginfo("[SpeakerPanel]",...)
end

local EDFMGamePlayMode = import "EDFMGamePlayMode"
local GVoiceLogic = require "DFM.Business.Module.GVoiceModule.Logic.GVoiceLogic"
local GVoiceUILogic = require "DFM.Business.Module.GVoiceModule.Logic.GVoiceUILogic"
local USlateBlueprintLibrary = import "SlateBlueprintLibrary"
local TeammateSpeakerWidget = require "DFM.Business.Module.GVoiceModule.UI.TeammateSpeakerWidget"
local UWidgetLayoutLibrary = import "WidgetLayoutLibrary"
local UDFMGameHudDelegates = import "DFMGameHudDelegates"
local ChatLogic = require "DFM.Business.Module.ChatModule.Logic.ChatLogic"
local ESpeakerState = Module.GVoice.Config.ESpeakerState
local UDFMGameNotch = import "DFMGameNotch"
local DFMGameNotch = UDFMGameNotch.Get(GetGameInstance())
local ULuaSubsystem = import "LuaSubsystem"

local KEY_PANEL_ORIGIN_OFFSET = FVector2D(0, -10)
local SpeakerPanel = ui("SpeakerPanel")
local UIManager = Facade.UIManager
function SpeakerPanel:Ctor()
    self._wtSpeakerPanel = self:Wnd("SpeakerPanelTest", UIWidgetBase)

    self._wtCanvasPanel_0 = self:Wnd("CanvasPanel_0", UIWidgetBase)

    self._wtOffSpeaker = self:Wnd("WBP_Chat_RangeSelection_3", UIWidgetBase)
    self._wtOffSpeakerBtn = self._wtOffSpeaker:Wnd("DFButton_0", UIButton)
    self._wtOffSpeakerBtn:Event("OnClicked", self._OnSpeakerBtnClick, self, EGVoiceButtonType.Close)

    -- self._wtTeamSpeaker = self:Wnd("WBP_Chat_RangeSelection", UIWidgetBase)
    -- self._wtTeamSpeakerBtn = self._wtTeamSpeaker:Wnd("DFButton_0", UIButton)
    -- self._wtTeamSpeakerBtn:Event("OnClicked", self._OnSpeakerBtnClick, self, EGVoiceButtonType.Team)

    self._wtAllSpeaker = self:Wnd("WBP_Chat_RangeSelection_1", UIWidgetBase)
    self._wtAllSpeakerBtn = self._wtAllSpeaker:Wnd("DFButton_0", UIButton)
    self._wtAllSpeakerBtn:Event("OnClicked", self._OnSpeakerBtnClick, self, EGVoiceButtonType.All)
    self._wtAllLine = self:Wnd("DFImage_1", UIWidgetBase)
    self._wtAllLine:SelfHitTestInvisible()

    self._wtCampSpeaker = self:Wnd("WBP_Chat_RangeSelection_2", UIWidgetBase)
    self._wtCampSpeakerBtn = self._wtCampSpeaker:Wnd("DFButton_0", UIButton)
    self._wtCampSpeakerBtn:Event("OnClicked", self._OnSpeakerBtnClick, self, EGVoiceButtonType.Camp)
    self._wtCampLine = self:Wnd("DFImage_2", UIWidgetBase)

    self._wtTeammateSpeakerCanvas = self:Wnd("TeammateSpeakerCanvas", UIWidgetBase)
    self._wtTeammateSpeakerCampCanvas = self:Wnd("TeammateSpeakerCanvas_1", UIWidgetBase)
    self._wtTeammateSpeakerCampPanel = self:Wnd("DFCanvasPanel_0", UIWidgetBase)

    self._wtTeammatesSpeaker = {}
    for i = 1, 3 do
        self._wtTeammatesSpeaker[i] = self:Wnd("TeammateSpeaker_" .. i, TeammateSpeakerWidget)
    end
    self._wtTeammatesCampSpeaker = {}

    self._bpBtnList = {
        [EGVoiceButtonType.Close] = self._wtOffSpeaker,
        [EGVoiceButtonType.Camp] = self._wtCampSpeaker,
        [EGVoiceButtonType.All] = self._wtAllSpeaker,
    }

    if DFMGameNotch:IsFoldDevice() then 
        self.statusHandle = DFMGameNotch.OnFoldStatusChanged:Add(CreateCPlusCallBack(self._OnFoldStatusChanged, self))
    end
    ULuaSubsystem.Get().OnNotifyLuaResolutionResized:Add(self._OnNotifyResolutionResized, self)
end

function SpeakerPanel:_OnSpeakerBtnClick(buttonType)
    loginfo("SpeakerPanel:_OnSpeakerBtnClick ", buttonType)
    Module.GVoice:SetSpeakerButtonType(buttonType)
    UIManager:CloseUI(self)
end

function SpeakerPanel:OnOpen()
    if Module.GVoice:IsCommanderGameMode() then
        self._wtCampSpeaker:SelfHitTestInvisible()
        self._wtCampLine:Collapsed()
        if Module.GVoice:IsMicrophoneChannelAvailable(ESpeakingChanel.All) then
            self._wtAllSpeaker:SelfHitTestInvisible()
            self._wtAllLine:SelfHitTestInvisible()
        else
            self._wtAllSpeaker:Collapsed()
            self._wtAllLine:Collapsed()
        end
    else
        self._wtCampSpeaker:Collapsed()
        self._wtCampLine:Collapsed()
    end
    self._wtSpeakerPanel:SetRenderOpacity(0)
    local gameInst = GetGameInstance()
    self._buttonUpHandle = UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonUpEvent:Add(self._OnMouseButtonUp, self)
    self:_RefreshSpeakerPanel()
    self:_RefreshTeammatesSpeakers()
    self.mUpdatePosTimer = Timer.DelayCall(0.2,self._UpdatePanelPosition,self)
end

function SpeakerPanel:_OnFoldStatusChanged()
    loginfo("SpeakerPanel:_OnFoldStatusChanged")
    self:_UpdatePanelPosition()
end

function SpeakerPanel:_OnNotifyResolutionResized()
    loginfo("SpeakerPanel:_OnNotifyResolutionResized")
    Timer.DelayCall(0.5, function ()
        self:_UpdatePanelPosition()
    end)
end

function SpeakerPanel:ReleaseCurrentTimer()
    if self.mUpdatePosTimer then
        Timer.CancelDelay(self.mUpdatePosTimer)
        self.mUpdatePosTimer = nil
    end
end

function SpeakerPanel:OnInitExtraData(parentWidget,anchor,center,offset,fOnCloseCallback)
    log("SpeakerPanel:OnInitExtraData")
    self._parentWidget = parentWidget
    self.anchor,self.center,self.offset = anchor, center, offset
    self._fOnCloseCallback = fOnCloseCallback
end

function SpeakerPanel:_UpdatePanelPosition()
    log("SpeakerPanel:_UpdatePanelPosition")
    if (not self._parentWidget) or (not self._parentWidget.GetCachedGeometry) then
        return
    end

    local parentWidget = self._parentWidget
    local panelWigdget = self._wtSpeakerPanel
    local anchor, center, offset = self.anchor,self.center,self.offset

    local isIosPlatform = false
    if PLATFORM_IOS then
        isIosPlatform = true
    end

    if ChatLogic.IsInGameStage() or type(offset) == "userdata" then

        if ChatLogic.IsInGameStage() then

            anchor, center = ChatLogic.CalTheLegalAttachConfig(
                parentWidget, self._wtSpeakerPanel, self._wtCanvasPanel_0, offset)
        end

        ChatLogic.AttachWidgetTo(self, panelWigdget, parentWidget, offset, anchor, center, isIosPlatform)
    else
        UIUtil.AttachWidgetTo(self, panelWigdget, parentWidget, offset, anchor, center)
    end

    self._wtSpeakerPanel:SetRenderOpacity(1)

    if anchor.Y > 0.5 then
        self:PlayWidgetAnim(self.WBP_Chat_Speaker_in_Down)
    else
        self:PlayWidgetAnim(self.WBP_Chat_Speaker_in_Up)
    end
end

function SpeakerPanel:_RefreshSpeakerPanel()
    log("SpeakerPanel:_RefreshSpeakerPanel")
    local curBtnType = Module.GVoice:GetSpeakerButtonType()
    for btnType, btn in pairs(self._bpBtnList) do
        local btnImagePath = Module.GVoice:GetSpeakerButtonImagePath(btnType)
        local btnText = Module.GVoice:GetButtonText(btnType)
        btn:SetSpeakerBtn(btnType == curBtnType, btnImagePath, btnText)
    end
end

function SpeakerPanel:OnClose()
    log("SpeakerPanel:OnClose")
    local gameInst = GetGameInstance()
    UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonUpEvent:Remove(self._buttonUpHandle)
    self:RemoveAllLuaEvent()
    if self._fOnCloseCallback then
        self._fOnCloseCallback()
    end
    self:ReleaseCurrentTimer()
    Facade.UIManager:ClearSubUIByParent(self, self._wtTeammateSpeakerCampCanvas)
    Facade.UIManager:ClearSubUIByParent(self, self._wtTeammateSpeakerCanvas)
end

function SpeakerPanel:ResetPosition()
end

function SpeakerPanel:_OnMouseButtonUp(mouseEvent)
    log("SpeakerPanel:_OnMouseButtonUp")
    local sceenPos = mouseEvent:GetScreenSpacePosition()
    local geometry = self._wtSpeakerPanel:GetCachedGeometry()
    local isUnder = USlateBlueprintLibrary.IsUnderLocation(geometry, sceenPos)
    if not isUnder then
        UIManager:CloseUI(self)
    end
end

function SpeakerPanel:_RefreshTeammatesSpeakers()
    local buttonType = Module.GVoice:GetSpeakerButtonType()
    if buttonType == EGVoiceButtonType.Camp or buttonType == EGVoiceButtonType.CampPress then
        local dataAllList = Module.GVoice:GetTeamMemberList(EGVoiceButtonType.All)
        local dataCampList = Module.GVoice:GetTeamMemberList(EGVoiceButtonType.Camp)
        local dataTempAllList = {}
        for _, memberData in ipairs(dataAllList) do
            dataTempAllList[memberData.playerId] = memberData
        end
        local dataCampFilterList = {}
        for _, memberData in ipairs(dataCampList) do
            --只有队长指挥官显示，而且不在全队列表中
            if memberData.teamIdentity and memberData.teamIdentity > 1 and (not dataTempAllList[memberData.playerId]) then
                table.insert(dataCampFilterList, memberData)
            end
        end

        self:_RefreshTeammatesSpeakersView(dataAllList, self._wtTeammatesSpeaker, self._wtTeammateSpeakerCanvas)
        self:_RefreshTeammatesSpeakersView(dataCampFilterList, self._wtTeammatesCampSpeaker, self._wtTeammateSpeakerCampCanvas)
        if #dataCampFilterList > 0 then
            self._wtTeammateSpeakerCampPanel:SelfHitTestInvisible()
        else
            self._wtTeammateSpeakerCampPanel:Collapsed()
        end
    else
        local dataList = Module.GVoice:GetTeamMemberList(buttonType)
        self:_RefreshTeammatesSpeakersView(dataList, self._wtTeammatesSpeaker, self._wtTeammateSpeakerCanvas)
        self._wtTeammateSpeakerCampPanel:Collapsed()
    end
end

function SpeakerPanel:_RefreshTeammatesSpeakersView(dataList, viewList, uiRoot)
    local dataNum = #dataList
    local viewNum = #viewList
    local bInSOL = Server.ArmedForceServer:GetCurArmedForceMode() == EArmedForceMode.SOL
    local gameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    local isInGame = gameFlow == EGameFlowStageType.Game
    loginfo("SpeakerPanel:_RefreshTeammatesSpeakersByButtonType -dataNum:", dataNum, bInSOL)

    if viewNum > dataNum then
        for i = viewNum, dataNum + 1, -1 do
            viewList[i]:Collapsed()
        end
    end

    if dataNum > 0 then
        for i = 1, dataNum do
            local memberData = dataList[i]
            if i > viewNum then
                local weakUIIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.TeammateSpeakerWidget, uiRoot)
                local uiIns = getfromweak(weakUIIns)
                table.insert(viewList, uiIns)
                viewNum = viewNum + 1
            end

            local memberView = viewList[i]
            if memberView then
                memberView:SetInfo(memberData.playerId, memberData.teamIndex, memberData.playerName, bInSOL, isInGame)
                memberView:SelfHitTestInvisible()
                local type = 0
                if viewNum == dataNum then
                    type = 1
                end
                memberView:SetType(type)
            end
        end
        uiRoot:SelfHitTestInvisible()
    else
        uiRoot:Collapsed()
    end
end

return SpeakerPanel