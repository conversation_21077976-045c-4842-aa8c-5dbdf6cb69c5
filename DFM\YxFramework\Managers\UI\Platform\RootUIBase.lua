----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaFUIManager)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class RootUIBase : LuaUIBaseView
local RootUIBase = ui("RootUIBase")
local PlatformLogic = require "DFM.YxFramework.Managers.UI.Platform.PlatformLogic"
local TransitionUI = require "DFM.YxFramework.Managers.UI.Platform.TransitionUI"

----------------------------------------------------------------------------------------------------------------
--- * self._wtCanvasFrameGod-----------------------------------##全局根节点
---     *self._wtCanvasFrameLayerBackRoot----------------------**背景Layer-------------容纳EUILayer.BackRoot
---
---     *self.wtSafeZone_Frame---------------------------------##游戏安全区（功能型边距）
---         *self._wtCanvasFrameRoot---------------------------##通用根节点
---             *self._wtCanvasFrameLayerHUD_ScreenEffect---------**HUDLayer------------容纳EUILayer.HUD中类型为ScreenEffect的UI
---             *self._wtCanvasFrameLayerHUD_Mark-----------------**HUDLayer------------容纳EUILayer.HUD中类型为Mask的UI
---             *self._wtCanvasFrameLayerHUD_Common---------------**HUDLayer------------容纳EUILayer.HUD中类型为Common的UI
---             *self._wtCanvasFrameLayerHUD_Hint-----------------**HUDLayer------------容纳EUILayer.HUD中类型为Hint的UI
---             *self._wtCanvasFrameLayerHUD_Feedback-------------**HUDLayer------------容纳EUILayer.HUD中类型为Feedback的UI
---
---             *self._wtCanvasFrameLayerScene--------------------**SceneLayer------------容纳EUILayer.Scene的UI
---             *self._wtCanvasFrameLayerRoot---------------------**RootLayer-------------容纳EUILayer.Root的UI
---
---             *self._wtPlatformPaddingForContent-------------##内容根节点（美观型边距）
---                 *self._wtCanvasFrameLayerStack-------------------##StackLayer---------容纳EUILayer.Stack的UI
---
---             *self._wtCanvasFrameLayerTop----------------------**TopLayer---------------容纳EUILayer.Top的UI
---             *self._wtCanvasFrameLayerLoading------------------**LoadingLayer---------------容纳EUILayer.Loading的UI
---             *self._wtCanvasFrameLayerPop----------------------**PopLayer---------------容纳EUILayer.Pop的UI
---             *self._wtCanvasFrameLayerTip----------------------**TipLayer---------------容纳EUILayer.Tip的UI
---             *self._wtCanvasFrameLayerMask---------------------**MaskLayer--------------容纳EUILayer.Mask的UI
---             *self._wtCanvasPanelLayerAdmin---------------------**AdminLayer--------------容纳EUILayer.Admin的UI
---             *self._wtCanvasFrameLayerWatermark----------------**WatermarkLayer---------容纳EUILayer.Watermark的UI
----------------------------------------------------------------------------------------------------------------

function RootUIBase:Ctor()
    self:SetIsIgnoreWorldForceGc(true)
    self._curSafePadding = FMargin(0, 0, 0, 0)
    --- [全局根节点]:不作为容器使用
    self._wtCanvasFrameGod = self:Wnd("wtCanvasPanel_God", UIWidgetBase)
    --- [游戏安全区]:不作为容器使用（功能型边距）
    self._wtSafeZone_Frame = self:Wnd("wtSafeZone_Frame", UIWidgetBase)
    --- [通用根节点]:用于内容区以外的UILayer,不作为容器使用
    self._wtCanvasFrameRoot = self:Wnd("wtCanvasPanel_Root", UIWidgetBase)
    --- [内容根节点]:内容区平台适配Box，不作为容器使用（美观型边距） 
    self._wtPlatformPaddingForContent = self:Wnd("wtPlatformPaddingBox_Content", UIWidgetBase)

    --- **HUD
    self._wtCanvasFrameLayerHUD_ScreenEffect = self:Wnd("wtCanvasPanelLayer_HUD_ScreenEffect", UIWidgetBase)
    self._wtCanvasFrameLayerHUD_Mark = self:Wnd("wtCanvasPanelLayer_HUD_Mark", UIWidgetBase)
    self._wtCanvasFrameLayerHUD_Common = self:Wnd("wtCanvasPanelLayer_HUD_Common", UIWidgetBase)
    self._wtCanvasFrameLayerHUD_Hint = self:Wnd("wtCanvasPanelLayer_HUD_Hint", UIWidgetBase)
    self._wtCanvasFrameLayerHUD_Feedback = self:Wnd("wtCanvasPanelLayer_HUD_Feedback", UIWidgetBase)
    self._wtCanvasFrameLayerHUD_Touch = self:Wnd("wtCanvasPanelLayer_HUD_Touch", UIWidgetBase)
    self._wtCanvasFrameLayerHUD_Popup = self:Wnd("wtCanvasPanelLayer_HUD_Popup", UIWidgetBase)
    self._wtCanvasFrameLayerHUD_LargePopup = self:Wnd("wtCanvasPanelLayer_HUD_LargePopup", UIWidgetBase)
    
    --- **背景Layer，用于EUILayer.BackRoot
    self._wtCanvasFrameLayerBackRoot = self:Wnd("wtCanvasPanelLayer_BackRoot", UIWidgetBase)
    --- **SceneLayer，用于EUILayer.Scene
    self._wtCanvasFrameLayerScene = self:Wnd("wtCanvasPanelLayer_Scene", UIWidgetBase)
    --- **RootLayer，用于EUILayer.Root
    self._wtCanvasFrameLayerRoot = self:Wnd("wtCanvasPanelLayer_Root", UIWidgetBase)
    --- **StackLayer，用于栈UI
    self._wtCanvasFrameLayerStack = self:Wnd("wtCanvasPanelLayer_Stack", UIWidgetBase)
    --- **TopLayer，用于EUILayer.Top
    self._wtCanvasFrameLayerTop = self:Wnd("wtCanvasPanelLayer_Top", UIWidgetBase)
    --- **TopLayer，用于EUILayer.Loading
    self._wtCanvasFrameLayerLoading = self:Wnd("wtCanvasFrameLayer_Loading", UIWidgetBase)
    --- **TopLayer，用于EUILayer.Pop
    self._wtCanvasFrameLayerPop = self:Wnd("wtCanvasPanelLayer_Pop", UIWidgetBase)
    --- **TipLayer，用于EUILayer.Tip
    self._wtCanvasFrameLayerTip = self:Wnd("wtCanvasPanelLayer_Tip", UIWidgetBase)
    --- **MaskLayer，用于EUILayer.Mask
    self._wtCanvasFrameLayerMask = self:Wnd("wtCanvasPanelLayer_Mask", UIWidgetBase)
    --- **AdminLayer，用于EUILayer.Admin
    self._wtCanvasFrameLayerAdmin = self:Wnd("wtCanvasPanelLayer_Admin", UIWidgetBase)
    --- **WatermarkLayer，用于EUILayer.Watermark
    self._wtCanvasFrameLayerWatermark = self:Wnd("wtCanvasPanelLayer_Watermark", UIWidgetBase)

    self:SetCppValue("bInterceptPreviewMouse", false)
    self:SetCppValue("bSwallowClick", false)
end

function RootUIBase:OnOpen()
    self.bIsFocusable = false
    self:SetCppValue("bSwallowClick", false)
end

--------------------------------------------------------------------------
--- Layer容器 FrameLayer
--------------------------------------------------------------------------
function RootUIBase:GetFrameLayerContiner(layerType)
    local LayerName = MapUILayer2Name[layerType]
    local layerContainer = self["_wtCanvasFrameLayer"..LayerName]
    return layerContainer
end

function RootUIBase:CheckIsFrameContainer(checkParent)
    local bContainer = false
    for _, layerType in pairs(EUILayer) do
        if checkParent == self:GetFrameLayerContiner(layerType) then
            bContainer = true
            return bContainer
        end
    end
    return bContainer
end

-- function RootUIBase:GetInjectionList()
-- 	return true
-- end

function RootUIBase:GetInjectionList()
	local list = LuaUIBaseView.GetInjectionList(self) or {}
	table.append(list, {
		"AddToPersistentFrameLayer",
	})
	return list
end

function UECall_AddToPersistentFrameLayer(uiIns, zOrder, uiLayer)
    logframe('RootUIBase:UECall_AddToPersistentFrameLayer(uiIns, uiLayer, zOrder) Loading')
    if Facade.UIManager then
        Facade.UIManager:AddToFrameRoot(
            uiIns, 
            zOrder, 
            uiLayer
        )
        return true
    else
        return false
    end
end

function RootUIBase:Imp_AddToPersistentFrameLayer(uiIns, zOrder, uiLayer)
    logframe('RootUIBase:Imp_AddToPersistentFrameLayer(uiIns, uiLayer, zOrder) Loading')
    Facade.UIManager:AddToFrameRoot(
        uiIns, 
        zOrder, 
        uiLayer
    )
    return true
end

---@param uiIns ui
---@param zOrder number
---@param layerType EUILayer
function RootUIBase:AddToFrameLayer(uiIns, zOrder, layerType)
    if not PlatformLogic.IsOutOfAreaType(layerType) then
        local layerContainer = self:GetFrameLayerContiner(layerType)
        if layerContainer and not hasdestroy(layerContainer) and isvalid(uiIns) and not hasdestroy(uiIns) then
            --if uiIns.bIsHud then
            -------------给hud套上invalidationbox和antisafezone start-------------------
            --    self:AddToLayerWithInvalidationAndAntiSafezone(layerContainer, uiIns)
            -------------给hud套上invalidationbox和antisafezone stop-------------------
            --else
                layerContainer:AddChild(uiIns)
            --end
        end
        if PlatformLogic.IsForceAreaType(layerType) and uiIns.SetFocus then
            uiIns:SetFocus()
        end
    end
    --local canvasSlot = uiIns.bIsHud and UWidgetLayoutLibrary.SlotAsCanvasSlot(uiIns:GetDynamicRoot()) or UWidgetLayoutLibrary.SlotAsCanvasSlot(uiIns)
    local canvasSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(uiIns)
    if canvasSlot then
        canvasSlot:SetZOrder(zOrder)
        canvasSlot:SetLayout(PlatformLogic.anchorData)
    else
        logerror('RootUIBase:AddToFrameLayer(uiIns, zOrder, layerType) canvasSlot is nil', uiIns, zOrder, layerType)
    end
end

---@param uiIns ui
---@param zOrder number
---@param newLayerType EUILayer optional
function RootUIBase:SetZOrderInFrameLayer(uiIns, zOrder, newLayerType)
    if newLayerType then
        local uiSettings = uiIns:GetUISettings()
        if uiSettings then
            if uiSettings.UILayer ~= newLayerType then
                return self:AddToFrameLayer(uiIns, zOrder, newLayerType)
            end
        end
        if PlatformLogic.IsForceAreaType(newLayerType) and uiIns.SetFocus then
            uiIns:SetFocus()
        end
    end
    local canvasSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(uiIns)
    if canvasSlot then
        canvasSlot:SetZOrder(zOrder)
        canvasSlot:SetLayout(PlatformLogic.anchorData)
    else
        logerror('RootUIBase:SetZOrderInFrameLayer(uiIns, zOrder) uiIns isnt in RootUIBase', debug.traceback())
    end
end

function RootUIBase:ClearFrameCanvas()
    for _, layerType in pairs(EUILayer) do
        --跳过hud的层级
        local hudLayerList = {
            EUILayer.HUD,
            EUILayer.HUD_ScreenEffect,
            EUILayer.HUD_Mark,
            EUILayer.HUD_Common,
            EUILayer.HUD_Hint,
            EUILayer.HUD_Feedback,
            EUILayer.HUD_Touch,
            EUILayer.HUD_Popup,
            EUILayer.HUD_LargePopup,
            EUILayer.Loading,
            EUILayer.Admin
        }
        if not table.contains(hudLayerList, layerType) then
            local layerContainer = self:GetFrameLayerContiner(layerType)
            PlatformLogic.ClearFrameContainer(layerContainer, nil)
        end
    end

    rawset(self, "_childWidgets", {})
    rawset(self, "_wndWidgets", {})
end

--------------------------------------------------------------------------
--- 根节点 SafeZone
--------------------------------------------------------------------------
function RootUIBase:InitSafeZone()
    -- local safeZoneSlot = UWidgetLayoutLibrary.SlotAsSafeBoxSlot(self._wtCanvasFrameRoot)
    -- safeZoneSlot.Padding = self._curSafePadding
    -- safeZoneSlot.SafeAreaScale

    local safePadding, safePaddingScale, spillOverPadding = UWidgetBlueprintLibrary.GetSafeZonePadding(GetWorld(), FVector4(), FVector2D(0, 0), FVector4())
    logwarning('RootUIBase:InitSafeZone() safePadding', 
    'Left:', safePadding.X, 
    'Right:', safePadding.Z,
    'Top:', safePadding.Y,
    'Bottom:', safePadding.W)
    logwarning('RootUIBase:InitSafeZone() safePaddingScale', 
    'X:', safePaddingScale.X, 
    'Y:', safePaddingScale.Y)
    logwarning('RootUIBase:InitSafeZone() spillOverPadding', 
    'Left:', spillOverPadding.X, 
    'Right:', spillOverPadding.Z,
    'Top:', spillOverPadding.Y,
    'Bottom:', spillOverPadding.W)

    local newSafePadding = FMargin(safePadding.X, safePadding.Y, safePadding.Z, safePadding.W)
    self:FreshSafeZone()
    logwarning('RootUIBase:InitSafeZone() ** RootUpdate - FreshSafeZone ** 成功刷新 safePadding, Finished!')
    self._curSafePadding = newSafePadding
end

function RootUIBase:GetCurSafePadding()
    return self._curSafePadding
end

function RootUIBase:Destroy()
    logframe("[ Lua GC Debug ] ******* RootUIBase:Destroy() -> ", self, self._cname, self.__cppinst)
end

--------------------------------------------------------------------------
--- 交互屏蔽相关
--------------------------------------------------------------------------
--- 是否拦截交互，仅允许InputMonitor慎用
function RootUIBase:SetIsInterceptPreviewMouse(bInterceptPreviewMouse)
    self:SetCppValue("bInterceptPreviewMouse", bInterceptPreviewMouse)
end

function RootUIBase:OnPreviewMouseButtonDown(InGeometry, InMouseEvent)
    loginfo('RootUIBase:OnPreviewMouseButtonDown(InGeometry, InMouseEvent)', InGeometry, InMouseEvent)
end

--------------------------------------------------------------------------
--- 子节点相关
--------------------------------------------------------------------------
function RootUIBase:AddChild_CallFromChild(childIns)
	self._childWidgets = rawget(self, "_childWidgets") or {}
	table.insert(self._childWidgets, childIns)
end

function RootUIBase:RemoveChild_CallFromChild(childIns)
	local childWidgets = rawget(self, "_childWidgets")
	if childWidgets then
		table.removebyvalue(childWidgets, childIns)
	end
end


--------------------------------------------------------------------------
--- 根节点表现相关
--------------------------------------------------------------------------
function RootUIBase:PlayFrameAnim(frameAniType)
    self:PlayWidgetAnim(self[MapFrameAnimType2Name[frameAniType]])
end

function RootUIBase:OnAnimStarted(anim)
    self._wtSafeZone_Frame:SetVisibility(ESlateVisibility.HitTestInvisible)
end

function RootUIBase:OnAnimFinished(anim)
    local roomInAnim = self[MapFrameAnimType2Name[EFrameAnimType.RoomIn]]
    local fadeInAnim = self[MapFrameAnimType2Name[EFrameAnimType.FadeIn]]
    local roomOutAnim = self[MapFrameAnimType2Name[EFrameAnimType.RoomOut]]
    local fadeOutAnim = self[MapFrameAnimType2Name[EFrameAnimType.FadeOut]]
    if anim == roomInAnim or anim == fadeInAnim then
        self._wtSafeZone_Frame:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    elseif anim == roomOutAnim or anim == fadeOutAnim then
        self._wtSafeZone_Frame:SetVisibility(ESlateVisibility.Collapsed)
    end
end

--------------------------------------------------------------------------
--- 转场表现相关
--------------------------------------------------------------------------
function RootUIBase:CommitTransition(bEnter, ...)
    logwarning('Please check FrameRoot TransitionUI')
end

function RootUIBase:RollbackTransition(...)
    logwarning('Please check FrameRoot TransitionUI')
end

--------------------------------------------------------------------------
--- 背景表现相关
--------------------------------------------------------------------------

return RootUIBase