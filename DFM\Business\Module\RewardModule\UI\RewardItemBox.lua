----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMReward)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class RewardItemBox : LuaUIBaseView
local RewardItemBox = ui("RewardItemBox")
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig" 
local EComp = CommonWidgetConfig.EIVWarehouseTempComponent
local EIVSlotPos = CommonWidgetConfig.EIVSlotPos

function RewardItemBox:Ctor()
	self._wtSelected = self:Wnd("wtSelected", UIWidgetBase)
	self._wtCommonItemTemplate = self:Wnd("wtCommonItemTemplate", IVCommonItemTemplate)
	self._wtOwnedText = self:Wnd("wtOwnedText", UITextBlock)
	self._wtLevelText = self:Wnd("wtLevelText", UITextBlock)
end

-- qualityInfo : {id, num}
function RewardItemBox:OnInitExtraData()
end

function RewardItemBox:OnOpen()
	self._wtSelected:Collapsed()
	self._wtOwnedText:Collapsed()
	self._wtLevelText:Collapsed()
end

function RewardItemBox:SetInfo(item)
	self._item = item
    local bindingComp = self._wtCommonItemTemplate:FindOrAdd(EComp.BottomLeftIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomLeft)
	if bindingComp then
		bindingComp:SetStyle(CommonWidgetConfig.EIVIconTextStyle.Binding)
	end
	if item.itemMainType == EItemType.Fashion then
		local bgComp = self._wtCommonItemTemplate:FindOrAdd(EComp.SecondaryIcon, UIName2ID.IVCommercializeShadingComponent, EIVSlotPos.BgLayer)
		if bgComp then
			bgComp:RefreshComponent()
		end
		local iconComp = self._wtCommonItemTemplate:FindOrAdd(EComp.TopRightIconText, UIName2ID.IVCommercializeItemComponent, EIVSlotPos.IconLayer)
		if iconComp then
			iconComp:RefreshComponent()
		end
		self._wtCommonItemTemplate:EnableComponent(EComp.SecondaryIcon, true)
		self._wtCommonItemTemplate:EnableComponent(EComp.NeedBuyIcon, true)
		self._wtCommonItemTemplate:EnableComponent(EComp.ItemIcon, false)
	end
	if item.bGiveaway == true then
		local giveawayComp = self._wtCommonItemTemplate:FindOrAdd(EComp.TopRightIconText, UIName2ID.IVGiveawayComponent, EIVSlotPos.TopRight)
		if giveawayComp then
			giveawayComp:RefreshComponent()
		end
		self._wtCommonItemTemplate:EnableComponent(EComp.TopRightIconText, true)
		if giveawayComp then
			giveawayComp:PlayAnimation(giveawayComp.WBP_Store_PacksItem_suan, 0, 1, EUMGSequencePlayMode.Forward, 1, true)
		end
	end
	if item.conversionTxt and item.conversionTxt ~= "" then
		local decompositionComp = self._wtCommonItemTemplate:FindOrAdd(EComp.TipMask, UIName2ID.IVDecompositionComp, EIVSlotPos.MaskLayer)
		decompositionComp:SetRichText(item.conversionTxt)
		decompositionComp:RefreshComponent()
	end
	self._wtCommonItemTemplate:InitItem(self._item)
	local rawExpireInfo = Server.CollectionServer:GetPropExpireInfo(self._item.id)
	if rawExpireInfo and #rawExpireInfo > 0 then
		table.sort(rawExpireInfo, function(expireInfoA, expireInfoB)
			return expireInfoA.expireTime > expireInfoB.expireTime 
		end)
		local expireTextComp = self._wtCommonItemTemplate:FindOrAdd(EComp.BottomLeftIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomLeft)
		expireTextComp:ShowIconAndText(
			"PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0011.Common_ItemProp_Icon_0011'", 
			Module.Collection:GetExpireTimeTextByTimestamp(rawExpireInfo[1].expireTime)
		)
		expireTextComp:SetCornerStyle()
		self._wtCommonItemTemplate:EnableComponent(EComp.BottomLeftIconText, rawExpireInfo and #rawExpireInfo > 0)
	end
	self._wtCommonItemTemplate:SetIsOnCommonPop(true)
    local fOnWidgetInsClicked = CreateCallBack(function(self, bSelected, itemWidget)
		if itemWidget and itemWidget.SetWeaponBulletUnEquipVisible then
			itemWidget:SetWeaponBulletUnEquipVisible(false)
		end
		self:SetSelected(bSelected)
    end,self)
	self._wtCommonItemTemplate:SetAdditionalClickCallback(fOnWidgetInsClicked)
	if self._item.owned == true then
		self._wtOwnedText:SetText(Module.Reward.Config.Loc.Owned)
		self._wtOwnedText:Visible()
	end
	self._wtCommonItemTemplate:Visible()
end

function RewardItemBox:PlayAnim()
	if self._item then
		if self._item.quality == 6 then
			self._wtCommonItemTemplate:PlayIVAnimation("WBP_CommonItemTemplate_in_special_Red", 1, EUMGSequencePlayMode.Forward, 1, false)
		elseif self._item.quality == 5 then
			self._wtCommonItemTemplate:PlayIVAnimation("WBP_CommonItemTemplate_in_special", 1, EUMGSequencePlayMode.Forward, 1, false)
		elseif self._item.quality == 4 then
			self._wtCommonItemTemplate:PlayIVAnimation("WBP_CommonItemTemplate_in_special_Purple", 1, EUMGSequencePlayMode.Forward, 1, false)
		else
			self._wtCommonItemTemplate:PlayIVAnimation("WBP_CommonItemTemplate_in_2", 1, EUMGSequencePlayMode.Forward, 1, false)
		end
	end
end

function RewardItemBox:SetSelected(bSelected)
	if bSelected then
		self._wtSelected:SelfHitTestInvisible()
	else
		self._wtSelected:Collapsed()
	end
end

return RewardItemBox
