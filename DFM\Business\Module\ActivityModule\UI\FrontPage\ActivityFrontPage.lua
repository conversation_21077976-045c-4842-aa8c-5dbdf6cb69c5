----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------

local function CheckURL(url)
    return url ~= nil and url ~= ""
end

---@class ActivityFrontPage : LuaUIBaseView, HasNavigationAgent, HasAnimManager
local ActivityFrontPage = ui("ActivityFrontPage")

local ActivityLogic             = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"
local AnimManager               = require "DFM.Business.DataStruct.Common.Base.DFUtil.AnimManager"

---@class ActivityFrontPageItemInfo
---@field activityInfo  pb_ActivityInfo
---@field style         "big" | "medium" | "small" | "crossoverBig" | "crossoverSmall"
---@field themeID       integer

local TabPatternUIMap = {
    [1] = UIName2ID.ActivityFrontPageNormalLayout,
    [2] = UIName2ID.ActivityFrontPageCrossoverLayout,
    [3] = UIName2ID.ActivityFrontPageAnniversaryLayout,
}

local DEFAULT_IMAGE = "/Game/UI/UIAtlas/System/Activity/Sp/Activity_Sp_15.Activity_Sp_15"

function ActivityFrontPage:Ctor()
    AnimManager.Create(self)
    self._eventTabTable = ActivityLogic.GetEventTabConfigTable()
    self._wtDefaultDecorations = self:Wnd("DefaultDecorations", UIWidgetBase)
    self._wtLayoutSlot      = self:Wnd("FrontpageLayout"    , UIWidgetBase)
    self._wtCrossoverBgSlot = self:Wnd("CrossoverBgSlot"    , UIWidgetBase)
    self._wtBackground      = self:Wnd("DFCDNImage_0", DFCDNImage)
    self._wtLayout          = nil
    
    self.imgResStub = ResStub:NewIns()
    Facade.ResourceManager:AsyncLoadResource(self.imgResStub, DEFAULT_IMAGE)
end

---@param groupInfo ActivityGroupPresentationInfo
function ActivityFrontPage:OnInitExtraData(groupInfo, selectActivityID)
    local tabPattern = self._eventTabTable[groupInfo.groupID].tabPattern
    local themeID    = self._eventTabTable[groupInfo.groupID].themeID
    local background = self._eventTabTable[groupInfo.groupID].tabBackground

    if background ~= "" then
        self._wtBackground:SetCDNImage(background, true, Module.CDNIcon.Config.ECdnTagEnum.Activity)
    else
        Facade.ResourceManager:AsyncLoadResource(self.imgResStub, DEFAULT_IMAGE, 
            function(self, res)
                self._wtBackground:SetImage(res[DEFAULT_IMAGE], true)
            end, self
        )
    end

    Facade.UIManager:RemoveSubUIByParent(self, self._wtLayoutSlot)
    local uiIns = getfromweak(Facade.UIManager:AddSubUI(self, TabPatternUIMap[tabPattern], self._wtLayoutSlot))
    assert(uiIns)
    if uiIns then
        uiIns:SetData(groupInfo, selectActivityID)
        self._wtLayout = uiIns
    else
        self._wtLayout = nil
    end

    Facade.UIManager:RemoveSubUIByParent(self, self._wtCrossoverBgSlot)
    if themeID == 1 then
        Facade.UIManager:AddSubUI(self, UIName2ID.ActivityFrontPageCrossoverBg, self._wtCrossoverBgSlot, nil)
        self._wtDefaultDecorations:Collapsed()
    else
        self._wtDefaultDecorations:HitTestInvisible()
    end
end

function ActivityFrontPage:GetSortedActivities()
    if self._wtLayout then
        return self._wtLayout:GetSortedActivities()
    end
end

function ActivityFrontPage:OnShow()
    self:PlayAnimThen(self.WBP_Activity_Entrance_loop, {loopCnt = 0})
end

function ActivityFrontPage:OnClose()
    Facade.UIManager:RemoveAllSubUI(self)
end

return ActivityFrontPage