----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMStore)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

---@class NightMarketRewardPop : LuaUIBaseView
local NightMarketRewardPop = ui("NightMarketRewardPop")
local StoreConfig = Module.Store.Config
local StoreLogic = require "DFM.Business.Module.StoreModule.Logic.StoreLogic"
local CommonPopWindows = require "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindows"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"

function NightMarketRewardPop:Ctor()
    -- self._wtCommonPopWin = self:Wnd("WBP_Store_NM_RewardPop_C_0", CommonPopWindows)
    -- local fCallbackIns = CreateCallBack(self._OnCloseBtnClicked,self)
    -- self._wtCommonPopWin:BindCloseCallBack(fCallbackIns)
    -- self._wtCommonPopWin:SetBackgroudClickable(true)

    self._rewardName = self:Wnd("wtTextBundleName", UITextBlock)
    self._rewardDesc = self:Wnd("DFTextBlock", UITextBlock)
    
    self._buyBtn = self:Wnd("wtCommonButtonV1S1", UIButton)
    self._buyBtn:Event("OnClicked", self.BuyItem, self)

    self._wtWaterFallList = UIUtil.WndWaterfallScrollBox(self, "DFWaterfallScrollView_0", self._OnGetItemCount, self._OnProcessItemWidget)
end

function NightMarketRewardPop:OnInitExtraData()
    local goods = Server.StoreServer:GetLuckyNestGoods()

    self._rewardName:SetText(Module.Store.Config.Loc.NightMarketFreeReward)
    self._rewardDesc:SetText(Module.Store.Config.Loc.NightMarketFreeRewardDesc)

    self.goods = goods
    self.needBuyGoods = {}
    for index, value in ipairs(goods) do
        if not (value.prop and Server.CollectionServer:IsOwnedWeaponSkin(value.prop.id, value.prop.gid) and Server.CollectionServer:IsOwnedMeleeSkin(value.prop.id)) then
            table.insert(self.needBuyGoods, value)
        end
        
    end
    self._wtWaterFallList:RefreshAllItems()

    self:_RefreshInfo()

    self._curSelectIndex = 1
end

function NightMarketRewardPop:OnShowBegin()
    self:AddLuaEvent(StoreConfig.evtStoreProductViewBundleItemClick, self._OnRewardClicked, self)
    self:AddLuaEvent(Server.StoreServer.Events.evtStoreBuyLuckyNestUpdate, self._RefreshInfo, self)
    self._buyBtn:SetMainTitle(Module.Store.Config.Loc.FreeToGet)

    if IsHD() then
        self:_EnableGamepadFeature()
    end
end

function NightMarketRewardPop:BuyItem()
    local currency_type_sub = StoreLogic.GetSubstituteCurrencyItemID()
    for index, value in ipairs(self.needBuyGoods) do
        Server.StoreServer:SendShopBuyLuckyNestItemReq(value.prop.id, value.currency_type, 0,
        currency_type_sub, 0)
    end
end

function NightMarketRewardPop:_OnGetItemCount()
    return #self.goods or 0
end

function NightMarketRewardPop:_OnProcessItemWidget(position, itemWidget)
    if self.goods[position] == nil then
        return
    end
    local curdata = self.goods[position].prop
    if curdata ~= nil then
        itemWidget:Visible()
        itemWidget:SetItemInfo(position, curdata, self._curSelectIndex, nil, nil, false)
    else
        itemWidget:Collapsed()
    end
end

function NightMarketRewardPop:_OnRewardClicked(index, goodInfo, uiIns)
    self:_PreviewItem(index)
end

function NightMarketRewardPop:_PreviewItem(index)  
    if self.goods[index] == nil then
        return
    end
    local curdata = self.goods[index].prop
    if curdata ~= nil then
        local item = ItemBase:New(curdata.id)
        local skinInfo = pb.WeaponSkinInfo:New()
        skinInfo.skin_id = curdata.id
        skinInfo.skin_gid = curdata.gid
        ItemBase.SetWeaponSkinInfo2PropInfo(curdata, skinInfo)
        ItemBase.SetWeaponSkinID2PropInfo(curdata, curdata.id)
        ItemBase.SetWeaponSkinGUID2PropInfo(curdata, curdata.gid)
        item:SetRawPropInfo(curdata)
        if item ~= nil then
            if item.itemMainType == EItemType.WeaponSkin or item.itemMainType == EItemType.Weapon then
                Module.Collection:ShowWeaponSkinDetailPage(item)
            elseif item.itemMainType == EItemType.Adapter and item.itemSubType == ItemConfig.EAdapterItemType.Pendant then
                Module.Collection:ShowHangingDetailPage(item)
            else
                return
            end
        end
    end
end

function NightMarketRewardPop:_RefreshInfo()
    local luckyNestRecord = Server.StoreServer:GetLuckyNestBoughtGoods()
    local allBuyed = true
    for index, value in ipairs(self.needBuyGoods) do
        if luckyNestRecord[value.prop.id] == nil then
            allBuyed = false
            break
        end
    end
    if allBuyed then
        self._buyBtn:SetIsEnabled(false)
    end
end

function NightMarketRewardPop:OnHideBegin()
    if IsHD() then
        self:_DisableGamepadFeature()
    end
end

function NightMarketRewardPop:OnHide()
    self:RemoveLuaEvent(StoreConfig.evtStoreProductViewBundleItemClick)
    self:RemoveLuaEvent(Server.StoreServer.Events.evtStoreBuyLuckyNestUpdate)
end

function NightMarketRewardPop:_OnCloseBtnClicked()
    Facade.UIManager:CloseUI(self)
end

function NightMarketRewardPop:_EnableGamepadFeature()

    -- 配置keyIcon
    if self._buyBtn then
        self._buyBtn:SetDisplayInputAction("MallPurchase", true, nil, true)
    end

    -- 配置输入
    if not self._Purchase then
        self._Purchase = self:AddInputActionBinding(
        "MallPurchase", 
        EInputEvent.IE_Pressed, 
        self.BuyItem,
        self, 
        EDisplayInputActionPriority.UI_Stack
        )  
    end

    -- 设置导航
    if self._wtWaterFallList then
        if not self._NavGroup_WaterFall then
            self._NavGroup_WaterFall = WidgetUtil.RegisterNavigationGroup(self._wtWaterFallList , self, "Hittest")
        end
    end

    if self._NavGroup_WaterFall then
        self._NavGroup_WaterFall:AddNavWidgetToArray(self._wtWaterFallList)
        self._NavGroup_WaterFall:SetScrollRecipient(self._wtWaterFallList)
        self._NavGroup_WaterFall:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Default)
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup_WaterFall)
    end

    self:_SetBottomBar()
end

function NightMarketRewardPop:_SetBottomBar()
    Module.CommonBar:RecoverBottomBarInputSummaryList()

    --- 显示按键提示
    local summaryList ={}

    table.insert(summaryList, {actionName = "SelectForStore",func = nil, caller = self ,bUIOnly = false, bHideIcon = false})   

    Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, false, true)
end

function NightMarketRewardPop:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end

    Module.CommonBar:RecoverBottomBarInputSummaryList()

    WidgetUtil.RemoveNavigationGroup(self)
    self._NavGroup_WaterFall = nil

    if self._Purchase then
        self:RemoveInputActionBinding(self._Purchase)
        self._Purchase = nil
    end

end

return NightMarketRewardPop