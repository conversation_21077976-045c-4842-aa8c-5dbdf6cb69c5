--cscpp_online.protoencode&decode functions.
function pb.pb_CSOnlineHeartbeatReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSOnlineHeartbeatReq) or {} 
    local __padding = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __padding ~= 0 then tb.padding = __padding end
    return tb
end

function pb.pb_CSOnlineHeartbeatReqEncode(tb, encoder)
    if(tb.padding) then    encoder:addi32(1, tb.padding)    end
end

function pb.pb_CSOnlineHeartbeatResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSOnlineHeartbeatRes) or {} 
    local __padding = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __padding ~= 0 then tb.padding = __padding end
    local __tick_count = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __tick_count ~= 0 then tb.tick_count = __tick_count end
    return tb
end

function pb.pb_CSOnlineHeartbeatResEncode(tb, encoder)
    if(tb.padding) then    encoder:addi32(1, tb.padding)    end
    if(tb.tick_count) then    encoder:addu64(2, tb.tick_count)    end
end

function pb.pb_CSStateGetInfoReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSStateGetInfoReq) or {} 
    return tb
end

function pb.pb_CSStateGetInfoReqEncode(tb, encoder)
end

function pb.pb_CSStateGetInfoResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSStateGetInfoRes) or {} 
    local __PlayerID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __PlayerID ~= 0 then tb.PlayerID = __PlayerID end
    local __State = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __State ~= 0 then tb.State = __State end
    local __TeamID = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    local __RoomID = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __RoomID ~= 0 then tb.RoomID = __RoomID end
    local __result = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.mode = pb.pb_MatchModeInfoDecode(decoder:getsubmsg(6))
    local __DsRoomID = decoder:getu64(7)
    if not PB_USE_DEFAULT_TABLE or __DsRoomID ~= 0 then tb.DsRoomID = __DsRoomID end
    local __PlatID = decoder:geti32(8)
    if not PB_USE_DEFAULT_TABLE or __PlatID ~= 0 then tb.PlatID = __PlatID end
    return tb
end

function pb.pb_CSStateGetInfoResEncode(tb, encoder)
    if(tb.PlayerID) then    encoder:addu64(1, tb.PlayerID)    end
    if(tb.State) then    encoder:addu32(2, tb.State)    end
    if(tb.TeamID) then    encoder:addu64(3, tb.TeamID)    end
    if(tb.RoomID) then    encoder:addu64(4, tb.RoomID)    end
    if(tb.result) then    encoder:addu32(5, tb.result)    end
    if(tb.mode) then    pb.pb_MatchModeInfoEncode(tb.mode, encoder:addsubmsg(6))    end
    if(tb.DsRoomID) then    encoder:addu64(7, tb.DsRoomID)    end
    if(tb.PlatID) then    encoder:addi32(8, tb.PlatID)    end
end

function pb.pb_CSStateBatchGetInfoReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSStateBatchGetInfoReq) or {} 
    tb.player_id = decoder:getu64ary(1)
    return tb
end

function pb.pb_CSStateBatchGetInfoReqEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
end

function pb.pb_CSStateBatchGetInfoResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSStateBatchGetInfoRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.player_info = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.player_info[k] = pb.pb_PlayerStateSimpleInfoDecode(v)
    end
    return tb
end

function pb.pb_CSStateBatchGetInfoResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.player_info) then
        for i=1,#(tb.player_info) do
            pb.pb_PlayerStateSimpleInfoEncode(tb.player_info[i], encoder:addsubmsg(2))
        end
    end
end

function pb.pb_PlayerStateSimpleInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PlayerStateSimpleInfo) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __state = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __state ~= 0 then tb.state = __state end
    local __fighting_time = decoder:geti64(7)
    if not PB_USE_DEFAULT_TABLE or __fighting_time ~= 0 then tb.fighting_time = __fighting_time end
    local __member_num = decoder:getu32(9)
    if not PB_USE_DEFAULT_TABLE or __member_num ~= 0 then tb.member_num = __member_num end
    local __team_id = decoder:getu64(13)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    tb.mode_info = pb.pb_MatchModeInfoDecode(decoder:getsubmsg(16))
    tb.mode_info_array = {}
    for k,v in pairs(decoder:getsubmsgary(17)) do
        tb.mode_info_array[k] = pb.pb_MatchModeInfoDecode(v)
    end
    local __PlatID = decoder:geti32(18)
    if not PB_USE_DEFAULT_TABLE or __PlatID ~= 0 then tb.PlatID = __PlatID end
    local __invisible = decoder:getbool(19)
    if not PB_USE_DEFAULT_TABLE or __invisible ~= false then tb.invisible = __invisible end
    return tb
end

function pb.pb_PlayerStateSimpleInfoEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.state) then    encoder:addu32(2, tb.state)    end
    if(tb.fighting_time) then    encoder:addi64(7, tb.fighting_time)    end
    if(tb.member_num) then    encoder:addu32(9, tb.member_num)    end
    if(tb.team_id) then    encoder:addu64(13, tb.team_id)    end
    if(tb.mode_info) then    pb.pb_MatchModeInfoEncode(tb.mode_info, encoder:addsubmsg(16))    end
    if(tb.mode_info_array) then
        for i=1,#(tb.mode_info_array) do
            pb.pb_MatchModeInfoEncode(tb.mode_info_array[i], encoder:addsubmsg(17))
        end
    end
    if(tb.PlatID) then    encoder:addi32(18, tb.PlatID)    end
    if(tb.invisible) then    encoder:addbool(19, tb.invisible)    end
end

