----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGuide)
----- LOG FUNCTION AUTO GENERATE END -----------



local GuideDataBase = require "DFM.Business.Module.GuideModule.Data.GuideDataBase"
local GuideLogic_CheckCondition = require "DFM.Business.Module.GuideModule.GuideLogic_CheckCondition"
local GuideConfig = require "DFM.Business.Module.GuideModule.GuideConfig"
local GuideLogic  = require "DFM.Business.Module.GuideModule.GuideLogic"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local UDFMGuideManager = import "DFMGuideManager"
local EInteractorPanelLayout = import "EInteractorPanelLayout"
local EInteractorType = import "EInteractorType"
local InGameController =  require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"

local function log(...) loginfo("[GuideDataCustom]", ...) end
local function warn(...) logwarning("[GuideDataCustom]", ...) end
local function err(...) logerror("[GuideDataCustom]", ...) end

---@class GuideDataCustom : GuideDataBase
local GuideDataCustom = class("GuideDataCustom", GuideDataBase)

function GuideDataCustom:Ctor()
    self._timerHandler = nil
end

function GuideDataCustom:Destroy()
end

local delimiter = GuideConfig.CustomDataFuncSeparator
function GuideDataCustom:OnStartGuide()
    local ret = 1
    local args = self._guideCfg.Args
    local optArgs = self._guideCfg.OptArgs

    -- number to query the *GuideDataCustomConfig* table for args
    local guideDataCustomConfigIdx = tonumber(args)
    if guideDataCustomConfigIdx ~= nil then
        local guideCustomConfig = GuideConfig.TableGuideCustomConfig[guideDataCustomConfigIdx]
        local funcname = guideCustomConfig.Args[1]
        local params = {}
        for idx, arg in ipairs(guideCustomConfig.Args) do
            if idx ~= 1 then
                table.insert(params, arg)
            end
        end
        ret = GuideDataCustom[funcname](self, params)
    else
        local arr = string.split(args, delimiter)
        assert(arr ~= false, "split failed, maybe args is empty")
        assert(#arr > 0 and arr[1] ~= "", "split failed, maybe args is empty")

        local funcname = arr[1]
        local params = table.pack(select(2, unpack(arr)))

        -- append to the splited params 
        for _, arg in pairs(optArgs) do
            table.insert(params, arg)
        end
        ret = GuideDataCustom[funcname](self, params)
    end
    if not ret then
        ret = 1
    end
    self:EndGuide(ret)
end

------------------------------------
--- 自定义函数，名称要有意义，方便策划配表的时候理解，返回值为执行的下一步分支idx，默认为1
------------------------------------
---- 示例：检查血包数量是否足够，足够的话下一步走分支1，不足的话走分支2
function GuideDataCustom:CheckHealthItemEnough()
    local medItemIdList = GuideConfig.RouletteAddHpMedList
    local result, medItemWrap = GuideLogic_CheckCondition.GetCurHaveMedItemInRoulette(medItemIdList)
    return result and 1 or 2
end

---- 示例：检查护甲数量是否足够，足够的话下一步走分支1，不足的话走分支2
function GuideDataCustom:CheckDefenseItemEnough()
    local medItemIdList = GuideConfig.AmorRepairItemIds;
    local result, medItemWrap = GuideLogic_CheckCondition.GetCurHaveMedItemInRoulette(medItemIdList)
    return result and 1 or 2
end


---判断当前是否有修甲或者修头盔的道具
---@return integer
---1 轮盘有对应的护甲/头修
---2 背包有对应的护甲/头修
---3 什么都没有
function GuideDataCustom:IsPlayerHaveAmorRepairItem()
    if Module.Guide.Field.inGamePlayerArmor < Module.Guide.Field.inGamePlayerMaxArmor then
        if  GuideLogic_CheckCondition.GetCurHaveMedItemInRoulette(GuideConfig.OnlyAmorRepairItemIds) then
            log("have amort repair item in roulette")
            return 1
        end

        for _, item in pairs(GuideConfig.OnlyAmorRepairItemIds) do
            local items = Server.InventoryServer:GetItemsById(item, ESlotGroup.Player)
            if items and #items > 0 then
                log("have amort repair item in bag")
                return 2
            end
        end
    end

    if Module.Guide.Field.inGamePlayerHelmetArmor < Module.Guide.Field.inGamePlayerHelmetMaxArmor  then
        if GuideLogic_CheckCondition.GetCurHaveMedItemInRoulette(GuideConfig.OnlyHelmetAmorRepairItemIds) then
            log("have helmet amort repair item in roulette")
            return 1
        end
        for _,item in pairs(GuideConfig.OnlyHelmetAmorRepairItemIds) do
            local items = Server.InventoryServer:GetItemsById(item,ESlotGroup.Player)
            if items and #items > 0 then
                log("have helmet amort repair item in bag")
                return  2
            end
        end
    end
    log("nothing in both bag and roulette")
    return 3
end

---- 示例：检查维修护甲道具数量是否足够，足够的话下一步走分支1，不足的话走分支2
function GuideDataCustom:CheckOnlyArmorRepairItemEnough()
    local medItemIdList = GuideConfig.OnlyAmorRepairItemIds;
    local result, medItemWrap = GuideLogic_CheckCondition.GetCurHaveMedItemInRoulette(medItemIdList)
    return result and 1 or 2
end

---- 示例：检查维修头盔道具数量是否足够，足够的话下一步走分支1，不足的话走分支2
function GuideDataCustom:CheckOnlyHelmetArmorRepairItemEnough()
    local medItemIdList = GuideConfig.OnlyHelmetAmorRepairItemIds;
    local result, medItemWrap = GuideLogic_CheckCondition.GetCurHaveMedItemInRoulette(medItemIdList)
    return result and 1 or 2
end

---- 示例：检查腿部受伤加血道具数量是否足够，足够的话下一步走分支1，不足的话走分支2
function GuideDataCustom:CheckLegHealthItemEnough()
    local medItemIdList = GuideConfig.RouletteLegAddHpMedList
    local result, medItemWrap = GuideLogic_CheckCondition.GetCurHaveMedItemInRoulette(medItemIdList)
    return result and 1 or 2
end

---- 示例：检查手部受伤加血道具数量是否足够，足够的话下一步走分支1，不足的话走分支2
function GuideDataCustom:CheckHandHealthItemEnough()
    local medItemIdList = GuideConfig.RouletteHandAddHpMedList
    local result, medItemWrap = GuideLogic_CheckCondition.GetCurHaveMedItemInRoulette(medItemIdList)
    return result and 1 or 2
end

---- 示例：检查头部受伤加血道具数量是否足够，足够的话下一步走分支1，不足的话走分支2
function GuideDataCustom:CheckHeadHealthItemEnough()
    local medItemIdList = GuideConfig.RouletteHeadAddHpMedList
    local result, medItemWrap = GuideLogic_CheckCondition.GetCurHaveMedItemInRoulette(medItemIdList)
    return result and 1 or 2
end

---- 示例：检查胸部受伤加血道具数量是否足够，足够的话下一步走分支1，不足的话走分支2
function GuideDataCustom:CheckChestHealthItemEnough()
    local medItemIdList = GuideConfig.RouletteChestAddHpMedList
    local result, medItemWrap = GuideLogic_CheckCondition.GetCurHaveMedItemInRoulette(medItemIdList)
    return result and 1 or 2
end

---- 设置当前药品轮盘为加血道具（按指定顺序检查，先检查到的推荐）
function GuideDataCustom:SetRooleteeMedItemAddHp()
    local medItemIdList = GuideConfig.RouletteAddHpMedList
    local result, medItemWrap = GuideLogic_CheckCondition.GetCurHaveMedItemInRoulette(medItemIdList)
    if medItemWrap then
        Module.HUDToolBar:SetCurSelectMedkitItemWrap(medItemWrap)
    end
    return 1
end

---- 设置当前药品轮盘为护甲道具（按指定顺序检查，先检查到的推荐）
function GuideDataCustom:SetRooleteeMedItemAddDEF()
    local medItemIdList = GuideConfig.RouletteAddDEFMedList
    local result, medItemWrap = GuideLogic_CheckCondition.GetCurHaveMedItemInRoulette(medItemIdList)
    if medItemWrap then
        Module.HUDToolBar:SetCurSelectMedkitItemWrap(medItemWrap)
    end
    return 1
end

---- 设置当前药品轮盘为加护甲耐久道具（按指定顺序检查，先检查到的推荐）
function GuideDataCustom:SetRooleteeMedItemAddArmor()
    local medItemIdList = GuideConfig.OnlyAmorRepairItemIds
    local result, medItemWrap = GuideLogic_CheckCondition.GetCurHaveMedItemInRoulette(medItemIdList)
    if medItemWrap then
        Module.HUDToolBar:SetCurSelectMedkitItemWrap(medItemWrap)
    end
    return 1
end

---- 设置当前药品轮盘为加头盔耐久道具（按指定顺序检查，先检查到的推荐）
function GuideDataCustom:SetRooleteeMedItemAddHelmetArmor()
    local medItemIdList = GuideConfig.OnlyHelmetAmorRepairItemIds
    local result, medItemWrap = GuideLogic_CheckCondition.GetCurHaveMedItemInRoulette(medItemIdList)
    if medItemWrap then
        Module.HUDToolBar:SetCurSelectMedkitItemWrap(medItemWrap)
    end
    return 1
end

---- 设置当前药品轮盘为腿部加血道具（按指定顺序检查，先检查到的推荐）
function GuideDataCustom:SetRooleteeMedItemLegAddHp()
    local medItemIdList = GuideConfig.RouletteLegAddHpMedList
    local result, medItemWrap = GuideLogic_CheckCondition.GetCurHaveMedItemInRoulette(medItemIdList)
    if medItemWrap then
        Module.HUDToolBar:SetCurSelectMedkitItemWrap(medItemWrap)
    end
    return 1
end

---- 设置当前药品轮盘为手部加血道具（按指定顺序检查，先检查到的推荐）
function GuideDataCustom:SetRooleteeMedItemHandAddHp()
    local medItemIdList = GuideConfig.RouletteHandAddHpMedList
    local result, medItemWrap = GuideLogic_CheckCondition.GetCurHaveMedItemInRoulette(medItemIdList)
    if medItemWrap then
        Module.HUDToolBar:SetCurSelectMedkitItemWrap(medItemWrap)
    end
    return 1
end

---- 设置当前药品轮盘为头部加血道具（按指定顺序检查，先检查到的推荐）
function GuideDataCustom:SetRooleteeMedItemHeadAddHp()
    local medItemIdList = GuideConfig.RouletteHeadAddHpMedList
    local result, medItemWrap = GuideLogic_CheckCondition.GetCurHaveMedItemInRoulette(medItemIdList)
    if medItemWrap then
        Module.HUDToolBar:SetCurSelectMedkitItemWrap(medItemWrap)
    end
    return 1
end

---- 设置当前药品轮盘为胸部加血道具（按指定顺序检查，先检查到的推荐）
function GuideDataCustom:SetRooleteeMedItemChestAddHp()
    local medItemIdList = GuideConfig.RouletteChestAddHpMedList
    local result, medItemWrap = GuideLogic_CheckCondition.GetCurHaveMedItemInRoulette(medItemIdList)
    if medItemWrap then
        Module.HUDToolBar:SetCurSelectMedkitItemWrap(medItemWrap)
    end
    return 1
end


--- 是否有足够的道具来清除/抑制负面状态
---@return 1|2 有|没有
function GuideDataCustom:IsResidentDeBuffGameItemEnough()
    local medIds = GuideConfig.MedOperationItems
    local ret  = GuideLogic_CheckCondition.GetCurHaveMedItemInRoulette(medIds)
    return ret and 1 or 2
end

--- 设置当前药品轮盘为清除负面状态道具（按指定顺序检查，先检查到的推荐）
function GuideDataCustom:SetRooleteeMedItemClearDeBuff()
    local medItemIdList = GuideConfig.MedOperationItems
    local result, medItemWrap = GuideLogic_CheckCondition.GetCurHaveMedItemInRoulette(medItemIdList)
    if medItemWrap then
        Module.HUDToolBar:SetCurSelectMedkitItemWrap(medItemWrap)
    end
    return 1
end

-- 关闭轮盘药品列表
function GuideDataCustom:HideRouletteMedList()
    Module.HUDToolBar:HideRouletteMedList()
    return 1
end

---- 检查是否存在looting drag的ui
function GuideDataCustom:CheckHaveDragLootingUI()
    local result = Module.Guide.Field:GetProxyWidget(GuideConfig.EGuideProxyWidget.guideProxyLootingDrag)
    return result ~= nil and 1 or 2
end

---- 检查是否存在looting doubleClick的ui
function GuideDataCustom:CheckHaveDoubleClickLootingUI()
    local result = Module.Guide.Field:GetProxyWidget(GuideConfig.EGuideProxyWidget.guideProxyLootingDoubleClick)
    return result ~= nil and 1 or 2
end

---- 检查是否存在手术包
function GuideDataCustom:CheckHaveAiLootingUI()
    local result = Module.Guide.Field:GetProxyWidget(GuideConfig.EGuideProxyWidget.guideProxyLootingAI)
    return result ~= nil and 1 or 2
end

---- 检查是否存在最高价值的ui
function GuideDataCustom:CheckHaveMostValueLootingUI()
    local result = Module.Guide.Field:GetProxyWidget(GuideConfig.EGuideProxyWidget.guideProxyLootingMostValueItem)
    return result ~= nil and 1 or 2
end

---- 3d安全屋ui是否已经打开
function GuideDataCustom:CheckSafeHouseHUDOpenFinish()
    local GuideLogic = require "DFM.Business.Module.GuideModule.GuideLogic"
    local hudUIWidget
    if DFHD_LUA == 1 then
        hudUIWidget = GuideLogic.GetWidgetByPath(nil, "wtBattleMainPanel", "IrisWorldEntryMainPanel")
    else
        hudUIWidget = GuideLogic.GetWidgetByPath(nil, "wtBattleMainPanel", "IrisWorldEntryMainPanel")
    end
    if hudUIWidget then
        return 1
    else
        return 2
    end
end

-- 是否topbar准备完毕
-- 1 准备完毕
-- 2 没有准备完毕
function GuideDataCustom:CheckTopbarEnable()
    if GuideLogic.IsTopbarEnable() then
        return 1
    else
        return 2
    end
end

-- 是否在不可以启动引导的sub stage
-- 1 在 不可以启动引导的stage
-- 2 不在
function GuideDataCustom:CheckInSubStageCantGuide()
    if GuideLogic.IsInSubStage3dSafehouse() or GuideLogic.IsInSubStageRange() then
        return 1
    end
    if Module.IrisSafeHouse:IsEnteringSafeHouse() then
        loginfo("GuideDataCustom:CheckInSubStageCantGuide, EnteringSafeHouse")
        return 1
    end
    return 2
end

---- 检查是否在等待服务器更新引导阶段id
function GuideDataCustom:CheckIsWaitServerUpdateGuideStage()
    return Server.GuideServer:IsWaitServerUpdateGuideStage() and 1 or 2
end

---- 撤离点引导打开小地图时设置：禁用小地图滑动，设置小地图缩放比例最小
function GuideDataCustom:EnableBigMapSpecStateForGuide()
    local UDFMHudHelper = import "DFMHudHelper"
    local world = GetWorld()
    UDFMHudHelper.ReSetBigmapScale(world)
    UDFMHudHelper.EnableBigmapInput(world, false)
    UDFMHudHelper.SetBigMapFocusCenterUV(world, FVector2D(0, 0))

    Module.Guide.Field.bSetBigMapInput = true
    return 1
end

---- 撤离点引导结束小地图时设置：启用小地图滑动
function GuideDataCustom:DisableBigMapSpecStateForGuide()
    Module.Guide.Field.bSetBigMapInput = false
    local UDFMHudHelper = import "DFMHudHelper"
    local world = GetWorld()
    UDFMHudHelper.EnableBigmapInput(world, true)
    return 1
end

---- 帮玩家选图、设置配装完成
function GuideDataCustom:AutoReadyMatch()
    local gameModeServer = Server.GameModeServer
    local ret, targetMatchMode = Server.GameModeServer:GenMatchModeByWorldEntrancCfg(2, MatchSubMode.SOLPMC)
    gameModeServer:SetMatchID(0)
    gameModeServer:SetMatchMode(targetMatchMode)
    gameModeServer:SetMatchModes({ targetMatchMode })
    Module.ArmedForce:MarkOutfitFlowFlag()
    return 1
end

---- 是否存在撤离失败的上一局
function GuideDataCustom:CheckLastGameIsFail()
    return Server.GuideServer:IsLastSolGameResultFail() and 2 or 1
end

---- 新手引导指定的任务是否领取
function GuideDataCustom:CheckQuestIsGet(params)
    --if true then return 1 end
    local questId
    if #params > 0 then
        questId = tonumber(params[1])
    else
        questId = GuideConfig.NewPlayerGuideSpecItem.questItem
    end
    local specGuideItemInfo = Module.Guide:GetNewPlayerGuideItemInfo(questId)
    local result = false
    if specGuideItemInfo then
        result = Server.QuestServer:IsQuestAccepted(tonumber(specGuideItemInfo[1]))
    end
    return result and 1 or 2
end

---- 新手引导指定的任务是否领取奖励
function GuideDataCustom:CheckQuestIsRewarded(params)
    --if true then return 1 end
    if #params <= 0 then return 3 end
    local questId = tonumber(params[1])
    local specGuideItemInfo = Module.Guide:GetNewPlayerGuideItemInfo(questId)
    local result = false
    if specGuideItemInfo then
        result = Server.QuestServer:IsQuestRewarded(questId)
    end
    return result and 1 or 2
end

---- 新手引导指定的任务是否可以领取奖励
function GuideDataCustom:CheckQuestIsCanReward(params)
    --if true then return 1 end
    if #params <= 0 then return 3 end
    local ItemConfigId = tonumber(params[1])
    local specGuideItemInfo = Module.Guide:GetNewPlayerGuideItemInfo(ItemConfigId)
    local result = false
    if specGuideItemInfo then
        local questId = tonumber(specGuideItemInfo[1])
        result = not Server.QuestServer:IsQuestRewarded(questId) and Server.QuestServer:IsQuestCompleted({questId})
    end
    return result and 1 or 2
end

---- 关闭栈ui
function GuideDataCustom:CloseAllStackUI()
    -- local syncUIInst = Facade.UIManager:GetStackUIByUINavId(UIName2ID.StarLinkView)
    -- if syncUIInst then
    --     Module.CommonBar:SetUseStarAppState(false, false)
    -- end
    Facade.UIManager:PopAllUI(false, false, MapPopAllUIReason2Str.BackActionToLobby)
    Facade.UIManager:CloseAllPopUI()
    return 1
end

-- TODO
---- 打开战略板，跳到指定地图
function GuideDataCustom:AutoOpenSadboxAndSelectGameMode()
    return 1
end

---- 注册仓库带药引导中的控件
function GuideDataCustom:TryAddGuideWidgetWareHouseItem(args)
    GuideLogic.RemoveGuideWidgetProxy(GuideConfig.EGuideProxyWidget.guideProxyInventoryItem)
    local itemIdList = Module.Guide:GetNewPlayerGuideItemInfo(GuideConfig.NewPlayerGuideSpecItem.invItem)
    local itemId = itemIdList[tonumber(args[1])]
    if not itemId then return 2 end
    itemId = tonumber(itemId)
    local inventorySlotView = GuideLogic.getWidgetFuncWarehouseSlotView()
    if not inventorySlotView then return 2 end
    --local itemViews = inventorySlotView:GetAllItemViews()
    local allItems = inventorySlotView:GetAllItems()
    for _, item in ipairs(allItems) do
        if item.id == itemId then
            if self:_WarehouseMoveByItem(item) then
                local uiIns = inventorySlotView:GetViewByItem(item)
                if uiIns then
                    -- uiIns:SetCppValue("bHandleDrag", false) -- 禁用一下拖动
                    uiIns:SetHandleDrag(false)
                    uiIns:SetHandleDoubleClick(false)
                    Module.CommonWidget:SetFlagDisableOpForGuide(true)
                    GuideLogic.AddGuideWidgetProxy(GuideConfig.EGuideProxyWidget.guideProxyInventoryItem, uiIns)
                    return 1
                else
                    return 3
                end
            else
                return 2
            end
        end
    end
    return 2
end

---- 重新注册仓库带药引导中的控件
--- 1 注册成功
--- 2 注册失败
function GuideDataCustom:TryAddGuideWidgetWareHouseItemAgain(args)
    local itemIdList = Module.Guide:GetNewPlayerGuideItemInfo(GuideConfig.NewPlayerGuideSpecItem.invItem)
    local itemId = itemIdList[tonumber(args[1])]
    if not itemId then return 2 end
    itemId = tonumber(itemId)
    local inventorySlotView = GuideLogic.getWidgetFuncWarehouseSlotView()
    if not inventorySlotView then return 2 end
    --local itemViews = inventorySlotView:GetAllItemViews()
    local allItems = inventorySlotView:GetAllItems()
    for _, item in ipairs(allItems) do
        if item.id == itemId then
            local uiIns = inventorySlotView:GetViewByItem(item)
            if uiIns then
                -- uiIns:SetCppValue("bHandleDrag", false) -- 禁用一下拖动
                uiIns:SetHandleDrag(false)
                uiIns:SetHandleDoubleClick(false)
                Module.CommonWidget:SetFlagDisableOpForGuide(true)
                GuideLogic.AddGuideWidgetProxy(GuideConfig.EGuideProxyWidget.guideProxyInventoryItem, uiIns)
                return 1
            end
        end
    end
    return 2
end

---- 注册looting引导子弹控件
function GuideDataCustom:CheckIsLootingSearch(args)
    local lootingUI = Module.Looting:GetMainPanel()
    if not lootingUI then
        err("looting ui is nil")
        return 2
    end
    local targetUI = lootingUI._wtLootDeadbodyPanel
    if not targetUI then
        err("_wtLootDeadbodyPanel ui is nil")
        return 2
    end
    targetUI = targetUI._wtDeadbodyContainerPanel
    if not targetUI then
        err("_wtDeadbodyContainerPanel ui is nil")
        return 2
    end

    local tryFind = function(widgetName)
        local targetUITemp = targetUI[widgetName]
        if not targetUITemp then
            err(widgetName, "_wtCHContainerView ui is nil")
            return 2
        end
        targetUITemp = targetUITemp._wtContainerSlotView
        if not targetUITemp then
            err("_wtContainerSlotView ui is nil")
            return 2
        end

        local allItemViews = targetUITemp:GetAllItemViews()
        for item, view in pairs(allItemViews) do
            if view._viewSearchState ~= 0 then
                return 1
            --else
            --    if item:IsBullet() then
            --        return 2
            --    end
            end
        end
        return 2
    end

    if tryFind("_wtCHContainerView") == 1 then
        return 1
    --elseif tryFind("wtPocketContainerView") == 1 then
    --    return 1
    --elseif tryFind("wtBagContainerView") == 1 then
    --    return 1
    --elseif tryFind("wtSafeBoxContainerView") == 1 then
    --    return 1
    end

    return 2
end

---- 注册looting引导子弹控件
function GuideDataCustom:TryAddGuideWidgetLootingItem(args)
    GuideLogic.RemoveGuideWidgetProxy(GuideConfig.EGuideProxyWidget.guideProxyLootingAI)

    local lootingUI = Module.Looting:GetMainPanel()
    if not lootingUI then
        err("looting ui is nil")
        return 2
    end
    local targetUI
    if IsHD() then
        targetUI = lootingUI.wtLootDeadbodyPanel
        if not targetUI then
            err("_wtLootDeadbodyPanel ui is nil")
            return 2
        end
        targetUI = targetUI._wtCHContainerView
        if not targetUI then
            err("_wtCHContainerView ui is nil")
            return 2
        end
        targetUI = targetUI._wtContainerSlotView
        if not targetUI then
            err("_wtContainerSlotView ui is nil")
            return 2
        end
    else
        targetUI = lootingUI:GetCurrentSubView()
        if not targetUI then
            err("_currentSubView ui is nil")
            return 2
        end
        targetUI = targetUI._wtLootDeadbodyPanel
        if not targetUI then
            err("_wtLootDeadbodyPanel ui is nil")
            return 2
        end
        -- targetUI = targetUI._wtDeadbodyContainerPanel
        -- if not targetUI then
        --     err("_wtDeadbodyContainerPanel ui is nil")
        --     return 2
        -- end
        targetUI = targetUI._wtCHContainerView
        if not targetUI then
            err("_wtCHContainerView ui is nil")
            return 2
        end
        targetUI = targetUI.wtContainerSlotView
        if not targetUI then
            err("wtContainerSlotView ui is nil")
            return 2
        end
    end

    local allItemViews = targetUI:GetAllItemViews()
    for item, view in pairs(allItemViews) do
        if item:IsBullet() then
            GuideLogic.AddGuideWidgetProxy(GuideConfig.EGuideProxyWidget.guideProxyLootingAI, view)
            return 1
        end
    end

    err("Still cannot find target item bullet!!")

    return 2
end


function GuideDataCustom.GetLootingDeadBodyPanelHD()
    return Module.Looting:GetMainPanel().wtLootDeadbodyPanel
end

function GuideDataCustom:TryAddGuideWidgetLootingLeftWeapon()
    GuideLogic.RemoveGuideWidgetProxy(GuideConfig.EGuideProxyWidget.guideProxyLootingLeftWeapon)

    local targetUI = GuideDataCustom.GetLootingDeadBodyPanelHD()
    if not targetUI then
        err("_wtLootDeadbodyPanel ui is nil")
        return 2
    end

    if IsHD() then
        targetUI = targetUI._wtDeadbodyEquipPanel
        if not targetUI then
            err("_wtDeadbodyEquipPanel ui is nil")
            return 2
        end
        targetUI = targetUI._wtLeftWeaponSlotView
        if not targetUI then
            err("_wtLeftWeaponSlotView ui is nil")
            return 2
        end
        targetUI = targetUI._wtForGuide
        if not targetUI then
            err("_wtForGuide ui is nil")
            return 2
        end
    end

    if not targetUI then
        err("faield to find left weapon ui")
        return 2
    end

    GuideLogic.AddGuideWidgetProxy(GuideConfig.EGuideProxyWidget.guideProxyLootingLeftWeapon, targetUI)
    return 1
end


function GuideDataCustom:TryMoveItemToInventory(args)
    if not args or #args < 1 then
        -- 参数错误
        return 3
    end

    local itemIdList = Module.Guide:GetNewPlayerGuideItemInfo(GuideConfig.NewPlayerGuideSpecItem.invItem)
    local itemId = tonumber(itemIdList[tonumber(args[1])])

    local allMatchItems = Server.InventoryServer:GetItemsById(itemId)
    local targetGroup = ESlotGroup.Player
    local targetSlot = Server.InventoryServer:GetSlot(ESlotType.MainContainer, targetGroup)
    local bMoveNum = 0
    for _, item in pairs(allMatchItems) do
        local oldSlotType = item.InSlot.SlotType
        if oldSlotType ~= ESlotType.MainContainer then
            local ret = ItemOperaTool.DoPlaceItem(item, targetSlot)
            if not ret then
                return 3
            end
            bMoveNum = bMoveNum + 1
        end
    end

    log("TryMoveItemToInventory", itemId, bMoveNum, #allMatchItems)
    if bMoveNum == 0 then
        -- 没有需要转移的
        return 2
    end
    -- 转移成功
    return 1
end

-- TODO
---- 判断仓库有没有药，背包装没装药
function GuideDataCustom:CheckInvHaveItemAndBagNotHaveItem()
    return 1
end

---- 判断玩家当前配装是否带了两把枪
function GuideDataCustom:CheckHaveTwoWeapon()
    local ret = Module.ArmedForce:GetEquipWeaponCount()
    -- 枪不够两把
    if ret ~= 2 then
        return 2
    end

    -- 判断是否有空间能卸下副武器的枪
    local slot2 = Server.InventoryServer:GetSlot(ESlotType.MainWeaponRight, ESlotGroup.Player)
    if not slot2 then
        return 2
    end
    local item = slot2:GetEquipItem()
    if not item then
        return 2
    end
    local targetSlot = Server.InventoryServer:GetItemAvaiableDepositSlot(item)
    if not targetSlot then
        return 2
    end

    return ret == 2 and 1 or 2
end

---- 判断玩家当前主武器槽位是否有枪
function GuideDataCustom:CheckHaveWeaponMainSlot()
    local ret = Module.ArmedForce:GetEquipWeaponCount({ESlotType.MainWeaponLeft})
    return ret == 1 and 1 or 2
end

---- 判断玩家当前副武器槽位是否有枪
function GuideDataCustom:CheckHaveWeaponSubSlot()
    local ret = Module.ArmedForce:GetEquipWeaponCount({ESlotType.MainWeaponRight})
    return ret == 1 and 1 or 2
end

---- 判断玩家当前是否没带枪
function GuideDataCustom:CheckNotHaveWeapon()
    local ret = Module.ArmedForce:GetEquipWeaponCount()
    return ret == 0 and 1 or 2
end

---- 判断玩家当前仓库有枪可以装备
--1 有
--2 没有
function GuideDataCustom:CheckHaveGunInInventory()
    -- 仓库道具处理
    local ItemMatchConditionTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemMatchConditionTool"
    local itemSlot = Server.InventoryServer:GetSlot(111, ESlotGroup.Player)
    for _, item in Server.InventoryServer:GetItemsIterator(ESlotGroup.Player) do
        if ItemMatchConditionTool.CheckItemMatchSOLCondition(item, itemSlot, 0) then
            return 1
        end
    end
    return 2
end

---- 获取当前商人列表ui
function GuideDataCustom:GetCurShopListUI()
    local ret = GuideLogic.getWidgetFuncCurShopList()
    return 1
end

---- 判断当前是否有扩容箱槽位
function GuideDataCustom:CheckCurHaveDepositSlot()
    local result = Module.Guide.Field:GetProxyWidget(GuideConfig.EGuideProxyWidget.guideProxyWarehouseExpansion)
    return result ~= nil and 1 or 2
end

---- 播放奖励弹窗
function GuideDataCustom:ShowGetRewardUI(params)
    local itemList = {}
    for _, itemIdStr in ipairs(params) do
        local itemId = tonumber(itemIdStr)
        local itemStructs = Server.InventoryServer:GetItemsById(itemId)
        if #itemStructs > 0 then
            table.insert(itemList, itemStructs[1])
        else
            logerror("GuideDataCustom:ShowGetRewardUI, item is nil", itemId, params)
        end
    end
    if #itemList > 0 then
        Module.Reward:OpenRewardPanel(GuideConfig.Loc.getReward, nil, itemList)
    else
        logerror("GuideDataCustom:ShowGetRewardUI, itemList is nil", params)
    end
    return 1
end

---- 播放撤离失败保底返还的内容
function GuideDataCustom:ShowFailRebackUI(params)
    local itemList = Server.GuideServer:GetLastGetItemList()
    -- for _, itemId in ipairs(itemIdList) do
    --     local itemStructs = Server.InventoryServer:GetItemsById(itemId)
    --     if #itemStructs > 0 then
    --         table.insert(itemList, itemStructs[1])
    --     else
    --         logerror("GuideDataCustom:ShowFailRebackUI, item is nil", itemId)
    --     end
    -- end
    if #itemList > 0 then
        local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
        local sortFunc1 = function(item1, item2)
            local itemMainType1 = ItemHelperTool.GetMainTypeById(item1.id)
            local itemMainType2 = ItemHelperTool.GetMainTypeById(item2.id)
            local itemSubType1 = ItemHelperTool.GetSubTypeById(item1.id)
            local itemSubType2 = ItemHelperTool.GetSubTypeById(item2.id)
            -- 武器
            if (itemMainType1 == EItemType.Weapon or itemMainType1 == EItemType.Receiver or itemMainType1 == EItemType.PoorWeapon) and
                (itemMainType2 == EItemType.Weapon or itemMainType2 == EItemType.Receiver or itemMainType2 == EItemType.PoorWeapon) then
                return item1.id < item2.id
            end
            if itemMainType1 == EItemType.Weapon or itemMainType1 == EItemType.Receiver or itemMainType1 == EItemType.PoorWeapon then
                return true
            end
            if itemMainType2 == EItemType.Weapon or itemMainType2 == EItemType.Receiver or itemMainType2 == EItemType.PoorWeapon then
                return false
            end
            -- 装备
            if itemMainType1 == EItemType.Equipment and itemMainType2 == EItemType.Equipment then
                return itemSubType1 < itemSubType2
            end
            if itemMainType1 == EItemType.Equipment then
                return true
            end
            if itemMainType2 == EItemType.Equipment then
                return false
            end
            -- 子弹
            if itemMainType1 == EItemType.Bullet and itemMainType2 == EItemType.Bullet then
                return item1.id < item2.id
            end
            if itemMainType1 == EItemType.Bullet then
                return true
            end
            if itemMainType2 == EItemType.Bullet then
                return false
            end
            -- 药品
            if itemMainType1 == EItemType.Medicine and itemMainType2 == EItemType.Medicine then
                return item1.id < item2.id
            end
            if itemMainType1 == EItemType.Medicine then
                return true
            end
            if itemMainType2 == EItemType.Medicine then
                return false
            end
            return item1.id < item2.id
        end
        table.sort(itemList, sortFunc1)

        -- 子弹的绑定状态刷新成非绑定
        for _, item in pairs(itemList) do
            if item:GetFeature(EFeatureType.Bullet) then
                item:ForceSetBindType(PropBindingType.BindingNotBind)
            end
        end

        Module.Reward:OpenRewardPanel(GuideConfig.Loc.getReward, GuideConfig.Loc.guideSolFailFirstGiveEuipDesc, itemList, nil, false, true)
    else
        logerror("GuideDataCustom:ShowFailRebackUI, itemList is nil")
    end
    return 1
end

---- 播放撤离失败保底返还的内容，非首次失败
function GuideDataCustom:ShowFailRebackUIWithNotFirst(params)
    local itemList = Server.GuideServer:GetLastGetItemList()
    if #itemList > 0 then
        local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
        local sortFunc1 = function(item1, item2)
            local itemMainType1 = ItemHelperTool.GetMainTypeById(item1.id)
            local itemMainType2 = ItemHelperTool.GetMainTypeById(item2.id)
            local itemSubType1 = ItemHelperTool.GetSubTypeById(item1.id)
            local itemSubType2 = ItemHelperTool.GetSubTypeById(item2.id)
            -- 武器
            if (itemMainType1 == EItemType.Weapon or itemMainType1 == EItemType.Receiver or itemMainType1 == EItemType.PoorWeapon) and
                (itemMainType2 == EItemType.Weapon or itemMainType2 == EItemType.Receiver or itemMainType2 == EItemType.PoorWeapon) then
                return item1.id < item2.id
            end
            if itemMainType1 == EItemType.Weapon or itemMainType1 == EItemType.Receiver or itemMainType1 == EItemType.PoorWeapon then
                return true
            end
            if itemMainType2 == EItemType.Weapon or itemMainType2 == EItemType.Receiver or itemMainType2 == EItemType.PoorWeapon then
                return false
            end
            -- 装备
            if itemMainType1 == EItemType.Equipment and itemMainType2 == EItemType.Equipment then
                return itemSubType1 < itemSubType2
            end
            if itemMainType1 == EItemType.Equipment then
                return true
            end
            if itemMainType2 == EItemType.Equipment then
                return false
            end
            -- 子弹
            if itemMainType1 == EItemType.Bullet and itemMainType2 == EItemType.Bullet then
                return item1.id < item2.id
            end
            if itemMainType1 == EItemType.Bullet then
                return true
            end
            if itemMainType2 == EItemType.Bullet then
                return false
            end
            -- 药品
            if itemMainType1 == EItemType.Medicine and itemMainType2 == EItemType.Medicine then
                return item1.id < item2.id
            end
            if itemMainType1 == EItemType.Medicine then
                return true
            end
            if itemMainType2 == EItemType.Medicine then
                return false
            end
            return item1.id < item2.id
        end
        table.sort(itemList, sortFunc1)

        -- 子弹的绑定状态刷新成非绑定
        for _, item in pairs(itemList) do
            if item:GetFeature(EFeatureType.Bullet) then
                item:ForceSetBindType(PropBindingType.BindingNotBind)
            end
        end

        Module.Reward:OpenRewardPanel(GuideConfig.Loc.guideSolFailGiveEuipTitle, GuideConfig.Loc.guideSolFailGiveEuipDesc, itemList, nil, false, true)
    else
        logerror("GuideDataCustom:ShowFailRebackUIWithNotFirst, itemList is nil")
    end
    return 1
end

---- 高亮仓库装备槽位
function GuideDataCustom:WarehouseHightlightEquipPart(params)
    local equipPanel = GuideLogic.getWidgetFuncWarehouseEquipPanel()
    if not equipPanel then return 1 end
    equipPanel:GuideHighlightEquipPart()
    return 1
end

---- 高亮仓库背包槽位
function GuideDataCustom:WarehouseHightlightBagPart(params)
    local equipPanel = GuideLogic.getWidgetFuncWarehouseEquipPanel()
    if not equipPanel then return 1 end
    equipPanel:GuideHighlightBagPart()
    return 1
end

---- 停止仓库左侧高亮
function GuideDataCustom:StopWarehouseLeftHighlight(params)
    local equipPanel = GuideLogic.getWidgetFuncWarehouseEquipPanel()
    if not equipPanel then return 1 end
    equipPanel:GuideStopHighlight()
    return 1
end

---- 仓库左侧移动至背包位置
function GuideDataCustom:WarehouseMoveToBagPart(params)
    local equipPanel = GuideLogic.getWidgetFuncWarehouseEquipPanel()
    if not equipPanel then return 1 end
    equipPanel:GuideMoveToBagPart()
    return 1
end

---- 仓库右侧移动至指定道具位置
function GuideDataCustom:_WarehouseMoveByItem(item)
    local slotView = GuideLogic.getWidgetFuncWarehouseSlotView()
    if not slotView then return false end

    --BEGIN MODIFICATION @ VIRTUOS : 关闭交易行引导后，也临时关闭移动到道具位置
    if DFCONSOLE_LUA == 1 then
        return true
    end
    --END MODIFICATION

    slotView:ScrollToItem(item, true, nil, true)
    return true
end

---- 锁定仓库的滚动 和item拖拽/双击操作
function GuideDataCustom:SetWareHouseScrollDisable()
    local slotView = GuideLogic.getWidgetFuncWarehouseWidget()
    if not slotView then return 2 end

    --BEGIN MODIFICATION @ VIRTUOS : 关闭交易行引导后，也临时跳过锁定逻辑
    if DFCONSOLE_LUA == 1 then
        return 1
    end
    --END MODIFICATION

    slotView:SetScrollEnable(false)
    Module.CommonWidget:SetDisableHover(true)
    Module.CommonWidget:SetFlagDisableOpForGuide(true)
    return 1
end

---- 解除锁定仓库的滚动 和item拖拽/双击操作
function GuideDataCustom:SetWareHouseScrollEnable()
    local slotView = GuideLogic.getWidgetFuncWarehouseWidget()
    if not slotView then return 2 end

    --BEGIN MODIFICATION @ VIRTUOS : 关闭交易行引导后，也临时跳过解锁逻辑
    if DFCONSOLE_LUA == 1 then
        return 1
    end
    --END MODIFICATION
    slotView:SetScrollEnable(true)
    Module.CommonWidget:SetDisableHover(false)
    Module.CommonWidget:SetFlagDisableOpForGuide(false)
    return 1
end


function GuideDataCustom.Set_SolSettlementSelf_EquipmentPanel_ScrollEnableState(bEnable)
    if IsHD() then
        -- TODO
        assert(false)
        return false
    else
        local targetUI = Facade.UIManager:GetStackUIByUINavId(UIName2ID.EvacuatePrivateTrophyInfoView)
        if targetUI then
            local equipPanel = targetUI:GeteEquipmentPanle()
            if equipPanel then
                equipPanel:SetScrollEnable(bEnable)
                return true
            end
        end
    end

    return false
end

function GuideDataCustom:DisableSettlementSelfEquipmentPnaelScroll()
    local ret = self.Set_SolSettlementSelf_EquipmentPanel_ScrollEnableState(false)
    return ret and 1 or 2
end

function GuideDataCustom:EnableSettlementSelfEquipmentPnaelScroll()
    local ret = self.Set_SolSettlementSelf_EquipmentPanel_ScrollEnableState(true)
    return ret and 1 or 2
end



---- 设置使用新手引导高亮
function GuideDataCustom:WarehouseSetPlayGuideHighlightForItemMove(params)
    local invSlotView = GuideLogic.getWidgetFuncWarehouseSlotView()
    if not invSlotView then return 1 end
    invSlotView:SetPlayGuideHighlightForItemMove(params[1] == "1" and true or false)
    return 1
end

---- 停止当前所有道具的高亮
function GuideDataCustom:StopAllItemsHighlight(params)
    local invSlotView = GuideLogic.getWidgetFuncWarehouseSlotView()
    if not invSlotView then return 1 end
    invSlotView:StopAllItemsHighlight()
    return 1
end

---- 打开指定商品对应的商人，并滑倒商品位置，不选中
function GuideDataCustom:ShowShopUIAndNotSelectByExchangeId(params)
    local guideItemCfgId = tonumber(params[1])
    local specGuideItemInfo = Module.Guide:GetNewPlayerGuideItemInfo(guideItemCfgId)
    if not specGuideItemInfo then return 2 end
    local itemId = tonumber(specGuideItemInfo[1])
    local shopData = Module.Shop:GetJumpBuyDataById(itemId)
    if shopData then
        local _, ret = Module.Shop:ShowMainPanelByExchangeId(shopData.exchangeId, false)
        if ret == 0 then
            return 1
        end
    end
    --Module.Shop:ShowMainPanelByExchangeId(exchangeId, false)
    return 2
end

---- 发起新手局
function GuideDataCustom:StartNewPlayerMatch(params)
    local gameModeServer = Server.GameModeServer
    local ret, targetMatchMode = Server.GameModeServer:GenMatchModeByWorldEntrancCfg(2, MatchSubMode.SOLPMC)
    gameModeServer:SetMatchID(0)
    gameModeServer:SetMatchMode(targetMatchMode)
    gameModeServer:SetMatchModes({ targetMatchMode })
    Module.ArmedForce:MarkOutfitFlowFlag()

    Module.Guide:ResetNewPlayerMatchGuideVariables()
    Server.GuideServer:BeginNewPlayerMatch(function()
        -- clean forrest
        GuideDataCustom:CleanStartNewPlayerMatchState()
    end)
    --Module.Guide:ResetNewPlayerMatchGuideCount()
    --Server.GuideServer:BeginNewPlayerMatch()
    return 1
end

function GuideDataCustom:CleanStartNewPlayerMatchState()
    -- gameModeServer:SetMatchID(0)
    loginfo("GuideDataCustom:CleanStartNewPlayerMatchState")
    Server.GameModeServer:ResetMatchMode()
    Module.ArmedForce:ClearOutfitFlowFlag()
end

---- 发起尖叫小关局
function GuideDataCustom:StartMiniGameMatch(params)
    Server.GuideServer:BeginMiniGameMatch()
    return 1
end

---- 检查玩家护甲是否小于某个比例
function GuideDataCustom:CheckPlayerArmorLessPercent(params)
    local checkPercent = tonumber(params[1])
    local guideField = Module.Guide.Field
    local curValue = guideField.inGamePlayerArmor
    local maxValue = guideField.inGamePlayerMaxArmor
    if maxValue == 0 then
        return 2
    end
    loginfo("GuideDataCustom:CheckPlayerArmorLessPercent", curValue, maxValue, curValue / maxValue)
    return curValue / maxValue <= checkPercent and 1 or 2
end

---- 检查玩家头盔耐久是否小于某个比例
function GuideDataCustom:CheckPlayerHelmetArmorLessPercent(params)
    local checkPercent = tonumber(params[1])
    local guideField = Module.Guide.Field
    local curValue = guideField.inGamePlayerHelmetArmor
    local maxValue = guideField.inGamePlayerHelmetMaxArmor
    if maxValue == 0 then
        return 2
    end
    loginfo("GuideDataCustom:CheckPlayerHelmetArmorLessPercent", curValue, maxValue, curValue / maxValue)
    return curValue / maxValue <= checkPercent and 1 or 2
end

---- 检查玩家血量是否小于某个比例
function GuideDataCustom:CheckPlayerHealthLessPercent(params)
    local checkPercent = tonumber(params[1])
    local guideField = Module.Guide.Field
    local curValue = guideField.inGamePlayerHealth
    local maxValue = guideField.inGamePlayerMaxHealth
    if maxValue == 0 then
        return 2
    end
    loginfo("GuideDataCustom:CheckPlayerHealthLessPercent", curValue, maxValue, curValue / maxValue)
    return curValue / maxValue <= checkPercent and 1 or 2
end

---- 检查玩家是否有某个buff
function GuideDataCustom:CheckPlayerCurHaveBuff(params)
    local buffId = tonumber(params[1])
    local guideField = Module.Guide.Field
    return table.contains(guideField.inGameBuffList, buffId) and 1 or 2
end

function GuideDataCustom:IsPlayerHaveNegativeState()
    local negativeBufIds =  Module.Guide.Config.PartDeBuffIds
    local guideField = Module.Guide.Field
    for _, buffId in pairs(negativeBufIds) do
        if table.contains(guideField.inGameBuffList, buffId) then
            return 1
        end
    end

    return 2
end

---- 设置引导中锁定轮盘的标记位
function GuideDataCustom:SetFlagDisableCommendInGuide(params)
    local bDisable = params[1]
    Module.HUDToolBar:SetFlagDisableCommendInGuide(bDisable == "1")
    Module.Guide.Field.bChangeRouletteState = true
    return 1
end

---- 检查小地图是否加载完毕（存在对应ui）
function GuideDataCustom:CheckMiniMapExist(params)
    local GuideLogic = require "DFM.Business.Module.GuideModule.GuideLogic"
    local uiWidgetId = 37
    local widgetConfig = GuideConfig.TableGuideWidgetConfig[uiWidgetId]
    local hudUIWidget = GuideLogic.GetWidgetByPath(widgetConfig.BpPath, widgetConfig.WidgetName, widgetConfig.UIName, widgetConfig.IsHudUI)
    if hudUIWidget then
        return 1
    else
        return 2
    end
end

---- 设置配装界面选中指定id的道具
function GuideDataCustom:SetAssemblySelectItem(params)
    local GuideLogic = require "DFM.Business.Module.GuideModule.GuideLogic"
    local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
    local WeaponHelperTool     = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
    local assemblyUI = GuideLogic.getWidgetFuncAssemblySelectionMain()
    if not assemblyUI then return 2 end
    local itemId = tonumber(params[1])
    local itemMainType = ItemHelperTool.GetMainTypeById(itemId)
    local mergeDataList = Module.ArmedForce:GetAssemblySelectionDataList()
    if itemMainType == EItemType.Weapon then
        for _, data in ipairs(mergeDataList) do
            local item = data:GetRepresentativeItem()
            local weaponFeature = item:GetFeature(EFeatureType.Weapon)
			if weaponFeature then
				local desc = weaponFeature:GetRawDescObj()
				if WeaponHelperTool.CompareWeaponDescriptionWithPresetID(itemId, desc) then
                    assemblyUI:DoSelectCommonItem(Module.ArmedForce:GetAssemblySelectionDataIdx(data))
                    return 1
                end
			end
        end
    else
        for _, data in ipairs(mergeDataList) do
            local item = data:GetRepresentativeItem()
            if item and item.id == itemId then
                assemblyUI:DoSelectCommonItem(Module.ArmedForce:GetAssemblySelectionDataIdx(data))
                return 1
            end
        end
    end
    return 2
end

---- 设置子弹选择数值
function GuideDataCustom:TrySetBulletSelectNum(params)
    if not params then
        return 1
    end

    local num = tonumber(params[1])
    Module.ArmedForce:TrySetBulletSelectNum(num)
end

---- 帮玩家把 指定道具 放到 快速配药背包 里
function GuideDataCustom:AutoPlaceItemsInArmedForce(params)
    if not params or #params % 2 == 1 then
        return 2
    end

    local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
    local itemInfos = {}
    local optItems = {}
    for i = 1, #params, 2 do
        local itemId = tonumber(params[i])
        local needNum = tonumber(params[i + 1])
        local itemStructs = Server.InventoryServer:GetItemsById(itemId)
        for _, itemStruct in pairs(itemStructs) do
            if itemStruct.num >= needNum then
                table.insert(optItems, {item = itemStruct, num = needNum})
                break
            else
                table.insert(optItems, {item = itemStruct, num = itemStruct.num})
                needNum = needNum - itemStruct.num
            end
        end
    end

    ItemOperaTool.DoPlaceItems(optItems, Server.InventoryServer:GetSlot(ESlotType.BagSpaceContainer), false, {bAutoMerge = true})
    local fOnSyncInvFinished = function ()
        GuideConfig.EGuideEvent.evtAutoDoPlaceItemFinished:Invoke()
    end
    Server.InventoryServer:SyncInventoryChanged(fOnSyncInvFinished)
    return 1
end

---- 刷新一下快速带药界面
function GuideDataCustom:RefreshAssemblyMedicineView(params)
    local assemblyUI = GuideLogic.getWidgetFuncAssemblyMedicineViewUI()
    if not assemblyUI then return 2 end
    assemblyUI:RefreshView()
    return 1
end

---- 设置结算界面是否停用自动下一步
function GuideDataCustom:SetEvacuateNotAutoContinueFlag(params)
    if not params then
        return 2
    end

    if params[1] == "0" then
        Module.Settlement:SetNotAutoContineFlag(false)
    else
        Module.Settlement:SetNotAutoContineFlag(true)
    end

    return 1
end

---- 设置是否详情页点击背景关闭
---@param params[1] string  [0] 关闭 [1] 不关闭
function GuideDataCustom:SetDisableItemdDetailPanelClickBgCloseState(params)
    if not params then
        return 2
    end

    if params[1] == "0" then
        Module.ItemDetail:SetDisableItemdDetailPanelClickBgCloseState(false)
    else
        Module.ItemDetail:SetDisableItemdDetailPanelClickBgCloseState(true)
    end

    return 1
end

---- 检查某个局内任务目标是否有效
function GuideDataCustom:CheckQuestObjectionIsAvtive(params)
    if not params then
        return 2
    end

    local ASOLPlayerQuestSystem = import "SOLPlayerQuestSystem"
    local ESOLQuestObjectionState = import "ESOLQuestObjectionState"
    local questId = tonumber(params[1])
    local questObjectionId = tonumber(params[2])
    local questSystem = ASOLPlayerQuestSystem.GetInstance(GetWorld())
    local playerQuestComp = questSystem:GetPlayerQuestComponent()

    if playerQuestComp then
        local ret, objectionInfo = playerQuestComp:GetQuestObjectionInfo(questId, questObjectionId, objectionInfo)
        if objectionInfo then
            return objectionInfo.QuestObjectionState == ESOLQuestObjectionState.Active and 1 or 2
        end
    else
        return 2
    end

    return 2
end

-- 大地图是否加载完
function GuideDataCustom:CheckBigMapLoadItemFinished(params)
    local _, targetUI = GuideLogic.GetWidgetByPath(nil, "CommonButtonIcon_Task", "MobileHUD_BigMap", true)
    if not targetUI then
        err("CheckBigMapLoadItemFinished, no targetUI")
        return 3
    end
    if not targetUI.IsLoadAllMapItem then
        err("CheckBigMapLoadItemFinished, no func IsLoadAllMapItem")
        return 3
    end
    if targetUI:IsLoadAllMapItem() then
        return 1
    end
    return 2
end

-- 是否在战斗状态
function GuideDataCustom:CheckIsInCombat(params)
    return Module.Guide.Field.bInCombat and 1 or 2
end

-- 是否处于安全屋的gameflow
function GuideDataCustom:CheckIsInSafeHouseGameFlow(params)
    return (Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse) and 1 or 2
end

-- 是否完成改名
function GuideDataCustom:CheckIsRenameFinished(params)
    return Server.AccountServer:GetPlayerRegisterState() and 1 or 2
end

-- 启动改名流程
function GuideDataCustom:BeginRename(params)
    Module.Login:ShowRegisterView()
    return 1
end

-- 启动模式选择弹窗
function GuideDataCustom:ShowModeSelectUI(params)
    -- Facade.UIManager:AsyncShowUI(UIName2ID.GuideGameModeSelect)
    return 1
end

-- 播放解锁系统效果
function GuideDataCustom:ShowModuleUnlock(params)
    if not params or #params < 1 then
        return 2
    end

    local unlockId = tonumber(params[1])
    Module.Reward:OpenModuleUnlockPanel(unlockId)
    return 1
end

-- 禁止/取消禁止 详情页打开
function GuideDataCustom:SetItemDetailPanelDisableShowState(params)
    local bDisable = params[1] and tonumber(params[1]) ~= 0
    Module.ItemDetail:SetDisableItemDetailPanelShowState(bDisable)
    return 1
end


-- 设置战略版模型选中效果
function GuideDataCustom:SetSandBoxItemSelectEffectEnable(params)
    local sandBoxMapUtil = Module.SandBoxMap:GetSandBoxMapUtil()

    local _checkMapAndPlayAnim = function(mapId, sandBoxIdx)
        local mapCfg = Server.GameModeServer:GetWorldEntranceInfoByMapID(mapId)
        if mapCfg then
            local bLock = sandBoxMapUtil.IsTargetPointLocked(mapCfg)
            if not bLock then
                Module.SandBoxMap:PlayGuideAnim(sandBoxIdx)
                return true
            end
        end
        return false
    end

    for  _ = 1, 1 do
        -- 看大坝是否解锁
        if _checkMapAndPlayAnim(2201, 2) then break end
        -- 看森林是否解锁
        if _checkMapAndPlayAnim(1901, 1) then break end
        -- 看巴克什是否解锁
        if _checkMapAndPlayAnim(8101, 5) then break end
        -- 看航天城是否解锁
        if _checkMapAndPlayAnim(3902, 3) then break end
    end

    return 1
end

-- 设置战略版模型选中效果
function GuideDataCustom:SetSandBoxItemSelectEffectDisable(params)
    Module.SandBoxMap:StopGuideAnim(1)
    return 1
end

-- 设置是否战略版限制锁定选图
function GuideDataCustom:SetRestrictSelectMapFlag(params)
    if not params or #params < 1 then
        return 2
    end
    Module.SandBoxMap:SetRestrictSelectMapFlag(params[1] ~= "0")
    if params[1] ~= "0" then
        self:SetRestrictUnlockMapList({2, 0})
    else
        self:SetRestrictUnlockMapList(nil)
    end
    return 1
end

-- 设置是否战略版锁定地图表现
function GuideDataCustom:SetRestrictUnlockMapList(params)
    if not params or #params < 1 then
        Module.SandBoxMap:SetUnlockMapByGuide(nil)
        return 1
    end
    local unlockList = {}
    for _, mapIdxStr in pairs(params) do
        table.insert(unlockList, tonumber(mapIdxStr))
    end
    Module.SandBoxMap:SetUnlockMapByGuide(unlockList)
    return 1
end

-- 设置是否禁用战略板点击检测
function GuideDataCustom:SetDisableSandboxClick(params)
    if not params or #params < 1 then
        Module.SandBoxMap:SetDisableSelectModeByGuide(false)
        return 1
    end
    Module.SandBoxMap:SetDisableSelectModeByGuide(params[1] ~= "0")
    return 1
end

-- 调用对话
function GuideDataCustom:ShowDialogUI(params)
    if not params or #params < 1 then
        return 1
    end
    local dialogId = tonumber(params[1])
    Module.Guide:OpenGuideDialogUI(dialogId)
    return 1
end

-- 关闭对话
function GuideDataCustom:CloseDialogUI(params)
    Module.Guide:CloseGuideDialogUI()
    return 1
end

-- 收起app
function GuideDataCustom:CancelUseApp(params)
    local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
    if not localCtrl then return 1 end
    Module.CommonBar:SetUseStarAppState(false, true)
    return 1
end

-- 收起app
function GuideDataCustom:UseApp(params)
    local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
    if not localCtrl then return 1 end
    Module.CommonBar:SetUseStarAppState(true, true)
    return 1
end

-- 记录播放过动画
function GuideDataCustom:RecordVedioPlayed(params)
    Server.TipsRecordServer:SetBoolean(Server.TipsRecordServer.keys.GuideSolVedioPlayed, true)
    return 1
end

-- 是否播过动画
function GuideDataCustom:IsVedioPlayed(params)
    if Server.TipsRecordServer:GetBoolean(Server.TipsRecordServer.keys.GuideSolVedioPlayed) then
        return 1
    else
        return 2
    end
end

-- 打开接受任务界面
function GuideDataCustom:ShowRegisterGetTaskUI(params)
    Facade.UIManager:AsyncShowUI(UIName2ID.RegisterView, nil, nil, nil, 1)
    return 1
end

-- 检查是否到达指定等级
function GuideDataCustom:CheckLevelMoreThan(params)
    if not params or #params < 1 then
        return 2
    end
    local targetLevel = tonumber(params[1])
    log("CheckLevelMoreThan target:", targetLevel, "cur Level:", Server.RoleInfoServer.seasonLevel)
    if Server.RoleInfoServer.seasonLevel >= targetLevel then
        return 1
    end

    return 2
end

-- 打开仓库界面
function GuideDataCustom:ShowInventoryMainUI(params)
    --BEGIN MODIFICATION @ VIRTUOS : 临时跳过打开仓库的操作
    if DFCONSOLE_LUA == 1 then
        loginfo("[Console] Jump over the GuideDataCustom:ShowInventoryMainUI")
        return 2
    end
    --END MODIFICATION

    if DFHD_LUA == 1 then
        --Module.IrisSafeHouse:OpenMainTabView(2)

        --Module.CommonBar:ShowPrimaryTabGroup(3)
        --Facade.UIManager:AsyncShowUI(UIName2ID.WarehouseMain)
        local warehouseIdx = 2
        if Module.CommonBar:GetTopTabGroupIndex(1) == warehouseIdx then
            return 2
        else
            Module.CommonBar:CheckTopTabGroup(warehouseIdx, true, 1)
        end
    end
    return 1
end

-- 是否打开过仓库界面
function GuideDataCustom:IsWarehouseOpened(params)
    --if true then return 2 end
    if Server.TipsRecordServer:GetBoolean(Server.TipsRecordServer.keys.GuideWarehouseOpened) then
        return 1
    else
        return 2
    end
end

-- 是否打开过部门界面
function GuideDataCustom:IsDepartmentOpened(params)
    --if true then return 2 end
    if Server.TipsRecordServer:GetBoolean(Server.TipsRecordServer.keys.GuideDepartmentOpened) then
        return 1
    else
        return 2
    end
end

-- 是否新手关完成
function GuideDataCustom:IsNewPlayerMatchFinished(params)
    if Server.GuideServer:IsNewPlayerMatchFinished() then
        return 1
    else
        return 2
    end
end

function GuideDataCustom:IsStrategicGuideAllOpFinished(params)
    local bAllFinish = true

    -- 只玩过新手关的话，战略板要高亮
    if Server.GuideServer:GetMatchCount(MatchGameRule.SOLGameRule) == 1 then
        bAllFinish = false

        local sandBoxMapUtil = Module.SandBoxMap:GetSandBoxMapUtil()
        local _checkMapAndPlayAnim = function(mapId, sandBoxIdx)
            local mapCfg = Server.GameModeServer:GetWorldEntranceInfoByMapID(mapId)
            if mapCfg then
                local bLock = sandBoxMapUtil.IsTargetPointLocked(mapCfg)
                if not bLock then
                    Module.SandBoxMap:PlayGuideAnim(sandBoxIdx)
                    return true
                end
            end
            return  false
        end
    
            
        for _ = 1, 1 do
            -- 看大坝是否解锁
            if _checkMapAndPlayAnim(2201, 2) then break end
            -- 看森林是否解锁
            if _checkMapAndPlayAnim(1901, 1) then break end
            -- 看巴克什是否解锁
            if _checkMapAndPlayAnim(8101, 5) then break end
            -- 看航天城是否解锁
            if _checkMapAndPlayAnim(3902, 3) then break end
        end

    end

    if self:IsWarehouseOpened() ~= 1 then
        bAllFinish = false
        -- 设置仓库入口高亮
        Module.Guide:OpenGuideHDWeakClickUI(148)
    end

    if self:IsDepartmentOpened() ~= 1 then
        bAllFinish = false
        -- 设置部门入口高亮
        Module.Guide:OpenGuideHDWeakClickUI(149)
    end

    if bAllFinish then
        return 1
    else
        return 2
    end
end

-- 1 大坝开启
-- 2 森林开启
-- 3 航天城开启
-- 4 没有开启的sol地图
function GuideDataCustom:SwitchValidSandBoxMap(params)
    local sandBoxMapUtil = Module.SandBoxMap:GetSandBoxMapUtil()

    local _checkMap = function(mapId)
        local mapCfg = Server.GameModeServer:GetWorldEntranceInfoByMapID(mapId)
        if mapCfg then
            local bLock = sandBoxMapUtil.IsTargetPointLocked(mapCfg)
            if not bLock then
                return true
            end
        end
        return  false
    end

    -- 看大坝是否解锁
    if _checkMap(2201) then
        return 1
    else
        -- 看森林是否解锁
        if _checkMap(1901) then
            return 2
        else
            -- 看航天城是否解锁
            if _checkMap(3902) then
                return 3
            else
            end
        end
    end
    return 4
end


-- 设置备战枪不要有hover tips
function GuideDataCustom:SetArmedForceNotShowHoverTips(params)
    Module.ArmedForce:SetNotShowHoverTipsFlag(true)
    return 1
end

-- 设置备战枪不要有hover tips
function GuideDataCustom:SetArmedForceShowHoverTips(params)
    Module.ArmedForce:SetNotShowHoverTipsFlag(false)
    return 1
end

function GuideDataCustom:IsRewardShowingPopUI(params)
    return Module.Reward:IsShowingPopUI() and 1 or 2
end

function GuideDataCustom:PlayDialogAndVedio(params)
    if not params or #params < 1 then
        return 2
    end
    local dialogConfigId = tonumber(params[1])
    local playTime = 2
    if #params >= 2 then
        playTime = tonumber(params[2])
    end
    log("PlayDialogAndVedio", dialogConfigId, playTime)
    local _delay = function()
        Module.Guide.CloseGuideDialogUI()
    end
    Module.Guide:OpenGuideDialogUI(dialogConfigId)
    Timer.DelayCall(playTime, _delay)
end

function GuideDataCustom:IsAuctionUnlockSell(params)
    return GuideLogic.IsAuctionUnlockSell() and 1 or 2
end

-- 1 开始预热
-- 2 预热完毕
-- 3 预热失败
function GuideDataCustom:WarmUpFirstSequence(params)
    local UDFMIrisEnterSubsystem = import "DFMIrisEnterSubsystem":Get(GetWorld())
    local bReady = UDFMIrisEnterSubsystem:CollectSeamlessEnterObject()
    if bReady then
        UDFMIrisEnterSubsystem:WarmUpFirstSequence()
        bReady = UDFMIrisEnterSubsystem:IsFirstSequenceReady()
        if bReady then
            loginfo("finish WarmUpFirstSequence")
            return 2
        else
            loginfo("begin WarmUpFirstSequence")
            return 1
        end
    else
        logerror("cant WarmUpFirstSequence")
        return 3
    end
end

function GuideDataCustom:IsMarkHighValueBox(params)
    return GuideLogic.IsMarkHighValueBox() and 1 or 2
end

function GuideDataCustom:CheckMarkHighValueBoxIconValid(params)
    local UDFMHudHelper = import "DFMHudHelper"
    local ret = UDFMHudHelper.GetHighValueBoxIconInBigMap(GetWorld())
    return #ret > 0 and 1 or 2
end

function GuideDataCustom:LootingScrollToDeadBodyChest(params)
    Module.Looting:ScrollToItemSlot(ESlotType.ChestHanging, ESlotGroup.DeadBody)
    return 1
end

-- 是否有可以出售的道具
-- 1 有
-- 2 没有
function GuideDataCustom:CheckHaveSellItem(params)
    local itemIdList = Module.Guide:GetNewPlayerGuideItemInfo(GuideConfig.NewPlayerGuideSpecItem.sellItemMobile)
    local itemId = tonumber(itemIdList[1])
    local items = Server.InventoryServer:GetItemsById(itemId)
    local targetSlot = Server.InventoryServer:GetSlot(ESlotType.MainContainer)
    if #items > 0 then
        for _, item in pairs(items) do
            if item.InSlot == targetSlot and item:CheckIsBind() then
                return 1
            end
        end

        logerror("item not in main container, id", itemId)
        return 2
    end
    logerror("not have sell item, id", itemId)
    return 2
end

-- 是否有可以购买的道具
-- 1 有
-- 2 没有
function GuideDataCustom:CheckHaveBuyItem(params)
    return 1
end

-- 是否打开sol大厅ui
-- 1 打开
-- 2 未开
function GuideDataCustom:CheckSolHallOpen(params)
    if Facade.UIManager:GetStackUICount() == 0 then
        return 2
    end
    -- if Facade.UIManager:GetCurrentStackUIId() ~= UIName2ID.IrisWorldEntryMainPanel then
    --     return 2
    -- end
    return 1
end

-- 重新注册仓库出售药品的控件
-- 1 成功
-- 2 失败
-- 3 有道具但是没有ui，等待滑动
function GuideDataCustom:TryAddGuideWidgetWareHouseBindItem(args)
    GuideLogic.RemoveGuideWidgetProxy(GuideConfig.EGuideProxyWidget.guideProxyInventoryBindItem)
    local itemIdList = Module.Guide:GetNewPlayerGuideItemInfo(GuideConfig.NewPlayerGuideSpecItem.sellItemMobile)
    local itemId = itemIdList[tonumber(args[1])]
    if not itemId then return 2 end
    itemId = tonumber(itemId)
    local inventorySlotView = GuideLogic.getWidgetFuncWarehouseSlotView()
    if not inventorySlotView then return 2 end
    --local itemViews = inventorySlotView:GetAllItemViews()
    local allItems = inventorySlotView:GetAllItems()
    for _, item in ipairs(allItems) do
        if item.id == itemId and item:CheckIsBind() then
            if self:_WarehouseMoveByItem(item) then
                local uiIns = inventorySlotView:GetViewByItem(item)
                if uiIns then
                    -- uiIns:SetCppValue("bHandleDrag", false) -- 禁用一下拖动
                    uiIns:SetHandleDrag(false)
                    uiIns:SetHandleDoubleClick(false)
                    Module.CommonWidget:SetFlagDisableOpForGuide(true)
                    GuideLogic.AddGuideWidgetProxy(GuideConfig.EGuideProxyWidget.guideProxyInventoryBindItem, uiIns)
                    return 1
                else
                    return 3
                end
            else
                return 2
            end
        end
    end
    return 2
end

---- 重新注册仓库带药引导中的控件
--- 1 注册成功
--- 2 注册失败
function GuideDataCustom:TryAddGuideWidgetWareHouseBindItemAgain(args)
    local itemIdList = Module.Guide:GetNewPlayerGuideItemInfo(GuideConfig.NewPlayerGuideSpecItem.sellItemMobile)
    local itemId = itemIdList[tonumber(args[1])]
    if not itemId then return 2 end
    itemId = tonumber(itemId)
    local inventorySlotView = GuideLogic.getWidgetFuncWarehouseSlotView()
    if not inventorySlotView then return 2 end
    --local itemViews = inventorySlotView:GetAllItemViews()
    local allItems = inventorySlotView:GetAllItems()
    for _, item in ipairs(allItems) do
        if item.id == itemId and item:CheckIsBind() then
            local uiIns = inventorySlotView:GetViewByItem(item)
            if uiIns then
                -- uiIns:SetCppValue("bHandleDrag", false) -- 禁用一下拖动
                uiIns:SetHandleDrag(false)
                uiIns:SetHandleDoubleClick(false)
                Module.CommonWidget:SetFlagDisableOpForGuide(true)
                GuideLogic.AddGuideWidgetProxy(GuideConfig.EGuideProxyWidget.guideProxyInventoryBindItem, uiIns)
                return 1
            end
        end
    end
    return 2
end

-- 尝试注册新手关免保物品
function GuideDataCustom:TryAddProxyNewPlayerLevelSafeBoxLootItem()
    local ids = Module.Guide:GetNewPlayerGuideItemInfo(GuideConfig.NewPlayerGuideSpecItem.newPlayerLevelSafeBoxLootItem)
    local id = tonumber(ids[1])
    log("TryAddProxyNewPlayerLevelSafeBoxLootItem", id)

    local safeBoxContainerView = GuideLogic.GetLootingContainerView(ESlotType.NearbyContainer, false)
    if not safeBoxContainerView then
        warn("TryAddProxyNewPlayerLevelSafeBoxLootItem, no safeBoxContainerView")
        return 2
    end
    local slotView = safeBoxContainerView:GetContainerSlotView()
    if hasdestroy (slotView) then
        warn("TryAddProxyNewPlayerLevelSafeBoxLootItem, no slotView")
        return 2
    end


    local itemSlotView = nil
    for item, itemView in pairs(slotView:GetAllItemViews() or {}) do
        if not hasdestroy(item) and not hasdestroy(itemView) then
            if item.id == id then
                itemSlotView = itemView
                break
            end
        end
    end
    if not itemSlotView then
        warn("TryAddProxyNewPlayerLevelSafeBoxLootItem, no itemSlotView")
        return 2
    end

    Module.Guide:AddGuideWidgetProxy(GuideConfig.EGuideProxyWidget.guideProxyNewPlayerLevelSafeBoxLootItem, itemSlotView)
    return 1
end


-- 自动帮玩家更新子弹携带
function GuideDataCustom:TryAutoSetBulletSelectNum(args)
    local targetNum = 180
    local targetQuality = 2
    if self:CheckHaveWeaponMainSlot() == 1 then
        return GuideLogic.TryAutoSetBulletSelectNum(111, targetQuality, targetNum) and 1 or 2
    elseif self:CheckHaveWeaponSubSlot() == 1 then
        return GuideLogic.TryAutoSetBulletSelectNum(112, targetQuality, targetNum) and 1 or 2
    end
    return 2
end

-- 自动帮玩家更新药品
function GuideDataCustom:TryAutoSetMedicineSelectNum(args)
    local itemId = tonumber(args[1])
    local slot = tonumber(args[2])
    local num = tonumber(args[3])
    return GuideLogic.TryAutoSetBulletSelectNum(slot, itemId, num) and 1 or 2
end

-- 自动帮玩家更新药品1
function GuideDataCustom:TryAutoSetMedicineSelectNumHealth(args)
    return GuideLogic.TryAutoSetMedicineSelectNum(1, GuideConfig.TakeItemAddHealth, 2) and 1 or 2
end

-- 自动帮玩家更新药品2
function GuideDataCustom:TryAutoSetMedicineSelectNumRecover(args)
    return GuideLogic.TryAutoSetMedicineSelectNum(2, GuideConfig.TakeItemAddRecover, 2) and 1 or 2
end

-- 检查是否可以进行子弹携带
function GuideDataCustom:TryCheckCanBulletSelect(args)
    local targetQuality = 2
    if self:CheckHaveWeaponMainSlot() == 1 then
        return GuideLogic.CheckCanGetBullet(111, targetQuality) and 1 or 2
    elseif self:CheckHaveWeaponSubSlot() == 1 then
        return GuideLogic.CheckCanGetBullet(112, targetQuality) and 1 or 2
    end
    return 2
end

-- 播放弹窗
function GuideDataCustom:ShowTips(args)
    local text = args[1]
    local duration = tonumber(args[2])
    local UDFMHudHelper = import "DFMHudHelper"
    if text then
        text = GuideConfig.Loc[text]
    end
    if not text then
        logerror("GuideDataCustom:ShowTips text is nil,", args[1])
        return
    end
    -- UDFMHudHelper.ShowCommonBanner(GetWorld(), text, duration)
    local guideMgrIns = UDFMGuideManager.Get(GetWorld())
    local opt = args[3]
    guideMgrIns:ShowCommonBanner(text, duration, opt or 0)
    return 1
end

-- 是否有可以出售拍卖行的道具
-- 1 有
-- 2 没有
function GuideDataCustom:CheckHaveAuctionSellItem(params)
    local itemIdList = Module.Guide:GetNewPlayerGuideItemInfo(GuideConfig.NewPlayerGuideSpecItem.invItem)
    local itemId = tonumber(itemIdList[5])
    local items = Server.InventoryServer:GetItemsById(itemId)
    if #items > 0 then
        return 1
    end
    logerror("not have sell item, id", itemId)
    return 2
end

-- 尝试播放mp 配装按钮高亮动画
function GuideDataCustom:TryStartMpAssemblyTopbarBtnAnim(args)
    Module.Guide:OpenGuideHDWeakClickUI(181)
end

-- 尝试取消播放mp 配装按钮高亮动画
function GuideDataCustom:TryStopMpAssemblyTopbarBtnAnim(args)
    Module.Guide:CloseGuideHDWeakClickUI(181)
end

-- 尝试播放mp 排位按钮高亮动画
function GuideDataCustom:TryStartMpRankTopbarBtnAnim(args)
    Module.Guide:OpenGuideHDWeakClickUI(205)
end

-- 尝试取消播放mp 排位按钮高亮动画
function GuideDataCustom:TryStopMpRankTopbarBtnAnim(args)
    Module.Guide:CloseGuideHDWeakClickUI(205)
end


-- 检查是否有高级经验书
function GuideDataCustom:CheckHaveWeaponExpItem(args)
    local itemData = Server.CollectionServer:GetCollectionItemById(GuideConfig.MpWeaponUpgradeItemId)
    if not itemData then
        return 2
    end
    return 1
end

---- 锁定looting尸体的滚动
function GuideDataCustom:SetLootingDeadBodyScrollDisable()
    local slotView = GuideLogic.getWidgetFuncLootingDeadBody()
    if not slotView then return 2 end
    slotView:SetScrollEnable(false)

    -- 2025/8/12 <dexzhou> guideLooting 中途关闭背包，会让滑动状态异常，she3强引导改若引导导致。
    -- 暂时使用事件做保底回收， 原有逻辑(在对应的ui中保存了一个bool flag来禁用滑动)不动。
    -- 中间的传递链可能不稳定(从module到对应的ui, ui 是否存在?). 使用事件广播让业务ui自己清理: see GuideField:ResetFlagForOtherModule
    Module.Guide.Field:AddExternalModified(GuideConfig.EExternalModification.LootingDeadBodyPanelScrollEnable)
    return 1
end

---- 解锁looting尸体的滚动
function GuideDataCustom:SetLootingDeadBodyScrollEnable()
    local slotView = GuideLogic.getWidgetFuncLootingDeadBody()
    if not slotView then return 2 end
    slotView:SetScrollEnable(true)
    Module.Guide.Field:AddExternalModified(GuideConfig.EExternalModification.LootingDeadBodyPanelScrollEnable)
    return 1
end


function GuideDataCustom:SetLootingWarehouseScrollEnable(params)
    local bEnable = params[1] == "1" and true or false
    local implWidget = GuideLogic.getWidgetFuncLootingWarehouse()
    if not implWidget then return 2 end

    --BEGIN MODIFICATION @ VIRTUOS : 关闭交易行引导后，也临时跳过解锁逻辑
    if DFCONSOLE_LUA == 1 then
        return 1
    end
    --END MODIFICATION

    loginfo("GuideDataCustom.SwitchDisableLootingWarehouseScroll", bEnable)
    implWidget:SetScrollEnable(bEnable)
    return 1
end

---- 滑动到药品快捷装配第二项
function GuideDataCustom:ScrollToFastMedicineSecendItem()
    local uiIns = GuideLogic.getWidgetFuncAssemblyBulletView()
    if not uiIns then return 2 end

    if uiIns.ScrollToSecendItem then
        uiIns:ScrollToSecendItem()
    end
    return 1
end

---- 是否是队员
function GuideDataCustom:IsMember()
    if Server.TeamServer:IsMember() then
        return 1
    else
        return 2
    end
end

---- 禁止关闭干员技能弹窗界面
function GuideDataCustom:DisableCloseHeroSkillPanel()
    Module.Hero:SetFlagDisableClickBgCloseHeroSkill(true)
    return 1
end

---- 允许关闭干员技能弹窗界面
function GuideDataCustom:EnableCloseHeroSkillPanel()
    Module.Hero:SetFlagDisableClickBgCloseHeroSkill(false)
    return 1
end

---- 判断尸体主武器是否有枪
function GuideDataCustom:CheckDeadBodyHaveMainWeapon()
    local slot = Server.InventoryServer:GetSlot(ESlotType.MainWeaponLeft, ESlotGroup.DeadBody)
    if slot then
        local item = slot:GetEquipItem()
        if item then
            loginfo("CheckDeadBodyHaveMainWeapon", item.id)
            return 1
        end
    end
    loginfo("CheckDeadBodyHaveMainWeapon no item")
    return 2
end

---- 判断玩家哪个槽位可以放枪
--- 1 第一个槽位
--- 2 第二个槽位
function GuideDataCustom:GetPlayerSlotCanGetWeapon()
    local cantChangeItemId = 18010000001 -- m4a1槽位不可替换
    local slot = Server.InventoryServer:GetSlot(ESlotType.MainWeaponLeft, ESlotGroup.Player)
    if slot then
        local item = slot:GetEquipItem()
        if item then
            loginfo("GetPlayerSlotCanGetWeapon left", item.id)
            if item.id == cantChangeItemId then
                return 2
            else
                local slot2 = Server.InventoryServer:GetSlot(ESlotType.MainWeaponRight, ESlotGroup.Player)
                if slot2 then
                    local item2 = slot2:GetEquipItem()
                    if item2 then
                        loginfo("GetPlayerSlotCanGetWeapon right", item2.id)
                        if item2.id == cantChangeItemId then
                            return 1
                        else
                            return 2
                        end
                    else
                        loginfo("GetPlayerSlotCanGetWeapon right no item")
                        return 2
                    end
                end
            end
        else
            loginfo("GetPlayerSlotCanGetWeapon left no item")
            return 1
        end
    end
    loginfo("GetPlayerSlotCanGetWeapon something wrong")
    return 2
end


function GuideDataCustom:LootingScrollToDeadBodyWeapon(params)
    Module.Looting:ScrollToItemSlot(ESlotType.MainWeaponLeft, ESlotGroup.DeadBody)
    return 1
end

function GuideDataCustom:DisableScrollSearchedItem(params)
    Module.Looting:SetDisableScrollSearchedItemState(true)
    return 1
end

function GuideDataCustom:EnableScrollSearchedItem(params)
    Module.Looting:SetDisableScrollSearchedItemState(false)
    return 1
end



--- @return integer 1-helmet 2-breastplate 3-none
function GuideDataCustom:CheckWhichAromorDurabilityLower (parms)
    local branch = 3
    local minDurabilityPercent = tonumber(parms[1])

    local percent,bCanRepair  = GuideLogic.GetPlayerEquipSlotDurabilityPercent(ESlotType.Helmet)
    if percent and percent < minDurabilityPercent and bCanRepair then
        minDurabilityPercent = percent
        branch = 1
    end


    percent = nil
    bCanRepair = nil
    local percent, bCanRepair = GuideLogic.GetPlayerEquipSlotDurabilityPercent(ESlotType.BreastPlate)
    if percent and percent < minDurabilityPercent and bCanRepair then
        minDurabilityPercent = percent
        branch = 2
    end

    return branch
end

--- @return integer 1-helmet 2-breastplate 3-none
function GuideDataCustom:CheckWhichAmorMatchLowMaxDurability(params)
    local requiredAmorLevel = GuideConfig.LowMaxAmorDurabilityGuide.requiredAmorLevel
    local requiredCurPercent = GuideConfig.LowMaxAmorDurabilityGuide.requiredCurPercent
    local requiredCurMaxPercent = GuideConfig.LowMaxAmorDurabilityGuide.requiredCurMaxPercent
    loginfo("GuideDataCustom:CheckWhichAmorMatchLowMaxDurability", requiredAmorLevel, requiredCurPercent, requiredCurMaxPercent)

    local feature = nil
    feature = GuideLogic.GetEquipmentFeature(ESlotType.BreastPlate)
    if feature then
        loginfo("GuideDataCustom:CheckWhichAmorMatchLowMaxDurability breastPlate", feature.ArmorLevel, feature.curDurability, feature.maxDurability,
            feature.maxDurabilityOriginal)
        if feature.ArmorLevel >= requiredAmorLevel then
            local cur = feature.curDurability or 0
            local max = feature.maxDurability or 1
            local originMax = feature.maxDurabilityOriginal or 1
            if cur / max <= requiredCurPercent then
                if max / originMax <= requiredCurMaxPercent then
                    return 2
                end
            end
        end
    end

    feature = nil
    feature = GuideLogic.GetEquipmentFeature(ESlotType.Helmet)
    if feature then
        loginfo("GuideDataCustom:CheckWhichAmorMatchLowMaxDurability helmet", feature.ArmorLevel, feature.curDurability, feature.maxDurability,
            feature.maxDurabilityOriginal)
        if feature.ArmorLevel >= requiredAmorLevel then
            local cur = feature.curDurability or 0
            local max = feature.maxDurability or 1
            local originMax = feature.maxDurabilityOriginal or 1
            if cur / max <= requiredCurPercent then
                if max / originMax <= requiredCurMaxPercent then
                    return 1
                end
            end
        end
    end

    return 3
end


function GuideDataCustom:IsLowHelmetDurability()
    local percent, bCanRepair = GuideLogic.GetPlayerEquipSlotDurabilityPercent(ESlotType.Helmet)
    if percent and percent >= 0 and percent < GuideConfig.guideArmedForceArmorDurabilityPercent and bCanRepair then
        return 1
    end

    return 2
end


function GuideDataCustom:IsLowBreastPlateDurability ()
    local percent, bCanRepair = GuideLogic.GetPlayerEquipSlotDurabilityPercent(ESlotType.BreastPlate)
    if percent and percent >= 0 and percent < GuideConfig.guideArmedForceArmorDurabilityPercent and bCanRepair then
        return 1
    end

    return 2
end



--- @return integer
-- 1 已下载
-- 2 未下载
-- 3 地图被锁定 
-- 4 状态异常
function GuideDataCustom:IsSelectMapAlreadyDownloaded()
    local matchMode =  Module.SandBoxMap:GetSelectedMatchMode()
    if table.isempty(matchMode) then
        logerror("Module.SanBoxMap:IsSelectMapAlreadyDownloaded: 4, current did not select any map!!!")
        return 4
    end

    if not Module.SandBoxMap.GetCurrentSelectedMapModeLockReason then
        logerror("Module.SanBoxMap:IsSelectMapAlreadyDownloaded: 4, Module.SanBoxMap:GetCurrentSelectedMapModeLockReason is nil!!!")
        return 4
    end
    local lockReason =  Module.SandBoxMap:GetCurrentSelectedMapModeLockReason()
    loginfo("Module.SanBoxMap:IsSelectMapAlreadyDownloaded: lockReason = " .. tostring(lockReason))
    if  lockReason ~= MapBoardLockReason.MapOpen then
        return 3
    end

    local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
    if LiteDownloadManager:IsSupportLitePackage() then 
        local mapid = matchMode.map_id --worldEntranceCfg.MapID
        local moduleName = LiteDownloadManager:GetModuleNameByMapId(tostring(mapid))
        local bDownloaded = LiteDownloadManager:IsDownloadedByModuleName(moduleName)
        loginfo("Module.SanBoxMap:IsSelectMapAlreadyDownloaded: LiteDownloadManager:IsDownloadedByModuleName(moduleName) = " .. tostring(bDownloaded))
        return bDownloaded and 1 or 2;
    else
        loginfo("Module.SanBoxMap:IsSelectMapAlreadyDownloaded: LiteDownloadManager:IsSupportLitePackage() = false")
        return 1
    end

    loginfo("Module.SandBoxMap:IsSelectMapAlreadyDownloaded: 4, exception!!!")
    return 4
end


--- @return integer
-- 1 已下载
-- 2 未下载
-- 3 状态异常
function GuideDataCustom:IsCollectionRoomAlreadyDownloaded()
    local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
    local moduleName = LiteDownloadManager:GetSystemDownloadModuleName(EDownloadSystem.SafeHouse)
    if LiteDownloadManager:IsSupportLitePackage() then 
        local bDownLoaded=   LiteDownloadManager:IsDownloadedByModuleName(moduleName)
        log("IsCollectionRoomAlreadyDownloaded: LiteDownloadManager:IsDownloadedByModuleName(EDownloadSystem.SafeHouse) = " .. tostring(bDownLoaded))
        return bDownLoaded and 1 or 2;
    else
        return 1
    end

    return 3
end

function GuideDataCustom:DisableMandelPanelOpen()
    Module.SandBoxMap:SetGuideDisableJumpMandelState(true)
    return 1
end

function GuideDataCustom:EnableMandelPanelOpen()
    Module.SandBoxMap:SetGuideDisableJumpMandelState(false)
    return 1
end


-- TODO: make this a GuideData -> wait until click ui valid
---Check if click-data/UI valid to process
---@param params string[]
---@return integer
--- 1 Ok
--- 2 bu OK
function GuideDataCustom:IsTargetClickUIValid(params)
    local guideClickId = params[1]
    log("GuideDataCustom:IsTargetClickUIValid -> checking guideClickId: " .. guideClickId)
    local guideClickDataCfg = GuideConfig.TableGuideClickConfig[guideClickId];

    for _, uiWidgetId in ipairs(guideClickDataCfg.UIWidgetId) do
        local widgetConfig = GuideConfig.TableGuideWidgetConfig[uiWidgetId]

        local targetWidget, targetUI, targetWidgetList = GuideLogic.GetWidgetByPath(widgetConfig.BpPath,
            widgetConfig.WidgetName, widgetConfig.UIName, widgetConfig.IsHudUI)

        if not (targetWidget or targetWidgetList) then
            return 2
        end
    end

    return 1
end

---Check click config's *first* ui is valid
---@param params string[]
---@return integer
--- 1 Ok
--- 2 bu OK
function GuideDataCustom:IsTargetClickUIValidAndVisible(params)
   local guideClickId = params[1]
    log("GuideDataCustom:IsTargetClickUIValid -> checking guideClickId: " .. guideClickId)
    local guideClickDataCfg = GuideConfig.TableGuideClickConfig[guideClickId];

    local uiWidgetId = guideClickDataCfg.UIWidgetId[1]
    local widgetConfig = GuideConfig.TableGuideWidgetConfig[uiWidgetId]

    ---@type UIWidgetBase, UIWidgetBase, UIWidgetBase[]
    local targetWidget, targetUI, targetWidgetList = GuideLogic.GetWidgetByPath(
        widgetConfig.BpPath, widgetConfig.WidgetName, widgetConfig.UIName, widgetConfig.IsHudUI)


    if targetWidget then
        log("target widget, RenderOpacity:", targetWidget.RenderOpacity, "Visibility:", targetWidget.Visibility)
        if targetWidget.RenderOpacity > 0 and targetWidget.Visibility ~= ESlateVisibility.Hidden and targetWidget.Visibility ~= ESlateVisibility.Collapsed then
            return 1
        end
    end

    return 2
end




---GuideDataCustom.CheckCurrentTopStackUI 
--- 判断当前的Stack UI是什么, 是否是传入的UIName2ID的Key所对应的UI
--- 1 true
--- 2 false
function GuideDataCustom:CheckCurrentTopStackUI(params)
    local Id = tonumber(params[1])
    local controller = Facade.UIManager:GetLayerControllerByType(EUILayer.Stack)
    local topStackViewId = controller:GetCurrentViewId()
    local topStackView = controller:GetCurrentView()
    if topStackViewId and topStackView then
        if topStackViewId == Id then
            if  topStackView:IsInHideState() or topStackView:IsInHideBeginState() then
                return 2
            end
            return 1
        end
    end

    return 2
end

function GuideDataCustom:IsCurrentStackUIEqual(params)
    local uiName2ID = params[1]
    local stackId = GuideLogic.GetValueByPath(uiName2ID)
    return self:CheckCurrentTopStackUI({ stackId })
end


--1 在
--2 不在
function GuideDataCustom:IsInAssemblyHDMainPanelNow()
    return self:CheckCurrentTopStackUI({ UIName2ID.AssemblyHDMainPanel })
end


--1 在
--2 不在
function GuideDataCustom:IsSystemSettingMainViewShow()
    return  self:CheckCurrentTopStackUI({UIName2ID.SystemSettingMainView}) 
end


-- 将局内大地图设置到中间位置
function GuideDataCustom:SetBigMapFocusCenterUV(args)
    local UDFMHudHelper = import "DFMHudHelper"
    UDFMHudHelper.SetBigMapFocusCenterUV(GetWorld(), FVector2D(0, 0))
    return 1
end

function GuideDataCustom:IsGuidingOrWaitGuide()
    return  GuideLogic.IsGuidingOrWaitGuide() and 1 or 2
end


-- 配装界面枪械等级是否满了
-- 1 满
-- 2 不满
-- 3 异常
function GuideDataCustom:IsGunSmithCurrentWeaponMaxLevele()
    local curSelectedSelectionData = Module.ArmedForce.Field:GetCurAssemblySelectionData()
    if not curSelectedSelectionData then
        log("curSelectedSelectionData is nil")
        return 3
    end

    local weaponItem = curSelectedSelectionData:GetRepresentativeItem() -- !
    local bLocked = curSelectedSelectionData:CheckIsLocked()

    if not weaponItem or bLocked then
        log("Auto selected should be not be equipped")
        return 3
    end

    local weaponFeature = weaponItem:GetFeature(EFeatureType.Weapon)
    local currentLevel = weaponFeature:GetWeaponLevel()
    local levelCfgs = Server.WeaponAssemblyServer:GetWeaponLevelUnLockData(weaponItem.id)

    if not weaponFeature or not levelCfgs  or not  currentLevel then
        log("weaponFeature or levelCfgs or currentLevel is nil")
        return 3
    end

    log("currentLevel: ", currentLevel)
    local bMaxLevel = levelCfgs[currentLevel].Exp <= 0

    log("is max level: " ,bMaxLevel)
    return bMaxLevel and 1 or 2
end

---@return 1|2 有足够的材料|没有足够的材料
function GuideDataCustom:HasEnoughMystialWeapon()
    local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
    local ownedWeaponSkins  = Server.CollectionServer:GetOwnedWeaponSkins()

    local counter = 0
    for skinId, group in pairs(ownedWeaponSkins) do
        if Server.CollectionServer:IsVisibleProp(skinId) and ItemHelperTool.IsMysticalSkin(skinId)  then
            local quality =  ItemHelperTool.GetQualityTypeById(skinId)  
            if quality < 5 then
                counter = counter + table.nums(group)
            end
        end
    end
    return counter >= GuideConfig.guideMysticalWorkshopMinWeaponAmount and 1 or 2
end


--@return 1|2 已经在游戏内|不在
function GuideDataCustom:IsPlayerInGame()
    return Server.AccountServer:IsPlayerInGame() and 1 or 2
end

function GuideDataCustom:SwitchGuideHDPopButton(params)
    local bOpen = tonumber(params[1])
    if bOpen == 0 then
        Module.Guide:CloseGuideHDPopButtonUI()
    elseif bOpen == 1 then
        local word = tostring(params[2])
        Module.Guide:OpenGuideHDPopButtonUI(word)
    end

    return 1
end

function GuideDataCustom:SwitchGuideHDPopButtonByActionName(params)
    local bOpen = tonumber(params[1])
    if bOpen == 0 then
        Module.Guide:CloseGuideHDPopButtonUI()
    elseif bOpen == 1 then
        local actionName = tostring(params[2])
        local bValid = true
        if  not actionName or actionName == "" then
            bValid = false
        end

        local keyMapping = GuideLogic.GetActionKeyMapping(actionName)

        if not keyMapping  or not keyMapping.Key or not keyMapping.Key.KeyName   then
            log("keyMapping or key is nil")
            bValid = false
        end

        local keyName = keyMapping.Key.KeyName

        -- dexzhou, currently not support 
        if keyName ~= "M"  and keyName ~= "X" then
            bValid = false
            log("keyname for pop button is not support currently, 2024/12/24, keyName: ", keyName)
        end

        if bValid then
            Module.Guide:OpenGuideHDPopButtonUI(keyName)
            return 1
        else 
            log("that key is not valid: ", keyName)
            return 2
        end
    end
    return 2
end


---@return integer 1-有|2-没有
function GuideDataCustom:IsWarehouseContainDoorKey()
    for _, item in Server.InventoryServer:GetItemsIterator() do
        ---@type SlotConfig
        if item:GetFeatureType() == EFeatureType.Key then
            local itemSlot = item.InSlot
            -- main warehouse
            if itemSlot and itemSlot.SlotType == ESlotType.MainContainer then
                return 1
            end
        end
    end
    return 2
end

---@return integer 1-展开|2-收起
function GuideDataCustom:IsItemDetailPanelUnFold()
    return Module.ItemDetail:GetIsFold() and 2 or 1
end


function  GuideDataCustom:IsInWarehouseStackUI()
    return Facade.UIManager:GetCurrentStackUIId() == UIName2ID.WarehouseMain and 1 or 2
end


-- *HD*
-- check interaction type 
---@param param string[]  uiWidgetId 传入 UDFHDInteractorPanelItem 的路径
function GuideDataCustom:IsLootingItemHudValidToGuide(param)
    log("GuideDataCustom:IsLootingHudValidToGuide -> checking uiWidgetId: " , uiWidgetId)
    local uiWidgetId = tonumber(param[1])

    local widgetConfig = GuideConfig.TableGuideWidgetConfig[uiWidgetId]
    local targetWidget, targetUI, targetWidgetList = GuideLogic.GetWidgetByPath(
        widgetConfig.BpPath, widgetConfig.WidgetName, widgetConfig.UIName, widgetConfig.IsHudUI)

    if not targetWidget then
        log("target widget is nil")
        return 2
    end

    log("target widget, RenderOpacity:", targetWidget.RenderOpacity, "Visibility:", targetWidget.Visibility)
    if targetWidget.RenderOpacity == 0 or
        targetWidget.Visibility == ESlateVisibility.Hidden or
        targetWidget.Visibility == ESlateVisibility.Collapsed
    then
        return 2
    end

    -- check the interact item, current assume it's a UDFHDInteractorPanelItem
    local hudContext  = targetWidget.HUDContext
    if not hudContext then
        log("cannot get hudContext, please confirm the right class")
        return 2
    end
    local layout = hudContext.layout
    if not layout then
        log("cannot get layout, please confirm the right class")
        return 2
    end

    print ("layout: ", layout)
    if  
        -- layout == EInteractorPanelLayout.InteractorPanelLayout_Pickup or
        layout == EInteractorPanelLayout.InteractorPanelLayout_Container  or
        layout == EInteractorPanelLayout.InteractorPanelLayout_DeadBody or
        -- layout == EInteractorPanelLayout.InteractorPanelLayout_SingleItemContainer or
        layout == EInteractorPanelLayout.InteractorPanelLayout_PickupContainer
    then
        log("ok")
        return 1
    end


    return 2
end

-- *MOBILE*
-- check UDFMInteractorButton interaction  type 
---@return integer
--- 1 button 1
--- 2 button 2
--- 3 不可用
function GuideDataCustom:IsUDFMInteractorButtonValidToGuide()
    log("GuideDataCustom:IsLootingHudValidToGuide")
    

    local isInteractWidgetItemValid = function(widgetName, uiName)
        log( widgetName, uiName)
        local targetWidget = GuideLogic.GetWidgetByPath("", widgetName, uiName, true)

        if not targetWidget then
            log("target widget is nil")
            return false
        end

        log("target widget, RenderOpacity:", targetWidget.RenderOpacity, "Visibility:", targetWidget.Visibility)
        if targetWidget.RenderOpacity == 0 or
            targetWidget.Visibility == ESlateVisibility.Hidden or
            targetWidget.Visibility == ESlateVisibility.Collapsed
        then
            return false
        end

        -- check the interact item, current assume it's a UDFHDInteractorPanelItem
        local curInteractInfo = targetWidget.CurrInteractableInfo
        if not curInteractInfo then
            log("cannot get  curInteractInfo, please confirm the right class")
            return false
        end
        local type = curInteractInfo.InteractorType
        if not type then
            log("cannot get type, please confirm the right class")
            return false
        end

        print("interactorType: ", type)
        if type == EInteractorType.InteractorType_BoxOrBag or
            type == EInteractorType.InteractorType_DeadBody or
            type == EInteractorType.InteractorType_SingleItem or
            type == EInteractorType.InteractorType_SingleItemContainer or
            type == EInteractorType.InteractorType_HackPC
        then
            log("ok")
            return true
        end

        return false
    end

    local widgetName1 = "WBP_IteractPanel.InteractorButton0"
    local widgetName2 = "WBP_IteractPanel.InteractorButton1"
    local uiName =  "MobileHUD_Interactor"

    if isInteractWidgetItemValid(widgetName1, uiName) == true then
        return 1
    elseif isInteractWidgetItemValid(widgetName2, uiName) == true then
        return 2
    end

    return 3
end


function GuideDataCustom:IsBagUIOpen()
    return Module.Guide.Field.lootingInGameOpenState and 1 or 2
end

function GuideDataCustom:PlaySettlementVideoGuide(param)
    local esacpeReason = tonumber(param[1])
    return Module.Guide:PlayEvacuateTeaching(1, esacpeReason) 
        and 1 -- 播放成功
        or 2 -- 播放失败
end

function GuideDataCustom:RecordSettlementVideoGuidePlayed(param)
    local esacpeReason = tonumber(param[1])
    GuideLogic.RecordSettlementVideoGuidePlayed(esacpeReason)
    return 1
end


function GuideDataCustom:ChangeBlackSiteTabIndex(param)
    local idx = tonumber(param[1])

    local curIdx = Module.BlackSite:GetCurTabIdx()
    if idx ~= curIdx then
        loginfo("GuideDataCustom:ChangeBlackSiteTabIndex", idx, curIdx)
        -- Module.BlackSite:JumpToMainPanelByUIIdx(idx)
        Module.CommonBar:CheckTopTabGroup(idx, true, 2, false)
    end
    return 1
end


function GuideDataCustom:TryAddGuideWidgetLootingMove2SafeBoxItem()
    if not Module.Guide.Field.lootingInGameOpenState == true then
        err("TryAddGuideWidgetLootingMove2SafeBoxItem lootingInGameOpenState is false")
        return false
    end

    local data = Module.Guide.Field.SafeBoxGuideData:Get()
    if data == nil then
        err("TryAddGuideWidgetLootingMove2SafeBoxItem SafeBoxGuideData is nil")
        return 2
    end
    local gid = data.gid
    local itemSlotType = data.slotType
    if gid == nil or itemSlotType == nil then
        err("TryAddGuideWidgetLootingMove2SafeBoxItem gid or itemSlotType is nil")
        return 2
    end

    GuideLogic.RemoveGuideWidgetProxy(GuideConfig.EGuideProxyWidget.guideProxyLootingBindItem)

    local containerView = GuideLogic.GetLootingContainerView(itemSlotType, true)
    if not containerView then
        err("TryAddGuideWidgetLootingMove2SafeBoxItem containerView is nil")
        return 2
    end

    local slotView = containerView:GetContainerSlotView()
    if not slotView then
        err("TryAddGuideWidgetLootingMove2SafeBoxItem slotView is nil")
        return 2
    end

    local itemView = slotView:GetViewByGID(gid)
    if not itemView then
        err("TryAddGuideWidgetLootingMove2SafeBoxItem itemView is nil")
        return 2
    end

    GuideLogic.AddGuideWidgetProxy(GuideConfig.EGuideProxyWidget.guideProxyLootingBindItem, itemView)
    Module.Guide.Field.SafeBoxGuideData:Reset() -- consuming this drag/moved item 

    return 1
end

function GuideDataCustom:DispatchEvacuationPointGuideType()
    ---@type EGuideEvacuationPointType
    local guideType = Module.Guide.Field.LastEvacuationPointGuideType:Get()
    log("DispatchEvacuationPointGuideType", guideType)

    if guideType == nil then
        return 1
    end

    if guideType == GuideConfig.EEvacuationPointType.Pay then
        return  2
    elseif guideType == GuideConfig.EEvacuationPointType.Random then
        return 3
    elseif guideType == GuideConfig.EEvacuationPointType.DropBag then
        return 4
    elseif guideType == GuideConfig.EEvacuationPointType.WeightLimit then
        return 5
    elseif guideType == GuideConfig.EEvacuationPointType.Switcher then
        return 6
    end
    warn("DispatchEvacuationPointGuideType not a valid  EEvacuationPointType")
    return  1
end


function GuideDataCustom:ReportEvacuationPointGuide()
    ---@type EGuideEvacuationPointType
    local guideType = Module.Guide.Field.LastEvacuationPointGuideType:Get()
    log("ReportEvacuationPointGuideType", guideType)

    if guideType == nil then
        log("ReportEvacuationPointGuideType is nil")
    else
        LogAnalysisTool.DoSendGuideEvacuationPointInstructReportLog(guideType)
        Module.Guide.Field.LastEvacuationPointGuideType:Set(nil)
    end

    return 1
end

function GuideDataCustom:SwitchCommbarGroup(params)
    local index = tonumber(params[1])
    local level = tonumber(params[2])
    assert(index and level, "GuideDataCustom:SwitchCommbarGroup index or level is nil")
    Module.CommonBar:CheckTopTabGroup(index, true, level, false)
    return 1
end

-- was config in GuideConfigForMobile and GuideConfigForHD
function GuideDataCustom:ChooseHeroSkillAgainstEntry()
    local bNeed = GuideLogic_CheckCondition.NeedSkillAgainstGuide()
    assert(bNeed == true, "GuideDataCustom:ChooseHeroSkillAgainstEntry bNeed is nil, must be true to enter current logic!!")

    ---1. 拿到当前的死亡技能相关
    ---2. 判断哪一个优先级最高(按优先级遍历)
    ---3. 判断是否有视频, 得出下一步idx
    local entry = GuideLogic.GetFirstAvaliableSkillAgainstEntry()
    if not entry then
        return 1
    end
    assert(entry.branch > 1, "GuideDataCustom:ChooseHeroSkillAgainstEntry entry.branch is nil")

    --- 4. 记录一下要播放的entry
    Module.Guide.Field.lastPlayedSkillAgainstEntry = entry

    return entry.branch
end

function GuideDataCustom:OnSkillAgainstVideoStepEnd()

    if IsHD() then
        -- Q: PC 结束后可以点击重播, 真正清理的时机是？
        -- A: 结束局内结算流程, 回到sol大厅(仅sol引导)
        -- assert(false)
        warn("GuideDataCustom:OnSkillAgainstVideoStepEnd is not support in HD")
    else
        GuideLogic.OnSkillAgainstVideoStepEnd()
    end

    return 1
end

function GuideDataCustom:IsLootingUIOpen()
    return Module.Guide.Field.lootingInGameOpenState and 1 or 2
end


return GuideDataCustom
