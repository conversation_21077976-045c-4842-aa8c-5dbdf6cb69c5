----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMQuest)
----- LOG FUNCTION AUTO GENERATE END -----------



local QuestSeasonActionPhase = require "DFM.Business.Module.QuestModule.UI.Season.QuestSeasonActionPhase"
---@class QuestSeasonListPanel : LuaUIBaseView

local FAnchors = import "Anchors"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"

local QuestSeasonListPanel = ui("QuestSeasonListPanel")

function QuestSeasonListPanel:Ctor()
    
    self._wtCanvasPanel = self:Wnd("DFCanvasPanel_126", UIWidgetBase)

    self._wtStageIcon = {
        self:Wnd("WBP_SeasonalTasks_PhaseItem", QuestSeasonActionPhase),
        self:Wnd("WBP_SeasonalTasks_PhaseItem_1", QuestSeasonActionPhase),
        self:Wnd("WBP_SeasonalTasks_PhaseItem_2", QuestSeasonActionPhase),
        self:Wnd("WBP_SeasonalTasks_PhaseItem_3", QuestSeasonActionPhase),
    }

    self._lineInfo = nil
    self._curStage = nil
    self._questId = nil
    self._selectedWidget = nil

    if IsHD() then
        self._wtPhasePanel = self:Wnd("DFVerticalBox_0", UIWidgetBase)
    end
end

function QuestSeasonListPanel:OnInitExtraData(lineInfo, stageId, questId)
    self._lineInfo = lineInfo
    if stageId ~= nil then
        self._curStage = lineInfo:GetStageInfoByID(stageId)
    end
    if questId ~= nil then
        self._questId = questId
    end
end

function QuestSeasonListPanel:OnShowBegin()
    self:_InitStageLists()
    self:_UpdateContent()
    self:_AddListeners()
    if IsHD then
        self:_EnableGamepad()
    end

    if DFHD_LUA == 1 then
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.DefaultSecondary)
    else
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.HideInfo)
    end
    Module.CommonBar:RegStackUITopBarTitle(self, Module.Quest.Config.Loc.QuestSeasonMission)

end

function QuestSeasonListPanel:OnHide()
    self:RemoveAllLuaEvent()
    if IsHD then
        self:_DisableGamepad()
    end
    self._questId = nil
end

function QuestSeasonListPanel:_AddListeners()
    self:AddLuaEvent(Module.Quest.Config.evtQuestSeasonPhaseItemSelected, self._OnStageIconClicked, self)
end

function QuestSeasonListPanel:_InitStageLists()

    local bNoSelectedStage = self._curStage == nil

    local stageIdList = self._lineInfo:GetStageIDList()
    local maxOrder = 0
    for index, value in ipairs(stageIdList) do

        local stageInfo = self._lineInfo:GetStageInfoByID(value)
        local icon = self._wtStageIcon[stageInfo.order]

        icon:SetStageInfo(stageInfo)

        if self._lineInfo:IsUnLockByStageID(value) then
            icon:SetIsUnlocked(true)
            icon:SetProgressText(self._lineInfo:CalGainStarByStageID(stageInfo.stageID),self._lineInfo:CalTotalStarByStageID(stageInfo.stageID))
            if bNoSelectedStage then
                if self._curStage == nil or stageInfo.order > self._curStage.order then
                    self._curStage = stageInfo
                end
            end
        else
            icon:SetIsUnlocked(false)
        end
        icon:SetSelectedState(false)
        if stageInfo.order > maxOrder then
            maxOrder = stageInfo.order
        end
    end

    self._wtStageIcon[maxOrder]:HideArrow()

    self._selectedWidget = self._wtStageIcon[self._curStage.order]
    self._selectedWidget:SetSelectedState(true)

end

function QuestSeasonListPanel:_UpdateContent()
    
    Facade.UIManager:RemoveSubUIByParent(self, self._wtCanvasPanel)
    self._listPanel = nil
    local uiIns, instanceID
    if self._lineInfo:IsUnLockByStageID(self._curStage.stageID) then
        if self._questId then
            uiIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.QuestSeasonListNormal, self._wtCanvasPanel, nil, self._lineInfo, self._curStage.stageID, self._questId, self)
        else
            uiIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.QuestSeasonListNormal, self._wtCanvasPanel, nil, self._lineInfo, self._curStage.stageID, nil, self)
        end
        self._listPanel = getfromweak(uiIns)
    else
        uiIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.QuestSeasonListLocked, self._wtCanvasPanel, nil, self._lineInfo, self._curStage.stageID)
    end

    uiIns = getfromweak(uiIns)
    local canvasSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(uiIns)
    if not canvasSlot then return end
    local commonAnchor = FAnchors()
    local commonOffset = FMargin(0, 0, 0, 0)
    commonAnchor.Minimum = LuaGlobalConst.TOP_LEFT_VECTOR
    commonAnchor.Maximum = LuaGlobalConst.BOTTOM_RIGHT_VECTOR
    canvasSlot:SetAnchors(commonAnchor)
    canvasSlot:SetOffsets(commonOffset)

end

function QuestSeasonListPanel:_OnStageIconClicked(stageInfo)

    self._selectedWidget:SetSelectedState(false)
    self._curStage = stageInfo
    self._selectedWidget = self._wtStageIcon[stageInfo.order]
    self._selectedWidget:SetSelectedState(true)    
    self:_UpdateContent()

end

-------- Gamepad ------------

function QuestSeasonListPanel:_EnableGamepad()
    self._wtNavGroup1 = WidgetUtil.RegisterNavigationGroup(self._wtPhasePanel, self, "Hittest")
    if self._wtNavGroup1 then
        self._wtNavGroup1:AddNavWidgetToArray(self._wtStageIcon[1])
        self._wtNavGroup1:AddNavWidgetToArray(self._wtStageIcon[2])
        self._wtNavGroup1:AddNavWidgetToArray(self._wtStageIcon[3])
        self._wtNavGroup1:AddNavWidgetToArray(self._wtStageIcon[4])
    end  
end

function QuestSeasonListPanel:_DisableGamepad()
    WidgetUtil.RemoveNavigationGroup(self)
    self._wtNavGroup1 = nil
end

return QuestSeasonListPanel