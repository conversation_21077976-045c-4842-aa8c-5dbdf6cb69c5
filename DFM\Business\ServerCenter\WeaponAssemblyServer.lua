----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaSWeaponAssembly)
----- LOG FUNCTION AUTO GENERATE END -----------

---------------------------------------------------------------------------------
--- WeaponAssemblyServer 依赖 ArmedForceServer|InventoryServer
---------------------------------------------------------------------------------


local WeaponAssemblyServer = class("WeaponAssemblyServer", require("DFM.YxFramework.Managers.Server.ServerBase"))
require "DFM.Business.DataStruct.WeaponAssemblyStruct.PartSocket"
require "DFM.Business.DataStruct.WeaponAssemblyStruct.AssembleGunData"
local CounterEvent = require "DFM.Business.DataStruct.Common.CounterEvent"

local EWeaponinfoCounterEvent = {
    CSWAssemblySkinInfoGetRes = 1,
    CSDepositGetPropsRes = 2,
    CSMPDepositGetPropsRes = 3,
}


function WeaponAssemblyServer:Ctor()
    self.Events = {
        evtOnServerPresetGunUpdate = LuaEvent:NewIns("WeaponAssemblyServer.evtOnServerPresetGunUpdate"),
        evtOnGunUpdateAdapterStyle = LuaEvent:NewIns("WeaponAssemblyServer.evtOnGunUpdateAdapterStyle"), --枪支上的配件刷新
        evtCSWAssemblyDepositPropUpdateRes = LuaEvent:NewIns("WeaponAssemblyServer.evtCSWAssemblyDepositPropUpdateRes"), --整枪更新
        evtCSWAssemblyModifySkinRes = LuaEvent:NewIns("WeaponAssemblyServer.evtCSWAssemblyModifySkinRes"),       -- 炫彩皮肤变化
        evtOnSOLWeaponSkinUpdated = LuaEvent:NewIns("evtOnSOLWeaponSkinUpdated"),   -- SOL皮肤信息变化
        evtOnMPWeaponPartDataUpdated = LuaEvent:NewIns("evtOnMPWeaponPartDataUpdated"),   -- MP武器配件数据更新
    }

    self._serverGunPresets ={}
    self.unlocked_styles = {}
    -- 武器等级解锁配件映射表，key为weaponid，value为MPWeaponLevelParts行，index为level，默认Level连续，否则会有问题
    self._weaponLevelUnLockMap = {}
    -- 武器解锁配件映射表，key为武器id，value为解锁配件id数组，按照解锁等级排序
    self._weapon2AllPartIdsArray = {}
      -- 武器解锁配件映射表，key为武器id，value为解锁配件id和解锁等级Map
    self._weapon2PartsUnLockLvlMap = {}
    -- 经验卡对应经验加成
    self._weaponExpItems = {}
    self._weaponItemId2Exp = {}

    ---武器炫彩皮肤数据
    self._weaponSkinInfos = {}      ---@type table<number, pb_WeaponSkinInfo> 

    self._weaponinfoCounterEvent = CounterEvent:NewIns(3, false)
    self._weaponinfoCounterEvent:BindCallbcak(CreateCallBack(self.BuildWeaponInfo2Inventory, self))
    --热更监听器
    self:_AddHotfixHandle(true)
end

------------------------------------ Override function ------------------------------------
function WeaponAssemblyServer:OnInitServer()
    --self:_OnHotfixWeaponLevelDT()
    --热更监听器
    self:_AddHotfixHandle(true)
end

function WeaponAssemblyServer:OnDestroyServer()
    self:_AddHotfixHandle()
end

function WeaponAssemblyServer:OnLoadingLogin2Frontend(gameFlowType)
    self:_InternalLoadDatas()
end

function WeaponAssemblyServer:OnLoadingGame2Frontend(gameFlowType)
    self:_InternalLoadDatas()
end

function WeaponAssemblyServer:OnLoadingFrontend2Game()
    self:_InternalResetDatas()
end

--热更监听器
function WeaponAssemblyServer:_AddHotfixHandle(isAddHotHandle)
    if isAddHotHandle then
        if self._weaponLeveHotHandle == nil then
            self._weaponLeveHotHandle = Facade.DataTableHotfixManager:AddHotfixCallback("WeaponSystem/MPWeaponLevelParts", self._OnHotfixWeaponLevelDT, self)
        end
    else
        if self._weaponLeveHotHandle then
            Facade.DataTableHotfixManager:RemoveHotfixCallback(self._weaponLeveHotHandle)
            self._weaponLeveHotHandle = nil
        end
    end
end

function WeaponAssemblyServer:_InternalResetDatas()
    self._weaponLevelUnLockMap = {}
    self._weapon2AllPartIdsArray = {}
    self._weapon2PartsUnLockLvlMap = {}
    self._weaponItemId2Exp = {}
    self._weaponExpItems = {}
end

function WeaponAssemblyServer:_InternalLoadDatas()
    self:_InternalResetDatas()
    local t = Facade.TableManager:GetTable("WeaponSystem/MPWeaponLevelParts")
    for _, row in t:LessGCPairs() do
        if row then
            local recFunctionId = tonumber(row.RecFunctionId)
            if recFunctionId then
                if self._weaponLevelUnLockMap[recFunctionId] == nil then
                    self._weaponLevelUnLockMap[recFunctionId] = {}
                end
                self._weaponLevelUnLockMap[recFunctionId][row.Level] = {ID = row.ID, RecFunctionId = row.RecFunctionId, 
                Level = row.Level, Exp = row.Exp, ExpSum = row.ExpSum, UnlockParts = row.UnlockParts}
                -- table.insert(self._weaponLevelUnLockMap[recFunctionId], row)
                if self._weapon2AllPartIdsArray[recFunctionId] == nil then
                    self._weapon2AllPartIdsArray[recFunctionId] = {}
                    self._weapon2PartsUnLockLvlMap[recFunctionId] = {}
                end
                for _, partsId in ipairs(row.UnlockParts) do
                    local partsIdNum = tonumber(partsId)
                    if partsIdNum then
                        table.insert(self._weapon2AllPartIdsArray[recFunctionId], partsIdNum)
                        self._weapon2PartsUnLockLvlMap[recFunctionId][partsIdNum] = row.Level
                    end
                end
            end
        end
    end
    for weaponId, partIdArray in pairs(self._weapon2AllPartIdsArray) do
        table.sort(partIdArray, function(id1, id2)
            return self._weapon2PartsUnLockLvlMap[weaponId][id1] < self._weapon2PartsUnLockLvlMap[weaponId][id2]
        end)
    end
    for _, row in pairs(Facade.TableManager:GetTable("WeaponSystem/WeaponExpItem")) do
        if row then
            self._weaponItemId2Exp[row.ItemID] = row.AddWeaponExp
            table.insert(self._weaponExpItems, row)
        end
    end
    table.sort(self._weaponExpItems, function (expCfg1, expCfg2)
        return expCfg1.AddWeaponExp < expCfg2.AddWeaponExp
    end)
end

--更新武器配件数据
function WeaponAssemblyServer:_OnHotfixWeaponLevelDT()
    --数据整理
    self:_InternalLoadDatas()
    --数据更新发布事件
    self.Events.evtOnMPWeaponPartDataUpdated:Invoke()
end

--检查MP武器配件是否在MPWeaponLevelParts(武器解锁配件表)中
function WeaponAssemblyServer:CheckWeaponPartByMPTable(weaponID, partID)
    if self._weaponLevelUnLockMap == nil or weaponID == nil or partID == nil then
        return false
    end
    local weapon = self._weaponLevelUnLockMap[tonumber(weaponID)]
    for level, parts in pairs(weapon or {}) do
        if parts then
            for key, partId in ipairs(parts.UnlockParts or {}) do
                if partId == tostring(partID) then
                    return true
                end
            end
        end
    end
    return false
end

function WeaponAssemblyServer:FetchServerData()
    self:PreFetchServerData()

    self:ReqGetPresetData()
    self:ReqStyleDatas()
    self:C2SCSWAssemblySkinInfoGetReq()
end

function WeaponAssemblyServer:PreFetchServerData()
    self._weaponinfoCounterEvent:Reset()
    self._weaponSkinInfos = {}
end

function WeaponAssemblyServer:BuildWeaponInfo2Inventory()

end

function WeaponAssemblyServer:InvokeWeaponinfoCounterEvent_CSWAssemblySkinInfoGetRes()
    self._weaponinfoCounterEvent:Invoke(EWeaponinfoCounterEvent.CSWAssemblySkinInfoGetRes)
end

function WeaponAssemblyServer:InvokeWeaponinfoCounterEvent_CSDepositGetPropsRes()
    self._weaponinfoCounterEvent:Invoke(EWeaponinfoCounterEvent.CSDepositGetPropsRes)
end

function WeaponAssemblyServer:InvokeWeaponinfoCounterEvent_CSMPDepositGetPropsRes()
    self._weaponinfoCounterEvent:Invoke(EWeaponinfoCounterEvent.CSMPDepositGetPropsRes)
end

function WeaponAssemblyServer:InvokeWeaponinfoCounterEvent(key)
    self._weaponinfoCounterEvent:Invoke(key)
end

--获取枪支预设
function WeaponAssemblyServer:ReqGetPresetData()
     --机匣对应的自定义预设
     local req = pb.CSWAssemblyGetDesignReq:New()
     local fOnResCallback = function(res)
        if res.result and res.result ~= 0 then --超时
            return
        end
        if not res.result then
            return
        end
        self._serverGunPresets = res.designs
        self.Events.evtOnServerPresetGunUpdate:Invoke(self._serverGunPresets)
     end

     req:Request(fOnResCallback)
end

--获取炫彩皮肤数据
function WeaponAssemblyServer:C2SCSWAssemblySkinInfoGetReq()
    --机匣对应的自定义预设
    local req = pb.CSWAssemblySkinInfoGetReq:New()
    ---@param pb_CSWAssemblySkinInfoGetRes
    local fOnCSWAssemblySkinInfoGetRes = function(res)
       if res.result and res.result ~= 0 then
           return
       end
       if not res.result then
           return
       end
       
       self:AddWeaponSkinInfos(res.infos)
       --self:InvokeWeaponinfoCounterEvent_CSWAssemblySkinInfoGetRes()
    end
    req:Request(fOnCSWAssemblySkinInfoGetRes)
end


--@param unequipPos 局外仓库，卸载到某个指定的不定，可以为nil, type = pbLocation
--@param bagId 局外仓库，指定背包，目前用于大战场
--@swapGunPropInfo 将一个配件，更新到另外一把枪上的时候，目标枪支的PropInfo
function WeaponAssemblyServer:ReqAssembleGun(newGun, fCallback, oldPropId, unequipPos, bagId,swapGunPropInfo, local_prop, pass_through)
    local req = pb.CSWAssemblyDepositPropUpdateReq:New()
    req.prop = newGun
    req.local_prop = local_prop
    req.pass_through = pass_through
    req.oldPropId = oldPropId
    if unequipPos and swapGunPropInfo == nil then
        req.unequip_pos = {unequipPos}
    end
    if bagId then
        req.bag_id = bagId
    end

    if swapGunPropInfo then
        req.swapped_peer_gun = swapGunPropInfo
    end
    local bShowErrMgs = true

    ---@param res pb_CSWAssemblyDepositPropUpdateRes
    local fOnResCallback = function(res)
        if res.result ~= 0 then
            local bError = true
            if res.result == Err.WassNoChange then
                bError = false
            elseif res.result == Err.WassDepositNoEnoughSpace then
                LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.WeaponAssemblyUnEnchangeTip)
            else
                Facade.ProtoManager:ManuelHandleErrCode(res)
            end
            
            if bShowErrMgs and bError then
                local text = "AssembleFaild:"
                if res.errmsg then
                    text = text .. res.errmsg
                    LuaGlobalEvents.evtServerShowTip:Invoke(text)
                end
            end
            if fCallback then
                fCallback(res.result)
            end
            return
        end

        local gun = AssembleGunData.CreateGunFromPropInfo(newGun) ---@type AssembleGunData
        if fCallback then
            fCallback(res.result, gun)
        end
        --- MP
        Server.InventoryServer:ProcessPbMPDataChange(res.mp_change, EMPChangeReason.Assembly)
        Server.ArmedForceServer:ApplyCurrentPresetToInvOnDataUpdated()
        --- SOL
        Server.InventoryServer:ProcessPbDataChangeByRes(res)
        self.Events.evtCSWAssemblyDepositPropUpdateRes:Invoke(res)
    end
    local res = req:Request(fOnResCallback,{bShowErrTip = false})
end

--卸载枪支的某个部位
function WeaponAssemblyServer:ReqAssemblyDepositUnEquip(nodeid, nodegid, slot, Callback)
    local req = pb.CSWAssemblyDepositUnEquipReq:New()
    req.node_id = nodeid
    req.node_gid = nodegid
    req.slot = slot

    local OnResCallback = function(res)
        if res.result ~= 0 then            
            if res.result == Err.WassDepositNoEnoughSpace then
                LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.WeaponAssemblyCantInstallPart)
            else
                Facade.ProtoManager:ManuelHandleErrCode(res)
            end
            if Callback~=nil then
                Callback(res.result)
            end
        else
            if Callback~=nil then
                Callback(res.result)
            end
            --- SOL
            Server.InventoryServer:ProcessPbDataChangeByRes(res)
        end
    end
    req:Request(OnResCallback,{bShowErrTip=false})
end

--更新枪支预设信息
function WeaponAssemblyServer:ReqAssemblyUpdatePresetInfo(design_name,design,PresetIndex,Callback)
    local req = pb.CSWAssemblyUpdateDesignReq:New()
    req.design_name = design_name
    req.design = design

    local OnResCallback = function(res)
        if res.result ~= 0 then
            loginfo("Error: ", res.result)          
            Callback(false)
        else
            local newDesign = res.changes.prop_changes[1].prop
            local gun = AssembleGunData.CreateGunFromPropInfo(newDesign, design_name)
            gun.Index = PresetIndex
            --local gun = { key = design_name, Rec = newDesign.id, Index = PresetIndex, design = newDesign }
            Callback(true,gun)
        end
        
    end
    req:Request(OnResCallback)
end

--TODO:整枪更换
function WeaponAssemblyServer:ReqWAssemblyDepositChange(RecId,RecGid,PartItemId,PartItemGid,TargetItemId,Callback)
    local req = pb.CSWAssemblyDepositChangeReq:New()
    req.root_id = RecId
    req.root_gid = RecGid
    req.node_id = PartItemId
    req.node_gid = PartItemGid
    req.target_id = TargetItemId

    local OnResCallback = function(res)
        if res.result ~= 0 then
            loginfo("Error: ", res.result)           
            Callback(res.result)
        else
            local newDesign = res.changes.prop_changes[1].prop
            local gun = AssembleGunData.CreateGunFromPropInfo(newDesign)
            Callback(res.result,gun)
        end
    end
    req:Request(OnResCallback)
end

-------- 款式系统
function WeaponAssemblyServer:ReqStyleDatas()
    local req = pb.CSWAssemblyGetUnlockedStylesReq:New()
    local fOnResCallback = function(res)
        if res.result == 0 then
            self.unlocked_styles = res.unlocked_styles
        end             
    end
    local res = req:Request(fOnResCallback)
end


---切换枪支款式
function WeaponAssemblyServer:ReqEquipStyleData(rootId,rootGid,itemId,ItemGid,styleInfos,fCallback)
    local req = pb.CSWAssemblyEquipPropStyleReq:New()
    req.root_id = rootId
    req.root_gid = rootGid
    req.node_id = itemId
    req.node_gid = ItemGid
    if rootId == 0 or rootGid == 0 then
        req.root_id = itemId
        req.root_gid = ItemGid
        req.node_id = 0
        req.node_gid = 0
    end

  
    req.styles = styleInfos
    local fOnResCallback = function(res)
        if fCallback~= nil then
            fCallback(res)
        end

        if res.result == 0 then
            self.Events.evtOnServerPresetGunUpdate:Invoke()
        end
        Server.InventoryServer:AdapterStyleUpdateFromAssemble(res)
    end 
    req:Request(fOnResCallback)
end

function WeaponAssemblyServer:ReqUnlockStyle(styleId,fCallback)
    -- body
    local req = pb.CSWAssemblyUnlockStyleReq:New()
    req.style_id = {styleId}   

    local function fOnResCallback(res)
        if res.result == 0 then
            table.insert(self.unlocked_styles,styleId)
            if fCallback~=nil then
                fCallback(res)
            end
        end
    end
    req:Request(fOnResCallback)
end
-------- 款式系统End


-- 根据武器id和经验总值获取当前等级
function WeaponAssemblyServer:GetWeaponLevelByExp(weaponId, totalExp)
    local weaponLvlCfg = self:GetWeaponLevelUnLockData(weaponId)
    local curLevel = 1
    if weaponLvlCfg then
        for _, lvlData in ipairs(weaponLvlCfg) do
            -- if lvlData.ExpSum <= totalExp and lvlData.ExpSum + lvlData.Exp > totalExp then
            --     curLevel = lvlData.Level
            -- end
            if lvlData.ExpSum and lvlData.ExpSum <= totalExp then
                curLevel = lvlData.Level
            else
                break
            end
        end
    end
    return curLevel
end

-- azhengzheng 获取武器下一个所需经验以及升级到下一个已获取经验
function WeaponAssemblyServer:GetWeaponMsgByExp(weaponId, totalExp)
    local weaponLvlCfg = self:GetWeaponLevelUnLockData(weaponId)
    local preExp = 0
    local needExp = 0
    local curLevel = 1
    local remainExp = 0
    local isMaxLevel = true
    if weaponLvlCfg then
        for _, lvlData in ipairs(weaponLvlCfg) do
            if lvlData.ExpSum <= totalExp then
                preExp = lvlData.ExpSum
                curLevel = lvlData.Level
                remainExp = totalExp - lvlData.ExpSum
            else
                needExp = lvlData.ExpSum - preExp
                isMaxLevel = false
                break
            end
        end
    end
    return curLevel, isMaxLevel, remainExp, needExp
end

function WeaponAssemblyServer:GetWeaponLevelMsgByExp(weaponId, exp)
    -- azhengzheng:这里先判空一下，防止最新配置未进包导致报错！
    if not weaponId or not exp or not self._weaponLevelUnLockMap[weaponId] then
        return 1, 1
    end

    local curLevel = 0
    local remainExp = 0
    for _, value in ipairs(self._weaponLevelUnLockMap[weaponId]) do
        if exp < value.ExpSum then
            return curLevel, remainExp
        end
        curLevel = value.Level
        remainExp = exp - value.ExpSum
    end
    return curLevel, remainExp
end

function WeaponAssemblyServer:GetWeaponNeedExpByLevel(weaponId, level)
    return self._weaponLevelUnLockMap[weaponId][level] and self._weaponLevelUnLockMap[weaponId][level].Exp
end

-- 根据武器id和经验总值获取解锁配件列表
function WeaponAssemblyServer:GetWeaponUnLockPartsByExp(weaponId, newExp, oldExp)
    oldExp = setdefault(oldExp, 0)
    local weaponLvlCfg = self:GetWeaponLevelUnLockData(weaponId)
    local unlockParts = {}
    if weaponLvlCfg then
        for _, lvlData in ipairs(weaponLvlCfg) do
            if lvlData.ExpSum > newExp then
                break
            elseif lvlData.ExpSum > oldExp then
                for _, partsId in ipairs(lvlData.UnlockParts) do
                    table.insert(unlockParts, partsId)
                end
            end
        end
    end
    return unlockParts
end

-- 根据武器id获取武器全部等级解锁信息
function WeaponAssemblyServer:GetWeaponLevelUnLockData(weaponId)
    return self._weaponLevelUnLockMap[weaponId] or {}
end

--获取所有武器信息
function WeaponAssemblyServer:GetAllWeaponData()
    return self._weaponLevelUnLockMap
end

-- 获取武器所有可装配配件
function WeaponAssemblyServer:GetAllWeaponPartsByWeaponId(weaponId)
    return self._weapon2AllPartIdsArray[weaponId] or {}
end

-- 获取可装配该配件的武器
function WeaponAssemblyServer:GetAllWeaponByWeaponPartsId(weaponPartId)
    local weaponList = {}
    for weaponId, weaponParts in pairs(self._weapon2AllPartIdsArray or {}) do
        for _, _weaponPartId in pairs(weaponParts or {}) do
            if _weaponPartId == weaponPartId then
                local level = self._weapon2PartsUnLockLvlMap[weaponId][weaponPartId]
                local weapon = {
                    id = weaponId,
                    weaponPartId = weaponPartId,
                    level = level,
                }
                table.insert(weaponList, weapon)
                break
            end
        end
    end
    return weaponList
end

-- 获取配件解锁等级
function WeaponAssemblyServer:GetWeaponPartsUnLockLvl(weaponId, partsId)
    local partsUnLockLvlMap = self._weapon2PartsUnLockLvlMap[weaponId]
    if partsUnLockLvlMap then
        return partsUnLockLvlMap[partsId] or 0
    end
    return 0
end

-- 获取所有经验道具
function WeaponAssemblyServer:GetWeaponExpItems()
    return self._weaponExpItems
end

-- 根据经验卡ID获取对武器经验加成
function WeaponAssemblyServer:GetItemAddExp(itemId)
    return self._weaponItemId2Exp[tostring(itemId)] or 0
end

--- 更换皮肤
---@param dataType number
---@param commond pb_WeaponApplySkinCmd
function WeaponAssemblyServer:C2SCSWAssemblyApplySkinReq(dataType, commond)
    local commonds = { commond }
    self:C2SCSWAssemblyApplySkinReqFromCommonds(dataType, commonds)
end

--- 更换皮肤
---@param dataType number
---@param commonds pb_WeaponApplySkinCmd[]
function WeaponAssemblyServer:C2SCSWAssemblyApplySkinReqFromCommonds(dataType, commonds)
    local req = pb.CSWAssemblyApplySkinReq:New()
    req.data_type = dataType
    req.cmds = commonds

    local function fOnCSWAssemblyApplySkinRes(res)
        self:ProcessCSWAssemblyModifySkinRes(res)        
    end
    req:Request(fOnCSWAssemblyApplySkinRes)
end

--- 更换皮肤炫彩
function WeaponAssemblyServer:C2SCSWAssemblyModifySkinReq(skin_id, color_id, func_id)
    local req = pb.CSWAssemblyModifySkinReq:New()
    req.skin_id = skin_id
    req.color_id = color_id
    req.func_id = func_id

    local function fOnCSWAssemblyModifySkinRes(res)
        if res == nil or res.result ~= 0 then
            return
        end
        
        Server.WeaponAssemblyServer:UpdateWeaponSkinInfo(res.info, true)
        Server.ArmedForceServer:ApplyCurrentPresetToInvOnDataUpdated()
        Server.WeaponAssemblyServer.Events.evtCSWAssemblyModifySkinRes:Invoke(res.info)
    end
    req:Request(fOnCSWAssemblyModifySkinRes)
end

-------------------------------------------------------------------------------------------------
-----------------------------------------Logic---------------------------------------------------
-------------------------------------------------------------------------------------------------

function WeaponAssemblyServer:ProcessCSWAssemblyModifySkinRes(res)
    if res == nil or res.result ~= 0 then
        return
    end

    local data_type = res.data_type
    if data_type == 1 then
        self:ProcessCSWAssemblyModifySkinRes4MP(res.mp_change)
    else
        self:ProcessCSWAssemblyModifySkinRes4SOL(res.changes)
    end

    Server.WeaponAssemblyServer.Events.evtOnSOLWeaponSkinUpdated:Invoke()
end

---@param mp_change pb_CSWAssemblyApplySkinRes
function WeaponAssemblyServer:ProcessCSWAssemblyModifySkinRes4MP(mp_change)
    if mp_change == nil then
        return
    end

    Server.InventoryServer:ProcessPbMPDataChange(mp_change, EMPChangeReason.Assembly)
    Server.ArmedForceServer:ApplyCurrentPresetToInvOnDataUpdated()
end

---@param changes pb_DataChange
function WeaponAssemblyServer:ProcessCSWAssemblyModifySkinRes4SOL(changes)
    if changes == nil then
        return
    end

    Server.InventoryServer:ProcessPbDataChange(changes, true)
end

function WeaponAssemblyServer:UpdateWeaponSkinInfoFromRawPropInfoUpdate(propinfo)
    local skinID = ItemBase.GetWeaponSkinIDFromPropInfo(propinfo)
    local weaponSkinInfo = self:GetWeaponSkinInfoFromSkinID(skinID)
    ItemBase.SetWeaponSkinInfo2PropInfo(propinfo, weaponSkinInfo)
end

function WeaponAssemblyServer:AddWeaponSkinInfos(infos)
    if infos == nil then
       return
    end
    for _, value in ipairs(infos) do
        self:UpdateWeaponSkinInfo(value)
    end
end

---@param pb_WeaponSkinInfo
function WeaponAssemblyServer:UpdateWeaponSkinInfo(info, bUpdateInventory)
    if info == nil then
        return
    end

    local skinID = info.skin_id
    local skinInfo = self:GetWeaponSkinInfoFromSkinID(skinID)
    if skinInfo == nil then
        skinInfo = info
        self._weaponSkinInfos[skinID] = skinInfo
        return
    end

    skinInfo.skin_id = info.skin_id
    skinInfo.color = info.color
    skinInfo.funcs = info.funcs
    skinInfo.unlocked_funcs = info.unlocked_funcs

    bUpdateInventory = setdefault(bUpdateInventory, false)
    if not bUpdateInventory then
        return
    end
end

function WeaponAssemblyServer:WriteWeaponSkinInfo2PropInfo(propinfo)
    if null == propinfo then
        return
    end

    if propinfo == nil or propinfo.weapon == nil then
        return
    end

    local skinID = propinfo.weapon.skin_id
    local skinInfo = self:GetWeaponSkinInfoFromSkinID(skinID)
    ItemBase.SetWeaponSkinInfo2PropInfo(propinfo, skinInfo)
end

function WeaponAssemblyServer:GetWeaponSkinInfoFromSkinID(skinID)
    local skinInfo = self._weaponSkinInfos[skinID]
    return skinInfo
end

return WeaponAssemblyServer