----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMQuest)
----- LOG FUNCTION AUTO GENERATE END -----------
local QuestLogic = require "DFM.Business.Module.QuestModule.QuestLogic"
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local EComp = CommonWidgetConfig.EIVWarehouseTempComponent
local EIVSlotPos = CommonWidgetConfig.EIVSlotPos
local EIVCompOrder = CommonWidgetConfig.EIVCompOrder
local EIVItemViewMode = Module.CommonWidget.Config.EIVItemViewMode

---@class QuestSeasonCollectorItem : LuaUIBaseView
local QuestSeasonCollectorItem = ui("QuestSeasonCollectorItem")

function QuestSeasonCollectorItem:Ctor()
    self._wt_WBP_CommonSwitch2 = self:Wnd("WBP_CommonSwitch2", UIWidgetBase)
    self._wt_CheckBoxSwitch = self._wt_WBP_CommonSwitch2:Wnd("wtCheckBoxSwitch", UICheckBox)
    self._wt_CheckBoxSwitch:SetCallback(self.OnCheckSwitchStateChanged, self)

    self._wtScrollGridBox =
        UIUtil.WndScrollGridBox(self, "DFScrollGridBox_59", self._OnGetItemsCount, self._OnProcessItemWidget)

    self._wtName = self:Wnd("DFTextBlock_1", UITextBlock)
    self._wtDesc = self:Wnd("DFTextBlock", UITextBlock)

    self._wtActionBtn = self:Wnd("wtCommonButtonV1S1", DFCommonButtonOnly)
    self._wtActionBtn:Event("OnClicked", self._OnClickActionBtn, self)
    self._wtActionBtn:Event("OnDeClicked", self._OnClickActionBtn, self)

    self._wtLockedTB = self:Wnd("DFTextBlock_44", UITextBlock)

    self.dataInfo = {}
    self.itemBaseList = {}
    self.itemList = {}
end

------------------------------------ Override function ------------------------------------
-- UI打开时触发
function QuestSeasonCollectorItem:OnOpen()
    self:_AddEventListener()
end

function QuestSeasonCollectorItem:OnShow()
    self._wtScrollGridBox:RefreshAllItems()
end

function QuestSeasonCollectorItem:OnHide()
end

-- UI关闭时触发
function QuestSeasonCollectorItem:OnClose()
    self:_RemoveEventListener()
end

function QuestSeasonCollectorItem:_OnGetItemsCount()
    return #self.itemBaseList
end

function QuestSeasonCollectorItem:_OnProcessItemWidget(index, widget)
    widget:InitItem(self.itemBaseList[index + 1])
    if self.dataInfo.state < QuestState.Completed then
        local storedItemList = Server.InventoryServer:GetItemsById(self.itemBaseList[index + 1].id)
        local hasCount = table.nums(storedItemList) > self.itemBaseList[index + 1].num and self.itemBaseList[index + 1].num or table.nums(storedItemList)
        local iconCP = widget:FindOrAdd(EComp.BottomLeftIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomRight)
        if hasCount < self.itemBaseList[index + 1].num then
            iconCP:SetTextColorAndOpacity(Facade.ColorManager:GetSlateColor("LightNegative"))
            iconCP:SetSecTextColorAndOpacity(Facade.ColorManager:GetSlateColor("LightNegative"))
        end
        iconCP:ShowTwoTextOnly(hasCount, GeneralHelperTool.GetTextColor("/", self.itemBaseList[index + 1].num))
    end

    widget:BindCustomOnClicked(
        function()
            Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIItemSelect)
            widget:_SetSelected(true)
            Module.ItemDetail:OpenItemDetailPanel(
                self.itemBaseList[index + 1],
                widget,
                nil,
                nil,
                nil,
                nil,
                nil,
                nil,
                nil,
                function()
                    widget:_SetSelected()
                end
            )
        end
    )
end
------------------------------------ Public function ------------------------------------
------------------------------------ Private function ------------------------------------
function QuestSeasonCollectorItem:_AddEventListener()
    self:AddLuaEvent(Server.QuestServer.Events.evtSeasonCollectorLockRefresh, self._OnRefreshLockTip, self)
end

function QuestSeasonCollectorItem:_RemoveEventListener()
    self:RemoveLuaEvent(Server.QuestServer.Events.evtSeasonCollectorLockRefresh)
end

function QuestSeasonCollectorItem:RefreshView(data)
    self.dataInfo = data
    local groupCfg = Facade.TableManager:GetRowByKey("CollectorGroup", tostring(self.dataInfo.id))
    if groupCfg then
        self._wtName:SetText(groupCfg.CollectorGroupName)
        self._wtDesc:SetText(groupCfg.CollectorGroupDesc)

        self.itemBaseList = {}
        self.itemList = {}
        for index, value in ipairs(groupCfg.ItemListArr) do
            table.insert(self.itemBaseList, ItemBase:New(value, groupCfg.ItemCountArr[index]))
            table.insert(self.itemList, {itemId = value, num = groupCfg.ItemCountArr[index]})
        end
    end
    self._wtScrollGridBox:RefreshAllItems()
    if self.dataInfo.is_locked then
        self._wt_CheckBoxSwitch:SetIsChecked(true, true)
        self._wtLockedTB:SetText(Module.Quest.Config.Loc.QuestCollectorLockd)
    else
        self._wt_CheckBoxSwitch:SetIsChecked(false, true)
    end

    self:UpdateStyle()
end

function QuestSeasonCollectorItem:UpdateStyle()
    if self.dataInfo == nil then
        return
    end
    self._wtLockedTB:SetText(Module.Quest.Config.Loc.QuestCollectorLock)
    if self.dataInfo.state < QuestState.Completed then
        -- 没有道具目标或提交已完成
        if QuestLogic.IsCanSubmitCollector(self.itemList) then
            self:SetType(2)
            self._wtActionBtn:SetIsEnabledStyle(true)
        else
            self._wtActionBtn:SetIsEnabledStyle(false)
            self:SetType(0)
        end
        self._wtActionBtn:SetMainTitle(Module.Quest.Config.Loc.submit)

        if self.dataInfo.is_locked then
            self:SetType(3)
            self._wtLockedTB:SetText(Module.Quest.Config.Loc.QuestCollectorLockd)
        end
    elseif self.dataInfo.state >= QuestState.Completed then
        self._wtActionBtn:SetIsEnabledStyle(false)
        self._wtActionBtn:SetMainTitle(Module.Quest.Config.Loc.QuestCollectorCompleted)
        self:SetType(1)
    end
end

function QuestSeasonCollectorItem:_OnClickActionBtn()
    if self.dataInfo == nil then
        return
    end
    if self.dataInfo.state >= QuestState.Completed then
        return
    else
        if QuestLogic.IsCanSubmitCollector(self.itemList) then
            --打开提交面板
            local confirmHandle = function()
                local submitItemInfoList = {}
                for index, value in ipairs(self.itemList) do
                    local storedItemList = Server.InventoryServer:GetItemsById(value.itemId)
                    local gids = {}
                    for _, matchItem in pairs(storedItemList) do
                        table.insert(gids, matchItem.gid)
                    end
                    for i = 1, value.num, 1 do
                        table.insert(submitItemInfoList, {id = value.itemId, num = 1, gid = gids[i]})
                    end
                end
                Server.QuestServer:CollectorTaskSubmitReq(self.dataInfo.id, submitItemInfoList)
            end

            Module.CommonTips:ShowConfirmItemsWindow(
                Module.Quest.Config.Loc.QuestCollectorSubmitTip,
                confirmHandle,
                nil,
                nil,
                nil,
                self.itemList,
                true
            )
        else
            Module.CommonTips:ShowSimpleTip(Module.Quest.Config.Loc.noItemsToSubmit) -- 暂时没有可提交的物品
        end
    end
end

function QuestSeasonCollectorItem:OnCheckSwitchStateChanged(bChecked)
    if bChecked then
        --self:SetType(3)
        if self.dataInfo and self.dataInfo.is_locked == false then
            Server.QuestServer:CollectorTaskSetStatusReq(self.dataInfo.id, true)
        end
    else
        --self:SetType(0)
        if self.dataInfo and self.dataInfo.is_locked then
            Server.QuestServer:CollectorTaskSetStatusReq(self.dataInfo.id, false)
        end
    end
end

function QuestSeasonCollectorItem:_OnRefreshLockTip(id, ret)
    if self.dataInfo and self.dataInfo.id == id then
        if self.dataInfo.is_locked then
            self._wt_CheckBoxSwitch:SetIsChecked(true, true)
        else
            self._wt_CheckBoxSwitch:SetIsChecked(false, true)
        end
        self:UpdateStyle()
    end
end

return QuestSeasonCollectorItem
