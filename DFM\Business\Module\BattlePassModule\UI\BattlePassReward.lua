----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMBattlePass)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
-- END MODIFICATION

---@class BattlePassReward : LuaUIBaseView
local BattlePassReward = ui("BattlePassReward")
local BattlePassConfig = Module.BattlePass.Config
local BattlePassLogic = require "DFM.Business.Module.BattlePassModule.Logic.BattlePassLogic"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local ItemDetailLogic = require "DFM.Business.Module.ItemDetailModule.ItemDetailLogic"
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
local LiteCommonDownload =  require "DFM.Business.Module.LitePackageModule.UI.LiteCommonDownload"
local UGPInputHelper = import("GPInputHelper")
local EGPInputType = import"EGPInputType"


--- 生命周期

function BattlePassReward:Ctor()
    ---- 蓝图控件引用
    self._wtTxtRewardQuality = self:Wnd("DFTextBlock_99", UITextBlock)  -- 奖励道具品质
    self._wtImgQualityIcon = self:Wnd("Image_BpQualityIcon", UIImage)  -- 奖励道具品质图标
    self._wtTxtRewardMode = self:Wnd("DFTextBlock_140", UITextBlock)  -- 奖励道具属于的模式
    self._wtTxtRewardName = self:Wnd("DFTextBlock", UITextBlock)  -- 奖励道具名
    self._wtBtnRewardDetail = self:Wnd("WBP_CommonIconButton", DFCommonButtonOnly)  -- 奖励道具详情按钮
    self._wtBtnRewardDetail:Event("OnClicked", self.OnBtnRewardDetailClick, self)
    --self._wtScrollBoxRewardItem = self:Wnd("DFScrollBox_160", UIScrollBox)  -- 奖励列表
    self._wtRewardWaterFallList = UIUtil.WndWaterfallScrollBox(
            self, "DFWaterfallScrollView_1", self._OnRewardWaterFallListGetItemCount, self._OnRewardWaterFallListProcessItemWidget, UIName2ID.BattlePassRewardItem, self.GetRewardListItemSize) -- 奖励列表，子UI
    self._wtImgPaste = self:Wnd("DFImage_1", UIImage)  -- 贴图
    self._wtPropImg_1 = self:Wnd("wtPropImg_1", DFCDNImage)
    self._wtPropImg_2 = self:Wnd("wtPropImg_2", DFCDNImage)
    self._wtPropImg_3 = self:Wnd("wtPropImg_3", DFCDNImage)
    self._wtPropImgBox_2 = self:Wnd("wtPropImgBox_2", DFCDNImage)
    self._wtPropImgPanel = self:Wnd("DFCanvasPanel_78", UIWidgetBase)
    self._wtTopCanvasPanel = self:Wnd("CanvasPanel_68", UIWidgetBase) -- 顶层面板
    self._wtBtnUnlockBP = self:Wnd("WBP_DFCommonButtonV1S3", DFCommonButtonOnly)  -- 解锁BP付费按钮
    self._wtTxtRewardDesc = self:Wnd("richtext", UITextBlock)  -- 道具描述
    self._wtLevelCanvasPanel = self:Wnd("DFCanvasPanel", UIWidgetBase) -- 等级面板
    self._wtTxtRewardLevel = self:Wnd("DFTextBlock_1", UITextBlock)  -- 道具所属级别

    self._wtSocialMilitaryTabPanel = self:Wnd("Panel_Base_01", UIWidgetBase) -- 军牌面板
    self._wtSocialMilitaryTabBase = self:Wnd("DFImage_01_Bg", UIImage)  -- 军牌底座
    self._wtSocialMilitaryTabPaste = self:Wnd("DFImage_01_Icon", UIImage)  -- 军牌贴图
    self._wtSocialCardPanel = self:Wnd("Panel_Base_02", UIWidgetBase) -- 名片面板
    self._wtSocialCardBase = self:Wnd("DFImage_02_Bg", UIImage)  -- 名片底座
    self._wtSocialCardPaste = self:Wnd("DFImage_02_Icon", UIImage)  -- 名片贴图
    self._wtSocialAvatarTabPanel = self:Wnd("Panel_Base_03", UIWidgetBase) -- 头像面板
    self._wtSocialAvatarTabBase = self:Wnd("DFImage_03_Bg", UIImage)  -- 头像底座
    self._wtSocialAvatarTabPaste = self:Wnd("DFImage_03_Icon", UIImage)  -- 头像贴图

    if Module.ExpansionPackCoordinator:IsSupportLitePackage() then
        self._wtCommonDownload = self:Wnd("wtCommonDownload", LiteCommonDownload)
    end
    if self._wtCommonDownload then
        self._wtCommonDownload:Collapsed()
    end

    self._wtExclusivePanel = self:Wnd("DFCanvasPanel_154", UIWidgetBase) -- 专属面板
    self._wtExclusiveTxt = self:Wnd("DFTextBlock_174", UITextBlock) -- 专属面板文本

    self._wtReferenceValueTxt = self:Wnd("DFRichTextBlock_61", UITextBlock) -- 参考价值文本

    --- 控件处理
    self._wtBtnUnlockBP:Event("OnClicked", self.OnBtnUnlockBtnClick, self)

    --- 成员变量声明
    self._tRewardItem = {}  -- 奖励列表 {key (0 免费，1 付费) : instanceId} 
    self._bLastRewardClickPay = false  -- 上一次点击的奖励的付费类型
    self._tLastClickItemTime = 0  -- 上一次点击奖励的时间
    self._iLastRewardClickLevel = 0  -- 上一次点击的奖励等级
    self._iLastRewardClickID = 0  -- 上一次点击的奖励ID
    self._iLastRewardClickCount = 0  -- 上一次点击的奖励数量
    self._iLastRewardClickPos = -1  -- 上一次点击的位置
    self._iLastRewardClickIndex = -1  -- 上一次点击的位置下标
    self._iLastRewardConfig = {}  -- 上一次点击的奖励配置
    self._bFirstRefreshDone = false  -- 第一次刷新是否完成
    self._tPosSubUI = {}  -- 位置和子UI的配置关系 {key, bTitle, list{id, tVal}}

    --- 预定义

    --- 初始化逻辑

    -- 藏品图层隐藏
    self._wtPropImg_1:Collapsed()
    self._wtPropImg_2:Collapsed()
    self._wtPropImg_3:Collapsed()

    -- 道具详情按钮不展示
    self._wtBtnRewardDetail:Visible()

    -- 服务器数据未到之前，不展示
    self._wtTopCanvasPanel:Collapsed()

    self._scrollStopHandle = nil
end

-- UI构造时触发, 用于初始化外部传参UI数据逻辑，由UIManager触发（仅触发一次）
-- 子节点蓝图不会触发该方法
-- Module加载好子控件所需的图片后，将数据传入主UI界面
---@overload fun(LuaUIBaseView, OnInitExtraData)
function BattlePassReward:OnInitExtraData(params)
    loginfo("BattlePassReward:OnInitExtraData")
end

-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function BattlePassReward:OnOpen()
    loginfo("BattlePassReward:OnOpen")

    -- 注册事件
    self:AddListenersOpen()

    -- 刷新非服务器UI
    self:RefreshUI()
end

-- UI监听事件、协议
function BattlePassReward:AddListeners()
    -- 普通事件
    -- self:AddLuaEvent(BattlePassConfig.evtMainPanelTest, self.OnMainPanelTest, self)
    -- ntf协议事件 ntfNameString
    -- Facade.ProtoManager:AddNtfListener("CSAuctionOrderChangeNtf", self.OnCSAuctionOrderChangeNtf, self)

    -- 移除事件监听
    self:RemoveListeners()

    self:AddLuaEvent(Server.BattlePassServer.Events.evtBattlePassInfoUpdate, self.RefreshUIServer, self)
    self:AddLuaEvent(Module.Reward.Config.Events.evtShowReward, self._OnShowReward, self)
end

-- 移除show监听的事件
function BattlePassReward:RemoveListeners()
    self:RemoveLuaEvent(Server.BattlePassServer.Events.evtBattlePassInfoUpdate)
    self:RemoveLuaEvent(Module.Reward.Config.Events.evtShowReward)
end

-- 监听open开始的事件
function BattlePassReward:AddListenersOpen()
    -- 移除监听的事件 
    self:RemoveListenersOpen()
    
    self:AddLuaEvent(BattlePassConfig.Events.evtBattlePassRewardTabItemClick, self.OnBattlePassRewardTabItemClick, self)
    self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult,self._OnDownloadStateChange,self)
    self:AddLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged, self._OnPackStateChanged, self)
end

-- 移除open监听的事件
function BattlePassReward:RemoveListenersOpen()
    self:RemoveLuaEvent(BattlePassConfig.Events.evtBattlePassRewardTabItemClick)
    self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult,self._OnDownloadStateChange,self)
    self:RemoveLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged, self._OnPackStateChanged, self)
end

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function BattlePassReward:OnClose()
    loginfo("BattlePassReward:OnClose")
    
    -- 移除open监听的事件
    self:RemoveListenersOpen()
end

-- UI OnOpen后、调用Show()显示时触发
-- 做表现
---@overload fun(LuaUIBaseView, OnShow)
function BattlePassReward:OnShowBegin()
    loginfo("BattlePassReward:OnShow")
    -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
    if IsHD() then 
        self:_EnableGamepadFeature(true)
    end 
    -- END MODIFICATION
    -- 判断是否当前正在展示该页签，不是则不处理
    local iUINameID = Module.BattlePass.Field:GetCurrStackUINameID()
    if iUINameID ~= UIName2ID.BattlePassReward then
        return
    end

    -- 注册监听事件
    self:AddListeners()

    if not Module.BattlePass.Field:IsStackFirstClickReward() then
        if self._iLastRewardClickID > 0 then
            -- 点击上一次点击的奖励
            Module.BattlePass.Config.Events.evtBattlePassRewardTabItemClick:Invoke(self._iLastRewardClickID, self._iLastRewardClickCount, self._bLastRewardClickPay, false, self._iLastRewardClickLevel, self._iLastRewardClickPos, self._iLastRewardClickIndex, self._iLastRewardConfig)
        end
    end
    
    -- 如果不是第一次刷新，则显示UI
    if self._bFirstRefreshDone then
        self._wtTopCanvasPanel:Visible()
    end

    -- 手动刷新奖励
    self._wtRewardWaterFallList:RefreshItemCount()
    self._wtRewardWaterFallList:RefreshAllItems()
    
    -- 拉取服务器数据
    Server.BattlePassServer:FetchServerData(true)

    -- 绑定滑动列表急停
    self._scrollStopHandle = UIUtil.AddScrollBoxClickStopScroll(self._wtRewardWaterFallList, self)
end

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function BattlePassReward:OnHide()
    loginfo("BattlePassReward:OnHide")

    -- 隐藏贴图控件
    self:CollapseAllPasteImage()

    -- 关闭道具详情页
    Module.ItemDetail:CloseAllPopUI()

    -- 重置上次点击的时间
    self._tLastClickItemTime = 0

    -- 调用场景展示结束函数
    BattlePassLogic.HideShowReward()

    -- 移除监听事件
    self:RemoveListeners()

    -- 解除滑动列表急停
    if self._scrollStopHandle then
		UIUtil.RemoveScrollBoxClickStopScroll(self._scrollStopHandle)
		self._scrollStopHandle = nil
	end

    -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
    if IsHD() then 
        self:_EnableGamepadFeature(false)
    end
    -- END MODIFICATION
end


--- 事件处理

-- 奖励详情点击
function BattlePassReward:OnBtnRewardDetailClick()
    local itemData = ItemBase:NewIns(self._iLastRewardClickID)

    -- 展示枪械皮肤详情
    if itemData.itemMainType == EItemType.WeaponSkin then
        local itemWeapon = nil
        itemWeapon = Server.CollectionServer:GetWeaponSkinIfExists(self._iLastRewardClickID)
        if itemWeapon == nil then
            local propInfo = {
                id = self._iLastRewardClickID,
                gid = 0,
                num = self._iLastRewardClickCount
            }
            itemWeapon = ItemHelperTool.CreateItemByPropInfo(propInfo)
        end
        Module.Collection:ShowWeaponSkinDetailPage(itemWeapon)
    elseif itemData.itemMainType == EItemType.Fashion then
        Facade.UIManager:AsyncShowUI(UIName2ID.StaffLotteryAccessoriesPreview, nil, nil, self._iLastRewardClickID)
    else
        Module.ItemDetail:OpenItemDetailPanel(itemData, self._wtBtnRewardDetail)
    end
end

-- 奖励点击事件
function BattlePassReward:OnBattlePassRewardTabItemClick(iRewardID, iCount, eType, bPlayerClick, iLevel, iPos, iIndex, tVal)
    -- 判断是否当前正在展示该页签，不是则不处理
    local iUINameID = Module.BattlePass.Field:GetCurrStackUINameID()
    if not bPlayerClick and iUINameID ~= UIName2ID.BattlePassReward then
        return
    end

    -- 关闭道具详情页
    Module.ItemDetail:CloseAllPopUI()

    ---- 判断奖励是否可以展示
    --if not BattlePassLogic.IsRewardCanShow(iRewardID) then
    --    return
    --end

    -- 刷新点击态
    self:RefreshRewardClicked(iPos, iIndex)
    
    -- 刷新右上角奖励信息
    self:RefreshRightTopItemInfo(iRewardID, iCount, eType, iLevel, tVal)

    -- 设置上一次点击的奖励ID
    Module.BattlePass.Field:SetRewardLastItemID(iRewardID)
    
    -- 展示奖励
    BattlePassLogic.ShowReward(iRewardID, UIName2ID.BattlePassReward, self, bPlayerClick)

    -- 设置上一次点击的奖励
    self._bLastRewardClickPay = eType
    self._iLastRewardClickLevel = iLevel
    self._iLastRewardClickID = iRewardID
    self._iLastRewardClickCount = iCount
    self._iLastRewardClickPos = iPos
    self._iLastRewardClickIndex = iIndex
    self._iLastRewardConfig = tVal
end

function BattlePassReward:OnBtnUnlockBtnClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.BattlePassUnlock)
end

function BattlePassReward:_OnRewardWaterFallListGetItemCount()
    return Server.BattlePassServer:GetRewardSubUIConfigCount()
end

function BattlePassReward:_OnRewardWaterFallListProcessItemWidget(iPosition, wtItem)
    local tPosConfig = Server.BattlePassServer:GetRewardSubUIConfigByPos(iPosition)
    wtItem:Init(self, iPosition, tPosConfig)
end

function BattlePassReward:_OnPackStateChanged(moduleName, packState)
    local isSuccess = packState == Module.ExpansionPackCoordinator.Config.PackState.Installed
    self:_OnDownloadStateChange(moduleName, isSuccess, 0)
end

function BattlePassReward:_OnDownloadStateChange(moduleName, bSuccess, errorCode)
    local tShowItemID = Module.BattlePass.Field:GetCurrShowItem()
    local sModuleName = BattlePassLogic.GetDownloadCategary(tShowItemID, UIName2ID.BattlePassReward, self)
    if moduleName == sModuleName then
        if bSuccess then
            self._wtCommonDownload:Collapsed()
            if self._iLastRewardClickID > 0 then
                -- 点击上一次点击的奖励
                Module.BattlePass.Config.Events.evtBattlePassRewardTabItemClick:Invoke(self._iLastRewardClickID, self._iLastRewardClickCount, self._bLastRewardClickPay, false, self._iLastRewardClickLevel, self._iLastRewardClickPos, self._iLastRewardClickIndex, self._iLastRewardConfig)
            end
        else
            self._wtCommonDownload:SelfHitTestInvisible()
        end
    end
end

-- 获取滚动奖励某个位置的尺寸
function BattlePassReward:GetRewardListItemSize(iPosition)
    --local tReward = Server.BattlePassServer:GetRewardSortedTable(self:GetKeyByPos(position))
    --
    --local iRewardCount = 0  -- 几个奖励
    --for _ in pairs(tReward) do
    --    iRewardCount = iRewardCount + 1
    --end
    --
    --local iCol = math.ceil(iRewardCount / 3)
    --
    --if IsHD() then
    --    return FVector2D(607, 371 + (iCol - 1) * 266)
    --else
    --    return FVector2D(607, 307 + (iCol - 1) * 202)
    --end

    local tPosConfig = Server.BattlePassServer:GetRewardSubUIConfigByPos(iPosition)
    if tPosConfig == nil then
        return
    end

    local iHeight = 0
    
    if tPosConfig.bTitle then
        if IsHD() then
            if tPosConfig.bTitleFirst then
                iHeight = 81
            else
                iHeight = 127
            end
        else
            if tPosConfig.bTitleFirst then
                iHeight = 81
            else
                iHeight = 112
            end
        end
    else
        if IsHD() then
            if tPosConfig.bRewardFirst then
                iHeight = 266
            else
                iHeight = 266
            end
        else
            if tPosConfig.bRewardFirst then
                iHeight = 202
            else
                iHeight = 202
            end
        end
    end
    
    if tPosConfig.bLastPos == true then
        iHeight = iHeight + 30
    end
    
    return FVector2D(607, iHeight)
end

function BattlePassReward:_OnShowReward(bIsShowing)
    if not bIsShowing then
        self:OnShowBegin()
    end
end


--- 刷新函数

function BattlePassReward:RefreshUI()
    -- 刷新奖励列表
    --self:RefreshRewardItem()
    
    -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
    if IsHD() then 
        self:_UpdateInputSummaryList()
    end
    -- END MODIFICATION

end

function BattlePassReward:RefreshUIServer()
    -- 如果赛季已经结束，不显示界面
    if BattlePassLogic.SeasonEndPrompt() then
        self._wtTopCanvasPanel:Collapsed()
        return
    end

    for _, tReward in pairs(self._tRewardItem) do
        if tReward ~= nil then
            -- 此处可写每个子UI的处理
        end
    end

    -- 设置第一次刷新完成标志
    if not self._bFirstRefreshDone then
        self._bFirstRefreshDone = true
    end
    
    -- 刷新解锁按钮
    self:RefreshUnlockBtn()

    -- 展示界面UI
    self._wtTopCanvasPanel:Visible()

    if Module.BattlePass.Field:IsStackFirstClickReward() then
        -- 点击上一次点击的奖励
        if self._iLastRewardClickID > 0 then
            Module.BattlePass.Config.Events.evtBattlePassRewardTabItemClick:Invoke(self._iLastRewardClickID, self._iLastRewardClickCount, self._bLastRewardClickPay, false, self._iLastRewardClickLevel, self._iLastRewardClickPos, self._iLastRewardClickIndex, self._iLastRewardConfig)
        end

        -- 设置下一次不是栈UI内第一次点击
        Module.BattlePass.Field:SetStackFirstClickReward(false)
    end
end

-- 刷新奖励列表
function BattlePassReward:RefreshRewardItem()
    Facade.UIManager:RemoveSubUIByParent(self, self._wtScrollBoxRewardItem)
    
    self._tRewardItem = {}
    
    self:AddRewardItem(EBattlePassRewardType.Pay)
    self:AddRewardItem(EBattlePassRewardType.SOL)
    self:AddRewardItem(EBattlePassRewardType.MP)
    self:AddRewardItem(EBattlePassRewardType.Free)
end

-- 刷新奖励点击态
function BattlePassReward:RefreshRewardClicked(iPos, iIndex)
    -- 如果上一次点击的奖励存在，则取消选中态
    local tLastItem = self:GetItemByPosIndex(self._iLastRewardClickPos, self._iLastRewardClickIndex)
    if tLastItem then
        tLastItem:SetSelected(tLastItem.item, false)
    end

    -- 设置这一次点击的按钮为选中态
    local tItem = self:GetItemByPosIndex(iPos, iIndex)
    if tItem then
        tItem:SetSelected(tItem.item, true)
    end
end

function BattlePassReward:RefreshRightTopItemInfo(iRewardID, iCount, eType, iLevel, tVal)
    -- 设置道具名
    local tItem = ItemBase:New(iRewardID, iCount)
    self._wtTxtRewardName:SetText(tItem.name)

    -- 设置品质图标 
    if tItem.quality > 0 then
        self:SetQualityIconType(tItem.quality)
        self._wtImgQualityIcon:SetColorAndOpacity(BattlePassLogic.GetQualityIconColorByNum(tItem.quality))
        self._wtImgQualityIcon:SelfHitTestInvisible()
    else
        self._wtImgQualityIcon:Collapsed()
    end

    -- 设置道具类型
    if eType == EBattlePassRewardType.Pay then
        self:SetPriceType(1)
        self._wtTxtRewardQuality:SetText(BattlePassConfig.Loc.TopRightTxtPay)
    elseif eType == EBattlePassRewardType.SOL then
        self:SetPriceType(2)
        self._wtTxtRewardMode:SetText(BattlePassConfig.Loc.TopRightTxtSOL)
    elseif eType == EBattlePassRewardType.MP then
        self:SetPriceType(3)
        self._wtTxtRewardMode:SetText(BattlePassConfig.Loc.TopRightTxtMP)
    elseif eType == EBattlePassRewardType.Free then
        self:SetPriceType(0)
        self._wtTxtRewardQuality:SetText(BattlePassConfig.Loc.TopRightTxtNormal)
    else
        self:SetPriceType(0)
        self._wtTxtRewardQuality:SetText(BattlePassConfig.Loc.TopRightTxtNormal)
    end
    
    -- 设置描述文本
    local sDesc = ItemDetailLogic.GetItemDesc(tItem)
    self._wtTxtRewardDesc:SetText(sDesc)
    
    -- 设置道具等级
    if iLevel <= 0 then
        self._wtLevelCanvasPanel:Collapsed()
    else
        self._wtLevelCanvasPanel:SelfHitTestInvisible()
        self._wtTxtRewardLevel:SetText(string.format(BattlePassConfig.Loc.RewardItemLevel, iLevel))
    end

    -- 设置专属文本
    self._wtExclusiveTxt:SetText(string.format(BattlePassConfig.Loc.ExclusiveSeason, Server.BattlePassServer:GetSeasonNum()))
    if tVal.bExclusive then
        self._wtExclusivePanel:SelfHitTestInvisible()
    else
        self._wtExclusivePanel:Collapsed()
    end

    -- 参考价值
    if tVal.iReferenceValue and tVal.iReferenceValue > 0 then
        local sRichTxtID = Module.Currency:GetRichTxtImgId(ECurrencyClientId.Tina)
        self._wtReferenceValueTxt:SetText(string.format(BattlePassConfig.Loc.RewardPanelReferenceValue, sRichTxtID, tVal.iReferenceValue))
        self._wtReferenceValueTxt:SelfHitTestInvisible()
    else
        self._wtReferenceValueTxt:Collapsed()
    end
end

function BattlePassReward:RefreshUnlockBtn()
    local ePaid = Server.BattlePassServer:GetPayType()

    -- 是否展示
    if ePaid == BattlePassType.BATTLE_PASS_TYPE_UNIVERSAL then
        -- 已经付费至最高级，不展示高级档案按钮
        self._wtBtnUnlockBP:Collapsed()
        if self._wtBtnUnlockBPHandle then
            self:RemoveInputActionBinding(self._wtBtnUnlockBPHandle)
            self._wtBtnUnlockBPHandle = nil
        end
    else
        -- 未付费，展示高级档案按钮 
        self._wtBtnUnlockBP:Visible()
    end

    -- 设置文本
    if ePaid == BattlePassType.BATTLE_PASS_TYPE_BASE then
        self._wtBtnUnlockBP:SetMainTitle(BattlePassConfig.Loc.RewardGotoUnlock)
    elseif ePaid == BattlePassType.BATTLE_PASS_TYPE_SOL or ePaid == BattlePassType.BATTLE_PASS_TYPE_MP then
        self._wtBtnUnlockBP:SetMainTitle(BattlePassConfig.Loc.RewardGotoUpgrade)
    end
end

---- 其他函数

-- 添加档案控件
--- @param iKey 
function BattlePassReward:AddRewardItem(iKey)
    local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.BattlePassRewardItem, self._wtScrollBoxRewardItem)
    local uiIns = getfromweak(weakUIIns)
    if uiIns then
        uiIns:Init(self, iKey)
        self._tRewardItem[iKey] = instanceId
    else
        return
    end
end

-- 获取贴图图像
function BattlePassReward:GetPasteImage(iItemID)
    local tItem = ItemBase:NewIns(iItemID)
    if tItem.itemMainType == EItemType.HeroAccessory and tItem.itemSubType == EHeroAccessroy.SparyPaint then  -- 干员商业化周边-喷漆
        return self._wtImgPaste
    elseif tItem.itemMainType == EItemType.SocialAppearance and tItem.itemSubType == ESocialAppearanceType.SocialAvatarTab then  -- 社交定制外观-头像
        return self._wtSocialAvatarTabPaste
    elseif tItem.itemMainType == EItemType.SocialAppearance and tItem.itemSubType == ESocialAppearanceType.SocialMilitaryTab then  -- 社交定制外观-军牌
        return self._wtSocialMilitaryTabPaste
    elseif tItem.itemMainType == EItemType.HeroAccessory and tItem.itemSubType == EHeroAccessroy.Card then  -- 干员商业化周边-名片
        return self._wtSocialCardPaste
    else
        return nil
    end
end

-- 获取贴图面板
function BattlePassReward:GetPasteImagePanel(iItemID)
    -- long1版本平main资源都改为场景处理
    --local tItem = ItemBase:NewIns(iItemID)
    --if tItem.itemMainType == EItemType.HeroAccessory and tItem.itemSubType == EHeroAccessroy.SparyPaint then  -- 干员商业化周边-喷漆
    --    return self._wtImgPaste
    --elseif tItem.itemMainType == EItemType.SocialAppearance and tItem.itemSubType == ESocialAppearanceType.SocialAvatarTab then  -- 社交定制外观-头像
    --    return self._wtSocialAvatarTabPanel
    --elseif tItem.itemMainType == EItemType.SocialAppearance and tItem.itemSubType == ESocialAppearanceType.SocialMilitaryTab then  -- 社交定制外观-军牌
    --    return self._wtSocialMilitaryTabPanel
    --elseif tItem.itemMainType == EItemType.HeroAccessory and tItem.itemSubType == EHeroAccessroy.Card then  -- 干员商业化周边-名片
    --    return self._wtSocialCardPanel
    --else
    --    return nil
    --end
    return nil
end

-- 隐藏所有贴图控件
function BattlePassReward:CollapseAllPasteImage()
    self._wtSocialAvatarTabPanel:Collapsed()
    self._wtSocialMilitaryTabPanel:Collapsed()
    self._wtSocialCardPanel:Collapsed()
    self._wtImgPaste:Collapsed()
end

-- 获取藏品图层
function BattlePassReward:GetCollectionImage1()
    return self._wtPropImg_1
end

function BattlePassReward:GetCollectionImage2()
    return self._wtPropImg_2
end

function BattlePassReward:GetCollectionImage3()
    return self._wtPropImg_3
end

function BattlePassReward:GetCollectionImage2Box()
    return self._wtPropImgBox_2
end

function BattlePassReward:GetCollectionPanel()
    return self._wtPropImgPanel
end


-- 关闭顶层面板
function BattlePassReward:CloseAllUI()
    self._wtTopCanvasPanel:Collapsed()
end

-- 播放喷漆动效
function BattlePassReward:PlaySparyPaintAnim()
    self:PlayAnimation(self.WBP_BattlePass_Reward_penqihuan, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
end

-- 根据位置获取奖励类型
function BattlePassReward:GetKeyByPos(iPos)
    if iPos == 1 then
        return EBattlePassRewardType.Pay
    elseif iPos == 2 then
        return EBattlePassRewardType.SOL
    elseif iPos == 3 then
        return EBattlePassRewardType.MP
    elseif iPos == 4 then
        return EBattlePassRewardType.Free
    end
end

-- 根据Pos和Index获取奖励实例
function BattlePassReward:GetItemByPosIndex(iPos, iIndex)
    local wtItem = self._wtRewardWaterFallList:GetItemByIndex(iPos)
    if nil == wtItem then
        return nil
    end

    local tItem = wtItem:GetSubUIByIndex(iIndex) 
    return tItem
end

function BattlePassReward:GetDownloadWidget()
    return self._wtCommonDownload
end


-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
function BattlePassReward:_EnableGamepadFeature(bEnable)
    if not IsHD() then
        return
    end
    if bEnable then

        if not self._wtNavGroup then
            self._wtNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtRewardWaterFallList, self, "Hittest")
            if self._wtNavGroup then 
                self._wtNavGroup:AddNavWidgetToArray(self._wtRewardWaterFallList)
                self._wtNavGroup:SetScrollRecipient(self._wtRewardWaterFallList)
                self._wtNavGroup:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
                WidgetUtil.BindCustomFocusProxy(self._wtNavGroup, self._FocusProxyMaker, self._FocusProxyResolver, self)
            end         
        end
        if self._wtNavGroup then 
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup) 
        end 
        self._wtBtnUnlockBP:SetDisplayInputAction("BattlePass_Unlock_Gamepad", true, nil, true)
        if not self._wtBtnUnlockBPHandle then
            self._wtBtnUnlockBPHandle = self:AddInputActionBinding("BattlePass_Unlock_Gamepad", EInputEvent.IE_Pressed, self.OnBtnUnlockBtnClick,self, EDisplayInputActionPriority.UI_Stack)
        end

        self:_UpdateInputSummaryList()
    else
        if self._wtNavGroup then 
            WidgetUtil.RemoveNavigationGroup(self)
            self._wtNavGroup = nil
        end
    
        if self._wtBtnUnlockBPHandle then
            self:RemoveInputActionBinding(self._wtBtnUnlockBPHandle)
            self._wtBtnUnlockBPHandle = nil
        end
    end
end


function BattlePassReward:_FocusProxyMaker(inWidget)
    return {1}
end

function BattlePassReward:_FocusProxyResolver(inProxyHandle)
    local guid = inProxyHandle[1]
    -- 利用guid去查找对应的Item，此时不需要再查找内部的原子控件，框架会自动处理
    return self._wtRewardWaterFallList:GetItemByIndex(guid)
end

function BattlePassReward:_UpdateInputSummaryList()
    local curInputType = UGPInputHelper.GetCurrentInputType(GetGameInstance())
    if curInputType ~= EGPInputType.Gamepad then
        return
    end 
    
    local summaryList = {}
    table.insert(summaryList, {actionName = "BattlePass_ItemInfo_Gamepad",func = self.OnBtnRewardDetailClick, caller = self ,bUIOnly = false, bHideIcon = false}) --道具详情
    Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, true, false)
end

-- END MODIFICATION



return BattlePassReward
