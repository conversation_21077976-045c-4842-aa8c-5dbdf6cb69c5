----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSettlement)
----- LOG FUNCTION AUTO GENERATE END -----------



local ArenaTeamInfo = ui("ArenaTeamInfoLable")

local UGameplayBlueprintHelper = import "GameplayBlueprintHelper"
-- BEGIN MODIFICATION @ VIRTUOS 
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

function ArenaTeamInfo:Ctor()
    self._wtResultTB = self:Wnd("wtResultTB", UITextBlock)
    self._wtMapNameTB = self:Wnd("wtMapNameTB", UITextBlock)

    self._wtTeamInfoLableHB = self:Wnd("wtTeamInfoLableHB", UIWidgetBase)
    self._wtTeamPlayerInfoSGB = UIUtil.WndScrollGridBox(self, "wtTeamPlayerInfoSGB", self._OnGetTeamPlayerInfoCount, self._OnProcessTeamPlayerInfoWidget)

    self:_CtorPc()
    self:_CtorMobile()
end

function ArenaTeamInfo:_CtorPc()
    if not IsHD() then
        return
    end

    Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.Hidden)
end

function ArenaTeamInfo:_CtorMobile()
    if IsHD() then
        return
    end

    self._wtNextStepWB = self:Wnd("wtNextStepWB", DFCommonButtonOnly)
    self._wtNextStepWB:Event("OnClicked", self._JumpToNextStep, self)

    Module.CommonBar:RegStackUIHideBar(self)
end

function ArenaTeamInfo:OnOpen()
    self:_DoStartThing()
end

function ArenaTeamInfo:OnActivate()
    self:_DoStartThing()
end

function ArenaTeamInfo:OnShowBegin()
    self:_SetPcBottomBar()

    -- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self:_EnableGamepadFeature()
    end
    -- END MODIFICATION
end

function ArenaTeamInfo:OnHideBegin()

    -- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self:_DisableGamepadFeature()
    end
    -- END MODIFICATION
end

function ArenaTeamInfo:OnDeactivate()
    self:_DoEndThing()
end

function ArenaTeamInfo:OnClose()
    self:_DoEndThing()
    Facade.UIManager:ClearSubUIByParent(self, self._wtTeamInfoLableHB)
end

function ArenaTeamInfo:_DoStartThing()
    self:_GetRoomID()
    self:_GetStartTime()
    self:_AddLuaEventBySelf()
    self:_FindStep()
    self:_RefreshView()
end

function ArenaTeamInfo:_DoEndThing()
    self:_RemoveLuaEventBySelf()
end

function ArenaTeamInfo:_AddLuaEventBySelf()
    self:AddLuaEvent(Module.Settlement.Config.Events.evtArenaSelectTeamInfoLabel, self._OnArenaSelectTeamInfoLabel, self)
end

function ArenaTeamInfo:_RemoveLuaEventBySelf()
    self:RemoveAllLuaEvent()
end

function ArenaTeamInfo:_GetRoomID()
    self._roomID = Server.SettlementServer:GetArenaRoomID()
end

function ArenaTeamInfo:_GetStartTime()
    self._startTime = Server.SettlementServer:GetArenaStartTime()
end

function ArenaTeamInfo:_FindStep()
    self._nextUIID = Module.Settlement.Field:GetArenaSettlementNextUIID(self.UINavID)
end

function ArenaTeamInfo:_RefreshView()
    Facade.UIManager:RemoveSubUIByParent(self, self._wtTeamInfoLableHB)
    local arenaTeamInfoList = Server.SettlementServer:GetArenaTeamInfoList()

    for key, value in ipairs(arenaTeamInfoList) do
        local weakUIIns = Facade.UIManager:AddSubUI(self, UIName2ID.ArenaTeamInfoLable, self._wtTeamInfoLableHB)
        local uiIns = getfromweak(weakUIIns)

        if uiIns then
            uiIns:RefreshView(key, value)
        end
    end

    local result = Server.SettlementServer:GetArenaResult()

    if result == 1 then
        self._wtResultTB:SetText(Module.Settlement.Config.Loc.WinVictory)
    elseif result == 2 then
        self._wtResultTB:SetText(Module.Settlement.Config.Loc.Defeat)
    else
        self._wtResultTB:SetText(Module.Settlement.Config.Loc.FlagBattleTieTips)
    end

    self._wtMapNameTB:SetText(Server.SettlementServer:GetArenaMapName() or "")
end

function ArenaTeamInfo:_SetPcBottomBar()
    if not IsHD() then
        return
    end

    local summaryList = {}
    table.insert(summaryList, {actionName = "SOLSettlementNext", func = CreateCallBack(self._JumpToNextStep, self)})
    -- BEGIN MODIFICATION @ VIRTUOS : 手柄的确认键(仅提示)
    table.insert(summaryList, {actionName = "Assembly_Confirm", func = nil, caller = nil, bUIOnly = true, bHideIcon = false})
    -- END MODIFICATION
    Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, true, true)
end

function ArenaTeamInfo:_JumpToNextStep()
    Facade.UIManager:AsyncShowUI(self._nextUIID)
    Facade.UIManager:CloseUI(self)
end

function ArenaTeamInfo:_OnArenaSelectTeamInfoLabel(teamID)
    self._arenaTeamPlayerInfoList = Server.SettlementServer:GetArenaTeamPlayerInfoListByTeamID(teamID)
    self._wtTeamPlayerInfoSGB:RefreshAllItems()
end

function ArenaTeamInfo:_OnGetTeamPlayerInfoCount()
    return self._arenaTeamPlayerInfoList and #self._arenaTeamPlayerInfoList or 0
end

function ArenaTeamInfo:_OnProcessTeamPlayerInfoWidget(idx, widget)
    local arenaTeamPlayerInfo = self._arenaTeamPlayerInfoList[idx + 1]

    if not arenaTeamPlayerInfo then
        return
    end

    -- azhengzheng:绑定控件
    local wtHeroCardWB = widget:Wnd("HeroCommonCard", HeroCommonCardBase)
    local wtCoinImg = wtHeroCardWB:Wnd("DFImage_Icon_4", UIImage)
    local wtCoinTB = wtHeroCardWB:Wnd("TotalPriceTB_1", UITextBlock)
    local wtKillCountTB = wtHeroCardWB:Wnd("TotalKillTB_1", UITextBlock)
    local wtDeadCountTB = wtHeroCardWB:Wnd("TotalSaveTB_1", UITextBlock)
    local wtAssistCount = wtHeroCardWB:Wnd("TotalResurrectTB_1", UITextBlock)

    local wtBottomBarCP = widget:Wnd("Pan_Button1", UIWidgetBase)
    local wtRadioPSZ = widget:Wnd("PlatformSizeBox_1", UIWidgetBase)
    local wtPraiseWB = widget:Wnd("wtPraiseWB", UIWidgetBase)
    local wtPraiseBtn = wtPraiseWB:Wnd("wtPraiseBtn", UIButton)
    local wtReportWB = widget:Wnd("ReportBtn", DFCommonButtonOnly)
    local wtAddFriendWB = widget:Wnd("AddFriendBtn", DFCommonButtonOnly)

    widget:SetType(3)
    wtHeroCardWB:SetCardInfoByAccessories(arenaTeamPlayerInfo.accessories)
    wtHeroCardWB:SetPlayerName(arenaTeamPlayerInfo.gameNick or "-")

    if wtCoinImg then
        wtCoinImg:Collapsed()
    end

    if wtCoinTB then
        wtCoinTB:Collapsed()
    end

    if wtKillCountTB then
        wtKillCountTB:SetText(arenaTeamPlayerInfo.killCount or "-")
    end

    if wtDeadCountTB then
        wtDeadCountTB:SetText(arenaTeamPlayerInfo.deadCount or "-")
    end

    if wtAssistCount then
        wtAssistCount:SetText(arenaTeamPlayerInfo.assistCount or "-")
    end

    if arenaTeamPlayerInfo.playerID then
        local localGPPlayerController = UGameplayBlueprintHelper.GetLocalGPPlayerController(self)

        if isvalid(localGPPlayerController) then
            local playerState = localGPPlayerController.PlayerState

            if isvalid(playerState) then
                if arenaTeamPlayerInfo.playerID == playerState.Uin then
                    local PlayerTitleInfo = playerState:GetCurPlayerTitleInfo()
                    wtHeroCardWB:UpdateTitleInfo(nil, PlayerTitleInfo.PlayerTitle, PlayerTitleInfo.RankAdcode, PlayerTitleInfo.RankNo)
                else
                    for index = 0, playerState.MemberInfoList:Num() - 1 do
                        local memberInfo = playerState.MemberInfoList:Get(index)

                        if isvalid(memberInfo) then
                            if arenaTeamPlayerInfo.playerID == memberInfo.PlayerUin then
                                local ps = memberInfo.PS

                                if isvalid(ps) then
                                    local PlayerTitleInfo = ps:GetCurPlayerTitleInfo()
                                    wtHeroCardWB:UpdateTitleInfo(nil, PlayerTitleInfo.PlayerTitle, PlayerTitleInfo.RankAdcode, PlayerTitleInfo.RankNo)
                                end

                                break
                            end
                        end
                    end
                end
            end
        end
    end

    if arenaTeamPlayerInfo.bMySelf then
        wtHeroCardWB:SetPlayerNameTextColor("C006")
        wtBottomBarCP:Collapsed()
        --- BEGIN MODIFICATION @ VIRTUOS
        if IsConsole() and widget._SetPlatformIcon  then
            logerror("ArenaTeamInfo:_OnProcessTeamPlayerInfoWidget Hide Arena Platform Icon in She2")
            -- widget:_SetPlatformIcon(arenaTeamPlayerInfo.platID)
        end
        --- END MODIFICATION
    else
        wtBottomBarCP:Visible()
        wtRadioPSZ:Collapsed()

        -- azhengzheng：点赞
        if arenaTeamPlayerInfo.bPraise or not arenaTeamPlayerInfo.playerID then
            wtPraiseWB:SetStyle(1)
            wtPraiseBtn:SetIsEnabled(false)
        else
            local onClickedPraiseBtn = function()
                local req = pb.CSPlayerInfoPraiseReq:New()
                req.praise_type = 0
                req.be_praised_player_id = arenaTeamPlayerInfo.playerID
                req:Request(function(res)
                    if res and res.result == 0 then
                        wtPraiseWB:SetStyle(1)
                        wtPraiseBtn:SetIsEnabled(false)
                        arenaTeamPlayerInfo.bPraise = true
                        Module.CommonTips:ShowSimpleTip(Module.Settlement.Config.Loc.FunctionIsMakingTXT)
                    end
                end)
            end

            wtPraiseWB:SetStyle(0)
            wtPraiseBtn:SetIsEnabled(true)
            wtPraiseBtn:RemoveEvent("OnClicked")
            wtPraiseBtn:Event("OnClicked", onClickedPraiseBtn, self)
        end

        -- azhengzheng:举报
        if arenaTeamPlayerInfo.bReport or not arenaTeamPlayerInfo.playerID or not self._roomID or not self._startTime then
            wtReportWB:SetIsEnabled(false)
        else
            local onTssReport = function()
                wtReportWB:SetIsEnabled(false)
                arenaTeamPlayerInfo.bReport = true
            end

            local onReportBtnClicked = function()
                local playerInfo = {
                    player_id = arenaTeamPlayerInfo.playerID,
                    nick_name = arenaTeamPlayerInfo.gameNick,
                    player_type = arenaTeamPlayerInfo.bMyTeam and Module.Report.Config.ReportPlayerType.Teammate or Module.Report.Config.ReportPlayerType.Enemy
                }

                Module.Report:ReportPlayer(playerInfo, 3, self._roomID, nil, self._startTime, ReportEntrance.ReportSettlementPVP, Module.Report.Config.EReportMode.ReportSol, CreateCallBack(onTssReport, self))
            end

            wtReportWB:SetIsEnabled(true)
            wtReportWB:RemoveEvent("OnClicked")
            wtReportWB:Event("OnClicked", onReportBtnClicked, self)
        end

        -- azhengzheng:添加好友
        if not arenaTeamPlayerInfo.playerID or not self._roomID or arenaTeamPlayerInfo.bAddFriend then
            wtAddFriendWB:SetIsEnabled(false)
        elseif Module.Friend:CheckIsFriend(arenaTeamPlayerInfo.playerID) then
            wtAddFriendWB:Collapsed()
        else
            local onFriendApplyAdd = function()
                wtAddFriendWB:SetIsEnabled(false)
                arenaTeamPlayerInfo.bAddFriend = true
            end

            local onAddFriendBtnClicked = function()
                Module.Friend:AddFriend(arenaTeamPlayerInfo.playerID, FriendApplySource.SolSettlementApply, CreateCallBack(onFriendApplyAdd, self), self._roomID, nil, arenaTeamPlayerInfo.platID)
            end

            wtAddFriendWB:SetIsEnabled(true)
            wtAddFriendWB:RemoveEvent("OnClicked")
            wtAddFriendWB:Event("OnClicked", onAddFriendBtnClicked, self)
        end
        --- BEGIN MODIFICATION @ VIRTUOS
        if IsConsole() and arenaTeamPlayerInfo.playerID then
            -- 平台 profile btn
            if widget._SetPlatformProfileBtn then
                widget:_SetPlatformProfileBtn(arenaTeamPlayerInfo.playerID, arenaTeamPlayerInfo.platID)
            end
            -- 平台 Icon
            if widget._SetPlatformIcon then
                logerror("ArenaTeamInfo:_OnProcessTeamPlayerInfoWidget Hide Arena Platform Icon in She2!")
                -- widget:_SetPlatformIcon(arenaTeamPlayerInfo.platID)
            end
        end
        --- END MODIFICATION
    end
end

-- BEGIN MODIFICATION @ VIRTUOS 
function ArenaTeamInfo:_EnableGamepadFeature()
    if not IsHD() then
        return 
    end

    if not self._NavGroup then
        self._NavGroup = WidgetUtil.RegisterNavigationGroup(self._wtTeamPlayerInfoSGB, self, "Hittest")
        if self._NavGroup then
            self._NavGroup:AddNavWidgetToArray(self._wtTeamPlayerInfoSGB)
            self._NavGroup:MarkIsStackControlGroup()
            self._NavGroup:SetNavSelectorWidgetVisibility(true)
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup)
        end
    end

    if not self._NavConfigHandler then 
        self._NavConfigHandler = WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.X_Accept, self)
    end
end

function ArenaTeamInfo:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end

    WidgetUtil.RemoveNavigationGroup(self)
    self._NavGroup = nil

    if self._NavConfigHandler then
        WidgetUtil.DisableDynamicNavConfig(self._NavConfigHandler)
        self._NavConfigHandler = nil
    end
end
-- END MODIFICATION

return ArenaTeamInfo