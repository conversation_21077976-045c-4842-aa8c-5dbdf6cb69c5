----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMLooting)
----- LOG FUNCTION AUTO GENERATE END -----------



local LootingMarkItemLogic = require "DFM.Business.Module.LootingModule.LootingMarkItemLogic"
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local LootingConfig = require "DFM.Business.Module.LootingModule.LootingConfig"
local InventoryConfig = require "DFM.Business.Module.InventoryModule.InventoryConfig"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local ELootingKeyCondition = import "ELootingKeyCondition"
local UWidgetLayoutLibrary = import "WidgetLayoutLibrary"
--BEGIN MODIFICATION @ VIRTUOS : 
local UGPInputHelper = import("GPInputHelper")
local EGPInputType = import "EGPInputType"
local UGPInputDelegates = import "GPInputDelegates"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
--END MODIFICATION

---@class LootingKeyMapping_HD : LuaUIBaseView
local LootingKeyMapping_HD = ui("LootingKeyMapping_HD")

local KEY_BINDING_DEFAULT_PADDING = FMargin(100, 0, 0, 0)

local function log(...)
    loginfo("[LootingKeyMapping_HD]", ...)
end

function LootingKeyMapping_HD:Ctor()
    self.defaultKeyBindings = {}
    self.mapCfg2KeyIconWidgets = {}
    self.currentShowKeyBindings = {}
    ---@type ItemBase
    self.currentHoverItem = nil

    self._wtMainContainer = self:Wnd("wtMainContainer", UIWidgetBase)

    --BEGIN MODIFICATION @ VIRTUOS : 
    if IsHD() then
        self.bIsItemDragStart = false
    end
    --END MODIFICATION

    -- self._wtMainContainer = UIUtil.WndScrollGridBox(self, "wtMainContainer", self._OnGetKeyBindingCount,
    --     self._OnProcessItemWidget)
end

function LootingKeyMapping_HD:OnOpen()
    self:_InitDefaultKeyBinding()
    self:_InitDefaultKeyIconWidgets()

    self:RefreshView()
end

function LootingKeyMapping_HD:OnShow()
    self:AddLuaEvent(CommonWidgetConfig.Events.evtIVMouseEnter, self._OnIVMouseEnter, self)
    self:AddLuaEvent(CommonWidgetConfig.Events.evtIVMouseLeave, self._OnIVMouseLeave, self)
    self:AddLuaEvent(Server.InventoryServer.Events.evtItemMove, self._OnItemMove, self)
    self:AddLuaEvent(Server.LootingServer.Events.evtLootingItemMove, self._OnItemMove, self)

    --BEGIN MODIFICATION @ VIRTUOS : 刷新按键提示
    self:_ForceRefreshIconBox()
    self:_EnableInputTypeChangedHandle(true)
    self:AddLuaEvent(CommonWidgetConfig.Events.evtGlobalItemPopViewOpened, self.RefreshView, self)
    self:AddLuaEvent(CommonWidgetConfig.Events.evtGlobalItemDragStart, self._OnGlobalItemDragStart, self)
    self:AddLuaEvent(CommonWidgetConfig.Events.evtGlobalItemDragCancelled, self._OnGlobalItemDragCancelled, self)
    self:AddLuaEvent(CommonWidgetConfig.Events.evtGlobalItemDrop, self._OnGlobalItemDrop, self)
    self:_EnableSimulatedMouseDragEvent(true)
    self:AddLuaEvent(CommonWidgetConfig.Events.evtGlobalItemBindLongPressedAction, self.BindIconWidgetHolding, self)
    self:AddLuaEvent(CommonWidgetConfig.Events.evtGlobalItemUnBindLongPressedAction, self.UnBindIconWidgetHolding, self)
    self:AddLuaEvent(CommonWidgetConfig.Events.evtGlobalHoverItemViewUseAnim, self._OnGlobalHoverItemViewUseAnim, self)
    --END MODIFICATION
end

function LootingKeyMapping_HD:OnHide()
    self:RemoveAllLuaEvent()

    --BEGIN MODIFICATION @ VIRTUOS : 
    self:_EnableInputTypeChangedHandle(false)
    self:_EnableSimulatedMouseDragEvent(false)
    --END MODIFICATION
end

function LootingKeyMapping_HD:OnClose()
    Facade.UIManager:ClearAllSubUI(self)
    Timer.CancelDelay(self._OnEnterRefreshHandle)
end

-----------------------------------------------------------------------
--region Public API

function LootingKeyMapping_HD:RefreshView()
    -- self._wtMainContainer:RefreshAllItems()

    for _, config in ipairs(self.defaultKeyBindings) do
        local bValid = true

        for condition in pairs(config.Conditions) do
            if not self:_CheckEachCondition(condition) then
                bValid = false
                break
            end
        end

        local widget = self.mapCfg2KeyIconWidgets[config]
        if widget and iscppvalid(widget) then
            if bValid then
                widget:SelfHitTestInvisible()
            else
                widget:Collapsed()
            end
        end
    end
end

function LootingKeyMapping_HD:HideAllKeys()
    for _, config in ipairs(self.defaultKeyBindings) do
        local widget = self.mapCfg2KeyIconWidgets[config]
        if widget and iscppvalid(widget) then
            widget:Collapsed()
        end
    end
end

--endregion
-----------------------------------------------------------------------

function LootingKeyMapping_HD:_InitDefaultKeyBinding()
    local tb = Facade.TableManager:GetTable("LootingKeyBinding")
    local list, orders = {}, {}
    for k, config in pairs(tb) do
        local order = tonumber(k)
        if order and config.bEnabled then
            table.insert(list, config)
            orders[config] = order
        end
    end
    local fCompare = function(a, b)
        return orders[a] > orders[b]
    end
    table.sort(list, fCompare)

    self.defaultKeyBindings = list
end

function LootingKeyMapping_HD:_InitDefaultKeyIconWidgets()
    for _, config in ipairs(self.defaultKeyBindings) do
        ---@type HDKeyIconBoxText
        local child = Facade.UIManager:AddSubUI(self, UIName2ID.HDKeyIconBoxText, self._wtMainContainer)
        child = getfromweak(child)
        child:SetType(1)
        if config.bDisplayInput then
            child:SetDisplayActionData(config.ActionOrAxisName)
        else
            child:SetInputActionData(config.ActionOrAxisName, config.RightText)
        end
        if not string.isempty(config.LeftText) then
            child:ShowPreText(true, config.LeftText)
        end
        -- child:InitByConfig(config)

        local slot = UWidgetLayoutLibrary.SlotAsHorizontalBoxSlot(child)
        if slot then
            slot:SetPadding(KEY_BINDING_DEFAULT_PADDING)
        end

        self.mapCfg2KeyIconWidgets[config] = child
    end
end

-- function LootingKeyMapping_HD:_OnGetKeyBindingCount()
--     self.currentShowKeyBindings = {}
--     for _, config in ipairs(self.defaultKeyBindings) do
--         local bValid = true

--         for condition in pairs(config.Conditions) do
--             if not self:_CheckEachCondition(condition) then
--                 bValid = false
--                 break
--             end
--         end

--         if bValid then
--             table.insert(self.currentShowKeyBindings, config)
--         end
--     end

--     return #self.currentShowKeyBindings
-- end

-- ---@param itemWidget HDKeyIconBoxWithText
-- function LootingKeyMapping_HD:_OnProcessItemWidget(position, itemWidget)
--     local config = self.currentShowKeyBindings[position + 1]
--     if config then
--         if config.bDisplayInput then
--             itemWidget:InitCustom_DisplayInputAction(config.ActionOrAxisName, config.LeftText, config.RightText)
--         else
--             itemWidget:InitCustom(config.ActionOrAxisName, config.LeftText, config.RightText)
--         end
--     else
--         logerror("LootingKeyMapping_HD:_OnProcessItemWidget index out of range", position)
--     end
-- end

function LootingKeyMapping_HD:_CheckEachCondition(condition)
    ---@type ItemBase
    local item = self.currentHoverItem
    local slot = item and item.InSlot
    if condition == ELootingKeyCondition.CheckHover then
        return item ~= nil
    elseif condition == ELootingKeyCondition.CheckHoverItemMarked then
        if item then
            local slotType = slot and slot.SlotType
            if slotType and LootingConfig.SlotsNotAllowMark[slotType] ~= true then
                return LootingMarkItemLogic.CheckItemMarkedByPlayer(item.gid)
            end
            return false
        else
            return false
        end
    elseif condition == ELootingKeyCondition.CheckHoverItemNotMarked then
        if item then
            local slotType = slot and slot.SlotType
            if slotType and LootingConfig.SlotsNotAllowMark[slotType] ~= true then
                return not LootingMarkItemLogic.CheckItemMarkedByPlayer(item.gid)
            end
            return false
        else
            return false
        end
    elseif condition == ELootingKeyCondition.CheckHoverItemNotFromPlayer then
        if item then
            if slot then
                return slot:GetSlotGroup() ~= ESlotGroup.Player
            else
                return false
            end
        else
            return false
        end
    elseif condition == ELootingKeyCondition.CheckIsLootingView then
        return Server.LootingServer:GetCurrentSelectorData() ~= nil
    elseif condition == ELootingKeyCondition.CheckCanDiscard then
        if slot then
            if slot.SlotType == ESlotType.NearbyPickups then
                return false
            end
            if ItemOperaTool.bIsInCollectionRoom then
                return InventoryConfig.SlotsAllowOperateInCollectionRoom[slot.SlotType]
            end
            return LootingConfig.SlotsNotAllowOperate[slot.SlotType] ~= true
        end
        return false
    elseif condition == ELootingKeyCondition.CheckItemCanBeMoved then
        if item and slot then
            local slotType = slot.SlotType
            if slotType then
                if ItemOperaTool.bIsInCollectionRoom then
                    return InventoryConfig.SlotsAllowOperateInCollectionRoom[slotType]
                elseif ItemOperaTool.CheckRunWarehouseLogic() then
                    return InventoryConfig.SlotsNotAllowOperate[slotType] ~= true
                else
                    return LootingConfig.SlotsNotAllowOperate[slotType] ~= true
                end
            else
                return false
            end
        end
    elseif condition == ELootingKeyCondition.CheckItemCanBeEquipped then
        if item and slot then
            if item:GetFeature(EFeatureType.Equipment) or item:GetFeature(EFeatureType.Weapon) then
                if slot:GetSlotGroup() ~= ESlotGroup.Player then
                    return true
                end
                if not item:IsEquipped() or slot.SlotType == ESlotType.MainWeaponRight then
                    return true
                end
            end
        end
    --BEGIN MODIFICATION @ VIRTUOS : 新增输入类型条件
    elseif condition == ELootingKeyCondition.CheckIsGamepadInput then
        local curInputType = UGPInputHelper.GetCurrentInputType(self)
        return curInputType == EGPInputType.Gamepad
    elseif condition == ELootingKeyCondition.CheckIsKeyboardInput then
        local curInputType = UGPInputHelper.GetCurrentInputType(self)
        return curInputType == EGPInputType.MouseAndKeyboard
    elseif condition == ELootingKeyCondition.CheckOpenedItemPopView then
        return Module.CommonWidget.GetOpenedPopItemView() ~= nil
    elseif condition == ELootingKeyCondition.CheckItemDragStart then
        return self.bIsItemDragStart
    elseif condition == ELootingKeyCondition.CheckIsOnSimulatedMouseDragging then
        return WidgetUtil.IsOnSimulatedMouseDraging()
    elseif condition == ELootingKeyCondition.CheckIsStopSimulatedMouseDrag then
        return WidgetUtil.IsStopSimulatedMouseDrag()
    elseif condition == ELootingKeyCondition.CheckItemCanBeUsed then
        if item then
            return Module.CommonWidget:IsItemEnableUse(item)
        end
    elseif condition == ELootingKeyCondition.CheckHoverItemNotUseIn then
        if item == nil then
            if self.LastHoverItem == nil or self.LastHoverItem:IsInUsing() == false then
                return true
            end
        else
            if item:IsInUsing() == false then
                return true
            end
        end
    elseif condition == ELootingKeyCondition.CheckItemCanBeDirectlyEquipped then
        if ItemOperaTool.bIsInCollectionRoom then
            return false
        end
        if item and slot then
            local itemNeedTransferType = Module.CommonWidget:GetItemNeedTransferType(item)
            return itemNeedTransferType == Module.CommonWidget.Config.EItemTransferType.FastEquipOrReplace
        end
    elseif condition == ELootingKeyCondition.CheckItemCanUsePlaceSubMenu then
        if item and slot then
            local itemNeedTransferType = Module.CommonWidget:GetItemNeedTransferType(item)
            return itemNeedTransferType == Module.CommonWidget.Config.EItemTransferType.TransferItemPanel
        end
    elseif condition == ELootingKeyCondition.CheckNoItemPopViewOpen then
        return Module.CommonWidget.GetOpenedPopItemView() == nil
    --END MODIFICATION
    elseif condition == ELootingKeyCondition.CheckNotInCollectionRoom then
        return not ItemOperaTool.bIsInCollectionRoom
    elseif condition == ELootingKeyCondition.CheckItemAllowedOperateInCollectionRoom then
        if ItemOperaTool.bIsInCollectionRoom then
            local slotType = slot and slot.SlotType
            if slotType then
                return InventoryConfig.SlotsAllowOperateInCollectionRoom[slotType]
            end
        end
        return true
    elseif condition == ELootingKeyCondition.CheckItemIsAbleToSplit then
        if item and slot then
            return item:IsBullet()
        end
    elseif condition == ELootingKeyCondition.CheckIsInteractivity then
        if item and slot then
            return not (slot.SlotType == ESlotType.MeleeWeapon or slot.SlotType == ESlotType.KeyChain )
        end
    elseif condition == ELootingKeyCondition.CheckIsAbleToDrag then
        if item and slot then
            return slot.SlotType ~= ESlotType.KeyChainContainer
        end
    end
    return false
end

function LootingKeyMapping_HD:_OnIVMouseEnter(itemView)
    self._OnEnterRefreshHandle = Timer.DelayCall(0.1, function ()
        if itemView.item then
            self.currentHoverItem = itemView.item
            self:RefreshView()
        end
    end)
end

function LootingKeyMapping_HD:_OnIVMouseLeave(itemView)
    if self.currentHoverItem == itemView.item then
        self.currentHoverItem = nil
        self:RefreshView()
    end
    Timer.CancelDelay(self._OnEnterRefreshHandle)
end

---@param itemMoveInfo itemMoveInfo
function LootingKeyMapping_HD:_OnItemMove(itemMoveInfo)
    local item = itemMoveInfo.item
    if self.currentHoverItem == item then
        self.currentHoverItem = nil

        --BEGIN MODIFICATION @ VIRTUOS : 
        if IsHD() and WidgetUtil.IsGamepad() then
            self.LastHoverItem = item
        end
        --END MODIFICATION

        self:RefreshView()
    end
end

--BEGIN MODIFICATION @ VIRTUOS : 强制刷新按键提示
function LootingKeyMapping_HD:_ForceRefreshIconBox()
    if not IsHD() then
        return 
    end
    
    for config, KeyIconWidget in pairs(self.mapCfg2KeyIconWidgets) do
        if config.bDisplayInput then
            KeyIconWidget:SetDisplayActionData(config.ActionOrAxisName)
        else
            KeyIconWidget:SetInputActionData(config.ActionOrAxisName, config.RightText)
        end
    end
end

-- 绑定输入类型切换事件
function LootingKeyMapping_HD:_EnableInputTypeChangedHandle(bEnable)
    if not IsHD() then
        return 
    end

    if bEnable then
        if not self._OnNotifyInputTypeChangedHandle then
            self._OnNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._HandleInputTypeChanged, self))
        end
    else
        if self._OnNotifyInputTypeChangedHandle then
            UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._OnNotifyInputTypeChangedHandle)
            self._OnNotifyInputTypeChangedHandle = nil
        end
    end
end

function LootingKeyMapping_HD:_HandleInputTypeChanged(inputType)
    if not IsHD() then
        return 
    end

    self:_ForceRefreshIconBox()
    self:RefreshView()
end

function LootingKeyMapping_HD:_EnableSimulatedMouseDragEvent(bEnable)
    if not IsHD() then
        return 
    end

    if bEnable then
        if not self._OnNotifySimulatedMouseDragHandle then
            self._OnNotifySimulatedMouseDragHandle = WidgetUtil.BindSimulatedMouseDragEvent(self._OnSimulatedMouseDragByGamepad, self)
        end
    else
        if self._OnNotifySimulatedMouseDragHandle then
            WidgetUtil.UnBindSimulatedMouseDragEvent(self._OnNotifySimulatedMouseDragHandle)
            self._OnNotifySimulatedMouseDragHandle = nil
        end
    end
end

function LootingKeyMapping_HD:_OnSimulatedMouseDragByGamepad(bIsDraging)
    if not IsHD() or WidgetUtil:IsGamepad() == false then
        return 
    end

    --Stop Simulate
    if bIsDraging == false then
        self.bIsItemDragStart = false
    end

    self:RefreshView()
end

---@param dragDropInfo ItemDragDropInfo
function LootingKeyMapping_HD:_OnGlobalItemDragStart(dragDropInfo, operation)
    if not IsHD() then
        return 
    end

    self.bIsItemDragStart = true
    self:RefreshView()
end

function LootingKeyMapping_HD:_OnGlobalItemDragCancelled()
    if not IsHD() then
        return 
    end

    self.bIsItemDragStart = false
end

function LootingKeyMapping_HD:_OnGlobalItemDrop()
    if not IsHD() then
        return 
    end

    self.bIsItemDragStart = false
end

function LootingKeyMapping_HD:GetIconWidgetByAction(actionName)
    if actionName == nil then
        return nil
    end

    for _, config in ipairs(self.defaultKeyBindings) do
        if config.ActionOrAxisName == actionName then
            return self.mapCfg2KeyIconWidgets[config]
        end
    end

    return nil
end

function LootingKeyMapping_HD:BindIconWidgetHolding(actionName, bDisplayIpnut, actionHandle)
    if actionName == nil or actionHandle == nil then
        return
    end

    if bDisplayIpnut == true then
        local targetIconWidget = self:GetIconWidgetByAction(actionName)
        if targetIconWidget and targetIconWidget.SetLongPressBindingByInputMonitor then
            targetIconWidget:SetLongPressBindingByInputMonitor(actionHandle)
        end
    end
end

function  LootingKeyMapping_HD:UnBindIconWidgetHolding(actionName, bDisplayIpnut)
    if actionName == nil then
        return
    end

    if bDisplayIpnut == true then
        local targetIconWidget = self:GetIconWidgetByAction(actionName)
        if targetIconWidget and targetIconWidget.HideLongPressKeyIconState then
            targetIconWidget:HideLongPressKeyIconState()
        end
    end
end

function LootingKeyMapping_HD:_OnGlobalHoverItemViewUseAnim(bHover, item)
    if not IsHD() then
        return 
    end

    if bHover == false then
        self.LastHoverItem = nil
    end
    self:RefreshView()
end
--END MODIFICATION

return LootingKeyMapping_HD