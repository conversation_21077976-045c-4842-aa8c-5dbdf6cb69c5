----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGamelet)
----- LOG FUNCTION AUTO GENERATE END -----------



--------------------------------------------------------------------------
--- UI路径映射配置示例（UILayer表示需要加入的层级）
--------------------------------------------------------------------------
UITable[UIName2ID.GameletEntrance] = {
    UILayer = EUILayer.Stack, 
    LuaPath = "DFM.Business.Module.GameletModule.UI.GameletEntrance", 
    BPKey = "WBP_GameletEntrance",

}

UITable[UIName2ID.PandoraStackNoCB] = {
    UILayer = EUILayer.Stack, 
    LuaPath = "DFM.Business.Module.GameletModule.UI.GameletStackNoCB", 
    BPKey = "WBP_GameletUIPanel",
}

UITable[UIName2ID.PandoraStackCB] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.GameletModule.UI.GameletStackCB",
    BPKey = "WBP_GameletUIPanel",
}

UITable[UIName2ID.PandoraStackCBStoreHR] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.GameletModule.UI.GameletStackCBStoreHR",
    BPKey = "WBP_GameletUIPanel",
}

UITable[UIName2ID.PandoraSub] = {
    UILayer = EUILayer.Sub, 
    LuaPath = "DFM.Business.Module.GameletModule.UI.GameletSub", 
    BPKey = "WBP_GameletUIPanel",
}

UITable[UIName2ID.PandoraSubActivity] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GameletModule.UI.GameletSubActivity",
    BPKey = "WBP_GameletUIPanel",
}

UITable[UIName2ID.PandoraPop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.GameletModule.UI.GameletPop",
    BPKey = "WBP_GameletUIPanel",
    IsModal = true,
}

UITable[UIName2ID.PandoraPopOld] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.GameletModule.UI.GameletPopOld",
    BPKey = "WBP_GameletUIPanel",
}

local GameletConfig = 
{
    Loc = {
        NoReady = NSLOCTEXT("GameletModule", "Lua_Gamelet_NoReady", "小应用未准备就绪"),
        AlreadyOpen = NSLOCTEXT("GameletModule", "Lua_Gamelet_AlreadyOpen", "小应用已打开"),
        MicroOfficialWebNoReady = NSLOCTEXT("GameletModule", "Lua_Gamelet_MicroOfficialWebNoReady", "微社区资源正在更新中，请稍后"),
        MicroOfficialWebReady = NSLOCTEXT("GameletModule", "Lua_Gamelet_MicroOfficialWebReady", "微社区更新完成")
    },
    
    evtPandoraShowEntrance = LuaEvent:NewIns("evtPandoraShowEntrance"),
    evtPandoraShowRedpoint = LuaEvent:NewIns("evtPandoraShowRedpoint"),
    evtPandoraShowTaskFinish = LuaEvent:NewIns("evtPandoraShowTaskFinish"),
    evtPandoraShowAppPage = LuaEvent:NewIns("evtPandoraShowAppPage"),
    evtPandoraHideAppPage = LuaEvent:NewIns("evtPandoraHideAppPage"),
    evtPandoraActCenterReady = LuaEvent:NewIns("evtPandoraActCenterReady"),
    evtPandoraPopClose = LuaEvent:NewIns("evtPandoraPopClose"),
    evtPandoraShowStackCurrency = LuaEvent:NewIns("evtPandoraShowStackCurrency"),
    evtPandoraShowItemScene = LuaEvent:NewIns("evtPandoraShowItemScene"),
    evtPandoraGetWidgetArea = LuaEvent:NewIns("evtPandoraGetWidgetArea"),
    evtPandoraShowStackTitle = LuaEvent:NewIns("evtPandoraShowStackTitle"),

    PandoraPopAppTestId = "5858",

    ESceneType = {
        None = 0,
        StoreHotRecommend = 1, --商城热门推荐
    }
}

return GameletConfig
