----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollection)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
local EGPInputType = import "EGPInputType"
-- END MODIFICATION

local CollectionMandelBrickPagePanel = ui("CollectionMandelBrickPagePanel")
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local CollectionLogic = require "DFM.Business.Module.CollectionModule.Logic.CollectionLogic"
local CollectionConfig = Module.Collection.Config
local ItemDetailViewEquip = require "DFM.Business.Module.ItemDetailModule.UI.ItemDetailType3.ItemDetailViewEquip"
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
local LiteCommonDownload =  require "DFM.Business.Module.LitePackageModule.UI.LiteCommonDownload"


function CollectionMandelBrickPagePanel:Ctor()
    self._wtMandelBrickScrollView = UIUtil.WndWaterfallScrollBox(self, "wtMandelBrickScrollView", self._OnGetItemsCount, self._OnProcessItemWidget)
    self._wtBoxArea = self:Wnd("wtBoxArea", UIWidgetBase)
    self._wtActionBtn = self:Wnd("wtActionBtn", DFCommonButtonOnly)
    self._wtActionBtn:Event("OnClicked", self._OnNavigateToStore, self)
    self._wtInfoPanel = self:Wnd("wtInfoPanel", UIWidgetBase)
    self._wtItemDetailView = self:Wnd("wtItemDetailView", ItemDetailViewEquip)
    self._wtItemDetailView:SetWeaponDetailIsHideBtn(true)
    self._wtItemDetailView:SetShowWeaponDetailCheckBox(false)
    self._wtAlertHintBox = self:Wnd("wtAlertHintBox", UIWidgetBase) 
    self._wtAlertHintTxt = self:Wnd("wtAlertHintTxt", UIWidgetBase) 
    self._wtEmptyBg = self:Wnd("wtEmptyBg", UIWidgetBase)
    self._wtEmptySlot = self:Wnd("wtEmptySlot", UIWidgetBase)
    if self._wtEmptySlot then
        Facade.UIManager:RemoveSubUIByParent(self, self._wtEmptySlot)
        local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.CommonEmptyContent, self._wtEmptySlot, nil, nil)
        self._wtEmptyHint = getfromweak(weakUIIns)
    end
    self._wtEmptyHint:BP_SetText(CollectionConfig.Loc.NoItems)
    self._wtEmptyHint:SetCppValue("Set_Type", 1)
    self._wtEmptyHint:BP_Set_Type()
    self._wtTradeBtn = self:Wnd("wtTradeBtn", DFCommonButtonOnly)
    self._wtTradeBtn:Event("OnClicked", self._JumpToTradePage, self)
    self._wtDownloadBtn = self:Wnd("wtDownloadBtn", DFCommonButtonOnly)
    self._wtDownloadBtn:Event("OnClicked", self._DownLoadResources, self)
    self._wtDownloadBtn:Collapsed()
    self._wtHideUIBtn = self:Wnd("wtHideUIBtn", UIButton)
    if not IsHD() then
        --self._wtHideUIBtn:Event("OnClicked", self._ToggleUI, self)
        --self._wtHideUIBtn:Event("OnPressed", self._OnHideUIBtnPressed, self)
        --self._wtHideUIBtn:Event("OnReleased", self._OnHideUIBtnReleased, self)
    end
    self._wtHideUIBtn:Collapsed()
    if Module.ExpansionPackCoordinator:IsSupportLitePackage() then
        self._wtCommonDownload = self:Wnd("wtCommonDownload", LiteCommonDownload)
    end
    if not hasdestroy(self._wtCommonDownload) then
        self._wtCommonDownload:Collapsed()
    end
    self._selectedPos = -1
    self._selectedCell = nil
    self._mandelBrickItems = {}
    self._dropDownDataList = {}
    self._animType = 1
    self._redDotInsMap = setmetatable({}, weakmeta_key)
end


function CollectionMandelBrickPagePanel:OnInitExtraData()
end


-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function CollectionMandelBrickPagePanel:OnOpen()
end

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function CollectionMandelBrickPagePanel:OnClose()
    self._selectedCell = nil
    self:RemoveAllLuaEvent()
    Facade.UIManager:ClearSubUIByParent(self, self._wtEmptySlot)
    Facade.UIManager:ClearSubUIByParent(self, self._wtItemDetailView)
    table.empty(self._redDotInsMap)
    Module.ReddotTrie:UnRegisterDynamicReddot(EReddotTrieObserverType.Collection, ECollectionDynamicDataType.MandelBrick)
end

function CollectionMandelBrickPagePanel:OnShowBegin()
    self:EnableGamepadFeature()
end

function CollectionMandelBrickPagePanel:OnHideBegin()
    self:DisableGamepadFeature()
    self:ClosePopup()
end

---@overload fun(LuaUIBaseView, OnShow)
function CollectionMandelBrickPagePanel:OnShow()
end


---@overload fun(LuaUIBaseView, OnHide)
function CollectionMandelBrickPagePanel:OnHide()
end

function CollectionMandelBrickPagePanel:OnAnimFinished(anim)
    if anim == self.WBP_Collections_BlueprintPage_Cutter_in then
        if not hasdestroy(self._selectedCell) then
            WidgetUtil.SetUserFocusToWidget(self._selectedCell, true)
        end
    end
end

function CollectionMandelBrickPagePanel:ToggleControlAndListeners(bEnable, bFullReset)
    if bEnable == true then
        self:AddLuaEvent(LuaGlobalEvents.evtSceneLoaded, self.OnRefreshModel, self)
        self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult,self._OnDownloadResult,self)
        self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleStateMaybeChanged,self._OnDownloadStateChange,self)
        self:AddLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged, self._OnPackStateChanged, self)
        if not self._scrollStopHandle then
            self._scrollStopHandle = UIUtil.AddScrollBoxClickStopScroll(self._wtMandelBrickScrollView, self)
        end
        CollectionLogic.RegStackUIInputSummary(self._shotcutList, false)
    else
        if bFullReset == true then
            self._selectedCell = nil
            self._selectedPos = -1
        end
        self:RemoveLuaEvent(LuaGlobalEvents.evtSceneLoaded, self.OnRefreshModel, self)
        self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult,self._OnDownloadResult,self)
        self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleStateMaybeChanged,self._OnDownloadStateChange,self)
        self:RemoveLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged, self._OnPackStateChanged, self)
        if self._scrollStopHandle then
            UIUtil.RemoveScrollBoxClickStopScroll(self._scrollStopHandle)
            self._scrollStopHandle = nil
        end
        self._bHideUI = nil
    end
end


function CollectionMandelBrickPagePanel:RefreshView(mainTabIndex, bResetList, bResetTab)
    if bResetList then
        Module.ReddotTrie:UnRegisterDynamicReddot(EReddotTrieObserverType.Collection, ECollectionDynamicDataType.MandelBrick)
    end
    if bResetTab then
        self:_ShowUI()
    end
    self:_OnRefreshMandelBrickItems(bResetList)
end

function CollectionMandelBrickPagePanel:EnableGamepadFeature()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    self._wtActionBtn:SetDisplayInputAction("Collection_Apply_Gamepad", true, nil, true)
    if not self._wtActionBtnHandle then
        self._wtActionBtnHandle = self:AddInputActionBinding("Collection_Apply_Gamepad", EInputEvent.IE_Pressed, self._OnNavigateToStore, self, EDisplayInputActionPriority.UI_Stack)
    end
    if not self._wtNavGroupMandelList then
        self._wtNavGroupMandelList = WidgetUtil.RegisterNavigationGroup(self._wtMandelBrickScrollView, self, "Hittest")
        if self._wtNavGroupMandelList then
            self._wtNavGroupMandelList:AddNavWidgetToArray(self._wtMandelBrickScrollView)
            self._wtNavGroupMandelList:SetScrollRecipient(self._wtMandelBrickScrollView)
            self._wtNavGroupMandelList:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
        end
    end
    if not hasdestroy(self._selectedCell) then
        WidgetUtil.SetUserFocusToWidget(self._selectedCell, true)
    end
    self:_DisableGamepadA()
end

function CollectionMandelBrickPagePanel:DisableGamepadFeature()
    if not IsHD() then
        return
    end
    self:_EnableGamepadA()
    if self._wtActionBtnHandle then
        self:RemoveInputActionBinding(self._wtActionBtnHandle)
    end
    if self._NavConfigHandler then
        WidgetUtil.DisableDynamicNavConfig(self._NavConfigHandler)
    end
    self._NavConfigHandler = nil
    self._wtActionBtnHandle = nil 
    self._wtNavGroupMandelList = nil
    WidgetUtil.RemoveNavigationGroup(self)
end

function CollectionMandelBrickPagePanel:_SetDefaultGamepadFocus()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if self._wtNavGroupMandelList then
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroupMandelList)
    end
end

function CollectionMandelBrickPagePanel:_DisableGamepadA()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if self._NavConfigHandler then
        WidgetUtil.DisableDynamicNavConfig(self._NavConfigHandler)
    end
    self._NavConfigHandler = WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)
end

function CollectionMandelBrickPagePanel:_EnableGamepadA()
    if not IsHD() then
        return
    end
    if self._NavConfigHandler then
        WidgetUtil.DisableDynamicNavConfig(self._NavConfigHandler)
        self._NavConfigHandler = nil
    end
end

function CollectionMandelBrickPagePanel:_OnGetItemsCount()
    return #self._mandelBrickItems
end

function CollectionMandelBrickPagePanel:_OnProcessItemWidget(position, itemWidget)
    local item = self._mandelBrickItems[position]
    itemWidget:SetSize(548, 254)
    itemWidget:SetCppValue("bIsFocusable", IsHD() and WidgetUtil.IsGamepad())
    itemWidget:SetCppValue("bHandleClick", IsHD() and WidgetUtil.IsGamepad())
    itemWidget:SetButtonEnable(not IsHD() or not WidgetUtil.IsGamepad())
    if item then
        local fCheckFunc = CreateCallBack(function(self, curUpdateReddotObType)
            return Server.CollectionServer:IsPropWithRedDot(item.id, item.gid)
        end,self)
        if isvalid(self._redDotInsMap[itemWidget]) and self._redDotInsMap[itemWidget]:GetIsValid() then
            Module.ReddotTrie:UpdateDynamicReddot(itemWidget:GetItemView(), EReddotTrieObserverType.Collection, ECollectionDynamicDataType.MandelBrick, self._redDotInsMap[itemWidget], fCheckFunc, nil)
        else
            self._redDotInsMap[itemWidget] = Module.ReddotTrie:RegisterDynamicReddot(EReddotTrieObserverType.Collection, ECollectionDynamicDataType.MandelBrick, fCheckFunc, nil ,itemWidget:GetItemView(), {EReddotType.Normal})
        end
        local fClickCb = CreateCallBack(self._OnMandelBrickItemClick, self,itemWidget, position)
        itemWidget:BindClickCallback(fClickCb)
        itemWidget:InitCollectionMandelBrickItem(item)

        if self._seasonGroupTitleMap[position] then
            itemWidget:EnableTitle(true, self._seasonGroupTitleMap[position])
        else
            itemWidget:EnableTitle(false) 
        end
    else
        itemWidget:EnableTitle(false) 
    end
    itemWidget:SetSelected(self._selectedPos == position-1)
    if self._selectedPos == position-1 then
        self._selectedCell = itemWidget
        WidgetUtil.SetUserFocusToWidget(self._selectedCell, true)
    end
end


function CollectionMandelBrickPagePanel:_OnRefreshMandelBrickItems(bResetList)
    self._mandelBrickItems = CollectionLogic.GetMandelBricks()
    self:_UpdateSeasonGroups()
    if bResetList then
        self._selectedCell = nil
        self._selectedPos = -1
        if #self._mandelBrickItems > 0 then
            self._selectedPos = 0
            local item = self._mandelBrickItems[self._selectedPos+1]
            if isvalid(item) and Server.CollectionServer:IsPropWithRedDot(item.id) then
                Timer.DelayCall(1, function ()
                    CollectionLogic.RemoveRedDots({item})
                end)
            end
        end
        self._wtMandelBrickScrollView:RefreshAllItems()
    else
        if #self._mandelBrickItems == 0 then
            self._wtMandelBrickScrollView:RefreshAllItems()
        else
            self._wtMandelBrickScrollView:RefreshVisibleItems()
        end
    end
    if #self._mandelBrickItems == 0 then
        self._wtEmptyBg:SelfHitTestInvisible()
        self._wtEmptySlot:SelfHitTestInvisible()
    else
        self._wtEmptyBg:Collapsed()
        self._wtEmptySlot:Collapsed()
    end
    self:_RefreshItemUI(not bResetList)
end

function CollectionMandelBrickPagePanel:OnRefreshModel(curSubStageType)
    curSubStageType = curSubStageType or self._targetSubStage
    if not curSubStageType then
        curSubStageType = ESubStage.LotteryCollection
    end
    if curSubStageType == ESubStage.LotteryCollection then
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.LotteryCollection, "SetDisplayType","Main")
        local weaponDesc, partIndexs
        if self._topPropId then
           weaponDesc, partIndexs = WeaponHelperTool.GetWeaponDescriptionFromSkinID(self._topPropId)
        end
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.LotteryCollection, "ResetDisplayItem")
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.LotteryCollection, "ResetWeapon")
        if isvalid(weaponDesc) then
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.LotteryCollection, "SetDisplayWeapon", weaponDesc, self._topPropId, false, true)
            return
        end
    elseif curSubStageType == ESubStage.CollectionHanging then
        if IsHD() then 
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.CollectionHanging, "SetDisplayType","Main")
        else
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.CollectionHanging, "SetDisplayType","Main_Mobile")
        end
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.CollectionHanging, "ResetDisplayItem")
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.CollectionHanging, "ResetWeapon")
        if self._topPropId then
            local topItem = ItemBase:New(self._topPropId)
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.CollectionHanging, "SetEnableTrans",false)
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.CollectionHanging, "SetIsAdapter", true)
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.CollectionHanging, "SetDisplayWeapon", topItem:GetRawDescObj(), topItem.id, 
            false, false)
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.CollectionHanging, "DestroyWatch")
            return
        end
    end
end


function CollectionMandelBrickPagePanel:_OnMandelBrickItemClick(itemCell, position)
    local item = self._mandelBrickItems[position]
    if self._selectedPos ~= position-1 then
        if self._selectedCell then
            self._selectedCell:SetSelected(false)
            self._selectedCell:SetCppValue("bHandleClick", IsHD() and WidgetUtil.IsGamepad())
        end
        if isvalid(item) and Server.CollectionServer:IsPropWithRedDot(item.id) then
            if self._selectedPos == -1 then
                Timer.DelayCall(1, function ()
                    CollectionLogic.RemoveRedDots({item})
                end)
            else
                CollectionLogic.RemoveRedDots({item})
            end
        end
        self._selectedCell = itemCell
        self._selectedCell:SetSelected(true)
        self._selectedCell:SetCppValue("bHandleClick", false)
        self._selectedPos = position-1
        self:_RefreshItemUI()
    end
end


function CollectionMandelBrickPagePanel:_RefreshItemUI(bUpdateInfo)
    local item = self._mandelBrickItems[self._selectedPos+1]
    if not bUpdateInfo or #self._mandelBrickItems == 0 then
        self._wtDownloadBtn:Collapsed()
        self._wtInfoPanel:Collapsed()
        self._wtActionBtn:Collapsed()
        self._wtTradeBtn:Collapsed()
        self._wtHideUIBtn:Collapsed()
        self._wtAlertHintBox:Collapsed()
        self._shotcutList = {}
        if isvalid(item) then
            self._wtActionBtn:SetIsEnabled(true)
            self._wtActionBtn:SetIsEnabledStyle(true)
            if IsHD() then
                if self._bHideUI then
                    table.insert(self._shotcutList, {actionName = "CollectionShowUI",func = self._ShowUI, caller = self ,bUIOnly = false, bHideIcon = false})
                else
                    table.insert(self._shotcutList, {actionName = "CollectionHideUI",func = self._HideUI, caller = self ,bUIOnly = false, bHideIcon = false}) 
                end
                self:_DownLoadResources()
            else
                self._wtTradeBtn:Visible()
                --self._wtHideUIBtn:Visible()
            end
            if Server.CollectionServer:IsOwnedMandelBrick(item.id) then
                if IsHD() then
                    table.insert(self._shotcutList, {actionName = "GunSkin_Trade",func = self._JumpToTradePage, caller = self ,bUIOnly = false, bHideIcon = false})
                else
                    self._wtTradeBtn:Visible()
                end
            end
            self._wtActionBtn:SetMainTitle(CollectionConfig.Loc.ProceedToUnboxing)
            self._wtActionBtn:SelfHitTestInvisible()
        end
    end
    if isvalid(item) then
        self._wtItemDetailView:UpdateItem(item)
        self._wtInfoPanel:SelfHitTestInvisible()
    end
    self._topPropId = nil
    self._targetSubStage = self:GetTargetSubStage()
    self:_RefreshDownloadBtn()
    CollectionLogic.RegStackUIInputSummary(self._shotcutList, self._bHideUI)
    local fAllLevelFinishCallback = function()
        if Facade.GameFlowManager:GetCurrentSubStage() ~= self._targetSubStage then
            Facade.GameFlowManager:EnterSubStage(self._targetSubStage)
        else
            self:OnRefreshModel(self._targetSubStage)
        end
    end
    Facade.LevelLoadManager:AsyncOperateStreamLevelsBySubStage(self._targetSubStage, true, nil, fAllLevelFinishCallback, false, 30)
    self:UpdateBackground()
end

function CollectionMandelBrickPagePanel:_CheckIsFirstIndexOfMandelBrickGroup(index)
    if self._mandelBrickItems and self._mandelBrickItems[index] then
        if self._mandelBrickItems[index-1] then
            local mandelBrickDataRow = Facade.TableManager:GetRowByKey("MandelBrickDataTable", self._mandelBrickItems[index].id)
            local mandelBrickDataRowPre = Facade.TableManager:GetRowByKey("MandelBrickDataTable", self._mandelBrickItems[index-1].id)
            if mandelBrickDataRow 
            and mandelBrickDataRowPre 
            and mandelBrickDataRow.SeasonID ~= mandelBrickDataRowPre.SeasonID 
            and mandelBrickDataRowPre.SeasonID == Server.BattlePassServer:GetSeasonID() then
                return true
            end
        else
            return true
        end
    end
    return false
end

function CollectionMandelBrickPagePanel:_UpdateSeasonGroups()
    self._seasonGroupTitleMap = {}
    local mandelBrickDataRow
    for index, mandelBrickItem in ipairs(self._mandelBrickItems) do
        if self:_CheckIsFirstIndexOfMandelBrickGroup(index) then
            mandelBrickDataRow = Facade.TableManager:GetRowByKey("MandelBrickDataTable", mandelBrickItem.id)
            if mandelBrickDataRow then
                if mandelBrickDataRow.SeasonID == Server.BattlePassServer:GetSeasonID() then
                    self._seasonGroupTitleMap[index] = CollectionConfig.Loc.CurrentSeason
                else
                    self._seasonGroupTitleMap[index] = CollectionConfig.Loc.PreviousSeason
                end
            end
        end
    end
end

--[[
function CollectionMandelBrickPagePanel:_OnHideUIBtnPressed()
    if not self._bHideUI then
        self.bHandleLongPressTimerHandle = Timer:NewIns(CollectionConfig.LongPressDelta, 1)
        self.bHandleLongPressTimerHandle:AddListener(self._HideUI, self)
        self.bHandleLongPressTimerHandle:Start()
    end
end

function CollectionMandelBrickPagePanel:_OnHideUIBtnReleased()
    if self.bHandleLongPressTimerHandle then
        self.bHandleLongPressTimerHandle:Release()
        self.bHandleLongPressTimerHandle = nil
        if self._bHideUI == true then
            self:_ShowUI()
        end
    end
end
--]]

function CollectionMandelBrickPagePanel:_ToggleUI()
    if self._bHideUI == true then
        self:_ShowUI()
    else
        self:_HideUI()
    end
end

function CollectionMandelBrickPagePanel:_HideUI()
    self._bHideUI = true
    self:HideUI(true)
    if IsHD() then
        self._shotcutList = {}
        table.insert(self._shotcutList, {actionName = "CollectionShowUI",func = self._ShowUI, caller = self ,bUIOnly = false, bHideIcon = false})
        CollectionLogic.RegStackUIInputSummary(self._shotcutList, true)
    else
        Module.CommonBar:SetTopBarVisible(false)
    end
end

function CollectionMandelBrickPagePanel:_ShowUI()
    self._bHideUI = false
    self:HideUI(false)
    if IsHD() then
        self._shotcutList = {}
        table.insert(self._shotcutList, {actionName = "CollectionHideUI",func = self._HideUI, caller = self ,bUIOnly = false, bHideIcon = false})
        local item = self._mandelBrickItems[self._selectedPos+1]
        if isvalid(item) then
            table.insert(self._shotcutList, {actionName = "GunSkin_Trade",func = self._JumpToTradePage, caller = self ,bUIOnly = false, bHideIcon = false})
        end
        CollectionLogic.RegStackUIInputSummary(self._shotcutList, false)
    else
        Module.CommonBar:SetTopBarVisible(true)
    end
end


function CollectionMandelBrickPagePanel:_OnNavigateToStore()
    if self._mandelBrickItems[self._selectedPos+1] then
        CollectionLogic.ShowMandelBrickPage(self._mandelBrickItems[self._selectedPos+1].id, 0, EMandelOpenSource.Collection)
    end
end


function CollectionMandelBrickPagePanel:_JumpToTradePage()
    if self._mandelBrickItems[self._selectedPos+1] then
        Module.Market:JumpToMandelBrickSellPage(self._mandelBrickItems[self._selectedPos+1].id)
    end
end

function CollectionMandelBrickPagePanel:_DownLoadResources()
    if not hasdestroy(self._wtCommonDownload) then
        local item = self._mandelBrickItems[self._selectedPos+1]
        local moduleKey = CollectionLogic.GetDownloadModuleKeyByItemId(item and item.id or nil)
        self._wtCommonDownload:InitModuleKey(moduleKey)
        self._wtCommonDownload:SelfHitTestInvisible()
        logerror("[v_dzhanshen] CollectionMandelBrickPagePanel:_DownLoadResources moduleKey="..moduleKey)
    end
end

function CollectionMandelBrickPagePanel:_OnDownloadStateChange(moduleName, bDownloaded)
    if self._wtDownloadBtn then
        local item = self._mandelBrickItems[self._selectedPos+1]
        local moduleKey = CollectionLogic.GetDownloadModuleKeyByItemId(item and item.id or nil)
        logerror("[v_dzhanshen] CollectionMandelBrickPagePanel:_OnDownloadStateChange moduleKey="..moduleKey.." bDownloaded="..tostring(bDownloaded))
        if moduleName == moduleKey then
            if not bDownloaded then
                self._wtDownloadBtn:Visible()
            else
                self._wtDownloadBtn:Collapsed()
            end
        end
    end
end

function CollectionMandelBrickPagePanel:_OnDownloadResult(moduleName, bDownloaded, errorCode)
    local item = self._mandelBrickItems[self._selectedPos+1]
    local moduleKey = CollectionLogic.GetDownloadModuleKeyByItemId(item and item.id or nil)
    logerror("[v_dzhanshen] CollectionMandelBrickPagePanel:_OnDownloadResult moduleKey="..moduleKey)
    if moduleName == moduleKey then
        self._wtMandelBrickScrollView:RefreshVisibleItems()
        self:OnRefreshModel(self._targetSubStage)
        self:_RefreshDownloadBtn()
    end
end

function CollectionMandelBrickPagePanel:_OnPackStateChanged(moduleName, packState)
    local isSuccess = packState == Module.ExpansionPackCoordinator.Config.PackState.Installed
    self:_OnDownloadResult(moduleName, isSuccess, 0)
end

function CollectionMandelBrickPagePanel:_RefreshDownloadBtn()
    if self._wtDownloadBtn then
        local item = self._mandelBrickItems[self._selectedPos+1]
        local moduleKey = CollectionLogic.GetDownloadModuleKeyByItemId(item and item.id or nil)
        local bDownloaded = Module.ExpansionPackCoordinator:IsDownloadedByModuleName(moduleKey)
        logerror("[v_dzhanshen] CollectionMandelBrickPagePanel:_RefreshDownloadBtn moduleKey="..moduleKey.." bDownloaded="..tostring(bDownloaded))
        if not bDownloaded and isvalid(item) then
            if not hasdestroy(self._wtCommonDownload) then
                self._wtCommonDownload:InitModuleKey(moduleKey)
            end
            self._wtDownloadBtn:Visible()
        else
            self._wtDownloadBtn:Collapsed()
            if not hasdestroy(self._wtCommonDownload) then
                self._wtCommonDownload:Collapsed()
            end
        end
    end
end

function CollectionMandelBrickPagePanel:_GetTopPrizeItemId()
    local item = self._mandelBrickItems[self._selectedPos + 1]
    if isvalid(item) then
        return Module.Collection:GetMandelBrickTopPrizeItemId(item.id)
    end
    return nil
end

function CollectionMandelBrickPagePanel:ClosePopup()

end

function CollectionMandelBrickPagePanel:OnInputTypeChanged(inputType)
    self._wtMandelBrickScrollView:RefreshVisibleItems()
end

function CollectionMandelBrickPagePanel:BindSetBackgourndCallback(callback, caller)
    self._setBackgourndCallback = SafeCallBack(callback, caller)
end


function CollectionMandelBrickPagePanel:UpdateBackground()
    if self._setBackgourndCallback then
        self._setBackgourndCallback(nil, false)
    end
end


function CollectionMandelBrickPagePanel:GetTargetSubStage()
    local topPropId, itemMainType, itemSubType = self:_GetTopPrizeItemId()
    self._topPropId = topPropId
    if itemMainType == EItemType.WeaponSkin then
        self._targetSubStage = ESubStage.LotteryCollection
    elseif itemMainType == EItemType.Adapter and itemSubType == ItemConfig.EAdapterItemType.Pendant then
        self._targetSubStage = ESubStage.CollectionHanging
    else
        self._targetSubStage = ESubStage.LotteryCollection
    end
    return self._targetSubStage
end

return CollectionMandelBrickPagePanel
