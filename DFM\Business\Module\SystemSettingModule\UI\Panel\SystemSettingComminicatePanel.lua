----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class SystemSettingComminicatePanel
local SystemSettingComminicatePanel = ui("SystemSettingComminicatePanel")
local ClientComminicateSetting = import "ClientComminicateSetting"
local EDFMGamePlayMode = import "EDFMGamePlayMode"
local ComminicateSettingLogic = require "DFM.Business.Module.SystemSettingModule.Logic.ComminicateSettingLogic"

function SystemSettingComminicatePanel:Ctor()
    self._clientComminicateSetting = ClientComminicateSetting.Get(GetWorld())
    self._battleTable = Facade.TableManager:GetTable("PresetChatBattle")
    self._raidTable = Facade.TableManager:GetTable("PresetChatRaid")
    self._solTable = Facade.TableManager:GetTable("PresetChatSOL")

    --self._wtOpenflag = self:Wnd("OpenflagOption", UIWidgetBase)
    self._wtSceneOption = self:Wnd("SceneOption", UIWidgetBase)
    --self._wtBlockVoice = self:Wnd("BlockVoiceOption", UIWidgetBase)
    self._wtFlagColorA = self:Wnd("FlagColorAOption", UIWidgetBase)
    self._wtComNumTxt = self:Wnd("TextBlock_104", UITextBlock)

    self._chatWidgetTbl = {}
    self._useWidgetTbl = {}
    self._datable = nil

    self._wtCanUseChatList = self:Wnd("wtCanUseChatList", UIWidgetBase)
    self._wtUseChatList = self:Wnd("wtUseChatList", UIWidgetBase)
    self._wtRootScrollBox = self:Wnd("wtRootScrollBox", UIWidgetBase)
    self._wtSecondLanguageForPeakGame = self:Wnd("SecondLanguageForPeakGame", UIWidgetBase)
    self._wtSecondLanguageForPeakGame:Wnd("DFButton_0",UIButton):Event("Onclicked",self._OpenSecondLanguage,self)
    self.__wtSecondLanguageTxt = self._wtSecondLanguageForPeakGame:Wnd("CultureText",UITextBlock)
    self._txtNum = 0

    self._modeTable = {
        [0] = EDFMGamePlayMode.GamePlayMode_SOL,
        [1] = EDFMGamePlayMode.GamePlayMode_Breakthrough,
    }

    self._mode = self._modeTable[0]
    self._index = 0

    self._chatTbl = {
        [EDFMGamePlayMode.GamePlayMode_SOL] = self._solTable,
        --[EDFMGamePlayMode.GamePlayMode_Raid] = self._solTable,
        [EDFMGamePlayMode.GamePlayMode_Breakthrough] = self._battleTable,
    }
    self.chatIndexTbl = {
        [EDFMGamePlayMode.GamePlayMode_SOL] = self._clientComminicateSetting.SolChatArray,
        --[EDFMGamePlayMode.GamePlayMode_Raid] = self._clientComminicateSetting.SolChatArray,
        [EDFMGamePlayMode.GamePlayMode_Breakthrough] = self._clientComminicateSetting.BattleChatArray,
    }

    self._chatAddFuncTbl = {
        [EDFMGamePlayMode.GamePlayMode_SOL] = ComminicateSettingLogic.ProcessAddSolChat,
        --[EDFMGamePlayMode.GamePlayMode_Raid] = ComminicateSettingLogic.ProcessAddSolChat,
        [EDFMGamePlayMode.GamePlayMode_Breakthrough] = ComminicateSettingLogic.ProcessAddBattleChat,
    }

    self._chatMinusFuncTbl = {
        [EDFMGamePlayMode.GamePlayMode_SOL] = ComminicateSettingLogic.ProcessMinusSolChat,
        --[EDFMGamePlayMode.GamePlayMode_Raid] = ComminicateSettingLogic.ProcessMinusSolChat,
        [EDFMGamePlayMode.GamePlayMode_Breakthrough] = ComminicateSettingLogic.ProcessMinusBattleChat,
    }

    self._wtFlagPanel = self:Wnd("wtFlagPanel", UIWidgetBase)
    self._wtQuickChatPanel = self:Wnd("wtQuickChatPanel", UIWidgetBase)
    self._wtGroupTab = self:Wnd("wtGroupBox", UIWidgetBase)
    self._wtGroupTab:Event("OnTabIndexChanged", self._SetCheckByIndex, self)
    self:BindBtnEvent()
end

function SystemSettingComminicatePanel:OnOpen()
    self:InitSecondLanguageTxt()
end

function SystemSettingComminicatePanel:OnShowBegin()
    --self:_SetCheckByIndex(self._index)
    self:_InitCanUseChatList(self._chatTbl[self._mode])
    self:_InitUseChatList()
    self:_InitSetting()
    self._wtRootScrollBox:ScrollToStart()
    self._wtRootScrollBox:EndInertialscrolling()
    Module.Chat:SetPresetChatIschanged(false)
end

function SystemSettingComminicatePanel:OnHide()
    if self._reorderableListComponent then
        self._reorderableListComponent:Reset()
    end
end

function SystemSettingComminicatePanel:BindBtnEvent()
    --self._wtOpenflag:SetBtnClickByIndex(1, function () ComminicateSettingLogic.ProcessbOpenFlag(self._mode, true) end)
    --self._wtOpenflag:SetBtnClickByIndex(2, function () ComminicateSettingLogic.ProcessbOpenFlag(self._mode, false) end)

    self._wtSceneOption:SetBtnClickByIndex(1, function () ComminicateSettingLogic.ProcessSceneOption(true) end)
    self._wtSceneOption:SetBtnClickByIndex(2, function () ComminicateSettingLogic.ProcessSceneOption(false) end)

    --self._wtBlockVoice:SetBtnClickByIndex(1, function () ComminicateSettingLogic.ProcessbOpenFlagVoice(self._mode, true) end)
    --self._wtBlockVoice:SetBtnClickByIndex(2, function () ComminicateSettingLogic.ProcessbOpenFlagVoice(self._mode, false) end)
    self:AddLuaEvent(Module.SystemSetting.Config.Event.evtSetSecondLanguage, self.OnSetSecondLanguage, self)
end

function SystemSettingComminicatePanel:_InitSetting()
    if self._mode == EDFMGamePlayMode.GamePlayMode_SOL then
        self._wtSceneOption:Collapsed()
    else
        self._wtSceneOption:SelfHitTestInvisible()
    end
    --self._wtOpenflag:InitSettingBtnByBool(self._clientComminicateSetting.bOpenFlag:Get(self._mode))
    self._wtSceneOption:InitSettingBtnByBool(self._clientComminicateSetting.bMPSingleClickCreateLocMark)
    --self._wtBlockVoice:InitSettingBtnByBool(self._clientComminicateSetting.bOpenFlagVoice:Get(self._mode))
    self._wtFlagColorA:InitSettingSlider(
        self._clientComminicateSetting.FlagColorAMin,
        self._clientComminicateSetting.FlagColorAMax,
        math.ceil(self._clientComminicateSetting.FlagColorA:Get(self._mode)),
        function (value)
            ComminicateSettingLogic.ProcessFlagColorA(self._mode, value)
        end,
        nil,
        nil,
        10)
end

function SystemSettingComminicatePanel:_SetCheckByIndex(index)
    self._index = index
    self._mode = self._modeTable[index]
    self._wtRootScrollBox:ScrollToStart()
    self._wtRootScrollBox:EndInertialscrolling()
    self:_InitCanUseChatList(self._chatTbl[self._mode])
    self:_InitUseChatList()
    self:_InitSetting()
    self:PlayAnimation(self.WBP_SetUp_Comminicate_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
end

function SystemSettingComminicatePanel:_InitCanUseChatList(dataTable)
    self._datable = dataTable
    self._chatWidgetTbl = {}
    Facade.UIManager:RemoveSubUIByParent(self, self._wtCanUseChatList)
    for i, info in ipairs(dataTable) do
        ---@type SystemSettingPanelTypeCheckCell
        local chatWidget, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.SettingQuickChatBtn, self._wtCanUseChatList, nil,self, info.PresetChat, i)
        self._chatWidgetTbl[i] = instanceID
        local uiIns = getfromweak(chatWidget)
        if uiIns then
            uiIns:SetType(0, false)
        end
    end
end

function SystemSettingComminicatePanel:_UpdateChatList()
    local allChildren = self._wtUseChatList:GetAllChildren()
    for index, child in pairs(allChildren) do
        child:SetIndex(index)
        Module.Chat:SetPresetChatIschanged(true)
    end
end

function SystemSettingComminicatePanel:_InitUseChatList()
    self._txtNum = 0
    self._indexTbl = self.chatIndexTbl[self._mode]
    self._useWidgetTbl = {}
    Facade.UIManager:RemoveSubUIByParent(self, self._wtUseChatList)
    for i = 0, 7 do
        local txt = nil
        if i < self._indexTbl:Num() and self._indexTbl:Get(i) > 0 then
            self._txtNum = self._txtNum + 1
            txt = self._datable[self._indexTbl:Get(i)].PresetChat
            local weakUIIns, instanceId = Facade.UIManager:GetSubUI(self, UIName2ID.SettingQuickChatBtn, self._chatWidgetTbl[self._indexTbl:Get(i)])
            local uiIns = getfromweak(weakUIIns)
            if uiIns then
                uiIns:SetType(0, true)
            end
        end
        local useWidget, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.UseChatBtn, self._wtUseChatList, nil, self, txt, i)

        self._useWidgetTbl[i] = instanceID
    end
    self._wtComNumTxt:SetText(string.format(Module.SystemSetting.Config.Loc.CommincateNumTxt,self._indexTbl:Num()))
    local function fOnDragStart(cell)
        cell:SetSelected(true)
    end

    local function fOnDragStop(cell)
        cell:SetSelected(false)
    end
    local function fOnShiftPosFinally(lastIndex, newIndex)
        if lastIndex > self._indexTbl:Num() then
            return
        end
        local tableNum = self._indexTbl:Get(lastIndex - 1)
        self._chatMinusFuncTbl[self._mode](lastIndex - 1)
        if newIndex <= self._txtNum - 1 then
            self._chatAddFuncTbl[self._mode](tableNum, newIndex - 1)
        else
            self._chatAddFuncTbl[self._mode](tableNum)
        end
        self:_UpdateChatList()
    end
    ---@type ReorderableListParams
    local params = {
        verticalBox = self._wtUseChatList,
        rootScrollBox = self._wtRootScrollBox,
        fOnDragStart = fOnDragStart,
        fOnDragStop = fOnDragStop,
        fOnShiftPosFinally = fOnShiftPosFinally
    }

    if self._reorderableListComponent then
        self._reorderableListComponent:Release()
        self._reorderableListComponent = nil
    end
    self._reorderableListComponent = Module.CommonWidget:InitReorderableList(params)
    self._reorderableListComponent:SetLimit(1, self._txtNum)
end

function SystemSettingComminicatePanel:AddUseChat(index)
    if self._txtNum < 8 then
        self._chatAddFuncTbl[self._mode](index)
        self._txtNum = self._txtNum + 1
        self:_InitUseChatList()
        self:_UpdateChatList()
    else
        Module.CommonTips:ShowSimpleTip(Module.SystemSetting.Config.Loc.UseCommunicateMax)
    end
end

function SystemSettingComminicatePanel:MinusUseChat(index)
    local weakUIIns, instanceId = Facade.UIManager:GetSubUI(self, UIName2ID.SettingQuickChatBtn, self._chatWidgetTbl[self._indexTbl:Get(index)])
    local uiIns = getfromweak(weakUIIns)
    if uiIns then
        uiIns:SetType(0, false)
    end

    self._chatMinusFuncTbl[self._mode](index)
    self:_InitUseChatList()
    self:_UpdateChatList()
end

function SystemSettingComminicatePanel:InitSecondLanguageTxt()
    if self._clientComminicateSetting then
        local cultureSign = self._clientComminicateSetting.SecondLanguage
        if cultureSign ~= "" then
            local displayLanguageText = string.format("<dfmrichtext type=\"img\" id=\"SetUpText_%s\"/>", cultureSign)
        else
            displayLanguageText = Module.SystemSetting.Config.Loc.NotSelect
        end

        if cultureSign == "zh-Hans" then
            displayLanguageText = "<dfmrichtext type=\"img\" id=\"SetUpText_zh\"/>" -- 海外主机单独支持简体中文
        end
        self.__wtSecondLanguageTxt:SetText(displayLanguageText)
    end
    
end

function SystemSettingComminicatePanel:_OpenSecondLanguage()
    Facade.UIManager:AsyncShowUI(UIName2ID.SystemSettingSecondLanguagePopView)
end

function SystemSettingComminicatePanel:OnSetSecondLanguage(txt)
    self.__wtSecondLanguageTxt:SetText(txt)
end

return SystemSettingComminicatePanel
