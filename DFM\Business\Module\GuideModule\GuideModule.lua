----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGuide)
----- LOG FUNCTION AUTO GENERATE END -----------



-- 引导不要跨gameflow，如果真的需要跨gameflow，应该拆成两个引导，分别在两个gameflow中执行
---@class GuideModule : ModuleBase
local GuideModule = class("GuideModule", require("DFM.YxFramework.Managers.Module.ModuleBase"))
local GuideLogic = require "DFM.Business.Module.GuideModule.GuideLogic"
local GuideLoadLogic = require "DFM.Business.Module.GuideModule.GuideLoadLogic"
local GuideEvtLogic = require "DFM.Business.Module.GuideModule.GuideEvtLogic"
local GuideLogic_CheckCondition = require "DFM.Business.Module.GuideModule.GuideLogic_CheckCondition"
local GuideConfig = require "DFM.Business.Module.GuideModule.GuideConfig"
local GuideLogic_Tick = require "DFM.Business.Module.GuideModule.GuideLogic_Tick"
local EGameHUDSate = import "GameHUDSate"
local UDFMGuideManager = import "DFMGuideManager"
local UGameVersionUtils = import "GameVersionUtils"
local UDFMGameLoadingManager = import("DFMGameLoadingManager")
local EBackgroundType = import "EBackgroundType"
local UHardwareParamHelper = import "HardwareParamHelper"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"

require "DFM.Business.DataStruct.GuideStruct.GuideDefines"

local function log(...) loginfo("[GuideModule]", ...) end
local function warn(...) logwarning("[GuideModule]", ...) end
local function err(...) logerror("[GuideModule]", ...) end


function GuideModule:Ctor()
end
---------------------------------------------------------------------------------
--- Module 生命周期
---------------------------------------------------------------------------------
--- 模块Init回调，用于初始化一些数据
---@overload fun(ModuleBase, OnInitModule)
function GuideModule:OnInitModule()
    log("OnInitModule")
    -- 启动器设置是否跳过引导
    local locSkipGuideStr = UGameVersionUtils.GetLauncherParamsByKey("skipGuide")
    if locSkipGuideStr == "yes" then
        log("Launcher Skip All Guide")
        self:SkipAllGuide()
    end

    self._bRegisterTick = false
    if not GuideConfig.FlagEnableGuide then return end

    GuideEvtLogic.BindLuaEventsGlobal()
    --GuideLogic.UpdateGuideInGameData()
end

--- 若为非懒加载模块，则在Init后调用;对应每个OnGameFlowChangeEnter
--- 模块默认加载资源（预加载UI蓝图、需要用到的图片等等
---@overload fun(ModuleBase, OnLoadModule)
function GuideModule:OnLoadModule()
    if not GuideConfig.FlagEnableGuide then return end

    --self:_AddGuideMainUI()
end

--- 无论是否懒加载都会调用，对应每个OnGameFlowChangeLeave
--- 模块默认卸载资源
---@overload fun(ModuleBase, OnUnloadModule)
function GuideModule:OnUnloadModule()
    if not GuideConfig.FlagEnableGuide then return end

    self:_RemoveGuideMainUI()
    self:_ClearAutoPlayGuideInfo()
end

--- 注销LuaEvent、Timer监听
---@overload fun(ModuleBase, OnDestroyModule)
function GuideModule:OnDestroyModule()
    GuideLogic.CloseGmUI()
    GuideEvtLogic.UnBindInGameEvents()
    GuideEvtLogic.UnBindLuaEventsGlobal()
    self:_ClearAutoPlayGuideInfo()
    self:RemoveAllLuaEvent()
    self:RemoveSelfTick()
end

---@overload fun(ModuleBase, OnGameFlowChangeLeave)
function GuideModule:OnGameFlowChangeLeave(gameFlowType)
    if not GuideConfig.FlagEnableGuide then return end

    self.Field:ClearCurShowUIState()
    -- 有一个手游mp引导跨gameflow，暂时先不清理
    if gameFlowType == EGameFlowStageType.GameToLobby then
        if IsHD() then
            GuideLogic.CloseGmUI()
            self:StopAllGuide()
        end
    else
        GuideLogic.CloseGmUI()
        self:StopAllGuide()
    end

    if gameFlowType == EGameFlowStageType.Game then
        --Server.GuideServer:RequestMatchCountInfo()
        GuideEvtLogic.UnBindInGameEvents()
        self:RemoveSelfTick()
        self.Field:ClearInGameInfo()
        GuideLogic.StopTimerForGuideInGame()
        GuideLogic.SetWaitSettlementState(true) -- Wait the server settlement process
    elseif gameFlowType == EGameFlowStageType.SafeHouse then
        -- self.Field.bBackFromSafeHouse = true
        GuideConfig.EGuideEvent.evtGuideLeaveSafeHouse:Invoke()
        self.Field:ResetNewPlayerGuidePassed()
    elseif gameFlowType == EGameFlowStageType.Intro then
        -- 如果gameflow离开了intro，但是intro还没完成，强制把intro状态设为完成
        if not GuideLogic.IsIntroFinished() then
            GuideLogic.SetIntroFinished()
        end
    elseif gameFlowType == EGameFlowStageType.ModeHallToSafeHouse then

    end

    self:_ClearAutoPlayGuideInfo()
    GuideEvtLogic.OnGameFlowChangeLeave(gameFlowType)
end

---@overload fun(ModuleBase,OnGameFlowChangeEnter)
---@param gameFlowType EGameFlowStageType
function GuideModule:OnGameFlowChangeEnter(gameFlowType)
    if not GuideConfig.FlagEnableGuide then return end

    local bDebugForceUnloadDatatable = UHardwareParamHelper.GetConsoleVariableRefBoolValue("r.DataTableLiteForceCloseBatchLoad")
    if bDebugForceUnloadDatatable then
        loginfo("bDebugForceUnloadDatatable is true, force init guide config")
        self.Field:_InitCfgTable()
    end

    local bNeedDelayLoading = false
    local bNeedFakeLoading = false
    if gameFlowType == EGameFlowStageType.SafeHouse then
        -- 局外每次打开安全屋大厅都检测是否启动新手引导
        GuideLogic.SetWaitSettlementState(true)

        -- self:_TryStartGuide()

        -- 如果gameflow进入了安全屋，但是intro还没完成，强制把intro状态设为完成
        if not GuideLogic.IsIntroFinished() then
            GuideLogic.SetIntroFinished()
        end
    elseif gameFlowType == EGameFlowStageType.Game then
        -- 绑定局内和引导相关的事件
        GuideLogic.DelayOpenUIInGame()
        self.Field:ClearInGameInfo()
        self:ProcessInGame(gameFlowType)
        GuideLogic.StopLoadingViewControl()

    elseif gameFlowType == EGameFlowStageType.GameToSafeHouse then
        bNeedFakeLoading = false
        bNeedDelayLoading = true
        self.Field.bSolSettlementPopEnd = false

    elseif gameFlowType == EGameFlowStageType.BattleFieldToSafeHouse then
        bNeedFakeLoading = true
        bNeedDelayLoading = true
        self.Field.bMpSettlementPopEnd = false
    elseif gameFlowType == EGameFlowStageType.GameToLobby then
        -- 为了掩盖sol新手关的加载创建，mp用不到
        -- bNeedFakeLoading = true
        -- bNeedDelayLoading = true
        self.Field.bMpSettlementPopEnd = false
    elseif
        gameFlowType == EGameFlowStageType.ModeHallToSafeHouse or
        --bug=143049722 [【CN】【PC】新手引导状态-点击sol进入大厅-干员与武器错位](https://tapd.woa.com/r/t?id=143049722&type=bug) 
        -- bhd到sol新手关未屏蔽， TODO: 其他的跳转?
        gameFlowType == EGameFlowStageType.LobbyBHDToSafeHouse 
    then
        log("need fakeload")
        bNeedFakeLoading = true
        bNeedDelayLoading = true
    end

    if bNeedDelayLoading then
        loginfo("OnGameFlowChangeLeave, IsNewPlayerMatchFinished?",Server.GuideServer:IsNewPlayerMatchFinished())
        local DFMGameLoadingManager = UDFMGameLoadingManager.GetGameLoadingManager(GetGameInstance())
        if DFMGameLoadingManager then
            local finishNewMatch = Server.GuideServer:IsNewPlayerMatchFinished()
            DFMGameLoadingManager:SetCloseLoadingViewAfterLoading(finishNewMatch)
            self.Field.bControlLoadingView = not finishNewMatch
            if not finishNewMatch and bNeedFakeLoading then
                loginfo("use fake loading for new player match")
                local ELoadingDestination = import "ELoadingDestination"
                LuaGlobalEvents.evtStartPreshowLoadingView:Invoke(ELoadingDestination.ELD_MainHall)
                self.Field:StartFakeLoadingTimer(8)
            end
        else
            logerror("OnGameFlowChangeLeave, DFMGameLoadingManager is nil !!!")
        end
    end


    GuideEvtLogic.OnGameFlowChangeEnter(gameFlowType)
end

---@overload fun(ModuleBase, OnLoadingLogin2Frontend)
function GuideModule:OnLoadingLogin2Frontend(gameFlowType)
    GuideLoadLogic.OnLoadingLogin2FrontendProcess(gameFlowType)
end

---@overload fun(ModuleBase, OnLoadingGame2Frontend)
function GuideModule:OnLoadingGame2Frontend(gameFlowType)
    GuideLoadLogic.OnLoadingGame2FrontendProcess(gameFlowType)
end

---@overload fun(ModuleBase, OnLoadingFrontend2Game)
function GuideModule:OnLoadingFrontend2Game(gameFlowType)
    GuideLoadLogic.OnLoadingFrontend2GameProcess(gameFlowType)
end

function GuideModule:ProcessInGame(gameFlowType)
    -- if GuideLogic.IsGameAndSolMode(gameFlowType) then
    --     log("ProcessInSolGame success")
    --     -- 注册tick
    --     -- 2024/11/20 此时不再注册tick
    --     if GuideLogic_Tick.CheckNeedTick() then
    --         self:RegisterSelfTick()
    --     end
    --     self:_InitAutoPlayGuideInfo()
    -- else
    --     log("ProcessInSolGame fail, not in sol mode")
    -- end

    GuideEvtLogic.BindInGameEvents() --
end

-- function GuideModule:_AddGuideMainUI(gameFlowType)
--     log("GuideModule:_AddGuideMainUI")
--     local _callback = function (ins)
--         ins:SetVisibility(ESlateVisibility.Collapsed)
--         if self.Field.onMainUILoadFinishCallback then
--             self.Field.onMainUILoadFinishCallback()
--             self.Field.onMainUILoadFinishCallback = nil
--         end
--     end
--     local guideMainUIHandle = Facade.UIManager:AsyncShowUI(UIName2ID.GuideMainUI, _callback, nil)
--     self.Field:SetMainUIHandle(guideMainUIHandle)
-- end

function GuideModule:_RemoveGuideMainUI(gameFlowType)
    log("GuideModule:_RemoveGuideMainUI")
    Facade.UIManager:CloseUIByHandle(self.Field:GetMainUIHandle())
    self.Field:SetMainUIHandle(nil)
end

function GuideModule:RegisterSelfTick()
    if GuideLogic.GetMatchCount(MatchGameRule.SOLGameRule) < 1 then return end
    if Server.TipsRecordServer:GetBoolean(Server.TipsRecordServer.keys.GuideLootBtnClickedInGame) then return end
    if not self._bRegisterTick then
        LuaTickController:Get():RegisterTick(self)
        self._bRegisterTick = true
        GuideLogic_Tick.InitTickInfo()
    end
end

function GuideModule:RemoveSelfTick()
    if self._bRegisterTick then
        LuaTickController:Get():RemoveTick(self)
        self._bRegisterTick = false
    end
end

function GuideModule:LateUpdate()
    GuideLogic_Tick.LateUpdate()
end

function GuideModule:_TryStartGuide()
    GuideEvtLogic.OnGuideOpenSafehouse()
end

function GuideModule:_InitAutoPlayGuideInfo()
    GuideLogic.InitAutoPlayGuideInfo()
end

function GuideModule:_ClearAutoPlayGuideInfo()
    GuideLogic.ClearAutoPlayGuideInfo()
end

---------------------------------------------------------------------------------
--- Module Public API
---------------------------------------------------------------------------------

-- 启动某个阶段的引导。推荐使用
---@param guideStageId number EGuideStage
function GuideModule:StartStageGuide(guideStageId)
    if not GuideConfig.FlagEnableGuide then return end

    return GuideLogic.DoStartStageGuide(guideStageId)
end

-- 打开常驻图文弹窗引导
---@param explainId number EGuideExplain
function GuideModule:ShowPopWindowGuide(explainId)
    if not GuideConfig.FlagEnableGuide then return end

    return GuideLogic.ShowPopWindowGuide(explainId)
end

-- 专用于启动 新手引导-局外部分
-- 局内部分自行调用 StartStageGuide
-- gameFlow到安全屋、服务器通知新的引导解锁、某个引导结束、重新回到安全屋 这些情况会触发新手引导开启的检测
function GuideModule:StartNewPlayerGuideInFrontEnd()
    if not GuideConfig.FlagEnableGuide then return end

    GuideLogic.TryStartNewPlayerGuideInFrontEnd()
end

-- 需要被引导的特殊控件，显式注册给引导模块
function GuideModule:AddGuideWidgetProxy(name, targetWidget)
    if not GuideConfig.FlagEnableGuide then return end
    -- loginfo("AddGuideWidgetProxy", name, targetWidget)
    return GuideLogic.AddGuideWidgetProxy(name, targetWidget)
end

function GuideModule:RemoveGuideWidgetProxy(name)
    if not GuideConfig.FlagEnableGuide then return end

    return GuideLogic.RemoveGuideWidgetProxy(name)
end

-- 【编辑器生效】强制从某一步启动引导，跳过回滚逻辑检查，跳过引导阶段是否启用检查
-- 2025/1/8 <dexzhou> 取消编辑器限制, 出现异常请重置账号
function GuideModule:ForceStartGuide(guideId)
    if not GuideConfig.FlagEnableGuide then return end

    return GuideLogic.DoForceStartGuide(guideId)
end

---@example slua.Do Module.Guide:StopAllGuide()
function GuideModule:StopAllGuide(bResetWidget)
    if not GuideConfig.FlagEnableGuide then return end

    return GuideLogic.DoStopAllGuide(bResetWidget)
end

function GuideModule:StopLoadingViewControl()
    return GuideLogic.StopLoadingViewControl()
end

function GuideModule:SkipAllGuide()
    log("SkipAllGuide")
    --if not GuideConfig.FlagEnableGuide then return end
    GuideEvtLogic.UnBindInGameEvents()
    GuideEvtLogic.UnBindLuaEventsGlobal()
    self.Field:ClearCurShowUIState()
    self:_ClearAutoPlayGuideInfo()
    self:RemoveSelfTick()
    Server.GuideServer.FlagEnableGuide = false
    GuideConfig.FlagEnableGuide = false
end

function GuideModule:IsGuiding()
    return GuideLogic.IsGuiding()
end

-- 比较重要的sol引导执行中，需要屏蔽别的系统的功能
-- 如详情页的来源用途、备战的弹窗等
function GuideModule:IsSolMainGuiding()
    if GuideLogic.IsGuiding() then
        local curGuideId = self.Field:GetCurGuideId()
        if not curGuideId then
            return false
        end
        local curStageId = self.Field:GetGuideCfg(curGuideId).GuideStageId
        local solMainGuideStages = {
            GuideConfig.EGuideStage.newPlayerGuideStage1,
            GuideConfig.EGuideStage.newPlayerGuideStage2,
            GuideConfig.EGuideStage.newPlayerGuideStage3,
        }
        if table.contains(solMainGuideStages, curStageId) then
            return true
        end
        if GuideLogic.IsSolFailGuide() then
            return true
        end
    end
    return false
end

function GuideModule:IsNewPlayerGuiding()
    return GuideLogic.IsNewPlayerGuiding()
end

-- 是否启动新手关的引导进行中
function GuideModule:IsNewPlayerMatchGuiding()
    return GuideLogic.IsNewPlayerMatchGuiding()
end


function GuideModule:IsNewPlayerMatchFinish()
    return GuideLogic.IsNewPlayerMatchFinish()
end

-- 是否在新手关中
function GuideModule:IsInNewPlayerMatch()
    local bInNewPlayerMatch = false
    local UGameplayBlueprintHelper = import "GameplayBlueprintHelper"
    local GameState = UGameplayBlueprintHelper.GetGPGameState(GetGameInstance())
    if GameState then
        bInNewPlayerMatch = not GameState.HasFinished3CGuide
    end
    loginfo("IsInNewPlayerMatch", bInNewPlayerMatch)
    return bInNewPlayerMatch
end

function GuideModule:IsNewPlayerGuideFinished()
    return GuideLogic.IsNewPlayerGuideFinished()
end

function GuideModule:GetMatchCount(game_rule)
    return GuideLogic.GetMatchCount(game_rule)
end

function GuideModule:CheckMeetGuideStageStartCondition(guideStageId)
    return GuideLogic.CheckMeetGuideStageStartCondition(guideStageId)
end

-- 检查是否需要屏蔽 拍脸相关内容 出现
function GuideModule:IsGuidingOrWaitGuide()
    return GuideLogic.IsGuidingOrWaitGuide()
end

-- finnywxu
-- 检查当前是否有强制引导，避免冲突
function GuideModule:IsForcedGuideActive()
    return GuideLogic.IsGuidingOrWaitGuide() and GuideLogic.IsForceGuide()
end

-- 涉及到引导的特殊道具
---@param guideItemConfigId number NewPlayerGuideSpecItem
function GuideModule:GetNewPlayerGuideItemInfo(guideItemConfigId)
    local itemCfg = GuideConfig.TableGuideItemConfig[guideItemConfigId]
    if not itemCfg then
        logbox("GuideItemConfig 表格配置有误，找不到对应id的道具配置")
        return
    end
    if self.Field.cacheSpecItems[guideItemConfigId] then
        return self.Field.cacheSpecItems[guideItemConfigId]
    end
    local ret = StringUtil.StringSplit(GuideConfig.TableGuideItemConfig[guideItemConfigId].ItemInfoList, ',')
    self.Field.cacheSpecItems[guideItemConfigId] = ret
    return ret
end

-- 进入3d安全屋时，如果有会覆盖在hud上的ui要加载，需要手动+1。如撤离失败弹窗，断线重连弹窗
function GuideModule:AddLoadingUINumUpSafeHouse()
    if not GuideConfig.FlagEnableGuide then return end

    self.Field.loadingUINumUpSafeHouse = self.Field.loadingUINumUpSafeHouse + 1
end

-- 弹窗关闭后，手动-1
function GuideModule:ReduceLoadingUINumUpSafeHouse()
    if not GuideConfig.FlagEnableGuide then return end

    self.Field.loadingUINumUpSafeHouse = self.Field.loadingUINumUpSafeHouse - 1
end

-- 部分ui，可以无视引导的暂停状态，强制对此ui继续进行引导。此类ui打开时计数加一
function GuideModule:AddIgnorePauseUINum()
    if not GuideConfig.FlagEnableGuide then return end

    self.Field:AddIgnorePauseUINum()
end

-- 弹窗关闭后，手动-1
function GuideModule:ReduceIgnorePauseUINum()
    if not GuideConfig.FlagEnableGuide then return end

    self.Field:ReduceIgnorePauseUINum()
end

function GuideModule:Set3CFinished()
    if not GuideConfig.FlagEnableGuide then return end

    return GuideLogic.Set3CFinished()
end

-- 需要被关注的ui打开动画播放完毕的时候，通知一下引导
function GuideModule:SetUIOpenAnimFinish(uiIns)
    GuideConfig.EGuideEvent.evtGuideUIOpenFinish:Invoke(uiIns)
end

function GuideModule:InvokeGuideEvent(guideEvent, ...)
    guideEvent:Invoke(...)
end

-- intro关是否已经完成
function GuideModule:IsIntroFinished()
    return GuideLogic.IsIntroFinished()
end

-- 设置intro关已完成
function GuideModule:SetIntroFinished(callback)
    log("SetIntroFinished")
    GuideConfig.EGuideEvent.evtOnIntroFinished:Invoke()
    return GuideLogic.SetIntroFinished(callback)
end

-- 改名是���已经完成
function GuideModule:IsRenameFinished()
    return true
end

-- 设置改名阶段已完成
function GuideModule:SetRenameFinished()
end

-- 是否当前屏蔽对指定道具的操作（出售、携带、带入局等）
function GuideModule:IsGuideItemDisableOp(itemId)
    return Server.GuideServer:IsGuideItemDisableOp(itemId)
end


----------------------
--region c++ 调用

-- 设置局内的引导状态，局内优先级高于局外触发式引导，会以独占的形式打断现有引导（如果有），执行局内引导
function GuideModule:SetGuideStateInGame(state)
    self.Field.isGuidingInGame = state
end

function GuideModule:ForceEnableGuide()
    GuideConfig.FlagEnableGuide = true
end

function GuideModule:DisplayHUDPanel(PanelNames)
    if PanelNames == nil then
        return
    end

    local hudLayerController = Facade.UIManager:GetLayerControllerByType(EUILayer.HUD)
    if hudLayerController == nil then
        return
    end

    for index, value in ipairs(PanelNames) do
        if string.find(value, "WBP_ToolbarWeapon") then
            local uiIns = hudLayerController:GetHudByName("WBP_ToolbarWeapon")
            if uiIns then
                if value == "WBP_ToolbarWeapon_1" then
                    local uiInsBtn1 = uiIns:Wnd("WBP_ControllerRouletteKill_PC", UIWidgetBase)
                    uiInsBtn1:RemoveStateFromInVisibleGameHudState(EGameHUDSate.GHS_Tutorial)
                    uiInsBtn1:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
                elseif value == "WBP_ToolbarWeapon_2" then
                    local uiInsBtn2 = uiIns:Wnd("WBP_ControllerRouletteEquip_PC", UIWidgetBase)
                    uiInsBtn2:RemoveStateFromInVisibleGameHudState(EGameHUDSate.GHS_Tutorial)
                    uiInsBtn2:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
                elseif value == "WBP_ToolbarWeapon_3" then
                    local uiInsBtn3 = uiIns:Wnd("WBP_ControllerRouletteEquip2_PC", UIWidgetBase)
                    uiInsBtn3:RemoveStateFromInVisibleGameHudState(EGameHUDSate.GHS_Tutorial)
                    uiInsBtn3:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
                elseif value == "WBP_ToolbarWeapon_Box" then
                    local uiInsBtn3 = uiIns:Wnd("CanvasPanel_Ability", UIWidgetBase)
                    uiInsBtn3:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
                else
                    uiIns:RemoveStateFromInVisibleGameHudState(EGameHUDSate.GHS_Tutorial)
                    uiIns:Show(true, true)
                end
            end
        else
            local uiIns = hudLayerController:GetHudByName(value)
            if uiIns then
                uiIns:RemoveStateFromInVisibleGameHudState(EGameHUDSate.GHS_Tutorial)
                uiIns:Show(true, true)
            end
        end
    end
end

function GuideModule:HideHUDPanel(PanelNames)
    if PanelNames == nil then
        return
    end

    local hudLayerController = Facade.UIManager:GetLayerControllerByType(EUILayer.HUD)
    if hudLayerController == nil then
        return
    end

    for index, value in ipairs(PanelNames) do
        if string.find(value, "WBP_ToolbarWeapon") then
            local uiIns = hudLayerController:GetHudByName("WBP_ToolbarWeapon")
            if uiIns then
                if value == "WBP_ToolbarWeapon_1" then
                    local uiInsBtn1 = uiIns:Wnd("WBP_ControllerRouletteKill_PC", UIWidgetBase)
                    uiInsBtn1:AddStateToInVisibleGameHudState(EGameHUDSate.GHS_Tutorial)
                    uiInsBtn1:SetVisibility(ESlateVisibility.Collapsed)
                elseif value == "WBP_ToolbarWeapon_2" then
                    local uiInsBtn2 = uiIns:Wnd("WBP_ControllerRouletteEquip_PC", UIWidgetBase)
                    uiInsBtn2:AddStateToInVisibleGameHudState(EGameHUDSate.GHS_Tutorial)
                    uiInsBtn2:SetVisibility(ESlateVisibility.Collapsed)
                elseif value == "WBP_ToolbarWeapon_3" then
                    local uiInsBtn3 = uiIns:Wnd("WBP_ControllerRouletteEquip2_PC", UIWidgetBase)
                    uiInsBtn3:AddStateToInVisibleGameHudState(EGameHUDSate.GHS_Tutorial)
                    uiInsBtn3:SetVisibility(ESlateVisibility.Collapsed)
                elseif value == "WBP_ToolbarWeapon_Box" then
                    local uiInsBtn3 = uiIns:Wnd("CanvasPanel_Ability", UIWidgetBase)
                    uiInsBtn3:SetVisibility(ESlateVisibility.Collapsed)
                else
                    uiIns:AddStateToInVisibleGameHudState(EGameHUDSate.GHS_Tutorial)
                    uiIns:Hide(true, true)
                end
            end
        else
            local uiIns = hudLayerController:GetHudByName(value)
            if uiIns then
                uiIns:AddStateToInVisibleGameHudState(EGameHUDSate.GHS_Tutorial)
                uiIns:Hide(true, true)
            end
        end
    end
end

--endregion
----------------


---------------------------------------------
--region 引导点击UI (Mobile)

-- guideClickId: GuideClickConfig中的id
function GuideModule:OpenGuideClickUI(guideClickId, openCallback, bForceClick)
    if bForceClick then
        self:OpenGuideClickUINew(guideClickId)
    else
        self:OpenGuideWeakClickUINew(guideClickId)
    end
end

function GuideModule:CloseGuideClickUI(guideClickId, bForceClick)
    if bForceClick == nil then
        self:CloseGuideClickUINew(guideClickId)
        self:CloseGuideWeakClickUINew(guideClickId)
    else
        if bForceClick then
            self:CloseGuideClickUINew(guideClickId)
        else
            self:CloseGuideWeakClickUINew(guideClickId)
        end
    end

    -- log("CloseGuideClickUI", Module.Guide.Field.guideClickDataInGame)
    -- local data = Module.Guide.Field.guideClickDataInGame
    -- if data then
    --     data:Destroy()
    --     Module.Guide.Field.guideClickDataInGame = nil
    -- end
end

function GuideModule:OpenGuideClickUINew(guideClickId)
    self:CloseGuideClickUINew(guideClickId)
    log("OpenGuideClickUINew", guideClickId)
    if not guideClickId then return end
    local guideClickDataCfg = GuideConfig.TableGuideClickConfig[guideClickId]
    if not guideClickDataCfg then
        log("OpenGuideClickUINew, guideClickDataCfg is nil", guideClickId)
        return
    end

    ---@class GuideClickUIShowData
    ---@field clipDataList GuideClickUIShowData_ClipData[]
    ---@field tipsPosType number --- 10-13 左上右下
    local showData = {}
    showData.tipsText = GuideLogic.GetRealClickTipsText(guideClickDataCfg)
    showData.tipsAudio = GuideLogic.GetRealClickTipsAudio(guideClickDataCfg)
    showData.tipsPosType = guideClickDataCfg.TipsPosType
    showData.exLoopAnimType = guideClickDataCfg.ExLoopAnimType
    showData.bNotRealClick = guideClickDataCfg.IsNotRealClick
    showData.clipDataList = {}


    for idx, uiWidgetId in ipairs(guideClickDataCfg.UIWidgetId) do
        local widgetConfig = GuideConfig.TableGuideWidgetConfig[uiWidgetId]
        log( "OpenGuideClickUINew".. guideClickId .. string.format("UIWidgetId: %s, BpPath: %s, WidgetName: %s, UIName: %s, IsHUD: %s", uiWidgetId, widgetConfig.BpPath, widgetConfig.WidgetName, widgetConfig.UIName, widgetConfig.IsHudUI ))
        local targetWidget, targetUI, targetWidgetList = GuideLogic.GetWidgetByPath(widgetConfig.BpPath, widgetConfig.WidgetName, widgetConfig.UIName, widgetConfig.IsHudUI)


        if targetWidget or targetWidgetList then

            local path = GuideConfig.EMaskTexturePath[guideClickDataCfg.MaskImageType[idx]] 
            if not path then
                logerror("OpenGuideClickUINew, guideClickDataCfg.MaskImageType is nil", guideClickId, idx)
                path = GuideConfig.DefaultMaskTexturePath
            end

            local setClipData = function(targetWidget)
                ---@class GuideClickUIShowData_ClipData
                local clipData = {
                    targetWidget = targetWidget, -- 传widget，方便做分辨率切换适配
                    maskImg =  path,
                    bGoNextOnClickAny = guideClickDataCfg.IsGoNextOnClickAny,
                    uiOffset = guideClickDataCfg.UIOffset,
                    triangleUIOffset = guideClickDataCfg.TriangleUIOffset,
                    triangleScale = guideClickDataCfg.TriangleScale,
                    uiType = guideClickDataCfg.UIType[idx] ~= 0 and guideClickDataCfg.UIType[idx] or guideClickDataCfg.MaskImageType[idx]
                }
                table.insert(showData.clipDataList, clipData)
            end
            if targetWidgetList then
                for _, targetWidget in pairs(targetWidgetList) do
                    setClipData(targetWidget)
                end
            else
                setClipData(targetWidget)
            end
        else
            logbox("this step guide target widget is nil,", guideClickId, widgetConfig.BpPath, widgetConfig.WidgetName, widgetConfig.UIName)
        end
    end

    GuideLogic.CommonOpenCacheGuideUI(UIName2ID.GuideClickUI, guideClickId, showData)
end

function GuideModule:CloseGuideClickUINew(guideClickId)
    log("CloseGuideClickUINew", guideClickId)
    GuideLogic.CommonCloseCacheGuideUI(UIName2ID.GuideClickUI, guideClickId)
end

function GuideModule:OpenGuideWeakClickUINew(guideClickId)
    self:CloseGuideWeakClickUINew(guideClickId)
    log("OpenGuideWeakClickUINew", guideClickId)
    if not guideClickId then return end
    local guideClickDataCfg = GuideConfig.TableGuideClickConfig[guideClickId]
    if not guideClickDataCfg then
        log("OpenGuideWeakClickUINew, guideClickDataCfg is nil", guideClickId)
        return
    end

    ---@class GuideWeakClickUI_ShowData
    local showData = {}
    showData.tipsText = GuideLogic.GetRealClickTipsText(guideClickDataCfg)
    showData.tipsAudio = GuideLogic.GetRealClickTipsAudio(guideClickDataCfg)
    showData.tipsPosType = guideClickDataCfg.TipsPosType
    log("OpenGuideWeakClickUINew tipsPosType", showData.tipsPosType)
    showData.exLoopAnimType = guideClickDataCfg.ExLoopAnimType
    showData.clipDataList = {}
    for idx, uiWidgetId in ipairs(guideClickDataCfg.UIWidgetId) do
        local widgetConfig = GuideConfig.TableGuideWidgetConfig[uiWidgetId]
        local targetWidget, targetUI, targetWidgetList = GuideLogic.GetWidgetByPath(
            widgetConfig.BpPath, widgetConfig.WidgetName, widgetConfig.UIName, widgetConfig.IsHudUI)

        if targetWidget or targetWidgetList then
            local setClipData = function(targetWidget)
                local clipData = {
                    targetWidget = targetWidget, -- 传widget，方便做分辨率切换适配
                    maskImg = GuideConfig.EMaskTexturePath[guideClickDataCfg.MaskImageType[idx]] or GuideConfig.DefaultMaskTexturePath,
                    bGoNextOnClickAny = guideClickDataCfg.IsGoNextOnClickAny,
                    uiOffset = guideClickDataCfg.UIOffset,
                    triangleUIOffset = guideClickDataCfg.TriangleUIOffset,
                    triangleScale = guideClickDataCfg.TriangleScale,
                    uiType = guideClickDataCfg.UIType[idx] ~= 0 and guideClickDataCfg.UIType[idx] or guideClickDataCfg.MaskImageType[idx]
                }
                table.insert(showData.clipDataList, clipData)
            end
            if targetWidgetList then
                for _, targetWidget in pairs(targetWidgetList) do
                    setClipData(targetWidget)
                end
            else
                setClipData(targetWidget)
            end
        else
            logerror("this step guide target widget is nil,", guideClickId, widgetConfig.BpPath, widgetConfig.WidgetName, widgetConfig.UIName)
        end
    end
    if guideClickDataCfg.UIWidgetIdForCalSizeAndPos > 0 then
        local widgetConfig = GuideConfig.TableGuideWidgetConfig[guideClickDataCfg.UIWidgetIdForCalSizeAndPos]
        local targetWidget, targetUI, targetWidgetList = GuideLogic.GetWidgetByPath(
            widgetConfig.BpPath, widgetConfig.WidgetName, widgetConfig.UIName, widgetConfig.IsHudUI)
        if targetWidget then
            local clipData = {
                targetWidget = targetWidget, -- 传widget，方便做分辨率切换适配
                uiOffset = guideClickDataCfg.UIOffset,
                triangleUIOffset = guideClickDataCfg.TriangleUIOffset,
                triangleScale = guideClickDataCfg.TriangleScale,
                uiType = guideClickDataCfg.UIType[1] ~= 0 and guideClickDataCfg.UIType[1] or guideClickDataCfg.MaskImageType[1]
            }
            showData.uiForCalSizeAndPosData = clipData
        else
            logerror("this step widget ForCalSizeAndPos is nil,", guideClickDataCfg.UIWidgetIdForCalSizeAndPos, widgetConfig.BpPath,
                widgetConfig.WidgetName, widgetConfig.UIName)
        end
    end

    GuideLogic.CommonOpenCacheGuideUI(UIName2ID.GuideWeakClickBigUI, guideClickId, showData)
end

function GuideModule:CloseGuideWeakClickUINew(guideClickId)
    -- log("CloseGuideWeakClickUINew", guideClickId)
    GuideLogic.CommonCloseCacheGuideUI(UIName2ID.GuideWeakClickBigUI, guideClickId)
end

--endregion
------------------------------------

-- guideDialogId: GuideDialogConfig中的id
-- endPlayCallback: FOnGPAudioFinishedBluprint
function GuideModule:OpenGuideDialogUI(guideDialogId, bEndCallback, uiOffsetY)
    self:CloseGuideDialogUI()
    log("OpenGuideDialogUI", guideDialogId, uiOffsetY)
    uiOffsetY = setdefault(uiOffsetY, 0)
    local guideDialogDataCfg = GuideConfig.TableGuideDialogConfig[guideDialogId]
    local dialogData = {}
    local npcNameList = {}
    for _, text in ipairs(guideDialogDataCfg.Dialog) do
        if guideDialogDataCfg.NPCName and guideDialogDataCfg.NPCName ~= "" then
            table.insert(npcNameList, string.format("<customstyle color=\"Color_Highlight02\">%s：</>", guideDialogDataCfg.NPCName))
        else
            table.insert(npcNameList, string.format(GuideConfig.Loc.guideDialogNpcName, ""))
        end
    end
    dialogData.npcNameList = npcNameList
    dialogData.textList = guideDialogDataCfg.Dialog
    dialogData.audioList = guideDialogDataCfg.NpcSound
    dialogData.bEndCallback = bEndCallback
    dialogData.bHideBg = true
    if guideDialogDataCfg.UIOffset then
        dialogData.uiOffset = {
            guideDialogDataCfg.UIOffset[1],
            (guideDialogDataCfg.UIOffset[2] or 0) + uiOffsetY,
        }
    end
    --BEGIN MODIFICATION @ VIRTUOS : 引导GamePad扩展
    if IsHD() then
        dialogData.textListGamepad = guideDialogDataCfg.DialogForGamePad
        dialogData.audioListGamepad = guideDialogDataCfg.NpcSoundForGamePad
    end
    -- END MODIFICATION

    --BEGIN MODIFICATION @ VIRTUOS : 引导Console扩展
    if IsHD() then
        dialogData.textListConsole = guideDialogDataCfg.DialogForConsole
        dialogData.audioListConsole = guideDialogDataCfg.NpcSoundForConsole
    end
    -- END MODIFICATION

    local handle = Facade.UIManager:AsyncShowUI(UIName2ID.GuideDialogUI, nil, nil, dialogData)
    Module.Guide.Field.guideDialogHandleInGame = handle
end

function GuideModule:CloseGuideDialogUI()
    log("CloseGuideDialogUI")
    local handle = Module.Guide.Field.guideDialogHandleInGame
    Facade.UIManager:CloseUIByHandle(handle)
    Module.Guide.Field.guideDialogHandleInGame = nil
end

-- guidePopWindowId: GuidePopWindowConfig中的id
-- autoNextTime: 自动关闭ui的时间
function GuideModule:OpenGuideNounExplainUI(guidePopWindowId, autoNextTime)
    autoNextTime = setdefault(autoNextTime, 5)
    self:CloseGuideNounExplainUI()
    log("OpenGuideNounExplainUI", guidePopWindowId, autoNextTime)
    local guidePopWindowConfig = GuideConfig.TableGuidePopWindowConfig[guidePopWindowId]

    local popWindowData = {}
    local descInfoList = GuideLogic.GetRealPopWindowDesc(guidePopWindowConfig)
    popWindowData.title = descInfoList[1].Title
    popWindowData.descText = descInfoList[1].DescText
    popWindowData.image = descInfoList[1].Image
    popWindowData.exContentType = descInfoList[1].ExContentType
    popWindowData.exContentParams = descInfoList[1].ExContentParams
    popWindowData.AutoNextGuideTime = autoNextTime
    popWindowData.endCallback = function()
        self:CloseGuideNounExplainUI()
    end

    local handle = Facade.UIManager:AsyncShowUI(UIName2ID.GuideLittlePopWindowUI, nil, nil, popWindowData)
    Module.Guide.Field.guideNounExplainHandleInGame = handle
end

function GuideModule:CloseGuideNounExplainUI()
    log("CloseGuideDialogUI")

    local handle = Module.Guide.Field.guideNounExplainHandleInGame
    if handle then
        Facade.UIManager:CloseUIByHandle(handle)
        Module.Guide.Field.guideNounExplainHandleInGame = nil
    end
end

-- 3C引导 Win包提示框
function GuideModule:Show3CGuideDialogue(guide3CStage)
    loginfo("[georgecgong11] Show3CGuideDialogue(), guide3CStage: " .. guide3CStage)
    if Module.Guide.Field.guideDialogue3CUiIns then
        Module.Guide.Field.guideDialogue3CUiIns:Show()
    else
        if guide3CStage == nil or guide3CStage < 0 then
            return
        end
        local dialogData = {}
        dialogData.textList = { [1] = GuideConfig.Guide3CDialogueText[guide3CStage] }
        dialogData.endCallback = function()
            -- do nothing
        end
        dialogData.bHideBg = true
        local onLoadFinish = function(uiIns)
            Module.Guide.Field.guideDialogue3CUiIns = uiIns
            uiIns._wtDialog:SetVisibility(ESlateVisibility.Collapsed)
            uiIns._wt3cStageContents[guide3CStage]:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        end
        Module.Guide.Field.guideDialogue3CUiHandle = Facade.UIManager:AsyncShowUI(UIName2ID.GuideDialogUI, onLoadFinish, nil, dialogData)
    end
end

function GuideModule:Hide3CGuideDialogue()
    loginfo("[georgecgong11] Hide3CGuideDialogue()")
    if Module.Guide.Field.guideDialogue3CUiIns then
        Module.Guide.Field.guideDialogue3CUiIns:Hide()
    end
end

function GuideModule:Close3CGuideDialogue()
    loginfo("[georgecgong11] Close3CGuideDialogue()")
    Facade.UIManager:CloseUI(Module.Guide.Field.guideDialogue3CUiIns)
    Module.Guide.Field.guideDialogue3CUiIns = nil
end

-- 设置3C引导结束
function GuideModule:Set3CFinishedState(state)
    if not GuideConfig.FlagEnableGuide then return end

    return GuideLogic.Set3CFinishedState(state)
end

function GuideModule:Is3CFinished()
    if not GuideConfig.FlagEnableGuide then return end

    return GuideLogic.Is3CFinished()
end

-- 将新手关loot引导播放次数置为0，让引导可以重播
function GuideModule:ResetNewPlayerMatchGuideVariables()
    if not GuideConfig.FlagEnableGuide then return end

    local needResetList = {
        GuideConfig.EGuideStage.lootingDeadBody,
        GuideConfig.EGuideStage.lootingGuide,
        GuideConfig.EGuideStage.solEscapeMark,
        GuideConfig.EGuideStage.solNewPlayerLevel_FreeSafeBoxLooting,
    }
    local finishMap = Server.TipsRecordServer:GetMap(Server.TipsRecordServer.keys.GuideStageFinished)
    for _, stageId in ipairs(needResetList) do
        finishMap[tostring(stageId)] = 0
    end
    Server.TipsRecordServer:SetMap(Server.TipsRecordServer.keys.GuideStageFinished, finishMap)
    Server.TipsRecordServer:SetNumber(Server.TipsRecordServer.keys.GuideIntroFirstDeadBody, 0)
end

function GuideModule:SendClientCommonEventLog(eventId)
    if not GuideConfig.FlagEnableGuide then return end

    -- 针对局内大战场埋点异常上报，做一个兜底
    local idStart = eventId // 100000
    if idStart == 142 or idStart == 242 or idStart == 141 or idStart == 142 then
        if table.contains(self.Field.sendOnceSpecLogIds, eventId) then
            logwarning("SendClientCommonEventLog same id start with:", idStart, eventId)
            return
        end
        table.insert(self.Field.sendOnceSpecLogIds, eventId)
    end

    LogAnalysisTool.DoSendClientCommonEventLog(eventId)
end

function GuideModule:SetCustomRooleteeMedItem(itemId, bForceUseCustom)
    if not GuideConfig.FlagEnableGuide then return end

    GuideLogic.SetCustomRooleteeMedItem(itemId, bForceUseCustom)
end

function GuideModule:CommonSetGuideUIValidState(uiId, cfgId, bValid)
    GuideLogic.CommonSetGuideUIValidState(uiId, cfgId, bValid)
end


-------------------------------
--region PopWindow 

-- guidePopWindowId: GuidePopWindowConfig中的id
-- pc和手游共用，自动切换样式
function GuideModule:OpenGuidePopWindowUI(guidePopWindowId, callback)
    self:CloseGuidePopWindowUI(guidePopWindowId)
    log("OpenGuidePopWindowUI", guidePopWindowId)
    local showData = GuideLogic.GetGuidePopWindowShowData(guidePopWindowId)

    ---@param endReason GuideConfig.EPopWindowUICloseReason 由(HD)PopWindow关闭时传出
    showData.callbackOnEnd = function(endReason)
        self:CloseGuidePopWindowUI(guidePopWindowId)
        if callback then
            callback(endReason)
        end
        -- 上报一下埋点
        if GuideConfig.EPopWindowCloseLogId[guidePopWindowId] then
            self:SendGuideSpecLog(GuideConfig.EPopWindowCloseLogId[guidePopWindowId])
        end
    end

    local uiId = IsHD() and UIName2ID.GuideHDPopWindowUI or UIName2ID.GuidePopWindowUI
    GuideLogic.CommonOpenCacheGuideUI(uiId, guidePopWindowId, showData)
end


function GuideModule:CloseGuidePopWindowUI(guidePopWindowId)
    log("CloseGuidePopWindowUI", guidePopWindowId)
    local uiId = IsHD() and UIName2ID.GuideHDPopWindowUI or UIName2ID.GuidePopWindowUI
    local ret = GuideLogic.CommonCloseCacheGuideUI(uiId, guidePopWindowId)
    if guidePopWindowId and ret then
        log("CloseGuidePopWindowUI success", guidePopWindowId)
        local guideMgrIns = UDFMGuideManager.Get(GetWorld())
        guideMgrIns:NtfGuidePopWindowClose(guidePopWindowId)

        if guidePopWindowId == GuideConfig.TutorialLevelFirstBattlePopWindowId then
            Module.GCloudSDK:OnNewPlayerGuideStartReport()
        end
    end
end

-- for tutorial level quest blueprint
function GuideModule:OpenGuideHDPopWindowUI(guidePopWindowId)
    GuideModule:OpenGuidePopWindowUI(guidePopWindowId, nil)
end
function GuideModule:CloseGuideHDPopWindowUI(guidePopWindowId)
    GuideModule:CloseGuidePopWindowUI(guidePopWindowId)
end

--endregion
-----------------------


-----------------
--region HDPopButtonUI

-- guidePopButtonWord: 需要显示的字符
-- pc生效，移动端调用无反应
function GuideModule:OpenGuideHDPopButtonUI(guidePopButtonWord)
    if not IsHD() then return end
    self:CloseGuideHDPopButtonUI()
    log("OpenGuideHDPopButtonUI", guidePopButtonWord)
    local showData = {}
    showData.word = guidePopButtonWord
    GuideLogic.CommonOpenCacheGuideUI(UIName2ID.GuideHDPopButtonUI, 0, showData)
end

function GuideModule:ChangeGuideHDPopButtonStrengh(bStrong)
    log("ChangeGuideHDPopButtonStrengh", bStrong)
    if not IsHD() then return end
    local uiDict = Module.Guide.Field.curUseUIDict[UIName2ID.GuideHDPopButtonUI]
    if not uiDict then return end

    if uiDict then
        local uiIns = uiDict[0]
        if uiIns then
            uiIns:ChangeStrengh(bStrong)
        end
    end
end

function GuideModule:CloseGuideHDPopButtonUI()
    log("CloseGuideHDPopButtonUI")
    if not IsHD() then return end
    GuideLogic.CommonCloseCacheGuideUI(UIName2ID.GuideHDPopButtonUI)
end

--endregion
-------------------------

-----------------
--region HDPopTipsUI

-- pc生效，移动端调用无反应
--- PopWindowConfig for media tips play
---@param guidePopWindowId   number GuidePopWindowConfig中的id
---@param bNotShowCompleteUI boolean
---@param callbackOnTipAutoTimeEndWithClose function 走NexGuideId[#NexGuideId]
function GuideModule:OpenGuideHDPopTipsUI(guidePopWindowId, bNotShowCompleteUI , callbackOnTipAutoTimeEndWithClose)
    -- self:OpenGuideHDPopFunctionDescUI(guidePopWindowId)

    if  not IsHD() then
        return
    end
    self:CloseGuideHDPopTipsUI(guidePopWindowId)
    log("OpenGuideHDPopTipsUI", guidePopWindowId)

    local guidePopWindowConfig = GuideConfig.TableGuidePopWindowConfig[guidePopWindowId]

    local descInfoList = {}
    local cfgDescInfoList = GuideLogic.GetRealPopWindowDesc(guidePopWindowConfig)
    for _, cfgDescInfo in ipairs(cfgDescInfoList) do
        ---@class GuideHDPopTipsUIShowData_DescInfo
        local descInfo = {}
        descInfo.title = cfgDescInfo.Title
        descInfo.descText = cfgDescInfo.DescText
        descInfo.exContentType = cfgDescInfo.ExContentType
        descInfo.exContentParams = cfgDescInfo.ExContentParams
        descInfo.Image = cfgDescInfo.Image
        descInfo.completeText = cfgDescInfo.CompleteConditionText
        if cfgDescInfo.CompleteConditionTextAndKey.ActionName ~= "None" and cfgDescInfo.CompleteConditionTextAndKey.ActionName ~= "" then
            -- 这里拿住c++表结构的话，表释放后访问会崩溃。转换成lua对象
            -- descInfo.completeTextAndKey = cfgDescInfo.CompleteConditionTextAndKey
            descInfo.completeTextAndKey = {
                ActionName = cfgDescInfo.CompleteConditionTextAndKey.ActionName,
                LeftText = cfgDescInfo.CompleteConditionTextAndKey.LeftText,
            }
        end
        table.insert(descInfoList, descInfo)
    end

    ---@class GuideHDPopTipsUIShowData
    ---@field autoTimeEndMethod number 0什么都不做 1 关闭
    ---@field callbackOnEnd function --2024/11/22 关闭PopTips  xx:CloseGuideHDPopTipsUI(guidePopWindowId) 调用NexGuideId[#NexGuideId]
    ---@field descInfo GuideHDPopTipsUIShowData_DescInfo[]
    local showData = {}
    showData.title = cfgDescInfoList[1].Title
    showData.descInfo = descInfoList
    showData.bNotShowCompleteUI = bNotShowCompleteUI
    showData.useMediaIndex = guidePopWindowId % 2
    showData.showType = guidePopWindowConfig.ShowType
    showData.autoTime = guidePopWindowConfig.AutoTime
    showData.autoTimeEndMethod = guidePopWindowConfig.AutoTimeEndMethod
    showData.callbackOnEnd = function ()
        if callbackOnTipAutoTimeEndWithClose then
            callbackOnTipAutoTimeEndWithClose()
        end
        self:CloseGuideHDPopTipsUI(guidePopWindowId)
    end
    showData.audio = GuideLogic.GetRealPopWindowAudio(guidePopWindowConfig)

    GuideLogic.CommonOpenCacheGuideUI(UIName2ID.GuideHDPopTipsUI, guidePopWindowId, showData)
end

function GuideModule:CloseGuideHDPopTipsUI(guidePopWindowId)
    -- self:CloseGuideHDPopFunctionDescUI(guidePopWindowId)
    if not IsHD() then return end

    log("CloseGuideHDPopTipsUI", guidePopWindowId)
    GuideLogic.CommonCloseCacheGuideUI(UIName2ID.GuideHDPopTipsUI, guidePopWindowId)
end

--endregion
----------------------


-- called by cpp tutorial now 2024/11/17
-- to end a hd pop tips with a animation
function GuideModule:SetGuideHDPopTipsUIComplete(guidePopWindowId, questIdx, callback)
    log("SetGuideHDPopTipsUIComplete", guidePopWindowId, questIdx)
    ---@type GuideHDPopTipsUI[]
    local uiDict = Module.Guide.Field.curUseUIDict[UIName2ID.GuideHDPopTipsUI]
    if not uiDict then return end
    if guidePopWindowId and guidePopWindowId > 0 then
        local uiIns = uiDict[guidePopWindowId]
        if uiIns then
            local OnCompletePlayed = function()
                self:CloseGuideHDPopTipsUI(guidePopWindowId)
                if callback then
                    callback()
                end
            end
            uiIns:PlayComplete(OnCompletePlayed)
        end
    end
end


---------------------------
--region HDPopFunctionDescUI



function GuideModule:OpenGuideHDPopFunctionDescUI(guidePopFunctionDescId, cbOnEnd)
    self:CloseGuideHDPopFunctionDescUI(guidePopFunctionDescId)
    log("OpenGuideHDPopFunctionDescUI", guidePopFunctionDescId)
    local guidePopFunctionDescConfig = GuideConfig.TableGuidePopFunctionDescConfig[guidePopFunctionDescId]
    if not guidePopFunctionDescConfig then
        logwarning("OpenGuideHDPopFunctionDescUI id invalid", guidePopFunctionDescId)
        return
    end

    ---@class GuideHDPopFunctionDescUI_ShowData
    local showData = {}
    showData.title = guidePopFunctionDescConfig.Title
    showData.bUseBtnGotoSummary = guidePopFunctionDescConfig.bUseBtnGotoSummary
    showData.position = guidePopFunctionDescConfig.Position or 0 -- 0左边 1右边
    showData.bUseBtnSkipGuide = guidePopFunctionDescConfig.bUseBtnSkipGuide
    showData.bNotMask = guidePopFunctionDescConfig.bNotMask
    showData.showType = guidePopFunctionDescConfig.ShowType  -- 0 有按钮 1 有进度条  2 仅手柄有进度条(She3再改)
    showData.autoTime = guidePopFunctionDescConfig.AutoTime
    showData.autoTimeEndMethod = guidePopFunctionDescConfig.AutoTimeEndMethod -- 0什么都不做 1 关闭
    showData.tipsAudio = guidePopFunctionDescConfig.Audio

    showData.descInfo = guidePopFunctionDescConfig.DescInfo
    --BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        showData.DescInfoForHDWithGamepad = guidePopFunctionDescConfig.DescInfoForHDWithGamepad
        showData.AudioForHDWithGamepad = guidePopFunctionDescConfig.AudioForHDWithGamepad
    end
    --END

    showData.callbackOnEnd = function()
        self:CloseGuideHDPopFunctionDescUI(guidePopFunctionDescId)
        if cbOnEnd then
            cbOnEnd()
        end
        local UGuideUtils = import "GuideUtils"
        UGuideUtils.BoardcastOnPopFunctionClosed(GetWorld(), guidePopFunctionDescId)
    end

    GuideLogic.CommonOpenCacheGuideUI(UIName2ID.GuideHDCommonDescUI, guidePopFunctionDescId, showData)
end

function GuideModule:CloseGuideHDPopFunctionDescUI(guidePopFunctionDescId)
    log("CloseGuideHDPopFunctionDescUI", guidePopFunctionDescId)
    GuideLogic.CommonCloseCacheGuideUI(UIName2ID.GuideHDCommonDescUI, guidePopFunctionDescId)
end

--#endregion
---------------------------

----------------------
--region HDClick & WeakClick

---@param guideClickId any
---@param guidePopFunctionDescId number optional for guideHDPopDescfAnd*Click
function GuideModule:OpenGuideHDClickUI(guideClickId, guidePopFunctionDescId)
    self:CloseGuideHDClickUI(guideClickId)
    log("OpenGuideHDClickUI", guideClickId)
    local guideClickDataCfg = GuideConfig.TableGuideClickConfig[guideClickId]
    local StackBgCfgTable = Facade.TableManager:GetTable("LuaAsset/StackBgAssetConfig", false)

    ---@class GuideHDClickUIShowData
    ---@field clipDataList GuideHDClickUIShowData_ClipData[]
    ---@field clipDataListOnlyDisplay GuideHDClickUIShowData_ClipData[]
    local showData = {}
    showData.tipsText = GuideLogic.GetRealClickTipsText(guideClickDataCfg)
    -- showData.tipsTextPolicy = guideClickDataCfg.TipsTextPolicy --  (0 默认) (1 TipsTextForHD 不填-> 不用 ) (11 PC 不会使用TipsText 作为 fallback, 同时 隐藏掉 Icon 2025/2/26)
    showData.options = guideClickDataCfg.Options  
    showData.tipsAudio = GuideLogic.GetRealClickTipsAudio(guideClickDataCfg)
    showData.exLoopAnimType = guideClickDataCfg.ExLoopAnimType
    showData.clipDataList = {}
    showData.clickID = guideClickId

    for idx, uiWidgetId in ipairs(guideClickDataCfg.UIWidgetId) do
        local widgetConfig = GuideConfig.TableGuideWidgetConfig[uiWidgetId]
        if not widgetConfig then
            logerror("OpenGuideHDClickUI no widgetConfig", uiWidgetId, #GuideConfig.TableGuideWidgetConfig)
            return
        end
        local targetWidget, targetUI, targetWidgetList = GuideLogic.GetWidgetByPath(
            widgetConfig.BpPath, widgetConfig.WidgetName, widgetConfig.UIName, widgetConfig.IsHudUI)

        if targetWidget or targetWidgetList then
            local setClipData = function(targetWidget)
                ---@class GuideHDClickUIShowData_ClipData
                local clipData = {
                    targetWidget = targetWidget, -- 传widget，方便做分辨率切换适配
                    maskImg = GuideConfig.EMaskTexturePath[guideClickDataCfg.MaskImageType[idx]] or GuideConfig.DefaultMaskTexturePath,
                    uiOffset = guideClickDataCfg.UIOffset,
                    triangleUIOffset = guideClickDataCfg.TriangleUIOffset,
                    triangleScale = guideClickDataCfg.TriangleScale,
                    uiType = guideClickDataCfg.UIType[idx] ~= 0 and guideClickDataCfg.UIType[idx] or guideClickDataCfg.MaskImageType[idx]
                }
                table.insert(showData.clipDataList, clipData)
            end
            if targetWidgetList then
                for _, targetWidget in pairs(targetWidgetList) do
                    setClipData(targetWidget)
                end
            else
                setClipData(targetWidget)
            end

            if targetUI and targetUI.UISettings and targetUI.UISettings.UILayer == EUILayer.Stack then
                local bpKey = targetUI.UISettings.BPKey
                if bpKey and StackBgCfgTable and StackBgCfgTable[bpKey] then
                    local curBgInfoRow = StackBgCfgTable[bpKey]
                    if curBgInfoRow.BackgroundType == EBackgroundType.Image and curBgInfoRow.DisplayAssetPath then
                        showData.maskBg = curBgInfoRow.DisplayAssetPath
                    end
                end
            end
        else
            logbox("this step guide target widget is nil,", guideClickId, widgetConfig.BpPath, widgetConfig.WidgetName, widgetConfig.UIName)
        end
    end

    showData.clipDataListOnlyDisplay = {}
    local offset = #showData.clipDataList
    for idxDisplay, uiWidgetId in ipairs(guideClickDataCfg.UIWidgetIdOnlyDisplay) do
        local idx = idxDisplay + offset
        local widgetConfig = GuideConfig.TableGuideWidgetConfig[uiWidgetId]
        local targetWidget, targetUI, targetWidgetList = GuideLogic.GetWidgetByPath(widgetConfig.BpPath, widgetConfig.WidgetName, widgetConfig.UIName, widgetConfig.IsHudUI)

        if targetWidget or targetWidgetList then
            local setClipData = function (targetWidget)
                local clipData = {
                    targetWidget = targetWidget,    -- 传widget，方便做分辨率切换适配
                    maskImg = GuideConfig.EMaskTexturePath[guideClickDataCfg.MaskImageType[idx]] or GuideConfig.DefaultMaskTexturePath,
                    uiOffset = guideClickDataCfg.UIOffset,
                    triangleUIOffset = guideClickDataCfg.TriangleUIOffset,
                    triangleScale = guideClickDataCfg.TriangleScale,
                    uiType = guideClickDataCfg.UIType[idx] ~= 0 and guideClickDataCfg.UIType[idx] or guideClickDataCfg.MaskImageType[idx]
                }
                table.insert(showData.clipDataListOnlyDisplay, clipData)
            end
            if targetWidgetList then
                for _, targetWidget in pairs(targetWidgetList) do
                    setClipData(targetWidget)
                end
            else
                setClipData(targetWidget)
            end
        else
            logbox("this step guide target widget is nil,", guideClickId, widgetConfig.BpPath, widgetConfig.WidgetName, widgetConfig.UIName)
        end
    end

    if guideClickDataCfg.MaskBgImage then
        local descImage = FLuaHelper.SoftObjectPtrToString(guideClickDataCfg.MaskBgImage)
        showData.maskBg = descImage
    end
    if showData.maskBg and showData.maskBg ~= "" then
        showData.bNotMaskTopBar = guideClickDataCfg.IsNotMaskTopBar
    end

    showData.callbackOnEnd = function ()
        self:CloseGuideHDPopFunctionDescUI(guidePopFunctionDescId)
    end

    GuideLogic.CommonOpenCacheGuideUI(UIName2ID.GuideHDClickUI, guideClickId, showData)
end

function GuideModule:CloseGuideHDClickUI(guideClickId)
    log("CloseGuideHDClickUI", guideClickId)
    GuideLogic.CommonCloseCacheGuideUI(UIName2ID.GuideHDClickUI, guideClickId)
end

function GuideModule:OpenGuideHDWeakClickUI(guideClickId, guidePopFunctionDescId)
    self:CloseGuideHDWeakClickUI(guideClickId)
    log("OpenGuideHDWeakClickUI", guideClickId)
    local guideClickDataCfg = GuideConfig.TableGuideClickConfig[guideClickId]
    -- local StackBgCfgTable = Facade.TableManager:GetTable("LuaAsset/StackBgAssetConfig", false)

    ---@class GuideHDWeakClickUI_ShowData
    ---@field clipDataList GuideHDWeakClickUIShowData_ClipData[]
    ---@field clipDataListOnlyDisplay GuideHDWeakClickUIShowData_ClipData[]
    local showData = {}
    showData.clipDataList = {}
    showData.tipsAudio = GuideLogic.GetRealClickTipsAudio(guideClickDataCfg)
    showData.tipsText = GuideLogic.GetRealClickTipsText(guideClickDataCfg)
    -- showData.tipsTextPolicy = guideClickDataCfg.TipsTextPolicy
    showData.options = guideClickDataCfg.Options
    showData.clickID = guideClickId

    for idx, uiWidgetId in ipairs(guideClickDataCfg.UIWidgetId) do
        local widgetConfig = GuideConfig.TableGuideWidgetConfig[uiWidgetId]
        local targetWidget, targetUI, targetWidgetList = GuideLogic.GetWidgetByPath(widgetConfig.BpPath, widgetConfig.WidgetName, widgetConfig.UIName, widgetConfig.IsHudUI)

        if targetWidget or targetWidgetList then
            local setClipData = function (targetWidget)
                ---@class GuideHDWeakClickUIShowData_ClipData
                local clipData = {
                    targetWidget = targetWidget,    -- 传widget，方便做分辨率切换适配
                    uiOffset = guideClickDataCfg.UIOffset,
                    triangleUIOffset = guideClickDataCfg.TriangleUIOffset,
                    triangleScale = guideClickDataCfg.TriangleScale,
                    uiType = guideClickDataCfg.UIType[idx] ~= 0 and guideClickDataCfg.UIType[idx] or guideClickDataCfg.MaskImageType[idx]
                }
                table.insert(showData.clipDataList, clipData)
            end

            if targetWidgetList then
                for _, targetWidget in pairs(targetWidgetList) do
                    setClipData(targetWidget)
                end
            else
                setClipData(targetWidget)
            end
        else
            logwarning("this step guide target widget is nil,", guideClickId, widgetConfig.BpPath, widgetConfig.WidgetName, widgetConfig.UIName)
        end
    end

    if #showData.clipDataList == 0 then
        logwarning("showData.clipDataList num is 0", guideClickId)
        return
    end

    -- use another widget to clip for click
    if guideClickDataCfg.UIWidgetIdForCalSizeAndPos > 0 then
        local widgetConfig = GuideConfig.TableGuideWidgetConfig[guideClickDataCfg.UIWidgetIdForCalSizeAndPos]
        local targetWidget, targetUI, targetWidgetList = GuideLogic.GetWidgetByPath(
            widgetConfig.BpPath, widgetConfig.WidgetName, widgetConfig.UIName, widgetConfig.IsHudUI)
        if targetWidget then
            ---@type GuideHDWeakClickUIShowData_ClipData
            local clipData = {
                targetWidget = targetWidget,    -- 传widget，方便做分辨率切换适配
                uiOffset = guideClickDataCfg.UIOffset,
                triangleUIOffset = guideClickDataCfg.TriangleUIOffset,
                triangleScale = guideClickDataCfg.TriangleScale,
                uiType = guideClickDataCfg.UIType[1] ~= 0 and guideClickDataCfg.UIType[1] or guideClickDataCfg.MaskImageType[1]
            }
            showData.uiForCalSizeAndPosData = clipData
        else
            logerror("this step widget ForCalSizeAndPos is nil,", guideClickDataCfg.UIWidgetIdForCalSizeAndPos, widgetConfig.BpPath, widgetConfig.WidgetName, widgetConfig.UIName)
        end
    end

    showData.callbackOnEnd = function ()
        self:CloseGuideHDPopFunctionDescUI(guidePopFunctionDescId)
    end

    GuideLogic.CommonOpenCacheGuideUI(UIName2ID.GuideHDWeakClickUI, guideClickId, showData)
end

function GuideModule:CloseGuideHDWeakClickUI(guideClickId)
    log("CloseGuideHDWeakClickUI", guideClickId)
    GuideLogic.CommonCloseCacheGuideUI(UIName2ID.GuideHDWeakClickUI, guideClickId)
end

--endregion
------------

-------------
--region HDPopTipsUI & WEAK Click

---@param guidePopWindowId number
---@param guideClickId number
function GuideModule:OpenGuideHDPopTipsUIAndClickUI(guidePopWindowId, guideClickId)
    self:OpenGuideHDPopTipsUI(guidePopWindowId, true)
    self:OpenGuideHDWeakClickUI(guideClickId, guidePopWindowId)
end

function GuideModule:CloseGuideHDPopTipsUIAndClickUI(guidePopWindowId, guideClickId)
    GuideLogic.CommonCloseCacheGuideUI(UIName2ID.GuideHDPopTipsUI, guidePopWindowId)
    GuideLogic.CommonCloseCacheGuideUI(UIName2ID.GuideHDWeakClickUI, guideClickId)
end

--endregion
-------------

-------------
--region HDPopDesc & WEAK Click

---@param guidePopFunctionDescId number
---@param callback function (just do EndGuide(#nextGuideIds) from `GuideDataHDPopDescAndWeakClick`)
function GuideModule:OpenGuideHDPopFunctionDescAndWeakClickUI(guidePopFunctionDescId, callback)
    local guidePopFunctionDescConfig = GuideConfig.TableGuidePopFunctionDescConfig[guidePopFunctionDescId]
    if not guidePopFunctionDescConfig then
        logwarning("OpenGuideHDPopFunctionDescAndWeakClickUI id invalid", guidePopFunctionDescId)
        return
    end

    local guideClickId = guidePopFunctionDescConfig.ClickConfigId[1]

    local _OnCloseFunctionUI = function()
        Module.Guide:CloseGuideHDWeakClickUI(guideClickId)
        local UGuideUtils = import "GuideUtils"
        UGuideUtils.BoardcastOnGuideClickUIClosed(GetWorld(), guideClickId)

        if callback then -- OnEndGuide(#nextGuideId)
            callback()
        end

        GuideConfig.EGuideEvent.evtCloseHDPopFunctionDescAndWeakClick:Invoke()
    end
    self:OpenGuideHDPopFunctionDescUI(guidePopFunctionDescId, _OnCloseFunctionUI)
    self:OpenGuideHDWeakClickUI(guideClickId, guidePopFunctionDescId)
end

--- 仅重新设置弱点击的UI
---@param guidePopFunctionDescId number
function GuideModule:ResetWeakClickForGuideHDPopFunctionDesc(guidePopFunctionDescId)
    local guidePopFunctionDescConfig = GuideConfig.TableGuidePopFunctionDescConfig[guidePopFunctionDescId]
    if not guidePopFunctionDescConfig then
        logwarning("OpenGuideHDPopFunctionDescAndWeakClickUI id invalid", guidePopFunctionDescId)
        return
    end
    local guideClickId = guidePopFunctionDescConfig.ClickConfigId[1]
    self:OpenGuideHDWeakClickUI(guideClickId, guidePopFunctionDescId)
end


function GuideModule:CloseGuideHDPopFunctionDescAndWeakClickUI(guidePopFunctionDescId )
    local guidePopFunctionDescConfig = GuideConfig.TableGuidePopFunctionDescConfig[guidePopFunctionDescId]
    if not guidePopFunctionDescConfig then
        logwarning("CloseGuideHDPopFunctionDescAndWeakClickUI id invalid", guidePopFunctionDescId)
        return
    end
    local guideClickId = guidePopFunctionDescConfig.ClickConfigId[1]
    log("CloseGuideHDPopFunctionDescAndWeakClickUI", guidePopFunctionDescId, guideClickId)
    GuideLogic.CommonCloseCacheGuideUI(UIName2ID.GuideHDCommonDescUI, guidePopFunctionDescId)
    GuideLogic.CommonCloseCacheGuideUI(UIName2ID.GuideHDWeakClickUI, guideClickId)
end

function GuideModule:SetPopFunctionDescAndWeakClickUIValidState(guidePopFunctionDescId, bValid)
    local guidePopFunctionDescConfig = GuideConfig.TableGuidePopFunctionDescConfig[guidePopFunctionDescId]
    if not guidePopFunctionDescConfig then
        logwarning("SetPopFunctionDescAndWeakClickUIValidState id invalid", guidePopFunctionDescId)
        return
    end
    local clickId = guidePopFunctionDescConfig.ClickConfigId[1]
    GuideLogic.CommonSetGuideUIValidState(UIName2ID.GuideHDCommonDescUI, guidePopFunctionDescId, bValid)
    GuideLogic.CommonSetGuideUIValidState(UIName2ID.GuideHDWeakClickUI, clickId, bValid)
end
--endregion
-------------


-------------
--region HDPopDesc & Click

function GuideModule:OpenGuideHDPopFunctionDescAndClickUI(guidePopFunctionDescId, callback )
    local guidePopFunctionDescConfig = GuideConfig.TableGuidePopFunctionDescConfig[guidePopFunctionDescId]
    if not guidePopFunctionDescConfig then
        logwarning("OpenGuideHDPopFunctionDescAndClickUI id invalid", guidePopFunctionDescId)
        return
    end
    local clickId = guidePopFunctionDescConfig.ClickConfigId[1]
    local _OnCloseFunctionUI = function()
        Module.Guide:CloseGuideHDClickUI(clickId)
        if callback then
            callback()
        end
    end
    self:OpenGuideHDPopFunctionDescUI(guidePopFunctionDescId, _OnCloseFunctionUI)
    self:OpenGuideHDClickUI(clickId, guidePopFunctionDescId)
end

function GuideModule:CloseGuideHDPopFunctionDescAndClickUI(guidePopFunctionDescId)
    local guidePopFunctionDescConfig = GuideConfig.TableGuidePopFunctionDescConfig[guidePopFunctionDescId]
    if not guidePopFunctionDescConfig then
        logwarning("CloseGuideHDPopFunctionDescAndClickUI id invalid", guidePopFunctionDescId)
        return
    end
    local clickId = guidePopFunctionDescConfig.ClickConfigId[1]
    log("CloseGuideHDPopFunctionDescAndClickUI", guidePopFunctionDescId, clickId)
    GuideLogic.CommonCloseCacheGuideUI(UIName2ID.GuideHDCommonDescUI, guidePopFunctionDescId)
    GuideLogic.CommonCloseCacheGuideUI(UIName2ID.GuideHDClickUI, clickId)
end

function GuideModule:SetPopFunctionDescAndClickUIValidState(guidePopFunctionDescId, bValid)
    local guidePopFunctionDescConfig = GuideConfig.TableGuidePopFunctionDescConfig[guidePopFunctionDescId]
    if not guidePopFunctionDescConfig then
        logwarning("SetPopFunctionDescAndClickUIValidState id invalid", guidePopFunctionDescId)
        return
    end
    local clickId = guidePopFunctionDescConfig.ClickConfigId[1]
    GuideLogic.CommonSetGuideUIValidState(UIName2ID.GuideHDCommonDescUI, guidePopFunctionDescId, bValid)
    GuideLogic.CommonSetGuideUIValidState(UIName2ID.GuideHDClickUI, clickId, bValid)
end




function GuideModule:OpenGuideHDPopFunctionDescAndWeakClickUIOld(guidePopFunctionDescId, guideClickId)
    local _OnCloseFunctionUI = function()
        Module.Guide:CloseGuideHDWeakClickUI(guideClickId)

        local UGuideUtils = import "GuideUtils"
        UGuideUtils.BoardcastOnGuideClickUIClosed(GetWorld(), guideClickId)
    end
    self:OpenGuideHDPopFunctionDescUI(guidePopFunctionDescId, _OnCloseFunctionUI)
    self:OpenGuideHDWeakClickUI(guideClickId)
end

function GuideModule:CloseGuideHDPopFunctionDescAndWeakClickUIOld(guidePopFunctionDescId, guideClickId)
    log("CloseGuideHDPopFunctionDescAndWeakClickUI", guidePopFunctionDescId, guideClickId)
    GuideLogic.CommonCloseCacheGuideUI(UIName2ID.GuideHDCommonDescUI, guidePopFunctionDescId)
    GuideLogic.CommonCloseCacheGuideUI(UIName2ID.GuideHDWeakClickUI, guideClickId)
end

function GuideModule:OpenGuideHDPopFunctionDescAndClickUIOld(guidePopFunctionDescId, guideClickId)
    local _OnCloseFunctionUI = function()
        Module.Guide:CloseGuideHDClickUI(guideClickId)

        local UGuideUtils = import "GuideUtils"
        UGuideUtils.BoardcastOnGuideClickUIClosed(GetWorld(), guideClickId)
    end
    self:OpenGuideHDPopFunctionDescUI(guidePopFunctionDescId, _OnCloseFunctionUI)
    self:OpenGuideHDClickUI(guideClickId)
end

function GuideModule:CloseGuideHDPopFunctionDescAndClickUIOld(guidePopFunctionDescId, guideClickId)
    log("CloseGuideHDPopFunctionDescAndClickUI", guidePopFunctionDescId, guideClickId)
    GuideLogic.CommonCloseCacheGuideUI(UIName2ID.GuideHDCommonDescUI, guidePopFunctionDescId)
    GuideLogic.CommonCloseCacheGuideUI(UIName2ID.GuideHDClickUI, guideClickId)
end

--endregion
-------------


function GuideModule:SetGuideInputGate(name, bEnable)
    GuideLogic.SetGuideInputGate(name, bEnable)
end

-- logId: GuideConfig.ESpecCommonEventLogId
function GuideModule:SendGuideSpecLog(logId, bOnlyFirst)
    GuideLogic.DoSendGuideSpecLog(logId, bOnlyFirst)
end

function GuideModule:GetGuideUIState(uiId)
    return self.Field:GetGuideUIState(uiId)
end

function GuideModule:GetBoolMpGuideIsFinished()
    --log("GetBoolMpGuideIsFinished")
    if not GuideConfig.FlagEnableGuide then
        loginfo("GuideModule:GetBoolMpGuideIsFinished FlagEnableGuide == false")
        return true
    end

    return Server.TipsRecordServer:GetBoolean(Server.TipsRecordServer.keys.MpGuideIsFinished)
end

function GuideModule:SetBoolMpGuideIsFinished(bMpFinished)
    --log("SetBoolMpGuideIsFinished", bMpFinished)
    Server.TipsRecordServer:SetBoolean(Server.TipsRecordServer.keys.MpGuideIsFinished, bMpFinished)
end

function GuideModule:GetNumberMpGuideIsFinished()
    --log("GetNumberMpGuideIsFinished")
    if not GuideConfig.FlagEnableGuide then
        loginfo("GuideModule:GetNumberMpGuideIsFinished FlagEnableGuide == false")
        return 255
    end

    return Server.TipsRecordServer:GetNumber(Server.TipsRecordServer.keys.NewMpGuideIsFinished)
end

function GuideModule:SetNumberMpGuideIsFinished(bMpFinished)
    --log("SetNumberMpGuideIsFinished", bMpFinished)
    Server.TipsRecordServer:SetNumber(Server.TipsRecordServer.keys.NewMpGuideIsFinished, bMpFinished)
end

--- 获取MP生涯对局数
function GuideModule:GetMpMatchCount(bExcludeQuit)
    --log("GetMpMatchCount")

    return Server.GuideServer:GetMatchCount(MatchGameRule.TDMClassGameRule, bExcludeQuit)
end

-- 标记新手关正在loot的AI序号
function GuideModule:SetLootingAiIndex(aiIndex)
    loginfo("SetLootingAiIndex", aiIndex)
    self.Field.lootingAiIndexInGame = aiIndex

    if aiIndex > 0 then
        self.Config.EGuideEvent.evtLootingAiTagChanged:Invoke()
    end
end



--------------
--region Debug

function GuideModule:DebugState()
    logwarning("---------------[GuideModule:DebugState]----------------")
    if GuideLogic.IsGuiding() then
        -- local curGuideStage= Module.Guide.Field:GetCurGuideStage()
        local curGuideId= Module.Guide.Field:GetCurGuideId()
        local curGuideCfg = Module.Guide.Field:GetGuideCfg(Module.Guide.Field:GetCurGuideId())
        -- logwarning("curGuideStage: ", curGuideStage)
        logwarning("curGuideId: ", curGuideId)
    else
        logwarning("Not in any gudie")
    end
    logwarning("----------------------------------------------------------")
end

function GuideModule:SetStageFinCount(k ,v)
    local finishMap = Server.TipsRecordServer:GetMap(Server.TipsRecordServer.keys.GuideStageFinished)
    k = tostring(k)
    v = tonumber(v)
    finishMap[k] = v
    Server.TipsRecordServer:SetMap(Server.TipsRecordServer.keys.GuideStageFinished, finishMap)
end


-- slua.Do Module.Guide:ResetStageFinCount(
function GuideModule:ResetStageFinCount(...)
    local stages = {...}
    local finishMap = Server.TipsRecordServer:GetMap(Server.TipsRecordServer.keys.GuideStageFinished)

    for _, strStage in ipairs(stages) do
        if type(strStage) == "number" then
            strStage = tostring(strStage)
        end
        finishMap[strStage] = 0
    end

    Server.TipsRecordServer:SetMap(Server.TipsRecordServer.keys.GuideStageFinished, finishMap)
end


-- slua.Do Module.Guide:ResetStageKeyStep(
function GuideModule:ResetStageKeyStep(...)
    local stages = {...}
    local stepMap = Server.TipsRecordServer:GetMap(GuideLogic._GetStageStepKey())
    for _, strStage in ipairs(stages) do
        if type(strStage) == "number" then
            strStage = tostring(strStage)
        end
        stepMap[strStage] = nil
    end
    Server.TipsRecordServer:SetMap(GuideLogic._GetStageStepKey(), stepMap)
end

function GuideModule:ResetStageDayPlayInfo(...)
    local stages = {...}
    local playMap = Server.TipsRecordServer:GetMap(Server.TipsRecordServer.keys.GuideStageDayPlayed)
    for _, strStage in ipairs(stages) do
        if type(strStage) == "number" then
            strStage = tostring(strStage)
        end
        playMap[strStage] = nil
    end
    Server.TipsRecordServer:SetMap(Server.TipsRecordServer.keys.GuideStageDayPlayed, playMap)
end

function GuideModule:ResetStage(...)
    self:ResetStageFinCount(...)
    self:ResetStageKeyStep(...)
    self:ResetStageDayPlayInfo(...)
end

-- Module.Guide:ExecCustomDataFunc(
function GuideModule:ExecCustomDataFunc(...)
    local funcName = select(1, ...)
    local guideDataClass = require("DFM.Business.Module.GuideModule.Data.GuideDataCustom")
    local guideData = guideDataClass:NewIns(--[[guideId, guideCfg]])
    guideData[funcName](guideData, select(2, ...))
end

function GuideModule:DevDelayFunc(sec, ...)
    local args = {...}
    Timer.DelayCall(sec, function()
        self:ExecCustomDataFunc(table.unpack( args))
    end)
end

function GuideModule:DevReInitCfgTable()
    self.Field:_InitCfgTable()
end

--endregion
--------------


------------------------------------------------
--region Evacuation Video 

function GuideModule:SetIsEvacuateTeachingPlaying(isPlaying)
    Module.Guide.Field:SetIsEvacuateTeachingPlaying(isPlaying)
end

function GuideModule:GetIsEvacuateTeachingPlaying()
    return Module.Guide.Field:GetIsEvacuateTeachingPlaying()
end



-- azhengzheng:playType暂时用1表示引导、2表示结算
-- 从SettlementServer中获取撤离原因，播放对应的引导视频
---@param playType nil|1|2
---@param evacuationResultType EGspPlayerResultType|nil
---@return boolean  是否成功开始播放
function GuideModule:PlayEvacuateTeaching(playType, evacuationResultType)
    loginfo("GuideModule:PlayEvacuateTeaching", playType)
    if self.Field:GetIsEvacuateTeachingPlaying() then
        return false
    end

    playType = playType or 1
    self.Field:SetIsEvacuateTeachingPlaying(true)

    -- 国赓ToDo
    local bSuccess =  GuideLogic.TryPlaySettlementVideoGuide(playType, evacuationResultType)
    if not bSuccess then
        logwarning("GuideModule:PlayEvacuateTeaching fail")
        self.Field:SetIsEvacuateTeachingPlaying(false)
    end
    return bSuccess
end

-- playType:播放类型 1 
-- playTime:时长（秒）
-- bEnd:是否完播
function GuideModule:OnPlayEvacuateTeachingFinish(playID, playType,  playTime, bFullPlayed)
    Module.Guide:SetIsEvacuateTeachingPlaying(false)
    local isHDWithGamePad = IsHD() and WidgetUtil.IsGamepad() or false
    log("GuideModule:OnPlayEvacuateTeachingFinish", playID, playType, playTime, bFullPlayed, isHDWithGamePad)

    -- azhengzhengToDo:埋点上报
    LogAnalysisTool.DoSendSettlementGuideVideoClientReportLog(playID, playType, math.floor(playTime), bFullPlayed and 1 or 0, isHDWithGamePad)
end

--endregion
----------------------------------------------------


--- set a guide stage that will be skip(did not meet start condition) temporary
---@param guideStageId integer
---@param bSkip boolean|nil
function GuideModule:SetClientSkipGuide(guideStageId, bSkip)
    bSkip = bSkip or true
    loginfo("SetClientSkipGuide", guideStageId, bSkip)
    self.Field.ClientSkipGuides:Set(guideStageId, bSkip)
end


function GuideModule:IsGuideEnable()
    return GuideConfig.FlagEnableGuide and Server.GuideServer.FlagEnableGuide
end

-- call from cpp hud
function GuideModule:IsEvacuationPointGuideEnable()
    if not self:IsGuideEnable() then
        return false
        -- return GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solPlayerEvacuationPointInstruct)
    end
    return true
end


-- 是否需要在结算页展示英雄技能对抗引导入口
---@return boolean
function GuideModule:IsNeedHeroSkillAgainstEntry()
    -- 1. check frequency
    if not GuideLogic_CheckCondition.NeedSkillAgainstGuide() then
        return false
    end

    -- 2. check if killed with any hero skill assist, choose max priority hero skill
    -- 3. check if current guide support target hero skill guide
    local firstEntry  =  GuideLogic.GetFirstAvaliableSkillAgainstEntry() 
    log("GuideModule:IsNeedHeroSkillAgainstEntry", firstEntry and firstEntry.name)
    return firstEntry ~= nil
end

---@param sig EGuideMsgSig
function GuideModule:SendMsg(sig, ...)
    if self.Config.FlagEnableGuide then
        return GuideEvtLogic.OnGuideMsg(sig, ...)
    end
end



return GuideModule
