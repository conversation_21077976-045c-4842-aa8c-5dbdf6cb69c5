----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCommonWidget)
----- LOG FUNCTION AUTO GENERATE END -----------



local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig" 
local IVTemplateBase = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.IVTemplateBase"
local IVTextIconComponent = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.Components.IVTextIconComponent"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local IVMainIconComponent = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.Components.IVMainIconComponent"
local IVQualityComponent = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.Components.IVQualityComponent"
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local ItemBaseTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemBaseTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local InventoryEvtLogic = require "DFM.Business.Module.InventoryModule.InventoryEvtLogic"
local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"
local ShopHelperTool = require "DFM.StandaloneLua.BusinessTool.ShopHelperTool"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local LootingAudioLogic = require "DFM.Business.Module.LootingModule.LootingAudioLogic"
local ItemConfigTool    = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local InventoryConfig = require "DFM.Business.Module.InventoryModule.InventoryConfig"
local IVBgComponent = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.Components.IVBgComponent"

local EViewDragMode = import "EViewDragMode"
local EItemState = import "EItemState"
local UGPInputHelper = import "GPInputHelper"
local UWidgetBlueprintLibrary = import "WidgetBlueprintLibrary"
local UDFCanvasPanel = import "DFCanvasPanel"
local ULuaItemDragConfigUtil = import "LuaItemDragConfigUtil"
local UKismetInputLibrary = import "KismetInputLibrary"
local EMouseCursor = import("EMouseCursor")
local UAmmoDataManager = import "AmmoDataManager"
local EItemInfoUpdatedReason = import "EItemInfoUpdatedReason"
local EDragPivot = import("EDragPivot")
local EDescendantScrollDestination = import "EDescendantScrollDestination"
local UAssembleWeaponDataLibrary = import "AssembleWeaponDataLibrary"
local UAmmoDataManager = import "AmmoDataManager"
local UCollectionRoomConstantWrap = import "CollectionRoomConstantWrap"
local FAnchors = import "Anchors"
local FMargin = import "Margin"
local UGlobalConstWrapper = import "GlobalConstWrapper"

--BEGIN MODIFICATION @ VIRTUOS : 手柄操作依赖
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPInputType = import("EGPInputType")
local UWidgetLayoutLibrary = import "WidgetLayoutLibrary"
local IVCommonBlankItemTemplate = require "DFM.Business.Module.CommonWidgetModule.UI.HD.IVCommonBlankItemTemplate"
--END MODIFICATION
local InventoryNavManager = require "DFM.Business.Module.InventoryModule.Logic.InventoryNavManager"

---@class IVWarehouseTemplateParams
---@field bBindCommonItemClickEvent boolean
---@field bShowDoubleClickEffectHint boolean

---@class IVWarehouseTemplate : IVTemplateBase
local IVWarehouseTemplate = ui("IVWarehouseTemplate", IVTemplateBase)

IVWarehouseTemplate.DEFAULT_SCALE = FVector2D(1, 1)
IVWarehouseTemplate.TRIGGER_DRAG_SCALE = FVector2D(1.2, 1.2)
IVWarehouseTemplate.TOGGLE_GRAB_MODE = false
IVWarehouseTemplate.TOGGLE_LONG_PRESS = false

local EComp = CommonWidgetConfig.EIVWarehouseTempComponent
local EIVSlotPos = CommonWidgetConfig.EIVSlotPos
local EIVCompOrder = CommonWidgetConfig.EIVCompOrder
local EIVItemViewMode = CommonWidgetConfig.EIVItemViewMode
local ShowWeaaponBp = CommonWidgetConfig.EIVWeaponBPShowStyle
local ComponentToVisibility = {}

local function log(...)
	loginfo("[IVWarehouseTemplate]", ...)
end

local EViewSearchState = ItemBase.EViewSearchState

local ammoMgr = UAmmoDataManager.Get()

function IVWarehouseTemplate:Ctor()
    -- Override LuaUIBaseView
    self._shouldRecoveryVisibility = false
    self.bIsShow = false
    self.bIsRefresh = false

    self._bFirstInit = false
	self._bBindCommonItemClickEvent = true
	self._bShowDoubleClickEffectHint = nil	-- 默认使用全局配置
    self._slotParent = nil
    self._bShouldShowDoubleClickHint = true
    self._bShowNewTag = true
    self._bListeningItemMoveEvt = false
    self.itemviewMode = EIVItemViewMode.CommonSlotItemView
    self._bUseCustomInteractivityControl = false
    self._viewSearchState = EViewSearchState.None
    self._bInTriggerDragState = false
    -- self._blockFrame = -1

    self._refreshTimestamp = -1

    self.bInMarkSelectionMode = false
    self._bCustomInteractivity = false    -- 自定义互动性

    self._bInPermanentHighLight = false

	self._wtItemIcon = self:Wnd("wtItemIcon", IVMainIconComponent)
	self._wtItemBg = self:Wnd("wtItemBg", IVBgComponent)
    -- self._wtItemBg:SetRenderOpacity(0.8)
	self._wtItemQuality = self:Wnd("wtItemQuality", IVQualityComponent)

	self._wtBottomLeftIconText = self:Wnd("wtBottomLeftIconText", IVTextIconComponent)
    self._wtBottomLeftIconText:SetComponentPos(EIVSlotPos.BottomLeft)
	self._wtBottomRightIconText = self:Wnd("wtBottomRightIconText", IVTextIconComponent)
    self._wtBottomRightIconText:SetComponentPos(EIVSlotPos.BottomRight)
    self._wtDFTipsAnchor = UIUtil.WndTipsAnchor(self, "wtDFTipsAnchor", self._OnHovered, self._OnUnhovered)

    self:RegisterComponent(EComp.ItemIcon, self._wtItemIcon)
    self:RegisterComponent(EComp.ItemBg, self._wtItemBg)
    self:RegisterComponent(EComp.ItemQuality, self._wtItemQuality)
    self:RegisterComponent(EComp.BottomLeftIconText, self._wtBottomLeftIconText)
    self:RegisterComponent(EComp.BottomRightIconText, self._wtBottomRightIconText)
    self._wtBottomRightIconText:SetCompOrder(EIVCompOrder.DefaultOrder)
    self._wtBottomLeftIconText:SetStyle(CommonWidgetConfig.EIVIconTextStyle.Binding)

	self:_InitDefaultInteractive()
	-- self:_InitialSetupComponents()

    -- self:_CreateHoverInHD()
    self:_PreloadComponents()

    self._timer = nil

    self._lastEffectiveDoubleClickTime = 0

    self._timers = {}

    --BEGIN MODIFICATION @ VIRTUOS : 启用鼠标中键和导航聚焦
    if IsHD() then
        self:SetCppValue("bHandleClickMid", true)
        self:SetCppValue("bIsFocusable", true)
    end

    -- 手柄提示控件引用
    self._wtSingleHoldKeyTip = nil
    -- self._wtMultiKeysTipOnDrag = nil
    --END MODIFICATION
end

function IVWarehouseTemplate:Destroy()
    UIUtil.RemoveTipsAnchorEvent(self._wtDFTipsAnchor)

    if Facade.ModuleManager:IsModuleValid("CommonWidget") then
        Module.CommonWidget:UnregisterItemView(self)
    end

    --BEGIN MODIFICATION @ VIRTUOS : 移除聚焦事件
    if IsHD() then
        self:RemoveEvent("OnFocusReceivedEvent")
        self:RemoveEvent("OnFocusLostEvent")
    end
    --END MODIFICATION
end

function IVWarehouseTemplate:_PreloadComponents()
    if ItemOperaTool.CheckRunWarehouseLogic() then

    else
        --self:PreloadComponent(EComp.ItemHighlight, UIName2ID.CommonItemHighlight, EIVSlotPos.MaskLayer, EIVCompOrder.High)
        self:PreloadComponent(EComp.TopLeftIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.TopLeft, EIVCompOrder.Order1)
    end
end

function IVWarehouseTemplate:OnObjectPoolFreed()
    self:ShowOrHideCommonHoverBg(false)
    if not ItemOperaTool.CheckRunWarehouseLogic() then
        local useAnimComp = self:GetComponent(EComp.UseAnim)
        if useAnimComp then
            useAnimComp:HideUsingTips()
        end
        self:EnableComponent(EComp.SearchAnim, false, true)
        self._viewSearchState = ItemBase.EViewSearchState.None
    end
    if self._timer then
        self._timer:Destroy()
    end

    if self.bUsedByEquipmentRental then
        self.OnClicked = IVWarehouseTemplate.OnClicked
        self.OnImmediateClicked = IVWarehouseTemplate.OnImmediateClicked
        if IsHD() then
            self:SetCppValue("bHandleClickR", true)
        end
        self._fPostRefreshFunc = nil
        self.bUsedByEquipmentRental = nil
    end

    local mainIcon = self:GetItemIconComponent()
    if mainIcon then
        mainIcon:SetParentSlot(nil)
    end
end

-- function IVWarehouseTemplate:_CreateHoverInHD()
--     if IsHD() then
--         local commonHoverWidget = UWidgetBlueprintLibrary.Create(self, self.HoverBPClass, nil)
--         local slot = self:GetSlotByPos(CommonWidgetConfig.EIVSlotPos.StateLayer)
--         -- local container = slot:GetContent()
--         if self.itemviewPanel then
--             local dfCanvasPanel = UDFCanvasPanel()
--             -- self.itemviewPanel:AddChild(dfCanvasPanel)
--             UIUtil.AddWidgetToParent_Full(dfCanvasPanel, self.itemviewPanel)
--             UIUtil.AddWidgetToParent_Full(commonHoverWidget, dfCanvasPanel)
--         end
--     end
-- end

--==================================================
--region Life function
function IVWarehouseTemplate:OnOpen()
	if self._bBindCommonItemClickEvent then
        self:AddLuaEvent(CommonWidgetConfig.Events.evtItemSetSelected, self.SetSelected, self)
        self:AddLuaEvent(CommonWidgetConfig.Events.evtInGameItemSelected, self.SetSelected, self)
    end

    self:AddLuaEvent(CommonWidgetConfig.Events.evtPostItemMultiSelected, self._OnSetMultiSelected, self)
    self:AddLuaEvent(CommonWidgetConfig.Events.evtItemOperaModeChanged, self._OnItemOperaModeChanged, self)
    self:AddLuaEvent(Server.InventoryServer.Events.evtItemMarkStatusChanged, self._OnItemMarkStatusChanged, self)

    self:AddLuaEvent(CommonWidgetConfig.Events.evtNotifyItemHighlight, self._OnNotifyItemHighlight, self)

    self:AddLuaEvent(Module.Inventory.Config.Events.evtShowEquipCompareIcon, self._ShowCompareIcon, self)
	self:AddLuaEvent(Module.ItemDetail.Config.evtItemDetailPanelClosed, self._HideCompareIcon, self)

    self:AddLuaEvent(Module.Looting.Config.Events.evtPlayerItemMarkingInfoArrayChange, self._OnPlayerItemMarkingInfoArrayChange, self)
    self:AddLuaEvent(Module.Looting.Config.Events.evtMarkSelectionModeChange, self._OnMarkSelectionModeChange, self)
	self:AddLuaEvent(Module.CommonWidget.Config.Events.evtGlobalItemDrop, self.OnDragEnd, self)
	self:AddLuaEvent(Module.CommonWidget.Config.Events.evtGlobalItemDragCancelled, self.OnDragEnd, self)
    self:AddLuaEvent(Module.CollectionRoom.Config.Events.evtOnClickedDIYGrid, self.CheckItemCanBePutInDIYCabinet, self)

--[[     UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonUpEvent:Add(self._SetSelected,self)
    UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonDownEvent:Add(self._SetSelected,self) ]]

     if DFHD_LUA == 1 then
         self:Event("OnMouseEnterEvent",self._OnMouseEnterEvent,self)
         self:Event("OnMouseLeaveEvent",self._OnMouseLeaveEvent,self)
         -- self:_SetHoverTips()
     end

    --BEGIN MODIFICATION @ VIRTUOS : 绑定聚焦事件
    if IsHD() then
        self:Event("OnFocusReceivedEvent", self._OnFocusReceived, self)
        self:Event("OnFocusLostEvent", self._OnFocusLost, self)
        self:AddLuaEvent(CommonWidgetConfig.Events.evtGlobalHoverItemViewUseAnim, self._OnGlobalHoverItemViewUseAnim, self)
    end
    --END MODIFICATION    
end

function IVWarehouseTemplate:OnClose()
	self:RemoveAllLuaEvent()
    if DFHD_LUA == 1 then
        if self._keyRegister then
            self._keyRegister:RemoveAllListeners()
            self._keyRegister = nil
        end
    end
    Facade.UIManager:ClearAllSubUI(self)
    if self._timer then
        self._timer:Destroy()
    end
    self:ClearGlobalRegisterComponents()
end

function IVWarehouseTemplate:OnShowBegin()
    if not ItemOperaTool.CheckRunWarehouseLogic() and self.item then
        self:CheckSearchState(nil, true)
    end

    self:EnableComponent(EComp.TipMask, false)
end

function IVWarehouseTemplate:OnShow()
    self:RemoveLuaEvent(CommonWidgetConfig.Events.evtHighLightItem)
    if DFHD_LUA == 1 then
        if self.bHandleClickR then
            self:SetCursor(EMouseCursor.Menu)
        else
            self:SetCursor(EMouseCursor.Hand)
        end
        self:_CheckMouseCursor()

        --BEGIN MODIFICATION @ VIRTUOS : 
        self._bOnFocused = false
        --END MODIFICATION
    end
    if not self._bListeningItemMoveEvt then
        if self.item and self.item:IsBullet() and not self.item:IsEquipped() then
            self._bListeningItemMoveEvt = true
            self:RefreshComponent(EComp.BottomLeftIconText2)
            self:AddLuaEvent(Server.InventoryServer.Events.evtItemMove, self._OnItemMoved, self)
            self:AddLuaEvent(Server.LootingServer.Events.evtLootingItemMove, self._OnItemMoved, self)
        end
    end
end

function IVWarehouseTemplate:OnHide()
    if self._bListeningItemMoveEvt then
        self._bListeningItemMoveEvt = false
        self:RemoveLuaEvent(Server.InventoryServer.Events.evtItemMove)
        self:RemoveLuaEvent(Server.LootingServer.Events.evtLootingItemMove)
    end
    self:ClearGlobalRegisterComponents()

    --BEGIN MODIFICATION @ VIRTUOS : 
    if IsHD() then
        self._bOnFocused = false
    end
    --END MODIFICATION
end

---@return IVComponentBase
function IVWarehouseTemplate:FindOrAdd(id, componentNameID, pos, order, bUseGlobalRegister)
    local component = self:GetComponent(id)
    if not component and bUseGlobalRegister and CommonWidgetConfig.bUseIVCompPool then
        component = Module.CommonWidget:ObtainIVComp(componentNameID)
        self.itemviewPanel:AddChild(component)

        if component then
            self:AddComponentAtPosition2(id, component, pos, order)
            component:SetComponentPos(pos)
            if self.item then
                component:BindItem(self.item)
            end
        else
            logwarning("[IVWarehouseTemplate]: uiIns is nil, please check")
        end
    else
        component = IVTemplateBase.FindOrAdd(self, id, componentNameID, pos, order, bUseGlobalRegister)
    end

    return component
end

function IVWarehouseTemplate:EnableComponent(id, bEnable, bReturn2GlobalRegisterIfDisable)
    bReturn2GlobalRegisterIfDisable = setdefault(bReturn2GlobalRegisterIfDisable, false)
    local component = self:GetComponent(id)
    if isinvalid(component) then
        return component
    end

    if not bEnable and bReturn2GlobalRegisterIfDisable and CommonWidgetConfig.bUseIVCompPool then
        component:EnableComponent(bEnable)
        self.itemviewPanel:RemoveChild(component)
        Module.CommonWidget:FreeIVComp(component, component.UINavID)
        self._itemComponents[id] = nil
    else
        component = IVTemplateBase.EnableComponent(self, id, bEnable, bReturn2GlobalRegisterIfDisable)
    end
    return component
end

function IVWarehouseTemplate:GetTipsAnchor()
    return self._wtDFTipsAnchor
end

---@type templateParams IVWarehouseTemplateParams
function IVWarehouseTemplate:OnInitExtraData(templateParams)
	if not templateParams then
		return
	end

	if templateParams.bBindCommonItemClickEvent ~= nil then
		self._bBindCommonItemClickEvent = templateParams.bBindCommonItemClickEvent
	end
end

function IVWarehouseTemplate:_OnDoubleClickLogic()
	-- 多选模式下不响应双击
	if Module.CommonWidget:IsInMultiSelectedMode() then
		return
	end
    -- 搜索状态下不响应双击
    if self.item and self.item:NeedSearchByLocalPlayer() then
        return
    end

    -- 拍卖行响应双击
    if Module.CommonWidget:IsAuctionMode() then
        CommonWidgetConfig.Events.evtItemClickedByAuction:Invoke(self)
        return
    end

    if self.item and self.item.InSlot then
        local slot = self.item.InSlot
        local slotType = slot.SlotType
        local SlotsNotAllowOperate = ItemOperaTool.CheckRunWarehouseLogic() and Module.Inventory.Config.SlotsNotAllowOperate
                or Module.Looting.Config.SlotsNotAllowOperate
        if SlotsNotAllowOperate[slotType] then
            logwarning("IVWarehouseTemplate:_OnDoubleClickLogic blocked", self.item.id, slotType)
            return
        end

        if not ItemOperaTool.CheckRunWarehouseLogic() then
            if not slot:HasItem(self.item) then
                logwarning("IVWarehouseTemplate:_OnDoubleClickLogic blocked", self.item.name, self.item.gid)
                return
            end
        end
    end
    
    -- 记录道具操作来源
    ItemMoveCmd.RecordMoveOperation(ItemMoveCmd.EFromOperation.ItemViewDoubleClick)

    -- 收藏室展示台上的itemview没有itemslot，所以需要确保self.item.InSlot存在
    if IsHD() and WidgetUtil.IsGamepad() and self.item.InSlot then
        InventoryNavManager.nextTargetItem = Module.Inventory:GetNavNextTarget(self.item)
        InventoryNavManager.bIsQuickCarry = true
    end

    local ts = Facade.ClockManager:GetLocalTimestamp()
    Module.CommonWidget:SetLastItemDoubleClickTimestamp(ts)
	CommonWidgetConfig.Events.evtItemDoubleClicked:Invoke(self)

    -- self:EnableComponent(EComp.TopLeftNewTag,false)
end

function IVWarehouseTemplate:IsNotAllowOperateInCollectionRoom()
    if ItemOperaTool.CheckRunCollectionRoomLootingLogic() then
        if self.item and self.item.InSlot then
            local slotType = self.item.InSlot.SlotType
            if not InventoryConfig.SlotsAllowOperateInCollectionRoom[slotType] then
                return true
            end
        end
    end
    return false
end

function IVWarehouseTemplate:OnClicked()
    if self:IsNotAllowOperateInCollectionRoom() then
        return
    end
    if self:IsNotCompletedSearched() then
        return
    end
    if self.item then
        log("IVWarehouseTemplate:OnClicked", self.item.name, self.item.gid)
    else
        return
    end
    -- log("OnClicked")
	Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UILobbyWHClick)

    local bItemHaveTeammateBindCondition, itemTeammateBindConditionReason = ItemOperaTool.CheckItemHaveTeammateBindCondition(self.item)
    if bItemHaveTeammateBindCondition then
        if itemTeammateBindConditionReason == ItemOperaTool.ETeammateBindConditionType.SelfTeammateBind then
            Module.CommonTips:ShowSimpleTip(Module.CommonWidget.Config.Loc.TeammateBindingItemTip)
        elseif itemTeammateBindConditionReason == ItemOperaTool.ETeammateBindConditionType.HaveTeammateBind then
            Module.CommonTips:ShowSimpleTip(Module.CommonWidget.Config.Loc.TeammateBindingContainerExistsTip)
        elseif itemTeammateBindConditionReason == ItemOperaTool.ETeammateBindConditionType.HaveTeammateBindAdapter then
            Module.CommonTips:ShowSimpleTip(Module.CommonWidget.Config.Loc.TeammateBindingWeaponExistsTip)
        end
        return
    end

    -- looting mark hack
    if Module.Looting:IsInMarkSelectionMode() then
        Module.Looting:PlayerSelectMarkItem(self.item)
        return
    end

    local inputMonitor = Facade.UIManager:GetInputMonitor()
    if inputMonitor:IsKeyPressedByName("LeftAlt") then
        loginfo("IVWarehouseTemplate:OnClicked", "return because of", "LeftAlt")
        return
    elseif inputMonitor:IsKeyPressedByName("LeftControl") then
        loginfo("IVWarehouseTemplate:OnClicked", "return because of", "LeftControl")
        return
    elseif inputMonitor:IsKeyPressedByName("LeftShift") then
        loginfo("IVWarehouseTemplate:OnClicked", "return because of", "LeftShift")
        return
    end

    --local item = Module.CommonWidget:GetRespondSingleClickItem()
    local bIsSingleClickItemResponded = Module.CommonWidget:IsSingleClickItemResponded(self.item)
    if self:_TriggerSingleClickMove() then
        return
    end

    -- pc 不需要
    if not IsHD() then
        self:_CheckShowDoubleClickHint()
    end

	if Module.CommonWidget:IsInMultiSelectedMode() then
        if self:_CheckMultiModeEnabled() then
            local bCurrentMultiSelected = Module.CommonWidget:CheckItemMultiSelected(self.item)
            Module.CommonWidget:SetItemMultiSelected(self.item, not bCurrentMultiSelected, true)
        else
            if not self:_CheckItemCanSell() then
                Module.CommonTips:ShowSimpleTip(string.format(CommonWidgetConfig.Loc.ItemCantSell, self.item.name))
            end
        end
    elseif Module.CommonWidget:IsAuctionMode() then
        CommonWidgetConfig.Events.evtItemClickedByAuction:Invoke(self)
    elseif ItemOperaTool.CheckRunWarehouseLogic() and self.item
        and self.item.InSlot and (self.item.InSlot.SlotType == ESlotType.SafeBox or self.item.InSlot.SlotType == ESlotType.KeyChain) then
            if ItemOperaTool.bInSettlement then
                return
            elseif self.item.InSlot.SlotType == ESlotType.SafeBox then
                Facade.UIManager:AsyncShowUI(UIName2ID.SafeBoxAndKeyChainMain, nil, nil, self.item.InSlot.SlotType, self.item)
            elseif self.item.InSlot.SlotType == ESlotType.KeyChain then
                Facade.UIManager:AsyncShowUI(UIName2ID.SafeBoxAndKeyChainPopWindow, nil, nil, self.item.InSlot.SlotType, self.item)
            end
    elseif ItemOperaTool.CheckRunWarehouseLogic() and self.item and self.item.InSlot and self.item.InSlot.SlotType == ESlotType.MeleeWeapon then
        local slotViewType = self.item.InSlot.SlotType
        local slotGroup = self.item.InSlot:GetSlotGroup()
        Facade.UIManager:AsyncShowUI(UIName2ID.AssemblySelectionMain, nil, nil, slotViewType, slotGroup)
    else
        Module.CommonWidget:SetItemMultiSelected(self.item, false, true)
        if not bIsSingleClickItemResponded then
            --BEGIN MODIFICATION @ VIRTUOS : 
            if IsHD() then
                Module.CommonWidget:SetOpenedPopItemView(self, self.item)
            end
            --END MODIFICATION
            CommonWidgetConfig.Events.evtItemClicked:Invoke(self)
        end
    end

    -- self:EnableComponent(EComp.TopLeftNewTag,false)
end

function IVWarehouseTemplate:_TriggerSingleClickMove()
    local item = ItemOperaTool.GetCurrentSingleClickMoveItem()
    if not item then
        return false
    end
    if self.item == item then
        return false
    end
    if self.item and self.item:GetFeature(EFeatureType.Weapon) and (item:GetFeature(EFeatureType.Adapter) or item:IsBullet()) then
        -- 单击物品转移，配件或子弹移动到武器上，尝试安装
        local inOperation = {DefaultDragVisual = {item = item}, WidgetReference = {item = item}}
        local tarLoc = self.item.InSlot:GetItemLocation(self.item)

        local EFastEquipCheckResult = InventoryConfig.EFastEquipCheckResult

        local function FastEquipLogic()
            local rawDescObj = self.item:GetRawDescObj()
            local weaponFeature = self.item:GetFeature(EFeatureType.Weapon)
            local adapterFeature = item:GetFeature(EFeatureType.Adapter)
            if weaponFeature and weaponFeature:IsWeapon() and adapterFeature and adapterFeature:IsAdapter() then
                if UAssembleWeaponDataLibrary.IsCanInstallOnWeapon(rawDescObj, item.id) then
                    local isEmpty = WeaponAssemblyTool.CheckWeaponHasAvailableEmptyAdapterSocket(rawDescObj, item.id)
                    local fastEquipCheckResult = Module.Inventory:CommonOnDropForFastEquip(inOperation, tarLoc.ItemSlot, tarLoc.X, tarLoc.Y, tarLoc.SubIndex, false, true)
                    if fastEquipCheckResult ~= EFastEquipCheckResult.NoFastEquip then
                        if ItemOperaTool.CheckRunWarehouseLogic() then
                            Module.Inventory:SetSingleClickEquipTargetItem(self.item, isEmpty)
                        end
                        if not ItemOperaTool.CheckRunWarehouseLogic() then
                            if fastEquipCheckResult == EFastEquipCheckResult.Success then
                                if isEmpty then
                                    Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.SingleClickMove_EquipSucceed)
                                else
                                    Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.SingleClickMove_ReplaceSucceed)
                                end
                            else
                                if isEmpty then
                                    Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.SingleClickMove_EquipFail)
                                else
                                    Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.SingleClickMove_ReplaceFail)
                                end
                            end
                        end
                    end
                    ItemOperaTool.DoSelectItem(nil, false)
                    return true
                else
                    return false
                end
            else
                return false
            end
        end

        if FastEquipLogic() then
            LogAnalysisTool.DoSendItemOperateReport(ItemOperateActionMethod.ItemOperateActionMethod_SingleClick)
            return true
        end

        local function LoadAmmoLogic(bRotated)
            local currentBulletNum, capacity = 0, 0
            if ItemOperaTool.CheckRunWarehouseLogic() then
                currentBulletNum, capacity = WeaponAssemblyTool.GetWeaponBulletNumAndCapacity(self.item:GetRawPropInfo())
            else
                local weaponFeature = self.item:GetFeature(EFeatureType.Weapon)
                if weaponFeature then
                    currentBulletNum = weaponFeature:GetWeaponCurAmmoNum()
                    capacity = weaponFeature:GetWeaponClipSize()
                end
            end
            local isEmpty =  currentBulletNum < math.round(capacity)
            if UAmmoDataManager.Get():IsMatchWeapon(self.item.id, item.id) then
                local loadAmmoResult = Module.Inventory:CommonOnDropForBullet(inOperation, tarLoc.ItemSlot, tarLoc.X, tarLoc.Y, tarLoc.SubIndex, bRotated)
                if loadAmmoResult ~= ELoadAmmoResult.NoLoadAmmoLogic then
                    if loadAmmoResult == ELoadAmmoResult.Success then
                        if isEmpty then
                            Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.SingleClickMove_EquipSucceed)
                        else
                            Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.SingleClickMove_ReplaceSucceed)
                        end
                    else
                        if isEmpty then
                            Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.SingleClickMove_EquipFail)
                        else
                            Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.SingleClickMove_ReplaceFail)
                        end
                    end
                    ItemOperaTool.DoSelectItem(nil, false)
                    return true
                end
            end
            return false
        end

        if LoadAmmoLogic(false) then
            LogAnalysisTool.DoSendItemOperateReport(ItemOperateActionMethod.ItemOperateActionMethod_SingleClick)
            return true
        end

    elseif self.item and self.item.InSlot and self.item.InSlot:IsEquipContainerSlot() and not self.item.InSlot:CheckItemFitSlot(item) then
        if item.InSlot == self.item.InSlot:GetContainerSlot() then
            return false
        end
        -- 单击物品转移，物品移动到已装备的容器上，尝试放入
        local containerSlot = self.item.InSlot:GetContainerSlot()
        if ItemOperaTool.ShouldCheckEquippedNotEmptyContainer(item, containerSlot) then
            if ItemOperaTool.DoPlaceContainerItems(item, containerSlot, false) then
                ItemOperaTool.DoSelectItem(nil, false)
                LogAnalysisTool.DoSendItemOperateReport(ItemOperateActionMethod.ItemOperateActionMethod_SingleClick)
                return true
            end
        else
            if ItemOperaTool.DoPlaceItem(item, containerSlot, false) then
                ItemOperaTool.DoSelectItem(nil, false)
                LogAnalysisTool.DoSendItemOperateReport(ItemOperateActionMethod.ItemOperateActionMethod_SingleClick)
                return true
            end
        end
    end
    ItemOperaTool.DoSelectItem(nil, false)
    return false
end

-- TODO 需要完善下右击规则
function IVWarehouseTemplate:OnClickedR(InGeometry, InMouseEvent)
    if self:IsNotAllowOperateInCollectionRoom() then
        return
    end
    if Facade.GameFlowManager:IsInOBMode() then
        return
    end
    if not self.item then
        return
    end
    if self:IsNotCompletedSearched() then
        return
    end

    local bItemHaveTeammateBindCondition, itemTeammateBindConditionReason = ItemOperaTool.CheckItemHaveTeammateBindCondition(self.item)
    if Module.CommonWidget:IsInMultiSelectedMode() then
    elseif Module.CommonWidget:IsAuctionMode() then
    elseif bItemHaveTeammateBindCondition then
        if itemTeammateBindConditionReason == ItemOperaTool.ETeammateBindConditionType.SelfTeammateBind then
            Module.CommonTips:ShowSimpleTip(Module.CommonWidget.Config.Loc.TeammateBindingItemTip)
        elseif itemTeammateBindConditionReason == ItemOperaTool.ETeammateBindConditionType.HaveTeammateBind then
            Module.CommonTips:ShowSimpleTip(Module.CommonWidget.Config.Loc.TeammateBindingContainerExistsTip)
        elseif itemTeammateBindConditionReason == ItemOperaTool.ETeammateBindConditionType.HaveTeammateBindAdapter then
            Module.CommonTips:ShowSimpleTip(Module.CommonWidget.Config.Loc.TeammateBindingWeaponExistsTip)
        end
    elseif self.item:IsInUsing() then
        Module.CommonTips:ShowSimpleTip(Module.Looting.Config.Loc.LootingFail_ItemBeingUsed)
    else
        local globalPosition = InMouseEvent:GetScreenSpacePosition()
        CommonWidgetConfig.Events.evtItemClickedRight:Invoke(self, globalPosition)
    end
end

function IVWarehouseTemplate:OnClickedMid(InGeometry, InMouseEvent)
    if ItemOperaTool.bIsInCollectionRoom then
        return
    end
    if self:IsNotAllowOperateInCollectionRoom() then
        return
    end
    if self:IsNotCompletedSearched() then
        return
    end
    -- 标记道具
    if Module.Looting:CheckItemMarkable(self.item) then
        Module.Looting:ToggleItemMarkedByLooting(self.item)
    end -- CommonIVInputLogic:ProcessMark() (默认鼠标中键，事件会被消耗，只能走这代偿)
    --BEGIN MODIFICATION @ VIRTUOS : 用手柄模拟鼠标中键按下事件，以执行原来双击事件
    if IsHD() then
        local isGamepad = self:_IsUsingGamepad()
        if isGamepad and Module.CommonWidget:IsInSwapingItem() == false then
            self:_OnDoubleClickLogic()

            Module.CommonWidget:BeginToFocusItemView(self.item)
        end
    end
    --END MODIFICATION    
end

function IVWarehouseTemplate:OnImmediateClicked()
    if self:IsNotAllowOperateInCollectionRoom() then
        return false
    end
    if self:IsNotCompletedSearched() then
        return false
    end
    if not ItemOperaTool.CheckIsLootingOrDepositoryOrSettlement() then
        return true
    end
    if self.item then
        log("IVWarehouseTemplate:OnImmediateClicked", self.item.name, self.item.gid)
    end

    local ret = true
    if IsHD() then
        ret = CommonWidgetConfig.Events.evtIVImmediateClicked:Invoke(self)

        if not ret then
            -- OnHandleMouseButtonUpEvent会先响应，会有一个选中的效果
            -- 这里需要特殊处理一下，在触发ImmediateClick逻辑的时候，取消选中
            self:_SetSelected(false)
        end
    end
    if ret then
        if ItemOperaTool.CheckRunSingleClickMoveItemLogic() then
            local item = ItemOperaTool.GetCurrentSingleClickMoveItem()
            if item then
                Module.CommonWidget:SetSingleClickItem(item)
            else
                Module.CommonWidget:SetSingleClickItem(self.item)
            end
        end
    end

    return ret
end

function IVWarehouseTemplate:_OnMouseEnterEvent()
    self:ShowOrHideCommonHoverBg(true)
    -- 租赁装备不需要hover后的状态
    if not self.item then return end

    if ItemOperaTool.CheckRunWarehouseLogic() then
        if self.item.InSlot and Server.ArmedForceServer:CheckIsRentalStatus() and Server.ArmedForceServer:IsRentalReferenceSlot(self.item.InSlot.SlotType) then
            return
        end
    end
    CommonWidgetConfig.Events.evtIVMouseEnter:Invoke(self)
end

function IVWarehouseTemplate:_OnMouseLeaveEvent()
    self:ShowOrHideCommonHoverBg(false)
    CommonWidgetConfig.Events.evtIVMouseLeave:Invoke(self)
end

-- function IVWarehouseTemplate:_OnShowHoverTips()
--     CommonWidgetConfig.Events.evtIVShowHoverTips:Invoke(self)
-- end

-- function IVWarehouseTemplate:_OnHideHoverTips()
--     CommonWidgetConfig.Events.evtIVHideHoverTips:Invoke(self)
-- end

function IVWarehouseTemplate:_OnHovered()
    self:ShowTip()
end

function IVWarehouseTemplate:_OnUnhovered()
    self:HideTip()
end

function IVWarehouseTemplate:ShowTip()
    CommonWidgetConfig.Events.evtIVShowHoverTips:Invoke(self)
end

function IVWarehouseTemplate:HideTip()
    CommonWidgetConfig.Events.evtIVHideHoverTips:Invoke(self)
end

function IVWarehouseTemplate:OnDoubleClicked()
    if self:IsNotAllowOperateInCollectionRoom() then
        return
    end
    if Facade.GameFlowManager:IsInOBMode() then
        return
    end

    if self.item then
        log("IVWarehouseTemplate:OnDoubleClicked", self.item.name, self.item.gid)
    end
    self.isDoubleClicked = true
    Module.CommonWidget:PopSingleClickItem()
    local currentTime = TimeUtil.GetCurrentTimeMillis()
    if self._lastEffectiveDoubleClickTime > 0 then
        local diffTime = currentTime - self._lastEffectiveDoubleClickTime
        if diffTime < 0.3 then
            log("IVWarehouseTemplate:OnDoubleClicked blocked because last diffTime =", diffTime)
            return
        end
    end
    self._lastEffectiveDoubleClickTime = currentTime
    Module.ItemDetail:PopCloseInfo()
    LogAnalysisTool.DoSendItemOperateReport(ItemOperateActionMethod.ItemOperateActionMethod_DoubleClick)
    self:_OnDoubleClickLogic()
end

function IVWarehouseTemplate:OnDragCancelled(InDragDropEvent, InOperation)
    if IsHD() then
        Module.Inventory:SetIsYMode(false)
    end
    -- Module.Inventory.Config.Events.evtUpdateFirstRowBlankItemVisibility:Invoke(false)
    -- Module.Inventory.Config.Events.evtUpdateAllBlankItemVisibility:Invoke(false)
end

function IVWarehouseTemplate:OnDragDetected(inGeometry, inDragDropEvent, operation)
    
    if IsHD() and WidgetUtil.IsGamepad() then
        Module.Inventory:SetIsYMode(true)
        self:AddBlankNavTarget()
    end

    if self:IsNotCompletedSearched() then
        return
    end
    if Facade.GameFlowManager:IsInOBMode() then
        return
    end
    Module.CommonWidget:PopSingleClickItem()
    if not self.item then
        return
    end
    if self.item and self.item.InSlot and self.item.InSlot.SlotType then
        if not IsHD() and ItemOperaTool.CheckRunWarehouseLogic() then
            -- 为防止滚动仓库左侧菜单时误触安全箱和门禁卡包，_CheckInteractive开放了安全箱和门禁卡包的拖拽，但这里需要阻断
            local SlotsNotAllowOperate = Module.Inventory.Config.SlotsNotAllowDragForMobile
            if SlotsNotAllowOperate[self.item.InSlot.SlotType] then
                return
            end
        end
    end
    LogAnalysisTool.DoSendItemOperateReport(ItemOperateActionMethod.ItemOperateActionMethod_Drag)
    self:SetNormalState(true)

    local bItemMultiSelected = Module.CommonWidget:CheckItemMultiSelected(self.item)
    if Module.CommonWidget:IsInMultiSelectedMode() and not bItemMultiSelected then
        return nil
    end

    if not ItemOperaTool.CheckRunWarehouseLogic() then
        if self.item:GetFeature(EFeatureType.Key) and self.item.InSlot and self.item.InSlot.SlotType == ESlotType.KeyChainContainer then
            Module.CommonTips:ShowSimpleTip(Module.Looting.Config.Loc.KeyChainNotAllowedToMoveOutTip)
            return nil
        end
    end
    -- 不同于原有DragItemView只需要替换资产，现在的DragItemView会维护一个动态列表
    -- 一期先改为每次先销毁DragItemView，后续检查DragItemView的代码，确认能否在其内部维护动态列表
    -- she3分支改
    Module.CommonWidget:ClearDragItemView()
    local previewItem = Module.CommonWidget:GetOrCreateDragItemView()
    Module.CommonWidget:SetCurDragingItemView(previewItem)
    self._bDraging = true
    local geometry = self:GetCachedGeometry()
    local viewSize = geometry:GetLocalSize()
    --- 拖拽的物品与原物品保持同样大小
    -- viewSize.X = viewSize.X * IVWarehouseTemplate.TRIGGER_DRAG_SCALE.X
    -- viewSize.Y = viewSize.Y * IVWarehouseTemplate.TRIGGER_DRAG_SCALE.Y
    if Module.CommonWidget:IsInMultiSelectedMode() then
        previewItem:SetMultipleItems(#Module.CommonWidget:GetSelectedItems(), viewSize.X, viewSize.Y)
    else
        previewItem:SetItem(self.item,viewSize.X,viewSize.Y,operation)
    end
    operation.WidgetReference = self
    operation.DefaultDragVisual = previewItem

    loginfo("TestNTBug-DragItemgid:" .. self.item.gid .. " self:", self)

    -- self:EnableComponent(EComp.TopLeftNewTag,false)

    --- 鼠标光标固定在左上角在旋转时效果不好，先不做这个功能
    -- operation.Offset = FVector2D(-1, -1) + FVector2D(self.item.length / ((self.item.length - 1) /2 + 3) * 2, self.item.width)
    -- operation.Pivot = EDragPivot.TopLeft

    ---被拖拽时降低一些透明度
    local iconComponent = self:GetComponent(EComp.ItemIcon)
    iconComponent:SetRenderOpacity(CommonWidgetConfig.DRAG_ITEM_RENDER_OPACITY)
    ---被拖拽时只显示图标
    ComponentToVisibility = {}
    for compId, component in pairs(self._itemComponents) do
        if compId ~= EComp.ItemIcon then
            ComponentToVisibility[compId] = component:GetVisibility() ~= ESlateVisibility.Collapsed
            component:EnableComponent(false)
        end
    end
    
    if IsHD() and WidgetUtil.IsGamepad() then
        local viewPanel = previewItem:Wnd("CanvasPanel_340",UIWidgetBase)
        -- local viewPanel = previewItem:Wnd("ItemRoot",UIWidgetBase)
        local weakUIIns, instanceID = Facade.UIManager:AddSubUI(previewItem, UIName2ID.CommonKeyIconTips, viewPanel)

        -- 之后封装进CommonKeyIconTips里面
        local TipsUI = getfromweak(weakUIIns)
        TipsUI.Slot:SetZOrder(6)
        local TipsPanel = TipsUI:Wnd("DFVerticalBox_44",UIWidgetBase)
        local anchor = FAnchors()
        anchor.Minimum = FVector2D(1.0, 0.5)
        anchor.Maximum = FVector2D(1.0, 0.5)
        TipsUI.Slot:SetAnchors(anchor)

        local margin = FMargin()
        margin.Left = 0
        margin.Right = 0
        margin.Top = 0
        margin.Bottom = 0
        TipsUI.Slot:SetOffsets(margin)
        TipsUI.Slot:SetAutoSize(true)
        TipsUI.Slot:SetAlignment(FVector2D(0, 0.5))

        -- 模拟焦点描边
        -- local FocusContent = previewItem:Wnd("FocusContent",UIWidgetBase)
        -- FocusContent:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        -- self:SetMultiKeysTipOnDrag(TipsUI)
        -- 放下按钮
        if not WidgetUtil.IsStopSimulatedMouseDrag() then
            weakUIIns, instanceID = Facade.UIManager:AddSubUI(previewItem, UIName2ID.ItemDetailCommonKeyIconTipsItem, TipsPanel)
            local subUI = getfromweak(weakUIIns)
            if subUI then
                -- subUI:Collapsed()
                local locText = CommonWidgetConfig.Loc.DropItemStr
                -- local locText = "[非本地化]放下"
                subUI:SetData("SimulateDropItem_Gamepad",locText)
                subUI:Visible()
            end
        end

        if self.item.length ~= self.item.width then
        -- 长宽不一致时，添加旋转按钮
            weakUIIns, instanceID = Facade.UIManager:AddSubUI(previewItem, UIName2ID.ItemDetailCommonKeyIconTipsItem, TipsPanel)
            local subUI = getfromweak(weakUIIns)
            if subUI then
                -- subUI:Collapsed()
                local locText = CommonWidgetConfig.Loc.RotateItemStr
                -- local locText = "[非本地化]旋转"
                subUI:SetData("RotateItemOnDrag_Gamepad",locText)
                subUI:Visible()
            end
        end
        InventoryNavManager.SetCurrentDragItem(self.item)
        loginfo("---InventoryNavManager:Set Start Item as: ", self.item.name,"---")

    end     

    LootingAudioLogic.PlayDragAudio(self.item.id)

    return operation
end


function IVWarehouseTemplate:OnDragEnd()
    if IsHD() then
        Module.Inventory:SetIsYMode(false)
    end
    ---结束拖拽时恢复透明度
    if not self._bDraging then
        return
    end
    self._bDraging = false
    local iconComponent = self:GetItemIconComponent()
    iconComponent:SetRenderOpacity(1)
    -- loginfo("TestNTBug-DropItemgid:" .. self.item.gid .. " self:", self)
    for compId, component in pairs(self._itemComponents) do
        if compId ~= EComp.ItemIcon then
            if ComponentToVisibility[compId] then
                component:EnableComponent(true)
            end
        end
    end
end

function IVWarehouseTemplate:OnLongPressed()
    if self.bHandleDrag  and (not Module.CommonWidget:IsAuctionMode()) then
        -- 只有HandleDrag才需要触发DragState
        self:SetCppValue("DragMode", EViewDragMode.Normal)
        self:SetCppValue("MaxDragPointSampleNum", 0)
        self:SetTriggerDragState()
        self.isLongPress = true
        InventoryEvtLogic.SetSelected(self, nil, false, nil, nil, true)
    end
end

-- 手柄Y模式专用：立即调用长按事件，使ULuaUIBaseView:ValidateDragLogic_SamplePoint()在下一帧
-- 走到ExecuteDragLogic()中，激活拖拽模式
function IVWarehouseTemplate:SimLongPressedIMM()
    if not IsHD() then
        return
    end

    if self.bHandleDrag then
        -- 只有HandleDrag才需要触发DragState
        self:SetCppValue("DragMode", EViewDragMode.Normal)
        self:SetCppValue("MaxDragPointSampleNum", 0)
        self:SetSkipDragStartDistanceCheck(true)
        self:SetTriggerDragState()
        self.isLongPress = true
        InventoryEvtLogic.SetSelected(self, nil, false, nil, nil, true)
    end
end

-- function IVWarehouseTemplate:OnNativeOnTouchMoved(inGeometry, inGestureEvent)
--     self:SetNormalState(false)
-- end

function IVWarehouseTemplate:OnNativeOnTouchEnded(inGeometry, inGestureEvent)
    self:SetNormalState(true)
    -- self:_SetSelected(false)
   self:MouseUpSetSelected()
end

function IVWarehouseTemplate:OnNativeOnTouchStarted(inGeometry, inGestureEvent)
    -- 开始按下时需要显示选中框来加强交互
    -- if self:_CheckIsShowSelectedCompByClicked() then
    --     self:_SetSelected(true)
    -- end
end

---------------------------Touch改Mouse--------------------------------------
function IVWarehouseTemplate:OnNativeOnMouseButtonUp(inGeometry, inGestureEvent)
    if UGPInputHelper.IsTouchOrLMBEvent(inGestureEvent) then
        self:SetNormalState(true)
        self:MouseUpSetSelected()
    end
end

-- function IVWarehouseTemplate:OnNativeOnMouseButtonDown(inGeometry, inGestureEvent)
--     if UGPInputHelper.IsTouchOrLMBEvent(inGestureEvent) then
--         if self:_CheckIsShowSelectedCompByClicked() then
--             self:_SetSelected(true)
--         end
--     end
-- end
---------------------------Touch改Mouse--------------------------------------
--endregion
--==================================================

-----------------------------------------------------------------------
--region Override

---@param item ItemBase
function IVWarehouseTemplate:InitItem(item)
    self._lastEffectiveDoubleClickTime = 0
    if self.item ~= item then
        self._refreshTimestamp = -1

        if item then
            Module.CommonWidget:RegisterItemView(self)
        else
            Module.CommonWidget:UnregisterItemView(self)
        end
    end

    -- 仓库的枪械动态图标在没有下载资源时用黑皮图标
    self:UseOriginalSkin()
    IVTemplateBase.InitItem(self, item)

    local bBullet = item and item:IsBullet() and not item:IsEquipped()
    if bBullet and not self._bListeningItemMoveEvt then
        if self:IsInShowState() and not self._bListeningItemMoveEvt then
            self._bListeningItemMoveEvt = true
            self:AddLuaEvent(Server.InventoryServer.Events.evtItemMove, self._OnItemMoved, self)
            self:AddLuaEvent(Server.LootingServer.Events.evtLootingItemMove, self._OnItemMoved, self)
        end
    elseif not bBullet and self._bListeningItemMoveEvt then
        self._bListeningItemMoveEvt = false
        self:RemoveLuaEvent(Server.InventoryServer.Events.evtItemMove)
        self:RemoveLuaEvent(Server.LootingServer.Events.evtLootingItemMove)
    end
end

---@param item ItemBase
function IVWarehouseTemplate:SetSelected(item, bSelected)
    local bSelfSelected = false
    if item and self.item == item and bSelected then
        bSelfSelected = true
    end

    -- IVTemplateBase.SetSelected(self, bSelfSelected)

    self:_SetSelected(bSelfSelected)
end

--endregion
-----------------------------------------------------------------------

--==================================================
--region Public API

function IVWarehouseTemplate:CheckShouldRefresh()
    if not self.item then return false end

    local bRunWarehouseLogic = ItemOperaTool.CheckRunWarehouseLogic()
    if bRunWarehouseLogic then
        return true
    else
        local result = self.item.modifyTimeStamp > self._refreshTimestamp
        if not result then
            result = Module.CommonWidget:IsSwitchWeightMode() -- 判断是否走负重模式切换
        end
        if not result then
            if self.item and self.item.InSlot and self.item.InSlot.SlotType == ESlotType.SafeBox then
                if Module.Looting:GetCachedSafeBoxSkinID() == 0 then
                    local safeBoxSkinID = Module.Looting:GetSafeBoxSkinID()
                    if safeBoxSkinID ~= 0 then
                        Module.Looting:SetCachedSafeBoxSkinID(safeBoxSkinID)
                        result = true
                    end
                end
            end
        end
        return result
    end
end

function IVWarehouseTemplate:RefreshView(bForce)
    bForce = setdefault(bForce, false)
    -- 通常不会走进来   
    -- 如果触发TriggerDrag的时候关闭面板，需要重置状态
    if self._bInTriggerDragState then
        self:SetNormalState(true)
    end

    -- -- 先检查SearchState
    -- local bRunWarehouseLogic = ItemOperaTool.CheckRunWarehouseLogic()
    -- if not bRunWarehouseLogic or ItemOperaTool.bInSettlement then
    --     -- 因为ItemView入池的关系，结算也需要检查SearchState，避免复用导致的UI显示问题
    --     if self:CheckSearchState() then
    --         return
    --     end
    -- end

    if not bForce and not self:CheckShouldRefresh() then
        if not ItemOperaTool.CheckRunWarehouseLogic() and self._viewSearchState == EViewSearchState.None then
            self:_CheckItemMarked()
            self:_CheckIVExposureComponent(true)
        end
        return
    end
    self._refreshTimestamp = self.item.modifyTimeStamp
    self._bDraging = false
    self._bInPermanentHighLight = false

    self:_InitialSetupComponents()
    IVTemplateBase.RefreshView(self)
end

function IVWarehouseTemplate:SetSlotParent(slotParent)
    self._slotParent = slotParent

    self:SyncSlotParentInteractive()
end

function IVWarehouseTemplate:SetOrRecoverType(bSet)
    local slot
    if bSet then
        slot = UWidgetLayoutLibrary.SlotAsCanvasSlot(self._wtBottomRightIconText)
        slot:SetPosition(FVector2D(-8, -2))
        self._wtItemBg:Settype(3)
    else
        slot = UWidgetLayoutLibrary.SlotAsCanvasSlot(self._wtBottomRightIconText)
        slot:SetPosition(FVector2D(-4, 0))
        self._wtItemBg:Settype(0)
    end
end

function IVWarehouseTemplate:HighlightItem()
    -- 出于未搜索/搜索中状态的道具，不高亮
    if self._viewSearchState ~= EViewSearchState.None then
        return
    end

    -- 局内物品进入保险箱，会有一个进入保险箱的动效
    local lastUpdateReason = self.item and self.item:GetLastModifyReason() or EItemInfoUpdatedReason.None
    self:CancelDelayDisableCompTimer(EComp.PutInSafebox)
    if not ItemOperaTool.CheckRunWarehouseLogic()  and (lastUpdateReason == EItemInfoUpdatedReason.Location or lastUpdateReason == EItemInfoUpdatedReason.SearchState) then
        local bSafeboxHighlight = self.item and self.item.InSlot and (self.item.InSlot.SlotType == ESlotType.SafeBoxContainer
        or self.item.InSlot.SlotType == ESlotType.KeyChainContainer)
        if bSafeboxHighlight then
            ---@type IVPutInSafeboxComponent
            local putInSafeboxComponent = self:FindOrAdd(EComp.PutInSafebox, UIName2ID.IVPutInSafeboxComponent, EIVSlotPos.MaskLayer, EIVCompOrder.High, true)

            putInSafeboxComponent:EnableComponent(true)
            self:SetDelayDisableCompTimer(EComp.PutInSafebox, putInSafeboxComponent:PlayHighlightAnim(), true)

            return
        else
            self:EnableComponent(EComp.PutInSafebox, false, true)
        end
    else
        self:EnableComponent(EComp.PutInSafebox, false, true)
    end

    local itemHighlightComponent = self:GetCommonItemHighlight(true)
    itemHighlightComponent:EnableComponent(true)
    local animTime = itemHighlightComponent:PlayItemHighlight(true)
    animTime = animTime or 0.2
    self:SetDelayDisableCompTimer(EComp.ItemHighlight, animTime, true)
end

-- 武器选择功能好像已经无了，功能不用
function IVWarehouseTemplate:PlayClick2ExchangeAnim()
    ---@type CommonItemHighlight
    local itemHighlightComponent = self:GetCommonItemHighlight(true)

	itemHighlightComponent:EnableComponent(true)
	itemHighlightComponent:PlayClick2ExchangeAnim()
end

function IVWarehouseTemplate:SetPermanentHighLight(IsVisible)
    self:ToggleHighlightComponent(IsVisible, true)
    self._bInPermanentHighLight = IsVisible
end

function IVWarehouseTemplate:IsInPermanentHighLight()
    return self._bInPermanentHighLight
end

--- 控制高亮组件的显隐与颜色
---@param IsVisible boolean 是否显示
---@param IsValid boolean 绿色还是红色
---@param IsDouble boolean 是否是双重高亮
function IVWarehouseTemplate:ToggleHighlightComponent(IsVisible, IsValid, IsDouble)
    if self._viewSearchState ~= EViewSearchState.None then
        return
    end
    if IsVisible then
        ---@type CommonItemHighlight
        local itemHighlightComponent = self:GetCommonItemHighlight(true)
        itemHighlightComponent:EnableComponent(true)
        if IsValid then
            if IsDouble then
                itemHighlightComponent:SetDoubleValid()
            else
                itemHighlightComponent:SetCommonValid()
            end
        else
            if IsDouble then
                itemHighlightComponent:SetDoubleInvalid()
            else
                itemHighlightComponent:SetCommonInvalid()
            end
        end
        --- 高亮时隐藏品质色，避免混合
        self:EnableComponent(EComp.ItemQuality, false)
    else
        self:EnableComponent(EComp.ItemHighlight, false, true)
        --- 恢复品质色
        self:EnableComponent(EComp.ItemQuality, true)
        self:RefreshComponent(EComp.ItemQuality)
    end
end

function IVWarehouseTemplate:IsSetInValidHighLight()
    ---@type CommonItemHighlight
    local itemHighlightComponent = self:GetComponent(EComp.ItemHighlight)
    if itemHighlightComponent then
        return itemHighlightComponent:IsSetInValid()
    end
    return false
end

function IVWarehouseTemplate:_CheckNeedPermanentHighLight()
    local curDragDropInfo = Module.CommonWidget:GetCurDragDropInfo()
    if curDragDropInfo and curDragDropInfo.item then
        self:CheckNeedPermanentHighLight(curDragDropInfo.item, true)
    end
end

function IVWarehouseTemplate:CheckNeedPermanentHighLight(dragItem, bDragStart)
    self:CheckWeaponAmmoMatchHighlight(dragItem, bDragStart)
    self:CheckWeaponAdapterMatchHighlight(dragItem, bDragStart)
    self:CheckItemCanBePutInEquippedContainer(dragItem, bDragStart)
end

---@param ammoItem ItemBase
function IVWarehouseTemplate:CheckWeaponAmmoMatchHighlight(ammoItem, bDragStart)
    if not self.item or not ammoItem then return end

    local weaponId = self.item and self.item.id or 0
    local bWeaponAmmoMatch = ammoMgr:IsMatchWeapon(weaponId, ammoItem.id)
    if bWeaponAmmoMatch then
        if bDragStart then
            -- 已经满容量的子弹不再显示高亮
            local currentBulletNum, capacity = 0, 0
            if ItemOperaTool.CheckRunWarehouseLogic() then
                currentBulletNum, capacity = WeaponAssemblyTool.GetWeaponBulletNumAndCapacity(self.item:GetRawPropInfo())
            else
                local weaponFeature = self.item:GetFeature(EFeatureType.Weapon)
                if weaponFeature then
                    currentBulletNum = weaponFeature:GetWeaponCurAmmoNum()
                    capacity = weaponFeature:GetWeaponClipSize()
                end
            end
            if currentBulletNum == capacity then
                local dragDropInfo = Module.CommonWidget:GetCurDragDropInfo()
                if not dragDropInfo or not dragDropInfo.bSimulation then
                    self:SetPermanentHighLight(false)
                    return
                end
            end
            -- 队友绑定的
            local bMyItemHaveTeammateBindCondition, myItemTeammateBindConditionReason = ItemOperaTool.CheckItemHaveTeammateBindCondition(self.item)
            local bAmmoItemHaveTeammateBindCondition, ammoItemTeammateBindConditionReason = ItemOperaTool.CheckItemHaveTeammateBindCondition(ammoItem)
            local bVipExpired = self.item.InSlot and self.item.InSlot:CheckVipContainerIsExpired()
            if bMyItemHaveTeammateBindCondition or bAmmoItemHaveTeammateBindCondition or bVipExpired then
                self:SetPermanentHighLight(false)
            else
                self:SetPermanentHighLight(true)
            end
        else
            self:SetPermanentHighLight(false)
        end
    else
        -- self:EnableComponent(EComp.ItemHighlight, false)
    end
end

---@param adapterItem ItemBase
function IVWarehouseTemplate:CheckWeaponAdapterMatchHighlight(adapterItem, bDragStart)
    if not self.item or not adapterItem then return end
    if self.item.itemMainType ~= EItemType.Receiver then return end
    if adapterItem.itemMainType ~= EItemType.Adapter then return end

    local bMatch = Module.FastEquip:CheckFastEquip(adapterItem, self.item)
    -- 租赁道具不显示高亮
    local isRentalItem = ItemOperaTool.CheckRunWarehouseLogic() and Server.ArmedForceServer:CheckIsRentalStatus() and self.item.InSlot and Server.ArmedForceServer:IsRentalReferenceSlot(self.item.InSlot.SlotType)
    if bMatch and not isRentalItem then
        if bDragStart then
            --- 如果目标武器对应槽位已经装备了物品，那么不再高亮
            if not WeaponAssemblyTool.CheckWeaponHasAvailableEmptyAdapterSocket(self.item:GetRawDescObj(), adapterItem.id) then
                local dragDropInfo = Module.CommonWidget:GetCurDragDropInfo()
                if not dragDropInfo or not dragDropInfo.bSimulation then
                    self:SetPermanentHighLight(false)
                    return
                end
            end
            -- 队友绑定的
            local bMyItemHaveTeammateBindCondition, myItemTeammateBindConditionReason = ItemOperaTool.CheckItemHaveTeammateBindCondition(self.item)
            local bAdapterItemHaveTeammateBindCondition, adapterItemTeammateBindConditionReason = ItemOperaTool.CheckItemHaveTeammateBindCondition(adapterItem)
            -- 当前所在扩容箱已经过期
            local bVipExpired = self.item.InSlot and self.item.InSlot:CheckVipContainerIsExpired()
            if bMyItemHaveTeammateBindCondition or bAdapterItemHaveTeammateBindCondition or bVipExpired then
                self:SetPermanentHighLight(false)
            else
                self:SetPermanentHighLight(true)
            end
        else
            self:SetPermanentHighLight(false)
        end
    else

    end
end

---@param dragItem ItemBase
function IVWarehouseTemplate:CheckItemCanBePutInEquippedContainer(dragItem, bDragStart)
    if not self.item or not dragItem then return end
    if not self.item:IsEquipped() then return end -- 检查是否已装备
    local slot = self.item.InSlot
    if slot == nil or not slot:IsEquipContainerSlot() then return end  -- 检查是否是可装备容器类
    local dragDropInfo = Module.CommonWidget:GetCurDragDropInfo()
    if bDragStart and dragDropInfo and slot and slot:GetEquipItem() then
        local containerSlot = Server.InventoryServer:GetSlot(slot:GetContainerSlotType(), slot:GetSlotGroup())
        if dragDropInfo.item.InSlot == containerSlot then
            --- 如果拖拽物已经在被检查的物品对应的容器槽位中，那么不高亮
            self:SetPermanentHighLight(false)
            return
        end
        local itemFeature = self.item:GetFeature(EFeatureType.Equipment)
        local dragItemFeature = dragItem:GetFeature(EFeatureType.Equipment)
        if itemFeature and dragItemFeature then
            if itemFeature:IsBag() and dragItemFeature:IsBag() then
                --- 如果拖拽物和被检查的物品同为背包，那么不高亮
                self:SetPermanentHighLight(false)
                return
            end
            if itemFeature:IsChestHanging() and dragItemFeature:IsChestHanging() then
                --- 如果拖拽物和被检查的物品同为胸挂，那么不高亮
                self:SetPermanentHighLight(false)
                return
            end
        elseif itemFeature:IsKeyChain() or itemFeature:IsSafeBox() then
            --- 如果安全箱和钥匙包过期，则不高亮
            if itemFeature:GetExpiredStatus() then
                self:SetPermanentHighLight(false)
                return
            end
        end
        local refContainerSlotType = slot:GetContainerSlotType()
        local refContainerSlot = Server.InventoryServer:GetSlot(refContainerSlotType, slot:GetSlotGroup())
        local bValid = table.contains(dragDropInfo.allAvailableSlots, refContainerSlot)
        if bValid then
            self:SetPermanentHighLight(true)
        else
            -- self:SetPermanentHighLight(true, false) --- fixbug. 常驻高亮不考虑红色，要么不高亮，要么高亮绿色
            self:SetPermanentHighLight(false)
        end
    else
        self:SetPermanentHighLight(false)
    end
end

function IVWarehouseTemplate:PlayDropDownAnim()
    if self.item then
        ---@type IVQualityComponent
        local qualityComponent = self:GetComponent(EComp.ItemQuality)
        if qualityComponent then
            qualityComponent:PlayQualityAnim()
        end

        ---@type IVMainIconComponent
        local iconComponent = self:GetComponent(EComp.ItemIcon)
        if iconComponent then
            iconComponent:PlayLootAnim()
        end
    end
end

-- 满足放入DIY展位里的藏品高亮逻辑
function IVWarehouseTemplate:CheckItemCanBePutInDIYCabinet(cabinetInfo)
    if not cabinetInfo then
        self:SetPermanentHighLight(false)
        return
    end
    local bCanPutInCabinet = Module.CollectionRoom:CheckCabinetCanPutInItem(self.item, cabinetInfo)
    if bCanPutInCabinet then
        self:SetPermanentHighLight(true)
    else
        self:SetPermanentHighLight(false)
    end
end

-- 【新手引导】高亮单个道具(当前引导不需要该功能)
-- function IVWarehouseTemplate:GuideHighlightItem()
--     ---@type CommonItemHighlight
--     local itemHighlightComponent = self:GetCommonItemHighlight(true)

--     itemHighlightComponent:EnableComponent(true)
-- 	itemHighlightComponent:PlayGuideHighlight(true)
-- end

function IVWarehouseTemplate:StopHighlightItem()
    self:EnableComponent(EComp.ItemHighlight, false, true)
end

function IVWarehouseTemplate:FadeOut()
    local slotItemView = self.WBP_SlotItemView
    -- local bgComponent = self:GetComponent(EComp.ItemBg)
    -- slotItemView:PlayAnimation(slotItemView.Slot_Bg_out, 0, 1, EUMGSequencePlayMode.Forward, 0.1, false)
    slotItemView:PlayAnimation(slotItemView.Slot_Icon_out, 0, 1, EUMGSequencePlayMode.Forward, 1, true)
    slotItemView:PlayAnimation(slotItemView.Slot_Border_out, 0, 1, EUMGSequencePlayMode.Forward, 1, true)
    slotItemView:PlayAnimation(slotItemView.Slot_L1_out, 0, 1, EUMGSequencePlayMode.Forward, 1, true)
    slotItemView:PlayAnimation(slotItemView.Slot_L0_out, 0, 1, EUMGSequencePlayMode.Forward, 1, true)
    slotItemView:PlayAnimation(slotItemView.Slot_R1_out, 0, 1, EUMGSequencePlayMode.Forward, 1, true)
    slotItemView:PlayAnimation(slotItemView.Slot_R0_out, 0, 1, EUMGSequencePlayMode.Forward, 1, true)
    slotItemView:PlayAnimation(slotItemView.Slot_Mask_out, 0, 1, EUMGSequencePlayMode.Forward, 1, true)
end

function IVWarehouseTemplate:SetShouldShowDoubleClickHint(bShouldShow)
    self._bShouldShowDoubleClickHint = bShouldShow
end

function IVWarehouseTemplate:CheckSearchState(forceState, bOnlyOperateSearchAnimComp)
    bOnlyOperateSearchAnimComp = setdefault(bOnlyOperateSearchAnimComp , false)
    local searchState = false
    if forceState then
        searchState = forceState
    else
        searchState = self.item:GetSearchState()
    end

    if self._viewSearchState ~= searchState then
        loginfo(string.format("IVWarehouseTemplate:CheckSearchState item=(name=%s gid=%d) search state from %d to %d", self.item.name, self.item.instanceId, self._viewSearchState, searchState))
    end

    local bSearchDone = (self._viewSearchState == EViewSearchState.Searching
            or self._viewSearchState == EViewSearchState.SearchingByTeammate) and searchState == EViewSearchState.None
    self._viewSearchState = searchState

    if searchState ~= EViewSearchState.None then
        if not bOnlyOperateSearchAnimComp then
            for id, component in pairs(self._itemComponents) do
                if id ~= EComp.SearchAnim then
                    self:EnableComponent(id, false)
                end
            end
        end

        ---@type IVSearchAnimComponent
        local searchAnimComp = self:FindOrAdd(EComp.SearchAnim, UIName2ID.IVSearchAnimComponent, EIVSlotPos.Extra, EIVCompOrder.SuperHigh, true)
        searchAnimComp:EnableComponent(true)
        searchAnimComp:SetWaitingStatus()

        if searchState == EViewSearchState.Searching or searchState == EViewSearchState.SearchingByTeammate then
            -- 开始搜索
            searchAnimComp:SetSearchingStatus()
            searchAnimComp:PlaySerachAnim()
            Module.Looting:Play_LootItemSearchStart()
        end
    else
        for id, component in pairs(self._itemComponents) do
            if id == EComp.SearchAnim then
                self:EnableComponent(id, false, true)
            else
                if not bOnlyOperateSearchAnimComp then
                    self:EnableComponent(id, true)
                end
            end
        end

        if bSearchDone then
            -- 结束搜索
            self:PlaySearchDoneAnim()
        end
    end
    return self._viewSearchState == EViewSearchState.None
end

function IVWarehouseTemplate:IsNotCompletedSearched()
    return self._viewSearchState ~= EViewSearchState.None
end

function IVWarehouseTemplate:PlaySearchDoneAnim()
    self:RefreshView(true)
    -- self:CheckSearchState(EViewSearchState.None)

    ---@type IVQualityComponent
    local qualityComponent = self:GetComponent(EComp.ItemQuality)
    if qualityComponent then
        qualityComponent:PlaySearchDoneAnim()
    end

    ---@type IVMainIconComponent
    local mainIconComp = self:GetComponent(EComp.ItemIcon)
    if mainIconComp then
        mainIconComp:PlaySearchDoneAnim()
    end

    ---@type IVSearchAnimComponent
    local searchAnimComp = self:GetComponent(EComp.SearchAnim)
    if searchAnimComp then
        searchAnimComp:FinishSearchAnim()
    end

    -- 播放结束的2D音效
    Module.Looting:Play_LootItemSearchStop()
    Module.Looting:Play_LootItemShow(self.item.quality)

    -- self._wtItemIcon:RefreshComponent()
    -- self._wtItemIcon:PreloadForSearchAnim()
end

function IVWarehouseTemplate:SyncSlotParentInteractive()
    if self._slotParent then
        local interactiveConfig = self._slotParent:GetItemInteractive()
        if interactiveConfig then
            for interactiveKey, value in pairs(interactiveConfig) do
                self:SetCppValue(interactiveKey, value)
            end
        end
    end
end

function IVWarehouseTemplate:ChangeItemViewMode(itemviewMode)
    if itemviewMode ~= self.itemviewMode then
        self.itemviewMode = itemviewMode
        self:RegisterComponent(EComp.ItemIcon, self._wtItemIcon)
        self:RegisterComponent(EComp.ItemBg, self._wtItemBg)
        self:RegisterComponent(EComp.ItemQuality, self._wtItemQuality)
        self:RegisterComponent(EComp.BottomLeftIconText, self._wtBottomLeftIconText)
        self:RegisterComponent(EComp.BottomRightIconText, self._wtBottomRightIconText)
    end
end

-- 是否使用自定义的交互控制，默认为不使用
function IVWarehouseTemplate:UseCustomInteractivityControl(bUse)
    self._bUseCustomInteractivityControl = bUse

    if not bUse then
        self:_CheckInteractive()
    end
end

--endregion
--==================================================


--==================================================
--region Private API

function IVWarehouseTemplate:_InitialSetupComponents()
    local bRunWarehouseLogic = ItemOperaTool.CheckRunWarehouseLogic()

    if not bRunWarehouseLogic and not self:CheckSearchState() then
        return
    end

    self:EnableComponent(EComp.ItemQuality, true)
    self:EnableComponent(EComp.ItemIcon, true)
    -- 通用逻辑
    local bSelected = Module.CommonWidget:CheckItemSingleSelect(self.item)
    self:SetSelected(bSelected)

    self:StopHighlightItem()
    self:_CheckTopLeftTwoLineText()
    self:_CheckBottomLeftIconText()
    self:_CheckBottomLeftIconText2()
    self:_CheckBottomRightIconText()
    self:_CheckShowMask()
    self:_CheckInteractive()
    -- self:_CheckItemMissionNeeded()
    self:_CheckItemUseAnim()
    self:_CheckMouseCursor()
    self:_ShowCompareIcon()

    self:_ShowItemTips()

    -- 尽量拆分局内外的逻辑，减少局内的性能开销
    if bRunWarehouseLogic then
        self:_CheckBottomRightSellPrice()
        self:_CheckMultiMode()
        self:_CheckItemCanSell()
        self:_CheckTopRightIconText()
    else
        -- self:_CheckIVExposureComponent()
        self:_CheckItemMarked()
        self:_CheckItemMarkedByLooting()
    end
    self:_CheckNeedPermanentHighLight()
    self:_CheckScrollAndHighLight()
    -- 手游不需要Hover高亮
    if not IsHD() then
        self:ShowOrHideCommonHoverBg(false)
    end
end

function IVWarehouseTemplate:_CheckScrollAndHighLight()
    local itemToScroll, scrollBox = Module.CommonWidget:GetItemScrollCmd()
    if self.item and self.item == itemToScroll and isvalid(scrollBox) then
        scrollBox:EndInertialScrolling()
        scrollBox:ScrollWidgetIntoView(self, true, EDescendantScrollDestination.IntoView)
        local scrollEndHandle = scrollBox.OnScrollEnd:Add(function()
            self:HighlightItem()
            Module.CommonWidget:RemoveItemScrollCmd()
        end)
        Module.CommonWidget:SetScrollEndHandle(scrollEndHandle)
    end
end

function IVWarehouseTemplate:_InitDefaultInteractive()
    ULuaItemDragConfigUtil.SyncWarehouseIVInteractive(self, true)

    self:SyncSlotParentInteractive()
	-- self:SetCppValue("bHandleClick", true)
	-- self:SetCppValue("bHandleDoubleClick", true)
    -- if IsHD() then
    --     self:SetCppValue("bHandleClickR", true)
    -- else
    -- end
    -- self:SetCppValue("DragMode", Module.CommonWidget:GetDefaultDragMode())
	-- self:SetCppValue("bHandleDrag", true)


    -- local defaultDoubleClickTriggerDuration = DFMGlobalConst.GetDepositConstNumber("DoubleClickTriggerDuration", 0.25)
    -- local defaultLongPressTriggerDuration = DFMGlobalConst.GetDepositConstNumber("LongPressTriggerDuration", 0.25)
    -- self:SetCppValue("DoubleClickInterval", defaultDoubleClickTriggerDuration)
    -- self:SetCppValue("LongPressInterval", defaultLongPressTriggerDuration)

	-- self:SetCppValue("bIgnoreFirstSample", false)
    -- self:SetCppValue("bHandleLongPress", true)
    -- self:SetCppValue("bCancelDragWhenLongPressTrigger", false)

	-- self:_InitDragDropConfig()
end

-- function IVWarehouseTemplate:_InitDragDropConfig()
-- 	local slotType = ESlotType.None
--     if self.item and self.item.InSlot then
--         slotType = self.item.InSlot.SlotType
--     end
--     -- Module.CommonWidget:InitCommonDragConfig(self, slotType)
--     ULuaItemDragConfigUtil.SyncDragConfig2View(self)

--     self:SyncSlotParentInteractive()
-- end

function IVWarehouseTemplate:_CheckBottomRightIconText(bForceRefresh)
    if not self.item then
        return
    end

    local bShowPrice = Module.CommonWidget:GetIsItemShowPrice()
    if bShowPrice then
        self:EnableComponent(EComp.BottomRightIconText, false)
    else
        self:EnableComponent(EComp.BottomRightIconText, true)

        local itemFeature = self.item:GetFeature()
        local featureType = self.item:GetFeatureType()
        if Module.CommonWidget:GetIsItemShowWeight() then
            self._wtBottomRightIconText:SetStyle(CommonWidgetConfig.EIVIconTextStyle.Weight)
        elseif featureType == EFeatureType.Weapon then
            self._wtBottomRightIconText:SetStyle(CommonWidgetConfig.EIVIconTextStyle.WeaponAmmo)
        elseif featureType == EFeatureType.Equipment then
            ---@type EquipmentFeature
            local equipmentFeature = itemFeature
            if equipmentFeature:IsHelmet() or equipmentFeature:IsBreastPlate() then
                self._wtBottomRightIconText:SetStyle(CommonWidgetConfig.EIVIconTextStyle.Durability)
            -- elseif equipmentFeature:IsBreastPlate() then
            --     self._wtBottomRightIconText:SetStyle(CommonWidgetConfig.EIVIconTextStyle.Armor)
            elseif equipmentFeature:IsBag() or equipmentFeature:IsChestHanging() then
                self._wtBottomRightIconText:SetStyle(CommonWidgetConfig.EIVIconTextStyle.BagCapacityExtend)
            elseif equipmentFeature:IsSafeBox() or equipmentFeature:IsKeyChain() then
                self._wtBottomRightIconText:SetStyle(CommonWidgetConfig.EIVIconTextStyle.Countdown)
            else
                self._wtBottomRightIconText:SetStyle(CommonWidgetConfig.EIVIconTextStyle.None)
            end
        elseif featureType == EFeatureType.Health then
            ---@type HealthFeature
            local healthFeature = itemFeature
            if healthFeature and healthFeature:GetDurabilityMaxValue() > 0 then
                self._wtBottomRightIconText:SetStyle(CommonWidgetConfig.EIVIconTextStyle.UseNum)
            else
                self._wtBottomRightIconText:SetStyle(CommonWidgetConfig.EIVIconTextStyle.Stack)
            end
        else
            self._wtBottomRightIconText:SetStyle(CommonWidgetConfig.EIVIconTextStyle.Stack)
        end

        if bForceRefresh then
            self:RefreshComponent(EComp.BottomRightIconText)
        end
    end
end


function IVWarehouseTemplate:_CheckShowDoubleClickHint()
    self:EnableComponent(EComp.TipMask, false)
    self:CancelDelayDisableCompTimer(EComp.TipMask)
    if not self.bHandleDoubleClick or not self._bShouldShowDoubleClickHint then
        return false
    end

    if ItemOperaTool.CheckIsDIYCabinetPanel() then
        return false
    end

	if Module.CommonWidget:IsInMultiSelectedMode()
	or self._bShowDoubleClickEffectHint == false
	or Module.CommonWidget:GetIsItemShowDoubleClickHint() == false then
        return false
    end

    if not self.item
    or Module.CommonWidget:CheckItemSingleSelect(self.item)
    or Module.CommonWidget:CheckItemInGameSelect(self.item) then
        return false
    end

    local inSlot = self.item.InSlot
    if not inSlot then
        return false
    end

    if Module.CommonWidget:IsAuctionMode() then
        return false
    end

    local bRunWarehouseLogic = ItemOperaTool.CheckRunWarehouseLogic()
    local tipText
    if bRunWarehouseLogic then
        if self.item:IsInDepository() then
            local equipmentFeature = self.item:GetFeature(EFeatureType.Equipment)
            if self.item:IsEquipableItem() or (equipmentFeature and equipmentFeature:IsExtendItem()) then
                tipText = Module.Inventory.Config.Loc.DoubleClick2Equip
            else
                tipText = Module.Inventory.Config.Loc.DoubleClick2Carry
            end
        else
            if inSlot.SlotType == ESlotType.SafeBox
            or inSlot.SlotType == ESlotType.KeyChain then

            elseif self.item:IsEquipped() then
                tipText = Module.Inventory.Config.Loc.DoubleClick2UnEquip
            else
                tipText = Module.Inventory.Config.Loc.DoubleClick2BackToDepos
            end
        end
    else
        if self.item and self.item.InSlot then
            if self.item.InSlot:IsKeyContainerSlot() then
                return false
            end
            if self.item.InSlot.SlotType == ESlotType.KeyChain
                    or self.item.InSlot.SlotType == ESlotType.SafeBox then
                return false
            end
        end
        -- 局内仅Loot界面需要双击引导
        local curInteractorType = Server.LootingServer:GetCurrentSelectorData()
        if curInteractorType then
            -- Loot
            local slotGroup = inSlot:GetSlotGroup()
            if slotGroup == ESlotGroup.Player then
                tipText = Module.Inventory.Config.Loc.DoubleClick2Remove
            else
                tipText = Module.Inventory.Config.Loc.DoubleClick2PickUp
            end
        else
            -- 背包
        end
    end

    if tipText then
        local tipsComponent = self:FindOrAdd(EComp.TipMask, UIName2ID.IVTipMaskComponent, EIVSlotPos.MaskLayer, nil, true)
        self:EnableComponent(EComp.TipMask, true)
        -- 如果因为意外导致tipsComponent没创建出来，也就不需要下面的操作
        if tipsComponent then
            self:SetDelayDisableCompTimer(EComp.TipMask, tipsComponent:PlayTipOnce(tipText), true)
        end
        return true
    else
        return false
    end
end

function IVWarehouseTemplate:_CheckTopRightIconText()
    local slot = self.item and self.item.InSlot or nil
    if slot and (slot:IsEquipSafeBoxAndKeyChain() or slot:IsSOLMeleeWeaponSlot()) then
        ---@type IVTextIconComponent
        local component = self:FindOrAdd(EComp.TopRightIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.TopRight)
        component:SetStyle(CommonWidgetConfig.EIVIconTextStyle.InSafebox)
        self:EnableComponent(EComp.TopRightIconText, true)
    else
        self:EnableComponent(EComp.TopRightIconText, false)
    end
end

function IVWarehouseTemplate:_CheckTopRightWeaponBP()
    local weaponFeature = self.item and self.item:GetFeature(EFeatureType.Weapon) or nil

    -- if self:_CheckMultiModeEnabled() or not weaponFeature then
    if not weaponFeature then
        self:EnableComponent(EComp.TopRightWeaponBP, false)
        return
    end

    local skinid = Server.InventoryServer:GetWeaponSkinID(self.item.gid)
    -- 是否装备蓝图
    -- if skinid == 0 then
    --     -- local isHaveBp = WeaponHelperTool.GetWeaponSkinIDs(self.item.id)
    --     local isHaveBp = Server.CollectionServer:IsHaveBlueprint(self.item.id)
    --     if isHaveBp then
    --         -- 如果是位于配装界面则���显示默认底纹
    --         if self.itemviewMode == EIVItemViewMode.EquipmentSlotItemView then
    --             self:EnableComponent(EComp.TopRightWeaponBP, false)
    --             return
    --         end
    --         local weaponBPComponent = self:FindOrAdd(EComp.TopRightWeaponBP, UIName2ID.IVWeaponBPComponent, EIVSlotPos.TopRight, EIVCompOrder.Order1)
    --         weaponBPComponent:SetShowStyle(CommonWidgetConfig.EIVWeaponBPShowStyle.GainedBp)
    --         self:EnableComponent(EComp.TopRightWeaponBP, true)
    --     else
    --         self:EnableComponent(EComp.TopRightWeaponBP, false)
    --     end
    -- else
    --     -- local weaponPropInfo = self.item:GetRawPropInfo()
    --     -- local isWeaponBP = WeaponHelperTool.GetIsBlueprintSolutionFromPropinfo(weaponPropInfo)
    --     -- local weaponBPComponent = self:FindOrAdd(EComp.TopRightWeaponBP, UIName2ID.IVWeaponBPComponent, EIVSlotPos.TopRight, EIVCompOrder.Order1)
    --     -- if isWeaponBP then
    --     --     weaponBPComponent:SetWeaponSkinID(skinid)
    --     --     -- weaponBPComponent:SetShowStyle(CommonWidgetConfig.EIVWeaponBPShowStyle.NameAndShading)
    --     --     weaponBPComponent:SetShowStyle(CommonWidgetConfig.EIVWeaponBPShowStyle.EquippedBp)
    --     -- else
    --     --     weaponBPComponent:SetWeaponSkinID(skinid)
    --     --     -- weaponBPComponent:SetShowStyle(CommonWidgetConfig.EIVWeaponBPShowStyle.OnlyName)
    --     --     weaponBPComponent:SetShowStyle(CommonWidgetConfig.EIVWeaponBPShowStyle.GainedBp)
    --     -- end
    -- end
    if skinid ~= 0 then
        local weaponBPComponent = self:FindOrAdd(EComp.TopRightWeaponBP, UIName2ID.IVWeaponBPComponent, EIVSlotPos.TopRight)
        weaponBPComponent:SetWeaponSkinID(skinid)
        weaponBPComponent:SetShowStyle(CommonWidgetConfig.EIVWeaponBPShowStyle.EquippedBp)
	    -- self._wtItemView:SetPosOrder(EIVSlotPos.TopRight, 0)
        self:EnableComponent(EComp.TopRightWeaponBP, true)
    else
        self:EnableComponent(EComp.TopRightWeaponBP, false)

    end
end

function IVWarehouseTemplate:_CheckTopLeftItemName()
    local featureType = self.item and self.item:GetFeatureType() or EFeatureType.None
    if featureType == EFeatureType.Weapon or featureType == EFeatureType.Bullet then
        self:_CheckTopLeftTwoLineText()
    else
        self:_CheckTopLeftIconText()
    end
end

function IVWarehouseTemplate:_CheckTopLeftIconText()
    local featureType = self.item and self.item:GetFeatureType() or EFeatureType.None
    local slot = self.item and self.item.InSlot or nil
    local shortName = self.item and self.item.shortName or ""
    local skinid = self.item and Server.InventoryServer:GetWeaponSkinID(self.item.gid) or nil

    -- 装备槽位置的ItemView不需要显示名字
    local bShouldShow = true
    if slot then
        bShouldShow = not slot:IsEquipableSlot()
    end
    if not bShouldShow then
        self:EnableComponent(EComp.TopLeftIconText, false)
        return
    end

    if slot and slot.SlotType == ESlotType.SafeBox then
        ---@type IVTextIconComponent
        local component = self:FindOrAdd(EComp.TopLeftIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.TopLeft)
        component:SetStyle(CommonWidgetConfig.EIVIconTextStyle.IsEquippedSafebox)
        self:EnableComponent(EComp.TopLeftIconText, true)
    else
        self:EnableComponent(EComp.TopLeftIconText, false)
    end
end

function IVWarehouseTemplate:_CheckTopLeftTwoLineText()
    local slot = self.item and self.item.InSlot or nil
    local shortName = self.item and self.item.shortName or ""
    local skinid = self.item and Server.InventoryServer:GetWeaponSkinID(self.item.gid) or nil

    -- 只有装配槽的枪械和安全箱钥匙包不显示名字
    local bShouldShow = true
    if slot then
        -- bShouldShow = not slot:IsEquipableSlot()
        bShouldShow = not (slot:IsMainWeaponSlot() or slot:IsEquipSafeBoxAndKeyChain())
    end

    -- 由于时许问题可能导致先刷新出售状态下文本的显示再调用该方法，需要在该方法内进行出售状态的判断
    local bSellMultiMode = Module.CommonWidget:GetItemOperMode() == EItemOperaMode.SellMode
    if bShouldShow and not string.isempty(shortName) and not bSellMultiMode then
        local shortNameSec
        -- if skinid == 0 then
            shortNameSec = self.item and self.item.shortNameSec or ""
        -- else
        --     local itemInfo = ItemConfigTool.GetItemConfigById(skinid)
        --     shortName = itemInfo and itemInfo.shortName or ""
        --     shortNameSec = itemInfo and itemInfo.shortNameSec or ""
        -- end
        local component = self:FindOrAdd(EComp.TopLeftTwoLineText, UIName2ID.IVTwoLinesTitleComponent, EIVSlotPos.Top)
        component:SetTwoLineText(shortName, shortNameSec)
        self:EnableComponent(EComp.TopLeftTwoLineText, true)
    else
        self:EnableComponent(EComp.TopLeftTwoLineText, false)
    end
end

function IVWarehouseTemplate:_CheckBottomLeftIconText()
    -- 道具绑定
    local bShowWeight = Module.CommonWidget:GetIsItemShowWeight()
    local bInSellMode = Module.CommonWidget:GetItemOperMode() == EItemOperaMode.SellMode
    -- self._wtBottomLeftIconText:EnableComponent(not bShowWeight and not bInSellMode)
    self._wtBottomLeftIconText:EnableComponent(not bShowWeight)
end

function IVWarehouseTemplate:_CheckBottomLeftIconText2()
    ---@type WeaponFeature
    local weaponFeature = self.item and self.item:GetFeature(EFeatureType.Weapon) or nil
    local equipmentFeature = self.item and self.item:GetFeature(EFeatureType.Equipment) or nil
    -- local mysticalPropInfo = weaponFeature and self.item:GetRawDescObj() and self.item:GetRawDescObj():GetSkinInfo()
    if weaponFeature and weaponFeature:IsPoorWeapon() then
        -- 次级预设
        ---@type IVTextIconComponent
        local component = self:FindOrAdd(EComp.BottomLeftIconText2, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomLeft)
        component:SetStyle(CommonWidgetConfig.EIVIconTextStyle.Custom)
        self:EnableComponent(EComp.BottomLeftIconText2, true)

        local iconPath = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0013.Common_ItemProp_Icon_0013'"
        component:ShowIconOnly(iconPath)
    elseif self.item and self.item:IsBullet() then
        -- 子弹提示
        ---@type IVTextIconComponent
        local component = self:FindOrAdd(EComp.BottomLeftIconText2, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomLeft)
        component:SetStyle(CommonWidgetConfig.EIVIconTextStyle.BulletHint)
        self:EnableComponent(EComp.BottomLeftIconText2, true)
    -- elseif equipmentFeature and (equipmentFeature:IsHelmet() or equipmentFeature:IsBreastPlate())  then
    --     local component = self:FindOrAdd(EComp.BottomLeftIconText2, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomLeft)
    --     component:SetStyle(CommonWidgetConfig.EIVIconTextStyle.DamagedMark)
    --     self:EnableComponent(EComp.BottomLeftIconText2, true)
    -- elseif mysticalPropInfo then
    --     local component = self:FindOrAdd(EComp.BottomLeftIconText2, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomLeft)
    --     component:SetStyle(CommonWidgetConfig.EIVIconTextStyle.SkinRarity)
    --     self:EnableComponent(EComp.BottomLeftIconText2, true)
    else
        self:EnableComponent(EComp.BottomLeftIconText2, false)
    end
end

function IVWarehouseTemplate:_CheckIsDamagedItem()
    local equipmentFeature = self.item and self.item:GetFeature(EFeatureType.Equipment) or nil
    -- 如果是装备
    if equipmentFeature and (equipmentFeature:IsHelmet() or equipmentFeature:IsBreastPlate()) then
        return equipmentFeature.curDurability <= 0
    end
end

function IVWarehouseTemplate:_CheckBottomLeftIconText3()
    if self:_CheckMultiModeEnabled() then
        self:EnableComponent(EComp.BottomLeftIconText3, false)
        return
    end

    -- 使用次数
    local healthFeature = self.item and self.item:GetFeature(EFeatureType.Health) or nil
    if healthFeature then
        local maxDurability = healthFeature:GetDurabilityMaxValue()
        if maxDurability > 0 then
            ---@type IVTextIconComponent
            local component = self:FindOrAdd(EComp.BottomLeftIconText3, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomLeft)
            component:SetStyle(CommonWidgetConfig.EIVIconTextStyle.Custom)
            self:EnableComponent(EComp.BottomLeftIconText3, true)

            local curDurability = healthFeature:GetDurabilityCurValue()

            local txt = string.format("%d/%d", curDurability, maxDurability)
            component:ShowTextOnly(txt)
        else
            self:EnableComponent(EComp.BottomLeftIconText3, false)
        end
    else
        self:EnableComponent(EComp.BottomLeftIconText3, false)
    end
end

function IVWarehouseTemplate:_CheckBottomLeftIconText4()
    -- local bMark = self.item and Server.InventoryServer:CheckItemMarked(self.item.id) or false
    -- -- if not bMark or ItemOperaTool.CheckRunWarehouseLogic()
    -- -- or self.item.InSlot:GetSlotGroup() == ESlotGroup.Player then
    -- if not bMark then
    --     self:EnableComponent(EComp.BottomLeftIconText4, false)
    -- else
    --     ---@type IVTextIconComponent
    --     local component = self:FindOrAdd(EComp.BottomLeftIconText4, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomLeft, EIVCompOrder.Order4)
    --     self:EnableComponent(EComp.TopRightIconText, true)

    --     component:SetStyle(CommonWidgetConfig.EIVIconTextStyle.Custom)
    --     local iconPath = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0003.Common_ItemProp_Icon_0003'"
    --     component:ShowIconOnly(iconPath)
    -- end
end

function IVWarehouseTemplate:_CheckBottomRightSellPrice(bForceRefresh)
    -- 仓库批量出售下，不能选中的道具不用显示价格
    local bShowPrice = Module.CommonWidget:GetIsItemShowPrice()-- and self:_CheckMultiModeEnabled()
    if bShowPrice then
        local sellPriceComp = self:FindOrAdd(EComp.BottomRightSellPrice, UIName2ID.IVSellPriceComponent, EIVSlotPos.BottomRight)
        self:EnableComponent(EComp.BottomRightSellPrice, true)

        if bForceRefresh then
            self:RefreshComponent(EComp.BottomRightSellPrice)
        end
    else
        self:EnableComponent(EComp.BottomRightSellPrice, false)
    end
end

function IVWarehouseTemplate:_CheckItemUseAnim()
    if not self.item then return end

    if self.item:IsInUsing() then
        -- 使用中
        ---@type ItemViewUseAnim
        local component = self:FindOrAdd(EComp.UseAnim, UIName2ID.ItemViewUseAnim, EIVSlotPos.MaskLayer, EIVCompOrder.High)
        self:EnableComponent(EComp.UseAnim, true)
    else
        self:EnableComponent(EComp.UseAnim, false)
    end
end

function IVWarehouseTemplate:_CheckShowMask()
    local bShowMask = false
    local item = self.item
    local fGetShouldShowMask = nil
    local bHandleClick = true
    local bTeammateBind = false

    -- 如果是结算时的队友绑定道具
    if ItemOperaTool.bInSettlement and ItemOperaTool.CheckItemHaveTeammateBindCondition(self.item) then
        bShowMask = true
        bHandleClick = false
        bTeammateBind = true
    end

    if self._slotParent then
        fGetShouldShowMask = self._slotParent:GetCustomItemMaskLogic()
        if fGetShouldShowMask then
            bShowMask, bHandleClick = fGetShouldShowMask(item)
            bTeammateBind = false
        end
    end

    if not fGetShouldShowMask and not ItemOperaTool.CheckRunWarehouseLogic() then
        -- 如果没有自定义的Mask逻辑，走默认的Mask Logic
        -- local curTime = TimeUtil.GetCurrentTime()
        -- if item and item.aginBeginTime and item.aginDuration then
        --     local endTime = item.aginBeginTime + item.aginDuration
        --     if endTime > curTime then
        --         bShowMask = true
        --     end
        -- end

        -- 不可操作道具
        local inSlot = item and item.InSlot
        if inSlot and Module.Looting.Config.SlotsNotAllowOperate[inSlot.SlotType] then
            bShowMask = inSlot.SlotType ~= ESlotType.KeyChainContainer -- 局内卡包内门禁卡的遮罩在InvSlotView上不在ItemView上
            bShowMask = bShowMask and inSlot.SlotType ~= ESlotType.SafeBox -- 安全箱不再需要遮罩
            bShowMask = bShowMask and inSlot.SlotType ~= ESlotType.KeyChain -- 卡包本身不再需要遮罩
            bHandleClick = inSlot.SlotType == ESlotType.SafeBox or inSlot.SlotType == ESlotType.KeyChain
            bTeammateBind = false
        end
    end

    -- 近战武器道具
    if item and item.InSlot and item.InSlot.SlotType == ESlotType.MeleeWeapon
    and self.itemviewMode ~= CommonWidgetConfig.EIVItemViewMode.EquipmentSlotItemView
    and not Facade.GameFlowManager:CheckIsInFrontEnd() then
        bShowMask = true
        bHandleClick = false
    end

    -- 租赁道具
    if ItemOperaTool.CheckRunWarehouseLogic() then
        if item.InSlot and Server.ArmedForceServer:CheckIsRentalStatus() and not item.InSlot:GetSlotGroupIsTempRental() then
            if item and item.InSlot and (Server.ArmedForceServer:IsRentalReferenceSlot(item.InSlot.SlotType) or item.InSlot:IsSOLMeleeWeaponSlot()) then
                bShowMask = true
                bHandleClick = false
            end
        end
    end

    -- 卡包内的钥匙可点击
    if item and item.InSlot and item:GetFeature(EFeatureType.Key) and item.InSlot:IsKeyContainerSlot() then
        bHandleClick = true
    end

    -- 收藏室不可上架道具需showmask
    if ItemOperaTool.CheckIsDIYCabinetPanel() then
        local itemMainID = ItemHelperTool.GetMainTypeById(self.item.id)
        if itemMainID ~= 15 or self.item:CheckIsBind() then
            bShowMask = true
            bHandleClick = true
        else
            local bInWhiteList = false
            local itemIDString = ULuautils.GetUInt64String(self.item.id)
            local ItemIDBanExemptionPrefix = UCollectionRoomConstantWrap.GetConstantByName("ItemIDBanExemptionPrefix")
            local prefixArray = string.split(ItemIDBanExemptionPrefix, ",")
            for _, prefix in pairs(prefixArray) do
                if string.starts_with(itemIDString, prefix) then
                    bInWhiteList = true
                    break
                end
            end
            if not bInWhiteList then
                local itemIDBanPrefix = UCollectionRoomConstantWrap.GetConstantByName("ItemIDBanPrefix")
                prefixArray = string.split(itemIDBanPrefix, ",")
                for _, prefix in pairs(prefixArray) do
                    if string.starts_with(itemIDString, prefix) then
                        bShowMask = true
                        bHandleClick = true
                    end
                end
            end
        end
    end

    -- Timer.DelayCall(0.2, function ()
        if bShowMask then
            local greyMaskComp = nil
            if bTeammateBind then
                greyMaskComp = self:FindOrAdd(EComp.TeammateReturnMark, UIName2ID.IVReturn, EIVSlotPos.MaskLayer, EIVCompOrder.MaskLayerOrder)
            else
                greyMaskComp = self:FindOrAdd(EComp.GreyMask, UIName2ID.IVGreyMask, EIVSlotPos.MaskLayer, EIVCompOrder.GreyMaskOrder)
            end

            if not bHandleClick then
                greyMaskComp:SetDefaultVisibility(ESlateVisibility.Visible)
            else
                greyMaskComp:SetDefaultVisibility(ESlateVisibility.SelfHitTestInvisible)
            end

            self:EnableComponent(EComp.TeammateReturnMark, bTeammateBind)
            self:EnableComponent(EComp.GreyMask, not bTeammateBind)

            --BEGIN MODIFICATION @ VIRTUOS :
            if IsHD() then
                self:SetCppValue("bIsFocusable", false)
            end
            --END MODIFICATION
        else
            self:EnableComponent(EComp.TeammateReturnMark, false)
            self:EnableComponent(EComp.GreyMask, false)

            --BEGIN MODIFICATION @ VIRTUOS :
            if IsHD() then
                self:SetCppValue("bIsFocusable", true)
            end
            --END MODIFICATION
        end
    -- end,self)

end

function IVWarehouseTemplate:_CheckLockIcon()
    local inSlot = self.item and self.item.InSlot
    if inSlot and Module.Inventory.Config.SlotsNotAllowOperate[inSlot.SlotType]
    and inSlot:GetSlotGroup() == ESlotGroup.Player then
        local lockIconComponent = self:FindOrAdd(EComp.LockIcon, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomLeft)
        lockIconComponent:SetStyle(CommonWidgetConfig.EIVIconTextStyle.LockIcon)
        self:EnableComponent(EComp.LockIcon, true)
    else
        self:EnableComponent(EComp.LockIcon, false)
    end
end

function IVWarehouseTemplate:_CheckSettlement()
    if Module.Settlement:GetInSettlementing() then
        local slot = self.item and self.item.InSlot or nil
        if slot and not slot:IsDepositorySlot() then
            self:FindOrAdd(EComp.SettlementDurability, UIName2ID.ItemViewDurabilityNew, EIVSlotPos.MaskLayer)
            self:EnableComponent(EComp.SettlementDurability, true)
        else
            self:EnableComponent(EComp.SettlementDurability, false)
        end
    else
        self:EnableComponent(EComp.SettlementDurability, false)
    end
end

function IVWarehouseTemplate:_CheckMouseCursor()
    if not self.item then return end

    local inSlot = self.item.InSlot
    if not inSlot then return end
    if inSlot:IsSOLMeleeWeaponSlot() or Module.CommonWidget:IsAuctionMode() then
        self:SetCursor(EMouseCursor.Hand)
    end
    if inSlot:IsEquipContainerSlot() then
        local itemFeature = self.item:GetFeature()
        local featureType = self.item:GetFeatureType()
        if featureType == EFeatureType.Equipment then
            if itemFeature:IsSafeBox() or itemFeature:IsKeyChain() then
                self:SetCursor(EMouseCursor.Hand)
            end
        end
    end
    if self:IsNotAllowOperateInCollectionRoom() then
        self:SetCursor(EMouseCursor.Default)
    end
    if Facade.GameFlowManager:IsInOBMode() then
        self:SetCursor(EMouseCursor.Hand)
    end
end

function IVWarehouseTemplate:_ShowCompareIcon(slotType, bImprove, bHide)
    if self:IsInHideBeginState() or self:IsInHideState() then
        return
    end
    local bCloseItemDetail = Module.ItemDetail:GetItemDetailPanelType() == EFeatureType.None
    if self.item and self.item.InSlot and self.item.InSlot.SlotType == slotType and not bHide and self.item.InSlot:GetSlotGroup() == ESlotGroup.Player
     and not bCloseItemDetail then
        local compareComponent = self:FindOrAdd(EComp.GetMask, UIName2ID.IVComparedComponent, EIVSlotPos.MaskLayer, EIVCompOrder.HighestOrder)
        -- compareComponent:PlayComponentAnim()
        compareComponent:SetWidgetType(bImprove)
        self:EnableComponent(EComp.GetMask, true)
    else
        self:EnableComponent(EComp.GetMask, false)
    end
end

function IVWarehouseTemplate:_HideCompareIcon()
    self:EnableComponent(EComp.GetMask, false)
end

-- 发光底部
-- function IVWarehouseTemplate:_ShowBgCollection()
--     -- 曼德尔砖显示动效
--     if self.item and self.item:GetFeature(EFeatureType.Reward) and (self.item:GetFeature(EFeatureType.Reward):IsUndecipheredBrick()
--     or self.item:GetFeature(EFeatureType.Reward):IsDecipheredBrick() ) then

--         local collectionComponent = self:FindOrAdd(EComp.GiftBg, UIName2ID.IVCollectionComponent, EIVSlotPos.BgLayer, EIVCompOrder.Order1)
--         collectionComponent:PlayWidgetAnim(collectionComponent.WBP_Itemview_Collections_loop_real, 0)
--         self:EnableComponent(EComp.GiftBg, true)
--     else
--         self:EnableComponent(EComp.GiftBg, false)
--     end
-- end

function IVWarehouseTemplate:_CheckInteractive()
    if self._bUseCustomInteractivityControl then
        return
    end
    local bInMultipleMode = Module.CommonWidget:IsInMultiSelectedMode()

    -- 引导设立的标记，有此标记的时候，禁用拖拽和双击操作
    if Module.CommonWidget.Field.flagDisableOpForGuide then
        self:SetHandleDoubleClick(false)
    else
        self:SetHandleDoubleClick(not bInMultipleMode)
    end

    local bHandleDrag = false
    local bPreciseClick = false
    local inSlot = self.item and self.item.InSlot or nil
    if bInMultipleMode then
        bHandleDrag = false
    else
        if inSlot then
            local slotType = inSlot.SlotType
            local SlotsNotAllowOperate = {}
            if ItemOperaTool.CheckRunWarehouseLogic() then
                -- if IsHD() then
                    SlotsNotAllowOperate = Module.Inventory.Config.SlotsNotAllowOperate
                -- else
                --     -- 为防止滚动仓库左侧菜单时误触安全箱和门禁卡包，这里开放安全箱和门禁卡包的拖拽，但是会在OnDragDetected的时候阻断
                --     SlotsNotAllowOperate = Module.Inventory.Config.SlotsNotAllowOperateForMobile
                -- end
            else
                SlotsNotAllowOperate = Module.Looting.Config.SlotsNotAllowDrag
            end
            bHandleDrag = not SlotsNotAllowOperate[slotType]
            bPreciseClick = SlotsNotAllowOperate[slotType]
        else
            bHandleDrag = ItemOperaTool.CheckIsDIYCabinetPanel()
        end
    end

    if self:IsNotAllowOperateInCollectionRoom() then
        bHandleDrag = false
    end

    -- 引导设立的标记，有此标记的时候，禁用拖拽和双击操作
    if Module.CommonWidget.Field.flagDisableOpForGuide then
        bHandleDrag = false
    end

    self:SetHandleDrag(bHandleDrag)
    if Module.CommonWidget:GetItemOperMode() == EItemOperaMode.SellMode or (ItemOperaTool.CheckRunWarehouseLogic() and bPreciseClick) then
        if IsHD() then
            self:SetCppValue("bPreciseClick", false)
        else
            self:SetCppValue("bPreciseClick", true)
        end
    else
        self:SetCppValue("bPreciseClick", false)
    end
end

function IVWarehouseTemplate:_ShowItemTips()
    -- 任务 > 不可带出 > 收藏
    self:_CheckItemMissionNeeded()

    if not ItemOperaTool.CheckRunWarehouseLogic() then
        self:_CheckIVExposureComponent()
        self:_CheckItemMarked()
    else
        self:_CheckIVForbidenToCarryOut()
    end
end

function IVWarehouseTemplate:_CheckIVExposureComponent(bOnlyCheckCarryOut)
    bOnlyCheckCarryOut = setdefault(bOnlyCheckCarryOut, false)
    if not self.item then
        return
    end
    -- 局内逻辑
    -- local isCantIntoDs = ItemHelperTool.IsCantIntoDsItemById(self.item.id)
    local isExposureItem = ItemHelperTool.IsExposureItem(self.item.id)
    local itemFeature = self.item:GetFeature(EFeatureType.Reward)
    if not bOnlyCheckCarryOut and isExposureItem and not itemFeature:IsSpecialBrick() then
        local exposureItem = self:FindOrAdd(EComp.ExposureItem, UIName2ID.IVExposureComponent, EIVSlotPos.MaskLayer)
        exposureItem:SetText(CommonWidgetConfig.Loc.ExposurePos)
        self:EnableComponent(EComp.ExposureItem, true)
        exposureItem:PlayLoop()
    else
        self:EnableComponent(EComp.ExposureItem, false)
        -- if UGlobalConstWrapper.IsItemIDForbiddenToCarryOut(self.item.id)
        --         and not Module.Looting:HasShowForbiddenToCarryOut(self.item.gid) then
        --     Module.Looting:MarkShowForbiddenToCarryOut(self.item.gid)
        --     local exposureItem = self:FindOrAdd(EComp.ExposureItem, UIName2ID.IVCantCarryOutComponent, EIVSlotPos.MaskLayer)
        --     self:EnableComponent(EComp.ExposureItem, true)
        -- else
        --     self:EnableComponent(EComp.ExposureItem, false)
        -- end
        -- self:_CheckIVForbidenToCarryOut()
    end
end

function IVWarehouseTemplate:_CheckIVForbidenToCarryOut()
    if UGlobalConstWrapper.IsItemIDForbiddenToCarryOut(self.item.id)
        and not Module.Looting:HasShowForbiddenToCarryOut(self.item.gid) then
        Module.Looting:MarkShowForbiddenToCarryOut(self.item.gid)
        local exposureItem = self:FindOrAdd(EComp.ExposureItem, UIName2ID.IVCantCarryOutComponent, EIVSlotPos.MaskLayer)
        self:EnableComponent(EComp.ExposureItem, true)
    else
        self:EnableComponent(EComp.ExposureItem, false)
    end
end

function IVWarehouseTemplate:_CheckItemMarked()
    local inSlot = self.item and self.item.InSlot or nil
    if inSlot and inSlot:GetSlotGroup() ~= ESlotGroup.Player and Server.InventoryServer:CheckItemMarked(self.item.id) then
        -- local markedInfo = Server.InventoryServer:GetItemMarkProp(self.item.id)
        ---@type IVMarkComponent
        local markComponent = self:FindOrAdd(EComp.ItemMark, UIName2ID.IVMarkComponent, EIVSlotPos.MaskLayer)
        self:EnableComponent(EComp.ItemMark, true)
        -- 动画可能被打断导致残留
        self:SetDelayDisableCompTimer(EComp.ItemMark, markComponent:PlayOnce())
    else
        self:EnableComponent(EComp.ItemMark, false)
        self:CancelDelayDisableCompTimer(EComp.ItemMark)
    end
end

function IVWarehouseTemplate:_CheckItemMarkedByLooting()
    local inSlot = self.item and self.item.InSlot or nil
    if inSlot and inSlot:GetSlotGroup() ~= ESlotGroup.Player and Module.Looting:CheckItemMarkedByLooting(self.item) then
        ---@type IVItemMarkComponent
        local markComponent = self:FindOrAdd(EComp.LootingItemMark, UIName2ID.IVItemMarkComponent, EIVSlotPos.MaskLayer, EIVCompOrder.MaskLayerOrder)
        self:EnableComponent(EComp.LootingItemMark, true)
        self:RefreshComponent(EComp.LootingItemMark)
    else
        self:EnableComponent(EComp.LootingItemMark, false)
    end
end

function IVWarehouseTemplate:_CheckItemMissionNeeded()
    if self.item and Server.QuestServer:IsSOLQuestItemButNotDSPickUpProp(self.item.id, self.item.bindType) and not self.item:IsCollectionCabinetItem() then
        ---@type IVMissionComponent
        local missionComp = self:FindOrAdd(EComp.MissionItem, UIName2ID.IVMissionComponent, EIVSlotPos.MaskLayer, EIVCompOrder.MaskLayerOrder)
        self:EnableComponent(EComp.MissionItem, true)
        missionComp:SetMissionIcon()
        missionComp:StopWidgetAnim(missionComp.WBP_Itemview_Favorite_loop)

        local slot = self.item.InSlot
        if slot then
            if slot:GetSlotGroup() == ESlotGroup.Player then
                if  self:_CheckMultiModeEnabled() then
                    missionComp:PlayLoop()
                else
                    if self:_IsHasBeenShow(self.item.gid) then
                        self:EnableComponent(EComp.MissionItem, false)
                    else
                        missionComp:PlayOnce()
                    end
                end
                -- missionComp:PlayOnce()
            elseif slot:GetSlotGroup() == ESlotGroup.Preset or slot:GetSlotGroup() == ESlotGroup.OutFit or self.itemviewMode == EIVItemViewMode.EquipmentSlotItemView then
                self:EnableComponent(EComp.MissionItem, false)
            else
                missionComp:PlayLoop()
            end
        end
    else
        self:EnableComponent(EComp.MissionItem, false)
    end
end

function IVWarehouseTemplate:_IsHasBeenShow(gid)
    local missionitem = Module.Inventory:GetMissionItemArray()
    for _, value in pairs(missionitem) do
        if value == gid or value == nil then
            return true
        end
    end
    table.insert(missionitem, gid)
    Module.Inventory:SetMissionItemArray(missionitem)
    return false
end

function IVWarehouseTemplate:_SetSelected(bSelected)
    if bSelected then
        ---@type IVSelectedComponent
        local selectedComp = self:FindOrAdd(EComp.ItemSelected, UIName2ID.IVItemSelectedComponent, EIVSlotPos.BgLayer, nil, true)
        if not selectedComp then 
            logerror("selectedComp is nil, pls check")
            return
        end
        selectedComp:IsAdjustOffset(true)
    end
    if not self.bHandleClick then
        bSelected = false
    end
    self:EnableComponent(EComp.ItemSelected, bSelected, true)
end

function IVWarehouseTemplate:SetNormalState(bShouldResetAngle)
    self._bInTriggerDragState = false
    if bShouldResetAngle and not self:IsCustomInteractivity() then
        -- self:_InitDragDropConfig()
        self:_InitDefaultInteractive()
        self:_CheckInteractive()
    end

    self._wtItemIcon:SetRenderScale(IVWarehouseTemplate.DEFAULT_SCALE)
    if DFHD_LUA == 1 then
        if self.bHandleClickR then
            self:SetCursor(EMouseCursor.Menu)
        else
            self:SetCursor(EMouseCursor.Hand)
        end
        self:_CheckMouseCursor()
    end
end

function IVWarehouseTemplate:SetTriggerDragState()
    --BEGIN MODIFICATION @ VIRTUOS :
    if IsHD() and WidgetUtil.IsGamepad() then
        -- if WidgetUtil.IsStopSimulatedMouseDrag() then
        --     -- 不希望手柄的A键长按能触发DragState
            
        --     return
        -- else
        --     -- 按照需求，在手柄准备拖拽时就播放声音
        --     -- if self.item then
        --     --     LootingAudioLogic.PlayDragAudio(self.item.id)
        --     -- end
        -- end
    end
    --END MODIFICATION

    self._bInTriggerDragState = true
    self._wtItemIcon:SetRenderScale(IVWarehouseTemplate.TRIGGER_DRAG_SCALE)

    if DFHD_LUA == 1 then
        self:SetCursor(EMouseCursor.GrabHand)
    end
end

---@param item ItemBase
function IVWarehouseTemplate:_OnSetMultiSelected(item)
    local component = self:GetComponent(EComp.MultiSelectedBox)
	if component and (not item or item == self.item) then
        component:RefreshComponent()
    end
end

--region slice refresh during opera mode changed
function IVWarehouseTemplate:SetSliceRefreshOperaMode(bEnable)
    self._bEnableSliceRefreshOperaMode = bEnable
end

-- 这边很多item单帧被广播 事件本身没有时序 目前随机划分有效提高帧率 单帧20次可以达到30ms
local _callCount = 0
function IVWarehouseTemplate:_OnItemOperaModeChanged(bInMultipleMode)
    if self._bEnableSliceRefreshOperaMode then
        if self._frameRefreshHandle then
            Timer.CancelDelay(self._frameRefreshHandle)
            self._frameRefreshHandle = nil
        end
        self._frameRefreshHandle = Timer.DelayCall(0.01 * (_callCount % 10), self._OnInternalItemOperaModeChanged, self, bInMultipleMode)
        _callCount = _callCount + 1
        if _callCount > 100 then
            _callCount = 0
        end
    else
        return self:_OnInternalItemOperaModeChanged(bInMultipleMode)
    end
end
--endregion

function IVWarehouseTemplate:_OnInternalItemOperaModeChanged(bInMultipleMode)
    -- self:RefreshView()
    self:_CheckInteractive()

    -- 出售特殊处理
    local bInSellMode = Module.CommonWidget:GetItemOperMode() == EItemOperaMode.SellMode
    local isDepositItem = self.item and self.item.InSlot and self.item.InSlot:IsDepositorySlot()
    if bInSellMode and not isDepositItem then
        return
    end
    self:_CheckItemNameVisible()
    self:_CheckBottomLeftIconText()
    self:_CheckBottomRightIconText()
    self:_CheckMultiMode()
    self:_CheckBottomRightSellPrice()
    self:_CheckItemCanSell()

    self:RefreshComponent(EComp.BottomLeftIconText)
    self:RefreshComponent(EComp.BottomRightIconText)
    self:RefreshComponent(EComp.MultiSelectedBox)
    self:RefreshComponent(EComp.BottomRightSellPrice)
    self:RefreshComponent(EComp.SoldOutMask)

end

function IVWarehouseTemplate:_CheckMultiMode()
    local bInMultipleMode = Module.CommonWidget:IsInMultiSelectedMode()
    if bInMultipleMode and self:_CheckMultiModeEnabled() then
        -- ���分不允许被出售的道具，不会进入多选的状态
        local component = self:FindOrAdd(EComp.MultiSelectedBox, UIName2ID.IVCheckBoxComponent, EIVSlotPos.TopRight, EIVCompOrder.MaskLayerOrder)
        self:EnableComponent(EComp.MultiSelectedBox, true)
    else
        self:EnableComponent(EComp.MultiSelectedBox, false)
    end
end

function IVWarehouseTemplate:_CheckMultiModeEnabled()
    if not Module.CommonWidget:IsInMultiSelectedMode() then
        return false
    end

    local bInSellMode = Module.CommonWidget:GetItemOperMode() == EItemOperaMode.SellMode
    local isDepositItem = self.item and self.item.InSlot and self.item.InSlot:IsDepositorySlot()

    if bInSellMode and not isDepositItem then
        return false
    end

    if self.item then
        if self.item:IsEquipped() then
            ---@type EquipmentFeature
            local equipmentFeature = self.item:GetFeature(EFeatureType.Equipment)
            if equipmentFeature then
                if equipmentFeature:IsKeyChain() or equipmentFeature:IsSafeBox() then
                    return false
                else
                    return true
                end
            else
                return true
            end
        elseif not Server.ShopServer:CheckIsCanRecycle(self.item.id) then
            return false
        else
            return true
        end
    end

    return false
end

function IVWarehouseTemplate:_CheckItemCanSell()
    if Module.CommonWidget:IsInMultiSelectedMode() and self.item then
        local bCanSell = Server.ShopServer:CheckIsCanRecycle(self.item.id)
        local component = self:FindOrAdd(EComp.SoldOutMask, UIName2ID.IVGreyMask, EIVSlotPos.MaskLayer, EIVCompOrder.Order1)
        if not bCanSell then
            component:EnableComponent(true)
            return false
        else
            component:EnableComponent(false)
            return true
        end
    else
        self:EnableComponent(EComp.SoldOutMask, false)
        return true
    end
end

function IVWarehouseTemplate:_CheckItemNameVisible()
    -- 不同样式设置不同的显隐
    -- local twoLineComponent = self:GetComponent(EComp.TopLeftTwoLineText)
    -- local itemNameComponent = self:GetComponent(EComp.TopLeftIconText)
    if Module.CommonWidget:GetItemOperMode() == EItemOperaMode.SellMode then
        self:EnableComponent(EComp.TopLeftTwoLineText, false)
        self:EnableComponent(EComp.TopLeftIconText, false)
    else
        self:_CheckTopLeftIconText()
        self:_CheckTopLeftTwoLineText()
    end
end

function IVWarehouseTemplate:_OnItemMarkStatusChanged(id)
    if self.item and self.item.id == id then
        self._wtBottomLeftIconText:RefreshComponent()
    end
end

function IVWarehouseTemplate:_OnNotifyItemHighlight(item)
    if self.item and self.item.gid == item.gid then
        self:HighlightItem()
    end
end

---@param itemMoveInfo itemMoveInfo
function IVWarehouseTemplate:_OnItemMoved(itemMoveInfo)
    if not self:IsInShowState() then return end

    if self.item and self.item:IsBullet() and not self.item:IsEquipped() then
        if itemMoveInfo.OldLoc and itemMoveInfo.OldLoc.ItemSlot:IsWeaponSlot() then
            self:RefreshComponent(EComp.BottomLeftIconText2)
        elseif itemMoveInfo.NewLoc and itemMoveInfo.NewLoc.ItemSlot:IsWeaponSlot() then
            self:RefreshComponent(EComp.BottomLeftIconText2)
        end
    end
end

-- 响应点击出选中框
function IVWarehouseTemplate:_CheckIsShowSelectedCompByClicked()
    local bItemHaveTeammateBindCondition = ItemOperaTool.CheckItemHaveTeammateBindCondition(self.item)
    local slotType = self.item and self.item.InSlot and self.item.InSlot.SlotType
    if not Module.CommonWidget:IsInMultiSelectedMode() and ItemOperaTool.CheckRunWarehouseLogic()
        and not Module.CommonWidget:IsAuctionMode() and not bItemHaveTeammateBindCondition
        and not (slotType == ESlotType.SafeBox or slotType == ESlotType.KeyChain or slotType == ESlotType.MeleeWeapon)
        and not Server.ArmedForceServer:CheckIsRentalStatus() then
        return true
    else
        return false
    end
end

function IVWarehouseTemplate:_OnPlayerItemMarkingInfoArrayChange(markItemSet)
    if self.item and markItemSet[self.item.gid] then
        self:_CheckItemMarkedByLooting()
    else
        self:EnableComponent(EComp.LootingItemMark, false)
    end
end

function IVWarehouseTemplate:_OnMarkSelectionModeChange()
    local slot = self.item and self.item.InSlot
    if slot and slot:GetSlotGroup() ~= ESlotGroup.Player then
        self:UpdateInteractiveInMarkSelectionMode()
    end
end

function IVWarehouseTemplate:UpdateInteractiveInMarkSelectionMode()
    local bInMarkSelectionMode = Module.Looting:IsInMarkSelectionMode()
    if self.bInMarkSelectionMode ~= bInMarkSelectionMode then
        self.bInMarkSelectionMode = bInMarkSelectionMode

        if bInMarkSelectionMode then
            self:SetCustomInteractivity(true)

            self:SetHandleDrag(false)
            self:SetHandleDoubleClick(false)
            -- self:SetCppValue("bHandleDrag", false)
            -- self:SetCppValue("bHandleDoubleClick", false)
            self:SetCppValue("bHandleLongPress", false)
        else
            self:SetCustomInteractivity(false)

            self:_InitDefaultInteractive()
            self:_CheckInteractive()
        end
    end
end

--- 隐藏下方的图标和文字
function IVWarehouseTemplate:HideBottomIconText()
    self._wtBottomLeftIconText:Collapsed()
    self._wtBottomRightIconText:Collapsed()
end

-- 对于按键抬起触发选中框的逻辑
function IVWarehouseTemplate:MouseUpSetSelected()
    if self:_CheckIsShowSelectedCompByClicked() and not self.isLongPress and not self.isDoubleClicked then
        self:_SetSelected(true)
    elseif self.isLongPress then
        self:_SetSelected(false)
    elseif self.isDoubleClicked then
        self:_SetSelected(false)
        self.isDoubleClicked = false
    end
end

---------------------------自定义互动性--------------------------------------
function IVWarehouseTemplate:SetCustomInteractivity(bInCustomInteractivity)
    self._bCustomInteractivity = bInCustomInteractivity
end

function IVWarehouseTemplate:IsCustomInteractivity()
    return self._bCustomInteractivity
end

function IVWarehouseTemplate:SetPosOrder(id, order)
    if id then
        local component = self:GetComponent(id)
        if component then
            local canvasSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(component)
            if canvasSlot then
                canvasSlot:SetZOrder(order)
            end
        end
    end
end

function IVWarehouseTemplate:GetItemIconComponent()
    return self:GetComponent(EComp.ItemIcon)
end

function IVWarehouseTemplate:SetClearStackNumAnimTimer()
    if self._timer then
        self._timer:Destroy()
    end
    ---@type Timer
    self._timer = Timer:NewIns(Module.Looting.Config.USING_ITEM_STACK_NUM_ANIM_DURATION)
    self._timer:AddListener(function()
        if self.item then
            self.item.previousNum = 0
        end
        if self._timer then
            self._timer:Destroy()
        end
    end, self)
    self._timer:Start()
end

---@return CommonItemHighlight
function IVWarehouseTemplate:GetCommonItemHighlight(bCreateIfNotExist)
    local itemHighlightComponent = self:GetComponent(EComp.ItemHighlight)
    if not itemHighlightComponent and bCreateIfNotExist then
        itemHighlightComponent = self:FindOrAdd(EComp.ItemHighlight, UIName2ID.CommonItemHighlight, EIVSlotPos.MaskLayer, EIVCompOrder.High, true)
    end
    return itemHighlightComponent
end

function IVWarehouseTemplate:SetDelayDisableCompTimer(CompId, endTime, bReturn2GlobalRegisterIfDisable)
    bReturn2GlobalRegisterIfDisable = setdefault(bReturn2GlobalRegisterIfDisable, false)
    if self._timers[CompId] then
        Timer.CancelDelay(self._timers[CompId])
    end
    self._timers[CompId] = Timer.DelayCall(endTime, function()
        self._timers[CompId] = nil
        self:EnableComponent(CompId, false, bReturn2GlobalRegisterIfDisable)
    end, self)
end

function IVWarehouseTemplate:CancelDelayDisableCompTimer(CompId)
    if self._timers[CompId] then
        Timer.CancelDelay(self._timers[CompId])
        self._timers[CompId] = nil
    end
end

function IVWarehouseTemplate:ShowOrHideCommonHoverBg(bShow)
    if isvalid(self) and isvalid(self.WBP_CommonHoverBg) then
        if bShow then
            self.WBP_CommonHoverBg:HitTestInvisible()
        else
            self.WBP_CommonHoverBg:Collapsed()
        end
    end
end

-- function IVWarehouseTemplate:SetCommonHoverBgHideFreeAnalogCursorHideFocusContentRoot(bHideFreeAnalogCursorHideFocusContentRoot)
--     if isvalid(self) and isvalid(self.WBP_CommonHoverBg) then
--         if self.WBP_CommonHoverBg.SetHideFreeAnalogCursorHideFocusContentRoot then
--             self.WBP_CommonHoverBg:SetHideFreeAnalogCursorHideFocusContentRoot(bHideFreeAnalogCursorHideFocusContentRoot)
--         end
--     end
-- end

---------------------------自定义互动性--------------------------------------

-- function IVWarehouseTemplate:_SetHoverTips()
--     -- 仓库悬停态
--     if Facade.UIManager:GetCurrentStackUIId() == UIName2ID.WarehouseMain then
--         self:SetCppValue("bHandleHover", true)

--         local function ShowHoverTips()
--             -- 需要显示的信息
--             local smallTipsList = {}
--             table.insert(smallTipsList, {smallTip = self.item.name})
--             -- local txt = string.format(Module.Inventory.Config.Loc.PriceText, self.item:GetTotalMallSellPrice())
--             local txt = ShopHelperTool.GetDynamicGuidePriceRichTextByItemV3(self.item, self.item.num,
--                 false, false, nil, false)
--             table.insert(smallTipsList, {smallTip = txt})
--             Module.Inventory:ShowCommonHoverTipsHD(
--                 {
--                     smallTipList = smallTipsList
--                 },
--                 self
--             )
--         end

--         local function HideHoverTips()
--             Module.Inventory:CloseCommonHoverTipsHD(false, self)
--         end
--         self:Event("OnHovered", ShowHoverTips, self)
--         self:Event("OnUnhovered", HideHoverTips, self)
--     end
-- end

-- function IVWarehouseTemplate:_OnHover()
--     if self.hoverHandle then
--         Module.CommonTips:RemoveCommonHoverTipsHD(self.hoverHandle)
--         self.hoverHandle = nil
--     end

--     table.insert(smallTipsList, {smallTip = self.item.name})
--     table.insert(smallTipsList, {smallTip = string.format(Module.Inventory.Config.Loc.PriceText, self.item:GetTotalMallSellPrice())})
--     self.hoverHandle = Module.CommonTips:ShowCommonHoverTipsHD({
--         smallTipList = smallTipsList
--     })
-- end

-- function IVWarehouseTemplate:_OnUnhovered()
--     if self.hoverHandle then
--         Module.CommonTips:RemoveCommonHoverTipsHD(self.hoverHandle)
--         self.hoverHandle = nil
--     end
-- end

-- --endregion
-- --==================================================

--BEGIN MODIFICATION @ VIRTUOS : 新增手柄操作相关方法
function IVWarehouseTemplate:_IsUsingGamepad()
    if not IsHD() then
        return false
    end

    local curInputType = UGPInputHelper.GetCurrentInputType(self)
    local bIsGamepad = (curInputType == EGPInputType.Gamepad)
    return bIsGamepad
end

function IVWarehouseTemplate:_OnFocusReceived()
    if not IsHD() then
        return
    end

    self:_InitShortcut()
    InventoryNavManager.SetCurrentFocusItemView(self)
    loginfo("---InventoryNavManager:Set Start Item as: ", self.item.name,"---")
    self._bOnFocused = true
end

function IVWarehouseTemplate:_OnFocusLost()
    if not IsHD() then
        return
    end
    

    self:ForceStopLongPressTimer()
    self:_RemoveShortcut()
    self._bOnFocused = false
end

-- 绑定输入
function IVWarehouseTemplate:_InitShortcut()
    if not IsHD() then
        return
    end

    
    if ItemOperaTool.CheckRunWarehouseLogic() == false then
        local equipmentFeature = self.item:GetFeature(EFeatureType.Key) 
        if equipmentFeature~= nil then
            return
        end
    end

    -- 开始用手柄交换物品
    if Module.CommonWidget.IsEnableGamepadSwapItem() == true and self.bHandleDrag == true and not Module.CommonWidget:IsInSwapingItem() then
        self._BeginSwapItemShortcut = self:AddInputActionBinding("BeginSwapItem", EInputEvent.IE_Pressed, self._OnBeginSwapItem, self, EDisplayInputActionPriority.UI_Stack)
        self._BeginSwapItemShortcutA = self:AddInputActionBinding("HoldASwapItem_Gamepad", EInputEvent.IE_Pressed, self._OnBeginSwapItem, self, EDisplayInputActionPriority.UI_Stack)
    end
end

function IVWarehouseTemplate:_RemoveShortcut()
    if not IsHD() then
        return
    end

    if self._BeginSwapItemShortcut then
        self:RemoveInputActionBinding(self._BeginSwapItemShortcut)
        self._BeginSwapItemShortcut = nil
    end

    if self._BeginSwapItemShortcutA then
        self:RemoveInputActionBinding(self._BeginSwapItemShortcutA)
        self._BeginSwapItemShortcutA = nil
    end
end

function IVWarehouseTemplate:_OnBeginSwapItem()
    if not IsHD() then
        return
    end

    if self.bHandleDrag == false or self.item:IsInUsing() then
        return
    end

    if Module.CommonWidget:IsInSwapingItem() then
        return
    end

    if self.item then
        log("_OnBeginSwapItem")

        -- 记录待交换的物品信息
        Module.CommonWidget:SetGamepadSwapItem(true, self, self.item)
        -- 立刻模拟长按完成
        self:SimLongPressedIMM()
    end
end

function IVWarehouseTemplate:AddBlankNavTarget()
    if IsHD() then
        local ParentView = self:GetViewParent(true)
        if ParentView then
            ParentView:AddNavBlankItem(self)
        end
    end
end

function IVWarehouseTemplate:CarryItemFromPopByGamepad()
    if Facade.GameFlowManager:IsInOBMode() then
        return
    end

    if not IsHD() then
        return
    end

    if not self:_IsUsingGamepad() then
        return
    end

    Module.ItemDetail:CloseAllPopUI()
    self:_OnDoubleClickLogic()
    
    Module.CommonWidget:SetOpenedPopItemView(nil)
end

function IVWarehouseTemplate:_OnGlobalHoverItemViewUseAnim(bHover, item, bNeedRefreshFocus)
    if not IsHD() then
        return
    end

    if not self:_IsUsingGamepad() then
        return
    end

    if item ~= nil and self.item == item then
        if bHover == true then
            self:HideTip()

        elseif self._bOnFocused == true and bNeedRefreshFocus == true then
            -- 这里的特殊处理是为了解决：在背包页面，当手柄聚焦在正在使用的道具时取消使用或道具用完后按键提示刷新错误的问题。
            -- 需要重新设置聚焦，通过修改鼠标位置的方式重新正确刷新按键提示，主要是重新HoverItem
            -- 后续可能需要再优化这里的实现
            WidgetUtil.SetUserFocusToGameViewport()
            
            -- 延迟后检查当前itemView是否已经被隐藏
            Timer.DelayCall(0.1, function()
                if self:IsVisible() == true then
                    WidgetUtil.SetUserFocusToWidget(self, true)
                end
            end)
        end
    end
end

function IVWarehouseTemplate:SetSingleHoldKeyTip(inWidget)
    Facade.UIManager:RemoveSubUIByParent(self,self._wtSingleHoldKeyTip)
    self._wtSingleHoldKeyTip = inWidget
end

-- function IVWarehouseTemplate:SetMultiKeysTipOnDrag(inWidget)
--     Facade.UIManager:RemoveSubUIByParent(self,self._wtMultiKeysTipOnDrag)
--     self._wtMultiKeysTipOnDrag = inWidget
-- end

function IVWarehouseTemplate:ClearGamepadKeyTip()
    self:SetSingleHoldKeyTip(nil)
    -- self:SetMultiKeysTipOnDrag(nil)
end

function IVWarehouseTemplate:GetSingleHoldKeyTip()
    return self._wtSingleHoldKeyTip
end

-- function IVWarehouseTemplate:GetMultiKeysTipOnDrag()
--     return self._wtMultiKeysTipOnDrag
-- end
--END MODIFICATION

return IVWarehouseTemplate