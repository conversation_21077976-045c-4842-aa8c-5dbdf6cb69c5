----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollection)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
local EGPUINavGroupTransSimpleStrategy = import "EGPUINavGroupTransSimpleStrategy"
-- END MODIFICATION

local CollectionsWeaponSkinPagePanel = ui("CollectionsWeaponSkinPagePanel")
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local CollectionLogic = require "DFM.Business.Module.CollectionModule.Logic.CollectionLogic"
local CommonItemViewDropDownBox = require "DFM.Business.Module.CommonWidgetModule.UI.DropDown.CommonItemViewDropDownBox"
local CollectionConfig = Module.Collection.Config
local ItemDetailViewEquip = require "DFM.Business.Module.ItemDetailModule.UI.ItemDetailType3.ItemDetailViewEquip"
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"
local CollectionMysticalSkinBtn = require "DFM.Business.Module.CollectionModule.UI.CollectionMysticalSkinBtn"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local UGunPresetTableManager = import "GunPresetTableManager"
local UAssembleWeaponDataLibrary = import "AssembleWeaponDataLibrary"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local CommonWeaponSkinMissionProgress = require "DFM.Business.Module.CommonWidgetModule.UI.CommonWeaponSkinMissionProgress"
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
local LiteCommonDownload =  require "DFM.Business.Module.LitePackageModule.UI.LiteCommonDownload"
local ETipsTriggerReason = import("ETipsTriggerReason")


function CollectionsWeaponSkinPagePanel:Ctor()
    self._wtMainPanel = self:Wnd("wtMainPanel", UIWidgetBase)
    self._wtFilterBtn = self:Wnd("wtFilterBtn", DFCommonButtonOnly)
    self._wtFilterBtn:Event("OnClicked",self._OnFilterBtnClick,self)
    self._wtMysticalSkinDropDown = self:Wnd("wtMysticalSkinDropDown", CommonItemViewDropDownBox)
    local data = {
        tabName = "",                                                               -- tab文本名字
        subUINavID = UIName2ID.CommonItemViewDropDownItem,                          -- 指定生成子ui
        caller = self,                                                              -- 方法持有者
        fOnGetPresetCount = self._OnGetMysticalWeaponSkinCount,                     -- 子控件数量获取方法
        fOnPresetProcessTabItemWidget = self._OnProcessMysticalWeaponSkinWidget,    -- 子控件生成回调方法,
    }
    self._wtMysticalSkinDropDown:InitDownBox(data)
    self._wtWeaponSkinScrollView = UIUtil.WndWaterfallScrollBox(self, "wtWeaponSkinScrollView", self._OnGetItemsCount, self._OnProcessItemWidget)
    self._wtBoxArea = self:Wnd("wtBoxArea", UIWidgetBase)
    self._wtInfoPanel = self:Wnd("wtInfoPanel", UIWidgetBase)
    self._wtItemDetailView = self:Wnd("wtItemDetailView", ItemDetailViewEquip)
    self._wtItemDetailView:SetWeaponDetailIsHideBtn(true)
    self._wtItemDetailView:SetShowWeaponDetailCheckBox(false)
    self._wtRandomInfoPanel = self:Wnd("wtRandomInfoPanel", UIWidgetBase)
    self._wtRandomItemNameTxt = self:Wnd("wtRandomItemNameTxt", UITextBlock)
    self._wtRandomItemDescTxt = self:Wnd("wtRandomItemDescTxt", UITextBlock)
    self._wtFromMandelBrickBtn = self:Wnd("wtFromMandelBrickBtn", CollectionMysticalSkinBtn)
    self._wtFromMandelBrickBtn:Event("OnClicked", self._JumpToMandelBrickPage, self)
    self._wtFromAuctionBtn = self:Wnd("wtFromAuctionBtn", CollectionMysticalSkinBtn)
    self._wtFromAuctionBtn:Event("OnClicked", self._JumpToAuctionPage, self)
    self._wtFromMatrixWorkshopBtn = self:Wnd("wtFromMatrixWorkshopBtn", CollectionMysticalSkinBtn)
    self._wtFromMatrixWorkshopBtn:Event("OnClicked", self._JumpToMysticalWorkshopPage, self)
    self._wtMysticalSkinBtnPanel = self:Wnd("wtMysticalSkinBtnPanel", UIWidgetBase)
    self._wtApplyBtn = self:Wnd("wtApplyBtn", DFCommonButtonOnly)
    self._wtApplyBtn:Event("OnClicked", self._OnActionBtnClicked, self)
    self._wtApplyBtn:Event("OnDeClicked", self._OnActionBtnClicked, self)
    self._wtEmptyBg = self:Wnd("wtEmptyBg", UIWidgetBase)
    self._wtEmptySlot = self:Wnd("wtEmptySlot", UIWidgetBase)
    if self._wtEmptySlot then
        Facade.UIManager:RemoveSubUIByParent(self, self._wtEmptySlot)
        local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.CommonEmptyContent, self._wtEmptySlot, nil, nil)
        self._wtEmptyHint = getfromweak(weakUIIns)
    end
    self._wtEmptyHint:BP_SetText(CollectionConfig.Loc.NoItems)
    self._wtEmptyHint:SetCppValue("Set_Type", 1)
    self._wtEmptyHint:BP_Set_Type()
    self._wtAlertHintBox = self:Wnd("wtAlertHintBox", UIWidgetBase) 
    self._wtAlertHintTxt = self:Wnd("wtAlertHintTxt", UIWidgetBase) 
    self._wtQuestionHintBox = self:Wnd("wtQuestionHintBox", UIWidgetBase)
    self._wtQuestionHintText = self:Wnd("wtQuestionHintText", UITextBlock)
    self._wtTipCheckBtn = self:Wnd("wtTipCheckBtn", DFCheckBoxOnly)
    self._wtTipCheckBtn:Event("OnCheckStateChanged", self._OnShowTipCheckBoxStateChanged, self)
    self._wtTipAnchor = UIUtil.WndTipsAnchor(self, "wtTipAnchor", self._OnShowInstruction, self._OnHideInstruction)
    self._wtWeaponSkinMissionProgressPanel = self:Wnd("wtWeaponSkinMissionProgressPanel", CommonWeaponSkinMissionProgress)
    self._wtDetailBtn = self:Wnd("wtDetailBtn", DFCommonButtonOnly)
    self._wtDetailBtn:Event("OnClicked", self._ShowSkinDetailPage, self)
    self._wtDetailBtn_PC = self:Wnd("wtDetailBtn_PC", DFCommonButtonOnly)
    self._wtDetailBtn_PC:Event("OnClicked", self._ShowSkinDetailPage, self)
    self._wtTradeBtn = self:Wnd("wtTradeBtn", DFCommonButtonOnly)
    self._wtTradeBtn:Event("OnClicked", self._JumpToTradePage, self)
    self._wtDownloadBtn = self:Wnd("wtDownloadBtn", DFCommonButtonOnly)
    self._wtDownloadBtn:Event("OnClicked", self._DownLoadResources, self)
    self._wtToggleRandomBtn = self:Wnd("wtToggleRandomBtn", DFCommonButtonOnly)
    self._wtToggleRandomBtn:Event("OnClicked", self._ToggleRandomSkin, self)
    if Module.ExpansionPackCoordinator:IsSupportLitePackage() then
        self._wtCommonDownload = self:Wnd("wtCommonDownload", LiteCommonDownload)
    end
    if not hasdestroy(self._wtCommonDownload) then
        self._wtCommonDownload:Collapsed()
    end
    self._wtHideUIBtn = self:Wnd("wtHideUIBtn", UIButton)
    if not IsHD() then
        --self._wtHideUIBtn:Event("OnClicked", self._ToggleUI, self)
        --self._wtHideUIBtn:Event("OnPressed", self._OnHideUIBtnPressed, self)
        --self._wtHideUIBtn:Event("OnReleased", self._OnHideUIBtnReleased, self)
    end
    self._wtHideUIBtn:Collapsed()
    -- 截屏分享相关
    self._wtShareBtn = self:Wnd("wtShareBtn", DFCommonButtonOnly)
    self._wtShareBtn:Event("OnClicked", self.OnShareClick,self)
    self._selectedPos = -1
    self._selectedMysticalPos = -1
    self._selectedCell = nil
    self._weaponSkinItems = {}
    self._mysticalWeaponSkinItems = {}
    self._redDotInsMap = setmetatable({}, weakmeta_key)
    self._scrollStopHandle = nil
    self._subTabIdToGunType = {
        [1] = 0,
        [2] = 1,
        [3] = 2,
        [4] = 3,
        [5] = 4,
        [6] = 5,
        [7] = 6,
        [8] = 7,
        [9] = 15,
    }
    self._srotFuncList = {}
    table.insert(self._srotFuncList, CollectionLogic.WeaponSkinFirearmSortDecend)
    table.insert(self._srotFuncList, CollectionLogic.WeaponSkinFirearmSortAscend)
    table.insert(self._srotFuncList, CollectionLogic.WeaponSkinQualitySortDecend)
    table.insert(self._srotFuncList, CollectionLogic.WeaponSkinQualitySortAscend)
    self._bUpdatingRandomPool = false
end


function CollectionsWeaponSkinPagePanel:OnInitExtraData()
end


-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function CollectionsWeaponSkinPagePanel:OnOpen()
end

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function CollectionsWeaponSkinPagePanel:OnClose()
    self._selectedCell = nil
    self:RemoveAllLuaEvent()
    Facade.UIManager:ClearSubUIByParent(self, self._wtEmptySlot)
    Facade.UIManager:ClearSubUIByParent(self, self._wtItemDetailView)
    table.empty(self._redDotInsMap)
    Module.ReddotTrie:UnRegisterDynamicReddot(EReddotTrieObserverType.Collection, ECollectionDynamicDataType.WeaponSkin)
end

function CollectionsWeaponSkinPagePanel:OnShowBegin()
    self:EnableGamepadFeature()
    self:_RefreshDownloadBtn()
end

function CollectionsWeaponSkinPagePanel:OnHideBegin()
    self:DisableGamepadFeature()
    self:ClosePopup()
end

-- UI OnOpen后、调用Show()显示时触发
-- 做表现
---@overload fun(LuaUIBaseView, OnShow)
function CollectionsWeaponSkinPagePanel:OnShow()
    -- BEGIN MODIFICATION - LiaoLianyu
    if (IsPS5() or IsXSX()) then
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "gc.timebetweenpurgingpendingkillobjects 10")
    end
    -- END MODIFICATION - VIRTUOS
    self._scrollStopHandle = UIUtil.AddScrollBoxClickStopScroll(self._wtWeaponSkinScrollView, self)
    self._bUpdatingRandomPool = false
end


-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function CollectionsWeaponSkinPagePanel:OnHide()
    -- BEGIN MODIFICATION - LiaoLianyu
    if (IsPS5() or IsXSX()) then
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "gc.timebetweenpurgingpendingkillobjects 30")
    end
    -- END MODIFICATION - VIRTUOS
    self._bDropDownBoxVisible = self._wtMysticalSkinDropDown:IsChecked()
    if self._scrollStopHandle then
		UIUtil.RemoveScrollBoxClickStopScroll(self._scrollStopHandle)
		self._scrollStopHandle = nil
	end
end

function CollectionsWeaponSkinPagePanel:OnAnimFinished(anim)
    if anim == self.WBP_Collections_GunTypeMain_in then
        if not hasdestroy(self._selectedCell) then
            WidgetUtil.SetUserFocusToWidget(self._selectedCell, true)
        end
    end
end

-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
function CollectionsWeaponSkinPagePanel:_RegisterDynamicNavConfig()  
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if self._wtApplyBtn:IsVisible() then 
        self:_EnableGamepadA()
    else 
        self:_DisableGamepadA()
    end
end

function CollectionsWeaponSkinPagePanel:_OnToggleTipsByPad()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if self._wtItemDetailView:IsVisible() then
        --self._wtItemDetailView是动态生成的，会根据初始化时传入的item生成不同类型的子UI
        self._ItemDetailEquipWeaponMysticalSkin =  self._wtItemDetailView._CollectionWeaponSkinPanel
        if self._ItemDetailEquipWeaponMysticalSkin then
            self._ItemDetailEquipWeaponMysticalSkin:ToggleTips()
        end
    end
end

function CollectionsWeaponSkinPagePanel:OnInputTypeChanged(inputType)
    self._wtWeaponSkinScrollView:RefreshVisibleItems()
end

function CollectionsWeaponSkinPagePanel:EnableGamepadFeature()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if not self._wtNavGroupMysticalSkinBtnPanel then
        self._wtNavGroupMysticalSkinBtnPanel = WidgetUtil.RegisterNavigationGroup(self._wtMysticalSkinBtnPanel, self, "Hittest")
        if self._wtNavGroupMysticalSkinBtnPanel then
            local navStrategy = self._wtNavGroupMysticalSkinBtnPanel:GetOwnerNavStrategy()
            if navStrategy then
                navStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.LastFocusedOrHittestOrDistance)
            end  
            self._wtNavGroupMysticalSkinBtnPanel:AddNavWidgetToArray(self._wtMysticalSkinBtnPanel) 
        end
    end 
    if not self._wtNavGroupSkinList then 
        self._wtNavGroupSkinList = WidgetUtil.RegisterNavigationGroup(self._wtWeaponSkinScrollView, self, "Hittest")
        if self._wtNavGroupSkinList then
            self._wtNavGroupSkinList:AddNavWidgetToArray(self._wtWeaponSkinScrollView)
            self._wtNavGroupSkinList:SetScrollRecipient(self._wtWeaponSkinScrollView)
            self._wtNavGroupSkinList:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
        end
    end 
    --添加长按A.应用外观
    self._wtApplyBtn:SetDisplayInputAction("Collection_Apply_Gamepad", true, nil, true)
    self._wtDetailBtn_PC:SetDisplayInputAction("GunSkin_ShowDetail", true, nil, true)    
    self._wtFilterBtn:SetDisplayInputAction("Collection_FilterLeft_Gamepad", true, nil, true)   
    self._wtMysticalSkinDropDown:EnableDropDownShortcutWithAction(true, "Collection_Style_Gamepad")
    if not self._wtDetailBtn_PC_handle then
	    self._wtDetailBtn_PC_handle = self:AddInputActionBinding("GunSkin_ShowDetail", EInputEvent.IE_Pressed, self._ShowSkinDetailPage, self, EDisplayInputActionPriority.UI_Stack)
    end
    if not self._wtFilterBtnHandle then
        self._wtFilterBtnHandle = self:AddInputActionBinding("Collection_FilterLeft_Gamepad", EInputEvent.IE_Pressed, self._OnFilterBtnClick,self, EDisplayInputActionPriority.UI_Stack)
    end   
    self:_AddInputActionForApplyBtn()
    if self._wtMysticalSkinDropDown then
        if not self._wtMysticalSkinCheckButton then
            self._wtMysticalSkinCheckButton = self._wtMysticalSkinDropDown:Wnd("DFCommonCheckButton", UIWidgetBase)
        end
        self._wtMysticalSkinCheckButton:Event("OnCheckButtonStateChanged",self._OnDropDownBoxOpenStateChanged,self)
    end
    if not self._hNavigationChangedFocus then
        self._hNavigationChangedFocus = self._wtNavGroupSkinList.OnNavGroupFocusReceivedEvent:Add(self._DisableGamepadA, self)
    end
    if not self._hNavigationChangedFocus2 then
        self._hNavigationChangedFocus2 = self._wtNavGroupMysticalSkinBtnPanel.OnNavGroupFocusReceivedEvent:Add(self._EnableGamepadA, self)
    end
    if not hasdestroy(self._selectedCell) then
        WidgetUtil.SetUserFocusToWidget(self._selectedCell, true)
    end
end


function CollectionsWeaponSkinPagePanel:_AddInputActionForApplyBtn()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if self._bEnableApplyBtn and not self._wtApplyBtnHandle then
        self._wtApplyBtnHandle = self:AddInputActionBinding("Collection_Apply_Gamepad", EInputEvent.IE_Pressed, self._OnActionBtnClicked,self, EDisplayInputActionPriority.UI_Stack)
    end   
end

function CollectionsWeaponSkinPagePanel:_RemoveInputActionForApplyBtn()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if self._wtApplyBtnHandle then
        self:RemoveInputActionBinding(self._wtApplyBtnHandle)
        self._wtApplyBtnHandle = nil 
    end
end

function CollectionsWeaponSkinPagePanel:_DisableGamepadA()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if self._NavConfigHandler then
        WidgetUtil.DisableDynamicNavConfig(self._NavConfigHandler)
    end
    self._NavConfigHandler = WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)
end

function CollectionsWeaponSkinPagePanel:_EnableGamepadA()
    if not IsHD() then
        return
    end
    if self._NavConfigHandler then
        WidgetUtil.DisableDynamicNavConfig(self._NavConfigHandler)
        self._NavConfigHandler = nil
    end
end

function CollectionsWeaponSkinPagePanel:DisableGamepadFeature()
    if not IsHD() then
        return
    end
    if self._wtDetailBtn_PC_handle then
        self:RemoveInputActionBinding(self._wtDetailBtn_PC_handle)
    end
    if self._wtFilterBtnHandle then
        self:RemoveInputActionBinding(self._wtFilterBtnHandle)
    end
    self:_RemoveInputActionForApplyBtn()
    self._wtMysticalSkinDropDown:EnableDropDownShortcut(false)
    if self._NavConfigHandler then
        WidgetUtil.DisableDynamicNavConfig(self._NavConfigHandler)
    end
    if self._wtMysticalSkinCheckButton then
        self._wtMysticalSkinCheckButton:RemoveEvent("OnCheckButtonStateChanged")
    end
    if self._hNavigationChangedFocus then
        self._wtNavGroupSkinList.OnNavGroupFocusReceivedEvent:Remove(self._hNavigationChangedFocus)
    end
    if self._hNavigationChangedFocus2 then
        self._wtNavGroupMysticalSkinBtnPanel.OnNavGroupFocusReceivedEvent:Remove(self._hNavigationChangedFocus2)
    end
    self._NavConfigHandler = nil
    self._wtNavGroupSkinList = nil
    self._wtNavGroupMysticalSkinBtnPanel = nil
    self._wtDetailBtn_PC_handle = nil 
    self._wtApplyBtnHandle = nil 
    self._wtFilterBtnHandle = nil
    self._hNavigationChangedFocus = nil
    self._hNavigationChangedFocus2 = nil
    WidgetUtil.RemoveNavigationGroup(self)
end

function CollectionsWeaponSkinPagePanel:_SetDefaultGamepadFocus()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if self._wtNavGroupSkinList then
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroupSkinList)
    end
end

function CollectionsWeaponSkinPagePanel:_OnDropDownBoxOpenStateChanged(bOpen)
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if bOpen then
        self:_DisableGamepadA()
    else
        self:_EnableGamepadA()
    end
end
-- END MODIFICATION



function CollectionsWeaponSkinPagePanel:ToggleControlAndListeners(bEnable, bFullReset)
    if bEnable == true then
        if bFullReset == true then
            self:AddLuaEvent(Server.GunsmithServer.Events.evtCSWAssemblyApplySkinRes, self._OnWeaponSkinApplied, self)
        end
        self:AddLuaEvent(Server.CollectionServer.Events.evtCollectionRandomSkinPoolToggled,self._OnRandomSkinPoolToggled, self)
        self:AddLuaEvent(Server.CollectionServer.Events.evtCollectionRandomSkinPoolUpdate,self._OnRandomSkinPoolUpdate, self)
        self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult,self._OnDownloadResult,self)
        self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleStateMaybeChanged,self._OnDownloadStateChange,self)
        self:AddLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged, self._OnPackStateChanged, self)
        self:AddLuaEvent(CollectionConfig.Events.evtOnGunSkinFilterUpdated, self._OnGunSkinFilterUpdated, self)
        self:AddLuaEvent(LuaGlobalEvents.evtSceneLoaded, self.OnRefreshModel, self)
        self:AddLuaEvent(Module.Share.Config.Events.evtShareFlowFinish,self.OnShareFlowFinish, self)
        self._wtMysticalSkinDropDown:SwitchCheckButtonState(self._bDropDownBoxVisible == true and ECheckButtonState.Checked or ECheckButtonState.Unchecked)
        CollectionLogic.RegStackUIInputSummary(self._shotcutList, false)
    else
        if bFullReset == true then
            self:RemoveLuaEvent(Server.GunsmithServer.Events.evtCSWAssemblyApplySkinRes, self._OnWeaponSkinApplied, self)
            self._selectedCell = nil
            self._selectedPos = -1
        end
        self:RemoveLuaEvent(Server.CollectionServer.Events.evtCollectionRandomSkinPoolToggled,self._OnRandomSkinPoolToggled, self)
        self:RemoveLuaEvent(Server.CollectionServer.Events.evtCollectionRandomSkinPoolUpdate,self._OnRandomSkinPoolUpdate, self)
        self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult,self._OnDownloadResult,self)
        self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleStateMaybeChanged,self._OnDownloadStateChange,self)
        self:RemoveLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged, self._OnPackStateChanged, self)
        self:RemoveLuaEvent(CollectionConfig.Events.evtOnGunSkinFilterUpdated, self._OnGunSkinFilterUpdated, self)
        self:RemoveLuaEvent(LuaGlobalEvents.evtSceneLoaded, self.OnRefreshModel, self)
        self:RemoveLuaEvent(Module.Share.Config.Events.evtShareFlowFinish,self.OnShareFlowFinish, self)
        self._bHideUI = nil
        local items = {}
        for index, skinItem in ipairs(self._weaponSkinItems) do
            if Server.CollectionServer:IsPropWithRedDot(skinItem.id) then
                if ItemHelperTool.IsMysticalSkin(skinItem.id) == true then
                    local mysticalWeaponSkinItems = CollectionLogic.GetMysticalSkinInstanceById(skinItem.id)
                    table.append(items, mysticalWeaponSkinItems)
                end
                table.insert(items, skinItem)
            end
        end
        if items and #items > 0 then
            CollectionLogic.RemoveRedDots(items)
        end 
    end
end


function CollectionsWeaponSkinPagePanel:RefreshView(mainTabIndex, bResetList, bResetTab, bUseTertiaryTab)
    self._mainTabIndex = mainTabIndex or self._mainTabIndex
    if bResetList then
        self._bDropDownBoxVisible = false
        Module.ReddotTrie:UnRegisterDynamicReddot(EReddotTrieObserverType.Collection, ECollectionDynamicDataType.WeaponSkin)
    end
    if bResetTab then
        self:_ShowUI()
    end
    if not bUseTertiaryTab then
        self:_OnRefreshWeaponSkinItems(bResetList)
    end
end

function CollectionsWeaponSkinPagePanel:_OnRefreshWeaponSkinItems(bResetList)
    local filterData = CollectionLogic.GetLastGunSkinFilterData()
    local selectedSkinGroups = filterData.selectedSkinGroups
    local skinGroup = CollectionConfig.EItemGroup.Any
    if table.nums(selectedSkinGroups) == 1 then
        skinGroup = table.keys(selectedSkinGroups)[1]
    end
    local selectedSkinTypes = filterData.selectedSkinTypes
    local skinType = CollectionConfig.EItemType.Any
    if table.nums(selectedSkinTypes) == 1 then
        skinType = table.keys(selectedSkinTypes)[1]
    end
    local selectedQualityIDs = filterData.selectedQualityIDs
    local selectedGunTypeIDs = filterData.selectedGunTypeIDs
    local weaponChoice = filterData.weaponChoice
    self._sortMode = filterData.sortMode
    self._bCanShowRandomSkinItem = (table.isempty(selectedQualityIDs) or table.nums(selectedQualityIDs) == 5) 
        and skinGroup ~= CollectionConfig.EItemGroup.NotOwned
        and skinType == CollectionConfig.EItemType.Any
    self._realWeaponSkinItems = Module.Collection.Field:GetSkinList()
    if skinGroup ~= CollectionConfig.EItemGroup.Any
    or skinType ~= CollectionConfig.EItemType.Any
    or weaponChoice ~= 0
    or not self._realWeaponSkinItems 
    or #self._realWeaponSkinItems == 0
    then
        self._realWeaponSkinItems = CollectionLogic.GetWeaponSkins(skinGroup, skinType, weaponChoice, nil, nil ,nil, true, true)        
    end
    if skinGroup == CollectionConfig.EItemGroup.Any
    and skinType == CollectionConfig.EItemType.Any
    and weaponChoice == 0 then
        Module.Collection.Field:SetSkinList(self._realWeaponSkinItems)
    end
    local filterSkins
    if table.nums(selectedQualityIDs) > 0 then
        filterSkins = {}
        for index, skinItem in ipairs(self._realWeaponSkinItems) do
            if selectedQualityIDs[skinItem.quality] == true then
                table.insert(filterSkins, skinItem)
            end
        end
        self._realWeaponSkinItems = filterSkins
    end
    if table.nums(selectedGunTypeIDs) > 0 then
        filterSkins = {}
        for index, skinItem in ipairs(self._realWeaponSkinItems) do
            if selectedGunTypeIDs[skinItem.itemSubType] == true then
                table.insert(filterSkins, skinItem)
            end
        end
        self._realWeaponSkinItems = filterSkins
    end
    filterSkins = {}
    for index, skinItem in ipairs(self._realWeaponSkinItems) do
        if Server.CollectionServer:IsVisibleProp(skinItem.id) then
            table.insert(filterSkins, skinItem)
        end
    end
    self._realWeaponSkinItems = filterSkins
    table.sort(self._realWeaponSkinItems, self._srotFuncList[self._sortMode+1])
    self._weaponSkinItems = self._realWeaponSkinItems
    self:_UpdateWeaponGroups()
    self._mysticalWeaponSkinItems = {}
    if bResetList then
        self._bApplied = false
        self._selectedCell = nil
        self._selectedPos = -1
        self._wtFromMandelBrickBtn:SetInfo(string.format(CollectionConfig.Loc.MandelDraw, tostring(0)), "PaperSprite'/Game/UI/UIAtlas/CommonHud/BakedSprite/CommonHud_MapMarker_Icon_0202.CommonHud_MapMarker_Icon_0202'")
        self._wtFromAuctionBtn:SetInfo(CollectionConfig.Loc.FromMarket, "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_De_Buy.CommonHall_De_Buy'")
        self._wtFromMatrixWorkshopBtn:SetInfo(CollectionConfig.Loc.FromMatrixWorkshop, "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Warehouse_Icon_0106.CommonHall_Warehouse_Icon_0106'")    
        if #self._weaponSkinItems > 0 then
            self._selectedPos = 0
            local item = self._weaponSkinItems[self._selectedPos+1]
            if isvalid(item) and not item.randomSkinTag then
                local items
                if ItemHelperTool.IsMysticalSkin(item.id) == true then
                    self._mysticalWeaponSkinItems = CollectionLogic.GetMysticalSkinInstanceById(item.id)
                    items = self._mysticalWeaponSkinItems
                else
                    items = {item}
                end
                if items and #items > 0 and Server.CollectionServer:IsPropWithRedDot(items[1].id) then
                    Timer.DelayCall(1, function ()
                        CollectionLogic.RemoveRedDots(items)
                    end)
                end 
                LogAnalysisTool.AddSkinIDs(item.id)
            end
        end
        self._wtWeaponSkinScrollView:RefreshAllItems()
    else
        if #self._weaponSkinItems > 0 then
            self._wtWeaponSkinScrollView:RefreshVisibleItems()
        else
            self._wtWeaponSkinScrollView:RefreshAllItems()
        end
    end
    if #self._weaponSkinItems == 0 then
        self._wtEmptyBg:SelfHitTestInvisible()
        self._wtEmptySlot:SelfHitTestInvisible()
    else
        if not bResetList then
            local item = self._weaponSkinItems[self._selectedPos+1]
            if isvalid(item) and not item.randomSkinTag and ItemHelperTool.IsMysticalSkin(item.id) == true then
                self._mysticalWeaponSkinItems = CollectionLogic.GetMysticalSkinInstanceById(item.id)
            end
        end
        self._wtEmptyBg:Collapsed()
        self._wtEmptySlot:Collapsed()
    end
    self:_RefreshItemUI(bResetList)
end

function CollectionsWeaponSkinPagePanel:_OnGetTabItemCount()
    return #self._dropDownDataList
end

function CollectionsWeaponSkinPagePanel:_OnGetItemsCount()
    return #self._weaponSkinItems
end

function CollectionsWeaponSkinPagePanel:_OnProcessItemWidget(position, itemWidget)
    local item = self._weaponSkinItems[position]
    itemWidget:SetSize(548, 254)
    itemWidget:SetCppValue("bIsFocusable", IsHD() and WidgetUtil.IsGamepad())
    itemWidget:SetCppValue("bHandleClick", IsHD() and WidgetUtil.IsGamepad())
    itemWidget:SetButtonEnable(not IsHD() or not WidgetUtil.IsGamepad())
    if isvalid(item) then
        itemWidget:BindClickCallback(CreateCallBack(self._OnWeaponSkinItemClick, self,itemWidget, position))
        local fCheckFunc = CreateCallBack(function(self, curUpdateReddotObType)
            if item.randomSkinTag then
                return false
            else
                return Server.CollectionServer:IsPropWithRedDot(item.id)
            end
        end,self)
        if isvalid(self._redDotInsMap[itemWidget]) and self._redDotInsMap[itemWidget]:GetIsValid() then
            Module.ReddotTrie:UpdateDynamicReddot(itemWidget:GetItemView(), EReddotTrieObserverType.Collection, ECollectionDynamicDataType.WeaponSkin, self._redDotInsMap[itemWidget], fCheckFunc, nil)
        else
            self._redDotInsMap[itemWidget] = Module.ReddotTrie:RegisterDynamicReddot(EReddotTrieObserverType.Collection, ECollectionDynamicDataType.WeaponSkin, fCheckFunc, nil ,itemWidget:GetItemView(), {EReddotType.Normal})
        end
        if item.randomSkinTag then
            itemWidget:InitCollectionRandomSkinItem(item)
        else
            if self._mysticalWeaponSkinItems and #self._mysticalWeaponSkinItems > 0 and item.id == self._mysticalWeaponSkinItems[1].id then
                if self._mysticalWeaponSkinItems[self._selectedMysticalPos+1] then
                    item = self._mysticalWeaponSkinItems[self._selectedMysticalPos+1]
                else
                    item = self._mysticalWeaponSkinItems[1]
                end
            end
            itemWidget:InitCollectionWeaponSkinItem(item)
        end
        if self._weaponGroupTitleMap[position] then
            itemWidget:EnableTitle(true, self._weaponGroupTitleMap[position])
        else
            itemWidget:EnableTitle(false) 
        end
    else
        itemWidget:EnableTitle(false)
    end
    itemWidget:SetSelected(self._selectedPos == position-1)
    if self._selectedPos == position-1 then
        self._selectedCell = itemWidget
        WidgetUtil.SetUserFocusToWidget(self._selectedCell, true)
    end
end

function CollectionsWeaponSkinPagePanel:_OnGetMysticalWeaponSkinCount()
    return #self._mysticalWeaponSkinItems or 0
end
function CollectionsWeaponSkinPagePanel:_OnProcessMysticalWeaponSkinWidget(position, itemWidget)
    --AssemblyPresetItem
    local item = self._mysticalWeaponSkinItems[position + 1]
    if item then
        local fItemOnClicked = CreateCallBack(self._OnMysticalWeaponSkinItemClick, self)
        local baseWeaponId = CollectionLogic.GetBaseWeaponIdFromSkinId(item.id)
        itemWidget:InitCollectionWeaponSkinData(
            position, 
            item, 
            baseWeaponId,
            CollectionLogic.CheckIfSkinAppliedOnWeapon(item.id, item.gid) and not CollectionLogic.CheckIsRandomPoolEnabled(baseWeaponId), 
            CollectionLogic.CheckIsSkinInRandomPool(item.id, item.gid),
            fItemOnClicked)
        itemWidget:SetSelected(self._selectedMysticalPos == position)
    end
    if self._selectedMysticalPos == position then
        self._selectedMysticalCell = itemWidget
    end
end

function CollectionsWeaponSkinPagePanel:_OnWeaponSkinItemClick(itemCell, position)
    if self._wtMysticalSkinDropDown:IsChecked() == true and self._selectedCell then
        local lastItem = self._weaponSkinItems[self._selectedPos+1]
        local weaponDesc = self._selectedCell.item:GetRawDescObj()
        local weaponSkinInfoParam = weaponDesc:GetPbWeaponSkinInfoParam()
        if self._selectedCell.item.id == lastItem.id or weaponSkinInfoParam.SkinId == lastItem.id then
            self._selectedCell:InitCollectionWeaponSkinItem(lastItem)
        end
    end
    if self._selectedPos ~= position-1 then
        self._bApplied = false
        if self._selectedCell then
            self._selectedCell:SetSelected(false)
            self._selectedCell:SetCppValue("bHandleClick", IsHD() and WidgetUtil.IsGamepad())
        end
        local item = self._weaponSkinItems[position]
        if item.randomSkinTag then
        else
            local items = nil
            self._mysticalWeaponSkinItems = {}
            if item then
                LogAnalysisTool.AddSkinIDs(item.id)
                if ItemHelperTool.IsMysticalSkin(item.id) == true then
                    self._mysticalWeaponSkinItems = CollectionLogic.GetMysticalSkinInstanceById(item.id)
                end
            end
            if #self._mysticalWeaponSkinItems > 0 then
                items = self._mysticalWeaponSkinItems
                self._weaponSkinItems[position] = self._mysticalWeaponSkinItems[1]
                itemCell:InitCollectionWeaponSkinItem(self._mysticalWeaponSkinItems[1], true)
            elseif item then
                items = {item}
            end
            if items and #items > 0 and Server.CollectionServer:IsPropWithRedDot(items[1].id) then
                CollectionLogic.RemoveRedDots(items)
            end 
        end
        self._selectedCell = itemCell
        self._selectedCell:SetSelected(true)
        self._selectedCell:SetCppValue("bHandleClick", false)
    end
    if self._selectedPos ~= position-1 or self._wtMysticalSkinDropDown:IsChecked() == true then
        self._selectedPos = position-1
        self:_RefreshItemUI(true)
    end
end

function CollectionsWeaponSkinPagePanel:_OnMysticalWeaponSkinItemClick(itemCell, position)
    if self._selectedMysticalPos ~= position then
        self._bApplied = false
        if isvalid(self._selectedMysticalCell) then
            if self._selectedMysticalPos ~= position then
                self._selectedMysticalCell:SetSelected(false)
            end
        end
        local item = self._mysticalWeaponSkinItems[position + 1]
        self._selectedMysticalCell = itemCell
        if self._selectedMysticalPos ~= position then
            self._selectedMysticalCell:SetSelected(true)
        end
        self._selectedMysticalPos = position
        if isvalid(item) then
            self._wtApplyBtn:SelfHitTestInvisible()
            self:_AddInputActionForApplyBtn()
            if CollectionLogic.CheckIfSkinAppliedOnWeapon(item.id, item.gid) and not CollectionLogic.CheckIsRandomPoolEnabled(CollectionLogic.GetBaseWeaponIdFromSkinId(item.id)) then
                self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.AppearanceApplied)
                self._wtApplyBtn:SetIsEnabled(false)
                -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
                self:_RemoveInputActionForApplyBtn()
                -- END MODIFICATION
            else
                self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.ApplyAppearance)
                self._wtApplyBtn:SetIsEnabled(true)
            end
            if IsHD() then
                self._shotcutList = {}
                if self._bHideUI then
                    table.insert(self._shotcutList, {actionName = "CollectionShowUI",func = self._ShowUI, caller = self ,bUIOnly = false, bHideIcon = false})
                else
                    table.insert(self._shotcutList, {actionName = "CollectionHideUI",func = self._HideUI, caller = self ,bUIOnly = false, bHideIcon = false}) 
                end
                if CollectionLogic.CheckIsSkinInRandomPool(item.id, item.gid) then
                    table.insert(self._shotcutList, {actionName = "GunSkin_RemoveFromRandomPool",func = self._RemoveFromRandomSkinPool, caller = self ,bUIOnly = false, bHideIcon = false})
                else
                    table.insert(self._shotcutList, {actionName = "GunSkin_AddToRandomPool",func = self._AddToRandomSkinPool, caller = self ,bUIOnly = false, bHideIcon = false})
                end
                table.insert(self._shotcutList, {actionName = "GunSkin_Trade",func = self._JumpToTradePage, caller = self ,bUIOnly = false, bHideIcon = false})
                CollectionLogic.RegStackUIInputSummary(self._shotcutList, self._bHideUI)
            else
                self._wtToggleRandomBtn:AsyncSetImageIconPathAllState(
                    CollectionLogic.CheckIsSkinInRandomPool(item.id, item.gid)
                    and "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Random_02.CommonHall_Random_02'"
                    or "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Random_01.CommonHall_Random_01'"
                )
                self._wtToggleRandomBtn:SelfHitTestInvisible()
            end
            self._wtItemDetailView:UpdateItem(item)
            self._wtItemDetailView:SetShowkillCnt(false)
            self._wtItemDetailView:SelfHitTestInvisible()
            if self._selectedCell then
                self._selectedCell:InitCollectionWeaponSkinItem(item, true)
            end
            self:OnRefreshModel(ESubStage.HallCollectionNew)
        end
    end
end

function CollectionsWeaponSkinPagePanel:_RefreshItemUI(bReset)
    local item = self._weaponSkinItems[self._selectedPos+1] or self._mysticalWeaponSkinItems[self._selectedMysticalPos+1]
    if bReset or #self._weaponSkinItems == 0 then
        self._wtApplyBtn:Collapsed()
        self._bEnableApplyBtn = false
        self._wtMysticalSkinBtnPanel:Collapsed()
        self._wtMysticalSkinDropDown:Collapsed()
        if self._wtMysticalSkinDropDown:IsChecked() == true and not self._bIgnoredDropDownState then
            self._wtMysticalSkinDropDown:SwitchCheckButtonState(ECheckButtonState.Unchecked)
        end
        self._bIgnoredDropDownState = false
        self._wtWeaponSkinMissionProgressPanel:Collapsed()
        self._wtDetailBtn:Collapsed()
        self._wtDetailBtn_PC:Collapsed()
        self._wtTradeBtn:Collapsed()
        self._wtShareBtn:Collapsed()
        self._wtToggleRandomBtn:Collapsed()
        self._wtHideUIBtn:Collapsed()
        self._wtAlertHintBox:Collapsed()
        self._wtQuestionHintBox:Collapsed()
        self._wtItemDetailView:Collapsed()
        self._wtRandomInfoPanel:Collapsed()
        self._shotcutList = {}
        self._selectedMysticalPos = -1
        self._selectedMysticalCell = nil
        if isvalid(item) then
            self._wtApplyBtn:SetIsEnabled(true)
            self._wtApplyBtn:SetIsEnabledStyle(true)
            if IsHD() then
                if self._bHideUI then
                    table.insert(self._shotcutList, {actionName = "CollectionShowUI",func = self._ShowUI, caller = self ,bUIOnly = false, bHideIcon = false})
                else
                    table.insert(self._shotcutList, {actionName = "CollectionHideUI",func = self._HideUI, caller = self ,bUIOnly = false, bHideIcon = false}) 
                end
                --table.insert(self._shotcutList, {actionName = "GunSkin_ShowDetail",func = self._ShowSkinDetailPage, caller = self ,bUIOnly = false, bHideIcon = false})
                self._wtDetailBtn_PC:Visible()
                self:_DownLoadResources()
            else
                self._wtDetailBtn:Visible()
                --self._wtHideUIBtn:Visible()
            end
            if item.randomSkinTag then
                self._wtQuestionHintBox:SelfHitTestInvisible()
                self._wtQuestionHintText:SetText(CollectionConfig.Loc.RandomSkinApplyHint)
                self._wtApplyBtn:SetIsEnabledStyle(not CollectionLogic.CheckIsRandomPoolEnabled(item.baseWeaponId) and CollectionLogic.CheckAreAnySkinsInRandomPool(item.baseWeaponId))
                if CollectionLogic.CheckIsRandomPoolEnabled(item.baseWeaponId) then
                    self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.AppearanceApplied)
                else
                    self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.ApplyAppearance)
                end
                self._wtApplyBtn:SelfHitTestInvisible()
                self._bEnableApplyBtn = true
                if IsHD() then
                    self._wtDetailBtn_PC:SetMainTitle(CollectionConfig.Loc.ManageRandomSkinPool)
                else
                    self._wtDetailBtn:SetMainTitle(CollectionConfig.Loc.ManageRandomSkinPool)
                end
            else
                if IsHD() then
                    self._wtDetailBtn_PC:SetMainTitle(CollectionConfig.Loc.ViewDetail)
                else
                    self._wtDetailBtn:SetMainTitle(CollectionConfig.Loc.ViewDetail)
                end
                if Server.CollectionServer:IsOwnedWeaponSkin(item.id) == true then
                    if CollectionLogic.CheckIfSkinAppliedOnWeapon(item.id, item.gid) and not CollectionLogic.CheckIsRandomPoolEnabled(CollectionLogic.GetBaseWeaponIdFromSkinId(item.id))  then
                        self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.AppearanceApplied)
                        self._wtApplyBtn:SetIsEnabled(false)
                    else
                        self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.ApplyAppearance)
                        self._bEnableApplyBtn = true
                    end
                    if IsHD() then
                        if CollectionLogic.CheckIsSkinInRandomPool(item.id, item.gid) then
                            table.insert(self._shotcutList, {actionName = "GunSkin_RemoveFromRandomPool",func = self._RemoveFromRandomSkinPool, caller = self ,bUIOnly = false, bHideIcon = false})
                        else
                            table.insert(self._shotcutList, {actionName = "GunSkin_AddToRandomPool",func = self._AddToRandomSkinPool, caller = self ,bUIOnly = false, bHideIcon = false})
                        end
                    else
                        self._wtToggleRandomBtn:AsyncSetImageIconPathAllState(
                            CollectionLogic.CheckIsSkinInRandomPool(item.id, item.gid)
                            and "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Random_02.CommonHall_Random_02'"
                            or "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Random_01.CommonHall_Random_01'"
                        )
                        self._wtToggleRandomBtn:SelfHitTestInvisible()
                    end
                    if ItemHelperTool.IsMysticalSkin(item.id) == true then
                        self._wtMysticalSkinDropDown:SelfHitTestInvisible()
                        self._wtMysticalSkinDropDown:SetMainTabText(string.format(CollectionConfig.Loc.MysticalSkinType, tostring(#self._mysticalWeaponSkinItems)))
                        if #self._mysticalWeaponSkinItems > 0 then
                            self._selectedMysticalPos = 0
                        end
                        self._wtMysticalSkinDropDown:RefreshTab()
                        if IsHD() then
                            table.insert(self._shotcutList, {actionName = "GunSkin_Trade",func = self._JumpToTradePage, caller = self ,bUIOnly = false, bHideIcon = false})
                        else
                            self._wtTradeBtn:Visible()
                            Module.Share:FuncPointUnLock(SwitchSystemID.SubShareMetaphysicalGunSkin, self._wtShareBtn)
                        end
                    else
                        if not IsHD() then
                            Module.Share:FuncPointUnLock(item.quality >= ItemConfig.EWeaponSkinQualityType.Orange and SwitchSystemID.SubShareAdvancedGunSkin or SwitchSystemID.SubShareSObtainLowGunSkin, self._wtShareBtn)
                        end
                    end
                    if Server.CollectionServer:IsRightsProp(item.id, item.gid) == true then
                        local weaponSkinMissionStatusCallback = CreateCallBack(self._OnWeaponSkinMissionStatusLoaded, self)
                        self._wtWeaponSkinMissionProgressPanel:LoadWeaponSkinProgress(item.id, weaponSkinMissionStatusCallback)
                    end
                    self._wtApplyBtn:SelfHitTestInvisible()
                    -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
                    if IsHD() and WidgetUtil.IsGamepad() then
                        self:_RegisterDynamicNavConfig()
                    end
                    -- END MODIFICATION
                else
                    if ItemHelperTool.IsMysticalSkin(item.id) == true then
                        local mandelBrickId = CollectionLogic.GetMandelBrickIdBySkinId(item.id)
                        local mandelBrickItem = Server.CollectionServer:GetCollectionItemById(mandelBrickId)
                        self._wtFromMandelBrickBtn:SetInfo(string.format(CollectionConfig.Loc.MandelDraw, tostring(mandelBrickItem ~= nil and mandelBrickItem.num or 0)), "PaperSprite'/Game/UI/UIAtlas/CommonHud/BakedSprite/CommonHud_MapMarker_Icon_0202.CommonHud_MapMarker_Icon_0202'")
                        self._wtMysticalSkinBtnPanel:SelfHitTestInvisible()
                        self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.ProceedToUnlock)
                        self._wtApplyBtn:SetIsEnabled(false)
                    else
                        local itemConfigRow = ItemConfigTool.GetItemConfigById(item.id)
                        local bHasConfig = false
                        self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.ProceedToUnlock)
                        if itemConfigRow then
                            if itemConfigRow.JumpID ~= nil and itemConfigRow.JumpID ~= 0 then
                                if itemConfigRow.ButtonDes ~= nil and itemConfigRow.ButtonDes ~= "" then
                                    self._wtAlertHintTxt:SetText(itemConfigRow.ButtonDes)
                                    self._wtAlertHintBox:SelfHitTestInvisible()
                                end
                                local bCanJump = Module.Jump:CheckCanJumpByID(itemConfigRow.JumpID, {bNoShowLoadWindow=true})
                                self._wtApplyBtn:SetIsEnabledStyle(bCanJump)
                                self._bEnableApplyBtn = true
                                bHasConfig = true
                            elseif itemConfigRow.EndButtonDes ~= nil and itemConfigRow.EndButtonDes ~= "" then
                                self._wtAlertHintTxt:SetText(itemConfigRow.EndButtonDes)
                                self._wtAlertHintBox:SelfHitTestInvisible()
                                self._wtApplyBtn:SetIsEnabled(false)
                                bHasConfig = true
                            end
                        end
                        if not bHasConfig then
                            if CollectionLogic.IsItemInStore(item.id) == true then
                                local priceStr = CollectionLogic.GePriceStrByItemId(item.id)
                                self._wtApplyBtn:SetMainTitle(priceStr)
                                self._bEnableApplyBtn = true
                            else
                                self._wtApplyBtn:SetIsEnabled(false)
                            end
                        end
                        self._wtApplyBtn:SelfHitTestInvisible()
                    end
                    local weaponSkinMissionStatusCallback = CreateCallBack(self._OnWeaponSkinMissionStatusLoaded, self)
                    self._wtWeaponSkinMissionProgressPanel:LoadWeaponSkinProgress(item.id, weaponSkinMissionStatusCallback)
                    -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
                    if IsHD() and WidgetUtil.IsGamepad() then
                        self:_RegisterDynamicNavConfig()
                    end
                    -- END MODIFICATION
                end
            end
        end
    end
    if isvalid(item) then
        if item.randomSkinTag then
            self._wtRandomItemNameTxt:SetText(StringUtil.Key2StrFormat(Module.Collection.Config.Loc.RandomSkinItemTitle,
                {["FirearmName"] = item.name
            }))
            --self._wtRandomItemDescTxt:SetText()
            self._wtItemDetailView:Hidden()
            self._wtRandomInfoPanel:SelfHitTestInvisible()
        else
            self._wtItemDetailView:UpdateItem(item)
            self._wtItemDetailView:SetShowkillCnt(false)
            self._wtItemDetailView:SelfHitTestInvisible()
            if self._wtMysticalSkinDropDown:IsVisible() == true then
                self._wtMysticalSkinDropDown:SetMainTabText(string.format(CollectionConfig.Loc.MysticalSkinType, tostring(#self._mysticalWeaponSkinItems)))
                self._wtMysticalSkinDropDown:RefreshTab()
            end
        end
    end
    self:_RefreshDownloadBtn()
    if self._bEnableApplyBtn then
        self:_AddInputActionForApplyBtn()
    else
        self:_RemoveInputActionForApplyBtn()
    end
    CollectionLogic.RegStackUIInputSummary(self._shotcutList, self._bHideUI)
    self:OnRefreshModel(ESubStage.HallCollectionNew)
    self:UpdateBackground()
end

function CollectionsWeaponSkinPagePanel:OnRefreshModel(curSubStageType)
    if not curSubStageType or curSubStageType == ESubStage.HallCollectionNew then
        local item = nil
        if self._selectedMysticalPos ~= -1 then
            item = self._mysticalWeaponSkinItems[self._selectedMysticalPos+1]
        else
            item = self._weaponSkinItems[self._selectedPos+1]
            if isvalid(item) and item.randomSkinTag then
                local ownedSkins = Server.CollectionServer:GetOwnedWeaponSkins()
                if CollectionLogic.IsInMp() then
                    local weaponProp = CollectionLogic.getWeaponPropFromInventory(item.baseWeaponId)
                    if weaponProp and ownedSkins[weaponProp.weapon.skin_id] then
                        item = ownedSkins[weaponProp.weapon.skin_id][weaponProp.weapon.skin_gid] or item   
                    end 
                else
                    if ownedSkins[Server.InventoryServer:GetSOLWeaponSkinIDFromBaseWeaponID(item.baseWeaponId)] then
                        item = ownedSkins[Server.InventoryServer:GetSOLWeaponSkinIDFromBaseWeaponID(item.baseWeaponId)][Server.InventoryServer:GetSOLWeaponSkinGUIDFromBaseWeaponID(item.baseWeaponId)]  or item   
                    end  
                end
            end
        end
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallCollectionNew, "ResetDisplayItem")
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallCollectionNew, "ResetWeapon")
        if isvalid(item) then
            local weaponDesc, partIndexs
            if item.randomSkinTag then
                local weaponItem = WeaponAssemblyTool.GetPreviewGunItemBaseFromRecId(item.baseWeaponId)
                local presetConfig = UGunPresetTableManager.Get():GetGunByItemId(weaponItem.id)
                if presetConfig ~= nil then
                    weaponDesc, partIndexs = UAssembleWeaponDataLibrary.GetWeaponDescAndPartIndexsFromPreset(presetConfig, partIndexs)
                end
                if isvalid(weaponDesc) then
                    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallCollectionNew, "SetDisplayWeaponAutoBoundAdapter", weaponDesc, true, false)
                end 
            else
                weaponDesc, partIndexs = WeaponHelperTool.GetWeaponDescriptionFromSkinID(item.id)
                if isvalid(weaponDesc) then
                    if item.gid ~= 0 then
                        WeaponAssemblyTool.SetWeaponDescriptionSkinInfoFromPropInfo(weaponDesc, item:GetRawPropInfo())
                    end
                    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallCollectionNew, "SetDisplayWeaponAutoBoundAdapter", weaponDesc, true, item.itemSubType == ItemConfig.EWeaponItemType.Melee)
                end
            end
        end
    end
end

function CollectionsWeaponSkinPagePanel:OnTertiaryTabIndexChanged(TabIndex, LastTabIndex)
    local tabInfo = Module.Collection.Field:GetTabInfo()
    if tabInfo[self._mainTabIndex] and tabInfo[self._mainTabIndex].subTabList[TabIndex] then
        local subTabId = tabInfo[self._mainTabIndex].subTabList[TabIndex].subTabId
        local gunType = self._subTabIdToGunType[subTabId] or 0
        if gunType > 0 then
            CollectionLogic.SetGunSkinFilterGunType(gunType)
        else
            CollectionLogic.ClearGunSkinFilterGunType()
        end
        CollectionLogic.UpdateLastGunSkinFilterData()
    end
end

function CollectionsWeaponSkinPagePanel:_OnWeaponSkinMissionStatusLoaded(skinId, bCanRetrieve, bIsChallengeSkin, bIsMasterChallengeSkin)
    local item = self._weaponSkinItems[self._selectedPos+1]
    if item and item.id == skinId then
        self._challengeSkinId = nil
        if bIsChallengeSkin then
            self._challengeSkinId = skinId
        end
        self._bIsMasterChallengeSkin = bIsMasterChallengeSkin
        self._canRetrieveSkinId = nil
        if bCanRetrieve == true then
            self._canRetrieveSkinId = skinId
        end
        if Server.CollectionServer:IsOwnedWeaponSkin(item.id) == true then
            if CollectionLogic.CheckIfSkinAppliedOnWeapon(item.id, item.gid) and not CollectionLogic.CheckIsRandomPoolEnabled(CollectionLogic.GetBaseWeaponIdFromSkinId(item.id))  then
                self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.AppearanceApplied)
                self._wtApplyBtn:SetIsEnabled(false)
                -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
                self:_RemoveInputActionForApplyBtn()
                -- END MODIFICATION
            else
                self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.ApplyAppearance)
            end
            self._wtApplyBtn:SelfHitTestInvisible()
        else
            self._wtAlertHintTxt:SetText(bIsMasterChallengeand and CollectionConfig.Loc.CompleteMasterChallengeToRetrieve or CollectionConfig.Loc.CompleteChallengeToRetrieve)
            self._wtAlertHintBox:SelfHitTestInvisible()
            if bIsChallengeSkin then
                self._wtApplyBtn:SetMainTitle(bCanRetrieve and CollectionConfig.Loc.RetrieveWeaponSkin or CollectionConfig.Loc.ViewChallenge)
            else
                self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.RetrieveWeaponSkin)
            end
            self._wtApplyBtn:SetIsEnabled(bCanRetrieve or bIsChallengeSkin)
            self._wtApplyBtn:SetIsEnabledStyle(true)
            if not ItemHelperTool.IsMysticalSkin(item.id) then
                self._wtApplyBtn:SelfHitTestInvisible()
            end
        end
    end
    -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
    if IsHD() and WidgetUtil.IsGamepad() then
        self:_RegisterDynamicNavConfig()
    end
    -- END MODIFICATION
end

function CollectionsWeaponSkinPagePanel:_JumpToMandelBrickPage()
    if self._weaponSkinItems[self._selectedPos+1] then
        CollectionLogic.ShowMandelBrickPage(CollectionLogic.GetMandelBrickIdBySkinId(self._weaponSkinItems[self._selectedPos+1].id), 0, EMandelOpenSource.Collection)
    end
end

function CollectionsWeaponSkinPagePanel:_JumpToAuctionPage()
    local item = self._weaponSkinItems[self._selectedPos + 1]
    if item ~= nil then
        CollectionLogic.ShowMarketPage()
    end
end

function CollectionsWeaponSkinPagePanel:_JumpToMysticalWorkshopPage()
    local tabInfo = Module.Collection.Field:GetTabInfo()
    for index, mainTabInfo in ipairs(tabInfo) do
        if mainTabInfo.mainTabId == 6 then
            Module.CommonBar:CheckTopTabGroup(index, true, 2)
            break
        end
    end
end

function CollectionsWeaponSkinPagePanel:_JumpToAppearanceChallengePage()
    local tabInfo = Module.Collection.Field:GetTabInfo()
    for mainTabIndex, mainTabInfo in ipairs(tabInfo) do
        if mainTabInfo.mainTabId == 8 then
            for subTabIndex, subTabInfo in ipairs(mainTabInfo.subTabList) do
                if (not self._bIsMasterChallengeSkin and subTabInfo.subTabId == 1) or (self._bIsMasterChallengeSkin and subTabInfo.subTabId == 2) then
                    local item = self._weaponSkinItems[self._selectedPos + 1]
                    if subTabInfo.subTabId == 1 then
                        if item ~= nil then
                            Module.Collection.Field:SetSelectedPropId(item.id)
                        end
                    end
                    Module.CommonBar:CheckTopTabGroup(mainTabIndex, true, 2)
                    if subTabInfo.subTabId == 2 then
                        if item ~= nil then
                            Module.Collection.Field:SetSelectedPropId(item.id)
                        end
                    end
                    Module.CommonBar:CheckTopTabGroup(subTabIndex, true, 3)
                    break
                end
            end
            break
        end
    end
end

function CollectionsWeaponSkinPagePanel:_JumpToTradePage()
    if self._selectedMysticalPos ~= -1 then
        Module.Market:JumpToMysticalSkinSellPopWindow(self._mysticalWeaponSkinItems[self._selectedMysticalPos+1])
    else
        Module.Market:JumpToMysticalSkinSellPopWindow(self._weaponSkinItems[self._selectedPos+1])
    end
end

function CollectionsWeaponSkinPagePanel:_ToggleRandomSkin()
    local item
    if self._selectedMysticalPos ~= -1 then
        item = self._mysticalWeaponSkinItems[self._selectedMysticalPos+1]
    else
        item = self._weaponSkinItems[self._selectedPos+1] 
    end
    if isvalid(item) and not item.randomSkinTag then
        if CollectionLogic.CheckIsSkinInRandomPool(item.id, item.gid) then
            self:_RemoveFromRandomSkinPool()
        else
            self:_AddToRandomSkinPool()
        end
    end
end

function CollectionsWeaponSkinPagePanel:_AddToRandomSkinPool()
    if self._bUpdatingRandomPool then
        return
    end
    local item
    if self._selectedMysticalPos ~= -1 then
        item = self._mysticalWeaponSkinItems[self._selectedMysticalPos+1]
    else
        item = self._weaponSkinItems[self._selectedPos+1] 
    end
    if isvalid(item) and not item.randomSkinTag then
        local fCallbackIns = CreateCallBack(function(self, res)
            if res.result == 0 then
                Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.AddedToRandomSkinPoolTip)
            end
            self._bUpdatingRandomPool = false
        end,self)
        self._bUpdatingRandomPool = true
        CollectionLogic.UpdateRandomSkin(item.id, item.gid, true, fCallbackIns)
    end
end

function CollectionsWeaponSkinPagePanel:_RemoveFromRandomSkinPool()
    if self._bUpdatingRandomPool then
        return
    end
    local item
    if self._selectedMysticalPos ~= -1 then
        item = self._mysticalWeaponSkinItems[self._selectedMysticalPos+1]
    else
        item = self._weaponSkinItems[self._selectedPos+1] 
    end
    if isvalid(item) and not item.randomSkinTag then
        local fCallbackIns = CreateCallBack(function(self, res)
            if res.result == 0 then
                Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.RemovedFromRandomSkinPoolTip)
            end
            self._bUpdatingRandomPool = false
        end,self)
        self._bUpdatingRandomPool = true
        CollectionLogic.UpdateRandomSkin(item.id, item.gid, false, fCallbackIns)
    end
end

function CollectionsWeaponSkinPagePanel:_CheckIsFirstIndexOfWeaponGroup(index)
    if self._sortMode and self._sortMode < 2 then
        if self._realWeaponSkinItems and self._realWeaponSkinItems[index] then
            if self._realWeaponSkinItems[index-1] then
                if Server.CollectionServer:IsPropWithRedDot(self._realWeaponSkinItems[index].id) then
                    return false
                else
                    local preBaseWeaponId = CollectionLogic.GetBaseWeaponIdFromSkinId(self._realWeaponSkinItems[index].id)
                    local baseWeaponId = CollectionLogic.GetBaseWeaponIdFromSkinId(self._realWeaponSkinItems[index-1].id)
                    return preBaseWeaponId ~= baseWeaponId or Server.CollectionServer:IsPropWithRedDot(self._realWeaponSkinItems[index-1].id)
                end
            else
                return true
            end
        end
    end
    return false
end

function CollectionsWeaponSkinPagePanel:_UpdateWeaponGroups()
    self._weaponGroupTitleMap = {}
    if self._sortMode and self._sortMode < 2 then
        local filterSkins = {}
        local weaponAvailableForRandomSkin = {}
        for index, skinItem in ipairs(self._realWeaponSkinItems) do
            if self:_CheckIsFirstIndexOfWeaponGroup(index) then
                local name = CollectionConfig.Loc.UnknownWeapon
                local baseWeaponId = CollectionLogic.GetBaseWeaponIdFromSkinId(skinItem.id)
                local itemConfigRow = ItemConfigTool.GetItemConfigById(baseWeaponId)
                if itemConfigRow and not string.isempty(itemConfigRow.Name) then
                    name = itemConfigRow.Name
                end
                if Server.CollectionServer:IsOwnedWeaponSkin(skinItem.id) == true then
                    weaponAvailableForRandomSkin[baseWeaponId] = true
                end
                if Server.CollectionServer:IsPropWithRedDot(skinItem.id) then
                    self._weaponGroupTitleMap[#filterSkins+1] = CollectionConfig.Loc.CurrentReceived
                else
                    self._weaponGroupTitleMap[#filterSkins+1] = name
                    if self._bCanShowRandomSkinItem and weaponAvailableForRandomSkin[baseWeaponId] then
                        table.insert(filterSkins, {randomSkinTag = true, baseWeaponId = baseWeaponId, name = name})
                    end
                end
            end
            if self._selectedCell and self._selectedCell.item and self._selectedCell.item.id == skinItem.id and self._selectedCell.item.gid == skinItem.gid then
                self._selectedPos = #filterSkins
            end
            table.insert(filterSkins, skinItem)
        end
        self._weaponSkinItems = filterSkins
    end
end

function CollectionsWeaponSkinPagePanel:_OnRandomSkinPoolUpdate()
    self._wtWeaponSkinScrollView:RefreshVisibleItems()
    local item
    if self._selectedMysticalPos ~= -1 then
        item = self._mysticalWeaponSkinItems[self._selectedMysticalPos+1]
    else
        item = self._weaponSkinItems[self._selectedPos+1] 
    end
    if isvalid(item) then
        if item.randomSkinTag then
            self._wtApplyBtn:SetIsEnabled(true)
            self._wtApplyBtn:SetIsEnabledStyle(not CollectionLogic.CheckIsRandomPoolEnabled(item.baseWeaponId) and CollectionLogic.CheckAreAnySkinsInRandomPool(item.baseWeaponId))
            if CollectionLogic.CheckIsRandomPoolEnabled(item.baseWeaponId) then
                self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.AppearanceApplied)
            else
                self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.ApplyAppearance)
            end
            self._wtApplyBtn:SelfHitTestInvisible()
        elseif Server.CollectionServer:IsOwnedWeaponSkin(item.id) then
            if self._selectedCell and self._selectedCell.item and self._selectedCell.item.id == item.id then
                self._selectedCell:InitCollectionWeaponSkinItem(item, true)
            end
            if self._selectedMysticalCell then
                local fItemOnClicked = CreateCallBack(self._OnMysticalWeaponSkinItemClick, self)
                local baseWeaponId = CollectionLogic.GetBaseWeaponIdFromSkinId(item.id)
                self._selectedMysticalCell:InitCollectionWeaponSkinData(
                    self._selectedMysticalPos, 
                    item, 
                    baseWeaponId, 
                    CollectionLogic.CheckIfSkinAppliedOnWeapon(item.id, item.gid) and not CollectionLogic.CheckIsRandomPoolEnabled(baseWeaponId),
                    CollectionLogic.CheckIsSkinInRandomPool(item.id, item.gid), 
                    fItemOnClicked
                )
            end
            if IsHD() then
                self._shotcutList = {}
                if self._bHideUI then
                    table.insert(self._shotcutList, {actionName = "CollectionShowUI",func = self._ShowUI, caller = self ,bUIOnly = false, bHideIcon = false})
                else
                    table.insert(self._shotcutList, {actionName = "CollectionHideUI",func = self._HideUI, caller = self ,bUIOnly = false, bHideIcon = false}) 
                end
                if CollectionLogic.CheckIsSkinInRandomPool(item.id, item.gid) then
                    table.insert(self._shotcutList, {actionName = "GunSkin_RemoveFromRandomPool",func = self._RemoveFromRandomSkinPool, caller = self ,bUIOnly = false, bHideIcon = false})
                else
                    table.insert(self._shotcutList, {actionName = "GunSkin_AddToRandomPool",func = self._AddToRandomSkinPool, caller = self ,bUIOnly = false, bHideIcon = false})
                end
                if ItemHelperTool.IsMysticalSkin(item.id) == true then
                    table.insert(self._shotcutList, {actionName = "GunSkin_Trade",func = self._JumpToTradePage, caller = self ,bUIOnly = false, bHideIcon = false})
                end
                CollectionLogic.RegStackUIInputSummary(self._shotcutList, self._bHideUI)
            else
                self._wtToggleRandomBtn:AsyncSetImageIconPathAllState(
                    CollectionLogic.CheckIsSkinInRandomPool(item.id, item.gid)
                    and "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Random_02.CommonHall_Random_02'"
                    or "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Random_01.CommonHall_Random_01'"
                )
                self._wtToggleRandomBtn:SelfHitTestInvisible()
            end
        end
    end
end

function CollectionsWeaponSkinPagePanel:_OnRandomSkinPoolToggled()
    self._wtWeaponSkinScrollView:RefreshVisibleItems()
    local item = self._weaponSkinItems[self._selectedPos+1]
    if isvalid(item) then
        if item.randomSkinTag then
            self._wtApplyBtn:SetIsEnabled(true)
            self._wtApplyBtn:SetIsEnabledStyle(not CollectionLogic.CheckIsRandomPoolEnabled(item.baseWeaponId) and CollectionLogic.CheckAreAnySkinsInRandomPool(item.baseWeaponId))
            if CollectionLogic.CheckIsRandomPoolEnabled(item.baseWeaponId) then
                self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.AppearanceApplied)
            else
                self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.ApplyAppearance)
            end
            self._wtApplyBtn:SelfHitTestInvisible()
        else
            self:_RefreshItemUI(true)
        end
    end
end
    
function CollectionsWeaponSkinPagePanel:_DownLoadResources()
    if not hasdestroy(self._wtCommonDownload) then
        local item = self._weaponSkinItems[self._selectedPos+1]
        if isvalid(item) and item.randomSkinTag then
            local ownedSkins = Server.CollectionServer:GetOwnedWeaponSkins()
            if CollectionLogic.IsInMp() then
                local weaponProp = CollectionLogic.getWeaponPropFromInventory(item.baseWeaponId)
                if weaponProp and ownedSkins[weaponProp.weapon.skin_id] then
                    item = ownedSkins[weaponProp.weapon.skin_id][weaponProp.weapon.skin_gid] or item   
                end 
            else
                if ownedSkins[Server.InventoryServer:GetSOLWeaponSkinIDFromBaseWeaponID(item.baseWeaponId)] then
                    item = ownedSkins[Server.InventoryServer:GetSOLWeaponSkinIDFromBaseWeaponID(item.baseWeaponId)][Server.InventoryServer:GetSOLWeaponSkinGUIDFromBaseWeaponID(item.baseWeaponId)]  or item   
                end  
            end
        end
        local moduleKey = CollectionLogic.GetDownloadModuleKeyByItemId(item and item.id or nil)
        self._wtCommonDownload:InitModuleKey(moduleKey)
        self._wtCommonDownload:SelfHitTestInvisible()
        logerror("[v_dzhanshen] CollectionsWeaponSkinPagePanel:_DownLoadResources moduleKey="..moduleKey)
    end
end

function CollectionsWeaponSkinPagePanel:_OnDownloadStateChange(moduleName, bDownloaded)
    if self._wtDownloadBtn then
        local item = self._weaponSkinItems[self._selectedPos+1]
        if isvalid(item) and item.randomSkinTag then
            local ownedSkins = Server.CollectionServer:GetOwnedWeaponSkins()
            if CollectionLogic.IsInMp() then
                local weaponProp = CollectionLogic.getWeaponPropFromInventory(item.baseWeaponId)
                if weaponProp and ownedSkins[weaponProp.weapon.skin_id] then
                    item = ownedSkins[weaponProp.weapon.skin_id][weaponProp.weapon.skin_gid] or item   
                end 
            else
                if ownedSkins[Server.InventoryServer:GetSOLWeaponSkinIDFromBaseWeaponID(item.baseWeaponId)] then
                    item = ownedSkins[Server.InventoryServer:GetSOLWeaponSkinIDFromBaseWeaponID(item.baseWeaponId)][Server.InventoryServer:GetSOLWeaponSkinGUIDFromBaseWeaponID(item.baseWeaponId)]  or item   
                end  
            end
        end
        local moduleKey = CollectionLogic.GetDownloadModuleKeyByItemId(item and item.id or nil)
        logerror("[v_dzhanshen] CollectionsWeaponSkinPagePanel:_OnDownloadStateChange moduleKey="..moduleKey.." bDownloaded="..tostring(bDownloaded))
        if moduleName == moduleKey then
            if not bDownloaded then
                self._wtDownloadBtn:Visible()
            else
                self._wtDownloadBtn:Collapsed()
            end
        end
    end
end

function CollectionsWeaponSkinPagePanel:_OnDownloadResult(moduleName, bDownloaded, errorCode)
    local item = self._weaponSkinItems[self._selectedPos+1]
    if isvalid(item) and item.randomSkinTag then
        local ownedSkins = Server.CollectionServer:GetOwnedWeaponSkins()
        if CollectionLogic.IsInMp() then
            local weaponProp = CollectionLogic.getWeaponPropFromInventory(item.baseWeaponId)
            if weaponProp and ownedSkins[weaponProp.weapon.skin_id] then
                item = ownedSkins[weaponProp.weapon.skin_id][weaponProp.weapon.skin_gid] or item   
            end 
        else
            if ownedSkins[Server.InventoryServer:GetSOLWeaponSkinIDFromBaseWeaponID(item.baseWeaponId)] then
                item = ownedSkins[Server.InventoryServer:GetSOLWeaponSkinIDFromBaseWeaponID(item.baseWeaponId)][Server.InventoryServer:GetSOLWeaponSkinGUIDFromBaseWeaponID(item.baseWeaponId)]  or item   
            end  
        end
    end
    local moduleKey = CollectionLogic.GetDownloadModuleKeyByItemId(item and item.id or nil)
    logerror("[v_dzhanshen] CollectionsWeaponSkinPagePanel:_OnDownloadResult moduleKey="..moduleKey)
    if moduleName == moduleKey then
        self._wtWeaponSkinScrollView:RefreshVisibleItems()
        if self._wtMysticalSkinDropDown:IsVisible() == true then
            self._wtMysticalSkinDropDown:RefreshTab()
        end
        self:OnRefreshModel(ESubStage.HallCollectionNew)
        self:_RefreshDownloadBtn()
    end
end

function CollectionsWeaponSkinPagePanel:_OnPackStateChanged(moduleName, packState)
    local isSuccess = packState == Module.ExpansionPackCoordinator.Config.PackState.Installed
    self:_OnDownloadResult(moduleName, isSuccess, 0)
end

function CollectionsWeaponSkinPagePanel:_RefreshDownloadBtn()
    if self._wtDownloadBtn then
        local item = self._weaponSkinItems[self._selectedPos+1]
        if isvalid(item) and item.randomSkinTag then
            local ownedSkins = Server.CollectionServer:GetOwnedWeaponSkins()
            if CollectionLogic.IsInMp() then
                local weaponProp = CollectionLogic.getWeaponPropFromInventory(item.baseWeaponId)
                if weaponProp and ownedSkins[weaponProp.weapon.skin_id] then
                    item = ownedSkins[weaponProp.weapon.skin_id][weaponProp.weapon.skin_gid] or item   
                end 
            else
                if ownedSkins[Server.InventoryServer:GetSOLWeaponSkinIDFromBaseWeaponID(item.baseWeaponId)] then
                    item = ownedSkins[Server.InventoryServer:GetSOLWeaponSkinIDFromBaseWeaponID(item.baseWeaponId)][Server.InventoryServer:GetSOLWeaponSkinGUIDFromBaseWeaponID(item.baseWeaponId)]  or item   
                end  
            end
        end
        local moduleKey = CollectionLogic.GetDownloadModuleKeyByItemId(item and item.id or nil)
        local bDownloaded = Module.ExpansionPackCoordinator:IsDownloadedByModuleName(moduleKey)
        logerror("[v_dzhanshen] CollectionsWeaponSkinPagePanel:_RefreshDownloadBtn moduleKey="..moduleKey.." bDownloaded="..tostring(bDownloaded))
        if not bDownloaded and isvalid(item) then
            if not hasdestroy(self._wtCommonDownload) then
                self._wtCommonDownload:InitModuleKey(moduleKey)
            end
            self._wtDownloadBtn:Visible()
        else
            self._wtDownloadBtn:Collapsed()
            if not hasdestroy(self._wtCommonDownload) then
                self._wtCommonDownload:Collapsed()
            end
        end
    end
end

--[[
function CollectionsWeaponSkinPagePanel:_OnHideUIBtnPressed()
    if not self._bHideUI then
        self.bHandleLongPressTimerHandle = Timer:NewIns(CollectionConfig.LongPressDelta, 1)
        self.bHandleLongPressTimerHandle:AddListener(self._HideUI, self)
        self.bHandleLongPressTimerHandle:Start()
    end
end

function CollectionsWeaponSkinPagePanel:_OnHideUIBtnReleased()
    if self.bHandleLongPressTimerHandle then
        self.bHandleLongPressTimerHandle:Release()
        self.bHandleLongPressTimerHandle = nil
        if self._bHideUI == true then
            self:_ShowUI()
        end
    end
end
--]]

function CollectionsWeaponSkinPagePanel:_ToggleUI()
    if self._bHideUI == true then
        self:_ShowUI()
    elseif self._wtMysticalSkinDropDown:IsChecked() == false then
        self:_HideUI()
    end
end

function CollectionsWeaponSkinPagePanel:_HideUI()
    self._bHideUI = true
    self:HideUI(true)
    if IsHD() then
        self._shotcutList = {}
        table.insert(self._shotcutList, {actionName = "CollectionShowUI",func = self._ShowUI, caller = self ,bUIOnly = false, bHideIcon = false})
        CollectionLogic.RegStackUIInputSummary(self._shotcutList, true)
    else
        Module.CommonBar:SetTopBarVisible(false)
    end
end

function CollectionsWeaponSkinPagePanel:_ShowUI()
    self._bHideUI = false
    self:HideUI(false)
    if IsHD() then
        self._shotcutList = {}
        table.insert(self._shotcutList, {actionName = "CollectionHideUI",func = self._HideUI, caller = self ,bUIOnly = false, bHideIcon = false})
        local item = self._weaponSkinItems[self._selectedPos+1]
        if isvalid(item) then
            if Server.CollectionServer:IsOwnedWeaponSkin(item.id) == true then
                if CollectionLogic.CheckIsSkinInRandomPool(item.id, item.gid) then
                    table.insert(self._shotcutList, {actionName = "GunSkin_RemoveFromRandomPool",func = self._RemoveFromRandomSkinPool, caller = self ,bUIOnly = false, bHideIcon = false})
                else
                    table.insert(self._shotcutList, {actionName = "GunSkin_AddToRandomPool",func = self._AddToRandomSkinPool, caller = self ,bUIOnly = false, bHideIcon = false})
                end
                if ItemHelperTool.IsMysticalSkin(item.id) == true then
                    table.insert(self._shotcutList, {actionName = "GunSkin_Trade",func = self._JumpToTradePage, caller = self ,bUIOnly = false, bHideIcon = false})
                end
            end
        end
        CollectionLogic.RegStackUIInputSummary(self._shotcutList, false)
    else
        Module.CommonBar:SetTopBarVisible(true)
    end
end

function CollectionsWeaponSkinPagePanel:_ShowSkinDetailPage()
    local item = nil
    if self._selectedMysticalPos ~= -1 then
        item = self._mysticalWeaponSkinItems[self._selectedMysticalPos+1]
    else
        item = self._weaponSkinItems[self._selectedPos+1] 
    end
    if isvalid(item) then
        --Module.Invite:InviteByQQ(Server.AccountServer:GetPlayerId())
        --Module.Invite:InviteByQQFromClient()
        --Module.Invite:InviteByWeChat(Server.AccountServer:GetPlayerId())
        --Module.Jump:JumpByID(********)
        --Module.Reward:OpenRewardPanel(ServerTipCode.GetItemTitle, nil, {item, ItemBase:New(***********), ItemBase:New(***********), ItemBase:New(***********), ItemBase:New(***********),ItemBase:New(***********),  ItemBase:New(***********),ItemBase:New(***********), ItemBase:New(***********)}, nil, nil, nil, true)
        --Module.Reward:OpenRewardPanel(ServerTipCode.GetItemTitle, nil, {ItemBase:New(***********)}, nil, nil, nil, true)
        --Module.Reward:OpenGestureUnlockPanel(***********)
        --Server.TeamServer:ApplyJoinFromMiniProgram(5348)
        --Module.Reward:OpenGestureUnlockPanel(***********)
        --Server.TeamServer:ApplyJoinFromMiniProgram(5348)
        --Module.Reward:OpenHeroStuffUnlockPanel(***********)
        --local testnum = tonumber("xixix34343")
        --Module.Reward:OpenSafehouseLevelUpRewardPanel(1001)
        --Module.Reward:OpenVehicleSkinGainPop({ItemBase:New(***********)})
        --[[
        local playerInfoList = {}
        local SOLRankScore = Server.RankingServer:GetRankScore()
        local SOLAttended = Server.RankingServer:GetHasAttended()
        local MPRankScore = Server.TournamentServer:GetRankScore()
        local MPAttended = Server.TournamentServer:GetHasAttended()
        for i = 1, 5, 1 do
            table.insert(playerInfoList, 
            {
                player_id = Server.AccountServer:GetPlayerId(),
                nick_name = Server.RoleInfoServer.nickName or "",
                pic_url = Server.RoleInfoServer.picUrl,
                level = Server.RoleInfoServer.accountLevel,
                season_lvl = Server.RoleInfoServer.seasonLevel,
                gender = Server.RoleInfoServer.gender,
                safehouse_degree = 0,
                sol_rank_score = SOLRankScore,
                sol_rank_attended = SOLAttended,
                mp_rank_score = MPRankScore,
                mp_rank_attended = MPAttended
            }
            )
        end
        Module.Social:ShowRecommendAddFriendPop(playerInfoList, 12)
        --]]
        --Module.Reward:OpenVehicleGainPop(ItemBase:New(***********))
        --Module.Reward:OpenWeaponGainPop({ItemBase:New(***********)})
        --Module.Reward:OpenWeaponAdapterGainPop(ItemBase:New(***********))
        --Module.Reward:OpenBHDUnlockPop()
        --Module.Reward:OpenPendantGainPop(ItemBase:New(***********))
        if item.randomSkinTag then
            CollectionLogic.OpenRandomSkinUI(item.baseWeaponId)
        else
            Module.Collection:ShowWeaponSkinDetailPage(item)
        end 
    end
end

function CollectionsWeaponSkinPagePanel:_OnGunSkinFilterUpdated()
    self:_OnRefreshWeaponSkinItems(true)
end

function CollectionsWeaponSkinPagePanel:_OnActionBtnClicked()
    local item = nil
    if self._selectedMysticalPos ~= -1 then
        item = self._mysticalWeaponSkinItems[self._selectedMysticalPos+1]
    else
        item = self._weaponSkinItems[self._selectedPos+1]
    end
    if isvalid(item) then
        if item.randomSkinTag then
            if CollectionLogic.CheckAreAnySkinsInRandomPool(item.baseWeaponId) then
                CollectionLogic.EnableRandomSkin(item.baseWeaponId, not CollectionLogic.CheckIsRandomPoolEnabled(item.baseWeaponId))
            else
                Module.CommonTips:ShowSimpleTip(StringUtil.Key2StrFormat(Module.Collection.Config.Loc.RandomSkinPoolEmptyTip,
                        {["FirearmName"] = item.name
                        }))
            end
        else
            if Server.CollectionServer:IsOwnedWeaponSkin(item.id, item.gid) == true then
                CollectionLogic.ApplyWeaponSkin(CollectionLogic.GetBaseWeaponIdFromSkinId(item.id), item.id, item.gid)
            elseif self._challengeSkinId == item.id then
                if self._canRetrieveSkinId then
                    Server.CollectionServer:RetrieveWeaponSkin(item.id)
                else
                    self:_JumpToAppearanceChallengePage()
                end
            elseif self._canRetrieveSkinId == item.id then
                Server.CollectionServer:RetrieveWeaponSkin(item.id)
            else
                self:_PurchaseWeaponSkin()
            end
        end
    end
end

function CollectionsWeaponSkinPagePanel:_OnWeaponSkinApplied(res)
    if not self._bApplied then
        local item = nil
        if self._selectedMysticalPos ~= -1 then
            table.sort(self._mysticalWeaponSkinItems, CollectionLogic.MysticalSkinInstanceListSort)
            item = self._mysticalWeaponSkinItems[1]
            self._weaponSkinItems[self._selectedPos+1] = item
            self._selectedMysticalPos = 0
            if self._wtMysticalSkinDropDown:IsVisible() == true then
                self._wtMysticalSkinDropDown:RefreshTab()
            end
            local skinList = Module.Collection.Field:GetSkinList()
            local tagetSkinlistIndex
            for index, skinItem in ipairs(skinList) do
                if skinItem.id == item.id then
                    tagetSkinlistIndex = index
                    break
                end
            end
            if tagetSkinlistIndex then
                skinList[tagetSkinlistIndex] = item
            end
        elseif self._selectedPos ~= -1 then
            item = self._weaponSkinItems[self._selectedPos+1]
        end
        if isvalid(item) then 
            if item.randomSkinTag then
                self._wtApplyBtn:SetIsEnabled(true)
                self._wtApplyBtn:SetIsEnabledStyle(not CollectionLogic.CheckIsRandomPoolEnabled(item.baseWeaponId) and CollectionLogic.CheckAreAnySkinsInRandomPool(item.baseWeaponId))
                if CollectionLogic.CheckIsRandomPoolEnabled(item.baseWeaponId) then
                    self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.AppearanceApplied)
                else
                    self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.ApplyAppearance)
                end
                self._wtWeaponSkinScrollView:RefreshVisibleItems()
            else
                if CollectionLogic.CheckIfSkinAppliedOnWeapon(item.id, item.gid, res) then
                    self._bApplied = true
                    Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.AppearanceApplied)
                    self:_RefreshItemUI(true)
                    local baseWeaponId = CollectionLogic.GetBaseWeaponIdFromSkinId(item.id)
                    if CollectionLogic.CheckIsRandomPoolEnabled(baseWeaponId) then
                        local fCallbackIns = CreateCallBack(function(self, res)
                            if res.result == 0 then
                                self._wtWeaponSkinScrollView:RefreshVisibleItems()
                            end
                        end,self) 
                        CollectionLogic.EnableRandomSkin(baseWeaponId, false, fCallbackIns)
                    else
                        self._wtWeaponSkinScrollView:RefreshVisibleItems()
                    end
                else
                    Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.FailedToUse)
                end
            end
        end
    end
end


function CollectionsWeaponSkinPagePanel:_PurchaseWeaponSkin()
    local item = nil
    if self._selectedPos ~= #self._weaponSkinItems then
        item = self._weaponSkinItems[self._selectedPos+1]
        if item ~= nil then 
            local itemConfigRow = ItemConfigTool.GetItemConfigById(item.id)
            local bHasConfig = false
            if itemConfigRow ~= nil then
                local bHasTipInConfig = itemConfigRow.UnlockTip ~= nil and itemConfigRow.UnlockTip ~= ""
                if itemConfigRow.JumpID ~= nil and itemConfigRow.JumpID ~= 0 then
                    local bCanJump = Module.Jump:CheckCanJumpByID(itemConfigRow.JumpID)
                    if bCanJump then
                        Module.Jump:JumpByID(itemConfigRow.JumpID)
                    elseif bHasTipInConfig then
                        Module.CommonTips:ShowSimpleTip(itemConfigRow.UnlockTip)
                    else
                        Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.UnlockTip)
                    end
                    bHasConfig = true
                end
            end
            if not bHasConfig then
                if CollectionLogic.IsItemInStore(item.id) == true then
                    Module.CommonTips:ShowConfirmWindow(
                        string.format(CollectionConfig.Loc.ConfirmPurchase, item.name),
                        function()
                            CollectionLogic.PurchaseItem(item.id)
                        end,
                        function()
            
                        end,
                        CollectionConfig.Loc.Cancel,
                        CollectionConfig.Loc.Confirm
                    )
                else
                    Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.UnlockTip)
                end
            end
        end
    end
end

function CollectionsWeaponSkinPagePanel:_OnShowTipCheckBoxStateChanged(bChecked)
    if not IsHD() then
        if bChecked then
            self:_OnShowInstruction()
        else
            self:_OnHideInstruction()
        end
    end
end

function CollectionsWeaponSkinPagePanel:_OnShowInstruction()
    local datas = {}
    local item = self._weaponSkinItems[self._selectedPos+1]
    if isvalid(item) and item.randomSkinTag then
        table.insert(datas, {
            textContent = CollectionConfig.Loc.RandomSkinApplyHint,
            styleRowId = "C000"
        })
        self._tipHandle = Module.CommonTips:ShowCommonMessagesWithAnchor(datas, self._wtTipAnchor)
    end
end

function CollectionsWeaponSkinPagePanel:_OnHideInstruction(reason)
    if self._tipHandle then
        Module.CommonTips:RemoveCommonMessageWithAnchor(self._tipHandle, self._wtTipAnchor)
        self._tipHandle = nil
        if reason ~= ETipsTriggerReason.Click then
            self._wtTipCheckBtn:SetSelectState(false, false)
        end
    end
end

function CollectionsWeaponSkinPagePanel:_OnFilterBtnClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.CollectionGunSkinFilterPanel, nil, self)
end  

function CollectionsWeaponSkinPagePanel:ClosePopup()
    self:_OnHideInstruction()
end

function CollectionsWeaponSkinPagePanel:BindSetBackgourndCallback(callback, caller)
    self._setBackgourndCallback = SafeCallBack(callback, caller)
end


function CollectionsWeaponSkinPagePanel:UpdateBackground()
    if self._setBackgourndCallback then
        self._setBackgourndCallback(nil, false)
    end
end

function CollectionsWeaponSkinPagePanel:OnHandleMouseButtonUpEvent(mouseEvent)
    if self._bHandling then
        return
    end 
    local curInputType = WidgetUtil.GetCurrentInputType()
    self._bHandling = true
    local absolutePoint = mouseEvent:GetScreenSpacePosition()
    if self._wtMysticalSkinDropDown:IsChecked() == true then
        if not IsHD() or not WidgetUtil.IsGamepad() then
            local bShouldIgnoreClick = UIUtil.CheckAbsolutePointInsideWidget(self._wtWeaponSkinScrollView, absolutePoint)
            if not bShouldIgnoreClick then
                bShouldIgnoreClick = UIUtil.CheckAbsolutePointInsideWidget(self._wtWeaponSkinScrollView, absolutePoint)
            end
            self._bIgnoredDropDownState = UIUtil.CheckAbsolutePointInsideWidget(self._wtMysticalSkinDropDown, absolutePoint)
            if not bShouldIgnoreClick then
                bShouldIgnoreClick = self._bIgnoredDropDownState
            end
            if not bShouldIgnoreClick then
                bShouldIgnoreClick = UIUtil.CheckAbsolutePointInsideWidget(self._wtMysticalSkinDropDown._wtScrollGridBox, absolutePoint)
            end
            if not bShouldIgnoreClick then
                bShouldIgnoreClick = UIUtil.CheckAbsolutePointInsideWidget(self._wtApplyBtn, absolutePoint)
            end
            if not bShouldIgnoreClick then
                bShouldIgnoreClick = UIUtil.CheckAbsolutePointInsideWidget(self._wtItemDetailView, absolutePoint)
            end
            if not bShouldIgnoreClick then
                bShouldIgnoreClick = UIUtil.CheckAbsolutePointInsideWidget(self._wtDetailBtn, absolutePoint)
            end
            if not bShouldIgnoreClick then
                bShouldIgnoreClick = UIUtil.CheckAbsolutePointInsideWidget(self._wtDetailBtn_PC, absolutePoint)
            end
            if not bShouldIgnoreClick then
                bShouldIgnoreClick = UIUtil.CheckAbsolutePointInsideWidget(self._wtTradeBtn, absolutePoint)
            end
            if not bShouldIgnoreClick then
                bShouldIgnoreClick = UIUtil.CheckAbsolutePointInsideWidget(self._wtDownloadBtn, absolutePoint)
            end
            if not bShouldIgnoreClick then
                bShouldIgnoreClick = UIUtil.CheckAbsolutePointInsideWidget(self._wtToggleRandomBtn, absolutePoint)
            end
            if not bShouldIgnoreClick then
                local bottomBarShowDetailBtn = Module.CommonBar:GetBottomBarItemByActionName("GunSkin_ShowDetail")
                if bottomBarShowDetailBtn then
                    bShouldIgnoreClick = UIUtil.CheckAbsolutePointInsideWidget(bottomBarRandomSkinAddBtn, absolutePoint)
                end
            end
            if not bShouldIgnoreClick then
                local bottomBarRandomSkinAddBtn = Module.CommonBar:GetBottomBarItemByActionName("GunSkin_AddToRandomPool")
                if bottomBarRandomSkinAddBtn then
                    bShouldIgnoreClick = UIUtil.CheckAbsolutePointInsideWidget(bottomBarRandomSkinAddBtn, absolutePoint)
                end
            end
            if not bShouldIgnoreClick  then
                local bottomBarRandomSkinRemoveBtn  = Module.CommonBar:GetBottomBarItemByActionName("GunSkin_RemoveFromRandomPool")
                if bottomBarRandomSkinRemoveBtn then
                    bShouldIgnoreClick = UIUtil.CheckAbsolutePointInsideWidget(bottomBarRandomSkinRemoveBtn, absolutePoint)
                end
            end
            if not bShouldIgnoreClick  then
                local bottomBarTradeBtn  = Module.CommonBar:GetBottomBarItemByActionName("GunSkin_Trade")
                if bottomBarTradeBtn then
                    bShouldIgnoreClick = UIUtil.CheckAbsolutePointInsideWidget(bottomBarTradeBtn, absolutePoint)
                end
            end
            if not bShouldIgnoreClick then
                local currentStackUI = Facade.UIManager:GetCurrentStackUI()
                if currentStackUI and currentStackUI.UINavID == UIName2ID.CollectionMainPanel then
                    local item = self._weaponSkinItems[self._selectedPos+1]
                    if isvalid(item) and not item.randomSkinTag and self._selectedCell then
                        local weaponDesc = self._selectedCell.item:GetRawDescObj()
                        local weaponSkinInfoParam = weaponDesc:GetPbWeaponSkinInfoParam()
                        if self._selectedCell.item and self._selectedCell.item.id == item.id or weaponSkinInfoParam.SkinId == item.id then
                            self._selectedCell:InitCollectionWeaponSkinItem(item)
                        end
                    end
                    self._bIgnoredDropDownState = bInsideDropDown
                    self:_RefreshItemUI(true)
                end
            end
        end
    elseif self._wtMysticalSkinDropDown:IsVisible() == true then
        local bInsideDropDown = UIUtil.CheckAbsolutePointInsideWidget(self._wtMysticalSkinDropDown, absolutePoint)
        if bInsideDropDown == true then
            local item = self._mysticalWeaponSkinItems[self._selectedMysticalPos + 1]
            if isvalid(item) then
                self._wtApplyBtn:SetIsEnabled(true)
                self._wtApplyBtn:SetIsEnabledStyle(true)
                self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.NotUnlocked)
                self._wtApplyBtn:SelfHitTestInvisible()
                -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
                if IsHD() and WidgetUtil.IsGamepad() then
                    self:_AddInputActionForApplyBtn()
                    self:_RegisterDynamicNavConfig()
                end
                -- END MODIFICATION
                if Server.CollectionServer:IsOwnedWeaponSkin(item.id, item.gid) == true then
                    if CollectionLogic.CheckIfSkinAppliedOnWeapon(item.id, item.gid) and not CollectionLogic.CheckIsRandomPoolEnabled(CollectionLogic.GetBaseWeaponIdFromSkinId(item.id))  then
                        self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.AppearanceApplied)
                        self._wtApplyBtn:SetIsEnabled(false)
                        -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
                        self:_RemoveInputActionForApplyBtn()
                        -- END MODIFICATION
                    else
                        self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.ApplyAppearance)
                    end
                else
                    self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.ProceedToUnlock)
                    if itemConfigRow then
                        if itemConfigRow.JumpID ~= nil and itemConfigRow.JumpID ~= 0 then
                            if itemConfigRow.ButtonDes ~= nil and itemConfigRow.ButtonDes ~= "" then
                                self._wtAlertHintTxt:SetText(itemConfigRow.ButtonDes)
                                self._wtAlertHintBox:SelfHitTestInvisible()
                            end
                            local bCanJump = Module.Jump:CheckCanJumpByID(itemConfigRow.JumpID)
                            self._wtApplyBtn:SetIsEnabledStyle(bCanJump)
                        elseif itemConfigRow.EndButtonDes ~= nil and itemConfigRow.EndButtonDes ~= "" then
                            self._wtAlertHintTxt:SetText(itemConfigRow.EndButtonDes)
                            self._wtAlertHintBox:SelfHitTestInvisible()
                            self._wtApplyBtn:SetIsEnabled(false)
                            -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
                            self:_RemoveInputActionForApplyBtn()
                            -- END MODIFICATION
                        elseif CollectionLogic.IsItemInStore(item.id) == true then
                            local priceStr = CollectionLogic.GePriceStrByItemId(item.id)
                            self._wtApplyBtn:SetMainTitle(priceStr)
                        else
                            self._wtApplyBtn:SetIsEnabled(false)
                            -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
                            self:_RemoveInputActionForApplyBtn()
                            -- END MODIFICATION
                        end
                    else
                        if CollectionLogic.IsItemInStore(item.id) == true then
                            local priceStr = CollectionLogic.GePriceStrByItemId(item.id)
                            self._wtApplyBtn:SetMainTitle(priceStr)
                        else
                            self._wtApplyBtn:SetIsEnabled(false)
                            -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
                            self:_RemoveInputActionForApplyBtn()
                            -- END MODIFICATION
                        end
                    end
                end 
                if self._selectedCell then
                    self._selectedCell:InitCollectionWeaponSkinItem(item, true)
                end
            end       
        end
    end
    self._bHandling = false
end

function CollectionsWeaponSkinPagePanel:PreScreenshotShare()
    Module.CommonBar:SetTopBarVisible(false)
    self._wtMainPanel:Hidden()
end

function CollectionsWeaponSkinPagePanel:AfterScreenshotShare()
    Module.CommonBar:SetTopBarVisible(true)
end

function CollectionsWeaponSkinPagePanel:OnShareFlowFinish()
    self._wtMainPanel:SelfHitTestInvisible()
end

function CollectionsWeaponSkinPagePanel:OnShareClick()
    local PreScreenshotShare = CreateCallBack(function(self)
        self:PreScreenshotShare()
    end,self)

    local AfterScreenshotShare = CreateCallBack(function(self)
        self:AfterScreenshotShare()
    end,self)
    local item = nil
    if self._selectedMysticalPos ~= -1 then
        item = self._mysticalWeaponSkinItems[self._selectedMysticalPos+1]
    else
        item = self._weaponSkinItems[self._selectedPos+1] 
    end
    if item ~= nil then 
        Module.Share:ReqShareWeapon(item.id, item, "CollectionWeaponSkinDetailPage", PreScreenshotShare, AfterScreenshotShare)
    end
end

return CollectionsWeaponSkinPagePanel
