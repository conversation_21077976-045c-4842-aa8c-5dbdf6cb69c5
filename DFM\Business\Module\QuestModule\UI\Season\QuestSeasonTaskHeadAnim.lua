----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMQuest)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class QuestSeasonTaskHeadAnim : LuaUIBaseView

local QuestSeasonTaskHeadAnim = ui("QuestSeasonTaskHeadAnim")

function QuestSeasonTaskHeadAnim:Ctor()
    self._wtStarText = self:Wnd("DFTextBlock_102", UITextBlock)
    self._wtCanvasPanel = self:Wnd("DFCanvasPanel_Dongxiao", UIWidgetBase)
end

function QuestSeasonTaskHeadAnim:OnInitExtraData(starNum)
    self._wtStarText:SetText(string.format(Module.Quest.Config.Loc.QuestSeasonStarGain, starNum))
    self._wtCanvasPanel:SelfHitTestInvisible()
end

function QuestSeasonTaskHeadAnim:PlayAnim()
    self:PlayWidgetAnim(self.WBP_SeasonalTasks_UpVFX_V1_upgrade)
end

function QuestSeasonTaskHeadAnim:OnAnimFinished(anim)
    if anim == self.WBP_SeasonalTasks_UpVFX_V1_upgrade then
        Facade.UIManager.CloseUI(self)
    end
end


return QuestSeasonTaskHeadAnim