----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMFriend)
local FriendLogic = require "DFM.Business.Module.FriendModule.Logic.FriendLogic"
----- LOG FUNCTION AUTO GENERATE END -----------

---@class FriendLatelyBoxHD : LuaUIBaseView
local FriendLatelyBoxHD = ui("FriendLatelyBoxHD")

--- BEGIN MODIFICATION @ VIRTUOS
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- 输入设备相关
local UGPInputDelegates = import "GPInputDelegates"
--- END MODIFICATION

function FriendLatelyBoxHD:Ctor()
    loginfo("FriendLatelyBoxHD:Ctor")
    self._wtPlayerName = self:Wnd("wtPlayerName", UITextBlock)
    self._wtLatelyTimeTxt = self:Wnd("wtLatelyTimeTxt", UITextBlock)
    self._wtPlayerState = self:Wnd("wtMemberType", UITextBlock)

    self._wtAddFriendBtn = self:Wnd("wtAddFriendBtn", UIWidgetBase)
    self._wtAddFriendBtn:Event("OnClicked", self._OnBtnAddFriendClick, self)
    self._wtplatIcon = self:Wnd("wtplatIcon", UIImage)
    self._wtRankIcon = self:Wnd("wtRankIcon" ,UIWidgetBase)
    self._wtRankDivision = self:Wnd("wtRankDivision", UITextBlock)
    self._wtPlayerIcon = self:Wnd("WBP_CommonHeadIcon", CommonHeadIcon)
    self._wtMilitary = self:Wnd("WBP_Friend_BrandAvatar", UIWidgetBase)
    self._wtOffLineImage = self:Wnd("Masks", UIWidgetBase)
    self._wtBHDMark = self:Wnd("WBP_BHD_OwnMark",UIWidgetBase)
    self._wtDFTipsAnchor = UIUtil.WndTipsAnchor(self._wtBHDMark, "DFTipsAnchor", self.ShowTips,self.CloseTips)
    self._playerInfo = {}

    --- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self._wtTeammateInfoPanel = self:Wnd("wtTeammateInfoPanel", UIWidgetBase)

        if self._wtTeammateInfoPanel then
            self._wtTeammateInfoPanel:SetCppValue("IsFocusable", false)
        end
    end
    --- END MODIFICATION
end

function FriendLatelyBoxHD:ShowUI(playerInfo)
    logtable(playerInfo)
    self._playerInfo = playerInfo
    self._gameTime = playerInfo.finish_time
    self._wtplatIcon:Visible()
    self._wtLatelyTimeTxt:Collapsed()
    self._wtMilitary:SetMilitary(playerInfo.military_tag)

    --self._wtLatelyTimeTxt:SetText(FriendLogic.GetFriendRecentTime(self._gameTime))
    self._wtPlayerName:SetText(self._playerInfo.nick_name)

    self._playIconInfo = {
        pic_url = self._playerInfo.pic_url,
        player_id = self._playerInfo.player_id,
        level = 0,
        nick_name = self._playerInfo.nick_name,
        --- BEGIN MODIFICATION @ VIRTUOS 增加平台logo
        plat_id = self._playerInfo.plat or PlatIDType.Plat_Invalid,
        --- END MODIFICATION
    }

    --- BEGIN MODIFICATION @ VIRTUOS using Platform Online ID as player name.
    if IsPS5() then
        local callback = function(onlineID)
            self._wtPlayerName:SetText(onlineID)
        end
        Module.Social:AsyncGetPS5OnlineIdByUID(self._playerInfo.player_id, callback, self)
    end
    --- END MODIFICATION
    
    if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse then
        self._playIconInfo.level = self._playerInfo.season_level
        self._playIconInfo.rank_attended = self._playerInfo.sol_attended
        self._playIconInfo.rank_score = self._playerInfo.sol_rank_score
    elseif Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Lobby then
        self._playIconInfo.level = self._playerInfo.level
        self._playIconInfo.rank_attended = self._playerInfo.mp_attended
        self._playIconInfo.rank_score = self._playerInfo.mp_rank_score

        -- azhengzheng:传入胜者为王段位信息
        self._playIconInfo.show_commander_rank_points = self._playerInfo.show_commander_rank_points
        self._playIconInfo.mp_commander_score = self._playerInfo.mp_commander_score
    end

    FriendLogic.InitFriendRank(self._wtRankIcon, self._wtRankDivision, self._playerInfo)

    --- BEGIN MODIFICATION @ VIRTUOS 增加平台logo
    self._wtplatIcon:AsyncSetImagePath(Module.Friend:GetPlatformIconPath(self._playIconInfo.plat_id, false, false), false)
    --- END MODIFICATION

    self._wtOffLineImage:HitTestInvisible()
    if self._playerInfo.state then
        FriendLogic.SetPlayerStateCode(self._wtPlayerState, self._playerInfo,self._wtplatIcon)
        local state = Module.Social:GetOnlinePlayerStateFromStateCode(self._playerInfo.state)
        if Module.Friend:IsFriendOffLineOrInvisible(self._playerInfo.state, self._playerInfo.player_id) then
            self._wtOffLineImage:HitTestInvisible()
        else
            self._wtOffLineImage:Collapsed()
        end
    end
    local btnTbl = {
        HeadButtonType.PlayerInformat,
        HeadButtonType.AddFriend,
        HeadButtonType.InviteAppTeam,
    }
    self._wtPlayerIcon:InitPortrait(self._playIconInfo, HeadIconType.HeadList, btnTbl, FriendApplySource.RecentPlayApply, TeamInviteSource.FromRecent)
    --self:SetBHDOwnMark(self._playerInfo)
end

function FriendLatelyBoxHD:OnClose()
    self:RemoveAllLuaEvent()
end

function FriendLatelyBoxHD:OnOpen()
end

--- BEGIN MODIFICATION @ VIRTUOS
function FriendLatelyBoxHD:OnShowBegin()
    if IsHD() then
        self:_EnableGamepadFeature()
    end
end

function FriendLatelyBoxHD:OnHideBegin()
    if IsHD() then
        self:_DisableGamepadFeature()
    end
end

function FriendLatelyBoxHD:_EnableGamepadFeature()
    if not IsHD() then
        return 
    end

    -- 绑定多输入设备切换事件
    if not self._OnNotifyInputTypeChangedHandle then
        self._OnNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._OnInputTypeChanged, self))
        -- 初始化：根据当前输入设备来设置TeamButtons的可见性
        local curInpurtType = WidgetUtil.GetCurrentInputType()
        self:_OnInputTypeChanged(curInpurtType)
    end
end

function FriendLatelyBoxHD:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end

    if self._OnNotifyInputTypeChangedHandle then
        UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._OnNotifyInputTypeChangedHandle)
        self._OnNotifyInputTypeChangedHandle = nil
    end
end

function FriendLatelyBoxHD:_OnInputTypeChanged(InputType)
    if not IsHD() then
        return 
    end
    
    -- 手柄操作，TeamButton将显示在名片内，不会显示在FriendInfo中，故隐藏
    if WidgetUtil.IsGamepad() then
        if self._wtAddFriendBtn then
            self._wtAddFriendBtn:Collapsed()
        end
    else
        if self._wtAddFriendBtn then
            self._wtAddFriendBtn:SelfHitTestInvisible()
        end
    end
end
--- END MODIFICATION

function FriendLatelyBoxHD:_OnBtnAddFriendClick()
    Module.Friend:AddFriend(self._playerInfo.player_id, FriendApplySource.RecentPlayApply, nil, nil, nil, self._playerInfo.plat_id)
    --self:_ChangeBtn()
end

function FriendLatelyBoxHD:_ChangeBtn()
    
end

function FriendLatelyBoxHD:SetBHDOwnMark(info)
    --info.bhd_is_purchased = true
    if info.bhd_is_purchased then
        self._wtBHDMark:SelfHitTestInvisible()
    else
        self._wtBHDMark:Collapsed()
    end
end

function FriendLatelyBoxHD:ShowTips()
    local datas = {}
    if self._tipsInfo ~= ""  then
        table.insert(datas, {textContent = Module.LobbyBHD.Config.Loc.BHDOwned})
        self._tipHandle = Module.CommonTips:ShowCommonMessagesWithAnchor(datas,self._wtDFTipsAnchor)
    end

end

function FriendLatelyBoxHD:CloseTips()
    if self._tipHandle then
        Module.CommonTips:RemoveCommonMessageWithAnchor(self._tipHandle, self._wtTipAnchor)
        self._tipHandle = nil
    end
end


return FriendLatelyBoxHD