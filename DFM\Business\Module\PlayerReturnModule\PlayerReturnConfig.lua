----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMPlayerReturn)
----- LOG FUNCTION AUTO GENERATE END -----------



local PlayerReturnConfig = {}

-- 回流任务子模块里的通用任务控件(ActivityMilestonesSub)要改文字换行长度
PlayerReturnConfig.PLAYERRETURN_TASK_WRAP_TEXT_AT = 796

--#region UITable
UITable[UIName2ID.PlayerReturnLobbyPopup] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.PlayerReturnModule.UI.PlayerReturnLobbyPopup", 
    BPKey = "WBP_Reflow_PopWindow",
    IsModal = true,
    Anim = {
        FirstDayInAnimation = "WBP_Reflow_PopWindow_First_in",
        NormalInAnimation   = "WBP_Reflow_PopWindow_2-7_in",
        OutAnimation = "WBP_Reflow_PopWindow_out",
    },
    SubUIs = {
        UIName2ID.IVCommonItemTemplate
    }
}

UITable[UIName2ID.PlayerReturnEntrance] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.PlayerReturnModule.UI.PlayerReturnEntrance",
    BPKey = "WBP_Reflow_Entrance"
}

UITable[UIName2ID.PlayerReturnMainPanel] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.PlayerReturnModule.UI.MainPanel.PlayerReturnMainPanel",
    BPKey = "WBP_Reflow_Main",

    SubUIs = {
        UIName2ID.PlayerReturnMainTips,
        UIName2ID.PlayerReturnNewContentPanel,
        UIName2ID.PlayerReturnTaskPanel,
        UIName2ID.PlayerReturnSignInPanel,
        UIName2ID.PlayerReturnDailyMatchPanel,
    },
}

UITable[UIName2ID.PlayerReturnMilestonePanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.PlayerReturnModule.UI.MainPanel.Tasks.PlayerReturnMilestonePanel",
    BPKey = "WBP_Reflow_MilestonePanel",
    SubUIs = {
        UIName2ID.ActivityGrowthRewardList,
        UIName2ID.ActivityItemAcquisition,
        UIName2ID.IVCommonItemTemplate,
    },
    Anim = {
        FlowInAni = "WBP_Activity_MilestonesPanel_in",
        FlowOutAni = "WBP_Activity_MilestonesPanel_out"
    }
}

UITable[UIName2ID.PlayerReturnMainTips] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.PlayerReturnModule.UI.MainPanel.PlayerReturnMainTips",
    BPKey = "WBP_Reflow_Tips",

    SubUIs = {UIName2ID.PlayerReturnMainPanelTipItem,},
}

UITable[UIName2ID.PlayerReturnSignInPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.PlayerReturnModule.UI.MainPanel.SignIn.PlayerReturnSignInPanel",
    BPKey = "WBP_Reflow_SignIn",

    SubUIs = {UIName2ID.PlayerReturnSignInItem}
}

UITable[UIName2ID.PlayerReturnDailyMatchPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.PlayerReturnModule.UI.MainPanel.DailyMatch.PlayerReturnDailyMatchPanel",
    BPKey = "WBP_Reflow_Task03",

    SubUIs = {UIName2ID.PlayerReturnDailyMatchItem}
}

UITable[UIName2ID.PlayerReturnDailyMatchItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.PlayerReturnModule.UI.MainPanel.DailyMatch.PlayerReturnDailyMatchItem",
    BPKey = "WBP_Reflow_Task03_Item",
}

UITable[UIName2ID.PlayerReturnDailyMatchPopup] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.PlayerReturnModule.UI.PlayerReturnDailyMatchPopup",
    BPKey = "WBP_Reflow_EnjoyGame",
    SubUIs = {UIName2ID.PlayerReturnBlurBehindItemView},
    LinkSubStage = ESubStage.HallCollectionNew,
}

UITable[UIName2ID.PlayerReturnSignInItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.PlayerReturnModule.UI.MainPanel.SignIn.PlayerReturnSigninItem",
    BPKey = "WBP_Reflow_SignIn_Item",

    SubUIs = {UIName2ID.HighlightableCommonItem},
}

UITable[UIName2ID.PlayerReturnBlurBehindItemView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.YxFramework.Managers.UI.LuaUIBaseView",
    BPKey = "WBP_Reflow_ItemView",
}

UITable[UIName2ID.HighlightableCommonItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.PlayerReturnModule.UI.MainPanel.SignIn.HighlightableCommonItem",
    BPKey = "WBP_Reflow_HighlightableCommonItem",

    SubUIs = {UIName2ID.IVCommonItemTemplate}
}

UITable[UIName2ID.PlayerReturnTaskPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.PlayerReturnModule.UI.MainPanel.Tasks.PlayerReturnTaskPanel",
    BPKey = "WBP_Reflow_Task01",

    SubUIs = {UIName2ID.PlayerReturnMilestonePanel}
}

UITable[UIName2ID.PlayerReturnMileStone] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.PlayerReturnModule.UI.MainPanel.Tasks.PlayerReturnMileStone",
    BPKey = "WBP_Reflow_MilestoneList",
}

UITable[UIName2ID.PlayerReturnTaskItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.PlayerReturnModule.UI.MainPanel.Tasks.PlayerReturnTaskItem",
    BPKey = "WBP_Reflow_Task01_Item",
    SubUIs = {UIName2ID.ActivityMilestonesSub}
}

-- UITable[UIName2ID.PlayerReturnPickRewardPop] = {
--     UILayer = EUILayer.Pop,
--     LuaPath = "DFM.Business.Module.PlayerReturnModule.UI.MainPanel.Tasks.PlayerReturnPickRewardPop",
--     BPKey = "WBP_Reflow_ChangeRewards",
--     IsModal = true,
--     SubUIs = {UIName2ID.PlayerReturnPickRewardItem}
-- }

-- UITable[UIName2ID.PlayerReturnPickRewardItem] = {
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.PlayerReturnModule.UI.MainPanel.Tasks.PlayerReturnPickRewardItem",
--     BPKey = "WBP_Reflow_ChangeRewards_Item",
-- }

UITable[UIName2ID.PlayerReturnNewContentPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.PlayerReturnModule.UI.MainPanel.NewContent.PlayerReturnNewContentPanel",
    BPKey = "WBP_Reflow_Task02",

    SubUIs = {
        UIName2ID.PlayerReturnNewContentItem,
        UIName2ID.Assembled_CommonTipsFrame,
        UIName2ID.Assembled_CommonMessageTips_V1,
        UIName2ID.Assembled_CommonMessageTips_V2,
    }
}

UITable[UIName2ID.PlayerReturnNewContentItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.PlayerReturnModule.UI.MainPanel.NewContent.PlayerReturnNewContentItem",
    BPKey = "WBP_Reflow_Task02_Item",

    SubUIs = {UIName2ID.ActivityMilestonesSub}
}

UITable[UIName2ID.PlayerReturnMainPanelTipItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.PlayerReturnModule.UI.MainPanel.PlayerReturnMainPanelTipItem",
    BPKey = "WBP_Reflow_Tips_Item"
}

UITable[UIName2ID.PlayerReturnNewContentPoster] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.PlayerReturnModule.UI.MainPanel.NewContent.PlayerReturnNewContentPoster",
    BPKey = "WBP_Reflow_FullscreenPoster",
    IsModal = true,
}

--#endregion

-- 调试开关，避免在开发调试中还未配翻译表时显示长串“未配置”字符串
-- 搞定后直接删掉这一部分
local DEBUG_USE_PLAIN_STRING = IsInEditor()
if DEBUG_USE_PLAIN_STRING then 
    PlayerReturnConfig.Localization = {
        CountDownDHMFmtStr = "{d}天{h}时{m}分",
        CountDownDHFmtStr = "{d}天{h}时",
        ActivityEndPopMessage = "重返行动已结束",

        DoubleExperience = "双倍经验",
        DoubleMerits = "双倍军功",
        DoubleExperienceCount = "({count}/{total})对局中获得双倍经验",
        DoubleMeritsCount = "({count}/{total})对局中获得双倍军功",

        LobbyPopup = {
            FxText = "未知通信...",
            Claim = "领取",
            Back = "返回",
        },

        TaskPanel = {
            AlreadyOwned = "已拥有",
            Picked = "已选择",
            Pick = "选择",
        },

        -- 签到奖励展示和大厅弹窗显示签到天数共用
        DayCountText = {
             "第一天",
             "第二天",
             "第三天",
             "第四天",
             "第五天",
             "第六天",
             "第七天",
        },

        SignIn = {
            SignInProgressFmtStr = "已累计签到{days}天", ---注意复数
        },

        NewContent = {
            TaskGroupTitleFormatter = " ( {progress}/{total} ) {title}",
        },

        DailyMatch = {
            DayFormat = "第{day}天",
            DescSOL = "参与一局危险行动，可获得以下奖励",
            DescMP = "参与一局全面战场，可获得以下奖励",
            ItemLocked = "待解锁",

            
            FmtCompleteLoginAndPrevious = "登录{day}天，且完成前面对局后解锁",
            FmtCompleteLogin = "登录{day}天后解锁",
            CompletePrevious = "完成前面对局后解锁",

            ExpBoostReceived = "获得{count}次对局双倍经验效果",
            MeritsBoostReceived = "获得{count}次对局双倍军功效果",
        },
    }

    PlayerReturnConfig.Loc={
        FirstState = "第一阶段",
        SecondState = "第二阶段",
        ReflowTitle = "回归",
    }

else
    PlayerReturnConfig.Localization = {
        CountDownDHMFmtStr = NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_CountDownDHMFmtStr", "{d}天{h}时{m}分"),
        CountDownDHFmtStr = NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_CountDownDHFmtStr", "{d}天{h}时"),
        ActivityEndPopMessage = NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_ActivityEndPopMessage", "重返行动已结束"),
        
        DoubleExperience = NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_DoubleExperience", "双倍经验"),
        DoubleMerits = NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_DoubleMerits", "双倍军功"),

        DoubleExperienceCount = NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_DoubleExperienceCount", "({count}/{total})对局中获得双倍经验"),
        DoubleMeritsCount = NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_DoubleMeritsCount", "({count}/{total})对局中获得双倍军功"),

        LobbyPopup = {
            FxText = NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_LobbyPopup_FxText", "未知通信..."),
            Claim = NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_LobbyPopup_Claim", "领取"),
            Back = NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_LobbyPopup_Back", "返回"),
        },

        TaskPanel = {
            AlreadyOwned    = NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_AlreadyOwned", "已拥有"),
            Picked          = NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_Picked", "已选择"),
            Pick            = NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_Pick", "选择"),
        },

        -- 签到奖励展示和大厅弹窗显示签到天数共用
        DayCountText = {
            NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_DayCountText1", "第一天"),
            NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_DayCountText2", "第二天"),
            NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_DayCountText3", "第三天"),
            NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_DayCountText4", "第四天"),
            NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_DayCountText5", "第五天"),
            NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_DayCountText6", "第六天"),
            NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_DayCountText7", "第七天"),
        },

        SignIn = {
            SignInProgressFmtStr = NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_SignIn_SignInProgressFmtStr", "已累计签到{days}天") ---注意复数
        },

        NewContent = {
            TaskGroupTitleFormatter = NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_NewContent_TaskGroupTitleFormatter"," ( {progress}/{total} ) {title}")
        },

        DailyMatch = {
            DayFormat = NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_DailyMatch_DayFormat","第{day}天"),
            DescSOL = NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_DailyMatch_DescSOL","参与一局危险行动，可获得以下奖励"),
            DescMP = NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_DailyMatch_DescMP","参与一局全面战场，可获得以下奖励"),
            ItemLocked = NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_DailyMatch_ItemLocked","待解锁"),

            FmtCompleteLoginAndPrevious = NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_FmtCompleteLoginAndPrevious", "登录{day}天，且完成前面对局后解锁"),
            FmtCompleteLogin = NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_FmtCompleteLogin", "登录{day}天后解锁"),
            CompletePrevious = NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_CompletePrevious", "完成前面对局后解锁"),

            ExpBoostReceived = NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_ExpBoostReceived", "获得{count}次对局双倍经验效果"),
            MeritsBoostReceived = NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_MeritsBoostReceived", "获得{count}次对局双倍军功效果"),
        }
    }

    PlayerReturnConfig.Loc={
        FirstState=NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_FirstState", "第一阶段"),
        SecondState=NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_SecondState", "第二阶段"),
        ReflowTitle=NSLOCTEXT("PlayerReturnModule", "Lua_PlayerReturn_ReflowTitle", "回归"),
    }
end

PlayerReturnConfig.Audio = {
    LobbyPopupFirstDay = "UI_Lobby_Return_Long",
    LobbyPopupNormal = "UI_Lobby_Return_Short",
}

return PlayerReturnConfig
