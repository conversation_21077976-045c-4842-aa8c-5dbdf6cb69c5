----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRecruit)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class RecruitMainPanel_Mobile : LuaUIBaseView
local RecruitMainPanel_Mobile = ui("RecruitMainPanel_Mobile")
local RecruitConfig = Module.Recruit.Config
local RecruitLogic = require "DFM.Business.Module.RecruitModule.Logic.RecruitLogic"
local RecruitListFilterBtn = require("DFM.Business.Module.RecruitModule.UI.RecruitListFilterBtn")
local RecruitTeamMemberInfoItem = require("DFM.Business.Module.RecruitModule.UI.RecruitTeamMemberInfoItem")

function RecruitMainPanel_Mobile:Ctor()
    self._wtRefreshListBtn = self:Wnd("wtRefreshListBtn", DFCommonButtonOnly)
    self._wtFilterListBtn = self:Wnd("wtFilterListBtn", RecruitListFilterBtn)
    self._wtPublishRecruitmentBtn = self:Wnd("wtPublishRecruitmentBtn", DFCommonButtonOnly)
    self._wtRecruitmentInfoList = UIUtil.WndWaterfallScrollBox(self, "wtRecruitmentInfoList", self._OnGetRecruitmentInfoItemCount, self._OnCreateRecruitmentInfoItemWidget)
    self._wtIsPublishingIcon = self:Wnd("wtIsPublishingIcon", UIImage)
    self._wtTeamMemberNumText = self:Wnd("wtTeamMemberNumText", UITextBlock)
    self._teamMemberWidgets = {}
    for i = 1, 4, 1 do
        local teamMemberItem = self:Wnd(string.format("wtTeamMemberInfo_%s", tostring(i)), RecruitTeamMemberInfoItem)
        if teamMemberItem then
            table.insert(self._teamMemberWidgets, teamMemberItem)
        end
    end
    self._wtExitTeamBtn = self:Wnd("wtExitTeamBtn", DFCommonButtonOnly)
    self._wtExitTeamBtn:Event("OnClicked",self._OnExitTeamBtnClick,self)
    self._wtEmptySlot = self:Wnd("wtEmptySlot", UIWidgetBase)
    if self._wtEmptySlot then
        local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.CommonEmptyContent, self._wtEmptySlot, nil, nil)
        self._wtEmptyHint = getfromweak(weakUIIns)
    end
    self._wtEmptyHint:BP_SetText(RecruitConfig.Loc.EmptyContent)
    self._wtEmptyHint:SetCppValue("Set_Type", 1)
    self._wtEmptyHint:BP_Set_Type()
    self._wtPublishRecruitmentBtn:Event("OnClicked",self._OnPublishRecruitmentBtnClick,self)
    self._wtRefreshListBtn:Event("OnClicked",self._OnRefreshListBtnClick,self)
    self._wtFilterListBtn:Event("OnClicked",self._OnFilterListBtnClick,self)
    self._recruitmentInfo = {}
    Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.HideInfo)
    Module.CommonBar:RegStackUITopBarCurrencyTypeList(self, {})

    -- azhengzheng:复制组队码按钮
    self._wtCopyTeamCodeWB = self:Wnd("wtCopyTeamCodeWB", DFCommonButtonOnly)
    self._wtCopyTeamCodeWB:Event("OnClicked", self._OnCopyTeamCodeBtnClicked, self)
    self._wtCopyTeamCodeWB:Collapsed()
end

-- UI构造时触发, 用于初始化外部传参UI数据逻辑，由UIManager触发（仅触发一次）
-- 子节点蓝图不会触发该方法
-- Module加载好子控件所需的图片后，将数据传入主UI界面
---@overload fun(LuaUIBaseView, OnInitExtraData)
function RecruitMainPanel_Mobile:OnInitExtraData()
    Module.CommonBar:RegStackUITopBarTitle(self, RecruitConfig.SocialTabInfo[3].title)
end

-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function RecruitMainPanel_Mobile:OnOpen()

end

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function RecruitMainPanel_Mobile:OnClose()
    Facade.UIManager:ClearSubUIByParent(self, self._wtEmptySlot)
end

function RecruitMainPanel_Mobile:OnShowBegin()
    Server.GameModeServer:FetchEntranceDifficulty()
    self:AddListeners()
    RecruitLogic.SetLastRecommendListInfo(nil)
    self:RefreshView()
    self:_StartRefreshTimer()
end

function RecruitMainPanel_Mobile:OnHideBegin()
    self:_StopRefreshTimer()
    self:RemoveAllLuaEvent()
    RecruitLogic.SetFirstTimeOpenRecruitmentPanel(false)
end

-- 动画结束回调
---@overload fun(LuaUIBaseView, OnAnimFinished)
function RecruitMainPanel_Mobile:OnAnimFinished(anim)
end

-- UI监听事件、协议
function RecruitMainPanel_Mobile:AddListeners()
    self:AddLuaEvent(RecruitConfig.Events.evtOnRecruitmentRefreshPendingChanged, self._OnRecruitmentRefreshPendingChanged, self)
    self:AddLuaEvent(RecruitConfig.Events.evtOnLoadRecruitments, self._OnLoadRecruitmentList, self)
    self:AddLuaEvent(RecruitConfig.Events.evtOnUpdateRecruitments, self._OnUpdateRecruitmentList, self)
    self:AddLuaEvent(RecruitConfig.Events.evtOnUpdateRecruitmentState, self._OnUpdateRecruitmentState, self)
    self:AddLuaEvent(RecruitConfig.Events.evtOnTeamMemberInfoChanged, self._OnTeamMemberInfoChanged, self)
end


function RecruitMainPanel_Mobile:RefreshView()
    self:_OnRecruitmentRefreshPendingChanged()
    self._wtFilterListBtn:SetTitle(RecruitLogic.GetFilterBtnTitle())
    self:_OnUpdateRecruitmentState()
end    

function RecruitMainPanel_Mobile:_OnTeamMemberInfoChanged()
    local maxMemberNum = RecruitLogic.GetTeamMemberMaxNum()
    self._wtTeamMemberNumText:SelfHitTestInvisible()
    local teamMemberNum = Server.TeamServer:GetTeamNum()
    self._wtTeamMemberNumText:SetText(string.format(RecruitConfig.Loc.MyTeam, tostring(math.max(teamMemberNum, 1)), tostring(maxMemberNum)))
    local bIsInTeam = Server.TeamServer:IsInTeam()
    for index, memberWidget in ipairs(self._teamMemberWidgets) do
        memberWidget:InitPlayerInfo(nil)
        if index > maxMemberNum then
            memberWidget:Collapsed()
        else
            memberWidget:SelfHitTestInvisible()
        end
    end
    if bIsInTeam then
        local myInfo = Server.TeamServer:GetMyInfo()
        local myPlayerSimpleInfo = myInfo.PlayerSimpleInfo
        local otherMembersInfo = Server.TeamServer:GetOtherMembers()
        local sortedPlayerInfo = RecruitLogic.SortMembersBySeat(otherMembersInfo)
        local memberInfo = nil
        for index, memberWidget in ipairs(self._teamMemberWidgets) do
            memberInfo = nil
            if index == 1 then
                memberInfo = myPlayerSimpleInfo
            elseif sortedPlayerInfo[index-1] ~= nil and index <= maxMemberNum then
                memberInfo = sortedPlayerInfo[index-1].PlayerSimpleInfo
            end
            memberWidget:InitPlayerInfo(memberInfo)
        end
        if Module.Recruit:IsRecruiting() == true or #otherMembersInfo > 0 then
            self._wtExitTeamBtn:SelfHitTestInvisible()
        else
            self._wtExitTeamBtn:Collapsed() 
        end
    else
        local SOLRankScore = Server.RankingServer:GetRankScore()
        local SOLAttended = Server.RankingServer:GetHasAttended()
        local MPRankScore = Server.TournamentServer:GetRankScore()
        local MPCommanderRankScore = Server.TournamentServer:GetCommanderRankScore()
        local MPAttended = Server.TournamentServer:GetHasAttended()
        local myPlayerSimpleInfo = {
            player_id = Server.AccountServer:GetPlayerId(),
            nick_name = Server.RoleInfoServer.nickName or "",
            pic_url = Server.RoleInfoServer.picUrl,
            level = Server.RoleInfoServer.accountLevel,
            season_lvl = Server.RoleInfoServer.seasonLevel,
            gender = Server.RoleInfoServer.gender,
            safehouse_degree = 0,
            sol_rank_score = SOLRankScore,
            sol_rank_attended = SOLAttended,
            mp_rank_score = MPRankScore,
            mp_commander_score = MPCommanderRankScore,
            mp_rank_attended = MPAttended
        }
        self._teamMemberWidgets[1]:InitPlayerInfo(myPlayerSimpleInfo)
        self._wtExitTeamBtn:Collapsed()  
    end
    if Server.TeamServer:IsMember() then
        self._wtPublishRecruitmentBtn:Collapsed()
    else
        self._wtPublishRecruitmentBtn:SelfHitTestInvisible()
        self._wtPublishRecruitmentBtn:SetMainTitle(Module.Recruit:IsRecruiting() == true and RecruitConfig.Loc.UpdateRecruitment or RecruitConfig.Loc.PublishRecruitment)
    end
end


function RecruitMainPanel_Mobile:_OnRefreshListBtnClick()
    RecruitLogic.LoadRecommendRecruitment()
    RecruitLogic.SetRefreshPendingTime(RecruitConfig.RecruitmentListRefreshCD)
end   

--打开招募队伍筛选弹窗
function RecruitMainPanel_Mobile:_OnFilterListBtnClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.RecruitFilterPanel, nil, self)
end  

--打开创建招募弹窗
function RecruitMainPanel_Mobile:_OnPublishRecruitmentBtnClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.RecruitPublishingPanel, nil, self)
end  

function RecruitMainPanel_Mobile:_OnExitTeamBtnClick()
    if Server.TeamServer:IsCaptial() then
        Module.Recruit:StopRecruitmentAndExitTeam()
    else
        Server.TeamServer:ExitTeam()
    end
end

function RecruitMainPanel_Mobile:_OnGetRecruitmentInfoItemCount() 
    return #self._recruitmentInfo
end

function RecruitMainPanel_Mobile:_OnCreateRecruitmentInfoItemWidget(position, itemWidget)
    local recruitmentInfo = self._recruitmentInfo[position]
    itemWidget:InitRecruitmentInfo(recruitmentInfo)
    if self._bNeedPlayItemAnim == true then
        itemWidget:PlayInAnim(position)
    else
        itemWidget:SelfHitTestInvisible()
    end
    if position == #self._recruitmentInfo and self._bNeedPlayItemAnim == true then
        self._bNeedPlayItemAnim = false
    end
end

function RecruitMainPanel_Mobile:_OnRecruitmentRefreshPendingChanged()
    local pendingTime = Module.Recruit.Field:GetRefreshPendingTime()
    if pendingTime > 0 then
        self._wtRefreshListBtn:SetIsEnabledStyle(false)
        self._wtRefreshListBtn:SetMainTitle(StringUtil.Key2StrFormat(RecruitConfig.Loc.RefreshListPending,{["countTime"] = tostring(pendingTime)}))
    else
        self._wtRefreshListBtn:SetIsEnabledStyle(true)
        self._wtRefreshListBtn:SetMainTitle(RecruitConfig.Loc.RefreshList)
    end
end

function RecruitMainPanel_Mobile:_OnLoadRecruitmentList()
    local oldNum = #self._recruitmentInfo
    self._recruitmentInfo = RecruitLogic.GetRecruitmentList()
    if table.isempty(self._recruitmentInfo) then
        self._wtRecruitmentInfoList:Collapsed()
        self._wtEmptyHint:SelfHitTestInvisible()
    else
        self._wtEmptyHint:Collapsed()
        self._bNeedPlayItemAnim = true
        self._wtRecruitmentInfoList:Visible()
        if oldNum ~= #self._recruitmentInfo then
            self._wtRecruitmentInfoList:RefreshAllItems()
        else
            self._wtRecruitmentInfoList:RefreshVisibleItems() 
        end
    end
    self._wtFilterListBtn:SetTitle(RecruitLogic.GetFilterBtnTitle())
end

function RecruitMainPanel_Mobile:_OnUpdateRecruitmentList()
    local oldNum = #self._recruitmentInfo
    self._recruitmentInfo = RecruitLogic.GetRecruitmentList()
    if table.isempty(self._recruitmentInfo) then
        self._wtRecruitmentInfoList:Collapsed()
        self._wtEmptyHint:SelfHitTestInvisible()
    else
        self._wtEmptyHint:Collapsed()
        self._wtRecruitmentInfoList:Visible()
        if oldNum ~= #self._recruitmentInfo then
            self._wtRecruitmentInfoList:RefreshAllItems()
        else
            self._wtRecruitmentInfoList:RefreshVisibleItems() 
        end
    end
end

function RecruitMainPanel_Mobile:_OnUpdateRecruitmentState()
    self:SetTeamType(Module.Recruit:IsRecruiting() and 0 or 1)
    if Server.TeamServer:IsMember() then
        self._wtPublishRecruitmentBtn:Collapsed()
    else
        self._wtPublishRecruitmentBtn:SelfHitTestInvisible()
        self._wtPublishRecruitmentBtn:SetMainTitle(Module.Recruit:IsRecruiting() == true and RecruitConfig.Loc.UpdateRecruitment or RecruitConfig.Loc.PublishRecruitment)
    end
    self:_OnTeamMemberInfoChanged()
    RecruitLogic.LoadRecommendRecruitment()
end

function RecruitMainPanel_Mobile:_StartRefreshTimer()
    self:_StopRefreshTimer()
    self._refreshTimer = Timer:NewIns(RecruitConfig.RecruitmentListRefreshCD, 0)
    self._refreshTimer:AddListener(self._RefreshPendingTick, self)
    self._refreshTimer:Start()
end

function RecruitMainPanel_Mobile:_StopRefreshTimer()
    if isvalid(self._refreshTimer) then
        self._refreshTimer:Release()
    end
    self._refreshTimer = nil
end

function RecruitMainPanel_Mobile:_RefreshPendingTick()
    self._recruitmentInfo = RecruitLogic.GetRecruitmentList()
    if table.isempty(self._recruitmentInfo) then
        RecruitLogic.LoadRecommendRecruitment()
    else
        RecruitLogic.UpdateRecommendRecruitment()
    end 
end

-- azhengzheng:复制组队码按钮
function RecruitMainPanel_Mobile:_OnCopyTeamCodeBtnClicked()
    -- Module.CommonTips:ShowSimpleTip("复制组队码功能正在开发！")
end

return RecruitMainPanel_Mobile
