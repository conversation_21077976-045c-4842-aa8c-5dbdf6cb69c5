----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMMarket)
----- LOG FUNCTION AUTO GENERATE END -----------



local MandelBrickBuy = ui("MandelBrickBuy")
local IVShopItemTemplate = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.IVShopItemTemplate"
local MarketConfig = require "DFM.Business.Module.MarketModule.MarketConfig"
local MarketLogic = require "DFM.Business.Module.MarketModule.Logic.MarketLogic"
local CommonPopWindows = require "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindows"
local ShopHelperTool = require "DFM.StandaloneLua.BusinessTool.ShopHelperTool"
local AuctionBarChart = require "DFM.Business.Module.AuctionModule.UI.Buy.AuctionBarChart"
local HDKeyIconBox = require "DFM.Business.Module.CommonWidgetModule.UI.HD.HDKeyIconBox"
local BARCHARTVISIBLECOUNT = 5
local SORTMODE = {
    ASC = 0,
    DESC = 1,
}

--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
--- END MODIFICATION
-- 输入设备相关
local UGPInputDelegates = import "GPInputDelegates"
local EGPInputType = import "EGPInputType"
local UGPInputHelper = import "GPInputHelper"

function MandelBrickBuy:Ctor()
    self._itemId = nil
    self._saleInfo = nil
    self._curPrice = nil
    self._curInputNum = nil
    self._maxBuyNum = 0
    self._minBuyNum = 0
    self._sortMode = SORTMODE.ASC
    self._wtItemView = self:Wnd("WBP_ShopItemTemplate", IVShopItemTemplate)
    self._wtSpecifyPriceOption = self:Wnd("WBP_DFCommonCheckBoxWithText", UIWidgetBase)
    self._wtSpecifyPriceOption:Event("OnCheckStateChanged", self._OnSpecifyPriceCheckStateChanged, self)
    self._wtDFCommonAddDecInputBoxFactorPrice = self:Wnd("WBP_CommonAddDecPrice", DFCommonAddDecInputBox)
    self._wtDFCommonAddDecInputBoxFactorPrice:Event("OnAddDecInputBoxCurNumChanged", self._OnAddDecInputBoxFactorCurNumChangedPrice, self)
    self._wtDFCommonAddDecInputBoxFactorPrice:Event("OnAddDecInputBoxCurNumCommitted", self._OnAddDecInputBoxCurNumCommittedPrice, self)
    -- self._addFactor = 1.1
    -- self._decFactor = 0.9
    self._wtDFCommonAddDecInputBoxFactorPrice:BindCustomAddOnClicked(self.OnCustomFactorAddClickedPrice, self)
    self._wtDFCommonAddDecInputBoxFactorPrice:BindCustomDecOnClicked(self.OnCustomFactorDecClickedPrice, self)
    self._wtDFCommonAddDecInputBoxFactor = self:Wnd("WBP_CommonAddDecPrice_139", DFCommonAddDecInputBox)
    self._wtDFCommonAddDecInputBoxFactor:Event("OnAddDecInputBoxCurNumChanged", self._OnAddDecInputBoxFactorCurNumChanged, self)
    self._wtDFCommonAddDecInputBoxFactor:Event("OnAddDecInputBoxCurNumCommitted", self._OnAddDecInputBoxCurNumCommitted, self)
    -- self._addFactor = 1.1
    -- self._decFactor = 0.9
    self._wtDFCommonAddDecInputBoxFactor:BindCustomAddOnClicked(self.OnCustomFactorAddClicked, self)
    self._wtDFCommonAddDecInputBoxFactor:BindCustomDecOnClicked(self.OnCustomFactorDecClicked, self)
    self._wtAveragePriceTextBlock = self:Wnd("DFRichTextBlock_67", UITextBlock)
    self._wtTotalPriceTextBlock = self:Wnd("DFRichTextBlock", UITextBlock)
    self._wtCommonPopWinV2 = self:Wnd("WBP_CommonPopWindowsV2", CommonPopWindows)
    self._wtCommonPopWinV2:SetIsRecharge(true) --美国合规

    local fCallbackIns = CreateCallBack(self.OnCloseBtnClicked,self)
    self._wtCommonPopWinV2:BindCloseCallBack(fCallbackIns)
    local fCloseBeginCallbackIns = CreateCallBack(self.OnCloseBeginBtnClicked,self)
    self._wtCommonPopWinV2:BindCloseBeginCallBack(fCloseBeginCallbackIns)
    -- 柱状图
    self._wtBarChart = self:Wnd("WBP_Auction_BarChart", AuctionBarChart)
    self._wtSalesPanel = self:Wnd("ItemListPanel", UIWidgetBase)
    self._wtEmptyBgSlot = self:Wnd("EmptySlot", UIWidgetBase)
    self._wtDataProportionDes = self:Wnd("DFTextBlock_107", UITextBlock)
    self._wtInfoBtn = self:Wnd("Button_Info", DFCheckBoxOnly)
    -- self._wtInfoBtn:Event("OnCheckStateChanged", self._OnInfoBtnChanged, self)
    self._wtInfoBtn:SetCallback(self._OnInfoBtnChanged, self)
    self._wtTipsAnchorInstruction = UIUtil.WndTipsAnchor(self, "DFTipsAnchor_161", self._OnShowInstruction, self._OnHideInstruction)
    self._wtPreorderContentPanel = self:Wnd("DFCanvasPanel_70", UIWidgetBase)
    self._wtPreorderDes = self:Wnd("DFTextBlock_5", UITextBlock)
    self._wtPreorderCancelBtn = self:Wnd("DFButton_88", UIButton)
    self._wtPreorderCancelBtn:Event("OnClicked", self.OnPreorderCancelBtnClick, self)
    self._wtPreorderValidTime = self:Wnd("DFTextBlock_4", UITextBlock)
    self._wtKeyIconBox = self:Wnd("WBP_CommonKeyIconBox", HDKeyIconBox)
    self._wtPreorderBuyContentPanel = self:Wnd("DFCanvasPanel_71", UIWidgetBase)
end

function MandelBrickBuy:OnInitExtraData(itemId)
    self._itemId = itemId
    self._subPageType = Server.MarketServer:GetMarketItemType(itemId)
    self:InitConfirmBtnContent()
end

--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
function MandelBrickBuy:OnShowBegin()
    if IsHD() then
        self:_EnableGamepadFeature()
        if not self._OnNotifyInputTypeChangedHandle then
            -- 绑定多输入设备切换事件
            self._OnNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._OnInputTypeChanged, self))
        end
        -- 初始化
        local curInpurtType = UGPInputHelper.GetCurrentInputType(GetGameInstance())
        self:_OnInputTypeChanged(curInpurtType)
    end
end

function MandelBrickBuy:OnHideBegin()
    if IsHD() then
        self:_DisableGamepadFeature()
        if self._OnNotifyInputTypeChangedHandle then
            UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._OnNotifyInputTypeChangedHandle)
            self._OnNotifyInputTypeChangedHandle = nil
        end
    end
end
--- END MODIFICATION
function MandelBrickBuy:OnShow()
    local gameInst = GetGameInstance()
	UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonDownEvent:Add(self._OnHandleMouseButtonUpEvent, self)
end

function MandelBrickBuy:OnHide()
	local gameInst = GetGameInstance()
	UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonDownEvent:Remove(self._OnHandleMouseButtonUpEvent, self)
end

function MandelBrickBuy:OnOpen()
    self:AddLuaEvent(Server.MarketServer.Events.evtMarketSaleListChanged, self.Init, self)
    self:AddLuaEvent(Module.Reward.Config.Events.evtOpenWeaponSkinGainPop,self._SetVisible, self)
    self:AddLuaEvent(Module.Reward.Config.Events.evtOpenCollectionDetailPage, self._SetVisible, self)
    self:AddLuaEvent(Server.MarketServer.Events.evtMarketPreorderCreated, self._OnMarketPreorderCreated, self)
    self:AddLuaEvent(Server.MarketServer.Events.evtMarketPreorderRemoved, self._OnMarketPreorderRemoved, self)
    self:AddLuaEvent(Server.MarketServer.Events.evtMarketPreorderChanged, self.RefreshRightPanel, self)
end

function MandelBrickBuy:OnClose()
    Module.ItemDetail:CloseAllPopUI()
    Facade.UIManager:ClearSubUIByParent(self, self._wtEmptyBgSlot)
    self:RemoveAllLuaEvent()
    self:ReleaseRefreshRemainTimeTimer()
end

function MandelBrickBuy:OnNavBack()
    return false
end

function MandelBrickBuy:InitConfirmBtnContent()
    if self._wtCommonPopWinV2 and not self._wtConfirmBtn then
        self.dfCommonButtons = self._wtCommonPopWinV2:SetConfirmBtnType(CommonPopWindows.EConfirmBtnType.CenterConfirm, {
            {btnText = Module.Market.Config.Loc.ScreeningConfirm, fClickCallback = self._OnConfirmBtnClicked, caller = self}
        })
        if self.dfCommonButtons then
            self._wtConfirmBtn = self.dfCommonButtons[CommonPopWindows.EHandleBtnType.Confirm]
            if self._wtConfirmBtn then
                self._wtConfirmBtn:RemoveEvent("OnClicked")
                self._wtConfirmBtn:Event("OnClicked", self._OnConfirmBtnClicked, self)
                self._wtConfirmBtn:RemoveEvent("OnDeClicked")
                self._wtConfirmBtn:Event("OnDeClicked", self._OnConfirmBtnClicked, self)  
            end
        end
        self._wtCommonPopWinV2:SetCenterBtnBg(false)
    end
    if self._itemId and Server.MarketServer:GetPreorderOrder(self._itemId) then
        if self._wtConfirmBtn then
            self._wtConfirmBtn:Collapsed()
        end
    else
        if self._wtConfirmBtn then
            self._wtConfirmBtn:SelfHitTestInvisible()
        end
    end
end

function MandelBrickBuy:FetchSaleData()
    if self._itemId then
        Server.MarketServer:FetchSaleList(self._itemId)
    end
end

function MandelBrickBuy:Init(saleListInfo)
    if self._itemId then
        if saleListInfo then
            self._saleInfo = {}
            deepcopy(self._saleInfo, saleListInfo)
        end
    end
    if self._saleInfo then
        self:_SortFun()
        self:RefreshLeftPanel()
        self:RefreshRightPanel()
    end
end

function MandelBrickBuy:RefreshLeftPanel()
    self:RefreshDataChartPanel()
    self:RefreshDataProportionDes()
end

function MandelBrickBuy:RefreshRightPanel(propId)
    if propId and propId ~= self._itemId then
        return
    end
    if self._itemId and self._saleInfo then
        -- 刷新itemView
        local item = ItemBase:NewIns(self._itemId)
        self._wtItemView:InitMarketItem(item, self._saleInfo)
        self._wtItemView:EnableComponent(Module.CommonWidget.Config.EIVWarehouseTempComponent.BottomLeftIconText, false)
        self._wtItemView:EnableComponent(Module.CommonWidget.Config.EIVWarehouseTempComponent.BottomRightIconText, false)
        self._wtItemView:BindClickCallback(CreateCallBack(self._OnItemViewClicked, self))
        self._wtItemView:SelfHitTestInvisible()
        if self._subPageType == EMarketSubPageType.MandelBrick then
            self._wtPreorderBuyContentPanel:SelfHitTestInvisible()
            if self.SetMeleeWeapons then
                self:SetMeleeWeapons(false)
            end
            self:RefreshSpecifyPriceInputBox()
            self:RefreshBuyNumInputBox()
            self:RefreshPreorderContent()
        elseif self._subPageType == EMarketSubPageType.MeleeWeapon then
            self._wtPreorderBuyContentPanel:Collapsed()
            if self.SetMeleeWeapons then
                self:SetMeleeWeapons(true)
            end
            self._curPrice = (self._saleInfo.sale_lists and self._saleInfo.sale_lists[1]) and self._saleInfo.sale_lists[1].price or 0
            self._curInputNum = 1
        end
        self:_RefreshFee()
    end
end

-- 刷新指定价格输入框
function MandelBrickBuy:RefreshSpecifyPriceInputBox()
    if not self._itemId then
        return
    end
    local preorder = Server.MarketServer:GetPreorderOrder(self._itemId)
    if preorder then
        self._wtSpecifyPriceOption:SetIsChecked(false, false)
        self._wtSpecifyPriceOption:SetIsEnabled(false)
        if preorder.price and self._saleInfo.price_range_begin and self._saleInfo.price_range_end then
            self._bCommittedFromInit_1 = nil
            self._wtDFCommonAddDecInputBoxFactorPrice:InitNum(preorder.price, 1, self._saleInfo.price_range_begin, self._saleInfo.price_range_end)
        end
        self._wtDFCommonAddDecInputBoxFactorPrice:SetIsEnabled(false)
    else
        self._wtSpecifyPriceOption:SetIsChecked(false, false)
        self._wtSpecifyPriceOption:SetIsEnabled(true)
        if self._saleInfo.price_range_begin and self._saleInfo.price_range_end then
            self._bCommittedFromInit_1 = nil
            if self._saleInfo.average_price then
                self._wtDFCommonAddDecInputBoxFactorPrice:InitNum(self._saleInfo.average_price, 1, self._saleInfo.price_range_begin, self._saleInfo.price_range_end)
            else
                self._wtDFCommonAddDecInputBoxFactorPrice:InitNum(1, 1, self._saleInfo.price_range_begin, self._saleInfo.price_range_end)
            end
        end
        self._wtDFCommonAddDecInputBoxFactorPrice:SetIsEnabled(false)
    end
end

-- 刷新购买数量输入框
function MandelBrickBuy:RefreshBuyNumInputBox()
    if not self._itemId then
        return
    end
    if self._saleInfo.sale_lists and self._saleInfo.max_buy_num then
        local saleNum = 0
        for _, saleList in ipairs(self._saleInfo.sale_lists) do
            saleNum = saleNum + saleList.selling_num
        end
        self._maxBuyNum = self._saleInfo.max_buy_num
        self._minBuyNum = saleNum > 0 and 1 or 0
        local preorder = Server.MarketServer:GetPreorderOrder(self._itemId)
        if preorder then
            self._wtDFCommonAddDecInputBoxFactor:SetIsEnabled(false)
            if preorder.pre_buy_num then
                self._bCommittedFromInit = nil
                if preorder.pre_buy_num >= self._minBuyNum and preorder.pre_buy_num <= self._maxBuyNum then
                    self._wtDFCommonAddDecInputBoxFactor:InitNum(preorder.pre_buy_num, 1, self._minBuyNum, self._maxBuyNum)
                else
                    self._wtDFCommonAddDecInputBoxFactor:InitNum(1, 1, self._minBuyNum, self._maxBuyNum)
                    logwarning("MandelBrickBuy:RefreshBuyNumInputBox pre_buy_num is out of range", preorder.pre_buy_num, self._maxBuyNum, self._minBuyNum)
                end
            end
        else
            self._wtDFCommonAddDecInputBoxFactor:SetIsEnabled(true)
            self._bCommittedFromInit = nil
            self._wtDFCommonAddDecInputBoxFactor:InitNum(1, 1, self._minBuyNum, self._maxBuyNum)
        end
    end
end

function MandelBrickBuy:RefreshDataChartPanel()
    if self._wtBarChart and self._saleInfo and self._saleInfo.sale_lists then
        if not table.isempty(self._saleInfo.sale_lists) and self._saleInfo.buy_currency_id then
            self._wtSalesPanel:SelfHitTestInvisible()
            if self._wtEmptyBgSlot then
                self._wtEmptyBgSlot:Collapsed()
            end
            local dataPoints = {}
            for k, v in ipairs(self._saleInfo.sale_lists) do
                if v.price and v.selling_num then
                    local dataPoint = {v.price, v.selling_num}
                    table.insert(dataPoints, dataPoint)
                end
            end
            local newDataPoints = MarketLogic.dataPointsProcessingForBarChar(dataPoints, 5)
            self._wtBarChart:SelfHitTestInvisible()
            local bShowComponentParams = {
                yellowBarParams = {bShowYellowBar = false, specialBarChartData = {}},
                greenBarParams = {bShowGreenBar = false, specialBarChartData = {}},
            }
            self._wtBarChart:InitStaticCharts(newDataPoints, self._saleInfo.buy_currency_id, BARCHARTVISIBLECOUNT, bShowComponentParams, nil, true)
        else
            self._wtSalesPanel:Collapsed()
            if self._wtEmptyBgSlot then
                Facade.UIManager:RemoveSubUIByParent(self, self._wtEmptyBgSlot)
                self._wtEmptyBgSlot:SelfHitTestInvisible()
                local weakUIIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.CommonEmptyContent, self._wtEmptyBgSlot)
                local emptyBg = getfromweak(weakUIIns)
                if emptyBg then
                    emptyBg:BP_SetText(Module.Market.Config.Loc.NoSaleInMarket)
                    emptyBg:BP_SetTypeWithParam(1)
                    emptyBg:Visible()
                end
            end
        end
    end
end

function MandelBrickBuy:RefreshDataProportionDes()
    if self._wtDataProportionDes then
        if self._saleInfo and self._saleInfo.sale_lists then
            if not table.isempty(self._saleInfo.sale_lists) then
                local saleNum = 0
                for _, saleList in ipairs(self._saleInfo.sale_lists) do
                    saleNum = saleNum + saleList.selling_num
                end
                self._wtDataProportionDes:SelfHitTestInvisible()
                if self._saleInfo.cur_num and self._saleInfo.cur_num > 0 then
                    self._wtDataProportionDes:SetText(string.format(MarketConfig.Loc.DataProportionDes, string.format("%.2f", saleNum / self._saleInfo.cur_num * 100)))
                else
                    logerror("MandelBrickBuy:RefreshDataProportionDes sale_lists selling_num is not zero but saleInfo cur_num is zero")
                    self._wtDataProportionDes:SetText(string.format(MarketConfig.Loc.DataProportionDes, string.format("%.2f", 1 * 100)))
                end
            else
                self._wtDataProportionDes:Collapsed()
            end
        end
    end 
end

function MandelBrickBuy:RefreshPreorderContent()
    self:RefreshPreorderState()
    self:RefreshRemainTime()
    self:StartRefreshRemainTimeTimer()
end

function MandelBrickBuy:RefreshPreorderState()
    self:InitConfirmBtnContent()
    self:_RefreshFee()
    if self._itemId then
        self._wtPreorderContentPanel:SelfHitTestInvisible()
        local preorder = Server.MarketServer:GetPreorderOrder(self._itemId)
        if preorder then
            self._wtPreorderDes:SetText(string.format(MarketConfig.Loc.PreorderBuyingState, preorder.succ_buy_num, preorder.pre_buy_num))
            self:_InitBuyingShortcuts()
        else
            self._wtPreorderContentPanel:Collapsed()
            self:_RemoveBuyingShortcuts()
        end
    else
        self._wtPreorderContentPanel:Collapsed()
    end
end

function MandelBrickBuy:StartRefreshRemainTimeTimer()
    self:ReleaseRefreshRemainTimeTimer()
    self._refreshRemainTimeTimerHandle = Timer:NewIns(1, 0)
    self._refreshRemainTimeTimerHandle:AddListener(self.RefreshRemainTime, self)
    self._refreshRemainTimeTimerHandle:Start()
end

function MandelBrickBuy:ReleaseRefreshRemainTimeTimer()
    if self._refreshRemainTimeTimerHandle then
        self._refreshRemainTimeTimerHandle:Release()
        self._refreshRemainTimeTimerHandle = nil
    end
end

function MandelBrickBuy:RefreshRemainTime()
    if self._itemId then
        local preorder = Server.MarketServer:GetPreorderOrder(self._itemId)
        if preorder then
            local curTime = Facade.ClockManager:GetLocalTimestamp()
            local remainTime = preorder.expire_time - curTime
            if remainTime > 0 then
                if self.SetType then
                    self:SetType(0)
                end
                local remainTimeStr = TimeUtil.GetSecondsFormatHHMMSSString(remainTime)
                self._wtPreorderValidTime:SetText(remainTimeStr)
            else
                if self.SetType then
                    self:SetType(1)
                end
                if self.StopPreorderAnimation then
                    self:StopPreorderAnimation()
                end
                self._wtPreorderValidTime:SetText(MarketConfig.Loc.PreorderExpireTime)
                self._wtPreorderDes:SetText(string.format(MarketConfig.Loc.PreorderBuyingStateExpire, preorder.succ_buy_num, preorder.pre_buy_num))
            end
        else
            self._wtPreorderValidTime:SetText(MarketConfig.Loc.PreorderValidTime)
            self:RefreshPreorderState()
            self:ReleaseRefreshRemainTimeTimer()
        end
    else
        self._wtPreorderValidTime:SetText(MarketConfig.Loc.PreorderValidTime)
    end
    self._wtPreorderValidTime:SelfHitTestInvisible()
end

function MandelBrickBuy:_OnItemViewClicked()
    if self._itemId then
        if self._subPageType == EMarketSubPageType.MandelBrick then
            local item = ItemBase:NewIns(self._itemId)
            local function fOnDetailPanelLoaded(detailIns)
                -- if detailIns then
                --     detailIns:SetBlindBoxCallBack(CreateCallBack(self._SetVisible, self))
                -- end
            end
            Module.ItemDetail:OpenItemDetailPanel(item, self._wtItemView, false, false, nil, nil, fOnDetailPanelLoaded, nil, nil, nil)
        elseif self._subPageType == EMarketSubPageType.MeleeWeapon then
            if self._saleInfo and self._saleInfo.sale_lists then
                MarketLogic.OpenMysticalSkinModelView(self._itemId, self._saleInfo.sale_lists[1])
            end
        end
    end
end

function MandelBrickBuy:_OnAddDecInputBoxFactorCurNumChanged(curNum, changeNum)
    self.changeNum = changeNum
end

function MandelBrickBuy:_OnAddDecInputBoxCurNumCommitted(curNum, changeNum)
    if self._curInputNum ~= curNum then
        self._curInputNum = curNum
        self:_RefreshFee()
    end
    -- 通用控件初始化会触发两次commit
    self._bCommittedFromInit = setdefault(self._bCommittedFromInit, 0)
    if self._bCommittedFromInit < 1 then
        self._bCommittedFromInit = self._bCommittedFromInit + 1
        return
    end
    if self._maxBuyNum ~= 0 and self._maxBuyNum == curNum and self.changeNum == 0 then
        Module.CommonTips:ShowSimpleTip(MarketConfig.Loc.NumCannotHigherThanThreshold)
    end
    if self._minBuyNum ~= 0 and self._minBuyNum == curNum and self.changeNum == 0 then
        Module.CommonTips:ShowSimpleTip(MarketConfig.Loc.NumCannotLowerThanThreshold)
    end
end

function MandelBrickBuy:OnCustomFactorAddClicked(curNum)
    return curNum + 1
end

function MandelBrickBuy:OnCustomFactorDecClicked(curNum)
    return curNum - 1
end

function MandelBrickBuy:_OnAddDecInputBoxFactorCurNumChangedPrice(curNum, changeNum)
    self.changeNum_1 = changeNum
end

function MandelBrickBuy:_OnAddDecInputBoxCurNumCommittedPrice(curNum, changeNum)
    if self._curPrice ~= curNum then
        self._curPrice = curNum
        self:_RefreshFee()
    end
    -- 通用控件初始化会触发一次commit，且进入此UI也会在OnInitExtraData生命周期内默认触发一次
    self._bCommittedFromInit_1 = setdefault(self._bCommittedFromInit_1, 0)
    if self._bCommittedFromInit_1 < 1 then
        self._bCommittedFromInit_1 = self._bCommittedFromInit_1 + 1
        return
    end
    if self._saleInfo and self._saleInfo.price_range_begin and self._saleInfo.price_range_end then
        if self._saleInfo.price_range_end ~= 0 and self._saleInfo.price_range_end == curNum and self.changeNum_1 == 0 then
            Module.CommonTips:ShowSimpleTip(MarketConfig.Loc.PriceCannotHigherThanThreshold)
        end
        if self._saleInfo.price_range_begin ~= 0 and self._saleInfo.price_range_begin == curNum and self.changeNum_1 == 0 then
            Module.CommonTips:ShowSimpleTip(MarketConfig.Loc.PriceCannotLowerThanThreshold)
        end
    end
end

function MandelBrickBuy:OnCustomFactorAddClickedPrice(curNum)
    return curNum + 1
end

function MandelBrickBuy:OnCustomFactorDecClickedPrice(curNum)
    return curNum - 1
end

function MandelBrickBuy:_RefreshFee()
    if self._saleInfo and self._saleInfo.sale_lists then
        if not table.isempty(self._saleInfo.sale_lists) then
            local totalPrice = self._curPrice * self._curInputNum
            local currencyId = Module.Currency:ConvertCurrencyIdByItemId(self._saleInfo.buy_currency_id)
            local currencyIcon = Module.Currency:GetRichTxtImgId(currencyId)
            if self._wtAveragePriceTextBlock and self._wtTotalPriceTextBlock then
                self._wtAveragePriceTextBlock:SetText(string.format(Module.Market.Config.Loc.CurrencyNum, 52, 52, currencyIcon,
                self._saleInfo.average_price and MathUtil.GetNumberFormatStr(self._saleInfo.average_price + 0.00001) or "--"))
                self._wtAveragePriceTextBlock:SelfHitTestInvisible()
                self._wtTotalPriceTextBlock:SetText(string.format(Module.Market.Config.Loc.CurrencyNum, 52, 52, currencyIcon,
                MathUtil.GetNumberFormatStr(totalPrice + 0.00001)))
                self._wtTotalPriceTextBlock:SelfHitTestInvisible()
            end
            local currencyNum = Module.Currency:GetNumByItemId(self._saleInfo.buy_currency_id)
            if currencyNum < totalPrice then
                if self._wtConfirmBtn then
                    self._wtConfirmBtn:SetMainTitle(string.format(Module.Market.Config.Loc.MarketGoodBuyLimitedPrice, currencyIcon,
                     MathUtil.GetNumberFormatStr(totalPrice + 0.00001)))
                    self._wtConfirmBtn:SetIsEnabledStyle(true)
                end
            else
                if self._wtConfirmBtn then
                    self._wtConfirmBtn:SetMainTitle(string.format(Module.Market.Config.Loc.BuyBtnText, currencyIcon,
                     MathUtil.GetNumberFormatStr(totalPrice + 0.00001)))
                    self._wtConfirmBtn:SetIsEnabledStyle(true)
                end
            end
        else
            local currencyId = Module.Currency:ConvertCurrencyIdByItemId(self._saleInfo.buy_currency_id)
            local currencyIcon = Module.Currency:GetRichTxtImgId(currencyId)
            if self._wtAveragePriceTextBlock and self._wtTotalPriceTextBlock then
                self._wtAveragePriceTextBlock:SetText(string.format(Module.Market.Config.Loc.CurrencyNum, 52, 52, currencyIcon, "--"))
                self._wtAveragePriceTextBlock:SelfHitTestInvisible()
                self._wtTotalPriceTextBlock:SetText(string.format(Module.Market.Config.Loc.CurrencyNum, 52, 52, currencyIcon, "--"))
                self._wtTotalPriceTextBlock:SelfHitTestInvisible()
            end
            if self._wtConfirmBtn then
                self._wtConfirmBtn:SetMainTitle(string.format(Module.Market.Config.Loc.BuyBtnText, currencyIcon, "--"))
                self._wtConfirmBtn:SetIsEnabledStyle(false)
            end  
        end
    end
end

function MandelBrickBuy:_SortFun()
    local function ASCSort(a, b)
        return a.price < b.price
    end
    local function DESCSort(a, b)
        return a.price > b.price
    end
    if self._saleInfo and self._saleInfo.sale_lists then
        -- 直接对server里的数据做排序影响到原始的数据，会导致每次都会根据排序后的数据再排序
        if self._sortMode == SORTMODE.ASC then
            table.insertionSort(self._saleInfo.sale_lists, ASCSort)
            -- table.sort(self._saleList, ASCSort)
        elseif self._sortMode == SORTMODE.DESC then
            table.insertionSort(self._saleInfo.sale_lists, DESCSort)
            -- table.sort(self._saleList, DESCSort)
        end
    end
end

function MandelBrickBuy:OnCloseBtnClicked()
    Module.ItemDetail:CloseItemDetailPanel()
    Facade.UIManager:CloseUI(self)
end

function MandelBrickBuy:OnCloseBeginBtnClicked()

end

function MandelBrickBuy:_OnConfirmBtnClicked()
    -- Module.Auction.Config.Events.evtAuctionRealSellAuctionClicked:Invoke() -- todo 类拍卖行，播放点击声音之类的
    if self._itemId and Server.MarketServer:GetPreorderOrder(self._itemId) then
        return
    end
    if self._itemId and self._saleInfo then
        if not table.isempty(self._saleInfo.sale_lists) then
            local bSpecialPrice = false
            if self._wtSpecifyPriceOption and self._wtSpecifyPriceOption.GetIsChecked then
                bSpecialPrice = self._wtSpecifyPriceOption:GetIsChecked()
            end
            local totalPrice = self._curPrice * self._curInputNum
            local currencyNum = Module.Currency:GetNumByItemId(self._saleInfo.buy_currency_id)
            if currencyNum < totalPrice then
                if self._subPageType == EMarketSubPageType.MandelBrick then
                    local function fOnExchangeCallback(bSuccess)
                        if bSuccess then
                            local currencyNumNew = Module.Currency:GetNumByItemId(self._saleInfo.buy_currency_id)
                            if currencyNumNew >= totalPrice then
                                MarketLogic.DoPreorderBuyProcess(self._saleInfo, self._curInputNum, self._curPrice, bSpecialPrice)
                            else
                                ShopHelperTool.ShowNotEnoughTipByCurrencyId(self._saleInfo.buy_currency_id)
                                logerror("MandelBrickBuy:_OnConfirmBtnClicked ExchangeMandelCoins success but not enough")
                            end
                        else
                            loginfo("MandelBrickBuy:_OnConfirmBtnClicked ExchangeMandelCoins failed")
                        end
                    end
                    Facade.UIManager:AsyncShowUI(UIName2ID.ExchangeMandelCoins, nil, nil, self._saleInfo.buy_currency_id, totalPrice - currencyNum, fOnExchangeCallback)
                elseif self._subPageType == EMarketSubPageType.MeleeWeapon then
                    ShopHelperTool.ShowNotEnoughTipByCurrencyId(self._saleInfo.buy_currency_id)
                end
            else
                if self._subPageType == EMarketSubPageType.MandelBrick then
                    MarketLogic.DoPreorderBuyProcess(self._saleInfo, self._curInputNum, self._curPrice, bSpecialPrice)
                elseif self._subPageType == EMarketSubPageType.MeleeWeapon then
                    MarketLogic.DoBuyMeleeWeaponProcess(self._saleInfo.sale_lists[1], self._curInputNum)
                    Facade.UIManager:CloseUI(self)
                end
            end
        else -- 无在售
            Module.CommonTips:ShowSimpleTip(MarketConfig.Loc.NoSaleInMarket)
            return
        end
        -- self:RemoveAllLuaEvent() -- 屏蔽UI还未关闭时的回调
    end
end

function MandelBrickBuy:_SetVisible(visible,eType)
    if not visible then
        self:Show(false)
    else
        self:Hide(true)
    end
end

function MandelBrickBuy:_OnShowInstruction()
    self:_OnHideInstruction()
    if self._wtTipsAnchorInstruction then
        local contents = {}
        table.insert(contents, {
            id = UIName2ID.Assembled_CommonMessageTips_V1,
            data = {
                textContent = self._subPageType == EMarketSubPageType.MandelBrick and MarketConfig.Loc.MandelBrickSellDes or MarketConfig.Loc.MysticalSkinSellDes
            }
        })
        self._tipHandle = Module.CommonTips:ShowAssembledTips(contents, self._wtTipsAnchorInstruction)
    end
end

function MandelBrickBuy:_OnHideInstruction(reason)
    if self._tipHandle then
        if self._wtTipsAnchorInstruction then
            Module.CommonTips:RemoveAssembledTips(self._tipHandle, self._wtTipsAnchorInstruction)
        end
        self._tipHandle = nil
    end
end

function MandelBrickBuy:_OnSpecifyPriceCheckStateChanged(bChecked)
    self._wtDFCommonAddDecInputBoxFactorPrice:SetIsEnabled(bChecked)
    if not bChecked then
        self:RefreshSpecifyPriceInputBox()
    end
end

function MandelBrickBuy:OnPreorderCancelBtnClick()
    MarketLogic.DoPreorderCancelProcess({self._itemId})
end

--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
function MandelBrickBuy:_EnableGamepadFeature()
    if not IsHD() then
        return 
    end

    if not self._NavGroup then
        self._NavGroup = WidgetUtil.RegisterNavigationGroup(self._wtItemView, self, "Hittest")
        if self._NavGroup then
            self._NavGroup:AddNavWidgetToArray(self._wtItemView)
            self._NavGroup:MarkIsStackControlGroup()
        end
    end

    if not self._NavGroup1 then
        self._NavGroup1 = WidgetUtil.RegisterNavigationGroup(self._wtSpecifyPriceOption, self, "Hittest")
        if self._NavGroup1 then
            self._NavGroup1:AddNavWidgetToArray(self._wtSpecifyPriceOption)
        end
    end

    if not self._NavGroup2 then
        self._NavGroup2 = WidgetUtil.RegisterNavigationGroup(self._wtDFCommonAddDecInputBoxFactorPrice, self, "Hittest")
        if self._NavGroup2 then
            self._NavGroup2:AddNavWidgetToArray(self._wtDFCommonAddDecInputBoxFactorPrice)
        end
    end

    if not self._NavGroup3 then
        self._NavGroup3 = WidgetUtil.RegisterNavigationGroup(self._wtDFCommonAddDecInputBoxFactor, self, "Hittest")
        if self._NavGroup3 then
            self._NavGroup3:AddNavWidgetToArray(self._wtDFCommonAddDecInputBoxFactor)
        end
    end
    if WidgetUtil.IsGamepad() then
        if self._subPageType == EMarketSubPageType.MandelBrick then
            if self._itemId and Server.MarketServer:GetPreorderOrder(self._itemId) then
                WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup)
            else
                WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup3)
            end
        elseif self._subPageType == EMarketSubPageType.MeleeWeapon then
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup)
        end
    end
    self:_InitShortcuts()
end

function MandelBrickBuy:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end
    
    WidgetUtil.RemoveNavigationGroup(self)
    if self._NavGroup then
        self._NavGroup = nil
    end

    if self._NavGroup1 then
        self._NavGroup1 = nil
    end

    if self._NavGroup2 then
        self._NavGroup2 = nil
    end

    if self._NavGroup3 then
        self._NavGroup3 = nil
    end
    self:_RemoveShortcuts()
end

function MandelBrickBuy:_InitShortcuts()
    -- 手柄模式下，确认为长按X键
    self._wtCommonPopWinV2:OverrideGamepadSetting("MandelBrickBuyComfirm_Gamepad", nil, WidgetUtil.EUINavDynamicType.Default, true)
    if not self._ToggleTip then
        self._ToggleTip = self:AddInputActionBinding("Common_ToggleTip", EInputEvent.IE_Pressed, self._ToggleTips,self, EDisplayInputActionPriority.UI_Pop)
    end
    self._wtCommonPopWinV2:AddSummaries({"Common_ToggleTip"})
end

function MandelBrickBuy:_RemoveShortcuts()
    if not IsHD() then
        return 
    end
    if self._ToggleTip then
        self:RemoveInputActionBinding(self._ToggleTip)
        self._ToggleTip = nil
    end
end

function MandelBrickBuy:_InitBuyingShortcuts()
    if not IsHD() then
        return 
    end
    if not self._CanelOrder then
        self._CanelOrder = self:AddInputActionBinding("MandelBrickBuy_CancelOrder", EInputEvent.IE_Pressed, self.OnPreorderCancelBtnClick,self, EDisplayInputActionPriority.UI_Pop)
        if self._wtKeyIconBox then
            --设置是否只在Gamepad上显示
            self._wtKeyIconBox:SetOnlyDisplayOnGamepad(true)
            --设置当前KeyIcon绑定的Action
            self._wtKeyIconBox:InitByDisplayInputActionName("MandelBrickBuy_CancelOrder", true, 0.0, true)
        else
            logerror('Cant find KeyIcon named [KeyIcon], Failed to DFCommonButtonOnly:SetDisplayInputAction(displayInputAction,bShowNoneKey, keyIconHeight)', " self:", self)
        end
    end
end

function MandelBrickBuy:_RemoveBuyingShortcuts()
    if not IsHD() then
        return 
    end
    if self._CanelOrder then
        self:RemoveInputActionBinding(self._CanelOrder)
        self._CanelOrder = nil
    end
end

function MandelBrickBuy:_ToggleTips()
    if self._wtInfoBtn:GetIsChecked() then
        self._wtInfoBtn:SetIsChecked(false, true)
    else
        self._wtInfoBtn:SetIsChecked(true, true)
    end
end

function MandelBrickBuy:_OnInputTypeChanged(InputType)
    if InputType == EGPInputType.Gamepad then
        if self._subPageType == EMarketSubPageType.MandelBrick then
            if self._itemId and Server.MarketServer:GetPreorderOrder(self._itemId) then
                if self._NavGroup then
                    WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup)
                end
            else
                if self._NavGroup3 then
                    WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup3)
                end
            end
        elseif self._subPageType == EMarketSubPageType.MeleeWeapon then
            if self._NavGroup then
                WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup)
            end
        end
    else
    end
end

--- END MODIFICATION

function MandelBrickBuy:_OnInfoBtnChanged(bChecked)
    if bChecked then
        self:_OnShowInstruction()
    else
        self:_OnHideInstruction()
    end
end

function MandelBrickBuy:_OnHandleMouseButtonUpEvent(mouseEvent)
    if not self:IsRelease() and self then
        local absolutePoint = mouseEvent:GetScreenSpacePosition()
        local bInfoBtnInside = UIUtil.CheckAbsolutePointInsideWidget(self._wtInfoBtn, absolutePoint)
        if not bInfoBtnInside then
            self._wtInfoBtn:SetIsChecked(false, true)
        end
    end
end

function MandelBrickBuy:_OnMarketPreorderCreated(propId)
    if IsHD() and WidgetUtil.IsGamepad()then
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup)
    end
    self:RefreshRightPanel(propId)
    self:_InitBuyingShortcuts()
end

function MandelBrickBuy:_OnMarketPreorderRemoved(propId)
    self:RefreshRightPanel(propId)
    self:_RemoveBuyingShortcuts()
end

return MandelBrickBuy