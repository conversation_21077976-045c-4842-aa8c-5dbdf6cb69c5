----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMStore)
----- LOG FUNCTION AUTO GENERATE END -----------



local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local IVCommonItemTemplate = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.IVCommonItemTemplate"

local EComp = CommonWidgetConfig.EIVWarehouseTempComponent
local EIVSlotPos = CommonWidgetConfig.EIVSlotPos
local EIVCompOrder = CommonWidgetConfig.EIVCompOrder

---@class StoreItemBox : LuaUIBaseView
local StoreItemBox = ui("StoreItemBox")

function StoreItemBox:Ctor()
	self._wtCommonItemTemplate = self:Wnd("wtCommonItemTemplate", IVCommonItemTemplate)
    self._wtCommonItemTemplate:BindCustomOnClicked(CreateCallBack(self._OnItemWidgetClick, self))
	self._wtTextBorder = self:Wnd("wtTextBorder", UIWidgetBase)
	self._wtOwnedText = self:Wnd("wtOwnedText", UITextBlock)
	self._wtCannotRestoreImg = self:Wnd("wtCannotRestoreImg", UIImage)
	-- BEGIN MODIFICATION @ VIRTUOS : 
	if IsHD() then
		self:SetCppValue("bIsFocusable", true)
	end
	-- END MODIFICATION
end


function StoreItemBox:OnInitExtraData()
end

function StoreItemBox:OnOpen()

end

function StoreItemBox:SetInfo(item)
    self.item = item
	self._wtCommonItemTemplate:InitItem(item)
	self._wtTextBorder:Collapsed()
	self._wtOwnedText:Collapsed()
	if item.restore_flag ~= nil and item.restore_flag == false then
		self._wtCannotRestoreImg:SelfHitTestInvisible()
	else
		self._wtCannotRestoreImg:Collapsed()
	end
	if item.owned == true then
		self._wtOwnedText:Visible()
		self._wtTextBorder:Visible()
	end
	--新增限时图标显示
	if item then
		local compId  = EComp.BottomLeftIconText
		local slotPos = EIVSlotPos.BottomLeft
		local uiNavId = UIName2ID.IVTextIconComponent
		if item.start_time ~= item.end_time then
			local addWeget = self._wtCommonItemTemplate:FindOrAdd(compId, uiNavId, slotPos, EIVCompOrder.Order1)
			if addWeget then
				local path = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_Icon_27.Activity_Icon_27'"
				addWeget:ShowIconOnly(path)
				addWeget:RefreshComponent()
			end
		end
		self._wtCommonItemTemplate:EnableComponent(compId, item.start_time ~= item.end_time)
	end
end

function StoreItemBox:_OnItemWidgetClick()
    loginfo("[StoreItemBox] _OnItemWidgetClick")
    -- Module.Store.Config.evtStoreMandelPropItemClick:Invoke(self.item)
    if self.item then
        if self.item.itemMainType == EItemType.WeaponSkin then
            -- Facade.UIManager:AsyncShowUI(UIName2ID.CollectionWeaponSkinDetailPage, nil, nil, item)
            Module.Collection:ShowWeaponSkinDetailPage(self.item)
        else
            Module.ItemDetail:OpenItemDetailPanel(self.item, self, nil, nil, nil, nil, nil, nil, nil, nil)
        end
    end
end


return StoreItemBox
