----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSocial)
----- LOG FUNCTION AUTO GENERATE END -----------



-- 好友列表的玩家条,  好友列表的子控件
---@class InviteFriendInfoSlot : LuaUIBaseView
local EWidgetState = {
    AvailableInvite = 0,
    Inviting        = 1,
    Observer        = 2,
    Appoint         = 3,
    NotAvailable    = 4,
    ChannelInivte   = 5,
}

local FSlateChildSize = import "SlateChildSize"
local ETextOverflowPolicy = import "ETextOverflowPolicy"
local SocialLogic = require ("DFM.Business.Module.SocialModule.Logic.SocialLogic")
local SocialConfig = require ("DFM.Business.Module.SocialModule.SocialConfig")
local RankIconAbbr = require "DFM.Business.Module.RankingModule.UI.RankIconAbbr"
local InviteFriendInfoSlot = ui("InviteFriendInfoSlot")

function InviteFriendInfoSlot:Ctor()
    self._wtPlayerIcon = self:Wnd("WBP_CommonHeadIcon", CommonHeadIcon)
    self._wtTxtInvite = self:Wnd("TextBlock_623", UITextBlock)
    self._wtPlayerNameLabel = self:Wnd("PlayerNameLabel", UITextBlock)
    self._wtPlayerNameLabel:SetRenderOpacity(0)
    self._wtRankIcon = self:Wnd("wtRankIcon", RankIconAbbr)
    self._wtRankDivision = self:Wnd("wtRankDivision", UITextBlock)
    self._wtPlayerStateIcon = self:Wnd("wtPlayerStateIcon", UIImage)
    self._wtPlayerStateTxt = self:Wnd("wtPlayerStateTxt", UITextBlock)
    self._wtPlayerRelationIcon = self:Wnd("wtPlayerRelationIcon", UIImage)
    self._wtPlayerRelationIcon:SetRenderOpacity(0)
    self._wtPlayerInviteBtn = self:Wnd("PlayerInviteBtn", UIButton)
    self._wtPlayerInviteBtn:Event("OnClicked", self._OnPlayerInviteBtnClick, self)
    self._wtRoomInviteBtn = self:Wnd("TeamBtn_1", UIButton)
    self._wtRoomInviteBtn:Event("OnClicked", self._OnPlayerInviteBtnClick, self)
    self._wtInviting = self:Wnd("Inviteing", UIWidgetBase)
    self._wtWidgetStateWS = self:Wnd("WidgetStateWS", UIWidgetSwitcher)
    self._wtOfflineMask = self:Wnd("wtOfflineMask", UIImage)
    -- 渠道邀请
    self._wtWBPTeamShareButton = self:Wnd("WBP_TeamShareButton", UIWidgetBase)
    self._memberNum = 1
    Module.Social.Config.Events.evtOnPlayerIdCoolDownTimeRefresh:AddListener(self.RefreshInviteColdDownTime, self)
    self._playerSource = nil --TeamInviteSource 好友来源
    self.mCurWidgeState = nil   -- 记录当前设置的控件状态
    self:SetCppValue("bIsEnabled", true)
end

--刷新邀请倒计时
function InviteFriendInfoSlot:RefreshInviteColdDownTime(playerId)
    loginfo("InviteFriendInfoSlot:RefreshInviteColdDownTime")

    if playerId == self:GetPlayerId() then
        local coolDownTime = Module.Social.Field:GetCoolDownPlayerTime(playerId)
        if coolDownTime > 0 then
            self:ChangeWidgetState(EWidgetState.Inviting, coolDownTime)
        else
            self:ChangeWidgetState(EWidgetState.AvailableInvite)
        end
    end
end

function InviteFriendInfoSlot:OnOpen()
    self._bIsAvailable = true
end

function InviteFriendInfoSlot:OnClose()
    self._bIsAvailable = false
end

function InviteFriendInfoSlot:SetPlayerStateCode(playerInfo) --message FriendInfo
    if self._bIsAvailable == true then
        self._playerInfo = playerInfo
        local ePlayerStateCode = self._playerInfo.state
        local playerState = SocialLogic.GetOnlinePlayerStateFromStateCode(ePlayerStateCode)
        if playerState == GlobalPlayerStateEnums.EPlayerState_InTeam and playerInfo.member_num < 2 then
            playerState = GlobalPlayerStateEnums.EPlayerState_Online
        end
        -- 隐身等同于离线
        if Module.Friend:IsFriendOffLineOrInvisible(playerState, playerInfo.player_id) then
            playerState = GlobalPlayerStateEnums.EPlayerState_Offline
        end

        self._wtPlayerStateIcon:Collapsed()
        self._wtPlayerStateTxt:SetColorAndOpacity(Module.Social:GetPlayerStateColor(playerState))
        self._wtOfflineMask:SelfHitTestInvisible()
        --模式文本
        if playerState == GlobalPlayerStateEnums.EPlayerState_InMatch or playerState == GlobalPlayerStateEnums.EPlayerState_PickHeroStage or playerState == GlobalPlayerStateEnums.EPlayerState_WaitDS then
            if playerState == GlobalPlayerStateEnums.EPlayerState_InMatch then
                local modeName = ""
                if playerInfo.mode_info.game_mode == 2 then 
                    modeName =  Module.GameMode:GetCustomMapNameByMatchModeId(playerInfo.mode_info.match_mode_id,"{0}-{1}",{{ECustomMapNameTab.Map,false},{playerInfo.mode_info.game_rule==10 and ECustomMapNameTab.MatchSubMode or ECustomMapNameTab.GameRule,false}})
                else
                    modeName =  Module.GameMode:GetStandardMapNameByMatchModeId(playerInfo.mode_info.match_mode_id)
                end
                local duration = math.ceil((os.time() - playerInfo.fighting_time)/60.0)
                self._wtPlayerStateTxt:SetText(string.format(Module.Social.Config.Loc.PlayerState_InMatch, modeName,duration))
            elseif playerState == GlobalPlayerStateEnums.EPlayerState_PickHeroStage then
                self._wtPlayerStateTxt:SetText(Module.Social.Config.Loc.PlayerState_PickHero)
            else
                self._wtPlayerStateTxt:SetText(Module.Social.Config.Loc.PlayerState_WaitDS)
            end
            if Module.Friend:CheckIsFriend(self.playerId) then
                if Module.Social.Field:hasAppointPlayer(self.playerId) then
                    self._wtPlayerInviteBtn:AsyncSetImageIconPathAllState("PaperSprite'/Game/UI/UIAtlas/System/TeamSystem/BakedSprite/TeamSystem_PlayerState_Icon_0316.TeamSystem_PlayerState_Icon_0316'") 
                else
                    self._wtPlayerInviteBtn:AsyncSetImageIconPathAllState("PaperSprite'/Game/UI/UIAtlas/System/TeamSystem/BakedSprite/TeamSystem_PlayerState_Icon_0321.TeamSystem_PlayerState_Icon_0321'")        
                end
                self:ChangeWidgetState(EWidgetState.Appoint)
            else
                self:ChangeWidgetState(EWidgetState.NotAvailable)
            end
        else
            if playerState == GlobalPlayerStateEnums.EPlayerState_InTeam then
                local teamMax = 4
                if playerInfo.mode_info_array then
                    for _,mode in pairs(playerInfo.mode_info_array) do
                        teamMax = math.min(teamMax, mode.team_mode)
                    end
                end
                if playerInfo.mode_info and playerInfo.mode_info.team_mode then
                    teamMax = math.min(teamMax, playerInfo.mode_info.team_mode)
                end
                local teamNum = string.format("(%d/%d)", playerInfo.member_num, teamMax)
                self._memberNum = playerInfo.member_num
                self._wtPlayerStateTxt:SetText(Module.Social:GetPlayerStateTxt(GlobalPlayerStateEnums.EPlayerState_InTeam) .. teamNum)
                if Server.AccountServer:IsInTeam() and Server.TeamServer:GetTeamMatesNum() >= 2 then
                    self._wtPlayerInviteBtn:AsyncSetImageIconPathAllState("PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_PlayerState_Icon_0303.CommonHall_PlayerState_Icon_0303'") --组队
                else
                    self._wtPlayerInviteBtn:AsyncSetImageIconPathAllState("PaperSprite'/Game/UI/UIAtlas/System/TeamSystem/BakedSprite/TeamSystem_PlayerState_Icon_0324.TeamSystem_PlayerState_Icon_0324'") --入队
                end
                Module.Social.Field:RemoveAppointPlayer(self.playerId)
            else
                self._wtPlayerStateTxt:SetText(Module.Social:GetPlayerStateTxt(playerState))
                self._wtPlayerInviteBtn:AsyncSetImageIconPathAllState("PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_PlayerState_Icon_0303.CommonHall_PlayerState_Icon_0303'")
                Module.Social.Field:RemoveAppointPlayer(self.playerId)
            end
            -- 设置冷却状态
            local coolDownTime = Module.Social.Field:GetCoolDownPlayerTime(self.playerId)
            if coolDownTime > 0 then
                self:ChangeWidgetState(EWidgetState.Inviting, coolDownTime)
                Module.Social.Field:AddCoolDownPlayerList(self.playerId, coolDownTime)
            else
                self:ChangeWidgetState(EWidgetState.AvailableInvite)
            end
        end
        if Module.Friend:IsFriendOffLineOrInvisible(playerState, self._playerInfo.player_id) then
            self._wtWidgetStateWS:Collapsed()
            if Server.FriendServer:CheckIsOpenFriend(self._playerInfo.player_id) then
                self:SetSupportChannelIniteStyle()
            end
        else
            self._wtPlayerStateIcon:SetColorAndOpacity(Module.Social:GetPlayerStateIconColor(playerState))
            local plat = self._playerInfo.plat or self._playerInfo.PlatID
            --- BEGIN MODIFICATION @ VIRTUOS
            self._wtPlayerStateIcon:AsyncSetImagePath(Module.Friend:GetPlatformIconPath(plat, false, false), false)
            --- END MODIFICATION
            self._wtPlayerStateIcon:SelfHitTestInvisible()
            self._wtWidgetStateWS:Visible()
            self._wtOfflineMask:Collapsed()
        end
    end
end

function InviteFriendInfoSlot:SetPlayerRelation(hideSource)
    if not hideSource then
        self._wtPlayerRelationIcon:AsyncSetImagePath(SocialConfig.TeamSourceToIcon[self._playerSource])
        self._wtPlayerRelationIcon:SelfHitTestInvisible()
    else
        self._wtPlayerRelationIcon:Collapsed()
    end
end

function InviteFriendInfoSlot:SetInfo(playerInfo) -- message FriendInfo
    if not playerInfo then
        self:Collapsed()
        return
    else
        self:Visible()
    end
    self._playerInfo = playerInfo
    if not SocialLogic.IsInMp() then
        self._playerInfo.level = self._playerInfo.season_level
    end
    self._playerSource = self._playerInfo.source
    self._roomId = self._playerInfo.room_id
    self:SetPlayerName(self._playerInfo)
    self:SetPlayerId(self._playerInfo.player_id)
    self:SetPlayerLv(self._playerInfo.level)
    self._wtRankIcon:SetRankIconNone()
    self._wtRankIcon:SelfHitTestInvisible()
    self._wtRankDivision:SetText(SocialConfig.Loc.NoRankDivision)
    self._wtRankDivision:SelfHitTestInvisible()
    if SocialLogic.IsInMp() == true then
        if self._playerInfo.mp_attended == true then
            self._wtRankIcon:SetTournamentIconByScore(self._playerInfo.mp_rank_score)
            local rankInfo = Module.Tournament:GetRankDataByScore(self._playerInfo.mp_rank_score)
            self._wtRankDivision:SetText(rankInfo and rankInfo.Name or SocialConfig.Loc.NoRankDivision)
        else
            self._wtRankIcon:SetRankIconNone()
        end
    else
        if self._playerInfo.sol_attended == true then
            self._wtRankIcon:SetRankingIconByScore(self._playerInfo.sol_rank_score)
            local rankInfo = Module.Ranking:GetMinorDataByScore(self._playerInfo.sol_rank_score)
            self._wtRankDivision:SetText(rankInfo and rankInfo.RankName or SocialConfig.Loc.NoRankDivision)
        else
            self._wtRankIcon:SetRankIconNone()
        end
    end
    self:SetPlayerStateCode(self._playerInfo)
    self:SetPlayerRelation(self._playerInfo.hideSource)
    self:SetTeamID(self._playerInfo.team_id)
    local btnTbl =  {
        HeadButtonType.AddFriend,
        HeadButtonType.PlayerInformat,
        HeadButtonType.Report,
        HeadButtonType.AddBlack,
    }
    self._wtPlayerIcon:InitPortrait(self._playerInfo, HeadIconType.HeadList, btnTbl, FriendApplySource.TeamApply)
end

function InviteFriendInfoSlot:_OnPlayerInviteBtnClick()
    if Module.Friend:CheckIsBlack(self.playerId) then
        Module.CommonTips:ShowSimpleTip(Module.Social.Config.Loc.BlackListPlayer)
        return
    end

    local function fUpdateState(newStateList)
        local bIsInMatch = false
        local targetPlayerInfo = nil
        for _,playerInfo in pairs(newStateList) do
            local id = playerInfo.player_id
            if id == self.playerId then
                local inTeam = (playerInfo.state & GlobalPlayerStateEnums.EPlayerState_InTeam) ~= 0
                bIsInMatch = (playerInfo.state & GlobalPlayerStateEnums.EPlayerState_InMatch) ~= 0
                self._teamID = inTeam and playerInfo.team_id or 0
                self._memberNum = inTeam and playerInfo.member_num or 0
                self:SetPlayerStateCode(playerInfo)
                targetPlayerInfo = playerInfo
                if bIsInMatch == true then
                    break
                end
            end
        end

        if bIsInMatch == true then
        else
            self:ChangeWidgetState(EWidgetState.Inviting)
            if Server.AccountServer:IsInRoom() then
                self:_PrcessRoomInvite()
            else
                self:_PrcessTeamInvite()
            end
        end
    end
    local updateList = {self.playerId}
    Server.SocialServer:ReqPlayerState(updateList,fUpdateState)
end

--处理来自房间的邀请
function InviteFriendInfoSlot:_PrcessRoomInvite()
    local playerId = self:GetPlayerId()
    self._wtTxtInvite:SetText(Module.Social.Config.Loc.Inviting)

    local function onInviteRes(res)
        if not res or res.result ~= 0 then
            Module.Social.Field:ClearInviteCDByPlayerID(playerId)
            self:ChangeWidgetState(EWidgetState.AvailableInvite)
        end
        if not res or res.result == 0 then
            Module.CommonTips:ShowSimpleTip(Module.Social.Config.Loc.SucceedRoomInvite)
        end
    end
    Module.Social:RoomInvite(playerId, self._playerSource, true, onInviteRes)
end

--处理来自组队邀请/申请
function InviteFriendInfoSlot:_PrcessTeamInvite()
    local playerId = self:GetPlayerId()
    local teamID = self:GetTeamID()
    --此处组队是指“队伍人数>=2”
    -- 发起玩家为组队，目标玩家为单人/组队时 —— 邀请组队
    -- 发起玩家为单人，目标玩家为单人 —— 邀请组队
    -- 发起玩家为单人，目标玩家为组队 —— 申请入队
    if self._memberNum > 1 and Server.TeamServer:GetTeamMatesNum() < 2 then
        self._wtTxtInvite:SetText(Module.Social.Config.Loc.Applying)
    else
        self._wtTxtInvite:SetText(Module.Social.Config.Loc.Inviting)
    end

    local fCallbackIns = CreateCallBack(function(self, res)
        Module.Social.Field:ClearInviteCDByPlayerID(playerId)
        Module.Social.Field:AddCoolDownPlayerList(playerId)
    end,self)
    SocialLogic.PrcessTeamInvite(teamID, playerId,self.playerName,self._memberNum,self._playerSource, self._roomId, fCallbackIns, self:GetTargetGameMode())
end

function InviteFriendInfoSlot:SetPlayerLv(playerLevel)
    loginfo("InviteFriendInfoSlot:SetPlayerLv", playerLevel)
    self.playerLv = playerLevel
end

function InviteFriendInfoSlot:GetPlayerLv()
    return self.playerLv
end

function InviteFriendInfoSlot:SetPlayerId(playerId)
    self.playerId = playerId
    if self._wtWBPTeamShareButton then
        self._wtWBPTeamShareButton:SetPlayerInfo(playerId, self.playerName)
    end
end

function InviteFriendInfoSlot:GetPlayerId()
    return self.playerId
end

function InviteFriendInfoSlot:SetTeamID(teamID)
    self._teamID = teamID
end

function InviteFriendInfoSlot:GetTeamID()
    return self._teamID
end

function InviteFriendInfoSlot:GetTargetGameMode()
    return self._playerInfo and (self._playerInfo.mode_info and self._playerInfo.mode_info.game_mode or nil) or nil
end

function InviteFriendInfoSlot:SetPlayerName(playerInfo)
    if playerInfo.nick_name then
        local remarkName = Server.FriendServer:GetFriendRemarkById(playerInfo.player_id)
        if remarkName ~= "" then
            self.playerName = string.format(Module.Friend.Config.QQFriend, remarkName, playerInfo.nick_name)
        else
            self.playerName = playerInfo.nick_name
        end
        self._wtPlayerNameLabel:SetText(self.playerName)
        Timer.DelayCall(0, self.SetPlayerNameLabelLayout, self)
    end
end

function InviteFriendInfoSlot:SetPlayerNameLabelLayout()
    local geometry = self._wtPlayerNameLabel:GetCachedGeometry()
    if geometry then
        local size = geometry:GetLocalSize()
        if size and size.X ~= 0 and size.X > 500 then
            local horizontalSlot = UWidgetLayoutLibrary.SlotAsHorizontalBoxSlot(self._wtPlayerNameLabel)
            horizontalSlot:SetSize(FSlateChildSize(ESlateSizeRule.Fill))
            self._wtPlayerNameLabel:SetTextOverflowAutoEllipse(ETextOverflowPolicy.Ellipsis)
        end
    end
    self._wtPlayerNameLabel:SetRenderOpacity(1)
    self._wtPlayerRelationIcon:SetRenderOpacity(1)
    loginfo('InviteFriendInfoSlot:SetPlayerNameLabelLayout self._wtPlayerNameLabel:SetRenderOpacity(1)')
end

function InviteFriendInfoSlot:GetPlayerName()
    return self.playerName
end

function InviteFriendInfoSlot:ChangeWidgetState(state, coldTime)
    if hasdestroy(self) 
        or hasdestroy(self._wtPlayerInviteBtn) 
        or hasdestroy(self._wtRoomInviteBtn) 
        or hasdestroy(self._wtInviting) 
        or hasdestroy(self._wtWBPTeamShareButton) then
        return
    end
    self.mCurWidgeState = state
    if not hasdestroy(self) then
        if not hasdestroy(self._wtPlayerInviteBtn) then
            self._wtPlayerInviteBtn:Collapsed()
            self._wtPlayerInviteBtn:SetIsEnabled(false)
        end
        if not hasdestroy(self._wtRoomInviteBtn) then
            self._wtRoomInviteBtn:Collapsed()
            self._wtRoomInviteBtn:SetIsEnabled(false)
        end
        if not hasdestroy(self._wtInviting) then
            self._wtInviting:Collapsed()
        end
        if not hasdestroy(self._wtWBPTeamShareButton) then
            self._wtWBPTeamShareButton:Collapsed()
        end
        if Server.TeamServer:FindMember(self.playerId) then
            return
        else
            local playerState = SocialLogic.GetOnlinePlayerStateFromStateCode(self._playerInfo.state)
            if playerState == GlobalPlayerStateEnums.EPlayerState_InTeam and self._playerInfo.member_num < 2 then
                playerState = GlobalPlayerStateEnums.EPlayerState_Online
            end
            -- 隐身等同于离线
            if Module.Friend:IsFriendOffLineOrInvisible(playerState, self._playerInfo.player_id) then
                playerState = GlobalPlayerStateEnums.EPlayerState_Offline
            end
            local plat = self._playerInfo.plat or self._playerInfo.PlatID
            if plat and (not Module.Friend:IsFriendOffLineOrInvisible(playerState, self._playerInfo.player_id))
                and not (plat == PlatIDType.Plat_IOS) and not (plat == PlatIDType.Plat_Android) and not IsInEditor() and (plat ~= PlatIDType.Plat_Harmony) then
                return
            end
        end
        logerror('InviteFriendInfoSlot ChangeWidgetState playerId =', self.playerId, ' state=',state)
        if state == EWidgetState.AvailableInvite then
            self._wtPlayerInviteBtn:SetIsEnabled(true)
            if Module.Room:GetIsInRoom() then
                self._wtPlayerInviteBtn:Collapsed()
                self._wtRoomInviteBtn:Visible()
                self._wtRoomInviteBtn:SetIsEnabled(true)
            else
                self._wtPlayerInviteBtn:Visible()
                self._wtRoomInviteBtn:Collapsed()
            end
        elseif state == EWidgetState.Inviting then
            --self._wtInviting:Visible()
            if Module.Room:GetIsInRoom() then
                self._wtRoomInviteBtn:Visible()
                self._wtPlayerInviteBtn:Collapsed()
            else
                self._wtPlayerInviteBtn:Visible()
                self._wtRoomInviteBtn:Collapsed()
            end
        elseif state == EWidgetState.Appoint then
            if IsHD() then
            else
                self._wtPlayerInviteBtn:Visible()
                if Module.Social.Field:hasAppointPlayer(self.playerId) == false then
                    self._wtPlayerInviteBtn:SetIsEnabled(true)
                end
                self._wtRoomInviteBtn:Collapsed()
            end
        elseif state == EWidgetState.ChannelInivte then
            self._wtWBPTeamShareButton:SetIsEnabled(Module.Invite:CanSupportInviteByPlayerId(self.playerId))
            if Server.SDKInfoServer:GetChannel() == EChannelType.kChannelWechat then
                Module.Share:FuncPointUnLock(SwitchSystemID.SubShareWXDirectionTeam, self._wtWBPTeamShareButton)
            elseif Server.SDKInfoServer:GetChannel() == EChannelType.kChannelQQ then
                Module.Share:FuncPointUnLock(SwitchSystemID.SubShareQQDirectionTeam, self._wtWBPTeamShareButton)
            end
        elseif state == EWidgetState.NotAvailable then
        end
    end
end

--------------------------------------- 渠道邀请相关 ------------------------------------

function InviteFriendInfoSlot:SetSupportChannelIniteStyle()
    -- 这里补个邀请相关逻辑，如果判断当前可以单独邀请该玩家则展示对应样式
    if not Module.Invite:CanSupportInviteByPlayerId(self.playerId) then
        self:ChangeWidgetState(EWidgetState.NotAvailable)
        self._wtWBPTeamShareButton:UpdateIconStyle()
        self._wtWBPTeamShareButton:Visible()
        return
    end

    self._wtWBPTeamShareButton:UpdateIconStyle()
    self._wtWBPTeamShareButton:Visible()
    self:ChangeWidgetState(EWidgetState.ChannelInivte)
end
--end
-----------------------------------------------------------------------

return InviteFriendInfoSlot
