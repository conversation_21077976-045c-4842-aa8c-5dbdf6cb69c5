----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMItemDetail)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class ItemDetail_CurrencyColumnTips : LuaUIBaseView
local ItemDetail_CurrencyColumnTips = ui("ItemDetail_CurrencyColumnTips")
local ECheckBoxState = import "ECheckBoxState"
local StoreLogic = require "DFM.Business.Module.StoreModule.Logic.StoreLogic"

local PC_SCALE = 1
function ItemDetail_CurrencyColumnTips:Ctor()
    self._wtCurrencyName = self:Wnd("_wtTextContent",UITextBlock)
    self._wtCurrencyDesc = self:Wnd("_wtTextContent_1",UITextBlock)
    self._wtCurrencyBtn = self:Wnd("DFButton_161",UIButton)

    self._wtBindCurrencyName = self:Wnd("_wtTextContent_3",UITextBlock)
    self._wtBindCurrencyDesc = self:Wnd("_wtTextContent_2",UITextBlock)
    self._wtBindCurrencyPanel = self:Wnd("DFCanvasPanel_4", UIWidgetBase)
    
    self._wtCurrencyExtraDesc = self:Wnd("_wtTextContent_4",UITextBlock)
    self._wtCurrencyExtraPanel = self:Wnd("DFCanvasPanel_0", UIWidgetBase)

    --物流凭证标签
    self._wtLimitedTimePanel = self:Wnd("DFCanvasPanel_2", UIWidgetBase)
    self._wtLimitedTimeTxt = self:Wnd("DFTextBlock_95", UITextBlock)

    self._wtCurrencyBtn:Event("OnClicked", self._OnCurrencyBtnClick, self)

    self._wItembindTips = self:Wnd("SizeBox_0", UIWidgetBase)
    self._bKeepParentClickState = false
    self._parentWidget = nil
    self._onCloseCallback = nil
    self.bNotAutoClose = true

    self._layerLevel = nil
end

function ItemDetail_CurrencyColumnTips:OnOpen()
    if DFHD_LUA == 1 then
		self._wItembindTips:SetRenderScale(FVector2D(PC_SCALE, PC_SCALE))
	end
end

function ItemDetail_CurrencyColumnTips:_OnCurrencyBtnClick()
    -- if self._currencyId == ECurrencyClientId.Diamond or self._currencyId == ECurrencyClientId.UnbindDiamond then
    --     Module.Store:ShowStoreRechargeMainPanle()
    -- end
end

function ItemDetail_CurrencyColumnTips:OnShowBegin()
    self:_AddEventListener()
end

function ItemDetail_CurrencyColumnTips:OnHideBegin()
    Module.ItemDetail.Config.evtItemDetailTipsClose:Invoke(false)
    if self._parentWidget and self._bKeepParentClickState then
        if self._parentWidget.SetSelectState then
            self._parentWidget:SetSelectState(false)
        end
        if self._parentWidget:Wnd("DFCheckBox_Icon", UICheckBox) then
            self._parentWidget:Wnd("DFCheckBox_Icon", UICheckBox):SetCheckedState(ECheckBoxState.Unchecked)
        end
    end
    if self._parentWidget then
        Module.ItemDetail:DeleteHandle(self._parentWidget)
    end
    self:_RemoveEventListener()
    if self._onCloseCallback then
        self._onCloseCallback()
    end
end

function ItemDetail_CurrencyColumnTips:OnHide()
    
end

function ItemDetail_CurrencyColumnTips:_AddEventListener()
    local gameInst = GetGameInstance()
    self._buttonUpHandle = UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonUpEvent:Add(self.OnMouseButtonUp, self)
    self._buttonDownHandle = UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonDownEvent:Add(self.OnMouseButtonDown, self)
end

function ItemDetail_CurrencyColumnTips:_RemoveEventListener()
    local gameInst = GetGameInstance()
    if self._buttonUpHandle then
		UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonUpEvent:Remove(self._buttonUpHandle)
	end
    if self._buttonDownHandle then
		UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonDownEvent:Remove(self._buttonDownHandle)
    end
end

function ItemDetail_CurrencyColumnTips:OnMouseButtonUp(MouseEvent)
    if not self:IsVisible() then
        return
    end
    if Module.ItemDetail.Field:GetIsLastDrag() then
		return
	end

    local sceenPos = MouseEvent:GetScreenSpacePosition()
    --local geometry = self._wItembindTips:GetCachedGeometry()
    local isUnder = self:IsHit(sceenPos)
    if not isUnder and Module.ItemDetail:CanItemDetailPanelClose(self._layerLevel, sceenPos, true) then
        --Facade.UIManager:CloseUI(self)
        if not Module.ItemDetail.Field:GetMainPanelHandle() then
            Facade.UIManager:CloseUI(self)
        else
            Module.ItemDetail:CloseItemDetailPanel(self._layerLevel, true)
        end
    end
end

function ItemDetail_CurrencyColumnTips:OnMouseButtonDown(MouseEvent)
    if not self:IsVisible() then
        return
    end
    Module.ItemDetail:SetIsLastClosed(false)
end

--- tipsData：ETipsContentType 和 每一个类别具体填充内容对应的信息
function ItemDetail_CurrencyColumnTips:OnInitExtraData(currencyId, parentWidget, alignWidget, bKeepParentClickState, deltaSpacing, bAlignDown)
    self._currencyId = currencyId
    if bKeepParentClickState then
        self._bKeepParentClickState = bKeepParentClickState
        self._parentWidget = parentWidget
        if parentWidget and self._parentWidget.SetSelectState then
            self._parentWidget:SetSelectState(true)
        end
    end

    -----Android凭证限时标签显示
    self:_IsShowEndTime(currencyId)
    -----

    local bindId = Module.Currency:GetBindId(currencyId)
    local currencyName = Module.Currency:GetName(currencyId)
    local currencyDesc = Module.Currency:GetTransactionDesc(currencyId)
    local currencyImgId = Module.Currency:GetRichTxtImgId(currencyId)
    local currencyNum = Module.Currency:GetRealNum(currencyId)

    local name = string.format(Module.ItemDetail.Config.Loc.CurrencyName, currencyImgId, currencyName)
    local tempCurrencyDesc = MathUtil.GetNumberFormatStr((currencyNum)) .. '\n' .. currencyDesc

    self._wtCurrencyName:SetText(name)
    self._wtCurrencyDesc:SetText(tempCurrencyDesc)

    if Module.Currency:GetBindType(currencyId) == ECurrencyBindType.BindAndUnbind then
		local bindId = Module.Currency:GetBindId(currencyId)
        local bindCurrencyName = Module.Currency:GetName(bindId)
        local bindCurrencyDesc = Module.Currency:GetTransactionDesc(bindId)
        local bindCurrencyImgId = Module.Currency:GetRichTxtImgId(bindId)
        local bindCurrencyNum = Module.Currency:GetRealNum(bindId)
        local unBindCurrencyNum =  Module.Currency:GetRealNum(Module.Currency:GetUnbindId(currencyId))

        local bindName = string.format(Module.ItemDetail.Config.Loc.CurrencyName, bindCurrencyImgId, bindCurrencyName)
        bindCurrencyDesc = MathUtil.GetNumberFormatStr(bindCurrencyNum) .. '\n' .. bindCurrencyDesc
        currencyDesc = MathUtil.GetNumberFormatStr(unBindCurrencyNum) .. '\n' .. currencyDesc

        self._wtCurrencyDesc:SetText(currencyDesc)

        self._wtBindCurrencyName:SetText(bindName)
        self._wtBindCurrencyDesc:SetText(bindCurrencyDesc)
        self._wtBindCurrencyPanel:Visible()

        self._wtCurrencyExtraDesc:SetText(Module.ItemDetail.Config.Loc.CurrencyDiamondDesc)
        self._wtCurrencyExtraPanel:Visible()
	else
        self._wtBindCurrencyPanel:Collapsed()
        self._wtCurrencyExtraPanel:Collapsed()
	end

	self:_UpdateTipsPos(alignWidget, deltaSpacing, bAlignDown)
end

--手游代理货币时间标签显示
function ItemDetail_CurrencyColumnTips:_IsShowEndTime(currencyId)
    if currencyId == ECurrencyClientId.LogisticsVoucher then
        --赛季物流
        local endTime = Server.ActivityServer:GetVoucherEndTime()
        if self._wtLimitedTimePanel and self._wtLimitedTimeTxt and endTime then
            local itemDesc = string.format(Module.ItemDetail.Config.Loc.NumDay, endTime)
            self._wtLimitedTimeTxt:SetText(itemDesc or "")
            self._wtLimitedTimePanel:SetVisibility(ESlateVisibility.HitTestSelfOnly)
        end
    elseif currencyId == ECurrencyClientId.RankCoin then--排位币
        if self._wtLimitedTimePanel and self._wtLimitedTimeTxt then
            local remainDay,remainHour,remainMin=Module.Ranking:GetSeasonRemainTime()
            local timeDesc = "?"
            if remainDay and remainDay>0 then
                timeDesc=string.format(Module.CommonBar.Config.Loc.NumDay, remainDay)
            elseif remainHour and remainHour>0 then
                timeDesc=string.format(Module.CommonBar.Config.Loc.NumHour, remainHour)
            elseif remainMin then
                timeDesc=string.format(Module.CommonBar.Config.Loc.NumMin, remainMin)
            end
            self._wtLimitedTimeTxt:SetText(timeDesc or "")
            self._wtLimitedTimePanel:SetVisibility(ESlateVisibility.HitTestSelfOnly)
        end
    elseif currencyId == ECurrencyClientId.ConsumerCoupon then
        local consumerCouponInfoStr = StoreLogic.GetConsumerCouponInfoStr()
        if consumerCouponInfoStr then
            self._wtLimitedTimeTxt:SetText(consumerCouponInfoStr)
            self._wtLimitedTimePanel:SetVisibility(ESlateVisibility.HitTestSelfOnly)
        end
    else
        if self._wtLimitedTimePanel then
            self._wtLimitedTimePanel:Collapsed()
        end
    end
end


function ItemDetail_CurrencyColumnTips:_UpdateTipsPos(alignWidget, deltaSpacing, bAlignDown)
    if alignWidget == nil then
		return
	end

    local tipPos, detailViewAlignment
    local adjustScale = 1
    if IsHD() then
        adjustScale = PC_SCALE
    end
    if not bAlignDown then
        tipPos, detailViewAlignment = Module.ItemDetail:GetNewTipsPos(self, self._wItembindTips, alignWidget, nil, nil, nil, deltaSpacing, nil, adjustScale)
    else
        tipPos, detailViewAlignment = Module.ItemDetail:GetNewTipsPosType2(self, self._wItembindTips, alignWidget, nil, nil, deltaSpacing, adjustScale)
    end
	self._wItembindTips.Slot:SetAlignment(detailViewAlignment)
    self._wItembindTips:SetRenderTransformPivot(detailViewAlignment)
    local alignAttachAlignment
    --self._wItembindTips.Slot:SetPosition(tipPos)
    local uISetting = self:GetUISettings()
    if detailViewAlignment.X == 0 then
        alignAttachAlignment = FVector2D(1, 0)
        -- if DFHD_LUA == 1 then
        --     uISetting.Anim = {
        --         FlowInAni = "WBP_CommonDescriptionMiniTip_in_pc",
        --         FlowOutAni = "WBP_CommonDescriptionMiniTip_out_pc"
        --     }
        -- else
        --     uISetting.Anim = {
        --         FlowInAni = "WBP_CommonDescriptionMiniTip_in",
        --         FlowOutAni = "WBP_CommonDescriptionMiniTip_out"
        --     }
        -- end
    else
        alignAttachAlignment = FVector2D(0, 0)
        -- if DFHD_LUA == 1 then
        --     uISetting.Anim = {
        --         FlowInAni = "WBP_CommonDescriptionMiniTip_in_right_pc",
        --         FlowOutAni = "WBP_CommonDescriptionMiniTip_out_right_pc"
        --     }
        -- else
        --     uISetting.Anim = {
        --         FlowInAni = "WBP_CommonDescriptionMiniTip_in_right",
        --         FlowOutAni = "WBP_CommonDescriptionMiniTip_out_right"
        --     }
        -- end
    end
    self._wItembindTips.Slot:SetPosition(tipPos)
    --UIUtil.AttachWidgetTo(self, self._wItembindTips, alignWidget, deltaSpacing, detailViewAlignment, alignAttachAlignment)
	self._wItembindTips:SetRenderOpacity(1)
end

function ItemDetail_CurrencyColumnTips:UpdateTipsPos(alignWidget, adjustAttachAlignment, alignAttachAlignment, deltaSpacing)
    if alignWidget == nil then
		return
	end

    self._wItembindTips.Slot:SetAlignment(LuaGlobalConst.TOP_RIGHT_VECTOR)
    UIUtil.AttachWidgetTo(self, self._wItembindTips, alignWidget, deltaSpacing, adjustAttachAlignment, alignAttachAlignment)
end

function ItemDetail_CurrencyColumnTips:_OnCloseBtnClicked()
	Facade.UIManager:CloseUI(self)
    return {}
end

function ItemDetail_CurrencyColumnTips:IsHit(absolutePoint)
	if self._isClosing then
		return false
	end
    local geometry = self._wItembindTips:GetCachedGeometry()
    local isUnder = USlateBlueprintLibrary.IsUnderLocation(geometry, absolutePoint)
    if self._parentWidget and self._bKeepParentClickState and self._parentWidget.GetCachedGeometry then
        geometry = self._parentWidget:GetCachedGeometry()
        if geometry then
            isUnder = isUnder or USlateBlueprintLibrary.IsUnderLocation(geometry, absolutePoint)
        end
    end
	return isUnder
end

function ItemDetail_CurrencyColumnTips:SetTipsLayerLevel(layerLevel)
    self._layerLevel = layerLevel
end

function ItemDetail_CurrencyColumnTips:SetCloseCallback(cb)
    self._onCloseCallback = cb
end

function ItemDetail_CurrencyColumnTips:OnClose()
    --Facade.UIManager:ClearSubUIByParent(self, self._wtDescBox)
end

return ItemDetail_CurrencyColumnTips