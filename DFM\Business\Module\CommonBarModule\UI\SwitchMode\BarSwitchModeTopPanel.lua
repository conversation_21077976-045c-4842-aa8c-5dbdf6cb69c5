----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCommonBar)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class BarSwitchModeTopPanel : LuaUIBaseView
local BarSwitchModeTopPanel = ui("BarSwitchModeTopPanel")

-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

local function log(...)
    loginfo("[BarSwitchMode] ", ...)
end
local EGPInputModeType = import "EGPInputModeType"
local UDFMGameHudDelegates = import("DFMGameHudDelegates")
local USlateBlueprintLibrary = import "SlateBlueprintLibrary"
local BarSwitchModeItem = require(string.format(ResourcePath.BUSINESS_MODULE, "CommonBarModule.UI.SwitchMode.BarSwitchModeItem"))
local BarSwitchModeItemBtn = require(string.format(ResourcePath.BUSINESS_MODULE, "CommonBarModule.UI.SwitchMode.BarSwitchModeItemBtn"))

local UGPInputHelper = import("GPInputHelper")
local ESwitchMode = Module.GameMode.Config.ESwitchMode
local SwitchModeBtnId = Module.GameMode.Config.SwitchModeBtnId
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"

local TopBarHD = require(string.format(ResourcePath.BUSINESS_MODULE, "CommonBarModule.UI.TopBarHD.TopBarHD2"))
local ETabGroupMode = TopBarHD.ETabGroupMode
local EGameHUDState = import "GameHUDSate"
local CustomerServicesEntranceType = import "ECustomerServicesEntranceType"
local UGameplayBlueprintHelper = import "GameplayBlueprintHelper"
--- BEGIN MODIFICATION @ VIRTUOS
-- PlayGo相关
local UDFMInitialChunkManager = import "DFMInitialChunkManager"
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
--- END MODIFICATION

function BarSwitchModeTopPanel:Ctor()
    self:InitModeBtnInOrder()

    self._wtCanvasDraw = self:Wnd("DFCanvasPanel_Interactive", UIWidgetBase)
    self._wtVbMode = self:Wnd("DFVerticalBox_Mode", UIWidgetBase)
    self._wtVbBottom = self:Wnd("DFVerticalBox_Bottom", UIWidgetBase)
    -- 使得此UI的进入动画播完之后才能进行隐藏
	self.bInEnd = false
    self._modeBtnId = SwitchModeBtnId.Top_SOL -- 按钮id

    self._wtImgLineEnter = self:Wnd("DFImage_Enter", UIImage)
    self._wtBtnEnter = self:Wnd("WBP_TopBarHD_SwitchMode_Enter", BarSwitchModeItemBtn)
    self._wtBtnEnter:BindClickCallback(CreateCallBack(self.OnEnterBtnClicked, self))
    self._wtImgLineServices = self:Wnd("DFImage_372", UIImage)
    self._wtBtnService = self:Wnd("WBP_TopBarHD_SwitchMode_Service", BarSwitchModeItemBtn)
    self._wtBtnService:BindClickCallback(CreateCallBack(self.OnServiceBtnClicked, self))
    self._wtBtnSetting = self:Wnd("WBP_TopBarHD_SwitchMode_Setting", BarSwitchModeItemBtn)
    self._wtBtnSetting:BindClickCallback(CreateCallBack(self.OnSettingBtnClicked, self))
    self._wtBtnExit = self:Wnd("WBP_TopBarHD_SwitchMode_Exit", BarSwitchModeItemBtn)
    self._wtBtnExit:BindClickCallback(CreateCallBack(self.OnExitBtnClicked, self))
    self._wtBtnGM = self:Wnd("WBP_TopBarHD_SwitchMode_GM", BarSwitchModeItemBtn)
    self._wtBtnGM:BindClickCallback(CreateCallBack(self.OnGMBtnClicked, self))
    self._wtIdcDesc = self:Wnd("DFRichTextBlock", UITextBlock)
    --需要支持导航
    self._wtIdcSelectorBtn = self:Wnd("SwitchIDCButton", UIButton)
    self._wtIdcSelectorBtn:Event("OnClicked", self.OpenIDCSelectorPanel, self)
    if VersionUtil.IsShipping() then
        self._wtBtnGM:Collapsed()
        self._wtImgLineEnter:Collapsed()
    end

    if Module.CustomerServices:CheckEntranceEnable(CustomerServicesEntranceType.ESCPage) then
        self._wtImgLineServices:Visible()
        self._wtBtnService:Visible()
    else
        self._wtImgLineServices:Collapsed()
        self._wtBtnService:Collapsed()
    end

    -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
    if IsConsole() then
        self._wtBtnExit:Collapsed()
    end
    -- END MODIFICATION

    self._wtBtnClose = self:Wnd("Button_Close", UIButton)
    self._wtBtnClose:Event("OnClicked",self.OnCloseBtnClick,self)

    ---@type MusicPlayerWidget 
    self._wtMusicPlayerWidget = self:Wnd("WBP_MusicPlayer_Main", UIWidgetBase)
    local curGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    if curGameFlow == EGameFlowStageType.LobbyBHD then
        self._wtMusicPlayerWidget:Collapsed()
    else
        self._wtMusicPlayerWidget:Visible()
    end
end

function BarSwitchModeTopPanel:InitModeBtnInOrder()
    local switchModePriorityList = Module.GameMode:GetSwitchModePriorityList(true)
    if DFHD_LUA == 1 then
        for i, priorityInfo in ipairs(switchModePriorityList) do
            if i <= 3 then
                local eSwitchMode = priorityInfo.eSwitchMode
                local switchModeItemRawName
                local switchModeItemWndName
                if i == 1 then
                    switchModeItemRawName = "WBP_Hall_SwitchMode_Item"
                else
                    switchModeItemRawName = string.format("WBP_Hall_SwitchMode_Item_%d", i - 1)
                end
                if self[switchModeItemRawName] and Module.GameMode.Config.MapESwitchModeToStr then
                    switchModeItemWndName = "_wtSwitchModeItem"..Module.GameMode.Config.MapESwitchModeToStr[eSwitchMode]
                end
                self[switchModeItemWndName] = self:Wnd(switchModeItemRawName, BarSwitchModeItem)
                self[switchModeItemWndName]:InitMode(eSwitchMode)
                loginfo("[BarSwitchModeTopPanel] switchModeItemWndName:", switchModeItemWndName)
            end
        end
    else
        self._wtSwitchModeItemSOL = self:Wnd("WBP_Hall_SwitchMode_Item", BarSwitchModeItem)
        self._wtSwitchModeItemMP = self:Wnd("WBP_Hall_SwitchMode_Item_1", BarSwitchModeItem)
        self._wtSwitchModeItemBHD = self:Wnd("WBP_Hall_SwitchMode_Item_2", BarSwitchModeItem)
        self._wtSwitchModeItemSOL:InitMode(ESwitchMode.SOL)
        self._wtSwitchModeItemMP:InitMode(ESwitchMode.MP)
        self._wtSwitchModeItemBHD:InitMode(ESwitchMode.BHD)
    end
    
    -- BEGIN MODIFICATION @ VIRTUOS 
    -- 隐藏黑鹰坠落，在console.
    -- Temp code, It must be removed when merge from dev at she1.
    if VersionUtil.IsShipping() and IsConsole() then
        self._wtSwitchModeItemBHD = self:Wnd("WBP_Hall_SwitchMode_Item_2", BarSwitchModeItem)
        self._wtSwitchModeItemBHD:SetVisibility(ESlateVisibility.Collapsed)
    end
    -- END MODIFICATION
    --- 目前不受表格管理
    self._wtSwitchModeItemDebugSOL = self:Wnd("WBP_Hall_SwitchMode_Item_3", BarSwitchModeItem)
    self._wtSwitchModeItemDebugSOL:InitMode(ESwitchMode.DEBUGSOL)
end

function BarSwitchModeTopPanel:OnCloseBtnClick()
    Module.CommonBar:CloseBarSwitchMode()
end

function BarSwitchModeTopPanel:OnEnterBtnClicked()
    Module.CommonBar:CloseBarSwitchMode()
    Module.IrisSafeHouse:Enter3DSafeHouse()
end

function BarSwitchModeTopPanel:OnServiceBtnClicked()
    --Module.CommonTips:ShowSimpleTip(Module.CommonTips.Config.Loc.CommingSoon)
    Module.CustomerServices:OpenEntrance(CustomerServicesEntranceType.ESCPage)
end

function BarSwitchModeTopPanel:OnSettingBtnClicked()
    Module.CommonBar:CloseBarSwitchMode()
    Module.SystemSetting:ShowSystemSettingMainView()
end

function BarSwitchModeTopPanel:OnExitBtnClicked()
    Module.CommonBar:CloseBarSwitchMode()
    -- @yixiaoguan 实现封装暴露到Module
    local fCancelLeaveGameHandle = CreateCallBack(function(self)
    end, self)

    local fConfirmLeaveGameHandle = CreateCallBack(function(self)
    end, self)
    Module.CommonBar:FlowBackQuitGame(fCancelLeaveGameHandle, fConfirmLeaveGameHandle)
end

function BarSwitchModeTopPanel:OnGMBtnClicked()
    if not VersionUtil.IsShipping() then
        Module.CommonBar:CloseBarSwitchMode()
        Module.GM:OpenGMButtonPanel()
        Module.GM:OpenGMMainPanel(true)
    end
end

function BarSwitchModeTopPanel:OnShowBegin()
    self:InitArmedForceMode()
    self:_InitShortcuts()
    self:UpdateItemAnim(true)

    -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
    if IsHD() then
        self:_EnableNavigation(true)
    end

    -- playGo还未加载完成，不显示BHD
    if IsPS5Family() and LiteDownloadManager and self._wtSwitchModeItemBHD then
        if not LiteDownloadManager:IsPlayGoFinished() then
            self._wtSwitchModeItemBHD:Collapsed()
        else
            self._wtSwitchModeItemBHD:SelfHitTestInvisible()
        end
    end
    -- END MODIFICATION  


    self._wtMusicPlayerWidget:RefreshView()
    self._wtMusicPlayerWidget:SetParentWidget(self)
end

function BarSwitchModeTopPanel:OnHideBegin()
    self:_RemoveShortcuts()
    self:UpdateItemAnim(false)

    -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
    if IsHD() then
        self:_EnableNavigation(false)
    end
    -- END MODIFICATION
end

function BarSwitchModeTopPanel:UpdateItemAnim(bShow)
    local needPlayItemList = {
        self._wtSwitchModeItemSOL,
        self._wtSwitchModeItemMP,
        self._wtSwitchModeItemBHD,
        self._wtSwitchModeItemDebugSOL
    }
    for count = 1 ,4 do
        local curItem = needPlayItemList[count]
        if not is_ui_invalid(curItem) then
            if bShow then
                curItem:PlayShowAni(self.BP_ItemDeltaTime * count)
            else
                curItem:PlayHideAni()
            end
        end
    end
end

function BarSwitchModeTopPanel:OnOpen()
    UDFMGameHudDelegates.Get(GetGameInstance()).OnHandlePostProcessMouseButtonUpEvent:Add(self._OnHandleMouseButtonUpEvent, self)
    --- BEGIN MODIFICATION @ VIRTUOS
    if IsPS5Family() then
        -- 监听PlayGO完成事件
        local DFMInitialChunkManager = UDFMInitialChunkManager.Get(GetWorld())
        if DFMInitialChunkManager then
            DFMInitialChunkManager.OnPlatformChunksDownloadCompleteDelegate:Add(self._OnPlatformChunksDownloadFinished, self)
        end
    end
    --- END MODIFICATION
    self:_UpdateIdcInfo()
end

function BarSwitchModeTopPanel:OnClose()
	UDFMGameHudDelegates.Get(GetGameInstance()).OnHandlePostProcessMouseButtonUpEvent:Remove(self._OnHandleMouseButtonUpEvent, self)
    --- BEGIN MODIFICATION @ VIRTUOS
    if IsPS5Family() then
        local DFMInitialChunkManager = UDFMInitialChunkManager.Get(GetWorld())
        if DFMInitialChunkManager then
            DFMInitialChunkManager.OnPlatformChunksDownloadCompleteDelegate:Remove(self._OnPlatformChunksDownloadFinished, self)
        end
    end
    --- END MODIFICATION
	self:RemoveAllLuaEvent()
end

function BarSwitchModeTopPanel:OnShow()
    self.bInEnd = true
end


function BarSwitchModeTopPanel:OnHide()
    self.bInEnd = false
end

function BarSwitchModeTopPanel:_OnHandleMouseButtonUpEvent(mouseEvent)
	local sceenPos = mouseEvent:GetScreenSpacePosition()
	local geometry = self._wtCanvasDraw:GetCachedGeometry()
    local isUnder = USlateBlueprintLibrary.IsUnderLocation(geometry, sceenPos)
    if not isUnder then
		if self.bInEnd then
            Module.CommonBar:CloseBarSwitchMode()
			self.bInEnd = false
		end
    end
end

function BarSwitchModeTopPanel:InitArmedForceMode()
    local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
    if armedForceMode == EArmedForceMode.MP then
        self._modeBtnId = SwitchModeBtnId.Top_MP
        self._wtBtnEnter:SetVisibility(ESlateVisibility.Collapsed)
    else
        self._modeBtnId = SwitchModeBtnId.Top_SOL
        self._wtBtnEnter:SetVisibility(ESlateVisibility.Visible)
    end

end

function BarSwitchModeTopPanel:_InitShortcuts()
    -- self._hActionOpenModeSwitch =
    --     self:AddInputActionBinding(
    --     "OpenModeSwitch",
    --     EInputEvent.IE_Pressed,
    --     self._OnSwitchModeBtnClicked,
    --     self,
    --     EDisplayInputActionPriority.UI_Pop
    -- )
end

function BarSwitchModeTopPanel:_RemoveShortcuts()
    -- if self._hActionOpenModeSwitch then
    --     self:RemoveInputActionBinding(self._hActionOpenModeSwitch)
    --     self._hActionOpenModeSwitch = nil
    -- end
end

function BarSwitchModeTopPanel:OnNavBack()
    Module.CommonBar:CloseBarSwitchMode()
    return true
end

-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
function BarSwitchModeTopPanel:_EnableNavigation(bEnable)
    if not IsHD() then
        return 
    end
    
    if bEnable then
        if not self._wtNavGroup then
            self._wtNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtCanvasDraw, self, "Hittest")
            if self._wtNavGroup then
                self._wtNavGroup:AddNavWidgetToArray(self._wtSwitchModeItemSOL)
                self._wtNavGroup:AddNavWidgetToArray(self._wtSwitchModeItemMP)
                self._wtNavGroup:AddNavWidgetToArray(self._wtSwitchModeItemBHD)
                self._wtNavGroup:AddNavWidgetToArray(self._wtSwitchModeItemDebugSOL)
                self._wtNavGroup:AddNavWidgetToArray(self._wtVbBottom)
                self._wtNavGroup:AddNavWidgetToArray(self._wtMusicPlayerWidget)
                self._wtNavGroup:AddNavWidgetToArray(self._wtIdcSelectorBtn)
                self._wtNavGroup:MarkIsStackControlGroup()
            end
        end
    else
        if self._wtNavGroup then
            WidgetUtil.RemoveNavigationGroup(self)
            self._wtNavGroup = nil
        end
    end
end

function BarSwitchModeTopPanel:_OnPlatformChunksDownloadFinished()
    if IsPS5Family() and self._wtSwitchModeItemBHD then
        self._wtSwitchModeItemBHD:SelfHitTestInvisible()
    end
end
-- END MODIFICATION

function BarSwitchModeTopPanel:_UpdateIdcInfo()
    local bEnableSwitch = Module.NetworkBusiness:IsEnableSwitchIDC()
    if bEnableSwitch then
        local fOnIdcSelectorChanged = CreateCallBack(function(self, res)
            if res and res.result == 0 then
                local desc = Module.NetworkBusiness:GetIdcDesc(res.match_area, res.rtt)
                if self._wtIdcDesc then
                    self._wtIdcDesc:SetText(desc)
                    self._wtIdcDesc:Visible()
                end
                if self._wtIdcSelectorBtn then
                    self._wtIdcSelectorBtn:Visible()
                end
            else
                if self._wtIdcDesc then
                    self._wtIdcDesc:Collapsed()
                end
            end
        end, self)
        Module.NetworkBusiness:GetSelectIdcInfo(fOnIdcSelectorChanged)
    else
        self._wtIdcDesc:Collapsed()
        self._wtIdcSelectorBtn:Collapsed()
    end
end

function BarSwitchModeTopPanel:OpenIDCSelectorPanel()
   Module.NetworkBusiness:OpenSwitchIDCPanel()
end
return BarSwitchModeTopPanel