----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMQuest)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class QuestSeasonTaskEntry : LuaUIBaseView

local QuestSeasonTaskEntry = ui("QuestSeasonTaskEntry")

function QuestSeasonTaskEntry:Ctor()   
    self._wtBtn = self:Wnd("DFButton_67", UIButton)
    self._wtBtn:Event("OnClicked", self._OnClickQuest, self)
    self._wtNameText = self:Wnd("DFTextBlock_QuestName", UITextBlock)

    self._questInfo = nil
    self._bIsSelected = false

    if IsHD() then
        self:Event("OnFocusReceivedEvent", self._OnFocusQuest, self)
    end
end

function QuestSeasonTaskEntry:OnShowBegin()
    self:StopWidgetAnim(self.WBP_SeasonalTasks_TaskEntry_V2_receive)
    self:StopWidgetAnim(self.WBP_SeasonalTasks_TaskEntry_V2_reward)
    self:_AddEventListener()
end

function QuestSeasonTaskEntry:OnHide()
    self:StopWidgetAnim(self.WBP_SeasonalTasks_TaskEntry_V2_receive)
    self:StopWidgetAnim(self.WBP_SeasonalTasks_TaskEntry_V2_reward)
    self:RemoveAllLuaEvent()
end

function QuestSeasonTaskEntry:_AddEventListener()
    self:AddLuaEvent(Server.QuestServer.Events.evtUpdateQuestState, self._OnQuestStateUpdate, self)
    self:AddLuaEvent(Server.QuestServer.Events.evtUpdateSeasonLevel, self._OnQuestStateUpdate, self)
end

function QuestSeasonTaskEntry:RefreshItemWidget(questInfo, bIsSelect)    
    self._questInfo = questInfo
    self._wtNameText:SetText(questInfo.name)
    self:SetIsSelected(bIsSelect)
end

function QuestSeasonTaskEntry:_OnClickQuest()
    -- loginfo("JackieTest: QuestSeasonTaskEntry: On Click")
    Module.Quest.Config.evtQuestSeasonEntryClicked:Invoke(self._questInfo, self)
end

function QuestSeasonTaskEntry:_OnFocusQuest()
    -- loginfo("JackieTest: QuestSeasonTaskEntry: On Focus")
    Module.Quest.Config.evtQuestSeasonEntryClicked:Invoke(self._questInfo, self)
end

function QuestSeasonTaskEntry:_OnQuestStateUpdate(questId)
    if questId then 
        if self._questInfo and self._questInfo.id == questId then
            self:_UpdateTaskState()
        end 
    end
end

function QuestSeasonTaskEntry:SetIsSelected(bIsSelect)
    self._bIsSelected = bIsSelect
    self:_UpdateTaskState()    
end

function QuestSeasonTaskEntry:_UpdateTaskState()

    self:StopWidgetAnim(self.WBP_SeasonalTasks_TaskEntry_V2_receive)
    self:StopWidgetAnim(self.WBP_SeasonalTasks_TaskEntry_V2_reward)

    local type = 0
    local icon = 0
    if self._questInfo.state == QuestState.Locked then
        if self._bIsSelected then
            type = 5
        else
            type = 4
        end
        icon = 4
    elseif self._questInfo.state == QuestState.Rewarded then
        if self._bIsSelected then
            type = 3
        else
            type = 2
        end
        icon = 0
    else
        if self._bIsSelected then
            type = 1
        else
            type = 0
        end
        if self._questInfo.state == QuestState.Completed then
            icon = 1
            self:PlayAnimation(self.WBP_SeasonalTasks_TaskEntry_V2_reward, 0, 0, EUMGSequencePlayMode.Forward, 1, true)
        elseif self._questInfo.state == QuestState.Unread or  
                self._questInfo.state == QuestState.Unaccepted then
            local lineInfo = Server.QuestServer:GetCurrentSeasonLine()

            if lineInfo == nil then
                logerror("No Valid Season Lineinfo")
                return
            end

            if not Server.QuestServer:IsQuestAcceptable(self._questInfo)
                or (self._questInfo.id == lineInfo._finalQuestID and not Module.Quest.Field:IsInSeasonOvertime())
            then
                if self._bIsSelected then
                    type = 5
                else
                    type = 4
                end
                icon = 4
            else
                icon = 2
                self:PlayAnimation(self.WBP_SeasonalTasks_TaskEntry_V2_receive, 0, 0, EUMGSequencePlayMode.Forward, 1, true)
            end
        else
            icon = 3
        end
    end
    self:SetIcon(icon)
    self:SetType(type)
end

return QuestSeasonTaskEntry