----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMWeaponAssembly)
----- LOG FUNCTION AUTO GENERATE END -----------

--定轨武器展示
---@class ActivityWeaponDisplayPanel : LuaUIBaseView
local ActivityWeaponDisplayPanel = ui("ActivityWeaponDisplayPanel")
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local EComp = CommonWidgetConfig.EIVWarehouseTempComponent
local EIVSlotPos = CommonWidgetConfig.EIVSlotPos
local EIVCompOrder = CommonWidgetConfig.EIVCompOrderlocal
local Config = Module.Activity.Config

function ActivityWeaponDisplayPanel:Ctor()
    self._wtTipsNameTxt = self:Wnd("DFTextBlock_187", UITextBlock)
    self._wtTipsDescTxt = self:Wnd("DFTextBlock", UITextBlock)
    self._wtTipsContent = self:Wnd("wtCommonCheckInstruction", DFCheckBoxOnly)
    if IsHD() then
        self._wtFTipsAnchor = UIUtil.WndTipsAnchor(self, "DFTipsAnchor_100", self._OnHovered, self._OnUnhovered, nil)
    else
        self._wtFTipsAnchor = self:Wnd("DFTipsAnchor_100", UIWidgetBase)
        self._wtTipsContent:SetCallback(self._OnCheckChanged, self)
    end
    self._wtIconImg = self:Wnd("wtQualityIcon", UIImage)
    self._wtNameTxt = self:Wnd("wtTextWeaponName", UITextBlock)
    self._wtDescTxt = self:Wnd("wtWeaponDesc", UITextBlock)
    self._wtDetaBtn = self:Wnd("wtButtonPreview"  , DFCommonButtonOnly)
    self._wtSureBtn = self:Wnd("wtButtonBuyBundle", DFCommonButtonOnly)
    self._wtWateBox = UIUtil.WndWaterfallScrollBox(self, "wtWaterfallView",  self._OnWaterfallCount, self._OnWaterfallWidget)
    self._wtDetaBtn:Event("OnClicked", self._OnClicked1, self)
    self._wtSureBtn:Event("OnClicked", self._OnClicked2, self)
end

function ActivityWeaponDisplayPanel:_OnClicked1()
    local itemData = self._itemData
    if itemData then
        local btn = self._wtDetaBtn
        if itemData.itemMainType == EItemType.WeaponSkin then
            Module.Collection:ShowWeaponSkinDetailPage(itemData, optHidePopupCallback)--武器皮肤(包括刀皮)
        elseif itemData.itemMainType == EItemType.Adapter then
            Module.Collection:ShowHangingDetailPage(itemData,    optHidePopupCallback)--配件(挂饰)
        elseif itemData.itemMainType == EItemType.Fashion then
            Facade.UIManager:AsyncShowUI(UIName2ID.StaffLotteryAccessoriesPreview, nil, nil, itemData.id)--干员皮肤
        else
            Module.ItemDetail:OpenItemDetailPanel(itemData, btn)--详情页
        end
    end
end

function ActivityWeaponDisplayPanel:_OnClicked2()
    if self._func then
        self._func(self._itemData)
        Facade.UIManager:CloseUI(self)
    end
end

function ActivityWeaponDisplayPanel:_OnCheckChanged(isBool)
    if isBool then
        self:_OnHovered()
    else
        self:_OnUnhovered()
    end
end

function ActivityWeaponDisplayPanel:_OnHovered()
    self:_OnUnhovered()
    local contents = self._contents
    if contents then
        self._handle = Module.CommonTips:ShowAssembledTips(contents, self._wtFTipsAnchor)
    end
end

function ActivityWeaponDisplayPanel:_OnUnhovered()
    if self._handle and self._handle.GetUIIns then
        self._wtTipsContent:SetIsChecked(false, false)
        Module.CommonTips:RemoveAssembledTips(self._handle, self._wtFTipsAnchor)
        self._handle = nil
    end
end

function ActivityWeaponDisplayPanel:_OnWaterfallCount()
    if self._list then
        return #self._list
    end
    return 0
end

function ActivityWeaponDisplayPanel:_OnWaterfallWidget(position, itemWidget)
    local index = position
    local list = self._list
    if list and itemWidget then
        local id = 0
        local data = list[index]
        if data then
            id = data.id or data.prop_id
        end
        local itemData = General.GetSkinItemData({id = id})
        if itemData then
            itemData["num_id"] = data.num_id
            local func = function(caller, index, itemData)
                self:_OnRewardClicked(index, itemData)
            end
            if index == self._defaultIndex then
                self:_OnRewardClicked(index, itemData)
                self._itemData = itemData
            end
            itemWidget:SetItemView(index, itemData, func)
            itemWidget:SetItemSelected(index == self._defaultIndex)
            itemWidget:SetItemGive(false)
            itemWidget:SetItemOwned(false)
            itemWidget:SetItemGet(General.IsHaved(itemData.id))
            if self._items == nil then
                self._items = {}
            end
            table.insert(self._items, itemWidget)
        end
    end
end

function ActivityWeaponDisplayPanel:_OnRewardClicked(index, itemData)
    self._itemData = itemData
    self._defaultIndex = index
    if itemData then
        self:_SetItemData(itemData)
        if General.IsHaved(itemData.id) then
            self._wtSureBtn:SetMainTitle(Config.Loc.ActUITextBlock[10])
            self._wtSureBtn:SetBtnEnable(false)
        else
            self._wtSureBtn:SetMainTitle(Config.Loc.ActUITextBlock[9])
            self._wtSureBtn:SetBtnEnable(true)
        end
        for _, itemWidget in ipairs(self._items or {}) do
            itemWidget:SetItemSelected(itemWidget:GetIndex() == self._defaultIndex)
        end
        --加载武器场景
        local curSubStage = Facade.GameFlowManager:GetCurrentSubStage()
        if curSubStage ~= ESubStage.HallMall then
            Timer.DelayCall(0, function()
                Facade.GameFlowManager:EnterSubStage(ESubStage.HallMall)
            end, self)
        else
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayWeaponAutoBoundAdapter", itemData:GetRawDescObj(), false, false)
        end
    end
end

function ActivityWeaponDisplayPanel:_AddEventListener()
    self:AddLuaEvent(LuaGlobalEvents.evtSceneLoaded, self._OnEnterExpertLevel, self)
end

function ActivityWeaponDisplayPanel:_OnEnterExpertLevel()
    self:_OnRewardClicked(self._defaultIndex, self._itemData)
end

---comment
---@param data any data = {items, tipName, tipDesc, tipContent}
---@param func any 回调函数
function ActivityWeaponDisplayPanel:OnInitExtraData(data, func)
    self._data = data
    self._func = func
    self._defaultIndex = 1
end

function ActivityWeaponDisplayPanel:_InitData()
    local data = self._data
    if data then
        self._contents = data.tipContent
        if data.title then
            Module.CommonBar:RegStackUITopBarTitle(self, data.title)
        end
        if data.tipName then
            self._wtTipsNameTxt:SetText(General.GetText(data.tipName))
            self._wtTipsNameTxt:SelfHitTestInvisible()
        else
            self._wtTipsNameTxt:Collapsed()
        end
        if data.tipDesc then
            self._wtTipsDescTxt:SetText(General.GetText(data.tipDesc))
            self._wtTipsDescTxt:SelfHitTestInvisible()
        else
            self._wtTipsDescTxt:Collapsed()
        end
        if self._contents then
            self._wtTipsContent:Visible()
            self._wtFTipsAnchor:SelfHitTestInvisible()
        else
            self._wtTipsContent:Collapsed()
            self._wtFTipsAnchor:Collapsed()
        end

        if data.tipName and self._func then
            self._wtSureBtn:Visible()
        else
            self._wtSureBtn:Collapsed()
        end
        self._list = {}
        --已获取后置(排序会影响外面奖池顺序)
        for index, value in ipairs(data.items or {}) do
            table.insert(self._list, value)
        end
        if self._list then
            local SortFunc = function(a, b)
                local a1 = General.IsHaved(a.id or a.prop_id)
                local b1 = General.IsHaved(b.id or b.prop_id)
                if a1 ~= b1 and b1 == true then
                    return true
                end
                return false
            end
            table.sort(self._list, SortFunc)
        end
        self._wtWateBox:RefreshAllItems()
        --手柄适配
        self:_AddGamepadInputs()
    end
end

function ActivityWeaponDisplayPanel:_SetItemData(itemData)
    if itemData then
        local QualityIcon = Config.QualityIconMapping
        if QualityIcon then
            local path = QualityIcon[itemData.quality]
            local color = ItemConfigTool.GetItemQualityLinearColor(itemData.quality)
            if path and color then
                self._wtIconImg:AsyncSetImagePath(path or "", true)
                self._wtIconImg:SetColorAndOpacity(color)
            end
        end
        self._wtNameTxt:SetText(itemData.name or "")
        self._wtDescTxt:SetText(General.GetItemDataDesc(itemData))
        self._itemData = itemData
    end
end

function ActivityWeaponDisplayPanel:_AddGamepadInputs()
    self:_RemoveGamepadInputs()
    if IsHD() then
        local DetailsFunc = function()
            self:_OnClicked1()
        end
        local OutFunc = function()
            self._isTips = not self._isTips
            self:_OnCheckChanged(self._isTips)
        end
        local list = {{actionName = "Act_Reward_Prop_Details", func = DetailsFunc, caller = self, bUIOnly = false}}
        if self._contents then
            table.insert(list, {actionName = "Act_Reward_Point_Out", func = OutFunc, caller = self, bUIOnly = false})
        end
        --让出A键
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)

        Module.CommonBar:SetBottomBarTempInputSummaryList(list)
        --a建选择
        if self._wtSureBtn then
            self._handleBtn = self:AddInputActionBinding("CaptureGameView_Confirm", EInputEvent.IE_Pressed, self._OnButtonClick, self, EDisplayInputActionPriority.UI_Pop)
            self._wtSureBtn:SetDisplayInputAction("CaptureGameView_Confirm", true, nil, true)
        end

        if not isvalid(self._navGroup) then
            self._navGroup = WidgetUtil.RegisterNavigationGroup(self._wtWateBox, self, "Hittest")
            self._navGroup:AddNavWidgetToArray(self._wtWateBox)
            self._navGroup:SetScrollRecipient(self._wtWateBox)
            -- self._navGroup:MarkIsStackControlGroup()
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroup)
        end
    end
end

function ActivityWeaponDisplayPanel:_OnButtonClick()
    if self._wtSureBtn then
        self._wtSureBtn:ButtonClick()
    end
end

function ActivityWeaponDisplayPanel:_RemoveGamepadInputs()
    if self._handleBtn then
        self:RemoveInputActionBinding(self._handleBtn)
        self._handleBtn = nil
    end
    self._navGroup = nil
    WidgetUtil.RemoveNavigationGroup(self)
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    Module.CommonBar:RecoverBottomBarInputSummaryList()
end

function ActivityWeaponDisplayPanel:OnShowBegin()
    self:_AddMouseButtonUp()
    self:_AddEventListener()
    self:_InitData()
    self:_OnEnterExpertLevel()
end

function ActivityWeaponDisplayPanel:OnHideBegin()
    self:_RemoveMouseButtonUp()
    self:RemoveAllLuaEvent()
    self:_OnUnhovered()
    Module.ItemDetail:CloseItemDetailPanel()
end

function ActivityWeaponDisplayPanel:OnClose()
    self:_RemoveGamepadInputs()
    self._items = nil
end

function ActivityWeaponDisplayPanel:_AddMouseButtonUp()
    local gamelnst = GetGameInstance()
    if self._btnHandle == nil and gamelnst then
        self._btnHandle = UDFMGameHudDelegates.Get(gamelnst).OnHandleMouseButtonUpEvent:Add(self._OnMouseButtonDown, self)
    end
end

function ActivityWeaponDisplayPanel:_RemoveMouseButtonUp()
    local gamelnst = GetGameInstance()
    if self._btnHandle and gamelnst then
        UDFMGameHudDelegates.Get(gamelnst).OnHandleMouseButtonUpEvent:Remove(self._btnHandle)
        self._btnHandle = nil
    end
end

--鼠标点击屏幕事件
function ActivityWeaponDisplayPanel:_OnMouseButtonDown(mouseEvent)
    local btn = self._wtTipsContent
    if mouseEvent and btn then
        local sceenPos = mouseEvent:GetScreenSpacePosition()
        local geometry = btn:GetCachedGeometry()
    	local isUnder = USlateBlueprintLibrary.IsUnderLocation(geometry, sceenPos)
        if not isUnder then
            self:_OnUnhovered()
        end
    end
end

return ActivityWeaponDisplayPanel