----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMStore)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"

---@class StoreMainPanel : LuaUIBaseView
local StoreMainPanel   = ui("StoreMainPanel")
local StoreConfig     = Module.Store.Config
local StoreLogic      = require "DFM.Business.Module.StoreModule.Logic.StoreLogic"
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"
local ButtonIdConfig  = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.ButtonIdConfig"
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
local LocalizeTool    = require "DFM.StandaloneLua.BusinessTool.LocalizeTool"
local StoreDataTrie   = require "DFM.Business.Module.ReddotTrieModule.DotUnit.StoreDotUnit.StoreTrie.StoreDataTrie"

function StoreMainPanel:Ctor()

    self.countDrawRoleSkin = 0
    self.countDrawMandel = 0

    self.test = 0
    self.tab2 = 0

    self._canvasPages = self:Wnd("CanvasMain", UIWidgetBase)
    self.defaultPageID = EStoreTab.Recommend

    self.dictTab1AndDefaultTab2 = {}

    Server.StoreServer:SendShopGetBuyRecordReq()

    -- Server.CurrencyServer:RegisterFakeCurrency(ECurrencyClientId.ResearchVoucher, ECurrencyItemId.ResearchVoucher)
    if DFHD_LUA == 1 then
        Module.CommonBar:RegStackUITopBarCurrencyTypeList(self,
            { ECurrencyClientId.BindDiamond, ECurrencyClientId.UnbindDiamond })
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.DefaultSecondary)

        Module.CommonBar:ShowBottomBar()
    else
        Module.CommonBar:RegStackUITopBarCurrencyTypeList(self,
            { ECurrencyClientId.BindDiamond, ECurrencyClientId.UnbindDiamond })
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.Default)
    end

    Module.CommonBar:RegStackUITopBarTitle(self, StoreConfig.Loc.TopBarTitle)
end

function StoreMainPanel:PrepareSubUIInfo(tabParam)
    self._createID = 0
    --新UI框架
    self.uiNavIDList = {}
    self.uiNavIDMapList = {}
    self.navIDMap = {}

    self.bundleInfo = {}
    self.skinInfo = {}
    self.mandelInfo = {}
    self.collabMandelInfo = {}
    self.collabSkinInfo = {}
    self.collabBundleInfo = {}

    self.tabCollaboration = 0 -- 标记活动页签是第几个

    self.themeId, self.themeTabName = Server.StoreServer:CheckIsCollaborationOn() -- 联动活动Id

    local tabCnt = 1
    self.collabBundleTab = 1

    self.mainTabTable = Server.StoreServer:GetMainTabTable()
    self.subTabTable = Server.StoreServer:GetSubTabTable()
    local mainTabInfos = {}
    local subTabInfos = {}
    for _, row in ipairs(self.mainTabTable) do
        mainTabInfos[row.TabId] = row
    end

    for _, row in ipairs(self.subTabTable) do
        subTabInfos[row.SubTabId] = row
    end

    self.bundleInfo, self.collabBundleInfo = Server.StoreServer:GetAllBundleInfo()
    self.skinInfo, self.collabSkinInfo = Server.StoreServer:GetDrawByType(3, self.themeId)
    self.mandelInfo, self.collabMandelInfo = Server.StoreServer:GetDrawByType(1, self.themeId)
    self.mallGiftData = Server.StoreServer:GetMallGiftDatas()

    local info = {
        tabTxtList             =            {},
        imgPathList            =            {},
        fCallbackIns           =            SafeCallBack(self._OnGroupTabClick, self),
        defalutIdx             =            self.defaultPageID,
        tabGroupSize           =            FVector2D(1904, 96),
        tabSpaceMargin         =            FMargin(0, 0, 16, 0),
        bTriggerCallback       =            true,
        tertiaryTabs           =            nil,
        bPostCallbackWhenPop   =            false,
        bNewReddotTrie         =            true,
        reddotTrieRegItemList  =            {},
        themeIDList            =            {},
        themeBundleIDList      =            {},
    }
    info.tertiaryTabs = { [0] = {} }

    for index, value in ipairs(self.mainTabTable) do
        if value.TabID == "HotRecommendation" then
            Server.StoreServer:SetRecommendTabId(tabCnt)
        end
        local hasData = false
        local defaultTab2 = 1

        local tabNameList = {}
        local tabIconList = {}
        local subThemeId = {}
        local subThemeBundleId = {}
        local reddotRegList = {}

        local SubTabLists = Server.StoreServer:StringtoTable(value.SubTabList)
        local tab2Cnt = 1
        for _, v in ipairs(SubTabLists) do
            local data = {}
            local createId = 0 
            if EStoreTab[v] ~= nil then
                data, createId = self:GetTabData(EStoreTab[v], value.UseCollabData)
            end
            if #data > 0 then
                hasData = true
                if self.navIDMap[tabCnt] == nil then
                    self.navIDMap[tabCnt] = {}
                end

                if subTabInfos[v] ~= nil and subTabInfos[v].IsExpanded then
                    for _, t in ipairs(data) do
                        self.navIDMap[tabCnt][self:GetCreateID()] = createId
                        

                        table.insert(tabNameList, t.LotteryName)
                        table.insert(tabIconList, t.LotteryIcon)
                        if value.UseCollabData then
                            table.insert(subThemeId, self.themeId)
                            table.insert(subThemeBundleId, t.LotteryId)
                            self.tabCollaboration = tabCnt
                        end

                        local sRedDotKey = "Store_Tab".. tostring(tabCnt) .."_Child" .. tostring(Server.StoreServer:GetRedDotKeyByLotteryID(t.LotteryId))
                        table.insert(reddotRegList, {
                            uiNavId = UIName2ID.TopBar,
                            reddotDataConfigWithStyleList = {{obType = EReddotTrieObserverType.Store, key = sRedDotKey}}
                        })

                        -- Server.StoreServer:SetReddotChildNodes(tabCnt, t.LotteryId)

                        Server.StoreServer:SetJumpTab(t.LotteryId, tabCnt, tab2Cnt)

                        if tabParam == t.LotteryId then
                            defaultTab2 = tab2Cnt
                        end

                        Server.StoreServer:SetTabCntToTabName(tabCnt, tab2Cnt, v)
                        tab2Cnt = tab2Cnt + 1
                    end
                else
                    self.navIDMap[tabCnt][self:GetCreateID()] = createId

                    if #SubTabLists > 1 and mainTabInfos ~= nil then
                        table.insert(tabNameList, Module.Store.Config.Loc.CollaborationBundleTab)
                    end

                    if value.UseCollabData then
                        table.insert(tabIconList, Server.StoreServer:getCollabSubTabIcon())
                        for i, d in ipairs(self.mainTabTable) do
                            if v == d.TabId then
                                -- Server.StoreServer:SetReddotChildNodes(tabCnt, i)
                                local sRedDotKey = "Store_Tab".. tostring(tabCnt) .."_Child" .. tostring(0)
                                table.insert(reddotRegList, {
                                    uiNavId = UIName2ID.TopBar,
                                    reddotDataConfigWithStyleList = {{obType = EReddotTrieObserverType.Store, key = sRedDotKey}}
                                })
                                break
                            end
                        end
                    else
                        -- Server.StoreServer:SetReddotChildNodes(tabCnt, nil)
                    end

                    for _, t in ipairs(data) do
                        if t == 1 then
                            break
                        end
                        if value.UseCollabData then
                            table.insert(subThemeBundleId, t.TabId)
                        end
                        Server.StoreServer:SetJumpTab(t.TabId, tabCnt, tab2Cnt)

                        if tabParam == t.TabId then
                            defaultTab2 = tab2Cnt
                        end
                    end

                    Server.StoreServer:SetTabCntToTabName(tabCnt, tab2Cnt, v)
                    tab2Cnt = tab2Cnt + 1
                end
            end
        end
        if hasData then
            if tab2Cnt == 2 then    --- 说明仅有一个子页签 则需要隐藏子页签
                tabNameList = {}
                tabIconList = {}
            end
            table.insert(info.tabTxtList, value.TabName)
            if not IsHD() then
                table.insert(info.imgPathList, value.TabIcon)
            end
            if self.tabCollaboration == tabCnt then
                table.insert(info.themeIDList, self.themeId)
            else
                table.insert(info.themeIDList, 0)
            end

            table.insert(info.reddotTrieRegItemList, {
                uiNavId = UIName2ID.TopBar,
                reddotDataConfigWithStyleList = {{obType = EReddotTrieObserverType.Store, key = "Store_Tab" .. tostring(tabCnt), reddotStyle = {placeOffset = FVector2D(0,12)}}}
            })

            info.tertiaryTabs[tabCnt] = {
                tabTxtList = tabNameList,
                fCallbackIns = SafeCallBack(self._OnClickSubTab, self),
                defalutIdx = nil,
                imgPathList = nil,
                bPostCallbackWhenPop = false,
                bNewReddotTrie = true,
                reddotTrieRegItemList = reddotRegList,
                themeIDList = subThemeId,
                themeBundleIDList = subThemeBundleId,
            }

            if IsHD() then
                info.tertiaryTabs[tabCnt].imgPathList = tabIconList
            end

            Server.StoreServer:SetTabNameToTabCnt(EStoreTab[value.TabId], tabCnt)

            Server.StoreServer:SetTabNameToSubTabCnt(EStoreTab[value.TabId], tabNameList and #tabNameList or 0)

            tabCnt = tabCnt + 1
        end
    end

    return info
end

function StoreMainPanel:GetTabData(key, useCollabData)
    local createId = 0
    local data = {}
    if key == EStoreTab.StaffLottery then
        createId = UIName2ID.StaffLotteryMainUI
        if useCollabData then
            data = self.collabSkinInfo
        else
            data = self.skinInfo
        end
    elseif key == EStoreTab.HotRecommendation then
        createId = UIName2ID.StoreRecommendHomepage
        if useCollabData then
            data = self.collabBundleInfo
        else
            data = self.bundleInfo
        end
    elseif key == EStoreTab.MandelLottery then
        createId = UIName2ID.StoreMandelDraw
        if useCollabData then
            data = self.collabMandelInfo
        else
            data = self.mandelInfo
        end
    elseif key == EStoreTab.WeaponSkinSales then
        createId = UIName2ID.StoreWeaponSkinsSale
        data = self.mallGiftData
    elseif key == EStoreTab.LuckyNest then
        createId = UIName2ID.StoreNightMarket
        data = Server.StoreServer:CheckIsLuckyNestOn()
    end

    return data, createId
end

function StoreMainPanel:InitNavIDList()
    local sortedTable = {}
    for idx, value in pairs(self.navIDMap) do
        sortedTable[idx] = value
    end

    local cnt = 0
    for idx, value in pairs(sortedTable) do
        local counter = 0
        for createid, nameid in pairs(value) do
            table.insert(self.uiNavIDList, nameid)
            counter = counter + 1
            cnt = cnt + 1
            self.uiNavIDMapList[self:GetIndexKey(idx, counter)] = cnt
        end
    end
end


function StoreMainPanel:GetCreateID()
    self._createID = self._createID + 1
    return self._createID
end

function StoreMainPanel:GetIndexKey(tab1, tab2)
    return tab1 * 100 + tab2
end

function StoreMainPanel:GetUIIndex(tab1, tab2)
    return self.uiNavIDMapList[self:GetIndexKey(tab1, tab2)]
end

function StoreMainPanel:RefreshCurrentUI(index)
    if self.selectTabIndex == index then
        return
    end

    self.selectTabIndex = index
    local weakUiIns = nil
    weakUiIns = Facade.UIManager:SwitchSubUIByIndex(self, index, self._canvasPages, self.tabParam, self.tab2, self.IsCollabration, self.needPlayStaffLotteryVedioLater)

    if weakUiIns ~= nil then
        local uiIns = getfromweak(weakUiIns)
        self._currentChildPage = uiIns
    end
end

function StoreMainPanel:_OnStoreSwitchMainTabByIndex(tabID, tabParam)
    self.tab1, self.tab2 = Server.StoreServer:GetJumpTab(tonumber(tabParam))
    self.dictTab1AndDefaultTab2[self.tab1] = self.tab2

    self.fobidSubClick = true
    logerror('OverrideTopTabGroupInfoIdx StoreMainHome:_OnStoreSwitchMainTabByIndex(index) tabID:', tabID, ' self.tab1, self.tab2:', self.tab1, self.tab2)
    Module.CommonBar:CheckTopTabGroupTabLevel23(self.tab1, self.tab2, true, false)
end

function StoreMainPanel:_OnStoreDataRefresh()
    self:RemoveLuaEvent(Server.StoreServer.Events.evtStoreDataRefresh)

    local newDrawRoleSkinCount = 0
    local newDrawMandelCount = 0

    local SkinInfo = Server.StoreServer:GetDrawByType(3)
    --- 曼德尔砖是否存在有效活动
    local DrawInfo = Server.StoreServer:GetDrawByType(1)

    if SkinInfo ~= nil then
        newDrawRoleSkinCount = #SkinInfo
    end

    if SkinInfo ~= nil then
        newDrawMandelCount = #SkinInfo
    end

    if self.countDrawRoleSkin ~= newDrawRoleSkinCount or self.countDrawMandel ~= newDrawMandelCount then
        --刷新
        -- self:CheckDrawExist()

        -- local topTabGroupRegInfo = self:GetCommonBarRegInfo()
        -- -- topTabGroupRegInfo.uiNavId = self.UINavID or 0
        -- Module.CommonBar:SetTopTabGroup(topTabGroupRegInfo, 2)
    end
end


function StoreMainPanel:OnCloseBtnClick()

end

-- UI构造时触发, 用于初始化外部传参UI数据逻辑，由UIManager触发（仅触发一次）
-- 子节点蓝图不会触发该方法
-- Module加载好子控件所需的图片后，将数据传入主UI界面
function StoreMainPanel:OnInitExtraData(tabID, tabParam, enterType)
    Facade.UIManager:UnRegSwitchSubUI(self)
    self.IsCollabration = false
    self.needPlayStaffLotteryVedioLater = false

    local topTabGroupRegInfo = self:PrepareSubUIInfo(tabParam)

    self.tabParam = tabParam
    self.dictTab1AndDefaultTab2 = {}
    self.tab1, self.tab2 = Server.StoreServer:GetJumpTab(tabParam)
    self.dictTab1AndDefaultTab2[self.tab1] = self.tab2
    self.defaultPageID = self.tab1 or 1

    topTabGroupRegInfo.defalutIdx = self.defaultPageID


    Module.CommonBar:RegStackUITopBarTabGroupRegInfo(self, topTabGroupRegInfo)

    self:InitNavIDList()
    Facade.UIManager:RegSwitchSubUI(self, self.uiNavIDList)

    self.selectTabIndex = -1

    if enterType == nil then
        enterType = 4
    end
    LogAnalysisTool.DoSendStoreViewPageReportLog(0, 1, enterType, 0)

    self.lastClickDrawTime = Facade.ClockManager:GetLocalTimestamp()
end

function StoreMainPanel:SetDefaultTab(tabID, tabParam)
    self.tabParam = tabParam
    self.defaultPageID = Server.StoreServer:GetTabCnt(tabID)
    self.tab1 = self.defaultPageID
    local data = self:GetTabData(tabID, self.tab1 == self.tabCollaboration)
    self.tab2 = self:GetTab2()
end

function StoreMainPanel:_SwitchUIWithParam(pageID, param)
    local index = 0
    local tab2 = 1

    -- if pageID == EStoreTab.Draw then
    --     local lottryInfo = Server.StoreServer:GetDrawByType(param)
    --     if lottryInfo ~= nil then
    --         if lottryInfo.LotteryType == 3 then
    --             -- role skin
    --             tab2 = 1
    --         else
    --             -- mandel
    --             tab2 = 2
    --         end
    --     end
    -- end

    self._defaultSelectedInputTabType = tab2
    -- index = self:GetUIIndex(pageID, tab2)

    self:RefreshCurrentUIWithTwoParam(pageID, tab2)

    -- if weakUiIns ~= nil then
    --     local uiIns = getfromweak(weakUiIns)
    --     self._currentChildPage = uiIns
    -- end
end

-- UI打开时触� 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后�---@overload fun(LuaUIBaseView, OnOpen)
function StoreMainPanel:OnOpen()
    Module.Store.Field:ResetVideoAutoPlay()
    self:AddLuaEvent(Server.StoreServer.Events.evtStoreLotteryInfoUpdated, self._OnLotteryInfoChanged, self)
    self:AddLuaEvent(StoreConfig.evtStoreMainPanelRefreshCurrencyState, self._OnStoreMainPanelRefreshCurrencyState, self)
    loginfo("StoreServer:GetServerLotteryInfo:Bind _OnLotteryInfoChanged")

    --req product info
    if IsMobile() and IsBuildRegionGA() then

    else
        local productInfo = Server.StoreServer:GetAllStoreProductInfo()
        if productInfo ~= nil then
            loginfo("StoreMainPanel:OnOpen Module.Pay:GetProductInfo count:" .. #productInfo)
            Module.Pay:GetProductInfo(productInfo)
        end
    end


    if Server.PayServer:IsGoogleEnable() then
        Module.Pay:ReapplyReceipt()
    end
end

-- UI关闭时触� 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function StoreMainPanel:OnClose()
    -- LuaTickController:Get():RemoveTick(self, 1)

    Facade.UIManager:UnRegSwitchSubUI(self)
end

function StoreMainPanel:OnShowBegin()
    self:AddLuaEvent(Module.Store.Config.evtMandelDrawShow, self.evtOnMandelDrawShow, self)
    self:AddLuaEvent(Module.Store.Config.evtMandelDrawHide, self.evtOnMandelDrawHide, self)
    Module.Gamelet:PrintAppLog()
    Server.StoreServer:SetIsInStore(true)
end

function StoreMainPanel:OnHideBegin()
    self._tab1 = self.tab1
    self._tab2 = self.tab2

    Server.StoreServer:SetIsInStore(false)
end

-- UI OnOpen后、调用Show()显示时触�-- 做表�---@overload fun(LuaUIBaseView, OnShow)
function StoreMainPanel:OnShow()
    Server.StoreServer:SendShopNewGetConfigReq()
    Server.StoreServer:SendShopGetClickedRedDotReq()
    Server.StoreServer:SendShopGetThemeBundleTimeConfigReq()
    -- LuaTickController:Get():RegisterTick(self, 1)

    self:RegisterEventListeners()

    if LiteDownloadManager:IsSupportLitePackage() then
        local isDownlaodedAll = LiteDownloadManager:IsAllQuestDownloaded()
        if isDownlaodedAll == false then
            LiteDownloadManager:StartCheckModuleInfosAsync()
        end
    end
end

-- UI OnClose前、调用Hide()隐藏时触�---@overload fun(LuaUIBaseView, OnHide)
function StoreMainPanel:OnHide()
    -- self:RemoveAllLuaEvent()

    self:RemoveLuaEvent(Module.Store.Config.evtMandelDrawShow)
    self:RemoveLuaEvent(Module.Store.Config.evtMandelDrawHide)
    self:UnRegisterEventListeners()
    -- Facade.UIManager:UnRegSwitchSubUI(self)
    if not Module.Store:GetIsToPricePool() then
        Facade.SoundManager:StopUIAudioEvent(Module.Store:GetCurMusicName())
        loginfo("[echewzhu]StoreModule Facade.SoundManager:StopUIAudioEvent musicName = "..Module.Store:GetCurMusicName())
    end

end

function StoreMainPanel:RegisterEventListeners()
    self:AddLuaEvent(StoreConfig.evtStoreSwitchMainTabByIndex, self._OnStoreSwitchMainTabByIndex, self)
    self:AddLuaEvent(Server.StoreServer.Events.evtStoreDataRefresh, self._OnStoreDataRefresh, self)
end

function StoreMainPanel:UnRegisterEventListeners()
    self:RemoveLuaEvent(StoreConfig.evtStoreSwitchMainTabByIndex)
end

function StoreMainPanel:Update(dt)

end

function StoreMainPanel:InitCommonBar()

end

function StoreMainPanel:CheckDrawExist()
    self.countDrawRoleSkin = 0
    self.countDrawMandel = 0
    --- 干员抽奖是否存在有效活动
    local SkinInfo = Server.StoreServer:GetDrawByType(3)
    self.bIsSkinExist = (next(SkinInfo) ~= nil)
    self.roleSkinExist =  (next(SkinInfo) ~= nil)

    --- 曼德尔砖是否存在有效活动
    local DrawInfo = Server.StoreServer:GetDrawByType(1)
    self.bIsDrawExist = (next(DrawInfo) ~= nil)
    self.drawSkinExist = (next(DrawInfo) ~= nil)

    if SkinInfo ~= nil then
        self.countDrawRoleSkin = #SkinInfo
    end
    if DrawInfo ~= nil then
        self.countDrawMandel = #DrawInfo
    end
    --- 研究中心是否存在有效活动
    self.bIsLotteryExist = self.bIsDrawExist or self.bIsSkinExist
end

function StoreMainPanel:_OnClickSubTab(tabIndex, lastTabIndex)
    -- if not self.bIsSkinExist and tabIndex == 1 then
    --     self.tab2 = tabIndex + 1
    -- else
    --     self.tab2 = tabIndex
    -- end
    if self.fobidSubClick == true then
        self.fobidSubClick = false
        Module.CommonBar:CheckTopTabGroup(self.tab2, false, 3, false)
        return
    end

    self.tab2 = tabIndex
    self.dictTab1AndDefaultTab2[self.tab1] = self.tab2
    self:ChangeParam()
end

function StoreMainPanel:_OnGroupTabClick(index)
    local nowClcikTime = Facade.ClockManager:GetLocalTimestamp()
    local timeSpan = nowClcikTime - self.lastClickDrawTime
    if timeSpan > 2 then
        Server.StoreServer:SendShopNewGetConfigReq()
        self.lastClickDrawTime = nowClcikTime
    end

    local btnId = 10150000 + index
    LogAnalysisTool.SignButtonClicked(btnId)

    self.tab1 = index

    self.fobidSubClick = true

    local tryGetDefault2 = self.dictTab1AndDefaultTab2[self.tab1]
    if tryGetDefault2 == nil then
        self.tab2 = 1
    else
        self.tab2 = tryGetDefault2
    end

    if index == self.tabCollaboration and not Module.Store.Field:GetCollabTabVedioPlayed() then
        LogAnalysisTool.DoSendStoreViewPageReportLog(11, 0, 0, 0)

        -- 屏蔽光标
        if IsHD() then
            WidgetUtil.SetFreeAnalogCursorIsBlocked(self, true, false)
        end

        Module.Store.Field:SetCollabTabVedioPlayed()
        self.needPlayStaffLotteryVedioLater = true
        self:ChangeParam()
        Module.CommonWidget:ShowFullScreenVideoView(Server.StoreServer:GetCollabTabMediaRowName(), false, false, self.PlayStoreTransition, self, 1, 0)
        return
    end

    self:ChangeParam()
end

function StoreMainPanel:ChangeParam()
    if self.tab1 == self.tabCollaboration then
        self.IsCollabration = true
    else
        self.IsCollabration = false
    end

    -- 恢复虚拟光标
    if IsHD() then
        WidgetUtil.SetFreeAnalogCursorIsBlocked(self, false)
    end

    self:RefreshCurrentUIWithTwoParam(self.tab1, self.tab2)
    self:RefreshCurrencyState(self.tab1, self.tab2)
    self.tabParam = 0
end

function StoreMainPanel:PlayStoreTransition()
    Facade.UIManager:AsyncShowUI(UIName2ID.StoreCollabTabTransition, nil, nil)
    Module.Store.Config.evtStaffLotteryVedioPlay:Invoke()
    self.needPlayStaffLotteryVedioLater = false
end

function StoreMainPanel:RefreshCurrentUIWithTwoParam(tab1, tab2)
    local index = self:GetUIIndex(tab1, tab2)
    Module.Store.Field:SetNowTabID(tab1)
    self:RefreshCurrentUI(index)
end

function StoreMainPanel:evtOnMandelDrawShow()
end
function StoreMainPanel:evtOnMandelDrawHide()
end

function StoreMainPanel:RefreshCurrencyState(mainTabId, subTabId)

    -- local subTabName = ""
    -- for index, value in ipairs(SubTabLists) do
    --     if index == subTabId then
    --         subTabName = value
    --     end
    -- end

    local subTabName = Server.StoreServer:GetTabName(mainTabId, subTabId)
    if subTabName == nil then
        return
    end

    if subTabName == "MandelLottery" then
        Module.Store.Config.evtStoreMainPanelRefreshCurrencyStateDone:Invoke()
        return
    end

    local currencyTypeList = {}
    for index, value in ipairs(self.subTabTable) do
        if subTabName == value.SubTabId then
            local list = Server.StoreServer:StringtoNumTable(value.CurrencyColumn)
            local currencyConfig = Server.StoreServer:GetCurrencyConfig()
            if mainTabId == self.tabCollaboration and subTabName == "StaffLottery" and list[1] then
               list[1] = ECurrencyItemId.CollabResearchVoucher
            end
            for _, v in ipairs(list) do
                table.insert(currencyTypeList, currencyConfig[v])
            end
            if value.ShowTimeLimitCoin == true and Server.CollectionServer:NeedShowConsumeTickets() then
                table.insert(currencyTypeList, ECurrencyClientId.ConsumerCoupon)
            end
            break
        end
    end

    Module.CommonBar:RefreshCurrencyTypeList(currencyTypeList, self)
end

function StoreMainPanel:_OnLotteryInfoChanged(res)
    -- Module.Store.Field:SetLotteryData(res)
end

function StoreMainPanel:_OnStoreMainPanelRefreshCurrencyState(currencyID)
    if currencyID == nil then
        Module.CommonBar:RefreshCurrencyTypeList({ ECurrencyClientId.QuantumKey, ECurrencyClientId.MandelCoins, ECurrencyClientId.UnbindDiamond }, self)
    else
        Module.CommonBar:RefreshCurrencyTypeList({ currencyID, ECurrencyClientId.QuantumKey, ECurrencyClientId.MandelCoins, ECurrencyClientId.UnbindDiamond }, self)
    end
end

function StoreMainPanel:CheckTimeValid(value)
    if value.ThemeOnlineTime == nil or value.ThemeOfflineTime == nil then
        return false
    end
    local remainOfflineTime = Facade.ClockManager:GetLocalTimestamp() - value.ThemeOnlineTime
    local remainOnlineTime = Facade.ClockManager:GetLocalTimestamp() - value.ThemeOfflineTime
    if remainOfflineTime < 0 and remainOnlineTime > 0 then -- 说明在时间范围内
        return true
    end
    return false
end

return StoreMainPanel
