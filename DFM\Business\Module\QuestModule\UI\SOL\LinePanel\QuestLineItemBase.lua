----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMQuest)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class QuestLineItemBase : LuaUIBaseView
local QuestLineItemBase = ui("QuestLineItemBase")
local USlateBlueprintLibrary = import "SlateBlueprintLibrary"
local FAnchors = import "Anchors"
local UGPInputHelper = import "GPInputHelper"
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local EComp = CommonWidgetConfig.EIVWarehouseTempComponent

function QuestLineItemBase:Ctor()
    self._questItemWidth = 100
    self._questItemHeight = 0

    self._wtNameText = self:Wnd("wtNameText", UITextBlock)
    self._wtShowItem = self:Wnd("wtShowItem", UIWidgetBase)

    self._wtSizeBox = self:Wnd("SizeBox_Normal", UIWidgetBase)
    self._wtCPL_Lock = self:Wnd("CPL_Lock", UIWidgetBase)

    self._wtQuestIDText = self:Wnd("QuestIDText", UITextBlock)

    self._wtDFVerticalBox_QuestNeed = self:Wnd("DFVerticalBox_QuestNeed", UIWidgetBase)

    self._wtTipsAnchor = UIUtil.WndTipsAnchor(self, "DFTipsAnchor_289", self._OnShowTips, self._OnHideTips)

    self._wtBtn = self:Wnd("DFButton_67", UIButton)
    self._wtBtn:Event("OnClicked", self._OnClicked, self)

    self._questInfo = nil
    self._relativePos = nil
    self._questLineComponentIns = nil
    self._importantRewardItem = nil
end

function QuestLineItemBase:_AddEventListener()
    self:AddLuaEvent(Module.Quest.Config.evtJumpToQuestLineItem, self._OnJumpToQuestLineItem, self)
end

function QuestLineItemBase:_RemoveEventListener()
    self:RemoveAllLuaEvent()
end

function QuestLineItemBase:_OnJumpToQuestLineItem(questId)
    if self._questInfo and self._questInfo.id == questId then
        self:PlayAnimation(self.WBP_TaskChapterDetail_Item_loop, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    end

    if self._questInfo and self._questInfo.id == Module.Quest.Field.jumpToQuestInfo.id then
        Module.Quest.Config.evtQuestLinePanelJumpedFinish:Invoke(self)
    end
end

function QuestLineItemBase:_OnClicked()
    Module.Quest.Config.evtQuestJumpToDetailPanel:Invoke(self._questInfo.id)
end


function QuestLineItemBase:_OnShowTips()
    self:_OnHideTips()
    self._tipsHandle = Module.CommonTips:ShowAssembledTips(self._tipsText, self._wtTipsAnchor)
end

function QuestLineItemBase:_OnHideTips()
    if self._tipsHandle then
        Module.CommonTips:RemoveAssembledTips(self._tipsHandle, self._wtTipsAnchor)
        self._tipsHandle = nil
    end
end

------------------------------------ Override function ------------------------------------
function QuestLineItemBase:OnInitExtraData(questInfo)
    self._questInfo = questInfo
    self._questItemWidth = 100
    self._questItemHeight = 0
    self:_UpdateLineData()
end

function QuestLineItemBase:RefreshData(questInfo)
    self._questInfo = questInfo
    self._questItemWidth = 150
    self._questItemHeight = 0
    self:_UpdateLineData()
end

-- UI打开时触发
function QuestLineItemBase:OnOpen()
    self:_AddEventListener()
end

function QuestLineItemBase:OnClose()
    Facade.LuaFramingManager:CancelAllFrameTasks(self)
    self:_RemoveEventListener()
    Facade.UIManager:ClearSubUIByParent(self, self._wtShowItem)
end

function QuestLineItemBase:OnShow()
end

function QuestLineItemBase:OnHide()
    Facade.LuaFramingManager:CancelAllFrameTasks(self)
end

------------------------------------ Private function ------------------------------------

function QuestLineItemBase:_UpdateLineData()
    self._wtNameText:SetText(self._questInfo.name)

    self._wtDFVerticalBox_QuestNeed:Collapsed()

    if self._questInfo.type == QuestType.Mission then
        self:SetSignalColor(0)
    elseif self._questInfo.type == QuestType.ImportantQuest then
        self:SetSignalColor(2)
    elseif self._questInfo.type == QuestType.Branch then
        self:SetSignalColor(1)
    elseif self._questInfo.type == QuestType.SeasonQuest then
        
        local questlineInfo = Server.QuestServer:GetSeasonLineData(self._questInfo._seasonLineID)
        if questlineInfo then
            if questlineInfo:IsMainGroup(self._questInfo._seasonStageID, self._questInfo._seasonGroupID) then
                self:SetSignalColor(3)
            else
                self:SetSignalColor(4)
            end
        end
        
    end

    if IsInEditor() then
        self._wtQuestIDText:SelfHitTestInvisible()
        self._wtQuestIDText:SetText(self._questInfo.id)
    else
        self._wtQuestIDText:Collapsed()
    end

    self._wtShowItem:Collapsed()

    Facade.UIManager:RemoveSubUIByParent(self, self._wtShowItem)

    self._importantRewardItem = self._questInfo:GetQuestImportantReward()
    if self._importantRewardItem then
        self._wtShowItem:SelfHitTestInvisible()
        local weakInst = Facade.UIManager:AddSubUI(self, UIName2ID.IVCommonItemTemplate, self._wtShowItem)
        local uiInst = getfromweak(weakInst)
        if uiInst then
            uiInst:SetRootSize(140, 140)
            uiInst:InitItem(self._importantRewardItem)
            uiInst:SetCppValue("bPreciseClick", false)
            if self._questInfo.state >= QuestState.Rewarded then
                uiInst:SetAlreadyGetState()
                uiInst:EnableComponent(EComp.GetMask, true)
            else
                uiInst:EnableComponent(EComp.GetMask, false)
            end
            -- 调用IVCommonItemTemplate设置详情页上报数据的界面id
            uiInst:SetItemDetailFromUIID(LogAnalysisTool.EItemDetailFromUIType.TaskQuest)
        end
    end

    local rewardedPreQuestCnt = 0
    for _, preId in ipairs(self._questInfo.preQuestIdList) do
        if Server.QuestServer:IsQuestRewarded(preId) then
            rewardedPreQuestCnt = rewardedPreQuestCnt + 1
        end
    end
    self.bIsPreQuestReward = rewardedPreQuestCnt == #self._questInfo.preQuestIdList and true or false
    --未解锁状态
    if self._questInfo.state == QuestState.Locked or self._questInfo.state == QuestState.Unread then
        self:SetCppValue("bSwallowClick", false)
        self:SetCppValue("bHandleClick", false)

        --前置任务
        if #self._questInfo.preQuestIdList > 1 then
            self._tipsText = {}
            for _, id in ipairs(self._questInfo.preQuestIdList) do
                local preQuset = Server.QuestServer:GetQuestInfoById(id)
                if preQuset then
                    local tip = string.format(Module.Quest.Config.Loc.QuestNeedPre_Mission, preQuset.name)
                    if preQuset.type == QuestType.Mission then
                        tip = string.format(Module.Quest.Config.Loc.QuestNeedPre_Mission, preQuset.name)
                    elseif preQuset.type == QuestType.ImportantQuest then
                        tip = string.format(Module.Quest.Config.Loc.QuestNeedPre_Important, preQuset.name)
                    elseif preQuset.type == QuestType.Branch then
                        tip = string.format(Module.Quest.Config.Loc.QuestNeedPre_Branch, preQuset.name)
                    elseif preQuset.type == QuestType.SeasonQuest then
                        tip = string.format(Module.Quest.Config.Loc.QuestNeedPre_Season, preQuset.name)
                    end
                    table.insert(
                        self._tipsText,
                        {
                            id = UIName2ID.Assembled_CommonMessageTips_V2,
                            data = {
                                textContent = tip,
                                styleRowId = "C002"
                            }
                        }
                    )
                end
            end
            self._wtDFVerticalBox_QuestNeed:SelfHitTestInvisible()
        end

        self._questItemWidth = self._importantRewardItem ~= nil and 250 or 100
        self._questItemWidth = self._questItemWidth - 30
        self._questItemHeight = 0

        if self._questInfo.type == QuestType.SeasonQuest then
            local questlineInfo = Server.QuestServer:GetSeasonLineData(self._questInfo._seasonLineID)
            if questlineInfo then
                if questlineInfo:IsMainGroup(self._questInfo._seasonStageID, self._questInfo._seasonGroupID) then
                    self:SetItemState(self._importantRewardItem ~= nil and 3 or 2)
                else
                    self._wtBtn:Visible()
                    self:SetItemState(self._importantRewardItem ~= nil and 1 or 0)
                    self:SetIconType(4)
                    self._wtCPL_Lock:Collapsed()

                    self._questItemWidth = self._importantRewardItem ~= nil and 630 or 480
                    self._questItemWidth = self._questItemWidth - 30
                    self._questItemHeight = 0
                end
            else
                logerror("Quest doesn't contain seasonline info")
            end
        else
            self._wtBtn:Collapsed()
            self:SetItemState(self._importantRewardItem ~= nil and 3 or 2)
        end

    else
        self:SetCppValue("bSwallowClick", true)
        self:SetCppValue("bHandleClick", true)

        self._wtBtn:Visible()
        local nameLen = math.floor(#tostring(self._questInfo.name) / 3) - 2
        nameLen = math.min(nameLen, 5)
        local len  = #tostring(self._questInfo.name) % 3
        self:SetItemState(self._importantRewardItem ~= nil and 1 or 0)
        self._questItemWidth = self._importantRewardItem ~= nil and 630 or 480
        self._questItemWidth = self._questItemWidth - 30
        self._questItemHeight = 0
        if self._questInfo.state == QuestState.Rewarded then
            self:SetIconType(0)
        elseif self._questInfo.state == QuestState.Completed then
            self:SetIconType(1)
        elseif self._questInfo.state == QuestState.Accepted then
            self:SetIconType(3)
        else
            -- Unaccepted
            if Server.QuestServer:IsQuestAcceptable(self._questInfo) then
                self:SetIconType(2)
            else
                self:SetIconType(4)
            end
        end
    end
end

------------------------------------ Public function ------------------------------------
-- 初始化任务并构建子任务节点
function QuestLineItemBase:InitLineItemData(questInfo, relativePos)
    self._relativePos = relativePos
    self:OnInitExtraData(questInfo)
    Facade.LuaFramingManager:RegisterFrameTask(self.FrameTask, self)
end

function QuestLineItemBase:FrameTask()
    self:UpdateChildLineItem()
end

-- 构建子任务节点
function QuestLineItemBase:UpdateChildLineItem()
    if self._questInfo == nil then
        logerror(" QuestLineItemBase UpdateChildLineItem : questInfo is nil ")
        return
    end

    local OnCreateSubUIFinished = function(uiIns)
        -- 资源异步加载过程中释放掉Handle，不能回调回来
        if self:IsRelease() then
            return
        end
        self._questLineComponentIns = uiIns
        local questComposlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(self._questLineComponentIns)
        self:_UpdateQuestCompoSlotLayout(questComposlot)
        self._questLineComponentIns:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self._questLineComponentIns:UpdateLineComponent()
        Module.Quest.Field:UpdateQuestLineContentSize({X = self:_GetChildPos().X, Y = self:_GetChildPos().Y})
        -- print(" xhz >>>>>>>>>>>>>>>>> ", self._questInfo.name, self:_GetChildPos().X, self:_GetChildPos().Y)
        -- Module.Quest.Field:UpdateQuestLineContentSize({X = self:_GetChildPos().X, Y = self:_GetChildPos().Y})
        Module.Quest.Field:SetCurQuestLineItemPos(self._questInfo.id, self:_GetSelfPos())

        Module.Quest:QuestLineCreateEndToJump()
    end
    self.postQuestNum = #self._questInfo:GetPostQuests()
    local navId = nil
    if self.postQuestNum == 0 then
        navId = UIName2ID.QuestLineOneComponent
    elseif self.postQuestNum == 1 then
        navId = UIName2ID.QuestLineOneComponent
    elseif self.postQuestNum == 2 then
        navId = UIName2ID.QuestLineTwoComponent
    elseif self.postQuestNum == 3 then
        navId = UIName2ID.QuestLineThreeComponent
    elseif self.postQuestNum == 4 then
        navId = UIName2ID.QuestLineFourComponent
    end

    self:_CommonSetContentSubUI(navId, OnCreateSubUIFinished)

    Module.Quest.Field:UpdateQuestLineContentSize({X = self:_GetChildPos().X, Y = self:_GetChildPos().Y})
end

function QuestLineItemBase:_CommonSetContentSubUI(uiId, setFinishCallback)
    -- 资源加载完成回调
    local function fLoadFinCallback(mapPath2ResIns)
        local weakUiIns, instanceID =
            Facade.UIManager:AddSubUI(
            Module.Quest.Field:GetQuestLinePanelHandle(),
            uiId,
            Module.Quest.Field:GetQuestLinePanelItemObject(),
            nil,
            self._questInfo,
            self.postQuestNum
        )
        Module.Quest.Field.lineItemInstanceId[self._questInfo.id] = instanceID
        if weakUiIns and getfromweak(weakUiIns) then
            if setFinishCallback then
                setFinishCallback(getfromweak(weakUiIns))
            end
        else
            logerror("QuestLineItemBase:_CommonSetContentSubUI:UIManager:AddSubUI: weakUiIns is nil!")
        end
    end

    if Facade.UIManager:CheckUIHasBeenLoaded(uiId) then
        local weakUiIns, instanceID
        instanceID = Module.Quest.Field.lineItemInstanceId[self._questInfo.id]
        if instanceID then
            weakUiIns = Facade.UIManager:GetSubUI(Module.Quest.Field:GetQuestLinePanelHandle(), uiId, instanceID)
            if weakUiIns and getfromweak(weakUiIns) then
                local uiIns = getfromweak(weakUiIns)
                uiIns:RefreshData(self._questInfo, self.postQuestNum)
            end
        end
        if weakUiIns == nil then
            weakUiIns, instanceID =
                Facade.UIManager:AddSubUI(
                Module.Quest.Field:GetQuestLinePanelHandle(),
                uiId,
                Module.Quest.Field:GetQuestLinePanelItemObject(),
                nil,
                self._questInfo,
                self.postQuestNum
            )
            Module.Quest.Field.lineItemInstanceId[self._questInfo.id] = instanceID
        end

        if weakUiIns and getfromweak(weakUiIns) then
            if setFinishCallback then
                setFinishCallback(getfromweak(weakUiIns))
            end
        else
            logerror("QuestLineItemBase:_CommonSetContentSubUI:UIManager:AddSubUI: weakUiIns is nil!")
        end
    else
        local uiBatchId = Facade.UIManager:AsyncLoadUIResOnly(uiId, fLoadFinCallback, nil)
    end
end

function QuestLineItemBase:_ResetRelativePos()
    self._relativePos = nil
end

function QuestLineItemBase:_SetRelativePos(posX, posY)
    self._relativePos = ({X = posX, Y = posY})
end

function QuestLineItemBase:_GetRelativePos()
    return self._relativePos ~= nil and self._relativePos or ({X = 0, Y = 0})
end

function QuestLineItemBase:_GetSelfPos()
    local pos = self:_GetRelativePos()
    return FVector2D(pos.X + self._questItemWidth / 2, pos.Y)
end

function QuestLineItemBase:_GetChildPos()
    local pos = self:_GetRelativePos()
    return FVector2D(pos.X + self._questItemWidth + 50, pos.Y)
end

local itemAnchor = FAnchors()
itemAnchor.Minimum = FVector2D(0, 0.5)
itemAnchor.Maximum = FVector2D(0, 0.5)
function QuestLineItemBase:_UpdateQuestCompoSlotLayout(canvasSlot)
    canvasSlot:SetAnchors(itemAnchor)
    canvasSlot:SetAlignment(FVector2D(0, 0.5))
    canvasSlot:SetPosition(self:_GetChildPos())
    -- canvasSlot:SetAutoSize(true)
end

return QuestLineItemBase
