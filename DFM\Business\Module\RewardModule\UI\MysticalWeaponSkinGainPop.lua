----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMReward)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class MysticalWeaponSkinGainPop : LuaUIBaseView
local RewardBaseView = require "DFM.Business.Module.RewardModule.UI.RewardBaseView"
local MysticalWeaponSkinGainPop = ui("MysticalWeaponSkinGainPop", RewardBaseView)
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local CollectionLogic = require "DFM.Business.Module.CollectionModule.Logic.CollectionLogic"
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local EGPInputModeType = import "EGPInputModeType"
local UHallSceneBGManager = import "HallSceneBGManager"
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
local LiteCommonDownload =  require "DFM.Business.Module.LitePackageModule.UI.LiteCommonDownload"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPInputType = import "EGPInputType"
local UGPInputDelegates = import "GPInputDelegates"


function MysticalWeaponSkinGainPop:Ctor()
    self._wtNicknameTxt = self:Wnd("wtNicknameTxt", UITextBlock)
    self._wtRateTxt = self:Wnd("wtRateTxt", UITextBlock)
    self._wtIDBg = self:Wnd("wtIDBg", UIImage)
    self._wtIDTxt = self:Wnd("wtIDTxt", UITextBlock)
    self._wtGradeTxt = self:Wnd("wtGradeTxt", UITextBlock)
    self._wtSkinNameTxt = self:Wnd("wtSkinNameTxt", UITextBlock)
    self._wtWearTxt = self:Wnd("wtWearTxt", UITextBlock)
    self._wtExceedPercentStartTxt = self:Wnd("wtExceedPercentStartTxt", UITextBlock)
    self._wtExceedPercentTxt = self:Wnd("wtExceedPercentTxt", UITextBlock)
    self._wtExceedPercentEndTxt = self:Wnd("wtExceedPercentEndTxt", UITextBlock)
    self._wtSkipBtn = self:Wnd("wtSkipBtn", DFCommonButtonOnly)
    self._wtSkipBtn:Event("OnClicked", self._OnSkipBtnClick, self)
    self._wtApplyBtn = self:Wnd("wtApplyBtn", CommonButton)
    self._wtApplyBtn:Event("OnClicked", self._ApplyWeaponSkin, self)
    -- 截屏分享相关
    self._wtShareBtn = self:Wnd("wtShareBtn", DFCommonButtonOnly)
    self._wtShareBtn:Event("OnClicked", self.OnShareClick,self)
    self._wtShareBtn:Collapsed()
    self._closeClickCount = -1
    self._wtDFCanvasPanel_Bottom = self:Wnd("DFCanvasPanel_Bottom", UIWidgetBase)
    if Module.ExpansionPackCoordinator:IsSupportLitePackage() then
        self._wtCommonDownload = self:Wnd("wtCommonDownload", LiteCommonDownload)
    end
    if not hasdestroy(self._wtCommonDownload) then
        self._wtCommonDownload:Collapsed()
    end

    self._wtSubUIRoot = self:Wnd("wtSubUIRoot", UIWidgetBase)
    self._subUIIns = nil
end


function MysticalWeaponSkinGainPop:OnInitExtraData(weaponskinItem)
    self._item = weaponskinItem
    Facade.UIManager:CommitTransition(true) --防止场景未加载完成时的穿帮
end


function MysticalWeaponSkinGainPop:OnOpen()
    self:_AddListeners()
    Module.Reward.Config.Events.evtOpenWeaponSkinGainPop:Invoke(true)
end

function MysticalWeaponSkinGainPop:OnClose()
    self:RemoveJumpInputAction()
    self:RemoveAllLuaEvent()
    self:_StopPercentEffect()
    Module.Reward.Config.Events.evtOpenWeaponSkinGainPop:Invoke(false)
    if isvalid(self._displayCtrlActor) then
        self._displayCtrlActor.HallWeaponDisplaySequenceComponent.OnSeqFinished:Remove(self._OnSeqFinished, self)
    end
    if self._bExecuteClose ~= true then
        self._bExecuteClose = true
        Module.Reward:ShowNextRewards()
    end
end


function MysticalWeaponSkinGainPop:OnShowBegin()
    self._wtDFCanvasPanel_Bottom:Collapsed()
    if Facade.GameFlowManager:GetCurrentSubStage() == ESubStage.WeaponDisplay then
        self:_OnRefreshModel(ESubStage.WeaponDisplay)
    end
    if not self._inputTypeChangedHandle then 
        self._inputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._OnInputTypeChanged, self))
    end
    self:AddJumpInputAction()
    self:_EnableGamepadFeature()
end


function MysticalWeaponSkinGainPop:OnHideBegin()
    if self._inputTypeChangedHandle then
        UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._inputTypeChangedHandle)
        self._inputTypeChangedHandle = nil
    end
    self:_DisableGamepadFeature()
end


function MysticalWeaponSkinGainPop:OnShow()
    self:SetCPPValue("WantedInputMode", EGPInputModeType.GameAndUI)
end


function MysticalWeaponSkinGainPop:OnHide()
    if self._skipHandle then
        self:RemoveInputActionBinding(self._skipHandle)
        self._skipHandle = nil
    end
    if isvalid(self._displayCtrlActor) and isvalid(self._item) then
        self._displayCtrlActor.WeaponSkinSceneDisplayComponent.OnLoadSublevelGroupDone:Remove(self._OnLoadSublevelGroupDone, self)
    end
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.WeaponDisplay, "ResetGeneralLevelSequence")
    self._subUIIns = nil
end

function MysticalWeaponSkinGainPop:OnAnimFinished(anim)
    if anim == self.WBP_Store_GetCollectionSkin_jp_in or anim == self.WBP_Store_GetCollectionSkin_yp_in then
        if self._closeClickCount < 1 then
            self._closeClickCount = self._closeClickCount + 1
        end
    elseif anim == self.WBP_Store_GetCollectionSkin_jp_out or anim == self.WBP_Store_GetCollectionSkin_yp_out then
        if self._bExecuteClose ~= true then
            self._bExecuteClose = true
            Module.Reward:ShowNextRewards(self._bTabPressed == true)
        end
    end

    if anim == self.WBP_Store_GetCollectionSkin_jp_in then
        if self._subUIIns then
            self._subUIIns:SetIsPreminum(true)
            self._subUIIns:PlayAnim_JP()
        end
    end
    
end


function MysticalWeaponSkinGainPop:_AddListeners()
    self:AddLuaEvent(Module.IrisSafeHouse.Config.evtTabBackToSafeHouseHD, self._OnTabPressed, self)
    self:AddLuaEvent(Server.GunsmithServer.Events.evtCSWAssemblyApplySkinRes, self._OnWeaponSkinApplied, self)
    self:AddLuaEvent(LuaGlobalEvents.evtSceneLoaded, self._OnRefreshModel, self)
    self:AddLuaEvent(Module.Reward.Config.Events.evtMysticalUnboxingAudio, self._OnMysticalUnboxingAudio, self)
    self:AddLuaEvent(Module.Share.Config.Events.evtShareFlowFinish, self.OnShareFlowFinish, self)
    self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult,self._OnDownloadResult,self)
    self:AddLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged, self._OnPackStateChanged, self)
end



function MysticalWeaponSkinGainPop:_OnRefreshWidget()
    if self._closeClickCount < 1 then
        self._closeClickCount = self._closeClickCount + 1
    end
    self._wtNicknameTxt:SetText(Server.RoleInfoServer.nickName or "")
    self._wtSkinNameTxt:SetText(Module.Reward.Config.Loc.Unknown)
    self._wtRateTxt:SetText(Module.Reward.Config.Loc.Unknown)
    self._wtIDTxt:SetText(Module.Reward.Config.Loc.Unknown)
    self._wtGradeTxt:SetText(Module.Reward.Config.Loc.Unknown)
    self._wtWearTxt:SetText(Module.Reward.Config.Loc.Unknown)
    self._wtExceedPercentTxt:SetText("0%")
    self._wtSkipBtn:SetMainTitle(Module.Collection.Config.Loc.Skip)
    self._wtSkipBtn:SetIsEnabled(true)
    self._wtApplyBtn:Collapsed()
    self._wtApplyBtn:SetMainTitle(Module.Collection.Config.Loc.ApplyAppearance)
    self._wtShareBtn:Collapsed()
    if isvalid(self._item) then
        self._wtSkinNameTxt:SetText(self._item.name)
        self._wtIDBg:SetColorAndOpacity(ItemConfigTool.GetItemQualityLinearColor(self._item.quality))
        self._wtRateTxt:SetColorAndOpacity(ItemConfigTool.GetItemQualitySlateColor(self._item.quality))
        self._wtGradeTxt:SetColorAndOpacity(ItemConfigTool.GetItemQualitySlateColor(self._item.quality))
        self._wtWearTxt:SetColorAndOpacity(ItemConfigTool.GetItemQualitySlateColor(self._item.quality))
        self._wtExceedPercentStartTxt:SetColorAndOpacity(ItemConfigTool.GetItemQualitySlateColor(self._item.quality))
        self._wtExceedPercentTxt:SetColorAndOpacity(ItemConfigTool.GetItemQualitySlateColor(self._item.quality))
        self._wtExceedPercentEndTxt:SetColorAndOpacity(ItemConfigTool.GetItemQualitySlateColor(self._item.quality))
        local mysticalInfo = self._item:GetRawPropInfo() and self._item:GetRawPropInfo().mystical_skin_data or nil
        if mysticalInfo then
            if mysticalInfo.unique_no then
                self._wtIDTxt:SetText(mysticalInfo.unique_no)
            end
            if mysticalInfo.appearance then
                self:SetType(self:IsPreminum() and 1 or 0)
                if Module.Inventory.Config.MysticalSkinRarityTxtMapping[self._ratityID] then
                    local rateTxt = Module.Inventory.Config.MysticalSkinRarityTxtMapping[self._ratityID]
                    self._wtRateTxt:SetText(rateTxt)
                end
            end
            if mysticalInfo.wear then
                local wearScale = ItemConfigTool.GetMysticalWearRate()
                local wearScaleDec = #tostring(wearScale) - 1
                local wearTxt = string.format("%."..wearScaleDec.."f", mysticalInfo.wear/wearScale)
                local gradeTxt = ItemConfigTool.GetMysticalWearConfig(mysticalInfo.wear) or ""
                self._wtGradeTxt:SetText(gradeTxt)
                self._wtWearTxt:SetText(wearTxt)
            end
        end
        if not IsHD() then
            if ItemHelperTool.IsMysticalSkin(self._item.id) then
                Module.Share:FuncPointUnLock(SwitchSystemID.SubShareMetaphysicalGunSkin, self._wtShareBtn)
            else
                Module.Share:FuncPointUnLock(self._item.quality >= ItemConfig.EWeaponSkinQualityType.Purple and SwitchSystemID.SubShareAdvancedGunSkin or SwitchSystemID.SubShareSObtainLowGunSkin, self._wtShareBtn)
            end
        end
        self._weaponId = CollectionLogic.GetBaseWeaponIdFromSkinId(self._item.id)
        self._wtApplyBtn:SetIsEnabled(true)
        if self._weaponId ~= nil then
            if CollectionLogic.CheckIfSkinAppliedOnWeapon(self._item.id, self._item.gid) then
                self._wtApplyBtn:SetMainTitle(Module.Collection.Config.Loc.AppearanceApplied)
                self._wtApplyBtn:SetIsEnabled(false)
            else
                self._wtApplyBtn:SetMainTitle(Module.Collection.Config.Loc.ApplyAppearance)
            end
        else
            self._wtApplyBtn:SetMainTitle(Module.Collection.Config.Loc.NotUnlocked)
            self._wtApplyBtn:SetIsEnabled(false)
        end
        self._wtApplyBtn:SelfHitTestInvisible()
        self:_RefreshDownloadBtn()
    end
    if self._closeClickCount < 1 then
        self:PlayAnimation(self:IsPreminum() and self.WBP_Store_GetCollectionSkin_jp_in or self.WBP_Store_GetCollectionSkin_yp_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    end
end

function MysticalWeaponSkinGainPop:_OnDownloadResult(moduleName, bDownloaded, errorCode)
    local moduleKey = CollectionLogic.GetDownloadModuleKeyByItemId(self._item and self._item.id or nil)
    if moduleName == moduleKey then
        self:_OnRefreshModel(ESubStage.WeaponDisplay)
        self:_RefreshDownloadBtn()
    end
end

function MysticalWeaponSkinGainPop:_OnPackStateChanged(moduleName, packState)
    local isSuccess = packState == Module.ExpansionPackCoordinator.Config.PackState.Installed
    self:_OnDownloadResult(moduleName, isSuccess, 0)
end

function MysticalWeaponSkinGainPop:_RefreshDownloadBtn()
    if not hasdestroy(self._wtCommonDownload) then
        local moduleKey = CollectionLogic.GetDownloadModuleKeyByItemId(self._item and self._item.id or nil)
        local bDownloaded = Module.ExpansionPackCoordinator:IsDownloadedByModuleName(moduleKey)
        if not bDownloaded and isvalid(self._item) then
            self._wtCommonDownload:InitModuleKey(moduleKey)
            self._wtCommonDownload:Visible()
        else
            self._wtCommonDownload:Collapsed()
        end
    end
end



function MysticalWeaponSkinGainPop:_OnRefreshModel(curSubStageType)
    if not curSubStageType or curSubStageType == ESubStage.WeaponDisplay then
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.WeaponDisplay, "ResetWeapon")
        if isvalid(self._item) then
            if not isvalid(self._displayCtrlActor) then
                self._displayCtrlActor = Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.WeaponDisplay)
            end
            if isvalid(self._displayCtrlActor) then
                self._displayCtrlActor.WeaponSkinSceneDisplayComponent.OnLoadSublevelGroupDone:Remove(self._OnLoadSublevelGroupDone, self)
                self._displayCtrlActor.WeaponSkinSceneDisplayComponent.OnLoadSublevelGroupDone:Add(self._OnLoadSublevelGroupDone, self)
                Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.WeaponDisplay, "DisplayWeaponSkinScene",self._item.id) --动态加载完关卡再显示界面
            end 
        else
            self._wtDFCanvasPanel_Bottom:Visible()
            self:_OnRefreshWidget()
            Facade.SoundManager:PlayUIAudioEvent("Ul_Unboxing_Mandel_Collection_Popup")
        end
    end
end

local mysticalId2WBP = {
    [28010050271] = "WBP_Reward_CollectionSkin_Lengjinggongshi",
    [28020050001] = "WBP_Reward_CollectionSkin_Medusa",
    [28010050272] = "WBP_Reward_CollectionSkin_Dianwangaoshou",
    [28010050273] = "WBP_Reward_CollectionSkin_Mingyunwangpai",
    [28010050274] = "WBP_Reward_CollectionSkin_Wangzhezhijian",
    [28010050275] = "WBP_Reward_CollectionSkin_TengLongqixiangganying",
    [28010050276] = "WBP_Reward_CollectionSkin_AUGqixiangganying",
}

--场景特异化区分
function MysticalWeaponSkinGainPop:_ShowSpecicalDiff(bIsPreminum)
    if not UHallSceneBGManager then
        return
    end

    local HallSceneBGManager = UHallSceneBGManager.Get(GetWorld())
    if not HallSceneBGManager then
       return
    end

    local bg1ActorName = "WeaponDisplayBoardBG"
    local bg2ActorName = "WeaponDisplayBoardBG1"
    
    -- Facade.HallSceneManager:SetHighLevelBackgroundImage(self._item.id.."_1",bg1ActorName)
    -- Facade.HallSceneManager:SetHighLevelBackgroundImage(self._item.id.."_2",bg2ActorName)
    HallSceneBGManager:SetRegisteredActorVisibilityWithChildren(bg2ActorName,not bIsPreminum)

    if bIsPreminum then
        local sceneSQ1Str =  tostring(self._item.id).."_1"
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.WeaponDisplay, "PlaySequenceByRowName",  sceneSQ1Str)
        self._displayCtrlActor = Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.WeaponDisplay)
        if isvalid(self._displayCtrlActor) then 
            self._displayCtrlActor.GeneralLevelSequenceCtrlComponent.OnGeneralSeqFinished:Add(self._OnGeneralSeqFinished, self)
        end
    end

    ---特有ui展示
    Facade.UIManager:ClearSubUIByParent(self, self._wtSubUIRoot)
    local subUIKey = mysticalId2WBP[tonumber(self._item.id)]
    if not subUIKey then
        logerror(string.format("[MysticalWeaponSkinGainPop][_ShowSpecicalDiff]not found subUI, id = %s",self._item.id))
        subUIKey = nil
    end
    local subUI = subUIKey and UIName2ID[subUIKey]
    if subUI then
        local ItemIns, instanceId = Facade.UIManager:AddSubUI(self, subUI , self._wtSubUIRoot)
        if instanceId == 0 then
            logerror(string.format("[MysticalWeaponSkinGainPop][_ShowSpecicalDiff] AddSubUI failed subUI, subUIKey = %s",subUIKey))
        end
        if ItemIns then
            ---@type MysticalWeaponSkinGainPopSubBase Description
            local itemWidget = getfromweak(ItemIns)
            self._subUIIns = itemWidget
            if self._subUIIns then
                if self._closeClickCount < 1 then
                    self._subUIIns:SetIsPreminum(false)
                else
                    self._subUIIns:SetIsPreminum(bIsPreminum)
                end
            end
        end
    else
        logerror(string.format("[MysticalWeaponSkinGainPop][_ShowSpecicalDiff]：not found subUI, subUIKey = %s",subUIKey))
    end
end

function MysticalWeaponSkinGainPop:_OnSeqFinished()
    if isvalid(self._weaponDesc) and isvalid(self._item) then
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.WeaponDisplay, "SetDisplayWeaponAutoBoundAdapter", self._weaponDesc,false,false)
    end
end

function MysticalWeaponSkinGainPop:_OnGeneralSeqFinished()
    local sceneSQ2Str =  tostring(self._item.id).."_2"
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.WeaponDisplay, "PlaySequenceByRowName", sceneSQ2Str)
end

function MysticalWeaponSkinGainPop:_OnSkipBtnClick()
    Facade.SoundManager:StopUIAudioEvent("Ul_Unboxing_Mandel_Collection_Bar")
    if self:IsPreminum() then
        Facade.SoundManager:StopUIAudioEvent("Ul_Unboxing_Mandel_Collection_Mythic")
        Facade.SoundManager:StopUIAudioEvent("Ul_Unboxing_Mandel_Collection_Color")
    else
        Facade.SoundManager:StopUIAudioEvent("Ul_Unboxing_Mandel_Collection_Legend")
    end
    self:_StopPercentEffect()
    if self._closeClickCount == 1 then
        self._closeClickCount = 2
        self:HandleTransition(true)
        Facade.SoundManager:StopUIAudioEvent("Ul_Unboxing_Mandel_Collection_Popup")
        self:PlayAnimation(self:IsPreminum() and self.WBP_Store_GetCollectionSkin_jp_out or self.WBP_Store_GetCollectionSkin_yp_out, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    elseif self._closeClickCount == 0 then
        self._closeClickCount = 1
        self:SkipAnimation(self:IsPreminum() and self.WBP_Store_GetCollectionSkin_jp_in or self.WBP_Store_GetCollectionSkin_yp_in)
    end
end


function MysticalWeaponSkinGainPop:_ApplyWeaponSkin()
    if self._item ~= nil and self._item.itemMainType == EItemType.WeaponSkin then
        CollectionLogic.ApplyWeaponSkin(self._weaponId, self._item.id, self._item.gid)   
    else
        Module.CommonTips:ShowSimpleTip(Module.Collection.Config.Loc.UnlockTip)
    end
end


function MysticalWeaponSkinGainPop:_OnWeaponSkinApplied(res)
    if not self._bApplied then
        if self._item ~= nil then
            if CollectionLogic.CheckIfSkinAppliedOnWeapon(self._item.id, self._item.gid, res) then
                self._bApplied = true
                Module.CommonTips:ShowSimpleTip(Module.Collection.Config.Loc.AppearanceApplied)
                self._wtApplyBtn:SetMainTitle(Module.Collection.Config.Loc.AppearanceApplied)
                self._wtApplyBtn:SetIsEnabled(false)
            else
                Module.CommonTips:ShowSimpleTip(Module.Collection.Config.Loc.FailedToUse)
            end
        end
    end
end


function MysticalWeaponSkinGainPop:_OnMysticalUnboxingAudio(stage)
    if stage == 1 then
        Facade.SoundManager:PlayUIAudioEvent("Ul_Unboxing_Mandel_Collection_Bar")
        self:_StartPercentEffect()
    elseif stage == 2 then
        Facade.SoundManager:StopUIAudioEvent("Ul_Unboxing_Mandel_Collection_Bar")
    elseif stage == 3 then
        if self:IsPreminum() then
            Facade.SoundManager:PlayUIAudioEvent("Ul_Unboxing_Mandel_Collection_Mythic")
            Facade.SoundManager:PlayUIAudioEvent("Ul_Unboxing_Mandel_Collection_Color")
        else
            Facade.SoundManager:PlayUIAudioEvent("Ul_Unboxing_Mandel_Collection_Legend")
        end
    end
end

function MysticalWeaponSkinGainPop:OnShareFlowFinish()
    self._wtDFCanvasPanel_Bottom:SelfHitTestInvisible()
    self._wtShareBtn:Visible()
end


function MysticalWeaponSkinGainPop:_StartPercentEffect()
    if isvalid(self._item) then
        self._currentPercent = 0
        self._targetPercent = self._item.exceedPercent or 0
        self._percentEffectTimerHandle = Timer:NewIns(0.02, 0)
        self._percentEffectTimerHandle:AddListener(self._PercentEffectTick, self)
        self._percentEffectTimerHandle:Start()
    end
end

function MysticalWeaponSkinGainPop:_PercentEffectTick()
    if self._currentPercent < self._targetPercent then
        local exceedPercentTxt = string.sub(tostring((self._currentPercent or 0) * 100),1,4)
        self._wtExceedPercentTxt:SetText(string.format("%s%%", exceedPercentTxt))
        self._currentPercent = self._currentPercent + (0.02/1*self._targetPercent)
    else
        self:_StopPercentEffect()
    end
end

function MysticalWeaponSkinGainPop:_StopPercentEffect()
    local exceedPercentTxt = string.sub(tostring((self._targetPercent or 0) * 100),1,4)
    self._wtExceedPercentTxt:SetText(string.format("%s%%", exceedPercentTxt))
    if isvalid(self._percentEffectTimerHandle) then
        self._percentEffectTimerHandle:Release()
    end  
end


function MysticalWeaponSkinGainPop:OnNavBack()
    self:_OnSkipBtnClick()
    return true
end

function MysticalWeaponSkinGainPop:_OnTabPressed()
    self._bTabPressed = true
    self:SkipAnimation(self:IsPreminum() and self.WBP_Store_GetCollectionSkin_jp_in or self.WBP_Store_GetCollectionSkin_yp_in)
    Facade.SoundManager:StopUIAudioEvent("Ul_Unboxing_Mandel_Collection_Popup")
    Facade.SoundManager:StopUIAudioEvent("Ul_Unboxing_Mandel_Collection_Bar")
    if self:IsPreminum() then
        Facade.SoundManager:StopUIAudioEvent("Ul_Unboxing_Mandel_Collection_Mythic")
        Facade.SoundManager:StopUIAudioEvent("Ul_Unboxing_Mandel_Collection_Color")
    else
        Facade.SoundManager:StopUIAudioEvent("Ul_Unboxing_Mandel_Collection_Legend")
    end
    self:PlayAnimation((self:IsPreminum()) and self.WBP_Store_GetCollectionSkin_jp_out or self.WBP_Store_GetCollectionSkin_yp_out, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
end


--------------------------------------- 界面分享相关 ------------------------------------

function MysticalWeaponSkinGainPop:PreScreenshotShare()
    -- Module.CommonBar:SetTopBarVisible(false)
    self._wtDFCanvasPanel_Bottom:Collapsed()
    self._wtShareBtn:Collapsed()
end

function MysticalWeaponSkinGainPop:AfterScreenshotShare()
end

function MysticalWeaponSkinGainPop:OnShareClick()
    local PreScreenshotShare = CreateCallBack(function(self)
        self:PreScreenshotShare()
    end,self)

    local AfterScreenshotShare = CreateCallBack(function(self)
        self:AfterScreenshotShare()
    end,self)

    Module.Share:ReqShareWeapon(self._item.id, self._item, "MysticalWeaponSkinGainPop", PreScreenshotShare, AfterScreenshotShare, true)
end
--end
-----------------------------------------------------------------------


function MysticalWeaponSkinGainPop:_OnLoadSublevelGroupDone()
    loginfo("[MysticalWeaponSkinGainPop] _OnLoadSublevelGroupDone")
    local weaponDesc, partIndexs = WeaponHelperTool.GetWeaponDescriptionFromSkinID(self._item.id)
    self._weaponDesc = weaponDesc
    if isvalid(weaponDesc) then
        if self._item.gid ~= 0 then
            WeaponAssemblyTool.SetWeaponDescriptionSkinInfoFromPropInfo(weaponDesc, self._item:GetRawPropInfo())
        end
        self._isMelee = self._item.itemSubType == ItemConfig.EWeaponItemType.Melee
        local mysticalInfo = self._item:GetRawPropInfo() and self._item:GetRawPropInfo().mystical_skin_data or nil

        if mysticalInfo then
            local ratityID = ItemConfigTool.GetItemRatityID(mysticalInfo.appearance.id)
            self._ratityID = ratityID
        end
        self:_ShowSpecicalDiff(self:IsPreminum())

        if isvalid(self._displayCtrlActor) then 
            self._displayCtrlActor.HallWeaponDisplaySequenceComponent.OnSeqFinished:Add(self._OnSeqFinished, self)
        end
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.WeaponDisplay, "PlayWeaponCutSceneByIndex", weaponDesc, 0  , true)
        Facade.UIManager:CommitTransition(false)
    end

    self._wtDFCanvasPanel_Bottom:Visible()
    self:_OnRefreshWidget()
    Facade.SoundManager:PlayUIAudioEvent("Ul_Unboxing_Mandel_Collection_Popup")
end

function MysticalWeaponSkinGainPop:_OnInputTypeChanged(inputType)
    if IsHD() and inputType == EGPInputType.Gamepad then
        self:RemoveJumpInputAction()
        self:_EnableGamepadFeature()
    else
        self:_DisableGamepadFeature()
        self:AddJumpInputAction()
    end
end

--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
function MysticalWeaponSkinGainPop:_EnableGamepadFeature()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if self._wtSkipBtn then
        self._wtSkipBtn:SetDisplayInputAction("Reward_Continue_Gamepad", true, nil, true)
    end
    if self._wtApplyBtn then
        self._wtApplyBtn:SetDisplayInputAction("Common_ButtonLeft", true, nil, true)
    end
    -- 跳过按键响应
    if not self._skipHandle then
        self._skipHandle = self:AddInputActionBinding("Reward_Continue_Gamepad", EInputEvent.IE_Pressed, self._OnSkipBtnClick, self, EDisplayInputActionPriority.UI_Pop)
    end
    -- 应用按键响应
    if not self._applyHandle then
        self._applyHandle = self:AddInputActionBinding("Common_ButtonLeft", EInputEvent.IE_Pressed, self._ApplyWeaponSkin, self, EDisplayInputActionPriority.UI_Pop)
    end
    WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self, WidgetUtil.ENavConfigPriority.UI_Pop)
end

function MysticalWeaponSkinGainPop:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end
    if self._skipHandle then
        self:RemoveInputActionBinding(self._skipHandle)
    end
    if self._applyHandle then
        self:RemoveInputActionBinding(self._applyHandle)
    end
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    self._skipHandle = nil
    self._applyHandle = nil
end

function MysticalWeaponSkinGainPop:AddJumpInputAction()
    if IsHD() and not WidgetUtil.IsGamepad() then
        if not self._skipHandle then
            self._skipHandle = self:AddInputActionBinding(
                "JumpOver",
                EInputEvent.IE_Pressed,
                self._OnSkipBtnClick,
                self,
                EDisplayInputActionPriority.UI_Pop
            )
        end
    end
end

function MysticalWeaponSkinGainPop:RemoveJumpInputAction()
    if self._skipHandle then
        self:RemoveInputActionBinding(self._skipHandle)
        self._skipHandle = nil
    end
end

--- END MODIFICATION

function MysticalWeaponSkinGainPop:IsPreminum()
    return self._ratityID == 3 or self._ratityID == 4
end
return MysticalWeaponSkinGainPop
