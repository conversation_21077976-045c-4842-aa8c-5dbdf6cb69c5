----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRankingList)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class HistoryTitleSubItem : UIWidgetBase
local HistoryTitleSubItem = ui("HistoryTitleSubItem")


function HistoryTitleSubItem:Ctor()
    self._wtRoleInfo = self:Wnd("WBP_RoleInfo_TitleItem", CommonSocialTitleItem_Big)
    self._wtTxtBlock = self:Wnd("DFTextBlock_26", UITextBlock)
end

function HistoryTitleSubItem:RefreshTitleSubItem(playerName, titleId, adCode, ranking)
    self._wtTxtBlock:Collapsed()
    self._wtRoleInfo:SelfHitTestInvisible()
    self._wtRoleInfo:UpdateInfo(playerName, titleId, adCode, ranking)
end

function HistoryTitleSubItem:SetDesc(txt)
    self._wtTxtBlock:SetText(txt)
end

return HistoryTitleSubItem
