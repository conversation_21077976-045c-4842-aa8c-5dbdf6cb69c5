----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMQuest)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class QuestSeasonActionPhase : LuaUIBaseView

local QuestSeasonActionPhase = ui("QuestSeasonActionPhase")

function QuestSeasonActionPhase:Ctor()
    self._wtStageText = self:Wnd("DFTextBlock_129", UITextBlock)
    self._wtProgressText = self:Wnd("DFTextBlock_54", UITextBlock)
    self._wtBtn = self:Wnd("DFButton_67", UIButton)
    if self._wtBtn then
        self._wtBtn:Event("OnClicked", self._OnClicked, self)
        -- self._wtBtn:Event("OnHovered", self._OnHovered, self)
        -- self._wtBtn:Event("OnUnhovered", self._OnUnhovered, self)
    end
    self._wtAnimText = self:Wnd("DFTextBlock_74", UITextBlock)

    self._bisUnlock = false
    self._bIsSelected = false
    self._stageInfo = nil
    self._bCanPlayAnim = false
    self._bCanPlayUnlockAnim = false

    self._prevStarNum = 0
    self._curStarNum = 0
    self._timerHandle = nil

    self._wtArrow1 = self:Wnd("DFImage_37", UIWidgetBase)
    self._wtArrow2 = self:Wnd("DFImage", UIWidgetBase)

end

function QuestSeasonActionPhase:OnShow()
    self:AddLuaEvent(Server.QuestServer.Events.evtUpdateQuestState, self._OnQuestStateUpdate, self)
    self:AddLuaEvent(Module.Reward.Config.Events.evtQuestCloseRewardPanel, self._OnCloseRewardPanel, self)
    self:AddLuaEvent(Module.Quest.Config.evtQuestPlayPhaseUnlockAnim, self._OnPlayUnlockAnim, self)
end

function QuestSeasonActionPhase:OnHide()
    if self.curReddotProxy ~= nil then
        Module.ReddotTrie:UnRegisterDynamicReddot(EReddotTrieObserverType.Quest,EQuestDynamicDataType.EQuestSeasonReddot,self.curReddotProxy)
        self.curReddotProxy = nil
    end
    if self._timerHandle then
        self._timerHandle:Release()
        self._timerHandle = nil
    end
end

function QuestSeasonActionPhase:SetProgressText(gain,total)
    self._prevStarNum = gain
    self._wtProgressText:SetText(string.format(Module.Quest.Config.Loc.QuestSeasonStarProgress,gain,total))
end

function QuestSeasonActionPhase:SetIsUnlocked(bisUnlock)
    self._bisUnlock = bisUnlock
end

function  QuestSeasonActionPhase:SetStageInfo(stageInfo)
    self._stageInfo = stageInfo
    self._wtStageText:SetText("0" .. stageInfo.order)
    self:_RegisterReddot()
end

function QuestSeasonActionPhase:SetSelectedState(bIsSelected)
    self._bIsSelected = bIsSelected
    self:_RefreshView()
end

function QuestSeasonActionPhase:_OnClicked()
    Module.Quest.Config.evtQuestSeasonPhaseItemSelected:Invoke(self._stageInfo)
end

-- function QuestSeasonActionPhase:_OnHovered()
--     if self._bisUnlock then
--         self:SetType(4)
--     else
--         self:SetType(1)
--     end
-- end

-- function QuestSeasonActionPhase:_OnUnhovered()
--     self:SetSelectedState(self._bIsSelected)
-- end

function QuestSeasonActionPhase:_RegisterReddot()
    if self.curReddotProxy == nil then
        local fCheckReddot = function (id)
            local needRet = Server.QuestServer:GetNotifyQuestInSeasonStage(id)
            return needRet
        end
        self.curReddotProxy = Module.ReddotTrie:RegisterDynamicReddot(EReddotTrieObserverType.Quest,
            EQuestDynamicDataType.EQuestSeasonReddot,fCheckReddot,self._stageInfo.stageID,self)
    end
end

function QuestSeasonActionPhase:_OnQuestStateUpdate(questId)
    local lineInfo = Server.QuestServer:GetCurrentSeasonLine()
    if lineInfo == nil then 
        return 
    end

    if self._bisUnlock then
        if lineInfo:GetStageIDByQuestID(questId) == self._stageInfo.stageID then
            self._curStarNum = lineInfo:CalGainStarByStageID(self._stageInfo.stageID)
            if self._curStarNum > self._prevStarNum then
                self._bCanPlayAnim = true
            end
        end
    else
        if lineInfo:IsUnLockByStageID(self._stageInfo.stageID) then
            self._bCanPlayUnlockAnim = true
        end 
    end
end

function QuestSeasonActionPhase:_RefreshView()
    if self._bIsSelected then
        if self._bisUnlock then
            self:SetType(3)
        else
            self:SetType(1)
        end
    else
        if self._bisUnlock then
            self:SetType(2)
        else
            self:SetType(0)
        end
    end
end

function QuestSeasonActionPhase:_OnCloseRewardPanel()
    if self._bCanPlayAnim == true then
        local gainedStar = self._curStarNum - self._prevStarNum
        self._wtAnimText:SetText(string.format(Module.Quest.Config.Loc.QuestSeasonStarGainShort, gainedStar))
        self:PlayWidgetAnim(self.WBP_SeasonalTasks_ActionPhase_upgrade)
        self._bCanPlayAnim = false
    end
end

function QuestSeasonActionPhase:_UpdateStarNum()
    
    local lineInfo = Server.QuestServer:GetCurrentSeasonLine()
    if lineInfo == nil then 
        return 
    end

    if self._prevStarNum < self._curStarNum then
        self._prevStarNum = self._prevStarNum + 1
    else
        if self._timerHandle then
            self._timerHandle:Release()
            self._timerHandle = nil
        end
        
        local nextStage = lineInfo:GetNextStageInfoByID(self._stageInfo.stageID)
        if nextStage then
            Module.Quest.Config.evtQuestPlayPhaseUnlockAnim:Invoke(nextStage.stageID)
        end
    end

    self:SetProgressText(self._prevStarNum, lineInfo:CalTotalStarByStageID(self._stageInfo.stageID))
end

function QuestSeasonActionPhase:OnAnimFinished(anim)
    local lineInfo = Server.QuestServer:GetCurrentSeasonLine()
    if lineInfo == nil then 
        return 
    end

    if anim == self.WBP_SeasonalTasks_ActionPhase_upgrade then
        if self._timerHandle then
            self._timerHandle:Release()
            self._timerHandle = nil
        end

        self._timerHandle = Timer:NewIns(0.1, 0)
        self._timerHandle:AddListener(self._UpdateStarNum, self)
        self._timerHandle:Start()
    elseif anim == self.WBP_SeasonalTasks_ActionPhase_Unlock then
        self:SetIsUnlocked(true)
        self:_RefreshView()
        self:SetProgressText(lineInfo:CalGainStarByStageID(self._stageInfo.stageID), lineInfo:CalTotalStarByStageID(self._stageInfo.stageID))
    end
end

function QuestSeasonActionPhase:_OnPlayUnlockAnim(stageID)
    if self._bCanPlayUnlockAnim == true then
        if stageID == self._stageInfo.stageID then
            self:PlayWidgetAnim(self.WBP_SeasonalTasks_ActionPhase_Unlock)
            Facade.SoundManager:PlayUIAudioEvent("UI_SeasonMission_Stage_Unlock")
        end
        self._bCanPlayUnlockAnim = false
    end
end

function QuestSeasonActionPhase:HideArrow()
    self._wtArrow1:Collapsed()
    self._wtArrow2:Collapsed()
end

return QuestSeasonActionPhase