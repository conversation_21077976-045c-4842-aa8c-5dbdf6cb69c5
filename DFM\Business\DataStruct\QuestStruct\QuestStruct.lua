---@class QuestStruct : LuaObject
local QuestStruct = class("QuestStruct", LuaObject)
OverrideClassByDynamicUD(
QuestStruct, true,
    {"id", FieldType.Integer},
    {"questClass", FieldType.Integer},
    {"type", FieldType.Integer},
    {"mapId", FieldType.Userdata},
    {"preQuestIdList", FieldType.Userdata},
    {"levelLimit", FieldType.Integer},
    {"bShouldAutoAccept", FieldType.Boolean},
    {"acceptDiaIogId", FieldType.String},
    {"rewardDialogId", FieldType.String},
    {"_submitObjectiveList", FieldType.Table},
    {"_objectiveDict", FieldType.Table},
    {"_questConditionDict", FieldType.Table},
    {"_rewardId2RewardInfo", FieldType.Table},
    {"_rewardItemList", FieldType.Table},
    {"bCgQuest", FieldType.Boolean},
    {"cgPath", FieldType.String},
    {"name", FieldType.Userdata},
    {"desc", FieldType.Userdata},
    {"bulletin", FieldType.Userdata},
    {"questImage", FieldType.String},
    {"questIcon", FieldType.String},
    {"yOffset", FieldType.Integer},
    {"bShowRewardPrview", FieldType.Boolean},
    {"rewardNPCImage", FieldType.String},
    {"rewardNPCJob", FieldType.String},
    {"rewardNPCName", FieldType.String},
    {"rewardNPCDesc", FieldType.String},
    {"bIsInMapDetail", FieldType.Boolean},
    {"resetAllWhenSettlementFailed", FieldType.Boolean},
    {"_postQuestList", FieldType.Table},
    {"_multiPostQuestList", FieldType.Table},
    {"_questLineId", FieldType.Integer},
    {"_isConnectPrevious", FieldType.Boolean},
    {"state", FieldType.Integer},
    {"acceptTime", FieldType.Integer},
    {"trackingObjectivesId", FieldType.Table},
    {"expire_time", FieldType.Integer},
    {"mapmaker", FieldType.String},
    {"lockTime", FieldType.Integer},
    {"reward_time", FieldType.Integer},
    {"_lastFinishObjectiveID", FieldType.Integer}
)
local QuestObjectiveStruct = require "DFM.Business.DataStruct.QuestStruct.QuestObjectiveStruct"
local QuestConditionStruct = require "DFM.Business.DataStruct.QuestStruct.QuestConditionStruct"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local RewardInfoDefine = MakeDynamicUDClass(
    {"id", FieldType.Integer},
    {"type", FieldType.Integer},
    {"num", FieldType.Integer},
    {"bindType", FieldType.Integer},
    {"important", FieldType.Boolean}
)
-- QuestClass = {
-- UnknownQuestClass = 0,
-- SOL = 1,
-- World = 2,
-- }
-- QuestType = {
-- UnknownQuestType = 0,
-- Mission = 1, 主线
-- Branch = 2,	支线
-- Trader = 3,	商人
-- Challenge = 4, 挑战
-- Activity = 5, 活动
-- Team = 6,
-- Repeated = 7,
-- BattlePass = 8,
-- }
--QuestState
--{
--    Locked     = 0;  // 未解锁， 客户端使用
--    Unread     = 1;  // 未查看，客户端使用
--    Unaccepted = 2;  // 未接受， 客户端使用
--    Accepted   = 3;  // 已经接受，客户端+服务器使用，任务进行中
--    Failed     = 4;  // 失败, 客户端+服务器使用
--    Paused     = 5;  // 任务暂停, 客户端+服务器使用
--    Completed  = 6;  // 已经达成所有必须目标，没有领取奖励，客户端+服务器使用
--    Rewarded   = 7;  // 已经领取奖励，客户端+服务器使用
--    Expired    = 8;  // 限时任务过期，客户端+服务器使用
--}

function QuestStruct:Ctor(questCfg)
    self.id = questCfg.QuestID
    -- 任务大类，QuestClass 定义
    self.questClass = questCfg.QuestClass
    -- 任务类型，QuestType 定义
    self.type = questCfg.QuestType
    self.mapId = questCfg.MapID
    -- 前置任务列表
    self.preQuestIdList = questCfg.PreviousIDList
    --self.parentQuestID = questCfg.ParentID
    self.levelLimit = questCfg.AcceptRequiredLevel
    -- 是否自动接取
    self.bShouldAutoAccept = questCfg.ShouldAutoAccept
    -- 接受任务剧情对话id
    self.acceptDiaIogId = questCfg.AcceptDiaIogID
    -- 领取奖励任务剧情对话id
    self.rewardDialogId = questCfg.RewardDialogID
    -- 提交类任务目标
    self._submitObjectiveList = {}
    -- key:id value:QuestObjectiveStruct
    self._objectiveDict = {}

    --挑战因子列表
    self._questConditionDict = {}

    -- 任务奖励
    self._rewardId2RewardInfo = {} -- 包含声望奖励
    self._rewardItemList = {} -- 不包含声望奖励

    self.bCgQuest = questCfg.IsCG > 0
    self.cgPath = questCfg.CgPath
    -- 任务名称
    self.name = questCfg.Name
    -- 任务描述
    self.desc = questCfg.Desc
    -- 任务描述简报
    self.bulletin = questCfg.Bulletin
    -- 详情界面图片
    self.questImage = questCfg.QuestImage
    -- 任务图标
    self.questIcon = questCfg.QuestIcon
    -- 偏移
    self.yOffset = questCfg.Yoffset
    -- 是否显示奖励预览按钮
    self.bShowRewardPrview = questCfg.ShowRewardPrview
    -- 奖励预览界面相关
    -- 立绘图片
    self.rewardNPCImage = questCfg.RewardNPCImage
    -- 立绘职位描述
    self.rewardNPCJob = questCfg.RewardNPCJob
    -- 立绘名称
    self.rewardNPCName = questCfg.RewardNPCName
    -- 立绘描述
    self.rewardNPCDesc = questCfg.RewardNPCDesc

    -- 是否展示战术地图
    self.bIsInMapDetail = questCfg.IsInMapDetail

    self.resetAllWhenSettlementFailed = questCfg.ResetAllWhenSettlementFailed or false

    -- 任务目标关系，与或关系
    -- self.objectiveRelation = 0
    -- 后置任务（多个）
    self._postQuestList = {}
    -- 多前置的后置任务
    self._multiPostQuestList = {}

    -- 任务线信息
    self._questLineId = 0
    --是否直连前置节点
    self._isConnectPrevious = questCfg.IsConnectPrevious or false

    -- 服务端数据
    -- 任务状态，参照QuestState宏定义
    self.state = QuestState.Locked
    self.acceptTime = 0
    -- 追踪中的目标列表（目标追踪中）
    self.trackingObjectivesId = {}
    -- 任务过期时间
    self.expire_time = 0
    -- mapmaker
    self.mapmaker = ""

    --时间锁
    self.lockTime = questCfg.FrozenTimeAfterPreQuestReward or 0

    -- 任务中的领奖时间，用于下一个任务是否可以接取的时间判断
    self.reward_time = 0

    self._lastFinishObjectiveID = 0

    self.seasonGroupID = questCfg.SeasonGroupID or 0
    self.seasonStarNum = questCfg.StarNum or 0

    self._seasonLineID = 0
    self._seasonStageID = 0
    self._seasonGroupID = 0
end

function QuestStruct:HotFixQuest(questCfg)
    -- self.questClass = questCfg.QuestClass
    -- self.type = questCfg.QuestType
    self.mapId = questCfg.MapID
    self.preQuestIdList = questCfg.PreviousIDList
    --self.parentQuestID = questCfg.ParentID
    self.levelLimit = questCfg.AcceptRequiredLevel
    self.acceptDiaIogId = questCfg.AcceptDiaIogID
    self.rewardDialogId = questCfg.RewardDialogID
    self.bCgQuest = questCfg.IsCG > 0
    self.cgPath = questCfg.CgPath
    self.name = questCfg.Name
    self.desc = questCfg.Desc
    self.bulletin = questCfg.Bulletin
    self.questImage = questCfg.QuestImage
    self.questIcon = questCfg.QuestIcon
    self.yOffset = questCfg.Yoffset
    self.bShowRewardPrview = questCfg.ShowRewardPrview
    self.rewardNPCImage = questCfg.RewardNPCImage
    self.rewardNPCJob = questCfg.RewardNPCJob
    self.rewardNPCName = questCfg.RewardNPCName
    self.rewardNPCDesc = questCfg.RewardNPCDesc
    self.bIsInMapDetail = questCfg.IsInMapDetail
    self.lockTime = questCfg.FrozenTimeAfterPreQuestReward or 0
    self.resetAllWhenSettlementFailed = questCfg.ResetAllWhenSettlementFailed or false
    self._isConnectPrevious = questCfg.IsConnectPrevious or false

    self.seasonGroupID = questCfg.SeasonGroupID or 0
    self.seasonStarNum = questCfg.StarNum or 0
end

function QuestStruct:ResetQuestCondition()
    self._questConditionDict = {}
end

function QuestStruct:AddQuestCondition(condtionTable)
    if self._questConditionDict[condtionTable.QuestConditionID] == nil then
        local conditionInfo = QuestConditionStruct:New(condtionTable, self)
        self._questConditionDict[conditionInfo.id] = conditionInfo
    else
        logwarning(
            string.format(
                "【Error】 : Quest 【%d】 has duplicate Condition ID 【%d】 in Condition Table !",
                self.id,
                condtionTable.QuestConditionID
            )
        )
    end
end

function QuestStruct:DeleteQuestCondition(conditionId)
    if self._questConditionDict[conditionId] ~= nil then
        self._questConditionDict[conditionId] = nil
    end
end

function QuestStruct:GetAllQusetConditions()
    local list = {}
    for key, value in pairs(self._questConditionDict) do
        table.insert(list, value)
    end
    return list
end

function QuestStruct:GetQusetConditionById(questConditionID)
    return self._questConditionDict[questConditionID]
end

function QuestStruct:GetConditionIDList()
    local list = {}
    for key, value in pairs(self._questConditionDict) do
        table.insert(list, value.id)
    end
    return list
end

function QuestStruct:AddQuestObjective(objectiveTable, complexPropObjTable)
    if self._objectiveDict[objectiveTable.ObjectiveID] == nil then
        local objectiveInfo = QuestObjectiveStruct:New(objectiveTable, self)
        self._objectiveDict[objectiveInfo.id] = objectiveInfo
        if complexPropObjTable then
            objectiveInfo:AddComplexPropObjective(complexPropObjTable)
        end
        if objectiveInfo.type == QuestObjectiveType.Submit then
            table.insert(self._submitObjectiveList, objectiveInfo)
        end
    else
        logwarning(
            string.format(
                "【Error】 : Quest 【%d】 has duplicate Objective ID 【%d】 in Quest Table !",
                self.id,
                objectiveTable.ObjectiveID
            )
        )
    end
end

function QuestStruct:DeleteQuestObjective(ObjectID)
    if self._objectiveDict[ObjectID] ~= nil then
        if objectiveInfo.type == QuestObjectiveType.Submit then
            table.removebyvalue(self._submitObjectiveList, self._objectiveDict[ObjectID])
        end
        self._objectiveDict[ObjectID] = nil
    end
end

function QuestStruct:DeleteAllQuestObjective()
    self._submitObjectiveList = {}
    self._objectiveDict = {}
end

function QuestStruct:HotFixQuestRewardById(rewardId, rewardCfg)
    local rewardInfo = self._rewardId2RewardInfo[rewardId]
    if rewardInfo then
        if rewardInfo.rewardType ~= rewardCfg.Type then
            if rewardInfo.rewardType ~= QuestRewardType.UnknownQuestRewardType then
                table.removebyvalue(self._rewardItemList, rewardInfo.rewardItem)
            end
            self:AddQuestReward(rewardCfg)
        else
            if rewardCfg.Type ~= QuestRewardType.RewardExp then
                rewardInfo.rewardItem.id = rewardCfg.ItemID
            end
            rewardInfo.rewardItem.num = rewardCfg.Number
        end
    end
end

function QuestStruct:AddQuestReward(rewardTable)
    local rewardItem = nil
    -- 经验
    if rewardTable.Type == QuestRewardType.RewardExp then
        -- 部门声望
        -- 道具、货币、藏品
        --rewardItem = self:_GenerateRewardItem({ id = EXP_ID, num = rewardTable.Number, bindType = rewardTable.bindType })
        local rewardInfo = RewardInfoDefine:new()
        rewardInfo.id = EXP_ID
        rewardInfo.type = rewardTable.Type
        rewardInfo.num = rewardTable.Number
        rewardInfo.bindType = rewardTable.bindType
        rewardInfo.important = rewardTable.ImportantReward
        rewardItem = {
            rewardInfo = rewardInfo,
            rewardItemBase = nil
        }
        self._rewardId2RewardInfo[rewardTable.RewardID] = {rewardItem = rewardItem, rewardType = rewardTable.Type}
        table.insert(self._rewardItemList, rewardItem)
    elseif rewardTable.Type ~= QuestRewardType.UnknownQuestRewardType then
        --rewardItem = self:_GenerateRewardItem({ id = rewardTable.ItemID, num = rewardTable.Number,
        --	bindType = rewardTable.bindType })
        local rewardInfo = RewardInfoDefine:new()
        rewardInfo.id = rewardTable.ItemID
        rewardInfo.type = rewardTable.Type
        rewardInfo.num = rewardTable.Number
        rewardInfo.bindType = rewardTable.bindType
        rewardInfo.important = rewardTable.ImportantReward
        rewardItem = {
            rewardInfo = rewardInfo,
            rewardItemBase = nil
        }
        self._rewardId2RewardInfo[rewardTable.RewardID] = {rewardItem = rewardItem, rewardType = rewardTable.Type}
        table.insert(self._rewardItemList, rewardItem)
    end
end

function QuestStruct:_GenerateRewardItem(rewardInfo)
    local item = ItemBase:New(rewardInfo.id, rewardInfo.num)
    if ItemHelperTool.GetMainTypeById(rewardInfo.id) == EItemType.Weapon then
        --item = WeaponAssemblyTool.GetWeaponItemByPresetId(rewardInfo.id)
        local weaponDesc = WeaponAssemblyTool.PresetRow_to_Desc(item.id)
        item:SetRawDescObj(weaponDesc)
        local propinfo = WeaponAssemblyTool.Desc_To_PropInfo(weaponDesc)
        if propinfo then
            propinfo.bind_type = rewardInfo.bindType
        end
        local partsPropInfo = propinfo.components

        ---@param partInfo pb_Component
        local function fRecursiveGetPart(partInfo)
            if partInfo.prop_data then
                partInfo.prop_data.bind_type = rewardInfo.bindType
                for _, subPartInfo in ipairs(partInfo.prop_data.components) do
                    fRecursiveGetPart(subPartInfo)
                end
            end
        end
        for _, partInfo in ipairs(partsPropInfo) do
            fRecursiveGetPart(partInfo)
        end
        item:SetRawPropInfo(propinfo)
    end

    if rewardInfo.type == QuestRewardType.RewardProp then
        item:ForceSetBindType(rewardInfo.bindType)
    end

    return item
end

-- 获取奖励列表
function QuestStruct:GetRewardList()
    -- 不包含声望奖励
    local list = {}
    for index, value in ipairs(self._rewardItemList) do
        if value.rewardItemBase == nil then
            local rewardItem = self:_GenerateRewardItem(value.rewardInfo)
            if rewardItem then
                value.rewardItemBase = rewardItem
            end
        end
        if value.rewardItemBase then
            table.insert(list, value.rewardItemBase)
        end
    end
    return list
end

function QuestStruct:DeleteAllQuestReward()
    self._rewardItemList = {}
    self._rewardId2RewardInfo = {}
end

function QuestStruct:GetAllQuestRewards()
    local allquestRewardItems = {}
    for rewardId, rewardInfo in pairs(self._rewardId2RewardInfo) do
        allquestRewardItems[rewardId] = rewardInfo.rewardItem
    end
    return allquestRewardItems
end

function QuestStruct:GetQuestImportantReward()
    local rewardItem = nil
    local questRewards = self:GetAllQuestRewards()
    for id, rewardInfo in pairs(questRewards) do
        if rewardInfo.rewardInfo.important then
            if rewardInfo.rewardItemBase == nil then
                rewardInfo.rewardItemBase = self:_GenerateRewardItem(rewardInfo.rewardInfo)
            end
            rewardItem = rewardInfo.rewardItemBase
        end
    end
    return rewardItem
end

-- 更新任务信息
function QuestStruct:UpdateQuest(questData, canNotify)
    self.acceptTime = setdefault(questData.quest_accept_time, self.acceptTime)
    self.reward_time = setdefault(questData.reward_time, self.reward_time)
    self.trackingObjectivesId = questData.marked_objective_id

    if self.state ~= QuestState.Completed and questData.quest_state == QuestState.Completed then
        if questData.quest_objectives then
            for _, objectiveData in ipairs(questData.quest_objectives) do
                local objective = self._objectiveDict[objectiveData.quest_objective_id]
                if objective ~= nil then
                    if objective.bIsFinsih == false and objectiveData.has_completed then
                        self._lastFinishObjectiveID = objectiveData.quest_objective_id
                    end
                end
            end
        end
    end

    self:UpdateState(questData.quest_state, canNotify)
    -- 更新其任务目标
    if questData.quest_objectives then
        for _, objectiveData in ipairs(questData.quest_objectives) do
            local objective = self._objectiveDict[objectiveData.quest_objective_id]
            if objective ~= nil then
                objective:UpdateObjective(objectiveData, canNotify)
            end
        end
    else
        for _, objective in ipairs(self:GetAllQusetObjectives()) do
            objective:ResetObjective()
        end
    end
end

-- 重置任务信息
function QuestStruct:ResetQuest()
    self.acceptTime = 0
    for _, objective in ipairs(self:GetAllQusetObjectives()) do
        objective:ResetObjective()
    end
    self:UpdateState(QuestState.Locked)
end

--放弃任务信息
function QuestStruct:GiveUpQuest()
    self.acceptTime = 0
    self:UpdateState(QuestState.Unaccepted, true)
    for _, objective in ipairs(self:GetAllQusetObjectives()) do
        objective:ResetObjective()
    end
end

-- 更新任务追踪状态
function QuestStruct:UpdateQuestObjectiveTrack(objectId, bTracking)
    local objective = self._objectiveDict[objectId]
    if objective ~= nil then
        objective:UpdateTrack(bTracking)
    end
end

-- 更新任务状态
function QuestStruct:UpdateState(questState, canNotify)
    local notifyExc = canNotify and (self.state ~= questState) or false
    self.state = questState
    if self.state >= QuestState.Completed or self.state == QuestState.Paused then
        -- 更新 已完成任务/已领取奖励任务 的一级后置任务状态为 未读取状态/可接受状态
        local postQusetState = self.state == QuestState.Completed and QuestState.Unread or QuestState.Unaccepted
        for _, postQuset in ipairs(self._postQuestList) do
            if postQuset.state < QuestState.Accepted and postQuset:IsPreQuestAllFinished() then
                postQuset:UpdateState(postQusetState, true)
            end
        end

        for _, multiPostQuset in ipairs(self._multiPostQuestList) do
            if multiPostQuset.state < QuestState.Accepted and multiPostQuset:IsPreQuestAllFinished() then
                multiPostQuset:UpdateState(postQusetState, true)
            end
        end
    end
    if notifyExc then
        Server.QuestServer.Events.evtUpdateQuestState:Invoke(self.id)
    end
end

--更新任务的接取状态
function QuestStruct:UpdateQuestLockState()
    if self.state < QuestState.Unaccepted and self:IsPreQuestAllFinished() then
        self.state = QuestState.Unaccepted
    end
end

function QuestStruct:GetQusetObjectiveById(objectiveId)
    return self._objectiveDict[objectiveId]
end

function QuestStruct:GetObjectIDList()
    local list = {}
    for key, value in pairs(self._objectiveDict) do
        table.insert(list, value.id)
    end
    return list
end

function QuestStruct:GetAllQusetObjectives()
    local list = {}
    for key, value in pairs(self._objectiveDict) do
        table.insert(list, value)
    end
    table.sort(
        list,
        function(quest1, quest2)
            return quest1.id < quest2.id
        end
    )
    return list
end

-- 获取任务目标列表
function QuestStruct:GetQusetObjectives()
    local unLockObjectives = {}
    for _, objective in ipairs(self:GetAllQusetObjectives()) do
        if objective.bUnLock == false then
            local preObjective = self._objectiveDict[objective.preObjectiveID]
            objective.bUnLock = preObjective and preObjective.bIsFinsih
        end
        if objective.bUnLock then
            table.insert(unLockObjectives, objective)
        end
    end

    table.sort(
        unLockObjectives,
        function(q1, q2)
            return q1.id < q2.id
        end
    )

    return unLockObjectives
end

-- 获取任务目标列表
function QuestStruct:GetQuestObjectivesById(objId)
    for _, objective in ipairs(self:GetAllQusetObjectives()) do
        if objective.id == objId then
            return objective
        end
    end
end

function QuestStruct:IsNeedShowUpdateTip()
    local need = true
    local objective = self._objectiveDict[self._lastFinishObjectiveID]
    if objective ~= nil then
        need = objective:IsNeedShowUpdateTip()
    end
    if not need then
        logwarning("QuestStruct:IsNeedShowUpdateTip _lastFinishObjectiveID = ", self._lastFinishObjectiveID)
    end
    return need
end

-- 获取提交道具类目标
function QuestStruct:GetSubmitObjectives()
    local unLockObjectives = {}
    for _, objective in ipairs(self._submitObjectiveList) do
        if objective.bUnLock == false then
            local preObjective = self._objectiveDict[objective.preObjectiveID]
            objective.bUnLock = preObjective and preObjective.bIsFinsih
        end
        if objective.bUnLock then
            table.insert(unLockObjectives, objective)
        end
    end
    return unLockObjectives
end

-- 添加后置任务
function QuestStruct:AddPostQuests(questInfo)
    for index, value in ipairs(self._postQuestList) do
        if value.id == questInfo.id then
            return
        end
    end
    table.insert(self._postQuestList, questInfo)
    table.sort(
        self._postQuestList,
        function(quest1, quest2)
            return quest1.id < quest2.id
        end
    )
end

-- 添加非直连后置任务
function QuestStruct:AddMultiPostQuest(questInfo)
    for index, value in ipairs(self._multiPostQuestList) do
        if value.id == questInfo.id then
            return
        end
    end
    table.insert(self._multiPostQuestList, questInfo)
end

function QuestStruct:DeletePostQuests()
    self._postQuestList = {}
end

--是否直连前置任务
function QuestStruct:IsConnectToPrevious()
    return self._isConnectPrevious
end

-- 获取后置任务列表
function QuestStruct:GetPostQuests()
    local openPostQuset = {}
    for _, questInfo in ipairs(self._postQuestList) do
        table.insert(openPostQuset, questInfo)
    end
    return openPostQuset
end

function QuestStruct:GetMultiPostQuests()
    local openPostQuset = {}
    for _, questInfo in ipairs(self._multiPostQuestList) do
        table.insert(openPostQuset, questInfo)
    end
    return openPostQuset
end

function QuestStruct:IsPostQuestsHasExistConnectPrevious()
    local isHasExist = false
    for index, value in ipairs(self._postQuestList) do
        if value:IsConnectToPrevious() then
            isHasExist = true
        end
    end
    return isHasExist
end

function QuestStruct:GetPostQuestLayer()
    local layer = 0
    local allPostQusets = self:GetPostQuests()
    if #allPostQusets > 1 then
        layer = layer + 1
    end
    for _, questInfo in ipairs(self:GetPostQuests()) do
        layer = layer + questInfo:GetPostQuestLayer()
    end
    return layer
end

-- 设置所属任务线信息
function QuestStruct:SetQuestLine(questLineId)
    if questLineId ~= nil then
        self._questLineId = questLineId
        local allPostQusets = self:GetPostQuests()
        for _, questInfo in ipairs(allPostQusets) do
            questInfo:SetQuestLine(questLineId)
        end
    end
end

-- 获取任务线（可能为nil，需要通过QuestServer去获取）
function QuestStruct:GetQuestLine()
    return self._questLineId
end

function QuestStruct:IsQuestNeedShow()
    local ret = false
    local lineInfo = Server.QuestServer:GetQuestLineInfoById(self._questLineId)
    if lineInfo then
       ret = lineInfo:IsLineNeedShow()
    else
        local seasonLine = Server.QuestServer:GetSeasonLineData(self._seasonLineID)
        if seasonLine then
            ret = seasonLine:IsCurSeasonLine() and seasonLine:IsSeasonLineOpen()
        end
    end
    return ret
end

--- 提交道具的任务是否完成
function QuestStruct:IsFinishSubmitObjectives()
    for _, objectiveInfo in pairs(self:GetSubmitObjectives()) do
        if not objectiveInfo.bIsFinsih then
            return false
        end
    end
    return true
end

-- 是否有追踪中的目标
function QuestStruct:HasTrackingObjections()
    local bTrackingQuset = false
    for _, objectiveInfo in pairs(self:GetAllQusetObjectives()) do
        bTrackingQuset = bTrackingQuset or not objectiveInfo.bIsFinsih and objectiveInfo.bShowTracking
    end
    return bTrackingQuset
end

--- 是否为前置任务
function QuestStruct:IsPreQuest(questId)
    for _, preId in ipairs(self.preQuestIdList) do
        if preId == questId then
            return true
        end
    end
    return false
end

function QuestStruct:GetPreRewardTime()
    local rewardTime = 0
    for _, preId in ipairs(self.preQuestIdList) do
        local preQuset = Server.QuestServer:GetQuestInfoById(preId)
        if preQuset and preQuset.reward_time > rewardTime then
            rewardTime = preQuset.reward_time
        end
    end
    return rewardTime
end

function QuestStruct:IsPreQuestAllFinished()
    local ret = true
    for _, preId in ipairs(self.preQuestIdList) do
        local preQuset = Server.QuestServer:GetQuestInfoById(preId)
        if preQuset and preQuset.state < QuestState.Rewarded then
            ret = false
            break
        end
    end
    return ret
end

function QuestStruct:GetRemainToAcceptTime()
    local time = -1
    local preRewardTime = self:GetPreRewardTime()
    if preRewardTime > 0 and self.lockTime > 0 then
        local lockMinTime = self.lockTime * 60
        local delta = Facade.ClockManager:GetLocalTimestamp() - preRewardTime
        if lockMinTime > delta then
            time = lockMinTime - delta
        end
    end
    return time
end

function QuestStruct:SetSeasonInfo(lineId, stageId, groupId)
    self._seasonLineID = lineId
    self._seasonStageID = stageId
    self._seasonGroupID = groupId
end

return QuestStruct
