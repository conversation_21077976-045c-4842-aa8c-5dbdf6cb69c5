----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGunsmith)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import "EGPUINavWidgetFocusedAction"
local UGameplayBlueprintHelper = import "GameplayBlueprintHelper"
-- END MODIFICATION

local GunsmithUI = require "DFM.Business.Module.GunsmithModule.UI.GunsmithUI"
local CommonItemViewDropDownBox = require "DFM.Business.Module.CommonWidgetModule.UI.DropDown.CommonItemViewDropDownBox"
local CollectionMysticalSkinBtn = require "DFM.Business.Module.CollectionModule.UI.CollectionMysticalSkinBtn"

local ItemUIDataContainer = require "DFM.Business.DataStruct.UIDataStruct.ItemUIDataContainer"
local GunsmithSkinItemUIDatasSearchParam = require "DFM.Business.Module.GunsmithModule.Data.Skin.GunsmithSkinItemUIDatasSearchParam"
local GunsmithSkinSpecialRefreshParam = require "DFM.Business.Module.GunsmithModule.Data.Skin.GunsmithSkinSpecialRefreshParam"
local GunsmithSkinDefaultMainItemUIData = require "DFM.Business.Module.GunsmithModule.Data.Skin.GunsmithSkinDefaultMainItemUIData"
local EGunsmithSkinSortType = require "DFM.Business.Module.GunsmithModule.Define.EGunsmithSkinSortType"
local GunsmithSkinData = require "DFM.Business.Module.GunsmithModule.Data.Skin.GunsmithSkinData"
local GunsmithSkinDefaultMainItemUI = require "DFM.Business.Module.GunsmithModule.UI.Skin.GunsmithSkinDefaultMainItemUI"

local GunsmithSkinLogic = require "DFM.Business.Module.GunsmithModule.Logic.Skin.GunsmithSkinLogic"
local GunsmithUIContextLogic = require "DFM.Business.Module.GunsmithModule.Logic.GunsmithUIContextLogic"
local GunsmithTLogLogic = require "DFM.Business.Module.GunsmithModule.Logic.TLog.GunsmithTLogLogic"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local CommonWeaponSkinMissionProgress = require "DFM.Business.Module.CommonWidgetModule.UI.CommonWeaponSkinMissionProgress"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local LiteCommonDownload =  require "DFM.Business.Module.LitePackageModule.UI.LiteCommonDownload"
local GunsmithSolutionMainLogic = require "DFM.Business.Module.GunsmithModule.Logic.Solution.GunsmithSolutionMainLogic"

local EAssemblerCameraType = import "EAssemblerCameraType"
local EAssemblerCamPoint = import "EAssemblerCamPoint"
local EGPInputModeType = import "EGPInputModeType"
local ECheckButtonState = import"ECheckButtonState"
local FPbWeaponSkinInfoParam = import "PbWeaponSkinInfoParam"

local GunsmithSkinDefaultMainUI_ProcessUIOPCode = {
    Force = 1,
    DefaultUI = 2,
    MysticalUI = 3,
}

---@class GunsmithSkinDefaultMainUI : GunsmithUI
local GunsmithSkinDefaultMainUI = ui("GunsmithSkinDefaultMainUI", GunsmithUI)

function GunsmithSkinDefaultMainUI:Ctor()
    self._wt_WaterFallList = UIUtil.WndWaterfallScrollBox(self, "wt_WaterFallList", self._OnGetItemCount, self._OnProcessItemWidget, UIName2ID.GunsmithSkinDefaultMainItemUI)
    local path = UIName2ID.GetBPFullPathByID(UIName2ID.GunsmithSkinDefaultMainItemUI)
    self._wt_WaterFallList:RelinkTemplateWidget(path)

    self._wt_WBP_GunStand_GunsmithSimulateStatePropmtUI = self:Wnd("wt_WBP_GunStand_GunsmithSimulateStatePropmtUI", GunsmithSimulateStatePropmtUI)

    -- self._wtSortDropDown = UIUtil.WndDropDownBox(self, "wtSortDropDown", self._OnCheckeSortIndexChanged)

    self._wt_Butten_Customize = self:Wnd("WBP_CommonButtonV3S1_91", CommonButton)
    self._wt_Butten_Customize:SetActive(false)

    self._wt_Button_Equip = self:Wnd("WBP_DFCommonButtonV1S2_57", DFCommonButtonOnly)
    self._wt_Button_Equip:Event("OnClicked", self._OnButtonEquipClicked, self)

    self._wt_Button_EquipAll = self:Wnd("wt_WBP_CommonButtonV1S1_Equip", DFCommonButtonOnly)
    self._wt_Button_EquipAll:Event("OnClicked", self._OnButtonEquipAllClicked, self)
    self._wt_Button_EquipAll:Event("OnDeClicked", self._OnButtonEquipAllDeClicked, self)

    -- 皮肤没有卸载逻辑
    -- self._wt_Button_UnequipAll = self:Wnd("WBP_DFCommonButtonV1S2", DFCommonButtonOnly)
    -- self._wt_Button_UnequipAll:Event("OnClicked", self._OnButtonUnequipAllClicked, self)

    self._wt_WBP_DFCommon_MissionUnlocked = self:Wnd("WBP_DFCommon_MissionUnlocked_156", CommonWeaponSkinMissionProgress)

    -- self._wt_WBP_CommonCheckBox_LockAppearance = self:Wnd("wt_WBP_CommonCheckBox_LockAppearance", UIWidgetBase):Wnd("DFCheckBox_Icon", UICheckBox)
    -- self._wt_WBP_CommonCheckBox_LockAppearance:SetCallback(self._OnCommonCheckBoxLockAppearanceStateUpdated, self)

    -- self._wt_WBP_CommonCheckBox_EquipOnly = self:Wnd("wt_WBP_CommonCheckBox_EquipOnly", UIWidgetBase):Wnd("DFCheckBox_Icon", UICheckBox)
    -- self._wt_WBP_CommonCheckBox_EquipOnly:SetCallback(self._OnCommonCheckBoxSkinEquipStateUpdated, self)

    self._wt_WBP_CommonCheckBox_EquipSolution = self:Wnd("wt_WBP_CommonCheckBox_EquipSolution", DFCheckBoxWithText)
    self._wt_WBP_CommonCheckBox_EquipSolution:Event("OnCheckStateChanged", self._OnCheckBoxEquipSolutionStateUpdated, self)
    self._wt_WBP_CommonCheckBox_EquipSolution:SetMainTitle(Module.Gunsmith.Config.Loc.GunsmithSkinDefaultMainUIEquipSolutionText)

    self._wtMysticalSkinDropDown = self:Wnd("wtMysticalSkinDropDown", CommonItemViewDropDownBox)

    local data = {
        tabName = Module.Gunsmith.Config.Loc.DropDownBoxTitle,                  -- tab文本名字
        subUINavID = UIName2ID.GunsmithSkinDefaultMainItemUI,                   -- 指定生成子ui
        caller = self,                                                          -- 方法持有者
        fOnGetPresetCount = self._OnGetMysticalItemCount,                       -- 子控件数量获取方法
        fOnPresetProcessTabItemWidget = self._OnProcessMysticalItemWidget,      -- 子控件生成回调方法,
    }
    self._wtMysticalSkinDropDown:InitDownBox(data)


    -- self._wtMysticalSkinDropDown = self:Wnd("wtMysticalSkinDropDown", AssemblyPresetDownBox) ---@type AssemblyPresetDownBox
    -- self._wtMysticalSkinDropDown:InitDownBox(self, self._OnGetMysticalItemCount, self._OnProcessMysticalItemWidget)
    -- self._wtMysticalSkinDropDown:RelinkTemplateWidget(UIName2ID.GunsmithSkinDefaultMainItemUI)

    self._wtAlertHintBox = self:Wnd("wtAlertHintBox", UIWidgetBase)
    self._wtAlertHintTxt = self:Wnd("wtAlertHintTxt", UIWidgetBase)

    self._wtMysticalSkinBtnPanel = self:Wnd("wtMysticalSkinBtnPanel", UIWidgetBase)

    self._wtFromMandelBrickBtn = self:Wnd("wtFromMandelBrickBtn", CollectionMysticalSkinBtn)
    self._wtFromMandelBrickBtn:Event("OnClicked", self._OnShowMandelBrickPage, self)

    self._wtFromAuctionBtn = self:Wnd("wtFromAuctionBtn", CollectionMysticalSkinBtn)
    self._wtFromAuctionBtn:Event("OnClicked", self._OnShowAuctionPage, self)

    self._wtFromMatrixWorkshopBtn = self:Wnd("wtFromMatrixWorkshopBtn", CollectionMysticalSkinBtn)
    self._wtFromMatrixWorkshopBtn:Event("OnClicked", self._OnShowMysticalWorkshopPage, self)

    self._wtCommonDownload = self:Wnd("WBP_CommonDownload", LiteCommonDownload)

    self._wt_WBP_CommonButtonV1S1_Retrieve = self:Wnd("wt_WBP_CommonButtonV1S1_Retrieve", DFCommonButtonOnly)
    self._wt_WBP_CommonButtonV1S1_Retrieve:Event("OnClicked", self._OnButtonRetrieveWeaponSkinClicked, self)

    self._wt_WBP_CommonButtonV1S1_Mission = self:Wnd("wt_WBP_CommonButtonV1S1_Mission", DFCommonButtonOnly)
    self._wt_WBP_CommonButtonV1S1_Mission:Event("OnClicked", self._OnButtonMissionSkinClicked, self)

    if IsHD() then
        self._wt_WBP_RandomButton_PC = self:Wnd("WBP_DFCommonButtonV1S2_1", DFCommonButtonOnly)
        self._wt_WBP_RandomButton_PC:Event("OnClicked", self._OnRandomButtonClicked, self)
    else
        self._wt_WBP_RandomButton_Moblie = self:Wnd("WBP_CommonButtonV3S1", DFCommonButtonOnly)
        self._wt_WBP_RandomButton_Moblie:Event("OnClicked", self._OnRandomButtonClicked, self)

        self._wtRandomBtn_Moblie = self:Wnd("WBP_DFCommonIconButton", UIButton)  -- 随机皮肤提示按钮
        self._wtRandomBtn_Moblie:Event("OnClicked", self._OnRandomSkinButtonClicked, self)
    end

    self._searchUIDataParam = GunsmithSkinItemUIDatasSearchParam:NewIns()
    self._itemUIdataContainer = ItemUIDataContainer:NewIns(GunsmithSkinDefaultMainItemUIData)
    self._itemUIdataContainer4Mystical = ItemUIDataContainer:NewIns(GunsmithSkinDefaultMainItemUIData)
    self._focusItemUIData = nil     ---@type GunsmithSkinDefaultMainItemUIData

    self:SetCPPValue("WantedInputMode", EGPInputModeType.GameAndUI)

    self._lastOPCode = GunsmithSkinDefaultMainUI_ProcessUIOPCode.Force

    -- 特殊业务需求导致的刷新界面情况,刷新数据,但是UI中的waterfall只做RefreshVisibleItem
    self._specialRefreshParam = GunsmithSkinSpecialRefreshParam:NewIns()

    -- self._sortType = EGunsmithSkinSortType.Quality

    self._skinData = GunsmithSkinData:NewIns()
end

function GunsmithSkinDefaultMainUI:Destroy()
    releaseobject(self._searchUIDataParam)
    self._searchUIDataParam = nil

    releaseobject(self._itemUIdataContainer)
    self._itemUIdataContainer = nil

    releaseobject(self._itemUIdataContainer4Mystical)
    self._itemUIdataContainer4Mystical = nil

    self._focusItemUIData = nil

    releaseobject(self._specialRefreshParam)
    self._specialRefreshParam = nil

    releaseobject(self._skinData)
    self._skinData = nil
end

-- BEGIN MODIFICATION @ VIRTUOS : 设置导航组
function GunsmithSkinDefaultMainUI:OnShowBegin()
    GunsmithUI.OnShowBegin(self)

    if IsHD() then
        self:_EnableNavigation(true)
        self._wtFromMandelBrickBtn._wtBtn:Event("OnFocusReceivedEvent", self._HandleMandelBrickBtnFocusRecive, self)
        self._wtFromMandelBrickBtn._wtBtn:Event("OnFocusLostEvent", self._HandleMandelBrickBtnFocusLost, self)
        self._wtFromAuctionBtn._wtBtn:Event("OnFocusReceivedEvent", self._HandleAuctionBtnFocusRecive, self)
        self._wtFromAuctionBtn._wtBtn:Event("OnFocusLostEvent", self._HandleAuctionBtnFocusLost, self)
        self._wtFromMatrixWorkshopBtn._wtBtn:Event("OnFocusReceivedEvent", self._HandleMatrixWorkBtnFocusRecive, self)
        self._wtFromMatrixWorkshopBtn._wtBtn:Event("OnFocusLostEvent", self.HandleMatrixWorkBtnFocusLost, self)
    end
end
-- END MODIFICATION

function GunsmithSkinDefaultMainUI:OnShow()
    GunsmithUI.OnShow(self)
    self:AddLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithOnSkinDefaultItemUIClicked, self._OnProcessGunsmithOnSkinDefaultItemUIClicked, self)
    self:AddLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithCSPostAssemblyApplySkin, self._OnProcessevtCSWAssemblyApplySkinRes, self)
    self:AddLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithCSPostAssemblyModifySkin, self._OnProcessevtCSAssemblyModifySkinRes, self)
    self:AddLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithOnRangeDataUpdated, self._OnProcessGunsmithOnRangeDataUpdated, self)
    self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult, self._ModuleDownloadResult, self)
    self:AddLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged, self._OnPackStateChanged, self)
    self:AddLuaEvent(Server.CollectionServer.Events.evtUpdateCollectionData, self._OnUpdateCollectionData, self)
    self:AddLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithCSPostAssemblyDepositPropUpdate, self._OnProcessServerDataUpdated, self)
    self:AddLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithCSPostRelayConnected, self.OnProcessRelayConnected, self)
    self:AddLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithOnSimulateDataUpdated, self._OnProcessGunsmithOnSimulateDataUpdated, self)
    self:AddLuaEvent(Server.CollectionServer.Events.evtCollectionRandomSkinPoolUpdate, self._OnProcessRandomSkinPoolUpdate, self)
    self:AddLuaEvent(Server.CollectionServer.Events.evtCollectionRandomSkinPoolToggled,self._OnRandomSkinPoolToggled, self)

    self._bEquipSolution = GunsmithSkinLogic.GetEquipSolutionState()
    self._wt_WBP_CommonCheckBox_EquipSolution:SetIsChecked(self._bEquipSolution)

    self:_OnRegisterCameraEvent()
end

function GunsmithSkinDefaultMainUI:OnHideBegin()
    GunsmithUI.OnHideBegin(self)
    if self._wt_WBP_GunStand_GunsmithSimulateStatePropmtUI and self._wt_WBP_GunStand_GunsmithSimulateStatePropmtUI.SetActive then
        self._wt_WBP_GunStand_GunsmithSimulateStatePropmtUI:SetActive(false)
    end

    -- BEGIN MODIFICATION @ VIRTUOS : 恢复默认导航配置
    if IsHD() then
        self:_EnableNavigation(false)
        self:_RemoveShortcuts()
    end
    -- END MODIFICATION
end

function GunsmithSkinDefaultMainUI:OnHide()
    GunsmithUI.OnHide(self)
    self:RemoveLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithOnSkinDefaultItemUIClicked)
    self:RemoveLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithCSPostAssemblyApplySkin)
    self:RemoveLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithCSPostAssemblyModifySkin)
    self:RemoveLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithOnRangeDataUpdated)
    self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult)
    self:RemoveLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged)
    self:RemoveLuaEvent(Server.CollectionServer.Events.evtUpdateCollectionData)
    self:RemoveLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithCSPostAssemblyDepositPropUpdate)
    self:RemoveLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithCSPostRelayConnected)
    self:RemoveLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithOnSimulateDataUpdated)
    self:RemoveLuaEvent(Server.CollectionServer.Events.evtCollectionRandomSkinPoolUpdate)
    self:RemoveLuaEvent(Server.CollectionServer.Events.evtCollectionRandomSkinPoolToggled)
    self:_OnUnRegisterCameraEvent()
end

function GunsmithSkinDefaultMainUI:_OnGetItemCount()
    if self._itemUIdataContainer == nil then
        return 0
    end
    local count = self._itemUIdataContainer:GetCount()
    return count
end

function GunsmithSkinDefaultMainUI:_OnProcessItemWidget(position, widget)
    local itemUIData = self._itemUIdataContainer:Get(position)
    widget:UpdateUI(itemUIData, GunsmithSkinDefaultMainUI_ProcessUIOPCode.DefaultUI, self._skinData)
end

function GunsmithSkinDefaultMainUI:_OnGetMysticalItemCount()
    if self._itemUIdataContainer4Mystical == nil then
        return 0
    end
    local count = self._itemUIdataContainer4Mystical:GetCount()
    return count
end

function GunsmithSkinDefaultMainUI:_OnProcessMysticalItemWidget(position, widget)
    position = position + 1
    local itemUIData = self._itemUIdataContainer4Mystical:Get(position)
    widget:UpdateUI(itemUIData, GunsmithSkinDefaultMainUI_ProcessUIOPCode.MysticalUI, self._skinData)
end

function GunsmithSkinDefaultMainUI:_OnRegisterCameraEvent()
    local cameraActor = GunsmithUIContextLogic.GetCameraActor()
    if isinvalid(cameraActor) then
        return
    end
    self._touchMoveStartHandle = cameraActor.OnTouchMoveStart:Add(CreateCPlusCallBack(self._OnTouchMoveStart, self))
    self._touchMoveEndHandle = cameraActor.OnTouchMoveEnd:Add(CreateCPlusCallBack(self._OnTouchMoveEnd, self))
end

function GunsmithSkinDefaultMainUI:_OnUnRegisterCameraEvent()
    local cameraActor = GunsmithUIContextLogic.GetCameraActor()
    if isinvalid(cameraActor) then
        return
    end

    if self._touchMoveStartHandle then
        cameraActor.OnTouchMoveStart:Remove(self._touchMoveStartHandle)
        self._touchMoveStartHandle = nil
    end

    if self._touchMoveEndHandle then
        cameraActor.OnTouchMoveEnd:Remove(self._touchMoveEndHandle)
        self._touchMoveEndHandle = nil
    end
end

function GunsmithSkinDefaultMainUI:_OnTouchMoveStart()
end

function GunsmithSkinDefaultMainUI:_OnTouchMoveEnd()
    self:OnProcessSceneObject()
    --local index = self:_GetFocusItemUIDataIndex()
    --self:OnProcessUIUpdate(false, self._lastOPCode, index)
end

function GunsmithSkinDefaultMainUI:_OnButtonEquipClicked()
    self:_InternalProcessSync(false)
end

function GunsmithSkinDefaultMainUI:_OnButtonEquipAllClicked()
    local bIsRandomItem = self:_GetFocusItemIsRandomItem()
    if bIsRandomItem then
        self:_OnProcessRandomSkinClicked()
        return
    end

    local bIsLocked = self:_GetSkinLocked()
    if bIsLocked then
        self:_OnProcessSkinLockedClicked()
        return
    end
    local bIsMP = GunsmithUIContextLogic.GetGroupIDIsMP()
    local bApplyAll = true
    if bIsMP then
        bApplyAll = false
    end
    self:_InternalProcessSync(bApplyAll)
end

function GunsmithSkinDefaultMainUI:_OnProcessRandomSkinClicked()
    local weaponID = GunsmithUIContextLogic.GetItemID()
    local bEnable = not GunsmithSkinLogic.CheckIsRandomPoolEnabled(weaponID)
    GunsmithSkinLogic.EnableRandomSkin(weaponID, bEnable, fCustomCallback)
end

function GunsmithSkinDefaultMainUI:_OnButtonEquipAllDeClicked()
    local bIsRandomItem = self:_GetFocusItemIsRandomItem()
    if not bIsRandomItem then
        return
    end
    local weaponID = GunsmithUIContextLogic.GetItemID()
    local bAnySkinsInRandomPool = Module.Collection:CheckAreAnySkinsInRandomPool(weaponID) or false
    local IsRandomPoolEnabled = Module.Collection:CheckIsRandomPoolEnabled(weaponID) or false
    local bEnable = bAnySkinsInRandomPool and (not IsRandomPoolEnabled)
    if bEnable then
        return
    end
    Module.CommonTips:ShowSimpleTip(Module.Gunsmith.Config.Loc.GunsmithSkinDefaultMainUIButtenRandomAllEquippedTips)
end

function GunsmithSkinDefaultMainUI:_OnProcessSkinLockedClicked()
    local bIsValidNormalJumpID = self:_GetFocusItemUIDataIsValidNormalJumpID()
    local bIsValidNormalBuyPriceRichText = self:_GetFocusItemUIDataIsValidNormalBuyPriceRichText()
    if bIsValidNormalJumpID then
        -- 1.跳转获取
        self:_OnProcessJump()
        return
    elseif bIsValidNormalBuyPriceRichText then
        -- 2.直购获取
        self:_OnProcessDoShopProp()
        return
    end

    -- 0.敬请期待
    Module.CommonTips:ShowSimpleTip(Module.Gunsmith.Config.Loc.GunsmithComminSoon)
end

function GunsmithSkinDefaultMainUI:_OnProcessJump()
    local jumpID = self:_GetFocusItemUIDataNormalJumpID()
    local bCanJump = Module.Jump:CheckCanJumpByID(jumpID)
    if bCanJump then
        GunsmithUIContextLogic.JumpByJumpID(jumpID)
        return
    end
    local unlockTips = self:_GetFocusItemUIDataUnlockTips()
    Module.CommonTips:ShowSimpleTip(unlockTips)
end

function GunsmithSkinDefaultMainUI:_OnProcessDoShopProp()
    local skinID = self:_GetFocusItemID()

    local fCallback = function(singlePropMsg)
        local bSuccess = isvalid(singlePropMsg) and singlePropMsg.bSuccess
        if not bSuccess then
            return
        end
        if isvalid(self) and self.OnProcessUIUpdate then
            self:OnProcessUIUpdate(true, nil, nil, skinID)
        end
    end

    GunsmithUIContextLogic.DoShopProp(skinID, 1, CreateCallBack(fCallback, self))
end

function GunsmithSkinDefaultMainUI:_OnShowMandelBrickPage()
    -- local mandelBrickItemID = Module.Gunsmith.Config.Const.GUNSMITH_PENDANTMAINUI_MANDEBRICK_ITEMID
    local skinID = self:_GetFocusItemID()
    Module.Collection:ShowMandelBrickPageBySkinID(skinID)
end

function GunsmithSkinDefaultMainUI:_OnShowAuctionPage()
    Module.Collection:ShowMarketPage()
end

function GunsmithSkinDefaultMainUI:_OnShowMysticalWorkshopPage()
    Module.Collection:ShowMysticalWorkshopPage()
end

function GunsmithSkinDefaultMainUI:_OnButtonMissionSkinClicked()
    -- local skinID = self:_GetFocusItemID()
    -- Server.CollectionServer:RetrieveWeaponSkin(skinID)
    -- local tipsStr = Module.Gunsmith.Config.Loc.GunsmithSkinDefaultMissionObtainText
    -- Module.CommonTips:ShowSimpleTip(tipsStr)
    local tabIndex = 8
    Module.Collection:ShowMainPanel(tabIndex)
end

function GunsmithSkinDefaultMainUI:_OnButtonRetrieveWeaponSkinClicked()
    local skinID = self:_GetFocusItemID()
    Server.CollectionServer:RetrieveWeaponSkin(skinID)
end

function GunsmithSkinDefaultMainUI:_OnRandomButtonClicked()
    local weaponID = GunsmithUIContextLogic.GetItemID()
    Module.Collection:OpenRandomSkinUI(weaponID)
end

function GunsmithSkinDefaultMainUI:_OnRandomSkinButtonClicked()
    self:ProcessShortcutRandonSkin()
    -- local weaponID = GunsmithUIContextLogic.GetItemID()
    -- local skinID = self:_GetFocusItemID()
    -- local skinGUID = self:_GetFocusItemGUID()
    -- GunsmithSkinLogic.RandomSkin(weaponID, skinID, skinGUID, bChecked)
end

function GunsmithSkinDefaultMainUI:_OnCommonCheckBoxLockAppearanceStateUpdated(bSelected)
    if self._focusItemUIData == nil then
        return
    end
    local cppWeaponSkininfoParam = self._focusItemUIData:GetPbWeaponSkinInfoParam()
    if cppWeaponSkininfoParam == nil then
        return
    end

    local pbSkinInfo = pb.WeaponSkinInfo:New()
    WeaponAssemblyTool.SetPbWeaponSkinInfoParam2PBSkinInfo(cppWeaponSkininfoParam, pbSkinInfo)

    pbSkinInfo.transmog = bSelected
    Server.GunsmithServer:C2SCSWAssemblyModifySkinReq(pbSkinInfo, WAssemblyModifySkinOp.Transmog)
end

function GunsmithSkinDefaultMainUI:_OnCheckBoxEquipSolutionStateUpdated(bChecked)
    if self._bEquipSolution == bChecked then
        return
    end
    self._bEquipSolution = bChecked
    GunsmithSkinLogic.SaveEquipSolutionState(bChecked)

    local index = self:_GetFocusItemUIDataIndex()
    local bIsMystical = self._lastOPCode == GunsmithSkinDefaultMainUI_ProcessUIOPCode.MysticalUI
    bIsMystical = bIsMystical and isvalid(self._focusItemUIData)
    if bIsMystical then
        index = self._focusItemUIData:GetIndex()
    end
    self:OnProcessUIUpdate(false, self._lastOPCode, index)
end

function GunsmithSkinDefaultMainUI:_OnProcessGunsmithOnSkinDefaultItemUIClicked(uiData, opcode)
    local bEqual = self:_GetEqualFocusItemUIData(uiData, opcode)
    if bEqual then
        return
    end

    local index = uiData:GetIndex()
    self:OnProcessUIUpdate(false, opcode, index)
end

function GunsmithSkinDefaultMainUI:_OnProcessevtCSWAssemblyApplySkinRes()
    local bIsFromUserPropInfo = GunsmithUIContextLogic.GetIsFromUserPropInfo()
    if not bIsFromUserPropInfo then
        self:OnProcessUIUpdate(true)
        return
    end
    self:_InternalProcessSpecialUIUpdate()
end

function GunsmithSkinDefaultMainUI:_OnProcessevtCSAssemblyModifySkinRes()
    local bIsFromUserPropInfo = GunsmithUIContextLogic.GetIsFromUserPropInfo()
    if not bIsFromUserPropInfo then
        self:OnProcessUIUpdate(true)
        return
    end
    self:_InternalProcessSpecialUIUpdate()
end

function GunsmithSkinDefaultMainUI:_InternalProcessSpecialUIUpdate()
    local index = nil
    local opcode = nil
    local bIsMystical = self._lastOPCode == GunsmithSkinDefaultMainUI_ProcessUIOPCode.MysticalUI
    bIsMystical = bIsMystical and isvalid(self._focusItemUIData)
    if bIsMystical then
        index = self._focusItemUIData:GetIndex()
        opcode = self._lastOPCode
    end
    local skinID = self:_GetFocusItemID()
    self:_InternalSetSpecialRefreshParam(true, false, true, false)
    self:OnProcessUIUpdate(true, opcode, index, skinID)
    self:_InternalSetSpecialRefreshParam(false)
end

function GunsmithSkinDefaultMainUI:_OnProcessGunsmithOnRangeDataUpdated(uiParam)
    self:SetUIParam(uiParam)
    self:OnProcessUIUpdate(true)
end

function GunsmithSkinDefaultMainUI:_ModuleDownloadResult(moduleName, bSuccess, errorCode)
    local skinID = self:_GetFocusItemID()
    local pakCategory = Module.ExpansionPackCoordinator:GetDownloadCategary(skinID)
    if pakCategory == nil or pakCategory == "None" or pakCategory ~= moduleName then
        return
    end

    if bSuccess then
        local questName = Module.ExpansionPackCoordinator:GetQuestNameByModuleName(moduleName)
        if questName == nil then
            questName = ""
        end
        local successTips = string.format(Module.Gunsmith.Config.Loc.GunsmithSkinDownloadSuccessTips, questName)
        Module.CommonTips:ShowSimpleTip(successTips)
    end

    local index = self:_GetFocusItemUIDataIndex()
    self:OnProcessUIUpdate(false, nil, index)
end

function GunsmithSkinDefaultMainUI:_OnPackStateChanged(moduleName, packState)
    local isSuccess = packState == Module.ExpansionPackCoordinator.Config.PackState.Installed
    self:_ModuleDownloadResult(moduleName, isSuccess, 0)
end

function GunsmithSkinDefaultMainUI:_OnUpdateCollectionData(itemsChanged, bAddOrRemove)
    local bChangeSkin = false
    for index, item in ipairs(itemsChanged) do
        local bIsWeaponSkin = item.itemMainType == EItemType.WeaponSkin
        if bIsWeaponSkin then
            bChangeSkin = true
            break
        end
    end
    if not bChangeSkin then
        return
    end
    local lastSkinID = self:_GetFocusItemID()
    self:OnProcessUIUpdate(true, nil, nil, lastSkinID)
end

function GunsmithSkinDefaultMainUI:_OnProcessServerDataUpdated()
    self:OnProcessUIUpdate(true)
end

function GunsmithSkinDefaultMainUI:OnProcessRelayConnected()
    -- self:OnProcessUIUpdate(true)
    self:_InternalProcessSpecialUIUpdate()
end

function GunsmithSkinDefaultMainUI:_OnProcessGunsmithOnSimulateDataUpdated()
    local index = nil
    local opcode = nil
    local bIsMystical = self._lastOPCode == GunsmithSkinDefaultMainUI_ProcessUIOPCode.MysticalUI
    bIsMystical = bIsMystical and isvalid(self._focusItemUIData)
    if bIsMystical then
        index = self._focusItemUIData:GetIndex()
        opcode = self._lastOPCode
    end
    self:OnProcessUIUpdate(true, opcode, index)
    -- self:OnProcessUIUpdate(true)
end

function GunsmithSkinDefaultMainUI:_OnProcessRandomSkinPoolUpdate()
    self:_InternalProcessSpecialUIUpdate()
end

function GunsmithSkinDefaultMainUI:_OnRandomSkinPoolToggled()
    self:OnProcessUIUpdate(true)
end

function GunsmithSkinDefaultMainUI:OpenFromMainUI(bFromUserClicked)
    self:OnProcessUIUpdate(true) 
end

function GunsmithSkinDefaultMainUI:OnForceProcessUI()
    GunsmithUIContextLogic.ProcessContext()

    self._focusItemUIData = nil
    self._searchUIDataParam:Reset()
    self._searchUIDataParam:SetDataContainer(self._itemUIdataContainer)
    self._searchUIDataParam:SetSkinData(self._skinData)
    -- self._searchUIDataParam:SetSortType(self._sortType)
    GunsmithSkinLogic.GetDefaultItemUIDatasFromContext(self._searchUIDataParam)
end

function GunsmithSkinDefaultMainUI:OnForceProcessMyticalUI(focusUIData)
    self:_InternalOnForceProcessMyticalUI(focusUIData)

    local count = self._itemUIdataContainer4Mystical:GetCount()
    local focusIndex = count > 0 and 1 or 0
    return focusIndex
end

function GunsmithSkinDefaultMainUI:_InternalOnForceProcessMyticalUI(focusUIData)
    local skinID = 0
    if focusUIData ~= nil then
        skinID = focusUIData:GetID()
    end
    self:_InternalOnForceProcessMyticalUIBySkinID(skinID)
end

function GunsmithSkinDefaultMainUI:_InternalOnForceProcessMyticalUIBySkinID(skinID)
    skinID = setdefault(skinID, 0)
    self._searchUIDataParam:Reset()
    self._searchUIDataParam:SetDataContainer(self._itemUIdataContainer4Mystical)
    self._searchUIDataParam:SetSkinData(self._skinData)

    self._searchUIDataParam:SetSkinID(skinID)
    GunsmithSkinLogic.GetMysticalItemUIDatasFromContext4Skin(self._searchUIDataParam)
end

function GunsmithSkinDefaultMainUI:OnPreProcessUI(opcode, index, itemID)
    -- BEGIN MODIFICATION @ VIRTUOS 
    if IsHD() then
        self:_OnPreSetGamepadFocusWidget()
    end
    -- END MODIFICATION 

    if opcode == nil then
        opcode = GunsmithSkinDefaultMainUI_ProcessUIOPCode.DefaultUI
        index = self:_InternalGetIndexByitemID(itemID)
    end

    local focusMysticalIndex = 0
    local uiData = self._focusItemUIData
    if opcode == GunsmithSkinDefaultMainUI_ProcessUIOPCode.DefaultUI then
        uiData = self._itemUIdataContainer:Get(index)
        focusMysticalIndex = self:OnForceProcessMyticalUI(uiData)
    else
        local bIsSpecialWaterFallRefresh = self:IsSpecialWaterFallRefresh()
        local bIsMysticalWaterFallRefreshData = self:IsMysticalWaterFallRefreshData()
        local bRefreshData = bIsSpecialWaterFallRefresh and bIsMysticalWaterFallRefreshData
        if bRefreshData then
            self:_InternalOnForceProcessMyticalUIBySkinID(itemID)
        end
        focusMysticalIndex = index
    end

    local code = GunsmithSkinDefaultMainUI_ProcessUIOPCode.DefaultUI
    if focusMysticalIndex > 0 then
        uiData = self._itemUIdataContainer4Mystical:Get(focusMysticalIndex)
        code = GunsmithSkinDefaultMainUI_ProcessUIOPCode.MysticalUI
    end

    self._focusItemUIData = uiData
    self._lastOPCode = opcode

    local bHasSkinInfo = self:_GetFocusItemHasSkinInfo()
    if bHasSkinInfo then
        return
    end
    GunsmithSkinLogic.SetGunsmithSkinDefaultMainItemUIData4SkinInfo(self._focusItemUIData, self._skinData)
end

function GunsmithSkinDefaultMainUI:_InternalGetIndexByitemID(itemID)
    local index = 1
    if itemID == nil then
        local bIsRandomEnabled = GunsmithSkinLogic.IsRandomEnabled()
        if bIsRandomEnabled then
            itemID = GunsmithUIContextLogic.GetItemID()
        else
            itemID = GunsmithSkinLogic.GetEquippedSkinID()
        end
    end

    if itemID == nil then
        return index
    end

    local bContained, itemData = self._itemUIdataContainer:ContainsFromID(itemID)
    if not bContained then
        return index
    end

    index = itemData:GetIndex()
    return index
end

function GunsmithSkinDefaultMainUI:OnProcessUI(opcode, index)
    local bForce = self:GetIsForceUIUpdate()
    local bScrollToStart = opcode == nil or bForce

    -- 特殊处理
    local bIsSpecialWaterFallRefresh = self:IsSpecialWaterFallRefresh()
    if bIsSpecialWaterFallRefresh then
        bScrollToStart = self:IsWaterFallRefreshAllUI()
    end

    local focusItemID = self:_GetFocusItemID()
    -- local bAlreadyFocused = self:_InternalGetFocused(focusItemID)
    -- -- 如果是已经设置聚焦了不需要重新刷新下
    -- bScrollToStart = bScrollToStart or bForce

    self._itemUIdataContainer:SetFocusFromID(focusItemID)
    local focusGUID = self:_GetFocusItemGUID()
    self._itemUIdataContainer4Mystical:SetFocusFromGUID(focusGUID)
    if bScrollToStart then
        self._wt_WaterFallList:RefreshAllItems()
        -- self._wt_WaterFallList:ScrollToStart()
    else
        self._wt_WaterFallList:RefreshVisibleItem()
    end

    self:_InternalOnProcessUIMyticals(opcode)

    self:_InternalOnProcessUIMissions(opcode)

    self:_InternalOnProcessUIButtons()

    self:_InternalUpdateRandomButtonUI()

    self:_InternalOnProcessUIDownload()

    self:_InternalUpdateEquipSolution() 
end

function GunsmithSkinDefaultMainUI:OnPostProcessUI()
    self:_InternalUpdateSimulateTip()

    -- BEGIN MODIFICATION @ VIRTUOS : 在Button刷新后再设置快捷键
    if IsHD() then
        self:_InitShortcuts()
        self:_OnSetGamepadFocusWidget()
    end
    -- END MODIFICATION  
end

function GunsmithSkinDefaultMainUI:OnPreProcessSceneObject()
    if self._focusItemUIData == nil then
        return
    end

    -- 1.处理方案来源
    local backUpWeaponDescription = GunsmithUIContextLogic.GetWeaponDescription4Backup()
    local  weaponDescription = nil
    if self._bEquipSolution then
        weaponDescription = self:_GetFocusWeaponDescription()
    else
        weaponDescription = backUpWeaponDescription
    end
    GunsmithUIContextLogic.SetFrontendFromWeaponDescription(weaponDescription)

    local skinInfoParam = nil
    local skinID = self:_GetFocusItemID()
    local bDownloaded = self:_InternalIsDownloaded(skinID)
    -- 2.在处理外观
    -- 2.1 处理皮肤
    if bDownloaded then
        -- 获取选中皮肤数据
        skinInfoParam = self:_GetFocusItemUIDataSkinInfoParam()
    else -- 未下载用黑铁数据
        -- 获取黑铁皮肤数据
        skinInfoParam = self:_GetDefaultItemUIDataSkinInfoParam()
    end

    -- 未获得有效数据，强制黑铁
    local bIsValid = isvalid(skinInfoParam)
    if not bIsValid then
        skinInfoParam = FPbWeaponSkinInfoParam()
        logerror("GunsmithSkinDefaultMainUI:OnPreProcessSceneObject: skinInfoParam is nil -- error --- skinID: ", skinID)
    end

    -- 2.2 处理皮肤以外的其他外观数据
    skinInfoParam = self:_InternalSetSkinInfoParamExceptSkinInfo(backUpWeaponDescription, skinInfoParam)
    GunsmithUIContextLogic.ProcessSetSkinInfoParam(skinInfoParam)
end

function GunsmithSkinDefaultMainUI:_InternalSetSkinInfoParamExceptSkinInfo(weaponDescription, skinInfoParam)
    -- 处理挂饰(如果后续有其他外观属性需要添加处理)
    skinInfoParam = WeaponAssemblyTool.SetPendantInfoFromWeaponDesc(weaponDescription, skinInfoParam)
    return skinInfoParam
end

function GunsmithSkinDefaultMainUI:OnProcessSceneObject()
    local cameraPoint = self:_InternalGetCameraPoint()
    GunsmithUIContextLogic.SetCameraType(EAssemblerCameraType.ROTATE_ROOT)
    GunsmithUIContextLogic.SetCameraDefaultPoint(cameraPoint)
    GunsmithUIContextLogic.SetFocusSocket(nil)

    GunsmithUI.OnProcessSceneObject(self)
end

function GunsmithSkinDefaultMainUI:_InternalGetCameraPoint()
    local cameraPoint = EAssemblerCamPoint.POINT_GUN_SKIN_DEFAULT
    return cameraPoint
end

function GunsmithSkinDefaultMainUI:_InternalOnProcessUISortDropDown()
    -- local dropDownDataList = Module.Gunsmith.Config.SkinMainUISortConfig
    -- UIUtil.InitDropDownBox(self._wtSortDropDown, dropDownDataList, {}, self._sortType)
    -- self._wtSortDropDown:BP_SwitchCheckButtonState(ECheckButtonState.Unchecked)
    -- local sortText = dropDownDataList[self._sortType]
    -- self._wtSortDropDown:BP_SetMainTabText(sortText)
end

function GunsmithSkinDefaultMainUI:_InternalOnProcessUIMyticals(opcode)
    local skinID = self:_GetFocusItemID()
    local bMysticalSkin = ItemHelperTool.IsMysticalSkin(skinID)
    local bLocked = self:_GetSkinLocked()
    local bShowMysticalSkinDropDown = bMysticalSkin and (not bLocked)
    self._wtMysticalSkinDropDown:SetActive(bShowMysticalSkinDropDown)

    local mysticalSkinNum = self._itemUIdataContainer4Mystical:GetCount()
    local bEnableMystical = mysticalSkinNum > 0
    if not bEnableMystical then
        self._wtMysticalSkinDropDown:SwitchCheckButtonState(ECheckButtonState.Unchecked)
        self._wtMysticalSkinDropDown:RefreshTab()
        return
    end

    -- BEGIN MODIFICATION @ VIRTUOS : 
    if IsHD() and bShowMysticalSkinDropDown then
        -- 设置下拉框图标和快捷键
        self._wtMysticalSkinDropDown:EnableDropDownShortcutWithAction(true, "Common_RightStickClick_Gamepad")
        self._RotationVec = FVector2D(0, 0)
    end
    -- END MODIFICATION

    local dropDownBoxTitle = string.format(Module.Gunsmith.Config.Loc.DropDownBoxTitle, tostring(mysticalSkinNum))
    self._wtMysticalSkinDropDown:SetMainTabText(dropDownBoxTitle)

    local bRebuildMystical = opcode ~= GunsmithSkinDefaultMainUI_ProcessUIOPCode.MysticalUI
    if bRebuildMystical then
        self._wtMysticalSkinDropDown:SwitchCheckButtonState(ECheckButtonState.Unchecked)
        self._wtMysticalSkinDropDown:RefreshTab()
        return
    end

    local bIsSpecialWaterFallRefresh = self:IsSpecialWaterFallRefresh()
    local bIsMysticalWaterFallVisibleItemsNotReBuild = self:IsMysticalWaterFallVisibleItemsNotReBuild()
    local bRefreshVisibleItems = bIsSpecialWaterFallRefresh and (not bIsMysticalWaterFallVisibleItemsNotReBuild)
    if bRefreshVisibleItems then
        self._wtMysticalSkinDropDown:RefreshVisibleItems()
        return
    end

    self._wtMysticalSkinDropDown:RefreshVisibleItemsNotReBuild()
end

function GunsmithSkinDefaultMainUI:_InternalOnProcessUIMissions(opcode)
    self._missionSkinID = nil
    self._bCanRetrieve = nil
    self._wt_WBP_CommonButtonV1S1_Retrieve:SetActive(false)
    self._wt_WBP_CommonButtonV1S1_Mission:SetActive(false)

    -- BEGIN MODIFICATION @ VIRTUOS : 在Button刷新后再设置快捷键
    if IsHD() then
        self._wt_WBP_DFCommon_MissionUnlocked.OpenUnlockWeaponTip = false
    end
    -- END MODIFICATION  
    local bLocked = self:_GetSkinLocked()

    self._wt_WBP_DFCommon_MissionUnlocked:SetActive(bLocked)
    if not bLocked then
        return
    end

    local skinID = self:_GetFocusItemID()
    local weaponSkinMissionStatusCallback = CreateCallBack(self._OnWeaponSkinMissionStatusLoaded, self)
    self._wt_WBP_DFCommon_MissionUnlocked:LoadWeaponSkinProgress(skinID, weaponSkinMissionStatusCallback)
end

function GunsmithSkinDefaultMainUI:_OnWeaponSkinMissionStatusLoaded(skinID, bCanRetrieve)
    self._missionSkinID = skinID
    self._bCanRetrieve = bCanRetrieve
    local focusSkinID = self:_GetFocusItemID()
    if focusSkinID ~= skinID then
        return
    end
    if not bCanRetrieve then
        self:_InternalOnProcessUIMissionButtonUI()
        return
    end
    self._wt_WBP_CommonButtonV1S1_Retrieve:SetActive(true)
    self._wt_WBP_CommonButtonV1S1_Retrieve:SetIsEnabledStyle(true)
    self._wt_Button_Equip:SetActive(false)
    self._wt_Button_EquipAll:SetActive(false)
    self._wt_WBP_CommonButtonV1S1_Mission:SetActive(false)
    -- self._wt_Button_UnequipAll:SetActive(false)

    -- BEGIN MODIFICATION @ VIRTUOS : 领取枪皮的输入
    if IsHD() then
        self._Retrieve = self:_InternalRemoveShortcut(self._Retrieve)
        self._Equip = self:_InternalRemoveShortcut(self._Equip)
        self._EquipAll = self:_InternalRemoveShortcut(self._EquipAll)
        self._Random = self:_InternalRemoveShortcut(self._Random)
        self._Mission = self:_InternalRemoveShortcut(self._Mission)

        self._Retrieve = self:AddInputActionBinding("RetrieveSkin_Gamepad", EInputEvent.IE_Pressed, self._RetrieveClicked, self, EDisplayInputActionPriority.UI_Stack)
        self._wt_WBP_CommonButtonV1S1_Retrieve:SetDisplayInputAction("RetrieveSkin_Gamepad", true, nil, true)
    end
    -- END MODIFICATION
end

function GunsmithSkinDefaultMainUI:_InternalOnProcessUIMissionButtonUI()
    local skinID = self:_GetFocusItemID()
    local bIsMissionSkin = self._missionSkinID and self._missionSkinID == skinID
    local bCanRetrieve = self._bCanRetrieve
    local bShow = bIsMissionSkin and not bCanRetrieve
    if not bShow then
        return
    end

    self._wt_Button_Equip:SetActive(false)
    self._wt_Button_EquipAll:SetActive(false)
    self._wt_WBP_CommonButtonV1S1_Retrieve:SetActive(false)

    local bIsEnabled = true
    local text = Module.Gunsmith.Config.Loc.GunsmithSkinDefaultMissionTitleText
    self._wt_WBP_CommonButtonV1S1_Mission:SetActive(bShow)
    self._wt_WBP_CommonButtonV1S1_Mission:SetIsEnabledStyle(bIsEnabled)
    self._wt_WBP_CommonButtonV1S1_Mission:SetMainTitle(text)

    self:_InternalOnProcessUIObtainText(true)

    -- BEGIN MODIFICATION @ VIRTUOS : 领取枪皮的输入
    if IsHD() then
        self._Retrieve = self:_InternalRemoveShortcut(self._Retrieve)
        self._Equip = self:_InternalRemoveShortcut(self._Equip)
        self._EquipAll = self:_InternalRemoveShortcut(self._EquipAll)
        self._Random = self:_InternalRemoveShortcut(self._Random)
        self._Mission = self:_InternalRemoveShortcut(self._Mission)

        self._Mission = self:AddInputActionBinding("MissionSkin_Gamepad", EInputEvent.IE_Pressed, self._MissionClicked, self, EDisplayInputActionPriority.UI_Stack)
        self._wt_WBP_CommonButtonV1S1_Mission:SetDisplayInputAction("MissionSkin_Gamepad", true, nil, true)
    end
    -- END MODIFICATION
end

function GunsmithSkinDefaultMainUI:_InternalOnProcessUIButtons()
    local bIsRandomItem = self:_GetFocusItemIsRandomItem()
    if bIsRandomItem then
        self:_InternalOnProcessUIButtonsForRandom()
        return
    end

    if IsHD() then
        self._wt_WBP_RandomButton_PC:SetActive(false)
    else
        self._wt_WBP_RandomButton_Moblie:SetActive(false)
    end

    local bEquipped, bEquippedAllDisabled, bLocked, bIsMP, bIsSimulate = self:_GetUIButtonsActive()

    self:_InternalUpdateEquipStype(bEquipped, bLocked, bIsMP, bIsSimulate)
    self:_InternalUpdateEquipAllStype(bEquippedAllDisabled, bLocked, bIsMP)

    -- self._wt_WBP_CommonButtonV1S1_Retrieve:SetActive(false)
    -- self._wt_WBP_CommonButtonV1S1_Retrieve:SetIsEnabledStyle(false)

    self:_InternalOnProcessUIObtainText(bLocked)
    self:_InternalOnProcessUIObtain(bLocked)

    -- self._wt_WBP_CommonCheckBox_LockAppearance:SetActive(bLockAppearance)
    -- self._wt_WBP_CommonCheckBox_LockAppearance:SetIsChecked(LockAppearanceState, false)
end

function GunsmithSkinDefaultMainUI:_InternalOnProcessUIButtonsForRandom()
    -- self._wt_WBP_CommonButtonV1S1_Retrieve:SetActive(false)
    self._wt_Button_Equip:SetActive(false)

    self:_InternalOnProcessUIObtainText(false)
    self:_InternalOnProcessUIObtain(false)

    if IsHD() then
        self._wt_WBP_RandomButton_PC:SetActive(true)
    else
        self._wt_WBP_RandomButton_Moblie:SetActive(true)
    end

    local bShow = true
    self._wt_Button_EquipAll:SetActive(bShow)

    local weaponID = GunsmithUIContextLogic.GetItemID()
    local bAnySkinsInRandomPool = Module.Collection:CheckAreAnySkinsInRandomPool(weaponID) or false
    local IsRandomPoolEnabled = Module.Collection:CheckIsRandomPoolEnabled(weaponID) or false
    local bEnable = bAnySkinsInRandomPool and (not IsRandomPoolEnabled)
    self._wt_Button_EquipAll:SetIsEnabledStyle(bEnable)

    local bIsMP = GunsmithUIContextLogic.GetGroupIDIsMP()
    local btnText
    if bIsMP then
        if IsRandomPoolEnabled then
            btnText = Module.Gunsmith.Config.Loc.GunsmithSkinDefaultMainUIButtenRandomEquippedText
        else
            btnText = Module.Gunsmith.Config.Loc.GunsmithSkinDefaultMainUIButtenOnlyEquipText
        end
    else
        if IsRandomPoolEnabled then
            btnText = Module.Gunsmith.Config.Loc.GunsmithSkinDefaultMainUIButtenRandomAllEquippedText
        else
            btnText = Module.Gunsmith.Config.Loc.GunsmithSkinDefaultMainUIButtenAllEquipText
        end
    end
    self._wt_Button_EquipAll:SetMainTitle(btnText)
end

function GunsmithSkinDefaultMainUI:_InternalUpdateRandomButtonUI()
    if IsHD() then
        return
    end
    local bShow = self:_InternalShowRandomButton()
    self._wtRandomBtn_Moblie:SetActive(bShow)
    if not bShow then
        return
    end
    local bIsInRandomPool = self:_InternalFocusItemInRandomPool()
    -- self._wtRandomBtn_Moblie:SetIsChecked(bIsInRandomPool)
    local iconPath = Module.Gunsmith.Config.Const.GUNSMITH_SKINMAINUI_RANDOM_BUTTON_CHECKED_ICON
    if not bIsInRandomPool then
        iconPath = Module.Gunsmith.Config.Const.GUNSMITH_SKINMAINUI_RANDOM_BUTTON_UNCHECKED_ICON
    end
    self._wtRandomBtn_Moblie:AsyncSetImageIconPathAllState(iconPath)
end

function GunsmithSkinDefaultMainUI:_InternalUpdateEquipStype(bEquipped, bLocked, bIsMP, bIsSimulate)
    local skinID = self:_GetFocusItemID()
    local bIsMissionSkin = self._missionSkinID and self._missionSkinID == skinID

    local bShow = (not (bLocked or bIsMP or bIsSimulate or (bIsMissionSkin and bLocked)) )
    self._wt_Button_Equip:SetActive(bShow)
    if not bShow then
        return
    end

    if bEquipped then
        self._wt_Button_Equip:SetIsEnabledStyle(false)
    else
        self._wt_Button_Equip:SetIsEnabledStyle(true)
    end

    local curText = Module.Gunsmith.Config.Loc.GunsmithSkinDefaultMainUIButtenOnlyEquipText
    if bEquipped then
        curText = Module.Gunsmith.Config.Loc.GunsmithSkinDefaultMainUIButtenOnlyEquippedText
    end
    self._wt_Button_Equip:SetMainTitle(curText)
end

function GunsmithSkinDefaultMainUI:_InternalUpdateEquipAllStype(bEquippedAllDisabled, bLocked, bIsMP)
    local skinID = self:_GetFocusItemID()
    local bMysticalSkin = ItemHelperTool.IsMysticalSkin(skinID)

    local bIsMissionSkin = self._missionSkinID and self._missionSkinID == skinID

    local bShow = (not ((bIsMissionSkin and bLocked) or (bMysticalSkin and bLocked)))
    self._wt_Button_EquipAll:SetActive(bShow)
    if not bShow then
        return
    end

    -- 跳转获取途径
    local bIsValidNormalJumpID = self:_GetFocusItemUIDataIsValidNormalJumpID()
    -- 直购途径
    local bIsValidNormalBuyPriceRichText = self:_GetFocusItemUIDataIsValidNormalBuyPriceRichText()

    local bIsEnabled = (bLocked and (bIsValidNormalJumpID or bIsValidNormalBuyPriceRichText )) or
                        ((not bLocked) and (not bEquippedAllDisabled))
    self._wt_Button_EquipAll:SetIsEnabledStyle(bIsEnabled)

    local text
    if bLocked then
        if bIsValidNormalJumpID then
            text = Module.Gunsmith.Config.Loc.GunsmithSkinDefaultMainUIButtenNormalObtain
        elseif bIsValidNormalBuyPriceRichText then
            text = self:_GetFocusItemUIDataNormalBuyPriceRichText()
        -- elseif bIsMissionSkin and (not bCanRetrieve) then
        --     text = Module.Gunsmith.Config.Loc.GunsmithSkinDefaultMissionTitleText
        else
            text = Module.Gunsmith.Config.Loc.GunsmithSkinDefaultLockTitleText
        end
        -- text = Module.Gunsmith.Config.Loc.GunsmithSkinDefaultReceiveText
    else
        if bIsMP then
            text = Module.Gunsmith.Config.Loc.GunsmithSkinDefaultMainUIButtenOnlyEquipText
            if bEquippedAllDisabled then
                text = Module.Gunsmith.Config.Loc.GunsmithSkinDefaultMainUIButtenOnlyEquippedText
            end
        else
            text = Module.Gunsmith.Config.Loc.GunsmithSkinDefaultMainUIButtenAllEquipText
            if bEquippedAllDisabled then
                text = Module.Gunsmith.Config.Loc.GunsmithSkinDefaultMainUIButtenAllEquippedText
            end
        end
    end
    self._wt_Button_EquipAll:SetMainTitle(text)
end

function GunsmithSkinDefaultMainUI:_InternalOnProcessUIObtainText(bLocked)
    local bIsRandomItem = self:_GetFocusItemIsRandomItem()
    local bIsValidNormalJumpID = self:_GetFocusItemUIDataIsValidNormalJumpID()
    local bIsValidNormalObtainDesc = self:_GetFocusItemUIDataIsValidNormalObtainDesc()
    local bIsValidNormalLockDesc = self:_GetFocusItemUIDataIsValidNormalLockDesc()

    local skinID = self:_GetFocusItemID()
    local bIsMissionSkin = self._missionSkinID and self._missionSkinID == skinID
    local bShowMission = bIsMissionSkin and (not self._bCanRetrieve)

    local bShow = ((not bIsRandomItem) and bLocked and ((bIsValidNormalJumpID and bIsValidNormalObtainDesc) or (not bIsValidNormalJumpID and bIsValidNormalLockDesc))) or bShowMission         

    self._wtAlertHintBox:SetActive(bShow)
    if not bShow then
        return
    end
    local obtainDescStr = self:_GetFocusItemUIDataNormalObtainDesc()
    if bShowMission then
        obtainDescStr = Module.Gunsmith.Config.Loc.GunsmithSkinDefaultMissionObtainText
    elseif not bIsValidNormalJumpID then
        obtainDescStr = self:_GetFocusItemUIDataNormalLockDesc()
    end
    local bIsValid = obtainDescStr and obtainDescStr ~= "" and string.len(obtainDescStr) > 0
    if not bIsValid then
        self._wtAlertHintBox:SetActive(false)
        return
    end
    self._wtAlertHintTxt:SetText(obtainDescStr)
end

function GunsmithSkinDefaultMainUI:_InternalOnProcessUIObtain(bLocked)
    local bIsRandomItem = self:_GetFocusItemIsRandomItem()
    local skinID = self:_GetFocusItemID()
    local bMysticalSkin = ItemHelperTool.IsMysticalSkin(skinID)
    local bShow = (not bIsRandomItem) and bMysticalSkin and bLocked
    self._wtMysticalSkinBtnPanel:SetActive(bShow)
    if not bShow then
        return
    end

    local mandelBrickNum = 0
    local mandelBrickItemID = Module.Collection:GetMandelBrickIdBySkinId(skinID)
    local mandelBrickItem = Server.CollectionServer:GetCollectionItemById(mandelBrickItemID)
    local bIsValid = isvalid(mandelBrickItem) and mandelBrickItem.num
    if bIsValid then
        mandelBrickNum = mandelBrickItem.num
    end
    local mandelBrickIconPath = Module.Gunsmith.Config.Const.GUNSMITH_PENDANTMAINUI_MANDEBRICK_ICON
    local titleStr = string.format(Module.Gunsmith.Config.Loc.GunsmithPendantMainUIMandelDraw, tostring(mandelBrickNum))
    self._wtFromMandelBrickBtn:SetInfo(titleStr, mandelBrickIconPath)

    local auctionIconPath = Module.Gunsmith.Config.Const.GUNSMITH_PENDANTMAINUI_AUCTION_ICON
    self._wtFromAuctionBtn:SetInfo(Module.Gunsmith.Config.Loc.GunsmithPendantMainUIFromMarket, auctionIconPath)

    local matrixWorkshopIconPath = Module.Gunsmith.Config.Const.GUNSMITH_PENDANTMAINUI_MATRIXWORKSHOP_ICON
    self._wtFromMatrixWorkshopBtn:SetInfo(Module.Gunsmith.Config.Loc.GunsmithPendantMainUIFromMatrixWorkshop, matrixWorkshopIconPath)
end

function GunsmithSkinDefaultMainUI:_InternalOnProcessUIDownload()
    if self._wtCommonDownload ~= nil then
        local skinID = self:_GetFocusItemID()
        local bDownloaded = self:_InternalIsDownloaded(skinID)
        if bDownloaded then
            self._wtCommonDownload:Hidden()
        else
            self._wtCommonDownload:Visible()
        end
    end
end

function GunsmithSkinDefaultMainUI:_InternalIsDownloaded(skinID)
    if self._wtCommonDownload == nil then
        return true
    end
    local pakCategory = Module.ExpansionPackCoordinator:GetDownloadCategary(skinID)
    return pakCategory == nil or pakCategory == "None" or self._wtCommonDownload:InitModuleKey(pakCategory)
end

function GunsmithSkinDefaultMainUI:_InternalUpdateEquipSolution()
    local bUnlocked = not self:_GetSkinLocked()
    local bShow = bUnlocked and (not self:_GetFocusItemIsRandomItem())
    self._wt_WBP_CommonCheckBox_EquipSolution:SetActive(bShow)
    if not bShow then
        return
    end
    self._bEquipSolution = GunsmithSkinLogic.GetEquipSolutionState()
    self._wt_WBP_CommonCheckBox_EquipSolution:SetIsChecked(self._bEquipSolution)
end

function GunsmithSkinDefaultMainUI:_InternalProcessSync(bApplyAll)
    if self._focusItemUIData == nil then
        return
    end

    -- 使用皮肤方案
    local bOpenSkinSolutionUI = not bApplyAll and self._bEquipSolution
    if bOpenSkinSolutionUI then
        local skinID = self:_GetFocusItemID()
        local skinWeaponDescription = self:_GetFocusWeaponDescription()
        GunsmithSolutionMainLogic.OpenSkinSolutionUI(skinID, skinWeaponDescription)
        return
    end

    local skinID = self:_GetFocusItemID()
    local skinGUID = self:_GetFocusItemGUID()
    local bMysticalSkin = ItemHelperTool.IsMysticalSkin(skinID)
    local bWarning = (not bMysticalSkin) and (skinGUID == nil or skinGUID ~= 0)
    if bWarning then
        skinGUID = 0
    end

    local bIsRange = GunsmithUIContextLogic.GetGroupIDIsRange()
    if bIsRange then
        GunsmithUIContextLogic.RangeSetSkinID(skinID, skinGUID, bApplyAll)
    else
        local commond, dataType = GunsmithSkinLogic.GetPBWeaponApplySkinCmdFromContext(skinID, skinGUID, bApplyAll)
        if commond == nil then
            return
        end
        Server.GunsmithServer:C2SCSWAssemblyApplySkinReq(dataType, commond, CreateCallBack(self._InternalCancelWeaponRandomSkin, self, bApplyAll))
    end

    if bApplyAll then
        GunsmithTLogLogic.TLogSkinDefaultSkin(skinID)
    else
        GunsmithTLogLogic.TLogSkinApplyOne(skinID)
    end
end

function GunsmithSkinDefaultMainUI:_InternalCancelWeaponRandomSkin(bApplyAll, res)
    if res == nil or res.result ~= 0 then
        return
    end
    local weaponID = GunsmithUIContextLogic.GetItemID()
    local bEnable = GunsmithSkinLogic.CheckIsRandomPoolEnabled(weaponID)
    if not bEnable then
        return
    end

    local bIsMP = GunsmithUIContextLogic.GetGroupIDIsMP()
    -- MP模式下应用枪皮和SOL模式下应用枪皮均触发取消随机枪皮
    local bCancel = bIsMP or bApplyAll
    if not bCancel then
        return
    end
    GunsmithSkinLogic.EnableRandomSkin(weaponID, false)
end

function GunsmithSkinDefaultMainUI:_InternalUpdateSimulateTip()
    self._wt_WBP_GunStand_GunsmithSimulateStatePropmtUI:UpdateUI()
end

function GunsmithSkinDefaultMainUI:_GetItemUIData(index)
    if self._itemUIdataContainer == nil then
        return nil
    end
    return self._itemUIdataContainer:Get(index)
end

function GunsmithSkinDefaultMainUI:_GetEqualFocusItemUIData(uiData, opcode)
    if uiData == nil then
        return true
    end

    if opcode == GunsmithSkinDefaultMainUI_ProcessUIOPCode.MysticalUI then
        local lhsItemGUID = self:_GetFocusItemGUID()
        local rhsItemGUID = uiData:GetGUID()
        return lhsItemGUID == rhsItemGUID
    end

    local lhsItemID = self:_GetFocusItemID()
    local rhsItemID = uiData:GetID()
    return lhsItemID == rhsItemID
end

function GunsmithSkinDefaultMainUI:_GetFocusItemID()
    if self._focusItemUIData == nil then
        return -1
    end
    local id = self._focusItemUIData:GetID()
    return id
end

function GunsmithSkinDefaultMainUI:_GetFocusItemGUID()
    if self._focusItemUIData == nil then
        return -1
    end
    local guid = self._focusItemUIData:GetGUID()
    return guid
end

function GunsmithSkinDefaultMainUI:_GetFocusWeaponDescription()
    if self._focusItemUIData == nil then
        return
    end
    local weaponDescription = self._focusItemUIData:GetWeaponDescription()
    return weaponDescription
end

function GunsmithSkinDefaultMainUI:_GetFocusItemUIDataSkinInfoParam()
    if self._focusItemUIData == nil then
        return nil
    end
    local skinInfoParam = self._focusItemUIData:GetPbWeaponSkinInfoParam()
    return skinInfoParam
end

-- 黑铁皮肤数据
function GunsmithSkinDefaultMainUI:_GetDefaultItemUIDataSkinInfoParam()
    local bContained, itemData = self._itemUIdataContainer:ContainsFromID(0)
    local bIsValid = bContained and isvalid(itemData)
    if not bIsValid then
        return nil
    end
    local skinInfoParam = itemData:GetPbWeaponSkinInfoParam()
    return skinInfoParam
end

function GunsmithSkinDefaultMainUI:_GetFocusItemUIDataIndex()
    if self._focusItemUIData == nil then
        return 1
    end

    local id = self._focusItemUIData:GetID()
    local bContained, itemData = self._itemUIdataContainer:ContainsFromID(id)
    if not bContained then
        return 1
    end

    local index = itemData:GetIndex()
    return index
end

function GunsmithSkinDefaultMainUI:_GetSkinLockAllAppearance()
    if self._focusItemUIData == nil then
        return false
    end
    local weaponDescription = self._focusItemUIData:GetWeaponDescription()
    if isinvalid(weaponDescription) then
        return false
    end
    return weaponDescription.bLockAllAppearance
end

function GunsmithSkinDefaultMainUI:_GetSkinLocked()
    if self._focusItemUIData == nil then
        return true
    end
    local bLocked = self._focusItemUIData:GetStateLocked()
    return bLocked
end

function GunsmithSkinDefaultMainUI:_GetFocusItemUIDataIsValidNormalObtainDesc()
    if self._focusItemUIData == nil then
        return false
    end
    return self._focusItemUIData:GetIsValidNormalObtainDesc()
end

function GunsmithSkinDefaultMainUI:_GetFocusItemUIDataNormalObtainDesc()
    if self._focusItemUIData == nil then
        return nil
    end
    return self._focusItemUIData:GetNormalObtainDesc()
end

function GunsmithSkinDefaultMainUI:_GetFocusItemUIDataIsValidNormalLockDesc()
    if self._focusItemUIData == nil then
        return false
    end
    return self._focusItemUIData:GetIsValidNormalLockDesc()
end

function GunsmithSkinDefaultMainUI:_GetFocusItemUIDataNormalLockDesc()
    if self._focusItemUIData == nil then
        return nil
    end
    return self._focusItemUIData:GetNormalLockDesc()
end

function GunsmithSkinDefaultMainUI:_GetFocusItemUIDataIsValidNormalJumpID()
    if self._focusItemUIData == nil then
        return false
    end
    return self._focusItemUIData:GetIsValidNormalJumpID()
end

function GunsmithSkinDefaultMainUI:_GetFocusItemUIDataNormalJumpID()
    if self._focusItemUIData == nil then
        return nil
    end
    return self._focusItemUIData:GetNormalJumpID()
end

function GunsmithSkinDefaultMainUI:_GetFocusItemUIDataUnlockTips()
    if self._focusItemUIData == nil then
        return nil
    end
    return self._focusItemUIData:GetUnlockTips()
end

function GunsmithSkinDefaultMainUI:_GetFocusItemUIDataIsValidNormalBuyPriceRichText()
    if self._focusItemUIData == nil then
        return false
    end
    return self._focusItemUIData:GetIsValidNormalBuyPriceRichText()
end

function GunsmithSkinDefaultMainUI:_GetFocusItemUIDataNormalBuyPriceRichText()
    if self._focusItemUIData == nil then
        return nil
    end
    return self._focusItemUIData:GetNormalBuyPriceRichText()
end

function GunsmithSkinDefaultMainUI:_GetFocusItemIsRandomItem()
    if self._focusItemUIData == nil then
        return false
    end
    local bIsRandomItem = self._focusItemUIData:IsRandomItem()
    return bIsRandomItem
end

function GunsmithSkinDefaultMainUI:_GetFocusItemHasSkinInfo()
    if self._focusItemUIData == nil then
        return false
    end
    local bHasSkinInfo = self._focusItemUIData:HasSkinInfo()
    return bHasSkinInfo
end

function GunsmithSkinDefaultMainUI:_GetUIButtonsActive()
    local bEquipped = false
    local bEquippedAllDisabled = false
    local bLocked = false
    local bIsMP = false
    local bIsSimulate = false

    local function fcallback()
        return bEquipped, bEquippedAllDisabled, bLocked, bIsMP, bIsSimulate
    end
    local bIsRange = GunsmithUIContextLogic.GetGroupIDIsRange()

    bIsSimulate = not bIsRange and GunsmithUIContextLogic.GetIsFromUserPropInfo()

    if self._focusItemUIData == nil then
        return fcallback()
    end

    bLocked = self._focusItemUIData:GetStateLocked()
    if bLocked then
        return fcallback()
    end

    local bPrereq = true

    if not bIsRange then
        local weaponID = GunsmithUIContextLogic.GetItemID()
        local bIsWeaponEnabledRandom = GunsmithSkinLogic.CheckIsRandomPoolEnabled(weaponID)
        bPrereq = not bIsWeaponEnabledRandom
    end

    bEquipped = self._focusItemUIData:GetStateEquipped()

    bIsMP = GunsmithUIContextLogic.GetGroupIDIsMP()
    if bIsMP then
        bEquipped = bPrereq and bEquipped
        bEquippedAllDisabled = bEquipped
    else
        bEquippedAllDisabled = bPrereq and self._focusItemUIData:GetIsSOLGlobalEquipped()
    end
    return fcallback()
end

function GunsmithSkinDefaultMainUI:_GetEnableLockAppearance()
    local bIsSkin = self._focusItemUIData:GetIsSkin()
    if not bIsSkin then
        return false
    end

    local skinID = self._focusItemUIData:GetID()
    local bEnable = GunsmithSkinLogic.GetIsEnableLockAppearance(skinID)
    return bEnable
end

function GunsmithSkinDefaultMainUI:_InternalGetSortText(sortType)
    -- sortType = sortType or self._sortType
    -- local dropDownDataList = Module.Gunsmith.Config.SkinMainUISortConfig
    -- return dropDownDataList[sortType]
end

function GunsmithSkinDefaultMainUI:_InternalGetFocused(focusItemID)
    local bAlreadyFocused = false
    local count = self._itemUIdataContainer:GetCount()
    for i = 1, count do
        local data = self._itemUIdataContainer:Get(i)
        if data ~= nil and data:GetID() == focusItemID then
            bAlreadyFocused = data:GetFocus()
            break
        end
    end
    return bAlreadyFocused
end

-- BEGIN MODIFICATION @ VIRTUOS : 
function GunsmithSkinDefaultMainUI:_EnableNavigation(bEnable)
    if not IsHD() then
        return 
    end

    if bEnable then
        if not self._wtNavGroup then
            self._wtNavGroup = WidgetUtil.RegisterNavigationGroup(self._wt_WaterFallList, self, "Hittest")
            if self._wtNavGroup then
                self._wtNavGroup:AddNavWidgetToArray(self._wt_WaterFallList)
                self._wtNavGroup:SetScrollRecipient(self._wt_WaterFallList)
                WidgetUtil.BindCustomFocusProxy(self._wtNavGroup, self._WaterfallFocusProxyMaker, self._WaterfallFocusProxyResolver, self)
                WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup)
                -- 设置NavGroup的子控件被Focus时，采用的行行为方式，这里为执行自动鼠标左键事件
                self._wtNavGroup:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
            end
            WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)

            if not self._wtNavGroupMystical then
                self._wtNavGroupMystical = WidgetUtil.RegisterNavigationGroup(self._wtMysticalSkinBtnPanel, self, "Hittest")
                if self._wtNavGroupMystical then
                    self._wtNavGroupMystical:AddNavWidgetToArray(self._wtMysticalSkinBtnPanel)
                end
            end
        end
    else
        if self._wtNavGroupMystical or self._wtNavGroup then
            WidgetUtil.RemoveNavigationGroup(self)
            self._wtNavGroup = nil
            self._wtNavGroupMystical = nil
        end
        WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    end
end

function GunsmithSkinDefaultMainUI:_WaterfallFocusProxyMaker(inWidget)
    -- 找到所在行
    local row = WidgetUtil.GetParentWidgetByClassName(inWidget, "WBP_GunStand_GunsmithSkinDefaultMainUI_C")
    local rowIndex = self._wt_WaterFallList:GetIndexByItem(row)
    return {rowIndex}
end

function GunsmithSkinDefaultMainUI:_WaterfallFocusProxyResolver(inProxyHandle)
    local rowIndex = inProxyHandle[1]
    -- 可能item会在屏幕外，先执行滚动
    self._wt_WaterFallList:ScrollToIndexToScreen(rowIndex, 0.5, 0.5)
    local row = self._wt_WaterFallList:GetItemByIndex(rowIndex)
    if row then
        return row
    end
    -- 可能会找不到，返回空，自动使用Gorup的默认逻辑查找聚焦
    return nil
end

function GunsmithSkinDefaultMainUI:_OnPreSetGamepadFocusWidget()
    if not IsHD() then
        return
    end

    local bIsForceUIUpdate = self:GetIsForceUIUpdate()
    if not bIsForceUIUpdate then
        return
    end

    if not self._wtNavGroup then
        return
    end
    self._wtNavGroup:SetFocusRecoveryEnabled(false)
end

function GunsmithSkinDefaultMainUI:_OnSetGamepadFocusWidget()
    if not IsHD() then
        return
    end

    local bIsForceUIUpdate = self:GetIsForceUIUpdate()
    if not bIsForceUIUpdate then
        return
    end

    if not self._wtNavGroup then
        return
    end

    local focusItemID = self:_GetFocusItemID()
    local focusIdx = self:_InternalGetIndexByitemID(focusItemID)
    WidgetUtil.FocusWidgetByProxy(self._wtNavGroup, {focusIdx}, true)

    self._wtNavGroup:SetFocusRecoveryEnabled(true)
end

-- 新增手柄快捷键（页面中显示的按钮：改装、预改装）
function GunsmithSkinDefaultMainUI:_InitShortcuts()
    if not IsHD() then
        return 
    end

    self:_RemoveShortcuts()
    local bEquipped, bEquippedAllDisabled, bLocked, bIsMP, bIsSimulate = self:_GetUIButtonsActive()
    local bIsMissionSkin = self._missionSkinID and self._missionSkinID == skinID

    -- 装备
    self._Equip = self:_InternalRemoveShortcut(self._Equip)
    local bShowEquipButton = not (bLocked or bIsMP or bIsSimulate or (bIsMissionSkin and bLocked))
    if not self._Equip and bShowEquipButton then
        self._Equip = self:AddInputActionBinding("Gunsmith_Equip_Gamepad", EInputEvent.IE_Pressed, self._ShortcutEventEquipClicked,self, EDisplayInputActionPriority.UI_Stack)
        self._wt_Button_Equip:SetDisplayInputAction("Gunsmith_Equip_Gamepad", true, nil, true)
    end

    -- 装备全部: 显示一个Button时设置EquipAllButton为A键，两个Button时EquipAllButton为X键
    self._EquipAll = self:_InternalRemoveShortcut(self._EquipAll)
    local EquipAll_Action = not bShowEquipButton and "Gunsmith_Equip_Gamepad" or "Gunsmith_EquipAll_Gamepad"
    if bIsMP then
        EquipAll_Action = "Gunsmith_EquipAll_Gamepad"
    end
    if not self._EquipAll then
        self._EquipAll = self:AddInputActionBinding(EquipAll_Action, EInputEvent.IE_Pressed, self._ShortcutEventEquipAllClicked,self, EDisplayInputActionPriority.UI_Stack)
        self._wt_Button_EquipAll:SetDisplayInputAction(EquipAll_Action, true, nil, true)
    end

    --编辑随机
    local bShowRandomButton = self._wt_WBP_RandomButton_PC:IsVisible()
    self._Random = self:_InternalRemoveShortcut(self._Random)
    local Random_Action = "Common_ButtonTop_Gamepad"
    if not self._Random and bShowRandomButton then
        self._Random = self:AddInputActionBinding(Random_Action, EInputEvent.IE_Pressed, self._ShortcutEventRandomClicked,self, EDisplayInputActionPriority.UI_Stack)
        self._wt_WBP_RandomButton_PC:SetDisplayInputAction(Random_Action, true, nil, true)
    end

    -- 武器旋转
    -- if not self._RotationX then
    --     self._RotationX = self:AddAxisInputActionBinding("Common_Right_X", self._WeaponRotationX, self, EDisplayInputActionPriority.UI_Stack)
    -- end
    -- if not self._RotationY then
    --     self._RotationY = self:AddAxisInputActionBinding("Common_Right_Y", self._WeaponRotationY, self, EDisplayInputActionPriority.UI_Stack)
    -- end
    local summaryList = {}
    -- 应用皮肤自带配件方案 快捷键
    if self._wt_WBP_CommonCheckBox_EquipSolution:IsVisible() then
        table.insert(summaryList, {actionName = "Gunsmith_UseDefaultSkin_Gamepad", func = self._ShortcutEventUseDefaultSkin, caller = self ,bUIOnly = false, bHideIcon = false})
    end

    --外观解锁挑战说明
    if self._wt_WBP_DFCommon_MissionUnlocked and self._wt_WBP_DFCommon_MissionUnlocked.OpenUnlockWeaponTip then
        table.insert(summaryList, {actionName = "Gunsmith_UnlockWeaponTip_Gamepad", func = self._ShortcutEventUnlockWeaponTip, caller = self ,bUIOnly = false, bHideIcon = false})
    end

    --获取方式
    if not self._AcquisitionMethod and self._wtMysticalSkinBtnPanel:IsVisible() then
        self._AcquisitionMethod = self:AddInputActionBinding("Gunsmith_Acquisition_Gamepad", EInputEvent.IE_Pressed, self._ShortcutEventAcquisitionClicked,self, EDisplayInputActionPriority.UI_Stack)
    end

    local bShowRandonShortcutsButton = self:_InternalShowRandonShortcutsButton()
    if bShowRandonShortcutsButton then
        local bIsInRandomPool = self:_InternalFocusItemInRandomPool()
        if bIsInRandomPool then
            table.insert(summaryList, {actionName = "GunsmithRemoveRandonSkin", func = self.ProcessShortcutRandonSkin, caller = self ,bUIOnly = false, bHideIcon = false})
        else
            table.insert(summaryList, {actionName = "GunsmithAddRandonSkin", func = self.ProcessShortcutRandonSkin, caller = self ,bUIOnly = false, bHideIcon = false})
        end
    end

    Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, false, false)
end

function GunsmithSkinDefaultMainUI:_RemoveShortcuts()
    if IsHD() then
        self._Equip = self:_InternalRemoveShortcut(self._Equip)
        self._EquipAll = self:_InternalRemoveShortcut(self._EquipAll)
        self._RotationX = self:_InternalRemoveShortcut(self._RotationX)
        self._RotationY = self:_InternalRemoveShortcut(self._RotationY)
        self._Retrieve = self:_InternalRemoveShortcut(self._Retrieve)
        self._AcquisitionMethod = self:_InternalRemoveShortcut(self._AcquisitionMethod)
        self._Random = self:_InternalRemoveShortcut(self._Random)
        self._Mission = self:_InternalRemoveShortcut(self._Mission)
        Module.CommonBar:RecoverBottomBarInputSummaryList()
    end
end

function GunsmithSkinDefaultMainUI:_InternalRemoveShortcut(InActionHandler)
    if IsHD() and InActionHandler then
        self:RemoveInputActionBinding(InActionHandler)
    end

    return nil
end

function GunsmithSkinDefaultMainUI:_ShortcutEventEquipClicked()
    if IsHD() and self._wt_Button_Equip then
        self._wt_Button_Equip:ButtonClick()
    end
end

function GunsmithSkinDefaultMainUI:_ShortcutEventEquipAllClicked()
    if IsHD() and self._wt_Button_EquipAll then
        self._wt_Button_EquipAll:ButtonClick()
    end
end

function GunsmithSkinDefaultMainUI:_ShortcutEventUseDefaultSkin()
    if IsHD() and self._wt_WBP_CommonCheckBox_EquipSolution then
        self._wt_WBP_CommonCheckBox_EquipSolution:SelfClick()
    end
end

function GunsmithSkinDefaultMainUI:_ShortcutEventRandomClicked()
    if IsHD() and self._wt_WBP_RandomButton_PC and self._wt_WBP_RandomButton_PC:IsVisible() then
        self._wt_WBP_RandomButton_PC:ButtonClick()
    end
end


function GunsmithSkinDefaultMainUI:_ShortcutEventUnlockWeaponTip()
    if not self._wt_WBP_DFCommon_MissionUnlocked then
        self._wt_WBP_DFCommon_MissionUnlocked = self:Wnd("WBP_DFCommon_MissionUnlocked_156", CommonWeaponSkinMissionProgress)
    end
    if IsHD() and self._wt_WBP_DFCommon_MissionUnlocked then
        self._wt_WBP_DFCommon_MissionUnlocked:_OnShowTipCheckBoxStateChanged(true)
    end
end

function GunsmithSkinDefaultMainUI:_RetrieveClicked()
    if IsHD() and self._wt_WBP_CommonButtonV1S1_Retrieve then
        self._wt_WBP_CommonButtonV1S1_Retrieve:ButtonClick()
    end
end

function GunsmithSkinDefaultMainUI:_MissionClicked()
    if IsHD() and self._wt_WBP_CommonButtonV1S1_Mission then
        self._wt_WBP_CommonButtonV1S1_Mission:ButtonClick()
    end
end

function GunsmithSkinDefaultMainUI:_WeaponRotationX(value)
    if IsHD() and WidgetUtil.IsGamepad() then
        self._RotationVec.X = value
        local gpPlayerController = UGameplayBlueprintHelper.GetLocalGPPlayerController(self)
        if gpPlayerController then
            gpPlayerController:TouchUpdateByGamepad(self._RotationVec.X, self._RotationVec.Y)
        end
    end
end

function GunsmithSkinDefaultMainUI:_WeaponRotationY(value)
    if IsHD() and WidgetUtil.IsGamepad() then
        self._RotationVec.Y = value * -1
        local gpPlayerController = UGameplayBlueprintHelper.GetLocalGPPlayerController(self)
        if gpPlayerController then
            gpPlayerController:TouchUpdateByGamepad(self._RotationVec.X, self._RotationVec.Y)
        end
    end
end

function GunsmithSkinDefaultMainUI:_HandleMandelBrickBtnFocusRecive()
    if IsHD() then
        self._FocusMandelBtn = true
    end
end

function GunsmithSkinDefaultMainUI:_HandleMandelBrickBtnFocusLost()
    if IsHD() then
        self._FocusMandelBtn = false
    end
end

function GunsmithSkinDefaultMainUI:_HandleAuctionBtnFocusRecive()
    if IsHD() then
        self._FocusAuctionBtn = true
    end
end

function GunsmithSkinDefaultMainUI:_HandleAuctionBtnFocusLost()
    if IsHD() then
        self._FocusAuctionBtn = false
    end
end

function GunsmithSkinDefaultMainUI:_HandleMatrixWorkBtnFocusRecive()
    if IsHD() then
        self._FocusMatrixWorkBtn = true
    end
end

function GunsmithSkinDefaultMainUI:HandleMatrixWorkBtnFocusLost()
    if IsHD() then
        self._FocusMatrixWorkBtn = false
    end
end

function GunsmithSkinDefaultMainUI:_ShortcutEventAcquisitionClicked()
    if IsHD() then
       if self._FocusMandelBtn and self._wtFromMandelBrickBtn then
            self:_OnShowMandelBrickPage()
       end
       if self._FocusAuctionBtn and self._wtFromAuctionBtn then
            self:_OnShowAuctionPage()
       end
       if self._FocusMatrixWorkBtn and self._wtFromMatrixWorkshopBtn then
            self:_OnShowMysticalWorkshopPage()
       end
    end
end
-- END MODIFICATION

function GunsmithSkinDefaultMainUI:_InternalShowRandonShortcutsButton()
    if not IsHD() then
        return false
    end

    return self:_InternalShowRandomButton()
end

function GunsmithSkinDefaultMainUI:_InternalShowRandomButton()
    local bIsRange = GunsmithUIContextLogic.GetGroupIDIsRange()
    if bIsRange then
        return false
    end

    if self._focusItemUIData == nil then
        return false
    end

    local bIsRandomItem = self._focusItemUIData:IsRandomItem()
    if bIsRandomItem then
        return false
    end

    local bIsBaseSkin = self._focusItemUIData:GetIsSkin()
    if not bIsBaseSkin then
        return false
    end

    local bLocked = self._focusItemUIData:GetStateLocked()
    if bLocked then
        return false
    end

    return true
end

function GunsmithSkinDefaultMainUI:_GetFocusItemIsEnabledRandom()
    if self._focusItemUIData == nil then
        return false
    end
    local bIsEnabledRandom = self._itemUIData:IsEnabledRandom()
    return bIsEnabledRandom
end

function GunsmithSkinDefaultMainUI:ProcessShortcutRandonSkin()
    local weaponID = GunsmithUIContextLogic.GetItemID()
    local skinID = self:_GetFocusItemID()
    local skinGUID = self:_GetFocusItemGUID()
    local bRandom = not self:_InternalFocusItemInRandomPool()
    GunsmithSkinLogic.RandomSkin(weaponID, skinID, skinGUID, bRandom)
end

function GunsmithSkinDefaultMainUI:_InternalFocusItemInRandomPool()
    local weaponID = GunsmithUIContextLogic.GetItemID()
    local skinID = self:_GetFocusItemID()
    local skinGUID = self:_GetFocusItemGUID()
    return GunsmithSkinLogic.CheckIsSkinInRandomPool(weaponID, skinID, skinGUID)
end

function GunsmithSkinDefaultMainUI:_InternalSetSpecialRefreshParam(bIsSpecialWaterFallRefresh, bIsWaterFallRefreshAllUI, bIsMysticalWaterFallRefreshData, bIsMysticalWaterFallVisibleItemsNotReBuild)
    self._specialRefreshParam:Reset()
    bIsSpecialWaterFallRefresh = setdefault(bIsSpecialWaterFallRefresh, false)
    bIsWaterFallRefreshAllUI = setdefault(bIsWaterFallRefreshAllUI, false)
    bIsMysticalWaterFallRefreshData = setdefault(bIsMysticalWaterFallRefreshData, false)
    bIsMysticalWaterFallVisibleItemsNotReBuild = setdefault(bIsMysticalWaterFallVisibleItemsNotReBuild, false)
    self._specialRefreshParam:SetSpecialWaterFallRefresh(bIsSpecialWaterFallRefresh)
    self._specialRefreshParam:SetWaterFallRefreshAllUI(bIsWaterFallRefreshAllUI)
    self._specialRefreshParam:SetMysticalWaterFallRefreshData(bIsMysticalWaterFallRefreshData)
    self._specialRefreshParam:SetMysticalWaterFallVisibleItemsNotReBuild(bIsMysticalWaterFallVisibleItemsNotReBuild)
end

function GunsmithSkinDefaultMainUI:IsSpecialWaterFallRefresh()
    return self._specialRefreshParam:IsSpecialWaterFallRefresh()
end

function GunsmithSkinDefaultMainUI:IsWaterFallRefreshAllUI()
    return self._specialRefreshParam:IsWaterFallRefreshAllUI()
end

function GunsmithSkinDefaultMainUI:IsMysticalWaterFallRefreshData()
    return self._specialRefreshParam:IsMysticalWaterFallRefreshData()
end

function GunsmithSkinDefaultMainUI:IsMysticalWaterFallVisibleItemsNotReBuild()
    return self._specialRefreshParam:IsMysticalWaterFallVisibleItemsNotReBuild()
end

return GunsmithSkinDefaultMainUI
