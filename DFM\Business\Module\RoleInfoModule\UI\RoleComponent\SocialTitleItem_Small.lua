----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRoleInfo)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class RoleInfoTitleItem_Small : LuaUIBaseView
local RoleInfoTitleItem_Small = ui("RoleInfoTitleItem_Small")

local RoleInfoLogic = require "DFM.Business.Module.RoleInfoModule.Logic.RoleInfoLogic" 
local SocialAvatarTable = Facade.TableManager:GetTable("SocialAvatarDataTable")

function RoleInfoTitleItem_Small:Ctor()
    self._wtCanvasPanel = self:Wnd("wtCanvasPanel", UIWidgetBase)
    self._wtSelectBtn = self:Wnd("wtSeclectBtn", UIWidgetBase)
    self._wtSelectBtn:Event("OnClicked", self._OnSelectBtnClicked, self)

    self._wtTitleImage = self:Wnd("wtTitleBg", UIImage)
    self._wtTitleName = self:Wnd("wtTitleText", UITextBlock)
    self._wtTitleIcon = self:Wnd("wtTitleIcon", UIImage)
    self._wtTitleRichText = self:Wnd("wtTitleRichText", UITextBlock)
    self._wtTitleImage:HitTestInvisible()
end

function RoleInfoTitleItem_Small:OnOpen()
end

---@param titleId number
---@param adCode number|nil
---@param ranking number|nil
function RoleInfoTitleItem_Small:UpdateByTitleId(titleId, adCode, ranking)
    loginfo("RoleInfoTitleItem_Small:UpdateByTitleId titleId:", titleId, "adCode:", adCode, "ranking:", ranking)
    self._wtTitleImage:Hidden()
    self._wtTitleName:Hidden()
    self._wtTitleIcon:Hidden()
    self._wtTitleRichText:Hidden()

    if not titleId or titleId == 0 then
        self._wtCanvasPanel:Collapsed()
        return
    end
    local avatarInfo = SocialAvatarTable[titleId]
    if not avatarInfo then
        logwarning("get avatarInfo failed, titleId:", titleId)
        self._wtCanvasPanel:Collapsed()
        return
    end

    -- composite background image
    local numOptResource =  #avatarInfo.OptionalResourceNames 
    if numOptResource == 3 then
        local smallIcon = avatarInfo.OptionalResourceNames[3]
        local smallBg = avatarInfo.OptionalResourceNames[2]

        self._wtTitleImage:HitTestInvisible()
        self._wtTitleIcon:Visible()

        self:_TrySetImage(self._wtTitleImage, smallBg, true)
        self:_TrySetImage(self._wtTitleIcon, smallIcon, true)

    else
        local uniqueBg = avatarInfo.OptionalResourceNames[2]
        self._wtTitleImage:HitTestInvisible()
        self._wtTitleIcon:Collapsed()

        self:_TrySetImage(self._wtTitleImage, uniqueBg, true)
    end

    -- title name
    -- title name (with region)
    self._wtTitleName:SelfHitTestInvisible()
    local titleName = RoleInfoLogic.TranslateTitleName(avatarInfo.AvatarName, adCode, ranking)
    self._wtTitleName:SetText(titleName)

    -- ranking
    if ranking ~= nil and ranking ~= 0 then
        self._wtTitleRichText:HitTestInvisible()
        self._wtTitleRichText:SetText(string.format(Module.RoleInfo.Config.TitleRankingTextSmall,  ranking ))
    end

end

---@comment if image is not exist, collapse the topper panel
function RoleInfoTitleItem_Small:_TrySetImage(imageWidget, texturePath ,IsAutoSize)
    if not texturePath or texturePath == nil or texturePath == "" then
        self._wtCanvasPanel:Collapsed()
        return
    end
    
    local OnImageLoadFinished = SafeCallBack(function(auto_this, imageWidget, imageAsset, bAutoResize)
        logerror("finish load img imageAsset:", imageAsset, " bAutoResize:", bAutoResize)
        if imageAsset then
            auto_this._wtCanvasPanel:Visible()
            local classname = LAI.GetObjectClassName(imageAsset)
            if classname == "PaperSprite" then
                imageWidget:SetBrushFromAtlasInterface(imageAsset, bAutoResize)
                if bAutoResize then
                    imageWidget:SetBrushSize(imageAsset.BakedSourceDimension)
                end
            else
                if imageWidget.SetBrushFromTexture then
                    imageWidget:SetBrushFromTexture(imageAsset, bAutoResize)
                end
            end
        else
            logerror("loaded imageAsset is nil", texturePath)
            auto_this._wtCanvasPanel:Collapsed()
        end
    end, self, imageWidget)
    ResImageUtil.StaticAsyncLoadImgObjByPath(texturePath, IsAutoSize, OnImageLoadFinished)
end

function RoleInfoTitleItem_Small:_OnSelectBtnClicked()
    if self._callback then
        self._callback()
    end
end

function RoleInfoTitleItem_Small:BindBtnClick(callback)
    self._callback = callback
end

function RoleInfoTitleItem_Small:ClearBtnClick()
    self._callback = nil
end

return RoleInfoTitleItem_Small
