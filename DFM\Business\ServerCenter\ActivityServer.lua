----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaSActivity)
local ACT_BROWSE = "ACTIVITY_HOMEPAGE_BROWSE_"
----- LOG FUNCTION AUTO GENERATE END -----------

local ChronoTimer         = require "DFM.Business.Datastruct.Common.Agent.Timer.ChronoTimer"
local GeneralHelperTool = require "DFM.StandaloneLua.BusinessTool.GeneralHelperTool"
local ActHelperTool = require "DFM.StandaloneLua.BusinessTool.ActHelperTool"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
-- local ActDataTestTool = require "DFM.StandaloneLua.BusinessTool.ActDataTestTool" 
local Sort          = require "DFM.Business.DataStruct.Common.Base.Sort"
local Functional    = require "DFM.Business.DataStruct.Common.Base.Functional"
local Safe          = require "DFM.Business.DataStruct.Common.Base.Safe"
local Deep          = require "DFM.Business.DataStruct.Common.Base.Deep"
local Table         = require "DFM.Business.DataStruct.Common.Base.Table"
local Filter        = require "DFM.Business.DataStruct.Common.Base.Filter"

local function logPb(...)
    logwarning("[CSActivityServer]", ...)
end

-- 活动类型标签
EActivityTag = {
    NONE            = 0,--默认活动
    Reflow         = 1, --回流
}

EActGameMode = {
    ALL         = 0,
    SOL         = 1,
    MP          = 2,
}

-- 活动交互
EActSubInteractive = {
    All    = 0, --所有
    Next1  = 1,
    Next2  = 2,
    Next3  = 3,
    Next4  = 4,
    Next5  = 5,
    Next6  = 6,
    Next7  = 7,
    Next8  = 8,
    Next9  = 9,
    Next10 = 10,
    JumpWarehouse = 11,--跳转仓库
    ItemWaterfall = 12,--列表->item
    Item = 13,--item->列表
    Jump = 14,--跳转
    Task = 15,--任务
    NTF  = 16,--通知
    Anim = 17,--动画
    AnimFinish = 18,--动画播放完成
    Reddot     = 19,--红点
}

---@class ActivityServer
local ActivityServer = class("ActivityServer", require("DFM.YxFramework.Managers.Server.ServerBase"))

-- Hint only
if false then
    Server.ActivityServer = nil ---@type ActivityServer
end

local LocalizeTool = require "DFM.StandaloneLua.BusinessTool.LocalizeTool"
require "DFM.Business.DataStruct.ActivityStruct.ActivityTableStruct"
require "DFM.Business.DataStruct.ActivityStruct.ActivityTableTaskStruct"
require "DFM.Business.DataStruct.ActivityStruct.ActivityConstStruct"
require "DFM.Business.DataStruct.ActivityStruct.ActivityChallengeStruct"
require "DFM.Business.DataStruct.ActivityStruct.ActivityTaskGoalsStruct"
require "DFM.Business.DataStruct.ActivityStruct.ActivityThemeEventTechStruct"
require "DFM.Business.DataStruct.ActivityStruct.ActivityQuestionStruct"
require "DFM.Business.DataStruct.ActivityStruct.ActivityTeachingManualStruct"
local ACT_KEY_RECORD = "ACT_KEY_RECORD"

function ActivityServer:Ctor()

    self.Events = {
        evtActivityTaskCurrencyChange = LuaEvent:NewIns("ActivityServer.evtActivityTaskCurrencyChange"), --暂未使用
        evtSignInfoNeedUpdate = LuaEvent:NewIns("ActivityServer.evtSignInfoNeedUpdate"), --暂未使用
        evtActivityTrackTaskChange = LuaEvent:NewIns("ActivityServer.evtActivityTrackTaskChange"), --追踪活动变化
        evtActivityExchangeChange = LuaEvent:NewIns("ActivityServer.evtActivityExchangeChange"), --玩家兑换活动变化
        evtActivityTaskChange = LuaEvent:NewIns("ActivityServer.evtActivityTaskChange"), --玩家任务状态变化
        evtActivityUnlockExchangeChange = LuaEvent:NewIns("ActivityServer.evtActivityExchangeChange"), --玩家解锁兑换活动变化

        evtActivityExchangeReward = LuaEvent:NewIns("ActivityServer.evtActivityExchangeReward"), --玩家兑换活动奖励
        evtActivityTaskReward = LuaEvent:NewIns("ActivityServer.evtActivityTaskReward"), --玩家领取任务奖励
        evtActivityMilestoneAward = LuaEvent:NewIns("ActivityServer.evtActivityMilestoneAward"), --里程碑奖励领取
        evtSignResArrive = LuaEvent:NewIns("ActivityServer.evtSignResArrive"), --签到回包到达

        evtActivitySOLExchangeReward = LuaEvent:NewIns("ActivityServer.evtActivitySOLExchangeReward"), --只弹获得不弹解锁
        evtActivityMPExchangeReward = LuaEvent:NewIns("ActivityServer.evtActivityMPExchangeReward"), --只弹解锁不弹获得

        evtActivityUpdateSign = LuaEvent:NewIns("ActivityServer.evtActivityUpdateSign"), --签到活动更新
        evtActivityUpdateRedDot = LuaEvent:NewIns("ActivityServer.evtActivityUpdateRedDot"), --红点更新通知  evtActivityUpdateRedDot(optActivityInfos:pb_ActivityInfo[]?) optActivityInfos为空代表全部更新
        evtActivityInfoUpdate = LuaEvent:NewIns("ActivityServer.evtActivityInfoUpdate"), --活动信息更新
        evtBannerInfoUpdate = LuaEvent:NewIns("ActivityServer.evtBannerInfoUpdate"), --首页信息更新
        evtBannerBtnChanged = LuaEvent:NewIns("ActivityServer.evtBannerBtnChanged"), --首页按钮变化（解决跑马灯动画，只能拆分事件）
        evtHardwareInfoUpdate = LuaEvent:NewIns("ActivityServer.evtHardwareInfoUpdate"), --moss硬件按钮更新

        evtFilingOnSequenceItemReceived = LuaEvent:NewIns("ActivityServer.evtFilingOnSequenceItemReceived"), --归档，批次物品领取
        evtFilingAudioFinish = LuaEvent:NewIns("ActivityServer.evtFilingAudioFinish"), -- 归档，语音播放完毕
        evtActivityFilingSubmitProps = LuaEvent:NewIns("ActivityServer.evtActivityFilingSubmitProps"), -- 归档，提交信物

        evtActivityStarDecrypt = LuaEvent:NewIns("ActivityServer.evtActivityStarDecrypt"), -- 繁星活动解密
        evtActivityAnswerRes = LuaEvent:NewIns("ActivityServer.evtActivityAnswerRes"), -- 回答问题回包
        evtActivityTriggerRes = LuaEvent:NewIns("ActivityServer.evtActivityTriggerRes"), -- 触发回包
        evtActivityRoleSurveyExchangeClick = LuaEvent:NewIns("ActivityServer.evtActivityRoleSurveyExchangeClick"), -- 兑换物品点击事件
        evtActivityRoleSurveyExchangeClickEnd = LuaEvent:NewIns("ActivityServer.evtActivityRoleSurveyExchangeClickEnd"), -- 兑换物品点击处理结束事件

        evtActivityDataUpdateGeneral = LuaEvent:NewIns("ActivityServer.evtActivityDataUpdateGeneral"), --调酒师事件(可通用)

        evtActivityRadioAdjustChange = LuaEvent:NewIns("ActivityServer.evtActivityRadioAdjustChange"), --电台调台事件
        evtActivityPreselectionUpdate = LuaEvent:NewIns("ActivityServer.evtActivityPreselectionUpdate"), --预选奖励更新

        evtMandelInfoUpdate = LuaEvent:NewIns("ActivityServer.evtMandelInfoUpdate"), --曼德尔砖更新

        evtEnableRewardNTFCall = LuaEvent:NewIns("ActivityServer.evtEnableRewardNTFCall"), --相当于 self.Events.evtEnableRewardNTFCall:Invoke(...)
        evtActTaraTrainingTravel = LuaEvent:NewIns("ActivityServer.evtActTaraTrainingTravel"),--阿萨拉巡旅
    }

    --活动表
    self.AllActivityConfig = {} --key:actvityid,value:ActivityTableStruct
    self._hotfixTokenActivity = Facade.DataTableHotfixManager:AddHotfixCallback("ActivityRow",self.OnReloadLocalActivityTable,self)
    --任务表
    self.AllTaskConfig = {} --key:taskid,value:ActivityConstStruct
    self._hotfixTokenTask = Facade.DataTableHotfixManager:AddHotfixCallback("ActivityTask",self.OnReloadLocalTaskTable,self)
    --常量表
    self.AllConstConfig = {} --key:constid,value:ActivityTableTaskStruct
    self._hotfixTokenConst = Facade.DataTableHotfixManager:AddHotfixCallback("ActivityConst",self.OnReloadLocalConstTable,self)
    --任务分类表
    self.AllNewbieChallengeConfig = {}
    self._hotfixTokenNewbieChallenge = Facade.DataTableHotfixManager:AddHotfixCallback("NewbieChallenge",self.OnReloadLocalNewbieChallengeTable,self)
    --任务类型表
    self.AllActivityTaskGoalsConfig = {}
    self._hotfixTokenTaskGoals = Facade.DataTableHotfixManager:AddHotfixCallback("ActivityTaskGoals",self.OnReloadLocalTaskGoalsTable,self)
    --繁星主题活动配置，技术呈现内容表
    self.AllActivityThemeEventTechConfig = {}
    self._hotfixTokenThemeEventTech = Facade.DataTableHotfixManager:AddHotfixCallback("ActivityThemeEventTech",self.OnReloadLocalThemeEventTechTable,self)
    --答题表
    self.ActivityQuestionConfig = {}
    self._hotfixTokenQuestion = Facade.DataTableHotfixManager:AddHotfixCallback("ActivityThemeEventTech",self.OnReloadLocalQuestionTable,self)
    --反省主题活动配置，火箭发射教学表
    self.ActivityTeachingManualConfig = {}
    self._hotfixTokenTeachingManual = Facade.DataTableHotfixManager:AddHotfixCallback("ActivityTeachingManual",self.OnReloadActivityTeachingManualTable,self)
    --本地排序屏蔽表
    self._sortShieldList = {}
    --本地模式倾向表
    self._modeLearningList = {}
    --巅峰赛数据
    self.CommanderRankInfos = {}
    --Pandora ID -> ActivityID
    self.PandoraToActivityIDMap = {}

    --服务器数据存储容器
    self.AllActivityInfos = {} --服务器下发的全量数据, 按照key:actvityid,value:ActivityInfo存储
    self.ActivityInfosByGroup = {} --服务器下发的全量数据, 按照页签分组
    self.AllBannerInfos = {} --服务器下发的全量数据，res.banners
    self.BannerInfosByID = {} --res.banners按所属种类分类
    self.AllBannerFilterInfos = {} --过滤后的全量数据
    self.BannerFilterInfosByID = {}  --过滤后的分类数据
    self.PropJumpInfos = {} --道具来源信息

    self.AllActivityInfosWithMode = {}
    self.ActivityInfosByGroupWithMode = {}
    self.BannerFilterInfosByIDWithMode = {}

    self._bMainInView = false
    self._bShowCurrentModeAct = true
    self.max_tracking_num = false

    self._tdmMandelBrickInfo={}

    --预告活动的开放Timer
    self._forecastTimers = {} ---@type table<integer, integer>  _forecastTimers[activityID] = chronoTimerToken

    self._tLastActivityInfoUpdate = nil
    self._lastTriggerTime = nil
end

function ActivityServer:Destroy()
    Facade.DataTableHotfixManager:RemoveHotfixCallback(self._hotfixTokenActivity)
    self._hotfixTokenActivity = nil

    Facade.DataTableHotfixManager:RemoveHotfixCallback(self._hotfixTokenTask)
    self._hotfixTokenTask = nil
    
    Facade.DataTableHotfixManager:RemoveHotfixCallback(self._hotfixTokenConst)
    self._hotfixTokenConst = nil

    Facade.DataTableHotfixManager:RemoveHotfixCallback(self._hotfixTokenNewbieChallenge)
    self._hotfixTokenNewbieChallenge = nil
    
    Facade.DataTableHotfixManager:RemoveHotfixCallback(self._hotfixTokenTaskGoals)
    self._hotfixTokenTaskGoals = nil

    Facade.DataTableHotfixManager:RemoveHotfixCallback(self._hotfixTokenThemeEventTech)
    self._hotfixTokenTaskGoals = nil
    
    Facade.DataTableHotfixManager:RemoveHotfixCallback(self._hotfixTokenQuestion)
    self._hotfixTokenTaskGoals = nil
end

function ActivityServer:OnReloadLocalActivityTable(name)
    loginfo("--ActivityServer:OnReloadLocalActivityTable--", name)
    self.AllActivityConfig = {}
    self:InitActivityTable()
end

function ActivityServer:OnReloadLocalTaskTable(name)
    loginfo("--ActivityServer:OnReloadLocalTaskTable--", name)
    self.AllTaskConfig = {}
    self:InitActivityTaskTable()
end

function ActivityServer:OnReloadLocalConstTable(name)
    loginfo("--ActivityServer:OnReloadLocalConstTable--", name)
    self.AllConstConfig = {}
    self:InitActivityConstTable()
end

function ActivityServer:OnReloadLocalNewbieChallengeTable(name)
    loginfo("--ActivityServer:OnReloadLocalNewbieChallengeTable--", name)
    self.AllNewbieChallengeConfig = {}
    self:InitNewbieChallengeTable()
end

function ActivityServer:OnReloadLocalTaskGoalsTable(name)
    loginfo("--ActivityServer:OnReloadLocalTaskGoalsTable--", name)
    self.AllActivityTaskGoalsConfig = {}
    self:InitActivityTaskGoalsTable()
end

function ActivityServer:OnReloadLocalThemeEventTechTable(name)
    loginfo("--ActivityServer:OnReloadLocalThemeEventTechTable--", name)
    self.AllActivityThemeEventTechConfig = {}
    self:InitActivityThemeEventTechTable()
end

function ActivityServer:OnReloadLocalQuestionTable(name)
    loginfo("--ActivityServer:OnReloadLocalThemeEventTechTable--", name)
    self.ActivityQuestionConfig = {}
    self:InitActivityQuestionTable()
end

function ActivityServer:OnReloadActivityTeachingManualTable(name)
    loginfo("--ActivityServer:OnReloadActivityTeachingManualTable--", name)
    self._hotfixTokenTeachingManual = {}
    self:InitTeachingManualTable()
end

--个性化
function ActivityServer:OnSetActPersonalized(activityVar)
    self._special = activityVar
end

function ActivityServer:OnGetActPersonalized()
    return self._special
end

function ActivityServer:OnSetCurrentModeAct(activityVar)
    self._bShowCurrentModeAct = activityVar
end

function ActivityServer:OnGetCurrentModeAct()
    return self._bShowCurrentModeAct
end

------------------------------------------------------------------
--- 生命周期
------------------------------------------------------------------

function ActivityServer:OnInitServer()
    self.Tables = {
        ActivityTable = Facade.TableManager:GetTable("ActivityRow"),
        ActivityTaskTable = Facade.TableManager:GetTable("ActivityTask"),
        ActivityConstTable = Facade.TableManager:GetTable("ActivityConst"),
        NewbieChallengeTable = Facade.TableManager:GetTable("NewbieChallenge"),
        ActivityTaskGoalsTable = Facade.TableManager:GetTable("ActivityTaskGoals"),
        ActivityThemeEventTech = Facade.TableManager:GetTable("ActivityThemeEventTech"),
        ActivityQuestion = Facade.TableManager:GetTable("ActivityQuestion"),
        ActivityTeachingManual = Facade.TableManager:GetTable("ActivityTeachingManual")
    }
    self:InitActivityTable()
    self:InitActivityTaskTable()
    self:InitActivityConstTable()
    self:InitNewbieChallengeTable()
    self:InitActivityTaskGoalsTable()
    self:InitActivityThemeEventTechTable()
    self:InitActivityQuestionTable()
    self:InitTeachingManualTable()
    self:InitLocalCustomTable()

    Facade.ProtoManager:AddNtfListener("CSActivityUpdateProgressNtf", self.OnCSActivityUpdateProgressNtf, self)
    Facade.ProtoManager:AddNtfListener("CSActivitySOCNewCardNtf", self.OnActivitySOCNewCardNtf, self)
    Facade.ProtoManager:AddNtfListener("CSActivitySOCNewMPPackNtf", self.OnActivitySOCNewMPPackNtf, self)
end

function ActivityServer:OnActivitySOCNewCardNtf(ntf)
    --收到好友赠送新卡通知,收到之后可以请求CSActivitySOCGetRecvCards
    local bActivityValid = (self.AllActivityInfos[ntf.activity_id] and (not self.AllActivityInfos[ntf.activity_id].isPreview))
    if ntf and ntf.activity_id and bActivityValid then
        self:UpdateNewYearCard(ntf.activity_id, EActSubInteractive.Next3, nil)
        self.Events.evtActivityDataUpdateGeneral:Invoke(ntf.activity_id, EActSubInteractive.NTF)
    end
end

function ActivityServer:OnActivitySOCNewMPPackNtf(ntf)
    --获得MP卡包通知, 收到之后干嘛来着？
    local bActivityValid = (self.AllActivityInfos[ntf.activity_id] and (not self.AllActivityInfos[ntf.activity_id].isPreview))
    if ntf and ntf.activity_id and bActivityValid then
        self:UpdateNewYearCard(ntf.activity_id, EActSubInteractive.Next2, nil)
        self.Events.evtActivityDataUpdateGeneral:Invoke(ntf.activity_id, EActSubInteractive.NTF)
    end
end

function ActivityServer:OnCSActivityUpdateProgressNtf(ntf)
    local datas = ntf.data[1]

    if datas and datas.actv_id ~= nil then
        local bActivityValid = (self.AllActivityInfos[datas.actv_id] and (not self.AllActivityInfos[datas.actv_id].isPreview))
        if not bActivityValid then return end

        loginfo("[heimu] OnCSActivityUpdateProgressNtf Success!")
        for idx, taskInfo in pairs(self.AllActivityInfos[datas.actv_id].task_info) do
            if taskInfo.task_id == datas.task_id then
                taskInfo.state = datas.state
                if next(datas.objectives) then
                    taskInfo.objectives = datas.objectives
                end
                --对应的新兵里程碑任务也要刷新（如果有的话）
                if self.AllActivityInfos[datas.actv_id].recruit_ms_tasks and self.AllActivityInfos[datas.actv_id].recruit_ms_tasks[idx] then
                    self.AllActivityInfos[datas.actv_id].recruit_ms_tasks[idx].state = datas.state
                    self.AllActivityInfos[datas.actv_id].recruit_ms_tasks[idx].objectives = datas.objectives
                end
                self.Events.evtActivityTaskChange:Invoke(datas.actv_id)
            end
        end
    end
end

function ActivityServer:FetchServerData()
    if _WITH_EDITOR == 1 then
        self:OnSetActPersonalized(1)
    end
    self:InitActivityInfo()
    self:SendTDMMandelBrickReq()
end

function ActivityServer:OnDestroyServer()
    self:RemoveAllLuaEvent()
    Facade.ProtoManager:RemoveAllNtfListenerByCaller(self)
end

function ActivityServer:OnLoadingLogin2Frontend(gameFlowType)
    self:OnInitServer()
    -- self:FetchServerData()
    self:SendTDMMandelBrickReq()
end

function ActivityServer:OnLoadingGame2Frontend(gameFlowType)
    self:OnInitServer()
    self:FetchServerData()
end

function ActivityServer:OnLoadingFrontend2Game(gameFlowType)
    self:ClearDataStore()
end

function ActivityServer:ClearDataStore()
    self.AllActivityInfos = {}
    self.AllTaskConfig = {}
    self.Tables = {}
    self.AllActivityTaskGoalsConfig = {}
    self.AllActivityConfig = {}
    self.AllBannerFilterInfos = {}
    self.AllConstConfig = {}
    self.AllNewbieChallengeConfig = {}
    self.BannerFilterInfosByID = {}
    self.BannerInfosByID = {}
    self.ActivityInfosByGroup = {}
    self.AllBannerInfos = {}
    self.AllActivityThemeEventTechConfig = {}
    self.ActivityTeachingManualConfig = {}
    self.AllActivityInfosWithMode = {}
    self.ActivityInfosByGroupWithMode = {}
    self.BannerFilterInfosByIDWithMode = {}
    self.CommanderRankInfos = {}
    self:ClearForecastTimers()
    self._tLastActivityInfoUpdate = nil
    self._lastTriggerTime = nil
end

function ActivityServer:IsActivityDataAvailable()
    return self._tLastActivityInfoUpdate ~= nil
end

function ActivityServer:ReqPlayerState(playerList, fCallback)
    local OnCSStateBatchGetInfoRes = function(res)
        if res.result == 0 then
            if fCallback then
                fCallback(res.player_info or {})
            end
        end
    end
    local req = pb.CSStateBatchGetInfoReq:New()
    req.player_id = playerList
    req:Request(OnCSStateBatchGetInfoRes, {bEnableHighFrequency = true})
end

function ActivityServer:ReplaceSpecialStr(input, pattern, replacement)
    local output = string.gsub(input, pattern, replacement)
    return output
end

-- 非通用字段勿加！！
function ActivityServer:HandleActivityLocalizeText(activityInfo)
    if activityInfo then
        if activityInfo.name then activityInfo.name = LocalizeTool.GetTransStr(activityInfo.name) end
        if activityInfo.desc then 
            activityInfo.desc = LocalizeTool.GetTransStr(activityInfo.desc) 
            activityInfo.desc = self:ReplaceSpecialStr(activityInfo.desc, "\\n", "\n")
        end
        if activityInfo.details then 
            activityInfo.details = LocalizeTool.GetTransStr(activityInfo.details)
            activityInfo.details = self:ReplaceSpecialStr(activityInfo.details, "\\n", "\n")
        end
        local taskInfos = activityInfo.task_info 
        if taskInfos and next(taskInfos) then
            for _, taskInfo in ipairs(taskInfos) do
                if taskInfo.name then taskInfo.name = LocalizeTool.GetTransStr(taskInfo.name) end
                if taskInfo.desc then taskInfo.desc = LocalizeTool.GetTransStr(taskInfo.desc) end
            end
        end
        self:HandleActivitySpecialText(activityInfo)
    end
    return activityInfo
end

function ActivityServer:HandleBannerLocalizeText(bannerInfo)
    if bannerInfo and next(bannerInfo) then
        for _, banner in ipairs(bannerInfo) do
            banner.diy_string1 = LocalizeTool.GetTransStr(banner.diy_string1)
            banner.diy_string2 = LocalizeTool.GetTransStr(banner.diy_string2)
        end
        self:HandleBannerSpecialText(bannerInfo)
    end
    return bannerInfo
end

function ActivityServer:HandleActivitySpecialText(activityInfo)
    if VersionUtil.IsShipping() then return end
    activityInfo.name = self:HandleSpecialTextWithRelatedID(activityInfo.actv_id, activityInfo.name, nil, true)
    local taskInfos = activityInfo.task_info 
    if taskInfos and next(taskInfos) then
        for idx, taskInfo in ipairs(taskInfos) do
            local objectives = taskInfo.objectives or {}
            for index, objective in ipairs(objectives) do
                local objectiveID = tostring(objective.id)
                taskInfos[idx].desc = self:HandleSpecialTextWithRelatedID(objectiveID, taskInfo.desc)
            end
            taskInfos[idx].desc = self:HandleSpecialTextWithRelatedID(taskInfo.task_id, taskInfo.desc, "taskID:")
        end
    end
end

function ActivityServer:HandleBannerSpecialText(bannerInfo)
    if VersionUtil.IsShipping() then return end
    for _, banner in ipairs(bannerInfo) do
        banner.diy_string1 = self:HandleSpecialTextWithRelatedID(banner.id, banner.diy_string1)
    end
end

function ActivityServer:HandleSpecialTextWithRelatedID(str1, str2, prefixStr, isWrap)
    local flag = self:OnGetActPersonalized()
    if flag == nil or flag == 0 then
        return str2
    end

    if flag == 1 then
        local txtStyle, first, second
        prefixStr = prefixStr or ""
        if isWrap then
            txtStyle = "%s\n[" .. prefixStr .. "%s]"
            first, second = tostring(str2), tostring(str1)
        else
            txtStyle = "[" .. prefixStr .. "%s] %s"
            first, second = tostring(str1), tostring(str2)
        end
        return string.format(txtStyle, first, second)
    end

    return str2
end

-- 活动收到回包后，处理首页中强关联活动的开启和结束时间
function ActivityServer:InitBannerInfoRelatedByActivity()
    if self.AllBannerFilterInfos then
        for idx, bannerInfo in ipairs(self.AllBannerFilterInfos) do
            if bannerInfo.banner_type == 2 and bannerInfo.connected_event then
                local activityInfo = self.AllActivityInfos[bannerInfo.connected_event]
                if activityInfo then
                    local currentBannerInfo = self.AllBannerFilterInfos[idx]
                    currentBannerInfo.appear_time = activityInfo.start_date
                    currentBannerInfo.disappear_time = activityInfo.end_date
                    currentBannerInfo.is_unlocked = 0
                    for index, value in ipairs(self.BannerFilterInfosByID[bannerInfo.position]) do
                        if value.id == bannerInfo.id then
                            local currentBannerInfoByID = self.BannerFilterInfosByID[bannerInfo.position][index]
                            currentBannerInfoByID.appear_time = activityInfo.start_date
                            currentBannerInfoByID.disappear_time = activityInfo.end_date
                            currentBannerInfoByID.is_unlocked = 0
                        end
                    end
                    loginfo("[ActivityServer] InitBannerInfoRelatedByActivity Update! activityID:", activityInfo.actv_id, "|  bannerID:", bannerInfo.id)
                end
            end
        end
    end
    self:InitAllRelatedTable()
    self.Events.evtBannerInfoUpdate:Invoke()
end

-- 初始化首页数据
function ActivityServer:InitBannerInfo()
    local function OnCSActivityGetBannersRes(res)
        self.AllBannerInfos = {}
        self.AllBannerFilterInfos = {}
        self.BannerInfosByID = {}
        self.BannerFilterInfosByID = {}
        self:FilterBannerInfosByModeConfig(res)
        if res.result == 0 then
            res.banners = self:HandleBannerLocalizeText(res.banners)
            --根据id对res.banners进行排序
            table.sort(res.banners, function(a, b)
                return a.id < b.id
            end)
            self.AllBannerInfos = res.banners
            local systemTime = Facade.ClockManager:GetServerTimestamp()
            for _, bannerInfo in ipairs(self.AllBannerInfos) do
                if self.BannerInfosByID[bannerInfo.position] == nil then
                    self.BannerInfosByID[bannerInfo.position] = {}
                end
                table.insert(self.BannerInfosByID[bannerInfo.position], bannerInfo)
                --若有预发布banner, 则出现后解禁前标记为预告态
                local startTime = bannerInfo.appear_time
                local endTime = bannerInfo.disappear_time
                if bannerInfo.unlock_time and bannerInfo.unlock_time ~= 0 and systemTime < bannerInfo.unlock_time and systemTime > startTime then
                    bannerInfo.is_unlocked = 1 --预告态
                end   
                --根据上下架时间过滤出一份新数据，强关联banner不过滤
                if startTime < systemTime and endTime > systemTime or bannerInfo.banner_type == 2 and bannerInfo.connected_event then
                    if self.BannerFilterInfosByID[bannerInfo.position] == nil then
                        self.BannerFilterInfosByID[bannerInfo.position] = {}
                    end
                    table.insert(self.BannerFilterInfosByID[bannerInfo.position], bannerInfo)
                    table.insert(self.AllBannerFilterInfos, bannerInfo)
                end
            end
        else
            logPb(string.format("InitBannerInfo failed. res.result: %s", res.result))
            return
        end
        self:InitBannerInfoRelatedByActivity()
    end
    local Req = pb.CSActivityGetBannersReq:New()
    Req:Request(OnCSActivityGetBannersRes,{bEnableHighFrequency = true})
end

-- 拉取单个活动数据
function ActivityServer:InitSingleActivityInfo(activityID)
    if not activityID or activityID == 0 then
        self:InitActivityInfo()
        return
    end
    
    self:InitActivityInfo({activityID}, true)
end

-- 创建独立时间戳缓存的频率控制函数
---@return boolean 是否允许触发
function ActivityServer:CreateCooldownChecker(cooldownSeconds)
    self._lastTriggerTime = setdefault(self._lastTriggerTime, 0)
    
    return function()
        local currentTime = Facade.ClockManager:GetLocalTimestamp()
        
        if currentTime - self._lastTriggerTime >= cooldownSeconds then
            self._lastTriggerTime = currentTime
            return true
        else
            return false
        end
    end
end

function ActivityServer:ResetActLastTriggerTime()
    self._lastTriggerTime = 0
end

-- 初始化活动数据
---@param activityIDs table 请求的活动ID集合
---@param isPart boolean 是否为部分拉取 [默认全拉，慎重调用]
---@param optCallback nil|fun(res)
function ActivityServer:InitActivityInfo(activityIDs, isPart, optCallback)
    ---@param res pb_CSActivityGetRes
    local function OnCSActivityGetRes(res)
        if res.result ~= 0 then
            logPb(string.format("InitActivityInfo failed. res.result: %s", res.result))
            if optCallback then optCallback(false) end
            return
        else
            logwarning("[ActivityServer] _CSActivityGetReq successful.")
        end

        -- table.insert(res.actv_infos, ActDataTestTool) -- test

        local now = Facade.ClockManager:GetLocalTimestamp()

        -- 数据容器初始化策略
        if not isPart then
            -- 全量拉取：清空重建
            self._currencyHashMap = {}
            self.ActivityInfosByGroup = {}
            self.AllActivityInfos = {}
            self.PandoraToActivityIDMap = {}
        else
            -- 部分拉取：保留原数据，后续做增量更新
            -- 也可添加预清理逻辑
        end

        self:FilterActivityInfosByModeLearning(res)

        -- 全局字段更新（全量/部分共用）
        self.max_tracking_num = res.max_tracking_num
        self.PropJumpInfos = res.prop_jump_infos

        -- 预告状态活动：只下发了活动的信息，没有下发内容，但是和其他正常活动一样处理
        -- 加上 isPreview 标记
        -- 把 activity_id 字段重命名，和 ActivityInfo 结构统一
        -- 补上 module_type 字段，预告态活动全部当作属于活动系统
        for _, previewInfo in ipairs(res.preview_infos or {}) do
            previewInfo.isPreview = true
            previewInfo.actv_id = previewInfo.activity_id
            previewInfo.module_type = 1
            table.insert(res.actv_infos, previewInfo)
        end

        -- 核心活动数据更新
        for _, activityInfo in pairs(res.actv_infos) do
            -- 预告态活动需要显式传在preview_infos里面，非预告态且未开始的活动会被过滤，不被保存
            local bShouldInsert = ((activityInfo.start_date <= now) or activityInfo.isPreview) and self:IsActivityPrepared(activityInfo)

            -- 部分拉取专用逻辑：清除旧数据
            if isPart then
                local oldInfo = self.AllActivityInfos[activityInfo.actv_id]

                -- 存在旧数据时执行清理
                if oldInfo then
                    -- 从所属分组中移除
                    if self.ActivityInfosByGroup[oldInfo.tab_belonging] then
                        ActHelperTool.ArrayRemoveFirstMatch(self.ActivityInfosByGroup[oldInfo.tab_belonging], function(item)
                            return item.actv_id == oldInfo.actv_id
                        end)
                    end

                    -- 从货币关联表中移除
                    if oldInfo.currency_id and oldInfo.currency_id ~= 0 then
                        ActHelperTool.RemoveFromHashList(self._currencyHashMap, oldInfo.currency_id, oldInfo.actv_id)
                    end

                    -- 清理 Pandora 映射
                    if oldInfo.pandora_info and oldInfo.pandora_info.app_id ~= 0 then
                        self.PandoraToActivityIDMap[oldInfo.pandora_info.app_id] = nil
                    end
                end
                
                -- 是否覆盖写入
                if bShouldInsert then
                    self.AllActivityInfos[activityInfo.actv_id] = nil -- 确保后续插入为新对象
                end
            end

            -- 公共逻辑处理
            if bShouldInsert then
                -- 本地化
                activityInfo = self:HandleActivityLocalizeText(activityInfo)
    
                -- 插入主存储
                self.AllActivityInfos[activityInfo.actv_id] = activityInfo

                -- 按分组归类
                if not self.ActivityInfosByGroup[activityInfo.tab_belonging] then
                    self.ActivityInfosByGroup[activityInfo.tab_belonging] = {}
                end
                table.insert(self.ActivityInfosByGroup[activityInfo.tab_belonging], activityInfo)
    
                -- 建立货币关联
                if activityInfo.currency_id ~= nil and activityInfo.currency_id ~= 0 then
                    ActHelperTool.AddToHashList(self._currencyHashMap, activityInfo.currency_id, activityInfo.actv_id)
                end
    
                -- 特殊活动类型处理：存储赛季物流活动id
                if activityInfo.actv_type == ActivityType.ActivityTypeLotteryTemplate then
                    self._LotteryId = activityInfo.actv_id
                end
    
                -- 任务排序
                self:SortTaskByState(activityInfo.task_info, activityInfo.actv_type)

                -- Pandora 映射更新
                if activityInfo.pandora_info and activityInfo.pandora_info.app_id and activityInfo.pandora_info.app_id ~= 0 then
                    self.PandoraToActivityIDMap[activityInfo.pandora_info.app_id] = activityInfo.actv_id
                end
            elseif isPart then
                -- 部分拉取下不符合条件的活动需彻底移除
                self.AllActivityInfos[activityInfo.actv_id] = nil
            end
        end

        -- 保存货币栏信息
        self._currency_infos = res.currency_infos

        -- 更新货币
        self:UpdateServerActivityCurrency(res.currency_infos)

        -- 拉取首页数据（暂时屏蔽）
        -- self:InitBannerInfo()

        -- 注册活动奖励物品的解锁途径
        self:InitActivityItemUnlockPaths()

        -- 部分活动需要额外拉取数据
        self:PullSpecialActivityInfos()
        
        self:ProcessForecastTimers(res.preview_infos)
        self._tLastActivityInfoUpdate = os.clock()
        
        self.Events.evtActivityInfoUpdate:Invoke()
        self.Events.evtActivityUpdateRedDot:Invoke() -- 更新所有活动红点表现
        
        if optCallback then optCallback(true) end
    end

    activityIDs = setdefault(activityIDs, {})
    isPart = setdefault(isPart, false)

    loginfo(string.format("[ActivityServer] _CSActivityGetReq, activityIDs: %s, isPart: %s", tostring(#activityIDs), tostring(isPart)))

    local CheckInitActivityCooldown = self:CreateCooldownChecker(5)
    if not CheckInitActivityCooldown() then return end

    local Req = pb.CSActivityGetReq:New()
    Req.activity_ids = activityIDs
    Req.is_part = isPart
    Req:Request(OnCSActivityGetRes, {bNeedResendAfterReconnected = true})
end

function ActivityServer:GetSubscribeEventIds()
    local list = {}
    for key, act in pairs(self.AllActivityInfos or {}) do
        if act and act.actv_type == ActivityType.ActivityTypeSubscription then
            if act.info3 then
                table.insert(list, act.info3)
            end
        end
    end
    return list
end

function ActivityServer:InitActivityItemUnlockPaths()
    Server.ItemUnlockPathServer:RemoveItemUnlockPathsByGroup(self.serverName)

    for _, propJumpInfo in ipairs(self.PropJumpInfos or {}) do
        local propID = propJumpInfo.prop_id
        local jumpPair = propJumpInfo.jump_pair
        local activityID, activityType, activityName, jumpID, armedForceMode

        if jumpPair and jumpPair[1] then
            local jumpInfo = jumpPair[1]
            activityID = jumpInfo.activity_id
            jumpID = jumpInfo.jump_id

            if activityID then
                local activityInfo = self.AllActivityInfos[activityID]
                if activityInfo then
                    activityType = activityInfo.actv_type
                    activityName = activityInfo.name
                    -- armedForceMode = activityInfo.mode_tag == EActGameMode.MP and EArmedForceMode.MP or EArmedForceMode.SOL
                    armedForceMode = EActGameMode.MP --策划说的先写死

                    Server.ItemUnlockPathServer:AddItemUnlockPathInfo(
                        self.serverName,                    -- groupKey: 使用活动ID作为分组
                        armedForceMode,                     -- gameMode: 活动所属模式
                        EItemUnlockSources.Activity,        -- source: 从活动获得
                        propID,                             -- itemID: 奖励物品ID
                        jumpID,                             -- jumpID: 活动跳转ID
                        {                                   -- optExtraData: 额外信息
                            activityID = activityID,
                            activityType = activityType,
                            activityName = activityName,
                        }
                    )
                end
            end
        end
    end
end

-- 初始化所有牵连表数据
function ActivityServer:InitAllRelatedTable()
    self:InitRelatedActTable()
    self:InitRelatedAct2Table()
    self:InitRelatedBannerTable()
end

-- 初始化牵连活动表数据
function ActivityServer:InitRelatedActTable()
    self.AllActivityInfosWithMode = {{}, {}}
    for actv_id, activityInfo in pairs(self.AllActivityInfos) do
        if self.AllActivityInfosWithMode[activityInfo.mode_tag] == nil then
            for mode_tag, _ in ipairs(self.AllActivityInfosWithMode) do
                self.AllActivityInfosWithMode[mode_tag][actv_id] = activityInfo
            end
        else
            self.AllActivityInfosWithMode[activityInfo.mode_tag][actv_id] = activityInfo
        end
    end
end

function ActivityServer:InitRelatedAct2Table()
    self.ActivityInfosByGroupWithMode = {{}, {}}
    for groupID, activityList in pairs(self.ActivityInfosByGroup) do
        for i, activityInfo in ipairs(activityList) do
            if self.ActivityInfosByGroupWithMode[activityInfo.mode_tag] == nil then
                for mode_tag, _ in ipairs(self.ActivityInfosByGroupWithMode) do
                    if self.ActivityInfosByGroupWithMode[mode_tag][groupID] == nil then
                        self.ActivityInfosByGroupWithMode[mode_tag][groupID] = {}
                    end
                    table.insert(self.ActivityInfosByGroupWithMode[mode_tag][groupID], activityInfo)
                end
            else
                if self.ActivityInfosByGroupWithMode[activityInfo.mode_tag][groupID] == nil then
                    self.ActivityInfosByGroupWithMode[activityInfo.mode_tag][groupID] = {}
                end
                table.insert(self.ActivityInfosByGroupWithMode[activityInfo.mode_tag][groupID], activityInfo)
            end
        end
    end
end

-- 初始化牵连首页表数据
function ActivityServer:InitRelatedBannerTable()
    self.BannerFilterInfosByIDWithMode = {{}, {}}
    for id, bannerList in pairs(self.BannerFilterInfosByID) do
        for i, bannerInfo in ipairs(bannerList) do
            if self.BannerFilterInfosByIDWithMode[bannerInfo.mode_tag] == nil then
                for mode_tag, _ in ipairs(self.BannerFilterInfosByIDWithMode) do
                    if self.BannerFilterInfosByIDWithMode[mode_tag][id] == nil then
                        self.BannerFilterInfosByIDWithMode[mode_tag][id] = {}
                    end
                    table.insert(self.BannerFilterInfosByIDWithMode[mode_tag][id], bannerInfo)
                end
            else
                if self.BannerFilterInfosByIDWithMode[bannerInfo.mode_tag][id] == nil then
                    self.BannerFilterInfosByIDWithMode[bannerInfo.mode_tag][id] = {}
                end
                table.insert(self.BannerFilterInfosByIDWithMode[bannerInfo.mode_tag][id], bannerInfo)
            end
        end
    end
end

-- 更新指定活动牵连表数据
function ActivityServer:UpdateRelatedActTableByID(actv_id)
    local mode = self:CheckIsInMode()
    local activityInfo = self.AllActivityInfos[actv_id]
    local groupID = self:GetGroupIDByActivityID(actv_id)

    if self.AllActivityInfosWithMode[mode] then
        self.AllActivityInfosWithMode[mode][actv_id] = activityInfo
    end

    local groupTable = self.ActivityInfosByGroupWithMode[mode] and self.ActivityInfosByGroupWithMode[mode][groupID]
    if groupTable then
        for index, value in ipairs(groupTable) do
            if value.actv_id == actv_id then
                groupTable[index] = activityInfo
                break
            end
        end
    end
end

-- 拉取特殊活动数据 [如有]
function ActivityServer:PullSpecialActivityInfos()
    self:_PullTheSpringFestival()
    self:_PullMPCommanderRank()
end

-------------------------------通用数据区-------------------------------------------
---comment 根据activityID判断活动是否存在
function ActivityServer:CheckActivityExistsByID(activityID)
    local activityInfo = self:GetActivityInfoByActivityID(activityID)
    if activityInfo then
        return true
    end
    return false
end

---comment 根据首页类型获取所有首页数据
---@return table
function ActivityServer:GetBannerInfosByID(ID)
    if self.BannerInfosByID == nil or not next(self.BannerInfosByID) then
        logwarning("[ActivityServer] GetBannerInfosByID BannerInfosByID is nil or {}.")
        return nil
    end

    return self.BannerInfosByID[ID]
end

---comment 根据首页类型获取所有过滤的首页数据
---@return table
function ActivityServer:GetFilterBannerInfosByID(ID)
    local bannerListByID = self:CheckFilterStatus() and self.BannerFilterInfosByIDWithMode[self:CheckIsInMode()] or self.BannerFilterInfosByID
    local bannerInfos = bannerListByID[ID]
    if bannerListByID == nil or bannerInfos == nil then
        logwarning("[ActivityServer] GetFilterBannerInfosByID BannerFilterInfosByID is nil or {}. ID = ", ID)
        return nil
    end

    return bannerInfos
end

---comment 获取所有活动的页签
---@return table
function ActivityServer:GetActivityGroups()
    local activityInfosByGroup = self:CheckFilterStatus() and self.ActivityInfosByGroupWithMode[self:CheckIsInMode()] or self.ActivityInfosByGroup
    if activityInfosByGroup == nil then
        logwarning("[ActivityServer] GetActivityGroups ActivityInfosByGroup is nil.")
        return nil
    end

    local activityGroups = {}
    for key, val in pairs(activityInfosByGroup) do
        if key > 0 then
            table.insert(activityGroups, key)
        end
    end

    activityGroups = table.unique(activityGroups, true)
    table.sort(
        activityGroups,
        function(a, b)
            return a < b
        end
    )

    return activityGroups
end

---comment 根据页签id获取所有活动数据:[1~5]
---@param groupID any
---@return nil
function ActivityServer:GetActivityInfosByGroupID(groupID)
    local activityInfosByGroup = self:CheckFilterStatus() and self.ActivityInfosByGroupWithMode[self:CheckIsInMode()] or self.ActivityInfosByGroup
    if activityInfosByGroup == nil then
        logwarning("[ActivityServer] GetActivityInfosByGroupID ActivityInfosByGroup is nil.")
        return nil
    end

    return activityInfosByGroup[groupID]
end

---comment 根据页签id获取所有活动id列表:[1~5]
---@param groupID any
---@return nil
function ActivityServer:GetActivityIDsByGroupID(groupID, bActiveGlobal)
    local activityInfosByGroup = self:CheckFilterStatus(bActiveGlobal) and self.ActivityInfosByGroupWithMode[self:CheckIsInMode()] or self.ActivityInfosByGroup
    if activityInfosByGroup == nil then
        logwarning("[ActivityServer] GetActivityIDsByGroupID ActivityInfosByGroup is nil.")
        return nil
    end

    local activityIDs = {}
    local finishedActivityIDs = {}
    for key, activityInfos in pairs(activityInfosByGroup) do
        if key == groupID then
            for activityID, activityInfo in ipairs(activityInfos) do
                local bFinish = self:IsActivityCompleted(activityID)
                if bFinish then
                    table.insert(finishedActivityIDs, activityInfo.actv_id)
                else
                    table.insert(activityIDs, activityInfo.actv_id)
                end
            end
        end
    end
    for _, id in ipairs(finishedActivityIDs) do
        table.insert(activityIDs, id)
    end

    return activityIDs
end

--通过活动类型获取Tab
function ActivityServer:GetTabByActvType(actv_type, bActiveGlobal)
    if actv_type == nil then
        return
    end
    local activityInfosByGroup = self:CheckFilterStatus(bActiveGlobal) and self.ActivityInfosByGroupWithMode[self:CheckIsInMode()] or self.ActivityInfosByGroup
    for group_id, infos in pairs(activityInfosByGroup or {}) do
        for index, info in ipairs(infos or {}) do
            if info.actv_type == actv_type then
                return group_id, index
            end
        end
    end
end

---comment 根据ActivityID返回服务器下发的ActivityInfo
---@param activityID any
---@return pb_ActivityInfo
function ActivityServer:GetActivityInfoByActivityID(activityID)
    local activityList = self:CheckFilterStatus() and self.AllActivityInfosWithMode[self:CheckIsInMode()] or self.AllActivityInfos
    local activityInfo = activityList[activityID]
    if activityInfo == nil then
        logwarning("[ActivityServer] GetActivityInfoByActivityID failed, activityID:", activityID)
    end
    return activityInfo
end

function ActivityServer:GetActivityInfoByPandoraAppID(appID)
    local activityID = self.PandoraToActivityIDMap[appID]
    if not activityID then return end

    local activityInfo = self.AllActivityInfos[activityID]
    return activityInfo
end

---comment 根据ActivityID和tag返回服务器下发的ActivityInfo
---@param activityID any
---@return unknown
function ActivityServer:GetActivityInfoByActivityIDAndTag(activityID, activityTag)
    -- 回流活动数据已合并回ActivityServer
    return self:GetActivityInfoByActivityID(activityID)
end

---comment 根据ActivityID返回活动类型
---@param activityID any
---@return unknown
function ActivityServer:GetActivityTypeByActivityID(activityID)
    local activityList = self:CheckFilterStatus() and self.AllActivityInfosWithMode[self:CheckIsInMode()] or self.AllActivityInfos
    local activityInfo = activityList[activityID]
    if activityInfo == nil then
        logwarning("[ActivityServer] GetActivityTypeByActivityID failed, activityID:", activityID)
    else
        return activityInfo.actv_type
    end

    return ActivityType.ActivityTypeUnknown
end

---comment 根据活动id返回所属页签
---@param activityID any
---@return unknown
function ActivityServer:GetGroupIDByActivityID(activityID)
    local activityList = self:CheckFilterStatus() and self.AllActivityInfosWithMode[self:CheckIsInMode()] or self.AllActivityInfos
    local activityInfo = activityList[activityID]
    if activityInfo == nil then
        logwarning("[ActivityServer] GetGroupIDByActivityID failed, activityID:", activityID)
    else
        return activityInfo.tab_belonging
    end

    return nil
end

---comment 根据活动id获取所属一级和二级页签
---@param  activityID any
---@return unknown
function ActivityServer:GetActivityTabByActivityID(activityID, bActiveGlobal)
    local activityList = self:CheckFilterStatus(bActiveGlobal) and self.AllActivityInfosWithMode[self:CheckIsInMode()] or self.AllActivityInfos
    local activityInfo = activityList[activityID]
    local groupID = nil
    if activityInfo ~= nil then
        groupID = activityInfo.tab_belonging
    end
    local activityIDs = {}
    local activityInfosByGroup = self:CheckFilterStatus(bActiveGlobal) and self.ActivityInfosByGroupWithMode[self:CheckIsInMode()] or self.ActivityInfosByGroup
    for key, activityinfos in pairs(activityInfosByGroup) do
        if key == groupID then
            for key, activity in ipairs(activityinfos) do
                table.insert(activityIDs, activity.actv_id)
            end
        end
    end
    local index = nil
    for key, activityid in ipairs(activityIDs) do
        if activityid == activityID then
            index = key
        end
    end
    return groupID, activityID
end

---comment 根据活动id返回所属页签下的活动数量
---@param activityID any
---@return unknown
function ActivityServer:GetActivityTabNumByActivityID(activityID)
    local activityInfosByGroup = self:CheckFilterStatus() and self.ActivityInfosByGroupWithMode[self:CheckIsInMode()] or self.ActivityInfosByGroup
    local groupID = self:GetGroupIDByActivityID(activityID)
    if groupID and activityInfosByGroup[groupID] then
        return #activityInfosByGroup[groupID]
    end
    return nil
end

---comment 根据活动id返回跟踪状态
---@param activityID any
---@return unknown
function ActivityServer:GetActivityTrackingState(activityID)
    local activityList = self:CheckFilterStatus() and self.AllActivityInfosWithMode[self:CheckIsInMode()] or self.AllActivityInfos
    local activityInfo = activityList[activityID]
    if activityInfo ~= nil then
        return activityInfo.is_tracking
    end

    return false
end

---comment 根据活动id返回玩家信息
---@param activityID any
---@return unknown
function ActivityServer:GetPlayerInfo(activityID)
    local activityInfo = self.AllActivityInfos[activityID]
    if activityInfo ~= nil then
        return activityInfo.player_info
    end

    return nil
end

---comment 根据活动ID获取任务数据
---@param activityID any
---@return unknown
function ActivityServer:GetTaskInfosByActivityID(activityID)
    local activityInfo = self.AllActivityInfos[activityID]
    if activityInfo ~= nil then
        return activityInfo.task_info
    end

    logwarning("[ActivityServer] GetTaskInfosByActivityID is nil. activityID:" .. activityID)
    return nil
end

---comment 根据活动ID获取跳转任务数据
---@param activityID any
---@return unknown
function ActivityServer:GetJumpTaskInfosByActivityID(activityID)
    local activityInfo = self.AllActivityInfos[activityID]
    if activityInfo and activityInfo.jump_to_config then
        return activityInfo.jump_to_config
    end

    logwarning("[ActivityServer] GetJumpTaskInfosByActivityID is nil. activityID:" .. activityID)
    return nil
end

---comment 根据活动ID返回下次刷新时间
---@param activityID any
---@return unknown
function ActivityServer:GetRefreshTimeByActivityID(activityID)
    local activityInfo = self.AllActivityInfos[activityID]
    if activityInfo ~= nil then
        return activityInfo.next_refresh_time
    end

    return nil
end

---comment 根据活动ID返回活动开始时间
---@param activityID any
---@return unknown
function ActivityServer:GetStartDateByActivityID(activityID)
    local activityInfo = self.AllActivityInfos[activityID]
    if activityInfo ~= nil then
        return activityInfo.start_date
    end

    return nil
end

---comment 根据活动ID和任务ID获取详细任务数据
---@param activityID any
---@param taskID any
---@return unknown
function ActivityServer:GetTaskInfo(activityID, taskID)
    local taskInfos = self:GetTaskInfosByActivityID(activityID)
    if taskInfos then
        for _, taskInfo in ipairs(taskInfos) do
            if taskInfo.taskID == taskID then
                return taskInfo
            end
        end
    end
    logwarning("[ActivityServer] GetTaskInfo is nil. TaskID:" .. taskID)
    return nil
end

---comment 根据活动ID返回里程碑奖励信息
---@param activityID any
---@return unknown
function ActivityServer:GetMilestonesReward(activityID)
    local activityInfo = self.AllActivityInfos[activityID]
    if activityInfo ~= nil then
        return activityInfo.milestone_award
    end

    return nil
end

---comment 根据活动ID和tag返回里程碑奖励信息
function ActivityServer:GetMilestonesRewardByIDAndTag(activityID, activityTag)
    return self:GetMilestonesReward(activityID)
end

---comment 根据活动ID返回新兵里程碑任务
---@param activityID any
---@return unknown
function ActivityServer:GetRecruitsMilestonesTask(activityID)
    local activityInfo = self.AllActivityInfos[activityID]
    if activityInfo ~= nil then
        --旧的(40)返回recruit_ms_tasks字段，新的(43)返回task_info字段
        if activityInfo.actv_type == ActivityType.ActivityTypeRecruitGrowthMilestone then
            return activityInfo.recruit_ms_tasks
        elseif activityInfo.actv_type == ActivityType.ActivityTypeRecruitNewMilestone then
            return activityInfo.task_info
        end
    end

    return nil
end

---comment 返回据某个活动类型下的所有活动
---@param activityType any
---@return table
function ActivityServer:GetActivityInfosByType(activityType)
    local activityList = {}
    for _, activityInfo in pairs(self.AllActivityInfos) do
        if activityInfo.actv_type == activityType then
            table.insert(activityList, activityInfo)
        end
    end

    return activityList
end

---comment 根据活动ID返回红点上新开关
---@param activityID any
---@return unknown
function ActivityServer:GetActivityBrowseReddotByActivityID(activityID)
    local activityInfo = self.AllActivityInfos[activityID]
    if activityInfo ~= nil then
        return activityInfo.listing_tips
    end

    return nil
end

--本地货币表
function ActivityServer:_UpdateLocalCurrenNum(currencyID, currencyNum)
    if currencyID == nil or currencyNum == nil then
        return
    end
    local updateActIdList = self._currencyHashMap[currencyID]
    if updateActIdList then
        for _, activityID in pairs(updateActIdList) do
            self.AllActivityInfos[activityID].currency_num = currencyNum
        end
    end
end

function ActivityServer:UpdateLocalCurrenNumByCycle(activityList)
    for _, activityInfo in pairs(activityList or {}) do --后续协议可以改成actv_info
        if activityInfo.currency_id ~= 0 then
            self:_UpdateLocalCurrenNum(activityInfo.currency_id, activityInfo.currency_num)
        end
    end
end

--后台货币表
function ActivityServer:_UpdateServerCurrenNum(currency_id, currency_num)
    if currency_id == nil or currency_num == nil then
        return
    end
    if self._currenMap == nil then
        self._currenMap = {}
    end
    self._currenMap[currency_id] = currency_num
    --更新导航栏数据
    self:_RefreshCurrencyNum(currency_id, currency_num)
end

--获取后台货币表对应的货币数量
function ActivityServer:GetCurrenNum(currency_id)
    return self._currenMap and self._currenMap[currency_id]
end


--针对回包res.actv_infos
function ActivityServer:UpdateLocalActivityCurrency(activityList)
    self:UpdateLocalCurrenNumByCycle(activityList)
    
    --拉取赛季物流货币:解决偶现数量不刷新问题
    self:SendLogisticsCurrencyNum()
end

--货币栏
function ActivityServer:GetCurrencyColumn(currencyId)
    local list = {}
    if self.AllActivityInfos == nil then
        return list
    end
    for index, info in ipairs(self._currency_infos or {}) do
        --判断是否存在改活动
        local actId = info.use_activity_info and info.use_activity_info.activity_id
        if actId and self.AllActivityInfos[actId] then
            local child = {}
            for _, act in ipairs(info.get_activity_infos or {}) do
                if act.activity_id then
                    local activity = self.AllActivityInfos[act.activity_id]
                    if activity then
                        local time = activity.end_date
                        local isEnd = true
                        if type(time) == "number" then
                            isEnd = time <= Facade.ClockManager:GetServerTimestamp()
                        end
                        time = activity.start_date
                        if type(time) == "number" and time <= Facade.ClockManager:GetServerTimestamp() and not isEnd then
                            table.insert(child, act)
                        end
                    end
                end
            end
            table.insert(list, {
                list    = child,
                id      = info.id,
                num     = info.num,
                desc    = info.use_activity_info.desc,
                act_id  = info.use_activity_info.activity_id,
                jump_id = info.use_activity_info.jump_id,
            })
        end
    end

    if currencyId then
        for index, value in ipairs(list or {}) do
            if value.id == currencyId then
                return value
            end
        end
        return nil
    end
    return list
end

--针对回包res.currency_infos
function ActivityServer:UpdateServerActivityCurrency(currencyList)
    for _, value in pairs(currencyList or {}) do
        self:_UpdateServerCurrenNum(value.id, value.num)
    end
    --合作凭证数量(藏品仓库查询)
    local itemId = ECurrencyItemId.CooperateVoucher
    local num = self:_GetCollectionItemNum(itemId)
    self:_UpdateServerCurrenNum(itemId, num)
end

--活动回包更新数据
function ActivityServer:UpdateServerActivityInfoList(actv_infos, currency_infos)
    for _, activityInfo in pairs(actv_infos or {}) do
        local actID = activityInfo.actv_id
        local groupID = activityInfo.tab_belonging

        if self.AllActivityInfos[actID] then
            activityInfo = self:HandleActivityLocalizeText(activityInfo)
            self:SortTaskByState(activityInfo.task_info, activityInfo.actv_type)
            
            self.AllActivityInfos[actID] = activityInfo
            self.Events.evtActivityUpdateRedDot:Invoke({activityInfo})
    
            if self.ActivityInfosByGroup[groupID] then
                for index, value in ipairs(self.ActivityInfosByGroup[groupID]) do
                    if value.actv_id == actID then
                        self.ActivityInfosByGroup[groupID][index] = activityInfo
                        break
                    end
                end
            end

            self:UpdateRelatedActTableByID(actID)
        end
    end

    self:UpdateServerActivityCurrency(currency_infos)
end

--- 根据任务进度排序[deprecated = true]
function ActivityServer:Calculateprogress(task1)
    if task1 and #task1 > 1 then
        if task1[1].objectives and #task1[1].objectives >= 1 then
            for index, value in ipairs(task1) do
                if value.objectives and #value.objectives >= 1 then
                    if value.objectives[1].progress_max ~= 0 then
                        value.objectives[1].num = value.objectives[1].progress / value.objectives[1].progress_max
                    else
                        value.objectives[1].num = 0
                    end
                    if value.state == 4 then
                        value.objectives[1].num = -1
                    end
                end
            end
        end
    end
    return task1
end

---comment 通用任务排序
---@param taskList ActivityTaskInfo 任务列表
---@param activityType ActivityType 活动类型，UI层可不传
function ActivityServer:SortTaskByState(taskList, activityType)
    if not taskList or not next(taskList) then
        return
    end

    if activityType then
        for _, type in ipairs(self._sortShieldList) do
            if type == activityType then
                return
            end
        end
    end

    for i = 2, #taskList do
        local key = taskList[i]
        local j = i - 1
        while j > 0 do
            local bSwap = false
            if taskList[j].state == ActivityTaskState.ActivityTaskStateRewarded and key.state ~= ActivityTaskState.ActivityTaskStateRewarded then
                bSwap = true
            elseif taskList[j].state ~= ActivityTaskState.ActivityTaskStateCompleted and key.state == ActivityTaskState.ActivityTaskStateCompleted then
                bSwap = true
            elseif taskList[j].state == key.state then
                if key.weight and taskList[j].weight < key.weight then
                    bSwap = true
                end
            end
            if bSwap then
                taskList[j + 1] = taskList[j]
                j = j - 1
            else
                break
            end
        end
        taskList[j + 1] = key
    end
end

-------------------------------活动一键领取-------------------------------------------
function ActivityServer:SendOneClickRewardClaimReq(activityID, taskLine)
    local function OnCSActivityResponse(res)
        if res.result == 0 then
            self:UpdateLocalActivityCurrency(res.actv_infos)
            self:UpdateServerActivityInfoList(res.actv_infos, res.currency_infos)
            self.Events.evtActivityTaskChange:Invoke(activityID, nil)
            self.Events.evtActivityTaskReward:Invoke(res.data_change, res.expand_info)
        else
            logPb(string.format("SendOneClickRewardClaimReq failed. res.result: %s activityID: %s, taskLine: %s", res.result, activityID, taskLine))
            return
        end
    end
    
    taskLine = setdefault(taskLine, 0)
    local Req = pb.CSActivityOneClickRewardClaimReq:New()
    Req.actv_id = activityID
    Req.task_line = taskLine
    self.Events.evtEnableRewardNTFCall:Invoke("Hero", false)
    Req:Request(OnCSActivityResponse)
end

-------------------------------每日任务刷新-------------------------------------------
function ActivityServer:SendDailyTaskRefreshReq(activityID, taskId)
    local function OnCSActivityResponse(res)
        if res.result == 0 then
            res.actv_info = self:HandleActivityLocalizeText(res.actv_info)
            self.AllActivityInfos[activityID].task_info = res.actv_info.task_info
            self.AllActivityInfos[activityID].available_refresh_times = res.actv_info.available_refresh_times
            self.Events.evtActivityTaskChange:Invoke(activityID)
        else
            logPb(string.format("SendDailyTaskRefreshReq failed. res.result: %s activityID: %s, taskId: %s", res.result, activityID, taskId))
            return
        end
    end

    local Req = pb.CSActivityRefreshDailyActivityReq:New()
    Req.actv_id = activityID
    Req.task_id = taskId
    Req:Request(OnCSActivityResponse)
end

-------------------------------任务+里程碑区-------------------------------------------
function ActivityServer:SendActivityTaskReceiveAwardReq(activityID, taskId)
    local function OnCSActivityResponse(res)
        if res.result == 0 then
            self:UpdateLocalActivityCurrency(res.actv_infos)
            self:UpdateServerActivityInfoList(res.actv_infos, res.currency_infos)
            self.Events.evtActivityTaskChange:Invoke(activityID, taskId)
            self.Events.evtActivityTaskReward:Invoke(res.data_change, res.expand_info)
        else
            logPb(string.format("SendActivityTaskReceiveAwardReq failed. res.result: %s activityID: %s, taskId: %s", res.result, activityID, taskId))
            return
        end
    end

    local Req = pb.CSActivityTaskReceiveAwardReq:New()
    Req.tasks = {}
    local task = pb.CSActivityTask:New()
    task.actv_id = activityID
    task.task_id = taskId
    table.insert(Req.tasks, task)
    self.Events.evtEnableRewardNTFCall:Invoke("Hero", false)
    Req:Request(OnCSActivityResponse)
end

--请求硬件安装/卸载
function ActivityServer:SendActivityInstallPropReq(activityID, props, del_props)
    local function OnInstallFunc(res)
        if res.result == 0 then
            if res.moss_construct_info and self.AllActivityInfos and self.AllActivityInfos[activityID] then
                self.AllActivityInfos[activityID].moss_construct_info = res.moss_construct_info
            end
            self.Events.evtHardwareInfoUpdate:Invoke(activityID)
        end
    end
    local req = pb.CSActivityInstallPropReq:New()
    if req and activityID and props and del_props then
        req.activity_id = activityID
        req.props = props
        req.del_props = del_props
        req:Request(OnInstallFunc)
    end
end

--里程碑领取奖励
function ActivityServer:SendReceiveMSAwardReq(activityID)
    local function OnMSAwardReqFunc(res)
        if res.result == 0 and res.activity_info then
            --里程碑领取奖励,尝试刷新赛季物流货币/并拉取赛季物流货币
            self:UpdateLocalActivityCurrency()
            --尝试获取要展示的奖励
            local change = {}
            local info = self:GetActivityInfoByActivityID(activityID or 0)
            if info then
                for index, value in ipairs(info.milestone_award or {}) do
                    if value.prop and value.prop[1] and value.prop[1].received == false then
                        --保持领取前的状态
                        change[index] = true
                    end
                end
            end
            self:UpdateServerActivityInfoList({res.activity_info})
            self.Events.evtActivityTaskChange:Invoke(activityID, change)
        end
    end
    --活动id判断
    if activityID == nil then
        return
    end
    local req = nil
    --活动类型判断
    if self:GetActivityTypeByActivityID(activityID) == ActivityType.ActivityTypeConstructMoss then
        req = pb.CSActivityMossReceiveMSAwardReq:New()--moss里程碑
    else
        req = pb.CSActivityRelinkReceiveMSAwardReq:New()--脑机里程碑
    end
    if req then
        req.activity_id = activityID
        req:Request(OnMSAwardReqFunc)
    end
end

--建设moss请求覆盖动画数据
function ActivityServer:SendActivityAnimationUpdateReq(activityID)
    local function OnAnimationUpdateFunc(res)
        if res.result == 0 then
            if res.moss_construct_info and self.AllActivityInfos and self.AllActivityInfos[activityID] then
                self.AllActivityInfos[activityID].moss_construct_info = res.moss_construct_info
            end
            self.Events.evtActivityTaskChange:Invoke(activityID)
        end
    end
    local req = pb.CSActivityAnimationUpdateReq:New()
    if req and activityID then
        req.activity_id = activityID
        req:Request(OnAnimationUpdateFunc)
    end
end

---活动抽奖模板标题/描述
function ActivityServer:GetDrawData(activityID)
    local info = self:GetActivityInfoByActivityID(activityID or 0)
    if info then
        --物流兼容info3字段(details)
        local txt = info.details
        if info.info3 and string.len(info.info3) > 0 then
            txt = info.info3
        end
        local data = {
            desc = info.desc,
            details = txt,
        }
        return data
    end
end

--刷新导航栏货币数量
function ActivityServer:_RefreshCurrencyNum(itemId, num)
    --更新导航栏货币数量
    if itemId and num then
        Server.CurrencyServer:RefreshCurrencyNum(itemId, num, num)
    end
end

--获取藏品道具数量
function ActivityServer:_GetCollectionItemNum(itemId)
    local itemData = Server.CollectionServer:GetCollectionItemById(itemId)
    if itemData then
        return itemData.num
    end
    return 0
end

--请求物流货币数量
function ActivityServer:SendLogisticsCurrencyNum(activityID)
    local function OnCurrencyFunc(res)
        if res.result == 0 then
            for index, info in pairs(self.AllActivityInfos or {}) do
                if info and info.currency_id == res.currency_id then
                    info.currency_num = res.currency_num
                end
            end
            --合作物流(到藏品查看)
            self:UpdateServerActivityCurrency()
            --赛季物流(协议获取)
            self:UpdateServerActivityCurrency({{id = res.currency_id, num = res.currency_num}})
        end
    end
    local req = pb.CSActivityGetCurrencyReq:New()
    if req and activityID then
        req.activity_id = activityID
        req:Request(OnCurrencyFunc)
    end
end

--获取活动抽奖的奖池ID
function ActivityServer:GetLotteryPoolId(activityID)
    local info = self:GetActivityInfoByActivityID(activityID or 0)
    if info and info.lottery_info then
        return info.lottery_info.LotteryPoolId
    end
end

--获取活动抽奖凭证id
function ActivityServer:GetLotteryVoucherId(activityID)
    local info = self:GetActivityInfoByActivityID(activityID or 0)
    if info and info.lottery_info then
        return info.lottery_info.lottery_item_id or 0
    end
    return 0
end

--获取活动抽奖物流凭证结束时间
function ActivityServer:GetVoucherEndTime()
    for key, info in pairs(self.AllActivityInfos or {}) do
        if info and info.actv_type == ActivityType.ActivityTypeLotteryTemplate then
            if info.lottery_info then
                return info.lottery_info.currency_effective_day or 0
            end
        end
    end
    return 0
end

--获取活动抽奖物流跳转数据
function ActivityServer:GetLogisticsInfo(currencyId)

    local actv_type = ActivityType.ActivityTypeLotteryTemplate
    if currencyId == ECurrencyItemId.CooperateVoucher then
        actv_type = ActivityType.ActivityLotteryDrawPanel
    end

    for key, info in pairs(self.AllActivityInfos or {}) do
        if info and info.actv_type == actv_type then
            if info.lottery_info then
                return info.lottery_info.get_currency_info_list
            end
        end
    end
end

--获取活动代币数量
function ActivityServer:GetCurrencyNum(currency_id)
    for index, info in pairs(self.AllActivityInfos or {}) do
        if info and info.currency_id == currency_id then
            return info.currency_num or 0
        end
    end
    return 0
end
---end

function ActivityServer:SendActivityReceiveMilestoneAwardReq(activityID, itemIndex)
    local function OnCSActivityReceiveMilestoneAwardRes(res)
        if res.result == 0 then
            self:UpdateLocalActivityCurrency(res.actv_infos)
            self:UpdateServerActivityInfoList(res.actv_infos, res.currency_infos)
            self.Events.evtActivityTaskChange:Invoke(activityID)
            self.Events.evtActivityMilestoneAward:Invoke(res.data_change, res.expand_info)
        else
            logPb(string.format("SendActivityReceiveMilestoneAwardReq failed. res.result: %s activityID: %s, itemIndex: %s", res.result, activityID, itemIndex))
            return
        end
    end

    local Req = pb.CSActivityReceiveMilestoneAwardReq:New()
    Req.actv_id = activityID
    Req.stages = {itemIndex}
    self.Events.evtEnableRewardNTFCall:Invoke("Hero", false)
    Req:Request(OnCSActivityReceiveMilestoneAwardRes)
end

function ActivityServer:SendActivityExchangeMilestoneAwardReq(activityID, itemIndex)
    local function OnCSActivityExchangeMilestoneAwardRes(res)
        if res.result == 0 then
            self:UpdateLocalActivityCurrency({res.actv_info})
            self:UpdateServerActivityInfoList({res.actv_info})
            self.Events.evtActivityTaskChange:Invoke(activityID)
            self.Events.evtActivityMilestoneAward:Invoke(res.data_change, res.expand_info)
        else
            logPb(string.format("SendActivityExchangeMilestoneAwardReq failed. res.result: %s activityID: %s, itemIndex: %s", res.result, activityID, itemIndex))
            return
        end
    end

    local Req = pb.CSActivityExchangeMilestoneAwardReq:New()
    Req.actv_id = activityID
    Req.stages = {itemIndex}
    self.Events.evtEnableRewardNTFCall:Invoke("Hero", false)
    Req:Request(OnCSActivityExchangeMilestoneAwardRes)
end

function ActivityServer:SendActivityReceiveFinalAwardReq(activityID, type)
    local function OnCSActivityReceiveFinalAwardReq(res)
        if res.result == 0 then
            self:UpdateServerActivityInfoList(res.actv_infos, res.currency_infos)
            self.Events.evtActivityTaskChange:Invoke(activityID)
            self.Events.evtActivityMilestoneAward:Invoke(res.data_change, res.expand_info)
        else
            logPb(string.format("SendActivityReceiveFinalAwardReq failed. res.result: %s activityID: %s, type: %s", res.result, activityID, type))
            return
        end
    end

    local Req = pb.CSActivityReceiveFinalAwardReq:New()
    -- 这里等后台修改，不需要传多个ID
    local activityIDs = {}
    table.insert(activityIDs, activityID)
    local newbieTypeList = {}
    table.insert(newbieTypeList, type)
    Req.actv_ids = activityIDs
    Req.newbie_type_list = newbieTypeList
    self.Events.evtEnableRewardNTFCall:Invoke("Hero", false)
    Req:Request(OnCSActivityReceiveFinalAwardReq)
end

-------------------------------------------------归档活动区---------------------------------------------------------
--- 领取批次奖励
function ActivityServer:SendFilingSeqAwardReq(activityID, timeline, seqIdx)
    local function OnCSActivityReceiveSequenceAwardReq(res)
        if res.result == 0 then
            self:UpdateServerActivityInfoList(res.activity_infos)

            self.Events.evtActivityTaskChange:Invoke(activityID)
            self.Events.evtFilingOnSequenceItemReceived:Invoke(timeline, seqIdx)
            self.Events.evtActivityTaskReward:Invoke(res.data_change, res.expand_info)
        else
            return
        end
    end

    local Req = pb.CSActivityReceiveSequenceAwardReq:New()

    Req.activity_id = activityID
    Req.task_line = timeline
    Req.sequence_order = seqIdx
    self.Events.evtEnableRewardNTFCall:Invoke("Hero", false)
    Req:Request(OnCSActivityReceiveSequenceAwardReq)
end

--- 首次打开活动
function ActivityServer:SendFilingFirstOpenReq(activityID)
    local function OnCSActivityArchiveFirstOpenReq(res)
        if res.result == 0 then
            self.AllActivityInfos[activityID].archive_info.is_first = false
            self.Events.evtActivityTaskChange:Invoke(activityID)
        else
            return
        end
    end

    local Req = pb.CSActivityArchiveFirstOpenReq:New()

    Req.activity_id = activityID
    Req:Request(OnCSActivityArchiveFirstOpenReq)
end

--- 语音播放完毕
function ActivityServer:SendFilingAudioFinishReq(activityID, timeline, seqIdx, bIsEnd)
    local function OnCSActivityArchiveAudioFinishReq(res)
        if res.result == 0 then
            local tasklines = self.AllActivityInfos[activityID].archive_info.task_line_info_list
            for index, value in ipairs(tasklines) do
                if value.task_line == timeline then
                    value.last_sequence_order = value.last_sequence_order + 1
                end
            end
            self.Events.evtActivityTaskChange:Invoke(activityID)
            self.Events.evtFilingAudioFinish:Invoke(seqIdx, bIsEnd)
        else
            return
        end
    end

    local Req = pb.CSActivityArchiveAudioFinishReq:New()

    Req.activity_id = activityID
    Req.task_line = timeline
    Req.sequence_order = seqIdx
    Req:Request(OnCSActivityArchiveAudioFinishReq)
end

--- 提交信物
function ActivityServer:SendFilingSubmitPropsReq(activityID, taskId, prop)
    local function OnCSActivityArchiveFirstOpenReq(res)
        if res.result == 0 then
            local activityInfo = res.actv_info
            self.AllActivityInfos[activityInfo.actv_id] = activityInfo
            self.Events.evtActivityUpdateRedDot:Invoke({activityInfo})
            self:UpdateServerActivityInfoList({res.actv_info})
            self:UpdateLocalActivityCurrency({res.actv_info})
            self.Events.evtActivityTaskChange:Invoke(activityID)
            self.Events.evtActivityFilingSubmitProps:Invoke(taskId)
        else
            return
        end
    end

    local Req = pb.CSActivityCollectionSubmitPropsReq:New()

    Req.activity_id = activityID
    Req.task_id = taskId
    Req.props = {}
    table.insert(Req.props, prop)
    Req:Request(OnCSActivityArchiveFirstOpenReq)
end

-------------------------------签到活动区-------------------------------------------
--主动领奖
function ActivityServer:SendSignAwardReq(activityID, dayIndex, callback, optType)
    optType = optType or 0
    local OnSignRes = function(res)
        if res.result == 0 and res.activity_info then
            self:UpdateServerActivityInfoList({res.activity_info}, res.currency_infos)
            self:UpdateLocalActivityCurrency({res.activity_info})
            self.Events.evtActivityUpdateSign:Invoke()
            self.Events.evtSignResArrive:Invoke(res.data_change, res.expand_info)
            loginfo("[ActivityServer] SendSignAwardReq successful.")
            if callback then callback(res) end
        else
            logPb(string.format("SendSignAwardReq failed. res.result: %s activityID: %s, dayIndex: %s", res.result, activityID, dayIndex))
            return
        end
    end
    local req = pb.CSActivityRecvAttendAwardReq:New()
    req.activity_id = activityID
    req.day_number = dayIndex
    req.type = optType
    self.Events.evtEnableRewardNTFCall:Invoke("Hero", false)
    req:Request(OnSignRes)
end

--自动签到
function ActivityServer:SendSignReq(activityID)
    local OnSignRes = function(res)
        if res.result == 0 and res.activity_info then
            self:UpdateServerActivityInfoList({res.activity_info})
            self:UpdateLocalCurrenNumByCycle({res.activity_info})
            self.Events.evtActivityUpdateSign:Invoke()
            self.Events.evtSignResArrive:Invoke(res.data_change, res.expand_info)
            loginfo("[ActivityServer] SendSignReq successful.")
        else
            logPb(string.format("SendSignReq failed. res.result: %s activityID: %s", res.result, activityID))
            return
        end
    end
    local req = pb.CSActivityAttendReq:New()
    req.activity_id = activityID
    self.Events.evtEnableRewardNTFCall:Invoke("Hero", false)
    req:Request(OnSignRes)
end

-------------------------------大战场巅峰赛活动-----------------------------------------
function ActivityServer:_PullMPCommanderRank()
    local activityInfos = self:GetActivityInfosByType(ActivityType.ActivityTypeMPCommander)
    if not activityInfos or next(activityInfos) == nil then return end
    
    self.CommanderRankInfos = {}
    self.CommanderSelfRankNo = nil
    
    for _, activityInfo in ipairs(activityInfos) do
        if activityInfo then
            local rankType = activityInfo.mp_commander_info and activityInfo.mp_commander_info.rank_type
            self:_FetchCommanderRankPages(1, rankType)
            break
        end
    end
end

function ActivityServer:_FetchCommanderRankPages(currentPage, rankType)
    local OnCSRankGetListRes = function(res)
        if res.result ~= 0 then
            logPb(string.format("_FetchCommanderRankPages failed. res.result: %s", res.result))
            return
        end

        if res.self_rank_no then
            self.CommanderSelfRankNo = res.self_rank_no
        end

        if res.list and #res.list > 0 then
            for _, item in ipairs(res.list) do
                table.insert(self.CommanderRankInfos, item)
            end
        end

        if res.page_max and currentPage < res.page_max - 1 then
            self:_FetchCommanderRankPages(currentPage + 1, rankType)
        else
            -- 最终完成时可触发事件通知
        end
    end

    local req = pb.CSRankGetListReq:New()
    req.rank_data_type = rankType or RankDataType.ACTIVITY_SCORE
    req.page_num = currentPage
    req.page_size = 100
    
    req:Request(OnCSRankGetListRes, {bEnableHighFrequency = true, bNeedResendAfterReconnected = true})
end

---@return table
function ActivityServer:GetRankInfos()
    return self.CommanderRankInfos
end

---@return number|nil
function ActivityServer:GetSelfRankNo()
    return self.CommanderSelfRankNo
end

-------------------------------SBC电台活动---------------------------------------------
function ActivityServer:SendActivitySBCAdjustReq(activityID, newsID, bAuto)
    local function OnCSActivitySBCAdjustRes(res)
        if res.result == 0 then
            self:UpdateServerActivityInfoList({res.activity_info})
            self.Events.evtActivityRadioAdjustChange:Invoke(activityID)
        else
            return
        end
    end

    local Req = pb.CSActivitySBCAdjustReq:New()
    Req.activity_id = activityID
    Req.news_id = newsID
    Req.is_auto = bAuto
    Req:Request(OnCSActivitySBCAdjustRes, {bEnableHighFrequency = true})
end

function ActivityServer:SendActivitySBCChangeRewardReq(activityID, rewardIDs)
    local function OnCSActivitySBCChangeRewardRes(res)
        if res.result == 0 then
            -- self:UpdateServerActivityInfoList({res.activity_info}) --无需更新？
            self.Events.evtActivityPreselectionUpdate:Invoke(activityID)
        else
            return
        end
    end

    local Req = pb.CSActivitySBCChangeRewardReq:New()
    Req.activity_id = activityID
    Req.reward_ids = rewardIDs
    Req:Request(OnCSActivitySBCChangeRewardRes, {bEnableHighFrequency = true})
end

-------------------------------星火主题充能区-------------------------------------------
function ActivityServer:SendActivityStarFireChargeTowerReq(activityID, taskID)
    local function OnCSActivityStarFireChargeTowerRes(res)
        if res.result == 0 then
            self:UpdateServerActivityInfoList({res.activity_info})
            self.Events.evtActivityTaskChange:Invoke(activityID)
        else
            return
        end
    end

    local Req = pb.CSActivityStarFireChargeTowerReq:New()
    Req.activity_id = activityID
    Req.task_id = taskID
    Req:Request(OnCSActivityStarFireChargeTowerRes)
end

-------------------------------主题密码兑换区-------------------------------------------
function ActivityServer:GetActivityPasswordBoxes(activityID)
    local activityInfo = self.AllActivityInfos[activityID]
    if activityInfo ~= nil then
        return activityInfo.password_boxes
    end

    return nil
end

function ActivityServer:SendActivityExchangePasswordReq(index, activityID, boxID, digitIndex)
    local function OnCSActivityExchangeReq(res)
        if res.result == 0 then
            self:UpdateServerActivityInfoList({res.actv_info})
            self.Events.evtActivityExchangeReward:Invoke(res.data_change, res.expand_info)
            self.Events.evtActivityExchangeChange:Invoke(index, activityID, boxID, digitIndex)
        else
            return
        end
    end

    local Req = pb.CSActivityExchangePasswordReq:New()
    Req.actv_id = activityID
    Req.box_id = boxID
    Req.digit_idx = digitIndex
    self.Events.evtEnableRewardNTFCall:Invoke("Hero", false)
    Req:Request(OnCSActivityExchangeReq)
end

-------------------------------兑换商城活动区-------------------------------------------
--兑换商城返回可兑物品表
function ActivityServer:GetActivityExchangeItem(activityID)
    local activityInfo = self.AllActivityInfos[activityID]
    if activityInfo ~= nil then
        return activityInfo.exchange_item
    end

    return nil
end

--常规兑换
function ActivityServer:SendActivityExchangeReq(activityID, exchangeID)
    local function OnCSActivityExchangeRes(res)
        if res.result == 0 then
            self:UpdateLocalActivityCurrency(res.actv_infos)
            self:UpdateServerActivityInfoList(res.actv_infos, res.currency_infos)
            self.Events.evtActivityExchangeChange:Invoke(activityID, exchangeID)
            self.Events.evtActivityExchangeReward:Invoke(res.data_change, res.expand_info)
        else
            logPb(string.format("SendActivityExchangeReq failed. res.result: %s activityID: %s exchangeID: %s", res.result, activityID, exchangeID))
            return
        end
    end

    local Req = pb.CSActivityExchangeReq:New()
    Req.exchanges = {}
    local exchanges = pb.CSActivityExchange:New()
    exchanges.actv_id = activityID
    exchanges.exchange_id = {exchangeID}
    table.insert(Req.exchanges, exchanges)
    self.Events.evtEnableRewardNTFCall:Invoke("Hero", false)
    Req:Request(OnCSActivityExchangeRes)
end

--兑换使用权(MP)
function ActivityServer:SendActivityMPExchangeReq(activityID, exchangeID)
    local function OnCSActivityExchangeRes(res)
        if res.result == 0 then
            self:UpdateLocalActivityCurrency(res.actv_infos)
            self:UpdateServerActivityInfoList(res.actv_infos, res.currency_infos)
            self.Events.evtActivityExchangeChange:Invoke(activityID, exchangeID)
            self.Events.evtActivityMPExchangeReward:Invoke(activityID, res.data_change, res.expand_info)
        else
            logPb(string.format("SendActivityMPExchangeReq failed. res.result: %s activityID: %s exchangeID: %s", res.result, activityID, exchangeID))
            return
        end
    end

    local Req = pb.CSActivityExchangeReq:New()
    Req.exchanges = {}
    local exchanges = pb.CSActivityExchange:New()
    exchanges.actv_id = activityID
    exchanges.exchange_id = {exchangeID}
    table.insert(Req.exchanges, exchanges)
    self.Events.evtEnableRewardNTFCall:Invoke("Hero", false)
    Req:Request(OnCSActivityExchangeRes)
end

--以物易物兑换(SOL)
function ActivityServer:SendActivityPropExchangeReq(activityID, exchangeID)
    local function OnCSActivityExchangeRes(res)
        if res.result == 0 then
            self:UpdateLocalActivityCurrency(res.activity_infos)
            self:UpdateServerActivityInfoList(res.activity_infos, res.currency_infos)
            self.Events.evtActivityExchangeChange:Invoke(activityID, exchangeID)
            self.Events.evtActivitySOLExchangeReward:Invoke(activityID, res.data_change, res.expand_info)
        else
            logPb(string.format("SendActivityPropExchangeReq failed. res.result: %s activityID: %s exchangeID: %s", res.result, activityID, exchangeID))
            return
        end
    end

    local Req = pb.CSActivityPropExchangeReq:New()
    Req.activity_id = activityID or 0
    Req.exchange_id = exchangeID or 0
    self.Events.evtEnableRewardNTFCall:Invoke("Hero", false)
    Req:Request(OnCSActivityExchangeRes)
end

--消耗代币解锁兑换协议，目前用于以物易物兑换
function ActivityServer:SendActivityUnlockExchangeReq(activityID, exchangeID)
    local function OnCSActivityExchangeRes(res)
        if res.result == 0 then
            self:UpdateLocalActivityCurrency({res.activity_info})
            self:UpdateServerActivityInfoList({res.activity_info}, res.currency_infos)
            self.Events.evtActivityUnlockExchangeChange:Invoke(activityID, exchangeID)
        else
            logPb(string.format("SendActivityUnlockExchangeReq failed. res.result: %s activityID: %s exchangeID: %s", res.result, activityID, exchangeID))
            return
        end
    end

    local Req = pb.CSActivityUnlockExchangeReq:New()
    Req.activity_id = activityID or 0
    Req.exchange_id = exchangeID or 0
    Req:Request(OnCSActivityExchangeRes)
end

--脑机物品兑换协议
function ActivityServer:SendRelinkExchangeReq(activityID)
    local function OnExchangeRes(res)
        if res.result == 0 and res.activity_infos then
            self:UpdateServerActivityInfoList(res.activity_infos)
            self:UpdateLocalActivityCurrency(res.activity_infos)
            self.Events.evtActivityExchangeChange:Invoke(activityID)
            self.Events.evtActivityExchangeReward:Invoke(res.data_change, res.expand_info)
        end
    end
    local Req = pb.CSActivityRelinkExchangeReq:New()
    Req.activity_id = activityID or 0
    Req:Request(OnExchangeRes)
end

-- 请求解密
function ActivityServer:SendActivityStarDecryptReq(activityID)
    local function OnActivityStarDecryptRes(res)
        self:UpdateServerActivityInfoList({res.actv_info})
        self:UpdateLocalActivityCurrency({res.actv_info})
        self.Events.evtActivityStarDecrypt:Invoke(res, activityID)
    end
    local Req = pb.CSActivityStarDecryptReq:New()
    Req.act_id = activityID or 0
    Req:Request(OnActivityStarDecryptRes)
end

----------------------------角色调查活动区-------------------------------
-- 回答问题
function ActivityServer:SendActivityAnswerReq(activityID, iTaskID, iGoalID, iAnswer)
    local function OnActivityAnswerRes(res)
        if res.result == 0 and res.activity_info then
            self:UpdateServerActivityInfoList({res.activity_info})
            self:UpdateLocalActivityCurrency({res.activity_info})
            self.Events.evtActivityAnswerRes:Invoke(res, activityID, iTaskID, iGoalID, iAnswer)
        end
    end
    local Req = pb.CSActivityAnswerReq:New()
    Req.activity_id = activityID or 0
    Req.task_id = iTaskID or 0
    Req.goal_id = iGoalID or 0
    Req.answer = iAnswer or 0
    Req:Request(OnActivityAnswerRes)
end

-- 触发
function ActivityServer:SendActivityTriggerReq_RoleSurvey(activityID)
    local function OnActivityTriggerRes(res)
        if res.result == 0 and res.activity_info then
            self:UpdateServerActivityInfoList({res.activity_info})
            self:UpdateLocalActivityCurrency({res.activity_info})
            self.Events.evtActivityTriggerRes:Invoke(activityID, res)
        end
    end
    local Req = pb.CSActivityTriggerReq:New()
    Req.activity_id = activityID or 0
    Req:Request(OnActivityTriggerRes)
end

function ActivityServer:SendActivityTriggerReq(activityID)
    local function OnActivityTriggerRes(res)
        if res.result == 0 and res.activity_info then
            self:UpdateServerActivityInfoList({res.activity_info})
        end
    end
    local Req = pb.CSActivityTriggerReq:New()
    Req.activity_id = activityID or 0
    Req:Request(OnActivityTriggerRes)
end

-------------------------------跟踪任务区-------------------------------------------
function ActivityServer:UnTrackInfo(activityID, trackactivityID, trackSign)
    local function OnCSActivityUnTrackReq(res)
        if res.result == 0 then
            for idx, activityInfo in ipairs(res.actv_infos) do
                self.AllActivityInfos[activityInfo.actv_id].is_tracking = activityInfo.is_tracking
                if trackSign then
                    self:TrackInfo(trackactivityID)
                end
            end
            self.Events.evtActivityTrackTaskChange:Invoke(activityID)
        else
            logPb(string.format("UnTrackInfo failed. res.result: %s activityID: %s trackactivityID: %s", res.result, activityID, trackactivityID))
            return
        end
    end

    local Req = pb.CSActivityUntrackReq:New()
    Req.actv_id = {activityID}
    Req:Request(OnCSActivityUnTrackReq)
end

function ActivityServer:TrackInfo(activityID)
    local function OnCSActivityTrackReq(res)
        if res.result == 0 then
            for idx, activityInfo in ipairs(res.actv_infos) do
                self.AllActivityInfos[activityInfo.actv_id].is_tracking = activityInfo.is_tracking
            end
            self.Events.evtActivityTrackTaskChange:Invoke(activityID)
        else
            logPb(string.format("TrackInfo failed. res.result: %s activityID: %s", res.result, activityID))
            return
        end
    end

    local Req = pb.CSActivityTrackReq:New()
    Req.actv_id = {activityID}
    Req:Request(OnCSActivityTrackReq)
end


--comment: 活动尽量读服务器表,方便热更
------------------------------获取服务器表信息------------------------------
--- 根据活动id返回活动名
function ActivityServer:GetActivityName(activityID)
    local activityInfo = self.AllActivityInfos[activityID]
    if activityInfo then
        return activityInfo.name
    end

    return nil
end

--- 根据活动id返回活动描述
function ActivityServer:GetActivityDesc(activityID)
    local activityInfo = self.AllActivityInfos[activityID]
    if activityInfo then
        return activityInfo.desc
    end

    return nil
end

--- 返回活动最大跟踪数量
function ActivityServer:GetActivityMaxTracking()
    return self.max_tracking_num
end

--- 根据活动id返回挑战类型表
function ActivityServer:GetActivityNewbieList(activityID)
    local activityInfo = self.AllActivityInfos[activityID]
    if activityInfo then
        return activityInfo.classify_config
    end

    return nil
end

---comment 根据活动ID返回新兵挑战最终奖励
---@param activityID any
---@return unknown
function ActivityServer:GetNewbieInfoByActivityID(activityID)
    local activityInfo = self.AllActivityInfos[activityID]
    if activityInfo ~= nil then
        return activityInfo.newbie_challenge_info
    end

    return nil
end

--------------------------------------------------------------------------
--- 活动表[deprecated=true]
--------------------------------------------------------------------------

--- 初始化活动配置
function ActivityServer:InitActivityTable()
    for activityID, activityRow in pairs(self.Tables.ActivityTable) do
        local activityStruct = ActivityTableStruct:New(activityRow)
        self.AllActivityConfig[activityID] = activityStruct
    end
end

--- 根据活动ID返回活动配置
---@param activityID any
---@return unknown
function ActivityServer:GetActivityConfigByActivityID(activityID)
    return self.AllActivityConfig[tostring(activityID)]
end

--------------------------------------------------------------------------
--- 任务表[deprecated=true]
--------------------------------------------------------------------------

--- 初始化任务配置
function ActivityServer:InitActivityTaskTable()
    for taskID, activityTaskRow in pairs(self.Tables.ActivityTaskTable) do
        local activityTaskStruct = ActivityTableTaskStruct:New(activityTaskRow)
        self.AllTaskConfig[taskID] = activityTaskStruct
    end
end

--- 根据任务ID返回任务配置
---@param taskID any
---@return unknown
function ActivityServer:GetTaskConfigByTaskID(taskID)
    return self.AllTaskConfig[tostring(taskID)]
end

--------------------------------------------------------------------------
--- 常量表[deprecated=true]
--------------------------------------------------------------------------

--- 初始化常量配置
function ActivityServer:InitActivityConstTable()
    for activityConstId, activityConstRow in pairs(self.Tables.ActivityConstTable) do
        local activityStruct = ActivityConstStruct:New(activityConstRow)
        self.AllConstConfig[activityConstId] = activityStruct
    end
end


function ActivityServer:InitNewbieChallengeTable()
    for activityConstId, activityConstRow in pairs(self.Tables.NewbieChallengeTable) do
        local activityStruct = ActivityChallengeStruct:New(activityConstRow)
        self.AllNewbieChallengeConfig[activityConstId] = activityStruct
    end
end


function ActivityServer:InitActivityTaskGoalsTable()
    for activityConstId, activityConstRow in pairs(self.Tables.ActivityTaskGoalsTable) do
        local activityStruct = ActivityTaskGoalsStruct:New(activityConstRow)
        self.AllActivityTaskGoalsConfig[activityConstId] = activityStruct
    end
end

function ActivityServer:InitActivityThemeEventTechTable()
    for Id, Row in pairs(self.Tables.ActivityThemeEventTech) do
        local activityStruct = ActivityThemeEventTechStruct:New(Row)
        self.AllActivityThemeEventTechConfig[Id] = activityStruct
    end
end

function ActivityServer:InitActivityQuestionTable()
    for Id, Row in pairs(self.Tables.ActivityQuestion) do
        local activityStruct = ActivityQuestionStruct:New(Row)
        self.ActivityQuestionConfig[Row.GoalID] = activityStruct
    end
end

function ActivityServer:InitTeachingManualTable()
    for Id, Row in pairs(self.Tables.ActivityTeachingManual) do
        local activityStruct = ActivityTeachingManualStruct:New(Row)
        self.ActivityTeachingManualConfig[Row.ID] = activityStruct
    end
end

function ActivityServer:InitLocalCustomTable()
    self._sortShieldList = {
        [1] = ActivityType.ActivityTypeOrderedTemplate,
        [2] = ActivityType.ActivityTypeKillProducer,
        [3] = ActivityType.ActivityTypeRecruitNewMilestone,
        [4] = ActivityType.ActivityTypeRecruitTraining,
        [5] = ActivityType.ActivityTypeStarFire,
        [6] = ActivityType.ActivityTypeSingleTaskTemplate,
        [7] = ActivityType.ActivityTypeSurveyBoss,
        [8] = ActivityType.ActivityTypeThemeActStar,
    }
    
    self._modeLearningList = {
        [1] = {
            EGameFlowStageType.SafeHouse,
            EGameFlowStageType.ModeHallToSafeHouse,
            EGameFlowStageType.GameToSafeHouse,
        },
        [2] = {
            EGameFlowStageType.Lobby,
        }
    }
end

--- 根据常量ID返回常量配置
---@param constID any
---@return unknown
function ActivityServer:GetActivityConstValue(constID)
    return self.AllConstConfig[tostring(constID)]
end

function ActivityServer:GetActivityTaskTypeValue()
    return self.AllNewbieChallengeConfig
end

function ActivityServer:GetActivityTaskGoalsValue(constID)
    return self.AllActivityTaskGoalsConfig[tostring(constID)]
end

----------------------------------------------------------------          
--- 过滤操作
----------------------------------------------------------------
--- 检查当前所处模式          
---@return number param: 1-sol，2-mp, 0-otherwise
function ActivityServer:CheckIsInMode()
    local gameFlowType = Facade.GameFlowManager:GetCurrentGameFlow()
    for modeType, modeValues in pairs(self._modeLearningList) do
        for _, value in ipairs(modeValues) do
            if value == gameFlowType then
                return modeType
            end
        end
    end

    return 0
end

--- 过滤旧数据ActivityInfosByGroup
function ActivityServer:RefreshActivityInfosByGroup()
    local removeTempIds = {}
    for key, activityInfos in ipairs(self.ActivityInfosByGroup) do
        local removeTempInfos = {}
        for key, activity in ipairs(activityInfos) do
            if Facade.ClockManager:GetLocalTimestamp() > activity.end_date then
                table.insert(removeTempInfos, key)
            end
        end
        for i = #removeTempInfos, 1, -1 do
            table.remove(activityInfos, removeTempInfos[i])
        end
        if next(activityInfos) == nil then
            table.insert(removeTempIds, key)
        end
    end
    for i = #removeTempIds, 1, -1 do
        table.remove(self.ActivityInfosByGroup, removeTempIds[i])
    end
end

--- 根据模式倾向性过滤活动数据（除了每日活动）
function ActivityServer:FilterActivityInfosByModeLearning(res)
    if res ~= nil and res.result == 0 and res.actv_infos ~= nil then
        if self:CheckIsInMode() == EActGameMode.SOL then
            for i = #res.actv_infos, 1, -1 do
                if res.actv_infos[i].mode_leaning == EActGameMode.MP then
                    if res.actv_infos[i].actv_type ~= ActivityType.ActivityTypeDaily then
                        table.remove(res.actv_infos, i)
                    end
                    --其它mode_leaning(非1，2)同ALL（0）处理，防止策划配错表，下同，待改
                end
            end
        elseif self:CheckIsInMode() == EActGameMode.MP then
            for i = #res.actv_infos, 1, -1 do
                if res.actv_infos[i].mode_leaning == EActGameMode.SOL then
                    if res.actv_infos[i].actv_type ~= ActivityType.ActivityTypeDaily then
                        table.remove(res.actv_infos, i)
                    end
                end
            end
        end
    end
end

--- 根据模式倾向性过滤首页数据
function ActivityServer:FilterBannerInfosByModeConfig(res)
    if res ~= nil and res.result == 0 and res.banners ~= nil then
        if self:CheckIsInMode() == EActGameMode.SOL then
            for i = #res.banners, 1, -1 do
                if res.banners[i].mode_visibility and res.banners[i].mode_visibility == EActGameMode.MP then
                    table.remove(res.banners, i)
                    --其它mode_visibility(非1，2)同ALL（0）处理，防止策划配错表，下同，待改
                end
            end
        elseif self:CheckIsInMode() == EActGameMode.MP then
            for i = #res.banners, 1, -1 do
                if res.banners[i].mode_visibility and res.banners[i].mode_visibility == EActGameMode.SOL then
                    table.remove(res.banners, i)
                end
            end
        end
    end
end

--- 判断是否启用过滤
---@param bActiveGlobal boolean 外部是否启用，默认只在活动内部生效
function ActivityServer:CheckFilterStatus(bActiveGlobal)
    --Server逻辑不应该关心打开了什么界面
    --筛选已经统一放到表现层
    return false
end

--// comment：深拷贝不能高频调用，否则会导致性能问题
--- 根据模式标签获取过滤活动数据
function ActivityServer:GetFilterActByModeTag()
    local actList = {}
    deepcopy(actList, self.AllActivityInfos)
    for key, value in pairs(actList) do
        if value.mode_tag == self:CheckIsInMode() then
            actList[key] = nil
            break
        end
    end
    return actList
end

function ActivityServer:GetFilterAct2ByModeTag()
    local actListByGroup = {}
    deepcopy(actListByGroup, self.ActivityInfosByGroup)
    for groupID, actList in pairs(actListByGroup) do
        for key, value in pairs(actList) do
            if value.mode_tag == self:CheckIsInMode() then
                actListByGroup[groupID][key] = nil
                break
            end
        end
    end
    return actListByGroup
end

--- 根据模式标签获取过滤首页数据
function ActivityServer:GetFilterBannerByModeTag()
    local bannerListByID = {}
    deepcopy(bannerListByID, self.BannerFilterInfosByID)
    for id, bannerList in pairs(bannerListByID) do
        for key, value in pairs(bannerList) do
            if value.mode_tag == self:CheckIsInMode() then
                bannerListByID[id][key] = nil
                break
            end
        end
    end
    return bannerListByID
end
-- //end

--------------------------------------------------------------------------
--- 页签红点判断
--------------------------------------------------------------------------

--- 红点判断方法 只处理活动内容
function ActivityServer:GetReddotState(activityID)
    local function handleTask(activityID)
        return self:GetReddotStateByTask(activityID)
    end

    local function handleTaskAndExchange(activityID, activityInfo)
        return self:GetReddotStateByTaskAndExchange(activityID, activityInfo)
    end

    local function handleWeaponDelivery(activityID, activityInfo)
        if self:GetReddotStateByTaskAndExchange(activityID, activityInfo) then
            return true
        end

        return self:GetReddotStateByMPWeaponApply(activityInfo)
    end

    local function handleMilestone(activityID, activityInfo)
        if self:GetReddotStateByTask(activityID) then
            return true
        end

        return self:GetReddotStateByMilestone(activityID, activityInfo.currency_num)
    end

    local function handleFinalAward(activityID, activityInfo)
        return self:GetReddotStateByFinalAward(activityID, activityInfo.final_award, activityInfo.final_award_received)
    end

    local function handleRecruitTraining(activityID)
        if self:GetReddotStateByTask(activityID) then
            return true
        end

        return self:GetReddotStateByNovFinalAward(activityID)
    end

    local function handleAttend(activityID, activityInfo)
        if activityInfo.attend_info then
            local attendInfo = activityInfo.attend_info
            for _, value in ipairs(attendInfo.states) do
                if value.state == 1 then
                    return true
                end
            end
        end

        return false
    end

    local function handlePasswordTopic(activityID, activityInfo)
        if self:GetReddotStateByTask(activityID) then
            return true
        end

        local passwordList = activityInfo.password_boxes
        if passwordList then
            local unlockPassboxNum = 0
            for _, passwordBox in ipairs(passwordList) do
                if passwordBox.unlocked == true then
                    unlockPassboxNum = unlockPassboxNum + 1
                end
            end
            return self:GetReddotStateByMilestone(activityID, unlockPassboxNum)
        end

        return false
    end

    local function handleConstructMoss(activityID, activityInfo)
        if self:GetReddotStateByTask(activityID) then
            return true
        elseif self:GetReddotStateByMilestone(activityID, activityInfo.currency_num) then
            return true
        else
            if activityInfo.moss_construct_info and activityInfo.moss_construct_info.calculate_info then
                local data = activityInfo.moss_construct_info.calculate_info
                if data.last_hardware_value < data.current_hardware_value or data.last_software_value < data.current_software_value then
                    return true
                end
            end
        end

        local max = 0
        if activityInfo.moss_construct_info and activityInfo.moss_construct_info.calculate_info then
            if activityInfo.moss_construct_info.calculate_info.max_power then
                max = activityInfo.moss_construct_info.calculate_info.max_power
            end
        end
        for index, value in ipairs(self:GetMilestonesReward(activityID) or {}) do
            if value.prop and value.prop[1] and value.prop[1].received == false then
                if value.currency_num <= max then
                    return true
                end
            end
        end
        
        return false
    end

    local function handleFiling(activityID, activityInfo)
        if self:GetReddotStateByFiling(activityInfo) then
            return true
        end

        return false
    end

    local function handleStarFire(activityID, activityInfo)
        if self:GetReddotStateByStarFire(activityInfo) then
            return true
        end

        return false
    end

    -- 兑换模板红点
    local function handleCollectionTemplate(activityID, activityInfo)
        -- for index, task in ipairs(activityInfo.task_info or {}) do
        --     -- local isExchange = true
        --     -- for key, prop in ipairs(task.required_props or {}) do
        --     --     local allItems = Server.InventoryServer:GetItemsById(prop.id)
        --     --     local num = 0
        --     --     for _, item in ipairs(allItems or {}) do
        --     --         num = num + item.num
        --     --     end
        --     --     if prop.num > num then
        --     --         isExchange = false
        --     --         break
        --     --     end
        --     -- end
        --     if task.state == ActivityTaskState.ActivityTaskStateCompleted then
        --         return true
        --     -- elseif task.state ~= ActivityTaskState.ActivityTaskStateRewarded then
        --     --     if isExchange then
        --     --         return true
        --     --     end
        --     end
        -- end

        return false
    end

    -- 脑机界面红点
    local function handleRelink(activityID, activityInfo)
        --任务红点
        for index, task in ipairs(activityInfo.task_info or {}) do
            if task.state == ActivityTaskState.ActivityTaskStateCompleted then
                return true
            end
        end
        --里程碑
        local max = 0
        if activityInfo.relink_info and activityInfo.relink_info.total_use_time then
            max = activityInfo.relink_info.total_use_time
        end
        for index, value in ipairs(activityInfo.milestone_award or {}) do
            if value.prop and value.prop[1] and value.prop[1].received == false then
                if value.currency_num <= max then
                    return true
                end
            end
        end

        return false
    end

    -- 角色调查红点
    local function handleRoleSurvey(activityID, activityInfo)
        if self:IsRoleSurveyClueStageRedDot(activityInfo) then
            return true
        end

        if self:IsRoleSurveyInfoTaskStageRedDot(activityInfo) then
            return true
        end

        if self:IsRoleSurveyInfoTaskAllRedDot(activityInfo) then
            return true
        end

        if self:IsRoleSurveyBossReward(activityInfo) then
            return true
        end

        if self:IsRoleSurveyExchangeRedDot(activityInfo) then
            return true
        end
        
        return false
    end

    -- 繁星红点
    local function handleStars(activityID, activityInfo)
        return self:IsStarsRedDot(activityInfo) 
    end

    -- 调酒师红点
    local function handleMakeDrink(activityID, activityInfo)
        for index, task in ipairs(activityInfo.task_info or {}) do
            if task.task_line == MakeDrinkTaskLine.MakeDrinkTaskLineNormal then
                if task.state == ActivityTaskState.ActivityTaskStateCompleted then
                    return true
                end
            end
        end

        if activityInfo.make_drink_info then
            local count = activityInfo.make_drink_info.total_make_time or 0
            for index, value in ipairs(activityInfo.milestone_award or {}) do
                if value.currency_num and value.currency_num <= count then
                    if value.prop and value.prop[1] and value.prop[1].received == false then
                        return true
                    end
                end
            end
        end

        return false
    end
    
    -- 新年活动
    local function handleSheOneCard(activityID, activityInfo)
        local cards = self:GetCards(activityID)
        -- 任务
        for index, task in ipairs(activityInfo.task_info or {}) do
            if task.state == ActivityTaskState.ActivityTaskStateCompleted then
                return true
            end
        end
        -- 集卡
        for index, card in ipairs(cards or {}) do
            if card then
                local isBool  = self:GetUserBoolean("ACT_KEY_SPRING"..card.id, true)
                if isBool and card.num > 0 then
                    return true
                end
            end
        end
        -- 兑换
        for index, reward in ipairs(self:GetNewYearCard(activityID, EActSubInteractive.Next6) or {}) do
            if reward.tag == 1 then
                local isBool = true
                for _, card in ipairs(cards or {}) do
                    if card.type == 3 and card.num <= 0 then
                        isBool = false
                        break
                    end
                end
                if reward.buy_count < reward.buy_limit and isBool then
                    return true
                end
            elseif reward.tag == 2 then
                local isBool = true
                for _, cost in ipairs(reward.costs or {}) do
                    for _, card in ipairs(cards or {}) do
                        if card.id == cost.id then
                            if card.num < cost.num then
                                isBool = false
                            end
                            break
                        end
                    end
                end
                local _bool  = self:GetUserBoolean("ACT_KEY_SPRING"..reward.reward_item, true)
                if isBool and _bool and reward.buy_count < reward.buy_limit then
                    return true
                end
            end
        end
        -- 是否可拆包红点
        if self:IsReddotCardHolder(activityID) then
            return true
        end
        
        return false
    end

    -- 万能兑换模板(货币/道具)
    local function handleTemplate1(activityID, activityInfo)
        local IsExpireFunc = function(time)
            local curTime = TimeUtil.GetCurrentTime()
            if time and type(time) == "number" then
                return time < curTime
            else
                return true
            end
        end

        local IsRemainingFunc = function(min, max)
            if min and max and type(max) == "number" and type(min) == "number" then
                return max - min > 0
            else
                return false
            end
        end

        for index, item in ipairs(activityInfo.prop_exchange_items or {}) do
            -- 是否收藏
            if item.is_focus then
                -- 刷新时间判断
                if IsExpireFunc(item.unlock_time) then
                    if IsRemainingFunc(item.exchanged_count, item.exchange_count_max) then
                        -- 判断货币是否满足
                        for _, plan in ipairs(item.plans or {}) do
                            for _, prop in ipairs(plan.prop or {}) do
                                -- 判断红点
                                if self:IsCanExchange(prop.id, prop.num) then
                                    return true
                                end
                                break
                            end
                            break
                        end
                    end
                end
            end
        end

        return false
    end
    
    local function handleTemplate2(activityID, activityInfo)
        local IsExpireFunc = function(time)
            local curTime = TimeUtil.GetCurrentTime()
            if time and type(time) == "number" then
                return time < curTime
            else
                return true
            end
        end

        local IsRemainingFunc = function(min, max)
            if min and max and type(max) == "number" and type(min) == "number" then
                return max - min > 0
            else
                return false
            end
        end

        for index, item in ipairs(activityInfo.prop_exchange_items or {}) do
            --是否收藏
            if item.is_focus then
                --刷新时间判断
                if IsExpireFunc(item.unlock_time) then
                    if IsRemainingFunc(item.exchanged_count, item.exchange_count_max) then
                        --判断货币是否满足(多套,满足一套都可以)
                        for _, plan in ipairs(item.plans or {}) do
                            --判断红点
                            if self:IsCanExchange(plan.prop, 1) then
                                return true
                            end
                        end
                    end
                end
            end
        end

        return false
    end

    -- 电台活动
    local function handleRadio(activityID, activityInfo)
        -- if self:GetReddotStateByTask(activityID) then
        --     return true
        -- end
        -- return self:GetReddotStateByAdjustTimes(activityID, activityInfo)

        return false
    end

    -- 潘多拉活动
    local function handlePandoraSubUI(activityID, activityInfo)
        local appID = activityInfo.pandora_info and activityInfo.pandora_info.app_id
        if appID then
            return Server.GameletServer:IsAppIDRedDot(appID)
        end

        return false
    end

    -- 简易里程碑
    local function handleSimple(activityID, activityInfo)
        if activityInfo.simple_milestone_info then
            local count = activityInfo.simple_milestone_info.current_score

            for index, value in ipairs(activityInfo.milestone_award or {}) do
                if count then
                    if value.currency_num and value.currency_num <= count then
                        if value.prop and value.prop[1] and value.prop[1].received == false then
                            return true
                        end
                    end
                end
            end
        end

        return false
    end

    -- QQ/微信订阅
    local function handleSubscription(activityID, activityInfo)
        if activityInfo.info3 then
            local isSubscribe = self:ReddotSubscribe(activityInfo.info3)
            for index, task in ipairs(activityInfo.task_info or {}) do
                if task.state ~= ActivityTaskState.ActivityTaskStateRewarded and isSubscribe then
                    return true
                end
            end
        end
        return false
    end
    

    --方舟商城活动
    local function handleFangzhouStore(activityID, activityInfo)
        if activityInfo.arknights_exchange_info then
            local times   = {}
            for index, page in ipairs(activityInfo.arknights_exchange_info.page_info or {}) do
                for _, id in ipairs(page.exchange_ids or {}) do
                    times[id] = page.unlock_time
                end
            end
            for _, reward in ipairs(activityInfo.prop_exchange_items or {}) do
                local id  = reward.prop and reward.prop.id
                local num = reward.prop and reward.prop.num
                local currency_id  = 0
                local currency_num = 0
                for _, plan in ipairs(reward.plans or {}) do
                    currency_id = plan.prop and plan.prop[1] and plan.prop[1].id
                    currency_num = plan.prop and plan.prop[1] and plan.prop[1].num
                    break
                end
                local data = {
                    id   = id   or 0, --ID
                    num  = num  or 0, --数量
                    max  = reward.exchange_count_max or 0,
                    min  = reward.exchanged_count or 0,
                    uuid = reward.exchange_id or 0,
                    currencyId  = currency_id or 0,
                    currencyNum = currency_num or 0,
                    refreshTime = reward.next_refresh_time or 0,
                    releaseTime = reward.unlock_time or 0,
                }
                
                local IsTimeExpired = function(time)
                    if type(time) == "number" then
                        return time <= Facade.ClockManager:GetServerTimestamp()
                    else
                        return true
                    end
                end

                local IsHave = function(max, min)
                    if type(max) == "number" and type(min) == "number" then
                        return (max - min) > 0, max - min
                    end
                    return false, 0
                end

                local IsHaved = function(Id)
                    if Server.CollectionServer:IsOwnedWeaponSkin(Id) then
                        return true
                    end
                    for index, head in ipairs(Server.RoleInfoServer:GetAvatarTbl() or {}) do
                        if head.Islock and head.AvatarID == id then
                            return true
                        end
                    end
                    for index, brand in ipairs(Server.RoleInfoServer:GetMilitaryTbl() or {}) do
                        if brand.Islock and brand.AvatarID == id then
                            return true
                        end
                    end
                    for key, hero in pairs(Server.HeroServer:GetHeroData() or {}) do
                        if hero and hero.hero_id then
                            if Server.HeroServer:IsAccessoryUnlocked(tostring(hero.hero_id), id) then
                                return true
                            end
                        end
                    end
                    return false
                end

                local IsCurrency = function(propId)
                    for key, value in pairs(ECurrencyItemId or {}) do
                        if value == propId then
                            return true
                        end
                    end
                    return false
                end

                local GetPropNum = function(propId)
                    if IsCurrency(propId) then
                        --获取货币
                        return Server.CurrencyServer:GetNumByItemId(propId)
                    else
                        --获取仓库道具
                        local num = Server.InventoryServer:GetItemNumById(propId)
                        if num == 0 then
                            --获取藏品道具
                            local itemData = Server.CollectionServer:GetCollectionItemById(propId)
                            if itemData and itemData.num and itemData.num > 0 then
                                return itemData.num
                            end
                            --获取活动代币
                            num = Server.ActivityServer:GetCurrenNum(propId)
                            if num then
                                return num
                            else
                                return 0
                            end
                        else
                            return num
                        end
                    end
                end

                local IsCanExchange = function(propId, propNum)
                    local _num = GetPropNum(propId)
                    if _num then
                        return _num >= propNum
                    else
                        return false
                    end
                end

                local GetExchange = function(data)
                    if data then
                        --可能兑换好几个
                        num = num or 1
                        --未开放
                        if not IsTimeExpired(data.releaseTime) then
                            return 0
                        end
                        --未刷新
                        if not IsTimeExpired(data.refreshTime) then
                            return 1
                        end
                        --已购完
                        if not IsHave(data.max, data.min) then
                            return 2
                        end
                        --是否可兑换
                        if IsCanExchange(data.currencyId, data.currencyNum * num) then
                            return 4
                        else
                            return 3
                        end
                    end
                    return -1
                end
                if data and IsTimeExpired(times[data.uuid]) then
                    data["min"] = IsHaved(data.id) and data.max or data.min
                    if reward.is_focus then
                        local state = GetExchange(data)
                        if state == 4 then
                            return true
                        end
                    end
                end
            end
        end
        return false
    end

    -- 方舟招募活动
    local function handleArkRecruit(activityID, activityInfo)
        if self:GetReddotStateByTask(activityID) then
            return true
        end

        return self:IsArkRecruitRedDot(activityID, activityInfo.arknights_recruit_info)
    end
    --阿萨拉巡旅
    local function handleTara(activityID, activityInfo)
        local info = activityInfo.ahsarah_travel_info
        if info then
            for index, data in ipairs(info.line_infos or {}) do
                local count = 0
                for _, message in ipairs(data.messages or {}) do
                    for _, relay in ipairs(message.relays or {}) do
                        if not relay.received and relay.rewards and #relay.rewards > 0 then
                            return true
                        end
                    end
                    if message.progress and message.progress > count then
                        count = message.progress
                    end
                end
                if not data.received and data.progress and data.progress >= count then
                    return true
                end
            end
        end
        return false
    end

    --战备物流
    local function handleLottery(activityID, activityInfo)
        return false
    end

    local activityType = self:GetActivityTypeByActivityID(activityID)
    local activityInfo = self:GetActivityInfoByActivityID(activityID)

    if activityInfo then
        -- 预告态活动字段不齐，不能读内容，直接返回false
        if activityInfo.isPreview then
            return false
        end

        local handlers = {
            [ActivityType.ActivityTypeExchangeTask] = handleTask,
            [ActivityType.ActivityTypeExchangeTaskV2] = handleTask,
            [ActivityType.ActivityTypeGenericTemplate] = handleTask,
            [ActivityType.ActivityTypeSingleTaskTemplate] = handleTask,
            [ActivityType.ActivityTypeTestingWeaponTemplate] = handleTask,
            [ActivityType.ActivityTypeKillProducer] = handleTask,
            [ActivityType.ActivityTypeMilestoneTask] = handleMilestone,
            [ActivityType.ActivityTypeMilestoneTaskV2] = handleMilestone,
            [ActivityType.ActivityTypeRecruitGrowthMilestone] = handleMilestone,
            [ActivityType.ActivityTypeRecruitNewMilestone] = handleMilestone,
            [ActivityType.ActivityTypeUnlockPasswordBox] = handlePasswordTopic,
            [ActivityType.ActivityTypeDaily] = handleFinalAward,
            [ActivityType.ActivityTypeOrderedTemplate] = handleFinalAward,
            [ActivityType.ActivityTypeRecruitTraining] = handleRecruitTraining,
            [ActivityType.ActivityTypeRecruitAttend] = handleAttend,
            [ActivityType.ActivityTypeAttend7Day] = handleAttend,
            [ActivityType.ActivityTypeAttend] = handleAttend,
            [ActivityType.ActivityTypeConstructMoss] = handleConstructMoss,
            [ActivityType.ActivityTypeArchive] = handleFiling,
            [ActivityType.ActivityTypeStarFire] = handleStarFire,
            [ActivityType.ActivityTypeCollectionTemplate] = handleCollectionTemplate,
            [ActivityType.ActivityTypeRelink] = handleRelink,
            [ActivityType.ActivityTypeSurveyBoss] = handleRoleSurvey,
            [ActivityType.ActivityTypeThemeActStar] = handleStars,
            [ActivityType.ActivityTypeMakeDrink] = handleMakeDrink,
            [ActivityType.ActivityTypeSheOneCard] = handleSheOneCard,
            [ActivityType.ActivityTypeSOLEquipmentDelivery] = handleTaskAndExchange,
            [ActivityType.ActivityTypeSOLComponentDelivery] = handleTaskAndExchange,
            [ActivityType.ActivityTypeMPComponentDelivery] = handleTaskAndExchange,
            [ActivityType.ActivityTypeWeaponDelivery] = handleWeaponDelivery,
            [ActivityType.ActivityTypeNormal] = handleFinalAward,
            [ActivityType.ActivityTypeAttend7Day] = handleAttend,
            [ActivityType.ActivityTypeFlexibleAttend] = handleTask,
            [ActivityType.ActivityTypeMPVehicleDelivery] = handleTask,
            [ActivityType.ActivityTypeMPCommander] = handleTask,
            [ActivityType.ActivityTypeSBC] = handleRadio,
            [ActivityType.ActivityTypePandora] = handlePandoraSubUI,
            [ActivityType.ActivityTypeAnythingExchangeCurrency] = handleTemplate1,
            [ActivityType.ActivityTypeAnythingExchange] = handleTemplate2,
            [ActivityType.ActivityTypeSimpleMilestone] = handleSimple,
            [ActivityType.ActivityTypeSubscription] = handleSubscription,
            [ActivityType.ActivityTypeArknightsExchange] = handleFangzhouStore,
            [ActivityType.ActivityTypeArknightsRecruit] = handleArkRecruit,
            [ActivityType.ActivityTypeAhsarahTravel] = handleTara,
            [ActivityType.ActivityTypeLotteryTemplate] = handleLottery,
        }

        local handler = handlers[activityType]

        if handler then
            local handlerResult = handler(activityID, activityInfo)
            return handlerResult
        end
    end

    return false
end

function ActivityServer:IsActivityBrowsed(activityID)
    local key = "ACTIVITY_HOMEPAGE_BROWSE_" .. activityID
    return Facade.ConfigManager:GetUserBoolean(key, false)
end

function ActivityServer:SetActivityBrowsed(activityID)
    local key = "ACTIVITY_HOMEPAGE_BROWSE_" .. activityID

    Facade.ConfigManager:SetUserBoolean(key, true)
    local activityInfo = self:GetActivityInfoByActivityID(activityID)
    if activityInfo then
        self.Events.evtActivityUpdateRedDot:Invoke({activityInfo})
    end
end

--- 红点判断方法 包括活动内容，完成状态，浏览状态，预告状态
function ActivityServer:GetReddotStateExtraCondition(activityID)
    local activityInfo = self.AllActivityInfos[activityID]
    if not activityInfo then return false end

    local bNewActivity = (not self:IsActivityBrowsed(activityID)) and (activityInfo.listing_tips)
    local bSuppress = (not self:IsActivityStarted(activityID)) or (self:IsActivityCompleted(activityID))
    
    return (self:GetReddotState(activityID) or bNewActivity) and (not bSuppress)
end

--- 通用 [任务] 判断红点方法
function ActivityServer:GetReddotStateByTask(activityID)
    local taskInfoList = self:GetTaskInfosByActivityID(activityID)
    for _, taskInfo in ipairs(taskInfoList or {}) do
        if taskInfo.state == ActivityTaskState.ActivityTaskStateCompleted then
            return true
        end
    end
    return false
end

--- 辅助方法：检测道具是否在MP仓库
function ActivityServer:CheckItemInMPInventory(propId)
    if isinvalid(propId) then return false end

    for _, item in Server.InventoryServer:GetItemsIterator(ESlotGroup.MPApply) do
        if item.id == propId then
            return true
        end
    end

    return false
end

--- 通用 [任务&兑换] 判断红点方法
function ActivityServer:GetReddotStateByTaskAndExchange(activityID, activityInfo)
    local exchangeFields = {
        [EActGameMode.SOL] = "prop_exchange_items",
        [EActGameMode.MP] = "exchange_item",
    }

    local bSolUnlocked, bMpUnlocked = true, true
    for index, exchangeField in pairs(exchangeFields) do
        for _, value in ipairs(activityInfo[exchangeField] or {}) do
            local propID = value.prop and value.prop.id
            if index == EActGameMode.SOL then
                if not value.unlocked then
                    bSolUnlocked = false
                    break
                end
            else
                if not self:CheckItemInMPInventory(propID) then
                    bMpUnlocked = false
                    break
                end
            end
        end
        if not bSolUnlocked and not bMpUnlocked then break end
    end
    if bSolUnlocked and bMpUnlocked then return false end

    local taskInfoList = self:GetTaskInfosByActivityID(activityID)
    for _, taskInfo in ipairs(taskInfoList or {}) do
        if taskInfo.state == ActivityTaskState.ActivityTaskStateCompleted then
            if (taskInfo.task_line == EActGameMode.SOL and not bSolUnlocked) or 
               (taskInfo.task_line == EActGameMode.MP and not bMpUnlocked) or
               (taskInfo.task_line ~= EActGameMode.SOL and taskInfo.task_line ~= EActGameMode.MP) then
                return true
            end
        end
    end

    return false
end

--- MP枪械申请额外判断红点方法
function ActivityServer:GetReddotStateByMPWeaponApply(activityInfo)
    local currencyID = activityInfo.currency_id or 0
    local currencyNum = self:GetCurrenNum(currencyID) or activityInfo.currency_num

    for _, value in ipairs(activityInfo.exchange_item or {}) do
        local propID = value.prop and value.prop.id
        local unlockNeedNum = value.currency_num or 0
        local bMpUnlocked = self:CheckItemInMPInventory(propID) or value.unlocked

        if not bMpUnlocked and currencyNum ~= 0 and currencyNum >= unlockNeedNum then
            return true
        end
    end

    return false
end

--- 通用 [里程碑] 判断红点方法
function ActivityServer:GetReddotStateByMilestone(activityID, currencyNum)
    local mileStones = self:GetMilestonesReward(activityID) ---@type pb_ActivityMilestoneAward[]
    for _, milestone in ipairs(mileStones or {}) do
        -- 对于所有已经解锁的阶段...
        if currencyNum and currencyNum >= milestone.currency_num then
            if milestone.need_choose then
                -- 对于可选奖励的里程碑阶段：没有任何一个选项被领取，则代表该阶段可领取，直接返回true
                if not Filter.AnyMatch(milestone.prop, function(prop) return prop.received end) then
                    return true
                end
            else
                -- 不可选奖励的里程碑阶段：直接检查
                if not milestone.prop[1].received then
                    return true
                end
            end
        end
    end

    return false
end

--- 通用 [兑换] 判断红点方法[deprecated=true]
function ActivityServer:GetReddotStateByExchange(activityID, currencyNum)
    local exchangeitemList = self:GetActivityExchangeItem(activityID)
    for _, exchangeitem in ipairs(exchangeitemList or {}) do
        local key = "ACTIVITY_EXCHANGE_" .. exchangeitem.exchange_id
        local remind = Facade.ConfigManager:GetUserBoolean(key, true)
        if currencyNum and currencyNum >= exchangeitem.currency_num and remind then
            if exchangeitem.exchanged_count < exchangeitem.exchange_count_max or exchangeitem.exchange_count_max == 0 then
                return true
            end
        end
    end

    return false
end

--- 通用 [最终奖励] 判断红点方法（含任务）
function ActivityServer:GetReddotStateByFinalAward(activityID, finalAward, bReceived)
    local bfinalRewardFlag = false
    if finalAward and next(finalAward) then
        bfinalRewardFlag = true
    end

    local taskInfoList = self:GetTaskInfosByActivityID(activityID)
    for _, taskInfo in ipairs(taskInfoList or {}) do
        if taskInfo.state == ActivityTaskState.ActivityTaskStateCompleted then
            return true
        elseif taskInfo.state < ActivityTaskState.ActivityTaskStateRewarded then
            bfinalRewardFlag = false
        end
    end

    if bfinalRewardFlag and not bReceived then
        return true
    end

    return false
end

--- 新兵挑战最终奖励判断红点方法（不合理，后台协议字段 final_award_received 应设计为枚举）
function ActivityServer:GetReddotStateByNovFinalAward(activityID)
    local newbieChallengeInfo = self:GetNewbieInfoByActivityID(activityID)
    local classifyConfig = self:GetActivityNewbieList(activityID)
    local taskInfoList = self:GetTaskInfosByActivityID(activityID)

    if newbieChallengeInfo then
        for _, finalAward in ipairs(newbieChallengeInfo.final_award_list or {}) do
            if finalAward.final_award then
                local goalTypeList = {}
                for _, value in ipairs(classifyConfig) do
                    if value.newbie_task_type == finalAward.final_award_type then
                        for _, goalType in ipairs(value.included_goal_types) do
                            table.insert(goalTypeList, goalType)
                        end
                        break
                    end
                end
                local bfinalRewardFlag = true
                for _, taskInfo in ipairs(taskInfoList or {}) do
                    for _, goalType in ipairs(goalTypeList) do
                        if taskInfo.objectives and taskInfo.objectives[1] and taskInfo.objectives[1].goal_type == goalType then
                            if taskInfo.state < ActivityTaskState.ActivityTaskStateCompleted then
                                bfinalRewardFlag = false
                                break
                            end
                        end
                    end
                    if not bfinalRewardFlag then
                        break
                    end
                end
                if bfinalRewardFlag and not finalAward.final_award.received then
                    return true
                end
            end
        end
    end

    return false
end

--- 星火活动判断红点方法
function ActivityServer:GetReddotStateByStarFire(activityInfo)
    local allTask = activityInfo.task_info
    local starFireInfo = activityInfo.star_fire_info

    if allTask == nil or starFireInfo == nil or starFireInfo.daily_info == nil then
        return false
    end

    local dailyTask = starFireInfo.daily_info.task_info
    local extraTask = starFireInfo.task_info

    if dailyTask == nil or extraTask == nil then
        return false
    end

    for _, task in ipairs(dailyTask) do
        if task.state == ActivityTaskState.ActivityTaskStateCompleted then
            return true
        end
    end

    local taskMap = {}
    for _, extra in ipairs(extraTask) do
        taskMap[extra.task_id] = extra.stage
    end

    for index, task in ipairs(allTask) do
        if taskMap[task.task_id] then
            allTask[index].stage = taskMap[task.task_id]
        end
    end

    for _, task in ipairs(allTask) do
        if task.stage == 2 then
            if task.state == ActivityTaskState.ActivityTaskStateCompleted then
                return true
            end
        elseif task.stage == 3 then
            local bReceived = activityInfo.final_award and activityInfo.final_award[1] and activityInfo.final_award[1].received
            if task.state == ActivityTaskState.ActivityTaskStateCompleted and not bReceived then
                return true
            end
        elseif task.sequence_order == 1 then
            if task.state == ActivityTaskState.ActivityTaskStateCompleted then
                return true
            end
        elseif task.sequence_order == 2 then
            if task.state == ActivityTaskState.ActivityTaskStateAccepted then
                local currencyNum = activityInfo.currency_num
                local chargeNum = task.objectives and task.objectives[1] and task.objectives[1].progress_max
                if currencyNum and chargeNum and currencyNum >= chargeNum then
                    return true
                end
            end
        end
    end

    return false
end

--- SBC调台次数判断红点方法
function ActivityServer:GetReddotStateByAdjustTimes(activityID, activityInfo)
    local sbcInfo = activityInfo.sbc_info
    if not sbcInfo then return false end

    local awardTimeList = sbcInfo.award_adjust_time or {}
    local totalTuningTimes = sbcInfo.total_adjust_time or 0
    if totalTuningTimes == 0 then return false end

    for index, value in ipairs(awardTimeList) do
        if totalTuningTimes >= value then
            local key = "ACT_SBC_ADJUST_TIMES_" .. activityID .. "_" .. index
            local isReached = self:GetUserBoolean(key, false)
            if not isReached then
                return true
            end
        end
    end

    return false
end

--- 归档活动判断红点方法
function ActivityServer:GetReddotStateByFiling(activityInfo)
    local taskGroups = {}
    local tasks = activityInfo.task_info
    local seqAwards = activityInfo.archive_info.sequence_award_list
    for i, v in ipairs(tasks) do
        taskGroups[v.task_line] = taskGroups[v.task_line] or {}
        taskGroups[v.task_line][v.sequence_order] = taskGroups[v.task_line][v.sequence_order] or {}
        table.insert(taskGroups[v.task_line][v.sequence_order], v)
    end

    local bReceived = false
    --- 任务线奖励
    for i, lineTasks in ipairs(taskGroups) do
        bReceived = bReceived or self:GetReddotStateByFilingLine(seqAwards, lineTasks, i)
    end
    --- 最终奖励
    if activityInfo.final_award_received then
        return bReceived
    else
        bReceived = bReceived or self:GetReddotStateByFilingFinal(activityInfo.archive_info, tasks)
    end
    
    return bReceived
end

--- 归档活动最终奖励判断红点方法
function ActivityServer:GetReddotStateByFilingFinal(archive_info, tasks)
    local bSendReq = false

    if archive_info then
        local needNum = archive_info.final_award_task_num
        local nowNum = 0
        for i, task in ipairs(tasks) do
            if task.state == 3 or task.state == 4 then
                nowNum = nowNum + 1
            end
        end
        bSendReq = nowNum >= needNum
    end

    return bSendReq
end

--- 归档活动任务线判断红点方法
function ActivityServer:GetReddotStateByFilingLine(seqAwards, tasks, lineIdx)
    local bReceived = false
    for i, seqTasks in ipairs(tasks) do
        bReceived = bReceived or self:GetReddotStateByFilingSeq(seqAwards, seqTasks, lineIdx, i)
    end
    return bReceived
end

--- 归档活动序列判断红点方法
function ActivityServer:GetReddotStateByFilingSeq(seqAwards, seqTasks, lineIdx, seqIdx)
    local bIsFinished = true
    for i, task in ipairs(seqTasks) do
        for j, goal in ipairs(task.objectives) do
            bIsFinished = bIsFinished and goal.completed
        end
    end

    for i, seq in ipairs(seqAwards) do
        if seq.task_line == lineIdx and seq.sequence == seqIdx then
            if seq.received then
                return false
            end
        end
    end
    
    return bIsFinished
end

function ActivityServer:IsRoleSurveyOptionRedDot(tActivityInfo)
    -- 筛选问答任务
    local tClueSelectedTask = {} 
    for k, v in pairs(tActivityInfo.task_info or {}) do
        if v.task_line == 2 then
            table.insert(tClueSelectedTask, v)
        end
    end

    -- 选项奖励任务的目标ID
    local tOptionRewardTask = tClueSelectedTask[1]
    local iOptionGoalID = 0
    if tOptionRewardTask then
        if tOptionRewardTask.objectives[1] then
            iOptionGoalID = tOptionRewardTask.objectives[1].id
        end
    end

    -- 选项状态
    local iOptionStatus = 0
    for index, answer in pairs(tActivityInfo.answers or {}) do
        if answer and
                answer.task_id == tOptionRewardTask.task_id and
                answer.goal_id == iOptionGoalID then
            iOptionStatus = 1
        end
    end

    -- 根据状态返回红点状态
    if iOptionStatus == 0 then
        return true
    elseif iOptionStatus == 1 then
        if tClueSelectedTask[1].state == ActivityTaskState.ActivityTaskStateCompleted then
            return true
        end
        return false
    end
end

function ActivityServer:IsRoleSurveyClueTaskTabRedDot(tActivityInfo)
    for k, v in pairs(tActivityInfo.task_info or {}) do
        if v.task_line == 1 then
            if v.state == ActivityTaskState.ActivityTaskStateCompleted then
                return true
            end
        end
    end

    return false
end

function ActivityServer:IsRoleSurveyInfoTaskRedDot(tActivityInfo, tInfoTask)
    -- 线索总数
    local iClueCount = tActivityInfo.currency_num or 0

    -- 需求线索数量
    local iClueNeed = 0
    if tInfoTask.objectives[1] then
        iClueNeed = tInfoTask.objectives[1].progress_max
    end
    
    -- 判断是否需要红点
    if tInfoTask.state == ActivityTaskState.ActivityTaskStateAccepted then
        if iClueNeed > iClueCount then
            return false
        else
            return true
        end
    elseif tInfoTask.state == ActivityTaskState.ActivityTaskStateCompleted then
        return true
    elseif tInfoTask.state == ActivityTaskState.ActivityTaskStateRewarded then
        return false
    end
    
    return false
end

function ActivityServer:IsRoleSurveyClueStageRedDot(tActivityInfo)
    if self:IsRoleSurveyOptionRedDot(tActivityInfo) then
        return true
    end

    if self:IsRoleSurveyClueTaskTabRedDot(tActivityInfo) then
        return true
    end
    
    return false
end

function ActivityServer:IsRoleSurveyInfoTaskStageRedDot(tActivityInfo)
    for k, v in pairs(tActivityInfo.task_info or {}) do
        if v.task_line == 3 then
            if self:IsRoleSurveyInfoTaskRedDot(tActivityInfo, v) then
                return true
            end
        end
    end
    
    return false
end

function ActivityServer:IsRoleSurveyInfoTaskAllRedDot(tActivityInfo)
    local tInfoTask = {}  -- 情报解析任务
    local tBossTask = {}  -- Boss任务

    for k, v in pairs(tActivityInfo.task_info or {}) do
        if v.task_line == 3 then
            table.insert(tInfoTask, v)
        elseif v.task_line == 4 then
            table.insert(tBossTask, v)
        end
    end
    
    -- 计算Boss页面的状态
    local bInfoFinish = true
    for k, v in pairs(tInfoTask) do
        if v.state ~= ActivityTaskState.ActivityTaskStateRewarded then
            bInfoFinish = false
        end
    end
    local bBossFeat = false
    if tBossTask[1] then
        if tBossTask[1].state == ActivityTaskState.ActivityTaskStateCompleted or
                tBossTask[1].state == ActivityTaskState.ActivityTaskStateRewarded then
            bBossFeat = true
        end
    end
    local bAuth = tActivityInfo.survey_boss_info.triggered

    if bBossFeat then
        return false
    else
        if bAuth then
            return false
        else
            if bInfoFinish then
                return true
            else
                return false
            end
        end
    end
    
    return false
end

function ActivityServer:IsRoleSurveyBossReward(tActivityInfo)
    local tBossTask = {}  -- Boss任务

    for k, v in pairs(tActivityInfo.task_info or {}) do
        if v.task_line == 4 then
            table.insert(tBossTask, v)
        end
    end

    if tBossTask[1].state == ActivityTaskState.ActivityTaskStateCompleted and 
        not tActivityInfo.final_award_received then
        return true
    end
    
    return false
end

function ActivityServer:IsRoleSurveyExchangeLock(tActivityInfo)
    local tInfoTask = {}

    for k, v in pairs(tActivityInfo.task_info or {}) do
        if v.task_line == 3 then
            table.insert(tInfoTask, v)
        end
    end

    local bInfoFinish = true
    for k, v in pairs(tInfoTask) do
        if v.state ~= ActivityTaskState.ActivityTaskStateRewarded then
            bInfoFinish = false
        end
    end

    return not bInfoFinish
end

function ActivityServer:IsRoleSurveyExchangeInfoRedDot(tActivityInfo, exchangeInfo)
    local iExchangeID = exchangeInfo.exchange_id 
    local iActivityID = tActivityInfo.actv_id

    local key = "ACTIVITY_REDEEM_" .. iActivityID .. iExchangeID
    local bClick = self:GetUserBoolean(key, false)

    if not self:IsRoleSurveyExchangeLock(tActivityInfo) and not bClick then
        return true
    end

    return false
end

function ActivityServer:IsRoleSurveyExchangeRedDot(tActivityInfo)
    for iIndex, tExchangeItem in pairs(tActivityInfo.exchange_item or {}) do
        if self:IsRoleSurveyExchangeInfoRedDot(tActivityInfo, tExchangeItem) then
            return true
        end
    end
    
    return false
end

function ActivityServer:IsStarsRocketRedDot(tActivityInfo)
    for k, v in pairs(tActivityInfo.task_info or {}) do
        if v.task_line == 1 and
                v.state == ActivityTaskState.ActivityTaskStateCompleted then
            return true
        end
    end

    return false
end

function ActivityServer:IsStarsRedDot(tActivityInfo)
    for k, v in pairs(tActivityInfo.task_info or {}) do
        if v.state == ActivityTaskState.ActivityTaskStateCompleted then
            return true
        end
    end
    
    return false
end

-- //本地缓存文件中间接口
function ActivityServer:RecordUserConfigKey(key)
    if _WITH_EDITOR ~= 1 then return end

    if not key then
        loginfo("[heimu] ActivityLogic RecordUserConfigKey with a nil key!")
        return
    end
    
    local cachedKeyList = Facade.ConfigManager:GetArray(ACT_KEY_RECORD, {})
    for _, value in pairs(cachedKeyList or {}) do
        if value == key then
            return
        end
    end
    table.insert(cachedKeyList, key)
    Facade.ConfigManager:SetArray(ACT_KEY_RECORD, cachedKeyList)
end

function ActivityServer:SetUserBoolean(key, value)
    self:RecordUserConfigKey(key)
    Facade.ConfigManager:SetUserBoolean(key, value)
end

function ActivityServer:GetUserBoolean(key, default)
    return Facade.ConfigManager:GetUserBoolean(key, default)
end

---------------------------------调酒师---------------------------------------
function ActivityServer:SendMakeDrink(activityID, drink_id, matarial_ids, make_time)
    if activityID == nil or drink_id == nil or matarial_ids == nil or #matarial_ids == 0 then
        return
    end
    if make_time == nil or make_time > 10 then
        make_time = 10
    end
    make_time = math.floor(make_time)
    local function OnFunc(res)
        if res.result == 0 then
            self:UpdateServerActivityInfoList({res.activity_info}, res.currency_infos)
            self.Events.evtActivityDataUpdateGeneral:Invoke(activityID, res.data_change)
        end
        self.Events.evtActivityDataUpdateGeneral:Invoke("KEY_ErrorTipIfNeeded", res)
    end
    local Req = pb.CSActivityMakeDrinkReq:New()
    Req.activity_id = activityID
    Req.base_drink_id = drink_id
    Req.material_ids = matarial_ids
    Req.move_time = make_time
    Req:Request(OnFunc)
end

function ActivityServer:SendExchangeProps(activityID, task_id)
    if activityID == nil or task_id == nil then
        return
    end
    local function OnFunc(res)
        if res.result == 0 then
            self:UpdateServerActivityInfoList({res.activity_info}, res.currency_infos)
            self.Events.evtActivityDataUpdateGeneral:Invoke(activityID, res.data_change)
        end
        self.Events.evtActivityDataUpdateGeneral:Invoke("KEY_ErrorTipIfNeeded", res)
    end
    local Req = pb.CSActivityExchangePropsReq:New()
    Req.activity_id = activityID
    Req.task_id = task_id
    Req:Request(OnFunc)
end

-----------------------------------------------------------------------------
-----------------------------------新年集换卡-----------------------------------

--拉取新年活动商城(处理红点数据)
function ActivityServer:_PullTheSpringFestival()
    local info = self:GetActivityInfosByType(ActivityType.ActivityTypeSheOneCard)
    if not info or next(info) == nil then return end

    for _, atv in ipairs(info) do
        if atv then
            if atv.act_theme_she_one_card_info then
                if atv.act_theme_she_one_card_info.is_new_card_received then
                    self:UpdateNewYearCard(atv.actv_id, EActSubInteractive.Next3, nil)
                else
                    self:UpdateNewYearCard(atv.actv_id, EActSubInteractive.Next3, {})
                end
            end
            self:UpdateNewYearCard(atv.actv_id, EActSubInteractive.Next2, nil)
            self:SendActivitySOCGetShopReq(atv.actv_id)--商城
            break
        end
    end
end

--获取新年集换卡数据
function ActivityServer:UpdateNewYearCard(act_id, type_id, data)
    if self._actSheOneCard == nil then
        self._actSheOneCard = {}
    end
    if act_id and type_id then
        if self._actSheOneCard[act_id] == nil then
            self._actSheOneCard[act_id] = {}
        end
        self._actSheOneCard[act_id][type_id] = data
    end
end

function ActivityServer:GetNewYearCard(act_id, type_id)
    if self._actSheOneCard == nil then
        self._actSheOneCard = {}
    end
    if act_id and type_id then
        local list = self._actSheOneCard[act_id]
        if list then
            return list[type_id]
        end
    end
end

--赠送好友
function ActivityServer:SendActivitySOCSendCardReq(activityID, target_player_id, card_id)
    if activityID == nil or target_player_id == nil or card_id == nil then
        return
    end
    local function OnFunc(res)
        if res.result == 0 then
            self:UpdateNewYearCard(activityID, EActSubInteractive.Next1, nil)
            self:UpdateServerActivityInfoList({res.activity_info})
            self.Events.evtActivityDataUpdateGeneral:Invoke(activityID, EActSubInteractive.Next1)
        end
        self.Events.evtActivityDataUpdateGeneral:Invoke("KEY_ErrorTipIfNeeded", res)
    end
    local Req = pb.CSActivitySOCSendCardReq:New()
    Req.activity_id = activityID
    Req.target_player_id = target_player_id
    Req.card_id = card_id
    Req:Request(OnFunc)
end

--获取MP结算流水
function ActivityServer:SendActivitySOCMPFlowsReq(activityID)
    if activityID == nil then
        return
    end
    local function OnFunc(res)
        if res.result == 0 then
            self:UpdateNewYearCard(activityID, EActSubInteractive.Next2, res.mp_flows)
            self.Events.evtActivityDataUpdateGeneral:Invoke(activityID, EActSubInteractive.Next2)
        end
        self.Events.evtActivityDataUpdateGeneral:Invoke("KEY_ErrorTipIfNeeded", res)
    end
    local Req = pb.CSActivitySOCMPFlowsReq:New()
    Req.activity_id = activityID
    Req:Request(OnFunc)
end

--拉取获赠卡片未读列表
function ActivityServer:SendActivitySOCGetRecvCardsReq(activityID)
    if activityID == nil then
        return
    end
    local function OnFunc(res)
        if res.result == 0 then
            local cards = self:GetNewYearCard(activityID, EActSubInteractive.Next3)
            if cards == nil then
                cards = {}
            end
            for index, card in ipairs(res.recv_cards or {}) do
                table.insert(cards, card)
            end
            self:UpdateServerActivityInfoList({res.activity_info})
            self:UpdateNewYearCard(activityID, EActSubInteractive.Next3, res.recv_cards)
            self.Events.evtActivityDataUpdateGeneral:Invoke(activityID, EActSubInteractive.Next3)
        end
        self.Events.evtActivityDataUpdateGeneral:Invoke("KEY_ErrorTipIfNeeded", res)
    end
    local Req = pb.CSActivitySOCGetRecvCardsReq:New()
    Req.activity_id = activityID
    Req:Request(OnFunc)
end

--拉取赠送流水
function ActivityServer:SendActivitySOCSendRecvFlowsReq(activityID)
    if activityID == nil then
        return
    end
    local function OnFunc(res)
        if res.result == 0 then
            self:UpdateNewYearCard(activityID, EActSubInteractive.Next4, res.send_recv_flows)
            self.Events.evtActivityDataUpdateGeneral:Invoke(activityID, EActSubInteractive.Next4)
        end
        self.Events.evtActivityDataUpdateGeneral:Invoke("KEY_ErrorTipIfNeeded", res)
    end
    local Req = pb.CSActivitySOCSendRecvFlowsReq:New()
    Req.activity_id = activityID
    Req:Request(OnFunc)
end

--请求拆包
function ActivityServer:SendActivitySOCDrawReq(activityID, drawType)
    if activityID == nil or drawType == nil then
        return
    end
    local function OnFunc(res)
        if res.result == 0 then
            self:UpdateNewYearCard(activityID, EActSubInteractive.Next5, res.cards)
            self:UpdateServerActivityInfoList({res.activity_info})
            self.Events.evtActivityDataUpdateGeneral:Invoke(activityID, EActSubInteractive.Next5)
        end
        self.Events.evtActivityDataUpdateGeneral:Invoke("KEY_ErrorTipIfNeeded", res)
    end
    local Req = pb.CSActivitySOCDrawReq:New()
    Req.activity_id = activityID
    Req.draw_type = drawType
    Req:Request(OnFunc)
end

--请求商城
function ActivityServer:SendActivitySOCGetShopReq(activityID)
    if activityID == nil then
        return
    end
    local function OnFunc(res)
        if res.result == 0 then
            self:UpdateNewYearCard(activityID, EActSubInteractive.Next6, res.shop_items)
            self:UpdateServerActivityInfoList({res.activity_info})
            self.Events.evtActivityDataUpdateGeneral:Invoke(activityID, EActSubInteractive.Next6)
        end
        self.Events.evtActivityDataUpdateGeneral:Invoke("KEY_ErrorTipIfNeeded", res)
    end
    local Req = pb.CSActivitySOCGetShopReq:New()
    Req.activity_id = activityID
    Req:Request(OnFunc)
end

--请求合成
function ActivityServer:SendActivitySOCCompositeReq(activityID, composite_type)
    if activityID == nil or composite_type == nil then
        return
    end
    local function OnFunc(res)
        if res.result == 0 then
            self:UpdateServerActivityInfoList({res.activity_info})
            local data_change = {}
            for index, card in ipairs(self:GetCards(activityID) or {}) do
                if card.id == res.card_id then
                    table.insert(data_change, card)
                    break
                end
            end
            self:UpdateNewYearCard(activityID, EActSubInteractive.Next7, data_change)
            self.Events.evtActivityDataUpdateGeneral:Invoke(activityID, EActSubInteractive.Next7)
        end
        self.Events.evtActivityDataUpdateGeneral:Invoke("KEY_ErrorTipIfNeeded", res)
    end
    local Req = pb.CSActivitySOCCompositeReq:New()
    Req.activity_id = activityID
    Req.composite_type = composite_type
    Req:Request(OnFunc)
end

--请求兑换
function ActivityServer:SendActivitySOCExchangeReq(activityID, shop_id)
    if activityID == nil or shop_id == nil then
        return
    end
    local function OnFunc(res)
        if res.result == 0 then
            self:UpdateServerActivityInfoList({res.activity_info})
            self:UpdateNewYearCard(activityID, EActSubInteractive.Next8, res.data_change)
            --请求物流卷
            self:SendLogisticsCurrencyNum()
            --请求商店
            self:SendActivitySOCGetShopReq(activityID)
            self.Events.evtActivityDataUpdateGeneral:Invoke(activityID, EActSubInteractive.Next8)
        end
        self.Events.evtActivityDataUpdateGeneral:Invoke("KEY_ErrorTipIfNeeded", res)
    end
    local Req = pb.CSActivitySOCExchangeReq:New()
    Req.activity_id = activityID
    Req.shop_id = shop_id
    Req:Request(OnFunc)
end

function ActivityServer:IsReddotCardHolder(activityID, reddotMode)
    local info = self:GetActivityInfoByActivityID(activityID)
    if info == nil then
        return false
    end
    local data = info.act_theme_she_one_card_info
    if data then
        --sol模式(1)
        if self:IsReddotByCardHolderId(data.sol_pack_id) and reddotMode ~= 2 then
            local num = Server.InventoryServer:GetItemNumById(data.sol_pack_id)
            if num > 0 and data.scissor_num > 0 then
                return true
            end
        end
        --mp模式(2)
        if self:IsReddotByCardHolderId(data.mp_pack_id) and reddotMode ~= 1 then
            if data.mp_pack_num > 0 and data.scissor_num > 0 then
                return true
            end
        end
    end
    return false
end

function ActivityServer:IsReddotByCardHolderId(id, isBool)
    if id == nil then
        return false
    end
    local key = "ACT_KEY_CARD_HOLDER_REDDOT"..id
    if isBool ~= nil then
        self:SetUserBoolean(key, false)
    end
    return self:GetUserBoolean(key, true)
end

function ActivityServer:GetCards(activityID)
    if activityID == nil then
        return {}
    end
    local heroCards = {}
    local info = self:GetActivityInfoByActivityID(activityID)
    if info and info.act_theme_she_one_card_info then
        local EventShe1CardCards = Facade.TableManager:GetTable("EventShe1CardCards")--当前all卡片
        local cards = {}
        for index, card in ipairs(info.act_theme_she_one_card_info.owned_card_list or {}) do
            if card and card.id then
                cards[card.id] = card
            end
        end
        for index, value in pairs(EventShe1CardCards or {}) do
            local card = {
                num = 0,
                id = value.ID,
                type = value.Type,
                name = value.Name,
                desc = value.Desc,
                weight = value.Weight,
                path = value.Asset,
            }
            if card.type == 1 then
                card.index = 3
            elseif card.type == 2 then
                card.index = 4
            elseif card.type == 3 then
                card.index = 2
            end
            if cards and cards[card.id] then
                card.num = cards[card.id].num
            end
            table.insert(heroCards, card)
        end
        --排序
        local func = function(a, b)
            if a == nil or b == nil then
                return false
            end
            -- if a.num ~= b.num then
            --     return a.num > b.num
            -- end
            if a.id ~= b.id then
                return a.id > b.id
            end
            return false
        end
        table.sort(heroCards, func)
    end
    return heroCards
end
--------------------------------------------------------------------------------

----------------------------大战场曼德尔砖---------------------------------------------

---@param optCallback nil|fun(res)
function ActivityServer:SendTDMMandelBrickReq(optCallback)
    loginfo("ActivityServer:SendTDMMandelBrickReq")
    local fOnPrepareMapBoard = function(res)
        loginfo("ActivityServer:receiveTDMMandelBrickReq",res.result)
        if res.result == 0 then
            self._tdmMandelBrickInfo={}
            for key, value in pairs(res.drop_list) do
                if value.type_id==MPDropLimitType.MandelBrick or value.type_id==MPDropLimitType.MandelBrickIron then
                    value.progress=0
                    if value.total_weight~=0 then
                        value.progress=math.floor(value.weight/value.total_weight*100)
                    end
                    value.bFinish=value.week_num==value.week_limit
                    self._tdmMandelBrickInfo[value.type_id]=value
                end
            end
            self.Events.evtMandelInfoUpdate:Invoke()
        end

        if optCallback then
            optCallback(res)
        end
    end

    local req = pb.CSSettlementGetTdmMandelBrickReq:New()
    req:Request(fOnPrepareMapBoard)
end

function ActivityServer:GetTDMMandelBrickInfo()
    return self._tdmMandelBrickInfo
end

--常规曼德尔砖
function ActivityServer:GetTDMNormalMandelBrickInfo()
    return self._tdmMandelBrickInfo[MPDropLimitType.MandelBrick]
end

--活跃曼德尔砖
function ActivityServer:GetTDMSpecialMandelBrickInfo()
    return self._tdmMandelBrickInfo[MPDropLimitType.MandelBrickIron]
end

function ActivityServer:CheckNormalTDMMandelBrickIsDropFinish()
    local info=self:GetTDMNormalMandelBrickInfo()
    if info then
        return info.week_num==info.week_limit
    end
    return true
end

function ActivityServer:CheckSpecialTDMMandelBrickIsDropFinish()
    local info=self:GetTDMSpecialMandelBrickInfo()
    if info then
        return info.week_num==info.week_limit
    end
    return true
end

-- 活动是否已经开始（用于预告态相关逻辑）
function ActivityServer:IsActivityStarted(activityID)
    if not self.AllActivityInfos[activityID] then return nil end
    local now = Facade.ClockManager:GetLocalTimestamp()
    local start_date = self.AllActivityInfos[activityID].start_date
    return start_date ~= nil and start_date ~= 0 and start_date <= now
end

-- 活动是否已经完成（用于完成态相关逻辑）
function ActivityServer:IsActivityCompleted(activityID)
    if not self.AllActivityInfos[activityID] then return nil end
    return self.AllActivityInfos[activityID].finished
end

function ActivityServer:ClearForecastTimers()
    -- 清除所有预告Timer
    for activityID, timer in pairs(self._forecastTimers) do
        ChronoTimer:Cancel(timer)
    end
    self._forecastTimers = {}
end

---@param activityInfos pb_ActivityInfo[]
function ActivityServer:ProcessForecastTimers(activityInfos)
    if not activityInfos then return end
    
    self:ClearForecastTimers()
    for _, activityInfo in pairs(activityInfos) do
        local activityID = activityInfo.actv_id
        if not self:IsActivityStarted(activityID) then
            self._forecastTimers[activityID] =
                ChronoTimer:New("timestamp",
                                activityInfo.start_date,
                                CreateCallBack(self.OnForecastTimer, self, activityID))
        end
    end
end

-- 预告解锁的活动时间到了
function ActivityServer:OnForecastTimer(activityID)
    self._forecastTimers[activityID] = nil
end

function ActivityServer:HasForecastDataOnly(activityID)
    return self.AllActivityInfos[activityID] and self.AllActivityInfos[activityID].isPreview == true
end

-- 更新潘多拉活动的完成态
function ActivityServer:UpdatePandoraCompletionStatus(activityInfo)
    local pandoraInfo = activityInfo.pandora_info
    if not pandoraInfo or not pandoraInfo.app_id then return end
    
    local isFinished = Server.GameletServer:IsAppIDTaskFinish(pandoraInfo.app_id)
    activityInfo.finished = isFinished
end

-- 判断活动是否准备好
function ActivityServer:IsActivityPrepared(activityInfo)
    if not activityInfo then return false end

    if activityInfo.actv_type ~= ActivityType.ActivityTypePandora then
        return true
    end

    local pandoraInfo = activityInfo.pandora_info
    if not pandoraInfo or not pandoraInfo.app_id then return false end

    self:UpdatePandoraCompletionStatus(activityInfo)

    return Server.GameletServer:GetActiveApp(pandoraInfo.app_id)
end

------------------------------------------------活动本地缓存--------------------------------------------
function ActivityServer:IsLocalUser(activityID, userId, userType, userBool)
    if activityID == nil or userId == nil or userType == nil then
        return false
    end
    local key = string.format("ACT_KEY_ISUSER_%s_%s_%s", activityID, userId, userType)
    if userBool == nil then
        local isBool = self:GetUserBoolean(key, false)
        if isBool == nil then
            return false
        else
            return isBool
        end
    end
    if type(userBool) == "boolean" then
        self:SetUserBoolean(key, userBool)
    end
    return false
end

function ActivityServer:NumLocalUser(activityID, userId, userType, userNum)
    if activityID == nil or userId == nil or userType == nil then
        return 1
    end
    local key = string.format("ACT_KEY_NUMUSER_%s_%s_%s", activityID, userId, userType)
    if userNum == nil then
        local num = self:GetUserInt(key, 1)
        if num == nil then
            return 1
        else
            return num
        end
    end
    if type(userNum) == "number" then
        self:SetUserInt(key, userNum)
    end
    return 1
end


function ActivityServer:GetUserInt(key, default)
    return Facade.ConfigManager:GetUserInt(key, default)
end

function ActivityServer:SetUserInt(key, value)
    self:RecordUserConfigKey(key)
    Facade.ConfigManager:SetUserInt(key, value)
end


--判断是否是货币------------------------------------------------------------------------------------------------
function ActivityServer:IsCurrency(currencyId)
    for key, value in pairs(ECurrencyItemId or {}) do
        if value == currencyId then
            return true
        end
    end
    return false
end

--查找货币下标
function ActivityServer:GetIndexByCurrencyId(currencyId)
    --查找货币下标
    local currencyType
    for key, value in pairs(ECurrencyItemId or {}) do
        if value and currencyId then
            if value == currencyId then
                currencyType = key
                break
            end
        end
    end
    for key, value in pairs(ECurrencyClientId or {}) do
        if key and currencyType then
            if key == currencyType then
                return value
            end
        end
    end
    --没有找到货币,默认返回0
    return 0
end

--通过道具id获取道具数量(包括货币/仓库/藏品/活动)
function ActivityServer:GetPropNumByPropId(propId)
    if propId == nil then
        --如果参数错误,就是返回0吧
        return 0
    end
    if self:IsCurrency(propId) then
        --获取货币
        return Server.CurrencyServer:GetNumByItemId(propId)
    else
        --获取仓库道具
        local num = Server.InventoryServer:GetItemNumById(propId)
        if num == 0 then
            --获取藏品道具
            local itemData = Server.CollectionServer:GetCollectionItemById(propId)
            if itemData and itemData.num and itemData.num > 0 then
                return itemData.num
            end
            --获取活动代币
            num = self:GetCurrenNum(propId)
            if num then
                return num
            else
                return 0
            end
        else
            return num
        end
    end
end

-------------------------------------------检查货币/道具是否足够----------------------------------------
function ActivityServer:IsCanExchange(currencyIdOrProps, exchangeNum)
    if currencyIdOrProps == nil then
        return false
    end

    exchangeNum = exchangeNum or 1
    --if is currency exchange
    if type(currencyIdOrProps) == "number" then
        local num = self:GetPropNumByPropId(currencyIdOrProps)
        
        if num then
            return num >= exchangeNum
        else
            return false
        end
    end
    --if is prop exchange
    if type(currencyIdOrProps) == "table" then
        for index, prop in ipairs(currencyIdOrProps or {}) do
            if prop and prop.id and prop.num then
                local num = self:GetPropNumByPropId(prop.id)
                
                local max = prop.num * exchangeNum
                if num < max then
                    return false
                end
            end
        end
        return true
    end
    return false
end

------------------------------------------------------------------------------------------------------
--#region 多选大奖里程碑

---判断某个皮肤是否已拥有
---@param item ItemBase
---@return boolean? -- true:已拥有 false:未拥有 nil:不是皮肤
local function ItemIsSkinAndIsOwned(item)
    -- 未拥有的载具皮肤不会出现在 _collectionMap，VehicleServer也是根据 _collectionMap 是否存在来判断皮肤拥有状态，未拥有的武器皮肤会出现在 _collectiomMap
    -- 而且 CollectionServer里武器皮肤+近战皮肤 才等于 itemBase 分类中的武器皮肤，需要再合并一次判断

    if item.itemMainType == EItemType.WeaponSkin then
        return Server.CollectionServer:IsOwnedWeaponSkin(item.id, item.gid) or
                Server.CollectionServer:IsOwnedMeleeSkin(item.id, item.gid)
    end

    if item.itemMainType == EItemType.VehicleSkin then
        return Server.CollectionServer:GetCollectionItemById(item.id, item.gid) ~= nil
    end

    return nil
end

---@param award pb_ActivityAward
local function ActivityAwardToItemBase(award)
    return ItemBase:NewIns(award.prop.id, award.prop.num, award.prop.gid)
end

local Not = Functional.Not
local Get = Functional.Get

---@class MilestoneAwardChoiceInfo
---@field milestoneIdx              integer                                 大奖的里程碑顺序（是第几个里程碑奖励）
---@field awards                    table<integer, MilestoneAwardOption>    propID作为索引的各个可选里程碑大奖的信息
---@field sortedAwards              MilestoneAwardOption[]                  按照优先级排序的各个可选里程碑大奖的信息
---@field selectedProp              integer                                 当前选中的奖励propID
---@field selectedAward             MilestoneAwardOption                    当前选中的奖励信息
---@field received                  boolean                                 是否已经领取过(其中任意一项)

---@class MilestoneAwardOption
---@field awardInfo         pb_ActivityAward                pb奖励信息
---@field item              ItemBase                        ItemBase形式的物品信息
---@field isOwnedSkin       boolean                         是否为已拥有的皮肤
---@field isNewSkin         boolean                         是否为未拥有的皮肤
---@field priority          integer                         策划配置的保底排序优先级

---查询已整理好的里程碑大奖选项信息: 按propID索引、按展示顺序排序、标记皮肤和已拥有状态
---@param milestoneInfo pb_ActivityMilestoneAward
---@return MilestoneAwardChoiceInfo|false --返回false代表该里程碑大奖不可选择
function ActivityServer:GetMilestoneFinalRewardChoiceInfo(milestoneInfo)

    ---@comment 大奖在里程碑的顺序
    local milestoneIdx, _ = Filter.FirstMatch(milestoneInfo, Get("need_choose"))
    if not milestoneIdx then
        return false
    end

    local awardList = milestoneInfo[milestoneIdx].prop
    local selectedPropID = milestoneInfo[milestoneIdx].choose_prop_id

    ---计算额外信息和awardInfo保存在一起
    ---@type table<integer, MilestoneAwardOption>
    local awardOptions = Table.Transform(awardList,
        ---@param award pb_ActivityAward
        ---@return MilestoneAwardOption
        function(idx, award)
            local item = ActivityAwardToItemBase(award)
            local isSkinAndIsOwned = ItemIsSkinAndIsOwned(item)
            local result = {
                awardInfo   = award,
                awardIdx    = idx,
                item        = item,
                isNewSkin   = (isSkinAndIsOwned == false), -- 是皮肤，未拥有
                isOwnedSkin = (isSkinAndIsOwned == true),  -- 是皮肤，已拥有
            }
            return {{item.id, result}}
        end
    )
    
    ---排序
    ---@type MilestoneAwardOption[]
    local sortedAwardOptions = Sort.MergeSort(
        Table.ToList(awardOptions),
        Sort.Preferenced({
            Sort.By(Get("isNewSkin")),                          -- 1. 新皮肤优先排序
            Sort.By(Not(Get("isOwnedSkin"))),                   -- 2. 可重复获得的物资优先
            Sort.By(Safe.MakeGetter("awardInfo.priority")),     -- 3. 策划配置优先级
        })
    )
    sortedAwardOptions = Sort.Reverse(sortedAwardOptions)       -- MergeSort是从小到大排的，这里需要倒序

    ---找到当前选中项
    local _, selectedAward = Filter.FirstMatch(awardOptions, function(awardOption) return awardOption.item.id == selectedPropID end)

    ---是否已领取
    local received = Filter.AnyMatch(awardOptions, function(awardOption) return awardOption.awardInfo.received end)

    ---@type MilestoneAwardChoiceInfo
    local result = {
        selectedAward   = selectedAward,
        milestoneIdx    = milestoneIdx,
        awards          = awardOptions,
        sortedAwards    = sortedAwardOptions,
        selectedProp    = selectedPropID,
        received        = received,
    }
    return result
end

---对于部分无法重复获得的物品，玩家可能在选择奖励之后又从其他途径获得了
---调用该方法进行检查会在冲突时自动修改奖励选择为展示排序顺序的第一个可选项
---返回是否发生了fallback情况
---@param activityID integer
---@param optCallback nil|fun(res:pb_CSActivityChangeMilestoneAwardRes)
---@return boolean
function ActivityServer:CheckMilestoneFinalRewardFallback(activityID, optCallback)

    ---检查活动存在
    ---@type pb_ActivityInfo
    local activityInfo = self.AllActivityInfos[activityID]
    if not activityInfo then return false end

    ---检查确实是多选大奖活动
    local choiceInfo = self:GetMilestoneFinalRewardChoiceInfo(activityInfo.milestone_award)
    if not choiceInfo then return false end

    logwarning("[ChoosableMilestone] Checking fallback, current selected:"..tostring(choiceInfo.selectedAward))

    ---已领奖直接跳过
    if choiceInfo.received then return false end

    ---只有在冲突或未选择过的时候才处理fallback逻辑
    local bFallback = (choiceInfo.selectedAward == nil) or
                      (ItemIsSkinAndIsOwned(choiceInfo.selectedAward.item))
    if not bFallback then return false end

    ---选择按优先级排序的第一个奖励
    ---@type any, MilestoneAwardOption
    local _, fallbackAwardInfo = Filter.FirstMatch(choiceInfo.sortedAwards, Not(Get("IsOwnedSkin")))
    if not fallbackAwardInfo then
        logerror("[ChoosableMilestone] Fallback needed but no available option is present.")
        return false
    end
    self:SendSelectFinalMilestoneAwardReq(activityID, fallbackAwardInfo.awardInfo.prop.id, optCallback)
    
    logwarning("[ChoosableMilestone] Fallback: changing selected reward "..tostring(choiceInfo.selectedAward).." -> "..tostring(fallbackAwardInfo.item.id))
    return true
end

--玩家选择里程碑的最终奖励
---@param optCallback nil|fun(res:pb_CSActivityChangeMilestoneAwardRes)
---@param propID integer
function ActivityServer:SendSelectFinalMilestoneAwardReq(activityID, propID, optCallback)

    ---@type pb_ActivityInfo
    local activityInfo = self.AllActivityInfos[activityID]
    if not activityInfo then return false end

    local milestoneIdx
    for idx, awardInfo in ipairs(activityInfo.milestone_award) do
        if awardInfo.need_choose then
            milestoneIdx = idx
        end
    end
    if not milestoneIdx then return false end

    local function OnRes(res)
        if res.result ~= 0 then
            logerror("[ChoosableMilestone] Unable to select final reward, error ", res.result)
        end
        
        -- 更新本地数据
        local storeSuccess = Safe.SafeSetter(self.AllActivityInfos, {activityID, "milestone_award", milestoneIdx, "choose_prop_id"}, propID, false)
        if not storeSuccess then logerror("Unable to select final reward, error ", res.result) end
        logwarning("[ChoosableMilestone] Selected "..tostring(propID))

        if optCallback then
            optCallback(res)
        end
    end

    local req = pb.CSActivityChangeMilestoneAwardReq:New()
    req.activity_id = activityInfo.actv_id
    req.reward_id = activityInfo.milestone_award[milestoneIdx].reward_id
    req.prop_id = propID

    -- 高频过滤在UI做了，C++默认设置1秒，实际操作体感较差
    -- 前端限制0.3秒
    req:Request(OnRes, {bEnableHighFrequency = true})
    logwarning("[ChoosableMilestone] Try to select "..tostring(propID))
end

--#endregion
------------------------------------------------------------------------------------------------------

-------------------------------------------万能模板通用兑换----------------------------------------
function ActivityServer:SendPropExchange(activity_id, exchange_id, plan_id, time)
    if activity_id == nil or exchange_id == nil or plan_id == nil then
        loginfo("ssy req prop data is nil ?")
        return
    end
    loginfo("ssy req exchange id and plan id ", exchange_id, ", ", plan_id)
    local function OnFunc(res)
        if res.result == 0 then
            loginfo("ssy req exchange success !!!")
            self:UpdateNewYearCard(activity_id, EActSubInteractive.Next1, res.data_change)
            self:UpdateServerActivityInfoList(res.activity_infos, res.currency_infos)--更新活动/货币
            self.Events.evtActivityDataUpdateGeneral:Invoke(activity_id, EActSubInteractive.Next1)
        end
        self.Events.evtActivityDataUpdateGeneral:Invoke("KEY_ErrorTipIfNeeded", res)
    end
    local Req = pb.CSActivityPropExchangeReq:New()
    Req.activity_id = activity_id
    Req.exchange_id = exchange_id
    Req.plan_id = plan_id
    Req.time = time or 1
    Req:Request(OnFunc, {bEnableHighFrequency = true})
end

--万能收藏
function ActivityServer:SendActExcFocus(activity_id, exchange_id, is_focus)
    if activity_id == nil or exchange_id == nil or is_focus == nil then
        loginfo("ssy req focus data is nil ?")
        return
    end
    loginfo("ssy req focus id and focus id ", exchange_id, ", ", is_focus)
    local function OnFunc(res)
        if res.result == 0 then
            loginfo("ssy req focus success !!!")
            local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(activity_id)
            if activityInfo then
                for index, item in ipairs(activityInfo.prop_exchange_items or {}) do
                    if item.exchange_id == exchange_id then
                        item.is_focus = is_focus
                        break
                    end
                end
                self:UpdateServerActivityInfoList({activityInfo})
            end
            self:UpdateNewYearCard(activity_id, EActSubInteractive.Next2, exchange_id)
            self.Events.evtActivityDataUpdateGeneral:Invoke(activity_id, EActSubInteractive.Next2)
        end
        self.Events.evtActivityDataUpdateGeneral:Invoke("KEY_ErrorTipIfNeeded", res)
    end
    local Req = pb.CSActivityFocusExchangeReq:New()
    Req.activity_id = activity_id
    Req.exchange_id = exchange_id
    Req.is_focus = is_focus
    Req:Request(OnFunc, {bEnableHighFrequency = true})--取消高频请求拦截
end

--万能选择组
function ActivityServer:SendActExcPlan(activity_id, exchange_id, plan_id)
    if activity_id == nil or exchange_id == nil or plan_id == nil then
        loginfo("ssy req plan data is nil ?")
        return
    end
    loginfo("ssy req plan id and plan id ", exchange_id, ", ", plan_id)
    local function OnFunc(res)
        if res.result == 0 then
            loginfo("ssy req plan success !!!")
            local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(activity_id)
            if activityInfo then
                for index, item in ipairs(activityInfo.prop_exchange_items or {}) do
                    if item.exchange_id == exchange_id then
                        item.selected_plan = plan_id
                        break
                    end
                end
                self:UpdateServerActivityInfoList({activityInfo})
            end
            self:UpdateNewYearCard(activity_id, EActSubInteractive.Next3, exchange_id)
            self.Events.evtActivityDataUpdateGeneral:Invoke(activity_id, EActSubInteractive.Next3)
        end
        self.Events.evtActivityDataUpdateGeneral:Invoke("KEY_ErrorTipIfNeeded", res)
    end
    local Req = pb.CSActivityChangeExchangePlanReq:New()
    Req.activity_id = activity_id
    Req.exchange_id = exchange_id
    Req.plan_id = plan_id
    Req:Request(OnFunc, {bEnableHighFrequency = true})
end

--模拟兑换模板时间刷新
function ActivityServer:SendSimulateTime(activity_id, exchange_id)
    if activity_id == nil then
        return
    end
    local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(activity_id)
    if activityInfo then
        for index, item in ipairs(activityInfo.prop_exchange_items or {}) do
            if item.exchange_id == exchange_id then
                item.exchanged_count = 0
                break
            end
        end
        self:UpdateServerActivityInfoList({activityInfo})
    end
    self:UpdateNewYearCard(activity_id, EActSubInteractive.Next3, exchange_id)
    self.Events.evtActivityDataUpdateGeneral:Invoke(activity_id, EActSubInteractive.Next3)
end

--处理订阅红点
function ActivityServer:ReddotSubscribe(eventId, status)
    if self._reddotSubscribe == nil then
        self._reddotSubscribe = {}
    end
    if eventId and self._reddotSubscribe then
        if status ~= nil then
            self._reddotSubscribe[eventId] = status
            self.Events.evtActivityDataUpdateGeneral.Invoke("KEY_Subscribe to updates", eventId, status)
        else
            return self._reddotSubscribe[eventId]
        end
    end
end

---------------------------------------------方舟商城--------------------------------------------------------
function ActivityServer:SendFangzhouCollect(activity_id, exchange_id, is_focus)
    local function OnFunc(res)
        if res.result then
            local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(activity_id)
            if activityInfo then
                for index, item in ipairs(activityInfo.prop_exchange_items or {}) do
                    if item.exchange_id == exchange_id then
                        item.is_focus = is_focus
                        break
                    end
                end
                self:UpdateServerActivityInfoList({activityInfo})
            end
            self.Events.evtActivityDataUpdateGeneral:Invoke("KEY_"..(activity_id or "")..EActSubInteractive.Next1, exchange_id)
        end
        self.Events.evtActivityDataUpdateGeneral:Invoke("KEY_ErrorTipIfNeeded", res)
    end
    if activity_id and exchange_id then
        local Req = pb.CSActivityFocusExchangeReq:New()
        Req.activity_id = activity_id
        Req.exchange_id = exchange_id
        Req.is_focus = is_focus
        Req:Request(OnFunc, {bEnableHighFrequency = true})--取消高频请求拦截
    end
end

function ActivityServer:SendFangzhouExchange(activity_id, exchange_id, plan_id, times)
    local function OnFunc(res)
        if res.result == 0 then
            self:UpdateServerActivityInfoList(res.activity_infos, res.currency_infos)--更新活动/货币
            self.Events.evtActivityDataUpdateGeneral:Invoke("KEY_"..(activity_id or "")..EActSubInteractive.Next2, res)
        end
        self.Events.evtActivityDataUpdateGeneral:Invoke("KEY_ErrorTipIfNeeded", res)
    end
    if activity_id and exchange_id and plan_id then
        local Req = pb.CSActivityPropExchangeReq:New()
        Req.activity_id = activity_id
        Req.exchange_id = exchange_id
        Req.plan_id = plan_id
        Req.time = times or 1
        Req:Request(OnFunc, {bEnableHighFrequency = true})
    end
end

-------------------------------------------------方舟招募--------------------------------------------------------
--- 方舟发布招募
function ActivityServer:SendArkRecruitPostReq(activityID, type, ad)
    local function OnCSActivityArknightsRecruitRes(res)
        if res.result == 0 then
            self:UpdateServerActivityInfoList({res.activity_info}, res.currency_infos)
            self.Events.evtActivityInfoUpdate:Invoke(activityID)
        else
            return
        end
    end
    
    local Req = pb.CSActivityArknightsRecruitReq:New()
    
    Req.activity_id = activityID
    Req.type = type
    Req.recruit_ad = ad
    Req:Request(OnCSActivityArknightsRecruitRes)
end

--- 方舟刷新广告
function ActivityServer:SendArkRecruitRefreshAdReq(activityID, type, fCallback)
    local function FilterById(filterTable, sourceTable)
        --- 创建查询哈希表
        local lookup = {}
        for _, id in ipairs(filterTable) do
            lookup[id] = true  -- 哈希表存储，O(1)时间复杂度查询
        end
        --- 根据哈希表寻找指定id元素
        local outTable = {}
        for i, v in ipairs(sourceTable) do
            if lookup[v.id] then
                table.insert(outTable, v)
            end
        end
        return outTable
    end

    local function OnCSActivityArknightsRefreshAdRes(res)
        if res.result == 0 then
            self:UpdateServerActivityInfoList({res.activity_info}, res.currency_infos)
            self.Events.evtActivityInfoUpdate:Invoke(activityID)
            if fCallback then
                local newADs = res.activity_info.arknights_recruit_info.board_infos[type].candidate_recruit_ads
                local adInfos = FilterById(newADs, res.activity_info.arknights_recruit_info.ad_info)
                
                fCallback(adInfos)
            end
        else
            return
        end
    end
    
    local Req = pb.CSActivityArknightsRefreshAdReq:New()
    
    Req.activity_id = activityID
    Req.type = type
    Req:Request(OnCSActivityArknightsRefreshAdRes)
end

--- 方舟选择干员奖励
function ActivityServer:SendArkRecruitChooseHeroReq(activityID, type, heroId, fCallback)
    local function OnCSActivityArknightsChooseHeroRes(res)
        if res.result == 0 then
            self:UpdateServerActivityInfoList({res.activity_info}, res.currency_infos)
            self.Events.evtActivityTaskChange:Invoke(activityID)
            if fCallback then
                local heroinfos = res.activity_info.arknights_recruit_info.hero_infos
                local heroinfo = {}
                for index, hero in ipairs(heroinfos) do
                    if hero.id == heroId then
                        heroinfo = hero
                        break
                    end
                end
                fCallback(res.data_change, res.expand_info, heroinfo)
            end
        else
            return
        end
    end
    
    local Req = pb.CSActivityArknightsChooseHeroReq:New()
    
    Req.activity_id = activityID
    Req.type = type
    Req.hero_id = heroId
    Req:Request(OnCSActivityArknightsChooseHeroRes)
end

--- 方舟领取图鉴奖励
function ActivityServer:SendArkRecruitBookAwardReq(activityID, bIsAll, collectId)
    local function OnCSActivityArknightsReceiveCollectRewardRes(res)
        if res.result == 0 then
            self:UpdateServerActivityInfoList({res.activity_info}, res.currency_infos)
            self.Events.evtActivityTaskReward:Invoke(res.data_change, res.expand_info)
            self.Events.evtActivityTaskChange:Invoke(activityID)
        else
            return
        end
    end
    
    local Req = pb.CSActivityArknightsReceiveCollectRewardReq:New()
    
    Req.activity_id = activityID
    Req.isAll = bIsAll or false
    Req.collect_id = collectId or 0
    Req:Request(OnCSActivityArknightsReceiveCollectRewardRes)
end

--- 方舟合成玉提示
function ActivityServer:SendArkRecruitMpStoneTipsReq(activityID, fCallBack)
    local function OnCSActivityArknightsUpdateMPMoneyNoticeRes(res)
        if res.result == 0 then
            if fCallBack then
                fCallBack(res.mp_money_update_time)
            end
        else
            return
        end
    end
    
    local Req = pb.CSActivityArknightsUpdateMPMoneyNoticeReq:New()
    
    Req.activity_id = activityID
    Req:Request(OnCSActivityArknightsUpdateMPMoneyNoticeRes)
end

--- 方舟红点
function ActivityServer:IsArkRecruitRedDot(activityID, recruit_info)
    return self:IsArkRecruitRedDot_Recruit(recruit_info.board_infos)
       or self:IsArkRecruitRedDot_Book(recruit_info.hero_infos, recruit_info.collect_infos)
end

function ActivityServer:IsArkRecruitRedDot_Recruit(board_infos)
    for index, value in ipairs(board_infos) do
        if value.state == 2 then
            return true
        end
    end
    return false
end

function ActivityServer:IsArkRecruitRedDot_Book(heroInfo, collectionInfo)
    local heroIsFullTable = {}
    for index, hero in ipairs(heroInfo) do
        heroIsFullTable[hero.id] = hero.level == 3
    end

    local bReddot = false
    for index, collection in ipairs(collectionInfo) do
        local bReceive = not collection.received
        for i, id in ipairs(collection.hero_ids) do
            if not heroIsFullTable[id] then
                bReceive = false
            end
        end
        bReddot = bReddot or bReceive
    end

    return bReddot
end

------------------------------------------------- 方舟招募以上 --------------------------------------------------------

--阿萨拉巡旅(点赞)
function ActivityServer:SendTaraLikeReq(activityID, message_id)
    local function OnFunc(res)
        if res.result == 0 then
            --金币整理
            if res.activity_info then
                res.activity_info["gold_id"]  = res.gain_currency_id
                res.activity_info["gold_num"] = res.gain_currency_num
            end
            self:UpdateServerActivityInfoList({res.activity_info}, res.currency_infos)
            --刷新当前干员
            self.Events.evtActTaraTrainingTravel:Invoke(true)
        end
    end
    local Req = pb.CSActivityAhsarahTravelLikeReq:New()
    Req.activity_id = activityID
    Req.message_id  = message_id
    Req:Request(OnFunc)
end

--阿萨拉巡旅(评论)
function ActivityServer:SendTaraCommentReq(activityID, message_id, reply_id, player_reply_id)
    local function OnFunc(res)
        if res.result == 0 then
            self:UpdateServerActivityInfoList({res.activity_info}, res.currency_infos)
            --刷新当前干员
            self.Events.evtActTaraTrainingTravel:Invoke(true)
        end
    end
    local Req = pb.CSActivityAhsarahTravelReplyReq:New()
    Req.activity_id = activityID
    Req.message_id  = message_id
    Req.reply_id    = reply_id or 0
    Req.player_reply_id = player_reply_id
    Req:Request(OnFunc)
end

--阿萨拉巡旅(切换干员)
function ActivityServer:SendTaraSwitchReq(activityID, hero_id)
    local function OnFunc(res)
        if res.result == 0 then
            self:UpdateServerActivityInfoList({res.activity_info}, res.currency_infos)
            --刷新整个界面
            self.Events.evtActTaraTrainingTravel:Invoke()
        end
    end
    local Req = pb.CSActivityAhsarahTravelTrackReq:New()
    Req.activity_id = activityID
    Req.hero_id  = hero_id
    Req:Request(OnFunc)
end

--阿萨拉巡旅(领取回复奖励)
function ActivityServer:SendTaraMinRewardReq(activityID, reply_id)
    local function OnFunc(res)
        if res.result == 0 then
            self:UpdateServerActivityInfoList({res.activity_info}, res.currency_infos)
            --通用领奖
            self.Events.evtActivityTaskReward:Invoke(res.data_change, res.expand_info)
            self.Events.evtActTaraTrainingTravel:Invoke(true)
        end
    end
    local Req = pb.CSActivityAhsarahTravelReceiveReplyRewardReq:New()
    Req.activity_id = activityID
    Req.reply_id    = reply_id
    Req:Request(OnFunc)
end

--阿萨拉巡旅(领取干员线奖励)
function ActivityServer:SendTaraMaxRewardReq(activityID, hero_id)
    local function OnFunc(res)
        if res.result == 0 then
            self:UpdateServerActivityInfoList({res.activity_info}, res.currency_infos)
            --通用领奖
            self.Events.evtActivityTaskReward:Invoke(res.data_change, res.expand_info)
            self.Events.evtActTaraTrainingTravel:Invoke(true)
        end
    end
    local Req = pb.CSActivityAhsarahTravelReceiveLineRewardReq:New()
    Req.activity_id = activityID
    Req.hero_id     = hero_id
    Req:Request(OnFunc)
end

------------------------------------------------- 博士游戏以下 --------------------------------------------------------
--#region

--- 博士游戏局外领取奖励
function ActivityServer:SendMorgenGetAwardReq(activityID, cycle, fCallBack)
    local function OnCSMorgenGetAwardRes(res)
        if res.result == 0 then
            if fCallBack then
                fCallBack(res.activity_info.arknights_recruit_info.match_records)
            end
            self:UpdateServerActivityInfoList({res.activity_info}, res.currency_infos)
            self.Events.evtActivityTaskReward:Invoke(res.data_change, res.expand_info)
            self.Events.evtActivityTaskChange:Invoke(activityID)
        else
            return
        end
    end
    
    local Req = pb.CSActivityArknightsGameReceiveCycleRewardReq:New()
    
    Req.activity_id = activityID
    Req.cycle = cycle
    Req:Request(OnCSMorgenGetAwardRes)
end

--- 博士游戏快速探索 开始
function ActivityServer:SendMorgenExploreStartReq(activityID, fCallBack)
    local function OnCSActivityArknightsGameExploreStartRes(res)
        if res.result == 0 then
            if fCallBack then
                fCallBack(res.records)
            end
        else
            return
        end
    end
    
    local Req = pb.CSActivityArknightsGameExploreStartReq:New()
    
    Req.activity_id = activityID
    Req:Request(OnCSActivityArknightsGameExploreStartRes)
end

--- 博士游戏快速探索 结束
function ActivityServer:SendMorgenExploreEndReq(activityID, event_id)
    local function OnCSActivityArknightsGameExploreEndRes(res)
        if res.result == 0 then
            self:UpdateServerActivityInfoList({res.activity_info}, res.currency_infos)
            self.Events.evtActivityTaskReward:Invoke(res.data_change, res.expand_info)
            self.Events.evtActivityTaskChange:Invoke(activityID)
        else
            return
        end
    end
    
    local Req = pb.CSActivityArknightsGameExploreEndReq:New()
    
    Req.activity_id = activityID
    Req.event_id = event_id
    Req:Request(OnCSActivityArknightsGameExploreEndRes)
end

--#endregion
------------------------------------------------- 博士游戏以上 --------------------------------------------------------

return ActivityServer
