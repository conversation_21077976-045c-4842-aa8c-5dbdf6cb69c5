----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMQuest)
----- LOG FUNCTION AUTO GENERATE END -----------
local IVCommonItemTemplate = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.IVCommonItemTemplate"
local QuestSeasonCollectorItem = require "DFM.Business.Module.QuestModule.UI.Season.Collection.QuestSeasonCollectorItem"
local QuestLogic = require "DFM.Business.Module.QuestModule.QuestLogic"

-- BEGIN MODIFICATION @ VIRTUOS
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

local EComp = Module.CommonWidget.Config.EIVWarehouseTempComponent
---@class QuestSeasonFactContractPanel : LuaUIBaseView
local QuestSeasonFactContractPanel = ui("QuestSeasonFactContractPanel")

function QuestSeasonFactContractPanel:Ctor()
    self._wtFateCarouselProV2 =
        UIUtil.WndCarouselProV2_Scroll(
        self,
        "DFExCommonCarouselProV2_34",
        self._OnGetCarouselV2ItemCount,
        self._OnProcessCarouselV2ItemWidget,
        self._OnCarouselV2IndexChanged
    )
    self._wtFateCarouselProV2:Event("OnPageClicked", self._OnCarouselV2ItemClicked, self)

    self._wtRightBtn = self:Wnd("RightBtn", UIButton)
    self._wtLeftBtn = self:Wnd("LeftBtn", UIButton)
    self._wtRightBtn:Event("OnClicked", self._OnSwitchBtnClicked, self, 1)
    self._wtLeftBtn:Event("OnClicked", self._OnSwitchBtnClicked, self, -1)

    self._wtTipBtn = self:Wnd("wtTipsBtn", DFCommonButtonOnly)
    self._wtTipBtn:SetCallback(self._OnTipBtnClicked, self)

    self._wtProgressTB = self:Wnd("Text_1", UITextBlock)

    --- 立绘左下角滚动按钮
    self._wtCarousel1 = self:Wnd("WBP_DFCommonButtonCarousel_1", UIWidgetBase)
    self._wtCarousel2 = self:Wnd("WBP_DFCommonButtonCarousel_2", UIWidgetBase)
    self._wtCarousel3 = self:Wnd("WBP_DFCommonButtonCarousel_3", UIWidgetBase)
    self._wtCarousel4 = self:Wnd("WBP_DFCommonButtonCarousel_4", UIWidgetBase)
    self._wtCarousel5 = self:Wnd("WBP_DFCommonButtonCarousel_5", UIWidgetBase)
    self._wtCarousel6 = self:Wnd("WBP_DFCommonButtonCarousel_6", UIWidgetBase)
    self._wtCarousel7 = self:Wnd("WBP_DFCommonButtonCarousel_7", UIWidgetBase)
    self.CarouselBtns = {}

   for i = 1, 7, 1 do
        table.insert(self.CarouselBtns, self["_wtCarousel" .. tostring(i)])
        self["_wtCarousel" .. tostring(i)]:Wnd("DFButton_Icon", UIButton):Event("OnClicked", self.OnHandleClicked, self, i)
   end

    self._selectedIndex = 1
end

function QuestSeasonFactContractPanel:OnHandleClicked(pos)
    self._wtFateCarouselProV2:SetTargetPageOnce(pos - 1)
end

function QuestSeasonFactContractPanel:_OnCarouselV2ItemClicked(carouselProWidget, itemWidget, index)
    if itemWidget then
        itemWidget:_OnQuestBtnClicked()
    end
end


function QuestSeasonFactContractPanel:OnInitExtraData()
    if DFHD_LUA == 1 then
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.DefaultSecondary)
    else
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.HideInfo)
    end
    Module.CommonBar:RegStackUITopBarTitle(self, Module.Quest.Config.Loc.QuestSeasonContract)
end

------------------------------------ Override function ------------------------------------
-- UI打开时触发
function QuestSeasonFactContractPanel:OnOpen()
    Server.QuestServer._questFactData:InitFact()
    self._wtFateCarouselProV2:RefreshAllItems()
    self:_AddEventListener()
end

function QuestSeasonFactContractPanel:OnShowBegin()
    -- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self:_InitGamepadInputs()
    end
    -- END MODIFICATION
end

-- BEGIN MODIFICATION @ VIRTUOS
function QuestSeasonFactContractPanel:OnHideBegin()
    if IsHD() then
        self:_DisableGamepadInputs()
    end
end
-- END MODIFICATION

function QuestSeasonFactContractPanel:OnShow()
    self:_OnRefreshView()
end

function QuestSeasonFactContractPanel:OnHide()
end

-- UI关闭时触发
function QuestSeasonFactContractPanel:OnClose()
    self:_RemoveEventListener()
end
------------------------------------ Private function ------------------------------------
function QuestSeasonFactContractPanel:_AddEventListener()
    self:AddLuaEvent(Server.QuestServer.Events.evtUpdateQuestState, self._OnQuestStateUpdate, self)
end

function QuestSeasonFactContractPanel:_OnQuestStateUpdate(questId)
    for index, value in ipairs(Server.QuestServer._questFactData.factQuestInfo) do
        if questId == value.Quest then
            self:_OnRefreshView()
            break
        end
    end
end

function QuestSeasonFactContractPanel:_RemoveEventListener()
    self:RemoveLuaEvent(Server.QuestServer.Events.evtUpdateQuestState)
end

function QuestSeasonFactContractPanel:_OnRefreshView()
    local count = 0
    self._selectedIndex = 1
    for index, value in ipairs(Server.QuestServer._questFactData.factQuestInfo) do
        local questInfo = Server.QuestServer:GetQuestInfoById(value.Quest)
        if questInfo and questInfo.state >= QuestState.Rewarded then
            count = count + 1
        end
        if questInfo and questInfo.state < QuestState.Rewarded and questInfo.state >= QuestState.Accepted then
            self._selectedIndex = index
        end
    end

    self._wtProgressTB:SetText(
        string.format(
            Module.Quest.Config.Loc.QuestSeasonContractProgress,
            count,
            #Server.QuestServer._questFactData.factQuestInfo
        )
    )
    if self._selectedIndex ~= 1 then
        self._wtFateCarouselProV2:SetTargetPageOnce(self._selectedIndex - 1)
    end
end

function QuestSeasonFactContractPanel:_OnGetCarouselV2ItemCount()
    return #Server.QuestServer._questFactData.factQuestInfo
end

--index从0开始
function QuestSeasonFactContractPanel:_OnProcessCarouselV2ItemWidget(carouselProWidget, itemWidget, index)
    itemWidget:SetInfo(Server.QuestServer._questFactData.factQuestInfo[index + 1])
end

--index从0开始
function QuestSeasonFactContractPanel:_OnCarouselV2IndexChanged(carouselProWidget, itemWidget, preIdx, curIdx)
    loginfo("QuestSeasonFactContractPanel:_OnCarouselV2IndexChanged", preIdx, curIdx)
    self._selectedIndex = curIdx + 1
    self:_OnRefreshCarousel()
end

function QuestSeasonFactContractPanel:_OnRefreshCarousel()
    for key, value in ipairs(self.CarouselBtns) do
        if key == self._selectedIndex then
            value:SetStyle(1)
        else
            value:SetStyle(0)
        end
    end
end

function QuestSeasonFactContractPanel:_OnSwitchBtnClicked(indexOffset)
    if indexOffset > 0 then
        self._wtFateCarouselProV2:NextPage()
    else
        self._wtFateCarouselProV2:PreviousPage()
    end
end

function QuestSeasonFactContractPanel:_OnTipBtnClicked()
    Module.Quest:OpenSeasonalQuestTutorial(Module.Quest.Config.EQuestSeasonGuideType.FateContract, nil)
end

-- BEGIN MODIFICATION @ VIRTUOS
function QuestSeasonFactContractPanel:_InitGamepadInputs()
    self._toggleNextTabHandle =
        self:AddInputActionBinding(
        "Common_SwitchToNextTab",
        EInputEvent.IE_Pressed,
        self._SwitchNextTab,
        self,
        EDisplayInputActionPriority.UI_Stack
    )
    self._togglePrevTabHandle =
        self:AddInputActionBinding(
        "Common_SwitchToPrevTab",
        EInputEvent.IE_Pressed,
        self._SwitchPrevTab,
        self,
        EDisplayInputActionPriority.UI_Stack
    )
end

function QuestSeasonFactContractPanel:_DisableGamepadInputs()
    self:RemoveInputActionBinding(self._toggleNextTabHandle)
    self._toggleNextTabHandle = nil
    self:RemoveInputActionBinding(self._togglePrevTabHandle)
    self._togglePrevTabHandle = nil
end

function QuestSeasonFactContractPanel:_SwitchNextTab()
    if self._wtRightBtn:IsVisible() then
        self:_OnSwitchBtnClicked(1)
    end
end

function QuestSeasonFactContractPanel:_SwitchPrevTab()
    if self._wtLeftBtn:IsVisible() then
        self:_OnSwitchBtnClicked(-1)
    end
end
-- END MODIFICATION

return QuestSeasonFactContractPanel
