----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaFResourceManagerStub)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class ResStub : Object
ResStub = class("ResStub", Object)
local RefUtil = require "DFM.YxFramework.Util.RefUtil"
local UKismetSystemLibrary = import "KismetSystemLibrary"

---------------------------------------------------------------------------------
--- 资源存根: 强引用住资源列表
---------------------------------------------------------------------------------
local ResStubIdSeed = 0
function ResStub.GenResStubId()
    ResStubIdSeed = ResStubIdSeed + 1
    return ResStubIdSeed
end

--- ResStub资源存根
local weakmeta = {__mode = "kv"}
function ResStub:Ctor(stubName, bWeakRef)
    self.bWeakRef = setdefault(bWeakRef, false)
    ---private
    if self.bWeakRef then
        self._mapPath2ResRefs = setmetatable({}, weakmeta_value)
    else
        self._mapPath2ResRefs = {}
    end
    ---public
    self.stubName = stubName
    self.stubId = ResStub.GenResStubId()
    
    local GUILowMemoryEnable = UKismetSystemLibrary.GetConsoleVariableIntValue("r.GUILowMemoryEnable")
    self.bLowMemoryState = GUILowMemoryEnable == 1
end

function ResStub:GetResRefs()
    return self._mapPath2ResRefs
end

---@param resPath string
---@param resInst Object
function ResStub:AddResRef(resPath, resInst)
    --logframe("AddResRef()", "StubName=", self.stubName, "ResPath=", resPath, "ResInst=", resInst)
    if self.bLowMemoryState then
        if self._mapPath2ResRefs[resPath] ~= resInst then
            if isvalid(resInst) then
                RefUtil.AddRef(resInst)
                if not VersionUtil.IsShipping() then
                    logframe('[Low Memory Log - LayerResClear] ', resPath, resInst, 'AddRef in stub', self.stubName)
                end
            else
                if not VersionUtil.IsShipping() then
                    logwarning('[Low Memory Log - LayerResClear] ', resPath, resInst ,'AddRef ResInst is invalid, cannot add to stub', self.stubName)
                end
            end
        else
            if not VersionUtil.IsShipping() then
                logerror('[Low Memory Log - LayerResClear] ', resPath, resInst ,'AddRef ResInst already exists in stub, not adding again', self.stubName)
            end
        end
    end
    self._mapPath2ResRefs[resPath] = resInst
end

---@param resPath string
function ResStub:TryGetResByPath(resPath)
    local resInst = self._mapPath2ResRefs[resPath]
    if resInst and isvalid(resInst) then
        return resInst
    else
        -- logframe('[AsyncLoadCheck] No cache found in stub, start loading', self.stubName, resPath)
        self:ClearResRefByPath(resPath)
        return nil
    end
end

function ResStub:ClearAllResRefs()
    -- self:GetAllResSize()
    if self.bLowMemoryState then
        logwarning('[Low Memory Log - LayerResClear] ResStub:ClearAllResRefs() RefUtil.RemoveRef in stub', self.stubName)
        for resPath, resInst in pairs(self._mapPath2ResRefs) do
            if isvalid(resInst) then
                RefUtil.RemoveRef(resInst)
            end
        end
    end
    if self.bWeakRef then
        self._mapPath2ResRefs = setmetatable({}, weakmeta_value)
    else
        self._mapPath2ResRefs = {}
    end
    local GUILowMemoryEnable = UKismetSystemLibrary.GetConsoleVariableIntValue("r.GUILowMemoryEnable")
    self.bLowMemoryState = GUILowMemoryEnable == 1
end

function ResStub:ClearResRefByPath(resPath)
    -- SHE1 手动移除cppinst 在LuaState_3的弱引用
    if self.bLowMemoryState then
        local resInst = self._mapPath2ResRefs[resPath]
        if isvalid(resInst) then
            RefUtil.RemoveRef(resInst)
            if not VersionUtil.IsShipping() then
                logframe('[Low Memory Log - LayerResClear] ', resPath, resInst, 'RemoveRef in stub', self.stubName)
            end
        else
            if not VersionUtil.IsShipping() then
                -- logwarning('[Low Memory Log - LayerResClear] ', resPath, resInst, 'RemoveRef ResInst is invalid when ClearResRefByPath', self.stubName)
            end
        end
    end
    self._mapPath2ResRefs[resPath] = nil
end

function ResStub:ClearResRefsByCondition(fConditionCheck)
    for resPath, resInst in pairs(self._mapPath2ResRefs) do
        if fConditionCheck and fConditionCheck(resPath, resInst) then
            self:ClearResRefByPath(resPath)
        end
    end
end

function ResStub:GetAllResSize(sizeMode)
    sizeMode = setdefault(sizeMode, EResourceSizeMode.EstimatedTotal)
    local insSize = 0
    local totalSize = 0
    for resPath, resInst in pairs(self._mapPath2ResRefs) do
        insSize = Facade.ResourceManager:GetResourceSizeBytes(resInst, sizeMode)
        totalSize = totalSize + insSize
    end
    logframe('ResStub:GetAllResSize()', self.stubName, 'TotalSize:', totalSize, 'bytes')
    return totalSize
end


function ResStub:Destroy()
    self:ClearAllResRefs()
end

return ResStub