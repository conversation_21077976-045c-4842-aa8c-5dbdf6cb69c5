----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMQuest)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class QuestUnlockItem : LuaUIBaseView
local QuestUnlockItem = ui("QuestUnlockItem")

function QuestUnlockItem:Ctor()
    self._timerHandle = nil
    self.WBP_TaskSystemCommonQuestType = self:Wnd("WBP_TaskSystemCommonQuestType", UIWidgetBase)
    self._wtDFTextBlock_QuestType = self.WBP_TaskSystemCommonQuestType:Wnd("DFTextBlock_QuestType", UITextBlock)

    self._wtDFTextBlock_QuestName = self:Wnd("DFTextBlock_QuestName", UITextBlock)

    self._wtDFTextBlock_UnLockTips = self:Wnd("DFTextBlock_UnLockTips", UITextBlock)

    self._wtBgImg = self:Wnd("DFImage_QuestBg", UIImage)

    self._wtLockPanel = self:Wnd("DFCanvasPanel_Lock", UIWidgetBase)

    self._wtSeasonStarPanel = self:Wnd("DFHorizontalBox_77", UIWidgetBase)
	self._wtStarNum = self:Wnd("DFTextBlock_182", UITextBlock)
	self._wtStarIcon_1 = self:Wnd("DFImage_1", UIWidgetBase)
	self._wtStarIcon_2 = self:Wnd("DFImage_2", UIWidgetBase)
	self._wtStarIcon_3 = self:Wnd("DFImage_3", UIWidgetBase)
	self._wtStarIcon_4 = self:Wnd("DFImage_4", UIWidgetBase)
	self._wtStarIcon_5 = self:Wnd("DFImage_5", UIWidgetBase)
	self._starIconList = {
		self._wtStarIcon_1,
		self._wtStarIcon_2,
		self._wtStarIcon_3,
		self._wtStarIcon_4,
		self._wtStarIcon_5
	}
end

------------------------------------ Override function ------------------------------------
function QuestUnlockItem:OnInitExtraData(questInfo, idx)
    if questInfo == nil then
        logerror(" QuestUnlockItem OnInitExtraData : questInfo is nil ")
        return
    end
    self._questInfo = questInfo
end

function QuestUnlockItem:OnOpen()
    -- print( "xhz >>>>>>>>>>>>> QuestUnlockItem OnOpen >>>>>>>>>>>>>>>> " )
    if self._questInfo then
        self:_UpdateQuestInfo()
    end
end

function QuestUnlockItem:OnClose()
    -- print( "xhz >>>>>>>>>>>>> QuestUnlockItem OnClose >>>>>>>>>>>>>>>> " )
    if self._timerHandle then
        self._timerHandle:Release()
        self._timerHandle = nil
    end
end

function QuestUnlockItem:OnShow()
end

function QuestUnlockItem:OnHide()
end

function QuestUnlockItem:_UpdateSeasonCountTime()
    self._wtDFTextBlock_UnLockTips:SetText("")
    local day, hour, min, sec = TimeUtil.GetSecondsFormatDDHHMMSS(self._questInfo:GetRemainToAcceptTime())
    if day > 0 then
        self._wtDFTextBlock_UnLockTips:SetText(
            string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeDay, day, hour, min)
        )
    elseif hour > 0 then
        self._wtDFTextBlock_UnLockTips:SetText(
            string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeHour, hour, min)
        )
    elseif min > 0 then
        self._wtDFTextBlock_UnLockTips:SetText(
            string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeMin, sec > 0 and min + 1 or min)
        )
    elseif sec > 0 then
        self._wtDFTextBlock_UnLockTips:SetText(string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeMin, 1))
    else
        if sec <= 0 then
            if self._timerHandle then
                self._timerHandle:Release()
                self._timerHandle = nil
            end
            self:_UpdateQuestInfo()
        end
    end
end

function QuestUnlockItem:_UpdateQuestInfo()
    if self._timerHandle then
        self._timerHandle:Release()
        self._timerHandle = nil
    end
    self:SetLock(false)
    self.WBP_TaskSystemCommonQuestType:SetFontType(0)
    if self._questInfo.type == QuestType.Mission then
        self._wtDFTextBlock_QuestType:SetText(Module.Quest.Config.Loc.QuestTypeLocMission)
        self.WBP_TaskSystemCommonQuestType:SetColorType(2)
        self:SetColor(2)
    elseif self._questInfo.type == QuestType.ImportantQuest then
        self._wtDFTextBlock_QuestType:SetText(Module.Quest.Config.Loc.QuestTypeLocImportant)
        self.WBP_TaskSystemCommonQuestType:SetColorType(1)
        self:SetColor(1)
    elseif self._questInfo.type == QuestType.Branch then
        self._wtDFTextBlock_QuestType:SetText(Module.Quest.Config.Loc.QuestTypeLocBranch)
        self.WBP_TaskSystemCommonQuestType:SetColorType(3)
        self:SetColor(3)
    elseif self._questInfo.type == QuestType.SeasonQuest then
        self:_UpdateSeasonQuestInfo(self._questInfo)
    end

    self._wtDFTextBlock_QuestName:SetText(self._questInfo.name)

    if self._questInfo.questIcon and self._questInfo.questIcon ~= "" then
        local imagePath = self._questInfo.questIcon
        local imageAssetCache = Facade.UIManager.stubUIImageRes:TryGetResByPath(imagePath)
        if imageAssetCache == nil or not isvalid(imageAssetCache) then
            local fOnImageLoadFinished = function(mapPath2ResIns)
                -- local asset=mapPath2ResIns[imagePath]
                local _,asset=next(mapPath2ResIns)
                if asset and isvalid(asset) then
                    local MatIns=self._wtBgImg:GetDynamicMaterial()
                    if MatIns then
                        MatIns:SetTextureParameterValue("Texture_Main",asset)
                        logerror("ResImageUtil.AsyncLoadImgObjByPath fOnLoadFinishedCallback imageAsset")
                    end
                end
            end
            logerror("ResImageUtil.AsyncLoadImgObjByPath fOnLoadFinishedCallback start")
            Facade.ResourceManager:AsyncLoadResource(Facade.UIManager.stubUIImageRes, imagePath, fOnImageLoadFinished)
        else
            local MatIns=self._wtBgImg:GetDynamicMaterial()
            if MatIns then
                MatIns:SetTextureParameterValue("Texture_Main",imageAssetCache)
                logerror("ResImageUtil.AsyncLoadImgObjByPath fOnLoadFinishedCallback imageAssetcache")
            end
        end
    end

    if self._questInfo.state == QuestState.Unaccepted and Server.QuestServer:IsQuestAcceptable(self._questInfo) == false then
        if Server.RoleInfoServer.seasonLevel < self._questInfo.levelLimit then
            self._wtDFTextBlock_UnLockTips:SetText(
                string.format(Module.Quest.Config.Loc.LvlLimitTabPureTxt, self._questInfo.levelLimit)
            )
            self:SetLock(true)
        elseif self._questInfo:GetRemainToAcceptTime() > 0 then
            self:_UpdateSeasonCountTime()
            self._timerHandle = Timer:NewIns(5, 0)
            self._timerHandle:AddListener(self._UpdateSeasonCountTime, self)
            self._timerHandle:Start()
            self:SetLock(true)
        end
    end
end


function QuestUnlockItem:_UpdateSeasonQuestInfo(questInfo)

    local questlineInfo = Server.QuestServer:GetSeasonLineData(questInfo._seasonLineID)
	if questlineInfo then
        self._wtLockPanel:Collapsed()
        self._wtSeasonStarPanel:SelfHitTestInvisible()
		local groupInfo = questlineInfo:GetGroupInfo(questInfo._seasonGroupID)
		if groupInfo then
			-- set type text
			if questlineInfo:IsMainGroup(questInfo._seasonStageID, questInfo._seasonGroupID) then
				self._wtDFTextBlock_QuestType:SetText(Module.Quest.Config.Loc.QuestTypeLocSeasonMain)
				self.WBP_TaskSystemCommonQuestType:SetColorType(0)
                self:SetColor(0)
			else
				self._wtDFTextBlock_QuestType:SetText(Module.Quest.Config.Loc.QuestTypeLocSeasonBranch)
				self.WBP_TaskSystemCommonQuestType:SetColorType(4)
                self:SetColor(4)
			end

			-- Add group stars
			if groupInfo.rewardStar == 0 then 
				self._wtSeasonStarPanel:Collapsed()
			elseif groupInfo.rewardStar <= 5 then
				for i = 1, groupInfo.rewardStar, 1 do
					self._starIconList[i]:SelfHitTestInvisible()
				end
                for i = groupInfo.rewardStar + 1, 5, 1 do
					self._starIconList[i]:Collapsed()
				end
				self._wtStarNum:Collapsed()
			else
				self._wtStarIcon_1:SelfHitTestInvisible()
				self._wtStarIcon_2:Collapsed()
				self._wtStarIcon_3:Collapsed()
				self._wtStarIcon_4:Collapsed()
				self._wtStarIcon_5:Collapsed()
				self._wtStarNum:SetText(tostring(groupInfo.rewardStar))
			end

		else
			logerror("GroupInfo Invalid")
		end
	else
		logerror("Season line Info Invalid")
	end

end

return QuestUnlockItem
