----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCommonWidget)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class CommonWidgetModule : ModuleBase
local CommonWidgetModule = class("CommonWidgetModule", require("DFM.YxFramework.Managers.Module.ModuleBase"))

local ReorderableListDragArea = require "DFM.Business.Module.CommonWidgetModule.Dynamic.ReorderableListDragArea"
local ReorderableList = require "DFM.Business.Module.CommonWidgetModule.Dynamic.ReorderableList"
local CommonTabLogic = require "DFM.Business.Module.CommonWidgetModule.Logic.CommonTabLogic"
local NumComponentLogic = require "DFM.Business.Module.CommonWidgetModule.Logic.NumComponentLogic"
local ItemViewLogic = require "DFM.Business.Module.CommonWidgetModule.Logic.ItemViewLogic"
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local AutoScrollLogic = require "DFM.Business.Module.CommonWidgetModule.Logic.AutoScrollLogic"
local CommonWidgetLogic = require "DFM.Business.Module.CommonWidgetModule.Logic.CommonWidgetLogic"
local CommonDragDropLogic = require "DFM.Business.Module.CommonWidgetModule.Logic.CommonDragDropLogic"
local WeaponSelectionLogic = require "DFM.Business.Module.CommonWidgetModule.Logic.WeaponSelectionLogic"
local CommonIVInputLogic = require "DFM.Business.Module.CommonWidgetModule.Logic.CommonIVInputLogic"
local CommonVideoLogic   = require "DFM.Business.Module.CommonWidgetModule.Logic.CommonVideoLogic"
local UGameplayStatics = import "GameplayStatics"
local EViewDragMode = import "EViewDragMode"
local UGPUINavigationUtils = import_hd("GPUINavigationUtils")
local ULuaItemDragConfigUtil = import "LuaItemDragConfigUtil"
local UDFCommonMediaView = import "DFCommonMediaView"

---@class ItemDescData
---@field titleText string
---@field descText string


--BEGIN MODIFICATION @ VIRTUOS : 
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
--END MODIFICATION

local function log(...)
    loginfo("[CommonWidgetModule]", ...)
end

local function err(...)
    logerror("lyl", "[CommonWidgetModule]", ...)
end

---@return CommonWidgetField
local function getField()
    return Module.CommonWidget.Field
end

function CommonWidgetModule:Ctor()
end

function CommonWidgetModule:OnLoadModule()
    logwarning('CommonWidgetModule:OnLoadModule()')
    CommonWidgetLogic.BindCppEvents()
    CommonWidgetLogic.BindModuleEvents()
    CommonDragDropLogic.BindDragDropEvent()
    --- 预加载对外提供同步创建的[常驻]类型资源
    local loadingUIIdList = {
        UIName2ID.CommonCheckTab,
        UIName2ID.CommonCheckTabS1,
        UIName2ID.CommonEmptyContent,
        UIName2ID.ItemSimpleView,
        UIName2ID.CommonValidBox,
        UIName2ID.CommonInValidBox,
        -- UIName2ID.CommonMainTab,
        -- UIName2ID.CommonSubTab,
        -- UIName2ID.CommonMainTabLiveS1,
        -- UIName2ID.CommonSubTabLiveS1,
        
        UIName2ID.CommonMainTabLiveS2,
        UIName2ID.CommonSubTabLiveS2,

        UIName2ID.CommonMainTabLiveS2V1,
        UIName2ID.CommonSubTabLiveS2V1,
        UIName2ID.CommonTipsPlayerBtn,
        -- UIName2ID.IVWarehouseTemplate,
        -- UIName2ID.IVCommonItemTemplate,
        -- UIName2ID.IVCommonItemTemplateMini,
        -- UIName2ID.IVShopItemTemplate,
        UIName2ID.CommonTipsPlayerBtn,
        UIName2ID.DragItemPreview,

        UIName2ID.CommonWeaponSkinMissionProgressItem,
    }
    
    Facade.UIManager:PreloadPermanentUIAssetList(loadingUIIdList)
    if DFHD_LUA == 1 and UGPUINavigationUtils.IsNavigationEnabled()then
        local loadingHDUIIdList = {
            UIName2ID.CommonNavigationSelectorHD,
        }
        Facade.UIManager:PreloadPermanentUIAssetList(loadingHDUIIdList)

        Facade.UIManager:RegisterNavigationSelectorUI(UIName2ID.CommonNavigationSelectorHD)
    end
end

function CommonWidgetModule:OnUnloadModule()
    logwarning('CommonWidgetModule:OnUnloadModule()')

    CommonWidgetLogic.UnBindCppEvents()
    CommonWidgetLogic.UnBindModuleEvents()
    CommonDragDropLogic.UnbindDragDropEvent()
end

function CommonWidgetModule:OnDestroyModule()
end

function CommonWidgetModule:OnGameFlowChangeEnter(gameFlowType)
    if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then
        Facade.UIManager.Events.evtStackUIChanged:AddListener(CommonDragDropLogic.ProcessOnStackUIPop)
    end
end

function CommonWidgetModule:OnGameFlowChangeLeave(gameFlowType)
    getField():ClearDragItemView()
    CommonWidgetLogic.RemoveItemScrollCmd()
    if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then
        Facade.UIManager.Events.evtStackUIChanged:RemoveListener(CommonDragDropLogic.ProcessOnStackUIPop)
    end
end

--------------------------------------------------------------------------
--- Public API
--------------------------------------------------------------------------
---@param btnText string
---@param fCallBack function
---@param styleInt number
function CommonWidgetModule:CreateCommonCheckTabS1(owner, parent, instanceId, btnText, styleInt, fCallBack)
    log("CommonWidgetModule:CreateCommonCheckTabS1")
    return Facade.UIManager:AddSubUI(owner, UIName2ID.CommonCheckTabS1, parent, instanceId, btnText, styleInt, fCallBack)
end

--------------------------------------------------------------------------
--- Tabs
--------------------------------------------------------------------------
--- 创建一组成Group的单选Tabs
---@param params TabComponentParams
function CommonWidgetModule:CreateTabComponent(params)
    log("CommonWidgetModule:CreateTabComponent")
    return CommonTabLogic.CreateTabComponentProcess(params)
end

--- 创建一个单独的Tab
---@param params CommonTabParams
function CommonWidgetModule:CreateCommonTab(params)
    log("CommonWidgetModule:CreateCommonTab")
    return CommonTabLogic.CreateCommonTabProcess(params)
end

---@param params NumSelectionParams
function CommonWidgetModule:CreateNumSlider(params)
    log("CommonWidgetModule:CreateNumSlider")
    return NumComponentLogic.CreateNumSliderProcess(params)
end

---@param params AutoScrollParams
---@return AutoScrollComponent
function CommonWidgetModule:CreateScrollComponent(params)
    log("CommonWidgetModule:CreateScrollComponent")
    return AutoScrollLogic.CreateScrollComponentProcess(params)
end

--------------------------------------------------------------------------
--- Live Main Tab & Sub Tab
--------------------------------------------------------------------------
function CommonWidgetModule:CreateMainTabLiveS2(owner, parent, instanceId, ...)
    log("CommonWidgetModule:CreateMainTabLiveS2")
    return Facade.UIManager:AddSubUI(owner, UIName2ID.CommonMainTabLiveS2, parent, instanceId, ...)
end

function CommonWidgetModule:CreateSubTabLiveS2(owner, parent, instanceId, ...)
    log("CommonWidgetModule:CreateSubTabLiveS2")
    return Facade.UIManager:AddSubUI(owner, UIName2ID.CommonSubTabLiveS2, parent, instanceId, ...)
end

function CommonWidgetModule:CreateMainTabLiveS2V1(owner, parent, instanceId, ...)
    log("CommonWidgetModule:CreateMainTabLiveS2V1")
    return Facade.UIManager:AddSubUI(owner, UIName2ID.CommonMainTabLiveS2V1, parent, instanceId, ...)
end

function CommonWidgetModule:CreateSubTabLiveS2V1(owner, parent, instanceId, ...)
    log("CommonWidgetModule:CreateSubTabLiveS2V1")
    return Facade.UIManager:AddSubUI(owner, UIName2ID.CommonSubTabLiveS2V1, parent, instanceId, ...)
end

--------------------------------------------------------------------------
--- ItemView
--------------------------------------------------------------------------
--- 创建通用道具简单ItemView, 点击弹出默认Tips
---@class SimpleItemInfo table
---@field itemId number
---@field itemBottomText string optional
---@field itemBottomTextColor FSlateColor optional

---@param info SimpleItemInfo
function CommonWidgetModule:CreateSimpleItemView(owner, parent, instanceId, info)
    return Facade.UIManager:AddSubUI(owner, UIName2ID.ItemSimpleView, parent, instanceId, info)
end

--- 创建通用道具简单ItemView, 点击绑定回调,
---@param info SimpleItemInfo
---@param callback function
---@param caller ins
function CommonWidgetModule:CreateSimpleItemView_CustomClick(owner, parent, instanceId, info, callback, caller)
    local weakUIIns, instanceId = Facade.UIManager:AddSubUI(owner, UIName2ID.ItemSimpleView, parent, instanceId, info)
    local itemViewIns = getfromweak(weakUIIns)
    itemViewIns:AddCustomHandler(callback, caller)
    return weakUIIns, instanceId
end
-----------------------------------------------------------------------
--region 选择模式
function CommonWidgetModule:SetItemOperMode(operaMode)
    ItemViewLogic.SetItemOperMode(operaMode)
end

function CommonWidgetModule:IsSwitchWeightMode()
    return getField():IsSwitchWeightMode()
end
function CommonWidgetModule:ResetSwitchWeightMode()
    getField():ResetSwitchWeightMode()
end

function CommonWidgetModule:SwitchWeightState(bSwitch)
    return getField():SwitchWeightState(bSwitch)
end

function CommonWidgetModule:GetItemOperMode()
    return getField():GetItemOperMode()
end

function CommonWidgetModule:ResetAllItemSelection()
    getField():SetSingleSelectedItem(nil)
    getField():ResetMultiSelectedItem()
end

function CommonWidgetModule:GetIsItemSellMode()
    return getField():GetItemOperMode() == EItemOperaMode.SellMode
end

function CommonWidgetModule:GetIsItemGiftMode()
    return getField():GetItemOperMode() == EItemOperaMode.GiftMode
end
--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region 道具单选
function CommonWidgetModule:SingleSelectItem(item, bForceSelected)
    ItemViewLogic.SingleSelectItem(item, bForceSelected)
end

function CommonWidgetModule:CancelItemSingleSelect()
    ItemViewLogic.CancelItemSingleSelect()
end

function CommonWidgetModule:CheckItemSingleSelect(item)
    return getField():GetSingleSelectedItem() == item
end

function CommonWidgetModule:GetItemSelected()
    return getField():GetSingleSelectedItem()
end

function CommonWidgetModule:GetInGameSelectedItem()
    return getField():GetInGameSelectedItem()
end

function CommonWidgetModule:SetInGameSelectedItem(inGameSelectedItem)
    return getField():SetInGameSelectedItem(inGameSelectedItem)
end

function CommonWidgetModule:CheckItemInGameSelect(inGameSelectedItem)
    return getField():CheckItemInGameSelect(inGameSelectedItem)
end

function CommonWidgetModule:ToggleDragItemSpin()
    CommonWidgetConfig.Events.evtToggleDragItemSpin:Invoke()
end
--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region 道具多选
function CommonWidgetModule:SetSelectedMode(bInMultipleMode)
    ItemViewLogic.SetSelectedMode(bInMultipleMode)
end

function CommonWidgetModule:SetItemMultiSelected(item, bSelected, bFromOnClick)
    ItemViewLogic.SetItemMultiSelected(item, bSelected)
end

function CommonWidgetModule:ResetItemMultiSelection()
    ItemViewLogic.ResetItemMultiSelection()
end

function CommonWidgetModule:CheckItemMultiSelected(item)
    return ItemViewLogic.CheckItemMultiSelected(item)
end

--- 获取一个表示当前多选的Items的数组列表
function CommonWidgetModule:GetSelectedItems()
    return table.keys(getField():GetMultiSelectedItems())
end

--- 获取一个Item为key，bool为value的Map，表示当前多选的Items
function CommonWidgetModule:GetMultiSelectedItems()
    return getField():GetMultiSelectedItems()
end

function CommonWidgetModule:IsInMultiSelectedMode()
    return getField():GetInMultiSelectedMode()
end
--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region 道具抓取模式

function CommonWidgetModule:StartGrab(grabItemView)
    ItemViewLogic.StartGrabLogic(grabItemView)
end

function CommonWidgetModule:StopGrab()
    ItemViewLogic.StopGrabLogic()
end

function CommonWidgetModule:IsInGrabMode()
    return getField():GetInGrabMode()
end

function CommonWidgetModule:GetCurGrabItem()
    return getField():GetCurGrabItem()
end

--endregion
-----------------------------------------------------------------------

function CommonWidgetModule:GetIsItemShowPrice()
    return getField():GetGlobalItemShowPrice()
end

function CommonWidgetModule:GetIsItemShowWeight()
    return getField():GetGlobalItemShowWeigth()
end

function CommonWidgetModule:GetIsItemShowDoubleClickHint()
    return getField():GetGlobalItemShowDoubleClickHint()
end

function CommonWidgetModule:SetAuctionMode(bInAuctionMode)
    return getField():SetAuctionMode(bInAuctionMode)
end

function CommonWidgetModule:IsAuctionMode()
    return getField():IsAuctionMode()
end

function CommonWidgetModule:IsSettlementMode()
    return getField():IsSettlementMode()
end

-----------------------------------------------------------------------
--region Global Drag Config

function CommonWidgetModule:GetMaxDragPointSampleNum()
    return getField():GetMaxDragPointSampleNum()
end

function CommonWidgetModule:SetMaxDragPointSampleNum(maxDragPointSampleNum)
    if type(maxDragPointSampleNum) == "string" then
        maxDragPointSampleNum = tonumber(maxDragPointSampleNum)
    end

    if type(maxDragPointSampleNum) == "number" and maxDragPointSampleNum > 0 then
        getField():SetMaxDragPointSampleNum(maxDragPointSampleNum)
    end
end

function CommonWidgetModule:GetDragAngle()
    return getField():GetDragAngle()
end

function CommonWidgetModule:SetDragAngle(dragAngle)
    if type(dragAngle) == "string" then
        dragAngle = tonumber(dragAngle)
    end

    if type(dragAngle) == "number" and dragAngle > 0 then
        getField():SetDragAngle(dragAngle)
    end
end

function CommonWidgetModule:GetFastDragAngle()
    return getField():GetFastDragAngle()
end

function CommonWidgetModule:SetFastDragAngle(fastDragAngle)
    if type(fastDragAngle) == "string" then
        fastDragAngle = tonumber(fastDragAngle)
    end

    if type(fastDragAngle) == "number" and fastDragAngle > 0 then
        getField():SetFastDragAngle(fastDragAngle)
    end
end

function CommonWidgetModule:GetFastDragThreshold()
    return getField():GetFastDragThreshold()
end

function CommonWidgetModule:SetFastDragThreshold(fastDragThreshold)
    if type(fastDragThreshold) == "string" then
        fastDragThreshold = tonumber(fastDragThreshold)
    end

    if type(fastDragThreshold) == "number" and fastDragThreshold > 0 then
        getField():SetFastDragThreshold(fastDragThreshold)
    end
end

--endregion
-----------------------------------------------------------------------

function CommonWidgetModule:GetCurDragingItem()
    return getField():GetCurDragingItem()
end

function CommonWidgetModule:GetCurDragDropInfo()
    return getField():GetCurDragDropInfo()
end

function CommonWidgetModule:SetCurDragingItemView(view)
    return getField():SetCurDragingItemView(view)
end

---@return DragItemPreview
function CommonWidgetModule:GetOrCreateDragItemView()
    return getField():GetOrCreateDragItemView()
end
function CommonWidgetModule:ClearDragItemView()
    return getField():ClearDragItemView()
end
function CommonWidgetModule:PostNotifyDragEnter(enterView, inGeometry, inDragDropEvent, operation)
    CommonWidgetConfig.Events.evtGlobalItemPostDragEnter:Invoke(enterView, inGeometry, inDragDropEvent, operation)
end

function CommonWidgetModule:PostNotifyDragLeave(leaveView, inDragDropEvent, operation)
    CommonWidgetConfig.Events.evtGlobalItemPostDragLeave:Invoke(leaveView, inDragDropEvent, operation)
end

function CommonWidgetModule:IsUseCustomDragEnterTrigger()
    return getField().bUseCustomDragEnterTrigger
end

function CommonWidgetModule:SetUseCustomDragEnterTrigger(bUseCustomDragEnterTrigger)
    getField().bUseCustomDragEnterTrigger = bUseCustomDragEnterTrigger
end

function CommonWidgetModule:SetCustomDragEnterTrigger(bCustomDragEnterTrigger, view)
    if bCustomDragEnterTrigger ~= getField().bCustomDragEnterTrigger then
        getField().bCustomDragEnterTrigger = bCustomDragEnterTrigger
        Module.CommonWidget.Config.Events.evtCustomDragEnterOrLeave:Invoke(bCustomDragEnterTrigger, view)
    end
end

function CommonWidgetModule:IsCustomDragEnterTrigger()
    return getField().bCustomDragEnterTrigger
end

function CommonWidgetModule:RegisterCommonDragDrop(view)
    CommonDragDropLogic.RegisterView(view)
end

function CommonWidgetModule:UnregisterCommonDragDrop(view)
    CommonDragDropLogic.UnregisterView(view)
end

function CommonWidgetModule:RegisterAutoSnapArea(area)
    CommonDragDropLogic.RegisterAutoSnapArea(area)
end

function CommonWidgetModule:UnregisterAutoSnapArea(area)
    CommonDragDropLogic.UnregisterAutoSnapArea(area)
end

function CommonWidgetModule:ForceEnableDragDropBehavior(bEnable)
    CommonDragDropLogic.ForceEnableDragDropBehavior(bEnable)
end

function CommonWidgetModule:CancelDragDrop()
    CommonDragDropLogic.CancelDragDrop()
end

function CommonWidgetModule:SetAutoSnapEnabled(bEnable)
    getField():SetAutoSnapEnabled(bEnable)
end

function CommonWidgetModule:NotifyItemMoveFail(item, bShowFailAnim)
    CommonDragDropLogic.NotifyItemMoveFail(item, bShowFailAnim)
end

---@param ui LuaUIBaseView
function CommonWidgetModule:InitCommonDragConfig(ui, slotType)
    CommonDragDropLogic.InitCommonDragConfig(ui, slotType)
end

function CommonWidgetModule:LuaCall_OnItemDragged(pointerEvent, operation)
    CommonDragDropLogic.LuaCall_OnItemDragged(pointerEvent, operation)
end

function CommonWidgetModule:LuaCall_OnItemDrop(pointerEvent, operation)
    CommonDragDropLogic.LuaCall_OnItemDrop(pointerEvent, operation)
end

function CommonWidgetModule:LuaCall_OnItemDragCancelled(pointerEvent, operation)
    CommonDragDropLogic.LuaCall_OnItemDragCancelled(pointerEvent, operation)
end

-----------------------------------------------------------------------
--region Common Widget Components

---@param params ReorderableListParams
---@return ReorderableList
function CommonWidgetModule:InitReorderableList(params)
    local newComponent = ReorderableList:New()
    newComponent:InitComponent(params)
    return newComponent
end

function CommonWidgetModule:GetDragAreaClass()
    return ReorderableListDragArea
end

function CommonWidgetModule:CreateTipsPlayer(callBack, info, parent, btnTbl, friendSource, teamSource)
    Facade.UIManager:AsyncShowUI(UIName2ID.CommonTipsPlayer, callBack, nil, info, parent, btnTbl, friendSource, teamSource)
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Item View

function CommonWidgetModule:ObtainWarehouseIVFromPool()
    return getField():ObtainWarehouseIV()
end

function CommonWidgetModule:FreeWarehouseIVToPool(warehouseIV)
    getField():FreeWarehouseIV(warehouseIV)
end

function CommonWidgetModule:PreloadWarehouseIV(num, bFraming, task)
    if Facade.UIManager:GetIsLowMemoryState() then
        logwarning("CommonWidgetModule:PreloadWarehouseIV discard in low memory device.")
        return
    end

    self:PreLoadGlobalRegisterUI()
    getField():PreloadWarehouseIV(num, bFraming, task)
end

function CommonWidgetModule:CleanWarehouseIVPool()
    getField():CleanWarehouseIVPool()
end

function CommonWidgetModule:ObtainIVComp(compNavID)
    return self.Field:ObtainIVComp(compNavID)
end

function CommonWidgetModule:FreeIVComp(IVComp, compNavID)
    return self.Field:FreeIVComp(IVComp, compNavID)
end

function CommonWidgetModule:PreloadIVComponent(IVComp, num, bFraming, task)
    if Facade.UIManager:GetIsLowMemoryState() then
        logwarning("CommonWidgetModule:PreloadIVComponent discard in low memory device.")
        return
    end

    self:PreLoadGlobalRegisterUI()
    getField():PreloadIVComp(IVComp, num, bFraming, task)
end

function CommonWidgetModule:CleanAllIVCompPools()
    getField():CleanAllIVCompPools()
end

function CommonWidgetModule:ObtainDragPreviewItemFromPool()
    return getField():ObtainDragPreviewItem()
end

function CommonWidgetModule:FreeDragPreviewItemToPool(dragPreviewItem)
    getField():FreeDragPreviewItem(dragPreviewItem)
end

function CommonWidgetModule:CleanDragPreviewItem()
    getField():CleanDragPreviewItem()
end

---@param warehouseTemplateParams IVWarehouseTemplateParams
---@return IVWarehouseTemplate
function CommonWidgetModule:CreateIVWarehouseTemplate(owner, parent, instanceId, warehouseTemplateParams)
    return Facade.UIManager:AddSubUI(owner, UIName2ID.IVWarehouseTemplate, parent, instanceId, warehouseTemplateParams)
end

---@return IVCommonItemTemplate
function CommonWidgetModule:CreateIVCommonItemTemplate(owner, parent, instanceId)
    return Facade.UIManager:AddSubUI(owner, UIName2ID.IVCommonItemTemplate, parent, instanceId)
end

--- 安全起见，开启对象池的版本仅影响商人
---@return IVCommonItemTemplateMini
function CommonWidgetModule:CreateIVCommonItemTemplateMini(owner, parent, instanceId)
    return Facade.UIManager:AddSubUI(owner, UIName2ID.IVCommonItemTemplateMini, parent, instanceId)
end

function CommonWidgetModule:ClearIVCommonItemTemplateMini()
    return Facade.UIManager:ClearReuseUIList(UIName2ID.IVCommonItemTemplateMini)
end

function CommonWidgetModule:CreateIVCommonItemTemplateBySize(owner, parent, instanceId, sizeX, sizeY)
    local weakUIIns, instanceId = Facade.UIManager:AddSubUI(owner, UIName2ID.IVCommonItemTemplate, parent, instanceId)
    local commonitem = getfromweak(weakUIIns)
    -- commonitem.WBP_SlotItemView:SetCppValue("DefaultSize",FVector2D(sizeX,sizeY))
    commonitem:SetRootSize(sizeX, sizeY)
    return weakUIIns, instanceId
end

function CommonWidgetModule:CreateIVCommonItemTemplateBindOwnerBySize(owner, sizeX, sizeY)
    local commonitem = Facade.UIManager:CreateSubUIBindOwner(owner, UIName2ID.IVCommonItemTemplate)
    -- commonitem.WBP_SlotItemView:SetCppValue("DefaultSize",FVector2D(sizeX,sizeY))
    commonitem:SetRootSize(sizeX, sizeY)
    return commonitem
end


---@param warehouseTemplateParams IVWarehouseTemplateParams
---@return IVWarehouseTemplate
function CommonWidgetModule:CreateIVWarehouseTemplateBindOwner(owner, warehouseTemplateParams)
    return Facade.UIManager:CreateSubUIBindOwner(owner, UIName2ID.IVWarehouseTemplate, warehouseTemplateParams)
end


function CommonWidgetModule:GetLastItemDoubleClickTimestamp()
    return getField():GetLastItemDoubleClickTimestamp()
end

function CommonWidgetModule:SetLastItemDoubleClickTimestamp(lastItemDoubleClickTimestamp)
    getField():SetLastItemDoubleClickTimestamp(lastItemDoubleClickTimestamp)
end

function CommonWidgetModule:NotifyItemHighlight(item)
    CommonWidgetConfig.Events.evtNotifyItemHighlight:Invoke(item)
end

function CommonWidgetModule:RegisterWeaponSelection(leftWeaponView, rightWeaponView)
    WeaponSelectionLogic.RegisterWeaponSelection(leftWeaponView, rightWeaponView)
end

function CommonWidgetModule:UnregisterWeaponSelection()
    WeaponSelectionLogic.UnregisterWeaponSelection()
end

---@param Item ItemBase
function CommonWidgetModule:CheckRunWeaponSelection(item)
    return WeaponSelectionLogic.CheckRunWeaponSelection(item)
end

function CommonWidgetModule:SetItemViewMode(itemviewStyle)
    Module.CommonWidget.Field:SetItemViewStyle(itemviewStyle)
end

-- btnInfoList：
-- btnInfo = { btnType = (默认CommonWidgetConfig.ESimpleBtnPanelItemType.Text) 内容项类型,
--             fOnClick = 点击事件响应函数，bEnable为false时可不配置, 
--             txt = (选填) 按钮显示文本或返回文本函数，没有可不配置,
--             bEnable = (默认true) bool值或返回bool值函数,
--             bBtnGrey = (默认false) bool是否按钮置灰，只是表现置灰，还可以响应click回调,
--             exParams = (选填) 各种类型内容项所需的自定义参数}
function CommonWidgetModule:OpenSimpleBtnListPanel(itemStruct, btnInfoList, fLoadCallback, fOnBgClickCallback, alignPosition, fOnCloseCallback, ...)
    local handle = Facade.UIManager:AsyncShowUI(UIName2ID.CommonRightclick, fLoadCallback, nil, itemStruct, btnInfoList, fOnBgClickCallback, alignPosition, fOnCloseCallback, ...)
    return handle
end

--BEGIN MODIFICATION @ VIRTUOS : 
-- 参考 OpenSimpleBtnListPanel 的相关实现，新增准确装备武器/物品的手柄辅助页面
function CommonWidgetModule:OpenTransferItemBtnListPanel(itemStruct, btnInfoList, fLoadCallback, fOnBgClickCallback, alignPosition, fOnCloseCallback, ...)
    if not IsHD() then
        return nil
    end

    local handle = Facade.UIManager:AsyncShowUI(UIName2ID.CommonTransferItemPanel, fLoadCallback, nil, itemStruct, btnInfoList, fOnBgClickCallback, alignPosition, fOnCloseCallback, ...)
    return handle
end
--END MODIFICATION

function CommonWidgetModule:OpenBattleFieldItemBtnListPanel(btnIndexList, btnNameList, btnEnableList, btnRightIconList, alignPosition, casterUin, operateUin)
    local handle = Module.CommonWidget.Field:GetSimpleBtnPanelHandle()
    if handle then
        Facade.UIManager:CloseUIByHandle(handle)
    end
    local btnTypeList = {}
    for i, btnName in ipairs(btnNameList) do
        local newBtnInfo = {}
        local fOnClick = function()
            local index = btnIndexList[i]
            if not UGameplayStatics then
                return
            end
            local gameState = UGameplayStatics.GetGameState(GetWorld())
            if not gameState then
                return
            end
            if gameState.GetCommanderComponent == nil then
                return
            end
            local battleFieldCommanderDataComponent = gameState:GetCommanderComponent()
            if not battleFieldCommanderDataComponent then
                return
            end
            loginfo("CommonWidgetModule:OpenBattleFieldItemBtnListPanel Operated", index)
            battleFieldCommanderDataComponent:OnCommanderPanelOperate(index, casterUin, operateUin)
            CommonWidgetModule.CloseBattleFieldItemBtnListPanel()
        end
        -- newBtnInfo.btnType = CommonWidgetConfig.ESimpleBtnPanelItemType.TextAndImage
        newBtnInfo.btnType = CommonWidgetConfig.ESimpleBtnPanelItemType.Text
        -- newBtnInfo.pcRightIcon = CommonWidgetConfig.pcRightIconList[btnRightIconList[i]]
        newBtnInfo.pcRightIcon = CommonWidgetConfig.pcRightIconList[btnRightIconList[i]]
        newBtnInfo.txt = btnName
        newBtnInfo.bEnable = btnEnableList[i]
        newBtnInfo.fOnClick = fOnClick
        newBtnInfo.bBtnGrey = not btnEnableList[i]
        newBtnInfo.exParams = {}
        table.insert(btnTypeList, newBtnInfo)
    end
    local onBgClickCallback = function ()
        CommonWidgetModule.CloseBattleFieldItemBtnListPanel()
        if not UGameplayStatics then
            return
        end
        local gameState = UGameplayStatics.GetGameState(GetWorld())
        if not gameState then
            return
        end
        if gameState.GetCommanderComponent == nil then
            return
        end
        local battleFieldCommanderDataComponent = gameState:GetCommanderComponent()
        if not battleFieldCommanderDataComponent then
            return
        end
        battleFieldCommanderDataComponent:OnCommanderPanelOperate(-1, 0, 0)
    end
    loginfo("CommonWidgetModule:OpenBattleFieldItemBtnListPanel")
    handle = Facade.UIManager:AsyncShowUI(UIName2ID.CommonRightclick, nil, nil, nil, btnTypeList, onBgClickCallback, alignPosition, nil)
    Module.CommonWidget.Field:SetSimpleBtnPanelHandle(handle)
end

function CommonWidgetModule:CreateTipsPlayerSimple(btnIndexList, operateUin, parent, textNames, btnRightIconList)
    local info = {
        player_id = operateUin,
        nick_name = "",
        report_type = 3,
    }
    local btnTblIngame = {
        HeadButtonType.IngameSilence,
        HeadButtonType.IngameGiveLeader,
        HeadButtonType.IngameGetLeader,
        HeadButtonType.IngameImpeachLeader,
        HeadButtonType.IngameGetCommander,
        HeadButtonType.IngameImpeachCommander,
        HeadButtonType.IngameAddFriend,
        HeadButtonType.IngameReport,
        HeadButtonType.IngameCommanderChannel
    }
    local btnTbl = {}
    for i, btnIndex in ipairs(btnIndexList) do
        if btnTblIngame[btnIndex + 1] ~= nil then
            table.insert(btnTbl, btnTblIngame[btnIndex + 1])
        end
    end

    local onBgClickCallback = function ()
        CommonWidgetModule.CloseBattleFieldItemBtnListPanel()
        if not UGameplayStatics then
            return
        end
        local gameState = UGameplayStatics.GetGameState(GetWorld())
        if not gameState then
            return
        end
        if gameState.GetCommanderComponent == nil then
            return
        end
        local battleFieldCommanderDataComponent = gameState:GetCommanderComponent()
        if not battleFieldCommanderDataComponent then
            return
        end
        battleFieldCommanderDataComponent:OnCommanderPanelOperate(-1, 0, 0)
    end

    loginfo("CommonWidgetModule:CreateTipsPlayerSimple")
    logtable(btnTbl, true)
    logtable(textNames, true)
    Module.CommonWidget.Field:SetRegisteredBtnTextNames(textNames, btnRightIconList)
    handle = Facade.UIManager:AsyncShowUI(UIName2ID.CommonTipsPlayerSimple, nil, nil, info, parent, btnTbl, FriendApplySource.RecentPlayApply, nil, "", true, nil, SafeCallBack(onBgClickCallback, self))
    Module.CommonWidget.Field:SetSimpleBtnPanelHandle(handle)
end

CommonWidgetModule.CloseBattleFieldItemBtnListPanel = function()
    local handle = Module.CommonWidget.Field:GetSimpleBtnPanelHandle()
    if handle then
        Facade.UIManager:CloseUIByHandle(handle)
    end
    Module.CommonWidget.Field:SetSimpleBtnPanelHandle(nil)
end


function CommonWidgetModule:CppShowCommonMessageWithAnchor(textArg, tipsAnchor)
	local handle = Module.CommonWidget.Field:GetCppHoverTipHandle()
    local content = {}
    table.insert(content, {textContent = textArg})
	if handle ~= nil then
		self:CppRemoveCommonMessageWithAnchor(tipsAnchor)
	end
	handle = Module.CommonTips:ShowCommonMessagesWithAnchor(content, tipsAnchor)
    Module.CommonWidget.Field:SetCppHoverTipHandle(handle)
end

function CommonWidgetModule:CppRemoveCommonMessageWithAnchor(tipsAnchor)
    local handle = Module.CommonWidget.Field:GetCppHoverTipHandle()
	if handle ~= nil then
        Module.CommonTips:RemoveCommonMessageWithAnchor(handle, tipsAnchor)
        Module.CommonWidget.Field:SetCppHoverTipHandle(nil)
	end
	return nil
end


function CommonWidgetModule:GetDefaultDragMode()
    if IsHD() then
        return EViewDragMode.Normal
    else
        return EViewDragMode.InverseValidateAngle
    end
end

function CommonWidgetModule:RegisterItemView(itemView)
    return CommonDragDropLogic.RegisterItemView(itemView)
end

function CommonWidgetModule:UnregisterItemView(itemView)
    return CommonDragDropLogic.UnregisterItemView(itemView)
end

function CommonWidgetModule:SetFlagDisableOpForGuide(bDisableItemOp)
    loginfo("CommonWidgetModule:SetFlagDisableOpForGuide", bDisableItemOp)
    self.Field.flagDisableOpForGuide = bDisableItemOp
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Common Video View
---打开视频播放界面，根据配置决定是弹窗式还是全屏
---@param rowName string MediaResTable行名
---@param needProgressBar boolean 是否需要进度条
---@param needSkipBtn boolean 是否需要跳过按钮
---@param fOnMediaPlayEnd function|nil 视频播放结束的回调
---@param caller LuaObject|nil 回调的调用对象，可以为nil
---@param defaultShowSkipDelay number 默认的延迟多久展示跳过按钮
---@param skipShowTime number 跳过按钮无交互多久后消失，传入0表示不自动消失
function CommonWidgetModule:ShowFullScreenVideoView(rowName, needProgressBar, needSkipBtn, fOnMediaPlayEnd, caller, defaultShowSkipDelay, skipShowTime, bAutoClose,fGetVideoLength, fOnMediaPlayBegin)
    local enablePlayFullScreenCG = UDFCommonMediaView.EnableCGMediaPlay()
    if not enablePlayFullScreenCG then
        logerror("[CommonWidgetModule:ShowFullScreenVideoView] Device not allowed CG play!")
        if fOnMediaPlayEnd then
            fOnMediaPlayEnd(caller) -- 直接返回
        end
        return false
    end

    local videoConfigData = Module.CommonWidget.Field:GetVideoConfigData(rowName)
    if not videoConfigData then
        Module.CommonTips:ShowSimpleTip(string.format(Module.CommonWidget.Config.Loc.VideoConfigError, rowName), 3)
        if fOnMediaPlayEnd then
            loginfo("CommonWidgetModule:ShowFullScreenVideoView VideoConfigData Invalid!", rowName)
            fOnMediaPlayEnd(caller) -- 直接返回
        end
        return false
    end

    if not CommonVideoLogic.CheckDataValid(videoConfigData) then
        -- 数据不合法直接返回，展示默认内容并提示
        if fOnMediaPlayEnd then
            loginfo("CommonWidgetModule:ShowFullScreenVideoView VideoConfig Movie Data Invalid!", rowName)
            fOnMediaPlayEnd(caller) -- 直接返回
        end
        return false
    end
    -- 如果是全屏播放的视频，调用CommonVideoFullScreenView
    local uiHandle = Facade.UIManager:AsyncShowUI(UIName2ID.CommonVideoFullScreenView, nil, nil, rowName, needProgressBar, needSkipBtn, fOnMediaPlayEnd, caller, defaultShowSkipDelay, skipShowTime, bAutoClose,fGetVideoLength, fOnMediaPlayBegin)
    return true, uiHandle
end

---CommonWidgetModule.IsMediaFileValid
---判断视频资源是否存在
---@param inRowName string MediaResTable行名
function CommonWidgetModule:IsMediaFileValid(inRowName)
    local enablePlayFullScreenCG = UDFCommonMediaView.EnableCGMediaPlay()
    if not enablePlayFullScreenCG then
        logerror("[CommonWidgetModule:IsMediaResValid] Device not allowed CG play!")
        return false
    end

    local videoConfigData = Module.CommonWidget.Field:GetVideoConfigData(inRowName)
    if not videoConfigData then
        return false
    end

    return CommonVideoLogic.CheckDataValid(videoConfigData, true)
end

--- 注意，这里需要强调一点，如果视频存在音频播放的话，就不要使用进度条，否则会出现进度条拖动后和音频无法对齐的问题
---@param rowName string MediaResTable行名
---@param needProgressBar boolean 是否需要进度条
---@param fOnMediaPlayEnd function 视频开始播放的回调，每次重新播放都会调用
---@param fOnMediaPlayBegin function 视频结束播放的回调，每次结束播放都会调用
---@param caller table|LuaObject|nil 回调的执行对象
function CommonWidgetModule:ShowVideoPopView(rowName, needProgressBar, fOnMediaPlayEnd, fOnMediaPlayBegin, caller)
    local videoConfigData = Module.CommonWidget.Field:GetVideoConfigData(rowName)
    if not videoConfigData then
        Module.CommonTips:ShowSimpleTip(string.format(Module.CommonWidget.Config.Loc.VideoConfigError, rowName), 3)
        if fOnMediaPlayEnd then
            loginfo("CommonWidgetModule:ShowFullScreenVideoView VideoConfigData Invalid!", rowName)
            fOnMediaPlayEnd(caller) -- 直接返回
        end
        return
    end

    if not CommonVideoLogic.CheckDataValid(videoConfigData) then
        -- 数据不合法直接返回，展示默认内容并提示
        if fOnMediaPlayEnd then
            loginfo("CommonWidgetModule:ShowFullScreenVideoView VideoConfig Movie Data Invalid!", rowName)
            fOnMediaPlayEnd(caller) -- 直接返回
        end
        return
    end
    
    -- 调用CommonVideoPopView
    Facade.UIManager:AsyncShowUI(UIName2ID.CommonVideoPopView, nil, nil, rowName,needProgressBar, fOnMediaPlayEnd, fOnMediaPlayBegin, caller)
end

function CommonWidgetModule:SetDisableHover(bDisable)
    self.Field.bDisableHover = bDisable
end
-----------------------------------------------------------------------

function CommonWidgetModule:BindCommonIVInputLogic()
    CommonIVInputLogic.BindEvents()
end

function CommonWidgetModule:UnbindCommonIVInputLogic()
    CommonIVInputLogic.UnbindEvents()
end

function CommonWidgetModule:GetHoverItem()
    return CommonIVInputLogic.GetHoverItem()
end

--BEGIN MODIFICATION @ VIRTUOS : 特殊情况下需要解除或恢复当前hoverView的输入绑定
function CommonWidgetModule:BindHoverViewDisplayInput()
    CommonIVInputLogic.BindHoverViewDisplayInput()
end

function CommonWidgetModule:UnBindHoverViewDisplayInput()
    CommonIVInputLogic.UnBindHoverViewDisplayInput()
end
--END MODIFICATION

--- 获取物品对应的ItemView
---@return IVWarehouseTemplate
function CommonWidgetModule:GetAssignedItemView(Item)
    return CommonWidgetLogic.GetAssignedItemView(Item)
end

function CommonWidgetModule:SetLongPressTriggerDuration(value)
    if VersionUtil.IsShipping() then
       return
    end
    if type(value) == "number" and value > 0 then
        ULuaItemDragConfigUtil.SetLongPressTriggerDuration(value)
        return
    end
    logerror("CommonWidgetModule:SetLongPressTriggerDuration invalid value", tostring(value))
end

-----------------------------------------------------------------------
function CommonWidgetModule:PreLoadGlobalRegisterUI()
    Facade.UIRegisterManager:PreLoadGlobalRegisterUI()
end

function CommonWidgetModule:SetItemScrollCmd(itemMoveInfo, scrollBox)
    CommonWidgetLogic.SetItemScrollCmd(itemMoveInfo, scrollBox)
end

function CommonWidgetModule:SetItemScrollCmd_ForceScrollTo(item, scrollBox)
    CommonWidgetLogic.SetItemScrollCmd_Internal(item, scrollBox)
end

function CommonWidgetModule:SetScrollEndHandle(onScrollEndHandle)
    CommonWidgetLogic.SetScrollEndHandle(onScrollEndHandle)
end

function CommonWidgetModule:RemoveItemScrollCmd()
    CommonWidgetLogic.RemoveItemScrollCmd()
end

function CommonWidgetModule:GetItemScrollCmd()
    return CommonWidgetLogic.GetItemScrollCmd()
end

function CommonWidgetModule:CheckDelayHighLight(item)
    local itemToScroll, _ = self:GetItemScrollCmd()
    return itemToScroll == item
end

function CommonWidgetModule:SimulateDragOrCancel(item, bDragOrCancel)
    CommonDragDropLogic.SimulateDragOrCancel(item, bDragOrCancel)
end

function CommonWidgetModule:CancelSimulateDrag()
    CommonDragDropLogic.CancelSimulateDrag()
end

function CommonWidgetModule:GetIsDragging()
    return getField():GetIsDragging()
end

function CommonWidgetModule:SetSingleClickItem(item)
    if item ~= nil then
        local singleClickCmd = getField().singleClickCmd
        singleClickCmd.item = item
        singleClickCmd.time = TimeUtil.GetCurrentTimeMillis()
        singleClickCmd.bRespond = false
    else
        getField().singleClickCmd = {}
    end
end

function CommonWidgetModule:IsSingleClickItemResponded(item)
    local singleClickCmd = getField().singleClickCmd
    if item and singleClickCmd and item == singleClickCmd.item and singleClickCmd.time and singleClickCmd.bRespond then
        local currentTime = TimeUtil.GetCurrentTimeMillis()
        if currentTime - singleClickCmd.time < 0.3 then
            return true
        end
    end
    return false
end

function CommonWidgetModule:PopSingleClickItem()
    local singleClickCmd = getField().singleClickCmd
    if singleClickCmd and singleClickCmd.item and singleClickCmd.time and not singleClickCmd.bRespond then
        singleClickCmd.bRespond = true
        local currentTime = TimeUtil.GetCurrentTimeMillis()
        if currentTime - singleClickCmd.time < 0.5 then
            return singleClickCmd.item
        end
    end
end

--BEGIN MODIFICATION @ VIRTUOS : 记录交换中间数据
--设置交换中间数据
---@param bInSwapItem bool
---@param swapWidget IVWarehouseTemplate
---@param swapItem ItemBase
function CommonWidgetModule:SetGamepadSwapItem(bInSwapItem, swapWidget, swapItem)
    if not IsHD() then
        return
    end

    local _field = getField()

    if bInSwapItem then
        -- 清除聚焦信息避免残留
        self:StopFocusItemView()

        if WidgetUtil.IsGamepad() == false then
            return
        end

        if swapWidget ~= nil then
            if WidgetUtil.TrySimulateMouseDrag(swapWidget) == true then
                _field.SelectedSwapItem = swapItem
            end
        end
        
    else
        _field.SelectedSwapItem = nil
        Module.CommonWidget:CancelDragDrop()
        WidgetUtil.StopSimulateMouseDrag()
    end
end

function CommonWidgetModule:EnableGamepadSwapItem(bEnableSwapItem)
    if not IsHD() then
        return
    end
    local _field = getField()
    _field.bEnableGamepadSwapItem = bEnableSwapItem
end
function CommonWidgetModule:IsEnableGamepadSwapItem()
    if not IsHD() then
        return false
    end
    local _field = getField()
    return _field.bEnableGamepadSwapItem
end

function CommonWidgetModule:EnableGamepadItemShortcutKey(bEnable)
    if not IsHD() then
        return
    end

    local _field = getField()
    _field.bEnableGamepadItemShortcutKey = bEnable
end

function CommonWidgetModule:IsEnableGamepadItemShortcutKey()
    if not IsHD() then
        return false
    end
    local _field = getField()
    return _field.bEnableGamepadItemShortcutKey
end

function CommonWidgetModule:IsInSwapingItem()
    if not IsHD() then
        return false
    end
    
    return WidgetUtil.IsOnSimulatedMouseDraging()
end

function CommonWidgetModule:GetSelectedSwapItem()
    if not IsHD() then
        return nil
    end

    local _field = getField()
    return _field.SelectedSwapItem
end

function CommonWidgetModule:SetShouldTryFocusPostDrag(bShould)
    if not IsHD() then
        return
    end

    local _field = getField()
    _field.bShouldTryFocusPostDrag = bShould
end

function CommonWidgetModule:ShouldTryFocusPostDrag()
    if not IsHD() then
        return false
    end

    local _field = getField()
    return _field.bShouldTryFocusPostDrag
end

function CommonWidgetModule:BeginToFocusItemView(waitToFocusItem)
    if not IsHD() then
        return nil
    end
 
    local _field = getField()
    if waitToFocusItem then
        _field.waitToFocusItem = waitToFocusItem
        _field.bInGamepadToFocusItemView = true
        _field.TryToFocusItemViewCount = 0
    end
end
 
function CommonWidgetModule:StopFocusItemView()
    if not IsHD() then
        return nil
    end
 
    local _field = getField()
    _field.bInGamepadToFocusItemView = false
    _field.TryToFocusItemViewCount = 0
    _field.bShouldTryFocusPostDrag = false
end
 
function CommonWidgetModule:CheckCanTryFocusItemView()
    if not IsHD() then
        return false
    end
 
    local _field = getField()
    _field.TryToFocusItemViewCount = _field.TryToFocusItemViewCount + 1
 
    -- 保底计数，最多尝试两次查找聚焦目标
    if _field.TryToFocusItemViewCount > 2 then
        return false
    end
    return true
end
 
function CommonWidgetModule:IsInTryFocusItemView()
    if not IsHD() then
        return false
    end
 
    local _field = getField()
    return _field.bInGamepadToFocusItemView
end
 
function CommonWidgetModule:GetWaitToFocusItem()
    if not IsHD() then
        return nil
    end
 
    local _field = getField()
    return _field.waitToFocusItem
end

function CommonWidgetModule:SetOpenedPopItemView(ItemView, Item)
    if not IsHD() then
        return
    end

    local _field = getField()

    if _field.bEnableGamepadCarryItemFromPop == false then
        _field.OpenedPopItemView = nil
        _field.OpenedPopItem = nil
        return
    end

    _field.OpenedPopItemView = ItemView
    _field.OpenedPopItem = Item
end

function CommonWidgetModule:GetOpenedPopItemView()
    if not IsHD() then
        return nil
    end

    local _field = getField()
    if _field.bEnableGamepadCarryItemFromPop == false then
        return nil
    end

    return _field.OpenedPopItemView
end

function CommonWidgetModule:GetOpenedPopItem()
    if not IsHD() then
        return nil
    end

    local _field = getField()
    if _field.bEnableGamepadCarryItemFromPop == false then
        return nil
    end

    return _field.OpenedPopItem
end

function CommonWidgetModule:EnableGamepadCarryItemFromPop(bEnableCarryItem)
    if not IsHD() then
        return
    end
    local _field = getField()
    _field.bEnableGamepadCarryItemFromPop= bEnableCarryItem

    if bEnableCarryItem == false then
        _field.OpenedPopItemView = nil
        _field.OpenedPopItem = nil
    end
end
function CommonWidgetModule:IsEnableGamepadCarryItemFromPop()
    if not IsHD() then
        return false
    end
    local _field = getField()
    return _field.bEnableGamepadCarryItemFromPop
end

---@param swapItem ItemBase
function CommonWidgetModule:IsItemEnableUse(item)
    if item then
        if item:IsUsableItem() then
            local itemFeature = item:GetFeature()
            local itemFeatureType = itemFeature:GetFeatureType()
            if itemFeatureType == EFeatureType.Key then
                return false
            else
                return true
            end
        end
    end

    return false
end

---@param swapItem ItemBase
function CommonWidgetModule:GetItemNeedTransferType(item)
    if item then
        local itemFeatureType = item:GetFeatureType()
        if itemFeatureType == EFeatureType.Equipment or (itemFeatureType == EFeatureType.Weapon and item.itemSubType == ItemConfig.EWeaponItemType.Pistol) then
            local itemInSlot = item.InSlot
            local bItemInPlayerSlot = itemInSlot ~= nil and itemInSlot:GetSlotGroup() == ESlotGroup.Player or false
            local bItemEquipped = item:IsEquipped()
    
            if bItemEquipped == false or bItemInPlayerSlot == false then
                return CommonWidgetConfig.EItemTransferType.FastEquipOrReplace
            end
        else
            return CommonWidgetConfig.EItemTransferType.TransferItemPanel
        end
    end

    return CommonWidgetConfig.EItemTransferType.None
end

--END MODIFICATION

-----------------------------------------------------------------------
---- Dynamic 通用富文本弹窗
-----------------------------------------------------------------------
function CommonWidgetModule:ShowCommonPopWindowV2Scroll(fOnLoadCallback, caller, windowTitleStr, richTextStr)
    return Facade.UIManager:AsyncShowUI(UIName2ID.CommonPopWindowV2Scroll, fOnLoadCallback, caller, windowTitleStr, richTextStr)
end

function CommonWidgetModule:ShowCommonPopWindowV2Waterfall(fOnLoadCallback, caller, windowTitleStr, contentTitleText, itemDescDataList, bShowTitle)
    return Facade.UIManager:AsyncShowUI(UIName2ID.CommonPopWindowV2Waterfall, fOnLoadCallback, caller, windowTitleStr, contentTitleText, itemDescDataList, bShowTitle)
end

function CommonWidgetModule:ShowCommonPopWindowV2Confirm(fOnLoadCallback, caller, textContentInfo)
    return Facade.UIManager:AsyncShowUI(UIName2ID.CommonPopWindowsRichText, fOnLoadCallback, caller, textContentInfo)
end


return CommonWidgetModule
