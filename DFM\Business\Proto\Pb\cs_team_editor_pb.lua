--cs_team.protoencode&decode functions.
function pb.pb_TeamMemberInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_TeamMemberInfo) or {} 
    local __PlayerID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __PlayerID ~= 0 then tb.PlayerID = __PlayerID end
    local __State = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __State ~= 0 then tb.State = __State end
    local __Nick = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __Nick ~= "" then tb.Nick = __Nick end
    local __Seat = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __Seat ~= 0 then tb.Seat = __Seat end
    tb.Info = pb.pb_PlayerSimpleInfoDecode(decoder:getsubmsg(6))
    local __JoinTimestamp = decoder:geti64(7)
    if not PB_USE_DEFAULT_TABLE or __JoinTimestamp ~= 0 then tb.JoinTimestamp = __JoinTimestamp end
    tb.Props = {}
    for k,v in pairs(decoder:getsubmsgary(10)) do
        tb.Props[k] = pb.pb_EquipPositionDecode(v)
    end
    tb.fashion = {}
    for k,v in pairs(decoder:getsubmsgary(11)) do
        tb.fashion[k] = pb.pb_HeroFashionDecode(v)
    end
    tb.PackQuestID = decoder:geti32ary(12)
    local __EquipPrice = decoder:geti64(13)
    if not PB_USE_DEFAULT_TABLE or __EquipPrice ~= 0 then tb.EquipPrice = __EquipPrice end
    tb.accessories = {}
    for k,v in pairs(decoder:getsubmsgary(14)) do
        tb.accessories[k] = pb.pb_CSHeroAccessoryDecode(v)
    end
    return tb
end

function pb.pb_TeamMemberInfoEncode(tb, encoder)
    if(tb.PlayerID) then    encoder:addu64(1, tb.PlayerID)    end
    if(tb.State) then    encoder:addu32(2, tb.State)    end
    if(tb.Nick) then    encoder:addstr(4, tb.Nick)    end
    if(tb.Seat) then    encoder:addi32(5, tb.Seat)    end
    if(tb.Info) then    pb.pb_PlayerSimpleInfoEncode(tb.Info, encoder:addsubmsg(6))    end
    if(tb.JoinTimestamp) then    encoder:addi64(7, tb.JoinTimestamp)    end
    if(tb.Props) then
        for i=1,#(tb.Props) do
            pb.pb_EquipPositionEncode(tb.Props[i], encoder:addsubmsg(10))
        end
    end
    if(tb.fashion) then
        for i=1,#(tb.fashion) do
            pb.pb_HeroFashionEncode(tb.fashion[i], encoder:addsubmsg(11))
        end
    end
    if(tb.PackQuestID) then    encoder:addi32(12, tb.PackQuestID)    end
    if(tb.EquipPrice) then    encoder:addi64(13, tb.EquipPrice)    end
    if(tb.accessories) then
        for i=1,#(tb.accessories) do
            pb.pb_CSHeroAccessoryEncode(tb.accessories[i], encoder:addsubmsg(14))
        end
    end
end

function pb.pb_TeamInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_TeamInfo) or {} 
    local __TeamID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    local __LeaderID = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __LeaderID ~= 0 then tb.LeaderID = __LeaderID end
    local __MatchID = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __MatchID ~= 0 then tb.MatchID = __MatchID end
    local __State = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __State ~= 0 then tb.State = __State end
    local __IsAddMember = decoder:getbool(5)
    if not PB_USE_DEFAULT_TABLE or __IsAddMember ~= false then tb.IsAddMember = __IsAddMember end
    local __IsRankedMatch = decoder:getbool(6)
    if not PB_USE_DEFAULT_TABLE or __IsRankedMatch ~= false then tb.IsRankedMatch = __IsRankedMatch end
    tb.Members = {}
    for k,v in pairs(decoder:getsubmsgary(10)) do
        tb.Members[k] = pb.pb_TeamMemberInfoDecode(v)
    end
    tb.Mode = pb.pb_MatchModeInfoDecode(decoder:getsubmsg(11))
    local __AddMemberType = decoder:getu32(12)
    if not PB_USE_DEFAULT_TABLE or __AddMemberType ~= 0 then tb.AddMemberType = __AddMemberType end
    tb.Modes = {}
    for k,v in pairs(decoder:getsubmsgary(14)) do
        tb.Modes[k] = pb.pb_MatchModeInfoDecode(v)
    end
    local __GroupID = decoder:getu64(15)
    if not PB_USE_DEFAULT_TABLE or __GroupID ~= 0 then tb.GroupID = __GroupID end
    local __DSTextualIP = decoder:getstr(30)
    if not PB_USE_DEFAULT_TABLE or __DSTextualIP ~= "" then tb.DSTextualIP = __DSTextualIP end
    local __DSPort = decoder:getu32(31)
    if not PB_USE_DEFAULT_TABLE or __DSPort ~= 0 then tb.DSPort = __DSPort end
    local __DSANode = decoder:getstr(32)
    if not PB_USE_DEFAULT_TABLE or __DSANode ~= "" then tb.DSANode = __DSANode end
    local __DSDomain = decoder:getstr(33)
    if not PB_USE_DEFAULT_TABLE or __DSDomain ~= "" then tb.DSDomain = __DSDomain end
    local __DSRoomID = decoder:getu64(34)
    if not PB_USE_DEFAULT_TABLE or __DSRoomID ~= 0 then tb.DSRoomID = __DSRoomID end
    local __Token = decoder:getstr(35)
    if not PB_USE_DEFAULT_TABLE or __Token ~= "" then tb.Token = __Token end
    tb.ds_ip_list = {}
    for k,v in pairs(decoder:getsubmsgary(36)) do
        tb.ds_ip_list[k] = pb.pb_HostInfoDecode(v)
    end
    local __IsOpenQuickJoin = decoder:getbool(37)
    if not PB_USE_DEFAULT_TABLE or __IsOpenQuickJoin ~= false then tb.IsOpenQuickJoin = __IsOpenQuickJoin end
    local __Version = decoder:getstr(38)
    if not PB_USE_DEFAULT_TABLE or __Version ~= "" then tb.Version = __Version end
    local __MaxMemberSize = decoder:getu32(39)
    if not PB_USE_DEFAULT_TABLE or __MaxMemberSize ~= 0 then tb.MaxMemberSize = __MaxMemberSize end
    local __RecruitmentState = decoder:getu32(40)
    if not PB_USE_DEFAULT_TABLE or __RecruitmentState ~= 0 then tb.RecruitmentState = __RecruitmentState end
    tb.Filter = pb.pb_RecruitmentFilterDecode(decoder:getsubmsg(41))
    local __WorldChatRoomId = decoder:getu64(42)
    if not PB_USE_DEFAULT_TABLE or __WorldChatRoomId ~= 0 then tb.WorldChatRoomId = __WorldChatRoomId end
    local __WorldChatRoomIndex = decoder:getu64(43)
    if not PB_USE_DEFAULT_TABLE or __WorldChatRoomIndex ~= 0 then tb.WorldChatRoomIndex = __WorldChatRoomIndex end
    local __lastPostTime = decoder:geti64(44)
    if not PB_USE_DEFAULT_TABLE or __lastPostTime ~= 0 then tb.lastPostTime = __lastPostTime end
    local __Session = decoder:getstr(45)
    if not PB_USE_DEFAULT_TABLE or __Session ~= "" then tb.Session = __Session end
    return tb
end

function pb.pb_TeamInfoEncode(tb, encoder)
    if(tb.TeamID) then    encoder:addu64(1, tb.TeamID)    end
    if(tb.LeaderID) then    encoder:addu64(2, tb.LeaderID)    end
    if(tb.MatchID) then    encoder:addu32(3, tb.MatchID)    end
    if(tb.State) then    encoder:addu32(4, tb.State)    end
    if(tb.IsAddMember) then    encoder:addbool(5, tb.IsAddMember)    end
    if(tb.IsRankedMatch) then    encoder:addbool(6, tb.IsRankedMatch)    end
    if(tb.Members) then
        for i=1,#(tb.Members) do
            pb.pb_TeamMemberInfoEncode(tb.Members[i], encoder:addsubmsg(10))
        end
    end
    if(tb.Mode) then    pb.pb_MatchModeInfoEncode(tb.Mode, encoder:addsubmsg(11))    end
    if(tb.AddMemberType) then    encoder:addu32(12, tb.AddMemberType)    end
    if(tb.Modes) then
        for i=1,#(tb.Modes) do
            pb.pb_MatchModeInfoEncode(tb.Modes[i], encoder:addsubmsg(14))
        end
    end
    if(tb.GroupID) then    encoder:addu64(15, tb.GroupID)    end
    if(tb.DSTextualIP) then    encoder:addstr(30, tb.DSTextualIP)    end
    if(tb.DSPort) then    encoder:addu32(31, tb.DSPort)    end
    if(tb.DSANode) then    encoder:addstr(32, tb.DSANode)    end
    if(tb.DSDomain) then    encoder:addstr(33, tb.DSDomain)    end
    if(tb.DSRoomID) then    encoder:addu64(34, tb.DSRoomID)    end
    if(tb.Token) then    encoder:addstr(35, tb.Token)    end
    if(tb.ds_ip_list) then
        for i=1,#(tb.ds_ip_list) do
            pb.pb_HostInfoEncode(tb.ds_ip_list[i], encoder:addsubmsg(36))
        end
    end
    if(tb.IsOpenQuickJoin) then    encoder:addbool(37, tb.IsOpenQuickJoin)    end
    if(tb.Version) then    encoder:addstr(38, tb.Version)    end
    if(tb.MaxMemberSize) then    encoder:addu32(39, tb.MaxMemberSize)    end
    if(tb.RecruitmentState) then    encoder:addu32(40, tb.RecruitmentState)    end
    if(tb.Filter) then    pb.pb_RecruitmentFilterEncode(tb.Filter, encoder:addsubmsg(41))    end
    if(tb.WorldChatRoomId) then    encoder:addu64(42, tb.WorldChatRoomId)    end
    if(tb.WorldChatRoomIndex) then    encoder:addu64(43, tb.WorldChatRoomIndex)    end
    if(tb.lastPostTime) then    encoder:addi64(44, tb.lastPostTime)    end
    if(tb.Session) then    encoder:addstr(45, tb.Session)    end
end

function pb.pb_CSTeamInfoTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamInfoTReq) or {} 
    local __TeamID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    return tb
end

function pb.pb_CSTeamInfoTReqEncode(tb, encoder)
    if(tb.TeamID) then    encoder:addu64(1, tb.TeamID)    end
end

function pb.pb_CSTeamInfoTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamInfoTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.Info = pb.pb_TeamInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSTeamInfoTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.Info) then    pb.pb_TeamInfoEncode(tb.Info, encoder:addsubmsg(2))    end
end

function pb.pb_CSTeamCreateReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamCreateReq) or {} 
    local __MatchID = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __MatchID ~= 0 then tb.MatchID = __MatchID end
    tb.Mode = pb.pb_MatchModeInfoDecode(decoder:getsubmsg(2))
    tb.Modes = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.Modes[k] = pb.pb_MatchModeInfoDecode(v)
    end
    local __AddMemberType = decoder:getu32(12)
    if not PB_USE_DEFAULT_TABLE or __AddMemberType ~= 0 then tb.AddMemberType = __AddMemberType end
    local __spawn_point = decoder:getu64(13)
    if not PB_USE_DEFAULT_TABLE or __spawn_point ~= 0 then tb.spawn_point = __spawn_point end
    local __GroupID = decoder:getu64(14)
    if not PB_USE_DEFAULT_TABLE or __GroupID ~= 0 then tb.GroupID = __GroupID end
    local __IsAddMember = decoder:getbool(15)
    if not PB_USE_DEFAULT_TABLE or __IsAddMember ~= false then tb.IsAddMember = __IsAddMember end
    tb.PackQuestID = decoder:geti32ary(10)
    local __CreateType = decoder:geti32(20)
    if not PB_USE_DEFAULT_TABLE or __CreateType ~= 0 then tb.CreateType = __CreateType end
    local __IsRankedMatch = decoder:getbool(16)
    if not PB_USE_DEFAULT_TABLE or __IsRankedMatch ~= false then tb.IsRankedMatch = __IsRankedMatch end
    return tb
end

function pb.pb_CSTeamCreateReqEncode(tb, encoder)
    if(tb.MatchID) then    encoder:addu32(1, tb.MatchID)    end
    if(tb.Mode) then    pb.pb_MatchModeInfoEncode(tb.Mode, encoder:addsubmsg(2))    end
    if(tb.Modes) then
        for i=1,#(tb.Modes) do
            pb.pb_MatchModeInfoEncode(tb.Modes[i], encoder:addsubmsg(3))
        end
    end
    if(tb.AddMemberType) then    encoder:addu32(12, tb.AddMemberType)    end
    if(tb.spawn_point) then    encoder:addu64(13, tb.spawn_point)    end
    if(tb.GroupID) then    encoder:addu64(14, tb.GroupID)    end
    if(tb.IsAddMember) then    encoder:addbool(15, tb.IsAddMember)    end
    if(tb.PackQuestID) then    encoder:addi32(10, tb.PackQuestID)    end
    if(tb.CreateType) then    encoder:addi32(20, tb.CreateType)    end
    if(tb.IsRankedMatch) then    encoder:addbool(16, tb.IsRankedMatch)    end
end

function pb.pb_CSTeamCreateResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamCreateRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.Info = pb.pb_TeamInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSTeamCreateResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.Info) then    pb.pb_TeamInfoEncode(tb.Info, encoder:addsubmsg(2))    end
end

function pb.pb_CSTeamInviteTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamInviteTReq) or {} 
    local __TeamID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    local __MatchID = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __MatchID ~= 0 then tb.MatchID = __MatchID end
    local __InviteeID = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __InviteeID ~= 0 then tb.InviteeID = __InviteeID end
    local __source = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __source ~= 0 then tb.source = __source end
    tb.Mode = pb.pb_MatchModeInfoDecode(decoder:getsubmsg(6))
    local __RoomID = decoder:getu64(7)
    if not PB_USE_DEFAULT_TABLE or __RoomID ~= 0 then tb.RoomID = __RoomID end
    local __SpecialChannel = decoder:geti32(8)
    if not PB_USE_DEFAULT_TABLE or __SpecialChannel ~= 0 then tb.SpecialChannel = __SpecialChannel end
    return tb
end

function pb.pb_CSTeamInviteTReqEncode(tb, encoder)
    if(tb.TeamID) then    encoder:addu64(1, tb.TeamID)    end
    if(tb.MatchID) then    encoder:addu32(3, tb.MatchID)    end
    if(tb.InviteeID) then    encoder:addu64(4, tb.InviteeID)    end
    if(tb.source) then    encoder:addu32(5, tb.source)    end
    if(tb.Mode) then    pb.pb_MatchModeInfoEncode(tb.Mode, encoder:addsubmsg(6))    end
    if(tb.RoomID) then    encoder:addu64(7, tb.RoomID)    end
    if(tb.SpecialChannel) then    encoder:addi32(8, tb.SpecialChannel)    end
end

function pb.pb_CSTeamInviteTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamInviteTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSTeamInviteTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_CSTeamToInviteeNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamToInviteeNtf) or {} 
    local __TeamID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    local __MatchID = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __MatchID ~= 0 then tb.MatchID = __MatchID end
    local __InviterID = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __InviterID ~= 0 then tb.InviterID = __InviterID end
    local __InviterNick = decoder:getstr(6)
    if not PB_USE_DEFAULT_TABLE or __InviterNick ~= "" then tb.InviterNick = __InviterNick end
    local __InviterLevel = decoder:getu32(7)
    if not PB_USE_DEFAULT_TABLE or __InviterLevel ~= 0 then tb.InviterLevel = __InviterLevel end
    local __InviterPicUrl = decoder:getstr(8)
    if not PB_USE_DEFAULT_TABLE or __InviterPicUrl ~= "" then tb.InviterPicUrl = __InviterPicUrl end
    tb.Inviter = pb.pb_PlayerSimpleInfoDecode(decoder:getsubmsg(10))
    local __TeamMemberNum = decoder:getu32(11)
    if not PB_USE_DEFAULT_TABLE or __TeamMemberNum ~= 0 then tb.TeamMemberNum = __TeamMemberNum end
    local __source = decoder:getu32(12)
    if not PB_USE_DEFAULT_TABLE or __source ~= 0 then tb.source = __source end
    tb.Mode = pb.pb_MatchModeInfoDecode(decoder:getsubmsg(13))
    tb.Modes = {}
    for k,v in pairs(decoder:getsubmsgary(14)) do
        tb.Modes[k] = pb.pb_MatchModeInfoDecode(v)
    end
    local __GroupID = decoder:getu64(15)
    if not PB_USE_DEFAULT_TABLE or __GroupID ~= 0 then tb.GroupID = __GroupID end
    local __InviterAccountLevel = decoder:getu32(16)
    if not PB_USE_DEFAULT_TABLE or __InviterAccountLevel ~= 0 then tb.InviterAccountLevel = __InviterAccountLevel end
    local __RoomID = decoder:getu64(17)
    if not PB_USE_DEFAULT_TABLE or __RoomID ~= 0 then tb.RoomID = __RoomID end
    local __SpecialChannel = decoder:geti32(18)
    if not PB_USE_DEFAULT_TABLE or __SpecialChannel ~= 0 then tb.SpecialChannel = __SpecialChannel end
    return tb
end

function pb.pb_CSTeamToInviteeNtfEncode(tb, encoder)
    if(tb.TeamID) then    encoder:addu64(1, tb.TeamID)    end
    if(tb.MatchID) then    encoder:addu32(3, tb.MatchID)    end
    if(tb.InviterID) then    encoder:addu64(4, tb.InviterID)    end
    if(tb.InviterNick) then    encoder:addstr(6, tb.InviterNick)    end
    if(tb.InviterLevel) then    encoder:addu32(7, tb.InviterLevel)    end
    if(tb.InviterPicUrl) then    encoder:addstr(8, tb.InviterPicUrl)    end
    if(tb.Inviter) then    pb.pb_PlayerSimpleInfoEncode(tb.Inviter, encoder:addsubmsg(10))    end
    if(tb.TeamMemberNum) then    encoder:addu32(11, tb.TeamMemberNum)    end
    if(tb.source) then    encoder:addu32(12, tb.source)    end
    if(tb.Mode) then    pb.pb_MatchModeInfoEncode(tb.Mode, encoder:addsubmsg(13))    end
    if(tb.Modes) then
        for i=1,#(tb.Modes) do
            pb.pb_MatchModeInfoEncode(tb.Modes[i], encoder:addsubmsg(14))
        end
    end
    if(tb.GroupID) then    encoder:addu64(15, tb.GroupID)    end
    if(tb.InviterAccountLevel) then    encoder:addu32(16, tb.InviterAccountLevel)    end
    if(tb.RoomID) then    encoder:addu64(17, tb.RoomID)    end
    if(tb.SpecialChannel) then    encoder:addi32(18, tb.SpecialChannel)    end
end

function pb.pb_CSTeamResponseInviteTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamResponseInviteTReq) or {} 
    local __TeamID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    local __IsAgree = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __IsAgree ~= false then tb.IsAgree = __IsAgree end
    local __RefuseMessage = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __RefuseMessage ~= "" then tb.RefuseMessage = __RefuseMessage end
    local __InviterID = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __InviterID ~= 0 then tb.InviterID = __InviterID end
    local __source = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __source ~= 0 then tb.source = __source end
    local __InviteeGameMode = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __InviteeGameMode ~= 0 then tb.InviteeGameMode = __InviteeGameMode end
    local __RoomID = decoder:getu64(7)
    if not PB_USE_DEFAULT_TABLE or __RoomID ~= 0 then tb.RoomID = __RoomID end
    tb.PackQuestID = decoder:geti32ary(10)
    return tb
end

function pb.pb_CSTeamResponseInviteTReqEncode(tb, encoder)
    if(tb.TeamID) then    encoder:addu64(1, tb.TeamID)    end
    if(tb.IsAgree) then    encoder:addbool(2, tb.IsAgree)    end
    if(tb.RefuseMessage) then    encoder:addstr(3, tb.RefuseMessage)    end
    if(tb.InviterID) then    encoder:addu64(4, tb.InviterID)    end
    if(tb.source) then    encoder:addu32(5, tb.source)    end
    if(tb.InviteeGameMode) then    encoder:addi32(6, tb.InviteeGameMode)    end
    if(tb.RoomID) then    encoder:addu64(7, tb.RoomID)    end
    if(tb.PackQuestID) then    encoder:addi32(10, tb.PackQuestID)    end
end

function pb.pb_CSTeamResponseInviteTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamResponseInviteTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.Info = pb.pb_TeamInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSTeamResponseInviteTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.Info) then    pb.pb_TeamInfoEncode(tb.Info, encoder:addsubmsg(2))    end
end

function pb.pb_CSTeamResponseInviteNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamResponseInviteNtf) or {} 
    local __TeamID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    local __IsAgree = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __IsAgree ~= false then tb.IsAgree = __IsAgree end
    local __RefuseMessage = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __RefuseMessage ~= "" then tb.RefuseMessage = __RefuseMessage end
    local __InviteeNick = decoder:getstr(6)
    if not PB_USE_DEFAULT_TABLE or __InviteeNick ~= "" then tb.InviteeNick = __InviteeNick end
    tb.Invitee = pb.pb_PlayerSimpleInfoDecode(decoder:getsubmsg(7))
    return tb
end

function pb.pb_CSTeamResponseInviteNtfEncode(tb, encoder)
    if(tb.TeamID) then    encoder:addu64(1, tb.TeamID)    end
    if(tb.IsAgree) then    encoder:addbool(3, tb.IsAgree)    end
    if(tb.RefuseMessage) then    encoder:addstr(4, tb.RefuseMessage)    end
    if(tb.InviteeNick) then    encoder:addstr(6, tb.InviteeNick)    end
    if(tb.Invitee) then    pb.pb_PlayerSimpleInfoEncode(tb.Invitee, encoder:addsubmsg(7))    end
end

function pb.pb_CSTeamApplyJoinTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamApplyJoinTReq) or {} 
    local __team_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    local __target_player_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __target_player_id ~= 0 then tb.target_player_id = __target_player_id end
    local __apply_player_game_mode = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __apply_player_game_mode ~= 0 then tb.apply_player_game_mode = __apply_player_game_mode end
    local __source = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __source ~= 0 then tb.source = __source end
    tb.PackQuestID = decoder:geti32ary(10)
    local __SpecialChannel = decoder:geti32(11)
    if not PB_USE_DEFAULT_TABLE or __SpecialChannel ~= 0 then tb.SpecialChannel = __SpecialChannel end
    return tb
end

function pb.pb_CSTeamApplyJoinTReqEncode(tb, encoder)
    if(tb.team_id) then    encoder:addu64(1, tb.team_id)    end
    if(tb.target_player_id) then    encoder:addu64(2, tb.target_player_id)    end
    if(tb.apply_player_game_mode) then    encoder:addi32(3, tb.apply_player_game_mode)    end
    if(tb.source) then    encoder:addu32(5, tb.source)    end
    if(tb.PackQuestID) then    encoder:addi32(10, tb.PackQuestID)    end
    if(tb.SpecialChannel) then    encoder:addi32(11, tb.SpecialChannel)    end
end

function pb.pb_CSTeamApplyJoinTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamApplyJoinTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSTeamApplyJoinTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_CSTeamApplyJoinNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamApplyJoinNtf) or {} 
    tb.apply_player = pb.pb_PlayerSimpleInfoDecode(decoder:getsubmsg(1))
    local __apply_player_game_mode = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __apply_player_game_mode ~= 0 then tb.apply_player_game_mode = __apply_player_game_mode end
    local __source = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __source ~= 0 then tb.source = __source end
    local __SpecialChannel = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __SpecialChannel ~= 0 then tb.SpecialChannel = __SpecialChannel end
    return tb
end

function pb.pb_CSTeamApplyJoinNtfEncode(tb, encoder)
    if(tb.apply_player) then    pb.pb_PlayerSimpleInfoEncode(tb.apply_player, encoder:addsubmsg(1))    end
    if(tb.apply_player_game_mode) then    encoder:addi32(2, tb.apply_player_game_mode)    end
    if(tb.source) then    encoder:addu32(5, tb.source)    end
    if(tb.SpecialChannel) then    encoder:addi32(6, tb.SpecialChannel)    end
end

function pb.pb_CSTeamResponseJoinTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamResponseJoinTReq) or {} 
    local __team_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    local __agree = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __agree ~= false then tb.agree = __agree end
    local __refuse_message = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __refuse_message ~= "" then tb.refuse_message = __refuse_message end
    local __apply_player_id = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __apply_player_id ~= 0 then tb.apply_player_id = __apply_player_id end
    local __source = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __source ~= 0 then tb.source = __source end
    local __apply_player_game_mode = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __apply_player_game_mode ~= 0 then tb.apply_player_game_mode = __apply_player_game_mode end
    return tb
end

function pb.pb_CSTeamResponseJoinTReqEncode(tb, encoder)
    if(tb.team_id) then    encoder:addu64(1, tb.team_id)    end
    if(tb.agree) then    encoder:addbool(2, tb.agree)    end
    if(tb.refuse_message) then    encoder:addstr(3, tb.refuse_message)    end
    if(tb.apply_player_id) then    encoder:addu64(4, tb.apply_player_id)    end
    if(tb.source) then    encoder:addu32(5, tb.source)    end
    if(tb.apply_player_game_mode) then    encoder:addi32(6, tb.apply_player_game_mode)    end
end

function pb.pb_CSTeamResponseJoinTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamResponseJoinTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSTeamResponseJoinTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_CSTeamResponseJoinNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamResponseJoinNtf) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.leader = pb.pb_PlayerSimpleInfoDecode(decoder:getsubmsg(2))
    local __team_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    local __agree = decoder:getbool(4)
    if not PB_USE_DEFAULT_TABLE or __agree ~= false then tb.agree = __agree end
    local __refuse_message = decoder:getstr(5)
    if not PB_USE_DEFAULT_TABLE or __refuse_message ~= "" then tb.refuse_message = __refuse_message end
    tb.info = pb.pb_TeamInfoDecode(decoder:getsubmsg(6))
    return tb
end

function pb.pb_CSTeamResponseJoinNtfEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.leader) then    pb.pb_PlayerSimpleInfoEncode(tb.leader, encoder:addsubmsg(2))    end
    if(tb.team_id) then    encoder:addu64(3, tb.team_id)    end
    if(tb.agree) then    encoder:addbool(4, tb.agree)    end
    if(tb.refuse_message) then    encoder:addstr(5, tb.refuse_message)    end
    if(tb.info) then    pb.pb_TeamInfoEncode(tb.info, encoder:addsubmsg(6))    end
end

function pb.pb_CSTeamMemberChangeNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamMemberChangeNtf) or {} 
    local __TeamID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    local __LeaderID = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __LeaderID ~= 0 then tb.LeaderID = __LeaderID end
    local __Type = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __Type ~= 0 then tb.Type = __Type end
    tb.Member = pb.pb_TeamMemberInfoDecode(decoder:getsubmsg(4))
    return tb
end

function pb.pb_CSTeamMemberChangeNtfEncode(tb, encoder)
    if(tb.TeamID) then    encoder:addu64(1, tb.TeamID)    end
    if(tb.LeaderID) then    encoder:addu64(2, tb.LeaderID)    end
    if(tb.Type) then    encoder:addu32(3, tb.Type)    end
    if(tb.Member) then    pb.pb_TeamMemberInfoEncode(tb.Member, encoder:addsubmsg(4))    end
end

function pb.pb_CSTeamMemberBatchChangeNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamMemberBatchChangeNtf) or {} 
    local __TeamID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    local __LeaderID = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __LeaderID ~= 0 then tb.LeaderID = __LeaderID end
    local __Type = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __Type ~= 0 then tb.Type = __Type end
    tb.MemberInfos = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.MemberInfos[k] = pb.pb_TeamMemberInfoDecode(v)
    end
    return tb
end

function pb.pb_CSTeamMemberBatchChangeNtfEncode(tb, encoder)
    if(tb.TeamID) then    encoder:addu64(1, tb.TeamID)    end
    if(tb.LeaderID) then    encoder:addu64(2, tb.LeaderID)    end
    if(tb.Type) then    encoder:addu32(3, tb.Type)    end
    if(tb.MemberInfos) then
        for i=1,#(tb.MemberInfos) do
            pb.pb_TeamMemberInfoEncode(tb.MemberInfos[i], encoder:addsubmsg(4))
        end
    end
end

function pb.pb_CSTeamBaseInfoChangeNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamBaseInfoChangeNtf) or {} 
    local __TeamID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    local __Type = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __Type ~= 0 then tb.Type = __Type end
    local __MatchID = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __MatchID ~= 0 then tb.MatchID = __MatchID end
    local __State = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __State ~= 0 then tb.State = __State end
    local __IsAddMember = decoder:getbool(5)
    if not PB_USE_DEFAULT_TABLE or __IsAddMember ~= false then tb.IsAddMember = __IsAddMember end
    local __LeaderID = decoder:getu64(6)
    if not PB_USE_DEFAULT_TABLE or __LeaderID ~= 0 then tb.LeaderID = __LeaderID end
    tb.Mode = pb.pb_MatchModeInfoDecode(decoder:getsubmsg(7))
    tb.Modes = {}
    for k,v in pairs(decoder:getsubmsgary(8)) do
        tb.Modes[k] = pb.pb_MatchModeInfoDecode(v)
    end
    local __GroupID = decoder:getu64(9)
    if not PB_USE_DEFAULT_TABLE or __GroupID ~= 0 then tb.GroupID = __GroupID end
    local __IsRankedMatch = decoder:getbool(10)
    if not PB_USE_DEFAULT_TABLE or __IsRankedMatch ~= false then tb.IsRankedMatch = __IsRankedMatch end
    local __AddMemberType = decoder:getu32(12)
    if not PB_USE_DEFAULT_TABLE or __AddMemberType ~= 0 then tb.AddMemberType = __AddMemberType end
    local __spawn_point = decoder:getu64(13)
    if not PB_USE_DEFAULT_TABLE or __spawn_point ~= 0 then tb.spawn_point = __spawn_point end
    local __NtfTime = decoder:geti64(14)
    if not PB_USE_DEFAULT_TABLE or __NtfTime ~= 0 then tb.NtfTime = __NtfTime end
    tb.ShowInfo = pb.pb_MatchIngShowInfoDecode(decoder:getsubmsg(15))
    local __node_id = decoder:getstr(16)
    if not PB_USE_DEFAULT_TABLE or __node_id ~= "" then tb.node_id = __node_id end
    local __IsOpenQuickJoin = decoder:getbool(17)
    if not PB_USE_DEFAULT_TABLE or __IsOpenQuickJoin ~= false then tb.IsOpenQuickJoin = __IsOpenQuickJoin end
    local __Session = decoder:getstr(18)
    if not PB_USE_DEFAULT_TABLE or __Session ~= "" then tb.Session = __Session end
    return tb
end

function pb.pb_CSTeamBaseInfoChangeNtfEncode(tb, encoder)
    if(tb.TeamID) then    encoder:addu64(1, tb.TeamID)    end
    if(tb.Type) then    encoder:addu32(2, tb.Type)    end
    if(tb.MatchID) then    encoder:addu32(3, tb.MatchID)    end
    if(tb.State) then    encoder:addu32(4, tb.State)    end
    if(tb.IsAddMember) then    encoder:addbool(5, tb.IsAddMember)    end
    if(tb.LeaderID) then    encoder:addu64(6, tb.LeaderID)    end
    if(tb.Mode) then    pb.pb_MatchModeInfoEncode(tb.Mode, encoder:addsubmsg(7))    end
    if(tb.Modes) then
        for i=1,#(tb.Modes) do
            pb.pb_MatchModeInfoEncode(tb.Modes[i], encoder:addsubmsg(8))
        end
    end
    if(tb.GroupID) then    encoder:addu64(9, tb.GroupID)    end
    if(tb.IsRankedMatch) then    encoder:addbool(10, tb.IsRankedMatch)    end
    if(tb.AddMemberType) then    encoder:addu32(12, tb.AddMemberType)    end
    if(tb.spawn_point) then    encoder:addu64(13, tb.spawn_point)    end
    if(tb.NtfTime) then    encoder:addi64(14, tb.NtfTime)    end
    if(tb.ShowInfo) then    pb.pb_MatchIngShowInfoEncode(tb.ShowInfo, encoder:addsubmsg(15))    end
    if(tb.node_id) then    encoder:addstr(16, tb.node_id)    end
    if(tb.IsOpenQuickJoin) then    encoder:addbool(17, tb.IsOpenQuickJoin)    end
    if(tb.Session) then    encoder:addstr(18, tb.Session)    end
end

function pb.pb_CSTeamExitTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamExitTReq) or {} 
    local __TeamID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    return tb
end

function pb.pb_CSTeamExitTReqEncode(tb, encoder)
    if(tb.TeamID) then    encoder:addu64(1, tb.TeamID)    end
end

function pb.pb_CSTeamExitTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamExitTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSTeamExitTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_CSTeamKickTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamKickTReq) or {} 
    local __TeamID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    local __KickPlayerID = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __KickPlayerID ~= 0 then tb.KickPlayerID = __KickPlayerID end
    return tb
end

function pb.pb_CSTeamKickTReqEncode(tb, encoder)
    if(tb.TeamID) then    encoder:addu64(1, tb.TeamID)    end
    if(tb.KickPlayerID) then    encoder:addu64(2, tb.KickPlayerID)    end
end

function pb.pb_CSTeamKickTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamKickTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSTeamKickTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_CSTeamSetReadyTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamSetReadyTReq) or {} 
    local __TeamID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    local __Ready = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __Ready ~= false then tb.Ready = __Ready end
    return tb
end

function pb.pb_CSTeamSetReadyTReqEncode(tb, encoder)
    if(tb.TeamID) then    encoder:addu64(1, tb.TeamID)    end
    if(tb.Ready) then    encoder:addbool(2, tb.Ready)    end
end

function pb.pb_CSTeamSetReadyTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamSetReadyTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __face_verify_url = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __face_verify_url ~= "" then tb.face_verify_url = __face_verify_url end
    return tb
end

function pb.pb_CSTeamSetReadyTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.face_verify_url) then    encoder:addstr(2, tb.face_verify_url)    end
end

function pb.pb_CSTeamPrepareTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamPrepareTReq) or {} 
    local __TeamID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    local __Prepare = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __Prepare ~= false then tb.Prepare = __Prepare end
    local __Refuse = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __Refuse ~= false then tb.Refuse = __Refuse end
    return tb
end

function pb.pb_CSTeamPrepareTReqEncode(tb, encoder)
    if(tb.TeamID) then    encoder:addu64(1, tb.TeamID)    end
    if(tb.Prepare) then    encoder:addbool(2, tb.Prepare)    end
    if(tb.Refuse) then    encoder:addbool(3, tb.Refuse)    end
end

function pb.pb_CSTeamPrepareTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamPrepareTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __face_verify_url = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __face_verify_url ~= "" then tb.face_verify_url = __face_verify_url end
    return tb
end

function pb.pb_CSTeamPrepareTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.face_verify_url) then    encoder:addstr(2, tb.face_verify_url)    end
end

function pb.pb_CSTeamPrepareNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamPrepareNtf) or {} 
    local __Prepare = decoder:getbool(1)
    if not PB_USE_DEFAULT_TABLE or __Prepare ~= false then tb.Prepare = __Prepare end
    return tb
end

function pb.pb_CSTeamPrepareNtfEncode(tb, encoder)
    if(tb.Prepare) then    encoder:addbool(1, tb.Prepare)    end
end

function pb.pb_CSTeamNotifyPrepareTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamNotifyPrepareTReq) or {} 
    local __TeamID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    local __MemberID = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __MemberID ~= 0 then tb.MemberID = __MemberID end
    return tb
end

function pb.pb_CSTeamNotifyPrepareTReqEncode(tb, encoder)
    if(tb.TeamID) then    encoder:addu64(1, tb.TeamID)    end
    if(tb.MemberID) then    encoder:addu64(2, tb.MemberID)    end
end

function pb.pb_CSTeamNotifyPrepareTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamNotifyPrepareTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSTeamNotifyPrepareTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_CSTeamPrepareEventNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamPrepareEventNtf) or {} 
    local __event = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __event ~= 0 then tb.event = __event end
    local __TeamID = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    local __MemberID = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __MemberID ~= 0 then tb.MemberID = __MemberID end
    local __SourcePlayerID = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __SourcePlayerID ~= 0 then tb.SourcePlayerID = __SourcePlayerID end
    return tb
end

function pb.pb_CSTeamPrepareEventNtfEncode(tb, encoder)
    if(tb.event) then    encoder:addu32(1, tb.event)    end
    if(tb.TeamID) then    encoder:addu64(2, tb.TeamID)    end
    if(tb.MemberID) then    encoder:addu64(3, tb.MemberID)    end
    if(tb.SourcePlayerID) then    encoder:addu64(4, tb.SourcePlayerID)    end
end

function pb.pb_CSTeamStartMatchTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamStartMatchTReq) or {} 
    local __TeamID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    local __IsAddMember = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __IsAddMember ~= false then tb.IsAddMember = __IsAddMember end
    local __Raid_ID = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __Raid_ID ~= 0 then tb.Raid_ID = __Raid_ID end
    local __rule = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __rule ~= 0 then tb.rule = __rule end
    local __spawn_point = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __spawn_point ~= 0 then tb.spawn_point = __spawn_point end
    local __IsRandedMatch = decoder:getbool(6)
    if not PB_USE_DEFAULT_TABLE or __IsRandedMatch ~= false then tb.IsRandedMatch = __IsRandedMatch end
    local __IsRankedMatch = decoder:getbool(7)
    if not PB_USE_DEFAULT_TABLE or __IsRankedMatch ~= false then tb.IsRankedMatch = __IsRankedMatch end
    local __IsForceStartMatch = decoder:getbool(8)
    if not PB_USE_DEFAULT_TABLE or __IsForceStartMatch ~= false then tb.IsForceStartMatch = __IsForceStartMatch end
    return tb
end

function pb.pb_CSTeamStartMatchTReqEncode(tb, encoder)
    if(tb.TeamID) then    encoder:addu64(1, tb.TeamID)    end
    if(tb.IsAddMember) then    encoder:addbool(2, tb.IsAddMember)    end
    if(tb.Raid_ID) then    encoder:addu32(3, tb.Raid_ID)    end
    if(tb.rule) then    encoder:addu32(4, tb.rule)    end
    if(tb.spawn_point) then    encoder:addu64(5, tb.spawn_point)    end
    if(tb.IsRandedMatch) then    encoder:addbool(6, tb.IsRandedMatch)    end
    if(tb.IsRankedMatch) then    encoder:addbool(7, tb.IsRankedMatch)    end
    if(tb.IsForceStartMatch) then    encoder:addbool(8, tb.IsForceStartMatch)    end
end

function pb.pb_CSTeamStartMatchTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamStartMatchTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.ShowInfo = pb.pb_MatchIngShowInfoDecode(decoder:getsubmsg(2))
    local __node_id = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __node_id ~= "" then tb.node_id = __node_id end
    tb.ntf = pb.pb_CSRoomMatchStartFailNtfDecode(decoder:getsubmsg(4))
    tb.CheckFailedPlayerIds = decoder:getu64ary(5)
    tb.CheckReputationFailedPlayerIds = decoder:getu64ary(6)
    local __face_verify_url = decoder:getstr(7)
    if not PB_USE_DEFAULT_TABLE or __face_verify_url ~= "" then tb.face_verify_url = __face_verify_url end
    return tb
end

function pb.pb_CSTeamStartMatchTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.ShowInfo) then    pb.pb_MatchIngShowInfoEncode(tb.ShowInfo, encoder:addsubmsg(2))    end
    if(tb.node_id) then    encoder:addstr(3, tb.node_id)    end
    if(tb.ntf) then    pb.pb_CSRoomMatchStartFailNtfEncode(tb.ntf, encoder:addsubmsg(4))    end
    if(tb.CheckFailedPlayerIds) then    encoder:addu64(5, tb.CheckFailedPlayerIds)    end
    if(tb.CheckReputationFailedPlayerIds) then    encoder:addu64(6, tb.CheckReputationFailedPlayerIds)    end
    if(tb.face_verify_url) then    encoder:addstr(7, tb.face_verify_url)    end
end

function pb.pb_CSTeamUnreadyNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamUnreadyNtf) or {} 
    local __team_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    tb.unready_member_array = decoder:getu64ary(2)
    local __result = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSTeamUnreadyNtfEncode(tb, encoder)
    if(tb.team_id) then    encoder:addu64(1, tb.team_id)    end
    if(tb.unready_member_array) then    encoder:addu64(2, tb.unready_member_array)    end
    if(tb.result) then    encoder:addu32(3, tb.result)    end
end

function pb.pb_CSTeamCancelMatchTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamCancelMatchTReq) or {} 
    local __TeamID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    return tb
end

function pb.pb_CSTeamCancelMatchTReqEncode(tb, encoder)
    if(tb.TeamID) then    encoder:addu64(1, tb.TeamID)    end
end

function pb.pb_CSTeamCancelMatchTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamCancelMatchTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSTeamCancelMatchTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_CSTeamChangeMatchIDTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamChangeMatchIDTReq) or {} 
    local __TeamID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    local __MatchID = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __MatchID ~= 0 then tb.MatchID = __MatchID end
    local __IsAddMember = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __IsAddMember ~= false then tb.IsAddMember = __IsAddMember end
    tb.Mode = pb.pb_MatchModeInfoDecode(decoder:getsubmsg(4))
    tb.Modes = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.Modes[k] = pb.pb_MatchModeInfoDecode(v)
    end
    local __GroupID = decoder:getu64(6)
    if not PB_USE_DEFAULT_TABLE or __GroupID ~= 0 then tb.GroupID = __GroupID end
    local __AddMemberType = decoder:getu32(12)
    if not PB_USE_DEFAULT_TABLE or __AddMemberType ~= 0 then tb.AddMemberType = __AddMemberType end
    local __spawn_point = decoder:getu64(13)
    if not PB_USE_DEFAULT_TABLE or __spawn_point ~= 0 then tb.spawn_point = __spawn_point end
    return tb
end

function pb.pb_CSTeamChangeMatchIDTReqEncode(tb, encoder)
    if(tb.TeamID) then    encoder:addu64(1, tb.TeamID)    end
    if(tb.MatchID) then    encoder:addu32(2, tb.MatchID)    end
    if(tb.IsAddMember) then    encoder:addbool(3, tb.IsAddMember)    end
    if(tb.Mode) then    pb.pb_MatchModeInfoEncode(tb.Mode, encoder:addsubmsg(4))    end
    if(tb.Modes) then
        for i=1,#(tb.Modes) do
            pb.pb_MatchModeInfoEncode(tb.Modes[i], encoder:addsubmsg(5))
        end
    end
    if(tb.GroupID) then    encoder:addu64(6, tb.GroupID)    end
    if(tb.AddMemberType) then    encoder:addu32(12, tb.AddMemberType)    end
    if(tb.spawn_point) then    encoder:addu64(13, tb.spawn_point)    end
end

function pb.pb_CSTeamChangeMatchIDTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamChangeMatchIDTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSTeamChangeMatchIDTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_CSTeamChangeLeaderTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamChangeLeaderTReq) or {} 
    local __TeamID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    local __NewLeaderID = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __NewLeaderID ~= 0 then tb.NewLeaderID = __NewLeaderID end
    return tb
end

function pb.pb_CSTeamChangeLeaderTReqEncode(tb, encoder)
    if(tb.TeamID) then    encoder:addu64(1, tb.TeamID)    end
    if(tb.NewLeaderID) then    encoder:addu64(2, tb.NewLeaderID)    end
end

function pb.pb_CSTeamChangeLeaderTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamChangeLeaderTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSTeamChangeLeaderTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_CSTeamApplyLeaderTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamApplyLeaderTReq) or {} 
    local __TeamID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    return tb
end

function pb.pb_CSTeamApplyLeaderTReqEncode(tb, encoder)
    if(tb.TeamID) then    encoder:addu64(1, tb.TeamID)    end
end

function pb.pb_CSTeamApplyLeaderTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamApplyLeaderTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSTeamApplyLeaderTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_CSTeamApplyLeaderNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamApplyLeaderNtf) or {} 
    local __TeamID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    local __ApplyPlayerID = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __ApplyPlayerID ~= 0 then tb.ApplyPlayerID = __ApplyPlayerID end
    return tb
end

function pb.pb_CSTeamApplyLeaderNtfEncode(tb, encoder)
    if(tb.TeamID) then    encoder:addu64(1, tb.TeamID)    end
    if(tb.ApplyPlayerID) then    encoder:addu64(2, tb.ApplyPlayerID)    end
end

function pb.pb_CSTeamApplyLeaderResponseTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamApplyLeaderResponseTReq) or {} 
    local __TeamID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    local __ApplyPlayerID = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __ApplyPlayerID ~= 0 then tb.ApplyPlayerID = __ApplyPlayerID end
    local __IsAgree = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __IsAgree ~= false then tb.IsAgree = __IsAgree end
    return tb
end

function pb.pb_CSTeamApplyLeaderResponseTReqEncode(tb, encoder)
    if(tb.TeamID) then    encoder:addu64(1, tb.TeamID)    end
    if(tb.ApplyPlayerID) then    encoder:addu64(2, tb.ApplyPlayerID)    end
    if(tb.IsAgree) then    encoder:addbool(3, tb.IsAgree)    end
end

function pb.pb_CSTeamApplyLeaderResponseTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamApplyLeaderResponseTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSTeamApplyLeaderResponseTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_CSTeamApplyLeaderResponseNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamApplyLeaderResponseNtf) or {} 
    local __TeamID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    local __IsAgree = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __IsAgree ~= false then tb.IsAgree = __IsAgree end
    return tb
end

function pb.pb_CSTeamApplyLeaderResponseNtfEncode(tb, encoder)
    if(tb.TeamID) then    encoder:addu64(1, tb.TeamID)    end
    if(tb.IsAgree) then    encoder:addbool(3, tb.IsAgree)    end
end

function pb.pb_CSTeamEquipPositionChangeTNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamEquipPositionChangeTNtf) or {} 
    local __TeamID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    return tb
end

function pb.pb_CSTeamEquipPositionChangeTNtfEncode(tb, encoder)
    if(tb.TeamID) then    encoder:addu64(1, tb.TeamID)    end
end

function pb.pb_CSTeamIntoEquipTNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamIntoEquipTNtf) or {} 
    local __TeamID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    local __IsIntoState = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __IsIntoState ~= false then tb.IsIntoState = __IsIntoState end
    return tb
end

function pb.pb_CSTeamIntoEquipTNtfEncode(tb, encoder)
    if(tb.TeamID) then    encoder:addu64(1, tb.TeamID)    end
    if(tb.IsIntoState) then    encoder:addbool(2, tb.IsIntoState)    end
end

function pb.pb_CSTeamChangeStateTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamChangeStateTReq) or {} 
    local __TeamID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    local __Set = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __Set ~= false then tb.Set = __Set end
    local __State = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __State ~= 0 then tb.State = __State end
    return tb
end

function pb.pb_CSTeamChangeStateTReqEncode(tb, encoder)
    if(tb.TeamID) then    encoder:addu64(1, tb.TeamID)    end
    if(tb.Set) then    encoder:addbool(2, tb.Set)    end
    if(tb.State) then    encoder:addu32(3, tb.State)    end
end

function pb.pb_CSTeamChangeStateTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamChangeStateTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSTeamChangeStateTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_CSTeamChangeIsRankTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamChangeIsRankTReq) or {} 
    local __TeamId = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __TeamId ~= 0 then tb.TeamId = __TeamId end
    local __isRankedMatch = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __isRankedMatch ~= false then tb.isRankedMatch = __isRankedMatch end
    return tb
end

function pb.pb_CSTeamChangeIsRankTReqEncode(tb, encoder)
    if(tb.TeamId) then    encoder:addu64(1, tb.TeamId)    end
    if(tb.isRankedMatch) then    encoder:addbool(2, tb.isRankedMatch)    end
end

function pb.pb_CSTeamChangeIsRankTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamChangeIsRankTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSTeamChangeIsRankTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_CSTeamEnterWorldTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamEnterWorldTReq) or {} 
    local __TeamID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    local __rule = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __rule ~= 0 then tb.rule = __rule end
    local __spawn_point = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __spawn_point ~= 0 then tb.spawn_point = __spawn_point end
    return tb
end

function pb.pb_CSTeamEnterWorldTReqEncode(tb, encoder)
    if(tb.TeamID) then    encoder:addu64(1, tb.TeamID)    end
    if(tb.rule) then    encoder:addu32(3, tb.rule)    end
    if(tb.spawn_point) then    encoder:addu64(5, tb.spawn_point)    end
end

function pb.pb_CSTeamEnterWorldTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamEnterWorldTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSTeamEnterWorldTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_CSTeamRecruitmentTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamRecruitmentTReq) or {} 
    local __TeamID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    return tb
end

function pb.pb_CSTeamRecruitmentTReqEncode(tb, encoder)
    if(tb.TeamID) then    encoder:addu64(1, tb.TeamID)    end
end

function pb.pb_CSTeamRecruitmentTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamRecruitmentTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSTeamRecruitmentTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_CSTeamGetInviteListReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamGetInviteListReq) or {} 
    return tb
end

function pb.pb_CSTeamGetInviteListReqEncode(tb, encoder)
end

function pb.pb_CSTeamGetInviteListResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamGetInviteListRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.invite_list = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.invite_list[k] = pb.pb_ClientSaveInviteInfoDecode(v)
    end
    return tb
end

function pb.pb_CSTeamGetInviteListResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.invite_list) then
        for i=1,#(tb.invite_list) do
            pb.pb_ClientSaveInviteInfoEncode(tb.invite_list[i], encoder:addsubmsg(2))
        end
    end
end

function pb.pb_CSTeamSaveInviteListReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamSaveInviteListReq) or {} 
    tb.invite_list = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.invite_list[k] = pb.pb_ClientSaveInviteInfoDecode(v)
    end
    return tb
end

function pb.pb_CSTeamSaveInviteListReqEncode(tb, encoder)
    if(tb.invite_list) then
        for i=1,#(tb.invite_list) do
            pb.pb_ClientSaveInviteInfoEncode(tb.invite_list[i], encoder:addsubmsg(2))
        end
    end
end

function pb.pb_CSTeamSaveInviteListResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamSaveInviteListRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSTeamSaveInviteListResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_ClientSaveInviteInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ClientSaveInviteInfo) or {} 
    local __invite_type = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __invite_type ~= 0 then tb.invite_type = __invite_type end
    local __team_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    local __member_num = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __member_num ~= 0 then tb.member_num = __member_num end
    local __match_id = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __match_id ~= 0 then tb.match_id = __match_id end
    local __invite_time = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __invite_time ~= 0 then tb.invite_time = __invite_time end
    tb.inviter = pb.pb_PlayerSimpleInfoDecode(decoder:getsubmsg(6))
    local __state = decoder:getu32(7)
    if not PB_USE_DEFAULT_TABLE or __state ~= 0 then tb.state = __state end
    local __flag = decoder:geti32(8)
    if not PB_USE_DEFAULT_TABLE or __flag ~= 0 then tb.flag = __flag end
    tb.mode = pb.pb_MatchModeInfoDecode(decoder:getsubmsg(9))
    tb.modes = {}
    for k,v in pairs(decoder:getsubmsgary(10)) do
        tb.modes[k] = pb.pb_MatchModeInfoDecode(v)
    end
    return tb
end

function pb.pb_ClientSaveInviteInfoEncode(tb, encoder)
    if(tb.invite_type) then    encoder:addu32(1, tb.invite_type)    end
    if(tb.team_id) then    encoder:addu64(2, tb.team_id)    end
    if(tb.member_num) then    encoder:addi32(3, tb.member_num)    end
    if(tb.match_id) then    encoder:addu32(4, tb.match_id)    end
    if(tb.invite_time) then    encoder:addu64(5, tb.invite_time)    end
    if(tb.inviter) then    pb.pb_PlayerSimpleInfoEncode(tb.inviter, encoder:addsubmsg(6))    end
    if(tb.state) then    encoder:addu32(7, tb.state)    end
    if(tb.flag) then    encoder:addi32(8, tb.flag)    end
    if(tb.mode) then    pb.pb_MatchModeInfoEncode(tb.mode, encoder:addsubmsg(9))    end
    if(tb.modes) then
        for i=1,#(tb.modes) do
            pb.pb_MatchModeInfoEncode(tb.modes[i], encoder:addsubmsg(10))
        end
    end
end

function pb.pb_CSTeamBroadcastTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamBroadcastTReq) or {} 
    local __team_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    local __broadcast_type = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __broadcast_type ~= 0 then tb.broadcast_type = __broadcast_type end
    return tb
end

function pb.pb_CSTeamBroadcastTReqEncode(tb, encoder)
    if(tb.team_id) then    encoder:addu64(1, tb.team_id)    end
    if(tb.broadcast_type) then    encoder:addu32(2, tb.broadcast_type)    end
end

function pb.pb_CSTeamBroadcastTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamBroadcastTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSTeamBroadcastTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_CSTeamBroadcastNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamBroadcastNtf) or {} 
    local __team_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    local __broadcast_type = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __broadcast_type ~= 0 then tb.broadcast_type = __broadcast_type end
    local __player_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    return tb
end

function pb.pb_CSTeamBroadcastNtfEncode(tb, encoder)
    if(tb.team_id) then    encoder:addu64(1, tb.team_id)    end
    if(tb.broadcast_type) then    encoder:addu32(2, tb.broadcast_type)    end
    if(tb.player_id) then    encoder:addu64(3, tb.player_id)    end
end

function pb.pb_CSTeamUpdatePackQuestTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamUpdatePackQuestTReq) or {} 
    local __team_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    tb.PackQuestID = decoder:geti32ary(10)
    local __update_pack_quest_type = decoder:getu32(11)
    if not PB_USE_DEFAULT_TABLE or __update_pack_quest_type ~= 0 then tb.update_pack_quest_type = __update_pack_quest_type end
    return tb
end

function pb.pb_CSTeamUpdatePackQuestTReqEncode(tb, encoder)
    if(tb.team_id) then    encoder:addu64(1, tb.team_id)    end
    if(tb.PackQuestID) then    encoder:addi32(10, tb.PackQuestID)    end
    if(tb.update_pack_quest_type) then    encoder:addu32(11, tb.update_pack_quest_type)    end
end

function pb.pb_CSTeamUpdatePackQuestTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamUpdatePackQuestTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSTeamUpdatePackQuestTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_CSTeamJoinFromRecruitmentTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamJoinFromRecruitmentTReq) or {} 
    local __team_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    tb.PackQuestID = decoder:geti32ary(2)
    return tb
end

function pb.pb_CSTeamJoinFromRecruitmentTReqEncode(tb, encoder)
    if(tb.team_id) then    encoder:addu64(1, tb.team_id)    end
    if(tb.PackQuestID) then    encoder:addi32(2, tb.PackQuestID)    end
end

function pb.pb_CSTeamJoinFromRecruitmentTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamJoinFromRecruitmentTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.Info = pb.pb_TeamInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSTeamJoinFromRecruitmentTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.Info) then    pb.pb_TeamInfoEncode(tb.Info, encoder:addsubmsg(2))    end
end

function pb.pb_CSTeamJoinFromMiniProgramTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamJoinFromMiniProgramTReq) or {} 
    local __team_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    tb.PackQuestID = decoder:geti32ary(2)
    return tb
end

function pb.pb_CSTeamJoinFromMiniProgramTReqEncode(tb, encoder)
    if(tb.team_id) then    encoder:addu64(1, tb.team_id)    end
    if(tb.PackQuestID) then    encoder:addi32(2, tb.PackQuestID)    end
end

function pb.pb_CSTeamJoinFromMiniProgramTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamJoinFromMiniProgramTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.Info = pb.pb_TeamInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSTeamJoinFromMiniProgramTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.Info) then    pb.pb_TeamInfoEncode(tb.Info, encoder:addsubmsg(2))    end
end

function pb.pb_CSTeamDismissTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamDismissTReq) or {} 
    local __team_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    return tb
end

function pb.pb_CSTeamDismissTReqEncode(tb, encoder)
    if(tb.team_id) then    encoder:addu64(1, tb.team_id)    end
end

function pb.pb_CSTeamDismissTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamDismissTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSTeamDismissTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_CSBhdTeamBeginMatchTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSBhdTeamBeginMatchTReq) or {} 
    local __team_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    local __client_version = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __client_version ~= "" then tb.client_version = __client_version end
    return tb
end

function pb.pb_CSBhdTeamBeginMatchTReqEncode(tb, encoder)
    if(tb.team_id) then    encoder:addu64(1, tb.team_id)    end
    if(tb.client_version) then    encoder:addstr(2, tb.client_version)    end
end

function pb.pb_CSBhdTeamBeginMatchTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSBhdTeamBeginMatchTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSBhdTeamBeginMatchTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_CSTeamChangeIsOpenQuickJoinTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamChangeIsOpenQuickJoinTReq) or {} 
    local __TeamId = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __TeamId ~= 0 then tb.TeamId = __TeamId end
    local __isOpenQuickJoin = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __isOpenQuickJoin ~= false then tb.isOpenQuickJoin = __isOpenQuickJoin end
    return tb
end

function pb.pb_CSTeamChangeIsOpenQuickJoinTReqEncode(tb, encoder)
    if(tb.TeamId) then    encoder:addu64(1, tb.TeamId)    end
    if(tb.isOpenQuickJoin) then    encoder:addbool(2, tb.isOpenQuickJoin)    end
end

function pb.pb_CSTeamChangeIsOpenQuickJoinTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamChangeIsOpenQuickJoinTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSTeamChangeIsOpenQuickJoinTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_CSTeamBhdNextLevelReadyTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamBhdNextLevelReadyTReq) or {} 
    local __team_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    local __is_ready = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __is_ready ~= false then tb.is_ready = __is_ready end
    local __chapter_id = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __chapter_id ~= 0 then tb.chapter_id = __chapter_id end
    return tb
end

function pb.pb_CSTeamBhdNextLevelReadyTReqEncode(tb, encoder)
    if(tb.team_id) then    encoder:addu64(1, tb.team_id)    end
    if(tb.is_ready) then    encoder:addbool(2, tb.is_ready)    end
    if(tb.chapter_id) then    encoder:addu32(3, tb.chapter_id)    end
end

function pb.pb_CSTeamBhdNextLevelReadyTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamBhdNextLevelReadyTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSTeamBhdNextLevelReadyTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_CSTeamSessionUpdateTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamSessionUpdateTReq) or {} 
    local __team_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    local __session = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __session ~= "" then tb.session = __session end
    return tb
end

function pb.pb_CSTeamSessionUpdateTReqEncode(tb, encoder)
    if(tb.team_id) then    encoder:addu64(1, tb.team_id)    end
    if(tb.session) then    encoder:addstr(2, tb.session)    end
end

function pb.pb_CSTeamSessionUpdateTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamSessionUpdateTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSTeamSessionUpdateTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_SSTeamGetLeaderInfoReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_SSTeamGetLeaderInfoReq) or {} 
    return tb
end

function pb.pb_SSTeamGetLeaderInfoReqEncode(tb, encoder)
end

function pb.pb_SSTeamGetLeaderInfoResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_SSTeamGetLeaderInfoRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __leader_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __leader_id ~= 0 then tb.leader_id = __leader_id end
    return tb
end

function pb.pb_SSTeamGetLeaderInfoResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.leader_id) then    encoder:addu64(2, tb.leader_id)    end
end

function pb.pb_CSTeamGetShortNumTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamGetShortNumTReq) or {} 
    local __team_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    return tb
end

function pb.pb_CSTeamGetShortNumTReqEncode(tb, encoder)
    if(tb.team_id) then    encoder:addu64(1, tb.team_id)    end
end

function pb.pb_CSTeamGetShortNumTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamGetShortNumTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __team_short_num_id = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __team_short_num_id ~= "" then tb.team_short_num_id = __team_short_num_id end
    local __expire_time = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __expire_time ~= 0 then tb.expire_time = __expire_time end
    return tb
end

function pb.pb_CSTeamGetShortNumTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.team_short_num_id) then    encoder:addstr(2, tb.team_short_num_id)    end
    if(tb.expire_time) then    encoder:addi64(3, tb.expire_time)    end
end

function pb.pb_CSTeamJoinByShortNumReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamJoinByShortNumReq) or {} 
    local __team_short_num_id = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __team_short_num_id ~= "" then tb.team_short_num_id = __team_short_num_id end
    tb.PackQuestID = decoder:geti32ary(2)
    return tb
end

function pb.pb_CSTeamJoinByShortNumReqEncode(tb, encoder)
    if(tb.team_short_num_id) then    encoder:addstr(1, tb.team_short_num_id)    end
    if(tb.PackQuestID) then    encoder:addi32(2, tb.PackQuestID)    end
end

function pb.pb_CSTeamJoinByShortNumResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSTeamJoinByShortNumRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.Info = pb.pb_TeamInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSTeamJoinByShortNumResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.Info) then    pb.pb_TeamInfoEncode(tb.Info, encoder:addsubmsg(2))    end
end

