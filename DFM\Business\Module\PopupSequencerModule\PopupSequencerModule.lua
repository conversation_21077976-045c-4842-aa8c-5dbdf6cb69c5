----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMPopupSequencer)
----- LOG FUNCTION AUTO GENERATE END -----------



-- local Promise   = require "DFM.Business.DataStruct.Common.Base.Promise"
local Promise   = require "DFM.YxFramework.Plugin.Promise.Promise"
local Deep      = require "DFM.Business.DataStruct.Common.Base.Deep"
local Table     = require "DFM.Business.DataStruct.Common.Base.Table"
local Sort      = require "DFM.Business.DataStruct.Common.Base.Sort"
local TokenGen  = require "DFM.Business.DataStruct.Common.Base.TokenGen":New()

local PopupSequencerLogic  = require "DFM.Business.Module.PopupSequencerModule.PopupSequencerLogic"
local PopupSequencerModule = class("PopupSequencerModule", require("DFM.YxFramework.Managers.Module.ModuleBase"))

---@enum EPopupReply
EPopupReply = {
    Continue = 0,
    Stop     = 1,
    Jump     = 2,
}

---@class PopupResult
---@field reply     EPopupReply
---@field extraInfo any

---@class PopupProvider
---@field id                 any
---@field priority           integer
---@field cancelConditions   any[]
---@field interruptEvents    any[]
---@field pauseGuide         boolean
---@field fPopup fun(timingID:any, extraParam:any, interruptPromise:Promise): popupPromise:Promise?

function PopupSequencerModule:Ctor()
    self.InterruptEvents = {
        Guide          = "Guide",
        ForcedGuide    = "ForcedGuide",
        ImportantGuide = "ImportantGuide",
    }
    
    self.CancelConditions = {
        Guide          = "Guide",
        ForcedGuide    = "ForcedGuide",
        ImportantGuide = "ImportantGuide",
    }
    
    self.Timings = {
        LobbyEntrance  = "LobbyEntrance",
    }

    self.Providers = {
        PlayerReturnLobbyPopup      = "PlayerReturnLobbyPopup",
        PlayerReturnDailyMatchPopup = "PlayerReturnDailyMatchPopup",
    }

    ---@type table<any, PopupProvider[]>
    self._popupProvider = {}        -- 所有注册的弹窗展示处理

    ---@type table<any, fun():boolean[]>
    self._conditionProvider = {}    -- 所有注册的弹窗条件

    ---@type table<integer, Promise>
    self._pendingInterruptPromises = {}

    ---@type table<integer, Promise>
    self._pendingPopupPromises = {}
end

---@param providerID any
---@param timingID any
---@param extraParam any
---@return Promise sequenceEndPromise
function PopupSequencerModule:ShowPopup(providerID, timingID, extraParam)
    return self:DoPopupSequence(timingID, extraParam, {providerID})
end

---@param providerIDs any[]
---@return Promise sequenceEndPromise
function PopupSequencerModule:DoPopupSequence(timingID, extraParam, providerIDs)
    local sequence = {}
    for i, id in ipairs(providerIDs) do
        if self._popupProvider[id] then
            table.insert(sequence, self._popupProvider[id])
        end
    end

    local sequenceEndPromise = Promise:NewIns(function()end)
    PopupSequencerLogic._AdvanceSequence(timingID, extraParam, sequence, sequenceEndPromise)
    return sequenceEndPromise
end

---@param condition   any
---@param fCheck      fun():boolean
function PopupSequencerModule:RegisterCancelCondition(condition, fCheck)
    self._conditionProvider[condition] = fCheck
end

---@param popupProvider PopupProvider
function PopupSequencerModule:RegisterPopupProvider(popupProvider)
    if not popupProvider then return end
    self._popupProvider[popupProvider.id] = popupProvider
end

---@param eventID any
function PopupSequencerModule:NotifyInterruptEvent(eventID)
    logwarning("[PopupSequencer] NotifyInterruptEvent - ", eventID)

    for token, promise in pairs(self._pendingInterruptPromises) do
        if promise.__interruptEvents[eventID] then
            logwarning("[PopupSequencer]     interrupt promise resolved: ", promise.__popupProviderID)

            -- 告知弹窗需要被打断，附带原因
            promise:Resolve(eventID)
            self._pendingInterruptPromises[token] = nil
        end
    end
end

function PopupSequencerModule:OnInitModule()

    -- 初始化：注册弹窗取消判断条件 ---------------------------------------------------------------------
    self:RegisterCancelCondition(
        self.InterruptEvents.Guide,
        function()
            return Module.Guide:IsGuidingOrWaitGuide()
        end
    )

    self:RegisterCancelCondition(
        self.InterruptEvents.ForcedGuide,
        function()
            return Module.Guide:IsForcedGuideActive()
        end
    )

    self:RegisterCancelCondition(
        self.InterruptEvents.ImportantGuide,
        function()
            -- TODO: 找引导模块要接口
            return false
        end
    )

    -- 初始化：注册弹窗打断事件 -----------------------------------------------------------------------
    local GuideConfig = require "DFM.Business.Module.GuideModule.GuideConfig"
    GuideConfig.EGuideEvent.evtGuideStart:AddListener(
        function()
            if Module.Guide:IsGuidingOrWaitGuide() then
                self:NotifyInterruptEvent(self.InterruptEvents.Guide)
            end
            if Module.Guide:IsForcedGuideActive() then
                self:NotifyInterruptEvent(self.InterruptEvents.ForcedGuide)
            end
            if false then -- TODO: 找引导模块要接口
                self:NotifyInterruptEvent(self.InterruptEvents.ImportantGuide)
            end
        end
        , self
    )
end

if false then
    Module.PopupSequencer = PopupSequencerModule
end

return PopupSequencerModule