----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGunsmith)
----- LOG FUNCTION AUTO GENERATE END -----------



local GunsmithUIContextFrontend = require "DFM.Business.Module.GunsmithModule.Data.GunsmithUIContextFrontend"
local GunsmithUIContextBackup = require "DFM.Business.Module.GunsmithModule.Data.GunsmithUIContextBackup"
local GunsmithUIStateData = require "DFM.Business.Module.GunsmithModule.Data.GunsmithUIStateData"
local GunsmithUICameraData = require "DFM.Business.Module.GunsmithModule.Data.GunsmithUICameraData"
local GunsmithUIMissionData = require "DFM.Business.Module.GunsmithModule.Data.GunsmithUIMissionData"
local GunsmithUIRangeData = require "DFM.Business.Module.GunsmithModule.Data.GunsmithUIRangeData"

---@class GunsmithUIContext : LuaObject
local GunsmithUIContext = class('GunsmithUIContext', LuaObject)

function GunsmithUIContext:Ctor()
    self._frontend = GunsmithUIContextFrontend:NewIns()         ---@type GunsmithUIContextFrontend
    self._backup = GunsmithUIContextBackup:NewIns()             ---@type GunsmithUIContextBackup
    self._uiStateData = GunsmithUIStateData:NewIns()            ---@type GunsmithUIStateData
    self._cameraData = GunsmithUICameraData:NewIns()            ---@type GunsmithUICameraData
    self._missionData = GunsmithUIMissionData:NewIns()          ---@type GunsmithUIMissionData
    self._rangeData = GunsmithUIRangeData:NewIns()              ---@type GunsmithUIRangeData

    self._uiParam = nil                                         ---@type GunsmithUIParam
    self._propinfo = nil                                        ---@type pb_PropInfo
    self._focusSocket = nil                                     ---@type GunsmithWeaponSocketData
    self._lastFocusSocket = nil                                 ---@type GunsmithWeaponSocketData
    self._weaponItemGUIDFromUser = nil                          ---@type weapon gid 用于记录机匣改装枪购买成功之后的gid

    self._fastEquipResult = nil ---@type FFastEquipResult

    self._uiID = nil
    self._uiLastID = nil
    self._uiOpreationSource = nil

    self._upgradeLockParts = nil

    self._finetuneMainUICheckBoxState = false

    self._bIsSubStageChangeEnter = false

    self._CSWeaponGetRecommendSharingCodeRes = nil

    self._propinfoFromRange = nil                               ---@type pb_PropInfo
    self._groupIDFromRange = nil                                ---@type Number
    self._bUnSycnRangeSolution = false
end

function GunsmithUIContext:Destroy()
    releaseobject(self._frontend)
    self._frontend = nil

    releaseobject(self._backup)
    self._backup = nil

    releaseobject(self._uiStateData)
    self._uiStateData = nil

    self._cameraData = nil

    releaseobject(self._missionData)
    self._missionData = nil

    releaseobject(self._rangeData)
    self._rangeData = nil

    self._uiParam = nil
    self._propinfo = nil

    self._fastEquipResult = nil

    self._upgradeLockParts = nil

    self._CSWeaponGetRecommendSharingCodeRes = nil

    self._propinfoFromRange = nil
    self._groupIDFromRange = nil
    self._bUnSycnRangeSolution = false
end

function GunsmithUIContext:SetUIID(uiID)
    self._uiID = uiID
end

function GunsmithUIContext:SetUILastID(uiLastID)
    self._uiLastID = uiLastID
end

function GunsmithUIContext:SetOpreationSource(source)
    self._uiOpreationSource = source
end

function GunsmithUIContext:SetUIParam(uiParam)
    self._uiParam = uiParam
end

function GunsmithUIContext:SetPropInfo(propinfo)
    self._propinfo = propinfo
end

function GunsmithUIContext:SetCameraType(type)
    self._cameraData:SetCameraType(type)
end

function GunsmithUIContext:SetCameraDefaultPoint(point)
    self._cameraData:SetCameraDefaultPoint(point)
end

function GunsmithUIContext:SetCameraFocusCenterPoint(point)
    self._cameraData:SetCameraFocusCenterPoint(point)
end

function GunsmithUIContext:SetFocusSocket(socket)
    local bIsValid = isvalid(self._focusSocket)
    if bIsValid then
        self:SetLastFocusSocket(self._focusSocket)
    end

    self._focusSocket = socket
end

function GunsmithUIContext:SetLastFocusSocket(socket)
    self._lastFocusSocket = socket
end

function GunsmithUIContext:SetWeaponDescription4SyncFromServer()
    self._backup:SetWeaponDescription4SyncFromServer()
end

function GunsmithUIContext:SetUIState(state)
    self._uiStateData:SetUIState(state)
end

function GunsmithUIContext:SetSimulateUIAnimState(animState)
    self._uiStateData:SetSimulateUIAnimState(animState)
end

function GunsmithUIContext:SetFastEquipResult(result)
    self._fastEquipResult = result
end

function GunsmithUIContext:GetUIID()
    return self._uiID
end

function GunsmithUIContext:GetUILastID()
    return self._uiLastID
end

function GunsmithUIContext:GetOpreationSource()
    return self._uiOpreationSource
end

function GunsmithUIContext:GetFrontend()
    return self._frontend
end

function GunsmithUIContext:GetBackup()
    return self._backup
end

function GunsmithUIContext:GetUIStateData()
    return self._uiStateData
end

function GunsmithUIContext:GetPropInfo()
    return self._propinfo
end

function GunsmithUIContext:GetItemID()
    return self._uiParam:GetItemID()
end

function GunsmithUIContext:GetGUID()
    return self._uiParam:GetGUID()
end

function GunsmithUIContext:GetGroupID()
    return self._uiParam:GetGroupID()
end

function GunsmithUIContext:GetIsFromUserPropInfo()
    local propinfo = self._uiParam:GetUserPropInfo()
    local bUser = isvalid(propinfo)
    return bUser, propinfo
end

function GunsmithUIContext:GetMissionID()
    return self._uiParam:GetMissionID()
end

function GunsmithUIContext:GetWeaponDescription4Frontend()
    return self._frontend:GetWeaponDescription()
end

function GunsmithUIContext:GetWeaponDescription4Backup()
    return self._backup:GetWeaponDescription()
end

function GunsmithUIContext:GetWeaponDescription4BackupServer()
    return self._backup:GetWeaponDescription4Server()
end

function GunsmithUIContext:GetWeaponDescription4BackupLocal()
    return self._backup:GetWeaponDescription4Local()
end

function GunsmithUIContext:GetWeaponDescription4Sync()
    return self._backup:GetWeaponDescription4Sync()
end

function GunsmithUIContext:GetWeaponSocketSnapshot4FrontEnd()
    return self._frontend:GetWeaponSocketSnapshot()
end

function GunsmithUIContext:GetWeaponSocketSnapshot4Backup()
    return self._backup:GetWeaponSocketSnapshot()
end

function GunsmithUIContext:GetUIState()
    return self._uiStateData:GetUIState()
end

function GunsmithUIContext:GetIsSimulateState()
    return self._uiStateData:GetIsSimulateState()
end

function GunsmithUIContext:GetSimulateUIAnimState()
    return self._uiStateData:GetSimulateUIAnimState()
end

function GunsmithUIContext:GetFastEquipResult()
    return self._fastEquipResult
end

function GunsmithUIContext:GetCameraType()
    return self._cameraData:GetCameraType()
end

function GunsmithUIContext:GetCameraDefaultPoint()
    return self._cameraData:GetCameraDefaultPoint()
end

function GunsmithUIContext:GetCameraFocusCenterPoint()
    return self._cameraData:GetCameraFocusCenterPoint()
end

function GunsmithUIContext:GetFocusSocket()
    return self._focusSocket
end

function GunsmithUIContext:GetLastFocusSocket()
    return self._lastFocusSocket
end

function GunsmithUIContext:GetUIParam()
    return self._uiParam
end

function GunsmithUIContext:SetWeaponItemGUIDFormUser(weaponGUID)
    self._weaponItemGUIDFromUser = weaponGUID
end

function GunsmithUIContext:GetWeaponItemGUIDFormUser()
    return self._weaponItemGUIDFromUser
end

-- 任务相关里逻辑 start----
function GunsmithUIContext:GetMissionData()
    return self._missionData
end

function GunsmithUIContext:MissionDataReset()
    return self._missionData:Reset()
end

function GunsmithUIContext:UpdateMissionData()
    local MissionID = self:GetMissionID()
    self._missionData:UpdateMissionData(MissionID)
end

function GunsmithUIContext:UpdateMissionEquippedAndUnequippedPartList()
    self._missionData:UpdateMissionEquippedAndUnequippedPartList()
end

function GunsmithUIContext:GetMissionWeaponID()
    return self._missionData:GetMissionWeaponID()
end

function GunsmithUIContext:CanShowMission()
    return self._missionData:CanShowMission()
end

function GunsmithUIContext:GetMissionTitle()
    return self._missionData:GetMissionTitle()
end

function GunsmithUIContext:GetMissionDes()
    return self._missionData:GetMissionDes()
end

function GunsmithUIContext:GetMissionRawItemIDList()
    return self._missionData:GetMissionRawItemIDList()
end

function GunsmithUIContext:GetMissionRawObjective()
    return self._missionData:GetMissionRawObjective()
end

function GunsmithUIContext:GetMissionItemBase()
    return self._missionData:GetMissionItemBase()
end

function GunsmithUIContext:SetEquippedPartItemIDList(equippedPartItemIDList)
    self._missionData:SetEquippedPartItemIDList(equippedPartItemIDList)
end

function GunsmithUIContext:GetEquippedPartItemIDList()
    return self._missionData:GetEquippedPartItemIDList()
end

function GunsmithUIContext:SetUnequippedPartItemIDList(unequippedPartItemIDList)
    self._missionData:SetUnequippedPartItemIDList(unequippedPartItemIDList)
end

function GunsmithUIContext:GetUnequippedPartItemIDList()
    return self._missionData:GetUnequippedPartItemIDList()
end

function GunsmithUIContext:SetShowSceneSocketItemMissionUI(bShowSceneSocketItemMissionUI)
    self._missionData:SetShowSceneSocketItemMissionUI(bShowSceneSocketItemMissionUI)
end

function GunsmithUIContext:SetShowMainPreviewMissionUI(bShowMainPreviewMissionUI)
    self._missionData:SetShowMainPreviewMissionUI(bShowMainPreviewMissionUI)
end

function GunsmithUIContext:IsShowSceneSocketItemMissionUI()
    return self._missionData:IsShowSceneSocketItemMissionUI()
end

function GunsmithUIContext:IsShowMainPreviewMissionUI()
    return self._missionData:IsShowMainPreviewMissionUI()
end
-- 任务相关里逻辑 end----

-- 靶场相关数据 start----
function GunsmithUIContext:GetRangeGUID()
    return self._rangeData:GetRangeGUID()
end

function GunsmithUIContext:SetSOLWeaponSkinID(weaponID, skinID, skinGUID)
    self._rangeData:SetSOLWeaponSkinID(weaponID, skinID, skinGUID)
end

function GunsmithUIContext:GetSOLWeaponSkinIDFromBaseWeaponID(weaponID)
    return self._rangeData:GetSOLWeaponSkinIDFromBaseWeaponID(weaponID)
end

function GunsmithUIContext:GetSOLWeaponSkinGUIDFromBaseWeaponID(weaponID)
    return self._rangeData:GetSOLWeaponSkinGUIDFromBaseWeaponID(weaponID)
end

function GunsmithUIContext:SetSOLWeaponPendantID(weaponID, pendantID, pendantGUID)
    self._rangeData:SetSOLWeaponPendantID(weaponID, pendantID, pendantGUID)
end

function GunsmithUIContext:GetSOLWeaponPendantIDFromBaseWeaponID(weaponID)
    return self._rangeData:GetSOLWeaponPendantIDFromBaseWeaponID(weaponID)
end

function GunsmithUIContext:GetSOLWeaponPendantGUIDFromBaseWeaponID(weaponID)
    return self._rangeData:GetSOLWeaponPendantGUIDFromBaseWeaponID(weaponID)
end

function GunsmithUIContext:GetSlotType()
    return self._uiParam:GetSlotType()
end
-- 靶场相关数据 end----

function GunsmithUIContext:SetUpgradeLockParts(upgradeLockParts)
    self._upgradeLockParts = upgradeLockParts
end

function GunsmithUIContext:GetUpgradeLockParts()
    return self._upgradeLockParts
end

function GunsmithUIContext:IsUpgradeLockPart(inPartItemID)
    local bIsEmpty = table.isempty(self._upgradeLockParts)
    if bIsEmpty then
        return false
    end
    local bIsResult = false
    for _, lockPart in pairs(self._upgradeLockParts) do
        if tonumber(lockPart) == tonumber(inPartItemID) then
            bIsResult = true
            break
        end
    end
    return bIsResult
end

function GunsmithUIContext:FinetuneMainUICheckBoxStateReset()
    self._finetuneMainUICheckBoxState = false
end

function GunsmithUIContext:SetFinetuneMainUICheckBoxState(finetuneMainUICheckBoxState)
    self._finetuneMainUICheckBoxState = finetuneMainUICheckBoxState
end

function GunsmithUIContext:GetFinetuneMainUICheckBoxState()
    return self._finetuneMainUICheckBoxState
end

function GunsmithUIContext:SetIsSubStageChangeEnter(bIsSubStageChangeEnter)
    self._bIsSubStageChangeEnter = bIsSubStageChangeEnter
end

function GunsmithUIContext:GetIsSubStageChangeEnter()
    return self._bIsSubStageChangeEnter
end

function GunsmithUIContext:SetCSWeaponGetRecommendSharingCodeRes(res)
    self._CSWeaponGetRecommendSharingCodeRes = res
end

function GunsmithUIContext:GetKOLRecommend()
    local bIsValid = self._CSWeaponGetRecommendSharingCodeRes ~= nil
    bIsValid = bIsValid and self._CSWeaponGetRecommendSharingCodeRes.result and self._CSWeaponGetRecommendSharingCodeRes.result == 0
    bIsValid = bIsValid and self._CSWeaponGetRecommendSharingCodeRes.kol_recommend ~= nil
    if not bIsValid then
        return nil
    end
    return self._CSWeaponGetRecommendSharingCodeRes.kol_recommend
end

function GunsmithUIContext:GetSystemRecommend()
    local bIsValid = self._CSWeaponGetRecommendSharingCodeRes ~= nil
    bIsValid = bIsValid and self._CSWeaponGetRecommendSharingCodeRes.result and self._CSWeaponGetRecommendSharingCodeRes.result == 0
    bIsValid = bIsValid and self._CSWeaponGetRecommendSharingCodeRes.system_recommend ~= nil
    if not bIsValid then
        return nil
    end
    return self._CSWeaponGetRecommendSharingCodeRes.system_recommend
end


function GunsmithUIContext:SetPropinfoFromRange(propinfo)
    self._propinfoFromRange = propinfo
end

function GunsmithUIContext:GetPropinfoFromRange()
    return self._propinfoFromRange
end

function GunsmithUIContext:SetGroupIDFromRange(groupID)
    self._groupIDFromRange = groupID
end

function GunsmithUIContext:GetGroupIDFromRange()
    return self._groupIDFromRange
end

function GunsmithUIContext:SetUnSycnRangeSolution(bUnSycnRangeSolution)
    self._bUnSycnRangeSolution = bUnSycnRangeSolution
end

function GunsmithUIContext:GetUnSycnRangeSolution()
    return self._bUnSycnRangeSolution
end

return GunsmithUIContext