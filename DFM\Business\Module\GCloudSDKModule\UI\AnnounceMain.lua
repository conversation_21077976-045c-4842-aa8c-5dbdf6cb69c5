----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGCloudSDK)
----- LOG FUNCTION AUTO GENERATE END -----------



local function log(...)
    logerror("[AnnounceMain]",...)
end
local Json = require("DFM.YxFramework.Plugin.Json.Json").createJson()

local USlateBlueprintLibrary = import "SlateBlueprintLibrary"
local UDFMGameplayGlobalDelegates = import "DFMGameplayGlobalDelegates"
local UGPInputHelper = import "GPInputHelper"
local EDescendantScrollDestination = import "EDescendantScrollDestination"
local AnnouncementButton = require("DFM.Business.Module.GCloudSDKModule.UI.AnnouncementButton")


local AnnounceMain = ui("AnnounceMain")
function AnnounceMain:Ctor()
    log("AnnounceMain:Ctor")

    self._wtTitleText = self:Wnd("Text_Title", UITextBlock)

    -- 由于这个时机太早，通用控件库模块还没加载，所以不能用DFCommonButton，只能自己Wnd
    -- 取消按钮
    self._wtCancelBtn = self:Wnd("wtCommonButtonV1S2", DFCommonButtonOnly)
    self._wtCancelBtn:Event("OnClicked", self.CancelClick, self)

    -- 确认按钮
    self._wtConfirmOldBtn = self:Wnd("wtCommonButtonV1S1", DFCommonButtonOnly)
    self._wtConfirmOldBtn:Collapsed()
    self._wtConfirmBtn = self:Wnd("DFAnnouncementButtonV1", DFCommonButtonOnly)
    self._wtConfirmBtn:Visible()
    self._wtConfirmBtn:Event("OnClicked", self.ConfirmClick, self)

    self._wtRightBtn = self:Wnd("DFButton_RightBtn",UIButton)
    self._wtRightBtn:Event("OnClicked", self.OnRightBtnClick, self)

    self._wtLeftBtn = self:Wnd("DFButton_LeftBtn",UIButton)
    self._wtLeftBtn:Event("OnClicked", self.OnLeftBtnClick, self)

    -- 右上角的X按钮
    self._wtXBtn = self:Wnd("wtCloseBtn",UIButton)
    self._wtXBtn:Event("OnClicked", self.XClick, self)

    -- 更新提示文本
    self._wtUpdateTB = self:Wnd("wtUpdateTB", UITextBlock)

    self._wtContentSB = self:Wnd("ContentSB", UIScrollBox)
    self._wtBtnHB = self:Wnd("BtnHB",UIWidgetBase)
    self._contentWidgetList = {}
    self._btnWidgetList = {}
    self._contentList = {}
    self._subContentList = {}
    self._contentNum = 0

    self._index = 1

    self.DISTANCE = 200

    self._pressPosX = 0.0
    self._pressPosY = 0.0
    self._releasePosX = 0.0
    self._enableInteract = false

    self._enableLeaveEvent = false

    self._isLoginState = false -- 标记是否是从登录界面唤起的，如果是关闭的时候就需要走紧急公告的事件逻辑

    self._newVersion = "" -- 新版本版本号，默认从Dolphin获取
    self._versionStr = ""
    self._downloadSizeStr = ""
end

-- 注册点击事件
function AnnounceMain:OnShowBegin()
    loginfo("[AnnounceMain] OnShowBegin()")
    local gameInst = GetGameInstance()
    self._buttonUpHandle = UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonUpEvent:Add(self._OnHandleMouseButtonUpEvent, self)
	self._buttonDownHandle = UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonDownEvent:Add(self._OnHandleMouseButtonDownEvent, self)
	self._buttonMoveHandle = UDFMGameHudDelegates.Get(gameInst).OnHandleMouseMoveEvent:Add(self._OnHandleMouseButtonMoveEvent, self)
end

function AnnounceMain:OnHideBegin()

end

function AnnounceMain:OnClose()
    loginfo("[AnnounceMain] OnClose")
    local gameInst = GetGameInstance()
	if self._buttonUpHandle then
		UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonUpEvent:Remove(self._buttonUpHandle)
		self._buttonUpHandle = nil
	end
	if self._buttonDownHandle then
		UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonDownEvent:Remove(self._buttonDownHandle)
		self._buttonDownHandle = nil
	end
	if self._buttonMoveHandle then
		UDFMGameHudDelegates.Get(gameInst).OnHandleMouseMoveEvent:Remove(self._buttonMoveHandle)
		self._buttonMoveHandle = nil
	end
end

function AnnounceMain:_OnHandleMouseButtonUpEvent(mouseEvent)
    log("AnnounceMain:_OnHandleMouseButtonUpEvent",self._moveDir)
    if not self._enableInteract then
        return
    end
    self._enableMoveCheck = false
    self._enableInteract = false

    local targetPos = mouseEvent:GetScreenSpacePosition()
    self._releasePosX = targetPos.X
    self:OperateScroll()
end

function AnnounceMain:_OnHandleMouseButtonDownEvent(mouseEvent)
    log("AnnounceMain:_OnHandleMouseButtonDownEvent")
    self._beginTouchPos = mouseEvent:GetScreenSpacePosition()
    self._pressPosX = self._beginTouchPos.X
    self._pressPosY = self._beginTouchPos.Y
    self._startDependMove = true
    self._moveDir = true -- true for Vertical, false for Horizon(横向)
    self._enableLeaveEvent = true
    self._enableMoveCheck = true

    local rootGeometry = self._wtContentSB:GetCachedGeometry()
    local isUnder = USlateBlueprintLibrary.IsUnderLocation(rootGeometry, self._beginTouchPos)
    if isUnder then
        self._enableInteract = true
        self._horizonScrollOffset = self._wtContentSB:GetScrollOffset()
        local weakContentWidget = Facade.UIManager:GetSubUI(self, UIName2ID.AnnounceSubContent, self._contentWidgetList[self._index or 1])
        local contentWidgetIns = getfromweak(weakContentWidget)
        if contentWidgetIns then
            self._verticalScrollOffset = contentWidgetIns:GetScrollOffset()
        else
            logerror("Get Content Widget Instance Failed!!!")
        end
    end
end

function AnnounceMain:_OnHandleMouseButtonMoveEvent(mouseEvent)
    if not self._enableInteract then
        return
    end

    if not self._enableMoveCheck then
        return
    end

    log("AnnounceMain:OnNativeOnTouchMoved")

    local tmpInitPos = mouseEvent:GetScreenSpacePosition()

    local deltaX = tmpInitPos.X - self._pressPosX
    local deltaY = tmpInitPos.Y - self._pressPosY
    -- 判断滑的方向 -- 判断一次
    if self._startDependMove then
        local absDeltaX = deltaX>0 and deltaX or (-deltaX)
        local absDeltaY = deltaY>0 and deltaY or (-deltaY)
        if absDeltaX == 0 and absDeltaY == 0 then
            return
        end
        if absDeltaY < absDeltaX then
            self._moveDir = false
        else
            self._moveDir = true
        end
    end
    self._startDependMove = false

    if self._moveDir then    
        local weakContentWidget = Facade.UIManager:GetSubUI(self, UIName2ID.AnnounceSubContent, self._contentWidgetList[self._index])
        local contentWidgetIns = getfromweak(weakContentWidget)
        if contentWidgetIns then
            contentWidgetIns:SetScrollOffset(math.max(self._verticalScrollOffset-deltaY))
        end
    else
        self._wtContentSB:SetScrollOffset(math.max(self._horizonScrollOffset-deltaX,0))
    end
end

function AnnounceMain:OnMouseLeave()
    loginfo("AnnounceMain:OnMouseLeave()")
    self._enableInteract = false
    if self._enableLeaveEvent then
        self:OperateScroll()
    end
end

function AnnounceMain:OperateScroll()
    if self._releasePosX  == 0 or self._pressPosX == 0 then
        return
    end

    log("AnnounceMain:OperateScroll 跳转",self._index,self._releasePosX,self._pressPosX)

    self._preIndex = self._index -- 保存当前状态，preIndex用于计算下一个目标
    if self._releasePosX < self._pressPosX - self.DISTANCE then
        self._index = math.min(self._index+1,self._contentNum)
    elseif self._releasePosX < self._pressPosX then
    elseif self._releasePosX > self._pressPosX+self.DISTANCE then
        self._index = math.max(self._index-1,1)
    elseif self._releasePosX > self._pressPosX then
    end

    -- 重置点击位置
    self._releasePosX = 0
    self._pressPosX = 0

    self:ScrollToIndex(self._index, true)
end

function AnnounceMain:OnInitExtraData(contentList, _type, isLoginState, newVersion, bIsAppUpdate, versionStr, downloadSizeStr)
    log("AnnounceMain:OnInitExtraData",_type, tostring(isLoginState), newVersion, bIsAppUpdate, versionStr, downloadSizeStr)
    -- 对几条公告做排序
    self._type = _type
    self._contentNum = #contentList or 0
    self._newVersion = newVersion or ""
    self._bIsAppUpdate = bIsAppUpdate
    self._versionStr = versionStr
    self._downloadSizeStr = downloadSizeStr
    if(self._contentNum == 0) then
    	-- self:ClosePanel()
	    return
    end

    self._isLoginState = isLoginState

    self._contentList = {}
    for i = 1,self._contentNum do
        self._contentList[i] = contentList[i]
    end
end

function AnnounceMain:OnOpen()
    log("AnnounceMain:OnOpen",self._contentNum,self._type,(not self._contentNum or self._contentNum == 0 or not self._type))

    -- 清除已存在的所有内容
    self._wtContentSB:ClearChildren()
    self._wtBtnHB:ClearChildren()

    -- 设置确定按钮文本
    if self._type == EAnnounceContentType.ForceUpdate or self._type == EAnnounceContentType.ShouldUpdate then
        self._wtConfirmBtn:SetMainTitle(Module.GCloudSDK.Config.Loc.UpdateNow)
        self._wtCancelBtn:SetMainTitle(Module.GCloudSDK.Config.Loc.NotUpdate)
    else
        self._wtConfirmBtn:SetMainTitle(Module.GCloudSDK.Config.Loc.DefaultConfirmBtnText)
        self._wtCancelBtn:SetMainTitle(Module.GCloudSDK.Config.Loc.DefaultCancelBtnText)
    end

    local announceUpdateTips = Module.GCloudSDK.Field:GetAnnounceUpdateTips()
    if self._bIsAppUpdate and not string.isempty(announceUpdateTips) then
        self._newVersion = string.format(announceUpdateTips, self._versionStr, self._downloadSizeStr)
        loginfo("AnnounceMain:OnOpen announceUpdateTips:", announceUpdateTips)
    end

    -- 根据公告内容决定如何展示
    if(self._type == EAnnounceContentType.Emergency) then
        self._wtUpdateTB:Collapsed()
        self._wtCancelBtn:Collapsed()
        self._wtXBtn:Collapsed()
    elseif(self._type == EAnnounceContentType.ForceUpdate) then
        if string.isempty(self._newVersion) then
            self._wtUpdateTB:Collapsed() -- 没有下发就不展示这个文本了
        else
            self._wtUpdateTB:SetText(self._newVersion)
            self._wtUpdateTB:Visible()
        end
        self._wtCancelBtn:Collapsed() -- 强更隐藏取消按钮
        self._wtXBtn:Collapsed() -- 强更隐藏右上角|X|关闭按钮
    elseif (self._type == EAnnounceContentType.ShouldUpdate) then
        if string.isempty(self._newVersion) then
            self._wtUpdateTB:Collapsed() -- 没有下发就不展示这个文本了
        else
            self._wtUpdateTB:SetText(self._newVersion)
            self._wtUpdateTB:Visible()
        end

        if not self._bIsAppUpdate then
            self._wtCancelBtn:Collapsed() -- 资源非强更隐藏取消按钮
        else
            
        end

        self._wtXBtn:Collapsed() -- 非强更也需要隐藏右上角的X
        -- self._wtCancelText:SetText(Module.GCloudSDK.Config.Loc.NotUpdate)
    end

    local subContentList = {}
    for i = 1,self._contentNum do
        local content = self._contentList[i] -- dfmNoticeInfo
        if content.contentType == EAnnounceContentDataType.EText then
            local subContent = {}
            local textInfo = content.textInfo
            subContent.contentType = content.contentType
            subContent.textInfo = textInfo
            table.insert(subContentList, subContent)
        elseif content.contentType == EAnnounceContentDataType.EImage then
            local num = content.picUrlList:Num()
            if num > 0 then
                for j = 1, num do
                    local subContent = {}
                    local textInfo = content.textInfo
                    local picInfo = content.picUrlList:Get(j - 1)
                    subContent.contentType = content.contentType
                    subContent.textInfo = textInfo
                    subContent.picInfo = picInfo
                    table.insert(subContentList, subContent)
                end
            end
        elseif content.contentType == EAnnounceContentDataType.EWeb then
            local subContent = {}
            local webUrl = content.webUrl
            subContent.contentType = content.contentType
            subContent.webUrl = webUrl
            table.insert(subContentList, subContent)
        end
    end

    for index, subContent in ipairs(subContentList) do
        self:CreateSubContent(index, subContent)
    end
    
    local subContenNum = #subContentList
    if subContenNum > 0 then
        self._contentNum = subContenNum
    end

    -- 只有一条内容或者没内容的时候不展示切页
    if self._contentNum <= 1 then
        self._wtBtnHB:Collapsed()
    else
        self._wtBtnHB:SelfHitTestInvisible()
    end

    self._subContentList = subContentList
    self._index = 1
    self:ScrollToIndex(self._index) -- 初始化显示的地方
end

function AnnounceMain:CreateSubContent(index, noticeInfo)
    local weakContentWidget, contentInstanceID = Facade.UIManager:AddSubUI(self, UIName2ID.AnnounceSubContent, self._wtContentSB)
    local contentWidgetIns = getfromweak(weakContentWidget)
    -- self._wtContentSB:AddChild(contentWidget)
    self._contentWidgetList[index] = contentInstanceID
    if contentWidgetIns then
        contentWidgetIns:SetInfo(noticeInfo)
    end

    if self._contentNum > 1 then
        local weakBtnWidget, btnInstanceID = Facade.UIManager:AddSubUI(self, UIName2ID.DFButtonCarousel2, self._wtBtnHB)
        local btnWidgetIns = getfromweak(weakBtnWidget)
        if btnWidgetIns then
            btnWidgetIns:Event("OnClicked", self.ScrollToIndex, self, index)
        end
        -- self._wtBtnHB:AddChild(btnWidget)
        self._btnWidgetList[index] = btnInstanceID
    end
end

function AnnounceMain:ScrollToIndex(i, bEnableAutoScroll) -- 只做显示
    log("AnnounceMain:ScrollToIndex",i)
--     local bEnableAutoScroll = bEnableAutoScroll or false  -- 是否展示自动滑动
    self._index = i
    self:UpdateLeftRightBtnVisible()
    -- self._dir = self._index - self._preIndex
    for index, widgetInstanceID in pairs(self._btnWidgetList) do
        local weakBtnWidget = Facade.UIManager:GetSubUI(self, UIName2ID.DFButtonCarousel2, widgetInstanceID)
        local btnWidgetIns = getfromweak(weakBtnWidget)
    
        if btnWidgetIns and index == i then
            btnWidgetIns:SetIsEnabledStyle(false)
        elseif btnWidgetIns then
            btnWidgetIns:SetIsEnabledStyle(true)
        end
    end

    if self._wtContentSB and i > 0 then
        local child = self._wtContentSB:GetItemByIndex(i - 1)
        if child then
            self._wtContentSB:ScrollWidgetIntoView(child, true, EDescendantScrollDestination.IntoView)
        end
    end
    self._enableLeaveEvent = false

    -- 设置标题
    if self._subContentList[self._index] then
        local content = self._subContentList[self._index] -- dfmNoticeInfo
        local noticeTitle = content.textInfo.noticeTitle or ""
        if self._noticeTitle ~= noticeTitle then
            self._wtTitleText:SetText(noticeTitle)
            self._noticeTitle = noticeTitle
        end
    else
        self._wtTitleText:SetText("")
    end
end

function AnnounceMain:CancelClick()
    log("AnnounceMain:CancelClick",self._type)
    if (self._type == EAnnounceContentType.ForceUpdate) then
        self:QuitGame()
    elseif (self._type == EAnnounceContentType.ShouldUpdate) then
        self:ClosePanel(false)
    else
        self:ClosePanel(false)
    end
end

function AnnounceMain:ConfirmClick()
    log("AnnounceMain:ConfirmClick",self._type)
    if self._type == EAnnounceContentType.ForceUpdate or self._type == EAnnounceContentType.ShouldUpdate then
        -- Update
        self:UpdateGame()
    else
        self:ClosePanel(true)
    end
end

function AnnounceMain:UpdateLeftRightBtnVisible()
    if self._contentNum <= 1 then
        self._wtRightBtn:Collapsed()
        self._wtLeftBtn:Collapsed()
    elseif self._index <= 1 then
        self._wtRightBtn:Visible()
        self._wtLeftBtn:Collapsed()
    elseif self._index >= self._contentNum then
        self._wtRightBtn:Collapsed()
        self._wtLeftBtn:Visible()
    else
        self._wtRightBtn:Visible()
        self._wtLeftBtn:Visible()
    end
end

function AnnounceMain:OnRightBtnClick()
    self._index = math.clamp(self._index + 1, 1, self._contentNum)
    self:ScrollToIndex(self._index)
end

function AnnounceMain:OnLeftBtnClick()
    self._index = math.clamp(self._index - 1, 1, self._contentNum)
    self:ScrollToIndex(self._index)
end

function AnnounceMain:XClick()
    log("AnnounceMain:XClick")
    self:CancelClick()
end


function AnnounceMain:ClosePanel(bConfirm)
    log("AnnounceMain:ClosePanel")
    if self._wtBtnHB then
        self._wtBtnHB:Collapsed()
    end

    if (self._type == EAnnounceContentType.Emergency) then
        if self._isLoginState then
            log("AnnounceMain:ClosePanel _isLoginState")
            Module.GCloudSDK.Config.Events.evtOnEmergencyAnnounceClosed:Invoke() -- 触发在登录界面的特殊逻辑
        else
            log("AnnounceMain:ClosePanel not _isLoginState")
        end
        Facade.UIManager:CloseUI(self)
    elseif (self._type == EAnnounceContentType.ForceUpdate) then
        -- 发事件
        -- 强更公告前序已经在ConfirmClick里处理过一次了，这里就没必要再发一次了
        -- Module.GCloudSDK.Config.Events.evtOnAnnouncementFinishPanel:Invoke(self._type,bConfirm)
        -- -- UDFMGameplayGlobalDelegates.Get(GetWorld()):OnLoadAnnounceEnd(true)
        Facade.UIManager:CloseUI(self)
    else
        Module.GCloudSDK.Config.Events.evtOnAnnouncementFinishPanel:Invoke(self._type,bConfirm)
        Facade.UIManager:CloseUI(self)
        -- self._wtCancelBtn:Collapsed()
    end
end
function AnnounceMain:QuitGame()
    log("AnnounceMain:QuitGame")
    local UKismetSystemLibrary = import "KismetSystemLibrary"
    local EQuitPreference = import "EQuitPreference"
    UKismetSystemLibrary.QuitGame(GetGameInstance(), nil, EQuitPreference.Quit, false)
--    Facade.UIManager:CloseUI(self)
end

--是否跳转应用市场下载
function AnnounceMain:IsAppStoreDownload()
    if not self._bIsAppUpdate then
        return false
    end
    return Module.VersionUpdate:IsAppStoreDownload()
end

--获取应用市场地址
function AnnounceMain:GetAppStoreUrlScheme()
    local urlScheme = ""
    if self._contentList[1] then 
        local extraData = self._contentList[1].ExtraData
        local urlScheme = extraData:Get("urlScheme")
        if not string.isempty(urlScheme) then
            logerror("AnnounceMain:_GetAppStoreUrlScheme ExtraData urlScheme:", urlScheme)
            return urlScheme
        end
    end
    if IsBuildRegionCN() then
        if PLATFORM_IOS then
            urlScheme = "https://itunes.apple.com/cn/app/id1642894547"
        elseif PLATFORM_ANDROID then
            --国内安卓不跳转商店
        elseif PLATFORM_OPENHARMONY then
            if BUILD_REGION_CN_EXPER then
                urlScheme = "https://appgallery.huawei.com/app/detail?id=com.tencent.tmgp.dfmexper.hw"
            else
                urlScheme = "https://appgallery.huawei.com/app/detail?id=com.tencent.tmgp.dfm.hw"
            end
        end
    elseif IsBuildRegionGlobal() then
        if PLATFORM_IOS then
            urlScheme = "https://apps.apple.com/app/apple-store/id6451399876"
        elseif PLATFORM_ANDROID then
            urlScheme = "https://play.google.com/store/apps/details?id=com.proxima.dfm"
            if IsChannelStoreAPK() then
                local storeChannel = Server.SDKInfoServer:GetStoreChannel()
                if storeChannel == 10002 then
                    urlScheme = "https://apps.samsung.com/appquery/appDetail.as?appId=com.proxima.dfm.samsung"
                elseif storeChannel == 10003 then
                    urlScheme = "https://appgallery.huawei.com/app/C114894079"
                end
                logerror("AnnounceMain:_GetAppStoreUrlScheme urlScheme:", urlScheme, "-storeChannel:", storeChannel)
            end
        end
    elseif IsBuildRegionGA() then
        if PLATFORM_IOS then
            urlScheme = "https://apps.apple.com/app/apple-store/id6670604287"
        elseif PLATFORM_ANDROID then
            urlScheme = "https://play.google.com/store/apps/details?id=com.garena.game.df"
        end
    end
    return urlScheme
end

function AnnounceMain:UpdateGame()
    local bIsAppStoreDownload = self:IsAppStoreDownload()
    if bIsAppStoreDownload then
        local urlScheme = self:GetAppStoreUrlScheme()
        logerror("AnnounceMain:UpdateGame urlScheme:", urlScheme)
        if not string.isempty(urlScheme) then
            logerror("AnnounceMain:UpdateGame skip to download url")
            Module.GCloudSDK:OpenUrl(urlScheme, 1, false, false, nil, true)
        end
    else
        --如果不用跳转商店则继续走游戏内更新流程
        Module.GCloudSDK.Config.Events.evtOnAnnouncementBeginVersionUpdate:Invoke()
        self:ClosePanel(true)
    end
end

function AnnounceMain:_OnQuitBtnClicked()
    -- @yixiaoguan 实现封装暴露到Module
    local fCancelLeaveGameHandle = CreateCallBack(function(self)
    end, self)

    local fConfirmLeaveGameHandle = CreateCallBack(function(self)
    end, self)

    if Facade.ModuleManager:IsModuleValid("CommonBar") then
        Module.CommonBar:FlowBackQuitGame(fCancelLeaveGameHandle, fConfirmLeaveGameHandle)
    end
end

function AnnounceMain:VerifyClose()
    return true
end

return AnnounceMain