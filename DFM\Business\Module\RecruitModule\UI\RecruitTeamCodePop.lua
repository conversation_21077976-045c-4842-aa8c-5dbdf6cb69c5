----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRecruit)
----- LOG FUNCTION AUTO GENERATE END -----------



local RecruitTeamCodePop = ui("RecruitTeamCodePop")

local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"

function RecruitTeamCodePop:Ctor()
    self._wtCommonPopWB = self:Wnd("wtCommonPopWB", CommonPopWindows)
    self._wtCommonPopWB:BindCloseCallBack(CreateCallBack(self._CloseSelf, self))

    self._wtJoinWB = self:Wnd("wtJoinWB", DFCommonButtonOnly)
    self._wtJoinWB:Event("OnClicked", self._OnJoinBtnClicked, self)

    self._wtShareWB = self:Wnd("wtShareWB", DFCommonButtonOnly)
    self._wtShareWB:Event("OnClicked", self._OnShareBtnClicked, self)
end

function RecruitTeamCodePop:OnShowBegin()
    self:_EnableGamepadFeature()
end

function RecruitTeamCodePop:OnHideBegin()
    self:_DisableGamepadFeature()
end

function RecruitTeamCodePop:_OnJoinBtnClicked()
    Module.CommonTips:ShowSimpleTip("点击了加入按钮！")
end

function RecruitTeamCodePop:_OnShareBtnClicked()
    Module.CommonTips:ShowSimpleTip("点击了分享按钮！")
end

function RecruitTeamCodePop:_CloseSelf()
    Facade.UIManager:CloseUI(self)
end

function RecruitTeamCodePop:OnNavBack()
    return false
end

-- azhengzheng:Gamepad start
function RecruitTeamCodePop:_EnableGamepadFeature()
    if not IsHD() then
        return
    end

    if not self._joinBtnClickInputAction then
        self._joinBtnClickInputAction = self:AddInputActionBinding("TeamCode_Click_X_Gamepad", EInputEvent.IE_Pressed, self._OnJoinBtnClicked, self, EDisplayInputActionPriority.UI_POP)
        self._wtJoinWB:SetDisplayInputAction("TeamCode_Click_X_Gamepad", true, nil, true)
    end

    if not self._shareBtnClickInputAction then
        self._shareBtnClickInputAction = self:AddInputActionBinding("TeamCode_Click_Y_Gamepad", EInputEvent.IE_Pressed, self._OnShareBtnClicked, self, EDisplayInputActionPriority.UI_POP)
        self._wtShareWB:SetDisplayInputAction("TeamCode_Click_Y_Gamepad", true, nil, true)
    end
end

function RecruitTeamCodePop:_DisableGamepadFeature()
    if not IsHD() then
        return
    end

    if self._joinBtnClickInputAction then
        self:RemoveInputActionBinding(self._joinBtnClickInputAction)
        self._joinBtnClickInputAction = nil
    end

    if self._shareBtnClickInputAction then
        self:RemoveInputActionBinding(self._shareBtnClickInputAction)
        self._shareBtnClickInputAction = nil
    end
end
-- azhengzheng:Gamepad end

return RecruitTeamCodePop