----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMLobbyDisplay)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class LobbyDisplayModule : ModuleBase
local LobbyDisplayModule = class("LobbyDisplayModule", require("DFM.YxFramework.Managers.Module.ModuleBase"))
local ItemHelperTool    = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local EHallMainDisplayType = import "EHallMainDisplayType"
local LobbyDisplayLogic = require "DFM.Business.Module.LobbyDisplayModule.Logic.LobbyDisplayLogic"
local ViewSwitchLogic = require "DFM.Business.Module.LobbyDisplayModule.Logic.ViewSwitchLogic"
local DisplayEventLogic = require "DFM.Business.Module.LobbyDisplayModule.Logic.DisplayEventLogic"
local LobbyChangeStageLogic = require "DFM.Business.Module.LobbyDisplayModule.Logic.LobbyChangeStageLogic"
-- local HallSceneManager = require("DFM.YxFramework.Managers.HallScene.HallSceneManager")
local UKismetSystemLibrary = import "KismetSystemLibrary"
local EPerfGearQualityLevel = import "EPerfGearQualityLevel"
local ULuaSubsystem              = import "LuaSubsystem"
local UHallSceneBGManager = import "HallSceneBGManager"
-- local UGameUserSettings = import("GameUserSettings")
local UWidgetLayoutLibrary = import "WidgetLayoutLibrary"

local UDFMGameNotch = import "DFMGameNotch"
local DFMGameNotch = UDFMGameNotch.Get(GetGameInstance())

LobbyDisplayModule.PerfGearModeName = "LobbyDisplay"
LobbyDisplayModule.LastMaxFPS = 30

LobbyDisplayModule.MobileMsaaCacheFlag = false
LobbyDisplayModule.LastMobileMsaa = 1

local PerfGearPipeline = import "PerfGearPipeline"
local PerfGearPipelineInst = PerfGearPipeline.Get()

local UDFMCharacterItemFashionManager = import "DFMCharacterItemFashionManager"

function LobbyDisplayModule:Ctor()
    self._GMTestSceneType = 0 -- 0, 默认；1，2D；2，3D;
    self._GMTestEnableCharacterAvatarSuit = false

    self._LobbyDisplayPerfGearEnable = false
    self._LobbyDisplayLastPerfGearMode = ""
    self._ItemId = 0 --武器通用场景使用
    self._newOffset = 0
    self._meshName = ""
    self._sceneRootName = ""
    self._offsetType = 0
    self._destoryDelegate = {}
end

--------------------------------------------------------------------------
--- 模块生命周期
--------------------------------------------------------------------------
function LobbyDisplayModule:OnInitModule()
    DisplayEventLogic.AddLobbyStageListeners()
    self:AddLuaEvent(Module.LobbyDisplay.Config.Events.evtToggleCharacterFashion, self._RefreshCharacterDisplay, self)
    --监听切换关卡事件,不要轻易发布这个事件
    self:AddLuaEvent(Module.LobbyDisplay.Config.Events.evtPOPUISwitchLevel, self._RefreshPOPUISwitchLevel, self)
    self:AddLuaEvent(Server.ArmedForceServer.Events.evtArmedForceRentalStatusChanged, self._CheckRentalStatus, self)
    self:AddLuaEvent(Module.LobbyDisplay.Config.Events.evtResetMobileLobbyMSAA, self._OnResetMobileLobbyMSAA, self)
end

---注销LuaEvent、Timer监听
function LobbyDisplayModule:OnDestroyModule()
    DisplayEventLogic.RemoveLobbyStageListeners()
    DisplayEventLogic.RemoveDisplayListeners()
    self:RemoveLuaEvent(Module.LobbyDisplay.Config.Events.evtToggleCharacterFashion)
    --注销切换关卡事件
    self:RemoveLuaEvent(Module.LobbyDisplay.Config.Events.evtPOPUISwitchLevel)
    self:RemoveLuaEvent(Server.ArmedForceServer.Events.evtArmedForceRentalStatusChanged, self._CheckRentalStatus, self)
    self:RemoveLuaEvent(Module.LobbyDisplay.Config.Events.evtResetMobileLobbyMSAA, self._OnResetMobileLobbyMSAA, self)
end

function LobbyDisplayModule:OnFoldStatusChanged()
    local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
    if armedForceMode == EArmedForceMode.BHD then loginfo('【LobbyDisplayModule】 BHD跳过Display阶段') return end
    local InMainFlowCtrl = armedForceMode - 1
    LobbyDisplayLogic.SetCameraType(InMainFlowCtrl)
end


---若拿住了ui/uiHandle,需要释放
---@param gameFlowType EGameFlowStageType
function LobbyDisplayModule:OnGameFlowChangeLeave(gameFlowType)
    self:SetSwitchingMainFlow(true)
    if gameFlowType == EGameFlowStageType.Lobby or gameFlowType == EGameFlowStageType.SafeHouse then
        self:RestoreLobbyDisplayMSAA()
        DisplayEventLogic.RemoveDisplayListeners()
        if LobbyDisplayLogic.UseCharacterCache then
            Facade.HallSceneManager:FoceCallSceneCtrlFunctionBySubstage(ESubStage.MainFlowLobby,"DestoryCharcter")
        end
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "fashion.useCacheItemFashionConfig 1", nil) -- 不在大厅的情况下，玄学道具尝试走缓存
    end
    if gameFlowType == EGameFlowStageType.ModeHall or 
        gameFlowType == EGameFlowStageType.GameToLobby or 
        gameFlowType == EGameFlowStageType.LoginToBattleField or 
        gameFlowType == EGameFlowStageType.LobbyBHDToLobby or 
        gameFlowType == EGameFlowStageType.LobbyBHDToSafeHouse then 
        Facade.UIManager:CommitTransition(true)
        Facade.UIManager:SetIsJumpRollbackTransition(true)
    end
end

function LobbyDisplayModule:OnGameFlowChangeEnter(gameFlowType)
    self:SetSwitchingMainFlow(false)
    if gameFlowType == EGameFlowStageType.ModeHall then
        self:SetSwitchMainFlowEnd(true)

        -- 在进入模式大厅后，手游提前检查pak的下载结果
        if not IsHD() then
            local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
            LiteDownloadManager:CheckModuleAsyncWithQueue("Fashion")
            LiteDownloadManager:CheckModuleAsyncWithQueue("Character")
        end
        -- 在进入模式大厅后，手游提前检查pak的下载结果 end

        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "showDebug.EnableLevelOptimization false", nil)
        loginfo("showDebug.EnableLevelOptimization false")

    elseif gameFlowType == EGameFlowStageType.Lobby or gameFlowType == EGameFlowStageType.SafeHouse then
        -- self:OnLobbyEnvAuxRHIEnable(false)
        self:SetLobbyDisplayMSAA()
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "fashion.useCacheItemFashionConfig 0", nil) -- 在大厅的情况下，玄学道具不走缓存，实时读表
        local CharacterItemFashionManager = UDFMCharacterItemFashionManager:Get()
        if CharacterItemFashionManager then
            CharacterItemFashionManager:ResetCachedFashionConfigs() -- 清理局内缓存
        end

    elseif gameFlowType == EGameFlowStageType.Game then
        
        --- 旧清理逻辑
        Facade.UIManager:TryUnloadLinkSubStage(ESubStage.HallMain)
        self.Field:ClearLobbyLevel()

        self:SetSwitchMainFlowEnd(true)
    end
    --启用异步加载关卡
    local GameInstance = GetGameInstance()
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "r.EnableSyncLoadSubLevel 0")
end

function LobbyDisplayModule:OnLoadingFrontend2Game()
    -- self:OnLobbyEnvAuxRHIEnable(true)
end

function LobbyDisplayModule:OnLobbyEnvAuxRHIEnable(bEnable)
    if IsHD() then
        return
    end

    local EnableParam = 1
    if not bEnable then
        EnableParam = 0
    end
    
    -- C++侧已经做了判断相同参数不处理
    print("LobbyDisplayModule:OnLobbyEnvAuxRHIEnable" .. EnableParam)
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(),"r.RuntimeAuxRHI.Enable " .. EnableParam, nil)
end


function LobbyDisplayModule:OnSubStageChangeEnter(curSubStageType)
    print('[LobbyDisplayModule]-----------OnSubStageChangeEnter', curSubStageType)
    if curSubStageType == ESubStage.HallMain then
        local currentGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
        Module.LobbyDisplay.Field:SetCurrentGameFlow(currentGameFlow)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMain,"ToggleEnable", true)
        DisplayEventLogic.InitAndAddDisplayListeners()

        -- LightUtil.SetMiniWorld(true)
        -- FogUtil.SetMiniWorldFog(false)
        
        local isSOLHighValue = false
        if not Module.BattlefieldEntry:IsInMPLobby() then
            isSOLHighValue = Server.GameModeServer:CheckIsSOLHighOrMidValue()
        end
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMain,"ToggleCustomScene", isSOLHighValue)

        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMain,"ToggleFogEnable", true)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMain,"ToggleInputEnable", true)

        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMain,"ToggleMainCtrlPureMode", true)

        Module.LobbyDisplay:EnterLobbyDisplayPerfGear()
        -- 触发事件
        Module.LobbyDisplay.Config.Events.evtEnterStageHallMain:Invoke()
    elseif curSubStageType == ESubStage.HallMall 
        or curSubStageType == ESubStage.HallIndividual 
        or curSubStageType == ESubStage.HallCollections 
        or curSubStageType == ESubStage.HallLevelUnlock 
        or curSubStageType == ESubStage.HallBlueprint 
        or curSubStageType == ESubStage.HallSceneCapture2D 
        or curSubStageType == ESubStage.HallHero 
        or curSubStageType == ESubStage.HallMatch 
        or curSubStageType == ESubStage.HallSecretService
        or curSubStageType == ESubStage.HallMpLobby 
        or curSubStageType == ESubStage.HallWeaponShow
        or curSubStageType == ESubStage.Lottery
        or curSubStageType == ESubStage.LotteryV2
        or curSubStageType == ESubStage.HallCollectionNew
        or curSubStageType == ESubStage.HallCollectionV2
        or curSubStageType == ESubStage.HallLevelUp 
        or curSubStageType == ESubStage.HallVehicle 
        or curSubStageType == ESubStage.MainFlowLobby 
        or curSubStageType == ESubStage.WeaponDisplay 
        or curSubStageType == ESubStage.HallBattlePass 
        or curSubStageType == ESubStage.LotteryCollection 
        or curSubStageType == ESubStage.OperatorItem 
        or curSubStageType == ESubStage.OperatorWatch 
        or curSubStageType == ESubStage.CollectionKnife
        or curSubStageType == ESubStage.HallPendantShow
        or curSubStageType == ESubStage.CollectionHanging
        or curSubStageType == ESubStage.WeaponSequence 
        or curSubStageType == ESubStage.Armory 
        or curSubStageType == ESubStage.CollectionLibrary
        or curSubStageType == ESubStage.LuckyNest
        or curSubStageType == ESubStage.MagicTower then
        if curSubStageType == ESubStage.MainFlowLobby then 
            LobbyDisplayLogic.ResetToCurGroupHero()
        end
        local GameInstance = GetGameInstance()

        -- UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "showDebug.EnableReloadDefaultLevelLogic 0")
        Module.LobbyDisplay:EnterLobbyDisplayPerfGear()
        LobbyDisplayLogic.OnDisplayCtrlInitWithSubStage(curSubStageType)
        LuaGlobalEvents.evtSceneLoaded:Invoke(curSubStageType)
    elseif curSubStageType == ESubStage.WeaponAssemble then
        -- 处理高度雾
        -- FogUtil.SetMiniWorldFog(true, "WeaponAssembleDisplay_Main")

        if IsHD() then
            local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
            if localCtrl then
                localCtrl:SetClientDisableProcessPlayerInput(true, "WeaponAssemble")
            end
        end
    elseif curSubStageType == ESubStage.Range
    or curSubStageType == ESubStage.CollectionRoom then
        if not IsHD() then
            local MaxFPS = 60
            local FPSLevel = PerfGearPipelineInst:GetFPSLevel()
            if FPSLevel == EPerfGearQualityLevel.EQuality_VeryLow then
                MaxFPS = 25
            elseif FPSLevel == EPerfGearQualityLevel.EQuality_Low then
                MaxFPS = 30
            elseif FPSLevel == EPerfGearQualityLevel.EQuality_Medium then
                MaxFPS = 40
            elseif FPSLevel == EPerfGearQualityLevel.EQuality_High then
                MaxFPS = 50
            elseif FPSLevel == EPerfGearQualityLevel.EQuality_VeryHigh then
                MaxFPS = 60
            elseif FPSLevel == EPerfGearQualityLevel.EQuality_Custom90 then
                MaxFPS = 90
            elseif FPSLevel == EPerfGearQualityLevel.EQuality_Custom120 then
                MaxFPS = 120
            elseif FPSLevel == EPerfGearQualityLevel.EQuality_Custom144 then
                MaxFPS = 144
            end
            
            local cachemaxfps = UKismetSystemLibrary.GetConsoleVariableFloatValue("t.maxFPS")
            LobbyDisplayModule.LastMaxFPS = cachemaxfps

            loginfo("############### ESubStage.Range Enter maxfps:", MaxFPS)
            UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(),"t.maxFPS " .. MaxFPS, nil)
        end
        Module.LobbyDisplay:EnterLobbyDisplayPerfGear()
        -- LobbyDisplayLogic.OnDisplayCtrlInitWithSubStage(curSubStageType)
        LuaGlobalEvents.evtSceneLoaded:Invoke(curSubStageType)

    elseif curSubStageType == ESubStage.None then
        -- donothing
    elseif curSubStageType == ESubStage.SafeHouse3D then
        Module.LobbyDisplay:ExitLobbyDisplayPerfGear()
    end
end

function LobbyDisplayModule:OnSubStageChangeLeave(leaveSubStageType)
    if leaveSubStageType == ESubStage.MainFlowLobby then
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MainFlowLobby,"CloseMainFlowModeSeq")
    end
    if leaveSubStageType == ESubStage.HallMain then
        DisplayEventLogic.RemoveDisplayListeners()
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMain,"ToggleInputEnable", false)
        Facade.HallSceneManager:CallSceneCharacterCtrlFunctionBySubstage(ESubStage.HallMain,"DestroyPlayerCharacter")
        Facade.HallSceneManager:CallSceneCharacterCtrlFunctionBySubstage(ESubStage.HallMain,"TurnOffAllHallLights")
        local EHallCharacterDisplayStage = import "EHallCharacterDisplayStage"
        Module.LobbyDisplay.Field:SetCharacterDisplayStage(EHallCharacterDisplayStage.Nothing)
        --关闭雾
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMain,"ToggleFogEnable", false)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMain,"ResetHallMainCameraDOFParams")
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMain,"ToggleMainCtrlPureMode", false)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMain,"ResetDisplayWeapon")

    elseif leaveSubStageType == ESubStage.HallMall
        or leaveSubStageType == ESubStage.HallIndividual
        or leaveSubStageType == ESubStage.HallCollections
        or leaveSubStageType == ESubStage.HallLevelUnlock
        or leaveSubStageType == ESubStage.HallBlueprint
        or leaveSubStageType == ESubStage.HallSceneCapture2D 
        or leaveSubStageType == ESubStage.HallMpLobby
        or leaveSubStageType == ESubStage.HallHero 
        or leaveSubStageType == ESubStage.HallMatch 
        or leaveSubStageType == ESubStage.HallSecretService 
        or leaveSubStageType == ESubStage.HallWeaponShow
        or leaveSubStageType == ESubStage.Lottery
        or leaveSubStageType == ESubStage.LotteryV2
        or leaveSubStageType == ESubStage.HallCollectionNew
        or leaveSubStageType == ESubStage.HallCollectionV2
        or leaveSubStageType == ESubStage.HallLevelUp 
        or leaveSubStageType == ESubStage.HallVehicle 
        or leaveSubStageType == ESubStage.MainFlowLobby 
        or leaveSubStageType == ESubStage.WeaponDisplay 
        or leaveSubStageType == ESubStage.HallBattlePass 
        or leaveSubStageType == ESubStage.LotteryCollection
        or leaveSubStageType == ESubStage.OperatorItem
        or leaveSubStageType == ESubStage.CollectionKnife 
        or leaveSubStageType == ESubStage.CollectionHanging
        or leaveSubStageType == ESubStage.Armory
        or leaveSubStageType == ESubStage.CollectionLibrary
        or leaveSubStageType == ESubStage.OperatorWatch
        or leaveSubStageType == ESubStage.MagicTower then
        LobbyDisplayLogic.OnDisplayCtrlUnInitWithSubStage(leaveSubStageType)
    elseif leaveSubStageType == ESubStage.WeaponAssemble then
        -- 处理高度雾
        FogUtil.DeactiveHeightFog("WeaponAssembleDisplay_Main")

        if IsHD() then
            local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
            if localCtrl then
                localCtrl:SetClientDisableProcessPlayerInput(false, "WeaponAssemble")
            end
        end
    elseif leaveSubStageType == ESubStage.Range
    or leaveSubStageType == ESubStage.CollectionRoom then
        if not IsHD() then
            -- It's not worth creating new switch gear specifically for a shooting range
            loginfo("############### ESubStage.Range Leave maxfps:", LobbyDisplayModule.LastMaxFPS)
            UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(),"t.maxFPS " .. LobbyDisplayModule.LastMaxFPS, nil)
        end
        
        LobbyDisplayLogic.OnDisplayCtrlUnInitWithSubStage(leaveSubStageType)
    end
end

--------------------------------------------------------------------------
--- 模块 Public API
--------------------------------------------------------------------------

function LobbyDisplayModule:PlayerCharacterEquipWeapon(weaponDesc)
    LobbyDisplayLogic._PlayerCharacterEquipWeapon(weaponDesc)
end

function LobbyDisplayModule:SetHallDisplayZoomType(isZoom)
    local curDisplayType = Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMain,"GetDisplayType")
    if isZoom then
        if curDisplayType == EHallMainDisplayType.Weapon_Item1 then
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMain,"SetDisplayType",EHallMainDisplayType.WeaponZoom)
        elseif curDisplayType == EHallMainDisplayType.TakingMedicine then
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMain,"SetDisplayType",EHallMainDisplayType.WeaponZoom_2)
        end
    else
        if curDisplayType == EHallMainDisplayType.WeaponZoom then
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMain,"SetDisplayType",EHallMainDisplayType.Weapon_Item1)
        elseif curDisplayType == EHallMainDisplayType.WeaponZoom_2 then
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMain,"SetDisplayType",EHallMainDisplayType.TakingMedicine)
        end
    end
end

function LobbyDisplayModule:SetIsPlayerCharacterVisible(bVisible, uiNavId)
    Facade.HallSceneManager:CallSceneCharacterAvatarFunctionBySubstage(ESubStage.HallMain,"SetActorHiddenInGame", not bVisible)
    if uiNavId then
        self.Field:AddCustomCharVisible(uiNavId, bVisible)
        loginfo('LobbyDisplayModule:SetIsPlayerCharacterVisible 该栈UI:', uiNavId, '自定义角色显示配置为:', bVisible, 'GameFlown内保持本设置')
    else
        loginfo('LobbyDisplayModule:SetIsPlayerCharacterVisible 自定义角色显示配置为:', bVisible, '但未传入栈UINavID，不保存此操作')
    end
end

function LobbyDisplayModule:CharacterEmptyBody()
    return LobbyDisplayLogic.ClearWearingEquipmentsAndWeapons()
end

-- 获取Actor在给定UI控件的Local Space中的位置
---@param slot 0|1|2|3|nil
---@param parentWidget Widget
---@return FVector2D
function LobbyDisplayModule:GetCharacterLocationInUILocalSpace(slot, parentWidget)
    return LobbyDisplayLogic.GetCharacterLocationInUILocalSpace(slot, parentWidget)
end

-- 获取Actor的世界坐标
---@param slot 0|1|2|3|nil
---@return FVector|nil
function LobbyDisplayModule:GetCharacterLocation(slot)
    return LobbyDisplayLogic.GetCharacterLocation(slot)
end

function LobbyDisplayModule.ProjectWorldToScreen(playerCtrl, worldPos, bViewportRelative)
    return LobbyDisplayLogic.ProjectWorldToScreen(playerCtrl, worldPos, bViewportRelative)
end

function LobbyDisplayModule:GetCharacterDisplayStage()
    return Module.LobbyDisplay.Field:GetCharacterDisplayStage()
end

function LobbyDisplayModule:SetWearSuit(bWearSuit)
    return Module.LobbyDisplay.Field:SetWearSuit(bWearSuit)
end

 --是否在组队中（自定义的，不是严格意义的组队中）  情景包括：① 队伍里多个人；② 队伍里单人但是正在招募中
function LobbyDisplayModule.IsInCustomTeam()
    return LobbyDisplayLogic.IsInCustomTeam()
end

function LobbyDisplayModule:SetGMTestSceneType(inSceneType)
    self._GMTestSceneType = inSceneType
end

function LobbyDisplayModule:GetGMTestSceneType()
    return self._GMTestSceneType
end

function LobbyDisplayModule:SetGMTestEnableCharacterAvatarSuit(enable)
    self._GMTestEnableCharacterAvatarSuit = enable
end

function LobbyDisplayModule:GetGMTestEnableCharacterAvatarSuit()
    return self._GMTestEnableCharacterAvatarSuit
end

function LobbyDisplayModule:IsEnableChatarcterSuitAvatarView()
    return ViewSwitchLogic.IsEnableChatarcterSuitAvatarView()
end

function LobbyDisplayModule:InitSceneCapture2D(imageWidget, callback)
    if not imageWidget then
        return
    end

    local HallSceneCapture2DDisplayCtrl = Facade.HallSceneManager:GetSceneCtrlByStr("SceneCapture2DDisplay")

    if HallSceneCapture2DDisplayCtrl then
        local imgMat = imageWidget:GetDynamicMaterial()
        if imgMat then
            local geometry = imageWidget:GetCachedGeometry()
            local size = geometry:GetAbsoluteSize()

            local x, y = math.ceil(size.X), math.ceil(size.Y)
            local sceneCapture2dTex = HallSceneCapture2DDisplayCtrl:InitSceneCapture2DTexture(x, y)
            imgMat:SetTextureParameterValue("CaptureTexture", sceneCapture2dTex)

            if callback then
                callback()
            end
        end
    end
end

function LobbyDisplayModule:EnterLobbyDisplayPerfGear()
    
    local curPerfGearMode = PerfGearPipelineInst:GetCurMode()
    if curPerfGearMode == LobbyDisplayModule.PerfGearModeName then
        return
    end

    print("LobbyDisplayModule:EnterLobbyDisplayPerfGear")

    self._LobbyDisplayLastPerfGearMode = curPerfGearMode

    PerfGearPipelineInst:SetCurMode(LobbyDisplayModule.PerfGearModeName)
    PerfGearPipelineInst:Apply(false, false, false)

    -- Set MSAA After PerfMode Refreshed 
    self:SetLobbyDisplayMSAA()
end



function LobbyDisplayModule:ExitLobbyDisplayPerfGear()
    
    if self._LobbyDisplayLastPerfGearMode == "" then
        logerror("LobbyDisplayModule:ExitLobbyDisplayPerfGear LastPerfGearMode = null")
        return
    end
    if self._LobbyDisplayLastPerfGearMode == LobbyDisplayModule.PerfGearModeName then
        logerror("LobbyDisplayModule:ExitLobbyDisplayPerfGear LastPerfGearMode = LobbyDisplay")
        return 
    end
    
    print("LobbyDisplayModule:ExitLobbyDisplayPerfGear")

    self:RestoreLobbyDisplayMSAA()

    PerfGearPipelineInst:SetCurMode(self._LobbyDisplayLastPerfGearMode)
    PerfGearPipelineInst:Apply(false, false, false)

    self._LobbyDisplayPerfGearEnable = false
    self._LobbyDisplayLastPerfGearMode = ""
end

function LobbyDisplayModule:SetLobbyDisplayMSAA()
    print("LobbyDisplayModule:SetLobbyDisplayMSAA")
    -- MSAA 依赖机型设置
    if not IsHD() then
        self:ProcessLobbyDisplayMSAA()
    end
end

function LobbyDisplayModule:ProcessLobbyDisplayMSAA()
    print("LobbyDisplayModule:ProcessLobbyDisplayMSAA")

    if IsInEditor() then
        local worldContext = GetGameInstance()
        if worldContext then
            if not LobbyDisplayModule.MobileMsaaCacheFlag then
                LobbyDisplayModule.MobileMsaaCacheFlag = true
                LobbyDisplayModule.LastMobileMsaa = UKismetSystemLibrary.GetConsoleVariableIntValue("r.mobilemsaa")
            end
            UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, "r.mobilemsaa 4")
        end
    else
        -- MSAA黑名单过滤
        if not PerfGearPipelineInst:IsParamInBlackList("MSAANumSample") then
            local worldContext = GetGameInstance()
            if worldContext then
                local oriQualityLv = PerfGearPipelineInst:GetCurApplyQuality() 
                local ingoreMsaaSetup = oriQualityLv == EPerfGearQualityLevel.EQuality_VeryLow or oriQualityLv == EPerfGearQualityLevel.EQuality_Low or oriQualityLv == EPerfGearQualityLevel.EQuality_Medium 

                if not LobbyDisplayModule.MobileMsaaCacheFlag then
                    LobbyDisplayModule.MobileMsaaCacheFlag = true
                    LobbyDisplayModule.LastMobileMsaa = UKismetSystemLibrary.GetConsoleVariableIntValue("r.mobilemsaa")
                end

                local curMSAA = 1
                if ingoreMsaaSetup or PerfGearPipelineInst:GetDeviceLevel() == 3 then
                    CurMSAA = 1 -- 二档机器关闭msaa
                elseif PerfGearPipelineInst:GetDeviceLevel() == 5 then
                    CurMSAA = 4 -- 超一档机器开启msaa 4x
                else
                    CurMSAA = 2 -- 一档机器开启msaa 2x
                end

                UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, "r.mobilemsaa " .. CurMSAA)
            end
        end
    end
end

function LobbyDisplayModule:_OnResetMobileLobbyMSAA()
    if IsHD() then
        return
    end

    local CurGF = Facade.GameFlowManager:GetCurrentGameFlow()
    if CurGF == EGameFlowStageType.Lobby or CurGF == EGameFlowStageType.SafeHouse then
        if LobbyDisplayModule.MobileMsaaCacheFlag then
            self:ProcessLobbyDisplayMSAA()
        end 
    end
end

function LobbyDisplayModule:RestoreLobbyDisplayMSAA()
    print("LobbyDisplayModule:RestoreLobbyDisplayMSAA")
    if LobbyDisplayModule.MobileMsaaCacheFlag then
        LobbyDisplayModule.MobileMsaaCacheFlag = false

        local worldContext = GetGameInstance()
        if worldContext then
            UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, "r.mobilemsaa " .. LobbyDisplayModule.LastMobileMsaa)
        end
    end
end

--触发角色时装切换
function LobbyDisplayModule:_RefreshCharacterDisplay()
    LobbyDisplayLogic.FreshSOLWearingEquipmentsAndWeapons()
    LobbyDisplayLogic.RefreshHeroDisplay()
end

--触发关卡切换(发布事件切换对应的关卡)
function LobbyDisplayModule:_RefreshPOPUISwitchLevel(StageType)
    if StageType then
        Facade.GameFlowManager:EnterSubStage(StageType)
    end
end

------------ MainFlow API------------ 
function LobbyDisplayModule:SetSwitchMainFlowEnd(isEnd)
    self.Field.OnSwitchMainFlowEnd = isEnd
end

function LobbyDisplayModule:GetSwitchMainFlowEnd()
    return self.Field.OnSwitchMainFlowEnd
end

function LobbyDisplayModule:SetSwitchingMainFlow(isFlag)
    self.Field.OnSwitchingMainFlow = isFlag
end

function LobbyDisplayModule:GetSwitchingMainFlow()
    return self.Field.OnSwitchingMainFlow
end

function LobbyDisplayModule:SwitchMainFlow(inMainFlowCtrl)
    LobbyDisplayLogic.SwitchMainFlow(inMainFlowCtrl)
end

function LobbyDisplayModule:SetCameraType(inMainFlowCtrl)
    LobbyDisplayLogic.SetCameraType(inMainFlowCtrl)
end

function LobbyDisplayModule:SetMainFlowSetCharacterInfo(inMainFlowCtrl, bRefreshDisplayType)
    LobbyDisplayLogic.SetMainFlowSetCharacterInfo(inMainFlowCtrl, bRefreshDisplayType)
end

function LobbyDisplayModule:RefreshPlayerModelByInfo(inMainFlowCtrl,playerInfo)
    LobbyDisplayLogic.RefreshPlayerModelByInfo(inMainFlowCtrl,playerInfo)
end

function LobbyDisplayModule:ClearPlayerModel(inMainFlowCtrl)
    LobbyDisplayLogic.ClearPlayerModel(inMainFlowCtrl)
end

function LobbyDisplayModule:SetMainFlowDefaultDisplayType(inMainFlowCtrl)
    LobbyDisplayLogic.SetMainFlowDefaultDisplayType(inMainFlowCtrl)
end

function LobbyDisplayModule:RefreshTeammateByEquipChange(InMainFlowCtrl,playerInfo,addEquips,removeEquips,reModel)
    LobbyDisplayLogic.RefreshTeammateByEquipChange(InMainFlowCtrl,playerInfo,addEquips,removeEquips,reModel)
end

function LobbyDisplayModule:SetMainFlowFullfillSelfCharacter(bFullfill)
    self.Field.bFullfillSelfCharacter = bFullfill
end

function LobbyDisplayModule:GetMainFlowFullfillSelfCharacter()
    return self.Field.bFullfillSelfCharacter
end

function LobbyDisplayModule:GetMainWeaponItemForShow()
    return LobbyDisplayLogic.GetMainWeaponItemForShow()
end

---@param bDisplay boolean 是否显示装备
function LobbyDisplayModule:SetCharacterDisplayEquipment(bDisplayEquipment)
    local tipServer = Server.TipsRecordServer
    local oldValue = tipServer:GetBoolean(tipServer.keys.LobbyShowSkin)
    Server.TipsRecordServer:SetBoolean(tipServer.keys.LobbyShowSkin, bDisplayEquipment) -- 虽然LobbyShowSkin名为显示时装但实际上按显示装备在赋值
    if oldValue ~= bDisplayEquipment then
        LogAnalysisTool.DoSendModelDisplayFlow(bDisplayEquipment and 2 or 1)
    end
end

---@return boolean 是否显示装备
function LobbyDisplayModule:GetCharacterDisplayEquipment()
    local tipServer = Server.TipsRecordServer
    local bDisplayEquipment = tipServer:GetBoolean(tipServer.keys.LobbyShowSkin)
    return bDisplayEquipment
end

function LobbyDisplayModule:CreateHallCharacterSetupDelegate(character,inMainFlowCtrl,slot)
    if character then
        local handle = character.HallCharacterSetup:Add(CreateCPlusCallBack(self.OnHallCharacterSetup, self,inMainFlowCtrl,slot))
        if not self._destoryDelegate[character]  then 
            self._destoryDelegate[character]  = {}
        end
        self._destoryDelegate[character]["SetUp"] = handle
    end
end

function LobbyDisplayModule:CreateViewSwitchCharacterSetupDelegate(character,firstMainWeapon)
    if character then
        local handle = character.HallCharacterSetup:Add(CreateCPlusCallBack(self.OnViewSwitchHallCharacterSetup,self,firstMainWeapon))
        if not self._destoryDelegate[character]  then 
            self._destoryDelegate[character]  = {}
        end
        self._destoryDelegate[character]["SetUp"] = handle
    end
end

function LobbyDisplayModule:CreateHallMainCharacterSetupDelegate(character)
    if character then
        local handle = character.HallCharacterSetup:Add(CreateCPlusCallBack(self.OnHallMainCharacterSetup, self))
        if not self._destoryDelegate[character]  then 
            self._destoryDelegate[character]  = {}
        end
        self._destoryDelegate[character]["SetUp"] = handle
    end
end

function LobbyDisplayModule:CreateHallCharacterAnimSetChangedDelegate(character,inMainFlowCtrl,slot)
    if character then
        local handle = character.OnHallCharacterAnimSetChanged:Add(CreateCPlusCallBack(self.OnAnimSetChange, self,inMainFlowCtrl,slot))
        if not self._destoryDelegate[character]  then 
            self._destoryDelegate[character]  = {}
        end
        self._destoryDelegate[character]["AnimSet"] = handle
    end
end

function LobbyDisplayModule:CreateHallCharacterDestroyDelegate(character)
    if character then
        local handle = character.HallCharacterDestroy:Add(CreateCPlusCallBack(self.OnHallCharacterDestroy, self,character))
        if not self._destoryDelegate[character]  then 
            self._destoryDelegate[character]  = {}
        end
        self._destoryDelegate[character]["Destory"] = handle
    end
end

function LobbyDisplayModule:OnHallCharacterDestroy(character)
    if self._destoryDelegate[character] then 
        local delegate = self._destoryDelegate[character]["SetUp"]
        if  delegate then 
            character.HallCharacterSetup:Remove(delegate)
            delegate = nil
        end
        delegate = self._destoryDelegate[character]["AnimSet"]
        if delegate then 
            character.OnHallCharacterAnimSetChanged:Remove(delegate)
            delegate = nil
        end
        delegate = self._destoryDelegate[character]["Destory"]
        if delegate then 
            character.HallCharacterDestroy:Remove(delegate)
            delegate = nil
        end
        delegate = self._destoryDelegate[character]["AnimEnd"]
        if delegate then 
            character.OnHallCharacterAnimPlayEnd:Remove(delegate)
            delegate = nil
        end
        self._destoryDelegate[character]  = nil
    end
end

function LobbyDisplayModule:OnHallMainCharacterSetup()
    LobbyDisplayLogic.OnHallMainCharacterSetup()
end
function LobbyDisplayModule:OnHallCharacterSetup(inMainFlowCtrl,slot)
    LobbyDisplayLogic.OnHallCharacterSetup(inMainFlowCtrl,slot)
end

function LobbyDisplayModule:OnViewSwitchHallCharacterSetup(firstMainWeapon)
    Facade.HallSceneManager:CallSceneCharacterAvatarFunctionBySubstage(ESubStage.HallMain,"UnequipWeapon")
    if firstMainWeapon then
        local weaponDesc = WeaponAssemblyTool.PropInfo_To_Desc(firstMainWeapon.rawPropInfo)
        Facade.HallSceneManager:CallSceneCharacterAvatarFunctionBySubstage(ESubStage.HallMain,"EquipWeapon", weaponDesc)
    else
        
    end
end

function LobbyDisplayModule:OnAnimSetChange(inMainFlowCtrl,slot)
    LobbyDisplayLogic.OnAnimSetChange(inMainFlowCtrl,slot)
end

function LobbyDisplayModule:CreateHallModeSeqDelegate()
    local displayCtrl = Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.MainFlowLobby)
    if displayCtrl then 
        displayCtrl.OnMainFlowHallModeSeqFinishedEvent:Add(CreateCPlusCallBack(self.OnPlaySeqFished, self))
    end
end

function LobbyDisplayModule:CreateHallCharacterAnimDelegate(character,inMainFlowCtrl,slot,playerID)
    if character then
        local handle = character.OnHallCharacterAnimPlayEnd:Add(CreateCPlusCallBack(self.OnHallCharacterAnimPlayEnd, self,inMainFlowCtrl,slot,playerID))
        if not self._destoryDelegate[character]  then 
            self._destoryDelegate[character]  = {}
        end
        self._destoryDelegate[character]["AnimEnd"] = handle
    end
end

function LobbyDisplayModule:OnPlaySeqFished()
    LobbyDisplayLogic.OnPlaySeqFished()
end

function LobbyDisplayModule:OnHallCharacterAnimPlayEnd(inMainFlowCtrl,slot,playerID)
    LobbyDisplayLogic.OnHallCharacterAnimPlayEnd(inMainFlowCtrl,slot,playerID)
end

function LobbyDisplayModule:SetDisplayWatchItemId(inWatchItemId)
    self.Field:SetDisplayWatchItemId(inWatchItemId)
end

function LobbyDisplayModule:GetDisplayWatchItemId()
    return self.Field._LobbyDisplayWatchDisplayItemId or 0
end

local EMainFlowCtrlType = import "EMainFlowCtrlType"
function LobbyDisplayModule:_CheckRentalStatus(bRentState)
    if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse then
        LobbyDisplayLogic.RefreshMySelfPlayerByUnEquip(EMainFlowCtrlType.MainFlowCtrl_SOL)
        if not bRentState then 
            LobbyDisplayLogic.FreshSOLWearingEquipmentsAndWeapons()
            LobbyDisplayLogic.ResetCharacterRotation()
        end
    end
end

function LobbyDisplayModule:OnTeammateLeft(playerId,teammateName,teamId,slot)
    LobbyDisplayLogic.SetTeammateAnimState(playerId, LobbyDisplayLogic.AnimState.PlayEnd)
end

function LobbyDisplayModule:OnMainRoleLeft()
    LobbyDisplayLogic.ClearTeammateAnimState()
end

function LobbyDisplayModule:OnFacePopClosed()
    local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
    if armedForceMode == EArmedForceMode.BHD then loginfo('【LobbyDisplayModule】 BHD跳过Display阶段') return end
    local ctrl = armedForceMode - 1
    LobbyDisplayLogic.ClearAllRoleAnimState(ctrl)
    Facade.HallSceneManager:FoceCallSceneCtrlFunctionBySubstage(ESubStage.MainFlowLobby,"DestoryCharcter")
    LobbyDisplayLogic.SetMainFlowSetCharacterInfo(ctrl,false)
end

function LobbyDisplayModule:OnFacePopJumped()
    local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
    if armedForceMode == EArmedForceMode.BHD then loginfo('【LobbyDisplayModule】 BHD跳过Display阶段') return end
    local ctrl = armedForceMode - 1
    LobbyDisplayLogic.SetSelfAnimState(ctrl,LobbyDisplayLogic.AnimState.PlayEnd)
end

function LobbyDisplayModule:GetTeammateAnimState()
    return LobbyDisplayLogic.GetTeammateAnimState()
end

function LobbyDisplayModule:GetMainRoleAnimState()
    local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
    if armedForceMode == EArmedForceMode.BHD then loginfo('【LobbyDisplayModule】 BHD跳过Display阶段') return end
    local ctrl = armedForceMode - 1
    return LobbyDisplayLogic.GetMainRoleAnimState(ctrl)
end

function LobbyDisplayModule:IsAnimStatePlayEnd(state)
    return LobbyDisplayLogic.IsAnimStatePlayEnd(state)
end

function LobbyDisplayModule:IsAnimStatePlaying(state)
    return LobbyDisplayLogic.IsAnimStatePlaying(state)
end

function LobbyDisplayModule:SetAllAnimEnd()
    LobbyDisplayLogic.SetAllAnimEnd()
end

function LobbyDisplayModule:PlayMainFlowSeqByName(InMainFlowCtrl,SeqName)
    LobbyDisplayLogic.PlayMainFlowSeqByName(InMainFlowCtrl,SeqName)
end

function LobbyDisplayModule:ForceUpdateRangeDisplay()
    ViewSwitchLogic.OnSwitchView_Range()
end


------------ MainFlow API------------ 
function LobbyDisplayModule:RefreshWeaponOfMainHall()
    local curViewId = ViewSwitchLogic.GetCurrentViewIDCompatibly()
    local slotType = Module.ArmedForce:GetCurSlotType()
    local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
    if armedForceMode == EArmedForceMode.MP then
        if curViewId == UIName2ID.BattleFieldPresetPanel or curViewId == UIName2ID.BattleFieldPresetPanelHD then
            -- local curSelectedPageIdx = Server.ArmedForceServer:GetMPCurTempPageIdx()
            -- LobbyDisplayLogic.TryOnMPPresetByPageIdx(curSelectedPageIdx)
            local curArmId = Server.ArmedForceServer:GetMPArmedForceId()
            LobbyDisplayLogic.TryOnMPPresetByArmId(curArmId)
        else
            LobbyDisplayLogic.FreshMPWearingEquipmentsAndWeapons()
        end
    elseif armedForceMode ~= EArmedForceMode.BHD then
        LobbyDisplayLogic.FreshSOLWearingEquipmentsAndWeapons()
    end
end

return LobbyDisplayModule