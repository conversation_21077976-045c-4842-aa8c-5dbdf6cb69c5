----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMIrisSafeHouse)
----- LOG FUNCTION AUTO GENERATE END -----------

local ActivityMainPage = require "DFM.Business.Module.BattlefieldEntryModule.UI.BattlefieldEntryMainPanel.ActivityMainPage"
local PrepareLogic = require "DFM.Business.Module.IrisSafeHouseModule.Logic.IrisSafeHousePrepareLogic"
local TopLeftRegion = require("DFM.Business.Module.BattlefieldEntryModule.UI.BattlefieldEntryMainPanel.TopLeftRegion")
local LobbyTopBar = require "DFM.Business.Module.CommonBarModule.UI.TopBottomBar.LobbyTopBar"
local LobbyBottomBar = require "DFM.Business.Module.CommonBarModule.UI.TopBottomBar.LobbyBottomBar"
-- local TeamRegion    = require("DFM.Business.Module.BattlefieldEntryModule.UI.BattlefieldEntryMainPanel.TeamRegion")
local LobbyBannerView = require("DFM.Business.Module.BattlefieldEntryModule.UI.BattlefieldEntryMainPanel.LobbyBannerView")
local Config = require("DFM.Business.Module.IrisSafeHouseModule.IrisSafeHouseConfig")
local ButtonIdConfig = require("DFM.StandaloneLua.BusinessTool.LogAnalysis.ButtonIdConfig")
local EHallCharacterDisplayStage = import "EHallCharacterDisplayStage"
local FAnchors = import "Anchors"
local ULuaSubsystem = import "LuaSubsystem"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
local EMainFlowCtrlType = import "EMainFlowCtrlType"
local UDFMLightCoordinateSubsystem = import "DFMLightCoordinateSubsystem"
local UGameVersionUtils = import "GameVersionUtils"
local MossMainPanel = require("DFM.Business.Module.MossModule.UI.MossMainPanel")
local ActivityRedDotLogic = require("DFM.Business.Module.ActivityModule.Logic.ActivityRedDotLogic")
local PlayerReturnEntrance = require "DFM.Business.Module.PlayerReturnModule.UI.PlayerReturnEntrance"

local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"
local ButtonIdConfig = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.ButtonIdConfig"
local ButtonIdEnum = ButtonIdConfig.Enum

--- BEGIN MODIFICATION @ VIRTUOS
local EGPInputType = import("EGPInputType")
local UGPInputHelper = import("GPInputHelper")
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local UGPInputDelegates = import "GPInputDelegates"
local UGPUINavigationUtils = import("GPUINavigationUtils")
--- END MODIFICATION

local UDFMGameNotch = import "DFMGameNotch"
local DFMGameNotch = UDFMGameNotch.Get(GetGameInstance())

local HD_WIFI_DOWNLOAD_MODEL_LEVEL_CN = 2
local HD_WIFI_DOWNLOAD_MODEL_LEVEL_GLOBAL = 4
local PerfGearPipeline = import "PerfGearPipeline"
local PerfGearPipelineInst = PerfGearPipeline.Get()

if IsHD() then
    local HallPrepareRegionHD = require("DFM.Business.Module.IrisSafeHouseModule.UI.HD.HallPrepareRegionHD")
else
    local HallPrepareRegion = require("DFM.Business.Module.IrisSafeHouseModule.UI.HallPrepareRegion")
end

---@class IrisWorldEntryMainPanel : LuaUIBaseView
local IrisWorldEntryMainPanel = ui("IrisWorldEntryMainPanel")

function IrisWorldEntryMainPanel:Ctor()
    loginfo("IrisWorldEntryMainPanel:Ctor")
    self._wtLobbyTopBar = self:Wnd("WBP_Common_LobbyTopBar", LobbyTopBar)
    self._wtLobbyBottomBar = self:Wnd("WBP_Common_LobbyBottomBar", LobbyBottomBar)

    -- 准备区域
    if IsHD() then
        local mobileStartBtn = self:Wnd("wtPrepareRegion", UIWidgetBase)
        if mobileStartBtn then
            mobileStartBtn:Collapsed()
        end
        self._wtPrepareRegion = self:Wnd("WBP_Hall_StartPC", HallPrepareRegionHD)
        self._wtPrepareRegion:SelfHitTestInvisible()
    else
        local pcStartBtn = self:Wnd("WBP_Hall_StartPC", UIWidgetBase)
        if pcStartBtn then
            pcStartBtn:Collapsed()
        end
        self._wtPrepareRegion = self:Wnd("wtPrepareRegion", HallPrepareRegion)
    end
    self._wtPrepareRegion:SetPrepareLogic(PrepareLogic)
    self._wtPrepareRegion:SetMpOrSOL(false)

    --self:Wnd("Image_142",UIImage):Collapsed()

    -- 包含全部子控件的CanvasPanel
    self._wtBattleMainPanel = self:Wnd("wtBattleMainPanel", UIWidgetBase)
    --名字画布
    self._wtNameModelPanel = self:Wnd("NameModelPanel", UIWidgetBase)

    --BP通行证
    self._wtBannerBox = self:Wnd("wtBannerBox", UIWidgetBase)
    self._wtBannerPanel = self:Wnd("wtBannerPanel", LobbyBannerView)

    --活动系统
    self._wtActivityPanelBox = self:Wnd("wtActivityPanelBox", UIWidgetBase)
    self._wtActivityPanel = self:Wnd("wtActivityPanel", ActivityMainPage)
    self._wtActivityPanelBox:Collapsed() --[MS24不显示]
    -- if VersionUtil.IsInReview() then
    --     loginfo("[ModuleDailyTask] SOL IsInReview")
    --     self._wtActivityPanelBox:Collapsed()
    -- else
    --     self._wtActivityPanelBox:SelfHitTestInvisible()
    -- end

    --玩家回流活动入口
    if IsHD() then
        self._wtPlayerReturnEntrance = self:Wnd("WBP_Reflow_Entrance", PlayerReturnEntrance)
    end

    self._wtChatHistory = self:Wnd("WBP_SafeRoomChatHistory", UIWidgetBase)
    if IsHD() then
        -- self:Wnd("WBP_TipsRes_PowerAndSignal",UIWidgetBase):Collapsed()
    else
        -- self:Wnd("WBP_TipsRes_PowerAndSignal",UIWidgetBase):Visible()
        if self._wtChatHistory then
            self._wtChatHistory:Collapsed()
        end
        if self._wtLobbyTopBar then
            self._wtLobbyTopBar:Collapsed()
        end
        if self._wtLobbyBottomBar then
            self._wtLobbyBottomBar:Collapsed()
        end
    end

    -- self._wtBPEntrance = self:Wnd("wtBPEntrance",UIWidgetBase)
    -- self._wtActivityEntrance = self:Wnd("wtActivityEntrance",UIWidgetBase)
    -- if IsHD() then
    -- else
    --     self._wtBPEntrance:SetTitle(Module.BattlefieldEntry.Config.Loc.BPTitle)
    --     self._wtBPEntrance:BindClickCallback(CreateCallBack(self._OnBPBtnClick, self))
    --     Module.ReddotTrie:RegisterStaticReddotDotWithConfig(
    --         self._wtBPEntrance:GetReddotWidgetComp(),
    --         {{
    --             obType = EReddotTrieObserverType.BattlePass,
    --             key = "",
    --         }}
    --     )
    --     self._wtActivityEntrance:SetTitle(Module.Activity.Config.Loc.TopBarTitle)
    --     self._wtActivityEntrance:BindClickCallback(CreateCallBack(self._OnActivityBtnClick, self))
    --     local activityRedDotKey
    --     if Module.Activity.Field:IsFilterByModeOn() then
    --         activityRedDotKey = ActivityRedDotLogic.KeyForMode(Server.ArmedForceServer:GetCurArmedForceMode())
    --     else
    --         activityRedDotKey = ActivityRedDotLogic.KeyForRoot()
    --     end
    --     Module.ReddotTrie:RegisterStaticReddotDotWithConfig(
    --         self._wtActivityEntrance:GetReddotWidgetComp(),
    --         {{
    --             obType = EReddotTrieObserverType.Activity,
    --             key = activityRedDotKey,
    --         }}
    --     )
    -- end

    --专家、藏品、背包、载具
    self._masterTabHandle = nil
    self._collectionTabHandle = nil
    self._equipTabHandle = nil
    self._vehicleTabHandle = nil

    -- self:_InitNameModelItems()

    ---@type SingleUIHandle|nil 匹配计时控件
    ---@deprecated use CommonMatchTime widget
    self._counterHandle = nil

    ---@type SingleUIHandle|nil 左上角导航条控件
    self._tabHandle = nil

    self._cameraMovedDoneDelegate = nil
    if IsHD() then
        --相对于模型根节点的偏移量
        self.modelPositionOffset = {
            {X = 0, Y = 0, Z = 90},
            {X = 0, Y = 0, Z = 90},
            {X = 0, Y = 0, Z = 90},
            {X = 0, Y = 0, Z = 90}
        }
    else
        self.modelPositionOffset = {
            {X = 0, Y = 0, Z = 10},
            {X = 0, Y = 0, Z = 10},
            {X = 0, Y = 0, Z = 10},
            {X = 0, Y = 0, Z = 10}
        }
    end
    -- BEGIN MODIFICATION @ VIRTUOS: TRC - move third player's model item down to prevent banner covering.
    if IsConsole() then
        self.modelPositionOffset = {
            {X = 0, Y = 0, Z = 90},
            {X = 0, Y = 0, Z = 90},
            {X = 0, Y = 0, Z = 10},
            {X = 0, Y = 0, Z = 90}
        }
    end
    -- END MODIFICATION
    if DFHD_LUA == 1 then
        Module.CommonBar:RegStackUITopBarStyle(
            self,
            Module.CommonBar.Config.TopBarStyle.DefaultSecondary | Module.CommonBar.Config.ETopBarStyleFlag.Cybercafe |
                Module.CommonBar.Config.ETopBarStyleFlag.Recovery
        )

        -- BEGIN MODIFICATION @ VIRTUOS : 初始化InputSummaries
        -- self._wtSideBar = self:Wnd("WBP_SideBar_Gamepad", SideBarGamepad)
        self._MainTabInputSummaryV2 = {}
        self._MainTabInputSummaryV2_SideBar = {}
        self:_InitMainTabInputSummaryV2(self._MainTabInputSummaryV2)
        self:_InitMainTabInputSummaryV2(self._MainTabInputSummaryV2_SideBar, true)

        --视情况开启自建房入口
        self:_OnRegRoomHD(self._MainTabInputSummaryV2)
        self:_OnRegRoomHD(self._MainTabInputSummaryV2_SideBar)
        -- END MODIFICATION

        Module.CommonBar:RegStackUINavInputSummary(
            self,
            {{actionName = "SwitchLobbyMode", func = self._ToggleBarSwitchMode, caller = self}}
        )
    else
        --- MS24 待yx充分自测开启
        --Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.LobbyMenu)
        --self:_RegBottomBarTab()
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.Hidden)
    end
    Module.CommonBar:RegStackUITopBarTitle(self, "")
    Module.CommonBar:RegStackUITopBarCurrencyTypeList(self, {ECurrencyClientId.Tina, ECurrencyClientId.Diamond})
    Module.CommonBar:RegStackUITopBarNetSignal(self, true)
end

function IrisWorldEntryMainPanel:_OnRegRoomHD(MainTabInputSummaryV2)
    loginfo(
        "自建房开启 BattlefieldEntryMainPanel:_OnRegRoomHD(MainTabInputSummaryV2)",
        Module.ModuleSwitcher:CheckModuleSwitcher(SwitchSystemID.SwitchSystemSOLRoom)
    )
    local roomIdx = -1
    for idx, v in pairs(MainTabInputSummaryV2) do
        if v.actionName == "OpenRoomSelectPanel" then
            roomIdx = idx
            break
        end
    end
    if Module.ModuleSwitcher:CheckModuleSwitcher(SwitchSystemID.SwitchSystemSOLRoom) ~= EFirstLockResult.Open then
        if roomIdx ~= -1 then
            table.remove(MainTabInputSummaryV2, roomIdx)
        end
    else
        if roomIdx == -1 then
            table.insert(
                MainTabInputSummaryV2,
                {
                    actionName = "OpenRoomSelectPanel",
                    func = function()
                        Module.Room:ShowSelectRoomPanel()
                    end,
                    iconID = "IconRoom"
                }
            )
        end
    end
end

function IrisWorldEntryMainPanel:_RegBottomBarTab()
    local tabTxtList = {}
    local tabTxtEnList = {}

    local lockDataList = {}
    local reddotTrieRegItemList = {}
    for index, tabInfo in ipairs(Config.MobileMainPanelTabInfo) do
        ---@type RawUILockData
        local lockData = {
            firstLockID = tabInfo.firstLockID,
            secondLockID = tabInfo.secondLockID,
            uiNavID = tabInfo.openUIID,
            fFailedCallback = tabInfo.openFaildFunc,
            failedCallbackCaller = nil,
            fCustomLockChecker = tabInfo.customCheckFunc,
            customLockCheckerCaller = nil
        }
        table.insert(tabTxtList, tabInfo.name)
        table.insert(tabTxtEnList, tabInfo.enName)
        table.insert(lockDataList, lockData)
        table.insert(reddotTrieRegItemList, tabInfo.reddotTrieTabRegItem or {})
    end

    local reddotTrieList = {}

    local topTabGroupRegInfo = {
        tabTxtList = tabTxtList,
        tabTxtEnList = tabTxtEnList,
        fCallbackIns = CreateCallBack(self._OnMainTabClicked, self),
        defalutIdx = 1,
        bTriggerCallback = true,
        bNewReddotTrie = true,
        reddotTrieRegItemList = reddotTrieRegItemList,
        lockDataList = lockDataList
    }
    Module.CommonBar:RegStackUIBottomBarTabGroupRegInfo(self, topTabGroupRegInfo)
end

function IrisWorldEntryMainPanel:_OnMainTabClicked(curActiveIndex, lastActiveIndex)
    local tabInfo = Config.MobileMainPanelTabInfo[curActiveIndex]
    Module.Guide:SendMsg(EGuideMsgSig.IrisWorldEntryMainPanelBottomBarTabClicked, curActiveIndex, lastActiveIndex, tabInfo)
    if tabInfo and tabInfo.openFunc then
        tabInfo.openFunc()
    end
end

function IrisWorldEntryMainPanel:OnOpen()
    LiteDownloadManager:SetMountGameMode(EMountGameMode.SOL)
    -- loginfo("[heimu] IrisWorldEntryMainPanel:OnOpen")
    -- if not VersionUtil.IsShipping() then
    --     Module.GM:OpenGMButtonPanel()
    -- end

    loginfo("[xxww] IrisWorldEntryMainPanel:OnOpen")
    --self:_CheckShowModule()
    self:_CheckModuleSwitcher()

    --手游排位赛入口
    if not IsHD() then
        self._wtRankEntranceBox = self:Wnd("PlatformPaddingBox_1", UIWidgetBase)
        self._wtRankEntrance = self:Wnd("WBP_Hall_Season", UIWidgetBase)
        self._wtRankEntranceBox:SelfHitTestInvisible()
        self._wtRankEntrance:InitRankEntrance(Module.Ranking.Config.RankModeType.Ranking)
    end
    -- if Server.SettlementServer:GetTDMSettlementWeaponChangeFlag() then
    --     local weaponChangeInfo = Server.SettlementServer:GetTDMWeaponChangeInfo()
    --     if weaponChangeInfo then
    --         Server.SettlementServer.bShowWeaponChangeUI = false
    --         Module.Settlement:OpenWeaponUpgradeDetailPanel(weaponChangeInfo,
    --             Server.SettlementServer.tdmSettlementAccountExp, Server.SettlementServer.tdmResult)
    --     end
    -- end
    self:AddLuaEvent(Server.TeamServer.Events.evtJoinTeam, self._OnJoinTeam, self)
    self:AddLuaEvent(Server.TeamServer.Events.evtYouLeaveTeam, self._OnLeaveTeam, self)
    self:AddLuaEvent(Server.TeamServer.Events.evtTeamInfosUpdated, self._OnTeamMemberChanged, self)
    self:AddLuaEvent(Server.TeamServer.Events.evtTeammateJoined, self._OnTeammateJoined, self)
    self:AddLuaEvent(Server.TeamServer.Events.evtTeammateLeft, self._OnTeammateLeft, self)
    self:AddLuaEvent(Server.TeamServer.Events.evtCaptialChanged, self._OnCaptialChanged, self)
    self:AddLuaEvent(Server.TeamServer.Events.evtTeammateEquipChange, self._OnTeamMemberEquipChanged, self)
    self:AddLuaEvent(Server.TeamServer.Events.evtTeammateStateChanged, self._OnTeamMemberStateChanged, self)

    self:AddLuaEvent(Module.Hero.Config.Events.evtSelectHeroIdForShowChanged, self._OnHeroSelectedChanged, self)
    self:AddLuaEvent(Server.MatchServer.Events.evtStartMatching, self._OnStartMatching, self)
    self:AddLuaEvent(Server.MatchServer.Events.evtPrepareJoinMatch, self._OnPrepareJoinMatch, self)
    self:AddLuaEvent(Server.MatchServer.Events.evtStopPreShowLoadingView, self._OnStopPreShowLoadingView, self)

    self:AddLuaEvent(Server.ModuleUnlockServer.Events.evtSwitchMoudleUnlock, self._UpdateDailyTask, self)
    self:AddLuaEvent(Server.ModuleSwitcherServer.Events.evtModuleSwitcherUpdated, self._ShowModuleNtf, self)

    self:AddLuaEvent(Server.RoleInfoServer.Events.evtSeasonLevelUpdate, self._OnSeasonLevelUp, self)
    self:AddLuaEvent(Server.ActivityServer.Events.evtActivityInfoUpdate, self._OnMainPageLoad, self)
    --大厅活动资源更新

    self:AddLuaEvent(Facade.ProtoManager.Events.evtOnTmpRelayConnected, self._OnReconnectLoginOperate, self)
    self:AddLuaEvent(Facade.UIManager.Events.evtLayerBinaryKeyReset, self._OnLayerBinaryKeyReset, self)

    self:AddLuaEvent(Server.ModuleUnlockServer.Events.evtOnLobbyBannerDataLoaded, self._OnLobbyBannerDataLoaded, self)
    self:AddLuaEvent(
        Server.ModuleUnlockServer.Events.evtOnLobbyEntranceDataLoaded,
        self._OnLobbyEntranceDataLoaded,
        self
    )
    self:AddLuaEvent(Facade.UIManager.Events.evtOnCultureChanged, self._OnCultureChanged, self)

    self:AddLuaEvent(Server.RecruitServer.Events.evtOnRecruitmentEnded, self._RecruitEnded, self)

    --邮件和好友红点通知
    --[[     self:AddLuaEvent(Server.MailServer.Events.evtReceiveNewMail, self._RefreshMailAndFriendRedDot, self)
    self:AddLuaEvent(Module.Mail.Config.evtSocialPanelClose, self._RefreshMailAndFriendRedDot, self)
    self:AddLuaEvent(Server.FriendServer.Events.evtFetchRedDot, self._RefreshMailAndFriendRedDot, self) ]]
    self:AddLuaEvent(LuaGlobalEvents.evtSceneLoaded, self._OnRefreshModel, self)
    self:AddLuaEvent(Module.LobbyDisplay.Config.Events.evtStartCameraMove, self._OnStartCameraMove, self)
    self:AddLuaEvent(Module.LobbyDisplay.Config.Events.evtEndCharacterAnimPlayEnd, self._OnCharacterAnimPlayEnd, self)

    -- end
    self:AddLuaEvent(Server.TeamServer.Events.evtYouAreKicked, self._OnMainRoleLeft, self)
    self:AddLuaEvent(Module.FacePop.Config.evtFacePopClosed, self._OnFacePopClosed, self)
    self:AddLuaEvent(Module.FacePop.Config.evtFacePopJumped, self._OnFacePopJumped, self)
    --self:AddLuaEvent(Server.MatchServer.Events.evtEndMatching, self._OnEndMatching, self)
    ULuaSubsystem.Get().OnNotifyLuaResolutionResized:Add(self._OnNotifyResolutionResized, self) -- 分辨率变化后需要重新计算位置

    self:AddLuaEvent(Module.Moss.Config.evtMossPanelOnShow, self._OnMossPanelOnShow, self)
    self:AddLuaEvent(Module.Moss.Config.evtMossPanelOnHide, self._OnMossPanelOnHide, self)

    self:AddLuaEvent(Server.InventoryServer.Events.evtInventoryFetchFinished, self._OnReconnected, self)
    self:AddLuaEvent(Server.HeroServer.Events.evtHeroCardReddotsChanged, self._OnReconnected, self)
    self:AddLuaEvent(Server.SettlementServer.Events.evtInventoryCollectionFetchFinished,self._OnRefreshCharacter,self)
    Facade.ProtoManager.Events.evtOnRelayConnected:AddListener(self._OnRelayConnected, self)  --断线重连

    local ctrl = Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.MainFlowLobby)
    if ctrl then
        self.activeHandle = ctrl.OnWindowActive:Add(CreateCPlusCallBack(self.OnGameFronted, self))
        self.loadedHandle = ctrl.OnAnimSeqLoaded:Add(CreateCPlusCallBack(self.OnAnimSeqLoaded, self))
    end
    -- 英雄界面展示英雄需要穿戴时装
    Module.LobbyDisplay:SetWearSuit(true)

    if Module.IrisSafeHouse.JumpParam then --活动界面跨关卡跳转地图
        local jumpMode = Module.IrisSafeHouse.JumpParam.jumpMode
        local needWait = false
        if jumpMode == 0 then
            needWait = true
        elseif jumpMode == 1 then
            local redirectParam2 = Module.IrisSafeHouse.JumpParam.jumpParam[2]
            if redirectParam2 == 1 then
                needWait = true
            end
        end
        if needWait then
            self.timer = Timer:NewIns(0.1, 0)
            self.timer:AddListener(self.CheckSandBoxResourceComplete, self)
            self.timer:Start()
        else
            Module.GameMode:JumpToSubMode(Module.IrisSafeHouse.JumpParam)
            Module.IrisSafeHouse.JumpParam = nil
        end
    end

    if LiteDownloadManager:IsSupportLitePackage() then
        --self._wtDownloadWidget:Visible()
        LiteDownloadManager:StartCheckModuleInfosAsync()
    else
        --self._wtDownloadWidget:Collapsed()
    end

    --判断在不在自建房中
    if Server.AccountServer:IsInRoom() then
        logwarning("IrisWorldEntryMainPanel:OnOpen GotoRoom")
        Module.Room:GoBackRoom()
    end
    --self:_RefreshMailAndFriendRedDot()

    --活动拉新数据
    Server.ActivityServer:ResetActLastTriggerTime()
    Server.ActivityServer:InitActivityInfo()

    LiteDownloadManager:ResetAllDownloadedQuestIDs()
    self._wtBannerBox:Collapsed()
    Server.ModuleUnlockServer:ReqGetLobbyBannerData(CreateCallBack(self._OnLobbyBannerDataLoaded, self))
    -- self._wtBPEntrance:Collapsed()
    -- self._wtActivityEntrance:Collapsed()
    if not IsHD() then
        Server.ModuleUnlockServer:ReqGetLobbyEntranceData(CreateCallBack(self._OnLobbyEntranceDataLoaded, self))
    end

    if Module.Jump:HasJumpSingleInWait() then
        Timer.DelayCall(
            2,
            function()
                --等资源加载完成
                Module.Jump:TriggerJumpModeSuccess()
            end
        )
    end

    Module.Quest:ShowUpdateQuestTips()

    self:CheckNeedJumpToArena()

    self:CheckLiteWifiDownload()
end

function IrisWorldEntryMainPanel:CheckNeedJumpToArena()
    if Module.IrisSafeHouse:GetNeedJumpToArena() then
        Module.IrisSafeHouse:SetNeedJumpToArena(false)
        Module.LobbyDisplay:SetAllAnimEnd()
        Module.SandBoxMap:JumpToArena()
    end
end

function IrisWorldEntryMainPanel:CheckLiteWifiDownload()
    self.HDResModuleName = "HDRuntimeCollection"
    --@dexzhou 新手阶段不下面的窗口逻辑
    if not Module.Guide:IsNewPlayerGuideFinished() then
        loginfo("BattlefieldEntryMainPanel:OnShow new player guide has not  finished, skip lite package check logics")
        LiteDownloadManager:CheckWIFIDownloadQuests(1)
        return
    end
    if LiteDownloadManager:IsSupportLitePackage() then
        local keyToPop = "FirstPopDownloadWindowForCNHDResSHE3"
        local keyToCheckDownload = "UserNeedCheckAndDownloadHDSHE3"
        local deviceLevel = PerfGearPipelineInst:GetDeviceLevel()
        local freeSpace = LiteDownloadManager:GetDeviceFreeSpace()
        local HD_LiteDownloadGBNum = 1024 * 1024 * 1024
        local spaceGB = freeSpace / HD_LiteDownloadGBNum
        local bBeginDonloadHD = false

        if IsBuildRegionCN() then
            loginfo("[IrisWorldEntryMainPanel] OnShow IsBuildRegionCN deviceLevel:" .. tostring(deviceLevel))
            if deviceLevel > 0 and deviceLevel <= HD_WIFI_DOWNLOAD_MODEL_LEVEL_CN then
                loginfo("[IrisWorldEntryMainPanel] OnShow CN check BeforeLoginHD")
                local bAlreadyPopDownloadWindow =
                    Facade.ConfigManager:GetUserBoolean(keyToPop, false)
                if not bAlreadyPopDownloadWindow then
                    local fConfirm = function()
                        if spaceGB > 3 then
                            bBeginDonloadHD = true
                            LiteDownloadManager:DownloadByModuleName(self.HDResModuleName)
                        end

                        Facade.ConfigManager:SetUserBoolean(keyToPop, true)
                        Facade.ConfigManager:SetUserBoolean(keyToCheckDownload, true)

                        LogAnalysisTool.SignButtonClicked(ButtonIdEnum.LitePOPToTipsDownloadHDOK)
                    end
                    local fCancel = function()
                        Facade.ConfigManager:SetUserBoolean(keyToPop, true)
                        Facade.ConfigManager:SetUserBoolean(keyToCheckDownload, false)

                        LogAnalysisTool.SignButtonClicked(ButtonIdEnum.LitePOPToTipsDownloadHDCancel)
                    end
                    Module.CommonTips:ShowConfirmWindow(
                        Module.LitePackage.Config.Loc.POPWINDOW_FOR_DOWNLOAD_HDRES_CN,
                        fCancel,
                        fConfirm,
                        Module.SystemSetting.Config.Loc.confirmBtnText,
                        Module.SystemSetting.Config.Loc.cancelBtnText
                    )
                end
            end
        end

        if IsBuildRegionGlobal() then
            -- Facade.ConfigManager:SetUserBoolean("FirstPopDownloadWindowForGlobalHDRes", false)
            loginfo("[IrisWorldEntryMainPanel] OnShow IsBuildRegionGlobal deviceLevel:" .. tostring(deviceLevel))

            loginfo("[IrisWorldEntryMainPanel] OnShow IsBuildRegionGlobal spaceGB:" .. tostring(spaceGB))
            if deviceLevel > HD_WIFI_DOWNLOAD_MODEL_LEVEL_GLOBAL and spaceGB > 5 then
                loginfo("[IrisWorldEntryMainPanel] OnShow Global check BeforeLoginHD")
                local bAlreadyPopDownloadWindow =
                    Facade.ConfigManager:GetUserBoolean(keyToPop, false)
                if not bAlreadyPopDownloadWindow then
                    local fConfirm = function()
                        if spaceGB > 3 then
                            bBeginDonloadHD = true
                            LiteDownloadManager:DownloadByModuleName(self.HDResModuleName)
                        end
                        Facade.ConfigManager:SetUserBoolean(keyToPop, true)
                        Facade.ConfigManager:SetUserBoolean(keyToCheckDownload, true)

                        LogAnalysisTool.SignButtonClicked(ButtonIdEnum.LitePOPToTipsDownloadHDOK)
                    end
                    local fCancel = function()
                        Facade.ConfigManager:SetUserBoolean(keyToPop, true)
                        Facade.ConfigManager:SetUserBoolean(keyToCheckDownload, false)

                        LogAnalysisTool.SignButtonClicked(ButtonIdEnum.LitePOPToTipsDownloadHDCancel)
                    end
                    Module.CommonTips:ShowConfirmWindow(
                        Module.LitePackage.Config.Loc.POPWINDOW_FOR_DOWNLOAD_HDRES_CN,
                        fConfirm,
                        fCancel
                    )
                end
            end
        end

        if IsBuildRegionCN() or IsBuildRegionGlobal() then
            local bUserNeedCheckAndDownloadHD = Facade.ConfigManager:GetUserBoolean(keyToCheckDownload, false)
            if bUserNeedCheckAndDownloadHD then
                local bDownloaded = LiteDownloadManager:IsDownloadedByModuleName(self.HDResModuleName)
                if bDownloaded == false then
                    if spaceGB > 3 then
                        bBeginDonloadHD = true
                        LiteDownloadManager:DownloadByModuleName(self.HDResModuleName)
                    end
                end
            end
        end

        if bBeginDonloadHD == false then
            LiteDownloadManager:CheckWIFIDownloadQuests(1)
        end
    end
end

function IrisWorldEntryMainPanel:_OnMainPageLoad()
    -- local activityInfoList = Server.ActivityServer:GetActivityInfosByType(ActivityType.ActivityTypeDaily)
    -- local moduleUnlockInfo = Module.ModuleUnlock:GetModuleUnlockInfoById(SwitchModuleID.ModuleDailyTaskSOL)
    -- if activityInfoList and activityInfoList[1] then
    --     local activityInfo = {}
    --     for _, value in ipairs(activityInfoList) do
    --         if value.mode_leaning == 1 then
    --             activityInfo = value
    --             break
    --         end
    --     end
    --     if next(activityInfo) then
    --         if moduleUnlockInfo.bIsUnlocked == true and activityInfo.task_info and next(activityInfo.task_info) then
    --             self._wtActivityPanel:SelfHitTestInvisible()
    --         else
    --             self._wtActivityPanel:Collapsed()
    --         end
    --     else
    --         logwarning("[ModuleDailyTask] SOL activityInfo error")
    --         self._wtActivityPanel:Collapsed()
    --     end
    -- else
    --     logwarning("[ModuleDailyTask] SOL activityInfoList error")
    --     self._wtActivityPanel:Collapsed()
    -- end
end

function IrisWorldEntryMainPanel:_UpdateDailyTask(moduleId, isUnlock)
    -- if moduleId == SwitchModuleID.ModuleDailyTaskSOL then
    --     loginfo("[ModuleDailyTask] SOL Update")
    --     if isUnlock == true then
    --         loginfo("[ModuleDailyTask] SOL UnLock")
    --         Server.ActivityServer:InitActivityInfo()
    --         self._wtActivityPanel:SelfHitTestInvisible()
    --         self._wtActivityPanel:RefreshUI()
    --     else
    --         loginfo("[ModuleDailyTask] SOL Lock")
    --         self._wtActivityPanel:Collapsed()
    --     end
    -- end
    Server.ModuleUnlockServer:ReqGetLobbyBannerData()
end

function IrisWorldEntryMainPanel:_ToggleBarSwitchMode()
    if Module.Moss:GetMossIsShowed() then
        Module.Moss:HideMoss()
        return
    end
    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIBack)
    Module.CommonBar:ShowBarSwitchMode()

    if DFHD_LUA == 0 then
        LogAnalysisTool.SignButtonClicked(ButtonIdEnum.SOLLobbySwitchToMP)
    else
        LogAnalysisTool.SignButtonClicked(10120010)
    end

    logerror("[IrisWorld] click esc")
end

function IrisWorldEntryMainPanel:_ToggleSettingEntrance()
    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIBack)
    -- Module.SystemSetting:ShowOrHideSystemSettingHDEntrance()

    if DFHD_LUA == 0 then
        LogAnalysisTool.SignButtonClicked(ButtonIdEnum.SOLLobbySetting)
    else
        LogAnalysisTool.SignButtonClicked(10120010)
    end

    logerror("[BattleField] mp click esc")
end

function IrisWorldEntryMainPanel:_OnRelayConnected()
    if LiteDownloadManager:IsSupportLitePackage() then
        LiteDownloadManager:CancelAll()
    end
end

function IrisWorldEntryMainPanel:OnClose()
    Facade.ProtoManager.Events.evtOnRelayConnected:RemoveListener(self._OnRelayConnected, self)
    Module.ExpansionPackCoordinator:PauseAll()
    -- LiteDownloadManager:ClearQuestToDownload()
    --LiteDownloadManager:CancelAll() --quit sol, stop all quest
    loginfo("IrisWorldEntryMainPanel:OnClose")
    self:RemoveAllLuaEvent()
    ULuaSubsystem.Get().OnNotifyLuaResolutionResized:Remove(self._OnNotifyResolutionResized, self)

    -- 尝试移除监听镜头变换
    local cameraMovedDoneDelegate = Facade.HallSceneManager:TryGetSceneCameraMovedDone(ESubStage.MainFlowLobby)
    if cameraMovedDoneDelegate ~= nil then
        cameraMovedDoneDelegate:Remove(self._OnEndCameraMove, self)
        logwarning("[darc] 取消监听HallCameraMovedDone")
    end
    self._cameraMovedDoneDelegate = nil
    if self._tabHandle then
    -- Facade.UIManager:CloseUIByHandle(self._tabHandle)
    -- self._tabHandle = nil
    end

    self:RemoveLuaEvent(Server.ModuleUnlockServer.Events.evtSwitchMoudleUnlock)
    --Facade.UIManager:ClearSubUIByParent(self,self._wtNameModelPanel)
end

function IrisWorldEntryMainPanel:OnActivate()
    loginfo("[v_dzhanshen] IrisWorldEntryMainPanel:OnActivate")
    self:_CheckModuleSwitcher()
    self:AddLuaEvent(Server.TeamServer.Events.evtJoinTeam, self._OnJoinTeam, self)
    self:AddLuaEvent(Server.TeamServer.Events.evtYouLeaveTeam, self._OnLeaveTeam, self)
    self:AddLuaEvent(Server.TeamServer.Events.evtTeamInfosUpdated, self._OnTeamMemberChanged, self)
    self:AddLuaEvent(Server.TeamServer.Events.evtTeammateJoined, self._OnTeammateJoined, self)
    self:AddLuaEvent(Server.TeamServer.Events.evtTeammateLeft, self._OnTeammateLeft, self)
    self:AddLuaEvent(Server.TeamServer.Events.evtCaptialChanged, self._OnCaptialChanged, self)
    self:AddLuaEvent(Server.TeamServer.Events.evtTeammateEquipChange, self._OnTeamMemberEquipChanged, self)
    self:AddLuaEvent(Server.TeamServer.Events.evtTeammateStateChanged, self._OnTeamMemberStateChanged, self)

    self:AddLuaEvent(Module.Hero.Config.Events.evtSelectHeroIdForShowChanged, self._OnHeroSelectedChanged, self)
    self:AddLuaEvent(Server.MatchServer.Events.evtStartMatching, self._OnStartMatching, self)
    self:AddLuaEvent(Server.MatchServer.Events.evtPrepareJoinMatch, self._OnPrepareJoinMatch, self)
    self:AddLuaEvent(Server.MatchServer.Events.evtStopPreShowLoadingView, self._OnStopPreShowLoadingView, self)

    self:AddLuaEvent(Server.ModuleUnlockServer.Events.evtSwitchMoudleUnlock, self._UpdateDailyTask, self)
    self:AddLuaEvent(Server.ModuleSwitcherServer.Events.evtModuleSwitcherUpdated, self._ShowModuleNtf, self)

    self:AddLuaEvent(Server.RoleInfoServer.Events.evtSeasonLevelUpdate, self._OnSeasonLevelUp, self)
    self:AddLuaEvent(Server.ActivityServer.Events.evtActivityInfoUpdate, self._OnMainPageLoad, self)
    --大厅活动资源更新

    self:AddLuaEvent(Facade.ProtoManager.Events.evtOnTmpRelayConnected, self._OnReconnectLoginOperate, self)
    self:AddLuaEvent(Facade.UIManager.Events.evtLayerBinaryKeyReset, self._OnLayerBinaryKeyReset, self)

    self:AddLuaEvent(Server.ModuleUnlockServer.Events.evtOnLobbyBannerDataLoaded, self._OnLobbyBannerDataLoaded, self)
    self:AddLuaEvent(
        Server.ModuleUnlockServer.Events.evtOnLobbyEntranceDataLoaded,
        self._OnLobbyEntranceDataLoaded,
        self
    )
    self:AddLuaEvent(Facade.UIManager.Events.evtOnCultureChanged, self._OnCultureChanged, self)

    self:AddLuaEvent(Server.RecruitServer.Events.evtOnRecruitmentEnded, self._RecruitEnded, self)
    self:AddLuaEvent(LuaGlobalEvents.evtSceneLoaded, self._OnRefreshModel, self)
    self:AddLuaEvent(Module.LobbyDisplay.Config.Events.evtStartCameraMove, self._OnStartCameraMove, self)
    self:AddLuaEvent(Module.LobbyDisplay.Config.Events.evtEndCharacterAnimPlayEnd, self._OnCharacterAnimPlayEnd, self)
    self:AddLuaEvent(Server.TeamServer.Events.evtYouAreKicked, self._OnMainRoleLeft, self)
    self:AddLuaEvent(Module.FacePop.Config.evtFacePopClosed, self._OnFacePopClosed, self)
    self:AddLuaEvent(Module.FacePop.Config.evtFacePopJumped, self._OnFacePopJumped, self)
    ULuaSubsystem.Get().OnNotifyLuaResolutionResized:Add(self._OnNotifyResolutionResized, self) -- 分辨率变化后需要重新计算位置

    self:AddLuaEvent(Module.Moss.Config.evtMossPanelOnShow, self._OnMossPanelOnShow, self)
    self:AddLuaEvent(Module.Moss.Config.evtMossPanelOnHide, self._OnMossPanelOnHide, self)
    self:AddLuaEvent(Server.SettlementServer.Events.evtInventoryCollectionFetchFinished,self._OnRefreshCharacter,self)
    -- 英雄界面展示英雄需要穿戴时装
    Module.LobbyDisplay:SetWearSuit(true)
    if Module.IrisSafeHouse.JumpParam then --活动界面跨关卡跳转地图
        local jumpMode = Module.IrisSafeHouse.JumpParam.jumpMode
        local needWait = false
        if jumpMode == 0 then
            needWait = true
        elseif jumpMode == 1 then
            local redirectParam2 = Module.IrisSafeHouse.JumpParam.jumpParam[2]
            if redirectParam2 == 1 then
                needWait = true
            end
        end
        if needWait then
            self.timer = Timer:NewIns(0.1, 0)
            self.timer:AddListener(self.CheckSandBoxResourceComplete, self)
            self.timer:Start()
        else
            Module.GameMode:JumpToSubMode(Module.IrisSafeHouse.JumpParam)
            Module.IrisSafeHouse.JumpParam = nil
        end
    end

    if LiteDownloadManager:IsSupportLitePackage() then
        --self._wtDownloadWidget:Visible()
        LiteDownloadManager:StartCheckModuleInfosAsync()
    else
        --self._wtDownloadWidget:Collapsed()
    end

    --判断在不在自建房中
    if Server.AccountServer:IsInRoom() then
        logwarning("IrisWorldEntryMainPanel:OnOpen GotoRoom")
        Module.Room:GoBackRoom()
    end
    --self:_RefreshMailAndFriendRedDot()

    -- 活动拉新数据
    Server.ActivityServer:InitActivityInfo()

    LiteDownloadManager:ResetAllDownloadedQuestIDs()
    self._wtBannerBox:Collapsed()
    Server.ModuleUnlockServer:ReqGetLobbyBannerData(CreateCallBack(self._OnLobbyBannerDataLoaded, self))
    -- self._wtBPEntrance:Collapsed()
    -- self._wtActivityEntrance:Collapsed()
    if not IsHD() then
        Server.ModuleUnlockServer:ReqGetLobbyEntranceData(CreateCallBack(self._OnLobbyEntranceDataLoaded, self))
    end

    if Module.Jump:HasJumpSingleInWait() then
        Timer.DelayCall(
            2,
            function()
                --等资源加载完成
                Module.Jump:TriggerJumpModeSuccess()
            end
        )
    end

    Module.Quest:ShowUpdateQuestTips()

    self:CheckNeedJumpToArena()
end

function IrisWorldEntryMainPanel:OnDeactivate()
    loginfo("[v_dzhanshen] IrisWorldEntryMainPanel:OnDeactivate")
    self:RemoveAllLuaEvent()
    ULuaSubsystem.Get().OnNotifyLuaResolutionResized:Remove(self._OnNotifyResolutionResized, self)
    -- 尝试移除监听镜头变换
    local cameraMovedDoneDelegate = Facade.HallSceneManager:TryGetSceneCameraMovedDone(ESubStage.MainFlowLobby)
    if cameraMovedDoneDelegate ~= nil then
        cameraMovedDoneDelegate:Remove(self._OnEndCameraMove, self)
        logwarning("[darc] 取消监听HallCameraMovedDone")
    end
    self._cameraMovedDoneDelegate = nil
    self._masterTabHandle = nil
    self._collectionTabHandle = nil
    self._equipTabHandle = nil
    self._vehicleTabHandle = nil
    self._tabHandle = nil
end

function IrisWorldEntryMainPanel:_OnReconnectLoginOperate()
    Server.FriendServer:FetchApplyPlayerList()
    --Server.FriendServer.Events.evtFetchRedDot:Invoke()
end

function IrisWorldEntryMainPanel:OnShow()
    if Facade.UIManager:GetIsJumpRollbackTransition() then
        Facade.UIManager:CommitTransition(false)
        Facade.UIManager:SetIsJumpRollbackTransition(false)
    end
    if not IsHD() then
        self:AddLuaEvent(Server.FrontEndChatServer.Events.evtShowAnnouncement, self.ShowAnnouncement, self)
    end
    if DFMGameNotch:IsFoldDevice() then
        if not self.statusHandle then
            self.statusHandle = DFMGameNotch.OnFoldStatusChanged:Add(CreateCPlusCallBack(self.OnFoldStatusChanged, self))
        end
    end
    --self._wtTopLeftWidget:PlayAnimationForward(self._wtTopLeftWidget.WBP_Hall_TopLeft_in,1.0,true)
    loginfo("IrisWorldMain OnShow")
    -- Module.FacePop:CheckAndShowFacePop()
    if IsHD() then
        self:_InitShortcuts()
    end
    self._startMoveCamera = false
    self:_InitNameModelItems()

    Module.IrisSafeHouse.Config.evtSafeHouseHUDOpenAnimFinish:Invoke()

    Module.LobbyDisplay:EnterLobbyDisplayPerfGear()

    Server.FrontEndChatServer:SetChatInLobby(true)
    self:ShowAnnouncement()
end

function IrisWorldEntryMainPanel:OnHide()
    Module.Moss:DoSendMossUseLog()
    Server.FrontEndChatServer:SetChatInLobby(false)
    Server.FrontEndChatServer.Events.evtHideAnnouncement:Invoke()
    Server.MossServer.Events.evtHideMoss:Invoke()
    if not IsHD() then
        self:RemoveLuaEvent(Server.FrontEndChatServer.Events.evtShowAnnouncement)
    end
    if DFMGameNotch:IsFoldDevice() then
        if self.statusHandle then
            DFMGameNotch.OnFoldStatusChanged:Remove(self.statusHandle)
            self.statusHandle = nil
        end
    end
    local ctrl = Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.MainFlowLobby)
    if ctrl then
        if self.activeHandle then
            ctrl.OnWindowActive:Remove(self.activeHandle)
            self.activeHandle = nil
        end
        if self.loadedHandle then
            ctrl.OnAnimSeqLoaded:Remove(self.loadedHandle)
            self.loadedHandle = nil
        end
    end
    --self._wtTopLeftWidget:PlayAnimationForward(self._wtTopLeftWidget.WBP_Hall_TopLeft_out,1.0,true)
    --self:PlayAnimationForward(self.WBP_Hall_Battle_Main_out_01,1.0,true)
    loginfo("IrisWorldMain OnHide")

    if self._announcementTimer then
        self._announcementTimer:Release()
        self._announcementTimer = nil
    end
end

function IrisWorldEntryMainPanel:OnShowBegin()
    -- self._wtTabHall:BP_SetType(1)
    -- self._wtTabEquip:BP_SetType(0)
    logwarning("IrisWorldEntryMainPanel:OnShowBegin")
    if IsHD() then
        Server.MossServer.Events.evtShowMoss:Invoke()
    end
    Module.IrisSafeHouse.Config.evtSafeHouseHUDOpenAnimStart:Invoke()

    -- if Server.ArmedForceServer:CheckIsRentalStatus() then
    --     Server.ArmedForceServer:SetCurSlotGroupId(ESlotGroup.MainRental)
    -- else
    --     Server.ArmedForceServer:SetCurSlotGroupId(ESlotGroup.Player)
    -- end
    if DFHD_LUA == 1 then
        -- END MODITICATION
        --Module.CommonBar:RegStackUIHideBar(self)
        -- Module.CommonBar:BindPersistentBackHandler(self._ToggleBarSwitchMode, self)

        -- BEGIN MODIFICATION @ VIRTUOS :
        self:_EnableGamepadFeature()
    else
        Module.CommonBar:BindPersistentBackHandler(self._OnQuitBtnClick, self)
        Module.CommonBar:RegStackUIBeforeBackConfirm(self, Module.SystemSetting.Config.Loc.ConfirmQuitLogin)
        self:_RegBottomBarTab()
        self._wtLobbyBottomBar:UpdateBarByCurStackUI(self)
    end
end

function IrisWorldEntryMainPanel:ShowAnnouncement()
    --检查跑马灯
    if self._bHideAnnouncement then
        return
    end
    local bShow = false
    if IsHD() then
        Server.FrontEndChatServer.Events.evtShowAnnouncement:Invoke()
        return
    else
        bShow = self._wtLobbyTopBar:ShowAnnouncement()
    end
    if not bShow then
        if self._announcementTimer then
            self._announcementTimer:Release()
            self._announcementTimer = nil
        end
    else
        if self._announcementTimer == nil then
        -- self._announcementTimer=Timer:NewIns(1,0)
        -- self._announcementTimer:AddListener(self.ShowAnnouncement,self)
        -- self._announcementTimer:Start()
        end
    end
end

function IrisWorldEntryMainPanel:OnHideBegin()
    -- 关闭界面取消英雄时装
    -- Module.LobbyDisplay:SetWearSuit(false)
    -- Module.Hero:ShowHeroById(nil)
    loginfo("IrisWorldMain OnHideBegin")
    Module.IrisSafeHouse.Config.evtIrisWorldEntryOnHideBegin:Invoke()

    if IsHD() then
        self:_RemoveShortcuts()
        -- BEGIN MODIFICATION @ VIRTUOS :
        self:_DisableGamepadFeature()
    -- END MODITICATION
    end
end

function IrisWorldEntryMainPanel:_InitShortcuts()
    local navActions = {}
    -- navActions = {
    --     {actionName="OpenChat",func=self._OnChatBtnClick},
    --     --{actionName="OpenMail",func=self._OnMailBtnClick},
    --     {actionName="OpenActivity",func=self._OnActivityBtnClick},
    --     {actionName="OpenStore",func=self._OnStoreBtnClick},
    -- }
    if Module.ModuleSwitcher:CheckModuleSwitcher(SwitchSystemID.SwitchSyetemActivity) == EFirstLockResult.Disappear then
    --table.remove(navActions, 2);
    end

    local inputMonitor = Facade.UIManager:GetInputMonitor()

    --[[self._hActionOpenChat =
        inputMonitor:AddDisplayActionBinding(
        "OpenChat",
        EInputEvent.IE_Pressed,
        self._OnChatBtnClick,
        self,
        EDisplayInputActionPriority.UI_Stack
    )]]

    --[[self._hActionOpenMail =
        inputMonitor:AddDisplayActionBinding(
        "OpenMail",
        EInputEvent.IE_Pressed,
        self._OnOpenMail,
        self,
        EDisplayInputActionPriority.UI_Stack
    )]]

    -- self._hActionBack =
    --     inputMonitor:AddDisplayActionBinding(
    --     "Back",
    --     EInputEvent.IE_Pressed,
    --     self._ToggleBarSwitchMode,
    --     self,
    --     EDisplayInputActionPriority.UI_Stack
    -- )
end

function IrisWorldEntryMainPanel:_RemoveShortcuts()
    local inputMonitor = Facade.UIManager:GetInputMonitor()

    --[[if self._hActionOpenChat then
        inputMonitor:RemoveDisplayActoinBingingForHandle(self._hActionOpenChat)
        self._hActionOpenChat = nil
    end]]
    --[[if self._hActionOpenMail then
        inputMonitor:RemoveDisplayActoinBingingForHandle(self._hActionOpenMail)
        self._hActionOpenMail = nil
    end]]
    if self._hActionBack then
        inputMonitor:RemoveDisplayActoinBingingForHandle(self._hActionBack)
        self._hActionBack = nil
    end
end

function IrisWorldEntryMainPanel:_OnQuitBtnClick()
    -- @yixiaoguan 实现封装暴露到Module
    if Facade.GameFlowManager:CheckIsInFrontEnd() then
        Module.Login:BackToLogin()
    end
end

function IrisWorldEntryMainPanel:_OnSettingBtnClick()
    Module.SystemSetting:ShowSystemSettingMainView()
end

function IrisWorldEntryMainPanel:_OnCollectionBtnClick()
    Module.Collection:ShowMainPanel()
end

function IrisWorldEntryMainPanel:_OnActivityBtnClick()
    if DFHD_LUA == 0 then
        LogAnalysisTool.SignButtonClicked(ButtonIdEnum.SOLLobbyActivity)
    else
        LogAnalysisTool.SignButtonClicked(10030030)
    end
    Module.Activity:ShowActivityPanel()
end

function IrisWorldEntryMainPanel:_OnDownloadBtnClick()
    --Module.CommonTips:ShowSimpleTip(Module.CommonTips.Config.Loc.CommingSoon)
    Module.LitePackage:ShowMainPanel()
end

function IrisWorldEntryMainPanel:_OnLiveStreamBtnClick()
end

function IrisWorldEntryMainPanel:_OnRecruitBtnClick()
    Module.Recruit:ShowMainPanel()
end

function IrisWorldEntryMainPanel:_OnInviteBtnClick()
    Module.Social:OpenInvitePanel()
end

function IrisWorldEntryMainPanel:_OnMailBtnClick()
    Module.Mail:ShowMainPanel(Module.Mail.Config.ESocialType.Mail)
    if DFHD_LUA == 0 then
        LogAnalysisTool.SignButtonClicked(ButtonIdEnum.SOLLobbyMail)
    else
        LogAnalysisTool.SignButtonClicked(10120005)
    end
end

function IrisWorldEntryMainPanel:_OnFriendBtnClick()
    Module.Mail:ShowFriendPanel()
end

function IrisWorldEntryMainPanel:_OnChatBtnClick()
    Module.Mail:ShowMainPanel(Module.Mail.Config.ESocialType.Chat)
    if DFHD_LUA == 0 then
        LogAnalysisTool.SignButtonClicked(ButtonIdEnum.SOLLobbyChat)
    else
        LogAnalysisTool.SignButtonClicked(10120009)
    end
end

function IrisWorldEntryMainPanel:_OnStoreBtnClick()
    Module.Store:ShowMainPanel()
    if DFHD_LUA == 0 then
        LogAnalysisTool.SignButtonClicked(ButtonIdEnum.SOLLobbyStore)
    else
        LogAnalysisTool.SignButtonClicked(10120005)
    end
end

function IrisWorldEntryMainPanel:_OnPlayerInfoBtnClick()
    Module.RoleInfo:ShowMainPanel()
end

function IrisWorldEntryMainPanel:_OnBPBtnClick()
    if DFHD_LUA == 0 then
        LogAnalysisTool.SignButtonClicked(ButtonIdEnum.SOLLobbyBattlePass)
    else
        LogAnalysisTool.SignButtonClicked(10030028)
    end
    Module.BattlePass:ShowBattleProcess(UIName2ID.BattlePassMain, Module.BattlePass.Field.eBPOriginType.SOL)
end

function IrisWorldEntryMainPanel:_OnMossPanelOnShow()
    self._bHideAnnouncement = true
    if DFHD_LUA == 0 then
        -- self._wtLobbyBottomBar:ShowChatPanel(false)
        self._wtLobbyBottomBar:ShowBtnsPanel(false)
        self._wtLobbyTopBar:HideAnnouncementManuel()
        if self._announcementTimer then
            self._announcementTimer:Release()
            self._announcementTimer = nil
        end
    else
        Server.FrontEndChatServer:SetCanOpenAnouncement(false)
        Server.FrontEndChatServer.Events.evtHideAnnouncement:Invoke()
        Module.Chat:OnStackUIChatStateSwitch(UIName2ID.MossMainPanel, EStackAction.Push)
    end
end

function IrisWorldEntryMainPanel:_OnMossPanelOnHide()
    self._bHideAnnouncement = false
    if DFHD_LUA == 0 then
        -- self._wtLobbyBottomBar:ShowChatPanel(true)
        self._wtLobbyBottomBar:ShowBtnsPanel(true)
    else
        Server.FrontEndChatServer:SetCanOpenAnouncement(true)
        Module.Chat:OnStackUIChatStateSwitch(UIName2ID.MossMainPanel, EStackAction.Pop)
    end
    self:ShowAnnouncement()
end

function IrisWorldEntryMainPanel:_RefreshMailAndFriendRedDot()
    local totalNewMail = Server.MailServer:GetNewMailTotalNum()
    local applyNewFriend = Module.Friend:GetNewFriendApplyNum()
    self:_SetLabelRedDot(applyNewFriend, self._wtFriendReddot, false)
    self:_SetLabelRedDot(totalNewMail, self._wtMailReddot, false)
end

function IrisWorldEntryMainPanel:_OnRefreshModel(curSubStageType)
    if curSubStageType and curSubStageType == ESubStage.MainFlowLobby then
        if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse then
            -- if not Module.LobbyDisplay:GetSwitchingMainFlow() then
            Module.LobbyDisplay:SwitchMainFlow(EMainFlowCtrlType.MainFlowCtrl_SOL)
        -- end
        end
    end
end

function IrisWorldEntryMainPanel:_OnRefreshCharacter()
    Module.LobbyDisplay:SetMainFlowSetCharacterInfo(EMainFlowCtrlType.MainFlowCtrl_SOL, true)
end

--页签红点设置
function IrisWorldEntryMainPanel:_SetLabelRedDot(num, dotWidget, showNum)
    showNum = setdefault(showNum, true)
    dotWidget:Visible()
    if not showNum then
        if num <= 0 then
            dotWidget:Hidden()
            return
        end
        dotWidget:BpSetReddotType(1)
        return
    else
        dotWidget:BpSetReddotType(3)
    end

    if num <= 0 then
        dotWidget:Hidden()
    elseif num >= 99 then
        local numTxt = dotWidget:Wnd("wReddotText", UITextBlock)
        numTxt:SetText("99")
    else
        local numTxt = dotWidget:Wnd("wReddotText", UITextBlock)
        numTxt:SetText(num)
    end
end

--#region 玩家名字显示

-- 创建子UI并添加到CanvasPanel中
function IrisWorldEntryMainPanel:_InitNameModelItems()
    loginfo("IrisWorldEntryMainPanel:_InitNameModelItems")
    self._nameModeItems = {}
    local promiseGroup = {}
    --Facade.UIManager:RemoveSubUIByParent(self,self._wtNameModelPanel)
    for i = 1, 3 do
        local teamModeItem = self:Wnd(string.format("WBP_Hall_TeamModelItem_%s", tostring(i)), UIWidgetBase)
        if teamModeItem and teamModeItem.Collapsed then
            teamModeItem:Collapsed()
            table.insert(self._nameModeItems, teamModeItem)
        end
    end
    self:_RefreshModelItems()
    -- self:_RefreshModelItemsPos()
end

---@type GameplayStatics
local GameplayStatics = import("GameplayStatics")
---@type SlateBlueprintLibrary
local SlateBlueprintLibrary = import("SlateBlueprintLibrary")
local FVector2D = import("Vector2D")

local function WorldSpace2WidgetSpace(worldLoc, parentWidget)
    local playerCtrl = GameplayStatics.GetPlayerController(GetWorld(), 0)
    local screenLoc = Module.LobbyDisplay.ProjectWorldToScreen(playerCtrl, worldLoc, true)
    local uiLoc =
        SlateBlueprintLibrary.ScreenToWidgetLocal(GetWorld(), parentWidget:GetCachedGeometry(), screenLoc, nil)
    return uiLoc
end

-- 刷新显示的名字UI
function IrisWorldEntryMainPanel:_RefreshModelItems()
    loginfo("IrisWorldEntryMainPanel:_RefreshModelItems")
    if self._nameModeItems == nil then
        loginfo("IrisWorldEntryMainPanel:_RefreshModelItems aborted no ModelItems!")
        return
    end

    loginfo("IrisWorldEntryMainPanel:#_nameModeItems", #self._nameModeItems)
    --先隐藏
    for index, teamModeItem in ipairs(self._nameModeItems) do
        teamModeItem:ToggleListeners(false)
        teamModeItem:Collapsed()
    end
    if Module.LobbyDisplay.IsInCustomTeam() then
        local displayCtrl = Module.LobbyDisplay
        local members = Server.TeamServer:GetMembers()
        logwarning("IrisWorldEntryMainPanel:membersNums", table.nums(members))
        for i, member in pairs(members) do
            local index = -1
            --玩家index是1，其他人是2，3，4
            if member.PlayerID == Server.AccountServer:GetPlayerId() then
                index = 1
            else
                index = Server.TeamServer:FindSeat(member.PlayerID)
            end
            local modelItem = self._nameModeItems[index]
            if modelItem ~= nil then
                -- modelItem:Visible()
                modelItem:SetPlayerInfo(member)
                modelItem:SetIsSelf(index == 1)
                modelItem:ToggleListeners(true)
                if index == 1 then
                    local state = displayCtrl:GetMainRoleAnimState()
                    if displayCtrl:IsAnimStatePlayEnd(state) then
                        self:_RefreshModelItemsPosBySlot(1)
                    end
                else
                    local info = displayCtrl:GetTeammateAnimState()
                    if displayCtrl:IsAnimStatePlayEnd(info[member.PlayerID]) then
                        self:_RefreshModelItemsPosBySlot(index)
                    end
                end
            else
                loginfo("IrisWorldEntryMainPanel:_RefreshModelItems item aborted")
            end
        end
    else
        --[[
        local modelItem= self._nameModeItems[1]
        local myInfo = {
            PlayerID = Server.AccountServer:GetPlayerId(),
            PlayerName = Server.RoleInfoServer.nickName or "",
            PicUrl = Server.RoleInfoServer.picUrl,
            Level = Server.RoleInfoServer.accountLevel,
            SeasonLevel = Server.RoleInfoServer.seasonLevel,
        }
        if modelItem ~= nil then
            modelItem:Visible()
            modelItem:SetPlayerInfo(myInfo)
            modelItem:SetIsSelf(true)
        end
        --]]
    end
    self:TryAddDelegate()
end

function IrisWorldEntryMainPanel:_RefreshModelItemsPos()
    if self._startMoveCamera then
        return
    end
    loginfo("IrisWorldEntryMainPanel:_RefreshModelItemsPos")
    if self._nameModeItems == nil then
        loginfo("IrisWorldEntryMainPanel:_RefreshModelItemsPos aborted no ModelItems!")
        return
    end

    loginfo("IrisWorldEntryMainPanel:#_nameModeItems", #self._nameModeItems)
    local displayCtrl = Module.LobbyDisplay
    if displayCtrl.IsInCustomTeam() then
        local members = Server.TeamServer:GetMembers()
        logwarning("IrisWorldEntryMainPanel:membersNums", table.nums(members))
        for i, member in pairs(members) do
            local index = -1
            --玩家index是1，其他人是2，3，4
            if member.PlayerID == Server.AccountServer:GetPlayerId() then
                index = 1
            else
                index = Server.TeamServer:FindSeat(member.PlayerID)
            end
            if index == 1 then
                local state = displayCtrl:GetMainRoleAnimState()
                if displayCtrl:IsAnimStatePlayEnd(state) then
                    self:_RefreshModelItemsPosBySlot(1)
                end
            else
                local info = displayCtrl:GetTeammateAnimState()
                if displayCtrl:IsAnimStatePlayEnd(info[member.PlayerID]) then
                    self:_RefreshModelItemsPosBySlot(index)
                end
            end
        end
    else
        --[[
        local playerLoc = Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MainFlowLobby, "GetCharacterLocationBySlot", EMainFlowCtrlType.MainFlowCtrl_SOL, 1)
        local modelItem= self._nameModeItems[1]
        if modelItem ~= nil and playerLoc then
            local positionOffset=modelPositionOffset[1]
            playerLoc.X=playerLoc.X+positionOffset.X
            playerLoc.Y=playerLoc.Y+positionOffset.Y
            playerLoc.Z=playerLoc.Z+positionOffset.Z
            local loc = WorldSpace2WidgetSpace(playerLoc, self._wtNameModelPanel)
            if loc ~= nil then
                local slot = UWidgetLayoutLibrary.SlotAsCanvasSlot(modelItem)
                if slot then
                    slot:SetPosition(loc)
                end
            end
        end
        --]]
    end
    --self:TryAddDelegate()
end

--[[function IrisWorldEntryMainPanel:_SafeRefreshModelItems()
    if hasdestroy(self) then return end     -- quick fix, timer call过来的时候，有可能已经被forcegc了

    -- 此时可能还没有把子UI加载进去
    trycall(self._RefreshModelItems, self)
end

function IrisWorldEntryMainPanel:_SafeRefreshModelItemsTwice()
    self:_SafeRefreshModelItems()
    -- 将就一下 第一下刷新骨骼可能还没摆好 名字会歪
    Timer.DelayCall(1, self._SafeRefreshModelItems, self)
end]]
function IrisWorldEntryMainPanel:_OnJoinTeam()
    local info = Server.TeamServer:GetMembers()
    for k, v in pairs(info) do
        if v.PlayerID ~= Server.AccountServer:GetPlayerId() then
            Module.LobbyDisplay:RefreshPlayerModelByInfo(EMainFlowCtrlType.MainFlowCtrl_SOL, v)
        end
    end
    self:_RefreshModelItems()
end

function IrisWorldEntryMainPanel:_OnLeaveTeam(myteamid, teamid, isrepeat) --isrepeat，是否是退队之后再入队
    if not isrepeat then
        self:_OnMainRoleLeft()
    end
    self:_RefreshModelItems()
end

function IrisWorldEntryMainPanel:_OnTeammateJoined(playerId)
    local info = Server.TeamServer:GetMemberById(playerId)
    Module.LobbyDisplay:RefreshPlayerModelByInfo(EMainFlowCtrlType.MainFlowCtrl_SOL, info)
    if not IsHD() then
        Module.LobbyDisplay:SetCameraType(EMainFlowCtrlType.MainFlowCtrl_SOL)
    end
    self:_RefreshModelItems()
end

function IrisWorldEntryMainPanel:_OnTeammateLeft() --队员走了只需要清除其模型即可
    Module.LobbyDisplay:ClearPlayerModel(EMainFlowCtrlType.MainFlowCtrl_SOL)
    if not IsHD() then
        Module.LobbyDisplay:SetCameraType(EMainFlowCtrlType.MainFlowCtrl_SOL)
    end
    self:_RefreshModelItems()
end

function IrisWorldEntryMainPanel:_OnCaptialChanged() --就目前逻辑来看的话是不需要刷新模型,刷新下头顶信息
    self:_RefreshModelItems()
end

function IrisWorldEntryMainPanel:_OnTeamMemberChanged()
    self:_OnRefreshCharacter()
    self:_RefreshModelItems()
end

function IrisWorldEntryMainPanel:_OnTeamMemberEquipChanged(playerInfo, addEquips, removeEquips, reModel, reName)
    Module.LobbyDisplay:RefreshTeammateByEquipChange(
        EMainFlowCtrlType.MainFlowCtrl_SOL,
        playerInfo,
        addEquips,
        removeEquips,
        reModel
    )
    if reName or reModel then --改名字了才需要刷新
        self:_RefreshModelItems()
    end
end

function IrisWorldEntryMainPanel:_OnTeamMemberStateChanged()
    self:_RefreshModelItems()
end
--#endregion 玩家名字显示

-- 检查功能模块是否解锁
function IrisWorldEntryMainPanel:_CheckShowModule()
end

function IrisWorldEntryMainPanel:_OnCultureChanged()
    Server.ModuleUnlockServer:ReqGetLobbyBannerData()
end

function IrisWorldEntryMainPanel:_ShowModuleNtf(moduleId)
    Server.ModuleUnlockServer:ReqGetLobbyBannerData()
end

function IrisWorldEntryMainPanel:_OnSeasonLevelUp(seasonLv, preSeasonLv, bActive)
    if seasonLv > preSeasonLv and bActive == true and Server.RoleInfoServer.deltaExp > 0 then
        Server.ModuleUnlockServer:ReqGetLobbyBannerData()
    end
end

function IrisWorldEntryMainPanel:_CheckModuleSwitcher()
    if Module.ModuleSwitcher:CheckModuleSwitcher(SwitchSystemID.SwitchSyetemActivity) == EFirstLockResult.Disappear then
        loginfo("[ModuleDailyTask] SOL CheckModuleSwitcher ActivityModule is close")
        self._wtActivityPanelBox:Collapsed()
    end
end

function IrisWorldEntryMainPanel:TryAddDelegate()
    if ULuautils.IsObjValid(self._cameraMovedDoneDelegate) then
        return -- 已有监听的
    end
    -- 尝试监听镜头变换
    local cameraMovedDoneDelegate = Facade.HallSceneManager:TryGetSceneCameraMovedDone(ESubStage.MainFlowLobby)
    if cameraMovedDoneDelegate ~= nil then
        self._cameraMovedDoneDelegate = cameraMovedDoneDelegate:Add(self._OnEndCameraMove, self)
        logwarning("[darc] 正在监听HallCameraMovedDone")
    end
end

--------------------------------------------------------------------------------
--- Lua events
--------------------------------------------------------------------------------
-- 当换英雄的时候要重新刷新一下名字的位置
function IrisWorldEntryMainPanel:_OnHeroSelectedChanged(_)
    --self:_SafeRefreshModelItemsTwice()
    self:_RefreshModelItems()
end

function IrisWorldEntryMainPanel:_OnStartMatching()
    if DFHD_LUA ~= 1 then
        logerror("[IrisWorldEntryMainPanel] start matching")
        Facade.UIManager:AsyncShowUI(UIName2ID.CommonMatchTime)
    else
        if Module.IrisSafeHouse.Field:GetMatchTipsHD() == nil then
            local function _OnTipsLoaded(uiIns)
                Module.IrisSafeHouse.Field:SetMatchTipsHD(uiIns)
            end
            Facade.UIManager:AsyncShowUI(UIName2ID.SafeHouseTipsHD, _OnTipsLoaded)
        end
    end
end

function IrisWorldEntryMainPanel:_OnPrepareJoinMatch()
    if self._tabHandle then
    -- Facade.UIManager:CloseUIByHandle(self._tabHandle)
    -- self._tabHandle = nil
    end
end

function IrisWorldEntryMainPanel:_OnEndMatching()
end

--------------------------------------------------------------------------------
--- Cpp Delegates
--------------------------------------------------------------------------------
function IrisWorldEntryMainPanel:_OnNotifyResolutionResized()
    --self:_SafeRefreshModelItemsTwice()
    self:_RefreshModelItems()
end

--------------------------------------------------------------------------------
--- Tab点���事件
--------------------------------------------------------------------------------
function IrisWorldEntryMainPanel:_OnMasterTabClicked()
    self._masterTabHandle =
        Module.ModuleSwitcher:ModuleOpenByCheck(SwitchSystemID.SwitchSystemHeroLobby, UIName2ID.HeroMainPanel)
end

function IrisWorldEntryMainPanel:_OnCollectionTabClicked()
    self._collectionTabHandle = Facade.UIManager:AsyncShowUI(UIName2ID.CollectionMainPanel)
end

function IrisWorldEntryMainPanel:_OnEquipTabClicked()
    self._equipTabHandle = Module.ArmedForce:ShowMainMPPanel()
end

function IrisWorldEntryMainPanel:_OnVehicleTabClicked()
    -- 不要直接调用Facade.UIManager:AsyncShowUI(UIName2ID.VehicleInspectorMainView),请调用Module.Vehicle:OpenInspectorMainUI()
    -- self._vehicleTabHandle = Facade.UIManager:AsyncShowUI(UIName2ID.VehicleInspectorMainView)
end

function IrisWorldEntryMainPanel:_OnActivityJumpClicked()
    Module.Activity:ShowActivityPanel()
end

function IrisWorldEntryMainPanel:OnAnimFinished(anim)
    if anim == self.WBP_Hal_SOL_Main_In then
        -- self:_RefreshModelItemsPos()
        local displayCtrl = Module.LobbyDisplay
        local state = displayCtrl:GetMainRoleAnimState()
        if displayCtrl:IsAnimStatePlayEnd(state) then
            self:_RefreshModelItemsPosBySlot(1)
        end
        local info = displayCtrl:GetTeammateAnimState()
        for playerId, state in pairs(info) do
            local slot = Server.TeamServer:FindSeat(playerId)
            if displayCtrl:IsAnimStatePlayEnd(state) then
                self:_RefreshModelItemsPosBySlot(slot)
            end
        end
    end
end

function IrisWorldEntryMainPanel:_OnStopPreShowLoadingView()
    --[[logwarning("IrisWorldEntryMainPanel:_OnStopPreShowLoadingView")
    self:Show()]]
end

function IrisWorldEntryMainPanel:_GetNameModelItemByInstId(instId)
    local weakInst = Facade.UIManager:GetSubUI(self, UIName2ID.BattlefieldEntryTeamModelItem, instId)
    local uiInst = getfromweak(weakInst)
    return uiInst
end

function IrisWorldEntryMainPanel:CheckSandBoxResourceComplete()
    if Module.SandBoxMap:CheckCanOpenSandBox() then
        self.timer:Stop()
        Module.GameMode:JumpToSubMode(Module.IrisSafeHouse.JumpParam)
        Module.IrisSafeHouse.JumpParam = nil
    end
end

function IrisWorldEntryMainPanel:_OnLayerBinaryKeyReset()
    self:PlayWidgetAnim(self.WBP_Hal_SOL_Main_in)
end

function IrisWorldEntryMainPanel:_OnLobbyBannerDataLoaded(lobbyBannerList)
    if not hasdestroy(self) and not self._bIsLoadingBanner then
        self._bIsLoadingBanner = true
        if
            lobbyBannerList and #lobbyBannerList > 0 and not hasdestroy(self._wtBannerBox) and
                not hasdestroy(self._wtBannerPanel)
         then
            self._wtBannerBox:SelfHitTestInvisible()
            self._wtBannerPanel:SetInfo(lobbyBannerList)
        elseif not hasdestroy(self._wtBannerBox) then
            self._wtBannerBox:Collapsed()
        end
        self._bIsLoadingBanner = false
    end
end

function IrisWorldEntryMainPanel:_OnLobbyEntranceDataLoaded(lobbyEntranceList)
    if not hasdestroy(self) then
    -- local bFindBPConfig = false
    -- local bFindActivityConfig = false
    -- if lobbyEntranceList and #lobbyEntranceList > 0 then
    --     for index, config in ipairs(lobbyEntranceList) do
    --         if config.type == 1 and not hasdestroy(self._wtActivityEntrance) then
    --             self._wtActivityEntrance:SelfHitTestInvisible()
    --             self._wtActivityEntrance:SetCDNImage(config.image_url)
    --             bFindBPConfig = true
    --         elseif config.type == 2 and not hasdestroy(self._wtBPEntrance) then
    --             self._wtBPEntrance:SelfHitTestInvisible()
    --             self._wtBPEntrance:SetCDNImage(config.image_url)
    --             bFindActivityConfig = true
    --         end
    --     end
    -- end
    -- if not hasdestroy(self._wtActivityEntrance) then
    --     if not bFindActivityConfig then
    --         self._wtActivityEntrance:SetLocalImage("Texture2D'/Game/UI/UIAtlas/System/Hall/Sp/Hall_Bg_Sp_02.Hall_Bg_Sp_02'")
    --         self._wtActivityEntrance:SelfHitTestInvisible()
    --     end
    -- end
    -- if not hasdestroy(self._wtBPEntrance) then
    --     if not bFindBPConfig then
    --         self._wtBPEntrance:SetLocalImage("Texture2D'/Game/UI/UIAtlas/System/Hall/Sp/Hall_Bg_Sp_01.Hall_Bg_Sp_01'")
    --         self._wtBPEntrance:SelfHitTestInvisible()
    --     end
    -- end
    end
end

function IrisWorldEntryMainPanel:_RecruitEnded()
    if not Module.LobbyDisplay.IsInCustomTeam() then
        self:_HideAllName()
    end
end

function IrisWorldEntryMainPanel:_HideAllName()
    if self._nameModeItems then
        for index, teamModeItem in ipairs(self._nameModeItems) do
            teamModeItem:Collapsed()
        end
    end
end

function IrisWorldEntryMainPanel:_OnStartCameraMove()
    self._startMoveCamera = true
    self:_HideAllName()
end

function IrisWorldEntryMainPanel:_OnEndCameraMove()
    self._startMoveCamera = false
    --目前这个事件只有手机会触发
    self:_RefreshModelItemsPos()
end

function IrisWorldEntryMainPanel:_OnFacePopClosed()
    Module.LobbyDisplay:OnFacePopClosed()
end

function IrisWorldEntryMainPanel:_OnFacePopJumped()
    Module.LobbyDisplay:OnFacePopJumped()
end

function IrisWorldEntryMainPanel:_OnMainRoleLeft()
    Module.LobbyDisplay:OnMainRoleLeft()
    Module.LobbyDisplay:ClearPlayerModel(EMainFlowCtrlType.MainFlowCtrl_SOL)
    if not IsHD() then
        Module.LobbyDisplay:SetCameraType(EMainFlowCtrlType.MainFlowCtrl_SOL)
    end
    self:_RefreshModelItems()
end

function IrisWorldEntryMainPanel:_OnCharacterAnimPlayEnd(slot)
    self:_RefreshModelItemsPosBySlot(slot)
end

function IrisWorldEntryMainPanel:_RefreshModelItemsPosBySlot(index)
    if self._startMoveCamera then
        return
    end
    loginfo("IrisWorldEntryMainPanel:_RefreshModelItemsPos")
    if self._nameModeItems == nil then
        loginfo("IrisWorldEntryMainPanel:_RefreshModelItemsPos aborted no ModelItems!")
        return
    end

    loginfo("IrisWorldEntryMainPanel:#_nameModeItems", #self._nameModeItems)
    if Module.LobbyDisplay.IsInCustomTeam() then
        local playerLoc =
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(
            ESubStage.MainFlowLobby,
            "GetCharacterLocationBySlot",
            EMainFlowCtrlType.MainFlowCtrl_SOL,
            index
        )
        local modelItem = self._nameModeItems[index]
        if modelItem ~= nil and playerLoc then
            modelItem:Visible()
            loginfo(
                "IrisWorldEntryMainPanel:_RefreshModelItemsPos",
                "index",
                index,
                "player loc:",
                playerLoc.X,
                playerLoc.Y,
                playerLoc.Z
            )
            --加偏移量之前位置
            local positionOffset = self.modelPositionOffset[index]
            playerLoc.X = playerLoc.X + positionOffset.X
            playerLoc.Y = playerLoc.Y + positionOffset.Y
            playerLoc.Z = playerLoc.Z + positionOffset.Z
            loginfo(
                "IrisWorldEntryMainPanel:_RefreshModelItemsPos",
                "index",
                index,
                "fixedLoc:",
                playerLoc.X,
                playerLoc.Y,
                playerLoc.Z
            )
            --之后位置
            local loc = WorldSpace2WidgetSpace(playerLoc, self._wtNameModelPanel)
            if loc ~= nil then
                local slot = UWidgetLayoutLibrary.SlotAsCanvasSlot(modelItem)
                loginfo("IrisWorldEntryMainPanel:_RefreshModelItemsPos", "index", index, "loc: ", loc.X, loc.Y)
                if slot then
                    slot:SetPosition(loc)
                end
            end
        else
            loginfo("IrisWorldEntryMainPanel:_RefreshModelItemsPos item aborted")
        end
    end
end

function IrisWorldEntryMainPanel:OnFoldStatusChanged()
    loginfo("IrisWorldEntryMainPanel:OnFoldStatusChanged")
    Timer.DelayCall(
        0.5,
        function()
            Module.LobbyDisplay:SetCameraType(EMainFlowCtrlType.MainFlowCtrl_SOL)
            self:_RefreshModelItems()
        end
    )
end

function IrisWorldEntryMainPanel:OnGameFronted()
    self:_RefreshModelItems()
end

function IrisWorldEntryMainPanel:_OnReconnected()
    self:_OnRefreshCharacter()
    loginfo("IrisWorldEntryMainPanel:_OnReconnected")
    local curGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    -- 新大厅
    if curGameFlow == EGameFlowStageType.SafeHouse then
        local curSubStage = Facade.GameFlowManager:GetCurrentSubStage()
        if curSubStage == ESubStage.MainFlowLobby then
            self:_OnRefreshCharacter()
        end
    end
end

function IrisWorldEntryMainPanel:OnAnimSeqLoaded(SeqName)
    loginfo("IrisWorldEntryMainPanel:OnAnimSeqLoaded " .. SeqName)
    Module.LobbyDisplay:PlayMainFlowSeqByName(EMainFlowCtrlType.MainFlowCtrl_SOL, SeqName)
end

-- BEGIN MODIFICATION @ VIRTUOS :
function IrisWorldEntryMainPanel:_EnableGamepadFeature()
    if not IsHD() then
        return
    end

    if not self._OnNotifyInputTypeChangedHandle then
        -- 手柄快捷键，点击正在显示的轮播图
        if not self._ClickTheCarousel then
            self._ClickTheCarousel =
                self:AddInputActionBinding(
                "ClickTheCarousel_Gamepad",
                EInputEvent.IE_Pressed,
                self._ShortcutEventOnCarouselClicked,
                self,
                EDisplayInputActionPriority.UI_Stack
            )
        end

        if not self._OnPreviousPageOfCarouselHandle then
            self._OnPreviousPageOfCarouselHandle =
                self:AddInputActionBinding(
                "Common_RightStickLeft_Gamepad",
                EInputEvent.IE_Pressed,
                self._OnSwitchPreviousPageOfCarousel,
                self,
                EDisplayInputActionPriority.UI_Stack
            )
        end

        if not self._OnNextPageOfCarouselHandle then
            self._OnNextPageOfCarouselHandle =
                self:AddInputActionBinding(
                "Common_RightStickRight_Gamepad",
                EInputEvent.IE_Pressed,
                self._OnSwitchNextPageOfCarousel,
                self,
                EDisplayInputActionPriority.UI_Stack
            )
        end

        self._OnNotifyInputTypeChangedHandle =
            UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(
            CreateCPlusCallBack(self._HandleInputTypeChanged, self)
        )
    end

    self:_HandleInputTypeChanged(WidgetUtil.GetCurrentInputType())

    if self._wtPrepareRegion then
        -- 打开配装
        if not self._OpenGearSetup then
            self._OpenGearSetup =
                self:AddInputActionBinding(
                "OpenGearSetup_Gamepad",
                EInputEvent.IE_Pressed,
                self._wtPrepareRegion._OnLoadbtnCicked,
                self._wtPrepareRegion,
                EDisplayInputActionPriority.UI_Stack
            )
            self._wtPrepareRegion:SetLongPressForKeyIcon("OpenGearSetup_Gamepad")
        end

        local summaryList = {}

        table.insert(
            summaryList,
            {
                actionName = "HallChatEnter",
                func = self._OnChatBtnClick,
                caller = self,
                bUIOnly = false,
                bHideIcon = false
            }
        )

        table.insert(
            summaryList,
            {
                actionName = "Enter3DSafeHouse_Gamepad",
                func = self._wtPrepareRegion._OnEnter3DSafeHouseClick,
                caller = self._wtPrepareRegion,
                bUIOnly = false,
                bHideIcon = false
            }
        )

        if self._wtPrepareRegion:IsShowRank() then
            -- 开关晋升之路
            table.insert(
                summaryList,
                {
                    actionName = "MainMenu_OpenRanking",
                    func = self._wtPrepareRegion._OnRankCheckBoxSelfClick,
                    caller = self._wtPrepareRegion,
                    bUIOnly = false,
                    bHideIcon = false
                }
            )
        end

        if self._wtPrepareRegion:IsShowAutoMatch() then
            -- 补齐队友
            table.insert(
                summaryList,
                {
                    actionName = "ComplementaryTeammates_Gamepad",
                    func = self._wtPrepareRegion._OnAutoMatchCheckBoxSelfClick,
                    caller = self._wtPrepareRegion,
                    bUIOnly = false,
                    bHideIcon = false
                }
            )
        end
        
        if self._wtPrepareRegion:IsShowSkin() then
            -- 显示装备
            table.insert(
                summaryList,
                {
                    actionName = "DisplayAppearance_Gamepad",
                    func = self._wtPrepareRegion._OnSkinCheckBoxSelfClick,
                    caller = self._wtPrepareRegion,
                    bUIOnly = false,
                    bHideIcon = false
                }
            )
        end
       
        Module.CommonBar:SetBottomBarInputSummaryList(summaryList, false)
    end
end

function IrisWorldEntryMainPanel:_DisableGamepadFeature()
    if not IsHD() then
        return
    end

    if self._ClickTheCarousel then
        self:RemoveInputActionBinding(self._ClickTheCarousel)
        self._ClickTheCarousel = nil
    end

    if self._OnNotifyInputTypeChangedHandle then
        UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._OnNotifyInputTypeChangedHandle)
        self._OnNotifyInputTypeChangedHandle = nil
    end

    if self._OpenGearSetup then
        self:RemoveInputActionBinding(self._OpenGearSetup)
        self._OpenGearSetup = nil
        if self._wtPrepareRegion then
            self._wtPrepareRegion:RemoveLongPressForKeyIcon()
        end
    end

    if self._OnPreviousPageOfCarouselHandle then
        self:RemoveInputActionBinding(self._OnPreviousPageOfCarouselHandle)
        self._OnPreviousPageOfCarouselHandle = nil
    end

    if self._OnNextPageOfCarouselHandle then
        self:RemoveInputActionBinding(self._OnNextPageOfCarouselHandle)
        self._OnNextPageOfCarouselHandle = nil
    end

    Module.CommonBar:RecoverBottomBarInputSummaryList()
end

function IrisWorldEntryMainPanel:_ShowSideBar()
    if not IsHD() then
        return
    end

    -- if self._wtSideBar then
    --     self._wtSideBar:SetSideBar(self._MainTabInputSummaryV2_SideBar)
    --     self._wtSideBar:Show()
    -- end

    Module.CommonBar:SetSideBar(self._MainTabInputSummaryV2_SideBar)
    Module.CommonBar:SetIsSideBarVisible(true)
end

function IrisWorldEntryMainPanel:_HideSideBar()
    if not IsHD() then
        return
    end

    -- if self._wtSideBar then
    --     self._wtSideBar:Hide()
    -- end
    Module.CommonBar:SetIsSideBarVisible(false)
end

function IrisWorldEntryMainPanel:_ShortcutEventOnCarouselClicked()
    if not IsHD() then
        return
    end

    if self._wtBannerPanel and self._wtBannerPanel.SimulateClickPageWithGamepad then
        self._wtBannerPanel:SimulateClickPageWithGamepad()
    end
end

function IrisWorldEntryMainPanel:_HandleInputTypeChanged(inputType)
    if not IsHD() then
        return
    end

    if inputType == EGPInputType.Gamepad then
        self:_ShowSideBar()
        if self:IsInShowBeginState() then
            Module.CommonBar:RegStackUIInputSummaryV2(self, {})
        else
            Module.CommonBar:SetBottomBarInputSummaryListV2({})
        end
    else
        self:_HideSideBar()
        if self:IsInShowBeginState() then
            Module.CommonBar:RegStackUIInputSummaryV2(self, self._MainTabInputSummaryV2)
        else
            Module.CommonBar:SetBottomBarInputSummaryListV2(self._MainTabInputSummaryV2)
        end
    end
end

function IrisWorldEntryMainPanel:_InitMainTabInputSummaryV2(mainTabInputSummariesList, bIsSideBar)
    if not IsHD() then
        return
    end
    if not bIsSideBar then
        table.insert(
            mainTabInputSummariesList,
            {
                actionName = (bIsSideBar and {"SideBar_OpenChat_Gamepad"} or {"OpenChat"})[1],
                func = function()
                    if Facade.UIManager:CheckAnyBlockUIIsLoading() then
                        return
                    end
                    if DFHD_LUA == 0 then
                        LogAnalysisTool.SignButtonClicked(ButtonIdEnum.SOLLobbyChat)
                    else
                        LogAnalysisTool.SignButtonClicked(10030027)
                    end

                    Module.Mail:ShowMainPanel(Module.Mail.Config.ESocialType.Chat)
                end,
                iconID = "IconChat",
                reddot = {
                    {
                        obType = EReddotTrieObserverType.Chat,
                        key = "ChatUnreadMessage",
                        reddotStyle = {reddotType = EReddotType.Normal}
                    }
                }
            }
        )
    end

    table.insert(
        mainTabInputSummariesList,
        {
            actionName = "OpenCollection",
            func = function()
                if Facade.UIManager:CheckAnyBlockUIIsLoading() then
                    return
                end
                if DFHD_LUA == 0 then
                    LogAnalysisTool.SignButtonClicked(ButtonIdEnum.SOLLobbyCollection)
                else
                    LogAnalysisTool.SignButtonClicked(10030008)
                end
                Module.Collection:ShowMainPanel()
            end,
            iconID = "IconCollection",
            reddot = {
                {
                    obType = EReddotTrieObserverType.Collection,
                    key = "CollectionUnlock",
                    reddotStyle = {reddotType = EReddotType.Normal}
                }
            },
            lockData = {
                firstLockID = SwitchSystemID.SwitchSystemCollection
            }
        }
    )

    table.insert(
        mainTabInputSummariesList,
        {
            actionName = "OpenMarket",
            func = function()
                if Facade.UIManager:CheckAnyBlockUIIsLoading() then
                    return
                end
                if DFHD_LUA == 0 then
                    LogAnalysisTool.SignButtonClicked(ButtonIdEnum.SOLLobbyMarket)
                else
                    LogAnalysisTool.SignButtonClicked(10030022)
                end
                Module.Market:ShowMainPanel()
            end,
            iconID = "IconMarket",
            lockData = {
                firstLockID = SwitchSystemID.SwitchSystemMarket,
                secondLockID = SwitchModuleID.ModuleMarket
            }
            -- reddot = {
            --     {
            --         obType = EReddotTrieObserverType.Market,
            --         key = "",
            --         reddotStyle = {reddotType = EReddotType.Normal}
            --     }
            -- }
        }
    )

    table.insert(
        mainTabInputSummariesList,
        {
            actionName = "OpenStore",
            func = function()
                if Facade.UIManager:CheckAnyBlockUIIsLoading() then
                    return
                end
                if DFHD_LUA == 0 then
                    LogAnalysisTool.SignButtonClicked(ButtonIdEnum.SOLLobbyStore)
                else
                    LogAnalysisTool.SignButtonClicked(10030029)
                end
                Module.Store:ShowMainPanel()
            end,
            iconID = "IconStore",
            -- reddot = {
            --     {
            --         obType = EReddotTrieObserverType.Shop,
            --         key = "",
            --         reddotStyle = {reddotType = EReddotType.Normal}
            --     }
            -- },
            lockData = {
                firstLockID = SwitchSystemID.SwitchSyetemStore,
                secondLockID = SwitchModuleID.ModuleShop
            },
            reddot = {
                {
                    obType = EReddotTrieObserverType.Store,
                    key = "",
                    reddotStyle = {reddotType = EReddotType.Normal}
                }
            }
        }
    )

    table.insert(
        mainTabInputSummariesList,
        {
            actionName = "OpenBattlePass",
            func = function()
                if Facade.UIManager:CheckAnyBlockUIIsLoading() then
                    return
                end
                if DFHD_LUA == 0 then
                    LogAnalysisTool.SignButtonClicked(ButtonIdEnum.SOLLobbyBattlePass)
                else
                    LogAnalysisTool.SignButtonClicked(10030028)
                end

                Module.BattlePass:ShowBattleProcess(UIName2ID.BattlePassMain, Module.BattlePass.Field.eBPOriginType.SOL)
            end,
            iconID = "IconBattlePass",
            lockData = {
                firstLockID = SwitchSystemID.SwitchSyetemBattlePass,
                secondLockID = SwitchModuleID.ModuleBattlePass
            },
            reddot = {
                {
                    obType = EReddotTrieObserverType.BattlePass,
                    key = "",
                    reddotStyle = {reddotType = EReddotType.Normal}
                }
            }
        }
    )

    table.insert(
        mainTabInputSummariesList,
        {
            actionName = "OpenActivity",
            func = function()
                if Facade.UIManager:CheckAnyBlockUIIsLoading() then
                    return
                end
                if DFHD_LUA == 0 then
                    LogAnalysisTool.SignButtonClicked(ButtonIdEnum.SOLLobbyActivity)
                else
                    LogAnalysisTool.SignButtonClicked(10030030)
                end
                Module.Activity:ShowActivityPanel()
            end,
            iconID = "IconActivity",
            reddot = {
                {
                    obType = EReddotTrieObserverType.Activity,
                    key = Module.Activity.Field:IsFilterByModeOn() and
                        ActivityRedDotLogic.KeyForMode(EArmedForceMode.SOL) or
                        ActivityRedDotLogic.KeyForRoot(),
                    reddotStyle = {reddotType = EReddotType.Normal}
                }
            },
            lockData = {
                firstLockID = SwitchSystemID.SwitchSyetemActivity
            }
        }
    )
    if bIsSideBar then
        table.insert(
            mainTabInputSummariesList,
            {
                actionName = "MainMenu_OpenRankingList",
                func = function()
                    --- TODO 暂无经分
                    Module.RankingList:ShowRankListMainPanel()
                end,
                iconID = "IconRanking",
                reddot = {
                    {
                        obType = EReddotTrieObserverType.RankingList,
                        key = "",
                        reddotStyle = {reddotType = EReddotType.Normal}
                    }
                },
                lockData = {
                    firstLockID = SwitchSystemID.SwitchSystemRankingList
                }
            }
        )

        -- if (not IsBuildRegionCN()) or IsInEditor() then
        table.insert(
            mainTabInputSummariesList,
            {
                actionName = "MainMenu_OpenRecruit",
                func = function()
                    if rawget(Server, "ArmedForceServer") then
                        if Server.ArmedForceServer:GetCurArmedForceMode() == EArmedForceMode.MP then
                            LogAnalysisTool.SignButtonClicked(10120031)
                        else
                            LogAnalysisTool.SignButtonClicked(10030031)
                        end
                    end
                    Module.Recruit:ShowMainPanel(3)
                end,
                iconID = "IconRecruit"
            }
        )
    -- end
    end
   
        table.insert(
            mainTabInputSummariesList,
            {
                actionName = "OpenRoomSelectPanel",
                func = function()
                    Module.Room:ShowSelectRoomPanel()
                end,
                iconID = "IconRoom",
                displayData = {
                    fcontrolFunc = function()
                        local locSkipGuideStr = UGameVersionUtils.GetLauncherParamsByKey("customRoom")
                        if locSkipGuideStr == "yes" then --使用启动器, 默认打开自建房入口
                            return true
                        end
                        local gameFlowType = Facade.GameFlowManager:GetCurrentGameFlow()
                        local switchId =
                            gameFlowType == EGameFlowStageType.SafeHouse and SwitchSystemID.SwitchSystemSOLRoom or
                            SwitchSystemID.SwitchSystemMPRoom
                        return Module.ModuleSwitcher:CheckModuleSwitcher(switchId) ~= EFirstLockResult.Disappear
                    end
                }
            }
            -- {
            --     actionName = "OpenTeachingArchives",
            --     func = function()  end,
            --     iconID = "IconTeachingArchives",
            -- },
        )
    if not bIsSideBar then
        -- if (not IsBuildRegionCN()) or IsInEditor() then
        table.insert(
            mainTabInputSummariesList,
            {
                actionName = "LiveRadio",
                func = function()
                    if Facade.UIManager:CheckAnyBlockUIIsLoading() then
                        return
                    end
                    --- TODO 暂无经分
                    Module.LiveRadio:ShowMainPanel()
                end,
                iconID = "IconLiveRadio",
                displayData = {
                    fcontrolFunc = function()
                        return Module.LiveRadio:GetIsShowEntrance()
                    end,
                    updateEvt = Module.LiveRadio.Config.evtLiveRadioRefresh
                },
                -- displayData已经接管，不需要lockData
                -- lockData = {
                --     firstLockID = SwitchSystemID.SwitchSystemLiveRadio,
                -- },
                reddot = {
                    {
                        obType = EReddotTrieObserverType.LiveRadio,
                        key = "LiveRadioEntrance",
                        reddotStyle = {reddotType = EReddotType.Normal}
                    }
                }
            }
        )
    end
end

function IrisWorldEntryMainPanel:_OnSwitchPreviousPageOfCarousel()
    if not IsHD() then
        return
    end
    self._wtBannerPanel._wtCarouseView:PreviousPage()
end
function IrisWorldEntryMainPanel:_OnSwitchNextPageOfCarousel()
    if not IsHD() then
        return
    end
    self._wtBannerPanel._wtCarouseView:NextPage()
end
-- END MODIFICATION
return IrisWorldEntryMainPanel
