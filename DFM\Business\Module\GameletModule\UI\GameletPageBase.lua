----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGamelet)
----- LOG FUNCTION AUTO GENERATE END -----------



local GameletBase = require "DFM.Business.Module.GameletModule.UI.GameletBase"
local GameletPageBase = ui("GameletPageBase", GameletBase)
local UGamelet = import "Gamelet"

function GameletPageBase:Ctor()
    GameletBase.Ctor(self)
    
    self.page = nil
    self.appInfo = nil
    self.widget = nil
end

function GameletPageBase:OnOpen()
    GameletBase.OnOpen(self)
end

function GameletPageBase:OnClose()
    local gamelet = UGamelet.Get()
    if gamelet then
        gamelet:CloseAppPage(self.appId, self.page)
    end
    
    GameletBase.OnClose(self)
end

function GameletPageBase:OpenPage(widget,appInfo)
    if self.appId ~= appInfo.belongToApp then
        return
    end
    
    self._wtAppPageRoot:AddChild(widget)
    self:SetWidgetOffset(widget, appInfo, 1)
end

function GameletPageBase:HidePage(widget,appInfo)
    if self.appId ~= appInfo.belongToApp then
        return
    end
    
    if appInfo.appPage == self.page then
        self._wtAppPageRoot:RemoveChild(widget)
        Facade.UIManager:CloseUI(self)
    end
end

function GameletPageBase:OpenApp()
    GameletBase.OpenApp(self)

    self:OpenPage(self.widget, self.appInfo)
end

function GameletPageBase:CloseApp()
    self.openArgs = nil
    self.tSubUI = {}

    GameletBase.CloseApp(self)
end

function GameletPageBase:InitExtraDataInternal(args)
    GameletBase.InitExtraDataInternal(self, args)
    
    local appInfo = args.tAppInfo
    self.page = appInfo.appPage
    self.appInfo = appInfo
    self.widget = args.tWidget
end

return GameletPageBase
