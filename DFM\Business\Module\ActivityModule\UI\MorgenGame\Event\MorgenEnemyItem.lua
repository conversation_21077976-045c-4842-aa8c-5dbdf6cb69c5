---------- LOG FUNCTION AUTO GENERATE -------------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
-------- LOG FUNCTION AUTO GENERATE END -----------

---对应蓝图:WBP_Example
---@class MorgenEnemyItem : LuaUIBaseView
local MorgenEnemyItem = ui("MorgenEnemyItem")
local ActivityConfig = Module.Activity.Config
local ActivityLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"

function MorgenEnemyItem:Ctor()
	self._wtImage = self:Wnd("DFImage_71", UIImage)
	self._wtName = self:Wnd("DFTextBlock_30", UITextBlock)
	self._wtATK = self:Wnd("DFTextBlock_70", UITextBlock)
	self._wtDEF = self:Wnd("DFTextBlock_5", UITextBlock)
	self._wtHIT = self:Wnd("DFTextBlock_3", UITextBlock)
	self._wtCRT = self:Wnd("DFTextBlock_1", UITextBlock)
	self._wtATKnum = self:Wnd("DFTextBlock", UITextBlock)
	self._wtDEFnum = self:Wnd("DFTextBlock_6", UITextBlock)
	self._wtHITnum = self:Wnd("DFTextBlock_4", UITextBlock)
	self._wtCRTnum = self:Wnd("DFTextBlock_2", UITextBlock)
end

function MorgenEnemyItem:OnOpen()
	self._wtATK:SetText(ActivityConfig.Loc.MogenEnemyText[1])
	self._wtDEF:SetText(ActivityConfig.Loc.MogenEnemyText[2])
	self._wtHIT:SetText(ActivityConfig.Loc.MogenEnemyText[3])
	self._wtCRT:SetText(ActivityConfig.Loc.MogenEnemyText[4])
end

function MorgenEnemyItem:RefreshInfo(enemyinfo)
	-- self._wtImage:AsyncSetImagePath(enemyinfo.avatarIsset)
	self._wtName:SetText(ActivityLogic.HandleLocalizeText(enemyinfo.name))
	self._wtATKnum:SetText(enemyinfo.attack)
	self._wtDEFnum:SetText(enemyinfo.defense)
	self._wtHITnum:SetText(enemyinfo.health)
	self._wtCRTnum:SetText(enemyinfo.hitProb)
end

function MorgenEnemyItem:RefreshUI()
end

return MorgenEnemyItem