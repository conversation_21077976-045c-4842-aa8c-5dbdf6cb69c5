----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMItemDetail)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

---@class ItemDetailView : LuaUIBaseView
local ItemDetailView = ui("ItemDetailView")
local ItemDetailLogic = require "DFM.Business.Module.ItemDetailModule.ItemDetailLogic"
-- local ItemLabelMarkBtn = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.ItemLabelMarkBtn"
local ItemDetailConfig = require "DFM.Business.Module.ItemDetailModule.ItemDetailConfig"
local ItemConfigTool   = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
-- local UModularWeaponDesc = import "ModularWeaponDesc"
-- local FWeaponDataAttribute = import "WeaponDataAttribute"
-- local WeaponHelperTool = require"DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local ShopHelperTool = require "DFM.StandaloneLua.BusinessTool.ShopHelperTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local VehicleHelperTool = require "DFM.StandaloneLua.BusinessTool.VehicleHelperTool"
local UItemIDUtil = import "ItemIDUtil"
local ThemeHelperTool = require "DFM.StandaloneLua.BusinessTool.ThemeHelperTool"
local UGPUserInterfaceUtil = import("GPUserInterfaceUtil")

local logw = function(...)
    logwarning('[ ItemDetailView 嵌入式 ] ', ...)
end

local loge = function(...)
    logerror('[ ItemDetailView 嵌入式 ] ', ...)
end

function ItemDetailView:Ctor()
	self:InjectLua()
	self._wtItemDetailTitle = self:Wnd("wItemDetailTitle", UIWidgetBase)
	self._wtScrollBoxContent = self:Wnd("wScrollBoxContent", UIScrollBox)
	self._wtRealContentSlot = self:Wnd("_wRealContentSlot", UIWidgetBase)
	self._wtCurrencyRichText = self:Wnd("wCurrencyText", UITextBlock)
	-- self._wtItemSpaceGrid = self:Wnd("wItemSpaceGrid", UIWidgetBase)
	-- self._wtWeightText = self:Wnd("wWeightText", UITextBlock)
	-- self._wtSourcePanel = self:Wnd("DFHorizontalBox_0", UIWidgetBase)
	self._wtPanelPrice = self:Wnd("Price",UIWidgetBase)

	self._wtBluePrintText = self:Wnd("DFTextBlock_641",UITextBlock)
	self._wtTitleLine = self:Wnd("DFImage", UIWidgetBase)
	self._wtTitleLine:SetVisibility(ESlateVisibility.Collapsed)
	self._wtLine = self:Wnd("DFImage_141", UIWidgetBase)
	self._wtLine2 = self:Wnd("line_1", UIWidgetBase)
	self._wtDesc = self:Wnd("wDesc", UITextBlock)
	self._wtDesc:SetVisibility(ESlateVisibility.Collapsed)
	-- 改枪台方案
	self._wtDFNamedSlot_139 = self:Wnd("DFNamedSlot_139", UIWidgetBase)
	-- self._wSourceBtnItem = self:Wnd("WBP_ItemDetailContent_CheckBox", UIWidgetBase)
	-- self._wSourceBtn = self:Wnd("wGiftRandomDetailBtn", DFCommonCheckButtonOnly)
	-- self._wSourceBtn:Event("OnCheckedBoxStateChangedNative", self._SourceBtnClicked, self)
	-- self._wUsePlaceBtnItem = self:Wnd("WBP_ItemDetailContent_CheckBox_1", UIWidgetBase)
	-- self._wUsePlaceBtn = self:Wnd("wGiftRandomDetailBtn_1", DFCommonCheckButtonOnly)
	-- self._wUsePlaceBtn:Event("OnCheckedBoxStateChangedNative", self._UsePlaceBtnClicked, self)
	-- self._wtLabelMarkItem = self:Wnd("WBP_ItemDetailContent_CheckBox_2", UIWidgetBase)
	-- self._wtLabelMarkBtn = self:Wnd("WBP_DFCommonCheckcollection", ItemLabelMarkBtn)

	-- 联动标识
	self._wtThemePanel = self:Wnd("Crossover", UIWidgetBase)
	self._wtThemeParent = self._wtThemePanel:Wnd("WBP_ItemDetailContent_CrossoverTag", UIWidgetBase)
	if self._wtThemeParent then
		self._wtThemeIcon = self._wtThemeParent:Wnd("DFImage_18", UIImage)
		self._wtThemeTxt = self._wtThemeParent:Wnd("DFTextBlock_29", TextBlock)
	end

	-- MP模式手柄聚焦用(此UI无焦点时无法滚动垂直框)
	self._wtMPFocus = self:Wnd("DFButton_238", UIWidgetBase)

	self._itemStruct = nil
	self._bInMp = false
	self._parent = nil
	self._bigTipsAlignWidget = nil	 --	带关闭按钮的tips位置停靠的widget，一般是弹窗详情页的标题
	self._tradeMode = ETradeMode.Sell
	self._collectionBtnClickedCallback = nil

	-- 异步加载内容
	self._curSubContentWeakUiIns = nil
	self._asyncCreateUIInst = 0
	self._funcAfterContentFinish = {}
	self._bSourceVisible = true
	self._bUsePlaceVisible = true
	self._bShowLabelMark = true
	self._asyncCreateUIBatchId = {}
end

function ItemDetailView:Destroy()
end

------------------------------------ Override function ------------------------------------
function ItemDetailView:OnInitExtraData()
end

function ItemDetailView:OnOpen()
end

function ItemDetailView:OnClose()
	self._currContentUINavID = nil
	self._curSubContentWeakUiIns = nil
	self:_ClearAsyncSubUI()
	self._GunsmithWeakUI = nil
	Facade.UIManager:ClearAllSubUI(self)
end

function ItemDetailView:OnShow()
	self:_AddEventListener()
end

function ItemDetailView:OnHideBegin()
	--BEGIN MODIFICATION @ VIRTUOS : 隐藏时移除导航
	if IsHD() then
		self:EnableSubUINavigation(false) 
	end
	--END MODIFICATION
end

function ItemDetailView:OnHide()
	self:_RemoveEventListener()
end

------------------------------------ Event listener function ------------------------------------
function ItemDetailView:_OnItemDetailViewHide()
	self:Collapsed()
end

function ItemDetailView:_OnItemMove(itemMoveCmd)
	local item = itemMoveCmd.item
	if self._itemStruct and itemMoveCmd.Reason == PropChangeType.Modify and item and item.gid == self._itemStruct.gid then
		self._itemStruct = item
	end
end

------------------------------------ Private function ------------------------------------
function ItemDetailView:_AddEventListener()
	self:AddLuaEvent(Module.ItemDetail.Config.evtItemDetailViewHide, self._OnItemDetailViewHide, self)
	self:AddLuaEvent(Server.InventoryServer.Events.evtItemMove, self._OnItemMove, self)
end

function ItemDetailView:_RemoveEventListener()
	self:RemoveAllLuaEvent()
end

-------------------------------------------------------------

-- 更新详情页物品标题
function ItemDetailView:_UpdateDetailViewTitle()
	self._wtItemDetailTitle:SetItemTitle(self._itemStruct)
	self._wtItemDetailTitle:SetCloseBtnVisible(false)
end

function ItemDetailView:_UpdateWeaponBluePrint()
	self._wtLine:SetVisibility(ESlateVisibility.Collapsed)
	local weaponFeature = self._itemStruct and self._itemStruct:GetFeature(EFeatureType.Weapon) or nil
	if weaponFeature then
		self._wtBluePrintText:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		local isWeaponBP = weaponFeature:IsEquipWeaponBP()
		local skinId = weaponFeature:GetSkinId()

		local qualityColor
		-- 是否装备皮肤
		if skinId ~= 0 then
			local skinquality = ItemHelperTool.GetQualityTypeById(skinId)
			qualityColor = ItemConfigTool.GetItemQualityLinearColor(skinquality)
			self._wtBluePrintText:SetText(self:_GetSkinName(skinId))
			self._wtLine:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		else
			qualityColor = ItemConfigTool.GetItemQualityLinearColor(1)
			-- 是否拥有皮肤
			local bIsHaveBlueprint, skinDatas = Server.CollectionServer:IsHaveBlueprint(self._itemStruct.id)
			if bIsHaveBlueprint then
				self._wtBluePrintText:SetText(StringUtil.PluralTextFormat(ItemDetailConfig.Loc.notEquipBluePrint, {["NumOfBP"]=#skinDatas})) -- 单复数改造[aidenliao]
				self._wtLine:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
			else
				self._wtBluePrintText:SetVisibility(ESlateVisibility.Collapsed)
			end
		end

		-- if qualityColor then
		-- 	self._wtBluePrintText:SetColorAndOpacity(qualityColor)
		-- end

		-- 如果为藏品则隐藏
		if ItemHelperTool.GetMainTypeById(self._itemStruct.id) == EItemType.WeaponSkin then
			self._wtBluePrintText:SetVisibility(ESlateVisibility.Collapsed)
			self._wtLine:SetVisibility(ESlateVisibility.Collapsed)
		end
	else
		self._wtBluePrintText:SetVisibility(ESlateVisibility.Collapsed)
	end
end

function ItemDetailView:_GetSkinName(skinId)
    local skinName = ItemConfigTool.GetItemName(skinId)
    if skinName then
        local skinNameArray = string.split(skinName,"-")
		if skinNameArray and next(skinNameArray) then
			return skinNameArray[#skinNameArray]
		end
    end
	return ""
end

function ItemDetailView:_UpdateDetailContent()
	if self._itemStruct == nil then
		loge("ItemDetailView _UpdateDetailContent : itemInfo is nil ")
		return
	end

	local itemFeature = self._itemStruct:GetFeature()
	local featureType = itemFeature:GetFeatureType()

	-- 战场道具
	if ItemHelperTool.IsArmedForceUniquePropByItem(self._itemStruct) then
		self:_SetArmedForceItemDetail()
	elseif featureType == EFeatureType.Default and itemFeature:IsCollecionItem() then
        self:_SetCollectionDetail()
	-- Weapon 0
	elseif featureType == EFeatureType.Weapon then
		-- 特殊处理近战武器 匕首
		if ItemHelperTool.GetMainTypeById(self._itemStruct.id) == EItemType.WeaponSkin then
			self:_SetWeaponCollectionDetail()
		elseif itemFeature:IsMeleeWeapon() then
			self:_SetMeleeWeaponDetail()
		else
			self:_SetWeaponDetail()
		end
	elseif featureType == EFeatureType.Bullet then
		self:_SetBulletDetail()
	elseif featureType == EFeatureType.Equipment then
	-- Container 3
		if itemFeature:IsContainerItem() or itemFeature:IsExtendItem() then
			local isExtendItem = itemFeature:IsExtendItem()
			self:_SetContainerDetail(isExtendItem)
		elseif itemFeature:IsHelmet() or itemFeature:IsBreastPlate() then
	-- Armor 1
			self:_SetArmorDetail()
		end
	-- Adapter 5
	elseif featureType == EFeatureType.Adapter and itemFeature:IsAdapter() then
		self:_SetAdapterDetail()
	elseif featureType == EFeatureType.Reward then
		self:_SetGiftDetail()
	elseif ItemHelperTool.GetMainTypeById(self._itemStruct.id) == EItemType.VehicleSkin then
		self:_SetVehicleSkinDetail()
	else
	-- Other 11
		self:_SetOtherDetail()
	end
end

-- function ItemDetailView:_UpdateBottomButton()
	-- self._allSourceInfo = {}
	-- self._allUsePlaceInfo = {}
	-- ItemDetailLogic.InitSource(self._itemStruct, self._allSourceInfo)
	-- ItemDetailLogic.InitUsePlace(self._itemStruct, self._allUsePlaceInfo)

	-- self:_RefreshSource()
	-- self:_RefreshUsePlace()
	-- self:_RefreshWishListBtn()
-- end

function ItemDetailView:_CommonSetContentSubUI(uiId, setFinishCallback)
	self._wtRealContentSlot:SetVisibility(ESlateVisibility.SelfHitTestInvisible)

	local function fLoadFinLogic()
		local prevContentUINavID = self._currContentUINavID
		self._currContentUINavID = uiId
		if prevContentUINavID ~= self._currContentUINavID then
			Facade.UIManager:RemoveSubUIByParent(self, self._wtRealContentSlot)
			local weakUiIns = Facade.UIManager:AddSubUI(self, uiId, self._wtRealContentSlot, nil, self)
			if getfromweak(weakUiIns) then
				self._curSubContentWeakUiIns = weakUiIns
			else
				loge("ItemDetailView:_CommonSetContentSubUI:UIManager:AddSubUI: weakUiIns is nil!")
			end
		end
		if self._wtScrollBoxContent and self._wtScrollBoxContent.ScrollToStart then
			self._wtScrollBoxContent:ScrollToStart()
		end
		if setFinishCallback then
			setFinishCallback(getfromweak(self._curSubContentWeakUiIns))
		end
	end

	local function fLoadFinCallback(mapPath2ResIns)
		self._asyncCreateUIInst = self._asyncCreateUIInst - 1
		fLoadFinLogic()
	end

	if Facade.UIManager:CheckUIHasBeenLoaded(uiId) then
		fLoadFinLogic()
	else
		self._asyncCreateUIInst = self._asyncCreateUIInst + 1
		local uiBatchId = Facade.UIManager:AsyncLoadUIResOnly(uiId, fLoadFinCallback, nil)
		table.insert(self._asyncCreateUIBatchId, uiBatchId)
	end
end

function ItemDetailView:_AfterLoadContentUI()
	if self._asyncCreateUIInst > 0 then
		return
	end
	for _, delayFuncInfo in ipairs(self._funcAfterContentFinish) do
		delayFuncInfo.func(self, unpack(delayFuncInfo.args))
	end
	self._funcAfterContentFinish = {}
end

function ItemDetailView:_SetWeaponDetail()
	local function OnCreateSubUIFinished(itemIns)
		local exArgs = {}
		exArgs.bCanDragAdapter = false
		exArgs.bAdapterCanFastUnequip = false
		exArgs.bAdapterTipsShowBtton = false
		itemIns:SetItem(self._itemStruct, exArgs)
		itemIns:SetBigTipsAlignWidget(self._bigTipsAlignWidget)
		self:_AfterLoadContentUI()
	end
	self:_CommonSetContentSubUI(UIName2ID.ItemDetailContentType2Weapon, OnCreateSubUIFinished)
end

function ItemDetailView:_SetWeaponCollectionDetail()
	local function OnCreateSubUIFinished(itemIns)
		local exArgs = {}
		exArgs.bSimple = self._bSimpleDetail
		exArgs.btnCallback = self._collectionBtnClickedCallback
		itemIns:SetItem(self._itemStruct, exArgs)
		self:_AfterLoadContentUI()
	end
	self:_CommonSetContentSubUI(UIName2ID.ItemDetailContentType2Collection, OnCreateSubUIFinished)
end

function ItemDetailView:_SetBulletDetail()
	local function OnCreateSubUIFinished(itemIns)
		local exArgs = {}
		itemIns:SetItem(self._itemStruct, exArgs)
		self:_AfterLoadContentUI()
	end
	self:_CommonSetContentSubUI(UIName2ID.ItemDetailContentType2Bullet, OnCreateSubUIFinished)
end

function ItemDetailView:_SetArmorDetail()
	local function OnCreateSubUIFinished(itemIns)
		itemIns:SetItem(self._itemStruct)
		self:_AfterLoadContentUI()
	end
	self:_CommonSetContentSubUI(UIName2ID.ItemDetailContentType2Armor, OnCreateSubUIFinished)
end

function ItemDetailView:_SetAdapterDetail()
	local function OnCreateSubUIFinished(itemIns)
		itemIns:SetItem(self._itemStruct)
		self:_AfterLoadContentUI()
		self:SetTitleBgVisible(false)
		if itemIns.hasBuff then
			self._wtLine:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		else
			self._wtLine:SetVisibility(ESlateVisibility.Collapsed)
		end
	end
	self:_CommonSetContentSubUI(UIName2ID.ItemDetailContentType2Adapter, OnCreateSubUIFinished)
end

function ItemDetailView:_SetGiftDetail()
	local function OnCreateSubUIFinished(itemIns)
		itemIns:SetItem(self._itemStruct)
		self:_AfterLoadContentUI()
	end
	self:_CommonSetContentSubUI(UIName2ID.ItemDetailContentType2Gift, OnCreateSubUIFinished)
end

function ItemDetailView:_SetVehicleSkinDetail()
	local outStr = VehicleHelperTool.GetVehicleDescByID(self._itemStruct.id) or  ""
	if not type(outStr) == "string" then
        outStr = tostring(outStr)   -- 可能是userdata
    end
	self:SetItemDesc(outStr)

	-- 联动标识
	local themeID = ThemeHelperTool.TryGetItemMapThemeID(self._itemStruct.id)
	if themeID then
		if self._wtThemeParent and UGPUserInterfaceUtil.IsThemeIDValid(themeID) and ItemDetailConfig.themeInfoList[themeID] then
			self._wtThemeIcon:AsyncSetImagePath(ItemDetailConfig.themeInfoList[themeID].ThemeIcon)
			self._wtThemeTxt:SetText(ItemDetailConfig.themeInfoList[themeID].ThemeTxt)
			self._wtThemePanel:SelfHitTestInvisible()
		end
	end
end

function ItemDetailView:_SetArmedForceItemDetail()
end

function ItemDetailView:_SetCollectionDetail()
	local outStr = self._itemStruct.description
	if not type(outStr) == "string" then
        outStr = tostring(outStr)   -- 可能是userdata
    end
	self:SetItemDesc(outStr)

	local function OnCreateSubUIFinished(itemIns)
		local exArgs = {}
		itemIns:SetItem(self._itemStruct, exArgs)
		self:_AfterLoadContentUI()
    end
    self:_CommonSetContentSubUI(UIName2ID.ItemdetailContentCollection, OnCreateSubUIFinished)
end

function ItemDetailView:_SetContainerDetail(isExtendItem)
	isExtendItem = setdefault(isExtendItem, false)
	local function OnCreateSubUIFinished(itemIns)
		-- 扩容箱屏蔽收藏按钮 
		-- self:SetLabelMarkItemVisible(not isExtendItem)
		-- self._wtLabelMarkItem:SetVisibility(isExtendItem and ESlateVisibility.Collapsed or ESlateVisibility.SelfHitTestInvisible)
		itemIns:SetItem(self._itemStruct)
		self:_AfterLoadContentUI()
	end
	self._wtTitleLine:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	self:_CommonSetContentSubUI(UIName2ID.ItemDetailContentType2Container, OnCreateSubUIFinished)
end

function ItemDetailView:_SetOtherDetail()

end

function ItemDetailView:_SetMeleeWeaponDetail()
	local function OnCreateSubUIFinished(itemIns)
		itemIns:SetItem(self._itemStruct)
		self:_AfterLoadContentUI()
	end
	self:_CommonSetContentSubUI(UIName2ID.ItemDetailContentMeleeWeapon, OnCreateSubUIFinished)
end

function ItemDetailView:_Reset()
	self._tradeMode = ETradeMode.Sell
	-- self:SetPriceVisible(true)
	self._bigTipsAlignWidget = self._wtItemDetailTitle

	-- content 部分reset
	self:_ClearAsyncSubUI()
	self._funcAfterContentFinish = {}
end

function ItemDetailView:_ClearAsyncSubUI()
	if self._asyncCreateUIBatchId and next(self._asyncCreateUIBatchId) then
		for _, uiBatchId in ipairs(self._asyncCreateUIBatchId) do
			if Facade.ResourceManager:CancelLoadByBatchId(uiBatchId) then
				local batch = Facade.ResourceManager:GetBatchByBatchId(uiBatchId)
				if batch and batch.mapPath2Idx then
					for path, _ in pairs(batch.mapPath2Idx) do
						loginfo('ItemDetailView:_ClearAsyncSubUI---------子UI中断加载,已取消:', path)
					end
				end
			end
		end
	end
	self._asyncCreateUIBatchId = {}
	self._asyncCreateUIInst = 0
end

function ItemDetailView:_SetItemPrice(customRichText)
	local showItem = self._itemStruct

	-- 藏品，货币不显示价格
	local itemFeature = nil
	if showItem ~= nil then
		itemFeature = showItem:GetFeature()
	end

	local bDisablePrice = itemFeature == nil or (itemFeature.IsCollecionItem and itemFeature:IsCollecionItem()) or (itemFeature.IsCurrency and itemFeature:IsCurrency())
	if bDisablePrice then
		self:SetPriceVisible(false)
		return
	elseif showItem and ItemHelperTool.GetMainTypeById(showItem.id) == EItemType.WeaponSkin then
		self:SetPriceVisible(false)
		return
	elseif showItem and ItemHelperTool.GetMainTypeById(showItem.id) == EItemType.Vehicle then
		self:SetPriceVisible(false)
		return
	elseif showItem and ItemHelperTool.GetMainTypeById(showItem.id) == EItemType.VehicleSkin then
		self:SetPriceVisible(false)
		return
	elseif showItem and ItemHelperTool.GetMainTypeById(showItem.id) == EItemType.SocialAppearance then
		self:SetPriceVisible(false)
		return
	elseif showItem and ItemHelperTool.GetMainTypeById(showItem.id) == EItemType.HeroAccessory then
		self:SetPriceVisible(false)
		return
	end

	local realPrice = 0
	if self._tradeMode == ETradeMode.Sell then
		realPrice = showItem:GetSingleSellPrice()
		-- self._wtCurrencyRichText:SetText(ShopHelperTool.GetDynamicGuidePriceRichTextByItemV2(showItem, 1, nil, nil, customRichText))
		self._wtCurrencyRichText:SetText(ShopHelperTool.GetDynamicGuidePriceRichTextByItemV3(showItem, 1, nil, nil, customRichText, false, FVector2D(44, 44)))
	else
		local bForceDefault = true
		realPrice = Server.ShopServer:GetShopBuyPriceByItem(showItem)
		-- self._wtCurrencyRichText:SetText(ShopHelperTool.GetShopBuyPriceRichTextByItemV2(showItem, 1, false, bForceDefault, customRichText))
		self._wtCurrencyRichText:SetText(ShopHelperTool.GetShopBuyPriceRichTextByItemV3(showItem, 1, false, bForceDefault, customRichText))
	end
	self:SetPriceVisible(realPrice ~= 0)
end

function ItemDetailView:_SetItemSpace()
	local length, height = self._itemStruct.length, self._itemStruct.width
	if length == 0 or height == 0 or ItemDetailLogic.IsCurrencyItem(self._itemStruct.id) or self._bInMp then
		self._wtItemSpaceGrid:SetVisibility(ESlateVisibility.Collapsed)
		return
	else
		self._wtItemSpaceGrid:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	end

	if ItemHelperTool.GetMainTypeById(self._itemStruct.id) == EItemType.WeaponSkin then
		self._wtWeightText:SetVisibility(ESlateVisibility.Collapsed)
	else
		self._wtWeightText:SetText(StringUtil.Key2StrFormat(ItemDetailConfig.Loc.weigth, {["weight"]=string.format("%.2f", self._itemStruct.weight)}))
		self._wtWeightText:SetVisibility(self._itemStruct.weight == 0 and ESlateVisibility.Collapsed or ESlateVisibility.SelfHitTestInvisible)
	end

	-- 先将所有格子置灰
	for idx = 0, 27 do
		local grid = self._wtItemSpaceGrid:GetChildAt(idx)
		grid:SetColorAndOpacity(FLinearColor(0.3,0.3,0.3,1))
	end
	-- 枪械道具和其他道具显示不同的格子数量
	if not self._itemStruct:GetFeature(EFeatureType.Weapon) then
		for h = 1, 4 do
			local sIdx = h * 7 - 3
			for idx = sIdx, h * 7 - 1 do
				local grid = self._wtItemSpaceGrid:GetChildAt(idx)
				if grid then
					grid:Collapsed()
				end
			end
		end
	else
		for idx = 0, 27 do
			local grid = self._wtItemSpaceGrid:GetChildAt(idx)
			grid:SelfHitTestInvisible()
		end
	end
	for h = 1, height do
		local sIdx = 7 * (h - 1)
		for idx = sIdx, sIdx + length - 1 do
			local grid = self._wtItemSpaceGrid:GetChildAt(idx)
			if grid then
				grid:SetColorAndOpacity(FLinearColor(1,1,1,1))
			end
		end
	end
end

-- function ItemDetailView:_RefreshSource()
	-- local bHaveSource = false
	-- self.sourceDataList = {}
	-- -- 根据 datalist 判断是否有来源途径
	-- for _, sourceInfo in ipairs(self._allSourceInfo) do
	-- 	if #sourceInfo.dataList > 0 then
	-- 		for _, datalist in pairs(sourceInfo.dataList) do
	-- 			table.insert(self.sourceDataList, datalist)
	-- 		end
	-- 	end
	-- end
	-- if #self.sourceDataList > 0 then
	-- 	bHaveSource = true
	-- end

	-- if bHaveSource then
	-- 	self._wSourceBtnItem:SetVisibility(self._bSourceVisible and ESlateVisibility.SelfHitTestInvisible or ESlateVisibility.Collapsed)
	-- else
	-- 	self._wSourceBtnItem:SetVisibility(ESlateVisibility.Collapsed)
	-- end
-- end

-- function ItemDetailView:_SourceBtnClicked()
-- 	local title = ItemDetailConfig.Loc.titleSource
-- 	local handle = Module.CommonTips:ShowDescriptionWindow(self._wSourceBtn, title, nil, self.sourceDataList, self)

-- 	if handle == nil then
-- 		return
-- 	end
-- end

-- function ItemDetailView:_RefreshUsePlace()
	-- local bHaveUsePlace = false

	-- self.usePlaceDataList = {}
	-- -- 根据 datalist 判断是否有来源途径
	-- for _, sourceInfo in ipairs(self._allUsePlaceInfo) do
	-- 	if #sourceInfo.dataList > 0 then
	-- 		for _, datalist in pairs(sourceInfo.dataList) do
	-- 			table.insert(self.usePlaceDataList, datalist)
	-- 		end
	-- 	end
	-- end
	-- if #self.usePlaceDataList > 0 then
	-- 	bHaveUsePlace = true
	-- end

	-- if bHaveUsePlace then
	-- 	self._wUsePlaceBtnItem:SetVisibility(self._bUsePlaceVisible and ESlateVisibility.SelfHitTestInvisible or ESlateVisibility.Collapsed)
	-- else
	-- 	self._wUsePlaceBtnItem:SetVisibility(ESlateVisibility.Collapsed)
	-- end
-- end

-- function ItemDetailView:_UsePlaceBtnClicked()
-- 	local title = ItemDetailConfig.Loc.titleUsePlace
-- 	local handle = Module.CommonTips:ShowDescriptionWindow(self._wUsePlaceBtn, title, nil, self.usePlaceDataList, self)

-- 	if handle == nil then
-- 		return
-- 	end
-- end

-- function ItemDetailView:_RefreshWishListBtn()
	-- self._wtLabelMarkItem:SetVisibility(self._bShowLabelMark and ESlateVisibility.SelfHitTestInvisible or ESlateVisibility.Collapsed)
	-- self._wtLabelMarkBtn:SetItem(self._itemStruct.id)
-- end

------------------------------------ Public function ------------------------------------
function ItemDetailView:Imp_SetInventoryItemInfo(InventoryItemInfo, PresetName)
	if isinvalid(InventoryItemInfo) then
		logerror("ItemDetailView:Imp_SetInventoryItemInfo InventoryItemInfo is nil!")
		return
	end

	local newItem = nil
    local id = UItemIDUtil.ToUint64(InventoryItemInfo.ItemId)
	if ItemHelperTool.GetSubTypeById(id) == ItemConfig.EWeaponItemType.Melee then
		logerror("ItemDetailView:Imp_SetInventoryItemInfo id is ItemConfig.EWeaponItemType.Melee! id: ", id)
		return
	end
    local num = InventoryItemInfo.ItemCount
    newItem = ItemBase:New(id, num)
    newItem:SetRawCppInfo(InventoryItemInfo)

	self:UpdateItem(newItem,false,false)
	self:BattleFieldStyle()
	self._wtItemDetailTitle:SelfHitTestInvisible()
	if PresetName and PresetName ~= "" then
		self._wtBluePrintText:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self._wtBluePrintText:SetText(PresetName)
		self._wtLine:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self._wtTitleLine:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	else
		self._wtBluePrintText:SetVisibility(ESlateVisibility.Collapsed)
		self._wtLine:SetVisibility(ESlateVisibility.Collapsed)
		self._wtTitleLine:SetVisibility(ESlateVisibility.Collapsed)
		return
	end
end

-- 更新详情页物品详情信息
-- itemStruct：物品信息
-- bPlayInAnim：切换道具的时候是否播放入场动画
function ItemDetailView:UpdateItem(itemStruct, bPlayInAnim, bSimple,...)
	bPlayInAnim = setdefault(bPlayInAnim, true)
	self._itemStruct = itemStruct
	self._bSimpleDetail = bSimple
	local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
	self._bInMp = armedForceMode == EArmedForceMode.MP

	self:_Reset()
	self._wtDFNamedSlot_139:Collapsed()
	self._wtThemePanel:Collapsed()
	self._wtDesc:SetVisibility(ESlateVisibility.Collapsed)
	self._wtTitleLine:SetVisibility(ESlateVisibility.Collapsed)
	if Server.ArmedForceServer:GetCurArmedForceMode() == EArmedForceMode.MP and (not Facade.GameFlowManager:CheckIsInFrontEnd()) then
		if self._wtMPFocus then
			self._wtMPFocus:Visible()
		end
	else
		if self._wtMPFocus then
			self._wtMPFocus:Collapsed()
		end
	end
	self:_UpdateDetailViewTitle()
	self:_SetItemPrice()
	-- self:_SetItemSpace()
	self:_UpdateDetailContent()
	self:_UpdateWeaponBluePrint()
	self:SetBigTipsAlignWidget(self._bigTipsAlignWidget)
	-- self:_UpdateBottomButton()
	if bPlayInAnim then
		self:PlayFadeInAnim()
	end

	local itemID = itemStruct == nil and 0 or itemStruct.id
	LogAnalysisTool.DoSendOpenItemDetailUILog(itemID)
end

function ItemDetailView:GetItem()
	return self._itemStruct
end

-- 设置交易模式
-- tradeMode: 出售（显示价格）:ETradeMode.Sell； 购买（隐藏价格）:ETradeMode.Buy
function ItemDetailView:SetTradeMode(tradeMode)
	self._tradeMode = tradeMode
	self:_SetItemPrice()
end

function ItemDetailView:SetPriceVisible(bVisible)
	if self._bInMp then
		self._wtCurrencyRichText:SetVisibility(ESlateVisibility.Collapsed)
		self._wtPanelPrice:Collapsed()
		return
	end
	if bVisible then
		self._wtCurrencyRichText:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self._wtPanelPrice:Visible()
	else
		self._wtCurrencyRichText:SetVisibility(ESlateVisibility.Collapsed)
		self._wtPanelPrice:Collapsed()
	end
end

-- 设置占格重量显隐性
function ItemDetailView:SetSpaceAndWeightVisible(bVisible)
	if self._bInMp then
		-- self._wtItemSpaceGrid:SetVisibility(ESlateVisibility.Collapsed)
		-- self._wtWeightText:Collapsed()
		return
	end
	if bVisible then
		-- self._wtItemSpaceGrid:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		-- self._wtWeightText:SelfHitTestInvisible()
	else
		-- self._wtItemSpaceGrid:SetVisibility(ESlateVisibility.Collapsed)
		-- self._wtWeightText:Collapsed()
	end
end

-- 设置来源按钮显隐性 目前所有使用场景都取消了此功能，UI提单删除，代码上先屏蔽
function ItemDetailView:SetSourcePanelVisible(bVisible)
	-- if self._bInMp then
	-- 	self._wtSourcePanel:SetVisibility(ESlateVisibility.Collapsed)
	-- 	return
	-- end
	-- if bVisible then
	-- 	self._wtSourcePanel:SelfHitTestInvisible()
	-- else
	-- 	self._wtSourcePanel:Collapsed()
	-- end
end

-- 自定义详情页标题
function ItemDetailView:SetItemTitleName(name)
	self._wtItemDetailTitle:SetItemTitleName(name)
end

-- 自定义详情页描述
function ItemDetailView:SetItemDesc(desc)
	self._wtDesc:SetText(desc)
	self._wtDesc:SetVisibility(ESlateVisibility.HitTestInvisible)
end

function ItemDetailView:SetTitleBgVisible(bShow)
	self._wtItemDetailTitle:SetTitleBgVisible(bShow)
end

function ItemDetailView:SetCloseBtnVisible(bShow)
	self._wtItemDetailTitle:SetCloseBtnVisible(bShow)
end

function ItemDetailView:SetCloseBtnCallback(callback)
	self._wtItemDetailTitle:SetCloseBtnCallback(callback)
end

function ItemDetailView:SetBtnClickedCallback(callback)
	self._collectionBtnClickedCallback = callback
end

function ItemDetailView:SetWeaponAdapterVisible(bVisible)
	if self._curSubContentWeakUiIns and getfromweak(self._curSubContentWeakUiIns) then
		if self._curSubContentWeakUiIns.SetWeaponAdapterVisible then
			self._curSubContentWeakUiIns:SetWeaponAdapterVisible(bVisible)
		end
	else
		table.insert(self._funcAfterContentFinish, {
			func = self.SetWeaponAdapterVisible,
			args = {bVisible}
		})
	end
end

function ItemDetailView:SetWeaponAttrVisible(bVisible)
	if self._curSubContentWeakUiIns and getfromweak(self._curSubContentWeakUiIns) then
		if self._curSubContentWeakUiIns.SetWeaponAttrVisible then
			self._curSubContentWeakUiIns:SetWeaponAttrVisible(bVisible)
		end
	else
		table.insert(self._funcAfterContentFinish, {
			func = self.SetWeaponAttrVisible,
			args = {bVisible}
		})
	end
end

function ItemDetailView:SetSourceDescVisible(bVisible)
	-- self._bSourceVisible = bVisible
end

function ItemDetailView:SetUsePlaceDescVisible(bVisible)
	-- self._bUsePlaceVisible = bVisible
end
function ItemDetailView:SetLabelMarkItemVisible(bVisible)
	-- self._bShowLabelMark = bVisible
end

function ItemDetailView:PlayInAnimation(bShow, bUp)
    self:PlayAnimation(self.Anima_AutoIn, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
end

function ItemDetailView:SetBigTipsAlignWidget(widget)
	self._bigTipsAlignWidget = widget
	if self._curSubContentWeakUiIns and getfromweak(self._curSubContentWeakUiIns) then
		if self._curSubContentWeakUiIns.SetBigTipsAlignWidget then
			self._curSubContentWeakUiIns:SetBigTipsAlignWidget(widget)
		end
	else
		table.insert(self._funcAfterContentFinish, {
			func = self.SetBigTipsAlignWidget,
			args = {widget}
		})
	end
end

function ItemDetailView:BattleFieldStyle()
	if self._curSubContentWeakUiIns and getfromweak(self._curSubContentWeakUiIns) then
		if self._curSubContentWeakUiIns.BattleFieldStyle then
			self._curSubContentWeakUiIns:BattleFieldStyle()
		end
	else
		table.insert(self._funcAfterContentFinish, {
			func = self.BattleFieldStyle,
			args = {}
		})
	end
end

function ItemDetailView:Reset()
	self:_Reset()
end

------------- ItemDetailContent -------------
function ItemDetailView:GetContentSlot()
	return self._wContentSlot--:GetContent()
end

function ItemDetailView:ScrollToContentEnd()
	self._wtScrollBoxContent:ScrollToContentEnd()
end

function ItemDetailView:ScrollToContentStart()
	self._wtScrollBoxContent:ScrollToContentStart()
end

function ItemDetailView:PlayFadeInAnim()
	self:PlayWidgetAnim(self.WBP_ItemDetailView_in_01)
end

function ItemDetailView:SetContentScrollCallback(callback)
    if callback then
        self._wtScrollBoxContent:Event("OnUserScrolled", self._OnScrolled, self)
    else
        self._wtScrollBoxContent:RemoveEvent("OnUserScrolled")
    end
    self._onContentScrollCallback = callback
end

function ItemDetailView:_OnScrolled(CurOffset)
    if self._onContentScrollCallback then
        self._onContentScrollCallback(CurOffset)
    end
end

--BEGIN MODIFICATION @ VIRTUOS : 初始化打开武器详情内容快捷键 - 仅限 ItemDetailContentType2Weapon
function ItemDetailView:InitSubUIShortcuts()
	if not IsHD() then
		return 
	end

	local curSubContentWeakUiIns = getfromweak(self._curSubContentWeakUiIns)
	if curSubContentWeakUiIns and curSubContentWeakUiIns.InitShortcuts then
		curSubContentWeakUiIns:InitShortcuts()
	end
end

-- 移除打开武器详情内容快捷键
function ItemDetailView:RemoveSubUIShortcuts()
	if not IsHD() then
		return 
	end

	local curSubContentWeakUiIns = getfromweak(self._curSubContentWeakUiIns)
	if curSubContentWeakUiIns and curSubContentWeakUiIns.RemoveShortcuts then
		curSubContentWeakUiIns:RemoveShortcuts()
	end
end

function ItemDetailView:EnableSubUINavigation(bEnable)
	if not IsHD() then
		return 
	end

	local ItemDetailContentWeaponIns = getfromweak(self._curSubContentWeakUiIns)
	if not hasdestroy(ItemDetailContentWeaponIns) then
		if bEnable then
			if not self._NavGroup_ScrollBox then
				self._NavGroup_ScrollBox = WidgetUtil.RegisterNavigationGroup(self._wtScrollBoxContent, self, "Hittest")
				if self._NavGroup_ScrollBox then
					self._NavGroup_ScrollBox:MarkIsStackControlGroup()
				end
				
				-- 武器详情内容 - ItemDetailContentType2Weapon ：
				-- 导航不直接在wScrollBoxContent上设置，而是在详情内容的WeaponBox上设置；
				-- 这是因为用ScrollBox设置导航，ScrollBox的范围太小，限制了导航组内组件能否导航的检测，会导致没显示出来的组件导航不了。
				if ItemDetailContentWeaponIns.InitNavigationDetailContentWeapon then
					ItemDetailContentWeaponIns:InitNavigationDetailContentWeapon(self._wtScrollBoxContent)
				end
				-- WBP_ItemDetailView_Equipped 内的三个CheckBox，不确认在武器升级页面是否会显示，先加上导航组（不显示不会影响上面的导航）
				local DFHorizontalBox = self:Wnd("DFHorizontalBox_0", UIWidgetBase)
				if DFHorizontalBox then
					if not self._NavGroup_HorizontalBox then
						self._NavGroup_HorizontalBox = WidgetUtil.RegisterNavigationGroup(DFHorizontalBox, self, "Hittest")
						if self._NavGroup_HorizontalBox then
							self._NavGroup_HorizontalBox:AddNavWidgetToArray(DFHorizontalBox)
							self._NavGroup_HorizontalBox:SetScrollRecipient(self._wtScrollBoxContent)
						end
					end
				end

				WidgetUtil.BuildGroupTree(self._wtScrollBoxContent)
				WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup_ScrollBox)
			end
		else
			if self._NavGroup_ScrollBox or self._NavGroup_HorizontalBox then
				WidgetUtil.RemoveNavigationGroup(self)
				self._NavGroup_ScrollBox = nil
				self._NavGroup_HorizontalBox = nil
			end
			if ItemDetailContentWeaponIns.RemoveNavigationDetailContentWeapon then
				ItemDetailContentWeaponIns:RemoveNavigationDetailContentWeapon()
			end
		end
	end
end
--END MODIFICATION

----------改枪台方案调用Start----------
-- 参数示例：
-- labelTexts		{"跑刀", "近战"}
-- labelTipText		"策划配置的提示文本"
-- labelTipDesc		"策划配置的描述文本"
-- weaponName		"枪械名字"
---@param labelTexts table
---@param labelTipText string
---@param labelTipDesc string
---@param weaponName string
function ItemDetailView:RefreshGunsmithUI(labelTexts, labelTipText, labelTipDesc, weaponName)
	labelTexts = labelTexts or {}
	labelTipText = labelTipText or ""
	labelTipDesc = labelTipDesc or ""
	weaponName = weaponName or ""

	local uiIns = getfromweak(self._GunsmithWeakUI)
	if uiIns and isvalid(uiIns) and uiIns.IsValid and uiIns:IsValid() then
		self._wtDFNamedSlot_139:SelfHitTestInvisible()
		uiIns:RefreshUI(labelTexts, labelTipText, labelTipDesc, weaponName)
	else
		local function fLoadFinLogic()
			self._wtDFNamedSlot_139:SelfHitTestInvisible()
			Facade.UIManager:RemoveSubUIByParent(self, self._wtDFNamedSlot_139)
			local weakUiIns = Facade.UIManager:AddSubUI(self, UIName2ID.ItemDetailEquippedSolution, self._wtDFNamedSlot_139)
			uiIns = getfromweak(weakUiIns)
			if uiIns then
				self._GunsmithWeakUI = weakUiIns
				uiIns:RefreshUI(labelTexts, labelTipText, labelTipDesc, weaponName)
			else
				logerror("ItemDetailView:InitLabelUI: weakUiIns is nil!")
			end
		end
		local SafeLoadFinLogic = CreateCallBack(fLoadFinLogic, self)
		local function fLoadFinCallback(mapPath2ResIns)
			self._asyncCreateUIInst = self._asyncCreateUIInst - 1
			SafeLoadFinLogic()
		end
		if Facade.UIManager:CheckUIHasBeenLoaded(UIName2ID.ItemDetailEquippedSolution) then
			SafeLoadFinLogic()
		else
			self._asyncCreateUIInst = self._asyncCreateUIInst + 1
			local uiBatchId = Facade.UIManager:AsyncLoadUIResOnly(UIName2ID.ItemDetailEquippedSolution, fLoadFinCallback, nil)
			table.insert(self._asyncCreateUIBatchId, uiBatchId)
		end
	end

end
----------改枪台方案调用End----------

return ItemDetailView