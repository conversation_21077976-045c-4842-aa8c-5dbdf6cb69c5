----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class SystemSettingHDCloudSettingSlot : LuaUIBaseView
local SystemSettingHDCloudSettingSlot = ui("SystemSettingHDCloudSettingSlot")
local CloudSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.CloudSettingLogicHD"

function SystemSettingHDCloudSettingSlot:Ctor()
    self._wtName = self:Wnd("DFTextBlock_50", UITextBlock)
    self._wtDateTime = self:Wnd("DFTextBlock_1", UITextBlock)

    self._wtEditBtn = self:Wnd("DFButton_53", UIButton)
    self._wtDeleteBtn = self:Wnd("WBP_CommonIconButton", DFCommonButtonOnly)
    self._wtSaveBtn = self:Wnd("WBP_CommonButtonV2S2_37", DFCommonButtonOnly)
    self._wtApplyBtn = self:Wnd("WBP_CommonButtonV2S1", DFCommonButtonOnly)

    self._wtEditBtn:Event("OnClicked", self._OnEditButtonClicked, self)
    self._wtDeleteBtn:Event("OnClicked", self._OnDeleteButtonClicked, self)
    self._wtSaveBtn:Event("OnClicked", self._OnSaveButtonClicked, self)
    self._wtApplyBtn:Event("OnClicked", self._OnApplyButtonClicked, self)
end

function SystemSettingHDCloudSettingSlot:_OnEditButtonClicked()
    local name = nil
    if self:GetVisibility() ~= ESlateVisibility.Collapsed then
        name = tostring(self._wtName:GetText())
    end
    local fOnNameConfirmed = function(newName)
        if newName then
            self._wtName:SetText(newName)
            CloudSettingLogicHD.SetName(self._index, newName)
        end
    end
    CloudSettingLogicHD.CreateNameWindow(fOnNameConfirmed, nil, name)
end

function SystemSettingHDCloudSettingSlot:_OnDeleteButtonClicked()
    local OnConfirm = function()
        CloudSettingLogicHD.DeleteIndex(self._index)
    end
    Module.CommonTips:ShowConfirmWindow(Module.SystemSetting.Config.Loc.HDSetting.CloudSettingTxt.ConfirmDelete, OnConfirm)
end

function SystemSettingHDCloudSettingSlot:_OnSaveButtonClicked()
    local OnConfirm = function()
        CloudSettingLogicHD.OverWrite(self._index)
    end
    Module.CommonTips:ShowConfirmWindow(Module.SystemSetting.Config.Loc.HDSetting.CloudSettingTxt.ConfirmSave, OnConfirm)
end

function SystemSettingHDCloudSettingSlot:_OnApplyButtonClicked()
    CloudSettingLogicHD.ApplyIndex(self._index)
end

function SystemSettingHDCloudSettingSlot:_SetName(Name)
    self._wtName:SetText(Name)
end

function SystemSettingHDCloudSettingSlot:_SetDate(date)
    local dateString = TimeUtil.TransTimestamp2YYMMDDHHMMSSStr(date, "YY/MM/DD HH:MM:SS")
    string.gsub(dateString, "-", "/")
    self._wtDateTime:SetText(dateString)
end

function SystemSettingHDCloudSettingSlot:_SetIsApplied(bApplied)
    self._applied = bApplied
    self._wtApplyBtn:SetIsEnabled(not bApplied)
    local applyBtnText = bApplied and Module.SystemSetting.Config.Loc.HDSetting.ApplyingTxt or Module.SystemSetting.Config.Loc.HDSetting.ApplyTxt
    self._wtApplyBtn:SetMainTitle(applyBtnText)

    self._autoSave = bApplied and CloudSettingLogicHD.GetAutoUploadState()
    self._wtSaveBtn:SetIsEnabled(not self._autoSave)
    local saveBtnText = self._autoSave and Module.SystemSetting.Config.Loc.HDSetting.CloudSettingTxt.CloudSettingAutoSavin or Module.SystemSetting.Config.Loc.HDSetting.CloudSettingTxt.CloudSettingSave
    self._wtSaveBtn:SetMainTitle(saveBtnText)
end

function SystemSettingHDCloudSettingSlot:SetIndex(index)
    if not index then
        self:Collapsed()
        self._index = index
        return
    end
    local meta = Module.SystemSetting.Field:GetCloudSettingMeta()
    if meta and meta.Slots then
        for _, v in ipairs(meta.Slots) do
            if v.index == index then
                local bApplied = CloudSettingLogicHD.GetAppliedCloudSettingIndex() == index
                local bAutoSave = bApplied and CloudSettingLogicHD.GetAutoUploadState()
                if self._index ~= index then
                    self._index = index
                    self:_SetName(CloudSettingLogicHD.GetDisplayNameFromMetaName(v.name))
                    self:_SetDate(v.timestamp)
                    self:_SetIsApplied(bApplied)
                    self:SelfHitTestInvisible()
                elseif self._applied ~= bApplied or self._autoSave ~= bAutoSave then
                    self:_SetIsApplied(bApplied)
                end
                break
            end
        end
    end
end

return SystemSettingHDCloudSettingSlot