----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMIrisSafeHouse)
----- LOG FUNCTION AUTO GENERATE END -----------



local IrisVoiceDownLogic = {}

local CommonPopWindows = require "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindows"
local LocalizeTool = require "DFM.StandaloneLua.BusinessTool.LocalizeTool"


--------------------------------------------------------------------------
--- GA首次登陆语音包下载流程指引
--------------------------------------------------------------------------

-- 首次登陆语音包下载提示
IrisVoiceDownLogic.ShowVoiceDownTipWindow = function() 
    -- 确定跳转到下载中心
    local confirm_func = function() 
        Module.SystemSetting:ShowSystemSettingMainView(Module.SystemSetting.Config.ESystemSettingPanel.LanguageSetting, true)
    end 
    -- 取消 默认播放英文语音  
    local cancel_func = function() logwarning("IrisVoiceDownLogic.ShowVoiceDownTipWindow cance_func") end   
    Module.CommonTips:ShowConfirmWindow(Module.IrisSafeHouse.Config.Loc.VoDownTipsContent, confirm_func, cancel_func)
end

-- 语音包下载完成提示
IrisVoiceDownLogic.ShowVoiceDownFinishTipWindow = function() 
    local confirm_func = function() logwarning("IrisVoiceDownLogic.ShowVoiceDownFinishTipWindow confirm_func ") end   
    Module.CommonTips:ShowConfirmWindow(Module.IrisSafeHouse.Config.Loc.VoDownFinishTipsContent, confirm_func, nil, "-1")
end

-- 语音包下载中断 退出提示 
IrisVoiceDownLogic.ShowVoiceDownBreakTipWindow = function(confirm_func, cancel_func)
    -- 返回模式大厅 
    Module.CommonTips:ShowConfirmWindow(Module.IrisSafeHouse.Config.Loc.VoDownBreakTipsContent, confirm_func, cancel_func)
end

IrisVoiceDownLogic.IsGuideDownLoad = function(curLanguage)
    local voiceConfig = Facade.TableManager:GetTable("Localize/LocalizeCultureConfig")
    for _,cultureInfo in pairs(voiceConfig) do
        -- loginfo(cultureInfo.CultureSign,"===IsGuideDownLoad===", LocalizeTool.CompatibleCultureSign(curLanguage))
        if LocalizeTool.CompatibleCultureSign(cultureInfo.CultureSign) == LocalizeTool.CompatibleCultureSign(curLanguage) and cultureInfo.IsGuideDownload ~= 0 then
            return true
        end
    end
    return false
end

return IrisVoiceDownLogic