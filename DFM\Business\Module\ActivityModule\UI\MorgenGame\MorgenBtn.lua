---------- LOG FUNCTION AUTO GENERATE -------------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
-------- LOG FUNCTION AUTO GENERATE END -----------

---对应蓝图:WBP_Example
---@class MorgenBtn : LuaUIBaseView
local MorgenBtn = ui("MorgenBtn")

function MorgenBtn:Ctor()
	self._wtBtn = self:Wnd("DFButton_0", UIButton)
	self._wtText = self:Wnd("Text_01", UITextBlock)
	self._wtIcon = self:Wnd("DFImage_Icon", UIImage)
    self._wtBtn:Event("OnClicked", self.OnClicked, self)

	self.fClickCallback = nil
end

function MorgenBtn:OnClicked()
	if self.fClickCallback then
		self.fClickCallback()
	end
end

function MorgenBtn:RefreshInfo(Callback, title, icon)
	self._wtText:SetText(title)
	self.fClickCallback = Callback
	self._wtIcon:AsyncSetImagePath(icon)
end

function MorgenBtn:RefreshUI()
end

return MorgenBtn