----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMWeaponAssembly)
----- LOG FUNCTION AUTO GENERATE END -----------

---阿萨拉巡旅
---@class ActivityTaraMain : LuaUIBaseView
local ActivityTaraMain = ui("ActivityTaraMain")
local ActivityTaraHead  = require "DFM.Business.Module.ActivityModule.UI.Tara.ActivityTaraHead"
local ActivityTaraItem1 = require "DFM.Business.Module.ActivityModule.UI.Tara.ActivityTaraItem1"
local ActivityTaraHead2 = require "DFM.Business.Module.ActivityModule.UI.Tara.ActivityTaraItem2"
local ActivityTaraPanel1 = require "DFM.Business.Module.ActivityModule.UI.Tara.ActivityTaraPanel1"
local ActivityTaraPanel2 = require "DFM.Business.Module.ActivityModule.UI.Tara.ActivityTaraPanel2"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
local Config = Module.Activity.Config

ETaraHandleType = {
    None    = 0; --默认
    Add     = 1; --添加
    Send    = 2; --发送
    Refresh = 3; --刷新
    Comment = 4; --评论
    Switch  = 5; --切换
}

function ActivityTaraMain:Ctor()
    self._wtState = self:Wnd("WBP_CommonButtonV2S2_37" , DFCommonButtonOnly)
    self._wtItem1 = self:Wnd("NewWidgetBlueprint2_C_2" , ActivityTaraItem1)
    self._wtItem2 = self:Wnd("NewWidgetBlueprint3"     , ActivityTaraHead2)
    self._wtFHead = self:Wnd("NewWidgetBlueprint"      , ActivityTaraHead)
    self._wtPanle = self:Wnd("DFVerticalBox_55"        , UIWidgetBase)
    self._wtToday = self:Wnd("DFRichTextBlock_0"       , UITextBlock)
    self._wtFCard = self:Wnd("DFImage_83"              , UIImage)
    self._wtTitle = self:Wnd("DFTextBlock"             , UITextBlock)
    self._wtFIcon = self:Wnd("DFImage_496"             , UIImage)
    self._wtFName = self:Wnd("DFTextBlock_168"         , UITextBlock)
    self._wtDFMax = self:Wnd("DFTextBlock_309"         , UITextBlock)
    self._wtDFGou = self:Wnd("WBP_SlotCompGet"         , UIWidgetBase)
    self._wtMaxTp = self:Wnd("DFRichTextBlock"         , UITextBlock)
    self._wtNFBox = self:Wnd("DFScrollBox_2"           , UIWidgetBase)
    self._wtCheck = self:Wnd("wtCommonCheckInstruction", DFCheckBoxOnly)
    self._wtState:Event("OnClicked", self._OnReqClicked, self)
    self._wtState:Visible()
    --切换/发送
    self._wtSwitchItem = self:Wnd("WBP_PatrolAsala_SwitchBlogger_Q1ZNK", ActivityTaraPanel1)
    self._wtSendNeItem = self:Wnd("WBP_PatrolAsala_InputField_Q1ZNK"   , ActivityTaraPanel2)
    self._wtReddotsIns = Module.ReddotTrie:CreateReddotIns(self:Wnd("Red", UIWidgetBase), nil, nil, nil, FVector2D(0, 12))
    self._wtbusCardBtn = self:Wnd("DFButton_16", UIButton)
    self._wtbusCardBtn:Event("OnClicked", self._OnClicked, self)
    self._wtDFBox = UIUtil.WndWaterfallScrollBox(self, "DFWaterfallScrollView_150", self._OnWaterfallCount, self._OnWaterfallWidget)
    if IsHD() then
        self._wtAnchor = UIUtil.WndTipsAnchor(self, "DFTipsAnchor_92", self._OnHovered, self._OnUnhovered, nil)
    else
        self._wtAnchor = self:Wnd("DFTipsAnchor_92", UIWidgetBase)
        self._wtCheck:SetCallback(self._OnCheckChanged, self)
    end
    self:Wnd("Image_95", UIImage):Collapsed()
    self:Wnd("Image_1", UIImage):Collapsed()
    self._wtHotzone = {
        self._wtDFBox,
        self:Wnd("DFScrollBox_2", UIWidgetBase),
    }
    self:InjectLua()
end

function ActivityTaraMain:_OnReqClicked()
    local hero = self._hero
    if hero.rawState == 0 then
        local confirmHandle = function ()
            Server.ActivityServer:SendTaraSwitchReq(self._activityID, hero.heroId)
        end
        local name = nil
        for index, item in ipairs(self._items or {}) do
            if item.state == 1 then
                local count = 0
                for _, wechatMoment in ipairs(item.items or {}) do
                    if wechatMoment.count and wechatMoment.count > count then
                        count = wechatMoment.count
                    end
                end
                if item.count and item.count < count then
                    name = item.name
                end
                break
            end
        end
        Module.CommonTips:ShowConfirmWindow(General.GetText(Config.Loc.ThisWillInterrupt, name or ""), confirmHandle)
    end
end

function ActivityTaraMain:_OnClicked()
    if self._isAvailable then
        local hero = self._hero
        if self._activityID and hero then
            Server.ActivityServer:SendTaraMaxRewardReq(self._activityID, hero.heroId)
        end
    end
end

function ActivityTaraMain:_OnHovered()
    self:_OnUnhovered()
    if self._tips and self._hero then
        local contents = {}
        local title = {
            id = UIName2ID.Assembled_CommonMessageTips_V8,
            background = 1,
            data = {
                textContent = Config.Loc.ScoringBehavior,
                styleRowId  = "C002",
                isLineIcon = true,
            },
        }
        table.insert(contents, title)
        for index, value in ipairs(self._tips or {}) do
            table.insert(contents, {
                id = UIName2ID.Assembled_CommonMessageTips_V5,
                background = 1,
                data = {
                    textContent = General.GetText(Config.Loc.ScoreContent, value.desc),
                    styleRowId  = "C002",
                    isLineIcon = true,
                },
            })
        end
        local list = {
            {
                id = UIName2ID.Assembled_CommonMessageTips_V8,
                background = 1,
                data = {
                    textContent = General.GetText(Config.Loc.ScoreLimit, self._hero.todaymax),
                    styleRowId  = "C002",
                    isLineIcon = true,
                },
            },
            {
                id = UIName2ID.Assembled_CommonMessageTips_V5,
                background = 1,
                data = {
                    textContent = Config.Loc.AllBloggers,
                    styleRowId  = "C002",
                    isLineIcon = true,
                },
            },
            {
                id = UIName2ID.Assembled_CommonMessageTips_V5,
                background = 1,
                data = {
                    textContent = Config.Loc.TheUpperLimitIs,
                    styleRowId  = "C002",
                    isLineIcon = true,
                },
            },
        }
        for index, value in ipairs(list or {}) do
            table.insert(contents, value)
        end
        self._handle = Module.CommonTips:ShowAssembledTips(contents, self._wtAnchor)
    end
end

function ActivityTaraMain:_OnUnhovered()
    if self._handle and self._handle.GetUIIns then
        self._wtCheck:SetIsChecked(false, false)
        Module.CommonTips:RemoveAssembledTips(self._handle, self._wtAnchor)
        self._handle = nil
    end
end

function ActivityTaraMain:_OnCheckChanged(isBool)
    if isBool then
        self:_OnHovered()
    else
        self:_OnUnhovered()
    end
end

function ActivityTaraMain:OnInitExtraData(activityID)
    self._activityID = activityID
    self._hero = nil
    self._defaultIndex = nil
end

function ActivityTaraMain:_InitData()
    if self._activityID then
        local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(self._activityID)
        if activityInfo then
            self._desc = activityInfo.details
            local info = activityInfo.ahsarah_travel_info
            if info then
                local heroIds = {}
                self._items = {}
                --整理干员ID
                for index, hero in ipairs(info.hero_info or {}) do
                    heroIds[hero.hero_id] = hero
                end
                for index, data in ipairs(info.line_infos or {}) do
                    local item = {}
                    local hero = heroIds[data.hero_id]
                    if hero then
                        item.heroId = data.hero_id  --干员ID
                        item.name   = hero.hero_name--干员名称
                        item.path   = hero.image    --头像本地路径
                    end
                    item.items = {}
                    for _, message in ipairs(data.messages or {}) do
                        local wechatMoments = {}
                        wechatMoments.playerId   = info.self_id                   --玩家ID
                        wechatMoments.playerName = Server.RoleInfoServer.nickName --玩家昵称
                        wechatMoments.playerPath = Server.RoleInfoServer.picUrl   --玩家头像
                        wechatMoments.id       = message.id           --唯一Id
                        wechatMoments.heroId   = data.hero_id         --干员Id(自己)
                        wechatMoments.count    = message.progress     --进度节点
                        wechatMoments.title    = message.title        --标题
                        wechatMoments.desc     = message.desc         --描述
                        wechatMoments.icons    = message.images       --图标数组
                        wechatMoments.time     = message.time         --时间
                        wechatMoments.likes    = message.like_hero_ids--点赞干员ID
                        wechatMoments.goldId   = activityInfo.gold_id --点赞代币id
                        wechatMoments.goldNum  = activityInfo.gold_num--点赞代币数量
                        wechatMoments.contents = message.reply_choices--可回复内容数组
                        wechatMoments.replied  = message.replied      --是否回复
                        wechatMoments.rewards  = message.show_rewards --未解锁展示奖励
                        wechatMoments.news = {}
                        for _, relay in ipairs(message.relays or {}) do
                            local new = {}
                            new.playerId   = info.self_id                   --玩家ID
                            new.playerName = Server.RoleInfoServer.nickName --玩家昵称
                            new.playerPath = Server.RoleInfoServer.picUrl   --玩家头像
                            new.wechatId = message.id         --朋友圈Id
                            new.id       = relay.id           --唯一Id
                            new.heroId   = relay.hero_id      --干员Id
                            new.time     = relay.time         --时间
                            new.comment  = relay.desc         --评论
                            new.rewards  = relay.rewards      --奖励
                            new.received = relay.received     --是否领取
                            new.atId     = relay.at_id        --回复干员ID
                            new.replied  = relay.replied      --是否回复
                            new.contents = relay.reply_choices--可回复内容数组
                            table.insert(wechatMoments.news, new)
                        end
                        table.insert(item.items, wechatMoments)
                    end
                    local state = data.state
                    local count = 0
                    for _, value in ipairs(item.items or {}) do
                        if value.count >= count then
                            count = value.count
                        end
                    end
                    if data.progress >= count and state == 1 then
                        state = 2
                    end
                    item.title    = data.name              --标题
                    item.rawState = data.state             --同行状态(原始状态)
                    item.state    = state                  --同行状态
                    item.count    = data.progress          --进度
                    item.received = data.received          --是否领取
                    item.rewards  = data.final_rewards     --奖励数组
                    item.todaymin = info.today_progress    --今日进度
                    item.todaymax = info.daily_max_progress--今日上限
                    item.tips     = info.score_descs       --提示描述数组
                    table.insert(self._items, item)
                end
            end
        end
    end
end

function ActivityTaraMain:_InitPanel(selectHero)
    --同行检查
    local unSamed = false
    local finishItem = nil
    self._hero = selectHero
    if selectHero == nil then
        --解析同行数据
        for index, item in ipairs(self._items or {}) do
            if item.rawState then
                if item.rawState == 0 then
                    unSamed = true
                end
                if item.rawState == 1 then
                    self._hero = item
                    --检查是否完成
                    local count = 0
                    for _, wechatMoment in ipairs(item.items or {}) do
                        if wechatMoment.count and wechatMoment.count > count then
                            count = wechatMoment.count
                        end
                    end
                    if item.count and item.count >= count then
                        --未领取奖励处理
                        local hero = self._hero
                        if hero then
                            local received = true
                            for i, message in ipairs(hero.items or {}) do
                                for j, relay in ipairs(message.relays or {}) do
                                    if relay.received ~= nil and relay.received == false then
                                        received = false
                                        break
                                    end
                                end
                            end
                            if hero.received and received then
                                self._hero = nil
                            end
                        end
                    end
                end
                if finishItem == nil then
                    finishItem = item
                end
                if item.rawState == 1 then
                    finishItem = item
                end
            end
        end
        if unSamed and self._hero == nil then
            --打开干员选择界面
            Facade.UIManager:AsyncShowUI(UIName2ID.ActivityTaraPop1, nil, self, self._activityID, self._items)
        end
    end
    --检查干员
    if self._hero == nil then
        self._hero = finishItem
    end
    local hero = self._hero
    if hero then
        self._wtFHead:InitData(self._activityID, hero, nil, 1)
        self._wtFName:SetText(General.GetText(hero.name  or ""))
        self._wtTitle:SetText(General.GetText(hero.title or ""))
        --同行3状态
        if hero.rawState == 0 then
            self._wtState:SetMainTitle(Config.Loc.ActUITextBlock[11] or "")
        elseif hero.rawState == 1 then
            self._wtState:SetMainTitle(Config.Loc.ActUITextBlock[7] or "")
        else
            self._wtState:SetMainTitle(Config.Loc.ActUITextBlock[8] or "")
        end
        self._wtDFMax:SetText(hero.count or "")
        self._wtToday:SetText(General.GetText(Config.Loc.TodaysUpperLimit, hero.todaymin, hero.todaymax))
        --初始化名片
        for index, prop in ipairs(hero.rewards or {}) do
            local itemData = General.GetSkinItemData(prop)
            if itemData then
                self._wtFCard:AsyncSetImagePath(itemData.itemIconPath or "", true)
                break
            end
        end
        --默认选中
        local count = 0
        local defaultIndex = 1
        for index, item in ipairs(hero.items or {}) do
            if item.count and hero.count and hero.count >= item.count then
                count = index
                defaultIndex = index
            end
        end
        if self._defaultIndex == nil then
            self._defaultIndex = defaultIndex or 1
        end
        --大奖提示信息
        if hero.items then
            self._wtMaxTp:SetText(General.GetText(Config.Loc.AllBlogsToRewards, count, #hero.items))
            if hero.received then
                --已领取
                self._wtDFGou:SetVisibility(ESlateVisibility.HitTestSelfOnly)
                self._wtReddotsIns:SetReddotVisible(false)
            else
                --可领取
                if count == #hero.items then
                    self._wtReddotsIns:SetReddotVisible(true, EReddotType.Normal)
                else
                    self._wtReddotsIns:SetReddotVisible(false)
                end
                self._wtDFGou:SetVisibility(ESlateVisibility.Collapsed)
            end
            self._isAvailable = not hero.received and count == #hero.items
        end
        --初始化评论状态
        self:_OpenTaraPanel(self._activityID)
        --手柄适配
        self:_InitHandleAdaptation()
        self:_SetHandleAdaptation()   
        self:_AddHandleAdaptation(self._wtbusCardBtn)
        self:_AddHandleAdaptation(self._wtNFBox)
        --初始化提示文本
        self._tips = hero.tips
        self._list = hero.items
        self._wtDFBox:RefreshAllItems()
    end
end

function ActivityTaraMain:_OnClickTaraItem(index)
    self._defaultIndex = index
    local hero = self._hero
    if self._list then
        local data = self._list[index]
        if data and hero then
            self:_SetHandleAdaptation()
            self._wtItem1:InitData(self._activityID, data, index, hero)
            --是否解锁
            if data.count and hero.count then
                if hero.count >= data.count then
                    self._wtItem2:InitData(self._activityID, data, index, self._items)
                    self:_AddSubItem()
                    for itemIndex, new in ipairs(data.news or {}) do
                        local item = self:_AddSubItem(itemIndex)
                        if item then
                            item:InitData(self._activityID, new, itemIndex, self._items, #data.news)
                        end
                    end
                    self._wtItem2:SetVisibility(ESlateVisibility.Visible)
                    self._wtPanle:SetVisibility(ESlateVisibility.Visible)
                else
                    self._wtItem2:SetVisibility(ESlateVisibility.Collapsed)
                    self._wtPanle:SetVisibility(ESlateVisibility.Collapsed)
                end
            end
            Module.ItemDetail:CloseItemDetailPanel()
        end
    end
end

function ActivityTaraMain:_AddEventListener()
    self:AddLuaEvent(Config.evtOnClickTaraItem, self._OnClickTaraItem, self)--里程碑点击
    self:AddLuaEvent(Config.evtOpenTaraPanel, self._OpenTaraPanel, self)--打开评论/切换
    self:AddLuaEvent(Config.evtRefreshTaraPanel, self._OnRefreshTaraPanel, self)--本地刷新
    --回复/评论/切换/领奖
    self:AddLuaEvent(Server.ActivityServer.Events.evtActTaraTrainingTravel, self._OnActTaraTrainingTravel, self)
    --手柄适配
    self:AddLuaEvent(Config.evtAddTaraHandleAdaptation, self._OnAddTaraHandleAdaptation, self)
end

--手柄适配分发器🎮
function ActivityTaraMain:_OnAddTaraHandleAdaptation(handleType, ...)
    if handleType == ETaraHandleType.Add then
        self:_AddHandleAdaptation(...)
    end
    if handleType == ETaraHandleType.Comment then
        self:_SetCommentData(...)
        self:_SetHandleAdaptation(0)
    end
    if handleType == ETaraHandleType.None then
        self:_SetHandleAdaptation()
    end
end

function ActivityTaraMain:_SetCommentData(data)
    self._data = data
end

function ActivityTaraMain:_OnActTaraTrainingTravel(isCurHero)
    self:_InitData()
    --是否只刷当前干员
    local hero = self._hero
    if isCurHero and hero then
        local heroId = hero.heroId
        for index, item in ipairs(self._items or {}) do
            if item.heroId == heroId then
                self._hero = item
                break
            end
        end
        self:_InitPanel(self._hero)
        return
    end
    self._defaultIndex = nil
    self:_InitPanel()
end

function ActivityTaraMain:_OnRefreshTaraPanel(activityID, data, index)
    if self._activityID == activityID then
        self._defaultIndex = nil
        self:_InitPanel(data)
    end
end

function ActivityTaraMain:_OpenTaraPanel(activityID, data, index)
    if activityID == self._activityID then
        local InitItemFunc = function(widget, isBool)
            if widget then
                if isBool then
                    widget:InitData(self._activityID, data, self._items)
                    -- widget:SetVisibility(ESlateVisibility.Visible)
                else
                    -- widget:SetVisibility(ESlateVisibility.Collapsed)
                end
            end
        end
        InitItemFunc(self._wtSwitchItem, index == 1)
        InitItemFunc(self._wtSendNeItem, index == 2)
        if data and index then
            Module.ItemDetail:CloseItemDetailPanel()
            self:_InitHandleAdaptation(2)
            self:_SetHandleAdaptation(index == 1 and 2 or 1)
            Module.CommonBar:BindPersistentBackHandler(self._OnBack, self)
            return
        end
        self:_InitHandleAdaptation(3)
        self:_SetHandleAdaptation()
        --播放退出动效
        self._wtSwitchItem:OnStopAnim()
        self._wtSendNeItem:OnStopAnim()
        --恢复主活动ESC
        Config.evtBackBtnChanged:Invoke()
    end
end

function ActivityTaraMain:_OnBack()
    self:_OpenTaraPanel(self._activityID)
end

function ActivityTaraMain:OnShowBegin()
    self:_AddMouseButtonUp()
    self:_AddEventListener()
    self:_InitData()
    self:_InitPanel(self._hero)
end

function ActivityTaraMain:OnHideBegin()
    self:_RemoveMouseButtonUp()
    self:RemoveAllLuaEvent()
    self:_OnUnhovered()
    -- self:_RemoveHandleAdaptation()
    Module.ItemDetail:CloseItemDetailPanel()
end

function ActivityTaraMain:OnClose()
    self._subs = nil
    self._wtHotzone = nil
    if self._wtReddotsIns then
        self._wtReddotsIns:SetReddotVisible(false)
        self._wtReddotsIns = nil
    end
end

function ActivityTaraMain:_OnWaterfallCount()
    if self._list then
        return #self._list
    end
    return 0
end

function ActivityTaraMain:_OnWaterfallWidget(position, itemWidget)
    local index = position
    if self._list and itemWidget then
        local data = self._list[index]
        itemWidget:InitData(data, index, self._defaultIndex, self._hero)
    end
end

function ActivityTaraMain:_AddSubItem(index)
    if self._subs == nil then
        self._subs = {}
    end
    local container = self._wtPanle
    local uiNavId = UIName2ID.ActivityTaraItem3
    if uiNavId and container then
        if index == nil then
            for key, item in pairs(self._subs or {}) do
                if item then
                    item:SetVisibility(ESlateVisibility.Collapsed)
                end
            end
            return
        end
        local item = self._subs[index]
        if item == nil then
            local uiIns, instanceID = Facade.UIManager:AddSubUI(self, uiNavId, container, nil)
            if uiIns then
                item = getfromweak(uiIns)
                if item then
                    self._subs[index] = item
                end
            end
        end
        if item then
            item:SetVisibility(ESlateVisibility.Visible)
            return item
        end
    end
end

--初始化手柄适配🎮
function ActivityTaraMain:_InitHandleAdaptation(defaultIndex)
    if IsHD() then
        if self._navGroup == nil then
            self._navGroup = {}
        end
        local arr = {
            self._wtDFBox,
            self._wtSwitchItem,
        }
        for index, widget in ipairs(arr or {}) do
            if not isvalid(self._navGroup[index]) then
                self._navGroup[index] = WidgetUtil.RegisterNavigationGroup(widget, self, "Hittest")
                self._navGroup[index]:AddNavWidgetToArray(widget)
                self._navGroup[index]:SetScrollRecipient(widget)
            end
        end
        local index = defaultIndex or 1
        if isvalid(self._navGroup[index]) then
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroup[index])
        end
    end
end

--添加手柄适配🎮
function ActivityTaraMain:_AddHandleAdaptation(widget, defaultIndex, isUserFocus)
    if IsHD() then
        defaultIndex = defaultIndex or 1
        for index, navGroup in pairs(self._navGroup or {}) do
            if isvalid(navGroup) and widget and index == defaultIndex then
                navGroup:AddNavWidgetToArray(widget)
                if widget.RefreshAllItems then
                    navGroup:SetScrollRecipient(widget)
                end
                if isUserFocus then
                    WidgetUtil.SetUserFocusToWidget(widget, true)
                end
            end
        end
    end
end

--移除手柄适配🎮
function ActivityTaraMain:_RemoveHandleAdaptation()
    if IsHD() then
        self._navGroup = nil
        WidgetUtil.RemoveNavigationGroup(self)
        WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
        --清空导航栏适配
        Module.CommonBar:RecoverBottomBarInputSummaryList()
    end
end

--设置导航栏手柄🎮
function ActivityTaraMain:_SetHandleAdaptation(handleType)
    if IsHD() then
        local func1 = function()--切换集合
            self:_OpenTaraPanel(self._activityID, {}, 1)
        end
        local func2 = function()--规则详情
            if self._isOpenRule == nil then
                self._isOpenRule = false
            end
            if not self._isOpenRule then
                self:_OnHovered()
            else
                self:_OnUnhovered()
            end
            self._isOpenRule = not self._isOpenRule
        end
        local func3 = function()--评论
            self:_OpenTaraPanel(self._activityID, self._data, 2)
        end
        local func4 = function()--发送
            Config.evtAddTaraHandleAdaptation:Invoke(ETaraHandleType.Send)
        end
        local func5 = function()--刷新
            Config.evtAddTaraHandleAdaptation:Invoke(ETaraHandleType.Refresh)
        end
        --默认开始界面
        local tabBarInputs = {
            {actionName = "ActTara_SwitchingCollections", func = func1, caller = self, bUIOnly = false},
            {actionName = "ActTara_RuleDetails"         , func = func2, caller = self, bUIOnly = false},
        }
        if handleType == 0 then
            tabBarInputs = {
                {actionName = "ActTara_SwitchingCollections", func = func1, caller = self, bUIOnly = false},
                {actionName = "ActTara_Comment"             , func = func3, caller = self, bUIOnly = false},
                {actionName = "ActTara_RuleDetails"         , func = func2, caller = self, bUIOnly = false},
            }
        end
        if handleType == 1 then
            tabBarInputs = {
                {actionName = "ActTara_Send"   , func = func4, caller = self, bUIOnly = false},
                {actionName = "ActTara_Refresh", func = func5, caller = self, bUIOnly = false},
            }
        end
        if handleType == 2 then
            tabBarInputs = {}
        end
        Module.CommonBar:SetBottomBarTempInputSummaryList(tabBarInputs)
    end
end

function ActivityTaraMain:_AddMouseButtonUp()
    local gamelnst = GetGameInstance()
    if self._btnHandle == nil and gamelnst then
        self._btnHandle = UDFMGameHudDelegates.Get(gamelnst).OnHandleMouseButtonUpEvent:Add(self._OnMouseButtonDown, self)
    end
end

function ActivityTaraMain:_RemoveMouseButtonUp()
    local gamelnst = GetGameInstance()
    if self._btnHandle and gamelnst then
        UDFMGameHudDelegates.Get(gamelnst).OnHandleMouseButtonUpEvent:Remove(self._btnHandle)
        self._btnHandle = nil
    end
end

--鼠标点击屏幕事件
function ActivityTaraMain:_OnMouseButtonDown(mouseEvent)
    local btn = self._wtCheck
    if mouseEvent and btn then
        local sceenPos = mouseEvent:GetScreenSpacePosition()
        local geometry = btn:GetCachedGeometry()
    	local isUnder = USlateBlueprintLibrary.IsUnderLocation(geometry, sceenPos)
        if not isUnder then
            self:_OnUnhovered()
        end
    end
end

return ActivityTaraMain