----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class SystemSettingHDSuperResolutionMethodItem
local SystemSettingHDBaseItem = require "DFM.Business.Module.SystemSettingModule.UI.DFHD.Common.SystemSettingHDBaseItem"
local SystemSettingHDSuperResolutionMethodItem = class("SystemSettingHDSuperResolutionMethodItem", SystemSettingHDBaseItem)
local VideoSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.VideoSettingLogicHD"

local CommonSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.CommonSettingLogicHD"
local VRamSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.VRamSettingLogicHD"

-- BEGIN MODIFICATION @ VIRTUOS : UI Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

function SystemSettingHDSuperResolutionMethodItem:Ctor()
    if not CommonSettingLogicHD.IsValidID(self.ID) then
        return
    end
    self.evtOnMethodChanged = LuaEvent:NewIns("evtOnMethodChanged")
end

local function _InitConfig(self)

    --BEGIN MODIFICATION @ VIRTUOS : PS5平台初始化会有Lua报错，而且Console平台不会显示该选项。因此跳过初始化
    if IsConsole() then
        return
    end
    --END MODIFICATION

    local method = CommonSettingLogicHD.GetDataByID(self.ID)
    local options = VideoSettingLogicHD.GetSuperResolutionMethodOptions()
    local index = VideoSettingLogicHD.GetSuperResolutionMethodIndex(method)
    self._curIndex = index
    UIUtil.InitDropDownBox(self._wtDropDown, options, {}, index)
    VRamSettingLogicHD.UpdateDropDownEstimatedVRamUsage(self.ID,self._curIndex,true)
end

function SystemSettingHDSuperResolutionMethodItem:OnOpen()
    SystemSettingHDBaseItem.OnOpen(self)
    if not CommonSettingLogicHD.IsValidID(self.ID) then
        return
    end
    self._wtDropDown = UIUtil.WndDropDownBox(self, "WBP_DFTabGroupDroDownBox_Pc", self._OnOptionChanged)
end

function SystemSettingHDSuperResolutionMethodItem:OnShowBegin()
    SystemSettingHDBaseItem.OnShowBegin(self)
    if not CommonSettingLogicHD.IsValidID(self.ID) then
        return
    end
    if  self._wtDropDown.MenuAnchor then
        self._wtDropDown:Event("PostOnMenuOpenChanged_GamepadUsed",self._OnDropDownBoxOpenStateChanged,self)
    end
    _InitConfig(self)
end

function SystemSettingHDSuperResolutionMethodItem:_OnOptionChanged(curIndex, lastIndex)
    self._curIndex = curIndex
    VRamSettingLogicHD.UpdateDropDownEstimatedVRamUsage(self.ID,self._curIndex,true)
    local method = VideoSettingLogicHD.GetSuperResolutionMethodByIndex(curIndex)
    if  curIndex ~= 0 and method == 0 then
        _InitConfig(self)
        return
    end
    CommonSettingLogicHD.SetDataByID(self.ID, method)
    self.evtOnMethodChanged:Invoke(method)
end

function SystemSettingHDSuperResolutionMethodItem:OnReloadSetting()
    _InitConfig(self)
end

function SystemSettingHDSuperResolutionMethodItem:_OnDropDownBoxOpenStateChanged(bOpen)
    if not bOpen then
        VRamSettingLogicHD.UpdateDropDownEstimatedVRamUsage(self.ID,self._curIndex,true)
    end
end

--BEGIN MODIFICATION @ VIRTUOS :
function SystemSettingHDSuperResolutionMethodItem:OnShow()
    if self._wtDropDown then
        self._wtDropDown:Event("PostOnMenuOpenChanged_GamepadUsed", self._OnDropDownBoxOpenStateChanged, self)
    end
end

function SystemSettingHDSuperResolutionMethodItem:OnClose()
    SystemSettingHDBaseItem.OnClose(self)
    if self._wtDropDown and self._wtDropDown.MenuAnchor then
        self._wtDropDown:RemoveEvent("PostOnMenuOpenChanged_GamepadUsed")
    end
end

function SystemSettingHDSuperResolutionMethodItem:OnHide()
    SystemSettingHDBaseItem.OnHide(self)
    --Remove UI Navigation
    self:_RemoveNavGroup()
    self:_RemoveShortCusts()
end

function SystemSettingHDSuperResolutionMethodItem:_OnDropDownBoxOpenStateChanged(bOpen)
    if bOpen then
        --UI Navigation
        self:_RegisterNavGroup()
        self:_InitShortCuts()
    else
        --Remove UI Navigation
        self:_RemoveNavGroup()
        self:_RemoveShortCusts()
    end
end

function SystemSettingHDSuperResolutionMethodItem:_RegisterNavGroup()
    if not self._NavGroup then
        if self._wtDropDown and self._wtDropDown.ScrollGridBox then
            if not self._NavGroup then
                self._NavGroup = WidgetUtil.RegisterNavigationGroup(self._wtDropDown.ScrollGridBox, self, "Hittest")
            end
        end

        if self._NavGroup then
            self._NavGroup:AddNavWidgetToArray(self._wtDropDown.ScrollGridBox)
            self._NavGroup:MarkIsStackControlGroup();
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup)
        end
    end
end

function SystemSettingHDSuperResolutionMethodItem:_RemoveNavGroup()
    if self._NavGroup then
        self._NavGroup = nil
    end

    WidgetUtil.RemoveNavigationGroup(self)
end

function SystemSettingHDSuperResolutionMethodItem:_InitShortCuts()
    if not self._closeDropDownHandler then
        self._closeDropDownHandler = self:AddInputActionBinding("Back", EInputEvent.IE_Pressed, self._CloseDropDown, self, EDisplayInputActionPriority.UI_Pop)
    end 
end

function SystemSettingHDSuperResolutionMethodItem:_RemoveShortCusts()
    if self._closeDropDownHandler then
        self:RemoveInputActionBinding(self._closeDropDownHandler)
        self._closeDropDownHandler = nil
    end
end

function SystemSettingHDSuperResolutionMethodItem:_CloseDropDown()
    if self._wtDropDown and self._wtDropDown.CloseMenu then
        self._wtDropDown:CloseMenu()
    end
end
--END MODIFICATION

return SystemSettingHDSuperResolutionMethodItem
