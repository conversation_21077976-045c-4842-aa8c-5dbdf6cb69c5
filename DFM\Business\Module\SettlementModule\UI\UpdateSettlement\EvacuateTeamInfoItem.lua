----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSettlement)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class EvacuateTeamInfoItem : LuaUIBaseView
local EvacuateTeamInfoItem = ui("EvacuateTeamInfoItem")

local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local HeroHelperTool = require "DFM.StandaloneLua.BusinessTool.HeroHelperTool"
local UGameplayBlueprintHelper = import "GameplayBlueprintHelper"
local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"
local SystemSettingHDMicrophoneButtonTypeItem = require "DFM.Business.Module.SystemSettingModule.UI.DFHD.Dynamic.SystemSettingHDMicrophoneButtonTypeItem"
local CommonSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.CommonSettingLogicHD"
local SystemSettingLogic = require "DFM.Business.Module.SystemSettingModule.Logic.SystemSettingLogic"

--- BEGIN MODIFICATION @ VIRTUOS
-- 输入设备相关
local UGPInputDelegates = import "GPInputDelegates"
local EGPInputType = import"EGPInputType"
local UGPInputHelper = import("GPInputHelper")
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local UDFMGameHudDelegates = import "DFMGameHudDelegates"

-- Console 查看用户档案
local UDFMPlatformUtils = import "DFMPlatformUtils"
local UDFMGameUrlGenerator = import "DFMGameUrlGenerator"
--- END MODIFICATION

function EvacuateTeamInfoItem:Ctor()
    self:InjectLua()
    -- 通用名片
    ---@type HeroCommonCardBase
    self._wtHeroCommonCard = self:Wnd("HeroCommonCard", HeroCommonCardBase)

    self._wtBtnCP = self:Wnd("Pan_Button1", UIWidgetBase)

    self._wtPreKeyTips = self:Wnd("WBP_CommonKeyIconBox_2", HDKeyIconBox)
    self._wtNxtKeyTips = self:Wnd("WBP_CommonKeyIconBox_3", HDKeyIconBox)
    
    self._wtReportVerticalBox = self:Wnd("DFVerticalBox_0", UIWidgetBase)
    self._wtReportCBO = self:Wnd("ReportBtn", DFCommonButtonOnly)
    self._wtReportCBOKeyTips = self:Wnd("WBP_CommonKeyIconBox", HDKeyIconBox)
    self._wtVoiceReportBtn = self:Wnd("VoiceReportBtn", DFCommonButtonOnly)
    self._wtVoiceReportBtnKeyTips = self:Wnd("WBP_CommonKeyIconBox_1", HDKeyIconBox)

    self._wtVoiceSlider = self:Wnd("WBP_Radio_Slider", UIWidgetBase)
    self._wtVoiceSliderLeftKeyTips = self._wtVoiceSlider:Wnd("WBP_CommonKeyIconBox_2", HDKeyIconBox)
    self._wtVoiceSliderRightKeyTips = self._wtVoiceSlider:Wnd("WBP_CommonKeyIconBox_3", HDKeyIconBox)

    self._wtPlatformProfileCanvas = self:Wnd("DFCanvasPanel_3", UIWidgetBase)
    self._wtPlatformProfileCanvas:Collapsed()
    self._wtPlatformProfile = self:Wnd("wtPlatformProfile", DFCommonButtonOnly)
    self._wtPlatformProfileKeyTips = self:Wnd("WBP_CommonKeyIconBox_7", HDKeyIconBox)

    self._wtPraiseWBCanvas = self:Wnd("DFCanvasPanel_2", UIWidgetBase)
    self._wtPraiseWBCanvas:Collapsed()
    self._wtPraiseWB = self:Wnd("wtPraiseWB", UIWidgetBase)
    self._wtPraiseWBKeyTips = self:Wnd("WBP_CommonKeyIconBox_6", HDKeyIconBox)

    self._wtMakeAppointmentBtnCanvas = self:Wnd("DFCanvasPanel_1", UIWidgetBase)
    self._wtMakeAppointmentBtnCanvas:Collapsed()
    self._wtMakeAppointmentBtn = self:Wnd("MakeAppointment", DFCommonButtonOnly)
    self._wtMakeAppointmentBtnKeyTips = self:Wnd("WBP_CommonKeyIconBox_5", HDKeyIconBox)

    self._wtFriendApplyAddCBOCanvas = self:Wnd("DFCanvasPanel_0", UIWidgetBase)
    self._wtFriendApplyAddCBOCanvas:Collapsed()
    self._wtFriendApplyAddCBO = self:Wnd("AddFriendBtn", DFCommonButtonOnly)
    self._wtFriendApplyAddCBOKeyTips = self:Wnd("WBP_CommonKeyIconBox_4", HDKeyIconBox)

    self._wtPCLocalVoiceInputModeCanvas = self:Wnd("Pan_Button1_1", UIWidgetBase)
    if self._wtPCLocalVoiceInputModeCanvas then
        self._wtPCLocalVoiceInputModeCanvas:Collapsed()
        self._wtPCLocalVoiceInputModeDropDownBox = UIUtil.WndDropDownBox(self, "WBP_DFCommonDropDownBox", self.OnPCLocalVoiceInputModeDropDownBoxChanged)
    end
    
    if IsHD() then
        -- 当前音量调节是否处于加速状态
        self._bisVolumeAccelerating = false
        -- 音量调节加速度因子
        self._volumeAccelerateFactor = 0.05
        -- 音量调节进入加速状态的时间
        self._volumeAccelerateTimeThreshold = 0.2
        -- 当前音量调节的值
        self._currentVolumeAccelerateValue = 0
        -- 音量调节进入加速状态的TimeHandler
        self._volumeAccelerateTimerHandler = nil

        -- 记录音量调节上一次的增长状态
        -- -1：减少 1：增长 0：无变化
        self._volumeLastIncreaseState = 0

        self._wtReportVerticalBox:Collapsed()
        self._wtCommonHover = self:Wnd("WBP_FocusState", UIWidgetBase)
        self._wtCommonHover:Event("OnBPHovered", self._OnHovered, self)
        self._wtCommonHover:Event("OnBPUnhovered", self._OnUnhovered, self)
        self.bIsHovered = false

        if self._wtPCLocalVoiceInputModeDropDownBox then
            local wtKeyIconBox = self._wtPCLocalVoiceInputModeDropDownBox:Wnd("DFCommonCheckButton", UIWidgetBase):Wnd("wtKeyIcon", HDKeyIconBox)
            if wtKeyIconBox then
                wtKeyIconBox:SetOnlyDisplayOnGamepad(true)
                wtKeyIconBox:InitByDisplayInputActionName("Common_LeftStickClick_Gamepad", true, 0, true)
            end
        end

    end
end

--- BEGIN MODIFICATION @ VIRTUOS
function EvacuateTeamInfoItem:OnShowBegin()
    -- 绑定多输入设备切换事件
    if not IsHD() then
        return
    end

    self._OnTeamInfoInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._OnInputTypeChanged, self))
    local curInpurtType = UGPInputHelper.GetCurrentInputType(GetGameInstance())
    self:_OnInputTypeChanged(curInpurtType)
end

function EvacuateTeamInfoItem:OnHideBegin()
    -- 解绑多输入设备切换事件
    if not IsHD() then
        return
    end
    
    if self._OnTeamInfoInputTypeChangedHandle then
        UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._OnTeamInfoInputTypeChangedHandle)
        self._OnTeamInfoInputTypeChangedHandle = nil
    end
end

function EvacuateTeamInfoItem:_OnInputTypeChanged(curInpurtType)
    if not IsHD() then
        return
    end

    if curInpurtType == EGPInputType.Gamepad then
        if self._wtPCLocalVoiceInputModeDropDownBox.MenuAnchor then
            self._wtPCLocalVoiceInputModeDropDownBox:Event("PostOnMenuOpenChanged_GamepadUsed",self._OnDropDownBoxOpenStateChanged,self)
        end
    else
        if self._wtPCLocalVoiceInputModeDropDownBox and self._wtPCLocalVoiceInputModeDropDownBox.MenuAnchor then
            self._wtPCLocalVoiceInputModeDropDownBox:RemoveEvent("PostOnMenuOpenChanged_GamepadUsed")
        end
        if self._ProgressButton then
            self._ProgressButton:RemoveEvent("OnFocusReceivedEvent")
            self._ProgressButton:RemoveEvent("OnFocusLostEvent")
        end
    end

    self:_UpdateGamepadInput()

    if self.optionsInfo and #self.optionsInfo > 0 then
        if #self.optionsInfo > 3 then
            self.optionKeyStr = options[1]
            self.optionKeyStrGamepad = options[4]
            table.remove(self.optionsInfo, 4)
        end
    
        if curInpurtType == EGPInputType.Gamepad then
            if self.optionKeyStr and self.optionKeyStrGamepad then
                self.optionsInfo[1] = self.optionKeyStrGamepad
                if self._wtVoiceSlider and self._wtVoiceSlider:IsVisible() then
                    self._ProgressBar = self._wtVoiceSlider:Wnd("DFSlider_96", UIWidgetBase)
                    if self._ProgressBar then
                        self._ProgressBar:SetRequiresControllerLock(false)
                    end
                end
            end
        end
    
        UIUtil.InitDropDownBox(self._wtPCLocalVoiceInputModeDropDownBox, self.optionsInfo, {},  self._initIdx)
        if self._wtPCLocalVoiceInputModeCanvas:IsVisible() then
            if curInpurtType == EGPInputType.Gamepad then
                if not self._dropDownBoxOpenHandle then 
                    self._dropDownBoxOpenHandle = self:AddInputActionBinding("Common_LeftStickClick_Gamepad", EInputEvent.IE_Pressed, self._OnOpenCloseDropDownBox, self, EDisplayInputActionPriority.UI_Pop)
                end
            else
                if self._dropDownBoxOpenHandle then 
                    self:RemoveInputActionBinding(self._dropDownBoxOpenHandle)
                    self._dropDownBoxOpenHandle = nil
                end
            end
        end
    end
    
end

function EvacuateTeamInfoItem:_OnHovered()
    if not self:IsLocalPlayer() then
        self._wtReportVerticalBox:Visible()
    end
    self.bIsHovered = true
    self:_UpdateGamepadInput()
end

function EvacuateTeamInfoItem:_OnUnhovered()
    self._wtReportVerticalBox:Collapsed()
    self.bIsHovered = false
    self:_UpdateGamepadInput()
end

function EvacuateTeamInfoItem:IsLocalPlayer()
    if self._info ~= nil then
        if self._info.basicInfo ~= nil then
            if self._info.basicInfo.player_id ~= nil then
                local localPlayerId = Facade.GameFlowManager:GetPlayerUin()
                if self._info.basicInfo.player_id == localPlayerId then
                    return true
                else
                    return false
                end
            end
        end
    end

    return false
end

function EvacuateTeamInfoItem:_UpdateGamepadInput()
    local curInpurtType = UGPInputHelper.GetCurrentInputType(GetGameInstance())
    if curInpurtType == EGPInputType.Gamepad and self.bIsHovered then
        self._wtPreKeyTips:SelfHitTestInvisible()
        self._wtNxtKeyTips:SelfHitTestInvisible()
        self._wtReportCBOKeyTips:SelfHitTestInvisible()
        self._wtVoiceReportBtnKeyTips:SelfHitTestInvisible()
        self._wtVoiceSliderLeftKeyTips:SelfHitTestInvisible()
        self._wtVoiceSliderRightKeyTips:SelfHitTestInvisible()
        self._wtPlatformProfileKeyTips:SelfHitTestInvisible()
        self._wtPraiseWBKeyTips:SelfHitTestInvisible()
        self._wtMakeAppointmentBtnKeyTips:SelfHitTestInvisible()
        self._wtFriendApplyAddCBOKeyTips:SelfHitTestInvisible()

        if self._wtBtnCP:IsVisible() then
            if not self._sliderLeftHandle then 
                self._sliderLeftHandle = self:AddInputActionBinding("Common_DpadLeft_Gamepad", EInputEvent.IE_Pressed, self._OnSliderLeft, self, EDisplayInputActionPriority.UI_Pop)
            end
            if not self._sliderRightHandle then 
                self._sliderRightHandle = self:AddInputActionBinding("Common_DpadRight_Gamepad", EInputEvent.IE_Pressed, self._OnSliderRight, self, EDisplayInputActionPriority.UI_Pop)
            end
        end

        if self.Type == 0 or self.Type == 1 then
            if not self._voiceReportHandle then
                self._voiceReportHandle = self:AddInputActionBinding("CardS1_VoiceReport", EInputEvent.IE_Pressed, self._OnClickedVoiceReportBtn, self, EDisplayInputActionPriority.UI_Pop)
            end
        elseif self.Type == 2 or self.Type == 3 then
            if not self._praiseHandle then
                self._praiseHandle = self:AddInputActionBinding("CardS1_Praise", EInputEvent.IE_Pressed, self._OnClickedPraiseBtn, self, EDisplayInputActionPriority.UI_Pop)
            end
        else
            loginfo("EvacuateTeamInfoItem:_UpdateGamepadInput(), Wrong type")
        end

        if IsConsole() then
            if not self._consoleProfileHandle then
                self._consoleProfileHandle = self:AddInputActionBinding("CardS1_ConsoleProfile", EInputEvent.IE_Pressed, self._OnClickedPlatformProfileBtn, self, EDisplayInputActionPriority.UI_Pop)
            end
        end

        if not self._reportHandle then
            self._reportHandle = self:AddInputActionBinding("CardS1_Report", EInputEvent.IE_Pressed, self._OnClickedReportBtn, self, EDisplayInputActionPriority.UI_Pop)
        end
        if not self._teamAppointmentHandle then
            self._teamAppointmentHandle = self:AddInputActionBinding("CardS1_TeamAppointment", EInputEvent.IE_Pressed, self._OnClickedMakeAppointmentBtn, self, EDisplayInputActionPriority.UI_Pop)
        end
        if not self._addFriendHandle then
            self._addFriendHandle = self:AddInputActionBinding("CardS1_AddFriend", EInputEvent.IE_Pressed, self._OnClickedFriendApplyAddBtn, self, EDisplayInputActionPriority.UI_Pop)
        end

    else
        self._wtPreKeyTips:Collapsed()
        self._wtNxtKeyTips:Collapsed()
        self._wtReportCBOKeyTips:Collapsed()
        self._wtVoiceReportBtnKeyTips:Collapsed()
        self._wtVoiceSliderLeftKeyTips:Collapsed()
        self._wtVoiceSliderRightKeyTips:Collapsed()
        self._wtPlatformProfileKeyTips:Collapsed()
        self._wtPraiseWBKeyTips:Collapsed()
        self._wtMakeAppointmentBtnKeyTips:Collapsed()
        self._wtFriendApplyAddCBOKeyTips:Collapsed()

        if self._reportHandle then 
            self:RemoveInputActionBinding(self._reportHandle)
            self._reportHandle = nil
        end
        if self._voiceReportHandle then 
            self:RemoveInputActionBinding(self._voiceReportHandle)
            self._voiceReportHandle = nil
        end
        if self._sliderLeftHandle then 
            self:RemoveInputActionBinding(self._sliderLeftHandle)
            self._sliderLeftHandle = nil
        end
        if self._sliderRightHandle then 
            self:RemoveInputActionBinding(self._sliderRightHandle)
            self._sliderRightHandle = nil
        end
        if self._consoleProfileHandle then 
            self:RemoveInputActionBinding(self._consoleProfileHandle)
            self._consoleProfileHandle = nil
        end
        if self._praiseHandle then 
            self:RemoveInputActionBinding(self._praiseHandle)
            self._praiseHandle = nil
        end
        if self._teamAppointmentHandle then 
            self:RemoveInputActionBinding(self._teamAppointmentHandle)
            self._teamAppointmentHandle = nil
        end
        if self._addFriendHandle then 
            self:RemoveInputActionBinding(self._addFriendHandle)
            self._addFriendHandle = nil
        end

    end

end

function EvacuateTeamInfoItem:_OnOpenCloseDropDownBox()
    if self._wtPCLocalVoiceInputModeDropDownBox:IsMenuShow() then 
        self._wtPCLocalVoiceInputModeDropDownBox:CloseMenu()
        self:SetFocus()
    else
        self._wtPCLocalVoiceInputModeDropDownBox:OpenMenu()
    end
end

local voiceSliderStep = 10
function EvacuateTeamInfoItem:_OnSliderLeft()
    local new = self._lastConfirmedValue - voiceSliderStep
    if new < 0 then new = 0 end
    if new > 150 then new = 150 end
    self:_OnConfirmValue(new)
end
function EvacuateTeamInfoItem:_OnSliderRight()
    local new = self._lastConfirmedValue + voiceSliderStep
    if new < 0 then new = 0 end
    if new > 150 then new = 150 end
    self:_OnConfirmValue(new)
end

function EvacuateTeamInfoItem:_OnDropDownBoxOpenStateChanged(bOpen)
    if not IsHD() then
        return
    end

    if bOpen then
        --导航
        if not self._navGroup then
            if self._wtPCLocalVoiceInputModeDropDownBox and self._wtPCLocalVoiceInputModeDropDownBox.ScrollGridBox then
                self._navGroup = WidgetUtil.RegisterNavigationGroup(self._wtPCLocalVoiceInputModeDropDownBox.ScrollGridBox, self, "Hittest")
            end

            if self._navGroup then
                self._navGroup:AddNavWidgetToArray(self._wtPCLocalVoiceInputModeDropDownBox.ScrollGridBox)
                self._navGroup:MarkIsStackControlGroup()
                WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroup)
                WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self._wtPCLocalVoiceInputModeDropDownBox)
            end
        end

        --输入
        if not self._closeDropDownHandler then
            self._closeDropDownHandler = self:AddInputActionBinding("Back", EInputEvent.IE_Pressed, self._CloseDropDown, self, EDisplayInputActionPriority.UI_Pop)
        end 
    else
        if self._navGroup then
            self._navGroup = nil
        end

        WidgetUtil.RemoveNavigationGroup(self)
        WidgetUtil.DisableDynamicNavConfigsbyWidget(self._wtPCLocalVoiceInputModeDropDownBox)
        if self._closeDropDownHandler then
            self:RemoveInputActionBinding(self._closeDropDownHandler)
            self._closeDropDownHandler = nil
        end
    end
end

function EvacuateTeamInfoItem:_CloseDropDown()
    if not IsHD() then
        return
    end

    if self._wtPCLocalVoiceInputModeDropDownBox and self._wtPCLocalVoiceInputModeDropDownBox.CloseMenu then
        self._wtPCLocalVoiceInputModeDropDownBox:CloseMenu()
    end
end
--- END MODIFICATION

function EvacuateTeamInfoItem:SetInfo(info)
    self._info = info

    self:SetType(2)

    self._wtHeroCommonCard:SetSelfCardImgPath(info.heroId)
    if info.accessories and #info.accessories ~= 0 then
        self._wtHeroCommonCard:SetCardInfoByAccessories(info.accessories)
    end
    self._wtHeroCommonCard:SetPlayerName(info.basicInfo.game_nick)

    -- azhengzheng:这里改造下，获取titleinfo
    local titleInfo = self:_GetTitleInfo(info.basicInfo.player_id)

    if titleInfo then
        self._wtHeroCommonCard:UpdateTitleInfo(nil, titleInfo.PlayerTitle, titleInfo.RankAdcode, titleInfo.RankNo)
    end

    -- local titleId = self:_GetTitleId(info.basicInfo.player_id)
    -- if titleId ~= nil then
    --     -- self._wtHeroCommonCard:SetTitleInfoAndNameByTitleId(titleId)
    --     self._wtHeroCommonCard:UpdateTitleInfo(nil, titleId, adCode, ranking)
    -- end
    -- self._wtHeroCommonCard:SetTitleName(string.format(Module.Settlement.Config.Loc.SeasonLvName, info.basicInfo.level))
    self._wtHeroCommonCard:SetGameState(self._info.gameStatusType, self._info.resultType)
    self._wtHeroCommonCard:SetStatistics(self._info.carryOutProfitPrice, self._info.killCount, self._info.rescueCount, self._info.resurgenceCount)
    loginfo("wtHeroCommonCard:SetStatistics  ", self._info.carryOutProfitPrice, self._info.killCount, self._info.rescueCount, self._info.resurgenceCount)
    self:_SetBtnCPState()

    --- BEGIN MODIFICATION @ VIRTUOS
    if IsConsole() and info.basicInfo then
        self:_SetPlatformIcon(info.basicInfo.plat_id)
    end

    if IsPS5() then
        if info.basicInfo.player_id ~= nil and info.basicInfo.plat_id == PlatIDType.Plat_Playstation then
            local onlineId = Module.Social:GetPS5OnlineIdByUID(info.basicInfo.player_id)
            if onlineId and not string.isempty(onlineId) then
                self._wtHeroCommonCard:SetPlayerName(onlineId)
            end
        end
    end
    --- END MODIFICATION
end

function EvacuateTeamInfoItem:_GetTitleInfo(player_id)
    local titleInfo = nil
    local controller = UGameplayBlueprintHelper.GetLocalGPPlayerController(self)
    if isvalid(controller) then
        local playerState = controller.PlayerState
        if isvalid(playerState) then
            if player_id == playerState.Uin then
                titleInfo = playerState:GetCurPlayerTitleInfo()
            else
                local memberInfoList = playerState.MemberInfoList
                local num = memberInfoList:Num()
                for index = 0, num - 1 do
                    local memberInfo = memberInfoList:Get(index)
                    if isvalid(memberInfo) then
                        if memberInfo.PlayerUin == player_id then
                            -- if isinvalid(memberInfo.PS) then
                            --     loginfo("EvacuateTeamInfoItem:_SetTitleInfo memberInfo.PS is invalid")
                            -- end
                            -- titleInfo = memberInfo.PS:GetCurPlayerTitleInfo()

                            -- azhengzheng:上面谁写的，反思一下（https://tapd.woa.com/tapd_fe/20421949/bug/detail/1120421949142835963）
                            if isinvalid(memberInfo.PS) then
                                loginfo("EvacuateTeamInfoItem:_SetTitleInfo memberInfo.PS is invalid")
                            else
                                titleInfo = memberInfo.PS:GetCurPlayerTitleInfo()
                            end
                        end
                    end
                end
            end
        else
            loginfo("EvacuateTeamInfoItem:_SetTitleInfo playerState is invalid")
        end
    else
        loginfo("EvacuateTeamInfoItem:_SetTitleInfo controller is invalid")
    end

    return titleInfo
end

function EvacuateTeamInfoItem:_SetBtnCPState()
    if not self._wtBtnCP then
        return
    end

    if self._info.idx == 1 then
        self._wtBtnCP:Collapsed()
        self._wtHeroCommonCard:SetPlayerNameTextColor("C006")
    else
        self._wtBtnCP:Visible()

        self:_SetPraiseBtn()

        self:_SetReportBtn()

        self:_SetFriendApplyAddBtn()

        --- BEGIN MODIFICATION @ VIRTUOS: Console 查看用户档案
        if IsConsole() and self._info and self._info.basicInfo then
            self:_SetPlatformProfileBtn(self._info.basicInfo.player_id, self._info.basicInfo.plat_id)
        end
        --- END MODIFICATION  
    end
end

function EvacuateTeamInfoItem:_SetPraiseBtn()
    if not self._wtPraiseWB then
        return
    end
    self._wtPraiseWBCanvas:SelfHitTestInvisible()

    self._wtPraiseBtn = self._wtPraiseWB:Wnd("wtPraiseBtn", UIButton)

    if self._wtPraiseBtn then
        self._wtPraiseBtn:RemoveEvent("OnClicked")
        self._wtPraiseBtn:Event("OnClicked", self._OnClickedPraiseBtn, self)
    end
end

function EvacuateTeamInfoItem:_OnClickedPraiseBtn()
    if self._info.isAI then
        local res = {}
        res.result = 0
        self:_OnPlayerInfoPraise(res)
        return
    end

    local req = pb.CSPlayerInfoPraiseReq:New()
    req.be_praised_player_id = self._info.basicInfo.player_id
    req.praise_type = 0
    req:Request(CreateCallBack(self._OnPlayerInfoPraise, self))
end

function EvacuateTeamInfoItem:_OnPlayerInfoPraise(res)
    if res.result == 0 then
        self._wtPraiseBtn:SetIsEnabled(false)
        Module.CommonTips:ShowSimpleTip(Module.Settlement.Config.Loc.FunctionIsMakingTXT)

        if self._wtPraiseWB.SetStyle then
            self._wtPraiseWB:SetStyle(1)
        end
    end
end

function EvacuateTeamInfoItem:_SetVoiceReportBtn()
    if not self._wtVoiceReportBtn then
        return
    end

    self._wtVoiceReportBtn:RemoveEvent("OnClicked")
    self._wtVoiceReportBtn:Event("OnClicked", self._OnClickedVoiceReportBtn, self)
end

function EvacuateTeamInfoItem:_OnClickedVoiceReportBtn()
    Module.GVoice:ReportPlayer({self._info.basicInfo.player_id}, "")
end

function EvacuateTeamInfoItem:_SetReportBtn()
    if not self._wtReportCBO then
        return
    end

    self._wtReportCBO:RemoveEvent("OnClicked")
    self._wtReportCBO:Event("OnClicked", self._OnClickedReportBtn, self)
end

function EvacuateTeamInfoItem:_OnClickedReportBtn()
    local playerInfo = {
        player_id = self._info.basicInfo.player_id,
        nick_name = self._info.basicInfo.game_nick,
        player_type = Module.Report.Config.ReportPlayerType.Teammate,
    }

    Module.Report:ReportPlayer(playerInfo, 3, self._info.roomId, nil, self._info.startTime, ReportEntrance.ReportSettlementPVP, Module.Report.Config.EReportMode.ReportSol, CreateCallBack(self._OnTssReport, self))
end

function EvacuateTeamInfoItem:_OnTssReport()
    self._wtReportCBO:SetIsEnabled(false)
    if self.PS then
        self.PS.ClientbReportedByLocal = true
    end
end

function EvacuateTeamInfoItem:_SetFriendApplyAddBtn()
    if not self._wtFriendApplyAddCBO then
        return
    end

    local isFriend = Module.Friend:CheckIsFriend(self._info.basicInfo.player_id)

    if isFriend then
        -- azhengzheng:long 1改成隐藏
        self._wtFriendApplyAddCBOCanvas:Collapsed()
    else
        self._wtFriendApplyAddCBOCanvas:SelfHitTestInvisible()
        self._wtFriendApplyAddCBO:RemoveEvent("OnClicked")
        self._wtFriendApplyAddCBO:Event("OnClicked", self._OnClickedFriendApplyAddBtn, self)
    end
end

function EvacuateTeamInfoItem:_OnClickedFriendApplyAddBtn()
    Module.Friend:AddFriend(self._info.basicInfo.player_id, FriendApplySource.SolSettlementApply, CreateCallBack(self._OnFriendApplyAdd, self), self._info.roomId, nil, self._info.basicInfo.plat_id)
end

function EvacuateTeamInfoItem:_OnFriendApplyAdd()
    self._wtFriendApplyAddCBO:SetIsEnabled(false)
end

function EvacuateTeamInfoItem:_SetMakeAppointmentBtn()
    if not self._wtMakeAppointmentBtn then
        return
    end
    self._wtMakeAppointmentBtnCanvas:SelfHitTestInvisible()

    self._wtMakeAppointmentBtn:Event("OnClicked", self._OnClickedMakeAppointmentBtn, self)

end

function EvacuateTeamInfoItem:_OnClickedMakeAppointmentBtn()
    -- TODO@georgecgong 预约野人组队

end

function EvacuateTeamInfoItem:_OnMakeAppointment(result)
    -- TODO@georgecgong 预约野人组队服务器响应
    
end

-- type: 0-显示可点击 1-显示但不可用 2-不显示
function EvacuateTeamInfoItem:_SetMakeAppointmentBtnShowType(type)
    if type == 0 then
        self._wtMakeAppointmentBtn:SetBtnEnable(true)
        self._wtMakeAppointmentBtnCanvas:SelfHitTestInvisible()
    elseif type == 1 then
        self._wtMakeAppointmentBtn:SetBtnEnable(false)
        self._wtMakeAppointmentBtnCanvas:SelfHitTestInvisible()
    elseif type == 2 then
        self._wtMakeAppointmentBtnCanvas:Collapsed()
    end
end

function EvacuateTeamInfoItem:PlayItemAnimation()
    self:PlayAnimation(self.WBP_Common_CardS1_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    if IsHD() then
        self._wtHeroCommonCard:PlayWidgetAnim(self._wtHeroCommonCard.WBP_Common_CardS2_in)
    else
        self._wtHeroCommonCard:PlayWidgetAnim(self._wtHeroCommonCard.WBP_Common_CardS2_in_1)
    end
end

function EvacuateTeamInfoItem:StopItemAnimation()
    self:StopAnimation(self.WBP_Common_CardS1_in)
    if IsHD() then
        self._wtHeroCommonCard:StopAnimation(self._wtHeroCommonCard.WBP_Common_CardS2_in)
    else
        self._wtHeroCommonCard:StopAnimation(self._wtHeroCommonCard.WBP_Common_CardS2_in_1)
    end
end

-- Long3 局内队伍信息复用，之后有空整理下吧，好乱 => She1 2.25 重构后，除语音、社交按钮外的信息，均收口到基础名片HeroCommonCardBase中
function EvacuateTeamInfoItem:Imp_InitCoorPlayer(playerState)
    if not isvalid(playerState) then
        return
    end
    self.PS = playerState
    if playerState.ClientbReportedByLocal == true then
        self._wtReportCBO:SetIsEnabled(false)
    end
    self._wtBtnCP:Collapsed()
    self:PlayItemAnimation()

    self._info = {}
    self._info.basicInfo = {}
    self._info.basicInfo.player_id = playerState.Uin
    --- BEGIN MODIFICATION @ VIRTUOS
    self._info.basicInfo.plat_id = playerState:GetPlatformID()
    --- END MODIFICATION
    self._info.basicInfo.game_nick = playerState:GetPlayerName()
    self._info.roomId = Server.MatchServer:GetDsRoomId()
    local gameState = InGameController:Get():GetGameState()
    if isvalid(gameState) then
        self._info.startTime = gameState.MatchInfo.MatchStartTimestamp
    end

    if self:IsLocalPlayer() then
        self._wtReportVerticalBox:Collapsed()
    end

    --- BEGIN MODIFICATION @ VIRTUOS
    if IsConsole() then
        self:_SetPlatformProfileBtn(playerState.Uin, playerState:GetPlatformID())
    end
    --- END MODIFICATION
end

function EvacuateTeamInfoItem:Imp_InitPCLocalVoiceInputModeDropDownBox()
    -- Local Drop
    self._wtPCLocalVoiceInputModeCanvas:Visible()
    -- Teammate Slider & Btn
    self._wtBtnCP:Collapsed()

    local SettingID = "MicrophoneButtonType"
    local options = SystemSettingHDMicrophoneButtonTypeItem.GetMicrophoneButtonTypeOptions(self, SettingID)

    --BEGIN MODIFICATION @ VIRTUOS : 给DropDownBox绑定OnMenuOpenChanged
    if IsHD() then
        self.optionsInfo = options
        self._initIdx = CommonSettingLogicHD.GetDataByID(SettingID)
        local curInpurtType = UGPInputHelper.GetCurrentInputType(GetGameInstance())
        self:_OnInputTypeChanged(curInpurtType)
    else
        local initIdx = CommonSettingLogicHD.GetDataByID(SettingID)
        UIUtil.InitDropDownBox(self._wtPCLocalVoiceInputModeDropDownBox, options, {},  initIdx)
    end
    --END MODIFICATION
end

function EvacuateTeamInfoItem:OnPCLocalVoiceInputModeDropDownBoxChanged(curIndex)
    local SettingID = "MicrophoneButtonType"
    CommonSettingLogicHD.SetDataByID(SettingID, curIndex)
end

function EvacuateTeamInfoItem:Imp_InitTeammateVoiceSliderAndButtons()
    -- Local Drop
    -- self._wtPCLocalVoiceInputModeCanvas:Collapsed() 默认collapsed，手游剔除
    -- Teammate Slider & Btn
    self._wtBtnCP:Visible()

    -- Slider
    self._constrainData = {
        min = 0,
        max = 150, -- She2 6.11 设置什么时候改成150了（而且还仅是ui放大到1.5倍）
        decimalPrecision = 0
    }
    self._wtVoiceSlider.DefaultValue = 100
    self._wtVoiceSlider:BP_SetSliderMinMax(self._constrainData.min, self._constrainData.max)
    self._wtVoiceSlider:Event("OnSliderValueChanged", self._OnValueChanged, self)
    self._wtVoiceSlider:Event("OnConfirmValue", self._OnConfirmValue, self)

    local value = self:OnGetValue()
    self._lastConfirmedValue = SystemSettingLogic._RoundValueByPrecision(value, self._constrainData.decimalPrecision)
    self._wtVoiceSlider:BP_SetSliderValue(value)
    self._wtVoiceSlider:BP_SetText(string.format("%d", value))
    -- Btn
    self:_SetVoiceReportBtn()
    self:_SetReportBtn()
    self:_SetFriendApplyAddBtn()
    self:_SetMakeAppointmentBtn()
    --- BEGIN MODIFICATION @ VIRTUOS: Console 查看用户档案
    if IsConsole() and self._info and self._info.basicInfo then
        self:_SetPlatformProfileBtn(self._info.basicInfo.player_id, self._info.basicInfo.plat_id)
    end
    --- END MODIFICATION
end

function EvacuateTeamInfoItem:Imp_CallLuaShowHide(bShow)
    if bShow then
        self:Show()
    else
        self:Hide()
    end
end

function EvacuateTeamInfoItem:OnSetValue(value)
    if Facade.ModuleManager:IsModuleValid("GVoice") then
        -- She2 6.11 设置什么时候改成150了（而且还仅是ui放大到1.5倍）
        value = math.floor(value / 1.5)
        Module.GVoice:SetRoomMemberVolume(self._info.basicInfo.player_id, value)
    end
end

function EvacuateTeamInfoItem:OnGetValue()
    if Facade.ModuleManager:IsModuleValid("GVoice") then
        local value = Module.GVoice:GetRoomMemberVolume(self._info.basicInfo.player_id)
        return value
    end
end

-- Slider控件逻辑 Begin
function EvacuateTeamInfoItem:_OnValueChanged(value)
    self._lastConfirmedValue = SystemSettingLogic._RoundValueByPrecision(value, self._constrainData.decimalPrecision)
    self._wtVoiceSlider:BP_SetText(string.format("%d", self._lastConfirmedValue))
    self:OnSetValue(self._lastConfirmedValue)
end

function EvacuateTeamInfoItem:_OnConfirmValue(value)
    self._lastConfirmedValue = SystemSettingLogic._RoundValueByPrecision(value, self._constrainData.decimalPrecision)
    self._wtVoiceSlider:BP_SetSliderValue(self._lastConfirmedValue)
    self._wtVoiceSlider:BP_SetText(string.format("%d", self._lastConfirmedValue))
    self:OnSetValue(self._lastConfirmedValue)
end
-- Slider控件逻辑 End

--- BEGIN MODIFICATION @ VIRTUOS: Console 查看用户档案
function EvacuateTeamInfoItem:_SetPlatformProfileBtn(playerID, platID)
    if not IsConsole() or not self._wtPlatformProfile then
        return
    end

    if self._profileReqPlayerID == playerID then
        return
    else
        self._profileReqPlayerID = playerID
    end

    if not playerID or platID ~= Server.AccountServer:GetPlatIdType() then
        self:_ResetPlayerPlatformProfile()
        return
    end

    local OnReqQueryRes = function(HttpRequest, HttpResponse, bSucceeded)
        -- 处理点击太快，收到回调时页面已经被关闭的bug
        if not self:IsValid() then
            return
        end

        if self._profileReqPlayerID ~= playerID then
            return
        end
        
        local queryRes = nil
        local UDFMGameUrlGeneratorIns = UDFMGameUrlGenerator.Get(GetGameInstance())
        if bSucceeded and UDFMGameUrlGeneratorIns then
            queryRes = UDFMGameUrlGeneratorIns:GenerateQueryRes(HttpResponse:GetContentAsString())
        end
        
        if queryRes and queryRes.bHasUID then
            self._platformId = queryRes.UID
            self._wtPlatformProfileCanvas:Visible()
            
            self._wtPlatformProfile:RemoveEvent("OnClicked")
            self._wtPlatformProfile:Event("OnClicked", self._OnClickedPlatformProfileBtn, self)
            
            -- 显示当前平台 Profile Icon
            local success, platData
            success, platData = UDFMPlatformUtils.GetPlatformData(platID, platData)
            if success then
                self._wtPlatformProfile:SetImageSoftPath(platData.ViewProfileIcon, platData.ViewProfileIcon)
            end
        else
            self:_ResetPlayerPlatformProfile()
        end
    end

    Server.SocialServer:ReqQueryUID(ULuautils.GetOverInt64String(playerID), OnReqQueryRes)
end

function EvacuateTeamInfoItem:_ResetPlayerPlatformProfile()
        self._profileReqPlayerID = nil
        self._platformId = nil
        self._wtPlatformProfileCanvas:Collapsed()
        self._wtPlatformProfile:RemoveEvent("OnClicked")
end

-- 设置玩家ID后的平台标识
function EvacuateTeamInfoItem:_SetPlatformIcon(platID)
    if not self._wtHeroCommonCard then
        return
    end

    if self._info and self._info.isAI and Server.AccountServer:IsCrossPlat() then
        -- AI默认设置为PC平台
        self._wtHeroCommonCard:SetPlayerPlatformIDType(PlatIDType.Plat_PC)
    else
        self._wtHeroCommonCard:SetPlayerPlatformIDType(platID)
    end

    if not Server.AccountServer:IsCrossPlat() then
        logerror("EvacuateTeamInfoItem:_SetPlatformIcon", "not cross plat")
        self._wtHeroCommonCard:SetPlayerPlatformIDType(nil)
    end
end

function EvacuateTeamInfoItem:_OnClickedPlatformProfileBtn()
    if self._platformId ~= nil then 
        UDFMPlatformUtils.ShowPlatformProfileUI(self._platformId)
    elseif Server.AccountServer:IsOpenFakePlat() then
        -- 显示错误信息
        Module.CommonTips:ShowSimpleTip(Module.CommonTips.Config.Loc.CrossPlatError)
    end
end
--- END MODIFICATION

return EvacuateTeamInfoItem