----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCommonBar)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class CommonBarModule: ModuleBase
local CommonBarModule = class("CommonBarModule", require("DFM.YxFramework.Managers.Module.ModuleBase"))
local CommonBarViewLogic = require "DFM.Business.Module.CommonBarModule.Logic.CommonBarViewLogic"
local CommonBarRegLogic = require "DFM.Business.Module.CommonBarModule.Logic.CommonBarRegLogic"
local CommonBarPureLogic = require "DFM.Business.Module.CommonBarModule.Logic.CommonBarPureLogic"
local CommonBarHDTabGroupLogic = require "DFM.Business.Module.CommonBarModule.Logic.CommonBarHDTabGroupLogic2"
local CommonBarLoadLogic = require "DFM.Business.Module.CommonBarModule.Logic.CommonBarLoadLogic"
local CommonBarInputLogic = require "DFM.Business.Module.CommonBarModule.Logic.CommonBarInputLogic"
local CommonBarUINavigationLogic = require_hd "DFM.Business.Module.CommonBarModule.Logic.CommonBarUINavigationLogic"
local CommonBarEventLogic = require "DFM.Business.Module.CommonBarModule.Logic.CommonBarEventLogic"
local CommonBarUnlockLogic = require "DFM.Business.Module.CommonBarModule.Logic.CommonBarUnlockLogic"
local ThemeHelperTool = require "DFM.StandaloneLua.BusinessTool.ThemeHelperTool"

local UKismetSystemLibrary = import "KismetSystemLibrary"
local EQuitPreference = import "EQuitPreference"

function CommonBarModule:Ctor()
    -- self.isUIDInfoViewCreated = false
    self.commonUIDInfoViewHandle = nil
end

--------------------------------------------------------------------------
--- 生命周期
--------------------------------------------------------------------------
---declare values
function CommonBarModule:OnInitModule()
end

---loadBPRes、TextureRes、private table
function CommonBarModule:OnLoadModule()
    Facade.UIManager:PreloadPermanentUIAsset(UIName2ID.InputSummaryItemHD)
    Facade.UIManager:PreloadPermanentUIAsset(UIName2ID.InputSummaryItemV2HD)
end

function CommonBarModule:OnDestroyModule()
    self:CloseTopBar()
    if DFHD_LUA == 1 then
        self:CloseBottomBar()
        CommonBarHDTabGroupLogic.Reset()
    end
    self:CloseCommonBarBasisView()
end

---@param gameFlowType EGameFlowStageType
function CommonBarModule:OnGameFlowChangeLeave(gameFlowType)
    -- if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then
    self:CloseTopBar()
    if DFHD_LUA == 1 then
        self:CloseBottomBar()
        CommonBarHDTabGroupLogic.Reset()
    end
    -- self:CloseCommonBarBasisView()
    -- end

    -- if not self:IsToStableLobbyGameFlow(gameFlowType) and self.commonUIDInfoViewHandle then
    --     Facade.UIManager:closeUIByHandle(self.commonUIDInfoViewHandle)
    --     self.commonUIDInfoViewHandle = nil
    -- end

    if self.commonUIDInfoViewHandle then
        Facade.UIManager:CloseUIByHandle(self.commonUIDInfoViewHandle)
        self.commonUIDInfoViewHandle = nil
    end
end

---@param gameFlowType EGameFlowStageType
function CommonBarModule:OnGameFlowChangeEnter(gameFlowType)
    if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then
        CommonBarEventLogic.AddLobbyListeners()

        if not GF_CheckIsLoadingFlowType(gameFlowType) then
            if not Facade.UIManager:IsFrameRootAvailable() then
                Facade.UIManager:InitUILayers()
            end
            -- self:ShowCommonBarBasisView()
        end
    end

    -- if self:IsToStableLobbyGameFlow(gameFlowType) then
    --     if not self.isUIDInfoViewCreated then
    --         self.commonUIDInfoViewHandle = Facade.UIManager:AsyncShowUI(UIName2ID.CommonUIDInfoView)
    --         self.isUIDInfoViewCreated = true
    --     end
    -- else
    --     if self.isUIDInfoViewCreated then
    --         Facade.UIManager:CloseUIByHandle(self.commonUIDInfoViewHandle)
    --         self.commonUIDInfoViewHandle = nil
    --         self.isUIDInfoViewCreated = false
    --     end
    -- end

    if self:IsToStableLobbyGameFlow(gameFlowType) then
        self.commonUIDInfoViewHandle = Facade.UIManager:AsyncShowUI(UIName2ID.CommonSystemInfoView)
    end
end

function CommonBarModule:IsToStableLobbyGameFlow(gameFlowType)
    if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then    
        if (gameFlowType == EGameFlowStageType.ModeHall) or
         (gameFlowType == EGameFlowStageType.ModeHallToLobby) or
         (gameFlowType == EGameFlowStageType.ModeHallToSafeHouse) or
         (gameFlowType == EGameFlowStageType.SafeHouse) or
         (gameFlowType == EGameFlowStageType.SafeHouseToBattleField) or
         (gameFlowType == EGameFlowStageType.Lobby) or
         (gameFlowType == EGameFlowStageType.BattleFieldToSafeHouse) 
          then
            return true
        end
    end
    return false
end

---------------------------------------------------------------------------------
--- Loading 生命周期
--- 设置bAutoLoading = true则下列生命周期有效
---------------------------------------------------------------------------------
---@overload fun(ModuleBase, OnLoadingLogin2Frontend)
function CommonBarModule:OnLoadingLogin2Frontend(gameFlowType)
    CommonBarLoadLogic.OnLoadingLogin2FrontendProcess(gameFlowType)
end

---@overload fun(ModuleBase, OnLoadingGame2Frontend)
function CommonBarModule:OnLoadingGame2Frontend(gameFlowType)
    CommonBarLoadLogic.OnLoadingGame2FrontendProcess(gameFlowType)
end

---@overload fun(ModuleBase, OnLoadingFrontend2Game)
function CommonBarModule:OnLoadingFrontend2Game(gameFlowType)
    CommonBarLoadLogic.OnLoadingFrontend2GameProcess(gameFlowType)
    ThemeHelperTool.ClearThemeID2IdClassMapRow()
end

--------------------------------------------------------------------------
--- 对外 Public API
--------------------------------------------------------------------------
function CommonBarModule:ShowCommonBarBasisView(loadCallback)
    return CommonBarViewLogic.ShowCommonBarBasisViewProcess(loadCallback)
end

function CommonBarModule:CloseCommonBarBasisView()
    return CommonBarViewLogic.CloseCommonBarBasisViewProcess()
end

--------------------------------------------------------------------------
--- 对外 Public API
--------------------------------------------------------------------------
function CommonBarModule:ShowTopBar(loadCallback)
    return CommonBarViewLogic.ShowTopBarProcess(loadCallback)
end

function CommonBarModule:CloseTopBar()
    return CommonBarViewLogic.CloseTopBarProcess()
end

function CommonBarModule:ShowBottomBar(loadCallback)
    return CommonBarViewLogic.ShowBottomBarProcess(loadCallback)
end

function CommonBarModule:CloseBottomBar()
    return CommonBarViewLogic.CloseBottomBarProcess()
end

function CommonBarModule:GetTopBarTabGroupWidget(tabLevel)
    return CommonBarViewLogic.GetTopBarTabGroupWidgetProcess(tabLevel)
end

function CommonBarModule:ForceInvokeBack()
    return CommonBarViewLogic.ForceInvokeBackProcess()
end

function CommonBarModule:OnCloseAllPopUIAndStackUI(...)
    return CommonBarViewLogic.OnCloseAllPopUIAndStackUI(...)
end

function CommonBarModule:GetBottomBarItemByActionName(actionName)
    return CommonBarViewLogic.GetBottomBarItemByActionName(actionName)
end

--------------------------------------------------------------------------
--- 栈UI 配置样式
--- 自动在OnOpen后切换, [必须]在 Ctor / OnInitExtraData 注册，否则无效
--------------------------------------------------------------------------
--- 栈UI默认样式为TopBarStyle.Default(返回按钮+货币条)，货币类型默认为{ECurrencyClientId.Tina}
--- 栈UI可配置的样式枚举见Config
--- TopBarStyle = {
--- 		Default = 0, -- 返回按钮+货币条 (所有栈UI默认样式)
--- 		Hidden = 1, -- 全部隐藏
--- 		HideBtn = 2, -- 仅隐藏返回按钮
--- 		HideInfo = 3, --仅隐藏货币
--- 		Custom = 4, -- 自定义返回按钮（需使用GetTopSideCustomInfo传入按钮蓝图对应的UINavID）
--- },

--- 配置Title方法 --- 调用Module.CommonBar:RegStackUITopBarTitle(uiIns, titleStr)
--- 配置样式方法 --- 调用Module.CommonBar:RegStackUITopBarStyle(uiIns, style)
--- 配置货币类型方法 --- 调用Module.CommonBar:RegStackUITopBarCurrencyTypeList(uiIns, currencyTypeList)
--- 配置自定义返回按钮方法 --- 调用Module.CommonBar:RegStackUITopBarCustomInfo(uiIns, customConfig)

--------------------------------------------------------------------------
--- 非栈UI 需手动在需要的时机配置样式
--------------------------------------------------------------------------
--- 手动change非栈UI导致的顶部bar可见性
function CommonBarModule:SetTopBarVisible(bVisible)
    return CommonBarViewLogic.SetTopBarVisibleProcess(bVisible)
end

function CommonBarModule:SetTopRenderOpacity(renderOpacity)
    return CommonBarViewLogic.SetTopRenderOpacityProcess(renderOpacity)
end

function CommonBarModule:SetTopAndBottomBarVisible(bVisible)
    return CommonBarViewLogic.SetBarVisibleProcess(bVisible)
end

function CommonBarModule:SetTopAndSecPanVisible(bVisible)
    return CommonBarViewLogic.SetBarAndSecPanVisibleProcess(bVisible)
end

--- 手动change非栈UI导致的顶部bar - 返回按钮可见性
function CommonBarModule:SetBackBtnVisible(bVisible)
    return CommonBarViewLogic.SetBackBtnVisibleProcess(bVisible)
end

function CommonBarModule:SetTopBarGroupTabVisible(bVisible, tabLevel)
    return CommonBarViewLogic.SetTopBarGroupTabVisibleProcess(bVisible, tabLevel)
end

function CommonBarModule:GetHDTopTabGroupMode()
    return CommonBarViewLogic.GetHDTopTabGroupModeProcess()
end

--- 手动change非栈UI导致的顶部bar - 返回按钮文字/在HD平台只有文字，没有按钮
function CommonBarModule:ChangeBackBtnText(text)
    return CommonBarViewLogic.ChangeBackBtnTextProcess(text)
end

--- 手动change非栈UI导致的顶部bar - 返回按钮callback
function CommonBarModule:BindBackHandler(callback, caller, ...)
    return CommonBarViewLogic.BindBackHandlerProcess(callback, caller, ...)
end

function CommonBarModule:BindPersistentBackHandler(callback, caller, ...)
    return CommonBarViewLogic.BindPersistentBackHandlerProcess(callback, caller, ...)
end

--- 手动change非栈UI导致的顶部bar - 信息按钮可见性
function CommonBarModule:SetInfoBtnVisible(bVisible)
    return CommonBarViewLogic.SetInfoBtnVisibleProcess(bVisible)
end

--- 手动change非栈UI导致的顶部bar - 信息按钮callback
function CommonBarModule:BindInfoHandler(callback, caller, ...)
    return CommonBarViewLogic.BindInfoHandlerProcess(callback, caller, ...)
end

--- 手动change非栈UI导致的顶部bar - 货币信息可见性
function CommonBarModule:SetCurrencyVisible(bVisible)
    return CommonBarViewLogic.SetCurrencyVisibleProcess(bVisible)
end

--- 设置TopBar等级信息
function CommonBarModule:ShowTopBarLevel(level)
    CommonBarViewLogic.ShowTopBarLevel(level)
end

--- 设置TopBar等级信息(升级按钮)
function CommonBarModule:ShowTopBarUpgradeBtn(deviceInfo)
    CommonBarViewLogic.ShowTopBarUpgradeBtn(deviceInfo)
end

--- 手动设置TopTabGroup
function CommonBarModule:SetTopTabGroup(topTabGroupRegInfo, tabLevel, overrideIndex, bInvoke)
    CommonBarViewLogic.SetTopTabGroup(topTabGroupRegInfo, tabLevel, overrideIndex, bInvoke)
end

--- 操作概述
--- @param summaryList UIInputSummary[]
function CommonBarModule:SetBottomBarInputSummaryList(summaryList, bNavigation)
    CommonBarViewLogic.SetBottomBarInputSummaryList(summaryList, bNavigation)
end

--- @param summaryList UIInputSummary[]
--- @param bReplace bool|nil 默认采用添加模式，传true则采用覆盖模式
--- @param bNavIncluded bool|nil 在bRplace=true时生效，传true时，可覆盖导航快捷键
function CommonBarModule:SetBottomBarTempInputSummaryList(summaryList, bReplace, bNavIncluded)
    CommonBarViewLogic.SetBottomBarTempInputSummaryList(summaryList, bReplace, bNavIncluded)
end

-- BEGIN MODIFICATION @ VIRTUOS : 手动设置BottomBarV2
--- @param summaryList UIInputSummary[]
function CommonBarModule:SetBottomBarInputSummaryListV2(summaryListV2)
    CommonBarViewLogic.SetBottomBarInputSummaryListV2(summaryListV2)
end

-- 仅控制BottomBar显隐，不创建新的对象
--- @param bool bShow 是否显示BottomBar
function CommonBarModule:ShowBottomBarOnly(bShow)
    CommonBarViewLogic.ShowBottomBarOnly(bShow)
end

--设置BottomBar显影
function CommonBarModule:IsBottomBarVisible()
    return CommonBarViewLogic.IsBottomBarVisible()
end
-- END MODIFICATION

-- 还原TempInputSummary产生的修改
function CommonBarModule:RecoverBottomBarInputSummaryList()
    CommonBarViewLogic.RecoverBottomBarInputSummaryList()
end

--- BEGIN MODIFICATION @ VIRTUOS: Clear BottomBar input summary.
function CommonBarModule:ClearBottomBarStackSummaryList()
    CommonBarViewLogic.ClearBottomBarStackSummaryList()
end

function CommonBarModule:ClearBottomBarOuterNavSummaryList()
    CommonBarViewLogic.ClearBottomBarOuterNavSummaryList()
end

function CommonBarModule:ClearBottomBarInputSummaryList()
    CommonBarViewLogic.ClearBottomBarInputSummaryList()
end
--- END MODIFICATION

-- 为TempInputSummary生成一个快照，使用快照可恢复某一时刻的快捷键状态
--- @return table|nil 快照句柄
function CommonBarModule:TakeBottomBarTempInputSummarySnapshot()
    return CommonBarViewLogic.TakeBottomBarTempInputSummarySnapshot()
end

-- 应用TempInputSummary快照
--- @param inHandle table 快照句柄
--- @param bReleaseSnapshot boolean|nil 是否释放快照，默认应用后释放 
function CommonBarModule:ApplyBottomBarTempInputSummarySnapshot(inHandle, bReleaseSnapshot)
    bReleaseSnapshot = setdefault(bReleaseSnapshot, true)
    CommonBarViewLogic.ApplyBottomBarTempInputSummarySnapshot(inHandle, bReleaseSnapshot)
end

function CommonBarModule:SetSideBar(MainTabInputSummaryV2)
    CommonBarViewLogic.SetSideBar(MainTabInputSummaryV2)
end

function CommonBarModule:SetIsSideBarVisible(bInVisible)
    CommonBarViewLogic.SetIsSideBarVisible(bInVisible)
end

function CommonBarModule:SetAndroidInputEnable(bEnable)
    CommonBarInputLogic.SetAndroidInputEnableProcess(bEnable)
end

function CommonBarModule:SetUseStarAppState(bUse, bNeedCloseAnim)
    CommonBarInputLogic.SetUseStarAppStateProcess(bUse, bNeedCloseAnim)
end

---@param index number 当前目标选中idx
---@param bForceCallback boolean 是否需要调用注册回调
---@param tabLevel number 目标页签为几级
---@param bOverrideInitIdx boolean 是否强制override上次记录的idx（用于栈UI返回上层时恢复旧的页签选择，一般不设置为true）
function CommonBarModule:CheckTopTabGroup(index, bForceCallback, tabLevel, bOverrideInitIdx)
    CommonBarViewLogic.CheckTopTabGroup(index, bForceCallback, tabLevel, bOverrideInitIdx)
end

---comment 带动效的接口,默认执行回调
---@param index number 当前目标选中idx
---@param tabLevel number 目标页签为几级
---@param bOverrideInitIdx boolean 是否强制override上次记录的idx（用于栈UI返回上层时恢复旧的页签选择，一般不设置为true）
function CommonBarModule:NavigationSetTopTabGroup(index, tabLevel, bOverrideInitIdx)
    CommonBarViewLogic.NavigationSetTopTabGroup(index, tabLevel, bOverrideInitIdx)
end

---@param indexLv2 number 当前二级页签目标选中idx
---@param indexLv3 number 当前三级级页签目标选中idx
---@param bForceCallback boolean 是否需要调用注册回调
---@param bOverrideInitIdx boolean 是否强制override上次记录的idx（用于栈UI返回上层时恢复旧的页签选择，一般不设置为true）
function CommonBarModule:CheckTopTabGroupTabLevel23(indexLv2, indexLv3, bForceCallback, bOverrideInitIdx)
    CommonBarViewLogic.CheckTopTabGroupTabLevel23(indexLv2, indexLv3, bForceCallback, bOverrideInitIdx)
end

function CommonBarModule:GetTopTabGroupIndex(tabLevel)
    return CommonBarViewLogic.GetTopTabGroupIndex(tabLevel)
end

-- function CommonBarModule:IsChangingStackUITab()
--     return CommonBarHDTabGroupLogic.IsChangingStackUITab()
-- end


--------------------------------------------------------------------------
--- 利用栈UI 模拟Tab切换
--------------------------------------------------------------------------

---想启动一组Tab时调用
-- ---@param topTabGroupRegInfo topTabGroupRegInfo
-- ---@param summaryList UIInputSummary[]|nil
-- ---@param stopCallback func|nil TabGroup Stop时的回调
-- ---@param caller table|nil
-- --- @return handle
-- function CommonBarModule:StartStackUITabGroup(topTabGroupRegInfo, summaryList, stopCallback, caller)
--     return CommonBarHDTabGroupLogic.StartStackUITabGroup(topTabGroupRegInfo, summaryList, stopCallback, caller)
-- end

---关闭一组Tab时调用
-- function CommonBarModule:StopStackUITabGroup(handle, bException, bOuter)
--     return CommonBarHDTabGroupLogic.StopStackUITabGroup(handle, bException, bOuter)
-- end

--- 利用Pop Push组个模拟Tab页面切换，考虑到异步加载导致的页面闪烁，因此需要提供异步加载的资源
--- @param pushFunc func
function CommonBarModule:ChangeStackUITab(UINavIDOrList, pushFunc)
    return CommonBarHDTabGroupLogic.ChangeStackUITab(UINavIDOrList, pushFunc)
end

function CommonBarModule:ChangeStackUITabByNavID(uiNavID)
    return CommonBarHDTabGroupLogic.ChangeStackUITabByNavID(uiNavID)
end

-- --- @param pushFunc func
-- function CommonBarModule:PushStackUIToTab(UINavIDOrList, pushFunc)
--     return CommonBarHDTabGroupLogic.PushStackUIToTab(UINavIDOrList, pushFunc)
-- end

-- function CommonBarModule:PopStackUIFromTab()
--     CommonBarHDTabGroupLogic.PopVirtualStack()
-- end

function CommonBarModule:ShowPrimaryTabGroup(index)
    CommonBarHDTabGroupLogic.ShowPrimaryTabGroup(index)
end

function CommonBarModule:IsChangingPrimaryTab()
    return CommonBarHDTabGroupLogic.IsChangingPrimaryTab()
end

function CommonBarModule:SetPrimaryTabInfoHD(info)
    self.Field:SetPrimaryTabInfoHD(info)
end

function CommonBarModule:GetPrimaryTabInfoHD()
    return self.Field:GetPrimaryTabInfoHD()
end

function CommonBarModule:GetUIIndexInPrimaryTabGroupHD(uiIns)
    return self.Field:GetUIIndexInPrimaryTabGroupHD(uiIns)
end

function CommonBarModule:SetPrimaryTabNavActionsHD(actions)
    self.Field:SetPrimaryTabNavActionsHD(actions)
end

function CommonBarModule:SetPersistentNavActionsHD(actions)
    self.Field:SetPersistentNavActionsHD(actions)
end

function CommonBarModule:GetPrimaryTabNavActionsHD()
    return self.Field:GetPrimaryTabNavActionsHD()
end

function CommonBarModule:IsTwoRowTabUIHD(uiIns)
    return CommonBarHDTabGroupLogic.IsPrimaryTabUI(uiIns) and
        next(CommonBarRegLogic.GetStackUITopBarTabGroupRegInfoProcess(uiIns))
end

function CommonBarModule:GetCurrentTopTabIndex(tabLevel)
    local topBar = self.Field:GetTopBar()
    if topBar then
        if IsHD() then
            return topBar:GetCurrentTopTabIndex(tabLevel)
        else
            return topBar:GetCurrentTopTabIndex(tabLevel)
        end
    end
    return -1
end

--------------------------------------------------------------------------
--- 栈UI 注册手动配置的样式
--------------------------------------------------------------------------
function CommonBarModule:RegStackUITopBarTitle(curUIInst, titleStr)
    return CommonBarRegLogic.RegStackUITopBarTitleProcess(curUIInst, titleStr)
end

function CommonBarModule:RegStackUITopBarStyle(curUIInst, style)
    return CommonBarRegLogic.RegStackUITopBarStyleProcess(curUIInst, style)
end

function CommonBarModule:RegStackUITopBarCurrencyTypeList(curUIInst, currencyTypeList)
    return CommonBarRegLogic.RegStackUITopBarCurrencyTypeListProcess(curUIInst, currencyTypeList)
end

function CommonBarModule:RegStackUITopBarNetSignal(curUIInst, bShow)
    return CommonBarRegLogic.RegStackUITopBarNetSignal(curUIInst, bShow)
end

function CommonBarModule:RegStackUITopBarCustomInfo(curUIInst, customConfig)
    return CommonBarRegLogic.RegStackUITopBarCustomInfoProcess(curUIInst, customConfig)
end

---@param tabGroupRegInfo topTabGroupRegInfo
function CommonBarModule:RegStackUITopBarTabGroupRegInfo(curUIInst, tabGroupRegInfo)
    return CommonBarRegLogic.RegStackUITopBarTabGroupRegInfoProcess(curUIInst, tabGroupRegInfo)
end

---@param tabGroupRegInfo topTabGroupRegInfo
function CommonBarModule:RegStackUIBottomBarTabGroupRegInfo(curUIInst, tabGroupRegInfo)
    return CommonBarRegLogic.RegStackUIBottomBarTabGroupRegInfoProcess(curUIInst, tabGroupRegInfo)
end

---@param tabGroupRegInfo topTabGroupRegInfo
function CommonBarModule:AddTopBarTertiaryTabGroup(tabGroupRegInfo)
    CommonBarViewLogic.AddTopBarTertiaryTabGroupProcess(tabGroupRegInfo)
end

function CommonBarModule:RemoveTopBarTertiaryTabGroup(tabGroupRegInfo)
    CommonBarViewLogic.RemoveTopBarTertiaryTabGroupProcess(tabGroupRegInfo)
end

--- @param summaryList UIInputSummary[]
function CommonBarModule:RegStackUIInputSummary(curUIInst, summaryList)
    return CommonBarRegLogic.RegStackUIInputSummaryProcess(curUIInst, summaryList)
end

function CommonBarModule:RegStackUINavInputSummary(curUIInst, summaryList)
    return CommonBarRegLogic.RegStackUINavInputSummaryProcess(curUIInst, summaryList)
end

function CommonBarModule:RegStackUIInputSummaryV2(curUIInst, summaryList)
    return CommonBarRegLogic.RegStackUIInputSummaryV2Process(curUIInst, summaryList)
end

-- with esc by default
function CommonBarModule:RegStackUIDefaultEsc(curUIInst, bEnable)
    return CommonBarRegLogic.RegStackUIDefaultEscProcess(curUIInst, bEnable)
end

--- 返回前确认弹窗
function CommonBarModule:RegStackUIBeforeBackConfirm(curUIInst, checkConfirmTxt)
    return CommonBarRegLogic.RegStackUIBeforeBackConfirmProcess(curUIInst, checkConfirmTxt)
end

function CommonBarModule:RegStackUIHideBar(curUIInst)
    CommonBarRegLogic.RegStackUIHideBarProcess(curUIInst)
end

function CommonBarModule:GetStackUIHideBar(curUIInst)
    return CommonBarRegLogic.GetStackUIHideBarProcess(curUIInst)
end

--------------------------------------------------------------------------
--- 纯净模式 (一键隐藏全部UI)
--------------------------------------------------------------------------
function CommonBarModule:EnterPureMode()
    return CommonBarPureLogic.EnterPureModeProcess()
end

function CommonBarModule:ExitPureMode()
    return CommonBarPureLogic.ExitPureModeProcess()
end

---刷新货币列表，可能主界面的各个子界面货币显示不同
---@param currencyTypeList any 货币列表
---@param curUIInst any stack ui instance
function CommonBarModule:RefreshCurrencyTypeList(currencyTypeList, curUIInst)
    CommonBarRegLogic.RegStackUITopBarCurrencyTypeListProcess(curUIInst, currencyTypeList)
    CommonBarViewLogic.RefreshCurrencyTypeList(currencyTypeList)
end
--------------------------------------------------------------------------
--- Navigation
--------------------------------------------------------------------------
function CommonBarModule:GetNavigationRecipient(myGeometry, inNavigationEvent)
    return CommonBarUINavigationLogic.GetNavigationRecipient(myGeometry, inNavigationEvent)
end

--type=1:mp,type=2:safehouse
function CommonBarModule:SetTopbarGameMode(type)
    self.Field:SetTopbarGameMode(type)
end

function CommonBarModule:GetTopbarGameMode()
    return self.Field:GetTopbarGameMode()
end

function CommonBarModule:GetIsLobbyTopBarShow()
    return CommonBarViewLogic.GetIsLobbyTopBarShow()
end

function CommonBarModule:SetIsLobbyTopBarShow(isLobbyTopBarShowing)
    CommonBarViewLogic.SetIsLobbyTopBarShow(isLobbyTopBarShowing)
end

function CommonBarModule:ShowBarSwitchMode(loadCallback)
    return CommonBarViewLogic.ShowBarSwitchModeProcess(loadCallback)
end

------------BHD专属
function CommonBarModule:ShowBHDBarSwitchMode(loadCallback)
    return CommonBarViewLogic.ShowBHDBarSwitchModeProcess(loadCallback)
end

function CommonBarModule:CloseBarSwitchMode()
    return CommonBarViewLogic.CloseBarSwitchModeProcess()
end

function CommonBarModule:DoBarSwitchMode()
    return CommonBarViewLogic.DoBarSwitchModeProcess()
end

--------------------------------------------------------------------------
--- 返回流程场景
--------------------------------------------------------------------------
function CommonBarModule:FlowBackQuitGame(fCancelLeaveGameHandle, fConfirmLeaveGameHandle)
    local fCancelLeaveGame = function()
        logframe('IrisSafeHouseModule:FlowBackQuitGame() Cancel')
        if fCancelLeaveGameHandle then
            fCancelLeaveGameHandle()
        end
    end
    local fConfirmLeaveGame = function()
        logframe('IrisSafeHouseModule:FlowBackQuitGame() Confirm')
        Module.CommonBar.Config.evtConfirmLeaveGame:Invoke()
        UKismetSystemLibrary.QuitGame(GetGameInstance(), nil, EQuitPreference.Quit, false)
        if fConfirmLeaveGameHandle then
            fConfirmLeaveGameHandle()
        end
    end
    Module.CommonTips:ShowConfirmWindow(self.Config.Loc.QuitGame, fConfirmLeaveGame, fCancelLeaveGame)
end

--------------------------------------------------------------------------
--- Unlock State
--------------------------------------------------------------------------
function CommonBarModule:RegClickedStateByLockID(barBtnUIIns, fSuccessOperaCallbackIns, lockData)
    return CommonBarUnlockLogic.RegClickedStateByLockIDProcess(barBtnUIIns, fSuccessOperaCallbackIns, lockData)
end

function CommonBarModule:RegClickedStateByCheckCallback(btnUIIns, fCheckBeforeClickCallbackIns, fSuccessOperaCallbackIns, fFailureOperaCallbackIns)
    return CommonBarUnlockLogic.RegClickedStateByCheckCallbackProcess(btnUIIns, fCheckBeforeClickCallbackIns, fSuccessOperaCallbackIns, fFailureOperaCallbackIns)
end

--------------------------------------------------------------------------
--- 提前篡改initIdx
--------------------------------------------------------------------------
function CommonBarModule:RegTopTabGroupInitIdx(tabLevel, initIdx)
    return CommonBarViewLogic.RegTopTabGroupInitIdxProcess(tabLevel, initIdx)
end



function CommonBarModule:GetTopBarUIInst(tabLevel, initIdx)
    local topBar = self.Field:GetTopBar()
    return topBar
end

function CommonBarModule:CheckAnyBlockUI()
    return CommonBarViewLogic.CheckAnyBlockUIProcess()
end


return CommonBarModule
