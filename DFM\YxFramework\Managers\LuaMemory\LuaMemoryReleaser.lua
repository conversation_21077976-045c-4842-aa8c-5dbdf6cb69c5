----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaFManager)
----- LOG FUNCTION AUTO GENERATE END -----------



local LuaMemoryPolicy = require "DFM.YxFramework.Managers.LuaMemory.LuaMemoryPolicy"
local UWeaponAssembleSubsystem = import "WeaponAssembleSubsystem"

local LuaMemoryReleaser = {}

local LuaMemoryReleaseResourceHandle = {}
function LuaMemoryReleaser.Init()
     LuaMemoryReleaseResourceHandle = {
          [LuaMemoryPolicy.ResourceTypes.Level] = LuaMemoryReleaser._ReleaseLevel,
          [LuaMemoryPolicy.ResourceTypes.LevelNoMatch] = LuaMemoryReleaser._ReleaseLevelNoMatch,
          [LuaMemoryPolicy.ResourceTypes.RTIRT] = LuaMemoryReleaser._ReleaseRTIRT,
          [LuaMemoryPolicy.ResourceTypes.RTCapture] = LuaMemoryReleaser._ReleaseRTCapture,
          [LuaMemoryPolicy.ResourceTypes.UIImageCache] = LuaMemoryReleaser._ReleaseUIImageCache,
          [LuaMemoryPolicy.ResourceTypes.UI] = LuaMemoryReleaser._ReleaseUI,
     }
end

---@param group LuaMemoryPolicy.EReleaseResourceGroups
function LuaMemoryReleaser.ReleaseResource(group)
     if group == nil then
          return
     end

     local groupConfig = LuaMemoryPolicy.ReleaseResourceGroupCongigs[group]
     if groupConfig == nil then
          return
     end

     LuaMemoryReleaser._ProcessReleaseResouces(groupConfig)
end

-----------------------------------------------------------------------------

function LuaMemoryReleaser._ProcessReleaseResouces(groupConfig)
     local resourcesType = groupConfig.ResourceType
     if resourcesType == nil then
         return 
     end

     local priorityList = LuaMemoryPolicy.ResourceTypesPriorityList
     for i,type in ipairs(priorityList) do
          if LuaMemoryReleaser._TestBitMask(resourcesType, type) then
               local handle = LuaMemoryReleaseResourceHandle[type]
               if handle ~= nil then
                    loginfo("[MemoryDebug] HandleReleaseResource", type)
                    handle(groupConfig)
               end
          end
     end
end

function LuaMemoryReleaser._ReleaseLevel(groupConfig)
     Facade.LevelLoadManager:UnLoadAllLevelExceptMainAndCurrent()
end

function LuaMemoryReleaser._ReleaseLevelNoMatch(groupConfig)
     Facade.LevelLoadManager:UnLoadAllLevelExceptMainAndCurrent(ESubStage.HallMatch)
end

function LuaMemoryReleaser._ReleaseRTIRT(groupConfig)
     local weaponAssembleSubsystem = UWeaponAssembleSubsystem.Get()
     if isvalid(weaponAssembleSubsystem) then
          weaponAssembleSubsystem:ClearRTI()
     end
end

function LuaMemoryReleaser._ReleaseRTCapture(groupConfig)
     local weaponAssembleSubsystem = UWeaponAssembleSubsystem.Get()
     if isvalid(weaponAssembleSubsystem) then
          weaponAssembleSubsystem:DestroyRTICapture()
     end
end

function LuaMemoryReleaser._ReleaseUIImageCache(groupConfig)
     Facade.UIManager:ClearUIImageRes()
end

function LuaMemoryReleaser._ReleaseUI(groupConfig)
     local gcType = groupConfig.GCType
     if gcType == nil then
         return 
     end

     local bCallFullGC = LuaMemoryReleaser._TestBitMask(gcType, LuaMemoryPolicy.GCTypes.CheckFullGC)
     local bImmediateCallGC = LuaMemoryReleaser._TestBitMask(gcType, LuaMemoryPolicy.GCTypes.ImmediateFullGC)
     local bImmediateUEGC = LuaMemoryReleaser._TestBitMask(gcType, LuaMemoryPolicy.GCTypes.ImmediateUEGC)

     Facade.UIManager:UltimateClearAllPoolWithRes(bCallFullGC, bImmediateCallGC, bImmediateUEGC)
end

function LuaMemoryReleaser._TestBitMask(lhs, rhs)
     local result =  (lhs & rhs) ~= 0
     return result
end

-----------------------------------------------------------------------------

return LuaMemoryReleaser