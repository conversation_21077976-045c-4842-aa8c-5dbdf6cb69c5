local UKismetSystemLibrary = import "KismetSystemLibrary"
local Locations = require "Automation.PerfTest.Locations"

local function BatchSpawn(Locations, IdleTemplate, MovingTemplate, ShootingTemplate)
    local GameInstance = GetGameInstance()

    for i, Location in ipairs(Locations) do
        local Template = Location.template
        local Cmd = string.format("GMSpawnInLocation %s %f %f %f", Template, Location.x, Location.y, Location.z)
        UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, Cmd)
    end
end

return function()
    BatchSpawn(Locations.SolLocations_InLine)
end
