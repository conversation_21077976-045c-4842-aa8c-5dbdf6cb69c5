----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMTeam)
----- LOG FUNCTION AUTO GENERATE END -----------



local UIrisWorldGamePlayUtils = import "IrisWorldGamePlayUtils"
local ASafeHouseLevelSubSys = import "SafeHouseLevelSubSys"

local USteamService = nil
if PLATFORM_WINDOWS then
    local UDFSteamServiceAgent = import "DFSteamServiceAgent"
    USteamService = UDFSteamServiceAgent.Get(GetGameInstance())
end

---@class TeamModule : ModuleBase
local TeamModule = class("TeamModule", require("DFM.YxFramework.Managers.Module.ModuleBase"))

local TeamLogic = require "DFM.Business.Module.TeamModule.TeamLogic"
local TeamSvr = Server.TeamServer
--- BEGIN MODIFICATION - VIRTUOS
local UDFMPlatformFriendManager = import("DFMPlatformFriendManager")
local DFMPlatformFriendManager = UDFMPlatformFriendManager.Get(GetWorld())
local UDFMGameUrlGenerator = import "DFMGameUrlGenerator"
local UDFMGameUrlGeneratorIns = UDFMGameUrlGenerator.Get(GetGameInstance())
local UDFMOnlineSessionManager = import("DFMOnlineSessionManager")
local DFMOnlineSessionManager = UDFMOnlineSessionManager.Get(GetWorld())
--- END MODIFICATION - VIRTUOS
local MapGameFlowType2MatchGameMode = {
    [EGameFlowStageType.SafeHouse] = MatchGameMode.WorldGameMode, --SOL
    [EGameFlowStageType.Lobby] = MatchGameMode.TDMGameMode, --MP
    [EGameFlowStageType.LobbyBHD] = MatchGameMode.BlackHawkDown, --BHD
    [EGameFlowStageType.ModeHall] = MatchGameMode.InvalidGameMode, --ModeHall
}
local MapMatchGameMode2EArmedForceMode = {
    [MatchGameMode.WorldGameMode] = EArmedForceMode.SOL, --SOL
    [MatchGameMode.TDMGameMode] = EArmedForceMode.MP, --MP
    [MatchGameMode.BlackHawkDown] = EArmedForceMode.BHD, --BHD
    [MatchGameMode.InvalidGameMode] = EArmedForceMode.Invalid, --ModeHall
}
function TeamModule:Ctor()
    self.bIsBattleField = false
    self:AddLuaEvent(Server.TeamServer.Events.evtReceiveTeamInvite, self._OnReceiveTeamInvite, self)
    --BEGIN MODIFICATION - VIRTUOS
    if IsConsole() then
        self:AddLuaEvent(Server.TeamServer.Events.evtJoinTeam, self.OnJoinTeamWithPlatformSession, self)
    else
        self:AddLuaEvent(Server.TeamServer.Events.evtJoinTeam, self.OnJoinTeam, self)
    end
     --BEGIN MODIFICATION - VIRTUOS
    self:AddLuaEvent(Server.TeamServer.Events.evtFaceCheck, self.OnFaceCheck, self)

    self:AddLuaEvent(Server.TeamServer.Events.evtJoinPlatformTeamSession, self.JoinPlatformTeamSession, self)
    self:AddLuaEvent(Server.TeamServer.Events.evtUpdatePlatformTeamSession, self.UpdatePlatformTeamSession, self)
    self:AddLuaEvent(Server.TeamServer.Events.evtSendTeamSessionInviteToUser, self.SendTeamSessionInviteToUser, self)
    self:AddLuaEvent(Server.TeamServer.Events.evtCreatePlatformTeamSession, self.CreatePlatformTeamSession, self)
    self:AddLuaEvent(Server.TeamServer.Events.evtDestroyPlatformTeamSession, self.DestroyPlatformTeamSession, self)
    self:AddLuaEvent(Server.TeamServer.Events.evtCanTeamWithPlayerViaCrossPlayPermission, self.CanTeamWithPlayerViaCrossPlayPermission, self)
    self:AddLuaEvent(Server.TeamServer.Events.evtCanSendTeamMatchWitchCurrentTeamMembers, self.CanSendTeamMatchWitchCurrentTeamMembers, self)

    --BEGIN MODIFICATION - liuyi_b
    --平台队伍会话相关
    if IsConsole() and DFMOnlineSessionManager then
        self:AddLuaEvent(Server.TeamServer.Events.evtYouLeaveTeam, self.DestroyPlatformTeamSession, self)
        self:AddLuaEvent(Server.TeamServer.Events.evtYouAreKicked, self.DestroyPlatformTeamSession, self)
        if IsXSX() then
            self:AddLuaEvent(Server.TeamServer.Events.evtTeamCreated, self.CreatePlatformTeamSession, self)
            self:AddLuaEvent(Server.TeamServer.Events.evtCaptialChanged, self.UpdatePlatformTeamSession, self)
            self:AddLuaEvent(Server.TeamServer.Events.evtTeammateJoined, self.UpdatePlatformTeamSession, self)
            self:AddLuaEvent(Server.TeamServer.Events.evtTeammateLeft, self.UpdatePlatformTeamSession, self)
        elseif IsPS5() then
            self:AddLuaEvent(Server.TeamServer.Events.evtCaptialChanged, self.UpdatePlatformTeamSessionLeader, self)
            self:AddLuaEvent(Server.TeamServer.Events.evtTeammateJoined, self.UpdatePlatformTeamSessionMaxMember, self)
            self:AddLuaEvent(Server.TeamServer.Events.evtTeammateLeft, self.UpdatePlatformTeamSessionMaxMember, self)
            DFMOnlineSessionManager.OnSessionMemberChangedCompleteDelegate:Add(self.OnPlatformSessionMemberChanged, self)
            DFMOnlineSessionManager.OnSessionLeaderChangedCompleteDelegate:Add(self.UpdatePlatformTeamSession, self) --平台队长更新后，刷新Session中Custom的玩家数据
        end
        DFMOnlineSessionManager.OnAcceptInvitationFromTeamSessionCompleteDelegate:Add(self.OnAcceptInvitationFromTeamSession, self)
    end
    --END MODIFICATION - liuyi_b    
end

---模块Init回调，用于初始化一些数据
function TeamModule:OnInitModule()
    Facade.ProtoManager.Events.evtOnRelayConnected:AddListener(TeamLogic.OnRelayConnected)

    --- Steam相关
    if USteamService and VersionUtil.IsGameChannelSteam() then
        --- Steam丰富状态相关
        --- 好友邀请Connect
        self:AddLuaEvent(Module.Login.Config.Events.evtOnLoginSuccess, self.SetSteamConnectRichPresence, self)
        self:AddLuaEvent(Server.TeamServer.Events.evtTeamCreated, self.SetSteamConnectRichPresence, self)
        self:AddLuaEvent(Module.Team.Config.evtIsInTeamChanged, self.SetSteamConnectRichPresence, self)
        self:AddLuaEvent(Server.TeamServer.Events.evtJoinTeam, self.SetSteamConnectRichPresence, self)
        --- 退出队伍清空队伍信息
        Server.TeamServer.Events.evtYouAreKicked:AddListener(self.SetSteamConnectRichPresence)
        Server.TeamServer.Events.evtYouLeaveTeam:AddListener(self.SetSteamConnectRichPresence)
        --- 队伍人数
        self:AddLuaEvent(Server.TeamServer.Events.evtReceiveTeamInvite, self.SetSteamGroupSizeRichPresence, self)
        self:AddLuaEvent(Server.TeamServer.Events.evtTeammateJoined, self.SetSteamGroupSizeRichPresence, self)
        self:AddLuaEvent(Server.TeamServer.Events.evtTeammateLeft, self.SetSteamGroupSizeRichPresence, self)
    end
end

-- BEGIN MODIFICATION - VIRTUOS
function TeamModule:OnJoinTeamWithPlatformSession()
    if not IsConsole() then
        return
    end
    self:JoinPlatformTeamSession()
    self:OnJoinTeam()
end
-- END MODIFICATION - VIRTUOS

function TeamModule:OnJoinTeam()
    loginfo("TeamModule:OnJoinTeam")
    -- if Server.TeamServer:GetTeamNum() <= 1 then
    --     return
    -- end
    local ESwitchMode = Module.GameMode.Config.ESwitchMode
    --根据邀请的模式类型，切换大厅(这里是LuaGameFlowEvent)
    local curTeamMatchMode = Server.TeamServer:GetTeamMatchMode().game_mode
    -- 若需要切模式
    if MapGameFlowType2MatchGameMode[Facade.GameFlowManager:GetCurrentGameFlow()] ~= curTeamMatchMode then
        -- 对没开放的模式处理
        local IsModeUnlocked = Module.GameMode:IsModeUnlocked(MapMatchGameMode2EArmedForceMode[curTeamMatchMode])
        if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.ModeHall and IsModeUnlocked then
            if curTeamMatchMode == MatchGameMode.WorldGameMode then
                Module.IrisSafeHouse:SetForceEnter3DSafeHouse(false)   -- 在模式选择大厅接受邀请，直接进2D大厅
                Module.IrisSafeHouse:EnterSafeHouseFlow()
            elseif curTeamMatchMode == MatchGameMode.TDMGameMode then
                Module.IrisSafeHouse:EnterBattlefield()
            elseif curTeamMatchMode == MatchGameMode.BlackHawkDown then
                Module.IrisSafeHouse:EnterBHDHallFlow()
            end
        elseif IsModeUnlocked then
            if curTeamMatchMode == MatchGameMode.WorldGameMode then
                Module.GameMode:TrySwitchMode(ESwitchMode.SOL, true)
            elseif curTeamMatchMode == MatchGameMode.TDMGameMode then
                Module.GameMode:TrySwitchMode(ESwitchMode.MP, true)
            elseif curTeamMatchMode == MatchGameMode.BlackHawkDown then
                Module.GameMode:TrySwitchMode(ESwitchMode.BHD, true)
            end
        else
            Server.TeamServer:ExitTeam() --若模式未开放，则退队
        end
    end
    -- BEGIN MODIFICATION - VIRTUOS
    if IsPS5() and Server.TeamServer:GetTeamNum() > 1 then
        local UDFMOnlineIdentityManager = import "DFMOnlineIdentityManager"
        local identityManager = UDFMOnlineIdentityManager.Get(GetGameInstance())
        identityManager:SetUsingPlatformPremiumFeatures(EPlatformPremiumFeaturesType.RealtimeMultiplay, true)
    end
    -- END MODIFICATION - VIRTUOS
    --UA上报
    Module.GCloudSDK:OnCreateOrJoinTeam()
end

---若为非懒加载模块，则在Init后调用
---模块默认加载资源（预加载UI蓝图、需要用到的图片等等
function TeamModule:OnLoadModule()
end

function TeamModule:IsInBattleField()
    return self.bIsBattleField
end

function TeamModule:OnGameFlowChangeEnter(gameFlowType)
    if gameFlowType == EGameFlowStageType.Lobby then
        self.bIsBattleField = true
    else
        self.bIsBattleField = false
    end

    if gameFlowType == EGameFlowStageType.Lobby or gameFlowType == EGameFlowStageType.SafeHouse then
        self:SetSteamConnectRichPresence()
    end
    -- self:CheckShouldExitTeamFlow(gameFlowType)
    -- BEGIN MODIFICATION - VIRTUOS
    -- 进入大厅或安全屋时，拉取最近同玩列表，筛选出Xbox平台玩家并提交
    local enterLobbyOrSafeHouse = gameFlowType == EGameFlowStageType.Lobby or gameFlowType == EGameFlowStageType.SafeHouse
    if PLATFORM_XSX == 1 and enterLobbyOrSafeHouse then
        local fOnCSCoPlayerListRes = function(res)
            local playerList = res["player_list"] or {}
            local recentPlayerListFromXbox = {}
            local recentPlayerXUIDList = {}
            for _, player in pairs(playerList) do
                local playerId = player.player_id
                local playerPlat = player.plat
                if playerId ~= Server.AccountServer:GetPlayerId() and not Module.Friend:CheckIsBlack(playerId) and playerPlat == PlatIDType.Plat_XBox then
                    -- 筛选出来自Xbox平台的非黑名单用户
                    table.insert(recentPlayerListFromXbox, playerId)
                end
            end
            local xboxUserNum = #recentPlayerListFromXbox
            if xboxUserNum > 0 then
                for _, xboxUser in pairs(recentPlayerListFromXbox) do
                    -- 查询Xbox用户的XUID
                    local OnReqQueryRes = function(HttpRequest, HttpResponse, bSucceeded)
                        queryRes = UDFMGameUrlGeneratorIns:GenerateQueryRes(HttpResponse:GetContentAsString())
                        xboxUserNum = xboxUserNum - 1
                        if queryRes.bHasUID then
                            table.insert(recentPlayerXUIDList, queryRes.UID)
                        end
                        if xboxUserNum <= 0 and  #recentPlayerXUIDList > 0 then
                            DFMPlatformFriendManager:AddRecentPlayers(recentPlayerXUIDList)
                        end
                    end
                    Server.SocialServer:ReqQueryUID(ULuautils.GetUInt64String(xboxUser), OnReqQueryRes)
                end
            end
        end
        Server.SocialServer:ReqCoPlayerList(fOnCSCoPlayerListRes)
    end
    -- END MODIFICATION - VIRTUOS

    if gameFlowType == EGameFlowStageType.Game then
        if self.Field.GameTeamInviteViewModel then
            self.Field.GameTeamInviteViewModel:EnterameStage()
        end
    else
        if self.Field.GameTeamInviteViewModel then
            self.Field.GameTeamInviteViewModel:ExitGameStage()
        end
    end
end

function TeamModule:CheckShouldExitTeamFlow(gameFlowType)
    local matchMode = setdefault(Server.TeamServer:GetTeamMatchMode(), {})
    local execpted = gameFlowType == EGameFlowStageType.Lobby and MatchGameMode.TDMGameMode or MatchGameMode.WorldGameMode
    loginfo("[darc] execpted: ", execpted, matchMode.game_mode)
    if matchMode.game_mode ~= execpted and Server.AccountServer:IsInTeam() then
        Server.TeamServer:ExitTeam()
        Server.GameModeServer:SetMatchMode(Server.GameModeServer:GetUnknownGameMode())
        Server.GameModeServer:SetMatchModes({})
    end
end

function TeamModule:OnGameFlowChangeLeave(gameFlowType)
    if gameFlowType == EGameFlowStageType.Lobby or gameFlowType == EGameFlowStageType.SafeHouse then
    end

    -- local TeamSvr = Server.TeamServer
    -- if TeamSvr:GetTeamNum() <= 1 and TeamSvr:GetTeamMatchMode() then
    --     TeamSvr:ExitTeam()
    -- end
end

function TeamModule:ConvenientAgreeInvite()
    if self.Field.GameTeamInviteViewModel then
        self.Field.GameTeamInviteViewModel:ConvenientAgreeInvite()
    end
end

function TeamModule:ConvenientRejectInvite()
    if self.Field.GameTeamInviteViewModel then
        self.Field.GameTeamInviteViewModel:ConvenientRejectInvite()
    end
end

function TeamModule:ShouldResInvitation()
    if self.Field.GameTeamInviteViewModel then
        return self.Field.GameTeamInviteViewModel:HaveOldestPendingReq()
    end

    return false
end

function TeamModule:ShowMainPanel()
    Facade.UIManager:AsyncShowUI(UIName2ID.TeamSystemMain)
end

function TeamModule:OpenInvitePanel()
    return Module.Social:OpenInvitePanel()
    -- return TeamLogic.OpenInvitePanelProcess()
end

function TeamModule:CloseInvitePanel()
    return TeamLogic.CloseInvitePanelProcess()
end

function TeamModule:GetTeammateNameByUin(playerUin)
    return TeamLogic.GetTeammateNameByUinProcess(playerUin)
end

function TeamModule:_OnReceiveTeamInvite(info,fConfirmFunction,fRefuseFunction)
    local function handleReceiveTeamInvite()
        local nick = ""
        local inviteType = "TeamInvite"
        if info.apply_player then
            nick = info.apply_player.nick_name
            inviteType = info.inviteType
        else
            nick = info.InviterNick
        end
        local btnList = { HeadButtonType.PlayerInformat, HeadButtonType.AddFriend, HeadButtonType.AddBlack }
        local isFriend = Server.FriendServer:CheckIsFriend(info.InviterID)
        if isFriend then table.removebyvalue(btnList, HeadButtonType.AddFriend) end
        Module.Social:ShowInviteMainWindow(
            Module.Team.Config.Loc.InviteFromNearby,
            nick,
            info,
            inviteType,
            fConfirmFunction,
            fRefuseFunction,
            Module.Team.Config.Loc.RejectInvite,
            Module.Team.Config.Loc.AcceptInvite,
            info.source,
            btnList
        )
    end
    -- BEGIN MODIFICATION - VIRTUOS
    if IsConsole() == false then
        handleReceiveTeamInvite()
    else
        -- 通过平台端发送的入队申请
        local specialChannel = info.SpecialChannel
        if specialChannel == 1 then
            -- 1表示通过平台端发送的邀请
            fConfirmFunction()
            return
        end
        -- 通过游戏内发送的入队申请
        if info.apply_player then
            local applyPlayerID = info.apply_player.player_id
            local checkList = {}
            table.insert(checkList, applyPlayerID)
            Server.ChatServer:CheckUsersPermissionsByOpenIdList(EPlatformUserPermissionType.CommunicateUsingText ,false, checkList, function(allowedList)
                if #allowedList > 0 then
                    handleReceiveTeamInvite()
                end
            end)
        else
            -- 通过游戏内发送的组队邀请
            handleReceiveTeamInvite()
        end
    end
    -- END MODIFICATION - VIRTUOS
end

function TeamModule:GetInviteTime()
    return self.Field:GetInviteTime()
end

-- 获取用于表现的匹配队友状态
-- 无队伍使用保存的设置
-- 有队伍使用队长设置
function TeamModule:GetMatchTeammateState()
    if Server.TeamServer:IsInTeam() and Server.TeamServer:GetTeamNum() > 1 then
       return Server.TeamServer:GetAutoMatchTeamMates()
    else
        return self.Field:GetMatchTeammateState()
    end
end

-- 设置个人的匹配队友状态
---@param s boolean
function TeamModule:SetUserMatchTeammateState(s)
    self.Field:SetMatchTeammateState(s)
end

---注销LuaEvent、Timer监听
function TeamModule:OnDestroyModule()
    self:RemoveAllLuaEvent()
    Facade.ProtoManager.Events.evtOnRelayConnected:RemoveListener(TeamLogic.OnRelayConnected)
end

function TeamModule:EnterBattlefield()
    TeamLogic.EnterBattlefield()
end

function TeamModule:EnterSafeHouse()
    TeamLogic.EnterSafeHouse()
end

function TeamModule:EnterLobbyBHD()
    TeamLogic.EnterLobbyBHD()
end

function TeamModule:FindSeat(teammateID)
    return self.Field:FindSeat(teammateID)
end

function TeamModule:GetTeamInfoBySeat(slot)
    return self.Field:GetTeamInfoBySeat(slot)
end

function TeamModule:CreateTeam(createType, fCallbackIns)
    createType = createType or 1
    if TeamLogic.IsInMp() then
        local modeList = Module.BattlefieldEntry:GetSelectedModeList()
        local groupId = Module.BattlefieldEntry:GetGroupId()
        Server.TeamServer:CreateTeamWithModeList(modeList, groupId,nil, createType, fCallbackIns)
    else
        Server.TeamServer:CreateTeam({Server.GameModeServer:GetMatchMode()}, false , createType, fCallbackIns)
    end
end

function TeamModule:JoinTeamFromMiniProgram(Url)
    local lastUrl = ""
    if VersionUtil.IsGameChannelSteam() and USteamService then
        --- Steam平台直接使用接口获取参数
        local bGetCommandline = true
        bGetCommandline, lastUrl = USteamService:GetLaunchCommandLine(lastUrl)
    else
        lastUrl = Url or Module.DeepLink:GetLastDeepLinkUrl()
    end

    if lastUrl and lastUrl ~= "" then
        lastUrl = tostring(lastUrl)
        logerror("[v_dzhanshen] TeamModule:JoinTeamFromMiniProgram lastUrl="..tostring(lastUrl))
        
        --- 避免微信小程序抽风导致参数位置变化，直接寻找teamid
        local teamId = nil
        --- match函数，返回（）捕获组中的数据，%d+代表多个数字
        teamId = tonumber(lastUrl:match("TeamID=(%d+)"))

        --- QQ中心数据没有TeamID=，在game_data中寻找
        if not teamId then
            local CommandLine = ""
            local JsonFactory = require "DFM.YxFramework.Plugin.Json.Json"
            local Json = JsonFactory.createJson()
            local data = Json.decode(lastUrl)
            if data and table.istable(data) and data.game_data then
                CommandLine = data.game_data
            end

            if tonumber(CommandLine) ~= nil then
                teamId = tonumber(CommandLine)
            end
        end

        if teamId and teamId ~= 0 then
            logerror("[v_dzhanshen] TeamModule:JoinTeamFromMiniProgram game_data teamId="..teamId)
            --- 自身是否有队伍，有队伍则退出并加入新队伍
            if Server.TeamServer:IsInTeam() and tostring(Server.TeamServer:GetTeamID()) ~= tostring(teamId) then
                local fExitCallback=function()
                    Server.TeamServer:ApplyJoinFromMiniProgram(teamId)
                end
                Server.TeamServer:ExitTeam(fExitCallback)
            else
                Server.TeamServer:ApplyJoinFromMiniProgram(teamId)
            end
            return true
        elseif VersionUtil.IsGameChannelSteam() then
            --- Steam邀请可能没有队伍，则走社交模块中邀请、申请
            Module.Social:OnSteamGameJoinReceived(lastUrl)
        elseif teamId and teamId == 0 then
            logerror("[v_dzhanshen] TeamModule:JoinTeamFromMiniProgram teamId is nil")
            Module.CommonTips:ShowSimpleTip(Module.Team.Config.Loc.CannotJoinTeamByInvite)
        else
            logerror("[v_dzhanshen] TeamModule:JoinTeamFromMiniProgram teamId配置为空")
        end
    else
        logerror("[v_dzhanshen] TeamModule:JoinTeamFromMiniProgram lastUrl is nil")
    end

    return false
end

function TeamModule:OnCreateTeam()
    --UA上报
    Module.GCloudSDK:OnCreateOrJoinTeam()
end

--BEGIN MODIFICATION - liuyi_b
-------------平台会话相关 Start-------------
function TeamModule:OnAcceptInvitationFromTeamSession(customJoinInfo, bWasSuccessful)
    if IsConsole() then
        TeamLogic.OnAcceptInvitationFromTeamSession(customJoinInfo, bWasSuccessful)
    end
 end 
 
 --创建平台队伍会话
 function TeamModule:CreatePlatformTeamSession()
    if IsConsole() then
        TeamLogic.CreatePlatformTeamSession()
    end
    --UA上报
    Module.GCloudSDK:OnCreateOrJoinTeam()
 end
 
 --加入平台队伍会话
 function TeamModule:JoinPlatformTeamSession()
    if IsConsole() then
        TeamLogic.JoinPlatformTeamSession()
    end
 end
 
 --销毁平台队伍会话
 function TeamModule:DestroyPlatformTeamSession()
    if IsConsole() then
        TeamLogic.DestroyPlatformTeamSession()
    end
 end
 
 --更新平台队伍会话
 function TeamModule:UpdatePlatformTeamSession()
    if IsConsole() then
        TeamLogic.UpdatePlatformTeamSession()
    end
 end

 --PS5平台下面两种情况需要单独处理
 --更新平台队伍Leader
 function TeamModule:UpdatePlatformTeamSessionLeader()
    if IsPS5() then
        TeamLogic.UpdatePlatformTeamSessionLeader()
    end
 end

 --更新平台队伍最大人数
 function TeamModule:UpdatePlatformTeamSessionMaxMember()
    if IsPS5() then
        TeamLogic.UpdatePlatformTeamSessionMaxMember()
    end
 end

 --处理平台Session成员变更流程
 function TeamModule:OnPlatformSessionMemberChanged(bJoined)
    if IsPS5() then
        TeamLogic.UpdatePlatformTeamSessionLeader()
    end
 end

  --处理平台Session Leader变更流程
  function TeamModule:OnPlatformSessionLeadChanged()
    if IsPS5() then
        if DFMOnlineSessionManager then
            if DFMOnlineSessionManager:IsTeamSessionLeader() then
                TeamLogic.UpdatePlatformTeamSession()
            end
        end
    end
 end

 --邀请用户加入队伍会话
 function TeamModule:SendTeamSessionInviteToUser(openId)
    if IsConsole() then
        TeamLogic.SendTeamSessionInviteToUser(openId)
    end
 end

--获取Session ID
function TeamModule:GetSessionID()
    return TeamLogic.GetSessionID()
 end

 --重新获取平台Session的最大人数
 function TeamModule:GetNewMaxSessionMemebers()
    return TeamLogic.GetNewMaxSessionMemebers()
 end

  --添加玩家到白名单
  function TeamModule:AddPlayerToSessionAccess(playerId)
    return TeamLogic.AddPlayerToSessionAccess(playerId)
 end

  --从白名单移出玩家
  function TeamModule:RemovePlayerToSessionAccess(playerId)
    return TeamLogic.RemovePlayerToSessionAccess(playerId)
 end

 -------------平台会话相关 End-------------
  --根据跨平台游玩权限判断是否可以与目标玩家组队
 function TeamModule:CanTeamWithPlayerViaCrossPlayPermission(openId, fCallback)
    TeamLogic.CanTeamWithPlayerViaCrossPlayPermission(openId, fCallback)
 end

 --根据队员的跨平台开关判断是否可以一起匹配
 function TeamModule:CanSendTeamMatchWitchCurrentTeamMembers(fCallback)
    TeamLogic.CanSendTeamMatchWitchCurrentTeamMembers(fCallback)
 end
 ---END MODIFICATION - liuyi_b

-----------------------------------------------------Steam丰富状态----------------------------------------------------

--- 设置Steam丰富状态键
function TeamModule:SetSteamRichPresence(richKey, richValue)
    if USteamService then
        USteamService:SetRichPresence(richKey, richValue)
    end
end

--- 设置Steam查看游戏信息
function TeamModule:SetSteamStatusRichPresence()
    if USteamService then
        Server.AccountServer:GetPlayerId()
        USteamService:SetRichPresence("status", "")
    end
end

--- 设置Steam组队人数
function TeamModule:SetSteamGroupSizeRichPresence()
    if USteamService then
        local MemberNum = Server.TeamServer:GetTeamMatesNum()
        USteamService:SetRichPresence("steam_player_group_size", tostring(MemberNum))
    end
end

--- 设置Steam组队状况
function TeamModule:SetSteamGroupRichPresence()
    if USteamService then
        local TeamID = Server.TeamServer:GetTeamID()
        local MemberNum = Server.TeamServer:GetTeamMatesNum()
        USteamService:SetRichPresence("steam_player_group", tostring(TeamID))
        USteamService:SetRichPresence("steam_player_group_size", tostring(MemberNum))
    end
end

--- 清空Steam组队状况
function TeamModule:ClearSteamGroupRichPresence()
    if USteamService then
        USteamService:SetRichPresence("steam_player_group", "")
        USteamService:SetRichPresence("steam_player_group_size", "")
    end
end

--- Connect字符串，用于邀请或申请加入好友游戏
function TeamModule:SetSteamConnectRichPresence()
    if USteamService then
        local TeamID = Server.TeamServer:GetTeamID()
        local PlayerID = Server.AccountServer:GetPlayerId()
        local PlayerName = Server.SDKInfoServer:GetUserName()
        
        --- 记录游戏模式
        local currentGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
        local Mode = "Other"
        if currentGameFlow == EGameFlowStageType.Lobby then
            Mode = "MP"
        elseif currentGameFlow == EGameFlowStageType.SafeHouse then
            Mode = "SOL"
        elseif currentGameFlow == EGameFlowStageType.LobbyBHD then
            Mode = "BHD"
        end
        
        -- Steam丰富状态-显示好友组队情况
        if TeamID ~= 0 then
            Module.Team:SetSteamGroupRichPresence()
        else
            Module.Team:ClearSteamGroupRichPresence()
        end
        
        -- local ConnectString = "TeamID=" .. tostring(TeamID)..", PlayerId="..tostring(PlayerID)..", Mode="..tostring(Mode)
        local ConnectString = "TeamID=" .. tostring(TeamID)..", PlayerId="..tostring(PlayerID)..", Mode="..tostring(Mode)..", PlayerName=" .. tostring(PlayerName)
        USteamService:SetRichPresence("connect", ConnectString)
        loginfo("[kirkxia]SteamConnectRichText:", ConnectString)
        
        -- 同时设置steam丰富状态
        Module.Social:SetSteamDisplayRichPresence()
    end
end


function TeamModule:OnFaceCheck(url)
    logerror("[TeamModule] OnFaceCheck:", url)
    Module.GCloudSDK:ShowFaceWindow(url)
end


function TeamModule:HandleAutomationMatch(match_mode_id)
    loginfo("[TeamModule] HandleAutomationMatch match_mode_id:", match_mode_id)
    if match_mode_id then
        local matchModeDataConfig = Facade.TableManager:GetTable("MatchModeDataConfig")
        local matchModeInfo = {}
        if matchModeDataConfig and matchModeDataConfig[match_mode_id] then
            local matchModeRow = matchModeDataConfig[match_mode_id]
            local groupIds = Server.GameModeServer:GetTDMGroupIdsByMatchId(match_mode_id)
            if matchModeRow then
                matchModeInfo.team_mode = matchModeRow.TeamMode
                matchModeInfo.map_id = matchModeRow.MapID
                matchModeInfo.game_rule = matchModeRow.GameRule
                matchModeInfo.raid_id = matchModeRow.RaidID
                matchModeInfo.sub_mode = matchModeRow.MatchSubMode
                matchModeInfo.match_mode_id = matchModeRow.MatchModeID
                matchModeInfo.game_mode = matchModeRow.GameMode
                matchModeInfo.add_member_type = 1
                matchModeInfo.equip_price = 0
                matchModeInfo.spawn_point = 0
                if groupIds and #groupIds > 0  then
                    logerror("[TeamModule] HandleAutomationMatch groupIdsp[0]",  groupIds[1])
                    Server.TeamServer:SendPersonMatchingWithParams({matchModeInfo}, false, 0, groupIds[1])
                else
                    logerror("[TeamModule] HandleAutomationMatch groupIdsp[0] e ", 0 )
                    Server.TeamServer:SendPersonMatchingWithParams({matchModeInfo}, false, 0, 0)
                end
            else
                logerror("[TeamModule] HandleAutomationMatch match_mode_id is not in MatchModeDataConfig, mode_info is nil")
            end
        else
            logerror("[TeamModule] HandleAutomationMatch match_mode_id is not in MatchModeDataConfig")
        end
    else
        logerror("[TeamModule] HandleAutomationMatch match_mode_id is nil")
    end
  
end

return TeamModule