require "DFM.Business.Proto.ProtoBaseHint"

pb = pb or {}

if __proto_require_editor_file == true then
    require "DFM.Business.Proto.pb.cs_gm_editor_pb"
end

EGMType = {
kGMTypeNone = 0,
kGMTypeTimeAdjust = 900,
kGMTypeGetCurTime = 901,
kGMTypeAddItem = 1,
kGMTypeClearDeposit = 2,
kGMTypeAddDepositCapacity = 3,
kGMTypeClearDepositCapacity = 4,
kGMTypeEquipTopExtensionBox = 5,
kGMTypeAddItemBatch = 6,
kGMTypeAddItemGod = 7,
kGMTypeAddExp = 100,
kGMTypeSetSkillScore = 101,
kGMTypeResetLevel = 102,
kGMTypeAddAccountExp = 103,
kGMTypeLoginLimit = 104,
kGMTypeForbidModifyNickname = 105,
kGMTypeSetLootScore = 106,
kGMTypeResetRenameCdTime = 107,
kGMAccountIncrPlayTime = 108,
kGMCleanAccountPlayTimeList = 109,
kGMResetReturningUserTag = 110,
kGMSetReturningUserTag = 111,
kGMTypeDelDB = 200,
kGMTypeDelAuctionTable = 201,
kGMTypeMatch = 202,
kGMTypeDelSomeDB = 203,
kGMTypeQueryCounterDB = 204,
kGMTypeUpdateCounterDB = 205,
kGMTypeAddInsurance = 300,
kGMTypeDelSafehouseDB = 400,
kGMTypeSafehouseProduce = 401,
kGMTypeSafehouseUpgrade = 402,
kGMTypeSafehouseUnlockSystem = 403,
kGMTypeSafehouseForceUpgrade = 404,
kGMTypeSafehouseUpgradeAll = 405,
kGMTypeQuestDeleteDB = 500,
kGMTypeQuestForceAccept = 501,
kGMTypeQuestForceFinish = 502,
kGMTypeQuestGetReward = 503,
kGMTypeQuestDeleteQuest = 504,
kGMTypeQuestDumpDBInServerLog = 505,
kGMTypeQuestForceSetQuestObjectiveValue = 506,
kGMTypeQuestForceFinishAllAccepted = 507,
kGMTypeQuestForceAcceptAndFinishAll = 508,
kGMTypeQuestForceSetQuestVar = 509,
kGMTypeQuestForceSetObjectiveSpentSeconds = 510,
kGMTypeQuestForceFailQuest = 511,
kGMTypeCSArmoryGetPlayerData = 512,
kGMTypeCSArmorySetPlayerData = 513,
kGMTypeAddMatchRobot = 600,
kGMTypeMatchScoreNtf = 601,
kGMTypeMatchRevenge = 602,
kGMTypePassCourse = 603,
kGMTypeSetCourse = 604,
kGMTypeMatchAlone = 605,
kGMTypeMatchLootStyle = 606,
kGMTypeMatchSnapshoot = 607,
kGMTypeMatchAddPlayer = 608,
kGMTypeMatchQuick = 609,
kGMTypeMatchSetSchemeModelScore = 610,
kGMTypeSolMatchAddPlayer = 611,
kGMTypeTDMSetPlayerHiddenScore = 612,
kGMTypeUnlockGeneralSkill = 700,
kGMTypeDelGeneralSkillDB = 701,
kGMTypeAddRoomPlayer = 800,
kGMTypeMallAddCredit = 1000,
kGMTypeMallAddFavorability = 1001,
kGMTypeMallTriggerGift = 1002,
kGMTypeAddSystemMail = 2000,
kGMTypeAddGlobalMail = 2001,
kGMTypeClearMail = 2002,
kGMTypeSCAVCD = 3000,
kGMTypeCheckSolAIRandData = 3001,
kGMTypeDsagentDSDebugArg = 4000,
kGMTypeDsagentStartAllMap = 4001,
kGMTypeChangeQuality = 5000,
kGMTypeSetMaxAllQuality = 5001,
kGMTypeTeamAddMem = 6000,
kGMTypeSettlementClearSeasonArea = 7000,
kGMTypeSettlementClearSeasonProp = 7001,
kGMTypeSettlementRaidClearEvaluatePrizeInfo = 7002,
kGMTypeSettlementGetEloPointInfo = 7003,
kGMTypeSettlementResetReqSeq = 7004,
kGMTypeChatJoinSpecWorldChatRoom = 8000,
kGMTypeChatAddWoldChatRobot = 8001,
kGMTypeChatForbidden = 8002,
kGMTypeChatRemoveSpeach = 8003,
kGMTypeCurrencyExchange = 9000,
kGMTypeMPAddItem = 9100,
kGMTypeMPClearDeposit = 9101,
kGMTypeMPUnlockWeaponAll = 9102,
kGMTypeMPWeaponMaxLevel = 9103,
kGMTypeMPAddVehicle = 9104,
kGMTypeMPVehicleExp = 9105,
kGMTypeMPSetVehicleUnlockTaskValue = 9106,
kGMTypeMPSetWeaponStar = 9107,
kGMTypeMPAddWeaponKillCount = 9108,
kGMTypeAuctionAutoBuyAllItems = 9200,
kGMTypeAuctionAllItemsExpired = 9201,
kGMTypeAuctionUnlockOneSlot = 9202,
kGMTypeAuctionSetAveragePrice = 9203,
kGMTypeAuctionDynamicPriceReCalc = 9204,
kGMTypeAuctionForceUpdateDynamicPrice = 9205,
kGMTypeAuctionQueryDynmicPrice = 9206,
kGMTypeAuctionUpdateTransactionRecord = 9207,
kGMTypeAuctionQueryPriceStatus = 9208,
kGMTypeAuctionDelPlayerOrder = 9209,
kGMTypeAuctionTriggerRackStrategy = 9210,
kGMTypeAuctionAutoTaskSwitch = 9211,
kGMTypeAuctionSpecialExpireTask = 9212,
kGMTypeAuctionQueryCounter = 9213,
kGMTypeAuctionClearAllSaleList = 9214,
kGMTypeSwitchUnlockModule = 9300,
kGMTypeCollectionAddProp = 9400,
kGMTypeCollectionClear = 9401,
kGMTypeUnlockAllPropReq = 9402,
kGMTypeCollectionUnlockMysticalSkinBatch = 9403,
kGMTypeCollectionUnlockRankTitle = 9404,
kGMTypeUnlockAllVehicleSkin = 9405,
kGMTypeCollectionCompleteGunRewardsTask = 9406,
kGMTypeCollectionSetGunRewardsGoalProgress = 9407,
kGMTypeActivityCompleteTask = 9500,
kGMTypeActivityAhsarahTravelSetProgress = 9501,
kGMTypeWeaponAllSkin = 9600,
kGMTypeWeaponAllFancyColorSkin = 9601,
kGMTypeHeroUnlockAll = 9700,
kGMTypeHeroGrowLineQuickGraduation = 9701,
kGMTypeHeroGrowLineFinishGoal = 9702,
kGMTypeHeroGrowLineJumpLevel = 9703,
kGMTypeHeroGrowLineResetGoal = 9704,
kGMTypeHeroGrowLineSetGoal = 9705,
kGMTypeSSHeroRecruitFinish = 9706,
kGMTypeSSHeroRecruitFinishGoal = 9707,
kGMTypeSSHeroRecruitReset = 9708,
kGMTypeSSHeroAddArmedPropFashionID = 9709,
kGMTypeSSHeroSetArmedPropLotteryQuality = 9710,
kGMTypeSSHeroReSetArmedPropLottery = 9711,
kGMTypeHeroGrowLineResetLevel = 9712,
kGMTypeHeroGMSetBadge = 9713,
kGMTypeSetGuideStage = 9800,
kGMTypeSetPlayerAccountBatch = 9801,
kGMTypeQueryPlayerCreditData = 10001,
kGMTypeQueryPlayerScnLimitData = 10002,
kGMTypePrintScnLimitInfo = 10003,
kGMTypeClearEasterEggAwardCD = 11001,
kGMTypeClearEasterEggAwardClearTakeData = 11002,
kGMTypeReqTakeEasterEggAward = 11003,
kGMTypeSeasonSetRankScore = 12001,
kGMTypeSeasonSetMPRankScore = 12002,
kGMTypeSeasonSetRankCoins = 12003,
kGMTypeSecurityCompensate = 12004,
kGMTypeSeasonSetMPCommanderScore = 12005,
kGMTypeCompleteAchievements = 13001,
kGMTypeCompleteCareerAchievements = 13002,
kGMTypeCompleteSetSteamProgress = 13003,
kGMTypeCompleteEquipRental = 14001,
kGMTypeGetMandelData = 15001,
kGMTypeChangeMandelData = 15002,
kGMTypeBhdSettlement = 16001,
}
GMExecuteState = {
GMExecuteStateNotExecute = 0,
GMExecuteStateFail = 1,
GMExecuteStateSuccess = 2,
GMExecuteStateNotFinish = 3,
}
pb.__pb_CSGMReq = {
    gm_type = 0,
    reason = 0,
}
pb.__pb_CSGMReq.__name = "CSGMReq"
pb.__pb_CSGMReq.__index = pb.__pb_CSGMReq
pb.__pb_CSGMReq.__pairs = __pb_pairs

pb.CSGMReq = { __name = "CSGMReq", __service="gm", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSGMReq : ProtoBase
---@field public gm_type number
---@field public args string[]
---@field public reason number

---@return pb_CSGMReq
function pb.CSGMReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSGMRes = {
    result = 0,
    reason = 0,
    gm_type = 0,
    tip = "",
    content = "",
}
pb.__pb_CSGMRes.__name = "CSGMRes"
pb.__pb_CSGMRes.__index = pb.__pb_CSGMRes
pb.__pb_CSGMRes.__pairs = __pb_pairs

pb.CSGMRes = { __name = "CSGMRes", __service="gm", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSGMRes : ProtoBase
---@field public result number
---@field public reason number
---@field public gm_type number
---@field public tip string
---@field public content string

---@return pb_CSGMRes
function pb.CSGMRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSGMNotifyNtf = {
    gm_type = 0,
    content = "",
}
pb.__pb_CSGMNotifyNtf.__name = "CSGMNotifyNtf"
pb.__pb_CSGMNotifyNtf.__index = pb.__pb_CSGMNotifyNtf
pb.__pb_CSGMNotifyNtf.__pairs = __pb_pairs

pb.CSGMNotifyNtf = { __name = "CSGMNotifyNtf", __service="gm", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSGMNotifyNtf : ProtoBase
---@field public gm_type number
---@field public content string

---@return pb_CSGMNotifyNtf
function pb.CSGMNotifyNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


