----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGuide)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local GPUINavigationStrategy_Hittest = import("GPUINavigationStrategy_Hittest")
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local UGPInputDelegates = import "GPInputDelegates"
local EGPInputType = import "EGPInputType"
-- END MODIFICATION

local InputSummaryItemHD = require "DFM.Business.Module.CommonBarModule.UI.BottomBarHD.InputSummaryItemHD"
local GuideConfig = require "DFM.Business.Module.GuideModule.GuideConfig"
---@class UGPInputHelper
local UGPInputHelper = import("GPInputHelper")

---@class GuideHDCommonDescUI : LuaUIBaseView
local GuideHDCommonDescUI = ui("GuideHDCommonDescUI")

local function log(...) loginfo("[GuideHDCommonDescUI]", ...) end
local function err(...) logerror("[GuideHDCommonDescUI]", ...) end

function GuideHDCommonDescUI:Ctor()
    self._wtTitle = self:Wnd("wtTitle", UITextBlock)
    self._wtDescItem = self:Wnd("wtDescItem", UIWidgetBase)
    self._wtDescItemTitle = self._wtDescItem:Wnd("wtTitle", UITextBlock)
    self._wtDescItemDesc = self._wtDescItem:Wnd("wtDesc", UITextBlock)
    self._wtInputSpace = self:Wnd("WBP_TopBarHD_InputSummary", InputSummaryItemHD)
    self._wtInputF = self:Wnd("WBP_TopBarHD_InputSummary_1", InputSummaryItemHD)
    self._wtMaskBg = self:Wnd("_wtSwallowImg", UIWidgetBase)
    self._wtMaskBg:Event("OnMouseButtonDownEvent", self._OnMaskClicked, self)
    self._wtPercent = self:Wnd("RadialImage_161", UIWidgetBase)
    self._wtDFScrollBox = self:Wnd("DFScrollBox_117", UIWidgetBase)

    self._wtInputF:EnableAutoSetVisibility(false)
    self._wtInputSpace:EnableAutoSetVisibility(false)

    self._wtInputF:Hidden()
    self._wtInputSpace:Hidden()

    self:Reset()
end

--==================================================
--region Life function
function GuideHDCommonDescUI:Reset()
    self._showData = nil
    if self._hActionGuideSkip then
        self:RemoveInputActionBinding(self._hActionGuideSkip)
        self._hActionGuideSkip = nil
    end
    if self._hActionGuideGoToSummary then
        self:RemoveInputActionBinding(self._hActionGuideGoToSummary)
        self._hActionGuideGoToSummary = nil
    end
    self._bSelfActive = false
    self._callbackOnEnd = nil
    self._bSetOnlyUI = false
    self._bPause = false

    self._bAutoTimeEnded = true
    self._beginPercentTime = 0
    self._totalTickTime = 0
end

function GuideHDCommonDescUI:RecycleSelf()
    self:Hide()
    if self._bSelfActive then
        Module.Guide.Field:ReturnCacheSubUI(UIName2ID.GuideHDCommonDescUI, self)
        self._bSelfActive = false
    end
end

function GuideHDCommonDescUI:OnOpen()
end

function GuideHDCommonDescUI:OnClose()
    self._bClose = true

    log("OnClose, RemoveDisplayActoinBingingForHandle GuideSkip", self._hActionGuideSkip)
    self:_RemoveTickTimer()

    if self._hActionGuideSkip then
        self:RemoveInputActionBinding(self._hActionGuideSkip)
        self._hActionGuideSkip = nil
    end
    if self._hActionGuideGoToSummary then
        self:RemoveInputActionBinding(self._hActionGuideGoToSummary)
        self._hActionGuideGoToSummary = nil
    end

    self._bSetOnlyUI = false
    Module.Guide:SetGuideInputGate("GuideHDCommonDescUI", false)
    UGPInputHelper.WantInputMode_GameOnly(GetGameInstance(), "GuideHDCommonDescUI", false, false)
    UGPInputHelper.WantInputMode_UIOnly(GetGameInstance(), "GuideHDCommonDescUI", false, false)
    -- BEGIN MODIFICATION @ VIRTUOS
    self:_DisableGamepad()
    -- END MODIFICATION
end

---@param showData GuideHDPopFunctionDescUI_ShowData
function GuideHDCommonDescUI:SetData(showData)
    if not showData then return end
    self._showData = showData
    self._callbackOnEnd = showData.callbackOnEnd
end

function GuideHDCommonDescUI:OnShow()
    Facade.UIManager:SetZOrderInFrameRoot(self, UILayer2ZOrder[EUILayer.Mask] + 20)
end

function GuideHDCommonDescUI:OnShowBegin()
    if not self._showData then return end
    self._bSelfActive = true

    self._bAutoTimeEnded = true;
    -- prevent skip before progress bar
    if self:_NeedProgressBar() then
        self._bAutoTimeEnded = false;
    end

    self:_UpdateShow()
    self:_AddTickTimer()
    self:_BindInput()

    -- mask and focus
    if not self._showData.bNotMask then
        self._bSetOnlyUI = true
        Module.Guide:SetGuideInputGate("GuideHDCommonDescUI", true)
        --inputMonitor:SetActionsPriorityGate(EDisplayInputActionPriority.UI_Guide, true)
        UGPInputHelper.WantInputMode_UIOnly(GetGameInstance(), "GuideHDCommonDescUI", true, false)
    else
        UGPInputHelper.WantInputMode_GameOnly(GetGameInstance(), "GuideHDCommonDescUI", true, false)
    end

    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UI_Tutorial_Popup_Small)
    self:_PlayAudio()

    -- BEGIN MODIFICATION @ VIRTUOS :
    if IsHD() then
        self._OnNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(
            self._handleInputTypeChanged, self))
        self:_handleInputTypeChanged(WidgetUtil.GetCurrentInputType())
    end
    --END MODIFICATION
end

function GuideHDCommonDescUI:_BindInput()
    -- TODO: click and space can bind function by string to a GuideXxxLogic, custom callback implementation

    -- space btn
    if self._showData.bUseBtnSkipGuide then
        local _OnClick = function()
            self:_OnSkipBtnClick()
        end
        self._wtInputSpace:SetData("GuideSkip", _OnClick)
        log("OnShowBegin, AddDisplayActionBinding GuideSkip", self._hActionGuideSkip)
        if not self._hActionGuideSkip then
            self._hActionGuideSkip = self:AddInputActionBinding(
                "GuideSkip", EInputEvent.IE_Pressed, self._OnSkipBtnClick, self, EDisplayInputActionPriority.UI_Guide)
        end
    end
    if self._showData.bUseBtnGotoSummary then
        self._wtInputF:SetData("GoToGuideSummary")
        if not self._hActionGuideGoToSummary then
            self._hActionGuideGoToSummary = self:AddInputActionBinding(
                "GoToGuideSummary", EInputEvent.IE_Pressed, self._OnGoToBtnClick, self, EDisplayInputActionPriority.UI_Guide)
        end
    end

    self:_UpdateFunctionBtnVisibility()
end

-- BEGIN MODIFICATION @ VIRTUOS :
-- 根据输入设备的不同，切换不同的输入Icon
function GuideHDCommonDescUI:_handleInputTypeChanged(inputType)
    if not IsHD() then
        return
    end

    self:_UpdateShow()

    if WidgetUtil.IsGamepad() then
        -- NOTICE:  2025/8/6 不禁止低优先级按键了, 兼容性存疑
        Module.Guide:SetGuideInputGate("GuideHDCommonDescUI", false)
        -- Module.Guide.Config.EGuideEvent.evtGuideNavChanged:Invoke("add", "GuideHDCommonDescUI", {
        --     self._wtDFScrollBox
        --  })
    else
        if self._bSetOnlyUI then
            Module.Guide:SetGuideInputGate("GuideHDCommonDescUI", true)
        end

        Module.Guide.Config.EGuideEvent.evtGuideNavChanged:Invoke("remove", "GuideHDCommonDescUI")
    end
end

function GuideHDCommonDescUI:_DisableGamepad()
    if not IsHD() then
        return
    end
    --移除输入设备切换事件
    if self._OnNotifyInputTypeChangedHandle then
        UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._OnNotifyInputTypeChangedHandle)
        self._OnNotifyInputTypeChangedHandle = nil
    end

    -- 导航
    Module.Guide.Config.EGuideEvent.evtGuideNavChanged:Invoke("remove", "GuideHDCommonDescUI")

    -- 恢复默认导航配置
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
end

-- END MODIFICATION


function GuideHDCommonDescUI:OnHide()
    self:_PauseTickTimer()
    self:StopAllAnimations()
    self:RemoveAllLuaEvent()

    log("OnHide, RemoveDisplayActoinBingingForHandle GuideSkip", self._hActionGuideSkip)

    if self._hActionGuideSkip then
        self:RemoveInputActionBinding(self._hActionGuideSkip)
        self._hActionGuideSkip = nil
    end
    if self._hActionGuideGoToSummary then
        self:RemoveInputActionBinding(self._hActionGuideGoToSummary)
        self._hActionGuideGoToSummary = nil
    end
    if self._bSetOnlyUI then
        Module.Guide:SetGuideInputGate("GuideHDCommonDescUI", false)
        --inputMonitor:SetActionsPriorityGate(EDisplayInputActionPriority.UI_Guide, false)
        UGPInputHelper.WantInputMode_UIOnly(GetGameInstance(), "GuideHDCommonDescUI", false, false)
        self._bSetOnlyUI = false
    else
        UGPInputHelper.WantInputMode_GameOnly(GetGameInstance(), "GuideHDCommonDescUI", false, false)
    end

    -- BEGIN MODIFICATION @ VIRTUOS
    self:_DisableGamepad()
    -- END MODIFICATION
end

function GuideHDCommonDescUI:OnInitExtraData(showData)
    self:SetData(showData)
end

function GuideHDCommonDescUI:_UpdateShow()
    if not self._showData then return end

    self._wtTitle:SetText(self._showData.title)
    if not self._showData.descInfo or #self._showData.descInfo == 0 then
        self._wtDescItemTitle:SetText("")
        self._wtDescItemDesc:SetText("")
        return
    end
    local descInfo = self._showData.descInfo[1]
    self._wtDescItemTitle:SetText(descInfo.Title)
    self._wtDescItemDesc:SetText(descInfo.DescText)
    self:SetTipsPosition(self._showData.position)

    -- 是启用点击遮罩
    self._wtMaskBg:SetVisibility(self._showData.bNotMask and ESlateVisibility.SelfHitTestInvisible or ESlateVisibility.Visible)

    -- BEGIN MODIFICATION @ VIRTUOS :
    if IsHD() then
        if WidgetUtil.IsGamepad() then
            -- 导航组 当显示字符串过长，出现滑动条时，支持使用右舵操作移动滑动条
            -- 2025/8/6 目前没有过长的文本? 且不需要聚焦(也没聚焦的美术支持)
            -- self._wtNavGroup = WidgetUtil.RegisterNavigationGroup(self, self, "Hittest")
            -- self._wtNavGroup:SetScrollRecipient(self._wtDFScrollBox)

            -- 让当前ui的A键优先级最高
            WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)

            -- text
            if self._showData.DescInfoForHDWithGamepad then
                local length = #self._showData.DescInfoForHDWithGamepad
                if length > 0 then
                    local descInfoWithGamepad = self._showData.DescInfoForHDWithGamepad[1]
                    self._wtDescItemTitle:SetText(descInfoWithGamepad.Title)
                    self._wtDescItemDesc:SetText(descInfoWithGamepad.DescText)
                end
                --使用PS手柄时，如果显示信息和XBOX不一样，则替换PS信息
                if not WidgetUtil.IsXBoxGamepad() and length > 1 then
                    local descInfoWithGamepad = self._showData.DescInfoForHDWithGamepad[2]
                    self._wtDescItemTitle:SetText(descInfoWithGamepad.Title)
                    self._wtDescItemDesc:SetText(descInfoWithGamepad.DescText)
                end
            end
        end
    end
    --END MODIFICATION


    -- 0 不显示进度条
    -- 1 显示进度条
    self:SetType(self:_CanActiveProgressBar() and 1 or 0)
    -- settype will reset the percent?
    if self._bAutoTimeEnded then
        self._wtPercent:SetPercent(0)
    end
    self:_UpdateFunctionBtnVisibility()
end

function GuideHDCommonDescUI:_OnSkipBtnClick()
    if self._bPause then
        return
    end

    if self:_CanActiveProgressBar() and not self._bAutoTimeEnded then
        return
    end

    if self._callbackOnEnd then
        self:_callbackOnEnd()
        return
    end

    self:RecycleSelf()
end

function GuideHDCommonDescUI:_UpdateFunctionBtnVisibility()
    if not self._showData then return end

    local bVisible1 = self._showData.bUseBtnSkipGuide
    local bVisible2 = self._showData.bUseBtnGotoSummary

    if self:_CanActiveProgressBar() then
        bVisible1 = self._bAutoTimeEnded and self._showData.bUseBtnSkipGuide
        bVisible2 = self._bAutoTimeEnded and self._showData.bUseBtnGotoSummary
    end

    self._wtInputSpace:SetVisibility(bVisible1 and ESlateVisibility.SelfHitTestInvisible or ESlateVisibility.Collapsed)
    self._wtInputF:SetVisibility(bVisible2 and ESlateVisibility.SelfHitTestInvisible or ESlateVisibility.Collapsed)
end

function GuideHDCommonDescUI:OnAnimFinished(anim)
    if anim == self.Anima_in then
        self:PlayWidgetAnim(self.Anima_loop, 0)
    elseif anim == self.WBP_Guide_FirstTimeInSystem_2q0 then
        if self._cbOnAnimFinished_WBP_Guide_FirstTimeInSystem_2q0 then
            self._cbOnAnimFinished_WBP_Guide_FirstTimeInSystem_2q0()
        end
    end
end

--region io
function GuideHDCommonDescUI:_OnGoToBtnClick()
    log("GuideHDCommonDescUI:_OnGoToBtnClick")
    -- GuideLogic[self._showData.gotoMethod](self)
end

function GuideHDCommonDescUI:_OnMaskClicked()
    self:_OnSkipBtnClick()
    return {}
end

--endregion

function GuideHDCommonDescUI:Pause()
    log("Pause", self._bPause)
    self._bPause = true
    self:SetVisibility(ESlateVisibility.Collapsed)
end

function GuideHDCommonDescUI:Restart()
    log("Restart", self._bPause)
    self._bPause = false
    self:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
end

function GuideHDCommonDescUI:_AddTickTimer()
    log("_AddTickTimer", self._checkTimerHandle)
    if self._showData and self._showData.autoTime and self._showData.autoTime > 0 then
        self._beginPercentTime = TimeUtil.GetCurrentTimeMillis()
        self._totalTickTime = self._showData.autoTime
        if self._checkTimerHandle then
            self._checkTimerHandle:Start()
            return
        end
        self._bAutoTimeEnded = false
        self._checkTimerHandle = Timer:NewIns(0.01, 0)
        self._checkTimerHandle:AddListener(self._UpdatePercent, self)
        self._checkTimerHandle:Start()
    end
end

function GuideHDCommonDescUI:_PauseTickTimer()
    log("_PauseTickTimer", self._checkTimerHandle)
    if self._checkTimerHandle then
        self._checkTimerHandle:Stop()
    end
end

function GuideHDCommonDescUI:_RemoveTickTimer()
    log("_RemoveTickTimer", self._checkTimerHandle)
    if self._checkTimerHandle then
        self._checkTimerHandle:Release()
        self._checkTimerHandle = nil
    end
end

function GuideHDCommonDescUI:_UpdatePercent()
    if self._beginPercentTime > 0 then
        local curTime = TimeUtil.GetCurrentTimeMillis()
        if curTime >= self._beginPercentTime + self._totalTickTime then
            self:_OnTimeEnd()
        end

        local percent = (curTime - self._beginPercentTime) / self._totalTickTime
        percent = math.min(1, percent)
        percent = math.max(0, percent)
        percent = 1 - percent -- RTL
        self._wtPercent:SetPercent(percent)
    end
end

function GuideHDCommonDescUI:_OnTimeEnd()
    self._bAutoTimeEnded = true
    self:_PauseTickTimer()
    self._cbOnAnimFinished_WBP_Guide_FirstTimeInSystem_2q0 = nil

    if self._showData.autoTimeEndMethod == 0 then
        -- 处理进度条结束的动画
        if self:_CanActiveProgressBar() then
            self:SetType(2) -- prepare for anim
            self._cbOnAnimFinished_WBP_Guide_FirstTimeInSystem_2q0 = function()
                if self:_CanActiveProgressBar() then
                    self:_UpdateFunctionBtnVisibility()
                end
            end
            self:PlayWidgetAnim(self.WBP_Guide_FirstTimeInSystem_2q0)
        end
    elseif self._showData.autoTimeEndMethod == 1 then
        -- 需要有进度条配置才才去关闭自己
        if self:_CanActiveProgressBar() then
            --
        else
            -- 重置状态?
            return
        end

        if self._callbackOnEnd then
            self:_callbackOnEnd()
            return
        end

        self:RecycleSelf()
    end
end

--endregion
---------------------

function GuideHDCommonDescUI:_PlayAudio()
    if not self._showData then return end
    local audioEvent = self._showData.tipsAudio
    if audioEvent then
        Facade.SoundManager:PlayGuideAudio(audioEvent)
        self._curAudio = audioEvent
    end
end

function GuideHDCommonDescUI:_CanActiveProgressBar()
    return self._showData and
        (self._showData.showType == 1 or self._showData.showType == 2 and WidgetUtil.IsGamepad()) and
        self._showData.autoTime > 0
end

function GuideHDCommonDescUI:_NeedProgressBar()
    return self._showData and
        (self._showData.showType == 1 or self._showData.showType == 2) and
        self._showData.autoTime > 0
end

return GuideHDCommonDescUI
