----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------


--- @class MorgenEventLogPop : LuaUIBaseView
local MorgenEventLogPop = ui("MorgenEventLogPop")
local ActivityConfig = Module.Activity.Config
local ActivityLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"
local CommonPopWindows = require "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindows"
local ConfigManager = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerConfigManager"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import "EGPUINavWidgetFocusedAction"
local EGPUINavScrollingCondition = import("EGPUINavScrollingCondition")

function MorgenEventLogPop:Ctor()
    self.curIndex = 1
    self._wtCommonPopWin = self:Wnd("wtRootWindow", CommonPopWindows)
    self._wtCommonPopWin:BindCloseCallBack(CreateCallBack(self.OnCloseBtnClicked, self))
    self._wtCommonPopWin:SetBackgroudClickable(true)

    self._wtEventList = UIUtil.WndScrollBox(self, "DFScrollBox_72", self.OnGetEventCount, self.OnProcessEventWidget)
    self._wtEnemyList = UIUtil.WndWaterfallScrollBox(self, "DFWaterfallScrollView", self.OnGetEnemyCount, self.OnProcessEnemyWidget)

    self._wtTitleTabGroup = UIUtil.WndTabGroupBox(self, "wtDFTabV3GroupBoxClass3StaticText", self._OnGroupBoxCount,
    self._OnGroupBoxWidget, self._OnGroupBoxIndexChanged)

    ---空状态slot
    self._wtEmptySlot = self:Wnd("EmptySlot", UIWidgetBase)
 
    if IsHD() and self._wtTitleTabGroup then
        self._wtTabKeyIconLeft = self._wtTitleTabGroup:Wnd("WBP_GamepadKeyIconBox_Left", HDKeyIconBox)
        self._wtTabKeyIconRight = self._wtTitleTabGroup:Wnd("WBP_GamepadKeyIconBox_Right", HDKeyIconBox)
    end
end

-----------------------------------------------------生命周期-----------------------------------------------------
--#region
function MorgenEventLogPop:OnInitExtraData(curCycle)
    self.tempEventInfo = ConfigManager.GetNPCConfigTable()
    self.tempEnemyInfo = ConfigManager.GetEnemyConfigTable()
    self.curCycle = curCycle or 0

    --- TODO Build Data
    self:BuildData()
end

function MorgenEventLogPop:OnOpen()
    self._wtCommonPopWin:SetTitle(ActivityConfig.Loc.MogenEventPopText[1])
end

function MorgenEventLogPop:OnShowBegin()
    self:InitGamepad()
    self:RefreshUI()
end

function MorgenEventLogPop:OnHideBegin()
    self:DisableGamepad()
end
--#endregion

-----------------------------------------------------响应操作-----------------------------------------------------
--#region

function MorgenEventLogPop:RefreshUI()
    local curList, preList, curInfo
    if self.curIndex == 1 then
        curList = self._wtEventList
        preList = self._wtEnemyList
        curInfo = self.eventInfo
    else
        curList = self._wtEnemyList
        preList = self._wtEventList
        curInfo = self.enemyInfo
    end

    local length = #curInfo
    local bIsEmpty = length == 0
    self:SetEmptyStyle(bIsEmpty)
    preList:Collapsed()

    if bIsEmpty then
        curList:Collapsed()
    else
        curList:Visible()
        curList:RefreshAllItems()
        curList:ScrollToStart()
    end
end

function MorgenEventLogPop:SetEmptyStyle(bIsEmpty)
    Facade.UIManager:RemoveSubUIByParent(self, self._wtEmptySlot)
    if bIsEmpty then
        self._wtEmptySlot:Visible()
        local weakUIIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.CommonEmptyContent, self._wtEmptySlot)
        local uiIns = getfromweak(weakUIIns)
        if uiIns then
            uiIns:BP_SetTypeWithParam(1)
            uiIns:Visible()
            uiIns:BP_SetText(Module.Store.Config.Loc.NothingContent)
        end
    else
        self._wtEmptySlot:Collapsed()
    end
end

--- 事件列表
function MorgenEventLogPop:_getTableLength(t)
    local n = 0
    for key, value in pairs(t) do
        n = n + 1
    end
    return n
end

--- 事件列表
function MorgenEventLogPop:OnGetEventCount()
    return #self.eventInfo or 0
end

function MorgenEventLogPop:OnProcessEventWidget(index, widget)
    local info = self.eventInfo[index + 1]
    widget:RefreshInfo(info, self.curCycle)
end

--- 敌人列表
function MorgenEventLogPop:OnGetEnemyCount()
    return #self.enemyInfo or 0
end

function MorgenEventLogPop:OnProcessEnemyWidget(index, widget)
    local info = self.enemyInfo[index + 1]
    widget:RefreshInfo(info)
end

--- 导航栏
function MorgenEventLogPop:_OnGroupBoxCount()
    return 2
end

function MorgenEventLogPop:_OnGroupBoxWidget(position, itemWidget)
    if position == 0 then
        itemWidget:SetMainTitle(ActivityConfig.Loc.MogenEventPopText[2])
    else
        itemWidget:SetMainTitle(ActivityConfig.Loc.MogenEventPopText[3])
    end
end

function MorgenEventLogPop:_OnGroupBoxIndexChanged(position)
    self.curIndex = position + 1
    self:RefreshUI()
end

--#endregion

-----------------------------------------------------其他函数-----------------------------------------------------
--#region

function MorgenEventLogPop:BuildData()
    self.eventInfo = {}
    self.enemyInfo = {}
    local n = 0
    -- table.sort(self.tempEventInfo, function(a, b)
    --         return a.NPCID < b.NPCID
    --     end)
    for key, value in pairs(self.tempEventInfo) do
        if value then
            n = n + 1
            self.eventInfo[n] = value
        end
    end
    n = 0
    for key, value in pairs(self.tempEnemyInfo) do
        if value and value.isVisible == 1 then
            n = n + 1
            self.enemyInfo[n] = value
        end
    end
end

function MorgenEventLogPop:OnNavBack()
    self:OnCloseBtnClicked()
    return true
end

function MorgenEventLogPop:OnCloseBtnClicked()
    Facade.UIManager:CloseUI(self)
end

function MorgenEventLogPop:InitGamepad()
    if not IsHD() then return end
    self:DisableGamepad()

    -- tab按键绑定
    if self._wtTitleTabGroup then
        self._SelectNextTabHandle = self:AddInputActionBinding("Common_SwitchToNextTab_Trigger", EInputEvent.IE_Pressed
                    , self._wtTitleTabGroup.OnNext, self._wtTitleTabGroup, EDisplayInputActionPriority.UI_Pop)
        self._SelectPrevTabHandle = self:AddInputActionBinding("Common_SwitchToPrevTab_Trigger", EInputEvent.IE_Pressed
                    , self._wtTitleTabGroup.OnPrev, self._wtTitleTabGroup, EDisplayInputActionPriority.UI_Pop)
    end

    if self._wtTabKeyIconLeft then
        self._wtTabKeyIconLeft:SelfHitTestInvisible()
        self._wtTabKeyIconLeft:SetOnlyDisplayOnGamepad(true)
        self._wtTabKeyIconLeft:InitByDisplayInputActionName("Common_SwitchToPrevTab_Trigger", true, 0, false)
    end

    if self._wtTabKeyIconRight then
        self._wtTabKeyIconRight:SelfHitTestInvisible()
        self._wtTabKeyIconRight:SetOnlyDisplayOnGamepad(true)
        self._wtTabKeyIconRight:InitByDisplayInputActionName("Common_SwitchToNextTab_Trigger", true, 0, false)
    end

    -- 事件列表导航
    if not self._eventListGroup then
        self._eventListGroup = WidgetUtil.RegisterNavigationGroup(self._wtEventList, self, "Hittest")
    end
    if self._eventListGroup then
        self._eventListGroup:AddNavWidgetToArray(self._wtEventList)
        self._eventListGroup:SetScrollInfo(self._wtEventList, EGPUINavScrollingCondition.None)
    end

    -- 敌人列表导航
    if not self._enemyListGroup then
        self._enemyListGroup = WidgetUtil.RegisterNavigationGroup(self._wtEnemyList, self, "Hittest")
    end
    if self._enemyListGroup then
        self._enemyListGroup:AddNavWidgetToArray(self._wtEnemyList)
        self._enemyListGroup:SetScrollInfo(self._wtEnemyList, EGPUINavScrollingCondition.None)
    end
end

function MorgenEventLogPop:DisableGamepad()
    if not IsHD() then return end

    if self._SelectNextTabHandle then
        self:RemoveInputActionBinding(self._SelectNextTabHandle)
    end

    if self._SelectPrevTabHandle then
        self:RemoveInputActionBinding(self._SelectPrevTabHandle)
    end

    WidgetUtil.RemoveNavigationGroup(self)
    self._prizesListGroup = nil
    self._eventListGroup = nil
    self._enemyListGroup = nil
end
--#endregion

return MorgenEventLogPop