----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMWeaponAssembly)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class ActivityTaraMainItem : LuaUIBaseView
local ActivityTaraMainItem = ui("ActivityTaraMainItem")
local ActivityTaraItem1 = require "DFM.Business.Module.ActivityModule.UI.Tara.ActivityTaraItem1"
local ActivityTaraItem2 = require "DFM.Business.Module.ActivityModule.UI.Tara.ActivityTaraItem2"
local ActivityTaraItem3 = require "DFM.Business.Module.ActivityModule.UI.Tara.ActivityTaraItem3"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local Config = Module.Activity.Config

function ActivityTaraMainItem:Ctor()
    self._wtItem1 = self:Wnd("NewWidgetBlueprint2", ActivityTaraItem1)
    self._wtItem2 = self:Wnd("NewWidgetBlueprint3", ActivityTaraItem2)
    self._wtItem3 = self:Wnd("WBP_PatrolAsala_CommentItem_Q1ZNK", ActivityTaraItem3)
end

function ActivityTaraMainItem:InitData(activityID, data, index, defaultIndex, hero, list, count)
    self._activityID = activityID
    self._data  = data
    self._index = index
    if data then
        if data.milestone then
            self._wtItem1:InitData(activityID, data.milestone, defaultIndex, hero)
            self._wtItem1:SelfHitTestInvisible()
            self._wtItem1:SetIndex(index)
        else
            self._wtItem1:Collapsed()
        end
        if data.thumbs_up then
            self._wtItem2:InitData(activityID, data.thumbs_up, index, list, count)
            self._wtItem2:SelfHitTestInvisible()
            self._wtItem2:SetIndex(index)
        else
            self._wtItem2:Collapsed()
        end
        if data.comment then
            self._wtItem3:InitData(activityID, data.comment, index, list, count)
            self._wtItem3:SelfHitTestInvisible()
            self._wtItem3:SetIndex(index)
        else
            self._wtItem3:Collapsed()
        end
        Timer.DelayCall(0.1, self.SetHandleState, self, index == defaultIndex)
    end
    self:Visible()
end

function ActivityTaraMainItem:SetHandleState(isbool)
    if isbool then
        WidgetUtil.SetUserFocusToWidget(self, isbool)
    end
end

function ActivityTaraMainItem:OnShowBegin()
    self:_AddEventListener()
end

function ActivityTaraMainItem:OnHideBegin()
    self:RemoveAllLuaEvent()
end

function ActivityTaraMainItem:_AddEventListener()
    self:AddLuaEvent(Config.evtAddTaraHandleAdaptation, self._OnAddTaraHandleAdaptation, self)
end

function ActivityTaraMainItem:_OnAddTaraHandleAdaptation(handleType, ...)
    if handleType == ETaraHandleType.Hide then
        --特发逻辑
        self:Collapsed()
    end
end

function ActivityTaraMainItem:OnClose()
end

return ActivityTaraMainItem