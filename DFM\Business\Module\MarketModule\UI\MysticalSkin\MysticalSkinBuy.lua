----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMMarket)
----- LOG FUNCTION AUTO GENERATE END -----------



local MysticalSkinBuy = ui("MysticalSkinBuy")
local IVShopItemTemplate = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.IVShopItemTemplate"
local MarketConfig = require "DFM.Business.Module.MarketModule.MarketConfig"
local MarketLogic = require "DFM.Business.Module.MarketModule.Logic.MarketLogic"
local CommonPopWindows = require "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindows"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ShopHelperTool = require "DFM.StandaloneLua.BusinessTool.ShopHelperTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local ItemDetailView = require "DFM.Business.Module.ItemDetailModule.UI.ItemDetailView"
local ItemLabelMarkBtn = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.ItemLabelMarkBtn"
local ECheckBoxState = import "ECheckBoxState"
local HDKeyIconBox = require "DFM.Business.Module.CommonWidgetModule.UI.HD.HDKeyIconBox"
local UDFMGameNotch = import "DFMGameNotch"
local DFMGameNotch = UDFMGameNotch.Get(GetGameInstance())
local FetchType =
{
    None = 0,
    Rarity = 1,
    pendentRarity = 2,
    Wear = 3,
    Notice = 4,
    Sort = 5,
    Price = 6,
    Refresh = 7,
    PageUp = 8,
    PageDown = 9,
}

--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
-- 输入设备相关
local UGPInputDelegates = import "GPInputDelegates"
local EGPInputType = import"EGPInputType"
--- END MODIFICATION

function MysticalSkinBuy:Ctor()
    self._itemId = nil
    self._saleListInfo = nil
    self._selectedPos = 0
    self._selectedCell = nil
    self._bUnlockedBefore = false -- 避免tick无用调用刷新详情页
    self._bLockedBefore = false -- 避免tick无用调用刷新详情页
    self._fetchType = FetchType.None
    self._rarityScreenParams = {
        colorIds = {},
        patternIds = {},
        attachmentIds = {},
    }
    -- 数据拉取参数
    self._fetchParams = {
        rarityId = 0,
        colorId = 0,
        patternId = 0,
        attachmentId = 0,
        minWear = 0,
        maxWear = 0,
        noticeType = 0,
        sortType = 0,
        minPrice = 0,
        maxPrice = 0,
        page = 1,
    }
    self._wtMainCanvasPanel = self:Wnd("DFCanvasPanel_0", UIWidgetBase)
    self._wtItemViewList = UIUtil.WndWaterfallScrollBox(self, "wtWaterFallList", self._OnGetItemCount, self._OnProcessItemWidget)
    self._wtZoomRateDroDownRarity = UIUtil.WndDropDownBox(self, "WBP_DFCommonDropDownBox", self.OnRarityCheckedTabIndexChanged)
    self._wtZoomRateDroDownWear = UIUtil.WndDropDownBox(self, "WBP_DFCommonDropDownBox_1", self.OnWearCheckedTabIndexChanged)
    self._wtZoomRateDroDownNotice = UIUtil.WndDropDownBox(self, "WBP_DFCommonDropDownBox_2", self.OnNoticeCheckedTabIndexChanged)
    self._wtZoomRateDroDownSort = UIUtil.WndDropDownBox(self, "WBP_DFCommonDropDownBox_3", self.OnSortCheckedTabIndexChanged)
    self._wtRarityScreenBtn = self:Wnd("WBP_CommonButtonV2S2_37", DFCommonButtonOnly)
    self._wtRarityScreenBtn:Event("OnClicked", self._OnRarityScreenBtnClicked, self)
    self._wtRarityScreenIcon = self:Wnd("DFImage_97", UIImage)
    self._wtScreenBtn = self:Wnd("WBP_CommonIconButton_1", DFCommonButtonOnly)
    self._wtScreenBtn:Event("OnClicked", self._OnScreenBtnClicked, self)
    self._wtItemDetailView = self:Wnd("WBP_ItemDetailView", ItemDetailView)
    self._wtLabelMarkBtn = self:Wnd("MarkBtn", ItemLabelMarkBtn)
    self._wtLabelMarkBtn:Event("OnCheckStateChanged", self._OnLabelMarkBtnClick, self)
    self._wtBuyBtn = nil
    self._wtRefreshButton = self:Wnd("RefreshButton", UIButton)
    if self._wtRefreshButton then
        self._wtRefreshButton:Event("OnClicked", self._OnRefreshBtnClicked, self)
        self._wtRefreshButton:Event("OnDeClicked", self._OnRefreshBtnDeClicked, self)
    end
    self._wtEmptyBgPanel = self:Wnd("DFCanvasPanel_Empty", UIWidgetBase)
    self._wtEmptyBgSlot = self:Wnd("EmptySlot", UIWidgetBase)
    self._wtEmptyBgPanel_1 = self:Wnd("DFCanvasPanel_33", UIWidgetBase)
    self._wtEmptyBgSlot_1 = self:Wnd("EmptySlot_1", UIWidgetBase)
    self._refreshTimerHandle = nil
    self._wtPageUpBtn = self:Wnd("DFButton_1", UIButton)
    self._wtPageUpBtn:Event("OnClicked", self.OnPageUpBtnClick, self) 
    self._wtPageDownBtn = self:Wnd("DFButton_2", UIButton)
    self._wtPageDownBtn:Event("OnClicked", self.OnPageDownBtnClick, self)
    self._wtPageNum = self:Wnd("DFTextBlock_98", UITextBlock)
    self._wtLeftContentPanel = self:Wnd("PlatformPaddingBox_3", UIWidgetBase)
    self._wtCollectionNum = self:Wnd("DFTextBlock_61", UITextBlock)
    if DFHD_LUA == 1 then
        Module.CommonBar:RegStackUITopBarCurrencyTypeList(self,
            { ECurrencyClientId.MandelCoins, ECurrencyClientId.BindDiamond, ECurrencyClientId.UnbindDiamond })
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.DefaultSecondary)
    else
        Module.CommonBar:RegStackUITopBarCurrencyTypeList(self,
            { ECurrencyClientId.MandelCoins, ECurrencyClientId.BindDiamond, ECurrencyClientId.UnbindDiamond })
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.Default)
    end
    Module.CommonBar:RegStackUITopBarTitle(self, Module.Market.Config.Loc.MarketSaleListTitle)

    --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() then
        self._wtDropDownsBox = self:Wnd("DFHorizontalBox_1", UIWidgetBase)
        self._wtPageKeyIcon = self:Wnd("wtPageKeyIcon", UIWidgetBase)
    end
    --- END MODIFICATION
    self._wtKeyIconBoxLeft = self:Wnd("WBP_CommonKeyIconBox", HDKeyIconBox)
    self._wtKeyIconBoxRight = self:Wnd("WBP_CommonKeyIconBox_1", HDKeyIconBox)
end

function MysticalSkinBuy:OnInitExtraData(subPageType, itemId)
    self._curSubPageType = subPageType
    self._itemId = itemId
    self._saleListInfo = nil
    self._fetchType = FetchType.None
    self:InitRarityScreenParams()
    self:InitFetchParams()
    self:ResetViewPanel()
end

function MysticalSkinBuy:OnShowBegin()
    self:AddLuaEvent(Server.MarketServer.Events.evtMarketSaleListForBuyChanged, self.Init, self)
    self:AddLuaEvent(Server.MarketServer.Events.evtBuyMysticalSkinSucceed, self.FetchSaleData, self)
    self:AddLuaEvent(Server.MarketServer.Events.evtBuyMysticalSkinFailed, self.FetchSaleData, self)
    self:AddLuaEvent(Server.CollectionServer.Events.evtPropsChangedByMarket, self.OnPropChangeByMarket, self)
    -- 折叠屏适配
    if DFMGameNotch:IsFoldDevice() and (not self.statusHandle) then
        self.statusHandle = DFMGameNotch.OnFoldStatusChanged:Add(CreateCPlusCallBack(self.OnFoldStatusChanged, self))
    end
    if self._saleListInfo then -- 第一次刷新手柄交给Init里处理
        if IsHD() then
            self:_EnableGamepadFeature()
        end
    end
end

function MysticalSkinBuy:OnHideBegin()
    self:RemoveAllLuaEvent()
    -- 折叠屏适配
    if self.statusHandle then
        DFMGameNotch.OnFoldStatusChanged:Remove(self.statusHandle)
        self.statusHandle = nil
    end
    if self._itemId and Server.MarketServer:CheckIsInSaleList(self._itemId) then
        Server.MarketServer:FetchSaleList(self._itemId) -- 刷新父页面
    end
    if IsHD() then
        self:_DisableGamepadFeature()
    end
end

function MysticalSkinBuy:OnFoldStatusChanged()
    self._wtItemViewList:TriggerViewportSizeChange()
end

function MysticalSkinBuy:OnShow()
    
end

function MysticalSkinBuy:OnHide()

end

function MysticalSkinBuy:OnOpen()
    self:ResetViewPanel()
end

function MysticalSkinBuy:OnClose()
    self:StopBanFetchTimer()
    self:ReleaseRefreshTimer()
    Facade.UIManager:ClearSubUIByParent(self, self._wtEmptyBgSlot)
end

function MysticalSkinBuy:FetchSaleDataAfresh(fetchType)
    self:InitPageTurningPanel()
    self:FetchSaleData(fetchType)
end

function MysticalSkinBuy:FetchSaleData(fetchType)
    if self._bBanFetch then
        return
    end
    if self._itemId then
        self._fetchType = fetchType and fetchType or FetchType.None
        Server.MarketServer:FetchSaleList(self._itemId, nil, nil, self._fetchParams)
    else
        self._fetchType = fetchType and fetchType or FetchType.None
        Server.MarketServer:FetchCollectionList(self._curSubPageType, self._fetchParams)
    end
    self:BanFetchRelatedContent()
end

function MysticalSkinBuy:BanFetchRelatedContent()
    self:SetFetchIsEnabled(false)
    self:StopBanFetchTimer()
    self._BanFetchTimerHandle = Timer.DelayCall(Server.MarketServer:GetSkinQueryCd() / 1000, function ()
        self:SetFetchIsEnabled(true)
        self:TryFocusLastFetchWidget()
    end, self)
end

function MysticalSkinBuy:StopBanFetchTimer()
    if self._BanFetchTimerHandle then
        Timer.CancelDelay(self._BanFetchTimerHandle)
        self._BanFetchTimerHandle = nil
    end
end

function MysticalSkinBuy:SetFetchIsEnabled(bEnabled)
    self._bBanFetch = not bEnabled
    if self._wtZoomRateDroDownRarity then
        self._wtZoomRateDroDownRarity:SetIsEnabled(bEnabled)
    end
    if self._wtZoomRateDroDownWear then
        self._wtZoomRateDroDownWear:SetIsEnabled(bEnabled)
    end
    if self._wtZoomRateDroDownNotice then
        self._wtZoomRateDroDownNotice:SetIsEnabled(bEnabled)
    end
    if self._wtZoomRateDroDownSort then
        self._wtZoomRateDroDownSort:SetIsEnabled(bEnabled)
    end
    if self._wtRarityScreenBtn then
        self._wtRarityScreenBtn:SetIsEnabled(bEnabled)
    end
    if self._wtScreenBtn then
        self._wtScreenBtn:SetIsEnabled(bEnabled)
    end
    if self._wtRefreshButton then
        self._wtRefreshButton:SetIsEnabledStyle(bEnabled)
    end
    if self._fetchParams and self._fetchParams.page == 1 then
        self._wtPageUpBtn:SetIsEnabled(false)
    else
        self._wtPageUpBtn:SetIsEnabled(bEnabled)
    end
    self._wtPageDownBtn:SetIsEnabled(bEnabled)
end

function MysticalSkinBuy:TryFocusLastFetchWidget()
    if not IsHD() then
        return
    end
    if self._fetchType == FetchType.Rarity then
        if self._wtZoomRateDroDownRarity then
            WidgetUtil.SetUserFocusToWidget(self._wtZoomRateDroDownRarity, true)
        end
    elseif self._fetchType == FetchType.Wear then
        if self._wtZoomRateDroDownWear then
            WidgetUtil.SetUserFocusToWidget(self._wtZoomRateDroDownWear, true)
        end
    elseif self._fetchType == FetchType.Notice then
        if self._wtZoomRateDroDownNotice then
            WidgetUtil.SetUserFocusToWidget(self._wtZoomRateDroDownNotice, true)
        end
    elseif self._fetchType == FetchType.Sort then
        if self._wtZoomRateDroDownSort then
            WidgetUtil.SetUserFocusToWidget(self._wtZoomRateDroDownSort, true)
        end
    elseif self._fetchType == FetchType.pendentRarity then
        if self._wtRarityScreenBtn then
            WidgetUtil.SetUserFocusToWidget(self._wtRarityScreenBtn, true)
        end
    elseif self._fetchType == FetchType.Price then
        if self._wtScreenBtn then
            WidgetUtil.SetUserFocusToWidget(self._wtScreenBtn, true)
        end
    elseif self._fetchType == FetchType.Refresh then
        if self._wtRefreshButton then
            WidgetUtil.SetUserFocusToWidget(self._wtRefreshButton, true)
        end
    end
end

function MysticalSkinBuy:ResetViewPanel()
    if self._itemId then
        Module.CommonBar:RegStackUITopBarTitle(self, Module.Market.Config.Loc.MarketSaleListTitle)
    else
        Module.CommonBar:RegStackUITopBarTitle(self, Module.Market.Config.Loc.MarketMyCollectionTitle)
    end
    if self._wtMainCanvasPanel then
        self._wtMainCanvasPanel:Collapsed()
    end
    self:SetFetchIsEnabled(true)
    self:InitDropDownBox()
    self:InitScreenBtn()
    self:InitRarityScreenBtn()
    self:InitPageTurningPanel()
    self:RefreshLeftPanel()
    self:RefreshBackGround_1()
    if self._wtItemDetailView then
        self._wtItemDetailView:Collapsed()
    end
end

function MysticalSkinBuy:InitPageTurningPanel()
    self._fetchParams.page = 1
    if self._fetchParams.page == 1 then
        self._wtPageUpBtn:SetIsEnabled(false)
    end
    self._wtPageNum:SetText(string.format(MarketConfig.Loc.PageNum, self._fetchParams.page))
end

function MysticalSkinBuy:InitDropDownBox()
    self:InitDropDownBoxRarity()
    self:InitDropDownBoxWear()
    self:InitDropDownBoxNotice()
    self:InitDropDownBoxSort()
end

function MysticalSkinBuy:InitDropDownBoxRarity()
    if self._wtZoomRateDroDownRarity then
        local zoomRateDroDownLoc = Module.Market.Config.ZoomRateDroDownBuyRarityLoc
        if self._curSubPageType == EMarketSubPageType.MysticalSkin then
            if self._itemId and ItemHelperTool.GetQualityTypeById(self._itemId) < ItemConfig.EWeaponSkinQualityType.Orange then -- 橙色品阶以下玄学皮肤无稀有度概念
                self._wtZoomRateDroDownRarity:Collapsed()
            else
                UIUtil.InitDropDownBox(self._wtZoomRateDroDownRarity, zoomRateDroDownLoc, {}, 0)
                self._wtZoomRateDroDownRarity:SelfHitTestInvisible()
            end
        elseif self._curSubPageType == EMarketSubPageType.MysticalPendant then
            zoomRateDroDownLoc = Module.Market.Config.ZoomRateDroDownPendantBuyRarityLoc
            UIUtil.InitDropDownBox(self._wtZoomRateDroDownRarity, zoomRateDroDownLoc, {}, 0)
            self._wtZoomRateDroDownRarity:Collapsed()
        else
            self._wtZoomRateDroDownRarity:Collapsed()
        end
    end
end

function MysticalSkinBuy:InitDropDownBoxWear()
    if self._wtZoomRateDroDownWear then
        local zoomRateDroDownLoc = Module.Market.Config.ZoomRateDroDownBuyWearLoc
        if self._curSubPageType == EMarketSubPageType.MysticalPendant then
            self._wtZoomRateDroDownWear:Collapsed()
        else
            UIUtil.InitDropDownBox(self._wtZoomRateDroDownWear, zoomRateDroDownLoc, {}, 0)
            self._wtZoomRateDroDownWear:SelfHitTestInvisible()
        end
    end
end

function MysticalSkinBuy:InitDropDownBoxNotice()
    if self._wtZoomRateDroDownNotice then
        local zoomRateDroDownLoc = Module.Market.Config.ZoomRateDroDownBuyNoticeLoc
        UIUtil.InitDropDownBox(self._wtZoomRateDroDownNotice, zoomRateDroDownLoc, {}, 0)
    end
end

function MysticalSkinBuy:InitDropDownBoxSort()
    if self._wtZoomRateDroDownSort then
        local zoomRateDroDownLoc = Module.Market.Config.ZoomRateDroDownBuySortMode1Loc
        if self._curSubPageType == EMarketSubPageType.MysticalSkin then
            if self._itemId and ItemHelperTool.GetQualityTypeById(self._itemId) < ItemConfig.EWeaponSkinQualityType.Orange then -- 橙色品阶以下玄学皮肤无稀有度概念
                zoomRateDroDownLoc = Module.Market.Config.ZoomRateDroDownBuySortMode2Loc
            end
        elseif self._curSubPageType == EMarketSubPageType.MysticalPendant then
            zoomRateDroDownLoc = Module.Market.Config.ZoomRateDroDownPendantBuySortMode1Loc
        end
        UIUtil.InitDropDownBox(self._wtZoomRateDroDownSort, zoomRateDroDownLoc, {}, 0)
    end
end

function MysticalSkinBuy:InitScreenBtn()
    if self._wtScreenBtn then
        if self._itemId then
            self._wtScreenBtn:SelfHitTestInvisible()
        else
            self._wtScreenBtn:Collapsed()
        end
    end
end

function MysticalSkinBuy:InitSelectedPos()
    if self._saleListInfo and self._saleListInfo.sale_lists and not table.isempty(self._saleListInfo.sale_lists) then
        self._selectedPos = 1
    else
        self._selectedPos = 0
    end
    self._selectedCell = nil
end

function MysticalSkinBuy:InitRarityScreenParams()
    self._rarityScreenParams = {
        colorIds = {},
        patternIds = {},
        attachmentIds = {},
    }
end

function MysticalSkinBuy:InitFetchParams()
    self._fetchParams = {
        rarityId = 0,
        minWear = 0,
        maxWear = 0,
        noticeType = 0,
        sortType = 0,
        minPrice = 0,
        maxPrice = 0,
        page = 1,
    }
end

function MysticalSkinBuy:Init(saleListInfo)
    if saleListInfo then
        self._saleListInfo = {}
        deepcopy(self._saleListInfo, saleListInfo)
        self._curCollectionNum = saleListInfo.curr_watch_num or nil
        self._curMaxCollectionNum = saleListInfo.max_watch_num or nil
        --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
        if IsHD() then
            self:_EnableGamepadFeature()
        end
        --- END MODIFICATION
        self:RefreshLeftAndRightPanel()
        if self._wtMainCanvasPanel then
            self._wtMainCanvasPanel:SelfHitTestInvisible()
        end
    end
end

function MysticalSkinBuy:RefreshLeftAndRightPanel()
    self:InitSelectedPos()
    self:RefreshLeftPanel()
    self:RefreshRightPanel()
    self:RefreshBackGround_1()
end

function MysticalSkinBuy:RefreshBackGround_1()
    if self._fetchType ~= FetchType.None then
        return
    end
    local function HideLeftAndRightContent(bHide)
        if self._wtLeftContentPanel then
            if bHide then
                self._wtLeftContentPanel:Collapsed()
            else
                self._wtLeftContentPanel:SelfHitTestInvisible()
            end
        end
        if self._wtItemDetailView then
            if bHide then
                self._wtItemDetailView:Collapsed()
            else
                self._wtItemDetailView:SelfHitTestInvisible()
            end
        end
    end
    if self._wtEmptyBgPanel_1 and self._wtEmptyBgSlot_1 then
        if self._itemId then
            HideLeftAndRightContent(false)
            self._wtEmptyBgPanel_1:Collapsed()
            self._wtEmptyBgSlot_1:Collapsed()
        else
            if self._saleListInfo and table.isempty(self._saleListInfo.sale_lists or {}) then
                HideLeftAndRightContent(true)
                Facade.UIManager:RemoveSubUIByParent(self, self._wtEmptyBgSlot_1)
                self._wtEmptyBgPanel_1:SelfHitTestInvisible()
                self._wtEmptyBgSlot_1:SelfHitTestInvisible()
                local weakUIIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.CommonEmptyContent, self._wtEmptyBgSlot_1)
                local emptyBg = getfromweak(weakUIIns)
                if emptyBg then
                    if self._curSubPageType == EMarketSubPageType.MysticalSkin then
                        emptyBg:BP_SetText(Module.Market.Config.Loc.NoMysticalSkinCollection)
                    elseif self._curSubPageType == EMarketSubPageType.MysticalPendant then
                        emptyBg:BP_SetText(Module.Market.Config.Loc.NoMysticalPendantCollection)
                    else
                        emptyBg:BP_SetText(Module.Market.Config.Loc.NoMysticalSkinCollection)
                    end
                    emptyBg:BP_SetTypeWithParam(1)
                    emptyBg:Visible()
                end
            else
                HideLeftAndRightContent(false)
                self._wtEmptyBgPanel_1:Collapsed()
                self._wtEmptyBgSlot_1:Collapsed()
            end
        end
    end
end

function MysticalSkinBuy:RefreshLeftPanel()
    self:RefreshItemViewList()
    self:RefreshCollectionNum()
end

function MysticalSkinBuy:RefreshRightPanel()
    self:ResetChangeParams()
    self:RefreshItemDetailView()
    self:RefreshRemainTime()
    self:StartRefreshTimer()
    if self._itemId and self._wtItemDetailView then
        self._wtItemDetailView:SelfHitTestInvisible() -- 初始化完成再显示
    end
end

function MysticalSkinBuy:ResetChangeParams()
    local cellInfo = self:GetSelectedCellInfo()
    if cellInfo then
        if not MarketLogic.GetOrderUnlockedState(cellInfo) then
            self._bUnlockedBefore = false
            self._bLockedBefore = false
        else
            self._bUnlockedBefore = true
            if not MarketLogic.GetOrderExpiredState(cellInfo) then
                self._bLockedBefore = false
            else
                self._bLockedBefore = true
            end
        end
    else
        self._bUnlockedBefore = false
        self._bLockedBefore = false
    end
end

function MysticalSkinBuy:RefreshItemDetailView()
    local fBuyBtnClicked = CreateCallBack(self._OnBuyBtnClicked, self)
    local fGetShopBtnTxt = CreateCallBack(function(self, itemStuct, curSliderNum)
        local btnTxt = Module.Market.Config.Loc.ItemSoldOutBtnText
        local cellInfo = self:GetSelectedCellInfo()
        if cellInfo then
            if MarketLogic.GetOrderUnlockedState(cellInfo) then
                local currencyId = Module.Currency:ConvertCurrencyIdByItemId(cellInfo.buy_currency_id)
                local currencyIcon = Module.Currency:GetRichTxtImgId(currencyId)
                local currencyNum = Module.Currency:GetNumByItemId(cellInfo.buy_currency_id)
                if currencyNum < cellInfo.price then
                    btnTxt = string.format(Module.Market.Config.Loc.CurrencyNumNotEnough, 52, 52, currencyIcon,
                    MathUtil.GetNumberFormatStr(cellInfo.price + 0.00001))
                else
                    btnTxt = string.format(Module.Market.Config.Loc.CurrencyNum, 52, 52, currencyIcon,
                    MathUtil.GetNumberFormatStr(cellInfo.price + 0.00001))
                end
            else
                btnTxt = Module.Market.Config.Loc.InPublicNotice
            end
        end
        return btnTxt
    end, self)
    -- 详情页初始化
    self._wtItemDetailView:SetIsOutBtnSlot(true) -- 先配置购买按钮于下方
    self._wtItemDetailView:SetWeaponBulletVisible(false) -- 配置武器子弹不显示
    self._wtItemDetailView:SetStatePlayAnimOnShow(false) -- 配置动效不展示
    local styleIndex = 0
    local btnNavId = UIName2ID.ItemDetailViewBtnOneBtn
    local function loadFinishCallback(btnIns)
        self._wtBuyBtn = btnIns
        self:InitTimeTextWidget()
    end
    local cellInfo = self:GetSelectedCellInfo()
    if cellInfo and cellInfo.prop then
        local selectItem = MarketLogic.CreateMysticalSkinItem(nil, cellInfo.prop) or ItemBase:NewIns(cellInfo.prop.id)
        local propInfo = selectItem:GetRawPropInfo()
        if propInfo then
            local weaponDesc = selectItem:GetRawDescObj()
            if isvalid(weaponDesc) then
                WeaponAssemblyTool.SetWeaponDescriptionSkinInfoFromPropInfo(weaponDesc, propInfo)
            end
        end
        local bNeedBtnGrey = (not MarketLogic.GetOrderUnlockedState(cellInfo)) or MarketLogic.GetOrderExpiredState(cellInfo)
        styleIndex = bNeedBtnGrey and 1 or 0
        local function loadFinishCallback(btnIns)
            self._wtBuyBtn = btnIns
            self:InitTimeTextWidget()
            --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
            -- if IsHD() then
            --     local _btnBuy = self._wtBuyBtn:Wnd("wtBtn1", DFCommonButtonOnly)
            --     if _btnBuy then
            --         _btnBuy:SetDisplayInputAction("Common_ButtonLeft", true, nil, true)
            --     end
            -- end
            --- END MODIFICATION
        end
        self._wtItemDetailView:UpdateItem(selectItem, true,
        {{fOnClick = fBuyBtnClicked,
            txt = fGetShopBtnTxt,
            bEnable = true,
            bBtnGrey = bNeedBtnGrey,
            styleIndex = styleIndex,
            PressedSoundNameAudioRes = DFMAudioRes.UIAHBuy, -- todo 改为市场花钱音效
            currencyClientType = cellInfo.buy_currency_id,
            GamepadActionName = "Common_ButtonLeft",
            GamepadkeyEvent = EInputEvent.IE_Pressed,
            GamepadActionPriority = EDisplayInputActionPriority.UI_Stack,
            GamepadActionNoLongPress = true}},
        btnNavId, nil, loadFinishCallback, 1)
    else
        if self._itemId then
            styleIndex = 1
            local showItem  = MarketLogic.CreateMysticalSkinItem(self._itemId) or ItemBase:NewIns(self._itemId)
            self._wtItemDetailView:UpdateItem(showItem, true,
            {{fOnClick = fBuyBtnClicked,
                txt = fGetShopBtnTxt,
                bEnable = true,
                bBtnGrey = true,
                styleIndex = styleIndex}},
            btnNavId, nil, loadFinishCallback, 0)
        end
    end
    self._wtItemDetailView:SetCloseBtnVisible(false)
    self._wtItemDetailView:SetDetailBtnVisible2(true)
    self._wtItemDetailView:SetDetailBtnClickedCallback2(CreateCallBack(self._OnViewModelBtnClicked, self))
    -- 初始化完成再显示详情页
    if cellInfo then
        self._wtItemDetailView:SelfHitTestInvisible()
    else
        if self._itemId then
            self._wtItemDetailView:SelfHitTestInvisible()
        else
            self._wtItemDetailView:Collapsed()
        end
    end
    if cellInfo then
        self._wtLabelMarkBtn:SelfHitTestInvisible()
        self._wtLabelMarkBtn:SetIsChecked(cellInfo.watch_state == 1, false)
    else
        self._wtLabelMarkBtn:Collapsed()
        self._wtLabelMarkBtn:SetIsChecked(false, false)
    end
end

function MysticalSkinBuy:InitTimeTextWidget()
    if self._wtBuyBtn then
        self._wtTimePanel = self._wtBuyBtn:Wnd("DFHorizontalBox_4", UIWidgetBase)
        self._wtTimeText = self._wtBuyBtn:Wnd("DFTextBlock_164", UITextBlock)
        self._wtLockImage = self._wtBuyBtn:Wnd("DFImage", UIImage)
        self._wtClockImage = self._wtBuyBtn:Wnd("DFImage_126", UIImage)
    end
end

function MysticalSkinBuy:RefreshRemainTime()
    local cellInfo = self:GetSelectedCellInfo()
    if cellInfo then
        if not MarketLogic.GetOrderUnlockedState(cellInfo) then
            local day, hour, min, sec = TimeUtil.GetSecondsFormatDDHHMMSS(MarketLogic.GetOrderUnlockRemainTime(cellInfo))
            if self._wtTimeText then
                if day == 0 and hour == 0 then
                    self._wtTimeText:SetText(string.format(Module.Market.Config.Loc.UnlockTime, string.format(Module.Market.Config.Loc.MinuteSecond, min, sec)))
                elseif day == 0 and hour > 0 then
                    self._wtTimeText:SetText(string.format(Module.Market.Config.Loc.UnlockTime, string.format(Module.Market.Config.Loc.HourMinute, hour, min)))
                elseif day > 0 then
                    self._wtTimeText:SetText(string.format(Module.Market.Config.Loc.UnlockTime, string.format(Module.Market.Config.Loc.DayHour, day, hour)))
                end
            end
            if self._wtBuyBtn and self._wtBuyBtn.SetStyle then
                self._wtBuyBtn:SetStyle(0)
            end
            if self._wtLockImage then
                self._wtLockImage:SelfHitTestInvisible()
            end
            if self._wtClockImage then
                self._wtClockImage:Collapsed()
            end
            if self._wtTimePanel then
                self._wtTimePanel:SelfHitTestInvisible()
            end
        else
            self:UnlockBuyBtn()
            if not MarketLogic.GetOrderExpiredState(cellInfo) then
                local day, hour, min, sec = TimeUtil.GetSecondsFormatDDHHMMSS(MarketLogic.GetOrderExpiredRemainTime(cellInfo))
                if self._wtTimeText then
                    if day == 0 and hour == 0 then
                        self._wtTimeText:SetText(string.format(Module.Market.Config.Loc.RemainTime, string.format(Module.Market.Config.Loc.MinuteSecond, min, sec)))
                    elseif day == 0 and hour > 0 then
                        self._wtTimeText:SetText(string.format(Module.Market.Config.Loc.RemainTime, string.format(Module.Market.Config.Loc.HourMinute, hour, min)))
                    elseif day > 0 then
                        self._wtTimeText:SetText(string.format(Module.Market.Config.Loc.RemainTime, string.format(Module.Market.Config.Loc.DayHour, day, hour)))
                    end
                end
                if self._wtBuyBtn and self._wtBuyBtn.SetStyle then
                    self._wtBuyBtn:SetStyle(0)
                end
                if self._wtLockImage then
                    self._wtLockImage:Collapsed()
                end
                if self._wtClockImage then
                    self._wtClockImage:SelfHitTestInvisible()
                end
                if self._wtTimePanel then
                    self._wtTimePanel:SelfHitTestInvisible()
                end
            else
                self:LockBuyBtn()
                if self._wtBuyBtn and self._wtBuyBtn.SetStyle then
                    self._wtBuyBtn:SetStyle(1)
                end
                if self._wtLockImage then
                    self._wtLockImage:Collapsed()
                end
                if self._wtClockImage then
                    self._wtClockImage:SelfHitTestInvisible()
                end
                if self._wtTimeText then
                    self._wtTimeText:SetText(Module.Market.Config.Loc.Expired)
                end
                if self._wtTimePanel then
                    self._wtTimePanel:SelfHitTestInvisible()
                end
                self:ReleaseRefreshTimer()
            end
        end
    else
        self:ReleaseRefreshTimer()
        if self._wtTimePanel then
            self._wtTimePanel:Collapsed()
        end
    end
end

function MysticalSkinBuy:StartRefreshTimer()
    self:ReleaseRefreshTimer()
    self._refreshTimerHandle = Timer:NewIns(1, 0)
    self._refreshTimerHandle:AddListener(self.RefreshRemainTime, self)
    self._refreshTimerHandle:Start()
end

function MysticalSkinBuy:ReleaseRefreshTimer()
    if self._refreshTimerHandle then
        self._refreshTimerHandle:Release()
        self._refreshTimerHandle = nil
    end
end

function MysticalSkinBuy:RefreshItemViewList()
    local function AddEmptyBgContent()
        if self._wtEmptyBgSlot then
            Facade.UIManager:RemoveSubUIByParent(self, self._wtEmptyBgSlot)
            self._wtEmptyBgSlot:SelfHitTestInvisible()
            local weakUIIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.CommonEmptyContent, self._wtEmptyBgSlot)
            local emptyBg = getfromweak(weakUIIns)
            if emptyBg then
                emptyBg:BP_SetText(self._itemId and Module.Market.Config.Loc.NoSaleInMarket or Module.Market.Config.Loc.NoInfoInMarket)
                emptyBg:BP_SetTypeWithParam(1)
                emptyBg:Visible()
            end
        end
    end
    if self._saleListInfo and self._saleListInfo.sale_lists then
        if #self._saleListInfo.sale_lists > 0 then
            if self._wtEmptyBgPanel then
                self._wtEmptyBgPanel:Collapsed()
            end
            if self._wtEmptyBgSlot then
                self._wtEmptyBgSlot:Collapsed()
            end
            if self._wtItemViewList then
                self._wtItemViewList:RefreshAllItems()
                self._wtItemViewList:Visible()
            end
        else
            if self._wtItemViewList then
                self._wtItemViewList:Collapsed()
            end
            if self._wtEmptyBgPanel then
                self._wtEmptyBgPanel:SelfHitTestInvisible()
            end
            AddEmptyBgContent()
            if self._fetchType == FetchType.PageDown then
                Module.CommonTips:ShowSimpleTip(MarketConfig.Loc.NoMoreSale)
            end
        end
    else
        if self._wtItemViewList then
            self._wtItemViewList:Collapsed()
        end
        if self._wtEmptyBgPanel then
            self._wtEmptyBgPanel:SelfHitTestInvisible()
        end
        AddEmptyBgContent()
        if self._fetchType == FetchType.PageDown then
            Module.CommonTips:ShowSimpleTip(MarketConfig.Loc.NoMoreSale)
        end
    end
end

function MysticalSkinBuy:RefreshCollectionNum()
    if self._wtCollectionNum then
        if not self._itemId and self._curCollectionNum and self._curMaxCollectionNum then
            self._wtCollectionNum:SetText(string.format(MarketConfig.Loc.MarketCollectionNum, self._curCollectionNum, self._curMaxCollectionNum))
            self._wtCollectionNum:SelfHitTestInvisible()
        else
            self._wtCollectionNum:Collapsed()
        end  
    end
end

function MysticalSkinBuy:UnlockBuyBtn()
    if not self._bUnlockedBefore then
        self:RefreshItemDetailView()
        self._bUnlockedBefore = true
    end
end

function MysticalSkinBuy:LockBuyBtn()
    if not self._bLockedBefore then
        self:RefreshItemDetailView()
        self._bLockedBefore = true
    end
end

function MysticalSkinBuy:_OnGetItemCount()
    local count = 0
    if self._saleListInfo and self._saleListInfo.sale_lists then
        count = #self._saleListInfo.sale_lists
    end
    return count
end

function MysticalSkinBuy:_OnProcessItemWidget(position, itemWidget)
    local cellInfo = (self._saleListInfo and self._saleListInfo.sale_lists) and self._saleListInfo.sale_lists[position] or nil
    if cellInfo and cellInfo.prop then
        if itemWidget then
            local item = MarketLogic.CreateMysticalSkinItem(nil, cellInfo.prop) or ItemBase:NewIns(cellInfo.prop.id)
            local info =
            {
                prop_id = cellInfo.prop.id,
                cur_num = cellInfo.selling_num,
                buy_currency_id = cellInfo.buy_currency_id,
                sell_currency_id = cellInfo.sell_currency_id,
                min_price = cellInfo.price,
                bShowBuyCurrency = true,
                bShowTopRightIcon = not MarketLogic.GetOrderUnlockedState(cellInfo),
            }
            itemWidget:BindClickCallback(function()
                self:_OnItemViewClicked(itemWidget, position)
            end)
            if not MarketLogic.GetOrderUnlockedState(cellInfo) then
                itemWidget:BindTimerUpdateCallback(function()
                    self:_OnItemViewTimerUpdate(itemWidget, cellInfo)
                end)
            else
                itemWidget:ReleaseTimer()
            end
            itemWidget:InitMarketItem(item, info)
            itemWidget:EnableComponent(Module.CommonWidget.Config.EIVWarehouseTempComponent.BottomRightIconText, true)
            itemWidget:SelfHitTestInvisible()
            if self._selectedPos and position == self._selectedPos then
                self._selectedCell = itemWidget
                itemWidget:SetSelected(itemWidget.item, true)
            else
                itemWidget:SetSelected(itemWidget.item, false)
            end
        end
    else
    end
end

function MysticalSkinBuy:_OnItemViewTimerUpdate(itemWidget, cellInfo)
    if itemWidget and cellInfo then
        if not MarketLogic.GetOrderUnlockedState(cellInfo, itemWidget) then
            itemWidget:EnableComponent(Module.CommonWidget.Config.EIVWarehouseTempComponent.TopRightIconText, true)
        else
            itemWidget:EnableComponent(Module.CommonWidget.Config.EIVWarehouseTempComponent.TopRightIconText, false)
            itemWidget:ReleaseTimer()
        end
    end
end

function MysticalSkinBuy:_OnItemViewClicked(itemWidget, position)
    if self._selectedPos == position then
        return
    end
    if self._saleListInfo and self._saleListInfo.sale_lists then
        if self._selectedCell then
            self._selectedCell:SetSelected(self._selectedCell.item, false)
        end
        self._selectedPos = position
        self._selectedCell = itemWidget
        if self._selectedCell then
            self._selectedCell:SetSelected(self._selectedCell.item, true)
        end
        self:RefreshRightPanel()
    end
end

function MysticalSkinBuy:_OnBuyBtnClicked()
    if self._selectedPos == 0 then
        if self._itemId then
            local itemInfo = ItemConfigTool.GetItemConfigById(self._itemId)
            local itemName = (itemInfo and itemInfo.Name) and itemInfo.Name or ""
            Module.CommonTips:ShowSimpleTip(string.format(MarketConfig.Loc.ItemHasBeenSoldOut, itemName))
        end
    else
        local cellInfo = self:GetSelectedCellInfo()
        if cellInfo then
            if not MarketLogic.GetOrderUnlockedState(cellInfo) then
                Module.CommonTips:ShowSimpleTip(MarketConfig.Loc.OrderIsLocked)
                return
            end
            if MarketLogic.GetOrderExpiredState(cellInfo) then
                Module.CommonTips:ShowSimpleTip(MarketConfig.Loc.OrderIsExpired)
                return
            end
            local currencyId = Module.Currency:ConvertCurrencyIdByItemId(cellInfo.buy_currency_id)
            local currencyIcon = Module.Currency:GetRichTxtImgId(currencyId)
            local currencyNum = Module.Currency:GetNumByItemId(cellInfo.buy_currency_id)
            if currencyNum < cellInfo.price then
                local showStr = Module.Store.Config.Loc.RechargeTipsWindowGoToChargeView
                local confirmStr = Module.Store.Config.Loc.StoreMainTabRecharge
                local _rechargeCancelHandle = function()
                end
                local _rechargeConfirmHandle = function()
                    Module.Store:ShowStoreRechargeMainPanle()
                end
                Module.CommonTips:ShowConfirmWindow(showStr, _rechargeConfirmHandle, _rechargeCancelHandle, nil, confirmStr)
                return
            end

            local payCallBack = CreateCallBack(function()
                Facade.UIManager:AsyncShowUI(UIName2ID.MysticalSkinConfirmBuy, nil, nil, cellInfo)
            end, self)

            if currencyId == ECurrencyClientId.Diamond or currencyId == ECurrencyClientId.UnbindDiamond then
                -- 如果支付三角币判断是否支付柔性限制
                Server.PayServer:ReqPayQueryMidasRiskInfo(EFlexiblePaymentLimitType.Market, payCallBack)
            else
                payCallBack()
            end
        end
    end
end

function MysticalSkinBuy:OnPropChangeByMarket(dataChange)
    if dataChange and type(dataChange) == "table" and not table.isempty(dataChange) then
        if (ItemHelperTool.IsMysticalSkin(dataChange[1].prop.id) or ItemHelperTool.IsMysticalPendant(dataChange[1].prop.id)) and dataChange[1].reason == ePropChangeReason.FromMarketBuy then
            local mysticalSkinItem = ItemHelperTool.CreateItemByPropInfo(dataChange[1].prop, dataChange[1])
            if mysticalSkinItem then
                Module.Reward:OpenRewardPanel(Module.Market.Config.Loc.GetSkinWindow, nil, {mysticalSkinItem}, nil, nil, nil, true)
            end
        end
    end
end

function MysticalSkinBuy:GetSelectedCellInfo()
    return (self._saleListInfo and self._saleListInfo.sale_lists) and self._saleListInfo.sale_lists[self._selectedPos] or nil
end

function MysticalSkinBuy:OnRarityCheckedTabIndexChanged(idx)
    loginfo("MysticalSkinBuy:OnRarityCheckedTabIndexChanged idx =", idx)
    if self._curSubPageType == EMarketSubPageType.MysticalSkin then
        self._fetchParams.rarityId = idx == 0 and 0 or MarketConfig.MysticalSkinRarityCategoryLoc[idx].rarityId
    elseif self._curSubPageType == EMarketSubPageType.MysticalPendant then
        self._fetchParams.rarityId = idx == 0 and 0 or MarketConfig.MysticalPendantRarityCategoryLoc[idx].rarityId
    end
    self:FetchSaleDataAfresh(FetchType.Rarity)
end

function MysticalSkinBuy:OnWearCheckedTabIndexChanged(idx)
    loginfo("MysticalSkinBuy:OnWearCheckedTabIndexChanged idx =", idx)
    if idx == 0 then
        self._fetchParams.minWear = 0
        self._fetchParams.maxWear = 0
    else
        local wearRangeBegin, wearRangeEnd = ItemConfigTool.GetMysticalWearRangeByIndex(idx)
        if wearRangeBegin and wearRangeEnd then
            self._fetchParams.minWear = wearRangeBegin
            self._fetchParams.maxWear = wearRangeEnd
        end
    end
    self:FetchSaleDataAfresh(FetchType.Wear)
end

function MysticalSkinBuy:OnNoticeCheckedTabIndexChanged(idx)
    loginfo("MysticalSkinBuy:OnNoticeCheckedTabIndexChanged idx =", idx)
    self._fetchParams.noticeType = idx
    self:FetchSaleDataAfresh(FetchType.Notice)
end

function MysticalSkinBuy:OnSortCheckedTabIndexChanged(idx)
    loginfo("MysticalSkinBuy:OnSortCheckedTabIndexChanged idx =", idx)
    if idx == 0 then
        self._fetchParams.sortType = 0
    else
        if self._curSubPageType == EMarketSubPageType.MysticalSkin and self._itemId and ItemHelperTool.GetQualityTypeById(self._itemId) < ItemConfig.EWeaponSkinQualityType.Orange then -- 橙色品阶以下玄学皮肤无稀有度概念
            self._fetchParams.sortType = idx + 2
        else
            self._fetchParams.sortType = idx
        end
    end
    self:FetchSaleDataAfresh(FetchType.Sort)
end

function MysticalSkinBuy:_OnScreenBtnClicked()
    if self._saleListInfo then
        Facade.UIManager:AsyncShowUI(UIName2ID.MysticalSkinPurchaseScreening, nil, nil,
        self._saleListInfo, CreateCallBack(self.ScreeningItemViewList, self), CreateCallBack(self.OnScreeningPanelClose, self))
    end
end

function MysticalSkinBuy:ScreeningItemViewList(eligibleResult)
    if eligibleResult then
        self._fetchParams.minPrice = eligibleResult.minPrice or 0
        self._fetchParams.maxPrice = eligibleResult.maxPrice or 0
    end
    self:FetchSaleDataAfresh(FetchType.Price)
end

function MysticalSkinBuy:OnScreeningPanelClose()

end

function MysticalSkinBuy:_OnRefreshBtnClicked()
    self:FetchSaleDataAfresh(FetchType.Refresh)
end

function MysticalSkinBuy:_OnRefreshBtnDeClicked()
    Module.CommonTips:ShowSimpleTip(MarketConfig.Loc.PleaseWait)
end

function MysticalSkinBuy:_OnViewModelBtnClicked()
    MarketLogic.OpenMysticalSkinModelView(self._itemId, self:GetSelectedCellInfo())
end

function MysticalSkinBuy:_OnLabelMarkBtnClick(bChecked)
    local cellInfo = self:GetSelectedCellInfo()
    if cellInfo then
        local function SetIsChecked(bIsChecked)
            self._wtLabelMarkBtn:SetIsChecked(bIsChecked, false)
        end
        MarketLogic.AddMarketCollectionProcess({cellInfo}, bChecked, SetIsChecked)
    end
end

function MysticalSkinBuy:OnPageUpBtnClick()
    if self._fetchParams.page <= 1 then
        return
    end
    self._fetchParams.page = self._fetchParams.page - 1
    if self._fetchParams.page == 1 then
        self._wtPageUpBtn:SetIsEnabled(false)
    end
    self._wtPageNum:SetText(string.format(MarketConfig.Loc.PageNum, self._fetchParams.page))
    self:FetchSaleData(FetchType.PageUp)
end

function MysticalSkinBuy:OnPageDownBtnClick()
    self._fetchParams.page = self._fetchParams.page + 1
    if self._fetchParams.page > 1 then
        self._wtPageUpBtn:SetIsEnabled(true)
    end
    self._wtPageNum:SetText(string.format(MarketConfig.Loc.PageNum, self._fetchParams.page))
    self:FetchSaleData(FetchType.PageDown)
end

function MysticalSkinBuy:InitRarityScreenBtn()
    if self._curSubPageType == EMarketSubPageType.MysticalSkin then
        self:SetRarityScreenVisible(false)
    elseif self._curSubPageType == EMarketSubPageType.MysticalPendant then
        if self._wtRarityScreenBtn then
            self._wtRarityScreenBtn:SetMainTitle(MarketConfig.Loc.AllRarity)
        end
        self:SetRarityScreenVisible(true)
    else
        self:SetRarityScreenVisible(false)
    end
end

function MysticalSkinBuy:SetRarityScreenVisible(bVisible)
    if self._wtRarityScreenBtn then
        if bVisible then
            self._wtRarityScreenBtn:SelfHitTestInvisible()
        else
            self._wtRarityScreenBtn:Collapsed()
        end
    end
    if self._wtRarityScreenIcon then
        if bVisible then
            self._wtRarityScreenIcon:SelfHitTestInvisible()
        else
            self._wtRarityScreenIcon:Collapsed()
        end
    end
end

function MysticalSkinBuy:_OnRarityScreenBtnClicked()
    if self._saleListInfo then
        Facade.UIManager:AsyncShowUI(UIName2ID.MysticalPendantRarityScreening, nil, nil,
        self._rarityScreenParams, CreateCallBack(self._OnRarityScreeningFinish, self), CreateCallBack(self._OnRarityScreeningPanelClose, self))
    end
end

function MysticalSkinBuy:_OnRarityScreeningFinish(rarityScreenParams)
    if rarityScreenParams then
        self._rarityScreenParams = rarityScreenParams
        local rarityScreenDesc = MarketConfig.Loc.AllRarity
        local colorDesc = ""
        if self._rarityScreenParams.colorIds then
            if #self._rarityScreenParams.colorIds ~= 1 then
                self._fetchParams.colorId = 0
                colorDesc = #self._rarityScreenParams.colorIds == 0 and MarketConfig.Loc.PendentRarityDesNone or MarketConfig.Loc.PendentRarityDescSA
            else
                self._fetchParams.colorId = self._rarityScreenParams.colorIds[1]
                if self._rarityScreenParams.colorIds[1] == MarketConfig.MysticalPendantColorCategoryLoc[1].colorId then
                    colorDesc = MarketConfig.Loc.PendentRarityDescS
                elseif self._rarityScreenParams.colorIds[1] == MarketConfig.MysticalPendantColorCategoryLoc[2].colorId then
                    colorDesc = MarketConfig.Loc.PendentRarityDescA
                end
            end
        end
        local patternDesc = ""
        if self._rarityScreenParams.patternIds then
            if #self._rarityScreenParams.patternIds ~= 1 then
                self._fetchParams.patternId = 0
                patternDesc = #self._rarityScreenParams.patternIds == 0 and MarketConfig.Loc.PendentRarityDesNone or MarketConfig.Loc.PendentRarityDescSA
            else
                self._fetchParams.patternId = self._rarityScreenParams.patternIds[1]
                if self._rarityScreenParams.patternIds[1] == MarketConfig.MysticalPendantPatternCategoryLoc[1].patternId then
                    patternDesc = MarketConfig.Loc.PendentRarityDescS
                elseif self._rarityScreenParams.patternIds[1] == MarketConfig.MysticalPendantPatternCategoryLoc[2].patternId then
                    patternDesc = MarketConfig.Loc.PendentRarityDescA
                end
            end
        end
        local attachmentDesc = ""
        if self._rarityScreenParams.attachmentIds then
            if #self._rarityScreenParams.attachmentIds ~= 1 then
                self._fetchParams.attachmentId = 0
                attachmentDesc = #self._rarityScreenParams.attachmentIds == 0 and MarketConfig.Loc.PendentRarityDesNone or MarketConfig.Loc.PendentRarityDescSA
            else
                self._fetchParams.attachmentId = self._rarityScreenParams.attachmentIds[1]
                if self._rarityScreenParams.attachmentIds[1] == MarketConfig.MysticalPendantAttachmentCategoryLoc[1].attachmentId then
                    attachmentDesc = MarketConfig.Loc.PendentRarityDescS
                elseif self._rarityScreenParams.attachmentIds[1] == MarketConfig.MysticalPendantAttachmentCategoryLoc[2].attachmentId then
                    attachmentDesc = MarketConfig.Loc.PendentRarityDescA
                end
            end
        end
        if colorDesc == MarketConfig.Loc.PendentRarityDesNone and patternDesc == MarketConfig.Loc.PendentRarityDesNone and attachmentDesc == MarketConfig.Loc.PendentRarityDesNone then
            rarityScreenDesc = MarketConfig.Loc.AllRarity
        else
            rarityScreenDesc = string.format(MarketConfig.Loc.PendentRarityDesc, colorDesc, patternDesc, attachmentDesc)
        end
        if self._wtRarityScreenBtn then
            self._wtRarityScreenBtn:SetMainTitle(rarityScreenDesc)
        end
    end
    self:FetchSaleDataAfresh(FetchType.pendentRarity)
end

function MysticalSkinBuy:_OnRarityScreeningPanelClose()

end

--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
function MysticalSkinBuy:_EnableGamepadFeature()
    if not IsHD() then
        return 
    end

    -- 如果是我的关注页，且无任何关注道具，不做任何处理
    if not self._itemId and self._saleListInfo and self._saleListInfo.sale_lists and table.isempty(self._saleListInfo.sale_lists or {}) then
        return
    end

    if not self._NavGroup_ItemViewList then
        self._NavGroup_ItemViewList = WidgetUtil.RegisterNavigationGroup(self._wtItemViewList, self, "Hittest")
        if self._NavGroup_ItemViewList then
            self._NavGroup_ItemViewList:AddNavWidgetToArray(self._wtItemViewList)
            self._NavGroup_ItemViewList:SetScrollRecipient(self._wtItemViewList)
            self._NavGroup_ItemViewList:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup_ItemViewList)
        end
    end

    if not self._NavGroup_wtDropDownsBox then
        self._NavGroup_wtDropDownsBox = WidgetUtil.RegisterNavigationGroup(self._wtDropDownsBox, self, "Hittest")
        if self._NavGroup_wtDropDownsBox then
            self._NavGroup_wtDropDownsBox:AddNavWidgetToArray(self._wtDropDownsBox)
        end
    end

    --初始化下拉框
    self:_EnableDropDownBoxGamepadFeature()

    -- 购买按键响应
    -- if not self._buyHandle then
    --     self._buyHandle = self:AddInputActionBinding("Common_ButtonLeft", EInputEvent.IE_Pressed, self._OnBuyBtnClicked, self, EDisplayInputActionPriority.UI_Stack)
    -- end


    if not self._labelMarkHandle then
        self._labelMarkHandle = self:AddInputActionBinding("MysticalSkinLabelMark", EInputEvent.IE_Pressed, self._ChangeLabelMarkBtnState, self, EDisplayInputActionPriority.UI_Stack)
    end

    -- 翻页绑定
    if not self._PageUpHandle then
        self._PageUpHandle = self:AddInputActionBinding("MarketPageUp", EInputEvent.IE_Pressed, self._TryPageUp, self, EDisplayInputActionPriority.UI_Stack)
    end
    
    if not self._PageDownHandle then
        self._PageDownHandle = self:AddInputActionBinding("MarketPageDown", EInputEvent.IE_Pressed, self._TryPageDown, self, EDisplayInputActionPriority.UI_Stack)
    end
    

    -- 翻页keyIcon显示
    if self._wtKeyIconBoxLeft then
        --设置是否只在Gamepad上显示
        self._wtKeyIconBoxLeft:SetOnlyDisplayOnGamepad(true)
        --设置当前KeyIcon绑定的Action
        self._wtKeyIconBoxLeft:InitByDisplayInputActionName("MarketPageUp", true, 0, true)
    end
    if self._wtKeyIconBoxRight then
        --设置是否只在Gamepad上显示
        self._wtKeyIconBoxRight:SetOnlyDisplayOnGamepad(true)
        --设置当前KeyIcon绑定的Action
        self._wtKeyIconBoxRight:InitByDisplayInputActionName("MarketPageDown", true, 0, true)
    end

    -- 绑定多输入设备切换事件
    if not self._OnNotifyInputTypeChangedHandle then
        self._OnNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._OnInputTypeChanged, self))
        -- 初始化
        local curInputType = WidgetUtil.GetCurrentInputType()
        self:_OnInputTypeChanged(curInputType)
    end
end

function MysticalSkinBuy:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end

    if self._NavGroup_ItemViewList or self._NavGroup_wtDropDownsBox then
        WidgetUtil.RemoveNavigationGroup(self)
        self._NavGroup_ItemViewList = nil
        self._NavGroup_wtDropDownsBox = nil
    end

    self:_DisableDropDownBoxGamepadFeature()

    -- 移除按键绑定
    if self._buyHandle then
        self:RemoveInputActionBinding(self._buyHandle)
        self._buyHandle = nil
    end

    if self._PageUpHandle then
        self:RemoveInputActionBinding(self._PageUpHandle)
        self._PageUpHandle = nil
    end
    
    if self._PageDownHandle then
        self:RemoveInputActionBinding(self._PageDownHandle)
        self._PageDownHandle = nil
    end

    if self._OnNotifyInputTypeChangedHandle then
        UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._OnNotifyInputTypeChangedHandle)
        self._OnNotifyInputTypeChangedHandle = nil
    end

    -- 隐藏时还原底部栏显示
    Module.CommonBar:RecoverBottomBarInputSummaryList()
end

function MysticalSkinBuy:_OnInputTypeChanged(InputType)
    if not IsHD() then
        return 
    end

    if InputType == EGPInputType.Gamepad then
        -- 手柄模式下显示额外的底部按键提示
        local summaryList = {}
        table.insert(summaryList, {actionName = "MysticalSkinLabelMark", func = nil, caller = self, bUIOnly = true, bHideIcon = false})
        table.insert(summaryList, {actionName = "Common_ToggleTip", func = self._ToggleTips, caller = self, bUIOnly = false, bHideIcon = false}) -- 查看Tips
        table.insert(summaryList, {actionName = "MysticalSkinShowDetail_Gamepad", func = self._OnViewModelBtnClicked, caller = self, bUIOnly = false, bHideIcon = false}) -- 查看详情按键响应
        Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, false, true)
    else
        -- 非手柄模式下，清空不需要的底部栏
        local summaryList = {}
        Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, false, true)
    end
end

function MysticalSkinBuy:_ToggleTips()
    if not IsHD() then
        return 
    end
    
    if not self._wtItemDetailView then
        return
    end
    
    local detailContent = self._wtItemDetailView:GetDetailContent()
    if detailContent then
        if detailContent._wtContentWeaponMysticalSkin and detailContent._wtContentWeaponMysticalSkin.ToggleTips then
            detailContent._wtContentWeaponMysticalSkin:ToggleTips()
        elseif detailContent._wtContentOrnaments and detailContent._wtContentOrnaments.ToggleTips then
            detailContent._wtContentOrnaments:ToggleTips()
        end
    end
end

function MysticalSkinBuy:_ChangeLabelMarkBtnState()
    if not IsHD() then
        return 
    end
    if self._wtLabelMarkBtn then
        self._wtLabelMarkBtn:SelfClick()
    end
end

function MysticalSkinBuy:_TryPageUp()
    if not IsHD() then
        return 
    end

    if self._wtPageUpBtn and self._wtPageUpBtn:GetIsEnabled() then
        self:OnPageUpBtnClick()
    end
end

function MysticalSkinBuy:_TryPageDown()
    if not IsHD() then
        return 
    end

    if self._wtPageDownBtn and self._wtPageDownBtn:GetIsEnabled() then
        self:OnPageDownBtnClick()
    end
end

function MysticalSkinBuy:_EnableDropDownBoxGamepadFeature()
    if not IsHD() then
        return 
    end

    if self._wtZoomRateDroDownRarity and self._wtZoomRateDroDownRarity.MenuAnchor then
        self._wtZoomRateDroDownRarity:Event("PostOnMenuOpenChanged_GamepadUsed", self._OnRarityDropDownBoxOpenStateChanged, self)
    end

    if self._wtZoomRateDroDownWear and self._wtZoomRateDroDownWear.MenuAnchor then
        self._wtZoomRateDroDownWear:Event("PostOnMenuOpenChanged_GamepadUsed", self._OnWearDropDownBoxOpenStateChanged, self)
    end

    if self._wtZoomRateDroDownNotice and self._wtZoomRateDroDownNotice.MenuAnchor then
        self._wtZoomRateDroDownNotice:Event("PostOnMenuOpenChanged_GamepadUsed", self._OnNoticeDropDownBoxOpenStateChanged, self)
    end

    if self._wtZoomRateDroDownSort and self._wtZoomRateDroDownSort.MenuAnchor then
        self._wtZoomRateDroDownSort:Event("PostOnMenuOpenChanged_GamepadUsed", self._OnSortDropDownBoxOpenStateChanged, self)
    end
end

function MysticalSkinBuy:_DisableDropDownBoxGamepadFeature()
    if not IsHD() then
        return 
    end

    if self._wtZoomRateDroDownRarity and self._wtZoomRateDroDownRarity.MenuAnchor then
        self._wtZoomRateDroDownRarity:RemoveEvent("PostOnMenuOpenChanged_GamepadUsed")
    end

    if self._wtZoomRateDroDownWear and self._wtZoomRateDroDownWear.MenuAnchor then
        self._wtZoomRateDroDownWear:RemoveEvent("PostOnMenuOpenChanged_GamepadUsed")
    end

    if self._wtZoomRateDroDownNotice and self._wtZoomRateDroDownNotice.MenuAnchor then
        self._wtZoomRateDroDownNotice:RemoveEvent("PostOnMenuOpenChanged_GamepadUsed")
    end

    if self._wtZoomRateDroDownSort and self._wtZoomRateDroDownSort.MenuAnchor then
        self._wtZoomRateDroDownSort:RemoveEvent("PostOnMenuOpenChanged_GamepadUsed")
    end

    self:_RemoveDropDownInputActions()
end

function MysticalSkinBuy:_OnRarityDropDownBoxOpenStateChanged(bOpen)
    if not IsHD() then
        return 
    end

    if bOpen then
        -- 注册导航组
        if not self._NavGroup_RarityDropDown then
            self._NavGroup_RarityDropDown = WidgetUtil.RegisterNavigationGroup(self._wtZoomRateDroDownRarity.ScrollGridBox, self._wtZoomRateDroDownRarity, "Hittest")
        end

        if self._NavGroup_RarityDropDown then
            self._NavGroup_RarityDropDown:AddNavWidgetToArray(self._wtZoomRateDroDownRarity.ScrollGridBox)
            self._NavGroup_RarityDropDown:MarkIsStackControlGroup()
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup_RarityDropDown)
        end

        -- 绑定输入
        self:_RemoveDropDownInputActions()

        if not self._closeZoomRateDroDownRarityHandler then
            self._closeZoomRateDroDownRarityHandler = self:AddInputActionBinding("Back", EInputEvent.IE_Pressed, self._CloseZoomRateDropDownRarity, self, EDisplayInputActionPriority.UI_Pop)
        end
    else
        if self._NavGroup_RarityDropDown then
            WidgetUtil.RemoveNavigationGroup(self._wtZoomRateDroDownRarity)
            self._NavGroup_RarityDropDown = nil
            if self._closeZoomRateDroDownRarityHandler then
                self:RemoveInputActionBinding(self._closeZoomRateDroDownRarityHandler)
                self._closeZoomRateDroDownRarityHandler = nil
            end
        end
    end
end

function MysticalSkinBuy:_CloseZoomRateDropDownRarity()
    if not IsHD() then
        return 
    end

    if self._wtZoomRateDroDownRarity and self._wtZoomRateDroDownRarity.CloseMenu then
        self._wtZoomRateDroDownRarity:CloseMenu()
    end
end

function MysticalSkinBuy:_OnWearDropDownBoxOpenStateChanged(bOpen)
    if not IsHD() then
        return 
    end

    if bOpen then
        -- 注册导航组
        if not self._NavGroup_WearDropDown then
            self._NavGroup_WearDropDown = WidgetUtil.RegisterNavigationGroup(self._wtZoomRateDroDownWear.ScrollGridBox, self._wtZoomRateDroDownWear, "Hittest")
        end

        if self._NavGroup_WearDropDown then
            self._NavGroup_WearDropDown:AddNavWidgetToArray(self._wtZoomRateDroDownWear.ScrollGridBox)
            self._NavGroup_WearDropDown:MarkIsStackControlGroup()
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup_WearDropDown)
        end

        -- 绑定输入
        self:_RemoveDropDownInputActions()

        if not self._closeZoomRateDroDownWearHandler then
            self._closeZoomRateDroDownWearHandler = self:AddInputActionBinding("Back", EInputEvent.IE_Pressed, self._CloseZoomRateDropDownWear, self, EDisplayInputActionPriority.UI_Pop)
        end
    else
        if self._NavGroup_WearDropDown then
            WidgetUtil.RemoveNavigationGroup(self._wtZoomRateDroDownWear)
            self._NavGroup_WearDropDown = nil
            if self._closeZoomRateDroDownWearHandler then
                self:RemoveInputActionBinding(self._closeZoomRateDroDownWearHandler)
                self._closeZoomRateDroDownWearHandler = nil
            end
        end
    end
end

function MysticalSkinBuy:_CloseZoomRateDropDownWear()
    if not IsHD() then
        return 
    end

    if self._wtZoomRateDroDownWear and self._wtZoomRateDroDownWear.CloseMenu then
        self._wtZoomRateDroDownWear:CloseMenu()
    end
end

function MysticalSkinBuy:_OnNoticeDropDownBoxOpenStateChanged(bOpen)
    if not IsHD() then
        return 
    end

    if bOpen then
        -- 注册导航组
        if not self._NavGroup_NoticeDropDown then
            self._NavGroup_NoticeDropDown = WidgetUtil.RegisterNavigationGroup(self._wtZoomRateDroDownNotice.ScrollGridBox, self._wtZoomRateDroDownNotice, "Hittest")
        end

        if self._NavGroup_NoticeDropDown then
            self._NavGroup_NoticeDropDown:AddNavWidgetToArray(self._wtZoomRateDroDownNotice.ScrollGridBox)
            self._NavGroup_NoticeDropDown:MarkIsStackControlGroup()
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup_NoticeDropDown)
        end

        -- 绑定输入
        self:_RemoveDropDownInputActions()

        if not self._closeZoomRateDroDownNoticeHandler then
            self._closeZoomRateDroDownNoticeHandler = self:AddInputActionBinding("Back", EInputEvent.IE_Pressed, self._CloseZoomRateDropDownNotice, self, EDisplayInputActionPriority.UI_Pop)
        end
    else
        if self._NavGroup_NoticeDropDown then
            WidgetUtil.RemoveNavigationGroup(self._wtZoomRateDroDownNotice)
            self._NavGroup_NoticeDropDown = nil
            if self._closeZoomRateDroDownNoticeHandler then
                self:RemoveInputActionBinding(self._closeZoomRateDroDownNoticeHandler)
                self._closeZoomRateDroDownNoticeHandler = nil
            end
        end
    end
end

function MysticalSkinBuy:_CloseZoomRateDropDownNotice()
    if not IsHD() then
        return 
    end

    if self._wtZoomRateDroDownNotice and self._wtZoomRateDroDownNotice.CloseMenu then
        self._wtZoomRateDroDownNotice:CloseMenu()
    end
end

function MysticalSkinBuy:_OnSortDropDownBoxOpenStateChanged(bOpen)
    if not IsHD() then
        return 
    end

    if bOpen then
        -- 注册导航组
        if not self._NavGroup_SortDropDown then
            self._NavGroup_SortDropDown = WidgetUtil.RegisterNavigationGroup(self._wtZoomRateDroDownSort.ScrollGridBox, self._wtZoomRateDroDownSort, "Hittest")
        end

        if self._NavGroup_SortDropDown then
            self._NavGroup_SortDropDown:AddNavWidgetToArray(self._wtZoomRateDroDownSort.ScrollGridBox)
            self._NavGroup_SortDropDown:MarkIsStackControlGroup()
            self._NavGroup_SortDropDown:SetScrollRecipient(self._wtZoomRateDroDownSort.ScrollGridBox)
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup_SortDropDown)
        end

        -- 绑定输入
        self:_RemoveDropDownInputActions()

        if not self._closeZoomRateDroDownSortHandler then
            self._closeZoomRateDroDownSortHandler = self:AddInputActionBinding("Back", EInputEvent.IE_Pressed, self._CloseZoomRateDropDownSort, self, EDisplayInputActionPriority.UI_Pop)
        end
    else
        if self._NavGroup_SortDropDown then
            WidgetUtil.RemoveNavigationGroup(self._wtZoomRateDroDownSort)
            self._NavGroup_SortDropDown = nil
            if self._closeZoomRateDroDownSortHandler then
                self:RemoveInputActionBinding(self._closeZoomRateDroDownSortHandler)
                self._closeZoomRateDroDownSortHandler = nil
            end
        end
    end
end

function MysticalSkinBuy:_CloseZoomRateDropDownSort()
    if not IsHD() then
        return 
    end

    if self._wtZoomRateDroDownSort and self._wtZoomRateDroDownSort.CloseMenu then
        self._wtZoomRateDroDownSort:CloseMenu()
    end
end

function MysticalSkinBuy:_RemoveDropDownInputActions()
    if not IsHD() then
        return 
    end

    if self._closeZoomRateDroDownRarityHandler then
        self:RemoveInputActionBinding(self._closeZoomRateDroDownRarityHandler)
        self._closeZoomRateDroDownRarityHandler = nil
    end

    if self._closeZoomRateDroDownWearHandler then
        self:RemoveInputActionBinding(self._closeZoomRateDroDownWearHandler)
        self._closeZoomRateDroDownWearHandler = nil
    end

    if self._closeZoomRateDroDownNoticeHandler then
        self:RemoveInputActionBinding(self._closeZoomRateDroDownNoticeHandler)
        self._closeZoomRateDroDownNoticeHandler = nil
    end

    if self._closeZoomRateDroDownSortHandler then
        self:RemoveInputActionBinding(self._closeZoomRateDroDownSortHandler)
        self._closeZoomRateDroDownSortHandler = nil
    end

end
--- END MODIFICATION

return MysticalSkinBuy