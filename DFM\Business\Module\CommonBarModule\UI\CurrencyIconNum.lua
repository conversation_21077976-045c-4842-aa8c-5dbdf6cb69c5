----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCommonBar)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class CurrencyIconNum : LuaUIBaseView
local CurrencyIconNum = ui("CurrencyIconNum")
local ShopHelperTool = require "DFM.StandaloneLua.BusinessTool.ShopHelperTool"

function CurrencyIconNum:Ctor()
	self._currencyId = ECurrencyClientId.Tina
	self._wtImgCurrency = self:Wnd("Image_Currency", UIImage)
	self._wtTbCurrencyValue = self:Wnd("wtTextBlock_value", UITextBlock)
	self._wtTbCurrencyValue:SetText("")
	self._wtBtnCurrencyTip = self:Wnd("ButtonCurrency", UIButton)
	self._wtBtnCurrencyTip:Event("OnClicked", self.OnCurrencyTipBtnClick, self)
	self._wtBtnCurrencyAdd = self:Wnd("Button_44", UIButton)
	self._wtBtnCurrencyAdd:Event("OnClicked", self.OnCurrencyAddBtnClick, self)
	self._wtBtnCurrencyAdd:Collapsed()

	-- self:AddLuaEvent(Server.InventoryServer.Events.evtCurrencyUpdated, self.OnCurrencyUpdate, self)
	-- self:AddLuaEvent(Server.CurrencyServer.Events.evtCurrencyNumChanged, self.OnEvtCurrencyNumChanged, self)
	-- self:AddLuaEvent(Server.ModuleUnlockServer.Events.evtSwitchMoudleUnlock,self._ShowModuleNtf,self)
end

function CurrencyIconNum:OnOpen()
	self:AddLuaEvent(Server.CurrencyServer.Events.evtCurrencyNumChanged, self.OnEvtCurrencyNumChanged, self)
	self:AddLuaEvent(Server.ModuleUnlockServer.Events.evtSwitchMoudleUnlock,self._ShowModuleNtf,self)
	self:AddLuaEvent(Server.RankingServer.Events.evtSeasonMallCurrencyUpdated, self.OnEvtCurrencyNumChanged, self)
    self:_CheckShowModule()
end

function CurrencyIconNum:OnClose()
    self:RemoveAllLuaEvent()
end

-- function CurrencyIconNum:OnCurrencyUpdate()
-- 	local num = Module.Currency:GetNum(self._currencyId)
-- 	self:SetCurrencyInfoById(self._currencyId, num)
-- end

function CurrencyIconNum:OnEvtCurrencyNumChanged()
	local num = Module.Currency:GetRealNum(self._currencyId)
	self:SetCurrencyInfoById(self._currencyId, num)
end

function CurrencyIconNum:SetCurrencyInfoById(currencyId, num)
	self._currencyId = setdefault(currencyId, ECurrencyClientId.Tina)
	local numStr = ShopHelperTool.GetCurrencyNumFormatStr(num)

	local currencyImgPath = Module.Currency:GetImgPath(self._currencyId)
	self._wtTbCurrencyValue:SetText(numStr)
	-- --Limit Text Width（在控件设置时判断会每次都执行，所以注释掉）
	-- local characterwidth = 7
	-- if #numStr <= characterwidth then
	-- 	self._wtTbCurrencyValue:SetText(numStr)
	-- else
	-- 	self._wtTbCurrencyValue:SetText(string.sub(numStr, 1, characterwidth))
	-- end
	self._wtImgCurrency:AsyncSetImagePath(currencyImgPath)


	local bShowAddBtn = Module.CommonBar.Config.MapCurrencyCID2CanRecharge[self._currencyId] or false
	if bShowAddBtn then
		self._wtBtnCurrencyAdd:Visible()
	else
		self._wtBtnCurrencyAdd:Collapsed()
	end
end

function CurrencyIconNum:OnCurrencyTipBtnClick()
	--点券不弹tips
	if self._currencyId == ECurrencyClientId.Diamond then
		Module.CommonBar.Config.evtShowCurrencyTip:Invoke(self, self._currencyId)
	else
		Module.CommonBar.Config.evtShowCurrencyTip:Invoke(self, self._currencyId)
	end
end

function CurrencyIconNum:OnCurrencyAddBtnClick()
	Module.CommonBar.Config.evtOnChangeButtonClicked:Invoke()
	if self._currencyId == ECurrencyClientId.Diamond or self._currencyId == ECurrencyClientId.UnbindDiamond then
		Module.Store:ShowStoreRechargeMainPanle()
    elseif self._currencyId == ECurrencyClientId.MandelCoins then -- 曼德尔币
        Facade.UIManager:AsyncShowUI(UIName2ID.ExchangeMandelCoins, nil, nil, 17888808887)
	elseif self._currencyId == ECurrencyClientId.QuantumKey then
		local buyManderKeysConfig = Server.StoreServer:GetSpecialItemInfoByPresentItemId(32320000001)
		Module.Store.OpenStoreMandelBuyKeysPanel(nil, buyManderKeysConfig, 0, 0, 1, 1)
	elseif self._currencyId == ECurrencyClientId.Reinvention then
		local mysticalLotteryId = Module.Hero:GetCurShowHeroPropsMysticalLotteryId()
		if mysticalLotteryId then
			Facade.UIManager:AsyncShowUI(UIName2ID.HeroPropMysticalSkinPurchasePop, nil, nil,nil,  ECurrencyItemId.Reinvention, 1 , mysticalLotteryId)
		end
	else
		if self:_ActTestCoin(self._currencyId) then
			return
		end
		Module.CommonTips:ShowSimpleTip(Module.CommonTips.Config.Loc.CommingSoon)
	end
end

--活动货币逻辑(策划某改)
function CurrencyIconNum:_ActTestCoin(itemId)
	--货币转化
    local curkey = nil
    for key, value in pairs(ECurrencyClientId or {}) do
        if itemId == value then
            curkey = key
            break
        end
    end
    for key, value in pairs(ECurrencyItemId or {}) do
        if curkey == key then
            Module.Activity:OpenLogisticsVoucherPanel(value)
            return true
        end
    end
    return false
end

-- -- 检查功能模块是否解锁
function CurrencyIconNum:_CheckShowModule()
    if Module.ModuleSwitcher:CheckModuleSwitcher(SwitchSystemID.SwitchSyetemStore) == EFirstLockResult.Open then
        self._wtBtnCurrencyAdd:Visible()
    else
        self._wtBtnCurrencyAdd:Collapsed()
    end
end

function CurrencyIconNum:_ShowModuleNtf(moduleId, isUnlock)
    if moduleId== SwitchSystemID.SwitchSyetemStore then
		if isUnlock then
        	self._wtBtnCurrencyAdd:Visible()
		else
			self._wtBtnCurrencyAdd:Collapsed()
		end
    end
end

return CurrencyIconNum