----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMFriend)
local FriendLogic = require "DFM.Business.Module.FriendModule.Logic.FriendLogic"
----- LOG FUNCTION AUTO GENERATE END -----------



---@class FriendPanel : LuaUIBaseView
local FriendPanel = ui("FriendPanel")
local OFF_LINE = GlobalPlayerStateEnums.EPlayerState_Offline
local ETextCommit = import "ETextCommit"
local DiscordBindLogic = require "DFM.Business.Module.FriendModule.Logic.DiscordBindLogic"
local Functional       = require "DFM.Business.DataStruct.Common.Base.Functional"

---@enum 好友类型枚举
local EPanelType = {
    Game = 1,
    Open = 2,
    Discord = 3,
}

--Chat_Btn_14 Chat_Btn_56
--GetFirstVisibleItemIndex
--GetLastVisibleItemIndex
function FriendPanel:Ctor()
    loginfo("FriendPanel:Ctor")
    self._wtAddFriend = self:Wnd("wtAddFriend", UIWidgetBase)
    self._wtAddFriend:Event("OnClicked", self._OnBtnAddFriendClick, self)
    self._wtBlackList = self:Wnd("wtBlackList", UIWidgetBase)
    self._wtBlackList:Event("OnClicked", self._OnBtnBlackListClick, self)

    self._wtFindBtn = self:Wnd("wtFindBtn", UIWidgetBase)
    self._wtFindBtn:Event("OnClicked", self._OnBtnAddFindFriendClick, self)

    local function OnHighFrequency()
        Module.CommonTips:ShowSimpleTip(Module.Activity.Config.Loc.HighFrequency)
    end
    local fDiscordCallback = CreateCallBack(Functional.HighFrequencyFilter(self._OnDiscordBindBtnClick, 10, OnHighFrequency), self)

    --- Discord账号绑定按钮
    self._discordBindBtn = self:Wnd("WBP_CommonButtonV3S1_91", DFCommonButtonOnly)
    self._discordBindBtn:BP_SetMainTitle(Module.Friend.Config.DiscordBindPopText[4])
    self._discordBindBtn:Event("OnClicked", fDiscordCallback, self)

    self._wtFindText = self:Wnd("_wtFindText", UITextBlock)

    self._wtEmptyPanel = self:Wnd("EmptySlot", UIWidgetBase)

    self._wtFriendPanel = self:Wnd("_wtFriendPanel", UIWidgetBase)

    local fOnChangedCallbackIns = CreateCallBack(self._OnSearchFriend, self)
    self._wtPlayerInput = UIUtil.WndInputBoxWithBtn(self, "WBP_inputBox", -1, nil, fOnChangedCallbackIns)

    self._wtSearchTxt = self._wtPlayerInput:Wnd("wtDFEditableTextBox", UITextBlock)

    self._wtFriendList = UIUtil.WndScrollGridBox(self, "wtFriendList", self._OnGetFriendCount, self._OnProcessFriendWidget)
    self._friendTbl = {}

    self._friendList = {}
    self._scollBoxTbl = {}
    self._isSearch = false
    self._panelType = EPanelType.Game

    self._friendIndex = 1
end

-----------------------------------------------------------------------
--region 生命周期

--endregion
-----------------------------------------------------------------------

function FriendPanel:OnInitExtraData(isOpenFriend)
    if isOpenFriend then
        if IsBuildRegionGlobal() then
            self._panelType = EPanelType.Discord
        else
            self._panelType = EPanelType.Open
        end
    else
        self._panelType = EPanelType.Game
    end
end

function FriendPanel:OnOpen()
    --self._reddot = Module.Reddot:Register(self._wtApplyFriend,EReddotType.Number)
end

function FriendPanel:OnShowBegin()
    local key = string.format("FriendMessage_Type%s", 2)
    local data = Module.ReddotTrie:GetReddotData(EReddotTrieObserverType.Friend, key)
    self._reddot= Module.ReddotTrie:RegisterStaticReddotDot(self._wtAddFriend, {{reddotData = data,reddotStyle={reddotType=EReddotType.Number}}})
    self._wtSearchTxt:SetText("")
    self:AddListeners()
    self:RefreshFriendList()
end

function FriendPanel:OnShow()
    if IsBuildRegionGlobal() then
        DiscordBindLogic.CheckDiscordFriendBindState()
    end
end

function FriendPanel:OnHideBegin()
    Module.ReddotTrie:UnRegisterStaticReddotDot(self._reddot)
    self._reddot=nil
end

function FriendPanel:OnHide()
    self:_RemoveTickTimer()
end

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
function FriendPanel:OnClose()
    self:RemoveAllLuaEvent()
    self:_RemoveTickTimer()
    Facade.UIManager:ClearSubUIByParent(self, self._wtEmptyPanel)
end

function FriendPanel:RefreshFriendList()

    if self._panelType == EPanelType.Game then
        self._friendList = Server.FriendServer:GetGameFriendPanelList()
    elseif self._panelType == EPanelType.Open then
        self._friendList = Server.FriendServer:GetOpenFriendPanelList()
    else
        self._friendList = Server.FriendServer:GetDiscordFriendPanelList()
    end
    
    if #self._friendList == 0 then
        self:CreateNothingUIByText()
    else
        self._wtEmptyPanel:Collapsed()
        self._discordBindBtn:Collapsed()
    end
    self._friendTbl = self._friendList
    FriendLogic.SortFriendList(self._friendList)
    self._wtFriendList:RefreshAllItems()
    if not self._friendListRefresh then
        self._friendListRefresh = Timer:NewIns(2, 0)
        self._friendListRefresh:AddListener(self._GetFriendState, self)
        self._friendListRefresh:Start()
    end
    self:_GetFriendState()
end
--------------------------------------------------------------------------
--好友状态刷新
--------------------------------------------------------------------------

function FriendPanel:_GetFriendState()
    local friendIdList = {}
    local bContinue = true
    for index = self._friendIndex , self._friendIndex + 49 do
        if self._friendList[index] then
            table.insert(friendIdList, self._friendList[index].player_id)
        else
            self._friendIndex = 1
            bContinue = false
            break
        end
    end
    local CallFunction = CreateCallBack(self._RefreshFriendState, self, bContinue)
    Server.FriendServer:ReFetchFriendStateList(CallFunction, friendIdList)
end

function FriendPanel:_RefreshFriendState(bContinue, stateList)
    for i, info in ipairs(self._friendList) do
        local stateInfo = stateList[info.player_id]
        if stateInfo then
            self._friendList[i].state = stateInfo.state
            self._friendList[i].fighting_time = stateInfo.fighting_time
            self._friendList[i].member_num = stateInfo.member_num
            self._friendList[i].team_id = stateInfo.team_id
            self._friendList[i].mode_info = stateInfo.mode_info
            self._friendList[i].plat = stateInfo.PlatID
            Server.FriendServer:UpdateFriendLoginInvisible(info.player_id, stateInfo.invisible)
        end
    end
    if bContinue then
        self:_GetFriendState()
    else
        FriendLogic.SortFriendList(self._friendList)
        self._wtFriendList:RefreshVisibleItems()
    end
end


-- UI监听事件、协议 --870009213
function FriendPanel:AddListeners()
    -- 普通事件
    self:AddLuaEvent(Server.FriendServer.Events.evtFriendListGetEnd, self._OnFriendList, self)

    self:AddLuaEvent(Server.FriendServer.Events.evtFriendListGetEnd, self.RefreshFriendList, self)
    self:AddLuaEvent(DiscordBindLogic.evtDiscordBindFriendSuccess, self.RefreshFriendList, self)
end

function FriendPanel:OnMainPanelTest()
end


function FriendPanel:_RemoveTickTimer()
    if self._friendListRefresh then
        self._friendListRefresh:Release()
        self._friendListRefresh = nil
    end
end

function FriendPanel:_OnBtnAddFriendClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.FriendAddMainPanel, nil, nil)
end

function FriendPanel:_OnDiscordBindBtnClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.FriendDiscordBindPop, nil, nil)
end

function FriendPanel:_OnBtnBlackListClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.FriendAddBlack, nil, nil)
end

function FriendPanel:_OnFriendList(player_list)
    if self._panelType == EPanelType.Game then
        self._friendList = Server.FriendServer:GetGameFriendPanelList()
    elseif self._panelType == EPanelType.Open then
        self._friendList = Server.FriendServer:GetOpenFriendPanelList()
    else
        self._friendList = Server.FriendServer:GetDiscordFriendPanelList()
    end

    if #self._friendList == 0 then
        self:CreateNothingUIByText()
    else
        self._wtEmptyPanel:Collapsed()
    end
    self._friendTbl = self._friendList
    FriendLogic.SortFriendList(self._friendList)
    if not self._isSearch then
        self._wtFriendList:RefreshAllItems()
    end
end

function FriendPanel:NoPlayAnim()
    for i = 1, #self._friendList do
        self._scollBoxTbl[i] = true
    end
end

function FriendPanel:_OnGetFriendCount()
    return #self._friendList
end

function FriendPanel:_OnProcessFriendWidget(index, itemWidget)
    loginfo("FriendPanel:_OnProcessFriendWidget")
    itemWidget:ShowUI(self._friendList[index + 1], self._panelType)
end

function FriendPanel:_OnSearchFriend(text)
    if not string.isempty(text) then
        local num ,str = StringUtil.GetRealWidth(text, 35)
        if num >= 35 then
          Module.CommonTips:AssignNextTipType_HD(true, false)
          Module.CommonTips:ShowSimpleTip(Module.Friend.Config.SearchFriendLimit)
          self._wtSearchTxt:SetText(str)
          text = str
        end
        self._wtFindText:SetText(string.format(Module.Friend.Config.GotoFindFriend, tostring(text)))
        self._wtFriendPanel:Visible()
        self._wtBlackList:Collapsed()
        self._wtAddFriend:Collapsed()
        local isNumber = Module.Friend:is_number(text)
        self._isSearch = true
        local findList = {}
        for _, info in ipairs(self._friendTbl) do
            if string.find(string.upper(info.nick_name) , string.upper(tostring(text))) then
                table.insert(findList, info)
            elseif isNumber and string.find(Module.RoleInfo:GOpenIdEncryption(ULuautils.GetUInt64String(info.player_id)), tostring(text)) then
                table.insert(findList, info)
            end
        end
        self._friendList = findList
        FriendLogic.SortFriendList(self._friendList)
        self._wtFriendList:RefreshAllItems()
    else
        self._wtBlackList:Visible()
        self._wtFriendPanel:Collapsed()
        self._wtAddFriend:Visible()
        self._isSearch = false

        if self._panelType == EPanelType.Game then
            self._friendList = Server.FriendServer:GetGameFriendPanelList()
        elseif self._panelType == EPanelType.Open then
            self._friendList = Server.FriendServer:GetOpenFriendPanelList()
        else
            self._friendList = Server.FriendServer:GetDiscordFriendPanelList()
        end

        self._wtFriendList:RefreshAllItems()
    end

    if #self._friendList == 0 then
        self:CreateNothingUIByText()
    else
        self._wtEmptyPanel:Collapsed()
    end
end

function FriendPanel:_OnBtnAddFindFriendClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.FriendAddMainPanel, nil, nil, self._wtSearchTxt:GetText())
    self._wtSearchTxt:SetText("")
end


function FriendPanel:_OnSearchTextCommitted(text, commitType)

end

function FriendPanel:CreateNothingUIByText()
    if not self._nothingIns then
        self._wtEmptyPanel:SelfHitTestInvisible()
        local weakUIIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.CommonEmptyContent, self._wtEmptyPanel)
        self._nothingIns = getfromweak(weakUIIns)
        if self._nothingIns then
            self._nothingIns:BP_SetTypeWithParam(1)
            self._nothingIns:Visible()
        end
    else
        self._wtEmptyPanel:SelfHitTestInvisible()
    end
    
    if self._panelType == EPanelType.Discord and not Module.Friend.Field:GetIsDiscordFriendBinded() then
        self:SetEmpty(1)
        self._discordBindBtn:Visible()
        self._nothingIns:BP_SetText(Module.Friend.Config.DiscordNotBinded)
    else
        self:SetEmpty(0)
        self._discordBindBtn:Collapsed()
        self._nothingIns:BP_SetText(Module.Friend.Config.NothingFriend)
    end
end

return FriendPanel