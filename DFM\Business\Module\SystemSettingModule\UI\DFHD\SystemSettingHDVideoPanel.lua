----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class SystemSettingHDVideoPanel
local SystemSettingHDVideoPanel = ui("SystemSettingHDVideoPanel")
local SettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.SettingLogicHD"
local CommonSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.CommonSettingLogicHD"
local VideoSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.VideoSettingLogicHD"
local UClientVideoSettingHD = import("ClientVideoSettingHD")
local UKismetSystemLibrary = import "KismetSystemLibrary"
local UClientSettingHelperHD = import "ClientSettingHelperHD"
local VRamSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.VRamSettingLogicHD"
local UHardwareParamHelper = import("HardwareParamHelper")
local UVideoSettingHelper = import("VideoSettingHelper")
local UGPGConfigUtils = import "GPGConfigUtils"

-- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

function SystemSettingHDVideoPanel:Ctor()
    self:_BindWidget()
    self:_InitSuperResolutionQualityItem()
    -- BEGIN MODIFICATION @ VIRTUOS :
    self:_InitBtnDisplayAction()
    -- END MODIFICATION
end

function SystemSettingHDVideoPanel:_BindWidget()
    self._wtBtnUndo = self:Wnd("wtCommonButtonV1S2", UIWidgetBase)
    self._InnerBtnUndo = self._wtBtnUndo:Wnd("DFCommonButton", UIWidgetBase)
    self._wtBtnApply = self:Wnd("wtCommonButtonV1S1", UIWidgetBase)
    self._wtInnerBtnApply = self._wtBtnApply:Wnd("DFCommonButton", UIWidgetBase)
    self._wtDescRootPanel = self:Wnd("DescRootPanel", UILightWidget)

    self._wtItemPanel = self:Wnd("wtItemPanel", UILightWidget)
    self._wtRayTracingPanel = self:Wnd("_wtRayTracingPanel", UILightWidget)
    self._wtItemLowMemoryMode =self:Wnd("_wtItemLowMemoryMode", UIWidgetBase)

    -- 临时扩大列表高度
    local _wtItemGraphicsPreset = self:Wnd("_wtItemGraphicsPreset", UIWidgetBase)
    local _innerDropdown = _wtItemGraphicsPreset:Wnd("WBP_DFTabGroupDroDownBox_Pc", UIWidgetBase)
    local _innerSizeBox = _innerDropdown:Wnd("DFSizeBox_1", UILightWidget)
    _innerSizeBox:SetMaxDesiredHeight(780)

    self._wtItemSuperResolutionMethod = self:Wnd("_wtItemSuperResolutionMethod", UIWidgetBase)
    self._wtItemGraphicsPreset = self:Wnd("_wtItemGraphicsPreset", UIWidgetBase)
    self._wtItemSuperResolutionQualityDLSS = self:Wnd("_wtItemSuperResolutionQualityDLSS", UIWidgetBase)
    self._wtItemSuperResolutionQualityTSR = self:Wnd("_wtItemSuperResolutionQualityTSR", UIWidgetBase)
    self._wtItemSuperResolutionQualityFSR2 = self:Wnd("_wtItemSuperResolutionQualityFSR2", UIWidgetBase)
    self._wtItemSuperResolutionQualityFSR3 = self:Wnd("_wtItemSuperResolutionQualityFSR3", UIWidgetBase)
    self._wtItemSuperResolutionQualityXESS = self:Wnd("_wtItemSuperResolutionQualityXESS", UIWidgetBase)
    self._wtItemRTXEnabled = self:Wnd("_wtItemRTXEnabled", UIWidgetBase)

    self._wtItemScopeUseRT = self:Wnd("_wtItemScopeUseRT", UIWidgetBase)

    self._wtItemDLSSReflexMode = self:Wnd("_wtItemDLSSReflexMode", UIWidgetBase)
    self._wtFastSync = self:Wnd("WBP_SetUpComponent_FastSync", UIWidgetBase)
    if not UHardwareParamHelper.HasNVIDIADevice() then
        self._wtItemDLSSReflexMode:Collapsed()
        self._wtFastSync:Collapsed()
    end

    self._wtVRamRoot = self:Wnd("_wtVRamRoot", UILightWidget)

    self._wtItemPerfMode_Console = self:Wnd("_wtItemPerfMode_Console", UIWidgetBase)
    if not IsConsole() and self._wtItemPerfMode_Console ~= nil then  --- 非主机平台不要显示这个选项
        self._wtItemPerfMode_Console:Collapsed()
    end
    
    self._wtItemFSR3FrameGeneration = self:Wnd("_wtItemFSR3FrameGeneration", UIWidgetBase)
    self._wtItemFSR3FrameGeneration:Collapsed()

    --BEGIN MODIFICATION @ VIRTUOS : 
    --- 主机平台开启部分设置项
    if IsConsole() then
        local showSettingList = {
            "WBP_SetUpComponent_ListTitle",
            "_wtItemBrightness_jump",
            "WBP_SetUpComponent_VSync",
            "_wtItemPerfMode_Console",
            "WBP_SetUpComponent_ListTitle_1",
            "WBP_SetUpComponent_SplitBtn"
        }

        if self._wtItemPanel then
            for i, widget in ipairs(self._wtItemPanel:GetAllChildren()) do
                if widget then
                    widget:Collapsed()
                end
            end
        end

        for i, widgetName in ipairs(showSettingList) do
            local showWidget = self:Wnd(widgetName, UIWidgetBase)
            if showWidget and showWidget.SetVisibility then
                showWidget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
            end
        end
    end
    --END MODIFICATION
end

function SystemSettingHDVideoPanel:_BindBtnEvent()
    self._InnerBtnUndo:SetIsEnabledStyle(false)
    self._InnerBtnUndo:Event("OnClicked", self._OnUndo, self)
    self._wtInnerBtnApply:Event("OnClicked", self._OnApply, self)
    self._wtInnerBtnApply:SetIsEnabledStyle(false)
    self:AddLuaEvent(Module.SystemSetting.Config.Event.evtPendingSettingChangedHD, self._OnPendingSettingChanged, self)

    self:AddLuaEvent(self._wtItemSuperResolutionMethod.evtOnMethodChanged, self._OnSuperResolutionMethodChanged, self)
    self:AddLuaEvent(self._wtItemGraphicsPreset.evtOnApplyRecommand, self._InitSuperResolutionQualityItem, self)

    self:AddLuaEvent(self._wtItemRTXEnabled.evtOnStateChanged, self._OnRayTracingStateChanged, self)

    self:AddLuaEvent(self._wtItemScopeUseRT.evtOnStateChanged, self._OnScopeUseRTStateChanged, self)

    -- BEGIN MODIFICATION @ VIRTUOS : 因为关闭亮度弹窗后，虽然会走ShowBegin，但是页签还是第一页的gameplay,GetCurrentTabTypeHD那里的判断过不了
    self:AddLuaEvent(Module.SystemSetting.Config.Event.evtSettingBrightnessHideHD, self._RegisterNavGroup, self)
    -- END MODIFICATION

    self:AddLuaEvent(self._wtItemFSR3FrameGeneration.evtOnStateChanged, self._OnFSR3FrameGenerationChanged, self)
end

function SystemSettingHDVideoPanel:_OnPendingSettingChanged()
    if CommonSettingLogicHD.AnyPendingSetting() then
        self._InnerBtnUndo:SetIsEnabledStyle(true)
        self._wtInnerBtnApply:SetIsEnabledStyle(true)
        -- BEGIN MODIFICATION @ VIRTUOS :
        self:_InitShortCuts()
        -- END MODIFICATION
    else
        self._InnerBtnUndo:SetIsEnabledStyle(false)
        self._wtInnerBtnApply:SetIsEnabledStyle(false)
        -- BEGIN MODIFICATION @ VIRTUOS :
        self:_RemoveShortCusts()
        -- END MODIFICATION
    end
end

local DFHD_SUPERRES_VALUE = {
    None = 0,
    TSR = 1,
    FSR2 = 2,
    FSR3 = 3,
    DLSS = 4,
    XESS = 5
}

function SystemSettingHDVideoPanel:_OnSuperResolutionMethodChanged(method)
    if method == DFHD_SUPERRES_VALUE.DLSS then
        self._wtItemSuperResolutionQualityDLSS:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        self._wtItemSuperResolutionQualityDLSS:SetVisibility(ESlateVisibility.Collapsed)
    end
    if method == DFHD_SUPERRES_VALUE.TSR then
        self._wtItemSuperResolutionQualityTSR:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        self._wtItemSuperResolutionQualityTSR:SetVisibility(ESlateVisibility.Collapsed)
    end
    if method == DFHD_SUPERRES_VALUE.FSR2 then
        self._wtItemSuperResolutionQualityFSR2:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        self._wtItemSuperResolutionQualityFSR2:SetVisibility(ESlateVisibility.Collapsed)
    end
    if method == DFHD_SUPERRES_VALUE.FSR3 then
        self._wtItemSuperResolutionQualityFSR3:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        self._wtItemSuperResolutionQualityFSR3:SetVisibility(ESlateVisibility.Collapsed)
    end
    if method == DFHD_SUPERRES_VALUE.XESS then
        self._wtItemSuperResolutionQualityXESS:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        self._wtItemSuperResolutionQualityXESS:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function SystemSettingHDVideoPanel:_InitSuperResolutionQualityItem()
    local method = CommonSettingLogicHD.GetDataByID(self._wtItemSuperResolutionMethod.ID)
    self:_OnSuperResolutionMethodChanged(method)
end

function SystemSettingHDVideoPanel:_OnUndo()
    CommonSettingLogicHD.ClearPendingSettings()
    self:_InitSuperResolutionQualityItem()
    VRamSettingLogicHD.ResetVRamUsage()
end

function SystemSettingHDVideoPanel:_InnerApply()
    CommonSettingLogicHD.ApplyPendingSettings()
    -- refresh
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.Shadow.ScrollingCSMC.ForceGlobalRefresh")
    -- GC
    UClientVideoSettingHD.GCAfterSeveralFrames(GetGameInstance(), 2)

    VideoSettingLogicHD.RecordRealViewportResolution(true)
    -- VRamSettingLogicHD.ApplyVRamUsage()
    VRamSettingLogicHD.InitVRamUsage()
end

function SystemSettingHDVideoPanel:_OnApply()
    -- 检查是否修改显示模式设置，并记录
    VideoSettingLogicHD.StartConfirmDisplayModes()

    VideoSettingLogicHD.ApplyPendingSettings()

    VideoSettingLogicHD.EndConfirmDisplayModes()
end

function SystemSettingHDVideoPanel:OnOpen()
    local systemInfo = UVideoSettingHelper.GetSystemInfoHD()
    -- BEGIN MODIFICATION @ VIRTUOS: Console平台不显示
    if not systemInfo.bIntegratedGPU and not IsConsole() then
        self._vramPanel = Facade.UIManager:AddSubUI(self, UIName2ID.SystemSettingHDVRamPanel, self._wtVRamRoot, nil)
    end
    -- END MODIFICATION

    self:_BindBtnEvent()

    if UClientSettingHelperHD.IsEditor() then
        if not VideoSettingLogicHD.IsRayTracingSupported() then
            self._wtRayTracingPanel:Collapsed()
        end
    else
        if (not VideoSettingLogicHD.IsRayTracingSupported()) or (not VideoSettingLogicHD.IsInRayTracingWhitelist()) then
            self._wtRayTracingPanel:Collapsed()
        end
    end

    if (not VideoSettingLogicHD.IsLowMemoryModeSupported()) then
        self._wtItemLowMemoryMode:Collapsed()
    end


    local countToContinue = CommonSettingLogicHD.RefreshItemUIBackground(self._wtItemPanel)
    CommonSettingLogicHD.RefreshItemUIBackground(self._wtRayTracingPanel, countToContinue)

    if VideoSettingLogicHD.ScopeUseRTSupported() then
        self._wtItemScopeUseRT:SelfHitTestInvisible()
    else
        self._wtItemScopeUseRT:Collapsed()
    end
end

function SystemSettingHDVideoPanel:OnShow()
    local function OnCancel()
        Facade.UIManager:PopStackUI()
    end
    local function CheckVideoSettingBeforeBack()
        if CommonSettingLogicHD.AnyPendingSetting() then
            local OnConfirm = function()
                CommonSettingLogicHD.ApplyPendingSettings()
                -- GC
                UClientVideoSettingHD.GCAfterSeveralFrames(GetGameInstance(), 2)
                Facade.UIManager:PopStackUI()
            end
            VideoSettingLogicHD.ConfirmVideoSettingsBeforeLeave(OnConfirm, OnCancel, self)
        else
            OnCancel()
        end
    end
    if Module.SystemSetting.Field:GetCurrentTabTypeHD() ==
        Module.SystemSetting.Config.ESystemSettingHDPanel.VideoSetting then
        Module.SystemSetting.Field:SetDescRootPanelHD(self._wtDescRootPanel)
        Module.SystemSetting.Field:SetCurrentSettingPanelHD(self)
    end
    Module.CommonBar:BindBackHandler(CheckVideoSettingBeforeBack, self)

    -- BEGIN MODIFICATION @ VIRTUOS
    if Module.SystemSetting.Field:GetCurrentTabTypeHD() ==
    Module.SystemSetting.Config.ESystemSettingHDPanel.VideoSetting then
        if not IsConsole() then
	        local list = {
	            {actionName = "Setting_Confirm_Gamepad", func = nil, caller = nil, bUIOnly = true},
	            {
	                actionName = "Reset",
	                func = self._OnReset,
	                caller = self
	            }, {
	                actionName = "RecompilePSO",
	                func = self._OnRecompilePSO,
	                caller = self
	        }}
	        local globalList = Module.SystemSetting.Field:GetGlobalSummaryList()
	        for _, v in ipairs(globalList) do
	            table.insert(list, v)
	        end
	        Module.CommonBar:SetBottomBarTempInputSummaryList(list, false)
        else
            -- Console平台屏蔽“重置” 和 “重新预热着色器”
            Module.CommonBar:SetBottomBarTempInputSummaryList({
                {actionName = "Setting_Confirm_Gamepad", func = nil, caller = nil, bUIOnly = true}
            }, false)
        end

        self:_RegisterNavGroup()
    end
    -- END MODIFICATION
end

function SystemSettingHDVideoPanel:OnHideBegin()
    Module.CommonBar:RecoverBottomBarInputSummaryList()
    -- BEGIN MODIFICATION @ VIRTUOS : UI Navigation
    self:_RemoveNavGroup()
    self:_RemoveShortCusts()
    -- END MODIFICATION
end

function SystemSettingHDVideoPanel:OnHide()
    Module.CommonBar:BindBackHandler(nil, nil)
    CommonSettingLogicHD.RemoveDesc()
    Module.SystemSetting.Field:SetDescRootPanelHD(nil)
    Module.SystemSetting.Field:SetCurrentSettingPanelHD(nil)
end

function SystemSettingHDVideoPanel:_OnReset()
    local fReset = function()
        SettingLogicHD.ResetCurrentSettings()
        SettingLogicHD.ApplyRecommandGraphicsQulitySetting(false)
        self:_InitSuperResolutionQualityItem()
    end
    local resetInputTxt = Module.SystemSetting.Config.Loc.HDSetting.ResetVideoTxt
    local cancelTxt = Module.SystemSetting.Config.Loc.HDEntrance.cancel
    local confirmTxt = Module.SystemSetting.Config.Loc.HDSetting.ResetTxt
    Module.CommonTips:ShowConfirmWindow(resetInputTxt, CreateCallBack(fReset, self), nil, cancelTxt, confirmTxt)
end

function SystemSettingHDVideoPanel:_OnRecompilePSO()
    local function _doRecompile()
        local ItemPSO = {
            section = "FirstLaunchSetting",
            key = "PSODone",
            ini = "UserSystemSettingHD"
        }
        local default_build_version = "0.0.0.0"
        UGPGConfigUtils.SetString(ItemPSO.section, ItemPSO.key, default_build_version, ItemPSO.ini)
        -- local GConfig = FConfigCacheIni.GetGlobalConfig()
        -- local SettingIni = FConfigCacheIni.LoadGloablIni(ItemPSO.ini)
        -- GConfig:Flush(false, SettingIni)
    end
    local contentTxt = Module.SystemSetting.Config.Loc.HDSetting.RecompilePSOTxt
    local confirmTxt = Module.SystemSetting.Config.Loc.HDSetting.RecompilePSOConfirmTxt

    Module.CommonTips:ShowConfirmWindowWithSingleBtn(contentTxt, CreateCallBack(_doRecompile, self), confirmTxt)
end

function SystemSettingHDVideoPanel:OnClose()
    Facade.UIManager:RemoveSubUIByParent(self, self._wtVRamRoot)
    Facade.UIManager:ClearSubUIByParent(self, self._wtVRamRoot)
    -- BEGIN MODIFICATION @ VIRTUOS : 因为关闭亮度弹窗后，虽然会走ShowBegin，但是页签还是第一页的gameplay,GetCurrentTabTypeHD那里的判断过不了
    self:RemoveLuaEvent(Module.SystemSetting.Config.Event.evtSettingBrightnessHideHD)
    -- END MODIFICATION
end

function SystemSettingHDVideoPanel:_OnRayTracingStateChanged(bEnable)
    if bEnable then
        local tipText = Module.SystemSetting.Config.Loc.HDSetting.RayTracingRebootTxt
        local confirmText = Module.SystemSetting.Config.Loc.HDSetting.RayTracingRebootConfirmTxt
        Module.CommonTips:ShowConfirmWindowWithSingleBtn(tipText, nil, confirmText)
    end
end

function SystemSettingHDVideoPanel:_OnScopeUseRTStateChanged(bEnable)
    if bEnable then
        local tipText = Module.SystemSetting.Config.Loc.HDSetting.ScopeUseRTTxt
        local confirmText = Module.SystemSetting.Config.Loc.HDSetting.ScopeUseRTConfirmTxt
        local cancelTex = Module.SystemSetting.Config.Loc.HDSetting.ScopeUseRTCancelTxt
        local onCancel = function()
            CommonSettingLogicHD.SetDataByID("bScopeUseRT", false)
            local curItems = Module.SystemSetting.Field:GetAllSettingItemHD()
            local item = curItems["bScopeUseRT"]
            if not hasdestroy(item) then
                item:ReloadSetting()
            end
        end
        Module.CommonTips:ShowConfirmWindowWithNotice(tipText, nil, nil, onCancel, cancelTex, confirmText)
    end
end

function SystemSettingHDVideoPanel:_OnFSR3FrameGenerationChanged(bEnable)
    if bEnable then
        local tipText = Module.SystemSetting.Config.Loc.HDSetting.GenericRebootTxt
        local confirmText = Module.SystemSetting.Config.Loc.HDSetting.GenericRebootConfirmTxt
        Module.CommonTips:ShowConfirmWindowWithSingleBtn(tipText, nil, confirmText)
    end
end

-- BEGIN MODIFICATION @ VIRTUOS : UI Navigation
function SystemSettingHDVideoPanel:_RegisterNavGroup()
    local wtScrollBox = self:Wnd("ScrollBox_206", UIWidgetBase)
    if not self._NavGroup then
        self._NavGroup = WidgetUtil.RegisterNavigationGroup(wtScrollBox, self, "Grid1D")
    end

    if self._NavGroup then
        if not IsConsole() then
            self._NavGroup:AddNavWidgetToArray(wtScrollBox)
        else
            -- 给Console显示的控件单独配合导航
            local NavWidgetTable = {}
            self:_AddSelfWidgetToNavWidgetByName("_wtItemBrightness_jump", NavWidgetTable)
            self:_AddSelfWidgetToNavWidgetByName("WBP_SetUpComponent_VSync", NavWidgetTable)
            self:_AddSelfWidgetToNavWidgetByName("_wtItemPerfMode_Console", NavWidgetTable)
    
            self:_AddSliderWidgetToNavWidgetByName("WBP_SetUpComponent_SplitBtn", NavWidgetTable)

            for index, value in ipairs(NavWidgetTable) do
                self._NavGroup:AddNavWidgetToArray(NavWidgetTable[index])
            end
        end

        self._NavGroup:SetScrollRecipient(wtScrollBox)
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup)
    end
end

function SystemSettingHDVideoPanel:_RemoveNavGroup()
    if self._NavGroup then
        self._NavGroup = nil
    end
    WidgetUtil.RemoveNavigationGroup(self)
end

function SystemSettingHDVideoPanel:_InitBtnDisplayAction()
    if self._wtBtnApply then
        self._wtBtnApply:SetDisplayInputAction("Common_ButtonLeft", true, nil, true)
    end

    if self._wtBtnUndo then
        self._wtBtnUndo:SetDisplayInputAction("Common_ButtonTop", true, nil, true)
    end

end

function SystemSettingHDVideoPanel:_InitShortCuts()
    if not self._shortCut_Apply then
        self._shortCut_Apply = self:AddInputActionBinding("Common_ButtonLeft", EInputEvent.IE_Pressed, self._OnApply,
            self, EDisplayInputActionPriority.UI_Stack)
    end

    if not self._shortCut_Undo then
        self._shortCut_Undo = self:AddInputActionBinding("Common_ButtonTop", EInputEvent.IE_Pressed, self._OnUndo, self,
            EDisplayInputActionPriority.UI_Stack)
    end
end

function SystemSettingHDVideoPanel:_RemoveShortCusts()
    if self._shortCut_Apply then
        self:RemoveInputActionBinding(self._shortCut_Apply)
        self._shortCut_Apply = nil
    end

    if self._shortCut_Undo then
        self:RemoveInputActionBinding(self._shortCut_Undo)
        self._shortCut_Undo = nil
    end
end

-- 根据名字查找控件并加入导航组
function SystemSettingHDVideoPanel:_AddSelfWidgetToNavWidgetByName(WidgetName, NavWidgetTable)
    if WidgetName then
        local targetWidget = self:Wnd(WidgetName, UIWidgetBase)

        if targetWidget then
            table.insert(NavWidgetTable, targetWidget)
        end
    end
end

-- 根据SplitBtn控件的名字，查找Slider控件并加入导航组
function SystemSettingHDVideoPanel:_AddSliderWidgetToNavWidgetByName(WidgetName, NavWidgetTable)
    if WidgetName then
        local targetWidget = self:Wnd(WidgetName, UIWidgetBase)

        if targetWidget then
            local Slider = targetWidget:Wnd("Slider", UIWidgetBase)
            if Slider then
                local subSlider = Slider:Wnd("Slider_167", UIWidgetBase)
                if subSlider then
                    table.insert(NavWidgetTable, subSlider)
                end
            end
        end
    end
end
-- END MODIFICATION

return SystemSettingHDVideoPanel
