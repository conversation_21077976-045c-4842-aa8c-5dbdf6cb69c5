----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMQuest)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class QuestSeasonalTutorial : LuaUIBaseView

local UKismetInputLibrary = import "KismetInputLibrary"
local UGPInputHelper = import "GPInputHelper"
local CommonPopWindows = require "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindows"
local HDKeyIconBox     = require "DFM.Business.Module.CommonWidgetModule.UI.HD.HDKeyIconBox"

local QuestSeasonalTutorial = ui("QuestSeasonalTutorial")
function QuestSeasonalTutorial:Ctor()
    
    self._wtPopWindow = self:Wnd("WBP_CommonPopWindowsV2", CommonPopWindows)
    local fCallbackIns = CreateCallBack(self._OnPopClose, self)
    self._wtPopWindow:BindCloseCallBack(fCallbackIns)

    self._wtNameSlot = self:Wnd("DFNamedSlot_102", UIWidgetBase)
    self._wtPageScroll = self:Wnd("DFScrollBox_261", UIScrollBox)

    self._wtTitle = self:Wnd("DFTextBlock_51", UITextBlock)
    self._wtDescription = self:Wnd("DFRichTextBlock_48", UITextBlock)

    self._wtBtnLeft = self:Wnd("DFButton_Left", UIButton)
    self._wtBtnLeft:Event("OnClicked", self._TurnPageLeft, self)
    self._wtBtnRight = self:Wnd("DFButton_Right", UIButton)
    self._wtBtnRight:Event("OnClicked", self._TurnPageRight, self)
    self._wtKeyIcon = self:Wnd("CarouselKeyIcon", HDKeyIconBox)

    self.type = 0
    self._subItemList = {}
    self._dotList = {}

    self._curIndex = 1
    self._totalPage = 0
    self._bShouldShowScroll = true

    self._callback = nil

    if IsHD() then
        self:InjectLua()
    end
end

------ Override Functions -----------

function QuestSeasonalTutorial:OnInitExtraData(type, callback)
    self.type = type
    self._callback = callback
    self._subItemList = Module.Quest.Field:GetQuestSeasonGuideByType(type)
end

function QuestSeasonalTutorial:OnShowBegin()
    local gameInst = GetGameInstance()
    self._downHandle = UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonDownEvent:Add(self.OnHandleMouseButtonDownEvent, self)
    self._upHandle = UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonUpEvent:Add(self.OnHandleMouseButtonUpEvent, self)
end

function QuestSeasonalTutorial:OnShow()

    if self._subItemList == nil or #self._subItemList == 0 then
        logerror("Invalid Seasonal Quest Tutorial subBpList")
        return
    end

    for  index, value in ipairs(self._subItemList) do
        local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.DFButtonCarousel2, self._wtPageScroll)
        table.insert(self._dotList, weakUIIns)
        local dotWidget = getfromweak(weakUIIns)
        if dotWidget then
            dotWidget:Event("OnClicked", self._OnDotClicked, self, index)
        end

    end

    if self._subItemList then
        self._totalPage = #self._subItemList
        self._bShouldShowScroll = self._totalPage ~= 1
    end

    self._wtPopWindow:SetTitle(Module.Quest.Config.QuestSeasonTutorialTitleLoc[self.type])

    self:_displayScroll(self._bShouldShowScroll)
    self:_UpdateContent()

    if IsHD() then
        self:_InitShortCut()
    end
end

function QuestSeasonalTutorial:OnHide()
    Facade.UIManager:ClearSubUIByParent(self, self._wtPageScroll)
    self:RemoveAllLuaEvent()
    if IsHD() then
        self:_RemoveShortCut()
    end

    if self._callback then
        self._callback()
    end
end

function QuestSeasonalTutorial:OnNavBack()
    self:_OnPopClose()
    return true
end

------

function QuestSeasonalTutorial:OnHandleMouseButtonDownEvent(inGestureEvent)
    if not IsHD() and UGPInputHelper.IsTouchOrLMBEvent(inGestureEvent) then
        if inGestureEvent and inGestureEvent.GetScreenSpacePosition then
            self.startPos = inGestureEvent:GetScreenSpacePosition()
        end
    end
end

function QuestSeasonalTutorial:OnHandleMouseButtonUpEvent(inGestureEvent)
    if not IsHD() then
        if inGestureEvent and inGestureEvent.GetScreenSpacePosition and self.startPos and self.startPos ~= FVector2D(0,0) then
            local screenDelta = inGestureEvent:GetScreenSpacePosition() - self.startPos
                if math.abs(screenDelta.X) > math.abs(screenDelta.Y) and math.abs(screenDelta.X) > 50 then --向右滑动
                    if screenDelta.X > 0 then
                        self:_TurnPageLeft()
                    else --向左滑动
                        self:_TurnPageRight()
                    end
                end
            self.startPos = FVector2D(0,0)
        end
    end
end


function QuestSeasonalTutorial:_OnPopClose()
    if self._scrollHandle then
        self:RemoveInputActionBinding(self._scrollHandle)
        self._scrollHandle = nil
    end
    Facade.UIManager:CloseUI(self)
end

function QuestSeasonalTutorial:_TurnPageLeft()
    
    if self._curIndex == 1 then
        self._curIndex = self._totalPage
    else
        self._curIndex = self._curIndex - 1
    end
    self:_UpdateContent()
end

function QuestSeasonalTutorial:_TurnPageRight()

    if self._curIndex == self._totalPage then
        self._curIndex = 1
    else
        self._curIndex = self._curIndex + 1
    end
    self:_UpdateContent()
end

function QuestSeasonalTutorial:_OnDotClicked(index)
    self._curIndex = index
    self:_UpdateContent()
end

function QuestSeasonalTutorial:_UpdateContent()

    if self._subItemList == nil or #self._subItemList == 0 then
        logerror("Invalid subItemList")
        return
    end

    self._wtNameSlot:ClearChildren()

    -- Load Blueprint
    local bpPath = self._subItemList[self._curIndex].bpPath

    if hasdestroy(bpPath) or not bpPath.AssetPathName then
        logwarning("CreateUI failed, widgetSoftObjectPath is invalid", bpPath)
        return
    end

    local widgetClassPath = bpPath.AssetPathName .. "_C"

    local onLoadFinished = function(mapPath2ResIns)
        loginfo("Asset Load Finished", widgetClassPath)

        local wbpClassRes = mapPath2ResIns[widgetClassPath]
        if hasdestroy(wbpClassRes) then
            logwarning("CreateUI failed, widgetSoftObjectPath is invalid", widgetSoftObjectPath)
            return
        end

        if hasdestroy(self.__cppinst) then
            logwarning("CreateUI failed, ownerIns has been destroyed")
            return
        end

        local userWidget = UWidgetBlueprintLibrary.Create(self.__cppinst, wbpClassRes, nil)
        self._wtNameSlot:AddChild(userWidget)
    end

    Facade.ResourceManager:AsyncLoadResource(
        nil,
        widgetClassPath,
        onLoadFinished,
        nil
    )

    self._wtTitle:SetText(self._subItemList[self._curIndex].Title)
    self._wtDescription:SetText(self._subItemList[self._curIndex].Text)

    if self._bShouldShowScroll then
        self:_UpdateDot()
    end
end

function QuestSeasonalTutorial:_UpdateDot()

    for  index, value in ipairs(self._dotList) do
        local dotWidget = getfromweak(value)
        if dotWidget then
            if index == self._curIndex then
                dotWidget:SetIsEnabledStyle(false)
            else
                dotWidget:SetIsEnabledStyle(true)
            end
        end       
    end
end

function QuestSeasonalTutorial:_displayScroll(bShouldDisplay)
    if bShouldDisplay then
        self._wtPageScroll:Visible()
        self._wtBtnLeft:Visible()
        self._wtBtnRight:Visible()
        self._wtKeyIcon:Visible()
    else
        self._wtPageScroll:Collapsed()
        self._wtBtnLeft:Collapsed()
        self._wtBtnRight:Collapsed()
        self._wtKeyIcon:Collapsed()
    end
end

function QuestSeasonalTutorial:Imp_OnMouseWheel(inGeometry, inGestureEvent)
    
    if not self._bShouldShowScroll then
        return
    end

    local delta = UKismetInputLibrary.PointerEvent_GetWheelDelta(inGestureEvent)
    if delta > 0 then
        self:_TurnPageLeft()
    else
        self:_TurnPageRight()
    end
end

function QuestSeasonalTutorial:_SetScrollOffsetX(inValue)
    
    if self._delayHandle ~= nil then
        return
    end

    if inValue > 0 then
        self:_TurnPageRight()
        self._delayHandle = Timer.DelayCall(0.3,self._ClearTimerHandle, self)      
    elseif inValue < 0 then
        self:_TurnPageLeft()
        self._delayHandle = Timer.DelayCall(0.3,self._ClearTimerHandle, self)      
    end
end

function QuestSeasonalTutorial:_ClearTimerHandle()
    self._delayHandle = nil
end

function QuestSeasonalTutorial:_InitShortCut()
    if self._ScrollAction == nil then 
        self._ScrollAction = self:AddAxisInputActionBinding("Common_Right_X", self._SetScrollOffsetX, self, EDisplayInputActionPriority.UI_Pop)
        self._wtKeyIcon:InitKeyIcon("Common_Right_X")
    end
end

function QuestSeasonalTutorial:_RemoveShortCut()
    if self._ScrollAction then
        self:RemoveInputActionBinding(self._ScrollAction)
        self._ScrollAction= nil
    end
end

return QuestSeasonalTutorial