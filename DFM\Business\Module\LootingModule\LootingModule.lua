----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMLooting)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class LootingModule : ModuleBase
local LootingModule = class("LootingModule", require("DFM.YxFramework.Managers.Module.ModuleBase"))

local WeaponHelperTool = require"DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local LootingConfig = require "DFM.Business.Module.LootingModule.LootingConfig"
local LootingLogic = require "DFM.Business.Module.LootingModule.LootingLogic"
local LootingEvtLogic = require "DFM.Business.Module.LootingModule.LootingEvtLogic"
local LootingOperaLogic = require "DFM.Business.Module.LootingModule.LootingOperaLogic"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local LootingVehicleLogic = require "DFM.Business.Module.LootingModule.LootingVehicleLogic"
local LootingLoadLogic = require "DFM.Business.Module.LootingModule.Logic.LootingLoadLogic"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local LootingSearchLogic = require "DFM.Business.Module.LootingModule.LootingSearchLogic"
local LootingMarkItemLogic = require "DFM.Business.Module.LootingModule.LootingMarkItemLogic"
local LootingInputLogic = require "DFM.Business.Module.LootingModule.Logic.LootingInputLogic"
local LootingAudioLogic = require "DFM.Business.Module.LootingModule.LootingAudioLogic"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local ItemBaseTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemBaseTool"
local UItemIDUtil = import "ItemIDUtil"
local ULootUtil = import "LootUtil"
local EGameHUDState = import "GameHUDSate"

local function log(...)
    loginfo("[LootingModule]", ...)
end

function LootingModule:Ctor()
    self._bEnabled = false

    -- ULuaSubsystem.Get().OnLuaClientSeamlessTravelEnd:Add(self.PreCreateUIs, self)
end

function LootingModule:Destroy()
    ---@type LootingField
    local field = self.Field
    local waitTimer = field:GetPickupPanelWaitTimer()
    if waitTimer then
        waitTimer:Release()
        field:SetPickupPanelWaitTimer(nil)
    end
    Facade.ProtoManager:RemoveAllNtfListenerByCaller(self)
    -- ULuaSubsystem.Get().OnLuaClientSeamlessTravelEnd:Remove(self.PreCreateUIs, self)
end

function LootingModule:OnInitModule()
    local curGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    if curGameFlow == EGameFlowStageType.Game and not self._bEnabled then
        self:OnGameFlowChangeEnter(curGameFlow)
    end
    Facade.ProtoManager:AddNtfListener("CSSettlementEmptyNtf", self._OnReceiveSettlementNtf, self)
end

---@param gameFlowType EGameFlowStageType
function LootingModule:OnGameFlowChangeEnter(gameFlowType)
    if gameFlowType == EGameFlowStageType.Game then
        self._bEnabled = true
        LootingEvtLogic.BindLuaEvents()
        -- loginfo("[georgecgong12] OnGameFlowChangeEnter()")
        -- azhengzheng:重连拉一下数据 暂时这么处理
        LootingEvtLogic._InitWishItemInfos()
        if ItemOperaTool.IsInTestMap() then
            if not self:GetMainPanel() then
                Facade.LuaFramingManager:RegisterFrameTask(self.PreCreateUIs, self)
            end
        end
    end
end

function LootingModule:OnGameFlowChangeLeave(gameFlowType)
    if gameFlowType == EGameFlowStageType.Game or gameFlowType == EGameFlowStageType.SafeHouse then
        self._bEnabled = false
        LootingEvtLogic.ResetField()
        LootingEvtLogic.UnBindLuaEvents()
    end
end

function LootingModule:OnDestroyModule()
    LootingEvtLogic.ResetField()
    LootingEvtLogic.UnBindLuaEvents()

end

---------------------------------------------------------------------------------
--- Loading 生命周期
--- 设置bAutoLoading = true则下列生命周期有效
---------------------------------------------------------------------------------
---@overload fun(ModuleBase, OnLoadingLogin2Frontend)
function LootingModule:OnLoadingLogin2Frontend(gameFlowType)
    LootingLoadLogic.OnLoadingLogin2FrontendProcess(gameFlowType)
end

---@overload fun(ModuleBase, OnLoadingGame2Frontend)
function LootingModule:OnLoadingGame2Frontend(gameFlowType)
    LootingLoadLogic.OnLoadingGame2FrontendProcess(gameFlowType)
end

---@overload fun(ModuleBase, OnLoadingFrontend2Game)
function LootingModule:OnLoadingFrontend2Game(gameFlowType)
    LootingLoadLogic.OnLoadingFrontend2GameProcess(gameFlowType)
end


-----------------------------------------------------------------------
--region Public API
function LootingModule:OnToggleLootPanelAction()
    LootingLogic._OnToggleLootPanelAction(LootingConfig.ESOLSelectPanel.Bag)
end

function LootingModule:OnToggleLootPanelHealthTab()
    LootingLogic._OnToggleLootPanelAction(LootingConfig.ESOLSelectPanel.Health)
end

function LootingModule:OnToggleLootPanelScoreTab()
    LootingLogic._OnToggleLootPanelAction(LootingConfig.ESOLSelectPanel.Score)
end

function LootingModule:OnOpenHealthPanel()
    LootingLogic._OnOpenHealthPanel()
end

function LootingModule:PreCreateUIs(task)
    LootingLogic.PreCreateUIs(task)
end

function LootingModule:OpenMainPanel()
    LootingLogic.OpenBagView()
end

function LootingModule:OpenHealthView()
    LootingLogic.OpenHealthView()
end

function LootingModule:OpenScoreBoardView()
    LootingLogic.OpenScoreBoardView()
end

function LootingModule:StopAllLoots()
    -- LootingLogic.CloseMainPanel()

    -- 如果此时正在标记选择阶段，会退出
    if LootingMarkItemLogic.IsInMarkSelectionMode() then
        LootingMarkItemLogic.LeaveMarkSelectionMode()
    end

    -- -- 关闭HoverTip
    -- if IsHD() then
    --     LootingLogic.CloseCommonHoverTipsHD()
    -- end

    -- 停止搜索过程
    local lootUIState = self.Field:GetCurrentLootUIState()
    if lootUIState == LootingConfig.ELootUIState.LootSearch then
        LootingSearchLogic.CancelFirstStageSearch()
    elseif lootUIState == LootingConfig.ELootUIState.LootPanel then
        Module.CommonWidget:CancelDragDrop()
        LootingSearchLogic.StopSecondStageSearch()
    elseif lootUIState == LootingConfig.ELootUIState.LootBag then
        Module.CommonWidget:CancelDragDrop()
        LootingLogic.InternalCloseMainPanel()
    end

    LootingLogic.CloseDetailPanel(true)
end

function LootingModule:SwitchFromLoot2BagView()
    local lootState = self.Field:GetCurrentLootUIState()
    if lootState ~= LootingConfig.ELootUIState.LootPanel then
        return false
    end

    LootingSearchLogic.StopSecondStageSearch(false) -- Keep panel open

    self.Field:SetCurrentLootUIState(LootingConfig.ELootUIState.LootBag)
    Server.LootingServer:SetCurrentSelectorData()
    if IsHD() then
        ---@type LootingMainView_HD
        local mainPanel = self.Field:GetMainPanel()
        mainPanel:InitPanel()
    else
        ---@type MobileBagPanel
        local mainPanel = self.Field:GetMainPanel()
        mainPanel:SwitchFromLoot2BagView()
    end
    Server.LootingServer:ServerNotifySwitchFromLoot2BagView()
end

function LootingModule:OnGamePickUpDestroy(containerGID)
    local currentInteractor = Server.LootingServer:GetCurrentInteractingBox()
    if isvalid(currentInteractor) and currentInteractor.InventoryGID == containerGID then
        log("Trigger SwitchFromLoot2BagView for containerGID=", containerGID)
        Module.Looting:SwitchFromLoot2BagView()
    elseif not isvalid(currentInteractor) then
        local interactorType = Server.LootingServer:GetCurrentInteractorType()
        if interactorType == EGamePickupType.SceneBox or interactorType == EGamePickupType.NearbyDeadBody
                or interactorType == EGamePickupType.DropContainer then
            log("Trigger SwitchFromLoot2BagView because currentInteractor not valid")
            Module.Looting:SwitchFromLoot2BagView()
        end
    end
end

function LootingModule:OpenPickupPanel(pickupType, interactors)
    LootingLogic.OpenPickupPanel(pickupType, interactors)
end


function LootingModule:OpenDetailPanel(refWidget)
    LootingLogic.OpenDetailPanel(refWidget)
end

function LootingModule:SetAutoLoot(bAutoLoot)
    Server.LootingServer:SetAutoSortSpace(bAutoLoot)
end

function LootingModule:GetAutoLoot()
    return Server.LootingServer:GetAutoSortSpace()
end

function LootingModule:AutoProcessItem(item)
    return LootingOperaLogic.AutoProcessItem(item)
end

function LootingModule:DoDiscardItem(item)
    return LootingLogic.DiscardOperation(item)
end

function LootingModule:DoReqUseItem(item)
    return LootingOperaLogic.DoUseItem(item)
end

function LootingModule:DoReqUseItemById(itemId)
    return LootingOperaLogic.DoUseItemById(itemId)
end

function LootingModule:DoLoadingAmmo(ammoItem, weaponItem, useItemSource)
    return LootingOperaLogic.DoLoadingAmmo(ammoItem, weaponItem, useItemSource)
end

function LootingModule:DoCancelUseItem()
    return LootingOperaLogic.DoCancelUseItem()
end

function LootingModule:DoEquipWeaponItemById(itemId, toSlotType)
    return LootingLogic.ReqEquipWeaponItemById(itemId, toSlotType)
end

function LootingModule:DoEquipWeaponItemByGid(itemGid)
    return LootingLogic.ReqEquipWeaponItemByGid(itemGid)
end

function LootingModule:DoReqUnEquipAmmoByGunGid(gunGid)
    return LootingLogic.ReqUnEquipAmmoByGunGid(gunGid)
end

function LootingModule:DoQuickLootItem(itemData, slotGroup, bRefreshView, bCheckEquipSlot, bShowTip)
    return LootingLogic.QuickLootItem(itemData, slotGroup, bRefreshView, bCheckEquipSlot, bShowTip)
end

function LootingModule:DoEquipItem(item, bAllowReplace)
    LootingOperaLogic.DoEquipItem(item, bAllowReplace)
end

function LootingModule:DoUnEquipItem(item)
    LootingOperaLogic.DoUnEquipItem(item)
end

function LootingModule:DoCarryItem(item)
    LootingOperaLogic.DoCarryItem(item)
end

function LootingModule:DoSplitItem(item, splitNum)
    return LootingOperaLogic.DoSplitItem(item, splitNum)
end

function LootingModule:DoReloadWeapon(weaponGid, itemId, itemGid)
    LootingLogic.ReloadWeapon(weaponGid, itemId, itemGid)
end

function LootingModule:DoSelectItem(selectedView, bForceSelected, bFromDoubleClick)
    LootingInputLogic.SetIVSelected(selectedView, bForceSelected, bFromDoubleClick)
end

function LootingModule:CommonBuildDragDropInfoInLooting(item, itemPreview)
    return LootingLogic.CommonBuildDragDropInfoInLooting(item, itemPreview)
end

function LootingModule:GetSafeBoxPreferMarkItem()
    return self.Field:GetSafeBoxPreferMarkItem()
end

function LootingModule:SetSafeBoxPreferMarkItem(bSafeBoxPreferMarkItem)
    self.Field:SetSafeBoxPreferMarkItem(bSafeBoxPreferMarkItem)
end

function LootingModule:SetSafeBoxPreferHighValueItem(bSafeBoxPreferHighValueItem)
    self.Field:SetSafeBoxPreferHighValueItem(bSafeBoxPreferHighValueItem)
end

function LootingModule:GetSafeBoxPreferHighValueItem()
    return self.Field:GetSafeBoxPreferHighValueItem()
end

function LootingModule:SetSafeBoxPriceThreshold(safeBoxPriceThreshold)
    self.Field:SetSafeBoxPriceThreshold(safeBoxPriceThreshold)
end

function LootingModule:GetSafeBoxPriceThreshold()
    return self.Field:GetSafeBoxPriceThreshold()
end

function LootingModule:SaveLootConfig()
    self.Field:SaveLootConfig()
end

function LootingModule:NotifyEnterSettlement()
    self:StopAllLoots()

    LootingEvtLogic.UnBindLuaEvents()
end

function LootingModule:ShouldHideWhiteDotForThisBox(box)
    -- local curSearchBox = Module.Looting.Field:GetCurSearchingBox()
    -- if curSearchBox and curSearchBox == box then
    --     log("ShouldHideWhiteDotForThisBox", true)
    --     return true
    -- end

    -- return LootingLogic.IsLootSearchHudShow()
    return false
end

function LootingModule:ShouldShowInteractorView()
    -- log(self.Field:GetCurrentLootUIState())
    return self.Field:GetCurrentLootUIState() == LootingConfig.ELootUIState.None
end

function LootingModule:IsInLootingView()
    local lootState = self.Field:GetCurrentLootUIState()
    return lootState == LootingConfig.ELootUIState.LootPanel
end

function LootingModule:IsLootPanelOrBag()
    local lootState = self.Field:GetCurrentLootUIState()
    return lootState == LootingConfig.ELootUIState.LootPanel
    or lootState == LootingConfig.ELootUIState.LootBag
end

function LootingModule:IsOBLoot()
    return self.Field:GetCurrentLootUIState() == LootingConfig.ELootUIState.OBLoot
end

function LootingModule:GetAICharacterNameConfig(aiName)
    return LootingLogic.GetAICharacterNameConfig(aiName)
end

function LootingModule:IsOperatingVehicle()
    return LootingVehicleLogic.IsOperatingVehicle()
end

function LootingModule:SetFirstPickUpTeammateItem(bFirstPickUpTeammateItem)
    self.Field:SetFirstPickUpTeammateItem(bFirstPickUpTeammateItem)
end

function LootingModule:ResetFirstPickUpTeammateItem()
    if not self.Field:GetFirstPickUpTeammateItem() then
        self.Field:SetFirstPickUpTeammateItem(true)
    end
    loginfo("LootingModule:ResetFirstPickUpTeammateItem(", self.Field:GetFirstPickUpTeammateItem())
end

-- 是否首次拾取队友道具
---@return boolean
function LootingModule:GetFirstPickUpTeammateItem()
    return self.Field:GetFirstPickUpTeammateItem()
end

-- 道具是否能拾取（主要是看有没有足够的空间）
---@return boolean
function LootingModule:CheckItemCanPickup(pickup)
    if not isvalid(pickup) then
        return false
    end
    local itemInfo = pickup:GetPickupItemInfo()
    local id = UItemIDUtil.ToUint64(itemInfo.ItemId)
    if id <= 0 then
        return false
    end
    if not self.Field.pickupItem then
        self.Field.pickupItem = ItemBase:NewIns(id, itemInfo.ItemCount, itemInfo.ItemGid)
    else
        self.Field.pickupItem:Ctor(tonumber(itemInfo:GetIdName()), 1, itemInfo.ItemGid)
    end
    return LootingOperaLogic.CheckItemCanPickup(self.Field.pickupItem)
end

-- 散落的道具是否能直接装备
function LootingModule:CheckItemCanEquipDirectly(pickup)
    local itemInfo = pickup:GetPickupItemInfo()
    local id = UItemIDUtil.ToUint64(itemInfo.ItemId)
    local gid = itemInfo.ItemGid
    local num = itemInfo.ItemCount
    local item = ItemBase:New(id, num, gid)
    item:SetRawCppInfo(itemInfo)

    return Server.InventoryServer:GetEquipmentEmptySlot(item) ~= nil
end

-- 散落道具占的尺寸
function LootingModule:GetItemSize(pickup)
    local itemInfo = pickup:GetPickupItemInfo()
    local id = UItemIDUtil.ToUint64(itemInfo.ItemId)

    local itemData = ItemConfigTool.GetItemConfigById(id)
    local length = itemData.Length
    local width = itemData.Width
    return length,width
end

function LootingModule:ProcessGuideLogic(itemViews, bFromPickUp)
    local GuideConfig = Module.Guide.Config
    if bFromPickUp then
        Module.Guide:RemoveGuideWidgetProxy(GuideConfig.EGuideProxyWidget.guideProxyLootingDrag)
        Module.Guide:RemoveGuideWidgetProxy(GuideConfig.EGuideProxyWidget.guideProxyLootingDoubleClick)
    end
    Module.Guide:RemoveGuideWidgetProxy(GuideConfig.EGuideProxyWidget.guideProxyLootingMostValueItem)
    
    local mostValueItemUI, dragItem, doubleClickItem = nil, nil, nil
    local mostValue = 0
    if #itemViews > 0 then
        mostValueItemUI = itemViews[1]
    end
    local specGuideItemInfo = Module.Guide:GetNewPlayerGuideItemInfo(GuideConfig.NewPlayerGuideSpecItem.lootingItemList)
    for _, itemView in ipairs(itemViews) do
        local item = itemView.item
        local realPrice = item:GetSingleSellPrice()
        log("ProcessGuideLogic", item.name, realPrice)
        if realPrice > mostValue then
            mostValueItemUI = itemView
            mostValue = realPrice
        end
        if bFromPickUp then
            if specGuideItemInfo then
                if tostring(item.id) == specGuideItemInfo[1] then
                    Module.Guide:AddGuideWidgetProxy(GuideConfig.EGuideProxyWidget.guideProxyLootingDrag, itemView)
                elseif tostring(item.id) == specGuideItemInfo[2] then
                    Module.Guide:AddGuideWidgetProxy(GuideConfig.EGuideProxyWidget.guideProxyLootingDoubleClick, itemView)
                elseif tostring(item.id) == specGuideItemInfo[3] then
                    Module.Guide:AddGuideWidgetProxy(GuideConfig.EGuideProxyWidget.guideProxyLootingAI, itemView)
                end
            end
        end
    end
    Module.Guide:AddGuideWidgetProxy(GuideConfig.EGuideProxyWidget.guideProxyLootingMostValueItem, mostValueItemUI)
end

function LootingModule:NotifyItemDropPassively(itemid)
    local itemCfg = ItemConfigTool.GetItemConfigById(itemid)
    if itemCfg then
        local tip = string.format(LootingConfig.Loc.LootingDropItemSuccess, itemCfg.Name)
        Module.CommonTips:ShowSimpleTip(tip)
    end
end

function LootingModule:_OnReceiveSettlementNtf()
    self:ResetFirstPickUpTeammateItem()
    loginfo("LootingModule:_OnReceiveSettlementNtf", self.Field:GetFirstPickUpTeammateItem())
end

---@param item ItemBase
function LootingModule:GetCurrentInteractingSlot(item)
    return LootingOperaLogic.GetCurrentInteractingSlot(item)
end

---@param item ItemBase
function LootingModule:CheckItemMarkedByLooting(item)
    return LootingMarkItemLogic.CheckItemMarked(item.gid)
end

---@param item ItemBase
function LootingModule:ToggleItemMarkedByLooting(item)
    local gid = item.gid
    if LootingMarkItemLogic.CheckItemMarkedByPlayer(gid) then
        LootingMarkItemLogic.CancelMarkItem()
    else
        LootingMarkItemLogic.MarkItem(item)
    end
end

function LootingModule:CheckItemMarkable(item)
    return LootingMarkItemLogic.CheckItemMarkable(item)
end

function LootingModule:IsInMarkSelectionMode()
    return LootingMarkItemLogic.IsInMarkSelectionMode()
end

function LootingModule:PlayerSelectMarkItem(item)
    LootingMarkItemLogic.PlayerSelectMarkItem(item)
end

function LootingModule:Play_LootItemSearchStart()
    LootingAudioLogic.Play_LootItemSearchStart()
end

function LootingModule:Play_LootItemSearchStop()
    LootingAudioLogic.Play_LootItemSearchStop()
end

function LootingModule:Play_LootItemShow(quality)
    LootingAudioLogic.Play_LootItemShow(quality)
end

function LootingModule:GetMainPanel()
    if IsHD() then
        ---@type LootingMainView_HD
        local mainPanel = self.Field:GetMainPanel()
        return mainPanel
    else
        local mainPanel = self.Field:GetMainPanel()
        return mainPanel
    end
end

function LootingModule:GetItemAvailableCarrySlot(itemGidStr, slotGroup)
    local itemGid = ULuautils.GetUInt64StrToInt64(itemGidStr)
    local item = Server.InventoryServer:GetItemByGid(itemGid, slotGroup)
    local slotTypeList = {}
    if item then
        local _, allAvailableSlots = ItemOperaTool.GetItemAvaiableCarrySlot(item, slotGroup)
        for _, slot in ipairs(allAvailableSlots) do
            table.insert(slotTypeList, slot.SlotType)
        end
    end
    return table.concat(slotTypeList, " ")
end

function LootingModule:SetOnCloseSimpleBtnListPanelCallback(callback)
    self.Field.onCloseSimpleBtnListPanelCallback = callback
end

function LootingModule:CloseItemDetailForLootPanel()
    LootingLogic.CloseItemDetailForLootPanel()
end

function LootingModule:ScrollToItemSlot(slotType, slotGroup)
    self.Config.Events.evtScrollToItemSlot:Invoke(slotType, slotGroup)
end

function LootingModule:CheckDeadbodyShouldBehaveAsPlayer()
    if Server.LootingServer:GetCurrentInteractorType() == EGamePickupType.NearbyDeadBody then
        local interactor = Server.LootingServer:GetCurrentInteractingBox()
        if interactor and interactor.ShouldBehaveAsPlayer then
            return interactor:ShouldBehaveAsPlayer()
        end
    end
    return false
end

function LootingModule:CheckDeadbodyShouldHaveArchiveContainer()
    if Server.LootingServer:GetCurrentInteractorType() == EGamePickupType.NearbyDeadBody then
        local interactor = Server.LootingServer:GetCurrentInteractingBox()
        if interactor and interactor.ShouldHaveArchiveContainer then
            return interactor:ShouldHaveArchiveContainer()
        end
    end
    return false
end

function LootingModule:SetScrollConfig(acceleration, minSpeed, maxSpeed)
    if VersionUtil.IsShipping() then
        return
    end
    loginfo("LootingModule:SetScrollConfig", acceleration, minSpeed, maxSpeed)
    self.Config.SCROLL_ACCELERATION = acceleration
    self.Config.SCROLL_MIN_SPEED = minSpeed
    self.Config.SCROLL_MAX_SPEED = maxSpeed
    self.Config.Events.evtScrollConfigChanged:Invoke()
end

function LootingModule:OnInvMgrRepData()
    self.Config.Events.evtOnInvMgrRepData:Invoke()
end

function LootingModule:InitPickupDatas(pickupType, interactors)
    LootingSearchLogic.InitPickupDatas(pickupType, interactors)
end

function LootingModule:GetOpenBagTime()
    return self.Field.openBagTime
end

-- 设置 禁用搜索时跳转到指定道具的功能
function LootingModule:SetDisableScrollSearchedItemState(newState)
    self.Field.bDisableScrollSearchedItem = newState
    if newState then
        self:SetHasScrollToSearchedItem(true)
    end
end

function LootingModule:GetDisableScrollSearchedItem()
    return self.Field.bDisableScrollSearchedItem
end

function LootingModule:SetHasScrollToSearchedItem(bHasScrollToSearchedItem)
    self.Field.bHasScrollToSearchedItem = bHasScrollToSearchedItem
end

function LootingModule:HasScrollToSearchedItem()
    return self.Field.bHasScrollToSearchedItem
end

function LootingModule:HasPreCreateUI()
    return self.Field.bHasPreCreateUI
end

function LootingModule:CheckItemValid(itemGid)
    local item = Server.LootingServer:GetItemDataByGid(itemGid)
    if not item or not item.InSlot then
        return false
    end
    if not item.InSlot:HasItem(item) then
        return false
    end
    return true
end

function LootingModule:HasEnoughSpaceToAddAmmo(ItemID)
    for _, slotType in ipairs({ESlotType.ChestHangingContainer, ESlotType.Pocket, ESlotType.BagContainer, ESlotType.SafeBoxContainer}) do
        local slot = Server.InventoryServer:GetSlot(slotType)
        if slot then
            local item = ItemBase:NewIns(ItemID, 1)
            if slot:TryFindLocationForItem(item) then
                return true
            end
            for _, item in ipairs(slot:GetItems_RefConst()) do
                if ItemBaseTool.CheckItemCanCombine(item, item) then
                    return true
                end
            end
        end
    end
    return false
end

function LootingModule:InspectHighValueItem(item, bSpecial)
    bSpecial = setdefault(bSpecial, false)
    local lootUIState = self.Field:GetCurrentLootUIState()
    if item then
        if item.InSlot then
            if item.InSlot:GetSlotGroup() == Server.LootingServer:GetSelfSlotGroup() then
                if lootUIState ~= LootingConfig.ELootUIState.None then
                    Server.LootingServer.highValueItemGid = item.gid
                    Server.LootingServer.bSpecialInspect = bSpecial
                    self:StopAllLoots()
                    Server.LootingServer.highValueItemGid = 0
                    Server.LootingServer.bSpecialInspect = false
                else
                    loginfo("LootingModule:InspectHighValueItem blocked", item.name, item.gid, "lootUIState ==", lootUIState)
                end
            else
                loginfo("LootingModule:InspectHighValueItem blocked", item.name, item.gid, "item.InSlot:GetSlotGroup() ==", item.InSlot:GetSlotGroup())
            end
        else
            loginfo("LootingModule:InspectHighValueItem blocked", item.name, item.gid, "item.InSlot == nil")
        end
    else
        loginfo("LootingModule:InspectHighValueItem blocked item == nil")
    end
end

function LootingModule:OB_OnRep_InventoryRepData()
    log("LootingModule:OB_OnRep_InventoryRepData")
    if not Facade.GameFlowManager:IsInOBMode() then
        return
    end
    Module.Looting:OnInvMgrRepData()
end

function LootingModule:OB_OnRep_InventoryItemArray()
    log("LootingModule:OB_OnRep_InventoryItemArray")
    local obInvMgr = Facade.GameFlowManager:GetOBInvMgr()
    if isvalid(obInvMgr) and not obInvMgr.bIsInit then
        Server.LootingServer:IncrementalUpdateOBPlayerData()
    end
end

function LootingModule:OB_OnRep_PlayerUin()
    log("LootingModule:OB_OnRep_PlayerUin")
    if not Facade.GameFlowManager:IsInOBMode() then
        return
    end
    Module.Looting:StopAllLoots()
    Server.LootingServer:FullUpdateOBPlayerData(true)
    local pickupType = Server.LootingServer:GetCurrentOBPickupType()
    if pickupType then
        LootingLogic.OBOpenLootPanel()
    end
end

function LootingModule:OB_OnRep_LootingObj()
    log("LootingModule:OB_OnRep_LootingObj")
    local obInvMgr = Facade.GameFlowManager:GetOBInvMgr()
    if not isvalid(obInvMgr) then
        return
    end
    local gamePickupType = Server.LootingServer:GetCurrentOBPickupType()
    local lootObj = Server.LootingServer:GetCurrentOBLootObj()
    if gamePickupType ~= nil then
        Server.LootingServer:SetCurrentSelectorData({type = gamePickupType, interactors={lootObj}})
    else
        Server.LootingServer:SetCurrentSelectorData()
    end
    if self.Field:GetCurrentLootUIState() == LootingConfig.ELootUIState.LootBag then
        if gamePickupType then
            Server.LootingServer:FullUpdateOBLootingData()
        end
        ---@type LootingMainView_HD
        local mainPanel = self.Field:GetMainPanel()
        if mainPanel then
            if gamePickupType then
                mainPanel:InitPanel(gamePickupType, {lootObj}, true)
            else
                mainPanel:InitPanel()
            end
        end
    else
        if gamePickupType then
            local hudLayerController = Facade.UIManager:GetHUDLayerController()
            local hudState = hudLayerController and hudLayerController.state or nil
            if hudState and hudState:HasState(EGameHUDState.GHS_OpenMap) then
                log("LootingModule:OB_OnRep_LootingObj don't open loot panel because map is showing")
                return
            end
            LootingLogic.OBOpenLootPanel()
        end
    end
end

function LootingModule:OB_OnRep_bIsOpenBag()
    log("LootingModule:OB_OnRep_bIsOpenBag")
    local obInvMgr = Facade.GameFlowManager:GetOBInvMgr()
    if not isvalid(obInvMgr) then
        return
    end
    if not obInvMgr.bIsOpenBag and self.Field:GetCurrentLootUIState() == LootingConfig.ELootUIState.LootBag and not self.Field.bOBIsActivelyOpenBag then
        Module.Looting:StopAllLoots()
    end
end

function LootingModule:ShowItemConsumeAllTip(itemId)
    if ItemHelperTool.GetMainTypeById(itemId) == EItemType.CollectableItem and ItemHelperTool.GetSubTypeById(itemId) == ECollectableType.Key then
        local itemName = ItemConfigTool.GetItemName(itemId)
        if itemName ~= "" then
            Timer.DelayCall(2, function()
                Module.CommonTips:ShowSimpleTip(string.format(LootingConfig.Loc.KeyConsumeAllTip, itemName), 3)
            end)
        end
    end
end

function LootingModule:OnNotifyLootingState(cmdGid, lootingState)
    Server.LootingServer:OnNotifyLootingState(cmdGid, lootingState)
end

function LootingModule:OnContainerLootingPlayersChange(container)
    if container == Server.LootingServer:GetCurrentInteractingBox() then
        self.Config.Events.evtContainerLootingPlayerChange:Invoke()
    end
end

function LootingModule:GetLootingTeammates()
    local container = Server.LootingServer:GetCurrentInteractingBox()
    if isinvalid(container) then
        return
    end
    local playerState = Facade.GameFlowManager:GetPlayerState()
    local teammateUinToIndex = {}
    if isvalid(playerState) then
        local memberInfoList = playerState.MemberInfoList
        for _, memberInfo in ipairs(memberInfoList) do
            if memberInfo.PlayerUin ~= playerState.Uin then
                teammateUinToIndex[memberInfo.PlayerUin] = memberInfo.InnerTeamIndex
            end
        end
    end
    local lootingTeammates = {}
    for _, playerUin in pairs(container:GetCurrentLootingPlayers()) do
        local teamInnerIndex = teammateUinToIndex[playerUin]
        if teamInnerIndex ~= nil and teamInnerIndex >= 0 then
            lootingTeammates[teamInnerIndex+1] = true
        end
    end
    return lootingTeammates
end

function LootingModule:CheckSafeBoxFirstByItemId(itemId, itemNum)
    return LootingOperaLogic.CheckSafeBoxFirstByItemId(itemId, itemNum) or false
end

function LootingModule:GetPlayerGainValue()
    return LootingLogic.GetPlayerGainValue()
end

--C++调用
function LootingModule:GetShopDynamicGuidePrice(itemId,dur)
    return Server.ShopServer:GetShopDynamicGuidePrice(itemId, dur)
end

function LootingModule:GetShopDynamicGuidePriceByGid(itemGId)
    local itemBase = Server.LootingServer:GetItemDataByGid(itemGId)
    if itemBase then
        return itemBase:GetTotalSellPrice()
    end
    return 0
end


function LootingModule:OnPassiveMoveEnd()
    local currentInteractorType = Server.LootingServer:GetCurrentInteractorType()
    local currentInteractingBox = Server.LootingServer:GetCurrentInteractingBox()
    local character = Facade.GameFlowManager:GetCharacter()
    if isvalid(character) then
        if currentInteractingBox then
            if isvalid(currentInteractingBox) and not ULootUtil.CheckLootingDistanceValid(character, currentInteractingBox) then
                self:StopAllLoots()
            end
        elseif currentInteractorType == EGamePickupType.NearbyPickups then
            local slot = Server.InventoryServer:GetSlot(ESlotType.NearbyPickups, ESlotGroup.Nearby)
            if slot then
                local items = slot:GetItems_RefConst()
                for _, item in pairs(items) do
                    local pickup = Server.LootingServer:GetCurrentNearbyPickupByGID(item.gid)
                    if isvalid(pickup) and not ULootUtil.CheckLootingDistanceValid(character, pickup) then
                        self:StopAllLoots()
                        break
                    end
                end
            end
        end
    end
end

function LootingModule:IsCreatingUIForCollectionRoom()
    return self.Field.bIsCreatingUIForCollectionRoom
end

function LootingModule:PlayDropAudio(itemID)
    LootingAudioLogic.PlayDropAudio(itemID)
end

function LootingModule:OpenItemSplitPanel(item, parentWidget)
    return LootingLogic.OpenItemSplitPanel(item, parentWidget)
end

function LootingModule:GetMainPanelHandle()
    return self.Field:GetMainPanelHandle()
end

function LootingModule:BindEvents(bBind)
    if bBind then
        LootingEvtLogic.BindLuaEvents()
    else
        LootingEvtLogic.UnBindLuaEvents()
    end
end

function LootingModule:HasShowForbiddenToCarryOut(itemGid)
    return self.Field.hasShowForbiddenToCarryOut[itemGid]
end

function LootingModule:MarkShowForbiddenToCarryOut(itemGid)
    self.Field.hasShowForbiddenToCarryOut[itemGid] = true
end

function LootingModule:GetCachedSafeBoxSkinID()
    return self.Field.safeBoxSkinID
end

function LootingModule:SetCachedSafeBoxSkinID(safeBoxSkinID)
    self.Field.safeBoxSkinID = safeBoxSkinID
end

function LootingModule:GetSafeBoxSkinID()
    return LootingLogic.GetSafeBoxSkinID()
end

--endregion
-----------------------------------------------------------------------

return LootingModule
