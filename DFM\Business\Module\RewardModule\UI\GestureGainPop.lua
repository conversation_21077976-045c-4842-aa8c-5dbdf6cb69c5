----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMReward)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class GestureGainPop : LuaUIBaseView
local RewardBaseView = require "DFM.Business.Module.RewardModule.UI.RewardBaseView"
local GestureGainPop = ui("GestureGainPop", RewardBaseView)
local EGPInputModeType = import "EGPInputModeType"
local HeroHelperTool = require "DFM.StandaloneLua.BusinessTool.HeroHelperTool"
local RewardDetail = require "DFM.Business.Module.RewardModule.UI.RewardDetail"
local FAnchors = import "Anchors"

function GestureGainPop:Ctor()
    self._wtRewardDetail = self:Wnd("wtRewardDetail", RewardDetail)
    self._wtRewardDetail:BindJumpClick(self._OnSkipBtnClick, self)
    self._wtRewardDetail:SetShowSkipTxt(true)
    self._wtGestureImg = self:Wnd("wtGestureImg", UIImage)
    self._wtDownloadPanel = self:Wnd("wtDownloadPanel", UIWidgetBase)
    self._closeClickCount = -1
end


function GestureGainPop:OnInitExtraData(itemId)
    self._itemId = itemId
end

function GestureGainPop:OnOpen()
    self:_AddListeners()
    Module.Guide:SendMsg(EGuideMsgSig.RewardGestureGainPop, "OnOpen")
end

function GestureGainPop:OnShowBegin()
    self:_RefreshWidget()
    self._wtRewardDetail:AddJumpInputAction()
end

function GestureGainPop:OnClose()
    self:RemoveAllLuaEvent()
    Facade.UIManager:ClearSubUIByParent(self, self._wtDownloadPanel)
    Module.Guide:SendMsg(EGuideMsgSig.RewardGestureGainPop, "OnClose")
    if self._bExecuteClose ~= true then
        self._bExecuteClose = true
        Module.Reward:ShowNextRewards()
    end
end

function GestureGainPop:OnShow()
    self:SetCPPValue("WantedInputMode", EGPInputModeType.UIOnly)
end

function GestureGainPop:OnHide()
end

function GestureGainPop:OnAnimFinished(anim)
    if anim == self.WBP_Hero_ShowHero_In03 then
        if self._closeClickCount < 1 then
            self._closeClickCount = self._closeClickCount + 1
        end
        self:PlayAnimation(self.WBP_Hero_ShowHero_loop, 0, 0, EUMGSequencePlayMode.Forward, 1, false)
    elseif anim == self.WBP_Hero_ShowHero_out3 then
        if self._bExecuteClose ~= true then
            self._bExecuteClose = true
            Module.Reward:ShowNextRewards(self._bTabPressed == true)
        end
    end
end

function GestureGainPop:_AddListeners()
    self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult,self._OnDownloadResult,self)
    self:AddLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged, self._OnPackStateChanged, self)
    self:AddLuaEvent(Module.IrisSafeHouse.Config.evtTabBackToSafeHouseHD, self._OnTabPressed, self)
end

function GestureGainPop:_OnSkipBtnClick()
    if self._closeClickCount == 1 then
        self._closeClickCount = 2
        self:HandleTransition(true)
        Facade.SoundManager:StopUIAudioEvent("UI_Common_Popup")
        self:StopAnimation(self.WBP_Hero_ShowHero_loop)
        self:PlayAnimation(self.WBP_Hero_ShowHero_out3, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    elseif self._closeClickCount == 0 then
        self._closeClickCount = 1
        self:SkipAnimation(self.WBP_Hero_ShowHero_In03)
        self._wtRewardDetail:SkipInAnimation()
    end
end

function GestureGainPop:_RefreshWidget()
    logerror("[v_dzhanshen] GestureGainPop.lua https://tapd.woa.com/tapd_fe/20421949/bug/detail/1020421949140993277")
    if self._closeClickCount < 1 then
        self._closeClickCount = self._closeClickCount + 1
    end
    self._wtRewardDetail:SetType(2)
    self._wtRewardDetail:SetDesc(Module.Reward.Config.Loc.Universal)
    local executionActionDataTable = HeroHelperTool.GetAllFinisherData()
    local executionActionRow = executionActionDataTable[self._itemId]
    if executionActionRow then
        self._wtRewardDetail:SetMainTitle(Module.Reward.Config.Loc.ObtainExecutionAction)
        self._wtRewardDetail:SetQuality(executionActionRow.ExecutionLevel)
        self._wtRewardDetail:SetName(executionActionRow.ExecutionName)
        for index, heroId in ipairs(executionActionRow.BelongedHeroIDs) do
            local heroData = HeroHelperTool.GetHeroProto(heroId)
            if heroData then
                self._wtRewardDetail:SetDesc(heroData.Name)
                break
            end
        end
    else
        local gestureDataTable = HeroHelperTool.GetAllGestureData()
        local gestureRow = gestureDataTable[self._itemId]
        if gestureRow then
            self._wtRewardDetail:SetMainTitle(Module.Reward.Config.Loc.ObtainGesture)
            self._wtRewardDetail:SetQuality(gestureRow.GestureLevel)
            self._wtRewardDetail:SetName(gestureRow.GestureName)
        end
    end
    self:_RefreshResource()
    self:_RefreshDownloadBtn()
    if self._closeClickCount < 1 then
        self:PlayAnimation(self.WBP_Hero_ShowHero_In03, 0, 1, EUMGSequencePlayMode.Forward, 1, true)
    end
    Facade.SoundManager:PlayUIAudioEvent("UI_Common_Popup")
    self:HandleTransition(false)
end

function GestureGainPop:_RefreshResource()
    local executionActionDataTable = HeroHelperTool.GetAllFinisherData()
    local executionActionRow = executionActionDataTable[self._itemId]
    if executionActionRow then
        self._wtGestureImg:AsyncSetImagePath(executionActionRow.ExecutionImage)
    else
        local gestureDataTable = HeroHelperTool.GetAllGestureData()
        local gestureRow = gestureDataTable[self._itemId]
        if gestureRow then
            self._wtGestureImg:AsyncSetImagePath(gestureRow.GestureImage)
        end
    end
end

function GestureGainPop:_OnDownloadResult(moduleName, bDownloaded, errorCode)
    local moduleKey = Module.ExpansionPackCoordinator:GetDownloadCategary(self._itemId)
    if moduleName == moduleKey then
        self:_RefreshResource()
        self:_RefreshDownloadBtn()
    end
end

function GestureGainPop:_OnPackStateChanged(moduleName, packState)
    local isSuccess = packState == Module.ExpansionPackCoordinator.Config.PackState.Installed
    self:_OnDownloadResult(moduleName, isSuccess, 0)
end

function GestureGainPop:_RefreshDownloadBtn()
    if Module.ExpansionPackCoordinator:IsSupportLitePackage() then
        if hasdestroy(self._wtCommonDownload) then
            Facade.UIManager:RemoveSubUIByParent(self, self._wtDownloadPanel)
            local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.LitePackageCommonDownload, self._wtDownloadPanel)
            self._wtCommonDownload = getfromweak(weakUIIns)
            if not hasdestroy(self._wtCommonDownload) then
                self._wtCommonDownload:SetUIPositionType(2)
                local slot = UWidgetLayoutLibrary.SlotAsCanvasSlot(self._wtCommonDownload)
                if isvalid(slot) then
                    local anchor = FAnchors()
                    anchor.Minimum = FVector2D(0, 0)
                    anchor.Maximum = FVector2D(1.0, 1.0)
                    slot:SetAnchors(anchor)
                    slot:SetAlignment(FVector2D(0, 0))
                    slot:SetOffsets(FMargin(0, 0, 0, 0))
                end
            end
        end
        if not hasdestroy(self._wtCommonDownload) then
            local moduleKey = Module.ExpansionPackCoordinator:GetDownloadCategary(self._itemId)
            local bDownloaded = Module.ExpansionPackCoordinator:IsDownloadedByModuleName(moduleKey)
            if not bDownloaded then
                self._wtCommonDownload:InitModuleKey(moduleKey)
                self._wtCommonDownload:SelfHitTestInvisible()
            else
                self._wtCommonDownload:Collapsed()
            end
        end
    end
end

function GestureGainPop:OnNavBack()
    self:_OnSkipBtnClick()
    return true
end


function GestureGainPop:_OnTabPressed()
    self._bTabPressed = true
    self:StopAnimation(self.WBP_Hero_ShowHero_in)
    Facade.SoundManager:StopUIAudioEvent("UI_Common_Popup")
    self:StopAnimation(self.WBP_Hero_ShowHero_loop)
    self:PlayAnimation(self.WBP_Hero_ShowHero_out3, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
end

return GestureGainPop