----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMStore)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

---@class ProductPreview : LuaUIBaseView
local StaffLotteryMainUI = ui("StaffLotteryMainUI")
local StoreConfig = Module.Store.Config
local StoreLogic = require "DFM.Business.Module.StoreModule.Logic.StoreLogic"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local UDFCommonMediaView = import "DFCommonMediaView"
local MediaResTable = Facade.TableManager:GetTable("MediaResTable")
local UIThemeUtil = require "DFM.YxFramework.Managers.UI.Util.UIThemeUtil"
local LiteCommonDownload =  require "DFM.Business.Module.LitePackageModule.UI.LiteCommonDownload"

function StaffLotteryMainUI:Ctor()    
    self._wtDFCanvasPanel = self:Wnd("DFCanvasPanel_7", UIWidgetBase)
    -- 控件获取    
    self._wtPlatformPaddingBox0 = self:Wnd("PlatformPaddingBox_0", UIWidgetBase) -- 文本控件的父控件

    self._wtPlatformPaddingBox2 = self:Wnd("PlatformPaddingBox_2", UIWidgetBase) -- 抽奖按钮的父控件

    self._wtPlatformPaddingBox1 = self:Wnd("PlatformPaddingBox_1", UIWidgetBase) -- 底部控件的父控件

    self._wtCanvasPanel2 = self:Wnd("DFCanvasPanel_2", UIWidgetBase)

    self._wtBgWidget = self:Wnd("WBP_Common_UnScaleBg", UIWidgetBase)

    self._wtMaskImg = self:Wnd("wtFirearmImg_1", UIImage)
    
    -- self._wtPlatformPaddingBox4 = self:Wnd("PlatformPaddingBox_4", UIWidgetBase) -- 播放视频按钮的父控件

    self._wtFirearmImg = self:Wnd("wtFirearmImg", UIImage) -- 背景图，调用一个蓝图函数，传奖池id过去，然后需要换图时，重构同学修改蓝图就可以

    self._wtSkipBtn = self:Wnd("wtSkipBtn", UIWidgetBase) -- 跳过动画按钮    

    self._wtSkipDownloadBtn = self:Wnd("wt_SkipBtn_Button", UIWidgetBase) -- 跳过大小包下载按钮    
    self._wtSkipDownloadBtn:Event("OnClicked", self.OnSkipDownloadBtnClicked, self)


    self._wtDFHorizontalBox = self:Wnd("DFHorizontalBox",UIWidgetBase) -- 需要的奖券信息的父控件

    --- 控件处理
    -- self._wtSkipBtn:SetKeepSelfOnClick(true)
    -- self._wtSkipBtn:BindClickEvent(self.OnSkipBtnClicked, self)
    
    self._wtCountWidget = self:Wnd("wtStoreCountDown", UIWidgetBase) -- 奖池的时间信息
    if self._wtCountWidget ~= nil then
        self._wtTextCountDown = self._wtCountWidget:Wnd("wtTextCountDown", UITextBlock)
    end

    self._wtTitleImg = self:Wnd("DFImage_Title", UIImage) -- 文字图片，调用一个蓝图函数，传奖池id过去，然后需要换图时，重构同学修改蓝图就可以

    self._wtDescTxt = self:Wnd("wtWeaponDesc", UITextBlock) -- 描述文本

    self._wtDetailCheckBtn = self:Wnd("wtDetailCheckBtn", DFCommonButtonOnly) -- 跳转到概率总览的按钮
    self._wtDetailCheckBtn:Event("OnClicked", self.OnDetailCheckBtnClick, self)

    self._wtLotteryOnceBtn = self:Wnd("wtMultipleActionBtn", DFCommonButtonOnly) -- 抽奖按钮
    -- UIThemeUtil.CheckIfAutoApplyTheme(self._wtLotteryOnceBtn)
    self._wtLotteryOnceBtn:Event("OnClicked", self._OnLotteryOnceBtnClick, self)

    self._wtCurrencyCostImg = self:Wnd("DFImage", UIImage) -- 抽奖所消耗的货币图标

    self._wtCostNumberTxt = self:Wnd("DFTextBlock_CostNumber", UITextBlock) -- 抽奖所消耗的货币数量

    self._wtPrizePoolPreviewBtn = self:Wnd("WBP_DFCommonButtonV2S2", DFCommonButtonOnly) -- 奖池预览按钮
    self._wtPrizePoolPreviewBtn:Event("OnClicked", self._OnPrizePoolPreviewBtnClick, self)
    self._wtPrizePoolPreviewBtn:BP_SetMainTitle(Module.Store.Config.Loc.StaffLotteryRewardTipText)

    self._wtCurrencyImg = self:Wnd("DFImage_1", UIImage) -- 当前拥有的货币图标
    self._wtCurrencyImg:Collapsed()

    self._wtTotalNumberNameTxt = self:Wnd("DFTextBlock_Total_1", UITextBlock)
    self._wtTotalNumberNameTxt:Collapsed()

    self._wtTotalNumberTxt = self:Wnd("DFTextBlock_Total", UITextBlock) -- 当前拥有的货币数量
    self._wtTotalNumberTxt:Collapsed()
    
    -- self._wtAnimBg = self:Wnd("AnimBg", UIWidgetBase) -- 背景动效组件
    -- self._wtAnimBg:Collapsed()

    self._wtAnimBGSlot = self:Wnd("DFNamedSlot_49", UINamedSlot) --背景slot

    self._wtBGForLowQuality = self:Wnd("wtFirearmImg", UIImage) --低配机背景

    self._wtPlayVideoBtn = self:Wnd("wtPlayVideoBtn", UIButton) -- 播放视频按钮
    self._wtPlayVideoBtn:Event("OnClicked", self._OnPlayVideoBtnClick, self)
    self._wtPlayVideoBtn:Collapsed()

    --视频专用
    ---@type CommonVideoComponent
    self._mediaComponent = self:Wnd("WBP_CommonVideoComponent", CommonVideoComponent)
    self._mediaComponent:InitComponent(false)
    if self._mediaComponent then
        self._mediaComponent:Collapsed()        
        -- 注册视频播放结束的回调
        if self._mediaComponent.evtOnMediaPlayEnd then
            self._mediaComponent.evtOnMediaPlayEnd:AddListener(self.OnMediaPlayEnd, self) -- 在视频播放完毕和视频被跳过的时候都会触发到
        end
    end

    self._wtCommonDownload = self:Wnd("WBP_CommonDownload", LiteCommonDownload)

    self.needSkip = false -- 视频是否允许跳过

    self._toPrizePoolUI = false --是否是去到奖池UI
    Module.Store:SetIsToPricePool(false)

    self._curMusicPlay = ""

    -- -- 空格跳过
    -- if DFHD_LUA == 1 then
    --     self._inputMonitor = Facade.UIManager:GetInputMonitor()
    --     self._handle = self._inputMonitor:AddDisplayActionBinding("JumpOver", EInputEvent.IE_Pressed, self.OnSkipBtnClicked, self, EDisplayInputActionPriority.UI_Pop)
    -- end
    self.lotteryPoolInfoMap = setdefault(self.lotteryPoolInfoMap, {})

    self.rewardProps = setdefault(self.rewardProps, {})

    self.enablePlayFullScreenCG = UDFCommonMediaView.EnableCGMediaPlay()

    self._videoPakModuleName = nil -- 演绎视频大小包名

    self._fOnVideoPakDownloadedCallback = nil -- 演绎视频包下载完成回调

    self:_DisplayDownloadWidgets(false)

end

function StaffLotteryMainUI:OnInitExtraData(param, tabID, isCollaboration, needPlayVedioLater)

    if StoreLogic.isLowMobile() then
        self._wtBGForLowQuality:Visible()
    else
        self._wtBGForLowQuality:Collapsed()
    end
    --tabID with click second tab

    --param是否是联动界面

    self.IsCollaboration = isCollaboration
    self.needPlayVedioLater = needPlayVedioLater

    if tabID ~= nil then
        self.tabID = tabID
    else
        self.tabID = 1
    end
end

-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function StaffLotteryMainUI:OnOpen()

    self:SetLocation(true)
    self:PC()
    self.PlayingMediaName = nil -- 默认不播放视频
    self.MediaNameToPlay = nil -- 需要播放的视频名
    -- 更新数据
    self:RefreshData()
    -- 刷新UI显示
    self:RefreshUI()
    -- 设置货币信息（数量和图标）
    self:SetKeyInfo()
end
-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function StaffLotteryMainUI:OnClose()
    if self._timerHandle then
        self._timerHandle:Release()
        self._timerHandle = nil
    end
    self._curMusicPlay = ""
end

function StaffLotteryMainUI:OnShowBegin()
    self:StopAllAnim()

    --无其他兄弟页签时顶替上方留白
    local hasSibling = self:GetSiblingTabCount()>1
    loginfo("StaffLotteryMainUI:OnShowBegin hasSibling = "..tostring(hasSibling))
    
    if self.SetIsTopTab then
        self:SetIsTopTab(hasSibling) --true有其他页签样式，false无其他页签样式
    end


    Facade.UIManager:CommitTransition(false)

    self:_DisplayDownloadWidgets(false)
    
    -- self._toPrizePoolUI = self._curMusicPlay ~= ""
    self._wtLotteryOnceBtn:Visible()

    -- -- 更新数据
    self:RefreshData()
    -- 请求奖池相关奖励数据
    self:GetServerLotteryInfo()

    -- 刷新下载信息
    self:_RefreshCommonDownload()

    -- 设置描述信息
    if self._currentLotteryInfo and self._currentLotteryInfo.LotteryText1 then
        self._wtDescTxt:SetText(self._currentLotteryInfo.LotteryText1)
    end

     -- 读取服务器发来的SpecialItemBuyConf表格，获取数据
     self.keyConfig = Server.StoreServer:GetSpecialItemInfoByPresentItemId(self._currentLotteryInfo.LotteryKeyId)
     if not self.keyConfig then
         logerror("StaffLotteryMainUI:OnInitExtraData self.keyConfig is nil")
         Facade.UIManager:CloseUI(self)
         return
     end

     if Module.Store:GetCanPlayMusic() and Module.Store:GetIsToPricePool() == false then
        if self._currentLotteryInfo then
            -- -- 播放退出音乐
            -- Facade.SoundManager:StopUIAudioEvent(Module.Store:GetLotteryMusic(self._currentLotteryInfo.LotteryId))
            local newMusic = Module.Store:GetLotteryMusic(self._currentLotteryInfo.LotteryId)
            if newMusic then
                Module.Store:SetCurMusicName(newMusic)
                -- 播放进入音乐
                Facade.SoundManager:StopUIAudioEvent(newMusic)
                Facade.SoundManager:PlayUIAudioEvent(newMusic)
               
                self._curMusicPlay = newMusic
                loginfo("[echewzhu]StoreModule Facade.SoundManager:PlayUIAudioEvent newMusic = "..newMusic)
            end
        end
    end
    Module.Store:SetIsToPricePool(false)
    self._toPrizePoolUI = false

    if self._currentLotteryInfo ~= nil then
        --设置背景
        local weakuiIns, instanceId = Module.Store:SetLotteryUIBackground(self._currentLotteryInfo.LotteryId, self, self._wtAnimBGSlot, self._wtBGForLowQuality)
        local uiIns = getfromweak(weakuiIns)
        if uiIns then
            -- uiIns:PlayLoopAnimation()
            if uiIns.ResetSpineAnim then
                uiIns:ResetSpineAnim()
            end
        end
    end
    StoreLogic.SetLotteryUIArtLogo(self._currentLotteryInfo.LotteryId, self._wtTitleImg)

    --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() then
        self:_EnableGamepadFeature()
    end
    --- END MODIFICATION
    
     -- 处理视频自动播放逻辑以及UI显隐逻辑
     if self._currentLotteryInfo then
        if self._currentLotteryInfo.MoviesRowName and self._currentLotteryInfo.MoviesRowName ~= "" and self.enablePlayFullScreenCG == true then
            self._wtPlayVideoBtn:Visible()
            -- self.lotteryPoolInfoMap[self._currentLotteryInfo.LotteryId]
            local lotteryPoolInfo = self.lotteryPoolInfoMap[self._currentLotteryInfo.LotteryId]
            local propNumIds = {}
            local OwnedItems = {}
            if lotteryPoolInfo then
                propNumIds = lotteryPoolInfo.prop_num_ids
                local LotteryRewards = lotteryPoolInfo.lottery_rewards
                for index, rewardItem in ipairs(LotteryRewards) do
                    for index, propNumId in ipairs(propNumIds) do
                        if rewardItem and rewardItem.num_id == propNumId then
                            for key, prop in pairs(rewardItem.props) do
                                table.insert(OwnedItems, prop.id)
                            end
                        end
                    end
                end
            end

            local isOwned = StoreLogic.GetIsItemOwned(self._currentLotteryInfo.MoviesRowName, OwnedItems)
            self.hasPlayed = Server.StoreServer:GetIsStorePvPlayed(self._currentLotteryInfo.MoviesRowName)
            local needAutoPlay = Module.Store.Field:CheckVideoNeedAutoPlay(self._currentLotteryInfo.MoviesRowName)
            if isOwned then
                self.hasPlayed = true
            end
            if (not self.hasPlayed or self.hasPlayed == nil) and not self.needPlayVedioLater then
                self:AutoPlayVideo()
            end
            self.needPlayVedioLater = false
        else
            self._wtPlayVideoBtn:Collapsed()
        end

         -- 埋点
        LogAnalysisTool.DoSendStoreViewPageReportLog(8, self._currentLotteryInfo.LotteryId, 0, 0)
    else
        self._wtPlayVideoBtn:Collapsed()
    end
    
end
-- UI OnOpen后、调用Show()显示时触发
-- 做表现
---@overload fun(LuaUIBaseView, OnShow)
function StaffLotteryMainUI:OnShow()    
    Server.StoreServer:SetStaffLotteryMainUIOpened(true) -- 记录界面打开
    -- 入场动效
    if self.WBP_StaffLottery_MainUI_zhuan01 then
        self:PlayAnimation(self.WBP_StaffLottery_MainUI_zhuan01, 0, 1, EUMGSequencePlayMode.Forward, 1, true)
    end
    -- self.PlayingMediaName = nil -- 默认不播放视频
    -- self.MediaNameToPlay = nil -- 需要播放的视频名

    -- 刷新UI显示
    -- 请求奖池相关奖励数据
    self:RefreshUI()
    self:SetKeyInfo()
    -- 事件监听
    self:RegisterEventListeners()
   

    --设置延迟弹出解锁奖励
    Server.HeroServer:_ShouldTriggerUnlockEvtImmediate(true)

    

    self:ReportRedPoint()

    --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() then
        -- 要等_wtPlayVideoBtn确定显隐之后再设置bottom bar
        self:_SetBottombar()
    end
    --- END MODIFICATION
end

--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
function StaffLotteryMainUI:OnHideBegin()
    self:UnbindAnyKeyPress()
    if IsHD() then
        self:_DisableGamepadFeature()
    end
    self:_DisplayDownloadWidgets(false)
end
--- END MODIFICATION

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function StaffLotteryMainUI:OnHide()
    -- 出场动效
    if self.WBP_StaffLottery_MainUI_zhuan02 then
        self:PlayAnimation(self.WBP_StaffLottery_MainUI_zhuan02, 0, 1, EUMGSequencePlayMode.Forward, 1, true)
    end
    -- 取消事件监听
    self:UnRegisterEventListeners()

    Server.StoreServer:SetStaffLotteryMainUIOpened(false) -- 记录界面关闭

    self:RemoveLuaEvent(Server.StoreServer.Events.evtStoreLotteryInfoUpdated)

    --设置延迟弹出解锁奖励关闭，防止影响到其他界面
    Server.HeroServer:_ShouldTriggerUnlockEvtImmediate(false)

    if Module.Store:GetCanPlayMusic() then
        local newMusic =  Module.Store:GetLotteryMusic(self._currentLotteryInfo.LotteryId)
        -- 播放退出音乐
        if self._currentLotteryInfo and newMusic then
            if newMusic == self._curMusicPlay then
                Facade.SoundManager:StopUIAudioEvent(newMusic)
                loginfo("StaffLotteryMainUI:OnHide Facade.SoundManager:StopUIAudioEvent")
            end
        end
    end
    loginfo("StaffLotteryMainUI:OnHide")

    if MediaResTable then
        local data = MediaResTable[self._currentLotteryInfo.MoviesRowName]
        if data then
            local curConfig = data.MediaResList:Get(0)
            if curConfig then
                local musicName = curConfig.AudioAssetKey
                Facade.SoundManager:StopUIAudioEvent(musicName)
                loginfo("StaffLotteryMainUI:OnClose audioName = "..musicName)
            end
        end
    end
    self._mediaComponent:Stop()

    self._videoPakModuleName = nil
    self._fOnVideoPakDownloadedCallback = nil
end

function StaffLotteryMainUI:OnAnimFinished(anim)
    if anim == self.WBP_StaffLottery_MainUI_qiehuan then -- 抽奖视频播放前的切换动画播放结束之后
        if self.WBP_StaffLottery_MainUI_qiehuan2 then -- 视频播放的渐入动画
            self:PlayAnimation(self.WBP_StaffLottery_MainUI_qiehuan2, 0, 1, EUMGSequencePlayMode.Forward, 1, true)
        end
        
        if self.MediaNameToPlay then -- 播放视频
            self:PlayVideoByName(self.MediaNameToPlay, self.needSkip)
            self.MediaNameToPlay = nil
        end
    end
end


-- 更新数据
function StaffLotteryMainUI:RefreshData()
    -- 获取当前奖池信息（是否在开放时间内）
    self._currentLotteryInfo, self.LotteryProbDistributionTable = Module.Store.Field:GetShopLotteryDataByTabID(self.tabID, self.IsCollaboration)
    if self._currentLotteryInfo == nil then
        logerror("当前时间不在任何奖池的开放时间内")
        return
    end
    -- 视频信息
    self.videoGreat = self._currentLotteryInfo.VedioGreat -- 大奖视频
    self.videoNormal = self._currentLotteryInfo.VedioNormal -- 普通奖视频

    -- 获取抽奖券信息
    local lotteryKeyId = self._currentLotteryInfo.LotteryKeyId
    self.lotteryKeyConfig = Server.StoreServer:GetLotteryKeyInfo(lotteryKeyId)

    if self._currentLotteryInfo == nil then
        return
    end
    self.LotteryProbDistributionTable = Server.StoreServer:GetDictStoreLotteryProbDistributionItemsByLotteryID(self._currentLotteryInfo.LotteryId)
    -- 获取大奖信息
    for _, lotteryProbDistribution in pairs(self.LotteryProbDistributionTable) do
        if lotteryProbDistribution.Is_great_reward == true then -- 大奖
            self.SpecialItemBigReward = lotteryProbDistribution.SpecialItem -- 记录大奖的SpecialItem
        end
    end

end

function StaffLotteryMainUI:_OnLotteryInfoChanged(res)
    loginfo("StaffLotteryMainUI:_OnLotteryInfoChanged")

    if res.result == 0 then
        -- 更新数据
        loginfo("StaffLotteryMainUI:GetServerLotteryInfo:fCSShopGetLotteryInfoRes Update data")
        self.lotteryPoolInfoArray = res.lottery_pool_info

        if self.lotteryPoolInfoArray then
            for _, shopLotteryInfo in pairs(self.lotteryPoolInfoArray) do
                if shopLotteryInfo then
                    self.lotteryPoolInfoMap[shopLotteryInfo.lottery_id] = shopLotteryInfo
                end
            end
            if self._currentLotteryInfo then
                local lotteryInfo = self.lotteryPoolInfoMap[self._currentLotteryInfo.LotteryId]
                if lotteryInfo then
                    self.propNumIds = lotteryInfo.prop_num_ids
                    self.currentRound = self.propNumIds and (#self.propNumIds + 1) or 1 -- 更新当前轮次
                    self.currentCost = lotteryInfo.cost_num -- 下一抽需要消耗的券数量

                    for _, lotteryReward in pairs(lotteryInfo.lottery_rewards) do
                        self.rewardProps[lotteryReward.num_id] = lotteryReward.props -- 一般都是1个，只有台词是多个
                    end
                    Module.Store.Field:SetRewardProps(self.rewardProps)
                end
            end
        end
        -- 更新UI
        self:RefreshUI()
        self:SetKeyInfo()
        -- 自动抽奖
        if self.autoClickLottery then
            self.autoClickLottery = false
            self:ExecuteLottery()
        end
    end
end

-- 获取奖池里的奖励信息
function StaffLotteryMainUI:GetServerLotteryInfo()
    self.lotteryPoolInfoMap, self.lotteryPoolInfoArray = Module.Store.Field:GetLotteryPoolInfoMap()
    if self.lotteryPoolInfoArray then
        -- for _, shopLotteryInfo in pairs(self.lotteryPoolInfoArray) do
        --     if shopLotteryInfo then
        --         self.lotteryPoolInfoMap[shopLotteryInfo.lottery_id] = shopLotteryInfo
        --     end
        -- end
        
    end
    if self._currentLotteryInfo then
        local lotteryInfo = self.lotteryPoolInfoMap[self._currentLotteryInfo.LotteryId]
        if lotteryInfo then
            self.propNumIds = lotteryInfo.prop_num_ids
            self.currentRound = self.propNumIds and (#self.propNumIds + 1) or 1 -- 更新当前轮次
            self.currentCost = lotteryInfo.cost_num -- 下一抽需要消耗的券数量

            for _, lotteryReward in pairs(lotteryInfo.lottery_rewards) do
                self.rewardProps[lotteryReward.num_id] = lotteryReward.props -- 一般都是1个，只有台词是多个
            end
            Module.Store.Field:SetRewardProps(self.rewardProps)
        end
    end

    -- 更新UI
    self:RefreshUI()
    self:SetKeyInfo()
end

-- 恢复显示
function StaffLotteryMainUI:RecoverRenderOpacity()
    self._wtCanvasPanel2:SetRenderOpacity(1)
    self._wtCanvasPanel2:SetRenderScale(FVector2D(1.0, 1.0))

    
    self._wtBgWidget:SetRenderOpacity(1)
    self._wtBgWidget:SetRenderScale(FVector2D(1.0, 1.0))

    self._wtPlatformPaddingBox0:SetRenderOpacity(1)
    self._wtPlatformPaddingBox0:SetRenderTranslation(FVector2D(0, 0))

    self._wtPlatformPaddingBox1:SetRenderOpacity(1)
    self._wtPlatformPaddingBox1:SetRenderTranslation(FVector2D(0, 0))

    self._wtPlatformPaddingBox2:SetRenderOpacity(1)
    self._wtPlatformPaddingBox2:SetRenderTranslation(FVector2D(0, 0))

    self._wtMaskImg:SetRenderOpacity(0)
end

function StaffLotteryMainUI:RefreshUI()

    -- 设置倒计时信息
    if self._currentLotteryInfo then
        self:RefreshRemainingTime()
        --1秒更新一次倒计时
        if not self._timerHandle then
            self._timerHandle = Timer:NewIns(1,0)
            self._timerHandle:AddListener(self.RefreshRemainingTime, self)
            self._timerHandle:Start()
        end
    end

    -- 处理开启按钮逻辑
    if self._currentLotteryInfo == nil then
        return
    end
    self.LotteryProbDistributionTable = Server.StoreServer:GetDictStoreLotteryProbDistributionItemsByLotteryID(self._currentLotteryInfo.LotteryId)
    -- if  #self.LotteryProbDistributionTable == 0 then
    --     loginfo("[echewzhu]StaffLotteryMainUI:RefreshUI: #self.LotteryProbDistributionTable == 0")
        
    -- end

    if self.currentRound
    and self.currentRound > #self.LotteryProbDistributionTable then -- 已获得所有奖励
        self._wtLotteryOnceBtn:SetIsEnabledStyle(false)
        self._wtLotteryOnceBtn:SetMainTitle(string.format(StoreConfig.Loc.StaffLotteryAllReward)) -- 已获得所有奖励                
        self._wtDFHorizontalBox:Collapsed()
    else
        self._wtLotteryOnceBtn:SetIsEnabledStyle(true)
        self._wtLotteryOnceBtn:SetMainTitle(string.format(StoreConfig.Loc.StaffLotteryOpenOnce)) -- 开启1次           
        self._wtDFHorizontalBox:Visible()
    end
end

function StaffLotteryMainUI:SetKeyInfo()
    if not self._currentLotteryInfo then
        return
    end
    -- 设置图标    
    local itemAsset = ItemConfigTool.GetItemAssetById(self._currentLotteryInfo.LotteryKeyId)
    if itemAsset then
        self._wtCurrencyCostImg:AsyncSetImagePath(itemAsset.ItemIconPath)
        self._wtCurrencyImg:AsyncSetImagePath(itemAsset.ItemIconPath)
    end
    
    -- 设置数量
    self.currentKeyCount = Server.CollectionServer:GetCollectionItemsNumById(self._currentLotteryInfo.LotteryKeyId) -- 当前拥有的货币数量
    self._wtTotalNumberTxt:SetText(self.currentKeyCount) -- 设置当前拥有的货币数量

    Server.CurrencyServer:RefreshCurrencyNum(self._currentLotteryInfo.LotteryKeyId, 1, self.currentKeyCount)

    if self.currentCost then
        self._wtCostNumberTxt:SetText(self.currentCost) -- 设置抽奖所消耗的货币数量 
    end
end
function StaffLotteryMainUI:_OnGetBreakChildRecursiveLifeToggle(uiState)
    return true
end

function StaffLotteryMainUI:RefreshRemainingTime()
    if not self._currentLotteryInfo then
        return
    end
    if self._currentLotteryInfo.IsDisplayCountdown == 0 then -- 不显示倒计时
        self._wtCountWidget:Collapsed()
    else
        -- 计算剩余时间的秒数
        local remainingTime = self._currentLotteryInfo.EndTime - Facade.ClockManager:GetLocalTimestamp()
        -- 将剩余时间拆分为天、小时、分钟和秒
        local day = math.floor(remainingTime / (24 * 3600))
        local hour = math.floor((remainingTime % (24 * 3600)) / 3600)
        local min = math.floor((remainingTime % 3600) / 60)
        local sec = remainingTime % 60
        -- 根据规则生成显示字符串
        local timeStr = nil
        if day >= 1 then
            timeStr = TimeUtil.GetSecondsFormatDDHHMMSSString(remainingTime, "DDHH_Second")
        elseif day == 0 and hour >= 1 then
            timeStr = string.format(StoreConfig.Loc.StaffLotteryCountDownHourMin, hour, min)
        elseif day == 0 and hour == 0 then
            timeStr = string.format(StoreConfig.Loc.StaffLotteryCountDownMinSec, min, sec)
        end

        -- self.offlineSeconds = TimeUtil.GetLocalRemainTime2Seconds(self._currentLotteryInfo.EndTime)
        -- local showStr = ""
        -- local day, hour, min, sec = TimeUtil.GetSecondsFormatDDHHMMSS(self.offlineSeconds)
        -- if self.offlineSeconds > 3600 * 24 then
        --     showStr = TimeUtil.GetSecondsFormatDDHHMMSSString(self.offlineSeconds, "DDHH_Second")
        -- elseif self.offlineSeconds > 3600 then
        --     showStr = string.format(StoreConfig.Loc.StoreBuyLimitTimeHourTip, hour, min)
        -- else
        --     showStr = string.format(StoreConfig.Loc.StoreBuyLimitTimeMinSecTip, min, sec)
        -- end

        -- 设置显示字符串
        if timeStr then
            self._wtTextCountDown:SetText(timeStr)
            
            self._wtCountWidget:Visible()
        end
    end
end

function StaffLotteryMainUI:_OnRelayConnected()
    self._wtLotteryOnceBtn:Visible()
end

function StaffLotteryMainUI:RegisterEventListeners()
    self:AddLuaEvent(Server.CollectionServer.Events.evtUpdateCollectionData, self._OnUpdateCollectionData, self)
    self:AddLuaEvent(Server.CollectionServer.Events.evtFetchCollectionData, self._OnUpdateCollectionData, self)
    self:AddLuaEvent(Server.StoreServer.Events.evtStoreMandelLotteryItemBuyChange, self._OnServerLotteryItemBuySuc, self)
    self:AddLuaEvent(Server.StoreServer.Events.evtStoreMandelLotteryItemBuyFailed, self._OnServerLotteryItemBuyFailed, self)
    self:AddLuaEvent(Module.Store.Config.evtStaffLotteryPurchasePopSuccess, self._OnStaffLotteryPurchasePopSuccess, self)
    self:AddLuaEvent(Module.Store.Config.evtStaffLotteryStartPurchase, self._OnStaffLotteryPurchaseStart, self)
    self:AddLuaEvent(Module.Reward.Config.Events.evtCloseRewardPanel, self._OnCloseRewardPanel, self)
    self:AddLuaEvent(Module.Store.Config.evtStaffLotteryVedioPlay, self._OnStaffLotteryVedioPlay, self)

    self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult,self._OnDownloadResult,self)
    self:AddLuaEvent(Facade.ProtoManager.Events.evtOnRelayConnected, self._OnRelayConnected, self) --断线重连
end

function StaffLotteryMainUI:UnRegisterEventListeners()
    self:RemoveLuaEvent(Server.CollectionServer.Events.evtUpdateCollectionData)
    self:RemoveLuaEvent(Server.CollectionServer.Events.evtFetchCollectionData)
    self:RemoveLuaEvent(Server.StoreServer.Events.evtStoreMandelLotteryItemBuyChange)
    self:RemoveLuaEvent(Server.StoreServer.Events.evtStoreMandelLotteryItemBuyFailed)
    self:RemoveLuaEvent(Module.Store.Config.evtStaffLotteryPurchasePopSuccess)
    self:RemoveLuaEvent(Module.Store.Config.evtStaffLotteryStartPurchase)
    self:RemoveLuaEvent(Module.Reward.Config.Events.evtCloseRewardPanel)
    self:RemoveLuaEvent(Module.Store.Config.evtStaffLotteryVedioPlay)
    self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleStateMaybeChanged)
    self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult)
    self:RemoveLuaEvent(Facade.ProtoManager.Events.evtOnRelayConnected, self._OnRelayConnected) --断线重连
end

function StaffLotteryMainUI:_OnCloseRewardPanel()
    self:RefreshUI()
    self:SetKeyInfo()
end

--- 直接触发抽奖流程
function StaffLotteryMainUI:_OnStaffLotteryPurchasePopSuccess(res)
    loginfo("StaffLotteryMainUI:_OnStaffLotteryPurchasePopSuccess")
    -- self:RefreshUI()
    -- self:_OnLotteryOnceBtnClick()
    -- 抽奖
    if res.result == 0 then
        self:HandleLotteryData(res)
    else
        self:ExecuteLottery()
    end
    
end

function StaffLotteryMainUI:_OnStaffLotteryPurchaseStart()
    self._wtLotteryOnceBtn:Collapsed()
    self:BindAnyKeyPress() --屏蔽其他快捷键导致得抽奖流程中断
end

-- 干员抽奖货币购买成功时，刷新UI
function StaffLotteryMainUI:_OnServerLotteryItemBuySuc(dataChange)
    local itemList = {}
    if dataChange then
        if dataChange.prop_changes then
            local prop_changes = dataChange.prop_changes
            for _, propChange in ipairs(prop_changes) do
                if propChange.prop then
                    local item = ItemBase:New(propChange.prop.id, propChange.prop.num, propChange.prop.gid)
                    local weaponFeature = item:GetFeature(EFeatureType.Weapon)
                    if weaponFeature and weaponFeature:IsWeaponSkin() then
                        -- 蓝图需要手动设置一下枪械信息
                        local weaponDescription = WeaponHelperTool.GetWeaponDescriptionFromSkinID(item.id)
                        local propinfo = WeaponAssemblyTool.Desc_To_PropInfo(weaponDescription)
                        item:SetRawPropInfo(propinfo)
                    else
                        item:SetRawPropInfo(propChange.prop)
                    end
                    table.insert(itemList, item)
                end
            end
        end
    end
    -- Module.Reward:OpenRewardPanel(Module.Store.Config.Loc.GetItemWindow, nil, itemList)

    -- 刷新数据与UI
    self.autoClickLottery = true -- 自动抽奖
    self:RefreshData()
    self:RefreshUI()
end

-- 干员抽奖货币购买失败时
function StaffLotteryMainUI:_OnServerLotteryItemBuyFailed(result)

end

-- 抽奖按钮点击事件
function StaffLotteryMainUI:_OnLotteryOnceBtnClick()

    if not self.currentRound then -- 当前轮次需要等服务器回复协议才有值
        return
    end
    if self.PlayingMediaName then -- 正在播放视频的时候无法抽奖，避免同时多次抽奖
        return
    end
    -- 判断是否有足够的抽奖券
    if not self.currentCost or not self.currentKeyCount or self.currentCost > self.currentKeyCount then -- 没有足够的券
    
        local OnBuySuccessCallback = function(res)
            
            local OnBePrepareCallback = function()
                self:_OnStaffLotteryPurchasePopSuccess(res)
            end
           local bisPrepare =  self:_CheckLotteryResourceBePrepare(OnBePrepareCallback)
           if bisPrepare then
                OnBePrepareCallback()
            end
        end

        local OnPurchasePopHide = function(susccessBuyClose)
            if not susccessBuyClose then --未成功购买恢复按钮
                self._wtLotteryOnceBtn:Visible()
            end
        end

        -- 购买弹窗
        Module.Store:OpenStaffLotteryPurchasePop(nil,  self._currentLotteryInfo.LotteryKeyId, self.currentCost - self.currentKeyCount, self._currentLotteryInfo.LotteryId, self.currentRound , OnBuySuccessCallback , OnPurchasePopHide)

        -- Facade.UIManager:AsyncShowUI(UIName2ID.StaffLotteryPurchasePop, nil, nil, self._currentLotteryInfo.LotteryKeyId, self.currentCost - self.currentKeyCount, self._currentLotteryInfo.LotteryId, self.currentRound)


    else
        local bisPrepare =  self:_CheckLotteryResourceBePrepare(CreateCallBack(self.ExecuteLottery,self))
        if bisPrepare then
             self:ExecuteLottery()
         end
    end
end


function StaffLotteryMainUI:HandleLotteryData(res)
    loginfo("StaffLotteryMainUI:HandleLotteryData")
    if res.result == 0 then -- 抽奖成功       
        loginfo("StaffLotteryMainUI:RefreshData() fCSShopGetLotteryInfoRes")
        -- 更新数据
        if res.lottery_pool_info then
            self.lotteryPoolInfoMap[res.lottery_pool_info.lottery_id] = res.lottery_pool_info
            local lotteryInfo = res.lottery_pool_info
            if lotteryInfo then
                if lotteryInfo.lottery_id == self._currentLotteryInfo.LotteryId then
                    self.propNumIds = lotteryInfo.prop_num_ids
                    self.currentRound = self.propNumIds and (#self.propNumIds + 1) or 1 -- 更新当前轮次
                    self.currentCost = lotteryInfo.cost_num -- 下一抽需要消耗的券数量

                    for _, lotteryReward in pairs(lotteryInfo.lottery_rewards) do
                        self.rewardProps[lotteryReward.num_id] = lotteryReward.props -- 一般都是1个，只有台词是多个
                    end
                    Module.Store.Field:SetRewardProps(self.rewardProps)

                    self.lotteryRewardMap = lotteryInfo.lottery_rewards
                    self.lotteryPoolInfoMap[lotteryInfo.lottery_id] = lotteryInfo
                    Module.Store.Field:SetLottertPoolInfoMap(self.lotteryPoolInfoMap)
                end
            end
            -- 收集抽出的奖励信息
            local bBigAward = false -- 是否为大奖
            local dataChange = res.change or {}
            if dataChange and dataChange.prop_changes then
                self.ChangeAddRewardItems = {} -- 仓库没满的奖励
                self.ChangeSendBuyMailArray = {} -- 仓库满的奖励
                for _, propChange in pairs(dataChange.prop_changes) do            
                    if propChange.prop and propChange.change_type == PropChangeType.Add then
                        local rewardItem = ItemBase:New(propChange.prop.id, propChange.prop.num, propChange.prop.gid, false)
                        table.insert(self.ChangeAddRewardItems, rewardItem)
                    elseif propChange.prop and propChange.change_type == PropChangeType.SendBuyMail then
                        table.insert(self.ChangeSendBuyMailArray, {prop = propChange.prop, change_type = propChange.change_type})
                    end      
                    --删除不想显示的
                    if propChange.prop and propChange.change_type == PropChangeType.Del 
                    or (propChange.prop and propChange.change_type == PropChangeType.Modify) 
                    or (propChange.prop and propChange.prop.id == 32210000004) then --临时
                        for key, value in pairs(self.ChangeAddRewardItems) do
                            if propChange.prop.id == value.id then
                                table.remove(self.ChangeAddRewardItems, key)
                            end
                        end
                    end      
                    if self.SpecialItemBigReward and propChange.prop.id == self.SpecialItemBigReward then -- 大奖
                        bBigAward = true
                    end
                end
                
                for _, currencyChange in pairs(dataChange.currency_changes) do
                    if currencyChange.delta > 0 then -- 说明是增加的货币
                        local rewardItem = ItemBase:New(currencyChange.currency_id, currencyChange.delta)
                        table.insert(self.ChangeAddRewardItems, rewardItem)
                    end
                end                   
                
                -- 将所有不是大奖都标记为赠送
                if bBigAward then
                    for _, rewardItem in pairs(self.ChangeAddRewardItems) do
                        if self.SpecialItemBigReward and rewardItem.id ~= self.SpecialItemBigReward then -- 不是大奖的物品
                            rewardItem.bGiveaway = true -- 标记为赠送
                        end
                    end
                end                    
            end
            -- 显示抽奖结果
            local enablePlayFullScreenCG = UDFCommonMediaView.EnableCGMediaPlay()
            if enablePlayFullScreenCG then
                if bBigAward  then -- 说明抽中大奖，播放大奖视频 -- or #self.propNumIds == 8
                    if self.WBP_StaffLottery_MainUI_qiehuan then
                        self.needSkip = false
                        self:PlayAnimation(self.WBP_StaffLottery_MainUI_qiehuan, 0, 1, EUMGSequencePlayMode.Forward, 1, true)
                    end
                    self.MediaNameToPlay = self.videoGreat
                    loginfo("StaffLotteryMainUI:HandleLotteryData: bBigAward = true, self.MediaNameToPlay = "..self.MediaNameToPlay)
                else -- 没有抽中大奖，弹奖励窗口    
                    if self.WBP_StaffLottery_MainUI_qiehuan then
                        self.needSkip = StoreLogic.GetCanPassVideoNormal(self._currentLotteryInfo.LotteryId)
                        self:PlayAnimation(self.WBP_StaffLottery_MainUI_qiehuan, 0, 1, EUMGSequencePlayMode.Forward, 1, true)
                    end         
                    self.MediaNameToPlay = self.videoNormal
                    loginfo("StaffLotteryMainUI:HandleLotteryData: bBigAward = false, self.MediaNameToPlay = "..self.MediaNameToPlay)
                end

                -- local AllCost = Module.Store.Field:GetAllCost(#self.propNumIds)
                -- local Cost = Module.Store.Field:GetCurCost(#self.propNumIds)
                -- local RewardList = Module.Store.Field:GetRewardPropList(self.ChangeAddRewardItems)

                -- LogAnalysisTool.DoRoleSkinLotteryLog(lotteryInfo.lottery_id, 3, 
                -- #self.propNumIds, lotteryInfo.cost_item_id, 
                -- Cost, AllCost, RewardList, bBigAward and 1 or 2)
    
                -- 延迟3秒更新UI，防止玩家提前知晓是否暴击
                Timer.DelayCall(1.0, function()                        
                    -- 刷新数据与UI
                    self:RefreshData()
                    self:RefreshUI()
                end, self)   
            else

                self.MediaNameToPlay = nil
                Module.Reward:ShowNextRewards(true)
                Facade.UIManager:CommitTransition(true)
                Timer.DelayCall(0.2, function()
                    if self.ChangeAddRewardItems and next(self.ChangeAddRewardItems) then
                        Module.Reward:OpenRewardPanel(Module.Store.Config.Loc.GetItemWindow, nil, self.ChangeAddRewardItems, nil, nil, nil, true)
                        --展示奖励结果
                        Server.HeroServer:_LatentUnlockCSHeroEvt()
                    end

                    self:RefreshData()
                    self:RefreshUI()
                    self._wtLotteryOnceBtn:Visible() 
                end, self)
            end
            loginfo("StaffLotteryMainUI:HandleLotteryData: self.MediaNameToPlay = "..tostring(self.MediaNameToPlay)..".enablePlayFullScreenCG = "..tostring(enablePlayFullScreenCG))
        end
    else
        self._wtLotteryOnceBtn:Visible() 
    end
    Module.Reward:EnableNTFCall("Hero", false)
end

function StaffLotteryMainUI:ExecuteLottery()  
    loginfo("StaffLotteryMainUI:ExecuteLottery")
    -- 获取大奖信息
    for _, lotteryProbDistribution in pairs(self.LotteryProbDistributionTable) do
        if lotteryProbDistribution.Is_great_reward == true then -- 大奖
            self.SpecialItemBigReward = lotteryProbDistribution.SpecialItem -- 记录大奖的SpecialItem
        end
      end
    self._wtLotteryOnceBtn:Collapsed()  

    Module.Reward:EnableNTFCall("Hero", false)
    -- 点击购买按钮逻辑
    local lotteryItemInfo = {}
    local needCount = self.currentCost
    local price = self.keyConfig.price * needCount
    local currency_type = self.keyConfig.currency_type
    local currency_type_sub = StoreLogic.GetSubstituteCurrencyItemID()
    local currentCurrencyCount = Module.Currency:GetNumByItemId(currency_type)
    local currencyToPay = 0
    local currencyToPaySub = 0
    
    -- 判断是否有足够的抽奖券
    if not self.currentCost or not self.currentKeyCount or self.currentCost > self.currentKeyCount then -- 没有足够的券
        if currentCurrencyCount >= price then
            currencyToPay = price
            currencyToPaySub = 0
        else
            if currency_type ~= currency_type_sub then
                currencyToPay = currentCurrencyCount
                currencyToPaySub = price - currentCurrencyCount
            end
        end
    end
    

    lotteryItemInfo["item_id"] = self.keyConfig.present_item_id
    lotteryItemInfo["num"] = needCount
    lotteryItemInfo["currency_type"] = currency_type
    if currencyToPay ~= 0 then
        lotteryItemInfo["price"] = currencyToPay
    end
    -- lotteryItemInfo["slippage"] = 1
    lotteryItemInfo["currency_type_substitute"] = currency_type_sub
    if currencyToPaySub ~= 0 then
        lotteryItemInfo["price_substitute"] = currencyToPaySub
    end
    
    local req = pb.CSShopOpenLotteryItemReq:New()
    req.lottery_id = self._currentLotteryInfo.LotteryId
    req.buy_prop = self:GetHasNoKey() and lotteryItemInfo or nil
    req.round = self.currentRound

    self:BindAnyKeyPress() --屏蔽其他快捷键导致得抽奖流程中断
    local executeLottery = function(res)
        self:HandleLotteryData(res)
    end
    req:Request(executeLottery)
end

function StaffLotteryMainUI:GetHasNoKey()
    return not self.currentCost or not self.currentKeyCount or self.currentCost > self.currentKeyCount
end

function StaffLotteryMainUI:AutoPlayVideo()
    -- 视频自动播放规则：只要进商城就会自动播放视频，但是本地商城里自动播放一次之后，就不会再播放，退出商城时会重置，下次进商城再播放

    if not self._mediaComponent or not self._currentLotteryInfo or self._currentLotteryInfo.MoviesAuto == 0 or self.enablePlayFullScreenCG == false then -- 是否需要自动播放视频
        return
    end

    if self._currentLotteryInfo.MoviesRowName and self._currentLotteryInfo.MoviesRowName ~= "" then
        self.needSkip = true
        self:PlayVideoByName(self._currentLotteryInfo.MoviesRowName)
        
        -- 记录已自动播放过
        Server.StoreServer:AddPlayedPV2List(self._currentLotteryInfo.MoviesRowName)
        Module.Store.Field:SetVideoAutoPlay(self._currentLotteryInfo.MoviesRowName)
        -- 埋点        
        LogAnalysisTool.DoSendStoreVideoEventReportLog(3, self._currentLotteryInfo.MoviesRowName, 1)
    end
end

function StaffLotteryMainUI:_OnStaffLotteryVedioPlay()
    if not self.hasPlayed or self.hasPlayed == nil then
        self:AutoPlayVideo()
    end
end

-- 播放视频按钮点击事件
function StaffLotteryMainUI:_OnPlayVideoBtnClick()    
    if not self._mediaComponent or not self._currentLotteryInfo then
        return
    end
    if self._currentLotteryInfo.MoviesRowName and self._currentLotteryInfo.MoviesRowName ~= "" then
        -- self.needSkip = true
        -- if self.WBP_StaffLottery_MainUI_qiehuan then
        --     self:PlayAnimation(self.WBP_StaffLottery_MainUI_qiehuan, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
        -- end
        -- self.MediaNameToPlay = self._currentLotteryInfo.MoviesRowName

        self:PlayVideoByName(self._currentLotteryInfo.MoviesRowName)
        -- 埋点        
        LogAnalysisTool.DoSendStoreVideoEventReportLog(3, self._currentLotteryInfo.MoviesRowName, 2)
    end
end


function StaffLotteryMainUI:PlayVideoByName(videoName, needSkipBtn)    
    loginfo("StaffLotteryMainUI:PlayVideoByName: videoName = "..videoName)
    if not self._currentLotteryInfo then
        return
    end
    
    

    if videoName and videoName ~= "" then
        self.PlayingMediaName = videoName
        -- 如果是全屏播放的视频，调用CommonVideoFullScreenView
        local needProgressBar = false
        local _needSkipBtn = needSkipBtn
        if _needSkipBtn == nil then -- 默认有跳过按钮
            _needSkipBtn = true
        end
        -- 关闭获得界面UI    
        Module.Reward:ShowNextRewards(true)

        -- 屏蔽光标
        if IsHD() then
            WidgetUtil.SetFreeAnalogCursorIsBlocked(self, true, false)
        end
        
        -- 如果是全屏播放的视频，调用CommonVideoFullScreenView
        -- Facade.UIManager:AsyncShowUI(UIName2ID.CommonVideoFullScreenView, nil, nil, self.CurSelectMovies, needProgressBar, needSkipBtn)

        if self.MediaNameToPlay == videoName then --是获得演绎
            local isDownLoaded =  LiteDownloadManager:GetModuleStateByModuleName(self.MediaNameToPlay) == 1
            if not isDownLoaded then
                self:OnMediaPlayEnd() --不播放视频直接通知奖励
                loginfo("StaffLotteryMainUI:PlayVideoByName: MediaNameToPlay is not downloaded")
                return
            end
            loginfo("StaffLotteryMainUI:PlayVideoByName:  MediaNameToPlay = ".. self.MediaNameToPlay .. "downloaded = ".. tostring(isDownLoaded))
        end

        local fOnMediaPlayEnd = function()
            if not isvalid(self) then
                return
            end
            self:UnbindAnyKeyPress() --恢复其他快捷键
            self:OnMediaPlayEnd()
        end

        Module.CommonWidget:ShowFullScreenVideoView(videoName, needProgressBar, _needSkipBtn, fOnMediaPlayEnd, self, 1, 0)
    end
end


--- 视频播放结束之后的回调
function StaffLotteryMainUI:OnMediaPlayEnd()

    if self.PlayingMediaName == self.videoGreat or self.PlayingMediaName == self.videoNormal then -- 抽奖视频播放完毕，展示奖励结果
        -- 判断是否为抽中大奖之后播放的视频，需要显示奖励弹窗                           
        if self.ChangeAddRewardItems and next(self.ChangeAddRewardItems) then -- 仓库没满，播放奖励弹窗 
            Facade.UIManager:CommitTransition(true)
            Module.Reward:OpenRewardPanel(Module.Store.Config.Loc.GetItemWindow, nil, self.ChangeAddRewardItems, nil, nil, nil, true)
            -- 通知抽奖后的视频播放完毕        
            Module.Store.Config.evtStoreMediaPlayEnd:Invoke()
            --展示奖励结果
            Server.HeroServer:_LatentUnlockCSHeroEvt()
        end
        if self.ChangeSendBuyMailArray and next(self.ChangeSendBuyMailArray) then -- 仓库满了，发弹窗提醒已发到邮箱
            LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.RewardInventoryIsFull) -- "仓库无足够空间，物品已通过邮件送达，请注意查收"
        end    
        -- 清空奖励数据
        self.ChangeAddRewardItems = {}
        self.ChangeSendBuyMailArray = {}
    else
        -- -- 播放退出音乐
        -- Facade.SoundManager:StopUIAudioEvent(Module.Store:GetLotteryMusic(self._currentLotteryInfo.LotteryId))
        -- --重新播放主题音乐
        -- Facade.SoundManager:PlayUIAudioEvent(Module.Store:GetLotteryMusic(self._currentLotteryInfo.LotteryId))
    end

    self._wtLotteryOnceBtn:Visible()  
    -- 更新数据
    self:RefreshData()
    -- 刷新UI
    self:RefreshUI()
    -- 恢复显示
    self:RecoverRenderOpacity()
    -- UI显隐
    self._wtDFCanvasPanel:Visible()
    self._wtPlatformPaddingBox0:Visible()    
    self._wtPlatformPaddingBox2:Visible()    
    self._wtPlatformPaddingBox1:Visible()
    self._wtSkipBtn:Collapsed()
    self._mediaComponent:Collapsed()   
    self._mediaComponent:Stop() 

    -- 恢复虚拟光标
    if IsHD() then
        WidgetUtil.SetFreeAnalogCursorIsBlocked(self, false) 
    end

    local allLayers = {}
    for k, v in pairs(EUILayer) do
        table.insert(allLayers, v)
    end
     Facade.UIManager:LayersOnly(allLayers, ELayerRuleChangeReason.BusinessPending)


    -- 记录视频播放完毕
    self.PlayingMediaName = nil
end

-- 字符串转时间戳
function StaffLotteryMainUI:TimeStringToTimeStamp(timeString)
    local year, month, day, hour, minute, second = string.match(timeString, "(%d+)-(%d+)-(%d+) (%d+):(%d+):(%d+)")
    local timeStamp = os.time({year = year, month = month, day = day, hour = hour, min = minute, sec = second})
    return timeStamp
end


-- 奖池预览按钮点击事件
function StaffLotteryMainUI:_OnPrizePoolPreviewBtnClick()
    if self.lotteryPoolInfoMap == {} then
        for _, shopLotteryInfo in pairs(Server.StoreServer:GetLotteryPoolInfo()) do
            if shopLotteryInfo then
                self.lotteryPoolInfoMap[shopLotteryInfo.lottery_id] = shopLotteryInfo
            end
        end
    end
    
    if self._currentLotteryInfo == nil or self.lotteryPoolInfoMap == nil then
        return
    end
    Module.Store:SetCanPlayMusic(false)
    Module.Store:SetIsToPricePool(true)
    self.LotteryProbDistributionTable = Server.StoreServer:GetDictStoreLotteryProbDistributionItemsByLotteryID(self._currentLotteryInfo.LotteryId)
    Facade.UIManager:AsyncShowUI(UIName2ID.StaffLotteryPreview, nil, nil, self._currentLotteryInfo.LotteryId, self.lotteryPoolInfoMap[self._currentLotteryInfo.LotteryId], nil, self.IsCollaboration)
    -- 埋点
    LogAnalysisTool.SignButtonClicked(10154002)
    self._toPrizePoolUI = true

end

function StaffLotteryMainUI:OnDetailCheckBtnClick()
    -- if not self._currentLotteryInfo or not self.lotteryPoolInfoMap or not self.lotteryPoolInfoMap[self._currentLotteryInfo.LotteryId] then
    --     return
    -- end
    Facade.UIManager:AsyncShowUI(UIName2ID.StaffLotteryPop, nil, nil, self.lotteryPoolInfoMap[self._currentLotteryInfo.LotteryId])
    -- 埋点    
    LogAnalysisTool.SignButtonClicked(10154001)
end

-- 跳过视频播放按钮事件
function StaffLotteryMainUI:OnSkipBtnClicked()
    if not self._mediaComponent then
        return
    end

    self._mediaComponent:Stop()
    self._mediaComponent:Collapsed()

    -- 埋点        
    LogAnalysisTool.DoSendStoreVideoEventReportLog(3, self._currentLotteryInfo.MoviesRowName, 3)
end

function StaffLotteryMainUI:_OnUpdateCollectionData()
    -- 刷新数据与UI
    self:RefreshData()
    self:RefreshUI()
    self:SetKeyInfo()
end

function StaffLotteryMainUI:ReportRedPoint()
    if self._currentLotteryInfo ~= nil then
        Server.StoreServer:CheckAndSetThirdTabReddotByGroupID(3, self._currentLotteryInfo.LotteryId, self.IsCollaboration)
    end
end

--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
function StaffLotteryMainUI:_EnableGamepadFeature()
    if not IsHD() then
        return 
    end

    -- 切换页签的时候也会触发onshowbegin，要确保visible的时候才初始化手柄功能
    if not self:IsVisible() then
        return
    end

    --配置keyIcon
    if self._wtLotteryOnceBtn then
        --配置长按
        if not self._LotteryLongPress then
            self._LotteryLongPress = self:AddInputActionBinding(
            "MallLottery",
            EInputEvent.IE_Pressed,
            self._OnLotteryOnceBtnClick,
            self,
            EDisplayInputActionPriority.UI_Stack
            )
        end 
        self._wtLotteryOnceBtn:SetDisplayInputActionWithLongPress(self._LotteryLongPress, self, "MallLottery", true, nil, true)
    end

    if self._wtPrizePoolPreviewBtn then
        self._wtPrizePoolPreviewBtn:SetDisplayInputAction("MallCadreDetails", true, nil, true)
    end

    --配置输入
    if not self._CadreDetails then
        self._CadreDetails = self:AddInputActionBinding(
        "MallCadreDetails", 
        EInputEvent.IE_Pressed, 
        self._OnPrizePoolPreviewBtnClick,
        self, 
        EDisplayInputActionPriority.UI_Stack
        )  
    end
end

function StaffLotteryMainUI:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end

    -- 移除输入
    if self._CadreDetails then
        self:RemoveInputActionBinding(self._CadreDetails)
        self._CadreDetails= nil
    end

    if self._LotteryLongPress then
        self:RemoveInputActionBinding(self._LotteryLongPress)
        self._LotteryLongPress =  nil
    end
    --移除按键提示
    Module.CommonBar:RecoverBottomBarInputSummaryList()
end

function StaffLotteryMainUI:_SetBottombar()
    if not IsHD() then
        return 
    end

    --显示按键提示
    local summaryList ={}
    if self._wtPlayVideoBtn and self._wtPlayVideoBtn:IsVisible() then
        table.insert(summaryList, {actionName = "MallDemo", func = self._OnPlayVideoBtnClick, caller = self, bUIOnly = false, bHideIcon = false}) 
    end
    table.insert(summaryList, {actionName = "ProbabilityForStore",func = self.OnDetailCheckBtnClick, caller = self ,bUIOnly = false, bHideIcon = false})   
    Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, false, true)
end
--- END MODIFICATION


function StaffLotteryMainUI:_RefreshCommonDownload()
    self._wtCommonDownload:Collapsed()
    if not self._currentLotteryInfo then
        logerror("StaffLotteryMainUI:_RefreshCommonDownload: _currentLotteryInfo is nil")
        return
    end

    self._videoPakModuleName = "Runtime"..tostring(self._currentLotteryInfo.LotteryId) .. "_VideoPakName"
    local multiModuleName = self._videoPakModuleName
    local childModules = {self.videoGreat, self.videoNormal}

    if #childModules == 0 then
        logerror("StaffLotteryMainUI:_RefreshCommonDownload: childModules is empty")
        return
    end

    local bIsRegistered =  LiteDownloadManager:GetRegisterRuntimeChildModules(multiModuleName)
    if not bIsRegistered then
        LiteDownloadManager:RegisterRuntimeMultiModuleByChildModule(multiModuleName, childModules, true)
    end

    local bDownloaded =  LiteDownloadManager:GetModuleStateByModuleName(multiModuleName) == 1
    
    local isDownloading = LiteDownloadManager:IsDownloadingByModuleName(multiModuleName)
    if not bDownloaded and not isDownloading then
        LiteDownloadManager:DownloadByModuleName(multiModuleName)
    end

    

    loginfo("[StaffLotteryMainUI:_RefreshCommonDownload] multiModuleName = " .. multiModuleName .. ", bDownloaded = " .. tostring(bDownloaded) .. ", isDownloading = " .. tostring(isDownloading))
end

function StaffLotteryMainUI:_OnDownloadResult(ModuleKey, bDownloaded, errorCode)
    loginfo("[StaffLotteryMainUI:_OnDownloadResult] ModuleKey = " .. ModuleKey .. ", bDownloaded = " .. tostring(bDownloaded).."self._videoPakModuleName = "..tostring(self._videoPakModuleName))

    if ModuleKey == self._videoPakModuleName then
        if bDownloaded then
            self:_DisplayDownloadWidgets(false)
            if self._fOnVideoPakDownloadedCallback then
                self._fOnVideoPakDownloadedCallback()
                self._fOnVideoPakDownloadedCallback = nil
            end
        end
    end
end

function StaffLotteryMainUI:OnSkipDownloadBtnClicked()
    self:_DisplayDownloadWidgets(false)
    if self._fOnVideoPakDownloadedCallback then
        self._fOnVideoPakDownloadedCallback()
        self._fOnVideoPakDownloadedCallback = nil
    end
end

function StaffLotteryMainUI:_DisplayDownloadWidgets(inDisplay)
    loginfo("StaffLotteryMainUI:_DisplayDownloadWidgets: inDisplay = "..tostring(inDisplay)..", self._videoPakModuleName = "..tostring(self._videoPakModuleName))
    if inDisplay then
        self._wtCommonDownload:InitModuleKey(self._videoPakModuleName)
        self._wtCommonDownload:Visible()
        self._wtSkipDownloadBtn:Visible()
    else
        self._wtCommonDownload:Collapsed()
        self._wtSkipDownloadBtn:Collapsed()
    end
end


---检查抽奖前置资源是否准备好
---@param fOnPrepared function 准备完成后的回调函数
---@return boolean 是否准备好
function StaffLotteryMainUI:_CheckLotteryResourceBePrepare(fOnPrepared)
    local isPrepare = false
    if self._videoPakModuleName then
        local isDownLoaded =  LiteDownloadManager:GetModuleStateByModuleName(self._videoPakModuleName) == 1
        if not isDownLoaded then
            local isDownloading = LiteDownloadManager:IsDownloadingByModuleName(self._videoPakModuleName)
            self:_DisplayDownloadWidgets(true)

            if not isDownloading then
                LiteDownloadManager:DownloadByModuleName(self._videoPakModuleName)
            end

            self._fOnVideoPakDownloadedCallback = fOnPrepared
        end

        isPrepare = isDownLoaded
    end

    return isPrepare
end


function StaffLotteryMainUI:BindAnyKeyPress()
    if self._anyKeyPressHandle then
        self:RemoveInputActionBinding(self._anyKeyPressHandle)
        self._anyKeyPressHandle = nil
    end
    self._anyKeyPressHandle = self:AddInputActionBinding(
        "AnyKey",
        EInputEvent.IE_Pressed,
        self.OnAnyKeyPressed,
        self,
        EDisplayInputActionPriority.UI_Pop
    )

    if IsHD() then
        if not self._GamepadAnyKey_Axis then
            self._GamepadAnyKey_Axis = self:AddAxisInputActionBinding("AnyKey_Axis", self.OnAnyKeyPressed, self, EDisplayInputActionPriority.UI_Pop)
        end
    end

    loginfo("StaffLotteryMainUI:BindAnyKeyPress")
end

function StaffLotteryMainUI:UnbindAnyKeyPress()
    if self._anyKeyPressHandle then
        self:RemoveInputActionBinding(self._anyKeyPressHandle)
        self._anyKeyPressHandle = nil
    end

    if IsHD() then
        if self._GamepadAnyKey_Axis then
            self:RemoveInputActionBinding(self._GamepadAnyKey_Axis)
            self._GamepadAnyKey_Axis = nil
        end
    end

    loginfo("StaffLotteryMainUI:UnbindAnyKeyPress")
end

--获取兄弟页签的数量
function StaffLotteryMainUI:GetSiblingTabCount()
    self.mainTabTable = Server.StoreServer:GetMainTabTable()
    self.subTabTable = Server.StoreServer:GetSubTabTable()
    local tab1 = Module.Store.Field:GetNowTabID()
    local foundTabRowData = nil
    local slibingCount = 0

    for k, mainTab in pairs(self.mainTabTable) do
        local runtimeTabIndex =Server.StoreServer:GetTabCnt(EStoreTab[mainTab.TabID])
        if runtimeTabIndex == tab1 then
            foundTabRowData = mainTab
            break
        end
    end

    if not foundTabRowData then
        return -1
    end
 
    slibingCount = Server.StoreServer:GetTabNameToSubTabCnt(EStoreTab[foundTabRowData.TabID])
    
    return slibingCount

end

function StaffLotteryMainUI:StopAllAnim()
    self:StopAnimation(self.WBP_StaffLottery_MainUI_in)
    self:StopAnimation(self.WBP_StaffLottery_MainUI_out)
    self:StopAnimation(self.WBP_StaffLottery_MainUI_qiehuan)
    self:StopAnimation(self.WBP_StaffLottery_MainUI_qiehuan2)
    self:StopAnimation(self.WBP_StaffLottery_MainUI_zhuan01)
    self:StopAnimation(self.WBP_StaffLottery_MainUI_zhuan02)
end

return StaffLotteryMainUI
