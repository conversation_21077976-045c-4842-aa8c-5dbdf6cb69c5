----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRange)
----- LOG FUNCTION AUTO GENERATE END -----------



local ASafehouseRangeInteractor = require "DFM.Business.Module.RangeModule.Gameplay.ASafehouseRangeInteractor"
local RangeLogic = require "DFM.Business.Module.RangeModule.Logic.RangeLogic"

local UDFMGameplayGlobalDelegates = import "DFMGameplayGlobalDelegates"
local USafeHouseRangeUtil = import "SafeHouseRangeUtil"

---@class ASafeHouseRangeAssembleEntrance : ASafehouseRangeInteractor
local ASafeHouseRangeAssembleEntrance = class("ASafeHouseRangeAssembleEntrance", ASafehouseRangeInteractor)

local function log(...)
    loginfo("[ASafeHouseRangeAssembleEntrance]", ...)
end

function ASafeHouseRangeAssembleEntrance:Ctor()
    log("Ctor")

    self.bEntranceShowed = false
end

function ASafeHouseRangeAssembleEntrance:TriggerStart()
    UDFMGameplayGlobalDelegates.Get(GetGameInstance()).OnNotifyActiveWeaponChanged:Add(self._OnActiveWeaponChanged, self)

    if RangeLogic.ShouldShowGunsmithEntrance() then
        self.bEntranceShowed = true
        Module.IrisSafeHouse.Field:AddOperator(self, Module.IrisSafeHouse.Config.EInteractorType.RangeAssembleEntrace, 100, 100)
    end
end

function ASafeHouseRangeAssembleEntrance:TriggerStop()
    UDFMGameplayGlobalDelegates.Get(GetGameInstance()).OnNotifyActiveWeaponChanged:Remove(self._OnActiveWeaponChanged, self)

    if self.bEntranceShowed then
        self.bEntranceShowed = false
        Module.IrisSafeHouse.Field:RemoveOperator(self)
    end
end

local function fEnterGunsmith()
    Module.Range:EnterGunsmith()
end

function ASafeHouseRangeAssembleEntrance:StartInteract()
    if RangeLogic.ShouldShowGunsmithEntrance() then
        local dfmCharacter = Facade.GameFlowManager:GetCharacter()
        if USafeHouseRangeUtil.IsZooming(dfmCharacter) then
            log("ASafeHouseRangeAssembleEntrance:StartInteract delay enter")
            USafeHouseRangeUtil.CancelWeaponZoom(dfmCharacter, true)
            Timer.DelayCall(0, fEnterGunsmith)
        else
            log("ASafeHouseRangeAssembleEntrance:StartInteract directly enter")
            fEnterGunsmith()
        end
    end
end

function ASafeHouseRangeAssembleEntrance:_OnActiveWeaponChanged()
    if self.bEntranceShowed and not RangeLogic.ShouldShowGunsmithEntrance() then
        self.bEntranceShowed = false
        Module.IrisSafeHouse.Field:RemoveOperator(self)
    elseif not self.bEntranceShowed and RangeLogic.ShouldShowGunsmithEntrance() then
        self.bEntranceShowed = true
        Module.IrisSafeHouse.Field:AddOperator(self, Module.IrisSafeHouse.Config.EInteractorType.RangeAssembleEntrace, 100, 100)
    end
end

return ASafeHouseRangeAssembleEntrance