----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMQuest)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class QuestUnlockLinePanel : LuaUIBaseView
local QuestUnlockLinePanel = ui("QuestUnlockLinePanel")

function QuestUnlockLinePanel:Ctor()
    self._wtDFTextBlock_Chapter = self:Wnd("DFTextBlock_Chapter", UITextBlock)
    self._wtDFScrollBox_Unlock = self:Wnd("DFVerticalBox_176", UIWidgetBase)
end

------------------------------------ Override function ------------------------------------
function QuestUnlockLinePanel:OnInitExtraData()
end

function QuestUnlockLinePanel:OnOpen()
    -- print( "xhz >>>>>>>>>>>>> QuestUnlockLinePanel OnOpen >>>>>>>>>>>>>>>> " )
end

function QuestUnlockLinePanel:OnClose()
    self:PlayAnimation(self.WBP_TaskChapter_UnlockTopBar_out, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    Facade.UIManager:ClearSubUIByParent(self, self._wtDFScrollBox_Unlock)
end

function QuestUnlockLinePanel:OnShow()
end

function QuestUnlockLinePanel:OnHide()
end


function QuestUnlockLinePanel:_UpdateQuestInfo(lineInfo, questInfos, bIsSeason)

    self._questInfos = questInfos
    
    if bIsSeason then
        self._wtDFTextBlock_Chapter:SetText(Module.Quest.Config.Loc.QuestSeason)
        
        if self._questInfos then
            self:PlayAnimation(self.WBP_TaskChapter_UnlockTopBar_in, 0, 1, EUMGSequencePlayMode.Forward, 1, true)
            Facade.UIManager:RemoveSubUIByParent(self, self._wtDFScrollBox_Unlock)
            for idx, questInfo in ipairs(self._questInfos) do
                Facade.UIManager:AddSubUI(self, UIName2ID.QuestUnlockItem, self._wtDFScrollBox_Unlock, nil, questInfo, idx)
            end
        end

    else
        if lineInfo == nil or questInfos == nil then
            logerror(" QuestUnlockLinePanel OnInitExtraData : questInfo is nil ")
            return
        end
        self._lineInfo = lineInfo

        self._wtDFTextBlock_Chapter:SetText(self._lineInfo.lineName)
        if self._questInfos then
            self:PlayAnimation(self.WBP_TaskChapter_UnlockTopBar_in, 0, 1, EUMGSequencePlayMode.Forward, 1, true)
            Facade.UIManager:RemoveSubUIByParent(self, self._wtDFScrollBox_Unlock)
            for idx, questInfo in ipairs(self._questInfos) do
                Facade.UIManager:AddSubUI(self, UIName2ID.QuestUnlockItem, self._wtDFScrollBox_Unlock, nil, questInfo, idx)
            end
        end
    end


    
end

return QuestUnlockLinePanel
