----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMWeaponAssembly)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class ActivityUniversalExchangeTemplateItem : LuaUIBaseView
local ActivityUniversalExchangeTemplateItem = ui("ActivityUniversalExchangeTemplateItem")
local IVCommonItemTemplate = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.IVCommonItemTemplate"
local ItemLabelMarkBtn = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.ItemLabelMarkBtn"
local ActivityLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"
local Config = Module.Activity.Config

function ActivityUniversalExchangeTemplateItem:Ctor()
    self._wtIconImg      = self:Wnd("wtItemIcon", UIWidgetBase):Wnd("wtMainIcon", UIImage)--图标
    self._wtFCDNPanel    = self:Wnd("DFScaleBox_0", UIWidgetBase)
    self._wtFCDNImg      = self:Wnd("DFCDNImage_59", DFCDNImage)--cdn图标
    self._wtImgMask      = self:Wnd("PlatformSizeBox_2", UIWidgetBase)--mask
    self._wtSwitchImg    = self:Wnd("DFImage_1", UIImage)--切换图片
    self._wtFrameImg     = self:Wnd("WBP_CommonHover", UIImage)--悬浮白框
    self._wtIconTxt      = self:Wnd("DFTextBlock_30", UITextBlock)--图标名称
    self._wtLimitTxt     = self:Wnd("DFTextBlock_214", UITextBlock)--兑换上限(描述)
    self._wtFNumTxt      = self:Wnd("DFTextBlock_110", UITextBlock)--兑换数量
    self._wtDFMMask      = self:Wnd("Mask", UIWidgetBase)--提示遮罩base
    self._wtFTipsTxt     = self:Wnd("DFRichTextBlock_41", UITextBlock)--提示
    self._wtMarkPanel    = self:Wnd("WBP_DFCommonCheckcollection", UIWidgetBase)
    self._wtMarkBtn      = self._wtMarkPanel:Wnd("wLabelMarkCheckBox", UICheckBox)--收藏按钮
    self._wtMarkBtn:Event("OnCheckStateChanged", self._OnMarkBtnClick, self)
    self._wtMarkRedoot   = self:Wnd("DFCanvasPanel_0", UIWidgetBase)--红点widget
    self._wtHoveredBtn   = self:Wnd("DFButton_0", UIButton)--悬浮按钮
    self._wtSwitchBtn    = self:Wnd("DFButton_60", UIButton)--切换按钮
    self._wtExchangeBtn1 = self:Wnd("WBP_DFCommonButtonV2S2", DFCommonButtonOnly)--兑换按钮(黄色)
    self._wtExchangeBtn2 = self:Wnd("WBP_DFCommonButtonV2S2_62", DFCommonButtonOnly)--兑换按钮(白色)
    ActivityLogic.AddBtnOnClicked(self, self._wtHoveredBtn,   EActSubInteractive.Next1, self._OnBtnClicked)
    ActivityLogic.AddBtnOnClicked(self, self._wtSwitchBtn,    EActSubInteractive.Next2, self._OnBtnClicked)
    ActivityLogic.AddBtnOnClicked(self, self._wtExchangeBtn1, EActSubInteractive.Next3, self._OnBtnClicked)
    ActivityLogic.AddBtnOnClicked(self, self._wtExchangeBtn2, EActSubInteractive.Next4, self._OnBtnClicked)
    self._wtWaterfallBox = UIUtil.WndWaterfallScrollBox(self, "DFScrollBox_86", self._OnWaterfallCount, self._OnWaterfallWidget)--兑换物品列表
    --动态新增EmptyItem
    self._wtItemPanel    = self:Wnd("CanvasPanel_0", UIWidgetBase)
    self._wtEmptyPanel   = self:Wnd("Empty", UIWidgetBase)
    self._wtEmptyBox     = self:Wnd("EmptySlot", UIWidgetBase)
    ActivityLogic.SetZOrder(self._wtHoveredBtn, 0)
end

function ActivityUniversalExchangeTemplateItem:OnInitExtraData(activityID)
    self._activityID = activityID
end

function ActivityUniversalExchangeTemplateItem:OnOpen()
end

function ActivityUniversalExchangeTemplateItem:OnShowBegin()
    self:_AddEventListener()
    self:_AddMouseMove(true)
end

function ActivityUniversalExchangeTemplateItem:OnHideBegin()
    self:RemoveAllLuaEvent()
    self:_AddMouseMove()
    ActivityLogic.AddTimer(self)
    ActivityLogic.AddReddot(self, self._wtMarkRedoot)
    Module.ItemDetail:CloseItemDetailPanel()
end

function ActivityUniversalExchangeTemplateItem:OnClose()
    ActivityLogic.AddTimer(self)
    self:_AddEmptyItem()
end

--------------------------------------------------数据初始化与界面刷新--------------------------------------------------
function ActivityUniversalExchangeTemplateItem:InitData(activityID, index, data)
    --转接控制器
    self:_IsRefreshIcon(activityID, index, data, true)
end

function ActivityUniversalExchangeTemplateItem:_IsRefreshIcon(activityID, index, data, isRefreshIcon)
    self._activityID = activityID
    self._index = index
    self._data = data
    self._isRefreshIcon = isRefreshIcon
    --开始初始化数据
    if data == nil then
        return
    end
    --判断是否是空状态
    if self:_IsJudge() then
        return
    end
    self:SetStyle(data.itemType == 1 and 0 or 1)
    self:_CreateItemIcon()--创建图标信息
    --刷新其他数据
    self:_SetBtnInformation()
    --直接启动定时器
    ActivityLogic.AddTimer(self, EActSubInteractive.Next1, self._SetPromptInformation)
end

function ActivityUniversalExchangeTemplateItem:_CreateItemIcon()
    if self._data == nil then
        return
    end
    local itemData = ActivityLogic.GetWeaponSkinItem({id = self._data.id, num = self._data.num})
    if itemData then
        local iconType = self._data.url and string.len(self._data.url) > 0
        if iconType then
            ActivityLogic.SetImgByPath(self._wtFCDNImg, self._data.url)
        else
            ActivityLogic.SetImgByPath(self._wtIconImg, itemData)
        end
        loginfo("ssy item data of icon name is ", itemData.name)
        --mask
        ActivityLogic.SetWidgetVisible(self._wtImgMask, iconType and self._data.itemType ~= 1)
        --icon
        ActivityLogic.SetWidgetVisible(self._wtIconImg, not iconType)
        ActivityLogic.SetWidgetVisible(self._wtFCDNPanel, iconType)--CDN有异步显示特性
        --name and num
        ActivityLogic.SetTextBlock(self._wtIconTxt, itemData.name)
        if itemData.num and itemData.num > 1 then
            ActivityLogic.SetTextBlock(self._wtFNumTxt, itemData.num)
        else
            ActivityLogic.SetTextBlock(self._wtFNumTxt)
        end
    end
end

function ActivityUniversalExchangeTemplateItem:_SetPromptInformation()
    --判断兑换条件
    local btnState, time, timeStr, btnStr = self:_IsCanExchange()
    --是否最后一批活动
    if btnState == 5 then
        self:_SetTimeTips(btnState, ActivityLogic.GetTimeStr(time), timeStr)
    elseif ActivityLogic.IsTimeExpired(time) then
        ActivityLogic.AddTimer(self)
        self:_SetTimeTips(btnState, nil, timeStr)
        self:_SetBtnInformation()
        --模拟数据
        local data = self._data
        if data and not ActivityLogic.IsHave(data.maxNum, data.minNum) then
            if data.refreshTime > 0 and data.internal > 0 then
                Server.ActivityServer:SendSimulateTime(self._activityID, data.exchangeId)
            end
        end
    else
        self:_SetTimeTips(btnState, ActivityLogic.GetTimeStr(time), timeStr)
    end
end

function ActivityUniversalExchangeTemplateItem:_SetBtnInformation()
    local data = self._data
    if data == nil then
        return
    end
    --判断兑换条件
    local btnState, time, timeStr, btnStr = self:_IsCanExchange()

    --收藏处理
    if btnState == 5 then
        --已兑换不显示收藏
        self:_SetReddot(false)
        ActivityLogic.SetWidgetVisible(self._wtMarkPanel, false)
    else
        self:_SetReddot(self:_IsCurCollect())
        ActivityLogic.SetWidgetVisible(self._wtMarkPanel, true, true)
    end

    --判断兑换次数是否足够
    local isHave, num = ActivityLogic.IsHave(data.maxNum, data.minNum)

    if data.url and string.len(data.url) > 0 or btnState >= 3 or not isHave then
        ActivityLogic.SetTextBlock(self._wtLimitTxt)
    else
        local arr = {
            {txt = data.refreshDesc},
            {txt = " "},
            {txt = num},
        }
        ActivityLogic.SetTextBlock(self._wtLimitTxt, ActivityLogic.GetTextColor(arr))
    end

    --兑换按钮状态/显隐和文本显示
    self:_SetExchangeBtn(btnState)
    ActivityLogic.SetTextBlock(self._wtExchangeBtn1, btnStr)
    ActivityLogic.SetTextBlock(self._wtExchangeBtn2, btnStr)

    --切换按钮
    if data.itemType ~= 1 and data.list then
        local isSwitch = btnState <= 3 and #data.list > 1
        ActivityLogic.SetWidgetVisible(self._wtSwitchBtn, isSwitch, true)
    end

    --获取道具
    local list, redottList = self:_GetCurGroupProp()
    self._list = list
    --按钮状态
    self._btnState = btnState
    --刷新道具组
    if data.itemType ~= 1 then
        --可兑换切换按钮颜色控制
        local isColor = false
        for index, value in ipairs(redottList or {}) do
            if value ~= list then
                isColor = true
                break
            end
        end
        local color = Facade.ColorManager:GetLinerColorByRowName("C001")
        if isColor then
            color = Facade.ColorManager:GetLinerColorByRowName("C006")
        end
        self._wtSwitchImg:SetColorAndOpacity(color)

        if self._isRefreshIcon then
            self._wtWaterfallBox:RefreshAllItems()
        end
    end
end

function ActivityUniversalExchangeTemplateItem:_SetExchangeBtn(btnState)
    if btnState then
        ActivityLogic.SetWidgetVisible(self._wtExchangeBtn1, btnState == 1, true)
        ActivityLogic.SetWidgetVisible(self._wtExchangeBtn2, btnState ~= 1, true)
        local btn = btnState == 1 and self._wtExchangeBtn1 or self._wtExchangeBtn2
        if btnState <= 2 then
            ActivityLogic.SetCommonBtn(btn, true)
        elseif btnState == 3 then
            ActivityLogic.SetCommonBtn(btn, false)
        elseif btnState >= 4 then
            ActivityLogic.SetCommonBtn(btn)
        else
            ActivityLogic.SetCommonBtn(btn)
        end
    end
end

--获取选中的道具组
function ActivityUniversalExchangeTemplateItem:_GetCurGroupProp()
    local data = self._data
    if data == nil then
        return {}
    end
    local plan_id = data.selected_plan
    if plan_id == 0 then
        if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse then
            plan_id = data.sol_default_plan
        else
            plan_id = data.mp_default_plan
        end
    end
    local list = {}
    local redootList = {}
    for index, plan in ipairs(data.list or {}) do
        if plan_id == 0 then
            list = plan.list
        elseif plan.planId == plan_id then
            list = plan.list
        end
        local isReddot = Server.ActivityServer:IsCanExchange(plan.list, 1)
        if isReddot then
            table.insert(redootList, plan.list)
        end
    end
    return list, redootList
end

--获取是否收藏
function ActivityUniversalExchangeTemplateItem:_IsCurCollect()
    local data = self._data
    if data == nil then
        return false
    end
    return data.isFocus
end

--- comment 判断按钮状态
function ActivityUniversalExchangeTemplateItem:_IsCanExchange()
    local data = self._data

    --按钮状态说明(1：可兑换，2：不可兑换，3：等待刷新，4：未开放，5：已兑换)
    local btnState, time, timeStr, btnStr = 0, 0, nil, nil
    if data == nil then
        return btnState, time, timeStr, btnStr
    end
    --判断货币是否足够
    if data.itemType == 1 then
        local isBool =  Server.ActivityServer:IsCanExchange(data.currencyId, data.currencyNum)
        local color = nil
        if isBool then
            btnState = 1
        else
            color = 1
            btnState = 2
        end
        btnStr = ActivityLogic.GetCurrencyStr(data.currencyId, data.currencyNum, color)
    else
        local isBool =  Server.ActivityServer:IsCanExchange(self:_GetCurGroupProp(), 1)
        if isBool then
            btnState = 1
        else
            btnState = 2
        end
        btnStr = Config.Loc.ExchangeTitle
    end
    --判断兑换次数是否足够
    if ActivityLogic.IsTimeExpired(data.opennessTime) then
        --兑换次数是否上限
        if ActivityLogic.IsHave(data.maxNum, data.minNum) then
            --如果还可以兑换，这不用处理

        elseif ActivityLogic.IsHave(data.actEndTime, data.refreshTime) then
            if ActivityLogic.IsTimeExpired(data.refreshTime) then
                btnState = 5
                timeStr = Config.Loc.AlreadyRedeemed
            else
                btnState = 3
                time = data.refreshTime
                timeStr = Config.Loc.PostUpdateListing
            end
        else
            btnState = 5
            timeStr = Config.Loc.AlreadyRedeemed
        end
    else
        btnState = 4
        time = data.opennessTime
        timeStr = Config.Loc.LaterOpen
        btnStr = Config.Loc.ToBeOpened
    end
    return btnState, time, timeStr, btnStr
end

function ActivityUniversalExchangeTemplateItem:_SetTimeTips(btnState, time, timeStr)
    if timeStr then
        local str = nil
        if btnState == 4 or btnState == 3 then
            str = General.GetText(timeStr, time)
        else
            str = timeStr
        end
        ActivityLogic.SetTextBlock(self._wtFTipsTxt, str)
        ActivityLogic.SetUIVisible(self._wtDFMMask, true)
    else
        ActivityLogic.SetUIVisible(self._wtDFMMask, false)
    end
end

function ActivityUniversalExchangeTemplateItem:_OnBtnClicked(index, state)
    local data = self._data
    if data == nil then
        return
    end
    local itemData = ActivityLogic.GetWeaponSkinItem({id = data.id})
    if itemData == nil then
        return
    end
    --判断兑换条件
    local btnState, time, timeStr, btnStr = self:_IsCanExchange()

    if index == EActSubInteractive.Next1 then
        if state == 2 then
            ActivityLogic.ShowItemDetail3D(itemData, self._wtHoveredBtn)
        end
        if state == 1 then
            ActivityLogic.SetWidgetVisible(self._wtFrameImg, true)
        elseif state == 0 then
            ActivityLogic.SetWidgetVisible(self._wtFrameImg, false)
        end
    elseif index == EActSubInteractive.Next2 then
        if state == 2 then
            ActivityLogic.OpenStackUI(UIName2ID.ActivityTemplateReplacementMaterialPop, self, self._activityID, data, self._index)
        end
    else
        if state == 2 then
            if btnState <= 2 then
                if data.itemType == 1 then
                    ActivityLogic.OpenStackUI(UIName2ID.ActivityUniversalExchangeTemplatePop1, self, self._activityID, data, self._index)
                else
                    ActivityLogic.OpenStackUI(UIName2ID.ActivityUniversalExchangeTemplatePop2, self, self._activityID, data, self._index)
                end
            end
        elseif state == 3 then
            local tipsStr = nil
            if btnState == 2 then
                --这里启用打开，应该不会提示
                if data.itemType == 1 then
                    local item = ActivityLogic.GetWeaponSkinItem({id = data.currencyId})
                    if item then
                        tipsStr = General.GetText(Config.Loc.InsufficientPleaseCollect, item.name)
                    end
                else
                    tipsStr = General.GetText(Config.Loc.InsufficientPleaseCollect, Config.Loc.Material)
                end
            elseif btnState == 3 then
                -- tipsStr = ActivityLogic.GetTextColor(ActivityLogic.GetTimeStr(time), timeStr, 1)
            elseif btnState == 4 then
                tipsStr = Config.Loc.ToBeOpened
            elseif btnState == 5 then
                tipsStr = Config.Loc.AlreadyRedeemed
            end
            if tipsStr then
                Module.CommonTips:ShowSimpleTip(tipsStr)
            end
        end
    end
end

function ActivityUniversalExchangeTemplateItem:_OnMarkBtnClick(isBool)
    local data = self._data
    if data == nil then
        return
    end
    if isBool then
        Module.CommonTips:ShowSimpleTip(Config.Loc.Collected)
    else
        Module.CommonTips:ShowSimpleTip(Config.Loc.CancelCollection)
    end
    loginfo("ssy on clicked is ", isBool)
    self._wtMarkBtn:SetIsChecked(not isBool)
    --请求改变收藏
    Server.ActivityServer:SendActExcFocus(self._activityID, data.exchangeId, isBool)
end

function ActivityUniversalExchangeTemplateItem:_SetReddot(isBool)
    --判断兑换条件
    local data = self._data
    if data == nil then
        return
    end
    local btnState, time, timeStr, btnStr = self:_IsCanExchange()
    local list, redottList = self:_GetCurGroupProp()
    local isReddot = false
    if btnState < 3 then
        if data.itemType == 1 then
            isReddot = btnState == 1
        else
            if redottList then
                isReddot = #redottList > 0
            else
                isReddot = false
            end
        end
    end
    loginfo("ssy set reddot of focus is ", isBool, " reddot is ", isReddot and isBool)
    --收藏处理
    self._wtMarkBtn:SetIsChecked(isBool)
    ActivityLogic.AddReddot(self, self._wtMarkRedoot, isReddot and isBool)
end

--------------------------------------------------事件监听与刷新处理--------------------------------------------------
function ActivityUniversalExchangeTemplateItem:_AddEventListener()
    self:AddLuaEvent(Server.ActivityServer.Events.evtActivityDataUpdateGeneral, self._OnDataUpdateGeneral, self)
    self:AddLuaEvent(Config.evtActivitySubBetweenInteractive, self._OnSubBetweenInteractive, self)
end

function ActivityUniversalExchangeTemplateItem:_OnDataUpdateGeneral(activityID, noticeType)
    if activityID == self._activityID then
        if noticeType == EActSubInteractive.Next1 then
            
        end
    end
end

function ActivityUniversalExchangeTemplateItem:_OnSubBetweenInteractive(activityID, itemType, index, param1, param2, param3)
    if activityID == self._activityID then
        local data = self._data
        if data == nil or param1 == nil then
            return
        end
        if itemType == EActSubInteractive.ItemWaterfall then
            --通过下标刷新对应的道具组
            if index == self._index then
                if param1 then
                    data.selected_plan = param1
                end
                self:_SetPromptInformation()--处理提示信息
            end
        elseif itemType == EActSubInteractive.Next4 then
            if index == nil and data.exchangeId == param1.exchangeId then
                loginfo("ssy Next4 exchangeId id ", data.exchangeId)
                self:_IsRefreshIcon(self._activityID, self._index, param1, false)
            end
        elseif itemType == EActSubInteractive.Next5 then
            if index == nil and data.exchangeId == param1.exchangeId then
                loginfo("ssy Next5 exchangeId id ", data.exchangeId)
                self:_IsRefreshIcon(self._activityID, self._index, param1, true)
            end
        end
    end
end

--------------------------------------------------WaterfallScrollBox逻辑处理--------------------------------------------------
function ActivityUniversalExchangeTemplateItem:_OnWaterfallCount()
    if self._list then
        return #self._list
    else
        return 0
    end
end

function ActivityUniversalExchangeTemplateItem:_OnWaterfallWidget(position, itemWidget)
    local index = ActivityLogic.GetBoxIndex(self, position)

    if self._list and itemWidget and index then
        if self._list[index] and self._btnState then
            self._list[index].isOpenness = self._btnState ~= 4
            self._list[index].isCollect  = self._btnState < 5
        end
        itemWidget:InitData(self._activityID, index, self._list[index])
    end
end

----------------------------------------------------动态添加空状态-------------------------------------------------------------

function ActivityUniversalExchangeTemplateItem:_IsJudge()
    local data = self._data
    if data and data.id then
        self:_AddEmptyItem(false)
        return false
    else
        self:_AddEmptyItem(true)
        return true
    end
end

function ActivityUniversalExchangeTemplateItem:_AddEmptyItem(isBool)
    if isBool == nil then
        self._btn = nil
        self:_CreateItem()
    elseif isBool then
        if self._btn == nil then
            self._btn = self:_CreateItem(1)
        end
        --隐藏文本
        if self._btn then
            ActivityLogic.SetWidgetVisible(self._btn:Wnd("RichTextBlock_50", UITextBlock))
        end

        ActivityLogic.SetWidgetVisible(self._btn, true)
        ActivityLogic.SetWidgetVisible(self._wtEmptyPanel, true,  true)
        ActivityLogic.SetWidgetVisible(self._wtItemPanel,  false, true, true)
    else
        ActivityLogic.SetWidgetVisible(self._btn, false)
        ActivityLogic.SetWidgetVisible(self._wtEmptyPanel, false, true)
        ActivityLogic.SetWidgetVisible(self._wtItemPanel,  true,  true, true)
    end
end

function ActivityUniversalExchangeTemplateItem:_CreateItem(itemType)
    local uiNavId = UIName2ID.CommonEmptyContent
    local container = self._wtEmptyBox
    local instance = ActivityLogic.GetSubUiInst(self, uiNavId, container, itemType)
    return instance
end

-------------------------------------------------------添加鼠标事件-------------------------------------------------------
function ActivityUniversalExchangeTemplateItem:_AddMouseMove(isBool)
    local gamelnst = GetGameInstance()

    if gamelnst then
        if isBool then
            self._buttonMoveHandle = UDFMGameHudDelegates.Get(gamelnst).OnHandleMouseMoveEvent:Add(self._OnMouseButtonMove, self)
        else
            if self._buttonMoveHandle then
                UDFMGameHudDelegates.Get(gamelnst).OnHandleMouseMoveEvent:Remove(self._buttonMoveHandle)
                self._buttonMoveHandle = nil
            end
        end
    end
end

function ActivityUniversalExchangeTemplateItem:_OnMouseButtonMove(mouseEvent)
    --鼠标位置控制(超框处理)
    if mouseEvent and self._wtWaterfallBox and self._wtItemPanel then
        local sceenPos = mouseEvent:GetScreenSpacePosition()
        local geometry = self._wtWaterfallBox:GetCachedGeometry()
        local geometryPar = self._wtItemPanel:GetCachedGeometry()
        if geometry and sceenPos and geometryPar then
            local isUnder = USlateBlueprintLibrary.IsUnderLocation(geometry, sceenPos)
            local isUnderPar = USlateBlueprintLibrary.IsUnderLocation(geometryPar, sceenPos)
            --发送鼠标位置事件(处理滚动框滑动问题)
            if isUnderPar then
                local btnState = self:_IsCanExchange()

                Config.evtActivitySubBetweenInteractive:Invoke(self._activityID, EActSubInteractive.Anim, self._index, isUnder and btnState ~= 4)
            end
        end
    end
end

return ActivityUniversalExchangeTemplateItem