----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMReddotTrie)
----- LOG FUNCTION AUTO GENERATE END -----------



local ReddotDataTrie = require "DFM.Business.Module.ReddotTrieModule.ReddotBase.ReddotDataTrie"
local QuestManifest = require "DFM.Business.Module.ReddotTrieModule.DotUnit.QuestDotUnit.Defination.QuestManifest"
local ReddotTrieConfig = require "DFM.Business.Module.ReddotTrieModule.ReddotTrieConfig"

local QuestDataTrie = ReddotDataTrie:New(EReddotTrieObserverType.Quest)

function QuestDataTrie:InitTrie()
    self:GenerateSubTrie(QuestManifest)
end

--建议在函数中对id做判空,以防阻塞创建数据节点
QuestDataTrie.CheckNeedTakeAndRewardByLineID = function(id)
    local questlineInfo = Server.QuestServer:GetQuestLineInfoById(id)
    local needRet = Server.QuestServer:GetNotifyQuestInQuestLine(questlineInfo)
    return needRet     
end

QuestDataTrie.CheckQuestSeason = function ()
    local lineInfo = Server.QuestServer:GetCurrentSeasonLine()
    local missionRet = false

    if lineInfo ~= nil then
        local seasonLevel = Server.RoleInfoServer.seasonLevel
        local openLevel = lineInfo.openLevel
        if openLevel < seasonLevel then
            missionRet = Server.QuestServer:GetNotifyQuestInSeasonLine(lineInfo)
        else
            return false
        end
    else
        return false
    end
    local collectionData = Server.QuestServer:GetCollectorDataInfo()
    local collectionRet = false
    if collectionData ~= nil then
        collectionRet = collectionData:IsCanGainRewardExist()
    end
    local factData = Server.QuestServer._questFactData
    local factRet = false   
    if factData ~= nil then
        factRet = Server.QuestServer._questFactData:IsCanGainRewardExist()
    end 
    return missionRet or collectionRet or factRet
end

return QuestDataTrie
