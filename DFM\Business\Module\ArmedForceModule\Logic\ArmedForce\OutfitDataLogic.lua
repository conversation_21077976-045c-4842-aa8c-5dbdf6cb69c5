local ItemOperaTool         = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local ItemConfigTool        = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local WeaponAssemblyTool    = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local ItemHelperTool        = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local WeaponHelperTool               = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local ArmedForceDataLogic            = require "DFM.Business.Module.ArmedForceModule.Logic.ArmedForce.ArmedForceDataLogic"
local QuickOperationDataMedicineStruct = require "DFM.Business.Module.ArmedForceModule.Data.QuickOperationDataMedicineStruct"
local QuickOperationDataBulletStruct   = require "DFM.Business.Module.ArmedForceModule.Data.QuickOperationDataBulletStruct"
local QuickOperationLogic               = require "DFM.Business.Module.ArmedForceModule.Logic.QuickOperation.QuickOperationLogic"
local CheckEquipLogic                  = require "DFM.Business.Module.ArmedForceModule.Logic.Equipment.CheckEquipLogic"
local EModularWeaponDescCompare = import "EModularWeaponDescCompare"
local UAmmoDataManager = import "AmmoDataManager"
local ammoMgr = UAmmoDataManager.Get()
----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMArmedForce)
----- LOG FUNCTION AUTO GENERATE END -----------
-- 部分逻辑来源于ArmedForcePresetLogic、ArmedForceRecommendationsLogic，仅移植过来
local function logPass(step, bPass)
    local log = logwarning
    if not bPass then
        log = logerror
    end
    log("[OutfitDataLogic] 应用方案 是否通过", step, bPass)
end



local OutfitDataLogic = {}
---------------------------------------------------------------------------------
--- Logic可以拆分多个，用于业务逻辑的编写，部分需要开放的接口由Module进行转发
---------------------------------------------------------------------------------
--- 可以仅模块内部调用，也可以在Module中被公开

OutfitDataLogic.OutfitTypeList = {
    ESlotType.MainWeaponLeft,
    ESlotType.MainWeaponRight,
    ESlotType.Pistrol,
    ESlotType.BreastPlate,
    ESlotType.Helmet,
    ESlotType.ChestHanging,
    ESlotType.Bag,
}

OutfitDataLogic.ContainerTypeList = {
    ESlotType.ChestHangingContainer,
    ESlotType.Pocket,
    ESlotType.BagContainer,
    ESlotType.SafeBoxContainer
}

OutfitDataLogic.WeaponTypeList = 
{
    ESlotType.MainWeaponLeft,
    ESlotType.MainWeaponRight,
    ESlotType.Pistrol
}

OutfitDataLogic.IgnoreContainer = 
{
    [ESlotType.BagContainer] = true,
    [ESlotType.SafeBoxContainer] = true,
    [ESlotType.ChestHangingContainer] = true,
    [ESlotType.Pocket] = true, -- 口袋
}

--#region ==============================================应用方案========================================================
-- 应用方案ing
local operateList = { -- 操作数据
        unEquip = {},--gid->num
        equip = {},--gid->num , 子弹id->num
        buy = {},-- id->num
        carry = {}, --id->num
        buyAndCarry = {},-- id->num
        buyAndAssembly = {},
    }
-- 重构应用逻辑，分三步骤：第一步是处理武器，第二步是处理装备，第三步是处理容器里的道具
OutfitDataLogic.ApplyOperationOutfit = function()
    -- 拿到装备数据
    operateList = OutfitDataLogic.GenOperateList()

    --------------------------------- 步骤1 武器处理 -----------------------------------------
    -- 1.1 卸下武器
    -- 1.2 装备武器
    -- 1.3 购买武器
    -- 1.4 购买配件和弹匣子弹
    -- 1.5 改枪
    -- 1.6 卸下子弹
    -- 1.7 填充子弹
    local fWeaponProcess = function()
        -- =1.1= 卸下武器**********************************
        -- 武器推荐有多种情况，避免回包中出现与推荐的情况不一样，先卸下武器再进行操作
        OutfitDataLogic.UnEquipWeapon()
    end
    
    -----------------------------------------------------------------------------------------

    --------------------------------- 步骤2 装备处理 -----------------------------------------
    -- 2.1 装配装备
    -- 2.2 购买装备
    -- 卡点 背包胸挂   步骤3（药品）
    -----------------------------------------------------------------------------------------

    --------------------------------- 步骤3 物资处理 -----------------------------------------
    -- 分子弹（子弹依赖机匣）和药品（依赖背包胸挂）
    -- 3.1 快照
    -- 3.2 购买物资
    -----------------------------------------------------------------------------------------


    --------------------------------- 步骤4 应用成功数据清除关闭界面处理 -----------------------------------------
    -- 4 关闭界面清除数据
    -----------------------------------------------------------------------------------------

    -- 武器处理
    fWeaponProcess()
end

-- 获取errcodestr
OutfitDataLogic.GetErrCode2String = function(errorCode)
    local commonErrorConfig = Module.CommonTips.Config.GetErrorTable()
    if commonErrorConfig and commonErrorConfig[tostring(errorCode)] then
        local errorConfig = commonErrorConfig[tostring(errorCode)]
        local tipStr = errorConfig.ExtDesc
        if tipStr and string.len(tipStr) > 0 then
            --loginfo("errorCode: ", errorCode, " 已扩展自定义配置")
            return tipStr
        else
            tipStr = errorConfig.BaseDesc
            if tipStr and string.len(tipStr) > 0 then
                --loginfo("errorCode: ", errorCode, " 读取服务器原生配置")
                return string.format("%s: %s", tostring(errorCode), tipStr)
            end
        end
    end
end

-- 异常处理
OutfitDataLogic.ApplyOutfitError = function(res, step)
    logerror("[OutfitDataLogic] 应用方案 回包异常 +++++++++++++++++++++++++++", step)
    if res ~= nil then
        Facade.ProtoManager:ManuelHandleErrCode(res)
    end
    operateList = { -- 操作数据
        unEquip = {},--gid->num
        equip = {},--gid->num , 子弹id->num
        buy = {},-- id->num
        carry = {}, --id->num
        buyAndCarry = {},-- id->num
        buyAndAssembly = {},
    }

    Module.ArmedForce.Config.evtApplyOutfitError:Invoke()
    Server.InventoryServer:ClearSlotGroup(ESlotGroup.OutFit)
    Module.ArmedForce.Field:ClearMapPos2FitSampleItem()
    logerror("[OutfitDataLogic] 应用方案 处理异常 关闭界面清除数据 结束!!!")
end

-- -- 异常处理
-- OutfitDataLogic.ApplyOutfitError = function(res, step, errStr)
--     logerror("[OutfitDataLogic] 应用方案 回包异常 +++++++++++++++++++++++++++", step)
--     if res ~= nil then
--         local errCodeStr = OutfitDataLogic.GetErrCode2String(res.result)
--         if errCodeStr then
--             Module.CommonTips:ShowSimpleTip(string.format(Module.ArmedForce.Config.Loc.OutfitErrorStr, errCodeStr, Module.ArmedForce.Config.Loc.ApplySchemeFailed))
--         else
--             if not string.isempty(errStr) then
--                 Module.CommonTips:ShowSimpleTip(string.format(Module.ArmedForce.Config.Loc.OutfitErrorStr, errStr, Module.ArmedForce.Config.Loc.ApplySchemeFailed))
--             else
--                 Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.ApplySchemeFailed)
--             end
--         end
--     else
--         if not string.isempty(errStr) then
--             Module.CommonTips:ShowSimpleTip(string.format(Module.ArmedForce.Config.Loc.OutfitErrorStr, errStr, Module.ArmedForce.Config.Loc.ApplySchemeFailed))
--         else
--             Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.ApplySchemeFailed)
--         end
--     end
--     operateList = { -- 操作数据
--         unEquip = {},--gid->num
--         equip = {},--gid->num , 子弹id->num
--         buy = {},-- id->num
--         carry = {}, --id->num
--         buyAndCarry = {},-- id->num
--         buyAndAssembly = {},
--     }

--     Module.ArmedForce.Config.evtApplyOutfitError:Invoke()
--     Server.InventoryServer:ClearSlotGroup(ESlotGroup.OutFit)
--     Module.ArmedForce.Field:ClearMapPos2FitSampleItem()
--     logerror("[OutfitDataLogic] 应用方案 处理异常 关闭界面清除数据 结束!!!")
-- end

-- 检查武器是否准备好
OutfitDataLogic.CheckWeaponsReady = function()
    local bWeaponReady = true
    local weaponGidMap = {}
    for _, slotType in ipairs(OutfitDataLogic.WeaponTypeList) do
        local outFitWeaponSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.OutFit)
        local outFitWeapon = outFitWeaponSlot:GetEquipItem()
        local weaponSlot = Server.InventoryServer:GetSlot(slotType)
        local weapon = weaponSlot:GetEquipItem()
        if outFitWeapon then
            if weapon and outFitWeapon.id == weapon.id then
                weaponGidMap[slotType] = weapon.gid
                logwarning("[OutfitDataLogic] 应用方案 检查武器 CheckWeaponsReady 准备好了~~~~~~~~~~~~~~~~~~~~~~~~~", slotType, weapon.name, weapon.gid)
            else
                logerror("[OutfitDataLogic] 应用方案 检查武器 CheckWeaponsReady 没准备好~~~~~~~~~~~~~~~~~~~~~~~~~", slotType, outFitWeapon.name)
                bWeaponReady = false
            end
        end
    end

    return bWeaponReady, weaponGidMap
end

-- 检查容器是否准备好
OutfitDataLogic.CheckContainerReady = function()
    local bContainerReady = true
    for _, slotType in ipairs({ESlotType.ChestHanging, ESlotType.Bag}) do
        local outFitContainerSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.OutFit)
        local outFitContainer = outFitContainerSlot:GetEquipItem()
        local containerSlot = Server.InventoryServer:GetSlot(slotType)
        local container = containerSlot:GetEquipItem()
        if outFitContainer then
            if container and outFitContainer.id == container.id then
                logwarning("[OutfitDataLogic] 应用方案 检查容器 CheckContainerReady 准备好了~~~~~~~~~~~~~~~~~~~~~~~~~", slotType, container.name, container.gid)
            else
                logerror("[OutfitDataLogic] 应用方案 检查武器 CheckContainerReady 没准备好~~~~~~~~~~~~~~~~~~~~~~~~~", slotType, outFitContainer.name)
                bContainerReady = false
            end
        end
    end

    return bContainerReady
end

-- 1.1 卸下武器
OutfitDataLogic.UnEquipWeapon = function()
    local function fOnUnEquipWeaponCallback(res)
        logwarning("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.1 卸下武器回包", res and res.result or "无需请求卸下")
        if not res or (res and res.result == 0) then
            -- =1.2= 装备武器**********************************
            OutfitDataLogic.EquipWeapon()
        elseif res and res.result ~= 0 then
            OutfitDataLogic.ApplyOutfitError(res, 1.1)
        end
    end

    logwarning("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.1 UnEquipWeapon 卸下武器+++++++++++++++++++++++++++")
    for _, slotType in ipairs(OutfitDataLogic.WeaponTypeList) do
        local equipOperateData = operateList.unEquip[slotType]
        if equipOperateData then
            local gid = equipOperateData.gid
            local equipItem_player = Server.InventoryServer:GetItemByGid(gid, ESlotGroup.Player)
            if equipItem_player then
                if not ItemOperaTool.DoUnEquipItem(equipItem_player) then
                    logerror(string.format("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.1 UnEquipWeapon 卸下武器 %s==>%s卸不了！！！", slotType, equipItem_player.name))
                    OutfitDataLogic.ApplyOutfitError(nil, 1.1)
                    return
                else
                    logwarning(string.format("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.1 UnEquipWeapon 卸下武器 %s==>%s", slotType, equipItem_player.name))
                end
            end
        end
    end

    if Server.InventoryServer:CheckItemMoveCmdBuffer() then
        Server.InventoryServer:SyncInventoryChanged(fOnUnEquipWeaponCallback)
    else
        fOnUnEquipWeaponCallback()
    end
end


-- 1.2 装备武器
OutfitDataLogic.EquipWeapon = function()
    local function fOnEquipWeaponCallback(res)
        logwarning("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.2 装备武器回包", res and res.result or "无需请求装备")
        if not res or (res and res.result == 0) then
            -- =1.3= 购买武器**********************************
            OutfitDataLogic.BuyWeapon()
        elseif res and res.result ~= 0 then
            OutfitDataLogic.ApplyOutfitError(res, 1.2)
        end
    end

    logwarning("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.2 EquipWeapon 装备武器+++++++++++++++++++++++++++")
    for _, slotType in ipairs(OutfitDataLogic.WeaponTypeList) do
        local equipOperateData = operateList.equip[slotType]
        if equipOperateData then
            local playerSlot = Server.InventoryServer:GetSlot(slotType)
            local item = Server.InventoryServer:GetItemByGid(equipOperateData.gid, ESlotGroup.Player)
            if not ItemOperaTool.DoEquipItem(item, playerSlot) then
                logerror(string.format("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.2 EquipWeapon %s==>%s装备不了！！！", slotType, item.name))
                OutfitDataLogic.ApplyOutfitError(nil, 1.2)
                return
            else
                logwarning("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.2 EquipWeapon 请求装备", item.name, item.gid, slotType)
            end
        end
    end

    if Server.InventoryServer:CheckItemMoveCmdBuffer() then
        Server.InventoryServer:SyncInventoryChanged(fOnEquipWeaponCallback)
    else
        fOnEquipWeaponCallback()
    end
end

-- 1.3 购买武器
OutfitDataLogic.BuyWeapon = function()
    local function fOnBuyWeaponCallback(bSuccess)
        LogAnalysisTool.DoSendPresetPriceFluctuationFlowLog()
        Timer.DelayCall(0.1, function () -- 做一个延时，因为购买的回包和仓库的ntf可能会后收到，做延时保证仓库数据及时刷新了
            logwarning("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.3 购买武器回包", bSuccess == nil and "无bSuccess" or bSuccess)
            if bSuccess then
                -- =1.4= 购买配件和弹匣子弹**********************************
                OutfitDataLogic.BuyWeaponComponentsAndBullets()

            else
                OutfitDataLogic.ApplyOutfitError(nil, 1.3)
            end
        end)
    end

    logwarning("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.3 BuyWeapon 购买武器+++++++++++++++++++++++++++")
    local cheapBuyItems = {}

    -- 机匣
    for _, slotType in ipairs(OutfitDataLogic.WeaponTypeList) do
        local buyOperateData = operateList.buy[slotType]
        if buyOperateData then
            local cheapBuyItem = {
                id = buyOperateData.id,
                num = buyOperateData.num,
                targetSlotType = slotType
            }
            local itemRow = ItemConfigTool.GetItemConfigById(cheapBuyItem.id)
            logwarning("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.3 BuyWeapon 请求比价购买【机匣】", itemRow.Name, cheapBuyItem.num, slotType)
            table.insert(cheapBuyItems, cheapBuyItem)
        end
    end

    OutfitDataLogic.BuyOp(cheapBuyItems, fOnBuyWeaponCallback)
end

-- 1.4 购买配件和弹匣子弹
OutfitDataLogic.BuyWeaponComponentsAndBullets = function()
    local function fOnBuyComponentsAndBulletCallback(bSuccess)
        logwarning("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.4 购买配件和弹匣子弹", bSuccess == nil and "无bSuccess" or bSuccess)
        LogAnalysisTool.DoSendPresetPriceFluctuationFlowLog()
        if bSuccess then
            -- =1.5= 改枪**********************************
            OutfitDataLogic.UsedWeaponPresetPropInfo()
        else
            OutfitDataLogic.ApplyOutfitError(nil, 1.4)
        end
    end

    logwarning("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.4 BuyWeaponComponentsAndBullets 购买配件和弹匣子弹 +++++++++++++++++++++++++++")

    -- todo 检查gid
    local bWeaponReady, weaponGidMap = OutfitDataLogic.CheckWeaponsReady()

    if bWeaponReady then
        logwarning("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.4 BuyWeaponComponentsAndBullets 武器准备好了 继续执行步骤1 剩下的逻辑")
        local cheapBuyItems = {}

        -- 配件
        for _, buyAndAssemblyOperateData in pairs(operateList.buyAndAssembly) do
            local posGuid = buyAndAssemblyOperateData.pos_guid
            local targetBuyGid = buyAndAssemblyOperateData.target_buy_gid
            local targetGid = weaponGidMap[buyAndAssemblyOperateData.target_buy_gid]
            local targetId = buyAndAssemblyOperateData.target_id

            local cheapBuyItem = {
                id = buyAndAssemblyOperateData.id,
                num = buyAndAssemblyOperateData.num,
                pos_guid = posGuid,
                target_gid = (targetGid and targetGid ~= 0) and targetGid or nil,
                target_id = (targetGid and targetGid == 0) and targetId or nil,
                target_buy_gid = targetBuyGid,
                targetSlotType = ESlotType.MainContainer
            }
            local itemRow = ItemConfigTool.GetItemConfigById(cheapBuyItem.id)
            logwarning("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.4 BuyWeaponComponentsAndBullets 请求比价购买【配件】", itemRow.Name, cheapBuyItem.num, ESlotType.MainContainer, targetBuyGid, targetGid)
            table.insert(cheapBuyItems, cheapBuyItem)
        end

        -- 子弹
        local allQuickOperationDataInfo = Module.ArmedForce.Field:GetAllQuickOperationDataInfo()
        for id, quickOperationDataInfo in pairs(allQuickOperationDataInfo) do
            if quickOperationDataInfo.itemMainType == EItemType.Bullet then
                local weaponBulletBuyNum = quickOperationDataInfo:GetWeaponBulletBuyNum()
                if weaponBulletBuyNum > 0 then
                    local findCheapBuyItem = table.find(cheapBuyItems, function (v, k)
                        return v.id == id and v.targetSlotType == ESlotType.MainContainer
                    end)
                    local itemRow = ItemConfigTool.GetItemConfigById(id)
                    if findCheapBuyItem then
                        findCheapBuyItem.num = findCheapBuyItem.num + weaponBulletBuyNum
                        logwarning("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.4 BuyWeaponComponentsAndBullets 请求比价购买【子弹】", itemRow.Name, weaponBulletBuyNum, ESlotType.MainContainer)
                    else
                        local cheapBuyItem = {
                            id = id,
                            num = weaponBulletBuyNum,
                            targetSlotType = ESlotType.MainContainer,
                        }
                        table.insert(cheapBuyItems, cheapBuyItem)
                        logwarning("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.4 BuyWeaponComponentsAndBullets 请求比价购买【子弹】", itemRow.Name, weaponBulletBuyNum, ESlotType.MainContainer)
                    end
                end
            end
        end

        OutfitDataLogic.BuyOp(cheapBuyItems, fOnBuyComponentsAndBulletCallback)
    else
        logerror("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.4 BuyWeaponComponentsAndBullets 武器还未准备好，不执行步骤1 后面逻辑了")
        OutfitDataLogic.ApplyOutfitError(nil, 1.4)
    end
end


-- 1.5 改枪
OutfitDataLogic.UsedWeaponPresetPropInfo = function()
    local allRes = {}
    local function fOnAllAssembleGunCallback()
        local bAllAssembleGunSucess = true
        local firstRes = nil
        for slotType, result in pairs(allRes) do
            logwarning("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.5 改枪 遍历回包", slotType, result and result or "无需改枪")
            if result and result ~= 0 then
                firstRes = {
                    result = result
                }
                bAllAssembleGunSucess = false
            end
        end
        if bAllAssembleGunSucess then
            -- =1.6= 卸下子弹**********************************
            OutfitDataLogic.WeaponRemoveBullet()
        else
            OutfitDataLogic.ApplyOutfitError(firstRes, 1.5)
        end
    end

    logwarning("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.5 UsedWeaponPresetPropInfo 改枪+++++++++++++++++++++++++++")
    local promiseList = {}
    local targetPropInfos = {}
    local curPropInfos = {}
    for index, slotType in ipairs(OutfitDataLogic.WeaponTypeList) do
        local presetData = Module.ArmedForce.Field:GetMapPos2FitPresetData(slotType)
        if presetData and presetData.weaponData and presetData.weaponData.propInfo then
            logwarning("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.5 UsedWeaponPresetPropInfo 改枪ing 1==>", slotType)
            local slot = Server.InventoryServer:GetSlot(slotType)
            local equipment = slot:GetEquipItem()
            if equipment then
                logwarning("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.5 UsedWeaponPresetPropInfo 改枪ing 2==>", equipment.name)
                local lhs, rhs
                local curPropInfo = equipment:GetRawPropInfo()
                lhs = WeaponAssemblyTool.PropInfo_To_Desc(curPropInfo)
                rhs = WeaponAssemblyTool.PropInfo_To_Desc(presetData.weaponData.propInfo)
                local bEqual = WeaponHelperTool.CompareWeaponDescription(lhs, rhs, EModularWeaponDescCompare.SolutionCompare)
                if bEqual then
                    logwarning("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.5 UsedWeaponPresetPropInfo 仓库武器和改枪方案一致，无需请求改枪==>", equipment.name, slotType)
                else
                    table.insert(targetPropInfos, presetData.weaponData.propInfo)
                    table.insert(curPropInfos, curPropInfo)
                end
            else
                logerror("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.5 UsedWeaponPresetPropInfo 改枪 该槽位没武器!!!!", slotType)
            end
        end
    end

    if #curPropInfos == #targetPropInfos then
        if table.isempty(curPropInfos) then
            logwarning("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.5 UsedWeaponPresetPropInfo 改枪 无需改枪!!!!", table.isempty(curPropInfos))
        else
            local bSuccess, newPropInfos = Module.Gunsmith:LinkWeaponDescriptionNodeGUIDFormPropInfos(curPropInfos, targetPropInfos, ESlotGroup.Player, false, false)
            if bSuccess then
                for i, propInfo in ipairs(newPropInfos) do
                    local curPropInfo = curPropInfos[i]
                    if propInfo then
                        table.insert(promiseList, OutfitDataLogic.PDoAssembleGunReq(propInfo):Then(function (result)
                            logwarning("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.5 UsedWeaponPresetPropInfo 改枪回调 ==>", curPropInfo.position, result)
                            allRes[curPropInfo.position] = result
                        end))
                    end
                end
            end
        end
    else
        logerror("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.5 UsedWeaponPresetPropInfo 改枪 改枪原数据和目标方案数据数量不一致!!!!", #curPropInfos, #targetPropInfos)
    end

    

    if not table.isempty(promiseList) then
        PromiseAll(promiseList):Then(function ()
            logwarning("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.5 UsedWeaponPresetPropInfo 改枪 PromiseAll 全部回包=============")
            fOnAllAssembleGunCallback()
        end)
    else
        logwarning("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.5 UsedWeaponPresetPropInfo 改枪 无需改枪")
        fOnAllAssembleGunCallback()
    end

    
    --         local slot = Server.InventoryServer:GetSlot(slotType)
    --         local equipment = slot:GetEquipItem()
    --         if equipment then
    --             logwarning("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.5 UsedWeaponPresetPropInfo 改枪ing 2==>", equipment.name)
    --             local fSetUsedNum = CreateCallBack(Module.ArmedForce.Field.SetMapGid2UsedNum, Module.ArmedForce.Field)
    --             local fGetUsedNum = CreateCallBack(Module.ArmedForce.Field.GetMapGid2UsedNum, Module.ArmedForce.Field)
    --             local presetWeaponPropInfo = Module.ComparePrice:GenerateFinalPreset(equipment, presetData.weaponData.propInfo, fSetUsedNum, fGetUsedNum)
    --             if presetWeaponPropInfo then
    --                 table.insert(promiseList, OutfitDataLogic.PDoAssembleGunReq(presetWeaponPropInfo):Then(function (result)
    --                     logwarning("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.5 UsedWeaponPresetPropInfo 改枪回调 ==>", slotType, result)
    --                     allRes[slotType] = result
    --                 end))
    --             end
    --         else
    --             logerror("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.5 UsedWeaponPresetPropInfo 改枪 该槽位没武器!!!!", slotType)
    --         end
    --     end
    -- end

    -- if not table.isempty(promiseList) then
    --     PromiseAll(promiseList):Then(function ()
    --         logwarning("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.5 UsedWeaponPresetPropInfo 改枪 PromiseAll 全部回包=============")
    --         fOnAllAssembleGunCallback()
    --     end)
    -- else
    --     logwarning("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.5 UsedWeaponPresetPropInfo 改枪 无需改枪")
    --     fOnAllAssembleGunCallback()
    -- end
end

-- 1.6 卸下子弹
OutfitDataLogic.WeaponRemoveBullet = function ()
    local function fOnWeaponRemoveBulletCallback(res)
        logwarning("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.6 卸下子弹回包", res and res.result or "无需请求卸下子弹")
        if not res or (res and res.result == 0) then
            -- =1.7= 填充子弹**********************************
            OutfitDataLogic.WeaponAddBulletProgress()
        else
            OutfitDataLogic.ApplyOutfitError(res, 1.6)
        end
    end

    logwarning("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.6 WeaponRemoveBullet 卸下子弹+++++++++++++++++++++++++++")
    local lastCommonds = {}
    local function fRemoveBullet(slotType)
        local playerWeaponSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.Player)
        local playerWeapon = playerWeaponSlot:GetEquipItem()
        if playerWeapon then
            local propInfo = playerWeapon:GetRawPropInfo()
            local bullets = WeaponAssemblyTool.GetWeaponBullets(propInfo)
            for _, bulletPropInfo in ipairs(bullets) do
                local id = bulletPropInfo.id
                local gid = bulletPropInfo.gid
                local operateNum = bulletPropInfo.num
                logwarning(string.format("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.6 WeaponRemoveBullet 武器卸下子弹 slotType = %s, playerWeapon.name = %s , id = %s, gid = %s, operateNum = %s",slotType, playerWeapon.name, id, gid, operateNum))
                Module.Gunsmith:AddBulletOperateCmd(lastCommonds, BulletOperateType.eBulletOperate_UnLoad, id, gid, operateNum, playerWeapon.id, playerWeapon.gid)
            end
        end
    end

    for _, slotType in ipairs(OutfitDataLogic.WeaponTypeList) do
        fRemoveBullet(slotType)
    end
    -- 发送装填协议
    if not table.isempty(lastCommonds) then
        Server.GunsmithServer:CSDepositOperateBulletReq(lastCommonds, fOnWeaponRemoveBulletCallback)
    else
        fOnWeaponRemoveBulletCallback()
    end
end

-- 1.7 填充子弹
OutfitDataLogic.WeaponAddBulletProgress = function ()
    local function fOnWeaponAddBulletCallback(res)
        logwarning("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.7 填充子弹回包", res and res.result or "无需请求填充子弹")
        logwarning("[OutfitDataLogic] 应用方案 步骤1 武器处理 结束 ！！！*************")
        if not res or (res and res.result == 0) then
            -- =2.1= 装配装备**********************************
            OutfitDataLogic.EquipOperateProgress()
        else
            OutfitDataLogic.ApplyOutfitError(res, 1.7)
        end
    end

    logwarning("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.7 WeaponAddBulletProgress 填充子弹+++++++++++++++++++++++++++")
    -- 标记使用过的子弹
    local mapBulletUsed = {}
    local lastCommonds = {}
    local function fAddBullet(slotType)
        local weaponSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.OutFit)
        local playerWeaponSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.Player)
        local weapon = weaponSlot:GetEquipItem()
        local playerWeapon = playerWeaponSlot:GetEquipItem()
        if weapon and playerWeapon then
            local outfitWeaponCapacity =  WeaponAssemblyTool.GetWeaponCapacity(weapon:GetRawPropInfo())
            local playerWeaponCapacity =  WeaponAssemblyTool.GetWeaponCapacity(playerWeapon:GetRawPropInfo())
            -- 判断改枪成功了不，基于弹夹容量
            if outfitWeaponCapacity == playerWeaponCapacity then
                local propInfo = weapon:GetRawPropInfo()
                local bullets = WeaponAssemblyTool.GetWeaponBullets(propInfo)
                for _, bulletPropInfo in ipairs(bullets) do
                    local id = bulletPropInfo.id
                    local operateNum = bulletPropInfo.num
                    local allMatchItems = Server.InventoryServer:GetItemsById(id)
                    for _, moveItem in ipairs(allMatchItems) do
                        local result = true
                        if operateNum <= 0 then
                            break
                        end
                        local inSlotType = moveItem.InSlot.SlotType
                        if inSlotType == ESlotType.SafeBoxContainer then
                            if Module.Inventory:CheckSafeBoxExpiredStatus() then
                                logerror("OutfitDataLogic.WeaponAddBulletProgress fAddBullet 安全箱过期了， 过滤掉安全箱里的子弹")
                                result = false
                            end
                        end
                        if result then
                            -- if not OutfitDataLogic.IgnoreContainer[inSlotType] then
                                if mapBulletUsed[moveItem.gid] then
                                    if mapBulletUsed[moveItem.gid] < moveItem.num then
                                        local optNum = math.min(operateNum, moveItem.num - mapBulletUsed[moveItem.gid])
                                        -- todo 装子弹
                                        logwarning(string.format("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.7 WeaponAddBulletProgress 填充子弹 slotType = %s, playerWeapon.name= %s , id = %s, gid = %s, optNum = %s",slotType, playerWeapon.name, id, moveItem.gid, optNum))
                                        Module.Gunsmith:AddBulletOperateCmd(lastCommonds, BulletOperateType.eBulletOperate_Load, moveItem.id, moveItem.gid, optNum, playerWeapon.id, playerWeapon.gid)
                                        operateNum = operateNum - optNum
                                        -- table.insert(finalOperateItemNums, {item = moveItem, num = optNum})
                                        mapBulletUsed[moveItem.gid] = mapBulletUsed[moveItem.gid] + optNum
                                    end
                                else
                                    local optNum = math.min(operateNum, moveItem.num)
                                    -- todo 装子弹
                                    logwarning(string.format("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.7 WeaponAddBulletProgress 填充子弹 slotType = %s, playerWeapon.name= %s , id = %s, gid = %s, optNum = %s",slotType, playerWeapon.name, id, moveItem.gid, optNum))
                                    Module.Gunsmith:AddBulletOperateCmd(lastCommonds, BulletOperateType.eBulletOperate_Load, moveItem.id, moveItem.gid, optNum, playerWeapon.id, playerWeapon.gid)
                                    operateNum = operateNum - optNum
                                    -- table.insert(finalOperateItemNums, {item = moveItem, num = optNum})
                                    mapBulletUsed[moveItem.gid] = optNum
                                end
                            -- end
                        end
                    end
                    if operateNum > 0 then
                        logerror("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.7 WeaponAddBulletProgress 填充子弹 ==> There are not enough bullets in the warehouse",slotType, playerWeapon.name, id, operateNum)
                    end
                end
            else
                logerror("[OutfitDataLogic] 应用方案 步骤1 武器处理 1.7 WeaponAddBulletProgress 填充子弹 ==> 没改枪成功，弹夹容量不对",slotType, playerWeapon.name, outfitWeaponCapacity, playerWeaponCapacity )
            end
        end
    end

    for _, slotType in ipairs(OutfitDataLogic.WeaponTypeList) do
        fAddBullet(slotType)
    end
    -- 发送装填协议
    if not table.isempty(lastCommonds) then
        Server.GunsmithServer:CSDepositOperateBulletReq(lastCommonds, fOnWeaponAddBulletCallback)
    else
        fOnWeaponAddBulletCallback()
    end
end

-- 2.1 装配装备
OutfitDataLogic.EquipOperateProgress = function()
    local function fOnDepositEquipPropCallback(res)
        logwarning("[OutfitDataLogic] 应用方案 步骤2 装备处理 2.1 装配装备回包", res and res.result or "无需请求装配装备")
        if not res or (res and res.result == 0) then
            -- =2.2= 购买装备**********************************
            OutfitDataLogic.BuyOperateEquipProgress()
        elseif res and res.result ~= 0 then
            OutfitDataLogic.ApplyOutfitError(res, 2.1)
        end
    end

    logwarning("[OutfitDataLogic] 应用方案 步骤2 装备处理 2.1 EquipOperateProgress 装配装备+++++++++++++++++++++++++++")

    -- 卸下装备
    for slotType, unEquipOperateData in pairs(operateList.unEquip) do
        -- 非武器
        if not table.contains(OutfitDataLogic.WeaponTypeList, slotType) then
            if not operateList.equip[slotType] then
                local item = Server.InventoryServer:GetItemByGid(unEquipOperateData.gid, ESlotGroup.Player)
                if item then
                    if not ItemOperaTool.DoUnEquipItem(item) then
                        -- 卸载不了就卸载不了吧，不需阻塞后面的
                        logerror(string.format("[OutfitDataLogic] 应用方案 步骤2 装备处理 2.1 EquipOperateProgress 【卸下装备】 %s==>%s 卸不了！！！", slotType, item.name))
                    else
                        logwarning(string.format("[OutfitDataLogic] 应用方案 步骤2 装备处理 2.1 EquipOperateProgress 【卸下装备】 %s==>%s", slotType, item.name))
                    end
                end
            end
        end
    end

    -- 装配装备
    for slotType, equipOperateData in pairs(operateList.equip) do
        -- 非武器
        if not table.contains(OutfitDataLogic.WeaponTypeList, slotType) then
            local playerSlot = Server.InventoryServer:GetSlot(slotType)
            local item = Server.InventoryServer:GetItemByGid(equipOperateData.gid, ESlotGroup.Player)
            if item then
                if not ItemOperaTool.DoEquipItem(item, playerSlot) then
                    -- 卸载不了就卸载不了吧，不需阻塞后面的
                    logerror(string.format("[OutfitDataLogic] 应用方案 步骤2 装备处理 2.1 EquipOperateProgress 【装配装备】 %s==>%s 装配不了！！！", slotType, item.name))
                else
                    logwarning(string.format("[OutfitDataLogic] 应用方案 步骤2 装备处理 2.1 EquipOperateProgress 【装配装备】 %s==>%s", slotType, item.name))
                end
            end
        end
    end

    if Server.InventoryServer:CheckItemMoveCmdBuffer() then
        Server.InventoryServer:SyncInventoryChanged(fOnDepositEquipPropCallback)
    else
        fOnDepositEquipPropCallback()
    end
end

-- 2.2 购买装备
OutfitDataLogic.BuyOperateEquipProgress = function()
    local function fOnBuyEquipmentCallback(bSuccess)
        LogAnalysisTool.DoSendPresetPriceFluctuationFlowLog()
        Timer.DelayCall(0.1, function () -- 做一个延时，因为购买的回包和仓库的ntf可能会后收到，做延时保证仓库数据及时刷新了
            logwarning("[OutfitDataLogic] 应用方案 步骤2 装备处理 2.2 购买装备回包", bSuccess == nil and "无bSuccess" or bSuccess)
            logwarning("[OutfitDataLogic] 应用方案 步骤2 装备处理 结束 ！！！*************")
            if bSuccess then
                -- =3.1= 快照**********************************
                OutfitDataLogic.SyncBodyContainerProgress()
            else
                OutfitDataLogic.ApplyOutfitError(nil, 2.2)
            end
        end)
    end

    logwarning("[OutfitDataLogic] 应用方案 步骤2 装备处理 2.2 BuyOperateEquipProgress 购买装备+++++++++++++++++++++++++++")
    local cheapBuyItems = {}
    for slotType, buyOperateData in pairs(operateList.buy) do
        -- 非武器
        if not table.contains(OutfitDataLogic.WeaponTypeList, slotType) then
            local cheapBuyItem = {
                id = buyOperateData.id,
                num = buyOperateData.num,
                targetSlotType = slotType
            }
            if slotType == ESlotType.Helmet or slotType == ESlotType.BreastPlate then
                cheapBuyItem.durabilitylvl = buyOperateData.durabilitylvl
            end
            local itemRow = ItemConfigTool.GetItemConfigById(cheapBuyItem.id)
            logwarning("[OutfitDataLogic] 应用方案 步骤2 武器处理 2.2 BuyOperateEquipProgress 请求比价购买【装备】", itemRow.Name, cheapBuyItem.num, slotType, cheapBuyItem.durabilitylvl)
            table.insert(cheapBuyItems, cheapBuyItem)
        end
    end

    OutfitDataLogic.BuyOp(cheapBuyItems, fOnBuyEquipmentCallback)

end

-- 3.1 快照
OutfitDataLogic.SyncBodyContainerProgress = function()
    local function fOnDepositAssemblySyncBodyContainerCallback(res)
        logwarning("[OutfitDataLogic] 应用方案 步骤3 物资处理 3.1 SyncBodyContainerProgress 快照 快照回包", res and res.result or "无快照请求")
        if not res or (res and res.result == 0) then
            -- =3.2= 购买物资**********************************
            OutfitDataLogic.BuyOperateContainerItemsProgress()
        elseif res and res.result ~= 0 then
            OutfitDataLogic.ApplyOutfitError(res, 3.1)
        end
    end

    logwarning("[OutfitDataLogic] 应用方案 步骤3 物资处理 3.1 SyncBodyContainerProgress 快照+++++++++++++++++++++++++++")
    local bWeaponReady, weaponGidMap = OutfitDataLogic.CheckWeaponsReady()
    local bContainerReady = OutfitDataLogic.CheckContainerReady()
    logwarning(string.format("[OutfitDataLogic] 应用方案 步骤3 物资处理 3.1 SyncBodyContainerProgress 快照 准备情况==> 武器 = %s, 容器 = %s", bWeaponReady, bContainerReady))
    if bContainerReady then
        ---@field public snapshots pb_BodyPosPropSnapshot[]
        -- 所需字段 // 身上容器里发生变动的道具信息，要包括id、num和location，新道具的gid可以没有，仅仅是移动的道具必须有
        ---@class pb_BodyPosPropSnapshot : ProtoBase
        ---@field public pos number
        ---@field public space number
        ---@field public props pb_PropInfo[]

        ---@class pb_PropInfo : ProtoBase
        ---@field public id number
        ---@field public gid number
        ---@field public num number
        ---@field public position number
        ---@field public loc pb_PropLocation

        local snapshots = {}
        local gidsMap = {}
        local function fFindItemGids(id)
            local allItems = Server.InventoryServer:GetItemsById(id)
            declare_if_nil(gidsMap, id, {})
            for _, item in ipairs(allItems) do
                local result = true
                local inSlot = item.InSlot
                if inSlot and inSlot.SlotType == ESlotType.SafeBoxContainer then
                    if Module.Inventory:CheckSafeBoxExpiredStatus() then
                        logerror("OutfitDataLogic.SyncBodyContainerProgress fFindItemGids 安全箱过期了， 过滤掉安全箱的快照数据")
                        result = false
                    end
                end
                if result then
                    table.insert(gidsMap[id], item.gid)
                end
            end
        end

        -- 设置快照数据
        local function fCopySlotProps(slotType)
            logwarning("[OutfitDataLogic] 应用方案 步骤3 物资处理 3.1 SyncBodyContainerProgress 快照 操作槽位", slotType)
            local operationSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.OutFit)
            local playerContainerSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.Player)
            if operationSlot and playerContainerSlot then
                local bInitSpaces = true
                if slotType ~= ESlotType.Pocket then
                    local containerType = playerContainerSlot:GetContainerType()
                    local playerEquipContainerSlot = Server.InventoryServer:GetSlot(containerType)
                    local equipment = playerEquipContainerSlot:GetEquipItem()
                    if not equipment then
                        bInitSpaces = false
                    elseif slotType == ESlotType.SafeBoxContainer then
                        if Module.Inventory:CheckSafeBoxExpiredStatus() then
                            logerror("OutfitDataLogic.SyncBodyContainerProgress fCopySlotProps 安全箱过期了， 过滤掉安全箱的快照数据")
                            return
                        end
                    end
                end
                if bInitSpaces then
                    -- 初始化spaces
                    local allSpaces = operationSlot:GetAllSpaces()
                    for _, space in pairs(allSpaces) do
                        local bodyPosPropSnapshot = pb.BodyPosPropSnapshot:New()
                        bodyPosPropSnapshot.pos = slotType
                        bodyPosPropSnapshot.space = space.index
                        bodyPosPropSnapshot.props = {}
                        table.insert(snapshots, bodyPosPropSnapshot)
                    end
                end

                local items = operationSlot:GetItems()
                if items and not table.isempty(items) then
                    for _, item in pairs(items) do
                        local prop = {}
                        local pbLocation = item:GetRawPropInfo().loc
                        local originalGid = item:GetRawPropInfo().originalGid
                        local space = pbLocation.space_id
                        local quickOperationDataInfo = Module.ArmedForce:GetQuickOperationDataInfo(item.id)
                        local moveNum = 0
                        if quickOperationDataInfo and quickOperationDataInfo.itemMainType == EItemType.Bullet and not bWeaponReady then
                            logerror(string.format("[OutfitDataLogic] 应用方案 步骤3 物资处理 3.1 SyncBodyContainerProgress 快照 武器还未准备好，方案里容器的子弹推荐失败 ==> 槽位 = %s, id = %s, num = %s, space = %s, pbLocation = (start_x = %s, start_y = %s, x = %s, y = %s, rotate = %s)",
                            slotType,
                            item.id,
                            item.num,
                            space,
                            pbLocation.start_x,
                            pbLocation.start_y,
                            pbLocation.x,
                            pbLocation.y,
                            pbLocation.rotate))
                            break
                        end
                        if quickOperationDataInfo then -- 有关联的
                            -- local moveNum = quickOperationDataInfo:GetMoveNum()
                            local quickOperationItemData = quickOperationDataInfo:GetItemDataByGid(item.gid)
                            if quickOperationItemData then -- 有关联的
                                moveNum = quickOperationItemData.moveNum
                                local buyNum = quickOperationItemData.buyNum
                                prop.id = item.id
                                prop.position = slotType
                                prop.loc = pbLocation
                                prop.num = moveNum
                                prop.gid = nil

                                if prop.num > 0 then
                                    if originalGid then
                                        if table.removebyvalue(gidsMap[item.id], originalGid) == 1 then
                                            prop.gid = originalGid
                                        elseif #gidsMap[item.id] > 0 then
                                            prop.gid = table.remove(gidsMap[item.id])
                                        end
                                    else
                                        if #gidsMap[item.id] > 0 then
                                            prop.gid = table.remove(gidsMap[item.id])
                                        end
                                    end
                                end

                                if prop.gid then
                                    local targetBodyPosPropSnapshot = table.find(snapshots, function(v, k)
                                        return v.space == space and v.pos == slotType
                                    end)
                                    if targetBodyPosPropSnapshot then
                                        table.insert(targetBodyPosPropSnapshot.props, prop)
                                    end
                                else
                                    logwarning(string.format("[OutfitDataLogic] 应用方案 步骤3 物资处理 3.1 SyncBodyContainerProgress 快照 找不到仓库gid【需购买】 快照数据：槽位 = %s, id = %s, num = %s, space = %s, pbLocation = (start_x = %s, start_y = %s, x = %s, y = %s, rotate = %s)",
                                    slotType,
                                    item.id,
                                    item.num, -- 这里打印的是整个格子的数量
                                    space,
                                    pbLocation.start_x,
                                    pbLocation.start_y,
                                    pbLocation.x,
                                    pbLocation.y,
                                    pbLocation.rotate
                                    ))
                                end
                            end
                        else
                            prop.id = item.id
                            prop.position = slotType
                            prop.loc = pbLocation
                            prop.gid = item.gid
                            prop.num = item.num
                            moveNum = item.num
                            local targetBodyPosPropSnapshot = table.find(snapshots, function(v, k)
                                return v.space == space and v.pos == slotType
                            end)
                            if targetBodyPosPropSnapshot then
                                table.insert(targetBodyPosPropSnapshot.props, prop)
                            end
                        end
                        logwarning(string.format("[OutfitDataLogic] 应用方案 步骤3 物资处理 3.1 SyncBodyContainerProgress 快照 快照数据：槽位 = %s, id = %s, gid = %s, moveNum = %s, space = %s, pbLocation = (start_x = %s, start_y = %s, x = %s, y = %s, rotate = %s)",
                        slotType,
                        item.id,
                        prop.gid or "无",
                        moveNum, -- 这里打印的是快照移动的数量
                        space,
                        pbLocation.start_x,
                        pbLocation.start_y,
                        pbLocation.x,
                        pbLocation.y,
                        pbLocation.rotate
                    ))
                    end
                end
            end
        end

        -- 拿到原始gid
        local allQuickOperationData = Module.ArmedForce:GetAllQuickOperationDataInfo()
        for id, quickOperationData in pairs(allQuickOperationData) do
            fFindItemGids(id)
        end

        -- 容器道具快照
        for _, slotType in ipairs(OutfitDataLogic.ContainerTypeList) do
            fCopySlotProps(slotType)
        end

        if not table.isempty(snapshots) then
            Server.ArmedForceServer:ReqDepositAssemblySyncBodyContainer(snapshots, fOnDepositAssemblySyncBodyContainerCallback)
        else
            fOnDepositAssemblySyncBodyContainerCallback()
        end
    else
        logerror("[OutfitDataLogic] 应用方案 步骤3 物资处理 3.1 SyncBodyContainerProgress 快照 容器还未准备好，方案推荐失败！！！")
        OutfitDataLogic.ApplyOutfitError(nil, 3.1)
    end
end

-- 3.2 购买物资
OutfitDataLogic.BuyOperateContainerItemsProgress = function()
    local function fOnBuyOperateContainerItemsCallback(bSuccess)
        LogAnalysisTool.DoSendPresetPriceFluctuationFlowLog()
        Timer.DelayCall(0.1, function () -- 做一个延时，因为购买的回包和仓库的ntf可能会后收到，做延时保证仓库数据及时刷新了
            logwarning("[OutfitDataLogic] 应用方案 步骤3 物资处理 3.2 购买物资回包", bSuccess == nil and "无bSuccess" or bSuccess)
            logwarning("[OutfitDataLogic] 应用方案 步骤3 物资处理 结束 ！！！*************")
            if bSuccess then
                -- =4= 关闭界面清除数据**********************************
                OutfitDataLogic.FinishOperationOutfit()
            else
                OutfitDataLogic.ApplyOutfitError(nil, 3.2)
            end
        end)
    end

    logwarning("[OutfitDataLogic] 应用方案 步骤3 物资处理 3.2 BuyOperateContainerItemsProgress 购买物资+++++++++++++++++++++++++++")

    local cheapBuyItems = {}

    -- 处理购买的
    local allQuickOperationDataInfo = Module.ArmedForce.Field:GetAllQuickOperationDataInfo()
    for id, quickOperationDataInfo in pairs(allQuickOperationDataInfo) do
        local totalBuyNum = quickOperationDataInfo:GetBuyNum()
        if totalBuyNum > 0 then
            local allMatchItems = Server.InventoryServer:GetItemsById(id, ESlotGroup.OutFit)
            for index, item in ipairs(allMatchItems) do
                local quickOperationItemData = quickOperationDataInfo:GetItemDataByGid(item.gid)
                if quickOperationItemData then -- 有关联的
                    local buyNum = quickOperationItemData.buyNum
                    if buyNum > 0 then
                        local pbLocation = item:GetRawPropInfo().loc

                        local findCheapBuyItem = table.find(cheapBuyItems, function (v, k)
                            return v.id == id and v.targetSlotType == pbLocation.pos
                        end)

                        if findCheapBuyItem then
                            local buyLocation = {
                                num = buyNum,
                                location = pbLocation,
                            }
                            findCheapBuyItem.num = findCheapBuyItem.num + buyNum
                            table.insert(findCheapBuyItem.buyLocations, buyLocation)
                        else
                            local buyLocation = {
                                num = buyNum,
                                location = pbLocation,
                            }
                            local cheapBuyItem = {
                                id = id,
                                num = buyNum,
                                targetSlotType = pbLocation.pos,
                                buyLocations = {}
                            }
                            table.insert(cheapBuyItem.buyLocations, buyLocation)
                            table.insert(cheapBuyItems, cheapBuyItem)
                        end
                        logwarning(string.format("[OutfitDataLogic] 应用方案 步骤3 物资处理 3.2 BuyOperateContainerItemsProgress 购买物资 容器道具数据：槽位 = %s, id = %s, buyNum = %s, space = %s, pbLocation = (start_x = %s, start_y = %s, x = %s, y = %s, rotate = %s)",
                            pbLocation.pos,
                            id,
                            buyNum,
                            pbLocation.space_id,
                            pbLocation.start_x,
                            pbLocation.start_y,
                            pbLocation.x,
                            pbLocation.y,
                            pbLocation.rotate
                        ))
                    end
                end
            end
        end
    end

    OutfitDataLogic.BuyOp(cheapBuyItems, fOnBuyOperateContainerItemsCallback)
end


-- 4 关闭界面清除数据
OutfitDataLogic.FinishOperationOutfit = function()
    logwarning("[OutfitDataLogic] 应用方案 步骤4 关闭界面清除数据 4 FinishOperationOutfit 应用顺利成功！！！ 关闭界面清除数据+++++++++++++++++++++++++++")
    Server.InventoryServer:ClearSlotGroup(ESlotGroup.OutFit)
    Module.ArmedForce.Field:ClearMapPos2FitSampleItem()
    Module.ArmedForce.Config.evtApplyOutfitFinished:Invoke() 
    logwarning("[OutfitDataLogic] 应用方案 步骤4 关闭界面清除数据 4 FinishOperationOutfit 关闭界面清除数据 结束!!!")
end
--#endregion ==============================================应用方案========================================================

--#region ==============================================应用方案细节========================================================
-- 整理出待卸下、待装备、待购买后装备列表
OutfitDataLogic.GenOperateList = function()
    local operateList = {
        unEquip = {},--gid->num
        equip = {},--gid->num , 子弹id->num
        buy = {},-- id->num
        carry = {}, --id->num
        buyAndCarry = {},-- id->num
        buyAndAssembly = {},
    }
    for _, slotType in ipairs(OutfitDataLogic.OutfitTypeList) do
        local presetData = Module.ArmedForce.Field:GetMapPos2FitPresetData(slotType) -- 推荐数据
        local playerSlot = Server.InventoryServer:GetSlot(slotType) -- 本地数据
        local equipItem_player = playerSlot:GetEquipItem()
        if presetData then
            if ItemHelperTool.IsSOLWeaponSlotType(slotType) then -- 武器特殊处理
                if presetData.weaponData.propInfo then
                    local targetGid = 0
                    if not presetData.weaponData.curPropInfo.bNeedBuy then -- 仓库的武器
                        targetGid = presetData.weaponData.curPropInfo.gid
                        if equipItem_player and targetGid == equipItem_player.gid then
                            logwarning(string.format("[OutfitDataLogic] 应用方案 生成数据 武器处理 当前槽位[%s], 推荐的武器和找到的武器一致[%s] gid = %s, equipItem_player.gid = %s", slotType, equipItem_player.name, targetGid, equipItem_player.gid))
                        else
                            operateList.equip[slotType] = {gid = presetData.weaponData.curPropInfo.gid, num = 1}
                        end
                    else
                        operateList.buy[slotType] = {id = presetData.weaponData.propInfo.id, num = 1}
                    end

                    if not table.isempty(presetData.weaponData.goodsList) then
                        for socketGUID, goods in pairs(presetData.weaponData.goodsList) do
                            local id = goods:GetItemId()
                            local mainType = ItemHelperTool.GetMainTypeById(id)
                            if mainType == EItemType.Adapter then
                                table.insert(operateList.buyAndAssembly, {id = id, num = 1, pos_guid = socketGUID, target_gid = targetGid, target_id = presetData.weaponData.propInfo.id, target_buy_gid = slotType})
                            end
                        end
                    end
                else
                    if equipItem_player then
                        operateList.unEquip[slotType] = {gid = equipItem_player.gid, num = equipItem_player.num}
                    end
                end
            else
                local invItemData = presetData.invPresetItems[1]
                if slotType == ESlotType.BulletLeft or slotType == ESlotType.BulletRight then
                    if presetData.invNum > 0 then
                        local inventoryItem = Server.InventoryServer:GetItemByGid(invItemData.gid, ESlotGroup.Player)
                        local id = inventoryItem.id
                        operateList.equip[slotType] = {id = id, num = presetData.invNum}
                    end
                    if presetData.needBuyNum > 0 then
                        local item = presetData.goods:GetItem()
                        operateList.buy[slotType] = {id = item.id, num = presetData.needBuyNum}
                    end
                else
                    if equipItem_player and ((invItemData and equipItem_player.gid ~= invItemData.gid) or (presetData.invNum == 0 and presetData.needBuyNum == 0)) then
                        operateList.unEquip[slotType] = {gid = equipItem_player.gid, num = equipItem_player.num}
                    end

                    if equipItem_player and invItemData and equipItem_player.gid == invItemData.gid and not ItemHelperTool.IsSOLWeaponSlotType(slotType) then -- gid一样不用管

                    elseif invItemData then -- gid不一样==》装备
                        operateList.equip[slotType] = {gid = invItemData.gid, num = invItemData.num}
                    elseif presetData.goods and presetData.needBuyNum > 0 then -- 购买并装备
                        local buyData
                        if slotType == ESlotType.Helmet or slotType == ESlotType.BreastPlate then
                            local equipPosition = Module.ArmedForce.Field:GetCurOperationOutfitEquipPositionBySlotType(slotType)
                            if equipPosition then
                                local propInfo = equipPosition.load_props[1]
                                if propInfo and propInfo.health_max then
                                    local durabilitylvl = Server.AuctionServer:GetArmorDurabilitylvlByDurability(propInfo.id, propInfo.health_max)
                                    if durabilitylvl > 0 then
                                        logwarning("OutfitDataLogic.GenOperateList 所需购买的护具 所选的耐久档次信息", propInfo.id, propInfo.health_max, durabilitylvl)
                                        buyData = {id = propInfo.id, num = presetData.needBuyNum, durabilitylvl = durabilitylvl}
                                        operateList.buy[slotType] = buyData
                                    else
                                        logerror("OutfitDataLogic.GenOperateList 所需购买的护具没 durabilitylvl 信息", propInfo.id, propInfo.health_max)
                                    end
                                else
                                    logerror("OutfitDataLogic.GenOperateList 所需购买的护具没 propInfo 或 propInfo.health_max 信息", propInfo, propInfo and propInfo.health_max)
                                end
                            else
                                logerror("OutfitDataLogic.GenOperateList 所需购买的护具没 equipPosition 信息")
                            end
                        else
                            buyData = {id = presetData.goods:GetItemId(), num = presetData.needBuyNum}
                            operateList.buy[slotType] = buyData
                        end
                        
                    end
                end
            end
        else
            if equipItem_player then
                operateList.unEquip[slotType] = {gid = equipItem_player.gid, num = equipItem_player.num}
            end
        end
    end
    return operateList
end

-- 单个改枪请求
OutfitDataLogic.PDoAssembleGunReq = function(finalPreset)
    return Promise:NewIns(function(resovle, fReject)
        local fOnResCallback = function(res, gun)
            resovle(res)
        end
        Server.GunsmithServer:CSWAssemblyDepositPropUpdateReq(finalPreset, fOnResCallback, nil, nil, 0, nil, nil, nil, false)
    end)
end

-- 实际购买逻辑
OutfitDataLogic.BuyOp = function(cheapBuyItems, fCallback)
    if not table.isempty(cheapBuyItems) then
        LogAnalysisTool.SetPresetPriceFluctuationFlowValueByStr("PresetScene", PresetScene.PresetScene_Recommend)
        Module.ComparePrice:DoCheapBuyBatch(cheapBuyItems, CSCheapBuyScene.CheapBuyScene_PresetRecommend, fCallback)
    else
        if fCallback then
            fCallback(true)
        end
    end
end

--#endregion ==============================================应用方案细节========================================================

--#region ==============================================记录容器道具数据========================================================
OutfitDataLogic.RecordOutfitQuickOperationDatas = function(subViewType)
    local function fSlotPropsProcess(slotType)
        local operationSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.Preset)
        local newEquipPosition = {
            position = slotType,
            load_props = {}
        }
        if operationSlot then
            local items = operationSlot:GetItems()
            if items and not table.isempty(items) then
                for _, item in pairs(items) do
                    local propInfo = item:GetRawPropInfo()
                    table.insert(newEquipPosition.load_props, propInfo)
                end
            end
        end
        OutfitDataLogic.UpdateOutfitEquipPosition(newEquipPosition, false)
    end
    
    local function fWeaponSlotBulletPropsProcess(slotType)
        local operationSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.Preset)
        local weaponItem = operationSlot:GetEquipItem()
        local equipPosition = Module.ArmedForce.Field:GetCurOperationOutfitEquipPositionBySlotType(slotType)
        if equipPosition and equipPosition.load_props[1] and weaponItem then
            local weaponPropInfo = equipPosition.load_props[1]
            weaponPropInfo.weapon.load_bullets = {}
            local bullets = WeaponAssemblyTool.GetWeaponBullets(weaponItem:GetRawPropInfo())
            deepcopy(weaponPropInfo.weapon.load_bullets,bullets)
        end
        OutfitDataLogic.UpdateOutfitEquipPosition(equipPosition, false)
    end

    for _, slotType in ipairs(OutfitDataLogic.ContainerTypeList) do
        fSlotPropsProcess(slotType)
    end
    if subViewType == Module.ArmedForce.Config.ESubViewType.Bullet then
        for _, slotType in ipairs(OutfitDataLogic.WeaponTypeList) do
            fWeaponSlotBulletPropsProcess(slotType)
        end
    end
    Module.ArmedForce.Config.evtFinishQuickOperationData:Invoke()
end
--#endregion ==============================================记录容器道具数据========================================================

--#region ==============================================生成CurOperationOutfit数据========================================================
-- 第一步：生成推荐配装outfit方案（系统推荐的要本地生成，自定义或上次入局的直接copy）
OutfitDataLogic.GenCurOperationOutfit = function (outfitID)
    local curOperationOutfit = {}

    ---@class pb_EquipPosition : ProtoBase
    ---@field public position number
    ---@field public load_props pb_PropInfo[]
    ---@field public capacity number
    ---@field public grid_space pb_GridSize[]
    ---@field public src_prop_id number
    curOperationOutfit.items = {}

    if outfitID > 0 then -- 系统方案
        Module.ArmedForce.Field:ClearMapGid2UsedNum()
        -- 处理装备
        for _, slotType in ipairs(OutfitDataLogic.OutfitTypeList) do
            local propInfo = OutfitDataLogic.TryGetSystemOutfitPropInfo_Equip(outfitID, slotType)
            local equipPosition = {
                position = slotType,
                load_props = {},
            }
            if propInfo then
                table.insert(equipPosition.load_props, propInfo)
            end
            table.insert(curOperationOutfit.items, equipPosition)
        end
        OutfitDataLogic.InitCustomOutfitArmor(curOperationOutfit.items)
        OutfitDataLogic.InitOutfitSafeBox(curOperationOutfit.items)

        OutfitDataLogic.TryGetSystemOutfitPropInfo_Container(outfitID, curOperationOutfit.items)

    elseif outfitID == 0 then -- 上次入局方案
        local LoadLastEquipment = Server.PresetServer:GetSOLArmedForceLoadLastEquipment()
        deepcopy(curOperationOutfit.items, LoadLastEquipment)
        OutfitDataLogic.InitOutfitSafeBox(curOperationOutfit.items)
        OutfitDataLogic.TrySetOutfitPropInfoGid_Container(curOperationOutfit)
    elseif outfitID < 0 then -- 自定义方案 
        local index = -outfitID - 1
        local customOutfit = Server.PresetServer:GetSOLArmedForceOutfit(index)
        deepcopy(curOperationOutfit.items, customOutfit.items)
        -- 处理护具耐久度档次
        OutfitDataLogic.InitCustomOutfitArmor(curOperationOutfit.items, customOutfit.durability_lvls)
        OutfitDataLogic.InitOutfitSafeBox(curOperationOutfit.items)
        OutfitDataLogic.TrySetOutfitPropInfoGid_Container(curOperationOutfit)
    end
    -- 看下需不需要排序
    table.sort(curOperationOutfit, function (a, b)
        return a.position < b.position
    end)
    return curOperationOutfit
end

-- 非系统方案，容器下的道具生成gid等操作
OutfitDataLogic.TrySetOutfitPropInfoGid_Container = function (curOperationOutfit)
    local function fSetItemGid(slotType)
        local equipPosition = table.find(curOperationOutfit.items, function (v, k)
            return v.position == slotType
        end)
        if equipPosition then
            for _, propInfo in ipairs(equipPosition.load_props) do
                propInfo.gid = GetGid()
                local itemMainType = ItemHelperTool.GetMainTypeById(propInfo.id)
                if itemMainType == EItemType.Medicine then
                    local ItemHealthInfo = ItemConfigTool.GetHealthInfoById(propInfo.id)
                    if ItemHealthInfo ~= nil then
                        propInfo.health = ItemHealthInfo.Durability
                        propInfo.health_max = ItemHealthInfo.Durability
                    else
                        logerror("OutfitDataLogic.TrySetOutfitPropInfoGid_Container ItemHealthInfo is nil", propInfo.id)
                    end
                end
            end
        end
    end
    for _, slotType in ipairs(OutfitDataLogic.ContainerTypeList) do
        fSetItemGid(slotType)
    end
end

-- 生成安全箱（仓库外显，方案数据）
OutfitDataLogic.InitOutfitSafeBox = function (equipPositions)
    local containerItemId = nil
    -- 处理安全箱装备
    local equipPosition = table.find(equipPositions, function (v, k)
        return v.position == ESlotType.SafeBox
    end)
    if not equipPosition then
        equipPosition = {
            position = ESlotType.SafeBox,
            load_props = {},
        }
        table.insert(equipPositions, equipPosition)
    else
        equipPosition.load_props = {}
    end
    local safeBoxSlot = Server.InventoryServer:GetSlot(ESlotType.SafeBox, ESlotGroup.Player)
    if safeBoxSlot then
        local equipItem = safeBoxSlot:GetEquipItem()
        if equipItem then
            local itemFeature = equipItem:GetFeature(EFeatureType.Equipment)
            if not itemFeature:GetExpiredStatus() then
                containerItemId = equipItem.id
                local prop = {}
                deepcopy(prop, equipItem:GetRawPropInfo())
                table.insert(equipPosition.load_props, prop)
            end
        end
    end

    -- 处理安全箱容器空间道具
    local safeBoxContainerEquipPosition = table.find(equipPositions, function (v, k)
        return v.position == ESlotType.SafeBoxContainer
    end)
    if safeBoxContainerEquipPosition and not table.isempty(safeBoxContainerEquipPosition.load_props) then
        if containerItemId then
            --创建一个临时的安全箱容器空间临时槽位
            local tempSlot = ItemSlot:New(ESlotType.SafeBoxContainer, ESlotGroup.None, containerItemId)
            if tempSlot then
                local index = #safeBoxContainerEquipPosition.load_props
                while index > 0 do
                    local propInfo = safeBoxContainerEquipPosition.load_props[index]
                    local pbLocation = propInfo.loc
                    if not OutfitDataLogic.CheckLocationValidInSlot(pbLocation, tempSlot) then -- 如果位置不合法就移除
                        table.removebyvalue(safeBoxContainerEquipPosition.load_props, propInfo)
                    end
                    index = index - 1
                end
            end
        else
            safeBoxContainerEquipPosition.load_props = {}
            logerror("OutfitDataLogic.InitOutfitSafeBox containerItemId is nil")
        end
    end
end

-- 生成门卡（仓库数据只是为了同步负重）
OutfitDataLogic.InitOutfitKeyChainSlot = function ()
    local keyChainoutFitSlot = Server.InventoryServer:GetSlot(ESlotType.KeyChain, ESlotGroup.OutFit)
    keyChainoutFitSlot:ResetSlot()

    local keyChainContaineroutFitSlot = Server.InventoryServer:GetSlot(ESlotType.KeyChainContainer, ESlotGroup.OutFit)
    keyChainContaineroutFitSlot:ResetSlot()


    local keyChainSlot = Server.InventoryServer:GetSlot(ESlotType.KeyChain, ESlotGroup.Player)
    if keyChainSlot then
        local equipItem = keyChainSlot:GetEquipItem()
        if equipItem then
            local propInfo = {}
            deepcopy(propInfo, equipItem:GetRawPropInfo())
            local item = ItemBase:New(equipItem.id)
            item:SetRawPropInfo(propInfo)
            item:AddToSlot(keyChainoutFitSlot, false)

            Server.InventoryServer:AddItemToList(item, keyChainoutFitSlot)
        end
    end

    local keyChainContainerSlot = Server.InventoryServer:GetSlot(ESlotType.KeyChainContainer, ESlotGroup.Player)
    if keyChainContainerSlot then
        local items = keyChainContainerSlot:GetItems()
        if items then
            for _, key in ipairs(items) do
                local propInfo = {}
                deepcopy(propInfo, key:GetRawPropInfo())
                local item = ItemBase:New(key.id)
                item:SetRawPropInfo(propInfo)
                item:AddToSlot(keyChainContaineroutFitSlot, false)
                Server.InventoryServer:AddItemToList(item, keyChainContaineroutFitSlot)
            end
        end
    end
end

-- 根据护具的耐久度档次处理护具的耐久度
OutfitDataLogic.InitCustomOutfitArmor = function (items, durability_lvls)
    local durability_lvl_map = {}
    if durability_lvls then
        for _, equipDurabilityLvl in ipairs(durability_lvls) do
            durability_lvl_map[equipDurabilityLvl.id] = equipDurabilityLvl.level
        end
    end

    for _, equipPosition in ipairs(items) do
        local pos = equipPosition.position
        if pos == ESlotType.Helmet or pos == ESlotType.BreastPlate then
            local propInfo = equipPosition.load_props[1]
            if propInfo then
                local id = propInfo.id
                local durability
                if durability_lvl_map[id] then
                    durability = Server.AuctionServer:GetArmorDurability(id, durability_lvl_map[id]) or 0
                else
                    durability = Server.AuctionServer:GetArmorDurability(id, 1) or 0
                end
                -- 多加一层检查，如果是拍卖行不上架的道具则显示满耐久
                if durability == 0 then
                    local armorFunctionTable = nil
                    if pos == ESlotType.Helmet then
                        armorFunctionTable = ItemConfigTool.GetHelmetArmorFunctionById(id)
                    elseif pos == ESlotType.BreastPlate then
                        armorFunctionTable = ItemConfigTool.GetBodyArmorFunctionById(id)
                    end

                    if armorFunctionTable then
                        durability = armorFunctionTable.MaxDurability
                    end
                end
                propInfo.health = durability
                propInfo.health_max = durability
            end
        end
    end
end

OutfitDataLogic.CheckLocationValidInSlot = function (pbLocation, slot)
    ---@class pb_PropLocation : ProtoBase
    ---@field public pos number
    ---@field public space_id number
    ---@field public start_x number
    ---@field public start_y number
    ---@field public x number
    ---@field public y number
    ---@field public rotate boolean
    ---@field public is_newly_get boolean
    local space = pbLocation.space_id
    local rotate = pbLocation.rotate
    local x = pbLocation.start_x
    local y = pbLocation.start_y
    local length = 0
    local height = 0
    if rotate then
        length = pbLocation.y
        height = pbLocation.x
    else
        length = pbLocation.x
        height = pbLocation.y
    end
    local spaceLength, spaceHeight = slot:GetSlotSize(space)
    return length + x <= spaceLength and height + y <= spaceHeight
end

--#endregion ==============================================生成CurOperationOutfit数据========================================================

--#region ==============================================推荐道具到槽位上========================================================
-- 第二步：根据curOperationOutfit方案尝试添加装备
OutfitDataLogic.UsedCurOperationOutfit = function (curOperationOutfit, bRefreshPresetData)
    bRefreshPresetData = setdefault(bRefreshPresetData, true)
    -- 生成仓库推荐数据
    ArmedForceDataLogic.GenInvPresetItemDataMap()
    -- 清空推荐数据
    Module.ArmedForce.Field:ClearMapPos2FitPresetData()
    -- 清空QuickOperationDataInfo数据
    Module.ArmedForce:ClearQuickOperationDataInfo()
    -- 清空gid使用数据
    Module.ArmedForce.Field:ClearMapGid2UsedNum()
    -- 避免不必要的提示
    Module.ArmedForce.Field:SetShowQuickOperationTipsed(true)
    if not curOperationOutfit then
        return
    end
    -- 1重置装备槽位
    for _, slotType in ipairs(OutfitDataLogic.OutfitTypeList) do
        local outFitSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.OutFit)
        outFitSlot:ResetSlot()
    end
    -- 重置safebox
    local safeBoxoutFitSlot = Server.InventoryServer:GetSlot(ESlotType.SafeBox, ESlotGroup.OutFit)
    safeBoxoutFitSlot:ResetSlot()

    -- 2重置容器槽位
    for _, slotType in ipairs(OutfitDataLogic.ContainerTypeList) do
        -- 先重置槽位
        local outFitSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.OutFit)
        outFitSlot:ResetSlot()

        local containerType = outFitSlot:GetContainerType()
        local equipPosition = table.find(curOperationOutfit.items, function (v, k)
            return v.position == containerType
        end)
        if slotType == ESlotType.Pocket then
            outFitSlot:InitPocketContainer()
        -- elseif slotType == ESlotType.SafeBoxContainer then
        --     local playerEquipContainerSlot = Server.InventoryServer:GetSlot(containerType)
        --     local equipment = playerEquipContainerSlot:GetEquipItem()
        --     if equipment then
        --         outFitSlot:InitContainerByEquipmentId(equipment.id)
        --     end

        else
            if equipPosition then
                local propinfo = equipPosition.load_props[1]
                if propinfo then 
                    local equipId = propinfo.id
                    outFitSlot:InitContainerByEquipmentId(equipId)
                end
            end
        end
    end

    local items = curOperationOutfit.items
    -- 武器方案数据
    local weaponEquipPositions = {}
    -- 根据方案数据生成PresetData
    for _, equipPosition in ipairs(items) do
        if table.contains(OutfitDataLogic.WeaponTypeList, equipPosition.position) and not table.isempty(equipPosition.load_props) then -- 如果是武器类的话，先存起来一起操作
            table.insert(weaponEquipPositions, equipPosition)
        else --推荐非武器类
            OutfitDataLogic.TryUsedOutfitByEquipPosition(equipPosition)
        end
    end
    if not table.isempty(weaponEquipPositions) then
        -- 处理武器数据
        -- 配装重构了改枪逻辑，改枪数据交给了改枪台处理
        OutfitDataLogic.TryUsedOutfitByEquipPosition_Weapons(weaponEquipPositions)
    end
    
    -- 根据缓存的PresetData推荐到槽位上
    if bRefreshPresetData then
        OutfitDataLogic.PresetMapPos2FitPresetData()
    end
end

-- 根据方案生成武器的PresetData
OutfitDataLogic.TryUsedOutfitByEquipPosition_Weapons = function (weaponEquipPositions)

    local usedGidMap = {} -- 记录机匣是否已使用
    local targetPropInfos = {}
    local curPropInfos = {}
    for _, equipPosition in ipairs(weaponEquipPositions) do
        local targetPropInfo = equipPosition.load_props[1]
        -- 处理目标propInfos
        table.insert(targetPropInfos, targetPropInfo)
        
        -- 处理当前propInfos
        local curPropInfo = nil
        local invItems = Server.InventoryServer:GetItemsById(targetPropInfo.id, ESlotGroup.Player)
        if invItems and not table.isempty(invItems) then
            local similarityList = {}
            for key, itembase in pairs(invItems) do
                if not usedGidMap[itembase.gid] then
                    local bResult, type = OutfitDataLogic.CheckWeaponCondition(itembase:GetRawPropInfo(), targetPropInfo)
                    if bResult then
                        logwarning("[OutfitDataLogic] TryUsedOutfitByEquipPosition_Weapons 匹配的武器", itembase.id, itembase.name, itembase.gid, bResult, type)
                        table.insert(similarityList, {item = itembase, similarity = type})
                    end
                    -- local similarity = OutfitDataLogic.GetWeaponPropInfoSimilarity(itembase:GetRawPropInfo(), targetPropInfo)
                    -- table.insert(similarityList, {item = itembase, similarity = similarity})
                end
            end
            if not table.isempty(similarityList) then
                table.sort(similarityList, function (a, b)
                    return a.similarity > b.similarity
                end)
                local similarityData = similarityList[1]
                curPropInfo = similarityData.item:GetRawPropInfo()
                usedGidMap[curPropInfo.gid] = true
            else -- 仓库找不到合适的，需要购买机匣了
                local goods = Module.ArmedForce.Field:GetMapArmedForceGoods(targetPropInfo.id)
                curPropInfo = goods:GetItem():GetRawPropInfo()
                curPropInfo.bNeedBuy = true
            end
        else -- 仓库找不到合适的，需要购买机匣了
            local goods = Module.ArmedForce.Field:GetMapArmedForceGoods(targetPropInfo.id)
            curPropInfo = goods:GetItem():GetRawPropInfo()
            curPropInfo.bNeedBuy = true
        end
        if curPropInfo then
            table.insert(curPropInfos, curPropInfo)
        else
            logerror("OutfitDataLogic.TryUsedOutfitByEquipPosition_Weapons 找不到curPropInfo", targetPropInfo.id, equipPosition.position)
        end
    end

    if #targetPropInfos ~= #curPropInfos then
        logerror("OutfitDataLogic.TryUsedOutfitByEquipPosition_Weapons #targetPropInfos ~= #curPropInfos 长度不一致")
        return
    end

    local bSuccess, newPropInfos, lockPartsInfos = Module.Gunsmith:GetLocksPartsFromPropInfos(curPropInfos, targetPropInfos, ESlotGroup.Player, true, false)

    if bSuccess and #newPropInfos == #lockPartsInfos and #newPropInfos == #weaponEquipPositions then
        for i, equipPosition in ipairs(weaponEquipPositions) do
            local slotType = equipPosition.position
            local curPropInfo = curPropInfos[i] -- 如果是仓库的则会保留gid信息
            local targetPropInfo = targetPropInfos[i] -- 方案原数据
            local newPropInfo = newPropInfos[i] -- 通过改枪台应用后的预览propinfo
            local lockPartsInfo = lockPartsInfos[i]
            local id = newPropInfo.id
            local needItemNum = newPropInfo.num
            local needBuyNum = needItemNum
            local fitPresetData = nil
            if newPropInfo then
                fitPresetData = 
                {
                    id = id,
                    invPresetItems = {},
                    invNum = 0,
                    goods = nil,
                    needBuyNum = 0,
                    needNum = needItemNum,
                    weaponData = { -- 武器特有
                        propInfo = nil,
                        curPropInfo = curPropInfo, -- 武器原propInfo
                        bCanBuyAll = true,
                        goodsList = {}
                    },
                }
                local goodsList = {}
                if curPropInfo.bNeedBuy then -- 如果绑定id不是玩家本身则需要购买（gid不能判断了，都是有效值）
                    lockPartsInfo[0] = curPropInfo.id
                    loginfo('[OutfitDataLogic] 推荐配装 #武器#-------------来源【购买】', ' |道具id:', curPropInfo.id, ' |道具Gid:', curPropInfo.gid)
                else
                    loginfo('[OutfitDataLogic] 推荐配装 #武器#-------------来源【仓库】', ' |道具id:', curPropInfo.id, ' |道具Gid:', curPropInfo.gid)
                end
                local itemName = ItemConfigTool.GetItemName(curPropInfo.id)
                loginfo('[OutfitDataLogic] 推荐配装 #武器#-------------', '|槽位:', slotType, ' |道具名:', itemName, ' |道具id:', curPropInfo.id, ' |道具Gid:', curPropInfo.gid)
                loginfo('【')
                for socketGUID, id in pairs(lockPartsInfo) do
                    local itemName = ItemConfigTool.GetItemName(id)
                    local goods = Module.ArmedForce.Field:GetMapArmedForceGoods(id)
                    goodsList[socketGUID] = goods
                    loginfo('[OutfitDataLogic] 推荐配装 #武器缺失配件#-------------', '|socketGUID:', socketGUID, ' |道具名:', itemName, ' |道具id:', id)
                end
                loginfo('】')

                -- 处理方案以外的数据
                OutfitDataLogic._WeaponPropInfoOtherDataProcess(newPropInfo, targetPropInfo)

                -- fitPresetData.weaponData.bCanBuyAll = true --bCanBuyAll
                fitPresetData.weaponData.curPropInfo = curPropInfo
                fitPresetData.weaponData.propInfo = newPropInfo
                fitPresetData.weaponData.goodsList = goodsList
            end
            Module.ArmedForce.Field:SetMapPos2FitPresetData(slotType, fitPresetData)
            -- -- 初始化武器子弹的QuickOperation
            -- if ItemHelperTool.IsSOLWeaponSlotType(slotType) and (fitPresetData and fitPresetData.weaponData and fitPresetData.weaponData.propInfo) then
            --     -- 处理武器子弹
            --     OutfitDataLogic.UnloadWeaponBullets(fitPresetData.weaponData)
            --     OutfitDataLogic.FillWeaponBullets(fitPresetData.weaponData)
            -- end
        end

        -- 卸下全部推荐仓库武器的子弹
        for i, equipPosition in ipairs(weaponEquipPositions) do
            local slotType = equipPosition.position
            local fitPresetData = Module.ArmedForce.Field:GetMapPos2FitPresetData(slotType)
            -- 初始化武器子弹的QuickOperation
            if fitPresetData and fitPresetData.weaponData and fitPresetData.weaponData.propInfo then
                -- 处理武器子弹
                OutfitDataLogic.UnloadWeaponBullets(fitPresetData.weaponData)
            end
        end
        for i, equipPosition in ipairs(weaponEquipPositions) do
            local slotType = equipPosition.position
            local fitPresetData = Module.ArmedForce.Field:GetMapPos2FitPresetData(slotType)
            -- 初始化武器子弹的QuickOperation
            if fitPresetData and fitPresetData.weaponData and fitPresetData.weaponData.propInfo then
                -- 处理武器子弹
                OutfitDataLogic.FillWeaponBullets(fitPresetData.weaponData)
            end
        end
    end
end

-- 检查仓库武器是否满足条件：
-- 1.仓库里的武器是标准预设
-- 2.仓库里的武器配件方案和目标的武器方案一致的
OutfitDataLogic.CheckWeaponCondition = function (scrPropInfo, destPropInfo)
    if not scrPropInfo then
        logerror("OutfitDataLogic.CheckWeaponCondition scrPropInfo is nil")
        return false
    elseif not destPropInfo then
        logerror("OutfitDataLogic.CheckWeaponCondition destPropInfo is nil")
        return false
    end


    local id = destPropInfo.id -- 机匣id
    logwarning("[OutfitDataLogic] CheckWeaponCondition id = ", id)

    local lhs, rhs
    lhs = WeaponAssemblyTool.PropInfo_To_Desc(scrPropInfo)

    -- 判断是否标准预设
	local gunItem = WeaponAssemblyTool.GetPresetPreviewGunFromRecId(id)
    if gunItem then
        rhs = gunItem:GetModularDesc()
        if lhs and rhs then
            local bEqual = WeaponHelperTool.CompareWeaponDescription(lhs, rhs, EModularWeaponDescCompare.SolutionCompare)
            if bEqual then
                return true, 1
            end
        else
            logerror("[OutfitDataLogic] CheckWeaponCondition 标准预设 lhs and rhs is nil")
        end
    else
        logerror("[OutfitDataLogic] CheckWeaponCondition gunItem is nil")
    end

    -- 判断是否与目标方案一致
    rhs = WeaponAssemblyTool.PropInfo_To_Desc(destPropInfo)
    if lhs and rhs then
        local bEqual = WeaponHelperTool.CompareWeaponDescription(lhs, rhs, EModularWeaponDescCompare.SolutionCompare)
        if bEqual then
            return true, 2
        end
    else
        logerror("[OutfitDataLogic] CheckWeaponCondition 目标方案 lhs and rhs is nil")
    end
    return false
end

OutfitDataLogic.GetWeaponPropInfoSimilarity = function (scrPropInfo, destPropInfo)
    local function fGetSimilarity(scrProp, destProp)
        local similarity = 0
        if scrProp.id == destProp.id then
            similarity = similarity + 1
        end
        for _, scrChild in ipairs(scrProp.components) do
            for _, destChild in ipairs(destProp.components) do
                if destChild.slot == scrChild.slot then
                    similarity = similarity + fGetSimilarity(scrChild.prop_data, destChild.prop_data)
                end
            end
        end
        return similarity
    end
    local similarity = fGetSimilarity(scrPropInfo, destPropInfo)
    return similarity
end

-- 处理改枪台方案应用以外的数据
OutfitDataLogic._WeaponPropInfoOtherDataProcess = function (newPropInfo, targetPropInfo)
    -- 拷贝子弹信息
    deepcopy(newPropInfo.weapon.load_bullets, targetPropInfo.weapon.load_bullets)
    -- -- 拷贝loc信息
    -- newPropInfo.loc = {}
    -- newPropInfo.position = targetPropInfo.position
    -- deepcopy(newPropInfo.loc, targetPropInfo.loc)
end

-- 根据方案生成PresetData
OutfitDataLogic.TryUsedOutfitByEquipPosition = function (equipPosition)
    local slotType = equipPosition.position
    if table.contains(OutfitDataLogic.OutfitTypeList, slotType) then
        OutfitDataLogic.TryUsedOutfitByEquipPosition_Equip(equipPosition)
    elseif table.contains(OutfitDataLogic.ContainerTypeList, slotType) then
        OutfitDataLogic.TryUsedOutfitByEquipPosition_Container(equipPosition)
    elseif slotType == ESlotType.SafeBox then
        OutfitDataLogic.TryUsedOutfitByEquipPosition_SafeBox(equipPosition)
    end
end

-- 根据方案生成装备的PresetData
OutfitDataLogic.TryUsedOutfitByEquipPosition_Equip = function (equipPosition)
    local slotType = equipPosition.position
    local fitPresetData = nil
    local propInfo = equipPosition.load_props[1]
    if propInfo then
        -- 根据propInfo生成fitPresetData
        fitPresetData = OutfitDataLogic.TryGetFitPresetDataProcessByPropInfo(propInfo, slotType)
    end
    Module.ArmedForce.Field:SetMapPos2FitPresetData(slotType, fitPresetData)
end

-- 根据方案生成装备的PresetData
OutfitDataLogic.TryUsedOutfitByEquipPosition_SafeBox = function (equipPosition)
    local slotType = equipPosition.position
    local fitPresetData = nil
    local propInfo = equipPosition.load_props[1]
    if propInfo then
        fitPresetData = 
        {
            id = propInfo.id,
            invPresetItems = {},
            invNum = 1,
            goods = nil,
            needBuyNum = 0,
            needNum = 1,
        }
        local safeBoxSlot = Server.InventoryServer:GetSlot(ESlotType.SafeBox, ESlotGroup.Player)
        if safeBoxSlot then
            local equipItem = safeBoxSlot:GetEquipItem()
            if equipItem then
                table.insert(fitPresetData.invPresetItems, {gid = equipItem.gid, num = 1})
            end
        end
        -- -- 根据propInfo生成fitPresetData
        -- fitPresetData = OutfitDataLogic.TryGetFitPresetDataProcessByPropInfo(propInfo, slotType)
    end
    Module.ArmedForce.Field:SetMapPos2FitPresetData(slotType, fitPresetData)
end

--- 根据prpoInfo, 槽位获取推荐道具数据
---@param propInfo pb_PropInfo 道具
---@param slotType number 槽位
---@return table fitPresetData 推荐数据
OutfitDataLogic.TryGetFitPresetDataProcessByPropInfo = function(propInfo, slotType)
    local id = propInfo.id
    local needItemNum = propInfo.num
    local needBuyNum = needItemNum
    local fitPresetData = 
    {
        id = id,
        invPresetItems = {},
        invNum = 0,
        goods = nil,
        needBuyNum = 0,
        needNum = needItemNum,
        weaponData = ItemHelperTool.IsSOLWeaponSlotType(slotType) and { -- 武器特有
            propInfo = nil,
            bCanBuyAll = true,
            goodsList = {}
        } or nil,
    }
    if slotType then
        if ItemHelperTool.IsSOLWeaponSlotType(slotType) then
            local presetPropInfo = {}
            deepcopy(presetPropInfo, propInfo)
            OutfitDataLogic.GenPresetWeaponData(fitPresetData.weaponData, nil, presetPropInfo)
            if fitPresetData.weaponData.propInfo and fitPresetData.weaponData.bCanBuyAll then
                local itemName = ItemConfigTool.GetItemName(fitPresetData.weaponData.propInfo.id)
                loginfo('[OutfitDataLogic] 推荐配装 #武器#-------------', ' |道具名:', itemName, ' |道具id:', fitPresetData.weaponData.propInfo.id, ' |道具仓库Gid:', fitPresetData.weaponData.propInfo.InventoryGid, ' |是否能全买:', fitPresetData.weaponData.bCanBuyAll)
                if not table.isempty(fitPresetData.weaponData.goodsList) then
                    for socketGUID, goods in pairs(fitPresetData.weaponData.goodsList) do
                        local goodsName = ItemConfigTool.GetItemName(goods:GetItemId())
                        loginfo('[OutfitDataLogic] 推荐配装 #武器需要购买配件#-------------', ' |道具id:', goods:GetItemId(), ' |道具名:', goodsName, ' |道具数量:', 1)
                    end
                end
            end
        else

            local invItems = Module.ArmedForce.Field:GetMapPos2InvPresetItemDataByPosAndType(slotType, "Id")[id]

            if invItems then
                for _, item in ipairs(invItems) do
                    if OutfitDataLogic.CheckItemConditions(item, slotType) then
                        if needBuyNum > 0 then
                            local usedNum = Module.ArmedForce.Field:GetMapGid2UsedNum(item.gid)
                            local remainNum = item.num - usedNum
                            if remainNum > 0 then
                                local optNum = math.min(needBuyNum, remainNum)
                                table.insert(fitPresetData.invPresetItems, {gid = item.gid, num = optNum})
                                needBuyNum = needBuyNum - optNum
                                fitPresetData.invNum = fitPresetData.invNum + optNum
                                Module.ArmedForce.Field:SetMapGid2UsedNum(item.gid, optNum)
                                loginfo('[OutfitDataLogic] 推荐配装-------------仓库精准符合条件:', ' |槽位:', slotType, ' |道具id:',item.id, ' |道具名:',item.name, ' |道具gid:',item.gid, ' |道具数量:', optNum)
                            end
                        end
                    end
                end
            end
            if needBuyNum > 0 then
                local newGoods = Module.ArmedForce.Field:GetMapArmedForceGoods(id)
                if OutfitDataLogic.CheckItemConditions(newGoods:GetItem(), slotType) then
                    fitPresetData.needBuyNum = needBuyNum
                    fitPresetData.goods = newGoods
                    if fitPresetData.goods then
                        local item = fitPresetData.goods:GetItem()
                        loginfo('[OutfitDataLogic] 推荐配装----￥￥￥------商品精准符合条件:', ' |道具id:', item.id, ' |道具名字:', item.name, ' |道具数量:', needBuyNum, ' |道具MinPrice:', fitPresetData.goods:GetMinPrice())
                    else
                        loginfo('[OutfitDataLogic] 推荐配装----￥￥￥------没找到商品精准符合条件的道具！！！')
                    end
                end
            end
        end
    end
    return fitPresetData
end

---------------------------------------------------------------------------------
--- 生成武器预设（仓库的加入gid，需要购买的加入goods）
---------------------------------------------------------------------------------
OutfitDataLogic.GenPresetWeaponData = function (weaponData, assemblyPresetInfo, presetPropInfo)
    local propInfo
    if assemblyPresetInfo then
        local itemId = assemblyPresetInfo.ItemId
        local pos = assemblyPresetInfo.Position
        propInfo = WeaponAssemblyTool.PresetRow_to_PropInfo(itemId)
    else
        propInfo = presetPropInfo
    end
    if not propInfo then
        logerror("OutfitDataLogic.GenPresetWeaponData  propInfo为空！！！", itemId)
        return
    end
    local fSetUsedNum = CreateCallBack(Module.ArmedForce.Field.SetMapGid2UsedNum, Module.ArmedForce.Field)
    local fGetUsedNum = CreateCallBack(Module.ArmedForce.Field.GetMapGid2UsedNum, Module.ArmedForce.Field)
    local fSetBoughtNum = CreateCallBack(Module.ArmedForce.Field.SetMapId2BoughtNum, Module.ArmedForce.Field)
    local fGetBoughtNum = CreateCallBack(Module.ArmedForce.Field.GetMapId2BoughtNum, Module.ArmedForce.Field)
    local bCanRecommend, presetWeaponPropInfo, bCanBuyAll, goodsList = Module.ComparePrice:GenSOLPresetWeaponPropInfo(propInfo, true, nil, fSetUsedNum, fGetUsedNum, fSetBoughtNum, fGetBoughtNum)
    if bCanRecommend then
        weaponData.bCanBuyAll = bCanBuyAll
        weaponData.propInfo = presetWeaponPropInfo
        weaponData.goodsList = goodsList
        if presetWeaponPropInfo.InventoryGid == 0 and not presetWeaponPropInfo.goods then
            logerror("[OutfitDataLogic] GenPresetWeaponData", Module.ArmedForce.Config.Loc.MissingWeaponReceiverRecommendationFailed)
            Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.MissingWeaponReceiverRecommendationFailed, 2)
        elseif not bCanBuyAll then
            logerror("[OutfitDataLogic] GenPresetWeaponData", Module.ArmedForce.Config.Loc.MissingWeaponParts)
            Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.MissingWeaponParts)
        end
    end
    
end

-- 填充武器子弹
OutfitDataLogic.FillWeaponBullets = function(weaponData)
    local curPropInfo = weaponData.curPropInfo
    local weaponPropInfo = weaponData.propInfo
    local bullets = WeaponAssemblyTool.GetWeaponBullets(weaponPropInfo)
    if bullets and not table.isempty(bullets) then
        for _, bullet in ipairs(bullets) do
            local needNum = bullet.num
            while needNum > 0 do
                -- 一般只有一个
                -- local bullet = bullets[1]
                local quickOperationDataInfo = Module.ArmedForce.Field:GetQuickOperationDataInfo(bullet.id)
                if not quickOperationDataInfo then
                    quickOperationDataInfo = QuickOperationDataBulletStruct:NewIns(bullet.id)
                    Module.ArmedForce.Field:SetQuickOperationDataInfo(quickOperationDataInfo)
                end
                local maxStackCount = quickOperationDataInfo:GetMaxStackCount()
                local operateNum = QuickOperationLogic.TryGetOperateNum(quickOperationDataInfo, needNum, maxStackCount)
                local equipItem = {id = weaponPropInfo.id, gid = weaponPropInfo.gid, num = weaponPropInfo.num } -- 简易版item
                if operateNum ~= 0 then
                    needNum = needNum - operateNum
                    local remainDepositoryNum = quickOperationDataInfo:GetDepositoryNum()
                    if remainDepositoryNum > 0 then
                        local operateDepositoryNum = math.min(remainDepositoryNum, operateNum)
                        quickOperationDataInfo:CalculateWeaponBulletMoveNum(equipItem, operateDepositoryNum)
                        operateNum = operateNum - operateDepositoryNum
                    end
                    if operateNum > 0 then
                        quickOperationDataInfo:CalculateWeaponBulletBuyNum(equipItem, operateNum)
                    end
                else
                    quickOperationDataInfo:CalculateWeaponBulletBuyNum(equipItem, needNum)
                    logerror("[OutfitDataLogic] FillWeaponBullets", weaponPropInfo.id, bullet.id, bullet.num, needNum, operateNum)
                    break
                end
            end
        end
        
    end
end

-- 卸下武器子弹
OutfitDataLogic.UnloadWeaponBullets = function(weaponData)
    local curPropInfo = weaponData.curPropInfo
    if not curPropInfo.bNeedBuy then -- 仓库的武器
        -- 卸下原有的
        local playerWeapon = Server.InventoryServer:GetItemByGid(curPropInfo.gid, ESlotGroup.Player)
        if playerWeapon then
            local bullets = WeaponAssemblyTool.GetWeaponBullets(playerWeapon:GetRawPropInfo())
            for _, bullet in ipairs(bullets) do
                local operateNum = bullet.num
                local quickOperationDataInfo = Module.ArmedForce.Field:GetQuickOperationDataInfo(bullet.id)
                if not quickOperationDataInfo then
                    quickOperationDataInfo = QuickOperationDataBulletStruct:NewIns(bullet.id)
                    Module.ArmedForce.Field:SetQuickOperationDataInfo(quickOperationDataInfo)
                end

                if operateNum > 0  then
                    -- 先将数据添加到武器上
                    quickOperationDataInfo:AddWeaponBulletMoveNum(playerWeapon, operateNum)
                    local bulletItem = {id = bullet.id, gid = bullet.gid, num = bullet.num } -- 简易版item
                    -- 然后将数据从武器上移除（注意移除的数据会放到身上的，具体操作逻辑类似于快速配置界面）
                    quickOperationDataInfo:RemoveWeaponBulletMoveNum(playerWeapon, bulletItem, operateNum)
                    -- 最后将数据从身上移除回仓库
                    quickOperationDataInfo:CalculateMoveNum(bulletItem, -operateNum)
                end
            end
        end
        
    end
end

-- 根据方案生成容器下的quickOperationDataInfo
OutfitDataLogic.TryUsedOutfitByEquipPosition_Container = function (equipPosition)
    for _, propInfo in ipairs(equipPosition.load_props) do
        local id = propInfo.id
        local gid = propInfo.gid
        local itemMainType = ItemHelperTool.GetMainTypeById(id)
        local quickOperationDataInfo = Module.ArmedForce.Field:GetQuickOperationDataInfo(id)
        if not quickOperationDataInfo then
            local quickOperationDataStructClass = nil
            if itemMainType == EItemType.Medicine then
                quickOperationDataStructClass = QuickOperationDataMedicineStruct
            elseif  itemMainType == EItemType.Bullet then
                quickOperationDataStructClass = QuickOperationDataBulletStruct
            end
            quickOperationDataInfo = quickOperationDataStructClass:NewIns(id)
            Module.ArmedForce.Field:SetQuickOperationDataInfo(quickOperationDataInfo)
        end

        local operateItem = {id = propInfo.id, gid = gid, num = propInfo.num } -- 简易版item
        -- -- 能否被交易
        -- local bTransaction = Server.AuctionServer:CheckIsInSaleList(id)
        -- if bTransaction then
            QuickOperationLogic.CalculateOperateNum(quickOperationDataInfo, operateItem, operateItem.num)
            local quickOperationItemData = quickOperationDataInfo:GetItemDataByGid(gid)
            if quickOperationItemData then -- 有关联的
                local buyNum = quickOperationItemData.buyNum
                local bNeedBuy = buyNum > 0
                local bChanged = propInfo.bNeedBuy ~= bNeedBuy
                if bChanged then
                    propInfo.bNeedBuy = bNeedBuy
                end
            end
        -- else
        --     -- 异常不在售
        --     propInfo.bNeedBuy = true
        -- end
    end
end

--- 把推荐列表预设到插槽上*（新）
OutfitDataLogic.PresetMapPos2FitPresetData = function(inSlotType)
    if inSlotType then
        OutfitDataLogic._PresetFitPresetData_Equip(inSlotType)
    else
        Server.InventoryServer:ClearSlotGroup(ESlotGroup.OutFit)
        -- 设置装备武器
        for _, outfitSlotType in ipairs(OutfitDataLogic.OutfitTypeList) do
            OutfitDataLogic._PresetFitPresetData_Equip(outfitSlotType)
        end
        OutfitDataLogic._PresetFitPresetData_Equip(ESlotType.SafeBox)
        -- 设置keyChain
        OutfitDataLogic.InitOutfitKeyChainSlot()
        OutfitDataLogic._PresetFitQuickOperationData_Container()
    end
    Module.ArmedForce.Config.evtPresetFitSampleItemFinished:Invoke()
    Server.InventoryServer:CalculateAllCarryItemsWeight()
    CheckEquipLogic.CheckEquipmentBeforEnterGameProcess()
    -- 设置所在目标容器，只是用于显示，实际还是填胸挂，修改cmds 的auto_select_if_full = true字段，自动放置药品
end

-- 将装备presetdata推荐到槽位上
OutfitDataLogic._PresetFitPresetData_Equip = function (slotType)
    local presetData = Module.ArmedForce.Field:GetMapPos2FitPresetData(slotType)
    local targetSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.OutFit)
    if presetData and ((not presetData.weaponData and not table.isempty(presetData.invPresetItems) or presetData.goods) or (presetData.weaponData and presetData.weaponData.propInfo)) then
        if ItemHelperTool.IsSOLWeaponSlotType(slotType) then -- 武器
            local weaponData = presetData.weaponData
            local propInfo = weaponData.propInfo
            local item = ItemBase:New(propInfo.id)
            item:SetRawPropInfo(propInfo)
            local bullets = WeaponAssemblyTool.GetWeaponBullets(propInfo)
            local bNeedBuyWeaponBullet = false
            for _, bulletPropInfo in ipairs(bullets) do
                local quickOperationDataInfo = Module.ArmedForce.Field:GetQuickOperationDataInfo(bulletPropInfo.id)
                local weaponBulletBuyNum = quickOperationDataInfo:GetWeaponBulletBuyNum()
                if weaponBulletBuyNum > 0 then
                    bNeedBuyWeaponBullet = true
                    break
                end
            end
            item.bNeedBuy = not table.isempty(presetData.weaponData.goodsList) or bNeedBuyWeaponBullet
            ItemOperaTool.DoEquipPresetItem(item, targetSlot, item.bNeedBuy)
        else -- 装备
            local item
            local invPresetItemData = presetData.invPresetItems[1]
            if invPresetItemData then
                item = Server.InventoryServer:GetItemByGid(invPresetItemData.gid, ESlotGroup.Player)
            end
            if not item then
                item = presetData.goods and presetData.goods:GetItem()
                local itemMainType = ItemHelperTool.GetMainTypeById(item.id)
                local itemSubType = ItemHelperTool.GetSubTypeById(item.id)
                if itemMainType == EItemType.Equipment and (itemSubType == EEquipmentType.Helmet or itemSubType == EEquipmentType.BreastPlate) then
                    local equipPosition = Module.ArmedForce.Field:GetCurOperationOutfitEquipPositionBySlotType(slotType)
                    if equipPosition then
                        local propInfo = equipPosition.load_props[1]
                        if propInfo then
                            item = ItemBase:New(item.id)
                            item:SetRawPropInfo(propInfo)
                        end
                    end
                end
                item.bNeedBuy = true
            end
            ItemOperaTool.DoEquipPresetItem(item, targetSlot, presetData.goods)
        end
    else -- 卸下
        local equipItem = targetSlot:GetEquipItem()
        if equipItem then
            ItemOperaTool.DoRemovePresetItem(equipItem)
        end
    end
    -- Module.ArmedForce.Config.evtPresetFitSampleItem:Invoke(slotType)
end

-- 将QuickOperationData推荐到槽位空间上
OutfitDataLogic._PresetFitQuickOperationData_Container = function ()
    local curOperationOutfit = Module.ArmedForce.Field:GetCurOperationOutfit()
    local items = curOperationOutfit.items
    -- 设置容器空间
    for _, outfitSlotType in ipairs(OutfitDataLogic.ContainerTypeList) do
        local equipPosition = table.find(items, function (v, k)
            return v.position == outfitSlotType
        end)
        if equipPosition then
            for _, propInfo in ipairs(equipPosition.load_props) do
                local operateItem = ItemBase:New(propInfo.id)
                local newPropInfo = {}
                deepcopy(newPropInfo, propInfo)

                local quickOperationDataInfo = Module.ArmedForce.Field:GetQuickOperationDataInfo(propInfo.id)
                local itemMainType = ItemHelperTool.GetMainTypeById(propInfo.id)
                if itemMainType == EItemType.Medicine then
                    if quickOperationDataInfo then
                        local prop = quickOperationDataInfo:GetDepositoryPropInfo()
                        if prop then
                            newPropInfo.originalGid = prop.originalGid
                            newPropInfo.health = prop.health
                            newPropInfo.health_max = prop.health_max
                        end
                    end
                end
                operateItem:SetRawPropInfo(newPropInfo)
                if operateItem then
                    local targetSlot = Server.InventoryServer:GetSlot(equipPosition.position, ESlotGroup.OutFit)
                    operateItem:AddToSlot(targetSlot)
                    Server.InventoryServer:AddItemToList(operateItem, targetSlot)

                    local targetLoc = targetSlot:SetItemPosFromPb(operateItem, newPropInfo.loc)
                    operateItem:FreshRawPropInfoByLoc(targetLoc)
                end
            end
        end
        -- Module.ArmedForce.Config.evtPresetFitSampleItem:Invoke(outfitSlotType)
        -- if outfitSlotType ~= ESlotType.Pocket then
            -- Module.ArmedForce.Config.evtPresetFitSampleItem:Invoke(math.floor(outfitSlotType / 1000))
        -- end
    end
end

--#endregion ==============================================推荐道具到槽位上========================================================

--#region ==============================================系统推荐装备数据生成逻辑========================================================
OutfitDataLogic.TryGetSystemOutfitPropInfo_Equip = function (outfitID, slotType)
    local propInfo = nil
    local assemblyPresetInfo = Module.ArmedForce.Field:GetAssemblyPresetInfoByPlanKey("Equipment", string.format("%s_%s", slotType, outfitID))
    if assemblyPresetInfo then
        if ItemHelperTool.IsSOLWeaponSlotType(slotType) then
            local itemId = assemblyPresetInfo.ItemId
            propInfo = WeaponAssemblyTool.PresetRow_to_PropInfo(itemId)
            loginfo(string.format('[OutfitDataLogic] TryGetSystemOutfitPropInfo_Equip 推荐 系统方案：%s , 槽位：%s -------------需要数量:%s', outfitID, slotType, assemblyPresetInfo.ItemNum))
            local name = ItemConfigTool.GetItemName(propInfo.id)
            loginfo(string.format('[OutfitDataLogic] TryGetSystemOutfitPropInfo_Equip 推荐 系统推荐武器 槽位:%s, 道具名:%s, id:%s', slotType, name, propInfo.id))
        else
            local invPresetItemGids, needBuyNum = OutfitDataLogic.TryGetFitPresetItemsFromInventory(assemblyPresetInfo)
            loginfo(string.format('[OutfitDataLogic] TryGetSystemOutfitPropInfo_Equip 推荐 系统方案：%s , 槽位：%s -------------还需要购买数量:%s', outfitID, slotType, needBuyNum))
            if not table.isempty(invPresetItemGids) then
                local invPresetItemData = invPresetItemGids[1]
                if invPresetItemData then
                    local inventoryItem = Server.InventoryServer:GetItemByGid(invPresetItemData.gid, ESlotGroup.Player)
                    if inventoryItem then
                        propInfo = {}
                        deepcopy(propInfo, inventoryItem:GetRawPropInfo())
                        loginfo(string.format('[OutfitDataLogic] TryGetSystemOutfitPropInfo_Equip 推荐 系统仓库道具 槽位:%s, 道具名:%s, id:%s, gid:%s', slotType, inventoryItem.name, propInfo.id, propInfo.gid))
                    end
                end
            else
                loginfo('[OutfitDataLogic] TryGetSystemOutfitPropInfo_Equip 推荐-------------没找到仓库精准符合条件的道具！！！')
            end
            if needBuyNum > 0 then
                local goods, canBuyNum = OutfitDataLogic.TryGetFitPresetItemsFromGoods(assemblyPresetInfo, needBuyNum)
                if goods then
                    local item = goods:GetItem()
                    -- 无gid需要购买
                    propInfo = {
                        id = item.id,
                        num = canBuyNum,
                    }
                    loginfo('[OutfitDataLogic] TryGetSystemOutfitPropInfo_Equip 推荐----￥￥￥------商品精准符合条件:', ' |道具id:', item.id, ' |道具名字:', item.name, ' |道具数量:', needBuyNum, ' |道具MinPrice:', goods:GetMinPrice())
                else
                    loginfo('[OutfitDataLogic] TryGetSystemOutfitPropInfo_Equip 推荐----￥￥￥------没找到商品精准符合条件的道具！！！')
                end
            end
        end
    end
    return propInfo
end

--- 根据方案, 从仓库里找
---@param assemblyPresetInfo table 方案
---@return table invPresetItemGids 推荐道具{gid,num}
---@return number needBuyNum 还需购买数量
OutfitDataLogic.TryGetFitPresetItemsFromInventory = function(assemblyPresetInfo)
    local invPresetItemGids = {}

    local pos = assemblyPresetInfo.Position
    local itemId = assemblyPresetInfo.ItemId
    local itemQuality = assemblyPresetInfo.ItemQuality
    local itemLevel = assemblyPresetInfo.Level
    local needItemNum = assemblyPresetInfo.ItemNum or 1
    local subType = assemblyPresetInfo.subType -- 武器子类型（用于定位用什么子弹）
    local needBuyNum = needItemNum

    if itemQuality and itemQuality ~= 0 then -- 现在品阶推荐只需要一个装备，所以先不考虑多个仓库品阶相同的装备
        local invItems = Module.ArmedForce.Field:GetMapPos2InvPresetItemDataByPosAndType(pos, "Quality")[itemQuality]
        if invItems then
            if itemId and itemId ~= 0 then
                table.sort(invItems, function (itemA, itemB)
                    if itemA.id == itemId and itemA.id == itemB.id then
                        local singleDynamicGuidePriceA = Server.ShopServer:GetShopSingleDynamicGuidePriceByItem(itemA)
                        local singleDynamicGuidePriceB = Server.ShopServer:GetShopSingleDynamicGuidePriceByItem(itemB)
                        return singleDynamicGuidePriceA > singleDynamicGuidePriceB
                    else
                        return itemA.id < itemB.id
                    end
                end)
            end
            for _, item in ipairs(invItems) do
                if OutfitDataLogic.CheckItemConditions(item, pos) then
                    if needBuyNum > 0 then
                        local usedNum = Module.ArmedForce.Field:GetMapGid2UsedNum(item.gid)
                        local remainNum = item.num - usedNum
                        if remainNum > 0 then
                            local optNum = math.min(needBuyNum, remainNum)
                            table.insert(invPresetItemGids, {gid = item.gid, num = optNum})
                            needBuyNum = needBuyNum - optNum
                            Module.ArmedForce.Field:SetMapGid2UsedNum(item.gid, optNum)
                        end
                    end
                end
            end
        end
    -- elseif itemLevel and itemLevel ~= 0 and subType then
    --     local invTypeItems = Module.ArmedForce.Field:GetMapPos2InvPresetItemDataByPosAndType(pos, "Level")[subType]
    --     if invTypeItems then
    --         local invItems = invTypeItems[itemLevel]
    --         if invItems then
    --             if itemId and itemId ~= 0 then
    --                 table.sort(invItems, function (itemA, itemB)
    --                     if itemA.id == itemId and itemA.id == itemB.id then
    --                         local singleDynamicGuidePriceA = Server.ShopServer:GetShopSingleDynamicGuidePriceByItem(itemA)
    --                         local singleDynamicGuidePriceB = Server.ShopServer:GetShopSingleDynamicGuidePriceByItem(itemB)
    --                         return singleDynamicGuidePriceA > singleDynamicGuidePriceB
    --                     else
    --                         return itemA.id < itemB.id
    --                     end
    --                 end)
    --             end
    --             if pos  == ESlotType.BulletLeft or pos == ESlotType.BulletRight then
    --                 local mapId2Num = {}
    --                 local maxNumId
    --                 for _, item in ipairs(invItems) do
    --                     declare_if_nil(mapId2Num, item.id, 0)
    --                     mapId2Num[item.id] = mapId2Num[item.id] + item.num
    --                     if not maxNumId then
    --                         maxNumId = item.id
    --                     elseif maxNumId ~= item.id then
    --                         if mapId2Num[maxNumId] < mapId2Num[item.id]  then
    --                             maxNumId = item.id
    --                         end
    --                     end
    --                 end
    --                 for _, item in ipairs(invItems) do
    --                     if ArmedForceRecommendationsLogic.CheckItemConditions(item, pos) then
    --                         if item.id == maxNumId then
    --                             if needBuyNum > 0 then
    --                                 local usedNum = Module.ArmedForce.Field:GetMapGid2UsedNum(item.gid)
    --                                 local remainNum = item.num - usedNum
    --                                 if remainNum > 0 then
    --                                     local optNum = math.min(needBuyNum, remainNum)
    --                                     table.insert(invPresetItemGids, {gid = item.gid, num = optNum})
    --                                     needBuyNum = needBuyNum - optNum
    --                                     Module.ArmedForce.Field:SetMapGid2UsedNum(item.gid, optNum)
    --                                 end
    --                             end
    --                         end
    --                     end
    --                 end
    --             else
    --                 for _, item in ipairs(invItems) do
    --                     if ArmedForceRecommendationsLogic.CheckItemConditions(item, pos) then
    --                         if needBuyNum > 0 then
    --                             local usedNum = Module.ArmedForce.Field:GetMapGid2UsedNum(item.gid)
    --                             local remainNum = item.num - usedNum
    --                             if remainNum > 0 then
    --                                 local optNum = math.min(needBuyNum, remainNum)
    --                                 table.insert(invPresetItemGids, {gid = item.gid, num = optNum})
    --                                 needBuyNum = needBuyNum - optNum
    --                                 Module.ArmedForce.Field:SetMapGid2UsedNum(item.gid, optNum)
    --                             end
    --                         end
    --                     end
    --                 end
    --             end
    --         end
    --     end
    elseif itemId and itemId ~= 0 then
        local invItems = Module.ArmedForce.Field:GetMapPos2InvPresetItemDataByPosAndType(pos, "Id")[itemId]
        if invItems then
            for _, item in ipairs(invItems) do
                if OutfitDataLogic.CheckItemConditions(item, pos) then
                    if needBuyNum > 0 then
                        local usedNum = Module.ArmedForce.Field:GetMapGid2UsedNum(item.gid)
                        local remainNum = item.num - usedNum
                        if remainNum > 0 then
                            local optNum = math.min(needBuyNum, remainNum)
                            table.insert(invPresetItemGids, {gid = item.gid, num = optNum})
                            needBuyNum = needBuyNum - optNum
                            Module.ArmedForce.Field:SetMapGid2UsedNum(item.gid, optNum)
                        end
                    end
                end
            end
        end
    end
    return invPresetItemGids, needBuyNum
end

--- 根据方案, 从商品里找
---@param assemblyPresetInfo table 方案
---@return GoodsItemStruct goods 推荐的goods
---@return number resultNeedBuyNum 可购买数量
OutfitDataLogic.TryGetFitPresetItemsFromGoods = function(assemblyPresetInfo, needBuyNum)
    local goods
    local resultNeedBuyNum = 0
    local pos = assemblyPresetInfo.Position
    local itemId = assemblyPresetInfo.ItemId
    local itemQuality = assemblyPresetInfo.ItemQuality
    local itemLevel = assemblyPresetInfo.Level
    local subType = assemblyPresetInfo.subType -- 武器子类型（用于定位用什么子弹）

    local function fCheckAndGetMatchQuality(id)
        local matchGoods
        if itemQuality and itemQuality ~= 0 then
            local quality = ItemConfigTool.GetItemQuality(id)
            if quality == itemQuality then
                local newGoods = Module.ArmedForce.Field:GetMapArmedForceGoods(id)
                matchGoods = newGoods
            end
        end
        return matchGoods
    end

    local function fCheckAndGetMatchLevel(id)
        local matchGoods
        if itemLevel and itemLevel ~= 0 and subType then
            local bulletCfg = WeaponHelperTool.GetAmmoConfig(id)
            if bulletCfg then
                local level = bulletCfg.PenetrateLevel
                local bulletType = bulletCfg.Type
                if level == itemLevel and bulletType == subType then
                    local newGoods = Module.ArmedForce.Field:GetMapArmedForceGoods(id)
                    matchGoods = newGoods
                end
            end
        end
        return matchGoods
    end

    if (itemQuality and itemQuality ~= 0) or (itemLevel and itemLevel ~= 0 and subType) then
        -- 优先匹配具体推荐的套装id
        if itemId and itemId ~= 0 then
            if itemQuality and itemQuality ~= 0 then
                local matchGoods = fCheckAndGetMatchQuality(itemId)
                if matchGoods and matchGoods:GetIsUnlock() then -- 找到id就推荐，数量和未解锁等比价判断
                    goods = matchGoods
                    resultNeedBuyNum = needBuyNum
                end
            elseif itemLevel and itemLevel ~= 0 and subType then
                local matchGoods = fCheckAndGetMatchLevel(itemId)
                if matchGoods and matchGoods:GetIsUnlock() then -- 找到id就推荐，数量和未解锁等比价判断
                    goods = matchGoods
                    resultNeedBuyNum = needBuyNum
                end
            end
        end

        -- 如没填具体推荐的id 则按照品阶或者等级推荐
        if not goods then
            local matchIds = {} -- 拍卖行全图鉴
            local typeInfo = Module.ArmedForce.Config.SlotType2Type[pos]
            if typeInfo then
                matchIds = Server.AuctionServer:GetSaleIdListFromType(typeInfo.mainType, typeInfo.subType)
            end
            table.sort(matchIds, function (a, b)
                local aDynamicGuidePrice =  Server.ShopServer:GetShopDynamicGuidePrice(a)
                local bDynamicGuidePrice = Server.ShopServer:GetShopDynamicGuidePrice(b)
                -- local aItemData = ItemConfigTool.GetItemConfigById(a)
                -- local bItemData = ItemConfigTool.GetItemConfigById(b)
                if not aDynamicGuidePrice or not bDynamicGuidePrice then
                    return false
                else
                    return aDynamicGuidePrice < bDynamicGuidePrice
                end
            end)
            for index, id in ipairs(matchIds) do
                local matchGoods
                if itemQuality and itemQuality ~= 0 then
                    matchGoods = fCheckAndGetMatchQuality(id)
                elseif itemLevel and itemLevel ~= 0 and subType then
                    matchGoods = fCheckAndGetMatchLevel(id)
                end
                if matchGoods then
                    goods = matchGoods
                    resultNeedBuyNum = needBuyNum
                    if matchGoods and matchGoods:GetIsUnlock() then -- 找到id就推荐，数量和未解锁等比价判断
                        break
                    end
                end
            end
        end
    elseif itemId and itemId ~= 0 then
        local newGoods = Module.ArmedForce.Field:GetMapArmedForceGoods(itemId)
        if newGoods then -- 找到id就推荐，数量和未解锁等比价判断
            goods = newGoods
            resultNeedBuyNum = needBuyNum
        end
    end
    return goods, resultNeedBuyNum
end

---------------------------------------------------------------------------------
--- 检查道具匹配
---------------------------------------------------------------------------------
OutfitDataLogic.CheckItemConditions = function (item, slotType)
    local bFit = true
    local equipmentFeature = item:GetFeature(EFeatureType.Equipment)
    local healthFeature = item:GetFeature(EFeatureType.Health)
    --    推荐仓库装备时，只推荐耐久度高于触发配装提醒阈值的装备

    --    推荐仓库药品时，只推荐耐久度或使用次数大于50%的药品
    if equipmentFeature and (equipmentFeature:IsHelmet() or equipmentFeature:IsBreastPlate()) then --护具
        local checkValue = Module.ArmedForce.Field:GetEquipmentCheckValue(Module.ArmedForce.Config.EAbnormalType.InsufficientDurability, slotType)
        if checkValue and MathUtil.GetTheSecondDecimal(equipmentFeature:GetDurabilityPercent()) <= MathUtil.GetTheSecondDecimal(checkValue) then -- 当前装备耐久度低于满值50%
            bFit = false
        end
    elseif healthFeature then
        if healthFeature:GetDurabilityPercent() <= 0.5 then
            bFit = false
        end
    -- elseif item:IsBullet() then
    --     local weaponSlotType = slotType == ESlotType.BulletLeft and ESlotType.MainWeaponLeft or ESlotType.MainWeaponRight
    --     local presetData = Module.ArmedForce.Field:GetMapPos2FitPresetData(weaponSlotType)
    --     if presetData and presetData.weaponData and presetData.weaponData.propInfo then
    --         if not ammoMgr:IsMatchWeapon(presetData.weaponData.propInfo.id, item.id) then
    --             bFit = false
    --         end
    --     else
    --         bFit = false
    --     end
    end
    return bFit
end
--#endregion ==============================================系统推荐装备数据生成逻辑========================================================

--#region ==============================================系统推荐容器数据生成逻辑========================================================
-- 系统方案推荐容器下的道具
OutfitDataLogic.TryGetSystemOutfitPropInfo_Container = function (outfitID, equipPositions)
    OutfitDataLogic.RecommendPresetContainerEquips2Slots(equipPositions)
    if outfitID then
        -- 处理药品
        OutfitDataLogic.TryGetSystemOutfitPropInfo_Container_Medicine(outfitID, equipPositions)
        -- 处理子弹
        OutfitDataLogic.TryGetSystemOutfitPropInfo_Container_Bullet(outfitID, equipPositions)
    end
end

-- 重置preset里的容器槽位（为什么是preset的槽位，因为这只是临时的，只是为了在一个空容器下获得一批道具的具体位置，最终是会放入outfit）
OutfitDataLogic.RecommendPresetContainerEquips2Slots = function (equipPositions)
    local function fSetSlotView(slotType)
        local presetSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.Preset)
        presetSlot:ResetSlot()
        local containerType = presetSlot:GetContainerType()
        local equipPosition = table.find(equipPositions, function (v, k)
            return v.position == containerType
        end)
        if slotType == ESlotType.Pocket then
            presetSlot:InitPocketContainer()
        elseif slotType == ESlotType.SafeBoxContainer then
            local playerEquipContainerSlot = Server.InventoryServer:GetSlot(containerType)
            local equipment = playerEquipContainerSlot:GetEquipItem()
            if equipment then
                presetSlot:InitContainerByEquipmentId(equipment.id)
            end

        else
            if equipPosition then
                local propinfo = equipPosition.load_props[1]
                if propinfo then
                    local equipId = propinfo.id
                    presetSlot:InitContainerByEquipmentId(equipId)
                else
                    logerror("OutfitDataLogic.RecommendPresetContainerEquips2Slots equipPosition ==》 propinfo is nil!!!")
                end
            end
        end
    end
    for _, slotType in ipairs(OutfitDataLogic.ContainerTypeList) do
        fSetSlotView(slotType)
    end
end

-- 获得系统推荐药品数据
OutfitDataLogic.TryGetSystemOutfitPropInfo_Container_Medicine = function (outfitID, equipPositions)
    local medicineDatas = OutfitDataLogic._GenMedicineQuickOperationDatas(outfitID)
    for id, medicineData in pairs(medicineDatas) do
        local needNum = medicineData.num
        local slotIncreaseDataMap = {}
        -- 当前指定胸挂>口袋>背包>保险箱
        needNum = OutfitDataLogic.TryIncreaseToSlot(id, needNum, slotIncreaseDataMap)
        OutfitDataLogic._SlotIncreaseDataMap2EquipPosition(slotIncreaseDataMap, equipPositions)
    end
end

-- 获得系统推荐子弹数据
OutfitDataLogic.TryGetSystemOutfitPropInfo_Container_Bullet = function (outfitID, equipPositions)
    local bulletDatas = OutfitDataLogic._GenBulletQuickOperationDatas(outfitID, equipPositions)
    for weaponSlotType, bulletData in pairs(bulletDatas) do
        local id = bulletData.id
        local needNum = bulletData.num
        local slotIncreaseDataMap = {}
        -- 当前指定枪内>胸挂>口袋>背包>保险箱
        -- 先处理填充武器
        local weaponEquipPosition = table.find(equipPositions, function(v, k) 
            return v.position == weaponSlotType
        end)

        if weaponEquipPosition and weaponEquipPosition.load_props[1] then
            local weaponPropInfo = weaponEquipPosition.load_props[1]

            local capacity = WeaponAssemblyTool.GetWeaponCapacity(weaponPropInfo)


            local bulletPropInfo = pb.PropInfo:New()
            bulletPropInfo.id = id
            bulletPropInfo.gid = GetGid()
            bulletPropInfo.num = capacity
            table.insert(weaponPropInfo.weapon.load_bullets, bulletPropInfo)
        end

        needNum = OutfitDataLogic.TryIncreaseToSlot(id, needNum, slotIncreaseDataMap)
        OutfitDataLogic._SlotIncreaseDataMap2EquipPosition(slotIncreaseDataMap, equipPositions)
    end
end

-- 将获得的临时位置数据转化为EquipPosition可用的数据
OutfitDataLogic._SlotIncreaseDataMap2EquipPosition = function (slotIncreaseDataMap, equipPositions)
    for slotType, slotIncreaseData in pairs(slotIncreaseDataMap) do
        local equipPosition = table.find(equipPositions, function(v, k) 
            return v.position == slotType
        end)
        if not equipPosition then
            equipPosition = {
                position = slotType,
                load_props = {},
            }
            table.insert(equipPositions, equipPosition)
        end
        for index, itemViewData in ipairs(slotIncreaseData) do
            local item = itemViewData.item
            local operateNum = itemViewData.operateNum
            if item then
                local propInfo = item:GetRawPropInfo()
                table.insert(equipPosition.load_props, propInfo)
            end
        end
    end
end

-- 生成药品推荐数据
OutfitDataLogic._GenMedicineQuickOperationDatas = function (outfitID)
    local needRecommendQuickOperationDatas = {}
    local drugAssemblyPresetInfo = Module.ArmedForce.Field:GetAssemblyPresetInfoByPlanKey("Drug", tostring(outfitID))
    if drugAssemblyPresetInfo then
        for _, presetInfo in ipairs(drugAssemblyPresetInfo) do
            local id = presetInfo.ItemId
            local num = presetInfo.ItemNum
            local bTransaction = Server.AuctionServer:CheckIsInSaleList(id)
            if bTransaction then
                local data = {
                    id = id,
                    num = num,
                }
                needRecommendQuickOperationDatas[id] = data
            end
        end
    end
    return needRecommendQuickOperationDatas
end

-- 生成子弹推荐数据
OutfitDataLogic._GenBulletQuickOperationDatas = function (outfitID, equipPositions)
    local needRecommendQuickOperationDatas = {}
    local function fCheckMatchLevel(itemLevel, id)
        if itemLevel and itemLevel ~= 0 then
            local bulletCfg = WeaponHelperTool.GetAmmoConfig(id)
            if bulletCfg then
                local level = bulletCfg.PenetrateLevel
                if level == itemLevel then
                    return true
                end
            end
        end
        return false
    end

    for _, slotType in ipairs(OutfitDataLogic.WeaponTypeList) do
        if equipPositions then
            local equipPosition = table.find(equipPositions, function (v, k)
                return v.position == slotType
            end)
            -- 找到武器
            if equipPosition and equipPosition.load_props[1] then
                local bulletAssemblyPresetInfo = Module.ArmedForce.Field:GetAssemblyPresetInfoByPlanKey("Bullet", string.format("%s_%s", slotType, outfitID))
                if bulletAssemblyPresetInfo then
                    local itemLevel = bulletAssemblyPresetInfo.Level
                    local num = bulletAssemblyPresetInfo.ItemNum
                    local levelMatchId = nil
                    if itemLevel and itemLevel ~= 0 then
                        local typeInfo = Module.ArmedForce.Config.SlotType2Type[ESlotType.BulletLeft] -- 用哪个子弹类型都会获得相同的拍卖行数据
                        if typeInfo then
                            local matchIds = Server.AuctionServer:GetSaleIdListFromType(typeInfo.mainType, typeInfo.subType)
                            for _, id in ipairs(matchIds) do
                                local bTransaction = Server.AuctionServer:CheckIsInSaleList(id)
                                if bTransaction and ammoMgr:IsMatchWeapon(equipPosition.load_props[1].id, id) then
                                    if fCheckMatchLevel(itemLevel, id) then
                                        levelMatchId = id
                                        break
                                    end
                                end
                            end
                        end
                    end
                    if levelMatchId then
                        local data = {
                            id = levelMatchId,
                            num = num,
                        }
                        needRecommendQuickOperationDatas[slotType] = data
                    end
                end
            end
        end
    end
    return needRecommendQuickOperationDatas
end

-- 在preset容器中放临时道具
OutfitDataLogic.TryIncreaseToSlot = function (itemId, needNum, slotIncreaseDataMap)
    -- 尝试位置
    local function fTryIncreaseToSlot(slotType)
        if needNum > 0 then
            needNum = OutfitDataLogic._TryIncreaseToSlotProcess(itemId, needNum, slotType, slotIncreaseDataMap)
        end
    end
    local containersInOrder = OutfitDataLogic.GetContainerOrderByItem(itemId)
    for _, slotType in ipairs(containersInOrder) do
        fTryIncreaseToSlot(slotType)
    end

    return needNum
end

OutfitDataLogic.GetContainerOrderByItem = function (itemId)
    local ret = {}
    local ContainerTypeList2 = {
        ESlotType.BagContainer,
        ESlotType.SafeBoxContainer
    }
    local itemMainType = ItemHelperTool.GetMainTypeById(itemId)
    if itemMainType == EItemType.Bullet then
        ret = {ESlotType.Pocket, ESlotType.ChestHangingContainer, table.unpack(ContainerTypeList2)}
    else
        ret = {ESlotType.ChestHangingContainer, ESlotType.Pocket, table.unpack(ContainerTypeList2)}
    end
    return ret
end

-- 从临时的preset容器槽中找位置
OutfitDataLogic._TryIncreaseToSlotProcess = function (itemId, needNum, slotType, slotIncreaseDataMap)
    local maxStackCount = ItemConfigTool.GetMaxStacksNumById(itemId)
    local presetSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.Preset)
    while needNum > 0 do
        local operateNum = math.min(needNum, maxStackCount)
        if operateNum == 0 then
            break
        end
        local operateItem = ItemBase:NewIns(itemId)
        local targetLoc = presetSlot:TryFindLocationForItem(operateItem)
        if targetLoc then --如果能添加的话
            local prop = pb.PropInfo:New()
            prop.id = itemId
            prop.gid = GetGid()
            prop.num = operateNum
            operateItem:SetRawPropInfo(prop)
            operateItem:FreshRawPropInfoByLoc(targetLoc)
            operateItem:AddToSlot(presetSlot)
            Server.InventoryServer:AddItemToList(operateItem, presetSlot)
            presetSlot:SetItemPosFromLoc(operateItem, targetLoc)
            declare_if_nil(slotIncreaseDataMap, slotType, {})
            table.insert(slotIncreaseDataMap[slotType], {
                item = operateItem,
                operateNum = operateNum
            })
            needNum = needNum - operateNum
        else
            logerror("找不到位置", itemId, operateNum, slotType)
            break
        end
    end
    return needNum
end
--#endregion ==============================================系统推荐容器数据生成逻辑========================================================

--#region ==============================================更新OutfitEquipPosition========================================================
-- 根据传入的inEquipPosition更新方案
OutfitDataLogic.UpdateOutfitEquipPosition = function (inEquipPosition, bRefreshPresetData)
    -- 是否刷PresetData
    bRefreshPresetData = setdefault(bRefreshPresetData, true)
    -- 只更新curOperationOutfit，然后再重新推荐，避免一些quickOperationData有问题
    local slotType = inEquipPosition.position
    local outFitSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.OutFit)
    local curOperationOutfit = Module.ArmedForce.Field:GetCurOperationOutfit()
    if curOperationOutfit and curOperationOutfit.items then
        for index, equipPosition in ipairs(curOperationOutfit.items) do
            if slotType == equipPosition.position then
                curOperationOutfit.items[index] = inEquipPosition
                break
            end
        end
    end

    if outFitSlot:IsEquipChestAndBagSlot() then
        local containerSlotType = outFitSlot:GetContainerSlotType()
        -- 找容器空间下的equipPosition
        local equipPosition = table.find(curOperationOutfit.items, function (v, k)
            return v.position == containerSlotType
        end)
        if equipPosition then
            -- 有可能会改，会将原有的道具依次尝试放入当前的容器空间
            -- 找到后将其空间下的置空
            local outFitContainerSlot = Server.InventoryServer:GetSlot(containerSlotType, ESlotGroup.OutFit)
            -- 先重置槽位
            outFitContainerSlot:ResetSlot()

            if containerSlotType == ESlotType.Pocket then
                outFitContainerSlot:InitPocketContainer()
            elseif containerSlotType == ESlotType.SafeBoxContainer then
                local playerEquipContainerSlot = Server.InventoryServer:GetSlot(slotType)
                local equipment = playerEquipContainerSlot:GetEquipItem()
                if equipment then
                    outFitContainerSlot:InitContainerByEquipmentId(equipment.id)
                end
            else
                if inEquipPosition then
                    local propinfo = inEquipPosition.load_props[1]
                    if propinfo then 
                        local equipId = propinfo.id
                        outFitContainerSlot:InitContainerByEquipmentId(equipId)
                    end
                end
            end
            equipPosition.load_props = {}
        end
    end
    OutfitDataLogic.UsedCurOperationOutfit(curOperationOutfit, bRefreshPresetData)
    Module.ArmedForce.Config.evtUpdateOutfitEquipPosition:Invoke()
end


OutfitDataLogic.OnLocalMoveResult = function (moveCmd)
    local curOperationOutfit = Module.ArmedForce.Field:GetCurOperationOutfit()
    if curOperationOutfit and curOperationOutfit.items then
        -- 处理操作的道具
        local originPos = moveCmd.originLoc.ItemSlot.SlotType
        local targetPos = moveCmd.targetLoc.ItemSlot.SlotType
        if #moveCmd.targetItems > 0 then
            -- 如果是以一换一的话，很有可能是发生堆叠了，需要处理一下QuickOperationData值
            if #moveCmd.targetItems == 1 then
                local targetItem = moveCmd.targetItems[1]
                if moveCmd.item.bindType == targetItem.bindType and moveCmd.item.id == targetItem.id then
                    local moveItemGid = moveCmd.item.gid
                    local targetItemGid = targetItem.gid
                    local quickOperationDataInfo = Module.ArmedForce.Field:GetQuickOperationDataInfo(moveCmd.item.id)
                    if quickOperationDataInfo then
                        local quickOperationItemData_move = quickOperationDataInfo:GetItemDataByGid(moveItemGid)
                        local quickOperationItemData_target = quickOperationDataInfo:GetItemDataByGid(targetItemGid)
                        local tatalBuyNum = quickOperationItemData_move.buyNum + quickOperationItemData_target.buyNum
                        local tatalMoveNum = quickOperationItemData_move.moveNum + quickOperationItemData_target.moveNum
                        assert(tatalBuyNum + tatalMoveNum == moveCmd.item.num + targetItem.num, string.format("本地挪动移动、购买总数量不一致，==> [quickOperationItemData_move buyNum = %s, moveNum = %s], [quickOperationItemData_target buyNum = %s, moveNum = %s], [moveCmd.item.num = %s, targetItem.num = %s]", 
                            quickOperationItemData_move.buyNum, quickOperationItemData_move.moveNum,
                            quickOperationItemData_target.buyNum, quickOperationItemData_target.moveNum,
                            moveCmd.item.num, targetItem.num))
        
                        -- 疑惑，需要将quickOperationDataInfo的数据都先取下来吗？
                        -- 先不取下，默认本地拖拽移动不会改变总数量
        
                        -- move剩下的数量与购买总数比较
                        if moveCmd.item.num > tatalBuyNum then
                            local moveNum = moveCmd.item.num - tatalBuyNum
                            quickOperationDataInfo:SetItemData(moveCmd.item, moveNum, tatalBuyNum)
                            quickOperationDataInfo:SetItemData(targetItem, targetItem.num, 0)
                        else
                            quickOperationDataInfo:SetItemData(moveCmd.item, 0, moveCmd.item.num)
                            local buyNum = tatalBuyNum - moveCmd.item.num
                            local moveNum = targetItem.num - buyNum
                            quickOperationDataInfo:SetItemData(targetItem, moveNum, buyNum)
                        end
                        -- self:_RefreshNeedBuyItemView(itemId)
                    end
                end
                OutfitDataLogic.LocalMoveProcess(curOperationOutfit, targetPos, originPos, targetItem)
            else
                for i, targetItem in ipairs(moveCmd.targetItems) do
                    -- 原有的pos和目标pos与操作道具相反
                    OutfitDataLogic.LocalMoveProcess(curOperationOutfit, targetPos, originPos, targetItem)
                end
            end
        end
        OutfitDataLogic.LocalMoveProcess(curOperationOutfit, originPos, targetPos, moveCmd.item)
    end
end

OutfitDataLogic.LocalMoveProcess = function (curOperationOutfit, originPos, targetPos, item)
    -- 原始EquipPosition
    local originEquipPosition = table.find(curOperationOutfit.items, function (v, k)
        return v.position == originPos
    end)

    local targetEquipPosition = nil
    -- 如果移动的是同一个槽
    if originPos == targetPos then
        targetEquipPosition = originEquipPosition
    else
        -- 目标EquipPosition
        targetEquipPosition = table.find(curOperationOutfit.items, function (v, k)
            return v.position == targetPos
        end)
        if not targetEquipPosition then
            local targetEquipPosition = {
                position = targetPos,
                load_props = {},
            }
            table.insert(curOperationOutfit.items, targetEquipPosition)
        end
    end

    if originEquipPosition and targetEquipPosition then
        local findPropInfo = nil
        if originEquipPosition.load_props ~= nil and next(originEquipPosition.load_props) ~= nil then
            for i = #originEquipPosition.load_props, 1, -1 do
                local propInfo = originEquipPosition.load_props[i]
                if propInfo.gid == item.gid then
                    -- 如果移动的是同一个槽，则不需要从原来的槽位移除
                    if  originPos == targetPos then
                        findPropInfo = propInfo
                    else
                        findPropInfo = table.remove(originEquipPosition.load_props, i)
                    end
                    break
                end
            end
        end
        if findPropInfo then
            -- 处理位置
            findPropInfo.loc = item:GetRawPropInfo().loc
            findPropInfo.position = targetPos
            if  originPos ~= targetPos then
                table.insert(targetEquipPosition.load_props, findPropInfo)
            end
            -- 处理数量
            if findPropInfo.num ~= item.num then
                -- 重新处理数据
                findPropInfo.num = item.num
                if findPropInfo.num == 0 then
                    table.removeByFunc(targetEquipPosition.load_props, function (v, k)
                        return v.gid == item.gid
                    end)
                else
                    local quickOperationDataInfo = Module.ArmedForce.Field:GetQuickOperationDataInfo(item.id)
                    local quickOperationItemData = quickOperationDataInfo:GetItemDataByGid(item.gid)
                    if quickOperationItemData then -- 有关联的
                        local buyNum = quickOperationItemData.buyNum
                        local bNeedBuy = buyNum > 0
                        local bChanged = findPropInfo.bNeedBuy ~= bNeedBuy
                        if bChanged then
                            findPropInfo.bNeedBuy = bNeedBuy
                            item.bNeedBuy = bNeedBuy
                            Module.ArmedForce.Config.evtPresetFitSampleItem:Invoke(targetPos)
                        end
                    end
                end
            end

        end
    end
end

OutfitDataLogic.DecreaseItemProcess = function(operateItem)
    local gid = operateItem.gid
    local num = operateItem.num
    if operateItem.InSlot then
        local slotType = operateItem.InSlot.SlotType
        local curOperationOutfit = Module.ArmedForce.Field:GetCurOperationOutfit()
        local equipPosition = table.find(curOperationOutfit.items, function (v, k)
            return v.position == slotType
        end)
        if equipPosition then
            local quickOperationDataInfo = Module.ArmedForce.Field:GetQuickOperationDataInfo(operateItem.id)
            if quickOperationDataInfo then
                table.removeByFunc(equipPosition.load_props, function (v, k)
                    return v.gid == gid
                end)
                QuickOperationLogic.CalculateOperateNum(quickOperationDataInfo, operateItem, -num) -- 计算当前操作数
                return true
            end
        end
    else
        logerror("OutfitDataLogic.DecreaseItemProcess operateItem.InSlot is nil!!!!", operateItem and operateItem.name)
    end
    return false
end

--#endregion ==============================================更新OutfitEquipPosition========================================================

--#region ==============================================outfit方案价格逻辑========================================================
--- 获取配装花费
OutfitDataLogic.GetNeedBuyItemsCost = function()
    local curPriceValue = 0
    local mapPos2FitPresetData = Module.ArmedForce.Field:GetMapPos2FitPresetData()
    local function fCalculateCost(presetData, slotType)
        if presetData.weaponData then -- 武器
            if presetData.weaponData.propInfo and not table.isempty(presetData.weaponData.goodsList) then
                for socketGUID, goods in pairs(presetData.weaponData.goodsList) do
                    if goods:GetIsUnlock() then
                        local cheapBuyChannel, minPrice, currencyClientType = goods:GetMinPrice()
                        if minPrice > 0 then
                            local totalPrice = minPrice * 1
                            curPriceValue = curPriceValue + totalPrice
                        end
                    end
                end
            end
        elseif presetData.goods then
            if presetData.goods:GetIsUnlock() and presetData.needBuyNum > 0 then
                local item = presetData.goods:GetItem()
                local itemMainType = ItemHelperTool.GetMainTypeById(item.id)
                local itemSubType = ItemHelperTool.GetSubTypeById(item.id)
                local cheapBuyChannel
                local minPrice = 0
                local currencyClientType
                if itemMainType == EItemType.Equipment and (itemSubType == EEquipmentType.Helmet or itemSubType == EEquipmentType.BreastPlate) then
                    local equipPosition = Module.ArmedForce.Field:GetCurOperationOutfitEquipPositionBySlotType(slotType)
                    if equipPosition then
                        local propInfo = equipPosition.load_props[1]
                        if propInfo and propInfo.health_max then
                            local durabilitylvl = Server.AuctionServer:GetArmorDurabilitylvlByDurability(item.id, propInfo.health_max)
                            if durabilitylvl > 0 then
                                cheapBuyChannel, minPrice, currencyClientType = presetData.goods:GetMinPrice(durabilitylvl)
                            end
                        end
                    end
                else
                    cheapBuyChannel, minPrice, currencyClientType = presetData.goods:GetMinPrice()
                end
                if minPrice > 0 then
                    local totalPrice = minPrice * presetData.needBuyNum
                    curPriceValue = curPriceValue + totalPrice
                end
            end
        end
    end
    for _, slotType in ipairs(OutfitDataLogic.OutfitTypeList) do
        local presetData = mapPos2FitPresetData[slotType]
        if presetData then
            fCalculateCost(presetData, slotType)
        end
    end

    local operationPriceValues = QuickOperationLogic.GetQuickOperationDataCost()
    if operationPriceValues then
        for currencyClientType, totalPrice in pairs(operationPriceValues) do
            curPriceValue = curPriceValue + totalPrice
        end
    end
    return curPriceValue
end

--#endregion ==============================================outfit方案价格逻辑========================================================

--#region ==============================================自定义方案界面逻辑========================================================
OutfitDataLogic.OpenSaveSchemeWindow = function ()
    Facade.UIManager:AsyncShowUI(UIName2ID.AssemblySaveSchemeWindow, nil, self)
end
--#endregion ==============================================自定义方案界面逻辑========================================================

--#region ==============================================保存方案请求========================================================
OutfitDataLogic.SaveCustomOutfitReq = function (index)
    local curOperationOutfit = Module.ArmedForce.Field:GetCurOperationOutfit()
    if not curOperationOutfit then
        local customOutfit = Server.PresetServer:GetSOLArmedForceOutfit(index)
        logerror("OutfitDataLogic.SaveCustomOutfitReq curOperationOutfit is nil!!!", index, customOutfit)
        return
    end
    ---@class pb_CustomOutfitPositionItems : ProtoBase
    ---@field public pos number
    ---@field public items pb_CustomOutfitItem[]
    local posItems = {}
    local durabilityLvls = {}
    for _, equipPosition in ipairs(curOperationOutfit.items) do
        local pos = equipPosition.position
        local loadProps = equipPosition.load_props
        local customOutfitPositionItems = {
            pos = pos,
            items = {}
        }
        for _, propInfo in ipairs(loadProps) do
            local customOutfitItem = {
                prop_id = propInfo.id,
                num = propInfo.num,
                loc = propInfo.loc,
                
            }
            if pos == ESlotType.Helmet or pos == ESlotType.BreastPlate then
                local durabilitylvl = Server.AuctionServer:GetArmorDurabilitylvlByDurability(propInfo.id, propInfo.health_max or 0)
                if durabilitylvl > 0 then
                    local equipDurabilityLvl = pb.EquipDurabilityLvl:New()
                    equipDurabilityLvl.id = propInfo.id
                    equipDurabilityLvl.level = durabilitylvl
                    table.insert(durabilityLvls, equipDurabilityLvl)
                end
            elseif table.contains(OutfitDataLogic.WeaponTypeList, pos) then
                customOutfitItem.weapon_design_slot_index = 0 -- 填0是上次入局的方案， -1则是标准预设，大于0则是改武器的预设方案
                local recPropInfo = WeaponAssemblyTool.PresetRow_to_PropInfo(WeaponAssemblyTool.GetPreviewGunItemIdFromRecId(propInfo.id))
                -- 先判断是否标准预设
                if WeaponHelperTool.CompareWeaponPropInfo(recPropInfo, propInfo) then
                    customOutfitItem.weapon_design_slot_index = -1
                else
                    -- 判断是否方案预设
                    local solutionData = Server.GunsmithServer:GetDataStore():GetSolutionDataItems(ESlotGroup.Player, propInfo.id)
                    if solutionData then
                        for _, value in pairs(solutionData) do
                            local name, presetPropInfo, data_index = value:GetData()
                            if WeaponHelperTool.CompareWeaponPropInfo(presetPropInfo, propInfo) then
                                customOutfitItem.weapon_design_slot_index = data_index
                                break
                            end
                        end
                    end
                end

                -- 处理保存子弹
                customOutfitItem.load_bullets = {}
                local bullets = WeaponAssemblyTool.GetWeaponBullets(propInfo)
                for _, bullet in ipairs(bullets) do
                    local customLoadBullet = pb.CustomLoadBullet:New()
                    customLoadBullet.prop_id = bullet.id
                    customLoadBullet.num = bullet.num
                    table.insert(customOutfitItem.load_bullets, customLoadBullet)
                end
            end
            table.insert(customOutfitPositionItems.items, customOutfitItem)
            
        end
        table.insert(posItems, customOutfitPositionItems)
    end
    Server.PresetServer:UpdateOutfit(index, posItems, durabilityLvls)
end
--#endregion ==============================================保存方案请求========================================================

--#region ==============================================检查所需道具能否放下容器========================================================
-- 检测 仓库容量不足，无法放下应用配装方案后容器内的多余道具
OutfitDataLogic.CheckCanPlaceItems = function(slotType)
    if ItemHelperTool.IsBagSlotType(slotType) then
        local outfitSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.OutFit)
        local equipedOutfitItem = outfitSlot:GetEquipItem()
        if equipedOutfitItem then
            local slot = Server.InventoryServer:GetSlot(slotType)
            local containerSlotType = slot:GetContainerSlotType()
            local containerSlot = Server.InventoryServer:GetSlot(containerSlotType)
            local allItems = containerSlot:GetItems()
            local fakeSlot = ItemSlot:New(containerSlotType, ESlotGroup.None, equipedOutfitItem.id)
            if allItems and next(allItems) then
                local result, mapItem2ActualPos, unfitItems  = ItemOperaTool.TryPlaceItems(allItems, fakeSlot)
                if not result and unfitItems and next(unfitItems) then
                    local equipedItem = slot:GetEquipItem()
                    if equipedItem then
                        table.insert(unfitItems, equipedItem)
                    end
                    local depositorySlot = Server.InventoryServer:GetSlot(ESlotType.MainContainer)
                    result, mapItem2ActualPos, unfitItems  = ItemOperaTool.TryPlaceItems(unfitItems, depositorySlot)
                    return result
                end
            end
        end
    end
    return true
end

--- 发送经分数据
-- @param analysisType 经分类型
-- @param param 需要发送的数据
-- param参数如下：
-- OutfitID = 0, -- 推荐配装方案 0:初始化 1：轻装上阵 2：战局生存 3：战场主宰
-- IsNotModify = 0, -- 是否完全使用系统推荐套装 0：未完全成功推荐 1：完全成功推荐
-- IsModifiedWeapon = 0, -- 是否修改了武器
-- IsModifiedHelmet = 0, -- 是否修改了头盔
-- IsModifiedBreastPlate = 0, --是否修改了护甲
-- IsModifiedBag = 0, -- 是否修改了背包
-- IsModifiedChestHanging = 0, -- 是否修改了胸挂
-- IsModifiedMedicine = 0, -- 是修改了容器的药物
-- IsModifiedOther = 0, --是否修改了容器中的前提物品
OutfitDataLogic.SendAnalysisData = function(analysisType, param)
	if analysisType then
		if analysisType == Module.ArmedForce.Config.EAnalysisType.View then
            if param then
                LogAnalysisTool.ResetPlayerOutFitLogData()
                LogAnalysisTool.SetPlayerOutFitOutfitID(param.OutfitID)
                LogAnalysisTool.DoSendPlayerOutFitViewFlowLog()
            end
        elseif analysisType == Module.ArmedForce.Config.EAnalysisType.Apply then
            if param then
                LogAnalysisTool.ResetPlayerOutFitLogData()
                LogAnalysisTool.SetPlayerOutFitOutfitID(param.OutfitID)
                LogAnalysisTool.SetPlayerOutFitIsNotModify(param.IsNotModify)
                LogAnalysisTool.DoSendPlayerOutFitApplyFlowLog()
            end
        elseif analysisType == Module.ArmedForce.Config.EAnalysisType.Modify then
            -- 有查看过推荐
            if LogAnalysisTool.GetPlayerOutFitOutfitID() ~= 0 then
                -- 应用了推荐(接下来需要判断是否完全应用该推荐)
                if LogAnalysisTool.GetPlayerOutFitIsNotModify() ~= 0 then
                    local bDiff, mapSlotType2Diff = OutfitDataLogic.GetCurEquipDiffPreset()
                    local bDiffCarry = LogAnalysisTool.GetPlayerOutFitIsModifiedMedicine() ~= 0 or LogAnalysisTool.GetPlayerOutFitIsModifiedOther() ~= 0
                    LogAnalysisTool.SetPlayerOutFitIsNotModify((bDiff or bDiffCarry) and 0 or 1)
                    for slotType, diff in pairs(mapSlotType2Diff) do
                        LogAnalysisTool.SetPlayerOutFitModified(slotType,diff and 1 or 0)
                    end
                end
            end
            LogAnalysisTool.DoSendPlayerOutFitModifyAGameFlowLog()
        end
	else
		logerror("OutfitDataLogic.SendAnalysisData LogAnalysisTool Data is nil!")
	end
end


OutfitDataLogic.EAnalysisSlotType =
{
    ESlotType.MainWeaponLeft,
    ESlotType.MainWeaponRight,
    ESlotType.Helmet,
    ESlotType.BreastPlate,
    ESlotType.Bag,
    ESlotType.ChestHanging
}
---获得角色身上的装备与推荐装备的对比信息
---@return boolean bModify 是否有改变
---@return table<SlotType,bDiff> mapSlotType2Diff 对应槽位是否有改变
OutfitDataLogic.GetCurEquipDiffPreset = function ()
    local armId = Server.ArmedForceServer:GetCurUsedArmedForceId()
    local mapSlotType2Diff = {}
    local bModify = false
    for _, slotType in pairs(OutfitDataLogic.EAnalysisSlotType) do
        if ItemHelperTool.IsEquipmentSlotType(slotType) then
            local bDiff = false
            local equipPosition = Module.ArmedForce.Field:GetCurOperationOutfitEquipPositionBySlotType(slotType)
            local equipSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.Player)
            local equipItem = equipSlot:GetEquipItem()
            local curPropInfo = equipItem and equipItem:GetRawPropInfo() or nil
            local outFitPropInfo = equipPosition and equipPosition.load_props[1] or nil
            if curPropInfo and outFitPropInfo then
                bDiff = curPropInfo.id ~= outFitPropInfo.id
            else
                bDiff = (curPropInfo and not outFitPropInfo) or (not curPropInfo and outFitPropInfo)
            end
            if not bModify and bDiff then
                bModify = true
            end
            mapSlotType2Diff[slotType] = bDiff
        end
    end
    return bModify, mapSlotType2Diff
end

OutfitDataLogic.ResetAnalysisData = function()
    LogAnalysisTool.ResetPlayerOutFitLogData()
end


OutfitDataLogic.DoBatchFetchOutfitData = function(operationOutfit, fCallback)
    if operationOutfit then
        local items = operationOutfit.items
        if items and next(items) then
            local propMap = {}
            local propIds = {}
            local limits = {}
            for _, itemData in ipairs(items) do
                local pos = itemData.position
                local props = itemData.load_props
                if props and not table.isempty(props) then
                    for _, propInfo in ipairs(props) do
                        local id = propInfo.id
                        if ItemHelperTool.GetMainTypeById(id) == EItemType.Receiver then
                            local adapterIds = WeaponAssemblyTool.GetItemIDsByPropInfo(propInfo, true, true, true)
                            if not table.isempty(adapterIds) then
                                for _, id in ipairs(adapterIds) do
                                    local optId = id
                                    if ItemHelperTool.GetMainTypeById(id) == EItemType.Receiver then
                                        local presetId = WeaponAssemblyTool.GetPreviewGunItemIdFromRecId(optId)
                                        if presetId then
                                            optId = presetId
                                        end
                                    end
                                    if not propMap[optId] then
                                        propMap[optId] = true
                                        table.insert(propIds, optId)
                                        table.insert(limits, 1)
                                    end
                                end
                            end
                        elseif ItemHelperTool.GetSubTypeById(id) ~= EEquipmentType.SafeBox then
                            if not propMap[id] then
                                propMap[id] = true
                                table.insert(propIds, id)
                                table.insert(limits, 1)
                            end
                        end
                    end
                end
            end
            if not table.isempty(propIds) then
                Server.AuctionServer:DoBatchFetchSaleList(propIds, fCallback, limits, 50)
            end
        else
            logerror("AssemblyHDPresetMainPanel:_CheckAndFetchOutfitData items is nil or isempty !!!")
        end
    else
        logerror("AssemblyHDPresetMainPanel:_CheckAndFetchOutfitData operationOutfit is nil !!!")
    end
end


OutfitDataLogic.CheckPresetDataSoldOutAbnormal = function()
    local bSoldOutAbnormal = false
    local mapPos2FitPresetData = Module.ArmedForce.Field:GetMapPos2FitPresetData()
    local function fcheck(presetData, slotType)
        if presetData.weaponData then -- 武器
            if presetData.weaponData.propInfo and not table.isempty(presetData.weaponData.goodsList) then
                for socketGUID, goods in pairs(presetData.weaponData.goodsList) do
                    if goods:GetIsUnlock() then
                        if not goods:GetIsSufficient() then
                            bSoldOutAbnormal = true
                        end
                    else
                        bSoldOutAbnormal = true
                    end
                end
            end
        elseif presetData.goods then
            if presetData.goods:GetIsUnlock() and presetData.needBuyNum > 0 then
                if not presetData.goods:GetIsSufficient() then
                    bSoldOutAbnormal = true
                end
            else
                bSoldOutAbnormal = true
            end
        end
    end
    for _, slotType in ipairs(OutfitDataLogic.OutfitTypeList) do
        local presetData = mapPos2FitPresetData[slotType]
        if presetData then
            fcheck(presetData, slotType)
            if bSoldOutAbnormal then
                return bSoldOutAbnormal
            end
        end
    end

    local bAllQuickOperationDataInfoSoldOutAbnormal = QuickOperationLogic.CheckAllQuickOperationDataInfoSoldOutAbnormal()
    return bAllQuickOperationDataInfoSoldOutAbnormal
end

OutfitDataLogic.GeneratesOutfitPlanItemDataList = function()
    local itemMap = {}
    local itemDataList = {}
    local allQuickOperationDataInfo = Module.ArmedForce.Field:GetAllQuickOperationDataInfo()
    local mapPos2FitPresetData = Module.ArmedForce.Field:GetMapPos2FitPresetData()
    local function fInsertData(id, num, durabilitylvl, bNeedBuy)
        table.insert(itemDataList, {id = id, num = num, bNeedBuy = bNeedBuy, durabilitylvl = durabilitylvl})
    end
    local function fProcessPresetData(presetData, slotType)
        local invNum = presetData.invNum
        local needBuyNum = presetData.needBuyNum
        local id = presetData.id
        if presetData.weaponData then -- 武器
            local weaponPropInfo = presetData.weaponData.propInfo
            local desc = WeaponAssemblyTool.PropInfo_To_Desc(weaponPropInfo)
            local allparts = desc:GetAllParts()
            if allparts then
                for _, value in ipairs(allparts) do
                    local partItemID = value.Itemid
                    local socketGUID = value.SocketGUID
                    local itemName = ItemConfigTool.GetItemName(partItemID)
                    local bIsAssemblyPart = WeaponAssemblyTool.IsAssemblyPart(partItemID)
                    local mainType = ItemHelperTool.GetMainTypeById(partItemID)
                    local bNeedBuy = presetData.weaponData.goodsList[socketGUID]
                    logerror(string.format("[OutfitDataLogic.GeneratesOutfitPlanItemDataList] 确认方案【武器】 itemID = %d. itemName = %s, socketGUID = %d ,bIsAssemblyPart = %s, bNeedBuy = %s", partItemID, itemName, socketGUID, bIsAssemblyPart, bNeedBuy))
                    if bIsAssemblyPart or mainType == EItemType.Receiver then
                        declare_if_nil(itemMap, partItemID, {
                            [0] = 
                            {
                                buyNum = 0,
                                invNum = 0
                            }
                        })
                        if bNeedBuy then
                            itemMap[partItemID][0].buyNum = itemMap[partItemID][0].buyNum + 1
                        else
                            itemMap[partItemID][0].invNum = itemMap[partItemID][0].invNum + 1
                        end
                    end
                end

            end
        else
            local itemName = ItemConfigTool.GetItemName(id)
            local durabilitylvl = 0
            if slotType == ESlotType.BreastPlate or slotType == ESlotType.Helmet then
               local equipPosition = Module.ArmedForce.Field:GetCurOperationOutfitEquipPositionBySlotType(slotType)
                if equipPosition then
                    local propInfo = equipPosition.load_props[1]
                    if propInfo then
                        durabilitylvl = Server.AuctionServer:GetArmorDurabilitylvlByDurability(id, propInfo.health_max or 0)
                    end
                end
            end
            logerror(string.format("[OutfitDataLogic.GeneratesOutfitPlanItemDataList] 确认方案【装备】 itemID = %d. itemName = %s, durabilitylvl = %d ,invNum = %s, buyNum = %s", id, itemName, durabilitylvl, invNum, needBuyNum))
            declare_if_nil(itemMap, id, {
                [durabilitylvl] = 
                {
                    buyNum = 0,
                    invNum = 0
                }
            })
            if invNum > 0 then
                itemMap[id][durabilitylvl].invNum = itemMap[id][durabilitylvl].invNum + invNum
            end
            if needBuyNum > 0 then
                itemMap[id][durabilitylvl].buyNum = itemMap[id][durabilitylvl].buyNum + needBuyNum
            end
        end
    end


    for _, slotType in ipairs(OutfitDataLogic.OutfitTypeList) do
        local presetData = mapPos2FitPresetData[slotType]
        if presetData then
            fProcessPresetData(presetData, slotType)
        end
    end


    for id, quickOperationDataInfo in pairs(allQuickOperationDataInfo) do
        local itemName = ItemConfigTool.GetItemName(id)
        local buyNum = quickOperationDataInfo:GetBuyNum()
        local moveNum = quickOperationDataInfo:GetMoveNum()
        local allBuyNum = 0
        local allMoveNum = 0
        if quickOperationDataInfo.itemMainType == EItemType.Bullet then
            local weaponBulletBuyNum = quickOperationDataInfo:GetWeaponBulletBuyNum()
            allBuyNum = buyNum + weaponBulletBuyNum
            local weaponBulletMoveNum = quickOperationDataInfo:GetWeaponBulletMoveNum()
            allMoveNum = moveNum + weaponBulletMoveNum
        elseif quickOperationDataInfo.itemMainType == EItemType.Medicine then
            allBuyNum = buyNum
            allMoveNum = moveNum
        end
        logerror(string.format("[OutfitDataLogic.GeneratesOutfitPlanItemDataList] 确认方案【物资】 itemID = %d. itemName = %s, allMoveNum = %s, allBuyNum = %s", id, itemName, allMoveNum, allBuyNum))
        declare_if_nil(itemMap, id, {
            [0] = 
            {
                buyNum = 0,
                invNum = 0
            }
        })
        if allMoveNum > 0 then
            itemMap[id][0].invNum = itemMap[id][0].invNum + allMoveNum
        end
        if allBuyNum > 0 then
            itemMap[id][0].buyNum = itemMap[id][0].buyNum + allBuyNum
        end
    end

    if not table.isempty(itemMap) then
        for id, durabilitylvlDatas in pairs(itemMap) do
            for durabilitylvl, value in pairs(durabilitylvlDatas) do
                if value.invNum > 0 then
                    fInsertData(id, value.invNum, durabilitylvl, false)
                end
                if value.buyNum > 0 then
                    fInsertData(id, value.buyNum, durabilitylvl, true)
                end
            end
        end
    end

    return itemDataList
end


-- OutfitDataLogic.CheckCanPlaceItemsByItem = function(item, slotType)
--     if ItemHelperTool.IsBagSlotType(slotType) then
--         local slot = Server.InventoryServer:GetSlot(slotType)
--         local containerSlotType = slot:GetContainerSlotType()
--         local containerSlot = Server.InventoryServer:GetSlot(containerSlotType)
--         local allItems = containerSlot:GetItems()
--         local fakeSlot = ItemSlot:New(containerSlotType, ESlotGroup.None, item.id)
--         if allItems and next(allItems) then
--             local result, mapItem2ActualPos, unfitItems  = ItemOperaTool.TryPlaceItems(allItems, fakeSlot)
--             if not result and unfitItems and next(unfitItems) then
--                 local equipedItem = slot:GetEquipItem()
--                 if equipedItem then
--                     table.insert(unfitItems, equipedItem)
--                 end
--                 local depositorySlot = Server.InventoryServer:GetSlot(ESlotType.MainContainer)
--                 result, mapItem2ActualPos, unfitItems  = ItemOperaTool.TryPlaceItems(unfitItems, depositorySlot)
--                 return result
--             end
--         end
--     end
--     return true
-- end
--#endregion ==============================================检查所需道具能否放下容器========================================================
return OutfitDataLogic
