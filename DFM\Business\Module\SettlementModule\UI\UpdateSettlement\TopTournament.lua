----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSettlement)
----- LOG FUNCTION AUTO GENERATE END -----------

local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"
local TopTournamentTeam = require "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.TopTournamentTeam"
local SettlementLogic = require "DFM.Business.Module.SettlementModule.SettlementLogic"
local SettlementConfig = require "DFM.Business.Module.SettlementModule.SettlementConfig"
local HDKeyIconBoxText = require "DFM.Business.Module.CommonWidgetModule.UI.HD.HDKeyIconBoxText"
local HDKeyIconBox = require "DFM.Business.Module.CommonWidgetModule.UI.HD.HDKeyIconBox"
local InputSummaryItemHD = require "DFM.Business.Module.CommonBarModule.UI.BottomBarHD.InputSummaryItemHD"
local EDFMGamePlayMode = import "EDFMGamePlayMode"
local EOutStandingContributionType = import "EOutStandingContributionType"

--BEGIN MODIFICATION @ VIRTUOS ：UI导航
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local UGPInputDelegates = import "GPInputDelegates"
local UGPInputHelper = import "GPInputHelper"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
local EGPInputType                  = import    ("EGPInputType")
--END MODIFICATION

---@class TopTournament : LuaUIBaseView
local TopTournament = ui("TopTournament")

local function xxwwinfo(...)
    loginfo("[xxww] ", ...)
end

local function xxwwwarning(...)
    logwarning("[xxww] @warning@", ...)
end

local function xxwwerror(...)
    logerror("[xxww] @error@", ...)
end

local JumpOverKeyName = "JumpOver"

function TopTournament:Ctor()
    self._wtTitleText = self:Wnd("DFTextBlock_65", UITextBlock)
    self._wtGameTimeText = self:Wnd("TextBlock", UITextBlock)
    self._wtEnemyNumTxt = self:Wnd("TextBlock_2", UITextBlock)
    self._wtEnemyNumNameTxt = self:Wnd("TextBlock_1", UITextBlock)
    self._wtPointNumTxt = self:Wnd("TextBlock_4", UITextBlock)
    self._wtPointNumNameTxt = self:Wnd("TextBlock_3", UITextBlock)
    self._wtCampNameText = self:Wnd("DFTextBlock_3", UITextBlock)
    self._wtCommanderNameText = self:Wnd("DFTextBlock_1", UITextBlock)
    self._wtCommanderTitleText = self:Wnd("DFTextBlock_2", UITextBlock)

    self._wtCommanderPraiseNumText = self:Wnd("DFTextBlock_118", UITextBlock)

    self._wtPraiseBtn = self:Wnd("WBP_CommonButtonV3S1_91", DFCommonButtonOnly)
    self._wtPraiseBtn:Event("OnClicked", self._OnPraiseBtnClick, self)

    self._wtPraisePanel = self:Wnd("DFCanvasPanel_105", UIWidgetBase)
    self._wtPraisePanel:Collapsed()

    self._wtPraisePanel1 = self:Wnd("DFCanvasPanel_103", UIWidgetBase)
    self._wtPraisePanel1:Collapsed()

    ---@type TopTournamentTeam[]
    self._teamList = {}
    local index = 0
    while true do
        local team
        if index == 0 then
            team = self:Wnd("WBP_Settlement_TopTournament_Team", TopTournamentTeam)
        else
            team = self:Wnd("WBP_Settlement_TopTournament_Team_" .. index, TopTournamentTeam)
        end
        if team == nil then
            break
        end
        table.insert(self._teamList, team)
        team:Collapsed()
        index = index + 1
    end
    if IsHD() then
        self._wtScoreBoardBtn = self:Wnd("WBP_TopBarHD_InputSummary", InputSummaryItemHD)
        self._wtScoreBoardBtn:SetData("Scoreboard_Settlement", self._OnScoreBoardBtnClicked)
        self._wtConfrimBtn = self:Wnd("WBP_TopBarHD_InputSummary_2", InputSummaryItemHD)
        if self._wtConfrimBtn then
            self._wtConfrimBtn:SetData("TopTournament_Select", nil)
        end

        self._wtQuitBtn = self:Wnd("WBP_TopBarHD_InputSummary_1", InputSummaryItemHD)
        self._wtQuitBtn:SetData("Settlement_Quit", self._OnQuitBtnClick)
        self._wtSkipBtn = self:Wnd("WBP_CommonKeyIconBoxText", HDKeyIconBoxText)
        self._wtSkipBtn:SetKeyByDisplayInputActionName(JumpOverKeyName)
        self._wtSkipBtnIcon = self._wtSkipBtn:Wnd("WBP_CommonKeyIconBox", HDKeyIconBox)
        Module.CommonBar:RegStackUIDefaultEsc(self, false)
        self._wtQuitText = self:Wnd("DFTextBlock_4", UITextBlock)
        self._wtBottomTips = self:Wnd("DFCanvasPanel_31", UIWidgetBase)
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.Hidden)
    else
        self._wtScoreBoardBtn = self:Wnd("WBP_CommonIconButton", UIButton)
        self._wtScoreBoardBtn:Event("OnClicked", self._OnScoreBoardBtnClicked, self)
        self._wtQuitBtn = self:Wnd("WBP_CommonButtonV2S1", UIButton)
        self._wtQuitBtn:Event("OnClicked", self._OnQuitBtnClick, self)
        self._wtSkipBtn = self:Wnd("wt_SkipBtn", UIButton)
        self._wtSkipBtn:Event("OnClicked", self._OnSkipBtnClicked, self)
        self._wtSkipBtn:SetMainTitleText4AllState(Module.Settlement.Config.Loc.SkipScroll)
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.Hidden)
    end
    self._wtRecordPanel = self:Wnd("DFCanvasPanel_29", UIWidgetBase)
    self._wtCampPanel = self:Wnd("DFCanvasPanel_145", UIWidgetBase)
    self._startScrollTimer = nil
    self._showAnim =
        IsHD() and self.WBP_Settlement_TopTournament_Main_PC_S_in or self.WBP_Settlement_TopTournament_Main_in
    self._scrollAnim =
        IsHD() and self.WBP_Settlement_TopTournament_Main_PC_S_in_2 or self.WBP_Settlement_TopTournament_Main_in_2
    self._bHasEndTheScroll = false
    self._quitTimer = nil
    self._quitEndTime = 0
    self._curBtnProgress = 0
    self._bIsSkipBtnVisible = false

    self._commanderPlayerId = 0
    self._commonderPraiseNum = 0
    self._lowPraiseShowAnim = self.Low_praise
    self._lowPraiseShowAnim_loop = self.Low_praise_loop

    self._highPraiseShowAnim = self.High_praise
    self._highPraiseShowAnim_loop = self.High_praise_loop

    self._lowPraiseShowAnim_in = self.Low_praise_in
    self._highPraiseShowAnim_in = self.High_praise_in

    self._commanderThumbedNum = 8

    self._itemEnterClick = false
end

function TopTournament:OnOpen()
    self:AddLuaEvent(Server.SettlementServer.Events.evtPlayerInfoBePraisedNtf, self._OnPlayerInfoBePraisedNtf, self)
    self:AddLuaEvent(Module.Settlement.Config.Events.evtTopTournamentItemClick, self._OnItemClickState, self)
    self:AddLuaEvent(Server.SettlementServer.Events.evtPlayerInfoPraisedClick, self._OnItemClickPraised, self)
    self:RefreshView()
end

function TopTournament:OnClose()
    if self._quitTimer then
        self._quitTimer:Release()
    end
    LuaTickController:Get():RemoveTick(self)
    self:RemoveAllLuaEvent()
end

function TopTournament:OnShow()
    self:_HideSkipButton()
    if self._bHasEndTheScroll then
        self:_BindScoreBoardAndQuitBtnPressed()
    else
        self:_UnBindScoreBoardAndQuitBtnPressed()
    end

end

function TopTournament:OnHide()
    self:_CancelHideSkipBtnTimer()
    self:_UnBindAnyKeyPress()
    self:_UnBindSkipBtnPress()
    self:_UnBindSkipBtnRelease()
    self:_UnBindMouseButtonDownEvent()
    self:_UnBindScoreBoardAndQuitBtnPressed()
end

function TopTournament:RefreshView()
    local commanderTitleName = SettlementLogic.GetCommanderGameContributorName(EOutStandingContributionType.ECommander)
    if commanderTitleName then
        self._wtCommanderTitleText:SetText(commanderTitleName)
    end
    local tdmData = Server.SettlementServer:GetTDMSettlementInfo()
    if tdmData == nil then
        return
    end
    local matchInfo = Server.SettlementServer:GetTDMSettlementMatchInfo()
    if matchInfo == nil then
        return
    end
    if tdmData.game_time then
        local gameTime = math.floor(tdmData.game_time)
        local minutes = math.floor(gameTime / 60)
        local seconds = gameTime - minutes * 60
        self._wtGameTimeText:SetText(string.format("%02d:%02d", minutes, seconds))
    end
    local myColor = tdmData.my_color
    local myCampScore = 0
    local enemyCampScore = 0
    if InGameController:Get():IsCaptureFlag() then
        for _, v in pairs(tdmData.camp_list) do
            if myColor == v.color then
                myCampScore = v.capture_flag_score
            else
                enemyCampScore = v.capture_flag_score
            end
        end
    end
    local gamePlayMode = InGameController:Get():GetGamePlayerMode()
    for _, v in pairs(tdmData.camp_list) do
        if myColor == v.color then
            self._wtTitleText:SetText(SettlementLogic.GetCommanderGameMotto(matchInfo.room_id, v.is_winner == 1))
            if v.is_winner == 1 then
                self._wtCampNameText:SetText(Module.Settlement.Config.Loc.CommanderGameSucceedCampName)
                self:SetType(0)
            else
                self._wtCampNameText:SetText(Module.Settlement.Config.Loc.CommanderGameFailCampName)
                self:SetType(1)
            end
            if InGameController:Get():IsCaptureFlag() then
                self._wtEnemyNumNameTxt:SetText(Module.Settlement.Config.Loc.FlagBattleScoreTips)
                self._wtEnemyNumTxt:SetText(myCampScore .. "/" .. enemyCampScore)
                self._wtPointNumNameTxt:Collapsed()
                self._wtPointNumTxt:Collapsed()
            elseif gamePlayMode == EDFMGamePlayMode.GamePlayMode_Breakthrough then
                if v.attacker == true then
                    self._wtEnemyNumNameTxt:SetText(Module.Settlement.Config.Loc.EnemyNumNameTextA)
                    self._wtEnemyNumTxt:SetText(v.od_camp.offense_reenforce)
                    self._wtPointNumNameTxt:SetText(Module.Settlement.Config.Loc.PointNumNameTextA)
                    self._wtPointNumTxt:SetText(
                        v.od_camp.offense_stronghold .. "/" .. v.od_camp.offense_total_stronghold
                    )
                else
                    self._wtEnemyNumNameTxt:SetText(Module.Settlement.Config.Loc.EnemyNumNameTextB)
                    self._wtPointNumNameTxt:SetText(Module.Settlement.Config.Loc.PointNumNameTextB)
                    self._wtEnemyNumTxt:SetText(v.od_camp.defense_kill_enemy)
                    self._wtPointNumTxt:SetText(
                        v.od_camp.defense_stronghold .. "/" .. v.od_camp.defense_total_stronghold
                    )
                end
            elseif gamePlayMode == EDFMGamePlayMode.GamePlayMode_Conquest then
                self._wtEnemyNumNameTxt:SetText(Module.Settlement.Config.Loc.ScoreRatio)
                self._wtEnemyNumTxt:SetText(v.od_camp.offense_reenforce .. "/" .. v.od_camp.defense_kill_enemy)
                self._wtPointNumNameTxt:Collapsed()
                self._wtPointNumTxt:Collapsed()
            end
            local id2team = {}
            local teamIDList = {}
            for _, team in pairs(v.team_list) do
                table.insert(teamIDList, team.team_id)
                id2team[team.team_id] = team
            end
            table.sort(teamIDList)
            local bFindCommander = false
            for index, teamID in ipairs(teamIDList) do
                local team = id2team[teamID]
                if self._teamList[index] then
                    self._teamList[index]:RefreshView(team)
                    self._teamList[index]:SelfHitTestInvisible()
                end
                if not bFindCommander then
                    for _, player in pairs(team.player_list) do
                        if player.commander_contributor_title == EOutStandingContributionType.ECommander then
                            if player.basic_info.game_nick ~= "" then
                                self._wtCommanderNameText:SetText(player.basic_info.game_nick)
                            else
                                self._wtCommanderNameText:SetText(player.ai_info.game_nick)
                            end

                            --BEGIN MODIFICATION @ VIRTUOS : Replace player name with PS5 OnlineId
                            if IsPS5Family() then
                                local plat_id = Module.Settlement:GetPlatIDByPlayerInfo(player)
                                if plat_id ~= nil and plat_id == PlatIDType.Plat_Playstation then
                                    local UDFMOnlineIdentityManager = import "DFMOnlineIdentityManager"
                                    local DFMOnlineIdentityManager = UDFMOnlineIdentityManager.Get(GetGameInstance())

                                    if DFMOnlineIdentityManager then
                                        local PS5OnlineId =
                                            DFMOnlineIdentityManager:GetPlayerPlatformIdByOpenId(player.player_id)
                                        if not string.isempty(PS5OnlineId) then
                                            self._wtCommanderNameText:SetText(PS5OnlineId)
                                        end
                                    end
                                end
                            end
                            --END MODIFICATION
                            self._commanderPlayerId = player.player_id
                            bFindCommander = true
                        end
                        if bFindCommander then
                            break
                        end
                    end
                end
            end
            if not bFindCommander then
                xxwwerror("TopTournament:RefreshView fail to find Commander")
                self._wtCommanderNameText:Collapsed()
                self._wtPraiseBtn:Collapsed()
            else
                if self._commanderPlayerId == Server.AccountServer:GetPlayerId() then
                    self._wtPraiseBtn:Collapsed()
                else
                    self._wtPraiseBtn:SelfHitTestInvisible()
                end
            end
        end
    end
    if IsHD() then
        self._wtBottomTips:Collapsed()
    else
        self._wtScoreBoardBtn:Collapsed()
        self._wtQuitBtn:Collapsed()
        self._wtSkipBtn:Collapsed()
    end
    self._startScrollTimer =
        Timer.DelayCall(SettlementConfig.CommanderGameConfig.ClosingCreditsStableSeconds, self._StartScroll, self)
    local kickTime = SettlementConfig.TDMQuitTimerSeconds
    self._quitEndTime = TimeUtil.GetCurrentTime() + kickTime
    self._quitTimer = Timer:NewIns(1, kickTime)
    self._quitTimer:AddListener(self._RefreshQuitTimer, self)
    self._quitTimer:Start()
    self:PlayWidgetAnim(self._showAnim)
end

function TopTournament:_StartScroll()
    if self._startScrollTimer then
        Timer.CancelDelay(self._startScrollTimer)
        self._startScrollTimer = nil
    end
    self:PlayWidgetAnim(
        self._scrollAnim,
        1,
        EUMGSequencePlayMode.Forward,
        SettlementConfig.CommanderGameConfig.ClosingCreditsRollSpeed,
        false
    )
end

function TopTournament:OnAnimFinished(animation)
    if animation == self._scrollAnim then
        self:_OnScrollEnd()
    end
    if animation == self._lowPraiseShowAnim then
        loginfo("TopTournament:_OnPlayerInfoBePraisedNtf  _lowPraiseShowAnim_loop  ")
        self:PlayWidgetAnim(self._lowPraiseShowAnim_loop, 0)
    end
    if animation == self._highPraiseShowAnim then
        self:PlayWidgetAnim(self._highPraiseShowAnim_loop, 0)
    end
end

function TopTournament:_OnScrollEnd()
    if self._bHasEndTheScroll then
        return
    end
    self._bHasEndTheScroll = true
    if IsHD() then
        self._wtBottomTips:SelfHitTestInvisible()
        self:_BindScoreBoardAndQuitBtnPressed()
    else
        self._wtScoreBoardBtn:SelfHitTestInvisible()
        self._wtQuitBtn:SelfHitTestInvisible()
    end
    self:_HideSkipButton()
end

function TopTournament:_OnScoreBoardBtnClicked()
    LogAnalysisTool.SignButtonClicked(10100001)
    Facade.UIManager:AsyncShowUI(UIName2ID.BF_All)
end

function TopTournament:_OnQuitBtnClick()
    loginfo("TopTournament:_OnQuitBtnClick self._itemEnterClick = ", self._itemEnterClick)
    if self._itemEnterClick then
        return
    end
    SettlementLogic.EndSettlement()
end

function TopTournament:_OnSkipBtnClicked()
    if self._bHasEndTheScroll then
        return
    end
    if self._startScrollTimer then
        Timer.CancelDelay(self._startScrollTimer)
        self._startScrollTimer = nil
    end
    if self:IsAnimationPlayingExactly(self._scrollAnim) then
        self:StopWidgetAnim(self._scrollAnim)
    end
    self:_OnScrollEnd()
    self:SkipAnimation(self._scrollAnim)
end

function TopTournament:_RefreshQuitTimer()
    local remainTime = self._quitEndTime - TimeUtil.GetCurrentTime()
    if IsHD() then
        self._wtQuitText:SetText(string.format(Module.Settlement.Config.Loc.BackToLobbyRemainTimeTip, remainTime))
    else
        self._wtQuitBtn:BP_SetMainTitle(string.format(Module.Settlement.Config.Loc.Back2Lobby, remainTime))
    end
    if remainTime <= 0 then
        self:_OnQuitBtnClick()
    end
end

--region 跳过相关
function TopTournament:_BindAnyKeyPress()
    self:_UnBindAnyKeyPress()
    self._anyKeyPressHandle =
        self:AddInputActionBinding(
        "AnyKey",
        EInputEvent.IE_Pressed,
        self._OnAnyKeyPressed,
        self,
        EDisplayInputActionPriority.UI_Pop
    )
end

function TopTournament:_UnBindAnyKeyPress()
    if self._anyKeyPressHandle then
        self:RemoveInputActionBinding(self._anyKeyPressHandle)
        self._anyKeyPressHandle = nil
    end
end

function TopTournament:_BindMouseButtonDownEvent()
    self:_UnBindMouseButtonDownEvent()
    self._mouseButtonDownHandle =
        UDFMGameHudDelegates.Get(GetGameInstance()).OnHandleMouseButtonDownEvent:Add(self._OnMouseButtonDown, self)
end

function TopTournament:_UnBindMouseButtonDownEvent()
    if self._mouseButtonDownHandle then
        UDFMGameHudDelegates.Get(GetGameInstance()).OnHandleMouseButtonDownEvent:Remove(self._mouseButtonDownHandle)
        self._mouseButtonDownHandle = nil
    end
end

function TopTournament:_BindSkipBtnPress()
    if IsHD() then
        self:_UnBindSkipBtnPress()
        self._skipBtnPressHandle =
            self:AddInputActionBinding(
            JumpOverKeyName,
            EInputEvent.IE_Pressed,
            self._OnSkipBtnPressed,
            self,
            EDisplayInputActionPriority.UI_Pop
        )
    end
end

function TopTournament:_UnBindSkipBtnPress()
    if IsHD() then
        if self._skipBtnPressHandle then
            self:RemoveInputActionBinding(self._skipBtnPressHandle)
            self._skipBtnPressHandle = nil
        end
    end
end

function TopTournament:_BindSkipBtnRelease()
    if IsHD() then
        self:_UnBindSkipBtnRelease()
        self._skipBtnReleaseHandle =
            self:AddInputActionBinding(
            JumpOverKeyName,
            EInputEvent.IE_Released,
            self._OnSkipBtnReleased,
            self,
            EDisplayInputActionPriority.UI_Pop
        )
    end
end

function TopTournament:_UnBindSkipBtnRelease()
    if IsHD() then
        if self._skipBtnReleaseHandle then
            self:RemoveInputActionBinding(self._skipBtnReleaseHandle)
            self._skipBtnReleaseHandle = nil
        end
        self:_OnSkipBtnReleased()
    end
end

function TopTournament:_BindScoreBoardAndQuitBtnPressed()
    if IsHD() then
        self:_UnBindScoreBoardAndQuitBtnPressed()
        self._scoreBoardBtnPressHandle =
            self:AddInputActionBinding(
            "Scoreboard_Settlement",
            EInputEvent.IE_Pressed,
            self._OnScoreBoardBtnClicked,
            self,
            EDisplayInputActionPriority.UI_Pop
        )
        self._quitBtnPressHandle =
            self:AddInputActionBinding(
            "Settlement_Quit",
            EInputEvent.IE_Pressed,
            self._OnQuitBtnClick,
            self,
            EDisplayInputActionPriority.UI_Pop
        )
        if self._PraiseButton == nil then
            self._PraiseButton =
                self:AddInputActionBinding(
                "PraiseCommander",
                EInputEvent.IE_Pressed,
                self._OnPraiseBtnClick,
                self,
                EDisplayInputActionPriority.UI_Stack
            )
            self._wtPraiseBtn:SetDisplayInputActionWithLongPress(
                self._PraiseButton,
                self,
                "PraiseCommander",
                true,
                nil,
                true
            )
        end

        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.X_Accept, self)

        if not self._navGroups then
            self._navGroups = {
            }
    
            for index, value in ipairs(self._teamList) do
                local temp = WidgetUtil.RegisterNavigationGroup(value._wtCustomWaterFallList, self, "Hittest")
                temp:AddNavWidgetToArray(value._wtCustomWaterFallList)
                temp:SetScrollRecipient(value._wtCustomWaterFallList)
                table.insert(self._navGroups, temp)
            end
            if #self._navGroups > 0 then
                WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroups[1])
            end
        end

    end
end

function TopTournament:_UnBindScoreBoardAndQuitBtnPressed()
    if IsHD() then
        if self._scoreBoardBtnPressHandle then
            self:RemoveInputActionBinding(self._scoreBoardBtnPressHandle)
            self._scoreBoardBtnPressHandle = nil
        end
        if self._quitBtnPressHandle then
            self:RemoveInputActionBinding(self._quitBtnPressHandle)
            self._quitBtnPressHandle = nil
        end

        if self._PraiseButton then
            self:RemoveInputActionBinding(self._PraiseButton)
            self._PraiseButton = nil
        end

        WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
        if self._navGroups then
            WidgetUtil.RemoveNavigationGroup(self)
            self._navGroups = nil
        end
    end
end

function TopTournament:_ResetHideSkipBtnTimer()
    self:_CancelHideSkipBtnTimer()
    self._hideSkipBtnTimer =
        Timer.DelayCall(SettlementConfig.CommanderGameConfig.SkipBtnHideSeconds, self._HideSkipButton, self)
end

function TopTournament:_CancelHideSkipBtnTimer()
    if self._hideSkipBtnTimer then
        Timer.CancelDelay(self._hideSkipBtnTimer)
        self._hideSkipBtnTimer = nil
    end
end

function TopTournament:_OnAnyKeyPressed()
    self:_ShowSkipButton()
end

function TopTournament:_OnMouseButtonDown()
    self:_ShowSkipButton()
end

function TopTournament:_ShowSkipButton()
    self._wtSkipBtn:SelfHitTestInvisible()
    self._bIsSkipBtnVisible = true
    self:_ResetHideSkipBtnTimer()
    self:_UnBindAnyKeyPress()
    self:_UnBindMouseButtonDownEvent()
    self:_BindSkipBtnPress()
    self:_BindSkipBtnRelease()
end

function TopTournament:_HideSkipButton()
    self._wtSkipBtn:Collapsed()
    self._bIsSkipBtnVisible = false
    self:_CancelHideSkipBtnTimer()
    self:_UnBindSkipBtnPress()
    self:_UnBindSkipBtnRelease()
    if self._bHasEndTheScroll then
        self:_UnBindAnyKeyPress()
        self:_UnBindMouseButtonDownEvent()
    else
        self:_BindAnyKeyPress()
        self:_BindMouseButtonDownEvent()
    end
end

function TopTournament:_OnSkipBtnPressed()
    if IsHD() then
        self:_OnSkipBtnReleased()
        LuaTickController:Get():RegisterTick(self)
        self:_CancelHideSkipBtnTimer()
    end
end

function TopTournament:_OnSkipBtnReleased()
    if IsHD() then
        self._curBtnProgress = 0
        self._wtSkipBtnIcon:BP_UpdateProgressBar(self._curBtnProgress)
        LuaTickController:Get():RemoveTick(self)
        self:_ResetHideSkipBtnTimer()
    end
end

function TopTournament:Update(dt)
    if IsHD() then
        self._curBtnProgress =
            math.clamp(self._curBtnProgress + dt / SettlementConfig.CommanderGameConfig.SkipBtnLongPressSeconds, 0, 1)
        self._wtSkipBtnIcon:BP_UpdateProgressBar(self._curBtnProgress)
        if self._curBtnProgress >= 1 then
            self:_OnSkipBtnClicked()
        end
    end
end

---endregion

--#region 点赞
function TopTournament:_OnPlayerInfoBePraisedNtf(playerId, num)
    loginfo("TopTournament:_OnPlayerInfoBePraisedNtf  player   num   ", playerId, num)
    local commanderThumbedNum = Facade.TableManager:GetRowByKey("FriendFavorabilityParam", tostring(self._commanderThumbedNum))
    local highNum = 10
    if commanderThumbedNum then
        highNum = setdefault(commanderThumbedNum.ParamValue, 10)
    end
    if self._commanderPlayerId == playerId and num > 0 then
        self._wtPraisePanel:SelfHitTestInvisible()
        self._wtPraisePanel1:SelfHitTestInvisible()
    --展示指挥官被点赞的数量和动效
        local oldNum = setdefault(self._commonderPraiseNum, 0)
        self._commonderPraiseNum = setdefault(num, 0)

        self._wtCommanderPraiseNumText:SetText("x " .. self._commonderPraiseNum)
        loginfo("TopTournament:_OnPlayerInfoBePraisedNtf  oldMum   newNum  highNum  ", oldNum, self._commonderPraiseNum, highNum)
        if oldNum < 1 and self._commonderPraiseNum >= 1 and self._commonderPraiseNum <= highNum then
            self:PlayWidgetAnim(self._lowPraiseShowAnim)
            loginfo("TopTournament:_OnPlayerInfoBePraisedNtf  _lowPraiseShowAnim  ")
        end

        if oldNum <= highNum and self._commonderPraiseNum > highNum then
            self:PlayWidgetAnim(self._highPraiseShowAnim)
        end

        if oldNum >= 1 and self._commonderPraiseNum <= highNum then
            self:PlayWidgetAnim(self._lowPraiseShowAnim_in)
        end

        if oldNum > highNum and self._commonderPraiseNum > highNum + 1 then
            self:PlayWidgetAnim(self._highPraiseShowAnim_in)
        end
        Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UICammanderBePraised)
    end
end

function TopTournament:_OnPraiseBtnClick()
    self._wtPraiseBtn:Collapsed()
    Server.SettlementServer:PlayerCommanderModePraiseReq(self._commanderPlayerId)
end

function TopTournament:_OnItemClickState(state)
    self._itemEnterClick = state
    loginfo("TopTournament:_OnItemClickState self._itemEnterClick = ", self._itemEnterClick)
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    if self._itemEnterClick then
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default, self)
    else
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.X_Accept, self)
    end
end

function TopTournament:_OnItemClickPraised(clickPlayerId, bePraisedId)
    if clickPlayerId == Server.AccountServer:GetPlayerId() and bePraisedId == self._commanderPlayerId then
        self._wtPraiseBtn:Collapsed()
    end
end

--#endregion

return TopTournament
