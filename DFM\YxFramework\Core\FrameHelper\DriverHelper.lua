---@diagnostic disable: undefined-global
local DriverHelper = {}
local ULuaExtension = import "LuaExtension"
local UInputMonitor = import "InputMonitor"
local UDFMGameHudDelegates = import "DFMGameHudDelegates"
local UGameplayStatics = import "GameplayStatics"
local FKey = import "Key"
local LAI = import "LAI"
local ULuaSubsystem = import "LuaSubsystem"
local UKismetInputLibrary = import "KismetInputLibrary"

--------------------------------------------------------------------------
--- LuaDriver Helper 私有工具类
--------------------------------------------------------------------------
function DriverHelper.PreInit()
    DriverHelper._StartLuaPanda()
    DriverHelper._CheckLuaVersion()
    DriverHelper._ReplaceLuaFunc()
    DriverHelper._InitErrorTipsTool()
end

local newInputProxy
function DriverHelper.PostInit()
    DriverHelper._PostInitErrorTipsTool()
    DriverHelper._LoadDebugLib()
    DriverHelper._InitHotUpdate()
    DriverHelper._InitGcStep()
    DriverHelper._InitDebugEnvTool()
    DriverHelper._InitSnapshotTool()
    DriverHelper._InitLocalTestLogic()

    local gameInst = GetGameInstance()
    newInputProxy = UInputMonitor(gameInst)
    newInputProxy:RegisterMouseButtonDown()

    DriverHelper._InitExecuteConsoleCommand()
end


function DriverHelper._InitExecuteConsoleCommand()
    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        "slate.bUseDragDropAnimation 0",
        nil
    )
end

function DriverHelper.PostRelease()

end

local bShouldStartLuaPandaThisTime = false
function GetShouldStartLuaPandaThisTime()
    return bShouldStartLuaPandaThisTime
end

function SetShouldStartLuaPandaNextLaunch()
    print("SetShouldStartLuaPandaNextLaunch")

    local GConfig = FConfigCacheIni.GetGlobalConfig()
    local GGameIni = FConfigCacheIni.LoadGlobalIni("Game")
    local defaultSession = "LuaFrontEnd"
    LAI.SetConfigCacheIniString(defaultSession, "bShouldStartLuaPandaThisTime", "1", GGameIni)
    GConfig:Flush(true, GGameIni)

    bShouldStartLuaPandaThisTime = true
end

function SetShouldRestartLua(bShouldRestart)
    ULuaSubsystem.Get().bShoudlRestartLua = bShouldRestart
end

local function fShouldStartLuaPanda()
    local GConfig = FConfigCacheIni.GetGlobalConfig()
    local GGameIni = FConfigCacheIni.LoadGlobalIni("Game")
    local defaultSession = "LuaFrontEnd"
    local bFound, bShouldStartLuaPanda = LAI.GetConfigCacheIniString(defaultSession, "bShouldStartLuaPandaThisTime", GGameIni, "")
    print("fShouldStartLuaPanda", bFound, bShouldStartLuaPanda)
    if bFound and bShouldStartLuaPanda == "1" then
        LAI.SetConfigCacheIniString(defaultSession, "bShouldStartLuaPandaThisTime", "0", GGameIni)
        GConfig:Flush(true, GGameIni)

        SetShouldRestartLua(false)

        return true
    end

    if _WITH_EDITOR == 1 then return true end

    return false
end

function DriverHelper._StartLuaPanda()
    local KismetSystemLibrary=import "KismetSystemLibrary"
    local StringUtil = require("DFM.YxFramework.Util.StringUtil")
    local commandline = nil
    if KismetSystemLibrary~=nil then
        commandline=string.lower(KismetSystemLibrary.GetCommandLine())
    end

    if string.find(commandline, "-luadebughost=") ~= nil then -- 用于真机调试（比如PS5和XBox），命令行中指定了需要连接的VS Code Host的ip地址，这种情况下直接以Client的方式连接
        local HostIpPos=string.find(commandline, "-luadebughost=")+#"-luadebughost="
        local HostIp=StringUtil.StringSplit(string.sub(commandline, HostIpPos, #commandline), " ")[1]
        if HostIp~=nil then
            require("DFM.YxFramework.Plugin.LuaPanda.LuaPanda").start(HostIp, 8818)
            print("--------------------- LuaPanda Start As Client Connect to Host ： ", HostIp)
            return
        end
    end

    if fShouldStartLuaPanda() then
        require("DFM.YxFramework.Plugin.LuaPanda.LuaPanda").start("127.0.0.1", 8818)
        -- -- 判断是否有成功引入slua_profiler模块
        -- if slua_profile and false then
        --     -- 根据实际的情况填入对应的 host 和 port
        --     ULuaExtension.SetEnableLuaProfilerTick(true)
        --     slua_profile.start()
        -- end
        print("--------------------- LuaPanda Start As Client Connect to localhost ---------------------")
    end
    
end

function DriverHelper._CheckLuaVersion()
    if _VERSION == "Lua 5.3" then
        function table.maxn(t)
            return #t
        end

        function unpack(...)
            return table.unpack(...)
        end

        function loadstring(...)
            return load(...)
        end
        
        --- 此处的getfenv和setfenv【无法】完全还原原先env的设置效果
        --- 但由于多个类已经依赖于这两个方法，无法判断是否可以直接替换
        --- 因此保守做法先保留这个版本的方法
        function getfenv(f)
            if type(f) == "function" then
                local name, value = debug.getupvalue(f, 1)
                if name == "_ENV" then
                    return value
                else
                    return _ENV
                end		
            end
        end
    
        function setfenv(f, Env)
            if type(f) == "function" then
                local name, value = debug.getupvalue(f, 1)
                if name == "_ENV" then
                    debug.setupvalue(f, 1, Env)
                end		
            end
        end
        
        debug = debug or {}
        debug.setfenv = setfenv
        debug.getfenv = getfenv

        --- 此处的新版本getfenv和setfenv方法【可以】还原原先5.2env的设置效果
        --- 为了沙盒作准备，此新方法先加入，之后尽量统一替代上面的方法
        function getfenvbox(fn)
            local i = 1
            while true do
                local name, val = debug.getupvalue(fn, i)
                if name == "_ENV" then
                    return val
                elseif not name then
                    break
                end
                i = i + 1
            end
        end
          
        function setfenvbox(fn, env)
            local i = 1
            while true do
                local name = debug.getupvalue(fn, i)
                if name == "_ENV" then
                    debug.upvaluejoin(fn, i, (function()
                        return env
                    end), 1)
                    break
                elseif not name then
                    break
                end
                i = i + 1
              end
            return fn
        end
    end
end

function DriverHelper._ReplaceLuaFunc()
    if not UE_BUILD_SHIPPING then
        -- local oldsetmetatable = setmetatable
        -- setmetatable = function(t, meta)
        --     if t == meta then
        --         return nil
        --     end
        --     return oldsetmetatable(t, meta)
        -- end

        -- 本意是明确迭代器传空的报错；
        -- 但这里可能会导致递归的error，从而cstackoverflow；
        -- 使用LuaPanda调试（先Attach再Play必现），可能是LuaPanda本身也劫持了ipairs/pairs接口；
        -- 因此先注释掉这部分逻辑，保证调试器正常工作；
        -- local oldIpairs = ipairs
        -- local oldPairs = pairs
    
        -- function ipairs(tb)
        --     ensure(tb, "Pass nil to ipairs")
        --     return oldIpairs(tb)
        -- end
    
        -- function pairs(tb)
        --     ensure(tb, "Pass nil to pairs")
        --     return oldPairs(tb)
        -- end
    else
        return
    end
end

function DriverHelper._InitErrorTipsTool()
    require("DFM.YxFramework.Tool.Debug.LuaErrorTipsTool")
end

function DriverHelper._PostInitErrorTipsTool()
    InitLuaErrorReport()
end

function DriverHelper._LoadDebugLib()
    local dir
    if _WITH_EDITOR == 1 then
        dir = FPaths.ProjectDir() .. "LuaSource" .. "/" .. "LuaDebug/"
    else
        dir = FPaths.ProjectSavedDir() .. "LuaDebug/"
    end
    local Ext = "*.lua"
    local DebugFiles = ULuaExtension.Ext_GetDebugFile(dir, Ext)
    for _, DebugFile in pairs(DebugFiles) do
        print("Find Debug File: ", DebugFile)
        local result, chotUpdatenk = ULuaExtension.Ext_LoadFileToString("", dir .. DebugFile)
        if result and chotUpdatenk then
            print(chotUpdatenk)
            local f = load(chotUpdatenk)
            if f and type(f) == "function" then
                print("Load Debug File Succeed! ", DebugFile)
                f()
            end
        end
    end
end

local firstKey = FKey()
firstKey.KeyName = "D"

local secondKey = FKey()
secondKey.KeyName = "F"

local thirdKey = FKey()
thirdKey.KeyName = "M"

local bInitHotUpdate
function DriverHelper._InitHotUpdate()
    if not IsInEditor() then
        return
    end
    if bInitHotUpdate then
        return
    end
    bInitHotUpdate = true

    local hotUpdate = require "DFM.YxFramework.Plugin.HotUpdate.LuaHotUpdate"
    local loadHelper = require "DFM.YxFramework.Plugin.HotUpdate.ReloadFunction"

    local LuaExtension = import "LuaExtension"
    hotUpdate.SetFileLoader(loadHelper.InitFileMap, loadHelper.LoadStringFunc, LuaExtension.Ext_GetModifyTimeStamp)
    hotUpdate.Init("HotUpdateList", {_luadir}, LogUtil.LogScreen, nil, {requirecpp = true})
    
    -- local function fHotReloadUpdate()
    --     local bApplyHotReload = Facade.ConfigManager:GetBoolean("bToggleTickHotReload", false)
    --     if not bApplyHotReload then
    --         return
    --     end
    --     hotUpdate.Update()
    -- end

    -- if _WITH_EDITOR == 1 then
    --     local timerIns = Timer:NewIns(1, 0)
    --     timerIns:AddListener(fHotReloadUpdate)
    --     timerIns:Start()

    --     hotUpdate.PreCollectModifyTS()
    --     -- fHotReloadUpdate()
    -- end

    -- 通过快捷键触发，不Tick
    if _WITH_EDITOR == 1 then
        hotUpdate.PreCollectModifyTS()

        local function fOnKeyUp(inKeyEvent)
            local key = UKismetInputLibrary.GetKey(inKeyEvent)
            if key.KeyName == thirdKey.KeyName then
                local player = UGameplayStatics.GetPlayerController(GetWorld(), 0)
                -- loginfo("ZZZZ", player:IsInputKeyDown(thirdKey), player:IsInputKeyDown(secondKey), player:IsInputKeyDown(firstKey))
                if player and player:IsInputKeyDown(secondKey) and player:IsInputKeyDown(firstKey) then
                    DriverHelper_ApplyHotReload()
                end
            end

        end
        UDFMGameHudDelegates.Get(GetGameInstance()).OnHandleKeyUpEvent:Add(fOnKeyUp)
    end
end

---应用热重载
function DriverHelper_ApplyHotReload()
    local hotUpdate = require "DFM.YxFramework.Plugin.HotUpdate.LuaHotUpdate"
    hotUpdate.Update()
end

local pause = 110
local step = 500
function DriverHelper._InitGcStep()
    -- logwarning("[ Lua GC Debug ] ******* DriverHelper._InitGcStep() -> ", pause, step)
    collectgarbage("setpause", pause)
    collectgarbage("setstepmul", step)
    if DFHD_LUA == 1 then
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.LuaSystemCallPreUEGCHandle 1", nil)
    end

    local bLuaLowMemoryEnable = ULuaSubsystem.GetIsLuaLowMemoryEnable()
    logwarning("DriverHelper::_InitGcStep", pause, step, bLuaLowMemoryEnable)
    if bLuaLowMemoryEnable then
        local limitSeconds = 1.0 / 1000.0
        local limitCount = 64
        local interval = 0
        DriverHelper._SetGCParam(limitSeconds, limitCount, interval)
    end
end

function DriverHelper._SetGCParam(limitSeconds, limitCount, interval)
    logwarning("DriverHelper::_SetGCParam", limitSeconds, limitCount, interval)
    ULuaSubsystem.Get().SetGCParam(GetWorld(), limitSeconds, limitCount, interval)
end

function DriverHelper._InitDebugEnvTool()
    if IsInEditor() then
        require "DFM.YxFramework.Tool.Debug.DebugEnvTool"
    end
end

function DriverHelper._InitSnapshotTool()
    require "DFM.YxFramework.Tool.Debug.SnapshotTool"
end

function DriverHelper._InitLocalTestLogic()
    if not IsInEditor() then
        return
    end

    local function f()
        require "LocalTest"
    end
    xpcall(f, function(err)
        LocalTest = {}
    end)
end

return DriverHelper