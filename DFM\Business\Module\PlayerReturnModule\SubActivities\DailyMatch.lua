----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMPlayerReturn)
----- LOG FUNCTION AUTO GENERATE END -----------



local IPlayerReturnSubActivity  = require "DFM.Business.Module.PlayerReturnModule.SubActivities.IPlayerReturnSubActivity"
local Filter                    = require "DFM.Business.DataStruct.Common.Base.Filter"
local F                         = require "DFM.Business.DataStruct.Common.Base.Functional"
local Sort                      = require "DFM.Business.DataStruct.Common.Base.Sort"

---@class PlayerReturnDailyMatchImpl : IPlayerReturnSubActivity
local PlayerReturnDailyMatchImpl = setmetatable({}, {__index = IPlayerReturnSubActivity})

function PlayerReturnDailyMatchImpl:GetType()
    ReturnActivityType.ReturnActivityTypeFight = 5 -- TODO: remove after protobuf enum update
    return ReturnActivityType.ReturnActivityTypeFight
end

function PlayerReturnDailyMatchImpl:GetVOEventNameWhenFirstEnter()
    return "Voice_Return_Radio_5"
end

function PlayerReturnDailyMatchImpl:GetSubPanelNavID()
    return UIName2ID.PlayerReturnDailyMatchPanel
end

function PlayerReturnDailyMatchImpl:GetActivityInfo()
    -- ActivityServer在拉取数据之后会自动给活动里的任务按照任务完成状态排序
    -- 但是对局爽打活动的任务应该按照任务ID，即对局爽打的天数顺序来处理
    -- 改ActivityServer会导致其他活动显示顺序不符合预期，这里加一层处理重新按task_id排序
    local activityInfo = IPlayerReturnSubActivity.GetActivityInfo(self)
    if not activityInfo then return end
    Sort.MergeSort(activityInfo.task_info, Sort.ByField("task_id"))
    return activityInfo
end

function PlayerReturnDailyMatchImpl:GetRedDotState()
    local activityInfo = self:GetActivityInfo()
    if not activityInfo then return false end

    local rewards = Filter.CountIf(activityInfo.task_info, F.Get("state"):Eq(F.V(ActivityTaskState.ActivityTaskStateCompleted)))
    return (rewards > 0)
end

function PlayerReturnDailyMatchImpl:GetDisplayedProgress()
    local activityInfo = self:GetActivityInfo()
    if not activityInfo then return 0, 0 end

    local progress = Filter.CountIf(activityInfo.task_info, F.Get("state"):GreaterEq(F.V(ActivityTaskState.ActivityTaskStateCompleted)))
    local total = #activityInfo.task_info

    return progress, total
end

function PlayerReturnDailyMatchImpl:GetUnlockedDays()
    local activityInfo = self:GetActivityInfo()
    if not activityInfo then return 0 end

    local unlocked = Filter.CountIf(activityInfo.task_info, F.Get("state"):GreaterEq(F.V(ActivityTaskState.ActivityTaskStateAccepted)))
    local days = activityInfo.return_info.login_day_num or 0
    return (unlocked > days) and unlocked or days
end

function PlayerReturnDailyMatchImpl:IsFinished()
    local progress, total = self:GetDisplayedProgress()
    return progress == total
end

function PlayerReturnDailyMatchImpl:GetLastPopupTime()
    local data = self:GetJsonStorage()
    local t = data.DailyMatchLastPopupTime[self:GetArmedForceMode()] or 0
    return t
end

---@param time integer timestamp, defaults to now
function PlayerReturnDailyMatchImpl:SetLastPopupTime(time)
    time = time or Facade.ClockManager:GetLocalTimestamp()
    local data = self:GetJsonStorage()
    data.DailyMatchLastPopupTime[self:GetArmedForceMode()] = time
    self:SetJsonStorage(data)
end

function PlayerReturnDailyMatchImpl:GetMPBuffState()
    if Server.ArmedForceServer:GetCurArmedForceMode() == EArmedForceMode.SOL then
        return 0, 0
    end

    local doubleExpRights = Server.CollectionServer:GetBattleConsumeChangeByFunctionId(CollectionFunctionalType.CollectionFunctional_MPExp)
    local doubleMeritsRights = Server.CollectionServer:GetBattleConsumeChangeByFunctionId(CollectionFunctionalType.CollectionFunctional_MPScore)
    doubleExpRights = (doubleExpRights or {newCurCount = 0}).newCurCount
    doubleMeritsRights = (doubleMeritsRights or {newCurCount = 0}).newCurCount

    return (doubleExpRights and doubleExpRights > 0) and doubleExpRights or 0, (doubleMeritsRights and doubleMeritsRights > 0) and doubleMeritsRights or 0
end

function PlayerReturnDailyMatchImpl:UpdateMainPageButton(wtMainPanel)
    local doubleExperience = wtMainPanel:Wnd("PlatformPaddingBox_8", UIWidgetBase)
    local doubleMerits = wtMainPanel:Wnd("DFCanvasPanel_3", UIWidgetBase)

    local activityInfo = self:GetActivityInfo()
    if not activityInfo or Server.ArmedForceServer:GetCurArmedForceMode() == EArmedForceMode.SOL then
        doubleExperience:Collapsed()
        doubleMerits:Collapsed()
    end

    local doubleExpRights, doubleMeritsRights = self:GetMPBuffState()
    if doubleExpRights > 0 then
        doubleExperience:SelfHitTestInvisible()
    else
        doubleExperience:Collapsed()
    end

    if doubleMeritsRights > 0 then
        doubleMerits:SelfHitTestInvisible()
    else
        doubleMerits:Collapsed()
    end
end


PlayerReturnDailyMatchImpl:Register()
return PlayerReturnDailyMatchImpl