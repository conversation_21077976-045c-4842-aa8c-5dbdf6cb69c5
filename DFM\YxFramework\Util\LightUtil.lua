LightUtil = {}

LightUtil.bMiniWorldEnable = false

local ANZSkyBoxActor = import "NZSkyBoxActor"
local UNZSkyBoxComponent = import "NZSkyBoxComponent"
local EControllMode = import "EControllMode"
local UGameplayStatics = import "GameplayStatics"
local UDFMLightCoordinateSubsystem = import "DFMLightCoordinateSubsystem"

function LightUtil.SetMiniWorld(bEnable, lightGroup)
    logframe("LightUtil.SetMiniWorld", bEnable, lightGroup)
    -- LightUtil.SetTodSuspension(bEnable)
    LightUtil.bMiniWorldEnable = bEnable
    if bEnable then
        LightUtil.SetControllMode(EControllMode.EExposureOnly)
        -- GetWorld():SetMiniWorld(true)
        if lightGroup then
            LightUtil.ActiveLightGroupOnly("Nothing")
            LightUtil.ActiveLightGroupOnly(lightGroup)
        end
    else
        LightUtil.SetControllMode(EControllMode.EFullControll)
        -- GetWorld():RestoreWorldScaleFromWorldSetting()
        LightUtil.ActiveLightGroupOnly("Nothing")
    end
end

function LightUtil.ActiveLightGroupOnly(lightGroup)
    UDFMLightCoordinateSubsystem.Get(GetWorld()):ActivateLightGroupOnly(lightGroup)
end

function LightUtil.ActiveLightGroup(lightGroup)
    UDFMLightCoordinateSubsystem.Get(GetWorld()):ActivateLightGroup(lightGroup)
end

function LightUtil.DeactivateLightGroup(lightGroup)
    UDFMLightCoordinateSubsystem.Get(GetWorld()):DeactivateLightGroup(lightGroup)
end

function LightUtil.SetTodSuspension(bSuspended)
    local skyBoxActor = UGameplayStatics.GetActorOfClass(GetWorld(), ANZSkyBoxActor)
	if skyBoxActor then
		skyBoxActor:SetSuspension(bSuspended)
	else
		logwarning("no sky box actor found!")
	end
end

function LightUtil.SetControllMode(mode)
    local skyBoxActor = UGameplayStatics.GetActorOfClass(GetWorld(), ANZSkyBoxActor)
    if skyBoxActor then
        local skyBoxComponent = skyBoxActor:GetComponentByClass(UNZSkyBoxComponent)
        if skyBoxComponent then
            skyBoxComponent.ControllMode = mode
        end
    end
end

local ASafeHouseLightCtrl = import "SafeHouseLightCtrl"
function LightUtil.SetSafeHouseHeightFog(enable)
    local safeHouseLightCtrl = ASafeHouseLightCtrl.Get(GetGameInstance())
    if safeHouseLightCtrl then
        safeHouseLightCtrl["ToggleHeightFog"](safeHouseLightCtrl, enable)
    end
end

--  ambientcubemap(abc) 引擎功能导致，业务保证切换miniworld的时候锁住abc，再回到安全屋的时候放开abc
function LightUtil.SetFixedCameraPos(bFixed)
    logerror("LightUtil.SetFixedCameraPos", bFixed)
    local skyBoxActor = UGameplayStatics.GetActorOfClass(GetWorld(), ANZSkyBoxActor)
    if skyBoxActor then
        local skyBoxComponent = skyBoxActor:GetComponentByClass(UNZSkyBoxComponent)
        if skyBoxComponent then
            skyBoxComponent:SetFixedCameraStatus(bFixed)
        end
    end
end

function LightUtil.GetMaxEVLocalLightScale()
    local skyBoxActor = UGameplayStatics.GetActorOfClass(GetWorld(), ANZSkyBoxActor)
    if skyBoxActor then
        local skyBoxComponent = skyBoxActor:GetComponentByClass(UNZSkyBoxComponent)
        if skyBoxComponent then
            return skyBoxComponent.GiAdjust.MaxEVLocalLightScale
        end
    end
end

function LightUtil.SetMaxEVLocalLightScale(scale)
    local skyBoxActor = UGameplayStatics.GetActorOfClass(GetWorld(), ANZSkyBoxActor)
    if skyBoxActor then
        local skyBoxComponent = skyBoxActor:GetComponentByClass(UNZSkyBoxComponent)
        if skyBoxComponent then
            skyBoxComponent.GiAdjust.MaxEVLocalLightScale = scale
        end
    end
end

function LightUtil.SetSkyLightEnable(bVisible)
    local skyBoxActor = UGameplayStatics.GetActorOfClass(GetWorld(), ANZSkyBoxActor)
    if skyBoxActor then
        local skyBoxComponent = skyBoxActor:GetComponentByClass(UNZSkyBoxComponent)
        if skyBoxComponent then
            skyBoxComponent.bVisible = bVisible
        end
    else
        logwarning("no sky box actor found!")
    end
end