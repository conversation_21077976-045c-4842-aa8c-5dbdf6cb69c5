----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMArmory)
----- LOG FUNCTION AUTO GENERATE END -----------

local InjectionAgentBase = require "DFM.Business.DataStruct.Common.Agent.InjectionAgentBase"
local NavigationAgent = class("NavigationAgent", InjectionAgentBase) ---@class NavigationAgent : InjectionAgentBase

local UGPInputHelper                = import    ("GPInputHelper")
local EGPInputType                  = import    ("EGPInputType")
local WidgetUtil                    = require   ("DFM.YxFramework.Util.WidgetUtil")
local EGPUINavWidgetFocusedAction   = import    ("EGPUINavWidgetFocusedAction")

---------------------------------------------------
--#region InjectionAgentBase interface

function NavigationAgent:Ctor(...)
    self:Init(...)
end

function NavigationAgent:AgentType()
    return "NavigationAgent"
end

function NavigationAgent:DestroyAgent()
    self:RemoveAllGroups(true)
    NavigationAgent.DestroyAgent(self)
end

function NavigationAgent:GetInjectionList()
    return {
        CreateNavigationGroup        = "CreateGroup",
        RemoveNavigationGroup        = "RemoveGroup",
        FocusNavigationGroup         = "FocusGroup",
        SetNavigationInputMapping    = "SetNavigationInputMapping",
        AddNavigationItem            = "AddToGroup",
        GetNavigationGroup           = "Get",
        AddExistingNavigationGroup   = "AddExistingGroup",
        RemoveAllNavigationGroups    = "RemoveAllGroups",
        BuildNavigationGroupTree     = "BuildGroupTree",
    }
end

--#endregion
---------------------------------------------------


---@enum EUINavDynamicType
NavigationAgent.NavigationInputMapping = WidgetUtil.EUINavDynamicType

---@alias CustomNavigationFunc fun(navDirection:EUINavigation):UIWidgetBase

---@class NavGroupConfig
---@field id                  any?
---@field rootWidget          UIWidgetBase?                           一般设置为导航组内所有控件的最近共同父级
---@field strategy            NavigationAgent.NavigationStrategy?     默认 Hittest
---@field members             UIWidgetBase[]?                         导航组成员
---@field scrollRecipient     UIWidgetBase?                           如果该导航组是为一个滚动框创建的（即组内控件都是某滚动框的成员），则该参数应设置为滚动框控件
---@field bSimClick           boolean?                                是否在组内控件获得焦点时向其模拟点击事件
---@field bStack              boolean?                                如果在该组激活时打开过弹窗，是否在弹窗关闭后将焦点返还到该组，默认false
---@field bShowSelector       boolean?                                组内控件获得焦点时是否要显示额外选中高亮控件
---@field wrapDirections      EUINavigation[]?                        导航组循环规则
---@field customBoundaryRule  CustomNavigationFunc?                   自定义导航规则
---@field customBoundaryRuleCaller any                                自定义导航规则函数caller
---@field analogCursorSpeedFactor  number                             虚拟光标移动速度乘数

-- 拥有NavigationAgent的UI实例获得下列方法:
---@class HasNavigationAgent
---@field CreateNavigationGroup         fun(self, config:NavGroupConfig)
---@field RemoveNavigationGroup         fun(self, groupID:any)
---@field FocusNavigationGroup          fun(self, groupID:any)
---@field SetNavigationInputMapping     fun(self, navInputMapping:EUINavDynamicType?)
---@field AddNavigationItem             fun(self, groupID:any, items, ...)
---@field GetNavigationGroup            fun(self, groupID:any):any
---@field AddExistingNavigationGroup    fun(self, id, navGroup)
---@field RemoveAllNavigationGroups     fun(self)
---@field BuildNavigationGroupTree      fun(self, widget)

function NavigationAgent.IsGamepad()
    return WidgetUtil.IsGamepad()
end

function NavigationAgent.IsKeyMouse()
    return not WidgetUtil.IsGamepad()
end

---@enum NavigationAgent.NavigationStrategy
NavigationAgent.NavigationStrategy = {
    Hittest             = "Hittest",
    Closest             = "Closest",
    Default             = "Hittest",
    Grid1D              = "Grid1D",
}
local NavigationStrategy = NavigationAgent.NavigationStrategy

---@param owner             LuaUIBaseView
---@param bDisableInjection boolean?       关闭注入后owner将不会获得 HasNavigationAgent 接口的方法
---@param rootWidget        any            需要传入一个 所有导航组的rootWidget 的共同祖先控件，BuildGroupTree 将从这里开始进行
function NavigationAgent.Create(owner, bDisableInjection, rootWidget)
    -- local obj = setmetatable({}, {__index=NavigationAgent})
    -- ---@cast obj NavigationAgent

    -- obj:Init(owner, bDisableInjection, rootWidget)
    -- return obj
    return NavigationAgent:NewIns(owner, true, rootWidget)
end

function NavigationAgent:Init(owner, bDisableInjection, rootWidget)
    self.owner = owner
    self.navGroups = {}
    self.internalKeys = {}
    self.rootWidget = rootWidget or owner

    -- 注入现在由 InjectionAgentBase 完成
    -- if not bDisableInjection then
    --     self.owner.CreateNavigationGroup        = function(owner,...) return self:CreateGroup(...) end
    --     self.owner.RemoveNavigationGroup        = function(owner,...) return self:RemoveGroup(...) end
    --     self.owner.FocusNavigationGroup         = function(owner,...) return self:FocusGroup(...) end
    --     self.owner.SetNavigationInputMapping    = function(owner,...) return self:SetNavigationInputMapping(...) end
    --     self.owner.AddNavigationItem            = function(owner,...) return self:AddToGroup(...) end
    --     self.owner.GetNavigationGroup           = function(owner,...) return self:Get(...) end
    --     self.owner.AddExistingNavigationGroup   = function(owner, id, navGroup) return self:AddExistingGroup(id, navGroup) end
    --     self.owner.RemoveAllNavigationGroups    = function(owner, ...) return self:RemoveAllGroups(...) end
    --     self.owner.BuildNavigationGroupTree     = function(owner, widget) return self:BuildGroupTree(widget) end
    -- end
end

function NavigationAgent:BuildGroupTree(widget)
    if not IsHD() then return end
    if not widget then widget = self.rootWidget end
    if not widget then return end
    WidgetUtil.BuildGroupTree(widget)
end

---@param groupID any
---@param wrapDirections EUINavigation[]
function NavigationAgent:SetWrapBoundaryRule(groupID, wrapDirections)
    if not IsHD() then return end
    if not self.navGroups[groupID] then return end
    WidgetUtil.WrapNavBoundary(self.navGroups[groupID], wrapDirections)
end

---@param groupID any
---@param fGetNextFocusWidget fun(navDirection:EUINavigation):UIWidgetBase?
---@param caller any?
function NavigationAgent:SetCustomBoundaryRule(groupID, fGetNextFocusWidget, caller)
    if not IsHD() then return end
    if not self.navGroups[groupID] then return end
    WidgetUtil.BindCustomBoundaryNavRule(self.navGroups[groupID], fGetNextFocusWidget, caller)
end

function NavigationAgent:Get(groupID)
    if not IsHD() then return end
    return self.navGroups[groupID]
end

---创建导航组
---@param config NavGroupConfig
function NavigationAgent:CreateGroup(config)
    if not IsHD() then return end
    if self.navGroups[config.id] then return self.navGroups[config.id] end
    return self:AddExistingGroup(config.id, NavigationAgent.StaticCreateGroup(self.owner, config), true)
end

function NavigationAgent:AddExistingGroup(id, navGroup, isInternal)
    if not IsHD() then return end
    assert(self.navGroups[id] == nil)
    self.navGroups[id] = navGroup
    if isInternal then
        self.internalKeys[id] = true
    end
    return navGroup
end

---@param config NavGroupConfig
function NavigationAgent.StaticCreateGroup(owner, config)
    if not IsHD() then return end
    local strategy = config.strategy or NavigationStrategy.Hittest

    local navGroup = WidgetUtil.RegisterNavigationGroup(config.rootWidget, owner, strategy)
    
    if navGroup then
        for _, member in pairs(config.members or {}) do
            navGroup:AddNavWidgetToArray(member)
        end

        if config.scrollRecipient then
            navGroup:SetScrollRecipient(config.scrollRecipient)
        end

        if config.bStack then
            navGroup:MarkIsStackControlGroup()
        end

        
        if config.bSimClick then
            navGroup:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
        else 
            navGroup:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Default)
        end

        if config.wrapDirections then
            WidgetUtil.WrapNavBoundary(navGroup, config.wrapDirections)
        end

        if config.customBoundaryRule then
            WidgetUtil.BindCustomBoundaryNavRule(navGroup, config.customBoundaryRule, config.customBoundaryRuleCaller)
        end

        if config.analogCursorSpeedFactor then
            navGroup:SetAnalogCursorStickySlowdownFactor(config.analogCursorSpeedFactor)
        end

        navGroup:SetNavSelectorWidgetVisibility(config.bShowSelector == true)
    end

    return navGroup
end

---移除导航组
---注意，从 NavigationAgent 只能移除从 NavigationAgent 创建的导航组
---通过 AddExistingGroup 导入的，由外界创建的导航组在这里不会被销毁，谁创建谁销毁
function NavigationAgent:RemoveGroup(id)
    if not IsHD() then return end
    if not self.navGroups[id] then return end

    if self.internalKeys[id] then
        WidgetUtil.RemoveOneNavigationGroup(self.navGroups[id])
    end
    self.navGroups[id] = nil
    self.internalKeys[id] = nil
end

---注意，从 NavigationAgent 只能移除从 NavigationAgent 创建的导航组
---通过 AddExistingGroup 导入的，由外界创建的导航组在这里不会被销毁，谁创建谁销毁
---@param bUnregisterExternal boolean? 同时解除链接的所有由外界创建的导航组（注意不是销毁）
function NavigationAgent:RemoveAllGroups(bUnregisterExternal)
    if not IsHD() then return end
    for k in pairs(self.navGroups) do
        self:RemoveGroup(k)
    end
end

---将焦点移至某个导航组内，选中其默认焦点控件
function NavigationAgent:FocusGroup(id)
    if not IsHD() then return end
    if not self.navGroups[id] then return end

    WidgetUtil.TryFocusDefaultWidgetByGroup(self.navGroups[id])
end

function NavigationAgent:FocusWidget(widget, bUseSelectedEffect)
    if not IsHD() then return end
    return WidgetUtil.SetUserFocusToWidget(widget, bUseSelectedEffect or false)
end

function NavigationAgent:AddToGroup(groupID, items, ...)
    if not IsHD() then return end
    if self.navGroups[groupID] then
        for _, member in pairs({items, ...} or {}) do
            self.navGroups[groupID]:AddNavWidgetToArray(member)
        end
    end
end

---更改导航输入模式（选择手柄上的那些按键用于导航等）
---@param navInputMapping EUINavDynamicType? 留空时返回默认输入模式
function NavigationAgent:SetNavigationInputMapping(navInputMapping)
    if not IsHD() then return end
    if self.hNavInputMapChange then
        WidgetUtil.DisableDynamicNavConfig(self.hNavInputMapChange)
        self.hNavInputMapChange = nil
    end

    if navInputMapping then
        self.hNavInputMapChange = WidgetUtil.EnableDynamicNavConfig(navInputMapping, self.owner)
    end
end

return NavigationAgent