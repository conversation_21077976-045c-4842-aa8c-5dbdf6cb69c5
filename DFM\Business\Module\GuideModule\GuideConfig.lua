----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGuide)
----- LOG FUNCTION AUTO GENERATE END -----------


require "DFM.Business.DataStruct.GuideStruct.GuideDefines"



--------------------------------------------------------------------------
--region UI路径映射配置示例（UILayer表示需要加入的层级）
--------------------------------------------------------------------------
UITable[UIName2ID.GuideMainUI] = {
    UILayer = EUILayer.Mask,
    LuaPath = "DFM.Business.Module.GuideModule.UI.GuideMainUI",
    BPKey = "WBP_GuideSwallowBg",
}

UITable[UIName2ID.GuideDialogUI] = {
    -- UILayer = EUILayer.Mask,
    UILayer = EUILayer.Tip,
    LuaPath = "DFM.Business.Module.GuideModule.UI.GuideDialogUI",
    BPKey = "WBP_GuideDialog",
    Anim = {
        FlowInAni = "Ani_in",
        FlowOutAni = "Ani_out",
    },
    SubUIs = {
        UIName2ID.WBP_GuideDialogContent,
    },
}

UITable[UIName2ID.GuideClickUI] = {
    UILayer = EUILayer.Mask,
    LuaPath = "DFM.Business.Module.GuideModule.UI.GuideClickUI",
    BPKey = "WBP_GuideClick",  -- 2025/6/21 assetName: WBP_Guide_Click
    SubUIs = {
        UIName2ID.WBP_GuideBtnTips,
    },
    Anim = {
        FlowInAni = "Ani_in",
        FlowOutAni = "Ani_out",
    },
}

UITable[UIName2ID.GuideWeakClickBigUI] = {
    UILayer = EUILayer.Mask,
    LuaPath = "DFM.Business.Module.GuideModule.UI.GuideWeakClickBigUI",
    BPKey = "WBP_GuideWeakClickBig",
    SubUIs = {
        UIName2ID.WBP_GuideBtnTips,
    },
}

UITable[UIName2ID.WBP_GuideBtnTips] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GuideModule.UI.GuideClickBtnBig",
    BPKey = "WBP_GuideBtnTips",  -- 2025/6/21 assetName: WBP_Guide_BtnTips2
    -- Anim = {
    --     FlowInAni = "Ani_in",
    --     FlowOutAni = "Ani_out",
    -- },
    Anim = {
        bManuelAnim = true
    },
    SubUIs = {
        UIName2ID.GuideBtnEntryComb,
    },
}

UITable[UIName2ID.GuideBtnEntryComb] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GuideModule.UI.GuideClickBtnEntry",
    BPKey = "WBP_GuideBtnEntry",
    Anim = {
        bManuelAnim = true
    },
}

UITable[UIName2ID.WBP_GuideTextParsing] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GuideModule.UI.GuideTextParsing",
    BPKey = "WBP_GuideTextParsing",
    Anim = {
        FlowInAni = "Anima_AutoIn",
        FlowOutAni = "Anima_AutoOut",
    }
}

UITable[UIName2ID.WBP_GuideDialogContent] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GuideModule.UI.GuideDialogContent",
    BPKey = "WBP_GuideDialogContent",
}

UITable[UIName2ID.GuidePopWindowUI] = {
    UILayer = EUILayer.Mask,
    LuaPath = "DFM.Business.Module.GuideModule.UI.GuidePopWindowUI",
    BPKey = "WBP_GuidePopWindowUI",
    Anim = {
        FlowInAni = "Ani_in",
        FlowOutAni = "Ani_out",
    },
    SubUIs = {
        UIName2ID.CommonVideocomponent,
    }

}

UITable[UIName2ID.GuideExplainUI] = {
    UILayer = EUILayer.Mask,
    LuaPath = "DFM.Business.Module.GuideModule.UI.GuideExplainUI",
    BPKey = "WBP_GuideModeExplain",
    Anim = {
        FlowInAni = "Ani_in",
        FlowOutAni = "Ani_out",
    }
}

UITable[UIName2ID.GuideExplainPageUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GuideModule.UI.GuideExplainPageUI",
    BPKey = "WBP_CommonBookmarkBtn",
}

UITable[UIName2ID.GuideGMUI] = {
    UILayer = EUILayer.Mask,
    LuaPath = "DFM.Business.Module.GuideModule.UI.GuideGMUI",
    BPKey = "WBP_GuideGM",
}

UITable[UIName2ID.GuideCutSceneUI] = {
    UILayer = EUILayer.Mask,
    LuaPath = "DFM.Business.Module.GuideModule.UI.GuideCutSceneUI",
    BPKey = "WBP_GuideCutScene",
    Anim = {
        FlowInAni = "Anima_AutoIn",
        FlowOutAni = "Anima_AutoOut",
    }
}

UITable[UIName2ID.GuideVedio] = {
    UILayer = EUILayer.Mask,
    LuaPath = "DFM.Business.Module.GuideModule.UI.GuideVedio",
    BPKey = "WBP_GuideVedio",
}

UITable[UIName2ID.GuideLittlePopWindowUI] = {
    UILayer = EUILayer.Mask,
    LuaPath = "DFM.Business.Module.GuideModule.UI.GuideLittlePopWindowUI",
    BPKey = "WBP_GuideNounExplain",
    Anim = {
        FlowInAni = "Anima_AutoIn",
        FlowOutAni = "Anima_AutoOut",
    }
}

UITable[UIName2ID.PopWindowContentLootDesc] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GuideModule.UI.PopWindowSubUI.LootDesc",
    BPKey = "WBP_GuidePopUpsItem",
}

-- PC
UITable[UIName2ID.GuideHDPopWindowUI] = {
    UILayer = EUILayer.Mask,
    LuaPath = "DFM.Business.Module.GuideModule.UIHD.GuideHDPopWindowUI",
    BPKey = "WBP_GuideHDPopWindowUI",
    Anim = {
        FlowInAni = "Anima_in",
        FlowOutAni = "Anima_Out",
		--bManuelAnim = true,
	},
}

UITable[UIName2ID.GuideHDPopTipsUI] = {
    UILayer = EUILayer.Mask,
    LuaPath = "DFM.Business.Module.GuideModule.UIHD.GuideHDPopTipsUI",
    BPKey = "WBP_GuideHDPopTipsUI",
    Anim = {
        FlowInAni = "Anima_in",
        FlowOutAni = "Anima_out",
		--bManuelAnim = true,
	},
}

UITable[UIName2ID.GuideHDPopButtonUI] = {
    UILayer = EUILayer.Mask,
    LuaPath = "DFM.Business.Module.GuideModule.UIHD.GuideHDPopButtonUI",
    BPKey = "WBP_GuideHDPopButtonUI",
    Anim = {
        FlowInAni = "Anima_in",
        FlowOutAni = "Anima_out",
		--bManuelAnim = true,
	},
}

UITable[UIName2ID.GuideHDCommonDescUI] = {
    UILayer = EUILayer.Mask,
    LuaPath = "DFM.Business.Module.GuideModule.UIHD.GuideHDCommonDescUI",
    BPKey = "WBP_GuideHDCommonDescUI",
    Anim = {
        FlowInAni = "Anima_in",
        FlowOutAni = "Anima_out",
		--bManuelAnim = true,
	},
}

UITable[UIName2ID.GuideHDClickUI] = {
    UILayer = EUILayer.Mask,
    LuaPath = "DFM.Business.Module.GuideModule.UIHD.GuideHDClickUI",
    BPKey = "WBP_GuideHDClickUI", -- 2025/6/21 assetName: WBP_Guide_Click_PC 
    Anim = {
		bManuelAnim = true
	},
    SubUIs = {
        UIName2ID.GuideHDClickBtnUI,
    },
}

UITable[UIName2ID.GuideHDClickBtnUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GuideModule.UIHD.GuideHDClickBtnUI", 
    BPKey = "WBP_GuideHDClickBtnUI",-- 2025/6/21 assetName: WBP_Guide_Tips_PC
    Anim = {
		bManuelAnim = true
	},
}

UITable[UIName2ID.GuideHDWeakClickUI] = {
    UILayer = EUILayer.Mask,
    LuaPath = "DFM.Business.Module.GuideModule.UIHD.GuideHDWeakClickUI",
    BPKey = "WBP_GuideHDWeakClickUI", -- 2025/8/6 assetName: WBP_Guide_WeakClickBigPC
    Anim = {
		bManuelAnim = true
	},
}

--endregion
-----------------------

local TableNames = {
    GuideConfigForHD = "GuideConfigForHD",
    GuideConfigForMobile = "GuideConfigForMobile",
    TableGuideStageClientAndServerConfig = "GuideStageConfig",
    GuideStageClientOnlyConfig = "GuideStageClientOnlyConfig",
    GuideDialogConfig = "GuideDialogConfig",
    GuideClickConfig = "GuideClickConfig", 
    GuideWidgetConfig = "GuideWidgetConfig",
    GuideVisibilityConfig = "GuideVisibilityConfig",
    GuideItemConfig = "GuideItemConfig",
    GuidePopWindowConfig = "GuidePopWindowConfig",
    GuidePopTipsConfig = "GuidePopTipsConfig",
    GuideExplainConfig = "GuideExplainConfig",
    GuideMovieTypeConfig = "GuideMovieTypeConfig",
    GuideCustomConfig = "GuideCustomConfig",
    GuideVideoConfig = "GuideVedioConfig",
    GuidePopFunctionDescConfig = "GuidePopFunctionDescConfig",
    GuideWBPConfig = "GuideWBPConfig",
    GuideStructuredConfig = "GuideStructuredConfig",
}

-- 800~999 预留，用于撤离失败补偿的引导的拓展
-- 2024/12/3 2000~2999 用于体系化教学
---@enum EGuideStage
local EGuideStage = {
    newPlayerGuideStageIntro = 6,      -- 新手引导的intro关阶段
    newPlayerGuideStageRename = 7,     -- 新手引导的改名阶段
    newPlayerGuideStage1 = 1,          -- 新手引导的第1个阶段 (2024/11/19 进入森林)
    newPlayerGuideStage2 = 2,          -- 新手引导的第2个阶段 (2024/11/21 入局)
    newPlayerGuideStage3 = 3,          -- 新手引导的第3个阶段 (2024/11/21 上架电台)
    -- newPlayerGuideStage5 = 5,   -- 新手引导的第4个阶段
    newPlayerGuideStage4 = 34,


    solFailGiveNewEquip = 4,     -- 非新手关首次撤离失败，送玩家新装备
    --solInfoIntro = 5,   -- 第二次入局SOL拍脸教学
    gotoInsurance = 8,           -- 拾荒商人
    raidInfoIntro = 9,           -- 首次进入Raid的规则教学
    mpInfoIntro = 10,            -- 首次进入MP的规则教学

    -- firstTransToInventory = 12, -- 首次SOL-结算时引导转移物品
    solEscapeMark = 13,        -- 局内撤离点标记引导
    solEscapeTime = 14,        -- 局内撤离时间将至引导
    transToInventory = 15,     -- SOL-非首次引导转移物品
    mpOffdefIntro = 17,        -- mp攻防教学


    playerStateHealth = 18,            -- 提醒玩家治疗生命值
    playerStateArmor = 19,             -- 提醒玩家治疗护甲值

    playerStateBuffLegHurt = 20,       -- 腿部受伤
    playerStateBuffAche = 21,          -- 疼痛状态
    playerStateBuffArmHurt = 22,       -- 手部受伤
    playerStateBuffChestHurt = 23,     -- 胸部受伤
    playerStateOverWeight = 24,        -- 超重

    -- inventoryFull = 27,  -- 仓库满教学 (deprecated)

    auctionInfo = 29,                 -- 拍卖行介绍
    auctionSell = 30,                 -- 拍卖行出售
    lootingGuide = 31,                -- SOL-looting教学 (第二个AI)
    lootingDeadBody = 32,             -- SOL-looting尸体教学 (第一个杂兵)
    shop = 34,                        -- 商人引导
    solFailUseNewEquip = 35,          -- 非新手关首次撤离失败，提示玩家用装备

    playerStateBuffHeadHurt = 36,     -- 头部受伤

    solSettlementTransfer = 37,       -- 新手关-标记收益
    solMarkEarnings = 38,             -- 首次正式对局--结算转移物资
    lootBtnGuide = 39,                -- 引导玩家点击loot按钮打开looting界面


    -- enterHall = 41,     -- 首次进入主大厅

    solScriptMatchMap                 = 42, -- 第二局局内任务引导 (2024/11/19 打开地图标记撤离点)
    -- questInGameWeak = 46, -- 剧本局地图任务引导 (2024/11/19 deprecated)

    mpDesc                            = 47,     -- mp玩法介绍

    sandBox                           = 48,     -- 战略板引导
    armedForceTwoGun                  = 49,     -- 备战卸枪
    armedForceTakeItem                = 50,     -- 备战消耗品
    armedForceAuctionUnlock           = 51,     -- 备战界面 拍卖行已解锁
    armedForceMedicineOpen            = 52,     -- 备战药品箱打开
    armedForceBulletOpen              = 53,     -- 备战弹药箱打开
    firstOpenInventory                = 54,     -- 首次进入仓库
    firstOpenShop                     = 55,     -- 首次进入军需处
    firstOpenQuest                    = 56,     -- 首次进入任务

    solScriptMatchPlayerStateHealthHD = 57,     --  *剧本局* 玩家状态引导-pc版
    solScriptMatchPlayerStateBuffHD   = 58,     -- *剧本局* 玩家状态引导-pc版

    bulletAndArmor                    = 59,     -- 配装攻防 -> she1 子弹穿透引导 (25/1/15)
    auctionLockBuy                    = 60,     -- 拍卖行锁住购买,3级前
    auctionUnlockBuy                  = 61,     -- 拍卖行3级后
    mapHighUnlock                     = 62,     -- 地图高倍场解锁
    mapSecondForrestUnlock            = 63,     -- 第二个sol地图 森林 解锁
    mapSecondBigBaUnlock              = 64,     -- 第二个sol地图 大坝 解锁
    mapSkyCityUnlock                  = 65,     -- 地图航天城解锁

    mpAssembly                        = 66,     -- mp装配
    mpExpUp                           = 67,     -- mp经验提升
    mpWeaponExpUp                     = 68,     -- mp武器经验提升
    mpSelectHero                      = 69,     -- mp选择英雄
    mpArmedForceItem                  = 70,     -- mp兵种道具引导
    mpRank                            = 71,     -- mp积分赛
    mpRankScoreSettlement             = 72,     -- mp积分结算/保护

    solRank                           = 73,     -- 排位指引
    solFirstRankProtection            = 74,     -- 首次触发排位保护
    solBlackSiteUpgrade_WareHouse     = 75,     -- 特勤处升级-仓库
    solHeroFirstSelectShepHerdb       = 76,     -- 干员界面首次选择牧羊人
    -- marketFirstOpenMysticalSkin = 77, -- 首次打开市场玄学皮肤页面
    solBlackSiteProduce_Bullet        = 78,     -- 特勤处制造-子弹
    solRepairEquipment                = 79,     -- 背包中，维修套件教学
    solFirstDying                     = 80,     -- sol首次死亡，且可以被扶起 （第二条命)

    solArmedForceArmorLowDurability   = 81,     -- 特勤处装备耐久度过低
    solStrategySecretOperation        = 82,     -- 中倍场教学

    mandelBrick                       = 83,     -- 曼德拉砖
    mysticalWorkshop                  = 84,     -- 典藏工坊
    mysticalSkin                      = 85,     -- 典藏皮肤

    solSupplyVoucher                  = 86,     -- 物资券

    -- gameplay modes
    solRaidIntro                      = 87,     --Raid竞技场玩法引导
    mpFlagIntro                       = 88,     --MP夺旗战玩法引导

    solPhysicalNegativeState          = 89,     --SOL负面状态优化 *in game*

    -- gameplay modes
    arenaIntro                        = 90,     -- sol竞技场

    solNightFight                     = 91,     -- sol夜战
    mpNightMap                        = 92,     -- mp夜图
    mpPeakLeague                      = 93,     -- 巅峰赛 // aka 指挥官模式

    solTidePrison                     = 94,     -- sol潮汐监狱
    mpIsland                          = 95,     -- mp海岛
    -- mpMapFault                        = 96,     -- mp断层 // 屏蔽 --bug=146405327 [【AtBug】屏蔽断层新手引导配置](https://tapd.woa.com/r/t?id=146405327&type=bug) 


    solFailGiveNewEquipBegin          = 900,
    solFailGiveNewEquip2              = 900, -- 非新手关第2次撤离失败
    solFailGiveNewEquip3              = 901, -- 非新手关第3次撤离失败
    solFailGiveNewEquip4              = 902, -- 非新手关第4次撤离失败
    solFailGiveNewEquip5              = 903, -- 非新手关第5次撤离失败
    solFailGiveNewEquip6              = 904, -- 非新手关第6次撤离失败
    solFailGiveNewEquip7              = 905, -- 非新手关第7次撤离失败
    solFailGiveNewEquip8              = 906, -- 非新手关第8次撤离失败
    solFailGiveNewEquip9              = 907, -- 非新手关第9次撤离失败
    solFailGiveNewEquip10             = 908, -- 非新手关第10次撤离失败
    solFailGiveNewEquipEnd            = 999,

    -- *in game*
    solPOI                            = 100, --SOL POI引导 (搜刮资源点)
    --SOL地图引导(第一把开局) --> 42
    solResourcePoint                  = 101, --SOL POI猎度引导(打开地图查看资源点分级,第二把开局)
    solEscapePath                     = 102, --SOL撤离路线引导

    -- *out game*
    solItemDetailFold                 = 103,     --SOL手游详情页引导 (2024/11/24 mobile only)
    solQuickSort                      = 104,     --SOL(仓库满)一键整理引导
    solWarehouseFull                  = 105,     --SOL仓库满引导
    solDoorKey                        = 106,     --SOL门禁卡引导

    -- *in settlement view*
    solFirstEvacuationSuccess         = 107,     --SOL首次撤离成功引导 结算视频引导 (Mobile)   PC -> 118
    solFirstEvacuationFailedByTimeout = 108,     --SOL首次行动超时引导 结算视频引导 (Mobile)
    solFirstEvacuationFailed          = 109,     --SOL首次撤离失败引导 结算视频引导 (Mobile)
    solNewOperationGoal               = 110,     --SOL行动目标引导 (~~跳出爬坡局~~ ->高级图  sol level >= 6)

    -- turing brick/ mystical hanging
    turingBrickIntro                  = 111,     -- 图灵砖，首次介绍
    mysticalHanging                   = 112,     -- 典藏挂饰，点击某个挂饰之后的特性介绍
    mysticalHangingMarket             = 113,     -- 典藏挂饰-市场
    mysticalWorkShopHanging           = 114,     -- 典藏工坊-挂饰


    solBlackSiteUpgrade_AmorPlatform          = 115,       -- sol 特勤处升级防具台
    solBlackSiteProduce_Amor                  = 116,       -- sol 特勤处制造防具

    solArmedForceArmorLowMaxDurability        = 117,       -- sol 备战防具耐久最大值太低
    solSettlementVideoHD                      = 118,       -- sol 结算视频引导 (PC)
    solSafeBox                                = 119,       -- sol 安全箱引导

    solPlayerEvacuationPointInstruct          = 120,       -- sol 玩家撤离点引导
    solPlayerEvacuationPointInstructPopWindow = 122,       -- sol 玩家撤离点引导(120)的弹窗

    solBlackSiteUpgrade_CollectionRoom        = 123,       -- sol 特勤处升级收藏室
    solBlackSiteGameplay_CollectionRoom       = 124,       -- sol 特勤处玩法收藏室
    solBlackSiteGameplay_CollectionBook       = 125,       -- sol 特勤处玩法收藏室图鉴

    solReflowBeginMatchPulling                = 126,       -- 回流开始比赛牵引
    mpReflowBeginMatchPulling                 = 127,       -- 回流开始比赛牵引

    solSkillAgainst                           = 128,       -- sol 技能对抗引导(点击, PCOnly, mobile 大厅自动触发)
    solSkillAgainstPopWindow                  = 129,       -- sol 技能对抗引导(弹窗)

    mpFailedOrQuit                            = 130,       -- mp 失败或退出引导

    solNewPlayerLevel_FreeSafeBoxLooting      = 131,       -- 新手关-免保搜刮
    solCustomVoucher                          = 132,       -- sol 自定义物资券

    --region 跟踪教学

    -- in-game hud
    tSolLootDeadBodyTips                      = 2001, -- 拾取尸体提示 1/2000001

    -- settlement view
    tSolLootTips                              = 2201, --  物资搜刮提示 1/2000002
    tSolHighYieldProductTips                  = 2202, --  高价值物资提示  1/2000003
    tSolMatchTimeout                          = 2203, --  行动超时教学 1/2000004 (失败)
    tSolPreventBankrupt                       = 2204, --  防破产教学 (损失过高将导致破产，推荐不同打法风格) 1/2000005 (失败)
    tSolPOILowAccessTips                      = 2205, --  POI教学1 (低访问量)  1/2000009
    tSolPOIHighAccessTips                     = 2206, --  POI教学2 (高访问量) 1/2000010 (失败)
    tSolOperationContract                     = 2207, --  行动合同 1/2000014
    tSolTeamMateRescue                        = 2208, --  队友救援 1/2000015

    -- assembly view
    tSolAssemblyModerateInvestment            = 2401, --  带入物资水平教学 (低倍场投入产出) 1/2000006
    tSolSupplyVoucherLowUsage                 = 2402, -- 物资券教学  (物资券使用率低） 1/2000007
    tSolSupplyVoucherLowCash                  = 2403, -- 物资券教学  (上把没出来没钱了) 1/2000008
    tSolBulletLevel                           = 2404, -- 弹药等级教学 1/2000012
    tSolBalanceEquipment                      = 2405, -- 出装平衡教学 1/2000013
    tSolEquipAmorRepairItem                   = 2406, -- 携带维修包 1/2000016


    -- hall view
    tSolBlackSiteDev                  = 2601,     --  特勤处教学 (升级与制造)  1/2000011
    tSolBlackSiteUpdateAbilityDevice  = 2602,     -- 特勤处能力建筑升级 1/2000017
    tSolBlackSiteUpgradeProduceDevice = 2603,     -- 特勤处生产建筑升级 1/2000018
    tSolBlackSiteProduceAmor          = 2604,     -- 特勤处制造防具 1/2000019
    tSolBlackSiteProduceBullet        = 2605,     -- 特勤处制造弹药 1/2000020

    --endregion
}


---@class GuideConfig
local GuideConfig = {
    --------------------------------------------------------------------------
    ---Table配置示例
    --------------------------------------------------------------------------
    TableNamse = TableNames,

    -- 双端配置
    ---@diagnostic disable: assign-type-mismatch

    ---@type FGuideConfig[]
    TableGuideConfig = Facade.TableManager:GetTable(IsHD() and TableNames.GuideConfigForHD or TableNames.GuideConfigForMobile), 

    TableGuideStageClientAndServerConfig = Facade.TableManager:GetTable(TableNames.TableGuideStageClientAndServerConfig), ---@type FGuideStageConfig[]
    TableGuideStageConfig = Facade.TableManager:GetTable(TableNames.GuideStageClientOnlyConfig), ---@type  FGuideStageClientOnlyConfig[]
    TableGuideDialogConfig = Facade.TableManager:GetTable(TableNames.GuideDialogConfig), ---@type FGuideDialogConfig []
    TableGuideClickConfig = Facade.TableManager:GetTable(TableNames.GuideClickConfig), ---@type FGuideClickConfig []
    TableGuideWidgetConfig = Facade.TableManager:GetTable(TableNames.GuideWidgetConfig), ---@type FGuideWidgetConfig []
    TableGuideVisibilityConfig = Facade.TableManager:GetTable(TableNames.GuideVisibilityConfig), ---@type FGuideVisibilityConfig []
    TableGuideItemConfig = Facade.TableManager:GetTable(TableNames.GuideItemConfig), ---@type FGuideItemConfig[]
    TableGuidePopWindowConfig = Facade.TableManager:GetTable(TableNames.GuidePopWindowConfig), ---@type FGuidePopWindowConfig[]
    TableGuidePopTipsConfig = Facade.TableManager:GetTable(TableNames.GuidePopTipsConfig), ---@type FGuidePopTipsConfig[]
    TableGuideExplainConfig = Facade.TableManager:GetTable(TableNames.GuideExplainConfig), ---@type FGuideExplainConfig[]
    TableGuideMovieTypeConfig = Facade.TableManager:GetTable(TableNames.GuideMovieTypeConfig), ---@type FGuideMovieTypeConfig[]
    TableGuideCustomConfig = Facade.TableManager:GetTable(TableNames.GuideCustomConfig), ---@type FGuideMovieTypeConfig[]
    TableGuideVideoConfig = Facade.TableManager:GetTable(TableNames.GuideVideoConfig), ---@type FGuideVedioConfig []
    TableGuidePopFunctionDescConfig = Facade.TableManager:GetTable(TableNames.GuidePopFunctionDescConfig), ---@type FGuidePopFuctionDescConfig[]
    TableGuideWBPConfig = Facade.TableManager:GetTable(TableNames.GuideWBPConfig), ---@type FGuideWBPConfig[]
    TableGuideStructuredConfig = Facade.TableManager:GetTable(TableNames.GuideStructuredConfig), ---@type FGuideStructuredConfig[]
    ---@diagnostic enable: assign-type-mismatch


    -- pc 配置
    --TableGuideConfigForHD = Facade.TableManager:GetTable("GuideConfigForHD"),

    -- 本地环境配置（用于本地开发，防止多人进行配置后冲突）
    --TableGuideConfigForLocalDev = Facade.TableManager:GetTable("GuideConfigForLocalDev"),


    ["EGuideStage"] = EGuideStage, -- 引导阶段枚举


    EGuideExplain = {
        gameModeSolAndRaid = 1, -- 游戏模式介绍
        mpMap = 2, -- mp选图介绍
    },



    -- 需要多语言的文本
    Loc = {
        skipNewPlayerGuide = NSLOCTEXT("GuideModule", "Lua_Guide_skipNewPlayerGuide", 	"是否跳过新手引导"),
        getReward           = NSLOCTEXT("GuideModule", "Lua_Guide_getReward", 	        "首次撤离失败发放"),

        guide3CDialogTitle2 = NSLOCTEXT("GuideModule", "Lua_Guide_guide3CDialogTitle2", 	"前往目标点"),
        guide3CDialogTitle3 = NSLOCTEXT("GuideModule", "Lua_Guide_guide3CDialogTitle3", 	"击中目标"),
        guide3CDialogTitle4 = NSLOCTEXT("GuideModule", "Lua_Guide_guide3CDialogTitle4", 	"搜索资源"),
-- BEGIN VIRTUOS MODIFICATION
        guide3CDialogText1 = NSLOCTEXT("GuideModule", "Console_Lua_Guide_guide3CDialogText1", 	"移动鼠标转动视角"),
-- END VIRTUOS MODIFICATION
        guide3CDialogText2 = NSLOCTEXT("GuideModule", "Lua_Guide_guide3CDialogText2", 	"移动至目标点"),
-- BEGIN VIRTUOS MODIFICATION
        guide3CDialogText3 = NSLOCTEXT("GuideModule", "Console_Lua_Guide_guide3CDialogText3", 	"点击鼠标左键击中所有目标靶子"),
-- END VIRTUOS MODIFICATION
        guide3CDialogText4 = NSLOCTEXT("GuideModule", "Lua_Guide_guide3CDialogText4", 	"前往目标点搜索资源"),
        guideDialogNpcName = NSLOCTEXT("GuideModule", "Lua_Guide_guideDialogNpcName", 	"<customstyle color=\"Color_Highlight02\">GTI：</>%s"),
    
        guidePauseCurGuide = NSLOCTEXT("GuideModule", "Lua_Guide_guidePauseCurGuide", 	"已暂停当前引导，重新触发时，继续引导"),
        guideSkipMainGuide = NSLOCTEXT("GuideModule", "Lua_Guide_guideSkipMainGuide", 	"已跳过主线引导。触发式引导仍会出现"),
        guideSkipAllGuide = NSLOCTEXT("GuideModule", "Lua_Guide_guideSkipAllGuide", 	"已跳过所有引导，包括主线引导和触发式引导"),
    
        cantSellGuideItem = NSLOCTEXT("GuideModule", "Lua_Guide_cantSellGuideItem", 	"请解锁交易行后再出售，获得更高收益"),
        cantMoveGuideItem = NSLOCTEXT("GuideModule", "Lua_Guide_cantMoveGuideItem", 	"教学道具，无法执行该操作"),
        needSelectMode    = NSLOCTEXT("GuideModule", "Lua_Guide_needSelectMode", 	    "请先选中想游玩的模式"),
        unlockTimeDay     = NSLOCTEXT("GuideModule", "Lua_Guide_unlockTimeDay", 	    "{0}天后解锁"),
        unlockTimeHour    = NSLOCTEXT("GuideModule", "Lua_Guide_unlockTimeHour", 	    "{0}小时后解锁"),
        unlockTimeSecond  = NSLOCTEXT("GuideModule", "Lua_Guide_unlockTimeSecond", 	    "{0}分钟{1}秒后解锁"),

        modeDescSOL       = NSLOCTEXT("GuideModule", "Lua_Guide_modeDescSOL", 	        "我们将前往阿萨拉地区,探索未知危险的战局,独自亦或与队友对抗敌人完成任务,获取装备和物资活着撤离。"),
        modeDescMP        = NSLOCTEXT("GuideModule", "Lua_Guide_modeDescMP", 	        "驾驶或配合各种军用载具在广阔战场上冲锋陷阵,占领关键的战略地点,与敌军进行激烈对抗,取得最终的胜利。"),
        onlyCanSelectOneMap = NSLOCTEXT("GuideModule", "Lua_Guide_onlyCanSelectOneMap", "请先选择长弓溪谷地图"),
        
        guideTipsKeyIsNone = NSLOCTEXT("GuideModule", "Lua_Guide_guideTipsKeyIsNone", "{0}（请先通过设置页面设置按钮）"),

        guideTipsUseMedicine1 = NSLOCTEXT("GuideModule", "Lua_Guide_guideTipsUseMedicine1", "使用医疗包治疗伤势"),
        guideTipsUseMedicine2 = NSLOCTEXT("GuideModule", "Lua_Guide_guideTipsUseMedicine2", "使用手术包消除异常状态"),
        guideTipsUseMedicine3 = NSLOCTEXT("GuideModule", "Lua_Guide_guideTipsUseMedicine3", "使用维修包维修护甲"),

        guideTipsEnterPOI = NSLOCTEXT("GuideModule", "Lua_Guide_guideTipsEnterPOI", "已进入资源点，寻找可搜刮物资"),
        guideTipsLootDeadBody = NSLOCTEXT("GuideModule", "Lua_Guide_guideTipsLootDeadBody", "你有敌人尸体可以搜刮"),

        guideSolFailGiveEuipTitle = NSLOCTEXT("GuideModule", "Lua_Guide_guideSolFailGiveEuipTitle", "撤离失败发放"),
        guideSolFailGiveEuipDesc = NSLOCTEXT("GuideModule", "Lua_Guide_guideSolFailGiveEuipDesc", "撤离失败后，身上所有物资都会丢失。特勤处补发了新的基础物资，重返战场吧！"),
        guideSolFailFirstGiveEuipDesc = NSLOCTEXT("GuideModule", "Lua_Guide_guideSolFailFirstGiveEuipDesc", "特勤处发你<customstyle color=\"Color_Highlight02\">一套【基础装备】</>和<customstyle color=\"Color_Highlight02\">【制式套装券】</>，重返战场吧！\n<customstyle color=\"Color_Highlight02\">【制式套装券】</>：一键便捷配装，使用后<customstyle color=\"Color_Highlight02\">赠送</>全套装备<customstyle color=\"Color_Highlight02\">快速入局</>。"),

        guideTriggerFailedByAccountBatch = NSLOCTEXT("GuideModule", "Lua_Guide_guideTriggerFailedByAccountBatch", "(仅dev存在)当前账号批次: {0}, 触发下列引导 [{1}] 失败, 请注意是否需要GM修改账号批次!"),
        structGuideTriggerFailedByNewPlayerGuide = NSLOCTEXT("GuideModule", "Lua_Guide_structGuideTriggerFailedByNewPlayerGuide", "(仅dev存在) 新手引导存在, 触发下列体系教学失败[{0}]")

    },

    -- guide模块注入，用来标记目标widget位置大小
    EGuideProxyWidget = {
        guideProxySandBoxRaid = "guideProxySandBoxRaid",
        guideProxySandBoxSOL = "guideProxySandBoxSOL",
        guideProxySandBoxSOLForrest = "guideProxySandBoxSOLForrest",
        guideProxySandBoxSOLBigBa = "guideProxySandBoxSOLBigBa",
        guideProxySandBoxSOLSpaceCenter = "guideProxySandBoxSOLSpaceCenter",

        guideProxyChooseItemBag = "guideProxyChooseItemBag",
        guideProxyQuestChapter = "guideProxyQuestChapter",
        guideProxyQuestChapter2 = "guideProxyQuestChapter2",
        guideProxyQuestItem = "guideProxyQuestItem",
        guideProxyQuestItem2 = "guideProxyQuestItem2",

        guideProxyQuestJumpBtn = "guideProxyQuestJumpBtn",

        guideProxyWarehouseExpansion = "guideProxyWarehouseExpansion",

        -- 二级页签Tab
        guideProxyTopBarFormat = "guideProxyTopBar_%s",
        guideProxyTopBar_0 = "guideProxyTopBar_0",
        guideProxyTopBar_1 = "guideProxyTopBar_1",

        guideProxyTopBarHDFormat = "guideProxyTopBarHD_%s_%s",
        guideProxyTopBarHD_2_0 = "guideProxyTopBarHD_2_0",
        guideProxyTopBarHD_2_1 = "guideProxyTopBarHD_2_1",
        guideProxyTopBarHD_2_2 = "guideProxyTopBarHD_2_2",

        guideProxyShelveWindowMarket = "guideProxyShelveWindowMarket",
        guideProxyShelveWindowSell = "guideProxyShelveWindowSell",

        guideProxyLootingMostValueItem = "guideProxyLootingMostValueItem",
        guideProxyLootingDrag = "guideProxyLootingDrag",
        guideProxyLootingDoubleClick = "guideProxyLootingDoubleClick",
        guideProxyLootingAI = "guideProxyLootingAI",
        guideProxyLootingLeftWeapon = "guideProxyLootingLeftWeapon",

        guideProxyShopChoose = "guideProxyShopChoose",

        guideProxyAuctionBuyView = "guideProxyAuctionBuyView",
        guideProxyInventoryItem = "guideProxyInventoryItem",
        guideProxyInventoryBindItem = "guideProxyInventoryBindItem",
        guideProxyLootingBindItem = "guideProxyLootingBindItem",

        guideProxyShopItem = "guideProxyShopItem",

        guideProxyBulletCarry = "guideProxyBulletCarry",
        guideProxyBulletMainSlot = "guideProxyBulletMainSlot",
        guideProxyBulletSubSlot = "guideProxyBulletSubSlot",

        guideProxyMedicineItem1 = "guideProxyMedicineItem1",
        guideProxyMedicineItem2 = "guideProxyMedicineItem2",

        guideProxyMpWeaponUpgradeItem = "guideProxyMpWeaponUpgradeItem",

        guideProxyTopBarPrimary0 = "guideProxyTopBarPrimary0",
        guideProxyTopBarPrimary1 = "guideProxyTopBarPrimary1",
        guideProxyTopBarPrimary2 = "guideProxyTopBarPrimary2",
        guideProxyTopBarPrimary3 = "guideProxyTopBarPrimary3",
        guideProxyTopBarPrimary4 = "guideProxyTopBarPrimary4",
        guideProxyTopBarPrimary5 = "guideProxyTopBarPrimary5",
        guideProxyTopBarPrimary6 = "guideProxyTopBarPrimary6",
        guideProxyTopBarPrimary7 = "guideProxyTopBarPrimary7",
        guideProxyTopBarPrimary8 = "guideProxyTopBarPrimary8",
        guideProxyTopBarPrimary9 = "guideProxyTopBarPrimary9",
        guideProxyTopBarPrimary10 = "guideProxyTopBarPrimary10",

        guideProxyBottomBarPrimary0 = "guideProxyBottomBarPrimary0",
        guideProxyBottomBarPrimary1 = "guideProxyBottomBarPrimary1",
        guideProxyBottomBarPrimary2 = "guideProxyBottomBarPrimary2",
        guideProxyBottomBarPrimary3 = "guideProxyBottomBarPrimary3",
        guideProxyBottomBarPrimary4 = "guideProxyBottomBarPrimary4",
        guideProxyBottomBarPrimary5 = "guideProxyBottomBarPrimary5",
        guideProxyBottomBarPrimary6 = "guideProxyBottomBarPrimary6",
        guideProxyBottomBarPrimary7 = "guideProxyBottomBarPrimary7",
        guideProxyBottomBarPrimary8 = "guideProxyBottomBarPrimary8",
        guideProxyBottomBarPrimary9 = "guideProxyBottomBarPrimary9",

        guideProxyBottomBarHDInputSummaryForamt = "guideProxyBottomBarHDInputSummary_%s",
        guideProxyBottomBarHDInputSummary0="guideProxyBottomBarHDInputSummary0",
        guideProxyBottomBarHDInputSummary1="guideProxyBottomBarHDInputSummary1",
        guideProxyBottomBarHDInputSummary2="guideProxyBottomBarHDInputSummary2",
        guideProxyBottomBarHDInputSummary3="guideProxyBottomBarHDInputSummary3",
        guideProxyBottomBarHDInputSummary4="guideProxyBottomBarHDInputSummary4",
        guideProxyBottomBarHDInputSummary5="guideProxyBottomBarHDInputSummary5",
        guideProxyBottomBarHDInputSummary6="guideProxyBottomBarHDInputSummary6",
        guideProxyBottomBarHDInputSummary_ViewSkills="guideProxyBottomBarHDInputSummary_ViewSkills",
        guideProxyBottomBarHDInputSummary_SkillShow="guideProxyBottomBarHDInputSummary_SkillShow",
        guideProxyBottomBarHDInputSummary_ClearBags = "guideProxyBottomBarHDInputSummary_ClearBags",

        guideProxyStrategicActionFileHighValueBtn = "guideProxyStrategicActionFileHighValueBtn",
        guideProxyStrategicActionFileConfirmBtn = "guideProxyStrategicActionFileConfirmBtn",
        guideProxyStrategicActionFileDownloadBtn = "guideProxyStrategicActionFileDownloadBtn",

        guideProxyItemDetailContentBtn_Sell="guideProxyItemDetailContentBtn_Sell",

        guideProxyBlackSiteEnterDeviceFormat = "guideProxyBlackSiteEnterDevice_%s",

        guideProxyNewPlayerLevelSafeBoxLootItem = "guideProxyNewPlayerLevelSafeBoxLootItem", -- 新手关-免保搜刮物品
    },

    -- 新手引导过程中指定的item id, 对应表格 GuideItemConfig
    NewPlayerGuideSpecItem = {
        bagSOL = 1,
        questChapter = 2,
        questItem    = 3,
        lootingItemList = 4,
        merchantId = 5,
        invItem = 6,   -- 背包引导3个道具
        shopItem = 7,   -- 商人引导购买的道具
        lootBtnGuideMinTimes = 8, -- 正式对局启动玩家loot按钮引导的最小次数
        questObjectIdInGame = 9, -- 正式对局启动玩家loot按钮引导的最小次数
        newPlayerExtendItemId = 10, -- 新手扩容箱id
        newPlayerSellItemId = 11, -- 新手要出售的id
        questItem2 = 12, -- 剧本局接受的任务
        questChapter2 = 13, -- 第二章任务
        modeSelectDefault = 14, -- 模式选择默认选中项
        canSelectMap = 15, -- 新手引导中可以选择的地图
        sellItemMobile = 16, -- 手游仓库出售的id
        newPlayerLevelSafeBoxLootItem = 17, -- 新手关-免保搜刮的物品id(2025/7/28: 对应免保)
    },

    -- 新手引导条件类型
    -- E(Start)Condion
    ECondition = {
        MinFinishGame = 101,        -- 最小完成对局数量，不足则不进行引导
        MaxFinishGame = 102,        -- 最大完成对局数量，超过后不进行引导
        PreGuideStage = 103,        -- 前置引导
        MaxGuideTimes = 104,        -- 最多引导次数
        FinishNewPlayerGuide = 105, -- 完成新手引导后才开启
        CurNotHasMapMarkExit = 106, -- 当前不存在地图标记撤离
        InCombatState = 107,        -- 当前战斗状态，0为非战斗，1为处于战斗状态
        LastSolGameIsFail = 108,    -- 上一局sol对局结果是失败的
        MinLevel = 109,             -- 最低赛季等级
        MaxLevel = 110,             -- 最高赛季等级
        NotInMathing = 111,         -- 当前不在匹配中
        PlayCD = 112,               -- 播放CD
    },

    ---@enum GuideConfig.EPlayCDType
    EPlayCDType = {
        BySec = 1,    -- 等待x秒后
        ByDay = 2,    -- 等待x天后
        ByMatch = 3,  -- 等待x局后
        EachMath = 4, -- 每局最大x次每次最小y秒gap
        EachDay = 5,  --  每天最大x次每次最小y秒gap (2025/7/13 暂无相应需求，只有技能教学用到,未实装)
    },

    -- 打断单步引导的条件
    ---@enum GuideConfig.EStopStepCondition
    EStopStepCondition = {
        InCombat = 1,   -- 进入战斗状态
    },

    -- 重启引导的原因
    ERestartGuideReason = {
        NetReconnect = 1,   -- 断线重连
    },

    ---@enum GuideConfig.EGuideData
    EGuideData = {
        Dialog = "Dialog",

        Server = "Server",
        Custom = "Custom",
        Delay = "Delay",
        Event = "Event",

        Click = "Click",
        WeakClick = "WeakClick",

        HDClick = "HDClick",
        HDWeakClick = "HDWeakClick",

        HDPopDescAndClick = "HDPopDescAndClick",
        HDPopDescAndWeakClick = "HDPopDescAndWeakClick",

        PopWindow = "PopWindow",
        HDPopWindow = "HDPopWindow",

        HDPopTips = "HDPopTips", -- 侧边小图文弹窗
    },

    EMaskTexturePath = {
        [1] = "/Game/UI/UIAtlas/System/Guide/Mat/T_Guide_Mask_02.T_Guide_Mask_02",
        [2] = "/Game/UI/UIAtlas/System/Guide/Mat/T_Guide_Mask_01.T_Guide_Mask_01",
        [3] = "/Game/UI/UIAtlas/System/Guide/Mat/T_Guide_Mask_03.T_Guide_Mask_03",
        [4] = "/Game/UI/UIAtlas/System/Guide/Mat/T_Guide_Mask_04.T_Guide_Mask_04",
    },

    DefaultMaskTexturePath = "/Game/UI/UIAtlas/System/Guide/Mat/T_Guide_Mask_01.T_Guide_Mask_01",
    DefaultMaskBgPath = "/Game/UI/UIAtlas/System/Guide/BakedSprite/Guide_WhiteBlock.Guide_WhiteBlock",


    Guide3CStage = {
        Rotate = 1,
        Move = 2,
        Shoot = 3,
        Loot = 4
    },

    -- 药品轮盘中可能推荐的加血药品的id
    RouletteAddHpMedList = {
        14020000001,
        14020000002,
        14020000003,
        14020000004,
        14020000005,
        14020000006,
    },

    -- 药品轮盘中可能推荐的加防药品的id
    RouletteAddDEFMedList = {
        14060000001,
        14060000002,
    },
    -- Currently all Shield/Helmet repair item ids
    AmorRepairItemIds={
        14060000001,
        14060000002,
        14060000003,
        14060000004,
        14060000005,
        14060000006,
        14060000007,
        14060000008,
    },

    OnlyAmorRepairItemIds={
        14060000002,
        14060000004,
        14060000006,
        14060000008,
    },

    OnlyHelmetAmorRepairItemIds={
        14060000001,
        14060000003,
        14060000005,
        14060000007,
    },

    -- 药品轮盘中可能推荐的腿部受伤加血药品的id
    RouletteLegAddHpMedList = {
        14030000001,
        14030000002,
        14030000003,
        14050000001,
        14050000002,
        14050000003,
    },
    -- 药品轮盘中可能推荐的手部受伤加血药品的id
    RouletteHandAddHpMedList = {
        14030000001,
        14030000002,
        14030000003,
        14050000001,
        14050000002,
        14050000003,
    },
    -- 药品轮盘中可能推荐的头部受伤加血药品的id
    RouletteHeadAddHpMedList = {
        14030000001,
        14030000002,
        14030000003,
        14050000001,
        14050000002,
        14050000003,
    },
    -- 药品轮盘中可能推荐的胸部受伤加血药品的id
    RouletteChestAddHpMedList = {
        14030000001,
        14030000002,
        14030000003,
        14050000001,
        14050000002,
        14050000003,
    },


    MedOperationItems = {
        14030000001,
        14030000002,
        14030000003,
        -- 暂时不要止痛药
        -- 14050000001,
        -- 14050000002,
        -- 14050000003,
    },

    -- 携带药品的回血
    TakeItemAddHealth = 14020000002,
    -- 携带药品的救治
    TakeItemAddRecover = 14030000001,
}


GuideConfig.EGuideEvent = {
    evtNewPlayerGuideBegin      = LuaEvent:NewIns("evtNewPlayerGuideBegin"),
    evtNewPlayerGuideEnd        = LuaEvent:NewIns("evtNewPlayerGuideEnd"),
    evtGuideDialogEnd           = LuaEvent:NewIns("evtGuideDialogEnd"),
    evtGuideSwallowBgClicked    = LuaEvent:NewIns("evtGuideSwallowBgClicked"),

    evtGuideClick                                        = LuaEvent:NewIns("Guide.evtGuideClick"),
    evtOnGuideMsg                                        = LuaEvent:NewIns("Guide.evtOnGuideMsg"),
    evtGuideNavChanged                                   = LuaEvent:NewIns("Guide.evtGuideNavChanged"),

    -- events for guide data listen
    gdEvtOnGuideClicked                                  = LuaEvent:NewIns("Guide.gdEvtOnGuideClicked"),
    gdEvtOnGuideMsg                                      = LuaEvent:NewIns("Guide.gdEvtOnGuideMsg"),
    gdEvtDummy                                           = LuaEvent:NewIns("Guide.gdEvtDummy"), -- Event的占位符

    evtInventoryOpen            = LuaEvent:NewIns("evtInventoryOpen"),
    evtNewPlayerGuidePrepare    = LuaEvent:NewIns("evtNewPlayerGuidePrepare"),
    evtSandBoxOpenBegin        = LuaEvent:NewIns("evtSandBoxOpenBegin"),
    evtSandBoxOpenFinish        = LuaEvent:NewIns("evtSandBoxOpenFinish"),
    evtSandBoxHideBegin        = LuaEvent:NewIns("evtSandBoxHideBegin"),
    evtSandBoxStrategicSelectionSOLActionFileOnShowBegin = LuaEvent:NewIns("evtSandBoxStrategicSelectionSOLActionFileOnShowBegin"),
    evtSandBoxStrategicSelectionSOLActionFileOnHideBegin = LuaEvent:NewIns("evtSandBoxStrategicSelectionSOLActionFileOnHideBegin"),
    evtStrategicSelectionSOLChange2LowYield = LuaEvent:NewIns("evtStrategicSelectionSOLChange2LowYield"),
    evtStrategicSelectionSOLChange2HighYield = LuaEvent:NewIns("evtStrategicSelectionSOLChange2HighYield"),
    evtStrategicSelectionSOLChange2ExtremeHighYield = LuaEvent:NewIns("evtStrategicSelectionSOLChange2ExtremeHighYield"),
    evtStrategicSelectionMapDownloadBtnClicked = LuaEvent:NewIns("evtStrategicSelectionMapDownloadBtnClicked"),

    evtGuideModification  = LuaEvent:NewIns("evtGuideModification"), -- 引导修改事件

    evtArmedForceOpenFinish     = LuaEvent:NewIns("evtArmedForceOpenFinish"),
    evtSelectionEquipOpenFinish = LuaEvent:NewIns("evtSelectionEquipOpenFinish"),
    evtSafeHouseHUDOpenFinish   = LuaEvent:NewIns("evtSafeHouseHUDOpenFinish"),
    evtSafeHouseHUDOpenStart   = LuaEvent:NewIns("Guide.evtSafeHouseHUDOpenStart"),
    evtGuideToSolGame           = LuaEvent:NewIns("evtGuideToSolGame"),
    evtGuideNewPlayerStageUpdate = LuaEvent:NewIns("evtGuideNewPlayerStageUpdate"),
    evtGuideUIOpenFinish        = LuaEvent:NewIns("evtGuideUIOpenFinish"),
    evtGuideQuestChapterLoadFinish    = LuaEvent:NewIns("evtGuideQuestChapterLoadFinish"),  -- 打开任务界面
    evtGuideQuestItemLoadFinish    = LuaEvent:NewIns("evtGuideQuestItemLoadFinish"),
    evtGuideQuestStateChange     = LuaEvent:NewIns("evtGuideQuestStateChange"),
    evtGuideSafeHouseUpgrade    = LuaEvent:NewIns("evtGuideSafeHouseUpgrade"),
    evtGuideShowInsuranceTips    = LuaEvent:NewIns("evtGuideShowInsuranceTips"),    -- 撤离失败，弹出弹窗
    evtCancelGotoInsurance    = LuaEvent:NewIns("evtCancelGotoInsurance"),    -- 撤离失败弹窗，点击取消
    evtGuideClickInGame    = LuaEvent:NewIns("evtGuideClickInGame"),    -- 局内引用局外点击界面时，点击按钮触发的事件
    evtGuideStart             = LuaEvent:NewIns("Guide.evtGuideStart"),
    evtGuideEnd             = LuaEvent:NewIns("evtGuideEnd"),
    evtPostOnGuideEnd             = LuaEvent:NewIns("evtPostOnGuideEnd"),
    evtGuideWarehouseExpansionOpenFinish             = LuaEvent:NewIns("evtGuideWarehouseExpansionOpenFinish"),

    evtOnCancelReconnectMatchBtnClicked = LuaEvent:NewIns("Guide.evtOnCancelReconnectMatchBtnClicked"),
    evtOnCancelReconnectMatchRes = LuaEvent:NewIns("Guide.evtOnCancelReconnectMatchRes"),
    ------ 弱点击引导按钮点击事件，用于驱动引导前进
    evtGuideTransClick             = LuaEvent:NewIns("evtGuideTransClick"),
    evtGuideContinueClick          = LuaEvent:NewIns("evtGuideContinueClick"),
    evtGuideMiniMapOpen            = LuaEvent:NewIns("evtGuideMiniMapOpen"),
    evtGuideMiniMapClose           = LuaEvent:NewIns("evtGuideMiniMapClose"),
    evtGuideItemDetailOpen         = LuaEvent:NewIns("evtGuideItemDetailOpen"),
    evtGuideEquipFinish            = LuaEvent:NewIns("evtGuideEquipFinish"),

    ------ 其他事件，驱动引导前进
    evtOpenLootingBagUI           = LuaEvent:NewIns("evtOpenLootingBagUI"),  -- 打开looting界面
    evtOpenBagWithoutLooting        = LuaEvent:NewIns("evtOpenBagWithoutLooting"),  -- 打开looting界面, 但是没有looting对象，即打开背包
    evtCloseLootingBagUI          = LuaEvent:NewIns("evtCloseLootingBagUI"),  -- 关闭looting界面
    evtOpenInventoryReward      = LuaEvent:NewIns("evtOpenInventoryReward"),  -- 奖励弹窗打开
    evtCloseInventoryReward       = LuaEvent:NewIns("evtCloseInventoryReward"),  -- 奖励弹窗关闭
    evtSyncMainOpen               = LuaEvent:NewIns("evtSyncMainOpen"),  -- sycn主界面打开
    evtSyncMainOpenStart          = LuaEvent:NewIns("Guide.evtSyncMainOpenStart"),  -- sycn主界面开始打开
    evtSafeHouseUpdateUIOpen      = LuaEvent:NewIns("evtSafeHouseUpdateUIOpen"),  -- 安全屋升级界面打开
    evtNoStackUI                  = LuaEvent:NewIns("evtNoStackUI"),  -- 最后一个栈ui也被关闭掉
    evtGuideWareHouseMainOpenFinish    = LuaEvent:NewIns("evtGuideWareHouseMainOpenFinish"),  -- 仓库打开
    evtWareHouseMainHideBegin = LuaEvent:NewIns("evtWareHouseMainHideBegin"),  -- 仓库关闭
    evtAuctionOpen               = LuaEvent:NewIns("evtAuctionOpen"),  -- 拍卖行界面打开
    evtGuideItemDoubleClick    = LuaEvent:NewIns("evtGuideItemDoubleClick"),  -- 双击looting道具
    evtGuideItemDragStart      = LuaEvent:NewIns("evtGuideItemDragStart"),  -- 拖动looting道具
    evtGuideItemDragCancelled      = LuaEvent:NewIns("Guide.evtGuideItemDragCancelled"),  -- 拖动looting道具

    evtGuideSlotViewClicked      = LuaEvent:NewIns("evtGuideSlotViewClicked"),  -- 兵种配装背包槽位被点击
    evtGuideSyncWarehouseBtnClicked      = LuaEvent:NewIns("evtGuideSyncWarehouseBtnClicked"),  -- Sync系统仓库按钮被点击
    evtGuideSyncQuestBtnClicked      = LuaEvent:NewIns("evtGuideSyncQuestBtnClicked"),  -- Sync系统任务按钮被点击
    evtGuideQuestChapterBtnClicked      = LuaEvent:NewIns("evtGuideQuestChapterBtnClicked"),  -- 引导配置的任务章节npc被点击
    evtGuideQuestDetailBtnClicked      = LuaEvent:NewIns("evtGuideQuestDetailBtnClicked"),  -- 引导配置的打开任务详情的按钮被点击
    evtGuideQuestDetailLoadFinish      = LuaEvent:NewIns("evtGuideQuestDetailLoadFinish"),  -- 任务详情界面打开
    evtGuideArmedForceSureBtnClicked      = LuaEvent:NewIns("evtGuideArmedForceSureBtnClicked"),  -- 兵种配装确认按钮被点击
    evtGuideArmedForceWarehouseBtnClicked      = LuaEvent:NewIns("evtGuideArmedForceWarehouseBtnClicked"),  -- 兵种配装仓库按钮被点击
    evtGuideWareHouseTransferEnd      = LuaEvent:NewIns("evtGuideWareHouseTransferEnd"),  -- 道具转移完成
    evtGuideSyncShopBtnClicked      = LuaEvent:NewIns("evtGuideSyncShopBtnClicked"),  -- Sync系统商人按钮被点击
    evtGuideShopChooseOpenFinish      = LuaEvent:NewIns("evtGuideShopChooseOpenFinish"),  -- 打开商人选择界面
    evtGuideShopSpecMerchanBtnClicked      = LuaEvent:NewIns("evtGuideShopSpecMerchanBtnClicked"),  -- 引导商人被点击
    evtGuideShopMainOpenFinish      = LuaEvent:NewIns("evtGuideShopMainOpenFinish"),  -- 商人主界面打开
    evtGuideGoSolBtnClicked      = LuaEvent:NewIns("evtGuideGoSolBtnClicked"),  -- 出发按钮点击
    evtGuideLeaveSafeHouse      = LuaEvent:NewIns("evtGuideLeaveSafeHouse"),  -- 离开3d安全屋
    evtGuideShopItemSelect      = LuaEvent:NewIns("evtGuideShopItemSelect"),  -- 选中商品
    evtItemDetailSliderBtnClicked = LuaEvent:NewIns("evtItemDetailSliderBtnClicked"),  -- 点击详情页确认按钮
    evtItemDetailPanelIsFoldChange = LuaEvent:NewIns("evtItemDetailPanelIsFoldChange"), -- 详情页折叠状态改变

    evtLootBtnHide        = LuaEvent:NewIns("evtLootBtnHide"),  -- loot按钮消失
    evtStepAutoTimeEnd        = LuaEvent:NewIns("evtStepAutoTimeEnd"),  -- 单步倒计时结束

    evtUseMedOperationSetItems = LuaEvent:NewIns("evtUseMedOperationSetItems"),  -- 使用手术包道具
    evtUseItemAddArmor        = LuaEvent:NewIns("evtUseItemAddArmor"),  -- 使用增加护甲的道具
    evtUseItemAddHp        = LuaEvent:NewIns("evtUseItemAddHp"),  -- 使用增加血量的道具
    evtUseItemCureHurtLeg        = LuaEvent:NewIns("evtUseItemCureHurtLeg"),  -- 使用治疗腿部受伤的道具
    evtUseItemCureHurtArm        = LuaEvent:NewIns("evtUseItemCureHurtArm"),  -- 使用治疗手部受伤的道具
    evtUseItemCureHurtHead        = LuaEvent:NewIns("evtUseItemCureHurtHead"),  -- 使用治疗头部受伤的道具
    evtUseItemCureHurtChest        = LuaEvent:NewIns("evtUseItemCureHurtChest"),  -- 使用治疗胸部受伤的道具
    evtOnSandboxItemClicked = LuaEvent:NewIns("Guide.evtOnSandboxItemClicked"), -- 战略板选择地图
    evtOnEvacuateWinViewOpenFinish = LuaEvent:NewIns("Guide.evtOnEvacuateWinViewOpenFinish"), -- 撤离成功结算界面打开
    evtOnArmedForceProcessBtnClicked = LuaEvent:NewIns("Guide.evtOnArmedForceProcessBtnClicked"), -- 配装确定选择按钮
    evtOnSystemSettingMainUIOpen = LuaEvent:NewIns("Guide.evtOnSystemSettingMainUIOpen"), -- 设置界面打开
    evtOnSystemSettingMainUIClose = LuaEvent:NewIns("Guide.evtOnSystemSettingMainUIClose"), -- 设置界面关闭
    evtLootSearchDone = LuaEvent:NewIns("Guide.evtLootSearchDone"), -- loot扫描结束
    evtShopItemRefresh = LuaEvent:NewIns("Guide.evtShopItemRefresh"), -- 商品列表刷新
    evtArmedForceMainShow = LuaEvent:NewIns("Guide.evtArmedForceMainShow"), -- 备战界面打开
    evtArmedForceMainHide = LuaEvent:NewIns("Guide.evtArmedForceMainHide"), -- 备战界面关闭
    evtQuestAcceptSuccess = LuaEvent:NewIns("Guide.evtQuestAcceptSuccess"), -- 任务接取成功

    evtAssemblyMainPanelRentalBtnClicked = LuaEvent:NewIns("Guide.evtAssemblyMainPanelRentalBtnClicked"),   -- 租借按钮被点击
    evtAssemblyRentalMainPanelOnShowBegin = LuaEvent:NewIns("Guide.evtAssemblyRentalMainPanleOnShowBegin"),   -- 租借界面打开
    evtAssemblyRentalMainPanelOnHideBegin = LuaEvent:NewIns("Guide.evtAssemblyRentalMainPanleOnHideBegin"),   -- 租借界面关闭
    evtAssemblySquadPickOpenFinish = LuaEvent:NewIns("Guide.evtAssemblySquadPickOpenFinish"),   -- 打开选角界面
    evtAssemblySquadPickClose = LuaEvent:NewIns("Guide.evtAssemblySquadPickClose"),             -- 关闭选角界面
    evtAssemblySquadPickHeroClicked = LuaEvent:NewIns("Guide.evtAssemblySquadPickHeroClicked"),             -- 选择英雄界面
    evtAssemblySquadPickConfirmClicked = LuaEvent:NewIns("Guide.evtAssemblySquadPickConfirmClicked"),   -- 点击确认选角按钮
    evtConfrimTarget = LuaEvent:NewIns("Guide.evtConfrimTarget"),   -- 确认选择地图
    evtAssemblyMedicineState = LuaEvent:NewIns("Guide.evtAssemblyMedicineState"),   -- 备战切换到带药界面
    evtAssemblyBulletState = LuaEvent:NewIns("Guide.evtAssemblyBulletState"),   -- 备战切换到子弹界面
    evtArmedForceQuickOperationOpenFinish = LuaEvent:NewIns("Guide.evtArmedForceQuickOperationOpenFinish"), -- 打开带药界面
    evtArmedForceQuickOperationTryOpen = LuaEvent:NewIns("Guide.evtArmedForceQuickOperationTryOpen"), -- 打开带药界面
    evtArmedForceQuickOperationConfirmClicked = LuaEvent:NewIns("Guide.evtArmedForceQuickOperationConfirmClicked"), -- 确认带药
    evtAutoDoPlaceItemFinished = LuaEvent:NewIns("Guide.evtAutoDoPlaceItemFinished"), -- 自动带药成功
    evtEvacuateTrophyViewLoadFinish = LuaEvent:NewIns("Guide.evtEvacuateTrophyViewLoadFinish"), -- 转移物资界面打开完毕
    evtContinueClicked = LuaEvent:NewIns("Guide.evtContinueClicked"), -- 转移物资界面下一步点击
    evtEvacuateExpViewOpen = LuaEvent:NewIns("Guide.evtEvacuateExpViewOpen"), -- 经验结算界面打开
    evtItemDetailPanelTryOpen = LuaEvent:NewIns("Guide.evtItemDetailPanelTryOpen"), -- 打开详情页
    evtItemDetailBtnCreateFinish = LuaEvent:NewIns("Guide.evtItemDetailBtnCreateFinish"), -- 详情页按钮创建完毕
    evtItemDetailSellClicked = LuaEvent:NewIns("Guide.evtItemDetailSellClicked"),   -- 详情页出售按钮点击
    evtCommonWindowOpenFinish = LuaEvent:NewIns("Guide.evtCommonWindowOpenFinish"), -- 通用弹窗打开
    evtItemDetailSellResult = LuaEvent:NewIns("Guide.evtItemDetailSellResult"), -- 出售道具结果返回
    evtStackUIClose = LuaEvent:NewIns("Guide.evtStackUIClose"), -- 栈ui关闭
    evtStackUIOpen = LuaEvent:NewIns("Guide.evtStackUIOpen"), -- 栈ui打开
    evtQuestObjectionFinished = LuaEvent:NewIns("Guide.evtQuestObjectionFinished"), -- 局内任务目标完成
    evtOnTaskSelected = LuaEvent:NewIns("Guide.evtOnTaskSelected"), -- 局内任务按钮点击
    evtOnBigMapItemLoadFinished = LuaEvent:NewIns("Guide.evtOnBigMapItemLoadFinished"), -- 大地图icon加载完毕
    evtOnIntroFinished = LuaEvent:NewIns("Guide.evtOnIntroFinished"), -- Intro结束
    evtModeHallOpenAnimFinish = LuaEvent:NewIns("Guide.evtModeHallOpenAnimFinish"), -- 主大厅打开动画完毕
    evtModeHallClose = LuaEvent:NewIns("Guide.evtModeHallClose"), -- 主大厅关闭
    evtQuestGetReward = LuaEvent:NewIns("Guide.evtQuestGetReward"), -- 领取任务奖励
    evtRenameFinished = LuaEvent:NewIns("Guide.evtRenameFinished"), -- 改名完成
    evtSelectModeUIOpen = LuaEvent:NewIns("Guide.evtSelectModeUIOpen"), -- 打开ms22模式选择弹窗
    evtSelectModeUIClose = LuaEvent:NewIns("Guide.evtSelectModeUIClose"), -- 关闭ms22模式选择弹窗
    evtUnlockUIOpen = LuaEvent:NewIns("Guide.evtUnlockUIOpen"), -- 解锁系统界面打开
    evtUnlockUIClose = LuaEvent:NewIns("Guide.evtUnlockUIClose"), -- 解锁系统界面关闭
    evtOnRegisterFinish = LuaEvent:NewIns("Guide.evtOnRegisterFinish"), -- 改名所有流程结束
    evtMpMainPanelShowAnimFinish = LuaEvent:NewIns("Guide.evtMpMainPanelShowAnimFinish"), -- mp主界面打开动画播放完毕
    evtMpSelectModeUIOpen = LuaEvent:NewIns("Guide.evtMpSelectModeUIOpen"), -- mp选模式界面打开
    evtMpSelectModeUIShowAnimFinish = LuaEvent:NewIns("Guide.evtMpSelectModeUIShowAnimFinish"), -- mp选模式界面打开完毕
    evtOnMpVedioPlayStart = LuaEvent:NewIns("Guide.evtOnMpVedioPlayStart"), -- mp介绍视频播放开始
    evtOnMpVedioPlayEnd = LuaEvent:NewIns("Guide.evtOnMpVedioPlayEnd"), -- mp介绍视频播放完毕
    evtOnMpConfirmModeBtnClick = LuaEvent:NewIns("Guide.evtOnMpConfirmModeBtnClick"), -- mp确认选图
    evtModeHallChangeMode = LuaEvent:NewIns("Guide.evtModeHallChangeMode"), -- 模式大厅切换模式
    evtDetailInAniFinished = LuaEvent:NewIns("Guide.evtDetailInAniFinished"),   -- 选图详情页打开完毕
    evtSOLConfirmBtnClicked = LuaEvent:NewIns("Guide.evtSOLConfirmBtnClicked"), -- 确认选图
    evtSelectSol = LuaEvent:NewIns("Guide.evtSelectSol"), -- 模式弹窗选择sol
    evtSelectMp = LuaEvent:NewIns("Guide.evtSelectMp"), -- 模式弹窗选择mp
    evtOnRegisterGetTaskBtnClick = LuaEvent:NewIns("Guide.evtOnRegisterGetTaskBtnClick"),   -- 起名界面点击接受任务
    evtLootingItemMove = LuaEvent:NewIns("Guide.evtLootingItemMove"),   -- 局内道具位置变化
    evtLootingItemDiscard = LuaEvent:NewIns("Guide.evtLootingItemDiscard"), -- 局内道具丢弃

    evtOnSeamlessSequencePreSpawn = LuaEvent:NewIns("Guide.evtOnSeamlessSequencePreSpawn"),
    evtOnBatchSellWindowShow = LuaEvent:NewIns("Guide.evtOnBatchSellWindowShow"),
    evtTrySale = LuaEvent:NewIns("Guide.evtTrySale"),
    evtOnWarehouseEquipSlotViewOnDrop = LuaEvent:NewIns("Guide.evtOnWarehouseEquipSlotViewOnDrop"),

    evtBackBtnClicked = LuaEvent:NewIns("Guide.evtBackBtnClicked"),
    evtOnMpPresetPanelShow = LuaEvent:NewIns("Guide.evtOnMpPresetPanelShow"),
    evtOnMpPresetPanelHide = LuaEvent:NewIns("Guide.evtOnMpPresetPanelHide"),
    
    evtOnAssemblySelectionMPMainShow = LuaEvent:NewIns("Guide.evtOnAssemblySelectionMPMainShow"),
    evtOnAssemblySelectionMPMainHide = LuaEvent:NewIns("Guide.evtOnAssemblySelectionMPMainHide"),
    evtOnAssemblySelectionMainShow = LuaEvent:NewIns("Guide.evtOnAssemblySelectionMainShow"),
    evtOnAssemblySelectionRepairBtnClicked = LuaEvent:NewIns("Guide.evtOnAssemblySelectionRepairBtnClicked"),
    evtOnPostAssemblySelectionRefreshEquipmentBtnState = LuaEvent:NewIns("Guide.evtOnPostAssemblySelectionRefreshEquipmentBtnState"),
    evtOnAssemblySelectionMainSelectFirstItemDone = LuaEvent:NewIns("Guide.evtOnAssemblySelectionMainSelectFirstItemDone"),


    evtRepairWindowRepairBtnClick = LuaEvent:NewIns("Guide.evtRepairWindowRepairBtnClick"),
    evtRepairWindowShowBegin = LuaEvent:NewIns("Guide.evtRepairWindowShowBegin"),
    evtRepairWindowHideBegin = LuaEvent:NewIns("Guide.evtRepairWindowHideBegin"),
    evtItemRepaireSuccess = LuaEvent:NewIns("Guide.evtItemRepaireSuccess"),
    evtWareHouseWithTabExtArrangeBtnPressed = LuaEvent:NewIns("Guide.evtWareHouseWithTabExtArrangeBtnPressed"),
    evtRepairWindowRepairFailedByNoEnoughCurrency = LuaEvent:NewIns("Guide.evtRepairWindowRepairFailedByNoEnoughCurrency"),

    evtOnWeaponUpgradeBtnClicked = LuaEvent:NewIns("Guide.evtOnWeaponUpgradeBtnClicked"),
    evtOnWeaponUpgradeUIShow = LuaEvent:NewIns("Guide.evtOnWeaponUpgradeUIShow"),
    evtOnWeaponUpgradeUIHide = LuaEvent:NewIns("Guide.evtOnWeaponUpgradeUIHide"),
    evtOnWeaponUpgradeUIShowLevelMax = LuaEvent:NewIns("Guide.evtOnWeaponUpgradeUIShowLevelMax"),
    evtOnWeaponUpgradePanelBtnClicked = LuaEvent:NewIns("Guide.evtOnWeaponUpgradePanelBtnClicked"),
    evtOnWeaponUpgradeSuccessShow = LuaEvent:NewIns("Guide.evtOnWeaponUpgradeSuccessShow"),
    evtOnWeaponUpgradeSuccessHide = LuaEvent:NewIns("Guide.evtOnWeaponUpgradeSuccessHide"),
    evtGunsmithUpgradeItemOperated = LuaEvent:NewIns("Guide.evtGunsmithUpgradeItemOperated"),
    evtOnUseItemBtnClicked = LuaEvent:NewIns("Guide.evtOnUseItemBtnClicked"),
    evtOnWeaponUpgradeSuccessPanelSelectAllClicked = LuaEvent:NewIns("Guide.evtOnWeaponUpgradeSuccessPanelSelectAllClicked"),
    evtOnAssemblySelectionOutAnimBegin = LuaEvent:NewIns("Guide.evtOnAssemblySelectionOutAnimBegin"),
    evtOnAssemblyWeaponClicked = LuaEvent:NewIns("Guide.evtOnAssemblyWeaponClicked"),
    evtOnAssemblyMainWeaponClicked = LuaEvent:NewIns("Guide.evtOnAssemblyMainWeaponClicked"),
    -- evtBeginMpSettlementPop = LuaEvent:NewIns("Guide.evtBeginSettlementPop"),   -- mp局外结算弹窗开始播放
    evtEndMpSettlementPop = LuaEvent:NewIns("Guide.evtEndMpSettlementPop"),   -- mp局外结算弹窗结束播放
    evtEndWeaponUpgradePop = LuaEvent:NewIns("Guide.evtEndWeaponUpgradePop"),   -- mp局外结算 武器升级弹窗结束/武器升级弹窗不需要弹出
    evtCarryItemFail = LuaEvent:NewIns("Guide.evtCarryItemFail"),

    evtTournamentMainUIShow = LuaEvent:NewIns("Guide.evtTournamentMainUIShow"),
    evtOpenRankRewardPanelBtnClicked = LuaEvent:NewIns("Guide.evtOpenRankRewardPanelBtnClicked"),

    evtMpRankSettlementInAnimFin = LuaEvent:NewIns("Guide.evtMpRankSettlementInAnimFinished"),   -- mp积分结算弹窗打开完毕
    evtMpRankSettlementClose = LuaEvent:NewIns("Guide.evtMpRankSettlementClose"),   -- mp积分结算弹窗关闭
    evtSolRankSettlementClose = LuaEvent:NewIns("Guide.evtSolRankSettlementClose"),   -- sol积分结算弹窗关闭

    evtOnAssemblyMPMainShowArmedforcedItem = LuaEvent:NewIns("Guide.evtOnAssemblyMPMainShowArmedforcedItem"),   -- mp兵种道具界面打开
    evtOnHeroItemClicked = LuaEvent:NewIns("Guide.evtOnHeroItemClicked"),
    evtHeroChangeToNonGuideHeroItem = LuaEvent:NewIns("Guide.evtChangeHeroToNonGuideItem"),   
    evtOnHeroViewSkillBtnClicked = LuaEvent:NewIns("Guide.evtOnHeroViewSkillBtnClicked"),
    evtOnHeroTopSkillPanelShow = LuaEvent:NewIns("Guide.evtOnHeroTopSkillPanelShow"),
    evtOnHeroTopSkillPanelClose = LuaEvent:NewIns("Guide.evtOnHeroTopSkillPanelClose"),
    evtOnHeroShowSkillBtnClicked = LuaEvent:NewIns("Guide.evtOnHeroShowSkillBtnClicked"),
    evtOnHeroSkillVideoPanelShow = LuaEvent:NewIns("Guide.evtOnHeroSkillVideoPanelShow"),
    evtHeroListTopPanelOnShowBegin = LuaEvent:NewIns("Guide.evtHeroListTopPanelOnShowBegin"),

    evtRankMainPanelShow = LuaEvent:NewIns("Guide.evtRankMainPanelShow"),
    evtRankMainPanelClose = LuaEvent:NewIns("Guide.evtRankMainPanelClose"),
    evtRankRewardBtnClicked = LuaEvent:NewIns("Guide.evtRankRewardBtnClicked"),

    -- BlackSite
    evtBlackSiteMainUIOpen = LuaEvent:NewIns("Guide.evtBlackSiteMainUIOpen"),
    evtBlackSiteMainUIClose = LuaEvent:NewIns("Guide.evtBlackSiteMainUIClose"),
    evtBlackSiteUpgradeUIOpen = LuaEvent:NewIns("Guide.evtBlackSiteUpgradeUIOpen"),
    evtBlackSiteUpgradeUIHideBegin = LuaEvent:NewIns("Guide.evtBlackSiteUpgradeUIHideBegin"),
    evtBlackSiteProduceUIHideBegin = LuaEvent:NewIns("Guide.evtBlackSiteProduceUIHideBegin"),
    evtBlackSiteUpgradeItemClicked = LuaEvent:NewIns("Guide.evtBlackSiteUpgradeItemClicked"),
    evtBlackSiteConstructUIOpen = LuaEvent:NewIns("Guide.evtBlackSiteConstructUIOpen"),
    evtBlackSiteProduceUIOpen = LuaEvent:NewIns("Guide.evtBlackSiteProduceUIOpen"),
    evtBlackSiteProduceOpenDevice = LuaEvent:NewIns("Guide.evtBlackSiteProduceOpenDevice"),
    evtBlackSiteEntranceConstructClicked = LuaEvent:NewIns("Guide.evtBlackSiteEntranceConstructClicked"),
    evtBlackSiteEntranceProduceClicked = LuaEvent:NewIns("Guide.evtBlackSiteEntranceProduceClicked"),
    evtBlackSiteUpgradeConditionsUIOnShowBegin = LuaEvent:NewIns("Guide.evtBlackSiteUpgradeConditionsUIOnShowBegin"),
    evtBlackSiteConditionsUpgradeBtnClicked = LuaEvent:NewIns("Guide.evtBlackSiteConditionsUpgradeBtnClicked"),


    evtOnPreDoSwitchModeOperation = LuaEvent:NewIns("Guide.evtOnPreDoSwitchModeOperation"), -- 切换模式前做的操作


    ------ 触发式引导启动事件
    evtLootBtnShow        = LuaEvent:NewIns("evtLootBtnShow"),  -- loot按钮出现

    -- @dexzhou 2025/7/23: 确认 12和15两个引导阶段 deprecated
    -- evtOpenWinTransToInventory    = LuaEvent:NewIns("evtOpenWinTransToInventory"),  -- sol对局结束后，撤离成功打开仓库转移界面
    evtMpGameStartBtnClick        = LuaEvent:NewIns("evtMpGameStartBtnClick"),  -- 点击mp的开始游戏按钮

    ------ c++事件
    evtGuideTryMarkMap          = LuaEvent:NewIns("evtGuideTryMarkMap"),
    evtGuideMarkDetailOpen      = LuaEvent:NewIns("evtGuideMarkDetailOpen"),
    evtGuideInteractionPanelShow = LuaEvent:NewIns("evtGuideInteractionPanelShow"),
    evtGuideInteractionPanelHide = LuaEvent:NewIns("evtGuideInteractionPanelHide"),
    evtGuideInteractionPanelInteractorsChanged = LuaEvent:NewIns("evtGuideInteractionPanelInteractorsChanged"),

    evtGuidePause      = LuaEvent:NewIns("evtGuidePause"),
    evtGuideRestart    = LuaEvent:NewIns("evtGuideRestart"),

    evtBeginSeamless = LuaEvent:NewIns("Guide.evtBeginSeamless"),
    evtRealBoarding = LuaEvent:NewIns("Guide.evtRealBoarding"),
    evtAuctionOnShelfUncountableSellOpen = LuaEvent:NewIns("Guide.evtAuctionOnShelfUncountableSellOpen"),
    evtAuctionSellAuctionClicked = LuaEvent:NewIns("Guide.evtAuctionSellAuctionClicked"),
    evtAuctionSellAuctionFail = LuaEvent:NewIns("Guide.evtAuctionSellAuctionFail"),
    evtAuctionSellOpen = LuaEvent:NewIns("Guide.evtAuctionSellOpen"),
    evtAuctionSellHide = LuaEvent:NewIns("Guide.evtAuctionSellHide"),
    evtAuctionRealSellAuctionClicked = LuaEvent:NewIns("Guide.evtAuctionRealSellAuctionClicked"),
    evtAuctionShelveClose = LuaEvent:NewIns("Guide.evtAuctionShelveClose"),
    evtMainPanelHide = LuaEvent:NewIns("Guide.evtMainPanelHide"),
    evtStartClicked = LuaEvent:NewIns("Guide.evtStartClicked"),
    evtOnIrisEnterStageChange = LuaEvent:NewIns("Guide.evtOnIrisEnterStageChange"),
    evtOnSeamlessEnterFirstSeqStart = LuaEvent:NewIns("Guide.evtOnSeamlessEnterFirstSeqStart"),

    -- Common Widget Framework
    evtTopBarFinished = LuaEvent:NewIns("Guide.evtTopBarFinished"),
    evtCommonBarTabIndexChanged = LuaEvent:NewIns("Guide.evtCommonBarTabIndexChanged"),

    evtLootingAiTagChanged = LuaEvent:NewIns("Guide.evtLootingAiTagChanged"),

    -- Market
    evtMarketSkinHideBegin = LuaEvent:NewIns("Guide.evtMarketSkinHideBegin"),

    -- Quest
    evtQuestMainPanelHideBegin = LuaEvent:NewIns("Guide.evtQuestMainPanelHideBegin"),


    -- SolHUD
    evtOnRouletteMain_PC_AnimShow = LuaEvent:NewIns("Guide.evtOnRouletteMain_PC_AnimShow"),

    -- GuideData
    evtCloseHDPopFunctionDescAndWeakClick = LuaEvent:NewIns("Guide.evtCloseHDPopFunctionDescAndWeakClick"),

    -- Inventory
    evtUnquipBreastPlate = LuaEvent:NewIns("Guide.evtUnquipBreastPlate"), -- 卸下胸甲
    evtUnEquipHelmet = LuaEvent:NewIns("Guide.evtUnEquipHelmet"),

    -- Sol Iris
    evtIrisWorldEntryOnHideBegin = LuaEvent:NewIns("Guide.evtIrisWorldEntryOnHideBegin"),

    
}


---@enum EGuideShowUIFlag
GuideConfig.EGuideShowUIFlag = {
    WaitSettlement          = 1,
    WaitGuideData           = 2,
    OnlyGuiding             = 3,
    WaitMaskLoading         = 4,
    Reconnect               = 5,
    OnlyGuidingAndPop       = 6,  -- 只显示pop层
    OnlyGuidingAndStack     = 7,  -- 主要是隐藏top层，其他层暂时不限制
    WaitEnterNewPlayerMatch = 8,  -- 等待直接进入新手局，暂时显示水印层、pop层、tips层
    Pause                   = 9,  -- 暂停引导
    MpWaitGameNum           = 10, -- 等待mp更新对局数
    PopWindowUI             = 11, -- 全屏弹窗，要屏蔽hud层
}


---@class EGuideShowUIFlagInfo
GuideConfig.EGuideShowUIFlagInfo = {
    [GuideConfig.EGuideShowUIFlag.WaitSettlement] = {
        EUILayer.Pop,
        EUILayer.Stack,
        EUILayer.Mask,
        EUILayer.Watermark,
        EUILayer.Admin,
    },
    [GuideConfig.EGuideShowUIFlag.WaitGuideData] = {
        EUILayer.Mask,
        EUILayer.Watermark,
        EUILayer.HUD, -- 局内sol撤离的时候，需要显示hud
        EUILayer.HUD_Common,
        EUILayer.Loading,
        EUILayer.Admin,
    },
    [GuideConfig.EGuideShowUIFlag.OnlyGuiding] = {
        EUILayer.Mask,
        EUILayer.Watermark,
        EUILayer.Admin,
    },
    [GuideConfig.EGuideShowUIFlag.WaitMaskLoading] = {
        EUILayer.Loading,
        EUILayer.Mask,
        EUILayer.Watermark,
        EUILayer.Admin,
    },
    [GuideConfig.EGuideShowUIFlag.Reconnect] = {
        EUILayer.Mask,
        EUILayer.Watermark,
        EUILayer.Admin,
    },
    [GuideConfig.EGuideShowUIFlag.OnlyGuidingAndPop] = {
        EUILayer.Mask,
        EUILayer.Watermark,
        EUILayer.Pop,
        EUILayer.Admin,
    },
    [GuideConfig.EGuideShowUIFlag.OnlyGuidingAndStack] = {
        EUILayer.Mask,
        EUILayer.Watermark,
        EUILayer.Stack,
        EUILayer.Tip,
        EUILayer.Pop,
        EUILayer.Admin,
    },
    [GuideConfig.EGuideShowUIFlag.WaitEnterNewPlayerMatch] = {
        EUILayer.Mask,
        EUILayer.Watermark,
        EUILayer.Tip,
        EUILayer.Pop,
        EUILayer.Loading,
        EUILayer.Admin,
    },
    [GuideConfig.EGuideShowUIFlag.Pause] = {
        EUILayer.HUD,
        EUILayer.HUD_ScreenEffect,
        EUILayer.HUD_Mark,
        EUILayer.HUD_Common,
        EUILayer.HUD_Hint,
        EUILayer.HUD_Feedback,
        EUILayer.HUD_Touch,
        EUILayer.HUD_Popup,
        EUILayer.HUD_LargePopup,
        EUILayer.Scene,
        EUILayer.Root,
        EUILayer.Stack,
        EUILayer.Top,
        EUILayer.Pop,
        EUILayer.Tip,
        EUILayer.Watermark,
        EUILayer.BackRoot,
        EUILayer.Admin,
    },
    [GuideConfig.EGuideShowUIFlag.MpWaitGameNum] = {
        EUILayer.Pop,
        EUILayer.Mask,
        EUILayer.Watermark,
        EUILayer.Admin,
    },
    [GuideConfig.EGuideShowUIFlag.PopWindowUI] = {
        EUILayer.Pop,
        EUILayer.Top,
        EUILayer.Mask,
        EUILayer.Watermark,
        EUILayer.Admin,
    },
}


if GuideConfig.TableGuideStageClientAndServerConfig then
    for _, stageConfig in pairs(GuideConfig.TableGuideStageClientAndServerConfig) do
        local clientOnlyConfig = GuideConfig.TableGuideStageConfig[stageConfig.GuideStageId]
        if clientOnlyConfig then
        else
            logerror("clientOnlyConfig is nil, stage id:", stageConfig.GuideStageId)
        end
    end
else
    logerror("TableGuideStageClientAndServerConfig is nil")
end

GuideConfig.Guide3CDialogueTitle = {
    [1] = "",
    [2] = GuideConfig.Loc.guide3CDialogTitle2,
    [3] = GuideConfig.Loc.guide3CDialogTitle3,
    [4] = GuideConfig.Loc.guide3CDialogTitle4,
}

GuideConfig.Guide3CDialogueText = {
    [1] = GuideConfig.Loc.guide3CDialogText1,
    [2] = GuideConfig.Loc.guide3CDialogText2,
    [3] = GuideConfig.Loc.guide3CDialogText3,
    [4] = GuideConfig.Loc.guide3CDialogText4,
}


GuideConfig.EStructTutorialStageHallView_Failed ={
    EGuideStage.tSolMatchTimeout,
    EGuideStage.tSolPreventBankrupt,
    EGuideStage.tSolPOIHighAccessTips,
}

--- represent of FGuideStructTutorial.GuideType
---@enum EGuideStructTutorialStage
GuideConfig.EGuideStructTutorialStage ={
    InGameHud = 1,
    SettlementView = 2,
    AssemblyView = 3,
    HallView = 4,
}

GuideConfig.EGuideStageClientMode = {
    None = 1 << 0,
    SOL = 1 << 1,
    MP = 1 << 2,
    Raid = 1 << 3,
    StructTutorial = 1 << 4,
    Arena = 1 << 5
}


GuideConfig.EGuideClickConfigOption ={
    HDNoFallbackText = 1 << 0, -- HD不使用fallback文本
    HDNoDefaultIcon = 1 << 1,  -- HD不使用默认图标
}

-- 2024/11/19  see datatable *Buff*
GuideConfig.PartDeBuffIds = {
    Bleed = 1101,  -- 流血
    -- HairlineFracture =  1210, -- 骨裂
    -- Ache = 1301 , --疼痛
    -- AcheWithMaxHealthReduction =130101 , --疼痛（最大生命值减少）
    HeadInjury = 4001,       -- 头部受伤
    BreastInjury = 4002,     -- 胸部受伤
    ArmBoneFracture = 4003,  -- 手部骨折
    LegBoneFracture = 4004,  -- 腿部骨折
    AbdominalInjury = 4005,  -- 腹部受伤

}

-- GuideConfig.BuffIdToGuideStageId = {
--     [4001] = EGuideStage.playerStateBuffHeadHurt,
--     [4002] = EGuideStage.playerStateBuffChestHurt,
--     [4003] = EGuideStage.playerStateBuffArmHurt,
--     [4004] = EGuideStage.playerStateBuffLegHurt,
--     [71] = EGuideStage.playerStateOverWeigth,
-- }

GuideConfig.BuffIdToGuideStageId = {
    [GuideConfig.PartDeBuffIds.HeadInjury]      = EGuideStage.playerStateBuffLegHurt,
    [GuideConfig.PartDeBuffIds.BreastInjury]    = EGuideStage.playerStateBuffLegHurt,
    [GuideConfig.PartDeBuffIds.ArmBoneFracture] = EGuideStage.playerStateBuffLegHurt,
    [GuideConfig.PartDeBuffIds.LegBoneFracture] = EGuideStage.playerStateBuffLegHurt,
}
if DFHD_LUA == 1 then
    GuideConfig.BuffIdToGuideStageId = {
        [GuideConfig.PartDeBuffIds.HeadInjury]      = EGuideStage.solScriptMatchPlayerStateBuffHD,
        [GuideConfig.PartDeBuffIds.BreastInjury]    = EGuideStage.solScriptMatchPlayerStateBuffHD,
        [GuideConfig.PartDeBuffIds.ArmBoneFracture] = EGuideStage.solScriptMatchPlayerStateBuffHD,
        [GuideConfig.PartDeBuffIds.LegBoneFracture] = EGuideStage.solScriptMatchPlayerStateBuffHD,
    }
end

GuideConfig.NewPlayerGuideStageList = {
    [1] = EGuideStage.newPlayerGuideStage1,
    [2] = EGuideStage.newPlayerGuideStage2,
    [3] = EGuideStage.newPlayerGuideStage3,
    [4] = EGuideStage.newPlayerGuideStage4,
    -- [5] = EGuideStage.newPlayerGuideStage5,
    -- [6] = EGuideStage.newPlayerGuideStageIntro,
    -- [7] = EGuideStage.newPlayerGuideStageRename,
}

GuideConfig.MpNewPlayerGuideStageList = {
    ["mpDesc"] = EGuideStage.mpDesc,
    ["mpAssembly"] = EGuideStage.mpAssembly,
    ["mpArmedForceItem"] = EGuideStage.mpArmedForceItem,
}

GuideConfig.ESelectMode = {
    None    = 0,
    SOL     = 1,
    MP      = 2,
}

GuideConfig.PopSubUI = {
    -- [1] = UIName2ID.PopWindowContentShootType,
    -- [2] = UIName2ID.PopWindowContentLootDesc,
}




GuideConfig.AuctionUnlockLevel = 5

GuideConfig.WaitAuctionUnlockStage = 2
GuideConfig.AuctionUnlockStage = 3


--[[
2025/6/3 <dexzhou>

ClientCommonEventLog 为 1*10^7 ~ 2*10^7 的整数
-- 首位表示是引导的log，第二位表示是 局外引导单步开始(1) 局外单步引导结束(2) 局内引导(3及其他)，三四五位表示阶段id，后三位表示具体单步id
-- 如阶段1的第一步，guideId为1001，开始时上报log为11001001，结束时上报12001001,在LogAnal 被重定向为 21001001(手游) 或 31001001(手柄)

]]

GuideConfig.ELogTypeId = {
    PC = 1,
    Mobile = 2,
    Gamepad = 3,
}

---@enum GuideConfig.ELogExType
GuideConfig.ELogExType = {
    GuideStepStart = 1,
    GuideStepEnd = 2,
    InGameAndOther = 3, -- 比如新手局内任务上报
    Ext1 = 4,
    Ext2 = 5,
    Ext3 = 6,
    Ext4 = 7,
    Ext5 = 8,
    Ext6 = 9,
}
--- See LogAnalysisTool.DoSendClientCommonEventLog
for k, v in pairs(GuideConfig.ELogExType) do
    assert(v >= 1 and v < 10, "EGuideStepExtPrefix value must be in range [1, 10), current value: " .. tostring(v))
end

-- 用于扩充 ClientCommonEventLog 的 ELogExtType
GuideConfig.EGuideData2ELogExType = {
    [GuideConfig.EGuideData.PopWindow] = {
        PlayVideoTrigger = GuideConfig.ELogExType.Ext1, -- 点击播放视频
        FullScreenVideo = GuideConfig.ELogExType.Ext2,  -- 全屏视频
    }
}


-- 特殊埋点id
-- 可选：11000001 ~ 11000999
GuideConfig.ESpecCommonEventLogId = {
    BeginSeamlessMatch = 11000001,  -- 无缝入局
    ModeSelectSOL      = 11000002,  -- 模式弹窗选择sol
    ModeSelectMP       = 11000003,  -- 模式弹窗选择mp

    FirstTabClicked    = 11000004,  -- tab第一次点击
    FirstForrestMapClicked = 11000005,  -- 森林地图第一次点击
    FirstForrestSelected = 11000006,  -- 森林地图第一次选择
    --FirstArmedForceOpened = 11000007,  -- 备战界面第一次打开
    AuctionVOBuyUnlock = 11000008,  -- 拍卖购买解锁
    AuctionVOSellUnlock = 11000009,  -- 拍卖出售解锁

    ClosePopWindowBattle = 11000010,  -- 关闭战斗弹窗
    ClosePopWindowLooting = 11000011,  -- 关闭掠夺弹窗
    ClosePopWindowEscape = 11000012,  -- 关闭撤离弹窗
    FirstBigBaMapClicked = 11000013,  -- 大坝地图第一次点击
    FirstSpaceCenterMapClicked = 11000014,  -- 航天城地图第一次点击
    FirstZBLTMapClicked = 11000015,  -- 直布罗陀地图第一次点击

    OpenMpMainPanel = 11000016,  -- MP主面板打开
    MpDescVedioBegin = 11000017,  -- MP描述视频开始
    MpDescVedioEnd = 11000018,  -- MP描述视频结束

    FirstMapItemClicked = 11000019,  -- 战略版解锁的任意地图第一次点击

    OpenBHDMainPanel = 11000020,  -- BHD主面板打开
    BHDDescVedioBegin = 11000021,  -- BHD描述视频开始
    BHDDescVedioEnd = 11000022,  -- BHD描述视频结束

    SeamlessOnBoard = 11001008, -- 无缝上飞机
}




GuideConfig.MpWeaponUpgradeItemId = 32210000002
GuideConfig.HeroSkillGuideHeroId = 88000000029

GuideConfig.guideArmedForceArmorDurabilityPercent = 0.8
GuideConfig.guideMysticalWorkshopMinWeaponAmount = 1

-- 笛卡尔积一下: 图片、进包/流视频、子蓝图、点击播视频、点击播全屏视频、点击跳转到下一步
GuideConfig.EGuidePopWindowItemExContentType = {
    Image = 0,
    ImageAndPlayBtn = 1,    -- 开发用， 点击走 -2 分支
    Video = 10,            --  弹窗内视频， 根据 MediaResTable 是否为流视频添加播放按钮(
    SubWBP = 11,
    FullScreenVideo = 12, -- 2025/5/22 仅mobile
}

GuideConfig.EPopWindowCloseLogId = {
    [19] = GuideConfig.ESpecCommonEventLogId.ClosePopWindowBattle,
    [20] = GuideConfig.ESpecCommonEventLogId.ClosePopWindowLooting,
    [21] = GuideConfig.ESpecCommonEventLogId.ClosePopWindowEscape,
}
---@enum   GuideConfig.EPopWindowUICloseReason
GuideConfig.EPopWindowUICloseReason = {
    CloseBtnClicked = 1,
    PlayVideoBtnClicked = 2,
    EscapedBtnClicked = 3,
}
GuideConfig.EPopWindowShowType = {
    ProgressBar = 1 << 0,
}
--story=123872793 [【手游|PC】【前台】【组件接入】【she2】UA客户端活跃向点位上报（自发+GA）——dexzhou](https://tapd.woa.com/r/t?id=123872793&type=story) 
--新手关第一个弹窗
GuideConfig.TutorialLevelFirstBattlePopWindowId = 19

---@enum EGuideEvacuationPointType
GuideConfig.EEvacuationPointType = {
    None = -1,
    Pay = 1,         -- 付费
    Random = 2,      -- 随机
    DropBag = 3,     -- 丢包
    WeightLimit = 4, -- 负重
    Switcher = 5,    -- 拉闸
}
GuideConfig.SolPayerEvacuationPointGuide = {
    refs = {
        EGuideStage.solPlayerEvacuationPointInstruct,
        EGuideStage.solPlayerEvacuationPointInstructPopWindow,
    },
    EEvacuationPointType = GuideConfig.EEvacuationPointType,
}


-- 特殊的引导弹窗Id, 供其他模块
GuideConfig.EEvacuationPointType2PopWindowId = {
    [GuideConfig.EEvacuationPointType.Pay] = 97,
    [GuideConfig.EEvacuationPointType.Random] = 98,
    [GuideConfig.EEvacuationPointType.DropBag] = 99,
    [GuideConfig.EEvacuationPointType.WeightLimit] = 100,
    [GuideConfig.EEvacuationPointType.Switcher] = 101,
}


GuideConfig.CustomDataFuncSeparator = "@"


---@enum GuideConfig.EVideoPlayType
GuideConfig.EVideoPlayType = {
    SolGuide = 1,   -- sol引导视频
    Settlement = 2, -- 局外结算视频
}

GuideConfig.EPlayerEscapeType2VideoId = {
    [EGspPlayerResultType.kGspPlayerResultEscaped] = "guidefullwindow_01",
    [EGspPlayerResultType.kGspPlayerResultKilled] = "guidefullwindow_02",
    [EGspPlayerResultType.kGspPlayerResultMissing] = "guidefullwindow_03",
}

GuideConfig.SettlemenVideoGuide ={
    hdRef = EGuideStage.solSettlementVideoHD,
    mobielRef = {
        EGuideStage.solFirstEvacuationFailed,
        EGuideStage.solFirstEvacuationSuccess,
        EGuideStage.solFirstEvacuationFailedByTimeout,
    },
    EPlayerEscapeType2Video = GuideConfig.EPlayerEscapeType2VideoId,
}



GuideConfig.bMockSkillDataFlow = false
-- 参考 https://doc.weixin.qq.com/sheet/e3_AKUAFwYlACgFHFv63ZjSP0hTobUm3
-- 按定义顺序优先级降序, 暂时定义在代码里(需要单独的datatable?)
---@type GuideConfig.AvaliableHeroAgainstSkillEntry[]
GuideConfig.AvaliableHeroAgainstSkill = {
    -- 盾构钩爪 玩家在倒地前10s被深蓝使用抓钩抓住
    ---@class GuideConfig.AvaliableHeroAgainstSkillEntry
    ---@field name string
    ---@field matches string[]
    ---@field guideStep number
    ---@field branch number
    {
        name = "AlexieChainHook",
        matches ={
            "AlexieBeChainHookedAsKilled10s",
        },
        guideStep = 129020,
        branch = 2,
    },
    -- 盾构防爆盾 玩家在倒地前10s被持盾的深蓝盾击过
    {
        name = "AlexieShield",
        matches = {
            "AlexieBeShieldAttackedAsKilled10s",
        },
        guideStep = 129021,
        branch = 3,
    },
    -- 无名静默 玩家在倒地10s前被在静默潜袭状态的无名击倒
    {
        name = "NoxSilent",
        matches = {
            "ShadowBeInSilenceAsKilled",
        },
        guideStep = 129022,
        branch = 4,
    },
    --骇爪信号 玩家使用信号破译器后10s内被击败
    {
        name = "HackerTracing",
        matches = {
            "HackerTracingAsKilled",
        },
        guideStep = 129023,
        branch = 5,
    },
}
GuideConfig.SkillAgainstGuide = {
    ref = EGuideStage.solSkillAgainst,
    avaliableHeroAgainstSkill = GuideConfig.AvaliableHeroAgainstSkill,
}
-- descending by priority, maybe we need a priority column in the future
-- table.sort(GuideConfig.AvaliableHeroAgainstSkill, function(a, b)
--     -- return a < b
--     return true
-- end)


--- 
GuideConfig.LowMaxAmorDurabilityGuide = {
    ref = EGuideStage.solArmedForceArmorLowMaxDurability,
    requiredAmorLevel = 3,
    requiredCurPercent = 0.8,
    requiredCurMaxPercent = 0.75,
}

GuideConfig.CustomSolVoucherGuide = {
    ref = EGuideStage.solCustomVoucher,
    itemIds = {
        32331000001,
        32331000002,
        32331000003,
        32332000001,
        32332000002,
        32332000003,
    }
}


---@enum GuideConfig.EExternalModification
GuideConfig.EExternalModification = {
    LootingDeadBodyPanelScrollEnable = "LootingDeadBodyPanelScrollEnable", -- 死亡尸体面板滚动条是否可用
}

-------------------------------------------------------------------------
--region 全局开关的设置

-- 是否启用引导
GuideConfig.FlagEnableGuide = Server.GuideServer.FlagEnableGuide
-- 纯客户端表现，不走服务器逻辑
GuideConfig.FlagOnlyClient = Server.GuideServer.FlagOnlyClient
-- 点击类型的引导挖空是否持续刷新
GuideConfig.FlagClickGuideTick = true
--endregion
-- 是否强制开启Intro关
GuideConfig.FlagForceEnableIntro = false
--endregion
-----------------------------------------------------------------------



return GuideConfig