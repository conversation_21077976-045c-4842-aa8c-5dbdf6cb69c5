----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LogMusicPlayer)
----- LOG FUNCTION AUTO GENERATE END -----------

local MusicPlayerWidgetLogic = require "DFM.Business.Module.MusicPlayerModule.Logic.MusicPlayerWidgetLogic"
local CommonPopWindows = require "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindows"
local CommonSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.CommonSettingLogicHD"
local VolumeSettingLogic = require "DFM.Business.Module.SystemSettingModule.Logic.VolumeSettingLogic"
local ClientVolumeSetting = import "ClientVolumeSetting"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"

---@class MusicPlayerListPop : LuaUIBaseView
local MusicPlayerListPop = ui("MusicPlayerListPop")

local TICK_TIME = 0.1
local MUSIC_VOLUME_ID = "Volume_Music"

function MusicPlayerListPop:Ctor()
    self._wtWaterfallList = UIUtil.WndWaterfallScrollBox(self, "DFWaterfallScrollView_0", self._OnWaterfallCount, self._OnWaterfallWidget)
    self._wtMainIcon = self:Wnd("DFImage_Icon", UIImage)
    self._wtTitle = self:Wnd("DFTextBlock_50", UITextBlock)
    self._wtDesc = self:Wnd("DFCommonTextBlock_91", UITextBlock)
    self._wtCurPlayTime = self:Wnd("DFTextBlock", UITextBlock)
    self._wtTotalPlayTime = self:Wnd("DFTextBlock_1", UITextBlock)
    self._wtSwitchModeIcon = self:Wnd("DFImage_1", UIImage)
    self._wtPlayOrPauseIcon = self:Wnd("DFImage_Icon_01", UIImage)
    self._wtCircleText = self:Wnd("DFTextBlock_137", UITextBlock)

    self._wtPreviewBtn = self:Wnd("DFButton_Previous", UIButton)
    self._wtPreviewBtn:Event("OnClicked", self.OnClickedPreviewBtn, self)
    self._wtPauseOrPlayBtn = self:Wnd("DFButton_PauseOrPlay", UIButton)
    self._wtPauseOrPlayBtn:Event("OnClicked", self.OnClickedPauseOrPlayBtn, self)
    self._wtNextBtn = self:Wnd("DFButton_Next", UIButton)
    self._wtNextBtn:Event("OnClicked", self.OnClickedNextBtn, self)
    self._wtSwitchModeBtn = self:Wnd("DFButton_0", UIButton)
    self._wtSwitchModeBtn:Event("OnClicked", self.OnClickedSwitchModeBtn, self)

    ---@type DFSlider 进度条控制控件
    self._wtOperaSlider = self:Wnd("DFSlider_65", UILightWidget)
    self._wtOperaSlider:SetValue(0) -- 初始化进度为0
    self._wtOperaSlider:Event("OnValueChanged", self.OnSliderValueChanged, self)
    self._wtOperaSlider:Event("OnMouseCaptureBegin", self.OnSliderControllerCaptureBegin, self)
    self._wtOperaSlider:Event("OnMouseCaptureEnd", self.OnSliderControllerCaptureEnd, self)

    self._wtDFButton_1 = self:Wnd("DFButton_1", UIButton)
    self._wtDFButton_1:Event("OnHovered", self.OnSliderControllerHover, self)
    self._wtDFButton_1:Event("OnUnHovered", self.OnSliderControllerUnhover, self)

    self._wtProgressSlider = self:Wnd("DFProgressBar_98", UIProgressBar)
    self._wtProgressSlider:SetPercent(0)

    self._wtMusicVolumeRoot = self:Wnd("DFCanvasPanel_PC", UIWidgetBase)
    --self._wtMusicVolumeRoot:Collapsed()

    ---@type DFSlider 进度条控制控件
    self._wtOperaMusicVolumeSlider = self:Wnd("DFSlider", UILightWidget)
    self._wtOperaMusicVolumeSlider:SetValue(0) -- 初始化进度为0
    self._wtOperaMusicVolumeSlider:Event("OnValueChanged", self.OnMusicVolumeSliderValueChanged, self)
    self._wtOperaMusicVolumeSlider:Event("OnMouseCaptureBegin", self.OnMusicVolumeSliderControllerCaptureBegin, self)
    self._wtOperaMusicVolumeSlider:Event("OnMouseCaptureEnd", self.OnMusicVolumeSliderControllerCaptureEnd, self)

    self._wtDFButton_2 = self:Wnd("DFButton_2", UIButton)
    self._wtDFButton_2:Event("OnHovered", self.OnMusicVolumeSliderHover, self)
    self._wtDFButton_2:Event("OnUnHovered", self.OnMusicVolumeSliderUnhover, self)

    self._wtMusicVolumeProgressSlider = self:Wnd("DFProgressBar", UIProgressBar)
    self._wtMusicVolumeProgressSlider:SetPercent(0)
    
    self._wtCommonPopWin = self:Wnd("wtMainPanel", CommonPopWindows)
    local fCallbackIns = CreateCallBack(self._OnCloseBtnClicked,self)
    self._wtCommonPopWin:BindCloseCallBack(fCallbackIns)
    self._wtCommonPopWin:SetBackgroudClickable(true)

    self._musicList = {}
    self._bIsSliderCpature = false
    self._seekValue = 0
    self._musicVolumeValue = 0
end
function MusicPlayerListPop:OnOpen()

end

function MusicPlayerListPop:OnShowBegin()
    MusicPlayerWidgetLogic.InitMusicList()
    self._musicList = MusicPlayerWidgetLogic.GetMusicList()
    self._wtWaterfallList:RefreshAllItems()
    --self._wtMusicVolumeRoot:Collapsed()
    self._bIsSliderCpature = false
    self:RegisterEvents()
    self:_EnableGamepadFeature()
    self:_RefreshView()
    
    local curIndex = MusicPlayerWidgetLogic.GetCurMusicIndex()
    self._wtWaterfallList:ScrollToIndex(curIndex)
    if IsHD() and self._listGroup then
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._listGroup)
        local curItemWidget = self._wtWaterfallList:GetItemByIndex(curIndex)
        if curItemWidget then
            WidgetUtil.SetUserFocusToWidget(curItemWidget,false)
        end
    end
end

function MusicPlayerListPop:OnHide()
    self:UnregisterEvents()
    self:_DisableGamepadFeature()
    self._curMusicDuration = 0
end

function MusicPlayerListPop:_OnCloseBtnClicked()
    Facade.UIManager:CloseUI(self)
end


function MusicPlayerListPop:RegisterEvents()
    self:AddLuaEvent(Module.MusicPlayer.Config.Events.evtOnMusicPlayerPlayMusicPercentChange, self.OnMusicPlayerPlayMusicPercentChange, self)
    self:AddLuaEvent(Module.MusicPlayer.Config.Events.evtOnMusicPlayerPlayStateChange, self.OnMusicPlayStateChanged, self)
    self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadProgress,
    self.OnDownloadedChanged, self)
    self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult, self.OnDownloadedChanged, self)

end
function MusicPlayerListPop:UnregisterEvents()
    self:RemoveLuaEvent(Module.MusicPlayer.Config.Events.evtOnMusicPlayerPlayMusicPercentChange)
    self:RemoveLuaEvent(Module.MusicPlayer.Config.Events.evtOnMusicPlayerPlayStateChange)
    self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadProgress)
    self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult)
end

function MusicPlayerListPop:OnDownloadedChanged(packageName)
    local musicModuleName = LiteDownloadManager:GetSystemDownloadModuleName(EDownloadSystem.MusicPlayer)
    if packageName == musicModuleName then
        self:_RefreshPlayIcon()
    end
end

function MusicPlayerListPop:OnMusicPlayStateChanged(evtName,evtState)
    self:_RefreshView()
end

function MusicPlayerListPop:OnMusicPlayerPlayMusicPercentChange(inPercent)

    local curMusicData = MusicPlayerWidgetLogic.GetCurMusicData()
    if not curMusicData then
        logerror("[MusicPlayerListPop:OnMusicPlayerPlayMusicPercentChange] curMusicData is nil")
        return
    end

    if self._curMusicDuration == 0 then
        self._curMusicDuration = Facade.SoundManager:GetMusicDuration(curMusicData.AudioEventName) -- 音乐时长
        self._wtTotalPlayTime:SetText(self:SecondsToMinutes(self._curMusicDuration))
    end

    if inPercent == 0 then
        return
    end

    local isPlaying = MusicPlayerWidgetLogic.IsPlayingInMusicList(MusicPlayerWidgetLogic.GetCurMusicEvtName())
    if isPlaying and  not self._bIsSliderCpature then
        self._wtProgressSlider:SetPercent(inPercent)
    end

    local curPlayingTime = math.floor(self._curMusicDuration * inPercent)
    self._wtCurPlayTime:SetText(self:SecondsToMinutes(curPlayingTime))
end

function MusicPlayerListPop:SecondsToMinutes(seconds)
    if not seconds or seconds < 0 then
        seconds = 0
    end
    local minutes = math.floor(seconds / 60)
    local remainingSeconds = seconds % 60
    remainingSeconds = math.round(remainingSeconds)
    local ret = string.format("%02d:%02d", minutes, remainingSeconds)
    return ret
end

function MusicPlayerListPop:_RefreshView(bIsUpdateTimeText)
    local curMusicData = MusicPlayerWidgetLogic.GetCurMusicData()
    if not curMusicData then
      logerror("[MusicPlayerWidget:OnShowBegin] curMusicData is nil")
      return
    end
    self._wtMainIcon:AsyncSetImagePath(curMusicData.Icon)
    self._wtTitle:SetText(curMusicData.Name)
    self._wtDesc:SetText(curMusicData.Desc)
    local curEvtName = MusicPlayerWidgetLogic.GetCurMusicEvtName()
    local curPos = MusicPlayerWidgetLogic.GetMusicRecordPos(curEvtName)
    self._wtProgressSlider:SetPercent(curPos)
    local volume = self:_GetMusicVolume() / 100
    self._wtMusicVolumeProgressSlider:SetPercent(volume)
    self._wtOperaMusicVolumeSlider:SetValue(volume)
    self._curMusicDuration = Facade.SoundManager:GetMusicDuration(curMusicData.AudioEventName) -- 音乐时长
    self._wtTotalPlayTime:SetText(self:SecondsToMinutes(self._curMusicDuration))
    self._wtCurPlayTime:SetText(self:SecondsToMinutes(self._curMusicDuration * curPos))
    self:_RefreshPlayIcon()
    self:_RefreshModeIcon()
end



function MusicPlayerListPop:_OnWaterfallCount()
    return #self._musicList
end

---@param itemWidget MusicPlayerListPopItem
function MusicPlayerListPop:_OnWaterfallWidget(position, itemWidget)
    local musicData = self._musicList[position]
    if not musicData then
        logerror("[MusicPlayerListPop:_OnWaterfallWidget] musicData is nil")
        return
    end

    local fOnMouseEnterCallback = function()
        if not musicData then
            logerror("[MusicPlayerListPopItem:fOnMouseEnterCallback] musicData is nil")
            return
        end
        self:_RefreshBottomTipsBar(musicData)
    end

    local fOnClickedCallback = function()
        if not musicData then
            logerror("[MusicPlayerListPopItem:fOnClickedCallback] musicData is nil")
            return
        end

        local isHd = IsHD()
        local isGamePad = WidgetUtil.IsGamepad()
        local isPlaying = MusicPlayerWidgetLogic.IsPlayingInMusicList(musicData.AudioEventName)
        local isUnlocked , unlockedWay = MusicPlayerWidgetLogic.IsUnlockedMusic(musicData.AudioEventName)
        if not isUnlocked then
            Module.CommonTips:ShowSimpleTip(unlockedWay)
            return
        end
        if isHd and isGamePad then
            if isPlaying then
                MusicPlayerWidgetLogic.SwitchCurMusicPalyState()
            else
                MusicPlayerWidgetLogic.PlayMusic(musicData.AudioEventName)
            end
            self:_RefreshBottomTipsBar(musicData)
        else
            if isPlaying then
                Module.CommonTips:ShowSimpleTip(Module.MusicPlayer.Config.Loc.MuiscIsPlaying)
            else
                MusicPlayerWidgetLogic.PlayMusic(musicData.AudioEventName)
            end
        end
    end
    itemWidget:SetData(musicData,fOnMouseEnterCallback,fOnClickedCallback)
    itemWidget:RefreshView()
end

function MusicPlayerListPop:OnClickedPreviewBtn()
    MusicPlayerWidgetLogic.PreMusic()
end

function MusicPlayerListPop:OnClickedPauseOrPlayBtn()
    local curEvtName = MusicPlayerWidgetLogic.GetCurMusicEvtName()
    MusicPlayerWidgetLogic.ClickedControlBtn_Implement(curEvtName)
    --MusicPlayerWidgetLogic.SwitchCurMusicPalyState()
end

function MusicPlayerListPop:OnClickedNextBtn()
    MusicPlayerWidgetLogic.NextMusic()
end

function MusicPlayerListPop:OnClickedSwitchModeBtn()
    local curPlayMode = MusicPlayerWidgetLogic.GetPlayMode()
    local nextPlayMode = (curPlayMode % 3) +1
    MusicPlayerWidgetLogic.SwitchPlayMode(nextPlayMode)
    self:_RefreshModeIcon()
end

function MusicPlayerListPop:OnSliderValueChanged(value)
    loginfo("[MusicPlayerListPop:OnSliderValueChanged] value = %s", tostring(value))
    if self._bIsSliderCpature then
       self._bSliderChanged = true
       self._seekValue = value
       self._wtProgressSlider:SetPercent(self._seekValue)
    end
end

function MusicPlayerListPop:OnSliderControllerCaptureBegin()
    self._bIsSliderCpature = true
    self._seekValue = Module.MusicPlayer.Field:GetCurMusicPercent()
    self:_OnEnterSlider()
end

function MusicPlayerListPop:OnSliderControllerCaptureEnd()
    if self._bSliderChanged then
        local curMusicEvtName = MusicPlayerWidgetLogic.GetCurMusicEvtName()
        if not curMusicEvtName then
          logerror("[MusicPlayerWidget:OnShowBegin] curMusicEvtName is nil")
          return
        end
        MusicPlayerWidgetLogic.SeekMusicByPercent(curMusicEvtName, self._seekValue)
    end

    self._bIsSliderCpature = false
    self:_OnLeaveSlider()
end

function MusicPlayerListPop:OnSliderControllerHover()
    if not WidgetUtil.IsGamepad() then
        return
    end
    self:OnSliderControllerCaptureBegin()
end

function MusicPlayerListPop:OnSliderControllerUnhover()
    if not WidgetUtil.IsGamepad() then
        return
    end
    self:OnSliderControllerCaptureEnd()
end

function MusicPlayerListPop:_RefreshPlayIcon()
    local curMusicEvtName = MusicPlayerWidgetLogic.GetCurMusicEvtName()
    if not curMusicEvtName then
      logerror("[MusicPlayerWidget:OnShowBegin] curMusicEvtName is nil")
      return
    end
    MusicPlayerWidgetLogic.SetPlayAndPauseIcon(self._wtPlayOrPauseIcon,curMusicEvtName)
end

function MusicPlayerListPop:_RefreshModeIcon()
    self._wtSwitchModeIcon:SetRenderTransformAngle(90)
    local curPlayMode = MusicPlayerWidgetLogic.GetPlayMode()
    if curPlayMode == 1 then
        self._wtCircleText:Visible()
        self._wtSwitchModeIcon:AsyncSetImagePath(Module.MusicPlayer.Config.MusicPlayerIconSoucre["Circle"])
    elseif curPlayMode == 2 then
        self._wtCircleText:Collapsed()
        self._wtSwitchModeIcon:AsyncSetImagePath(Module.MusicPlayer.Config.MusicPlayerIconSoucre["Circle"])
    else
        self._wtCircleText:Collapsed()
        self._wtSwitchModeIcon:AsyncSetImagePath(Module.MusicPlayer.Config.MusicPlayerIconSoucre["Random"])
        self._wtSwitchModeIcon:SetRenderTransformAngle(0)
    end
end

---@param inVolume number 音乐音量[0,100]
function MusicPlayerListPop:_SetMusicVolume(inVolume)
    local volume = inVolume and  math.clamp(inVolume, 0, 100) or 0

    local isHd = IsHD()

    if isHd then
        CommonSettingLogicHD.SetDataByID(MUSIC_VOLUME_ID, volume)
    else
        VolumeSettingLogic.ProcessVolumeMusic(volume)
    end
end

---@return number 音乐音量[0,100]
function MusicPlayerListPop:_GetMusicVolume()
    local isHd = IsHD()
    local ret = 0
    if isHd then
        ret = CommonSettingLogicHD.GetDataByID(MUSIC_VOLUME_ID) or 0 -- 默认音量为100
    else
        local clientVolumeSetting = ClientVolumeSetting and ClientVolumeSetting.Get(GetWorld())
        ret = clientVolumeSetting and  clientVolumeSetting.VolumeMusic or 0
    end
    return ret
end

function MusicPlayerListPop:OnMusicVolumeSliderValueChanged(value)
    if self._bIsMusicVolumeSliderCpature then
        self._bSliderChanged = true
        self._musicVolumeValue = value * 100
        self._wtMusicVolumeProgressSlider:SetPercent(value)
        self._wtOperaMusicVolumeSlider:SetValue(value)
        loginfo("[MusicPlayerListPop:OnMusicVolumeSliderValueChanged] value = %s", tostring(value))
    end
end

function MusicPlayerListPop:OnMusicVolumeSliderControllerCaptureBegin()
    self._bIsMusicVolumeSliderCpature = true
    self._musicVolumeValue = self:_GetMusicVolume()
    self:_OnEnterSlider()
end

function MusicPlayerListPop:OnMusicVolumeSliderControllerCaptureEnd()
    if self._bSliderChanged then
        self:_SetMusicVolume(self._musicVolumeValue)
    end
    self._bIsMusicVolumeSliderCpature = false
    self:_OnLeaveSlider()
end

function MusicPlayerListPop:OnMusicVolumeSliderHover()
    if not WidgetUtil.IsGamepad() then
        return
    end
    self:OnMusicVolumeSliderControllerCaptureBegin()
end

function MusicPlayerListPop:OnMusicVolumeSliderUnhover()
    if not WidgetUtil.IsGamepad() then
        return
    end
    self:OnMusicVolumeSliderControllerCaptureEnd()
end

function MusicPlayerListPop:_EnableGamepadFeature()
    if not IsHD() then
        return
    end
    self._listGroup = WidgetUtil.RegisterNavigationGroup(self._wtWaterfallList, self, "Hittest")
    if self._listGroup then
        self._listGroup:SetScrollRecipient(self._wtWaterfallList)
        self._listGroup:AddNavWidgetToArray(self._wtWaterfallList)
        self._listGroup:MarkIsStackControlGroup()
    end
    self._listGroup:AddNavWidgetToArray(self._wtOperaSlider)
    self._listGroup:AddNavWidgetToArray(self._wtOperaMusicVolumeSlider)
    self._listGroup:AddNavWidgetToArray(self._wtPreviewBtn)
    self._listGroup:AddNavWidgetToArray(self._wtPauseOrPlayBtn)
    self._listGroup:AddNavWidgetToArray(self._wtNextBtn)
    self._listGroup:AddNavWidgetToArray(self._wtSwitchModeBtn)
end

function MusicPlayerListPop:_DisableGamepadFeature()
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    Module.CommonBar:RecoverBottomBarInputSummaryList()
    self._listGroup = nil
    self._NavGroup_OperaSlider = nil
    self._NavGroup_OperaMusicVolumeSlider = nil
    self._NavGroup_OperaMusicVolumeSlider = nil
    self._NavGroup_PreviewBtn = nil
    self._NavGroup_PauseOrPlayBtn = nil
    self._NavGroup_SwitchModeBtn = nil
    self:_OnLeaveSlider()
end

function MusicPlayerListPop:_RefreshBottomTipsBar(inMusicData)
    local isGamePad = WidgetUtil.IsGamepad()
    self._wtCommonPopWin:RemoveSummaries()
    if not isGamePad then
        return
    end
    if not inMusicData then
        logerror("[MusicPlayerListPop:_RefreshBottomTipsBar] inMusicData is nil")
        return
    end
    local AudioEventName = inMusicData.AudioEventName
    local isPlaying = MusicPlayerWidgetLogic.IsPlayingInMusicList(AudioEventName)
    
    if isPlaying then
        self._wtCommonPopWin:AddSummaries({"Pause"})
    else
        self._wtCommonPopWin:AddSummaries({"Play"})
    end

   
end

function MusicPlayerListPop:_OnEnterSlider()
    self._bSliderChanged = false
    if not WidgetUtil.IsGamepad() then
        return
    end
    loginfo("[MusicPlayerListPop:_OnEnterSlider]")
    self:_OnLeaveSlider()
    WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoDpadLR_NoA, self)
    self._LeftSliderCtrlHandle = self:AddAxisInputActionBinding("AudioPlayerSliderCtrl_Left", self._OnLeftSliderMove, self, EDisplayInputActionPriority.UI_Pop)
    self._RightSliderCtrlHandle = self:AddAxisInputActionBinding("AudioPlayerSliderCtrl_Right", self._OnRightSliderMove, self, EDisplayInputActionPriority.UI_Pop)

end

function MusicPlayerListPop:_OnLeaveSlider()
    if not WidgetUtil.IsGamepad() then
        return
    end
    loginfo("[MusicPlayerListPop:_OnLeaveSlider]")
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    if self._LeftSliderCtrlHandle then
        self:RemoveInputActionBinding(self._LeftSliderCtrlHandle)
        self._LeftSliderCtrlHandle = nil
    end

    if self._RightSliderCtrlHandle then
        self:RemoveInputActionBinding(self._RightSliderCtrlHandle)
        self._RightSliderCtrlHandle = nil
    end
end

function MusicPlayerListPop:_OnLeftSliderMove(value)
    loginfo("[MusicPlayerListPop:_OnLeftSliderMove] value = %s", tostring(value))
    if self._bIsSliderCpature then
        local newValue = self._seekValue - value
        newValue = math.clamp(newValue, 0, 1)
        self:OnSliderValueChanged(newValue)
        -- local curMusicEvtName = MusicPlayerWidgetLogic.GetCurMusicEvtName()
        -- MusicPlayerWidgetLogic.SeekMusicByPercent(curMusicEvtName, self._seekValue)
    elseif self._bIsMusicVolumeSliderCpature then
        local newValue = self._musicVolumeValue / 100 - value
        newValue = math.clamp(newValue, 0, 1)
        self:OnMusicVolumeSliderValueChanged(newValue)
        self:_SetMusicVolume(newValue * 100)
    end
end

function MusicPlayerListPop:_OnRightSliderMove(value)
    loginfo("[MusicPlayerListPop:_OnRightSliderMove] value = %s", tostring(value))
    if self._bIsSliderCpature then
        local newValue = self._seekValue + value
        newValue = math.clamp(newValue, 0, 1)
        self:OnSliderValueChanged(newValue)
        -- local curMusicEvtName = MusicPlayerWidgetLogic.GetCurMusicEvtName()
        -- MusicPlayerWidgetLogic.SeekMusicByPercent(curMusicEvtName, self._seekValue)
    elseif self._bIsMusicVolumeSliderCpature then
        local newValue = self._musicVolumeValue / 100 + value
        newValue = math.clamp(newValue, 0, 1)
        self:OnMusicVolumeSliderValueChanged(newValue)
        self:_SetMusicVolume(newValue * 100)
    end
end

return MusicPlayerListPop