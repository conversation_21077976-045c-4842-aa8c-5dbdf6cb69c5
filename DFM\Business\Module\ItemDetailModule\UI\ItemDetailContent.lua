----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMItemDetail)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class ItemDetailContent : LuaUIBaseView
local ItemDetailContent = ui("ItemDetailContent")

local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local WeaponAttributeDisplayItem = require("DFM.Business.Module.ItemDetailModule.UI.WeaponAttributeDisplayItem")
local ItemDetailConfig = require("DFM.Business.Module.ItemDetailModule.ItemDetailConfig")
local UWidgetLayoutLibrary = import "WidgetLayoutLibrary"
local UAssembleWeaponDataLibrary = import "AssembleWeaponDataLibrary"
local EModularPartNodeType = import "EModularPartNodeType"
local UDFMGameHudDelegates = import("DFMGameHudDelegates")

local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local AirbrushView = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.AirbrushView"
local ItemDetailLogic = require "DFM.Business.Module.ItemDetailModule.ItemDetailLogic"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local ShopHelperTool = require "DFM.StandaloneLua.BusinessTool.ShopHelperTool"
local JumpConfig = require "DFM.Business.Module.JumpModule.JumpConfig"
local EDescendantScrollDestination = import "EDescendantScrollDestination"
local ShopTable = Facade.TableManager:GetTable("Trader")
local FSlateChildSize = import "SlateChildSize"
local ESlateSizeRule = import "ESlateSizeRule"
local VehicleHelperTool = require "DFM.StandaloneLua.BusinessTool.VehicleHelperTool"
local FAnchors = import "Anchors"
local UGPInputHelper = import("GPInputHelper")
local EGPInputType = import "EGPInputType"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"

local function xxww(...)
    loginfo("[xxww] ", ...)
end

function ItemDetailContent:Ctor()
    self._wtRealContentSlot = self:Wnd("_wRealContentSlot", UIWidgetBase)
    -- Scroll
    if self._wtContentSlotTop then
        self._wtContentSlotTop:Collapsed()
    end

    self._wtDesc = self:Wnd("wDesc", UITextBlock)
    -- self._wtSource = self:Wnd("wSource", UIWidgetBase)
    -- self._wtUsePlace = self:Wnd("wUsePlace", UIWidgetBase)
    -- if self._wtSource then
        -- self._wtSourceWrap = self._wtSource:Wnd("_wWrapBox", UIWidgetBase)
        -- self._wtShowSourceDescription = self._wtSource:Wnd("wGiftRandomDetailBtn", DFCommonCheckButtonOnly)
        -- self._wtShowSourceDescription:Event("OnCheckedBoxStateChangedNative", self.OnCheckSourceBoxClicked, self)
    -- end
    -- if self._wtUsePlace then
        -- self._wtUsePlaceWrap = self._wtUsePlace:Wnd("_wWrapBox", UIWidgetBase)
        -- self._wtShowUsePlaceDescription = self._wtUsePlace:Wnd("wGiftRandomDetailBtn", DFCommonCheckButtonOnly)
        -- self._wtShowUsePlaceDescription:Event("OnCheckedBoxStateChangedNative", self.OnCheckUsePlaceBoxClicked, self)
    -- end
    self._wtUsePlaceInGame = self:Wnd("_wtUsePlaceInGame", UIWidgetBase)
    if self._wtUsePlaceInGame then
        self._wtUsePlaceTextInGame = self._wtUsePlaceInGame:Wnd("_wtUsePlaceText", UITextBlock)
    end

    self._wtBtnPanel = self:Wnd("WBP_ItemDetailPanelBtn", UIWidgetBase)
    self._wtBtnWrapBox = self._wtBtnPanel:Wnd("DFWrapBox_91", UIWidgetBase)
    self._wtBtnLine = self:Wnd("Line_20", UIImage)
    Facade.UIManager:RemoveSubUIByParent(self, self._wtBtnWrapBox)

    self._wtBtnPanel2 = self:Wnd("WBP_ItemDetailPanelBtn_82", UIWidgetBase)
    if self._wtBtnPanel2 then
        self._wtBtnWrapBox2 = self._wtBtnPanel2:Wnd("DFWrapBox_91", UIWidgetBase)
        Facade.UIManager:RemoveSubUIByParent(self, self._wtBtnWrapBox2)
    end

    self._wtVerticalBox = self:Wnd("DFVerticalBox_0", UIWidgetBase)

    ---@type ItemBase
    self._itemInfo = nil
    self._asyncCreateUIBatchId = {}
    self._asyncCreateUIInst = 0
    self._asyncCreateBtnNum = 0
    self._weaponAttrOffset = nil
    self._weaponAttrAlignment = FVector2D(0, 0)

    self._fUnEquipWeaponBulletCallback = nil
    self._bCanDragAdapter = false
    -- 默认icon不显示时调整按钮位置
    self._bAdjustBtnPosWhenIconHide = true
    self._bAdapterCanFastUnequip = false
    self._bAdapterTipsShowBtton = false
    self._weaponShowState = ItemDetailConfig.ETipsWeaponShowState.Auto

    -- self._extraContentWidgets = setmetatable({}, weakmeta_value)

    self._allSourceInfo = {}
    self._allUsePlaceInfo = {}
    self._allAdapter = {}

    self._sourceBtnMap = {}
    self._afterSourceBtnLoadCallback = nil

    self._curSubContentWeakUiIns = nil
    self._lastSubContentUIName = nil
    self._funcAfterContentFinish = {}

    self._onContentScrollCallback = nil
    self._bigTipsAlignWidget = nil --	带关闭按钮的tips位置停靠的widget，一般是弹窗详情页的标题
    self._bSourceVisible = true
    self._bUsePlaceVisible = true
end

function ItemDetailContent:OnOpen()
    --self:_AddEventListener()
end

function ItemDetailContent:OnClose()
    --self:_RemoveEventListener()
    Facade.UIManager:ClearSubUIByParent(self, self._wtRealContentSlot)
    Facade.UIManager:ClearSubUIByParent(self, self._wtBtnWrapBox)
    if self._wtBtnWrapBox2 then
        Facade.UIManager:ClearSubUIByParent(self, self._wtBtnWrapBox2)
    end
    Facade.UIManager:ClearSubUIByParent(self, self._wContentSlotTop)

    self._ItemDetaiLView = nil
end

function ItemDetailContent:OnShow()
    self:_AddEventListener()
end

function ItemDetailContent:OnHide()
    self:_RemoveEventListener()
end

------------------------------------ Event listener function ------------------------------------
----- 暂时废弃的接口

function ItemDetailContent:_OnProtectedPartBtnClicked()
    Facade.UIManager:AsyncShowUI(UIName2ID.ItemProtectionBodyPanel, nil, nil, self._itemInfo)
end

function ItemDetailContent:_OnContainerPerviewBtnClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.ContainerPreviewPanel, nil, nil, self._itemInfo)
end

function ItemDetailContent:_OnBuffBtnClick()
    local healthFeature = self._itemInfo:GetFeature(EFeatureType.Health)
    if healthFeature then
        Facade.UIManager:AsyncShowUI(UIName2ID.ItemBuffDescPanel, nil, nil, healthFeature.addBuffs)
    end
end

-- 装备配件
function ItemDetailContent:_SetItemComponentsList(compParentWidget, compWidget)
    if self._itemInfo.components and #(self._itemInfo.components) > 0 then
        compParentWidget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        Facade.UIManager:RemoveSubUIByParent(compParentWidget, compWidget)
        for _, componentData in ipairs(self._itemInfo.components) do
            local info = componentData.prop_data
            local componentItem = ItemBase:New(info.id, info.num, info.gid)
            componentItem:SetRawPropInfo(info)
            local function OnCreateItemViewFinished(itemView)
                itemView:SetSize()
            end
            Module.Inventory:CreateItemView(componentItem, false, OnCreateItemViewFinished, compParentWidget, compWidget)
        end
    else
        compParentWidget:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function ItemDetailContent:_CreatrCollectableUsageByName(name)
    --Facade.UIManager:AsyncCreateSubUI(UIName2ID.ItemDetailCollectableUsage,
    --function(subUI)
    --	self._wtUsageWrapBox:AddChild(subUI)
    --	subUI:SetText(name)
    --	self._asyncCreateUIInst = self._asyncCreateUIInst - 1
    --end)
end
----- 暂时废弃的接口 end
function ItemDetailContent:_OnItemRepaireSuccess(itemInfo)
    --[[if itemInfo and itemInfo.instanceId == self._itemInfo.instanceId then
		self:_SetWeaponDetail()
	end]]
end

function ItemDetailContent:_OnItemMove(itemMoveCmd)
    if self._itemInfo == nil then
        return
    end
    local item = itemMoveCmd.item
    if item and item.gid == self._itemInfo.gid then
        -- self:SetItem(item, nil, true, true)
        -- 性能优化，不刷UI只刷数据
        local uiIns = getfromweak(self._curSubContentWeakUiIns)
        if uiIns and uiIns.SetItem then
            uiIns:SetItem(item)
            ItemDetailConfig.evtItemDetailPanelNoBtn:Invoke(self)
        end
    end
end

-- function ItemDetailContent:_OnScrollBoxMove(widget)
--     self._wtScrollBoxContent:ScrollWidgetIntoView(widget, true, EDescendantScrollDestination.TopOrLeft)
-- end

------------------------------------ Private function ------------------------------------
function ItemDetailContent:_AddEventListener()
    -- self:AddLuaEvent(Server.InventoryServer.Events.evtItemRepaireSuccess, self._OnItemRepaireSuccess, self)
    self:AddLuaEvent(Server.InventoryServer.Events.evtItemMove, self._OnItemMove, self)
    self:AddLuaEvent(Server.LootingServer.Events.evtLootingItemMove, self._OnItemMove, self)
    -- self:AddLuaEvent(Module.ItemDetail.Config.evtShowWeaponPrepare, self._OnScrollBoxMove, self)
    self:AddLuaEvent(Module.ItemDetail.Config.evtOnSellOrSplitOpenClicked, self._OnSellOrSplitOpenClicked, self)
    self:AddLuaEvent(Module.ItemDetail.Config.evtOnSellOrSplitCloseClicked, self._OnSellOrSplitCloseClicked, self)
end

function ItemDetailContent:_RemoveEventListener()
    self:RemoveAllLuaEvent()
end

function ItemDetailContent:_CommonSetContentSubUI(uiId, setFinishCallback, bIsNoToStart)
    bIsNoToStart = setdefault(bIsNoToStart, false)

    self._wtRealContentSlot:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	local function fLoadFinLogic(bManual)
        if not bManual then
            self:SubAsyncFrameUINum()
        end
        Facade.UIManager:RemoveSubUIByParent(self, self._wtRealContentSlot)
		local weakUiIns = Facade.UIManager:AddSubUI(self, uiId, self._wtRealContentSlot, nil)
        local uiIns = getfromweak(weakUiIns)
		if uiIns then

            if self._wtVerticalBox and self._wtVerticalBox.InvalidateLayoutAndVolatility then
                self._wtVerticalBox:InvalidateLayoutAndVolatility()
            else
                logerror('self._wtVerticalBox error ', debug.traceback())
            end

            if uiIns.Reset then
                uiIns:Reset()
            end

            -- 父节点必须是canvasPanel，不然会触发嵌套子ui的close
            -- 需要主动设置一下容器的layout数据
            local slot = UWidgetLayoutLibrary.SlotAsCanvasSlot(uiIns)
            if slot then
                local commonAnchor = FAnchors()
                commonAnchor.Minimum = FVector2D(0, 0)
                commonAnchor.Maximum = FVector2D(1, 0)
                slot:SetOffsets(FMargin(0, 0, 0, 0))
                slot:SetAnchors(commonAnchor)
                slot:SetAutoSize(true)
            end

			self._asyncCreateUIInst = self._asyncCreateUIInst - 1
			self._curSubContentWeakUiIns = weakUiIns
            -- if not self:IsVisible() then
			--     logerror("ItemDetailContent:_CommonSetContentSubUI:UIManager:not self:IsVisible()!")
            --     return
            -- end
            if not bIsNoToStart then
                if self._ItemDetaiLView and self._ItemDetaiLView._wtScrollBoxContent and self._ItemDetaiLView._wtScrollBoxContent.ScrollToStart then
			        self._ItemDetaiLView._wtScrollBoxContent:ScrollToStart()
                end
            end
			if setFinishCallback then
				setFinishCallback(uiIns)
			end
		else
			logerror("ItemDetailContent:_CommonSetContentSubUI:UIManager:AddSubUI: weakUiIns is nil!")
		end
    end

	local function fLoadFinCallback(mapPath2ResIns)
       fLoadFinLogic()
	end

    self._asyncCreateUIInst = self._asyncCreateUIInst + 1
	if Facade.UIManager:CheckUIHasBeenLoaded(uiId) then
       fLoadFinLogic(true)
	else
        self:AddAsyncFrameUINum()
		local uiBatchId = Facade.UIManager:AsyncLoadUIResOnly(uiId, fLoadFinCallback, nil)
		table.insert(self._asyncCreateUIBatchId, uiBatchId)
	end
end

function ItemDetailContent:_SetWeaponDetail(bIsNoToStart)
    bIsNoToStart = setdefault(bIsNoToStart, false)
    local function OnCreateSubUIFinished(itemIns)
        local exArgs = {}
        exArgs.bCanDragAdapter = self._bCanDragAdapter
        exArgs.bAdapterCanFastUnequip = self._bAdapterCanFastUnequip
        exArgs.bAdapterTipsShowBtton = self._bAdapterTipsShowBtton
        exArgs.weaponShowState = self._weaponShowState
        exArgs.fUnEquipWeaponBulletCallback = self._fUnEquipWeaponBulletCallback
        itemIns:SetItem(self._itemInfo, exArgs)
        --itemIns:SetBigTipsAlignWidget(self._bigTipsAlignWidget)
        self:_AfterLoadContentUI()
        if itemIns.bShowBtn then
            itemIns.btnTypeCfg.btnGroupType = ItemDetailConfig.EBtnGroupType.Item
            self:AddBtnType(itemIns.btnTypeCfg)
        end
    end
    self:_CommonSetContentSubUI(UIName2ID.ItemDetailContentWeapon, OnCreateSubUIFinished, bIsNoToStart)
end

function ItemDetailContent:_SetBulletDetail()
    local function OnCreateSubUIFinished(itemIns)
        local exArgs = {}
        itemIns:SetItem(self._itemInfo, exArgs)
        self:_AfterLoadContentUI()
    end
    self:_CommonSetContentSubUI(UIName2ID.ItemDetailContentBullet, OnCreateSubUIFinished)
end

function ItemDetailContent:_AfterLoadContentUI()
    if self._asyncCreateUIInst > 0 then
        return
    end
    for _, delayFuncInfo in ipairs(self._funcAfterContentFinish) do
        delayFuncInfo.func(self, unpack(delayFuncInfo.args))
    end
    self._funcAfterContentFinish = {}
end

function ItemDetailContent:_SetAirbrushDetail()
    local function OnCreateSubUIFinished(itemIns)
        local exArgs = {}
        itemIns:SetItem(self._itemInfo, exArgs)
        self:_AfterLoadContentUI()
    end
    self:_CommonSetContentSubUI(UIName2ID.ItemDetailContentAirbrush, OnCreateSubUIFinished)
end

function ItemDetailContent:_SetMedicineAndFoodDetail()
    local function OnCreateSubUIFinished(itemIns)
        local exArgs = {}
        itemIns:SetItem(self._itemInfo, exArgs)
        self:_AfterLoadContentUI()
    end
    self:_CommonSetContentSubUI(UIName2ID.ItemDetailContentMedicineAndFood, OnCreateSubUIFinished)
end

function ItemDetailContent:_SetContainerDetail()
    local function OnCreateSubUIFinished(itemIns)
        -- local exArgs = {}
        -- exArgs.isContainer = true
        itemIns:SetItem(self._itemInfo, exArgs)
        self:_AfterLoadContentUI()
    end
    local bInGame = not Facade.GameFlowManager:CheckIsInFrontEnd()
    self:_CommonSetContentSubUI(
        bInGame and UIName2ID.ItemDetailContentContainerInGame or UIName2ID.ItemDetailContentContainer,
        OnCreateSubUIFinished
    )
end

function ItemDetailContent:_SetBagAndChestDetail()
    local function OnCreateSubUIFinished(itemIns)
        -- local exArgs = {}
        -- exArgs.isContainer = true
        itemIns:SetItem(self._itemInfo, exArgs)
        self:_AfterLoadContentUI()
    end
    local bInGame = not Facade.GameFlowManager:CheckIsInFrontEnd()
    self:_CommonSetContentSubUI(UIName2ID.ItemDetailContentBagAndChest, OnCreateSubUIFinished)
end

function ItemDetailContent:_SetDepotDetailNew()
    local function OnCreateSubUIFinished(itemIns)
        -- local exArgs = {}
        -- exArgs.isContainer = false
        itemIns:SetItem(self._itemInfo, exArgs)
        self:_AfterLoadContentUI()
    end
    self:_CommonSetContentSubUI(UIName2ID.ItemDetailContentContainer, OnCreateSubUIFinished)
end

-- 护甲
function ItemDetailContent:_SetArmorDetail()
    local function OnCreateSubUIFinished(itemIns)
        local exArgs = {}
        itemIns:SetItem(self._itemInfo, exArgs)
        self:_AfterLoadContentUI()
    end
    self:_CommonSetContentSubUI(UIName2ID.ItemDetailContentArmor, OnCreateSubUIFinished)
end

function ItemDetailContent:_SetHelmetDetail()
    local function OnCreateSubUIFinished(itemIns)
        local exArgs = {}
        itemIns:SetItem(self._itemInfo, exArgs)
        self:_AfterLoadContentUI()
    end
    self:_CommonSetContentSubUI(UIName2ID.ItemDetailContentHelmet, OnCreateSubUIFinished)
end

function ItemDetailContent:_SetAdapterDetail()
    local function OnCreateSubUIFinished(itemIns)
        local exArgs = {}
        exArgs.bCanDragAdapter = self._bCanDragAdapter
        exArgs.bAdapterCanFastUnequip = self._bAdapterCanFastUnequip
        exArgs.bAdapterTipsShowBtton = self._bAdapterTipsShowBtton
        itemIns:SetItem(self._itemInfo, exArgs)
        self:_AfterLoadContentUI()
    end
    self:_CommonSetContentSubUI(UIName2ID.ItemDetailContentAdapter, OnCreateSubUIFinished)
end

function ItemDetailContent:_GetKeyInfoData(itemId)
    if Module.ItemDetail.Config.KeyInfoConfig == nil then
        Module.CommonTips:ShowSimpleTip(Module.ItemDetail.Config.Loc.errKeyInfoConfigFail)
        return
    end
    -- 用item对应的id找到钥匙对应的地图和位置
    local keyInfoData = Module.ItemDetail.Config.KeyInfoConfig[tostring(itemId)]
    if keyInfoData then
        return keyInfoData
    else
        -- Module.CommonTips:ShowSimpleTip(string.format(Module.ItemDetail.Config.Loc.errKeyInfoConfigNotFound, itemId))
    end
end

function ItemDetailContent:_SetKeyDetail()
    local function OnCreateSubUIFinished(itemIns)
        local exArgs = {}
        itemIns:SetItem(self._itemInfo, exArgs)
        self:_AfterLoadContentUI()
        if itemIns.bShowBtn then
            itemIns.btnTypeCfg.btnGroupType = ItemDetailConfig.EBtnGroupType.Item
            self:AddBtnType(itemIns.btnTypeCfg)
        end
    end
    self:_CommonSetContentSubUI(UIName2ID.ItemDetailContentKey, OnCreateSubUIFinished)
end

function ItemDetailContent:_SetGiftDetail()
    local function OnCreateSubUIFinished(itemIns)
        local exArgs = {detailContent = self}
        itemIns:SetItem(self._itemInfo, exArgs)
        self:_AfterLoadContentUI()
    end
    self:_CommonSetContentSubUI(UIName2ID.ItemDetailContentGift, OnCreateSubUIFinished)
end

function ItemDetailContent:_SetCollectableDetail()
    local function OnCreateSubUIFinished(itemIns)
        local exArgs = {}
        itemIns:SetItem(self._itemInfo, exArgs)
        self:_AfterLoadContentUI()
    end
    self:_CommonSetContentSubUI(UIName2ID.ItemDetailContentCollectable, OnCreateSubUIFinished)
end

function ItemDetailContent:_SetKeyCaseDetail()
    local function OnCreateSubUIFinished(itemIns)
        local exArgs = {}
        itemIns:SetItem(self._itemInfo, exArgs)
        self:_AfterLoadContentUI()
        if itemIns.bShowBtn then
            itemIns.btnTypeCfg.btnGroupType = ItemDetailConfig.EBtnGroupType.Item
            self:AddBtnType(itemIns.btnTypeCfg)
        end
    end
    self:_CommonSetContentSubUI(UIName2ID.ItemDetailContentKeyCase, OnCreateSubUIFinished)
end

function ItemDetailContent:_SetKeyCaseUnlockDetail()
    local function OnCreateSubUIFinished(itemIns)
        local exArgs = {}
        itemIns:SetItem(self._itemInfo, exArgs)
        self:_AfterLoadContentUI()
    end
    self:_CommonSetContentSubUI(UIName2ID.ItemDetailContentKeyCaseUnlock, OnCreateSubUIFinished)
end

function ItemDetailContent:_SetArmedForceItemDetail()
    local function OnCreateSubUIFinished(itemIns)
        local exArgs = {}
        itemIns:SetItem(self._itemInfo, exArgs)
        self:_AfterLoadContentUI()
    end
    self:_CommonSetContentSubUI(UIName2ID.ItemDetailContentArmedForceItem, OnCreateSubUIFinished)
end


function ItemDetailContent:_SetCommercializeDetail()
    local function OnCreateSubUIFinished(itemIns)
        local _, typeText = ItemDetailLogic.IsCommercializeItem(self._itemInfo)
        local exArgs = {typeText = typeText}
        itemIns:SetItem(self._itemInfo, exArgs)
        self:_AfterLoadContentUI()
    end
    self:_CommonSetContentSubUI(UIName2ID.ItemDetailContentCommercialize, OnCreateSubUIFinished)
end

function ItemDetailContent:_SetOrnamentsDetail()
    local function OnCreateSubUIFinished(itemIns)
        local exArgs = {}
        itemIns:SetItem(self._itemInfo, exArgs)
        self:_AfterLoadContentUI()
        --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
        if IsHD() then
            self._wtContentOrnaments = itemIns
        end
        --- END MODIFICATION
    end
    self:_CommonSetContentSubUI(UIName2ID.ItemDetailContentOrnaments, OnCreateSubUIFinished)
end

function ItemDetailContent:_SetOtherDetail()
    local function OnCreateSubUIFinished(itemIns)
        local exArgs = {}
        itemIns:SetItem(self._itemInfo, exArgs)
        self:_AfterLoadContentUI()
    end
    self:_CommonSetContentSubUI(UIName2ID.ItemDetailContentOther, OnCreateSubUIFinished)
end

function ItemDetailContent:_SetMeleeWeaponDetail()
	local function OnCreateSubUIFinished(itemIns)
		itemIns:SetItem(self._itemInfo)
		self:_AfterLoadContentUI()
	end
	self:_CommonSetContentSubUI(UIName2ID.ItemDetailContentMeleesWeapon, OnCreateSubUIFinished)
end

function ItemDetailContent:_SetCollectionDetail()
    local function OnCreateSubUIFinished(itemIns)
        local exArgs = {}
        itemIns:SetItem(self._itemInfo, exArgs)
        self:_AfterLoadContentUI()
    end
    self:_CommonSetContentSubUI(UIName2ID.ItemdetailContentCollection, OnCreateSubUIFinished)
end

function ItemDetailContent:_SetBluePrintCollectionDetail()
    -- 蓝图枪详情页
    local function OnCreateSubUIFinished(itemIns)
        local exArgs = {}
        itemIns:SetItem(self._itemInfo, exArgs)
        self:_AfterLoadContentUI()
    end
    self:_CommonSetContentSubUI(UIName2ID.ItemdetailContentBluePrintWeapon, OnCreateSubUIFinished)
end

function ItemDetailContent:_SetWeaponMysticalSkinDetail()
    local function OnCreateSubUIFinished(itemIns)
		local exArgs = {}
		itemIns:SetItem(self._itemInfo, exArgs)
		self:_AfterLoadContentUI()
        --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
        if IsHD() then
            self._wtContentWeaponMysticalSkin = itemIns
        end
        --- END MODIFICATION
	end
	self:_CommonSetContentSubUI(UIName2ID.ItemDetailContentWeaponMysticalSkin, OnCreateSubUIFinished)
end

function ItemDetailContent:_SetWeaponSkinDetail()
    local function OnCreateSubUIFinished(itemIns)
		local exArgs = {}
		itemIns:SetItem(self._itemInfo, exArgs)
		self:_AfterLoadContentUI()
	end
	self:_CommonSetContentSubUI(UIName2ID.ItemDetailContentWeaponSkin, OnCreateSubUIFinished)
end

function ItemDetailContent:_SetItemDesc()
    local outStr = ItemDetailLogic.GetItemDesc(self._itemInfo)
    self._wtDesc:SetText(outStr)
end

function ItemDetailContent:_InitSourceAndUsePlace()
    self._allSourceInfo = {}
    self._allUsePlaceInfo = {}
    ItemDetailLogic.InitSource(self._itemInfo, self._allSourceInfo)
    ItemDetailLogic.InitUsePlace(self._itemInfo, self._allUsePlaceInfo)
end

function ItemDetailContent:_RefreshSource()
    self._sourceBtnMap = {}
    if Facade.GameFlowManager:CheckIsInFrontEnd() then
        -- self._wtSource:SetVisibility(
        --     self._bSourceVisible and ESlateVisibility.SelfHitTestInvisible or ESlateVisibility.Collapsed
        -- )
        -- self._wtSourceWrap:ClearChildren()

        local bHaveSource = false
        local subTitle = ItemDetailConfig.Loc.titleSource
        -- for _, sourceInfo in ipairs(self._allSourceInfo) do
        -- 	if #sourceInfo.dataList > 0 then
        -- 		local clickBtnCallback = function()
        -- 			LogAnalysisTool.SetCurItemDetailItemId(self._itemInfo.id)
        -- 			LogAnalysisTool.DoSendOpenItemSourceUILog(sourceInfo.sourceType)

        -- 			-- 来源按钮数据上报
        -- 			self:_SendToSafeHouse(sourceInfo.sourceType, Module.ItemDetail.Field:GetSafeHouseScene())
        -- 		end

        -- 		bHaveSource = true
        -- 		self._asyncCreateUIInst = self._asyncCreateUIInst + 1
        -- 		local name = ItemDetailConfig.SourceText[sourceInfo.sourceType]
        -- 		local tipsAlignWidget = self._bigTipsAlignWidget or self._wtSource

        -- 		local function OnCreateSubUIFinished(uiIns)
        -- 			self._sourceBtnMap[sourceInfo.sourceType] = uiIns
        -- 			self._wtSourceWrap:AddChild(uiIns)
        -- 			local horizontalSlot = UWidgetLayoutLibrary.SlotAsHorizontalBoxSlot(uiIns)
        -- 			horizontalSlot:SetSize(FSlateChildSize(ESlateSizeRule.Fill))
        -- 			-- uiIns:SetOpenCallback(clickBtnCallback)
        -- 			uiIns:SetMainTitle(name)
        -- 			uiIns:ShowIcon(false)
        -- 			uiIns:SetSize(2)

        -- 			local function OnCheckBoxClicked()
        -- 				local handle = Module.CommonTips:ShowDescriptionWindow(uiIns, name, nil, sourceInfo.dataList, tipsAlignWidget, subTitle)
        -- 				if handle == nil then
        -- 					return
        -- 				end
        -- 				safecall(clickBtnCallback())
        -- 			end
        -- 			uiIns:Event("OnCheckedBoxStateChangedNative", OnCheckBoxClicked, self)

        -- 			self._asyncCreateUIInst = self._asyncCreateUIInst - 1
        -- 			if self._asyncCreateUIInst <= 0 then
        -- 				self:_AfterLoadContentUI()
        -- 			end
        -- 			if self._afterSourceBtnLoadCallback then
        -- 				self._afterSourceBtnLoadCallback()
        -- 			end
        -- 		end
        -- 		-- local uiHandle = Facade.UIManager:AsyncCreateSubUI(UIName2ID.ItemDetailDescBtn, OnCreateSubUIFinished, nil, sourceInfo.dataList, name, subTitle, tipsAlignWidget)
        -- 		local uiHandle = Facade.UIManager:AsyncCreateSubUI(UIName2ID.DFCommonCheckButtonV3S1, OnCreateSubUIFinished, nil)
        -- 		table.insert(self._asyncCreateUIBatchId, uiHandle)
        -- 	end
        -- end

        self._sourceDataList = {}
        local sourceType = {}
        -- 根据 datalist 判断是否有来源途径
        for _, sourceInfo in ipairs(self._allSourceInfo) do
            if #sourceInfo.dataList > 0 then
                table.insert(sourceType, sourceInfo.sourceType)
                for _, datalist in pairs(sourceInfo.dataList) do
                    table.insert(self._sourceDataList, datalist)
                end
            end
        end

        if #self._sourceDataList > 0 then
            table.sort(self._sourceDataList, ItemDetailContent.FuncSortWeight)
            bHaveSource = true
        end
        local sourceTypeList = {}
        for _, sourceType in pairs(sourceType) do
            local sourceTypeName = ItemDetailConfig.SourceText[sourceType]
            table.insert(sourceTypeList, sourceTypeName)
        end
        -- self._wtSource:SetItemText(sourceTypeList)

        if bHaveSource then
            -- self._wtSource:SetVisibility(
                -- self._bSourceVisible and ESlateVisibility.SelfHitTestInvisible or ESlateVisibility.Collapsed
            -- )
            if self._bSourceVisible then
                local btnType = {
                    fOnClickWithSelf = CreateCallBack(self.OnCheckSourceBoxClicked, self),
                    txt = ItemDetailConfig.Loc.titleSource,
                    pcRightIcon = ItemDetailConfig.pcRightIconList.Source,
                    btnGroupType = ItemDetailConfig.EBtnGroupType.SourceAndUsePlace,
                    btnType = ItemDetailConfig.ItemBtnKey.InnerSource,
                }
                self:AddBtnType(btnType)
            end
        else
            -- self._wtSource:SetVisibility(ESlateVisibility.Collapsed)
        end
        -- self._wtSource:SetVisibility(ESlateVisibility.Collapsed)
    else
        -- if self._wtSource then
            -- self._wtSource:SetVisibility(ESlateVisibility.Collapsed)
        -- end
    end
end

function ItemDetailContent:OnCheckSourceBoxClicked()
    local tipsAlignWidget = self._bigTipsAlignWidget or self
    local title = ItemDetailConfig.Loc.titleSource
    local loadFinCall = nil
    if Facade.GameFlowManager:GetCurrentGameFlow() ~= EGameFlowStageType.Game then
        loadFinCall = function (uiIns)
            local uiController = Facade.UIManager:GetLayerControllerByType((EUILayer.Pop))
            if uiController then
                uiController:SetUIToLastPos(uiIns)
            end
        end
    end
    local handle =
        Module.CommonTips:ShowDescriptionWindow(
        self,
        title,
        nil,
        self._sourceDataList,
        tipsAlignWidget,
        nil,
        nil,
        loadFinCall
    )

    if handle == nil then
        return
    end
end

function ItemDetailContent:_RefreshUsePlace()
    -- 安全箱不显示用途
    local itemFeature = self._itemInfo:GetFeature()
    local featureType = itemFeature:GetFeatureType()
    if featureType == EFeatureType.Equipment and itemFeature:IsExtendItem() then
        -- if self._wtUsePlace then
            -- self._wtUsePlace:SetVisibility(ESlateVisibility.Collapsed)
        -- end
        if self._wtUsePlaceInGame then
            self._wtUsePlaceInGame:SetVisibility(ESlateVisibility.Collapsed)
        end
        return
    end

    if Facade.GameFlowManager:CheckIsInFrontEnd() then
        -- if self._wtUsePlace then
            -- self._wtUsePlace:SetVisibility(
                -- self._bUsePlaceVisible and ESlateVisibility.SelfHitTestInvisible or ESlateVisibility.Collapsed
            -- )
        -- end
        if self._wtUsePlaceInGame then
            self._wtUsePlaceInGame:SetVisibility(ESlateVisibility.Collapsed)
        end
        -- self._wtUsePlaceWrap:ClearChildren()

        local bHaveUsePlace = false
        local subTitle = ItemDetailConfig.Loc.titleUsePlace
        -- for _, usePlaceInfo in ipairs(self._allUsePlaceInfo) do
        -- 	if #usePlaceInfo.dataList > 0 then
        -- 		local clickBtnCallback = function()
        -- 			LogAnalysisTool.SetCurItemDetailItemId(self._itemInfo.id)
        -- 			LogAnalysisTool.DoSendOpenItemUsePlaceUILog(usePlaceInfo.sourceType)
        -- 			self:_SendToSafeHouse(usePlaceInfo.sourceType, Module.ItemDetail.Field:GetSafeHouseScene())
        -- 		end
        -- 		-- local function OnCreateSubUIFinished(uiIns)
        -- 		-- 	self._wtUsePlaceWrap:AddChild(uiIns)
        -- 		-- 	uiIns:SetOpenCallback(clickBtnCallback)
        -- 		-- 	self._asyncCreateUIInst = self._asyncCreateUIInst - 1
        -- 		-- 	if self._asyncCreateUIInst <= 0 then
        -- 		-- 		self:_AfterLoadContentUI()
        -- 		-- 	end
        -- 		-- end
        -- 		bHaveUsePlace = true
        -- 		self._asyncCreateUIInst = self._asyncCreateUIInst + 1
        -- 		local name = ItemDetailConfig.UsePlaceText[usePlaceInfo.sourceType]
        -- 		local tipsAlignWidget = self._bigTipsAlignWidget or self._wtUsePlace

        -- 		local function OnCreateSubUIFinished(uiIns)
        -- 			-- self._sourceBtnMap[sourceInfo.sourceType] = uiIns
        -- 			self._wtUsePlaceWrap:AddChild(uiIns)
        -- 			local horizontalSlot = UWidgetLayoutLibrary.SlotAsHorizontalBoxSlot(uiIns)
        -- 			horizontalSlot:SetSize(FSlateChildSize(ESlateSizeRule.Fill))
        -- 			-- uiIns:SetOpenCallback(clickBtnCallback)
        -- 			uiIns:SetMainTitle(name)
        -- 			uiIns:ShowIcon(false)
        -- 			uiIns:SetSize(2)

        -- 			local function OnCheckBoxClicked()
        -- 				local handle = Module.CommonTips:ShowDescriptionWindow(uiIns, name, nil, usePlaceInfo.dataList, tipsAlignWidget, subTitle)
        -- 				if handle == nil then
        -- 					return
        -- 				end
        -- 				safecall(clickBtnCallback())
        -- 			end
        -- 			uiIns:Event("OnCheckedBoxStateChangedNative", OnCheckBoxClicked, self)

        -- 			self._asyncCreateUIInst = self._asyncCreateUIInst - 1
        -- 			if self._asyncCreateUIInst <= 0 then
        -- 				self:_AfterLoadContentUI()
        -- 			end
        -- 		end
        -- 		-- local uiHandle = Facade.UIManager:AsyncCreateSubUI(UIName2ID.ItemDetailDescBtn, OnCreateSubUIFinished, nil, usePlaceInfo.dataList, name, subTitle, tipsAlignWidget)
        -- 		local uiHandle = Facade.UIManager:AsyncCreateSubUI(UIName2ID.DFCommonCheckButtonV3S1, OnCreateSubUIFinished, nil)
        -- 		table.insert(self._asyncCreateUIBatchId, uiHandle)
        -- 	end
        -- end

        self._usePlaceDataList = {}
        local sourceType = {}
        -- 根据 datalist 判断是否有来源途径
        for _, sourceInfo in ipairs(self._allUsePlaceInfo) do
            if #sourceInfo.dataList > 0 then
                table.insert(sourceType, sourceInfo.sourceType)
                for _, datalist in pairs(sourceInfo.dataList) do
                    table.insert(self._usePlaceDataList, datalist)
                end
            end
        end

        if #self._usePlaceDataList > 0 then
            table.sort(self._usePlaceDataList, ItemDetailContent.FuncSortWeight)
            bHaveUsePlace = true
        end
        local sourceTypeList = {}
        for _, sourceType in pairs(sourceType) do
            local sourceTypeName = ItemDetailConfig.UsePlaceText[sourceType]
            table.insert(sourceTypeList, sourceTypeName)
        end
        -- self._wtUsePlace:SetItemText(sourceTypeList)

        if bHaveUsePlace then
            -- self._wtUsePlace:SetVisibility(
            --     self._bUsePlaceVisible and ESlateVisibility.SelfHitTestInvisible or ESlateVisibility.Collapsed
            -- )
            if self._bUsePlaceVisible then
                local btnType = {
                    fOnClickWithSelf = CreateCallBack(self.OnCheckUsePlaceBoxClicked, self),
                    txt = ItemDetailConfig.Loc.titleUsePlace,
                    pcRightIcon = ItemDetailConfig.pcRightIconList.Usage,
                    btnGroupType = ItemDetailConfig.EBtnGroupType.SourceAndUsePlace,
                    btnType = ItemDetailConfig.ItemBtnKey.InnerUsePlace,
                }
                self:AddBtnType(btnType)
            end
        else
            -- self._wtUsePlace:SetVisibility(ESlateVisibility.Collapsed)
        end
        -- self._wtUsePlace:SetVisibility(ESlateVisibility.Collapsed)
    else
        -- if self._wtUsePlace then
            -- self._wtUsePlace:SetVisibility(ESlateVisibility.Collapsed)
        -- end
        local title = {}
        local bHaveUsePlace = false
        for _, usePlaceInfo in ipairs(self._allUsePlaceInfo) do
            if #usePlaceInfo.dataList > 0 then
                bHaveUsePlace = true
                local name = ItemDetailConfig.UsePlaceText[usePlaceInfo.sourceType]
                table.insert(title, tostring(name))
            end
        end
        self._wtUsePlaceTextInGame:SetText(table.concat(title, " / "))
        if bHaveUsePlace and self._wtUsePlaceInGame then
            self._wtUsePlaceInGame:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        else
            self._wtUsePlaceInGame:SetVisibility(ESlateVisibility.Collapsed)
        end
    end
end

function ItemDetailContent:OnCheckUsePlaceBoxClicked()
    local tipsAlignWidget = self._bigTipsAlignWidget or self
    local title = ItemDetailConfig.Loc.titleUsePlace
    local loadFinCall = nil
    if Facade.GameFlowManager:GetCurrentGameFlow() ~= EGameFlowStageType.Game then
        loadFinCall = function (uiIns)
            local uiController = Facade.UIManager:GetLayerControllerByType((EUILayer.Pop))
            if uiController then
                uiController:SetUIToLastPos(uiIns)
            end
        end
    end
    local handle =
        Module.CommonTips:ShowDescriptionWindow(
        self,
        title,
        nil,
        self._usePlaceDataList,
        tipsAlignWidget,
        nil,
        nil,
        loadFinCall
    )

    if handle == nil then
        return
    end
end

function ItemDetailContent:_OnScrolled(CurOffset)
    if self._onContentScrollCallback then
        self._onContentScrollCallback(CurOffset)
    end
end
------------------------------------ Public function ------------------------------------

---@param itemInfo ItemBase
-- @param bForceReset boolean 是否强制重刷新数据，当UI有子UI在异步加载的时候会拒绝数据刷新，避免短时间内多次刷新
-- @param bItemMoveCmd boolean 如果触发了ItemMove回调，应当清除原先的按钮，例如武器卸载子弹
-- @param bIconItemShow boolean itemview道具图标的可视性，如果不显示图标，那么按钮要放到详情的最下方
-- @param ItemDetaiLView table 由于滑动框移到ItemDetaiLView，而bIsNoToStart的时机是在conetnt加载完毕时，所以需要透传
function ItemDetailContent:SetItem(itemInfo, bForceReset, bIsNoToStart, bItemMoveCmd, bIconItemShow, itemDetaiLView)
    bForceReset = setdefault(bForceReset, false)
    bIsNoToStart = setdefault(bIsNoToStart, false)
    bItemMoveCmd = setdefault(bItemMoveCmd, false)
    self._bIconItemShow = setdefault(bIconItemShow, true)
    if itemDetaiLView then
        self._ItemDetaiLView = itemDetaiLView
    end

    self._bSourceVisible = true
    self._bUsePlaceVisible = true
    if itemInfo == nil then
        logerror(" ItemDetailContent SetItem : itemInfo is nil ")
        return
    end
    self._itemInfo = itemInfo
    loginfo("ItemDetailContent:SetItem", itemInfo.id, bForceReset, bIsNoToStart, bItemMoveCmd, self._asyncCreateUIBatchId and #self._asyncCreateUIBatchId)

    --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() then
        self._wtContentWeaponMysticalSkin = nil
        self._wtContentOrnaments = nil
    end
    --- END MODIFICATION

    -- 不重复加载
    if bForceReset == false and self._asyncCreateUIInst > 0 then
        return
    else
        self:_ClearAsyncLoadLogic()
    end

    --self._curSubContentWeakUiIns = nil
    if not self._bShowCommonContentBtn then
        self:InitContentBtnCfg(true, true, self, self._wtBtnWrapBox, nil, UIName2ID.ItemDetailDFCommonButtonV3S1, self._wContentSlotTop)
    end

    if (not self._bIconItemShow) and self._bAdjustBtnPosWhenIconHide then
        if self._wtBtnWrapBox2 then
            self._btnParent = self
            self._btnParentSlot = self._wtBtnWrapBox2
            self._wtBtnLine:Collapsed()
        end
    end

    table.removeByFunc(self._btnTypeList, ItemDetailContent.BtnTypeRemoveFunc)
    if bItemMoveCmd then
        Facade.UIManager:RemoveSubUIByParent(self, self._wtBtnWrapBox)
    else
        Facade.UIManager:RemoveSubUIByParent(self, self._wContentSlotTop)
    end

    self:_CreateTimerDelayShowSourceAndUsePlace()

    local itemFeature = self._itemInfo:GetFeature()
    local featureType = itemFeature:GetFeatureType()
    local itemMainType = ItemHelperTool.GetMainTypeById(self._itemInfo.id)
    local itemSubType = ItemHelperTool.GetSubTypeById(self._itemInfo.id)
    local itemThirdType = ItemHelperTool.GetThirdTypeById(self._itemInfo.id)

    -- 战场道具
    if ItemHelperTool.IsArmedForceUniquePropByItem(self._itemInfo) then
        self:_SetArmedForceItemDetail()
    -- 藏品类道具
    elseif featureType == EFeatureType.Default and itemFeature:IsCollecionItem() then
        self:_SetCollectionDetail()
    -- Weapon 0
    elseif featureType == EFeatureType.Weapon then
        -- 特殊处理近战武器 匕首
        if itemMainType == EItemType.WeaponSkin then
            if itemThirdType == 0 then
				self:_SetWeaponMysticalSkinDetail()
			else
                self:_SetWeaponSkinDetail()
				-- self:_SetBluePrintCollectionDetail()
			end
        elseif itemFeature:IsKnife() then
            self:_SetMeleeWeaponDetail()
        elseif itemFeature:IsAirBrush() then
            self:_SetAirbrushDetail()
        elseif itemFeature:IsMeleeWeapon() then
            self:_SetOtherDetail()
        else
            self:_SetWeaponDetail(bIsNoToStart)
        end
    elseif featureType == EFeatureType.Bullet then
        -- MedicineAndFood 4
        self:_SetBulletDetail()
    elseif featureType == EFeatureType.Health and itemFeature:IsMedicine() then
        self:_SetMedicineAndFoodDetail()
    elseif featureType == EFeatureType.Equipment then
        -- Adapter 5
        -- Container 3
        -- 钥匙包容器先什么都不显示
        if itemFeature:IsKeyChain() then
            self:_SetKeyCaseDetail()
        elseif itemFeature:IsContainerItem() then
            -- Container-Depot 3
            if itemFeature:IsBag() or itemFeature:IsChestHanging() then
                self:_SetBagAndChestDetail()
            else
                -- 保险箱
                self:_SetContainerDetail()
            end
        elseif itemFeature:IsExtendItem() then
            self:_SetDepotDetailNew()
        elseif itemFeature:IsBreastPlate() then
            -- Armor 1
            self:_SetArmorDetail()
        elseif itemFeature:IsHelmet() then
            self:_SetHelmetDetail()
        end
    elseif featureType == EFeatureType.Adapter and itemFeature:IsAdapter() then
        -- Key 6
        self:_SetAdapterDetail()
    elseif featureType == EFeatureType.Key then
        -- Gift 7
        self:_SetKeyDetail()
    elseif featureType == EFeatureType.Reward then
        -- Collectable 9
        self:_SetGiftDetail()
    elseif featureType == EFeatureType.Default and itemFeature:IsCollectableItem() then
        self:_SetCollectableDetail()
    elseif featureType == EFeatureType.KeyBoxUnlockItem then
        -- KeyCase 10
        self:_SetKeyCaseUnlockDetail()
    elseif itemMainType == EItemType.Adapter and itemSubType == ItemConfig.EAdapterItemType.Pendant then
        self:_SetOrnamentsDetail()
    elseif ItemDetailLogic.IsCommercializeItem(self._itemInfo) then
        self:_SetCommercializeDetail()
    else
        -- Other 11
        self:_SetOtherDetail()
    end

    -- Desc
    self:_SetItemDesc()

    self._wtBtnWrapBox:SelfHitTestInvisible()
    -- if self._wtSource then
        -- self._wtSource:SetVisibility(ESlateVisibility.Collapsed)
    -- end
    -- if self._wtUsePlace then
        -- self._wtUsePlace:SetVisibility(ESlateVisibility.Collapsed)
    -- end
    self:SetBigTipsAlignWidget(self._bigTipsAlignWidget)
end

function ItemDetailContent:_ClearTimerDelayShowSourceAndUsePlace()
    if self._timerDelayShowSourceAndUsePlace then
        Timer.CancelDelay(self._timerDelayShowSourceAndUsePlace)
        self._timerDelayShowSourceAndUsePlace = nil
    end
end

function ItemDetailContent:_CreateTimerDelayShowSourceAndUsePlace()
    self:_ClearTimerDelayShowSourceAndUsePlace()
    self._timerDelayShowSourceAndUsePlace = Timer.DelayCall(0.1, self._DelayShowSourceAndUsePlace, self)
    self:AddAsyncFrameUINum()
end

function ItemDetailContent:_DelayShowSourceAndUsePlace()
    self._timerDelayShowSourceAndUsePlace = nil
    self:_InitSourceAndUsePlace()
    self:_RefreshSource()
    self:_RefreshUsePlace()
    self:SubAsyncFrameUINum()
end

-- 设置打开武器属性界面的顶点和锚点，默认按钮位置
function ItemDetailContent:SetWeaponAttrOffset(weaponAttrOffset, weaponAttrAlignment)
    if self._curSubContentWeakUiIns and getfromweak(self._curSubContentWeakUiIns) then
        if self._curSubContentWeakUiIns.SetWeaponAttrOffset then
            self._curSubContentWeakUiIns:SetWeaponAttrOffset(weaponAttrOffset, weaponAttrAlignment)
        end
    else
        table.insert(
            self._funcAfterContentFinish,
            {
                func = self.SetWeaponAttrOffset,
                args = {weaponAttrOffset, weaponAttrAlignment}
            }
        )
    end
end

-- function ItemDetailContent:SetTrackBtnVisible(bVisible)
--     if self._curSubContentWeakUiIns and getfromweak(self._curSubContentWeakUiIns) then
--         if self._curSubContentWeakUiIns.SetTrackBtnVisible then
--             self._curSubContentWeakUiIns:SetTrackBtnVisible(bVisible)
--         end
--     else
--         table.insert(
--             self._funcAfterContentFinish,
--             {
--                 func = self.SetTrackBtnVisible,
--                 args = {bVisible}
--             }
--         )
--     end
-- end

function ItemDetailContent:SetKeyDurabilityVisible(bVisible)
    if self._curSubContentWeakUiIns and getfromweak(self._curSubContentWeakUiIns) then
        if self._curSubContentWeakUiIns.SetKeyDurabilityVisible then
            self._curSubContentWeakUiIns:SetKeyDurabilityVisible(bVisible)
        end
    else
        table.insert(
            self._funcAfterContentFinish,
            {
                func = self.SetKeyDurabilityVisible,
                args = {bVisible}
            }
        )
    end
end

function ItemDetailContent:SetSourceDescVisible(bVisible)
    -- if not self._wtSource then
    --     return
    -- end
    self._bSourceVisible = bVisible
    -- if bVisible then
    --     self._wtSource:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    -- else
    --     self._wtSource:SetVisibility(ESlateVisibility.Collapsed)
    -- end
end

function ItemDetailContent:SetUsePlaceDescVisible(bVisible)
    -- if not self._wtUsePlace then
        -- return
    -- end
    self._bUsePlaceVisible = bVisible
    -- if bVisible then
        -- self._wtUsePlace:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    -- else
        -- self._wtUsePlace:SetVisibility(ESlateVisibility.Collapsed)
    -- end
end

function ItemDetailContent:SetAdjustBtnPosWhenIconHide(bAdjustBtnPosWhenIconHide)
    self._bAdjustBtnPosWhenIconHide = bAdjustBtnPosWhenIconHide
end

function ItemDetailContent:SetWeaponAdapterVisible(bVisible)
    if self._curSubContentWeakUiIns and getfromweak(self._curSubContentWeakUiIns) then
        if self._curSubContentWeakUiIns.SetWeaponAdapterVisible then
            self._curSubContentWeakUiIns:SetWeaponAdapterVisible(bVisible)
        end
    else
        table.insert(
            self._funcAfterContentFinish,
            {
                func = self.SetWeaponAdapterVisible,
                args = {bVisible}
            }
        )
    end
end

function ItemDetailContent:SetWeaponBulletVisible(bVisible)
    if self._curSubContentWeakUiIns and getfromweak(self._curSubContentWeakUiIns) then
        if self._curSubContentWeakUiIns.SetWeaponBulletVisible then
            self._curSubContentWeakUiIns:SetWeaponBulletVisible(bVisible)
        end
    else
        table.insert(
            self._funcAfterContentFinish,
            {
                func = self.SetWeaponBulletVisible,
                args = {bVisible}
            }
        )
    end
end

function ItemDetailContent:SetWeaponBulletUnEquipVisible(bVisible)
    if self._curSubContentWeakUiIns and getfromweak(self._curSubContentWeakUiIns) then
        if self._curSubContentWeakUiIns.SetWeaponBulletUnEquipVisible then
            self._curSubContentWeakUiIns:SetWeaponBulletUnEquipVisible(bVisible)
        end
    else
        table.insert(
            self._funcAfterContentFinish,
            {
                func = self.SetWeaponBulletUnEquipVisible,
                args = {bVisible}
            }
        )
    end
end

function ItemDetailContent:SetBulletDamge(damage, armorDamage)
    if self._curSubContentWeakUiIns and getfromweak(self._curSubContentWeakUiIns) then
        if self._curSubContentWeakUiIns.SetBulletDamge then
            self._curSubContentWeakUiIns:SetBulletDamge(damage, armorDamage)
        end
    else
        table.insert(
            self._funcAfterContentFinish,
            {
                func = self.SetBulletDamge,
                args = {damage, armorDamage}
            }
        )
    end
end

function ItemDetailContent:SetSourceInfo(sourceInfo)
    self._allSourceInfo = sourceInfo
    self:_RefreshSource()
end

function ItemDetailContent:SetUsePlaceInfo(usePlaceInfo)
    self._allUsePlaceInfo = usePlaceInfo
    self:_RefreshUsePlace()
end

function ItemDetailContent:SetAssemblyQuickOperationInfo(fUnEquipWeaponBulletCallback)
    self._fUnEquipWeaponBulletCallback = fUnEquipWeaponBulletCallback
    if self._curSubContentWeakUiIns and getfromweak(self._curSubContentWeakUiIns) then
        if self._curSubContentWeakUiIns.SetAssemblyQuickOperationInfo then
            self._curSubContentWeakUiIns:SetAssemblyQuickOperationInfo(fUnEquipWeaponBulletCallback)
        end
    else
        table.insert(
            self._funcAfterContentFinish,
            {
                func = self.SetAssemblyQuickOperationInfo,
                args = {fUnEquipWeaponBulletCallback}
            }
        )
    end
end

function ItemDetailContent:SetAdapterDragState(bCanDrag)
    self._bCanDragAdapter = bCanDrag
    if self._curSubContentWeakUiIns and getfromweak(self._curSubContentWeakUiIns) then
        if self._curSubContentWeakUiIns.SetAdapterDragState then
            self._curSubContentWeakUiIns:SetAdapterDragState(bCanDrag)
        end
    else
        table.insert(
            self._funcAfterContentFinish,
            {
                func = self.SetAdapterDragState,
                args = {bCanDrag}
            }
        )
    end
end

function ItemDetailContent:SetAdapterTipsShowButtonState(bTipsShowBtton)
    self._bAdapterTipsShowBtton = bTipsShowBtton
    if self._curSubContentWeakUiIns and getfromweak(self._curSubContentWeakUiIns) then
        if self._curSubContentWeakUiIns.SetAdapterTipsShowButtonState then
            self._curSubContentWeakUiIns:SetAdapterTipsShowButtonState(bTipsShowBtton)
        end
    else
        table.insert(
            self._funcAfterContentFinish,
            {
                func = self.SetAdapterTipsShowButtonState,
                args = {bTipsShowBtton}
            }
        )
    end
end

function ItemDetailContent:SetAdapterFastUnequipState(bCanFastUnequip)
    self._bAdapterCanFastUnequip = bCanFastUnequip
    if self._curSubContentWeakUiIns and getfromweak(self._curSubContentWeakUiIns) then
        if self._curSubContentWeakUiIns.SetAdapterFastUnequipState then
            self._curSubContentWeakUiIns:SetAdapterFastUnequipState(bCanFastUnequip)
        end
    else
        table.insert(
            self._funcAfterContentFinish,
            {
                func = self.SetAdapterFastUnequipState,
                args = {bCanFastUnequip}
            }
        )
    end
end

function ItemDetailContent:SetOnPoorWeaponState(bOnPoorWeapon)
    self._bOnPoorWeapon = bOnPoorWeapon
    if self._curSubContentWeakUiIns and getfromweak(self._curSubContentWeakUiIns) then
        if self._curSubContentWeakUiIns.SetOnPoorWeaponState then
            self._curSubContentWeakUiIns:SetOnPoorWeaponState(bOnPoorWeapon)
        end
    else
        table.insert(
            self._funcAfterContentFinish,
            {
                func = self.SetOnPoorWeaponState,
                args = {bOnPoorWeapon}
            }
        )
    end
end

---@param showState ETipsWeaponShowState
function ItemDetailContent:SetWeaponShowState(showState)
    self._weaponShowState = showState
    if self._curSubContentWeakUiIns and getfromweak(self._curSubContentWeakUiIns) then
        if self._curSubContentWeakUiIns.SetWeaponShowState then
            self._curSubContentWeakUiIns:SetWeaponShowState(showState)
        end
    else
        table.insert(
            self._funcAfterContentFinish,
            {
                func = self.SetWeaponShowState,
                args = {showState}
            }
        )
    end
end

---@param direction ENewTipsAlignDirect
function ItemDetailContent:SetWeaponTipsShowDirection(direction)
    self._weaponTipsShowDirection = direction
    if self._curSubContentWeakUiIns and getfromweak(self._curSubContentWeakUiIns) then
        if self._curSubContentWeakUiIns.SetWeaponTipsShowDirection then
            self._curSubContentWeakUiIns:SetWeaponTipsShowDirection(direction)
        end
    else
        table.insert(
            self._funcAfterContentFinish,
            {
                func = self.SetWeaponTipsShowDirection,
                args = {direction}
            }
        )
    end
end

-- 隐藏所有tips的显示，包括配件的点击
function ItemDetailContent:HideAllTipsBtn()
    if self._curSubContentWeakUiIns and getfromweak(self._curSubContentWeakUiIns) then
        if self._curSubContentWeakUiIns.HideAllTipsBtn then
            self._curSubContentWeakUiIns:HideAllTipsBtn()
        end
    else
        table.insert(
            self._funcAfterContentFinish,
            {
                func = self.HideAllTipsBtn,
                args = {}
            }
        )
    end
end

-- 播放来源用途指定按钮光效
--@param btnType EItemSource
function ItemDetailContent:PlayBreathAnimSource(btnType, times)
    times = setdefault(times, 0)
    if self._asyncCreateUIInst > 0 then
        table.insert(
            self._funcAfterContentFinish,
            {
                func = self.PlayBreathAnimSource,
                args = {btnType, times}
            }
        )
    else
        local uiIns = self._sourceBtnMap[btnType]
        if uiIns then
            uiIns:PlayWidgetAnim(uiIns.BreathAnim, times)
        else
            self._afterSourceBtnLoadCallback = function()
                local uiIns = self._sourceBtnMap[btnType]
                if uiIns then
                    uiIns:PlayWidgetAnim(uiIns.BreathAnim, times)
                end
            end
        end
    end
end

-- 停止来源用途指定按钮光效
--@param btnType EItemSource
function ItemDetailContent:StopBreathAnimSource(btnType)
    if self._asyncCreateUIInst > 0 then
        table.insert(
            self._funcAfterContentFinish,
            {
                func = self.StopBreathAnimSource,
                args = {btnType}
            }
        )
    else
        local uiIns = self._sourceBtnMap[btnType]
        if uiIns then
            uiIns:StopWidgetAnim(uiIns.BreathAnim)
        else
            self._afterSourceBtnLoadCallback = nil
        end
    end
end

function ItemDetailContent:Reset()
    --logerror("ItemDetailContent:Reset", self._lastSubContentUIName,  self._curSubContentWeakUiIns)
    self._afterSourceBtnLoadCallback = nil
    if self._curSubContentWeakUiIns then
        self._fUnEquipWeaponBulletCallback = nil
        self._curSubContentWeakUiIns = nil
        self._bCanDragAdapter = false
        self._bAdapterCanFastUnequip = false
        self._bAdapterTipsShowBtton = false
    end
    self._bAdjustBtnPosWhenIconHide = true
    self._bigTipsAlignWidget = nil
    self._funcAfterContentFinish = {}
    self:_ClearAsyncLoadLogic()
    Facade.UIManager:RemoveSubUIByParent(self, self._wtBtnWrapBox)
    Facade.UIManager:RemoveSubUIByParent(self, self._wtBtnWrapBox2)
end

function ItemDetailContent:_ClearAsyncLoadLogic()
	if self._asyncCreateUIBatchId and next(self._asyncCreateUIBatchId) then
		for _, uiBatchId in ipairs(self._asyncCreateUIBatchId) do
			if Facade.ResourceManager:CancelLoadByBatchId(uiBatchId) then
				local batch = Facade.ResourceManager:GetBatchByBatchId(uiBatchId)
				if batch and batch.mapPath2Idx then
					for path, _ in pairs(batch.mapPath2Idx) do
						loginfo('ItemDetailContent:_ClearAsyncLoadLogic---------子UI中断加载,已取消:', path)
					end
				end
			end
		end
	end
	self._asyncCreateUIBatchId = {}
	self._asyncCreateUIInst = 0
    self._asyncCreateBtnNum = 0
end

function ItemDetailContent:SetBigTipsAlignWidget(widget)
    self._bigTipsAlignWidget = widget
    if self._curSubContentWeakUiIns and getfromweak(self._curSubContentWeakUiIns) then
        if self._curSubContentWeakUiIns.SetBigTipsAlignWidget then
            self._curSubContentWeakUiIns:SetBigTipsAlignWidget(widget)
        end
    else
        table.insert(
            self._funcAfterContentFinish,
            {
                func = self.SetBigTipsAlignWidget,
                args = {widget}
            }
        )
    end
end

function ItemDetailContent:SetFromItemView(bFromItemDetailView)
    if self._curSubContentWeakUiIns and getfromweak(self._curSubContentWeakUiIns) then
        if self._curSubContentWeakUiIns.SetFromItemView then
            self._curSubContentWeakUiIns:SetFromItemView(bFromItemDetailView)
        end
    else
        table.insert(
            self._funcAfterContentFinish,
            {
                func = self.SetFromItemView,
                args = {bFromItemDetailView}
            }
        )
    end
end

function ItemDetailContent:AddToContent(widget)
    -- 已废弃先注释
    -- if not self._wtContentSlotTop then
    --     return
    -- end

    -- self._wtContentSlotTop:SelfHitTestInvisible()
    -- self._wtContentSlotTop:AddChild(widget)
    -- table.insert(self._extraContentWidgets, widget)
    -- self._wtScrollBoxContent:ScrollToStart()
end

function ItemDetailContent:ResetContent()
    -- if not self._wtContentSlotTop then
    --     return
    -- end

    -- self._wtContentSlotTop:Collapsed()
    -- for _, widget in ipairs(self._extraContentWidgets) do
    --     Facade.UIManager:CloseUI(widget)
    -- end
    -- self._extraContentWidgets = setmetatable({}, weakmeta_value)
    -- self._wtScrollBoxContent:ScrollToStart()
end

function ItemDetailContent:_SendToSafeHouse(btnType, scene)
end

----------------------------------------创建按钮相关----------------------------------------

function ItemDetailContent:GetBtnWrapBox()
    return self._wtBtnWrapBox
end

function ItemDetailContent:GetContentSlot()
    return self._wContentSlotTop
end

function ItemDetailContent:GetBtnWrapBox2()
    return self._wtBtnWrapBox2
end

function ItemDetailContent:AddBtnType(btnType)
    if not btnType then
        logwarning("ItemDetailContent:AddBtnType >>> btnType is nil!")
        return
    end
    table.insert(self._btnTypeList, btnType)
end

function ItemDetailContent:ResetContentBtnCfg()
    self._bShowCommonContentBtn = nil
    self._bItemGeneralBtn = nil
    self._btnParent = nil
    self._btnParentSlot = nil
    self._btnTypeList = nil
    self._btnNavId = nil
    self._floorSlot = nil
    self._btnCreateFinished = nil
    self._btnExtraParams = nil
end

function ItemDetailContent:InitContentBtnCfg(bShowCommonContentBtn, bItemGeneralBtn, btnParent, btnParentSlot, btnTypeList, btnNavId, floorSlot, btnCreateFinished, btnExtraParams)
    self._bShowCommonContentBtn = bShowCommonContentBtn
    self._bItemGeneralBtn = setdefault(bItemGeneralBtn, true)
    self._btnParent = setdefault(btnParent, self)
    self._btnParentSlot = setdefault(btnParentSlot, self._wtBtnWrapBox)
    self._btnTypeList = {}
    deepcopy(self._btnTypeList, btnTypeList or {})
    self._btnNavId = setdefault(btnNavId, UIName2ID.ItemDetailDFCommonButtonV3S1)
    self._floorSlot = setdefault(floorSlot, self._wContentSlotTop)
    self._btnCreateFinished = setdefault(btnCreateFinished, nil)
    self._btnExtraParams = setdefault(btnExtraParams, {})
end

function ItemDetailContent:SetNeedHideUnequipBulletBtnState(state)
	self._bNeedHideUnequipBulletBtn = state
end

function ItemDetailContent:AddAsyncFrameUINum()
    self._asyncCreateBtnNum = self._asyncCreateBtnNum + 1
end

function ItemDetailContent:SubAsyncFrameUINum()
    self._asyncCreateBtnNum = self._asyncCreateBtnNum - 1
    if self._asyncCreateBtnNum <= 0 then
        self._asyncCreateBtnNum = 0
        self:_OnProcessCreateBtn()
    end
end

function ItemDetailContent:_OnProcessCreateBtn()
    if self._asyncCreateBtnNum > 0 then
        return
    end

    if #self._btnTypeList > 0 then
        if self._bIconItemShow and self._wtBtnLine then
            self._wtBtnLine:SelfHitTestInvisible()
        end
        if self._wtBtnWrapBox == self._btnParentSlot then
             self._wtBtnPanel:SelfHitTestInvisible()
        end
        if self._wtBtnWrapBox2 == self._btnParentSlot then
             self._wtBtnPanel2:SelfHitTestInvisible()
        end

        if self._bItemGeneralBtn then
            for inedx, btnType in ipairs(self._btnTypeList) do
                btnType.insertIDX = inedx
            end
            table.sort(self._btnTypeList, ItemDetailContent.BtnFuncSortWeight)
            local function fLoadFinLogic()
                Facade.UIManager:RemoveSubUIByParent(self._btnParent, self._btnParentSlot)
                for k, v in ipairs(self._btnTypeList) do
                    local weakUiIns = Facade.UIManager:AddSubUI(self._btnParent, self._btnNavId, self._btnParentSlot, nil, self._itemInfo, v, self._floorSlot, table.unpack(self._btnExtraParams))
                    if weakUiIns and getfromweak(weakUiIns) then
                        if v.btnType == ItemDetailConfig.ItemBtnKey.InnerSource then
                            self._InnerSourceWeakUIIns = weakUiIns
                        end
                        local uiIns = getfromweak(weakUiIns)
                        if uiIns then
                            uiIns:SetCppValue("bIsFocusable", true)
                            Module.Guide:AddGuideWidgetProxy( string.format("guideProxyItemDetailContentBtn_%s", v.btnType), uiIns)
                            if self._btnCreateFinished then
                                self._btnCreateFinished(uiIns)
                            end
                            if self._bFromPop and IsHD() and uiIns.AddGamepadStyleListen then
                                uiIns:AddGamepadStyleListen()
                            end
                            if self._bFromPop and k == 1 and UGPInputHelper.GetCurrentInputType(GetGameInstance()) == EGPInputType.Gamepad then
                                WidgetUtil.SetUserFocusToWidget(uiIns, true)
                            end
                        end
                    else
                        logerror("ItemDetailContent:_OnProcessCreateBtn: weakUiIns is nil!")
                    end
                end

                if self._ItemDetaiLView then
                    self._ItemDetaiLView:SetBtnNum(#self._btnTypeList)
                    Facade.UIManager:RemoveSubUIByParent(self._ItemDetaiLView, self._ItemDetaiLView._btnParentSlot)
                    for k, v in ipairs(self._btnTypeList) do
                        local weakUiIns = Facade.UIManager:AddSubUI(self._ItemDetaiLView, self._btnNavId, self._ItemDetaiLView._btnParentSlot, nil, self._itemInfo, v, self._floorSlot, table.unpack(self._btnExtraParams))
                        if weakUiIns and getfromweak(weakUiIns) then
                            local uiIns = getfromweak(weakUiIns)
                            if uiIns then
                                if self._btnCreateFinished then
                                    self._btnCreateFinished(uiIns)
                                end
                                if self._bFromPop and IsHD() and uiIns.AddGamepadStyleListen then
                                    uiIns:AddGamepadStyleListen()
                                end
                            end
                        else
                            logerror("ItemDetailContent:_OnProcessCreateBtn2: weakUiIns is nil!")
                        end
                    end
                end
            end
            local function fLoadFinCallback(mapPath2ResIns)
                fLoadFinLogic()
            end
            if Facade.UIManager:CheckUIHasBeenLoaded(self._btnNavId) then
                fLoadFinLogic()
            else
                Facade.UIManager:AsyncLoadUIResOnly(self._btnNavId, fLoadFinCallback)
            end
        else
            local function fLoadFinLogic()
                Facade.UIManager:RemoveSubUIByParent(self._btnParent, self._btnParentSlot)
                local weakUiIns = Facade.UIManager:AddSubUI(self._btnParent, self._btnNavId, self._btnParentSlot, nil, self._itemInfo, self._btnTypeList, self._floorSlot, table.unpack(self._btnExtraParams))
                if weakUiIns and getfromweak(weakUiIns) then
                    local uiIns = getfromweak(weakUiIns)
                    if uiIns then
                        if self._btnCreateFinished then
                            self._btnCreateFinished(uiIns)
                        end
                    end
                else
                    logerror("ItemDetailContent:_OnProcessCreateBtn: weakUiIns is nil!")
                end

                if self._ItemDetaiLView then
                    self._ItemDetaiLView:SetBtnNum(#self._btnTypeList)
                    Facade.UIManager:RemoveSubUIByParent(self._ItemDetaiLView, self._ItemDetaiLView._btnParentSlot)
                    local weakUiIns = Facade.UIManager:AddSubUI(self._ItemDetaiLView, self._btnNavId, self._ItemDetaiLView._btnParentSlot, nil, self._itemInfo, self._btnTypeList, self._floorSlot, table.unpack(self._btnExtraParams))
                    if weakUiIns and getfromweak(weakUiIns) then
                        local uiIns = getfromweak(weakUiIns)
                        if uiIns then
                            uiIns:SetCppValue("bIsFocusable", true)
                            if self._btnCreateFinished then
                                self._btnCreateFinished(uiIns)
                            end
                        end
                    else
                        logerror("ItemDetailContent:_OnProcessCreateBtn2: weakUiIns is nil!")
                    end
                end
            end
            local function fLoadFinCallback(mapPath2ResIns)
                fLoadFinLogic()
            end

            if Facade.UIManager:CheckUIHasBeenLoaded(self._btnNavId) then
                fLoadFinLogic()
            else
                Facade.UIManager:AsyncLoadUIResOnly(self._btnNavId, fLoadFinCallback)
            end
        end
    else
        if self._wtBtnLine then
            self._wtBtnLine:Collapsed()
        end
        if self._wtBtnPanel then
            self._wtBtnPanel:Collapsed()
        end
        if self._wtBtnPanel2 then
            self._wtBtnPanel2:Collapsed()
        end

        ItemDetailConfig.evtItemDetailPanelNoBtn:Invoke(self)
    end
end

ItemDetailContent.BtnFuncSortWeight = function(a, b)
    -- 防止报错
    if not a.btnGroupType then
        a.btnGroupType = ItemDetailConfig.EBtnGroupType.CommonOpt
        logwarning("ItemDetailContent:BtnFuncSortWeight: btnGroupType is nil! a.btnType: ", a.btnType)
    end
    if not b.btnGroupType then
        b.btnGroupType = ItemDetailConfig.EBtnGroupType.CommonOpt
        logwarning("ItemDetailContent:BtnFuncSortWeight: btnGroupType is nil! b.btnType: ", b.btnType)
    end
    -- 先比组别 已有明确顺序，组别仅用于移除按钮了
    -- if a.btnGroupType ~= b.btnGroupType then
    --     return a.btnGroupType < b.btnGroupType
    -- end
    -- 再比推荐
    if a.isRec then
        return true
    end
    if b.isRec then
        return false
    end
    -- 再比预设排序
    local aOrder = table.orderfindkey(Module.ItemDetail.Config.InnerButtonAtTheBegin, a.btnType, false)
    local bOrder = table.orderfindkey(Module.ItemDetail.Config.InnerButtonAtTheBegin, b.btnType, false)
    if aOrder and bOrder then
        if aOrder ~= bOrder then
            return aOrder < bOrder
        else
            logwarning("ItemDetailContent:BtnFuncSortWeight: InnerButtonAtTheBegin is error! a.btnType: ", a.btnType)
            logwarning("ItemDetailContent:BtnFuncSortWeight: InnerButtonAtTheBegin is error! a.btnType: ", a.btnType)
            return false
        end
    else
        logwarning("ItemDetailContent:BtnFuncSortWeight: InnerButtonAtTheBegin is nil! a.btnType: ", a.btnType)
        logwarning("ItemDetailContent:BtnFuncSortWeight: InnerButtonAtTheBegin is nil! b.btnType: ", b.btnType)
    end

    -- 未配置预设顺序的，保底放到最后，便于发现问题
    if not aOrder then
        return false
    else
        return true
    end
end

-- 一些需要回包的item特殊按钮每次添加都需要先清除
function ItemDetailContent:RmoveItemBtn()
    table.removeByFunc(self._btnTypeList, ItemDetailContent.BtnTypeRemoveFuncByItem)
end

ItemDetailContent.BtnTypeRemoveFunc = function(t)
    return t.btnGroupType and t.btnGroupType ~= ItemDetailConfig.EBtnGroupType.CommonOpt or false
end

ItemDetailContent.BtnTypeRemoveFuncByItem = function(t)
    return t.btnGroupType and t.btnGroupType == ItemDetailConfig.EBtnGroupType.Item or false
end
----------------------------------------创建按钮相关----------------------------------------

-- 序号控制显示顺序，相同时按照插入顺序先后比较
ItemDetailContent.FuncSortWeight = function(a, b)
    if a.sortIDX ~= b.sortIDX then
        return a.sortIDX < b.sortIDX
    end
    return a.insertIDX < b.insertIDX
end

function ItemDetailContent:_OnSellOrSplitOpenClicked(itemStruct, btnTypeList)
    if self._wtBtnPanel then
        self._wtBtnPanel:Collapsed()
    end

    local function fLoadFinLogic()
        local weakUiIns = Facade.UIManager:AddSubUI(self, UIName2ID.ItemDetailViewBtnSplitBtn, self._wContentSlotTop, nil, itemStruct, btnTypeList)
        if weakUiIns and getfromweak(weakUiIns) then
        else
            logerror("ItemDetailContent:_OnSellOrSplitOpenClicked:UIManager:AddSubUI: weakUiIns is nil!")
        end
     end

    local function fLoadFinCallback(mapPath2ResIns)
        fLoadFinLogic()
    end

    if Facade.UIManager:CheckUIHasBeenLoaded(UIName2ID.ItemDetailViewBtnSplitBtn) then
        fLoadFinLogic()
    else
        Facade.UIManager:AsyncLoadUIResOnly(UIName2ID.ItemDetailViewBtnSplitBtn, fLoadFinCallback)
    end
end

function ItemDetailContent:_OnSellOrSplitCloseClicked()
    if self._wtBtnPanel then
        self._wtBtnPanel:SelfHitTestInvisible()
    end
    Facade.UIManager:RemoveSubUIByParent(self, self._wContentSlotTop)
end

function ItemDetailContent:MarkOpenFromPop(bFromPop)
	self._bFromPop = bFromPop
end

function ItemDetailContent:GetSourceWeakUIIns()
	return self._InnerSourceWeakUIIns
end


return ItemDetailContent
