----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRankingList)
----- LOG FUNCTION AUTO GENERATE END -----------



--------------------------------------------------------------------------
---UI路径映射配置示例（UILayer表示需要加入的层级）
--------------------------------------------------------------------------


UITable[UIName2ID.RankingListMainPanel] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.RankingListModule.UI.RankingListMainPanel",
    BPKey = "WBP_RankingList_Main",
    ReConfig = {
        IsPoolEnable = true,
    },
    LinkSubStage = ESubStage.HallIndividual,
    SubUIs = {
        UIName2ID.RankingListItem,
        UIName2ID.HistoryTitleSubItemList,
        UIName2ID.HistoryTitleSubItem,
    },
}

UITable[UIName2ID.RankingListItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RankingListModule.UI.RankingListItem",
    BPKey = "WBP_RankingList_RankingInformation",
}

UITable[UIName2ID.RankingListChallenging] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.RankingListModule.UI.RankingListChallenging",
    BPKey = "WBP_RankingList_ChallengingHistory",
}

UITable[UIName2ID.RankingListHistory] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.RankingListModule.UI.RankingListHistory",
    BPKey = "WBP_RankingList_History",
}

UITable[UIName2ID.RankingListIllustrate] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.RankingListModule.UI.RankingListIllustrate",
    BPKey = "WBP_RankingList_Illustrate",
    SubUIs = {
        UIName2ID.IllustrateItem,
    },
    IsModal = true,
}

UITable[UIName2ID.IllustrateItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RankingListModule.UI.IllustrateItem",
    BPKey = "WBP_Reputation_CodeOfConductSegmentation",
}

UITable[UIName2ID.RankingListChangeChallenge] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.RankingListModule.UI.RankingListChangeChallenge",
    BPKey = "WBP_RankingList_Challenge",
    IsModal = true,
}

UITable[UIName2ID.RankingListRatingDetails] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.RankingListModule.UI.RankingListRatingDetails",
    BPKey = "WBP_RankingList_RatingDetails",
    IsModal = true,
}

UITable[UIName2ID.RatingDetailItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RankingListModule.UI.RatingDetailItem",
    BPKey = "WBP_RankingList_Score",
}

UITable[UIName2ID.ChallengingItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RankingListModule.UI.ChallengingItem",
    BPKey = "WBP_RankingList_TitleDisplay",
}

UITable[UIName2ID.DataTypeDisplayItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RankingListModule.UI.DataTypeDisplayItem",
    BPKey = "WBP_RankingList_DisplayButton",
}

UITable[UIName2ID.HistoryItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RankingListModule.UI.HistoryItem",
    BPKey = "WBP_RankingList_HistoryItem",
}

UITable[UIName2ID.HistoryTitleSubItemList] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RankingListModule.UI.HistoryTitleSubItemList",
    BPKey = "WBP_RankingList_HistoryTitleSubItemList",
}

UITable[UIName2ID.HistoryTitleSubItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RankingListModule.UI.HistoryTitleSubItem",
    BPKey = "WBP_RankingList_HistoryTitleSubItem",
}

UITable[UIName2ID.TitleUnlockPop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.RankingListModule.UI.Pop.TitleUnlockPop",
    BPKey = "WBP_RankingList_TitleUnlockPop",
}

local ERankingArea = {
    ALL = 1,
    AREA = 2,
    FRIEND = 3,
}

local ERankingGlobalArea = {
    ALL = 1,
    FRIEND = 2,
}




local RankingListConfig = {
    Loc = {
        NoInThisArea = NSLOCTEXT("RankingListModule", "Lua_RankingList_NoInThisArea", "不同战区"),
        PrivacyName = NSLOCTEXT("RankingListModule", "Lua_RankingList_PrivacyName", "匿名玩家"),
        AllInRank = NSLOCTEXT("RankingListModule", "Lua_RankingList_AllInRank", "%s    首次三角洲百强"),
        ProvinceInRank = NSLOCTEXT("RankingListModule", "Lua_RankingList_ProvinceInRank", "%s    首次省级百强"),
        CityInRank = NSLOCTEXT("RankingListModule", "Lua_RankingList_CityInRank", "%s    首次市级百强"),
        RankTitle = NSLOCTEXT("RankingListModule", "Lua_RankingList_RankTitle", "%s榜"),
        RankTipDesc = NSLOCTEXT("RankingListModule", "Lua_RankingList_RankTipDesc", "段位越高，生效场次上限越高，具体规则可见排行榜说明"),
        ChallengTipDesc = NSLOCTEXT("RankingListModule", "Lua_RankingList_ChallengTipDesc", "由战区榜单决定，进入战区则揭晓问号，结算过市级百强则点亮市级图标，结算过省级百强则点亮省级图标"),
        ChallengNum = NSLOCTEXT("RankingListModule", "Lua_RankingList_ChallengNum", "点亮称号：<customstyle color=\"Color_Highlight02\">%s</>/35"),
        RankingListMainTitle = NSLOCTEXT("RankingListModule", "Lua_RankingList_RankingListMainTitle", "排行榜"),
        RankingChallengingMainTitle = NSLOCTEXT("RankingListModule", "Lua_RankingList_RankingChallengingMainTitle", "挑战历程"),
        NoRankArea = NSLOCTEXT("RankingListModule", "Lua_RankingList_NoRankArea", "暂无战区"),
        AllCityArea = NSLOCTEXT("RankingListModule", "Lua_RankingList_AllCityArea", "不限"),
        ScoreString = NSLOCTEXT("RankingListModule", "Lua_RankingList_ScoreString", "%s（%s/min）"),

        SolMatchDesc = NSLOCTEXT("RankingListModule", "Lua_RankingList_SolMatchDesc", "本周对局中，对应维度表现最好的若干场次的成绩之和"),
        SolMatchNum = NSLOCTEXT("RankingListModule", "Lua_RankingList_SolMatchNum", "当前计数场次：<customstyle color=\"Color_Highlight02\">%s</>"),
        SolMatchTips = NSLOCTEXT("RankingListModule", "Lua_RankingList_SolMatchTips", "受玩家赛季段位影响，初始限额20场，钻石及以上限额30场"),

        MpMatchDesc = NSLOCTEXT("RankingListModule", "Lua_RankingList_MpMatchDesc", "本周对局中，对应维度表现最好的若干场次的成绩之和"),
        MpRatingNum = NSLOCTEXT("RankingListModule", "Lua_RankingList_MpRatingNum", "当前计数分钟：<customstyle color=\"Color_Highlight02\">%s</>"),
        MpMatchTips = NSLOCTEXT("RankingListModule", "Lua_RankingList_MpMatchTips", "受玩家赛季段位影响，初始限额200分钟，校官及以上限额300分钟"),

        FirstChangeAreaDesc = NSLOCTEXT("RankingListModule", "Lua_RankingList_FirstChangeAreaDesc", "首次选择战区后，将立即生效。之后预选战区将延后下周一生效"),
        
        SecondChangeAreaDesc = NSLOCTEXT("RankingListModule", "Lua_RankingList_SecondChangeAreaDesc", "选择预选战区后，将在下周一4：00自动生效。"),

        SelectPreAreaTips = NSLOCTEXT("RankingListModule", "Lua_RankingList_SelectPreAreaTips", "已预选<customstyle color=\"Color_Highlight02\">%s</>战区进行挑战，将在下周一生效"),

        SelectAreaTips = NSLOCTEXT("RankingListModule", "Lua_RankingList_SelectAreaTips", "已加入<customstyle color=\"Color_Highlight02\">%s</>战区进行挑战"),

        SelectLocalAreaTips = NSLOCTEXT("RankingListModule", "Lua_RankingList_SelectLocalAreaTips", "已选择当前定位战区"),

        SelectLocalAreaFailTips = NSLOCTEXT("RankingListModule", "Lua_RankingList_SelectLocalAreaFailTips", "无获取位置权限"),

        CNTopPlayer = NSLOCTEXT("RankingListModule", "Lua_RankingList_CNTopPlayer", "国服最强"),
        ProvinceTopPlayer = NSLOCTEXT("RankingListModule", "Lua_RankingList_ProvinceTopPlayer", "省级最强"),
        CityTopPlayer = NSLOCTEXT("RankingListModule", "Lua_RankingList_CityTopPlayer", "市级最强"),
        
        RankingHistoryTitle = NSLOCTEXT("RankingListModule", "Lua_RankingList_HistoryTitle", "历史荣誉"),
        SolTitle = NSLOCTEXT("RankingListModule", "Lua_RankingList_SolTitle", "烽火地带"),
        MpTitle = NSLOCTEXT("RankingListModule", "Lua_RankingList_MpTitle", "全面战场"),
        RankDataTypeOverview = NSLOCTEXT("RankingListModule", "Lua_RankingList_RankDataTypeOverview", "榜单总览"),
        SeasonOverview = NSLOCTEXT("RankingListModule", "Lua_RankingList_SeasonOverview", "赛季总览"),
        CurrentPeriodNum = NSLOCTEXT("RankingListModule", "Lua_RankingList_CurrentPeriodNum", "当前\n第%s期"),
        PeriodNum = NSLOCTEXT("RankingListModule", "Lua_RankingList_PeriodNum", "第%s期"),

        RankingDeviceInstruct = NSLOCTEXT("RankingListModule", "Lua_RankingList_RankingDeviceInstruct", "手机端评分: %s\n电脑端评分: %s\n已根据评分情况自动参与%s榜单排行。当前正在查看%s榜单情况。"),
        RankingDevicePC = NSLOCTEXT("RankingListModule", "Lua_RankingList_RankingDevicePC", "电脑端"),
        RankingDeviceMobile = NSLOCTEXT("RankingListModule", "Lua_RankingList_RankingDeviceMobile", "移动端"),
        RankingDifferentDevice = NSLOCTEXT("RankingListModule", "Lua_RankingList_RankingDifferentDevice", "不同设备类型"),
        StopPeriod = NSLOCTEXT("RankingListModule", "Lua_RankingList_StopPeriod", "更新到新版本后可查看"),
        Current = NSLOCTEXT("RankingListModule", "Lua_RankingList_Current", "至今"),
        EmptyDesc = NSLOCTEXT("RankingListModule", "Lua_RankingList_EmptyDesc", "虚位以待\n上榜成功获得本期荣誉称号"),
        ComingSoon = NSLOCTEXT("RankingListModule", "Lua_RankingList_ComingSoon", "敬请期待\n历史荣誉回溯数据中"),
        InSeason = NSLOCTEXT("RankingListModule", "Lua_RankingList_InSeason", "赛季内"),
        -- CountryObtain = NSLOCTEXT("RankingListModule", "Lua_RankingList_CountryObtain", "国榜获得xxx"),
        -- ProvinceObtain = NSLOCTEXT("RankingListModule", "Lua_RankingList_ProvinceObtain", "省榜获得xxx"),
        -- CityObtain = NSLOCTEXT("RankingListModule", "Lua_RankingList_CityObtain", "市榜获得xxx"),
        
        RankingAreaTxt = {
            [ERankingArea.ALL] = NSLOCTEXT("RankingListModule", "Lua_RankingList_ALL", "三角洲"),
            [ERankingArea.AREA] = NSLOCTEXT("RankingListModule", "Lua_RankingList_AREA", "战区"),
            [ERankingArea.FRIEND] = NSLOCTEXT("RankingListModule", "Lua_RankingList_FRIEND", "好友"),
        },
        RankingAreaGlobalTxt = {
            [ERankingGlobalArea.ALL] = NSLOCTEXT("RankingListModule", "Lua_RankingList_ALL", "三角洲"),
            [ERankingGlobalArea.FRIEND] = NSLOCTEXT("RankingListModule", "Lua_RankingList_FRIEND", "好友"),
        },

        SolRankListTabTxt = {
            [1] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_SolRankList1", "段位"),
            [2] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_SolRankList2", "带出价值"),
            [3] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_SolRankList3", "击败干员"),
            -- [4] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_SolRankList4", "行动报酬"),
            -- [5] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_SolRankList5", "击杀首领"),
            -- [6] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_SolRankList6", "曼德尔砖"),
        },

        MpRankListTabTxt = {
            [1] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_MpRankList1", "军功"),
            [2] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_MpRankList7", "胜者为王"),
            [3] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_MpRankList2", "击败干员"),
            [4] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_MpRankList3", "占点贡献"),
            [5] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_MpRankList4", "救治贡献"),
            -- [6] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_MpRankList5", "工程贡献"),
            -- [7] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_MpRankList6", "战术贡献"),
        },
        
        -- deprecated
        RankTypeListTabTxt = {
            [RankDataType.SOL_SCORE] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_SolRankList1", "段位"),
            [RankDataType.SOL_BRINGOUT_VALUE] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_SolRankList2", "带出价值"),
            [RankDataType.SOL_KILL] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_SolRankList3", "击败干员"),
            -- [RankDataType.SOL_REWARD] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_SolRankList4", "行动报酬"),
            -- [RankDataType.SOL_KILL_BOSS] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_SolRankList5", "击杀首领"),
            -- [RankDataType.SOL_MANDEL] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_SolRankList6", "曼德尔砖"),
            [RankDataType.TDM_SCORE] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_MpRankList1", "军功"),
            [RankDataType.TDM_KILL] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_MpRankList2", "击败干员"),
            [RankDataType.TDM_CAPTURE_ZONE_POINT] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_MpRankList3", "占点贡献"),
            [RankDataType.TDM_RESCUE_POINT]= NSLOCTEXT("RankingListModule", "Lua_RoleInfo_MpRankList4", "救治贡献"),
            -- [RankDataType.TDM_ENGINEER_POINT] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_MpRankList5", "工程贡献"),
            -- [RankDataType.TDM_TACTICAL_POINT] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_MpRankList6", "战术贡献"),
            [RankDataType.TDM_VICTORY_UNIT_SCORE] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_MpRankList7", "胜者为王"),
        },

        RankTypeListScoreTxt = {
            [RankDataType.SOL_SCORE] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_SolRankList1Score", "段位分"),
            [RankDataType.SOL_BRINGOUT_VALUE] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_SolRankList2", "带出价值"),
            [RankDataType.SOL_KILL] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_SolRankList3", "击败干员"),
            -- [RankDataType.SOL_REWARD] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_SolRankList4", "行动报酬"),
            -- [RankDataType.SOL_KILL_BOSS] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_SolRankList5", "击杀首领"),
            -- [RankDataType.SOL_MANDEL] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_SolRankList6", "曼德尔砖"),
            [RankDataType.TDM_SCORE] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_MpRankList1Score", "军功分"),
            [RankDataType.TDM_KILL] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_MpRankList2", "击败干员"),
            [RankDataType.TDM_CAPTURE_ZONE_POINT] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_MpRankList3", "占点贡献"),
            [RankDataType.TDM_RESCUE_POINT]= NSLOCTEXT("RankingListModule", "Lua_RoleInfo_MpRankList4", "救治贡献"),
            -- [RankDataType.TDM_ENGINEER_POINT] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_MpRankList5", "工程贡献"),
            -- [RankDataType.TDM_TACTICAL_POINT] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_MpRankList6", "战术贡献"),
            [RankDataType.TDM_VICTORY_UNIT_SCORE] = NSLOCTEXT("RankingListModule", "Lua_RoleInfo_MpRankList7", "胜者为王"),
        },
    },
    SolRankListImgTbl = {
        [1] = "PaperSprite'/Game/UI/UIAtlas/System/RankingList/BakedSprite/RankingList_Icon_0202.RankingList_Icon_0202'",
        [2] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Card_0001.Common_Card_0001'",
        [3] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Card_0004.Common_Card_0004'",
        -- [4] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Card_0010.Common_Card_0010'",
        -- [5] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Card_0011.Common_Card_0011'",
        -- [6] = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Market_Icon_0001.CommonHall_Market_Icon_0001'",
    },

    MpRankListImgTbl = {
        [1] = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Ranking_Icon_02.CommonHall_Ranking_Icon_02'",
        [2] = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Ranking_Icon_03.CommonHall_Ranking_Icon_03'",
        [3] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Card_0004.Common_Card_0004'",
        [4] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Card_0005.Common_Card_0005'",
        [5] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Card_0002.Common_Card_0002'",
        -- [5] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Details_0005.Common_Details_0005'",
        -- [6] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Details_0003.Common_Details_0003'",
    },
    MpChangeImg = "PaperSprite'/Game/UI/UIAtlas/System/RankingList/BakedSprite/RankingList_Icon_0103.RankingList_Icon_0103'",
    SolChangeImg = "PaperSprite'/Game/UI/UIAtlas/System/RankingList/BakedSprite/RankingList_Icon_0102.RankingList_Icon_0102'",
    ChAreaImg = "Texture2D'/Game/UI/UIAtlas/System/RankingList/Sp/AreaBg/RankingList_AreaBg_01.RankingList_AreaBg_01'",
    SolUnlockTitleImg = "Texture2D'/Game/UI/UIAtlas/System/RankingList/Sp/RankingList_Sp_03.RankingList_Sp_03'",
    MpUnlockTitleImg = "Texture2D'/Game/UI/UIAtlas/System/RankingList/Sp/RankingList_Sp_03.RankingList_Sp_04'",
    MpHistoryImg = "Texture2D'/Game/UI/UIAtlas/System/BattlePass/SP/BattlePass_Icon_02.BattlePass_Icon_02'",
    SolHistoryImg = "Texture2D'/Game/UI/UIAtlas/System/BattlePass/SP/BattlePass_Icon_01.BattlePass_Icon_01'",
    
    -- 排行榜类型（根据主页签索引映射）
    MpRankDataType = {
        RankDataType.TDM_SCORE,
        RankDataType.TDM_VICTORY_UNIT_SCORE,
        RankDataType.TDM_KILL,
        RankDataType.TDM_CAPTURE_ZONE_POINT,
        RankDataType.TDM_RESCUE_POINT,
        -- RankDataType.TDM_ENGINEER_POINT,
        -- RankDataType.TDM_TACTICAL_POINT,
    },

    SolRankDataType = {
        RankDataType.SOL_SCORE,
        RankDataType.SOL_BRINGOUT_VALUE,
        RankDataType.SOL_KILL,
        -- RankDataType.SOL_REWARD,
        -- RankDataType.SOL_KILL_BOSS,
        -- RankDataType.SOL_MANDEL,
    },

    RankTypeHashTbl = {
        [RankDataType.SOL_SCORE] = "PaperSprite'/Game/UI/UIAtlas/System/RankingList/BakedSprite/RankingList_Icon_0202.RankingList_Icon_0202'",
        [RankDataType.SOL_BRINGOUT_VALUE] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Card_0001.Common_Card_0001'",
        [RankDataType.SOL_KILL] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Card_0004.Common_Card_0004'",
        -- [RankDataType.SOL_REWARD] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Card_0010.Common_Card_0010'",
        -- [RankDataType.SOL_KILL_BOSS] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Card_0011.Common_Card_0011'",
        -- [RankDataType.SOL_MANDEL] = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Market_Icon_0001.CommonHall_Market_Icon_0001'",
        [RankDataType.TDM_SCORE] = "PaperSprite'/Game/UI/UIAtlas/System/RankingList/BakedSprite/RankingList_Icon_0201.RankingList_Icon_0201'",
        [RankDataType.TDM_KILL] =  "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Card_0004.Common_Card_0004'",
        [RankDataType.TDM_CAPTURE_ZONE_POINT] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Card_0005.Common_Card_0005'",
        [RankDataType.TDM_RESCUE_POINT] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Card_0002.Common_Card_0002'",
        -- [RankDataType.TDM_ENGINEER_POINT] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Card_0008.Common_Card_0008'",
        -- [RankDataType.TDM_TACTICAL_POINT] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Details_0003.Common_Details_0003'",
        [RankDataType.TDM_VICTORY_UNIT_SCORE] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Details_0001.Common_Details_0001'",
    },
}

return RankingListConfig