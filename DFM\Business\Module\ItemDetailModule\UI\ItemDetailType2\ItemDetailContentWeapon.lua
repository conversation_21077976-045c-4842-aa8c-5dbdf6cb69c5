----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMItemDetail)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

local ItemDetailContentBase = require("DFM.Business.Module.ItemDetailModule.UI.ItemDetailContentBase")
local ItemDetailConfig = require("DFM.Business.Module.ItemDetailModule.ItemDetailConfig")
local FWeaponDataAttribute = import "WeaponDataAttribute"
local UAssembleWeaponDataLibrary = import "AssembleWeaponDataLibrary"
local EModularPartNodeType = import "EModularPartNodeType"
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local UWeaponBlueprintLibrary = import "WeaponBlueprintLibrary"
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ItemDetailLogic = require "DFM.Business.Module.ItemDetailModule.ItemDetailLogic"
local RecycleContainer = require "DFM.Business.DataStruct.Container.RecycleContainer"
local GunsmithWeaponSocketBaseData = require "DFM.Business.DataStruct.GunsmithStruct.GunsmithWeaponSocketBaseData"

---@class ItemDetailContentWeapon : ItemDetailContentBase
local ItemDetailContentWeapon = ui("ItemDetailContentWeapon", ItemDetailContentBase)

function ItemDetailContentWeapon:Ctor()
    self._itemInfo = nil

    -- Weapon 0
	self._wtWeaponBox = self:Wnd("WeaponVerticalBox", UIWidgetBase)
	-- self._wtWeaponDetailBtn = self:Wnd("wWeaponDetailBtn", UIButton)
	-- self._wtWeaponDetailBtn:Event("OnClicked", self._OnWeaponDetailBtnClicked, self)
	-- self._wtDamageText = self:Wnd("wDamageItemValue", UITextBlock)
	-- self._wtAmmoText = self:Wnd("wAmmoItemValue", UITextBlock)
	-- self._wtDamageText = self:Wnd("wDamageItem", UIWidgetBase):Wnd("wValueText", UITextBlock)
	-- self._wtAmmoText = self:Wnd("wAmmoItem", UIWidgetBase):Wnd("wValueText", UITextBlock)

	-- 当前方案属性
	self._wtWeaponAttr = self:Wnd("DFCanvasPanel_58", UIWidgetBase)

	-- 二级武器详情
	self._wtDetailAttr = self:Wnd("WBP_DFCommonButtonV3S1", DFCommonButtonOnly)
	self._wtDetailAttr:Event("OnClicked", self.OnBtnClicked, self)

	-- 操控速度
	self._wtHandlingItem = self:Wnd("WBP_ItemDetailContent_LimitGroup", UIWidgetBase)
	-- 优势射程
	self._wtEffectiveRange = self:Wnd("WBP_ItemDetailContent_LimitGroup_1", UIWidgetBase)
	-- 后坐力控制	
	self._wtRecoilControlItem = self:Wnd("WBP_ItemDetailContent_LimitGroup_2", UIWidgetBase)
	-- 腰际射击精度
	self._wtWaistItem = self:Wnd("WBP_ItemDetailContent_LimitGroup_3", UIWidgetBase)
	-- 武器稳定性
	self._wtStabilityItem = self:Wnd("WBP_ItemDetailContent_LimitGroup_4", UIWidgetBase)
	-- 伤害	
	self._wtHarmItem = self:Wnd("WBP_ItemDetailContent_LimitGroup_5", UIWidgetBase)

	-- 护甲伤害
	self._wtArmorDamageEffectPanel = self:Wnd("WBP_ItemDetailContent_Effect_4", UIWidgetBase)
	self._wtArmorDamageEffect = self._wtArmorDamageEffectPanel:Wnd("DFTextBlock", UITextBlock)

	-- 射速属性
	self._wtFireSpeedEffectPanel = self:Wnd("WBP_ItemDetailContent_Effect", UIWidgetBase)
	self._wtFireSpeedEffect = self._wtFireSpeedEffectPanel:Wnd("DFTextBlock", UITextBlock)

	-- 容量属性
	self._wtBulletCapacityEffectPanel = self:Wnd("WBP_ItemDetailContent_Effect_1", UIWidgetBase)
	self._wtBulletCapacityEffect = self._wtBulletCapacityEffectPanel:Wnd("DFTextBlock", UITextBlock)

	-- 开火模式
	self._wtFireModeEffectPanel = self:Wnd("WBP_ItemDetailContent_Effect_2", UIWidgetBase)
	self._wtFireModeEffect = self._wtFireModeEffectPanel:Wnd("DFTextBlock", UITextBlock)

	-- 枪口初速
	self._wtInitialSpeedEffectPanel = self:Wnd("WBP_ItemDetailContent_Effect_3", UIWidgetBase)
	self._wtInitialSpeedEffect = self._wtInitialSpeedEffectPanel:Wnd("DFTextBlock", UITextBlock)

	-- 枪声传播距离
	self._wtShotDistanceEffectPanel = self:Wnd("WBP_ItemDetailContent_Effect_5", UIWidgetBase)
	self._wtShotDistanceEffect = self._wtShotDistanceEffectPanel:Wnd("DFTextBlock", UITextBlock)

	-- 护甲伤害衰减水平
	self._wtArmorDamageLevel = self:Wnd("WBP_ItemDetailContent_Effect_6", UIWidgetBase)
	self._wtArmorDamageLevel:Collapsed()

	-- 武器配置界面专用的武器描述
    self._wtWeaponDesc = self:Wnd("wDesc", UITextBlock)

	-- 枪械配件
	self._wtWeaponAdaptsContent = self:Wnd("wWeaponAdaptsContent", UIWidgetBase)
	self._wtWeaponAdaptsWrapBox = self:Wnd("wWeaponAdaptsWrapBox", UIWidgetBase)

	-- 显示枪械原属性
	self._wtCheckBox = self:Wnd("wtDFCommonCheckBoxWithText", DFCheckBoxWithText)
	self._wtCheckBox:Event("OnCheckStateChanged", self._OnCheckStateChanged, self)
	self._wtCheckBox:SelfHitTestInvisible()

    self._bCanDragAdapter = false
	self._bAdapterCanFastUnequip = false
	self._bAdapterTipsShowBtton = false
    self._allAdapterWeakUI = {} -- 这个数组会存2种不同的数据，虽然代码逻辑上不可能出现，但用到接口的地方如果是子类特有的都要判空

    self._weaponAttrOffset = nil
	self._weaponAttrAlignment = FVector2D(0, 0)

    self._weaponShowState = ItemDetailConfig.ETipsWeaponShowState.Auto
	self._isTearAdapter = false
	self._weaponTipsShowDirection = nil
	self._weaponTipsAlignWidget = nil
	-- self._weaponTipsHandle = nil

	self._partSocketdataContainer = RecycleContainer:NewIns(GunsmithWeaponSocketBaseData)
	self._socketArray = {}
end

function ItemDetailContentWeapon:OnInitExtraData(itemDetailView)
	self._itemDetailView = itemDetailView
end

-- BEGIN MODIFICATION @ VIRTUOS : 隐藏时自动清除注册的快捷键
function ItemDetailContentWeapon:OnHideBegin()
	if IsHD() then
    	self:RemoveShortcuts()
	end
end
-- END MODIFICATION

function ItemDetailContentWeapon:OnShow()
	self:_AddEventListener()
end

function ItemDetailContentWeapon:OnHide()
	self:_RemoveEventListener()
end

function ItemDetailContentWeapon:OnClose()
	Facade.UIManager:RemoveSubUIByParent(self, self._wtWeaponAdaptsWrapBox)
	self._allAdapterWeakUI = {}
	Facade.LuaFramingManager:CancelAllFrameTasks(self)
	-- if self._weaponTipsHandle then
	-- 	Module.ItemDetail:CloseItemTipsByHandle(self._weaponTipsHandle)
	-- 	self._weaponTipsHandle = nil
	-- 	return
	-- end

	-- BEGIN MODIFICATION @ VIRTUOS : 防止外部控件调用移除导航组失败，自己在关闭的时候移除下。
	if IsHD() then
		self:RemoveNavigationDetailContentWeapon()
	end
	-- END MODIFICATION
end

function ItemDetailContentWeapon:Destroy()
	releaseobject(self._partSocketdataContainer)
	self._partSocketdataContainer = nil
	self._weaponAttribute = nil
end

function ItemDetailContentWeapon:_AddEventListener()
	if not Facade.GameFlowManager:CheckIsInFrontEnd() and Server.ArmedForceServer:GetCurArmedForceMode() == EArmedForceMode.MP then
		UDFMGameHudDelegates.Get(GetGameInstance()).OnWeaponStoreHudOnHide:Add(ItemDetailLogic.OnWeaponStoreClose)
	end
end

function ItemDetailContentWeapon:_RemoveEventListener()
	UDFMGameHudDelegates.Get(gameIns).OnWeaponStoreHudOnHide:Remove(ItemDetailLogic.OnWeaponStoreClose)
	self:RemoveAllLuaEvent()
end

function ItemDetailContentWeapon:_Reset()
	self._socketArray = {}
	self._usedAdapterWeakUIPointer = 0
	for _, weakUI in ipairs(self._allAdapterWeakUI) do
		local uIIns = getfromweak(weakUI)
		if uIIns and isvalid(uIIns) and uIIns.IsValid and uIIns:IsValid() then
			uIIns:Collapsed()
		end
	end
	Facade.LuaFramingManager:CancelAllFrameTasks(self)
	if self._partSocketdataContainer then
		self._partSocketdataContainer:Reset()
	end
	self._wtCheckBox:SelfHitTestInvisible()
	self._wtWeaponDesc:SelfHitTestInvisible()
end

function ItemDetailContentWeapon:Reset()
	self._bCanDragAdapter = false
	self._bAdapterCanFastUnequip = false
	self._bAdapterTipsShowBtton = false
	self._weaponAttrAlignment = FVector2D(0, 0)
    self._weaponShowState = ItemDetailConfig.ETipsWeaponShowState.Auto
	self._wtHandlingItem:SetVisibility(ESlateVisibility.Collapsed)
	self._wtEffectiveRange:SetVisibility(ESlateVisibility.Collapsed)
	self._wtRecoilControlItem:SetVisibility(ESlateVisibility.Collapsed)
	self._wtWaistItem:SetVisibility(ESlateVisibility.Collapsed)
	self._wtStabilityItem:SetVisibility(ESlateVisibility.Collapsed)
	self._wtHarmItem:SetVisibility(ESlateVisibility.Collapsed)
	self:SetWeaponTipsShowDirection(nil)
end

---@param item ItemBase
function ItemDetailContentWeapon:SetItem(item, exArgs)
    self._itemInfo = item
    if exArgs then
        if exArgs.bCanDragAdapter then
            self._bCanDragAdapter = exArgs.bCanDragAdapter
        end
        if exArgs.bAdapterCanFastUnequip then
            self._bAdapterCanFastUnequip = exArgs.bAdapterCanFastUnequip
        end
        if exArgs.bAdapterTipsShowBtton then
            self._bAdapterTipsShowBtton = exArgs.bAdapterTipsShowBtton
        end
        if exArgs.weaponShowState then
            self._weaponShowState = exArgs.weaponShowState
        end
    end
	self:_Reset()
	self:_SetWeaponDetail()
	self:_ShowWeaponPrepare()

	-- self._wtWeaponDetailBtn:SetVisibility(ESlateVisibility.Collapsed)
	--if Facade.GameFlowManager:CheckIsInFrontEnd() then
	--	self._wtWeaponDetailBtn:SetVisibility(ESlateVisibility.Visible)
	--else
	--	self._wtWeaponDetailBtn:SetVisibility(ESlateVisibility.Collapsed)
	--end
end

function ItemDetailContentWeapon:_SetWeaponDetail()
	local itemDetailField = Module.ItemDetail.Field
	local weaponFeature = self._itemInfo:GetFeature(EFeatureType.Weapon)
	-- 展示分
	local desc = self._itemInfo:GetRawDescObj()
	if not self._weaponAttribute then
		self._weaponAttribute = FWeaponDataAttribute()
	end
	self._weaponAttribute = WeaponHelperTool.GetWeaponDataAttribute(self._weaponAttribute, desc)
	local weaponDataAttr = self._weaponAttribute
	local bPoorWeapon = weaponFeature:IsShowPoorWeapon() -- TODO AI预设武器
	local bShowPoorWeaponInfo = self._weaponShowState == ItemDetailConfig.ETipsWeaponShowState.PoorShowDiff or
								self._weaponShowState == ItemDetailConfig.ETipsWeaponShowState.PoorNotShowDiff or
								(self._weaponShowState == ItemDetailConfig.ETipsWeaponShowState.Auto and bPoorWeapon)
	local poorWeaponId = weaponFeature:GetPoorWeaponId()
	-- 普通武器
	local damage, poorDamage, damageStr
	damage, damageStr = WeaponHelperTool.GetWeaponAttDamge(weaponDataAttr)
	poorDamage = damage
	if poorWeaponId > 0 then
		local poorDamageStr
		poorDamage, poorDamageStr = WeaponHelperTool.GetPoorWeaponAttDamge(weaponDataAttr, poorWeaponId)
		if bShowPoorWeaponInfo then
			damageStr = poorDamageStr
		end
	end
	local armorDamage = WeaponHelperTool.GetWeaponArmorDamge(weaponDataAttr)
	local _, clipAmmoStr = WeaponHelperTool.GetWeaponAttCarriedAmmoCount(weaponDataAttr)
	local _, maxCarriedAmmoStr = WeaponHelperTool.GetWeaponAttMaxCarriedAmmo(weaponDataAttr)
	local initialSpeed, initialSpeedStr = WeaponHelperTool.GetFlySpeed(weaponDataAttr)
	local fireMode = WeaponHelperTool.GetFireMode(weaponDataAttr)
	local _, fireRateStr = WeaponHelperTool.GetWeaponAttFireRate(weaponDataAttr)
	local harmValue, armorDamageValue = self:CalcWeaponHarm(damage, armorDamage)

	-- 获取装备的子弹
	local curBulletId = 0
	if desc then
		curBulletId = desc:GetCurrentAmmoItemId()
	end

	local StrHarmValue = tostring(harmValue)
	local StrArmorDamageValue = tostring(armorDamageValue)
	local strProjectileNum = nil

	-- 霰弹枪伤害显示改为伤害*弹数
	if self._itemInfo.itemSubType == ItemConfig.EWeaponItemType.Shotgun then
		if curBulletId ~= 0 then
			local AmmoConfig = WeaponHelperTool.GetAmmoConfig(curBulletId)
			if AmmoConfig and AmmoConfig.ProjectileNum > 1 then
        		local StrVaule = tostring(AmmoConfig.ProjectileNum)
            	StrVaule = string.gsub(StrVaule, "%.0+$", "")
				strProjectileNum = StrVaule
				StrHarmValue = StringUtil.SequentialFormat("{0}*{1}", StrHarmValue, strProjectileNum)
				StrArmorDamageValue = StringUtil.SequentialFormat("{0}*{1}", StrArmorDamageValue, strProjectileNum)
			end
		else
			strProjectileNum = WeaponAssemblyTool.GetProjectileNumPerShot(weaponFeature:GetWeaponRecId())
			StrHarmValue = StringUtil.SequentialFormat("{0}*{1}", StrHarmValue, strProjectileNum)
			StrArmorDamageValue = StringUtil.SequentialFormat("{0}*{1}", StrArmorDamageValue, strProjectileNum)
		end
	end
	self._wtBulletCapacityEffect:SetText(clipAmmoStr)
	self._wtFireSpeedEffect:SetText(fireRateStr)
	self._wtHarmItem:SetVisibility(ESlateVisibility.HitTestInvisible)
	self._wtHarmItem:SetDescText(StrHarmValue)
	self._wtHarmItem:SetProgress(math.clamp(harmValue / 100, 0, 1))
	self._wtHarmItem:SetSliderNum(harmValue)
	self._wtArmorDamageEffect:SetText(tostring(StrArmorDamageValue))
	self._wtInitialSpeedEffect:SetText(initialSpeedStr)
	local fireModeStrTable = {}
	for key, value in pairs(fireMode) do
		table.insert(fireModeStrTable, ItemDetailConfig.EFireMode[value])
	end
	if #fireModeStrTable == 1 then
		self._wtFireModeEffect:SetText(fireModeStrTable[1])
	elseif #fireModeStrTable == 2 then
		self._wtFireModeEffect:SetText(string.format(ItemDetailConfig.Loc.FireMode, fireModeStrTable[1], fireModeStrTable[2]))
	elseif #fireModeStrTable >= 3 then
		self._wtFireModeEffect:SetText(string.format(ItemDetailConfig.Loc.FireMode2, fireModeStrTable[1], fireModeStrTable[2], fireModeStrTable[3]))
	end

	self._additionalInfo = UWeaponBlueprintLibrary.GetItemDetailValue(desc)
	local gunItem = WeaponAssemblyTool.GetPresetPreviewGunFromRecId(weaponFeature:GetPresetWeaponId())
	if not gunItem then
		gunItem = WeaponAssemblyTool.CreatePreviewPresetGun(weaponFeature:GetPresetWeaponId())
	end
	if not gunItem then
		loginfo("Can't get gunItem, plz check")
		return
	end
	local tmpDesc = gunItem:GetModularDesc()
	self._namalAdditionalInfo = UWeaponBlueprintLibrary.GetItemDetailValue(tmpDesc)
	self:_NoWeaponPrepare()

	-- 处理配件
	-- Facade.UIManager:RemoveSubUIByParent(self, self._wtWeaponAdaptsWrapBox)
	-- self._allAdapterWeakUI = {}

	local allNodeTree = UAssembleWeaponDataLibrary.GetAssemblePartNodesFromeDesc(desc)
	local allWeaponPartNodes = allNodeTree.AllNodes ---@type TArray<FGPAssemblePartNode>
	local rootNodeSocketList = nil
	local adapterIdArray = {}

	for key, node in pairs(allWeaponPartNodes) do
		if node.NodeType == EModularPartNodeType.Adpater then
			if node.AllSocketList:Num() > 0 then
				if rootNodeSocketList == nil then
					rootNodeSocketList = node.AllSocketList
				end
				for _, socket in pairs(node.AllSocketList) do
					local tempNode = nil
					if socket.AttachPartIndex >= 0 then
						tempNode = allWeaponPartNodes:Get(socket.AttachPartIndex)
					end
					local luaSocket = self._partSocketdataContainer:Add()
					luaSocket:SetData(socket, tempNode, socket.flag)

					local bShowable = Module.Gunsmith:GetIsShowSocketNode(luaSocket:GetValidPartItemList())
					if luaSocket:CanBeInteraction() and bShowable then
						table.insert(self._socketArray, luaSocket)
					end
				end
			end
		end
	end
	if #self._socketArray == 0 then
		self._wtWeaponAdaptsContent:SetVisibility(ESlateVisibility.Collapsed)
	else
		local function getSocketIdInRootNode(nodeIdx)
			for idx, socket in ipairs(rootNodeSocketList) do
				if socket.AttachPartIndex == nodeIdx then
					return socket.SocketId
				end
			end
			return 0
		end
        local sortFunc = function(socket1, socket2)
            -- 判断强化套件
            if socket1:GetSocketPartType() == ItemConfig.EAdapterItemType.Attacker then
                return true
            end
            if socket2:GetSocketPartType() == ItemConfig.EAdapterItemType.Attacker then
                return false
            end
            -- 判断是否子配件，子配件的话找父节点排序
            if socket1:GetParentPartIndex() == socket2:GetParentPartIndex() then
                return socket1:GetSocketID() < socket2:GetSocketID()
            end
            if socket1:GetParentPartIndex() == 0 then
                -- 父配件排到子配件前面
                if socket2:GetParentPartIndex() == socket1:GetAttachPartIndex() then
                    return true
                end
                return socket1:GetSocketID() < getSocketIdInRootNode(socket2:GetParentPartIndex())
            end
            if socket2:GetParentPartIndex() == 0 then
                -- 父配件排到子配件前面
                if socket1:GetParentPartIndex() == socket2:GetAttachPartIndex() then
                    return false
                end
                return getSocketIdInRootNode(socket1:GetParentPartIndex()) < socket2:GetSocketID()
            end
            return getSocketIdInRootNode(socket1:GetParentPartIndex()) < getSocketIdInRootNode(socket2:GetParentPartIndex())
        end
		table.sort(self._socketArray, sortFunc)
		self._wtWeaponAdaptsContent:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
		local bInMpMode = armedForceMode == EArmedForceMode.MP
		for idx, socket in ipairs(self._socketArray) do
			if (socket:GetSocketPartType() ~= ItemConfig.EAdapterItemType.Attacker) and (socket:IsShowPart()) then
				table.insert(adapterIdArray, socket:GetAttachItemId())
			end
		end

		if bInMpMode and (not Facade.GameFlowManager:CheckIsInFrontEnd()) then
			local function fLoadFinLogic()
				for idx, socket in ipairs(self._socketArray) do
					if socket:GetSocketPartType() ~= ItemConfig.EAdapterItemType.Attacker then
						self._usedAdapterWeakUIPointer = self._usedAdapterWeakUIPointer + 1
						local uIIns = getfromweak(self._allAdapterWeakUI[self._usedAdapterWeakUIPointer])
						if uIIns and isvalid(uIIns) and uIIns.IsValid and uIIns:IsValid() then
							uIIns:ReInit(socket, self._itemInfo, self)
							uIIns:SelfHitTestInvisible()
							self:_AfterLoadSubUI()
						else
							Facade.LuaFramingManager:RegisterFrameTask(self.AddAdapteImge, self, {socket})
						end
					end
				end
			end
			local function fLoadFinCallback(mapPath2ResIns)
				fLoadFinLogic()
			end
			if Facade.UIManager:CheckUIHasBeenLoaded(UIName2ID.ItemDetailAdapterItemSimple) then
				fLoadFinLogic()
			else
				Facade.UIManager:AsyncLoadUIResOnly(UIName2ID.ItemDetailAdapterItemSimple, fLoadFinCallback, nil)
			end
		else
			local function fLoadFinLogic()
				for idx, socket in ipairs(self._socketArray) do
					if socket:GetSocketPartType() ~= ItemConfig.EAdapterItemType.Attacker then
						self._usedAdapterWeakUIPointer = self._usedAdapterWeakUIPointer + 1
						local uIIns = getfromweak(self._allAdapterWeakUI[self._usedAdapterWeakUIPointer])
						if uIIns and isvalid(uIIns) and uIIns.IsValid and uIIns:IsValid() then
							uIIns:ReInit(socket, self._itemInfo, self)
							self:OnCreateSubUIFinished(uIIns, socket)
						else
							Facade.LuaFramingManager:RegisterFrameTask(self.AddAdapteItem, self, {socket, bShowPoorWeaponInfo})
						end
					end
				end
			end
			local function fLoadFinCallback(mapPath2ResIns)
				fLoadFinLogic()
			end
			if Facade.UIManager:CheckUIHasBeenLoaded(UIName2ID.ItemDetailAdapterItem) then
				fLoadFinLogic()
			else
				Facade.UIManager:AsyncLoadUIResOnly(UIName2ID.ItemDetailAdapterItem, fLoadFinCallback, nil)
			end
		end
	end

	-- 穿甲信息
	local penetrateLevel = WeaponHelperTool.GetPenetrateLevel(weaponDataAttr)
	penetrateLevel = setdefault(penetrateLevel, 0)
    penetrateLevel = math.ceil(penetrateLevel)

	-- 武器描述
	local outStr = ""
	self._wtWeaponDesc:SelfHitTestInvisible()
	local realWeaponCfg = ItemConfigTool.GetItemConfigById(self._itemInfo:GetFeature():GetWeaponRecId())
	if realWeaponCfg then
		outStr = realWeaponCfg.description
	end
	self._wtWeaponDesc:SetText(outStr)

	-- MP模式屏蔽属性
	local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
	if armedForceMode == EArmedForceMode.MP then
		self._wtArmorDamageEffectPanel:SetVisibility(ESlateVisibility.Collapsed)
		self._wtShotDistanceEffectPanel:SetVisibility(ESlateVisibility.Collapsed)
	else
		-- 枪声传播距离
		local recId = weaponFeature:GetWeaponRecId()
		local attributeConfig = WeaponHelperTool.GetWeaponAttributeConfig(recId)
		local shotDistance = ""
		if attributeConfig then
			shotDistance = attributeConfig.ShotDistance or 100
			for idx, adpaterId in ipairs(adapterIdArray) do
				local FunctionParamRow = ItemDetailLogic.GetFunctionParamByAdapterId(adpaterId)
				if FunctionParamRow then
					shotDistance = shotDistance * FunctionParamRow.Param3
				end
			end
			shotDistance = MathUtil.GetRoundingNum(shotDistance)
			shotDistance = string.format(ItemDetailConfig.Loc.weaponRange, shotDistance)
		end
		self._wtShotDistanceEffect:SetText(shotDistance)
		self._wtArmorDamageEffectPanel:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self._wtShotDistanceEffectPanel:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	end

	-- 局内屏蔽
	local bInGame = not Facade.GameFlowManager:CheckIsInFrontEnd()
	if bInGame then
		self._wtDetailAttr:Collapsed()
	else
		self._wtDetailAttr:SelfHitTestInvisible()
	end
end

-- function ItemDetailContentWeapon:_OnWeaponDetailBtnClicked()
-- 	if self._weaponTipsHandle then
-- 		Module.ItemDetail:CloseItemTipsByHandle(self._weaponTipsHandle)
-- 		self._weaponTipsHandle = nil
-- 		return
-- 	end

-- 	local ETipsLayerLevel = Module.ItemDetail.Config.ETipsLayerLevel
--     Module.ItemDetail:CloseItemDetailPanel(ETipsLayerLevel.Second)
-- 	local alignWidget = self._weaponTipsAlignWidget or self._wtWeaponDetailBtn
-- 	local topTipsHandle = Module.ItemDetail.Field:GetTopPanelHandle()
-- 	if topTipsHandle then
-- 		alignWidget = topTipsHandle:GetUIIns():GetWidget()
-- 	end
-- 	local layerLevel = Module.ItemDetail.Field:GetPanelHandleNum() + 1
-- 	local weaponDetailHandle = Module.FastEquip:OpenFastEquipView(self._itemInfo, self._weaponAttrOffset, self._wtWeaponDetailBtn, self._weaponAttrAlignment, nil, function (uiIns)
-- 		if alignWidget then
-- 			uiIns:UpdatePosition(alignWidget, self._weaponTipsShowDirection)
-- 		end
-- 	end, layerLevel)

-- 	Module.ItemDetail.Field:SetPanelHandleByLevel(layerLevel, weaponDetailHandle)
-- 	self._weaponTipsHandle = weaponDetailHandle
-- end

function ItemDetailContentWeapon:_NoWeaponPrepare()
	loginfo("ItemDetailContentWeapon:_NoWeaponPrepare")
	for index,value in pairs(self._additionalInfo) do
		local preValue = self._namalAdditionalInfo:Get(index)
		if index == 0 then
		elseif index == 1 then
		elseif index == 2 then
			-- 优势射程
			self._wtEffectiveRange:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
			self._wtEffectiveRange:SetDescText(string.format(ItemDetailConfig.Loc.weaponRange, MathUtil.GetRoundingNum(value)))
			self._wtEffectiveRange:SetProgress(math.clamp(value / 150, 0, 1))
			self._wtEffectiveRange:SetSliderScope(0, 150)
			self._wtEffectiveRange:SetSliderNum(preValue)
		elseif index == 3 then
			-- 后坐力控制
			self._wtRecoilControlItem:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
			-- self._wtRecoilControlItem:SetDescText(string.format("%d", value))
			self._wtRecoilControlItem:SetDescText(tostring(MathUtil.GetRoundingNum(value)))
			self._wtRecoilControlItem:SetProgress(math.clamp(value / 100, 0, 1))
			self._wtRecoilControlItem:SetSliderNum(preValue)
		elseif index == 4 then
			-- 腰际射击精度
			self._wtWaistItem:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
			-- self._wtWaistItem:SetDescText(string.format("%d", value))
			self._wtWaistItem:SetDescText(tostring(MathUtil.GetRoundingNum(value)))
			self._wtWaistItem:SetProgress(math.clamp(value / 100, 0, 1))
			self._wtWaistItem:SetSliderNum(preValue)
		elseif index == 5 then
			-- 操控速度
			self._wtHandlingItem:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
			self._wtHandlingItem:SetDescText(tostring(MathUtil.GetRoundingNum(value)))
			self._wtHandlingItem:SetProgress(math.clamp(value / 100, 0, 1))
			self._wtHandlingItem:SetSliderNum(preValue)
		elseif index == 6 then
			-- 武器稳定性
			self._wtStabilityItem:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
			self._wtStabilityItem:SetDescText(tostring(MathUtil.GetRoundingNum(value)))
			self._wtStabilityItem:SetProgress(math.clamp(value / 100, 0, 1))
			self._wtStabilityItem:SetSliderNum(preValue)
		end
	end
	-- BEGIN MODIFICATION @ VIRTUOS : Navigation
	self._wtHarmItem:SetSliderVisible(ESlateVisibility.SelfHitTestInvisible)
	-- END MODIFICATION
end

function ItemDetailContentWeapon:SetAdapterDragState(bCanDrag)
    self:_TryCallSelfWaitLoadFunc(self._SetAdapterDragState, {bCanDrag})
end

function ItemDetailContentWeapon:SetAdapterTipsShowButtonState(bTipsShowBtton)
    self:_TryCallSelfWaitLoadFunc(self._SetAdapterTipsShowButtonState, {bTipsShowBtton})
end

function ItemDetailContentWeapon:SetAdapterFastUnequipState(bCanFastUnequip)
    self:_TryCallSelfWaitLoadFunc(self._SetAdapterFastUnequipState, {bCanFastUnequip})
end

function ItemDetailContentWeapon:_SetAdapterDragState(bCanDrag)
    self._bCanDragAdapter = bCanDrag
	for _, weakUIIns in ipairs(self._allAdapterWeakUI) do
		local uiIns = getfromweak(weakUIIns)
		if uiIns and uiIns.SetDragState then
			uiIns:SetDragState(self._bCanDragAdapter)
		end
    end
end

function ItemDetailContentWeapon:_SetAdapterTipsShowButtonState(bTipsShowBtton)
    self._bAdapterTipsShowBtton = bTipsShowBtton
	for _, weakUIIns in ipairs(self._allAdapterWeakUI) do
		local uiIns = getfromweak(weakUIIns)
		if uiIns and uiIns.SetTipsShowButtonState then
			uiIns:SetTipsShowButtonState(self._bAdapterTipsShowBtton)
		end
    end
end

function ItemDetailContentWeapon:_SetAdapterFastUnequipState(bCanFastUnequip)
    self._bAdapterCanFastUnequip = bCanFastUnequip
	for _, weakUIIns in ipairs(self._allAdapterWeakUI) do
		local uiIns = getfromweak(weakUIIns)
		if uiIns and uiIns.SetFastUnequipState then
			uiIns:SetFastUnequipState(self._bAdapterCanFastUnequip)
		end
    end
end

-- 设置打开武器属性界面的顶点和锚点，默认按钮位置
function ItemDetailContentWeapon:SetWeaponAttrOffset(weaponAttrOffset, weaponAttrAlignment)
	self._weaponAttrOffset = weaponAttrOffset
	self._weaponAttrAlignment = weaponAttrAlignment
end

---@param showState ETipsWeaponShowState
function ItemDetailContentWeapon:SetWeaponShowState(showState)
    self._weaponShowState = showState
end

function ItemDetailContentWeapon:SetWeaponAdapterVisible(bVisible)
	if bVisible then
		self._wtWeaponAdaptsContent:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	else
		self._wtWeaponAdaptsContent:SetVisibility(ESlateVisibility.Collapsed)
	end
end

-- 隐藏所有tips的显示，包括配件的点击
function ItemDetailContentWeapon:HideAllTipsBtn()
	self:_TryCallSelfWaitLoadFunc(self._HideAllTipsBtn, {})
end

function ItemDetailContentWeapon:_HideAllTipsBtn()
	for _, weakUIIns in ipairs(self._allAdapterWeakUI) do
		local uiIns = getfromweak(weakUIIns)
		if uiIns and uiIns.SetCanClick then
			uiIns:SetCanClick(false)
		end
    end
end

function ItemDetailContentWeapon:SetWeaponTipsShowDirection(direction)
	self._weaponTipsShowDirection = direction
end

function ItemDetailContentWeapon:SetBigTipsAlignWidget(widget)
	self._weaponTipsAlignWidget = widget
end

function ItemDetailContentWeapon:CalcWeaponHarm(damageValue, armorDamageValue)
	-- 通过子弹判断肉伤加成和甲伤加成
	local curBulletId
	local weaponFeature = self._itemInfo:GetFeature(EFeatureType.Weapon)
	if weaponFeature and weaponFeature:GetRawDescObj() then
		curBulletId = weaponFeature:GetRawDescObj():GetCurrentAmmoItemId()
	else
		return damageValue, armorDamageValue
	end

	local harmValue = damageValue
	local armorDamageValue = armorDamageValue
	if curBulletId ~= 0 then
		local bulletCfg = WeaponHelperTool.GetAmmoConfig(curBulletId)
		if bulletCfg then
			harmValue = harmValue * bulletCfg.DamageRate
			armorDamageValue = armorDamageValue * bulletCfg.ArmorDamageRate
		end
	end

	return math.round(harmValue), math.round(armorDamageValue)
end

function ItemDetailContentWeapon:OnBtnClicked()
	Facade.UIManager:AsyncShowUI(UIName2ID.ItemDetail_SecondWeaponDetaill, nil, nil, self._itemInfo)
end

function ItemDetailContentWeapon:SetWeaponAttrVisible(bVisible)
	if bVisible then
		self._wtWeaponAttr:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	else
		self._wtWeaponAttr:SetVisibility(ESlateVisibility.Collapsed)
	end
end

function ItemDetailContentWeapon:_OnCheckStateChanged()
    local bChecked = self._wtCheckBox:GetIsChecked()
	if bChecked then
		LogAnalysisTool.DoSendOpenUILog(LogAnalysisTool.EItemDetailFromUIType.WeaponCompared)
		-- Module.ItemDetail.Config.evtShowWeaponPrepare:Invoke(self._wtCheckBox)
		self:_ShowWeaponPrepare()
	else
		self:_HideWeaponPrepare()
	end
end

function ItemDetailContentWeapon:_ShowWeaponPrepare()
	-- BEGIN MODIFICATION @ VIRTUOS : Navigation
	self._wtHandlingItem:SetSliderVisible(ESlateVisibility.SelfHitTestInvisible)
	self._wtEffectiveRange:SetSliderVisible(ESlateVisibility.SelfHitTestInvisible)
	self._wtRecoilControlItem:SetSliderVisible(ESlateVisibility.SelfHitTestInvisible)
	self._wtWaistItem:SetSliderVisible(ESlateVisibility.SelfHitTestInvisible)
	self._wtStabilityItem:SetSliderVisible(ESlateVisibility.SelfHitTestInvisible)
	self._wtHarmItem:SetSliderVisible(ESlateVisibility.SelfHitTestInvisible)
	-- END MODIFICATION
	self._wtCheckBox:SetIsChecked(true, false)
end

function ItemDetailContentWeapon:_HideWeaponPrepare()
	self._wtHandlingItem:SetSliderVisible(ESlateVisibility.Collapsed)
	self._wtEffectiveRange:SetSliderVisible(ESlateVisibility.Collapsed)
	self._wtRecoilControlItem:SetSliderVisible(ESlateVisibility.Collapsed)
	self._wtWaistItem:SetSliderVisible(ESlateVisibility.Collapsed)
	self._wtStabilityItem:SetSliderVisible(ESlateVisibility.Collapsed)
	self._wtHarmItem:SetSliderVisible(ESlateVisibility.Collapsed)
	self._wtCheckBox:SetIsChecked(false, false)
end

-- 此接口由局内C++调用
function ItemDetailContentWeapon:BattleFieldStyle()
	if self._wtCheckBox and self._wtCheckBox.Collapsed then
		self._wtCheckBox:Collapsed()
	end
	if self._wtWeaponDesc and self._wtWeaponDesc.Collapsed then
		self._wtWeaponDesc:Collapsed()
	end
end

function ItemDetailContentWeapon:OnCreateSubUIFinished(itemIns, socket)
	itemIns:SetVisibility(socket:IsShowPart() and ESlateVisibility.Visible or ESlateVisibility.HitTestInvisible)
	itemIns:SetDragState(self._bCanDragAdapter)
	itemIns:SetTipsShowButtonState(self._bAdapterTipsShowBtton)
	itemIns:SetFastUnequipState(self._bAdapterCanFastUnequip)
	itemIns:SetOnPoorWeapon(bShowPoorWeaponInfo)
	itemIns:SetSizeSmall()
	itemIns:SetTitleVisible(false)

	-- MP模式下配件不显示品阶
	local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
	if armedForceMode == EArmedForceMode.MP then
		itemIns._wtQualityBp:SetVisibility(ESlateVisibility.Collapsed)
	end

	self:_AfterLoadSubUI()
end

function ItemDetailContentWeapon:AddAdapteItem(socket, bShowPoorWeaponInfo)
	local weakUIIns = Facade.UIManager:AddSubUI(self, UIName2ID.ItemDetailAdapterItem, self._wtWeaponAdaptsWrapBox, nil, socket, self._itemInfo, self)
	if weakUIIns then
		local uiIns = getfromweak(weakUIIns)
		if uiIns then
			table.insert(self._allAdapterWeakUI, weakUIIns)
			self:OnCreateSubUIFinished(uiIns, socket)
		end
	end
end

function ItemDetailContentWeapon:AddAdapteImge(socket)
	local weakUIIns = Facade.UIManager:AddSubUI(self, UIName2ID.ItemDetailAdapterItemSimple, self._wtWeaponAdaptsWrapBox, nil, socket)
	if weakUIIns then
		local uiIns = getfromweak(weakUIIns)
		if uiIns then
			table.insert(self._allAdapterWeakUI, weakUIIns)
			uiIns:SelfHitTestInvisible()
			self:_AfterLoadSubUI()
		end
	end
end

-- BEGIN MODIFICATION @ VIRTUOS : 打开详细属性快捷键
function ItemDetailContentWeapon:InitShortcuts()
	if not IsHD() then
        return 
    end

    if not self._OpenDetail then
        self._OpenDetail = self:AddInputActionBinding("IrisSateHouse_OpenDetail_Gamepad", EInputEvent.IE_Pressed, self.OnBtnClicked, self, EDisplayInputActionPriority.UI_Stack)
        self._wtDetailAttr:SetDisplayInputAction("IrisSateHouse_OpenDetail_Gamepad", true, nil, true)
    end
end

function ItemDetailContentWeapon:RemoveShortcuts()
	if not IsHD() then
		return 
	end
	
	if self._OpenDetail then
		self:RemoveInputActionBinding(self._OpenDetail)
		self._OpenDetail = nil
	end
end

function ItemDetailContentWeapon:InitNavigationDetailContentWeapon(scrollBox)
	if not IsHD() then
		return 
	end
	
	if not self._NavGroup_VerticalBox then
		self._NavGroup_VerticalBox = WidgetUtil.RegisterNavigationGroup(self._wtWeaponBox, self, "Hittest")
		if self._NavGroup_VerticalBox then
			self._NavGroup_VerticalBox:AddNavWidgetToArray(self._wtWeaponBox)
			if scrollBox then
				self._NavGroup_VerticalBox:SetScrollRecipient(scrollBox)
			end
		end
	end
end

function ItemDetailContentWeapon:RemoveNavigationDetailContentWeapon()
	if IsHD() then
		if self._NavGroup_VerticalBox then
			WidgetUtil.RemoveNavigationGroup(self)
			self._NavGroup_VerticalBox = nil
		end
	end
end
-- END MODIFICATION

return ItemDetailContentWeapon