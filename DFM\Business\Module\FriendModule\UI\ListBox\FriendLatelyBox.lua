----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMFriend)
local FriendLogic = require "DFM.Business.Module.FriendModule.Logic.FriendLogic"
----- LOG FUNCTION AUTO GENERATE END -----------



---@class FriendLatelyBox : LuaUIBaseView
local FriendLatelyBox = ui("FriendLatelyBox")

function FriendLatelyBox:Ctor()
    loginfo("FriendLatelyBox:Ctor")
    self._wtPlayerName = self:Wnd("wtPlayerName", UITextBlock)
    self.wtLatelyTimeTxt = self:Wnd("wtLatelyTimeTxt", UITextBlock)
    self._wtPlayerState = self:Wnd("wtPlayerState", UITextBlock)

    self._wtAddFriendBtn = self:Wnd("WBP_FriendAddButton_C_0", UIWidgetBase)
    self._wtBtnBox = self._wtAddFriendBtn:Wnd("wtAddFriendBtn", DFCommonButtonOnly)
    self._wtBtnBox:Event("OnClicked", self._OnBtnAddFriendClick, self)
    self._wtAddEndImage = self._wtAddFriendBtn:Wnd("Image_79", UIImage)
    self._wtplatIcon = self:Wnd("wtplatIcon", UIImage)

    self._wtPlayerIcon = self:Wnd("WBP_CommonHeadIcon", CommonHeadIcon)
    self._wtMatchIcon = self:Wnd("Image_01", UIImage)
    self._wtRankIcon = self:Wnd("wtRankIcon" ,UIWidgetBase)
    self._wtRankDivision = self:Wnd("wtRankDivision", UITextBlock)
    self._wtMilitary = self:Wnd("WBP_Friend_BrandAvatar", UIWidgetBase)
    self._wtOffLineImage = self:Wnd("Masks", UIWidgetBase)
    self._playerInfo = {}
end

function FriendLatelyBox:ShowUI(playerInfo)
    logtable(playerInfo)
    self._playerInfo = playerInfo
    self._gameTime = playerInfo.finish_time
    self._wtplatIcon:Visible()
    self._wtBtnBox:Visible()
    self._wtAddEndImage:Collapsed()
    self.wtLatelyTimeTxt:Collapsed()

    --self.wtLatelyTimeTxt:SetText(FriendLogic.GetFriendRecentTime(self._gameTime))
    self._wtPlayerName:SetText(self._playerInfo.nick_name)
    self._wtMilitary:SetMilitary(playerInfo.military_tag)
    -- self._playIconInfo = {
    --     pic_url = self._playerInfo.pic_url,
    --     player_id = self._playerInfo.player_id,
    --     level = 0,
    --     nick_name = self._playerInfo.nick_name,
    -- }

    self._playIconInfo = self._playerInfo

    if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse then
        self._playIconInfo.level = self._playerInfo.season_level
        self._playIconInfo.rank_attended = self._playerInfo.sol_attended
        self._playIconInfo.rank_score = self._playerInfo.sol_rank_score
    elseif Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Lobby then
        self._playIconInfo.level = self._playerInfo.level
        self._playIconInfo.rank_attended = self._playerInfo.mp_attended
        self._playIconInfo.rank_score = self._playerInfo.mp_rank_score
    end

    FriendLogic.InitFriendRank(self._wtRankIcon, self._wtRankDivision, self._playerInfo)

    if self._playerInfo.state then
        FriendLogic.SetPlayerStateCode(self._wtPlayerState, self._playerInfo,self._wtplatIcon)
        local state = Module.Social:GetOnlinePlayerStateFromStateCode(self._playerInfo.state)
        if Module.Friend:IsFriendOffLineOrInvisible(state, self._playerInfo.player_id) then
            self._wtOffLineImage:HitTestInvisible()
        else
            self._wtOffLineImage:Collapsed()
        end
    end

    local btnTbl = {
        HeadButtonType.PlayerInformat,
        HeadButtonType.AddFriend,
        HeadButtonType.InviteAppTeam,
    }

    self._wtPlayerIcon:InitPortrait(self._playIconInfo, HeadIconType.HeadList, btnTbl, FriendApplySource.RecentPlayApply, TeamInviteSource.FromRecent)
end

function FriendLatelyBox:OnClose()
    self:RemoveAllLuaEvent()
end

function FriendLatelyBox:OnOpen()
end



function FriendLatelyBox:_OnBtnAddFriendClick()
    Module.Friend:AddFriend(self._playerInfo.player_id, FriendApplySource.RecentPlayApply, nil, nil, nil, self._playerInfo.plat_id)
end

function FriendLatelyBox:_ChangeBtn()
    if not Module.Friend:CheckIsFriend(self._playerInfo.player_id) then
        self._wtBtnBox:Collapsed()
        self._wtAddEndImage:Visible()
    end
end

return FriendLatelyBox