----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMArmedForce)
----- LOG FUNCTION AUTO GENERATE END -----------



local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local AssemblySelectionDataStruct = require "DFM.Business.Module.ArmedForceModule.Data.AssemblySelectionDataStruct"
local ArmedForceDataLogic = require "DFM.Business.Module.ArmedForceModule.Logic.ArmedForce.ArmedForceDataLogic"
local ArmedForcePresetLogic = require "DFM.Business.Module.ArmedForceModule.Logic.ArmedForce.ArmedForcePresetLogic"
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local HandleEquipLogic   = require "DFM.Business.Module.ArmedForceModule.Logic.Equipment.HandleEquipLogic"
local ItemConfigTool     = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local BattleFieldSelectionDataLogic = require "DFM.Business.Module.ArmedForceModule.Logic.BattleField.BattleFieldSelectionDataLogic"
local TabUIContentData = require"DFM.Business.DataStruct.UIDataStruct.TabUIContentData"
local EButtonState = import "EButtonState"
local EComp = CommonWidgetConfig.EIVWarehouseTempComponent
local EIVSlotPos = CommonWidgetConfig.EIVSlotPos
local EIVCompOrder = CommonWidgetConfig.EIVCompOrder
local UDFMGameHudDelegates = import("DFMGameHudDelegates")
local gameInst = GetGameInstance()
ECheckButtonState = import"ECheckButtonState"
local UWidgetLayoutLibrary  = import "WidgetLayoutLibrary"
local FAnchors              = import "Anchors"
local FMargin               = import "Margin"

---@class AssemblySelectionMPMain : LuaUIBaseView
local AssemblySelectionMPMain = ui("AssemblySelectionMPMain")
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"
local ButtonIdConfig  = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.ButtonIdConfig"
local ButtonIdEnum = ButtonIdConfig.Enum
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local ECompLockReason = WeaponAssemblyTool.ECompLockReason
local EGPUINavGroupTransSimpleStrategy = import "EGPUINavGroupTransSimpleStrategy"

-- BEGIN MODIFICATION @ VIRTUOS : 
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
local AHallMainDisplayCtrl = import "HallMainDisplayCtrl"
-- END MODIFICATION

local ArmedForceConfig = Module.ArmedForce.Config
local ArmedForceField = Module.ArmedForce.Field

local function log(...)
	loginfo("[AssemblySelectionMPMain]", ...)
end


local LoadingUnit = 10
function AssemblySelectionMPMain:Ctor()
	self._curProcessSlotType = nil
	self._curProcessSlot = nil
    self._curProcessSlotGroup = nil
    self._weaponPresetDataList = {}
    self._weaponPresetIndex = 1

    self._wtCanvasRoot = self:Wnd("CanvasPanel_0", UIWidgetBase)
    -- self._wtTopRightRoot = self:Wnd("DFVerticalBox_167", UIWidgetBase)
    --升级按钮/下拉选容器
    self._wtUpBtnCanvasRoot = self:Wnd("DFCanvasPanel_0", UIWidgetBase)
    self._wtUpBtnRoot = self:Wnd("DFNamedSlot_220", UIWidgetBase)
    self._wtDropdownRoot = self:Wnd("SelectionAssemblyDroDownBox", UIWidgetBase)
    
    self._wtWaterFallList = UIUtil.WndWaterfallScrollBox(self, "wtWaterFallList", self._OnGetItemCount, self._OnProcessItemWidget)
    self._wtWaterFallList:Event('OnProcessItemsUpdateFinished', self._OnProcessItemsUpdateFinished, self)
    self._wtWaterFallListBorder = self:Wnd("DFBorder_1", UIWidgetBase)
    -- 仓库商品按钮画布
    self._wtBtnPan1 = self:Wnd("BtnPan1", UIWidgetBase)

    self._wtOperaBtn = self:Wnd("WBP_CommonButtonV1S1", DFCommonButtonOnly)
    self._wtOperaBtn:Event("OnClicked", self.OnProcessBtnClicked, self)
    self._wtOperaBtn:Event("OnDeClicked", self.OnProcessBtnClicked, self)
    self._wtChangeBtn = self:Wnd("WBP_CommonButtonV1S2_1", DFCommonButtonOnly)
    self._wtChangeBtn:Event("OnClicked", self.OnAssemblyBtnClicked, self)
    self._wtChangeBtn:Event("OnDeClicked", self.OnAssemblyBtnClicked, self)
    self._wtChangeBtn:Event("OnFocusReceivedEvent", self.OnAssemblyBtnFocusReceived, self)
    self._wtChangeBtn:Event("OnFocusLostEvent", self.OnAssemblyBtnFocusLost, self)

    -- 蓝图按钮画布
    self._wtBtnPan2 = self:Wnd("BtnPan2", UIWidgetBase)
    self._wtBtnPan2:Collapsed()
    -- 蓝图购买按钮
    self._wtBuleprintBuyBtn = self:Wnd("CommonButtonV1S_3", DFCommonButtonOnly)
    -- 蓝图生产
    self._wtBuleprintProductCanvas = self:Wnd("wtBuleprintProductCanvas", UIWidgetBase)
    self._wtBuleprintProductBar = self:Wnd("wtBuleprintProductBar", UIProgressBar)
    self._wtBuleprintProductCoolDownText = self:Wnd("DFTextBlock_79", UITextBlock)
    -- 蓝图生产按钮
    self._wtCommonButtonIcon = self:Wnd("WBP_CommonIconButton", UIWidgetBase)

    self._wtItemViewStreamline = self:Wnd("WBP_ItemDetailView_Streamline",LuaUIBaseView)
    if self._wtItemViewStreamline then
        self._wtItemViewStreamline:SetWeaponDetailIsHideBtn(true)
        self._wtItemViewStreamline:SetShowWeaponDetailCheckBox(false)
        self._wtItemViewStreamline_ScrollBox = self._wtItemViewStreamline:Wnd("wScrollBoxContent", UIWidgetBase)
    end

    -- 下载按钮画布
    self._wtDownloadPanel = self:Wnd("Download_Panel", UIWidgetBase)
    self._wtDownloadPanel:Collapsed()

    local unLockTipsList = self:MultiWnd("WBP_AssemblyMapNeed", UIWidgetBase)
    self._wtUnLockTipsList = {}
    for index = 1, 2 do
        local unLockTips  = self:Wnd("WBP_AssemblyMapNeed_" .. index, UIWidgetBase)
        unLockTips:Collapsed()
        table.insert(self._wtUnLockTipsList, unLockTips)
    end

    --self._wtItemDetail:SetRenderOpacity(0)
	self._wtOperaBtn:SetRenderOpacity(0)
	self._wtChangeBtn:SetRenderOpacity(0)

	if DFHD_LUA == 1 then
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.DefaultSecondary)
    end
    self.cacheSubIdx = 1

    -- BEGIN MODIFICATION @ VIRTUOS : 初始化旋转控件
    if IsHD() then
        self:_InitHallMainDisplayCtrl()
        self._bCameraStartRotation = false
        self._RotationVec = FVector2D(0, 0)
        if WidgetUtil.IsGamepad() then 
            self:SetCppValue("WantedInputMode", EGPInputModeType.UIOnly)
        else
            self:SetCppValue("WantedInputMode", EGPInputModeType.GameAndUI)
        end
    end

    self._bFocusWaterFallNavGroup = false
    --- 组1 self._wtWaterFallList
    --- 组2 下拉预览蓝图框 self._wtDropdownRoot
    --- 组3 按钮Vb
    -- self._wtVbProcessBtns = self:Wnd("DFVerticalBox_Process", UIWidgetBase)
    --- 组3可聚焦
    -- self._wtOperaBtn:SetCppValue("bIsFocusable", true)
    -- self._wtChangeBtn:SetCppValue("bIsFocusable", true)
    -- END MODIFICATION

    -- 交易按钮在MP屏蔽
    self._wtExchangeTips = self:Wnd("DFSizeBox_1", UIWidgetBase)
    self._wtExchangeTips:Collapsed()

    self._realWeaponTypeList = {}
end

--==================================================
--region Life function
function AssemblySelectionMPMain:OnOpen()
end

function AssemblySelectionMPMain:OnClose()
    self:RemoveAllLuaEvent()
    Module.ArmedForce.Field:SetCurPropSlotSubId(0)
    Facade.UIManager:ClearSubUIByParent(self, self._wtDropdownRoot)--下拉选
    Facade.UIManager:ClearSubUIByParent(self, self._wtUpBtnRoot)--升级按钮
end

function AssemblySelectionMPMain:OnActivate()
    self:InitData_OnActivate()
end

function AssemblySelectionMPMain:OnShowBegin()
    self:InitData_OnShowBegin()
    -- BEGIN MODIFICATION @ VIRTUOS : 初始化手柄快捷键
    if IsHD() then
        self:_EnableNavigation(true)
	    self:_InitShortcuts()
    end
    -- END MODIFICATION
end

function AssemblySelectionMPMain:OnDeactivate()
    Module.ArmedForce.Field:SetCurPropSlotSubId(0)
    self:RestoreData_OnDeactivate()
end

function AssemblySelectionMPMain:OnHideBegin()
    self:RestoreData_OnHideBegin()
    -- BEGIN MODIFICATION @ VIRTUOS : 移除手柄快捷键
    if IsHD() then
        self:_EnableNavigation(false)
        self:_RemoveShortcuts()
    end
    -- END MODIFICATION
end

-- 初始化数据
function AssemblySelectionMPMain:InitData_OnShowBegin()
    self:RegisterEventListeners()
    self._bClose = false
    self:InitBySlotClicked(self._curProcessSlotType)
    local posIdx = Module.ArmedForce.Field:GetCurOperaAssemblySelectionDataIdx()
    if posIdx then
        self:DoSelectCommonItem(posIdx)
    end
    Module.CommonBar:SetTopBarVisible(true)
    Module.CommonBar:SetCurrencyVisible(true)
end

function AssemblySelectionMPMain:InitData_OnActivate()
end

-- 还原数据
function AssemblySelectionMPMain:RestoreData_OnHideBegin()
    self:UnRegisterEventListeners()
    if self._wtBulletPopView then
        self._wtBulletPopView:Collapsed()
    end
    self:_RemoveWeaponBoxBtn()
    self:_RemoveWeaponLvBtn()
    self._bClose = true
    Module.ArmedForce.Field:SetCurSelectedSlot(nil)
end

function AssemblySelectionMPMain:RestoreData_OnDeactivate()
    self.cacheSubIdx = 1
    Module.ArmedForce.Field:SetCurOperaAssemblySelectionDataIdx(nil)
end

function AssemblySelectionMPMain:OnShow()
    Module.ArmedForce.Config.evtOnAssemblySelectionMPMainShow:Invoke(self._curProcessSlotType)
    if not IsHD() then
        self._buttonDownHandle = UIUtil.AddScrollBoxClickStopScroll( self._wtWaterFallList , self)
    end
end

function AssemblySelectionMPMain:OnHide()
    Module.ArmedForce.Config.evtOnAssemblySelectionMPMainHide:Invoke()
    if not IsHD() then
        if self._buttonDownHandle then
            UIUtil.RemoveScrollBoxClickStopScroll(self._buttonDownHandle)
            self._buttonDownHandle = nil
        end
    end
    self:ReleaseTimer()
end

function AssemblySelectionMPMain:OnInitExtraData(itemSlotType, slotGroup)
    slotGroup = setdefault(slotGroup, ESlotGroup.Player)
    self._curProcessSlotType =  itemSlotType
    self._curProcessSlotGroup = slotGroup
    Module.CommonBar:RegStackUITopBarTitle(self, string.format(ArmedForceConfig.Loc.SelectTitle, Module.Inventory.Config.SlotNameMapping[Server.InventoryServer:GetSlot(self._curProcessSlotType, self._curProcessSlotGroup).SlotType]))
end

function AssemblySelectionMPMain:OnAnimFinished(anim)
    if anim == self.WBP_AssemblyFirearmsMain_in then
        ArmedForceConfig.evtSelectionEquipMainOpenFinish:Invoke()
    end
end
--endregion
--==================================================


--==================================================
--region Public API
function AssemblySelectionMPMain:RegisterEventListeners()
    -- self:AddLuaEvent(Server.ArmedForceServer.Events.evtMPPresetPageItemChanged, self.OnMPPresetPageChanged, self)
    self:AddLuaEvent(Facade.UIManager.Events.evtStackUIChanged, self.OnStackViewChange, self, false)
    self:AddLuaEvent(ArmedForceConfig.evtOnDebugWeaponSceneObject, self._OnDebugWeaponSceneObject, self)
    self:AddLuaEvent(ArmedForceConfig.evtAssemblySelectionDataListGenerated, self.OnAssemblySelectionDataListGenerated, self)
    self:AddLuaEvent(Server.HeroServer.Events.evtMPUsedHeroIdChanged, self._OnHeroUsed, self)

    self:AddLuaEvent(Server.EquipmentServer.Events.evtSelectionChangeFinished, self.OnSelectionChangeFinished, self)
    self:AddLuaEvent(Module.ArmedForce.Config.evtOnMeleeWeaponDataChanged, self.OnMeleeWeaponDataChanged, self)

    self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult, self._OnDownloadeFinishedCheck, self)
    -- self:AddLuaEvent(Server.InventoryServer.Events.evtPostUpdateDeposData, self.OnPostUpdateDeposData, self)
    -- BEGIN MODIFICATION @ VIRTUOS : 
    if IsHD() then
        self:AddLuaEvent(self._wtItemViewStreamline.evtOnMouseEnter, self._OnMouseEnterItemView, self)
    end
    -- END MODIFICATION
end

function AssemblySelectionMPMain:UnRegisterEventListeners()
    -- self:RemoveLuaEvent(Server.ArmedForceServer.Events.evtMPPresetPageItemChanged)
    -- self:RemoveLuaEvent(Facade.UIManager.Events.evtStackUIChanged)
    self:RemoveLuaEvent(ArmedForceConfig.evtOnDebugWeaponSceneObject)
    self:RemoveLuaEvent(ArmedForceConfig.evtAssemblySelectionDataListGenerated)
    self:RemoveLuaEvent(Server.HeroServer.Events.evtMPUsedHeroIdChanged)
    self:RemoveLuaEvent(Server.EquipmentServer.Events.evtSelectionChangeFinished)
    self:RemoveLuaEvent(Module.ArmedForce.Config.evtOnMeleeWeaponDataChanged)

    self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult)
    -- self:RemoveLuaEvent(Server.InventoryServer.Events.evtPostUpdateDeposData)
    -- BEGIN MODIFICATION @ VIRTUOS : 
    if IsHD() then
        self:RemoveLuaEvent(self._wtItemViewStreamline.evtOnMouseEnter)
    end
    -- END MODIFICATION
end


function AssemblySelectionMPMain:OnMPPresetPageChanged()
    self._wtBtnPan1:SelfHitTestInvisible()
end

function AssemblySelectionMPMain:_OnDebugWeaponSceneObject()
    self:OnEnterPureBtnClicked()
end

function AssemblySelectionMPMain:OnEnterPureBtnClicked()
    Module.CommonBar:BindBackHandler(self.OnExitPureBtnClicked, self)
    self:PlayWidgetAnim(self.WBP_AssemblyFirearmsMain_out)

    Module.LobbyDisplay:SetHallDisplayZoomType(true)
    
    LogAnalysisTool.SignButtonClicked(ButtonIdEnum.MPSelectionMagnifier)
    LogAnalysisTool.SetSelectionButtonID(ButtonIdEnum.MPSelectionMagnifier)
    LogAnalysisTool.DoSendSelectionWeaponClickFlowLog()
end

function AssemblySelectionMPMain:_OnGetItemCount()
    local selectionDataList = Module.ArmedForce.Field:GetAssemblySelectionDataList()
	return #selectionDataList or 0
end

function AssemblySelectionMPMain:_OnProcessItemWidget(position, itemWidget)
    local selectionDataList = Module.ArmedForce.Field:GetAssemblySelectionDataList()
    local assemblySelectionData = selectionDataList[position]
    if assemblySelectionData then
        itemWidget:Visible()
        local OnSelectionDataItemViewClicked = CreateCallBack(self.OnWidgetClicked, self)
        itemWidget:InitSelectionData(assemblySelectionData, position, ESlotGroup.MPApply, OnSelectionDataItemViewClicked)

        if IsHD() and self._bFocusWaterFallNavGroup then
            local posIdx = Module.ArmedForce.Field:GetCurOperaAssemblySelectionDataIdx()
            if position == posIdx then
                WidgetUtil.SetUserFocusToWidget(itemWidget, true)
            end
        end
    else
        itemWidget:Collapsed()
    end
end

function AssemblySelectionMPMain:OnNavGroupFocusReceivedEvent(navGroup)
    if navGroup and navGroup == self._wtNavGroup then
        self._bFocusWaterFallNavGroup = true
    end
end


function AssemblySelectionMPMain:OnNavGroupFocusLostEvent(navGroup)
    if navGroup and navGroup == self._wtNavGroup then
        self._bFocusWaterFallNavGroup = false
    end
end

function AssemblySelectionMPMain:_OnProcessItemsUpdateFinished()
    local selectionDataList = Module.ArmedForce.Field:GetAssemblySelectionDataList()
    local length = #selectionDataList or 0
    local curSelectedSlot = Module.ArmedForce.Field:GetCurSelectedSlot()
    if length > 0 and curSelectedSlot ~= nil then
    else
    end
end

function AssemblySelectionMPMain:RefreshAllItems()
    self._wtWaterFallList:RefreshAllItems()
end

function AssemblySelectionMPMain:OnAssemblySelectionDataListGenerated()
    --self._wtItemDetail:SetRenderOpacity(0)
	self._wtOperaBtn:SetRenderOpacity(0)
	self._wtChangeBtn:SetRenderOpacity(0)
    local selectionDataList = Module.ArmedForce.Field:GetAssemblySelectionDataList()
    if selectionDataList and next(selectionDataList) then
        -- self._wtWaterFallList:ScrollToStart()
    else
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMain,"ResetDisplayItem")
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMain,"ResetDisplayWeapon")
        self._wtBtnPan1:Collapsed()
        self._wtBtnPan2:Collapsed()
        self._wtItemViewStreamline:Collapsed()
    end
    Timer.DelayCall(0, function ()
        self._wtWaterFallList:RefreshAllItems()
    end, self)
    local weaponType = Module.ArmedForce.Field:GetCurPropSlotSubId()
    local curSlotEquipedItem
    --- [ArmedForceProp]
    if ItemHelperTool.IsMPArmedForceSlotType(self._curProcessSlotType) then
        Timer.DelayCall(0.02, function (self)
            self:SelectFirstItem()
        end, self)
            
    else
        Timer.DelayCall(0.02, function (self)
            self:SelectFirstItem()
        end, self)
    end
end

function AssemblySelectionMPMain:SelectFirstItem(bForceFirst)
    bForceFirst = setdefault(bForceFirst, false)
    local curSelectedSlot = Module.ArmedForce.Field:GetCurSelectedSlot()
    if curSelectedSlot == nil then
        return
    end
    local selectionDataList = Module.ArmedForce.Field:GetAssemblySelectionDataList()
    if selectionDataList then
        if #selectionDataList == 0 then
            self:DoSelectCommonItem(-1)
        else
            --- [ArmedForceProp]
            if ItemHelperTool.IsMPArmedForceSlotType(self._curProcessSlotType) then
                self:DoSelectCommonItem(1)
            else
                -- 当点击空的武器栏位时，进入到选择主武器的界面，应该默认选中仓库中第一条，非装配的枪械。
                if not self._curProcessSlot:GetEquipItem() and selectionDataList[1]:GetIsDeposData()
                and ItemHelperTool.IsMainWeaponSlotType(self._curProcessSlot.SlotType)
                and selectionDataList[1]:GetRepresentativeItem():IsEquipped() and #selectionDataList >= 2 then
                    self:DoSelectCommonItem(2)
                else
                    local posIdx = Module.ArmedForce.Field:GetCurOperaAssemblySelectionDataIdx()
                    if not bForceFirst and posIdx and posIdx > 0 and posIdx <= #selectionDataList then
                        self:DoSelectCommonItem(posIdx)
                        return
                    end
                    self:DoSelectCommonItem(1)
                end
            end
        end
    else
        self:DoSelectCommonItem(-1)
    end
end

function AssemblySelectionMPMain:OnMeleeWeaponDataChanged()
    self:InitBySlotClicked(self._curProcessSlotType)
    local posIdx = Module.ArmedForce.Field:GetCurOperaAssemblySelectionDataIdx()
    if posIdx then
        self:DoSelectCommonItem(posIdx)
    end
end

function AssemblySelectionMPMain:_OnDownloadeFinishedCheck()
    self:InitBySlotClicked(self._curProcessSlotType)
    local posIdx = Module.ArmedForce.Field:GetCurOperaAssemblySelectionDataIdx()
    if posIdx then
        self:DoSelectCommonItem(posIdx)
    end
end

---@param  itemSlotType ESlotType
function AssemblySelectionMPMain:InitBySlotClicked(itemSlotType)
    local itemSlot = Server.InventoryServer:GetSlot(itemSlotType, self._curProcessSlotGroup)
    Module.ArmedForce.Field:SetCurSelectedSlot(itemSlot)
    self._curProcessSlot = itemSlot
	self._selectedItemSlotType = itemSlotType
    self:InitOperationTypeBySlot(itemSlot)
    self:InitSubTabsBySlot(itemSlot)
end

---@param itemSlot ItemSlot
function AssemblySelectionMPMain:InitOperationTypeBySlot(itemSlot)
    Module.ArmedForce.Field:SetCurOperationType(ArmedForceConfig.ESelectionOperationType.Single)
end

---@param itemSlot ItemSlot
function AssemblySelectionMPMain:InitSubTabsBySlot(itemSlot)
    if not IsHD() and itemSlot:IsWeaponSlot() and ItemHelperTool.IsMainWeaponSlotType(itemSlot.SlotType) then
        self:Bp_SetType(0)
    else
        self:Bp_SetType(1)
    end
    if ItemHelperTool.IsMainWeaponSlotType(itemSlot.SlotType) then
        local fFliterCheckFunc = function(idx)
            local weaponType = ArmedForceConfig.TabWeaponTypeList[idx] or 0
            return not BattleFieldSelectionDataLogic.CheckIsFitCurMPRuleListEmpty(self._curProcessSlot, weaponType)
        end
        self._realWeaponTypeList = {}
        local weaponTabTxtList, weaponImgPathList, realWeaponTypeList = ArmedForceDataLogic.GenTabListWithAll(fFliterCheckFunc)
        self._realWeaponTypeList = realWeaponTypeList

        Module.CommonBar:RegStackUITopBarTabGroupRegInfo(self, {
            tabTxtList = weaponTabTxtList,
            imgPathList = weaponImgPathList,
            fCallbackIns = SafeCallBack(self._OnMainTabClicked, self),
            tabGroupSize = FVector2D(1904, 96),
            tabSpaceMargin = FMargin(0, 0, 16, 0),
            defalutIdx = self.cacheSubIdx,
        })
        
        if self._wtDFCommonButtonV3S1InsId == nil then
            self:_OnCreateWeaponLvBtn()
        end
        if self._wtPresetDownBoxInsId == nil then
            self:_OnCreateWeaponBoxBtn()
        end
    else
        Module.CommonBar:RegStackUITopBarTabGroupRegInfo(self, {})
        self._realWeaponTypeList = {}
        self:_OnMainTabClicked(0)
    end
end

-- function AssemblySelectionMPMain:OnCreateWeaponLvBtn()
--     self:_RemoveAllGradeItem()
--     --等级
--     local btnData = TabUIContentData:NewIns(0, "", true, ArmedForceConfig.UnlockIcon, true)
--     local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.DFCommonButtonV3S1, self._wtTopRightRoot, nil, btnData)
--     self._wtDFCommonButtonV3S1InsId = instanceId
--     local unlockLvBtn = getfromweak(weakUIIns)
--     if unlockLvBtn then
--         unlockLvBtn.Size = 1
--         unlockLvBtn:Event('OnClicked', self._OnUpgradeBtnClicked, self)
--         unlockLvBtn:SetVisibility(ESlateVisibility.Collapsed)
--         --设置item间隔
--         unlockLvBtn.Slot:SetPadding(FMargin(0, 0, 0, 40))
--     end
--     --下拉选
--     local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.CommonItemViewDropDownBox, self._wtTopRightRoot)
--     self._wtPresetDownBoxInsId = instanceId
--     local wtPresetDownBox = getfromweak(weakUIIns)
--     if wtPresetDownBox then
--         wtPresetDownBox:InitDownBox(self, self.OnGetPresetCount, self.OnPresetProcessTabItemWidget)
--     end
-- end

function AssemblySelectionMPMain:_OnCreateWeaponLvBtn()
    self:_RemoveWeaponLvBtn()
    --升级
    local btnData = TabUIContentData:NewIns(0, "", true, ArmedForceConfig.UnlockIcon, true)
    local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.DFCommonButtonV3S1, self._wtUpBtnRoot, nil, btnData)
    self._wtUpBtnCanvasRoot:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    self._wtDFCommonButtonV3S1InsId = instanceId
    local unlockLvBtn = getfromweak(weakUIIns)
    if unlockLvBtn then
        unlockLvBtn:AsyncSetImageIconPathAllState(ArmedForceConfig.AllIcon.UpgradIconPath, true)
        unlockLvBtn.Size = 1
        unlockLvBtn:RemoveEvent("OnClicked")
        unlockLvBtn:RemoveEvent("OnDeClicked")
        unlockLvBtn:Event('OnClicked', self._OnUpgradeBtnClicked, self)
        unlockLvBtn:Event('OnDeClicked', self._OnUpgradeBtnClicked, self)
        
        if IsHD() then
            unlockLvBtn:RemoveEvent("OnFocusReceivedEvent")
            unlockLvBtn:RemoveEvent("OnFocusLostEvent")
            unlockLvBtn:Event("OnFocusReceivedEvent", self._OnUpgradeBtnFocusReceived, self)
            unlockLvBtn:Event("OnFocusLostEvent", self._OnUpgradeBtnFocusLost, self)
        end

        -- BEGIN MODIFICATION @ VIRTUOS : 升级枪械快捷键和图标
        if IsHD() and not self._Upgrage then
            self._Upgrage = self:AddInputActionBinding("Assembly_Upgrage_Gamepad", EInputEvent.IE_Pressed, self._OnUpgradeBtnClicked,self, EDisplayInputActionPriority.UI_Stack)
            unlockLvBtn:SetDisplayInputAction("Assembly_Upgrage_Gamepad", true, nil, true)
        end
        -- END MODIFICATION
    end
end

function AssemblySelectionMPMain:_RemoveWeaponLvBtn()
    self._wtUpBtnCanvasRoot:SetVisibility(ESlateVisibility.Collapsed)
    if self._wtDFCommonButtonV3S1InsId then
        Facade.UIManager:RemoveSubUI(self, UIName2ID.DFCommonButtonV3S1, self._wtDFCommonButtonV3S1InsId)
        self._wtDFCommonButtonV3S1InsId = nil
    end

    -- BEGIN MODIFICATION @ VIRTUOS : 移除升级枪械快捷键
    if IsHD() and self._Upgrage then
        self:RemoveInputActionBinding(self._Upgrage)
        self._Upgrage= nil
    end
    self:_OnUpgradeBtnFocusLost()
    -- END MODIFICATION
end

function AssemblySelectionMPMain:_OnCreateWeaponBoxBtn()
    self:_RemoveWeaponBoxBtn()
    --下拉选
    local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.CommonItemViewDropDownBox, self._wtDropdownRoot)
    self._wtPresetDownBoxInsId = instanceId
    local wtPresetDownBox = getfromweak(weakUIIns)
    if wtPresetDownBox then
        local data = {
            tabName = Module.ArmedForce.Config.Loc.DropDownBoxTitle,           	-- tab文本名字
            subUINavID = UIName2ID.CommonItemViewDropDownItem,                  -- 指定生成子ui
            caller = self,                                                      -- 方法持有者
            fOnGetPresetCount = self.OnGetPresetCount,                          -- 子控件数量获取方法
            fOnPresetProcessTabItemWidget = self.OnPresetProcessTabItemWidget,   -- 子控件生成回调方法
            fOnCheckedBoxStateChangedNative = function ()
                LogAnalysisTool.SignButtonClicked(ButtonIdEnum.MPSelectionPreset)
            end,    -- DropDown点击选择方法
        }
        wtPresetDownBox:InitDownBox(data)

        -- BEGIN MODIFICATION @ VIRTUOS : 注册下拉框快捷键并显示图标
        if IsHD() then
            wtPresetDownBox:EnableDropDownShortcut(true)
            wtPresetDownBox:RemoveEvent("OnFocusReceivedEvent")
            wtPresetDownBox:RemoveEvent("OnFocusLostEvent")
            wtPresetDownBox:Event("OnFocusReceivedEvent", self._EnbalePresetDownBoxFocus, self)
            wtPresetDownBox:Event("OnFocusLostEvent", self._DisablePresetDownBoxFocus, self)
        end
        -- END MODIFICATION
    end
end

function AssemblySelectionMPMain:_RemoveWeaponBoxBtn()
    if self._wtPresetDownBoxInsId then
        Facade.UIManager:RemoveSubUI(self, UIName2ID.CommonItemViewDropDownBox, self._wtPresetDownBoxInsId)
        self._wtPresetDownBoxInsId = nil
    end
end


function AssemblySelectionMPMain:OnFreshWeaponLvBtn()
    local curSelectedSelectionData = Module.ArmedForce.Field:GetCurAssemblySelectionData()
    if curSelectedSelectionData then
        local selectItem = curSelectedSelectionData:GetRepresentativeItem()
        local bLocked = curSelectedSelectionData:CheckIsLocked()
        local bIsDownload = curSelectedSelectionData:CheckIsDownloaded()
        if selectItem and not bLocked then
            local weaponFeature = selectItem:GetFeature(EFeatureType.Weapon)
            if weaponFeature ~= nil and ItemHelperTool.IsMainWeaponSlotType(self._curProcessSlotType) then
                if self._wtDFCommonButtonV3S1InsId == nil then
                    self:_OnCreateWeaponLvBtn()
                end
                if self._wtPresetDownBoxInsId == nil then
                    self:_OnCreateWeaponBoxBtn()               
                end
                local weakUIIns, instanceId = Facade.UIManager:GetSubUI(self, UIName2ID.DFCommonButtonV3S1, self._wtDFCommonButtonV3S1InsId)
                local unlockLvBtn = getfromweak(weakUIIns)
                if unlockLvBtn then
                    local weaponLevel = weaponFeature:GetWeaponLevel()
                    --判断等级是否是满级显示
                    local mainTitle = StringUtil.SequentialFormat(ArmedForceConfig.Loc.CurLvText, weaponLevel or 1)
                    local selectionData = Module.ArmedForce.Field:GetCurAssemblySelectionData()
                    local isMaxed = false
                    if selectionData then
                        local weaponItem = selectionData:GetRepresentativeItem()
                        if weaponItem then
                            local levelCfgs = Server.WeaponAssemblyServer:GetWeaponLevelUnLockData(weaponItem.id)
                            if levelCfgs and weaponLevel then
                                local level = levelCfgs[weaponLevel]
                                if level and level.Exp == 0 then
                                    mainTitle = ArmedForceConfig.Loc.MaxLvText
                                    isMaxed = true
                                else
                                    isMaxed = false
                                end
                                unlockLvBtn:SetIsShowIcon(false)
                            end
                        end
                    end
                    unlockLvBtn:Visible()
                    unlockLvBtn:SetMainTitle(mainTitle)
                    --杀人升星
                    self:_KillingRisingStar(unlockLvBtn, selectItem, isMaxed)
                end
            else
                if self._wtDFCommonButtonV3S1InsId then
                    self:_RemoveWeaponLvBtn()
                end
                if self._wtPresetDownBoxInsId then
                    self:_RemoveWeaponBoxBtn()                
                end
            end
        else
            if self._wtDFCommonButtonV3S1InsId then
                self:_RemoveWeaponLvBtn()
            end
            if self._wtPresetDownBoxInsId then
                self:_RemoveWeaponBoxBtn()                
            end
        end
    else
        if self._wtDFCommonButtonV3S1InsId then
            self:_RemoveWeaponLvBtn()
        end
        if self._wtPresetDownBoxInsId then
            self:_RemoveWeaponBoxBtn()                
        end
    end
end

--杀人星级
function AssemblySelectionMPMain:_KillingRisingStar(btn, selectItem, isMaxed)
    if btn and isMaxed and selectItem then
        --获取杀人星星数据
        local weaponFeature = selectItem:GetFeature(EFeatureType.Weapon)
        if weaponFeature then
            local starNum = weaponFeature:GetStarNum()
            if starNum == 0 then
                return
            end
            btn:SetMainTitle(starNum or "")
            btn:AsyncSetImageIconPathAllState(Module.Gunsmith.Config.IconPaths[1], true)
            btn:SetIsShowIcon(true)
            if btn.wtCommonBtn and btn.wtCommonBtn.DFImageSizeBox then
                if Module.Gunsmith.Config.IconPaths[1] ~= nil then
                    btn.wtCommonBtn.DFImageSizeBox:Visible()
                else
                    btn.wtCommonBtn.DFImageSizeBox:Collapsed()
                end
            end
        end
    end
end

--- 主页签点击回调
function AssemblySelectionMPMain:_OnMainTabClicked(curActiveIndex, lastActiveIndex)
    if self.cacheSubIdx and self.cacheSubIdx ~= curActiveIndex then
        Module.ArmedForce.Field:SetCurOperaAssemblySelectionDataIdx(nil)
    end
    if curActiveIndex == 0 then
        self.cacheSubIdx = 1
    else
        self.cacheSubIdx = curActiveIndex
    end
    --- 为0表示全部
    local weaponType = self._realWeaponTypeList[curActiveIndex] or 0
    self:InitDataBySlotClicked(self._curProcessSlot, weaponType)

    -- BEGIN MODIFICATION @ VIRTUOS : 刷新Focus
    if IsHD() then
        Timer.DelayCall(0, function ()
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup)
        end)
    end
    -- END MODIFICATION
end


function AssemblySelectionMPMain:InitDataBySlotClicked(itemSlot, subType)
    local bIsGun = self._curProcessSlotType == ESlotType.MP_MainWeapon or self._curProcessSlotType == ESlotType.MP_SecondaryWeapon
    local assemblySelectionDataList = BattleFieldSelectionDataLogic.GenMPSelectionDataList(itemSlot, subType, Server.ArmedForceServer:GetCurSlotGroupId(), bIsGun)
    loginfo("AssemblySelectionMPMain:InitDataBySlotClicked selectionDataList num=", assemblySelectionDataList and table.nums(assemblySelectionDataList) or 0)
    Module.ArmedForce.Field:SetAssemblySelectionDataList(assemblySelectionDataList)
end
--endregion
--==================================================


--==================================================
--region Private API

-- 点击itemview
function AssemblySelectionMPMain:OnWidgetClicked(posIdx)
    print('AssemblySelectionMPMain:OnWidgetClicked', Module.ArmedForce.Field:GetCurOperaAssemblySelectionDataIdx(), posIdx)
    if Module.ArmedForce.Field:GetCurOperaAssemblySelectionDataIdx() ~= posIdx or
       Module.ArmedForce.Field:GetCurPropSlotSubId() ~= 0 then
        self:DoSelectCommonItem(posIdx)

        -- 选中新手引导中指定的道具
        Module.Guide.Config.EGuideEvent.evtGuideClick:Invoke()
    end
end

function AssemblySelectionMPMain:DoSelectCommonItem(posIdx)
    if self._wtDFCommonButtonV3S1InsId then
        self:_RemoveWeaponLvBtn()
    end
    if self._wtPresetDownBoxInsId == nil then
        self:_OnCreateWeaponBoxBtn()      
    end
    local selectionDataList = Module.ArmedForce.Field:GetAssemblySelectionDataList()
    if (posIdx > 0 and posIdx <= #selectionDataList) then
        Module.ArmedForce.Field:SetCurOperaAssemblySelectionDataIdx(posIdx)
        local assemblySelectionData = Module.ArmedForce.Field:GetAssemblySelectionDataByIdx(posIdx)
        if self._curProcessSlot and ItemHelperTool.IsMainWeaponSlotType(self._curProcessSlot.SlotType) then
            self._weaponPresetDataList = assemblySelectionData:GetWeaponPresetDataList()
            self._weaponPresetIndex = 1
        else
            self._weaponPresetDataList = {}
        end
        --显示等级按钮,解决等级不能显示的问题
        self:OnFreshWeaponLvBtn()

        if self._wtPresetDownBoxInsId then
            local weakUIIns, instanceId = Facade.UIManager:GetSubUI(self, UIName2ID.CommonItemViewDropDownBox, self._wtPresetDownBoxInsId)
            local wtPresetDownBox = getfromweak(weakUIIns)
            if wtPresetDownBox then
                wtPresetDownBox:RefreshTab()
                wtPresetDownBox:SwitchCheckButtonState(ECheckButtonState.Unchecked)
            end
        end

        self._wtItemViewStreamline:SetShowWeaponDetailCheckBox(false)
        self:_OnCommonItemClicked(assemblySelectionData)
    else
        logwarning('AssemblySelectionMPMain:DoSelectCommonItem 数组越界,未刷新选中', posIdx)
    end
end

-- 所需道具数据显示
---@param assemblySelectionData AssemblySelectionDataStruct
function AssemblySelectionMPMain:_OnCommonItemClicked(assemblySelectionData)
    local item
    if assemblySelectionData then
        self._wtItemViewStreamline:SelfHitTestInvisible()
        self._wtItemViewStreamline:PlayAnimation(self._wtItemViewStreamline.WBP_ItemDetailView_Streamline_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
		self._wtOperaBtn:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self._wtChangeBtn:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self._wtOperaBtn:SetRenderOpacity(1)
		self._wtChangeBtn:SetRenderOpacity(1)

        --- long1需求 模型显示已装备的皮肤,下拉预设列表显示黑铁
        if self._curProcessSlot and ItemHelperTool.IsMainWeaponSlotType(self._curProcessSlot.SlotType) then
            assemblySelectionData:GenWeaponPresetPropInfo(self._weaponPresetIndex)
            item = assemblySelectionData:GetWeaponPresetItem()
        else
            item = assemblySelectionData:GetRepresentativeItem()
        end

        Module.ArmedForce.Field:OnCurOperaAssemblySelectionItemChanged(item)
        --- 刷新模型
        self._wtItemViewStreamline:UpdateItem(item)
        self:FreshItemUIDownloadMask(item)

        -- self._wtItemViewStreamline:SetDescBtnVisible(false)
        ArmedForceConfig.evtOutfitSelectionCellClicked:Invoke(item)
        self:OnFreshBtnState()
        self:OnFreshWeaponLvBtn()
    end
end

function AssemblySelectionMPMain:OnProcessBtnClicked()
    if not self._bClose then
        ArmedForceConfig.evtProcessBtnClicked:Invoke()
        local curSelectedSelectionData = Module.ArmedForce.Field:GetCurAssemblySelectionData()
        self:_DoProcessSingle(curSelectedSelectionData)
    end
end

function AssemblySelectionMPMain:_OnProcessMPLockTips(assemblySelectionData)
    local eCompLockReason = assemblySelectionData:GetWeaponPresetCompLockReason()
    if eCompLockReason == ECompLockReason.LevelLock then
        Module.CommonTips:ShowSimpleTip(ArmedForceConfig.Loc.PlanMissingParts)
    elseif eCompLockReason == ECompLockReason.SkinLock then
        Module.CommonTips:ShowSimpleTip(ArmedForceConfig.Loc.PlanMissingSkinParts)
    elseif eCompLockReason == ECompLockReason.NotInTable then
        if not VersionUtil.IsShipping() then
            Module.CommonTips:ShowSimpleTip(ArmedForceConfig.Loc.PlanMissingTableParts)
        end
    end
end

function AssemblySelectionMPMain:_DoProcessSingle(assemblySelectionData)
    if not assemblySelectionData then
        return
    end

    local bLocked = assemblySelectionData:CheckIsLocked()
    local bNeedBuy = assemblySelectionData:IsWeaponPresetNeedBuy()
    local bOriginalPropInfo = assemblySelectionData:IsOriginalPropInfo()
    local bIsDownload = assemblySelectionData:CheckIsDownloaded()

    local selectItem = assemblySelectionData:GetRepresentativeItem() or nil
    if selectItem then
        local bFitArm = Module.ArmedForce:CheckWeaponItemIsFitCurrentArm(selectItem)
        if not bFitArm then
            Module.CommonTips:ShowSimpleTip(ArmedForceConfig.Loc.NotMatchArmBtnText)
            --- 失败装备关闭当前UI
            self:_DoOperateCallback()
            return
        end
    end

    --- 是否有已装备的物品
    local bHasEquipped = assemblySelectionData.bSample
    if bHasEquipped then
        if bOriginalPropInfo then
            Module.CommonTips:ShowSimpleTip(ArmedForceConfig.Loc.UsingMPEquipTip)
        else
            if bIsDownload then
                if not bNeedBuy then
                    BattleFieldSelectionDataLogic.HandlePresetDataProcess(assemblySelectionData, self._curProcessSlot, self._DoOperateCallback, self)
                else
                    --- MS24 bNeedBuy == true 需要应用
                    -- BattleFieldSelectionDataLogic.HandleMergeDataProcess(assemblySelectionData, self._curProcessSlot)
                    -- self:_DoOperateCallback()
                    self:_OnProcessMPLockTips(assemblySelectionData)
                end
            else
                Module.CommonTips:ShowSimpleTip(ArmedForceConfig.Loc.CannotMPApplyDownloadTip)
            end
        end
    else
        if not bLocked then
            --- 主副武器走背包页协议
            if ItemHelperTool.IsMainWeaponSlotType(self._curProcessSlot.SlotType) then
                if bIsDownload then
                    if bOriginalPropInfo then
                        BattleFieldSelectionDataLogic.HandleMergeDataProcess(assemblySelectionData, self._curProcessSlot)
                        --- 成功装备关闭当前UI
                        self:_DoOperateCallback()
                    else
                        if not bNeedBuy then
                            BattleFieldSelectionDataLogic.HandlePresetDataProcess(assemblySelectionData, self._curProcessSlot, self._DoOperateCallback, self)
                        else
                            --- MS24 bNeedBuy == true 需要应用
                            -- BattleFieldSelectionDataLogic.HandleMergeDataProcess(assemblySelectionData, self._curProcessSlot)
                            -- self:_DoOperateCallback()
                            self:_OnProcessMPLockTips(assemblySelectionData)
                        end
                    end
                else
                    Module.CommonTips:ShowSimpleTip(ArmedForceConfig.Loc.CannotMPEquipDownloadTip)
                end
            else
                -- 近战武器走藏品协议
                if self._curProcessSlot.SlotType == ESlotType.MP_MeleeWeapon then
                    self:_DoMeleeWeaponProcess(assemblySelectionData)
                    --- 成功装备关闭当前UI
                    self:_DoOperateCallback()
                else
                    -- 兵种道具走专家协议
                    if ItemHelperTool.IsMPArmedForceSlotType(self._curProcessSlotType) then
                        local curShowHeroId = Module.Hero:GetCurShowHeroId()
                        Server.HeroServer:DoSelectHeroArmedPropReq(curShowHeroId, assemblySelectionData.itemId, self._curProcessSlotType)
                        --- 成功装备关闭当前UI
                        self:_DoOperateCallback()
                    else
                        if self._curProcessSlot.SlotType == ESlotType.MP_ArmedForceTDMProp then
                            self:_DoArmedForceTDMPropProcess(assemblySelectionData)
                            --- 成功装备关闭当前UI
                            self:_DoOperateCallback()
                        end
                    end
                end
            end

            LogAnalysisTool.SignButtonClicked(ButtonIdEnum.MPSelectionEquip)
            LogAnalysisTool.SetSelectionButtonID(ButtonIdEnum.MPSelectionEquip)
            LogAnalysisTool.DoSendSelectionWeaponClickFlowLog()
        else
            local selectItem = assemblySelectionData:GetRepresentativeItem()
            if selectItem then
                local bJumped = false
                local itemUnlockPathRegistryItem = Server.ItemUnlockPathServer:GetPreferredItemUnlockPathRegistryItem(EArmedForceMode.MP, selectItem.id)
                if bIsDownload then
                    if itemUnlockPathRegistryItem then
                        local jumpId = itemUnlockPathRegistryItem.jumpID
                        if itemUnlockPathRegistryItem.source == EItemUnlockSources.Activity then
                            --- SHE1 离开当前系统之前先应用当前展示干员
                            --- 以免进入靶场时是临时未应用干员
                            -- self:_ForceUseCurShowHeroId()
                            local activityId = itemUnlockPathRegistryItem.activityId
                            Module.Jump:JumpByID(tonumber(jumpId))
                            bJumped = true
                        elseif itemUnlockPathRegistryItem.source == EItemUnlockSources.Armory then
                            --- SHE1 离开当前系统之前先应用当前展示干员
                            --- 以免进入靶场时是临时未应用干员
                            -- self:_ForceUseCurShowHeroId()
                            Module.Jump:JumpByID(tonumber(jumpId))
                            bJumped = true
                        end
                    end
                    if not bJumped then
                        local unlockTipStr = string.format(Module.Shop.Config.Loc.NotUnlockTip, selectItem.name)
                        Module.CommonTips:ShowSimpleTip(unlockTipStr)
                    end
                else
                    Module.CommonTips:ShowSimpleTip(ArmedForceConfig.Loc.CannotMPJumpDownloadTip)
                end
            end
        end
    end
end

-- 处理近战武器
function AssemblySelectionMPMain:_DoMeleeWeaponProcess(assemblySelectionData)
    BattleFieldSelectionDataLogic.HandleMergeDataProcess(assemblySelectionData, self._curProcessSlot)
end

function AssemblySelectionMPMain:_DoArmedForceTDMPropProcess(assemblySelectionData)
    BattleFieldSelectionDataLogic.HandleMergeDataProcess(assemblySelectionData, self._curProcessSlot)
end

function AssemblySelectionMPMain:_DoOperateCallback(bSuccess)
    self._wtBtnPan1:HitTestInvisible()
    local curView = Facade.UIManager:GetCurrentStackUI()
    if curView and (curView.UINavID == UIName2ID.AssemblySelectionMPMain) then
        Facade.UIManager:CloseUI(self)
    end
end

function AssemblySelectionMPMain:OnStackViewChange(operaViewId, operaAction)
    local curViewWeakIns = Facade.UIManager:GetStackUIByUINavId(self.UINavID, false)
    local curView = curViewWeakIns and getfromweak(curViewWeakIns) or nil
    if not hasdestroy(curView) and operaViewId ~= self.UINavID and operaAction == EStackAction.Push then
        --- SHE1 离开当前系统之前先应用当前展示干员
        --- 以免进入靶场时是临时未应用干员
        self:_ForceUseCurShowHeroId()
    end
end


function AssemblySelectionMPMain:_ForceUseCurShowHeroId()
    --- 必须应用干员后再改装
    local curShowHeroId = Module.Hero:GetCurShowHeroId()
    local curHeroId = Server.HeroServer:GetCurUsedHeroId()
    if curShowHeroId ~= curHeroId then
        Module.Hero:UseHeroById(curShowHeroId)
    end
end

-- 武器改装
function AssemblySelectionMPMain:OnAssemblyBtnClicked()
    if not self._bClose then
        local curSelectedSelectionData = Module.ArmedForce.Field:GetCurAssemblySelectionData()
        if curSelectedSelectionData then
            local selectItem = curSelectedSelectionData:GetRepresentativeItem()
            local bLocked = curSelectedSelectionData:CheckIsLocked()
            local bOriginalPropInfo = curSelectedSelectionData:IsOriginalPropInfo()
            local bIsDownload = curSelectedSelectionData:CheckIsDownloaded()

            if selectItem then
                if selectItem:IsAssemblyItem() then
                    if not bLocked then
                        if bIsDownload then
                            --- long1 改枪和升级之前先应用当前展示干员
                            --- 以免进入靶场时是临时未应用干员
                            -- self:_ForceUseCurShowHeroId()
                            if bOriginalPropInfo then
                                Module.Gunsmith:OpenMPFromItemBase(selectItem)
                            else
                                --- 如果不是原方案就预改装
                                Module.Gunsmith:OpenMPFromItemBase(selectItem)
                            end
                        else
                            Module.CommonTips:ShowSimpleTip(ArmedForceConfig.Loc.CantGoToWeaponAssemblyNeedDownload)
                        end
                    else
                        Module.CommonTips:ShowSimpleTip(ArmedForceConfig.Loc.CantGoToWeaponAssembly)
                    end
                end
            end
        else
            logerror('AssemblySelectionMPMain:OnAssemblyBtnClicked 报空curSelectedSelectionData = ', curSelectedSelectionData)
        end

        LogAnalysisTool.SignButtonClicked(ButtonIdEnum.MPSelectionAssembly)
        LogAnalysisTool.SetSelectionButtonID(ButtonIdEnum.MPSelectionAssembly)
        LogAnalysisTool.DoSendSelectionWeaponClickFlowLog()
    end
end

function AssemblySelectionMPMain:_OnUpgradeBtnClicked()
    local curSelectedSelectionData = Module.ArmedForce.Field:GetCurAssemblySelectionData()
    if curSelectedSelectionData == nil then
        return
    end
    if curSelectedSelectionData:CheckIsLocked() then
        Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.NotUnlockTip)
    else
        Module.ArmedForce.Config.evtOnWeaponUpgradeBtnClicked:Invoke()
        -- Module.Gunsmith:OpenWeaponUpgradePanel(curSelectedSelectionData:GetRepresentativeItem())
        --跳转到改枪台的成长界面
        local selectItem = curSelectedSelectionData:GetRepresentativeItem()
        local bOriginalPropInfo = curSelectedSelectionData:IsOriginalPropInfo()
        local bIsDownload = curSelectedSelectionData:CheckIsDownloaded()

        if selectItem and selectItem:IsAssemblyItem() then
            -- if bIsDownload then
                --- long1 改枪和升级之前先应用当前展示干员
                --- 以免进入靶场时是临时未应用干员
                -- self:_ForceUseCurShowHeroId()
                if bOriginalPropInfo then
                    --打开成长界面
                    Module.Gunsmith:OpenWeaponUpgradePanel(selectItem)
                else
                    Module.Gunsmith:OpenWeaponUpgradePanel(selectItem)
                end
            -- else
            --     Module.CommonTips:ShowSimpleTip(ArmedForceConfig.Loc.CantGoToWeaponAssemblyNeedDownload)
            -- end
        end
        --end
        local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
        if armedForceMode == EArmedForceMode.MP then
            LogAnalysisTool.SignButtonClicked(ButtonIdEnum.MPSelectionUpgrade)
            LogAnalysisTool.SetSelectionButtonID(ButtonIdEnum.MPSelectionUpgrade)
        else
        end
        LogAnalysisTool.DoSendSelectionWeaponClickFlowLog()
    end
end

function AssemblySelectionMPMain:ReleaseTimer()
    if self._timerHandle then
        self._timerHandle:Release()
        self._timerHandle = nil
    end
end

-- 刷新主武器操作按钮
function AssemblySelectionMPMain:OnFreshMainOrSecondaryWeaponBtnState()
    local curSelectedSelectionData = Module.ArmedForce.Field:GetCurAssemblySelectionData()
    if not curSelectedSelectionData then
        logerror('AssemblySelectionMPMain:OnFreshMainOrSecondaryWeaponBtnState 报空curSelectedSelectionData=', curSelectedSelectionData)
        return
    end
    --- 是否有已装备的物品
    local bHasEquipped = curSelectedSelectionData.bSample
    --- 是否在仓库中，不在则需要购买
    local bLocked = curSelectedSelectionData:CheckIsLocked()
    local bNeedBuy = curSelectedSelectionData:IsWeaponPresetNeedBuy()
    local bOriginalPropInfo = curSelectedSelectionData:IsOriginalPropInfo()
    local bIsDownload = curSelectedSelectionData:CheckIsDownloaded()
    local selectItem = curSelectedSelectionData:GetRepresentativeItem() or nil
    
    if selectItem then
        -- 设置改装按钮显隐
        if selectItem:IsAssemblyItem() then
            if not bLocked then
                if bOriginalPropInfo then
                    self._wtChangeBtn:Visible()
                    self._wtChangeBtn:SetMainTitle(ArmedForceConfig.Loc.GoToWeaponAssembly)
                else
                    if not bNeedBuy then
                        -- self._wtChangeBtn:Visible()
                        -- self._wtChangeBtn:SetMainTitle(ArmedForceConfig.Loc.GoToWeaponPreAssembly)
                        self._wtChangeBtn:Collapsed()
                    else
                        self._wtChangeBtn:Collapsed()
                    end
                end
            else
                self._wtChangeBtn:Collapsed()
            end
        else
            self._wtChangeBtn:Collapsed()
        end
        if not bIsDownload then
            self._wtChangeBtn:SetIsEnabledStyle(false)
        end

        -- 设置操作按钮显隐
        self._wtOperaBtn:SetMainTitle(ArmedForceConfig.Loc.SelectionEquipBtnText)
        self._wtOperaBtn:Visible()

        if selectItem then
            if not bLocked then
                if bHasEquipped then
                    -- 设置卸下文案
                    if bOriginalPropInfo then
                        self._wtOperaBtn:SetIsEnabledStyle(false)
                        self._wtOperaBtn:SetMainTitle(ArmedForceConfig.Loc.SelectionMPUsingBtnText)
                        self._wtOperaBtn:Visible()
                    else
                        if not bNeedBuy then
                            if bIsDownload then
                                self._wtOperaBtn:SetIsEnabledStyle(true)
                            else
                                self._wtOperaBtn:SetIsEnabledStyle(false)
                            end
                            self._wtOperaBtn:SetMainTitle(ArmedForceConfig.Loc.SelectionMPApplyWithAssmblyBtnText)
                            self._wtOperaBtn:Visible()
                        else
                            self._wtOperaBtn:SetIsEnabledStyle(false)
                            self._wtOperaBtn:SetMainTitle(ArmedForceConfig.Loc.SelectionMPApplyWithAssmblyBtnText)
                            self._wtOperaBtn:Visible()
                        end
                    end
                else
                    local bFitArm = Module.ArmedForce:CheckWeaponItemIsFitCurrentArm(selectItem)
                    if bFitArm then
                        if bOriginalPropInfo then -- 如果是是原有的方案则显示装备或者交换
                            if bIsDownload then
                                self._wtOperaBtn:SetIsEnabledStyle(true)
                            else
                                self._wtOperaBtn:SetIsEnabledStyle(false)
                            end
                            self._wtOperaBtn:SetMainTitle(ArmedForceConfig.Loc.SelectionEquipBtnText)
                            self._wtOperaBtn:Visible()
                        else
                            if not bNeedBuy then
                                if bIsDownload then
                                    self._wtOperaBtn:SetIsEnabledStyle(true)
                                else
                                    self._wtOperaBtn:SetIsEnabledStyle(false)
                                end
                                self._wtOperaBtn:SetMainTitle(ArmedForceConfig.Loc.SelectionMPApplyWithAssmblyBtnText)
                                self._wtOperaBtn:Visible()
                            else
                                self._wtOperaBtn:SetIsEnabledStyle(false)
                                self._wtOperaBtn:SetMainTitle(ArmedForceConfig.Loc.SelectionMPApplyWithAssmblyBtnText)
                                self._wtOperaBtn:Visible()
                            end
                        end
                    else
                        self._wtOperaBtn:SetIsEnabledStyle(false)
                        local btnText = ArmedForceConfig.Loc.NotMatchArmBtnText
                        self._wtOperaBtn:SetMainTitle(btnText)
                        self._wtOperaBtn:Visible()
                    end
                end
            else
                --- 整个武器未解锁
                local btnText, unlockType = self:GetUnlockPathBtnText(selectItem.id)
                self._wtOperaBtn:SetMainTitle(btnText)
                if unlockType == EItemUnlockSources.Activity or unlockType == EItemUnlockSources.Armory then
                    self._wtOperaBtn:SetIsEnabledStyle(true)
                else
                    self._wtOperaBtn:SetIsEnabledStyle(false)
                end
                self._wtOperaBtn:Visible()
            end
        end
    end
end

-- 刷新装备操作按钮
function AssemblySelectionMPMain:OnFreshMeleeWeaponBtnState()
    local curSelectedSelectionData = Module.ArmedForce.Field:GetCurAssemblySelectionData()
    if not curSelectedSelectionData then
        logerror('AssemblySelectionMPMain:OnFreshMeleeWeaponBtnState 报空curSelectedSelectionData=', curSelectedSelectionData)
        return
    end
    --- 是否有已装备的物品
    local bHasEquipped = curSelectedSelectionData.bSample
    --- 是否在仓库中，不在则需要购买
    local bLocked = curSelectedSelectionData:CheckIsLocked()
    local selectItem = curSelectedSelectionData:GetRepresentativeItem() or nil

    self._wtChangeBtn:Collapsed()
    self._wtOperaBtn:Visible()
    self._wtOperaBtn:SetMainTitle(ArmedForceConfig.Loc.SelectionEquipBtnText)

    if selectItem then
        if not bLocked then
            if bHasEquipped then
                self._wtOperaBtn:SetIsEnabledStyle(false)
                self._wtOperaBtn:SetMainTitle(ArmedForceConfig.Loc.SelectionMPUsingBtnText)
            else
                local bFitArm = Module.ArmedForce:CheckWeaponItemIsFitCurrentArm(selectItem)
                if bFitArm then
                    self._wtOperaBtn:SetIsEnabledStyle(true)
                    self._wtOperaBtn:SetMainTitle(ArmedForceConfig.Loc.SelectionEquipBtnText)
                else
                    self._wtOperaBtn:SetIsEnabledStyle(false)
                    self._wtOperaBtn:SetMainTitle(ArmedForceConfig.Loc.NotMatchArmBtnText)
                end
            end
        else
            local btnText, unlockType = self:GetUnlockPathBtnText(selectItem.id)
            self._wtOperaBtn:SetMainTitle(btnText)
            if unlockType == EItemUnlockSources.Activity or unlockType == EItemUnlockSources.Armory then
                self._wtOperaBtn:SetIsEnabledStyle(true)
            else
                self._wtOperaBtn:SetIsEnabledStyle(false)
            end
        end
    end
end

function AssemblySelectionMPMain:OnFreshArmedForceTDMPropBtnState()
    local curSelectedSelectionData = Module.ArmedForce.Field:GetCurAssemblySelectionData()
    if not curSelectedSelectionData then
        logerror('AssemblySelectionMPMain:OnFreshArmedForceTDMPropBtnState 报空curSelectedSelectionData=', curSelectedSelectionData)
        return
    end
    --- 是否有已装备的物品
    local bHasEquipped = curSelectedSelectionData.bSample
    --- 是否在仓库中，不在则需要购买
    local bLocked = curSelectedSelectionData:CheckIsLocked()
    local selectItem = curSelectedSelectionData:GetRepresentativeItem() or nil

    self._wtChangeBtn:Collapsed()
    self._wtOperaBtn:Visible()
    self._wtOperaBtn:SetMainTitle(ArmedForceConfig.Loc.SelectionEquipBtnText)

    if selectItem then
        if not bLocked then
            if bHasEquipped then
                self._wtOperaBtn:SetIsEnabledStyle(false)
                self._wtOperaBtn:SetMainTitle(ArmedForceConfig.Loc.SelectionMPUsingBtnText)
            else
                local bFitArm = Module.ArmedForce:CheckWeaponItemIsFitCurrentArm(selectItem)
                if bFitArm then
                    self._wtOperaBtn:SetIsEnabledStyle(true)
                    self._wtOperaBtn:SetMainTitle(ArmedForceConfig.Loc.SelectionEquipBtnText)
                else
                    self._wtOperaBtn:SetIsEnabledStyle(false)
                    self._wtOperaBtn:SetMainTitle(ArmedForceConfig.Loc.NotMatchArmBtnText)
                end
            end
        else
            local btnText, unlockType = self:GetUnlockPathBtnText(selectItem.id)
            self._wtOperaBtn:SetMainTitle(btnText)
            if unlockType == EItemUnlockSources.Activity or unlockType == EItemUnlockSources.Armory then
                self._wtOperaBtn:SetIsEnabledStyle(true)
            else
                self._wtOperaBtn:SetIsEnabledStyle(false)
            end
        end
    end
end

function AssemblySelectionMPMain:OnFreshBtnState()
    local curSelectedSelectionData = Module.ArmedForce.Field:GetCurAssemblySelectionData()
    if not curSelectedSelectionData or Module.ArmedForce.Field:GetAssemblySelectionDataListLength() == 0 then
        self._wtBtnPan1:Collapsed()
        self._wtBtnPan2:Collapsed()
        self._wtItemViewStreamline:Collapsed()
        logerror("AssemblySelectionMPMain:OnFreshBtnState curSelectedSelectionData is nil! SelectionDataListLength===>", Module.ArmedForce.Field:GetAssemblySelectionDataListLength())
        return
    end

    --- 重置状态
    self._wtBtnPan1:SelfHitTestInvisible()
    self._wtOperaBtn:SetMainTitle(ArmedForceConfig.Loc.SelectionEquipBtnText)
    self._wtOperaBtn:SetIsEnabledStyle(true)
    self._wtChangeBtn:SetIsEnabledStyle(true)
    self._wtBtnPan2:Collapsed()
    for index, unLockTips in ipairs(self._wtUnLockTipsList) do
        unLockTips:Collapsed()
    end

    --- 刷新状态
    --- 主副武器走背包页协议
    if ItemHelperTool.IsMainWeaponSlotType(self._curProcessSlot.SlotType) then
        self:OnFreshMainOrSecondaryWeaponBtnState()
    else
        -- 近战武器走藏品协议
        if self._curProcessSlot.SlotType == ESlotType.MP_MeleeWeapon then
            self:OnFreshMeleeWeaponBtnState()
        -- 兵种道具走专家协议
        else
            if ItemHelperTool.IsMPArmedForceSlotType(self._curProcessSlotType) then
                -- self:OnFreshArmPropBtnState()
            else
                if self._curProcessSlot.SlotType == ESlotType.MP_ArmedForceTDMProp then
                    self:OnFreshArmedForceTDMPropBtnState()
                end
            end
        end
    end
    self:OnFreshUnlockTips()
end

function AssemblySelectionMPMain:OnFreshUnlockTips()
    local curSelectedSelectionData = Module.ArmedForce.Field:GetCurAssemblySelectionData()
    if not curSelectedSelectionData then
        logerror('AssemblySelectionMPMain:OnFreshUnlockTips 报空curSelectedSelectionData=', curSelectedSelectionData)
        return
    end
    --- 是否有已装备的物品
    local bHasEquipped = curSelectedSelectionData.bSample
    --- 是否在仓库中，不在则需要购买
    local bLocked = curSelectedSelectionData:CheckIsLocked()
    local bNeedBuy = curSelectedSelectionData:IsWeaponPresetNeedBuy()
    local bOriginalPropInfo = curSelectedSelectionData:IsOriginalPropInfo()
    local selectItem = curSelectedSelectionData:GetRepresentativeItem() or nil

    local bNeedShowTip = true
    if self._wtUnLockTipsList[1] and selectItem then
        local str, unlockType = self:GetUnlockPathTipsText(selectItem.id)
        if ItemHelperTool.IsMainWeaponSlotType(self._curProcessSlot.SlotType) then
            if bLocked then
                if unlockType ~= EItemUnlockSources.Activity and unlockType ~= EItemUnlockSources.Armory then
                    local lockedInfo = Server.InventoryServer:GetMPWeaponLockedInfoById(selectItem.id)
                    local lvNum = tonumber(lockedInfo.level) or 0
                    if lvNum > 0 then
                        str = StringUtil.SequentialFormat(ArmedForceConfig.Loc.NotUnlockSelectionText, lockedInfo.level)
                    end
                end
                self._wtUnLockTipsList[1]:SetType(2)
            else
                if bOriginalPropInfo then
                    bNeedShowTip = false
                else
                    if bNeedBuy then
                        str = ArmedForceConfig.Loc.NeedToUpgradeComponents
                        self._wtUnLockTipsList[1]:SetType(2)
                    else
                        str = ArmedForceConfig.Loc.HadUpgradeComponents
                        self._wtUnLockTipsList[1]:SetType(2)
                    end
                end
            end
            self._wtUnLockTipsList[1]:SetMainTitle(str)
            if bNeedShowTip then
                self._wtUnLockTipsList[1]:SelfHitTestInvisible()
            else
                self._wtUnLockTipsList[1]:Collapsed()
            end
        else
            self._wtUnLockTipsList[1]:Collapsed()
        end
    end
end

function AssemblySelectionMPMain:OnSelectionChangeFinished()
	self._wtOperaBtn:SelfHitTestInvisible()
	self._wtChangeBtn:SelfHitTestInvisible()
end

function AssemblySelectionMPMain:OnPostUpdateDeposData()
    self:_OnReGenerateMergeInfo()
end

function AssemblySelectionMPMain:_OnReGenerateMergeInfo()
    if Module.ArmedForce.Field:GetCurOperationType() == ArmedForceConfig.ESelectionOperationType.Batch then
        -- SelectionDataLogic.RefreshCurSelectionData(self._curProcessSlot)
    end
end

function AssemblySelectionMPMain:_OnItemDataChanged(item)
    if item then
        if Module.ArmedForce.Field:GetCurOperationType() == ArmedForceConfig.ESelectionOperationType.Single then
            local curSelectedSelectionData = Module.ArmedForce.Field:GetCurAssemblySelectionData()
            if curSelectedSelectionData and curSelectedSelectionData:IsOwnItem(item) then
                if item.CurDurability ~= curSelectedSelectionData.durability then
                    -- SelectionDataLogic.RefreshCurSelectionDataDurability(self._curProcessSlot, item.CurDurability)
                end
            end
        end
        local curSelectedSelectionData = Module.ArmedForce.Field:GetCurAssemblySelectionData()
        if curSelectedSelectionData then
            self:_OnCommonItemClicked(curSelectedSelectionData)
        end
    end
end

function AssemblySelectionMPMain:_OnHeroUsed()
    self:InitBySlotClicked(self._curProcessSlotType)
    local posIdx = Module.ArmedForce.Field:GetCurOperaAssemblySelectionDataIdx()
    if posIdx then
        self:DoSelectCommonItem(posIdx)
    end
end

function AssemblySelectionMPMain:_OnCSAuctionGetSaleListBatchRes(res)
    self._wtWaterFallList:RefreshAllItems()
    -- self:OnWidgetClicked(1)
end

function AssemblySelectionMPMain:OnGetPresetCount()
    return #self._weaponPresetDataList or 0
end
function AssemblySelectionMPMain:OnPresetProcessTabItemWidget(position, droItem)
    local dataIdx = position + 1
    local droDownData = self._weaponPresetDataList[dataIdx]
    if not droDownData then
        return
    end
    local fPresetItemOnClicked = CreateCallBack(self.OnPresetCheckedTabIndexChanged, self)
    droItem:InitData(dataIdx, droDownData, fPresetItemOnClicked)

    if dataIdx == self._weaponPresetIndex then
        droItem:SetSelected(true)
    else
        droItem:SetSelected(false)
    end
end

function AssemblySelectionMPMain:OnPresetCheckedTabIndexChanged(idx)
    if self._weaponPresetIndex ~= idx and self._wtPresetDownBoxInsId then
        local weakUIIns, instanceId = Facade.UIManager:GetSubUI(self, UIName2ID.CommonItemViewDropDownBox, self._wtPresetDownBoxInsId)
        local wtPresetDownBox = getfromweak(weakUIIns)
        if wtPresetDownBox then
            local preDroItem = wtPresetDownBox.ScrollGridBox:GetItemByIndex(self._weaponPresetIndex - 1)
            if preDroItem then
                preDroItem:SetSelected(false)
            end
            self._weaponPresetIndex = idx
            local curDroItem = wtPresetDownBox.ScrollGridBox:GetItemByIndex(self._weaponPresetIndex - 1)
            if curDroItem then
                curDroItem:SetSelected(true)
            end
            local assemblySelectionData = Module.ArmedForce.Field:GetCurAssemblySelectionData()
            if assemblySelectionData then
                self:_OnCommonItemClicked(assemblySelectionData)
            end
        end
    end
end

function AssemblySelectionMPMain:GetUnlockPathBtnText(itemId)
    local unlockStr = ArmedForceConfig.Loc.NotUnlockSelectionDepos
    local itemUnlockPathRegistryItem = Server.ItemUnlockPathServer:GetPreferredItemUnlockPathRegistryItem(EArmedForceMode.MP, itemId)
    local unlockType
    if itemUnlockPathRegistryItem then
        if itemUnlockPathRegistryItem.source == EItemUnlockSources.Activity then
            unlockStr = ArmedForceConfig.Loc.NotUnlock2Jump
            unlockType = itemUnlockPathRegistryItem.source
        elseif itemUnlockPathRegistryItem.source == EItemUnlockSources.Armory then
            unlockStr = ArmedForceConfig.Loc.NotUnlock2Jump
            unlockType = itemUnlockPathRegistryItem.source
        end
    end
    return unlockStr, unlockType
end

function AssemblySelectionMPMain:GetUnlockPathTipsText(itemId)
    local unlockStr = ArmedForceConfig.Loc.NotUnlockSelectionDepos
    local itemUnlockPathRegistryItem = Server.ItemUnlockPathServer:GetPreferredItemUnlockPathRegistryItem(EArmedForceMode.MP, itemId)
    local unlockType
    if itemUnlockPathRegistryItem then
        if itemUnlockPathRegistryItem.source == EItemUnlockSources.Activity then
            unlockStr = ArmedForceConfig.Loc.NotUnlockSelectionActivity
            unlockType = itemUnlockPathRegistryItem.source
        elseif itemUnlockPathRegistryItem.source == EItemUnlockSources.Armory then
            unlockStr = ArmedForceConfig.Loc.NotUnlockSelectionArmory
            unlockType = itemUnlockPathRegistryItem.source
        end
    end
    return unlockStr, unlockType
end

function AssemblySelectionMPMain:_OnHandleMouseButtonUpEvent(mouseEvent)
    -- if self and not self:IsRelease() and self._wtPresetDownBoxInsId then
    --     local weakUIIns, instanceId = Facade.UIManager:GetSubUI(self, UIName2ID.CommonItemViewDropDownBox, self._wtPresetDownBoxInsId)
    --     local wtPresetDownBox = getfromweak(weakUIIns)
    --     if wtPresetDownBox then
    --         local absolutePoint = mouseEvent:GetScreenSpacePosition()
    --         local bZoomRateInside = UIUtil.CheckAbsolutePointInsideWidget(wtPresetDownBox, absolutePoint)
    --         if not bZoomRateInside then
    --             wtPresetDownBox:BP_SwitchCheckButtonState(ECheckButtonState.Unchecked)
    --         end
    --     end
    -- end
end


function AssemblySelectionMPMain:_WaterfallFocusProxyMaker(inWidget)
    -- 找到所在行
    local row = WidgetUtil.GetParentWidgetByClassName(inWidget, "WBP_SelectionAssemblyDeposCell_V1_C")
    local rowIndex = self._wtWaterFallList:GetIndexByItem(row)
    return {rowIndex}
end

function AssemblySelectionMPMain:_WaterfallFocusProxyResolver(inProxyHandle)
    local rowIndex = inProxyHandle[1]
    -- 可能item会在屏幕外，先执行滚动
    -- self._wtWaterFallList:ScrollToIndexToScreen(rowIndex, 0.5, 0.5)
    local row = self._wtWaterFallList:GetItemByIndex(rowIndex)
    if row then
        return row
    end
    -- 可能会找不到，返回空，自动使用Gorup的默认逻辑查找聚焦
    return nil
end

function AssemblySelectionMPMain:_CustomBoundaryRule(direction)
    if direction ==  EUINavigation.Down then
        self._wtWaterFallList:ScrollToIndex(1)
        local item = self._wtWaterFallList:GetItemByIndex(1)
        return item
    elseif direction == EUINavigation.Up then
        local lastIdx = self:_OnGetItemCount()
        self._wtWaterFallList:ScrollToIndex(lastIdx)
        local item = self._wtWaterFallList:GetItemByIndex(lastIdx)
        return item
    else
        return nil
    end
end

-- BEGIN MODIFICATION @ VIRTUOS : 导航设置
function AssemblySelectionMPMain:_EnableNavigation(bEnable)
    if not IsHD() then
        return 
    end

    if bEnable then
        -- 导航组
        self._wtNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtWaterFallList, self, "Hittest")
        if self._wtNavGroup then
            self._wtNavGroup:AddNavWidgetToArray(self._wtWaterFallList)
            self._wtNavGroup:SetScrollRecipient(self._wtWaterFallList)
            self._wtNavGroup:SetEnableRightStickScrolling(false)
            self._wtNavGroup:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
            WidgetUtil.BindCustomFocusProxy(self._wtNavGroup, self._WaterfallFocusProxyMaker, self._WaterfallFocusProxyResolver, self)
            WidgetUtil.BindCustomBoundaryNavRule(self._wtNavGroup, self._CustomBoundaryRule, self)
            local navStrategy = self._wtNavGroup:GetOwnerNavStrategy()
            if navStrategy then
                navStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.LastFocusedOrHittestOrDistance)
            end  
        end

        -- self._wtNavGroupProcessBtns = WidgetUtil.RegisterNavigationGroup(self._wtVbProcessBtns, self, "Hittest")
        -- if self._wtNavGroupProcessBtns then
        --     self._wtNavGroupProcessBtns:AddNavWidgetToArray(self._wtVbProcessBtns)
        -- end

        -- 枪械详情信息
        local ItemView_RealContentSlot = self._wtItemViewStreamline:Wnd("_wRealContentSlot", UIWidgetBase)
        -- local ItemView_DetailButton = self._wtItemViewStreamline:Wnd("WBP_DFCommonButtonV3S1", UIWidgetBase)
        -- local ItemView_ExContentSlot = self._wtItemViewStreamline:Wnd("DFNamedSlot_44", UIWidgetBase)
        -- local ItemView_DetailButtonPanel = self._wtItemViewStreamline:Wnd("DFNamedSlot_Btn", UIWidgetBase)
        -- RealContentSlot
        if not self._wtNavGroup_RealContentSlot then
            self._wtNavGroup_RealContentSlot = WidgetUtil.RegisterNavigationGroup(ItemView_RealContentSlot, self, "Hittest")
            if self._wtNavGroup_RealContentSlot then
                self._wtNavGroup_RealContentSlot:AddNavWidgetToArray(ItemView_RealContentSlot)
                self._wtNavGroup_RealContentSlot:SetScrollRecipient(self._wtItemViewStreamline_ScrollBox)
                local navStrategy = self._wtNavGroup_RealContentSlot:GetOwnerNavStrategy()
                if navStrategy then
                    navStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.LastFocusedOrHittestOrDistance)
                end  
            end
        end

        -- 启用禁用A键导航的配置
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)
    else
        -- 移除导航组
        WidgetUtil.RemoveNavigationGroup(self)
        self._wtNavGroup = nil
        self._wtNavGroupPresetDropDown = nil
        -- self._wtNavGroupProcessBtns = nil
        self._wtNavGroup_RealContentSlot = nil
        -- 恢复默认导航配置
        WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
        WidgetUtil.DisableDynamicNavConfigsbyWidget(self._wtItemViewStreamline)    
    end    
end

function AssemblySelectionMPMain:_EnbalePresetDownBoxFocus()
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
end

function AssemblySelectionMPMain:_DisablePresetDownBoxFocus()
    -- 启用禁用A键导航的配置
    WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)
end


-- 新增手柄快捷键（页面中显示的按钮：装备、改装、旋转武器）
function AssemblySelectionMPMain:_InitShortcuts()
    if not IsHD() then
        return 
    end

    -- 装备
    if not self._Equip then
        self._Equip = self:AddInputActionBinding("Assembly_Equip_Gamepad", EInputEvent.IE_Pressed, self._ShortcutEventOnProcessBtnClicked,self, EDisplayInputActionPriority.UI_Stack)
        self._wtOperaBtn:SetDisplayInputAction("Assembly_Equip_Gamepad", true, nil, true)
    end
    
    -- 改装
    if not self._Modification then
        self._Modification = self:AddInputActionBinding("Assembly_Modification_Gamepad", EInputEvent.IE_Pressed, self._ShortcutEventOnAssemblyBtnClicked,self, EDisplayInputActionPriority.UI_Stack)
        self._wtChangeBtn:SetDisplayInputAction("Assembly_Modification_Gamepad", true, nil, true)
    end

    -- 武器旋转
    if not self._WeaponRotation_X then
        self._WeaponRotation_X = self:AddAxisInputActionBinding("Common_Right_X", self._HandleCameraRotation_X, self, EDisplayInputActionPriority.UI_Stack)
    end

    if not self._WeaponRotation_Y then
        self._WeaponRotation_Y = self:AddAxisInputActionBinding("Common_Right_Y", self._HandleCameraRotation_Y, self, EDisplayInputActionPriority.UI_Stack)
    end
end

function AssemblySelectionMPMain:_RemoveShortcuts()
    if not IsHD() then
        return 
    end

    if self._Upgrage then
        self:RemoveInputActionBinding(self._Upgrage)
        self._Upgrage= nil
    end
    if self._Equip then
        self:RemoveInputActionBinding(self._Equip)
        self._Equip= nil
    end
    if self._Modification then
        self:RemoveInputActionBinding(self._Modification)
        self._Modification= nil
    end

    -- 移除武器旋转绑定的事件
    if self._WeaponRotation_X then
        self:RemoveInputActionBinding(self._WeaponRotation_X)
        self._WeaponRotation_X = nil
    end

    if self._WeaponRotation_Y then
        self:RemoveInputActionBinding(self._WeaponRotation_Y)
        self._WeaponRotation_Y = nil
    end
end

function AssemblySelectionMPMain:OnAssemblyBtnFocusReceived()
    -- self.bChangeBtnFocus = true
    -- WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default, self)
end

function AssemblySelectionMPMain:OnAssemblyBtnFocusLost()
    -- self.bChangeBtnFocus = false
    -- WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)
end


function AssemblySelectionMPMain:_OnUpgradeBtnFocusReceived()
    -- self.bUpgradeBtnFocus = true
    -- WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default, self)
end

function AssemblySelectionMPMain:_OnUpgradeBtnFocusLost()
    -- self.bUpgradeBtnFocus = false
    -- WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)
end

function AssemblySelectionMPMain:IsBtnGroupFocus()
    if IsHD() then
        return false
        -- local bHasFocus = false
        -- if self._wtDFCommonButtonV3S1InsId then
        --     local weakUIIns, instanceId = Facade.UIManager:GetSubUI(self, UIName2ID.DFCommonButtonV3S1, self._wtDFCommonButtonV3S1InsId)
        --     local unlockLvBtn = getfromweak(weakUIIns)
        --     if unlockLvBtn then
        --         bHasFocus =  unlockLvBtn:HasAnyUserFocus()
        --         if bHasFocus then return bHasFocus end
        --     end
        -- end
        -- bHasFocus = bHasFocus or self._wtChangeBtn:HasAnyUserFocus()
        -- bHasFocus = bHasFocus or self.bChangeBtnFocus
        -- bHasFocus = bHasFocus or self.bUpgradeBtnFocus
        -- return bHasFocus
    end
end

function AssemblySelectionMPMain:_ShortcutEventOnProcessBtnClicked()
    if IsHD() and self._wtOperaBtn and self._wtOperaBtn:IsVisible() and not self:IsBtnGroupFocus() then
        self._wtOperaBtn:ButtonClick()
    end
end

function AssemblySelectionMPMain:_ShortcutEventOnAssemblyBtnClicked()
    if IsHD() and self._wtChangeBtn and self._wtChangeBtn:IsVisible() then
        self._wtChangeBtn:ButtonClick()
    end
end

function AssemblySelectionMPMain:_InitHallMainDisplayCtrl()
    if not IsHD() then
        return 
    end

    if not self.HallMainDisplayCtrl then
        self.HallMainDisplayCtrl = AHallMainDisplayCtrl.Get(GetGameInstance())
    end
end

-- 用手柄遥感的输入是否为0来模拟鼠标旋转开始、结束状态
function AssemblySelectionMPMain:_EnableRotationbyGamepad(bEnable)
    if not IsHD() then
        return 
    end

    if not isvalid(self.HallMainDisplayCtrl) or not isvalid(self.HallMainDisplayCtrl.CameraCtrlComponent) then
        logerror("[HallMainDisplayCtrl][_EnableRotationbyGamepad]", "HallMainDisplayCtrl Init Failed!")
        return 
    end

    if bEnable then
        self.HallMainDisplayCtrl.CameraCtrlComponent:OnLeftMouseButtonDown()
        self._bCameraStartRotation = true
    else
        self.HallMainDisplayCtrl.CameraCtrlComponent:OnLeftMouseButtonUp()
        self._bCameraStartRotation = false
    end
end

function AssemblySelectionMPMain:_HandleCameraRotation_X(value)
    self._RotationVec.X = value
    self:_HandleCameraRotation()
end

function AssemblySelectionMPMain:_HandleCameraRotation_Y(value)
    self._RotationVec.Y = value
    self:_HandleCameraRotation()
end

function AssemblySelectionMPMain:_HandleCameraRotation()
    if not IsHD() then
        return 
    end

    if not WidgetUtil.IsGamepad() or (WidgetUtil.IsEquipZero(self._RotationVec.X) and WidgetUtil.IsEquipZero(self._RotationVec.Y)) then
        if self._bCameraStartRotation then
            self:_EnableRotationbyGamepad(false)
        end

        return 
    end

    if not self._bCameraStartRotation then
        self:_EnableRotationbyGamepad(true)
    end

    if isvalid(self.HallMainDisplayCtrl) and isvalid(self.HallMainDisplayCtrl.CameraCtrlComponent) then
        local RotateZoom = 5
        self.HallMainDisplayCtrl.CameraCtrlComponent:OnRotationByGamepad(self._RotationVec.X * RotateZoom, self._RotationVec.Y * RotateZoom)
    end
end
-- END MODIFICATION

--endregion
--==================================================
function AssemblySelectionMPMain:FreshItemUIDownloadMask(item)
    -- local bIsDownload = Module.ArmedForce:CheckItemWithCompsDownloaded(item)
    local curSelectedSelectionData = Module.ArmedForce.Field:GetCurAssemblySelectionData()
    local bIsDownload = curSelectedSelectionData:CheckIsDownloaded()
    if not bIsDownload then
        if self._commonDownload == nil then
            self:_InternalAddCommonDownloadSubUI()
        end
        local commonDownloadInstance = getfromweak(self._commonDownload)
        if commonDownloadInstance and commonDownloadInstance.InitModuleKey then
            local pakCategory = Module.LitePackage:GetWeaponPartModuleKey()
            commonDownloadInstance:InitModuleKey(pakCategory)
        end
    end
    local bIsVisible = not bIsDownload
    self:_InternalSetDownloadPanel(bIsVisible)
end

function AssemblySelectionMPMain:_InternalSetDownloadPanel(bIsVisible)
    if bIsVisible then
        self._wtDownloadPanel:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        self._wtDownloadPanel:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function AssemblySelectionMPMain:_InternalAddCommonDownloadSubUI()
    if isinvalid(self._commonDownload) then
        self._commonDownload = Facade.UIManager:AddSubUI(self, UIName2ID.LitePackageCommonDownload, self._wtDownloadPanel)
    end
    local commonDownloadInstance = getfromweak(self._commonDownload)
    if isinvalid(commonDownloadInstance) then
        return
    end
    commonDownloadInstance:SetUIPositionType(1)

    local slot = UWidgetLayoutLibrary.SlotAsCanvasSlot(commonDownloadInstance)
    if slot == nil then
        return
    end
    local anchor = FAnchors()
    anchor.Minimum = FVector2D(0, 0)
    anchor.Maximum = FVector2D(1.0, 1.0)
    slot:SetAnchors(anchor)
    slot:SetAlignment(FVector2D(0, 0))
    slot:SetOffsets(FMargin(0, 0, 0, 0))
end

function AssemblySelectionMPMain:_OnMouseEnterItemView(bIsMouseEnter)
    if not IsHD() then
        return 
    end

    if bIsMouseEnter == true then
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default, self._wtItemViewStreamline)
    else
        WidgetUtil.DisableDynamicNavConfigsbyWidget(self._wtItemViewStreamline)
    end
end

return AssemblySelectionMPMain