----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class ActivityRadionChildMainPanel : LuaUIBaseView
local ActivityRadionChildMainPanel = ui("ActivityRadionChildMainPanel")
local ActivityRadioScreen = require "DFM.Business.Module.ActivityModule.UI.RadioNews.ActivityRadioScreen"
local ActivityConfig = Module.Activity.Config
local ActivityLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"

function ActivityRadionChildMainPanel:Ctor()
    self._activityID = 0
    self._activityInfo = {}
    self._radioConfig = {}
    
    self._index = 0
    self._curChannel = 0

    -- 主面板（非root, 包含除加载和遮罩外所有控件的面板）
    self._wtCanvasPanel = self:Wnd("DFCanvasPanel_24", UIWidgetBase)

    -- 主面板背景
    self._wtBgImg = self:Wnd("DFImage_Bg", UIImage)

    -- 主面板屏幕
	self._wtRadioScreen = self:Wnd("WBP_RadioNews_Screen", ActivityRadioScreen)

    -- 调节按钮
    self._wtAdjustPanel = self:Wnd("DFCanvasPanel_46", UIWidgetBase)
    self._wtAdjustOnImg = self:Wnd("DFImage_Off", UIImage)
    self._wtAdjustOffImg = self:Wnd("DFImage_On", UIImage)

    -- 子界面加载面板
    self._wtChildPanel = self:Wnd("Panel_ChildPanel", UIWidgetBase)

    self._uiNavIdList = {
        [1] = UIName2ID.ActivityRadioChildPanelV1,
        [2] = UIName2ID.ActivityRadioChildPanelV2,
        [3] = UIName2ID.ActivityRadioChildPanelV3,
    }

    self:Wnd("DFCanvasPanel_VFX", UIWidgetBase):Collapsed()
    self:Wnd("WBP_Activity_CommonMask_62", UIWidgetBase):Collapsed()
    self:Wnd("WBP_Common_UnScaleBg", UIWidgetBase):Collapsed()
end

-----------------------------------------------------生命周期-----------------------------------------------------
function ActivityRadionChildMainPanel:OnShowBegin()
    self:RefreshUI()
end

function ActivityRadionChildMainPanel:OnInitExtraData(activityID, activityInfo, radioConfig, index, channel)
    self._activityID = activityID or 0
    self._activityInfo = activityInfo or {}
    self._radioConfig = radioConfig or {}
    self._index = index or 0
    self._curChannel = channel or 0
end

--- 主面板驱动数据更新
function ActivityRadionChildMainPanel:RefreshData(...)
    self:OnInitExtraData(...)
    self:RefreshUI()
end

function ActivityRadionChildMainPanel:OnHideBegin()
    self:_OnRemoveSubUI()
end

function ActivityRadionChildMainPanel:OnClose()
end

-----------------------------------------------------UI刷新-----------------------------------------------------
function ActivityRadionChildMainPanel:RefreshUI()
    self:_InitWidgetVisibility()
    -- self:_OnRefreshScreenPanel()
    self:_OnOpenChildPanel()
end

function ActivityRadionChildMainPanel:_InitWidgetVisibility()
    -- ActivityLogic.SetUIVisibility(self._wtAdjustOnImg, false)
    -- ActivityLogic.SetUIVisibility(self._wtAdjustOffImg, true)

    -- 盖不住主面板的UI，因此子主面板先隐藏所有控件
    ActivityLogic.SetUIVisibility(self._wtCanvasPanel, false)
end

function ActivityRadionChildMainPanel:_OnRefreshScreenPanel()
    self._wtRadioScreen:OnPlayChannelLoopAnimation(self._curChannel)
end

function ActivityRadionChildMainPanel:_OnOpenChildPanel()
    local uiNavID = self._uiNavIdList[self._index]
    if uiNavID == nil then return end
    
    if self._currentChildPage then
        self._currentChildPage:RefreshData(self._activityID, self._activityInfo)
    else
        local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, uiNavID, self._wtChildPanel, nil, self._activityID, self._activityInfo, self._radioConfig)
        self._instanceId = instanceId
        local uiIns = getfromweak(weakUIIns)
        if uiIns then
            self._currentChildPage = uiIns
            UIUtil.SetWidgetToParent_Full(self._currentChildPage, self._wtChildPanel)
        end
    end
end

function ActivityRadionChildMainPanel:_OnRemoveSubUI()
    if self._currentChildPage and self._uiNavIdList[self._index] then
        Facade.UIManager:RemoveSubUI(self, self._uiNavIdList[self._index], self._instanceId)
        self._currentChildPage = nil
        self._index = 0
    end
end

--- 暂时废弃，背景为UIImage，在蓝图里写死
function ActivityRadionChildMainPanel:_InitCdnImg(backgroundImg)
    if backgroundImg and backgroundImg ~= "" then
        local backImg = "Resource/Texture/Activity/" .. tostring(backgroundImg)
        self._wtBgImg:SetCDNImage(backImg, false, Module.CDNIcon.Config.ECdnTagEnum.Activity)
    end
end

return ActivityRadionChildMainPanel