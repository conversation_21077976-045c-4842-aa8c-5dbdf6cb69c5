----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGuide)
----- LOG FUNCTION AUTO GENERATE END -----------

--BEGIN MODIFICATION @ VIRTUOS : 引导GamePad扩展
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local UGPInputDelegates = import "GPInputDelegates"
-- END MODIFICATION

local GuideLogic = require "DFM.Business.Module.GuideModule.GuideLogic"

---@class GuideHDClickBtnUI : LuaUIBaseView
local GuideHDClickBtnUI = ui("GuideHDClickBtnUI")

local function log(...)
    loginfo("[GuideHDClickBtnUI]", ...)
end

function GuideHDClickBtnUI:Ctor()
    self._wtButton = self:Wnd("Button_81", UIWidgetBase)
    self._realSetSizeWidget = self:Wnd("Image_DE_0", UIImage)
    self._realSetSizeWidget2 = self:Wnd("Image_DE_1", UIImage)
    self._wtText = self:Wnd("wtText", UITextBlock)
    self._wtText2 = self:Wnd("wtText2", UITextBlock)

    self.loopAnimType = 0 -- 循环动画
end

--==================================================
--region Life function
    -- BEGIN MODIFICATION @ VIRTUOS :
function GuideHDClickBtnUI:OnShowBegin()
    if IsHD() then 
        self._OnNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._handleInputTypeChanged, self))
        self._wtTips = self:Wnd("DFCanvasPanel_158", UIWidgetBase)
        self:_UpdateShowTips()
    end
end
function GuideHDClickBtnUI:_handleInputTypeChanged(inputType)
    if IsHD() then
       self:_UpdateShowTips()
    end 
end
function GuideHDClickBtnUI:OnHide()
    -- if IsHD() then 
    --     --移除输入设备切换事件    
    --     if self._OnNotifyInputTypeChangedHandle then
    --         UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._OnNotifyInputTypeChangedHandle)
    --         self._OnNotifyInputTypeChangedHandle = nil
    --     end
    -- end
end

-- TODO: 处理一下手柄的tips
function GuideHDClickBtnUI:_UpdateShowTips()
    -- if IsHD() then
    --     if WidgetUtil.IsGamepad() then
    --         self._wtTips:Collapsed()
    --     else
    --         self._wtTips:SelfHitTestInvisible()
    --     end 
    -- end 
end

--END MODIFICATION

function GuideHDClickBtnUI:_EnableBtn()
    -- self._wtButton:SetVisibility(bEnable and ESlateVisibility.HitTestInvisible or ESlateVisibility.Collapsed)
end


function GuideHDClickBtnUI:OnOpen()
end

function GuideHDClickBtnUI:OnClose()
    self._bClose = true
end

-- pos:锚点在(0.5, 0.5)的位置
function GuideHDClickBtnUI:SetPos(pos)
    self.Slot:SetPosition(pos) 
end

-- 矩形用
function GuideHDClickBtnUI:SetSize(size)
    self:SetCppValue("SizeX", size.X)
    self:SetCppValue("SizeY", size.Y)
end

-- 圆形用
function GuideHDClickBtnUI:SetScale(scale)
    self:SetCppValue("Multiple_X", scale)
    self:SetCppValue("Multiple_Y", scale)
end

function GuideHDClickBtnUI:SetUIType(uiType)
    uiType = setdefault(uiType, 0)
    local uiTypeDict = {
        [0] = 0,
        [1] = 0,
        [2] = 1,
        [3] = 2,
        [4] = 1,
    }
    self:SetCppValue("Type", uiTypeDict[uiType])
    self:Set_Type(uiTypeDict[uiType])

    local opacityDict = {
        [0] = 0,
        [1] = 0,
        [2] = 0,
        [3] = 0,
        [4] = 1,
    }
    self:SetOpacity(opacityDict[uiType])
end

function GuideHDClickBtnUI:GetRealSetSizeWidget()
    return self._realSetSizeWidget:IsVisible() and self._realSetSizeWidget or self._realSetSizeWidget2
end

function GuideHDClickBtnUI:SetTipsText(text)
    if 1 and self._wtText2 then
        self._wtText2:HitTestInvisible()
        self._wtText2:SetText(text)
        self._wtText:Collapsed()
    else
        self._wtText:SetText(text)
    end
end

function GuideHDClickBtnUI:PlayInAnim(loopAnimType)
    self:StopAllAnimations()

    loopAnimType = setdefault(loopAnimType, 0)
    if loopAnimType == 0 then
        self:PlayWidgetAnim(self.Anima_loop, 0)
    elseif loopAnimType == 1 then
        self:PlayWidgetAnim(self.Anima_loop_dianji, 0)
    elseif loopAnimType == 2 then
        self:PlayWidgetAnim(self.Anima_FlickThreeTime, 0)
    end
    --self:PlayWidgetAnim(self.Anima_in)
end

function GuideHDClickBtnUI:OnAnimFinished(anim)
    -- if anim == self.Anima_in then
    --     self:PlayWidgetAnim(self.Anima_loop, 0)
    -- end
end

return GuideHDClickBtnUI
