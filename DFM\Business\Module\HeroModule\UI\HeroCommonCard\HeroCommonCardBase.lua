----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMHero)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class HeroCommonCardBase : LuaUIBaseView
local HeroCommonCardBase = ui("HeroCommonCardBase")
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local HeroHelperTool = require "DFM.StandaloneLua.BusinessTool.HeroHelperTool"
local TextStyleBlueprintLib = import "TextStyleBlueprintLib"
local UDFMGameplayBlueprintHelper = import "DFMGameplayBlueprintHelper"
local ETeamIdentity = import "ETeamIdentity"
local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"
local CommonSocialTitleItem_Big = require "DFM.Business.Module.RoleInfoModule.UI.RoleComponent.CommonSocialTitleItem_Big"

---InGameController
---@return InGameController
local function GetInGameController()
    return InGameController:Get()
end

function HeroCommonCardBase:Ctor()
    loginfo("[georgecgong] HeroCommonCardBase:Ctor()")
    self:InjectLua()
    -- 名片图片
    self._wtCardImage = self:Wnd("ArmedForceImg", UIImage)

    ------------------------------ 称号相关被剥离为称号组件 ------------------------------
    -- 玩家名称
    -- self._wtPlayerName = self:Wnd("NameTB", UITextBlock)
    -- 称号 - 头衔名
    -- self._wtTitleName = self:Wnd("LevelText", UITextBlock)
    -- self._wtTitleName:Collapsed()
    -- 称号 - 背景图片
    -- self._wtTitleImg = self:Wnd("DFImage_Long", UIImage)
    -- self._wtTitleImg:Collapsed()
    -- 称号 - 图标
    -- self._wtTitleIcon = self:Wnd("Title_Icon", UIImage)
    -- self._wtTitleIcon:Collapsed()
    -- 称号组件
    self._wtTitleItem = self:Wnd("WBP_RoleInfo_TitleItem", CommonSocialTitleItem_Big)
    ------------------------------ 称号相关被剥离为称号组件 ------------------------------

    -- 身份标识
    self._wtIdentityIcon = self:Wnd("DFImage_TeamIdentity", UIWidgetBase)
    -- 徽章
    self._wtBadgesContainer = self:Wnd("PlatformPaddingBox_0", UIWidgetBase)
    self._ListBadgeUI = {
        [0] = self:Wnd("WBP_Common_CardIcon", UIWidgetBase),
        [1] = self:Wnd("WBP_Common_CardIcon_1", UIWidgetBase),
        [2] = self:Wnd("WBP_Common_CardIcon_2", UIWidgetBase),
    }
    for k, widget in pairs(self._ListBadgeUI) do
        widget:Hidden()
    end
    -- 小队序号
    self._wtIdxTB = self:Wnd("DFTextBlock_132", UITextBlock)
    self._wtIdxImg = self:Wnd("DFImage_218", UIImage)  
    -- 游戏状态（SOL撤离）
    self._wtGameStateTB = self:Wnd("DFTextBlock_266", UITextBlock)
    -- 数据栏（Icon用settype管理）
    self._wtCarryOutProfitPriceTB = self:Wnd("TotalPriceTB_1", UITextBlock)
    self._wtKillCountTB = self:Wnd("TotalKillTB_1", UITextBlock)
    self._wtRescueCountTB = self:Wnd("TotalSaveTB_1", UITextBlock)
    self._wtResurgenceCountTB = self:Wnd("TotalResurrectTB_1", UITextBlock)

    -- BEGIN MODIFICATION @ VIRTUOS : 存一下玩家ID，用来拿空的勋章位
    if IsHD() then
        self._heroIdStr = nil
    end
    -- END MODIFICATION
end

function HeroCommonCardBase:OnInitExtraData(needHoverAnim)
    loginfo("HeroCommonCardBase:OnInitExtraData", needHoverAnim)
    self._needHoverAnim = needHoverAnim
    if self._needHoverAnim then
        self:Visible()
        self:SetCppValue("bHandleHover", true)
        self:Event("OnHovered", self._OnHovered, self)
        self:Event("OnUnHovered", self._OnUnHovered, self)
    end
end

-- 通过干员ID设置当前玩家装备的名片图片
function HeroCommonCardBase:SetSelfCardImgPath(heroIdStr)
    heroIdStr = tostring(heroIdStr)
    local CardImagePath = HeroHelperTool.GetSelectedHeroCardPath(heroIdStr)
    self:SetCardImgPath(CardImagePath)
end

-- 通过名片道具ID设置名片图片
function HeroCommonCardBase:SetCardImgPathByCardId(cardId)
    loginfo("HeroCommonCardBase:SetCardImgPathByCardId", cardId)
    if cardId then
        local CardImagePath = HeroHelperTool.GetHeroCardPathByCardId(cardId)
        self:SetCardImgPath(CardImagePath)
    end
end

-- 设置干员名片图片
function HeroCommonCardBase:SetCardImgPath(CardImagePath)
    loginfo("HeroCommonCardBase:SetCardImgPath", CardImagePath and CardImagePath.AssetPathName)
    if CardImagePath and CardImagePath.AssetPathName ~= "None" then
        self._wtCardImage:AsyncSetImagePath(CardImagePath)
    else
        logerror("HeroCommonCardBase:SetCardImgPath, CardImagePath is nil!!!")
        self._wtCardImage:AsyncSetImagePath(Module.Hero.Config.DefaultCardPath)
    end
end

-- 通过当前玩家名字设置玩家名字
function HeroCommonCardBase:SetSelfPlayerName(customName)
    if Server.RoleInfoServer.nickName then
        self:SetPlayerName(customName or Server.RoleInfoServer.nickName)
    end
end

-- 设置玩家名字
function HeroCommonCardBase:SetPlayerName(playerName)
    loginfo("HeroCommonCardBase:SetPlayerName, name: " .. playerName)
    self._wtTitleItem:SetPlayerName(playerName)
end

function HeroCommonCardBase:Imp_SetPlayerNameColor(color)
    self:SetPlayerNameTextColor(color)
end

-- azhengzheng:设置干员名字颜色
-- color:rowName
function HeroCommonCardBase:SetPlayerNameTextColor(color)
    self._wtTitleItem:SetPlayerNameTextColor(color)
end

--- BEGIN MODIFICATION @ VIRTUOS
-- 设置玩家的平台标识
function HeroCommonCardBase:SetPlayerPlatformIDType(platID)
    self._wtTitleItem:SetPlatformIDType(platID)
end

-- 设置玩家名字是否能聚焦
function HeroCommonCardBase:SetPlayerNameFocusable(bFocusable)
    if self._wtTitleItem ~= nil then
        self._wtTitleItem:SetButtonFocusable(bFocusable)
    end
end
--- END MODIFICATION


-- 通过干员ID设置当前玩家装备的头衔名字
-- function HeroCommonCardBase:SetSelfTitleName(heroIdStr)
--     heroIdStr = tostring(heroIdStr)
--     local TitleName = HeroHelperTool.GetSelectedHeroTitleName(heroIdStr)
--     if TitleName then
--         self:SetTitleName(TitleName)
--     else
--         self._wtTitleName:Collapsed()
--     end
-- end

-- 通过头衔道具ID设置头衔名字
-- function HeroCommonCardBase:SetTitleNameByTitleId(titleId)
--     local TitleName = Module.RoleInfo:GetTitleName(titleId)
--     if TitleName then
--         self:SetTitleName(TitleName)
--     else
--         self._wtTitleName:Collapsed()
--     end
-- end

-- 设置干员头衔名字
-- function HeroCommonCardBase:SetTitleName(titleName)
--     if titleName then
--         self._wtTitleName:SetText(titleName)
--         self._wtTitleName:SelfHitTestInvisible()
--     else
--         self._wtTitleName:Collapsed()
--     end
-- end

-- 通过干员ID设置当前玩家装备的头衔图片
-- function HeroCommonCardBase:SetSelfTitleImgPath(heroIdStr)
--     heroIdStr = tostring(heroIdStr)
--     local CardImagePath = HeroHelperTool.GetSelectedHeroTitleImg(heroIdStr)
--     if CardImagePath then
--         self:SetTitleImgPath(CardImagePath)
--     else
--         self._wtTitleImg:Collapsed()
--     end
-- end

-- 通过称号ID设置称号图片
-- function HeroCommonCardBase:SetTitleImgPathByTitleId(titleId)
--     local TitleImagePath = Module.RoleInfo:GetTileBigTexture(titleId)
--     if TitleImagePath then
--         self:SetTitleImgPath(TitleImagePath)
--     else
--         self._wtTitleImg:Collapsed()
--     end

--     -- 图片资源2合1，这个字段不用了
--     -- local TitleImageIcon = Module.RoleInfo:GetTileIcon(titleId)
--     -- if TitleImageIcon then
--     --     self:SetTitleIconPath(TitleImageIcon)
--     -- else
--     --     self._wtTitleIcon:Collapsed()
--     -- end
-- end

-- 设置干员称号背景图片
-- function HeroCommonCardBase:SetTitleImgPath(TitleImagePath)
--     loginfo("HeroCommonCardBase:SetTitleImgPath",TitleImagePath and TitleImagePath.AssetPathName)
--     if TitleImagePath then
--         self._wtTitleImg:AsyncSetImagePath(TitleImagePath)
--         self._wtTitleImg:SelfHitTestInvisible()
--     end
-- end

-- 设置干员称号图标
-- function HeroCommonCardBase:SetTitleIconPath(TitleIconPath)
--     loginfo("HeroCommonCardBase:SetTitleIconPath",TitleIconPath and TitleIconPath.AssetPathName)
--     if TitleIconPath then
--         self._wtTitleIcon:AsyncSetImagePath(TitleIconPath)
--         self._wtTitleIcon:SelfHitTestInvisible()
--     end
-- end

-- 通过 称号ID 设置称号图片和名称
-- function HeroCommonCardBase:SetTitleInfoAndNameByTitleId(titleId)
    -- loginfo("HeroCommonCardBase:SetTitleInfoAndNameByTitleId ", titleId)
    -- self:SetTitleImgPathByTitleId(titleId)
    -- self:SetTitleNameByTitleId(titleId)
-- end

-- 通过干员ID设置当前玩家装备的徽章图片
function HeroCommonCardBase:SetSelfBadgeImgPath(heroIdStr, uiId)
    heroIdStr = tostring(heroIdStr)
    uiId = navId or UIName2ID.HeroCommonCardIcon

    local curAccessoryDatas = Server.HeroServer:GetUsedAccessory(heroIdStr, EHeroAccessroy.Badge)
    local curAccessoryItemDatas = {}
    for k, v in ipairs(curAccessoryDatas) do
        v.item.unlock_time = v.unlocked_timestamp
        table.insert(curAccessoryItemDatas, v.item)
    end
    self:SetBadgeImgPathByAccessoryDatas(curAccessoryItemDatas, uiId)
end

---accessories FHeroAccessoryItem {uint64 prop_id, int64 slot}
-- 通过 accessories 结构设置名片周边数据
function HeroCommonCardBase:SetCardInfoByAccessories(accessories)
    loginfo("HeroCommonCardBase:SetCardInfoByAccessories")
    accessories = accessories or {}
    -- if next(accessories) then
    local cardId = 0
    local badgeAccessoryDatas = {}
    for key, accessory in pairs(accessories) do
        if accessory.prop_id ~= nil then
            loginfo("HeroCommonCardBase:SetCardInfoByAccessories, prop_id " .. accessory.prop_id)
        end
        if ItemHelperTool.GetSubTypeById(accessory.prop_id) == EHeroAccessroy.Card then
            cardId = accessory.prop_id
        elseif ItemHelperTool.GetSubTypeById(accessory.prop_id) == EHeroAccessroy.Badge then
            table.insert(badgeAccessoryDatas, accessory)
        end
    end
    self:SetCardImgPathByCardId(cardId)
    self:SetBadgeImgPathByAccessoryDatas(badgeAccessoryDatas)
    -- end
end

---accessories pb_CSHeroAccessory[]
-- 通过 accessories 结构设置名片周边数据
function HeroCommonCardBase:SetCardInfoByAccessories2(accessories)
    loginfo("HeroCommonCardBase:SetCardInfoByAccessories2")
    accessories = accessories or {}
    -- if next(accessories) then
    local cardId = 0
    local badgeAccessoryDatas = {}
    for key, accessory in pairs(accessories) do
        if ItemHelperTool.GetSubTypeById(accessory.item.prop_id) == EHeroAccessroy.Card then
            cardId = accessory.item.prop_id
        elseif ItemHelperTool.GetSubTypeById(accessory.item.prop_id) == EHeroAccessroy.Badge then
            table.insert(badgeAccessoryDatas, accessory.item)
        end
        accessory.item.unlock_time = accessory.unlocked_timestamp
    end
    self:SetCardImgPathByCardId(cardId)
    self:SetBadgeImgPathByAccessoryDatas(badgeAccessoryDatas)
    -- end
end

-- 根据道具的槽位排序
local function SortByBadgeSlot(a, b)
    if a.item and b.item then
        return a.item.slot < b.item.slot
    elseif a.slot and b.slot then
        return a.slot < b.slot
    else
        return false
    end
end

-- 通过 HeroAccessoryItems 结构设置徽章图片
function HeroCommonCardBase:SetBadgeImgPathByAccessoryDatas(curAccessoryDatas, uiId)
    loginfo("HeroCommonCardBase:SetBadgeImgPathByAccessoryDatas", uiId)
    if curAccessoryDatas then
        logtable(curAccessoryDatas, true)
    end
    uiId = navId or UIName2ID.HeroCommonCardIcon
    curAccessoryDatas = curAccessoryDatas or {}

    -- if next(curAccessoryDatas) then
    -- table.sort(curAccessoryDatas, SortByBadgeSlot)

    for k, widget in pairs(self._ListBadgeUI) do
        if widget and widget.Hidden then
            widget:Hidden()
        end
    end

    for k, v in ipairs(curAccessoryDatas) do
        local widget = self._ListBadgeUI[v.slot]
        if widget then
            widget:OnInitExtraData(v.prop_id, v.slot, v.unlock_time)
        end
    end
    -- end
end

-- 通过 称号ID 设置称号图片和称号名字
-- function HeroCommonCardBase:SetTitleInfoByTitleId(titleId)
    -- loginfo("HeroCommonCardBase:SetTitleInfoByTitleId ", titleId)
    -- self:SetTitleImgPathByTitleId(titleId)
    -- self:SetTitleNameByTitleId(titleId)
-- end

-- 徽章界面的徽章是可操作的
function HeroCommonCardBase:InitBadgeInBadgeUI(heroIdStr)
    heroIdStr = tostring(heroIdStr)
    -- BEGIN MODIFICATION @ VIRTUOS : 存一下，在更新空槽位的时候用
    if IsHD() then
        self._heroIdStr = heroIdStr
    end
    -- END MODIFICATION
    local curAccessoryDatas = Server.HeroServer:GetUsedAccessory(heroIdStr, EHeroAccessroy.Badge)
    -- table.sort(curAccessoryDatas, SortByBadgeSlot)
    local uiId = UIName2ID.HeroCommonCardIcon

    for k, widget in pairs(self._ListBadgeUI) do
        widget:HitTestInvisible()
    end
    -- BEGIN MODIFICATION @ VIRTUOS : 在初始化的时候记录第一个空的勋章槽
    local bFindFirstEmptyBadgeSlot = false
    -- END MODIFICATION
    for _, eBadgeSlotType in ipairs(Module.Hero.Config.EBadgeSlotType) do
        local badgeId = nil
        for k, v in ipairs(curAccessoryDatas) do
            if v.item.slot == eBadgeSlotType then
                badgeId = v.item.prop_id
            end
        end
        local widget = self._ListBadgeUI[eBadgeSlotType]
        if widget then
            widget:OnInitExtraData(badgeId, eBadgeSlotType)
            widget:EnableEquip()
            -- BEGIN MODIFICATION @ VIRTUOS : 在初始化的时候记录第一个空的勋章槽
            if IsHD() and badgeId==nil and not bFindFirstEmptyBadgeSlot then
                Module.Hero.Field:SetFirstEmptyBadgeSlot(widget)
                bFindFirstEmptyBadgeSlot = true
            end
            -- END MODIFICATION
        end
    end
    -- BEGIN MODIFICATION @ VIRTUOS : 如果都是满的，数据清空
    if IsHD() and (not bFindFirstEmptyBadgeSlot) then
        Module.Hero.Field:SetFirstEmptyBadgeSlot(nil)
    end
    -- END MODIFICATION

end

function HeroCommonCardBase:ResetCard()
    -- 名片
    if self._wtCardImage then
        self._wtCardImage:AsyncSetImagePath(Module.Hero.Config.DefaultCardPath) -- 异步显示，即使有资源也会迟一帧
    end
    -- 头衔 称号
    -- if self._wtTitleImg then
    --     self._wtTitleImg:Collapsed()
    -- end
    self._wtTitleItem:ResetCard()
    -- 徽章
    if self._ListBadgeUI then
        for k, widget in pairs(self._ListBadgeUI) do
            if widget and widget.Hidden then
                widget:Hidden()
            end
        end
    end
end

function HeroCommonCardBase:Imp_PlayCardFadeInAnimation()
    loginfo("HeroCommonCardBase:Imp_PlayCardFadeInAnimation")
    if IsHD() then
        self:PlayWidgetAnim(self.WBP_Common_CardS2_in)
    else
        self:PlayWidgetAnim(self.WBP_Common_CardS2_in_1)
    end
end

function HeroCommonCardBase:Imp_PlayCardFadeOutAnimation()
    loginfo("HeroCommonCardBase:Imp_PlayCardFadeOutAnimation")
    self:PlayWidgetAnim(self.WBP_Common_CardS2_out)
end

function HeroCommonCardBase:Imp_SetCardInfoByPlayerState(playerState)
    self:ResetCard()
    if not self._wtCardImage then
        -- C++调用时机太早，此时lua还没走ctor
        logerror("HeroCommonCardBase:Imp_SetCardInfoByPlayerState, lua no Ctor!")
        return
    end
    if isvalid(playerState) then
        self:SetPlayerName(playerState:GetPlayerName())
        -- FHeroAccessoryItem {uint64 prop_id, int64 slot}
        local AccessoryItemsArr = playerState:GetCurHeroAccessoryItems()
        loginfo(
            "HeroCommonCardBase:Imp_SetCardInfoByPlayerState, HeroID " ..
                playerState.HeroID .. " get " .. #AccessoryItemsArr .. " accessory items"
        )
        self:SetCardInfoByAccessories(AccessoryItemsArr)
        local PlayerTitleInfo = playerState:GetCurPlayerTitleInfo()
        loginfo("HeroCommonCardBase:Imp_SetCardInfoByPlayerState, titleId "..PlayerTitleInfo.PlayerTitle)
        -- self:SetTitleInfoByTitleId(PlayerTitleInfo.PlayerTitle)
        self:UpdateTitleInfo(nil, PlayerTitleInfo.PlayerTitle, PlayerTitleInfo.RankAdcode, PlayerTitleInfo.RankNo)

        -- MP身份
        self.teamIdentity = playerState:GetTeamIdentity()
        self.bIsCampmate = false
        local localPlayerState = GetInGameController():GetPlayerState()
        if localPlayerState then
            local localCamp = localPlayerState:GetCamp()
            local thisCamp = playerState:GetCamp()
            if localCamp == thisCamp then
                self.bIsCampmate = true
            end
        end
        loginfo("HeroCommonCardBase:Imp_SetCardInfoByPlayerState, teamIdentity "..self.teamIdentity)
        self:SetMPIdentityIcon(self.teamIdentity, self.bIsCampmate, false) -- 默认不显示
        self:SetInitStatus()
        --- BEGIN MODIFICATION @ VIRTUOS
        if IsConsole() then
            local platID = playerState:GetPlatformID()
            logerror("HeroCommonCardBase:Imp_SetCardInfoByPlayerState, platID", platID)
            -- if platID <= 0 then
            --     --robot
            --     platID = PlatIDType.Plat_PC
            -- end

            self:SetPlayerPlatformIDType(platID)

            if not Server.AccountServer:IsCrossPlat() then
                logerror("HeroCommonCardBase:Imp_SetCardInfoByPlayerState, not crossplat")
                self:SetPlayerPlatformIDType(nil)
            end
        end
        --- END MODIFICATION
    end
end

function HeroCommonCardBase:Imp_CppHidePlatIcon()
    self:SetPlayerPlatformIDType(nil)
end

function HeroCommonCardBase:Imp_SetCardInfoPlayerName(playerName)
    self:SetPlayerName(playerName)
end

function HeroCommonCardBase:_OnHovered()
    self:PlayWidgetAnim(self.WBP_Common_CardS2_HoverAni)
end

function HeroCommonCardBase:_OnUnHovered()
    self:StopWidgetAnim(self.WBP_Common_CardS2_HoverAni)
end

-- BEGIN MODIFICATION @ VIRTUOS : 更新第一个空的槽位，在勋章放取时调用
function HeroCommonCardBase:UpdateFirstEmptySlot()
    if not IsHD() then
		return
	end

    if not self._heroIdStr then
        return
    end
    local curAccessoryDatas = Server.HeroServer:GetUsedAccessory(self._heroIdStr, EHeroAccessroy.Badge)
    local bFindFirstEmptyBadgeSlot = false

    for _, eBadgeSlotType in ipairs(Module.Hero.Config.EBadgeSlotType) do
        local badgeId = nil
        for k, v in ipairs(curAccessoryDatas) do
            if v.item.slot == eBadgeSlotType then
                badgeId = v.item.prop_id
            end
        end
        local widget = self._ListBadgeUI[eBadgeSlotType]
        if widget then
            -- 如果找到空的了，就更新数据
            if badgeId==nil and not bFindFirstEmptyBadgeSlot then
                Module.Hero.Field:SetFirstEmptyBadgeSlot(widget)
                bFindFirstEmptyBadgeSlot = true
            end
        end
    end
    -- 如果都是满的，数据清空
    if not bFindFirstEmptyBadgeSlot then
        Module.Hero.Field:SetFirstEmptyBadgeSlot(nil)
    end
end

function HeroCommonCardBase:SetSlotReadyEquipBadge(bReady)
    for i = 0, #self._ListBadgeUI do
        self._ListBadgeUI[i].bReadyEquipBadge = bReady 
    end
end
-- END MODIFICATION

-- 不需要显示徽章信息的界面调用这个接口
function HeroCommonCardBase:RemoveBadgeTipsEvent()
    for i = 0, #self._ListBadgeUI do
        self._ListBadgeUI[i]:RemoveBadgeTipsEvent()
    end
end

-- C++需要关闭徽章信息的界面调用这个接口
function HeroCommonCardBase:Imp_CppCloseBadgeTips()
    self:CloseBadgeTips()
end
function HeroCommonCardBase:CloseBadgeTips()
    for i = 0, #self._ListBadgeUI do
        self._ListBadgeUI[i]:CloseBadgeTips()
    end
end

-- 禁止徽章交互的界面调用这个接口
function HeroCommonCardBase:DisableBadgeInteraction()
    for i = 0, #self._ListBadgeUI do
        self._ListBadgeUI[i]:HitTestInvisible()
    end
end

-- 隐藏徽章
function HeroCommonCardBase:Imp_CppHideideBadge()
    self:SetBadgeInVisible()
end
function HeroCommonCardBase:SetBadgeInVisible()
    for i = 0, #self._ListBadgeUI do
        self._ListBadgeUI[i]:Hidden()
    end
    self:CloseBadgeTips()
end

function HeroCommonCardBase:Imp_CppShowBadge()
    self:SetBadgeVisible()
end
function HeroCommonCardBase:SetBadgeVisible()
    for i = 0, #self._ListBadgeUI do
        self._ListBadgeUI[i]:SelfHitTestInvisible()
    end
end

function HeroCommonCardBase:Imp_ShowMPIdentityIcon(bShow)
    if bShow and self.teamIdentity ~= nil and self.bIsCampmate ~= nil then
        self:SetMPIdentityIcon(self.teamIdentity, self.bIsCampmate, true)
    else
        self._wtIdentityIcon:Collapsed()
    end
end

function HeroCommonCardBase:SetGameState(gameStatusType, resultType)
    if not self._wtGameStateTB or not self._wtIdxTB or not self._wtIdxImg then
        return
    end

    self._wtIdxTB:Collapsed()
    self._wtIdxImg:Collapsed()

    if gameStatusType == EGspPlayerGameStatusType.kGspPlayerGameStatusTypePlaying then
        self._wtGameStateTB:SetText(Module.Settlement.Config.Loc.EscapeStillInGameTXT)
        TextStyleBlueprintLib.Blueprint_SetTextStyle(self._wtGameStateTB, "C002")
    elseif gameStatusType == EGspPlayerGameStatusType.kGspPlayerGameStatusTypeEndGame then
        if resultType == EGspPlayerResultType.kGspPlayerResultEscaped then
            self._wtGameStateTB:SetText(Module.Settlement.Config.Loc.EscapeSuccessTXT)
            TextStyleBlueprintLib.Blueprint_SetTextStyle(self._wtGameStateTB, "C005")
        elseif resultType == EGspPlayerResultType.kGspPlayerResultKilled then
            self._wtGameStateTB:SetText(Module.Settlement.Config.Loc.EscapeFailTXT)
            TextStyleBlueprintLib.Blueprint_SetTextStyle(self._wtGameStateTB, "C004")
        else
            self._wtGameStateTB:SetText(Module.Settlement.Config.Loc.EscapeMissingTXT)
            TextStyleBlueprintLib.Blueprint_SetTextStyle(self._wtGameStateTB, "C003")
        end
    else
        self._wtGameStateTB:SetText(Module.Settlement.Config.Loc.QuitGame)
        TextStyleBlueprintLib.Blueprint_SetTextStyle(self._wtGameStateTB, "C003")
    end
end

function HeroCommonCardBase:Imp_SetSOLTeammatesIndexAndState(bIsLocal, index, stateType)
    self:SetType(2)
    if not self._wtGameStateTB or not self._wtIdxTB or not self._wtIdxImg then
        return
    end
    self._wtIdxTB:SelfHitTestInvisible()
    self._wtIdxImg:SelfHitTestInvisible() 
    self._wtIdxTB:SetText(index)
    local color = UDFMGameplayBlueprintHelper.GetCharacterIconColor(self, index - 1, bIsLocal)
    if color then
        self._wtIdxTB:SetColorAndOpacity(FSlateColor(color))
    end

    if stateType == 0 then -- 还在局内
        self._wtGameStateTB:Collapsed()
    elseif stateType == 1 then -- 撤离成功
        self._wtGameStateTB:SelfHitTestInvisible()
        self._wtGameStateTB:SetText(Module.Settlement.Config.Loc.EscapeSuccessTXT)
        TextStyleBlueprintLib.Blueprint_SetTextStyle(self._wtGameStateTB, "C005")
    elseif stateType == 2 then -- 撤离失败
        self._wtGameStateTB:SelfHitTestInvisible()
        self._wtGameStateTB:SetText(Module.Settlement.Config.Loc.EscapeFailTXT)
        TextStyleBlueprintLib.Blueprint_SetTextStyle(self._wtGameStateTB, "C004")
    elseif stateType == 3 then -- 退出游戏
        self._wtGameStateTB:SelfHitTestInvisible()
        self._wtGameStateTB:SetText(Module.Settlement.Config.Loc.QuitGame)
        TextStyleBlueprintLib.Blueprint_SetTextStyle(self._wtGameStateTB, "C003")
    end
end

function HeroCommonCardBase:SetMPQuit(bQuit)
    if not self._wtGameStateTB or not self._wtIdxTB or not self._wtIdxImg then
        return
    end
    self._wtIdxTB:Collapsed()
    self._wtIdxImg:Collapsed() 

    if bQuit then -- 退出游戏
        self._wtGameStateTB:SelfHitTestInvisible()
        self._wtGameStateTB:SetText(Module.Settlement.Config.Loc.QuitGame)
        TextStyleBlueprintLib.Blueprint_SetTextStyle(self._wtGameStateTB, "C003")
    else -- 还在局内
        self._wtGameStateTB:Collapsed()
    end
end

function HeroCommonCardBase:_SetCarryOutProfitPrice(price)
    if not self._wtCarryOutProfitPriceTB then
        return
    end

    self._wtCarryOutProfitPriceTB:SetText(price)
end

function HeroCommonCardBase:_SetKillCount(kill)
    if not self._wtKillCountTB then
        return
    end

    self._wtKillCountTB:SetText(kill)
    loginfo("HeroCommonCardBase _SetKillCount ", kill)
end

function HeroCommonCardBase:_SetRescueCount(rescueCount)
    if not self._wtRescueCountTB then
        return
    end

    self._wtRescueCountTB:SetText(rescueCount)
end

function HeroCommonCardBase:_SetResurgenceCount(resurgenceCount)
    if not self._wtResurgenceCountTB then
        return
    end
    
    self._wtResurgenceCountTB:SetText(resurgenceCount)
end

function HeroCommonCardBase:Imp_SetStatistics(Stat1, Stat2, Stat3, Stat4)
    loginfo("HeroCommonCardBase Imp_SetStatistics", Stat1, Stat2, Stat3, Stat4)
    self:_SetCarryOutProfitPrice(Stat1)
    self:_SetKillCount(Stat2)
    self:_SetRescueCount(Stat3)
    self:_SetResurgenceCount(Stat4)
end

-- 更新称号信息 目前playerName逻辑较为独立，playerName传nil，通过SetPlayerName单独设置
function HeroCommonCardBase:UpdateTitleInfo(playerName, titleId, adCode, ranking)
    loginfo("HeroCommonCardBase:UpdateTitleInfo(playerName, titleId, adCode, ranking)", playerName, titleId, adCode, ranking)
    self._wtTitleItem:UpdateInfo(playerName, titleId, adCode, ranking)
end


function HeroCommonCardBase:GetBadgeSlotByIndex(index)
    return self._ListBadgeUI[index]
end



return HeroCommonCardBase
