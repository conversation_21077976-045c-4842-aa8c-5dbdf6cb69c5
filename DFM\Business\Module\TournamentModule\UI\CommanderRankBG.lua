----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMTournament)
----- LOG FUNCTION AUTO GENERATE END -----------



local CommanderRankBG = ui("CommanderRankBG")

function CommanderRankBG:Ctor()
    self._wtRankWidget=self:Wnd("WBP_Common_BigFlatRank",UIWidgetBase)
end

function CommanderRankBG:OnOpen()
    self:PlayWidgetAnim(self.Anim_loop,0)
end

function CommanderRankBG:SetRankIconByScore(score)
    self._wtRankWidget:SetCommanderIconByScore(score)
end

function CommanderRankBG:SetRankIconNone()
    self._wtRankWidget:SetCommanderIconNone()
end

return CommanderRankBG    
