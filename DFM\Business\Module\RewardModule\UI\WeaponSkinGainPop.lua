----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMReward)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class WeaponSkinGainPop : LuaUIBaseView
local RewardBaseView = require "DFM.Business.Module.RewardModule.UI.RewardBaseView"
local WeaponSkinGainPop = ui("WeaponSkinGainPop", RewardBaseView)
local CollectionLogic = require "DFM.Business.Module.CollectionModule.Logic.CollectionLogic"
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local RewardDetail = require "DFM.Business.Module.RewardModule.UI.RewardDetail"
local ItemDetailViewEquip = require "DFM.Business.Module.ItemDetailModule.UI.ItemDetailType3.ItemDetailViewEquip"
local EGPInputModeType = import "EGPInputModeType"
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
local LiteCommonDownload =  require "DFM.Business.Module.LitePackageModule.UI.LiteCommonDownload"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPInputType = import "EGPInputType"
local UGPInputDelegates = import "GPInputDelegates"



function WeaponSkinGainPop:Ctor()
    self._wtRewardDetail = self:Wnd("wtRewardDetail", RewardDetail)
    self._wtRewardDetail:BindJumpClick(self._OnSkipBtnClick, self)
    self._wtRewardDetail:SetShowSkipPanel(false)
    self._wtRewardDetail:SetShowSkipTxt(false)
    self._wtItemDetailView = self:Wnd("wtItemDetailView", ItemDetailViewEquip)
    self._wtItemDetailView:SetWeaponDetailIsHideBtn(true)
    self._wtItemDetailView:SetShowWeaponDetailCheckBox(false)
    self._wtSkipBtn = self:Wnd("wtSkipBtn", DFCommonButtonOnly)
    self._wtSkipBtn:Event("OnClicked", self._OnSkipBtnClick, self)
    self._wtApplyBtn = self:Wnd("wtApplyBtn", CommonButton)
    self._wtApplyBtn:Event("OnClicked", self._ApplyWeaponSkin, self)
    self._wtUnlockHint = self:Wnd("wtUnlockHint", UIWidgetBase)
    if Module.ExpansionPackCoordinator:IsSupportLitePackage() then
        self._wtCommonDownload = self:Wnd("wtCommonDownload", LiteCommonDownload)
    end
    if not hasdestroy(self._wtCommonDownload) then
        self._wtCommonDownload:Collapsed()
    end
    self._closeClickCount = -1

    -- 截屏分享相关
    self._wtShareBtn = self:Wnd("wtShareBtn", DFCommonButtonOnly)
    self._wtShareBtn:Event("OnClicked", self.OnShareClick,self)
end


function WeaponSkinGainPop:OnInitExtraData(weaponskinItem, bDetailViewOnly)
    self._item = weaponskinItem
    self._bDetailViewOnly = bDetailViewOnly
end


function WeaponSkinGainPop:OnOpen()
    self:_AddListeners()
    Module.Reward.Config.Events.evtOpenWeaponSkinGainPop:Invoke(true)
end

function WeaponSkinGainPop:OnClose()
    self:RemoveJumpInputAction()
    Facade.UIManager:ClearSubUIByParent(self, self._wtItemDetailView)
    self:RemoveAllLuaEvent()
    Module.Reward.Config.Events.evtOpenWeaponSkinGainPop:Invoke(false)
    if Facade.GameFlowManager:GetCurrentSubStage() == ESubStage.HallWeaponShow then
        local currentStackUI = Facade.UIManager:GetCurrentStackUI()
        if currentStackUI and (currentStackUI.UINavID == UIName2ID.CollectionWeaponSkinDetailPage or currentStackUI.UINavID == UIName2ID.CollectionMainPanel) then
            LuaGlobalEvents.evtSceneLoaded:Invoke(ESubStage.HallWeaponShow)
        else
            Facade.HallSceneManager:FoceCallSceneCtrlFunctionBySubstage(ESubStage.HallWeaponShow, "ResetDisplayItem")
            Facade.HallSceneManager:FoceCallSceneCtrlFunctionBySubstage(ESubStage.HallWeaponShow, "ResetWeapon")
        end
    end
    if not self._bDetailViewOnly and self._bExecuteClose ~= true then
        self._bExecuteClose = true
        Module.Reward:ShowNextRewards()
    end
end

function WeaponSkinGainPop:OnShowBegin()
    self:_RefreshWidget()
    if Facade.GameFlowManager:GetCurrentSubStage() == ESubStage.HallWeaponShow then
        self:_OnRefreshModel(ESubStage.HallWeaponShow)
    end
    if not self._inputTypeChangedHandle then 
        self._inputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._OnInputTypeChanged, self))
    end
    self:AddJumpInputAction()
    self:_EnableGamepadFeature()
end


function WeaponSkinGainPop:OnHideBegin()
    if self._inputTypeChangedHandle then
        UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._inputTypeChangedHandle)
        self._inputTypeChangedHandle = nil
    end
    self:_DisableGamepadFeature()
end


function WeaponSkinGainPop:OnShow()
    self:SetCPPValue("WantedInputMode", EGPInputModeType.GameAndUI)
end


function WeaponSkinGainPop:OnHide()
end

function WeaponSkinGainPop:OnAnimFinished(anim)
    if anim == self.WBP_Collections_Unboxing_in then
        if self._closeClickCount < 1 then
            self._closeClickCount = self._closeClickCount + 1
        end
    elseif anim == self.WBP_Collections_Unboxing_out then
        if self._bDetailViewOnly then
            Facade.UIManager:CloseUI(self)
        else
            if self._bExecuteClose ~= true then
                self._bExecuteClose = true
                Module.Reward:ShowNextRewards(self._bTabPressed == true)
            end 
        end
    end
end

function WeaponSkinGainPop:_AddListeners()
    self:AddLuaEvent(Module.IrisSafeHouse.Config.evtTabBackToSafeHouseHD, self._OnTabPressed, self)
    self:AddLuaEvent(Server.GunsmithServer.Events.evtCSWAssemblyApplySkinRes, self._OnWeaponSkinApplied, self)
    if CollectionLogic.IsInMp() then
        self:AddLuaEvent(Server.InventoryServer.Events.evtMPEquipedItemsChanged, self._OnMeleeWeaponEquiped, self)
    else
        self:AddLuaEvent(Server.InventoryServer.Events.evtPostUpdateDeposData, self._OnMeleeWeaponEquiped, self)    
    end
    self:AddLuaEvent(LuaGlobalEvents.evtSceneLoaded, self._OnRefreshModel, self)
    self:AddLuaEvent(Module.Share.Config.Events.evtShareFlowFinish,self._OnShareFlowFinish, self)
    self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult,self._OnDownloadResult,self)
    self:AddLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged, self._OnPackStateChanged, self)
end

function WeaponSkinGainPop:_OnInputTypeChanged(inputType)
    if IsHD() and inputType == EGPInputType.Gamepad then
        self:RemoveJumpInputAction()
        self:_EnableGamepadFeature()
    else
        self:_DisableGamepadFeature()
        self:AddJumpInputAction()
    end
end

function WeaponSkinGainPop:_EnableGamepadFeature()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if self._wtSkipBtn then
        self._wtSkipBtn:SetDisplayInputAction("Reward_Continue_Gamepad", true, nil, true)
    end
    if self._wtApplyBtn then
        self._wtApplyBtn:SetDisplayInputAction("Common_ButtonLeft", true, nil, true)
    end
    -- 跳过按键响应
    if not self._skipHandle then
        self._skipHandle = self:AddInputActionBinding("Reward_Continue_Gamepad", EInputEvent.IE_Pressed, self._OnSkipBtnClick, self, EDisplayInputActionPriority.UI_Pop)
    end
    -- 应用按键响应
    if not self._applyHandle then
        self._applyHandle = self:AddInputActionBinding("Common_ButtonLeft", EInputEvent.IE_Pressed, self._ApplyWeaponSkin, self, EDisplayInputActionPriority.UI_Pop)
    end
    WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self, WidgetUtil.ENavConfigPriority.UI_Pop)
end


function WeaponSkinGainPop:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end
    if self._skipHandle then
        self:RemoveInputActionBinding(self._skipHandle)
    end
    if self._applyHandle then
        self:RemoveInputActionBinding(self._applyHandle)
    end
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    self._skipHandle = nil
    self._applyHandle = nil
end

function WeaponSkinGainPop:_RefreshWidget()
    if self._closeClickCount < 1 then
        self._closeClickCount = self._closeClickCount + 1
    end
    self._wtRewardDetail:SetType(2)
    self._wtRewardDetail:SetQuality(nil)
    self._wtRewardDetail:SetName(nil)
    self._wtRewardDetail:SetDesc(nil)
    self._wtSkipBtn:SetMainTitle(Module.Collection.Config.Loc.Skip)
    self._wtSkipBtn:SetIsEnabled(true)
    self._wtApplyBtn:Collapsed()
    self._wtApplyBtn:SetMainTitle(Module.Collection.Config.Loc.ApplyAppearance)
    self._wtShareBtn:Collapsed()
    if isvalid(self._item) then
        self._wtItemDetailView:UpdateItem(self._item)
        self._wtItemDetailView:SetShowkillCnt(false)
        self._wtApplyBtn:SelfHitTestInvisible()
        if not IsHD() then
            if ItemHelperTool.IsMysticalSkin(self._item.id) then
                Module.Share:FuncPointUnLock(SwitchSystemID.SubShareMetaphysicalGunSkin, self._wtShareBtn)
            else
                Module.Share:FuncPointUnLock(self._item.quality >= ItemConfig.EWeaponSkinQualityType.Purple and SwitchSystemID.SubShareAdvancedGunSkin or SwitchSystemID.SubShareSObtainLowGunSkin, self._wtShareBtn)
            end
        end
        self._weaponId = CollectionLogic.GetBaseWeaponIdFromSkinId(self._item.id)
        self._wtApplyBtn:SetIsEnabled(true)
        local skinDataRow = CollectionLogic.GetWeaponSkinDataRow(self._item.id)
        if self._item.itemSubType == ItemConfig.EWeaponItemType.Melee then
            self._wtRewardDetail:SetMainTitle(Module.Reward.Config.Loc.ReceiveMeleeSkin)
            if CollectionLogic.CheckIfMeleeWeaponEquiped(self._item.id) then
                self._wtApplyBtn:SetMainTitle(Module.Collection.Config.Loc.Equipd)
                self._wtApplyBtn:SetIsEnabled(false)
            else
                self._wtApplyBtn:SetMainTitle(Module.Collection.Config.Loc.ApplyAndEquip)
            end
            skinDataRow = Facade.TableManager:GetRowByKey("WeaponSkin/MeleeWeaponSkinDataTable", tostring(self._item.id))
        else
            self._wtRewardDetail:SetMainTitle(Module.Reward.Config.Loc.ReceiveWeaponSkin)
            if self._weaponId ~= nil then
                if CollectionLogic.CheckIfSkinAppliedOnWeapon(self._item.id, self._item.gid) then
                    self._wtApplyBtn:SetMainTitle(Module.Collection.Config.Loc.AppearanceApplied)
                    self._wtApplyBtn:SetIsEnabled(false)
                else
                    self._wtApplyBtn:SetMainTitle(Module.Collection.Config.Loc.ApplyAppearance)
                end
            else
                self._wtApplyBtn:SetMainTitle(Module.Collection.Config.Loc.NotUnlocked)
                self._wtApplyBtn:SetIsEnabled(false)
            end
        end
        self:_RefreshDownloadBtn()
    else
        self._wtRewardDetail:SetMainTitle(Module.Reward.Config.Loc.Unknown)
    end
    if skinDataRow and skinDataRow.UnlockAudio and skinDataRow.UnlockAudio ~= "" then
        self._unlockAudio = skinDataRow.UnlockAudio
        Facade.SoundManager:PlayUIAudioEvent(self._unlockAudio)
    else
        Facade.SoundManager:PlayUIAudioEvent("Music_Luckydraw_FieryOwl_Result_Normal")
    end
    if self._closeClickCount < 1 then
        self:PlayAnimation(self.WBP_Collections_Unboxing_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    end
end

function WeaponSkinGainPop:_OnDownloadResult(moduleName, bDownloaded, errorCode)
    local moduleKey = CollectionLogic.GetDownloadModuleKeyByItemId(self._item and self._item.id or nil)
    if moduleName == moduleKey then
        self:_OnRefreshModel(ESubStage.HallWeaponShow)
        self:_RefreshDownloadBtn()
    end
end

function WeaponSkinGainPop:_OnPackStateChanged(moduleName, packState)
    local isSuccess = packState == Module.ExpansionPackCoordinator.Config.PackState.Installed
    self:_OnDownloadResult(moduleName, isSuccess, 0)
end

function WeaponSkinGainPop:_RefreshDownloadBtn()
    if not hasdestroy(self._wtCommonDownload) then
        local moduleKey = CollectionLogic.GetDownloadModuleKeyByItemId(self._item and self._item.id or nil)
        local bDownloaded = Module.ExpansionPackCoordinator:IsDownloadedByModuleName(moduleKey)
        if not bDownloaded and isvalid(self._item) then
            self._wtCommonDownload:InitModuleKey(moduleKey)
            self._wtCommonDownload:Visible()
        else
            self._wtCommonDownload:Collapsed()
        end
    end
end

function WeaponSkinGainPop:_OnRefreshModel(curSubStageType)
    if not curSubStageType or curSubStageType == ESubStage.HallWeaponShow then
        Facade.HallSceneManager:ResetRootActorOffset()
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallWeaponShow, "SetIsAdapter",
        false)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallWeaponShow, "ResetDisplayItem")
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallWeaponShow, "ResetWeapon")
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallWeaponShow, "SetWeaponDisplayType", "")
        if isvalid(self._item) then
            Facade.HallSceneManager:SetDisplayBackground(self._item.id, false)
            local weaponDesc, partIndexs = WeaponHelperTool.GetWeaponDescriptionFromSkinID(self._item.id)
            if isvalid(weaponDesc) then
                if self._item.gid ~= 0 then
                    WeaponAssemblyTool.SetWeaponDescriptionSkinInfoFromPropInfo(weaponDesc, self._item:GetRawPropInfo())
                end
                -- local isMelee = self._item.itemSubType == ItemConfig.EWeaponItemType.Melee
                logerror("[v_dzhanshen] WeaponSkinGainPop:_OnRefreshModel weaponDesc:GetSkinId="..tostring(weaponDesc:GetSkinId()))
                Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallWeaponShow, "SetDisplayWeaponAutoBoundAdapter", weaponDesc, false, false)
                Facade.UIManager:CommitTransition(false)
            end
        end 
    end
end

function WeaponSkinGainPop:_OnSkipBtnClick()
    if self._closeClickCount == 1 then
        self._closeClickCount = 2
        self:HandleTransition(true)
        if self._unlockAudio then
            Facade.SoundManager:StopUIAudioEvent(self._unlockAudio)
        else
            Facade.SoundManager:StopUIAudioEvent("Music_Luckydraw_FieryOwl_Result_Normal")
        end
        self:PlayAnimation(self.WBP_Collections_Unboxing_out, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    elseif self._closeClickCount == 0 then
        self._closeClickCount = 1
        self:SkipAnimation(self.WBP_Collections_Unboxing_in)
        self._wtRewardDetail:SkipInAnimation()
    end
end


function WeaponSkinGainPop:_ApplyWeaponSkin()
    if self._item ~= nil and self._item.itemMainType == EItemType.WeaponSkin then
        if self._item.itemSubType == ItemConfig.EWeaponItemType.Melee then
            CollectionLogic.EquipMeleeWeapon(self._item.id)
        else
            CollectionLogic.ApplyWeaponSkin(self._weaponId, self._item.id, self._item.gid)   
        end
    else
        Module.CommonTips:ShowSimpleTip(Module.Collection.Config.Loc.UnlockTip)
    end
end


function WeaponSkinGainPop:_OnWeaponSkinApplied(res)
    if not self._bApplied then
        if self._item ~= nil then
            if self._item.itemSubType ~= ItemConfig.EWeaponItemType.Melee then
                if CollectionLogic.CheckIfSkinAppliedOnWeapon(self._item.id, self._item.gid, res) then
                    self._bApplied = true
                    Module.CommonTips:ShowSimpleTip(Module.Collection.Config.Loc.AppearanceApplied)
                    self._wtApplyBtn:SetMainTitle(Module.Collection.Config.Loc.AppearanceApplied)
                    self._wtApplyBtn:SetIsEnabled(false)
                else
                    Module.CommonTips:ShowSimpleTip(Module.Collection.Config.Loc.FailedToUse)
                end
            end
        end
    end
end

function WeaponSkinGainPop:_OnMeleeWeaponEquiped()
    if not self._bApplied then
        if self._item ~= nil then
            if self._item.itemSubType == ItemConfig.EWeaponItemType.Melee then
                if CollectionLogic.CheckIfMeleeWeaponEquiped(self._item.id) then
                    self._bApplied = true
                    Module.CommonTips:ShowSimpleTip(Module.Collection.Config.Loc.AppearanceApplied)
                    self._wtApplyBtn:SetMainTitle(Module.Collection.Config.Loc.Equipd)
                    self._wtApplyBtn:SetIsEnabled(false)
                else
                    Module.CommonTips:ShowSimpleTip(Module.Collection.Config.Loc.FailedToUse)
                end
            end
        end
    end
end

function WeaponSkinGainPop:AddJumpInputAction()
    if IsHD() and not WidgetUtil.IsGamepad() then
        if not self._skipHandle then
            self._skipHandle = self:AddInputActionBinding(
                "JumpOver",
                EInputEvent.IE_Pressed,
                self._OnSkipBtnClick,
                self,
                EDisplayInputActionPriority.UI_Pop
            )
        end
    end
end

function WeaponSkinGainPop:RemoveJumpInputAction()
    if self._skipHandle then
        self:RemoveInputActionBinding(self._skipHandle)
        self._skipHandle = nil
    end
end

function WeaponSkinGainPop:OnNavBack()
    self:_OnSkipBtnClick()
    return true
end

function WeaponSkinGainPop:_OnTabPressed()
    self._bTabPressed = true
    self:SkipAnimation(self.WBP_Collections_Unboxing_in)
    if self._unlockAudio then
        Facade.SoundManager:StopUIAudioEvent(self._unlockAudio)
    else
        Facade.SoundManager:StopUIAudioEvent("Music_Luckydraw_FieryOwl_Result_Normal")
    end
    self:PlayAnimation(self.WBP_Collections_Unboxing_out, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
end

--------------------------------------- 界面分享相关 ------------------------------------

function WeaponSkinGainPop:_OnShareFlowFinish()
    -- Module.CommonBar:SetTopBarVisible(true)
    if Facade.GameFlowManager:GetCurrentSubStage() == ESubStage.HallWeaponShow then
        self:_OnRefreshModel(ESubStage.HallWeaponShow)
    end
    self._wtRewardDetail:SelfHitTestInvisible()
    self._wtSkipBtn:SelfHitTestInvisible()
    self._wtApplyBtn:SelfHitTestInvisible()
    self._wtShareBtn:Visible()
    self._wtUnlockHint:SelfHitTestInvisible()
end

function WeaponSkinGainPop:PreScreenshotShare()
    -- Module.CommonBar:SetTopBarVisible(false)
    self._wtRewardDetail:Collapsed()
    self._wtSkipBtn:Collapsed()
    self._wtApplyBtn:Collapsed()
    self._wtShareBtn:Collapsed()
    self._wtUnlockHint:Collapsed()
end

function WeaponSkinGainPop:AfterScreenshotShare()
end

function WeaponSkinGainPop:OnShareClick()
    local PreScreenshotShare = CreateCallBack(function(self)
        self:PreScreenshotShare()
    end,self)

    local AfterScreenshotShare = CreateCallBack(function(self)
        self:AfterScreenshotShare()
    end,self)

    Module.Share:ReqShareWeapon(self._item.id, self._item, "WeaponSkinGainPop", PreScreenshotShare, AfterScreenshotShare, true)
end
--end
-----------------------------------------------------------------------

return WeaponSkinGainPop
