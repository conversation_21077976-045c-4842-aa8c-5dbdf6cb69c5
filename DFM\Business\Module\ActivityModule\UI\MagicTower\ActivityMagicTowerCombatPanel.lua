----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class ActivityMagicTowerCombatPanel : LuaUIBaseView
local ActivityMagicTowerCombatPanel = ui("ActivityMagicTowerCombatPanel")
local ActivityMagicTowerNumericalPanel = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerNumericalPanel"
local ActivityConfig = Module.Activity.Config
local ActivityMagicTowerCombatLogic = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerCombatLogic"
local ConfigManager = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerConfigManager"
local Logic = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerLogic"

local MAX_ROUNDS = 300 -- 手动战斗的最大回合数

local PANEL_OPEN_WAIT_TIME = 0.9 -- 面板打开等待时间
local ENEMY_DEATH_AUDIO = 0.2 -- 敌人死亡音效延迟播放时间
local PANEL_CLOSE_WAIT_TIME = 1.5 -- 面板关闭等待时间

local UI_DEFAULT_ANIM_TIME = 1.6 -- 大回合的总时长，由于敌人普遍有较长的攻击前摇，双方的攻击动画需要一起播放
local CHARACTER_DEFAULT_ANIM_TIME = 1.6 -- 角色默认动画时长，最好和大回合的总时长一致

local ENEMY_UI_START_TIME = 0.1 -- 主角攻击动画播放完后，提前刷新敌人UI的时间（不影响大回合的总时长）
local PLAYER_UI_START_TIME = 0.9 -- 敌人攻击动画播放完后，提前刷新主角UI的时间（不影响大回合的总时长）

local FIRST_BOSS_ID = 3010003 -- BOSS赛伊德
local SECOND_BOSS_ID = 3010012 -- BOSS雷斯
local FINAL_BOSS_ID = 3010034 -- BOSS德穆兰

local function testLog(...)
    loginfo("[Magic_Tower_Combat_Panel] ", ...)
end

local function testWarningLog(...)
    logwarning("[Magic_Tower_Combat_Panel] ", ...)
end

local function testErrorLog(...)
    logerror("[Magic_Tower_Combat_Panel] ", ...)
end

local ENEMY_MUSIC_CONVERT = {
    ["3010012"] = 7,
    ["3010003"] = 8,
    ["3010034"] = 9,
    ["3010031"] = 9,
}

local ENEMY_NO_GUN_LIST = {
    [1] = 3010023,
    [2] = 3010028,
    [3] = 3010011,
    [4] = 3010022,
    [5] = 3010019,
    [6] = 3010018,
}

function ActivityMagicTowerCombatPanel:Ctor()
    -- 根面板
    self._wtCanvasPanel = self:Wnd("CanvasPanel_0", UIWidgetBase)
    -- 战斗背景
    self._wtFightBgImg = self:Wnd("DFImage_53", UIImage)
    -- 主角信息框
    self._wtHeroNumericalPanel = self:Wnd("WBP_MorgenGame_NumericalPanel_C_22", ActivityMagicTowerNumericalPanel)
    -- 敌人信息框
    self._wtEnemyNumericalPanel = self:Wnd("WBP_MorgenGame_NumericalPanel_C_24", ActivityMagicTowerNumericalPanel)
    -- 主角Spine
    self._wtHeroSpine = self:Wnd("DFCommonSpine", UIWidgetBase)
    -- 敌人Spine
    self._wtEnemySpine = self:Wnd("DFCommonSpine_1", UIWidgetBase)
    -- 主角伤害飘窗文本
    self._wtHeroDamageTxt = self:Wnd("DFTextBlock", UITextBlock)
    -- 敌人伤害飘窗文本
    self._wtEnemyDamageTxt = self:Wnd("DFTextBlock_26", UITextBlock)
end

-----------------------------------------------------生命周期-----------------------------------------------------
function ActivityMagicTowerCombatPanel:OnInitExtraData(activityID, commbatAsset, commbatData, FinishCallBack, isNoCritMode, isVirtualMutual, virtualEnemyHealth)
    self._activityID = activityID or 0
    self._commonbatAsset = commbatAsset or {}
    self._commonbatData = commbatData or {}
    self.FinishCallBack = FinishCallBack or nil
    self._isNoCritMode = isNoCritMode or false
    self._isVirtualMutual = isVirtualMutual or false
    self._virtualEnemyHealth = virtualEnemyHealth or nil

    self:_InitData()
end

function ActivityMagicTowerCombatPanel:OnOpen()
    self._wtCanvasPanel:Collapsed()
end

function ActivityMagicTowerCombatPanel:OnShowBegin()
    self:BindBackAction()
    
    self:_InitUIAsset()
    self:_RefreshUI()
    
    local fOnCombatLoadingFinished = CreateCallBack(function()
        ConfigManager.PlayAudio(19)
        self:_OnStartCombat()
    end, self)
    Facade.UIManager:AsyncShowUI(UIName2ID.ActivityMagicTowerCombatLoading, nil, nil, fOnCombatLoadingFinished)
    
    self._timerHandle = Timer.DelayCall(PANEL_OPEN_WAIT_TIME, function()
        self._wtCanvasPanel:SelfHitTestInvisible()
    end)
end

function ActivityMagicTowerCombatPanel:OnHideBegin()
    self:ClearTimerHandle()
    self:RemoveAllActions()
    self:RemoveAllLuaEvent()
end

function ActivityMagicTowerCombatPanel:OnNavBack()
    if self._isVirtualMutual then return end

    if not VersionUtil.IsShipping() then
        local function fOnConfirmCallback()
            if self.FinishCallBack then
                self:FinishCallBack("win", self._combatState.playerHP)
            end
            
            Module.CommonTips:ShowSimpleTip("非Shipping包功能：直接结束战斗！")
            testWarningLog("[测试功能] 直接跳过战斗!")
            Facade.UIManager:CloseUI(self)
        end
    
        local confirmTxt = "非正式版本功能：是否要离开战斗？（保留玩家当前血量，直接消灭敌人）"
        self:ShowConfirmWindow(confirmTxt, fOnConfirmCallback, nil, ActivityConfig.Loc.Cancel, ActivityConfig.Loc.Confirm)
    end
end

function ActivityMagicTowerCombatPanel:ClearTimerHandle()
    testLog("清除定时器")

    if self._timerHandle then
        Timer.CancelDelay(self._timerHandle)
        self._timerHandle = nil
    end
    if self._enemyUITimerHandle then
        Timer.CancelDelay(self._enemyUITimerHandle)
        self._enemyUITimerHandle = nil
    end
    if self._playerUITimerHandle then
        Timer.CancelDelay(self._playerUITimerHandle)
        self._playerUITimerHandle = nil
    end
    if self._attackRestoreTimerHandle then
        Timer.CancelDelay(self._attackRestoreTimerHandle)
        self._attackRestoreTimerHandle = nil
    end
    if self._enemayAttackTimerHandle then
        Timer.CancelDelay(self._enemayAttackTimerHandle)
        self._enemayAttackTimerHandle = nil
    end
end

function ActivityMagicTowerCombatPanel:BindBackAction()
    if self._backActionHandle then
        self:RemoveInputActionBinding(self._backActionHandle)
        self._backActionHandle = nil
    end

    self._backActionHandle = self:AddInputActionBinding(
        "Back",
        EInputEvent.IE_Pressed,
        self.OnNavBack,
        self,
        EDisplayInputActionPriority.UI_Pop
    )
end

function ActivityMagicTowerCombatPanel:RemoveAllActions()
    if self._backActionHandle then
        self:RemoveInputActionBinding(self._backActionHandle)
        self._backActionHandle = nil
    end
end

function ActivityMagicTowerCombatPanel:ShowConfirmWindow(text, confirmHandle, cancelHandle, cancelText, confirmText, confirmSound, cancelSound, stateInGuide, checkBoxText, fFinishCallback, bIsRecharge, detailText)
	return Facade.UIManager:AsyncShowUI(
		UIName2ID.ActivityMagicTowerConfirmWindow,
		fFinishCallback,
		nil,
		text,
		nil,
		confirmHandle,
		cancelHandle,
		cancelText,
		confirmText,
		confirmSound,
        cancelSound,
		stateInGuide,
		checkBoxText,
		nil,
		nil,
		bIsRecharge,
		nil,
		detailText
	)
end

-----------------------------------------------------数据区-----------------------------------------------------
function ActivityMagicTowerCombatPanel:_InitData()
    if not self._commonbatData or not next(self._commonbatData) then
        return
    end

    self._combatState = {
        currentRound = 1,
        playerHP = self._commonbatData.playerData.health or 0,
        enemyHP = self._commonbatData.enemyConfig.health or 0,
        playerStatus = {
            attack = self._commonbatData.playerData.attack or 0,
            defense = self._commonbatData.playerData.defense or 0,
            hitProb = self._commonbatData.playerData.hitProb or 0
        },
        enemyStatus = {
            attack = self._commonbatData.enemyConfig.attack or 0,
            defense = self._commonbatData.enemyConfig.defense or 0,
            hitProb = self._commonbatData.enemyConfig.hitProb or 0
        },
        enemyID = self._commonbatData.enemyConfig.enemyID or 0,
        battleLog = {},
        isPlayerTurn = true
    }

    if self._virtualEnemyHealth then
        self._combatState.enemyHP = self._virtualEnemyHealth
    end

    self:_CheckSpecialEnemyID(self._combatState.enemyID)
end

function ActivityMagicTowerCombatPanel:_CheckSpecialEnemyID(enemyID)
    if not enemyID or enemyID == 0 then return end

    if self._isVirtualMutual then
        local bossID_str = tostring(SECOND_BOSS_ID)
        ConfigManager.PlayMusic(ENEMY_MUSIC_CONVERT[bossID_str])
    else
        local enemyID_str = tostring(enemyID)
        if ENEMY_MUSIC_CONVERT[enemyID_str] then
            ConfigManager.PlayMusic(ENEMY_MUSIC_CONVERT[enemyID_str])
        end
    end

    self._isNoGunEnemy = false
    for _, value in ipairs(ENEMY_NO_GUN_LIST) do
        if value == enemyID then
            self._isNoGunEnemy = true
            break
        end
    end
end

function ActivityMagicTowerCombatPanel:_CheckSpecialDefeatEnemyID(enemyID)
    if self._isVirtualMutual then return end
    
    if not enemyID or enemyID == 0 then return end

    if enemyID == FINAL_BOSS_ID then
        testWarningLog("----------<< 击败BOSS德穆兰 >>----------")
        ActivityConfig.evtMagicTowerLevelGameOver:Invoke(true)
    elseif enemyID == FIRST_BOSS_ID then
        testWarningLog("----------<< 击败BOSS赛伊德 >>----------")
    elseif enemyID == SECOND_BOSS_ID then
        testWarningLog("----------<< 击败BOSS雷斯 >>----------")
    end
end

-----------------------------------------------------战斗逻辑-----------------------------------------------------
function ActivityMagicTowerCombatPanel:_OnStartCombat(delayTime)
    if not self._commonbatData or not next(self._commonbatData) then
        return
    end

    self:_ProcessCombatRound()
end

function ActivityMagicTowerCombatPanel:_ProcessCombatRound()
    testLog(string.format("执行战斗回合处理，当前回合%s，玩家HP%s，敌人HP%s", self._combatState.currentRound, self._combatState.playerHP, self._combatState.enemyHP))

    if self._combatState.playerHP <= 0 or self._combatState.enemyHP <= 0 or self._combatState.currentRound > MAX_ROUNDS then
        self:_EndCombat()
        return
    end

    if self._combatState.isPlayerTurn then
        self:_ProcessPlayerAttack()
    else
        self:_ProcessEnemyAttack()
    end
end

function ActivityMagicTowerCombatPanel:_ProcessPlayerAttack()
    local roundResult = ActivityMagicTowerCombatLogic.CalculateRoundDamage(
        "player",
        self._combatState.playerStatus,
        self._combatState.enemyStatus,
        self._combatState.playerHP,
        self._combatState.enemyHP,
        self._isNoCritMode
    )
    
    self._combatState.enemyHP = roundResult.targetHP
    roundResult.round = self._combatState.currentRound
    table.insert(self._combatState.battleLog, roundResult)

    local enemyUIStartTime = self._isVirtualMutual and PLAYER_UI_START_TIME or ENEMY_UI_START_TIME
    self._enemyUITimerHandle = Timer.DelayCall(enemyUIStartTime, function()
        self:_RefreshEnemyUI(roundResult)
    end)
    
    self:_PlayAttackAnimation(true, roundResult.isCrit, function()
        self._combatState.isPlayerTurn = not self._combatState.isPlayerTurn
        self:_ProcessCombatRound()
    end)
end

function ActivityMagicTowerCombatPanel:_ProcessEnemyAttack()
    local roundResult = ActivityMagicTowerCombatLogic.CalculateRoundDamage(
        "enemy",
        self._combatState.enemyStatus,
        self._combatState.playerStatus,
        self._combatState.enemyHP,
        self._combatState.playerHP,
        self._isNoCritMode
    )
    
    self._combatState.playerHP = roundResult.targetHP
    roundResult.round = self._combatState.currentRound
    table.insert(self._combatState.battleLog, roundResult)

    self._playerUITimerHandle = Timer.DelayCall(PLAYER_UI_START_TIME, function()
        self:_RefreshPlayerUI(roundResult)
    end)

    local uiDefaultAnimTime = self._isVirtualMutual and PLAYER_UI_START_TIME or UI_DEFAULT_ANIM_TIME
    self:_PlayAttackAnimation(false, roundResult.isCrit, function()
        self._enemayAttackTimerHandle = Timer.DelayCall(UI_DEFAULT_ANIM_TIME, function()
            self._combatState.isPlayerTurn = not self._combatState.isPlayerTurn
            self._combatState.currentRound = self._combatState.currentRound + 1
            self:_ProcessCombatRound()
        end)
    end)
end

-- 播放动画接口
---@param isPlayerAttack boolean 是否为玩家攻击
---@param isCrit boolean 是否暴击
---@param onComplete function 动画播放完成回调
function ActivityMagicTowerCombatPanel:_PlayAttackAnimation(isPlayerAttack, isCrit, onComplete)
    if not self._wtHeroSpine or not self._wtHeroSpine.SetData or not self._wtHeroSpine.PlaySpineAnimation then
        testErrorLog("_wtHeroSpine or _wtHeroSpine.SetData or _wtHeroSpine.PlaySpineAnimation is nil")
        return
    end

    if not self._wtEnemySpine or not self._wtEnemySpine.SetData or not self._wtEnemySpine.PlaySpineAnimation then
        testErrorLog("_wtEnemySpine or _wtEnemySpine.SetData or _wtEnemySpine.PlaySpineAnimation is nil")
        return
    end

    local attackingSpine, defendingSpine, attackAnimName, idleAnimName

    if isPlayerAttack then
        attackingSpine = self._wtHeroSpine
        defendingSpine = self._wtEnemySpine
        if self._isVirtualMutual then
            attackAnimName = "fight"
            idleAnimName = "idle"
        else
            attackAnimName = "fight_right"
            idleAnimName = "idle_right"
        end
    else
        attackingSpine = self._wtEnemySpine
        defendingSpine = self._wtHeroSpine
        attackAnimName = "fight"
        idleAnimName = "idle"
    end
    
    -- 先获取原始动画时长
    local originalDuration = attackingSpine:GetSpineAnimationDurationTime(attackAnimName)
    if originalDuration <= 0 then
        originalDuration = CHARACTER_DEFAULT_ANIM_TIME
    end
    
    -- 计算需要的时间缩放比例
    local timeScale = originalDuration / CHARACTER_DEFAULT_ANIM_TIME
    
    -- 设置攻击动画的时间缩放
    attackingSpine:SetTimeScale(timeScale)
    attackingSpine:PlaySpineAnimation(0, attackAnimName, false)
    
    -- -- 防御方保持正常速度
    -- defendingSpine:SetTimeScale(1.0)
    -- defendingSpine:PlaySpineAnimation(0, idleAnimName, true)

    -- self._attackRestoreTimerHandle = Timer.DelayCall(CHARACTER_DEFAULT_ANIM_TIME, function()
    --     -- 攻击方恢复正常速度
    --     if attackingSpine and attackingSpine.SetTimeScale then
    --         attackingSpine:SetTimeScale(1.0)
    --         attackingSpine:PlaySpineAnimation(0, idleAnimName, true)
    --     end
    -- end)

    if onComplete then
        onComplete()
    end

    testLog(string.format("播放%s攻击动画，时长%s秒，快进处理%s倍", isPlayerAttack and "玩家" or "敌人", tostring(originalDuration), tostring(timeScale)))
end

function ActivityMagicTowerCombatPanel:_InitUIAsset()
    if not self._commonbatAsset or not next(self._commonbatAsset) then
        self._wtFightBgImg:Collapsed()
        return
    end

    if not self._wtHeroSpine or not self._wtHeroSpine.SetData or not self._wtHeroSpine.PlaySpineAnimation then
        testErrorLog("_wtHeroSpine or _wtHeroSpine.SetData or _wtHeroSpine.PlaySpineAnimation is nil")
        return
    end

    if not self._wtEnemySpine or not self._wtEnemySpine.SetData or not self._wtEnemySpine.PlaySpineAnimation then
        testErrorLog("_wtEnemySpine or _wtEnemySpine.SetData or _wtEnemySpine.PlaySpineAnimation is nil")
        return
    end

    self._wtFightBgImg:SelfHitTestInvisible()

    local fightImgPath = ConfigManager.GetAssetImgPath(self._commonbatAsset.fightAsset)
    local heroImgPath = ConfigManager.GetAssetImgPath(self._commonbatAsset.heroAvatarAsset)
    local enemyImgPath = ConfigManager.GetAssetImgPath(self._commonbatAsset.enemyAvatarAsset)
    
    self._wtFightBgImg:AsyncSetImagePath(fightImgPath)
    self._wtHeroNumericalPanel:SetHeadIconImg(heroImgPath)
    self._wtEnemyNumericalPanel:SetHeadIconImg(enemyImgPath)

    -- 初始化角色Spine资源和站立动画
    local heroAtalas, heroSkeleton = ConfigManager.GetSpineAtalasAndSkeleton(self._commonbatAsset.heroSpine)

    local heroAtalasSoftPath = FSoftObjectPath()
    heroAtalasSoftPath:SetPath(heroAtalas)
    local heroSkeletonSoftPath= FSoftObjectPath()
    heroSkeletonSoftPath:SetPath(heroSkeleton)
    if self._isVirtualMutual then
        self._wtHeroSpine:SetLoopAnimationName("idle") -- Async
    else
        self._wtHeroSpine:SetLoopAnimationName("idle_right") -- Async
    end
    -- self. wtEnemySpine:SetAsyncLoadSwitcher(false) -- Sync
    self._wtHeroSpine:SetData(heroAtalasSoftPath, heroSkeletonSoftPath)

    local enemyAtalas, enemySkeleton = ConfigManager.GetSpineAtalasAndSkeleton(self._commonbatAsset.enemySpine)

    local enemyAtalasSoftPath = FSoftObjectPath()
    enemyAtalasSoftPath:SetPath(enemyAtalas)
    local enemySkeletonSoftPath = FSoftObjectPath()
    enemySkeletonSoftPath:SetPath(enemySkeleton)
    self._wtEnemySpine:SetLoopAnimationName("idle") -- Async
    -- self. wtEnemySpine:SetAsyncLoadSwitcher(false) -- Sync
    self._wtEnemySpine:SetData(enemyAtalasSoftPath, enemySkeletonSoftPath)

	-- self._wtHeroSpine:PlaySpineAnimation(0, "idle_right", true) -- Sync
    -- self._wtEnemySpine:PlaySpineAnimation(0, "idle", true) -- Sync
end

function ActivityMagicTowerCombatPanel:_RefreshUI()
    if not self._commonbatData or not next(self._commonbatData) then
        return
    end

    local lastAction = self._combatState.battleLog[#self._combatState.battleLog]

    self:_RefreshEnemyUI(lastAction)
    self:_RefreshPlayerUI(lastAction)
    self:_RecordCombatLog(lastAction)
end

function ActivityMagicTowerCombatPanel:_RecordCombatLog(lastAction)
    if self._isVirtualMutual then return end
    
    if lastAction then
        local enemyName = self._commonbatData.enemyConfig.name or ActivityConfig.Loc.MagicTowerText[9]
        local logText = string.format(ActivityConfig.Loc.MagicTowerText[7],
        lastAction.isCrit and ActivityConfig.Loc.MagicTowerText[10] or "",
        lastAction.attacker == "player" and ActivityConfig.Loc.MagicTowerText[8] or enemyName,
        lastAction.attacker == "player" and enemyName or ActivityConfig.Loc.MagicTowerText[8],
        Logic.roundDownToInteger(lastAction.damage))
        Module.CommonTips:ShowSimpleTip(tostring(logText))
    end
end

function ActivityMagicTowerCombatPanel:_RefreshPlayerUI(lastAction)
    testLog("刷新玩家UI")

    self._wtHeroNumericalPanel:UpdateAllValues({
        hp = self._combatState.playerHP,
        atk = self._combatState.playerStatus.attack,
        def = self._combatState.playerStatus.defense,
        crit = self._combatState.playerStatus.hitProb
    }, nil, true)

    if not lastAction then return end

    local soundID = self._isNoGunEnemy and (lastAction.isCrit and 14 or 12) or (lastAction.isCrit and 15 or 13)
    ConfigManager.PlayAudio(soundID)
    
    self._wtHeroDamageTxt:SetText(Logic.roundDownToInteger(lastAction.damage))
    self:PlayAnimation(self.WBP_Activity_LeftDamageFloating, 0, 1, EUMGSequencePlayMode.Forward, 1, false)

    self:_RecordCombatLog(lastAction)
end

function ActivityMagicTowerCombatPanel:_RefreshEnemyUI(lastAction)
    testLog("刷新敌人UI")

    self._wtEnemyNumericalPanel:UpdateAllValues({
        hp = self._combatState.enemyHP,
        atk = self._combatState.enemyStatus.attack,
        def = self._combatState.enemyStatus.defense,
        crit = self._combatState.enemyStatus.hitProb
    }, nil, true)

    if not lastAction then return end

    local soundID = self._isVirtualMutual and (self._isNoGunEnemy and (lastAction.isCrit and 14 or 12) or (lastAction.isCrit and 15 or 13)) or (lastAction.isCrit and 11 or 10)
    ConfigManager.PlayAudio(soundID)

    self._wtEnemyDamageTxt:SetText(Logic.roundDownToInteger(lastAction.damage))
    self:PlayAnimation(self.WBP_Activity_RigthDamageFloating, 0, 1, EUMGSequencePlayMode.Forward, 1, false)

    self:_RecordCombatLog(lastAction)
end

function ActivityMagicTowerCombatPanel:_EndCombat()
    local result = self._combatState.enemyHP <= 0 and "win" or "lose"
    testLog(string.format("战斗结束，结果：%s", result))

    local resultText = ""
    local soundID
    if result == "win" then
        soundID = 8
        resultText = ActivityConfig.Loc.MagicTowerText[13]
    else
        resultText = ActivityConfig.Loc.MagicTowerText[14]
    end
    
    if self._isVirtualMutual then
        ActivityConfig.evtMagicTowerResidueBossHealth:Invoke(self._combatState.playerHP)
    else
        local showTipTxt = tostring(ActivityConfig.Loc.MagicTowerText[12] .. " - " .. resultText)
        Module.CommonTips:ShowSimpleTip(showTipTxt)
    end

    self:_CheckSpecialDefeatEnemyID(self._combatState.enemyID)

    Timer.DelayCall(ENEMY_DEATH_AUDIO, function()
        if soundID then
            ConfigManager.PlayAudio(soundID)
        end
    end)
    
    Timer.DelayCall(PANEL_CLOSE_WAIT_TIME, function()
        if self.FinishCallBack then
            self:FinishCallBack(result, self._combatState.playerHP)
        end
        
        testWarningLog("面板关闭，开始清理自身UI")
        Facade.UIManager:CloseUI(self)
    end)
end

return ActivityMagicTowerCombatPanel