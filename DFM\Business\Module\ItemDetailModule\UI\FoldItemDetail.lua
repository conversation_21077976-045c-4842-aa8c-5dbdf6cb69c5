----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMItemDetail)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class FoldItemDetail : LuaUIBaseView
local FoldItemDetail = ui("FoldItemDetail")
local ShopHelperTool = require "DFM.StandaloneLua.BusinessTool.ShopHelperTool"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ItemDetailConfig = require("DFM.Business.Module.ItemDetailModule.ItemDetailConfig")
local itemDetailField = Module.ItemDetail.Field
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local EDragPivot = import("EDragPivot")
local UGPInputHelper = import "GPInputHelper"
local FAnchors = import "Anchors"
local ItemDetailLogic = require "DFM.Business.Module.ItemDetailModule.ItemDetailLogic"
local ItemDetailDFCommonButtonV3S1 = "DFM.Business.Module.ItemDetailModule.UI.ItemDetailDFCommonButtonV3S1"
local ItemOperaTool      = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local InventoryLogic = require "DFM.Business.Module.InventoryModule.InventoryLogic"
local LootingLogic = require "DFM.Business.Module.LootingModule.LootingLogic"
local UDFMGameplayGlobalDelegates = import "DFMGameplayGlobalDelegates"

function FoldItemDetail:Ctor()
    self._wtTitleContent = self:Wnd("WBP_ItemDetail_TitleContent", UIWidgetBase)
    self._wtTitleBgImage = self._wtTitleContent:Wnd("wTitleBgImage", UIImage)
    self._wtNameLabel = self._wtTitleContent:Wnd("wNameLabel", UITextBlock)
    self._wtItemId = self._wtTitleContent:Wnd("wItemId", UITextBlock)
    self._wtCurrencyRichText = self._wtTitleContent:Wnd("wCurrencyText", UITextBlock)
    self._wtCloseBtn = self._wtTitleContent:Wnd("wCloseBtn", UIButton)
    -- self._wtUpDiffImage = self._wtTitleContent:Wnd("wDiffImage", UIImage)
    self._wQualityIcon = self._wtTitleContent:Wnd("wtQualityIcon", UIImage)
    -- self._wQualityIcon:SetVisibility(ESlateVisibility.Collapsed)
    self._wtCloseBtn:Event("OnClicked", self._OnCloseBtnClick, self)

	-- 常用按钮
    self._wtBtnCanvas = self._wtTitleContent:Wnd("DFCanvasPanel_0", UIWidgetBase)
    self._wtBtn1 = self._wtTitleContent:Wnd("WBP_ItemDetailDFCommonButtonV3S1", ItemDetailDFCommonButtonV3S1)
	self._wtBtn1:Collapsed()
    self._wtBtn2 = self._wtTitleContent:Wnd("WBP_ItemDetailDFCommonButtonV3S1_1", ItemDetailDFCommonButtonV3S1)
	self._wtBtn2:Collapsed()
	self._btnList = {
		self._wtBtn1,
		self._wtBtn2,
	}

    -- 折叠按钮
    self._wtFoldBtn = self._wtTitleContent:Wnd("DFCheckBox_107", UICheckBox)
	self._wtFoldBtn:SetCallback(self._OnFoldCliked, self)
	self._wtFoldBtn:Visible()

	self._beginTouchPos = nil
	self._bMoveStateChecked = false

	-- 出售模式
	self._tradeMode = ETradeMode.Sell

    -- self:SetCppValue("bHandleClick", true)
    self._bInMp = false

    -- local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
    -- if armedForceMode == EArmedForceMode.MP then
    -- 	self._bInMp = true
    -- 	self._wtCurrencyRichText:SetVisibility(ESlateVisibility.Collapsed)
    -- end
	self._bCustomVisibilityControllLogic = false
    self._dragWidget = nil
    self._dragPanelParent = nil
    self._bCanDrag = false
    self._bCollectionItem = false
	self._bValid = true
	self._bSwallowBgTouch = false
	self.canClose = true
	self.bIsDelayClose = false
	self:SetCppValue("bSwallowClick", false)
	self:SetCloseBtnVisible(true)
end

------------------------------------ Override function ------------------------------------

function FoldItemDetail:_Reset()
	self._tradeMode = ETradeMode.Sell
	self._wtFoldBtn:SetIsChecked(true, false)
	for index, widget in ipairs(self._btnList) do
		widget:Collapsed()
	end
end

function FoldItemDetail:_InitBtn()
	if ItemOperaTool.CheckRunWarehouseLogic() then
		self._btnTypeList = InventoryLogic.CreateCommonFoldDetailBtnsInWH(self._itemStruct)
	else
		self._btnTypeList = LootingLogic.CreateFoldDetailBtns(self._itemStruct)
	end
	for index, widget in ipairs(self._btnList) do
		widget:Collapsed()
	end
end

function FoldItemDetail:OnInitExtraData(
    itemStruct,
    parentWidget,
    bFastEquippable,
    bShowBtn,
    btnTypeList,
    btnNavId,
    transBtnList,
    layerLevel,
    bCanDrag,
    closeCallback,
    bNeedHideUnequipBulletBtn,
    bOpenFoldDetail,
	bOnCommonPop
	)

    self._itemStruct = itemStruct
	self._parentWidget = parentWidget
	self._layerLevel = layerLevel
	self:SetDragState(bCanDrag)
	self._closeCallback = closeCallback
	if btnTypeList == nil then
		self:_InitBtn()
	else
		self._btnTypeList = btnTypeList
	end
	local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
	self._bInMp = armedForceMode == EArmedForceMode.MP
	if Facade.GameFlowManager:GetCurrentGameFlow() ~= EGameFlowStageType.Game then
		self:OverrideUISettingsByKv("IsModal", Module.ItemDetail:TryGetLastPopUIIsModalFilterNavIds({UIName2ID.ItemDetailPanel, UIName2ID.FoldItemDetail}))
	end
end

function FoldItemDetail:UpdatePosition()
	local viewportGeometry = UE.WidgetLayoutLibrary.GetViewportWidgetGeometry(self)
	local viewportLocalSize = viewportGeometry:GetLocalSize()

	local widgetGeometry = self._wtTitleContent:GetCachedGeometry()
	local widgetLocalSize = widgetGeometry:GetLocalSize()

	local safePadding, safePaddingScale, spillOverPadding = UWidgetBlueprintLibrary.GetSafeZonePadding(GetWorld(), FVector4(), FVector2D(0, 0), FVector4())

	local position = FVector2D()
	position.X = viewportLocalSize.X / 2 - widgetLocalSize.X / 2 - safePadding.X - safePadding.Z
	position.Y = viewportLocalSize.Y - 60 - widgetLocalSize.Y

	self._wtTitleContent.slot:SetPosition(position)
end

function FoldItemDetail:_OnItemMove(itemMoveCmd)
    if self._itemStruct == nil then
        return
    end
    local item = itemMoveCmd.item
    if item and item.gid == self._itemStruct.gid then
		self:_InitBtn()
		self:SetItemBtn()
		if itemMoveCmd.Reason ~= PropChangeType.Modify then
			self:_ClosePanel()
		end
    end
end

function FoldItemDetail:_OnBulletUnEquip(itemId, gunGid, resultCode)
	self:_InitBtn()
	self:SetItemBtn()
end

function FoldItemDetail:SetItemBtn()
	local function fLoadFinLogic()
		if self._itemStruct then
			for index, v in ipairs(self._btnTypeList or {}) do
				local widget = self._btnList[index]
				if widget then
					widget:ReInit(self._itemStruct, v, self, self._parentWidget)
					widget:InitCommonBtns(self._itemStruct, v, self._parentWidget)
					widget:Visible()
				end
			end
		end
	end
	local function fLoadFinCallback(mapPath2ResIns)
		fLoadFinLogic()
	end
	if Facade.UIManager:CheckUIHasBeenLoaded(UIName2ID.ItemDetailDFCommonButtonV3S1) then
		fLoadFinLogic()
	else
		Facade.UIManager:AsyncLoadUIResOnly(UIName2ID.ItemDetailDFCommonButtonV3S1, fLoadFinCallback)
	end
end

function FoldItemDetail:OnOpen()
	self._wtTitleContent:Bp_SetFold(1)
end

function FoldItemDetail:OnShow()
	self:_AddEventListener()

	self:_Reset()
	self:SetItemTitle()
	self:SetItemPrice()
	self:SetItemBtn()
	Timer.DelayCall(0, self.UpdatePosition, self)

	if Module.ItemDetail.Field:GetOpenFoldItemDetailByBtnFlag() then
		self._wtTitleContent:PlayAnimation(self._wtTitleContent.WBP_ItemDetail_TitleContent_in_2, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
	else
		self._wtTitleContent:PlayAnimation(self._wtTitleContent.WBP_ItemDetail_TitleContent_in_3, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
	end
	if self._layerLevel == nil or self._layerLevel == Module.ItemDetail.Config.ETipsLayerLevel.Main then
		Module.CommonWidget:SimulateDragOrCancel(self._itemStruct, true)
		Module.ItemDetail:BindUserScrolledEvent()
	end
end

function FoldItemDetail:OnHide()
	self:_RemoveEventListener()
	Module.ItemDetail.Field:SetOpenFoldItemDetailByBtnFlag(false)
	if self._layerLevel == nil or self._layerLevel == Module.ItemDetail.Config.ETipsLayerLevel.Main then
		Module.CommonWidget:SimulateDragOrCancel(self._itemStruct, false)
		Module.ItemDetail:UnBindUserScrolledEvent()
	end
end

function FoldItemDetail:OnClose()
	-- 增加debug用的log
	if true then
		loginfo('FoldItemDetail:OnClose',self, debug.traceback())
	end

	if self._bValid then
		self:ProcessClose()
	end
end

function FoldItemDetail:GetDetailItem()
	return self._itemStruct
end

function FoldItemDetail:ProcessClose()
	if self._customCloseHandle then
		self._customCloseHandle(self)
	end

	Module.ItemDetail.Field:SetDragInfo(nil)
	local item = self:GetDetailItem()
	Module.ItemDetail.Config.evtItemDetailPanelClosed:Invoke(item)
	if self._closeCallback then
		safecall(self._closeCallback)
		self._closeCallback = nil
	end

	-- 关闭详情页后，处理通用道具的选中态
	if self._bCancelItemSelectionWhenClose then
		Module.CommonWidget:CancelItemSingleSelect()
	end

	self._wtTitleContent.Slot:SetPosition(FVector2D(0, -60))
end

function FoldItemDetail:SetCustomClosePanelLogic(fCustomCloseCallback, caller)
	self._customCloseHandle = SafeCallBack(fCustomCloseCallback, caller)
end

function FoldItemDetail:_AddEventListener()
	local gameInst = GetGameInstance()
    self._buttonUpHandle = UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonUpEvent:Add(self._OnHandleMouseButtonUpEvent, self)
	self._buttonDownHandle = UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonDownEvent:Add(self._OnHandleMouseButtonDownEvent, self)
	self._buttonMoveHandle = UDFMGameHudDelegates.Get(gameInst).OnHandleMouseMoveEvent:Add(self._OnHandleMouseButtonMoveEvent, self)

	self:AddLuaEvent(Server.InventoryServer.Events.evtItemMove, self._OnItemMove, self)
    self:AddLuaEvent(Server.LootingServer.Events.evtLootingItemMove, self._OnItemMove, self)
	UDFMGameplayGlobalDelegates.Get(gameInst).OnUnEquipAmmoResult:Add(self._OnBulletUnEquip, self)
end

function FoldItemDetail:_RemoveEventListener()
	local gameInst = GetGameInstance()
	if self._buttonUpHandle then
		UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonUpEvent:Remove(self._buttonUpHandle)
		self._buttonUpHandle = nil
	end
	if self._buttonDownHandle then
		UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonDownEvent:Remove(self._buttonDownHandle)
		self._buttonDownHandle = nil
	end
	if self._buttonMoveHandle then
		UDFMGameHudDelegates.Get(gameInst).OnHandleMouseMoveEvent:Remove(self._buttonMoveHandle)
		self._buttonMoveHandle = nil
	end
	UDFMGameplayGlobalDelegates.Get(gameInst).OnUnEquipAmmoResult:Remove(self._OnBulletUnEquip, self)
	self:RemoveAllLuaEvent()
end

function FoldItemDetail:_OnHandleMouseButtonUpEvent(mouseEvent)
	if Module.ItemDetail:GetUserScrolledInGap() then
		return
	end
	if Module.ItemDetail.Field.bDisableCloseOnClickBg then return end
	self._beginTouchPos = nil
	self._bMoveStateChecked = false
	if not self._bValid then return end
	if self._bCustomVisibilityControllLogic or not self:IsVisible() then
		return
	end

	self:SetVisibility(self._bSwallowBgTouch and ESlateVisibility.Visible or ESlateVisibility.SelfHitTestInvisible)
	Module.ItemDetail.Field:SetDragInfo(nil)
	if Module.ItemDetail.Field:GetIsLastDrag() then
		return
	end

    local absolutePoint = mouseEvent:GetScreenSpacePosition()

	local bInside = UIUtil.CheckAbsolutePointInsideWidget(self._wtTitleContent, absolutePoint)
	if self._ignoreWidget then
		bInside = bInside or UIUtil.CheckAbsolutePointInsideWidget(self._ignoreWidget, absolutePoint)
	end

	if not bInside and Module.ItemDetail:CanItemDetailPanelClose(self._layerLevel, absolutePoint, true) then
		if Module.ItemDetail:GetItemDetailPanelType() ~= EFeatureType.Weapon then
			self:_DelayClosePanel(true)
		elseif self.canClose then
			self:_DelayClosePanel(true)
		end
	end
end

function FoldItemDetail:_OnHandleMouseButtonDownEvent(mouseEvent)
	if not self._bValid then return end
	Module.ItemDetail.Field:SetIsLastDrag(false)
	Module.ItemDetail.Field:SetIsLastClosed(false)
	self._bMoveStateChecked = false
	self._beginTouchPos = mouseEvent:GetScreenSpacePosition()
	if self._bCanDrag == false then
		return
	end
end

function FoldItemDetail:_OnHandleMouseButtonMoveEvent(mouseEvent)
	if not self._bValid then return end
	if not self._bMoveStateChecked and self._beginTouchPos then
		local absolutePoint = mouseEvent:GetScreenSpacePosition()
		local threshold = 10
		if math.abs(self._beginTouchPos.X - absolutePoint.X) > threshold and
			math.abs(self._beginTouchPos.Y - absolutePoint.Y) > threshold then
			Module.ItemDetail.Field:SetIsLastDrag(self:IsHit(self._beginTouchPos))
			self._bMoveStateChecked = true
		end
	end
	Module.ItemDetail.Field:SetIsLastClosed(false)
end

---@param itemMoveInfo itemMoveInfo
function FoldItemDetail:_OnLootingItemMove(itemMoveInfo)
	local item = itemMoveInfo.item
	if self._itemStruct == item and itemMoveInfo.Reason ~= PropChangeType.Modify then
		self:_ClosePanel()
	end
end

function FoldItemDetail:IgnoreWidgetOnBtnUp(widget)
	self._ignoreWidget = widget
end

function FoldItemDetail:IsHit(absolutePoint)
	if self._bCustomVisibilityControllLogic or not self:IsVisible() then
		return false
	end
	local bInside = UIUtil.CheckAbsolutePointInsideWidget(self._wtTitleContent, absolutePoint)

	if self._ignoreWidget then
		bInside = bInside or UIUtil.CheckAbsolutePointInsideWidget(self._ignoreWidget, absolutePoint)
	end

	return bInside
end

function FoldItemDetail:SetCancelItemSelectionWhenClose(bCancelItemSelectionWhenClose)
	self._bCancelItemSelectionWhenClose = bCancelItemSelectionWhenClose
end

function FoldItemDetail:_SetValid(bValid)
	self._bValid = bValid
	if not bValid then
		self._ignoreWidget = nil
	end
end

function FoldItemDetail:SetSwallowBgTouches(bSwallow)
	self._bSwallowBgTouch = bSwallow
	self:SetVisibility(self._bSwallowBgTouch and ESlateVisibility.Visible or ESlateVisibility.SelfHitTestInvisible)
end

---------------------------Touch改Mouse--------------------------------------
function FoldItemDetail:_OnCloseBtnClick()
	Module.ItemDetail.Field:SetIsLastClosed(false)
	self:_ClosePanel()
end

------------------------------------ Private function ------------------------------------
function FoldItemDetail:_DelayClosePanel(bOnlyTop)
	if ItemOperaTool.CheckRunSingleClickMoveItemLogic() then
		self.bIsDelayClose = true
		Timer.DelayCall(0.01, function()
			self:_ClosePanel(bOnlyTop)
		end, self)
	else
		self:_ClosePanel(bOnlyTop)
	end
end

function FoldItemDetail:_ClosePanel(bOnlyTop)
	self.bIsDelayClose = false
	if not Module.ItemDetail.Field:GetMainPanelHandle() and self:IsVisible() then
		Facade.UIManager:CloseUI(self)
	end
	Module.ItemDetail:CloseItemDetailPanel(self._layerLevel, bOnlyTop)
	if self._layerLevel == Module.ItemDetail.Config.ETipsLayerLevel.Main then
		ItemDetailLogic.SetCloseInfo(self._itemStruct)
	end
end

-- 设置详情页标题
function FoldItemDetail:SetItemTitle()
    if self._itemStruct == nil then
        return
    end
    local name = self._itemStruct.name
    -- 次级预设道具隐藏次级预设信息时，名字做一下处理
    local weaponFeature = self._itemStruct:GetFeature(EFeatureType.Weapon)
    if weaponFeature and weaponFeature:IsPoorWeapon() and not weaponFeature:IsShowPoorWeapon() then
        local tempItem = ItemBase:NewIns(self._itemStruct.id)
        name = tempItem.name
    end

    local itemFeature = self._itemStruct:GetFeature()
    if itemFeature.IsCollecionItem and itemFeature:IsCollecionItem() then
        self._bCollectionItem = true
    else
        self._bCollectionItem = false
    end

    -- 玄学皮肤橙色改名后更换读取来源
    if
        ItemHelperTool.GetMainTypeById(self._itemStruct.id) == EItemType.WeaponSkin and
            ItemHelperTool.GetThirdTypeById(self._itemStruct.id) == 0
     then
        if self._itemStruct.rawPropInfo and self._itemStruct.rawPropInfo.mystical_skin_data then
            local mysticalPropInfo = self._itemStruct.rawPropInfo.mystical_skin_data
            if mysticalPropInfo.custom_name ~= "" then
                name = mysticalPropInfo.custom_name
            end
        end
    end

    -- 武器装备改名的玄学皮肤，名字显示玄学皮肤的名字
    if weaponFeature then
        local weaponDesc = self._itemStruct:GetRawDescObj()
        local weaponMysticalPropInfo = weaponDesc and weaponDesc.GetSkinInfo and weaponDesc:GetSkinInfo()
        if weaponMysticalPropInfo and ItemHelperTool.GetThirdTypeById(weaponMysticalPropInfo.SkinId) == 0 then
            if weaponMysticalPropInfo.SkinGid ~= 0 and weaponMysticalPropInfo.CustomName ~= "" then
                name = weaponMysticalPropInfo.CustomName
            end
        end
    end

    self._wtNameLabel:SetText(name)
    if itemDetailField:GetCacheItemStruct() ~= self._itemStruct then
    -- self._wtNameLabel:PlayAnim_ComputeTextBlock()
    end

    if _WITH_EDITOR == 1 then
        self._wtItemId:SetText(self._itemStruct.id)
        if
            ItemHelperTool.GetMainTypeById(self._itemStruct.id) == EItemType.WeaponSkin and
                ItemHelperTool.GetThirdTypeById(self._itemStruct.id) == 0
         then
            if self._itemStruct.rawPropInfo and self._itemStruct.rawPropInfo.mystical_skin_data then
                local mysticalPropInfo = self._itemStruct.rawPropInfo.mystical_skin_data
                self._wtItemId:SetText(
                    string.format(
                        "%d_%d_%d",
                        self._itemStruct.id,
                        mysticalPropInfo.appearance.id,
                        mysticalPropInfo.appearance.seed
                    )
                )
            end
        end
    end
    -- Quality
    local quality = self._itemStruct.quality
    local qualityColor = ItemConfigTool.GetItemQualityLinearColor(self._itemStruct.quality)
    -- -- 对枪械进行处理
    -- if weaponFeature then
    -- 	-- 武器是否装备蓝图
    -- 	local skinid = Server.InventoryServer:GetWeaponSkinID(self._itemStruct.gid)
    -- 	if skinid ~= 0 then
    -- 		local skinquality = ItemHelperTool.GetQualityTypeById(skinid)
    -- 		qualityColor = ItemConfigTool.GetItemQualityLinearColor(skinquality)
    -- 		quality = skinquality
    -- 	end
    -- end

    -- 武器不显示品阶
    if weaponFeature and ItemHelperTool.GetMainTypeById(self._itemStruct.id) ~= EItemType.WeaponSkin then
        quality = 0
        qualityColor = nil
    end

    -- 兵种道具不显示品阶
    if ItemHelperTool.IsArmedForceUniquePropByItem(self._itemStruct) then
        quality = 0
        qualityColor = nil
    end

    -- 载具不显示品阶
    if ItemHelperTool.GetMainTypeById(self._itemStruct.id) == EItemType.Vehicle then
        quality = 0
        qualityColor = nil
    end

    -- MP模式下配件不显示品阶
    local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
    if armedForceMode == EArmedForceMode.MP then
        if ItemHelperTool.GetMainTypeById(self._itemStruct.id) == EItemType.Adapter then
            quality = 0
            qualityColor = nil
        end
    end

    -- 特战干员不显示品阶
    if ItemHelperTool.GetMainTypeById(self._itemStruct.id) == EItemType.Hero then
        quality = 0
        qualityColor = nil
    end

    if not qualityColor then
        if self._wtTitleContent.Type == 0 then
            self._wtTitleContent:SetType(1)
        elseif self._wtTitleContent.Type == 2 then
            self._wtTitleContent:SetType(3)
        end
    else
        self._wtTitleContent:SetType(self._wtTitleContent.Type)
    end

    if qualityColor then
        -- self._wtTitleBgImage:SetColorAndOpacity(qualityColor)
        self._wQualityIcon:SetColorAndOpacity(qualityColor)
    end

    -- quality映射到合法区间
    quality = math.max(quality - 1, 0)
    quality = math.min(quality, 5)
    self._wtTitleContent:BpSetQuality(quality)
end

function FoldItemDetail:SetItemTitleFromItemID(itemID)
    local name = ItemConfigTool.GetItemName(itemID)
    self._wtNameLabel:SetText(name)

    local quality = ItemConfigTool.GetItemQuality(itemID)
    local qualityColor = ItemConfigTool.GetItemQualityLinearColor(quality)
    if not qualityColor then
        qualityColor = ItemConfigTool.GetItemQualityLinearColor(1)
    end
    if qualityColor then
        self._wtTitleBgImage:SetColorAndOpacity(qualityColor)
        self._wQualityIcon:SetColorAndOpacity(qualityColor)
    --self._wtNameLabel:SetColorAndOpacity(FSlateColor(qualityColor))
    end

    local quality = math.max(quality - 1, 0)
    quality = math.min(quality, 5)
    self:BpSetQuality(quality)
end

function FoldItemDetail:SetItemTitleName(name)
    self._wtNameLabel:SetText(name)
end

-- 设置关闭按钮是否显示
function FoldItemDetail:SetCloseBtnVisible(bVisible)
    if bVisible then
        self._wtCloseBtn:SetVisibility(ESlateVisibility.Visible)
    else
        self._wtCloseBtn:SetVisibility(ESlateVisibility.Collapsed)
    end
end

-- 透明详情页屏蔽品质背景
function FoldItemDetail:SetTitleBgVisible(bVisible)
    if bVisible then
        self._wtTitleBgImage:SetVisibility(ESlateVisibility.Visible)
    else
        self._wtTitleBgImage:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function FoldItemDetail:SetShowDiffVisible(bShow, bUp)
end

function FoldItemDetail:SetDragPanel(panel, parent)
    self._dragWidget = panel
    self._dragPanelParent = parent
end

function FoldItemDetail:SetCanDragState(bCanDrag)
    self._bCanDrag = bCanDrag
end


---- TODO need delete end -----

--通用描述弹窗
function FoldItemDetail:_OnFoldCliked(bIsChecked)
    Module.ItemDetail:SetIsFold(false)
	self:_OnCloseBtnClick(true)
    local cacheItemDetailParams = Module.ItemDetail.Field:GetCacheItemDetailParams()
    Module.ItemDetail:OpenItemDetailPanel(
        cacheItemDetailParams.itemStruct,
        cacheItemDetailParams.parentWidget,
        cacheItemDetailParams.bFastEquippable,
        cacheItemDetailParams.bShowBtn,
		-- cacheItemDetailParams.btnTypeList,
		nil, -- 折叠态的按钮并不和展开态通用，需要重新生成
        cacheItemDetailParams.btnNavId,
        cacheItemDetailParams.fPanelLoadCallback,
        cacheItemDetailParams.layerLevel,
        cacheItemDetailParams.bCanDrag,
        cacheItemDetailParams.closeCallback,
        cacheItemDetailParams.transBtnList,
        cacheItemDetailParams.bNeedHideUnequipBulletBtn,
        true,
		cacheItemDetailParams.bOnCommonPop
    )
end

-- 设置价格显隐性
function FoldItemDetail:SetPriceVisible(bVisible)
	if self._bInMp then
		self._wtCurrencyRichText:SetVisibility(ESlateVisibility.Collapsed)
		return
	end
	if bVisible then
		self._wtCurrencyRichText:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	else
		self._wtCurrencyRichText:SetVisibility(ESlateVisibility.Collapsed)
	end
end

-- 设置交易模式
-- tradeMode: 出售（显示价格）:ETradeMode.Sell； 购买（隐藏价格）:ETradeMode.Buy；只显示数量：ETradeMode.ShowNum
function FoldItemDetail:SetTradeMode(tradeMode)
	self._tradeMode = tradeMode
	self:SetItemPrice()
end

-- 设置价格
function FoldItemDetail:SetItemPrice(customRichText)
	local showItem = self._itemStruct

	-- 藏品不显示价格
	local itemFeature = showItem:GetFeature()
	if itemFeature.IsCollecionItem and itemFeature:IsCollecionItem() then
		self:SetPriceVisible(false)
		return
	elseif ItemHelperTool.GetMainTypeById(self._itemStruct.id) == EItemType.WeaponSkin or ItemDetailLogic.IsCurrencyItem(self._itemStruct.id) or ItemDetailLogic.IsExpItem(self._itemStruct.id)
			or ItemHelperTool.GetMainTypeById(self._itemStruct.id) == EItemType.VehicleSkin
			or ItemHelperTool.GetMainTypeById(self._itemStruct.id) == EItemType.Adapter and ItemHelperTool.GetSubTypeById(self._itemStruct.id) == ItemConfig.EAdapterItemType.Pendant
			or ItemHelperTool.GetMainTypeById(self._itemStruct.id) == EItemType.Equipment and ItemHelperTool.GetSubTypeById(self._itemStruct.id) == EEquipmentType.SafeBox
			or ItemHelperTool.GetMainTypeById(self._itemStruct.id) == EItemType.Equipment and ItemHelperTool.GetSubTypeById(self._itemStruct.id) == EEquipmentType.KeyChain
			or ItemDetailLogic.IsActivityItem(self._itemStruct.id)
			or ItemDetailLogic.IsPrestige(self._itemStruct.id) then
		self:SetPriceVisible(false)
		return
	elseif (itemFeature.IsUndecipheredBrick and itemFeature:IsUndecipheredBrick()) or (itemFeature.IsUnableDecipherBrick and itemFeature:IsUnableDecipherBrick()) or (itemFeature.IsDecipheredBrick and itemFeature:IsDecipheredBrick()) then
		self:SetPriceVisible(false)
		return
	elseif ItemHelperTool.GetMainTypeById(self._itemStruct.id) == EItemType.SocialAppearance then
		self:SetPriceVisible(false)
		return
	elseif ItemHelperTool.GetMainTypeById(self._itemStruct.id) == EItemType.HeroAccessory then
		self:SetPriceVisible(false)
		return
	end

	local realPrice = 0
	local showNum = self.showNum or showItem.num
	if self._tradeMode == ETradeMode.Sell then
		realPrice = showItem:GetSingleSellPrice()
		self._wtCurrencyRichText:SetText(ShopHelperTool.GetDynamicGuidePriceRichTextByItemV3(showItem, 1, nil, nil, customRichText, nil, FVector2D(44, 44)))
		self:SetPriceVisible(realPrice ~= 0)
	elseif self._tradeMode == ETradeMode.ShowNum then
		self._wtCurrencyRichText:SetText(string.format(ItemDetailConfig.Loc.itemNum, showNum))
		self:SetPriceVisible(showNum ~= 0)
	else
		local bForceDefault = true
		realPrice = Server.ShopServer:GetShopBuyPriceByItem(showItem)
		self._wtCurrencyRichText:SetText(ShopHelperTool.GetShopBuyPriceRichTextByItemV2(showItem, 1, false, bForceDefault, customRichText))
		self:SetPriceVisible(realPrice ~= 0)
	end
end

function FoldItemDetail:SetDragState(bCanDrag)
	self._bCanDrag = bCanDrag
	if self._bCanDrag then
		self._wtTitleContent:SetDragPanel(self._wtTitleContent, self)
		self._wtTitleContent:SetCanDragState(bCanDrag)
	end
end

function FoldItemDetail:OnDragOver(inGeometry, inDragDropEvent, operation)
	if not self._bValid then return end
	if not self._bCanDrag then
		return
	end

	local dragWidget = operation.WidgetReference
	local dragInfo = Module.ItemDetail.Field:GetDragInfo()
	if not dragInfo then
		return
	end

	local dragOffset = dragInfo.dragOffset
	local absolutePoint = inDragDropEvent:GetScreenSpacePosition()
	local viewportGeometry = UE.WidgetLayoutLibrary.GetViewportWidgetGeometry(dragInfo.dragPanelParent)
    local localPos = viewportGeometry:AbsoluteToLocal(absolutePoint)
	local safePos = self:_GetSafePosition(dragInfo.startDragPos + localPos - dragOffset)
	dragInfo.dragWidget.Slot:SetPosition(safePos)
end

function FoldItemDetail:_GetSafePosition(oriLocalPosition)
	local alignment = self._wtTitleContent.Slot:GetAlignment()

	local parentGeometry = self:GetCachedGeometry()
	local widgetGeometry = self._wtTitleContent:GetCachedGeometry()
	local adjuestWidgetScaleX = self._wtTitleContent.RenderTransform.Scale.X

	local ltGlobalPos = parentGeometry:GetAbsolutePositionAtCoordinates(FVector2D(0, 0))
	local rbGlobalPos = parentGeometry:GetAbsolutePositionAtCoordinates(FVector2D(1, 1))
	local ltWidgetGlobalPos = widgetGeometry:GetAbsolutePositionAtCoordinates(FVector2D(0, 0))
	local rbWidgetGlobalPos = widgetGeometry:GetAbsolutePositionAtCoordinates(FVector2D(1, 1))
	local oriGlobalPos = parentGeometry:LocalToAbsolute(oriLocalPosition) - ltGlobalPos
	local parentViewSize = rbGlobalPos - ltGlobalPos
	local panelGlobalSize = rbWidgetGlobalPos - ltWidgetGlobalPos

	local xOffset = 0
	local yOffset = 0

	if DFHD_LUA == 1 then
		yOffset = math.max(-(oriGlobalPos.Y - alignment.Y * panelGlobalSize.Y), 0)
		if yOffset == 0 then
			yOffset = math.min(parentViewSize.Y- (oriGlobalPos.Y + (1 - alignment.Y) * panelGlobalSize.Y), 0)
		end
	else
		yOffset = math.max(-oriGlobalPos.Y, 0)
		if yOffset == 0 then
			yOffset = math.min(parentViewSize.Y - panelGlobalSize.Y - oriGlobalPos.Y, 0)
		end
	end

	xOffset = math.max(-(oriGlobalPos.X - alignment.X * panelGlobalSize.X), 0)
	if xOffset == 0 then
		xOffset = math.min(parentViewSize.X- (oriGlobalPos.X + (1 - alignment.X) * panelGlobalSize.X), 0)
	end

	local globalSafePos = FVector2D(oriGlobalPos.X + xOffset, oriGlobalPos.Y + yOffset) + ltGlobalPos
	local localSafePos =  parentGeometry:AbsoluteToLocal(globalSafePos)
	--logerror("_GetSafePosition:", safePos.X, safePos.Y, safeWidth, safeHeight, viewportLocalSize.X, viewportLocalSize.Y)
	return localSafePos
end

function FoldItemDetail:GetParentWidget()
	return self._parentWidget
end

--- 由用户自定义其显示隐藏的逻辑
--- 目前会影响点击外部区域自动关闭的逻辑
function FoldItemDetail:SetCustomVisibilityControllLogic(bCustom)
	self._bCustomVisibilityControllLogic = bCustom
end

function FoldItemDetail:GetItem()
	return self._itemStruct
end

function FoldItemDetail:OnNavBack()
	ItemDetailLogic.CloseLastPopUI()
	return true
end

function FoldItemDetail:OnActivate()
	self:_SetValid(true)
	self._bCustomVisibilityControllLogic = false
	self._bCanDrag = false
	self.canClose = true
	self._bSwallowBgTouch = false
	self:SetCppValue("bSwallowClick", false)
	self:SetCloseBtnVisible(true)
end

function FoldItemDetail:OnDeactivate()
	self:_SetValid(false)
	self:ProcessClose()
end

return FoldItemDetail
