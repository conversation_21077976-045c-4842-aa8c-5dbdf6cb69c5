----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGuide)
----- LOG FUNCTION AUTO GENERATE END -----------



local GuideDataBase = require "DFM.Business.Module.GuideModule.Data.GuideDataBase"
local GuideConfig = require "DFM.Business.Module.GuideModule.GuideConfig"
local GuideLogic = require "DFM.Business.Module.GuideModule.GuideLogic"

---@class GuideDataHDPopDescAndWeakClick : GuideDataBase
local GuideDataHDPopDescAndWeakClick = class('GuideDataHDPopDescAndWeakClick', GuideDataBase)

function GuideDataHDPopDescAndWeakClick:Ctor()
    self._dialogHandle = nil

    local cfg  = self:GetGuideCfg()
    local cfgId = tonumber(cfg.Args)
    local popFunctionCfg = Module.Guide.Config.TableGuidePopFunctionDescConfig[cfgId]
    self.popDescID = cfgId
    self.clikID = popFunctionCfg.ClickConfigId[1]
end

function GuideDataHDPopDescAndWeakClick:Destroy()
    if Module.Guide then
        local cfgId = tonumber(self._guideCfg.Args)
        Module.Guide:CloseGuideHDPopFunctionDescAndWeakClickUI(cfgId)
    end
end

function GuideDataHDPopDescAndWeakClick:OnStartGuide()
    local cfgId = tonumber(self._guideCfg.Args)

    -- occur:
    -- 1. HDPopDesc space btn pressed
    -- 2. Guide step timer timeout
    local _callbackOnCloseUI = function()
        self:EndGuide(#self._guideCfg.NextGuideId)
    end
    Module.Guide:OpenGuideHDPopFunctionDescAndWeakClickUI(cfgId, _callbackOnCloseUI)
end

function GuideDataHDPopDescAndWeakClick:OnEndGuide(idx)
    local cfgId = tonumber(self._guideCfg.Args)
    Module.Guide:CloseGuideHDPopFunctionDescAndWeakClickUI(cfgId)
end

function GuideDataHDPopDescAndWeakClick:OnPause(uiIns)
    local cfgId = tonumber(self._guideCfg.Args)
    Module.Guide:SetPopFunctionDescAndWeakClickUIValidState(cfgId, false)
end

function GuideDataHDPopDescAndWeakClick:OnRestart()
    local cfgId = tonumber(self._guideCfg.Args)
    Module.Guide:SetPopFunctionDescAndWeakClickUIValidState(cfgId, true)
end

function GuideDataHDPopDescAndWeakClick:OnInputTypeChanged(inputType)
    -- 重新定位一下, 比如BottonBar 位置会刷新
    Module.Guide:OpenGuideHDWeakClickUI(self.clikID, self.popDescID)
end

return GuideDataHDPopDescAndWeakClick