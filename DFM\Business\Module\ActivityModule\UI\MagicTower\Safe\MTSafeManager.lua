----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class MTSafeManager : LuaObject
local MTSafeManager = class('MTSafeManager', LuaObject)

local MTSafeItem = require "DFM.Business.Module.ActivityModule.UI.MagicTower.Safe.MTSafeItem"
local ConfigManager = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerConfigManager"
local ActivityInputHandler = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityInputHandler"

function MTSafeManager:Ctor()
    -- 初始化随机种子
    math.randomseed(os.time())

    self._safeConfig = ConfigManager.GetTreasureConfigTable()           -- 保险箱
    self._safePropConfig = ConfigManager.GetTreasureProbConfigTable()   -- 保险概率
    self._safeItemConfig = ConfigManager.GetTreasureItemConfigTable()   -- 保险物品

    self._allProb = {}  -- 按概型和组分类
    self.allSafeItemInfo = {}   -- 生成的Item信息，根据safeId分组
    self.allSafeDescInfo = {}   -- 名称信息
    self.curSafeId = 0

    self:InitConfig()
    -- Timer.DelayCall(1, self.OpenSafePanelWithSafeId, self, 3050001, 1)
end

function MTSafeManager:InitConfig()
    --- 为概型分组
    for index, value in ipairs(self._safePropConfig) do
        self._allProb[value.treasureProbID] = self._allProb[value.treasureProbID] or {}
        self._allProb[value.treasureProbID][value.group] = self._allProb[value.treasureProbID][value.group] or {}
        table.insert(self._allProb[value.treasureProbID][value.group], value)
    end
end

---打开保险箱UI
---@param treasureID integer 保险箱类型id
---@param safeId     integer 保险箱识别id，用于区分同类型保险箱
function MTSafeManager:OpenSafePanelWithSafeId(treasureID, safeId)
    if not self.allSafeItemInfo[safeId] then
        self:CreateSafeInfo(treasureID, safeId)
    end
    ActivityInputHandler.DisableInput()

    self.curSafeId = safeId
    self.tempItemIDList = {}

    self:AddLuaEvent(MTSafeItem.evtItemPickup, self.OnItemClicked, self)

    local fCloseCallBack = SafeCallBack(self.OnSafePanelClosed, self)
    Facade.UIManager:AsyncShowUI(UIName2ID.MTSafePanel, nil, nil, self.allSafeItemInfo[safeId], self.allSafeDescInfo[safeId], safeId, fCloseCallBack)
end

--- 关闭panel后移除数据
function MTSafeManager:OnSafePanelClosed()
    self:RemoveLuaEvent(MTSafeItem.evtItemPickup)
    ActivityInputHandler.EnableInput()

    local tempItemInfo = self.allSafeItemInfo[self.curSafeId]

    for i = #tempItemInfo, 1, -1 do
        if self.tempItemIDList[i] then
            table.remove(self.allSafeItemInfo[self.curSafeId], i)
        end
    end

    self.tempItemIDList = {}
    self.curSafeId = 0
end

--- 记录已经拾取的物品id
function MTSafeManager:OnItemClicked(attributeChange, index)
    self.tempItemIDList[index] = true
end

---生成保险箱信息并保存
---@param treasureID integer 保险箱类型id
---@param safeId     integer 保险箱识别id，用于区分同类型保险箱
function MTSafeManager:CreateSafeInfo(treasureID, safeId)
    self.allSafeItemInfo[safeId] = self:CreateSafeItems(treasureID)
    self.allSafeDescInfo[safeId] = self._safeConfig[treasureID].name
end

local function shuffle(tbl)
    local n = #tbl
    for i = n, 2, -1 do
        local j = math.random(i)  -- 随机选择 [1, i] 范围内的索引
        tbl[i], tbl[j] = tbl[j], tbl[i]  -- 交换元素
    end
    return tbl
end

--- 根据保险箱id随机生成物品
---@param treasureID integer 保险箱类型id
function MTSafeManager:CreateSafeItems(treasureID)
    local propId = self._safeConfig[treasureID].treasureProb
    local prop = self._allProb[propId]
    local itemIdHash = {}

    -- 遍历组
    for key, group in pairs(prop) do
        -- 检查组是否生成
        if self:probCheck(group[1].groupProb) then
            local sumWeight = 0
            local weightTable = {}

            -- 计算总权重以及分段权重
            for i, item in ipairs(group) do
                sumWeight = sumWeight + item.treasureItemWeight
                table.insert(weightTable, sumWeight)
            end

            -- 根据权重计算组内物品是否生成
            local random = math.random() * sumWeight
            for i, weight in ipairs(weightTable) do
                if random <= weight then
                    itemIdHash[group[i].treasureItemID] = true
                end
            end
        end
    end

    local items = {}
    for i, item in pairs(self._safeItemConfig) do
        if itemIdHash[item.treasureItemID] then
            local newitem = {}
            deepcopy(newitem, item)
            table.insert(items, newitem)
        end
    end
    return shuffle(items)
end


-- 概率判断接口
---@param rate integer 成功概率，范围0-100
function MTSafeManager:probCheck(rate)
    -- 边界情况处理
    if rate == 0 then
        return false
    elseif rate == 100 then
        return true
    end
    
    -- 生成随机数并比较
    local randomValue = math.random(100)
    return randomValue <= rate
end

function MTSafeManager:Destroy()
    self.allSafeItemInfo = {}
    self.allSafeDescInfo = {}

    self._safeConfig = {}
    self._safePropConfig = {}
    self._safeItemConfig = {}
end

return MTSafeManager