----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaFUIManagerLayer)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class UIManager : ManagerBase
local UIManager = {}
local UGameplayStatics = import "GameplayStatics"
local PlatformLogic = require "DFM.YxFramework.Managers.UI.Platform.PlatformLogic"
local MemoryUtil = require "DFM.YxFramework.Util.MemoryUtil"
local UGPPerfManager = import "GPPerfManager"
local ULuaSubsystem = import "LuaSubsystem"
local UDFMGameNotch = import "DFMGameNotch"

---------------------------------------------------------------------------------
--- UIManager关于UMG实例OBJ的管理逻辑拆分
---------------------------------------------------------------------------------
----------------------------------------------------------------------------------------------------------------
--- * self._wtCanvasFrameGod-----------------------------------##全局根节点
---     *self._wtCanvasFrameLayerBackRoot----------------------**背景Layer-------------容纳EUILayer.BackRoot
---
---     *self.wtSafeZone_Frame---------------------------------##游戏安全区（功能型边距）
---         *self._wtCanvasFrameRoot---------------------------##通用根节点
---             *self._wtCanvasFrameLayerScene-----------------**SceneLayer------------容纳EUILayer.Scene的UI
---             *self._wtCanvasFrameLayerRoot------------------**RootLayer-------------容纳EUILayer.Root的UI
---
---             *self._wtPlatformPaddingForContent-------------##内容根节点（美观型边距）
---                 *self._wtCanvasFrameLayerStack-------------##StackLayer------------容纳EUILayer.Stack的UI
---
---             *self._wtCanvasFrameLayerTop------------------**TopLayer---------------容纳EUILayer.Top的UI
---             *self._wtCanvasFrameLayerTip------------------**TipLayer---------------容纳EUILayer.Tip的UI
---             *self._wtCanvasFrameLayerMask-----------------**MaskLayer--------------容纳EUILayer.Mask的UI
---             *self._wtCanvasFrameLayerWatermark------------**WatermarkLayer---------容纳EUILayer.Watermark的UI
----------------------------------------------------------------------------------------------------------------
local DefaultLayerController = require("DFM.YxFramework.Managers.UI.Layer.DefaultLayerController")
require "DFM.YxFramework.Managers.UI.Layer.FeatureLayerControllerBase"
local UGPUIManager = import "GPUIManager"
function UIManager:Ctor()
    --- 状态和等待队列
    self._curWorkingState = EWorkingState.Terminal
    self._waitingAddQueue = {}

    if UGPUIManager then
        self._gpUIManagerIns = UGPUIManager.Get(GetGameInstance())
        if isvalid(self._gpUIManagerIns) then
            self._gpUIManagerIns.OnPersistentRootFinished:Add(self._OnPersistentRootFinished, self)
        end
    end

    if ULuaSubsystem then
        self._luaResolutionResizedHandle = ULuaSubsystem.Get().OnNotifyLuaResolutionResized:Add(self._OnNotifyResolutionResized, self)
    end

    if UDFMGameNotch then
        local DFMGameNotch = UDFMGameNotch.Get(GetGameInstance())
        if DFMGameNotch and DFMGameNotch:IsFoldDevice() and (not self.statusHandle) then
            self.statusHandle = DFMGameNotch.OnFoldStatusChanged:Add(CreateCPlusCallBack(self._OnNotifyFoldStatusChanged, self))
        end
    end

    --- Layer逻辑
    self.mapLayer2Controller = {}
    self.defaultLayerController = DefaultLayerController:NewIns()
    self:InitUILayerController()

    self._contentSuitDataList = {}
    self._bStackBottomKeepMode = false
    self._stackBottomKeepModeReason = "Default"
    self._bLowMemoryCVarState = false
    self._bMemoryGuardedState = false

    self:AddLuaEvent(Facade.ResourceManager.Events.evtUILodLevelChanged, self._OnUILodLevelChanged, self)
    -- self:AddLuaEvent(LuaGlobalEvents.evtUObjectNumWarning, self._OnUObjectNumWarning, self)
    -- self:AddLuaEvent(LuaGlobalEvents.evtLowMemoryWarning, self._OnLowMemoryWarning, self)
    self:AddLuaEvent(LuaGlobalEvents.evtInGameControllerInited, self.OnInGameControllerInited,self)
    --self:AddLuaEvent(LuaGlobalEvents.evtLuaHandleUEPreGC, self.OnLuaHandleUEPreGC,self)
end

--------------------------------------------------------------------------
--- Layer 分层设计初始化（分层管理器逻辑）
--------------------------------------------------------------------------
function UIManager:InitUILayerController()
    for _, layerType in pairs(EUILayer) do
        self.mapLayer2Controller[layerType] = self:GetLayerControllerByType(layerType)
    end
    self:CheckIsLowMemoryState()
end

function UIManager:OnInGameControllerInited()
    logwarning("[Low Memory Log]  UIManager:OnInGameControllerInited")
    self:CheckIsLowMemoryState()
end

function UIManager:OnLuaHandleUEPreGC()
    -- logwarning("[Low Memory Log]  UIManager:OnLuaHandleUEPreGC()")
    -- if self:GetIsLowMemoryState() then
    --     local gameFlowType = Facade.GameFlowManager:GetCurrentGameFlow()
    --     -- if gameFlowType == EGameFlowStageType.Game then
    --         self:UltimateClearAllPoolWithRes()
    --         logwarning("[Low Memory Log]  UIManager:OnLuaHandleUEPreGC() GetCurrentGameFlow:Game self:UltimateClearAllPoolWithRes()")
    --     -- end
    -- end
end

function UIManager:CheckIsLowMemoryState()
    logwarning("[Low Memory Log]  UIManager:CheckIsLowMemoryState")
    self._bLowMemoryCVarState = self:CycleSyncIsLowMemoryState() or MemoryUtil.IsLogicLowMemoryDevice()
    if self._bLowMemoryCVarState then
        if self._lowMemoryHandle == nil then
            self._lowMemoryHandle = Timer.DelayCallSomeTimes(self._stackLruDurationLow, 0, function(self)
                self:_InternalClearOldestPoolUI()
            end, self)
        end
    else
        if self._highMemoryHandle == nil then
            local curGameFlowType = Facade.GameFlowManager:GetCurrentGameFlow()
            if curGameFlowType ~= EGameFlowStageType.Game then
                self._highMemoryHandle = Timer.DelayCallSomeTimes(self._stackLruDuration, 0, function(self)
                    self:_InternalClearOldestPoolUI()
                end, self)
            end
        end
    end
end

function UIManager:GetLayerControllerByType(layerType)
    local bDefault = UILayer2ControllerPath[layerType] == nil and true or false
    local layerController = self.mapLayer2Controller[layerType]
    if layerController == nil then
        local layerControllerPath = UILayer2ControllerPath[layerType]
        if layerControllerPath then
            layerController = require(layerControllerPath):NewIns(layerType)
        else
            layerController = self.defaultLayerController
        end
    end
    return layerController, bDefault
end

--------------------------------------------------------------------------
--- Layer 分层设计初始化（分辨率相关）
--------------------------------------------------------------------------
function UIManager:_OnNotifyResolutionResized(newSizeX, newSizeY)
    logwarning('[ 统一根节点RootUI Debug ]------------------------ ** RootUpdate - OnViewportResized **')
    if self._viewportPersistentRootUI and not hasdestroy(self._viewportPersistentRootUI) then
        self._viewportPersistentRootUI:InitSafeZone()
    end
end

function UIManager:_OnNotifyFoldStatusChanged(foldState, expandScreenCount)
    logwarning('[ 统一根节点RootUI Debug ]------------------------ ** RootUpdate - OnFoldStatusChanged **', foldState, expandScreenCount)
    if self._viewportPersistentRootUI and not hasdestroy(self._viewportPersistentRootUI) then
        self._viewportPersistentRootUI:InitSafeZone()
    end
end

--------------------------------------------------------------------------
--- Layer 分层设计初始化（UI根节点表现）
--------------------------------------------------------------------------
function UIManager:_OnPersistentRootFinished(viewportPersistentRootUI)
    logframe('UIManager:_OnPersistentRootFinished()', viewportPersistentRootUI)
    if isvalid(self._gpUIManagerIns) then
        self._viewportPersistentRootUI = viewportPersistentRootUI
        self._viewportPersistentRootUI:AddCloseCallBack(self._ClosePersistentFrameRootUI, self)

        self:_ActivatePersistentLayer()
    end
end

function UIManager:_ClosePersistentFrameRootUI()
    if isvalid(self._gpUIManagerIns) then
        if self._viewportPersistentRootUI and not hasdestroy(self._viewportPersistentRootUI) then
            -- self._viewportRootUI:RemoveFromViewport()
        end
        self._gpUIManagerIns:DeactivatePersistentRoot()
        logwarning('#[ 统一根节点RootUI Debug ]------------------------【_CloseLayers】 PersistentFrameRoot RemoveFromViewport')
    end
end

function UIManager:_ActivatePersistentLayer()
    if not hasdestroy(self._viewportPersistentRootUI) and not self._viewportPersistentRootUI:IsInViewport() then
        if isvalid(self._gpUIManagerIns) then
            --- 这里可能不需要
            self._gpUIManagerIns:ActivatePersistentRoot()
            logwarning('#[ 统一根节点RootUI Debug ]------------------------【_ActivatePersistentLayer】 PersistentFrameRoot AddToViewport')
        end
    end
    self._inputMonitor:ClearInputCountProcess()
end

function UIManager:InitUILayers()
    if not (DEFAULT_CPLUS_LAYER and isvalid(self._gpUIManagerIns)) then
        self:InitLuaUILayers()
    else
        -- 不依赖Lua初始化
        -- self._gpUIManagerIns:InitPersistentUILayers()
    end
end

function UIManager:InitLuaUILayers()
    if DEFAULT_CPLUS_LAYER and isvalid(self._gpUIManagerIns) then
        return
    end
    if self._curWorkingState ~= EWorkingState.Initing and self._curWorkingState ~= EWorkingState.Idle then
        self._curWorkingState = EWorkingState.Initing
        if self._viewportRootUI == nil or hasdestroy(self._viewportRootUI) then
            local fRootUICallback = function(rootUI)
                logwarning('#[ 统一根节点RootUI Debug ]------------------------【InitUILayers】 新建的FrameRoot AddToViewport')
                self._viewportRootUI = rootUI
                self._viewportRootUI:AddCloseCallBack(self._CloseFrameRootUI, self)
                self:_ActivateLuaLayer()
            end
            self:AsyncCreateSubUI(UIName2ID.VIEWPORT_ROOTUI, fRootUICallback)
        else
            self:_ActivateLuaLayer()
        end
    end
end

function UIManager:_ActivateLuaLayer()
    if DEFAULT_CPLUS_LAYER and isvalid(self._gpUIManagerIns) then
        return
    end
    if self._viewportRootUI and not self._viewportRootUI:IsInViewport() then
        self._viewportRootUI:AddToViewport(DEFAULT_ZORDER_BASE)
    end

    self._inputMonitor:ClearInputCountProcess()
    logwarning('#[ 统一根节点RootUI Debug ]------------------------【_ActivateLuaLayer】 FrameRoot AddToViewport')
    self._curWorkingState = EWorkingState.Idle
    self:ClearLuaWaitingQueue()
end

function UIManager:ClearLuaWaitingQueue()
    if DEFAULT_CPLUS_LAYER and isvalid(self._gpUIManagerIns) then
        return
    end
    if self._waitingAddQueue and next(self._waitingAddQueue) then
        for _, waitingInfo in ipairs(self._waitingAddQueue) do
            if waitingInfo.uiIns and type(waitingInfo.uiIns) == "table" and not hasdestroy(waitingInfo.uiIns) then
                self:_DoAddToFrameRoot(waitingInfo.uiIns, waitingInfo.zOrder, waitingInfo.layerType)
            end
        end
    end
    self._waitingAddQueue = {}
end

function UIManager:AddToLuaWaitingQueue(uiIns, zOrder, layerType)
    for idx, waitingInfo in ipairs(self._waitingAddQueue) do
        if waitingInfo.uiIns == uiIns then
            logwarning('#[ 统一根节点RootUI Debug ]------------------------【AddToFrameRoot】UI BP WAITING Repeated, cancel：', uiIns.UISettings.BPPath,'  当前在层级：', MapUILayer2Name[layerType],'  当前ZOrder：',zOrder)
            return
        end
    end
    local waitingInfo = {uiIns = uiIns, zOrder = zOrder, layerType = layerType}
    table.insert(self._waitingAddQueue, waitingInfo)
    logwarning('#[ 统一根节点RootUI Debug ]------------------------【AddToFrameRoot】UI BP WAITING：', uiIns.UISettings.BPPath,'  当前在层级：', MapUILayer2Name[layerType],'  当前ZOrder：',zOrder)
    --等UIManager创建rootui，这里不触发
    --self:InitUILayers()
end

---@param uiIns ui
---@param zOrder number
---@param layerType EUILayer
function UIManager:AddToFrameRoot(uiIns, zOrder, layerType)
     local bAddPersistent = false
    if isvalid(self._gpUIManagerIns) and (PlatformLogic.IsForceAreaType(layerType) or DEFAULT_CPLUS_LAYER) then
        bAddPersistent = true
    end
    if bAddPersistent then
        if not self._gpUIManagerIns:CheckIsIdleState() then
            self._gpUIManagerIns:AddToWaitingQueue(uiIns, zOrder, layerType, false)
            return
        end
        self:_DoAddToFrameRoot(uiIns, zOrder, layerType)
    else
        local viewportRootUI = self:_InternalGetFrameRoot()
        if viewportRootUI == nil or hasdestroy(viewportRootUI) or not viewportRootUI:IsInViewport() then
            self:AddToLuaWaitingQueue(uiIns, zOrder, layerType)
            return
        end
        self:_DoAddToFrameRoot(uiIns, zOrder, layerType)
    end
end

---@param uiIns ui
---@param zOrder number
---@param layerType EUILayer
function UIManager:_DoAddToFrameRoot(uiIns, zOrder, layerType)
    local bAddPersistent = false
    if isvalid(self._gpUIManagerIns) and (PlatformLogic.IsForceAreaType(layerType) or DEFAULT_CPLUS_LAYER) then
        bAddPersistent = true
    end

    if bAddPersistent then
        if not hasdestroy(self._viewportPersistentRootUI) and self._viewportPersistentRootUI:IsInViewport() then
            self._viewportPersistentRootUI:AddToFrameLayer(uiIns, zOrder, layerType)
            logwarning('#[ 统一根节点RootUI Debug ]------------------------【AddToFrameRoot】UI BP：', uiIns.UISettings and uiIns.UISettings.BPPath or uiIns,'  当前在层级：', MapUILayer2Name[layerType],'  当前ZOrder：',zOrder)
        else
            logwarning('#[ 统一根节点RootUI Debug ]------------------------【AddToFrameRoot】UI BP：', uiIns.UISettings and uiIns.UISettings.BPPath or uiIns, "but viewportUI isnt valid, current gameflow:", __DebugOnly_GameFlowName(Facade.GameFlowManager:GetCurrentGameFlow()))
        end
    else
        if self:IsFrameRootAvailable() then
            self._viewportRootUI:AddToFrameLayer(uiIns, zOrder, layerType)
            logwarning('#[ 统一根节点RootUI Debug ]------------------------【AddToFrameRoot】UI BP：', uiIns.UISettings and uiIns.UISettings.BPPath or uiIns,'  当前在层级：', MapUILayer2Name[layerType],'  当前ZOrder：',zOrder)
        else
            logwarning('#[ 统一根节点RootUI Debug ]------------------------【AddToFrameRoot】UI BP：', uiIns.UISettings and uiIns.UISettings.BPPath or uiIns, "but viewportUI isnt valid, current gameflow:", __DebugOnly_GameFlowName(Facade.GameFlowManager:GetCurrentGameFlow()))
        end
    end
end

---@param uiIns ui
---@param zOrder number
---@param newLayerType EUILayer optional
function UIManager:SetZOrderInFrameRoot(uiIns, zOrder, newLayerType)
    if self:IsFrameRootAvailable() then
        if DEFAULT_CPLUS_LAYER and isvalid(self._gpUIManagerIns) then
            self._viewportPersistentRootUI:SetZOrderInFrameLayer(uiIns, zOrder, newLayerType)
        else
            self._viewportRootUI:SetZOrderInFrameLayer(uiIns, zOrder, newLayerType)
        end
        logwarning('#[ 统一根节点RootUI Debug ]------------------------【SetZOrderInFrameRoot】UI BP：', uiIns.UISettings.BPPath,'  当前在层级：', MapUILayer2Name[newLayerType],'  当前ZOrder：',zOrder)
    else
        logwarning('#[ 统一根节点RootUI Debug ]------------------------【AddToFrameRoot】UI BP：', uiIns.UISettings.BPPath, "but viewportUI isnt valid, current gameflow:", __DebugOnly_GameFlowName(Facade.GameFlowManager:GetCurrentGameFlow()))
    end
end

function UIManager:_InternalGetFrameRoot()
    local viewportRootUI
    if DEFAULT_CPLUS_LAYER and isvalid(self._gpUIManagerIns) then
        viewportRootUI = self._viewportPersistentRootUI
    else
        viewportRootUI = self._viewportRootUI
    end
    return viewportRootUI
end


function UIManager:IsFrameContainer(checkParent)
    local viewportRootUI = self:_InternalGetFrameRoot()
    if not hasdestroy(viewportRootUI) then
        return viewportRootUI:CheckIsFrameContainer(checkParent)
    else
        return false
    end
end

function UIManager:PlayFrameAnim(frameAniType)
    local viewportRootUI = self:_InternalGetFrameRoot()
    if not hasdestroy(viewportRootUI) then
        return viewportRootUI:PlayFrameAnim(frameAniType)
    else
        return nil
    end
end

function UIManager:GetFrameSafeZonePadding()
    local viewportRootUI = self:_InternalGetFrameRoot()
    if not hasdestroy(viewportRootUI) then
        return viewportRootUI:GetCurSafePadding()
    else
        return nil
    end
end

function UIManager:GetFrameRoot()
    local viewportRootUI = self:_InternalGetFrameRoot()
    if viewportRootUI and not hasdestroy(viewportRootUI) then
        return viewportRootUI
    else
        return nil
    end
end

function UIManager:IsFrameRootAvailable()
    if DEFAULT_CPLUS_LAYER and isvalid(self._gpUIManagerIns) then
        return not hasdestroy(self._viewportPersistentRootUI) and self._viewportPersistentRootUI:IsInViewport()
    else
        return not hasdestroy(self._viewportRootUI) and self._viewportRootUI:IsInViewport()
    end
end

---------------------------------------------------------------------------------
--- UI BP Ins Layer总体管理相关 Public API
---------------------------------------------------------------------------------
function UIManager:GetTopZOrderOfLayer(layerType)
    local topZOrder = 0
    if UILayer2ControllerPath[layerType] then
        topZOrder = self.mapLayer2Controller[layerType]:GetTopZOrder()
    else
        topZOrder=  self.defaultLayerController:GetTopZOrder(layerType)
    end
    logframe('[LayerControll Debug] UIManager:GetTopZOrderOfLayer(layerType)', layerType, topZOrder)
    return topZOrder
end

function UIManager:AddUIToLayer(uiIns, ...)
    local uiSettings = uiIns:GetUISettings()
    if uiSettings then
        local layerType = uiSettings.UILayer
        local layerController = self.mapLayer2Controller[layerType]

        if layerType == EUILayer.Sub then
            if uiIns.InitExtraData then
                trycall(uiIns.InitExtraData, uiIns, ...)
            end
            return
        end

        if UILayer2ControllerPath[layerType] then
            if layerType == EUILayer.Stack then
                ---@type StackLayerController
                layerController:PushUI(uiIns, ...)
            else
                layerController:AddUI(uiIns, ...)
            end
        else
            ---@type DefaultLayerController
            layerController:AddUIByLayerType(uiIns, layerType, ...)
        end
    else
        logerror("AddUIToLayer UISettings is nil, please check!!!!", UIName2ID.GetNameByID(uiIns.UINavID), debug.traceback())
    end
end

--------------------------------------------------------------------------
--- HUD层 查询类 Public API
--------------------------------------------------------------------------
function UIManager:GetHUDLayerController()
    return self.mapLayer2Controller[EUILayer.HUD]
end

--------------------------------------------------------------------------
--- Stack层 查询类 Public API
--------------------------------------------------------------------------
--- 获取栈Stack层级前一个在栈中隐藏的UI UINavID
function UIManager:GetPreStackUIId()
    return self.mapLayer2Controller[EUILayer.Stack]:GetPreStackUIId()
end

--- 获取栈Stack层级的当前UI
function UIManager:GetCurrentStackUI()
    return self.mapLayer2Controller[EUILayer.Stack]:GetCurrentView()
end

function UIManager:GetCurrentStackUIId()
    return self.mapLayer2Controller[EUILayer.Stack]:GetCurrentViewId()
end

--- 获取栈Stack层级上一个已经出栈UI UINavID
function UIManager:GetLastStackUIId()
    return self.mapLayer2Controller[EUILayer.Stack]:GetLastViewId()
end

--- 获取当前栈Stack层级的UI个数
function UIManager:GetStackUICount()
    return self.mapLayer2Controller[EUILayer.Stack]:GetStackUICount()
end

--- 打印当前全部栈UI列表
function UIManager:DumpStackUIList()
    return self.mapLayer2Controller[EUILayer.Stack]:DumpStackUIList()
end

--- 获取当前栈中指定uiNavId的UI
function UIManager:GetStackUIByUINavId(uiNavId, bIncludeDeactive)
    local uiSettings = UITable[uiNavId]
    assertlog(uiSettings.UILayer == EUILayer.Stack,'please use this api when you try to get [Stack] Layer ui')
    return self.mapLayer2Controller[EUILayer.Stack]:TryGetUIInsByNavID(uiNavId, bIncludeDeactive)
end

--- 获取指定Index的UI
function UIManager:GetStackUIInsByIndex(idx)
    return self.mapLayer2Controller[EUILayer.Stack]:TryGetUIInsByIndex(idx)
end
--------------------------------------------------------------------------
--- Stack 层 黑匣子存取正式启用 Public API
--------------------------------------------------------------------------
function UIManager:SaveBlackBoxForCurGameFlow()
    local gameFlowType = Facade.GameFlowManager:GetCurrentGameFlow()
    return self.mapLayer2Controller[EUILayer.Stack]:SaveBlackBoxForPlane(gameFlowType)
end

function UIManager:RecoverBlackBoxForCurGameFlow(bNeedTransition, fLoadFinCallback, caller)
    local gameFlowType = Facade.GameFlowManager:GetCurrentGameFlow()
    return self.mapLayer2Controller[EUILayer.Stack]:RecoverBlackBoxForPlane(gameFlowType, bNeedTransition, fLoadFinCallback, caller)
end

function UIManager:SaveBlackBoxWithTransition()
    logwarning('UIManager:PopAllUI(bIncludeBottom, bSilence, reasonStr)', true, true, "SaveBlackBoxWithTransition")
    self:SaveBlackBoxForCurGameFlow()
    self:CloseAllPopUI(false)
    self:PopAllUI(true, true)
end

function UIManager:RecoverBlackBoxWithTransition(bNeedTransition, fLoadFinCallback, caller)
    logwarning('UIManager:PopAllUI(bIncludeBottom, bSilence, reasonStr)', true, true, "RecoverBlackBoxWithTransition")
    bNeedTransition = setdefault(bNeedTransition, true)
    self:CloseAllPopUI(false)
    self:PopAllUI(true, true)
    self:RecoverBlackBoxForCurGameFlow(bNeedTransition, fLoadFinCallback, caller)
end
--------------------------------------------------------------------------
--- Root & BackRoot 层 查询类 Public API
--------------------------------------------------------------------------
function UIManager:GetRootUIByUINavId(uiNavId, bIncludeDeactive)
    local uiSettings = UITable[uiNavId]
    assertlog(uiSettings.UILayer == EUILayer.Root,'please use this api when you try to get [Root] Layer ui')
    return self.mapLayer2Controller[EUILayer.Root]:TryGetUIInsByNavID(uiNavId, bIncludeDeactive)
end

function UIManager:GetBackRootUIByUINavId(uiNavId, bIncludeDeactive)
    local uiSettings = UITable[uiNavId]
    assertlog(uiSettings.UILayer == EUILayer.BackRoot,'please use this api when you try to get [BackRoot] Layer ui')
    return self.mapLayer2Controller[EUILayer.BackRoot]:TryGetUIInsByNavID(uiNavId, bIncludeDeactive)
end

function UIManager:ManuelRefreshBackgroundUI(uiNavId, ...)
    local bgUI = self.mapLayer2Controller[EUILayer.BackRoot]:TryGetUIInsByNavID(UIName2ID.BACKROOTLAYER_BGUI)
    if not hasdestroy(bgUI) and bgUI.ManuelRefreshBgComp then
        logframe('ManuelRefreshBackgroundUI', uiNavId, ...)
        bgUI:ManuelRefreshBgComp(uiNavId, ...)
    end
end

function UIManager:RecoverStackWorldRendering()
    local bgUI = self.mapLayer2Controller[EUILayer.BackRoot]:TryGetUIInsByNavID(UIName2ID.BACKROOTLAYER_BGUI)
    if not hasdestroy(bgUI) and bgUI.ManuelRefreshBgComp then
        bgUI:RecoverStackWorldRendering()
    end
end

--------------------------------------------------------------------------
--- Stack层 操作类 Public API
--------------------------------------------------------------------------
--- 入栈一个不显示的栈UI（仅仅创建实例并加入列表，不进入视口）
function UIManager:PushSilenceUI(uiIns, ...)
    self.mapLayer2Controller[EUILayer.Stack]:PushSilenceUI(uiIns, ...)
end

--- 出栈一个栈UI
function UIManager:PopStackUI()
    self.mapLayer2Controller[EUILayer.Stack]:PopUI()
end

--- 出栈当前栈内所有UI
---@param bIncludeBottom boolean 是否最后一个ui也要关闭
---@param bSilence boolean 是否走无多余动画的流程（直接关闭）
function UIManager:PopAllUI(bIncludeBottom, bSilence, reasonStr)
    logwarning('UIManager:PopAllUI(bIncludeBottom, bSilence, reasonStr)', bIncludeBottom, bSilence, reasonStr)
    bIncludeBottom = setdefault(bIncludeBottom, false)
    bSilence = setdefault(bSilence, false)
    self.mapLayer2Controller[EUILayer.Stack]:PopToBottom(bIncludeBottom, bSilence)

    self.Events.evtStackUIPopAllUI:Invoke(bIncludeBottom, bSilence, reasonStr)
end

--- 出栈当前栈内指定数目的UI
---@param numToPop number 要出栈的个数
---@param bIncludeBottom boolean 如果超出长度,是否最后一个ui也要关闭
---@param bSilence boolean 是否走无多余动画的流程（直接关闭）
function UIManager:PopStackUIByNum(numToPop, bIncludeBottom, bSilence)
    self.mapLayer2Controller[EUILayer.Stack]:PopUIByNum(numToPop, bIncludeBottom, bSilence)
end

--- 弹出到指定栈UI（这个栈UI必须在栈里，否则结果为false）
---@param uiIns LuaUIBaseView
function UIManager:TryPopToStackUI(uiIns)
    return self.mapLayer2Controller[EUILayer.Stack]:TryPopToUIInStack(uiIns)
end

--- 恢复指定GameFlow Leave之前的栈UI列表
function UIManager:RecoverStackUI(gameFlowType, bNeedTransition)
    self.mapLayer2Controller[EUILayer.Stack]:RecoverBlackBoxForPlane(gameFlowType, bNeedTransition)
end

--- 保留Stack 底部UI, 针对只有栈UI的情况。默认不保留。gameflow leave时会自动重置
function UIManager:SetStackBottomKeepMode(bEnable, reason)
    reason = setdefault(reason, "Default")

    self._bStackBottomKeepMode = bEnable
    self._stackBottomKeepModeReason = reason

    logwarning("UIManager:SetStackBottomKeepMode", self._bStackBottomKeepMode, self._stackBottomKeepModeReason)
end

function UIManager:GetStackBottomKeepMode()
    return self._bStackBottomKeepMode
end

--------------------------------------------------------------------------
--- Pop层 操作类 Public API
--------------------------------------------------------------------------
function UIManager:CloseAllPopUI(bNeedVerify)
    bNeedVerify = setdefault(bNeedVerify, true)
    local layerController = self.mapLayer2Controller[EUILayer.Pop]
    local bResult = true
    if bNeedVerify and layerController.VerifyClosePopUIInOrder then
        bResult = layerController:VerifyClosePopUIInOrder()
        logwarning('[ UIManager ui复用打点 ]  CloseAllPopUI(bNeedVerify) VerifyClosePopUIInOrder bNeedVerify', bNeedVerify,' bResult', bResult)
    else
        layerController:Reset()
        logwarning('[ UIManager ui复用打点 ]  CloseAllPopUI(bNeedVerify) Reset bNeedVerify', bNeedVerify,' bResult', bResult)
    end
    return bResult
end

---------------------------------------------------------------------------------
--- UI BP Ins Layer清理相关 Private API
---------------------------------------------------------------------------------
--- new frame feature
--- 后续调用子LayerController各自Reset()
function UIManager:_ClearUIInsOfLayers()
    logwarning('[ UIManager ui复用打点 ]  关闭全局ui对象池 UIManager:_ClearUIInsOfLayers() start')
    self.bRePoolEnable = false
    -- self.mapLayer2Controller[EUILayer.HUD]:Reset()
    -- self.mapLayer2Controller[EUILayer.BackRoot]:Reset()
    if not self._bNeedJumpRollback then
        self:RollbackTransition()
    end
    self:ClearSubUIPack()
    self.mapLayer2Controller[EUILayer.Root]:Reset()
    self.mapLayer2Controller[EUILayer.Stack]:Reset()
    self.mapLayer2Controller[EUILayer.Pop]:Reset()
    self.defaultLayerController:Reset()
    self:_InternalClearPool()

    if not hasdestroy(self._viewportPersistentRootUI) then
        self._viewportPersistentRootUI:ClearFrameCanvas()
    end

    if not DEFAULT_CPLUS_LAYER then
        if not hasdestroy(self._viewportRootUI) then
            self._viewportRootUI:ClearFrameCanvas()
        end
    end

    self.bRePoolEnable = true
    logwarning('[ UIManager ui复用打点 ]  重启全局ui对象池 UIManager:_ClearUIInsOfLayers() end')
end

---------------------------------------------------------------------------------
--- UI BP Ins 对象池全清理相关 Private API
---------------------------------------------------------------------------------
function UIManager:_InternalClearPool()
    if self._deUniqueUIList and next(self._deUniqueUIList) then
        local length = #self._deUniqueUIList
        logerror("[MemoryDebug] _InternalClearPool count:", length)
        for i = -length, -1 do
            local idx = - i
            if idx > 0 and idx <= length then
                local uiIns = self._deUniqueUIList[idx]
                if not hasdestroy(uiIns) then
                    if issubclass(LAI.GetObjectClass(uiIns), UE.BaseUIView) then
                        --DO NOTHING
                    else
                        local bLuaPendingKill = true
                        self:FinalCloseUI(uiIns, true, bLuaPendingKill)
                        logframe("[ GameFlow Debug ] ******* Lua Clean All UI self._deUniqueUIList -------------", uiIns, " GetAllChildren FinalCloseUI", uiIns._cname)
                    end
                else
                    logwarning("[ GameFlow Debug ] ******* Lua Clean All UI self._deUniqueUIList -------------", uiIns," GetAllChildren FinalCloseUI but ui hasdestroy", hasdestroy(uiIns))
                end
            end
        end
    end
    self:_InternalClearList()
end

function UIManager:_InternalClearList()
    self._deUniqueUIList = {}

    for uiNavId, uiBundle in pairs(self._deMapUINavID2UIBundle) do
        uiBundle:Release()
    end
    self._deMapUINavID2UIBundle = {}
    self._deUIBundleOrderList = setmetatable({}, weakmeta_value)
    self._deSubUIBundleOrderList = setmetatable({}, weakmeta_value)

    self._mapUINavID2AliveCount = {}
end


function UIManager:ClearAliveCountList()
    if self._mapUINavID2ClearTimerHandle then
        for uiNavId, timerHandle in pairs(self._mapUINavID2ClearTimerHandle) do
            Timer.CancelDelay(timerHandle)
        end
    end
    self._mapUINavID2AliveCount = {}
    self._mapUINavID2TokenIDMap = {}
    self._mapUINavID2ClearTimerHandle = {}

    self._mapWeakOwerIns2bSubHistory = setmetatable({}, weakmeta_key)
    self._mapLoadingGid2SubHistory = {}
    if self._mapLoadingGid2ClearTimerHandle then
        for loadingGid, timerHandle in pairs(self._mapLoadingGid2ClearTimerHandle) do
            Timer.CancelDelay(timerHandle)
        end
    end
    self._mapLoadingGid2ClearTimerHandle = {}

    self._mapUITokenID2IsLuaPendingKill = {}
end

---------------------------------------------------------------------------------
--- UI BP Ins 对象池LRU定时 按照类型清理相关 Private API
---------------------------------------------------------------------------------
function UIManager:_InternalClearOldestPoolUI()
    local curLength = self._deUniqueUIList and #self._deUniqueUIList or 0
    if not self.bRePoolEnable then
        logwarning('[Low Memory Log - GameFlow Debug ] ******* Lua self.bRePoolEnable = false 当前对象池处理中，不可操作 -------------', '当前栈UI对象池内长度', curLength)
        return
    end

    if PLATFORM_IOS == 1 then
        local GameInstance = GetGameInstance()
        local PerfManager = UGPPerfManager.Get(GameInstance)
        if isvalid(PerfManager) then
            local warningLevel = PerfManager:GetMemoryWarningLevel()
            if warningLevel < 3 then
                logwarning('[Low Memory Log - GameFlow Debug ] ******* Lua 当前内存充足，不进行清理 -------------', '当前栈UI对象池内栈长度', curLength, 'warningLevel', warningLevel)
                return
            end
        end
    end

    self:_InternalClearOldestPoolUI_Stack()
    self:_InternalClearOldestPoolUI_UIBundle()
    self:_InternalClearOldestPoolUI_SubUIBundle()
end

function UIManager:_InternalClearOldestPoolUI_Stack()
    local curLength = self._deUniqueUIList and #self._deUniqueUIList or 0
    local maxKeepLength = 0
    local needClearNum = 1
    if curLength > maxKeepLength then
        local processCount = 0
        local processIdx = 1
        local maxProcessRound = 10
        while processCount < needClearNum and processIdx <= curLength do
            local oldestUIIns = self._deUniqueUIList[1]
            if oldestUIIns then
                if not hasdestroy(oldestUIIns) then
                    if issubclass(LAI.GetObjectClass(oldestUIIns), UE.BaseUIView) then
                        --DO NOTHING
                    else
                        table.removebyvalue(self._deUniqueUIList, oldestUIIns)
                        Facade.UIManager:FinalCloseUI(oldestUIIns, true)
                        processCount = processCount + 1
                        curLength = #self._deUniqueUIList
                        logwarning('[Low Memory Log - GameFlow Debug ] ******* Lua Clean _InternalClearOldestPoolUI self._deUniqueUIList -------------', oldestUIIns, oldestUIIns._cname, '轮回次数：', oldestUIIns.lifeTimes, '清理后,当前对象池内长度', curLength)
                    end
                else
                    table.removebyvalue(self._deUniqueUIList, oldestUIIns)
                    logwarning("[Low Memory Log - GameFlow Debug ] ******* Lua Clean _InternalClearOldestPoolUI self._deUniqueUIList -------------", oldestUIIns," GetAllChildren FinalCloseUI but ui hasdestroy", hasdestroy(oldestUIIns))
                end
                processIdx = processIdx + 1
                if processIdx >= maxProcessRound then
                    --- 防止死循环
                    break
                end
            end
        end
    else
        logwarning('[Low Memory Log - GameFlow Debug ] ******* Lua Clean _InternalClearOldestPoolUI self._deUniqueUIList -------------', '当前对象池内长度', curLength)
    end
end

function UIManager:_InternalClearOldestPoolUI_UIBundle()
    if self._deMapUINavID2UIBundle and next(self._deMapUINavID2UIBundle) then
        local curLength = self._deUIBundleOrderList and #self._deUIBundleOrderList or 0
        local maxKeepLength = 0
        local needClearNum = 1
        if curLength > maxKeepLength then
            local processCount = 0
            local processIdx = 1
            local maxProcessRound = 100
            while processCount < needClearNum and processIdx <= curLength do
                local oldestUIBundle = self._deUIBundleOrderList[1]
                if oldestUIBundle then
                    local uiNavId = oldestUIBundle:GetUINavID()
                    if hasdestroy(oldestUIBundle) then
                        table.removebyvalue(self._deUIBundleOrderList, oldestUIBundle)
                        if uiNavId then
                            self._deMapUINavID2UIBundle[uiNavId] = nil
                        end
                    else
                        if oldestUIBundle:GetUIListLength() > 0 then
                            --- 方法1 (不复用)
                            -- table.removebyvalue(self._deUIBundleOrderList, oldestUIBundle)
                            -- oldestUIBundle:Release()
                            -- if uiNavId then
                            --     self._deMapUINavID2UIBundle[uiNavId] = nil
                            -- end
            
                            --- 方法2 (复用)
                            table.removebyvalue(self._deUIBundleOrderList, oldestUIBundle)
                            oldestUIBundle:Reset()
                            table.insert(self._deUIBundleOrderList, oldestUIBundle)
                            processCount = processCount + 1
                        end
                    end
                    processIdx = processIdx + 1
                end
                if processIdx >= maxProcessRound then
                    --- 防止死循环
                    break
                end
            end
        end
    end
end

function UIManager:_InternalClearOldestPoolUI_SubUIBundle()
    if self._deMapUINavID2UIBundle and next(self._deMapUINavID2UIBundle) then
        local curSubLength = self._deSubUIBundleOrderList and #self._deSubUIBundleOrderList or 0
        if curSubLength > 0 then
            local needClearNum = math.floor(0.382 * curSubLength)
            if needClearNum >= 1 then
                local processCount = 0
                for idx, uiBundle in ipairs(self._deSubUIBundleOrderList) do
                    if processCount < needClearNum then
                        if uiBundle and not hasdestroy(uiBundle) and uiBundle:GetUIListLength() > 0 then
                            uiBundle:Reset()
                            processCount = processCount + 1
                        end
                    else
                        break
                    end
                end
                table.sort(self._deSubUIBundleOrderList, function (aUIBundle, bUIBundle)
                    if bUIBundle == aUIBundle or aUIBundle == nil or bUIBundle == nil then
                        return false
                    end
                    return aUIBundle.updateTimeStamp < bUIBundle.updateTimeStamp
                end)
            end
        end
    end
end

function UIManager:_CloseFrameRootUI()
    if DEFAULT_CPLUS_LAYER and isvalid(self._gpUIManagerIns) then
        --- 不依赖Lua销毁
        -- self._gpUIManagerIns:DeactivatePersistentRoot()
    else
        if self._viewportRootUI and not hasdestroy(self._viewportRootUI) then
            -- self._viewportRootUI:RemoveFromViewport()
        end
        logwarning('#[ 统一根节点RootUI Debug ]------------------------【_CloseLayers】 FrameRoot RemoveFromViewport')
        self._curWorkingState = EWorkingState.Terminal
        self._waitingAddQueue = {}
    end
end

function UIManager:_CloseLayerController()
    for _, layerType in pairs(EUILayer) do
        local controllerIns = self.mapLayer2Controller[layerType]
        controllerIns:Release()
    end
    self.mapLayer2Controller = {}
end

function UIManager:Destroy()
    if UGPUIManager then
        self._gpUIManagerIns = UGPUIManager.Get(GetGameInstance())
        if isvalid(self._gpUIManagerIns) then
            self._gpUIManagerIns.OnPersistentRootFinished:Remove(self._OnPersistentRootFinished, self)
        end
    end
    self._gpUIManagerIns = nil

    if ULuaSubsystem then
        if self._luaResolutionResizedHandle then
            local luaSubsystem = ULuaSubsystem.Get()
            if isvalid(luaSubsystem) then
                luaSubsystem.OnNotifyLuaResolutionResized:Remove(self._OnNotifyResolutionResized, self)
            end
            self._luaResolutionResizedHandle = nil
        end
    end

    if UDFMGameNotch then
        local DFMGameNotch = UDFMGameNotch.Get(GetGameInstance())
        if DFMGameNotch and DFMGameNotch:IsFoldDevice() and self.statusHandle then
            DFMGameNotch.OnFoldStatusChanged:Remove(self._OnNotifyFoldStatusChanged, self)
            self.statusHandle = nil
        end
    end
end


---------------------------------------------------------------------------------
--- UI BP Ins Layer状态监控导致清理相关 Private API
---------------------------------------------------------------------------------
function UIManager:_OnUILodLevelChanged(newLevel)
    logwarning('UIManager:_OnUILodLevelChanged(newLevel)', newLevel)
    -- self:SaveBlackBoxForCurGameFlow()
    -- Facade.UIManager:CloseAllPopUI(false)
    -- Facade.UIManager:PopAllUI(false)
    -- self:ClearUIResStub(false)
    -- self:ClearExUIResStub()
    -- collectgarbage("collect")
    -- collectgarbage("collect")
    -- Facade.UIManager:DisableInput(EInputChangeReason.BatchOperating)
    -- Facade.UIManager:CommitTransition(true, nil, nil, ETransitionChangeReason.BatchOperating)

    -- Timer.DelayCall(0.5, function(self)
    --     Facade.UIManager:EnableInput(EInputChangeReason.BatchOperating)
    --     Facade.UIManager:CommitTransition(false, nil, nil, ETransitionChangeReason.BatchOperating)
    --     self:RecoverBlackBoxForCurGameFlow()
    -- end, self)
end

function UIManager:GetIsLowMemoryState()
    return self._bLowMemoryCVarState == true
end

function UIManager:CycleSyncIsLowMemoryState()
    local UKismetSystemLibrary = import "KismetSystemLibrary"
    local GUILowMemoryEnable = UKismetSystemLibrary.GetConsoleVariableIntValue("r.GUILowMemoryEnable")
    logwarning("[Low Memory Log]  UIManager:CycleSyncIsLowMemoryState r.GUILowMemoryEnable", GUILowMemoryEnable)
    return GUILowMemoryEnable == 1
end

function UIManager:_OnUObjectNumWarning()
    -- logwarning("[Low Memory Log]  UIManager:_OnUObjectNumWarning")
    -- self:UltimateClearAllPoolWithRes(true)
end

function UIManager:_OnLowMemoryWarning()
    -- logwarning('[Low Memory Log - UIManager ui复用打点 ]  关闭全局ui对象池 UIManager:_OnLowMemoryWarning() UltimateClearAllPoolWithRes start')
    -- self:UltimateClearAllPoolWithRes(true)
    -- logwarning('[Low Memory Log - UIManager ui复用打点 ]  重启全局ui对象池 UIManager:_OnLowMemoryWarning() UltimateClearAllPoolWithRes end')
end

function UIManager:GetMemoryGuardedState()
    return self._bMemoryGuardedState == true
end

function UIManager:CheckMemoryGuardedState(bForceUpdate)
    bForceUpdate = setdefault(bForceUpdate, false)
    if LUA_CHECK_MEMORY_USAGE then
        if self._bLowMemoryCVarState then
            if self._bMemoryGuardedState == false or bForceUpdate == true then
                local availableMemoryMB = MemoryUtil.GetCurrentAvailableMemory()
                local config = MapApmLv2PerformanceConfig[self._apmLevel]
                local memoryGuardedMB_mutepool = config.memoryGuardedMB_mutepool or 0
                if availableMemoryMB < memoryGuardedMB_mutepool then
                    self._bMemoryGuardedState = true
                    logwarning('[Low Memory Log - UIManager ui复用打点 - MemoryGuard ]  内存不足, 进入内存保护状态')
                    self:InitGuardExitCheckTimer()
                else
                    self._bMemoryGuardedState = false
                    logwarning('[Low Memory Log - UIManager ui复用打点 - MemoryGuard ]  内存充足, 退出内存保护状态')
                end
            else
                --- 内存保护状态，不更新
            end
        else
            self._bMemoryGuardedState = false
        end
    else
        self._bMemoryGuardedState = false
    end
    return self._bMemoryGuardedState
end

function UIManager:GetMemoryGuardedUINum()
    local config = MapApmLv2PerformanceConfig[self._apmLevel]
    local memoryGuardedMaxNum = config.memoryGuardedMaxNum or 0
    return memoryGuardedMaxNum
end

function UIManager:GetMemoryGuardedMB_PopToBottom()
    local config = MapApmLv2PerformanceConfig[self._apmLevel]
    if config and config.memoryGuardedMB_poptobottom then
        local memoryGuardedMB_poptobottom = config.memoryGuardedMB_poptobottom or 0
        return memoryGuardedMB_poptobottom
    else
        return 0
    end
end

function UIManager:InitGuardExitCheckTimer()
    if self._guardCheckHandle then
        Timer.CancelDelay(self._guardCheckHandle)
        self._guardCheckHandle = nil
    end
    local config = MapApmLv2PerformanceConfig[self._apmLevel]
    local checkDuration = config.memoryGuardCheckDuration or DEFAULT_ONE_MINUTE
    self._guardCheckHandle = Timer.DelayCall(checkDuration, self._OnExitCheckMemoryGuard, self)
end

function UIManager:_OnExitCheckMemoryGuard()
    local bForceUpdate = true
    self:CheckMemoryGuardedState(bForceUpdate)
    self._guardCheckHandle = nil
end

---------------------------------------------------------------------------------
--- UI BP Ins Layer黑屏转场相关 Transition API
---------------------------------------------------------------------------------
function UIManager:SetEnableWorldRendering(bEnableRendering)
    logframe("UIManager:SetEnableWorldRendering", bEnableRendering)
    UGameplayStatics.SetEnableWorldRendering(GetWorld(), bEnableRendering)
end

function UIManager:SetIsJumpRollbackTransition(bNeedJumpRollback)
    self._bNeedJumpRollback = bNeedJumpRollback
end

function UIManager:GetIsJumpRollbackTransition()
    return self._bNeedJumpRollback
end

function UIManager:CommitTransition(bEnter, ...)
    -- logwarning('[ 转场 Debug ]----- UIManager:CommitTransition(bEnter)', bEnter, debug.traceback())
    local frameRootUI = self:GetFrameRoot()
    if frameRootUI and frameRootUI.CommitTransition then
        frameRootUI:CommitTransition(bEnter, ...)
    end
end

function UIManager:RollbackTransition(...)
    local frameRootUI = self:GetFrameRoot()
    if frameRootUI and frameRootUI.RollbackTransition then
        frameRootUI:RollbackTransition(...)
    end
end
---------------------------------------------------------------------------------
--- UI BP Ins Layer适配相关 ContentSuit API
---------------------------------------------------------------------------------
function UIManager:AddContentSuit(UINavID, customPadding)
    if UINavID == 0 then
        return
    end
    local uiSettings = UITable[UINavID]
    if uiSettings.UILayer == EUILayer.Stack then
        declare_if_nil(self._contentSuitDataList, UINavID, {})
        self._contentSuitDataList[UINavID] = {
            UINavID = UINavID,
            customPadding = customPadding,
        }
    else
        logerror('UIManager:CustomizeContentSuit(UINavID, customPadding) failed, the UILayer of uisettings must be EUILayer.Stack')
    end
end

function UIManager:RemoveContentSuit(UINavID)
    if UINavID == 0 then
        return
    end
    self._contentSuitDataList[UINavID] = nil
end


function UIManager:TryGetContentSuit(UINavID)
    if UINavID == 0 then
        return nil
    end
    return  self._contentSuitDataList[UINavID] or nil
end

return UIManager
