----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaFGameFlowManager)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class InGameController : Singleton
local InGameController = class("InGameController", Singleton)

local UInventoryManager = import "InventoryManager"
local UDFMGameplayGlobalDelegates = import "DFMGameplayGlobalDelegates"
local UGameplayStatics = import "GameplayStatics"
local ULuaSubsystem = import "LuaSubsystem"
local UInventoryUseItemManager = import "InventoryUseItemManager"
local UBasePlayerSettlementComponent = import "BasePlayerSettlementComponent"
local UGPGameplayStatics = import "GPGameplayStatics"


local EDFMGamePlayMode = import "EDFMGamePlayMode"
local EDFMGamePlaySubMode = import "EDFMGamePlaySubMode"
local memoize = require "DFM.YxFramework.Plugin.Performance.LuaMemoize"

local function log(...)
    loginfo("[InGameController]", ...)
end

function InGameController:Ctor()
    self._bInited = false
    self._luaSubSystem = ULuaSubsystem.Get()

    self:StartMemorize()

    self._luaSubSystem.OnLuaPreClearDynamicObjectsDelegate:Add(self._OnLuaPreClearDynamicObjects, self)
    self._luaSubSystem.OnLuaClientSeamlessTravelStart:Add(self._OnLuaClientSeamlessTravelStart, self)
    self._luaSubSystem.OnLuaClientSeamlessTravelEnd:Add(self._OnLuaClientSeamlessTravelEnd, self)
    self._luaSubSystem.OnActorProxyUpdate:Add(self._OnActorProxyUpdate, self)

    self._luaSubSystem.OnLuaUObjectNumWarningDelegate:Add(self._OnLuaUObjectNumWarning, self)
    self._luaSubSystem.OnLuaLowMemoryWarningDelegate:Add(self._OnLuaLowMemoryWarning, self)
    self._luaSubSystem.OnLuaPreGarbageCollect:Add(self._OnLuaPreGarbageCollect, self)
    
    UDFMGameplayGlobalDelegates.Get(GetGameInstance()).OnClientCharacterSpawnSuccess:Add(self._OnCharacterSpawnSuccess, self)
end


function InGameController:Destroy()
    self._luaSubSystem.OnLuaPreClearDynamicObjectsDelegate:Remove(self._OnLuaPreClearDynamicObjects, self)
    self._luaSubSystem.OnLuaClientSeamlessTravelStart:Remove(self._OnLuaClientSeamlessTravelStart, self)
    self._luaSubSystem.OnLuaClientSeamlessTravelEnd:Remove(self._OnLuaClientSeamlessTravelEnd, self)

    self._luaSubSystem.OnLuaUObjectNumWarningDelegate:Remove(self._OnLuaUObjectNumWarning, self)
    self._luaSubSystem.OnLuaLowMemoryWarningDelegate:Remove(self._OnLuaLowMemoryWarning, self)
    self._luaSubSystem.OnLuaPreGarbageCollect:Remove(self._OnLuaPreGarbageCollect, self)

    self._luaSubSystem.OnActorProxyUpdate:Remove(self._OnActorProxyUpdate, self)
    UDFMGameplayGlobalDelegates.Get(GetGameInstance()).OnClientCharacterSpawnSuccess:Remove(self._OnCharacterSpawnSuccess, self)
    
    self._bInited = false
end


function InGameController:_OnLuaPreGarbageCollect()
    log("InGameController:_OnLuaPreGarbageCollect")
    LuaGlobalEvents.evtLuaHandleUEPreGC:Invoke()
end

function InGameController:StartMemorize()
    if not self._bMemorized then
        log("StartMemorize")
        self._bMemorized = true

        self.cache = {}

        memoize.quick_memoize_no_args(self, "GetGPPlayerController", self.cache, true)
        memoize.quick_memoize_no_args(self, "GetGPCharacter", self.cache, true)
        memoize.quick_memoize_no_args(self, "GetGameState", self.cache, true)
        memoize.quick_memoize_no_args(self, "GetPlayerState", self.cache, true)
        memoize.quick_memoize_no_args(self, "GetGPCharacterInventoryMgr", self.cache, true)
        memoize.quick_memoize_no_args(self, "GetInputManager", self.cache, true)
        memoize.quick_memoize_no_args(self, "GetUseItemMgr", self.cache, true)
    end
end

function InGameController:CancelMemorize()
    if self._bMemorized then
        log("CancelMemorize")
        self._bMemorized = false

        self.cache = {}

        memoize.restore(self, "GetGPPlayerController")
        memoize.restore(self, "GetGPCharacter")
        memoize.restore(self, "GetGameState")
        memoize.restore(self, "GetPlayerState")
        memoize.restore(self, "GetGPCharacterInventoryMgr")
        memoize.restore(self, "GetInputManager")
        memoize.restore(self, "GetUseItemMgr")
    end
end

function InGameController:ClearMemorizeCache()
    log("ClearMemorizeCache")
    self.cache = {}
end

function InGameController:_OnActorProxyUpdate()
    log("_OnActorProxyUpdate")
    self:ClearMemorizeCache()
end

function InGameController:_OnLuaClientSeamlessTravelStart()
    log("_OnLuaClientSeamlessTravelStart")
    self:CancelMemorize()
end

function InGameController:_OnLuaClientSeamlessTravelEnd()
    log("_OnLuaClientSeamlessTravelEnd")
    self:StartMemorize()
end

-- function InGameController:UpdateMemoizeCacheWhileSeamless(pc)
--     assert(isvalid(pc))

--     memoize.force_update_memoize_cache(self, "GetGPPlayerController", self.cache, pc)

--     local pawn = pc:GetPawn()
--     assert(isvalid(pawn))
--     memoize.force_update_memoize_cache(self, "GetGPCharacter", self.cache, pawn)
-- end

---无缝入局(断线重连)删除Actor之前
function InGameController:_OnLuaPreClearDynamicObjects()
    log("InGameController:_OnLuaPreClearDynamicObjects")
    LuaGlobalEvents.evtInGameControllerLuaPreClearDynamicObjects:Invoke()
end

function InGameController:_OnLuaUObjectNumWarning()
    log("InGameController:_OnLuaUObjectNumWarning")
    LuaGlobalEvents.evtUObjectNumWarning:Invoke()
end

function InGameController:_OnLuaLowMemoryWarning()
    log("InGameController:_OnLuaLowMemoryWarning")
    LuaGlobalEvents.evtLowMemoryWarning:Invoke()
end

function InGameController:_OnCharacterSpawnSuccess(character)
    log("InGameController:_OnCharacterSpawnSuccess", character)
    LuaGlobalEvents.evtInGameControllerInited:Invoke()
end

---获取GameState,有2种情况下返回不可用. >1.进入游戏时还没有replicate到Client。 >2:局内静默断线重连被销毁,还没有重建。
---@return AGameState|nil
function InGameController:GetGameState()
    local gameState = self._luaSubSystem:LuaGetGameState(nil)
    return gameState
end

function InGameController:GetGPPlayerController()
    return self._luaSubSystem:LuaGetFirstPlayerController(nil)
end


function InGameController:GetPlayerState()
    local playerState = self._luaSubSystem:LuaGetFirstPlayerState(nil)
    return playerState
end

function InGameController:GetGPCharacter()
    return self._luaSubSystem:LuaGetFirstCharacter(nil)
end


---获取当前模式
---@return EDFMGamePlayMode|nil
function InGameController:GetGamePlayerMode()
    local gameState = self:GetGameState()
    if gameState then
        return gameState.DFMGamePlayerMode
    end

    return nil
end


---获取当前子模式
---@return EDFMGamePlayerSubMode|nil
function InGameController:GetGamePlayerSubMode()
    local gameState = self:GetGameState()
    if gameState then
        return gameState.DFMGamePlayerSubMode
    end

    return nil
end

function InGameController:GetGPCharacterInventoryMgr()
    local character = self:GetGPCharacter()
    if character then
        return UInventoryManager.Get(character)
    end
end

function InGameController:GetPlayerSettlementComponent()
    local PlayerController = self:GetGPPlayerController()
    if isvalid(PlayerController) then
        return PlayerController:GetComponentByClass(UBasePlayerSettlementComponent)
    end
end

function InGameController:IsTDMMode()
    local gamePlayerMode = self:GetGamePlayerMode()
    return gamePlayerMode and (gamePlayerMode == EDFMGamePlayMode.GamePlayMode_BattleField
            or gamePlayerMode == EDFMGamePlayMode.GamePlayMode_Breakthrough
            or gamePlayerMode == EDFMGamePlayMode.GamePlayMode_Conquest)
end

function InGameController:IsMPMode()
    local gamePlayerMode = self:GetGamePlayerMode()
    return gamePlayerMode and (gamePlayerMode == EDFMGamePlayMode.GamePlayMode_BattleField
    or gamePlayerMode == EDFMGamePlayMode.GamePlayMode_Breakthrough)
end

function InGameController:IsConquestMode()
    local gamePlayerMode = self:GetGamePlayerMode()
    return gamePlayerMode and gamePlayerMode == EDFMGamePlayMode.GamePlayMode_Conquest
end

function InGameController:IsSOLMode()
    local gamePlayerMode = self:GetGamePlayerMode()
    return gamePlayerMode and gamePlayerMode == EDFMGamePlayMode.GamePlayMode_SOL
end

function InGameController:IsIntro()
    local gamePlayerMode = self:GetGamePlayerMode()
    return gamePlayerMode and gamePlayerMode == EDFMGamePlayMode.GamePlayMode_Intro
end

function InGameController:IsBattleField()
    local gamePlayerMode = self:GetGamePlayerMode()
    return gamePlayerMode and gamePlayerMode == EDFMGamePlayMode.GamePlayMode_BattleField
end

function InGameController:IsBreakThrough()
    local gamePlayerMode = self:GetGamePlayerMode()
    return gamePlayerMode and gamePlayerMode == EDFMGamePlayMode.GamePlayMode_Breakthrough
end

function InGameController:IsRaid()
    local gamePlayerMode = self:GetGamePlayerMode()
    return gamePlayerMode and gamePlayerMode == EDFMGamePlayMode.GamePlayMode_Raid
end

function InGameController:IsArena()
    local gamePlayerMode = self:GetGamePlayerSubMode()
    return gamePlayerMode and gamePlayerMode == EDFMGamePlaySubMode.GamePlaySubMode_Arena
end

function InGameController:IsCaptureFlag()
    local gamePlayerSubMode = self:GetGamePlayerSubMode()
    return gamePlayerSubMode and gamePlayerSubMode == EDFMGamePlaySubMode.GamePlaySubMode_CaptureFlag
end

function InGameController:IsCommander()
    local gamePlayerSubMode = self:GetGamePlayerSubMode()
    return gamePlayerSubMode and gamePlayerSubMode == EDFMGamePlaySubMode.GamePlaySubMode_Commander
end

function InGameController:GetInputManager()
    local pc = self:GetGPPlayerController()
    if pc then
        pc:GetInputManager()
    end

    return nil
end

function InGameController:GetUseItemMgr()
    local character = self:GetGPCharacter()
    if character then
        return UInventoryUseItemManager.Get(character)
    end

    return nil
end

function InGameController:GetIsInited()
    -- return self._bInited
    return true
end

function InGameController:GetCurrentMapId()
    local gameState = self:GetGameState()
    if gameState and gameState.GetMapId then
        return gameState:GetMapId()
    end

    return 0
end

-- 根据当前的WorldName来判断GameMode，适用于入局时局内关卡已经在加载但是GameState还没同步下来时
function InGameController:GetGameModeByWorldName()
    local worldName = UGPGameplayStatics.GetWorldName(GetWorld())
    if worldName ~= "" then
        local mapConfig = Facade.TableManager:GetTable("MapConfig")
        if mapConfig then
            for _, row in pairs(mapConfig) do
                if row.LevelName == worldName then
                    local matchDataConfig = Facade.TableManager:GetTable("MatchModeDataConfig")
                    if matchDataConfig then
                        for _, v in pairs(matchDataConfig) do
                            if v.MapID == row.MapID then
                                return v.GameMode
                            end
                        end
                    end
                    break
                end
            end
        end
    end
    return 0
end

-- 是否在胜者为王对局内
function InGameController:IsVictoryUniteMatch()
    local gameState = self:GetGameState()
    return isvalid(gameState) and gameState.MatchInfo.bIsVictoryUniteMatch or false
end

return InGameController