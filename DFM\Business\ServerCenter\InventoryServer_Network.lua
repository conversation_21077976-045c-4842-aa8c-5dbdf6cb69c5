----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaSInventory)
----- LOG FUNCTION AUTO GENERATE END -----------



local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local ItemBaseTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemBaseTool"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local InventoryServerSkinCSQueue = require "DFM.Business.ServerCenter.InventoryServerSkinPacker.InventoryServerSkinCSQueue"

---@class InventoryServer : ServerBase
local InventoryServer = {}

InventoryServer.PRINT_DEBUG_INFO = IsInEditor()
InventoryServer.PRELOAD_ITEM_ICON = true
InventoryServer.PRELOAD_ITEM_ICON_ONLY_FIRST_PAGE = true

local function log(...)
    loginfo("[InventoryServer]", ...)
end

function InventoryServer:Ctor()
    ---@type table<number, pb_CSDepositEquipPropReq>
    self._waitingRequests = {}

    self.fetchCount = 0
    self.fetchTarget = 0
    self.bNotifyReissue = false -- 是否预告补发提示
    self._hasFetchSettlementItems = false -- 是否发送了不触发队友归还的拉仓库请求，用于保底触发队友归还
end

function InventoryServer:FetchAllItems()
    local fetchReason = EGetPropInvokeEvent.Login
    if ItemOperaTool.bInSettlement then -- 因为结算时被断线重连后，如果fetchReason为Login，则直接归还队友绑定道具了
        fetchReason = EGetPropInvokeEvent.Settlement
    end
    if not ItemOperaTool.CheckRunWarehouseLogic() then
        return
    end

    -- self:_FetchAllItems_Start()

    self:_FetchAllItems_1(fetchReason)
    -- （可能）不能并行，需要串行拉取
    -- self:_FetchAllItems_2(fetchReason)
end

function InventoryServer:_FetchAllItems_Start()
    log("_FetchAllItems_Start")

    self:ProcessSkinDependsCSReq(InventoryServerSkinCSQueue.IDs.CSDepositGetProps)

    self.fetchCount = 0
    self.fetchTarget = 2

    -- self:ClearSlotGroup(ESlotGroup.Player)
    self.Items[ESlotGroup.Player] = {}
    self._mapGid2Items[ESlotGroup.Player] = {}
    self._allRecParts[ESlotGroup.Player] = {}
    self._allWeaponParts[ESlotGroup.Player] = {}
    self._allDisabledParts = {}
    self.KeyMapping = {}

    -- 清空扩容箱道具
    for extSlotType, slotType in pairs(self._extMapping) do
        local itemSlot = self:GetSlot(slotType)
        local extSlot = self:GetSlot(extSlotType)
        if itemSlot then
            itemSlot:ResetSpaceStructure()
            itemSlot:ClearItems()
        end
        if extSlot then
            extSlot:ResetSpaceStructure()
            extSlot:ClearItems()
        end
    end

    self._extMapping = {}    -- Map Ext Id To Container Id

    -- 先清空每一个Slot
    for _, slotType in pairs(EActualSlotType) do
        local itemSlot = self:GetSlot(slotType)
        if itemSlot then
            itemSlot:ResetSpaceStructure()
            itemSlot:ClearItems()
        end
    end
end

function InventoryServer:_FetchAllItems_Start2()
    -- 清空扩容箱容器道具和扩容箱槽道具
    for extSlotType, slotType in pairs(self._extMapping) do
        local itemSlot = self:GetSlot(slotType)
        local extSlot = self:GetSlot(extSlotType)
        if itemSlot then
            itemSlot:ResetSpaceStructure()
            itemSlot:ClearItems()
        end
        if extSlot then
            extSlot:ResetSpaceStructure()
            extSlot:ClearItems()
        end
    end

    self._extMapping = {}    -- Map Ext Id To Container Id
end

function InventoryServer:_FetchAllItems_1(fetchReason)
    log("_FetchAllItems_1", fetchReason)

    self._hasFetchSettlementItems = (fetchReason == EGetPropInvokeEvent.Settlement)

    ---@param res pb_CSDepositGetPropsRes
    local fOnCSDepositGetPropsRes = function(res)
        log("_FetchAllItems_1 Res")
        if res.result ~= 0 then
            local tips = string.format(ServerTipCode.PullInvDataFailedErrCode, res.result)
            LuaGlobalEvents.evtServerShowTip:Invoke(tips)
            return
        end
        -- 回包时再清理一次
        self:_FetchAllItems_Start()

        self._extOrder = res.extension_pos_order    -- Ext slot order
    
        local gridPages = res.grid_pages
        local equippedProps = res.equiped_props
        local meleeWeapons = res.melee_weapons
        local permissions = res.safe_and_card_pack_permission

        self.maxExtendSlotNum = res.upperlimit_extension_num
        self.usedExtendSlotNum = res.cur_extension_num
        self.curMaxExtSlotNum = res.max_extension_num
        self.availableExtendSlotNum = res.max_extension_num - res.cur_extension_num
        self.lockExtendSlotNum = self.maxExtendSlotNum - self.usedExtendSlotNum - self.availableExtendSlotNum

        local newReissueRoomId = res.notify_reissue_latest_roomid
        log("_FetchAllItems_1", newReissueRoomId)
        if newReissueRoomId > 0 then
            local lastReissueRoomId = Facade.ConfigManager:GetUserInt("LAST_REISSUE_ROOM_ID", 0)
            log(string.format("_FetchAllItems_1 Res reissue trigger lastRoomId=%d newRoomId=%d", lastReissueRoomId, newReissueRoomId))
            
            if newReissueRoomId ~= lastReissueRoomId then
                Facade.ConfigManager:SetUserInt("LAST_REISSUE_ROOM_ID", newReissueRoomId)
                self:SetNotifyReissue()
            end
        else
            self.bNotifyReissue = false
        end

        self:_ProcessUpdateWeaponSkinSetups(res.weapon_skin_setup, true)

        -- Sort config
        self:_InitSortConfig(res.sort_config)

        -- Auto Sort
        self:_LocalSetAutoSort(res.auto_sort)
    
        self:_FetchServerProcessEquipProps(equippedProps)
        self:_FetchServerProcessGridProps(gridPages)
        self:_FetchServerProcessMeleeWeapons(meleeWeapons)
        self:_FetchServerProcessSafeBoxAndKeyChain(permissions)

        -- 金钱
        self:_FetchServerProcessCurrencyNum(res.currency, res.currency_limit)

        self.Events.evtRecoveryAmountUpdate:Invoke(res.recovery_amount)

        self:_FetchAllItems_MarkStepFinish()
        -- Start next step
        self:_FetchAllItems_2(fetchReason)
    end

    ---@type pb_CSDepositGetPropsReq
    local req = pb.CSDepositGetPropsReq:New()
    req.invoke_event = fetchReason
    req:Request(fOnCSDepositGetPropsRes, {bEnableHighFrequency = true})
end

function InventoryServer:_FetchAllItems_2(fetchReason)
    log("_FetchAllItems_2")

    ---@param res pb_CSDepositGetExtensionPropsRes
    local fOnCSDepositGetExtensionPropsRes = function(res)
        log("_FetchAllItems_2 Res")

        if res.result ~= 0 then
            local tips = string.format(ServerTipCode.PullInvDataFailedErrCode, res.result)
            LuaGlobalEvents.evtServerShowTip:Invoke(tips)
            return
        end
        -- 清理一次扩容箱道具
        self:_FetchAllItems_Start2()
        -- 初始化vip扩容箱槽假数据
        self:_FetchFakeVipExtensionProps()
        self:_FetchServerProcessEquipProps(res.extension_slots)
        self:_FetchServerProcessGridProps(res.in_extension_pages)

        self:_FetchAllItems_MarkStepFinish()
        self:FetchRapairTypeInfo()
    end

    ---@type pb_CSDepositGetExtensionPropsReq
    local req = pb.CSDepositGetExtensionPropsReq:New()
    req.invoke_event = fetchReason
    req:Request(fOnCSDepositGetExtensionPropsRes, {bEnableHighFrequency = true})
end

function InventoryServer:_FetchAllItems_MarkStepFinish()
    self.fetchCount = self.fetchCount + 1
    if self.fetchCount >= self.fetchTarget then
        self:_FetchAllItems_Finished()
    end
end

function InventoryServer:FetchRapairTypeInfo()
    ---@param res CSSafehouseFuncIsUnlockRes
    local function fOnUnlockRepair(res)
        if res.result == 0 then
            if res.params and #res.params > 0 then
                for index, params in pairs(res.params) do
                    if params.unlock_id == SystemUnlockID.SU_DepositRepair then
                        self._bUnlockIntermediateRepair = true
                    end
                end
            end
        end
    end
    local req = pb.CSSafehouseFuncIsUnlockReq:New()
    req.unlock_id = SystemUnlockID.SU_DepositRepair
    req:Request(fOnUnlockRepair)
end

function InventoryServer:_FetchAllItems_Finished()
    log("_FetchAllItems_Finished")

    self.Events.evtInventoryFetchFinished:Invoke()
    self:CalculateAllCarryItemsWeight(ESlotGroup.Player)
    --Server.GunsmithServer:InvokeWeaponinfoCounterEvent_CSDepositGetPropsRes()
    self:ProcessSkinDependsCSRes(InventoryServerSkinCSQueue.IDs.CSDepositGetProps)
end

---@param equipProps pb_EquipPosition[]
function InventoryServer:_FetchServerProcessEquipProps(equipProps)
    table.sort(
        equipProps,
        function(a, b)
            return a.position < b.position
        end
    )

    for i, equipPos in pairs(equipProps) do
        local itemSlot = self:GetSlot(equipPos.position)
        if itemSlot ~= nil then
            local position = equipPos.position
            local equipmentId = equipPos.src_prop_id
            if equipmentId and equipmentId > 0 then
                if equipmentId > 0 then
                    local spaceData = ItemBaseTool.GetSpaceDataByEquipmentId(equipmentId)
                    if spaceData then
                        itemSlot:InitMultiSpaceStructureByGrids(spaceData)
                    else
                        -- logerror(string.format("InventoryServer:_FetchServerProcessEquipProps not space config found for item(id=%d)", equipmentId))
                        itemSlot:InitSpaceStructureByPb(equipPos.grid_space)
                    end
                else
                    itemSlot:InitSpaceStructureByPb(equipPos.grid_space)
                end
            elseif position == ESlotType.Pocket then
                itemSlot:InitMultiSpaceStructureByGrids(ItemBaseTool.GetPocketSpaceData())
            else
                itemSlot:InitSpaceStructureByPb(equipPos.grid_space)
            end
            
            for j, prop in pairs(equipPos.load_props) do
                if itemSlot:IsExtendSlot() then
                    itemSlot:ClearItems()
                end

                local newItem = self:_InternalCreateNewItem(prop, itemSlot)

                if newItem then
                    self:_PreloadItemIcon(newItem)
                else
                    logerror("[FetchAllItems] \t Not found prop (id =", prop.id, ", position =", equipPos.position, "), create Item failed !")
                end
            end

            if itemSlot:IsExtendSlot() and itemSlot:GetEquipItem() then
                local depositId = ItemHelperTool.GetDepositIdByExtId(position)
                self._extMapping[position] = depositId
            end
        end
    end
    self:_SortExtensionBox()
end

function InventoryServer:_SortExtensionBox()
    local extOrderNum = 0
    local extMappingNum = 0
    for _, _ in pairs(self._extOrder) do
        extOrderNum = extOrderNum + 1
    end
    for _, _ in pairs(self._extMapping) do
        extMappingNum = extMappingNum + 1
    end
    if extOrderNum ~= extMappingNum and extMappingNum > 0 then
        loginfo("extOrder need rebuild")
        local idx = 1
        for position, value in pairs(self._extMapping) do
            self._extOrder[idx] = position
            idx = idx + 1
        end
        Server.InventoryServer:DoChangeSortConfig()
    else
        loginfo("extOrder no need rebuild")
    end
end

---@param equipProps pb_EquipPosition[]
function InventoryServer:_FetchServerProcessMeleeWeapons(meleeWeapons)
    self._meleeWeapons = {}
    for i, meleeWeaponProp in pairs(meleeWeapons) do
        if ItemHelperTool.GetMainTypeById(meleeWeaponProp.id) == EItemType.Receiver then
            local newItem = ItemBase:New(meleeWeaponProp.id, meleeWeaponProp.num, meleeWeaponProp.gid)
            newItem:SetRawPropInfo(meleeWeaponProp)
            declare_if_nil(self._meleeWeapons, meleeWeaponProp.id, {})
            table.insert(self._meleeWeapons[meleeWeaponProp.id], newItem)
        end
    end
end

function InventoryServer:_FetchServerProcessSafeBoxAndKeyChain(permissions)
    self._permissionItems = {}
    for i, permissionsProp in pairs(permissions) do
        local newItem = ItemBase:New(permissionsProp.id)
        newItem:SetRawPropInfo(permissionsProp)
        table.insert(self._permissionItems, newItem)
        loginfo("[FetchPermissionItems] PermissionsMember: ", permissionsProp.id," expiredTime:" .. permissionsProp.expire_timestamp)
    end
end

function InventoryServer:_ProcessMeleeWeaponItemChange(propChange)
    local propInfo = propChange.prop
    local item = ItemBase:New(propInfo.id, propInfo.num, propInfo.gid)
    item:SetRawPropInfo(propInfo)
    local weaponFeature = item:GetFeature(EFeatureType.Weapon)
    if weaponFeature and weaponFeature:IsMeleeWeapon() then
        if propChange.change_type == PropChangeType.Add then
            logerror("InventoryServer:_ProcessMeleeWeaponItemChange 近战武器 【添加】", item.name)
            if not self._meleeWeapons[item.id] then
                declare_if_nil(self._meleeWeapons, item.id, {})
                table.insert(self._meleeWeapons[item.id], item)
            end
        elseif propChange.change_type == PropChangeType.Del then
            logerror("InventoryServer:_ProcessMeleeWeaponItemChange 近战武器 【移除】", item.name)
            if self._meleeWeapons[item.id] then
                self._meleeWeapons[item.id] = nil
            end
        end
    end
end

function InventoryServer:GetSOLMeleeWeaponItemsIterator()
    local meleeWeapons = self._meleeWeapons or {}

    local totalIndex = 0
    local keys = {}
    for id, itemsOfId in pairs(meleeWeapons) do
        table.insert(keys, id)
    end
    local length = #keys

    local mainIndex = 0
    local subIndex = 0
    local subLength = 0
    
    return function()
        subIndex = subIndex + 1
        if subIndex > subLength then
            subIndex = 1
            subLength = 0
            mainIndex = mainIndex + 1
        end
        
        if mainIndex <= length then
            local itemsOfId = meleeWeapons[keys[mainIndex]]
            subLength = #itemsOfId
            
            if subIndex <= subLength then
                totalIndex = totalIndex + 1
                return totalIndex, itemsOfId[subIndex]
            end
        end
    end
end

---@param gridProps pb_PropGridPage[]
function InventoryServer:_FetchServerProcessGridProps(gridProps)
    for i, propGrid in ipairs(gridProps) do
        local position = propGrid.grid_page_id
        local itemSlot = self:GetSlot(position)
        itemSlot:InitSingleSpaceStructure(propGrid.grid_length, propGrid.grid_width)
        
        itemSlot:SetLabel(propGrid.tag)

        if itemSlot.SlotType > ESlotType.MainContainer and itemSlot.SlotType < ESlotType.ContainerEnd then
            local refExtId = self:GetExtIdByDepositId(itemSlot.SlotType)
            local refExtSlot = self:GetSlot(refExtId)
            self:UpdateDepositConfigByExtSlot(refExtSlot)
        end

        -- loginfo(string.format("[FetchAllItems] Container Slot: %s(%d), length = %d, height = %d",
        --     itemSlot:GetSlotName(), itemSlot.SlotType, itemSlot.Length, itemSlot.Width))
        
        local i = 0
        for j, prop in pairs(propGrid.props) do
            local targetSlot = self:GetSlot(position)
            local newItem = self:_InternalCreateNewItem(prop, targetSlot)

            if newItem then
                self:_PreloadItemIcon(newItem)
            else
                logerror("[FetchAllItems] \t Not found prop (id =", prop.id, ", pageid =", propGrid.grid_page_id, "), create Item failed !")
            end
        end
    end
end

function InventoryServer:_FetchFakeVipExtensionProps()
    -- 由于初始话的是虚假的扩容箱，所以需要有可用的position，可供真实的数据去覆盖
    -- 1. 构建假的vip扩容箱数据
    -- 预计重构一版
    local vipExtID = 20090050001
    local vipExtItem = ItemBase:New(vipExtID)
    local position = ESlotType.ExtensionVIP
    local targetSlot = self:GetSlot(position)
    vipExtItem:AddToSlot(targetSlot, false)
    self:UpdateDepositConfigByExtSlot(targetSlot)
    local vipContainerSlot = self:GetSlot(ESlotType.VipDepositContainer)
    local ExtensionDesc = ItemConfigTool.GetExtentionBoxDescRowById(vipExtID)
    if ExtensionDesc then
        vipContainerSlot:InitSingleSpaceStructure(ExtensionDesc.GainPosLength, ExtensionDesc.GainPosWidth)
    else
        -- 默认
        vipContainerSlot:InitSingleSpaceStructure(9, 12)
    end
end

function InventoryServer:CSDepositBuySafeAndCardPackPermReq(item)
    local req = pb.CSDepositBuySafeAndCardPackPermReq:New()
    req.perm_propID = item.id

    ---@param res CSDepositBuySafeAndCardPackPermRes
    local function fOnCSDepositBuySafeAndCardPackPermRes(res)
        if res.result == 0 then
            LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.RenewalSuccess)
            -- ntf时序问题延迟0.1秒刷新
            Timer.DelayCall(0.1, function ()
                self.Events.evtRenewalSuccess:Invoke()
            end)
        end
    end

    req:Request(fOnCSDepositBuySafeAndCardPackPermRes)
end

-----------------------------------------------------------------------
--region 网络同步相关

-- 扩容道具的装卸，现在会有单独的接口去处理
---@param moveCmd ItemMoveCmd
function InventoryServer:ProcessExtItemMoveCmd(moveCmd)
    local req = pb.CSDepositOperateExtensionBoxReq:New()
    req.prop_id = moveCmd.item.id
    req.prop_gid = moveCmd.item.gid
    req.src_pos = moveCmd.originLoc.ItemSlot.SlotType

    local targetSlot = moveCmd.targetLoc.ItemSlot
    req.dest_pos = targetSlot.SlotType
    if targetSlot:IsExtendSlot() then
        -- 安装（包括替换）
        req.operation = ExtensionOpType.OpreateEquip
    else
        -- 卸下
        req.operation = ExtensionOpType.OpreateUninstall
    end
    
    ---@param res pb_CSDepositOperateExtensionBoxRes
    local fCallBack = function(res)
        -- Preprocess
        -- self:_PreprocessCSDepositEquipRes(res)
        if res.result == 0 then
            for _, change in ipairs(res.changes.prop_changes) do
                local target = change.dest.pos
                if target > ESlotType.ExtensionStart and target < ESlotType.ExtensionEnd then
                    -- 安装扩容箱
                    local id = change.prop.id
                    local config = ItemConfigTool.GetItemConfigById(id)
                    local tip = string.format(ServerTipCode.EquipExtItemSuccess, config.name)
                    LuaGlobalEvents.evtServerShowTip:Invoke(tip)
                end
            end

            self._extOrder = res.extension_pos_order    -- Ext slot order
    
            -- self:ProcessPbDataChange(res.changes, true)
        -- elseif res.result == 14032 then
        --     local tipStr = ServerTipCode.ReplacePermissionItemFailed
        --     LuaGlobalEvents.evtServerShowTip:Invoke(tipStr)
        end

        self.Events.evtExtensionBoxRes:Invoke(res)
    end

    req:Request(fCallBack, {bShowErrTip = false})
    return true
end

-- 结算中，虚拟空间请求处理剩下物品
function InventoryServer:ProcessClearCarryOutTempProps()
    local req = pb.CSDepositClearCarryOutTempPropsReq:New()
    ---@param res pb_DataChange
    local fCallBack = function(res)
        if res.result == 0 then
            loginfo("ProcessClearCarryOutTempPropsRes：",res.changes)
        end
    end
    req:Request(fCallBack)
end


---@param moveCmd ItemMoveCmd
function InventoryServer:ProcessItemMoveCmd(moveCmd)
    ---@type pb_EquipPropCommand
    local cmd = {}
    cmd.sourceCmd = moveCmd
    cmd.prop_id = moveCmd.item.id
    cmd.prop_gid = moveCmd.item.instanceId
    -- cmd.src_pos = moveCmd.originLoc.ItemSlot.SlotType
    if moveCmd.item.rawPropInfo and moveCmd.item.rawPropInfo.loc and moveCmd.item.rawPropInfo.loc.pos == eEquipPosition.CarryOutPropsPos then
        cmd.src_pos = eEquipPosition.CarryOutPropsPos
    else
        cmd.src_pos = moveCmd.originLoc.ItemSlot.SlotType
    end
    cmd.target_pos = moveCmd.targetLoc.ItemSlot.SlotType
    if not moveCmd.targetLoc:IsAnywhere() then
        cmd.spec_loc = moveCmd.targetLoc:ToPbLocation()
    end
    cmd.num = moveCmd.moveNum

    if moveCmd.moveParams.bAutoMerge ~= nil then
        cmd.disable_auto_merge = not moveCmd.moveParams.bAutoMerge
    else
        cmd.disable_auto_merge = true
    end
    -- 是否自动放置胸挂 > 背包 > 安全箱
    if moveCmd.moveParams.bSendDrugToChesthang ~= nil then
        cmd.send_drug_to_chesthang = moveCmd.moveParams.bSendDrugToChesthang
    else
        cmd.send_drug_to_chesthang =  false
    end

    -- 自动排序模式修正
    if Server.InventoryServer:GetAutoSort() and cmd.spec_loc then
        cmd.spec_loc.space_id = 0
        -- 自动排序下，不允许内部移动
        if cmd.src_pos == cmd.target_pos then
            return false
        end
    end

    local targetItemsNum = #moveCmd.targetItems
    if targetItemsNum > 0 and not moveCmd.moveParams.ignoreLocationUsed then
        if targetItemsNum == 1 then
            local targetItem = moveCmd.targetItems[1]
            cmd.target_prop_id = targetItem.id
            cmd.target_prop_gid = targetItem.instanceId
        else
            local ids = {}
            local gids = {}
            for _, targetItem in ipairs(moveCmd.targetItems) do
                table.insert(ids, targetItem.id)
                table.insert(gids, targetItem.gid)
            end
            cmd.multi_target_prop_ids = ids
            cmd.multi_target_prop_gids = gids
        end
    end

    Server.InventoryServer:AddItemMoveCmd(cmd)

    if moveCmd:ProcessLocally(true) then
        local function fPrebehaveCallback(res)
            if res.result ~= 0 then
                log("Prebehave result different from server, revert!")
                if UE_BUILD_DEVELOPMENT == 1 then
                    LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.MovePreBehaveFailed)

                end
                moveCmd:RevertLocalProcess(true, true)
            end
        end
        Server.InventoryServer:SyncInventoryChanged(fPrebehaveCallback)
    end

    return true
end

---@param moveCmd ItemMoveCmd
function InventoryServer:ProcessItemMoveCmdByFrontendIgnoresChecks(moveCmd)
    ---@type pb_EquipPropCommand
    local cmd = {}
    cmd.prop_id = moveCmd.item.id
    cmd.prop_gid = moveCmd.item.instanceId
    cmd.src_pos = moveCmd.originLoc.ItemSlot.SlotType
    cmd.target_pos = moveCmd.targetLoc.ItemSlot.SlotType
    cmd.spec_loc = moveCmd.targetLoc:ToPbLocation()
    cmd.num = moveCmd.moveNum

    if moveCmd.moveParams.bAutoMerge ~= nil then
        cmd.disable_auto_merge = not moveCmd.moveParams.bAutoMerge
    else
        cmd.disable_auto_merge = true
    end

    -- 自动排序模式修正
    if Server.InventoryServer:GetAutoSort() then
        cmd.spec_loc.space_id = 0
        -- 自动排序下，不允许内部移动
        if cmd.src_pos == cmd.target_pos then
            return false
        end
    end

    Server.InventoryServer:AddItemMoveCmd(cmd)

    return true
end

-- ---@param moveCmd ItemMoveCmd
-- function InventoryServer:RevertItemMoveCmdLocally(moveCmd)
--     local sourceSlot = moveCmd.originLoc.ItemSlot
--     local targetSlot = moveCmd.targetLoc.ItemSlot

--     targetSlot:RemoveItem(moveCmd.item)
--     for _, item in ipairs(moveCmd.targetItems) do
--         targetSlot:RemoveItem(item)
--     end

--     local loc = moveCmd.cacheForRevert[]
-- end

---@param moveCmd ItemMoveCmd
function InventoryServer:ProcessMeleeWeaponItemMoveCmd(moveCmd)
    Server.InventoryServer:AddItemMoveCmd(moveCmd)
    return true
end

---@param cmd pb_EquipPropCommand
function InventoryServer:AddItemMoveCmd(cmd)
    -- Before add to cmd buffer, we should remove duplicate cmd.
    -- It's possible due to some duplciate event listeners.
    local duplicateCmds = {}
    for _, bufferCmd in ipairs(self._itemMoveCmdBuffer) do
        if bufferCmd.prop_gid == cmd.prop_gid and bufferCmd.target_pos == cmd.target_pos then
            duplicateCmds[bufferCmd] = true
        end
    end
    table.removeByFunc(self._itemMoveCmdBuffer, function(cmd)
        return duplicateCmds[cmd] == true 
    end)

    table.insert(self._itemMoveCmdBuffer, cmd)
end

---@param cmd pb_EquipPropCommand
function InventoryServer:AddItemMoveCmdByLocally(cmd)
    table.insert(self._itemMoveCmdBuffer, cmd)
end

function InventoryServer:ClearSyncCmd()
    self._itemMoveCmdBuffer = {}
end

function InventoryServer:GetSyncReq()
    local req = pb.CSDepositEquipPropReq:New()
    req.allow_partly = false
    req.using_scav = self.bScav
    req.cmds = {}

    for _, cmd in ipairs(self._itemMoveCmdBuffer) do
        table.insert(req.cmds, cmd)
    end

    return req
end

function InventoryServer:TickSyncItemMove(delta)
    self:SyncInventoryChanged()
end

---@class itemMoveExtraParam
---@field allow_partly boolean
---@field is_transfer_all boolean
---@param extraParams itemMoveExtraParam
function InventoryServer:SyncInventoryChanged(fRequestCallBack, extraParams)
    if not ItemOperaTool.CheckRunWarehouseLogic() then
        return
    end
    if table.isempty(self._itemMoveCmdBuffer) then
        return
    end
    
    extraParams = setdefault(extraParams, {})
    local req = self:GetSyncReq()
    req.allow_partly = setdefault(extraParams.allow_partly, false)
    req.is_transfer_all = setdefault(extraParams.is_transfer_all, false)
    req.outfit_ammo_to_bag = setdefault(extraParams.outfit_ammo_to_bag, false)

    self:ClearSyncCmd() 

    -- if InventoryServer.PRINT_DEBUG_INFO then
    --     logtable(req)
    -- end

    ---@param res pb_CSDepositEquipPropRes
    local fCallBack = function(res)
        -- Preprocess
        self:_PreprocessCSDepositEquipRes(res)
        -- Process error code (if error)
        self:_ProcessInvErrorCode(res)

        -- Process data changes
        self:_OnCSDepositEquipPropRes(res, req)

        -- Post broadcast delegate
        if fRequestCallBack then
            fRequestCallBack(res, req)
        end
    end
    
    req:Request(fCallBack, {bShowErrTip = false, bEnableHighFrequency = true})
    return true
end

-- 统一处理仓库相关的错误码
function InventoryServer:_ProcessInvErrorCode(res)
    if res.result ~= 0 then
        if res.result == Err.DepositGuidePropInvalidMove then
            LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.DepositGuidePropInvalidMove)
        elseif res.result == Err.DepositSpaceNotEnough then
            LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.TransferFailedInsufficientSpace)
        elseif res.result == Err.DepositSwapPropFialed and res and res.cmds and res.cmds[1].target_pos == ESlotType.SafeBox then
            LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.ReplacePermissionItemFailed)
        end
    end
end

---@param res pb_CSDepositEquipPropRes
function InventoryServer:_PreprocessCSDepositEquipRes(res)
    local header = res._header

    if res.result ~= 0 and res.cmds then
        local firstItemGid = res.cmds[1].prop_gid
        local firstItem = self:GetItemByGid(firstItemGid)
        -- 替换扩容箱可能会导致item为nil
        if firstItem then
            self.Events.evtItemMoveFail:Invoke(firstItem)
        end
    end
end

---@param res pb_CSDepositEquipPropRes
---@param req pb_CSDepositEquipPropReq
function InventoryServer:_OnCSDepositEquipPropRes(res, req)
    if res.result == 0 then
        self._extOrder = res.extension_pos_order    -- Ext slot order
        -- Check has prebehaved
        local bHasPrebehaved = false
        for _, cmd in ipairs(req.cmds) do
            if cmd.sourceCmd and cmd.sourceCmd.bPrebehave then
                bHasPrebehaved = true
                break
            end
        end

        self:ProcessPbDataChange(res.deposit_change, true, bHasPrebehaved)
        --Server.TeamServer:OnCSDepositChangeNtf(res)
    end
end

---@param ntf pb_CSDepositChangeNtf
function InventoryServer:_OnCSDepositChangeNtf(ntf)
    if not ItemOperaTool.CheckRunWarehouseLogic() then
        return
    end

    -- if ntf.result == 0 then
        self._extOrder = ntf.extension_pos_order    -- Ext slot order
        if #ntf.safe_and_card_pack > 0 then
            self:_FetchServerProcessSafeBoxAndKeyChain(ntf.safe_and_card_pack)
        end
        self:ProcessPbDataChange(ntf.deposit_change, true)

        --Server.TeamServer:OnCSDepositChangeNtf(ntf)
    -- end
end

function InventoryServer:_OnCSMeleeUnlockChangeNtf(ntf)
    local dataChange = ntf.deposit_change
    for i, propChange in ipairs(dataChange.prop_changes) do
        self:_ProcessMeleeWeaponItemChange(propChange)
    end
    self.Events.evtPostSOLMeleeDataChange:Invoke()
end

--- SOL
function InventoryServer:ProcessPbDataChangeByRes(res, bUpdateData)
    if res.result == 0 then
        self:ProcessPbDataChange(res.changes, true, nil, bUpdateData)
    else
        --local ErrorStr = Facade.ProtoManager.MapErrCode2String[res.result]
        if res.result ~= 0 and res.result ~= Err.WassNoChange then
           --LogUtil.LogInfo("Error: ", res.result, "," ,ErrorStr,",", res.errmsg)
            local text = ServerTipCode.AssemblyFailed
            if res.errmsg then
                text = text .. res.errmsg
            end
            LuaGlobalEvents.evtServerShowTip:Invoke(text)
        end
    end
end

--款式更新
function InventoryServer:AdapterStyleUpdateFromAssemble(res)
    if res.result == 0 then
        self:ProcessPbDataChange(res.changes, true)
    end
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Process pb data

---@class itemMoveInfo
---@field item ItemBase
---@field OldLoc ItemLocation
---@field NewLoc ItemLocation
---@field bInternalMove boolean
---@field Reason PropChangeType
---@field bIncreaseOrDecrease boolean 数量增加或减少了（nil表示不增不减）
---@field bNeedScrollTo boolean

---------------------------------------------------------------------------------
--- SOL仓库 刷新 changes
---------------------------------------------------------------------------------
---@param dataChange pb_DataChange
---@param bRefreshView boolean
---@param bHasPrebehaved boolean
function InventoryServer:ProcessPbDataChange(dataChange, bRefreshView, bHasPrebehaved, bUpdateData)
    bRefreshView = setdefault(bRefreshView, true)
    bHasPrebehaved = setdefault(bHasPrebehaved, false)
    -- MP改枪会通过传默认值而导致仓库扩容箱数量数据发生改变，所以需要一个字段来屏蔽某些数据的更新
    bUpdateData = setdefault(bUpdateData, true)

    ---@type table<i, itemMoveInfo>
    local cacheItemsInfo = {}

    local cacheCapacity = {}
    local cacheTotalCapacity = {}
    for _, slotType in ipairs(AllContainerSlots) do
        local slot = self:GetSlot(slotType)

        cacheCapacity[slotType] = slot:GetUsedCapacity()
        cacheTotalCapacity[slotType] = slot:GetTotalCapacity()
        
        -- for _, Item in ipairs(Slot:GetItems()) do
        --     CacheCapacity[SlotType] = CacheCapacity[SlotType] + Item.Num
        -- end
    end

    -- 更新扩容的数量
    if bUpdateData then
        self.maxExtendSlotNum = dataChange.upperlimit_extension_num >= 0 and dataChange.upperlimit_extension_num or self.maxExtendSlotNum
        self.usedExtendSlotNum = dataChange.cur_extension_num >= 0 and dataChange.cur_extension_num or self.usedExtendSlotNum
        self.curMaxExtSlotNum = dataChange.max_extension_num >= 0 and dataChange.max_extension_num or self.curMaxExtSlotNum
        self.availableExtendSlotNum = self.curMaxExtSlotNum - self.usedExtendSlotNum
        self.lockExtendSlotNum = self.maxExtendSlotNum - self.usedExtendSlotNum - self.availableExtendSlotNum
    end

    self:_ProcessUpdateWeaponSkinSetups(dataChange.weapon_skin_setup, false)

    -- 先处理PosChange
    for _, posChange in ipairs(dataChange.pos_changes) do
        self:ProcessPbPosChange(posChange)
    end

    --- 预处理过程
    --- 先做一次聚合，有可能回包里有多次相同道具的移动
    --- 客户端只关心初始位置和最后的结果
    ---@type table<number, pb_PropChange>
    local tmp = {}
    local indexToMove = {}
    for i, propChange in ipairs(dataChange.prop_changes) do
        local gid = propChange.prop.gid
        local duplicateChange = tmp[gid]

        if not duplicateChange then
            tmp[gid] = propChange
        elseif propChange.change_type == PropChangeType.Move then
            -- 重复move
            duplicateChange.dest = propChange.dest
            indexToMove[i] = true
            if duplicateChange.change_type == PropChangeType.Modify then
                duplicateChange.change_type = PropChangeType.ModifyWithMove
            end
        elseif propChange.change_type == PropChangeType.Modify then
            duplicateChange.prop = propChange.prop
            indexToMove[i] = true
            if duplicateChange.change_type == PropChangeType.Move then
                duplicateChange.change_type = PropChangeType.ModifyWithMove
            end
        elseif propChange.change_type == PropChangeType.Del then
            duplicateChange.change_type = PropChangeType.Del
            indexToMove[i] = true
        end
    end
    for i = #dataChange.prop_changes, 1, -1 do
        if indexToMove[i] then
            table.remove(dataChange.prop_changes, i)
        end
    end
    -- DataChange.prop_changes = table.values(Tmp)

    if bHasPrebehaved then
        -- 如果有预表现，则应该校验本地的结果与后台是否一致
        for i, propChange in ipairs(dataChange.prop_changes) do
            ---@type ItemBase
            local item = self:GetItemByGid(propChange.prop.gid)
            local slot = item and item.InSlot
            if not slot then
                log("item or slot is no exit")
                break
            end
            local loc = slot:GetItemLocation(item)

            local serverLoc = ItemLocation:NewIns()
            local serverSlot = Server.InventoryServer:GetSlot(propChange.dest.pos)
            serverLoc:InitFromPb(serverSlot, propChange.dest)

            if not loc:Equal(serverLoc) then
                -- 本地的结果与后台不一致
                log(string.format("ProcessPbDataChange Trigger Prebahave Inconsistent, item=%s", item.name))
                if UE_BUILD_DEVELOPMENT == 1 then
                    LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.MovePreBehaveFailed)
                end

                bHasPrebehaved = false
                break
            end
        end
    end

    for i, propChange in ipairs(dataChange.prop_changes) do
        loginfo("Processing PbDataChange: "..i)
        if InventoryServer.PRINT_DEBUG_INFO then
            logtable(propChange)
        end

        if propChange.change_type == PropChangeType.Add then
            -- bug=130214262 [【CN】【PC】【必现】购买的推荐装备不能通过双击放回仓库](https://tapd.woa.com/r/t?id=130214262&type=bug)
            -- 由于配装可能在仓库的同一个位置同时移走一个物品和新增一个物品，如果先新增再移走，这个位置的占格信息会是空的，会被认为还可以放
            -- 因而这里需要先处理移动物品再处理新增物品的情况
            --self:ProcessPbPropChange_Add(propChange, cacheItemsInfo)
        elseif propChange.change_type == PropChangeType.Del then
            self:ProcessPbPropChange_Delete(propChange, cacheItemsInfo)
        elseif propChange.change_type == PropChangeType.Modify then
            self:ProcessPbPropChange_Modify(propChange, cacheItemsInfo)
        elseif propChange.change_type == PropChangeType.Move then
            -- 先移除所有src
            self:ProcessPbPropChange_Move_Src(propChange, cacheItemsInfo)
        elseif propChange.change_type == PropChangeType.ModifyWithMove then
            self:ProcessPbPropChange_Modify(propChange, cacheItemsInfo)
            self:ProcessPbPropChange_Move_Src(propChange, cacheItemsInfo)
        end
    end

    -- 再添加到所有dest
    for i, propChange in ipairs(dataChange.prop_changes) do
        -- loginfo("Processing PbDataChange Move Add: "..i)
        if propChange.change_type == PropChangeType.Add then
            -- 上面处理了移动物品，这里再处理添加物品
            self:ProcessPbPropChange_Add(propChange, cacheItemsInfo)
        elseif propChange.change_type == PropChangeType.Move
        or propChange.change_type == PropChangeType.ModifyWithMove then
            self:ProcessPbPropChange_Move_Dest(propChange, cacheItemsInfo)
        end

         -- rebind weapon skin info
         local item = self:GetItemByGid(propChange.prop.gid)
         self:OverrideItemBaseSkinInfosChecked(item)
    end

    -- 收藏室红点处理，防止调用频率过高
    for i, propChange in ipairs(dataChange.prop_changes) do
        if propChange.change_type == PropChangeType.Add or propChange.change_type == PropChangeType.Del then
            Server.CollectionRoomServer.Events.evtIsClickedReddot:Invoke()
        end
    end

    self:ProcessPbCurrencyChange_Delta(dataChange.currency_changes)

    -- 最后处理其他的
    local items = {}
    for itemGID, itemMoveInfo in pairs(cacheItemsInfo) do
        itemMoveInfo.ChangeReason = dataChange.reason
        local item = itemMoveInfo.item
        table.insert(items, item)

        ---@type ItemLocation
        local newLoc = itemMoveInfo.NewLoc
        local newSlot = newLoc and newLoc.ItemSlot or nil
        if newSlot and newSlot:IsExtendSlot() then
            self:UpdateDepositConfigByExtSlot(newSlot)
        end

    end
    -- self:CheckAndFreshApplication(items) -- 废弃

    -- 更新负重
    self:CalculateAllCarryItemsWeight(ESlotGroup.Player)

    --local moveItemInfoTemp = nil
    if bRefreshView then
        self.Events.evtItemMoveBatch:Invoke(cacheItemsInfo)
    end
    local num = 0
    local itemGidsNeedScrollTo = ItemOperaTool.GetItemGidsNeedScrollTo(table.values(cacheItemsInfo))
    for itemGID, moveItemInfo in pairs(cacheItemsInfo) do
        moveItemInfo.bNeedScrollTo = moveItemInfo.item and (itemGidsNeedScrollTo[moveItemInfo.item.gid] or false) or false
        num = num + 1
        if bRefreshView and not bHasPrebehaved then
            self.Events.evtItemMove:Invoke(moveItemInfo, num)
        end
        --if not moveItemInfoTemp then
        --    moveItemInfoTemp = moveItemInfo
        --end
    end
    -- 道具改变相关音频
    --if ItemOperaTool.CheckRunWarehouseLogic() and moveItemInfoTemp then
    --    self:_ProcessAudioPlayByMoveInfo(moveItemInfoTemp)
    --end

    for _, slotType in ipairs(AllContainerSlots) do
        local slot = self:GetSlot(slotType)

        local tmpCapacity = slot:GetUsedCapacity()
        local tmpTotalCapacity = slot:GetTotalCapacity()

        -- for _, Item in ipairs(Slot:GetItems()) do
        --     TmpCapacity = TmpCapacity + Item.Num
        -- end

        if tmpCapacity ~= cacheCapacity[slotType] or tmpTotalCapacity ~= cacheTotalCapacity[slotType] then
            self.Events.evtUpdateItemCapacity:Invoke(slot, tmpCapacity > cacheCapacity[slotType])

            self.Events.evtUpdateItemCapacity:Invoke(slot, tmpCapacity > cacheCapacity[slotType])
        end
    end

    self.Events.evtPostUpdateDeposData:Invoke(dataChange)
    Server.TeamServer:CheckedDepositData(dataChange)
end

function InventoryServer:CheckAndFreshApplication(items)
    local bNeedRefresh = false
    for i, item in ipairs(items) do
        local healthFeature = item:GetFeature(EFeatureType.Health)
        if healthFeature and (healthFeature.IsMedicine and healthFeature:IsMedicine()) then
            bNeedRefresh = true
            break
        end
    end
    if bNeedRefresh then
        self.Events.evtSupplyDataUpdate:Invoke()
    end
    return bNeedRefresh
end

---@param propChange pb_PropChange
function InventoryServer:ProcessPbPropChange_Move_Src(propChange, cacheItemsInfo)
    ensure(type(cacheItemsInfo) == "table")
    local propInfo = propChange.prop
    local oldPbLoc = propChange.src
    local itemGid = propInfo.gid

    local item = self:GetItemByGid(itemGid)
    if item == nil then
        item = self:GetItemByGid(itemGid, ESlotGroup.Scav)
    end
    -- 查看是否为结算时的虚拟空间的道具
    --local virtualItem = Server.SettlementServer:GetMyCarryOutVirtualItem(itemGid)
    --if virtualItem then
    --    self.Events.evtPostUpdateVirtualDeposData:Invoke(virtualItem)
    --    -- 以下是不会移除的，只是为了添加一个cacheItemsInfo
    --    if item == nil then
    --        item = virtualItem
    --    end
    --end
    if item then
        item:SetRawPropInfo(propChange.prop)
        ---@type ItemSlot
        local oldItemSlot
        if oldPbLoc.pos ~= 0 then
            oldItemSlot = self:GetSlot(oldPbLoc.pos)
        else
            oldItemSlot = item.InSlot
        end
        local oldLoc = oldItemSlot:GetItemLocation(item)
        oldItemSlot:RemoveItemByGID(itemGid, false)
    
        local itemMoveInfo = {
            item = item, 
            OldLoc = oldLoc, 
            bInternalMove = propChange.src.pos == propChange.dest.pos,
            Reason = PropChangeType.Move
        }
        cacheItemsInfo[itemGid] = itemMoveInfo
        if oldItemSlot:IsExtendSlot() then
            self._extMapping[oldPbLoc.pos] = nil
        end
    else
        logwarning(string.format("Error gid %d while ProcessPbPropChange_Move_Src.", itemGid))
    end
end

---@param propChange pb_PropChange
function InventoryServer:ProcessPbPropChange_Move_Dest(propChange, cacheItemsInfo)
    ensure(type(cacheItemsInfo) == "table")

    local propInfo = propChange.prop
    local newPbLoc = propChange.dest
    local itemGid = propInfo.gid

    local newItemSlot = self:GetSlot(newPbLoc.pos)
    local itemMoveInfo = cacheItemsInfo[itemGid]
    if itemMoveInfo then
        local item = itemMoveInfo.item
        ---@type ItemLocation
        local oldLoc = itemMoveInfo.OldLoc
        local newLoc
        if item then
            item.num = propInfo.num
            newItemSlot:AddItem(item, false)
            -- loginfo(Item.Gid,Item.InSlot.SlotType,'slot change')
            newLoc = newItemSlot:SetItemPosFromPb(item, newPbLoc)
            itemMoveInfo.NewLoc = newLoc
            self:AddItemToList(item, newItemSlot)
    
            if newItemSlot:IsExtendSlot() then
                self._extMapping[newPbLoc.pos] = ItemHelperTool.GetDepositIdByExtId(newPbLoc.pos)
            end
        end
    
        -- -- 服务器下发的Move，有可能原位置移动（即位置不变）
        -- -- 客户端不处理
        -- if oldLoc:Equal(newLoc) then
        --     cacheItemsInfo[itemGid] = nil
        -- end
    else
        logwarning(string.format("Error gid %d while ProcessPbPropChange_Move_Dest.", itemGid))
    end
end

---@param propChange pb_PropChange
function InventoryServer:ProcessPbPropChange_Add(propChange, cacheItemsInfo)
    ensure(propChange.dest.pos ~= ESlotType.None)

    local propInfo = propChange.prop
    local newPbLoc = propChange.dest
    local itemGid = propInfo.gid

    local targetSlot = self:GetSlot(newPbLoc.pos)
    if targetSlot:IsExtendSlot() then
        targetSlot:ClearItems()
    end
    local item = self:_InternalCreateNewItem(propInfo, targetSlot)
    local newItemSlot = self:GetSlot(newPbLoc.pos)
    local newLoc = newItemSlot:SetItemPosFromPb(item, newPbLoc)

    local itemMoveInfo = {
        item = item,
        OldLoc = nil,
        NewLoc = newLoc,
        bInternalMove = false,
        Reason = PropChangeType.Add
    }
    cacheItemsInfo[itemGid] = itemMoveInfo

    if newItemSlot:IsExtendSlot() then
        self._extMapping[newPbLoc.pos] = ItemHelperTool.GetDepositIdByExtId(newPbLoc.pos)
    end

    -- 将添加的道具的gid放入TempItemGids中
    table.insert(self.newItemGids , itemGid)

end

---@param propChange pb_PropChange
function InventoryServer:ProcessPbPropChange_Delete(propChange, cacheItemsInfo)

    local propInfo = propChange.prop
    local itemGid = propInfo.gid

    local item = self:GetItemByGid(itemGid)
    if item then
        ---@type ItemSlot
        local oldItemSlot = item.InSlot
        local oldLoc = oldItemSlot:GetItemLocation(item)
        oldItemSlot:RemoveItemByGID(itemGid, false)

        local itemMoveInfo = {
            item = item, 
            OldLoc = oldLoc, 
            NewLoc = nil,
            bInternalMove = false,
            Reason = PropChangeType.Del
        }
        cacheItemsInfo[itemGid] = itemMoveInfo

        if oldItemSlot:IsExtendSlot() then
            self._extMapping[oldItemSlot.SlotType] = nil
        end

        if oldItemSlot:GetSlotGroup() == ESlotGroup.Player then
            if WeaponAssemblyTool.IsAssembledWeapon(item.id) then
                --保存机匣
                if item.itemMainType == EItemType.Receiver then
                    self._allRecParts[ESlotGroup.Player][propInfo.gid] = nil
                elseif item.itemMainType == EItemType.Adapter then
                    self._allWeaponParts[ESlotGroup.Player][propInfo.gid] = nil
                end
            end
        end
    end

    -- 当道具减少时，需要删除两种道具Gid，一种是新增时添加的Gid且在仓库中没有显示的
    table.removebyvalue(self.newItemGids , itemGid)

    -- 获取玩家本地数据，并将存在本地数据上的Gid进行删除
    -- local allItemGids = Facade.ConfigManager:GetUserArray("UserObtained",{})
    self:RemoveAllItemGid(itemGid)
    -- table.removebyvalue(allItemGids , itemGid)
    -- Facade.ConfigManager:SetUserArray("UserObtained" , allItemGids)

end

---@param propChange pb_PropChange
function InventoryServer:ProcessPbPropChange_Modify(propChange, cacheItemsInfo)
    -- ensure(propChange.dest.pos ~= ESlotType.None)

    local propInfo = propChange.prop
    local newPbLoc = propChange.dest
    local itemGid = propInfo.gid

    ---@type ItemBase
    local item = self:GetItemByGid(itemGid)
    if item then
        local oldSlot = item.InSlot
        local oldLoc = oldSlot:GetItemLocation(item)
        local srcNum = item.num
        item:SetRawPropInfo(propInfo)
        Server.WeaponAssemblyServer:UpdateWeaponSkinInfoFromRawPropInfoUpdate(propInfo)

        local newItemSlot = self:GetSlot(newPbLoc.pos)
        local newLoc = newItemSlot:GetItemLocation(item)

        local itemMoveInfo = {
            item = item, 
            OldLoc = oldLoc, 
            NewLoc = newLoc,
            bInternalMove = false,
            bIncreaseOrDecrease = srcNum < item.num,
            Reason = PropChangeType.Modify
        }
        if srcNum == item.num then
            itemMoveInfo.bIncreaseOrDecrease = nil
        end
        cacheItemsInfo[itemGid] = itemMoveInfo
        if oldSlot:GetSlotGroup() == ESlotGroup.Player then
            if WeaponAssemblyTool.IsAssembledWeapon(item.id) then
                --保存机匣
               if item.itemMainType == EItemType.Receiver then
                   self._allRecParts[ESlotGroup.Player][propInfo.gid] = item
               elseif item.itemMainType == EItemType.Adapter then
                   self._allWeaponParts[ESlotGroup.Player][propInfo.gid] = item
               end
            end
        end
    else
        local cacheItemInfo = cacheItemsInfo[itemGid]
        if cacheItemInfo and cacheItemInfo.item then
            item = cacheItemInfo.item
            
            item:SetRawPropInfo(propInfo)
        else
            logwarning('cant find item by gid:', itemGid)
        end
    end
end

---@param posChange pb_PositionChange
function InventoryServer:ProcessPbPosChange(posChange)
    if InventoryServer.PRINT_DEBUG_INFO then
        logtable(posChange)
    end

    local changeType = posChange.change_type
    if changeType == PositionChangeType.Add or changeType == PositionChangeType.PosChangeModify then

    elseif changeType == PositionChangeType.PosChangeDel then

    end

    local position = posChange.pos_id
    local changeSlot = self:GetSlot(position)
    local equipmentId = posChange.src_prop_id
    if equipmentId and equipmentId > 0 then
        if equipmentId > 0 then
            local spaceData = ItemBaseTool.GetSpaceDataByEquipmentId(equipmentId)
            if spaceData then
                changeSlot:InitMultiSpaceStructureByGrids(spaceData)
            else
                loginfo(string.format("InventoryServer:_FetchServerProcessEquipProps not space config found for item(id=%d)", equipmentId))
                changeSlot:InitSpaceStructureByPb(posChange.space)
            end
        else
            changeSlot:InitSpaceStructureByPb(posChange.space)
        end
    elseif position == ESlotType.Pocket then
        changeSlot:InitMultiSpaceStructureByGrids(ItemBaseTool.GetPocketSpaceData())
    else
        changeSlot:InitSpaceStructureByPb(posChange.space)
    end
    
    if changeSlot:GetLabel() ~= posChange.tag then
        changeSlot:SetLabel(posChange.tag)
        self.Events.evtSlotTagChange:Invoke(changeSlot)
    end
    self.Events.evtSlotSpaceChange:Invoke(changeSlot)
end

function InventoryServer:_ProcessAudioPlayByMoveInfo(moveInfo)
    local item = moveInfo.item
    ---@type ItemLocation
    local oldLoc = moveInfo.OldLoc
    ---@type ItemLocation
    local newLoc = moveInfo.NewLoc
    loginfo("InventoryServer:_ProcessAudioPlayByMoveInfo")

    if oldLoc and newLoc then
        loginfo("valid")
        local targetSlot = newLoc.ItemSlot
        local oldSlot = oldLoc.ItemSlot
        loginfo("targetSlot", targetSlot, "oldSlot", oldSlot)
        if targetSlot:IsExtendSlot() then
            Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UILobbyWHSafeboxExp)
        elseif targetSlot:IsEquipableSlot() then
            Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UILobbyWHEquip)
        elseif targetSlot.SlotType == ESlotType.SafeBoxContainer then
            -- 放入保险箱
            loginfo("InvSound SafeBox")
            Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIItemMoveSafetyBox)
        elseif oldSlot.SlotType == targetSlot.SlotType and oldSlot:GetSlotGroup() == targetSlot:GetSlotGroup() then
            -- 容器内转移
            loginfo("InvSound InContainer")
            Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIItemMoveGrid)
        else
            -- 容器间转移
            loginfo("InvSound AcrossContainer")
            Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIItemMoveContainer)
        end
    else
        if newLoc then
            loginfo("oldLoc is nil")
        else
            loginfo("newloc is nil")
        end
    end
end
--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region 预表现相关

function InventoryServer:CheckItemIsWaitingServer(item)
    for _, cmd in ipairs(self._itemMoveCmdBuffer) do
        if cmd.prop_gid == item.gid then
            return true
        end
    end

    for seqId, req in pairs(self._waitingRequests) do
        for _, cmd in ipairs(req.cmds) do
            if cmd.prop_gid == item.gid then
                return true
            end
        end
    end

    return false
end

function InventoryServer:CheckItemMoveCmdBuffer()
    return not table.isempty(self._itemMoveCmdBuffer)
end

--endregion
-----------------------------------------------------------------------

---@param setups table<pb_WeaponSkinSetup>
---@param bReset boolean
function InventoryServer:_ProcessUpdateWeaponSkinSetups(setups, bReset)
    bReset = setdefault(bReset, true)
    if bReset then
        self._weaponSkinSetups = {}    
    end

    if setups == nil then
        return
    end

    for index, value in ipairs(setups) do
        if value ~= nil then
            self._weaponSkinSetups[value.weapon_id] = value
        end
    end
end

function InventoryServer:SetNotifyReissue()
    log("SetNotifyReissue")
    self.bNotifyReissue = true
end

function InventoryServer:ConsumeNotifyReissue()
    if self.bNotifyReissue then
        log("ConsumeNotifyReissue")
        self.bNotifyReissue = false

        return true
    end

    return false
end

function InventoryServer:GetWeaponSkinSetUp()
    return _weaponSkinSetups
end

return InventoryServer