----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSettlement)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS
-- Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local UGPInputHelper = import "GPInputHelper"
local UGPInputDelegates = import "GPInputDelegates"
local EGPInputType = import "EGPInputType"
local FAnchors = import "Anchors"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
--- END MODIFICATION

local MPWeaponsUpgrade = ui("MPWeaponsUpgrade")

local MPWeaponLevelPartsTable = Facade.TableManager:GetTable("WeaponSystem/MPWeaponLevelParts")

function MPWeaponsUpgrade:Ctor()
    self._curIdx = 1

    self._wtCarouselBtnHB = self:Wnd("wtCarouselBtnHB", UIWidgetBase)

    self._wtWeaponNameTB = self:Wnd("wtWeaponNameTB", UITextBlock)

    self._wtCurWeaponLevelTB = self:Wnd("wtCurWeaponLevelTB", UITextBlock)
    self._wtArrowImg = self:Wnd("wtArrowImg", UIImage)
    self._wtEndWeaponLevelTB = self:Wnd("wtEndWeaponLevelTB", UITextBlock)
    self._wtCurAddExpTB = self:Wnd("wtCurAddExpTB", UITextBlock)
    self._wtExpPBImg = self:Wnd("wtExpPBImg", UIImage)
    self._wtExpPBLine = self:Wnd("wtExpPBLine", UIImage)
    self._wtExpPBTB = self:Wnd("wtExpPBTB", UITextBlock)

    self._wtRightBtn = self:Wnd("DFButton",UIButton)
    self._wtRightBtn:Event("OnClicked", self.OnRightBtnClick, self)

    self._wtLeftBtn = self:Wnd("DFButton_195",UIButton)
    self._wtLeftBtn:Event("OnClicked", self.OnLeftBtnClick, self)

    self._wtWeaponListWSV = UIUtil.WndWaterfallScrollBox(self, "wtWeaponListWSV", self._OnGetWeaponCount, self._OnProcessWeaponWidget)
    self._wtWeaponListWSV.OnScrolling:Add(self._OnScrolling, self)
    self._wtWeaponListWSV.OnScrollEnd:Add(self._OnScrollEnd, self)

    self._wtComponetsSGB = UIUtil.WndScrollGridBox(self, "wtComponetsSGB", self._OnGetComponetsCount, self._OnProcessComponetsWidget)

    -- azhengzheng:long3武器经验卡
    self._wtExpOverflowTipWB = self:Wnd("wtExpOverflowTipWB", UIWidgetBase)
    self._wtExpOverflowTipTA = UIUtil.WndTipsAnchor(self, "wtExpOverflowTipTA", self._OnOpenExpOverflowTips, self._OnCloseExpOverflowTips)
    
    self._wtCarouselKeyIcon = self:Wnd("WBP_CommonKeyIconBox", UIWidgetBase)

    if IsHD() then
        self._length = 2914
    else
        self._length = 2108
    end
    --杀人星级
	self._wtSharkIcon = self:Wnd("DFImage_95", UIImage)
end

function MPWeaponsUpgrade:OnInitExtraData(weaponsInfo, parent)
    self._weaponsInfo = weaponsInfo
    self._parent = parent
end

function MPWeaponsUpgrade:OnOpen()
    if self._weaponsInfo == nil then
        local mpSettlemengInfo = Server.SettlementServer:GetMPSettlementInfo()
        if mpSettlemengInfo and mpSettlemengInfo.tdm_data and mpSettlemengInfo.tdm_data.weapon_change and #mpSettlemengInfo.tdm_data.weapon_change ~= 0 then
            self._weaponsInfo = mpSettlemengInfo.tdm_data.weapon_change
        else
            return
        end
    end
    
    self._weaponTips = nil

    self:_InitCarouselBtn()

    self:_InitItemBase()

    self._wtWeaponListWSV:RefreshAllItems()

    self:_InitWeaponsInfoTable()

    self:_InitWeaponsInfo()

    self:_StartShowWeapons()
end

function MPWeaponsUpgrade:OnShow()
    Module.Settlement.Config.Events.evtWeaponExpShow:Invoke()
    if IsHD() then
        self:_EnableGamepadFeature(true)
    end
end

-- BEGIN MODIFICATION @ VIRTUOS :
function MPWeaponsUpgrade:_EnableGamepadFeature(bEnable)
    if not IsHD() then
        return
    end

    if bEnable then
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)

        if not self._navGroups then
            self._navGroups = WidgetUtil.RegisterNavigationGroup(self._wtComponetsSGB, self, "Hittest")
            self._navGroups:AddNavWidgetToArray(self._wtComponetsSGB)
            self._navGroups:SetScrollRecipient(self._wtComponetsSGB)

            --self._navGroups:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroups)
        end
        self._wtCarouselKeyIcon:SetOnlyDisplayOnGamepad(true)
        self._wtCarouselKeyIcon:InitByDisplayInputActionName("SwitchCarousel", true, 0.0, true)
   
        self._toggleNextTabHandle = self:AddInputActionBinding("Common_SwitchToNextTab", EInputEvent.IE_Pressed, self.OnRightBtnClick, self, EDisplayInputActionPriority.UI_Pop)
        self._togglePrevTabHandle = self:AddInputActionBinding("Common_SwitchToPrevTab", EInputEvent.IE_Pressed, self.OnLeftBtnClick, self, EDisplayInputActionPriority.UI_POP)  
    else
        WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
        if self._navGroups then
            WidgetUtil.RemoveNavigationGroup(self)
            self._navGroups = nil
        end

        if self._toggleNextTabHandle then
            self:RemoveInputActionBinding(self._toggleNextTabHandle)
            self._toggleNextTabHandle = nil
        end
    
        if self._togglePrevTabHandle then
            self:RemoveInputActionBinding(self._togglePrevTabHandle)
            self._togglePrevTabHandle = nil
        end
    end
end
--- END MODIFICATION

function MPWeaponsUpgrade:OnHide()
    if IsHD() then
        self:_EnableGamepadFeature(false)
    end
end

function MPWeaponsUpgrade:OnClose()
    Facade.UIManager:ClearSubUIByParent(self, self._wtCarouselBtnHB)
    self:StopWidgetAnim(self.WBP_Settlement_Battle_WeaponUpgrades_Flush)
    Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.UISettlementBarLoop)
end

function MPWeaponsUpgrade:_InitCarouselBtn()
    if #self._weaponsInfo < 2 then
        self._wtCarouselBtnHB:Collapsed()
    else
        self._wtCarouselBtnList = {}
        Facade.UIManager:RemoveSubUIByParent(self, self._wtCarouselBtnHB)

        for key = 1, #self._weaponsInfo do
            local weakBtnWidget, btnInstanceID = Facade.UIManager:AddSubUI(self, UIName2ID.DFButtonCarousel2, self._wtCarouselBtnHB)
            local btnWidgetIns = getfromweak(weakBtnWidget)
            if btnWidgetIns then
                --btnWidgetIns:Event("OnClicked", self.ScrollToIndex, self, index)
            end
            table.insert(self._wtCarouselBtnList, btnWidgetIns)
        end
    end
end

function MPWeaponsUpgrade:_InitWeaponsInfoTable()
    self._weaponsInfoTable = {}

    if not MPWeaponLevelPartsTable then
        return
    end

    for _, value in pairs(MPWeaponLevelPartsTable) do
        if not self._weaponsInfoTable[value.RecFunctionId] then
            self._weaponsInfoTable[value.RecFunctionId] = {}
        end

        table.insert(self._weaponsInfoTable[value.RecFunctionId], {Level = value.Level, Exp = value.Exp, ExpSum = value.ExpSum})
    end

    for _, value in pairs(self._weaponsInfoTable) do
        table.sort(
            value,
            function(a, b)
                return a.Level < b.Level
            end
        )
    end
end

function MPWeaponsUpgrade:_InitWeaponsInfo()
    for _, value in ipairs(self._weaponsInfo) do
        value.curLevel, value.curLevelRemainExp, value.curLevelNeedExp = self:_CalculateWeaponMsg(value.ID, value.old_exp)
        value.startLevel = value.curLevel

        value.endLevel, value.endLevelRemainExp, value.endLevelNeedExp = self:_CalculateWeaponMsg(value.ID, value.exp)

        value.totalExp = value.exp - value.old_exp
    end
end

function MPWeaponsUpgrade:_CalculateWeaponMsg(id, exp)
    id = tostring(id)

    if not self._weaponsInfoTable[id] then
        logerror("azhengzheng:please check weapon " .. id .. " table info is not exit!")
        return 0, 0, 0
    end

    for _, value in pairs(self._weaponsInfoTable[id]) do
        if value.Exp == 0 or exp < value.Exp + value.ExpSum then
            return value.Level, exp - value.ExpSum, value.Exp
        end
    end

    logerror("azhengzheng:please check weapon " .. id .. " table info is legal!")
    return 0, 0, 0
end

function MPWeaponsUpgrade:_InitItemBase()
    for _, value in ipairs(self._weaponsInfo) do
        local items = Server.InventoryServer:GetItemsById(value.ID, ESlotGroup.MPApply)

        if items then
            value.itemBase = items[1]
        end

        value.componentsItemBase = {}

        for _, component in ipairs(value.unlock_componets_prop) do
            table.insert(value.componentsItemBase, ItemBase:New(component.id, 1))
        end

        if value.converted_expcard and #value.converted_expcard ~= 0 then
            value.expOverflowTips = {}
            table.insert(value.expOverflowTips, {textContent = Module.Settlement.Config.Loc.ExpOverflowIntoCard, styleRowId = "C000"})

            for _, expCard in pairs(value.converted_expcard) do
                local itemBaseIns = ItemBase:New(expCard.id, expCard.num)

                if itemBaseIns then
                    table.insert(value.componentsItemBase, itemBaseIns)
                    table.insert(value.expOverflowTips, {textContent = StringUtil.Key2StrFormat(Module.Settlement.Config.Loc.ItemNameWithNum, {["itemName"] = itemBaseIns.name, ["num"] = expCard.num}), styleRowId = "C000"})
                end
            end
        end
    end
end

function MPWeaponsUpgrade:_OnGetWeaponCount()
    return self._weaponsInfo and #self._weaponsInfo or 0
end

function MPWeaponsUpgrade:_OnProcessWeaponWidget(idx, widget)
    if self._weaponsInfo[idx].itemBase then
        local wtIconImg = widget:Wnd("wtWeaponIconWB", UIWidgetBase):Wnd("wtMainIcon", UIImage)

        if wtIconImg then
            RuntimeIconTool.SetItemIcon(self._weaponsInfo[idx].itemBase, wtIconImg)
        end
    end
end

function MPWeaponsUpgrade:_OnScrolling()
    self:_CheckAllAnimIsFinish()
end

function MPWeaponsUpgrade:_OnScrollEnd()
    local curIdx = math.round(self._wtWeaponListWSV:GetScrollOffset() / 2920) + 1

    if self._curIdx ~= curIdx then
        self._curIdx = #self._weaponsInfo < curIdx and #self._weaponsInfo or curIdx

        self:_RefreshView()
    end

    self._wtWeaponListWSV:ScrollToIndex(self._curIdx)
end

function MPWeaponsUpgrade:GoScrollToIndex(i, bEnableAutoScroll)
    -- azhengzheng:这里报错了，暂时屏蔽https://tapd.woa.com/tapd_fe/20421949/bug/detail/1120421949138730210
    -- log("MPWeaponsUpgrade:ScrollToIndex",i)

    self:_RefreshView()
    if self._wtWeaponListWSV then
        self._wtWeaponListWSV:ScrollToIndex(i)
        logerror("MPWeaponsUpgrade:GoScrollToIndex index = ", i)
    end
    logerror("MPWeaponsUpgrade:GoScrollToIndex")
end

function MPWeaponsUpgrade:UpdateLeftRightBtnVisible()
    if #self._weaponsInfo <= 1 then
        self._wtRightBtn:Collapsed()
        self._wtLeftBtn:Collapsed()
    elseif self._curIdx <= 1 then
        self._wtRightBtn:Visible()
        self._wtLeftBtn:Collapsed()
    elseif self._curIdx >= #self._weaponsInfo then
        self._wtRightBtn:Collapsed()
        self._wtLeftBtn:Visible()
    else
        self._wtRightBtn:Visible()
        self._wtLeftBtn:Visible()
    end
end

function MPWeaponsUpgrade:OnRightBtnClick()
    if self._weaponsInfo and #self._weaponsInfo ~= 0 then
        self._curIdx = math.clamp(self._curIdx + 1, 1, #self._weaponsInfo)
        self:GoScrollToIndex(self._curIdx)
    end
end

function MPWeaponsUpgrade:OnLeftBtnClick()
    if self._weaponsInfo and #self._weaponsInfo ~= 0 then
        self._curIdx = math.clamp(self._curIdx - 1, 1, #self._weaponsInfo)
        self:GoScrollToIndex(self._curIdx)
    end
end


function MPWeaponsUpgrade:_StartShowWeapons()
    if #self._weaponsInfo ~= 1 then
        self._weaponsAnimTimerHandle = Timer:NewIns(3, 0)
        self._weaponsAnimTimerHandle:AddListener(self._ShowWeaponsByTimer, self)
        self._weaponsAnimTimerHandle:Start()
    end

    self:_RefreshView()
end

function MPWeaponsUpgrade:_ShowWeaponsByTimer()
    self._wtWeaponListWSV:ScrollToIndex(self._curIdx + 1)

    if self._curIdx == #self._weaponsInfo then
        self:_ReleaseShowWeaponsTimer()
    end
end

function MPWeaponsUpgrade:_RefreshView()
    self:_SetUpLevelState()

    self:_RefreshCarouselBtn()

    self:_RefreshStaticMsg()

    self:UpdateLeftRightBtnVisible()

    --杀人升星
    if self:_KillingRisingStar() then
        self:_SetUpLevelState(true)
        return
    end

    if self._weaponsAnimTimerHandle or not self._notFirstIn then
        self._notFirstIn = true
        self:_StartExpAnim()
    else
        self:_RefreshFinalExp()
    end
end

function MPWeaponsUpgrade:_RefreshCarouselBtn()
    if self._wtCarouselBtnList then
        for key, value in ipairs(self._wtCarouselBtnList) do
            value:SetIsEnabledStyle(key ~= self._curIdx)
        end
    end
end

function MPWeaponsUpgrade:_RefreshStaticMsg()
    self._wtComponetsSGB:RefreshAllItems()

    self._wtCurWeaponLevelTB:SetText(string.format(Module.Settlement.Config.Loc.WeaponLevel, self._weaponsInfo[self._curIdx] and self._weaponsInfo[self._curIdx].startLevel or "-"))
    self._wtEndWeaponLevelTB:SetText(self._weaponsInfo[self._curIdx] and self._weaponsInfo[self._curIdx].endLevel or "-")
    self._wtWeaponNameTB:SetText(self._weaponsInfo[self._curIdx].itemBase and self._weaponsInfo[self._curIdx].itemBase.name or "-")

    if self._weaponsInfo[self._curIdx].expOverflowTips and #self._weaponsInfo[self._curIdx].expOverflowTips > 1 then
        self._wtExpOverflowTipWB:Visible()
        if IsHD() and self._parent then
            self._parent:ChangeWeaponUpdateTipBtn(true)
        end
    else
        self._wtExpOverflowTipWB:Collapsed()
        if IsHD() and self._parent then
            self._parent:ChangeWeaponUpdateTipBtn(false)
        end
    end
end

function MPWeaponsUpgrade:_StartExpAnim()
    self._curAddExp = 0
    self._singleAddExp = math.ceil(self._weaponsInfo[self._curIdx].totalExp / 80)
    self._singlePBAddExp = math.ceil((self._weaponsInfo[self._curIdx].curLevelNeedExp - self._weaponsInfo[self._curIdx].curLevelRemainExp + (self._weaponsInfo[self._curIdx].endLevelNeedExp == 0 and 0 or self._weaponsInfo[self._curIdx].endLevelRemainExp)) / 80)

    self._expAnimTimerHandle = Timer:NewIns(0.025, 0)
    self._expAnimTimerHandle:AddListener(self._RefreshExpByTimer, self)
    self._expAnimTimerHandle:Start()
    self:PlayAnimation(self.WBP_Settlement_Battle_WeaponUpgrades_Flush, 0, 0, EUMGSequencePlayMode.Forward, 1, true)
    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UISettlementBarLoop)
end

function MPWeaponsUpgrade:_RefreshExpByTimer()
    self._curAddExp = self._curAddExp + self._singleAddExp
    self._weaponsInfo[self._curIdx].curLevelRemainExp = self._weaponsInfo[self._curIdx].curLevelRemainExp + self._singlePBAddExp

    if self._weaponsInfo[self._curIdx].totalExp <= self._curAddExp then
        self:_ReleaseExpAnimTimer()
        return
    end

    if self._weaponsInfo[self._curIdx].curLevel ~= self._weaponsInfo[self._curIdx].endLevel then
        if self._weaponsInfo[self._curIdx].curLevelNeedExp <= self._weaponsInfo[self._curIdx].curLevelRemainExp then
            self:_SetUpLevelState(true)

            if self._weaponsInfo[self._curIdx].endLevelNeedExp == 0 then
                self:_ReleaseExpAnimTimer()
                return
            end

            self._weaponsInfo[self._curIdx].curLevel = self._weaponsInfo[self._curIdx].endLevel
            self._weaponsInfo[self._curIdx].curLevelRemainExp = self._weaponsInfo[self._curIdx].curLevelRemainExp - self._weaponsInfo[self._curIdx].curLevelNeedExp
            self._weaponsInfo[self._curIdx].curLevelNeedExp = self._weaponsInfo[self._curIdx].endLevelNeedExp
        end
    end

    if self._weaponsInfo[self._curIdx].curLevel == self._weaponsInfo[self._curIdx].endLevel and self._weaponsInfo[self._curIdx].curLevelNeedExp <= self._weaponsInfo[self._curIdx].curLevelRemainExp then
        self:_ReleaseExpAnimTimer()
        return
    end

    self:_RefreshExp()
end

function MPWeaponsUpgrade:_RefreshExp()
    self._wtCurAddExpTB:SetText(string.format(Module.Settlement.Config.Loc.plusSignText, MathUtil.GetNumberFormatStr(self._curAddExp)))

    if self._weaponsInfo[self._curIdx].curLevelNeedExp == 0 then
        self._wtExpPBImg:SetPercent(1)
        self._wtExpPBLine:SetPosition(FVector2D(self._length, 0))
        self._wtExpPBTB:SetText(Module.Settlement.Config.Loc.MaxLevel)
    else
        self._wtExpPBImg:SetPercent(self._weaponsInfo[self._curIdx].curLevelRemainExp / self._weaponsInfo[self._curIdx].curLevelNeedExp)
        self._wtExpPBLine:SetPosition(FVector2D(self._length * self._weaponsInfo[self._curIdx].curLevelRemainExp / self._weaponsInfo[self._curIdx].curLevelNeedExp, 0))
        self._wtExpPBTB:SetText(string.format(Module.Settlement.Config.Loc.CurLevelExpSchedule, self._weaponsInfo[self._curIdx].curLevelRemainExp, self._weaponsInfo[self._curIdx].curLevelNeedExp))
    end
end

function MPWeaponsUpgrade:_RefreshFinalExp()
    self._curAddExp = self._weaponsInfo[self._curIdx].totalExp
    self._weaponsInfo[self._curIdx].curLevel = self._weaponsInfo[self._curIdx].endLevel
    self._weaponsInfo[self._curIdx].curLevelRemainExp = self._weaponsInfo[self._curIdx].endLevelRemainExp
    self._weaponsInfo[self._curIdx].curLevelNeedExp = self._weaponsInfo[self._curIdx].endLevelNeedExp

    self:_RefreshExp()
    self:_SetUpLevelState(true)
    self:StopWidgetAnim(self.WBP_Settlement_Battle_WeaponUpgrades_Flush)
    Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.UISettlementBarLoop)
end

function MPWeaponsUpgrade:_ReleaseExpAnimTimer()
    if self._expAnimTimerHandle then
        self._expAnimTimerHandle:Release()
        self._expAnimTimerHandle = nil

        self:_SetUpLevelState(true)
        self:_RefreshFinalExp()
    end
end

function MPWeaponsUpgrade:_ReleaseShowWeaponsTimer()
    if self._weaponsAnimTimerHandle then
        self._weaponsAnimTimerHandle:Release()
        self._weaponsAnimTimerHandle = nil
    end
end

function MPWeaponsUpgrade:_CheckAllAnimIsFinish()
    local res = true

    if self._weaponsAnimTimerHandle then
        self:_ReleaseShowWeaponsTimer()
        res = nil
    end

    if self._expAnimTimerHandle then
        self:_ReleaseExpAnimTimer()
        res = nil
    end

    return res
end

function MPWeaponsUpgrade:_SetUpLevelState(bShow)
    if bShow then
        self._wtArrowImg:Visible()
        self._wtEndWeaponLevelTB:Visible()
        self._wtComponetsSGB:Visible()
    else
        self._wtArrowImg:Collapsed()
        self._wtEndWeaponLevelTB:Collapsed()
        self._wtComponetsSGB:Collapsed()
    end
end

function MPWeaponsUpgrade:_OnGetComponetsCount()
    return self._weaponsInfo[self._curIdx] and self._weaponsInfo[self._curIdx].componentsItemBase and #self._weaponsInfo[self._curIdx].componentsItemBase or 0
end

function MPWeaponsUpgrade:_OnProcessComponetsWidget(idx, widget)
    widget:InitItem(self._weaponsInfo[self._curIdx].componentsItemBase[idx + 1])

    widget:BindCustomOnClicked(
        function()
            self:_CheckAllAnimIsFinish()
            Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIItemSelect)
            widget:_SetSelected(true)
            Module.ItemDetail:OpenItemDetailPanel(
                self._weaponsInfo[self._curIdx].componentsItemBase[idx + 1],
                widget,
                nil,
                nil,
                nil,
                nil,
                nil,
                nil,
                nil,
                function()
                    widget:_SetSelected()
                end
            )
        end
    )

    local wtBottomRightWB = widget:Wnd("wtBottomRightIconText", UIWidgetBase)

    if wtBottomRightWB then
        if self._weaponsInfo[self._curIdx].componentsItemBase[idx + 1].num == 1 then
            wtBottomRightWB:Collapsed()
        else
            wtBottomRightWB:Visible()
        end
    end
end


function MPWeaponsUpgrade:_OnOpenExpOverflowTips()
    if self._expOverflowTipsHandle then
        return
    end

    self._expOverflowTipsHandle = Module.CommonTips:ShowCommonMessagesWithAnchor(self._weaponsInfo[self._curIdx].expOverflowTips, self._wtExpOverflowTipTA)
end

function MPWeaponsUpgrade:_OnCloseExpOverflowTips()
    if not self._expOverflowTipsHandle then
        return
    end

    Module.CommonTips:RemoveCommonMessageWithAnchor(self._expOverflowTipsHandle, self._wtExpOverflowTipTA)
    self._expOverflowTipsHandle = nil
end

function MPWeaponsUpgrade:ChangeTipState()
    if not IsHD() then
        return
    end
    if self._expOverflowTipsHandle then
        self:_OnCloseExpOverflowTips()
    else
        self:_OnOpenExpOverflowTips()
    end
end

--杀人星级
function MPWeaponsUpgrade:_KillingRisingStar()
    self._wtSharkIcon:Collapsed()
    if self._weaponsInfo and self._curIdx then
        local weapon = self._weaponsInfo[self._curIdx]
        if weapon then
            local itemData = weapon.itemBase
            if itemData then
                local weaponFeature = itemData:GetFeature(EFeatureType.Weapon)
                if weaponFeature then
                    local starNum = weaponFeature:GetStarNum()
                    if starNum == nil or starNum == 0 then
                        return false
                    end
                    local killNum = weaponFeature:GetKillCount()
                    local killMax = weaponFeature:GetKillCountPerStar()
                    local starMax = DFMGlobalConst.GetGlobalConstNumber("WeaponStarMaxNum", 0)
                    --旧📝
                    local oldStartNum = weaponFeature:GetOldStarNum()
                    local oldKillNum  = weaponFeature:GetOldKillCount()
                    local addKillNum = 0
                    local data1 = Server.GrowthRoadServer:GetWeaponStar(itemData.id, "star_num")
                    local data2 = Server.GrowthRoadServer:GetWeaponStar(itemData.id, "cur_kill_count")
                    local data3 = Server.GrowthRoadServer:GetWeaponStar(itemData.id, "old_star_num")
                    local data4 = Server.GrowthRoadServer:GetWeaponStar(itemData.id, "old_kill_count")
                    if data1 == 0 and data2 == 0 and data3 == 0 and data4 == 0 then
                        oldStartNum = starNum
                        oldKillNum = killNum
                    end
                    --继续结算表现
                    if starNum and killNum and killMax and oldStartNum and oldKillNum then
                        addKillNum = (starNum - oldStartNum) * killMax + killNum - oldKillNum
                    end
                    --she2重复bug
                    if self._weaponTips == nil then
                        self._weaponTips = {}
                    end
                    if self._weaponTips[itemData.id] == nil then
                        self._weaponTips[itemData.id] = weapon.expOverflowTips
                    end
                    weapon.expOverflowTips = {}
                    for index, value in ipairs(self._weaponTips[itemData.id] or {}) do
                        table.insert(weapon.expOverflowTips, value)
                    end
                    table.insert(weapon.expOverflowTips, {textContent = StringUtil.Key2StrFormat(Module.Settlement.Config.Loc.NumberOfVictories, {num = addKillNum}), styleRowId = "C000"})
                    --控件初始化
                    self._wtCurWeaponLevelTB:SetText(StringUtil.Key2StrFormat(Module.Settlement.Config.Loc.FirearmsStarRating, {num = oldStartNum}))
                    self._wtEndWeaponLevelTB:SetText(starNum)
                    self._wtCurAddExpTB:SetText(string.format(Module.Settlement.Config.Loc.plusSignText, addKillNum or ""))
                    self._wtSharkIcon:SelfHitTestInvisible()
                    self._wtArrowImg:SelfHitTestInvisible()
                    self._wtEndWeaponLevelTB:SelfHitTestInvisible()
                    self:_KillimgAnim(starNum ~= oldStartNum, oldKillNum, killNum, killMax)
                    return true
                end
            end
        end
    end
end

function MPWeaponsUpgrade:_KillimgAnim(isStar, oldKillNum, killNum, killMax)
    if oldKillNum and killNum and killMax then
        local num1 = oldKillNum
        local num2 = isStar and killNum + killMax or killNum
        local length = num2 - num1
        length = length/50
        local SetText = function(min, max)
            if min and max then
                local count = min/max
                local num = math.floor(min)
                if count > 1 then
                    num = math.floor(min - max)
                end
                self._wtExpPBTB:SetText(string.format(Module.Settlement.Config.Loc.ColorQuality, num or 0, max or 0))
                self._wtExpPBImg:SetPercent(count > 1 and count - 1 or count)
                if self._length and self._wtExpPBLine then
                    self._wtExpPBLine:SetPosition(FVector2D(self._length*(count > 1 and count - 1 or count), 0))
                end
            end
        end
        if length == 0 then
            SetText(killNum, killMax)
            return
        end
        self:PlayAnimation(self.WBP_Settlement_Battle_WeaponUpgrades_Flush, 0, 0, EUMGSequencePlayMode.Forward, 1, true)
        Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UISettlementBarLoop)

        local func = function ()
            if num1 and num2 and length and killMax and killNum then
                if num1 >= num2 then
                    if self._ainTimer then
                        self._ainTimer:Stop()
                        self._ainTimer:Release()
                    end
                    SetText(killNum, killMax)
                    self:StopWidgetAnim(self.WBP_Settlement_Battle_WeaponUpgrades_Flush)
                    Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.UISettlementBarLoop)
                    return
                end
                num1 = num1 + length
                SetText(num1, killMax)
            else
                if killMax and killNum then
                    SetText(killNum, killMax)
                end
                self:StopWidgetAnim(self.WBP_Settlement_Battle_WeaponUpgrades_Flush)
                Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.UISettlementBarLoop)
                if self._ainTimer then
                    self._ainTimer:Stop()
                    self._ainTimer:Release()
                end
            end
        end
        if self._ainTimer == nil then
            self._ainTimer = Timer:NewIns(0.02, 0)
            self._ainTimer:AddListener(func, self)
        end
        if self._ainTimer then
            self._ainTimer:Start()
        end
    end
end

return MPWeaponsUpgrade