----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCommonTips)
----- LOG FUNCTION AUTO GENERATE END -----------



local CommonTipsModule = class("CommonTipsModule", require("DFM.YxFramework.Managers.Module.ModuleBase"))
local CommonTipsLogic = require "DFM.Business.Module.CommonTipsModule.CommonTipsLogic"
local CommonTipsLoadLogic = require "DFM.Business.Module.CommonTipsModule.Logic.CommonTipsLoadLogic"
local CommonTipsHDLogic = require "DFM.Business.Module.CommonTipsModule.Logic.HD.CommonTipsHDLogic"

local UIManager = Facade.UIManager


function CommonTipsModule:Ctor()
end

------------------------------------ Override function ------------------------------------
---declare values
function CommonTipsModule:OnInitModule()
	CommonTipsLogic.AddEventListener()
end

---loadBPRes、TextureRes、private table
function CommonTipsModule:OnLoadModule()
end

function CommonTipsModule:OnDestroyModule()
	CommonTipsLogic.RemoveEventListener()
end

---@param gameFlowType EGameFlowStageType
function CommonTipsModule:OnGameFlowChangeLeave(gameFlowType)
	CommonTipsLogic.DoCommonPopTipClose()
end

function CommonTipsModule:CreateCommonPanelUI(msg, hopeType)
	if hopeType == 2 then
		local function confirmHandle()
			local var = nil
			loginfo("QuitGameTips")
			local UICacheList = {UIUtil.CreateCacheInfo(UIName2ID.LoginInterface)}
			UIManager:BatchShowStackUI(UICacheList)
			Facade.UIManager:CloseUI(self)
			Facade.ProtoManager:TryDisConnectServer(true)
		end
		Module.CommonTips:ShowConfirmWindow(msg, confirmHandle, confirmHandle, Module.VersionUpdate.Config.Loc.UpdateConfirmWindowConfirm, "-1")
    else
		Module.CommonTips:ShowConfirmWindow(msg, nil, nil ,Module.VersionUpdate.Config.Loc.UpdateConfirmWindowConfirm, "-1")
	end
end

---------------------------------------------------------------------------------
--- Loading 生命周期
--- 设置bAutoLoading = true则下列生命周期有效
---------------------------------------------------------------------------------
---@overload fun(ModuleBase, OnLoadingLogin2Frontend)
function CommonTipsModule:OnLoadingLogin2Frontend(gameFlowType)
    CommonTipsLoadLogic.OnLoadingLogin2FrontendProcess(gameFlowType)
end

---@overload fun(ModuleBase, OnLoadingGame2Frontend)
function CommonTipsModule:OnLoadingGame2Frontend(gameFlowType)
    CommonTipsLoadLogic.OnLoadingGame2FrontendProcess(gameFlowType)
end

---@overload fun(ModuleBase, OnLoadingFrontend2Game)
function CommonTipsModule:OnLoadingFrontend2Game(gameFlowType)
    CommonTipsLoadLogic.OnLoadingFrontend2GameProcess(gameFlowType)
end

------------------------------------ public function ------------------------------------
-- 通用Tips弹窗
-- text:提示文字
-- duration：浮动时间,默认2s
function CommonTipsModule:ShowSimpleTip(text, duration, bPositive, bDFMShow, bDFHDShow)
	CommonTipsLogic.DoCommonPopTipShow(text, duration, bPositive, bDFMShow, bDFHDShow)
end

function CommonTipsModule:HideActiveTips()
    CommonTipsLogic.HideActiveTips()
end

function CommonTipsModule:ShowTipWithGrid(text, duration, length, width, bPositive, bDFMShow, bDFHDShow)
	CommonTipsLogic.DoCommonPopTipShowWithGrid(text, duration, true, length, width, bPositive, bDFMShow, bDFHDShow)
end

function CommonTipsModule:CloseSimpleTip()
	CommonTipsLogic.DoCommonPopTipClose()
end

-- 局内指定下一次显示的tip的样式-端游
-- bPopTips：是否使用局外用tip层tips
-- bPopCenter：使用局外用tip层tips时是否使用屏幕正中样式
function CommonTipsModule:AssignNextTipType_HD(bPopTips, bPopCenter)
	CommonTipsLogic.AssignNextTipType_HD(bPopTips, bPopCenter)
end

-- 通用强提醒Tips弹窗
-- text：提示文字
-- caller：调用ui
-- bCloseByCaller：是否随着caller关闭强提醒Tips弹窗
function CommonTipsModule:ShowStrongTip(text, caller, bCloseByCaller, delayTime, delayCheckFunc)
	local fShowFunc = function ()
		if not caller or hasdestroy(caller) then
			logwarning("CommonTipsModule:ShowStrongTip caller is nil!!!")
			return
		end
		if delayCheckFunc ~= nil and not delayCheckFunc then
			logwarning("CommonTipsModule:ShowStrongTip delayCheckFunc is nil or delayCheckFunc return false")
			return
		end
		bCloseByCaller = setdefault(bCloseByCaller, true)
		Module.CommonTips.Field:CheckAndClearStrongTipHandle()
		local strongTipText = Module.CommonTips.Field:GetStrongTipText() or ""
		local strongTipHandle = CommonTipsLogic.DoCommonStrongPopTipShow(strongTipText, caller, bCloseByCaller)
		Module.CommonTips.Field:SetStrongTipHandle(strongTipHandle)
	end
	Module.CommonTips.Field:SetStrongTipText(text)
	if delayTime then
		Timer.DelayCall(delayTime, fShowFunc, self)
	else
		fShowFunc()
	end

end

function CommonTipsModule:ModifyStrongTipText(text)
	Module.CommonTips.Field:SetStrongTipText(text)
	local strongTipHandle = Module.CommonTips.Field:GetStrongTipHandle()
	if strongTipHandle then
		local uiIns = strongTipHandle:GetUIIns()
		uiIns:SetContentText(text)
		uiIns:StopAllAnimations()
		uiIns:PlayAnimation(uiIns.WBP_CommonStrongPopTips_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
	end
end

-- 关闭通用强提醒Tips弹窗
function CommonTipsModule:CloseStrongTip()
	CommonTipsLogic.DoCommonStrongPopTipClose()
end

-- 确认弹出框界面
-- text：弹框显示内容
-- confirmHandle：确认按钮处理逻辑
-- cancelHandle：取消按钮处理逻辑
-- cancelText：取消按钮文字
-- confirmText：确认按钮文字
-- confirmSound: [aidenliao]确认按钮音效重载，目前仅AuctionMySaleItem中使用到
-- cancelSound(备用)
-- stateInGuide: EStateInGuide。引导期间弹出弹窗，会·对引导造成的影响。默认为暂停引导，弹窗关闭后继续引导
-- fFinishCallback：ui创建完成后回调 ，默认为nil
-- bIsRecharge: 是否是充值弹窗（涉及美国地区合规功能）
-- detailText: 详情文本
function CommonTipsModule:ShowConfirmWindow(
	text,
	confirmHandle,
	cancelHandle,
	cancelText,
	confirmText,
	confirmSound,
    cancelSound,
	stateInGuide,
	checkBoxText,
	fFinishCallback,
	bIsRecharge,
	detailText)
	return Facade.UIManager:AsyncShowUI(
		UIName2ID.ConfirmWindows,
		fFinishCallback,
		nil,
		text,
		nil,
		confirmHandle,
		cancelHandle,
		cancelText,
		confirmText,
		confirmSound,
        cancelSound,
		stateInGuide,
		checkBoxText,
		nil,
		nil,
		bIsRecharge,
		nil,
		detailText
	)
end

-- 确认弹出框界面
-- text：弹框显示内容
-- noticeText：备注显示内容
-- confirmHandle：确认按钮处理逻辑
-- cancelHandle：取消按钮处理逻辑
-- cancelText：取消按钮文字
-- confirmText：确认按钮文字
-- confirmSound: [aidenliao]确认按钮音效重载，目前仅AuctionMySaleItem中使用到
-- cancelSound(备用)
function CommonTipsModule:ShowConfirmWindowWithNotice(
	text,
	noticeText,
	confirmHandle,
	cancelHandle,
	cancelText,
	confirmText,
	confirmSound,
    cancelSound)
	return Facade.UIManager:AsyncShowUI(
		UIName2ID.ConfirmWindows,
		nil,
		nil,
		text,
		noticeText,
		confirmHandle,
		cancelHandle,
		cancelText,
		confirmText,
		confirmSound,
        cancelSound
	)
end

function CommonTipsModule:ShowConfirmWindowWithSingleBtn(
	text,
	confirmHandle,
	confirmText,
	confirmSound,
	ignoreGc,
	bIsBlock)
	return Facade.UIManager:AsyncShowUI(
		UIName2ID.ConfirmWindows,
		nil,
		nil,
		text,
		nil,
		confirmHandle,
		nil,
		"-1",
		confirmText,
		confirmSound,
        nil,
		nil,
		nil,
		ignoreGc,
		bIsBlock
	)
end

function CommonTipsModule:ShowConfirmWindowWithSingleBtnAlwaysCallback(
	text,
	confirmHandle,
	confirmText,
	confirmSound,
	ignoreGc)
	return Facade.UIManager:AsyncShowUI(
		UIName2ID.ConfirmWindows,
		nil,
		nil,
		text,
		nil,
		confirmHandle,
		confirmHandle,
		"-1",
		confirmText,
		confirmSound,
        nil,
		nil,
		nil,
		ignoreGc
	)
end

function CommonTipsModule:ShowConfirmWindowByCpp(
	text,
	obj,
	confirmFuncName,
	cancelFuncName,
	cancelText,
	confirmText,
	confirmSound)
	
	local confirmHandle = function(...)
		obj[confirmFuncName](...)
	end

	local cancelHandle = function(...)
		obj[cancelFuncName](...)
	end

	Module.CommonTips:ShowConfirmWindow(	
	text,
	confirmHandle,
	cancelHandle,
	cancelText,
	confirmText,
	confirmSound)
end

-- 确认弹出框界面
-- title：弹框标题文字
-- text：弹框显示内容
-- confirmHandle：确认按钮处理逻辑
-- cancelHandle：取消按钮处理逻辑
-- cancelText：取消按钮文字
-- confirmText：确认按钮文字
-- onlyConfirmBtn : 是否仅显示确认按钮
function CommonTipsModule:ShowConfirmBasicWindow(
	text,
	confirmHandle,
	cancelHandle,
	cancelText,
	confirmText,
	onlyConfirmBtn)
	return Facade.UIManager:AsyncShowUI(
		UIName2ID.ConfirmBasicWindows,
		nil,
		nil,
		text,
		confirmHandle,
		cancelHandle,
		cancelText,
		confirmText,
		onlyConfirmBtn
	)
end

-- 确认弹出框界面
-- title：弹框标题文字
-- text：弹框显示内容
-- confirmHandle：确认按钮处理逻辑
-- cancelHandle：取消按钮处理逻辑
-- cancelText：取消按钮文字
-- confirmText：确认按钮文字
-- itemIdNumList：item信息列表
function CommonTipsModule:ShowConfirmItemsWindow(
	text,
	confirmHandle,
	cancelHandle,
	cancelText,
	confirmText,
	itemIdNumList,
	bNeedClose)
	Facade.UIManager:AsyncShowUI(
		UIName2ID.ConfirmItemsWindow,
		nil,
		nil,
		text,
		confirmHandle,
		cancelHandle,
		cancelText,
		confirmText,
		itemIdNumList,
		bNeedClose
	)
end

-- 网络连接提示界面
-- bShow：是否显示
-- delay：网络连接文字延迟显示时间（s）
function CommonTipsModule:ShowNetworkLoading(bShow, delay)
	CommonTipsLogic.NetworkLoading(bShow, delay)
end

-- 自动移除透明遮罩层
-- delay：自动移除时间(s)
function CommonTipsModule:ShowDelayWindow(delay)
	CommonTipsLogic.DelayWindow(delay)
end

function CommonTipsModule:CloseDelayWindow()
	CommonTipsLogic.CloseDelayWindow()
end

-- 获取错误码对应文字
-- errorCode：错误码
function CommonTipsModule:GetErrCode2String(errorCode)
	return self.Field:GetErrCode2String(errorCode)
end

-- 纯文本描述弹窗
-- parentWidget: 用于计算相对位置
-- title：弹框标题文字
-- text：弹框显示内容
-- bAlignBorder：默认对齐alignWidget的上边界，为true时，对齐安全区的上下边界，仅限手游
-- bSourceOrUsePlace: 来源与用途高度特殊处理
function CommonTipsModule:ShowDescriptionWindow(parentWidget, title, content, dataList, alignWidget, subtitle, bAlignBorder, loadCallBack, bSourceOrUsePlace)
	return CommonTipsLogic.ShowDescriptionWindow(parentWidget, title, subtitle, content, dataList, alignWidget, bAlignBorder, loadCallBack, bSourceOrUsePlace)
end

function CommonTipsModule:CloseDescriptionWindow()
	CommonTipsLogic.CloseDescriptionWindow()
end

function CommonTipsModule:ShowReplayPurchaseView(Desc, StartTimestamp, TimeLimit, PurchaseConfirmTips, GiveupConfirmTips, OnGiveupReplyDelegate, OnConfirmReplayDelegate)
	CommonTipsLogic.ShowReplayPurchaseView(Desc, StartTimestamp,TimeLimit, PurchaseConfirmTips, GiveupConfirmTips, OnGiveupReplyDelegate, OnConfirmReplayDelegate)
end

-- function CommonTipsModule:ShowCommonMessageView(parentWidget, content, alignWidget)
-- 	return CommonTipsLogic.ShowCommonMessageView(parentWidget, content, alignWidget)
-- end

function CommonTipsModule:ShowCommonMessagesView(parentWidget, datas, alignWidget)
	return CommonTipsLogic.ShowCommonMessagesView(parentWidget, datas, alignWidget)
end

function CommonTipsModule:ShowCommonMessageWithAnchor(content, tipsAnchor)
	return CommonTipsLogic.ShowCommonMessageWithAnchor(content, tipsAnchor)
end

function CommonTipsModule:CppShowCommonMessageWithAnchor(textArg, tipsAnchor)
	local content = {}
    table.insert(content, {textContent = textArg})
	if self.cppCommonMessageTipHandle ~= nil then
		self:CppRemoveCommonMessageWithAnchor(tipsAnchor)
	end
	self.cppCommonMessageTipHandle = CommonTipsLogic.ShowCommonMessageWithAnchor(content, tipsAnchor)
end

function CommonTipsModule:ShowCommonMessagesWithAnchor(datas, tipsAnchor)
	return CommonTipsLogic.ShowCommonMessagesWithAnchor(datas, tipsAnchor)
end

function CommonTipsModule:ShowCommonLoadTipsWithAnchor(tipsAnchor)
	return CommonTipsLogic.ShowCommonLoadTipsWithAnchor(tipsAnchor)
end

function CommonTipsModule:RemoveCommonMessageWithAnchor(handle, tipsAnchor)
	return CommonTipsLogic.RemoveCommonMessageWithAnchor(handle, tipsAnchor)
end

function CommonTipsModule:CppRemoveCommonMessageWithAnchor(tipsAnchor)
	if self.cppCommonMessageTipHandle ~= nil then
		self.cppCommonMessageTipHandle = nil
		return CommonTipsLogic.RemoveCommonMessageWithAnchor(self.cppCommonMessageTipHandle, tipsAnchor)
	end
	return nil
end

function CommonTipsModule:ShowTipsFromTemplate(tipsAnchor)
	return CommonTipsLogic.ShowTipsFromTemplate(tipsAnchor)
end

function CommonTipsModule:RemoveTipsFromTemplate(handle, tipsAnchor)
	CommonTipsLogic.RemoveTipsFromTemplate(handle, tipsAnchor)
end

function CommonTipsModule:ShowAssembledTips(contents, tipsAnchor, maxDesiredWidth, loadCallBack)
	return CommonTipsLogic.ShowAssembledTips(contents, tipsAnchor, maxDesiredWidth, loadCallBack)
end

function CommonTipsModule:RemoveAssembledTips(handle, tipsAnchor)
	CommonTipsLogic.RemoveAssembledTips(handle, tipsAnchor)
end

function CommonTipsModule:CloseReplayPurchaseView()
	if self.Field.purchaseReplayView then
		self.Field.purchaseReplayView:CloseView()
	end
end

--------------------------------------------时间更新tips 2025.2.19 ssy--------------------------------
function CommonTipsModule:ShowTimeAssembledTips(contents, tipsAnchor, callback, maxDesiredWidth)
	return CommonTipsLogic.ShowTimeAssembledTips(contents, tipsAnchor, callback, maxDesiredWidth)
end

function CommonTipsModule:RemoveTimeAssembledTips(handle, tipsAnchor)
	CommonTipsLogic.RemoveTimeAssembledTips(handle, tipsAnchor)
end
----------------------------------------------------------------------------------------------------

-- 确认弹出框界面
-- text：弹框显示内容
-- string：选择数量占比显示内容
-- Number：数量默认值
-- Number：数量最大值
-- confirmHandle：确认按钮处理逻辑
-- cancelHandle：取消按钮处理逻辑
-- cancelText：取消按钮文字
-- confirmText：确认按钮文字
-- confirmSound: [aidenliao]确认按钮音效重载，目前仅AuctionMySaleItem中使用到
-- cancelSound(备用)
function CommonTipsModule:ShowAddDecSliderConfirmWindow(
	text,
	numText,
	defaultNum,
	maxNum,
	confirmHandle,
	cancelHandle,
	cancelText,
	confirmText,
	confirmSound,
    cancelSound)
	return Facade.UIManager:AsyncShowUI(
		UIName2ID.ConfirmAddDecSliderWindows,
		nil,
		nil,
		text,
		numText,
		defaultNum,
		maxNum,
		confirmHandle,
		cancelHandle,
		cancelText,
		confirmText,
		confirmSound,
        cancelSound
	)
end


-- 确认弹出框界面 网络专用
function CommonTipsModule:ShowReconnectConfirmWindow(
	text,
	confirmHandle,
	cancelHandle,
	cancelText,
	confirmText,
	confirmSound,
    cancelSound,
	stateInGuide,
	checkBoxText,
	bSkipExcute)
	return Facade.UIManager:AsyncShowUI(
		UIName2ID.ConfirmWindows,
		nil,
		nil,
		text,
		nil,
		confirmHandle,
		cancelHandle,
		cancelText,
		confirmText,
		confirmSound,
        cancelSound,
		stateInGuide,
		checkBoxText,
		nil,
		nil,
		bSkipExcute
	)
end

-- function CommonTipsModule:ShowCommonMessageTips(loadFinCall, data)
-- 	local handle = CommonTipsLogic.ShowCommonMessageTips(loadFinCall, data)
-- 	return handle
-- end

-- function CommonTipsModule:RemoveCommonMessageTips(handle)
-- 	CommonTipsLogic.RemoveCommonMessageTips(handle)
-- end

-----------------------------------------------------------------------
--region HDTips
--- @class TipsInputSummary
--- @field actionName string
--- @field func func|nil
--- @field caller table|nil

--- @class CommonHoverTipsHDData
--- @field title string|nil
--- @field content string|nil
--- @field summaryList TipsInputSummary[]|nil
--- @field smallTip string|nil
--- @field optionalStr string|nil 如果传入会与Action对应的DisplayName 进行format操作

--- @param data CommonHoverTipsHDData
-- function CommonTipsModule:ShowCommonHoverTipsHD(data, tipsAnchor)
--     return CommonTipsHDLogic.ShowCommonHoverTipsHD(data, tipsAnchor)
-- end

-- function CommonTipsModule:RemoveCommonHoverTipsHD(handle, tipsAnchor)
--     CommonTipsHDLogic.RemoveCommonHoverTipsHD(handle, tipsAnchor)
-- end

-- function CommonTipsModule:HideCommonHoverTipsHD(handle, tipsAnchor)
--     CommonTipsHDLogic.HideCommonHoverTipsHD(handle, tipsAnchor)
-- end

-- function CommonTipsModule:RecoverCommonHoverTipsHD(handle, tipsAnchor)
--     CommonTipsHDLogic.RecoverCommonHoverTipsHD(handle, tipsAnchor)
-- end


--endregion
-----------------------------------------------------------------------

return CommonTipsModule
