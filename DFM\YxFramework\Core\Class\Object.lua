-- 类系统里最基本的类
---@class Object
Object = {}
Object.__index = Object
Object._meta_ = Object
Object._cname = "Object"

-- function Object:Ctor()
-- end

-- function Object:Destroy()
-- end

--- 判断是否是该类的子类
function Object:IsChildOf(parent)
    if rawget(self, "_meta_") == nil then
        -- error("not class")
        return false
    end
    local direct_parent = self:Super()
    if direct_parent == parent then
        return true
    elseif direct_parent then
        return direct_parent:IsChildOf(parent)
    else
        return false
    end
end

--- 获取类的父类
function Object:Super()
    local meta = self._meta_
    return meta and meta._parentclass
end

--- 获取类
function Object:Class()
    local meta = self._meta_
    return meta
end

function Object:ClassName()
    local meta = self._meta_
    return meta:ToString()
end

function Object:ToString()
    return self._cname
end
-----------------------------------------------------------------------
-- region Record object creation

local records = {}
local bEnableObjRecord = false

function getInheritChain(cls)
    local result = cls._cname
    local super = cls._parentclass
    while super do
        result = result .. " -> " .. super._cname
        super = super._parentclass
    end

    return result
end

function toggleRecords(bToggle)
    bEnableObjRecord = bToggle
end

function dumpRecords()
    local keys = table.keys(records)
    table.sort(keys, function(lhs, rhs)
        return records[lhs] > records[rhs]
    end)

    loginfo("[Dump all lua object's creation during sample time]")
    loginfo(string.format("%-30s\t%-10s\t%s", "Lua Object Name", "Number", "Inherit Chain"))
    for _, k in ipairs(keys) do
        loginfo(string.format("%-30s\t%-10d\t%s", k._cname, records[k], getInheritChain(k)))
    end
end

function clearRecords()
    records = {}
end

-- endregion
-----------------------------------------------------------------------



--- 创建类的实例，但不走Ctor
function Object:Ins(...)
    local newIns = setmetatable({
        _meta_ = self,
        _has_destroy_ = false,
        _cname = self._cname
    }, self)

    -- if bEnableObjRecord then
    --     local num = records[self]
    --     if num then
    --         records[self] = num + 1
    --     else
    --         records[self] = 1
    --     end
    -- end

    return newIns
end

-- 创建类的一个实例，...走Ctor，参数会传到构造函数Ctor里
---@generic T
---@param  self T
---@param ... unknown
---@return T
function Object:NewIns(...)
    local ins = self:Ins(...)
    ins:Initialize(...)
    return ins
end

function Object:NewFromOutIns(ins, ...)
    ins._meta_ = self
    ins._has_destroy_ = false
    ins._cname = self._cname
    setmetatable(ins, self)

    ins:Initialize(...)
    return ins
end

--- 递归调用Ctor
function Object:Initialize(...)
    local ctorList = get_ctor_list(self._meta_)
    for _, ctor in ipairs(ctorList) do
        ctor(self, ...)
    end

    -- ctor_recursively(self._meta_, self, ...)
end

function Object:GetCppInst()
    return self.__cppinst
end

function Object:IsRelease()
    if hasdestroy(self) then
        return true
    end
    return false
end

--- 递归调用Destroy
--- Release一个对象，需要手动调用，...会当成参数传入Destroy函数
function Object:Release()
    if self:IsRelease() then
        return
    end

    --- 顺序由子到父版本 3.0
    local destroyList = get_destroy_list(self._meta_)
    if next(destroyList) then
        for i = -#destroyList, -1 do
            local order = - i
            local destroyFunc = destroyList[order]
            if destroyFunc then
                destroyFunc(self)
            end
        end
    end
    
    --- 顺序由父到子版本 2.0 (有问题)
    -- for _, destroy in ipairs(destroyList) do
    --     destroy(self)
    -- end

    --- 顺序由子到父版本 1.0
    -- intern_destroy(self._meta_, self, ...)

    rawset(self, "_has_destroy_", true)
end

function Object:SetIsIgnoreWorldForceGc(bIgnoreWorldForceGc)
	bIgnoreWorldForceGc = setdefault(bIgnoreWorldForceGc, false)
	rawset(self, '_bIgnoreWorldForceGc', bIgnoreWorldForceGc)
end

--- check if this obj can survival across gc
function Object:GetIsIgnoreWorldForceGc()
    return rawget(self, "_bIgnoreWorldForceGc")
end

function Object:ForwardClassRawget(k)
    if self._meta_ == nil then
        return nil
    end

    local ret = rawget(self._meta_, k)
    if ret ~= nil then
        return ret
    end

    local wrapper_meta = rawget(self._meta_, "__meta__")
    if type(wrapper_meta) == "table" then
        return rawget(wrapper_meta, k)
    end
    return nil
end

function Object:ForwardClassRawset(k, v)
    if self._meta_ == nil then
        return
    end

     local wrapper_meta = rawget(self._meta_, "__meta__")
    if type(wrapper_meta) == "table" then
        rawset(wrapper_meta, k, v)
        return
    end

    rawset(self._meta_, k, v)
end

Object.New = Object.NewIns

return Object