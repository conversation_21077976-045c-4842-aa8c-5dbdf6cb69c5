----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMItemDetail)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class ItemDetailAdapterItemSimple : LuaUIBaseView
local ItemDetailAdapterItemSimple = ui("ItemDetailAdapterItemSimple")
local RuntimeIconTool = require "DFM.StandaloneLua.BusinessTool.RuntimeIconTool"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"

function ItemDetailAdapterItemSimple:Ctor()
	self._wtItemImage = self:Wnd("wItemIcon", UIImage)
end

-- function ItemDetailAdapterItemSimple:OnOpen()
-- end

-- function ItemDetailAdapterItemSimple:OnClose()
-- end

-- function ItemDetailAdapterItemSimple:OnShow()
-- end

-- function ItemDetailAdapterItemSimple:OnHide()
-- end

function ItemDetailAdapterItemSimple:ReInit(partSocket)
	self:OnInitExtraData(partSocket)
end

function ItemDetailAdapterItemSimple:OnInitExtraData(partSocket)
	if not partSocket then
		logerror("ItemDetailAdapterItemSimple:OnInitExtraData partSocket is nil!")
		return
	end

	local IsShowPart = partSocket:IsShowPart()
	if IsShowPart then
		local aiid = partSocket:GetAttachItemId()
		local Desc = WeaponAssemblyTool.ItemId_To_NewDesc(aiid)
		self._wtItemImage.Brush.ImageSize = FVector2D(64, 64)
		RuntimeIconTool.SetIconByDesc(Desc, self._wtItemImage)
	else
		self._wtItemImage.Brush.ImageSize = FVector2D(140, 140)
		RuntimeIconTool.SetStaticIcon(partSocket:GetIconPath(), self._wtItemImage)
	end

	self:SetCppValue("BpIsEquip", IsShowPart)

	if partSocket:GetSocketPartType() == ItemConfig.EAdapterItemType.Attacker then
		self:SetCppValue("BpStyle", 1)
	else
		self:SetCppValue("BpStyle", 0)
	end
	self:SetStyle()
end

return ItemDetailAdapterItemSimple
