----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGunsmith)
----- LOG FUNCTION AUTO GENERATE END -----------



--------------------------------------------------------------------------
--- UI路径映射配置示例（UILayer表示需要加入的层级）
--------------------------------------------------------------------------
UITable[UIName2ID.GunsmithMainUI] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.GunsmithMainUI",
    BPKey = "WBP_GunStand_GunsmithMainUI",
    SubUIs = {
        UIName2ID.GunsmithMainPreviewUI,
        UIName2ID.GunsmithFinetuneMainUI,
		UIName2ID.GunsmithMainAppearanceUI,
        UIName2ID.GunsmithSolutionMainUI,
        UIName2ID.WeaponUpgradePanel,
    },
    ReConfig = {
        IsPoolEnable = true,
    },
    LinkSubStage = ESubStage.WeaponAssemble,
}

UITable[UIName2ID.GunsmithMainPreviewUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.GunsmithMainPreviewUI",
    BPKey = "WBP_GunStand_GunsmithMainPreviewUI",
    SubUIs = {
        UIName2ID.GunsmithSceneSocketMainUI,
        UIName2ID.GunsmithDetailPreviewMainUI,
    },
    Anim = {
        FlowInAni = "WBP_GunStand_GunsmithMainPreviewUI_in",
        FlowOutAni = "WBP_GunStand_GunsmithMainPreviewUI_out"
    },
}

UITable[UIName2ID.GunsmithSceneSocketMainUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.SceneSocket.GunsmithSceneSocketMainUI",
    BPKey = "WBP_GunStand_GunsmithSceneSocketMainUI",
    SubUIs = {
        UIName2ID.GunsmithSceneSocketItemUI,
        UIName2ID.GunsmithSceneSocketTextItemUI
    }
}

UITable[UIName2ID.GunsmithSceneSocketPartUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.SceneSocket.GunsmithSceneSocketPartUI",
    BPKey = "WBP_GunStand_GunsmithSceneSocketPartUI",
    SubUIs = {
        UIName2ID.GunsmithSceneSocketItemUI
    }
}

UITable[UIName2ID.GunsmithSceneSocketItemUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.SceneSocket.GunsmithSceneSocketItemUI",
    BPKey = "WBP_GunStand_GunsmithSceneSocketItemUI",
    Anim = {
        -- FlowInAni = "WBP_GunStandBox_in0",
        -- FlowOutAni = "WBP_GunStandBox_out0"
    },
}

UITable[UIName2ID.GunsmithSceneSocketTextItemUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.SceneSocket.GunsmithSceneSocketTextItemUI",
    BPKey = "WBP_Collections_Fittings",
    Anim = {},
}

UITable[UIName2ID.GunsmithMissionUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Mission.GunsmithMissionUI",
    BPKey = "WBP_GunStand_Misson",
    SubUIs = {
        UIName2ID.GunsmithMissionItemUI
    }
}

UITable[UIName2ID.GunsmithMissionItemUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Mission.GunsmithMissionItemUI",
    BPKey = "WBP_GunStand_MissonNeed"
}

UITable[UIName2ID.GunsmithPartMainUI] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Part.GunsmithPartMainUI",
    BPKey = "WBP_GunStand_GunsmithPartMainUI",
    SubUIs = {
        UIName2ID.GunsmithPartMainItemUI,
        UIName2ID.GunsmithSceneSocketPartUI,
        UIName2ID.GunsmithDetailPartMainUI,
        UIName2ID.GunsmithDebugContextUI,
        UIName2ID.GunsmithDebugPartListUI,
        UIName2ID.GunsmithDetailScopeUI,
        UIName2ID.CommonEmptyContent,
    },
    ReConfig = {
        IsPoolEnable = true,
    },
    LinkSubStage = ESubStage.WeaponAssemble,
}

UITable[UIName2ID.GunsmithPartMainItemUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Part.GunsmithPartMainItemUI",
    BPKey = "WBP_GunStand_GunsmithPartMainItemUI",
    SubUIs = {
        UIName2ID.IVTextIconComponent,
        UIName2ID.IVTextQualityComponent,
        UIName2ID.IVItemSelectedComponent,
        UIName2ID.IVMaskLockComponent,
        UIName2ID.IVUsingComponent,
        UIName2ID.IVShowDefaultUsingComponent,
        UIName2ID.IVMissionComponent
    }
}

UITable[UIName2ID.GunsmithPartFilterUI] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Part.GunsmithPartFilterUI",
    BPKey = "WBP_GunStand_GunsmithPartWindow",
    SubUIs = {
        UIName2ID.GunsmithPartFilterCheckBoxWithTextUI,
    },
    IsModal = true,
}

UITable[UIName2ID.GunsmithPartFilterCheckBoxWithTextUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Part.GunsmithPartFilterCheckBoxWithTextUI",
    BPKey = "WBP_DFCommonCheckBoxWithText",
}

UITable[UIName2ID.GunsmithSolutionMainItemUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Solution.GunsmithSolutionMainItemUI",
    BPKey = "WBP_GunStand_GunsmithSolutionMainItemUI",
    SubUIs = {
        UIName2ID.IVShowDefaultUsingComponent,
        UIName2ID.IVTextQualityComponent,
    }
}

UITable[UIName2ID.GunsmithSolutionMainTabItemUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Solution.GunsmithSolutionMainTabItemUI",
    BPKey = "WBP_GunStand_GunsmithSolutionMainTabItemUI",
    SubUIs = {
    }
}

UITable[UIName2ID.GunsmithSolutionMainUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Solution.GunsmithSolutionMainUI",
    BPKey = "WBP_GunStand_GunsmithSolutionMainUI",
    SubUIs = {
        UIName2ID.GunsmithDetailPartMainUI,
        -- UIName2ID.GunsmithSolutionMainItemUI,
        UIName2ID.GunsmithSolutionMainTabItemUI,
    },
    ReConfig = {
        IsPoolEnable = true,
    },
    LinkSubStage = ESubStage.WeaponAssemble,
}

UITable[UIName2ID.GunsmithSolutionSaveConfirmUI] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Solution.GunsmithSolutionSaveConfirmUI",
    BPKey = "WBP_GunStand_GunsmithSolutionSaveConfirmUI",
    SubUIs = {
        UIName2ID.GunsmithSolutionSaveConfirmItemUI
    },
    IsModal = true,
}

UITable[UIName2ID.GunsmithSolutionSaveConfirmItemUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Solution.GunsmithSolutionSaveConfirmItemUI",
    BPKey = "WBP_GunStand_GunsmithSolutionSaveConfirmItemUI"
}

UITable[UIName2ID.GunsmithSolutionShareUI] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Solution.GunsmithSolutionShareUI",
    BPKey = "WBP_GunStand_GunsmithSolutionShareUI",
    SubUIs = {
        UIName2ID.ItemDetailAdapterItem,
        UIName2ID.CommonEmptyContent,
    },
    IsModal = true,
}

-- 武器同步功能已挂起 start---
-- UITable[UIName2ID.GunsmithSolutionSynchronizationUI] = {
--     UILayer = EUILayer.Pop,
--     LuaPath = "DFM.Business.Module.GunsmithModule.UI.Solution.GunsmithSolutionSynchronizationUI",
--     BPKey = "WBP_GunStand_GunsmithSolutionSynchronization",
--     SubUIs = {
--         UIName2ID.GunsmithSolutionSynchronizationItemUI,
--     },
--     IsModal = true,
-- }

-- UITable[UIName2ID.GunsmithSolutionSynchronizationItemUI] = {
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.GunsmithModule.UI.Solution.GunsmithSolutionSynchronizationItemUI",
--     BPKey = "WBP_GunStand_GunsmithSolutionSynchronizationItem"
-- }
-- 武器同步功能已挂起 end---

UITable[UIName2ID.GunsmithMainAppearanceUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.GunsmithMainAppearanceUI",
    BPKey = "WBP_GunStand_GunsmithMainAppearanceUI",
    SubUIs = {
        UIName2ID.GunsmithSkinDefaultMainUI,
        UIName2ID.GunsmithPendantMainUI,
    }
}

UITable[UIName2ID.GunsmithSkinDefaultMainUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Skin.GunsmithSkinDefaultMainUI",
    BPKey = "WBP_GunStand_GunsmithSkinDefaultMainUI",
    SubUIs = {
        UIName2ID.GunsmithSkinDefaultMainItemUI
    }
}

UITable[UIName2ID.GunsmithSkinDefaultMainItemUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Skin.GunsmithSkinDefaultMainItemUI",
    BPKey = "WBP_GunStand_GunsmithSkinDefaultMainItemUI",
    SubUIs = {
        UIName2ID.IVTextIconComponent,
        UIName2ID.IVTextQualityComponent,
        UIName2ID.IVItemSelectedComponent,
        UIName2ID.IVMaskLockComponent,
        UIName2ID.IVUsingComponent,
        UIName2ID.IVShowDefaultUsingComponent,
        UIName2ID.IVRandomSkinComponent,
    }
}

UITable[UIName2ID.GunsmithSkinSolutionUI] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Skin.GunsmithSkinSolutionUI",
    BPKey = "WBP_GunStand_GunsmithSkinRreplacementPartsUI",
    SubUIs = {
        UIName2ID.GunsmithSkinSkinSolutionItemUI,
    },
    IsModal = true,
}

UITable[UIName2ID.GunsmithSkinSkinSolutionItemUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Skin.GunsmithSkinSkinSolutionItemUI",
    BPKey = "WBP_GunStand_GunsmithSkinRreplacementPartsItem",
    SubUIs = {
        UIName2ID.IVTextIconComponent,
    }
}

UITable[UIName2ID.GunsmithPendantMainUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Pendant.GunsmithPendantMainUI",
    BPKey = "WBP_GunStand_GunsmithPendantMainUI",
    SubUIs = {
        UIName2ID.GunsmithPendantMainItemUI,
        UIName2ID.CommonEmptyContent,
    }
}

UITable[UIName2ID.GunsmithPendantMainItemUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Pendant.GunsmithPendantMainItemUI",
    BPKey = "WBP_GunStand_GunsmithPendantMainItemUI",
    SubUIs = {
        UIName2ID.IVTextIconComponent,
        UIName2ID.IVTextQualityComponent,
        UIName2ID.IVItemSelectedComponent,
        UIName2ID.IVMaskLockComponent,
        UIName2ID.IVUsingComponent,
        UIName2ID.IVShowDefaultUsingComponent,
        UIName2ID.IVCommercializeShadingComponent,
    }
}

UITable[UIName2ID.GunsmithDetailPartMainUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Detail.GunsmithDetailPartMainUI",
    BPKey = "WBP_GunStand_GunsmithDetailPartMainUI",
    SubUIs = {
        UIName2ID.ItemDetailTitle,
        UIName2ID.ItemDetailAdapterBuffer,
    }
}

UITable[UIName2ID.GunsmithDetailPreviewMainUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Detail.GunsmithDetailPreviewMainUI",
    BPKey = "WBP_GunStand_GunsmithDetailPreviewMainUI",
    -- SubUIs = {

    -- }
}

UITable[UIName2ID.GunsmithDetailScopeUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Detail.GunsmithDetailScopeUI",
    BPKey = "WBP_GunStand_GunsmithDetailScopeUI"
}

UITable[UIName2ID.GunsmithSimulateStatePropmtUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Simulate.GunsmithSimulateStatePropmtUI",
    BPKey = "WBP_GunStandPresetTips",
    Anim = {
        bManuelAnim = true
    }
}

UITable[UIName2ID.GunsmithSOLInspectorMainUI] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.SOLInspector.GunsmithSOLInspectorMainUI",
    BPKey = "WBP_GunStand_FirearmsMain",
    SubUIs = {
        UIName2ID.GunsmithSOLInspectorMainItemUI,
        UIName2ID.LitePackageCommonDownload,
    },
    ReConfig = {
        IsPoolEnable = true,
    },
    Anim = {
        -- FlowInAni = "WBP_AssemblyFirearmsMain_in",
        -- FlowOutAni = "WBP_AssemblyFirearmsMain_out"
    },
    LinkSubStage = ESubStage.WeaponAssemble,
}

-- 靶场选枪界面
UITable[UIName2ID.GunsmithRangeInspectorMainUI] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.RangeInspector.GunsmithRangeInspectorMainUI",
    BPKey = "WBP_AssemblyFirearmsMain",
    SubUIs = {
        UIName2ID.GunsmithSOLInspectorMainItemUI,
        UIName2ID.LitePackageCommonDownload,
    },
    ReConfig = {
        IsPoolEnable = true,
    },
    Anim = {
        FlowInAni = "WBP_AssemblyFirearmsMain_in",
        FlowOutAni = "WBP_AssemblyFirearmsMain_out"
    },
    LinkSubStage = ESubStage.WeaponAssemble,
}

UITable[UIName2ID.GunsmithSOLInspectorMainItemUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.SOLInspector.GunsmithSOLInspectorMainItemUI",
    BPKey = "WBP_SelectionAssemblyDeposCell_V1_Gunsmith",
    SubUIs = {
        UIName2ID.IVCommonItemTemplate,
        UIName2ID.IVItemSelectedComponent,
        UIName2ID.IVMissionComponent,
        UIName2ID.IVUsingComponent,
    }
}

UITable[UIName2ID.GunsmithModelMainUI] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.GunsmithModelMainUI",
    BPKey = "WBP_Gunstand_GunsmithModelMainUI",
    ReConfig = {
        IsPoolEnable = true,
    },
    LinkSubStage = ESubStage.WeaponAssemble,
}

UITable[UIName2ID.GunsmithCameraEditorMainUI] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.CameraEditor.GunsmithCameraEditorMainUI",
    BPKey = "WBP_GunStand_GunsmithCameraEditorMainUI",
    LinkSubStage = ESubStage.WeaponAssemble,
}

UITable[UIName2ID.GunsmithCameraEditorMainItemUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.CameraEditor.GunsmithCameraEditorMainItemUI",
    BPKey = "WBP_GunStand_GunsmithCameraEditorMainItemUI",
}

UITable[UIName2ID.GunsmithFinetuneMainUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Finetune.GunsmithFinetuneMainUI",
    BPKey = "WBP_GunStand_GunsmithFinetuneMainUI",
    SubUIs = {
        UIName2ID.GunsmithFinetune3DPointCanvasUI,
    },
}

UITable[UIName2ID.GunsmithFinetune3DPointCanvasUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Finetune.GunsmithFinetune3DPointCanvasUI",
    BPKey = "WBP_GunStand_GunsmithFinetune3DPointCanvasUI",
    SubUIs = {
        UIName2ID.GunsmithFinetune3DPointItemUI,
    },
}

UITable[UIName2ID.GunsmithFinetune3DPointItemUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Finetune.GunsmithFinetune3DPointItemUI",
    BPKey = "WBP_GunStand_GunsmithFinetune3DPointItemUI",
}

UITable[UIName2ID.GunsmithFinetunePartUI] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Finetune.GunsmithFinetunePartUI",
    BPKey = "WBP_GunStand_GunsmithFinetunePartUI",
    ReConfig = {
        IsPoolEnable = true,
    },
    LinkSubStage = ESubStage.WeaponAssemble,
    SubUIs = {
        UIName2ID.GunsmithFinetunePartOpreationItemUI,
    },
}

UITable[UIName2ID.GunsmithFinetunePartOpreationItemUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Finetune.GunsmithFinetunePartOpreationItemUI",
    BPKey = "WBP_GunStand_GunsmithFinetunePartOpreationItemUI",
    ReConfig = {
        IsPoolEnable = true,
    }
}

UITable[UIName2ID.GunsmithUnlockPathUI] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.WeaponUpgrade.GunsmithUnlockPathUI",
    BPKey = "WBP_CommonfluctuationPopWindow",
    SubUIs = {
        UIName2ID.GunsmithUnlockPathItemUI,
    },
    IsModal = true,
}

UITable[UIName2ID.GunsmithUnlockPathItemUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.WeaponUpgrade.GunsmithUnlockPathItemUI",
    BPKey = "WBP_CommonfluctuationPopItem",
    SubUIs = {
        UIName2ID.IVCommonItemTemplate,
    }
}

----//成长之路panel
UITable[UIName2ID.GunUnlockGrowthRoadPanel] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Unlock.GunUnlockGrowthRoadPanel",
    BPKey = "WBP_PathofGrowth_Main",
    SubUIs = {
        UIName2ID.GunUnlockGrowthRoadItem,
        UIName2ID.GunUnlockGrowthRoadShowItem,
        UIName2ID.GunUnlockGrowthRoadListItem,
        UIName2ID.CommonEmptyContent,
    },
    ReConfig = {
        IsPoolEnable = true,
    },
    Anim = {
        FlowInAni = "WBP_PathofGrowth_Main_in",
        FlowOutAni = "WBP_PathofGrowth_Main_out",
    },
    -- LinkSubStage = ESubStage.HallLevelUp,
}
----成长之路item
UITable[UIName2ID.GunUnlockGrowthRoadItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Unlock.GunUnlockGrowthRoadItem",
    BPKey = "WBP_PathofGrowth_Detail",
    Anim = {
        bManuelAnim = true,
    },
}
----成长之路ListItem
UITable[UIName2ID.GunUnlockGrowthRoadListItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Unlock.GunUnlockGrowthRoadListItem",
    BPKey = "WBP_PathofGrowth_ItemView",
    SubUIs = {
        UIName2ID.IVCommonItemTemplate,
    },
    Anim = {
        bManuelAnim = true,
    },
}
----成长之路ShowItem
UITable[UIName2ID.GunUnlockGrowthRoadShowItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Unlock.GunUnlockGrowthRoadShowItem",
    BPKey = "WBP_PathofGrowth_Detail_01",
    Anim = {
        bManuelAnim = true,
    },
}

----//武器界面
UITable[UIName2ID.WeaponUpgradePanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.WeaponUpgrade.WeaponUpgradePanel",
    BPKey = "WBP_Equipment_UpgradeMain",
    ReConfig = {
        IsPoolEnable = true,
    },
    Anim = {
        FlowInAni = "WBP_Equipment_UpgradeMain1_in",
        FlowOutAni = "WBP_Equipment_UpgradeMain1_out",
    },
    SubUIs = {
        UIName2ID.WeaponUpgradeItem,
        UIName2ID.IVCommonItemTemplate,
    },
    -- LinkSubStage = ESubStage.HallMain
    -- LinkSubStage = ESubStage.WeaponAssemble,
}
----武器item
UITable[UIName2ID.WeaponUpgradeItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.WeaponUpgrade.WeaponUpgradeItem",
    BPKey = "WBP_Equipment_Upgrade_UpButton",
    SubUIs = {
        UIName2ID.IVCommonItemTemplate,
        UIName2ID.IVUsingYellowComponent,
    },
}
----武器升级界面
UITable[UIName2ID.WeaponUpgradePop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.WeaponUpgrade.Pop.WeaponUpgradePop",
    BPKey = "WBP_Equipment_UpgradeTips",
    SubUIs = {
        UIName2ID.WeaponUpgradePopItem,
        UIName2ID.ActivityCommonButtonV2S1,
    },
    IsModal = true,
}
----武器升级item
UITable[UIName2ID.WeaponUpgradePopItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.WeaponUpgrade.Pop.WeaponUpgradePopItem",
    BPKey = "WBP_Equipment_Upgrade_Stagepropety"
}
----关联枪械界面
UITable[UIName2ID.WeaponUpgradeFirearmPanel] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.WeaponUpgrade.WeaponUpgradeFirearmPanel",
    BPKey = "WBP_Equipment_Upgrade_RelatedFirearms",
    SubUIs = {
        UIName2ID.WeaponUpgradeFirearmItem,
    },
    IsModal = true,
}
----关联枪械item
UITable[UIName2ID.WeaponUpgradeFirearmItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.WeaponUpgrade.WeaponUpgradeFirearmItem",
    BPKey = "WBP_Equipment_Upgrade_RelatedFirearmsltem"
}
----配件进度PC组
UITable[UIName2ID.UpgradeSchedulePC] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.WeaponUpgrade.UpgradeSchedulePC",
    BPKey = "WBP_Equipment_UpgradeSchedule_PC"
}
----配件进度手游组
UITable[UIName2ID.UpgradeSchedule] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.WeaponUpgrade.UpgradeSchedule",
    BPKey = "WBP_Equipment_UpgradeSchedule"
}
----配件进度Item
UITable[UIName2ID.UpgradeScheduleItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.WeaponUpgrade.UpgradeScheduleItem",
    BPKey = "WBP_Equipment_UpgradeScheduleItem"
}
----end


UITable[UIName2ID.GunsmithDebugContextUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Debug.GunsmithDebugContextUI",
    BPKey = "WBP_GunStand_GunsmithDebugContextUI",
}

UITable[UIName2ID.GunsmithDebugPartListUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Debug.GunsmithDebugPartListUI",
    BPKey = "WBP_GunStand_GunsmithDebugPartListUI",
    SubUIs = {
        UIName2ID.GunsmithDebugPartItemUI,
    },
}

UITable[UIName2ID.GunsmithDebugPartItemUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GunsmithModule.UI.Debug.GunsmithDebugPartItemUI",
    BPKey = "WBP_GunStand_GunsmithDebugPartItemUI",
}

local GunsmithConfig =
{
    --------------------------------------------------------------------------
    ---Table配置示例
    --------------------------------------------------------------------------
    -- Tab_EquipInfo = Facade.TableManager:GetTable("EquipInfo"),
    --------------------------------------------------------------------------
    ---LuaEvent声明示例
    --------------------------------------------------------------------------
    -- evtMainPanelTest = LuaEvent:NewIns("evtMainPanelTest"),
    -- evtGunsmithVarChanged = LuaEvent:NewIns("evtGunsmithVarChanged"),

    Const = {
        GUNSMITH_SOLUTION_SERVER_MAX_COUNT = 5,
        GUNSMITH_ENVIONMENT_LIGHT_GROUP_NAME = "LightGroup_Gunsmith",
        GUNSMITH_ITEM_QUALITY_MAX = 0xF,            -- 改枪台所用道具品阶最大值

        GUNSMITH_SCENE_SOCKET_TEXT_ITEM_SIZE_X = 519,       -- 藏品界面ItemSizeX
        GUNSMITH_SCENE_SOCKET_TEXT_ITEM_SIZE_Y = 112,       -- 藏品界面ItemSizeY
        GUNSMITH_SCENE_SOCKET_TEXT_ITEM_SIZE_TARGETDISTANCE = 350,  -- 藏品界面TargetDistance

        GUNSMITH_SOLINSPECTORMAINUI_DDEFAULT_BUTTON_ID = 10100001,
        GUNSMITH_SOLINSPECTORMAINUI_SIMULATE_BUTTON_ID = 10100002,
        GUNSMITH_MAINUI_SKIN_BUTTON_ID = 10100006,
        GUNSMITH_PARTMAINUI_SIMULATE_BUTTON_ID = 10100007,
        GUNSMITH_PARTMAINUI_EQUIP_BUTTON_ID = 10100008,
        GUNSMITH_PARTMAINUI_PURCHASE_AND_EQUIP_BUTTON_ID = 10100009,
        GUNSMITH_PARTMAINUI_DETAIL_BUTTON_ID = 10100010,

        GUNSMITH_SOLUTIONMAINUI_SUGGESTION_BUTTON_ID = 10100011,
        GUNSMITH_SOLUTIONMAINUI_OPERATOR_BUTTON_ID = 10100012,
        GUNSMITH_SOLUTIONSHAREUI_EQUIP_BUTTON_ID = 10100013,
        GUNSMITH_MAINUI_SIMULATE_BUTTON_ID = 10100014,
        GUNSMITH_MAINUI_SIMULATE_CANCEL_BUTTON_ID = 10100015,

        GUNSMITH_RANGE_SOL_SLOTMIN = ESlotType.MainWeaponLeft,
        GUNSMITH_RANGE_SOL_SLOTMAX = ESlotType.Pistrol,

        GUNSMITH_RANGE_MP_SLOTMIN = ESlotType.MP_MainWeapon,
        GUNSMITH_RANGE_MP_SLOTMAX = ESlotType.MP_SecondaryWeapon,

        GUNSMITH_ENABLE_COMPAREWEAPON = true,

        GUNSMITH_SOLUTIONSHARE_ENABLE_INPUTBOX_SCROLLTO = true,

        GUNSMITH_PENDANT_ANIMSEQUENCE_PATH = "AnimSequence'/Game/Models/Weapons/Parts/Pendant/Pendant_Master/pendant_swing.pendant_swing'",

        GUNSMITH_SWITCHSYSTEMFUNSMITH = SwitchSystemID.SwitchSystemWeaponAssembly,
        GUNSMITH_MAINVIEW_APPEARANCETAB = SwitchSystemID.SwitchSystemWeaponAssemblySkin,

        GUNSMITH_SKIN_CYBERCAFE_PATH = "PaperSprite'/Game/UI/UIAtlas/System/Hall/BakedSprite/Hall_Icon_33.Hall_Icon_33'",
        GUNSMITH_SKIN_UNIVERSITY_PATH = "PaperSprite'/Game/UI/UIAtlas/System/Hall/BakedSprite/Hall_Icon_32.Hall_Icon_32'",

        GUNSMITH_PENDANTMAINUI_MANDEBRICK_ITEMID = 16110000014,

        GUNSMITH_PENDANTMAINUI_MANDEBRICK_ICON = "PaperSprite'/Game/UI/UIAtlas/CommonHud/BakedSprite/CommonHud_MapMarker_Icon_0202.CommonHud_MapMarker_Icon_0202'",
        GUNSMITH_PENDANTMAINUI_AUCTION_ICON = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_De_Buy.CommonHall_De_Buy'",
        GUNSMITH_PENDANTMAINUI_MATRIXWORKSHOP_ICON = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Warehouse_Icon_0106.CommonHall_Warehouse_Icon_0106'",

        GUNSMITH_SKINMAINUI_RANDOM_ICON = "Texture2D'/Game/UI/UIAtlas/CommonHall/Sp/CommonHall_SP_01.CommonHall_SP_01'",
        GUNSMITH_SKINMAINUI_RANDOM_BUTTON_CHECKED_ICON = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Random_02.CommonHall_Random_02'",
        GUNSMITH_SKINMAINUI_RANDOM_BUTTON_UNCHECKED_ICON = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Random_01.CommonHall_Random_01'",

        -- GUNSMITH_INSPECTOR_COMPOUNDBOW = 18150000001,
        -- GUNSMITH_INSPECTOR_SPECIAL_GROUP_ID = 99,
        -- GUNSMITH_INSPECTOR_SPECIAL_GROUPS = {
        --     [1] = ItemConfig.EWeaponItemType.Universal,
        -- },

        GUNSMITH_AUTO_DOWNLOAD_PAK = false,

        GUNSMITH_SOLUTION_SAVE_CONFIRM_ITEM_COLOR = ColorUtil.GetSlateColorByHex("C4C4C207")
    },
    IconPaths = {
        [1] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Arms_Icon_0101.Common_Arms_Icon_0101",--杀人星级1
        [2] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Arms_Icon_0102.Common_Arms_Icon_0102",--杀人星级2
    },
    --武器升级
    TabImgPathList = {
        GunsmithGrowthRouteTxt = "PaperSprite'/Game/UI/UIAtlas/System/Equipment/BakedSprite/Equimpent_Up_Icon_0001.Equimpent_Up_Icon_0001",
        GunsmithLevelUpTxt = "PaperSprite'/Game/UI/UIAtlas/System/Equipment/BakedSprite/Equimpent_Up_Icon_0002.Equimpent_Up_Icon_0002",
        GrowthRouteIcon = "PaperSprite'/Game/UI/UIAtlas/System/PathofGrowth/BakedSprite/PatnofGrowth_De_38.PatnofGrowth_De_38'",
    },
    --end
    Loc = {
        --道具类型
        GunsmithWeaponSkin = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithWeaponSkin", "武器皮肤"),
        GunsmithVehicleSkin = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithVehicleSkin", "载具皮肤"),
        GunsmithVehicle = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithGunsmithVehicle", "载具"),
        GunsmithWeapon = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithWeapon", "武器"),
        --武器升级
        GunsmithWeaponStarTipsTitle = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithWeaponStarTipsTitle", "枪械星级"),
        GunsmithWeaponStarTipsTitles = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithWeaponStarTipsTitles", "枪械星级 %s"),
        GunsmithWeaponStarTipsDes = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithWeaponStarTipsDes", "枪械满级后，解锁该枪械的星级系统。每使用该武器击败100次敌方干员可提升1星级。"),
        WeaponHighestStarRating = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_WeaponHighestStarRating", "{star} (最高星级)"),
        PreInstallationUnlocking = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_PreInstallationUnlocking", "升级至{level}可解锁当前预安装配件"),
        To = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_To", "至"),
        Approve = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_Approve", "可"),
        Unlock = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_Unlock", "解锁"),
        Current = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_Current", "当前"),
        Preinstall = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_Preinstall", "预安装"),
        Accessory = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_Accessory", "配件"),
        GunsmithFirearms = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithFirearms", "枪械"),
        GunsmithStarRating = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithStarRating", "星级"),
        GunsmithHighest = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithHighest", "最高"),

        GunsmithGrade = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithGrade", "等级%s"),
        GunsmithGrade1 = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithGrade1", "<customstyle color=\"Color_Highlight02\">等级%s</>"),
        GunsmithGrade2 = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithGrade2", "<customstyle color=\"Color_Highlight01\">等级%s</>"),


        GunsmithUse   = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithUse", "使用 : %s"),
        AddExperience = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_AddExperience", "使用武器经验卡增加了%s点经验"),
        GunsmithUnlockEmpty = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithUnlockEmpty", "当前没有解锁内容"),
        GunsmithUnlockAtLevelNumber = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithUnlockAtLevelNumber", "达到%s级即可解锁"),
        GunsmithAssociatedFirearms = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithAssociatedFirearms", "关联枪械"),
        GunsmithCanUsedFollowingFirearms = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithCanUsedFollowingFirearms", "%s可被以下枪械使用"),
        GunsmithItemUseOut = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithItemUseOut", "没有武器经验书可用"),
        GunsmithLevelUp = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithLevelUp", "枪械升级"),
        GunsmithComminSoon = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithComminSoon", "敬请期待"),
        GunsmithUpgrade = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithUpgrade", "升级"),
        GunsmithCurrentLevel = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithCurrentLevel", "等级%s"),
        GunsmithHighestLevel = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithHighestLevel", "等级%s (最高等级)"),
        GunsmithExpRidExp = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithExpRidExp", "%s/%s"),
        GunsmithPercentage = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPercentage", "%s%s%%"),
        GunsmithAddExp = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithAddExp", "+%s"),
        GunsmithUpgradeWeapon = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithUpgradeWeapon", "升级%s"),
        GunsmithSelectAll = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSelectAll", "全选"),
        GunsmithUnSelected = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithUnSelected", "取消"),
        GunsmithUpToLevelMax = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithUpToLevelMax", "最高可以升到%s级"),
        GunsmithUpToLevelSimulateTips = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithUpToLevelSimulateTips", "升级至 <customstyle color=\"Color_Highlight01\">等级{level}</> 可解锁当前预安装配件"),
        --end
        GunsmithMaxExp = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithMaxExp", "已达最高等级"),
        GunsmithReward = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithReward", "奖励"),
        GunsmithUnLockLevel = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithUnLockLevel", "等级%s已解锁"),
        GunsmithLockLevel = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithLockLevel", "等级%s未解锁"),
        GunsmithGrowthRoad = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithGrowthRoad", "成长之路"),
        GunsmithMainLevel = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithMainLevel", "战场等级：%s"),
        GunsmitActionLevel = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmitActionLevel", "行动等级：%s"),
        GunsmithMainExp = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithMainExp", "/%s"),
        GunsmithMainSOLLevelInfi = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithMainSOLLevelInfi", "参与烽火地带玩法，获得行动经验，可提升行动等级，解锁丰富游玩内容。"),
        GunsmithMainLevelInfi = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithMainLevelInfi", "参与全面战场对局，获得经验可以升级，解锁丰富武器。"),
        GunsmithMainRangeSolutionSaveTips = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithMainRangeSolutionSaveTips", "是否将靶场内临时方案保留"),

        GunsmithUnlockPathUIConfirmBtnText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithUnlockPathUIConfirmBtnText", "应用方案"),
        GunsmithUnlockPathUIFineTuneText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithUnlockPathUIFineTuneText", "%s，<customstyle color=\"Color_Highlight01\">包含精校数据</>"),
        GunsmithUnlockPathUITips = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithUnlockPathUITips", "当前展示方案与仓库不一致是否同步"),
        GunsmithUnlockPathUILevelText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithUnlockPathUILevelText", "枪械%s级解锁"),
        GunsmithUnlockPathUIEmptyTips = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithUnlockPathUIEmptyTips", "没有可应用配件！"),
        GunsmithUnlockPathUISuccessTips = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithUnlockPathUISuccessTips", "已应用方案"),
        GunsmithUnlockPathUIDeClickedTips = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithUnlockPathUIDeClickedTips", "请勾选需要应用的配件"),

        GunsmithMainTabPreview = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithMainTabPreview", "改装"),
        GunsmithMainTabAppearance = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithMainTabAppearance", "个性化"),
        GunsmithMainTabFineTune = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithMainTabFineTune", "精校"),
        GunsmithMainTabSolution = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithMainTabSolution", "方案"),
        GunsmithMainTabWeaponUpgrade = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithMainTabWeaponUpgrade", "成长"),
        GunsmithMainUISkinTabText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithMainUISkinTabText", "外观"),
        GunsmithMainUIPendantTabText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithMainUIPendantTabText", "挂饰"),
        GunsmithMainUINotEnterTipsText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithMainUINotEnterTipsText", "匹配中/准备中状态无法进行改枪操作"),
        GunsmithPreviewOnCloseWhenSimulatingText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPreviewOnCloseWhenSimulatingText", "方案尚未完全实现，是否仍然退出？"),
        GunsmithPreviewRemoveAllPartsText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPreviewRemoveAllPartsText", "确定要卸下所有已安装配件吗？"),
        GunsmithPreviewMPButtonUnlockText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPreviewMPButtonUnlockText", "解锁缺失配件"),
        GunsmithMainUIPakNoDownloadTipsText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithMainUIPakNoDownloadTipsText", "当前资源未下载"),
        GunsmithInspectorMainPakNoDownloadTipsText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithInspectorMainPakNoDownloadTipsText", "当前枪械未下载，不支持改装"),


        GunsmithSOLInspectorMainUIDefaultEquipeButtonText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSOLInspectorMainUIDefaultEquipeButtonText", "改装"),
        GunsmithSOLInspectorMainUISimulateEquipeButtonText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSOLInspectorMainUISimulateEquipeButtonText", "预改装"),
        GunsmithSOLInspectorInventoryTitleText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSOLInspectorInventoryTitleText", "改装已有"),
        GunsmithSOLInspectorShopTitleText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSOLInspectorShopTitleText", "定制新枪"),
        GMTargetItemNotExistText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_TargetItemNotExistText", "目标id:%s,不在当前类型中"),

        GunsmithInspectorWeaponItemTypeUnknown =  NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithInspectorWeaponItemTypeUnknown", "全部枪械"),
        GunsmithInspectorWeaponItemTypeRifle =  NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithInspectorWeaponItemTypeRifle", "步枪"),
        GunsmithInspectorWeaponItemTypeSubmachine =  NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithInspectorWeaponItemTypeSubmachine", "冲锋枪"),
        GunsmithInspectorWeaponItemTypeShotgun =  NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithInspectorWeaponItemTypeShotgun", "霰弹枪"),
        GunsmithInspectorWeaponItemTypeLightMachine =  NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithInspectorWeaponItemTypeLightMachine", "轻机枪"),
        GunsmithInspectorWeaponItemTypePrecisionShootingRifle = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithInspectorWeaponItemTypePrecisionShootingRifle", "精确射手步枪"),
        GunsmithInspectorWeaponItemTypeSniper =  NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithInspectorWeaponItemTypeSniper", "狙击步枪"),
        GunsmithInspectorWeaponItemTypePistol =  NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithInspectorWeaponItemTypePistol", "手枪"),
        GunsmithInspectorWeaponItemTypeUniversal =  NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithInspectorWeaponItemTypeUniversal", "特殊武器"),

        GunsmithMainPreviewTitle = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithMainPreviewTitle", "改枪台"),
        GunsmithMainPreviewUIButtonPriceText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithMainPreviewUIButtonPriceText", "确认"),
        GunsmithMainPreviewUIButtonText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithMainPreviewUIButtonText", "购买缺失配件"),
        GunsmithMainPreviewUIRangeText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithMainPreviewUIRangeText", "是否确认进入靶场试用此枪？"),
        GunsmithMainPreviewUIMPLevelText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithMainPreviewUIMPLevelText", "等级：%d"),
        GunsmithPartSimulateConfimCheckBoxText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPartSimulateConfimCheckBoxText", "近期不再提示"),

        GunsmithDetailCheckTip = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithDetailCheckTip", "查看详情"),
        GunsmithSaveSolutionTip = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSaveSolutionTip", "保存"),
        GunsmithRangeTip = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithRangeTip", "靶场"),

-- BEGIN VIRTUOS MODIFICATION
        GunsmithIllegalPartText = NSLOCTEXT("GunsmithModule", "Console_Lua_Gunsmith_GunsmithIllegalPartText", "配装方案已不合规，点击确认后该方案将被初始化"),
-- END VIRTUOS MODIFICATION


        WeaponUnlockLevelFormat = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_WeaponUnlockLevelFormat", "武器<customstyle color=\"Color_Highlight01\">%s级</>解锁"),
        GunsmithPartMainUIUnlockTitleText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPartMainUIUnlockTitleText", "已解锁"),
        GunsmithPartMainUILockTitleText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPartMainUILockTitleText", "未解锁"),
        GunsmithPriceText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPriceText", "{currencyIconTxt}{priceStr}"),
        GunsmithPartMainUIButtonUnenabledText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPartMainUIButtonUnenabledText", "不可购买"),
        GunsmithPartMainUIPriceSourceTextFormat = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPartMainUIPriceSourceText", "价格来源:{Source}"),
        GunsmithPartMainUIPriceSourceShopText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPartMainUIPriceSourceShopText", "军需处"),
        GunsmithPartMainUIPriceSourceAuctionText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPartMainUIPriceSourceAuctionText", "拍卖行"),
        GunsmithPartMainUIAuctionUnlockTips = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPartMainUIAuctionUnlockTips", "{Condition}级可解锁拍卖行"),
        -- GunsmithPartMainUIAuctionUnlockTips = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPartMainUIAuctionUnlockTips", "拍卖行未解锁"),
        GunsmithPartMainUISOLShopUnlockSuffix = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPartMainUISOLShopUnlockSuffix", "{Condition}军需处购买"),
        GunsmithPartMainUIUnReceivePrice = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPartMainUIUnReceivePrice", "正在查价"),
        GunsmithPartMainUIShopSoldOut = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPartMainUIShopSoldOut", "商城已限购"),
        GunsmithPartMainUIAuctionSoldOut = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPartMainUIAuctionSoldOut", "拍卖行已限购"),
        GunsmithPartMainUINotGet = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPartMainUINotGet", "此物品暂不开放获取"),
        GunsmithPartMainUISimulateTips = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPartMainUISimulateTips", "该节点的父节点已进入预改装，子节点不能进行安装"),
        GunsmithPartMainUIFinetuneEquipTip = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPartMainUIFinetuneEquipTip", "安装后可精校"),
        GunsmithPartMainUIFailedPurchase = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPartMainUIFailedPurchase", "由于价格波动，购买失败，已刷新价格，请重新购买"),
        GunsmithPartMainUIPriceSourceTips = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPartMainUIPriceSourceTips", "系统会自动比较所有已解锁的购买途径，并选取最低价格。"),
        GunsmithPartMainUIButtonMPLevelUnlockText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPartMainUIButtonMPLevelUnlockText", "升级以解锁"),
        GunsmithPartMainUIButtonMPOtherUnlockText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPartMainUIButtonMPOtherUnlockText", "获取途径"),
        GunsmithPartMainUIMPOtherUnlockText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPartMainUIMPOtherUnlockText", "%s获取"),
        GunsmithPartMainUIMPOtherUnlockMaskText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPartMainUIMPOtherUnlockMaskText", "<customstyle color=\"Color_Highlight01\">%s</>解锁"),
        GunsmithPartMainUIMPUnlockActivityText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPartMainUIMPUnlockActivityText", "活动"),
        GunsmithPartMainUIMPUnlockArmoryText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPartMainUIMPUnlockArmoryText", "军械库"),
        GunsmithPartMainUIUnableObtainText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPartMainUIUnableObtainText", "无法获取"),
        GunsmithPartMainUIExceptionPurchase = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPartMainUIExceptionPurchase", "购买异常，请稍后重试"),


        GunsmithPartSimulateConfimText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPartSimulateConfimText", "当前所选的配件并未解锁，仍然安装将进入预改装状态。您可以在此状态下预安装任何配件而不管它们是否解锁，但是所安装的配件仅供预览，返回后武器会恢复原状，您确定吗？"),
        GunsmithPartSOLInventoryTitleText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPartSOLInventoryTitleText", "仓库已有"),
        GunsmithPartSOLShopTitleText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPartSOLShopTitleText", "当前在售"),
        GunsmithPartClickAddSocketText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPartClickAddSocketText", "若安装当前选中配件，将解锁当前闪烁槽位"),
        GunsmithPreviewUnlockePartAuctionText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPreviewUnlockePartAuctionText", "拍卖行暂未解锁"),

        GunsmithDetailScopeUIScopeText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithDetailScopeUIScopeText", "x{Condition}"),


        GunsmithSkinBaseWeaponNameFormat = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSkinBaseWeaponNameFormat", "%s基础样式"),
        -- GunsmithSkinSOLGlobalEquippedFormat = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSkinSOLGlobalEquippedFormat", "%s默认皮肤"),
        GunsmithSkinSOLGlobalEquippedFormat = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSkinSOLGlobalEquippedFormat", "默认外观"),
        GunsmithSkinDefaultMainUIButtenOnlyEquipText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSkinDefaultMainUIButtenOnlyEquipText", "装备此枪"),
        GunsmithSkinDefaultMainUIButtenOnlyEquippedText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSkinDefaultMainUIButtenOnlyEquippedText", "当前武器已应用"),
        GunsmithSkinDefaultMainUIButtenAllEquipText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSkinDefaultMainUIButtenAllEquipText", "设为默认"),
        GunsmithSkinDefaultMainUIButtenAllEquippedText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSkinDefaultMainUIButtenAllEquippedText", "已设为默认外观"),
        GunsmithSkinDefaultUnlockTitleText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSkinDefaultUnlockTitleText", "已拥有"),
        GunsmithSkinDefaultLockTitleText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSkinDefaultLockTitleText", "未拥有"),
        GunsmithSkinDefaultMainUISortQualityText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSkinDefaultMainUISortQualityText", "按品质"),
        GunsmithSkinDefaultMainUISortGainTimeText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSkinDefaultMainUISortGainTimeText", "按获取日期"),
        GunsmithSkinBaseWeaponName4PendantFormat = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSkinBaseWeaponName4PendantFormat", "%s基础样式"),
        -- GunsmithSkinSOLGlobalEquipped4PendantFormat = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSkinSOLGlobalEquipped4PendantFormat", "%s默认挂饰"),
        GunsmithSkinSOLGlobalEquipped4PendantFormat = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSkinSOLGlobalEquipped4PendantFormat", "默认挂饰"),
        GunsmithSkinUnEquippedButtonText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSkinUnEquippedButtonText", "从此枪卸下"),
        GunsmithSkinGlobalUnEquippedButtonText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSkinGlobalUnEquippedButtonText", "取消默认"),
        DropDownBoxTitle = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_DropDownTitle", "典藏款式 %s"),
        GunsmithSkinDownloadSuccessTips = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSkinDownloadSuccessTips", "%s资源下载完成"),
        GunsmithSkinDefaultReceiveText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSkinDefaultReceiveText", "前往藏品领取"),
        GunsmithSkinDefaultMainUIEquipSolutionText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSkinDefaultMainUIEquipSolutionText", "应用皮肤自带配件方案"),
        GunsmithSkinSolutionUICurrentTitleText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSkinSolutionUICurrentTitleText", "当前配置"),
        GunsmithSkinSolutionUISkinSolutionTitleText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSkinSolutionUISkinSolutionTitleText", "皮肤自带配件方案"),
        GunsmithSkinSolutionUIConfirmTips = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSkinSolutionUIConfirmTips", "已替换为%s皮肤自带配件方案"),
        GunsmithSkinDefaultMainUIButtenNormalObtain = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSkinDefaultMainUIButtenNormalObtain", "前往获取"),
        GunsmithSkinDefaultMainUIRandomTitle = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSkinDefaultMainUIRandomTitle", "{WeaponName}-随机外观"),
        GunsmithSkinDefaultMainUIAddRandomTips = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSkinDefaultMainUIAddRandomTips", "已加入随机外观列表"),
        GunsmithSkinDefaultMainUIRemoveRandomTips = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSkinDefaultMainUIRemoveRandomTips", "已移出随机外观列表"),
        GunsmithSkinDefaultMainUIButtenRandomEquippedText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSkinDefaultMainUIButtenRandomEquippedText", "已应用"),
        GunsmithSkinDefaultMainUIButtenRandomAllEquippedText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSkinDefaultMainUIButtenRandomAllEquippedText", "已设为默认"),
        GunsmithSkinDefaultMainUIButtenRandomAllEquippedTips = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSkinDefaultMainUIButtenRandomAllEquippedTips", "请先编辑随机外观"),
        GunsmithSkinDefaultMissionTitleText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSkinDefaultMissionTitleText", "查看挑战"),
        GunsmithSkinDefaultMissionObtainText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSkinDefaultMissionObtainText", "完成挑战可解锁"),

        GunsmithPendantMainUIMandelDraw = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPendantMainUIMandelDraw", "曼德尔砖（%s）"),
        GunsmithPendantMainUIPendantMandelDraw = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPendantMainUIPendantMandelDraw", "图灵砖（%s）"),
        GunsmithPendantMainUIFromMarket = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPendantMainUIFromMarket", "市场"),
        GunsmithPendantMainUIFromMatrixWorkshop = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithPendantMainUIFromMatrixWorkshop", "典藏工坊"),


        GunsmithFinetunePartOpreationItemUIDefaultValueText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithFinetunePartOpreationItemUIDefaultValueText", "默认"),
        GunsmithFinetunePartOpreationItemUIValueTextFormat = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithFinetunePartOpreationItemUIValueTextFormat", "{Name}{Value}{Unit}"),


        GunsmithSolutionMainTabTitle4Local = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionMainTabTitle4Local", "当前改装"),
        GunsmithSolutionMainTabTitle4Server = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionMainTabTitle4Server", "自定义方案"),
        GunsmithSolutionMainTabTitle4Suggestion = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionMainTabTitle4Suggestion", "推荐方案"),
        GunsmithSolutionMainTabTitle4Blueprint = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionMainTabTitle4Blueprint", "蓝图方案"),
        GunsmithSolutionMainTabTitle4Operator = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionMainTabTitle4Operator", "主播推荐"),
        GunsmithSolutionServerDefaultNameFormat = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionServerDefaultNameFormat", "自定义方案%s"),
        GunsmithSolutionServerDefaultNameFormat_Index_1 = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionServerDefaultNameFormat_Index_1", "一"),
        GunsmithSolutionServerDefaultNameFormat_Index_2 = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionServerDefaultNameFormat_Index_2", "二"),
        GunsmithSolutionServerDefaultNameFormat_Index_3 = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionServerDefaultNameFormat_Index_3", "三"),
        GunsmithSolutionServerDefaultNameFormat_Index_4 = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionServerDefaultNameFormat_Index_4", "四"),
        GunsmithSolutionServerDefaultNameFormat_Index_5 = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionServerDefaultNameFormat_Index_5", "五"),
        GunsmithSolutionInputHintText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionInputHintText", "请输入方案名称"),
        GunsmithSolutionNameCanNotBeNullText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionNameCanNotBeNullText", "名字不能为空"),
        GunsmithSolutionSaveSuccessText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionSaveSuccessText", "当前方案已保存"),
        GunsmithSolutionEquipSuccessText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionEquipSuccessText", "已按照方案更改配件设置"),
        GunsmithSolutionSkinSourceFormat = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionSkinSourceFormat", "因购买%s而解锁"),
        GunsmithSolutionMianUICheckText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionMianUICheckText", "适用预设时套用外观"),
        GunsmithSolutionSaveErrorTips = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionSaveErrorTips", "当前武器数据异常，无法保存"),
        GunsmithSolutionSaveConfirmUITitleFormat = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionSaveConfirmUITitleFormat", "保存为%s预设方案"),
        GunsmithSolutionSaveConfirmUISOLTitle = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionSaveConfirmUISOLTitle", "烽火地带"),
        GunsmithSolutionSaveConfirmUIMPTitle = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionSaveConfirmUIMPTitle", "全面战场"),
        GunsmithSolutionSaveConfirmUIButtonText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionSaveConfirmUIButtonText", "保存"),
        GunsmithSolutionMainButtonSaveText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionMainButtonSaveText", "保存方案"),
        GunsmithSolutionMainButtonUsedText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionMainButtonUsedText", "已使用"),
        GunsmithSolutionMainButtonEquipText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionMainButtonEquipText", "使用方案"),
        GunsmithSolutionMainKOLTipsText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionMainKOLTipsText", "每个方案都拥有适合的打法标签，各位干员可以选择与自己打法相近的方案"),

        GunsmithSolutionShareUILockText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionShareUILockText", "分享功能暂未开放"),
        GunsmithSolutionShareUITabImportText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionShareUITabImportText", "导入方案"),
        GunsmithSolutionShareUITabShareText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionShareUITabShareText", "分享方案"),
        GunsmithSolutionShareUIInputText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionShareUIInputText", "请输入武器分享码"),
        GunsmithSolutionShareUIFormatTipsText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionShareUIFormatTipsText", "（%s）"),
        GunsmithSolutionShareUITipsText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionShareUITipsText", "{weaponName}-{modeName}"),
        GunsmithSolutionShareUIButtonUseText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionShareUIButtonUseText", "使用方案"),
        GunsmithSolutionShareUIButtonShareText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionShareUIButtonShareText", "复制分享码"),
        GunsmithSolutionShareUICodeWaittingText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionShareUICodeWaittingText", "正在查询"),
        GunsmithSolutionShareUIImportNil = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionShareUIImportNil", "请输入分享码"),
        GunsmithSolutionShareUIImportError = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionShareUIImportError", "分享码错误"),
        GunsmithSolutionShareUIWeaponError = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionShareUIWeaponError", "该分享码只能用于%s"),
        GunsmithSolutionShareUIModeError = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionShareUIModeError", "该分享码只能用于%s模式"),
        GunsmithSolutionShareUICopySuccessText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionShareUICopySuccessText", "分享码复制成功"),
        GunsmithSolutionShareUINothingText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithSolutionShareUINothingText", "暂无信息内容"),


        GunsmithMissionUINotMissionTips = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithMissionUINotMissionTips", "暂无任务信息"),
        GunsmithMissionUINoMissionTitle = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithMissionUINoMissionTitle", "任务名字为空"),
        GunsmithMissionUINoMissionDes = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithMissionUINoMissionDes", "任务描述为空"),
        GunsmithModelMainUITitle = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithModelMainUITitle", "查看详情"),


        GunsmithAmmoTipsOperateLimit = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithAmmoTipsOperateLimit", "子弹只能添加和卸载"),
        GunsmithAmmoTipsParamsError = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithAmmoTipsParamsError", "子弹操作参数错误"),
        GunsmithAmmoTipsNotMatchWeapon = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithAmmoTipsNotMatchWeapon", "该子弹不能安装到这把武器上"),
        GunsmithAmmoTipsInventoryErrorData = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithAmmoTipsInventoryErrorData", "仓库数据不正常"),
        GunsmithAmmoTipsMoreUnload = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithAmmoTipsMoreUnload", "卸载子弹数量超过已装载子弹数量"),


        GunsmithRangeInspectorMainUIEquipButtonText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithRangeInspectorMainUIEquipButtonText", "装备"),
        GunsmithRangeInspectorMainUIEquippedButtonText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithRangeInspectorMainUIEquippedButtonText", "正在使用"),
        GunsmithRangeInspectorMainUIEquippedChangeButtonText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithRangeInspectorMainUIEquippedChangeButtonText", "交换"),
        GunsmithRangeInspectorMainUIOpenGunsmithButtonText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithRangeInspectorMainUIOpenGunsmithButtonText", "改装"),

        GunsmithErrorOperationTips = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithErrorOperationTips", "该模式不能发送改枪协议"),

        GunsmithCameraEditorMainUIResetAllTips = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithCameraEditorMainUIResetAllTips", "确定将所有武器的所有镜头重置？"),
        GunsmithCameraEditorMainUIResetTips = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithCameraEditorMainUIResetTips", "确定将所有武器的\"%s\"镜头重置？"),
        GunsmithCameraEditorMainUIResetAllPointText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithCameraEditorMainUIResetAllPointText", "Reset All Point"),
        GunsmithCameraEditorMainUIResetPointText = NSLOCTEXT("GunsmithModule", "Lua_Gunsmith_GunsmithCameraEditorMainUIResetPointText", "Reset %s Point"),
    },

    Events = {
        evtGunsmithPropItemClicked = LuaEvent:NewIns("evtGunsmithPropItemClicked"),
        -- evtGunsmithWeaponFlashingCreateItem  = LuaEvent:NewIns("evtGunsmithWeaponFlashingCreateItem"),

        evtGunsmithOnSceneLoaded = LuaEvent:NewIns("evtGunsmithOnSceneLoaded"),
        evtGunsmithOnSceneSocketItemUIClicked = LuaEvent:NewIns("evtGunsmithOnSceneSocketItemUIClicked"),
        evtGunsmithOnGunsmithPartMainItemUIClicked = LuaEvent:NewIns("evtGunsmithOnGunsmithPartMainItemUIClicked"),
        evtGunsmithOnGunsmithPartMainItemUIDoubleClicked = LuaEvent:NewIns("evtGunsmithOnGunsmithPartMainItemUIDoubleClicked"),
        evtGunsmithOnGunsmithPartMainItemUIFocus = LuaEvent:NewIns("evtGunsmithOnGunsmithPartMainItemUIFocus"),
        evtGunsmithOnSimulateDataUpdated = LuaEvent:NewIns("evtGunsmithOnSimulateDataUpdated"),
        evtGunsmithOnSkinDefaultItemUIClicked = LuaEvent:NewIns("evtGunsmithOnSkinDefaultItemUIClicked"),
        evtGunsmithOnGunsmithSolutionSaveConfirmItemUIClicked = LuaEvent:NewIns("evtGunsmithOnGunsmithSolutionSaveConfirmItemUIClicked"),
        evtGunsmithOnGunsmithPurchaseSuccessCallback = LuaEvent:NewIns("evtGunsmithOnGunsmithPurchaseSuccessCallback"),
        evtGunsmithOnGunsmithSolutionDisabledTabClicked = LuaEvent:NewIns("evtGunsmithOnGunsmithSolutionDisabledTabClicked"),
        evtGunsmithOnGunsmithSceneSocketMainUIUpdateCenterPosition = LuaEvent:NewIns("evtGunsmithOnGunsmithSceneSocketMainUIUpdateCenterPosition"),
        evtGunsmithOnGunsmithSOLInspectorMainItemUIClicked = LuaEvent:NewIns("evtGunsmithOnGunsmithSOLInspectorMainItemUIClicked"),
        evtGunsmithOnMissionEquippedAndUnequippedPartListRefresh = LuaEvent:NewIns("evtGunsmithOnMissionEquippedAndUnequippedPartListRefresh"),
        evtGunsmithOnCameraEditorMainItemUIClicked = LuaEvent:NewIns("evtGunsmithOnCameraEditorMainItemUIClicked"),
        evtGunsmithOnCameraEditorMainItemUIResetClicked = LuaEvent:NewIns("evtGunsmithOnCameraEditorMainItemUIResetClicked"),
        evtGunsmithModuleCallShowTargetItem = LuaEvent:NewIns("evtGunsmithModuleCallShowTargetItem"),
        evtGunsmithCSPostUpdateDeposData = LuaEvent:NewIns("evtGunsmithCSPostUpdateDeposData"),
        evtGunsmithCSPostAssemblyDepositPropUpdate = LuaEvent:NewIns("evtGunsmithCSPostAssemblyDepositPropUpdate"),
        evtGunsmithCSPostAssemblyApplySkin = LuaEvent:NewIns("evtGunsmithCSPostAssemblyApplySkin"),
        evtGunsmithCSPostAssemblyModifySkin = LuaEvent:NewIns("evtGunsmithCSPostAssemblyModifySkin"),
        evtGunsmithFinetunePartOpreationItemValueUpdated = LuaEvent:NewIns("evtGunsmithFinetunePartOpreationItemValueUpdated"),
        evtGunsmithSceneLineItemUICanvasLineDrawed = LuaEvent:NewIns("evtGunsmithSceneLineItemUICanvasLineDrawed"),
        evtGunsmithCSPostWeaponGetRecommendSharingCodeRes = LuaEvent:NewIns("evtGunsmithCSPostWeaponGetRecommendSharingCodeRes"),
        evtGunsmithCSPostInventoryCollectionFetchFinished = LuaEvent:NewIns("evtGunsmithCSPostInventoryCollectionFetchFinished"),
        evtGunsmithCSPostRelayConnected = LuaEvent:NewIns("evtGunsmithCSPostRelayConnected"),

        evtGunsmithOnGunsmithPartFilterCheckBoxStateChanged = LuaEvent:NewIns("evtGunsmithOnGunsmithPartFilterCheckBoxStateChanged"),
        evtGunsmithPartFilterUIConfirm = LuaEvent:NewIns("evtGunsmithPartFilterUIConfirm"),

        evtGunsmithOnGunsmithSolutionSynchronizationItemUIClicked = LuaEvent:NewIns("evtGunsmithOnGunsmithSolutionSynchronizationItemUIClicked"),
        evtGunsmithOnGunsmithSolutionSynchronizationItemUIChecked = LuaEvent:NewIns("evtGunsmithOnGunsmithSolutionSynchronizationItemUIChecked"),

        evtGunsmithDebugUIContextOpen = LuaEvent:NewIns("evtGunsmithDebugUIContextOpen"),
        evtGunsmithDebugUIContextClose = LuaEvent:NewIns("evtGunsmithDebugUIContextClose"),
        evtGunsmithDebugUIContextUpdate = LuaEvent:NewIns("evtGunsmithDebugUIContextUpdate"),

        evtGunsmithDebugUIPartListOpen = LuaEvent:NewIns("evtGunsmithDebugUIPartListOpen"),
        evtGunsmithDebugUIPartListClose = LuaEvent:NewIns("evtGunsmithDebugUIPartListClose"),
        evtGunsmithDebugUIPartListUpdate = LuaEvent:NewIns("evtGunsmithDebugUIPartListUpdate"),

        Gamescom = {
            evtGunsmithCamescomAutoRotateCamera = LuaEvent:NewIns("evtGunsmithCamescomAutoRotateCamera"),
            evtGunsmithCamescomAutoUpdatePart = LuaEvent:NewIns("evtGunsmithCamescomAutoUpdatePart"),
        },

        evtGunsmithOnRangeDataUpdated = LuaEvent:NewIns("evtGunsmithOnRangeDataUpdated"),
        evtGunsmithOnRangeAddOrUpdateAssembleWeapon = LuaEvent:NewIns("evtGunsmithOnRangeAddOrUpdateAssembleWeapon"),

        evtGunsmithShortcutPropUpdate = LuaEvent:NewIns("evtGunsmithShortcutPropUpdate"),
        evtGunsmithOnSceneSocketItemUIFocus = LuaEvent:NewIns("evtGunsmithOnSceneSocketItemUIFocus"),
        --武器升级
        evtGunsmithRefreshAccessoryDetailsClicked = LuaEvent:NewIns("evtGunsmithRefreshAccessoryDetailsClicked"),
        evtGunsmithUpgradeItemOperated = LuaEvent:NewIns("evtGunsmithUpgradeItemOperated"),
        evtGunsmithUpgradeItemUpdate = LuaEvent:NewIns("evtGunsmithUpgradeItemUpdate"),
        --end

        evtGunsmithOnPendantItemUIClicked = LuaEvent:NewIns("evtGunsmithOnPendantItemUIClicked"),

        evtOnWeaponUpgradeUIShow = LuaEvent:NewIns("evtOnWeaponUpgradeUIShow"),
        evtOnWeaponUpgradeUIHide = LuaEvent:NewIns("evtOnWeaponUpgradeUIHide"),
        evtOnWeaponUpgradePanelBtnClicked = LuaEvent:NewIns("evtOnWeaponUpgradePanelBtnClicked"),

        evtOnWeaponUpgradeSuccessShow = LuaEvent:NewIns("evtOnWeaponUpgradeSuccessShow"),
        evtOnWeaponUpgradeSuccessHide = LuaEvent:NewIns("evtOnWeaponUpgradeSuccessHide"),
        evtOnUseItemBtnClicked = LuaEvent:NewIns("evtOnUseItemBtnClicked"),

        evtOnWeaponUpgradeSuccessPanelSelectAllClicked = LuaEvent:NewIns("evtOnWeaponUpgradeSuccessPanelSelectAllClicked"),
        evtOnGunsmithUnlockPathItemUIUnlockClicked = LuaEvent:NewIns("evtOnGunsmithUnlockPathItemUIUnlockClicked"),
        evtGunsmithUnlockPathItemStateChanged = LuaEvent:NewIns("evtGunsmithUnlockPathItemStateChanged"),
        evtGunsmithUnlockPathSuccessCallback = LuaEvent:NewIns("evtGunsmithUnlockPathSuccessCallback"),
        -- BEGIN MODIFICATION @ VIRTUOS : 在刷新Item的时候将其添加至手柄的导航的事件
		evtOnGunsmithUpgradeItemUIInitItemData = LuaEvent:NewIns("evtOnGunsmithUpgradeItemUIInitItemData"),
		-- END MODIFICATION
        evtSubGeneral = LuaEvent:NewIns("evtSubGeneral"),
        --she3+>>>>>
        evtWeaponNotAddExped = LuaEvent:NewIns("evtWeaponNotAddExped"),
        evtWeaponUpgradePop = LuaEvent:NewIns("evtWeaponUpgradePop"),
        evtMPWeaponUpMainOnClose = LuaEvent:NewIns("evtMPWeaponUpMainOnClose"),
        evtWeaponUpgradePopOnClose = LuaEvent:NewIns("evtWeaponUpgradePopOnClose"),
        evtWeaponToOneItemView = LuaEvent:NewIns("evtWeaponToOneItemView"),
    },
}

GunsmithConfig.SolutionSlotGroupID = {
}

GunsmithConfig.SolutionSlotGroupID[ESlotGroup.SafeHouseRange] = ESlotGroup.Player
GunsmithConfig.SolutionSlotGroupID[ESlotGroup.SafeHouseRangeMP] = ESlotGroup.MPApply

GunsmithConfig.MainUIConfig = {
    TabText = {
        GunsmithConfig.Loc.GunsmithMainTabPreview,
        GunsmithConfig.Loc.GunsmithMainTabFineTune,
        GunsmithConfig.Loc.GunsmithMainTabSolution,
        GunsmithConfig.Loc.GunsmithMainTabAppearance,
    },

    MPExceptRangeTabText = {
        GunsmithConfig.Loc.GunsmithMainTabPreview,
        GunsmithConfig.Loc.GunsmithMainTabFineTune,
        GunsmithConfig.Loc.GunsmithMainTabSolution,
        GunsmithConfig.Loc.GunsmithMainTabAppearance,
        GunsmithConfig.Loc.GunsmithMainTabWeaponUpgrade,
    },

    TabImage = {
        "PaperSprite'/Game/UI/UIAtlas/System/GunStand/BakedSprite/GunStand_icon_01.GunStand_icon_01'",
        "PaperSprite'/Game/UI/UIAtlas/System/GunStand/BakedSprite/GunStand_icon_02.GunStand_icon_02'",
        "PaperSprite'/Game/UI/UIAtlas/System/GunStand/BakedSprite/GunStand_icon_04.GunStand_icon_04'",
        "PaperSprite'/Game/UI/UIAtlas/System/GunStand/BakedSprite/GunStand_icon_03.GunStand_icon_03'",
    },

    MPExceptRangeTabImage = {
        "PaperSprite'/Game/UI/UIAtlas/System/GunStand/BakedSprite/GunStand_icon_01.GunStand_icon_01'",
        "PaperSprite'/Game/UI/UIAtlas/System/GunStand/BakedSprite/GunStand_icon_02.GunStand_icon_02'",
        "PaperSprite'/Game/UI/UIAtlas/System/GunStand/BakedSprite/GunStand_icon_04.GunStand_icon_04'",
        "PaperSprite'/Game/UI/UIAtlas/System/GunStand/BakedSprite/GunStand_icon_03.GunStand_icon_03'",
        "PaperSprite'/Game/UI/UIAtlas/System/GunStand/BakedSprite/GunStand_icon_05.GunStand_icon_05'",
    },

    GrowthRoadImage = {
        EmptyIconPath = "PaperSprite'/Game/UI/UIAtlas/System/PathofGrowth/BakedSprite/PatnofGrowth_CommonIcon_01.PatnofGrowth_CommonIcon_01'",
        SOLIconPath = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Icon_SOL.CommonHall_Icon_SOL'",
        MPIconPath = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Icon_MP.CommonHall_Icon_MP'",
    },

    SubTabPartText = {
        GunsmithConfig.Loc.GunsmithMainUISkinTabText,
        GunsmithConfig.Loc.GunsmithMainUIPendantTabText,
    },

    SubTabPartImage = {
        "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Market_Icon_0002.CommonHall_Market_Icon_0002'",
        "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Market_Icon_0003.CommonHall_Market_Icon_0003'",
    },
}

GunsmithConfig.WeaponTabTxtGroup = {
    [ItemConfig.EWeaponItemType.Unknown]                    =   GunsmithConfig.Loc.GunsmithInspectorWeaponItemTypeUnknown,
    [ItemConfig.EWeaponItemType.Rifle]                      =   GunsmithConfig.Loc.GunsmithInspectorWeaponItemTypeRifle,
    [ItemConfig.EWeaponItemType.Submachine]                 =   GunsmithConfig.Loc.GunsmithInspectorWeaponItemTypeSubmachine,
    [ItemConfig.EWeaponItemType.Shotgun]                    =   GunsmithConfig.Loc.GunsmithInspectorWeaponItemTypeShotgun,
    [ItemConfig.EWeaponItemType.LightMachine]               =   GunsmithConfig.Loc.GunsmithInspectorWeaponItemTypeLightMachine,
    [ItemConfig.EWeaponItemType.PrecisionShootingRifle]     =   GunsmithConfig.Loc.GunsmithInspectorWeaponItemTypePrecisionShootingRifle,
    [ItemConfig.EWeaponItemType.Sniper]                     =   GunsmithConfig.Loc.GunsmithInspectorWeaponItemTypeSniper,
    [ItemConfig.EWeaponItemType.Pistol]                     =   GunsmithConfig.Loc.GunsmithInspectorWeaponItemTypePistol,
    [ItemConfig.EWeaponItemType.Universal]                  =   GunsmithConfig.Loc.GunsmithInspectorWeaponItemTypeUniversal,
}

GunsmithConfig.WeaponTabImgPathGroup = {
    [ItemConfig.EWeaponItemType.Unknown]                    =   "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemClass_Icon_0000.Common_ItemClass_Icon_0000",
    [ItemConfig.EWeaponItemType.Rifle]                      =   "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_GunStand_Icon_0201.CommonHall_GunStand_Icon_0201",
    [ItemConfig.EWeaponItemType.Submachine]                 =   "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_GunStand_Icon_0202.CommonHall_GunStand_Icon_0202'",
    [ItemConfig.EWeaponItemType.Shotgun]                    =   "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_GunStand_Icon_0203.CommonHall_GunStand_Icon_0203'",
    [ItemConfig.EWeaponItemType.LightMachine]               =   "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_GunStand_Icon_0207.CommonHall_GunStand_Icon_0207'",
    [ItemConfig.EWeaponItemType.PrecisionShootingRifle]     =   "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_GunStand_Icon_0205.CommonHall_GunStand_Icon_0205'",
    [ItemConfig.EWeaponItemType.Sniper]                     =   "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_GunStand_Icon_0204.CommonHall_GunStand_Icon_0204'",
    [ItemConfig.EWeaponItemType.Pistol]                     =   "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_GunStand_Icon_0206.CommonHall_GunStand_Icon_0206'",
    [ItemConfig.EWeaponItemType.Universal]                  =   "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_GunStand_Icon_0208.CommonHall_GunStand_Icon_0208'",
}

GunsmithConfig.WeaponTabTxtList = {
    GunsmithConfig.Loc.GunsmithInspectorWeaponItemTypeUnknown,
    GunsmithConfig.Loc.GunsmithInspectorWeaponItemTypeRifle,
    GunsmithConfig.Loc.GunsmithInspectorWeaponItemTypeSubmachine,
    GunsmithConfig.Loc.GunsmithInspectorWeaponItemTypeShotgun,
    GunsmithConfig.Loc.GunsmithInspectorWeaponItemTypeLightMachine,
    GunsmithConfig.Loc.GunsmithInspectorWeaponItemTypePrecisionShootingRifle,
    GunsmithConfig.Loc.GunsmithInspectorWeaponItemTypeSniper,
    GunsmithConfig.Loc.GunsmithInspectorWeaponItemTypePistol,
    GunsmithConfig.Loc.GunsmithInspectorWeaponItemTypeUniversal,
}

GunsmithConfig.WeaponImgPathList  = {
    "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemClass_Icon_0000.Common_ItemClass_Icon_0000",
    "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_GunStand_Icon_0201.CommonHall_GunStand_Icon_0201",
    "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_GunStand_Icon_0202.CommonHall_GunStand_Icon_0202'",
    "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_GunStand_Icon_0203.CommonHall_GunStand_Icon_0203'",
    "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_GunStand_Icon_0207.CommonHall_GunStand_Icon_0207'",
    "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_GunStand_Icon_0205.CommonHall_GunStand_Icon_0205'",
    "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_GunStand_Icon_0204.CommonHall_GunStand_Icon_0204'",
    "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_GunStand_Icon_0206.CommonHall_GunStand_Icon_0206'",
    "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_GunStand_Icon_0208.CommonHall_GunStand_Icon_0208'",---万能武器图标
}

GunsmithConfig.MainWeaponTabTxtList = {
    GunsmithConfig.Loc.GunsmithInspectorWeaponItemTypeUnknown,
    GunsmithConfig.Loc.GunsmithInspectorWeaponItemTypeRifle,
    GunsmithConfig.Loc.GunsmithInspectorWeaponItemTypeSubmachine,
    GunsmithConfig.Loc.GunsmithInspectorWeaponItemTypeShotgun,
    GunsmithConfig.Loc.GunsmithInspectorWeaponItemTypeLightMachine,
    GunsmithConfig.Loc.GunsmithInspectorWeaponItemTypePrecisionShootingRifle,
    GunsmithConfig.Loc.GunsmithInspectorWeaponItemTypeSniper,
    GunsmithConfig.Loc.GunsmithInspectorWeaponItemTypeUniversal,
}

GunsmithConfig.RangeMainWeaponImgPathList  = {
    "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemClass_Icon_0000.Common_ItemClass_Icon_0000",
    "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_GunStand_Icon_0201.CommonHall_GunStand_Icon_0201",
    "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_GunStand_Icon_0202.CommonHall_GunStand_Icon_0202'",
    "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_GunStand_Icon_0203.CommonHall_GunStand_Icon_0203'",
    "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_GunStand_Icon_0207.CommonHall_GunStand_Icon_0207'",
    "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_GunStand_Icon_0205.CommonHall_GunStand_Icon_0205'",
    "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_GunStand_Icon_0204.CommonHall_GunStand_Icon_0204'",
    -- "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_GunStand_Icon_0206.CommonHall_GunStand_Icon_0206'",---之前代码是注释状态
    "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_GunStand_Icon_0208.CommonHall_GunStand_Icon_0208'",---万能武器图标
}

GunsmithConfig.PistolTabTxtList = {
    GunsmithConfig.Loc.GunsmithInspectorWeaponItemTypeUnknown,
    GunsmithConfig.Loc.GunsmithInspectorWeaponItemTypePistol,
    GunsmithConfig.Loc.GunsmithInspectorWeaponItemTypeUniversal,
}

GunsmithConfig.RangePistolImgPathList  = {
    "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemClass_Icon_0000.Common_ItemClass_Icon_0000'",
    "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_GunStand_Icon_0206.CommonHall_GunStand_Icon_0206'",
    "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_GunStand_Icon_0208.CommonHall_GunStand_Icon_0208'",---万能武器图标
}

GunsmithConfig.SolutionShareUIConfig = {
    {keyText = GunsmithConfig.Loc.GunsmithSolutionShareUITabImportText},
    {keyText = GunsmithConfig.Loc.GunsmithSolutionShareUITabShareText},
}

GunsmithConfig.SkinMainUISortConfig = {
    GunsmithConfig.Loc.GunsmithSkinDefaultMainUISortQualityText,
    GunsmithConfig.Loc.GunsmithSkinDefaultMainUISortGainTimeText,
}

return GunsmithConfig
