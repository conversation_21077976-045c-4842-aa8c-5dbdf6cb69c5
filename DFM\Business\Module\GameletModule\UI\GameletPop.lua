----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGamelet)
----- LOG FUNCTION AUTO GENERATE END -----------

local GameletApp = require "DFM.Business.Module.GameletModule.UI.GameletApp"
local GameletPop = ui("GameletPop", GameletApp)
local GameletLogic = require "DFM.Business.Module.GameletModule.Logic.GameletLogic"

function GameletPop:OnOpen()
    GameletApp.OnOpen(self)

    if not self._Esc then
        self._Esc = self:AddInputActionBinding("GameletPopEsc", EInputEvent.IE_Pressed, self.OnEsc,self, EDisplayInputActionPriority.Always)
    end
end

function GameletPop:OnClose()
    if self._Esc then
        self:RemoveInputActionBinding(self._Esc)
        self._Esc = nil
    end

    GameletApp.OnClose(self)
end

function GameletPop:OnEsc()
    for page, value in pairs(self.tSubUI) do
        GameletLogic.SendPandoraGameEvent(self.appId, "ClosePop", page)
    end
end

function GameletPop:CloseApp()
    Module.Gamelet.Config.evtPandoraPopClose:Invoke(self.appId)

    GameletApp.CloseApp(self)
end

function GameletPop:HidePage(widget,appInfo)
    GameletApp.HidePage(self, widget, appInfo)

    self:CloseIfNoPage()
end

function GameletPop:InitExtraDataInternal(args)
    GameletApp.InitExtraDataInternal(self, args)

    if self.parent == nil then
        self.parent = "DefaultPop"
    end
end

function GameletPop:OnNavBack()
    return true
end


return GameletPop