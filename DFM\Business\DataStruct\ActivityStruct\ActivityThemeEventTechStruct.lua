---@class ActivityThemeEventTechStruct : LuaObject
-- ActivityThemeEventTech
ActivityThemeEventTechStruct = class('ActivityThemeEventTechStruct', LuaObject)

---@param ActivityThemeEventTechStruct AT
function ActivityThemeEventTechStruct:Ctor(ActivityThemeEventTech)
    self:InitActivityStruct(ActivityThemeEventTech)
end

---@param ActivityThemeEventTechStruct AT
function ActivityThemeEventTechStruct:InitActivityStruct(ActivityThemeEventTech)
    self.ID = ActivityThemeEventTech.ID or 0
    self.Name = ActivityThemeEventTech.Name or 0
    self.IsCoreTech = ActivityThemeEventTech.IsCoreTech or 0
end

return ActivityThemeEventTechStruct