----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMPlayerReturn)
----- LOG FUNCTION AUTO GENERATE END -----------

---@diagnostic disable: missing-parameter

-- Imports from within the module
local IPlayerReturnSubActivity = require "DFM.Business.Module.PlayerReturnModule.SubActivities.PlayerReturnSubActivities"

-- DFUtil library
local VisibilityHelper        = require "DFM.Business.DataStruct.Common.Base.DFUtil.VisibilityHelper"
local HoverLink               = require "DFM.Business.DataStruct.Common.Base.DFUtil.HoverLink"
local ImageOutline            = require "DFM.Business.DataStruct.UIDataStruct.ImageOutline"
local ChronoTimer             = require "DFM.Business.Datastruct.Common.Agent.Timer.ChronoTimer"
local SubUIInfo               = require "DFM.Business.DataStruct.UIDataStruct.SubUIInfo"
local InputBindingAgent       = require "DFM.Business.DataStruct.Common.Agent.InputBindingAgent"
local PlayerReturnStatistics  = require "DFM.Business.Module.PlayerReturnModule.PlayerReturnStatistics"
local AnimManager             = require "DFM.Business.DataStruct.Common.Base.DFUtil.AnimManager"
local NavigationAgent         = require "DFM.Business.DataStruct.Common.Agent.NavigationAgent"
local EGPUINavGroupTransSimpleStrategy = import "EGPUINavGroupTransSimpleStrategy"
local FVector2D = import("Vector2D")

-- Common library
local Functional              = require "DFM.Business.DataStruct.Common.Base.Functional"
local Tween                   = require "DFM.Business.DataStruct.Common.Base.Tween"
local Table                   = require "DFM.Business.DataStruct.Common.Base.Table"

local WidgetUtil              = require   ("DFM.YxFramework.Util.WidgetUtil")

---@class PlayerReturnMainPanel: LuaUIBaseView, HasAnimManager, HasVisibilityHelper
local PlayerReturnMainPanel = ui("PlayerReturnMainPanel")

function PlayerReturnMainPanel:OnBack()
    if self._currPanelSubType == ReturnActivityType.ReturnActivityTypeMainPage then
        Facade.UIManager:CloseUI(self)
    else
        self:SwitchPanel(ReturnActivityType.ReturnActivityTypeMainPage, true)
    end
end

function PlayerReturnMainPanel:Ctor()
    Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.HideInfo)

    AnimManager.Create(self)
    self._navMgr = NavigationAgent:NewIns(self, nil, self)
    self._inputMgr = InputBindingAgent.New(self)
    self._armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
    VisibilityHelper.Create(self)

    self._slotByType = {}            ---@type table<integer, UIWidgetBase>
    self._subPanelByType = {}        ---@type table<integer, IPlayerReturnSubActivityPanel>
    self._visibilityAnimHelpers = {} ---@type table<integer, VisibilityAnimHelper>
    self._monitorBtnCallback = nil

    self._availableSlots = {
        self:Wnd("SubSlot1"         , UIWidgetBase),
        self:Wnd("SubSlot2"         , UIWidgetBase),
        self:Wnd("SubSlot3"         , UIWidgetBase),
        self:Wnd("SubSlot4"         , UIWidgetBase),
        self:Wnd("SubSlot5"         , UIWidgetBase),
    }

    ---@type UIButton[]
    self._imageButtonByType = {
        [ReturnActivityType.ReturnActivityTypeAttend]       = self:Wnd("DFButton"     , UIButton),
        [ReturnActivityType.ReturnActivityTypeFight]        = self:Wnd("DFButton_1"   , UIButton),
        [ReturnActivityType.ReturnActivityTypeNormalTask]   = self:Wnd("DFButton_2"   , UIButton),
        [ReturnActivityType.ReturnActivityTypeNewContent]   = self:Wnd("DFButton_150" , UIButton),
    }

    ---@type UIImage[]
    self._imageButtonHoverImg = {
        [ReturnActivityType.ReturnActivityTypeAttend]       = self:Wnd("DFButton_Hover"      , UIImage),
        [ReturnActivityType.ReturnActivityTypeFight]        = self:Wnd("DFButton_1_Hover"    , UIButton),
        [ReturnActivityType.ReturnActivityTypeNormalTask]   = self:Wnd("DFButton_2_Hover"    , UIImage),
        [ReturnActivityType.ReturnActivityTypeNewContent]   = self:Wnd("DFButton_150_Hover"  , UIImage),
    }

    ---@type UITextBlock[]
    self._buttonTextByType = {
        [ReturnActivityType.ReturnActivityTypeAttend]       = self:Wnd("DFTextBlock_115" , UITextBlock),
        [ReturnActivityType.ReturnActivityTypeFight]        = self:Wnd("DFTextBlock_2"   , UITextBlock),
        [ReturnActivityType.ReturnActivityTypeNormalTask]   = self:Wnd("DFTextBlock"     , UITextBlock),
        [ReturnActivityType.ReturnActivityTypeNewContent]   = self:Wnd("DFTextBlock_1"   , UITextBlock),
    }

    ---@type UIWidgetBase[]
    self._redDotAttachmentByType = {
        [ReturnActivityType.ReturnActivityTypeAttend]       = self:Wnd("DFCanvasPanel_386" , UIWidgetBase),
        [ReturnActivityType.ReturnActivityTypeFight]        = self:Wnd("DFCanvasPanel_2"   , UIWidgetBase),
        [ReturnActivityType.ReturnActivityTypeNormalTask]   = self:Wnd("DFCanvasPanel"     , UIWidgetBase),
        [ReturnActivityType.ReturnActivityTypeNewContent]   = self:Wnd("DFCanvasPanel_1"   , UIWidgetBase),
    }

    ---@type HoverLink[]
    self._hoverLinkCtrlByType = {}

    self._wtModeTag                      = self:Wnd("WBP_Activity_ModeTag"   , UIWidgetBase)     --模式标签
    self._wtCountdownText                = self:Wnd("DFTextBlock_213"        , UITextBlock)      --倒计时显示
    self._wtTitle                        = self:Wnd("DFTextBlock_Title"      , UITextBlock)      --主标题
    self._wtMessage                      = self:Wnd("DFMTextBlock_129"       , UITextBlock)      --主标题下文案
    self._wtOffsetRoot                   = self:Wnd("OffsetRootPanel"        , UIWidgetBase)     --背景移动编组
    self._wtAltBackground                = self:Wnd("DFImage_Bg_1", UIImage)

    self._rewardEventListener1           = nil
    self._rewardEventListener2           = nil

    self._wtModeTag:SetStyle(self._armedForceMode)

    self:CreateSubPanels()
    self:EnablePanelSwitchButtons()
    self:_RegisterInputBindings()

    local subActivity = IPlayerReturnSubActivity.Get(ReturnActivityType.ReturnActivityTypeMainPage, self._armedForceMode)
    self._wtTitle:SetText(subActivity:GetName())
    self._wtMessage:SetText("") --subActivity:GetDesc()
    self._wtTitle:PlayAnim_ComputeTextBlock()

    for subType, attachWidget in pairs(self._redDotAttachmentByType) do
        local actv = IPlayerReturnSubActivity.Get(subType, self._armedForceMode)
        Module.ReddotTrie:RegisterStaticReddotDotWithConfig(attachWidget, {{
            obType = EReddotTrieObserverType.PlayerReturn,
            key = actv:GetRedDotKey(),
            reddotStyle = {
                placeOffset = FVector2D(40, 23)
            }
        }})
    end
end

function PlayerReturnMainPanel:_RegisterInputBindings()
    self._inputMgr:AddBinding(
        "MonitorKeyIcon",
        {
            actionName = "Common_LeftStickClick_Gamepad",
            callback   = self._OnMonitorKeyIcon,
            caller     = self,
        },
        true -- initially disabled
    )

    self._inputMgr:AddBinding(
        "LT_PrevSection",
        {
            actionName  = "Common_SwitchToPrevTab_Trigger",
            callback    = CreateCallBack(self._PrevOrNextSection, self, -1),
            caller      = self,
        },
        true -- initially disabled
    )

    self._inputMgr:AddBinding(
        "RT_NextSection",
        {
            actionName  = "Common_SwitchToNextTab_Trigger",
            callback    = CreateCallBack(self._PrevOrNextSection, self, 1),
            caller      = self,
        },
        true -- initially disabled
    )
end

function PlayerReturnMainPanel:_PrevOrNextSection(delta)

    local rotateOrder = {
        ReturnActivityType.ReturnActivityTypeAttend,
        ReturnActivityType.ReturnActivityTypeFight,
        ReturnActivityType.ReturnActivityTypeNormalTask,
        ReturnActivityType.ReturnActivityTypeNewContent,
    }

    local currOrder
    for order, subType in pairs(rotateOrder) do
        if subType == self._currPanelSubType then
            currOrder = order
            break
        end
    end

    if currOrder == nil then
        return
    end

    local target = rotateOrder[(currOrder - 1 + delta) % 4 + 1]
    self:SwitchPanel(target, true)
end

---@param enterPanelSubType integer ActivityReturnType
function PlayerReturnMainPanel:OnInitExtraData(enterPanelSubType)
    self._enterPanelSubType = enterPanelSubType or ReturnActivityType.ReturnActivityTypeMainPage
end

function PlayerReturnMainPanel:SetMonitorKeyIconCallback(fMonitorKeyIconCallback)
    if not IsHD() then return end
    
    self._fMonitorKeyIconCallback = fMonitorKeyIconCallback
    if fMonitorKeyIconCallback then
        self._wtMonitorKeyIcon_HD:SetOnlyDisplayOnGamepad(true)
        self._wtMonitorKeyIcon_HD:InitByDisplayInputActionName("Common_LeftStickClick_Gamepad", true, 0, false)
        self._wtMonitorKeyIcon_HD:Visible()
        self._inputMgr:Activate("MonitorKeyIcon")
    else
        self._wtMonitorKeyIcon_HD:Collapsed()
        self._inputMgr:Deactivate("MonitorKeyIcon")
    end
end

function PlayerReturnMainPanel:_OnMonitorKeyIcon()
    if self._fMonitorKeyIconCallback then
        self._fMonitorKeyIconCallback()
    end
end

function PlayerReturnMainPanel:OnShow()
    self:PlayAnimThen(self.WBP_Reflow_Main_loop, {loopCnt = 0})
end

function PlayerReturnMainPanel:OnShowBegin()
    if self._enterPanelSubType then
        self:SwitchPanel(self._enterPanelSubType, true)
        self._enterPanelSubType = nil
    elseif self._currPanelSubType then
        if self._currPanelSubType ~= ReturnActivityType.ReturnActivityTypeMainPage then
            self._subPanelByType[self._currPanelSubType]:OnPlayerReturnEnter()
        else
            self:EnablePanelSwitchButtons(true)
        end
    end

    Module.CommonBar:BindPersistentBackHandler(self.OnBack, self)

    local actv = IPlayerReturnSubActivity.Get(ReturnActivityType.ReturnActivityTypeMainPage, self._armedForceMode)
    
    Module.CommonBar:ChangeBackBtnText(actv:GetName())
    Module.CommonBar:RegStackUITopBarTitle(self, actv:GetName())

    self._minuteTicker = ChronoTimer:New("minute", nil, self._UpdateCountDownText, self)
    self:_UpdateCountDownText()

    self._rewardEventListener1 = Server.ActivityServer.Events.evtActivityTaskReward:AddListener(self._ShowRewardPop, self)
    self._rewardEventListener2 = Server.ActivityServer.Events.evtActivityMilestoneAward:AddListener(self._ShowRewardPop, self)

    self._inputMgr:Activate("LT_PrevSection")
    self._inputMgr:Activate("RT_NextSection", true)

    if self._currPanelSubType == ReturnActivityType.ReturnActivityTypeMainPage then
        Timer.DelayCall(0,
            function()
                -- 子版块入口的显示顺序
                local displayOrder = {
                    ReturnActivityType.ReturnActivityTypeAttend,
                    ReturnActivityType.ReturnActivityTypeFight,
                    ReturnActivityType.ReturnActivityTypeNormalTask,
                    ReturnActivityType.ReturnActivityTypeNewContent,
                }
    
                -- 寻找第一个未完成的子板块，聚焦入口按钮
                for idx, subType in ipairs(displayOrder) do
                    local actv = IPlayerReturnSubActivity.Get(subType, self._armedForceMode)
                    local progress, total = actv:GetDisplayedProgress()
                    if progress < total then
                        self._navMgr:FocusWidget(self._imageButtonByType[subType], false)
                        return
                    end
                end

                -- 默认聚焦第一个
                self._navMgr:FocusWidget(self._imageButtonByType[displayOrder[1]], false) 
            end
            , self
        )
    end
end

function PlayerReturnMainPanel:_ShowRewardPop(dataChange, expandInfo)
    local actv = IPlayerReturnSubActivity.Get(self._currPanelSubType, self._armedForceMode)
    local activityInfo = actv:GetActivityInfo()
    local activityID
    if activityInfo then
        activityID = activityInfo.actv_id
    end
    Module.Activity:ShowErrorTipIfNeeded(expandInfo)
    Module.Activity:ShowDataChangeAwards(dataChange, activityID)
end

function PlayerReturnMainPanel:OnHideBegin()
    ChronoTimer:Cancel(self._minuteTicker)
    Server.ActivityServer.Events.evtActivityTaskReward:RemoveListenerByHandle(self._rewardEventListener1)
    Server.ActivityServer.Events.evtActivityMilestoneAward:RemoveListenerByHandle(self._rewardEventListener2)
    self._inputMgr:DeactivateAll()
    self._inputMgr:BindInputTypeChangeCallback(nil)
    self:StopWidgetAnim(self.WBP_Reflow_Main_loop)
end

function PlayerReturnMainPanel:_UpdateCountDownText()
    self._wtCountdownText:SetText(Module.PlayerReturn:GetCountdownText(false))
end

function PlayerReturnMainPanel:OnClose()
    Facade.UIManager:RemoveAllSubUI(self)

    self._inputMgr:ClearAll()
end

function PlayerReturnMainPanel:CreateSubPanels()
    for subType in pairs(IPlayerReturnSubActivity:GetRegisteredTypes()) do
        local impl = IPlayerReturnSubActivity.Get(subType, self._armedForceMode)
        if impl:GetType() ~= ReturnActivityType.ReturnActivityTypeMainPage then
            local slot = table.remove(self._availableSlots, 1)
            local uiIns = getfromweak(Facade.UIManager:AddSubUI(self, impl:GetSubPanelNavID(), slot)) ---@type IPlayerReturnSubActivityPanel
        
            uiIns:Hide(false, true)
            uiIns:SetParent(self)

            self._slotByType[subType] = slot
            self._subPanelByType[subType] = uiIns
        end
    end

    self._userSwitchPanel = Functional.HighFrequencyFilter(self.SwitchPanel, 0.5)
end

function PlayerReturnMainPanel:EnablePanelSwitchButtons(bEnable)
    -- ---@type PlayerReturnMainTips
    -- local mainPage = self._subPanelByType[ReturnActivityType.ReturnActivityTypeMainPage]
    if bEnable == nil then bEnable = true end

    if bEnable then

        if self._navMgr:Get("PanelSwitchButtons") == nil then
            local EUINavigation = import("EUINavigation")
            local navGroup = self._navMgr:CreateGroup({
                id = "PanelSwitchButtons",
                rootWidget = self,
                members = self._imageButtonByType,
                bStack = true,
                bShowSelector = false,
                wrapDirections = {EUINavigation.Left, EUINavigation.Right},
            })
        end

        for subType, imageButton in pairs(self._imageButtonByType) do
            local hoverImg = self._imageButtonHoverImg[subType]
            imageButton:Visible()

            imageButton:RemoveEvent("OnHovered")
            imageButton:Event("OnHovered"  , CreateCallBack(function() hoverImg:SelfHitTestInvisible() end, self))

            imageButton:RemoveEvent("OnUnhovered")
            imageButton:Event("OnUnhovered", CreateCallBack(function() hoverImg:Collapsed() end, self))

            imageButton:RemoveEvent("OnClicked")
            imageButton:Event("OnClicked",CreateCallBack(function() self:_userSwitchPanel(subType, true) end, self))

            local mousePos = UWidgetLayoutLibrary.GetMousePositionOnPlatform()
            local bInside = UIUtil.CheckAbsolutePointInsideWidget(imageButton, mousePos)
            if bInside then
                hoverImg:SelfHitTestInvisible()
            else
                hoverImg:Collapsed()
            end

            local actv = IPlayerReturnSubActivity.Get(subType, self._armedForceMode)
            local textBlock = self._buttonTextByType[subType]
            local progress, total = actv:GetDisplayedProgress()
            local progressText = string.format("(%d/%d)", progress, total)
            textBlock:SetText(progressText..actv:GetName())

            actv:UpdateMainPageButton(self)
        end
    else
        -- self._navMgr:RemoveGroup("PanelSwitchButtons")
        for subType, imageButton in pairs(self._imageButtonByType) do
            imageButton:RemoveEvent("OnHovered")
            imageButton:RemoveEvent("OnUnhovered")
            imageButton:RemoveEvent("OnClicked")
        end
    end

    -- self:_EnableImageButtons(true)
end

function PlayerReturnMainPanel:SwitchPanel(targetSubType, bWithAnim)
    if self._currPanelSubType == targetSubType then return end
    local panelTypeBeforeSwitch = self._currPanelSubType

    -- 通知当前面板离开
    if self._currPanelSubType and self._subPanelByType[self._currPanelSubType] then
        self._subPanelByType[self._currPanelSubType]:OnPlayerReturnLeave(targetSubType)
    end

    -- 实际控制子面板显隐
    for subType, subPanel in pairs(self._subPanelByType) do
        if subType == targetSubType then
            subPanel:Show(true, not bWithAnim)
        else
            subPanel:Hide(true, not bWithAnim)
        end
    end

    if targetSubType ~= ReturnActivityType.ReturnActivityTypeMainPage then
        -- 通知切换到的面板进入
        self._subPanelByType[targetSubType]:OnPlayerReturnEnter(self._currPanelSubType)

        -- 经分上报
        LogAnalysisTool.SignButtonClicked(PlayerReturnStatistics:GetMainPanelUIStatID(targetSubType))
        self:EnablePanelSwitchButtons(false)
    else
        self:EnablePanelSwitchButtons(true)
        local wtRestoreFocus = self._imageButtonByType[panelTypeBeforeSwitch]
        if wtRestoreFocus then
            Timer.DelayCall(0, function() self._navMgr:FocusWidget(wtRestoreFocus) end, self)
        end
    end

    -- 更新全局信息/显示
    -- local subActivity = IPlayerReturnSubActivity.Get(targetSubType, self._armedForceMode)
    -- self._wtTitle:SetText(subActivity:GetName())
    -- self._wtMessage:SetText(subActivity:GetDesc())
    -- self._wtTitle:PlayAnim_ComputeTextBlock()
    self._currPanelSubType = targetSubType

    local bgSwitchAnim = bWithAnim and (not self:IsAnimationPlaying(self.WBP_Reflow_Sub_to_Main)) and (not self:IsAnimationPlaying(self.WBP_Reflow_Main_to_Sub)) and panelTypeBeforeSwitch
    if panelTypeBeforeSwitch ~= ReturnActivityType.ReturnActivityTypeMainPage and
       targetSubType == ReturnActivityType.ReturnActivityTypeMainPage then
        if bgSwitchAnim then
            self:PlayAnimThen(self.WBP_Reflow_Sub_to_Main)
        else
            self:SkipAnimation(self.WBP_Reflow_Sub_to_Main)
        end
    elseif panelTypeBeforeSwitch == ReturnActivityType.ReturnActivityTypeMainPage and
           targetSubType ~= ReturnActivityType.ReturnActivityTypeMainPage then
        if bgSwitchAnim then
            self:PlayAnimThen(self.WBP_Reflow_Main_to_Sub)
        else
            self:SkipAnimation(self.WBP_Reflow_Main_to_Sub)
        end
    end
end


return PlayerReturnMainPanel
