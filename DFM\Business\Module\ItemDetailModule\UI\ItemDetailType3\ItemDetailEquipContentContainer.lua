----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMItemDetail)
----- LOG FUNCTION AUTO GENERATE END -----------



local ItemDetailContentBase = require("DFM.Business.Module.ItemDetailModule.UI.ItemDetailContentBase")
local ItemDetailConfig = require("DFM.Business.Module.ItemDetailModule.ItemDetailConfig")
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"

---@class ItemDetailEquipContentContainer : ItemDetailContentBase
local ItemDetailEquipContentContainer = ui("ItemDetailEquipContentContainer", ItemDetailContentBase)

function ItemDetailEquipContentContainer:Ctor()
    self._itemInfo = nil

	-- 容量
	self._wtBagCapacityPanel = self:Wnd("WBP_ItenDetail_BagCapacity_C_0", UIWidgetBase)
	self._wtBagCapacity = self._wtBagCapacityPanel:Wnd("wDesc", UITextBlock)
	self._wtBagCapacityName = self._wtBagCapacityPanel:Wnd("DFTextBlock_119", UITextBlock)

	-- buf效果
	self._wtBuffWrapPanel = self:Wnd("wBackpack", UIWidgetBase)
	self._wtBuffWrapPanel:Collapsed()
	self._wtwBuffWrap = self:Wnd("wBuffWrap", UIWidgetBase)

	-- 装备重量
	self._wtWeightPanel = self:Wnd("WBP_ItemDetailContent_Item_1", UIWidgetBase)
	self._wtWeightText = self:Wnd("DFTextBlock", UITextBlock)
end

function ItemDetailEquipContentContainer:OnShow()
	self:_AddEventListener()
end

function ItemDetailEquipContentContainer:OnHide()

end

function ItemDetailEquipContentContainer:OnClose()
	Facade.UIManager:ClearSubUIByParent(self, self._wtwBuffWrap)
end

function ItemDetailEquipContentContainer:_AddEventListener()
end

function ItemDetailEquipContentContainer:_RemoveEventListener()
end

function ItemDetailEquipContentContainer:Reset()
end

---@param item ItemBase
function ItemDetailEquipContentContainer:SetItem(item)
    self._itemInfo = item
	self:_SetContainerDetail()

end

function ItemDetailEquipContentContainer:_SetContainerDetail()
	local funcCfg
	local equipmentFeature = self._itemInfo:GetFeature(EFeatureType.Equipment)
	if equipmentFeature:IsBag() then
		self._wtBagCapacityPanel:BP_SetType(0)
		funcCfg = ItemConfigTool.GetBagFuncTable(self._itemInfo.id)
	else
		self._wtBagCapacityPanel:BP_SetType(1)
		funcCfg = ItemConfigTool.GetChestHangingFuncTable(self._itemInfo.id)
	end

	self._wtBagCapacity:SetText(string.format("%d", equipmentFeature:GetContainerSize()))
	self._wtBuffWrapPanel:SetVisibility(ESlateVisibility.Collapsed)
	Facade.UIManager:RemoveSubUIByParent(self, self._wtwBuffWrap)

	if funcCfg then
		-- 获取buffInfos
		local buffInfos = {}
		if funcCfg.AddBuffs then
			for insertIDX, value in pairs(funcCfg.AddBuffs) do
				local buffCfg = ItemConfigTool.GetBuffById(value.BuffId)
				if buffCfg then
					table.insert(buffInfos, {buffId = value.BuffId, buffDebuffType = buffCfg.BuffDebuffType, insertIDX = insertIDX})
				end
			end
		end
		if buffInfos and #buffInfos > 0 then
			self._wtBuffWrapPanel:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
			table.sort(buffInfos, ItemConfigTool.SortByBuffDebuffType)
			for _, buffInfo in ipairs(buffInfos) do
				local buffCfg = ItemConfigTool.GetBuffById(buffInfo.buffId)
				local extraDesc = nil
				if buffCfg then
					if buffCfg.Duration > 0 then
						extraDesc = buffCfg.Duration.."s"
					end
					local weakUIIns = Facade.UIManager:AddSubUI(self, UIName2ID.ItemDetailBufferItem, self._wtwBuffWrap, nil, buffCfg, extraDesc)
					local uiIns = getfromweak(weakUIIns)
					if uiIns then
						uiIns:SelfHitTestInvisible()
					end
				end
			end
		end
	end

	-- 装备重量
	if self._itemInfo.weight < 0.1 then
		self._wtWeightText:SetText(ItemDetailConfig.Loc.weigthMin)
	else
		self._wtWeightText:SetText(StringUtil.Key2StrFormat(ItemDetailConfig.Loc.weigth, {["weight"]=string.format("%.2f", self._itemInfo.weight)}))
	end
	self._wtWeightPanel:SetVisibility(self._itemInfo.weight == 0 and ESlateVisibility.Collapsed or ESlateVisibility.SelfHitTestInvisible)

	if ItemHelperTool.GetMainTypeById(self._itemInfo.id) == EItemType.Equipment and ItemHelperTool.GetSubTypeById(self._itemInfo.id) == EEquipmentType.SafeBox then
		self._wtWeightPanel:SetVisibility(ESlateVisibility.Collapsed)
		local length, height = equipmentFeature:GetRealLengthAndHeight()
		self._wtBagCapacity:SetText(string.format("%d<customstyle color=\"C002\">(%dx%d)</>",equipmentFeature:GetContainerSize(), length, height))
		self._wtBagCapacityName:SetText(ItemDetailConfig.Loc.capacity)
	else
		self._wtBagCapacityName:SetText(ItemDetailConfig.Loc.bagSpace)
	end
end

return ItemDetailEquipContentContainer