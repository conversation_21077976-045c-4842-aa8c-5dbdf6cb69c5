----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSandBoxMap)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class QuestTraceMapItem : LuaUIBaseView
local QuestTraceMapItem = ui("QuestTraceMapItem")

function QuestTraceMapItem:Ctor()
	self._wtQuestTitle = self:Wnd("wtStrategicQuestTitleBtn", UIWidgetBase)
	-- self._wtQuestTitle:SetSelectedType(0)
	self._wtQuestTitleBtn = self._wtQuestTitle:Wnd("DFButton_QuestItem", UIButton)
    self._wtQuestTitleBtn:Event("OnClicked", self._OnItemClick, self)

	self._wtQuestNameText = self._wtQuestTitle:Wnd("DFTextBlock_QuestName", UITextBlock)
	self._wtQuestType = self._wtQuestTitle:Wnd("WBP_TaskSystemCommonQuestType", UIWidgetBase)
	self._wtQuestTypeText = self._wtQuestType:Wnd("DFTextBlock_QuestType", UITextBlock)

	self._wtSeasonStarPanel = self._wtQuestTitle:Wnd("DFHorizontalBox_77", UIWidgetBase)
	self._wtStarNum = self._wtQuestTitle:Wnd("DFTextBlock_182", UITextBlock)
	self._wtStarIcon_1 = self._wtQuestTitle:Wnd("DFImage_1", UIWidgetBase)
	self._wtStarIcon_2 = self._wtQuestTitle:Wnd("DFImage_2", UIWidgetBase)
	self._wtStarIcon_3 = self._wtQuestTitle:Wnd("DFImage_3", UIWidgetBase)
	self._wtStarIcon_4 = self._wtQuestTitle:Wnd("DFImage_4", UIWidgetBase)
	self._wtStarIcon_5 = self._wtQuestTitle:Wnd("DFImage_5", UIWidgetBase)
	self._starIconList = {
		self._wtStarIcon_1,
		self._wtStarIcon_2,
		self._wtStarIcon_3,
		self._wtStarIcon_4,
		self._wtStarIcon_5
	}

	self._wtQuestContent = self:Wnd("DFVerticalBox_All", UIWidgetBase)
	self._wtQuestConditionBox = self:Wnd("wChallengeItemBox", UIWidgetBase)
	self._wtQuestObjectiveBox = self:Wnd("wMissionBtnBox", UIWidgetBase)
	self._wtQuestConditionBox:ClearChildren()
	self._wtQuestObjectiveBox:ClearChildren()
	self._mapId = nil
	self._bSelected = false
	self._wtQuestTitle:SetSelectedType(self._bSelected)
	self._wtQuestContent:Collapsed()
end

------------------------------------ Override function ------------------------------------
function QuestTraceMapItem:OnInitExtraData(questInfo, mapId, bShowUnderLine)
	if questInfo == nil then
		logwarning(" QuestTraceMapItem OnInitExtraData : questInfo is nil ")
		return
	end
	self._mapId = mapId
	self._wtQuestNameText:SetText(questInfo.name)
	self._wtQuestType:SetFontType(0)
    if questInfo.type == QuestType.Mission then
        self._wtQuestTypeText:SetText(Module.Quest.Config.Loc.QuestTypeLocMission)
        self._wtQuestType:SetColorType(2)
    elseif questInfo.type == QuestType.ImportantQuest then
        self._wtQuestTypeText:SetText(Module.Quest.Config.Loc.QuestTypeLocImportant)
        self._wtQuestType:SetColorType(1)
    elseif questInfo.type == QuestType.Branch then
        self._wtQuestTypeText:SetText(Module.Quest.Config.Loc.QuestTypeLocBranch)
        self._wtQuestType:SetColorType(3)
    elseif questInfo.type == QuestType.SeasonQuest then
		self:_InitSeasonQuestInfo(questInfo)
    end
	self._wtQuestTitle:SetLineVisible(bShowUnderLine)
    self:_InitQuestCondition(questInfo)
    self:_InitQuestObjective(questInfo)
end

function QuestTraceMapItem:OnOpen()
	self:_AddEventListener()
end

function QuestTraceMapItem:OnClose()
	self:_RemoveEventListener()
	Facade.UIManager:RemoveSubUIByParent(self, self._wtQuestObjectiveBox)
	Facade.UIManager:RemoveSubUIByParent(self, self._wtQuestConditionBox)
end

function QuestTraceMapItem:OnShow()
    loginfo("[SandBoxTaskPanel] QuestTraceMapItem OnShow")
end

function QuestTraceMapItem:OnHide()
    loginfo("[SandBoxTaskPanel] QuestTraceMapItem OnHide")
    Facade.LuaFramingManager:CancelAllFrameTasks(self)
end

------------------------------------ private function ------------------------------------
function QuestTraceMapItem:_AddEventListener()
end

function QuestTraceMapItem:_RemoveEventListener()
	-- self:RemoveAllLuaEvent()
end

function QuestTraceMapItem:_OnItemClick()
	self._bSelected = not self._bSelected
	self._wtQuestTitle:SetSelectedType(self._bSelected)
	if self._bSelected then
		self._wtQuestContent:SelfHitTestInvisible()
	else
		self._wtQuestContent:Collapsed()
	end
end

function QuestTraceMapItem:_InitQuestCondition(questInfo)
	Facade.UIManager:RemoveSubUIByParent(self, self._wtQuestConditionBox)
	local conditions = questInfo:GetAllQusetConditions()
	if #conditions > 0 then
		self._wtQuestConditionBox:SelfHitTestInvisible()
		for idx, conditionInfo in ipairs(conditions) do
			local fAddSubTask=function (self)
			    Facade.UIManager:AddSubUI(self, UIName2ID.QuestConditionItem, self._wtQuestConditionBox, nil, conditionInfo, idx)
			end
			Facade.LuaFramingManager:RegisterFrameTask(fAddSubTask,self)
		end
	else
		self._wtQuestConditionBox:Collapsed()
	end
end

function QuestTraceMapItem:_InitQuestObjective(questInfo)
	 Facade.UIManager:RemoveSubUIByParent(self, self._wtQuestObjectiveBox)
	for idx, objInfo in ipairs(questInfo:GetQusetObjectives()) do
		if objInfo.bIsFinsih == false then
			for _, id in ipairs(objInfo.mapId) do
				if self._mapId == id then
					local fAddSubTask=function (self)
						Facade.UIManager:AddSubUI(self, UIName2ID.QuestTraceMapObjectiveItem, self._wtQuestObjectiveBox, nil, objInfo, idx)
					end
					Facade.LuaFramingManager:RegisterFrameTask(fAddSubTask,self)
					break
				end
			end
		end
	end
end

function QuestTraceMapItem:_InitSeasonQuestInfo(questInfo)
	local questlineInfo = Server.QuestServer:GetSeasonLineData(questInfo._seasonLineID)
	if questlineInfo then
		local stageInfo = questlineInfo:GetStageInfoByID(questInfo._seasonStageID)
		local groupInfo = questlineInfo:GetGroupInfo(questInfo._seasonGroupID)
		if stageInfo and groupInfo then
			-- set type text
			if questlineInfo:IsMainGroup(questInfo._seasonStageID, questInfo._seasonGroupID) then
				self._wtQuestTypeText:SetText(Module.Quest.Config.Loc.QuestTypeLocSeasonMain)
				self._wtQuestType:SetColorType(0)
			else
				local str = tostring(stageInfo.order) .. "-" .. tostring(groupInfo.order) 
				self._wtQuestTypeText:SetText(str .. Module.Quest.Config.Loc.QuestTypeLocSeasonBranch)
				self._wtQuestType:SetColorType(4)
			end

			-- Add group stars
			if groupInfo.rewardStar == 0 then 
				self._wtSeasonStarPanel:Collapsed()
			elseif groupInfo.rewardStar <= 5 then
				for i = 1, groupInfo.rewardStar, 1 do
					self._starIconList[i]:SelfHitTestInvisible()
				end
				for i = groupInfo.rewardStar + 1, 5, 1 do
					self._starIconList[i]:Collapsed()
				end
				self._wtStarNum:Collapsed()
			else
				self._wtStarIcon_1:SelfHitTestInvisible()
				self._wtStarIcon_2:Collapsed()
				self._wtStarIcon_3:Collapsed()
				self._wtStarIcon_4:Collapsed()
				self._wtStarIcon_5:Collapsed()
				self._wtStarNum:SetText(tostring(groupInfo.rewardStar))
			end

		else
			logerror("StageInfo or GroupInfo Invalid")
		end
	else
		logerror("Season line Info Invalid")
	end
end
------------------------------------ Event listener function ------------------------------------

------------------------------------ public function ------------------------------------
function QuestTraceMapItem:HideLine()
	-- self._wtLine:Collapsed()
end

function QuestTraceMapItem:ShowLine()
	-- self._wtLine:SelfHitTestInvisible()
end

return QuestTraceMapItem