----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGunsmith)
----- LOG FUNCTION AUTO GENERATE END -----------




local GunsmithEnvironmentLogic = require "DFM.Business.Module.GunsmithModule.Logic.GunsmithEnvironmentLogic"
local GPModularWeaponDescLogic = require "DFM.Business.Module.GunsmithModule.Logic.GunsmithCPPLogic.GPModularWeaponDescLogic"
local EGunsmithUIState = require "DFM.Business.Module.GunsmithModule.Define.EGunsmithUIState"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local EGunsmithSimulateStatePropmtUIAnimState = require "DFM.Business.Module.GunsmithModule.Define.EGunsmithSimulateStatePropmtUIAnimState"
local GunsmithEnvironmentCameraConfig = require "DFM.Business.Module.GunsmithModule.Data.Environment.GunsmithEnvironmentCameraConfig"
local GunsmithUIParam = require "DFM.Business.DataStruct.GunsmithStruct.GunsmithUIParam"

local GunsmithUIContextLogic = require "DFM.Business.Module.GunsmithModule.Logic.GunsmithUIContextLogic_Linker"

------------------------------------------------------------------------
--------------------------------  data  --------------------------------
------------------------------------------------------------------------

function GunsmithUIContextLogic.SetUIID(uiID)
    local context = GunsmithUIContextLogic.GetUIContext()
    context:SetUIID(uiID)
end

function GunsmithUIContextLogic.SetUILastID(uiLastID)
    local context = GunsmithUIContextLogic.GetUIContext()
    context:SetUILastID(uiLastID)
end

function GunsmithUIContextLogic.SetOpreationSource(source)
    local context = GunsmithUIContextLogic.GetUIContext()
    context:SetOpreationSource(source)
end

function GunsmithUIContextLogic.SetIsSubStageChangeEnter(bIsSubStageChangeEnter)
    local context = GunsmithUIContextLogic.GetUIContext()
    context:SetIsSubStageChangeEnter(bIsSubStageChangeEnter)
end

function GunsmithUIContextLogic.SetUIContext4EnterModule()
    local bIsInRange = Server.IrisSafeHouseServer.bIsInRange
    if bIsInRange then
        GunsmithUIContextLogic.SetUIStateRange()
    end
end

function GunsmithUIContextLogic.ResetUIContext()
    GunsmithUIContextLogic.SetUnSycnRangeSolution(false)
    GunsmithUIContextLogic.SetUIStateDefault()
    GunsmithUIContextLogic.SetSimulateUIAnimState(EGunsmithSimulateStatePropmtUIAnimState.Idle)

    GunsmithUIContextLogic.MissionDataReset()

    GunsmithUIContextLogic.FinetuneMainUICheckBoxStateReset()
end

function GunsmithUIContextLogic.SetPropInfo(propinfo)
    local context = GunsmithUIContextLogic.GetUIContext()
    context:SetPropInfo(propinfo)
end

function GunsmithUIContextLogic.SetCameraType(type)
    local context = GunsmithUIContextLogic.GetUIContext()
    context:SetCameraType(type)
end

function GunsmithUIContextLogic.SetCameraDefaultPoint(point)
    local context = GunsmithUIContextLogic.GetUIContext()
    context:SetCameraDefaultPoint(point)
end

function GunsmithUIContextLogic.SetCameraFocusCenterPoint(point)
    local context = GunsmithUIContextLogic.GetUIContext()
    context:SetCameraFocusCenterPoint(point)
end

function GunsmithUIContextLogic.SetFocusSocket(socket)
    local context = GunsmithUIContextLogic.GetUIContext()
    context:SetFocusSocket(socket)
end

function GunsmithUIContextLogic.SetWeaponDescription4SyncFromServer()
    local context = GunsmithUIContextLogic.GetUIContext()
    context:SetWeaponDescription4SyncFromServer()
end

function GunsmithUIContextLogic.SetUIState(state, bFromDataUpdated)
    -- 靶场返回方案未同步前强制预改装
    state = GunsmithUIContextLogic.ProcessUnSycnRangeSolutionState(state)

    local uiContext = GunsmithUIContextLogic.GetUIContext()
    local lastUIstate = GunsmithUIContextLogic.GetUIState()
    if lastUIstate == state then
        -- if not bFromDataUpdated then
        --     GunsmithUIContextLogic.SetUILastState()
        -- end
        return
    end

    uiContext:SetUIState(state)

    bFromDataUpdated = setdefault(bFromDataUpdated, false)
    local processor = GunsmithUIContextLogic._GetActiveUIContextProcessor()
    processor:ProcessUIStateUpdated(state, bFromDataUpdated)
end

function GunsmithUIContextLogic.ProcessUnSycnRangeSolutionState(state)
    local bIsDefaultState = EGunsmithUIState.IsDefaultState(state)
    local bUnSycnRangeSolution = GunsmithUIContextLogic.GetUnSycnRangeSolution()
    if bIsDefaultState and bUnSycnRangeSolution then
        state = EGunsmithUIState.Simulate
    end
    return state
end

function GunsmithUIContextLogic.SetUIStateDefault()
    GunsmithUIContextLogic.SetUIState(EGunsmithUIState.Default)
end

function GunsmithUIContextLogic.SetUIStateSimulate()
    GunsmithUIContextLogic.SetUIState(EGunsmithUIState.Simulate)
end

function GunsmithUIContextLogic.SetUIStateRange()
    GunsmithUIContextLogic.SetUIState(EGunsmithUIState.Range)
end

function GunsmithUIContextLogic._InternalSetRangeState(uiParam)
    local bIsValid = isvalid(uiParam)
    local groupID = nil
    if bIsValid then
        groupID = uiParam:GetGroupID()
    end

    local bIsInRange = groupID == ESlotGroup.SafeHouseRange or groupID == ESlotGroup.SafeHouseRangeMP
    if bIsInRange then
        GunsmithUIContextLogic.SetUIState(EGunsmithUIState.Range, true)
    end
end

function GunsmithUIContextLogic.SetSimulateUIAnimState(animState)
    local uiContext = GunsmithUIContextLogic.GetUIContext()
    uiContext:SetSimulateUIAnimState(animState)
end

function GunsmithUIContextLogic.SetFastEquipResult(result)
    local uiContext = GunsmithUIContextLogic.GetUIContext()
    uiContext:SetFastEquipResult(result)
end

function GunsmithUIContextLogic.GetEnvironmentData()
    return GunsmithEnvironmentLogic.GetEnvironmentData()
end

function GunsmithUIContextLogic.GetWeaponAcator()
    local enviromentData = GunsmithUIContextLogic.GetEnvironmentData()
    return enviromentData:GetWeaponActor()
end

function GunsmithUIContextLogic.GetCameraActor()
    local enviromentData = GunsmithUIContextLogic.GetEnvironmentData()
    return enviromentData:GetCameraActor()
end

function GunsmithUIContextLogic.GetUIContext()
    local context = Module.Gunsmith.Field:GetUIContext()
    return context
end

function GunsmithUIContextLogic.GetFrontend()
    local context =  GunsmithUIContextLogic.GetUIContext()
    return context:GetFrontend()
end

function GunsmithUIContextLogic.GetBackup()
    local context =  GunsmithUIContextLogic.GetUIContext()
    return context:GetBackup()
end

function GunsmithUIContextLogic.GetItemID()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetItemID()
end

function GunsmithUIContextLogic.GetGUID()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetGUID()
end

function GunsmithUIContextLogic.GetGroupID()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetGroupID()
end

function GunsmithUIContextLogic.GetGroupIDIsMP(inGroupID)
    local groupID = inGroupID
    if inGroupID == nil then
        groupID = GunsmithUIContextLogic.GetGroupID()
    end
    local bIsMP = (groupID == ESlotGroup.MPApply) or (groupID == ESlotGroup.SafeHouseRangeMP)
    return bIsMP
end

function GunsmithUIContextLogic.GetIsFromUserPropInfo()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetIsFromUserPropInfo()
end

function GunsmithUIContextLogic.GetPropInfo()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetPropInfo()
end

function GunsmithUIContextLogic.GetWeaponDescription4Frontend()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetWeaponDescription4Frontend()
end

function GunsmithUIContextLogic.GetWeaponDescription4Backup()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetWeaponDescription4Backup()
end

function GunsmithUIContextLogic.GetWeaponDescription4BackupServer()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetWeaponDescription4BackupServer()
end

function GunsmithUIContextLogic.GetWeaponDescription4BackupLocal()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetWeaponDescription4BackupLocal()
end

function GunsmithUIContextLogic.GetWeaponDescription4Sync()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetWeaponDescription4Sync()
end

function GunsmithUIContextLogic.GetWeaponSocketSnapshot4FrontEnd()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetWeaponSocketSnapshot4FrontEnd()
end

function GunsmithUIContextLogic.GetWeaponSocketSnapshot4Backup()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetWeaponSocketSnapshot4Backup()
end

function GunsmithUIContextLogic.GetUIState()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetUIState()
end

function GunsmithUIContextLogic.GetIsSimulateState()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetIsSimulateState()
end

function GunsmithUIContextLogic.GetSimulateUIAnimState()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetSimulateUIAnimState()
end

function GunsmithUIContextLogic.GetFastEquipResult()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetFastEquipResult()
end

function GunsmithUIContextLogic.GetCameraType()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetCameraType()
end

function GunsmithUIContextLogic.GetCameraDefaultPoint()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetCameraDefaultPoint()
end

function GunsmithUIContextLogic.GetCameraFocusCenterPoint()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetCameraFocusCenterPoint()
end

function GunsmithUIContextLogic.GetCameraFocusCenterPointConfig()
    local point = GunsmithUIContextLogic.GetCameraFocusCenterPoint()
    local config = GunsmithEnvironmentCameraConfig.FocusCenter[point]
    return config
end

-- 改枪台任务接口 start---
-- 获取当前武器任务数据
function GunsmithUIContextLogic.UpdateMissionData()
    local weaponID = GunsmithUIContextLogic.GetItemID()
    local missionWeaponID = GunsmithUIContextLogic.GetMissionWeaponID()
    if missionWeaponID and weaponID and weaponID == missionWeaponID then
        return
    end
    local context = GunsmithUIContextLogic.GetUIContext()
    context:UpdateMissionData()
end

-- 显示武器数据更新之后刷新任务状态
function GunsmithUIContextLogic.UpdateMissionEquippedAndUnequippedPartList()
    local context = GunsmithUIContextLogic.GetUIContext()
    context:UpdateMissionEquippedAndUnequippedPartList()
end

function GunsmithUIContextLogic.GetMissionID()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetMissionID()
end

-- 重置改枪台武器数据
function GunsmithUIContextLogic.MissionDataReset()
    local context = GunsmithUIContextLogic.GetUIContext()
    context:MissionDataReset()
end

function GunsmithUIContextLogic.GetMissionData()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetMissionData()
end

function GunsmithUIContextLogic.GetMissionWeaponID()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetMissionWeaponID()
end

function GunsmithUIContextLogic.CanShowMission()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:CanShowMission()
end

function GunsmithUIContextLogic.GetMissionTitle()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetMissionTitle()
end

function GunsmithUIContextLogic.GetMissionDes()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetMissionDes()
end

function GunsmithUIContextLogic.GetMissionRawItemIDList()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetMissionRawItemIDList()
end

function GunsmithUIContextLogic.GetMissionRawObjective()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetMissionRawObjective()
end

function GunsmithUIContextLogic.GetMissionItemBase()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetMissionItemBase()
end

function GunsmithUIContextLogic.SetEquippedPartItemIDList(equippedPartItemIDList)
    local context = GunsmithUIContextLogic.GetUIContext()
    context:SetEquippedPartItemIDList(equippedPartItemIDList)
end

function GunsmithUIContextLogic.GetEquippedPartItemIDList()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetEquippedPartItemIDList()
end

function GunsmithUIContextLogic.SetUnequippedPartItemIDList(unequippedPartItemIDList)
    local context = GunsmithUIContextLogic.GetUIContext()
    context:SetUnequippedPartItemIDList(unequippedPartItemIDList)
end

function GunsmithUIContextLogic.GetUnequippedPartItemIDList()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetUnequippedPartItemIDList()
end

function GunsmithUIContextLogic.SetShowSceneSocketItemMissionUI(bShowSceneSocketItemMissionUI)
    local context = GunsmithUIContextLogic.GetUIContext()
    context:SetShowSceneSocketItemMissionUI(bShowSceneSocketItemMissionUI)
end

function GunsmithUIContextLogic.SetShowMainPreviewMissionUI(bShowMainPreviewMissionUI)
    local context = GunsmithUIContextLogic.GetUIContext()
    context:SetShowMainPreviewMissionUI(bShowMainPreviewMissionUI)
end

function GunsmithUIContextLogic.IsShowSceneSocketItemMissionUI()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:IsShowSceneSocketItemMissionUI()
end

function GunsmithUIContextLogic.IsShowMainPreviewMissionUI()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:IsShowMainPreviewMissionUI()
end
-- 改枪台任务接口 end---

function GunsmithUIContextLogic.SetWeaponItemGUIDFormUser(weaponGUID)
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:SetWeaponItemGUIDFormUser(weaponGUID)
end

function GunsmithUIContextLogic.GetWeaponItemGUIDFormUser()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetWeaponItemGUIDFormUser()
end

function GunsmithUIContextLogic.GetFocusSocket()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetFocusSocket()
end

function GunsmithUIContextLogic.GetFocusSocketItemID()
    local socketItemID = 0
    local socket = GunsmithUIContextLogic.GetFocusSocket()
    if socket == nil then
        return socketItemID
    end

    socketItemID = socket:GetItemID()
    return socketItemID
end

function GunsmithUIContextLogic.GetFocusSocketID()
    local socketID = 0
    local socket = GunsmithUIContextLogic.GetFocusSocket()
    if socket == nil then
        return socketID
    end

    socketID = socket:GetSocketID()
    return socketID
end

function GunsmithUIContextLogic.GetFocusSocketGUID()
    local socketGUID = G_GUNSMITH.SOCKET_GUID_INVALID
    local socket = GunsmithUIContextLogic.GetFocusSocket()
    if socket == nil then
        return socketGUID
    end

    socketGUID = socket:GetSocketGUID()
    return socketGUID
end

function GunsmithUIContextLogic.GetFocusSocketFlag()
    local socket = GunsmithUIContextLogic.GetFocusSocket()
    return GunsmithUIContextLogic.GetSocketFlagBySocket(socket)
end

function GunsmithUIContextLogic.GetLastFocusSocket()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetLastFocusSocket()
end

-- socketflag数据只在backup中
function GunsmithUIContextLogic.GetSocketFlagBySocket(socket)
    local flag = EGunsmithUIState.Default
    if isinvalid(socket) then
        return flag
    end
    local socketGUID = socket:GetSocketGUID()

    local backupSnapshot = GunsmithUIContextLogic.GetWeaponSocketSnapshot4Backup()
    local backupSocket = backupSnapshot:GetSocketFromGUID(socketGUID)
    if backupSocket ~= nil then
        flag = backupSocket:GetFlag()
    end
    return flag
end

function GunsmithUIContextLogic.GetUIID()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetUIID()
end

function GunsmithUIContextLogic.GetUILastID()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetUILastID()
end

function GunsmithUIContextLogic.GetOpreationSource()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetOpreationSource()
end

function GunsmithUIContextLogic.GetIsFromPushUI()
    local source = GunsmithUIContextLogic.GetOpreationSource()
    if source ~= EStackAction.Push then
        return false
    end
    return true
end

function GunsmithUIContextLogic.GetIsFromPopTunePartUI()
    local source = GunsmithUIContextLogic.GetOpreationSource()
    if source ~= EStackAction.Pop then
        return false
    end

    local lastID = GunsmithUIContextLogic.GetUILastID()
    if lastID == UIName2ID.GunsmithFinetunePartUI then
        return true
    end
    return false
end

function GunsmithUIContextLogic.GetIsSubStageChangeEnter()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetIsSubStageChangeEnter()
end

function GunsmithUIContextLogic.GetUIParam()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetUIParam()
end

-- 刷新uiParam
function GunsmithUIContextLogic.UpdateUIParam(uiParam)
    local uiContext = GunsmithUIContextLogic.GetUIContext()
    uiContext:SetUIParam(uiParam)
end

function GunsmithUIContextLogic.SetSOLWeaponSkinID(weaponID, skinID, skinGUID)
    local uiContext = GunsmithUIContextLogic.GetUIContext()
    uiContext:SetSOLWeaponSkinID(weaponID, skinID, skinGUID)
end

function GunsmithUIContextLogic.GetSOLGlobalSkinEquippedIDByWeaponID(weaponID)
    local solGlobalEquippedID = Server.InventoryServer:GetSOLWeaponSkinIDFromBaseWeaponID(weaponID)
    local bIsRange = GunsmithUIContextLogic.GetGroupIDIsRange()
    if bIsRange then
        local rangeSkinID = GunsmithUIContextLogic.GetSOLWeaponSkinIDFromBaseWeaponID(weaponID)
        if rangeSkinID ~= nil then
            solGlobalEquippedID = rangeSkinID
        end
    end
    return solGlobalEquippedID
end

function GunsmithUIContextLogic.GetSOLWeaponSkinIDFromBaseWeaponID(weaponID)
    local uiContext = GunsmithUIContextLogic.GetUIContext()
    return uiContext:GetSOLWeaponSkinIDFromBaseWeaponID(weaponID)
end

function GunsmithUIContextLogic.GetSOLGlobalSkinEquippedGUIDByWeaponID(weaponID)
    local solGlobalEquippedGUID = Server.InventoryServer:GetSOLWeaponSkinGUIDFromBaseWeaponID(weaponID)
    local bIsRange = GunsmithUIContextLogic.GetGroupIDIsRange()
    if bIsRange then
        local rangeSkinGUID = GunsmithUIContextLogic.GetSOLWeaponSkinGUIDFromBaseWeaponID(weaponID)
        if rangeSkinGUID ~= nil then
            solGlobalEquippedGUID = rangeSkinGUID
        end
    end
    return solGlobalEquippedGUID
end

function GunsmithUIContextLogic.GetSOLWeaponSkinGUIDFromBaseWeaponID(weaponID)
    local uiContext = GunsmithUIContextLogic.GetUIContext()
    return uiContext:GetSOLWeaponSkinGUIDFromBaseWeaponID(weaponID)
end

function GunsmithUIContextLogic.SetSOLWeaponPendantID(weaponID, pendantID, pendantGUID)
    local uiContext = GunsmithUIContextLogic.GetUIContext()
    uiContext:SetSOLWeaponPendantID(weaponID, pendantID, pendantGUID)
end

function GunsmithUIContextLogic.GetSOLGlobalPendantEquippedIDByWeaponID(weaponID)
    local solGlobalEquippedID = Server.InventoryServer:GetWeaponPendantIDFromBaseWeaponID(weaponID)
    local bIsRange = GunsmithUIContextLogic.GetGroupIDIsRange()
    if bIsRange then
        local rangePendantID = GunsmithUIContextLogic.GetSOLWeaponPendantIDFromBaseWeaponID(weaponID)
        if rangePendantID ~= nil then
            solGlobalEquippedID = rangePendantID
        end
    end
    return solGlobalEquippedID
end

function GunsmithUIContextLogic.GetSOLWeaponPendantIDFromBaseWeaponID(weaponID)
    local uiContext = GunsmithUIContextLogic.GetUIContext()
    return uiContext:GetSOLWeaponPendantIDFromBaseWeaponID(weaponID)
end

function GunsmithUIContextLogic.GetSOLGlobalPendantEquippedGUIDByWeaponID(weaponID)
    local solGlobalEquippedGUID = Server.InventoryServer:GetWeaponPendantGUIDFromBaseWeaponID(weaponID)
    local bIsRange = GunsmithUIContextLogic.GetGroupIDIsRange()
    if bIsRange then
        local rangePendantGUID = GunsmithUIContextLogic.GetSOLWeaponPendantGUIDFromBaseWeaponID(weaponID)
        if rangePendantGUID ~= nil then
            solGlobalEquippedGUID = rangePendantGUID
        end
    end
    return solGlobalEquippedGUID
end

function GunsmithUIContextLogic.GetSOLWeaponPendantGUIDFromBaseWeaponID(weaponID)
    local uiContext = GunsmithUIContextLogic.GetUIContext()
    return uiContext:GetSOLWeaponPendantGUIDFromBaseWeaponID(weaponID)
end

function GunsmithUIContextLogic.GetSlotType()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetSlotType()
end


function GunsmithUIContextLogic.GetRangeGUID()
    return Module.Gunsmith.Field:GetRangeGUID()
end

function GunsmithUIContextLogic.GetWeaponDescriptionSocketItemIDs(weaponDescription, socketGUID)
    return GPModularWeaponDescLogic.GetWeaponDescriptionSocketItemIDs(weaponDescription, socketGUID)
end

function GunsmithUIContextLogic.GetSocketFromSocketName(socketName)
    local snapshot = GunsmithUIContextLogic.GetWeaponSocketSnapshot4FrontEnd()
    local socket = snapshot:GetSocketFromSocketName(socketName)
    return socket
end

function GunsmithUIContextLogic.GetSkinIDFromFrontend()
    local weaponDescription = GunsmithUIContextLogic.GetWeaponDescription4Frontend()
    local skinID = GPModularWeaponDescLogic.GetSkinID(weaponDescription)
    return skinID
end

function GunsmithUIContextLogic.GetSkinIDFromBackup()
    local weaponDescription = GunsmithUIContextLogic.GetWeaponDescription4Backup()
    local skinID = GPModularWeaponDescLogic.GetSkinID(weaponDescription)
    return skinID
end

function GunsmithUIContextLogic.GetSkinGUIDFromBackup()
    local weaponDescription = GunsmithUIContextLogic.GetWeaponDescription4Backup()
    local skinGUID = GPModularWeaponDescLogic.GetSkinGUID(weaponDescription)
    return skinGUID
end

function GunsmithUIContextLogic.GetSkinIDFromBackupServer()
    local weaponDescription = GunsmithUIContextLogic.GetWeaponDescription4BackupServer()
    local skinID = GPModularWeaponDescLogic.GetSkinID(weaponDescription)
    return skinID
end

function GunsmithUIContextLogic.GetSkinGUIDFromBackupServer()
    local weaponDescription = GunsmithUIContextLogic.GetWeaponDescription4BackupServer()
    local skinGUID = GPModularWeaponDescLogic.GetSkinGUID(weaponDescription)
    return skinGUID
end

function GunsmithUIContextLogic.GetPendantIDFromFrontend()
    local weaponDescription = GunsmithUIContextLogic.GetWeaponDescription4Frontend()
    local pendantID = GPModularWeaponDescLogic.GetPendantID(weaponDescription)
    return pendantID
end

function GunsmithUIContextLogic.GetPendantIDFromBackup()
    local weaponDescription = GunsmithUIContextLogic.GetWeaponDescription4Backup()
    local pendantID = GPModularWeaponDescLogic.GetPendantID(weaponDescription)
    return pendantID
end

function GunsmithUIContextLogic.GetPendantIDFromBackupServer()
    local weaponDescription = GunsmithUIContextLogic.GetWeaponDescription4BackupServer()
    local pendantID = GPModularWeaponDescLogic.GetPendantID(weaponDescription)
    return pendantID
end

function GunsmithUIContextLogic.GetPendantGUIDFromBackupServer()
    local weaponDescription = GunsmithUIContextLogic.GetWeaponDescription4BackupServer()
    local pendantGUID = GPModularWeaponDescLogic.GetPendantGUID(weaponDescription)
    return pendantGUID
end

---@return FPbWeaponSkinInfoParam
function GunsmithUIContextLogic.GetPbWeaponSkinInfoParamFromBackup()
    local weaponDescription = GunsmithUIContextLogic.GetWeaponDescription4Backup()
    local skininfoParam = GunsmithUIContextLogic.GetPbWeaponSkinInfoParamFromWeaponDescription(weaponDescription)
    return skininfoParam
end

---@return FPbWeaponSkinInfoParam
function GunsmithUIContextLogic.GetPbWeaponSkinInfoParamFromBackupServer()
    local weaponDescription = GunsmithUIContextLogic.GetWeaponDescription4BackupServer()
    local skininfoParam = GunsmithUIContextLogic.GetPbWeaponSkinInfoParamFromWeaponDescription(weaponDescription)
    return skininfoParam
end

---@return FPbWeaponSkinInfoParam
function GunsmithUIContextLogic.GetPbWeaponSkinInfoParamFromWeaponDescription(weaponDescription)
    local skininfoParam = WeaponAssemblyTool.GetPbWeaponSkinInfoParamFromWeaponDescription(weaponDescription)
    return skininfoParam
end

---@return FPendantInfo
function GunsmithUIContextLogic.GetPendantInfoFromBackupServer()
    local weaponDescription = GunsmithUIContextLogic.GetWeaponDescription4BackupServer()
    local pendantInfo = GunsmithUIContextLogic.GetPendantInfoFromWeaponDescription(weaponDescription)
    return pendantInfo
end

---@return FPendantInfo
function GunsmithUIContextLogic.GetPendantInfoFromWeaponDescription(weaponDescription)
    local pendantInfo = WeaponAssemblyTool.GetPendantInfoFromWeaponDescription(weaponDescription)
    return pendantInfo
end

---@param skinInfoParam FPbWeaponSkinInfoParam
function GunsmithUIContextLogic.SetSkin4Frontend(skinInfoParam)
    local weaponDescription4Frontend = GunsmithUIContextLogic.GetWeaponDescription4Frontend()
    GPModularWeaponDescLogic.SetSkinFromPbWeaponSkinInfoParam(weaponDescription4Frontend, skinInfoParam)
end

---@param pendantInfo FPendantInfo
function GunsmithUIContextLogic.SetPendant4Frontend(pendantInfo)
    local weaponDescription4Frontend = GunsmithUIContextLogic.GetWeaponDescription4Frontend()
    GPModularWeaponDescLogic.SetSkinFromPendantInfo(weaponDescription4Frontend, pendantInfo)
end

function GunsmithUIContextLogic.FinetuneMainUICheckBoxStateReset()
    local context = GunsmithUIContextLogic.GetUIContext()
    context:FinetuneMainUICheckBoxStateReset()
end

function GunsmithUIContextLogic.SetFinetuneMainUICheckBoxState(finetuneMainUICheckBoxState)
    local context = GunsmithUIContextLogic.GetUIContext()
    context:SetFinetuneMainUICheckBoxState(finetuneMainUICheckBoxState)
end

function GunsmithUIContextLogic.GetFinetuneMainUICheckBoxState()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetFinetuneMainUICheckBoxState(uiID)
end

function GunsmithUIContextLogic.ItemBaseAlloctorAdd(itemID)
    return Module.Gunsmith.Field:ItemBaseAlloctorAdd(itemID)
end

function GunsmithUIContextLogic.SetCSWeaponGetRecommendSharingCodeRes(res)
    local context = GunsmithUIContextLogic.GetUIContext()
    context:SetCSWeaponGetRecommendSharingCodeRes(res)
end

function GunsmithUIContextLogic.GetKOLRecommend()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetKOLRecommend()
end

function GunsmithUIContextLogic.GetSystemRecommend()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetSystemRecommend()
end

function GunsmithUIContextLogic.SetPropinfoFromRange(propinfo)
    local context = GunsmithUIContextLogic.GetUIContext()
    context:SetPropinfoFromRange(propinfo)
end

function GunsmithUIContextLogic.GetPropinfoFromRange()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetPropinfoFromRange()
end

function GunsmithUIContextLogic.SetGroupIDFromRange(groupID)
    local context = GunsmithUIContextLogic.GetUIContext()
    context:SetGroupIDFromRange(groupID)
end

function GunsmithUIContextLogic.GetGroupIDFromRange()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetGroupIDFromRange()
end

function GunsmithUIContextLogic.SetUnSycnRangeSolution(bUnSycnRangeSolution)
    local context = GunsmithUIContextLogic.GetUIContext()
    context:SetUnSycnRangeSolution(bUnSycnRangeSolution)
end

function GunsmithUIContextLogic.GetUnSycnRangeSolution()
    local context = GunsmithUIContextLogic.GetUIContext()
    return context:GetUnSycnRangeSolution()
end

function GunsmithUIContextLogic.GetIsRangetoGunsmithBytPropInfo()
    local propinfoFromRange = GunsmithUIContextLogic.GetPropinfoFromRange()

    local bIsValid = isvalid(propinfoFromRange)
    if not bIsValid then
        return false
    end

    return true
end

function GunsmithUIContextLogic.GetReloadUIParamFromRange()
    local uiParam = nil
    local fCallback = function()
        return uiParam
    end

    local propinfoFromRange = GunsmithUIContextLogic.GetPropinfoFromRange()
    local bIsValid = isvalid(propinfoFromRange)
    if not bIsValid then
        return fCallback()
    end

    local groupID = GunsmithUIContextLogic.GetGroupIDFromRange()
    local bIsEqual, propinfo = GunsmithUIContextLogic.IsEqualServerPropinfo(propinfoFromRange, groupID)
    uiParam = GunsmithUIParam.SetFromPropInfoForRange(propinfo, groupID)

    GunsmithUIContextLogic.SetUnSycnRangeSolution(not bIsEqual)
    -- 如果仓库数据和需要展示一致,直接返回
    if bIsEqual then
        return fCallback()
    end

    GunsmithUIContextLogic.OnForceOverrideContext(uiParam, propinfoFromRange)
    return fCallback()
end

function GunsmithUIContextLogic.ResetRangetoGunsmith()
    GunsmithUIContextLogic.SetPropinfoFromRange(nil)
    GunsmithUIContextLogic.SetGroupIDFromRange(nil)
end

function GunsmithUIContextLogic.GetReloadUIParam()
    return GunsmithUIContextLogic.GetReloadUIParamFromRange()
end


-- return GunsmithUIContextLogic