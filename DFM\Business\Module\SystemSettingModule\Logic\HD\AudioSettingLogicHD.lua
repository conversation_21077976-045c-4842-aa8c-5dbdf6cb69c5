----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------



local AudioSettingLogicHD = {}
local SettingRegLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.SettingRegLogicHD"
local CommonSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.CommonSettingLogicHD"
local _audioSetting = import("ClientAudioSettingHD").Get()
local UGPAudioStatics = import("GPAudioStatics")
local UDFMAudioMastering = import("DFMAudioMastering")
local ELODLevel = import "ELODLevel"
local VolumeSettingLogic = require "DFM.Business.Module.SystemSettingModule.Logic.VolumeSettingLogic"

local function _VolumeApplyFunc(settingObj, key, value, rtpc)
    value = math.max(0, value)
    UGPAudioStatics.SetGlobalRTPCByName(rtpc, value, 0)
    if key == "Volume_Music" then
        Module.SystemSetting.Field:SetVolumeMusic(value)
    end
end

local function _RegVolumeBatch(id, key, rtpc)
    local settingObj = _audioSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _VolumeApplyFunc, {rtpc})
end

local function _DynamicRangeApplyFunc(settingObj, key, value)
    local EAudioDynamicRangeHD = Module.SystemSetting.Config.EAudioDynamicRangeHD
    if value == EAudioDynamicRangeHD.HomeCinema then
        UDFMAudioMastering.SetPCHomeCinema()
    elseif value == EAudioDynamicRangeHD.Headphones then
        UDFMAudioMastering.SetPCHeadphones()
    elseif value == EAudioDynamicRangeHD.NightMode then
        UDFMAudioMastering.SetPCNightMode()
    end
end

local function _RegDynamicRange(id, key)
    local settingObj = _audioSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _DynamicRangeApplyFunc)
end

local function _MircrophoneVolumeApplyFunc(settingObj, key, value)
    if Facade.ModuleManager:IsModuleValid("GVoice") then
        value = math.max(0, value)
        Module.GVoice:SetMicrophoneVolume(math.ceil(value))
    end
end

local function _RegMicrophoneVolumeBatch(id, key)
    local settingObj = _audioSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _MircrophoneVolumeApplyFunc)
end

local function _ConvertButtonType(MicrophoneButtonType, SpeakingChanel)
    local btnType = EGVoiceButtonType.Close
    if SpeakingChanel == ESpeakingChanel.Team then
        if MicrophoneButtonType == EMicrophoneButtonType.Press then
            btnType = EGVoiceButtonType.TeamPress
        elseif MicrophoneButtonType == EMicrophoneButtonType.Always then
            btnType = EGVoiceButtonType.Team
        end
    elseif SpeakingChanel == ESpeakingChanel.All then
        if MicrophoneButtonType == EMicrophoneButtonType.Press then
            btnType = EGVoiceButtonType.AllPress
        elseif MicrophoneButtonType == EMicrophoneButtonType.Always then
            btnType = EGVoiceButtonType.All
        end
    elseif SpeakingChanel == ESpeakingChanel.Camp then
        if MicrophoneButtonType == EMicrophoneButtonType.Press then
            btnType = EGVoiceButtonType.CampPress
        elseif MicrophoneButtonType == EMicrophoneButtonType.Always then
            btnType = EGVoiceButtonType.Camp
        end
    end
    return btnType
end

local function _MicrophoneButtonTypApplyFunc(settingObj, key, value)
    if Facade.ModuleManager:IsModuleValid("GVoice") then
        local MicrophoneButtonType = value
        local SpeakingChanel = CommonSettingLogicHD.GetDataByID("SpeakingChanel")
        local btnType = _ConvertButtonType(MicrophoneButtonType, SpeakingChanel)
        Module.GVoice:SetMicrophoneButtonType(btnType, true)
    end
end

local function _SpeakingChanelApplyFunc(settingObj, key, value)
    if Facade.ModuleManager:IsModuleValid("GVoice") then
        local MicrophoneButtonType = CommonSettingLogicHD.GetDataByID("MicrophoneButtonType")
        local SpeakingChanel = value
        local btnType = _ConvertButtonType(MicrophoneButtonType, SpeakingChanel)
        Module.GVoice:SetMicrophoneButtonType(btnType, true)
    end
end

local function _ListeningChanelApplyFunc(settingObj, key, value)
    if Facade.ModuleManager:IsModuleValid("GVoice") then
        local btnType = EGVoiceButtonType.Close
        if value == EListenChanel.All then
            btnType = EGVoiceButtonType.All
        end
        if value == EListenChanel.Camp then
            btnType = EGVoiceButtonType.Camp
        end
        Module.GVoice:SetSpeakerButtonType(btnType, true)
    end
end


local function _RegMicrophoneButtonTypeBatch(id, key)
    local settingObj = _audioSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _MicrophoneButtonTypApplyFunc)
end

local function _RegSpeakingChanelBatch(id, key)
    local settingObj = _audioSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _SpeakingChanelApplyFunc)
end

local function _RegListeningChanelBatch(id, key)
    local settingObj = _audioSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _ListeningChanelApplyFunc)
end


local function _AudioLevelMobileFunc(settingMobileObj, value)
    local level = ELODLevel.Low
    if value == 0 then
        level = ELODLevel.Low
    elseif value == 1 then
        level = ELODLevel.High
    elseif value == 2 then
        level = ELODLevel.Superhigh
    end
    VolumeSettingLogic.ProcessLODLevel(level)
end

local function _RegAudioLevelBatch(id, key)
    local settingObj = _audioSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegMobileMapping(id, nil, _AudioLevelMobileFunc)
end

local function _SpeakerApplyFunc(settingObj, key, value, rtpc)
    value = math.max(0, value)
    UGPAudioStatics.SetGlobalRTPCByName(rtpc, value, 0)
    if Facade.ModuleManager:IsModuleValid("GVoice") then
        Module.GVoice:SetSpeakerVolume(math.floor(value))
    end
end

local function _RegSpeakerBatch(id, key, rtpc)
    local settingObj = _audioSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _SpeakerApplyFunc, {rtpc})
end

local function _3DAudioTypeApplyFunc(settingObj, key, value)
    -- if value == 0 then
    --     UGPAudioStatics.SetHRTFPluginEnable(false)
    -- else
    --     UGPAudioStatics.SetHRTFPluginEnable(true)
    -- end
    UGPAudioStatics.SetHRTFPluginMode(value)
end

local function _Reg3DAudioTypeBatch(id, key)
    local settingObj = _audioSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _3DAudioTypeApplyFunc)
end

local function _ChangeWarTapeModeApplyFunc(settingObj, key, value) --音频战争纪实模式回调
    UGPAudioStatics.OpenWarTapeMode(value)
end

local function _ChangeMultiOutputApplyFunc(settingObj, key, value) --多声道回调
    UGPAudioStatics.Set714OutPut(value)
end

function AudioSettingLogicHD.GetAudioDeviceOptions(id, deviceType)
    local options = {}
    local index = 0

    local lastDeviceID = CommonSettingLogicHD.GetDataByID(id)

    local count = Module.GVoice:GetDeviceCount(deviceType)
    for i = 1, count do
        local deviceId, deviceName = Module.GVoice:DescribleDevice(deviceType, i - 1)
        table.insert(options, deviceName)
        if deviceId == lastDeviceID then
            index = i - 1
        end
    end

    if count == 0 then
        options = CommonSettingLogicHD.GetDropDownOptionsByID(id)
    end

    return options, index
end

function AudioSettingLogicHD.IsValidAudioDeviceId(deviceType, deviceId)
    local bValid = false
    local count = Module.GVoice:GetDeviceCount(deviceType)
    for i = 1, count do
        local tmpDeviceId, _ = Module.GVoice:DescribleDevice(deviceType, i - 1)
        if tmpDeviceId == deviceId then
            bValid = true
            break
        end
    end
    return bValid
end

local function _AudioDeviceRsetter(settingObj, key, value)
    settingObj[key] = ""
end

local function _AudioDeviceGetter(settingObj, key)
    return settingObj[key]
end

local function _AudioDeviceSetter(settingObj, key, value, deviceType)
    local deviceId = value
    settingObj[key] = deviceId
end

local function _AudioDeviceApplyFunc(settingObj, key, value, deviceType)
    if Facade.ModuleManager:IsModuleValid("GVoice") then
        local deviceId = value
        if deviceId == "" then
            local count = Module.GVoice:GetDeviceCount(deviceType)
            if count > 0 then
                deviceId = Module.GVoice:DescribleDevice(deviceType, 0)
            end
        end
        if deviceId ~= "" then
            Module.GVoice:SelectDevice(deviceType, deviceId)
        end
    end

    if deviceType == 0 and value ~= "" then
        AudioSettingLogicHD.SetOutputDevice(value)
    end
end

function AudioSettingLogicHD._RegAudioInputDeviceBatch(id, key)
    local settingObj = _audioSetting
    SettingRegLogicHD.RegMapping(
        id,
        settingObj,
        key,
        _AudioDeviceGetter,
        _AudioDeviceSetter,
        _AudioDeviceApplyFunc,
        {1}
    )
    SettingRegLogicHD.RegResetMapping(id, settingObj, key, _AudioDeviceRsetter)
end

local function _RegAudioOutputDeviceBatch(id, key)
    local settingObj = _audioSetting
    SettingRegLogicHD.RegMapping(
        id,
        settingObj,
        key,
        _AudioDeviceGetter,
        _AudioDeviceSetter,
        _AudioDeviceApplyFunc,
        {0}
    )
    SettingRegLogicHD.RegResetMapping(id, settingObj, key, _AudioDeviceRsetter)
end


local function _WarTapModeeSetter(settingObj, key, value, deviceType)
    local deviceId = value
    settingObj[key] = deviceId
end

function AudioSettingLogicHD. _RegWarTapMode(id, key) ---- 音频战争纪实模式
    local settingObj = _audioSetting
    SettingRegLogicHD.RegMapping(
        id,
        settingObj,
        key,
        function (settingObj, key)
            return settingObj[key]
        end,

        function (settingObj, key, value, deviceType)
            -- local function fOnconfirmCallback()
            --     local bOpen = value
            --     settingObj[key] = bOpen
            -- end
            -- Module.CommonTips:ShowConfirmWindow("",fOnconfirmCallback)
            local bOpen = value
            settingObj[key] = bOpen
        end,
        _ChangeWarTapeModeApplyFunc
    )

end

function AudioSettingLogicHD. _RegMultiOutput(id, key) ---- 多声道
    local settingObj = _audioSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _ChangeMultiOutputApplyFunc)
end

-- 废弃
function AudioSettingLogicHD.ApplySettingData()
    -- UGPAudioStatics.SetGlobalRTPCByName("Volume_All", math.max(0, _audioSetting.VolumeAll), 0)
    -- UGPAudioStatics.SetGlobalRTPCByName("Volume_Music",clientVolumeSetting.VolumeMusic,0);
    -- UGPAudioStatics.SetGlobalRTPCByName("Volume_UI",clientVolumeSetting.VolumeUI,0);
    -- UGPAudioStatics.SetGlobalRTPCByName("Volume_VO",clientVolumeSetting.VolumeVO,0);
    -- UGPAudioStatics.SetGlobalRTPCByName("Volume_GVoice",clientVolumeSetting.EarpieceVolume,0);
end

function AudioSettingLogicHD.ApplyGVoiceSettings()
    -- 先检查，可能之前选择的音频设备已发生了变化
    AudioSettingLogicHD.VerifySettings()

    -- 监听音频设变变更
    Module.GVoice.Config.Events.evtOnGVoiceGetDeviceCount:AddListener(AudioSettingLogicHD._OnDeviceCountChanged)

    local Volume_Microphone = CommonSettingLogicHD.GetDataByID("Volume_Microphone")
    _MircrophoneVolumeApplyFunc(nil, nil, Volume_Microphone)

    local MicrophoneButtonType = CommonSettingLogicHD.GetDataByID("MicrophoneButtonType")
    _MicrophoneButtonTypApplyFunc(nil, nil, MicrophoneButtonType)

    -- local Volume_GVoice = CommonSettingLogicHD.GetDataByID("Volume_GVoice")
    -- _SpeakerApplyFunc(nil, nil, Volume_GVoice, "Volume_GVoice")

     local AudioOutputDevice = CommonSettingLogicHD.GetDataByID("AudioOutputDevice")
     _AudioDeviceApplyFunc(_audioSetting, "AudioOutputDevice", AudioOutputDevice, 0)

    local AudioInputDevice = CommonSettingLogicHD.GetDataByID("AudioInputDevice")
    _AudioDeviceApplyFunc(_audioSetting, "AudioInputDevice", AudioInputDevice, 1)


    local SpeakingChanel = CommonSettingLogicHD.GetDataByID("SpeakingChanel")
    _SpeakingChanelApplyFunc(_audioSetting, "SpeakingChanel", SpeakingChanel)


    local ListeningChanel = CommonSettingLogicHD.GetDataByID("ListeningChanel")
    _ListeningChanelApplyFunc(_audioSetting, "ListeningChanel", ListeningChanel)

end

function AudioSettingLogicHD.VerifySettings()
    local AudioOutputDevice = CommonSettingLogicHD.GetDataByID("AudioOutputDevice")
    if not AudioSettingLogicHD.IsValidAudioDeviceId(0, AudioOutputDevice) then
        CommonSettingLogicHD.SetDataByID("AudioOutputDevice", "")
    end

    local AudioInputDevice = CommonSettingLogicHD.GetDataByID("AudioInputDevice")
    if not AudioSettingLogicHD.IsValidAudioDeviceId(1, AudioInputDevice) then
        CommonSettingLogicHD.SetDataByID("AudioInputDevice", "")
    end

    SettingRegLogicHD.FlushPendingSaveObjs()
end

function AudioSettingLogicHD._OnDeviceCountChanged(deviceType, deviceCount)
    local audioDeviceType = nil
    if deviceType == 0 then
        audioDeviceType = "AudioOutputDevice"
    elseif deviceType == 1 then
        audioDeviceType = "AudioInputDevice"
    end
    if audioDeviceType then
        --VerifySettings
        local AudioDevice = CommonSettingLogicHD.GetDataByID(audioDeviceType)
        if not AudioSettingLogicHD.IsValidAudioDeviceId(deviceType, AudioDevice) then
            CommonSettingLogicHD.SetDataByID(audioDeviceType, "")
        end
        SettingRegLogicHD.FlushPendingSaveObjs()

        -- 刷新UI
        local curItems = Module.SystemSetting.Field:GetAllSettingItemHD()
        if curItems then
            local item = curItems[audioDeviceType]
            if not hasdestroy(item) then
                item:ReloadSetting()
            end
        end
    end
end

function AudioSettingLogicHD.Init()
    -- CommonSettingLogicHD.SetDataByID("SpeakingChanel", 1)
    -- CommonSettingLogicHD.SetDataByID("ListeningChanel", 0)
end

function AudioSettingLogicHD.Uninit()
    if Facade.ModuleManager:IsModuleValid("GVoice") then
        Module.GVoice.Config.Events.evtOnGVoiceGetDeviceCount:RemoveListener(AudioSettingLogicHD._OnDeviceCountChanged)
    end
end

function AudioSettingLogicHD.MuteCopyrightMusic(settingObj, key, value)
    if settingObj[key] then
        UGPAudioStatics.SetGlobalRTPCByName("Music_Copyright_Mute",1,0)
    else
        UGPAudioStatics.SetGlobalRTPCByName("Music_Copyright_Mute",0,0)
    end
end

function AudioSettingLogicHD.Register()
    SettingRegLogicHD.BeginRegSection(_audioSetting)
    _RegVolumeBatch("Volume_All", "VolumeAll", "Volume_All")
    -- 背景音乐
    _RegVolumeBatch("Volume_Music", "Volume_Music", "Volume_Music")
    -- 战场音效
    _RegVolumeBatch("Volume_SFX", "Volume_SFX", "Volume_SFX")
    -- 按键音效
    _RegVolumeBatch("Volume_UI", "Volume_UI", "Volume_UI")
    ---语音播报
    _RegVolumeBatch("Volume_VO", "Volume_VO", "Volume_VO")
    ---听筒音量
    -- _RegSpeakerBatch("Volume_GVoice", "Volume_GVoice", "Volume_GVoice")
    ---标记音量 已合并到Volume_UI
    -- _RegVolumeBatch("Volume_Mark", "Volume_Mark", "Volume_Mark")
    ---麦克风音量
    _RegMicrophoneVolumeBatch("Volume_Microphone", "Volume_Microphone", "Volume_Microphone")

    _RegDynamicRange("AudioDynamicRange", "AudioDynamicRange")

    _RegMicrophoneButtonTypeBatch("MicrophoneButtonType", "MicrophoneButtonType")

    _RegSpeakingChanelBatch("SpeakingChanel", "SpeakingChanel")

    _RegListeningChanelBatch("ListeningChanel", "ListeningChanel")

    SettingRegLogicHD.RegPassThroughMappingWithFunc("bCopyrightMusicPlayback", "bCopyrightMusicPlayback",AudioSettingLogicHD.MuteCopyrightMusic)


    SettingRegLogicHD.RegPrerequisite("MicrophoneButtonType","SpeakingChanel",function(value)
        return value ~= ESpeakingChanel.Close
    end)

    SettingRegLogicHD.RegPrerequisite("AudioInputDevice","SpeakingChanel",function(value)
        return value ~= ESpeakingChanel.Close
    end)

    SettingRegLogicHD.RegPrerequisite("Volume_Microphone","SpeakingChanel",function(value)
        return value ~= ESpeakingChanel.Close
    end)


    -- _RegAudioLevelBatch("AudioLevel", "AudioLevel")

    _Reg3DAudioTypeBatch("ThreeDAudioType", "ThreeDAudioType")

    AudioSettingLogicHD._RegAudioInputDeviceBatch("AudioInputDevice", "AudioInputDevice")
    _RegAudioOutputDeviceBatch("AudioOutputDevice", "AudioOutputDevice")

    SettingRegLogicHD.RegPrerequisite(
        "AudioInputDevice",
        nil,
        function()
            if Facade.ModuleManager:IsModuleValid("GVoice") then
                return Module.GVoice:GetDeviceCount(1) > 0
            else
                return false
            end
        end
    )

    SettingRegLogicHD.RegPrerequisite(
        "AudioOutputDevice",
        nil,
        function()
            if Facade.ModuleManager:IsModuleValid("GVoice") then
                return Module.GVoice:GetDeviceCount(0) > 0
            else
                return false
            end
        end
    )

    -- 最小化时静音
    SettingRegLogicHD.RegPassThroughMapping("bMuteBackground", "bMuteBackground")

    -- 多声道输出
    AudioSettingLogicHD._RegMultiOutput("bMultiOutput", "bMultiOutput")

    -- 音频战争纪实模式（全面战场)
    AudioSettingLogicHD._RegWarTapMode("bOpenWarTapeMode", "bOpenWarTapeMode")

    SettingRegLogicHD.EndRegSection()

--- 获取系统音频设备列表
function AudioSettingLogicHD.GetAudioDeviceList()
    local deviceList = UGPAudioStatics.GetSystemDeviceList()
    return deviceList
end

--- 获取当前音频设备ID
function AudioSettingLogicHD.GetCurAudioDeviceID()
    local curDeviceID = UGPAudioStatics.GetCurUsingSystemDeviceID()
    return curDeviceID
end

--- 修改选中的音频设备(返回是否成功)
function AudioSettingLogicHD.SetOutputDevice(curDeviceID)
    local WWiseDeiveceID = UGPAudioStatics.FindMatchDeviceIDByDirectSoundGUID(curDeviceID)
    --1.validate
    if WWiseDeiveceID ~= 0 then
        UGPAudioStatics.ReplaceOutputDevice(WWiseDeiveceID)
    end
end

--- 将string类型的deviceid转换成uint32
function AudioSettingLogicHD.IDStrToInt(curDeviceID)
   return UGPAudioStatics.GetIDFromString(curDeviceID)
end



end

return AudioSettingLogicHD
