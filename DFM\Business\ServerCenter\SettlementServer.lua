----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaSSettlement)
----- LOG FUNCTION AUTO GENERATE END -----------


local SettlementServer = class("SettlementServer", require("DFM.YxFramework.Managers.Server.ServerBase"))
local json = require("DFM.YxFramework.Plugin.Json.Json").createJson()
local UDFMSettlementManager = import "DFMSettlementManager"
local settlementMgrIns = UDFMSettlementManager.Get(GetWorld())
local ULuaExtension = import("LuaExtension")
local ABreakthroughGPSubsystem = import "BreakthroughGPSubsystem"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local SettlementDefine = require "DFM.Business.DataStruct.SettlementStruct.SettlementDefine"
--- BEGIN MODIFICATION @ VIRTUOS: 实现Competitive Activity
local FPlatformMemberInfo = import "PlatformMemberInfo"
local FPlatformMatchInfo = import "PlatformMatchInfo"
local UDFMPlatformActivityManager = import "DFMPlatformActivityManager"
local EOutStandingContributionType = import "EOutStandingContributionType"
--- END MODIFICATION

local UHighlightMomentMgr = import "HighlightMomentMgr"
local FHighlightMomentSOLSettlementInfo = import "HighlightMomentSOLSettlementInfo"
local FHighlightMomentMPSettlementInfo = import "HighlightMomentMPSettlementInfo"
local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"
local EEventType = import "EEventType"
local function printtable(t)
    --logtable(t)
end

local function xxwwinfo(...)
    loginfo("[xxww] ", ...)
end

local function xxwwwarning(...)
    logwarning("[xxww] @warning@", ...)
end

local function xxwwerror(...)
    logerror("[xxww] @error@", ...)
end

-- 依赖InventoryServer/Server.RoomServer/Server.AccountServer
function SettlementServer:Ctor()
    xxwwinfo("SettlementServer:Ctor")
    --self:CleanUpSettlementData()
    self.bGetNtf = false
    self.bAlreadySettlement = false

    -- azhengzheng:Raid模式团队信息
    self._raidSettlementTeamInfoList = {}

    -- azhengzheng:结算信息来源
    self._settlementInfoSource = SettlementDefine.ESettlementInfoSource.None
end

function SettlementServer:OnInitServer()
    Facade.ProtoManager:AddNtfListener("CSSettlementEmptyNtf", self.OnReceiveSettlementNtf, self)
    Facade.ProtoManager:AddNtfListener("CSGatewayKickPlayerNtf", self.OnReceivePlayerBanNtf, self)
    Facade.ProtoManager:AddNtfListener("CSPlayerInfoBePraisedNtf", self.OnPlayerInfoBePraisedNtf, self)
    Facade.ProtoManager:AddNtfListener("CSAchieveProgressUpdatedNtf", self.OnAchieveProgressUpdatedNtf, self)

    Facade.ProtoManager:AddNtfListener("CSPlayerInfoCommanderBePraisedNtf", self.OnPlayerInfoCommanderBePraisedNtf, self)

    -- 断线重连
    self:AddLuaEvent(Facade.ProtoManager.Events.evtOnRelayConnected, self._OnRelayConnected, self)
    self.Events = {
        evtOnReqForSettlementInfoFailed = LuaEvent:NewIns("FOnReqForSettlementInfoFailed"),
        evtCameraMovementGetReadyBroadCast = LuaEvent:NewIns("evtCameraMovementGetReadyBroadCast"),
        evtUIGetReadyBroadCast = LuaEvent:NewIns("evtUIGetReadyBroadCast"),
        evtCutSceneUIGetReadyBroadCast = LuaEvent:NewIns("evtCutSceneUIGetReadyBroadCast"),
        evtStartCameraMovementBroadCast = LuaEvent:NewIns("evtStartCameraMovementBroadCast"),
        evtEndCameraMovementBroadCast = LuaEvent:NewIns("evtEndCameraMovementBroadCast"),
        evtSOLSettlementDo = LuaEvent:NewIns("evtSOLSettlementDo"),
        evtTDMSettlementDo = LuaEvent:NewIns("evtTDMSettlementDo"),
        evtRaidSettlementDo = LuaEvent:NewIns("evtRaidSettlementDo"),
        evtExceptionSettlementDo = LuaEvent:NewIns("evtExceptionSettlementDo"),
        evtNoExceptionSettlement = LuaEvent:NewIns("evtNoExceptionSettlement"),
        evtInventoryCollectionFetchFinished = LuaEvent:NewIns("InventoryServer.evtInventoryCollectionFetchFinished"),
        evtArenaSettlementDo = LuaEvent:NewIns("evtArenaSettlementDo"),
        evtReceiveEmptyNtf = LuaEvent:NewIns("evtReceiveEmptyNtf"),
        evtAchieveProgressUpdatedNtf = LuaEvent:NewIns("evtAchieveProgressUpdatedNtf"),
        evtPlayerInfoBePraisedNtf = LuaEvent:NewIns("evtPlayerInfoBePraisedNtf"),
        evtPlayerInfoPraisedClick = LuaEvent:NewIns("evtPlayerInfoPraisedClick"),
    }
end

function SettlementServer:OnDestroyServer()
    Facade.ProtoManager:RemoveAllNtfListenerByCaller(self)
end

function SettlementServer:Reset()
    xxwwinfo("SettlementServer:Reset")
    ---@type pb_CSSettlementGetNewSettlementInfoRes
    self.settlementInfo = {}
end

function SettlementServer:CleanUpSettlementData()
    xxwwinfo("SettlementServer:CleanUpSettlementData")
    self.settlementMode = EnumSettlementMode.MultiNotEscape
    self.settlementInfo = {}
    ---@type pb_GspPlayerGameStatus
    self.playerInfo = {}
    self.matchInfo = {}
    self.teamInfo = {}
    self.teammateArray = {}
    self.selectTeamPlayerId = nil -- used for SettlementTeamItemView -- TODO: 不该放在Server中
    self.bTeammateItem = false --带出物品中是否有队友绑定的物品
    self.successEscapeIndex = 1 --默认是1
    self.escapePointEnum = -1
    self.durabilityGidMap = {}
    self.myCarryOutProps = {}
    self.myCarryOutItems = {}
    self.notMyItemsProps = {}
    self.myVirtualItemsProps = {}
    self.killMap = {}
    self.bIsGuide = false
    self.MandelBricksID = 0
    self.settlementTimeStamp = -1
    self.bGetNtf = false
    --配装经分埋点数据重置
    LogAnalysisTool.ResetPlayerOutFitLogData()
    ---------------------------------------------TDM数据---------------------------------------
    ---@type pb_TDMSettlementData
    self.tdmData = {}
    ---@type pb_DsMatchInfoNew
    self.tdmMatchInfo = {}
    ---@type pb_NumeralActivityData
    self.tdmActivityData = {}
    --记录队伍信息
    self.tdmPlayerTeamInfo = {}
    --所有玩家信息
    self.tdmPlayerInfo = {}
    self.showOrder = {}
    self.mvp2ShowOrder = {}
    ---@type pb_CSAchievementTask[]
    self.achiveInfo = {}
    ---------------------------------------------Raid数据---------------------------------------
    self.raidData = {}
    self.raidSettlementNtf = {}
    self.raidPlayerInfo = {}
end

-- note: 收到服务器SOL结算完成的通知包 立刻向服务器要结算包
function SettlementServer:ReqForSOLSettlementInfo(roomId)
    self:SetSOLEliminationReplayStartTime(TimeUtil.GetCurrentTime() + 60)

    self:CleanUpSettlementData()
    xxwwinfo("SettlementServer:ReqForSOLSettlementInfo")
    local req = pb.CSSettlementGetNewSettlementInfoReq:New()
    if roomId then
        req.room_id = roomId
    else
        req.room_id = Server.MatchServer:GetDsRoomId()
    end
    req:Request(function(res)
        local bToIgnore = false
        local currentGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
        if currentGameFlow ~= EGameFlowStageType.Game
                and currentGameFlow ~= EGameFlowStageType.GameSettlement
                and currentGameFlow ~= EGameFlowStageType.GameToLobby then
            bToIgnore = true
        end
        -- logtable(res, true)ADFObserverPlayerControllerADFObserverPlayerController
        self:Save2File(res)
        if res.result ~= 0 then
            xxwwerror("SettlementServer:ReqForSOLSettlementInfo error res.result ==", res.result)
            if bToIgnore then
                xxwwinfo("SettlementServer:ReqForSOLSettlementInfo ignore res because CurrentGameFlow =", __DebugOnly_GameFlowName(currentGameFlow))
                return
            end
            self.Events.evtOnReqForSettlementInfoFailed:Invoke(ESettlementNtf.SNTF_SOL)
        else
            self._isNewSOLSettlement = true
            self._settlementInfoSource = SettlementDefine.ESettlementInfoSource.SOL
            if self:ParseSettlementInfo(res) then
                --高光时刻上报
                self:SetHighlightMomentSettlemtnInfo(res)
                if bToIgnore then
                    xxwwinfo("SettlementServer:ReqForSOLSettlementInfo ignore res because CurrentGameFlow =", __DebugOnly_GameFlowName(currentGameFlow))
                    return
                end
                self.Events.evtSOLSettlementDo:Invoke()
            end
        end
    end, { bNeedResendAfterReconnected = true })
end

function SettlementServer:CheckIsNewSOLSettlement()
    return self._isNewSOLSettlement
end

function SettlementServer:CheckIsGuide()
    return self.settlementInfo and self.settlementInfo.match_info and self.settlementInfo.match_info.is_guide
end

function SettlementServer:RecoverSOLSettlement()
    self._isNewSOLSettlement = nil
end

--监听断线重连成功后需要主动请求一次结算包 [xxww] todo
function SettlementServer:_OnRelayConnected()
    xxwwinfo("SettlementServer:_OnRelayConnected")
end

-- note: 解析结算包
---@param Ntf pb_CSSettlementGetNewSettlementInfoRes
function SettlementServer:ParseSettlementInfo(Ntf)

    xxwwinfo("SettlementServer:ParseSettlementInfo")
    Server.MatchServer:EndMatching()
    self.settlementInfo = Ntf
    self.playerInfo = Ntf.player
    self.matchInfo = Ntf.match_info
    self.teamInfo = Ntf.team_info
    self.teammateArray = Ntf.teammate_array
    self.settlementTimeStamp = os.time()
    local seasonInfo = {}
    local carryOutProps = {}
    if not self.matchInfo then
        xxwwerror("SettlementServer:ParseSettlementInfo self.matchInfo is nil!")
        return false
    end
    xxwwinfo(
            string.format("SettlementServer:ParseSettlementInfo success , new dsRoomId:%s  cache dsRoomId:%s",
                    self.matchInfo.room_id,
                    Server.MatchServer:GetDsRoomId())
    )
    local endGameType = self:GetEndGameType()
    if endGameType == EGspPlayerGameStatusType.kGspPlayerGameStatusTypeQuitGame then
        seasonInfo = self.playerInfo.quit_game.season_change_info
        carryOutProps = self.playerInfo.quit_game.carry_out_props
    elseif endGameType == EGspPlayerGameStatusType.kGspPlayerGameStatusTypeEndGame then
        seasonInfo = self.playerInfo.end_game.season_change_info
        carryOutProps = self.playerInfo.end_game.carry_out_props
        self.successEscapeIndex = self.playerInfo.end_game.success_escape_index
        xxwwinfo("self.playerInfo.end_game.success_escape_index ", self.playerInfo.end_game.success_escape_index)
        self.escapePointEnum = self.playerInfo.end_game.extraction_point
        self:_CalculateKillList()
        self.durabilityGidMap = self.playerInfo.end_game.carry_out_health_list
    else
        seasonInfo = self.playerInfo.quit_game.season_change_info or self.playerInfo.end_game.season_change_info
    end
    if seasonInfo.old_level == 0 or seasonInfo.level == 0 then
        seasonInfo.old_level = 1
        seasonInfo.level = 1
    end
    if self.matchInfo.match_type == 5 then
        self.bIsGuide = true
    end
    self.MandelBricksID = self.playerInfo.end_game.blue_print_special_id
    xxwwinfo("SettlementServer:ParseSettlementInfo self.MandelBricksID", self.MandelBricksID)
    Server.SeasonServer:SetSeasonChangeInfo(seasonInfo)
    self:SetTeammateItem(carryOutProps)
    self.myCarryOutProps = carryOutProps
    self.breastPlateProp = nil
    self.HelmetProp = nil
    for _, v in pairs(self.myCarryOutProps) do
        local propInfo = v.load_props
        -- 如果是虚拟道具（队友背包下的自己的道具）
        if v.position == eEquipPosition.CarryOutPropsPos then
            for _, eachPropInfo in pairs(propInfo) do
                if eachPropInfo.bind_player == Server.AccountServer:GetPlayerId() then
                    table.insert(self.myVirtualItemsProps, eachPropInfo)
                end
            end
        else
            for _, eachPropInfo in pairs(propInfo) do
                if eachPropInfo.bind_player ~= 0 and eachPropInfo.bind_player ~= Server.AccountServer:GetPlayerId() then
                    table.insert(self.notMyItemsProps, eachPropInfo)
                end
            end
        end
        if v.position == eEquipPosition.BreastPlate then
            for _, eachPropInfo in pairs(propInfo) do
                self.breastPlateProp = eachPropInfo.id
            end
        end
        if v.position == eEquipPosition.Helmet then
            for _, eachPropInfo in pairs(propInfo) do
                self.HelmetProp = eachPropInfo.id
            end
        end
    end
    local profit = 0
    if endGameType == EGspPlayerGameStatusType.kGspPlayerGameStatusTypeQuitGame then
        profit = Ntf.player.quit_game.carry_out_profit_price
    elseif endGameType == EGspPlayerGameStatusType.kGspPlayerGameStatusTypeEndGame then
        profit = Ntf.player.end_game.carry_out_profit_price
    end
    return true
end

--精彩时刻 结算日志上报
function SettlementServer:SetHighlightMomentSettlemtnInfo(Ntf)
    --CSSettlementGetNewSettlementInfoRes
    local highLightMomentMgr = UHighlightMomentMgr.Get(GetWorld())
    if isvalid(highLightMomentMgr) then
        local HighlightMomentSOLSettlementInfo = FHighlightMomentSOLSettlementInfo() 
        --游戏时长
        local gameDuration = 0
        local endGameType = self:GetEndGameType()
        local ExitPointName = ""
        local rankLevelIncrease = 0
        local rankScore = 0
        local KilNo = 0
        if endGameType == EGspPlayerGameStatusType.kGspPlayerGameStatusTypeQuitGame then
            if Ntf.player and Ntf.player.quit_game then
                gameDuration = Ntf.player.quit_game.play_time

                for i, v in pairs( Ntf.player.quit_game.kill_array) do
                    if v.type == 0 then
                        KilNo = KilNo + 1
                    end
                end
               
            end
        elseif endGameType == EGspPlayerGameStatusType.kGspPlayerGameStatusTypeEndGame then
            if Ntf.player and Ntf.player.end_game then
                -- gameDuration = Ntf.player.end_game.play_time
                -- assetsGained = Ntf.player.end_game.carry_out_profit_price + Ntf.player.end_game.cost_price
                ExitPointName = Ntf.player.end_game.extraction_location
                for i, v in pairs( Ntf.player.end_game.kill_array) do
                    if v.type == 0 then
                        KilNo = KilNo + 1
                    end
                end
            end
        end

        
        if Ntf.player then
            rankLevelIncrease = Ntf.player.rank_match_score_delta
            rankScore = Ntf.player.rank_match_score_delta
        end
        if Ntf.match_info then
            HighlightMomentSOLSettlementInfo.RoomId = Ntf.match_info.room_id
        end
        HighlightMomentSOLSettlementInfo.KillNo = KilNo
        HighlightMomentSOLSettlementInfo.AssistNo = Ntf.player.assist_cnt
        HighlightMomentSOLSettlementInfo.RankScore = rankScore
        HighlightMomentSOLSettlementInfo.GameDuration = gameDuration
        HighlightMomentSOLSettlementInfo.ExitPointName = ExitPointName
        HighlightMomentSOLSettlementInfo.RankLevelIncrease = rankLevelIncrease
        xxwwinfo("highLightMomentMgr:SetSOLSettlemtnInfos")
        highLightMomentMgr:SetSOLSettlemtnInfos(HighlightMomentSOLSettlementInfo)
    end
end


--精彩时刻 结算日志上报
function SettlementServer:SetHighlightMomentMPSettlemtnInfo(Ntf)
    --CSGetTDMSettlementInfoRes
    local highLightMomentMgr = UHighlightMomentMgr.Get(GetWorld())
    if isvalid(highLightMomentMgr) then
        local HighlightMomentMPSettlementInfo = FHighlightMomentMPSettlementInfo() 
        --游戏时长
        local gameDuration = 0
        local rankLevelIncrease = 0
        local rankScore = 0
        local account_exp = 0
        
        if Ntf.tdm_data then
            rankLevelIncrease = Ntf.tdm_data.real_total_ranked_score
            rankScore = Ntf.tdm_data.rank_match_score
            gameDuration = Ntf.tdm_data.game_time
            account_exp = Ntf.tdm_data.account_exp.exp
        end
        if Ntf.match_info then
            HighlightMomentMPSettlementInfo.RoomId = Ntf.match_info.room_id
        end
        HighlightMomentMPSettlementInfo.RankScore = rankScore
        HighlightMomentMPSettlementInfo.GameDuration = gameDuration
        local myPlayerID = Server.AccountServer:GetPlayerId()
        
        if self.tdmPlayerInfo[myPlayerID] then
            local myPlayer = self.tdmPlayerInfo[myPlayerID]
            HighlightMomentMPSettlementInfo.KillNo = (myPlayer.kill_player_count or 0) + (myPlayer.kill_ai_count or 0)
            HighlightMomentMPSettlementInfo.AssistNo = myPlayer.assist_count 
            HighlightMomentMPSettlementInfo.DeadNum = myPlayer.dead_count
        end
      
        xxwwinfo("highLightMomentMgr:SetMPSettlemtnInfos")
        highLightMomentMgr:SetMPSettlemtnInfos(HighlightMomentMPSettlementInfo)
    end
end

function SettlementServer:GetSettlementInfo()
    return self.settlementInfo
end

-- 是否触发了sol的段位保护
function SettlementServer:IsSolSettlementRankShield()
    if not self.settlementInfo then
        xxwwerror("SettlementServer:IsSolSettlementRankShield, settlementInfo is nil")
        return false
    end

    if not self.settlementInfo.player then
        xxwwerror("SettlementServer:IsSolSettlementRankShield, player is nil")
        return false
    end

    if self.settlementInfo.player.trigger_rank_shield then
        xxwwerror("SettlementServer:IsSolSettlementRankShield, trigger_rank_shield is true")
        return true
    end

    xxwwinfo("SettlementServer:IsSolSettlementRankShield, trigger_rank_shield is false")
    return false
end

function SettlementServer:GetSettlementMode()
    return self.settlementMode
end

function SettlementServer:GetDurabilityGidMap()
    if self.durabilityGidMap and next(self.durabilityGidMap) then
        return self.durabilityGidMap
    end
end

-- note: 计算击杀信息
function SettlementServer:_CalculateKillList()
    xxwwinfo("SettlementServer:_CalculateKillList")
    local endGameInfo = self:GetEndGameInfo()
    local killPlayerNum = 0
    local killAINum = 0
    local killBossNum = 0
    for i, v in pairs(endGameInfo.kill_array) do
        if v.enemy_type == EGspEnemyType.kGspEnemyTypePlayer or v.enemy_type == EGspEnemyType.kGspEnemyTypePlayerAI then
            killPlayerNum = killPlayerNum + 1
        elseif v.enemy_type == EGspEnemyType.kGspEnemyTypeAI then
            killAINum = killAINum + 1
        elseif v.enemy_type == EGspEnemyType.kGspEnemyTypeBoss then
            killBossNum = killBossNum + 1
        end
    end
    self.killMap[EGspEnemyType.kGspEnemyTypePlayer] = killPlayerNum
    self.killMap[EGspEnemyType.kGspEnemyTypeAI] = killAINum
    self.killMap[EGspEnemyType.kGspEnemyTypeBoss] = killBossNum
end

-- note: 根据击杀类型枚举获取该类型击杀数 EEnemyTag
function SettlementServer:GetKillNumByEnum(enemyTag)
    return self.killMap[enemyTag]
end

-- note: 获取开赛类型: 匹配/房间 EDSMatchType
function SettlementServer:GetMatchInfoType()
    xxwwinfo("SettlementServer:GetMatchInfoType")
    if self.matchInfo then
        return self.matchInfo.match_type
    end
    return nil
end

function SettlementServer:GetMatchInfoMapId()
    loginfo("SettlementServer:GetMatchInfoMapId")
    if self.matchInfo then
        return self.matchInfo.map_id
    end
    return nil
end

function SettlementServer:GetMatchInfoStartTime()
    xxwwinfo("SettlementServer:GetMatchInfoStartTime")
    if self.matchInfo then
        return self.matchInfo.start_time
    end
end

-- note: 获取死亡原因 被玩家/被ai/被boss/意外 EGspPlayerDeathReason
function SettlementServer:GetDeathType()
    local deathInfo = self:GetDeathInfo()
    xxwwinfo("SettlementServer:GetDeathType deathInfo ", deathInfo.reason)
    return deathInfo.reason
end

-- note: 获取意外死亡类型 EGspAccidentType
function SettlementServer:GetAccidentType()
    xxwwinfo("SettlementServer:GetAccidentType")
    local deathInfo = self:GetDeathInfo()
    local deathAccident = deathInfo.accident
    local accidentType = deathAccident.accident_type
    return accidentType
end

-- note: 致命部位枚举 EGspBodyPart
function SettlementServer:GetFatalBodyPart()
    xxwwinfo("SettlementServer:GetFatalBodyPart")
    local deathInfo = self:GetDeathInfo()
    local deathReason = deathInfo.reason
    if deathReason == EGspPlayerDeathReason.kGspPlayerDeathReasonKilledByAccident then
        local deathAccident = deathInfo.accident
        return deathAccident.body_part
    else
        local deathDamage = deathInfo.damage
        return deathDamage.body_part -- EGspBodyPart
    end
end

-- note: 击杀者名字
function SettlementServer:GetKillerName()
    xxwwinfo("SettlementServer:GetKillerName")
    local deathInfo = self:GetDeathInfo()
    local deathReason = self:GetDeathType()
    if deathReason == EGspPlayerDeathReason.kGspPlayerDeathReasonKilledByAccident then
        return ServerTipCode.AccidentalDeath
    elseif deathReason == EGspPlayerDeathReason.kGspPlayerDeathReasonKilledByAI or
        deathReason == EGspPlayerDeathReason.kGspPlayerDeathReasonKilledByAIPlayer then
        return deathInfo.ai.game_nick
    elseif deathReason == EGspPlayerDeathReason.kGspPlayerDeathReasonKilledByBoss then
        return string.format(ServerTipCode.Boss, deathInfo.boss.game_nick)
    elseif deathReason == EGspPlayerDeathReason.kGspPlayerDeathReasonKilledByPlayer then
        return deathInfo.player.game_nick
    end
end

-- azhengzheng:目前服务器返回的死亡原因不准确 这里特殊获取正确的击杀者信息
function SettlementServer:GetRightKillerName()
    local deathInfo = self:GetDeathInfo()

    if deathInfo.player then
        return deathInfo.player.game_nick
    end

    if deathInfo.ai then
        return deathInfo.ai.game_nick
    end

    return nil
end

function SettlementServer:IsPlayer2ShowReport()
    local deathReason = self:GetDeathType()
    if deathReason == EGspPlayerDeathReason.kGspPlayerDeathReasonKilledByAI then
        return false
    end
    return true
end

-- note: 击杀者等级
function SettlementServer:GetKillerLevel()
    xxwwinfo("SettlementServer:GetKillerLevel")
    local deathInfo = self:GetDeathInfo()
    local deathReason = deathInfo.reason
    if deathReason == EGspPlayerDeathReason.kGspPlayerDeathReasonKilledByAccident then
        return -1
    elseif deathReason == EGspPlayerDeathReason.kGspPlayerDeathReasonKilledByAI or
        deathReason == EGspPlayerDeathReason.kGspPlayerDeathReasonKilledByAIPlayer then
        return -1
    elseif deathReason == EGspPlayerDeathReason.kGspPlayerDeathReasonKilledByBoss then
        return -1
    elseif deathReason == EGspPlayerDeathReason.kGspPlayerDeathReasonKilledByPlayer then
        return deathInfo.ai.level
    end
end

-- note: 获取击杀者武器id
function SettlementServer:GetKillerWeaponId()
    xxwwinfo("SettlementServer:GetKillerWeaponId")
    local deathInfo = self:GetDeathInfo()
    local deathReason = deathInfo.reason
    local fatalDamageInfo = deathInfo.damage
    if deathReason ~= EGspPlayerDeathReason.kGspPlayerDeathReasonKilledByAccident then
        return fatalDamageInfo.weapon
    end
end

-- note: 获取击杀者武器属性
function SettlementServer:GetKillerWeaponPropInfo(weaponId)
    xxwwinfo("SettlementServer:GetKillerWeaponPropInfo", weaponId)
    local deathInfo = self:GetDeathInfo()
    local deathReason = deathInfo.reason
    local fatalDamageInfo = deathInfo.damage
    if deathReason ~= EGspPlayerDeathReason.kGspPlayerDeathReasonKilledByAccident then
        return fatalDamageInfo.weapon_prop
    end
end

-- //note: 获取膛线等级
function SettlementServer:GetKillerWeaponLevel()
    xxwwinfo("SettlementServer:GetKillerWeaponLevel")
    local deathInfo = self:GetDeathInfo()
    local deathReason = deathInfo.reason
    if deathReason ~= EGspPlayerDeathReason.kGspPlayerDeathReasonKilledByAccident then
        local fatalDamageInfo = deathInfo.damage
        local finalEquipment = fatalDamageInfo.weapon_prop
        local finalEquipmentInfo = finalEquipment.weapon
        local finalWeaponPerkInfo = finalEquipmentInfo.equipped_perk
        --TODO: 还没定
        return 0
    end
end

-- note: 获取死亡最后伤害值
function SettlementServer:GetKillerLastDamageValue()
    xxwwinfo("SettlementServer:GetKillerLastDamageValue")
    local deathInfo = self:GetDeathInfo()
    local deathReason = deathInfo.reason
    local damageValue = 0
    if deathReason ~= EGspPlayerDeathReason.kGspPlayerDeathReasonKilledByAccident then
        local fatalDamageInfo = deathInfo.damage
        damageValue = fatalDamageInfo.damage
    end
    damageValue = math.floor(damageValue + 0.5)
    return damageValue
end

-- //note: 获取防具耐久度
function SettlementServer:_GetProtectionDurability(fatalDamageInfo)
    xxwwinfo("SettlementServer:_GetProtectionDurability")
    local protectEquipmentCurrentDurability = fatalDamageInfo.health
    local protectEquipmentMaxDurability = fatalDamageInfo.health_max
    return protectEquipmentCurrentDurability, protectEquipmentMaxDurability
end

-- //note: 获取防护文本
function SettlementServer:GetProtectionText()
    xxwwinfo("SettlementServer:GetProtectionText")
    local deathInfo = self:GetDeathInfo()
    local deathReason = deathInfo.reason
    if deathReason ~= EGspPlayerDeathReason.kGspPlayerDeathReasonKilledByAccident then
        local fatalDamageInfo = deathInfo.damage
        local protectEquipmentId = fatalDamageInfo.enemy_equipment_id
        if protectEquipmentId then
            local protectEquipmentCurrentDurability, protectEquipmentMaxDurability = self:_GetProtectionDurability(fatalDamageInfo)
            return string.format("%d/%d", protectEquipmentCurrentDurability, protectEquipmentMaxDurability)
        else
            return string.format(ServerTipCode.UnProtected)
        end
    end
end

-- note: 获取击杀距离
function SettlementServer:GetHitDistance()
    xxwwinfo("SettlementServer:GetHitDistance")
    local deathInfo = self:GetDeathInfo()
    local deathReason = deathInfo.reason
    if deathReason ~= EGspPlayerDeathReason.kGspPlayerDeathReasonKilledByAccident then
        local fatalDamageInfo = deathInfo.damage
        local hitDistance = fatalDamageInfo.hit_distance or 0
        return math.ceil(hitDistance)
    end
    return 0
end

-- note: 获取正常结束比赛信息
function SettlementServer:GetEndGameInfo()
    xxwwinfo("SettlementServer:GetEndGameInfo")
    return self.playerInfo and self.playerInfo.end_game
end

-- note: 获取退出比赛信息
function SettlementServer:GetQuitGameInfo()
    xxwwinfo("SettlementServer:GetQuitGameInfo")
    return self.playerInfo.quit_game
end

-- note: 获取死亡信息
function SettlementServer:GetDeathInfo()
    return self:GetEndGameInfo().death_info
end

-- note: 获取撤离点枚举 EGspExtractionPoint
function SettlementServer:GetEscapePointEnum()
    xxwwinfo("SettlementServer:GetEscapePointEnum", self.escapePointEnum)
    return self.escapePointEnum
end

-- note: 点赞好友
function SettlementServer:Praise(playerId, praiseType)
    local function fOnLikeCallBack(res)
        if res.result ~= 0 then
            xxwwerror("Praise player", playerId, "Failed!")
        end
    end

    local req = pb.CSPlayerInfoPraiseReq:New()
    req.be_praised_player_id = playerId
    req.praise_type = praiseType
    req:Request(fOnLikeCallBack)
end

-- note: 收到点赞
function SettlementServer:OnPlayerInfoBePraisedNtf(ntf)
    xxwwinfo("SettlementServer:OnPlayerInfoBePraisedNtf")
    printtable(ntf)
end

-- note: 获取自身道具
function SettlementServer:GetCarryOutProps()
    xxwwinfo("SettlementServer:GetCarryOutProps")
    local carryOutProps = {}
    local endGameType = self:GetEndGameType()
    if endGameType == EGspPlayerGameStatusType.kGspPlayerGameStatusTypeQuitGame then
        carryOutProps = self:GetQuitGameInfo().carry_out_props
    elseif endGameType == EGspPlayerGameStatusType.kGspPlayerGameStatusTypeEndGame then
        carryOutProps = self:GetEndGameInfo().carry_out_props
    end
    return carryOutProps
end

-- note: 结束游戏的类型:游戏中0/正常结束1/退出2 EGspPlayerGameStatusType
function SettlementServer:GetEndGameType()
    self.playerInfo.type = self.playerInfo.type or EGspPlayerGameStatusType.kGspPlayerGameStatusTypeQuitGame
    xxwwinfo("SettlementServer:GetEndGameType", self.playerInfo.type)
    return self.playerInfo.type
end

-- note: 正常结束的类型:成功撤离0/失败撤离1/失踪2 EGspPlayerResultType
function SettlementServer:GetEscapeGameType()
    if self:GetEndGameType() == EGspPlayerGameStatusType.kGspPlayerGameStatusTypeEndGame then
        self.playerInfo.end_game.type = self.playerInfo.end_game.type or EGspPlayerResultType.kGspPlayerResultEscaped
        xxwwinfo("SettlementServer:GetEscapeGameType self.playerInfo.end_game.type ", self.playerInfo.end_game.type)
        return self.playerInfo.end_game.type
    else
        xxwwerror("SettlementServer:GetEscapeGameType error")
        return nil
    end
end

-- note: 获取队友信息
function SettlementServer:GetTeammateArray()
    xxwwinfo("SettlementServer:GetTeammateArray")
    return self.teammateArray
end

-- note: 设置队友道具数据
function SettlementServer:SetTeammateItem(props)
    if table.isempty(props) then
        xxwwerror("SettlementServer:SetTeammateItem props is nil!")
        return
    end
    for _, prop in pairs(props) do
        for _, item in pairs(prop.load_props) do
            if item.bind_type == PropBindingType.BindingTeammate then
                self.bTeammateItem = true
                break
            end
        end
        if self.bTeammateItem then
            break
        end
    end
end

-- note: 获得myCarryOutItem
function SettlementServer:GetMyCarryOutVirtualItem(itemGid)
    if self.myCarryOutVirtualItems then
        for _, item in pairs(self.myCarryOutVirtualItems) do
            if item.rawPropInfo.gid == itemGid then
                return item
            end
        end
    end
end

-- note: 设置myCarryOutItems
function SettlementServer:SetMyCarryOutVirtualItems(myCarryOutVirtualItems)
    self.myCarryOutVirtualItems = myCarryOutVirtualItems
end

-- note: 有队友的道具? -- todo:不知道干嘛
function SettlementServer:HasTeammateItem()
    return self.bTeammateItem
end

function SettlementServer:OnReceivePlayerBanNtf(res)
    xxwwinfo("SettlementServer:OnReceivePlayerBanNtf")
    if res.result == 0 then
        xxwwinfo("SettlementServer:OnReceivePlayerBanNtf playerid " .. Server.AccountServer:GetPlayerId())
        xxwwinfo("SettlementServer:OnReceivePlayerBanNtf res.reason " .. res.reason)
        xxwwinfo("SettlementServer:OnReceivePlayerBanNtf res.custom_reason " .. res.custom_reason)
        if res.reason == 2 then
            xxwwinfo("SettlementServer:OnReceivePlayerBanNtf ignore because reason = 2")
            return
        end
        settlementMgrIns:BroadcastServerPlayerBan(Server.AccountServer:GetPlayerId(), res.reason, res.custom_reason)
        settlementMgrIns:SettlementEndBroadcast()
    end
end

function SettlementServer:OnAchieveProgressUpdatedNtf(res)
    xxwwinfo("SettlementServer:OnAchieveProgressUpdatedNtf")
    self.achiveInfo = {}
    if res and res.states then
        for _, value in ipairs(res.states) do
            table.insert(self.achiveInfo, value)
        end
    end
    self.Events.evtAchieveProgressUpdatedNtf:Invoke()
end

function SettlementServer:GetAchieveProgressUpdatedInfo()
    return self.achiveInfo
end

------------------------Raid 相关 Start-------------------------------------------------------------------------------------------------------------------
------------------------Raid 相关 Start-------------------------------------------------------------------------------------------------------------------
------------------------Raid 相关 Start-------------------------------------------------------------------------------------------------------------------
-- note: 获取结算通知
---@param res pb_CSSettlementEmptyNtf
function SettlementServer:OnReceiveSettlementNtf(res)
    xxwwinfo("SettlementServer:OnReceiveSettlementNtf res.result:" ..
        tostring(res.result) .. " res.mode:" .. tostring(res.mode))
    self:CleanUpSettlementData()
    if Facade.GameFlowManager:IsInOBMode() then
        xxwwinfo("SettlementServer:OnReceiveSettlementNtf obmode doesn't need settlement")
        return
    end
    if DFHD_LUA == 1 then
        Server.LogUploadServer:OnUploadMetaperf()
    end
    if res.result == 0 then
        self.Events.evtReceiveEmptyNtf:Invoke()
    end

    local currentGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    if currentGameFlow == EGameFlowStageType.Game or currentGameFlow == EGameFlowStageType.GameSettlement then
        -- 仅当还在局内时收到CSSettlementEmptyNtf认为是有效的
        self.bGetNtf = true
    else
        self.bGetNtf = false
        xxwwerror("SettlementServer:OnReceiveSettlementNtf self.bGetNtf=false because currentGameFlow is", currentGameFlow)
    end
    settlementMgrIns:CloseForceQuit()
    -- 停止局内音效
    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.IngameSoundOff)
    if res.result == 0 then
        -- 注释掉，结算多拉了一次
        -- Server.InventoryServer:FetchAllItems(EGetPropInvokeEvent.Settlement) -- 需要在结算后拉取仓库数据
        if Server.MatchServer:GetDsRoomId() ~= 0 and res.room_id ~= Server.MatchServer:GetDsRoomId() then
            xxwwinfo("SettlementServer:OnReceiveSettlementNtf roomid not equality , dont req settPkg! this == ", res.room_id,
                " now == ", Server.MatchServer:GetDsRoomId())
            return
        end
        if currentGameFlow ~= EGameFlowStageType.Game and
            currentGameFlow ~= EGameFlowStageType.GameSettlement then
            Server.InventoryServer:FetchAllItems() -- 需要在结算后拉取仓库数据
            xxwwinfo("SettlementModule:OnReceiveSettlementNtf curGameFlow ", currentGameFlow)
            if currentGameFlow == EGameFlowStageType.LobbyToGame or
                currentGameFlow == EGameFlowStageType.SafeHouseToGame then
                self.bAlreadySettlement = true
                xxwwinfo("SettlementModule:OnReceiveSettlementNtf bAlreadySettlement ",
                    currentGameFlow)
            end
            if currentGameFlow ~= EGameFlowStageType.GameToLobby then
                xxwwinfo("SettlementServer:OnReceiveSettlementNtf is not in game and GameToLobby ignore settNTF!send delete roomid ",
                        res.room_id)

                -- azhengzheng:玩家中途退出 自己拉一次SOL结算包 再清空
                if res.mode == ESettlementNtf.SNTF_SOL then
                    self:ReqForSOLSettlementInfo()
                end
               
                local req = pb.CSSettlementGetHasUnsettledMatchReq:New()
                req.delete_room_id = res.room_id
                req:Request()
                xxwwinfo("SettlementModule:OnReceiveSettlementNtf curGameFlow is to show bfweapon ", currentGameFlow)
                return
            end
        end
        --还没加载前就切gameflow
        settlementMgrIns:BroadcastChangeGameFlow2GameSettlement()
        if res.mode == ESettlementNtf.SNTF_RAID then
            self:ReqForRaidSettlementInfo()
        elseif res.mode == ESettlementNtf.SNTF_TDM then
            if DFHD_LUA == 1 then
            else
                xxwwinfo("SettlementModule:OnReceiveSettlementNtf tdm force gc ")
                settlementMgrIns:DestroyAllPlayerActor()
                ULuaExtension.ForceGC()
            end
            self:ReqForTDMSettlementInfo()
        elseif res.mode == ESettlementNtf.SNTF_SOL then
            self:ReqForSOLSettlementInfo()
        elseif res.mode == ESettlementNtf.SNTF_Arena then
            self:ReqForArenaSettlementInfo()
        end
    else
        xxwwerror("SettlementServer:OnReceiveSettlementNtf error res.result ==", res.result)
    end
end

function SettlementServer:ReqForArenaSettlementInfo(roomID)
    local req = pb.CSGetArenaSettlementInfoReq:New()
    req.room_id = roomID or Server.MatchServer:GetDsRoomId()

    req:Request(function(res)
        if res.result == 0 then
            local currentGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
            self._bGetArenaSettlementInfo = true
            self._settlementInfoSource = SettlementDefine.ESettlementInfoSource.Arena

            if currentGameFlow ~= EGameFlowStageType.Game and currentGameFlow ~= EGameFlowStageType.GameSettlement and currentGameFlow ~= EGameFlowStageType.GameToLobby then
                return
            end

            self:ParseArenaSettlementInfo(res)
            Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.IngameSoundOff)
            self.Events.evtArenaSettlementDo:Invoke()

            return
        end

        local currentGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()

        if currentGameFlow ~= EGameFlowStageType.Game and currentGameFlow ~= EGameFlowStageType.GameSettlement and currentGameFlow ~= EGameFlowStageType.GameToLobby then
            return
        end

        self.Events.evtOnReqForSettlementInfoFailed:Invoke(ESettlementNtf.SNTF_Arena)
    end, {bNeedResendAfterReconnected = true})
end

function SettlementServer:ParseArenaSettlementInfo(arenaSettlementInfo)
    if not arenaSettlementInfo then
        return
    end

    self:ParseArenaMatchInfo(arenaSettlementInfo.match_info)
    self:ParseArenaSettlementData(arenaSettlementInfo.arena_data)
end

function SettlementServer:ParseArenaMatchInfo(matchInfo)
    if not matchInfo then
        return
    end

    self._arenaRoomID = matchInfo.room_id
    self._arenaStartTime = matchInfo.start_time
    self._arenaMapName = self:_GetMapNameByMapID(matchInfo.mode_info and matchInfo.map_id)
end

function SettlementServer:GetArenaRoomID()
    return self._arenaRoomID
end

function SettlementServer:GetArenaStartTime()
    return self._arenaStartTime
end

function SettlementServer:GetArenaMapName()
    return self._arenaMapName
end

function SettlementServer:ParseArenaSettlementData(arenaData)
    if not arenaData then
        return
    end

    self._arenaResult = arenaData.result
    local myPlayerID = Server.AccountServer:GetPlayerId()
    self._arenaTeamInfoList, self._arenaTeamPlayerInfoList = {}, {}

    for _, team in pairs(arenaData.teams) do
        table.insert(self._arenaTeamInfoList, {teamID = team.team_id, score = team.score, bMyTeam = arenaData.my_team_id == team.team_id})

        for _, player in pairs(team.players) do
            local arenaTeamPlayerInfoDataRow, remainFindEquipment = {}, 3
            arenaTeamPlayerInfoDataRow.teamID = team.team_id
            arenaTeamPlayerInfoDataRow.bMyTeam = arenaData.my_team_id == team.team_id
            arenaTeamPlayerInfoDataRow.playerID = player.basic_info.player_id
            --- BEGIN MODIFICATION @ VIRTUOS: platform identifier
            arenaTeamPlayerInfoDataRow.platID = Module.Settlement:GetPlatIDByPlayerInfo(player)
            --- END MODIFICATION @ VIRTUOS
            arenaTeamPlayerInfoDataRow.bMySelf = myPlayerID == player.basic_info.player_id
            arenaTeamPlayerInfoDataRow.gameNick = player.basic_info.game_nick
            arenaTeamPlayerInfoDataRow.heroID = player.hero.hero_id
            arenaTeamPlayerInfoDataRow.accessories = player.hero.accessories
            arenaTeamPlayerInfoDataRow.rank = player.rank or 0
            arenaTeamPlayerInfoDataRow.killCount = (player.kill_player_count or 0) + (player.kill_ai_count or 0)
            arenaTeamPlayerInfoDataRow.deadCount = player.dead_count
            arenaTeamPlayerInfoDataRow.assistCount = player.assist_count

            --- BEGIN MODIFICATION @ VIRTUOS: Replace player name with OnlineId 
            if IsPS5Family() and Module.Settlement:GetPlatIDByPlayerInfo(player) == PlatIDType.Plat_Playstation then
                local UDFMOnlineIdentityManager = import "DFMOnlineIdentityManager"
                local OnlineIdManager = UDFMOnlineIdentityManager.Get(GetGameInstance())
                if OnlineIdManager and player.basic_info.player_id ~= nil then
                    local PS5OnlineId = OnlineIdManager:GetPlayerPlatformIdByOpenId(player.basic_info.player_id)
                    if not string.isempty(PS5OnlineId) then
                        arenaTeamPlayerInfoDataRow.gameNick = PS5OnlineId
                    end
                end
            end
            --- END MODIFICATION @ VIRTUOS

            if myPlayerID == player.basic_info.player_id then
                --缓存自己的数据给精彩时刻

            end

            for _, finalProp in pairs(player.final_props) do
                for _, loadProp in pairs(finalProp.load_props) do
                    if loadProp.position == ESlotType.MainWeaponLeft or loadProp.position == ESlotType.MainWeaponRight or loadProp.position == ESlotType.Pistrol then
                        if not arenaTeamPlayerInfoDataRow.weapon then
                            arenaTeamPlayerInfoDataRow.weapon = ItemBase:NewIns(loadProp.id)
                            arenaTeamPlayerInfoDataRow.weapon:SetRawPropInfo(loadProp)
                            remainFindEquipment = remainFindEquipment - 1
                        end
                    elseif loadProp.position == ESlotType.Helmet then
                        if not arenaTeamPlayerInfoDataRow.helmet then
                            arenaTeamPlayerInfoDataRow.helmet = ItemBase:NewIns(loadProp.id)
                            arenaTeamPlayerInfoDataRow.helmet:SetRawPropInfo(loadProp)
                            remainFindEquipment = remainFindEquipment - 1
                        end
                    elseif loadProp.position == ESlotType.BreastPlate then
                        if not arenaTeamPlayerInfoDataRow.breastPlate then
                            arenaTeamPlayerInfoDataRow.breastPlate = ItemBase:NewIns(loadProp.id)
                            arenaTeamPlayerInfoDataRow.breastPlate:SetRawPropInfo(loadProp)
                            remainFindEquipment = remainFindEquipment - 1
                        end
                    end

                    if remainFindEquipment == 0 then
                        break
                    end
                end

                if remainFindEquipment == 0 then
                    break
                end
            end

            table.insert(self._arenaTeamPlayerInfoList, arenaTeamPlayerInfoDataRow)
        end
    end

    table.sort(self._arenaTeamInfoList, function(arenaTeamInfoA, arenaTeamInfoB)
        return arenaTeamInfoA.teamID < arenaTeamInfoB.teamID
    end)

    table.sort(self._arenaTeamPlayerInfoList, function(arenaTeamPlayerInfoA, arenaTeamPlayerInfoB)
        if arenaTeamPlayerInfoA.rank == 0 then
            if arenaTeamPlayerInfoB.rank == 0 then
                return arenaTeamPlayerInfoA.playerID < arenaTeamPlayerInfoB.playerID
            end

            return false
        end

        if arenaTeamPlayerInfoB.rank == 0 then
            return true
        end

        return arenaTeamPlayerInfoA.rank < arenaTeamPlayerInfoB.rank
    end)

    for _, player in pairs(self._arenaTeamPlayerInfoList) do
        for key, team in ipairs(self._arenaTeamInfoList) do
            if player.teamID == team.teamID then
                player.teamIdx = key
                break
            end
        end
    end
end

function SettlementServer:GetArenaResult()
    return self._arenaResult
end

function SettlementServer:GetArenaTeamInfoList()
    return self._arenaTeamInfoList
end

function SettlementServer:GetArenaTeamPlayerInfoList()
    return self._arenaTeamPlayerInfoList
end

function SettlementServer:GetArenaTeamPlayerInfoListByTeamID(teamID)
    local arenaTeamPlayerInfoList = {}

    for _, value in ipairs(self._arenaTeamPlayerInfoList) do
        if teamID == value.teamID then
            table.insert(arenaTeamPlayerInfoList, value)
        end
    end

    return arenaTeamPlayerInfoList
end

--请求大战场结算包
function SettlementServer:ReqForTDMSettlementInfo(roomId)
    xxwwinfo("SettlementServer:ReqForTDMSettlementInfo")
    local req = pb.CSGetTDMSettlementInfoReq:New()
    if roomId then
        req.room_id = roomId
    else
        req.room_id = Server.MatchServer:GetDsRoomId()
    end
    req:Request(function(res)
        if Server.MatchServer:GetDsRoomId() ~= 0 and res.match_info ~= nil
                and res.match_info.room_id ~= nil and res.match_info.room_id ~= Server.MatchServer:GetDsRoomId() then
            xxwwinfo("SettlementServer:ReqForTDMSettlementInfo roomid not equality , dont req settPkg! this == ",
                res.room_id,
                " now == ", Server.MatchServer:GetDsRoomId())
            return
        end
        self:Save2File(res)
        if res.result == 0 then
            self.isNewMPSettlementData = true
            self._settlementInfoSource = SettlementDefine.ESettlementInfoSource.MP
            --- BEGIN MODIFICATION @ VIRTUOS: 实现Competitive Activity
            if self:ParseTDMSettlementInfo(res) then
                --logtable(res, true)
                local currentGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
                if currentGameFlow == EGameFlowStageType.Game or currentGameFlow == EGameFlowStageType.GameSettlement then
                    xxwwinfo("SettlementModule:ReqForTDMSettlementInfo now in game 2 show")
                    local breakthroughGPSubsystemIns = ABreakthroughGPSubsystem.Get(GetWorld())
                    breakthroughGPSubsystemIns:BindCutSceneEvent()
                    self.Events.evtTDMSettlementDo:Invoke()
                    self:HandlePS5CompetitiveActivity()
                    
                else
                    xxwwerror("SettlementServer:ReqForTDMSettlementInfo error curGameFlow is ", Facade.GameFlowManager:GetCurrentGameFlow())
                end
            else
                --处理中途退出
                self:HandleLeaveMatchForPS5Activity()
            end
            --- END MODIFICATION
            self:SetHighlightMomentMPSettlemtnInfo(res)
        else
            xxwwerror("SettlementServer:ReqForTDMSettlementInfo error res.result ==", res.result)
            self.Events.evtOnReqForSettlementInfoFailed:Invoke(ESettlementNtf.SNTF_TDM)
        end
    end, { bNeedResendAfterReconnected = true })
end

function SettlementServer:CheckIsNewMPSettlement()
    return self.isNewMPSettlementData
end

function SettlementServer:RecoverMPSettlement()
    self.isNewMPSettlementData = nil
end

function SettlementServer:ParseTDMSettlementInfo(res)
    xxwwinfo("SettlementServer:ParseTDMSettlementInfo")
    self._mpSettlementInfo = res
    self.tdmData = res.tdm_data
    self.tdmMatchInfo = res.match_info
    self.tdmActivityData = res.activity_data
    --记录队伍信息
    self.tdmPlayerTeamInfo = {}
    --记录队伍排名
    self.tdmPlayerTeamRank = {}
    --所有玩家信息
    self.tdmPlayerInfo = {} ---@type table<any, pb_TDMPlayer| {}>
    self.weaponChange = nil
    self.weaponChange = self.tdmData.weapon_change
    self.bShowWeaponChangeUI = false
    if self.weaponChange then
        self.bShowWeaponChangeUI = true
    end
    local myColor = self.tdmData.my_color
    self.tdmResult = -1
    self.tdmSettlementAccountExp = self.tdmData.account_exp
    for _, v in pairs(self.tdmData.camp_list) do
        if v.od_camp.offense_total_stronghold > 0 then
            self.bIsGF = true
            break
        else
        end
    end
    for _, tdmCamp in pairs(self.tdmData.camp_list) do
        for _, tdmTeam in pairs(tdmCamp.team_list) do
            if not self.tdmPlayerTeamInfo[tdmTeam.team_id] then
                self.tdmPlayerTeamInfo[tdmTeam.team_id] = {}
                self.tdmPlayerTeamRank[tdmTeam.team_id] = {}
            end
            self.tdmPlayerTeamInfo[tdmTeam.team_id] = tdmTeam.player_list
            self.tdmPlayerTeamRank[tdmTeam.team_id] = tdmTeam.team_rank
            for _, tdmPlayer in pairs(tdmTeam.player_list) do
                if not self.tdmPlayerInfo[tdmPlayer.player_id] then
                    self.tdmPlayerInfo[tdmPlayer.player_id] = {}
                end
                self.tdmPlayerInfo[tdmPlayer.player_id] = tdmPlayer
            end
        end
        if myColor == tdmCamp.color then
            self.tdmResult = tdmCamp.is_winner
        end
    end
    --logtable(self.showOrderFlag, true)
    --for index, tdmMvp in pairs(self.tdmData.mvp_list) do
    --    xxwwinfo("SettlementServer:ParseTDMSettlementInfo self.tdmData.mvp_list[index].evaluated_list.dimension ",
    --        self.tdmData.mvp_list[index].evaluated_list[1].dimension)
    --    if self.showOrderFlag[self.tdmData.mvp_list[index].evaluated_list[1].dimension] and
    --        self.showOrderFlag[self.tdmData.mvp_list[index].evaluated_list[1].dimension] == true then
    --        xxwwinfo("SettlementServer:ParseTDMSettlementInfo self.tdmData.mvp_list[index].evaluated_list[1].dimension ",
    --            self.tdmData.mvp_list[index].evaluated_list[1].dimension)
    --        table.insert(self.showOrder, self.tdmData.mvp_list[index].evaluated_list[1].dimension)
    --        self.mvp2ShowOrder[self.tdmData.mvp_list[index].evaluated_list[1].dimension] = index
    --    end
    --end
    if next(self.tdmData) and next(self.tdmMatchInfo) and next(self.tdmPlayerTeamInfo) and next(self.tdmPlayerInfo) and
        self.tdmPlayerInfo[Server.AccountServer:GetPlayerId()].leave == false then --看看自己是不是强退的
        return true
    end
    if self.tdmPlayerInfo[Server.AccountServer:GetPlayerId()].leave == false then
        xxwwerror("SettlementServer:ParseTDMSettlementInfo fail!")
    else
        xxwwinfo("SettlementServer:ParseTDMSettlementInf Quit Game")
    end
    return false
end

function SettlementServer:GetMPQuitGameIsLeave()
    if not self.tdmPlayerInfo or not self.tdmPlayerInfo[Server.AccountServer:GetPlayerId()] then
        return false
    end
    return self.tdmPlayerInfo[Server.AccountServer:GetPlayerId()].leave
end

function SettlementServer:GetMPSettlementInfo()
    return self._mpSettlementInfo
end

-- 是否触发了mp的段位保护
function SettlementServer:IsMpSettlementRankShield()
    if not self._mpSettlementInfo then
        xxwwerror("SettlementServer:IsMpSettlementRankShield, _mpSettlementInfo is nil")
        return false
    end

    if not self._mpSettlementInfo.tdm_data then
        xxwwerror("SettlementServer:IsMpSettlementRankShield, tdm_data is nil")
        return false
    end

    if not self._mpSettlementInfo.tdm_data.rank_shields then
        xxwwinfo("SettlementServer:IsMpSettlementRankShield, rank_shields is nil")
        return false
    end

    if #self._mpSettlementInfo.tdm_data.rank_shields > 0 then
        xxwwinfo("SettlementServer:IsMpSettlementRankShield, rank_shields num:", #self._mpSettlementInfo.tdm_data.rank_shields)
        return true
    end

    xxwwinfo("SettlementServer:IsMpSettlementRankShield, rank_shields num: 0")
    return false
end

function SettlementServer:GetTDMSettlementWeaponChangeFlag()
    return self.bShowWeaponChangeUI
end

function SettlementServer:GetTDMSettlementInfo()
    if self.tdmData and next(self.tdmData) then
        return self.tdmData
    end
    xxwwerror("SettlementServer:GetTDMSettlementInfo fail!")
    return nil
end

function SettlementServer:GetTDMSettlementMatchInfo()
    if next(self.tdmMatchInfo) then
        return self.tdmMatchInfo
    end
    xxwwerror("SettlementServer:GetTDMSettlementMatchInfo fail!")
    return nil
end

---@desc 是否是人机局
function SettlementServer:IsInHumanMachineRoom()
    local tdmMatchInfo = Server.SettlementServer:GetTDMSettlementMatchInfo()
    if tdmMatchInfo and tdmMatchInfo.mode_info and tdmMatchInfo.mode_info.game_rule == MatchGameRule.TDMHumanMachineGameRule then
        return true
    end
    return false
end

---@desc 是否是指挥官模式
function SettlementServer:IsTDMCommanderGame()
    local tdmMatchInfo = Server.SettlementServer:GetTDMSettlementMatchInfo()
    if tdmMatchInfo and tdmMatchInfo.mode_info and tdmMatchInfo.mode_info.game_rule == MatchGameRule.TDMCommanderGameRule then
        return true
    end
    return false
end

---@desc 是否是团队死斗模式
function SettlementServer:IsTDMTacticalConquestEvolution()
    local tdmMatchInfo = Server.SettlementServer:GetTDMSettlementMatchInfo()
    if tdmMatchInfo and tdmMatchInfo.mode_info and tdmMatchInfo.mode_info.sub_mode == MatchSubMode.TDMReserve13 then
        return true
    end
    return false
end

function SettlementServer:GetTDMSettlementMapInfo()
    if next(self.tdmMatchInfo) and self.tdmMatchInfo.mode_info.map_id then
        return self.tdmMatchInfo.mode_info.map_id
    end
    xxwwerror("SettlementServer:GetTDMSettlementMapInfo fail!")
    return nil
end

function SettlementServer:GetTDMSettlementMatchModeID()
    if next(self.tdmMatchInfo) and self.tdmMatchInfo.mode_info.match_mode_id then
        return self.tdmMatchInfo.mode_info.match_mode_id
    end
    xxwwerror("SettlementServer:GetTDMSettlementMatchModeID fail!")
    return nil
end

function SettlementServer:GetTDMSettlementTeamInfo()
    if next(self.tdmPlayerTeamInfo) then
        return self.tdmPlayerTeamInfo
    end
    xxwwerror("SettlementServer:GetTDMSettlementTeamInfo fail!")
    return nil
end

function SettlementServer:GetTDMSettlementTeamRankInfo(teamID)
    if next(self.tdmPlayerTeamRank) then
        if self.tdmPlayerTeamRank[teamID] then
            return self.tdmPlayerTeamRank[teamID]
        end
    end
    xxwwerror("SettlementServer:GetTDMSettlementTeamRankInfo fail!")
    return nil
end

function SettlementServer:GetTDMSettlementPlayerInfo()
    if next(self.tdmPlayerInfo) then
        return self.tdmPlayerInfo
    end
    xxwwerror("SettlementServer:GetTDMSettlementPlayerInfo fail!")
    return nil
end

function SettlementServer:GetTDMSettlementPlayerInfoNameById(id)
    if self.tdmPlayerInfo[id] then
        if self.tdmPlayerInfo[id].basic_info then
            if self.tdmPlayerInfo[id].basic_info.game_nick ~= "" then
                return self.tdmPlayerInfo[id].basic_info.game_nick
            end
        end
        if self.tdmPlayerInfo[id].ai_info then
            return self.tdmPlayerInfo[id].ai_info.game_nick
        end
    end
    xxwwerror("SettlementServer:GetTDMSettlementPlayerInfoNameById use default name")
    return "Player"
end

function SettlementServer:GetTDMSettlementIsAi(id)
    if self.tdmPlayerInfo[id] then
        if self.tdmPlayerInfo[id].basic_info then
            if self.tdmPlayerInfo[id].basic_info.game_nick ~= "" then
                return false
            end
        end
        if self.tdmPlayerInfo[id].ai_info then
            return true
        end
    end
    return true
end

function SettlementServer:GetTDMSettlementMVPTeamInfo()
    if not next(self.tdmPlayerInfo) or not next(self.tdmData) then
        xxwwerror("SettlementServer:GetTDMSettlementMVPTeamInfo fail!")
        return nil
    end
    -- 如果我的小队是最佳小队，那么使用我的小队的结果
    if self:GetTDMSettlementMVPTeamSame() then
        xxwwinfo("SettlementServer:GetTDMSettlementMVPTeamInfo GetTDMSettlementMVPTeamSame return true, turn to GetTDMSettlementTeamMemberInfo")
        return self:GetTDMSettlementTeamMemberInfo()
    end
    local myPlayerId = Server.AccountServer:GetPlayerId()
    local MVPTeamEquipList = {}
    local MVPTeamHeroList = {}
    local MVPTeamFashionList = {}
    for _, v in pairs(self.tdmData.camp_list) do
        if v.is_winner == 1 then
            for _, tdmTeam in pairs(v.team_list) do
                if tdmTeam.team_rank == 1 then
                    xxwwinfo("SettlementServer:GetTDMSettlementMVPTeamEquipInfo MVPteam ", tdmTeam.team_id)
                    for _, player in ipairs(tdmTeam.player_list) do
                        if tostring(myPlayerId) == tostring(player.player_id) then
                            table.insert(MVPTeamEquipList,1,player.props)
                            table.insert(MVPTeamFashionList,1, player.hero.fashion)
                            table.insert(MVPTeamHeroList,1, player.hero.hero_id)
                        else
                            table.insert(MVPTeamEquipList, player.props)
                            table.insert(MVPTeamFashionList, player.hero.fashion)
                            table.insert(MVPTeamHeroList, player.hero.hero_id)
                        end
                    end
                end
            end
        end
    end
    if not next(MVPTeamEquipList) then
        xxwwerror("SettlementServer:GetTDMSettlementMVPTeamInfo MVPTeamEquipList is nil!")
    end
    if not next(MVPTeamHeroList) then
        xxwwerror("SettlementServer:GetTDMSettlementMVPTeamInfo MVPTeamHeroList is nil!")
    end
    if not next(MVPTeamFashionList) then
        xxwwerror("SettlementServer:GetTDMSettlementMVPTeamInfo MVPTeamFashionList is nil!")
    end
    return MVPTeamEquipList,MVPTeamHeroList,MVPTeamFashionList
end

function SettlementServer:GetTDMSettlementMVPHeroId()
    if not next(self.tdmPlayerInfo) or not next(self.tdmData) then
        xxwwerror("SettlementServer:GetTDMSettlementMVPHeroId fail!")
        return nil
    end
    local MVPHeroList = {}
    --if next(self.tdmData.mvp_list) then
    --    for _, mvp in pairs(self.tdmData.mvp_list) do
    --        local HeroInfo = self.tdmPlayerInfo[mvp.player_id].hero.hero_id
    --        if HeroInfo then
    --            table.insert(MVPHeroList, HeroInfo)
    --        else
    --            xxwwerror("SettlementServer:GetTDMSettlementMVPHeroId HeroInfo is nil!")
    --        end
    --    end
    --end
    --if not next(MVPHeroList) then
    --    xxwwerror("SettlementServer:GetTDMSettlementMVPHeroId MVPHeroList is nil!")
    --end
    return MVPHeroList
end

function SettlementServer:GetTDMSettlementMVPTeamSame()
    if not next(self.tdmPlayerInfo) or not next(self.tdmData) then
        xxwwerror("SettlementServer:GetTDMSettlementMVPTeamSame fail!")
        return nil
    end
    for _, v in pairs(self.tdmData.camp_list) do
        if v.is_winner == 1 then
            for _, tdmTeam in pairs(v.team_list) do
                if tdmTeam.team_rank == 1 then
                    local myPlayerId = Server.AccountServer:GetPlayerId()
                    for _, player in pairs(tdmTeam.player_list) do
                        if player.player_id == myPlayerId then
                            xxwwinfo("SettlementServer:GetTDMSettlementMVPTeamSame same")
                            return true
                        end
                    end
                end
            end
        end
    end
    xxwwinfo("SettlementServer:GetTDMSettlementMVPTeamSame notsame")
    return false
end

---@desc 是否有获胜阵营（平局时没有）
function SettlementServer:HasSuccessCamp()
    if not next(self.tdmPlayerInfo) or not next(self.tdmData) then
        xxwwerror("SettlementServer:HasSuccessCamp fail!")
        return false
    end
    for _, v in pairs(self.tdmData.camp_list) do
        if v.is_winner == 1 then
            xxwwinfo("SettlementServer:HasSuccessCamp true")
            return true
        end
    end
    xxwwinfo("SettlementServer:HasSuccessCamp false")
    return false
end

function SettlementServer:GetTDMSettlementMVPTeamHeroId()
    if not next(self.tdmPlayerInfo) or not next(self.tdmData) then
        xxwwerror("SettlementServer:GetTDMSettlementMVPTeamHeroId fail!")
        return nil
    end
    local myPlayerId = Server.AccountServer:GetPlayerId()
    local MVPTeamHeroList = {}
    for _, v in pairs(self.tdmData.camp_list) do
        if v.is_winner == 1 then
            for _, tdmTeam in pairs(v.team_list) do
                if tdmTeam.team_rank == 1 then
                    xxwwinfo("SettlementServer:GetTDMSettlementMVPTeamHeroId MVPteam ", tdmTeam.team_id)
                    for _, player in ipairs(tdmTeam.player_list) do
                        if tostring(myPlayerId) == tostring(player.player_id) then
                            table.insert(MVPTeamHeroList,1, player.hero.hero_id)
                        else
                            table.insert(MVPTeamHeroList, player.hero.hero_id)
                        end
                    end
                end
            end
        end
    end
    if not next(MVPTeamHeroList) then
        xxwwerror("SettlementServer:GetTDMSettlementMVPTeamHeroId MVPTeamHeroList is nil!")
    end
    return MVPTeamHeroList
end

function SettlementServer:GetTDMSettlementMVPEquipInfo()
    if not next(self.tdmPlayerInfo) or not next(self.tdmData) then
        xxwwerror("SettlementServer:GetTDMSettlementMVPEquipInfo fail!")
        return nil
    end
    local MVPEquipList = {}
    --if next(self.tdmData.mvp_list) then
    --    for _, mvp in pairs(self.tdmData.mvp_list) do
    --        local EquipInfo = self.tdmPlayerInfo[mvp.player_id].props
    --        if EquipInfo then
    --            table.insert(MVPEquipList, EquipInfo)
    --        else
    --            xxwwerror("SettlementServer:GetTDMSettlementMVPEquipInfo EquipInfo is nil!")
    --        end
    --    end
    --end
    if not next(MVPEquipList) then
        xxwwerror("SettlementServer:GetTDMSettlementMVPEquipInfo MVPEquipList is nil!")
    end
    return MVPEquipList
end

function SettlementServer:GetTDMSettlementMVPTeamEquipInfo()
    if not next(self.tdmPlayerInfo) or not next(self.tdmData) then
        xxwwerror("SettlementServer:GetTDMSettlementMVPTeamEquipInfo fail!")
        return nil
    end
    local myPlayerId = Server.AccountServer:GetPlayerId()
    local MVPTeamEquipList = {}
    for _, v in pairs(self.tdmData.camp_list) do
        if v.is_winner == 1 then
            for _, tdmTeam in pairs(v.team_list) do
                if tdmTeam.team_rank == 1 then
                    xxwwinfo("SettlementServer:GetTDMSettlementMVPTeamEquipInfo MVPteam ", tdmTeam.team_id)
                    for _, player in ipairs(tdmTeam.player_list) do
                        if tostring(myPlayerId) == tostring(player.player_id) then
                            table.insert(MVPTeamEquipList,1,player.props)
                        else
                            table.insert(MVPTeamEquipList, player.props)
                        end
                    end
                end
            end
        end
    end
    if not next(MVPTeamEquipList) then
        xxwwerror("SettlementServer:GetTDMSettlementMVPEquipInfo MVPTeamEquipList is nil!")
    end
    return MVPTeamEquipList
end


function SettlementServer:GetTDMSettlementMVPHeroFashion()
    if not next(self.tdmPlayerInfo) or not next(self.tdmData) then
        xxwwerror("SettlementServer:GetTDMSettlementMVPHeroFashion fail!")
        return nil
    end
    local MVPFashionList = {}
    --if next(self.tdmData.mvp_list) then
    --    for _, mvp in pairs(self.tdmData.mvp_list) do
    --        local fashionInfo = self.tdmPlayerInfo[mvp.player_id].hero.fashion
    --        if fashionInfo then
    --            table.insert(MVPFashionList, fashionInfo)
    --        else
    --            xxwwerror("SettlementServer:GetTDMSettlementMVPHeroFashion MVPFashionList is nil!")
    --        end
    --    end
    --end
    if not next(MVPFashionList) then
        xxwwerror("SettlementServer:GetTDMSettlementMVPHeroFashion MVPFashionList is nil!")
    end
    return MVPFashionList
end

function SettlementServer:GetTDMSettlementMVPTeamHeroFashion()
    if not next(self.tdmPlayerInfo) or not next(self.tdmData) then
        xxwwerror("SettlementServer:GetTDMSettlementMVPTeamHeroFashion fail!")
        return nil
    end
    local myPlayerId = Server.AccountServer:GetPlayerId()
    local MVPTeamFashionList = {}
    for _, v in pairs(self.tdmData.camp_list) do
        if v.is_winner == 1 then
            for _, tdmTeam in pairs(v.team_list) do
                if tdmTeam.team_rank == 1 then
                    xxwwinfo("SettlementServer:GetTDMSettlementMVPTeamHeroFashion MVPteam ", tdmTeam.team_id)
                    for _, player in ipairs(tdmTeam.player_list) do
                        if tostring(myPlayerId) == tostring(player.player_id) then
                            table.insert(MVPTeamFashionList,1, player.hero.fashion)
                        else
                            table.insert(MVPTeamFashionList, player.hero.fashion)
                        end
                    end
                end
            end
        end
    end
    if not next(MVPTeamFashionList) then
        xxwwerror("SettlementServer:GetTDMSettlementMVPHeroFashion GetTDMSettlementMVPTeamHeroFashion is nil!")
    end
    return MVPTeamFashionList
end

function SettlementServer:GetTDMSettlementTeamMemberInfo()
    if not next(self.tdmPlayerTeamInfo) then
        xxwwerror("SettlementServer:GetTDMSettlementTeamInfo fail!")
        return nil
    end
    local myPlayerId = Server.AccountServer:GetPlayerId()
    --找到自己在哪个小队
    local myTeamID = -1
    for teamID, playerList in pairs(self.tdmPlayerTeamInfo) do
        for _, player in pairs(playerList) do
            if tostring(myPlayerId) == tostring(player.player_id) then
                myTeamID = teamID
                break
            end
        end
    end
    local TeamEquipList = {}
    local HeroidList = {}
    local HeroFashionList = {}
    local myTeamPlayerListInfo = self.tdmPlayerTeamInfo[myTeamID]
    --把自己放第一位
    local t1 = {}
    local t2 = {}
    for _, player in ipairs(myTeamPlayerListInfo) do
        if tostring(player.player_id) == tostring(myPlayerId) then
            table.insert(t1, player)
        else
            table.insert(t2, player)
        end
    end
    for _, v in ipairs(t2) do
        table.insert(t1, v)
    end
    myTeamPlayerListInfo = t1
    for _, player in ipairs(myTeamPlayerListInfo) do
        local HeroId = player.hero.hero_id
        if HeroId then
            table.insert(HeroidList, HeroId)
        else
            xxwwerror("SettlementServer:GetTDMSettlementTeamInfo HeroId is nil!")
        end
        local EquipInfo = player.props
        if EquipInfo then
            table.insert(TeamEquipList, EquipInfo)
        else
            xxwwerror("SettlementServer:GetTDMSettlementTeamEquipInfo EquipInfo is nil!")
        end
        local HeroFashion = player.hero.fashion
        if HeroFashion then
            table.insert(HeroFashionList, HeroFashion)
        else
            xxwwerror("SettlementServer:GetTDMSettlementTeamHeroId HeroFashion is nil!")
        end
    end
    if not next(TeamEquipList) then
        xxwwerror("SettlementServer:GetTDMSettlementTeamInfo TeamEquipList is nil!")
    end
    if not next(HeroidList) then
        xxwwerror("SettlementServer:GetTDMSettlementTeamInfo HeroidList is nil!")
    end
    if not next(HeroFashionList) then
        xxwwerror("SettlementServer:GetTDMSettlementTeamInfo HeroFashionList is nil!")
    end

    return TeamEquipList,HeroidList,HeroFashionList
end

function SettlementServer:GetTDMSettlementMyFashion()
    if not next(self.tdmPlayerTeamInfo) then
        xxwwerror("SettlementServer:GetTDMSettlementTeamHeroId fail!")
        return nil
    end
    local myPlayerId = Server.AccountServer:GetPlayerId()
    --找到自己在哪个小队
    local myTeamID = -1
    for teamID, playerList in pairs(self.tdmPlayerTeamInfo) do
        for _, player in pairs(playerList) do
            if tostring(myPlayerId) == tostring(player.player_id) then
                myTeamID = teamID
                break
            end
        end
    end
    local HeroFashionList = {}
    local myTeamPlayerListInfo = self.tdmPlayerTeamInfo[myTeamID]
    for _, player in ipairs(myTeamPlayerListInfo) do
        if tostring(player.player_id) == tostring(myPlayerId) then
            local HeroFashion = player.hero.fashion
            if HeroFashion then
                HeroFashionList = HeroFashion
            else
                xxwwerror("SettlementServer:GetTDMSettlementMyFashion HeroFashion is nil!")
            end
        end
    end
    if not next(HeroFashionList) then
        xxwwerror("SettlementServer:GetTDMSettlementMyFashion HeroFashionList is nil!")
    end
    return HeroFashionList
end

function SettlementServer:GetTDMSettlementTeamHeroFashion()
    if not next(self.tdmPlayerTeamInfo) then
        xxwwerror("SettlementServer:GetTDMSettlementTeamHeroId fail!")
        return nil
    end
    local myPlayerId = Server.AccountServer:GetPlayerId()
    --找到自己在哪个小队
    local myTeamID = -1
    for teamID, playerList in pairs(self.tdmPlayerTeamInfo) do
        for _, player in pairs(playerList) do
            if tostring(myPlayerId) == tostring(player.player_id) then
                myTeamID = teamID
                break
            end
        end
    end
    local HeroFashionList = {}
    local myTeamPlayerListInfo = self.tdmPlayerTeamInfo[myTeamID]
    --把自己放第一位
    local t1 = {}
    local t2 = {}
    for _, player in ipairs(myTeamPlayerListInfo) do
        if tostring(player.player_id) == tostring(myPlayerId) then
            table.insert(t1, player)
        else
            table.insert(t2, player)
        end
    end
    for _, v in ipairs(t2) do
        table.insert(t1, v)
    end
    myTeamPlayerListInfo = t1
    for _, player in ipairs(myTeamPlayerListInfo) do
        local HeroFashion = player.hero.fashion
        if HeroFashion then
            table.insert(HeroFashionList, HeroFashion)
        else
            xxwwerror("SettlementServer:GetTDMSettlementTeamHeroId HeroFashion is nil!")
        end
    end
    if not next(HeroFashionList) then
        xxwwerror("SettlementServer:GetTDMSettlementTeamHeroId HeroFashionList is nil!")
    end
    return HeroFashionList
end

function SettlementServer:GetTDMSettlementTeamHeroId()
    if not next(self.tdmPlayerTeamInfo) then
        xxwwerror("SettlementServer:GetTDMSettlementTeamHeroId fail!")
        return nil
    end
    local myPlayerId = Server.AccountServer:GetPlayerId()
    --找到自己在哪个小队
    local myTeamID = -1
    for teamID, playerList in pairs(self.tdmPlayerTeamInfo) do
        for _, player in pairs(playerList) do
            if tostring(myPlayerId) == tostring(player.player_id) then
                myTeamID = teamID
                break
            end
        end
    end
    local HeroidList = {}
    local myTeamPlayerListInfo = self.tdmPlayerTeamInfo[myTeamID]
    --把自己放第一位
    local t1 = {}
    local t2 = {}
    for _, player in ipairs(myTeamPlayerListInfo) do
        if tostring(player.player_id) == tostring(myPlayerId) then
            table.insert(t1, player)
        else
            table.insert(t2, player)
        end
    end
    for _, v in ipairs(t2) do
        table.insert(t1, v)
    end
    myTeamPlayerListInfo = t1
    for _, player in ipairs(myTeamPlayerListInfo) do
        local HeroId = player.hero.hero_id
        if HeroId then
            table.insert(HeroidList, HeroId)
        else
            xxwwerror("SettlementServer:GetTDMSettlementTeamHeroId HeroId is nil!")
        end
    end
    if not next(HeroidList) then
        xxwwerror("SettlementServer:GetTDMSettlementTeamHeroId HeroidList is nil!")
    end
    return HeroidList
end

function SettlementServer:GetTDMSettlementTeamEquipInfo()
    if not next(self.tdmPlayerTeamInfo) then
        xxwwerror("SettlementServer:GetTDMSettlementMVPEquipInfo fail!")
        return nil
    end
    local myPlayerId = Server.AccountServer:GetPlayerId()
    --找到自己在哪个小队
    local myTeamID = -1
    for teamID, playerList in pairs(self.tdmPlayerTeamInfo) do
        for _, player in pairs(playerList) do
            if tostring(myPlayerId) == tostring(player.player_id) then
                myTeamID = teamID
                break
            end
        end
    end
    local TeamEquipList = {}
    local myTeamPlayerListInfo = self.tdmPlayerTeamInfo[myTeamID]
    --把自己放第一位
    local t1 = {}
    local t2 = {}
    for _, player in ipairs(myTeamPlayerListInfo) do
        if tostring(player.player_id) == tostring(myPlayerId) then
            table.insert(t1, player)
        else
            table.insert(t2, player)
        end
    end
    for _, v in ipairs(t2) do
        table.insert(t1, v)
    end
    myTeamPlayerListInfo = t1
    for _, player in ipairs(myTeamPlayerListInfo) do
        local EquipInfo = player.props
        if EquipInfo then
            table.insert(TeamEquipList, EquipInfo)
        else
            xxwwerror("SettlementServer:GetTDMSettlementTeamEquipInfo EquipInfo is nil!")
        end
    end
    if not next(TeamEquipList) then
        xxwwerror("SettlementServer:GetTDMSettlementMVPEquipInfo TeamEquipList is nil!")
    end
    return TeamEquipList
end

function SettlementServer:GetTDMSettlementShowOrder()
    --logtable(self.showOrder, true)
    if next(self.showOrder) then
        return self.showOrder
    end
    xxwwerror("SettlementServer:GetTDMSettlementShowOrder fail!")
    return nil
end

function SettlementServer:GetTDMWeaponChangeInfo()
    if next(self.weaponChange) then
        return self.weaponChange
    end
    xxwwerror("SettlementServer:GetTDMWeaponChangeInfo fail!")
    return nil
end

-- note: 请求Raid结算包
function SettlementServer:ReqForRaidSettlementInfo(roomId)
    xxwwinfo("SettlementServer:ReqForRaidSettlementInfo")
    local req = pb.CSGetRaidSettlementInfoReq:New()
    if roomId then
        req.room_id = roomId
    else
        req.room_id = Server.MatchServer:GetDsRoomId()
    end
    req:Request(function(res)
        self:Save2File(res)
        if res.result == 0 then
            --logtable(res, true)
            self._settlementInfoSource = SettlementDefine.ESettlementInfoSource.Raid
            if self:ParseRaidSettlementInfo(res) then
                Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.IngameSoundOff)
                self.Events.evtRaidSettlementDo:Invoke()
            end
        else
            xxwwerror("SettlementServer:ReqForRaidSettlementInfo error res.result ==", res.result)
            self.Events.evtOnReqForSettlementInfoFailed:Invoke(ESettlementNtf.SNTF_RAID)
        end
    end)
end

-- note: 解析Raid结算包
function SettlementServer:ParseRaidSettlementInfo(res)
    xxwwinfo("SettlementServer:ParseRaidSettlementInfo")
    -- azhengzheng:缓存Raid结算信息
    self._raidSettlementInfo = res
    self:_ResetRaidSettlementTeamInfoList()

    self.raidData = res.raid_data
    self.raidSettlementNtf = res.settlement_ntf
    self.raidPlayerInfo = {}
    for _, player in pairs(self.raidData.player_array) do
        self.raidPlayerInfo[player.player_id] = player
    end
    if next(self.raidData) and next(self.raidPlayerInfo) then
        return true
    end
    return false
end

function SettlementServer:GetRaidSettlementInfo()
    return self._raidSettlementInfo
end

function SettlementServer:_ResetRaidSettlementTeamInfoList()
    self._raidSettlementTeamInfoList = {}

    if not self._raidSettlementInfo then
        return
    end

    self:_SetRaidData()
    self:_SetRaidSettlementNtf()
    self:_SortRaidSettlementTeamInfoList()
    self:_SetRaidSettlementTeamInfoMVP()
    self:_InitRaidEvaluation()
    self:_SetRaidMapNameAndDifficulty()
end

function SettlementServer:_SetRaidData()
    if not self._raidSettlementInfo.raid_data then
        return
    end

    self:_SetRaidPlayerArray()
end

function SettlementServer:_SetRaidPlayerArray()
    if not self._raidSettlementInfo.raid_data.player_array then
        return
    end

    local myPlayerId = Server.AccountServer:GetPlayerId()

    for _, value in pairs(self._raidSettlementInfo.raid_data.player_array) do
        local raidSettlementTeamInfoRow = {}
        raidSettlementTeamInfoRow.playerId = value.player_id
        -- raidSettlementTeamInfoRow.raidScore = value.raid_score
        raidSettlementTeamInfoRow.raidLevel = value.raid_level
        -- raidSettlementTeamInfoRow.killNum = value.kill_num

        if myPlayerId == value.player_id then
            raidSettlementTeamInfoRow.mySelf = true
        else
            raidSettlementTeamInfoRow.isFriend = Server.FriendServer:CheckIsFriend(value.player_id)
        end

        table.insert(self._raidSettlementTeamInfoList, raidSettlementTeamInfoRow)
    end
end

function SettlementServer:_SetRaidSettlementNtf()
    if not self._raidSettlementInfo.settlement_ntf then
        return
    end

    self:_SetRaidMatchInfo()
    self:_SetRaidStatusArray()
    self:_SetRaidPlayerStatusArray()
end

function SettlementServer:_SetRaidMatchInfo()
    if not self._raidSettlementInfo.settlement_ntf.match_info then
        return
    end

    for _, value in pairs(self._raidSettlementTeamInfoList) do
        value.mapID = self._raidSettlementInfo.settlement_ntf.match_info.mode_info.map_id
        value.roomID = self._raidSettlementInfo.settlement_ntf.match_info.room_id
        value.startTime = self._raidSettlementInfo.settlement_ntf.match_info.start_time
    end
end

function SettlementServer:_SetRaidStatusArray()
    if not self._raidSettlementInfo.settlement_ntf.status_array then
        return
    end

    for _, status in pairs(self._raidSettlementInfo.settlement_ntf.status_array) do
        for _, raidSettlementTeamInfo in pairs(self._raidSettlementTeamInfoList) do
            if status.basic_info.player_id == raidSettlementTeamInfo.playerId then
                raidSettlementTeamInfo.gameNick = status.basic_info.game_nick

                if status.playing.hero.hero_id ~= 0 then
                    raidSettlementTeamInfo.heroID = status.playing.hero.hero_id
                    raidSettlementTeamInfo.accessories = status.playing.hero.accessories
                    raidSettlementTeamInfo.carryInPropsPrice = status.playing.carry_in_props_price
                    raidSettlementTeamInfo.costPrice = status.playing.cost_price
                elseif status.end_game.hero.hero_id ~= 0 then
                    raidSettlementTeamInfo.heroID = status.end_game.hero.hero_id
                    raidSettlementTeamInfo.accessories = status.end_game.hero.accessories
                    raidSettlementTeamInfo.carryInPropsPrice = status.end_game.carry_in_props_price
                    raidSettlementTeamInfo.costPrice = status.end_game.cost_price
                else
                    raidSettlementTeamInfo.heroID = status.quit_game.hero.hero_id
                    raidSettlementTeamInfo.accessories = status.quit_game.hero.accessories
                    raidSettlementTeamInfo.carryInPropsPrice = status.quit_game.carry_in_props_price
                    raidSettlementTeamInfo.costPrice = status.quit_game.cost_price
                end

                break
            end
        end
    end
end

function SettlementServer:_SetRaidPlayerStatusArray()
    if not self._raidSettlementInfo.settlement_ntf.raid_player_status_array then
        return
    end

    for _, raidPlayerStatus in pairs(self._raidSettlementInfo.settlement_ntf.raid_player_status_array) do
        for _, raidSettlementTeamInfo in pairs(self._raidSettlementTeamInfoList) do
            if raidPlayerStatus.player_id == raidSettlementTeamInfo.playerId then
                raidSettlementTeamInfo.damageToVehicles = raidPlayerStatus.damage_to_vehicles
                raidSettlementTeamInfo.deathCount = raidPlayerStatus.death_count
                raidSettlementTeamInfo.healValue = raidPlayerStatus.heal_value
                raidSettlementTeamInfo.totalDamage = raidPlayerStatus.total_damage or 0
                raidSettlementTeamInfo.rescueCount = raidPlayerStatus.rescue_count
                raidSettlementTeamInfo.killEnemyList = raidPlayerStatus.kill_enemy_list
                raidSettlementTeamInfo.killNum = raidPlayerStatus.total_kill_count
                raidSettlementTeamInfo.difficulty = raidPlayerStatus.difficulty
                raidSettlementTeamInfo.raidScore = raidPlayerStatus.raid_score or 0
                break
            end
        end
    end
end

function SettlementServer:_SortRaidSettlementTeamInfoList()
    table.sort
    (
        self._raidSettlementTeamInfoList,
        function(teamInfoA, teamInfoB)
            if teamInfoA.raidScore ~= teamInfoB.raidScore then
                return teamInfoA.raidScore > teamInfoB.raidScore
            end

            return teamInfoA.playerId < teamInfoB.playerId
        end
    )
end

function SettlementServer:_SetRaidSettlementTeamInfoMVP()
    if not self:GetRaidTaskSucc() or #self._raidSettlementTeamInfoList == 0 then
        return
    end

    self._raidSettlementTeamInfoList[1].isMVP = true
end

function SettlementServer:_InitRaidEvaluation()
    local raidEvaluation = Facade.TableManager:GetTable("RaidEvaluation")

    if not raidEvaluation then
        return
    end

    local raidEvaluationList = {}

    for _, value in pairs(raidEvaluation) do
        table.insert
        (
            raidEvaluationList,
            {
                evaluateName = value.EvaluateName,
                paramID = value.ParamID,
                minValue = value.MinValue,
                evaluateWeight = value.EvaluateWeight,
                evaluateType = value.EvaluateType
            }
        )
    end

    table.sort(
        raidEvaluationList,
        function(a, b)
            return a.evaluateWeight > b.evaluateWeight
        end
    )

    local remainEvaluate = #self._raidSettlementTeamInfoList

    for _, raidEvaluationItem in pairs(raidEvaluationList) do
        local idx, value = nil, nil

        for key, raidSettlementTeamInfoItem in pairs(self._raidSettlementTeamInfoList) do
            if raidEvaluationItem.paramID == 1 then
                if idx then
                    if value < (raidSettlementTeamInfoItem.totalDamage or 0) then
                        idx, value = key, raidSettlementTeamInfoItem.totalDamage or 0
                    end
                else
                    idx, value = key, raidSettlementTeamInfoItem.totalDamage or 0
                end
            elseif raidEvaluationItem.paramID == 2 then
                if idx then
                    if value > (raidSettlementTeamInfoItem.deathCount or 0) then
                        idx, value = key, raidSettlementTeamInfoItem.deathCount or 0
                    end
                else
                    idx, value = key, raidSettlementTeamInfoItem.deathCount or 0
                end
            elseif raidEvaluationItem.paramID == 3 then
                if idx then
                    if value < (raidSettlementTeamInfoItem.rescueCount or 0) then
                        idx, value = key, raidSettlementTeamInfoItem.rescueCount or 0
                    end
                else
                    idx, value = key, raidSettlementTeamInfoItem.rescueCount or 0
                end
            elseif raidEvaluationItem.paramID == 4 then
                if idx then
                    if value < (raidSettlementTeamInfoItem.carryInPropsPrice or 0) then
                        idx, value = key, raidSettlementTeamInfoItem.carryInPropsPrice or 0
                    end
                else
                    idx, value = key, raidSettlementTeamInfoItem.carryInPropsPrice or 0
                end
            elseif raidEvaluationItem.paramID == 5 then
                if idx then
                    if value > (raidSettlementTeamInfoItem.costPrice or 0) then
                        idx, value = key, raidSettlementTeamInfoItem.costPrice or 0
                    end
                else
                    idx, value = key, raidSettlementTeamInfoItem.costPrice or 0
                end
            elseif raidEvaluationItem.paramID == 6 then
                if idx then
                    if value < (raidSettlementTeamInfoItem.killNum or 0) then
                        idx, value = key, raidSettlementTeamInfoItem.killNum or 0
                    end
                else
                    idx, value = key, raidSettlementTeamInfoItem.killNum or 0
                end
            end
        end

        if idx and (not self._raidSettlementTeamInfoList[idx].evaluateName) then
            if raidEvaluationItem.paramID == 1 or raidEvaluationItem.paramID == 3 or raidEvaluationItem.paramID == 4 or raidEvaluationItem.paramID == 6 then
                if raidEvaluationItem.minValue <= value then
                    remainEvaluate = remainEvaluate - 1
                    self._raidSettlementTeamInfoList[idx].evaluateName = raidEvaluationItem.evaluateName
                    self._raidSettlementTeamInfoList[idx].evaluateType = raidEvaluationItem.evaluateType
                end
            elseif raidEvaluationItem.paramID == 2 or raidEvaluationItem.paramID == 5 then
                if value <= raidEvaluationItem.minValue then
                    remainEvaluate = remainEvaluate - 1
                    self._raidSettlementTeamInfoList[idx].evaluateName = raidEvaluationItem.evaluateName
                    self._raidSettlementTeamInfoList[idx].evaluateType = raidEvaluationItem.evaluateType
                end
            end
        end

        if remainEvaluate == 0 then
            return
        end
    end
end

function SettlementServer:_SetRaidMapNameAndDifficulty()
    self._raidMapDifficulty = nil
    local mapID = self._raidSettlementTeamInfoList and self._raidSettlementTeamInfoList[1] and self._raidSettlementTeamInfoList[1].mapID
    self._raidMapName = self:_GetMapNameByMapID(mapID)
    local difficulty = self._raidSettlementTeamInfoList and self._raidSettlementTeamInfoList[1] and self._raidSettlementTeamInfoList[1].difficulty
    local raidSettlementLong = Facade.TableManager:GetTable("RaidSettlementLong")

    if not mapID or not difficulty or not raidSettlementLong then
        return
    end

    for _, value in pairs(raidSettlementLong) do
        if mapID == value.MapID and difficulty == value.Difficulty then
            self._raidMapDifficulty = value.DifficultyDesc
            break
        end
    end
end

function SettlementServer:GetRaidMapNameAndDifficulty()
    return self._raidMapName, self._raidMapDifficulty
end

function SettlementServer:GetMySelfRaidInfo()
    for _, value in pairs(self._raidSettlementTeamInfoList) do
        if value.mySelf then
            return value
        end
    end

    return nil
end

function SettlementServer:GetRaidTaskSucc()
    return self._raidSettlementInfo and self._raidSettlementInfo.settlement_ntf and self._raidSettlementInfo.settlement_ntf.raid_task_succ
end

function SettlementServer:GetRaidTeamInfoByIdx(idx, bFromRaidTeamInfo)
    if bFromRaidTeamInfo and #self._raidSettlementTeamInfoList == 2 then
        if #self._raidSettlementTeamInfoList == 2 then
            if idx == 1 then
                return self._raidSettlementTeamInfoList[2]
            end

            if idx == 2 then
                return self._raidSettlementTeamInfoList[1]
            end
        end
    end

    return self._raidSettlementTeamInfoList[idx]
end

function SettlementServer:GetRaidTeamDeathCount()
    local raidTeamDeathCount = 0

    for _, value in pairs(self._raidSettlementTeamInfoList) do
        raidTeamDeathCount = raidTeamDeathCount + (value.deathCount or 0)
    end

    return raidTeamDeathCount
end

function SettlementServer:GetRaidTeamCostCount()
    local raidTeamCostCount = 0

    for _, value in pairs(self._raidSettlementTeamInfoList) do
        raidTeamCostCount = raidTeamCostCount + (value.costPrice or 0)
    end

    return raidTeamCostCount
end

function SettlementServer:GetRaidTeamCarryInPropsPriceCount()
    local raidTeamCarryInPropsPriceCount = 0

    for _, value in pairs(self._raidSettlementTeamInfoList) do
        raidTeamCarryInPropsPriceCount = raidTeamCarryInPropsPriceCount + (value.carryInPropsPrice or 0)
    end

    return raidTeamCarryInPropsPriceCount
end

function SettlementServer:GetRaidSettlementNtfInfo()
    if next(self.raidSettlementNtf) then
        return self.raidSettlementNtf
    end
    xxwwerror("SettlementServer:GetRaidSettlementNtfInfo fail!")
    return nil
end

function SettlementServer:GetRaidDataInfo()
    if next(self.raidData) then
        return self.raidData
    end
    xxwwerror("SettlementServer:GetRaidDataInfo fail!")
    return nil
end

function SettlementServer:GetRaidPlayerInfo()
    if next(self.raidPlayerInfo) then
        return self.raidPlayerInfo
    end
    xxwwerror("SettlementServer:GetRaidPlayerInfo fail!")
    return nil
end

-- note: 获取Raid玩家raidPlayerStatusMap
function SettlementServer:GetRaidPlayerStatusMap()
    xxwwinfo("SettlementServer:GetRaidPlayerStatusMap")
    return self.raidPlayerStatusMap
end

-- note: 获取Raid成功与否
function SettlementServer:GetRaidSettleOverState()
    xxwwinfo("SettlementServer:GetRaidSettleOverState")
    return self.dsInfo.raid_task_succ
end

-- note: 获取通关Id
function SettlementServer:GetRaidLevelId()
    if self.dsInfo then
        if self.dsInfo.match_info then
            if self.dsInfo.match_info.mode_info then
                if self.dsInfo.match_info.mode_info.raid_id then
                    local raidId = self.dsInfo.match_info.mode_info.raid_id
                    xxwwinfo("SettlementServer:GetRaidLevelId_", raidId)
                    return raidId
                end
            end
        end
    end
end

-- note: 获取通关名称
function SettlementServer:GetRaidLevelName()
    local raidId = self:GetRaidLevelId()
    local IrisRaidEntityTable = Facade.TableManager:GetTable("RaidEntityTable") or {}
    local raidName = ""
    for id, info in pairs(IrisRaidEntityTable) do
        if info.ID == raidId then
            raidName = info.Name
            xxwwinfo("SettlementServer:GetRaidLevelName_", raidName)
            return raidName
        end
    end
    xxwwinfo("no SettlementServer:GetRaidLevelName_", raidName)
    return ""
end

-- note: 获取通关时间 -- return second
function SettlementServer:GetRaidGameUsedTime()
    xxwwinfo("SettlementServer:GetRaidGameUsedTime")
    return self.raidData.play_time and self.raidData.play_time or 0
end

-- note: 获取击杀数量
function SettlementServer:GetRaidKillNum()
    xxwwinfo("SettlementServer:GetRaidKillNum")
    local playerId = Server.AccountServer:GetPlayerId()
    return self:GetTeammateKillNumFromPlayerId(playerId) or 0
end

-- note: 获取通关命数
function SettlementServer:GetRaidUsedLife()
    xxwwinfo("SettlementServer:GetRaidUsedLife")
    local playerId = Server.AccountServer:GetPlayerId()
    return self:GetTeammateUsedLifeFromPlayerId(playerId)
end

-- note: 获取本局分数
function SettlementServer:GetRaidGameScore()
    xxwwinfo("SettlementServer:GetGameScore")
    local playerId = Server.AccountServer:GetPlayerId()
    local info = self.playerMap[playerId]
    return info and info.raid_score or 0
end

-- note: 获取整体评价 -- return EEvaluateLevel
function SettlementServer:GetRaidEvaluateLevel()
    xxwwinfo("SettlementServer:GetRaidEvaluateLevel")
    local playerId = Server.AccountServer:GetPlayerId()
    local info = self.playerMap[playerId]
    return info and info.raid_level -- EEvaluateLevel.SLevel
end

-- //note: 获取所有队友信息
-- function SettlementServer:GetTeammateInfo()
--     xxwwinfo("SettlementServer:GetTeammateInfo")
--     return self.raidTeamInfo
-- end


-- note: 获取队友的信息
function SettlementServer:GetTeammateInfoByPlayerId(playerId)
    xxwwinfo("SettlementServer:GetTeammateInfoByPlayerId")
    local info = self.statusMap[playerId]
    return info and info.basic_info
end

-- note: 队友头像url
function SettlementServer:GetTeammateHeadUrlFromPlayerId(playerId)
    xxwwinfo("SettlementServer:GetTeammateHeadUrlFromPlayerId")
    local info = self:GetTeammateInfoByPlayerId(playerId)
    return info and info.avatar or ""
end

-- note: 队友名字 -- input:TeammateInfo
function SettlementServer:GetTeammateNameFromPlayerId(playerId)
    xxwwinfo("SettlementServer:GetTeammateNameFromPlayerId")
    local info = self:GetTeammateInfoByPlayerId(playerId)
    return info and info.game_nick
end

-- note: 队友击杀敌人数量
function SettlementServer:GetTeammateKillNumFromPlayerId(playerId)
    xxwwinfo("SettlementServer:GetTeammateNameFromPlayerId")
    local info = self.raidPlayerStatusMap[playerId]
    local killNum = info.kill_num or 0
    return killNum
end

-- note: 队友消耗命数
function SettlementServer:GetTeammateUsedLifeFromPlayerId(playerId)
    xxwwinfo("SettlementServer:GetTeammateNameFromPlayerId")
    local info = self.raidPlayerStatusMap[playerId]
    return info and info.passed_life or 0
end

-- note: 队友KD
function SettlementServer:GetTeammateKDFromPlayerId(playerId)
    xxwwinfo("SettlementServer:GetTeammateNameFromPlayerId")
    local info = self.playerMap[playerId]
    return info and info.raid_kd or 0.0
end

--异常结算回放
function SettlementServer:ExceptionSettlementReq()
    xxwwwarning("SettlementServer:ExceptionSettlementReq")
    local req = pb.CSSettlementGetHasUnsettledMatchReq:New()
    req:Request(function(res)
        local bNoExceptionSettlement = true
        if res.result == 0 then
            if res.has_match then
                if res.mode == ESettlementNtf.SNTF_RAID then
                    --Raid不展示
                    --Server.SettlementServer:ReqForRaidSettlementInfo(res.room_id)
                    xxwwwarning("SettlementServer:ExceptionSettlementReq Raid has_match is true")
                elseif res.mode == ESettlementNtf.SNTF_TDM then
                    --大战场目前只有局内结算，和策划对的是先不展示
                    --self:ReqForTDMSettlementInfo(res.room_id)
                    xxwwwarning("SettlementServer:ExceptionSettlementReq Mp has_match is true")
                elseif res.mode == ESettlementNtf.SNTF_SOL then
                    self.Events.evtExceptionSettlementDo:Invoke(res)
                    bNoExceptionSettlement = false
                    xxwwwarning("SettlementServer:ExceptionSettlementReq SOL has_match is true")
                end
            else
                xxwwwarning("SettlementServer:ExceptionSettlementReq has_match is false")
            end
        else
            xxwwwarning("SettlementServer:ExceptionSettlementReq error res.result ==", res.result)
        end
        -- 没有异常结算
        if bNoExceptionSettlement then
            self.Events.evtNoExceptionSettlement:Invoke()
        end
    end)
end

function SettlementServer:Save2File(pkg)
    -- 屏蔽无用且会导致Lua报错的代码
    ---- SHIPPING包或者性能采集的时候，不执行
    --if not VersionUtil.IsShipping() and not IsCollectingPerformanceData() and VersionUtil.Data.buildConfiguration ~= "Test"then
    --    -- 加trycall避免报错时阻塞结算
    --    trycall(function()
    --        settlementMgrIns:SaveSettlementPkg2Locals(json.encode(pkg))
    --    end)
    --end
end

function SettlementServer:_GetMapNameByMapID(mapID)
    if not mapID then
        return "-"
    end

    local matchModeDataConfig = Facade.TableManager:GetTable("MatchModeDataConfig")

    if not matchModeDataConfig then
        return "--"
    end

    for _, value in pairs(matchModeDataConfig) do
        if mapID == value.MapID then
            return value.MapName or "---"
        end
    end

    return "----"
end

function SettlementServer:GetSettlementInfoSource()
    return self._settlementInfoSource
end

function SettlementServer:ResetSettlementInfoSource()
    self._settlementInfoSource = SettlementDefine.ESettlementInfoSource.None
end

function SettlementServer:SetSOLEliminationReplayStartTime(time)
    self._solEliminationReplayStartTime = time
    return self._solEliminationReplayStartTime
end

function SettlementServer:GetSOLEliminationReplayStartTime()
    return self._solEliminationReplayStartTime or self:SetSOLEliminationReplayStartTime(TimeUtil.GetCurrentTime() + 60)
end

--- BEGIN MODIFICATION @ VIRTUOS: 实现Competitive Activity
function SettlementServer:HandlePS5CompetitiveActivity()
    local DFMPlatformActivityManager = UDFMPlatformActivityManager.Get(GetWorld())
    if not IsPS5Family() or not DFMPlatformActivityManager then
        return
    end
    local platformMatchInfo = FPlatformMatchInfo()
    local members = platformMatchInfo.Members
    local matchResult = -1
    
    DFMPlatformActivityManager:SetActivityMatchResult(matchResult)
    for _, tdmCamp in pairs(self.tdmData.camp_list) do
        if tdmCamp.is_winner then
            DFMPlatformActivityManager:SetActivityMatchResult(tdmCamp.color-1)
        end
        for _, tdmTeam in pairs(tdmCamp.team_list) do
            for _, tdmPlayer in pairs(tdmTeam.player_list) do
                local ps5MatchMemberInfo = FPlatformMemberInfo()
                ps5MatchMemberInfo.TeamId = tdmCamp.color-1
                ps5MatchMemberInfo.PlayerId = tdmPlayer.player_id
                if self:GetTDMSettlementIsAi(tdmPlayer.player_id) then
                    ps5MatchMemberInfo.PlayerName = tdmPlayer.ai_info.game_nick
                else
                    ps5MatchMemberInfo.PlayerName = tdmPlayer.basic_info.game_nick
                end
                --plat_id == 108，为ps5平台
                if Module.Settlement:GetPlatIDByPlayerInfo(tdmPlayer) == PlatIDType.Plat_Playstation then
                    ps5MatchMemberInfo.bIsPS5Player = true
                end
                members:Add(ps5MatchMemberInfo)
            end
        end
    end
    
    platformMatchInfo.Members = members

    for index, value in ipairs(platformMatchInfo.Members) do
        if value.bIsPS5Player == true  then
            --只有第一个ps5玩家需要创建和更新match
            if platformMatchInfo.Members ~= nil then
                --刷新match数据      
                DFMPlatformActivityManager:SetActivityMatchState(2)
                DFMPlatformActivityManager:UpdateMatchState(platformMatchInfo)    
            end
            break
        end
    end   
   
end

function SettlementServer:HandleLeaveMatchForPS5Activity()
    if not IsPS5Family() or not next(self.tdmPlayerInfo) then
        return
    end
    local DFMPlatformActivityManager = UDFMPlatformActivityManager.Get(GetWorld())
    if not DFMPlatformActivityManager or not self.tdmPlayerInfo[Server.AccountServer:GetPlayerId()] then
        return
    end
    if self.tdmPlayerInfo[Server.AccountServer:GetPlayerId()].leave == true then
        Server.MatchServer:OnLeaveMatchForActivity()
    end
    
end
--- END MODIFICATION

function SettlementServer:GetSOLMapID()
    local settlementInfo = self:GetSettlementInfo()
    return settlementInfo and settlementInfo.match_info and settlementInfo.match_info.mode_info and settlementInfo.match_info.mode_info.map_id
end

function SettlementServer:ClearSettlementInfo()
    self._mpSettlementInfo = nil
end

-----------------------------------------------------------------------
--region 点赞
function SettlementServer:OnPlayerInfoCommanderBePraisedNtf(res)
    if res then
        self.Events.evtPlayerInfoBePraisedNtf:Invoke(res.player_id, res.be_praised_num)
    end
end

function SettlementServer:PlayerCommanderModePraiseReq(bePraisedPlayerId, delayCall)
    local callback = function(res)
        if res.result == 0 then
            if delayCall then
                delayCall()
            end
        end
    end

    local req = pb.CSPlayerCommanderModePraiseReq:New()
    req.praise_player_id = Server.AccountServer:GetPlayerId()
    req.be_praised_player_id = bePraisedPlayerId
    req.room_id = self.tdmMatchInfo.room_id
    req.commander_player_id = self:GetCommanderPlayerId()
    req.all_player_id = self:GetCommanderAllPlayerIDNotAi()
    req.match_info = self.tdmMatchInfo

    self.Events.evtPlayerInfoPraisedClick:Invoke(Server.AccountServer:GetPlayerId(), bePraisedPlayerId)

    req:Request(callback)
end
function SettlementServer:GetCommanderPlayerId()
    local playerId = 0
    if next(self.tdmPlayerInfo) then
        for key, value in pairs(self.tdmPlayerInfo) do
            if value.commander_contributor_title == EOutStandingContributionType.ECommander then
                playerId = value.player_id
            end
        end
    end
    return playerId
end

function SettlementServer:GetCommanderAllPlayerIDNotAi()
    local list = {}
    if next(self.tdmPlayerInfo) then
        for key, value in pairs(self.tdmPlayerInfo) do
            if value.basic_info then
                if value.basic_info.game_nick ~= "" then
                    table.insert(list, value.player_id)
                end
            end
        end
    end
    return list
end
--endregion
-----------------------------------------------------------------------

return SettlementServer
