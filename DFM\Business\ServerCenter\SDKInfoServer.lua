----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaSSDKInfo)
----- LOG FUNCTION AUTO GENERATE END -----------



local SDKInfoServer = class("SDKInfoServer", require("DFM.YxFramework.Managers.Server.ServerBase"))
local UAppSetting = import "AppSetting"
local OneSDKModule = import "DFMGameOneSDK"
local UGameplayStatics = import "GameplayStatics"
local EGameBuildConfiguration = import "EGameBuildConfiguration"
local EServerType = import "EServerType"
local UGameVersionUtils = import "GameVersionUtils"

local UWeGameManager = nil
local WeGameManager = nil
if PLATFORM_WINDOWS then
    UWeGameManager = import "WeGameSDKManager"
    WeGameManager = UWeGameManager.Get(GetGameInstance())
end
-- SDKInfoServer 是用于存储建立连接所需要的SDK获得的openId/ChannelId等数据

local function log(...)
    loginfo("[SDKInfoServer]", ...)
end

local EPropertyClass = import "EPropertyClass"
local FConnectServerInfo = import "ConnectServerInfo"
function SDKInfoServer:Ctor()
    self.Events = {
        evtPlayerServerNameChanged = LuaEvent:NewIns("SDKInfoServer.evtPlayerServerNameChanged"),
        evtServerAddrSelect = LuaEvent:NewIns("SDKInfoServer.evtServerAddrSelect"),
        evtKeyNode = LuaEvent:NewIns("SDKInfoServer.evtKeyNode"),
        evtServerHotUpdateInfoChanged = LuaEvent:NewIns("SDKInfoServer.evtServerHotUpdateInfoChanged"),
        evtServerAddrNotfound = LuaEvent:NewIns("SDKInfoServer.evtServerAddrNotfound")
    }
    self._playerAdultState = -1
    self.netbarInfo = {}
    self:AddLuaEvent(Facade.ProtoManager.Events.evtOnStartReconnected, self._OnStartReconnected, self)
    self:AddLuaEvent(Facade.ProtoManager.Events.evtOnDnsAsyncResloved, self._OnDnsAsyncResloved, self)
    self:InitDataBeforLogin()
end

function SDKInfoServer:GetDefaultServerAddrKey()
    local defaultServerAddrKey = "daily"
    if not VersionUtil.IsShipping() then
        defaultServerAddrKey= Facade.ConfigManager:GetString("LastServerKey", "")
        if defaultServerAddrKey and defaultServerAddrKey ~= "" then
            logerror("SDKInfoServer:GetDefaultServerAddrKey defaultServerAddrKey = ",defaultServerAddrKey)
            return defaultServerAddrKey
        end
    end
    local platFormName = UGameplayStatics.GetPlatformName()
    -- key 可以直接用GetGameBuildConfiguration @yili
    local envKey = VersionUtil.GetGameBuildConfiguration()
    if envKey == EGameBuildConfiguration.Default  or envKey == EGameBuildConfiguration.Test then
        envKey = EGameBuildConfiguration.Daily
    end
    local serverAddrListAll = self:GetServerAddrList()
    -- 最后一个环境设置的地址
    for keyi,addr in pairs(serverAddrListAll) do
        if addr.TargetBuildConfiguration == envKey then -- TODO: 字段还未确认
            defaultServerAddrKey = addr.KeyName
        end
    end

    -- She2 Console临时修改，默认情况下使用海外She2 Daily服
    if IsConsole() and not VersionUtil.IsShipping() then
        for keyi,addr in pairs(serverAddrListAll) do
            if addr.KeyName == "zone_304" then -- Console海外默认使用海外She2 Daily 304服
                defaultServerAddrKey = addr.KeyName
            end
        end
    end
    logerror("SDKInfoServer:GetDefaultServerAddrKey last key = ",defaultServerAddrKey)
    return defaultServerAddrKey
end

function SDKInfoServer:GetServerGroupList()
    if self.serverGroupList then
        return self.serverGroupList
    end
    self.serverGroupList = {}
    local serverGroupCount = {}
    local serverGroupsNum = 0
    local serverAddrs = self:GetServerAddrList()
    for keyi, value in pairs(serverAddrs) do
        if value then
            local groupName = tostring(value.groupName)
            if serverGroupCount[groupName] == nil then
                serverGroupCount[groupName] = 1
                serverGroupsNum = serverGroupsNum + 1;
                table.insert(self.serverGroupList, 
                    {
                        name = groupName,
                        order = serverGroupsNum
                    }
                )
            else
                serverGroupCount[groupName] = serverGroupCount[groupName]  + 1
            end
        end
    end
    local function fSortGroup(a, b)
        if a and b then
            return a.order < b.order
        end
    end
    table.sort(self.serverGroupList, fSortGroup)
    return self.serverGroupList
end

function SDKInfoServer:GetServerAddrList()
    local playerInfo = self:GetGamePlayerInfo()
    -- if self.serverAddrList then
    --     return self.serverAddrList
    -- end

    local function fSortAddr(a, b)
        if a and b then
            return tostring(a.displayName) < tostring(b.displayName)
        end
    end
    if playerInfo then
        --serverAddrList = playerInfo:GetServerAddrList()
        self.serverAddrList = {}
        local serverAddrListOriginal
        if VersionUtil.IsRelease() then
            serverAddrListOriginal = Facade.TableManager:GetTable("ServerAddrRelease")
        else
            serverAddrListOriginal =  Facade.TableManager:GetTable("ServerAddr")
        end

        local isNoEditor = not IsInEditor()
        for key,addr in pairs(serverAddrListOriginal) do
            local useThisAddr = true
            -- 隐藏编辑器专有
            if useThisAddr and isNoEditor and addr.bEditorOnly then
                useThisAddr = false
            end

            -- 隐藏TargetBuildConfiguration不为shipping的项
            if useThisAddr and VersionUtil.IsShipping() and (addr.TargetBuildConfiguration ~= VersionUtil.GetGameBuildConfiguration()) then
                useThisAddr = false
            end

            --PC包排除编辑器专用的干扰，保留所有服务器地址
            if not (isNoEditor and addr.bEditorOnly) then
                if PLATFORM_WINDOWS then
                    useThisAddr = true
                end
            end

            if useThisAddr then
                addr.KeyName = key
                table.insert(self.serverAddrList, addr)
            end
        end
        table.sort(self.serverAddrList, fSortAddr)
        --playerInfo:SetServerAddrList(serverAddrList)
    end
    return self.serverAddrList or {}
end

function SDKInfoServer:SetMapleServerUrl(retUrl)
    log("SDKInfoServer:SetMapleServerUrl",retUrl)
    local mapleServerUrl = retUrl
    local serverAddrKey = "customize"
    self:SetSelectAddrKey(serverAddrKey,mapleServerUrl)
end

function SDKInfoServer:InitDataBeforLogin()
    -- 通过启动参数传的
    local url = UGameVersionUtils.GetLauncherParamsByKey("ip")
    if url ~= nil and url ~= "" then
        local ip , port = string.match(url, "(.*):(.*)")
        if ip ~= nil and ip ~= "" and port ~= nil and port ~= "" then
            local serverAddr = {ip = ip, port = port, Urls = {url}}
            self:SetSelectAddrKey("customize",url)
            return
        end
    end  
    
    self._gvoiceURL = ""
    
    local openId = Facade.ConfigManager:GetString("lastOpenId", "")
    self:SetOpenId(openId)

    if IsWeGameEnabled() then
        -- 避免结算时大区信息被覆盖
        -- local LauncherServerName = Facade.ConfigManager:GetString("LastLauncherServerName", "")
        -- local LauncherAddr = Facade.ConfigManager:GetString("customizeIpPort", "")
        -- local tmpIpPort = string.split(LauncherAddr, ':')
        -- local ip,port = tmpIpPort[1],tmpIpPort[2]
        -- local serverAddr =  {displayName = LauncherServerName, ip = ip, port = port, Urls = {}}
        -- self:SetServerAddr(serverAddr)
        -- log("WeGame InitDataBeforLogin Last Server Name=", LauncherServerName, " ip:port=", LauncherAddr)
    end
end

function SDKInfoServer:SetSelectAddrKey(key, addrStr)
    logerror("SDKInfoServer:SetSelectAddrKey key = ",key)

    self:SetServerAddrKey(key)
    self:UpdateServerAddr(key, addrStr)
end

function SDKInfoServer:GetSelectAddrKey()
    local playerInfo = self:GetGamePlayerInfo()
    if playerInfo then
        local serverKey = playerInfo:GetServerAddrKey();
        if not serverKey or serverKey == "" then
            -- DefaultServerAddr
            serverKey = self:GetDefaultServerAddrKey()
            self:UpdateServerAddr(serverKey)
        end
        log("[GetSelectAddrKey] ", serverKey)
        return serverKey
    else
        log("[GetSelectAddrKey] playerInfo is nil")
        return nil
    end
end

function SDKInfoServer:FindABTestKey(key)
    logwarning("FindABTestKey:origin keyName", key)
    local openId = self:GetOpenId()
    local ABTestKey = key
    if math.abs(openId) % 2 == 1 then
        -- 奇数A组
        ABTestKey = ABTestKey.."_abtest_a"
    else
        ABTestKey = ABTestKey.."_abtest_b"
    end

    logwarning("SDKInfoServer:FindABTestKey target keyName", ABTestKey)
    -- 查询key是否存在
    local hasKey, selectedServerAddr = self:GetServerAddrByKey(ABTestKey)
    if hasKey and selectedServerAddr then
        logwarning("SDKInfoServer:Find ABTestKey, keyName", ABTestKey)
    else
        logwarning("SDKInfoServer: not Find ABTestKey,keyName", ABTestKey)
    end
    return hasKey,ABTestKey

end

function SDKInfoServer:SetServerAddrKey(key)
    -- 查询是否配置了ABTestKey
    local hasKey,ABTestKey = self:FindABTestKey(key)
    if hasKey and ABTestKey then
        logwarning("SDKInfoServer:SetServerAddrKey originKeyName targetKeyName", key, ABTestKey)
        key = ABTestKey
    end
    self:SaveServerAddr(key)
    local playerInfo = self:GetGamePlayerInfo()
    if playerInfo and key then
        playerInfo:SetServerAddrKey(key);
    else
        log("[SetServerAddrKey] key or playerInfo is nil")
    end
end

function SDKInfoServer:GetServerName()
    local addr = self:GetServerAddr()
    if addr and addr.displayName and addr.displayName~= "" then
        return addr.displayName
    else
        if(addr and addr.ip and addr.port) then
            return (addr.ip..":"..addr.port)
        else
            return ""
        end
    end
end

function SDKInfoServer:SetLauncherServerInfo(selectedServerKey, serverName, addrStr)
    -- WeGame环境 优先通过配置的ip:port连接
    if addrStr ~= nil then
        log("WeGame Launcher Connect With ip:port",addrStr)
        local tmpIpPort = string.split(addrStr, ':')
        local ip,port = tmpIpPort[1],tmpIpPort[2]
        Facade.ConfigManager:SetString("customizeIpPort", string.format("%s:%s",ip,port))
        Facade.ConfigManager:SetString("LastLauncherServerName", serverName)
        self:SetServerAddrKey("customize")
    else
        logerror("SDKInfoServer:SetLauncherServerInfo error, empty ip:port")
    end
end

function SDKInfoServer:GetServerAddrByKey(addrKey)
    local serverAddrListAll = self:GetServerAddrList()
    local hasKey = false
    local selectedServerAddr = nil
    for keyi, value in pairs(serverAddrListAll) do
        if value and string.lower(value.KeyName) == string.lower(addrKey) then
            hasKey = true
            selectedServerAddr = value
            break
        end
    end
    return hasKey, selectedServerAddr
end

function SDKInfoServer:UpdateServerAddr(selectedServerKey, addrStr)
    if selectedServerKey and selectedServerKey ~= "" then
        local hasKey, selectedServerAddr = self:GetServerAddrByKey(selectedServerKey)
        if hasKey then
            self:SetServerAddrKey(selectedServerKey)
        elseif (not VersionUtil.IsRelease()) and addrStr and string.len(addrStr) > 0 then
            logerror(selectedServerKey.." can not be found, reset it as 'customize' serverkey")
            local tmpIpPort = string.split(addrStr, ':')
            local ip,port = tmpIpPort[1],tmpIpPort[2]
            Facade.ConfigManager:SetString("customizeIpPort", string.format("%s:%s",ip,port))
            self:SetServerAddrKey("customize")
            logerror("SDKInfoServer:UpdateServerAddr customize ", addrStr)
        end
        logerror("SDKInfoServer:UpdateServerAddr by key", selectedServerKey, hasKey)
    end
end

function SDKInfoServer:GetConnectedUrl()
    local playerInfo = self:GetGamePlayerInfo()
    if playerInfo then
        return playerInfo:GetConnectedUrl();
    else
        log("[GetConnectedUrl] connectedUrl is nil")
        return ""
    end
end

function SDKInfoServer:GetConnectedIP()
    local playerInfo = self:GetGamePlayerInfo()
    if playerInfo then
        return playerInfo:GetConnectedRealIp();
    else
        log("[GetConnectedIP] connectedIP is nil")
        return ""
    end
end

function SDKInfoServer:GetGamePlayerInfo()
    local UDFMGamePlayerInfo = import "DFMGamePlayerInfo"
    local DFMGamePlayerInfoIns = UDFMGamePlayerInfo.Get(GetGameInstance())
    return DFMGamePlayerInfoIns
end

function SDKInfoServer:GetServerAddr()
    local serverKey = self:GetSelectAddrKey()
    -- 是否包含serverKey_a或者serverKey_b，如果包含则使用，不包含则使用默认的
    local openId = self:GetOpenId()
    if serverKey then
        local hasKey, selectedServerAddr = self:GetServerAddrByKey(serverKey)
        if hasKey then
            return selectedServerAddr
        elseif serverKey == "customize" then
            local addrStr = Facade.ConfigManager:GetString("customizeIpPort");
            local tmpIpPort = string.split(addrStr, ':')
            local ip,port = tmpIpPort[1],tmpIpPort[2]
            local serverAddr = {ip = ip, port = port, Urls = {addrStr}}
            return serverAddr
        end
        log("GetServerAddr ", serverKey, hasKey)
    else
        log("GetServerAddr is nil")
        return nil
    end
end

function SDKInfoServer:GetServerAddrDisplay()
    local serverAddr = self:GetServerAddr()
    return self:GetServerAddrDisplayWithFormat(serverAddr)
end

function SDKInfoServer:GetServerAddrDisplayWithFormat(serverAddrRow)
    if serverAddrRow then
        local serverAddr = serverAddrRow
        local urlsNum = #serverAddr.Urls or 0
        if serverAddr.displayName then
            if urlsNum > 1 and not VersionUtil.IsShipping() then
                return serverAddr.displayName .. "(" .. urlsNum .. " urls)"
            else
                return serverAddr.displayName
            end
        else
            return serverAddr.ip .. ":" .. serverAddr.port
        end
    end
    return ""
end

function SDKInfoServer:SaveServerAddr(serverKey)
    serverKey = setdefault(serverKey, "")
    Facade.ConfigManager:SetString("LastServerKey", serverKey)
end

function SDKInfoServer:SetOpenId(OpenId)
    local playerInfo = self:GetGamePlayerInfo()
    if playerInfo and OpenId then
        if type(OpenId) == "string" then
            playerInfo:SetOpenIdStr(OpenId)
        elseif type(OpenId) == "number" then
            playerInfo:SetOpenId(OpenId)
        else
            log("[SetOpenId] openId is not number or string")
        end
        local UGameSDKManager = import "GameSDKManager"
        local gameSDKManager = UGameSDKManager.Get(GetGameInstance())
        if gameSDKManager ~= nil then
            gameSDKManager:SetOpenId(OpenId)
        end
    else
        log("[SetOpenId] openId is nil")
    end
end

function SDKInfoServer:GetOpenId()
    local playerInfo = self:GetGamePlayerInfo()
    if playerInfo then
        return playerInfo:GetOpenId()
    else
        log("[GetOpenId] openId is nil")
        return 0
    end
end

function SDKInfoServer:GetOpenIdStr()
    local playerInfo = self:GetGamePlayerInfo()
    if playerInfo then
        return playerInfo:GetOpenIdStr()
    else
        log("[GetOpenId] openId is nil")
        return ""
    end
end

function SDKInfoServer:SetUserName(UserName)
    local playerInfo = self:GetGamePlayerInfo()
    if playerInfo and UserName then
        playerInfo:SetUsername(UserName);
    else
        log("[SetUserName] UserName is nil")
    end
end

function SDKInfoServer:GetUserName()
    local playerInfo = self:GetGamePlayerInfo()
    if playerInfo then
        return playerInfo:GetUsername()
    else
        log("[GetUserName] UserName is nil")
        return "Err UserName"
    end
end

function SDKInfoServer:SetPictureUrl(url)

--- BEGIN MODIFICATION @ VIRTUOS: [XR] not display xbox avatar icon by default
    if IsXSX() then
        log("[VTS] [SetPictureUrl] skip set pirture url")
        return
    end
--- END MODIFICATION

    local playerInfo = self:GetGamePlayerInfo()
    if playerInfo and url then
        playerInfo:SetPictureUrl(url)
    else
        log("[SetPictureUrl] url is nil")
    end
end

function SDKInfoServer:GetPictureUrl()
    local playerInfo = self:GetGamePlayerInfo()
    if playerInfo then
        return playerInfo:GetPictureUrl()
    else
        log("[GetPictureUrl] url is nil")
        return ""
    end
end

function SDKInfoServer:GetAppId()
    local channelId = self:GetChannel()
    if channelId == EChannelType.kChannelQQ then
        return UAppSetting.Get().QQAppId
    elseif channelId == EChannelType.kChannelWechat then
        return UAppSetting.Get().WeixinAppId
    end
    return ""
end

function SDKInfoServer:GetChannel()
    local playerInfo = self:GetGamePlayerInfo()
    if playerInfo then
        return playerInfo:GetChannelID()
    else
        log("[GetChannel] channel is nil")
        return 0
    end
end

function SDKInfoServer:SetChannel(channelId)
    local playerInfo = self:GetGamePlayerInfo()
    if playerInfo and channelId then
        playerInfo:SetChannelID(channelId)
    else
        log("[SetChannelID] channel is nil")
    end
end

--PC端登录标准化的Loginchannel报固定官包渠道号（10430644）
--手游还是用之前的逻辑
function SDKInfoServer:GetConfigChannelIdNumber()
    if IsBuildRegionCN() and PLATFORM_WINDOWS then
        return 10430644
    end
    return self:GetConfigChannelId()
end

function SDKInfoServer:GetConfigChannelId()
    local configChannelId = ""
    if IsBuildRegionCN() then
        --纯血鸿蒙的用户的渠道号 固定值
        if PLATFORM_OPENHARMONY then
            return "39000001"
        end
        if VersionUtil.IsGameChannelSteam() then
            configChannelId = tostring(EChannelType.kChannelSteam)
        else
            local UDFMGameLogin = import "DFMGameLogin"
            local loginIns = UDFMGameLogin.Get(GetGameInstance())
            if loginIns then
                configChannelId = loginIns:GetConfigChannelID()  -- WakeupLoginHelper.GetLoginSource(); -- 渠道号
            else
                configChannelId = "0"
            end
        end
    elseif IsBuildRegionGlobal() then
        if IsMobile() then
            configChannelId = tostring(self:GetStoreChannel())
        elseif IsWindows() then
            configChannelId = tostring(VersionUtil.GetLauncherChannel())
        else
            --海外暂时没有ConfigChannelID,暂时用于映射充值OfferId,用EChannelType
            if VersionUtil.IsGameChannelOfficial() then
                configChannelId = tostring(EChannelType.kChannelLevelInfinite)
            elseif VersionUtil.IsGameChannelWeGame() then
                configChannelId = tostring(EChannelType.kChannelLevelInfinite)
            elseif VersionUtil.IsGameChannelSteam() then
                configChannelId = tostring(EChannelType.kChannelSteam)
            elseif VersionUtil.IsGameChannelEpic() then
                configChannelId = tostring(EChannelType.kChannelLevelInfinite)
            elseif VersionUtil.IsGameChannelGoogle() then
                configChannelId = tostring(EChannelType.kChannelGooglePlay)
            else
                configChannelId = tostring(self:GetChannel())
            end
        end

    elseif IsBuildRegionGA() then
        if IsMobile() then
            configChannelId = tostring(self:GetStoreChannel())
        elseif IsWindows() then
            configChannelId = tostring(VersionUtil.GetLauncherChannel())
        else
            configChannelId = tostring(EChannelType.kChannelGarena)
        end
    end
    return configChannelId
end

function SDKInfoServer:GetStoreChannel()
    if PLATFORM_ANDROID then
        if not IsBuildRegionCN() then
            if self._storeChannel then
                return self._storeChannel
            end
            --是否aab包
            if UGameVersionUtils.IsAndroidAppBundle() then
                self._storeChannel = 1
                logerror("SDKInfoServer:GetStoreChannel is aab")
            else
                logerror("SDKInfoServer:GetStoreChannel is apk")
                local UDFMGameLogin = import "DFMGameLogin"
                local loginIns = UDFMGameLogin.Get(GetGameInstance())
                if loginIns then
                    self._storeChannel = loginIns:GetStoreChannel() or 0
                end
                --apk包取不到渠道号或默认值为1认为是offical的渠道号
                if self._storeChannel <= 1 then
                    logerror("SDKInfoServer:GetStoreChannel is apk self._storeChannel == ", self._storeChannel)
                    self._storeChannel = 20000000
                end
            end
            logerror("SDKInfoServer:GetStoreChannel self._storeChannel", self._storeChannel)
            return self._storeChannel
        end
    end
    return 0
end

--是否安卓应用商店渠道包
function SDKInfoServer:IsAndroidAppStoreChannel()
    if PLATFORM_ANDROID then
        if not IsBuildRegionCN() then
            local bIsAAB = UGameVersionUtils.IsAndroidAppBundle()
            return (not bIsAAB)
        end
    end
    return false
end

function SDKInfoServer:IsAndroidSelfPublishChannel()
    if PLATFORM_ANDROID then
        if not IsBuildRegionCN() then
            local channel = self:GetStoreChannel()
            --[20000000~20001000]
            --google        1
            --offical       20000000
            --yandex        20000001
            --telegram	    20000002
            --vk	        20000003
            --applovin	    20000004
            --ironsource	20000005
            --unity	        20000006
            --reserve	    20000007 - 20001000 --备用
            if channel >= 20000000 and channel <= 20001000 then
                return true
            end
        end
    end
    return false
end

function SDKInfoServer:GetToken()
    local playerInfo = self:GetGamePlayerInfo()
    if playerInfo then
        return playerInfo:GetToken()
    else
        log("[GetToken] token is nil")
        return ""
    end
end

function SDKInfoServer:SetToken(token)
    local playerInfo = self:GetGamePlayerInfo()
    if playerInfo and token and token ~= "" then
        playerInfo:SetToken(token)
    else
        log("[SetToken] token is nil")
    end
end

function SDKInfoServer:ParsePayToken(channelInfo)
    if string.isempty(channelInfo) then
        logerror("[SDKInfoServer] ParsePayToken channelInfo is nil")
        return
    end
    logerror("[SDKInfoServer] ParsePayToken channelInfo:", channelInfo)
    local Json = require("DFM.YxFramework.Plugin.Json.Json").createJson()
    local info = Json.decode(channelInfo)
    if info ~= nil then
        local payToken = info.pay_token or ""
        local playerInfo = self:GetGamePlayerInfo()
        if playerInfo and payToken then
            playerInfo:SetPayToken(payToken)
        else
            log("[SetToken] payToken is nil")
        end
        
        self:SetCoreUserId(info.garena_sns_openid or "")
        if not IsHD() then
            if not info.garena_sns_openid then
                self:SetCoreUserId(info.uid or "")
            end
        end
        local acessToken = info.access_token or ""

        -- 安卓手游无accesstoken，使用token（越南合规用）
        if not IsHD() and IsBuildRegionGA() then
            acessToken = info.token or ""
        end
        if playerInfo and acessToken then
            playerInfo:SetAccessToken(acessToken)
        end
        local refreshToken = info.refresh_token or ""
        if playerInfo and refreshToken then
            playerInfo:SetRefreshToken(refreshToken)
        end
    end
end

function SDKInfoServer:GetAccessToken()
    local playerInfo = self:GetGamePlayerInfo()
    if playerInfo then
        return playerInfo:GetAccessToken()
    else
        log("[GetAccessToken] AccessToken is nil")
        return ""
    end
end

function SDKInfoServer:GetRefreshToken()
    local playerInfo = self:GetGamePlayerInfo()
    if playerInfo then
        return playerInfo:GetRefreshToken()
    else
        log("[GetRefreshToken] RefreshToken is nil")
        return ""
    end
end

function SDKInfoServer:GetPayToken()
    --海外固定为hy_openkey
    if IsBuildRegionGlobal() then
        return "hy_openkey"
    end
    --PC直接用token
    if IsHD() then
        return self:GetToken()
    end
    local playerInfo = self:GetGamePlayerInfo()
    if playerInfo then
        return playerInfo:GetPayToken()
    else
        log("[GetPayToken] payToken is nil") 
        return ""
    end
end

function SDKInfoServer:GetCoreUserId()
    local playerInfo = self:GetGamePlayerInfo()
    if playerInfo then
        return playerInfo:GetCoreUserId()
    else
        log("[GetCoreUserId] CoreUserId is nil")
        return ""
    end
end
function SDKInfoServer:SetCoreUserId(coreUserId)
    local playerInfo = self:GetGamePlayerInfo()
    if playerInfo and coreUserId then
        playerInfo:SetCoreUserId(coreUserId)
    else
        log("[SetCoreUserId] coreUserId is nil")
    end
end

function SDKInfoServer:SetPF(pf)
    local playerInfo = self:GetGamePlayerInfo()
    if playerInfo and pf then
        logerror("SDKInfoServer:SetPF pf:", pf)
        if PLATFORM_WINDOWS and IsBuildRegionGlobal() and VersionUtil.IsGameChannelEpic() then
            pf = pf:gsub("([^%-]+%-)([^%-]+%-)([^%-]+%-)([^%-]+)", "%1%2%3Epic")
            logerror("SDKInfoServer:SetPF pf epic:", pf)
        end
        playerInfo:SetPf(pf)
    else
        log("[SetPF] pf is nil") 
    end
end

function SDKInfoServer:GetPF()
--- BEGIN MODIFICATION @ VIRTUOS: 接入XSX米大师支付
    if IsConsole() then
        return "xxx-xxx-gameconsole"
    end
--- END MODIFICATION

    local playerInfo = self:GetGamePlayerInfo()
    if playerInfo then
        return playerInfo:GetPf()
    else
        log("[GetPF] pf is nil") 
        return ""
    end
end

function SDKInfoServer:SetPFKey(pfKey)
    local playerInfo = self:GetGamePlayerInfo()
    if playerInfo and pfKey then
        playerInfo:SetPfKey(pfKey)
    else
        log("[SetPFKey] pf is nil") 
    end
end

function SDKInfoServer:GetPFKey()
    --海外固定为hy_pfkey
    if IsBuildRegionGlobal() then
        return "hy_pfkey"
    end
    local playerInfo = self:GetGamePlayerInfo()
    if playerInfo then
        return playerInfo:GetPfKey()
    else
        return ""
    end
end

--- BEGIN MODIFICATION @ VIRTUOS: 接入XSX米大师支付
function SDKInfoServer:SetAreaId(areaId)
    if not IsConsole() then
        return
    end

    local playerInfo = self:GetGamePlayerInfo()
    if playerInfo and areaId then
        playerInfo:SetAreaId(areaId)
    else
        log("[SetAreaId] areaId is nil") 
    end
end

function SDKInfoServer:GetAreaId()
    if not IsConsole() then
        return
    end
    
    local playerInfo = self:GetGamePlayerInfo()
    if playerInfo then
        return playerInfo:GetAreaId()
    else
        log("[GetAreaId] playerInfo is nil") 
        return 1
    end
end
--- END MODIFICATION

function SDKInfoServer:ParseExtraJson(extraJson)
    if string.isempty(extraJson) then
        logerror("[SDKInfoServer] ParseExtraJson extraJson is nil")
        return
    end
    loginfo("[SDKInfoServer] ParseExtraJson extraJson:", extraJson)
    local Json = require("DFM.YxFramework.Plugin.Json.Json").createJson()
    local info = Json.decode(extraJson)
    local platform = string.match(extraJson, "platform=([^\"}]+)")
    if platform then
        platform = platform:gsub("\\", "")
        loginfo("[SDKInfoServer] ParseExtraJson platform:", platform)
        if platform == "1" then
            self:SetLoginSubChannelID(EChannelType.kChannelGarena)
        elseif platform == "3" then
            self:SetLoginSubChannelID(EChannelType.kChannelFacebook)
        elseif platform == "8" then
            self:SetLoginSubChannelID(EChannelType.kChannelGooglePlay)
        elseif platform == "10" then
            self:SetLoginSubChannelID(EChannelType.kChannelApple)
        end
    else
        loginfo("[SDKInfoServer] ParseExtraJson platform is nil")
    end
    if info ~= nil then
        if IsBuildRegionGlobal() then
            local get_status_rsp = info.get_status_rsp
            if get_status_rsp then
                local regionCode = get_status_rsp.region
                if string.isempty(regionCode) then
                    loginfo("[SDKInfoServer] ParseExtraJson regionCode is nil")
                else
                    self:SetRegionCode(regionCode)
                    loginfo("[SDKInfoServer] ParseExtraJson regionCode:", regionCode)
                end
                local adult_check_status = get_status_rsp.adult_check_status or 0
                loginfo("[SDKInfoServer] ParseExtraJson adult_check_status:", adult_check_status)
                self:SetPlayerAdultState(adult_check_status)
            end
        end
    else
        logwarning("[SDKInfoServer] ParseExtraJson get_status_rsp is nil")
    end
end

function SDKInfoServer:SetRegionCode(regionNumericCode)
    loginfo("[SDKInfoServer] SetRegionCode regionNumericCode:", regionNumericCode)
    if string.isempty(regionNumericCode) then
        logwarning("[SDKInfoServer] SetRegionCode regionNumericCode is nil")
        return
    end
    local dt = Facade.TableManager:GetTable("RegionCode")
    if dt then
        local regionCodeConfig = dt[regionNumericCode]
        if regionCodeConfig then
            self._regionCode = regionCodeConfig.RegionCode
            self._currencyCode = regionCodeConfig.CurrencyCode
            loginfo("[SDKInfoServer] SetRegionCode regionCode:", self._regionCode, "--currencyCode:", self._currencyCode)
        else
            logwarning("[SDKInfoServer] SetRegionCode regionCodeConfig is nil")
        end
    else
        logwarning("[SDKInfoServer] SetRegionCode dt is nil")
    end
    self._regionNumericCode = regionNumericCode
end

-- 地区代码 （数字代码）
function SDKInfoServer:GetRegionNumericCode()
    return self._regionNumericCode
end

-- 地区代码 (Alpha-2 Code)
function SDKInfoServer:GetRegionCode()
    return self._regionCode or ""
end

-- 地区货币代码
function SDKInfoServer:GetCurrencyCode()
    return self._currencyCode or ""
end

function SDKInfoServer:GetRegionNumericCodeByRegionCode(RegionNumericCode)
    logerror("[SDKInfoServer] GetRegionNumericCodeByRegionCode regionNumericCode:", RegionNumericCode)
    if string.isempty(RegionNumericCode) then
        logerror("[SDKInfoServer] GetRegionNumericCodeByRegionCode regionNumericCode is nil")
        return
    end
    local dt = Facade.TableManager:GetTable("RegionCode")
    if dt then
        for k, v in pairs(dt) do
            if RegionNumericCode == v.NumericCode then
                return v.RegionCode
            end
        end
    else
        logerror("[SDKInfoServer] GetRegionNumericCodeByRegionCode dt is nil")
    end
end

function SDKInfoServer:GetRegionCodeByCurrencyCode(CurrencyCode)
    logerror("[SDKInfoServer] GetRegionCodeByCurrencyCode CurrencyCode:", CurrencyCode)
    if string.isempty(CurrencyCode) then
        logerror("[SDKInfoServer] GetRegionCodeByCurrencyCode CurrencyCode is nil, fallback to US/USD")
        return 'US', 'USD'
    end
    if IsHD() and not Server.PayServer:IsGoogleEnable() then
        local dt = Facade.TableManager:GetTable("RegionCode")
        if dt then
            for k, v in pairs(dt) do
                if CurrencyCode == v.CurrencyCode then
                    return v.RegionCode
                end
            end
            logerror("[SDKInfoServer] GetRegionCodeByCurrencyCode PC Region not found, fallback to US/USD")
        else
            logerror("[SDKInfoServer] GetRegionCodeByCurrencyCode PC dt is nil, fallback to US/USD")
        end
        return 'US', 'USD'
    else
        local dt = Facade.TableManager:GetTable("CurrencyCodeLUTConfig")
        if dt then
            for k, v in pairs(dt) do
                if CurrencyCode == v.CurrencyCode then
                    if PLATFORM_IOS then
                        return v.AppleRegionCode
                    elseif PLATFORM_ANDROID or Server.PayServer:IsGoogleEnable() then
                        return v.GoogleRegionCode
                    end
                end
            end
            logerror("[SDKInfoServer] GetRegionCodeByCurrencyCode Mobile LUT failed, fallback to US/USD")
            return 'US', 'USD'
        else
            logerror("[SDKInfoServer] GetRegionCodeByCurrencyCode Mobile CurrencyCodeLUTConfig is nil")
            return 'US', 'USD'
        end
    end
end

-- 首次登录地区
function SDKInfoServer:SetFirstLoginRegion(numericCode)
    loginfo("[SDKInfoServer] SetFirstLoginRegion regionNumericCode:", numericCode)
    if string.isempty(numericCode) then
        logwarning("[SDKInfoServer] SetFirstLoginRegion regionNumericCode is nil")
        return
    end
    local dt = Facade.TableManager:GetTable("RegionCode")
    if dt then
        local regionCodeConfig = dt[numericCode]
        if regionCodeConfig then
            self._firstLoginRegionCode = regionCodeConfig.RegionCode
            self._firstLoginCurrencyCode = regionCodeConfig.CurrencyCode
            loginfo("[SDKInfoServer] SetFirstLoginRegion regionCode:", self._firstLoginRegionCode, "--currencyCode:", self._currencyCode)
        else
            logwarning("[SDKInfoServer] SetFirstLoginRegion regionCodeConfig is nil")
        end
    else
        logwarning("[SDKInfoServer] SetFirstLoginRegion dt is nil")
    end
    self._firstLoginRegionNumericCode = numericCode
end

-- 当前登录地区
function SDKInfoServer:SetCurrentLoginRegion(numericCode)
    loginfo("[SDKInfoServer] SetCurrentLoginRegion regionNumericCode:", numericCode)
    if string.isempty(numericCode) then
        logwarning("[SDKInfoServer] SetCurrentLoginRegion regionNumericCode is nil")
        return
    end
    local dt = Facade.TableManager:GetTable("RegionCode")
    if dt then
        local regionCodeConfig = dt[numericCode]
        if regionCodeConfig then
            self._currentLoginRegionCode = regionCodeConfig.RegionCode
            loginfo("[SDKInfoServer] SetCurrentLoginRegion regionCode:", self._currentLoginRegionCode)
        else
            logwarning("[SDKInfoServer] SetCurrentLoginRegion regionCodeConfig is nil")
        end
    else
        logwarning("[SDKInfoServer] SetCurrentLoginRegion dt is nil")
    end
end

function SDKInfoServer:GetFirstLoginRegion()
    return self._firstLoginRegionCode or ""
end

function SDKInfoServer:GetFirstLoginCurrency()
    return self._firstLoginCurrencyCode or ""
end

function SDKInfoServer:GetCurrentLoginRegion()
    return self._currentLoginRegionCode or ""
end

-- GVoice Url
function SDKInfoServer:SetGVoiceURL(url)
    loginfo("[SDKInfoServer] SetGVoiceURL url:", url)
    if string.isempty(url) then
        logerror("[SDKInfoServer] SetGVoiceURL url is nil")
        return
    end
    self._gvoiceURL = url
end

function SDKInfoServer:GetGVoiceURL()
    return self._gvoiceURL
end

-- 成年状态 WeGame传递的参数和INTL中不一样，这里转换成INTL参数
-- kSailPlayerAdultStateAgeLowerThanGameRequired = 0,  // the player's age is lower than the required age
-- for the game level, and he cannot enter the game.
-- kSailPlayerAdultStateUnderage = 1,  // the player is underage.
-- kSailPlayerAdultStateNotSet   = 2,  // the player has not set his age.
-- kSailPlayerAdultStateAdult    = 3,  // the player is adult.
function SDKInfoServer:ParsePlayerAdultState(playerAdultState)
    loginfo("[SDKInfoServer] ParsePlayerAdultState playerAdultState:", playerAdultState)
    if playerAdultState == 0 then
        playerAdultState = -1
    elseif playerAdultState == 1 then
        playerAdultState = -1
    elseif playerAdultState == 2 then
        playerAdultState = 0
    elseif playerAdultState == 3 then
        playerAdultState = 1
    else
        playerAdultState = 0
    end
    self:SetPlayerAdultState(playerAdultState)
end

function SDKInfoServer:SetPlayerAdultState(playerAdultState)
    loginfo("[SDKInfoServer] SetPlayerAdultState playerAdultState:", playerAdultState)
    self._playerAdultState = playerAdultState or -1
end

-- 成年状态
-- 未成年：-1
-- 未设置：0
-- 成年：1
function SDKInfoServer:GetPlayerAdultState()
    return self._playerAdultState or -1
end

function SDKInfoServer:IsRegionJapan()
    return (self:GetRegionCode() == "JP")
end

function SDKInfoServer:IsRegionKorea()
    return (self:GetRegionCode() == "KR")
end

function SDKInfoServer:IsRegionVietnam()
    return (self:GetRegionCode() == "VN" or self:GetFirstLoginRegion() == "VN")
end

function SDKInfoServer:IsAnyRegionAmerica()
    return (self:GetFirstLoginRegion() == "US" or self:GetCurrentLoginRegion() == "US") and IsBuildRegionGlobal()
end

function SDKInfoServer:SetLoginRelatedInfo(tab)
    self:SetOpenId(tab["openId"] or "")
    self:SetToken(tab["token"] or "")
    self:SetChannel(tonumber(tab["channelId"]) or 0)
    self:SetUserName(tab["userName"] or "")
    self:SetPictureUrl(tab["pictureUrl"] or "")
    self:SetPF(tab["pf"] or "")
    self:SetPFKey(tab["pfKey"] or "")
    self:SetRegionCode(tab["regionCode"] or "")
    self:ParsePlayerAdultState(tab["playerAdultState"] or "")
    log("SetLoginRelatedInfo",self.openId,self.channelId,self.sdkPicUrl)
    self:ParsePayToken(tab["channelInfo"])
    self:ParseExtraJson(tab["extraJson"])
    --PC这边需要将pf中的windows替换成android（组件bug）
    local pf = tab["pf"] or "";
    if IsBuildRegionCN() and IsHD() and not string.isempty(pf) then
        loginfo("[SDKInfoServer] SetLoginRelatedInfo self.pf before:"..pf)
        pf = string.gsub(pf, "-web", "-android")
        self:SetPF(pf)
        loginfo("[SDKInfoServer] SetLoginRelatedInfo self.pf after:"..pf)
    end
end

function SDKInfoServer:SetConnectInfo(connectInfo)
    local playerInfo = self:GetGamePlayerInfo()
    if connectInfo and playerInfo and connectInfo.RealIP and connectInfo.Url then
        playerInfo:SetConnectedRealIp(connectInfo.RealIP)
        playerInfo:SetConnectedUrl(connectInfo.Url)
    end
end
-- 设置登录成功的ChannelId
function SDKInfoServer:SetLastConnectChannelID(channelId)
    logwarning("[SDKInfoServer] SetLastConnectChannelID channelId:", channelId)
    if channelId == nil then
        channelId = 0
    end 
    Facade.ConfigManager:SetNumber("ChannelID", channelId)
end

function SDKInfoServer:GetLastConnectChannelID()
    return  Facade.ConfigManager:GetNumber("ChannelID", 0)
end

function SDKInfoServer:_OnStartReconnected()
    Facade.ProtoManager:TryDisConnectServer()
    self:TrySDKConnect()
end

function SDKInfoServer:TrySDKConnect()
    self.Events.evtKeyNode:Invoke("PointReport",{Login = "TrySDKConnect"})
    if Facade.ProtoManager:GetDNSResolvedAsync() == true then
        logerror("[SDKInfoServer] MakeConnectInfoAsync")
        self:MakeConnectInfoAsync()
    else
        logerror("[SDKInfoServer] MakeConnectInfo")
        local connectInfo = self:MakeConnectInfo()
        Facade.ProtoManager:TryConnectServer(connectInfo)
    end
end

-- 同步解析逻辑
function SDKInfoServer:MakeConnectInfo()
    local SdkGameId = UAppSetting.Get().SdkGameId
    local AuthType = UAppSetting.Get().AuthType
    local info = FConnectServerInfo()
    info.Channel = self:GetChannel() or 0
    logwarning("[MakeConnectInfo] login channelId",info.Channel)
    local Urls = slua.Array(EPropertyClass.Str)
    
    local serverAddr = self:GetServerAddr()
    local FullUrls = self:GetUrlList(serverAddr)
   
    if FullUrls == nil or #FullUrls == 0 then
        logerror("[MakeConnectInfo] no server info")
        self.Events.evtServerAddrNotfound:Invoke();
        return;
    end

    for key, url in pairs(FullUrls) do
        local resloveUrl
        for ip,port in url:gmatch('tcp://(.+):(%d+)' ) do
            if ip and port then
                resloveUrl = self:ResloveConnectUrl(ip, port)
                if resloveUrl and resloveUrl ~= "" then
                    Urls:Add(resloveUrl)
                end
            end
        end
        if resloveUrl == nil or resloveUrl == "" then
            logerror("[MakeConnectInfo] create url fail with key:url ", key, url)
        end
    end

    info.Urls = Urls
    local openId = self:GetOpenIdStr()
    if openId and openId ~= "" then
        info.OpenId = openId
    else
        info.OpenId = self:GetUserName()
    end
    info.Token = self:GetToken() or ""
    -- info.Expire = -1
    if AuthType ~= nil and AuthType ~= "" and type(AuthType) == "number" then
        info.AuthType = AuthType
    else
        info.AuthType = 4096
    end
    info.Appid = SdkGameId --"27808" -- MSDK_GAME_ID -- QQ/WeChat
    log("[MakeConnectInfo] SdkGameId and openId:",SdkGameId, openId)
    return info
end

function SDKInfoServer:ResloveConnectUrl(ip, port)
    local ipType = MathUtil.GetIPType(ip)
    local ipReslove = ip
    if(ipType == MathUtil.EIPType.string) then
        -- 调用域名解析
        local _OneSDK = OneSDKModule.Get(GetGameInstance())
        ipReslove = _OneSDK:GetAddressByName(ip)
    end
    
    -- 合法的URL格式为：scheme://host:port。Scheme支持 TCP和LWIP，host支持域名和IP（IPv6地址要加[]）。
    -- 如：tcp://************:2018，tcp://[2001:db8:1f70::999:de8:5678:6e8]:2018，lwip://tapolloipv6.gamedl.qq.com:1024。
    ipType = MathUtil.GetIPType(ipReslove)
    local Result
    -- IPV6格式处理
    if(ipType == MathUtil.EIPType.IPv6) then
        Result = "tcp://[" .. ipReslove .. "]:" .. port
    else
        Result = "tcp://" .. ipReslove .. ":" .. port
    end
    return Result
end

-- 异步解析逻辑
function SDKInfoServer:MakeConnectInfoAsync()
    self.UrlNum = 0
    self.Urls = {}
    local serverAddr = self:GetServerAddr()
    local FullUrls = self:GetUrlList(serverAddr)
    if FullUrls == nil or #FullUrls == 0 then
        logerror("SDKInfoServer:no server info")
        self.Events.evtServerAddrNotfound:Invoke();
        return;
    end

    local function f()
        logerror("[MakeConnectInfoAsync] timeout dns error")
        self:_StartConnectServer()
    end

    self._dsInfoTimeHandler = Timer.DelayCall(3, f)

    for key, url in pairs(FullUrls) do
        local resloveUrl
        for ip , port in url:gmatch('tcp://(.+):(%d+)' ) do
            if ip and port then
                self.UrlNum = self.UrlNum + 1
                -- 异步解析 优先级从小到大
                self:ResloveConnectUrlAsync(ip, port, self.UrlNum)
            end
        end
    end
end

function SDKInfoServer:ResloveConnectUrlAsync(ip, port, priority)
    local ipType = MathUtil.GetIPType(ip)

    if ipType == MathUtil.EIPType.string then
        local _OneSDK = OneSDKModule.Get(GetGameInstance())
        -- 异步解析
        _OneSDK:GetAddressByNameFromLua(ip, port, priority, EDnsResloveSeed.Login)
        logerror("[ResloveConnectUrlAsync] domain:port:", ip, port)
    else
        -- 无需域名解析直接回调
        self:_OnDnsAsyncResloved(ip, port, priority, EDnsResloveSeed.Login)
        logerror("[ResloveConnectUrlAsync] ip:port", ip, port)
    end
end

function SDKInfoServer:_OnDnsAsyncResloved(ip, port, priority, seed)
    --需要异步解析域名，直到解析完所有domain才可发起连接
    if EDnsResloveSeed and EDnsResloveSeed.Login and seed == EDnsResloveSeed.Login then
        self.UrlNum = self.UrlNum - 1
        local url = self:MakeUrl(ip, port)
        table.insert(self.Urls, {url = url, priority = priority})
        -- 判断域名是否解析完,需要额外处理有解析没有回来的情况
        logerror("[OnDnsAsyncResloved] ip, port, priority, wait urlNums, EDnsResloveSeed.Login", ip, port, priority,  self.UrlNum)
        if self.UrlNum == 0 then
            self:_StartConnectServer()
        end
    end
end

function SDKInfoServer:_StartConnectServer()
    logerror("SDKInfoServer:_StartConnectServer...")
    if self._dsInfoTimeHandler then
        Timer.CancelDelay(self._dsInfoTimeHandler)
        self._dsInfoTimeHandler = nil
    end

    local SdkGameId = UAppSetting.Get().SdkGameId
    local AuthType = UAppSetting.Get().AuthType
    local info = FConnectServerInfo()

    table.sort(self.Urls, function(a, b)
        return a.priority < b.priority
    end)

    local realUrls = slua.Array(EPropertyClass.Str)

    for _, item in ipairs(self.Urls) do
        logerror("SDKInfoServer:_StartConnectServer url and priority:",item.url, item.priority)
        realUrls:Add(item.url)
    end
    
    info.Channel = self:GetChannel() or 0
    info.Urls = realUrls
    local openId = self:GetOpenIdStr()
    if openId and openId ~= "" then
        info.OpenId = openId
    else
        info.OpenId = self:GetUserName()
    end
    info.Token = self:GetToken() or ""
    if AuthType ~= nil and AuthType ~= "" and type(AuthType) == "number" then
        info.AuthType = AuthType
    else
        info.AuthType = 4096
    end
    info.Appid = SdkGameId
    Facade.ProtoManager:TryConnectServer(info)
end



function SDKInfoServer:MakeUrl(ip, port)
    local ipType = MathUtil.GetIPType(ip)
    local Result
    -- IPV6格式处理
    if(ipType == MathUtil.EIPType.IPv6) then
        Result = "tcp://[" .. ip .. "]:" .. port
    else
        Result = "tcp://" .. ip .. ":" .. port
    end
    return Result
end

function SDKInfoServer:TryGetCustomServerUrlList()
    local KismetSystemLibrary = import "KismetSystemLibrary"
    local StringUtil = require("DFM.YxFramework.Util.StringUtil")
    local commandline = nil
    local hardcodeUrlListResult = {}
    
    if KismetSystemLibrary ~= nil then
        commandline = string.lower(KismetSystemLibrary.GetCommandLine())
    else
        return false, hardcodeUrlListResult
    end

    local pos = string.find(commandline, "-serverurl=")
    if pos ~= nil then
        local IdPos = string.find(commandline, "-serverurl=") + #"-serverurl="
        local zone_command = string.sub(commandline, IdPos, #commandline)
        local cmds = StringUtil.StringSplit(zone_command, " ")
        local ip = cmds[1] or ""

        if string.match(ip, "%d") then
            hardcodeUrlListResult[1] = "tcp://" .. ip .. ":55010"
            logerror("SDKInfoServer GetUrlList connect custom server: ", hardcodeUrlListResult[1])
            local noticeText = "正在连接自定义服务器" .. ip .. "..."
            local ConfirmQuitGame = "继续"
            Module.GCloudSDK:ShowCommonTip(noticeText, ConfirmQuitGame, nil, true, nil, nil, true)
            return true, hardcodeUrlListResult
        else
            logerror("SDKInfoServer GetUrlList custom server ip format invalid: ", ip)
        end
    end
    return false, hardcodeUrlListResult
end

function SDKInfoServer:TryGetPrefutureUrlList()
    local KismetSystemLibrary = import "KismetSystemLibrary"
    local StringUtil = require("DFM.YxFramework.Util.StringUtil")
    local commandline = nil
    local hardcodeUrlListResult = {}
    
    if KismetSystemLibrary ~= nil then
        commandline = string.lower(KismetSystemLibrary.GetCommandLine())
    else
        return false, hardcodeUrlListResult
    end

    local pos = string.find(commandline, "-prefuture=")
    if pos ~= nil and string.find(commandline, "-prerelease") ~= nil then
        local IdPos = string.find(commandline, "-prefuture=") + #"-prefuture="
        local zone_command = string.sub(commandline, IdPos, #commandline)
        local cmds = StringUtil.StringSplit(zone_command, " ")

        -- Validate id
        local id = cmds[1] or ""
        if string.len(id) == 1 then
            id = id .. "-a"
        end
        local serverId = string.sub(id, 1, 1)
        local serverGroup = string.sub(id, 3, 3)

        if string.match(serverId, "%d") and (serverGroup == "a" or serverGroup == "b") then
            hardcodeUrlListResult[1] = "tcp://lobby-prefuture" .. id .. ".dgameglobal.com:55010"
            logerror("SDKInfoServer GetUrlList connect prefuture: ", hardcodeUrlListResult[1])
            local noticeText = "正在连接未来" .. id .. "..."
            local ConfirmQuitGame = "继续"
            Module.GCloudSDK:ShowCommonTip(noticeText, ConfirmQuitGame, nil, true, nil, nil, true)
            return true, hardcodeUrlListResult
        else
            logerror("SDKInfoServer GetUrlList prefuture parameter format invalid: ", id)
        end
    end
    return false, hardcodeUrlListResult
end

function SDKInfoServer:GetUrlList(serverAddr)
    -- @akihikofeng 主机通过启动参数进自定义服
    if IsConsole() then
        logerror("SDKInfoServer IsConsole, TryGetCustomServerUrlList")
        local isCustomServer, customServerUrlListResult = self:TryGetCustomServerUrlList()
        if isCustomServer then
            return customServerUrlListResult
        end
    end

    -- @akihikofeng 主机通过启动参数进预发布未来服
    if IsConsole() and VersionUtil.IsShipping() then
        logerror("SDKInfoServer IsConsole, TryGetPrefutureUrlList")
        local isPrefuture, prefutureUrlListResult = self:TryGetPrefutureUrlList()
        if isPrefuture then
            return prefutureUrlListResult
        end
    end

    if serverAddr == nil  then
        logerror("SDKInfoServer GetUrlList serverAddr is nil")
        return
    end

    local urlListResult = {}
    for key, urlInfo in pairs(serverAddr.Urls) do
        local TargetServer = EServerType.Default
        if VersionUtil.IsShipping() then
            if VersionUtil.IsPreRelease() then
                TargetServer = EServerType.PreRelease
            elseif VersionUtil.IsInReview() then
                TargetServer = EServerType.Review
            end
        end
        if urlInfo.Type == TargetServer and urlInfo.Url and urlInfo.Url ~= "" then
            urlListResult[#urlListResult + 1] = urlInfo.Url
        end
    end
    if #urlListResult == 0 then
        urlListResult[1] = "tcp://" .. serverAddr.ip .. ":" .. serverAddr.port
    end
    return urlListResult
end

function SDKInfoServer:GetEditorLoginList()
    return UAppSetting.Get().LoginSetting.EditorLoginList
end

function SDKInfoServer:GetWindowsLoginList()
    return UAppSetting.Get().LoginSetting.WindowsLoginList
end

function SDKInfoServer:GetAndroidLoginList()
    return UAppSetting.Get().LoginSetting.AndroidLoginList
end

function SDKInfoServer:GetXSXLoginList()
    return UAppSetting.Get().LoginSetting.XSXLoginList
end

function SDKInfoServer:GetPS5LoginList()
    return UAppSetting.Get().LoginSetting.PS5LoginList
end

function SDKInfoServer:GetIOSLoginList()
    local iOSLoginList = UAppSetting.Get().LoginSetting.IOSLoginList
    local UDFMGameLogin = import "DFMGameLogin"
    local loginIns = UDFMGameLogin.Get(GetGameInstance())
    local resultList = StringUtil.StringSplit(iOSLoginList, ",")
    if loginIns:IsWechatInstalled() == false and table.indexof(resultList,tostring(EChannelType.kChannelWechat)) > 0 then
        -- 渠道中有微信但未安装微信，则去除微信
        table.removebyvalue(resultList,tostring(EChannelType.kChannelWechat))
    end 
    return table.concat(resultList,",")
end

function SDKInfoServer:GetLoginList()
    local loginlist = nil
    -- 获取登录渠道
    if _WITH_EDITOR == 1 then
        --editor
        loginlist = Server.SDKInfoServer:GetEditorLoginList()
    elseif PLATFORM_WINDOWS or PLATFORM_MAC then
        --PC
        loginlist = Server.SDKInfoServer:GetWindowsLoginList()
    elseif PLATFORM_ANDROID then
        --android
        loginlist = Server.SDKInfoServer:GetAndroidLoginList()
    elseif PLATFORM_OPENHARMONY then
        --todo PLATFORM_OPENHARMONY
        --openHarmony 
        loginlist = Server.SDKInfoServer:GetAndroidLoginList()
    elseif PLATFORM_IOS then
        --ios
        loginlist = Server.SDKInfoServer:GetIOSLoginList()
    elseif PLATFORM_XSX then
        --xsx
        loginlist = Server.SDKInfoServer:GetXSXLoginList()
    elseif PLATFORM_PS5 then
        --ps5
        loginlist = Server.SDKInfoServer:GetPS5LoginList()
    else
        logerror("SDKInfoServer GetLoginList platform is not defined")
    end
    return loginlist
end

function SDKInfoServer:GetVersionUpdateInfo()
    log("GetVersionUpdateInfo start")
    local clientVersion = VersionUtil.GetAppVersion()
    local resVersion = VersionUtil.GetResVersion()
    local hotUpdateVersion = Facade.ConfigManager:GetString("LastHotUpdateVersion", "")

    if(hotUpdateVersion == "") then  
        hotUpdateVersion = clientVersion
    end 

    if(clientVersion == "" or resVersion == "" or hotUpdateVersion == "") then
        logerror("SDKInfoServer:GetVersionUpdateInfo clientVersion or resVersion is error")
        return
    end

    local onCSPatchCheckVersionRes = function(res)
        if res ~= nil and res.result ~= 0 and res.rules ~= nil and #res.rules > 0 then
           log("GetVersionUpdateInfo Invoke")
           Server.SDKInfoServer.Events.evtServerHotUpdateInfoChanged:Invoke()
        end
    end

    local req = pb.CSPatchCheckVersionReq:New()
    req.client_version = string.match(clientVersion, "^%d+%.%d+%.%d+") or "0"
    req.res_version = tonumber(string.match(resVersion, "%d+$")) or 0
    req.hotfix_version = tonumber(string.match(hotUpdateVersion, "%d+$")) or 0
    req.platform = Server.AccountServer:GetPlatIdType()
    loginfo("SDKInfoServer:GetVersionUpdateInfo req ",req.client_version,req.res_version,req.hotfix_version,req.platform)
    req:Request(onCSPatchCheckVersionRes)
end


function SDKInfoServer:SetLaunchForm(extraJson)
    logerror("SDKInfoServer:SetLaunchForm ", extraJson)
    if extraJson == nil then
        return
    end

    local launchForm = 0;
    if string.find(extraJson,"sq_gamecenter") then
        launchForm = 4 -- qq游戏中心
    elseif string.find(extraJson,"WX_GameCenter") then
        launchForm = 3 -- 微信游戏中心
    end

    logerror("SDKInfoServer:SetLaunchForm ", launchForm)
    local playerInfo = self:GetGamePlayerInfo()
    if playerInfo then
        playerInfo:SetLaunchFrom(launchForm)
    end
end

function SDKInfoServer:GetLaunchChannelId()
    local launchForm = 0
    local playerInfo = self:GetGamePlayerInfo()
    if playerInfo then
        launchForm = playerInfo:GetLaunchFrom()
    end

    local channelId = 0
    if launchForm == 4 then
        channelId = EChannelType.kChannelQQ
    elseif launchForm == 3 then
        channelId = EChannelType.kChannelWechat
    end
    logerror("SDKInfoServer:GetLaunchChannelId = ", channelId)
    return channelId
end

function SDKInfoServer:SavePlayerNameConf(res)
    self.unicodeConf = res
end

function SDKInfoServer:GetPlayerNameConf()
    return self.unicodeConf
end

function SDKInfoServer:SetNetBarInfo(bIsNetBarMachine ,tokenBuff, tokenLen, ip, macSize, macs)
    logerror("[NetBar] SetNetBarInfo...")
    self.netbarInfo.bIsNetBarMachine = bIsNetBarMachine
    self.netbarInfo.tokenBuff = tokenBuff
    self.netbarInfo.tokenLen = tokenLen
    self.netbarInfo.ip = ip
    self.netbarInfo.macSize = macSize
    self.netbarInfo.macs = macs
end

function SDKInfoServer:GetNetBarInfo()
    return self.netbarInfo
end


function SDKInfoServer:GetNetBarToken()
    logerror("[NetBar] netbar_token = ", self.netbarInfo.tokenBuff)
    return self.netbarInfo.tokenBuff
end

function SDKInfoServer:GetNetBarMacs()
    local result = {}
    logerror("[NetBar] macs = ", self.netbarInfo.macs)
    local t = string.split(self.netbarInfo.macs ,';')
    for i, v in ipairs(t) do
        if v ~= "" then
            table.insert(result, v)
        end
    end
    return result
end

function SDKInfoServer:SetZoneId(zoneId)
    if zoneId and zoneId ~= 0 then
        local playerInfo = self:GetGamePlayerInfo()
        if playerInfo then
            playerInfo:SetZoneId(zoneId)
        end
    end
end

function SDKInfoServer:GetZoneId()
    local playerInfo = self:GetGamePlayerInfo()
    if playerInfo then
       local zoneId = playerInfo:GetZoneId()
       if zoneId and zoneId ~= 0 then
            logerror("[SDKInfoServer] ZoneId form server = ", zoneId)
            return zoneId
       end
    end

    local selectedKey = self:GetSelectAddrKey()
    local number = string.match(selectedKey, "%d+")
    if number then
        local zoneId = tonumber(number)
        if VersionUtil.IsPreRelease() and VersionUtil.GetGameBuildConfiguration() == EGameBuildConfiguration.Release then
            zoneId = tonumber(number) + 1
        end
        logerror("[SDKInfoServer] ZoneId from local = ", zoneId)
        return zoneId
    else
        return 0
    end
end

function SDKInfoServer:SetINTLComplianceInfo(info)
    self.INTLComplianceInfo = nil
    self.INTLComplianceInfo = info
end

function SDKInfoServer:GetINTLComplianceInfo()
    return self.INTLComplianceInfo
end

function SDKInfoServer:SetClientIp(clientIp)
    self._clientIp = clientIp
end

function SDKInfoServer:GetClientIp()
    return self._clientIp
end

function SDKInfoServer:SetWeLoginInfo(info)
    logerror("[SDKInfoServer] SetWeLoginInfo info:", info)
    self._wg_login_info = info;
end

function SDKInfoServer:GetWeLoginInfo()
    if self._wg_login_info then
        return self._wg_login_info
    else
        logerror("[GetWeLoginInfo] wg_login_info is nil ")
        return ""
    end
end

function SDKInfoServer:SetLoginRegionCode(regionCode)
    self._LoginRegionCode = regionCode
end

function SDKInfoServer:GetLoginRegionCode()
    return self._LoginRegionCode
end

function SDKInfoServer:SetLoginThirdType(channel)
    local thirdType = "unknown"
    if channel == EChannelType.kChannelGuest then
        thirdType = "guest"
    elseif channel == EChannelType.kChannelFacebook then
        thirdType = "facebook"
    elseif channel == EChannelType.kChannelGooglePlay then
        thirdType = "google"
    elseif channel == EChannelType.kChannelGarena then
        thirdType = "garena"
    elseif channel == EChannelType.kChannelApple then
        thirdType = "apple"
    end
    loginfo("SDKInfoServer:SetLoginThirdType channel:", channel, thirdType)
    self._LoginThirdType = thirdType
end

function SDKInfoServer:GetLoginThirdType()
    return self._LoginThirdType
end

function SDKInfoServer:GetNetWorkDetailInfo()
    local playerInfo = self:GetGamePlayerInfo()
    if playerInfo then
        return playerInfo:GetNetWorkDetailInfo()
    else
        return ""
    end
end

function SDKInfoServer:SetNetworkType(type)
    self._networkType = type
end

function SDKInfoServer:GetNetworkType()
    return self._networkType
end

function SDKInfoServer:SetLoginSubChannelID(channel)
    loginfo("SDKInfoServer:SetLoginSubChannelID channel:", channel)
    self._loginSubChannelID = channel
end

function SDKInfoServer:GetLoginSubChannelID()
    if self._loginSubChannelID then
        return self._loginSubChannelID
    else
        return self:GetChannel()
    end
end

function SDKInfoServer:SetLastSubChannelID(channelId)
    logwarning("[SDKInfoServer] SetLastSubChannelID channelId:", channelId)
    if channelId == nil then
        channelId = 0
    end 
    Facade.ConfigManager:SetNumber("SubChannelID", channelId)
end

function SDKInfoServer:GetLastSubChannelID()
    return  Facade.ConfigManager:GetNumber("SubChannelID", 0)
end

function SDKInfoServer:GetSDKGameID()
    return UAppSetting.Get().SdkGameId
end

return SDKInfoServer
