----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMImGuiPanel)
----- LOG FUNCTION AUTO GENERATE END -----------



local RTIDebugPanel = ImGuiPanelCls("RTIDebugPanel")

RTIDebugPanel.PanelWindowTitle = "RTI调试面板"
RTIDebugPanel.bMovable = false
RTIDebugPanel.bShowTitle = false
RTIDebugPanel.PositionX = 0.0
RTIDebugPanel.PositionY = 0.0
RTIDebugPanel.BackgroundAlpha = 0.2

local UGameplayStatics = import "GameplayStatics"
local ARTICapture = import "RTICapture"

function RTIDebugPanel:OnCreatePanel()
    self.RTICapture = UGameplayStatics.GetActorOfClass(GetWorld(), ARTICapture)
end

function RTIDebugPanel:OnGUI()
    if self.RTICapture == nil then
        self.RTICapture = UGameplayStatics.GetActorOfClass(GetWorld(), ARTICapture)
    end
    if self.RTICapture ~= nil then
        local position = self.RTICapture:GetActorLocation()
        ImGui.Text("RTICapture Pos : %s",position)
    end
    ImGui.Separator()
    self:ShowRTIConsoleVars()
    
end

function RTIDebugPanel:ShowRTIConsoleVars()
    ImGui.TextColored(ImVec4(1,1,0,1),"RTI控制台变量预览")
    ImGui.EditConsoleVar("r.RTIShow")
    ImGui.EditConsoleVar("r.RTIShowLog")
    ImGui.EditConsoleVar("r.RTIClearTime")
    ImGui.EditConsoleVar("r.RTIBorder")
    ImGui.EditConsoleVar("r.RTIForceInGame")
    ImGui.EditConsoleVar("r.RTIEnable")
    ImGui.EditConsoleVar("r.RTIMerge")
    ImGui.EditConsoleVar("r.RTIEnablePreload")
    ImGui.EditConsoleVar("r.RTIEnableMultiWorld")
    ImGui.EditConsoleVar("r.RTIRenderDocCaptureFrame")
    ImGui.EditConsoleVar("r.RTIForceHighIcon")
    ImGui.EditConsoleVar("r.RTIForceLowIcon")
    ImGui.EditConsoleVar("r.RTIForceWhiteStaticIcon")
    ImGui.EditConsoleVar("r.RTICompilePSOFrameNum")
    ImGui.EditConsoleVar("r.RTIDownsampled")
    ImGui.EditConsoleVar("r.RTILimitScale")
    ImGui.EditConsoleVar("r.RTICreateWeaponMaxNumOneFrame")
    ImGui.EditConsoleVar("r.RTIMergeStaticIconMaxNumOneFrame")
    ImGui.EditConsoleVar("r.RTIHandleWeaponSkinStaticIcon")
    ImGui.EditConsoleVar("r.RTIAllWhiteSkinId")
    ImGui.EditConsoleVar("r.RTIForceRTIConfigId")
    ImGui.EditConsoleVar("r.RTIForceLightId")
    ImGui.EditConsoleVar("r.RTILightExposureCoef")
    ImGui.EditConsoleVar("r.RTILightExposureCoefSky")
    ImGui.EditConsoleVar("r.RTILiteRendererType")
    ImGui.EditConsoleVar("r.RTIEnableReflectionCapture")
    ImGui.EditConsoleVar("r.RTIShowCube")
    ImGui.EditConsoleVar("r.RTISSAA")
    ImGui.EditConsoleVar("r.RTIWaitLightBPFrameNum")
    ImGui.EditConsoleVar("r.RTICaptureStepFrameNum")
    ImGui.EditConsoleVar("r.RTISceneCaptureComponent2DX")
    ImGui.EditConsoleVar("r.RTIEnableRTCache")
    ImGui.EditConsoleVar("r.RTIEnableCVarPostprocessingColorGradingLazyLut")
end


return RTIDebugPanel