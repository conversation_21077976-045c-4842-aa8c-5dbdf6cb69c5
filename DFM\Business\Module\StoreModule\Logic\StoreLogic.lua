----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMStore)
----- LOG FUNCTION AUTO GENERATE END -----------

local PriceDisplayOrder = import "EPriceDisplayOrder"
local UDFMGameGPM = import "DFMGameGPM"
local HD_DOWNLOAD_MODEL_LEVEL = 2   -- 高清档位

local StoreLogic = {}
---------------------------------------------------------------------------------
--- Logic可以拆分多个，用于业务逻辑的编写，部分需要开放的接口由Module进行转发
---------------------------------------------------------------------------------
--- 可以仅模块内部调用，也可以在Module中被公开
StoreLogic.DoSomeThingProcess = function(...)
    -- TODO 业务逻辑、弹窗Tips表现、发送Server Req等等一系列事情
    -- Server.StoreServer:DoSomeThingReq(...)
    -- return 
end

StoreLogic.ShowMainPanelProcess = function(...)
    --- 异步打开配置好lua脚本的UI
    ---@param UINavID number 来自于UITable里配置的UIName2ID
    ---@param fLoadFinCallback function
    ---@param caller table
    ---@param ... args 调用传入UIIns:OnInitExtraData(...)

    -- 1.local 写法
    -- local OnCreateCallback = function(self, uiIns)
    --     Module.Store.Field:SetMainPanel(uiIns)
    -- end
    -- Facade.UIManager:AsyncShowUI(UIName2ID.StoreMainPanel, OnCreateCallback, nil, ...)

    -- 2.外部函数写法
    Module.ModuleSwitcher:ModuleOpenByCheck(SwitchSystemID.SwitchSyetemStore, UIName2ID.StoreMainPanel, nil, StoreLogic.OnMainPanelCreateFinished, nil, ...)
    --Facade.UIManager:AsyncShowUI(UIName2ID.StoreMainPanel, StoreLogic.OnMainPanelCreateFinished, nil, ...)
end

StoreLogic.OnMainPanelCreateFinished = function(uiIns)
    Module.Store.Field:SetMainPanel(uiIns)
end

StoreLogic.CloseMainPanelProcess = function()
    local mainPanel = Module.Store.Field:GetMainPanel()
    if mainPanel then
        Facade.UIManager:CloseUI(mainPanel)
        Module.Store.Field:SetMainPanel(nil)
    end
end


StoreLogic.ShowStoreRechargeMainPanle = function(...)
    Facade.UIManager:AsyncShowUI(UIName2ID.StoreRechargeMainPanle, StoreLogic.OnMainPanelCreateFinished, nil, ...)
end

StoreLogic.ShowStoreMandelOnlyPanle = function(...)
    local first = select(1, ...)  -- 获取第一个参数
    local second = select(2, ...)  -- 获取第一个参数

    loginfo("StoreLogic.ShowStoreMandelOnlyPanel first param", first, "second", second)

    if (first and StoreLogic.IsActivityMandelBrick(first)) or second == 1 then
        Facade.UIManager:AsyncShowUI(UIName2ID.StoreActivityMandelDrawOnly2, nil, nil, ...)
    else
        Facade.UIManager:AsyncShowUI(UIName2ID.StoreMandelDrawOnly, nil, nil, ...)
    end
end

---展示模型
---@param data StoreItemStruct 商品数据
---@param bMagnify boolean 是否模型放大界面 
StoreLogic.ShowModel = function (data, bMagnify)
    if data == nil then
        return
    end

    local mainTabId = Module.Store.Field:GetSelectMainTabId()
    local subTabId = Module.Store.Field:GetSelectSubTabId()

    if mainTabId == EStoreMainTab.Hero or mainTabId == EStoreMainTab.Appearance then
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "ResetCharacterRotation")
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayType", "CharacterR")
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetCharacterAvatar", data.avatarId, data.bAvatarWithSuit)
        Facade.HallSceneManager:CallSceneCharacterAvatarFunctionBySubstage(ESubStage.HallMall, "SetActorHiddenInGame", false)
    elseif mainTabId == EStoreMainTab.Gun then
        if subTabId == EStoreSpecialSubTab.GunTheme then
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayType", "WeaponR")
        else
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayType", "WeaponR2")
        end
        local item = ItemBase:NewIns(data.itemId)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayWeapon", item:GetRawDescObj(), true)
    else
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayType", "ItemR")
        local item = ItemBase:NewIns(data.itemId)
        if item.itemMainType == EItemType.Hero then
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayType", "CharacterR")
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetCharacterAvatar", data.avatarId, data.bAvatarWithSuit)
        elseif item.itemMainType == EItemType.WeaponSkin or item.itemMainType == EItemType.Weapon then
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayWeapon", item:GetRawDescObj(), true)
        else
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayItem", tostring(data.itemId))
        end
    end
end

StoreLogic.ShowBoxModel = function (id)
    if id == nil then
        return
    end
    local item = ItemBase:NewIns(id)
    if not item then
        return
    end
    if item.itemMainType == EItemType.WeaponSkin or item.itemMainType == EItemType.Weapon then
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayType", "Weapon")
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayWeapon", item:GetRawDescObj(), true)
    else
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayType", "ItemR")
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayItem", tostring(id))
    end
end

---隐藏模型
StoreLogic.HideModel = function ()
    local mainTabId = Module.Store.Field:GetSelectMainTabId()
    if mainTabId == EStoreMainTab.Hero or mainTabId == EStoreMainTab.Appearance then
        Facade.HallSceneManager:CallSceneCharacterAvatarFunctionBySubstage(ESubStage.HallMall, "SetActorHiddenInGame", true)
    elseif mainTabId == EStoreMainTab.Gun then
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "ResetDisplayWeapon")
    else
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "ResetDisplayWeapon")
        Facade.HallSceneManager:CallSceneCharacterAvatarFunctionBySubstage(ESubStage.HallMall, "SetActorHiddenInGame", true)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "ResetDisplayItem")
    end
end

---是否展示放大模型
---@param bMagnify boolean 是否放大
StoreLogic.DisplayMagnifyModel = function (bMagnify)
    local mainTabId = Module.Store.Field:GetSelectMainTabId()
    local subTabId = Module.Store.Field:GetSelectSubTabId()

    if mainTabId == EStoreMainTab.Hero or mainTabId == EStoreMainTab.Appearance then
        if bMagnify then
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayType", "Character")
        else
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayType", "CharacterR")
        end
    elseif mainTabId == EStoreMainTab.Gun then
        if bMagnify then
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayType", "Weapon")
        else
            if subTabId == EStoreSpecialSubTab.GunTheme then
                Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayType", "WeaponR")
            else
                Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall,"SetDisplayType", "WeaponR2")
            end
        end
    else
        --Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "ResetDisplayItem")
    end
end

---@return number
StoreLogic.GetSubstituteCurrencyItemID = function ()
    return ECurrencyItemId.Diamond
end

StoreLogic.GetMallGiftIsBuyLimitedByShopData = function (shopData)
    local ret = false

    if shopData == nil or shopData.GoodsId == nil then
        return ret
    end

    local buyRecord = Server.StoreServer:GetGetMallGiftRecordByGoodsId(shopData.GoodsId)
    if buyRecord ~= nil then
        if shopData.LimitType ~= nil and shopData.LimitType > 0 and shopData.LimitAmount > 0 then
            local timeStamp =  Server.StoreServer:GetBuyLimitResetTimeByLimitType(shopData.LimitType)
            if timeStamp ~= 0 then
                if buyRecord.num >= shopData.LimitAmount and buyRecord.buy_time > timeStamp then
                    ret = true
                end
            end
        else
            ret = true
        end
    else
        --buyRecord == nil doesn't limited
        ret = false
    end

    return ret
end

StoreLogic.GetDisplayPriceByPriceAndOrder = function(priceStr, cashConf)
    if cashConf.ShowType == PriceDisplayOrder.NumAbbr then
        return priceStr .. cashConf.CurrencyShort
    elseif cashConf.ShowType == PriceDisplayOrder.AbbrNum then
        return cashConf.CurrencyShort .. priceStr
    elseif cashConf.ShowType == PriceDisplayOrder.NumSymbol then
        return priceStr .. cashConf.CurrencySign
    elseif cashConf.ShowType == PriceDisplayOrder.SymbolNum then
        return cashConf.CurrencySign .. priceStr
    elseif cashConf.ShowType == PriceDisplayOrder.NumSymbolAbbr then
        return priceStr .. cashConf.CurrencySign .. cashConf.CurrencyShort
    elseif cashConf.ShowType == PriceDisplayOrder.NumAbbrSymbol then
        return priceStr .. cashConf.CurrencyShort .. cashConf.CurrencySign
    elseif cashConf.ShowType == PriceDisplayOrder.SymbolNumAbbr then
        return cashConf.CurrencySign .. priceStr .. cashConf.CurrencyShort
    elseif cashConf.ShowType == PriceDisplayOrder.SymbolAbbrNum then
        return cashConf.CurrencySign .. cashConf.CurrencyShort .. priceStr
    elseif cashConf.ShowType == PriceDisplayOrder.AbbrNumSymbol then
        return cashConf.CurrencyShort .. priceStr .. cashConf.CurrencySign
    elseif cashConf.ShowType == PriceDisplayOrder.AbbrSymbolNum then
        return cashConf.CurrencyShort .. cashConf.CurrencySign .. priceStr
    end
end

StoreLogic.GetDisplayPriceByProductItemData = function(productItemData, Discount)
    if productItemData == nil then
        return
    end

    loginfo("GetDisplayPriceByProductItemData:" , productItemData)
    local cashConf = nil
    --if productItemData.region_code == "" then
    --    cashConf = Server.StoreServer:GetStoreCashAreaDiffByCurrencyCode(productItemData.currency_code)
    --else
    --    cashConf = Server.StoreServer:GetStoreCashAreaDiffByRegionNumericCode(
    --            Server.SDKInfoServer:GetRegionNumericCodeByRegionCode(productItemData.region_code))
    --end
    local precision = 2
    local realPrice, origPrice

    ---- 计算原价乘数 ----
    ---- PC：100
    ---- 自发行：使用SDK返回的指数计算，安卓：1000000，iOS：100或1000000
    ---- GA：安卓：1000000，iOS：1
    ---- Google Play PC：1000000
    local original_price_multiplier = 100
    --如果PayRechargeItemStruct有SDK返回的指数则使用SDK的值
    if productItemData.decimal_point then
        original_price_multiplier = 10 ^ productItemData.decimal_point
    else
        -- 如果没有 则使用固定值
        -- 苹果IAP GooglePlay和Google Play PGS使用1m乘数
        if IsMobile() or Server.PayServer:IsGoogleEnable() then
            original_price_multiplier = 1000000
        end
    end
    
    cashConf = Server.StoreServer:GetStoreCashAreaDiffByCurrencyCode(productItemData.currency_code)
    if cashConf == nil then
        logerror("GetDisplayPriceByProductItemData cashConf is nil")
        return productItemData.currency_code .. MathUtil.GetNumberFormatStr(productItemData.original_price / original_price_multiplier, precision),
            productItemData.currency_code .. MathUtil.GetNumberFormatStr(math.ceil(productItemData.original_price / original_price_multiplier / Discount))
    end
    precision = cashConf.precision
    realPrice = MathUtil.GetNumberFormatStr(productItemData.original_price / original_price_multiplier, precision)
    origPrice = MathUtil.GetNumberFormatStr(math.ceil(productItemData.original_price / original_price_multiplier / Discount))
    return StoreLogic.GetDisplayPriceByPriceAndOrder(realPrice, cashConf),
        StoreLogic.GetDisplayPriceByPriceAndOrder(origPrice, cashConf)
end

StoreLogic.GetStrLength = function (idx, history, info, strType)
    if info == nil or idx == nil or strType == nil then
        return
    end
    local showStr = ""
    local length = 0
    local item = ItemBase:New(info.id)
    if item and item.quality then
        local qualityColorIDx = item.quality - 1
        if qualityColorIDx >= 0 then
            showStr = string.format(Module.Store.Config.Loc.MandelDrawSweepHistoryWithColor, idx, qualityColorIDx, item.name)
            if strType == 1 then
                showStr = string.format(Module.Store.Config.Loc.LogistMandelDrawSweepHistoryWithColor, idx, qualityColorIDx, item.name)
            end
            length = string.len("<customstyle color=\"Color_Quality0\"></>") + string.len(tostring(qualityColorIDx))
        else
            showStr = string.format(Module.Store.Config.Loc.MandelDrawSweepHistoryNormal, idx, item.name)
            if strType == 1 then
                showStr = string.format(Module.Store.Config.Loc.LogistMandelDrawSweepHistoryNormal, idx, item.name)
            end
        end
    end
    return string.len(showStr) - length
end

-----------------------------------------进入子关卡--------------------------------------------
StoreLogic.EnterSubLevel = function (itemId, subStageType, fAllLevelFinishCallback)
    subStageType = setdefault(subStageType, ESubStage.HallMall)
    local levelFinishCallBack = function ()
        local curSubStage = Facade.GameFlowManager:GetCurrentSubStage()
        if curSubStage ~= subStageType then
            Facade.GameFlowManager:EnterSubStage(subStageType)
        end
        fAllLevelFinishCallback()
    end
    Facade.LevelLoadManager:AsyncOperateStreamLevelsBySubStage(subStageType, true, nil, levelFinishCallBack, false, 30)
end
-----------------------------------------进入子关卡--------------------------------------------

--获取商城抽奖背景音乐
StoreLogic.GetLotteryMusic = function(lotteryId)
    local StoreLottery = Facade.TableManager:GetTable("StoreLottery")
    local musicName = ""
    if lotteryId and StoreLottery and StoreLottery[tostring(lotteryId)].MusicName then
        musicName = StoreLottery[tostring(lotteryId)].MusicName
    end
    return musicName
end

--获取该抽奖奖池视频是否可以跳过
StoreLogic.GetCanPassVideoNormal = function(lotteryId)
    local StoreLottery = Facade.TableManager:GetTable("StoreLottery")
    local canPass = true
    if lotteryId and StoreLottery then
        canPass = StoreLottery[tostring(lotteryId)].PassVideoNormal
    end
    return canPass
end

---商城抽奖UI背景设置 
---@param lotteryId any             奖池Id
---@param uiIns UIWidgetBase        传入当前UI的lua的self即可
---@param parentUISlot UIWidgetBase UI背景
---@param uiTextureImage UIImage    低配机底图UI
StoreLogic.SetLotteryUIBackground = function(lotteryId, uiIns, parentUISlot, uiTextureImage)
    local LotteryUIBackground = Facade.TableManager:GetTable("LotteryUIBackground")
    local weakUIIns, instanceId = nil, 0
    if lotteryId and LotteryUIBackground and LotteryUIBackground[lotteryId] then
       local UIName = LotteryUIBackground[lotteryId].LotteryUIName
       local LotteryImagePath = LotteryUIBackground[lotteryId].LotteryImagePath

       --设置UI背景
       if parentUISlot then
            Facade.UIManager:RemoveSubUIByParent(uiIns, parentUISlot)
            weakUIIns, instanceId = Facade.UIManager:AddSubUI(uiIns, UIName2ID[UIName], parentUISlot)
       end

       
       --设置低配机UI背景图
       if uiTextureImage and LotteryImagePath then
            uiTextureImage:AsyncSetImagePath(LotteryImagePath)
       end
    end
    return weakUIIns, instanceId
end

--商城抽奖UI艺术字设置
---@param lotteryId any             奖池Id
---@param uiTextureImage UIImage    低配机底图UI
StoreLogic.SetLotteryUIArtLogo = function(lotteryId, uiTextureImage , isAutoCtrlHide)
    if not uiTextureImage then
        return
    end
    local LotteryUIBackground = Facade.TableManager:GetTable("LotteryUIBackground")
    local bIsHide = false
    if lotteryId and LotteryUIBackground and uiTextureImage then
        local ImageSourceLogo = LotteryUIBackground[lotteryId] and LotteryUIBackground[lotteryId].ImageSourceLogo
        if ImageSourceLogo then
            uiTextureImage:AsyncSetImagePath(ImageSourceLogo)
        else
            bIsHide = true
        end
    else
        bIsHide = true
    end
    if isAutoCtrlHide then
        if bIsHide then
            uiTextureImage:Collapsed()
        else
            uiTextureImage:Visible()
        end
    end

end

---商城曼德尔抽奖UI背景视频设置
---@param mandelId any
---@param mediaComponent CommonVideComponent
StoreLogic.SetMandelDrawMediaBG = function(mandelId, mediaComponent)
    local LotteryUIBackground = Facade.TableManager:GetTable("LotteryUIBackground")
    if LotteryUIBackground then
        if mandelId and mediaComponent then
            if LotteryUIBackground[mandelId] 
            and LotteryUIBackground[mandelId].BGVideoName 
            and LotteryUIBackground[mandelId].BGVideoName ~= "None" 
            and LotteryUIBackground[mandelId].BGVideoName ~= "" then
                local MandelMediaName = LotteryUIBackground[mandelId].BGVideoName
                mediaComponent:HitTestInvisible()
                mediaComponent:Play(MandelMediaName)
            else
                mediaComponent:Hidden()
            end
        end
    end
end

-----------------------------------------其他--------------------------------------------

StoreLogic.IsMandelBrick = function(id)
    -- 判断是否为曼德尔砖
    local numStr = tostring(id)
    if numStr:sub(1, 6) == "161100" then -- 检查是否以16110开头            
        if numStr:sub(1, 6) ~= "161109" then -- 进一步检查是否不以161109开头
            return true
        end
    end 
    return false
end

StoreLogic.IsActivityMandelBrick = function(id)
    return Server.RewardServer:IsActivityMandelBrick(id)
    --local numStr = tostring(id)
    --if numStr:sub(1, 8) == "16110001" then 
    --    return  true
    --end
    --return false
    --if not StoreLogic.IsMandelBrick(id) then
    --    return false 
    --end
    --
    --local numStr = tostring(id)
    --local numLast6 = tonumber(numStr:sub(7, 11))
    --
    --if numLast6 > 1000 then
    --    return true
    --end
    --
    --return false
end

local StorePvConfigTable = Facade.TableManager:GetTable("StorePVConfig")
--获取是否已获得该道具
function StoreLogic.GetIsItemOwned(VideoName, IDList, isSoldOut)
    local StorePvConfig = StorePvConfigTable[VideoName]
    local bOwned = false
    if StorePvConfig == nil then
        return false
    end
    --bundle用
    if isSoldOut then
        bOwned = true
        return bOwned
    end
    
    for key, idListStr in pairs(StorePvConfig.ItemIdList) do
        local result = {}
        for value in string.gmatch(idListStr, "[^" .. "," .. "]+") do
            table.insert(result, value)
        end
        local count = 0
        local ItemIdStr = ""
        for key, ItemId in pairs(IDList) do
            ItemIdStr = tostring(ItemId)
            if ItemIdStr then
                if table.contains(result, ItemIdStr) then
                    count = count + 1
                end
                if count == #result then
                    bOwned = true
                    break
                end
            end
        end
    end
    return bOwned
end

--打开曼德尔砖的奖励详情和历史记录弹窗
function StoreLogic.OpenStorePrizePopPanel(fOnCreatePanelFunc,...)
    Facade.UIManager:AsyncShowUI(UIName2ID.StorePrizesPop, fOnCreatePanelFunc, nil,...)
end

--打开曼德尔砖的奖励总览弹窗
function StoreLogic.OpenStorePrizeOverviewPanel(fOnCreatePanelFunc,...)
    Facade.UIManager:AsyncShowUI(UIName2ID.StorePrizeOverview, fOnCreatePanelFunc, nil,...)
end

StoreLogic.GetDownloadCategary = function(iItemID)
    local tItem = ItemBase:NewIns(iItemID)
    if tItem.itemMainType == EItemType.WeaponSkin then
        -- 武器皮肤
        return "WeaponSkin"
    elseif tItem.itemMainType == EItemType.Adapter and tItem.itemSubType == ItemConfig.EAdapterItemType.Pendant then
        -- 武器配件-吊坠
        return "OtherWeapon"
    elseif tItem.itemMainType == EItemType.HeroAccessory and tItem.itemSubType == EHeroAccessroy.SparyPaint then
        -- 干员商业化周边-喷漆
        return "OtherCharacter"
    elseif tItem.itemMainType == EItemType.HeroAccessory and tItem.itemSubType == EHeroAccessroy.Gesture then
        -- 干员商业化周边-表演手势1p
        return "OtherCharacter"
    elseif tItem.itemMainType == EItemType.SocialAppearance and tItem.itemSubType == ESocialAppearanceType.SocialAvatarTab then
        -- 社交定制外观-头像
        return "OtherCharacter"
    elseif tItem.itemMainType == EItemType.SocialAppearance and tItem.itemSubType == ESocialAppearanceType.SocialMilitaryTab then
        -- 社交定制外观-军牌
        return "OtherCharacter"
    elseif tItem.itemMainType == EItemType.HeroAccessory and tItem.itemSubType == EHeroAccessroy.Card then
        -- 干员商业化周边-名片
        return "OtherCharacter"
    elseif tItem.itemMainType == EItemType.Hero then
        -- 干员
        return "Character"
    elseif tItem.itemMainType == EItemType.Fashion then
        -- 干员皮肤
        return "Fashion"
    elseif tItem.itemMainType == EItemType.HeroAccessory and tItem.itemSubType == EHeroAccessroy.AnimShow then
        -- 干员动作
        return "OtherCharacter"
    elseif tItem.itemMainType == EItemType.VehicleSkin then
        -- 载具皮肤
        return "VehiclesFashion"
    elseif tItem.itemMainType == EItemType.CollectionProp then
        -- 藏品道具
        return "None"
    elseif tItem.itemMainType == EItemType.Equipment and tItem.itemSubType == EEquipmentType.Helmet then
        -- 头盔
        return "OtherEquipment"
    elseif tItem.itemMainType == EItemType.Medicine then
        -- 药品
        return "OtherProp"
    elseif tItem.itemMainType == EItemType.Equipment and tItem.itemSubType == EEquipmentType.BreastPlate then
        -- 胸甲
        return "OtherEquipment"
    elseif tItem.itemMainType == EItemType.Equipment and tItem.itemSubType == EEquipmentType.ChestHanging then
        -- 胸挂
        return "OtherEquipment"
    elseif tItem.itemMainType == EItemType.Equipment and tItem.itemSubType == EEquipmentType.Bag then
        -- 背包
        return "OtherEquipment"
    elseif iItemID == 17888808889 then
        -- 三角券
        return "None"
    elseif StoreLogic.IsActivityMandelBrick(iItemID) then
        -- 活跃砖
        return "None"
    else
        -- 其他类型
        return "None"
    end
end

function StoreLogic.OpenStoreMandelBuyKeysPanel(fOnPanelCreatedCallback ,...)
    Facade.UIManager:AsyncShowUI(UIName2ID.StoreMandelBuyKeys, fOnPanelCreatedCallback, nil,...)
end

function StoreLogic.RegisterBottomAction(uiIns, isHide)
    --显示按键提示
    local summaryList ={}
    table.insert(summaryList, {actionName = "Back",func = function()
        Facade.UIManager:CloseUI(uiIns)
    end, caller = uiIns ,bUIOnly = false, bHideIcon = false})   
    if isHide then
        table.insert(summaryList, {actionName = "HidePanel", func = function(isBool)
           if isBool then
                uiIns:Collapsed()
                summaryList = {}
                table.insert(summaryList, {actionName = "BackList",func = function()
                    uiIns:Visible()
                    StoreLogic.RegisterBottomAction(uiIns, true)
                end, caller = uiIns ,bUIOnly = false, bHideIcon = false}) 
                Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, true, true)
           end
        end, caller = uiIns, bUIOnly = false, bHideIcon = false}) 
    end  
    Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, true, true)
end

function StoreLogic.GetMandelTopBarIDByItemID(iItemID)
    if iItemID == ECurrencyItemId.Mandel1 then
        return ECurrencyClientId.Mandel1
    end
    if iItemID == ECurrencyItemId.Mandel2 then
        return ECurrencyClientId.Mandel2
    end
    if iItemID == ECurrencyItemId.Mandel3 then
        return ECurrencyClientId.Mandel3
    end
    if iItemID == ECurrencyItemId.Mandel4 then
        return ECurrencyClientId.Mandel4
    end
    if iItemID == ECurrencyItemId.Mandel5 then
        return ECurrencyClientId.Mandel5
    end
    if iItemID == ECurrencyItemId.Mandel6 then
        return ECurrencyClientId.Mandel6
    end
    return nil
end

-- 获取消费券数量
function StoreLogic.GetConsumerCouponNum()
    local consumerCouponNum = 0
    local item = Server.CollectionServer:GetCollectionItemById(Server.StoreServer:GetTimeLimitComsumeCouponId(), nil, true)
    if item and item.num then
        consumerCouponNum = item.num
    end
    return consumerCouponNum
end

-- 获取消费券信息
function StoreLogic.GetConsumerCouponInfoStr()
    -- local consumerCouponNum = StoreLogic.GetConsumerCouponNum()
    -- if consumerCouponNum <= 0 then
    --     return nil
    -- end
    local ConsumeTicketsShowInfo = Server.CollectionServer:GetConsumeTicketsShowInfo()
    local currentTimestamp = Facade.ClockManager:GetLocalTimestamp()
    if ConsumeTicketsShowInfo then
        if currentTimestamp > ConsumeTicketsShowInfo.end_time then
            return nil
        else
            -- 更新倒计时
            local countDownTimestamp = ConsumeTicketsShowInfo.end_time - currentTimestamp
            local day, hour, min, sec = TimeUtil.GetSecondsFormatDDHHMMSS(countDownTimestamp)
            local countDownStr = ""
            if day == 0 and hour == 0 then
                countDownStr = string.format(Module.Market.Config.Loc.MinuteSecond, min, sec)
            elseif day == 0 and hour > 0 then
                countDownStr = string.format(Module.Market.Config.Loc.HourMinute, hour, min)
            elseif day > 0 then
                countDownStr = string.format(Module.Market.Config.Loc.DayHour, day, hour)
            end
            return countDownStr
        end
    end
    return nil
end

----------------- 支付柔性限制 begin ------------------

function StoreLogic.IsPaymentFlexibilityLimitByTypeList(tTypeList)
    return Server.StoreServer:IsPaymentFlexibilityLimitByTypeList(tTypeList)
end

function StoreLogic.IsPaymentFlexibilityLimitByType(iType)
    return Server.StoreServer:IsPaymentFlexibilityLimit(iType)
end

function StoreLogic.IsPaymentFlexibilityLimit()
    return Server.StoreServer:IsPaymentFlexibilityLimit()
end

function StoreLogic.GetPaymentFlexibilityLimitDetailTxt(solNum, solTotalNum, mpNum, mpTotalNum)
    local sTask = ""
    
    if solNum < solTotalNum then
        sTask = sTask .. string.format(Module.Store.Config.Loc.PaymentFlexibilityLimitSOLTaskDoing, solNum, solTotalNum, solTotalNum) .. "\n"
    else
        sTask = sTask .. string.format(Module.Store.Config.Loc.PaymentFlexibilityLimitSOLTaskDone, solNum, solTotalNum, solTotalNum) .. "\n"
    end

    if mpNum < mpTotalNum then
        sTask = sTask .. string.format(Module.Store.Config.Loc.PaymentFlexibilityLimitMPTaskDoing, mpNum, mpTotalNum, mpTotalNum)
    else
        sTask = sTask .. string.format(Module.Store.Config.Loc.PaymentFlexibilityLimitMPTaskDone, mpNum, mpTotalNum, mpTotalNum)
    end
    
    return sTask
end

function StoreLogic.ShowPaymentFlexibilityLimitConfirmWindow(level, solNum, solTotalNum, mpNum, mpTotalNum, cond)
    logerror("[StoreLogic] ShowPaymentFlexibilityLimitConfirmWindow, level", level, "solNum", solNum, "solTotalNum", solTotalNum, "mpNum", mpNum, "mpTotalNum", mpTotalNum, "cond", cond)
    if level > 0 then
        local fConfirmWindowProcess = function()
            local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
            if armedForceMode == EArmedForceMode.SOL then
                Module.Jump:JumpByID(1700003)
            elseif armedForceMode == EArmedForceMode.MP then
                Module.Jump:JumpByID(2000001)
            else
                Module.Jump:JumpByID(2000001)
            end
        end

        local sTitle = ""
        if cond == 0 then
            sTitle = Module.Store.Config.Loc.PaymentFlexibilityLimitOrTitle
        elseif cond == 1 then
            sTitle = Module.Store.Config.Loc.PaymentFlexibilityLimitAndTitle
        else
            logerror("[StoreLogic] ShowPaymentFlexibilityLimitConfirmWindow cond error, cond", cond)
            return
        end

        Module.CommonTips:ShowConfirmWindow(sTitle, fConfirmWindowProcess, nil, nil, Module.Store.Config.Loc.PaymentFlexibilityLimitGotoBtnText, nil, nil, nil, nil, nil, true, 
                StoreLogic.GetPaymentFlexibilityLimitDetailTxt(solNum, solTotalNum, mpNum, mpTotalNum))
    end
end

----------------- 支付柔性限制 end ------------------

----------------- 曼德尔砖累抽奖励 begin ------------------

function StoreLogic.GetMandelContinuousRewardCount(boxID)
    local info = Server.StoreServer:GetBoxInfoByBoxID(boxID)
    if info == nil then
        return 0
    end
    return #info.info_list
end

function StoreLogic.GetMandelContinuousRewardList(boxID)
    local list = {}

    local info = Server.StoreServer:GetBoxInfoByBoxID(boxID)
    if info == nil then
        return 0
    end

    for _, reward in pairs(info.info_list) do
        local listReward = {
            countLevel = reward.approve_count,
            reward = {} 
        }
        for _, prop in pairs(reward.props) do
            table.insert(listReward.reward, {
                id = prop.id,
                count = prop.num
            })  
        end
        table.insert(list, listReward)
    end
    
    return list
end

function StoreLogic.GetMandelCurrContinuousRewardCount(boxID)
    local info = Server.StoreServer:GetBoxInfoByBoxID(boxID)
    if info == nil or info.last_core_reward_approve_num == nil then
        return 0
    end
    return info.core_open_count
end

function StoreLogic.GetMandelContinuousRewardReceiveCount(boxID)
    local info = Server.StoreServer:GetBoxInfoByBoxID(boxID)
    if info == nil or info.core_open_count == nil then
        return 0
    end
    return info.last_core_reward_approve_num
end

function StoreLogic.IsMandelContinuousRewardBtnRedDot(boxID)
    return Server.StoreServer:IsMandelContinuousRewardBtnRedDot(boxID)
end

----------------- 曼德尔砖累抽奖励 end ------------------

function StoreLogic.ReplaceBetween(text, newContent)
    local colonPos, colonEnd = string.find(text, "%s*：")  -- 匹配冒号及前面的空格
    if not colonPos then return text end
    
    local semicolonPos = string.find(text, "；", colonEnd + 1)
    if semicolonPos then
        return string.sub(text, 1, colonEnd) .. newContent .. string.sub(text, semicolonPos)
    else
        return string.sub(text, 1, colonEnd) .. newContent
    end
end

function StoreLogic.StaffLotteryPurchasePop(fOnCreatePanelFunc,...)
    Facade.UIManager:AsyncShowUI(UIName2ID.StaffLotteryPurchasePop, fOnCreatePanelFunc, nil,...)
end

function StoreLogic.SetRichText(RichTextBlock, consumerCouponId, consumerCouponNum, currency_type, currecny, currency_type_sub, currecny_sub, price)
    --记录货币结果
    local riangularCoinNum, timeLimitTriangularRoll, triangularRoll = 0, 0, 0
    --
    local StoreConfig = Module.Store.Config
    if consumerCouponNum <= 0 then
        if currecny >= price then
            local priceStr = string.format(StoreConfig.Loc.BundlePurchaseTipMoneyEnough,
                Module.Currency:GetRichTxtImgByItemId(currency_type),
                MathUtil.GetNumberFormatStr(price))

            RichTextBlock:SetText(priceStr)
            triangularRoll = price
        else
            --use currency_type_sub
            if currency_type ~= currency_type_sub then
                if currecny + currecny_sub >= price then
                    local priceStr = string.format(StoreConfig.Loc.BundlePurchaseTipMoneyAndBindEnough,
                        Module.Currency:GetRichTxtImgByItemId(currency_type),
                        MathUtil.GetNumberFormatStr(currecny),
                        Module.Currency:GetRichTxtImgByItemId(currency_type_sub),
                        MathUtil.GetNumberFormatStr(price - currecny))

                    RichTextBlock:SetText(priceStr)
                else
                    --currecny + currecny_sub not enough
                    local priceStrNotEnough = string.format(StoreConfig.Loc.BundlePurchaseTipBindMoneyNotEnough,
                        Module.Currency:GetRichTxtImgByItemId(currency_type),
                        MathUtil.GetNumberFormatStr(currecny),
                        Module.Currency:GetRichTxtImgByItemId(currency_type_sub),
                        MathUtil.GetNumberFormatStr(price - currecny),
                        Module.Currency:GetRichTxtImgByItemId(currency_type_sub),
                        MathUtil.GetNumberFormatStr(price - currecny - currecny_sub))

                    RichTextBlock:SetText(priceStrNotEnough)
                end
            else
                --money not enough
                local priceStrNotEnough = string.format(StoreConfig.Loc.BundlePurchaseTipMoneyNotEnough,
                    Module.Currency:GetRichTxtImgByItemId(currency_type),
                    MathUtil.GetNumberFormatStr(price), Module.Currency:GetRichTxtImgByItemId(currency_type),
                    MathUtil.GetNumberFormatStr(price - currecny))

                RichTextBlock:SetText(priceStrNotEnough)
            end
            triangularRoll   = currecny
            riangularCoinNum = price - currecny
        end
    else
        if consumerCouponNum >= price then
            local priceStr = string.format(StoreConfig.Loc.BundlePurchaseTipMoneyEnough,
                "ConsumerCoupon",
                MathUtil.GetNumberFormatStr(price))
            RichTextBlock:SetText(priceStr)
            timeLimitTriangularRoll = price
        else
            if consumerCouponNum + currecny >= price then
                local priceStr = string.format(StoreConfig.Loc.BundlePurchaseTipMoneyAndBindEnough,
                    "ConsumerCoupon",
                    MathUtil.GetNumberFormatStr(consumerCouponNum),
                    Module.Currency:GetRichTxtImgByItemId(currency_type),
                    MathUtil.GetNumberFormatStr(price - consumerCouponNum))

                RichTextBlock:SetText(priceStr)
                timeLimitTriangularRoll = consumerCouponNum
                triangularRoll = price - consumerCouponNum
            else
                if currency_type ~= currency_type_sub then
                    if consumerCouponNum + currecny + currecny_sub >= price then
                        local priceStr = string.format(StoreConfig.Loc.BundlePurchaseTipMoneyAndBindAndCouponEnough,
                            "ConsumerCoupon",
                            MathUtil.GetNumberFormatStr(consumerCouponNum),
                            Module.Currency:GetRichTxtImgByItemId(currency_type),
                            MathUtil.GetNumberFormatStr(currecny),
                            Module.Currency:GetRichTxtImgByItemId(currency_type_sub),
                            MathUtil.GetNumberFormatStr(price - consumerCouponNum - currecny))

                        RichTextBlock:SetText(priceStr)
                    else
                            local priceStrNotEnough = string.format(StoreConfig.Loc.BundlePurchaseTipBindMoneyCouponNotEnough,
                            "ConsumerCoupon",
                            MathUtil.GetNumberFormatStr(consumerCouponNum),
                            Module.Currency:GetRichTxtImgByItemId(currency_type),
                            MathUtil.GetNumberFormatStr(currecny),
                            Module.Currency:GetRichTxtImgByItemId(currency_type_sub),
                            MathUtil.GetNumberFormatStr(price - consumerCouponNum - currecny),
                            Module.Currency:GetRichTxtImgByItemId(currency_type_sub),
                            MathUtil.GetNumberFormatStr(price - consumerCouponNum - currecny - currecny_sub))


                        RichTextBlock:SetText(priceStrNotEnough)
                    end
                else
                    local priceStrNotEnough = string.format(StoreConfig.Loc.BundlePurchaseTipBindMoneyNotEnough,
                        "ConsumerCoupon",
                        MathUtil.GetNumberFormatStr(consumerCouponNum),
                        Module.Currency:GetRichTxtImgByItemId(currency_type),
                        MathUtil.GetNumberFormatStr(currecny),
                        Module.Currency:GetRichTxtImgByItemId(currency_type),
                        MathUtil.GetNumberFormatStr(price - consumerCouponNum - currecny))


                    RichTextBlock:SetText(priceStrNotEnough)
                end
                riangularCoinNum = price - consumerCouponNum - currecny
                timeLimitTriangularRoll = consumerCouponNum
                triangularRoll = currecny
            end
        end
    end
    --返回所需货币数量
    return riangularCoinNum, timeLimitTriangularRoll, triangularRoll
end

function StoreLogic.isLowMobile()
    if not IsHD() then
        return false
    end

    local APMLevel = 0
    UDFMGameGPM.Get(GetWorld())
    if PLATFORM_IOS then
        APMLevel = UDFMGameGPM.GetDeviceLevelByQcc("level6")
    elseif PLATFORM_ANDROID then
        APMLevel = UDFMGameGPM.GetDeviceLevelByQcc("level6")
    else
        APMLevel = 4
    end

    if APMLevel <= HD_DOWNLOAD_MODEL_LEVEL then
        return true
    end

    return false
end

return StoreLogic
