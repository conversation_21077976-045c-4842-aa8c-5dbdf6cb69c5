----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaSGrowthRoad)
----- LOG FUNCTION AUTO GENERATE END -----------


--成长之路Server
---@class GrowthRoadServer : ServerBase
local GrowthRoadServer = class("GrowthRoadServer", require("DFM.YxFramework.Managers.Server.ServerBase"))
require "DFM.Business.DataStruct.GunsmithStruct.BeaconfireRewardsTableStruct"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local GeneralHelperTool = require "DFM.StandaloneLua.BusinessTool.GeneralHelperTool"

function GrowthRoadServer:Ctor()
end

function GrowthRoadServer:OnInitServer()
    self:_AddLoadTable(true)
    self:_AddListener(true)
    Facade.ProtoManager:AddNtfListener("CSMPDepositDataChangeNtf", self._OnCSMPDepositChangeNtf, self)
end

function GrowthRoadServer:_OnCSMPDepositChangeNtf(ntf)
    if self._weaponsstar == nil then
        self._weaponsstar = {}
    end
    if ntf then
        local change = ntf.mpdeposit_change
        if change then
            local weapon_changes = change.weapon_changes
            for _, weaponChange in ipairs(weapon_changes or {}) do
                local weaponId = weaponChange.ID
                if weaponId then
                    self._weaponsstar[weaponId] = {
                        star_num       = weaponChange.star_num,
                        cur_kill_count = weaponChange.cur_kill_count,
                        old_star_num   = weaponChange.old_star_num,
                        old_kill_count = weaponChange.old_kill_count,
                    }
                end
            end
        end
    end
end

function GrowthRoadServer:GetWeaponStar(weaponId, name)
    if self._weaponsstar == nil then
        return 0
    end
    if weaponId and name then
        local itemData = self._weaponsstar[weaponId]
        if itemData then
            return itemData[name] or 0
        end
    end
    return 0
end

--登录--局外
function GrowthRoadServer:OnLoadingLogin2Frontend(gameFlowType)
    self:_AddLoadTable(true)
end

--局内->局外
function GrowthRoadServer:OnLoadingGame2Frontend(gameFlowType)
    self:_AddLoadTable(true)
end

--局外->局内
function GrowthRoadServer:OnLoadingFrontend2Game(gameFlowType)
    self:_AddLoadTable()
end

--销毁SOL成长之路表
function GrowthRoadServer:Destroy()
    self:_AddLoadTable()
    self:_AddListener()
end

--加载表格
function GrowthRoadServer:_AddLoadTable(isBool)
    if isBool then
        --不用开始读取数据，使用的时候在加载
        self._isInGame = false
    else
        self._isInGame = true
    end
end

function GrowthRoadServer:_AddListener(isBool)
    if isBool then
        Server.RoleInfoServer.Events.evtSeasonLevelUpdate:AddListener(self._OnTestDataTbaleLoading, self)
        Server.RoleInfoServer.Events.evtAccountLevelUpdate:AddListener(self._OnTestDataTbaleLoading, self)
        Server.WeaponAssemblyServer.Events.evtOnMPWeaponPartDataUpdated:AddListener(self._UpdataWeaponData, self)
    else
        Server.RoleInfoServer.Events.evtSeasonLevelUpdate:RemoveListener(self._OnTestDataTbaleLoading, self)
        Server.RoleInfoServer.Events.evtAccountLevelUpdate:RemoveListener(self._OnTestDataTbaleLoading, self)
        Server.WeaponAssemblyServer.Events.evtOnMPWeaponPartDataUpdated:RemoveListener(self._UpdataWeaponData, self)
    end
end

function GrowthRoadServer:_UpdataWeaponData()
    local list = Server.WeaponAssemblyServer:GetAllWeaponData()
    --武器数据处理
    self._weapons = {}
    self._firstLevelParts = {}
    self._weaponParts = {}
    for id, weapon in pairs(list or {}) do
        self._weapons[id] = {}
        self._weaponParts[id] = {}
        for _, parts in pairs(weapon or {}) do
            if parts and parts.UnlockParts and #parts.UnlockParts > 0 then
                if parts.Level == 1 then
                    self._firstLevelParts[id] = parts.UnlockParts
                    table.insert(self._weapons[id], {
                        Level = parts.Level,
                        UnlockParts = {[1] = id},
                    })
                else
                    table.insert(self._weapons[id], parts)
                end
                table.insert(self._weaponParts[id], parts)
            end
        end
        --排序(避免数据未从小到大)
		table.sort(self._weapons[id], function (a, b)
            if a and b and a.Level and b.Level then
                return a.Level < b.Level
            end
			return false
		end)
        --排序(避免数据未从小到大)
        table.sort(self._weaponParts[id], function (a, b)
			if a and b and a.Level and b.Level then
                return a.Level < b.Level
            end
			return false
		end)
    end
    --配件名称/类型处理
    if self._pages == nil then
        self._pages = {}
        for key, value in pairs(Facade.TableManager:GetTable("AuctionPage") or {}) do
            if value and value.MainPageOrder == 4 then
                self._pages[value.PageSubId] = value
            end
        end
    end
    if self._parts == nil then
        self._parts = {}
        for key, value in pairs(Facade.TableManager:GetTable("PropSlotSubConfig") or {}) do
            if value and self._pages then
                local partInfo = self._pages[value.PageSubId]
                if partInfo and value.PagePropTypes then
                    local name = partInfo.LabelNo2Name
                    local partType = partInfo.SubPageOrder
                    local parts = string.split(value.PagePropTypes, ",")
                    for index, part in ipairs(parts or {}) do
                        if part and string.len(part) > 0 then
                            self._parts[tonumber(part)] = {
                                name = name,
                                type = partType,
                            }
                        end
                    end
                end
            end
        end
    end
    --配件解锁比例处理
    if self._parts == nil then
        return
    end
    self._unLocks = {}
    for id, weapon in pairs(self._weaponParts or {}) do
        self._unLocks[id] = {}
        for index, level in ipairs(weapon or {}) do
            local sum = {}
            for _, part in ipairs(level.UnlockParts or {}) do
                local info = self._parts[tonumber(part)]
                if info and info.type then
                    if sum[info.type] == nil then
                        sum[info.type] = {
                            level = level.Level,
                            type = info.type,
                            name = info.name,
                            num = 0,
                        }
                    end
                    if sum[info.type] and sum[info.type].num then
                        sum[info.type].num = sum[info.type].num + 1
                    end
                end
            end
            table.insert(self._unLocks[id], sum)
        end
    end
end

function GrowthRoadServer:GetWeaponDataById(id)
    if self._weapons == nil then
        self:_UpdataWeaponData()
    end
    if self._weapons and id then
        local weapon = self._weapons[id]
        if weapon then
            local parts = {}
            if self._firstLevelParts and self._firstLevelParts[id] then
                parts = self._firstLevelParts[id]
            end
            return weapon, parts
        end
    end
end

function GrowthRoadServer:GetUnLockPartById(id, level)
    if self._weapons == nil then
        self:_UpdataWeaponData()
    end
    if self._unLocks == nil or id == nil then
        return {}
    end
    level = level or 1
    local list = {}
    for index, value in ipairs(self._unLocks[id] or {}) do
        for key, progress in pairs(value or {}) do
            if progress then
                if list[progress.type] == nil then
                    list[progress.type] = {
                        name = progress.name,
                        unLock = 0,
                        all = 0,
                    }
                end
                if list[progress.type] then
                    if level >= progress.level then
                        list[progress.type].unLock = list[progress.type].unLock + progress.num
                    end
                    list[progress.type].all = list[progress.type].all + progress.num
                end
            end
        end
    end
    return list
end

function GrowthRoadServer:_OnTestDataTbaleLoading()
    --查询在局内是否发了这个事件
    if self._isInGame then
        logerror("ssy is in game loading datatable tips ..........")
    end
end

--获取武器皮肤描述
function GrowthRoadServer:_GetSkinDescription(itemData)
    if self._WeaponSkinDataTable == nil then
        self._WeaponSkinDataTable = Facade.TableManager:GetTable("WeaponSkin/WeaponSkinDataTable")
    end
    if self._VehicleSkin == nil then
        self._VehicleSkin = Facade.TableManager:GetTable("Vehicle/VehicleSkin")
    end
    if itemData and itemData.id and self._VehicleSkin and self._WeaponSkinDataTable then
        if itemData.itemMainType == EItemType.WeaponSkin then--武器皮肤
            local weapon = self._WeaponSkinDataTable[itemData.id]
            if weapon then
                return weapon.SkinDescription
            end
        elseif itemData.itemMainType == EItemType.VehicleSkin then--载具皮肤
            local vehicle = self._VehicleSkin[itemData.id]
            if vehicle then
                return vehicle.SkinDescription
            end
        end
    end
    return nil
end

-------------MP数据处理---------------------
--通过等级获取MP数据
function GrowthRoadServer:GetMPDataByLevel(level)
    local data = self:GetMPData()
    local maxLevel = self:GetMpMaxLevel()
    if data and maxLevel and level and level <= maxLevel then
        for i = level, maxLevel, 1 do
            if data[i] and data[i][1] and not data[i][1].isEmpty then
                return {
                    Level = i,
                    itemInfo = data[i],
                }
            end
        end
    end
    return {}
end

-------------SOL数据处理---------------------
function GrowthRoadServer:GetSOLNextLevelExp(curLevel)
    if self._SeasonLevel == nil then
        self._SeasonLevel = Facade.TableManager:GetTable("SeasonLevel")
    end
    for key, value in pairs(self._SeasonLevel or {}) do
        if value and value.Level == curLevel then
            return tonumber(value.Experience or 0)
        end
    end
    return 0
end

--通过等级获取SOL数据
function GrowthRoadServer:GetSOLDataByLevel(level)
    local data = self:GetSOLData()
    local maxLevel = self:GetSolMaxLevel()
    if data and maxLevel and level and level <= maxLevel then
        for i = level, maxLevel, 1 do
            if data[i] and data[i][1] and not data[i][1].isEmpty then
                return {
                    Level = i,
                    itemInfo = data[i],
                }
            end
        end
    end
    return {}
end

----------------------------------------------------------------------------------------------------------------------
--------------------------------------------------------------成长之路-------------------------------------------------
function GrowthRoadServer:GetSOLData()
    local solMax = self:GetSolMaxLevel()
    local sol = {}
    for key, data in ipairs(self:_GetLevelExpansionTable() or {}) do
        if data then
            local item = self:_GetItemByData(data)
            if item then
                if data.Mode == 1 then
                    if sol[data.Level] == nil then
                        sol[data.Level] = {}
                    end
                    table.insert(sol[data.Level], item)
                end
            end
        end
    end
    --构建空数据
    for i = 1, solMax, 1 do
        if sol[i] == nil then
            sol[i] = {}
            local item = self:_GetItemByData({Mode = 1, IsEmpty = true, IsIcon = 1})
            if item and sol[i] then
                table.insert(sol[i], item)
            end
        end
    end
    return sol
end

function GrowthRoadServer:GetMPData()
    local mpMax = self:GetMpMaxLevel()
    local mp = {}
    for key, data in ipairs(self:_GetLevelExpansionTable() or {}) do
        if data then
            local item = self:_GetItemByData(data)
            if item then
                if data.Mode ~= 1 then
                    if mp[data.Level] == nil then
                        mp[data.Level] = {}
                    end
                    table.insert(mp[data.Level], item)
                end
            end
        end
    end
    for i = 1, mpMax, 1 do
        if mp[i] == nil then
            mp[i] = {}
        end
        --合并数据
        local list = Server.RoleInfoServer:GetAccountUnlockItemsByLevel(i)
        for index, itemData in ipairs(list or {}) do
            if itemData then
                local item = self:_GetItemByData({
                    Mode = 2,
                    IsIcon = 2,
                    Info = itemData.id,
                    IsReward = itemData.reward_type,
                    IsBind   = itemData.isBind,
                })
                if item then
                    table.insert(mp[i], item)
                end
            end
        end
        if #mp[i] == 0 then
            local item = self:_GetItemByData({Mode = 2, IsEmpty = true, IsIcon = 1})
            if item and mp[i] then
                table.insert(mp[i], item)
            end
        end
    end
    return mp
end

function GrowthRoadServer:_GetAllDataTable(mode)
    local solMax = self:GetSolMaxLevel()
    local mpMax = self:GetMpMaxLevel()
    local sol = {}
    local mp = {}
    for key, data in ipairs(self:_GetLevelExpansionTable() or {}) do
        if data then
            local item = self:_GetItemByData(data)
            if item then
                if data.Mode == 1 then
                    if sol[data.Level] == nil then
                        sol[data.Level] = {}
                    end
                    table.insert(sol[data.Level], item)
                else
                    if mp[data.Level] == nil then
                        mp[data.Level] = {}
                    end
                    table.insert(mp[data.Level], item)
                end
            end
        end
    end
    --构建空数据
    for i = 1, solMax, 1 do
        if sol[i] == nil then
            sol[i] = {}
            local item = self:_GetItemByData({Mode = 1, IsEmpty = true, IsIcon = 1})
            if item and sol[i] then
                table.insert(sol[i], item)
            end
        end
    end
    for i = 1, mpMax, 1 do
        if mp[i] == nil then
            mp[i] = {}
        end
        --合并数据
        local list = Server.RoleInfoServer:GetAccountUnlockItemsByLevel(i)
        for index, itemData in ipairs(list or {}) do
            if itemData then
                local item = self:_GetItemByData({
                    Mode = 2,
                    IsIcon = 2,
                    Info = itemData.id,
                    IsReward = itemData.reward_type,
                    IsBind   = itemData.isBind,
                })
                if item then
                    table.insert(mp[i], item)
                end
            end
        end
        if #mp[i] == 0 then
            local item = self:_GetItemByData({Mode = 2, IsEmpty = true, IsIcon = 1})
            if item and mp[i] then
                table.insert(mp[i], item)
            end
        end
    end
    if mode == 1 then
        return sol
    else
        return mp
    end
end

function GrowthRoadServer:_GetItemByData(data)
    if data then
        local itemData = nil
        if data.IsIcon == 2 then
            itemData = self:_GetItemDataById(data.Info, data.Num)
        end
        local title = data.Title
        local name = data.Name
        local description = data.Desc
        if itemData then
            --名称
            if name == nil or string.len(name) == 0 then
                name = itemData.name
            end
            --分类
            if title == nil or string.len(title) == 0 then
                if string.len(itemData.subName or "") > 0 then
                    title = itemData.subName
                else
                    title = itemData.itemSubTagName
                end
            end
            --描述
            local desc = self:_GetSkinDescription(itemData)
            if description == nil or string.len(description) == 0 then
                if desc and string.len(desc) > 0 then
                    description = desc
                else
                    description = itemData.description
                end
            end
            --设置是否奖励
            itemData.reward_type = data.IsReward
        end
        return {
            battleType = data.Mode   or 1 ,
            isEmpty  = data.IsEmpty  or false,
            isReward = data.IsReward == 1 ,
            itemType = title         or "",
            itemName = name          or "",
            itemDesc = description   or "",
            iconType = data.IsIcon   or 1 ,
            iconPath = data.Info     or "",
            itemData = itemData,
            itemNum  = data.Num      or 0 ,
            isMap    = data.IsMap    or 0 ,
            isBind   = data.IsBind   or 0 ,
        }
    end
end

function GrowthRoadServer:_GetItemDataById(id, num, gid)
    if id == nil then
        return
    end
    --构建数据
    local prop = {
        id  = tonumber(id),
        num = num or 1,
        gid = gid or 0,
        components = {},
    }
    local itemData = ItemBase:New(prop.id, prop.num, prop.gid)
    if itemData == nil then
        return
    end
    --类型设置
    itemData.bindType = PropBindingType.BindingNotBind
    itemData.exceedPercent = prop.exceed_percent or 0
    --武器皮肤(玄学皮肤/普通皮肤)
    if itemData.SetRawPropInfo and itemData.GetFeature then
        local weapon = itemData:GetFeature(EFeatureType.Weapon)
        if weapon and weapon.IsWeapon and weapon:IsWeapon() then
            --设置武器皮肤信息(模型/图标)
            local itemPresetId = WeaponAssemblyTool.GetPreviewGunItemIdFromRecId(prop.id, EArmedForceMode.SOL)
            if itemPresetId then
                local rawPropInfo = WeaponAssemblyTool.PresetRow_to_PropInfo(itemPresetId)
                if rawPropInfo then
                    itemData:SetRawPropInfo(rawPropInfo)
                end
            end
        else
            --标准预设(图标皮肤处理)
            itemData:SetRawPropInfo(prop)
        end
    end
    return itemData
end

--获取成长之路等级数据
function GrowthRoadServer:_GetLevelExpansionTable()
    local list = Facade.TableManager:GetTable("LevelExpansion")
    local datas = {}
    for key, value in pairs(list or {}) do
        if value then
            table.insert(datas, value)
        end
    end
    if datas then
        table.sort(datas, function (a, b)
            if a and b and a.Id and b.Id then
                if a.Id ~= b.Id then
                    return a.Id < b.Id
                end
            end
            return false
        end)
    end
    return datas
end

function GrowthRoadServer:GetSolMaxLevel()
    if self._solMax then
        return self._solMax
    end 
    self._solMax = 0
    for key, data in pairs(Facade.TableManager:GetTable("SeasonLevel") or {}) do
        if data and tonumber(data.Experience) == 0 then
            self._solMax = data.Level
        end
    end
    return self._solMax
end

function GrowthRoadServer:GetMpMaxLevel()
    if self._mpMax then
        return self._mpMax
    end
    self._mpMax = 0
    self._mpMax = Server.RoleInfoServer:GetAccountMaxLevel()
    for key, data in pairs(Facade.TableManager:GetTable("AccountRewards") or {}) do
        if data and tonumber(data.AccountLevel) > self._mpMax then
            self._mpMax = data.AccountLevel
        end
    end
    for key, data in pairs(self:_GetLevelExpansionTable() or {}) do
        if data and data.Mode == 2 and data.Level > self._mpMax then
            self._mpMax = data.Level
        end
    end
    return self._mpMax
end

return GrowthRoadServer