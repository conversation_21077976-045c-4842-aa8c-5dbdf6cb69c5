----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRoleInfo)
----- LOG FUNCTION AUTO GENERATE END -----------
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local RoleInfoLogic = require "DFM.Business.Module.RoleInfoModule.Logic.RoleInfoLogic"
local gameInst = GetGameInstance()
local RoleInfoMainPanel = ui("RoleInfoMainPanel")

local Config = Module.RoleInfo.Config

---@class RoleInfoMainPanel : LuaUIBaseView

function RoleInfoMainPanel:Ctor()
    Module.CommonBar:RegStackUITopBarTitle(self, Config.Loc.RoleMainTitle)
    self._wtRootPanel = self:Wnd("wtRootPanel", UIWidgetBase)

    self._imgTable = {
        nil,
        "PaperSprite'/Game/UI/UIAtlas/System/RoleInfo/Sp/RoleInfo_Sp_06.RoleInfo_Sp_06'",
        "PaperSprite'/Game/UI/UIAtlas/System/RoleInfo/Sp/RoleInfo_Sp_06.RoleInfo_Sp_06'",
        "PaperSprite'/Game/UI/UIAtlas/Common/sp/C4X4/Common_OutSideBG.Common_OutSideBG'",
        "PaperSprite'/Game/UI/UIAtlas/Common/sp/C4X4/Common_OutSideBG.Common_OutSideBG'",
        "PaperSprite'/Game/UI/UIAtlas/System/RoleInfo/Sp/RoleInfo_Reputation_Sp_00.RoleInfo_Reputation_Sp_00'",
    }

    if IsHD() then
        local ETopBarStyleFlag = Module.CommonBar.Config.ETopBarStyleFlag
        Module.CommonBar:RegStackUITopBarStyle(
            self,
            ETopBarStyleFlag.DefaultSecondary &
            ~(ETopBarStyleFlag.Team | ETopBarStyleFlag.Friends | ETopBarStyleFlag.Currency)
        )
    else
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.HideInfo)
    end

    self._index = 1
    self._recordIndex = 1
    self._detailIndex = 1
    self._isMainTabClick = false -- 是一级标签，导致的二级页签点击
    self._bAssign = false
    self._bSelfOpen = false -- 点击打开的，不是栈处理打开

    -- BEGIN MODIFICATION @ VIRTUOS : Gamepad switch fire mode input(Game) will trigger by this UI. So need to change it to UIOnly
    if IsHD() then
        self:SetCppValue("WantedInputMode", EGPInputModeType.UIOnly)
    end
    -- END MODIFICATION
end

function RoleInfoMainPanel:OnInitExtraData(playerId)
    self._uiNavIDList = {
        UIName2ID.RoleInfoPersonal,
        UIName2ID.RoleInfoRecord,
        UIName2ID.RoleInfoMainDetail,
    }

    self._bSelfOpen = true
    self._index = 1
    if RoleInfoLogic.IsInMp() then
        self._recordIndex = 2
        self._detailIndex = 2
    else
        self._recordIndex = 1
        self._detailIndex = 1
    end
    self._playerId = setdefault(playerId, Server.AccountServer:GetPlayerId())
    if self._playerId == 0 then
        self._playerId = Server.AccountServer:GetPlayerId()
    end
    Facade.UIManager:RegSwitchSubUI(self, self._uiNavIDList)
    self:OnSetRoleInfoTab()
    self:AddLuaEvent(Server.RoleInfoServer.Events.evtPlayerDeregister, self._QuitPanel, self)
    --******拉取一下MP武器数据
    Server.RoleInfoServer:FetchAccountUnlockItems()
end

function RoleInfoMainPanel:OnActivate()
    Facade.UIManager:RegSwitchSubUI(self, self._uiNavIDList)
end

function RoleInfoMainPanel:OnDeactivate()
    Facade.UIManager:UnRegSwitchSubUI(self)
end

function RoleInfoMainPanel:OnShowBegin()
    self:AddLuaEvent(LuaGlobalEvents.evtSceneLoaded, self._RefrshRoleMode, self)
    self:AddLuaEvent(Config.Event.evtRefreshSelfModel, self._ChangeRoleMode, self)
    self:AddLuaEvent(Config.Event.evtSaveSelfModel, self._SaveSelfModel, self)

    if self._playerId == Server.AccountServer:GetPlayerId() then
        table.insert(self._uiNavIDList, UIName2ID.RoleInfoAchievement)
        table.insert(self._uiNavIDList, UIName2ID.RoleInfoSocialChange)
        if Module.Reputation:ModuleFuncIsUnlock() then
            table.insert(self._uiNavIDList, UIName2ID.ReputationMain)
        end
        self:AddLuaEvent(Config.Event.evtOpenSocialChangePanel, self._OpenSocialChangePanel, self)
    end
    self:_RefrshRoleMode(ESubStage.HallIndividual)

    Config.Event.evtRoleMainPanelOpen:Invoke()
end

function RoleInfoMainPanel:OnHideBegin()
    Config.Event.evtRoleMainPanelClose:Invoke()
    self:RemoveAllLuaEvent()
    self._bSelfOpen = false
end

function RoleInfoMainPanel:_OpenSocialChangePanel(index)
    self._bAssign = true

    Module.CommonBar:CheckTopTabGroup(5, true, 2)
    Module.CommonBar:CheckTopTabGroup(index, true, 4)
end

function RoleInfoMainPanel:OnSetRoleInfoTab()
    local roleTabTxtList = { Config.Loc.RoleInformationTxt,
        Config.Loc.RolePlayerRecordTxt, Config.Loc.RoleDetailTabTxt }
    local roleImgPathList = { Config.InformationImagePath, Config.RecordImagePath,
        Config.DetailImagePath }
    local recordTabTextList = {
        Config.Loc.RoleDroTxtSol,
        Config.Loc.RoleDroTxtMP,
    }
    -- TODO: old version remove SocialTab(index)
    local socialTabTextList = {
        Config.Loc.SocialAvatarTab,
        Config.Loc.SocialMilitaryTab,
        Config.Loc.SocialTitleTab,
        Config.Loc.SocialBadgeTab,
    }
    local reddotTrieRegItemList = {}
    local roleTertiaryTabs = {
        [2] = {
            tabTxtList = recordTabTextList,
            fCallbackIns = SafeCallBack(self._OnRecordTabClick, self),
            defalutIdx = self._recordIndex,
            bPostCallbackWhenPop = true
        },
        [3] = {
            tabTxtList = recordTabTextList,
            fCallbackIns = SafeCallBack(self._OnDetailTabClick, self),
            defalutIdx = self._detailIndex,
            bPostCallbackWhenPop = true
        }
    }
    if IsHD() then
        roleTertiaryTabs[2].imgPathList = {
            "PaperSprite'/Game/UI/UIAtlas/System/RoleInfo/BakedSprite/RoleInfo_Btn_13.RoleInfo_Btn_13'",
            "PaperSprite'/Game/UI/UIAtlas/System/RoleInfo/BakedSprite/RoleInfo_Btn_12.RoleInfo_Btn_12'",
        }
        roleTertiaryTabs[3].imgPathList = {
            "PaperSprite'/Game/UI/UIAtlas/System/RoleInfo/BakedSprite/RoleInfo_Btn_13.RoleInfo_Btn_13'",
            "PaperSprite'/Game/UI/UIAtlas/System/RoleInfo/BakedSprite/RoleInfo_Btn_12.RoleInfo_Btn_12'",
        }
    end
    if self._playerId == Server.AccountServer:GetPlayerId() then
        local reddotTrieRegItemListSocial = {}

        -- TODO: old version remove SocialTab(index)
        for index = 1, 4 do
            table.insert(reddotTrieRegItemListSocial, {
                uiNavId = UIName2ID.TopBar,
                reddotDataConfigWithStyleList = { { obType = EReddotTrieObserverType.RoleInfo,
                    key = "RoleInfoMessage_Type5_Social" .. index } }
            })
        end

        roleTertiaryTabs[5] = {
            tabTxtList = socialTabTextList,
            fCallbackIns = SafeCallBack(self._OnSocialTabClick, self),
            defalutIdx = 1,
            bPostCallbackWhenPop = true,
            bNewReddotTrie = true,
            reddotTrieRegItemList = reddotTrieRegItemListSocial,
        }
        if IsHD() then
            roleTertiaryTabs[5].imgPathList = {
                "PaperSprite'/Game/UI/UIAtlas/System/RoleInfo/BakedSprite/RoleInfo_Btn_09.RoleInfo_Btn_09'",
                "PaperSprite'/Game/UI/UIAtlas/System/RoleInfo/BakedSprite/RoleInfo_Btn_10.RoleInfo_Btn_10'",
                "PaperSprite'/Game/UI/UIAtlas/System/RoleInfo/BakedSprite/RoleInfo_Btn_11.RoleInfo_Btn_11'",
                "PaperSprite'/Game/UI/UIAtlas/System/RoleInfo/BakedSprite/RoleInfo_Btn_12.RoleInfo_Btn_12'"
            }
        end

        table.insert(roleTabTxtList, Config.Loc.AchievementTabTxt)
        table.insert(roleImgPathList, Config.AchievementImagePath)
        table.insert(roleTabTxtList, Config.Loc.RoleSocialTabTxt)
        table.insert(roleImgPathList, Config.SocialImagePath)

        -- TODO: old version remove SocialTab (index)
        for index = 1, 5 do
            table.insert(reddotTrieRegItemList, {
                uiNavId = UIName2ID.TopBar,
                reddotDataConfigWithStyleList = { { obType = EReddotTrieObserverType.RoleInfo,
                    key = "RoleInfoMessage_Type" .. index } }
            })
        end
    end

    self:OnReputationTabInit(roleTertiaryTabs, roleTabTxtList, roleImgPathList)

    Module.CommonBar:RegStackUITopBarTabGroupRegInfo(self, {
        tabTxtList = roleTabTxtList,
        imgPathList = roleImgPathList,
        fCallbackIns = SafeCallBack(self._OnMainTabClick, self),
        tabGroupSize = FVector2D(1136, 96),
        tabSpaceMargin = FMargin(0, 0, 16, 0),
        defalutIdx = self._index,
        tertiaryTabs = roleTertiaryTabs,
        bPostCallbackWhenPop = true,
        bNewReddotTrie = true,
        reddotTrieRegItemList = reddotTrieRegItemList,
    })
end

function RoleInfoMainPanel:OnReputationTabInit(roleTertiaryTabs, roleTabTxtList, roleImgPathList)
    if (not Module.Reputation:ModuleFuncIsUnlock()) then
        return roleTertiaryTabs, roleTabTxtList, roleImgPathList
    end

    if self._playerId == Server.AccountServer:GetPlayerId() then
        roleTertiaryTabs[6] = {
            tabTxtList = {},
            fCallbackIns = SafeCallBack(self._OnReputationTabClick, self),
            defalutIdx = 1,
            bPostCallbackWhenPop = true,
            bNewReddotTrie = false,
        }

        table.insert(roleTabTxtList, Config.Loc.ReputationTabTxt)
        table.insert(roleImgPathList, Config.ReputationImagePath)
    end
end

function RoleInfoMainPanel:GetSocialDefaultIdx()
    if Server.HeroServer:HasUnreadAccessorryByType(Module.Hero:GetCurShowHeroId(), EHeroAccessroy.Badge, false) then
        return 4
    end

    local socialTbl
    for i = 1, 3, 1 do
        socialTbl = Server.RoleInfoServer:GetSocialTypeRedPoint(i)
        if socialTbl and #socialTbl > 0 then
            return i
        end
    end
    return 1
end

function RoleInfoMainPanel:OnOpen()
    Facade.UIManager:SwitchSubUIByIndex(self, 0, self._wtRootPanel, self._playerId)
end

function RoleInfoMainPanel:OnClose()
    Facade.UIManager:UnRegSwitchSubUI(self)
end

function RoleInfoMainPanel:FreshBGImage()
    local bg = self._imgTable[self._index]
    Facade.UIManager:ManuelRefreshBackgroundUI(self.UINavID, bg)
end

function RoleInfoMainPanel:_OnMainTabClick(mainIndex, lastIndex, TabLevel)
    self._index = mainIndex
    local weakUiIns, instanceId = Facade.UIManager:SwitchSubUIByIndex(self, mainIndex, self._wtRootPanel, self._playerId)
    self._instanceId = instanceId
    self._weakUiIns = weakUiIns
    self:FreshBGImage()

    self._isMainTabClick = true

    if mainIndex == 5 and self._bSelfOpen and self._isMainTabClick and self._bAssign == false then -- 这里会和信息也打开军牌 头像等冲突，_bAssign 控制
        Timer.DelayCall(0.3, function()
            local subIndex = self:GetSocialDefaultIdx()
            if subIndex ~= nil and subIndex ~= 1 then
                Module.CommonBar:CheckTopTabGroup(subIndex, true, 4)
            end
        end)
    end
end

function RoleInfoMainPanel:_OnRecordTabClick(mainIndex, lastIndex, TabLevel)
    self._recordIndex = mainIndex
    local weakUIIns = Facade.UIManager:GetSubUI(self, UIName2ID.RoleInfoMPDetail, self._instanceId)
    local uiIns = getfromweak(self._weakUiIns)
    if uiIns then
        uiIns:OnRecordTabClick(mainIndex)
    end
end

function RoleInfoMainPanel:_OnDetailTabClick(mainIndex, lastIndex, TabLevel)
    self._detailIndex = mainIndex
    local weakUIIns = Facade.UIManager:GetSubUI(self, UIName2ID.RoleInfoSocialChange, self._instanceId)
    local uiIns = getfromweak(self._weakUiIns)
    if uiIns then
        uiIns:OnDetailTabClick(mainIndex)
    end
end

function RoleInfoMainPanel:_OnSocialTabClick(mainIndex, lastIndex, TabLevel)
    local weakUIIns = Facade.UIManager:GetSubUI(self, UIName2ID.RoleInfoSocialChange, self._instanceId)
    local uiIns = getfromweak(self._weakUiIns)

    if uiIns then
        self._bAssign = false
        self._isMainTabClick = false
        uiIns:OnSocialTabClick(mainIndex)
    end
end

function RoleInfoMainPanel:_OnReputationTabClick(mainIndex, lastIndex, TabLevel)
    local weakUIIns = Facade.UIManager:GetSubUI(self, UIName2ID.ReputationMain, self._instanceId)
    local uiIns = getfromweak(self._weakUiIns)
    if uiIns then
        uiIns:OnReputationTabClick(mainIndex)
    end
end

function RoleInfoMainPanel:_SetDisplayType()
    local TypeName = "CharacterR"
    if IsHD() then TypeName = "CharacterR_HD" end
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallIndividual, "SetDisplayType", TypeName)
end

function RoleInfoMainPanel:_RefrshRoleMode(curSubStageType)
    self:_SetDisplayType()
    if not curSubStageType or curSubStageType == ESubStage.HallIndividual then
        local SetPlayerModule = CreateCallBack(function(self, res)
            local appearance = res.appearance
            local fashionSuitId = 0
            for _, v in ipairs(appearance.hero.fashion_equipped) do
                if v.slot == eHeroFashionPosition.FashionSuit then
                    fashionSuitId = v.id
                    break
                end
            end
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallIndividual, "SetCharacterAvatar",
                fashionSuitId)
            local character = Facade.HallSceneManager:GetSceneCharacterAvatarBySubstage(ESubStage.HallIndividual)
            if character then
                character.HallCharacterSetup:Add(CreateCPlusCallBack(self.OnHallCharacterSetup, self))
            end
            local CurUsedWatchItemId = 0
            for _, v in ipairs(res.appearance.hero.accessories) do
                if v.is_selected then
                    if Server.HeroServer:IsWatchAccessory(v.item.prop_id) then
                        CurUsedWatchItemId = v.item.prop_id
                        break
                    end
                end
            end

            local GMWatchItemId = Module.LobbyDisplay:GetDisplayWatchItemId()
            if GMWatchItemId ~= 0 then
                CurUsedWatchItemId = GMWatchItemId
            end
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallIndividual, "SetCharacterWatch",
                CurUsedWatchItemId)
            self.equipInfo = appearance.equip_props
            for _, info in ipairs(appearance.equip_props) do
                if info.position == ESlotType.MainWeaponLeft or info.position == ESlotType.MP_MainWeapon then
                    local propsInfo = info.load_props[1]
                    local weapDesc = WeaponAssemblyTool.PropInfo_To_Desc(propsInfo)
                    --WeaponAssemblyTool.SetWeaponDescriptionSkinInfoFromPropInfo(weapDesc, appearance.equip_skin_props)
                    Facade.HallSceneManager:CallSceneCharacterAvatarFunctionBySubstage(ESubStage.HallIndividual,
                        "EquipWeapon", weapDesc)


                elseif info.position == ESlotType.Bag or info.position == ESlotType.Helmet or
                    info.position == ESlotType.ChestHanging or info.position == ESlotType.BreastPlate then
                    Facade.HallSceneManager:CallSceneCharacterAvatarFunctionBySubstage(ESubStage.HallIndividual, "Equip"
                        , tostring(info.load_props[1].id))
                end
            end
        end, self)

        if Server.ArmedForceServer:GetCurArmedForceMode() == EArmedForceMode.MP then
            Server.RoleInfoServer:GetMPPlayerShowAvatarId(self._playerId, SetPlayerModule)
        else
            Server.RoleInfoServer:GetSolPlayerShowAvatarId(self._playerId, SetPlayerModule)
        end
    end
end

function RoleInfoMainPanel:OnHallCharacterSetup()
    if self.changeWeapon then
        local weaponDesc = WeaponAssemblyTool.PropInfo_To_Desc(self.changeWeapon.rawPropInfo)
        Facade.HallSceneManager:CallSceneCharacterAvatarFunctionBySubstage(ESubStage.HallIndividual, "EquipWeapon",
            weaponDesc)
        self.changeWeapon = nil
    end

    if self.equipInfo then
        for _, info in ipairs(self.equipInfo) do
            if info.position == ESlotType.MainWeaponLeft or info.position == ESlotType.MP_MainWeapon then
                local propsInfo = info.load_props[1]
                local weapDesc = WeaponAssemblyTool.PropInfo_To_Desc(propsInfo)
                --WeaponAssemblyTool.SetWeaponDescriptionSkinInfoFromPropInfo(weapDesc, appearance.equip_skin_props)
                Facade.HallSceneManager:CallSceneCharacterAvatarFunctionBySubstage(ESubStage.HallIndividual,
                    "EquipWeapon", weapDesc)


            elseif info.position == ESlotType.Bag or info.position == ESlotType.Helmet or
                info.position == ESlotType.ChestHanging or info.position == ESlotType.BreastPlate then
                Facade.HallSceneManager:CallSceneCharacterAvatarFunctionBySubstage(ESubStage.HallIndividual, "Equip",
                    tostring(info.load_props[1].id))
            end
        end
        self.equipInfo = nil
    end
end

function RoleInfoMainPanel:_ChangeRoleMode()
    Module.CommonBar:BindBackHandler(self._EnterQuit, self)
    local FashionId = Server.HeroServer:GetCurUsedHeroFashionId()
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallIndividual, "SetCharacterAvatar", FashionId)

    local curUsedWatchItemId = Server.HeroServer:GetUsedWatchAccessory(FashionId)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallIndividual, "SetCharacterWatch",
        curUsedWatchItemId)

    local firstMainWeapon = nil
    if RoleInfoLogic.IsInMp() then
        firstMainWeapon = Server.ArmedForceServer:GetCurModeFirstMainWeaponItem()
    else
        firstMainWeapon = Module.LobbyDisplay:GetMainWeaponItemForShow()
    end
    if firstMainWeapon then
        self.changeWeapon = firstMainWeapon
        local weaponDesc = WeaponAssemblyTool.PropInfo_To_Desc(firstMainWeapon.rawPropInfo)
        Facade.HallSceneManager:CallSceneCharacterAvatarFunctionBySubstage(ESubStage.HallIndividual, "EquipWeapon",
            weaponDesc)
    else
        Facade.HallSceneManager:CallSceneCharacterAvatarFunctionBySubstage(ESubStage.HallIndividual, "UnequipWeapon")
    end
end

function RoleInfoMainPanel:_SaveSelfModel()
    Module.CommonBar:BindBackHandler(nil, self)
end

function RoleInfoMainPanel:_EnterQuit()
    local function CloseFunc()
        Facade.UIManager:CloseUI(self)
    end

    Module.CommonTips:ShowConfirmWindow(Config.Loc.QuitRoleMain,
        CloseFunc, nil,
        Module.CommonWidget.Config.Loc.CancelTextDefault,
        Module.CommonWidget.Config.Loc.ConfirmTextDefault)
end

function RoleInfoMainPanel:_QuitPanel()
    Facade.UIManager:CloseUI(self)
end

return RoleInfoMainPanel
