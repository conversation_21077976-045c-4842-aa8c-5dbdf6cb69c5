----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMReward)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class SafeBoxGainPop : LuaUIBaseView
local RewardBaseView = require "DFM.Business.Module.RewardModule.UI.RewardBaseView"
local SafeBoxGainPop = ui("SafeBoxGainPop", RewardBaseView)
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local EGPInputModeType = import "EGPInputModeType"
local RewardConfig = Module.Reward.Config
local CollectionConfig = Module.Collection.Config
local RewardDetail = require "DFM.Business.Module.RewardModule.UI.RewardDetail"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPInputType = import "EGPInputType"
local UGPInputDelegates = import "GPInputDelegates"

function SafeBoxGainPop:Ctor()
    self._wtRewardDetail = self:Wnd("wtRewardDetail", RewardDetail)
    self._wtRewardDetail:BindJumpClick(self._OnSkipBtnClick, self)
    self._wtPropImg_1 = self:Wnd("wtPropImg_1", DFCDNImage)
    self._wtPropImg_2 = self:Wnd("wtPropImg_2", DFCDNImage)
    self._wtPropImgBox = self:Wnd("wtPropImgBox", UIWidgetBase)
    self._wtPropImg_3 = self:Wnd("wtPropImg_3", DFCDNImage)
    self._wtBtnPanel = self:Wnd("wtBtnPanel", UIWidgetBase)
    self._wtSkipBtn = self:Wnd("wtSkipBtn", DFCommonButtonOnly)
    self._wtSkipBtn:Event("OnClicked", self._OnSkipBtnClick, self)
    self._wtApplyBtn = self:Wnd("wtApplyBtn", CommonButton)
    self._wtApplyBtn:Event("OnClicked", self._ApplyBoxSkin, self)
    self._closeClickCount = -1
end


function SafeBoxGainPop:OnInitExtraData(safeBoxId)
    self._safeBoxId = safeBoxId or 0
    local itemMainType = ItemHelperTool.GetMainTypeById(self._safeBoxId)
    local itemSubType = ItemHelperTool.GetSubTypeById(self._safeBoxId)
    self._bIsBoxAppearance = itemMainType ~= EItemType.Equipment or itemSubType ~= EEquipmentType.SafeBox
end

function SafeBoxGainPop:OnOpen()
    self:AddListeners()
end

function SafeBoxGainPop:OnClose()
    self:RemoveAllLuaEvent()
    self:RemoveJumpInputAction()
    if self._bExecuteClose ~= true then
        self._bExecuteClose = true
        Module.Reward:ShowNextRewards()
    end
end

function SafeBoxGainPop:OnShowBegin()
    self:_RefreshWidget()
    if not self._inputTypeChangedHandle then 
        self._inputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._OnInputTypeChanged, self))
    end
    self:AddJumpInputAction()
    self:_EnableGamepadFeature()
end

function SafeBoxGainPop:OnHideBegin()
    if self._inputTypeChangedHandle then
        UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._inputTypeChangedHandle)
        self._inputTypeChangedHandle = nil
    end
    self:_DisableGamepadFeature()
end

function SafeBoxGainPop:OnShow()
    self:SetCPPValue("WantedInputMode", EGPInputModeType.UIOnly)
end

function SafeBoxGainPop:OnHide()
end

function SafeBoxGainPop:OnAnimFinished(anim)
    if anim == self.WBP_Vehicle_Obtain_in then
        if self._closeClickCount < 1 then
            self._closeClickCount = self._closeClickCount + 1
        end
    elseif anim == self.WBP_Vehicle_Obtain_out then
        if self._bExecuteClose ~= true then
            self._bExecuteClose = true
            Module.Reward:ShowNextRewards(self._bTabPressed == true)
        end
    end
end

function SafeBoxGainPop:AddListeners()
    self:AddLuaEvent(Server.CollectionServer.Events.evtEquipSafeBoxSkin, self._OnBoxSkinApplied, self)
    self:AddLuaEvent(Module.IrisSafeHouse.Config.evtTabBackToSafeHouseHD, self._OnTabPressed, self)
end

function SafeBoxGainPop:_OnInputTypeChanged(inputType)
    if IsHD() and inputType == EGPInputType.Gamepad then
        self:RemoveJumpInputAction()
        self:_EnableGamepadFeature()
    else
        self:_DisableGamepadFeature()
        self:AddJumpInputAction()
    end
end

function SafeBoxGainPop:_EnableGamepadFeature()
    if not IsHD() or not WidgetUtil.IsGamepad() or not self._bIsBoxAppearance then
        return
    end
    if self._wtSkipBtn then
        self._wtSkipBtn:SetDisplayInputAction("Reward_Continue_Gamepad", true, nil, true)
    end
    if self._wtApplyBtn then
        self._wtApplyBtn:SetDisplayInputAction("Common_ButtonLeft", true, nil, true)
    end
    -- 跳过按键响应
    if not self._skipHandle then
        self._skipHandle = self:AddInputActionBinding("Reward_Continue_Gamepad", EInputEvent.IE_Pressed, self._OnSkipBtnClick, self, EDisplayInputActionPriority.UI_Pop)
    end
    -- 应用按键响应
    if not self._applyHandle then
        self._applyHandle = self:AddInputActionBinding("Common_ButtonLeft", EInputEvent.IE_Pressed, self._ApplyBoxSkin, self, EDisplayInputActionPriority.UI_Pop)
    end
    WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self, WidgetUtil.ENavConfigPriority.UI_Pop)
end


function SafeBoxGainPop:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end
    if self._skipHandle then
        self:RemoveInputActionBinding(self._skipHandle)
    end
    if self._applyHandle then
        self:RemoveInputActionBinding(self._applyHandle)
    end
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    self._skipHandle = nil
    self._applyHandle = nil
end

function SafeBoxGainPop:AddJumpInputAction()
    if IsHD() and not WidgetUtil.IsGamepad() then
        if not self._skipHandle then
            self._skipHandle = self:AddInputActionBinding(
                "JumpOver",
                EInputEvent.IE_Pressed,
                self._OnSkipBtnClick,
                self,
                EDisplayInputActionPriority.UI_Pop
            )
        end
    end
end

function SafeBoxGainPop:RemoveJumpInputAction()
    if self._skipHandle then
        self:RemoveInputActionBinding(self._skipHandle)
        self._skipHandle = nil
    end
end

function SafeBoxGainPop:_RefreshWidget()
    if self._closeClickCount < 1 then
        self._closeClickCount = self._closeClickCount + 1
    end
    local boxItemConfigRow = ItemConfigTool.GetItemConfigById(self._safeBoxId)
    self._wtRewardDetail:SetShowSkipPanel(not self._bIsBoxAppearance)
    self._wtRewardDetail:SetShowSkipTxt(not self._bIsBoxAppearance)
    self._wtRewardDetail:SetType(2)
    self._wtRewardDetail:SetMainTitle(self._bIsBoxAppearance and RewardConfig.Loc.ObtainSafeBoxAppearance or RewardConfig.Loc.ObtainSafeBoxAuthority)
    self._wtRewardDetail:SetDesc()
    if self._bIsBoxAppearance then
        local boxAppearanceItemConfigRow = ItemConfigTool.GetItemConfigById(self._safeBoxId)
        self._wtRewardDetail:SetQuality(boxAppearanceItemConfigRow and boxAppearanceItemConfigRow.Quality or 0)
        self._wtRewardDetail:SetName(boxAppearanceItemConfigRow and boxAppearanceItemConfigRow.Name or RewardConfig.Loc.Unknown)
        self._wtApplyBtn:SetMainTitle(Server.CollectionServer.safeboxInfoEquiped ~= self._safeBoxId and CollectionConfig.Loc.ApplyAppearance or CollectionConfig.Loc.AppearanceApplied)
        self._wtApplyBtn:SetIsEnabled(Server.CollectionServer.safeboxInfoEquiped ~= self._safeBoxId)
        self._wtSkipBtn:SetMainTitle(CollectionConfig.Loc.Skip)
        self._wtSkipBtn:SetIsEnabled(true)
        self._wtBtnPanel:SelfHitTestInvisible()
    else
        self._wtRewardDetail:SetQuality(boxItemConfigRow and boxItemConfigRow.Quality or 0)
        self._wtRewardDetail:SetName(boxItemConfigRow and boxItemConfigRow.Name or RewardConfig.Loc.Unknown)
        self._wtBtnPanel:Collapsed()
    end
    Module.Collection:SetBackgroundImgByPropId(
        {self._wtPropImg_1, self._wtPropImg_2, self._wtPropImg_3},
        true, 
        nil, 
        nil, 
        self._safeBoxId, 
        self._wtPropImgBox)
    if self._closeClickCount < 1 then
        self:PlayAnimation(self.WBP_Vehicle_Obtain_in, 0, 1, EUMGSequencePlayMode.Forward, 1, true)
    end
    Facade.SoundManager:PlayUIAudioEvent("UI_Common_Popup")
    self:HandleTransition(false)
end

function SafeBoxGainPop:_ApplyBoxSkin()
    if self._safeBoxId and self._bIsBoxAppearance then
        Server.CollectionServer:EquipSafeBoxSkin(self._safeBoxId)
    end
end

function SafeBoxGainPop:_OnBoxSkinApplied()
    self._wtApplyBtn:SetMainTitle(Server.CollectionServer.safeboxInfoEquiped ~= self._safeBoxId and CollectionConfig.Loc.ApplyAppearance or CollectionConfig.Loc.AppearanceApplied)
    self._wtApplyBtn:SetIsEnabled(Server.CollectionServer.safeboxInfoEquiped ~= self._safeBoxId)
    Module.CommonTips:ShowSimpleTip(Server.CollectionServer.safeboxInfoEquiped == self._safeBoxId and CollectionConfig.Loc.AppearanceApplied or CollectionConfig.Loc.FailedToUse)
end

function SafeBoxGainPop:_OnSkipBtnClick()
    if self._closeClickCount == 1 then
        self._closeClickCount = 2
        self:HandleTransition(true)
        Facade.SoundManager:StopUIAudioEvent("UI_Common_Popup")
        self:PlayAnimation(self.WBP_Vehicle_Obtain_out, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    elseif self._closeClickCount == 0 then
        self._closeClickCount = 1
        self:SkipAnimation(self.WBP_Vehicle_Obtain_in)
        self._wtRewardDetail:SkipInAnimation()
    end
end

function SafeBoxGainPop:OnNavBack()
    self:_OnSkipBtnClick()
    return true
end


function SafeBoxGainPop:_OnTabPressed()
    self._bTabPressed = true
    Facade.SoundManager:StopUIAudioEvent("UI_Common_Popup")
    self:StopAnimation(self.WBP_Vehicle_Obtain_in)
    self:PlayAnimation(self.WBP_Vehicle_Obtain_out, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
end

return SafeBoxGainPop