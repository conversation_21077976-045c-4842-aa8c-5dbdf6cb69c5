----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollection)
----- LOG FUNCTION AUTO GENERATE END -----------



local CollectionLogic = require "DFM.Business.Module.CollectionModule.Logic.CollectionLogic"
--------------------------------------------------------------------------
--- UI路径映射配置示例（UILayer表示需要加入的层级）
--------------------------------------------------------------------------
UITable[UIName2ID.CollectionMainPanel] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionMainPanel",
    BPKey = "WBP_Collections_Main",
    SubUIs = {
        UIName2ID.CollectionsWeaponSkinPagePanel,
        UIName2ID.CollectionsMeleeSkinPagePanel,
        UIName2ID.CollectionMandelBrickPagePanel,
        UIName2ID.CollectionsPagePanel,
        UIName2ID.CollectionMysticalPendantWorkshopPagePanel,
        UIName2ID.CollectionMysticalSkinWorkshopPagePanel,
        UIName2ID.CollectionMysticalHallPagePanel,
        UIName2ID.CollectionsHangingPagePanel,
        UIName2ID.CollectionAppearanceCamouflagePagePanel,
        UIName2ID.CollectionAppearanceMasterPagePanel,
    },
    Anim = {
        FlowInAni = "WBP_Collections_Main_in",
        FlowOutAni = "WBP_Collections_Main_out",
    },
    --- 默认Cache配置
    ReConfig = {
        IsPoolEnable = true,
    },
    --LinkSubStage = ESubStage.HallCollectionNew,--HallWeaponShow
}
  

UITable[UIName2ID.CollectionsPageContainer] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionsPageContainer", 
    SubUIs = {
        UIName2ID.CollectionsWeaponSkinPagePanel,
        UIName2ID.CollectionsMeleeSkinPagePanel,
        UIName2ID.CollectionMandelBrickPagePanel,
        UIName2ID.CollectionsPagePanel,
        UIName2ID.CollectionMysticalSkinWorkshopPagePanel,
        UIName2ID.CollectionMysticalHallPagePanel,
        UIName2ID.CollectionsHangingPagePanel,
        UIName2ID.CollectionAppearanceCamouflagePagePanel,
        UIName2ID.CollectionAppearanceMasterPagePanel,
    },
    BPKey = "WBP_CollectionsPageContainer",
    Anim = {
    },
}


UITable[UIName2ID.CollectionsPagePanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionsPagePanel", 
    SubUIs = {
        UIName2ID.IVCommonItemTemplate,
        UIName2ID.CommonEmptyContent,
    },
    BPKey = "WBP_CollectionsPage",
    Anim = {
        FlowInAni = "WBP_CollectionsPage_in",
        FlowOutAni = "WBP_CollectionsPage_out",
    },
}

UITable[UIName2ID.CollectionProbabilityPop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionProbabilityPop", 
    SubUIs = {
        UIName2ID.CollectionProbabilityGroup,
    },
    BPKey = "WBP_Collections_Probability",
    Anim = {
    },
    IsModal = true,
}

UITable[UIName2ID.CollectionProbabilityGroup] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionProbabilityGroup", 
    SubUIs = {
        UIName2ID.StorePrizeBox,
    },
    BPKey = "WBP_Collections_Probability_Item",
}

UITable[UIName2ID.CollectionChangeNameView] = {
    UILayer = EUILayer.Pop, 
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionChangeNameView",
    BPKey = "WBP_Collections_ChangeName",
    Anim = {
        FlowInAni = "WBP_LoginSetname_in2",
        FlowOutAni = "WBP_LoginSetname_out2",
    },
    IsModal = true,
}
UITable[UIName2ID.CollectionPendantChangeNameView] = {
    UILayer = EUILayer.Pop, 
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionPendantChangeNameView",
    BPKey = "WBP_Collections_ChangeName",
    Anim = {
        FlowInAni = "WBP_LoginSetname_in2",
        FlowOutAni = "WBP_LoginSetname_out2",
    },
    IsModal = true,
}

UITable[UIName2ID.CollectionGunSkinFilterPanel] = {
    UILayer = EUILayer.Pop, 
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionGunSkinFilterPanel", 
    BPKey = "WBP_Collections_FilterPop",
    IsModal = true,
}


UITable[UIName2ID.CollectionRandomSkinOverviewPanel] = {
    UILayer = EUILayer.Pop, 
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionRandomSkinOverviewPanel", 
    BPKey = "WBP_Collections_RandomGunSkin",
    SubUIs = {
        UIName2ID.CollectionMysticalWorkshopItem
    },
    IsModal = true,
}


UITable[UIName2ID.CollectionHangingFilterPanel] = {
    UILayer = EUILayer.Pop, 
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionHangingFilterPanel", 
    BPKey = "WBP_Collections_FilterPop",
    SubUIs = {
    },
    Anim = {
    },
    IsModal = true,
}

UITable[UIName2ID.CollectionHangingDefaultPanel] = {
    UILayer = EUILayer.Pop, 
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionHangingDefaultPanel", 
    BPKey = "WBP_Collections_DefaultOrnaments",
    SubUIs = {
        UIName2ID.CollectionMysticalWorkshopItem
    },
    Anim = {
    },
    IsModal = true,
}


UITable[UIName2ID.CollectionsWeaponSkinPagePanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionsWeaponSkinPagePanel", 
    BPKey = "WBP_Collections_GunTypeMain",
    SubUIs = {
        UIName2ID.CollectionCommonItemView,
        UIName2ID.ItemDetailViewEquip,
        UIName2ID.CommonItemViewDropDownBox,
        UIName2ID.CollectionMysticalSkinBtn,
        UIName2ID.CommonEmptyContent,
    },
    Anim = {
        FlowInAni = "WBP_Collections_GunTypeMain_in",
        FlowOutAni = "WBP_Collections_GunTypeMain_out",
    },
}

UITable[UIName2ID.CollectionCommonItemView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionCommonItemView", 
    BPKey = "WBP_Collections_GunTypeList",
    SubUIs = {
        UIName2ID.IVShopItemTemplate,
    },
}

UITable[UIName2ID.CollectionsHangingPagePanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionsHangingPagePanel", 
    BPKey = "WBP_Collections_Pendant",
    SubUIs = {
        UIName2ID.IVShopItemTemplate,
        UIName2ID.ItemDetailEquipOrnaments,
        UIName2ID.CommonItemViewDropDownBox,
        UIName2ID.CollectionMysticalSkinBtn,
        UIName2ID.CommonEmptyContent,
    },
    Anim = {
        FlowInAni = "WBP_Collections_GunTypeMain_in",
        FlowOutAni = "WBP_Collections_GunTypeMain_out",
    },
}


UITable[UIName2ID.CollectionMysticalSkinBtn] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionMysticalSkinBtn", 
    BPKey = "WBP_Collections_ProductionButton",
    SubUIs = {
    },
    Anim = {
    }
}

UITable[UIName2ID.CollectionsMeleeSkinPagePanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionsMeleeSkinPagePanel", 
    BPKey = "WBP_Collections_BlueprintPage_Cutter",
    SubUIs = {
        UIName2ID.IVShopItemTemplate,
        UIName2ID.ItemDetailViewEquip,
        UIName2ID.CommonEmptyContent,
    },
    Anim = {
        FlowInAni = "WBP_Collections_BlueprintPage_Cutter_in",
        FlowOutAni = "WBP_Collections_BlueprintPage_Cutter_out",
    },
}

UITable[UIName2ID.CollectionMandelBrickPagePanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionMandelBrickPagePanel", 
    BPKey = "WBP_Collections_Mandel",
    SubUIs = {
        UIName2ID.IVShopItemTemplate,
        UIName2ID.ItemDetailViewEquip,
        UIName2ID.CommonEmptyContent,
    },
    Anim = {
        FlowInAni = "WBP_Collections_BlueprintPage_Cutter_in",
        FlowOutAni = "WBP_Collections_BlueprintPage_Cutter_out",
    },
}

UITable[UIName2ID.CollectionMysticalSkinWorkshopPagePanel] = {
    UILayer = EUILayer.Sub, 
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionMysticalSkinWorkshopPagePanel", 
    BPKey = "WBP_Collections_ProductionMatrix",
    SubUIs = {
        UIName2ID.CollectionMysticalWorkshopItem
    },
    Anim = {
        FlowInAni = "WBP_Collections_ProductionMatrix_in",
        FlowOutAni = "WBP_Collections_ProductionMatrix_out",
    },
}

UITable[UIName2ID.CollectionMysticalPendantWorkshopPagePanel] = {
    UILayer = EUILayer.Sub, 
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionMysticalPendantWorkshopPagePanel", 
    BPKey = "WBP_Collections_ProductionMatrix",
    SubUIs = {
        UIName2ID.CollectionMysticalWorkshopItem
    },
    Anim = {
        FlowInAni = "WBP_Collections_ProductionMatrix_in",
        FlowOutAni = "WBP_Collections_ProductionMatrix_out",
    },
}

UITable[UIName2ID.CollectionAppearanceCamouflagePagePanel] = {
    UILayer = EUILayer.Sub, 
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionAppearanceCamouflagePagePanel", 
    BPKey = "WBP_Collections_Camouflage",
    SubUIs = {
        UIName2ID.CollectionAppearanceWeaponSkinGroup,
        UIName2ID.CollectionAppearanceTaskItem,
        UIName2ID.ItemDetailTitleComp,
    },
    Anim = {
        FlowInAni = "WBP_Collections_Camouflage_in",
        FlowOutAni = "WBP_Collections_Camouflage_out",
    },
}

UITable[UIName2ID.CollectionAppearanceWeaponSkinGroup] = {
    UILayer = EUILayer.Sub, 
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionAppearanceWeaponSkinGroup", 
    BPKey = "WBP_Collections_Camouflage_Item_01",
    SubUIs = {
        UIName2ID.IVCommonItemTemplate,
    },
}

UITable[UIName2ID.CollectionAppearanceTaskItem] = {
    UILayer = EUILayer.Sub, 
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionAppearanceTaskItem", 
    BPKey = "WBP_Collections_Camouflage_Item_02",
}

UITable[UIName2ID.CollectionAppearanceMasterPagePanel] = {
    UILayer = EUILayer.Sub, 
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionAppearanceMasterPagePanel", 
    BPKey = "WBP_Collections_Master",
    SubUIs = {
        UIName2ID.IVCommonItemTemplate,
        UIName2ID.CollectionAppearanceMasterTaskItem,
        UIName2ID.ItemDetailTitleComp,
    },
    Anim = {
        FlowInAni = "WBP_Collections_Master_in",
        FlowOutAni = "WBP_Collections_Master_in",
    },
}

UITable[UIName2ID.CollectionAppearanceMasterTaskItem] = {
    UILayer = EUILayer.Sub, 
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionAppearanceMasterTaskItem", 
    BPKey = "WBP_Collections_Master_Item",
}

UITable[UIName2ID.CollectionAppearanceSwitchChallengePage] = {
    UILayer = EUILayer.Stack, 
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionAppearanceSwitchChallengePage", 
    BPKey = "WBP_Collections_SwitchChallenge",
    SubUIs = {
        UIName2ID.CollectionCommonItemView,
        UIName2ID.ItemDetailTitleComp,
    },
    Anim = {
        FlowInAni = "WBP_Collections_SwitchChallenge_in",
        FlowOutAni = "WBP_Collections_SwitchChallenge_in",
    },
    ReConfig = {
        IsPoolEnable = true,
    },
    LinkSubStage = ESubStage.HallCollectionNew,
}

UITable[UIName2ID.CollectionMysticalHallPagePanel] = {
    UILayer = EUILayer.Sub, 
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionMysticalHallPagePanel", 
    BPKey = "WBP_Collections_Production",
    SubUIs = {
        UIName2ID.CollectionSeasonSkinItem,
    },
    Anim = {
        bManuelAnim = true
    },
}

UITable[UIName2ID.CollectionSeasonSkinItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionSeasonSkinItem", 
    BPKey = "WBP_Collections_ProductionSeason",
    SubUIs = {
    },
    Anim = {
        FlowInAni = "WBP_Collections_ProductionSeason_in",
    }
}


UITable[UIName2ID.CollectionWeaponSkinDetailPage] = {
    UILayer = EUILayer.Stack, 
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionWeaponSkinDetailPage", 
    BPKey = "WBP_Collections_BlueprintDetail",
    SubUIs = {
        UIName2ID.GunsmithSceneSocketMainUI,
        UIName2ID.ItemDetailViewEquip,
    },
    Anim = {
        FlowInAni = "WBP_Collections_BlueprintDetail_IN",
        FlowOutAni = "WBP_Collections_BlueprintDetail_out",
    },
    ReConfig = {
        IsPoolEnable = true,
    },
    LinkSubStage = ESubStage.HallWeaponShow,
}

UITable[UIName2ID.CollectionHangingDetailPage] = {
    UILayer = EUILayer.Stack, 
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionHangingDetailPage", 
    BPKey = "WBP_Collections_BlueprintDetail",
    SubUIs = {
        UIName2ID.GunsmithSceneSocketMainUI,
        UIName2ID.ItemDetailViewEquip,
    },
    Anim = {
        FlowInAni = "WBP_Collections_BlueprintDetail_IN",
        FlowOutAni = "WBP_Collections_BlueprintDetail_out",
    },
    ReConfig = {
        IsPoolEnable = true,
    },
    LinkSubStage = ESubStage.HallMall,
}


UITable[UIName2ID.CollectionCommonVideoListView] = {
    UILayer = EUILayer.Stack, 
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionCommonVideoListView", 
    BPKey = "WBP_CollectionsVideos",
    bEnableWorldRendering = true,
    SubUIs = {
    },
    Anim = {
        FlowInAni = "WBP_CollectionsVideos_in",
        FlowOutAni = "WBP_CollectionsVideos_out",
    },
}

UITable[UIName2ID.CollectionWeaponSkinRenamePage] = {
    UILayer = EUILayer.Stack, 
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionWeaponSkinRenamePage", 
    BPKey = "WBP_Collections_Renamed",
    SubUIs = {
    },
    Anim = {
        FlowInAni = "WBP_Collections_Renamed_in",
        FlowOutAni = "WBP_Collections_Renamed_out",
    },
    LinkSubStage = ESubStage.HallWeaponShow,
}

UITable[UIName2ID.CollectionMysticalSkinWorkshopPage] = {
    UILayer = EUILayer.Stack, 
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionMysticalSkinWorkshopPage", 
    BPKey = "WBP_Collections_ProductionMatrix",
    SubUIs = {
        UIName2ID.CollectionMysticalWorkshopItem
    },
    Anim = {
        FlowInAni = "WBP_Collections_ProductionMatrix_in",
        FlowOutAni = "WBP_Collections_ProductionMatrix_out",
    },
}

UITable[UIName2ID.CollectionMysticalWorkshopItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionMysticalWorkshopItem", 
    BPKey = "WBP_Collections_ProductionMatrix_Item",
    SubUIs = {
    },
    Anim = {
    }
}


UITable[UIName2ID.CollectionMysticalSkinFilterPanel] = {
    UILayer = EUILayer.Pop, 
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionMysticalSkinFilterPanel", 
    BPKey = "WBP_Collections_ScreeningPop",
    SubUIs = {
    },
    Anim = {
    },
    IsModal = true,
}

UITable[UIName2ID.CollectionMysticalPendantFilterPanel] = {
    UILayer = EUILayer.Pop, 
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionMysticalPendantFilterPanel", 
    BPKey = "WBP_Collections_ScreeningPop",
    SubUIs = {
    },
    Anim = {
    },
    IsModal = true,
}

UITable[UIName2ID.CollectionPackagePop] = {
    UILayer = EUILayer.Pop, 
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionPackagePop", 
    BPKey = "WBP_Collections_SelectRewards",
    SubUIs = {
        UIName2ID.IVCommonItemTemplate,
    },
    Anim = {
    },
    IsModal = true,
}


UITable[UIName2ID.CollectionSelectivePackagePop] = {
    UILayer = EUILayer.Pop, 
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionSelectivePackagePop", 
    BPKey = "WBP_Collections_OptionalPackage",
    SubUIs = {
        UIName2ID.IVCommonItemTemplate,
        UIName2ID.CommonEmptyContent,
    },
    Anim = {
    },
    IsModal = true,
}



UITable[UIName2ID.CollectionEquipItems] = {
    UILayer = EUILayer.Pop, 
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionEquipItems", 
    BPKey = "WBP_Collections_EquipItems",
    Anim = {
    },
    IsModal = true,
}


UITable[UIName2ID.CollectionEquipItemsList] = {
    UILayer = EUILayer.Sub, 
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionEquipItemsList", 
    BPKey = "WBP_Collections_EquipItemsList",
    SubUIs = {
        UIName2ID.IVCommonItemTemplate,
    },
    Anim = {
    }
}


UITable[UIName2ID.CollectionActivationCard] = {
    UILayer = EUILayer.Pop, 
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionActivationCard", 
    BPKey = "WBP_Collections_ActivateTheCard",
    Anim = {
    },
    IsModal = true
}

-- 挂饰收藏馆
UITable[UIName2ID.CollectionsHallMain] = {
    UILayer = EUILayer.Stack, 
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionsHallMain",
    BPKey = "WBP_CollectionsHall_Main",
    SubUIs = {
        UIName2ID.CollectionsHall_Panel_S1,
        UIName2ID.CollectionsHall_Panel_S2,
        UIName2ID.CollectionsHall_Panel_S3,
        UIName2ID.CollectionsHall_Panel_S4,
    },
    -- Anim = {
	-- 	bManuelAnim = true,
	-- }
}

-- 收藏馆挂饰条目
-- UITable[UIName2ID.CollectionsHallItem] = {
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionsHall_Item",
--     BPKey = "WBP_CollectionsHall_Item",
-- }

-- S4赛季挂饰
UITable[UIName2ID.CollectionsHall_Panel_S4] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionsHall_Panel_S2",
    BPKey = "WBP_CollectionsHall_Panel_04",
}

-- S3赛季挂饰
UITable[UIName2ID.CollectionsHall_Panel_S3] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionsHall_Panel_S2",
    BPKey = "WBP_CollectionsHall_Panel_03",
}

-- S2赛季挂饰
UITable[UIName2ID.CollectionsHall_Panel_S2] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionsHall_Panel_S2",
    BPKey = "WBP_CollectionsHall_Panel_01",
}

-- S1赛季挂饰
UITable[UIName2ID.CollectionsHall_Panel_S1] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionsHall_Panel_S2",
    BPKey = "WBP_CollectionsHall_Panel_02",
}

-- 挂饰隐藏效果
UITable[UIName2ID.CollectionsHall_Pop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.CollectionModule.UI.CollectionsHall_Pop",
    BPKey = "WBP_CollectionsHall_Pop",
    IsModal = true,
}

--购买改名卡
UITable[UIName2ID.BuyReNameCardView] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.CollectionModule.UI.BuyCard.BuyReNameCardView",
    BPKey = "WBP_Store_PacksPurchasePop",
    IsModal = true,
}

local CollectionConfig = 
{
    --------------------------------------------------------------------------
    ---Table配置示例
    --------------------------------------------------------------------------
    CollectionTabConfig = Facade.TableManager:GetTable("Collection/CollectionTab"),
    WeaponSkinConfig = Facade.TableManager:GetTable("WeaponSkin/WeaponSkinDataTable"),
    PendantConfig = Facade.TableManager:GetTable("PendantDataTable"),
    MeleeWeaponSkinConfig = Facade.TableManager:GetTable("WeaponSkin/MeleeWeaponSkinDataTable"),
    MysticalSkinBasicParaConfig = Facade.TableManager:GetTable("WeaponSkin/MysticalSkinBasicPara"),
    RecFunctionConfig = Facade.TableManager:GetTable("WeaponPart/RecFunctionTable"),
    PendantRenameConfig = Facade.TableManager:GetTable("PendantRenameDataTable"),
    MysticalPendantSuitConfig = Facade.TableManager:GetTable("MysticalPendantSuit"),

    QualityIconMapping = {
        [0] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0401.Common_ItemProp_Icon_0401'",
        [1] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0401.Common_ItemProp_Icon_0401'",
        [2] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0402.Common_ItemProp_Icon_0402'",
        [3] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0403.Common_ItemProp_Icon_0403'",
        [4] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0404.Common_ItemProp_Icon_0404'",
        [5] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0405.Common_ItemProp_Icon_0405'",
        [6] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0406.Common_ItemProp_Icon_0406'",
    },
    --------------------------------------------------------------------------
    Loc = {
        ReNameCardFree = NSLOCTEXT("CollectionModule", "Lua_Collection_ReNameCardFree", "将消耗<customstyle color=\"Color_Highlight02\">改名卡x1</>，首次更改昵称<customstyle color=\"Color_Highlight02\">免费</>"),--改名卡
        ReNameCardIsZero = NSLOCTEXT("CollectionModule", "Lua_Collection_ReNameCardIsZero", "将消耗<customstyle color=\"Color_Highlight02\">改名卡x1</>，当前<customstyle color=\"Color_Highlight02\">改名卡数量为0</>"),--改名卡
        ConsumeReNameCard = NSLOCTEXT("CollectionModule", "Lua_Collection_ConsumeReNameCard", "将消耗<customstyle color=\"Color_Highlight02\">改名卡x1</>"),--改名卡
        CDTime = NSLOCTEXT("CollectionModule", "Lua_Collection_CDTime", "改名功能冷却中，剩余时间<customstyle color=\"Color_Highlight02\">%s</>"),--改名卡
        NewNick = NSLOCTEXT("CollectionModule", "Lua_Collection_NewNick", "最大限制为14个字符"),--改名卡
        NickNameNotAvailable = NSLOCTEXT("CollectionModule", "Lua_Collection_NickNameNotAvailable", "昵称（问题），请更改"),--改名卡
        PayPrice = NSLOCTEXT("CollectionModule", "Lua_Collection_PayPrice", "将支付：%s %s %s"),
        Price = NSLOCTEXT("CollectionModule", "Lua_Collection_Price", "<dfmrichtext type=\"img\" width=\"50\" height=\"50\" id=\"%s\" align=\"0\"/> %s"),
        DifPrice = NSLOCTEXT("CollectionModule", "Lua_Collection_DifPrice", "缺少：<dfmrichtext type=\"img\" width=\"50\" height=\"50\" id=\"%s\" align=\"0\"/> <customstyle color=\"Color_CampEnemy\">%s</>"),
        BuyDesc = NSLOCTEXT("CollectionModule", "Lua_Collection_BuyDesc", "缺少改名卡，是否购买？"),
        GoToRecharge = NSLOCTEXT("CollectionModule", "Lua_Collection_GoToRecharge", "充值"),
        RechargeTips = NSLOCTEXT("CollectionModule", "Lua_Collection_RechargeTips", "您当前的三角币不足，是否前往充值？"),
        GetReward = NSLOCTEXT("CollectionModule", "Lua_Collection_GetReward", "获得物品"),
        CoolTime = NSLOCTEXT("CollectionModule", "Lua_Collection_CoolTime", "改名功能正在冷却期"),

        Title = NSLOCTEXT("CollectionModule", "Lua_Collection_Title", "藏品"),
        ItemNum = NSLOCTEXT("CollectionModule", "Lua_Collection_Num", "数量:%d"),
        ShareTip = NSLOCTEXT("CollectionModule", "Lua_Collection_ShareTip", "分享功能尚未开发~"),
        CollectionItemMissingModel = NSLOCTEXT("CollectionModule", "Lua_Collection_CollectionItemMissingModel", "<ItemHighlight>%s</>暂无模型~"),
        Detail = NSLOCTEXT("CollectionModule", "Lua_Collection_Detail", "细节"),
        All = NSLOCTEXT("CollectionModule", "Lua_Collection_All", "全部"),
        DigitalProps = NSLOCTEXT("CollectionModule", "Lua_Collection_DigitalProps", "数字道具"),
        Purchase = NSLOCTEXT("CollectionModule", "Lua_Collection_Purchase", "购买"),
        ConfirmPurchase = NSLOCTEXT("CollectionModule", "Lua_Collection_ConfirmPurchase", "确认购买%s"),
        FailToPurchase = NSLOCTEXT("CollectionModule", "Lua_Collection_FailToPurchase", "购买失败"),
        UnlockTip = NSLOCTEXT("CollectionModule", "Lua_Collection_UnlockTip", "敬请期待"),
        ByDefeatBossTip = NSLOCTEXT("CollectionModule", "Lua_Collection_ByDefeatBossTip", "击败boss随机掉落"),
        BySearchingTip = NSLOCTEXT("CollectionModule", "Lua_Collection_BySearchingTip", "地图中探索随机触发"),

        ProceedToUnlock = NSLOCTEXT("CollectionModule", "Lua_Collection_ProceedToUnlock", "前往解锁"),
        CannotPurchaseTip  = NSLOCTEXT("CollectionModule", "Lua_Collection_CannotPurchaseTip", "<customstyle color=\"Color_DarkNegative\">无法购买</>"),
        CannotCollectTip  = NSLOCTEXT("CollectionModule", "Lua_Collection_CannotCollectTip", "<customstyle color=\"Color_DarkNegative\">无法领取</>"),
        CollectBlueprint = NSLOCTEXT("CollectionModule", "Lua_Collection_CollectBlueprint", "领取预设"),
        CollectSuccess = NSLOCTEXT("CollectionModule", "Lua_Collection_CollectSuccess", "%s蓝图%d已放入仓库"),
        WeaponSkinTotal = NSLOCTEXT("CollectionModule", "Lua_Collection_WeaponSkinTotal", "外观总数：<customstyle color=\"Color_Highlight01\">%d/%d</>"),
        WeaponSkin = NSLOCTEXT("CollectionModule", "Lua_Collection_WeaponSkin", "外观"),
        WeaponSkinDetail = NSLOCTEXT("CollectionModule", "Lua_Collection_WeaponSkinDetail", "外观详情"),
        WeaponSkinRename = NSLOCTEXT("CollectionModule", "Lua_Collection_WeaponSkinRename", "外观改名"),
        PreviewEffects = NSLOCTEXT("CollectionModule", "Lua_Collection_PreviewEffects", "效果预览"),
        ApplyAppearance = NSLOCTEXT("CollectionModule", "Lua_Collection_ApplyAppearance", "应用外观"),
        AppearanceApplied = NSLOCTEXT("CollectionModule", "Lua_Collection_AppearanceApplied", "已应用外观"),
        ApplyAndEquip = NSLOCTEXT("CollectionModule", "Lua_Collection_ApplyAndEquip", "应用并装备"),
        Equipd = NSLOCTEXT("CollectionModule", "Lua_Collection_Equipd", "应用中"),
        FailedToUse = NSLOCTEXT("CollectionModule", "Lua_Collection_FailedToUse", "应用失败"),
        WeaponSkinOwned = NSLOCTEXT("CollectionModule", "Lua_Collection_WeaponSkinOwned", "已解锁外观"),
        PurchaseFromStore = NSLOCTEXT("CollectionModule", "Lua_Collection_PurchaseFromStore", "商城获取"),
        UnlockedInProduction = NSLOCTEXT("CollectionModule", "Lua_Collection_UnlockedInProduction", "已解锁烽火地带模式制造工坊的制作权限"),
        UnlockedInCollection = NSLOCTEXT("CollectionModule", "Lua_Collection_UnlockedInCollection", "已获得{WeaponNum}把烽火地带模式下的对应武器"),
		Skip = NSLOCTEXT("CollectionModule", "Lua_Collection_Skip", "跳过"),
        ManufacturedBy = NSLOCTEXT("CollectionModule", "Lua_Collection_ManufacturedBy", "由%s制造"),
        UnknownBrand = NSLOCTEXT("CollectionModule", "Lua_Collection_UnknownBrand", "未知厂牌"),
        UnknownBattlePosition = NSLOCTEXT("CollectionModule", "Lua_Collection_UnknownBattlePosition", "未知战斗定位"),
        FunctionNotAvailableTip = NSLOCTEXT("CollectionModule", "Lua_Collection_FunctionNotAvailableTip", "功能暂未开放"),
        ProductionInCooldownTip = NSLOCTEXT("CollectionModule", "Lua_Collection_ProductionInCooldownTip", "生产功能还在冷却中"),
        SuccessfullyEquipedPendant = NSLOCTEXT("CollectionModule", "Lua_Collection_SuccessfullyEquipedPendant", "已应用挂饰"),
        WeaponNotExist = NSLOCTEXT("CollectionModule", "Lua_Collection_WeaponNotExist", "武器不存在"),
        UnknownWeapon = NSLOCTEXT("CollectionModule", "Lua_Collection_UnknownWeapon", "未知武器"),
        UnknownSkin = NSLOCTEXT("CollectionModule", "Lua_Collection_UnknownSkin", "未知外观"),
        LimitedReceivedFromActivity = NSLOCTEXT("CollectionModule", "Lua_Collection_LimitedReceivedFromActivity", "限时活动获得"),
        BlueprintEffect = NSLOCTEXT("CollectionModule", "Lua_Collection_BlueprintEffect", "蓝图特效"),
        UnknownEffect = NSLOCTEXT("CollectionModule", "Lua_Collection_UnknownEffect", "未知特效"),
        UnknownDescription = NSLOCTEXT("CollectionModule", "Lua_Collection_UnknownDescription", "未知描述"),
        ByUnlockedInSafeHouse = NSLOCTEXT("CollectionModule", "Lua_Collection_ByUnlockedInSafeHouse", "特勤处生成此预设"),
        ByUnlockedInMP = NSLOCTEXT("CollectionModule", "Lua_Collection_ByUnlockedInMP", "在全面战场中解锁此预设"),
        SkinColorPurchaseHint = NSLOCTEXT("CollectionModule", "Lua_Collection_SkinColorPurchaseHint", "需先获取该蓝图，即可购买炫彩"),
        Default = NSLOCTEXT("CollectionModule", "Lua_Collection_Default", "默认"),
        GainTime = NSLOCTEXT("CollectionModule", "Lua_Collection_GainTime", "获取时间"),
        limitedProperty = NSLOCTEXT("CollectionModule", "Lua_Collection_limitedProperty", "限时属性"),
        ViewDetail = NSLOCTEXT("CollectionModule", "Lua_Collection_ViewDetail", "查看详情"),
        ManageRandomSkinPool = NSLOCTEXT("CollectionModule", "Lua_Collection_ManageRandomSkinPool", "编辑随机"),
        Appearance = NSLOCTEXT("CollectionModule", "Lua_Collection_Appearance", "外观"),
        ChangeNameSuccess = NSLOCTEXT("CollectionModule", "Lua_Collection_ChangeNameSuccess", "改名成功"),
        NickNameEmpty = NSLOCTEXT("CollectionModule", "Lua_Collection_NickNameEmpty", "请输入新的昵称"),
        ShopLock = NSLOCTEXT("CollectionModule", "Lua_Collection_ShopLock", "商城未解锁，还不能在商城购买改名卡。"),
        NoRenameCard = NSLOCTEXT("CollectionModule", "Lua_Collection_NoRenameCard", "当前没有改名卡"),
        ComfirmRenameTip = NSLOCTEXT("CollectionModule", "Lua_Collection_ComfirmRenameTip", "您确定重命名为新的：<customstyle color=\"Color_Highlight01\">%s</>？"),
        LookOver = NSLOCTEXT("CollectionModule", "Lua_Collection_LookOver", "查看"),
        UnknownType = NSLOCTEXT("CollectionModule", "Lua_Collection_UnknownType", "未知类型"),
        OwnedNum = NSLOCTEXT("CollectionModule", "Lua_Collection_OwnedNum", "拥有：%s"),
        PackagePrizeInfo = NSLOCTEXT("CollectionModule", "Lua_Collection_PackagePrizeInfo", "将获得：%s <customstyle color=\"Color_Highlight01\">x%d</>"),
        ConsumePackage = NSLOCTEXT("CollectionModule", "Lua_Collection_ConsumePackage", "消耗<customstyle color=\"Color_Quality0{Quality}\">{PackageName}</>：{ConsumeNum}/{TotalNum}"),
        OpenPackage = NSLOCTEXT("CollectionModule", "Lua_Collection_OpenPackage", "打开礼包：%s/%s"),
        NotSelected = NSLOCTEXT("CollectionModule", "Lua_Collection_NotSelected", "未选择"),
        NoMoreSupplyPack = NSLOCTEXT("CollectionModule", "Lua_Collection_NoMoreSupplyPack", "已经没有礼包可以用啦"),
        PropReachMaxNum = NSLOCTEXT("CollectionModule", "Lua_Collection_PropReachMaxNum", "当前拥有道具数量达到显示上限"),
        RandomPrize = NSLOCTEXT("CollectionModule", "Lua_Collection_RandomPrize", "随机奖励"),
        ChoiceOfPrize = NSLOCTEXT("CollectionModule", "Lua_Collection_ChoiceOfPrize", "可选奖励"),
        IncludePrize = NSLOCTEXT("CollectionModule", "Lua_Collection_IncludePrize", "包含道具"),
        PropNotAvailableInCurrentMode = NSLOCTEXT("CollectionModule", "Lua_Collection_PropNotAvailableInCurrentMode", "此道具在当前模式下不可用"),
        UnknownGunSeries = NSLOCTEXT("CollectionModule", "Lua_Collection_UnknownGunSeries", "未知枪系"),
        QualitySort = NSLOCTEXT("CollectionModule", "Lua_Collection_QualitySort", "按品质"),
        QualitySortAscend = NSLOCTEXT("CollectionModule", "Lua_Collection_QualitySortAscend", "按品质升序"),
        QualitySortDecend = NSLOCTEXT("CollectionModule", "Lua_Collection_QualitySortDecend", "按品质降序"),
        WearSortAscend = NSLOCTEXT("CollectionModule", "Lua_Collection_WearSortAscend", "按成色升序"),
        WearSortDecend = NSLOCTEXT("CollectionModule", "Lua_Collection_WearSortDecend", "按成色降序"),
        GainTimeSortAscend = NSLOCTEXT("CollectionModule", "Lua_Collection_GainTimeSortAscend", "按获取时间升序"),
        GainTimeSortDecend = NSLOCTEXT("CollectionModule", "Lua_Collection_GainTimeSortDecend", "按获取时间降序"),
        FirearmSortAscend = NSLOCTEXT("CollectionModule", "Lua_Collection_FirearmSortAscend", "按枪型升序"),
        FirearmSortDecend = NSLOCTEXT("CollectionModule", "Lua_Collection_FirearmSortDecend", "按枪型降序"),
        GainTimeSort = NSLOCTEXT("CollectionModule", "Lua_Collection_GainTimeSort", "按获取日期"),
        NotUnlocked = NSLOCTEXT("CollectionModule", "Lua_Collection_NotUnlocked", "未解锁"),
        ReceiveAllPrizes = NSLOCTEXT("CollectionModule", "Lua_Collection_ReceiveAllPrizes", "打开礼包获得下方所有物品"),
        ReceiveRandomPrizes = NSLOCTEXT("CollectionModule", "Lua_Collection_ReceiveRandomPrizes", "打开后随机获取下方的<customstyle color=\"Color_Highlight01\">{ItemNum}</>个道具"),
        SelectPrize = NSLOCTEXT("CollectionModule", "Lua_Collection_SelectPrize", "请从下方选择一个物品"),
        OpenSelectivePackage = NSLOCTEXT("CollectionModule", "Lua_Collection_OpenSelectivePackage", "打开自选包"),
        NoMoreSelectivePackage = NSLOCTEXT("CollectionModule", "Lua_Collection_NoMoreSelectivePackage", "已选择所有自选包"),
        OpenFullArmedPackage = NSLOCTEXT("CollectionModule", "Lua_Collection_OpenFullArmedPackage", "打开全装包"),
        PleaseSelectAnyPrize = NSLOCTEXT("CollectionModule", "Lua_Collection_PleaseSelectAnyPrize", "请选择一个物品"),
        OpenSuitPackage = NSLOCTEXT("CollectionModule", "Lua_Collection_OpenSuitPackage", "打开套装包"),
        OpenRandomPackage = NSLOCTEXT("CollectionModule", "Lua_Collection_OpenRandomPackage", "打开补给包"),
        ReadyToEquip = NSLOCTEXT("CollectionModule", "Lua_Collection_ReadyToEquip", "即将装备以下物品"),
        PropsSendByMail = NSLOCTEXT("CollectionModule", "Lua_Collection_PropsSendByMail", "仓库空间不足，部分物品已经通过邮件发放"),
        DropWhenConflict = NSLOCTEXT("CollectionModule", "Lua_Collection_DropWhenConflict", "冲突物品将被卸下，仓库已满时通过邮件发送"),
        FullArmedPackageEquiped = NSLOCTEXT("CollectionModule", "Lua_Collection_FullArmedPackageEquiped", "全装包物品已成功装备"),
        WillReceiveRightsWithTimeLimit = NSLOCTEXT("CollectionModule", "Lua_Collection_WillReceiveRightsWithTimeLimit", "将获得：\n{CardName} {DaysNum}天权限"),
        WillReceiveRightsPermanently = NSLOCTEXT("CollectionModule", "Lua_Collection_WillReceiveRightsPermanently", "将获得：\n{CardName} 永久权限"),
        WillReceiveMissionFileExp = NSLOCTEXT("CollectionModule", "Lua_Collection_WillReceiveMissionFileExp", "将获得：\n{ExpNum}经验值"),
        ActCardUsedTip = NSLOCTEXT("CollectionModule", "Lua_Collection_ActCardUsedTip", "已获得{CardName}{DaysNum}天权限"),
        ActCardPermanentlyTip = NSLOCTEXT("CollectionModule", "Lua_Collection_ActCardPermanentlyTip", "已获得{CardName}永久权限"),
        ActCardInvalidTip = NSLOCTEXT("CollectionModule", "Lua_Collection_ActCardInvalidTip", "非当前赛季权限卡"),
        UseNum = NSLOCTEXT("CollectionModule", "Lua_Collection_UseNum", "使用数量：%s/%s"),
        NotAuthorized = NSLOCTEXT("CollectionModule", "Lua_Collection_NotAuthorized", "%s：无权限"),
        Eternal = NSLOCTEXT("CollectionModule", "Lua_Collection_Eternal", "%s：永久"),
        Inactive = NSLOCTEXT("CollectionModule", "Lua_Collection_Inactive", "%s：未激活"),
        ExpireTime = NSLOCTEXT("CollectionModule", "Lua_Collection_ExpireTime", "%s：%s"),
        PropNotExists = NSLOCTEXT("CollectionModule", "Lua_Collection_PropNotExists", "当前无该道具"),
        UnknownSafeBox = NSLOCTEXT("CollectionModule", "Lua_Collection_UnknownSafeBox", "未知安全箱"),
        UnknownCardPack = NSLOCTEXT("CollectionModule", "Lua_Collection_UnknownCardPack", "未知门禁卡包"),
        BasicSkin = NSLOCTEXT("CollectionModule", "Lua_Collection_BasicSkin", "基础样式"),
-- BEGIN VIRTUOS MODIFICATION
        PackageUnboxingTip = NSLOCTEXT("CollectionModule", "Console_Lua_Collection_PackageUnboxingTip", "物资发放中，请不要频繁点击"),
-- END VIRTUOS MODIFICATION
        CardUsingTip = NSLOCTEXT("CollectionModule", "Lua_Collection_CardUsingTip", "使用中，请不要频繁点击"),
        MandelDraw = NSLOCTEXT("CollectionModule", "Lua_Collection_MandelDraw", "曼德尔砖（%s）"),
        FromMarket = NSLOCTEXT("CollectionModule", "Lua_Collection_FromMarket", "市场"),
        FromMatrixWorkshop = NSLOCTEXT("CollectionModule", "Lua_Collection_FromMatrixWorkshop", "典藏工坊"),
        MatrixWorkshop = NSLOCTEXT("CollectionModule", "Lua_Collection_MatrixWorkshop", "典藏外观工坊"),
        MysticalSkinType = NSLOCTEXT("CollectionModule", "Lua_Collection_MysticalSkinType", "典藏款式 %s"),
        SkinRenameDisable = NSLOCTEXT("CollectionModule", "Lua_Collection_SkinRenameDisable", "无法改名"),
        SkinRenameConfirmationTip = NSLOCTEXT("CollectionModule", "Lua_Collection_SkinRenameConfirmationTip", "是否花费{Cash} 将外观命名为：{NewName}"),
        SkinFreeRenameConfirmationTip = NSLOCTEXT("CollectionModule", "Lua_Collection_SkinFreeRenameConfirmationTip", "您是否将外观命名为：{NewName}"),
        MandelBrickNotEnoughTip = NSLOCTEXT("CollectionModule", "Lua_Collection_MandelBrickNotEnoughTip", "您的曼德尔转不足，是否前往交易行获取？"),
        NotExistInSkinStoreTip = NSLOCTEXT("CollectionModule", "Lua_Collection_NotExistInSkinStoreTip", "检测到当前外观市场并无该物品"),
        UnknownSeason = NSLOCTEXT("CollectionModule", "Lua_Collection_UnknownSeason", "未知赛季"),
        UnknownDate = NSLOCTEXT("CollectionModule", "Lua_Collection_UnknownDate", "未知日期"),
        Confirm = NSLOCTEXT("CollectionModule", "Lua_Collection_Confirm", "确认"),
        Cancel = NSLOCTEXT("CollectionModule", "Lua_Collection_Cancel", "取消"),
        MatrixRecombination = NSLOCTEXT("CollectionModule", "Lua_Collection_MatrixRecombination", "典藏外观重组"),
        CannotRecombine = NSLOCTEXT("CollectionModule", "Lua_Collection_CannotRecombine", "无法重组"),
        CannotExchange = NSLOCTEXT("CollectionModule", "Lua_Collection_CannotExchange", "无法兑换"),
        QualityDifferent = NSLOCTEXT("CollectionModule", "Lua_Collection_QualityDifferent", "品阶不同"),
        MysticalSkinRecombineDes = NSLOCTEXT("CollectionModule", "Lua_Collection_MysticalSkinRecombineDes", "用于重组的外观"),
        MatrixRecombinationHint = NSLOCTEXT("CollectionModule", "Lua_Collection_MatrixRecombinationHint", "必重组成功高一品阶外观"),
        MatrixRecombinationRuleHint = NSLOCTEXT("CollectionModule", "Lua_Collection_MatrixRecombinationRuleHint", "{skinNum}个{qualityName}典藏外观合成1个外观"),
        MatrixRecombinationTip = NSLOCTEXT("CollectionModule", "Lua_Collection_MatrixRecombinationTip", "重组后外观的成色数值取决于消耗用的外观的成色"),
        MatrixRecombinationEnoughTip = NSLOCTEXT("CollectionModule", "Lua_Collection_MatrixRecombinationEnoughTip", "重组数据已足够，无法再添加"),
        ResetSKinTip = NSLOCTEXT("CollectionModule", "Lua_Collection_ResetSKinTip", "存在已装备的典藏外观，确认进行重置？"),
        MatrixRecombinationSuccessTip = NSLOCTEXT("CollectionModule", "Lua_Collection_MatrixRecombinationSuccessTip", "重组成功"),
        MatrixRecombinationConfirmationTip = NSLOCTEXT("CollectionModule", "Lua_Collection_MatrixRecombinationConfirmationTip", "是否确认消耗<customstyle color=\"Color_Highlight01\">{skinNum}个典藏{qualityName}外观</>，合成后将永久失去所消耗的典藏{qualityName}外观"),
        MysticalSkinAddToRecombineHint = NSLOCTEXT("CollectionModule", "Lua_Collection_MysticalSkinAddToRecombineHint", "添加左侧外观用于重组"),
        Recombine = NSLOCTEXT("CollectionModule", "Lua_Collection_Recombine", "重组"),
        Exchange = NSLOCTEXT("CollectionModule", "Lua_Collection_Exchange", "兑换"),
        MysticalPendantExchangeDes = NSLOCTEXT("CollectionModule", "Lua_Collection_MysticalPendantExchangeDes", "用于兑换的挂饰"),
        MysticalPentantAddToExchangeHint = NSLOCTEXT("CollectionModule", "Lua_Collection_MysticalPentantAddToExchangeHint", "添加左侧挂饰用于兑换"),
        MatrixExchangeEnoughTip = NSLOCTEXT("CollectionModule", "Lua_Collection_MatrixExchangeEnoughTip", "兑换槽已满，无法继续添加挂饰"),
        MatrixExchangeHint = NSLOCTEXT("CollectionModule", "Lua_Collection_MatrixExchangeHint", "可消耗挂饰兑换图灵砖"),
        MatrixExchangeRuleHint = NSLOCTEXT("CollectionModule", "Lua_Collection_MatrixExchangeRuleHint", "消耗{pendantNum}个{qualityName}典藏挂饰兑换{brickNum}个图灵砖"),
        MatrixExchangeTip = NSLOCTEXT("CollectionModule", "Lua_Collection_MatrixExchangeTip", "可消耗相同品阶的挂饰去兑换图灵砖，品阶越高所需消耗数量越少"),
        MatrixExchangeSuccessTip = NSLOCTEXT("CollectionModule", "Lua_Collection_MatrixExchangeSuccessTip", "兑换成功"),
        MatrixExchangeConfirmationTip = NSLOCTEXT("CollectionModule", "Lua_Collection_MatrixExchangeConfirmationTip", "是否确认消耗<customstyle color=\"Color_Highlight01\">{pendantNum}个{qualityName}典藏挂饰</>，兑换<customstyle color=\"Color_Highlight01\">{brickNum}个图灵砖</>？兑换后将永久失去所消耗的挂饰"),
        ProceedToUnboxing = NSLOCTEXT("CollectionModule", "Lua_Collection_ProceedToUnboxing", "前往开启"),
        ClearWhenSeasonEnd = NSLOCTEXT("CollectionModule", "Lua_Collection_ClearWhenSeasonEnd", "当前赛季结束后清除"),
        OnlyAvailableInCurrentSeason = NSLOCTEXT("CollectionModule", "Lua_Collection_OnlyAvailableInCurrentSeason", "仅当前赛季可用"),
        ActivationMatchRemain = NSLOCTEXT("CollectionModule", "Lua_Collection_ActivationMatchRemain", "剩余生效场次：%s"),
        RetrieveWeaponSkin = NSLOCTEXT("CollectionModule", "Lua_Collection_RetrieveWeaponSkin", "领取外观"),
        NotInAuctionTip = NSLOCTEXT("CollectionModule", "Lua_Collection_NotInAuctionTip", "当前交易行无该物品"),
        AddAtLeastOneSkinToPool = NSLOCTEXT("CollectionModule", "Lua_Collection_AddAtLeastOneSkinToPool", "请先添加1个外观继续重组"),
        AddAtLeastOnePendantToPool = NSLOCTEXT("CollectionModule", "Lua_Collection_AddAtLeastOnePendantToPool", "请先添加1个挂饰继续兑换"),
        CannotRecombineTip = NSLOCTEXT("CollectionModule", "Lua_Collection_CannotRecombineTip", "该外观已为最高品阶，无法进行重组"),
        CannotExchangeTip = NSLOCTEXT("CollectionModule", "Lua_Collection_CannotExchangeTip", "该挂饰已为最高品阶，无法进行兑换"),
        AddMysticalSkinToRecombineTip = NSLOCTEXT("CollectionModule", "Lua_Collection_AddMysticalSkinToRecombineTip", "需要添加20个相同品阶典藏外观进行重组"),
        AddMysticalPendantToExchangeTip = NSLOCTEXT("CollectionModule", "Lua_Collection_AddMysticalPendantToExchangeTip", "需要添加{pendantNum}个相同品阶挂饰进行兑换"),
        DefaultSkin = NSLOCTEXT("CollectionModule", "Lua_Collection_DefaultSkin", "%s默认外观"),
        SeasonTip = NSLOCTEXT("CollectionModule", "Lua_Collection_SeasonTip", "典藏-{SeasonIndex}赛季"),
        UniqueNum = NSLOCTEXT("CollectionModule", "Lua_Collection_UniqueNum", "专属编号"),
        Rarity = NSLOCTEXT("CollectionModule", "Lua_Collection_Rarity", "稀有度"),
        Wear = NSLOCTEXT("CollectionModule", "Lua_Collection_Wear", "成色"),
        Color = NSLOCTEXT("CollectionModule", "Lua_Collection_Color", "颜色"),
        SpecialEffect = NSLOCTEXT("CollectionModule", "Lua_Collection_SpecialEffect", "特效"),
        ItemDetailWear = NSLOCTEXT("CollectionModule", "Lua_Collection_ItemDetailWear", "（{value}）"),
        KillCount = NSLOCTEXT("CollectionModule", "Lua_Collection_KillCount", "计数数量"),
        PendantColor = NSLOCTEXT("CollectionModule", "Lua_Collection_PendantColor", "挂饰炫彩"),
        PatternTemplate = NSLOCTEXT("CollectionModule", "Lua_Collection_PatternTemplate", "图案模板"),
        Accessory = NSLOCTEXT("CollectionModule", "Lua_Collection_Accessory", "附加配件"),
        HiddenProperty = NSLOCTEXT("CollectionModule", "Lua_Collection_HiddenProperty", "隐藏特性"),
        DoubleClickToAdd = NSLOCTEXT("CollectionModule", "Lua_Collection_DoubleClickToAdd", "双击添加"),
        DoubleClickToRemove = NSLOCTEXT("CollectionModule", "Lua_Collection_DoubleClickToRemove", "双击移除"),
        SkinEffect = NSLOCTEXT("CollectionModule", "Lua_Collection_SkinEffect", "外观效果"),
        Use = NSLOCTEXT("CollectionModule", "Lua_Collection_Use", "使用"),
        Effecting = NSLOCTEXT("CollectionModule", "Lua_Collection_Effecting", "生效中"),
        SuccessfullyAppliedVehicleSkin = NSLOCTEXT("CollectionModule", "Lua_Collection_SuccessfullyAppliedVehicleSkin", "已应用此外观到%s"),
        NoLimit = NSLOCTEXT("CollectionModule", "Lua_Collection_NoLimit", "不限"),
        AnyFirearm = NSLOCTEXT("CollectionModule", "Lua_Collection_AnyFirearm", "全部枪型"),
        AnyType = NSLOCTEXT("CollectionModule", "Lua_Collection_AnyType", "全部种类"),
        CanNotChangeQualityFilter = NSLOCTEXT("CollectionModule", "Lua_Collection_CanNotChangeQualityFilter", "当前无法进行品阶筛选"),
        NoItems = NSLOCTEXT("CollectionModule", "Lua_Collection_NoItems", "没有道具"),
        NoPendantsToExchange = NSLOCTEXT("CollectionModule", "Lua_Collection_NoPendantsToExchange", "暂无可兑换挂饰"),
        DoubleTournamentExpCardTip = NSLOCTEXT("CollectionModule", "Lua_Collection_DoubleTournamentExpCardTip", "使用后会在之后特定场次下的对局获得晋升之路的双倍军功得分，该效果不会获得双倍扣分，不论是否残局还是完整对局都将获得双倍"),
        DoubleExpCardTip = NSLOCTEXT("CollectionModule", "Lua_Collection_DoubleExpCardTip", "使用后会再之后特定场次下的对局获得全面战场的等级经验，不论是否残局还是完整对局都将获得双倍，但获得量不会超过上限"),
        PointProtectiveCardTip = NSLOCTEXT("CollectionModule", "Lua_Collection_PointProtectiveCardTip", "使用后可在结算军功为负数时自动触发保护，重复使用会增加保护次数"),
        WeaponFilterHint = NSLOCTEXT("CollectionModule", "Lua_Collection_WeaponFilterHint", "请选择需要筛选的枪械种类"),
        ProceedToAssemble = NSLOCTEXT("CollectionModule", "Lua_Collection_ProceedToAssemble", "前往当前模式配装界面应用"),
        Owned = NSLOCTEXT("CollectionModule", "Lua_Collection_Owned", "已获得"),
        CurrentSeason = NSLOCTEXT("CollectionModule", "Lua_Collection_CurrentSeason", "当前赛季限定"),
        PreviousSeason = NSLOCTEXT("CollectionModule", "Lua_Collection_PreviousSeason", "往期赛季"),
        RenameForbidTip = NSLOCTEXT("CollectionModule", "Lua_Collection_RenameForbidTip", "禁止改名，解封时间{UnlockTime}"),
        EquipForbidTip = NSLOCTEXT("CollectionModule", "Lua_Collection_EquipForbidTip", "当前正在使用装备券，不允许一键装备"),
        LoadingTip = NSLOCTEXT("CollectionModule", "Lua_Collection_LoadingTip", "正在加载，请稍后再试"),
        AllSeasons = NSLOCTEXT("CollectionModule", "Lua_Collection_AllSeasons", "全部赛季"),
        SpecificSeason = NSLOCTEXT("CollectionModule", "Lua_Collection_SpecificSeason", "S%s赛季"),
        NumFormat = NSLOCTEXT("CollectionModule", "Lua_Collection_NumFormat", "{countNum}/{maxNum}"),
        NumFormat2 = NSLOCTEXT("CollectionModule", "Lua_Collection_NumFormat2", "系列收集进度：{countNum}/{maxNum}"),
        NumFormat3 = NSLOCTEXT("CollectionModule", "Lua_Collection_NumFormat3", "({countNum}/{maxNum})"),
        SetDefault = NSLOCTEXT("CollectionModule", "Lua_Collection_SetDefault", "设置默认"),
        EquipedDefault = NSLOCTEXT("CollectionModule", "Lua_Collection_EquipedDefault", "已设置为默认使用"),
        CancelDefault = NSLOCTEXT("CollectionModule", "Lua_Collection_CancelDefault", "已取消默认使用"),
        Apply = NSLOCTEXT("CollectionModule", "Lua_Collection_Apply", "应用"),
        PendantRenameConfirmationTip = NSLOCTEXT("CollectionModule", "Lua_Collection_PendantRenameConfirmationTip", "是否花费{Cash} 将挂饰命名为：{NewName}"),
        PendantRenameLimitTip = NSLOCTEXT("CollectionModule", "Lua_Collection_PendantRenameLimitTip", "最大名称限制%d个字符，不支持符号"),
        PendantRenameFreeTip = NSLOCTEXT("CollectionModule", "Lua_Collection_PendantRenameFreeTip", "全新挂饰可免费更改一次命名"),
        PendantRenameTitle = NSLOCTEXT("CollectionModule", "Lua_Collection_PendantRenameTitle", "挂饰改名"),
        PendantMandelDraw= NSLOCTEXT("CollectionModule", "Lua_Collection_PendantMandelDraw", "图灵砖（%s）"),
        CollectionDesc = NSLOCTEXT("CollectionModule", "Lua_Collection_CollectionDesc", "集齐指定典藏挂饰后，会解锁隐藏效果。"),
        GoToGet = NSLOCTEXT("CollectionModule", "Lua_Collection_GoToGet", "前往获取"),
        UnlockhiddenEffects = NSLOCTEXT("CollectionModule", "Lua_Collection_UnlockhiddenEffects", "解锁隐藏效果"),
        Unlocked = NSLOCTEXT("CollectionModule", "Lua_Collection_Unlocked", "已解锁"),
        NotEnoughMoney = NSLOCTEXT("CollectionModule", "Lua_Collection_NotEnoughMoney", "%s不足"),
        SuitNotUnlock = NSLOCTEXT("CollectionModule", "Lua_Collection_SuitNotUnlock", "集齐该系列挂饰将解锁隐藏效果"),
        SuitUnlock = NSLOCTEXT("CollectionModule", "Lua_Collection_SuitUnlock", "已解锁隐藏效果"),
        PropExpirationInSecondsHint = NSLOCTEXT("CollectionModule", "Lua_Collection_PropExpirationInSecondsHint", "{Amount}个道具将在1分钟内过期"),
        PropExpirationInTimeHint = NSLOCTEXT("CollectionModule", "Lua_Collection_PropExpirationInTimeHint", "{Amount}个道具将在{TimeText}后过期"),
        PropExpirationInSecondsTip = NSLOCTEXT("CollectionModule", "Lua_Collection_PropExpirationInSecondsTip", "1分钟内过期（{Amount}）"),
        PropExpirationInTimeTip = NSLOCTEXT("CollectionModule", "Lua_Collection_PropExpirationInTimeTip", "{TimeText}后过期（{Amount}）"),
        PropExpirationInTimeSimpleTip = NSLOCTEXT("CollectionModule", "Lua_Collection_PropExpirationInTimeSimpleTip", "{TimeText}（{Amount}）"),
        InOneMinute = NSLOCTEXT("CollectionModule", "Lua_Collection_InOneMinute", "1分钟内"),
        NumOfMinute = NSLOCTEXT("CollectionModule", "Lua_Collection_NumOfMinute", "{Minute}分钟"),
        NumOfHour = NSLOCTEXT("CollectionModule", "Lua_Collection_NumOfHour", "{Hour}小时"),
        NumOfDay = NSLOCTEXT("CollectionModule", "Lua_Collection_NumOfDay", "{Day}天"),
        AmountSelected = NSLOCTEXT("CollectionModule", "Lua_Collection_AmountSelected", "选择数量："),
        CurrentReceived = NSLOCTEXT("CollectionModule", "Lua_Collection_CurrentReceived", "新获得"),
        RandomSkinItemTitle = NSLOCTEXT("CollectionModule", "Lua_Collection_RandomSkinItemTitle", "{FirearmName}-随机外观"),
        RandomSkinOverviewTitle = NSLOCTEXT("CollectionModule", "Lua_Collection_RandomSkinOverviewTitle", "{FirearmName}随机外观"),
        RandomSkinApplyHint = NSLOCTEXT("CollectionModule", "Lua_Collection_RandomSkinApplyHint", "每次入局枪械外观会进行随机"),
        PleaseSelectRandomSkinTip = NSLOCTEXT("CollectionModule", "Lua_Collection_PleaseSelectRandomSkinTip", "请选择需要随机的外观"),
        BatchUpdateRandomSkinPoolTip = NSLOCTEXT("CollectionModule", "Lua_Collection_BatchUpdateRandomSkinPoolTip", "已设置为随机外观"),
        BatchRemoveFromRandomSkinPoolTip = NSLOCTEXT("CollectionModule", "Lua_Collection_BatchRemoveFromRandomSkinPoolTip", "已取消随机外观"),
        AddToRandomSkinPool = NSLOCTEXT("CollectionModule", "Lua_Collection_AddToRandomSkinPool", "加入随机"),
        AddedToRandomSkinPoolTip = NSLOCTEXT("CollectionModule", "Lua_Collection_AddedToRandomSkinPoolTip", "已加入随机"),
        RemovedFromRandomSkinPoolTip = NSLOCTEXT("CollectionModule", "Lua_Collection_RemovedFromRandomSkinPoolTip", "已移出随机"),
        RandomSkinPoolEmptyTip = NSLOCTEXT("CollectionModule", "Lua_Collection_RandomSkinPoolEmptyTip", "{FirearmName}当前未设置随机外观"),
        ExpirationOwnedNum = NSLOCTEXT("CollectionModule", "Lua_Collection_ExpirationOwnedNum", "拥有总数量：{Amount}"),
        Expired = NSLOCTEXT("CollectionModule", "Lua_Collection_Expired", "已过期"),
        UseExpiredPropTip = NSLOCTEXT("CollectionModule", "Lua_Collection_UseExpiredPropTip", "道具已过期，使用失败，已为你刷新界面"),
        PreviewInstallation = NSLOCTEXT("CollectionModule", "Lua_Collection_PreviewInstallation", "预览安装效果"),
        WeaponPreview = NSLOCTEXT("CollectionModule", "Lua_Collection_WeaponPreview", "%s外观"),
        UseActivationCard = NSLOCTEXT("CollectionModule", "Lua_Collection_UseActivationCard", "使用激活卡"),
        UseExpCard = NSLOCTEXT("CollectionModule", "Lua_Collection_UseExpCard", "使用经验卡"),
        UseAnyProp = NSLOCTEXT("CollectionModule", "Lua_Collection_UseAnyProp", "使用{PropName}"),
        PropUsedTip = NSLOCTEXT("CollectionModule", "Lua_Collection_PropUsedTip", "已使用{PropName}x{PropNum}"),
        WeaponSkinNew = NSLOCTEXT("CollectionModule", "Lua_Collection_WeaponSkinNew", "枪械外观"),
        SetDefaultPendant = NSLOCTEXT("CollectionModule","Lua_Collection_SetDefaultPendant","勾选需要设置/取消默认使用该挂饰的枪械型号"),
        MoreCamouflageSkinHint = NSLOCTEXT("CollectionModule","Lua_Collection_MoreCamouflageSkinHint","更多<customstyle color=\"Color_Highlight01\">{PatternName}</>外观，敬请期待"),
        TaskDescWithProgress = NSLOCTEXT("CollectionModule","Lua_Collection_TaskDescWithProgress","({currentNum}/{maxNum}){targetDesc}"),
        MasterChallengeTitle = NSLOCTEXT("CollectionModule","Lua_Collection_MasterChallengeTitle","{GameMode}-大师挑战"),
        MasterChallengeTip = NSLOCTEXT("CollectionModule","Lua_Collection_MasterChallengeTip","烽火地带和全面战场的大师挑战任务不同，在任意模式中完成所有大师挑战，均可以解锁所有大师外观，并能在所有模式中使用。"),
        Activate = NSLOCTEXT("CollectionModule", "Lua_Collection_Activate", "激活"),
        Activated = NSLOCTEXT("CollectionModule", "Lua_Collection_Activated", "已激活"),
        CamouflageChallenge = NSLOCTEXT("CollectionModule", "Lua_Collection_CamouflageChallenge", "迷彩外观挑战"),
        MasterCamouflagePreTaskHint = NSLOCTEXT("CollectionModule", "Lua_Collection_MasterCamouflagePreTaskHint", "解锁{skinNum}个<customstyle color=\"Color_Highlight01\">{patternName}</>外观可开启大师挑战"),
        RetrieveAll = NSLOCTEXT("CollectionModule", "Lua_Collection_RetrieveAll", "全部领取"),
        Unlocked = NSLOCTEXT("CollectionModule", "Lua_Collection_Unlocked", "已解锁"),
        ViewChallenge = NSLOCTEXT("CollectionModule", "Lua_Collection_ViewChallenge", "查看挑战"),
        CompleteChallengeToRetrieve = NSLOCTEXT("CollectionModule", "Lua_Collection_CompleteChallengeToRetrieve", "完成挑战可解锁"),
        CompleteMasterChallengeToRetrieve = NSLOCTEXT("CollectionModule", "Lua_Collection_CompleteMasterChallengeToRetrieve", "完成大师挑战可解锁"),
        CamouflageChallengeReActivateTip = NSLOCTEXT("CollectionModule", "Lua_Collection_CamouflageChallengeReActivateTip", "<customstyle color=\"Color_Highlight01\">{patternName}</>的所有外观均已解锁，是否仍然激活挑战"),
    },

    ---LuaEvent声明示例
    --------------------------------------------------------------------------
    Events = {
        evtOnJumpStore = LuaEvent:NewIns("evtOnJumpStore"),
        evtOnCollectionMainPanelClosed = LuaEvent:NewIns("evtOnCollectionMainPanelClosed"),
        evtOnPlayerRename = LuaEvent:NewIns("evtOnPlayerRename"),
        evtOnAddWorkshopItemToTargetPool = LuaEvent:NewIns("evtOnAddWorkshopItemToTargetPool"),
        evtOnRemoveWorkshopItemFromTargetPool = LuaEvent:NewIns("evtOnRemoveWorkshopItemFromTargetPool"),
        evtOnWorkshopFilterQualityChanged = LuaEvent:NewIns("evtOnWorkshopFilterQualityChanged"),
        evtOnWorkshopFilterGunTypeChanged = LuaEvent:NewIns("evtOnWorkshopFilterGunTypeChanged"),
        evtOnWorkshopFilterSeasonChoiceChanged = LuaEvent:NewIns("evtOnWorkshopFilterSeasonChoiceChanged"),
        evtOnWorkshopFilterWeaponChoiceChanged = LuaEvent:NewIns("evtOnWorkshopFilterWeaponChoiceChanged"),
        evtOnWorkshopFilterUpdated = LuaEvent:NewIns("evtOnWorkshopFilterUpdated"),
        evtOnWorkshopFilterWearRangeChanged = LuaEvent:NewIns("evtOnWorkshopFilterWearRangeChanged"),
        evtOnGunSkinFilterUpdated = LuaEvent:NewIns("evtOnGunSkinFilterUpdated"),
        evtOnGunSkinFilterSkinGroupChanged = LuaEvent:NewIns("evtOnGunSkinFilterSkinGroupChanged"),
        evtOnGunSkinFilterSkinTypeChanged = LuaEvent:NewIns("evtOnGunSkinFilterSkinTypeChanged"),
        evtOnGunSkinFilterQualityChanged = LuaEvent:NewIns("evtOnGunSkinFilterQualityChanged"),
        evtOnGunSkinFilterGunTypeChanged = LuaEvent:NewIns("evtOnGunSkinFilterGunTypeChanged"),
        evtOnGunSkinFilterWeaponChoiceChanged = LuaEvent:NewIns("evtOnGunSkinFilterWeaponChoiceChanged"),
        evtOnGunSkinFilterSortModeChanged = LuaEvent:NewIns("evtOnGunSkinFilterSortModeChanged"),
        evtOnCollectionMainPanelTabIndexChanged = LuaEvent:NewIns("evtOnCollectionMainPanelTabIndexChanged"), -- 主tab界面切换

        -- 子Tab界面切换
        -- args = (mainTabIndex, tertiaryTabIndex, lastTertiaryTabIndex)
        evtOnCollectionMainPanelTertiaryTabIndexChanged = LuaEvent:NewIns("evtOnCollectionMainPanelTertiaryTabIndexChanged"), 

        evtOnHangingFilterUpdated = LuaEvent:NewIns("evtOnHangingFilterUpdated"),
        evtOnHangingFilterSeasonChanged = LuaEvent:NewIns("evtOnHangingFilterSeasonChanged"),
        evtOnHangingFilterSkinGroupChanged = LuaEvent:NewIns("evtOnHangingFilterSkinGroupChanged"),
        evtOnHangingFilterSkinTypeChanged = LuaEvent:NewIns("evtOnHangingFilterSkinTypeChanged"),
        evtOnHangingFilterQualityChanged = LuaEvent:NewIns("evtOnHangingFilterQualityChanged"),

        evtHangingPageItemClicked = LuaEvent:NewIns("evtHangingPageItemClicked"), -- 挂饰界面挂饰点击事件
        evtHangingSuitActived = LuaEvent:NewIns("evtHangingSuitActived"), -- 挂饰套装激活事件
    },
    CardType2UsageText = {
        [ECollectableType.BattleDoubleLevelExpCard] = NSLOCTEXT("CollectionModule", "Lua_Collection_LevelExpCardUsageNum", "将获得：\n全面战场经验倍增次数+{UsageNum}"),
        [ECollectableType.BattleDoubleWeaponExpCard] = NSLOCTEXT("CollectionModule", "Lua_Collection_WeaponExpCardUsageNum", "将获得：\n全面战场经验倍增次数+{UsageNum}"),
        [ECollectableType.BattleDoubleTournamentExpCard] = NSLOCTEXT("CollectionModule", "Lua_Collection_TournamentExpCardUsageNum", "将获得：\n全面战场军功倍增次数+{UsageNum}"),
        [ECollectableType.BattlePointProtectiveCard] = NSLOCTEXT("CollectionModule", "Lua_Collection_ProtectiveCardUsageNum", "将获得：\n全面战场军功保护次数+{UsageNum}"),
    },
    QualityType2Name = {
        [ItemConfig.EWeaponSkinQualityType.Unknown] = NSLOCTEXT("CollectionModule", "Lua_Collection_AllQuality", "全部品阶"),
        [ItemConfig.EWeaponSkinQualityType.Green] = NSLOCTEXT("CollectionModule", "Lua_Collection_GreenQuality", "普通"),
        [ItemConfig.EWeaponSkinQualityType.Blue] = NSLOCTEXT("CollectionModule", "Lua_Collection_BlueQuality", "稀有"),
        [ItemConfig.EWeaponSkinQualityType.Purple] = NSLOCTEXT("CollectionModule", "Lua_Collection_PurpleQuality", "史诗"),
        [ItemConfig.EWeaponSkinQualityType.Orange] = NSLOCTEXT("CollectionModule", "Lua_Collection_OrangeQuality", "传说"),
        [ItemConfig.EWeaponSkinQualityType.Red] = NSLOCTEXT("CollectionModule", "Lua_Collection_RedQuality", "红"),
    },

    WeaponType2Name = {
        [ItemConfig.EWeaponItemType.Rifle] = ItemConfig.MapWeaponItemType2Name[ItemConfig.EWeaponItemType.Rifle],
        [ItemConfig.EWeaponItemType.Submachine] = ItemConfig.MapWeaponItemType2Name[ItemConfig.EWeaponItemType.Submachine],
        [ItemConfig.EWeaponItemType.Shotgun] = ItemConfig.MapWeaponItemType2Name[ItemConfig.EWeaponItemType.Shotgun],
        [ItemConfig.EWeaponItemType.LightMachine] = ItemConfig.MapWeaponItemType2Name[ItemConfig.EWeaponItemType.LightMachine],
        [ItemConfig.EWeaponItemType.PrecisionShootingRifle] = ItemConfig.MapWeaponItemType2Name[ItemConfig.EWeaponItemType.PrecisionShootingRifle],
        [ItemConfig.EWeaponItemType.Sniper] = ItemConfig.MapWeaponItemType2Name[ItemConfig.EWeaponItemType.Sniper],
        [ItemConfig.EWeaponItemType.Pistol] = ItemConfig.MapWeaponItemType2Name[ItemConfig.EWeaponItemType.Pistol],
        [ItemConfig.EWeaponItemType.Universal] = ItemConfig.MapWeaponItemType2Name[ItemConfig.EWeaponItemType.Universal],
    },
    ECollectionSortMode = 
    {
        Default     = 1,
        GetTime     = 2,
        TimeLimit   = 3,
    },
    RarityType2Text = 
    {
        [MysticalSkinRarityType.RarityNone] = NSLOCTEXT("CollectionModule", "Lua_Collection_RarityNone", "无"),
        [MysticalSkinRarityType.RarityHigh] = NSLOCTEXT("CollectionModule", "Lua_Collection_RarityHigh", "优品"),
        [MysticalSkinRarityType.RarityRara] = NSLOCTEXT("CollectionModule", "Lua_Collection_RarityRara", "稀有"),
        [MysticalSkinRarityType.RarityPremium] = NSLOCTEXT("CollectionModule", "Lua_Collection_RarityPremium", "极品"),
        [MysticalSkinRarityType.RarityExtraordinary] = NSLOCTEXT("CollectionModule", "Lua_Collection_RarityExtraordinary", "超凡"),
    },
    EItemGroup = {
        Any = 0,
        Owned = 1,
        NotOwned = 2
    },
    EItemType = {
        Any = 0,
        Mystical = 1,
        Normal = 2
    },
    EExpireType = {
        Expired = 0,
        Seconds = 1,
        Minutes = 2,
        Hours = 3,
        Days = 4
    },
    ---@type CollectionTabId2Info_Elem[]
    CollectionTabId2Info = {
        [1] = {
            uiNavID = UIName2ID.CollectionsWeaponSkinPagePanel,
            iconPath = "PaperSprite'/Game/UI/UIAtlas/System/CollectionsWarehouse/BakedSprite/Collections_Icon_04.Collections_Icon_04'",
            subStage = ESubStage.HallCollectionNew,
            tabType = CollectionTabType.CollectionTabType_WEAPON_SKIN_TAB,
            tertiaryIconPaths = ItemConfig.WeaponImgPathList,
            tertiaryIconSizeListHD = Module.ArmedForce.Config.WeaponImgSizeListHD
        },
        [2] = {
            uiNavID = UIName2ID.CollectionsMeleeSkinPagePanel,
            iconPath = "PaperSprite'/Game/UI/UIAtlas/System/CollectionsWarehouse/BakedSprite/Collections_Icon_05.Collections_Icon_05'",
            subStage = ESubStage.CollectionKnife,
            tabType = CollectionTabType.CollectionTabType_WEAPON_SKIN_TAB,
        },
        [3] = {
            uiNavID = UIName2ID.CollectionMandelBrickPagePanel,
            iconPath = "PaperSprite'/Game/UI/UIAtlas/CommonHud/BakedSprite/CommonHud_MapMarker_Icon_0202.CommonHud_MapMarker_Icon_0202'",
            subStage = ESubStage.LotteryCollection,
            bUseDynamicSubStage = true,
            tabType = CollectionTabType.CollectionTabType_None,
        },
        [4] = {
            uiNavID = UIName2ID.CollectionsPagePanel,
            iconPath = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Warehouse_Icon_0101.CommonHall_Warehouse_Icon_0101'",
            subStage = ESubStage.HallCollectionNew,
            tabType = CollectionTabType.CollectionTabType_GAMEITEM_TAB,
            tertiaryIconPaths = {
                "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemClass_Icon_0000.Common_ItemClass_Icon_0000'",
                "PaperSprite'/Game/UI/UIAtlas/System/CollectionsWarehouse/BakedSprite/Collections_Icon_0001.Collections_Icon_0001'",
                "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_EquipClass_Icon_0005.Common_EquipClass_Icon_0005'",
                "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemClass_Icon_0050.Common_ItemClass_Icon_0050'",
            },
        },
        [5] = {
            uiNavID = UIName2ID.CollectionsHangingPagePanel,
            iconPath = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Market_Icon_0003.CommonHall_Market_Icon_0003'",
            subStage = ESubStage.CollectionHanging,
            tabType = CollectionTabType.CollectionTabType_None,
        },
        ---@class CollectionTabId2Info_Elem
        [6] = {
            uiNavID = UIName2ID.CollectionsPageContainer,
            iconPath = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Warehouse_Icon_0106.CommonHall_Warehouse_Icon_0106'",
            subStage = ESubStage.HallCollectionNew,
            tabType = CollectionTabType.CollectionTabType_None,
            tertiaryUiNavIDs = {
                UIName2ID.CollectionMysticalSkinWorkshopPagePanel, 
                UIName2ID.CollectionMysticalPendantWorkshopPagePanel
            },
            tertiaryIconPaths = {
                "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Warehouse_Icon_0106.CommonHall_Warehouse_Icon_0106'",
                "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Ornaments_Icon_0002.CommonHall_Ornaments_Icon_0002'",
            },
            bAlwaysUseTertiaryTab = true
        },
        [7] = {
            uiNavID = UIName2ID.CollectionMysticalHallPagePanel,
            iconPath = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Warehouse_Icon_0104.CommonHall_Warehouse_Icon_0104'",
            subStage = ESubStage.HallCollectionV2,
            tabType = CollectionTabType.CollectionTabType_None,
        },
        [8] = {
            uiNavID = UIName2ID.CollectionsPageContainer,
            iconPath = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Market_Icon_0002.CommonHall_Market_Icon_0002'",
            subStage = ESubStage.HallCollectionNew,
            tabType = CollectionTabType.CollectionTabType_None,
            tertiaryUiNavIDs = {
                UIName2ID.CollectionAppearanceCamouflagePagePanel, 
                UIName2ID.CollectionAppearanceMasterPagePanel
            },
            tertiaryIconPaths = {
                "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Market_Icon_0002.CommonHall_Market_Icon_0002'",
                "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Market_Icon_0002.CommonHall_Market_Icon_0002'",
            },
            tertiaryIconSizeListHD = {},
            tertiarySubStages = {
                ESubStage.HallCollectionNew,
                ESubStage.HallWeaponShow,
            },
            bAlwaysUseTertiaryTab = true
        },
    },

    -- 典藏挂饰配置
    seasonId2Value = {
        [202403] = 1,
        [202501] = 2,
        [202502] = 3,
    },
    SeasonValue2Name = {
        [1] = {
            [1] = NSLOCTEXT("CollectionModule", "Lua_Collection_Season1_1", "<customstyle color=\"C000\">S3赛季{0}</>"),
            [2] = NSLOCTEXT("CollectionModule", "Lua_Collection_Season1_2", "<customstyle color=\"Color_Highlight01\">S3赛季{0}</>"),
        },
        [2] = {
            [1] = NSLOCTEXT("CollectionModule", "Lua_Collection_Season2_1", "<customstyle color=\"C000\">S4赛季{0}</>"),
            [2] = NSLOCTEXT("CollectionModule", "Lua_Collection_Season2_2", "<customstyle color=\"Color_Highlight01\">S4赛季{0}</>"),
        },
        [3] = {
            [1] = NSLOCTEXT("CollectionModule", "Lua_Collection_Season3_1", "<customstyle color=\"C000\">S5赛季{0}</>"),
            [2] = NSLOCTEXT("CollectionModule", "Lua_Collection_Season3_2", "<customstyle color=\"Color_Highlight01\">S5赛季{0}</>"),
        },
    },
    ItemDetailSeasonValue2Name = {
        [1] = NSLOCTEXT("CollectionModule", "Lua_Collection_ItemDetailSeason1", "S3赛季"),
        [2] = NSLOCTEXT("CollectionModule", "Lua_Collection_ItemDetailSeason2", "S4赛季"),
        [3] = NSLOCTEXT("CollectionModule", "Lua_Collection_ItemDetailSeason3", "S5赛季"),
    },

    SeasoNamePrefix = NSLOCTEXT("CollectionModule", "Lua_Collection_SeasoNamePrefix", "典藏-{0}"),
    PendantMaterialInstanceConstant = {
        TracedEdge = "MaterialInstanceConstant'/Game/UI/UIAtlas/System/CollectionsWarehouse/SP/Ornaments/Ornaments_Inst_02.Ornaments_Inst_02'",
        NotTracedEdge = "MaterialInstanceConstant'/Game/UI/UIAtlas/System/CollectionsWarehouse/SP/Ornaments/Ornaments_Inst_01.Ornaments_Inst_01'",
    },
    RecombineNum = 20,
    DefaultWorkshopSlotNum = 20,
    LongPressDelta = 0.5,
    BoxOpenMaxNum = 50
}

return CollectionConfig
