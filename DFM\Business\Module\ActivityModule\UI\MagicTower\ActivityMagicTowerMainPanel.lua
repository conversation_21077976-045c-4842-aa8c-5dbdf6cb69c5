----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class ActivityMagicTowerMainPanel : LuaUIBaseView
local ActivityMagicTowerMainPanel = ui("ActivityMagicTowerMainPanel")
local ActivityConfig = Module.Activity.Config
local Logic = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerLogic"
local ActivityLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"
local ActivityInputHandler = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityInputHandler"
local ActivityMagicTowerPlayer = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerPlayer"
local ActivityMagicTowerCombatLogic = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerCombatLogic"
local ActivityMagicTowerKeyItem = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerKeyItem"
local ActivityMagicTowerNumericalPanel = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerNumericalPanel"
local ActivityMagicTowerConditionEvaluator = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerConditionEvaluator"
local ActivityMagicTowerAStarHandler = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerAStarHandler"
-- local ActivityMagicTowerAStarNode = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerAStarNode"
local ConfigManager = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerConfigManager"
local MTSafeManager = require "DFM.Business.Module.ActivityModule.UI.MagicTower.Safe.MTSafeManager"
local MTSafeItem = require "DFM.Business.Module.ActivityModule.UI.MagicTower.Safe.MTSafeItem"

local UKismetSystemLibrary = import "KismetSystemLibrary"
local ActivityMagicTowerMove = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerMove"

local FHitResult = import "HitResult"
local HitResult = FHitResult()
local UDFMGameNotch = import "DFMGameNotch"
local DFMGameNotch = UDFMGameNotch.Get(GetGameInstance())
local function testLog(...)
    loginfo("[Magic_Tower] ", ...)
end

local function testWarningLog(...)
    logwarning("[Magic_Tower] ", ...)
end

local function testErrorLog(...)
    logerror("[Magic_Tower] ", ...)
end

local MoveDirection = {
    [1] = "MoveUp",
    [2] = "MoveDown",
    [3] = "MoveLeft",
    [4] = "MoveRight",
    [5] = "StopMovement"
}

local IdleIdxToAnim = {
    [1] = "idle_back",
    [2] = "idle_front",
    [3] = "idle_left",
    [4] = "idle_right",
}

local MoveIdxToAnim = {
    [1] = "run_back",
    [2] = "run_front",
    [3] = "run_left",
    [4] = "run_right",
}

local FrameTaskMaxID = 1000000 -- 帧任务最大ID
local MaxframeExeCount = 2 -- 每帧最大执行数量

local ITEM_CLASS_NAME = ConfigManager.ITEM_CLASS_NAME
local SECRET_KEY = "d36f607d559b2d45376ab4f0345ca5a5"
local UNCERTAIN_STR = "???"

-- 铁门
local UNABLE_OPEN_DOOR_ID1 = 2031105
local UNABLE_OPEN_DOOR_ID2 = 2031168

-- 活性源石地块
local SPECIAL_PROP_ID = 3041072

-- 琳琅诗怀雅，星熊和陈
local REWARD_NPC_CONDITION_ID = 8012003

-- 周目默认对话组
local DEFAULT_DIALOGUE_LIST = {
    [1] = 6010035,
    [2] = 6015001,
    [3] = 6015002,
    [4] = 6015003,
    [5] = 6015004,
    [6] = 6015005,
}

-- 关卡类型对应的音乐索引
local LEVEL_MUSIC_CONVERT = {
    [1] = 3,
    [2] = 4,
    [3] = 5,
}

-- 周目状态相关
local LevelState = {
    FIRST_AND_NEW       = 1,
    FIRST_NOT_NEW       = 2,
    FIRST_ENTERED_TOWER = 3,
    SECOND_DESTROY_COMP = 4,
    SECOND_SAVED_COMP   = 5,
    AFTER_THE_SECOND    = 6,
}

-- 二周目及之后生成点
local MAX_LEVEL_SPAWN_LEVEL_ID = 1010930
local MAX_LEVEL_SPAWN_X = 8
local MAX_LEVEL_SPAWN_Y = 7

-- 哈夫克通勤车上班传送点
local SPECIAL_TELEPORT_LEVEL_ID = 1010012
local SPECIAL_TELEPORT_X = 8
local SPECIAL_TELEPORT_Y = 13

local FIRST_BOSS_ID = 3010003 -- BOSS赛伊德
local SECOND_BOSS_ID = 3010012 -- BOSS雷斯
local FINAL_BOSS_ID = 3010034 -- BOSS德穆兰

local COMPULSORY1_DISAPPEAR_DIALOGUE_ID = 6019001 -- 打赢赛伊德后的消失对话
local COMPULSORY2_DISAPPEAR_DIALOGUE_ID = 6019002 -- 打赢雷斯后的消失对话
local COMPULSORY2_DIALOGUE_ID = 6010603 -- 打赢雷斯后的强制对话（紧接着就是赛伊德的战斗）

local COMPULSORY3_DISAPPEAR_DIALOGUE_ID = 6019004 -- 互殴完且玩家击败雷斯后的对话
local COMPULSORY3_DIALOGUE_ID = 6019003 -- 互殴完的强制对话（紧接着就是雷斯的战斗）

-- 特殊点类型
local SpecialPointType = {
    HERO_SPAWN        = 1,  -- 主角出生点
    NEWBIE_SPAWN      = 2,  -- 新手主角出生点
    FREE_EXIT         = 3,  -- 无条件撤离点
    PAY_EXIT          = 4,  -- 丢钱撤离点
    LEVER             = 5,  -- 拉闸点
    LEVER_LOCKED_EXIT = 6,  -- 需拉闸撤离点
    TELEPORT          = 7,  -- 联通传送点
    DIALOGUE          = 8,  -- 纯对话点
    WALL              = 9,  -- 墙
    SPAWN             = 10, -- 生成点
    DOOR              = 11, -- 门
}

-- 撤离情况
local EExitType = {
    FAILED  = 0,  -- 死亡
    ESCAPE  = 1,  -- 撤离
    QUIT    = 2,  -- 中退
    PASS    = 3,  -- 通关
}

function ActivityMagicTowerMainPanel:Ctor()
    self._activityID = 0
    self._activityInfo = {}
    self._levelItemData = {}

    self._curLevel = {
        maxLengthX = 1,
        maxLengthY = 1,
        x = 1,
        y = 1,
    }

    self._mapActors = {}
    self._deadEnemies = {}
    self._completedDialogues = {}

    self._treasureGlobalNum = 1
    self._treasureGlobalID = {}

    -- 打包数据
    self._checkSum = ""
    self._curState = 0
    self._newState = 0
    self._sanNum = 0
    self._exitType = EExitType.FAILED

    -- 根面板
    self._wtCanvasPage = self:Wnd("CanvasPanel_0", UIWidgetBase)
    -- 关卡名字
    self._wtLevelNameTxt = self:Wnd("DFTextBlock_42", UITextBlock)
    -- 关卡名字后缀
    self._wtLevelSubNameTxt = self:Wnd("DFTextBlock_160", UITextBlock)
    -- 预测战斗结果
    self._wtCombatResultSwitch = self:Wnd("WBP_CommonSwitch", UIWidgetBase)
    self._wtCombatResultCheckBox = self._wtCombatResultSwitch:Wnd("wtCheckBoxSwitch", UICheckBox)
    self._wtCombatResultCheckBox:SetCallback(self.OnCombatResultStateChanged, self)
    -- 快速战斗
    self._wtQuickCombatSwitch = self:Wnd("WBP_CommonSwitch_1", UIWidgetBase)
    self._wtQuickCombatCheckBox = self._wtQuickCombatSwitch:Wnd("wtCheckBoxSwitch", UICheckBox)
    self._wtQuickCombatCheckBox:SetCallback(self.OnQuickCombatStateChanged, self)
    -- 查看键
    self._wtCheckBtn = self:Wnd("WBP_CommonIconButton", DFCommonCheckButtonOnly)
    self._wtCheckBtn:Event("OnCheckedBoxStateChangedNative", self.OnCheckBtnClicked, self)
    -- 商人键
    self._wtMerchantBtn = self:Wnd("WBP_CommonIconButton_1", DFCommonButtonOnly)
    self._wtMerchantBtn:Event("OnClicked", self.OnMerchantBtnClicked, self)
    -- 返回键
    self._wtBackBtn = self:Wnd("WBP_CommonIconButton_2", DFCommonButtonOnly)
    self._wtBackBtn:Event("OnClicked", self.OnBackBtnClicked, self)

    -- 主角信息框
    self._wtHeroNumericalPanel = self:Wnd("WBP_MorgenGame_NumericalPanel_C_1", ActivityMagicTowerNumericalPanel)
    -- 敌人信息框
    self._wtEnemyNumericalPanel = self:Wnd("WBP_MorgenGame_NumericalPanel_C", ActivityMagicTowerNumericalPanel)
    -- 钥匙卡槽
    self._wtKeyItem = self:Wnd("WBP_MorgenGame_KeyItem", ActivityMagicTowerKeyItem)

    -- 理智
    self._wtTopBar1 = self:Wnd("WBP_Common_TopBarBtn", UIWidgetBase)
    self._wtTopBar1Txt = self._wtTopBar1:Wnd("wtTextBlock_value", UITextBlock)
    -- 经验
    self._wtTopBar2 = self:Wnd("WBP_Common_TopBarBtn_148", UIWidgetBase)
    self._wtTopBar2Txt = self._wtTopBar2:Wnd("wtTextBlock_value", UITextBlock)
    -- 龙门币
    self._wtTopBar3 = self:Wnd("WBP_Common_TopBarBtn_1", UIWidgetBase)
    self._wtTopBar3Txt = self._wtTopBar3:Wnd("wtTextBlock_value", UITextBlock)

    self._wtMoveEffect = self:Wnd("WBP_MorgenGame_Move", ActivityMagicTowerMove)
    self._wtMoveEffect:Collapsed()

    self._debugInfo = false

    self._isMoving = false
    self._navPath = {}

    -- 保险箱管理器
    self.mSafeManager = MTSafeManager:NewIns()

    -- self._LevelInfoReady = false
end

-----------------------------------------------------生命周期-----------------------------------------------------
function ActivityMagicTowerMainPanel:OnOpen()
    math.randomseed(os.time())
    -- Facade.LevelLoadManager:AsyncOperateStreamLevelsBySubStage(ESubStage.MagicTower, true, nil, nil, false, 30)
end

function ActivityMagicTowerMainPanel:_InitEvent()
    self:AddLuaEvent(MTSafeItem.evtItemPickup, self._ApplyAttributeChange, self)
    self:AddLuaEvent(LuaGlobalEvents.evtSceneLoaded,self.OnSceneSubLevelLoaded, self)
    self:AddLuaEvent(ActivityConfig.evtMagicTowerMoveReq, self.OnProcessCharacterMovement, self)
    self:AddLuaEvent(ActivityConfig.evtMagicTowerMoveFinish, self.OnMagicTowerMoveFinish, self)
    self:AddLuaEvent(ActivityConfig.evtMagicTowerSwitchLevel, self.OnProcessSwitchLevel, self)
    self:AddLuaEvent(ActivityConfig.evtMagicTowerSetPlayer, self.ModifyPlayerAttribute, self)
    self:AddLuaEvent(ActivityConfig.evtProcessMagicTowerAstarPath, self.OnProcessMagicTowerAstarPath, self)
    self:AddLuaEvent(ActivityConfig.evtMagicTowerTileMapClickInfo, self.OnProcessMagicTowerAstarPath, self)
    self:AddLuaEvent(ActivityConfig.evtMagicTowerOpenSafePanel, self.OpenSafePanelWithSafeId, self)
    self:AddLuaEvent(ActivityConfig.evtMagicTowerSafePanelLoadFinish, self.OnSafePanelLoadFinish, self)
    self:AddLuaEvent(ActivityConfig.evtMagicTowerLevelStateChanged, self.RecordNewLevelState, self)
    self:AddLuaEvent(ActivityConfig.evtMagicTowerLevelGameOver, self.SetPreGameOver, self)
    self:AddLuaEvent(ActivityConfig.evtMagicTowerResidueBossHealth, self.SetResidueBossHealth, self)
    self:AddLuaEvent(ActivityConfig.evtSwitchMagicTowerDebugInfo, self.OnSwitchMagicTowerDebugInfo, self)

    if DFMGameNotch:IsFoldDevice() then
        if not self.statusHandle then
            self.statusHandle = DFMGameNotch.OnFoldStatusChanged:Add(CreateCPlusCallBack(self.OnFoldStatusChanged, self))
        end
    end
end

function ActivityMagicTowerMainPanel:OnHandleMove(funcName)
    -- if ActivityInputHandler then 
    --     ActivityInputHandler.OnProcessCharacterMovement(funcName)
    -- end
    if self._navPath and next(self._navPath) then 
        self:HideMoveEffect()
        self._navPath = nil --将寻路的路径干掉。
    end
    if not self._isMoving then 
        self:_HandleMovement(funcName)
    end
end

function ActivityMagicTowerMainPanel:OnInitExtraData(activityID, testState)
    self._activityID = activityID or 0
    if not VersionUtil.IsShipping() then
        self._testState = testState
    end
end

function ActivityMagicTowerMainPanel:OnShowBegin()
    self:BindBackAction()
    self:_InitEvent()

    self:_InitData()

    ActivityInputHandler.EnableInput()
    ActivityInputHandler.Init(Facade.UIManager:GetInputMonitor(), CreateCallBack(self._HandleMovement, self))

    self:PlayDefaultDialogue()
end

function ActivityMagicTowerMainPanel:OnHideBegin()
    self:RemoveAllActions()
    self:RemoveAllLuaEvent()
    local displayCtrl = Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.MagicTower)
    if displayCtrl then
        if self._handleMoveEvent then 
            displayCtrl.OnHandleMove:Remove(self._handleMoveEvent)
            self._handleMoveEvent = nil
        end
    end
    if DFMGameNotch:IsFoldDevice() then
        if self.statusHandle then
            DFMGameNotch.OnFoldStatusChanged:Remove(self.statusHandle)
            self.statusHandle = nil
        end
    end
    ActivityInputHandler.Destroy()
end

function ActivityMagicTowerMainPanel:OnClose()
    self:SetTileMapGrayParam(false)

    self:ClearFrameTask()
    ActivityInputHandler.Destroy()
    self.mSafeManager:Release()
    self:_DestroyHeroSpine()
    self:_DestroyAllMapActors()
end

-----------------------------------------------------输入处理-----------------------------------------------------
function ActivityMagicTowerMainPanel:_HandleMovement(funcName, isCheck)
    if not ActivityInputHandler.IsInputEnabled() then
        logerror("输入禁用，无法寻路")
        return
    end
    
    isCheck = setdefault(isCheck, true)

    local dirIdx, dx, dy = 0, 0, 0
    local moveAnimName, idleAnimName

    if not self:_IsMoveFunction(funcName) then
        testErrorLog("不是移动函数！")
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, funcName)
        return
    end

    local dirInfo = self:_GetDirectionInfo(funcName)
    if not dirInfo then return end
    
    local dx, dy = dirInfo.dx, dirInfo.dy
    local moveAnimName, idleAnimName = dirInfo.moveAnim, dirInfo.idleAnim
    self._dirIdx = dirInfo.dirIdx -- 提前记录请求方向

    local targetX = self._curLevel.x + dx
    local targetY = self._curLevel.y + dy
    
    if targetX < 1 or targetX > self._curLevel.maxLengthX or
       targetY < 1 or targetY > self._curLevel.maxLengthY then
        local errStr = string.format("超出地图边界！请求移动坐标(%s, %s），边界限制(%s, %s)", targetX, targetY, self._curLevel.maxLengthX, self._curLevel.maxLengthY)
        if not VersionUtil.IsShipping() and GetGameInstance then
            UKismetSystemLibrary.PrintString(GetGameInstance(), errStr, true, false, FLinearColor(1, 0, 0, 1), 5)
        end
        testErrorLog(errStr)
        return
    end
    
    if isCheck then
        local canMove = self:_CheckTargetGridMovable(targetX, targetY)
        if not canMove then
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, MoveDirection[5])
            self:HideMoveEffect()
            return
        end
    else
        -- 先尽量保证不会出现穿墙，实际上不应该跳过检查，但由于存在交互次数为0/无限的道具，重复调用会导致死循环
        local canMove = self:_CheckTargetGridAgain(targetX, targetY)
        if not canMove then
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, MoveDirection[5])
            self:HideMoveEffect()
            return
        end
    end

    -- 保存动画名称用于移动结束后使用
    self._pendingIdleAnimName = idleAnimName

    self._curLevel.x = targetX
    self._curLevel.y = targetY

    if not VersionUtil.IsShipping() and GetGameInstance then
        UKismetSystemLibrary.PrintString(GetGameInstance(), string.format("主角当前坐标(%s, %s)", self._curLevel.x, self._curLevel.y), true, false, FLinearColor(1, 1, 1, 1), 5)
    end
    testLog(string.format("MainPanel _HandleMovement self._curLevel.x = %s, self._curLevel.y = %s", self._curLevel.x, self._curLevel.y))
    
    self._isMoving = true
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, funcName)
    
    self:UpdatePositionUI(moveAnimName, true)

    self:_CheckSpecialPoint(targetX, targetY)
end

function ActivityMagicTowerMainPanel:_CheckTargetGridAgain(targetX, targetY)
    testLog(string.format("_CheckTargetGridAgain（%s, %s）", targetX, targetY))
    local specialPointID = self._levelGridList[targetX] and self._levelGridList[targetX][targetY]
    if specialPointID == 0 then return true end

    local config = self:_FindSpecialPointConfig(specialPointID)
    if not config then return true end

    if config.specialPointType == SpecialPointType.WALL then
        return self:_HandleWallPointWithoutCheck(targetX, targetY)
    end

    return true
end

function ActivityMagicTowerMainPanel:_GetDirectionInfo(funcName)
    local directionMap = {
        [MoveDirection[1]] = { dx =  0,  dy = -1, dirIdx = 1 },  -- 上
        [MoveDirection[2]] = { dx =  0,  dy =  1, dirIdx = 2 },  -- 下
        [MoveDirection[3]] = { dx = -1,  dy =  0, dirIdx = 3 },  -- 左
        [MoveDirection[4]] = { dx =  1,  dy =  0, dirIdx = 4 },  -- 右
    }
    
    local info = directionMap[funcName]
    if not info then return nil end
    
    info.moveAnim = MoveIdxToAnim[info.dirIdx]
    info.idleAnim = IdleIdxToAnim[info.dirIdx]
    
    return info
end

function ActivityMagicTowerMainPanel:_IsMoveFunction(funcName)
    for i = 1, 4 do
        if funcName == MoveDirection[i] then
            return true
        end
    end

    return false
end

function ActivityMagicTowerMainPanel:UpdatePositionUI(animName, isMoving)
    if animName and self._heroActor then
        self._heroActor:PlayAnim(animName, true)
    end

    ConfigManager.PlayAudio(1, isMoving)
end

function ActivityMagicTowerMainPanel:OnNavBack()
    if self._isBacking then
        testWarningLog("----- 玩家正在离开 -----")
        return
    end

    local function fOnConfirmCallback()
        testWarningLog("----- 玩家确认要离开 -----")
        self:_HandleGameExit(false, 1)
    end

    local function fOnCancelCallback()
        testWarningLog("----- 玩家取消离开 -----")
        self._isBacking = false
        ActivityInputHandler.EnableInput()
    end

    self._isBacking = true
    ActivityInputHandler.DisableInput()
    local confirmTxt = ActivityConfig.Loc.MagicTowerText[11]
    self:ShowConfirmWindow(confirmTxt, fOnConfirmCallback, fOnCancelCallback, ActivityConfig.Loc.Cancel, ActivityConfig.Loc.Confirm)
end

function ActivityMagicTowerMainPanel:OnExit()
    testLog("--- 退出魔塔界面 ---")
    self._isBacking = false
    ConfigManager.PlayMusic(2)
    ConfigManager.PlayMusic(10)
    ConfigManager.ClearMusicIdx()
    Facade.UIManager:CloseUI(self)
end

function ActivityMagicTowerMainPanel:BindBackAction()
    if self._backActionHandle then
        self:RemoveInputActionBinding(self._backActionHandle)
        self._backActionHandle = nil
    end

    self._backActionHandle = self:AddInputActionBinding(
        "Back",
        EInputEvent.IE_Pressed,
        self.OnNavBack,
        self,
        EDisplayInputActionPriority.UI_Pop
    )
end

function ActivityMagicTowerMainPanel:RemoveAllActions()
    if self._backActionHandle then
        self:RemoveInputActionBinding(self._backActionHandle)
        self._backActionHandle = nil
    end
end

-----------------------------------------------------协议区-----------------------------------------------------
function ActivityMagicTowerMainPanel:_OnDataChanged()
    self:_InitData()
    self:RefreshUI()
end

function ActivityMagicTowerMainPanel:SendMagicTowerStartReq()
    if not self._activityID or self._activityID == 0 then return end

    Server.ActivityServer:SendMagicTowerStartReq(self._activityID)
end

function ActivityMagicTowerMainPanel:SendMagicTowerEndReq()
    testLog("----- 准备发送结束协议 -----")
    if not self._activityID or self._activityID == 0 then
        testErrorLog("活动id非法，无法发送结束协议" .. tostring(self._activityID))
        return
    end
    
    local isMeetRewardNPC = self:CheckCondition(REWARD_NPC_CONDITION_ID)
    local gainMoneyNum = Logic.roundDownToInteger(self._player:GetMoney())

    self._checkSum = Logic.concatAndMD5(tostring(gainMoneyNum), tostring(self._exitType), tostring(self._curLevelID), tostring(isMeetRewardNPC), tostring(self._newState), SECRET_KEY)
    if not self._checkSum or type(self._checkSum) ~= "string" then
        testErrorLog("----- checkSum error -----")
        return
    end

    testWarningLog(string.format("发送结束协议：activityID = %s, gainMoneyNum = %s, exitType = %s, curLevelID = %s, isMeetRewardNPC = %s, newState = %s, checkSum = %s",
    tostring(self._activityID), tostring(gainMoneyNum), tostring(self._exitType), tostring(self._curLevelID), tostring(isMeetRewardNPC), tostring(self._newState), self._checkSum))

    Server.ActivityServer:SendMagicTowerEndReq(self._activityID, gainMoneyNum, self._exitType, self._curLevelID, isMeetRewardNPC, self._newState, self._checkSum)
end

-----------------------------------------------------数据区-----------------------------------------------------
function ActivityMagicTowerMainPanel:_InitData()
    -- 获取数据
    self:_FetchDataInfo()
    self:_InitDataConfig()

    -- 若为困难周目需要对数据进行额外处理
    self:_PostProcessDataByHardState()
    self:_PostProcessDataByNoCritState()

    -- 创建玩家实例
    self:_InitPlayer()
end

function ActivityMagicTowerMainPanel:_FetchDataInfo()
    local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(self._activityID)
    if activityInfo == nil or next(activityInfo) == nil then return end

    self.allInfo = activityInfo.arknights_game_info or {}
    self._sanNum = self.allInfo.san_num
    
    if not VersionUtil.IsShipping() then
        if self._testState then
            self._curState = self._testState
            self._testState = nil
        else
            self._curState = self:_CheckLevelState(self.allInfo.game_state)
        end
    else
        self._curState = self:_CheckLevelState(self.allInfo.game_state)
    end
    self._newState = self._curState
    testWarningLog(string.format("----- 当前状态为：%s -----", tostring(self._curState)))
end

function ActivityMagicTowerMainPanel:_InitPlayer()
    local heroID, heroConfig = next(self._heroConfig or {})
    if not heroConfig then
        testErrorLog("_InitPlayer Not find heroConfig!")
        return
    end
    
    local initConfig = {
        health = heroConfig.health,
        maxHealth = heroConfig.health * 10000,
        attack = heroConfig.attack,
        defense = heroConfig.defense,
        hitProb = heroConfig.hitProb,
        avatarAsset = heroConfig.avatarIsset,
        spine = heroConfig.spine,
    }

    local currencyConfig = {
        exp = 0,
        keys = 0,
        money = 0
    }

    self._player = ActivityMagicTowerPlayer.New(initConfig, currencyConfig)
    
    -- local healthChangedCallBack = CreateCallBack(self._HandleHealthChanged, self)
    -- self._player:SetHealthChangedCallback(healthChangedCallBack)
    
    -- local deathCallBack = CreateCallBack(self._OnPlayerDefeated, self)
    -- self._player:SetDeathCallback(deathCallBack) 
end

---@param inLevelId number|nil 关卡ID
---@param levelX number|nil 出生点X坐标
---@param levelY number|nil 出生点Y坐标
function ActivityMagicTowerMainPanel:_InitLevelStructData(inLevelId, levelX, levelY)
    -- 保存当前关卡数据
    if self._curLevelID then
        self:_SaveCurrentLevelData()
    end
    
    -- 初始化本地关卡数据
    self:_InitLocalLevelData(inLevelId)
    
    -- 清除任务
    self:ClearFrameTask()

    -- 播放阴乐
    self:_PlayLevelMusic()

    -- 清理场景对象
    self:_DestroyAllMapActors()

    -- 清理主角对象
    self:_DestroyHeroSpine()

    -- 加载或初始化关卡数据
    if not self._levelItemData[self._curLevelID] then
        self:_InitNewLevelData()
    else
        self:_LoadLevelData()
    end

    -- 设置/修正出生点
    self:_SetSpawnPosition(levelX, levelY)
    self:_InitHeroSpine()

    -- 初始化地图信息并刷新UI
    self:_InitMapInfo()
    self:RefreshUI()

    self:HideMoveEffect()
end

function ActivityMagicTowerMainPanel:_InitLocalLevelData(inLevelId)
    self._curLevelID = inLevelId or self:_InitSpawnLevelID()
    self._curLevelInfo = self._levelConfig[self._curLevelID] or {}

    self._curLevel.maxLengthX = self._curLevelInfo.levelSizeHorizontal or 13
    self._curLevel.maxLengthY = self._curLevelInfo.levelSizeVertical or 13

    self._curLevelStructID = self._curLevelInfo.levelStructure or 0
    self._fightAsset = self._curLevelInfo.fightAsset
end

function ActivityMagicTowerMainPanel:_PlayLevelMusic()
    local levelType = self._curLevelInfo.levelType and tonumber(self._curLevelInfo.levelType) or 0
    if LEVEL_MUSIC_CONVERT[levelType] then
        ConfigManager.PlayMusic(LEVEL_MUSIC_CONVERT[levelType])
    end
end

function ActivityMagicTowerMainPanel:_InitNewLevelData()
    testWarningLog(string.format("_InitNewLevelData, self._curLevelID = %s", self._curLevelID))

    self._levelItemData[self._curLevelID] = {
        spawnedItems = {},
        interactionCounts = {},
        specialPoints = {}
    }
    
    self:_SetGirdList()
    self:_PreprocessSpawnItem()
end

function ActivityMagicTowerMainPanel:_LoadLevelData()
    testLog(string.format("_LoadLevelData, self._curLevelID = %s", self._curLevelID))

    local levelData = self._levelItemData[self._curLevelID]
    self._levelGridList = levelData.specialPoints
    self._itemInteractionCounts = levelData.interactionCounts
    self._spawnedItems = levelData.spawnedItems
    
    for x, row in pairs(levelData.spawnedItems or {}) do
        for y, itemID in pairs(row or {}) do
            self:_CreateMapItemVisual(x, y, itemID)
        end
    end
end

function ActivityMagicTowerMainPanel:_SaveCurrentLevelData()
    if not self._curLevelID then return end
    
    self._levelItemData[self._curLevelID] = {
        spawnedItems = self._spawnedItems,
        interactionCounts = self._itemInteractionCounts,
        specialPoints = self._levelGridList
    }
end

function ActivityMagicTowerMainPanel:_PreprocessSpawnItem()
    self._spawnedItems = {}          ---@type table<x, table<y, itemID|specialPointID>>
    self._itemInteractionCounts = {} ---@type table<x, table<y, countInt>>

    for x, row in ipairs(self._levelGridList or {}) do
        for y, specialPointID in ipairs(row or {}) do
            if specialPointID ~= 0 then
                local config = self:_FindSpecialPointConfig(specialPointID)
                if config then
                    if config.specialPointType == SpecialPointType.SPAWN then
                        local itemID = self:_GenerateSpawnItem(config.spawnProb)
                        if itemID then
                            local direction = config.direction or 1 -- 默认左朝向
                            self._spawnedItems[x] = self._spawnedItems[x] or {}
                            self._spawnedItems[x][y] = itemID
                            self:_CreateMapItemVisual(x, y, itemID, direction)

                            -- 初始化交互次数
                            local itemConfig = self:_GetItemConfig(itemID)
                            if itemConfig then
                                self._itemInteractionCounts[x] = self._itemInteractionCounts[x] or {}
                                self._itemInteractionCounts[x][y] = itemConfig.disappearTime ~= 0 and itemConfig.disappearTime or nil
                            end
                        end
                    elseif config.specialPointType == SpecialPointType.DOOR then
                        self._spawnedItems[x] = self._spawnedItems[x] or {}
                        self._spawnedItems[x][y] = specialPointID -- 使用特殊点ID作为标识
                        self:_CreateMapItemVisual(x, y, specialPointID)
                    end
                end
            end
        end
    end
end

function ActivityMagicTowerMainPanel:_GenerateSpawnItem(spawnProbID)
    if not spawnProbID then return end

    local probGroup = {}
    for _, config in pairs(self._levelSpawnProbConfig) do
        if config.spawnProbID == spawnProbID then
            table.insert(probGroup, config)
        end
    end

    if #probGroup == 0 then
        testWarningLog(string.format("生成item失败，找不到相关Spawn概型配置，spawnProbID = %s", spawnProbID))
    end

    local selected = ActivityLogic.SelectByWeight(probGroup, "weight")
    return selected and selected.result
end

function ActivityMagicTowerMainPanel:_SetGirdList()
    self._levelGridList = {} ---@type table<int, table<int, SpecialPointID>>

    for i = 1, self._curLevel.maxLengthX do
        self._levelGridList[i] = {}
        for j = 1, self._curLevel.maxLengthY do
            self._levelGridList[i][j] = 0
        end
    end

    local levelStructureData = self._levelStructureConfig[self._curLevelStructID]
    if levelStructureData then
        for _, structure in ipairs(levelStructureData) do
            local posX = structure.coordinateX or 0
            local posY = structure.coordinateY or 0
            local specialPoint = structure.specialPoint or 0
        
            if posX >= 1 and posX <= self._curLevel.maxLengthX and posY >= 1 and posY <= self._curLevel.maxLengthY then
                self._levelGridList[posX][posY] = specialPoint
            end
        end
    end
end

function ActivityMagicTowerMainPanel:_SetDebugGirdList()
    if not self._levelGridList then
        self:_SetGirdList()
    end

    local wallPoints = {
        {1,1}, {1,5}, {1,6}, {1,7}, {1,8}, {1,9}, {1,10}, {1,11}, {1,12}, {1,13},
        {2,3}, {2,5}, {2,7}, {2,8}, {2,9}, {2,13},
        {3,1}, {3,2}, {3,3},
        {4,1}, {4,5}, {4,11}, {4,13},
        {5,1}, {5,2}, {5,3}, {5,4}, {5,5}, {5,8}, {5,11}, {5,13},
        {6,8}, {6,13},
        {7,1}, {7,2}, {7,3}, {7,4}, {7,5}, {7,6}, {7,8}, {7,11}, {7,13},
        {8,1}, {8,2}, {8,3}, {8,4}, {8,5}, {8,6}, {8,11}, {8,13},
        {9,1}, {9,2}, {9,3}, {9,4}, {9,5}, {9,6}, {9,8}, {9,11}, {9,13},
        {10,6}, {10,8}, {10,13},
        {11,1}, {11,3}, {11,4}, {11,6}, {11,8}, {11,11}, {11,13},
        {12,3}, {12,4}, {12,6}, {12,11}, {12,13},
        {13,2}, {13,3}, {13,4}, {13,8}, {13,10}, {13,11}, {13,13}
    }

    local WALL_POINT_ID = 2030901

    for _, coord in ipairs(wallPoints) do
        local x, y = coord[1], coord[2]
        if x >= 1 and x <= 13 and y >= 1 and y <= 13 then
            self._levelGridList[x][y] = WALL_POINT_ID
        end
    end

    self._levelGridList[3][7] = SpecialPointType.DIALOGUE
end

function ActivityMagicTowerMainPanel:_GetPlayerSpawnPointID()
    local newbieSpawn, heroSpawn
    
    for _, config in pairs(self._specialPointConfig or {}) do
        if config.specialPointType == SpecialPointType.NEWBIE_SPAWN then
            testLog(string.format("预选择新手出生点（特殊点ID：%s），如果没有找到常规出生点，则采用新手出生点----->", tostring(config.specialPointID)))
            newbieSpawn = config.specialPointID
        elseif config.specialPointType == SpecialPointType.HERO_SPAWN then
            testLog(string.format("预选择常规出生点（特殊点ID：%s）", tostring(config.specialPointID)))
            heroSpawn = config.specialPointID
        end
    end

    return newbieSpawn or heroSpawn
end

function ActivityMagicTowerMainPanel:_GetPlayerSpawnPointLevelStructureID()
    local spawnID = self:_GetPlayerSpawnPointID()
    if spawnID then 
        for levelStructureID, levelStructureData in pairs(self._levelStructureConfig or {}) do
            for _, structure in ipairs(levelStructureData) do
                if structure.specialPoint == spawnID then
                    testLog(string.format("找到出生点（特殊点ID：%s）对应的关卡结构（ID：%s）", tostring(spawnID), tostring(levelStructureID)))
                    return levelStructureID
                end
            end
        end
    end

    return nil
end

function ActivityMagicTowerMainPanel:_InitSpawnLevelID()
    local structureID = self:_GetPlayerSpawnPointLevelStructureID()
    if structureID then        
        for _, value in pairs(self._levelConfig or {}) do
            if value.levelStructure == structureID then
                testLog(string.format("找到出生点（关卡结构：%s）对应的关卡（ID：%s）", tostring(structureID), tostring(value.levelID)))
                return value.levelID
            end
        end
    end

    -- default
    testErrorLog(string.format("关卡结构%s找不到与之对应的关卡ID，默认返回1010006", tostring(structureID)))
    return 1010006
end

function ActivityMagicTowerMainPanel:_SetSpawnPosition(levelX, levelY)
    if levelX and levelY then
        self._curLevel.x = levelX
        self._curLevel.y = levelY
        if not VersionUtil.IsShipping() and GetGameInstance then
            UKismetSystemLibrary.PrintString(GetGameInstance(), string.format("关卡切换，设置主角坐标(%s, %s)", self._curLevel.x, self._curLevel.y), true, false, FLinearColor(1, 1, 0, 1), 10)
        end
        testLog(string.format("设置出生点(%s, %s)", self._curLevel.x, self._curLevel.y))
        return
    end

    local function SetSpawnPoint(spawnType)
        for x, row in ipairs(self._levelGridList or {}) do
            for y, specialPointID in ipairs(row or {}) do
                if specialPointID ~= 0 then
                    local config = self:_FindSpecialPointConfig(specialPointID)
                    if config and config.specialPointType == spawnType then
                        self._curLevel.x = x
                        self._curLevel.y = y
                        if not VersionUtil.IsShipping() and GetGameInstance then
                            UKismetSystemLibrary.PrintString(GetGameInstance(), string.format("关卡初始化主角坐标(%s, %s)", self._curLevel.x, self._curLevel.y), true, false, FLinearColor(1, 1, 0, 1), 10)
                        end
                        testLog(string.format("寻找并设置出生点(%s, %s)", self._curLevel.x, self._curLevel.y))
                        return true
                    end
                end
            end
        end
    
        return false
    end

    if not self._isNewPlayerSpawnPoint then -- 需要记录在后台
        local newbieFound = SetSpawnPoint(SpecialPointType.NEWBIE_SPAWN)
        if newbieFound then
            self._isNewPlayerSpawnPoint = true
            return
        end
    end

    local heroFound = SetSpawnPoint(SpecialPointType.HERO_SPAWN)
    if heroFound then
        return
    end

    -- default
    self._curLevel.x = 3
    self._curLevel.y = 6
    if not VersionUtil.IsShipping() and GetGameInstance then
        UKismetSystemLibrary.PrintString(GetGameInstance(), string.format("关卡初始化主角坐标(%s, %s)", self._curLevel.x, self._curLevel.y), true, false, FLinearColor(1, 0, 0, 1), 10)
    end
    testWarningLog("找不到合适的出生点，当前关卡ID：", tostring(self._curLevelID))
end

function ActivityMagicTowerMainPanel:_DestroyHeroSpine()
    if isvalid(self._heroActor) then
        testLog("_DestroyHeroSpine heroActor")
        self._heroActor:K2_DestroyActor()
        self._heroActor = nil
    end
end

function ActivityMagicTowerMainPanel:_InitHeroSpine()
    if self._heroActor then
        testWarningLog("_InitHeroSpine heroActor is Exist!")
        return
    end

    local heroID, heroConfig = next(self._heroConfig or {})
    if not heroConfig then
        testErrorLog("_InitHeroSpine Not find heroConfig!")
        return
    end

    self:_CreateHeroPortraitOnMap(self._curLevel.x, self._curLevel.y, heroConfig.spine)
    
    -- self._displayCtrlActor = Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.MagicTower)
    -- if not isvalid(self._displayCtrlActor) then
    --     return
    -- end

    -- self._heroActor = self:SpawnSpineActorByCtrl(self._curLevel.x - 1, self._curLevel.y - 1, heroConfig.spine)
    -- if not isvalid(self._heroActor) then
    --     return
    -- end

    -- self._displayCtrlActor.PlayerPaper2D = self._heroActor

    -- local spineConfig = self:_FindSpineConfig(spineID)
    -- if spineConfig then
    --     if self._heroActor and self._heroActor.PlayAnim then
    --         self._heroActor:PlayAnim("idle_front", true)
    --     end    
    -- end
end

function ActivityMagicTowerMainPanel:_InitDataConfig()
    -- 关卡配置 ---@type table<int64, MorgenTowerLevelConfig>
    self._levelConfig = ConfigManager.GetLevelConfigTable()

    -- 关卡结构配置 ---@type table<int64, MorgenTowerLevelStructureConfig>
    self._levelStructureConfig = ConfigManager.GetLevelStructureConfigTable()

    -- 特殊点配置 ---@type table<int64, MorgenTowerLevelSpecialPointConfig>
    self._specialPointConfig = ConfigManager.GetSpecialPointConfigTable()

    -- 联通关系配置 ---@type table<int64, MorgenTowerLevelTransTypeConfig>
    self._levelTransTypeConfig = ConfigManager.GetLevelTransTypeConfigTable()

    -- Spawn概型配置 ---@type table<int64, MorgenTowerLevelSpawnProbConfig>
    self._levelSpawnProbConfig = ConfigManager.GetLevelSpawnProbConfigTable()

    -- 道具配置 ---@type table<int64, MorgenTowerPropConfig>
    self._propConfig = ConfigManager.GetPropConfigTable()

    -- 怪物配置 ---@type table<int64, MorgenTowerEnemyConfig>
    self._enemyConfig = ConfigManager.GetEnemyConfigTable()

    -- NPC配置 ---@type table<int64, MorgenTowerNPCConfig>
    self._npcConfig = ConfigManager.GetNPCConfigTable()

    -- 主角配置 ---@type table<int64, MorgenTowerHeroConfig>
    self._heroConfig = ConfigManager.GetHeroConfigTable()

    -- 保险柜配置 ---@type table<int64, MorgenTowerTreasureConfig>
    self._treasureConfig = ConfigManager.GetTreasureConfigTable()
    
    -- 保险物品配置 ---@type table<int64, MorgenTowerTreasureItemConfig>
    self._treasureItemConfig = ConfigManager.GetTreasureItemConfigTable()
    
    -- 保险概型配置 ---@type table<int64, MorgenTowerTreasureProbConfig>
    self._treasureProbConfig = ConfigManager.GetTreasureProbConfigTable()

    -- 小对话配置 ---@type table<int64, MorgenTowerSmallDialogueConfig>
    self._smallDialogueConfig = ConfigManager.GetSmallDialogueConfigTable()

    -- 小对话台词配置 ---@type table<int64, MorgenTowerSmallDialogueLineConfig>
    self._smallDialogueLineConfig = ConfigManager.GetSmallDialogueLineConfigTable()
    
    -- 大对话配置 ---@type table<int64, MorgenTowerDialogueConfig>
    self._dialogueConfig = ConfigManager.GetDialogueConfigTable()

    -- 对话组配置 ---@type table<int64, MorgenTowerDialogueGroupConfig>
    self._dialogueGroupConfig = ConfigManager.GetDialogueGroupConfigTable()

    -- 台词配置 ---@type table<int64, MorgenTowerDialogueLineConfig>
    self._dialogueLineConfig = ConfigManager.GetDialogueLineConfigTable()

    -- 回答选项配置 ---@type table<int64, MorgenTowerAnswerBranchConfig>
    self._answerBranchConfig = ConfigManager.GetAnswerBranchConfigTable()

    -- png资源配置 ---@type table<int64, MorgenTowerAssetConfig>
    self._assetConfig = ConfigManager.GetAssetConfigTable()

    -- spine动画配置 ---@type table<int64, MorgenTowerSpineConfig>
    self._spineConfig = ConfigManager.GetSpineConfigTable()

    -- 属性变化配置 ---@type table<int64, MorgenTowerAttributeChangeConfig>
    self._attributeChangeConfig = ConfigManager.GetAttributeChangeConfigTable()

    -- 死亡台词配置 ---@type table<int64, MorgenTowerDeathLinesConfig>
    self._deathLinesConfig = ConfigManager.GetDeathLinesConfigTable()

    -- 转场文字配置 ---@type table<int64, MorgenTowerCutSceneConfig>
    self._cutSceneConfig = ConfigManager.GetCutSceneConfigTable()
    
    -- 其它常量配置 ---@type table<int64, MorgenTowerConstantConfig>
    self._constantConfig = ConfigManager.GetConstantConfigTable()

    -- 条件配置 ---@type table<int64, MorgenTowerConditionConfig>
    self._conditionConfig = ConfigManager.GetConditionConfigTable()

    -- 子条件配置 ---@type table<int64, MorgenTowerSubConditionConfig>
    self._subConditionConfig = ConfigManager.GetSubConditionConfigTable()
end

function ActivityMagicTowerMainPanel:_InitMerchantNPCConfig()
    self._merchantNPCConfig = {}
    self._shopNPCUnlockConfig = {}

    for slot = 1, 3 do
        for _, config in pairs(self._dialogueConfig or {}) do
            if config.shopSLot and config.shopSLot == slot then
                self._merchantNPCConfig[slot] = config
                self._shopNPCUnlockConfig[slot] = self:CheckCondition(config.condition)
                break
            end
        end
    end
end

function ActivityMagicTowerMainPanel:_FindSpecialPointConfig(specialPointID)
    if self._specialPointConfig[specialPointID] then
        return self._specialPointConfig[specialPointID]
    end

    return nil
end

function ActivityMagicTowerMainPanel:_FindTransTypeConfig(transTypeID)
    local transTypeConfig = self._levelTransTypeConfig[transTypeID]
    if transTypeConfig then
        return transTypeConfig
    end

    return nil
end

function ActivityMagicTowerMainPanel:_FindPropConfig(propID)
    local propConfig = self._propConfig[propID]
    if propConfig then
        return propConfig
    end
    
    return nil
end

function ActivityMagicTowerMainPanel:_FindAttributeChangeConfig(attributeChangeID)
    local attributeChangeConfig = self._attributeChangeConfig[attributeChangeID]
    if attributeChangeConfig then
        return attributeChangeConfig
    end
    
    return nil
end

function ActivityMagicTowerMainPanel:_FindEnemyConfig(enemyID)
    local enemyConfig = self._enemyConfig[enemyID]
    if enemyConfig then
        return enemyConfig
    end
    
    return nil
end

function ActivityMagicTowerMainPanel:_ResetEnemyConfig(rise)
    if not rise or type(rise) ~= "number" or rise <= 0 then
        return
    end
    
    for enemyID, config in pairs(self._enemyConfig) do
        if config.health then
            config.health = Logic.roundToInteger(config.health * rise)
        end
        
        if config.attack then
            config.attack = Logic.roundToInteger(config.attack * rise)
        end
        
        if config.defense then
            config.defense = Logic.roundToInteger(config.defense * rise)
        end
        
        if config.hitProb then
            config.hitProb = math.min(Logic.roundToInteger(config.hitProb * rise), 100)
        end
    end
end

function ActivityMagicTowerMainPanel:_FindNPCConfig(npcID)
    local npcConfig = self._npcConfig[npcID]
    if npcConfig then
        return npcConfig
    end
    
    return nil
end

function ActivityMagicTowerMainPanel:_FindTreasureConfig(treasureID)
    local treasureConfig = self._treasureConfig[treasureID]
    if treasureConfig then
        return treasureConfig
    end
    
    return nil
end

function ActivityMagicTowerMainPanel:_FindSpineConfig(spineID)
    local spineConfig = self._spineConfig[spineID]
    if spineConfig then
        return spineConfig
    end

    return nil
end

function ActivityMagicTowerMainPanel:_FindAssetConfig(assetID)
    local assetConfig = self._assetConfig[assetID]
    if assetConfig then
        return assetConfig
    end

    return nil
end

function ActivityMagicTowerMainPanel:OnProcessCharacterMovement(moveDirection)
    if not moveDirection then return end

    local moveFuncName = MoveDirection[tonumber(moveDirection)]
    if moveFuncName then
        ActivityInputHandler.OnProcessCharacterMovement(moveFuncName)
    end
end

function ActivityMagicTowerMainPanel:OnMagicTowerMoveFinish()
    self._isMoving = false

    if isvalid(self._heroActor)  then
        loginfo("[ActivityMagicTowerMainPanel:OnMagicTowerMoveFinish] Set Hero Actor ZOrder to: ", self._curLevel.y)
        self._heroActor:SetZOrder(self._curLevel.y -1)
    else
        logerror("Hero actor is not valid, cannot set ZOrder.")
    end

    if self._pendingIdleAnimName then
        self:UpdatePositionUI(self._pendingIdleAnimName, false)
        self._pendingIdleAnimName = nil
    end

    if self._navPath and #self._navPath > 0 then
        local isLast = false
        if #self._navPath == 1 then
            isLast = true
        end
        local node = table.remove(self._navPath, 1)

        self:OnProcessNavNode(node)

        testLog("MainPanel OnMagicTowerMoveFinish:", node.x, " ",  node.y)

        if isLast then
           self:HideMoveEffect()
        end

    end
end

function ActivityMagicTowerMainPanel:OnProcessSwitchLevel(inLevelId, levelX, levelY)
    self:_InitLevelStructData(inLevelId, levelX, levelY)
end

function ActivityMagicTowerMainPanel:OnSwitchMagicTowerDebugInfo(inDebugInfo)
    self._debugInfo = inDebugInfo
    if self._debugInfo then
        local girdvec = {}
        local levelStructureData = self._levelStructureConfig[self._curLevelStructID]
        if levelStructureData then
            for _, structure in ipairs(levelStructureData) do
                local posX = structure.coordinateX - 1 or 0
                local posY = structure.coordinateY - 1 or 0
                if structure.specialPoint == 2030901 then -- debug 输出墙的位置
                    table.insert(girdvec, FVector2D(posX, posY))
                end
            end
        end

        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, "ShowDebugGirdInfo", girdvec)
    else
        local girdvec = {}
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, "ShowDebugGirdInfo", girdvec)

        local AStarNodes = {}
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, "ShowDebugAStarInfo", AStarNodes)
    end
end

function ActivityMagicTowerMainPanel:OnProcessMagicTowerAstarPath(x, y)
    if self._isChecked then
        self:_ShowEnemyDetailInfos(x, y)
        return
    end

    if not ActivityInputHandler.IsInputEnabled() then 
        logerror("输入禁用，无法寻路")
        return
    end

    testLog("OnProcessMagicTowerAstarPath x:", x, " y:", y)

    local curLevelX = self._curLevel.x
    local curLevelY = self._curLevel.y

    local newNode = {
        x = x, y = y,
        g = 0, h = 0, f = 0,
        parent = nil
    }

    local curNode = {
        x = curLevelX, y = curLevelY,
        g = 0, h = 0, f = 0,
        parent = nil
    }
    self._navPath = ActivityMagicTowerAStarHandler.aStar(self._levelGridList, curNode, newNode,true)
    if not (self._navPath and next(self._navPath)) then 
        self._navPath = ActivityMagicTowerAStarHandler.aStar(self._levelGridList, curNode, newNode)
    end
    if self._navPath then
        local lastNode = self._navPath[#self._navPath]
        self:ShowMoveEffect(FVector2D(lastNode.x,lastNode.y))
    end

    if IsInEditor() and self._debugInfo then
        local AStarNodes = {}
        if self._navPath then
            print("找到路径：")
            for i, node in ipairs(self._navPath) do
                table.insert(AStarNodes, FVector2D(node.x - 1, node.y - 1))
                print("节点：".. i..": ("..node.x..", "..node.y..")")
            end

        else
            print("未找到路径")
        end
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, "ShowDebugAStarInfo", AStarNodes)
    end
    if self._navPath then 
        local specialPointID = self._levelGridList[x] and self._levelGridList[x][y]
        if specialPointID == 0 then 
            local way = {}
            for _,node in ipairs(self._navPath) do 
                local specialPointID = self._levelGridList[node.x] and self._levelGridList[node.x][node.y]
                if specialPointID == 0 then 
                    table.insert(way,node)
                else
                    local config = self:_FindSpecialPointConfig(specialPointID)
                    if config and config.specialPointType ~= SpecialPointType.SPAWN then  
                        table.insert(way,node)
                    else
                        Module.CommonTips:ShowSimpleTip(ActivityConfig.Loc.NoWay)
                        break
                    end
                end
            end
            self._navPath = way
        end
    end
    if not self._isMoving then 
        if self._navPath and #self._navPath > 0 then
            local isLast = false
            if #self._navPath == 1 then
                isLast = true
            end

            local node = table.remove(self._navPath, 1)
            self:OnProcessNavNode(node)

            if isLast then
                self:HideMoveEffect()
             end
        end        
    end
end

function ActivityMagicTowerMainPanel:OnProcessNavNode(node)
    if node.x ~= self._curLevel.x then
        if node.x > self._curLevel.x then
            self:_HandleMovement(MoveDirection[4])
        else
            self:_HandleMovement(MoveDirection[3])
        end
    elseif node.y ~= self._curLevel.y then
        if node.y > self._curLevel.y then
            self:_HandleMovement(MoveDirection[2])
        else
            self:_HandleMovement(MoveDirection[1])
        end
    else
        self:_HandleMovement(MoveDirection[5])
    end
end

function ActivityMagicTowerMainPanel:OnSceneSubLevelLoaded(curSubStageType)
    if curSubStageType ~= ESubStage.MagicTower then
        return
    end
    
    local displayCtrl = Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.MagicTower)
    if displayCtrl then
        if self._handleMoveEvent then 
            displayCtrl.OnHandleMove:Remove(self._handleMoveEvent)
            self._handleMoveEvent = nil
        end
        self._handleMoveEvent = displayCtrl.OnHandleMove:Add(CreateCPlusCallBack(self.OnHandleMove, self))
    end
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, "OnDisplayCtrlUnInit")
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, "OnDisplayCtrlInit")
    self._mapResLoaded = true

    local inLevelId, x, y = self:_PostProcessDataByMaxState()
    self:_InitLevelStructData(inLevelId, x, y)

    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, "SetCustomViewTarget")
end

function ActivityMagicTowerMainPanel:_InitMapInfo()
    if not self._mapResLoaded then return end
    local length = ActivityConfig.MogenTower.StepLength
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, "SwitchTileMapByMapId", self._curLevelID)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, "InitMapParams", self._curLevel.maxLengthX, self._curLevel.maxLengthY, length)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, "InitCameraViewLimit", 5, 3)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, "InitPlayerAndCameraSpawnPoint", self._curLevel.x - 1, self._curLevel.y - 1)


    -- 创建调试网格
    if IsInEditor() and self._debugInfo then
        local girdvec = {}
        local levelStructureData = self._levelStructureConfig[self._curLevelStructID]
        if levelStructureData then
            for _, structure in ipairs(levelStructureData) do
                local posX = structure.coordinateX - 1 or 0
                local posY = structure.coordinateY - 1 or 0
                if structure.specialPoint == 2030901 then -- debug 输出墙的位置
                    table.insert(girdvec, FVector2D(posX, posY))
                end
            end
        end

        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, "ShowDebugGirdInfo", girdvec)
    end
end

-----------------------------------------------------核心逻辑-----------------------------------------------------
function ActivityMagicTowerMainPanel:_CheckTargetGridMovable(targetX, targetY)
    testLog(string.format("_CheckTargetGridMovable（%s, %s）", targetX, targetY))
    local specialPointID = self._levelGridList[targetX] and self._levelGridList[targetX][targetY]
    if specialPointID == 0 then return true end

    local config = self:_FindSpecialPointConfig(specialPointID)
    if not config then return true end

    local typeHandlers = {
        [SpecialPointType.WALL] = function()
            return self:_HandleWallPointWithoutCheck(targetX, targetY)
        end,
        [SpecialPointType.DOOR] = function()
            return self:_HandleDoorPoint(config, targetX, targetY)
        end,
        [SpecialPointType.LEVER] = function()
            return self:_HandleLeverPoint(config)
        end,
        [SpecialPointType.FREE_EXIT] = function()
            return self:_HandleExitPoint(config)
        end
    }

    local handler = typeHandlers[config.specialPointType]
    if handler then
        return handler()
    end

    local itemID = self._spawnedItems[targetX] and self._spawnedItems[targetX][targetY]
    if not itemID then return true end

    local itemType = ConfigManager.ParseItemTypeByID(itemID)
    local itemHandlers = {
        [ITEM_CLASS_NAME.ENEMY] = function()
            self:_HandleEnemyItem(itemID, targetX, targetY)
            return false
        end,
        [ITEM_CLASS_NAME.PROP] = function()
            self:_HandlePropItem(itemID, targetX, targetY)
            return false
        end,
        [ITEM_CLASS_NAME.NPC] = function()
            self:_HandleNPCItem(itemID, targetX, targetY)
            return false
        end,
        [ITEM_CLASS_NAME.TREASURE] = function()
            self:_HandleTreasureItem(itemID, targetX, targetY)
            return false
        end
    }
    
    handler = itemHandlers[itemType]
    if handler then
        return handler()
    end

    return true
end

function ActivityMagicTowerMainPanel:_CheckSpecialPoint(targetX, targetY)
    testLog(string.format("_CheckSpecialPoint（%s, %s）", targetX, targetY))
    local specialPointID = self._levelGridList[targetX] and self._levelGridList[targetX][targetY]
    if not specialPointID or specialPointID == 0 then return end
    
    local config = self:_FindSpecialPointConfig(specialPointID)
    if not config then return end
    
    local handlers = {
        -- [SpecialPointType.SPAWN] = function()
        --     self:_HandleSpawnPoint(targetX, targetY)
        -- end,
        [SpecialPointType.DIALOGUE] = function()
            self:_HandleDialoguePoint(config, targetX, targetY)
        end,
        [SpecialPointType.TELEPORT] = function()
            self:_PreHandleTeleportPoint(config)
        end
    }
    
    local handler = handlers[config.specialPointType]
    if handler then
        handler()
    end
end

function ActivityMagicTowerMainPanel:_OnStartCheckSpecialPoint(targetX, targetY) 
    local specialPointID = self._levelGridList[targetX] and self._levelGridList[targetX][targetY]
    if not specialPointID or specialPointID == 0 then return end
    
    local config = self:_FindSpecialPointConfig(specialPointID)
    if not config then return end
    
    local handlers = {
        -- [SpecialPointType.SPAWN] = function()
        --     self:_HandleSpawnPoint(targetX, targetY)
        -- end,
        [SpecialPointType.DIALOGUE] = function()
            self:_HandleDialoguePoint(config, targetX, targetY)
        end
    }
    
    local handler = handlers[config.specialPointType]
    if handler then
        handler()
    end
end

-- 只处理数据层销毁
function ActivityMagicTowerMainPanel:_RemoveSpecialPointInGrid(x, y)
    if not x or not y then return end

    if self._levelGridList[x] and self._levelGridList[x][y] then
        self._levelGridList[x][y] = 0
    end

    if self._spawnedItems[x] and self._spawnedItems[x][y] then
        self._spawnedItems[x][y] = nil
    end

    if self._itemInteractionCounts[x] and self._itemInteractionCounts[x][y] then
        self._itemInteractionCounts[x][y] = nil
    end
end

function ActivityMagicTowerMainPanel:_HandleWallPointWithoutCheck(targetX, targetY)
    testWarningLog(string.format("MainPanel _CheckTargetGridMovable Wall Block! targetX = %s, targetY = %s", targetX, targetY))
    return false
end

function ActivityMagicTowerMainPanel:_HandleDoorPoint(config, targetX, targetY)
    if config.specialPointID == UNABLE_OPEN_DOOR_ID1 or config.specialPointID == UNABLE_OPEN_DOOR_ID2 then
        if config.dialogue and config.dialogue ~= 0 then
            local finishCallBack = CreateCallBack(function()
                ActivityInputHandler.EnableInput()
            end, self)
            
            ActivityInputHandler.DisableInput()
            self:OpenDialoguePanel(config.dialogue, finishCallBack)
            return false
        end

        if config.unableTip and config.unableTip ~= "" then
            Module.CommonTips:ShowSimpleTip(config.unableTip)
        end
        return false
    end

    if self._player:GetKeys() > 0 then
        self._player:ModifyKeys(1, 2)
        self:_RefreshKeyItem()

        Module.CommonTips:ShowSimpleTip(ActivityConfig.Loc.MagicTowerText[15])

        if config.dialogue and config.dialogue ~= 0 then
            local finishCallBack = CreateCallBack(function()
                ConfigManager.PlayAudio(3)
                ActivityInputHandler.EnableInput()
                self:_RemoveSpecialPointInGrid(targetX, targetY)
                self:_DestroyMapItemResource(targetX, targetY, config.specialPointID)
            end, self)
            
            ActivityInputHandler.DisableInput()
            self:OpenDialoguePanel(config.dialogue, finishCallBack)
            return false
        else
            ConfigManager.PlayAudio(3)
            self:_RemoveSpecialPointInGrid(targetX, targetY)
            self:_DestroyMapItemResource(targetX, targetY, config.specialPointID)
            return true
        end
    else
        Module.CommonTips:ShowSimpleTip(ActivityConfig.Loc.MagicTowerText[16])

        testErrorLog(string.format("MainPanel _CheckTargetGridMovable Door Block! targetX = %s, targetY = %s", targetX, targetY))
        return false
    end
end

function ActivityMagicTowerMainPanel:_HandleLeverPoint(config)
    if config.dialogue and config.dialogue ~= 0 then
        local finishCallBack = CreateCallBack(function(self, isCancelAction)
            ActivityInputHandler.EnableInput()
            if not isCancelAction then
                testWarningLog("拉闸成功，替换整张底图")
            end
        end, self)

        ActivityInputHandler.DisableInput()
        self:OpenDialoguePanel(config.dialogue, finishCallBack)
    end
    return false
end

function ActivityMagicTowerMainPanel:_HandleExitPoint(config)
    if config.dialogue and config.dialogue ~= 0 then
        local finishCallBack = CreateCallBack(function(self, isCancelAction)
            if isCancelAction then
                if config.unableTip and config.unableTip ~= "" then
                    Module.CommonTips:ShowSimpleTip(config.unableTip)
                end
                ActivityInputHandler.EnableInput()
            else
                if config.completeTip and config.completeTip ~= "" then
                    Module.CommonTips:ShowSimpleTip(config.completeTip)
                end
                self:_HandleGameExit(true)
            end
        end, self)

        ActivityInputHandler.DisableInput()
        self:OpenDialoguePanel(config.dialogue, finishCallBack)
        return false
    else
        if config.completeTip and config.completeTip ~= "" then
            Module.CommonTips:ShowSimpleTip(config.completeTip)
        end
        self:_HandleGameExit(true)
        return true
    end
end

function ActivityMagicTowerMainPanel:_HandleDialoguePoint(specialPointConfig, targetX, targetY)
    local dialogueID = specialPointConfig.dialogue
    if not dialogueID then return end

    self:_RemoveSpecialPointInGrid(targetX, targetY)
    
    ActivityInputHandler.DisableInput()
    self:OpenDialoguePanel(dialogueID)
end

function ActivityMagicTowerMainPanel:_HandleTeleportPoint(specialPointConfig)
    local transType = specialPointConfig.transType
    if not transType then return end

    local transTypeConfig = self:_FindTransTypeConfig(transType)
    if not transTypeConfig then return end

    ConfigManager.PlayAudio(4)
    self:_InitLevelStructData(transTypeConfig.targetLevel, transTypeConfig.targetX, transTypeConfig.targetY)
end

function ActivityMagicTowerMainPanel:_HandleSpecialTeleport()
    ConfigManager.PlayAudio(4)
    self:_InitLevelStructData(SPECIAL_TELEPORT_LEVEL_ID, SPECIAL_TELEPORT_X, SPECIAL_TELEPORT_Y)
end

function ActivityMagicTowerMainPanel:SetIsExecuteTeleport(isExecute)
    self._isExecuteTeleport = isExecute
end

function ActivityMagicTowerMainPanel:_HandleChangeHeroAttribute()
    if not self._player then return end

    local playerAttack = self._player:GetAttack()
    local playerHitProb = self._player:GetHitProb()
    playerAttack = playerAttack * (1 + 2 * playerHitProb / 100)

    testWarningLog(string.format("直接执行属性变更，玩家攻击力：%s，暴击率：%s", tostring(playerAttack), playerHitProb and tostring(playerHitProb / 100) or "nil"))

    self._player:ModifyAttack(playerAttack, 4)
    self._player:ModifyHitProb(0, 3)

    self:_RefreshPlayerUI()
    self._player:ResetLastChangeTypes()

    ConfigManager.PlayAudio(7)
    self:_RefreshAllPredictionUI()
end

function ActivityMagicTowerMainPanel:SetIsChangeHeroAttribute(isChange)
    self._isChangeHeroAttribute = isChange
end

function ActivityMagicTowerMainPanel:_PreHandleTeleportPoint(specialPointConfig)
    if specialPointConfig.dialogue and specialPointConfig.dialogue ~= 0 then
        local finishCallBack = CreateCallBack(function(self, isCancelAction)
            ActivityInputHandler.EnableInput()
            if not isCancelAction then
                self:_HandleTeleportPoint(specialPointConfig)
            end
        end, self)
        
        ActivityInputHandler.DisableInput()
        self:OpenDialoguePanel(specialPointConfig.dialogue, finishCallBack)
    else
        self:_HandleTeleportPoint(specialPointConfig)
    end
end

function ActivityMagicTowerMainPanel:_HandleSpawnPoint(targetX, targetY)
    local itemID = self._spawnedItems[targetX] and self._spawnedItems[targetX][targetY]
    if not itemID then return end

    if ConfigManager.ParseItemTypeByID(itemID) == ITEM_CLASS_NAME.PROP then
        self:_HandlePropItem(itemID, targetX, targetY)
    end
end

function ActivityMagicTowerMainPanel:_HandlePropItem(itemID, targetX, targetY)
    local propConfig = self:_FindPropConfig(itemID)
    if not propConfig then return end

    local function handlePropEffect()
        ConfigManager.PlayAudio(5)
        self:_ApplyAttributeChange(propConfig.attributeChange)
        self:_HandleItemDisappear(itemID, targetX, targetY, propConfig)
        self:_HandleSpecialPropItem(itemID, propConfig)
        self:_CheckIsPlayerDeath()
        self:_HandleMovement(MoveDirection[self._dirIdx], false)
    end

    if propConfig and propConfig.dialogue and propConfig.dialogue ~= 0 then
        local finishCallBack = CreateCallBack(function(self, isCancelAction)
            ActivityInputHandler.EnableInput()
            if not isCancelAction then
                handlePropEffect()
            end
        end, self)

        ActivityInputHandler.DisableInput()
        self:OpenDialoguePanel(propConfig.dialogue, finishCallBack)
    else
        handlePropEffect()
    end
end

function ActivityMagicTowerMainPanel:_HandleSpecialPropItem(itemID, config)
    if not itemID or not config or itemID ~= SPECIAL_PROP_ID then return end

    if config.disappearNote and config.disappearNote ~= "" then
        Module.CommonTips:ShowSimpleTip(config.disappearNote)
    end
end

-- 属性变更方法
function ActivityMagicTowerMainPanel:_ApplyAttributeChange(attributeChangeID)
    if not self._player then return end
    if not attributeChangeID or attributeChangeID == 0 then return end

    local attributeChangeConfig = self:_FindAttributeChangeConfig(attributeChangeID)
    if not attributeChangeConfig then return end

    local modifiers = {
        ["ModifyHealth" ] = { way = attributeChangeConfig.healthChangeWay , value = attributeChangeConfig.healthChangeValue , isKeyAttr = true  },
        ["ModifyAttack" ] = { way = attributeChangeConfig.attackChangeWay , value = attributeChangeConfig.attackChangeValue , isKeyAttr = true  },
        ["ModifyDefense"] = { way = attributeChangeConfig.defenseChangeWay, value = attributeChangeConfig.defenseChangeValue, isKeyAttr = true  },
        ["ModifyHitProb"] = { way = attributeChangeConfig.hitProbChangeWay, value = attributeChangeConfig.hitProbChangeValue, isKeyAttr = true  },
        ["ModifyExp"    ] = { way = attributeChangeConfig.expChangeWay    , value = attributeChangeConfig.expChangeValue    , isKeyAttr = false },
        ["ModifyKeys"   ] = { way = attributeChangeConfig.keyChangeWay    , value = attributeChangeConfig.keyChangeValue    , isKeyAttr = false },
        ["ModifyMoney"  ] = { way = attributeChangeConfig.moneyChangeWay  , value = attributeChangeConfig.moneyChangeValue  , isKeyAttr = false },
    }

    local anyChanged = false
    local anyCombatAttributeChanged = false

    for methodName, modifier in pairs(modifiers) do
        if modifier.way and modifier.way ~= 0 and modifier.value then
            local changed = self._player[methodName](self._player, modifier.value, modifier.way)
            anyChanged = changed or anyChanged
            if changed and modifier.isKeyAttr then
                anyCombatAttributeChanged = true
            end
        end
    end

    if anyChanged then
        self:_RefreshPlayerUI()
    
        -- 重置变更标记
        self._player:ResetLastChangeTypes()

        if anyCombatAttributeChanged then
            ConfigManager.PlayAudio(7)
        end
    end

    self:_RefreshAllPredictionUI()
end

function ActivityMagicTowerMainPanel:_EnhancedAllEnemy()
    self:_ResetEnemyConfig(1.3)
end

function ActivityMagicTowerMainPanel:_HandleEnemyItem(itemID, targetX, targetY)
    local enemyConfig = self:_FindEnemyConfig(itemID)
    if not enemyConfig then return end

    if enemyConfig.dialogue and enemyConfig.dialogue ~= 0 then
        self:_ProcessPreCombatDialogue(enemyConfig, targetX, targetY)
    else
        self:_ProcessCombatConfirmation(enemyConfig, targetX, targetY)
    end
end

-- 处理战斗前对话
function ActivityMagicTowerMainPanel:_ProcessPreCombatDialogue(enemyConfig, targetX, targetY)
    local finishCallBack = CreateCallBack(function(self, isCancelAction)
        if isCancelAction then
            ActivityInputHandler.EnableInput()
        else
            self:_ProcessCombatConfirmation(enemyConfig, targetX, targetY)
        end
    end, self)

    ActivityInputHandler.DisableInput()
    self:OpenDialoguePanel(enemyConfig.dialogue, finishCallBack)
end

-- 处理战斗二次确认
function ActivityMagicTowerMainPanel:_ProcessCombatConfirmation(enemyConfig, targetX, targetY)
    if enemyConfig.isCompulsoryConfirming == 1 and enemyConfig.confirmingText and enemyConfig.confirmingText ~= "" then
        self:_ShowCombatConfirm(enemyConfig, targetX, targetY)
    else
        self:_CheckPredictionAndStartCombat(enemyConfig, targetX, targetY)
    end
end

-- 检查预测并开始战斗
function ActivityMagicTowerMainPanel:_CheckPredictionAndStartCombat(enemyConfig, targetX, targetY)
    if self._enemyPredictions then
        local prediction = self._enemyPredictions[enemyConfig.enemyID or 0]
        local confirmType = ActivityMagicTowerCombatLogic.GetCombatResultType(prediction, self._player:GetStatusSnapshot(), enemyConfig, self._isNoCritMode)
        if confirmType and confirmType ~= 0 then
            self:_ShowPredictionConfirmDialog(confirmType, enemyConfig, targetX, targetY)
        else
            self:_StartCombat(enemyConfig, targetX, targetY)
        end
    else
        self:_StartCombat(enemyConfig, targetX, targetY)
    end
end

-- 显示预测二次确认弹窗
---@param confirmType number  弹窗类型（1: 高度不确定 2： 必然失败）
function ActivityMagicTowerMainPanel:_ShowPredictionConfirmDialog(confirmType, enemyConfig, targetX, targetY)
    local confirmTxt = ""
    if confirmType == 1 then
        confirmTxt = ActivityConfig.Loc.MagicTowerText[6]
    elseif confirmType == 2 then
        confirmTxt = ActivityConfig.Loc.MagicTowerText[1]
    else
        return
    end

    local function fOnConfirmCallback()
        self:_StartCombat(enemyConfig, targetX, targetY)
    end

    local function fOnCancelCallback()
        ActivityInputHandler.EnableInput()
    end

    self:ShowConfirmWindow(confirmTxt, fOnConfirmCallback, fOnCancelCallback, ActivityConfig.Loc.Cancel, ActivityConfig.Loc.Confirm)
end

-- 显示战斗二次确认弹窗
function ActivityMagicTowerMainPanel:_ShowCombatConfirm(enemyConfig, targetX, targetY)
    local function fOnConfirmCallback()
        self:_CheckPredictionAndStartCombat(enemyConfig, targetX, targetY)
    end

    local function fOnCancelCallback()
        ActivityInputHandler.EnableInput()
    end

    local confirmTxt = enemyConfig.confirmingText
    if not confirmTxt or confirmTxt == "" then return end

    self:ShowConfirmWindow(confirmTxt, fOnConfirmCallback, fOnCancelCallback, ActivityConfig.Loc.Cancel, ActivityConfig.Loc.Confirm)
end

-- 开始战斗流程
function ActivityMagicTowerMainPanel:_StartCombat(enemyConfig, targetX, targetY)
    ActivityInputHandler.DisableInput()
    self:_ProcessCombatResult(enemyConfig, targetX, targetY)
end

-- 战斗结果处理
function ActivityMagicTowerMainPanel:_ProcessCombatResult(enemyConfig, targetX, targetY)
    local isAutoCombat = self._isAutoCombat and enemyConfig.isCompulsoryAnimation ~= 1
    if isAutoCombat then
        self:_AutoCombat(enemyConfig, targetX, targetY)
    else
        self:_ManualCombat(enemyConfig, targetX, targetY)
    end
end

-- 自动战斗
function ActivityMagicTowerMainPanel:_AutoCombat(enemyConfig, targetX, targetY)
    local battleResult = ActivityMagicTowerCombatLogic.CalculateFullCombatResult(self._player:GetStatusSnapshot(), enemyConfig, self._isNoCritMode)
    ActivityMagicTowerCombatLogic.RecordBattleLog(battleResult)
    
    self._player:ModifyHealth(battleResult.playerFinalHealth, 4)
    self:_RefreshPlayerUI()
    
    if battleResult.result == "win" then
        ConfigManager.PlayAudio(8)
        self:_OnEnemyDefeated(enemyConfig, targetX, targetY)
        self:_HandleMovement(MoveDirection[self._dirIdx], false)
    else
        self:_OnPlayerDefeated(enemyConfig)
    end
end

-- 手动战斗
function ActivityMagicTowerMainPanel:_ManualCombat(enemyConfig, targetX, targetY)
    local combatCallBack = CreateCallBack(function(ownUI, popUI, result, playerFinalHealth)
        if not result then
            ActivityInputHandler.EnableInput()
            return
        end

        self._player:ModifyHealth(playerFinalHealth, 4)
        self:_RefreshPlayerUI()
        
        if result == "win" then
            self:_OnEnemyDefeated(enemyConfig, targetX, targetY)
            self:_HandleMovement(MoveDirection[self._dirIdx], false)
        else
            self:_OnPlayerDefeated(enemyConfig)
        end
    end, self)

    Facade.UIManager:AsyncShowUI(UIName2ID.ActivityMagicTowerCombatPanel,
    nil,
    nil,
    self._activityID,
    {enemyAvatarAsset = enemyConfig.avatarIsset, heroAvatarAsset = self._player:GetAvatarAsset(), fightAsset = self._fightAsset,
    enemySpine = enemyConfig.spine, heroSpine = self._player:GetSpine()},
    {enemyConfig = enemyConfig, playerData = self._player:GetStatusSnapshot()},
    combatCallBack, self._isNoCritMode)
end

-- 虚拟战斗
function ActivityMagicTowerMainPanel:_VirtualCombat(enemyID)
    local enemyConfig = self:_FindEnemyConfig(enemyID)
    if not enemyConfig then return end

    local combatCallBack = CreateCallBack(function(ownUI, popUI, result, playerFinalHealth)
        if not result then
            ActivityInputHandler.EnableInput()
            return
        end

        self._player:ModifyHealth(playerFinalHealth, 4)
        self:_RefreshPlayerUI()
        
        if result == "win" then
            self:_OnVirtualEnemyDefeated(enemyConfig)
            if self._specialCombatType == 2 then
                self._specialCombatType = nil -- 保证时序

                ActivityInputHandler.DisableInput()
                self:OpenDialoguePanel(COMPULSORY2_DIALOGUE_ID)
            else
                self._specialCombatType = nil -- 保底清除
            end
        else
            self:_OnPlayerDefeated(enemyConfig)
        end

    end, self)

    local health
    if self._residueBossHealth then
        health = self._residueBossHealth
        self._residueBossHealth = nil
    end

    Facade.UIManager:AsyncShowUI(UIName2ID.ActivityMagicTowerCombatPanel,
    nil,
    nil,
    self._activityID,
    {enemyAvatarAsset = enemyConfig.avatarIsset, heroAvatarAsset = self._player:GetAvatarAsset(), fightAsset = self._fightAsset,
    enemySpine = enemyConfig.spine, heroSpine = self._player:GetSpine()},
    {enemyConfig = enemyConfig, playerData = self._player:GetStatusSnapshot()},
    combatCallBack, self._isNoCritMode, nil, health)
end

function ActivityMagicTowerMainPanel:SetResidueBossHealth(health)
    self._residueBossHealth = health
end

function ActivityMagicTowerMainPanel:_VirtualMutualCombat(enemyID1, enemyID2)
    local enemyConfig1 = self:_FindEnemyConfig(enemyID1)
    if not enemyConfig1 then return end

    local enemyConfig2 = self:_FindEnemyConfig(enemyID2)
    if not enemyConfig2 then return end

    local combatCallBack = CreateCallBack(function(ownUI, popUI, result, playerFinalHealth)
        if not result then
            ActivityInputHandler.EnableInput()
            return
        end
        
        local enemyConfig = enemyConfig2
        if result == "win" then
            enemyConfig = enemyConfig1
        end
        self:_OnVirtualEnemyDefeated(enemyConfig)

        if self._specialCombatType == 4 then
            self._specialCombatType = nil -- 保证时序

            ActivityInputHandler.DisableInput()
            self:OpenDialoguePanel(COMPULSORY3_DIALOGUE_ID)
        else
            self._specialCombatType = nil -- 保底清除
        end

    end, self)

    Facade.UIManager:AsyncShowUI(UIName2ID.ActivityMagicTowerCombatPanel,
    nil,
    nil,
    self._activityID,
    {enemyAvatarAsset = enemyConfig2.avatarIsset, heroAvatarAsset = enemyConfig1.avatarIsset, fightAsset = self._fightAsset,
    enemySpine = enemyConfig2.spine, heroSpine = enemyConfig1.spine},
    {enemyConfig = enemyConfig2, playerData = enemyConfig1},
    combatCallBack, self._isNoCritMode, true)
end

-- 虚拟敌人被击败处理
function ActivityMagicTowerMainPanel:_OnVirtualEnemyDefeated(enemyConfig)
    self._deadEnemies[enemyConfig.enemyID] = true
    testLog(string.format("虚拟敌人%s被击败，enemyID = %s", tostring(enemyConfig.name), enemyConfig.enemyID))

    if enemyConfig.defeatReward and enemyConfig.defeatReward ~= 0 then
        self:_ApplyAttributeChange(enemyConfig.defeatReward)
    end

    if enemyConfig.disappearNote and enemyConfig.disappearNote ~= "" then
        Module.CommonTips:ShowSimpleTip(enemyConfig.disappearNote)
    end

    self:_HandleVirtualEnemyDisappear()

    ActivityInputHandler.EnableInput()
end

function ActivityMagicTowerMainPanel:_HandleVirtualEnemyDisappear()
    ActivityInputHandler.DisableInput()

    local dialogueID
    if self._specialCombatType == 1 then
        dialogueID = COMPULSORY2_DISAPPEAR_DIALOGUE_ID
    elseif self._specialCombatType == 3 then
        dialogueID = COMPULSORY1_DISAPPEAR_DIALOGUE_ID
    elseif self._specialCombatType == 5 then
        dialogueID = COMPULSORY3_DISAPPEAR_DIALOGUE_ID
    end

    if dialogueID then
        self:OpenDialoguePanel(dialogueID)
    end
end

---@param combatType number 战斗类型，默认nil正常，1：雷斯 2:雷斯（带后置对话） 3：赛伊德 4：互殴
function ActivityMagicTowerMainPanel:SetSpecialCombatType(combatType)
    self._specialCombatType = combatType
end

-- 敌人被击败处理
function ActivityMagicTowerMainPanel:_OnEnemyDefeated(enemyConfig, targetX, targetY)
    self._deadEnemies[enemyConfig.enemyID] = true
    testLog(string.format("敌人%s被击败，enemyID = %s", tostring(enemyConfig.name), enemyConfig.enemyID))
    
    if enemyConfig.defeatReward and enemyConfig.defeatReward ~= 0 then
        self:_ApplyAttributeChange(enemyConfig.defeatReward)
    end

    ActivityInputHandler.EnableInput()
    self:_HandleItemDisappear(enemyConfig.enemyID, targetX, targetY, enemyConfig)
end

-- 玩家被击败处理
function ActivityMagicTowerMainPanel:_OnPlayerDefeated(enemyConfig)
    local enemyID = enemyConfig.enemyID
    self:_HandleGameExit(false, enemyID)
end

function ActivityMagicTowerMainPanel:_CheckIsPlayerDeath()
    if self._player:GetHealth() <= 0 then
        self:_HandleGameExit(false, 0)
    end
end

function ActivityMagicTowerMainPanel:_HandleNPCItem(itemID, targetX, targetY)
    local npcConfig = self:_FindNPCConfig(itemID)
    if not npcConfig then return end

    local finishCallBack = CreateCallBack(function()
        ActivityInputHandler.EnableInput()
        self:_HandleItemDisappear(itemID, targetX, targetY, npcConfig)
    end, self)
    
    if npcConfig and npcConfig.dialogue and npcConfig.dialogue ~= 0 then
        ActivityInputHandler.DisableInput()
        self:OpenDialoguePanel(npcConfig.dialogue, finishCallBack)
    else
        ActivityInputHandler.EnableInput()
        self:_HandleItemDisappear(itemID, targetX, targetY, npcConfig)
    end
end

function ActivityMagicTowerMainPanel:_HandleTreasureItem(itemID, targetX, targetY)
    local treasureConfig = self:_FindTreasureConfig(itemID)
    if not treasureConfig then return end

    local finishCallBack = CreateCallBack(function()
        testLog("保险箱UI关闭，清除子UI")
        self._curChildIns = nil

        ActivityInputHandler.EnableInput()
        self:_RemoveSpecialPointInGrid(targetX, targetY)
        self:_DestroyMapItemResource(targetX, targetY, itemID)

        if treasureConfig.disappearDialogue and treasureConfig.disappearDialogue ~= 0 then
            ActivityInputHandler.DisableInput()
            self:OpenDialoguePanel(treasureConfig.disappearDialogue)
        end
    end, self)

    if treasureConfig.dialogue and treasureConfig.dialogue ~= 0 then
        local onDialogueFinish = CreateCallBack(function(self, isCancelAction)
            if isCancelAction then
                ActivityInputHandler.EnableInput()
            else
                self:OpenSafePanelWithSafeId(itemID, nil, targetX, targetY, finishCallBack)
            end
        end, self)

        ActivityInputHandler.DisableInput()
        self:OpenDialoguePanel(treasureConfig.dialogue, onDialogueFinish)
    else
        self:OpenSafePanelWithSafeId(itemID, nil, targetX, targetY, finishCallBack)
    end 
end

-- 辅助函数：处理物品消失逻辑
function ActivityMagicTowerMainPanel:_HandleItemDisappear(itemID, targetX, targetY, config)
    if not self._itemInteractionCounts[targetX] or not self._itemInteractionCounts[targetX][targetY] then return end

    self._itemInteractionCounts[targetX][targetY] = self._itemInteractionCounts[targetX][targetY] - 1

    -- 显示消失后飘窗
    if config.disappearNote and config.disappearNote ~= "" then
        Module.CommonTips:ShowSimpleTip(config.disappearNote)
    end
    
    -- 从地图移除
    if self._itemInteractionCounts[targetX][targetY] <= 0 then
        self:_RemoveSpecialPointInGrid(targetX, targetY)
        self:_DestroyMapItemResource(targetX, targetY, itemID)
        self._itemInteractionCounts[targetX][targetY] = nil
        
        -- 处理消失后对话
        if config.disappearDialogue and config.disappearDialogue ~= 0 then
            ActivityInputHandler.DisableInput()
            self:OpenDialoguePanel(config.disappearDialogue)
        end
    end
end

-- [备用方法] 用来处理一些因生命值变更而产生的延时效果
function ActivityMagicTowerMainPanel:_HandleHealthChanged()
end

-- 提前通关
function ActivityMagicTowerMainPanel:SetPreGameOver(isGameOver)
    testLog(string.format("设置是否提前通关，isGameOver = %s", tostring(isGameOver)))
    self._isGameOver = isGameOver

    if self._curState == LevelState.SECOND_DESTROY_COMP or self._curState == LevelState.SECOND_SAVED_COMP then
        self:RecordNewLevelState(LevelState.AFTER_THE_SECOND)
    end
end

---@param isEvacuate boolean 是否成功撤离
---@param enemyID number 击杀者ID
function ActivityMagicTowerMainPanel:_HandleGameExit(isEvacuate, enemyID)
    ActivityInputHandler.DisableInput()
    self:_InitExitType(isEvacuate, enemyID)

    ConfigManager.PlayAudio(isEvacuate and 17 or 16)

    local levelInfo = {
        name = self._curLevelInfo.levelName or "",
        subName = self._curLevelInfo.levelSubName or "",
    }

    local evacuateCondition = 0 -- 默认撤离失败
    if isEvacuate then
        if self._exitType == EExitType.PASS then
            if self._curState > LevelState.FIRST_ENTERED_TOWER then
                evacuateCondition = 3 -- 二周目及之后通关
            else
                evacuateCondition = 2 -- 一周目通关
            end
        else
            evacuateCondition = 1 -- 普通撤离
        end
    end

    local finishCallBack = CreateCallBack(function()
        self:OnExit()
    end, self)

    local extraInfo = {
        exitType = self._exitType,
        curState = self._curState,
        newState = self._newState
    }

    self:SendMagicTowerEndReq()
    Facade.UIManager:AsyncShowUI(UIName2ID.ActivityMagicTowerEvacuatePanel, nil, nil, self._activityID, levelInfo, isEvacuate, enemyID, finishCallBack, self._player:GetMoney(), evacuateCondition, extraInfo)
end

function ActivityMagicTowerMainPanel:_InitExitType(isEvacuate, enemyID)
    if isEvacuate then
        if self._isGameOver then
            self._isGameOver = nil
            self._exitType = EExitType.PASS 
        else
            self._exitType = EExitType.ESCAPE
        end
    else
        if enemyID == 1 then
            self._exitType = EExitType.QUIT  
        else
            self._exitType = EExitType.FAILED
        end
    end
end

-----------------------------------------------------周目相关-----------------------------------------------------
function ActivityMagicTowerMainPanel:_CheckLevelState(curState)
    if not curState or curState < LevelState.FIRST_AND_NEW or curState > LevelState.AFTER_THE_SECOND then
        testErrorLog(string.format("MainPanel _CheckLevelState is invalid! curState = %s, set default to 1", tostring(curState)))
        return 1
    end

    testWarningLog(string.format("MainPanel _CheckLevelState curState = %s", tostring(curState)))
    return curState
end

function ActivityMagicTowerMainPanel:_PostProcessDataByHardState()
    if self._curState > LevelState.FIRST_ENTERED_TOWER then
        self:_EnhancedAllEnemy()
    end
end

function ActivityMagicTowerMainPanel:_PostProcessDataByNoCritState()
    if self._curState == LevelState.SECOND_SAVED_COMP then
        self._isNoCritMode = true
    else
        self._isNoCritMode = false
    end
end

function ActivityMagicTowerMainPanel:_PostProcessDataByMaxState()
    if self._curState == LevelState.AFTER_THE_SECOND then
        return MAX_LEVEL_SPAWN_LEVEL_ID, MAX_LEVEL_SPAWN_X, MAX_LEVEL_SPAWN_Y
    end

    return nil
end

-- 因为不同状态的特殊逻辑执行顺序不一致，所以先拆分处理 [未使用]
function ActivityMagicTowerMainPanel:_HandleLevelStateLogic()
end

function ActivityMagicTowerMainPanel:RecordNewLevelState(newState)
    if not newState or newState < LevelState.FIRST_AND_NEW or newState > LevelState.AFTER_THE_SECOND then
        testWarningLog(string.format("请求的新状态 %s 为非法值，不更新状态", tostring(newState)))
        return 
    end

    if self._newState >= newState then
        testWarningLog(string.format("请求的新状态 %s 不大于当前记录值 %s，不更新状态", tostring(newState), tostring(self._newState)))
        return
    end

    if self._newState == LevelState.AFTER_THE_SECOND then
        -- 自身调用检测
        if not ((self._curState == LevelState.SECOND_DESTROY_COMP or self._curState == LevelState.SECOND_SAVED_COMP) and self._isGameOver) then
            testWarningLog(string.format("请求的 二周目后(MAX) 状态不符合，当前状态 %s，是否通关 %s，不更新状态", tostring(self._curState), tostring(self._isGameOver)))
        end
    else
        -- 对话调用检测
        if self._curState > LevelState.FIRST_ENTERED_TOWER then
            testWarningLog(string.format("请求的 非二周目后(NOT MAX) 状态不符合，当前状态 %s，请求状态 %s，不更新状态", tostring(self._curState), tostring(newState)))
        end
    end

    testWarningLog(string.format("请求新状态成功：%s --> %s （当前所在状态 %s）", tostring(self._newState) ,tostring(newState), tostring(self._curState)))
    self._newState = newState

    if not VersionUtil.IsShipping() then
        Module.CommonTips:ShowSimpleTip(string.format("非Shipping包提示：状态变更成功！当前状态 %s，请求新状态 %s", tostring(self._curState), tostring(self._newState)))
    end
end

-----------------------------------------------------UI刷新-----------------------------------------------------
function ActivityMagicTowerMainPanel:RefreshUI()
    self:_RefreshLevelInfoPanel()

    -- 分帧策略会导致无法预测，需要逐个spine处理
    self._enemyPredictions = {}
    -- self:_RefreshAllPredictionUI()

    self:_RefreshPlayerUI()
    self:_OnStartCheckSpecialPoint(self._curLevel.x, self._curLevel.y)
end

function ActivityMagicTowerMainPanel:_RefreshPlayerUI()
    self:_RefreshTopBarInfo()
    self:_RefreshNumericalPanel()
    self:_RefreshKeyItem()
end

function ActivityMagicTowerMainPanel:_RefreshTopBarInfo()
    if not self._player then return end

    local topBarInfo = {
        sanity = Logic.roundDownToInteger(self._sanNum),
        exp = Logic.roundDownToInteger(self._player:GetExp()),
        lmb = Logic.roundDownToInteger(self._player:GetMoney())
    }

    if self._curLmbNum and self._curLmbNum ~= topBarInfo.lmb then
        ConfigManager.PlayAudio(6)
    end
    self._curLmbNum = topBarInfo.lmb

    self._wtTopBar1Txt:SetText(tostring(topBarInfo.sanity))
    self._wtTopBar2Txt:SetText(tostring(topBarInfo.exp))
    self._wtTopBar3Txt:SetText(tostring(topBarInfo.lmb))

    if self._curChildIns and self._curChildIns.RefreshTopBarInfo then
        self._curChildIns:RefreshTopBarInfo(topBarInfo)
    end
end

function ActivityMagicTowerMainPanel:_RefreshNumericalPanel()
    if not self._player then return end
    
    local status = self._player:GetStatusSnapshot()
    local lastChanges = self._player:GetLastChangeTypes()

    local heroStauts = {
        hp = status.health or 0,
        atk = status.attack or 0,
        def = status.defense or 0,
        crit = status.hitProb or 0
    }

    -- local changes = {
    --     hp = lastChanges.health,
    --     atk = lastChanges.attack,
    --     def = lastChanges.defense,
    --     crit = lastChanges.hitProb
    -- }

    local heroImgPath = ConfigManager.GetAssetImgPath(self._player:GetAvatarAsset())

    -- self._wtHeroNumericalPanel:UpdateAllValues(heroStauts, changes)
    self._wtHeroNumericalPanel:UpdateAllValues(heroStauts, nil, true)
    self._wtHeroNumericalPanel:SetHeadIconImg(heroImgPath)

    if self._curChildIns and self._curChildIns.RefreshNumericalPanel then
        self._curChildIns:RefreshNumericalPanel(heroStauts, heroImgPath)
    end
end

function ActivityMagicTowerMainPanel:_RefreshKeyItem()
    if not self._player then return end

    local keyNum = self._player:GetKeys()
    local keyIcon = ActivityConfig.EMagicTowerImgPath[2]

    self._wtKeyItem:SetNumTxtOnly(keyNum)
    self._wtKeyItem:SetItemIconOnly(keyIcon)

    if self._curChildIns and self._curChildIns.RefreshKeyItem then
        self._curChildIns:RefreshKeyItem(keyNum, keyIcon)
    end
end

function ActivityMagicTowerMainPanel:_RefreshLevelInfoPanel()
    if self._curLevelInfo then
        self._wtLevelNameTxt:SetText(self._curLevelInfo.levelName or "")
        self._wtLevelSubNameTxt:SetText(self._curLevelInfo.levelSubName or "")
    end
end

function ActivityMagicTowerMainPanel:PlayDefaultDialogue()
    local defaultDialogueID = DEFAULT_DIALOGUE_LIST[self._curState]

    if not defaultDialogueID then
        testErrorLog("MainPanel PlayDefaultDialogue defaultDialogueID not find")
        return
    end

    if not self._dialogueConfig[defaultDialogueID] then
        testWarningLog("MainPanel PlayDefaultDialogue dialogueID not find")
        return
    end

    ActivityInputHandler.DisableInput()
    self:OpenDialoguePanel(defaultDialogueID)
end

function ActivityMagicTowerMainPanel:OpenDialoguePanel(dialogueID, finishCallBack)
    if not dialogueID or not self._dialogueConfig[dialogueID] then
        return
    end

    -- 检查对话是否满足条件
    local canOpen = false
    local dialogueGroups = {}
    for _, group in pairs(self._dialogueGroupConfig or {}) do
        if group.belongingDialogue == dialogueID then
            table.insert(dialogueGroups, group)
        end
    end

    -- 只要有一个组满足条件就可以打开
    for _, group in ipairs(dialogueGroups) do
        if self:CheckCondition(group.condition) then
            testLog(string.format("对话ID %s 满足开启条件 %s", tostring(dialogueID), tostring(group.condition)))
            canOpen = true
            break
        end
    end

    if not canOpen then
        testWarningLog(string.format("对话ID %s 不满足其所有开启条件", tostring(dialogueID)))
        if finishCallBack then
            finishCallBack()
        else
            ActivityInputHandler.EnableInput()
        end
        return
    end

    local onDialogueFinish = CreateCallBack(function(ownUI, popUI, isCancelAction, killedItemIDs)
        testLog("对话结束，清除子UI")
        self._curChildIns = nil

        if self._isGameOver then
            self:_HandleGameExit(true)
        end

        if finishCallBack then
            finishCallBack(isCancelAction)
        else
            ActivityInputHandler.EnableInput()
        end
        
        if killedItemIDs and next(killedItemIDs) then
            self:_HandleKilledItem(killedItemIDs)
        end

        if self._isChangeHeroAttribute then
            self:_HandleChangeHeroAttribute()
            self._isChangeHeroAttribute = nil
        end

        if self._specialCombatType then
            self:_HandleSpecialCombat()
        end

        if self._isExecuteTeleport then
            self:_HandleSpecialTeleport()
            self._isExecuteTeleport = nil
        end
    end, self)

    local topbarInfo = {
        sanity = Logic.roundDownToInteger(self._sanNum),
        exp = Logic.roundDownToInteger(self._player:GetExp()),
        lmb = Logic.roundDownToInteger(self._player:GetMoney())
    }
    
    local function fLoadFinCallBack(uiIns)
        testLog("对话UI加载完成，更换子UI")
        self._curChildIns = uiIns
    end
    Facade.UIManager:AsyncShowUI(UIName2ID.ActivityMagicTowerDialoguePanel, fLoadFinCallBack, nil, self, self._activityID, dialogueID, topbarInfo, onDialogueFinish)
end

function ActivityMagicTowerMainPanel:_HandleSpecialCombat()
    if self._specialCombatType == 1 or self._specialCombatType == 2 then
        ActivityInputHandler.DisableInput()
        self:_VirtualCombat(SECOND_BOSS_ID)
    elseif self._specialCombatType == 3 then
        ActivityInputHandler.DisableInput()
        self:_VirtualCombat(FIRST_BOSS_ID)
    elseif self._specialCombatType == 4 then
        ActivityInputHandler.DisableInput()
        self:_VirtualMutualCombat(SECOND_BOSS_ID, FIRST_BOSS_ID)
    elseif self._specialCombatType == 5 then
        self:_VirtualCombat(SECOND_BOSS_ID)
    end
end

function ActivityMagicTowerMainPanel:_HandleKilledItem(itemIDs)
    local function processSingleItem(itemID, itemType)
        for x, row in pairs(self._spawnedItems or {}) do
            for y, spawnedID in pairs(row or {}) do
                if spawnedID == itemID then
                    -- 因为消失逻辑和交互次数绑定，但是killed的item可能是交互次数为0的，又要播放消失后飘窗,所以要特殊处理
                    local itemConfig = self:_GetItemConfig(itemID)
                    if itemConfig then
                        if itemConfig.disappearNote and itemConfig.disappearNote ~= "" then
                            Module.CommonTips:ShowSimpleTip(itemConfig.disappearNote)
                        end
                    end 
                    self:_RemoveSpecialPointInGrid(x, y)
                    self:_DestroyMapItemResource(x, y, itemID)
                      if itemType == ITEM_CLASS_NAME.ENEMY then
                        self._deadEnemies[itemID] = true
                    elseif itemType == ITEM_CLASS_NAME.TREASURE then
                        self._deadEnemies[itemID] = true
                    end
                    return true
                end
            end
        end
        return false
    end

    for itemID, _ in pairs(itemIDs) do
        local itemType = ConfigManager.ParseItemTypeByID(itemID)

        if processSingleItem(itemID) then
            testWarningLog(string.format("MainPanel _HandleKilledItem itemID: %s, itemType: %s", itemID, tostring(itemType)))
        else
            testErrorLog(string.format("MainPanel _HandleKilledItem Counld not find, itemID: %s, itemType: %s", itemID, tostring(itemType)))
        end
    end
end

-- 辅助函数：获取物品配置信息
function ActivityMagicTowerMainPanel:_GetItemConfig(itemID)
    if not itemID then return nil, "ItemID不能为空" end
    
    local itemType = ConfigManager.ParseItemTypeByID(itemID)
    if not itemType then return nil, string.format("无效的ItemID: %s", itemID) end
    
    local configTable = ({
        [ITEM_CLASS_NAME.ENEMY   ] = self._enemyConfig   ,
        [ITEM_CLASS_NAME.NPC     ] = self._npcConfig     ,
        [ITEM_CLASS_NAME.PROP    ] = self._propConfig    ,
        [ITEM_CLASS_NAME.TREASURE] = self._treasureConfig,
    })[itemType]
    
    if not configTable then return nil, string.format("未知的Item类型: %s", itemType), itemType end
    
    local itemConfig = configTable[itemID]
    if not itemConfig then return nil, string.format("ItemID未配置: %s (类型: %s)", itemID, itemType), itemType end
    
    return itemConfig, nil, itemType
end

-- 辅助函数：获取物品资源信息
function ActivityMagicTowerMainPanel:_GetItemResourceInfo(itemConfig)
    if not itemConfig then return nil, nil, "配置不能为空" end
    
    if itemConfig.spine and itemConfig.spine ~= 0 then
        return itemConfig.spine, "spine"
    elseif itemConfig.itemAsset and itemConfig.itemAsset ~= 0 then
        return itemConfig.itemAsset, "icon"
    end
    
    return nil, nil, string.format("%s未配置视觉资源", tostring(itemConfig.name))
end

-- 辅助函数：验证坐标
function ActivityMagicTowerMainPanel:_ValidateCoordinates(x, y, funcName)
    if not x or not y then
        local msg = string.format("%s 传入坐标错误: (%s, %s)", funcName, tostring(x), tostring(y))
        testErrorLog(msg)
        return false
    end
    return true
end

-- 创建视觉表现
---@param x number 地图坐标x
---@param y number 地图坐标y
---@param itemID number itemID
---@param direction number 角色spine朝向
function ActivityMagicTowerMainPanel:_CreateMapItemVisual(x, y, itemID, direction)
    local isValid = self:_ValidateCoordinates(x, y, "_CreateMapItemVisual")
    if not isValid then return end
    
    local itemConfig, err, itemType = self:_GetItemConfig(itemID)
    if itemType == ITEM_CLASS_NAME.DOOR then
        testLog("解析到门类型，预创建门资源----->")
        self:_CreateDoorVisual(x, y, itemID)
        return
    end

    if not itemConfig then
        testErrorLog(err)
        return
    end
    
    local resourceID, resourceType, errRes = self:_GetItemResourceInfo(itemConfig)
    if not resourceID then
        testErrorLog(string.format("_CreateMapItemVisual %s (ItemID: %s, 类型: %s)", errRes, itemID, itemType))
        return
    end
    
    if resourceType == "icon" then
        self:_CreateItemAssetOnMap(x, y, resourceID, itemID, itemType)
    else
        self:_CreateItemPortraitOnMap(x, y, resourceID, itemID, itemType, direction)
    end
end

--- 销毁视觉表现
---@param x number 地图坐标x
---@param y number 地图坐标y
---@param itemID number itemID
function ActivityMagicTowerMainPanel:_DestroyMapItemResource(x, y, itemID)
    local isValid = self:_ValidateCoordinates(x, y, "_DestroyMapItemResource")
    if not isValid then return end
    
    local itemConfig, err, itemType = self:_GetItemConfig(itemID)
    if itemType == ITEM_CLASS_NAME.DOOR then
        testLog("解析到门类型，销毁门资源----->")
        self:_DestroyDoorVisual(x, y, itemID)
        return
    end

    if not itemConfig then
        testErrorLog(err)
        return
    end

    local resourceID, resourceType, errRes = self:_GetItemResourceInfo(itemConfig)
    if not resourceID then
        testErrorLog(string.format("_DestroyMapItemResource %s (ItemID: %s, 类型: %s)", errRes, itemID, itemType))
        return
    end
    
    if resourceType == "icon" then
        self:_DestroyItemAssetOnMap(x, y, resourceID)
    else
        self:_DestroyItemPortraitOnMap(x, y, resourceID)
    end
end

-- item图片资源创建接口
---@param lx number lua地图坐标x
---@param ly number lua地图坐标y
---@param assetID number item类型 (关联self._assetConfig)
---@param itemID number itemID
---@param itemType string 物品类型字符串
function ActivityMagicTowerMainPanel:_CreateItemAssetOnMap(lx, ly, assetID, itemID, itemType)
    testLog(string.format("%s 创建图片(%d, %d) assetID = %s, itemType = %s", tostring(itemID), lx, ly, tostring(assetID), tostring(itemType)))

    -- 旧接口方法
    -- local roleType = self:_ConvertItemTypeToRoleType(itemType)
    -- local actor = self:SpawnMapActorByCtrl(lx - 1, ly - 1, itemID, roleType)

    local actor = self:SpawnTexture2DActorByCtrl(lx - 1, ly - 1, assetID)

    self:_StoreMapActor(lx, ly, actor)
end

-- item图片资源销毁接口
---@param lx number lua地图坐标x
---@param ly number lua地图坐标y
---@param assetID number item类型 (关联self._assetConfig)
function ActivityMagicTowerMainPanel:_DestroyItemAssetOnMap(lx, ly, assetID)
    testWarningLog(string.format("销毁图片(%d, %d) assetID = %s", lx, ly, tostring(assetID)))

    self:_DestroyMapActor(lx, ly)
end

-- 创建门资源（非item类）
function ActivityMagicTowerMainPanel:_CreateDoorVisual(x, y, specialPointID)
    local config = self:_FindSpecialPointConfig(specialPointID)
    if not config then
        testErrorLog(string.format("_CreateDoorVisual 找不到门特殊点表配置，specialPointID: %s", tostring(specialPointID)))
        return
    end

    local assetID = config.doorAsset and tonumber(config.doorAsset)
    if not assetID then
        testErrorLog(string.format("_CreateDoorVisual 找不到门的图片资源ID，specialPointID: %s", specialPointID))
        return
    end

    local assetConfig = self:_FindAssetConfig(assetID)
    if not assetConfig then
        testErrorLog(string.format("_CreateDoorVisual 找不到门资源表配置，assetID: %s", assetID))
        return
    end
    
    self:_CreateItemAssetOnMap(x, y, assetID, nil, ITEM_CLASS_NAME.DOOR)
end

-- 销毁门资源（非item类）
function ActivityMagicTowerMainPanel:_DestroyDoorVisual(x, y, specialPointID)
    local config = self:_FindSpecialPointConfig(specialPointID)
    if not config then
        testErrorLog(string.format("_DestroyDoorVisual 找不到门特殊点表配置，specialPointID: %s", specialPointID))
        return
    end

    local assetID = config.doorAsset and tonumber(config.doorAsset)
    if not assetID then
        testErrorLog(string.format("_DestroyDoorVisual 找不到门的图片资源ID，specialPointID: %s", specialPointID))
        return
    end

    self:_DestroyItemAssetOnMap(x, y, assetID)
end

-- item立绘创建接口
---@param lx number lua地图坐标x
---@param ly number lua地图坐标y
---@param spineID number spine动画ID (关联self._spineConfig)
---@param itemID number itemID
---@param itemType string 物品类型字符串
---@param direction number 角色spine朝向
function ActivityMagicTowerMainPanel:_CreateItemPortraitOnMap(lx, ly, spineID, itemID, itemType, direction)
    testLog(string.format("[ctivityMagicTowerMainPanel:_CreateItemPortraitOnMap] %s 创建Item立绘(%d, %d) spineID = %s, direction = %s", itemID, lx, ly, tostring(spineID), tostring(itemType), tostring(direction)))

    self:Add_CreateItemPortraitOnMapFrameTask({lx, ly, spineID, itemID, itemType, direction})
end

-- 主角立绘创建接口
---@param lx number lua地图坐标x
---@param ly number lua地图坐标y
---@param spineID number spine动画ID (关联self._spineConfig)
function ActivityMagicTowerMainPanel:_CreateHeroPortraitOnMap(lx, ly, spineID)
    testLog(string.format("创建主角立绘(%d, %d) spineID = %s", lx, ly, tostring(spineID)))

    local scaleFactor = 1.0
    self._heroActor = Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, "SpawnPlayerSpineActor", FVector2D(lx - 1, ly - 1), spineID, scaleFactor)

    if isvalid(self._heroActor) then
        if self._heroActor.PlayAnim then
            self._heroActor:PlayAnim("idle_front", true)
        end

        local cx = lx - 1
        local cy = ly - 1

        local worldPos = self:ConvertGridPosToWorldPos(cx, cy)
        local offset = self:GetSpineActorOffset()
        local targetScale  = self:GetSpineSourceInitScale(spineID)
        targetScale = targetScale * self:GetMapActorScaleFactor()
       
        worldPos= worldPos +  offset-- 底部对齐偏移

        self._heroActor:K2_SetActorLocation(worldPos ,false, HitResult, false)
        self._heroActor:SetActorScale3D(targetScale)
        self._heroActor:SetZOrder(cy)

        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, "SetPlayerLocOffset",offset) --由于主角移动写在cpp，worldPos实际是会被覆盖的，所以偏移也应用在cpp，

        loginfo(string.format("[ActivityMagicTowerMainPanel:_CreateHeroPortraitOnMap] _CreateHeroPortraitOnMap: worldPos = (%s, %s, %s), targetScale = (%s, %s, %s)", 
            tostring(worldPos.X), tostring(worldPos.Y), tostring(worldPos.Z),
            tostring(targetScale.X), tostring(targetScale.Y), tostring(targetScale.Z)))
    end

    -- local actor = self:SpawnSpineActorByCtrl(lx - 1, ly - 1, spineID)
    -- local spineConfig = self:_FindSpineConfig(spineID)
    -- if spineConfig then
    --     if actor and actor.PlayAnim then
    --         actor:PlayAnim("idle_front", true)
    --     end    
    -- end

    -- self:_StoreMapActor(lx, ly, actor)
end

-- item立绘销毁接口
---@param lx number lua地图坐标x
---@param ly number lua地图坐标y
---@param spineID number spine动画ID (关联self._spineConfig)
function ActivityMagicTowerMainPanel:_DestroyItemPortraitOnMap(lx, ly, spineID)
    testWarningLog(string.format("销毁Item立绘(%d, %d) spineID = %s", lx, ly, tostring(spineID)))

    self:_DestroyMapActor(lx, ly)
end

--- 将itemType转换为roleType
---@param itemType string 物品类型字符串
---@return number
function ActivityMagicTowerMainPanel:_ConvertItemTypeToRoleType(itemType)
    return ({
        [ITEM_CLASS_NAME.PROP    ] = 1,
        [ITEM_CLASS_NAME.ENEMY   ] = 2,
        [ITEM_CLASS_NAME.NPC     ] = 3,
        [ITEM_CLASS_NAME.TREASURE] = 4,
    })[itemType] or 0
end

local roleType2Scale = {
    [1] = 2.5, -- prop
    [2] = 1.0, -- enemy
    [3] = 1.0, -- npc
    [4] = 2.5, -- treasure
}

-- C++层创建图片和spine的接口
---@param cx number c++地图坐标x
---@param cy number c++地图坐标y
---@param itemID number 角色ID
---@param roleType number 角色类型 [0: none, 1: item 2: enemy, 3: npc]
---@param scaleFactor number 缩放因子
---@return AActor
function ActivityMagicTowerMainPanel:SpawnMapActorByCtrl(cx, cy, itemID, roleType, scaleFactor)
    local pos = FVector2D(cx, cy)
    if not scaleFactor then
        scaleFactor = roleType2Scale[roleType] or 1.0
    end

    return Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, "SpawnMapActor", pos, itemID, roleType, scaleFactor)
end

function ActivityMagicTowerMainPanel:SpawnTexture2DActorByCtrl(cx, cy, assetId)
    loginfo(string.format("[ActivityMagicTowerMainPanel:SpawnTexture2DActorByCtrl]: (%s, %s) assetId = %s, scaleFactor = %s", tostring(cx), tostring(cy), tostring(assetId), tostring(scaleFactor)))
    local scaleFactor = 1
    local pos = FVector2D(cx, cy)
    local ret =  Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, "SpawnTexture2DActorBySourceId", pos, assetId, scaleFactor)

    if isvalid(ret) then
        local worldPos = self:ConvertGridPosToWorldPos(cx, cy)
        local mapActorScale = self:GetMapActorScaleFactor()
        local targetScale =FVector(1,1,1)

         -- 获取贴图的原始大小


        local isDoor = false
        if (assetId >= 4010127 and assetId <= 4010193) or assetId == 4019999 then
            isDoor = true
        end
        if isDoor then
            targetScale = targetScale * 3
        else
            local boxSize2D =   ret.GetBoxSize and ret:GetBoxSize() or FVector2D(1,1)

            local sumSqtr = boxSize2D.X * boxSize2D.X + boxSize2D.Y * boxSize2D.Y
            local normalizeSacle =  1.0 / math.sqrt(sumSqtr);
            boxSize2D.X = boxSize2D.X * normalizeSacle
            boxSize2D.Y = boxSize2D.Y * normalizeSacle
            
            targetScale.X = boxSize2D.X 
            targetScale.Y = boxSize2D.Y
        end

        targetScale = targetScale * mapActorScale * 2.5


        local offset = self:GetTexture2dOffset(ActivityConfig.MogenTower.AlignmentType.Center,ret.GetBoxSize and ret:GetBoxSize() or FVector2D(1,1))
        worldPos= worldPos +  offset

        ret:SetActorScale3D(targetScale)
        ret:K2_SetActorLocation(worldPos,false, HitResult, false)

        loginfo(string.format("[ActivityMagicTowerMainPanel:SpawnTexture2DActorByCtrl] SpawnTexture2DActorByCtrl: worldPos = (%s, %s, %s), targetScale = (%s, %s, %s)", 
            tostring(worldPos.X), tostring(worldPos.Y), tostring(worldPos.Z),
            tostring(targetScale.X), tostring(targetScale.Y), tostring(targetScale.Z)))
    end

    return ret
end

function ActivityMagicTowerMainPanel:SpawnSpineActorByCtrl(cx, cy, spineId, scaleFactor)
    scaleFactor = setdefault(scaleFactor, 1.0)
    loginfo(string.format("[ActivityMagicTowerMainPanel:SpawnSpineActorByCtrl]: (%s, %s) spineId = %s, scaleFactor = %s", tostring(cx), tostring(cy), tostring(spineId), tostring(scaleFactor)))
    local pos = FVector2D(cx, cy)
    local ret = Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, "SpawnSpineActorBySourceId", pos, spineId, scaleFactor)

    if isvalid(ret) then
        local worldPos = self:ConvertGridPosToWorldPos(cx, cy)
        local offset = self:GetSpineActorOffset()
        local targetScale  = self:GetSpineSourceInitScale(spineId)
        local scaleFactor = self:GetMapActorScaleFactor() or 1.0
        targetScale = targetScale * scaleFactor
       
        worldPos= worldPos +  offset-- 底部对齐偏移

        ret:K2_SetActorLocation(worldPos ,false, HitResult, false)
        ret:SetZOrder(cy)
        ret:SetSpineScale(FVector2D(targetScale.X, targetScale.Y)) -- 设置spine缩放
        local uiWidget = ret:GetTop3DUI()
         -- 设置缩放
        if isvalid(uiWidget) then
            uiWidget:SetFontSize(48 * targetScale.X)
        end

        loginfo(string.format("[ActivityMagicTowerMainPanel:SpawnSpineActorByCtrl] SpawnSpineActorByCtrl: worldPos = (%s, %s, %s), targetScale = (%s, %s, %s)", 
            tostring(worldPos.X), tostring(worldPos.Y), tostring(worldPos.Z),
            tostring(targetScale.X), tostring(targetScale.Y), tostring(targetScale.Z)))
    end

    return ret
end


---@param cx number 网格坐标x
---@param cy number 网格坐标y
---@return FVector
--- 将网格坐标转换为世界坐标
function ActivityMagicTowerMainPanel:ConvertGridPosToWorldPos(cx, cy)
    local stepLength = ActivityConfig.MogenTower.StepLength
    local ctrl = Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.MagicTower)
    local Ret = FVector(0.0,0.0,0.0)
    if not isvalid(ctrl) then
        logerror("[ActivityMagicTowerMainPanel:SpawnTexture2DActorByCtrl] ConvertGridPosToWorldPos: ctrl is invalid")
        return Ret
    end

    local mapActor = ctrl.MapActor

    if not isvalid(mapActor) then
        logerror("[ActivityMagicTowerMainPanel:SpawnTexture2DActorByCtrl] ConvertGridPosToWorldPos: mapActor is invalid")
        return Ret
    end

    local origin = mapActor:K2_GetActorLocation()

    local PosX = origin.X + cx * stepLength;
    local PosY = origin.Y + cy * stepLength;

    Ret.X = PosX
    Ret.Y = PosY
    Ret.Z = origin.Z + 3

    loginfo(string.format("[ActivityMagicTowerMainPanel:ConvertGridPosToWorldPos] ConvertGridPosToWorldPos: (%s, %s) -> (%s, %s, %s)", tostring(cx), tostring(cy), tostring(Ret.X), tostring(Ret.Y), tostring(Ret.Z)))
    return Ret
end

--- 根据2D盒子大小获取缩放比例
---@param inBoxSize2D FVector2D 代表2D盒子大小
---@return FVector 缩放比例
--- 返回值：X为宽度缩放，Y为高度缩放，Z为均值
function ActivityMagicTowerMainPanel:GetScaleByBoxSize2D(inBoxSize2D)
    local ret = FVector(1.0,1.0,1.0)
    if not inBoxSize2D then
        logerror("[ActivityMagicTowerMainPanel:GetScaleByBoxSize2D] inBoxSize2D is nil")
        return ret
    end

    local stepLength = ActivityConfig.MogenTower.StepLength
    local max = math.max(inBoxSize2D.X,inBoxSize2D.Y)
    local base = stepLength / max

    ret.X = base *( inBoxSize2D.X / max)  --宽
    ret.Y = base *( inBoxSize2D.Y / max)  --高
    ret.Z = (ret.X+ret.Y) / 2 -- 平面轴

    loginfo(string.format("[ActivityMagicTowerMainPanel:GetScaleByBoxSize2D] GetScaleByBoxSize2D: (%s, %s) -> (%s, %s, %s)", tostring(inBoxSize2D.X), tostring(inBoxSize2D.Y), tostring(ret.X), tostring(ret.Y), tostring(ret.Z)))
    return ret
end

function ActivityMagicTowerMainPanel:GetSpineActorOffset()
   local ret = FVector(0,0,0)
   local stepLength = ActivityConfig.MogenTower.StepLength
    ret.Y = stepLength / 2 - 37
    return ret
end

function ActivityMagicTowerMainPanel:GetMapActorScaleFactor()
    local ret = 1
     local ctrl = Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.MagicTower)
     if not isvalid(ctrl) then
         logerror("[ActivityMagicTowerMainPanel:GetSpineActorScale] ctrl is invalid")
         return ret
     end
 
     local mapActor = ctrl.MapActor
     if not isvalid(mapActor) then
        logerror("[ActivityMagicTowerMainPanel:GetSpineActorScale] mapActor is invalid")
        return ret
     end
     local mapSacle =  mapActor:GetActorScale3D()
     
     ret = mapSacle.X
     return ret
 end

 ---@param inAlignmentType ActivityConfig.MogenTower.AlignmentType 对齐类型
 ---@param inBoxSize2D FVector2D 代表2D盒子大小
 function ActivityMagicTowerMainPanel:GetTexture2dOffset(inAlignmentType , inBoxSize2D)
    inAlignmentType = setdefault(inAlignmentType, ActivityConfig.MogenTower.AlignmentType.Center)
    local ret = FVector(0,0,0)
    if inAlignmentType == ActivityConfig.MogenTower.AlignmentType.Center then
        return ret
    elseif inAlignmentType == ActivityConfig.MogenTower.AlignmentType.CenterBottom then
        local half = inBoxSize2D.Y / 2
        local stepLengthhalf = ActivityConfig.MogenTower.StepLength / 2
        ret.Y = stepLengthhalf - half
    end

    return ret
 end


 function ActivityMagicTowerMainPanel:GetSpineSourceInitScale(inSpineSourceID)
    local ret = FVector(1,1,1)
    if  Module.Activity.Config.MogenTower.SpineInitScaleTable[inSpineSourceID]  then
        return Module.Activity.Config.MogenTower.SpineInitScaleTable[inSpineSourceID]
    end
    return ret
 end
 


-- 存储AActor对象
---@param lx number lua地图坐标x
---@param ly number lua地图坐标y
---@param actor AActor 要存储的actor对象
function ActivityMagicTowerMainPanel:_StoreMapActor(lx, ly, actor)
    if not lx or not ly then return end
    if not isvalid(actor) then return end

    self._mapActors[lx] = self._mapActors[lx] or {}
    self._mapActors[lx][ly] = actor
end

-- 获取AActor对象
---@param lx number lua地图坐标x
---@param ly number lua地图坐标y
---@return AActor|nil
function ActivityMagicTowerMainPanel:_GetMapActor(lx, ly)
    if self._mapActors[lx] then
        return self._mapActors[lx][ly]
    end

    return nil
end

-- 销毁指定位置的AActor对象
---@param lx number lua地图坐标x
---@param ly number lua地图坐标y
function ActivityMagicTowerMainPanel:_DestroyMapActor(lx, ly)
    if not lx or not ly then return end

    local actor = self:_GetMapActor(lx, ly)
    if isvalid(actor) then
        actor:K2_DestroyActor()
        self._mapActors[lx][ly] = nil
    end
end

-- 销毁所有AActor对象
function ActivityMagicTowerMainPanel:_DestroyAllMapActors()
    testWarningLog("销毁所有AActor对象")

    for x, row in pairs(self._mapActors) do
        for y, actor in pairs(row) do
            if isvalid(actor) then
                actor:K2_DestroyActor()
            end
        end
    end

    self._mapActors = {}
end

-- 通用遍历敌人方法
function ActivityMagicTowerMainPanel:_ForEachEnemy(callback)
    if not self._spawnedItems then return end
    
    for x, row in pairs(self._spawnedItems) do
        for y, itemID in pairs(row) do
            if ConfigManager.ParseItemTypeByID(itemID) == ITEM_CLASS_NAME.ENEMY then
                local enemyConfig = self:_FindEnemyConfig(itemID)
                if enemyConfig and type(callback) == "function" then
                    callback(x, y, itemID, enemyConfig)
                end
            end
        end
    end
end

function ActivityMagicTowerMainPanel:_OnSpineActorCreateFinished(targetX, targetY)
    local itemID = self._spawnedItems[targetX] and self._spawnedItems[targetX][targetY]
    if not itemID then
        ActivityLogic.SetUIVisibility(self._wtEnemyNumericalPanel, false)
        testErrorLog(string.format("_OnSpineActorCreateFinished 传入的坐标有误，targetX = %s, targetY = %s", targetX, targetY))
        return
    end

    if ConfigManager.ParseItemTypeByID(itemID) == ITEM_CLASS_NAME.ENEMY then
        local enemyConfig = self:_FindEnemyConfig(itemID)
        if enemyConfig then
            local prediction = ActivityMagicTowerCombatLogic._CalculateCombatPrediction(
                self._player:GetStatusSnapshot(), 
                enemyConfig
            )

            self._enemyPredictions[itemID] = prediction

            local playerChangeHealth_str = prediction.playerChangeHealth and math.abs(prediction.playerChangeHealth) or tostring(prediction.playerChangeHealth)
            testLog(string.format("[分帧加载] 存储预测结果：%s敌人%s，预计扣除玩家%s生命，战斗预测结果：%s", itemID, tostring(enemyConfig.name), playerChangeHealth_str, tostring(prediction.result)))

            self:_UpdateEnemyPredictionUI(targetX, targetY, prediction)
        end
    end
end

function ActivityMagicTowerMainPanel:_RefreshAllPredictionUI()
    self:_UpdateCombatPredictResult()

    -- 开启了预测战斗才会实时更新，节省部分性能
    -- if self._isPredictCombat then
    --     self:_UpdateCombatPredictResult()
    -- end
end

-- 更新预测战斗结果
function ActivityMagicTowerMainPanel:_UpdateCombatPredictResult()
    self._enemyPredictions = {}
    
    self:_ForEachEnemy(function(x, y, itemID, enemyConfig)
        local prediction = ActivityMagicTowerCombatLogic._CalculateCombatPrediction(
            self._player:GetStatusSnapshot(), 
            enemyConfig
        )
        
        self._enemyPredictions[itemID] = prediction
        
        local playerChangeHealth_str = prediction.playerChangeHealth and math.abs(prediction.playerChangeHealth) or tostring(prediction.playerChangeHealth)
        testLog(string.format("存储预测结果：%s敌人%s，预计扣除玩家%s生命，战斗预测结果：%s", itemID, tostring(enemyConfig.name), playerChangeHealth_str, tostring(prediction.result)))
            
        self:_UpdateEnemyPredictionUI(x, y, prediction)
    end)
end

-- 预测战斗结果接口
function ActivityMagicTowerMainPanel:OnCombatResultStateChanged(bIsChecked)
    self._isPredictCombat = bIsChecked
    
    local tipKey = bIsChecked and 4 or 5
    Module.CommonTips:ShowSimpleTip(ActivityConfig.Loc.MagicTowerText[tipKey])
    
    if not bIsChecked then
        self:_ClearAllPredictionUI()
        return
    end
    
    self:_UpdateCombatPredictResult()
end

-- 清除所有预测UI
function ActivityMagicTowerMainPanel:_ClearAllPredictionUI()
    self:_ForEachEnemy(function(x, y)
        self:_UpdateEnemyPredictionUI(x, y, nil)
    end)
end

-- 更新所有已计算的预测UI [未使用]
function ActivityMagicTowerMainPanel:_UpdateAllPredictionUI()
    self:_ForEachEnemy(function(x, y, itemID)
        self:_UpdateEnemyPredictionUI(x, y, self._enemyPredictions[itemID])
    end)
end

-- 更新单个敌人头顶UI的显示预测结果
function ActivityMagicTowerMainPanel:_UpdateEnemyPredictionUI(x, y, prediction)    
    local actor = self:_GetMapActor(x, y)
    if not isvalid(actor) then
        testErrorLog(string.format("_UpdateEnemyPredictionUI 传入坐标错误: (%s, %s)", x, y))
        return
    end
    
    local headUI = actor:GetTop3DUI()
    if not isvalid(headUI) then
        testErrorLog(string.format("_UpdateEnemyPredictionUI 获取头顶UI失败: (%s, %s)", x, y))
        return
    end
    
    local showTxt
    if self._isPredictCombat and prediction and prediction.playerChangeHealth then
        local playerHealth = self._player:GetHealth()
        local absHealth = math.abs(prediction.playerChangeHealth)
        
        -- [neomtzhang]
        if prediction.result == ActivityMagicTowerCombatLogic.COMBAT_RESULT.TIMEOUT then
            showTxt = UNCERTAIN_STR
        elseif prediction.result == ActivityMagicTowerCombatLogic.COMBAT_RESULT.WIN then
            showTxt = string.format("%s", Logic.roundDownToInteger(absHealth))
        else
            if absHealth > ActivityMagicTowerCombatLogic.MAX_PREDICT_HEALTH and absHealth > playerHealth * 3 then
                showTxt = UNCERTAIN_STR
            else
                showTxt = string.format("%s", Logic.roundDownToInteger(absHealth))
            end
        end
    end
    
    headUI:SetNumTxt(showTxt)
end

-- 快速战斗接口
function ActivityMagicTowerMainPanel:OnQuickCombatStateChanged(bIsChecked)
    self._isAutoCombat = bIsChecked

    local tipTxt = bIsChecked and ActivityConfig.Loc.MagicTowerText[2] or ActivityConfig.Loc.MagicTowerText[3]
    Module.CommonTips:ShowSimpleTip(tipTxt)
end

-- 封装确认弹出框界面
function ActivityMagicTowerMainPanel:ShowConfirmWindow(
	text,
	confirmHandle,
	cancelHandle,
	cancelText,
	confirmText,
	confirmSound,
    cancelSound,
	stateInGuide,
	checkBoxText,
	fFinishCallback,
	bIsRecharge,
	detailText)
	return Facade.UIManager:AsyncShowUI(
		UIName2ID.ActivityMagicTowerConfirmWindow,
		fFinishCallback,
		nil,
		text,
		nil,
		confirmHandle,
		cancelHandle,
		cancelText,
		confirmText,
		confirmSound,
        cancelSound,
		stateInGuide,
		checkBoxText,
		nil,
		nil,
		bIsRecharge,
		nil,
		detailText
	)
end

function ActivityMagicTowerMainPanel:OnCheckBtnClicked(bIsChecked)
    self._isChecked = bIsChecked
    self:SetTileMapGrayParam(bIsChecked)
    
    if bIsChecked then
        ActivityInputHandler.DisableInput()
        Module.CommonTips:ShowSimpleTip(ActivityConfig.Loc.MagicTowerText[18])
    else
        ActivityInputHandler.EnableInput()
        ActivityLogic.SetUIVisibility(self._wtEnemyNumericalPanel, false)
    end 
end

function ActivityMagicTowerMainPanel:SetTileMapGrayParam(bGray)
    local param = bGray and 1 or 0
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, "SetTileMapGrayParam", param)
end

function ActivityMagicTowerMainPanel:_ShowEnemyDetailInfos(targetX, targetY)
    local itemID = self._spawnedItems[targetX] and self._spawnedItems[targetX][targetY]
    if not itemID then
        ActivityLogic.SetUIVisibility(self._wtEnemyNumericalPanel, false)
        testLog(string.format("_ShowEnemyDetailInfos 选中的坐标点为空，targetX = %s, targetY = %s", targetX, targetY))
        return
    end

    local itemType = ConfigManager.ParseItemTypeByID(itemID)
    if itemType == ITEM_CLASS_NAME.DOOR then
        Module.CommonTips:ShowSimpleTip(tostring(ActivityConfig.Loc.MagicTowerText[20]))

        ActivityLogic.SetUIVisibility(self._wtEnemyNumericalPanel, false)
        testLog(string.format("_ShowEnemyDetailInfos 选中的坐标点为门，targetX = %s, targetY = %s", targetX, targetY))
        return
    end

    if itemType ~= ITEM_CLASS_NAME.ENEMY then
        local itemConfig = self:_GetItemConfig(itemID)
        local itemName = itemConfig and itemConfig.name or UNCERTAIN_STR
        local tipTxt = string.format(ActivityConfig.Loc.MagicTowerText[19], itemName)
        Module.CommonTips:ShowSimpleTip(tostring(tipTxt))

        ActivityLogic.SetUIVisibility(self._wtEnemyNumericalPanel, false)
        testLog(string.format("_ShowEnemyDetailInfos 选中的坐标点不为敌人，targetX = %s, targetY = %s, itemID = %s", targetX, targetY, tostring(itemID)))
        return
    end

    local enemyConfig = self:_FindEnemyConfig(itemID)
    if not enemyConfig then
        ActivityLogic.SetUIVisibility(self._wtEnemyNumericalPanel, false)
        testErrorLog(string.format("_ShowEnemyDetailInfos 找不到敌人配置，targetX = %s, targetY = %s, itemID = %s", targetX, targetY, tostring(itemID)))
        return
    end

    local enemyStauts = {
        hp = enemyConfig.health or 0,
        atk = enemyConfig.attack or 0,
        def = enemyConfig.defense or 0,
        crit = enemyConfig.hitProb or 0
    }

    local enemyImgPath = ConfigManager.GetAssetImgPath(enemyConfig.avatarIsset)

    self._wtEnemyNumericalPanel:UpdateAllValues(enemyStauts)
    self._wtEnemyNumericalPanel:SetHeadIconImg(enemyImgPath)

    ActivityLogic.SetUIVisibility(self._wtEnemyNumericalPanel, true)
end

function ActivityMagicTowerMainPanel:OnMerchantBtnClicked()
    local merchantCallBack = CreateCallBack(function(ownUI, popUI, dialogueID)
        if not dialogueID or dialogueID == 0 then
            ActivityInputHandler.EnableInput()
            return
        end

        self:OpenDialoguePanel(dialogueID)
    end, self)

    -- 初始化商人解锁状态
    self:_InitMerchantNPCConfig()

    ActivityInputHandler.DisableInput()
    
    Facade.UIManager:AsyncShowUI(UIName2ID.ActivityMagicTowerMerchant,
    nil,
    nil,
    self._activityID,
    self._merchantNPCConfig,
    self._shopNPCUnlockConfig,
    merchantCallBack)
end

function ActivityMagicTowerMainPanel:OnBackBtnClicked()
    self:OnNavBack()
end

function ActivityMagicTowerMainPanel:CheckCondition(conditionID)
    if not conditionID or conditionID == 0 then return true end

    local context = {
        attributes = {
            [1] = self._player:GetHealth(),
            [2] = self._player:GetAttack(),
            [3] = self._player:GetDefense(),
            [4] = self._player:GetHitProb(),
            [5] = self._player:GetMoney(),
            [6] = self._player:GetExp(),
            [7] = self._player:GetKeys(),
        },
        completedDialogues = self._completedDialogues or {}, -- 已完成对话
        deadEnemies = self._deadEnemies or {} -- 已击败敌人（新增：或消失的保险箱）
    }
    
    return ActivityMagicTowerConditionEvaluator.EvaluateCondition(
        conditionID, 
        self._conditionConfig, 
        self._subConditionConfig, 
        context
    )
end

---打开保险箱UI
---@param treasureID     integer  保险箱类型id
---@param safeId         integer  保险箱识别id，用于区分同类型保险箱
---@param targetX        integer  保险箱坐标x
---@param targetY        integer  保险箱坐标y
---@param finishCallBack function 打开保险箱完成回调
function ActivityMagicTowerMainPanel:OpenSafePanelWithSafeId(treasureID, safeId, targetX, targetY, finishCallBack)
    if not safeId then
        if not targetX or not targetY then
            testErrorLog(string.format("保险箱(%s)计数增加失败：关卡(%s)，坐标(%s, %s)", treasureID, self._curLevelID, tostring(targetX), tostring(targetY)))
            return
        end

        if not self._treasureGlobalID then
            self._treasureGlobalID = {}
        end
        if not self._treasureGlobalID[self._curLevelID] then
            self._treasureGlobalID[self._curLevelID] = {}
        end
        if not self._treasureGlobalID[self._curLevelID][targetX] then
            self._treasureGlobalID[self._curLevelID][targetX] = {}
        end
    
        if not self._treasureGlobalID[self._curLevelID][targetX][targetY] then
            self._treasureGlobalID[self._curLevelID][targetX][targetY] = self._treasureGlobalNum
            self._treasureGlobalNum = self._treasureGlobalNum + 1
            testLog(string.format("保险箱(%s)计数增加：关卡(%s)，坐标(%s, %s) = %s", treasureID, self._curLevelID, targetX, targetY, self._treasureGlobalID[self._curLevelID][targetX][targetY]))
        else
            testLog(string.format("维持保险箱(%s)计数：关卡(%s)，坐标(%s, %s) = %s", treasureID, self._curLevelID, targetX, targetY, self._treasureGlobalID[self._curLevelID][targetX][targetY]))
        end
    else
        testWarningLog(string.format("指定保险箱(%s)唯一ID：%s", treasureID, safeId))
    end

    safeId = safeId or self._treasureGlobalID[self._curLevelID][targetX][targetY]

    ActivityInputHandler.DisableInput()
    return self.mSafeManager:OpenSafePanelWithSafeId(treasureID, safeId, finishCallBack)
end

function ActivityMagicTowerMainPanel:OnSafePanelLoadFinish(uiIns)
    if uiIns then
        testLog("保险箱UI加载完成，更换子UI")
        self._curChildIns = uiIns
        self:_RefreshPlayerUI()
    end
end

---@param attributeType string 属性类型字符串("health"/"maxHealth"/"attack"/"defense"/"hitProb"/"money"/"exp"/"keys")
---@param value number 新值
---@param isDelta boolean 可选参数，true表示增加值，false(默认)表示直接设置值
function ActivityMagicTowerMainPanel:ModifyPlayerAttribute(attributeType, value, isDelta)
    local logTxt = ""

    if not self._player then
        logTxt = "修改玩家属性失败：玩家对象未初始化！"
        testErrorLog(logTxt)
        Module.CommonTips:ShowSimpleTip(logTxt)
        return false
    end

    if not attributeType or not value then
        logTxt = "修改玩家属性失败：无效的参数！"
        testErrorLog(logTxt)
        Module.CommonTips:ShowSimpleTip(logTxt)
    end

    local validAttributes = {
        health = true,
        maxHealth = true,
        attack = true,
        defense = true,
        hitProb = true,
        money = true,
        exp = true,
        keys = true
    }

    if not validAttributes[attributeType] then
        logTxt = string.format("修改玩家属性失败：无效的属性类型 %s", attributeType)
        testErrorLog(logTxt)
        Module.CommonTips:ShowSimpleTip(logTxt)
        return false
    end

    local currentValue = self._player["Get"..attributeType:sub(1,1):upper()..attributeType:sub(2)](self._player)
    
    local newValue = isDelta and (currentValue + value) or value

    if attributeType == "health" then
        local maxHealth = self._player:GetMaxHealth()
        newValue = math.min(newValue, maxHealth)
    end

    self._player["Modify"..attributeType:sub(1,1):upper()..attributeType:sub(2)](self._player, newValue, 4)

    self:_RefreshPlayerUI()
    self:_RefreshAllPredictionUI()

    logTxt = string.format("[无音效提示] 修改玩家属性成功：属性类型 %s, 原值 %s, 新值 %s", attributeType, currentValue, newValue)
    testLog(logTxt)
    Module.CommonTips:ShowSimpleTip(logTxt)
    return true
end

function ActivityMagicTowerMainPanel:ShowMoveEffect(inGridPos)
    if not isvalid(self._wtMoveEffect) then
        logerror("[ActivityMagicTowerMainPanel:ShowMoveEffect] self._wtMoveEffect is invalid")
        return
    end

    local worldPos = self:ConvertGridPosToWorldPos(inGridPos.X - 1, inGridPos.Y - 1)
 
    self._wtMoveEffect:Visible()
    self._wtMoveEffect:SetParentWidget(self)
    self._wtMoveEffect:SetWorldPos(worldPos)
    self._wtMoveEffect:PlayEffectAnimation()
end

function ActivityMagicTowerMainPanel:HideMoveEffect()
    if not isvalid(self._wtMoveEffect) then
        logerror("[ActivityMagicTowerMainPanel:HideMoveEffect] self._wtMoveEffect is invalid")
        return
    end

    self._wtMoveEffect:PlayOutAnimAndAffterCollapsed()
end

function ActivityMagicTowerMainPanel:Implement_CreateItemPortraitOnMap(taskInfo ,taskID )
    if not self._taskInfoMap[taskID] then
        loginfo(string.format("[ActivityMagicTowerMainPanel:Implement_CreateItemPortraitOnMap] taskID = %s invalid task", tostring(taskID)))
        return
    end
    local lx, ly, spineID, itemID, itemType, direction = table.unpack(taskInfo)
    loginfo(string.format("[ActivityMagicTowerMainPanel:Implement_CreateItemPortraitOnMap] taskID = %s", tostring(taskID)))
    testLog(string.format("%s 创建Item立绘(%d, %d) spineID = %s, direction = %s", itemID, lx, ly, tostring(spineID), tostring(itemType), tostring(direction)))

    -- 旧接口方法
    -- local roleType = self:_ConvertItemTypeToRoleType(itemType)
    -- local actor = self:SpawnMapActorByCtrl(lx - 1, ly - 1, itemID, roleType)

    local actor = self:SpawnSpineActorByCtrl(lx - 1, ly - 1, spineID)
    local spineConfig = self:_FindSpineConfig(spineID)
    if spineConfig then
        local isLoopRelax = spineConfig.isLoopRelax == 1
        if actor and actor.PlayAnim then
            actor:PlayAnim(isLoopRelax and "Relax" or "idle", true)
        end    
    end

    self:_StoreMapActor(lx, ly, actor)
    self._taskInfoMap[taskID] = nil
    self:Start_CreateItemPortraitOnMapFrameTask()

    self:_OnSpineActorCreateFinished(lx, ly)
end

function ActivityMagicTowerMainPanel:Add_CreateItemPortraitOnMapFrameTask(taskInfo)
    if not self._taskInfoMap then
        self._taskInfoMap = {}
    end
    FrameTaskMaxID = FrameTaskMaxID - 1
    if FrameTaskMaxID < 0 then
        logerror("ActivityMagicTowerMainPanel:Add_CreateItemPortraitOnMapFrameTask frameTaskMaxID overflow")
        return
    end

    self._taskInfoMap[FrameTaskMaxID] = taskInfo
    if not self._runingTask then
        self:Start_CreateItemPortraitOnMapFrameTask()
    end

    loginfo("[ActivityMagicTowerMainPanel:Add_CreateItemPortraitOnMapFrameTask] _runingTask = "..tostring(self._runingTask))
end

function ActivityMagicTowerMainPanel:Start_CreateItemPortraitOnMapFrameTask()
    self._runingTask = true;
    local exeTask = false
    loginfo("[ActivityMagicTowerMainPanel:Start_CreateItemPortraitOnMapFrameTask] _runingTask = "..tostring(self._runingTask))
    local exeCount = 0
    for taskID, taskInfo in pairs(self._taskInfoMap) do
        loginfo(string.format("[ActivityMagicTowerMainPanel:Start_CreateItemPortraitOnMapFrameTask] Start taskID = %s", tostring(taskID)))
        if taskInfo then
            Facade.LuaFramingManager:RegisterFrameTask(self.Implement_CreateItemPortraitOnMap, self, {taskInfo,taskID})
            exeTask = true
        end
        exeCount = exeCount + 1
        if exeCount >= MaxframeExeCount then
            return
        end
    end
    if not exeTask then
        self._runingTask = false;
    end

    loginfo("[ActivityMagicTowerMainPanel:Start_CreateItemPortraitOnMapFrameTask] _runingTask = "..tostring(self._runingTask))
end

function ActivityMagicTowerMainPanel:ClearFrameTask()
    Facade.LuaFramingManager:CancelAllFrameTasks(self)
    self._runingTask = false
end

function ActivityMagicTowerMainPanel:OnFoldStatusChanged()
    loginfo("ActivityMagicTowerMainPanel:OnFoldStatusChanged")
    Timer.DelayCall(
        0.5,
        function()
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, "UpdateCameraAdaptation")
        end
    )
end

return ActivityMagicTowerMainPanel