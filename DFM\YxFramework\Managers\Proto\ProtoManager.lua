----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaFManager)
----- LOG FUNCTION AUTO GENERATE END -----------



local ManagerBase = require("DFM.YxFramework.Managers.ManagerBase")
---@class ProtoManager : ManagerBase
local ProtoManager = class("ProtoManager", ManagerBase)
local UGameSDKManager = import "GameSDKManager"
local GameProtocolIns = UGameSDKManager.GetGameProtocolIns(GetGameInstance())
local GameConnectIns = UGameSDKManager.GetGameConnectIns(GetGameInstance())
local ProtoDefaultConfig = require "DFM.YxFramework.Managers.Proto.ProtoDefaultConfig"
local LocalizeTool = require "DFM.StandaloneLua.BusinessTool.LocalizeTool"
local ProtoUnLoadInGame = require "DFM.YxFramework.Managers.Proto.UnLoadInGame.ProtoUnLoadInGame"
local ProtoLazyLoader = require "DFM.Business.Proto.ProtoLazyLoader"
local ProtoLUT = require "DFM.Business.Proto.ProtoLUT"


local function log(...)
    loginfo("[ProtoManager]", ...)
end
require "DFM.YxFramework.Managers.Proto.ProtoGlobalConst" -- 枚举

---------------------------------------------------------------------------------------------------------------
--- Proto管理器，负责连接状态的处理/协议的收发
--- [ProtoManager]
---
--- 全自动处理req对应的错误码解析，手动处理ntf的错误码解析，表格为配置ErrorMsg
---------------------------------------------------------------------------------------------------------------

require "DFM.YxFramework.Managers.Timer.Timer"
local ProtocolProcessingToolClass = require "DFM.YxFramework.Managers.Proto.ProtocolProcessingToolClass"
local ProtocolConnectionToolClass = require "DFM.YxFramework.Managers.Proto.ProtocolConnectionToolClass"
function ProtoManager:Ctor()
    self.Events = {
        -- 连接成功
        evtOnConnectSuccess = LuaEvent:NewIns("ProtocolConnectionToolClass.evtOnConnectSuccess"),
        -- 连接失败
        evtOnConnectFail = LuaEvent:NewIns("ProtocolConnectionToolClass.evtOnConnectFail"),
        -- 断开成功
        evtOnDisconnect = LuaEvent:NewIns("ProtocolConnectionToolClass.evtOnDisconnect"),
         -- 心跳超时通知
        evtOnDisconnectHeartBeat = LuaEvent:NewIns("ProtocolConnectionToolClass.evtOnDisconnectHeartBeat"),
        -- 断线后仅连接成功
        evtOnTmpRelayConnected = LuaEvent:NewIns("ProtocolConnectionToolClass.evtOnTmpRelayConnected"),
        -- 断线重连成功(登录成功并在拉取必要数据)
        evtOnRelayConnected = LuaEvent:NewIns("ProtocolConnectionToolClass.evtOnRelayConnected"),
        -- 尝试重连
        evtOnStartReconnected = LuaEvent:NewIns("ProtocolConnectionToolClass.evtOnStartReconnected"),
        -- 超出自动重连次数 -- 需要弹窗
        evtOnExceedAutoReconnectTime = LuaEvent:NewIns("ProtocolConnectionToolClass.evtOnExceedAutoReconnectTime"),
        -- 取消手动重连-- 返回登录
        evtOnCancelReconnect = LuaEvent:NewIns("ProtocolConnectionToolClass.evtOnCancelReconnect"),
        -- 登录成功拉取所有协议结束
        evtOnLoginGetAllData = LuaEvent:NewIns("ProtocolConnectionToolClass.evtOnLoginGetAllData"),
        -- UDP Ping Event
        evtOnUdpPingRecv = LuaEvent:NewIns("ProtocolProcessingToolClass.evtOnUdpPingRecv"),
        -- 排队
        evtOnConnectStayInQueue = LuaEvent:NewIns("ProtocolConnectionToolClass.evtOnConnectStayInQueue"),
        -- 网络错误
        evtOnLuaNetworkFailure = LuaEvent:NewIns("ProtocolConnectionToolClass.evtOnLuaNetworkFailure"),
        evtOnDnsAsyncResloved = LuaEvent:NewIns("ProtocolConnectionToolClass.evtOnDnsAsyncResloved"),
        evtOnGroupTaskFinished = LuaEvent:NewIns("ProtocolConnectionToolClass.evtOnGroupTaskFinished"),
        evtOnNetBarResultNtf =  LuaEvent:NewIns("ProtocolConnectionToolClass.evtOnNetBarResultNtf"),
        evtOnClientConnectDSSuccessNtf = LuaEvent:NewIns("ProtocolConnectionToolClass.evtOnClientConnectDSSuccessNtf"),
        evtOnClientConnectDSFailedNtf = LuaEvent:NewIns("ProtocolConnectionToolClass.evtOnClientConnectDSFailedNtf"),
        evtReconnectResetChange = LuaEvent:NewIns("ProtocolConnectionToolClass.evtReconnectResetChange"),
        evtReconnectUpdateIpInfo = LuaEvent:NewIns("ProtocolConnectionToolClass.evtReconnectUpdateIpInfo"),
       
    }
    self._protocolProcessingToolClassIns = ProtocolProcessingToolClass:NewIns()
    self._protocolConnectionToolClassIns = ProtocolConnectionToolClass:NewIns()

    -- self.Events.evtOnConnectSuccess:AddListener(self.StartHeartbeatTimer,self) -- 连接成功时，开始心跳计时
    self.Events.evtOnRelayConnected:AddListener(self._OnReconnected,self) -- 重连后，开始心跳计时
    self.Events.evtOnDisconnect:AddListener(self.SetCanSendProto,self,false)
    self.reconnectState = false
    self.quickReconnectState = false
    self.bEnableReconnect = true

    self.useNewProtoSystem = true
    self.bIsAsync = true
    self.groupData = {}
    self:SetHandleNotify()
    if _WITH_EDITOR == 1 then
        self:EnableLuaPack(true)
    else
        self:EnableLuaPack(false)
    end

    if GameConnectIns ~= nil then
        GameConnectIns:BindLuaFunctions()
    end
end

function ProtoManager:SetCanSendProto(flg)
    self._protocolProcessingToolClassIns:SetCanSendProto(flg)
end

function ProtoManager:InitProtoMapping()
    ProtoLazyLoader.Initialize()
end

function ProtoManager:GetReconnectState()
    return self.reconnectState
end
function ProtoManager:SetReconnectState(state)
    self.reconnectState = state
end
function ProtoManager:GetQuickReconnectState()
    local curGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    return curGameFlow ~= EGameFlowStageType.Login
end
function ProtoManager:SetQuickReconnectState(state)
    self.quickReconnectState = state
end

function ProtoManager:SetEnableReconnect(flg)
    if self.useNewProtoSystem then
        if GameProtocolIns then
            GameProtocolIns:SetEnableReconnect(flg)
        end
    else
        self.bEnableReconnect = flg
    end
end
function ProtoManager:GetEnableReconnect()
    if self.useNewProtoSystem then
        if GameProtocolIns then
            return GameProtocolIns:GetEnableReconnect()
        end
    else
        return self.bEnableReconnect
    end
end
function ProtoManager:Reset()
end

function ProtoManager:Destroy()
    -- self.Events.evtOnConnectSuccess:RemoveListener(self.StartHeartbeatTimer,self) -- 连接成功时，开始心跳计时
    -- self.Events.evtOnRelayConnected:RemoveListener(self._OnReconnected,self)
    -- self.Events.evtOnDisconnect:RemoveListener(self.SetCanSendProto,self)

    self._protocolProcessingToolClassIns:Release()
    self._protocolConnectionToolClassIns:Release()
end

---@param gameFlowType EGameFlowStageType
function ProtoManager:OnGameFlowChangeLeave(gameFlowType)
    if not GF_CheckIsLoadingFlowType(gameFlowType) then
        self:Reset()
    end
end

---@param gameFlowType EGameFlowStageType
function ProtoManager:OnGameFlowChangeEnter(gameFlowType)
    if gameFlowType == EGameFlowStageType.Game then
        logwarning("ProtoManager:OnGameFlowChangeEnter proto进入局内设置重连状态")
        self:ResetIngameReconnectState()
        self.Events.evtReconnectResetChange:Invoke()
    end
end

function ProtoManager:OnLoadingFrontend2Game(gameFlowType)
    self:UnLoadProtoInGame()
end

function ProtoManager:OnPostManagerCleanUp()
	self:Reset()
end

function ProtoManager:ResetIngameReconnectState()
    if self.useNewProtoSystem then
        self:SetHeartbeatConfig(30,10,4,300,false,true)
    else
        --进入局内时设置局内的发送心跳间隔
        self._protocolConnectionToolClassIns:SetHeartbeatGapTime(10)
        -- 心跳检测间隔
        self._protocolProcessingToolClassIns:SetCheckHeartBeatTimeoutLimit(30)
        -- 设置重连间隔
        self._protocolConnectionToolClassIns:SetReconnectDelayTime(20)
    end
end

function ProtoManager:ResetSystemReconnectState()
    logwarning("ProtoManager:ResetSystemReconnectState")
    if self.useNewProtoSystem then
        self:SetHeartbeatConfig(15,5,2,300,true,false)
    else
        -- 恢复局外发送心跳间隔
        self._protocolConnectionToolClassIns:SetHeartbeatGapTime(5)
        -- 心跳检测间隔
        self._protocolProcessingToolClassIns:SetCheckHeartBeatTimeoutLimit(15)
        -- 恢复局外重连间隔
        self._protocolConnectionToolClassIns:SetReconnectDelayTime(2)
    end
end


-- 全局函数调用 Start ---
function UECall_ProtoProcess(buffer)
    Facade.ProtoManager:ProtoProcess(buffer)
end

function UECall_TableProtoProcess(header, body)
    Facade.ProtoManager:TableProtoProcess(header, body)
end

function UECall_UdpProtoProcess(buffer)
    Facade.ProtoManager:UdpProtoProcess(buffer)
end

function UECall_UdpTableProtoProcess(header, body)
    Facade.ProtoManager:UdpTableProtoProcess(header, body)
end
-- 全局函数调用 End ---

function ProtoManager:UdpProtoProcess(buffer)
    log("ProtoManager:UdpProtoProcess")
    self._protocolProcessingToolClassIns:UdpProtoProcess(buffer)
end

function ProtoManager:UdpTableProtoProcess(header, body)
    log("ProtoManager:UdpTableProtoProcess")
    self._protocolProcessingToolClassIns:UdpTableProtoProcess(header, body)
end

-- protocolProcessingToolClass 接口调用 Start ---
function ProtoManager:ProtoProcess(buffer)
    self._protocolProcessingToolClassIns:ProtoProcess(buffer)
end

function ProtoManager:TableProtoProcess(header, body, result)
    result = setdefault(result, 0)
    if self.useNewProtoSystem and GameProtocolIns then
        GameProtocolIns:ReceiveProto(header, body, result)
    else
        self._protocolProcessingToolClassIns:TableProtoProcess(header, body)
    end
    
end

function ProtoManager:AddNtfListener(ntfName, fCallback, caller)
    return self._protocolProcessingToolClassIns:AddNtfListener(ntfName, fCallback, caller)
end

---解绑协议下发ntf协议 by fCallback, caller
function ProtoManager:RemoveNtfListener(ntfName, fCallback, caller)
    self._protocolProcessingToolClassIns:RemoveNtfListener(ntfName, fCallback, caller)
end

---解绑协议下发ntf协议 by handleId
function ProtoManager:RemoveNtfListenerByHandleId(ntfName, handleId)
    self._protocolProcessingToolClassIns:RemoveNtfListenerByHandleId(ntfName, handleId)
end

-- 解绑caller所有协议下发ntf协议
---解绑协议下发ntf协议
function ProtoManager:RemoveAllNtfListenerByCaller(caller)
    self._protocolProcessingToolClassIns:RemoveAllNtfListenerByCaller(caller)
end

function ProtoManager:GetErrHandler()
    return self._protocolProcessingToolClassIns:GetErrHandler()
end

function ProtoManager:ManuelHandleErrCode(bufferTable)
    return self._protocolProcessingToolClassIns:ManuelHandleErrCode(bufferTable)
end

function ProtoManager:LuaSendProto(ServiceInfo, sendParam)
    if self.useNewProtoSystem then
        if GameProtocolIns then
            GameProtocolIns:SetLanguage(LocalizeTool.GetCurrentCulture())
            GameProtocolIns:SendProtoBase(ServiceInfo,sendParam,0)
        end
    else
        return self._protocolProcessingToolClassIns:LuaSendProto(ServiceInfo, sendParam)
    end
end

function ProtoManager:ClearBlockSeq()
    self._protocolProcessingToolClassIns:ClearBlockSeq()
end

function ProtoManager:ConsumeAllWaitTimer()
    self._protocolProcessingToolClassIns:ConsumeAllWaitTimer()
end

function ProtoManager:StartHeartbeatTimer()
    if self.useNewProtoSystem then
        if GameProtocolIns then
            GameProtocolIns:StartHeartbeatTimer()
        end
    else
        self._protocolProcessingToolClassIns:StartHeartbeatTimer()
    end
end

function ProtoManager:StopHeartbeatTimer()
    if self.useNewProtoSystem then
        if GameProtocolIns then
            GameProtocolIns:StopHeartbeatTimer()
       end
    else
        self._protocolProcessingToolClassIns:StopHeartbeatTimer()
    end
end

function ProtoManager:BindLuaTestChar(str)
    log("ProtoManager:BindLuaTestChar",str)
    self._protocolConnectionToolClassIns:BindLuaTestChar(str)
end


function ProtoManager:InitHeartBeatThread()
    log("ProtoManager:InitHeartBeatThread")
    if self.useNewProtoSystem then
        if GameProtocolIns then
            GameProtocolIns:InitHeartbeatThread()
        end
    else
        self._protocolConnectionToolClassIns:InitHeartBeatThread()
        self._protocolConnectionToolClassIns:SetRunningHeartbeat(true)
    end
end

function ProtoManager:DeinitHeartBeatThread() -- TODO:暂无调用，看后面什么时机回到登录界面会走这个
    log("ProtoManager:DeinitHeartBeatThread")
    self._protocolConnectionToolClassIns:DeinitConnector()
end

function ProtoManager:BindLoginConnectDelegate()
    log("ProtoManager:BindLoginConnectDelegate")
    self._protocolConnectionToolClassIns:BindLoginConnectDelegate()
end


-- 各个业务监听重连事件
function ProtoManager:InvokeRelayConnected()
    log("ProtoManager:InvokeRelayConnected")
    self._protocolConnectionToolClassIns:InvokeRelayConnected()
end

function ProtoManager:SetProtoConnectionState(state)
    self.state = state
end
function ProtoManager:GetProtoConnectionState()
    return self.state
end

-- protocolProcessingToolClass 接口调用 End ---

-- protocolConnectionToolClas 接口调用 Start ---
-- function ProtoManager:GetIsStopConnection()
--     return self._protocolConnectionToolClassIns:GetIsStopConnection()
-- end


function ProtoManager:SendBuffer(buffer)
    return self._protocolConnectionToolClassIns:SendBuffer(buffer)
end

function ProtoManager:SendTable(HeadTable, TheDataTable)
    return self._protocolConnectionToolClassIns:SendTable(HeadTable, TheDataTable)
end

function ProtoManager:SetWithRawProto(Name)
    return self._protocolConnectionToolClassIns:SetWithRawProto(Name)
end

-- 连接状态是否是连着的
function ProtoManager:IsConnected()
    if self.useNewProtoSystem and GameConnectIns~= nil then
        local res = GameConnectIns:GetConnectInfo()
        if res then
            return res.IsConnected or false
        else
            return false
        end
    else
        return self._protocolConnectionToolClassIns:CheckIsConnected()
    end
end

function ProtoManager:TryDisConnectServer(flg)
    logerror("ProtocolConnectionToolClass:TryDisConnectServer flg:",flg)
    if self.useNewProtoSystem then
        if GameConnectIns then
            if flg then
                self:SetEnableReconnect(false)
            else
                self:SetEnableReconnect(true)
            end
            GameConnectIns:DisConnectServer()
        end
    else
        self._protocolConnectionToolClassIns:TryDisConnectServer(flg)
    end
end

function ProtoManager:TryConnectServer(connectInfo)
    logerror("ProtocolConnectionToolClass:TryConnectServer")
    if self.useNewProtoSystem then
        if GameConnectIns then
            GameConnectIns:ConnectServer(connectInfo)
        end
    else
        self._protocolConnectionToolClassIns:TryConnectServer(connectInfo)
    end
end

function ProtoManager:EnableLuaPack(useLuaPack)
    if GameConnectIns then
        GameConnectIns:EnableLuaPack(useLuaPack)
    end
    if GameProtocolIns then
        GameProtocolIns:EnableLuaPack(useLuaPack)
    end
    self._protocolProcessingToolClassIns:EnableLuaPack(useLuaPack)
end

function ProtoManager:TryStartReconnecting()
    self._protocolConnectionToolClassIns:TryStartReconnecting()
end

-----------------------------------
--- 服务于出局时Lua重启
-----------------------------------
function ProtoManager:TryStartReconnectUseLastConnectedInfo()
    --self._protocolConnectionToolClassIns:TryStartReconnectUseLastConnectedInfo()
end

-- 直接连接，不走断线流程
function ProtoManager:ConnectDirectly()
    self._protocolConnectionToolClassIns:SetIsLoadingReconnect(true)
    self._protocolConnectionToolClassIns:DoReconnectWithLastConnectedInfo()
end

function ProtoManager:GetIsDisconnected()
    return self._protocolConnectionToolClassIns:GetIsDisconnected()
end
-- protocolConnectionToolClas 接口调用 End ---

function ProtoManager:_OnReconnected()
    if self.useNewProtoSystem then
        if GameProtocolIns then
            GameProtocolIns:OnReconnectedSuccess()
        end
    else
        self._protocolProcessingToolClassIns:OnReconnectedSuccess()
    end   
end

function ProtoManager:GetOnConfigData(stage)
    if self.useNewProtoSystem then
        self:RequestProtoWhenLoginOrReconnect(stage)
    else
        self._protocolProcessingToolClassIns:GetOnConfigData(stage)
    end   
end

function ProtoManager:GetOnRelayConnectedData()
    loginfo("接口废弃,改用ProtoManager:GetOnConfigData(2)")
    self._protocolProcessingToolClassIns:GetOnRelayConnectedData()
end

function ProtoManager:GetOnLoginData()
    loginfo("接口废弃,改用ProtoManager:GetOnConfigData(1)")
    self._protocolProcessingToolClassIns:GetOnLoginData()
end





-- 网络协议2.0  by mackyang
-->>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<

---------------------UE Call Lua 连接成功 ---------------------
function UECall_ConnectSuccessNtf(ConnectorResultInfo)
    Facade.ProtoManager:ConnectSuccessNtf(ConnectorResultInfo)
end

function ProtoManager:ConnectSuccessNtf(ConnectorResultInfo)
    self.Events.evtOnConnectSuccess:Invoke(ConnectorResultInfo)
    local timerHandler = Timer:NewIns(30, 0)
    local function TssHeartbeatSend()
        log("ProtocolConnectionToolClass:TssHeartbeatSend")
        local HeartBeat = pb.CSTssHeartbeatNtf:New()
        HeartBeat:SendNtf()
    end

    if timerHandler then
        timerHandler:AddListener(TssHeartbeatSend, nil)
        timerHandler:Start()
    end
end
----------------------------------------------------


----------------------UE Call Lua 连接失败 ---------------------
function UECall_ConnectFailedNtf(ConnectorResultInfo)
    Facade.ProtoManager:ConnectFailedNtf(ConnectorResultInfo)
end

function ProtoManager:ConnectFailedNtf(ConnectorResultInfo)
    self.Events.evtOnConnectFail:Invoke(ConnectorResultInfo)
end
----------------------------------------------------


----------------------UE Call Lua 网络断开 ---------------------
function UECall_DisconnectNtf() 
    Facade.ProtoManager:DisconnectNtf()
end

function UECall_DisconnectByHeartBeatNtf()
    Facade.ProtoManager:DisconnectByHeartBeat()
end

function ProtoManager:DisconnectNtf()
    self.Events.evtOnDisconnect:Invoke()
end

function ProtoManager:DisconnectByHeartBeat()
    self.Events.evtOnDisconnectHeartBeat:Invoke()
end
----------------------------------------------------


----------------------UE Call Lua 重连成功 ---------------------
function UECall_RelayConnected(ConnectorResultInfo)
    Facade.ProtoManager:RelayConnected(ConnectorResultInfo)
end

function ProtoManager:RelayConnected(ConnectorResultInfo)
    self.Events.evtOnTmpRelayConnected:Invoke(ConnectorResultInfo)
end
----------------------------------------------------


----------------------UE Call Lua 开始重连 ---------------------
function UECall_StartReconnectingNtf(bManualReconnect)
    local function ConfirmFunc()
        if GameProtocolIns then
            GameProtocolIns.bIsReConnecting = true
        end
        Facade.ProtoManager:ComfirmReconnect()
    end

    local function CancelFunc()
        Facade.ProtoManager:CancelReconnect()
    end
    if bManualReconnect then
        LuaGlobalEvents.evtShowProtoWaitLoading:Invoke(false)
        LuaGlobalEvents.evtManagerShowConfirm_Network:Invoke(SafeCallBack(ConfirmFunc), SafeCallBack(CancelFunc))
    else
        Facade.ProtoManager:StartReconnectingNtf()
    end 
end

function ProtoManager:StartReconnectingNtf()
    LuaGlobalEvents.evtShowProtoWaitLoading:Invoke(true)
    self.Events.evtOnStartReconnected:Invoke()    
end

----------------------UE Call Lua 取消重连 --------------------
function UECall_CancelReconnect()
    Facade.ProtoManager:CancelReconnect()
end

function ProtoManager:CancelReconnect()
    self.Events.evtOnCancelReconnect:Invoke()
    self:ClearWaitingProto()
end

function ProtoManager:ComfirmReconnect()
    if GameProtocolIns then
        GameProtocolIns:StartReconnectResultTimer()
    end
    self:StartReconnectingNtf()
end
----------------------------------------------------


----------------------UE Call Lua 错误码处理 -------------------
function UECall_HandleErrorTips(sendParam, protoName, result)
    Facade.ProtoManager:HandleErrorTips(sendParam, protoName, result)
end

function ProtoManager:HandleErrorTips(sendParam, protoName, result)
    self._protocolProcessingToolClassIns:HandleErrorTips(sendParam, protoName, result)
end
----------------------------------------------------


----------------------UE Call Lua 时间同步 --------------------
function UECall_UpdateServerTime(res)
    Facade.ClockManager:UpdateServerTime(res.tick_count)
end
----------------------------------------------------

-------------------UE Call Lua 使用Lua打包（Editor下）-----------
function UECall_LuaEncodePkg(protoName, TheDataTable, seqId)
    local buffer = Facade.ProtoManager:LuaEncodePkg(protoName, TheDataTable, seqId)
    if GameProtocolIns then
        GameProtocolIns:LuaEncodePkg(buffer,protoName,seqId)
    end
end

function ProtoManager:LuaEncodePkg(protoName, dataTable, seqId)
    log("ProtoManager:LuaEncodePkg Start")
    if dataTable == nil or type(dataTable) ~= "table" then
        log("ProtoManager:LuaEncodePkg dataTable is nil")
        return
    end

    local ProtoMsgName = protoName
    local EncodeFuncName = ProtoLUT.ProtoName2EncodeName(protoName)
    if EncodeFuncName == nil then
        logerror("ProtoManager:LuaEncodePkg, EncodeFuncName is nil", protoName)
        return
    end

    local EncodeFunc = pb[EncodeFuncName]
    if EncodeFunc == nil then
        logerror("ProtoManager:LuaEncodePkg, EncodeFunc is nil", protoName)
        return
    end

    local HeadEncoder = PbEncoder()
 
    local buffer = nil
    if EncodeFunc == nil then
        logerror("ProtoManager:LuaEncodePkg EncodeFunc is nil", protoName)
        return
    else
        EncodeFunc(dataTable, HeadEncoder)
        buffer = HeadEncoder:encode()
    end
 
    if (not buffer) then
        buffer = ""
    end

    local HeadTable = { cmd = 0, name = "", service = "", language = ""}
    HeadTable.name = ProtoMsgName
    HeadTable.service = dataTable["__service"]
    HeadTable.client_sequence_id = seqId
    HeadTable.language = LocalizeTool.GetCurrentCulture() or "" -- 添加语言
 
    local Pack = { head = HeadTable, body = buffer }
    local BodyEncoder = PbEncoder()
    EncodeFunc = pb["pb_CSPkgEncode"]
    EncodeFunc(Pack, BodyEncoder)

    buffer = BodyEncoder:encode()
    return buffer
end


----------------------UE Call Lua 经分上报 --------------------
function UECall_DoSendLobbyNetworkStatus(maxPing, minPing, avgPing, sdPing)
    logerror("ProtoManager:UECall_DoSendLobbyNetworkStatus, maxPing, minPing, avgPing, sdPing", maxPing, minPing, avgPing, sdPing)
    LogAnalysisTool.DoSendLobbyNetworkStatus(maxPing, minPing, avgPing, sdPing)
end
----------------------------------------------------

------------------ 登录或重连后拉取协议 --------------
-- stage 1 for Login/ 2 for Reconnect
function ProtoManager:RequestProtoWhenLoginOrReconnect(stage)
    logerror("ProtoManager:RequestProtoWhenLoginOrReconnect stage = ",stage)
    local protos = {}
    self.bCollectServerProtoState = stage
    GameProtocolIns.bRequestProto = true;
    if stage == 1 then
        protos = ProtoDefaultConfig.Login
    elseif stage == 2 then
        local curGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
        if curGameFlow == EGameFlowStageType.Game then
            protos = ProtoDefaultConfig.ReconnectInGame
        else
            protos = ProtoDefaultConfig.Reconnect
        end
    else
        log("ProtoManager:RequestProtoWhenLoginOrReconnect unknown stage")
        return
    end

    self._requestProtoNum = 0
    for i, protoInfo in pairs(protos) do
        if protoInfo.protoName then
            self._requestProtoNum = self._requestProtoNum + #protoInfo.protoName
        end
    end

    if self._requestProtoNum == 0 then
        log("ProtoManager:RequestProtoWhenLoginOrReconnect no proto need request")
        return
    end

    self._requestProtoList = {}
    for i, proto in pairs(protos) do
        if proto.protoName == nil or proto.protoName == "" then
            return
        end

        for j, protoName in pairs(proto.protoName) do
            self._requestProtoList[protoName] = true
        end

        local callfunc = proto.callfunc
        local t = string.split(callfunc, ".")

        callfunc = _G
        for j, v in pairs(t) do
            callfunc = callfunc[v]
        end

        local caller = proto.caller
        t = string.split(caller, ".")

        caller = _G
        for j, v in pairs(t) do
            caller = caller[v]
        end

        local params = proto.params or {}
        trycall(callfunc, caller, unpack(params)) 
    end
end

function UECall_CheckRequestFinish(protoName)
    Facade.ProtoManager:CheckRequestFinish(protoName)
end

function ProtoManager:CheckRequestFinish(protoName)
    if self._requestProtoList == nil then
        logerror("ProtoManager:CheckRequestFinish _requestProtoList is nil")
        return
    end

    if self._requestProtoList[protoName] then
        self._requestProtoList[protoName] = false
        self._requestProtoNum = self._requestProtoNum - 1
    end

    if (self._requestProtoNum <= 0) then
        GameProtocolIns.bRequestProto = false
        log("ProtoManager:RequestProtoWhenLoginOrReconnect request proto finish, stage = "..self.bCollectServerProtoState)
        if self.bCollectServerProtoState == 1 then
            Facade.ProtoManager.Events.evtOnLoginGetAllData:Invoke()
        elseif self.bCollectServerProtoState == 2 then
            local curGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
            if curGameFlow ~= EGameFlowStageType.Game then
                -- 局内不发重连成功事件
                logerror("ProtoManager: not in game, send relayconnected event")
                Facade.ProtoManager.Events.evtOnRelayConnected:Invoke()
            else
                -- 在局内，更新ip
                Facade.ProtoManager.Events.evtReconnectUpdateIpInfo:Invoke()
                logerror("ProtoManager in game do not send relayconnected event")
            end
        else
            log("ProtoManager:RequestProtoWhenLoginOrReconnect unknown stage")
        end
        self.bCollectServerProtoState = nil
    end
end

function UECall_ResendProtoAllReceived()
    Facade.ProtoManager:ResendProtoAllReceived()
end

function ProtoManager:ResendProtoAllReceived()
    logerror("ProtoManager:ResendProtoAllReceived")
    LuaGlobalEvents.evtProtoLoadFinish:Invoke()
end
----------------------------------------------------


----------------------Lua Call C++ -----------------

function ProtoManager:SetHeartbeatConfig(timeoutLimit, gapTime, delayTime, pingUploadInterval, bHeartbeatStats,bInGame)
    if GameProtocolIns then
        GameProtocolIns:SetHeartbeatConfig(timeoutLimit,gapTime,delayTime, pingUploadInterval, bHeartbeatStats,bInGame)
        GameProtocolIns.bUseReconnectTimer = true
    end
end

function ProtoManager:SetProtocolConfig(normalInterval,minInterval,openProtected)
    if GameProtocolIns then
        GameProtocolIns:SetProtocolConfig(normalInterval,minInterval,openProtected)
    end
end

function ProtoManager:SetHandleNotify()
    local function HandleNotifyCallback(protoName, dataTable)
        self._protocolProcessingToolClassIns:HandleNotify(protoName, dataTable)
    end

    if GameProtocolIns then
        GameProtocolIns:SetHandleNotifyFunction(HandleNotifyCallback)
    end
end

function ProtoManager:EnableNewProto(bUseNewProto)
    self.useNewProtoSystem = bUseNewProto;
    self._protocolConnectionToolClassIns:EnableNewProto(bUseNewProto)
    if GameProtocolIns then
        if GameProtocolIns.SetUseNewProto then
            GameProtocolIns:SetUseNewProto(bUseNewProto)
        end

        if  GameProtocolIns.SetProtocolConfig then
            GameProtocolIns:SetProtocolConfig(1000,500,false)
        end
    end

    if GameConnectIns then
        GameConnectIns.bUseNewProtoSystem = bUseNewProto
    end
end

function ProtoManager:IsProtoReceived()
    if self.useNewProtoSystem then
        return GameProtocolIns:CheckProtoReceived()
    else
        return self._protocolProcessingToolClassIns:IsProtoReceived()
    end

    return true
end

function ProtoManager:ClearWaitingProto()
    if self.useNewProtoSystem then
        GameProtocolIns:ClearWaitingProto()
    else
        self._protocolProcessingToolClassIns:ClearWaitingProto()
    end
end

-- 异步Dns解析通知，param为业务参数，seed为业务种子
function UECall_DNSResloved(ip, port, param, seed)
    Facade.ProtoManager:DNSResloved(ip, port, param, seed)
end

function ProtoManager:DNSResloved(ip, port, param, seed)
    --发送事件，通知
    self.Events.evtOnDnsAsyncResloved:Invoke(ip, port, param, seed)
end

function ProtoManager:GetDNSResolvedAsync()
    return self.bIsAsync
end

function ProtoManager:SetDNSResolvedAsync(bAsync)
    self.bIsAsync = bAsync
end

-- 请求之间不能嵌套
function ProtoManager:GroupRequest(groupId, groupTask, reqS, callbackS, extParamS)
    if self.groupData[groupId] == nil then
        self.groupData[groupId] = {}
    end

    for i = 1, #reqS do
        local req = reqS[i]
        if req then
            local callback = callbackS[i]
            local extParam = extParamS[i]
            local protoName = req.__name
        
            if table.indexof(self.groupData[groupId], protoName) <= 0 then
                table.insert(self.groupData[groupId], protoName)
            end

            local function MemberCallback(res)
                if callback then
                    callback(res)
                end
                if table.indexof(self.groupData[groupId], protoName) > 0 then
                    table.removebyvalue(self.groupData[groupId], protoName)
                    if #self.groupData[groupId] <= 0 then
                        -- 组请求都已经收到回包
                        if groupTask then
                            groupTask()
                        end
                    end
                end
            end

            req:Request(MemberCallback, extParam)
        end
    end
end

function ProtoManager:GroupRequestRegister(serviceInfo, sendParam)
    if sendParam and serviceInfo and sendParam.bIsGroupTask then
        -- 注册
        local groupId = sendParam.groupId
        local groupTaskNum = sendParam.groupTaskNum
        if self.groupData[groupId] == nil then
            self.groupData[groupId] = groupTaskNum
        end
    end
end

function ProtoManager:GroupRequestUnRegister(serviceInfo, sendParam)
    self.groupData[sendParam.groupId] = self.groupData[sendParam.groupId] - 1
    if self.groupData[sendParam.groupId] <= 0 then
        -- 组请求已完成
        self.Events.evtOnGroupTaskFinished:Invoke(sendParam.groupId)
    end
end

function ProtoManager:UnLoadProtoInGame()
    ProtoUnLoadInGame.UnLoadInGame()
end

function ProtoManager:ReLoadProto()
    ProtoUnLoadInGame.ReLoad()
end

function UECall_OnQQNetBarNotify(bIsNetBarMachine ,tokenBuff, tokenLen, ip, macSize, macs)
    -- 网吧检测通知
    Facade.ProtoManager.Events.evtOnNetBarResultNtf:Invoke(bIsNetBarMachine ,tokenBuff, tokenLen, ip, macSize, macs)
end

function UECall_OnClientConnectDSSuccess(IpAddrStr, Domain, ReconnectType)
    -- ds连接成功通知
    Facade.ProtoManager.Events.evtOnClientConnectDSSuccessNtf:Invoke(IpAddrStr, Domain, ReconnectType)
end

function UECall_OnClientConnectDSFailed(IpAddrStr,Domain,ActorRepMask,TotalInPackets,TotalInPacketsLost,TotalOutPackets,TotalOutPacketsLost, LastRecvPacketDeltaSecond,LastAckPakcetDeltaSecond,ConntectedSecond)
    -- ds连接失败通知
    local ip, port = string.match(IpAddrStr, "^%[(.-)%]:(%d+)$")
    if not ip then
        ip, port = string.match(IpAddrStr, "^(.-):(%d+)$")
    end

    if ip and port then
        local info = {
            Ip = ip,
            Port = tonumber(port),
            Domain = Domain,
            ActorRepMask = ActorRepMask,
            TotalInPackets = TotalInPackets,
            TotalInPacketsLost = TotalInPacketsLost,
            TotalOutPackets = TotalOutPackets,
            TotalOutPacketsLost = TotalOutPacketsLost,
            LastRecvPacketDeltaSecond = LastRecvPacketDeltaSecond,
            LastAckPakcetDeltaSecond = LastAckPakcetDeltaSecond,
            ConntectedSecond = ConntectedSecond
        }
        Facade.ProtoManager.Events.evtOnClientConnectDSFailedNtf:Invoke(info)
    end
end

function ProtoManager:BroadcastReqPartChunkedFinish(seqId)
    if self.useNewProtoSystem then
        if GameProtocolIns and GameProtocolIns.BroadcastReqPartChunkedFinish then
            GameProtocolIns:BroadcastReqPartChunkedFinish(seqId)
        end
    end
end

return ProtoManager

