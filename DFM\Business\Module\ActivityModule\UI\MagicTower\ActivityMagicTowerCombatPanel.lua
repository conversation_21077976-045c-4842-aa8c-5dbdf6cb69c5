----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class ActivityMagicTowerCombatPanel : LuaUIBaseView
local ActivityMagicTowerCombatPanel = ui("ActivityMagicTowerCombatPanel")
local ActivityMagicTowerNumericalPanel = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerNumericalPanel"
local ActivityConfig = Module.Activity.Config
local ActivityMagicTowerCombatLogic = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerCombatLogic"
local ActivityInputHandler = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityInputHandler"
local ConfigManager = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerConfigManager"

local COMBAT_ROUND_TIME = 1.0 -- 战斗单人回合时间
local PANEL_CLOSE_WAIT_TIME = 2.0 -- 面板关闭等待时间

function ActivityMagicTowerCombatPanel:Ctor()
    -- 主角信息框
    self._wtHeroNumericalPanel = self:Wnd("WBP_MorgenGame_NumericalPanel_C_22", ActivityMagicTowerNumericalPanel)
    -- 敌人信息框
    self._wtEnemyNumericalPanel = self:Wnd("WBP_MorgenGame_NumericalPanel_C_24", ActivityMagicTowerNumericalPanel)
end

-----------------------------------------------------生命周期-----------------------------------------------------
function ActivityMagicTowerCombatPanel:OnInitExtraData(activityID, commbatAsset, commbatData, FinishCallBack)
    self._activityID = activityID or 0
    self._commonbatAsset = commbatAsset or {}
    self._commonbatData = commbatData or {}
    self.FinishCallBack = FinishCallBack or nil

    self:_InitData()
end

function ActivityMagicTowerCombatPanel:OnShowBegin()
    self:BindBackAction()

    self:_InitUIAsset()
    self:_RefreshUI()

    local fOnCombatLoadingFinished = CreateCallBack(function()
        self:_OnActivateTimer()
    end, self)
    Facade.UIManager:AsyncShowUI(UIName2ID.ActivityMagicTowerCombatLoading, nil, nil, fOnCombatLoadingFinished)
end

function ActivityMagicTowerCombatPanel:OnHideBegin()
    self:_OnStopTimer()
    self:RemoveAllActions()
    self:RemoveAllLuaEvent()
end

-- 后续可能要支持玩家返回
function ActivityMagicTowerCombatPanel:OnNavBack()
    -- Facade.UIManager:CloseUI(self)
    
    -- if self.FinishCallBack then
    --     self:FinishCallBack()
    -- else
    --     ActivityInputHandler.EnableInput()
    -- end
end

function ActivityMagicTowerCombatPanel:BindBackAction()
    if self._backActionHandle then
        self:RemoveInputActionBinding(self._backActionHandle)
        self._backActionHandle = nil
    end

    self._backActionHandle = self:AddInputActionBinding(
        "Back",
        EInputEvent.IE_Pressed,
        self.OnNavBack,
        self,
        EDisplayInputActionPriority.UI_Pop
    )
end

function ActivityMagicTowerCombatPanel:RemoveAllActions()
    if self._backActionHandle then
        self:RemoveInputActionBinding(self._backActionHandle)
        self._backActionHandle = nil
    end
end

-----------------------------------------------------数据区-----------------------------------------------------
function ActivityMagicTowerCombatPanel:_InitData()
    if not self._commonbatData or not next(self._commonbatData) then
        return
    end

    self._combatState = {
        currentRound = 1,
        playerHP = self._commonbatData.playerData.health or 0,
        enemyHP = self._commonbatData.enemyConfig.health or 0,
        playerStatus = {
            attack = self._commonbatData.playerData.attack or 0,
            defense = self._commonbatData.playerData.defense or 0,
            hitProb = self._commonbatData.playerData.hitProb or 0
        },
        enemyStatus = {
            attack = self._commonbatData.enemyConfig.attack or 0,
            defense = self._commonbatData.enemyConfig.defense or 0,
            hitProb = self._commonbatData.enemyConfig.hitProb or 0
        },
        battleLog = {},
        isPlayerTurn = true
    }
end

-----------------------------------------------------战斗逻辑-----------------------------------------------------
function ActivityMagicTowerCombatPanel:_OnActivateTimer(delayTime)
    if not self._commonbatData or not next(self._commonbatData) then
        return
    end

    delayTime = setdefault(delayTime, COMBAT_ROUND_TIME)
    Timer.FastAddObjTimer(self, delayTime, 0, self.Update)
end

function ActivityMagicTowerCombatPanel:_OnStopTimer()
    Timer.FastRemoveObjTimer(self)
end

function ActivityMagicTowerCombatPanel:_OnRestoreTimer(delayTime)
    self:_OnStopTimer()
    self:_OnActivateTimer(delayTime)
end

function ActivityMagicTowerCombatPanel:Update()
    self:_ProcessCombatRound()
end

function ActivityMagicTowerCombatPanel:_ProcessCombatRound()
    if self._combatState.playerHP <= 0 or self._combatState.enemyHP <= 0 then
        self:_EndCombat()
        return
    end

    if self._combatState.isPlayerTurn then
        self:_ProcessPlayerAttack()
    else
        self:_ProcessEnemyAttack()
    end

    self._combatState.isPlayerTurn = not self._combatState.isPlayerTurn
    if not self._combatState.isPlayerTurn then
        self._combatState.currentRound = self._combatState.currentRound + 1
    end
    
    self:_RefreshUI()
end

function ActivityMagicTowerCombatPanel:_ProcessPlayerAttack()
    local roundResult = ActivityMagicTowerCombatLogic.CalculateRoundDamage(
        "player",
        self._combatState.playerStatus,
        self._combatState.enemyStatus,
        self._combatState.playerHP,
        self._combatState.enemyHP
    )

    self._combatState.enemyHP = roundResult.targetHP
    roundResult.round = self._combatState.currentRound
    table.insert(self._combatState.battleLog, roundResult)

    self:_PlayAttackAnimation(true, roundResult.isCrit)
end

function ActivityMagicTowerCombatPanel:_ProcessEnemyAttack()
    local roundResult = ActivityMagicTowerCombatLogic.CalculateRoundDamage(
        "enemy",
        self._combatState.enemyStatus,
        self._combatState.playerStatus,
        self._combatState.enemyHP,
        self._combatState.playerHP
    )

    self._combatState.playerHP = roundResult.targetHP
    roundResult.round = self._combatState.currentRound
    table.insert(self._combatState.battleLog, roundResult)

    self:_PlayAttackAnimation(false, roundResult.isCrit)
end

function ActivityMagicTowerCombatPanel:_InitUIAsset()
    if not self._commonbatAsset or not next(self._commonbatAsset) then
        return
    end

    self._wtHeroNumericalPanel:SetHeadIconImg(self._commonbatAsset.heroAvatarAsset)
    self._wtEnemyNumericalPanel:SetHeadIconImg(self._commonbatAsset.enemyAvatarAsset)
end

function ActivityMagicTowerCombatPanel:_RefreshUI()
    if not self._commonbatData or not next(self._commonbatData) then
        return
    end

    self._wtHeroNumericalPanel:UpdateAllValues({
        hp = self._combatState.playerHP,
        atk = self._combatState.playerStatus.attack,
        def = self._combatState.playerStatus.defense,
        crit = self._combatState.playerStatus.hitProb
    }, nil, true)
    
    self._wtEnemyNumericalPanel:UpdateAllValues({
        hp = self._combatState.enemyHP,
        atk = self._combatState.enemyStatus.attack,
        def = self._combatState.enemyStatus.defense,
        crit = self._combatState.enemyStatus.hitProb
    }, nil, true)

    -- debug
    if IsInEditor() then
        if #self._combatState.battleLog > 0 then
            local lastAction = self._combatState.battleLog[#self._combatState.battleLog]
            local logText = string.format("%s 对 %s 造成 %d 点伤害%s",
            lastAction.attacker == "player" and "你" or "敌人",
            lastAction.attacker == "player" and "敌人" or "你",
            lastAction.damage,
            lastAction.isCrit and " [CRIT!]" or "")
            Module.CommonTips:ShowSimpleTip(logText)
        end
    end
end

-- 预留播放动画接口（如果有的话，delayTime需要修改）
---@param isPlayerAttack boolean 是否为玩家攻击
---@param isCrit boolean 是否暴击
function ActivityMagicTowerCombatPanel:_PlayAttackAnimation(isPlayerAttack, isCrit)
    -- local animName = isPlayerAttack and "PlayerAttack" or "EnemyAttack"
    -- if isCrit then
    --     animName = animName .. "_Crit"
    -- end
    
    -- Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(
    --     ESubStage.MagicTower, 
    --     "PlayCombatAnimation", 
    --     animName, 
    --     COMBAT_ROUND_TIME
    -- )
    
    -- self:_OnRestoreTimer(COMBAT_ROUND_TIME)
end

function ActivityMagicTowerCombatPanel:_EndCombat()
    self:_OnStopTimer()
    
    local result = self._combatState.enemyHP <= 0 and "win" or "lose"
    
    if IsInEditor() then        
        local resultText = result == "win" and "胜利!" or "失败!"
        Module.CommonTips:ShowSimpleTip("战斗结束 - " .. resultText)
    end
    
    Timer.DelayCall(PANEL_CLOSE_WAIT_TIME, function()
        if self.FinishCallBack then
            self:FinishCallBack(result, self._combatState.playerHP)
        end
        
        Facade.UIManager:CloseUI(self)
    end)
end

return ActivityMagicTowerCombatPanel