----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class LitePackageDownloadItem : LuaUIBaseView
local LitePackageDownloadItem = ui("LitePackageDownloadItem")
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"
local LitePackageConfig = Module.LitePackage.Config
local function log(...)
    loginfo("[LitePackageDownloadItem]", ...)
end

function LitePackageDownloadItem:Ctor()
    self._wtMainBtn = self:Wnd("Button_231", UIButton)
    self._wtCheckBox = self:Wnd("CheckBox_01", UICheckBox)
    self._wtCheckBox_Cleaning = self:Wnd("CheckBox_Cleaning", UICheckBox)
    self._wtCheckBox_Cleaning:Event("OnCheckStateChanged", self.SelectedCleanUp, self)
    self._wtMainText = self:Wnd("CultureText", UITextBlock)
    self._wtCurProgressImg = self:Wnd("wtCurRankScoreScheduleImg", UIProgressBar)
    self._wtWaitDownBtn = self:Wnd("DFButton_0", UIButton)
    self._wtDowningBtn = self:Wnd("DFButton_1", UIButton)
    self._wtPauseDownBtn = self:Wnd("DFButton", UIButton)
    self._wtDownloadedImg = self:Wnd("DFImage_118", UIImage)
    self._wtReddotRoot = self:Wnd("DFCanvasPanel_110", UIWidgetBase)
    self._wtMainBtn:Event("OnClicked", self.OnMainBtnClicked, self)
    self._wtCheckBox:SetCallback(self.OnItemClicked, self)

    self._wtWaitDownBtn:Event("OnClicked", self.OnWaitDownClicked, self)
    self._wtDowningBtn:Event("OnClicked", self.OnDowningClicked, self)
    self._wtPauseDownBtn:Event("OnClicked", self.OnPauseClicked, self)

    self.displayName = ""
    self._bNeedCheck = true --下载前是否需要弹确认框
    self._bInCleanUpState= false --是否处于资源清理界面

    self.LiteDownloadMBNum = 1024 * 1024
end


function LitePackageDownloadItem:OnShowBegin()
    -- 定时刷新功能
    if self.tickHandle then
        self.tickHandle:Release()
        self.tickHandle = nil
    end

    self.tickHandle = Timer:NewIns(0.5, 0)
    self.tickHandle:AddListener(function()
        self:RefreshState()
    end, self)
    self.tickHandle:Start()
    --self:RefreshState()
end

function LitePackageDownloadItem:OnHide()
    self:RemoveAllLuaEvent()
    self:ClearTimer()
end
function LitePackageDownloadItem:OnClose()
    loginfo(111)
end

function LitePackageDownloadItem:ClearTimer()
    if self.tickHandle then
        self.tickHandle:Release()
        self.tickHandle = nil
    end
end

function LitePackageDownloadItem:DownLoadWithCheck(checkStr,confirmHandle) --弹确认框的下载
    if self.btnStateType == Module.LitePackage.Config.EVODownloadType.WaitDown then
        Module.CommonTips:ShowConfirmWindow(checkStr, confirmHandle)
    else
        confirmHandle()
    end

end

---@param fItemSelectCb function 选中回调
function LitePackageDownloadItem:InitItem(displayInfo, finishedCallback,isAutoDownload,bShowReddot)
    self:InitText(displayInfo)
    self.displayName = displayInfo.DisplayName
    self.ModuleKey = displayInfo.ModuleKey
    self.Category = displayInfo.Category
    self.bShowReddot = displayInfo.bShowReddot
    self.finishedCallback = finishedCallback
    if bShowReddot and Facade.GameFlowManager:GetCurrentGameFlow() ~= EGameFlowStageType.Login then
        self._wtRedDotWidget = Module.ReddotTrie:CreateReddotIns(self._wtReddotRoot)
    end
    self:RefreshState()
    self:CheckToDownload(isAutoDownload)
    if self._wtRedDotWidget then
        if self.bShowReddot then
            self._wtRedDotWidget:SetReddotVisible(true,EReddotType.Normal)
        else
            self._wtRedDotWidget:SetReddotVisible(false,EReddotType.Normal)
        end
    end

end

function LitePackageDownloadItem:CheckToDownload(isAutoDownload)
    if isAutoDownload then
        if self.btnStateType==Module.LitePackage.Config.EVODownloadType.WaitDown then
            self:OnWaitDownClicked()
        end
    end
end

function LitePackageDownloadItem:InitText(displayInfo)
    log("Cur Cell Text = ", displayInfo)
    self._wtMainText:SetText(displayInfo.DisplayName)
end

function LitePackageDownloadItem:OnMainBtnClicked()
    self:CloseReddot()
end

function LitePackageDownloadItem:CloseReddot()--隐藏红点
    if self._wtRedDotWidget then
        self._wtRedDotWidget:SetReddotVisible(false,EReddotType.Normal)
    end
    self:SetReddot(self.ModuleKey,false)
    Module.LitePackage.Config.evtReddotChanged:Invoke()
end

function LitePackageDownloadItem:OnItemClicked()
    if self.btnStateType == Module.LitePackage.Config.EVODownloadType.Downloaded then
        if self.finishedCallback then
            local func = self.finishedCallback
            func()
        end
        Module.LitePackage.Config.evtPakDownloadFinish:Invoke()
    elseif self.btnStateType == Module.LitePackage.Config.EVODownloadType.WaitDown then
        self:OnWaitDownClicked()
    elseif self.btnStateType == Module.LitePackage.Config.EVODownloadType.Donwing then
        self:OnDowningClicked()
    end
    self:CloseReddot()
end

---初始化或者刷新时使用，此时不触发回调
---@param bSelected boolean
function LitePackageDownloadItem:SetSelected(bSelected)
    self:SetCppValue("BP_Selected", bSelected or false)
    if self.BP_Set_Type then
        self:BP_Set_Type()
    end
end

-- 设置下载状态
function LitePackageDownloadItem:SetDownLoadType(down_type)
    if self.SetType then
        self:SetType(down_type,true)
    end
end

-- 点击开始下载
function LitePackageDownloadItem:OnWaitDownClicked()
    -- if LiteDownloadManager:IsDownloadingByModuleName("HDRuntimeCollection") then --若高清资源下载，则弹出提示并返回
    --     Module.CommonTips:ShowSimpleTip(LitePackageConfig.Loc.LitePackageMainPanel_Download_WaitForHDDownloading)
    --     return
    -- end
    self:DownloadResource()
    self:RefreshState()
    self:CloseReddot()
    Module.LitePackage.Config.evtPakDownloadTrigged:Invoke()
end

-- 点击暂停下载
function LitePackageDownloadItem:OnDowningClicked()
    -- if LiteDownloadManager:IsDownloadingByModuleName("HDRuntimeCollection") then --若高清资源下载，则弹出提示并返回
    --     Module.CommonTips:ShowSimpleTip(LitePackageConfig.Loc.LitePackageMainPanel_Download_WaitForHDDownloading)
    --     return
    -- end
    LiteDownloadManager:CancelByModuleName(self.ModuleKey)
    self:RefreshState()
    self:CloseReddot()
    Module.LitePackage.Config.evtPakDownloadTrigged:Invoke()
end

-- 点击继续下载
function LitePackageDownloadItem:OnPauseClicked()
    -- if LiteDownloadManager:IsDownloadingByModuleName("HDRuntimeCollection") then --若高清资源下载，则弹出提示并返回
    --     Module.CommonTips:ShowSimpleTip(LitePackageConfig.Loc.LitePackageMainPanel_Download_WaitForHDDownloading)
    --     return
    -- end
    self:DownloadResource()
    self:CloseReddot()
    self:RefreshState()
end

function LitePackageDownloadItem:RefreshState()
    if self._bInCleanUpState then -- 若处于清理界面，则无需实时刷新状态
        return
     end
    local pufferInitSucceed = LiteDownloadManager:IsPufferInitSucceed()
    if pufferInitSucceed == false then
        self.btnStateType = Module.LitePackage.Config.EVODownloadType.WaitDown
        self:SetDownLoadType(self.btnStateType)
        return
    end

    if self.ModuleKey == "" then
        self.btnStateType = Module.LitePackage.Config.EVODownloadType.Downloaded
        self:SetDownLoadType(self.btnStateType)
        self._wtMainText:SetText(self.displayName)
        return
    end

    local bDownloaded = LiteDownloadManager:IsDownloadedByModuleName(self.ModuleKey)
    if self.ModuleKey == "HDRuntimeCollection" then --高清资源是否下载进行个特判
        bDownloaded = LiteDownloadManager:IsHDDownloadStatus()
    end
    local nowSize = LiteDownloadManager:GetNowSizeByModuleName(self.ModuleKey)
    local totalSize = LiteDownloadManager:GetTotalSizeByModuleName(self.ModuleKey)
    if bDownloaded then -- 下载完成
        self.btnStateType = Module.LitePackage.Config.EVODownloadType.Downloaded
        self:SetDownLoadType(self.btnStateType)
        self._wtMainText:SetText(string.format("%s (%.1fMB)", self.displayName, totalSize / self.LiteDownloadMBNum))
        --self:OnItemClicked()
        self:ClearTimer()
        self:CloseReddot()
        Module.LitePackage.Config.evtPakDownloadFinish:Invoke()
    else
        local remainderSize = LiteDownloadManager:GetRemainderSizeToDownloadByModuleName(self.ModuleKey)
        local isDownloading = LiteDownloadManager:IsDownloadingByModuleName(self.ModuleKey)
        local isWaiting = LiteDownloadManager:IsWaitingByModuleName(self.ModuleKey)

        if totalSize == nil or totalSize <= 0 then
            self.btnStateType = Module.LitePackage.Config.EVODownloadType.WaitDown
            self:SetDownLoadType(self.btnStateType)
            return
        end

        local progressInfo = LiteDownloadManager:GetDownloadProgressInfoByModuleName(self.ModuleKey)
        if isDownloading then -- 下载中
            local cachSize = LiteDownloadManager:GetCachNowSize(self.ModuleKey)
            if nowSize < cachSize then
                cachSize = nowSize
            end

            local realSizeToDownload = LiteDownloadManager:GetRemainderSizeToDownloadByModuleName(self.ModuleKey)
            local downloadSpeed = LiteDownloadManager:GetDownlaodSpeed()
            local showStr = string.format("%.1fMB %.1fMB/s", realSizeToDownload / self.LiteDownloadMBNum, downloadSpeed / self.LiteDownloadMBNum)

            self.btnStateType = Module.LitePackage.Config.EVODownloadType.Donwing
            self:SetDownLoadType(self.btnStateType)
            self._wtMainText:SetText(string.format("%s (%s)", self.displayName, showStr))
        elseif isWaiting then -- 等待其他下载完成
            self.btnStateType = Module.LitePackage.Config.EVODownloadType.WaitDown
            self:SetDownLoadType(self.btnStateType)
            self._wtMainText:SetText(string.format("%s (%.1fMB)", self.displayName, remainderSize / self.LiteDownloadMBNum))
        else
            self.btnStateType = Module.LitePackage.Config.EVODownloadType.WaitDown
            self:SetDownLoadType(self.btnStateType)
            self._wtMainText:SetText(string.format("%s (%.1fMB)", self.displayName, remainderSize / self.LiteDownloadMBNum))
        end
        if self._wtCurProgressImg ~= nil then
            self._wtCurProgressImg:SetPercent(progressInfo.PercentValue / 100)
        end
    end

end



function LitePackageDownloadItem:InitCleanUpState() --初始化在清理界面的样式
    local totalSize = LiteDownloadManager:GetTotalSizeByModuleName(self.ModuleKey)
    self._bInCleanUpState= true
    self._wtCheckBox_Cleaning:Visible()
    self._wtCheckBox:Collapsed()
    self._wtWaitDownBtn:Collapsed()
    self._wtDowningBtn:Collapsed()
    self._wtPauseDownBtn:Collapsed()
    self._wtMainText:SetText(string.format(self.displayName,(totalSize / self.LiteDownloadMBNum)))
    self._wtDownloadedImg:Collapsed()
end

function LitePackageDownloadItem:SetCleanUpCheck(bIscheck) -- 在清理界面时是否被选中
    self._wtCheckBox_Cleaning:SetIsChecked(bIscheck)
end

function LitePackageDownloadItem:SelectedCleanUp(bSelcted)
    Module.LitePackage.Config.evtDownloadItemSelected:Invoke(bSelcted,self.ModuleKey,self.Category)
end

function LitePackageDownloadItem:SetIsEnabled(bEnabled)
    self._wtMainBtn:SetIsEnabled(bEnabled)
end

function LitePackageDownloadItem:SetbtnType(btnType)
    self.btnStateType = btnType
end

function LitePackageDownloadItem:DownloadResource() --下载资源，两种方式（用封装好的函数下载，或者通过传key的方式）
    if self.DownLoadFunc then
        local func = self.DownLoadFunc
        func()
    else
        -- check space > 3G
        local WIFI_DOWNLOAD_SPACE_GB = 3
        local LiteDownloadGBNum = 1024 * 1024 * 1024
        local freeSpace = LiteDownloadManager:GetDeviceFreeSpace()
        local spaceGB = freeSpace / LiteDownloadGBNum
        local nowSize = LiteDownloadManager:GetNowSizeByModuleName(self.ModuleKey)
        local totalSize = LiteDownloadManager:GetTotalSizeByModuleName(self.ModuleKey)
        local questSize = (totalSize - nowSize) / LiteDownloadGBNum
        if spaceGB < WIFI_DOWNLOAD_SPACE_GB + questSize then
            local confirmHandle = function()

            end

            LogAnalysisTool.SignButtonClicked(10200001)
            local content = Module.LitePackage.Config.Loc.LITE_DOWNLOAD_CHECK_SPACE_NOT_ENOUGH
            Module.CommonTips:ShowConfirmWindowWithSingleBtn(content, confirmHandle, nil)
        else
            LiteDownloadManager:DownloadByModuleName(self.ModuleKey)
        end
    end
end

function LitePackageDownloadItem:SetReddot(moduleKey,bShowReddot)
    local key = moduleKey.."ShowReddot"
    Facade.ConfigManager:SetString(key,tostring(bShowReddot))
end

return LitePackageDownloadItem