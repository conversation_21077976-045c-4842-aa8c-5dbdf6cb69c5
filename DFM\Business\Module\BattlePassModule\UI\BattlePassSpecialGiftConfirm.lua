----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMBattlePass)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class BattlePassSpecialGiftConfirm : LuaUIBaseView
local BattlePassSpecialGiftConfirm = ui("BattlePassSpecialGiftConfirm")
local BattlePassConfig = Module.BattlePass.Config
local BattlePassLogic = require "DFM.Business.Module.BattlePassModule.Logic.BattlePassLogic"
local CommonPopWindows = require "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindows"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"


--- 生命周期

function BattlePassSpecialGiftConfirm:Ctor()
    -- 蓝图控件引用
    self.wtRootWindow = self:Wnd("wtMainPanel", CommonPopWindows)
    local fCallbackIns = CreateCallBack(self.OnCloseBtnClicked, self)
    self.wtRootWindow:BindCloseCallBack(fCallbackIns)
    self.wtRootWindow:SetBackgroudClickable(true)
    self.wtRootWindow:OverrideGamepadSetting("BatltePass_BuyPack", nil, WidgetUtil.EUINavDynamicType.Default)
    self.wtRootWindow:AddSummaries({"BattlePass_GiftDetail"})
    local dfCommonButtons = self.wtRootWindow:SetConfirmBtnType(CommonPopWindows.EConfirmBtnType.CenterConfirm, {
        {btnText = "Test", fClickCallback = self.OnConfirmClicked, caller = self}
    })
    if dfCommonButtons then
        self.wtConfirmBtn = dfCommonButtons[CommonPopWindows.EHandleBtnType.Confirm]
    end
    self.wtDesc = self:Wnd("DFRichTextBlock_61", UITextBlock)
    self.wtRewardWaterFall = UIUtil.WndWaterfallScrollBox(self, "DFWaterfallScrollView_46", self.OnRewardWaterFallCount, self.OnRewardWaterFallProcessWidget) 

    --- 控件处理

    --- 成员变量声明
    self.bpLevel = Server.BattlePassServer:GetLevel()  -- 通行证当前等级
    self.giftLevel = -1  -- 礼包提升等级
    self.price = -1  -- 礼包价格
    self.rewardList = {}  -- 奖励列表

    --- 预定义


    --- 初始化逻辑
end

-- UI构造时触发, 用于初始化外部传参UI数据逻辑，由UIManager触发（仅触发一次）
-- 子节点蓝图不会触发该方法
-- Module加载好子控件所需的图片后，将数据传入主UI界面
---@overload fun(LuaUIBaseView, OnInitExtraData)
function BattlePassSpecialGiftConfirm:OnInitExtraData(extra)
    loginfo("BattlePassSpecialGiftConfirm:OnInitExtraData")
    self:InitParam(extra)
    self:MakeCache() 
end

-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function BattlePassSpecialGiftConfirm:OnOpen()
    loginfo("BattlePassSpecialGiftConfirm:OnOpen")

    -- 注册监听事件
    self:AddListenersOpen()
end

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function BattlePassSpecialGiftConfirm:OnClose()
    loginfo("BattlePassSpecialGiftConfirm:OnClose")

    -- 移除lua事件
    self:RemoveListenersOpen()

    --调出后续结算界面
    Module.Reward:ShowNextRewards()
end

-- UI OnOpen后、调用Show()显示时触发
-- 做表现
---@overload fun(LuaUIBaseView, OnShow)
function BattlePassSpecialGiftConfirm:OnShow()
    loginfo("BattlePassSpecialGiftConfirm:OnShow")
    self.wtRewardWaterFall:RefreshAllItems()
end

function BattlePassSpecialGiftConfirm:OnShowBegin()
    loginfo("BattlePassSpecialGiftConfirm:OnShowBegin")

    -- 注册监听事件
    self:AddListenersShow()

    self:RefreshUI()
    
    self:EnableGamepad()
end

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function BattlePassSpecialGiftConfirm:OnHide()
    loginfo("BattlePassSpecialGiftConfirm:OnHide")

    self:DisableGamepad()

    -- 移除监听事件
    self:RemoveListenersShow()
end

function BattlePassSpecialGiftConfirm:AddListenersShow()
    -- 普通事件
    -- self:AddLuaEvent(BattlePassConfig.evtMainPanelTest, self.OnMainPanelTest, self)
    -- ntf协议事件 ntfNameString
    -- Facade.ProtoManager:AddNtfListener("CSAuctionOrderChangeNtf", self.OnCSAuctionOrderChangeNtf, self)

    -- 移除事件监听
    self:RemoveListenersShow()

    -- 服务器数据更新事件
    --self:AddLuaEvent(Server.BattlePassServer.Events.evtBattlePassInfoUpdate, self.RefreshUIServer, self)
end

-- 移除show监听的事件
function BattlePassSpecialGiftConfirm:RemoveListenersShow()
    -- 服务器数据更新事件
    --self:RemoveLuaEvent(Server.BattlePassServer.Events.evtBattlePassInfoUpdate)
end

-- 监听open开始的事件
function BattlePassSpecialGiftConfirm:AddListenersOpen()
    -- 移除监听的事件 
    self:RemoveListenersOpen()

    -- 奖励点击事件
    self:AddLuaEvent(Server.BattlePassServer.Events.evtBattlePassCloseSpecialGift, self.OnBattlePassCloseSpecialGift, self)
end

-- 移除open监听的事件
function BattlePassSpecialGiftConfirm:RemoveListenersOpen()
    -- 服务器数据更新事件
    self:RemoveLuaEvent(Server.BattlePassServer.Events.evtBattlePassCloseSpecialGift)
end

--- 事件处理

function BattlePassSpecialGiftConfirm:OnConfirmClicked()
    loginfo("BattlePassSpecialGiftConfirm:OnConfirmClicked")
    
    BattlePassLogic.PayProcess(self.price, nil, self, self.PriceBtnConfirmMoneyEnoughHandle)
end

function BattlePassSpecialGiftConfirm:OnCloseBtnClicked()
    Facade.UIManager:CloseUI(self)
end

function BattlePassSpecialGiftConfirm:OnRewardWaterFallCount()
    return self.rewardCount
end

function BattlePassSpecialGiftConfirm:OnRewardWaterFallProcessWidget(position, itemWidget)
    local reward = self.rewardList[position]
    if reward == nil then
        return
    end

    local itemData = ItemBase:New(reward.iID, reward.iCount)
    itemWidget:InitItem(itemData)
end

function BattlePassSpecialGiftConfirm:OnBattlePassCloseSpecialGift()
    Facade.UIManager:CloseUI(self)
end

--- 刷新函数

function BattlePassSpecialGiftConfirm:RefreshUI()
    if self.bpLevel == nil or self.bpLevel <= 0 or
        self.giftLevel == nil or self.giftLevel <= 0 or
        self.price == nil or self.price <= 0 then
        Facade.UIManager:CloseUI(self)
        return
    end
    
    self:RefreshUIDesc()
    self:RefreshUIBtn()
end

function BattlePassSpecialGiftConfirm:RefreshUIDesc()
    self.wtDesc:SetText(string.format(BattlePassConfig.Loc.SpecialGiftConfirmDesc, self.bpLevel + self.giftLevel))
end

function BattlePassSpecialGiftConfirm:RefreshUIBtn()
    local iCurrencyItemID = Server.BattlePassServer:GetCurrencyItemID()
    local sPrice = string.format(BattlePassConfig.Loc.BuyPriceButton,
            Module.Currency:GetRichTxtImgByItemId(iCurrencyItemID),
            MathUtil.GetNumberFormatStr(self.price))
    self.wtConfirmBtn:SetMainTitle(sPrice)
end

---- 其他函数

function BattlePassSpecialGiftConfirm:OnNavBack()
    Facade.UIManager:CloseUI(self)
    return true
end

-- 按钮点击确认处理
function BattlePassSpecialGiftConfirm:PriceBtnConfirmMoneyEnoughHandle(iNeedBindCurrency, iNeedCurrency, tArg)
    -- 阻止干员NTF调用解锁弹窗
    Module.Reward:EnableNTFCall("Hero", false)

    -- 发送购买礼包请求
    Server.BattlePassServer:ReqBattlePassBuyPack(self.packID, iNeedBindCurrency, iNeedCurrency)
end

function BattlePassSpecialGiftConfirm:InitParam(extra)
    if extra then
        if extra.giftLevel then
            self.giftLevel = extra.giftLevel
        end
        if extra.price then
            self.price = extra.price
        end
        if extra.packID then
            self.packID = extra.packID
        end
    end
end

function BattlePassSpecialGiftConfirm:MakeCache()
    self.rewardCount = 0
    self.rewardList = {}

    local tRewardSummary = BattlePassLogic.GetRewardSummaryByCurrPayType(self.bpLevel + 1, self.bpLevel + self.giftLevel)

    for iID, iCount in pairs(tRewardSummary) do
        if iID ~= nil and iCount ~= nil then
            self.rewardCount = self.rewardCount + 1
            self.rewardList[self.rewardCount] = {["iID"] = iID, ["iCount"] = iCount}
        end
    end
end

-- 手柄相关

function BattlePassSpecialGiftConfirm:EnableGamepad()
    if not IsHD() then
        return
    end

    if not self._wtRewardNavGroup then
        self._wtRewardNavGroup = WidgetUtil.RegisterNavigationGroup(self.wtRewardWaterFall, self, "Hittest")
        if self._wtRewardNavGroup then
            self._wtRewardNavGroup:AddNavWidgetToArray(self.wtRewardWaterFall)
            self._wtRewardNavGroup:SetScrollRecipient(self.wtRewardWaterFall)
        end
    end

    WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtRewardNavGroup)
end

function BattlePassSpecialGiftConfirm:DisableGamepad()
    if not IsHD() then
        return
    end
    
    WidgetUtil.RemoveNavigationGroup(self)

    if self._wtRewardNavGroup then
        self._wtRewardNavGroup = nil
    end
end


return BattlePassSpecialGiftConfirm