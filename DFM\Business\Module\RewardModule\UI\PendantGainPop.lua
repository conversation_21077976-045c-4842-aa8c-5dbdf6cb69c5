----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMReward)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class PendantGainPop : LuaUIBaseView
local RewardBaseView = require "DFM.Business.Module.RewardModule.UI.RewardBaseView"
local EGPInputModeType = import "EGPInputModeType"
local RewardDetail = require "DFM.Business.Module.RewardModule.UI.RewardDetail"
local ItemDetailViewEquip = require "DFM.Business.Module.ItemDetailModule.UI.ItemDetailType3.ItemDetailViewEquip"
local FAnchors                      = import "Anchors"


local PendantGainPop = ui("PendantGainPop", RewardBaseView)

function PendantGainPop:Ctor()
    self._wtRewardDetail = self:Wnd("wtRewardDetail", RewardDetail)
    self._wtRewardDetail:BindJumpClick(self._OnSkipBtnClick, self)
    self._wtRewardDetail:SetShowSkipTxt(true)
    self._wtItemDetailView = self:Wnd("wtItemDetailView", ItemDetailViewEquip)
    self._wtItemDetailView:SetWeaponDetailIsHideBtn(true)
    self._wtItemDetailView:SetShowWeaponDetailCheckBox(false)
    self._closeClickCount = -1
    self._wtShareBtn = self:Wnd("wtShareBtn", DFCommonButtonOnly)
    self._wtShareBtn:Event("OnClicked", self._OnShareBtnClick, self)

    self._wtDownloadPanel = self:Wnd("DFCanvasPanel_Download", UIWidgetBase)
end


function PendantGainPop:OnInitExtraData(pendantItem)
    self._item = pendantItem
end

function PendantGainPop:OnOpen()
    self:_AddListeners()
end

function PendantGainPop:OnClose()
    Facade.UIManager:ClearSubUIByParent(self, self._wtItemDetailView)
    self:RemoveAllLuaEvent()
    if Facade.GameFlowManager:GetCurrentSubStage() == ESubStage.HallMall then
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "ResetDisplayItem")
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "ResetWeapon")
    end
    if self._bExecuteClose ~= true then
        self._bExecuteClose = true
        Module.Reward:ShowNextRewards()
    end
end

function PendantGainPop:OnShowBegin()
    self:_RefreshWidget()
    self:HandleTransition(false)
    if Facade.GameFlowManager:GetCurrentSubStage() == ESubStage.HallMall then
        self:_OnRefreshModel(ESubStage.HallMall)
    end
    self._wtRewardDetail:AddJumpInputAction()
end

function PendantGainPop:OnHideBegin()
end


function PendantGainPop:OnShow()
    self:SetCPPValue("WantedInputMode", EGPInputModeType.GameAndUI)
end

function PendantGainPop:OnHide()
end

function PendantGainPop:OnAnimFinished(anim)
    if anim == self.WBP_Hero_ShowHero_in then
        if self._closeClickCount < 1 then
            self._closeClickCount = self._closeClickCount + 1
        end
    elseif anim == self.WBP_Hero_ShowHero_out then
        if self._bExecuteClose ~= true then
            self._bExecuteClose = true
            Module.Reward:ShowNextRewards(self._bTabPressed == true)
        end
    end
end

function PendantGainPop:_AddListeners()
    self:AddLuaEvent(Module.IrisSafeHouse.Config.evtTabBackToSafeHouseHD, self._OnTabPressed, self)
    self:AddLuaEvent(LuaGlobalEvents.evtSceneLoaded, self._OnRefreshModel, self)

    self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult, self._ModuleDownloadResult, self)
    -- self:AddLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged, self._OnPackStateChanged, self)
end

function PendantGainPop:_RemoveListeners()
    -- self:RemoveLuaEvent(Module.IrisSafeHouse.Config.evtTabBackToSafeHouseHD)
    -- self:RemoveLuaEvent(LuaGlobalEvents.evtSceneLoaded)

    -- self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult)
    -- self:RemoveLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged)
end

function PendantGainPop:_RefreshWidget()
    if self._closeClickCount < 1 then
        self._closeClickCount = self._closeClickCount + 1
    end
    self._wtRewardDetail:SetType(2)
    self._wtRewardDetail:SetQuality(nil)
    self._wtRewardDetail:SetName(nil)
    self._wtRewardDetail:SetDesc(nil)
    self._wtShareBtn:Collapsed()
    if isvalid(self._item) then
        self._wtRewardDetail:SetMainTitle(Module.Reward.Config.Loc.ObtainPendant)
        self._wtItemDetailView:UpdateItem(self._item)
        self._wtItemDetailView:SetShowkillCnt(false)
        if not IsHD() then
           Module.Share:FuncPointUnLock(SwitchSystemID.SubShareHangingOrnaments, self._wtShareBtn)
        end
    else
        self._wtRewardDetail:SetMainTitle(Module.Reward.Config.Loc.Unknown)
    end
    if self._closeClickCount < 1 then
        self:PlayAnimation(self.WBP_Hero_ShowHero_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    end
end

function PendantGainPop:_OnRefreshModel(curSubStageType)
    if not curSubStageType or curSubStageType == ESubStage.HallMall then    
        Facade.HallSceneManager:SetDisplayBackground(self._item.id, false)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "ResetDisplayItem")
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "ResetWeapon")

        local bDownloaded = self:IsDownloaded()
        if isvalid(self._item) and bDownloaded then
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetIsAdapter", true)
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetEnableTrans", true)
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayWeapon", self._item:GetRawDescObj(), self._item.id, 
            false, false)
        end
    end

    self:_InternalOnProcessUIDownload()
end

function PendantGainPop:_OnSkipBtnClick()
    if self._closeClickCount == 1 then
        self._closeClickCount = 2
        self:HandleTransition(true)
		Facade.SoundManager:StopUIAudioEvent("UI_Common_Popup")
		self:PlayAnimation(self.WBP_Hero_ShowHero_out, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    elseif self._closeClickCount == 0 then
        self._closeClickCount = 1
        self:SkipAnimation(self.WBP_Hero_ShowHero_in)
        self._wtRewardDetail:SkipInAnimation()
    end
end

function PendantGainPop:OnNavBack()
    self:_OnSkipBtnClick()
    return true
end

function PendantGainPop:_OnTabPressed()
    self._bTabPressed = true
    self:SkipAnimation(self.WBP_Hero_ShowHero_in)
    Facade.SoundManager:StopUIAudioEvent("UI_Common_Popup")
    self:PlayAnimation(self.WBP_Hero_ShowHero_out, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
end

function PendantGainPop:_OnShareBtnClick()
    local PreScreenshotShare = CreateCallBack(function(self)
        self:PreScreenshotShare()
    end,self)

    local AfterScreenshotShare = CreateCallBack(function(self)
        self:AfterScreenshotShare()
    end,self)

    Module.Share:ApplySimpleScreenshot(
            "RewardDetail",
            PreScreenshotShare,
            AfterScreenshotShare,
            Module.Share.Config.ESocialShareSceneEnum.RewardDetailShare)
end

function PendantGainPop:PreScreenshotShare()
    Module.CommonBar:SetTopBarVisible(false)
    self._wtShareBtn:Hidden()
    self._wtRewardDetail:SetShowSkipPanel(false)
    self._wtRewardDetail:SetMainTitleVisible(false)
end

function PendantGainPop:AfterScreenshotShare()
    Module.CommonBar:SetTopBarVisible(true)
    self._wtShareBtn:Visible()
    self._wtRewardDetail:SetShowSkipPanel(true)
    self._wtRewardDetail:SetMainTitleVisible(true)
end

function PendantGainPop:_ModuleDownloadResult(moduleName, bSuccess, errorCode)
    local pendantID = self:_GetPendantItemID()
    local pakCategory = Module.ExpansionPackCoordinator:GetDownloadCategary(pendantID)
    if pakCategory == nil or pakCategory == "None" or pakCategory ~= moduleName then
        return
    end

    if bSuccess then
        local questName = Module.ExpansionPackCoordinator:GetQuestNameByModuleName(moduleName)
        if questName == nil then
            questName = ""
        end
        local successTips = string.format(Module.Gunsmith.Config.Loc.GunsmithSkinDownloadSuccessTips, questName)
        Module.CommonTips:ShowSimpleTip(successTips)
    end

    self:_OnRefreshModel(ESubStage.HallMall)
end

function PendantGainPop:_OnPackStateChanged(moduleName, packState)
    local isSuccess = packState == Module.ExpansionPackCoordinator.Config.PackState.Installed
    self:_ModuleDownloadResult(moduleName, isSuccess, 0)
end

function PendantGainPop:_GetPendantItemID()
    local bIsValid = isvalid(self._item)
    if not bIsValid then
        return 0
    end
    return self._item.id or 0
end

function PendantGainPop:_InternalOnProcessUIDownload()
    self:_InternalAddCommonDownloadSubUI()
    local bIsValid = isvalid(self._wtCommonDownload) and self._wtCommonDownload.Hidden and self._wtCommonDownload.Visible
    if not bIsValid then
        return
    end
    local bDownloaded = self:IsDownloaded()
    if bDownloaded then
        self._wtCommonDownload:Hidden()
    else
        self._wtCommonDownload:SelfHitTestInvisible()
    end
end

function PendantGainPop:_InternalAddCommonDownloadSubUI()
    local bIsValid = isvalid(self._wtCommonDownload)
    if bIsValid then
        return
    end
    local weakUIIns = Facade.UIManager:AddSubUI(self, UIName2ID.LitePackageCommonDownload, self._wtDownloadPanel)
    bIsValid = isvalid(weakUIIns)
    if not bIsValid then
        return
    end
    self._wtCommonDownload = getfromweak(weakUIIns)
    bIsValid = isvalid(self._wtCommonDownload) and self._wtCommonDownload.SetUIPositionType
    if not bIsValid then
        return
    end
    self._wtCommonDownload:SetUIPositionType(2)

    local slot = UWidgetLayoutLibrary.SlotAsCanvasSlot(self._wtCommonDownload)
    if slot == nil then
        return
    end
    local anchor = FAnchors()
    anchor.Minimum = FVector2D(0, 0)
    anchor.Maximum = FVector2D(1.0, 1.0)
    slot:SetAnchors(anchor)
    slot:SetAlignment(FVector2D(0, 0))
    slot:SetOffsets(FMargin(0, 0, 0, 0))
end

function PendantGainPop:IsDownloaded()
    local pendantID = self:_GetPendantItemID()
    local bDownloaded = self:_InternalIsDownloaded(pendantID)
    return bDownloaded
end

function PendantGainPop:_InternalIsDownloaded(pendantID)
    local bIsValid = isvalid(self._wtCommonDownload) and self._wtCommonDownload.InitModuleKey
    if not bIsValid then
        return true
    end
    local pakCategory = Module.ExpansionPackCoordinator:GetDownloadCategary(pendantID)
    return pakCategory == nil or pakCategory == "None" or self._wtCommonDownload:InitModuleKey(pakCategory)
end

return PendantGainPop