----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaSMatch)
----- LOG FUNCTION AUTO GENERATE END -----------



local EPropertyClass = import "EPropertyClass"
local ULuautils = import "Luautils"
local UDFMGameTss = import "DFMGameTss"
local DFMGameTss = UDFMGameTss.Get(GetGameInstance())
local DataTableLoader=import"DataTableLoader"
local EDataTableLoaderGameScene=import"EDataTableLoaderGameScene"
local UDFMTODSubSystem = import "DFMTODSubSystem"
local UGameVersionUtils = import "GameVersionUtils"
local ULuaExtension = import("LuaExtension")
local UGameplayBlueprintHelper = import "GameplayBlueprintHelper"
local function log(...)
    print("[MatchServer]", ...)
end

local function printtable(t, prefix)
    log(prefix)
    logtable(t)
end

-- 依赖AccountServer/SoundManager
-- TODO:需要请求开赛之类的协议放在此处

local EMatchState = {
    InIdle = 1,
    Unprepare = 2,
    Prepared = 3,
    Matching = 4,
    InGame = 5,
    WaitForGotoGame = 6,
}

local SafeHouseDSState = {
    Nothing = 0,
    NeedEnterDS = 1,
    NeedEnterStandalone = 2,
}

local function matchStatetoString(state)
    local stateTable={}
    stateTable[EMatchState.InIdle]="InIdle"
    stateTable[EMatchState.Unprepare]="Unprepare"
    stateTable[EMatchState.Prepared]="Prepared"
    stateTable[EMatchState.Matching]="Matching"
    stateTable[EMatchState.InGame]="InGame"
    stateTable[EMatchState.WaitForGotoGame]="WaitForGotoGame"
    return stateTable[state]

end

local UDFMIrisEnterSeamlessGameplayHelper = import "DFMIrisEnterSeamlessGameplayHelper"
local UWeaponAssemblyBPLibrary = import "WeaponAssemblyBPLibrary"
local UBlueprintPathsLibrary = import "BlueprintPathsLibrary"
local UGPGameplayStatics = import "GPGameplayStatics"
local UGPGameplayGlobalDelegates = import "GPGameplayGlobalDelegates"
local UGameSDKMgr = import "GameSDKManager"
local FPlayerFrontEndMatchInfo = import "PlayerFrontEndMatchInfo"
local FTeamMemberIdentity = import "TeamMemberIdentity"
local MatchServer = class("MatchServer", require("DFM.YxFramework.Managers.Server.ServerBase"))
local UDFMIrisEnterSubsystem = import "DFMIrisEnterSubsystem"
local UDFMGameplayDelegates = import "DFMGameplayDelegates"
local FSOLQuest = import "SOLQuest"
local FSOLQuestObjection = import "SOLQuestObjection"
local ESOLQuestObjectionState = import "ESOLQuestObjectionState"
local UDFMGameLoadingManager = import("DFMGameLoadingManager")
local LOCAL_DS_PORT = 18888

--- BEGIN MODIFICATION @ VIRTUOS: 实现Competitive Activity
local FPlatformMemberInfo = import "PlatformMemberInfo"
local FPlatformMatchInfo = import "PlatformMatchInfo"
local UDFMPlatformActivityManager = import "DFMPlatformActivityManager"
--- END MODIFICATION

function MatchServer:Ctor()
    -- 监听所有开赛相关协议
    self:_InitEvents()
    -- 匹配状态
    self._matchingState = EMatchState.InIdle
    -- 是否本地ds
    self.bUseLocalDs = false

    -- 连接ds的ip/port及其他参数
    self.dsDomain = nil
    self.dsLinkDomain = ""
    self.dsIpList = nil
    self.IsOBPlayer = false
    self.dsIp = 0
    self.dsPort = 0
    self.dsMapId = 0
    self.dsPlayerToken = 0
    self.dsRoomId = 0
    self.UseSeamlessTravel = false
    self.matchStartDelay = 0
    self._matchModeInfo = {}
    self:_AddListenIrisReady() -- TODO: 感觉设计不合理，不该在这里处理

    self.seqId = 0
    self.reportIDCRoundTripTimeMap = {}
    self.seqId2AddrMap = {}
    self.seqId2RecvStateMap = {}
    self._SafeHouseDSRoomId = -1
    self._SafeHouseDSTeamId = -1
    self._hasReport = false
    self.bGameStateChanged = false
    self.dsHostInfo = {}
    self.matchNtf = nil
    self.lastReconnectPanelConfirmTime = 0
    self:SetSafeHouseDSState_Nothing()
end

function MatchServer:SetSafeHouseDSState_ToDS()
    self._SafeHouseDSState = SafeHouseDSState.NeedEnterDS
end

function MatchServer:SetSafeHouseDSState_ToStandalone()
    self._SafeHouseDSState = SafeHouseDSState.NeedEnterStandalone
end

function MatchServer:SetSafeHouseDSState_Nothing()
    self._SafeHouseDSState = SafeHouseDSState.Nothing
end

function MatchServer:SafeHouseDSStateIsToDS()
    return self._SafeHouseDSState == SafeHouseDSState.NeedEnterDS
end

function MatchServer:SafeHouseDSStateIsToStandalone()
    return self._SafeHouseDSState == SafeHouseDSState.NeedEnterStandalone
end

function MatchServer:SafeHouseDSStateIsNothing()
    return self._SafeHouseDSState == SafeHouseDSState.Nothing
end

function MatchServer:ResetSafeHouseDSInfo()
    self:SetSafeHouseDSRoomId(-1)
	self:SetSafeHouseDSTeamId(-1)
    self:SetSafeHouseDSState_Nothing()
end

function MatchServer:GetSafeHouseDSTeamId()
    log("MatchServer:GetSafeHouseDSTeamId",self._SafeHouseDSTeamId)
    return self._SafeHouseDSTeamId
end

function MatchServer:SetSafeHouseDSTeamId(safeHouseDSTeamId)
    log("MatchServer:SetSafeHouseDSTeamId",safeHouseDSTeamId)
    self._SafeHouseDSTeamId = safeHouseDSTeamId
end

function MatchServer:GetSafeHouseDSRoomId()
    log("MatchServer:GetSafeHouseDSRoomId",self._SafeHouseDSRoomId)
    return self._SafeHouseDSRoomId
end

function MatchServer:SetSafeHouseDSRoomId(safeHouseDSRoomId)
    log("MatchServer:SetSafeHouseDSRoomId",safeHouseDSRoomId)
    self._SafeHouseDSRoomId = safeHouseDSRoomId
end

function MatchServer:GetDsRoomId()
    log("MatchServer:GetDsRoomId:",self.dsRoomId)
    return self.dsRoomId
end

function MatchServer:SetDsRoomId(dsRoomId)
    log("MatchServer:SetDsRoomId:",dsRoomId)
    self.dsRoomId = dsRoomId
end

function MatchServer:GetDsTeamId()
    log("MatchServer:GetDsTeamId:",self.dsTeamId)
    return self.dsTeamId
end

function MatchServer:SetDsTeamId(dsTeamId)
    log("MatchServer:SetDsTeamId:",dsTeamId)
    self.dsTeamId = dsTeamId
end

function MatchServer:GetMatchId()
    log("MatchServer:GetMatchId",self.matchId)
    return self.matchId
end

function MatchServer:SetMatchId(matchId)
    log("MatchServer:SetMatchId",matchId)
    self.matchId = matchId
    local matchModeDataConfig=Facade.TableManager:GetTable("MatchModeDataConfig")
    if matchModeDataConfig then
        if matchModeDataConfig[matchId] then
            local matchModeInfo={
                game_mode=matchModeDataConfig[matchId].GameMode,
                game_rule=matchModeDataConfig[matchId].GameRule,
                sub_mode=matchModeDataConfig[matchId].MatchSubMode,
                team_mode=matchModeDataConfig[matchId].TeamMode,
                raid_id=matchModeDataConfig[matchId].RaidID,
                map_id=matchModeDataConfig[matchId].MapID,
                match_mode_id=matchId,

            }
            self:SetMatchModeInfo(matchModeInfo)
        else
            logerror("MatchServer:SetMatchId, configRow is nil!!!")
        end
    else
        logerror("MatchServer:SetMatchId, matchModeDataConfig is nil!!!")
    end

end

function MatchServer:OnLobbyJoinMatch(matchModeId)
    self:StopReportFlowInGameTimeOutTimer()
    self.waitTimer=Timer:NewIns(5*60)
    self.waitTimer:AddListener(self.ReportFlowInGameTimeOut,self)
    self.waitTimer:Start()
end

function MatchServer:ReportFlowInGameTimeOut()
    local req=pb.CSPlayerJoinDsStatusReq:New()
    req.ds_room_id=Server.MatchServer:GetDsRoomId()
    req.match_mode_id=self._matchId
    req.status=3
    req.fail_reason=4
    req.desc=tostring(ServerTipCode.MatchJoinTimeOut)
    req:Request()
    logwarning("MatchServer:ReportFlowInGameFaild CSPlayerJoinDsStatusReq_Request","dsRoomId",req.ds_room_id,"matchModeId",req.match_mode_id,"status",req.status,"desc",req.desc)
end

function MatchServer:StopReportFlowInGameTimeOutTimer()
    if self.waitTimer then
        self.waitTimer:Stop()
        self.waitTimer=nil
    end
end

function MatchServer:SetGameStateChanged(bGameStateChanged)
    self.bGameStateChanged = bGameStateChanged
end


function MatchServer:IsGameStateChanged()
    logwarning("MatchServer:IsGameStateChanged:",self.bGameStateChanged)
    return self.bGameStateChanged
end

function MatchServer:_OnLuaNetworkFailure()
    -- 连接ds失败时，回到安全屋或模式选择大厅需要检测游戏是否结束，结束需要弹窗提示
    -- 如果玩家仍在游戏中，需要弹窗重连
    local fStateInfoCallback = function (res)
        local bServerInGame = Server.AccountServer:IsPlayerInGame()
        local gameflow = Facade.GameFlowManager:GetCurrentGameFlow()
        logerror("[OnLuaNetworkFailure] curgameflow = %s",gameflow)
        local bClientInGame =  gameflow == EGameFlowStageType.Game or gameflow == EGameFlowStageType.GameSettlement
        if bServerInGame == false and bClientInGame then
            logerror("[OnLuaNetworkFailure] self.bGameStateChanged = true")
            if not self.bGameStateChanged then
                UECall_ShowReconnectPanel()
            end
            self.bGameStateChanged = true
        end
    end

    local param = {bHighFrequency = false}
    -- 连接ds发生错误，回到大厅时需要检测
    Server.AccountServer:GetStateInfo(fStateInfoCallback, param)
    if Facade.GameFlowManager:CheckIsInFrontEnd() and (Server.MatchServer:GetIsWaitForGotoGame() or Server.MatchServer:GetIsInGame()) then
        local req=pb.CSPlayerJoinDsStatusReq:New()
        req.ds_room_id=Server.MatchServer:GetDsRoomId()
        req.match_mode_id=self._matchId
        req.status=3
        req.fail_reason=4
        req.desc=tostring(ServerTipCode.MatchNetworkFailure)
        req:Request()
        logwarning("[OnLuaNetworkFailure] CSPlayerJoinDsStatusReq_Request","dsRoomId",req.ds_room_id,"matchModeId",req.match_mode_id,"status",req.status,"desc",req.desc)
    end
end

-- 异步域名解析相关 start
-- 异步解析回调
function MatchServer:_OnDnsAsyncResloved(ip, port, param, seed)
    if seed == EDnsResloveSeed.Ping then
        -- 测速
       self:_SetIdcSelectorInfo(ip, port, param)
    elseif seed == EDnsResloveSeed.Login then
        -- 登录
        logwarning("[OnDnsAsyncResloved] ignore event from login")
    elseif seed == EDnsResloveSeed.EchoDomain then
        -- 探测域名
        logwarning("[OnDnsAsyncResloved] event from EchoDomain ip and port", ip, port)
        self.Events.evtUpdateUdpEchoAddress:Invoke(ip, port)
        --Module.NetworkBusiness:SetUdpEchoAddress(ip, port)
    else
        -- 入局
        for _, hostInfo in ipairs(self.dsIpList) do
            for _, dsIpInfo in ipairs(hostInfo.ds_ip_info) do
                if dsIpInfo.ds_textual_ip == ip then
				   local dsHostInfo = {Ip = ip, Port = dsIpInfo.ds_port, Param = param, Seed = seed, Domain = hostInfo.ds_domain}
                   logerror("[OnDnsAsyncResloved] async resolved, ip and port:",dsIpInfo.ds_textual_ip, dsIpInfo.ds_port)
                   table.insert(self.dsHostInfo, dsHostInfo)
                end
            end
        end


        if #self.dsHostInfo > 0 and #self.dsHostInfo == self.dsIndex then
            logerror("[OnDnsAsyncResloved] async resolved complete!")
            table.sort(self.dsHostInfo, function(a, b)
                local aParam = a.Param or 999
                local bParam = b.Param or 999
                return aParam < bParam
            end)

            local defaultHostInfo = {Ip = self.dsIp, Port = self.dsPort, Param = 999, Seed = seed}
            table.insert(self.dsHostInfo, dsHostInfo)
            local realHostInfo = self.dsHostInfo[1]
            if realHostInfo and (seed == EDnsResloveSeed.StartMatch or seed == EDnsResloveSeed.BHDEnterGame) then
                self:SetIpAddressList()
                logerror("[OnDnsAsyncResloved] async resolved complete! use ip and port:", realHostInfo.Ip, realHostInfo.Port)
                self.dsLinkDomain = realHostInfo.Domain
                self.Events.evtUpdateAccelAddress:Invoke(self.dsHostInfo)
                -- Module.NetworkBusiness:SetAccelAddressByDsInfo(self.dsHostInfo)
                -- Module.NetworkBusiness:BeginRound()
                self:_OnDnsReslovedGuide(realHostInfo.Ip, realHostInfo.Port, realHostInfo.Seed)
            else
                if seed == EDnsResloveSeed.UpdateIpInfo then
                    logerror("[OnDnsAsyncResloved] only update ip list")
                    self:SetIpAddressList()
                    if self._dsInfoTimeHandler then
                        Timer.CancelDelay(self._dsInfoTimeHandler)
                        self._dsInfoTimeHandler = nil
                    end
                end
            end
        end
    end
end

function MatchServer:_SetIdcSelectorInfo(ip, port, param)
    if self.IdcNum == nil then
        logerror("[SetIdcSelectorInfo] idcNum is nil, give up report")
        return
    end
    self.IdcNum = self.IdcNum - 1
    local useThisAddr = true
    local roundtripAddr = self.indexToAddr[param]

    if roundtripAddr and roundtripAddr.domain ~= nil and self.domainMap[roundtripAddr.domain] == nil then
        self.domainMap[roundtripAddr.domain] = ip
    end

    if roundtripAddr and roundtripAddr.domain ~= nil and self.domainMap[roundtripAddr.domain] ~= roundtripAddr.vip then
        useThisAddr = false
    end

    if useThisAddr and roundtripAddr then
        logwarning("[SetIdcSelectorInfo] final ping: domain = %s, ip = %s, port = %s", roundtripAddr.domain, ip, port)
        if PLATFORM_IOS then
            ip = self:ConvertIPv4ToIPv6(ip)
            logwarning("[SetIdcSelectorInfo] convert ipv4 to ipv6 %s", ip)
        end
        self.seqId = self.seqId + 1
        self.seqId2AddrMap[self.seqId] = roundtripAddr
        local idcSelectorIns = UGameSDKMgr.GetGameIdcSelectorIns(GetGameInstance())
        if idcSelectorIns then
            if ip and port and self.seqId then
                idcSelectorIns:AddUdpIpAddr(ip, port, self.seqId)
            end
        end
    end    

    if self.IdcNum == 0 then
        local vipNum = self.index - 1
        local waitTimeoutTime = self.conf.client_udp_timeout * 0.001
        if vipNum > 0 then
            -- 出现过同时出现2个回包的情况
            if self._timerDelayReportIdcSpeed then
                logwarning("[SetIdcSelectorInfo]  when a delayReport is timing finish %d, ignore this call", Timer.IsFinished(self._timerDelayReportIdcSpeed) and 1 or 0)
                return
            end
            local idcSelectorIns = UGameSDKMgr.GetGameIdcSelectorIns(GetGameInstance())
            idcSelectorIns:SetEnable(true)
            -- 线程等待最多1倍，真实收包时间(c++ FUdpThreadRunable::Run() sleep时间)等待1倍，每个间隔0.01m
            waitTimeoutTime = waitTimeoutTime * (vipNum + 1) + 0.01 * vipNum
            self._timerDelayReportIdcSpeed = Timer.DelayCall(waitTimeoutTime, self.ReportIdcSpeed, self)
            logwarning("[SetIdcSelectorInfo] idcinfo with %d idcs %d vips and will report in %f seconds later",#self.cache_idc_list, vipNum, waitTimeoutTime)
        end
    end
end

--获取最终url
function MatchServer:GetLevelUrlAsync(ip, port, mapId, dsToken)
    local modularWeapon = UWeaponAssemblyBPLibrary.GetWeaponGlobals().bModularWeaponEnable and 1 or 0
    local url = string.format("%s:%d?PlayerId=%s?ModularWeapon=%s?Cookie=%s?MapId=%s?SpotGroup=%d?DSRoomId=%d?IsOBPlayer=%s",
        ip,
        port, 
        Server.AccountServer:GetPlayerIdStr(),
        modularWeapon, 
        dsToken, 
        mapId,
        Server.IrisSafeHouseServer.irisStartSpot,
        self:GetDsRoomId(),
        self.IsOBPlayer)
    logwarning("[GetLevelUrlAsync] url = ", url)
     -- 上报 CLB

    local bFound
    local bEnableSwitchIpAddressDynamic, bFound = UGameplayBlueprintHelper.GPGetConsoleVariableIntValue("dualChannel.EnableSwitchIpAddressDynamic", bFound)
    if bEnableSwitchIpAddressDynamic == 0 then
        LogAnalysisTool.DOSendMatchDsCLB(Server.AccountServer:GetPlayerIdStr(), self:GetDsRoomId(), self.dsLinkDomain , ip, port , 0)
    end
    return url
end

-- 开始异步解析
function MatchServer:_ParseDsInfoAsync(dsIpList, defaultIP, defaultPort, seed, echoIpList)
    self.dsHostInfo = {}
    self.dsIndex = 0
    if seed == EDnsResloveSeed.StartMatch and not dsIpList or #dsIpList == 0 then
        logerror("[ParseDsInfoAsync] dsIpList is nil or empty")
        self:_OnDnsReslovedGuide(defaultIP, defaultPort, seed)
        return
    end

    logerror("[ParseDsInfoAsync] defaultIP and defaultPort:", defaultIP , defaultPort)
    local OneSDKModule = import "DFMGameOneSDK"
    local _OneSDK = OneSDKModule.Get(GetGameInstance())
    local bNeedEchoAddress = PLATFORM_ANDROID and IsBuildRegionCN() and echoIpList and #echoIpList > 0

    if bNeedEchoAddress then
        for _, hostInfo in ipairs(echoIpList) do
            if hostInfo.ds_domain then
                local domain = string.split(hostInfo.ds_domain, ":")[1]
                local port = string.split(hostInfo.ds_domain, ":")[2]
                if _OneSDK and domain and port then
                    logwarning("[ParseDsInfoAsync] echoIplist domain, port:", domain, port)
                    _OneSDK:GetAddressByNameFromLua(domain, port, 0, EDnsResloveSeed.EchoDomain)
                end
            end
        end
    end

    for _, hostInfo in ipairs(dsIpList) do
        if hostInfo.ds_domain and #hostInfo.ds_ip_info > 0 then
            if _OneSDK then
                -- 要把所有的域名都解开
                self.dsIndex = self.dsIndex + 1
                logwarning("[ParseDsInfoAsync] ds_domain:", hostInfo.ds_domain)
                _OneSDK:GetAddressByNameFromLua(hostInfo.ds_domain, 0, self.dsIndex, seed)
            end
        end
    end

    -- DNS服务异常时保底策略
    local function f()
        if #self.dsHostInfo == 0 then
            -- 域名解析没有收到任何结果
            logerror("[ParseDsInfoAsync] DsInfoTimeHandler dns error and do not have useful ip,  use defaultIp and defaultPort:",defaultIP, defaultPort)
            self:_OnDnsReslovedGuide(defaultIP, defaultPort, seed)
        else
            -- 这里表明有部分域名解析成功，可能有部分域名解析失败
            logerror("[ParseDsInfoAsync] DsInfoTimeHandler dns wait timeout and have useful ip")
            table.sort(self.dsHostInfo, function(a, b)
                local aParam = a.Param or 999
                local bParam = b.Param or 999
                return aParam < bParam
            end)

            local defaultHostInfo = {Ip = self.dsIp, Port = self.dsPort, Param = 999, Domain = self.Domain, Seed = EDnsResloveSeed.StartMatch}
            table.insert(self.dsHostInfo, defaultHostInfo)
            local realHostInfo = self.dsHostInfo[1]
            if (seed == EDnsResloveSeed.StartMatch or seed == EDnsResloveSeed.BHDEnterGame) and realHostInfo then
                logerror("[ParseDsInfoAsync] DsInfoTimeHandler start link:", realHostInfo.Ip, realHostInfo.Port)
                self.dsLinkDomain = realHostInfo.Domain
                self.Events.evtUpdateAccelAddress:Invoke(self.dsHostInfo)
                -- Module.NetworkBusiness:SetAccelAddressByDsInfo(self.dsHostInfo)
                -- Module.NetworkBusiness:BeginRound()
                self:_OnDnsReslovedGuide(realHostInfo.Ip, realHostInfo.Port, realHostInfo.Seed)
            else
                if seed == EDnsResloveSeed.UpdateIpInfo then
                    logerror("[ParseDsInfoAsync] only update ip list")
                    self:SetIpAddressList()
                end
            end
        end
    end
    
    self._dsInfoTimeHandler = Timer.DelayCall(3, f)
end

-- 异步域名解析相关 end

function MatchServer:_OnDnsReslovedGuide(ip, port, seed)
    if seed == EDnsResloveSeed.StartMatch then
        if self._dsInfoTimeHandler then
            logerror("[OnDnsReslovedGuide] StartMatch cancel delay timer:", ip , port)
            Timer.CancelDelay(self._dsInfoTimeHandler)
            self._dsInfoTimeHandler = nil
        end
        self:SetDsIpAndPort(ip, port)
        logerror("[OnDnsReslovedGuide] ip, port, EDnsResloveSeed.StartMatch", ip , port)
        self:StartMatch(ip, port, self.dsMapId, self.dsPlayerToken)

    elseif seed == EDnsResloveSeed.EnterSafeHouseDS then
        local url = self:GetLevelUrlAsync(ip, port, self.dsMapId, self.dsPlayerToken)
        logerror("[OnDnsReslovedGuide] EDnsResloveSeed.EnterSafeHouseDS, url", url)
        local UGameplayStatics = import "GameplayStatics"
        UGameplayStatics.OpenLevel(GetWorld(), url, true, "")

    elseif seed == EDnsResloveSeed.PlayerJoinMatchGame then
        local url = self:GetLevelUrlAsync(ip, port, self.dsMapId, self.dsPlayerToken)
        logerror("[OnDnsReslovedGuide] EDnsResloveSeed.PlayerJoinMatchGame, url", url)
        import"IrisWorldGamePlayUtils".JoinTheNewHost(GetWorld(), url)

    elseif seed == EDnsResloveSeed.BHDEnterGame then
        if self._dsInfoTimeHandler then
            logerror("[OnDnsReslovedGuide] BHDEnterGame cancel delay timer:", ip , port)
            Timer.CancelDelay(self._dsInfoTimeHandler)
            self._dsInfoTimeHandler = nil
        end
        self:GotoInGame()
        ---@type  ReconnectToBHDArgs
        local args = {
            PlayerID = Server.AccountServer:GetPlayerIdStr(),
            DSIp = ip,
            DSPort = port,
            TokenId = self.dsPlayerToken,
        }
        if DFHD_LUA == 1 then
            local bhdGameController = Facade.GameFlowManager:GetBHDGameController()
            if not bhdGameController:GetIsPreloadBHD() then
                self.Events.evtLaunchUE5:Invoke()
            else
                logerror('[OnDnsReslovedGuide] DFHD_LUA == 1',DFHD_LUA == 1)
            end
        end
        self.Events.evtJoinBHDGame:Invoke(args)
    end
end

function MatchServer:_AddListenIrisReady()
    log("[AddListenIrisReady]...")
    local function OnPlayerEnterIrisReady()
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "gc.ForceCollectGarbage", nil)
        loginfo("MatchServer:OnPlayerEnterIrisReady::无缝入局表演完成,准备连接DS")
        if Facade.ProtoManager:GetDNSResolvedAsync() == true then
            self:_ParseDsInfoAsync(self.dsIpList, self.dsIp, self.dsPort, EDnsResloveSeed.StartMatch)
        else
            self:StartMatch(self.dsIp, self.dsPort, self.dsMapId, self.dsPlayerToken, self.dsDomain, self.dsIpList)
        end
    end

    local GameInst = GetGameInstance()
    UGPGameplayGlobalDelegates.Get(GameInst).OnPlayerEnterIrisReady:Clear()
    UGPGameplayGlobalDelegates.Get(GameInst).OnPlayerEnterIrisReady:Add(OnPlayerEnterIrisReady)
end


function MatchServer:_InitEvents()
    log("MatchServer:_InitEvents")
    self.Events = {
        -- 这几个主流程事件，需要改下配置的模块
        flowEvtPreparationStartMatchSuccess = LuaGameFlowEvent:NewIns("flowEvtPreparationStartMatchSuccess", "Preparation"),
        flowEvtRoomStartMatchSuccess = LuaGameFlowEvent:NewIns("flowEvtRoomStartMatchSuccess", "Room"),
        flowEvtRoomStartMatchFail = LuaGameFlowEvent:NewIns("flowEvtRoomStartMatchFail", "Room"),

        evtMatchReady = LuaEvent:NewIns("evtMatchReady"),
        evtStartMatching = LuaEvent:NewIns("evtStartMatching"),
        -- evtReadyForMatch = LuaEvent:NewIns("evtReadyForMatch"),
        evtPrepareJoinMatch = LuaEvent:NewIns("MatchServer.evtPrepareJoinMatch"),
        evtLobbyJoinMatch = LuaEvent:NewIns("MatchServer.evtLobbyJoinMatch"),
        evtUnreadyForMatch = LuaEvent:NewIns("evtUnreadyForMatch"),
        evtEndMatching = LuaEvent:NewIns("evtEndMatching"),
        evtGotoInGame = LuaEvent:NewIns("evtGotoInGame"),
        evtPlayerDsRoomIdChanged = LuaEvent:NewIns("evtPlayerDsRoomIdChanged"),
        evtSeamlessStartMatchSucceed = LuaEvent:NewIns("MatchServer.evtSeamlessStartMatchSucceed"),
        evtSeamlessStartMatchCanceled = LuaEvent:NewIns("MatchServer.evtSeamlessStartMatchCanceled"),
        evtTdmPlayerArmedForceChanged = LuaEvent:NewIns("evtTdmPlayerArmedForceChanged"),
        evtMatchReport = LuaEvent:NewIns("MatchServer.evtMatchReport"),
        evtMatchRoomMatchEnd = LuaEvent:NewIns("evtMatchRoomMatchEnd"),

        evtMatchInfoUpdate = LuaEvent:NewIns("evtMatchInfoUpdate"),
        evtMatchIdUpdate = LuaEvent:NewIns("evtMatchIdUpdate"),
        evtMatchCampUpdate = LuaEvent:NewIns("evtMatchCampUpdate"),
        evtStopPreShowLoadingView = LuaEvent:NewIns("evtStopPreShowLoadingView"),
        evtMatchReconnect = LuaEvent:NewIns("evtMatchReconnect"),
        evtMatchCannotReconnect = LuaEvent:NewIns("evtMatchCannotReconnect"),
        --用来通知创建主流程选人ui
        evtShowSquadPickWidget = LuaEvent:NewIns("evtShowSquadPickWidget"),
        --用来创建匹配成功ui
        evtShowMatchReadyWidget = LuaEvent:NewIns("evtShowMatchReadyWidget"),
        --用来创建播SOL入局事件视频ui
        evtShowSOLEventVideoWidget = LuaEvent:NewIns("evtShowSOLEventVideoWidget"),
        --无缝入局开始
        evtBeginSeamless = LuaEvent:NewIns("MatchServer.evtBeginSeamless"),
        --实际上飞机
        evtRealBoarding = LuaEvent:NewIns("MatchServer.evtRealBoarding"),
        --gameplayConfig加载完成
        evtGameplayConfigLoaded = LuaEvent:NewIns("MatchServer.evtGameplayConfigLoaded"),
        --matchCheck返回错误码检测重连
        evtMatchCheckTriggerReconnect = LuaEvent:NewIns("MatchServer.evtMatchCheckTriggerReconnect"),


        evtMatchStateChanged = LuaEvent:NewIns("MatchServer.evtMatchStateChanged"),
        evtReEnterSafeHouse = LuaEvent:NewIns("MatchServer.evtReEnterSafeHouse"),
        evtReEnterBattleField = LuaEvent:NewIns("MatchServer.evtReEnterBattleField"),
        evtJoinMatchFailed = LuaEvent:NewIns("MatchServer.evtJoinMatchFailed"),
        evtLaunchUE5 = LuaEvent:NewIns("MatchServer.evtLaunchUE5"), --LaunchUE5
        evtJoinBHDGame = LuaEvent:NewIns("MatchServer.evtJoinBHDGame"), --SendDS

        -- 设置迅游探测地址
        evtUpdateUdpEchoAddress = LuaEvent:NewIns("MatchServer.evtUpdateUdpEchoAddress"),
        evtUpdateAccelAddress = LuaEvent:NewIns("MatchServer.evtUpdateAccelAddress"),
        evtUpdateIdcData = LuaEvent:NewIns("MatchServer.evtUpdateIdcData"),

    }
end

function MatchServer:OnInitServer()
    log("MatchServer:OnInitServer")
    -- self:AddLuaEvent(Server.TeamServer.Events.evtSetIsMatching, self._SetIsMatching, self)
    -- 给客户端的通知显示信息
    Facade.ProtoManager:AddNtfListener("CSNotifyNtf", self._OnCSNotifyNtf, self)
    -- 对局异常退出ntf
    Facade.ProtoManager:AddNtfListener("CSRoomMatchInvalidEndNtf", self._OnCSRoomMatchInvalidEndNtf, self)
    -- 通知拉起本地ds
    Facade.ProtoManager:AddNtfListener("CSLobbyLaunchLocalDsNtf", self._OnCSLobbyLaunchLocalDsNtf, self)
    -- 本地ds模式下连接其他人的ds
    -- Facade.ProtoManager:AddNtfListener("CSLobbyConnectLocalDsNtf", self._OnCSLobbyConnectLocalDsNtf, self)
    -- 开赛包-- 进入局内
    Facade.ProtoManager:AddNtfListener("CSPlayerJoinMatchNtf", self._OnPlayerJoinMatchNtf, self)

    --quarterzeng:下发这一场的随机种子,用于提前判定出生点和无缝入局的能力
    Facade.ProtoManager:AddNtfListener("CSPrepareJoinMatchNtf", self._OnCSPrepareJoinMatchNtf, self)
    -- 大战场配装同步 用于同步队伍配装信息
    Facade.ProtoManager:AddNtfListener("CSMatchRoomTdmChangeArmedForceNtf", self._OnMatchRoomTDMChangeArmedForce, self)
    -- ds异常崩溃
    Facade.ProtoManager:AddNtfListener("CSMatchRoomMatchEndNtf", self._OnMatchRoomMatchEndNtf, self)
    -- 开始带入
    Facade.ProtoManager:AddNtfListener("CSMatchRoomCallNumeralNtf", self._OnMatchRoomCallNumeralNtf, self) 
    -- 无缝入局交互地图的队友标记同步
    Facade.ProtoManager:AddNtfListener("CSMatchRoomSyncMemberOptionNtf", self._OnMatchRoomSyncMemberOptionNtf, self)

    Facade.ProtoManager:AddNtfListener("RobotJoinMatchNtf", self._RobotJoinMatchNtf, self)

    -- UdpPingEvent
    self:AddLuaEvent(Facade.ProtoManager.Events.evtOnUdpPingRecv, self._OnUdpPingRecv, self)
    --网络错误
    self:AddLuaEvent(Facade.ProtoManager.Events.evtOnLuaNetworkFailure, self._OnLuaNetworkFailure, self)
    -- Dns异步解析
    self:AddLuaEvent(Facade.ProtoManager.Events.evtOnDnsAsyncResloved, self._OnDnsAsyncResloved, self)
    -- 重连状态变化
    self:AddLuaEvent(Facade.ProtoManager.Events.evtReconnectResetChange, self._OnReconnectResetChange, self)
    -- BEGIN MODIFICATION - VIRTUOS
    if IsPS5()  then
        local DFMPlatformActivityManager = UDFMPlatformActivityManager.Get(GetWorld())
        if DFMPlatformActivityManager then
            DFMPlatformActivityManager.GetMatchIDDelegate:Add(self._OnGetMatchIdForActivity, self)
            DFMPlatformActivityManager.SetMatchIDDelegate:Add(self._OnSetMatchIdForActivity, self)
            DFMPlatformActivityManager.LeaveMatchDelegate:Add(self.OnLeaveMatchForActivity, self)
        end
    end
    
    -- END MODIFICATION - VIRTUOS
    self:AddLuaEvent(Facade.ProtoManager.Events.evtReconnectUpdateIpInfo, self._UpdateIpListInGame, self)
    -- Ds连接成功
    self:AddLuaEvent(Facade.ProtoManager.Events.evtOnClientConnectDSSuccessNtf, self._OnClientConnectDSSuccessNtf, self)
    self:AddLuaEvent(Facade.ProtoManager.Events.evtOnClientConnectDSFailedNtf, self._OnClientConnectDSFailedNtf, self)
end

function MatchServer:OnDestroyServer()
    log("MatchServer:OnDestroyServer")
    Facade.ProtoManager:RemoveAllNtfListenerByCaller(self)
    -- BEGIN MODIFICATION - VIRTUOS
    if IsPS5()  then
        local DFMPlatformActivityManager = UDFMPlatformActivityManager.Get(GetWorld())
        if DFMPlatformActivityManager then
            DFMPlatformActivityManager.GetMatchIDDelegate:Remove(self._OnGetMatchIdForActivity, self)
            DFMPlatformActivityManager.SetMatchIDDelegate:Remove(self._OnSetMatchIdForActivity, self)
            DFMPlatformActivityManager.LeaveMatchDelegate:Remove(self.OnLeaveMatchForActivity, self)
        end
    end
   
    -- END MODIFICATION - VIRTUOS
    Timer.CancelDelay(self._rollbackTransitionDelayHandle)
end

function MatchServer:_OnMatchStateChange()
end

function MatchServer:TrySwitchLoadingState()
    loginfo("MatchServer:TrySwitchLoadingState")
    local world = GetWorld()
    local enableSeamlessTravel = false
	if world then
		local worldSettings = world:K2_GetWorldSettings()
		if worldSettings then
			enableSeamlessTravel = worldSettings.EnableSeamlessTravel
		end
	end
    
    local matchModeInfo = self:GetMatchModeInfo()
    if matchModeInfo and matchModeInfo.map_id then
        loginfo("MatchServer:TrySwitchLoadingState TargetMapId: ", matchModeInfo.map_id)
        local mapConfig = Facade.TableManager:GetRowByKey("MapConfig", tostring(matchModeInfo.map_id))
        if not mapConfig then
            enableSeamlessTravel = false
            logerror("MatchServer:TrySwitchLoadingState mapconfig is nil")
        elseif mapConfig.LevelName ~= mapConfig.SubWorldName then
            enableSeamlessTravel = false
            loginfo("MatchServer:TrySwitchLoadingState not a SeamlessTravel Level:", mapConfig.LevelName)
        elseif not self:CheckIsEnableSeamless() then
            loginfo("MatchServer:TrySwitchLoadingState, seamlessEntry disabled!")
            enableSeamlessTravel = false
        end
    else
        logerror("MatchServer:TrySwitchLoadingState, matchModeInfo not valid!!!")
        enableSeamlessTravel=false   
    end
    

    if DFHD_LUA == 1 then
        if self._matchingState == EMatchState.WaitForGotoGame then
            -- 屏蔽输入
            local inputMonitor = Facade.UIManager:GetInputMonitor()
            inputMonitor:SetActionsPriorityGate(EDisplayInputActionPriority.UI_Loading, true)
        else
            -- 恢复输入
            local inputMonitor = Facade.UIManager:GetInputMonitor()
            inputMonitor:SetActionsPriorityGate(EDisplayInputActionPriority.UI_Loading, false)
        end
    end

    
    --非无缝
    logerror("MatchServer:TrySwitchLoadingState,enableSeamlessTravel=",enableSeamlessTravel)
    if not enableSeamlessTravel then
        if self:GetIsWaitForGotoGame() then
            -- LuaGlobalEvents.evtStartPreshowLoadingView:Invoke()

        end
    end
end

function MatchServer:_OnCSNotifyNtf(res)
    self.Events.evtMatchReady:Invoke()
    local msg = res.notify_msg
    if not res.duration or res.duration == 0 then
        LuaGlobalEvents.evtServerShowTip:Invoke(msg, 5)
        return
    end
    local duration = res.duration
    LuaGlobalEvents.evtServerShowTip:Invoke(msg, duration)
end

function MatchServer:DoRoomBeginMatch()
    logerror("MatchServer:房间开赛请求")
    local CSRoomBeginMatchTRes = function(res)
        self.Events.evtMatchReport:Invoke("PointReport",{Match = "RoomBeginMatchRes"})
        if not res.result or res.result ~= 0 then
            --LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.MatchBeginFailed .. (res.result or -1))
            Facade.ProtoManager:ManuelHandleErrCode(res)
            logerror("MatchServer: 开赛异常", res.result or -1)
            self.Events.flowEvtRoomStartMatchFail:Invoke(res.result)
            self:EndMatching()
        else
            LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.RoomBeginWaitTips, 5)
            loginfo("MatchServer:准备开始游戏 Waiting Join Match!")
        end
    end
    -- TODO:这个bHasReceivedServer做什么的呢？
    -- if self.bHasReceivedServer == false then
    --     return
    -- end
    self:StartRoomMatching()
    local req = pb.CSRoomBeginMatchTReq:New()
    req.RoomID = Server.RoomServer:GetRoomId()
    local Param = {maxWaitTime = 10, bShowLoading = true,bEnableHighFrequency = true}
    req:Request(CSRoomBeginMatchTRes, Param)
end

---@param ntf pb_CSPrepareJoinMatchNtf
function MatchServer:_OnCSPrepareJoinMatchNtf(ntf)
    local udfmGameGPM = import "DFMGameGPM"
    if udfmGameGPM then
        udfmGameGPM.UpdateGameScene(48,"1")--vivo场景光效
    end

    Facade.UIManager:DisableInput(EInputChangeReason.BusinessPending)
    self:_ProcessSeamlessEntry(ntf)
    self:SetIsReconnect(false)
    DFMGlobalEvents.evtReportEvent:Invoke(EReportCategoryName.Match, EMatchFlow.MatchIng,  EReportStatusCode.Success,  0, "")
    --上报加载的地图类型，SOL还是MP
    local matchModeTable=Facade.TableManager:GetTable("MatchModeDataConfig")
    local matchId=ntf.match_mode_id or 31100003
    local matchModeInfo=matchModeTable[matchId] or {}
    local gameMode=matchModeInfo.GameMode or 0
    if gameMode == 1 then
        DataTableLoader.SetClientGameScene(EDataTableLoaderGameScene.GameScene_SOL,GetWorld())
        --logwarning("testSetClientGameScene SOL PrepareJoinMatch")

    elseif gameMode==2 then
        DataTableLoader.SetClientGameScene(EDataTableLoaderGameScene.GameScene_BattleField,GetWorld())
        --logwarning("testSetClientGameScene BattleField PrepareJoinMatch")

    elseif gameMode == 5 then
        if DFHD_LUA == 1 then
            DataTableLoader.SetClientGameScene(EDataTableLoaderGameScene.GameScene_None,GetWorld())
            loginfo("【BHDHelper】 PrepareJoinMatch")
            local bhdGameController = Facade.GameFlowManager:GetBHDGameController()
            if bhdGameController:GetIsPreloadBHD() then
                self.Events.evtLaunchUE5:Invoke()
            else
                loginfo("【BHDHelper】 PrepareJoinMatch, but not preload BHD")
            end
        else
            logerror("【BHDHelper】 PrepareJoinMatch, but DFHD_LUA == 0")
        end
    else
        DataTableLoader.SetClientGameScene(EDataTableLoaderGameScene.GameScene_None,GetWorld())
    end

    local DFMGameLoadingManager = UDFMGameLoadingManager.GetGameLoadingManager(GetGameInstance())
    if DFMGameLoadingManager and DFMGameLoadingManager.Prepare2Game then
        DFMGameLoadingManager:Prepare2Game() -- 在这里准备入局的一些事项
    end
    DFMGlobalEvents.evtReportEvent:Invoke(EReportCategoryName.Match, EMatchFlow.PrepareJoinMatch, EReportStatusCode.Success, 0, "")
end

function MatchServer:_OnCSRoomMatchInvalidEndNtf(ntf)
    local result = ntf.ds_result
    if result == 0 then
        loginfo("MatchServer:收到ds异常协议的包, 显示正常")
    else
        logerror("MatchServer:ds异常, result =", result)
        LuaGlobalEvents.evtServerShowTip:Invoke("ds异常,错误码为" .. result, 5)
    end
end

-- 客户端启动ds
function MatchServer:_OnCSLobbyLaunchLocalDsNtf(res)
    loginfo("MatchServer:_OnCSLobbyLaunchLocalDsNtf")
    local origin_dsaIP = res.dsa_ip
    local mapId = res.map_id
    -- local dsaIP = MathUtil.ConvertIntToIpAddress(res.dsa_ip)
    local dsaIP = res.dsa_textual_ip
    local dsaPort = res.dsa_port
    local mapName = res.map_name
    --local mapName = "Iris_Entry"
    local dsRoomId = res.room_id
    local outPort = LOCAL_DS_PORT -- 接收客户端数据用的port
    local innerPort = outPort + 1 -- 接收dsa数据用的port
    local subworld = res.sub_world_name
    local projectPath = UBlueprintPathsLibrary.ConvertRelativePathToFull(UBlueprintPathsLibrary.GetProjectFilePath(), "")
    local is_multi_room = res.is_multi_room
    local params = nil
    if is_multi_room then
        params =
            string.format(
            "%s %s -game -server -log ROOM_ID=%d DSA_PORT=%d IN_PORT=%d DSA_IP=%s -multi_room_port=%d",
            projectPath,
            mapName,
            dsRoomId,
            dsaPort,
            innerPort,
            dsaIP,
            outPort
        )
    else
        if subworld and subworld ~= "" then
            -- 当subworld非空时，包含-SUBWORLD=%s
            params = string.format(
                "%s %s -game -server -log ROOM_ID=%d PORT=%d DSA_PORT=%d IN_PORT=%d DSA_IP=%s -SUBWORLD=%s",
                projectPath,
                mapName,
                dsRoomId,
                outPort,
                dsaPort,
                innerPort,
                dsaIP,
                subworld
            )
        else
            -- 当subworld为空时，移除-SUBWORLD部分及其对应参数
            params = string.format(
                "%s %s -game -server -log ROOM_ID=%d PORT=%d DSA_PORT=%d IN_PORT=%d DSA_IP=%s",
                projectPath,
                mapName,
                dsRoomId,
                outPort,
                dsaPort,
                innerPort,
                dsaIP
            )
        end
    end
    local stat = UGPGameplayStatics.StartLocalDS(params)
    logerror("ClientSeamless", "subworld",subworld)
    dump(res)
    loginfo("MatchServer:_OnCSLobbyLaunchLocalDsNtf StartLocalDSEnd","params", params, "stat",tostring(stat))
    self.bUseLocalDs = true
    logwarning("MatchServer:_OnCSLobbyLaunchLocalDsNtf StartLaunchLocalDS","GetWorld",GetWorld(),"dsRoomId",dsRoomId,"mapId",mapId,"mapName",mapName,"origin_dsaIP",origin_dsaIP,"dsaPort",dsaPort,"is_multi_room",is_multi_room)
    UDFMIrisEnterSeamlessGameplayHelper.StartLaunchLocalDS(GetWorld(), 999, "QStage_999_1", 9,dsRoomId,mapId,mapName,origin_dsaIP,dsaPort)
end

function MatchServer:_OnCSLobbyConnectLocalDsNtf(ntf)
    local ip = ntf.ds_textual_ip -- MathUtil.ConvertIntToIpAddress(ntf.ds_ip)
    local port = ntf.ds_port
    loginfo("MatchServer:_OnCSLobbyConnectLocalDsNtf: %s, %d", ip, port)

    Facade.ConfigManager:SetString("lastLocalDSIP", ip)
    Facade.ConfigManager:SetNumber("lastLocalDSPort", port)

    self.dsIp = ip
    self.dsPort = port

    local IsRetOk = UDFMIrisEnterSeamlessGameplayHelper.ReadyToConnectRemoteDS(GetWorld(), 999, "QStage_999_1", 6)
    if not IsRetOk then
        loginfo("MatchServer:_OnCSLobbyConnectLocalDsNtf::无法找到无缝入局逻辑直接Loading连接DS")
        self:StartMatch(self.dsIp, self.dsPort, self.dsMapId, self.dsPlayerToken, self.dsDomain, self.dsIpList)
    end
end


function MatchServer:IsLocalDSDebug()
    if Server.SDKInfoServer:GetServerName() == nil or Server.SDKInfoServer:GetServerName() == "" then
        log("MatchServer:IsLocalDSDebug 缺少SDKInfoServer中的servername")
        return false
    end
    if string.find(Server.SDKInfoServer:GetServerName(), ServerTipCode.MatchLocalDSDebug) == nil then
        return false
    else
        return true
    end
end

function MatchServer:LeaveSafeHouseDS(zero, oldTeamId)
    if Facade.GameFlowManager:GetCurrentGameFlow() ~= EGameFlowStageType.SafeHouse or
        self:GetSafeHouseDSRoomId() == -1 or self:GetSafeHouseDSTeamId() ~= oldTeamId
    then
        log("MatchServer:LeaveSafeHouseDS return, ", 
            self:GetSafeHouseDSRoomId(), " ",
            self:GetSafeHouseDSTeamId(), " ", tostring(zero), " ", tostring(oldTeamId)
        )
        return
    end
    --如果在其他substage等回到安全屋substage时再处理
    local curStage = Facade.GameFlowManager:GetCurrentSubStage()
    if curStage ~= ESubStage.SafeHouse3D then
        self:SetSafeHouseDSState_ToStandalone()
        log("MatchServer:LeaveSafeHouseDS return, SubStage is ", curStage)
        return
    end
    self:SetSafeHouseDSState_Nothing()
    self:ResetSafeHouseDSInfo()
    log("MatchServer:LeaveSafeHouseDS")
    --开启无缝入局
    UDFMIrisEnterSeamlessGameplayHelper.SetClientSeamlessTravelEnable(GetWorld(), true)
    --开启playercontroller的autoswitch
    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        "Game.AutoSwitchPlayerOnSeamlessTravel 1",
        nil
    )
    --走无缝入局
    local UGameplayStatics = import "GameplayStatics"
    UGameplayStatics.OpenLevel(GetWorld(), "Iris_Entry", true, "")
end


function MatchServer:EnterSafeHouseDS(ntf)
    if Facade.GameFlowManager:GetCurrentGameFlow() ~= EGameFlowStageType.SafeHouse or 
        ntf.ds_room_id == self:GetSafeHouseDSRoomId()
    then
        log("MatchServer:EnterSafeHouseDS return ", ntf.ds_room_id, " ", self:GetSafeHouseDSRoomId())
        return
    end
    --如果在其他substage等回到安全屋substage时再处理
    local curStage = Facade.GameFlowManager:GetCurrentSubStage()
    if curStage ~= ESubStage.SafeHouse3D then
        self:SetSafeHouseDSState_ToDS()
        log("MatchServer:EnterSafeHouseDS return, SubStage is ", curStage)
        return
    end
    if not Server.TeamServer:CheckPlayerJoinSafehouseNtfValid(ntf) then
        logerror("MatchServer:EnterSafeHouseDS,travelInfo not valid!!!")
        return
    end
        
    self:SetSafeHouseDSState_Nothing()
    self.dsIp = ntf.ds_textual_ip
    self.dsPort = ntf.ds_port
    self.dsMapId = ntf.map_id
    self.dsPlayerToken = ntf.ds_token
    self.dsDomain = ntf.ds_domain
    self.dsIpList = ntf.ds_ip_list
    self:SetDsRoomId(ntf.ds_room_id)
    self:SetSafeHouseDSRoomId(ntf.ds_room_id)
	self:SetSafeHouseDSTeamId(ntf.sys_team_id)
    Facade.ConfigManager:SetString("lastLocalDSIP", self.dsIp)
    Facade.ConfigManager:SetNumber("lastLocalDSPort", self.dsPort)

    self.Events.evtPlayerDsRoomIdChanged:Invoke() -- 不要紧，刷新Hint
    --开启无缝入局
    UDFMIrisEnterSeamlessGameplayHelper.SetClientSeamlessTravelEnable(GetWorld(), true)
    --开启playercontroller的autoswitch
    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        "Game.AutoSwitchPlayerOnSeamlessTravel 1",
        nil
    )
    --走无缝入局
    if Facade.ProtoManager:GetDNSResolvedAsync() == true then
        self:_ParseDsInfoAsync(self.dsIpList, self.dsIp, self.dsPort, EDnsResloveSeed.EnterSafeHouseDS)
    else
        local url = self:GetLevelUrl(self.dsIp, self.dsPort, self.dsMapId, self.dsPlayerToken, self.dsDomain, self.dsIpList, self.IsOBPlayer)
        log("MatchServer:EnterSafeHouseDS url: ", url)
        local UGameplayStatics = import "GameplayStatics"
        UGameplayStatics.OpenLevel(GetWorld(), url, true, "")
    end
end

function MatchServer:SetGMPickHeroExtraTime(extraTime)
    loginfo("MatchServer:SetGMPickHeroExtraTime", extraTime)
    self._gmPickHeroExtraTime = extraTime
end

function MatchServer:_OnPlayerJoinMatchNtf(ntf)
    if self._gmPickHeroExtraTime and self._gmPickHeroExtraTime > 0 then
        logerror("MatchServer:_OnPlayerJoinMatchNtf, 手动添加选人时间默认为调试模式，不入局")
        return
    end
    --设置OB标记
    if ntf then
        self.matchNtf = ntf
    end
    if ntf.player then
        self.IsOBPlayer = ntf.player.is_observer
        logwarning("MatchServer:_OnPlayerJoinMatchNtf isOB:", self.IsOBPlayer)
    end
    --停掉请求服务器状态的计时器
    if self._matchCheckTimer then
        logwarning("MatchServer:playerJoinMatch _matchCheckTimerStop")
        self._matchCheckTimer:Stop()
    end
    if self._CheckCallNumeralHandel then
        logwarning("MatchServer:playerJoinMatch CheckCallNumeralHandel_CancelDelay")
        Timer.CancelDelay(self._CheckCallNumeralHandel)
        self._CheckCallNumeralHandel=nil
    end
    if Facade.UIManager:GetIsJumpRollbackTransition() then
        local matchModeInfo=self:GetMatchModeInfo()
        local gameMode=matchModeInfo and matchModeInfo.game_mode
        local delayTime=gameMode==MatchGameMode.TDMGameMode and 5 or 3
        self._rollbackTransitionDelayHandle=Timer.DelayCall(delayTime,function()
            Facade.UIManager:CommitTransition(false)
            Facade.UIManager:SetIsJumpRollbackTransition(false)
        end)
    end

    logtable(ntf)
    logwarning("MatchServer:_OnPlayerJoinMatchNtf ","result",ntf.result,"gameFlow",Facade.GameFlowManager:GetCurrentGameFlow())
    if ntf.result~=0 then
        LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.JoinMatchFailed..ntf.result, 3)
        DFMGlobalEvents.evtReportEvent:Invoke(EReportCategoryName.Match, EMatchFlow.PlayerJoinMatch,  EReportStatusCode.Failed,  ntf.result, "")
        self:OnJoinMatchFailed("CSPlayerJoinMatchNtf",ntf.result)
        return
    end
    self.Events.evtMatchReport:Invoke("PointReport",{Match = "PlayerJoinMatch"})
    local curDsRoomId = self:GetDsRoomId()
    if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Game then
        self:_ReportStartMatchTglog(ntf)
    
        --是主机
        if self:GetDsRoomId() == ntf.ds_room_id then
            --什么都不做
            logwarning("MatchServer:_OnPlayerJoinMatchNtf 主机")
        else
            logerror("MatchServer:_OnPlayerJoinMatchNtf() 房间号", ntf.ds_room_id, "中途加入别人的ds")
            self.dsIp = ntf.ds_textual_ip
            self.dsPort = ntf.ds_port
            self.dsMapId = ntf.map_id
            self.dsPlayerToken = ntf.player.ds_token
            self.dsDomain = ntf.ds_domain
            -- MS23新增ds_ip_list,支持下发多个ip列表，通过域名解析获得最佳ip
            self.dsIpList = ntf.ds_ip_list
            self:SetDsRoomId(ntf.ds_room_id)
            self:SetDsTeamId(ntf.player.team_id)
        
            Facade.ConfigManager:SetString("lastLocalDSIP", self.dsIp)
            Facade.ConfigManager:SetNumber("lastLocalDSPort", self.dsPort)
        
            log("MatchServer:_OnPlayerJoinMatchNtf DsRoomId")
            self.Events.evtPlayerDsRoomIdChanged:Invoke() -- 不要紧，刷新Hint
            --直接连
            if Facade.ProtoManager:GetDNSResolvedAsync() == true then
                self:ClearIpListInGame()
                self:_ParseDsInfoAsync(self.dsIpList, self.dsIp, self.dsPort, EDnsResloveSeed.PlayerJoinMatchGame)
            else
                local url = self:GetLevelUrl(self.dsIp, self.dsPort, self.dsMapId, self.dsPlayerToken, self.dsDomain, self.dsIpList, self.IsOBPlayer)
                import "IrisWorldGamePlayUtils".JoinTheNewHost(GetWorld(), url)
            end
        end
    else
        logerror("MatchServer:_OnPlayerJoinMatchNtf() 房间号", ntf.ds_room_id, "判断是否房间开赛")
        -- self.dsIp = MathUtil.ConvertIntToIpAddress(ntf.ds_ip)

        --入局信息安全日志
        self:_ReportStartMatchTglog(ntf)

        --入局结果上报
        local req=pb.CSPlayerJoinDsStatusReq:New()
        req.ds_room_id=ntf.ds_room_id
        req.match_mode_id=ntf.match_mode_id
        req.status=1
        req.fail_reason=4
        req.desc=""
        req:Request()
        logwarning("MatchServer:_OnPlayerJoinMatchNtf CSPlayerJoinDsStatusReq_Request","dsRoomId",req.ds_room_id,"matchModeId",req.match_mode_id,"status",req.status)
        
    
        self.Events.evtLobbyJoinMatch:Invoke()
        self:OnLobbyJoinMatch()

        self.dsDomain = ntf.ds_domain
        self.dsIp = ntf.ds_textual_ip
        self.dsPort = ntf.ds_port
        self.dsMapId = ntf.map_id
        self.dsPlayerToken = ntf.player.ds_token
        self.dsIpList = ntf.ds_ip_list
        self.dsEchoIpList = ntf.xunyou_ip_list
        self:SetDsRoomId(ntf.ds_room_id)
        self:SetDsTeamId(ntf.player.team_id)
        self:SetMatchId(ntf.match_mode_id)

        Facade.ConfigManager:SetString("lastLocalDSIP", self.dsIp)
        Facade.ConfigManager:SetNumber("lastLocalDSPort", self.dsPort)

        log("MatchServer:_OnPlayerJoinMatchNtf DsRoomId",ntf.ds_room_id)
        self.Events.evtPlayerDsRoomIdChanged:Invoke() -- 不要紧，刷新Hint
        if self._fPreEnterGame and self._matchModeInfo.game_mode ~= MatchGameMode.BlackHawkDown then--选人回调，主要处理sol入局(无缝入局动画/非无缝入局播视频) 且 不在BHD模式下
            logerror('MatchServer:OnPlayerJoinMatchNtf:执行self._fPreEnterGame')
            self._fPreEnterGame()
		end
        if self._matchModeInfo and self._matchModeInfo.game_mode == MatchGameMode.BlackHawkDown then
            loginfo('【BHDHelper】recieve PlayerJoinMatchNtf')
            self:_ParseDsInfoAsync(self.dsIpList, self.dsIp, self.dsPort, EDnsResloveSeed.BHDEnterGame) --现在都走这个异步操作，调用这里，在_OnDnsReslovedGuide中监听BHD
            --TODO: BHD，需要设置当前状态为inGame
            return
        end
        if not self:IsSeamlessTravelEnabled() then
            logerror("MatchServer:OnPlayerJoinMatchNtf::无法找到无缝入局逻辑直接Loading连接DS")
            if Facade.ProtoManager:GetDNSResolvedAsync() == true then
                self:ClearIpListInGame()
                self:_ParseDsInfoAsync(self.dsIpList, self.dsIp, self.dsPort, EDnsResloveSeed.StartMatch, self.dsEchoIpList)
            else
                self:StartMatch(self.dsIp, self.dsPort, self.dsMapId, self.dsPlayerToken, self.dsDomain, self.dsIpList)
            end
        else
            logerror("MatchServer:OnPlayerJoinMatchNtf::找到无缝入局逻辑，等待无缝入局发起连接DS!")
            self:ClearIpListInGame()
            UDFMIrisEnterSeamlessGameplayHelper.ReadyToConnectDS(GetWorld(), 999, "QStage_999_1", 5)
        end
    end
end

function MatchServer:IsSeamlessTravelEnabled()
    if not self:CheckIsEnableSeamless() then
        return false
    end 

    if Server.ArmedForceServer:GetCurArmedForceMode() ~= EArmedForceMode.SOL then
        return false
    end

    if  Server.AccountServer:IsInRoom() then
        return false
    end

    local matchInfoMode = self:GetMatchModeInfo()
    if matchInfoMode and matchInfoMode.game_rule == MatchGameRule.ArenaGameRule then
        return false
    end

    return true
end

function MatchServer:CheckIsEnableSeamless()
    loginfo("MatchServer:CheckIsEnableSeamless")
    local DeviceDisableDeviceSeamless = UDFMIrisEnterSeamlessGameplayHelper.GetCVarDisableDeviceSeamless() > 0
    local IsNewPlayerMatchFinished = Server.GuideServer:IsNewPlayerMatchFinished()
    local bForceSkipNewPlayerMatchSeamless = Server.GuideServer:CheckNextForceSkipSeamless()
    loginfo("CheckNextForceSkipSeamless",bForceSkipNewPlayerMatchSeamless ,DeviceDisableDeviceSeamless, IsNewPlayerMatchFinished )

    if DeviceDisableDeviceSeamless or (not IsNewPlayerMatchFinished and bForceSkipNewPlayerMatchSeamless) then
    -- if DeviceDisableDeviceSeamless and IsNewPlayerMatchFinished then
        -- 开启了低配机关闭无缝
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "Game.ForceSetSeamlessType 2", nil)
        -- if disable seamless, show sol map info when loading
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.Loading.EnableSOLMapEntryLoading 1", nil)
    else
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "Game.ForceSetSeamlessType 0", nil)
    end

    -- 前面执行了ExecuteConsoleCommand之后,这个接口会返回SOL到底要不要无缝
    local enableSeamless = UDFMIrisEnterSeamlessGameplayHelper.GetSeamlessEnterEnable(GetWorld()) 
    logerror("MatchServer:CheckIsEnableSeamless ",enableSeamless,"DeviceDisableDeviceSeamless", DeviceDisableDeviceSeamless, " IsNewPlayerMatchFinished", IsNewPlayerMatchFinished)
    return enableSeamless
end

---@class pb_CSMatchRoomTdmChangeArmedForceNtf : ProtoBase
---@field public room_id number
---@field public player_armed_force pb_CSMatchRoomTdmArmedForceShowInfo
---@param ntf pb_CSMatchRoomTdmChangeArmedForceNtf
function MatchServer:_OnMatchRoomTDMChangeArmedForce(ntf)
    logerror("MatchServer:_OnMatchRoomTDMChangeArmedForce RoomId:", ntf.room_id, "PlayerId", ntf.player_armed_force.player_id, "ArmedForceId", ntf.player_armed_force.armedforce_id)
    self.Events.evtTdmPlayerArmedForceChanged:Invoke(ntf.player_armed_force)
end

function MatchServer:_OnMatchRoomCallNumeralNtf(ntf)
    logerror("MatchServer:_OnMatchRoomCallNumeralNtf")
    if not self:GetIsWaitForGotoGame() then
        logwarning("not is wait for goto game")
        return
    end

    logtable(ntf,true)
    self._CheckCallNumeralHandel=Timer.DelayCall(ntf.check_gap,function()
        self:ReqMatchRoomCheckCallNumeral(ntf)
    end)
end

function MatchServer:ReqMatchRoomCheckCallNumeral(ntf)
    logerror("MatchServer:ReqMatchRoomCheckCallNumeral")
    self._CheckCallNumeralHandel=nil
    local fMatchRoomCheckCallNumeralRes=function(res)
        logerror("fMatchRoomCheckCallNumeralRes",res.result)
        if res.result==0 then
            self:_OnPlayerJoinMatchNtf(res.ntf)
        else
            self:OnJoinMatchFailed("CSMatchRoomCheckCallNumeralRes",res.result)
            DFMGlobalEvents.evtReportEvent:Invoke(EReportCategoryName.Error, EReportErrorCode.BringError,  EReportStatusCode.Failed,  res.result, "")
        end

    end
    local matchModeInfo=self:GetMatchModeInfo()
    local gameMode=matchModeInfo.game_mode

    local req=pb.CSMatchRoomCheckCallNumeralReq:New()
    req.ds_room_id=ntf.ds_room_id
    req.player_id=ntf.player_id
    req.match_mode_id=ntf.match_mode_id
    req.game_mode=gameMode
    req:Request(fMatchRoomCheckCallNumeralRes)

end

--入局失败返回大厅
function MatchServer:OnJoinMatchFailed(reasonStr,errorCode,bUseCustomTips)
    logerror("MatchServer:OnJoinMatchFailed",reasonStr,errorCode,bUseCustomTips)
    local fCallback=CreateCallBack(function(self)
        local world = GetWorld()
        local enableSeamlessTravel = false
        if world then
            local worldSettings = world:K2_GetWorldSettings()
            if worldSettings then
                enableSeamlessTravel = worldSettings.EnableSeamlessTravel and self.UseSeamlessTravel
            end
        end
        logerror("MatchServer:OnJoinMatchFailed ","IsenableSeamlessTravel",enableSeamlessTravel,"CurrentGameFlow",Facade.GameFlowManager:GetCurrentGameFlow())
        if Facade.GameFlowManager:GetCurrentGameFlow()==EGameFlowStageType.SafeHouse then
            if enableSeamlessTravel and not Server.AccountServer:IsInRoom() then
                UDFMIrisEnterSubsystem:Get(GetWorld()):ForceStopSeamlessEnter()
            end
            self.Events.evtReEnterSafeHouse:Invoke()
        elseif Facade.GameFlowManager:GetCurrentGameFlow()==EGameFlowStageType.Lobby then
            self.Events.evtReEnterBattleField:Invoke()
        end
        self:EndMatching()
    end,self)

    self.Events.evtJoinMatchFailed:Invoke(errorCode,fCallback,bUseCustomTips)
end

function MatchServer:_OnMatchRoomMatchEndNtf(ntf)
    logerror("MatchServer:_OnMatchRoomMatchEndNtf ntf.reason = ", ntf.reason)
    local gameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    if gameFlow ~= EGameFlowStageType.Game and ntf and ntf.reason ~= 0 and ntf.reason ~= 9 then
        LuaGlobalEvents.evtServerShowTip:Invoke(NSLOCTEXT("ServerTipCode", "Lua_Match_DSCore", "连接异常"))
    end

    self.Events.evtMatchRoomMatchEnd:Invoke()
    local isWaitForGotoGame=self:GetIsWaitForGotoGame()
    if isWaitForGotoGame and (Facade.GameFlowManager:GetCurrentGameFlow()==EGameFlowStageType.SafeHouse or
    Facade.GameFlowManager:GetCurrentGameFlow()==EGameFlowStageType.Lobby) and ntf.ds_room_id==self:GetDsRoomId() then
        self:OnJoinMatchFailed("CSMatchRoomMatchEndNtf",ntf.reason,true)
    end
    DFMGlobalEvents.evtReportEvent:Invoke(EReportCategoryName.Error, EReportErrorCode.DsCore,  EReportStatusCode.Failed,  ntf.result, "")
end

function MatchServer:GetLevelUrl(dsIp, dsPort, mapId, dsToken, domain, dsIpList, IsOBPlayer)
    local modularWeapon = UWeaponAssemblyBPLibrary.GetWeaponGlobals().bModularWeaponEnable and 1 or 0
    local ip , port = self:GetDsInfo(dsIpList, dsIp, dsPort) 
    
    if not ip or not port then
        logerror("MatchServer:GetLevelUrl error! ip or port is nil")
    end

    -- 上报 CLB
    local bFound
    local bEnableSwitchIpAddressDynamic, bFound = UGameplayBlueprintHelper.GPGetConsoleVariableIntValue("dualChannel.EnableSwitchIpAddressDynamic", bFound)
    if bEnableSwitchIpAddressDynamic == 0 then
        LogAnalysisTool.DOSendMatchDsCLB(Server.AccountServer:GetPlayerIdStr(), self:GetDsRoomId(), self.dsLinkDomain , ip, port , 0)
    end

    local url = string.format(
        "%s:%d?PlayerId=%s?ModularWeapon=%s?Cookie=%s?MapId=%s?SpotGroup=%d?DSRoomId=%d?IsOBPlayer=%s",
        ip,
        port,
        Server.AccountServer:GetPlayerIdStr(),
        modularWeapon,
        dsToken,
        mapId,
        Server.IrisSafeHouseServer.irisStartSpot,
        self:GetDsRoomId(),
        IsOBPlayer)
    logwarning("MatchServer:GetLevelUrl url = ", url)
    return url
end

function MatchServer:_ParseHostInfo(hostInfo)
    if hostInfo ~= nil and hostInfo.ds_domain ~= nil then
        logwarning("[ParseHostInfo] ds_domain and ds_ip_info: ", hostInfo.ds_domain, hostInfo.ds_ip_info)
        if #hostInfo.ds_ip_info <= 0 then
            logwarning("[ParseHostInfo] error! check hostInfo.ds_ip_info is Zero")
        end
        local OneSDKModule =  import "DFMGameOneSDK"
        local _OneSDK = OneSDKModule.Get(GetGameInstance())
        local parsedIP = _OneSDK:GetAddressByName(hostInfo.ds_domain)
        logwarning("[ParseHostInfo] parsedIp:", parsedIP)
        if parsedIP and parsedIP ~= "" then
            for _, dsIpInfo in ipairs(hostInfo.ds_ip_info) do
                if MathUtil.GetIPType(parsedIP) == MathUtil.EIPType.IPv6 then
                    logwarning("[ParseHostInfo] parsedIp is ipv6")
                    return "["..parsedIP.."]", dsIpInfo.ds_port
                else
                    if dsIpInfo.ds_textual_ip == parsedIP then
                        logwarning("[ParseHostInfo] Find Ip and Port", dsIpInfo.ds_textual_ip, dsIpInfo.ds_port)
                        return dsIpInfo.ds_textual_ip, dsIpInfo.ds_port
                    end
                end
            end
        end
    end
    logwarning("[ParseHostInfo] not found")
    return nil,nil
end

function MatchServer:GetDsInfo(dsIpList, defaultIP, defaultPort)
    self.dsHostInfo = {}
    if not dsIpList or #dsIpList == 0 then
        logwarning("[GetDsInfo] dsIpList is nil or empty")
        self:SetDsIpAndPort(defaultIP, defaultPort)
        return defaultIP, defaultPort
    end

    for _, hostInfo in ipairs(dsIpList) do
        local ip, port = self:_ParseHostInfo(hostInfo)
        if ip and port then
            local bInBlackList = self:InBlackIpList(ip, port)
            if not bInBlackList then
                -- self:SetDsIpAndPort(ip, port)
                -- return ip, port
                logwarning("[GetDsInfo] ip and port:",ip,port)
                local dsHostInfo = {Ip = ip, Port = port, Domain = hostInfo.ds_domain}
                table.insert(self.dsHostInfo, dsHostInfo)
            end
        end
    end

    self:SetIpAddressList()

    table.insert(self.dsHostInfo, {Ip = defaultIP, Port = defaultPort, Domain = self.dsDomain})
    self.Events.evtUpdateAccelAddress:Invoke(self.dsHostInfo)
    --Module.NetworkBusiness:SetAccelAddressByDsInfo()
    if #self.dsHostInfo > 1 then
        local realHostInfo = self.dsHostInfo[1]
        logwarning("[GetDsInfo] used realHostInfo Ip and Port is", realHostInfo.Ip, realHostInfo.Port)
        self:SetDsIpAndPort(realHostInfo.Ip, realHostInfo.Port)
        return realHostInfo.Ip, realHostInfo.Port
    else
        self:ClearBlackIpList()
        self:SetDsIpAndPort(defaultIP, defaultPort)
        logwarning("[GetDsInfo] used default ip, port")
        return defaultIP, defaultPort
    end
end

function MatchServer:StartMatch(ip, port, mapId, ds_token, domain, dsIpList)
    logerror("MatchServer:StartMatch")
    local DFMGameLoadingManager = import("DFMGameLoadingManager").GetGameLoadingManager(GetGameInstance())
    if DFMGameLoadingManager then
        DFMGameLoadingManager:SetIsReconnect(false);
    end
    self:CatchStartMatchInfo(ip, port, mapId, ds_token, domain, dsIpList)
    if (not self:CheckIsEnableSeamless()) and Server.ArmedForceServer:GetCurArmedForceMode()==EArmedForceMode.SOL and not Server.AccountServer:IsInRoom() then--sol非无缝入局
        self._isReceivePlayerJoinMatch=true
        if not self._isSOLEventVideoPlayEnd then
            logerror("MatchServer:StartMatch","ArmedForceMode",Server.ArmedForceServer:GetCurArmedForceMode(),"IsInRoom",Server.AccountServer:IsInRoom())
            return
        end
    end
    self:DoStartMatch(self:GetStartMatchInfo())
end

function MatchServer:DoStartMatch(startMatchInfo)
    logerror("MatchServer:DoStartMatch")
    DFMGlobalEvents.evtReportEvent:Invoke(EReportCategoryName.Match, EMatchFlow.PlayerJoinMatch,  EReportStatusCode.Success,  0, "")
    if startMatchInfo then
        logtable(startMatchInfo,true)
    end
    startMatchInfo=startMatchInfo or {}
    local ip=startMatchInfo.ip
    local port=startMatchInfo.port
    local mapId=startMatchInfo.mapId
    local ds_token=startMatchInfo.ds_token
    local domain=startMatchInfo.domain
    local dsIpList=startMatchInfo.dsIpList

    Timer.DelayCall(self.matchStartDelay, function()
        local url
        if  Facade.ProtoManager:GetDNSResolvedAsync() == true then
            url = self:GetLevelUrlAsync(ip, port, mapId, ds_token, self.IsOBPlayer)
        else
            url = self:GetLevelUrl(ip, port, mapId, ds_token, domain, dsIpList, self.IsOBPlayer)
        end
        log("MatchServer:DoStartMatch","IsSOL",Server.GameModeServer:IsSOL(),"SubIsPMC",Server.GameModeServer:SubIsPMC(),"SubIsSCAV",Server.GameModeServer:SubIsSCAV())
        if Server.GameModeServer:IsSOL() then
            if Server.GameModeServer:SubIsPMC() then
                url = string.format("%s?Camp=%s", url, "0") -- PMC
                logerror("MatchServer: start match PMC")
            elseif Server.GameModeServer:SubIsSCAV() then
                url = string.format("%s?Camp=%s", url, "3") -- SCAV
                logerror("MatchServer: start match SCAV")
            end
        end

        if not self:IsSeamlessTravelEnabled() then
            -- MP直接关闭无缝
            logerror("MP Close Seamless Travel Force!")
            UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "Game.EnableClientSeamlessTravel 0", nil)
        else
            logerror("IsSOL Match!")
        end

        logerror("MatchServer:DoStartMatch() URL: " .. url)
        if Server.AccountServer:IsInRoom() then
            self.Events.flowEvtRoomStartMatchSuccess:Invoke(url)
        elseif Server.AccountServer:IsInTeam() or Server.AccountServer:IsInIdle() then
            self.Events.flowEvtPreparationStartMatchSuccess:Invoke(url)
        else
            log("MatchServer:DoStartMatch AccountServer.PlayerState 值异常",Server.AccountServer:GetPlayerState())
        end

        Facade.SoundManager:StopBGM() -- TODO:应该在主流程的下个步骤开始后处理
        self:GotoInGame()
        self.matchStartDelay = 0
    end)
end

function MatchServer:CatchStartMatchInfo(ip, port, mapId, ds_token, domain, dsIpList)
    loginfo("MatchServer:CatchStartMatchInfo",ip, port, mapId, ds_token, domain)
    if dsIpList then
        logtable(dsIpList,true)
    end
    self._startMatchInfo={
        ip=ip,
        port=port,
        mapId=mapId,
        ds_token=ds_token,
        domain=domain,
        dsIpList=dsIpList,

    }
end

function MatchServer:GetStartMatchInfo()
    loginfo("MatchServer:GetStartMatchInfo")
    return self._startMatchInfo
end

-- 定义行为
-- 匹配成功进入局内
function MatchServer:GotoInGame()
    log("MatchServer:GotoInGame")
    self.Events.evtMatchReport:Invoke("PointReport",{Match = "GotoInGame"})
    self:_SetMatchState(EMatchState.InGame)
    self.Events.evtGotoInGame:Invoke(self.dsMapId)
end

function MatchServer:StartRoomMatching()
    log("MatchServer:StartRoomMatching")
    self.Events.evtMatchReport:Invoke("PointReport",{Match = "StartRoomMatching"})

    self:_SetMatchState(EMatchState.Matching)

    --rannliu仅做能否开启无缝演绎的检查,不涉及演绎流程的预热与启动
    self.UseSeamlessTravel = UDFMIrisEnterSeamlessGameplayHelper.StartMatchCheck(GetWorld(),999,"QStage_999_1",1,"NONE",Server.AccountServer:IsInRoom())
end

function MatchServer:UpdateSeamlessTravel()
    log("MatchServer:UpdateSeamlessTravel")
    
    --rannliu仅做能否开启无缝演绎的检查,不涉及演绎流程的预热与启动
    self.UseSeamlessTravel = UDFMIrisEnterSeamlessGameplayHelper.StartMatchCheck(GetWorld(),999,"QStage_999_1",1,"NONE",Server.AccountServer:IsInRoom())
end

-- 开始matching
function MatchServer:StartMatching(nodeId)
    --清理掉matchCheck错误码拉起的stateCheck
    if self._checkStateTimer then
        self._checkStateTimer:Stop()
        self._checkStateTimer=nil
    end

    -- quarterzeng 将低配机型判断提前,不然会出现,演绎播了一半,然后走loading了
    local DeviceDisableDeviceSeamless = UDFMIrisEnterSeamlessGameplayHelper.GetCVarDisableDeviceSeamless() > 0
    local IsNewPlayerMatchFinished = Server.GuideServer:IsNewPlayerMatchFinished() 
    if DeviceDisableDeviceSeamless and IsNewPlayerMatchFinished then
        -- 开启了低配机关闭无缝
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "Game.ForceSetSeamlessType 2", nil)
        -- if disable seamless, show sol map info when loading
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.Loading.EnableSOLMapEntryLoading 1", nil)
    else
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "Game.ForceSetSeamlessType 0", nil)
    end

    self.Events.evtMatchReport:Invoke("PointReport",{Match = "StartMatching"})
    log("MatchServer:StartMatching")
    self:_SetMatchState(EMatchState.Matching)
    self.Events.evtStartMatching:Invoke(self.dsMapId)

    --rannliu仅做能否开启无缝演绎的检查,不涉及演绎流程的预热与启动
    self.UseSeamlessTravel = UDFMIrisEnterSeamlessGameplayHelper.StartMatchCheck(GetWorld(),999,"QStage_999_1",1,Server.GameModeServer:GetLevelName(),Server.AccountServer:IsInRoom())
    self.Events.evtSeamlessStartMatchSucceed:Invoke(1)
end

-- 异常停止matching
function MatchServer:EndMatching(MatchSuccess)
    --停掉请求服务器状态的计时器
    if self._matchCheckTimer then
        logwarning("MatchServer:EndMatching _matchCheckTimerStop")
        self._matchCheckTimer:Stop()
    end

    self.Events.evtMatchReport:Invoke("PointReport",{Match = "EndMatching"})

    log("MatchServer:EndMatching",MatchSuccess)
    if MatchSuccess then
        self:_SetMatchState(EMatchState.WaitForGotoGame)
    else
        self:_SetMatchState(EMatchState.InIdle)
    end
    self.Events.evtEndMatching:Invoke()

    self.Events.evtSeamlessStartMatchCanceled:Invoke(1)
end

function MatchServer:GetMatchState()
    log("MatchServer:GetMatchState",matchStatetoString(self._matchingState))
    return self._matchingState
end

function MatchServer:GetIsInGame()
    log("MatchServer:GetIsInGame",self._matchingState == EMatchState.InGame)
    return self._matchingState == EMatchState.InGame
end


function MatchServer:GetIsMatching()
    log("MatchServer:GetIsMatching",self._matchingState == EMatchState.Matching)
    return self._matchingState == EMatchState.Matching
end

function MatchServer:GetIsIdle()
    log("MatchServer:GetIsIdle",self._matchingState == EMatchState.InIdle)
    return self._matchingState == EMatchState.InIdle
end

function MatchServer:GetIsWaitForGotoGame()
    log("MatchServer:GetIsWaitForGotoGame",self._matchingState == EMatchState.WaitForGotoGame)
    return self._matchingState == EMatchState.WaitForGotoGame
end

--是否是不可操作装备状态(准备或匹配中)
function MatchServer:GetIsReadytoGo()
    if not Facade.GameFlowManager:CheckIsInFrontEnd()then
        return false
    end
    local isWaitForGotoGame=Server.MatchServer:GetIsWaitForGotoGame()
    local isMatching=Server.MatchServer:GetIsMatching()
    local isReady=Server.TeamServer:IsInTeam() and Server.TeamServer:IsReady() and Server.TeamServer:IsMember()
    log("MatchServer:GetIsReadytoGo","isWaitForGotoGame",isWaitForGotoGame,"isMatching",isMatching,"isReady",isReady)
    local readyState=0
    if isWaitForGotoGame or isMatching then
        readyState=1
    elseif isReady then
        readyState=2
    end
    return isWaitForGotoGame or isMatching or isReady, readyState
end

function MatchServer:_SetMatchState(state)
    log("MatchServer:_SetMatchState",matchStatetoString(state))
    self._matchingState = state
    self.Events.evtMatchStateChanged:Invoke(state)
    self:_OnMatchStateChange()
end

function MatchServer:LogMatchState()
    log("MatchServer:LogMatchState",self._matchingState)
end

function MatchServer:_SetIsMatching(bMatchingState)
    log("MatchServer:_SetIsMatching",bMatchingState)
    if bMatchingState then
        self:StartMatching()
    else
        self:EndMatching()
    end
end

function MatchServer:ReqReconnectMatch(bReconnect,fCallback)
    logerror("MatchServer:ReqReconnectMatch",bReconnect)
    local fOnCSMatchRoomReconnectRes = function(res)
        if fCallback then
            fCallback(res)
        end
    end
    local req = pb.CSMatchRoomReconnectReq:New()
    req.is_reconnect = bReconnect
    req.room_id = 0--这个字段后台可以舍弃，先填个0

    req:Request(fOnCSMatchRoomReconnectRes, {bHighFrequency = true})
end

function MatchServer:ReqReconnectPickHeroStage()--拉取匹配成功信息
    log("MatchServer:ReqReconnectPickHeroStage")
    local OnCSStateGetInfoRes = function(res)
        log("MatchServer:ReqReconnectPickHeroStage CSStateGetInfoRes",res.result)
        logtable(res,true)
        if res.result==0 then
            local dsRoomId=res.DsRoomID

            local fOnCSMatchRoomPickStageReconnectRes=function(res)
                log("MatchServer:ReqReconnectPickHeroStage fOnCSMatchRoomPickStageReconnectRes",res.result)
                if res.result==0 then
                    --rannliu仅做能否开启无缝演绎的检查,不涉及演绎流程的预热与启动
                    self.UseSeamlessTravel = UDFMIrisEnterSeamlessGameplayHelper.StartMatchCheck(GetWorld(),999,"QStage_999_1",1,Server.GameModeServer:GetLevelName(),Server.AccountServer:IsInRoom())
                    self:SetDsRoomId(res.ds_room_id)
                    self:SetDsTeamId(res.team_id)
                    self:_ProcessSeamlessEntry(res,true)
                end
                
            end
            local req=pb.CSMatchRoomPickStageReconnectReq:New()
            req.ds_room_id=dsRoomId
            req:Request(fOnCSMatchRoomPickStageReconnectRes)
        end
        
    end

    Server.AccountServer:GetStateInfo(OnCSStateGetInfoRes)
    
end

function MatchServer:_ProcessSeamlessEntry(ntf,bSkipMatchReadyAnim,bSkipPickHeroStage)
    --停掉请求服务器状态的计时器
    if self._matchCheckTimer then
        logwarning("匹配成功 _matchCheckTimerStop")
        self._matchCheckTimer:Stop()
    end

    self:ClearBlackIpList()
    log("MatchServer:_OnCSPrepareJoinMatchNtf","selfId",Server.AccountServer:GetPlayerId())
    logerror("MatchServer:收到了PrepareJoinMatch")
    self.Events.evtMatchReport:Invoke("PointReport",{Match = "PreparePlayerJoinMatch"})
    logtable(ntf)
    -- uint64 ds_room_id = 1;
    -- int64 time_stamp = 2;   // 拉起时间
    -- uint64 player_id = 3;
    -- uint64 team_id = 4;
    -- int64 random_seed = 5;  // 随机种子, 用于预加载出生点

    -- 缓存一下ds room id，为了交互地图的队友同步
    self.DSRoomIDForSync = ntf.ds_room_id
    logwarning("[SyncMapSign] _OnCSPrepareJoinMatchNtf, DSRoomIDForSync=", self.DSRoomIDForSync)

    local MatchInfo = FPlayerFrontEndMatchInfo()
    MatchInfo.ds_room_id = ntf.ds_room_id
    MatchInfo.time_stamp = ntf.time_stamp
    MatchInfo.player_id = ntf.player_id
    MatchInfo.team_id = ntf.team_id
    MatchInfo.random_seed = ntf.random_seed
    MatchInfo.player_idx = ntf.player_idx
    MatchInfo.spawn_point_config_id = ntf.spawn_point_config_id
    MatchInfo.MainMode = Server.GameModeServer:GetMainMode()
    MatchInfo.SubMode = Server.GameModeServer:GetSubMode()

    if ntf.tod_weather_id then
        -- 缓存一下天气
        MatchInfo.tod_weather_id = ntf.tod_weather_id
        logwarning("[UDFMTODSubSystem] MatchServer:OnCSPrepareJoinMatchNtf, tod_weather_id=", ntf.tod_weather_id)
    else
        logerror("[UDFMTODSubSystem] MatchServer:OnCSPrepareJoinMatchNtf tod_weather_id null")
    end

    local TeamMemberIdArray = MatchInfo.TeamMemberIds

    -- 直接在收到的时候预热玩家昵称，此时是局外，性能压力没这么大
    local UFontStyleRuntimeManager = import("FontStyleRuntimeManager").Get()
    local nickNamePresetStyle = {"Body2_24pt", "Body0_20pt", "Body4_28pt", "Header4_32pt"} -- 昵称的预设字体样式名

    for index, value in ipairs(ntf.room_member_infos) do
        local TeamMemberIndentity = FTeamMemberIdentity()
        TeamMemberIndentity.PlayerUin = value.player_id
        TeamMemberIndentity.TeamId = value.team_id
        TeamMemberIndentity.PlayerIdx = value.player_idx
        TeamMemberIdArray:Add(TeamMemberIndentity)

        if (value.player_id == ntf.player_id) then
            -- 只关注自己的攻守方信息就好
            self.Events.evtMatchCampUpdate:Invoke(value.camp)
        end

        -- 有这个字段且不为空
        if value.nick_name and value.nick_name ~= "" then
            if UFontStyleRuntimeManager then
                for k, v in ipairs(nickNamePresetStyle) do
                    UFontStyleRuntimeManager:WarmupText(v, value.nick_name) -- 预热昵称
                end
            end
        end
    end
    MatchInfo.TeamMemberIds = TeamMemberIdArray
    for index, value in ipairs(ntf.box_event) do
        MatchInfo.MandelSpawnerIDArray:Add(value)
    end
    if ntf.match_mode_id then
        self.Events.evtMatchIdUpdate:Invoke(ntf)
    end
    self:SetDsRoomId(ntf.ds_room_id)
    self:SetDsTeamId(ntf.team_id)
    self:SetMatchId(ntf.match_mode_id)
    self.Events.evtPrepareJoinMatch:Invoke(ntf.ds_room_id, ntf.team_id)
    logerror("MatchServer:OnCSPrepareJoinMatchNtf() 房间号",ntf.ds_room_id,",拉起时间:",ntf.time_stamp,",player_id:",ntf.player_id,",team_id:",ntf.team_id,",random_seed:",ntf.random_seed,",course_id:",ntf.iris_event_course_id)
    local EventIds = slua.Array(EPropertyClass.Str)
	local EventIdsUInt64 = slua.Array(EPropertyClass.UInt64)
	
    for index, value in ipairs(ntf.iris_event_ids) do
        local EventIdStr = ULuautils.GetUInt64String(value)
        logerror("MatchServer:OnCSPrepareJoinMatchNtf() iris_event_id:", EventIdStr)
        EventIds:Add(EventIdStr)
		
		EventIdsUInt64:Add(value)
    end
	MatchInfo.EventIds = EventIdsUInt64
	
    --检查下是否启用无缝入局
    local world = GetWorld()
    local enableSeamlessTravel = false
	if world then
		local worldSettings = world:K2_GetWorldSettings()
		if worldSettings then
            logerror("MatchServer:OnCSPrepareJoinMatchNtf()","EnableSeamlessTravel",worldSettings.EnableSeamlessTravel,"UseSeamlessTravel",self.UseSeamlessTravel)
			enableSeamlessTravel = worldSettings.EnableSeamlessTravel and self.UseSeamlessTravel
		end
	end
    self._bSeamlessEntryPromiseCall = false
    self._bSeamlessEntrySquadPickFinish = false;
    logerror("MatchServer:OnCSPrepareJoinMatchNtf(), set _bSeamlessEntryPromiseCall:", self._bSeamlessEntryPromiseCall, ", set _bSeamlessEntrySquadPickFinish:", self._bSeamlessEntrySquadPickFinish)
    if not (self._matchModeInfo and self._matchModeInfo.game_mode == MatchGameMode.BlackHawkDown) then
        local mapId = Server.GameModeServer:GetMapID()
        UDFMIrisEnterSubsystem:Get(GetWorld()).CurrentEnterMapID = mapId or 0;
        self:RequestAcceptedQuests();
        self:RequestAcceptedActivity();
        self:GetAllKeys()
    end

    Server.MatchServer:EndMatching(true)
    if self._matchModeInfo and self._matchModeInfo.game_mode == MatchGameMode.BlackHawkDown then
        logerror('MatchServer:_ProcessSeamlessEntry BHD mode return loadLevel，LoadGameplayConfig，UI等操作')
        return
    end
    local gameplayConfigLevelLoadPromise = Promise:NewIns(function()end)
    gameplayConfigLevelLoadPromise:Then(function(fCallback)
        logwarning("MatchServer:_OnCSPrepareJoinMatchNtf gameplayConfigLevelLoadPromise")
        local seamlessEntryPromise = Promise:NewIns(function()end)
        seamlessEntryPromise:Then(function()
            
            --隐藏所有hud
            local hudStateManager = UE.HUDStateManager.Get(GetWorld())
            hudStateManager:AddState(UE.GameHUDSate.GHS_GlobalHideAllHUD, false)

            logwarning("MatchServer:_OnCSPrepareJoinMatchNtf seamlessEntryPromise")
            -- 先用CVar指令指定
            -- forceIntroEvent = UDFMIrisEnterSeamlessGameplayHelper.GetCVarForceEnableIntroEventSOL()
            if enableSeamlessTravel then
                local matchCount = Server.GuideServer:GetMatchCount(MatchGameRule.SOLGameRule)
                UDFMIrisEnterSubsystem:Get(GetWorld()).CurrentPlayedMatchNum = matchCount
                self.UseSeamlessTravel = UDFMIrisEnterSubsystem:Get(GetWorld()).bCanUseSeamlessTravelAfterLoadGameplayConfig
                UDFMIrisEnterSeamlessGameplayHelper.RealMatchSucceed(GetWorld(),999,"QStage_999_1",3)
                LogAnalysisTool.DoSendBeginSeamlessLog()

                --临时处理内存峰值的代码
                if self.UseSeamlessTravel then
                    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "OpenWorld.PendingUnloadTimeLimit 0.01", nil)

                    Facade.LevelLoadManager:AsyncOperateStreamLevelsBySubStage(ESubStage.HallMain, false, nil, nil)

                    Timer.DelayCall(1,function()
                        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "gc.ForceCollectGarbage", nil)
                    end)
                    Timer.DelayCall(4,function()
                        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "gc.ForceCollectGarbage", nil)
                    end)
                end
            end

            self._bSeamlessEntryPromiseCall = true
            logerror("MatchServer:OnCSPrepareJoinMatchNtf(), set _bSeamlessEntryPromiseCall:", self._bSeamlessEntryPromiseCall, ", _bSeamlessEntrySquadPickFinish:", self._bSeamlessEntrySquadPickFinish)
            if self._bSeamlessEntrySquadPickFinish then
                local SwitchResult = UDFMIrisEnterSubsystem:Get(GetWorld()):TrySwitchNextStage()
                Server.MatchServer.Events.evtRealBoarding:Invoke()
                logwarning("MatchServer:OnCSPrepareJoinMatchNtf(), TrySwitchNextStage in gameplayconfig callback, result=", SwitchResult)
            end
        end)

        Timer.DelayCall(0.01, function() 
            seamlessEntryPromise:Resolve()
            self.Events.evtGameplayConfigLoaded:Invoke()
            if fCallback then
                fCallback()
            end
        end)

    end)

    local fLoadGameplayConfig=function(fCallback)
        logwarning("MatchServer:_OnCSPrepareJoinMatchNtf LoadGameplayConfig")
        local GameplayConfigLoadedCallback = function ()
            logwarning("MatchServer:_OnCSPrepareJoinMatchNtf Do GameplayConfigLoadedCallback")
            if self._gameplayConfigLoadCompleteHandle then
                self._gameplayConfigLoadCompleteHandle=UDFMIrisEnterSubsystem:Get(GetWorld()).OnGameplayConfigLoadCompleteEvent:Remove(self._gameplayConfigLoadCompleteHandle)
            end
            fCallback=type(fCallback)=="function" and fCallback--需要判断一下类型，因为从squadPick回调过来传的是uiIns
            gameplayConfigLevelLoadPromise:Resolve(fCallback)
        end
        local GameplayConfigLoaded = UDFMIrisEnterSubsystem:Get(GetWorld()).bGameplayConfigLoadComplete
        if GameplayConfigLoaded or not enableSeamlessTravel then
            logwarning("MatchServer:_OnCSPrepareJoinMatchNtf GameplayConfigLoadedCallback")
            GameplayConfigLoadedCallback()
        else
            logwarning("MatchServer:_OnCSPrepareJoinMatchNtf OnGameplayConfigLoadCompleteEvent")
            self._gameplayConfigLoadCompleteHandle=UDFMIrisEnterSubsystem:Get(GetWorld()).OnGameplayConfigLoadCompleteEvent:Add(GameplayConfigLoadedCallback)
        end
    end

    if Server.AccountServer:IsInRoom() then
        bSkipMatchReadyAnim=true
    end
    if ntf.is_observer then
        bSkipMatchReadyAnim=true
        bSkipPickHeroStage=true
    end
    UGPGameplayStatics.LoadTODLevel(GetWorld())
    self._fPreEnterGame=nil

    if not Server.AccountServer:IsInRoom() and Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse then
        local needRollbackTransition=false
        local fPopAllUI = function()
            logerror("MatchServer:OnCSPrepareJoinMatchNtf, PopAllUI")
            --弹出所有UI
            Facade.UIManager:PopAllUI(true, true, MapPopAllUIReason2Str.PrepareJoinMatchNtf)
            Facade.UIManager:CloseAllPopUI()
        end
        local fSwitchEnterStage = function()
            fPopAllUI()
            if needRollbackTransition and Facade.UIManager:GetIsJumpRollbackTransition() then
                Facade.UIManager:CommitTransition(false)
                Facade.UIManager:SetIsJumpRollbackTransition(false)
            end
            Server.MatchServer.Events.evtBeginSeamless:Invoke()
            self._bSeamlessEntrySquadPickFinish = true
            logwarning("MatchServer:OnCSPrepareJoinMatchNtf, SquadPick Finish, SeamlessEntryPromiseCall=", self._bSeamlessEntryPromiseCall)
            if self._bSeamlessEntryPromiseCall then
                local SwitchResult = UDFMIrisEnterSubsystem:Get(GetWorld()):TrySwitchNextStage()
                Server.MatchServer.Events.evtRealBoarding:Invoke()
                logwarning("MatchServer:OnCSPrepareJoinMatchNtf(), TrySwitchNextStage, in squad pick finish, result=", SwitchResult)
            end
            Facade.GameFlowManager:EnterSubStage(ESubStage.None) -- 清理SubStage
        end
        local fInternalPreEnterGame=CreateCallBack(function(self)
            loginfo("MatchServer:OnCSPrepareJoinMatchNtf, fInternalPreEnterGame")

            if self:IsSeamlessTravelEnabled() then
                fLoadGameplayConfig(fSwitchEnterStage)
            else
                local fPlayEndCallback=CreateCallBack(function(self)
                    Facade.UIManager:CommitTransition(true) -- 添加遮罩转场
                    if self._isPlayEndCalledOnce then
                        logerror("MatchServer:OnCSPrepareJoinMatchNtf, fPlayEndCallback is called once!!!")
                        return
                    end
                    self._isPlayEndCalledOnce=true

                    logerror("MatchServer:OnCSPrepareJoinMatchNtf, fPlayEndCallback")
                    self._isSOLEventVideoPlayEnd=true
                    if self._isReceivePlayerJoinMatch then
                        self:DoStartMatch(self:GetStartMatchInfo())
                    else
                        self:TrySwitchLoadingState()
                    end
                    if self._fPlayEndDelayHandle then
                        Timer.CancelDelay(self._fPlayEndDelayHandle)
                        self._fPlayEndDelayHandle=nil
                    end
                end,self)
                local fOnGetTotalTime=CreateCallBack(function(self,totalTime)--兜底逻辑
                    loginfo("MatchServer:OnCSPrepareJoinMatchNtf, fOnGetTotalTime",totalTime)
                    self._fPlayEndDelayHandle=Timer.DelayCall(totalTime+2,fPlayEndCallback)
                end,self)
                fPopAllUI()
                self._isPlayEndCalledOnce=false
                self.Events.evtShowSOLEventVideoWidget:Invoke(ntf.iris_event_ids,fPlayEndCallback,fOnGetTotalTime)
            end
        end,self)
        local fOnAnimEnd = CreateCallBack(
            function(self,solRoomTeamRes)
                loginfo("MatchServer:OnCSPrepareJoinMatchNtf, OnAnimEnd")
                if not bSkipMatchReadyAnim and not self._showSquadPickDelayHandle then
                    logerror("MatchServer:OnCSPrepareJoinMatchNtf, On CallBack, showSquadPickFunc is called once!!!")
                    return
                end
                Timer.CancelDelay(self._showSquadPickDelayHandle)
                self._showSquadPickDelayHandle=nil
                fPopAllUI()
                --rannliu(此时在兵种选择界面中)开始无缝演绎的资源预热 [这段代码非常重要,直接决定了无缝演绎能否拉起,如果重构别漏了这一行]
                self:_StartMatchSucceed(MatchInfo,EventIds,ntf)
                --rannliu
                Facade.UIManager:EnableInput(EInputChangeReason.BusinessPending)
                Facade.UIManager:ResetAutoApplyThemeID()
                self.Events.evtShowSquadPickWidget:Invoke(nil, nil, ntf.ds_room_id,solRoomTeamRes,self:CheckIsEnableSeamless())
            end,
        self)
        -- 是新手关的话，跳过选角
        if Server.GuideServer:IsNewPlayerGuiding() and not Server.GuideServer:IsNewPlayerMatchFinished() then
            bSkipMatchReadyAnim=true
            bSkipPickHeroStage=true
        end
        self._isSOLEventVideoPlayEnd=false
        self._isReceivePlayerJoinMatch=false

        local fGetSolRoomTeamRes=function(res)
            if res.result==0 then
                if not bSkipMatchReadyAnim then
                    self.Events.evtShowMatchReadyWidget:Invoke(fOnAnimEnd,res)
                    self._showSquadPickDelayHandle=Timer.DelayCall(3,function()
                        loginfo("MatchServer:OnCSPrepareJoinMatchNtf, Call DelayFunction fOnAnimEnd")
                        fOnAnimEnd(res)
                    end)
                else
                    fOnAnimEnd(res)
                end
                self._fPreEnterGame=function()
                    logerror("MatchServer:OnCSPrepareJoinMatchNtf, fPreEnterGame SOL")
                    fInternalPreEnterGame()
                end
            else
                logerror("MatchServer:OnCSPrepareJoinMatchNtf, fGetSolRoomTeamRes errorCode",res.result)
                Server.ArmedForceServer.Events.evtAssemblyCardDisplayOnShow:Invoke()--切PerfGear
                self:_StartMatchSucceed(MatchInfo,EventIds,ntf)
                local fShowMatchReadyCallback=function()
                    fPopAllUI()
                    Facade.UIManager:CommitTransition(true, 1, 99)
                    Facade.UIManager:SetIsJumpRollbackTransition(true)
                    needRollbackTransition=true
                    fInternalPreEnterGame()
                end
                self.Events.evtShowMatchReadyWidget:Invoke(fShowMatchReadyCallback)
            end
        end
        if not bSkipMatchReadyAnim or not bSkipPickHeroStage then
            Server.ArmedForceServer:ReqGetSolRoomTeam(ntf.ds_room_id,fGetSolRoomTeamRes)
        else
            fPopAllUI()
            -- [aidenliao] 新手无缝入局的时候没有界面，需要主动设置一次PerfGear
            Server.ArmedForceServer.Events.evtAssemblyCardDisplayOnShow:Invoke()
            fInternalPreEnterGame()
        end
        
    else
        local fPopAllUI = function()
            logerror("MatchServer:OnCSPrepareJoinMatchNtf, PopAllUI")
            --弹出所有UI
            Facade.UIManager:PopAllUI(true, true, MapPopAllUIReason2Str.PrepareJoinMatchNtf)
            Facade.UIManager:CloseAllPopUI()
        end
        local fSwitchLoading=function()
            loginfo("MatchServer:OnCSPrepareJoinMatchNtf, fSwitchLoading")

            Server.MatchServer:TrySwitchLoadingState()
        end
            
        local fOnDelayEnd=CreateCallBack(function(self,solRoomTeamRes)
            loginfo("MatchServer:OnCSPrepareJoinMatchNtf, OnDelayEnd")
            if not bSkipMatchReadyAnim and not self._showSquadPickDelayHandle then
                logerror("MatchServer:OnCSPrepareJoinMatchNtf, On CallBack, showSquadPickFunc is called once!!!")
                return
            end
            Timer.CancelDelay(self._showSquadPickDelayHandle)
            self._showSquadPickDelayHandle=nil

            fPopAllUI()
            Facade.UIManager:EnableInput(EInputChangeReason.BusinessPending)
            Facade.UIManager:ResetAutoApplyThemeID()
            self.Events.evtShowSquadPickWidget:Invoke(nil, nil, ntf.ds_room_id,solRoomTeamRes)
        end,self)
        
        
        local fGetSolRoomTeamRes=function(res)
            loginfo("MatchServer:OnCSPrepareJoinMatchNtf, fGetSolRoomTeamRes",res.result)
            if res.result==0 then
                if not bSkipMatchReadyAnim then
                    self.Events.evtShowMatchReadyWidget:Invoke(fOnDelayEnd,res)
                    self._showSquadPickDelayHandle=Timer.DelayCall(3,function()
                        loginfo("MatchServer:OnCSPrepareJoinMatchNtf, Call DelayFunction fOnDelayEnd")
                        fOnDelayEnd(res)
                    end)
                else
                    fOnDelayEnd(res)
                end
                
            else
                logerror("MatchServer:OnCSPrepareJoinMatchNtf, fGetSolRoomTeamRes errorCode",res.result)
                local fShowMatchReadyCallback=function()
                    fPopAllUI()
                    Facade.UIManager:CommitTransition(true, 1, 99)
                    Facade.UIManager:SetIsJumpRollbackTransition(true)
                end
                self.Events.evtShowMatchReadyWidget:Invoke(fShowMatchReadyCallback)
            end
            self._fPreEnterGame=function()
                logerror("MatchServer:OnCSPrepareJoinMatchNtf, fPreEnterGame")
                fPopAllUI()
                fSwitchLoading()
            end
            
        end
        if not bSkipMatchReadyAnim or not bSkipPickHeroStage then
            Server.ArmedForceServer:ReqGetSolRoomTeam(ntf.ds_room_id,fGetSolRoomTeamRes)
        else
            fPopAllUI()
            --ob位用转场拖过选人时间
            Facade.UIManager:CommitTransition(true, 1, 99)
            Facade.UIManager:SetIsJumpRollbackTransition(true)
        end
    end
end

---------------------------------------------------------------------------------
--- MP
---------------------------------------------------------------------------------
function MatchServer:DoMatchRoomSetTdmRoomArmedForceReq(roomId, bagId)
    log("MatchServer:DoMatchRoomSetTdmRoomArmedForceReq", roomId, bagId)
    
    
end

function MatchServer:GetMatchModeInfo()
    log("MatchServer:GetMatchModeInfo")
    return self._matchModeInfo
end

function MatchServer:ConvertIPv4ToIPv6(ipv4)
    local ipv6Prefix = "0000:0000:0000:0000:0000:ffff:"
    local ipv4Parts = {}
    for part in string.gmatch(ipv4, "%d+") do
      table.insert(ipv4Parts, tonumber(part))
    end

    if #ipv4Parts ~= 4 then
      return nil -- 无效的IPv4地址
    end

    local ipv6Suffix = string.format("%02x%02x:%02x%02x", ipv4Parts[1], ipv4Parts[2], ipv4Parts[3], ipv4Parts[4])
    return ipv6Prefix .. ipv6Suffix
end

function MatchServer:SetMatchModeInfo(mode)
    loginfo("MatchServer:SetMatchModeInfo game_mode=", mode and mode.game_mode)
    logtable(mode,true)
    self._matchModeInfo = mode
    self.Events.evtMatchInfoUpdate:Invoke(mode)
end

--请求服务器状态，状态异常时中断本地匹配状态
function MatchServer:ReqMatchCheck()
    local fMatchCheckRes=function(res)
        logwarning("MatchServer:ReqMatchCheck","result",res.result)
        if res.result==Err.MatchNodeNotExist or res.result==Err.MatchPlayerNotInMatching or res.result==Err.MatchNodeIDInvalid then
            if self._checkStateTimer then
                self._checkStateTimer:Stop()
                self._checkStateTimer=nil
            end
            self._checkStateTimer=Timer:NewIns(2,5)
            self._checkStateTimer:AddListener(self.CheckStatetoReconnect,self)
            self._checkStateTimer:Start()
        
        end
    end
    local req=pb.CSMatchCheckTReq:New()
    req.node_id=self._nodeId or 0
    req:Request(fMatchCheckRes,{bShowErrTip=false})
end

function MatchServer:CheckStatetoReconnect()
    loginfo("MatchServer:CheckStatetoReconnect")
    local fOnGetStateInfo=function(res)
        if (Server.AccountServer:IsInPickHeroStage() or Server.AccountServer:IsPlayerInGame()) then
            if self:GetDsRoomId()~=res.DsRoomID then--不相等说明没收到过匹配成功或入局通知
                self:EndMatching()
            end
            if self._checkStateTimer then
                self._checkStateTimer:Stop()
                self._checkStateTimer=nil
            end
            logerror("MatchServer:CheckStatetoReconnect, MatchCheckTriggerReconnect")
            self.Events.evtMatchCheckTriggerReconnect:Invoke(true)
        elseif not Server.AccountServer:IsPlayerInMatching() and res.State & GlobalPlayerStateEnums.EPlayerState_WaitDS==0 then
            loginfo("MatchServer:CheckStatetoReconnect, EndMatching")
            self:EndMatching()
            if self._checkStateTimer then
                self._checkStateTimer:Stop()
                self._checkStateTimer=nil
            end
            DFMGlobalEvents.evtReportEvent:Invoke(EReportCategoryName.Match, EMatchFlow.MatchIng,  EReportStatusCode.Failed,  res.result, "")
        end
    end
    Server.AccountServer:GetStateInfo(fOnGetStateInfo)
end

function MatchServer:ReqPlayerStateBHDCheck(fOnPlayerStateBHDCheck)
    -- local fMatchCheckRes=function(res)
    --     logwarning("[BHD断线重连 - BHDGameController Step5] MatchServer:ReqMatchCheck","result",res.result)
    --     if res.result==Err.MatchNodeNotExist or res.result==Err.MatchPlayerNotInMatching or res.result==Err.MatchNodeIDInvalid then
    --         local fOnGetStateInfo = function(res)
    --             local bInGame = Server.AccountServer:IsPlayerInGame()
    --             if bInGame then
    --                 loginfo('[BHD断线重连 - BHDGameController Step5] MatchServer:ReqPlayerStateBHDCheck Server.TeamServer:ForceTriggerReconnect()')
    --                 Server.TeamServer:ForceTriggerReconnect()
    --             else
    --                 loginfo('[BHD断线重连 - BHDGameController Step5] MatchServer:ReqPlayerStateBHDCheck self:EndMatching()')
    --                 self:EndMatching()
    --                 DFMGlobalEvents.evtReportEvent:Invoke(EReportCategoryName.Match, EMatchFlow.MatchIng,  EReportStatusCode.Failed,  res.result, "")
    --             end
    --             if fOnPlayerStateBHDCheck then
    --                 fOnPlayerStateBHDCheck(bInGame)
    --             end
    --         end
    --         Server.AccountServer:GetStateInfo(fOnGetStateInfo)
    --     end
    -- end
    -- local req = pb.CSMatchCheckTReq:New()
    -- req.node_id = self._nodeId or 0
    -- req:Request(fMatchCheckRes, {bShowErrTip = false})


    local fOnGetStateInfo = function(res)
        local bInGame = Server.AccountServer:IsPlayerInGame()
        if bInGame then
            loginfo('[BHD断线重连 - BHDGameController Step5] MatchServer:ReqPlayerStateBHDCheck Server.TeamServer:ForceTriggerReconnect()')
            Server.TeamServer:ForceTriggerReconnect()
        else
            loginfo('[BHD断线重连 - BHDGameController Step5] MatchServer:ReqPlayerStateBHDCheck self:EndMatching()')
            self:EndMatching()
            DFMGlobalEvents.evtReportEvent:Invoke(EReportCategoryName.Match, EMatchFlow.MatchIng,  EReportStatusCode.Failed,  res.result, "BHD")
        end
        if fOnPlayerStateBHDCheck then
            fOnPlayerStateBHDCheck(bInGame)
        end
    end
    Server.AccountServer:GetStateInfo(fOnGetStateInfo)
end

function MatchServer:GetIdcList(fCallback)
    logwarning("[LogDFMGameIdcSelector] MatchServer:GetIdcList req")
    self.seqId = 0
    self.domainMap = {}
    local OnCSRoundtripDirRes = function(res)

        local idcSelectorIns = UGameSDKMgr.GetGameIdcSelectorIns(GetGameInstance())
        self.conf = res.client_conf or {client_ping_tick = 150, client_udp_timeout = 3000, client_ping_packets = 1}
        self.tickTime = self.conf.client_ping_tick
        --测速超时时间
        local waitTimeoutTime = self.conf.client_udp_timeout * 0.001 
        if idcSelectorIns then

            idcSelectorIns:SetUdpConfig(self.tickTime, waitTimeoutTime, true, self.conf.client_ping_packets)
            idcSelectorIns:SetUdpPlayerId(Server.AccountServer:GetPlayerIdStr())
        end

        if fCallback then
            fCallback(res)
        end

        self.cache_idc_list = res.idc_list or {}

        local index = 1
        local OneSDKModule =  import "DFMGameOneSDK"
        local _OneSDK = OneSDKModule.Get(GetGameInstance())

        self.seqId2AddrMap = {}
        self.seqId2RecvStateMap = {}
        self.reportIDCRoundTripTimeMap = {}
        logtable(self.cache_idc_list,true)
        for i, idc in pairs(self.cache_idc_list) do
            -- 设置conf
            for j, roundtripAddr in pairs(idc.services) do
                local vip = roundtripAddr.vip
                local vport = roundtripAddr.vport 
                local domain = roundtripAddr.domain
                if domain and _OneSDK then
                    local ip = self.domainMap[domain] or _OneSDK:GetAddressByName(domain)
                    if ip and self.domainMap[domain] == nil then
                        self.domainMap[domain] = ip
                    end
                    if  ip and (ip == vip or MathUtil.GetIPType(ip) == MathUtil.EIPType.IPv6) then
                        vip = ip
                        local idc = roundtripAddr.idc
                        local access_point = roundtripAddr.access_point or ""
                        -- 设置需要测速的端口 -- add
                        self.seqId = self.seqId + 1
                        self.seqId2AddrMap[self.seqId] = roundtripAddr

                        if PLATFORM_IOS and MathUtil.GetIPType(vip) ~= MathUtil.EIPType.IPv6 then
                            vip = self:ConvertIPv4ToIPv6(vip)
                            logerror("[LogDFMGameIdcSelector] MatchServer:GetIdcListRes convert ipv4 to ipv6 %s", vip)
                        end
                    
                        idcSelectorIns:AddUdpIpAddr(vip, vport, self.seqId)
                        logwarning("[LogDFMGameIdcSelector] MatchServer:GetIdcListRes add ip at seq %d with idc %s, access_point %s, address %s:%d domain:%s", self.seqId, idc, access_point, vip, vport,domain)
                        index = index + 1
                    end
                end
            end
        end

        local vipNum = index - 1
        if vipNum > 0 then
            -- 出现过同时出现2个回包的情况
            if self._timerDelayReportIdcSpeed then
                logwarning("[LogDFMGameIdcSelector] MatchServer:GetIdcListRes when a delayReport is timing finish %d, ignore this call", Timer.IsFinished(self._timerDelayReportIdcSpeed) and 1 or 0)
                return
            end

            idcSelectorIns:SetEnable(true)
            -- 线程等待最多1倍，真实收包时间(c++ FUdpThreadRunable::Run() sleep时间)等待1倍，每个间隔0.01m
            waitTimeoutTime = waitTimeoutTime * (vipNum + 1) + 0.01 * vipNum

            self._timerDelayReportIdcSpeed = Timer.DelayCall(waitTimeoutTime, self.ReportIdcSpeed, self)
            logwarning("[LogDFMGameIdcSelector] MatchServer:GetIdcListRes with %d idcs %d vips and will report in %f seconds later", #self.cache_idc_list, vipNum, waitTimeoutTime)
        end
    end

    local OnCSRoundtripDirResAsync = function(res)
        local idcSelectorIns = UGameSDKMgr.GetGameIdcSelectorIns(GetGameInstance())
        self.conf = res.client_conf or {client_ping_tick = 150,client_udp_timeout = 3000,client_ping_packets = 1}
        self.tickTime = self.conf.client_ping_tick
        --测速超时时间
        local waitTimeoutTime = self.conf.client_udp_timeout * 0.001
        local APMLevel = 0
        local bUseMutiPing = true
        local UDFMGameGPM = import "DFMGameGPM"
        UDFMGameGPM.Get(GetWorld())
        if PLATFORM_IOS then
            APMLevel = UDFMGameGPM.GetDeviceLevelByQcc("level6")
        elseif PLATFORM_ANDROID then
            APMLevel = UDFMGameGPM.GetDeviceLevelByQcc("level6")
        else
            APMLevel = 4
        end

        if APMLevel <= 1 then
            bUseMutiPing = false
        end

        logerror("MatchServer:GetIdcListRes APMLevel and bUseMutiPing:", APMLevel, bUseMutiPing)
        if idcSelectorIns then
            idcSelectorIns:SetUdpConfig(self.tickTime, waitTimeoutTime, bUseMutiPing, self.conf.client_ping_packets)
            idcSelectorIns:SetUdpPlayerId(Server.AccountServer:GetPlayerIdStr())
        end

        if fCallback then
            fCallback(res)
        end

        self.cache_idc_list = res.idc_list or {}

        self.index = 1
        local OneSDKModule = import "DFMGameOneSDK"
        local _OneSDK = OneSDKModule.Get(GetGameInstance())
        _OneSDK:SetEnable(true)
        self.IdcNum = 0
        self.seqId2AddrMap = {}
        self.seqId2RecvStateMap = {}
        self.reportIDCRoundTripTimeMap = {}
        self.indexToAddr = {}
        logtable(self.cache_idc_list, true)
        for i, idc in pairs(self.cache_idc_list) do
            for j, roundtripAddr in pairs(idc.services) do
                self.IdcNum = self.IdcNum + 1
            end
        end

        logwarning("MatchServer:GetIdcList idcNum:", self.IdcNum)
        for i, idc in pairs(self.cache_idc_list) do
            -- 设置conf
            for j, roundtripAddr in pairs(idc.services) do
                local vip = roundtripAddr.vip
                local vport = roundtripAddr.vport
                local domain = roundtripAddr.domain

                local idc = roundtripAddr.idc
                local access_point = roundtripAddr.access_point or ""
                -- 设置需要测速的端口 -- add
                self.indexToAddr[self.index] = roundtripAddr
                if domain and domain ~= "" and self.domainMap[domain] == nil then
                    logwarning("[LogDFMGameIdcSelector] MatchServer:GetAddressByNameFromLua domain, vport, seqId", domain, vport, self.index)
                    _OneSDK:GetAddressByNameFromLua(domain, vport, self.index, EDnsResloveSeed.Ping);
                else
                    self:_OnDnsAsyncResloved(vip, vport, self.index, EDnsResloveSeed.Ping)
                end
                logwarning(
                    "[LogDFMGameIdcSelector] MatchServer:GetIdcListRes add ip at seq %d with idc %s, access_point %s, address %s:%d domain:%s",
                    self.index, idc, access_point, vip, vport, domain)
                self.index = self.index + 1
            end
        end        
    end

    local req = pb.CSRoundtripDirReq:New()
    logwarning("MatchServer:GetIdcList use OnCSRoundtripDirResAsync")
    if IsInEditor() then
        -- Editor下用同步
        req:Request(OnCSRoundtripDirRes)
    else
        req:Request(OnCSRoundtripDirResAsync)
    end
end

function MatchServer:PingUdp(ip,port,seqid)
    log("[LogDFMGameIdcSelector] MatchServer:PingUdp")
    local idcSelectorIns = UGameSDKMgr.GetGameIdcSelectorIns(GetGameInstance())
    idcSelectorIns:AddUdpIpAddr(ip,port,seqid)
end

-- todo:需要tick错峰上报
function MatchServer:ReportIdcSpeed()
    if self._timerDelayReportIdcSpeed then
        Timer.CancelDelay(self._timerDelayReportIdcSpeed)
        self._timerDelayReportIdcSpeed = nil
    end

    local OnCSReportIDCRes = function(res)
        log("[LogDFMGameIdcSelector] MatchServer:OnReportIDCRes")
        logtable(res, true)
    end
    
    local req = pb.CSReportIDCReq:New()
    local idcSelectorIns = UGameSDKMgr.GetGameIdcSelectorIns(GetGameInstance())
    
    local idc_list = self:_GetReportIDCRoundTripTimeMap() -- {} -- 获取当前获取到的数据
    -- 只上报一次Timeout测速结果
    if self._hasReport == false then
        for i, roundtripAddr in pairs(idc_list) do
            for _, service in pairs(idc_list[i].services) do
                if service.result == RttTimeResult.RttTime_Timeout then
                   -- 上报Timeout数据
                   local Idc = roundtripAddr.idc
                   local IP = service.addr.vip
                   local Port = service.addr.vport
                   local PlayerId = Server.AccountServer:GetPlayerId()
                   local Result = RttTimeResult.RttTime_Timeout
                   LogAnalysisTool.DoSendIDCTimeoutLog(Idc,IP,Port,PlayerId,Result)
                   self._hasReport = true 
                end
            end
        end
    end

    req.idc_list = idc_list

    req.client_ip = idcSelectorIns:GetLocalIPAddr() -- "" -- 应该是固定的
    local appVersion = UGameVersionUtils.GetAppProductVersion()
    if appVersion == "" then
        req.version = "0.0.0.0"
    else
        req.version = appVersion
    end

    if  #req.idc_list == 0 then
        logwarning("[LogDFMGameIdcSelector] ReportIdcSpeed with empty data, ignore")
        return
    end
    req.network = Server.SDKInfoServer:GetNetworkType()
    req.mobile_network = Server.SDKInfoServer:GetNetWorkDetailInfo()
    logwarning("[LogDFMGameIdcSelector] MatchServer:ReportIdcSpeed req with %d idc list", #req.idc_list)
    req:Request(OnCSReportIDCRes)
    local idcSelectorIns = UGameSDKMgr.GetGameIdcSelectorIns(GetGameInstance())
    if idcSelectorIns then
        idcSelectorIns:SetEnable(false)
        self.index = 1
        self.IdcNum = 0
        self.seqId2AddrMap = {}
        self.seqId2RecvStateMap = {}
        self.reportIDCRoundTripTimeMap = {}
        self.indexToAddr = {}
    end
	--Module.NetworkBusiness:SetIdcData(idc_list)
    self.Events.evtUpdateIdcData:Invoke(idc_list)
end

function MatchServer:_GetReportIDCRoundTripTimeMap()
    local result = self.reportIDCRoundTripTimeMap or {}
    -- 遍历seqId2AddrMap,查询其中每个seqAddr是否都在result中，如不在则补齐
    for seqId, seqAddr in pairs(self.seqId2AddrMap) do
        local idc = seqAddr.idc
        local access_point = seqAddr.access_point or ""
        local seqServiceFound = false
        local seqIdcFound = false
        local roundtripTime = {
            rtt = 0,
            addr = seqAddr,
            result = RttTimeResult.RttTime_Timeout
        }

        for i, idcRoundTripTimeIns in pairs(result) do
            if idcRoundTripTimeIns.idc == seqAddr.idc then
                seqIdcFound = true
                for j, server in pairs(result[i].services) do
                    if seqAddr.vip == server.addr.vip and seqAddr.vport == server.addr.vport then
                        seqServiceFound = true
                        break
                    end
                end

                -- idc中的服务没找到
                if not seqServiceFound then
                    table.insert(result[i].services, roundtripTime)
                    logwarning("[LogDFMGameIdcSelector] GetReportIDCRoundTripTimeMap add timeout access point: %s(%s:%d) for idc %s", seqAddr.access_point, seqAddr.vip, seqAddr.vport, idcRoundTripTimeIns.idc)
                end
            end
        end
        
        -- idc没找到
        if not seqIdcFound then
            local idcRoundtripTime = {
                idc = idc,
                services = {roundtripTime}
            }
            table.insert(result, idcRoundtripTime)
            logwarning("[LogDFMGameIdcSelector] GetReportIDCRoundTripTimeMap add timeout idc: access point %s(%s:%d) for idc %s", seqAddr.access_point, seqAddr.vip, seqAddr.vport, seqAddr.idc)
        end
    end

    logtable(result, true)
    return result
end

function MatchServer:_OnUdpPingRecv(bufferTable, seqId)
    logformat("[LogDFMGameIdcSelector] MatchServer:_OnUdpPingRecv seqId %d", seqId)
    local rtt = bufferTable.time
    local addr = self.seqId2AddrMap[seqId] or {}
    if addr == {} then
        logwarning("[LogDFMGameIdcSelector] _OnUdpPingRecv udp seqId %d no found", seqId)
        return 
    end
    self.conf = self.conf or {client_ping_tick = 150, client_udp_timeout = 3000}
    local roundtripTime = {
        rtt = rtt,
        addr = addr,
        result = rtt >= self.conf.client_udp_timeout and RttTimeResult.RttTime_Timeout or RttTimeResult.RttTime_OK
    }
    self:ConstructReportStruct(roundtripTime)
end

function UECall_UdpRecvTime(finalRTT, seqId, totalReceive)
    Server.MatchServer:RecvTime(finalRTT, seqId, totalReceive)
    Server.MatchServer:CheckAllAddrReady(seqId)
end

function MatchServer:CheckAllAddrReady(seqId)
    if self.seqId2AddrMap[seqId] == nil then
        logwarning("[LogDFMGameIdcSelector] not found seqId, may report already")
        return 
    end
    self.seqId2RecvStateMap[seqId] = 1
    local hasNoRecv = false
    for seqIdTemp, seqAddr in pairs(self.seqId2AddrMap) do
        if not self.seqId2RecvStateMap[seqIdTemp] then
            hasNoRecv = true
            break
        end
    end

    -- 测速结果都已经收到，直接发送不再等待
    if not hasNoRecv then
        logwarning("[LogDFMGameIdcSelector] all idc address are received, start report")
        self:ReportIdcSpeed()
    end
end

function MatchServer:RecvTime(finalRTT, seqId, totalReceive)
    if self.seqId2AddrMap[seqId] == nil then
        logwarning("[LogDFMGameIdcSelector] not found seqId, may report already")
        return 
    end
    local idcSelectorIns = UGameSDKMgr.GetGameIdcSelectorIns(GetGameInstance())
    if seqId > 0 and finalRTT > 0 then
        local rtt = finalRTT
        local addr = self.seqId2AddrMap[seqId] or {}
        -- 收到回包的seqId在当前请求的seqId2AddrMap中不存在
        if not addr or not addr.idc then
            logwarning("[LogDFMGameIdcSelector] RecvTime udp seqId no found for seqid %d", seqId)
            return 
        end

        local roundtripTime = {
            rtt = rtt,
            addr = addr,
            packet_loss = 100 - totalReceive * 10,
            result = rtt >= self.conf.client_udp_timeout and RttTimeResult.RttTime_Timeout or RttTimeResult.RttTime_OK
        }
    
        self:ConstructReportStruct(roundtripTime)
    end
end

function MatchServer:ConstructReportStruct(roundtripTime)
    local addr = roundtripTime.addr
    local idc = addr.idc
    local access_point = addr.access_point or ""

    if not addr.vport or not roundtripTime.rtt then
        logtable(roundtripTime)
    end

    logwarning("[LogDFMGameIdcSelector] MatchServer:ConstructReportStruct: idc %s, access_point %s, addr %s:%d, rtt %f, result %d", idc, access_point, addr.vip, addr.vport, roundtripTime.rtt, roundtripTime.result)
    
    for i, idcRoundTripTimeIns in pairs(self.reportIDCRoundTripTimeMap) do
        if idcRoundTripTimeIns.idc == idc then
            for j, server in pairs(self.reportIDCRoundTripTimeMap[i].services) do
                if addr.vip == server.addr.vip and addr.vport == server.addr.vport then
                    self.reportIDCRoundTripTimeMap[i].services[j] = roundtripTime
                    return
                end
            end

            table.insert(self.reportIDCRoundTripTimeMap[i].services,roundtripTime)
            return
        end
    end

    local idcRoundtripTime = {
        idc = idc,
        services = {roundtripTime}
    }
    table.insert(self.reportIDCRoundTripTimeMap,idcRoundtripTime)
end

--匹配时间相关信息(预计时长、所需人数等)
function MatchServer:SetMatchTimeInfo(res)
    self._timeInfo = res.ShowInfo
    if res.node_id then
        logwarning("MatchServer:StartMatching _matchCheckTimerStart nodeId",res.node_id)
        self._nodeId=res.node_id
        if self._matchCheckTimer then
            self._matchCheckTimer:Stop()
        end
        self._matchCheckTimer=Timer:NewIns(20,0)
        self._matchCheckTimer:AddListener(self.ReqMatchCheck,self)
        self._matchCheckTimer:Start()

    end
end

function MatchServer:GetMatchTimeInfo()
    return self._timeInfo
end

--region 无缝入局交互地图需要的数据

---@param ntf pb_CSMatchRoomSyncMemberOptionNtf
function MatchServer:_OnMatchRoomSyncMemberOptionNtf(ntf)
    logwarning("[SyncMapSign] MatchServer:_OnMatchRoomSyncMemberOptionNtf received!")
    logtable(ntf)
    UDFMIrisEnterSubsystem:Get(GetWorld()):ReceiveSyncMapSignInfo(ntf.member_id, ntf.data)
end

function MatchServer:_OnSeamlessMapSignChange(type, data)
    logwarning("[SyncMapSign] MatchServer:_OnSeamlessMapSignChange!")
    logtable(data)

    local OnCSMatchRoomSyncMemberOptionRes = function(res)
        logwarning("[SyncMapSign] MatchServer:OnCSMatchRoomSyncMemberOptionRes, ", res.result)
    end

    local req = pb.CSMatchRoomSyncMemberOptionReq:New()
    req.option_type = type
    req.ds_room_id = self.DSRoomIDForSync
    local str = ""
    for i = 1, #data do
        str = str .. string.char(data[i])
    end
    req.data = str
    logwarning("[SyncMapSign] send, ds_room_id=", req.ds_room_id)
    req:Request(OnCSMatchRoomSyncMemberOptionRes)
end

function MatchServer:AddSeamlessMapSignChangeEvent()
    if self.SeamlessMapSignChangeHandle ~= nil then
        UDFMGameplayDelegates.Get(GetWorld()).OnSeamlessMapSignChange:Remove(self.SeamlessMapSignChangeHandle)
    end
    self.SeamlessMapSignChangeHandle = UDFMGameplayDelegates.Get(GetWorld()).OnSeamlessMapSignChange:Add(self._OnSeamlessMapSignChange, self)
end

function MatchServer:RequestAcceptedQuests()
    logwarning("[SyncMapSign] RequestAcceptQuests")

    local mapId = Server.GameModeServer:GetMapID()
    UDFMIrisEnterSubsystem:Get(GetWorld()).CurrentEnterMapID = mapId or 0
    logwarning("[SyncMapSign] TestGetMapId = ", mapId)

    ---@param res pb_CSQuestGetAcceptedQuestsRes
    local OnCSQuestGetAcceptedQuestsRes = function(res)
        logwarning("[SyncMapSign] OnCSQuestGetAcceptedQuestsRes, result:", res.result, ", data:", res.accepted_quests)
        if res.accepted_quests ~= nil then
            LuaQuestArr = {}
            ---@param quest pb_PlayerQuestData
            for _, quest in ipairs(res.accepted_quests) do
                local SOLQuest = FSOLQuest()
                SOLQuest.QuestID = quest.quest_id
                SOLQuest.QuestState = quest.quest_state
                SOLQuest.QuestAcceptTime = quest.quest_accept_time
                LuaQuestObjectionArr = {}
                ---@param quest_obj pb_PlayerQuestObjectiveData
                for _, quest_obj in ipairs(quest.quest_objectives) do 
                    local SOLQuestObj = FSOLQuestObjection()
                    SOLQuestObj.QuestObjectionId = quest_obj.quest_objective_id
                    SOLQuestObj.Value = quest_obj.value
                    SOLQuestObj.QuestId = quest.quest_id
                    SOLQuestObj.bHasMark = quest_obj.has_marked
                    SOLQuestObj.ServerTimeSpent = quest_obj.spent_seconds
                    if quest_obj.has_completed then
                        SOLQuestObj.QuestObjectionState = ESOLQuestObjectionState.InitialComplete
                    else
                        SOLQuestObj.QuestObjectionState = ESOLQuestObjectionState.InActive
                    end
                    -- SOLQuest.QuestObjectionArr:Add(SOLQuestObj)
                    table.insert(LuaQuestObjectionArr, SOLQuestObj)
                end
                SOLQuest.QuestObjectionArr = LuaQuestObjectionArr
                
                table.insert(LuaQuestArr, SOLQuest)
                logwarning("[SyncMapSign] OnCSQuestGetAcceptedQuestsRes, quest:", quest.quest_id)
            end
            UDFMIrisEnterSubsystem:Get(GetWorld()):LuaAddSOLQuest(LuaQuestArr)
        else
            logerror("[SyncMapSign] OnCSQuestGetAcceptedQuestsRes, res.accepted_quests == nil")
        end
    end

    local req = pb.CSQuestGetAcceptedQuestsReq:New()
    req:Request(OnCSQuestGetAcceptedQuestsRes)
end

function MatchServer:RequestAcceptedActivity()
    logwarning("[SyncMapSign] RequestAcceptedActivity")

    ---@param res pb_CSActivityGetAcceptedActivityRes
    local OnCSActivityGetAcceptedActivityRes = function(res)
        logwarning("[SyncMapSign] OnCSActivityGetAcceptedActivityRes, result:", res.result, ", data:", res.quest_objectives)
        if res.quest_objectives ~= nil then
            LuaQuestObjectionArr = {}
            ---@param objective pb_NumeralActivityObjective
            for _, objective in ipairs(res.quest_objectives) do
                logwarning("[SyncMapSign] OnCSActivityGetAcceptedActivityRes, objective:", objective.id)
                local SOLQuestObj = FSOLQuestObjection()
                SOLQuestObj.QuestObjectionId = objective.id
                SOLQuestObj.Value = objective.current_value
                if objective.completed then
                    SOLQuestObj.QuestObjectionState = ESOLQuestObjectionState.InitialComplete
                else
                    SOLQuestObj.QuestObjectionState = ESOLQuestObjectionState.InActive
                end
                table.insert(LuaQuestObjectionArr, SOLQuestObj)
            end
            UDFMIrisEnterSubsystem:Get(GetWorld()):LuaAddActivityQuest(LuaQuestObjectionArr)
        else
            logerror("[SyncMapSign] OnCSActivityGetAcceptedActivityRes, res.quest_objectives == nil")
        end
    end
    
    local req = pb.CSActivityGetAcceptedActivityReq:New()
    req:Request(OnCSActivityGetAcceptedActivityRes)
end

function MatchServer:GetAllKeys()
    local AllKeys = Server.InventoryServer:GetAllKeys()
    for _, KeyID in pairs(AllKeys) do
        UDFMIrisEnterSubsystem:Get(GetWorld()):LuaAddItemID(KeyID)
    end
end
--endregion


function MatchServer:InBlackIpList(ip, port)
    if not ip or not port then
        logerror("[InBlackIpList] ip or port is nil")
        return false
    end

    local blackIpListStr = Facade.ConfigManager:GetString("BlackIpList","")
    if blackIpListStr == nil or blackIpListStr == "" then
        logerror("[InBlackIpList] black list is empty")
        return false
    end

    local urls = string.split(blackIpListStr,";")
    local curUrl = string.format("%s:%s",ip, port)
    logerror("[InBlackIpList] blackIpListStr and curUrl:",blackIpListStr, curUrl)
    if #urls ~= 0 then
        for _, value in pairs(urls) do
            if not string.isempty(value) and value == curUrl then
                logerror("[InBlackIpList] in black list, ip and port:",ip , port)
                return true
            end
        end
    end

    return false
end

function MatchServer:AddBlackIp(url)
    if url == nil or string.isempty(url) then
        logerror("[AddBlackIp] empty url")
        return
    end
    local blackIpListStr = Facade.ConfigManager:GetString("BlackIpList","")
    local resultStr = url
    if not string.isempty(blackIpListStr) then
        -- 已有黑名单，要判断是否已经包含该url,已包含不需要重复添加
        local urls = string.split(blackIpListStr,";")
        if #urls ~= 0 then
            for _, value in pairs(urls) do
                if not string.isempty(value) and value == url then
                    logerror("[AddBlackIp] already in black list")
                    return
                end
            end
        end
        resultStr = string.format("%s;%s", blackIpListStr, resultStr)
    end
    logerror("[AddBlackIp] resultStr:",resultStr)
    Facade.ConfigManager:SetString("BlackIpList",resultStr)
end

function MatchServer:ClearBlackIpList()
    logerror("[ClearBlackIpList]..")
    Facade.ConfigManager:SetString("BlackIpList","")
end

function MatchServer:SetDsIpAndPort(dsIp, dsPort)
    self.usedDsIp = dsIp
    self.usedDsPort = dsPort
end

function MatchServer:GetDsIpAndPort()
    return self.usedDsIp, self.usedDsPort
end

function MatchServer:IsReconnect()
    return self.bIsReconnect
end

function MatchServer:SetIsReconnect(isReconenct)
    self.bIsReconnect = isReconenct
end

function MatchServer:_OnReconnectResetChange()
    if self:IsReconnect() then
        DFMGlobalEvents.evtReportEvent:Invoke(EReportCategoryName.Reconnect, EReconnectFlow.DsLink,  EReportStatusCode.Success,  0, "")
        logerror("MatchServer:_OnReconnectResetChange() IsReconnect ...",  0, "")
    else
        DFMGlobalEvents.evtReportEvent:Invoke(EReportCategoryName.Match, EMatchFlow.DsLink,  EReportStatusCode.Success,  0, "")
        logerror("MatchServer:_OnReconnectResetChange() Not Reconnect ...",  0, "")
    end
end

--- BEGIN MODIFICATION @ VIRTUOS: 实现Competitive Activity
function MatchServer:_GeneratePS5MatchData(res)
    if not IsPS5() then
        return
    end
    self._platformMatchInfo = FPlatformMatchInfo()
    local members = self._platformMatchInfo.Members
    for index, value in ipairs(res.players) do
        local ps5MatchMemberInfo = FPlatformMemberInfo()
        ps5MatchMemberInfo.PlayerId = value.player_id
        ps5MatchMemberInfo.TeamId = value.camp

        local playerOnlineId = self:GetPS5OnlineIdByOpenId(value.player_id)
        if playerOnlineId ~= nil then
            ps5MatchMemberInfo.PlayerName = playerOnlineId
        else
            ps5MatchMemberInfo.PlayerName = value.player_nick
        end

        
        --plat_id == 108，为ps5平台
        if value.plat_id == PlatIDType.Plat_Playstation then
            ps5MatchMemberInfo.bIsPS5Player = true
        end
        members:Add(ps5MatchMemberInfo)
        logwarning("player_id is: ",value.player_id)
        logtable(value)
    end
    self._platformMatchInfo.Members = members
    
end

function MatchServer:HandlePS5MatchData()
    local DFMPlatformActivityManager = UDFMPlatformActivityManager.Get(GetWorld())
    if DFMPlatformActivityManager and self._platformMatchInfo ~= nil then
        --刷新match数据      
        DFMPlatformActivityManager:UpdateMatchState(self._platformMatchInfo)       
    end
end

function MatchServer:_OnGetMatchIdForActivity()
    if not IsPS5() then
        return
    end
    local OnCSMatchRoomGetPlayStationMatchIdTRes=function(res)
        logwarning("Get MatchServer: CSMatchRoomGetPlayStationMatchIdTRes","result",res.result)
        logwarning("PlayerId MatchServer: CSMatchRoomGetPlayStationMatchIdTRes","PlayerId",Server.AccountServer:GetPlayerId())
        if res.result==0 then
            local DFMPlatformActivityManager = UDFMPlatformActivityManager.Get(GetWorld())
            if DFMPlatformActivityManager and res.match_id ~= "" then
                DFMPlatformActivityManager:SetMatchId(res.match_id)
            end
            table.sort(
                res.players,
                function(player1Info, player2Info)
                    return player1Info.player_id < player2Info.player_id
                end
                )
            logtable(res)
            for index, value in ipairs(res.players) do
                if value.plat_id == PlatIDType.Plat_Playstation  then
                    --只有第一个ps5玩家需要创建和更新match
                    if Server.AccountServer:GetPlayerId() == value.player_id  then
                        self:_GeneratePS5MatchData(res)
                        self:HandlePS5MatchData()
                    end
                    break
                end
            end   
            
        end
    end
    local req=pb.CSMatchRoomGetPlayStationMatchIdTReq:New()
    req.ds_room_id=Server.MatchServer:GetDsRoomId()
    req:Request(OnCSMatchRoomGetPlayStationMatchIdTRes)

    --特殊情况处理，team session在某些情况下可能会销毁
    local DFMPlatformActivityManager = UDFMPlatformActivityManager.Get(GetWorld())
    if DFMPlatformActivityManager then
        --刷新Session状况，接口内部回判断Session是否需要创建    
        DFMPlatformActivityManager:CreateSessionForActivity()     
    end

end

function MatchServer:_OnSetMatchIdForActivity(MatchId)
    if not IsPS5() then
        return
    end
    local OnCSMatchRoomSetPlayStationMatchIdTRes=function(res)
        logwarning("Set MatchServer: OnCSMatchRoomSetPlayStationMatchIdTRes","result",res.result)
        if res.result==0 then
            logtable(res)
        end
    end
    if MatchId ~= "" then
        local req=pb.CSMatchRoomSetPlayStationMatchIdTReq:New()
        req.ds_room_id=Server.MatchServer:GetDsRoomId()
        req.match_id=MatchId
        req:Request(OnCSMatchRoomSetPlayStationMatchIdTRes) 
    end

end

function MatchServer:OnLeaveMatchForActivity()

    local OnCSMatchRoomGetPlayStationMatchIdTRes=function(res)
        logwarning("Leave MatchServer: CSMatchRoomGetPlayStationMatchIdTRes","result",res.result)
        logwarning("PlayerId MatchServer: CSMatchRoomGetPlayStationMatchIdTRes","PlayerId",Server.AccountServer:GetPlayerId())
        if res.result==0 then
            if res.match_id ~= "" then
                local DFMPlatformActivityManager = UDFMPlatformActivityManager.Get(GetWorld())
                if DFMPlatformActivityManager then
                    DFMPlatformActivityManager:SetMatchId(res.match_id)
                    DFMPlatformActivityManager:HandlePlayerLeaveGameMatch()
                end
            end
        end
    end
    local req=pb.CSMatchRoomGetPlayStationMatchIdTReq:New()
    req.ds_room_id=Server.MatchServer:GetDsRoomId()
    req:Request(OnCSMatchRoomGetPlayStationMatchIdTRes)
end

function MatchServer:GetPS5OnlineIdByOpenId(openId)
    if IsPS5() then
        local UDFMOnlineIdentityManager = import "DFMOnlineIdentityManager"
        local DFMOnlineIdentityManager = UDFMOnlineIdentityManager.Get(GetGameInstance())
        if DFMOnlineIdentityManager then
            local PS5OnlineId = DFMOnlineIdentityManager:GetPlayerPlatformIdByOpenId(openId)
            if not string.isempty(PS5OnlineId) then
                logerror("MatchServer:GetPS5OnlineIdByOpenId, Return the OnlineId", PS5OnlineId)
                return PS5OnlineId
            end
        end
    end
    return nil
end
--- END MODIFICATION

function MatchServer:SetIpAddressList()
    local parts = {}
    local domains = {}
    if self.dsHostInfo == nil or #self.dsHostInfo == 0 then
        logerror("MatchServer:SetIpAddressList  self.dsHostInfo is nil or empty")
        return
    end

    for _, host in pairs(self.dsHostInfo) do
        if host.Ip and host.Port then
            local ip_str = tostring(host.Ip):gsub("%s+", "")
            local port_num = tonumber(host.Port)
            if port_num and port_num > 0 and port_num <= 65535 then
                local temp = ip_str .. ":" .. tostring(port_num)
                table.insert(parts, temp)
                table.insert(domains, host.Domain or "unknown")
            end
        end
    end
   
    if #parts > 0 and #parts == #domains then
        local ipList = #parts > 0 and table.concat(parts, ",") or ""
        local domainList = #domains > 0 and table.concat(domains, ",") or ""
        local result = ipList .. " "..domainList
        logerror("MatchServer:SetIpAddressList result:", result)
        if result ~= "" then
            UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "dualChannel.RemoteIpAddressCandidates "..result, nil)
        end
    end 
end

function MatchServer:_UpdateIpListInGame()
    logerror("MatchServer:_UpdateIpListInGame...")
    if self.matchNtf then
        local dsIp = self.matchNtf.ds_textual_ip
        local dsPort = self.matchNtf.ds_port
        local dsIpList = self.matchNtf.ds_ip_list
		self.dsDomain = self.matchNtf.ds_domain
        if Facade.ProtoManager:GetDNSResolvedAsync() == true then
            logerror("MatchServer:_UpdateIpListInGame start, ip and port:", dsIp, dsPort)
            self:_ParseDsInfoAsync(dsIpList, dsIp, dsPort, EDnsResloveSeed.UpdateIpInfo)
        end
    end
end

function MatchServer:ClearIpListInGame()
    logerror("MatchServer:_ClearIpListInGame...")
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "dualChannel.RemoteIpAddressCandidates ", nil)
end

function MatchServer:_OnClientConnectDSSuccessNtf(IpAddrStr, Domain, ReconnectType)
    logerror("MatchServer:_OnClientConnectDSSuccessNtf IpAddrStr:", IpAddrStr, "Domain:", Domain)
    if IpAddrStr == nil or string.isempty(IpAddrStr) then
        logerror("MatchServer:_OnClientConnectDSSuccessNtf IpAddrStr is nil or empty")
        return
    end

    local ip, port = string.match(IpAddrStr, "^%[(.-)%]:(%d+)$")
    if not ip then
        ip, port = string.match(IpAddrStr, "^(.-):(%d+)$")
    end
    logerror("MatchServer:_OnClientConnectDSSuccessNtf ip and port is ",ip , port)
    if ip == nil or port == nil or string.isempty(ip) or string.isempty(port) then
        logerror("MatchServer:_OnClientConnectDSSuccessNtf ip or port is nil")
        return
    end
    LogAnalysisTool.DOSendMatchDsCLB(Server.AccountServer:GetPlayerIdStr(), self:GetDsRoomId(), Domain, ip, tonumber(port), ReconnectType)
end

function MatchServer:_OnClientConnectDSFailedNtf(info)
    if info then
        logerror("MatchServer:_OnClientConnectDSSuccessNtf ip and port is ",info.Ip , info.Port)
        local dsroomId = self:GetDsRoomId()
        LogAnalysisTool.DOSendConnectDSFailed(info,dsroomId)
    end
end

function MatchServer:_ReportStartMatchTglog(ntf)
    local timeStr=TimeUtil.TransUnixTimestamp2YYMMDDHHMMSSString(TimeUtil.GetCurrentTime()+TimeUtil.GetTimeOffsetInSeconds())
    local req=pb.CSMatchRoomStartMatchTglogTReq:New()
    req.ds_room_id=ntf.ds_room_id
    req.sec_report_data=DFMGameTss and DFMGameTss:GetSdkCoreData()
    req.client_start_time=timeStr
    loginfo("MatchServer:_OnPlayerJoinMatchNtf CSMatchRoomStartMatchTglogTReq",req.ds_room_id,req.client_start_time)
    req:Request()
end

function MatchServer:_RobotJoinMatchNtf(ntf)
    loginfo("MatchServer:_RobotJoinMatchNtf ")
    local nick_list = ntf.enemy_robot_nicknames
    if nick_list == nil or table.isempty(nick_list) then
        return
    end
    loginfo("MatchServer:_RobotJoinMatchNtf  enemy_robot_nicknames", nick_list)
    local UFontStyleRuntimeManager = import("FontStyleRuntimeManager").Get()
    local nickNamePresetStyle = {"Body2_24pt", "Body0_20pt", "Body4_28pt", "Header4_32pt"} -- 昵称的预设字体样式名
    for index, value in ipairs(nick_list) do
        if value and value ~= "" then
            if UFontStyleRuntimeManager then
                for k, v in ipairs(nickNamePresetStyle) do
                    UFontStyleRuntimeManager:WarmupText(v, value) -- 预热昵称
                end
            end
        end
    end
end

function MatchServer:_StartMatchSucceed(MatchInfo,EventIds,ntf)
    Timer.DelayCall(2, function()
        self.UseSeamlessTravel = UDFMIrisEnterSeamlessGameplayHelper.StartMatchSucceed(GetWorld(),999,"QStage_999_1",1,Server.GameModeServer:GetLevelName(),Server.AccountServer:IsInRoom(), MatchInfo)
        UDFMIrisEnterSeamlessGameplayHelper.PrepareIntroEvent(GetWorld(), 999, "QStage_999_1", 16, EventIds, ntf.iris_event_course_id, Server.GameModeServer:GetLevelName()) -- 让蓝图做一些入局事件的初始化
    end)
end

-- 仅入局Loading和局内时能获得正确的值，不要在其他阶段使用
function MatchServer:IsInTDMGameMode()
    return self._matchModeInfo and self._matchModeInfo.game_mode == MatchGameMode.TDMGameMode
end

return MatchServer
