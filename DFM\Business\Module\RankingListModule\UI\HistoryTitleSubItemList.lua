----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRankingList)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class HistoryTitleSubItemList : UIWidgetBase
local HistoryTitleSubItemList = ui("HistoryTitleSubItemList")


function HistoryTitleSubItemList:Ctor()
    self._wtPeriodTxtBlock = self:Wnd("DFTextBlock_66", UITextBlock)
    self._wtWrapBox = self:Wnd("DFWrapBox_1", UIWidgetBase)
    self._wtDividerLine = self:Wnd("DFImage_119", UIWidgetBase)
    self._wtWrapBox:ClearChildren()
    self._itmeInsIds = {}
end


function HistoryTitleSubItemList:RefreshTitleSubItemList(period, resList, isCurrent, playerName, bIsLast)
    if period == nil or resList == nil then
        return
    end
    -- She3特殊处理
    if period == -1 then
        self._wtPeriodTxtBlock:SetText(Module.RankingList.Config.Loc.InSeason)
    else
        if isCurrent then
            self._wtPeriodTxtBlock:SetText(string.format(Module.RankingList.Config.Loc.CurrentPeriodNum, period))
            -- local green = FLinearColor(0.0588, 0.9686, 0.5882, 1)
            local green = Facade.ColorManager:GetSlateColorByRowName('C005')
            self._wtPeriodTxtBlock:SetColorAndOpacity(green)  -- 设置为绿色
        else
            self._wtPeriodTxtBlock:SetText(string.format(Module.RankingList.Config.Loc.PeriodNum, period))
        end
    end
    -- resList[i]: playerName, titleId, adCode, ranking
    -- She3特殊处理:period为-1时代表敬请期待
    if period == -1 then
        local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.HistoryTitleSubItem, self._wtWrapBox)
        local uiIns = getfromweak(weakUIIns)
        if uiIns then
            local slot = UWidgetLayoutLibrary.SlotAsWrapBoxSlot(uiIns)
            slot:SetFillEmptySpace(false)
            uiIns:SetDesc(Module.RankingList.Config.Loc.ComingSoon)
            table.insert(self._itmeInsIds, instanceId)
        end
    elseif #resList == 0 then  -- 当前期:虚位以待
        local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.HistoryTitleSubItem, self._wtWrapBox)
        local uiIns = getfromweak(weakUIIns)
        if uiIns then
            local slot = UWidgetLayoutLibrary.SlotAsWrapBoxSlot(uiIns)
            slot:SetFillEmptySpace(false)
            uiIns:SetDesc(Module.RankingList.Config.Loc.EmptyDesc)
            table.insert(self._itmeInsIds, instanceId)
        end
    else
        for i, item in ipairs(resList) do
            local titleId, adCode, ranking = item.titleId, item.adCode, item.ranking
            loginfo(string.format("[%d]: playerName=%s, titleId=%s, adCode=%s, ranking=%s", 
                i, playerName, titleId, adCode, ranking))
            local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.HistoryTitleSubItem, self._wtWrapBox)
            local uiIns = getfromweak(weakUIIns)
            if uiIns then
                uiIns:RefreshTitleSubItem(playerName, titleId, adCode, ranking)
                table.insert(self._itmeInsIds, instanceId)
            end
        end
    end
    if not IsHD() then
        self:AutoSetPadding()
    end
    -- 分割线
    if bIsLast then
        self._wtDividerLine:Collapsed()
    else
        self._wtDividerLine:Visible()
    end
end

function HistoryTitleSubItemList:AutoSetPadding()
    if #self._itmeInsIds == 0 then
        return
    end

    Timer.DelayCall(0.1, function()
        local Geometry = self._wtWrapBox:GetCachedGeometry()
        -- local Geometry = self._wtVerticalBox:GetCachedGeometry()
        if Geometry then

            local itemWidth = IsHD() and 826 or 534
            local itemHeight = IsHD() and 184 or 119
            local padding = IsHD() and 16 or 10
            local boxWidth = Geometry:GetLocalSize().X

            -- 计算每行最大子项数（考虑间距）
            local maxPerRow = math.floor((boxWidth + padding) / (itemWidth + padding))
            maxPerRow = math.max(1, maxPerRow)

            -- 计算剩余空间（包含间距）
            local totalUsedWidth = maxPerRow * itemWidth + (maxPerRow - 1) * padding
            local spaceWidth = boxWidth - totalUsedWidth

            -- 平均分配到每个子项
            local extraPerItem = spaceWidth / maxPerRow
            local newWidth = math.floor(itemWidth + extraPerItem)

            -- 应用新宽度
            for k, insId in pairs(self._itmeInsIds) do
                local wtItem = Facade.UIManager:GetSubUI(self, UIName2ID.HistoryTitleSubItem, insId)
                if wtItem then
                    wtItem:SetSize(newWidth, newWidth / 4.48)
                end
            end
        end
    end)
end

return HistoryTitleSubItemList
