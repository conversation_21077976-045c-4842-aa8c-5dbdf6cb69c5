----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class ActivityModule : ModuleBase
local ActivityModule = class("ActivityModule", require("DFM.YxFramework.Managers.Module.ModuleBase"))
local ActivityLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"
local ActivityLoadLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityLoadLogic"
local GeneralHelperTool = require "DFM.StandaloneLua.BusinessTool.GeneralHelperTool"
local ActivityRedDotLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityRedDotLogic"

function ActivityModule:Ctor()
end

---------------------------------------------------------------------------------
--- Module 生命周期
---------------------------------------------------------------------------------
--- 模块Init回调，用于初始化一些数据
---@overload fun(ModuleBase, OnInitModule)
function ActivityModule:OnInitModule()
end

--- 若为非懒加载模块，则在Init后调用;对应每个OnGameFlowChangeEnter
--- 模块默认加载资源（预加载UI蓝图、需要用到的图片等等
---@overload fun(ModuleBase, OnLoadModule)
function ActivityModule:OnLoadModule()
end

--- 无论是否懒加载都会调用，对应每个OnGameFlowChangeLeave
--- 模块默认卸载资源
---@overload fun(ModuleBase, OnUnloadModule)
function ActivityModule:OnUnloadModule()
end

--- 注销LuaEvent、Timer监听
---@overload fun(ModuleBase, OnDestroyModule)
function ActivityModule:OnDestroyModule()
    self:CloseMainPanel()
    self:RemoveAllLuaEvent()
end

---@overload fun(ModuleBase, OnGameFlowChangeLeave)
function ActivityModule:OnGameFlowChangeLeave(gameFlowType)
    if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then
        --- ActivityEventLogic.RemoveLobbyListeners()
    end
    if gameFlowType == EGameFlowStageType.ModeHallToLobby then
        Module.Activity.Field:SetMode(1)
    else
        Module.Activity.Field:SetMode(0)
    end
end

---@overload fun(ModuleBase, OnGameFlowChangeEnter)
function ActivityModule:OnGameFlowChangeEnter(gameFlowType)
    if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then
        --- ActivityEventLogic.AddLobbyListeners()
    end
end

function ActivityModule:DoSomeThing(...)
    return ActivityLogic.DoSomeThingProcess(...)
end

---------------------------------------------------------------------------------
--- Loading 生命周期
--- 设置bAutoLoading = true则下列生命周期有效
---------------------------------------------------------------------------------
---@overload fun(ModuleBase, OnLoadingLogin2Frontend)
function ActivityModule:OnLoadingLogin2Frontend(gameFlowType)
    ActivityLoadLogic.OnLoadingLogin2FrontendProcess(gameFlowType)
    self:_SubscribeEvent(true)
end

---@overload fun(ModuleBase, OnLoadingGame2Frontend)
function ActivityModule:OnLoadingGame2Frontend(gameFlowType)
    ActivityLoadLogic.OnLoadingGame2FrontendProcess(gameFlowType)
    self:_SubscribeEvent(true)
end

---@overload fun(ModuleBase, OnLoadingFrontend2Game)
function ActivityModule:OnLoadingFrontend2Game(gameFlowType)
    ActivityLoadLogic.OnLoadingFrontend2GameProcess(gameFlowType)
    self:_SubscribeEvent()
end

-----------------------------------------------------public-----------------------------------------------------
--打开活动界面 
-- slua.Do Module.Activity:ShowActivityPanel(1,1)
---ActivityModule.ShowActivityPanel Description of the function
---@param _groupID   integer 一级页签
---@param activityID integer 二级页签
---@param bSendLog   boolean 是否上传经分 默认为true
---@param bShowCurMode boolean 是否只显示当前模式 默认为true
function ActivityModule:ShowActivityPanel(_groupID, activityID, bSendLog, bShowCurMode)
    bSendLog = setdefault(bSendLog, true)
    bShowCurMode = setdefault(bShowCurMode, true)

    Module.Activity.Field:SetIsFilterByModeOn(bShowCurMode)
    return ActivityLogic.ShowActivityPanelProcess(_groupID, activityID, bSendLog)
end

-- //调试接口
function ActivityModule:ShowActPerspective(flag)
    if flag == nil then
        return
    end
    Module.CommonTips:ShowSimpleTip("设置成功, 重新打开活动面板生效")
    return ActivityLogic.ShowActPerspectiveProcess(flag)
end

function ActivityModule:ClearUserConfigKey()
    Module.CommonTips:ShowSimpleTip("清除成功")
    ActivityLogic.ClearUserConfigKey()
end

-- //end

-- 订阅红点处理
function ActivityModule:_SubscribeEvent(isBool)
    if isBool then
        self:AddLuaEvent(Module.GVoice.Config.Events.evtOnGVoiceApplicationActive, self._OnResumeApp, self)
        self:AddLuaEvent(Server.ActivityServer.Events.evtEnableRewardNTFCall, self._OnEnableRewardNTFCall, self)
        self:AddLuaEvent(Server.ActivityServer.Events.evtActivityUpdateRedDot, self._OnActivityUpdateRedDot, self)
        self:AddLuaEvent(Server.ActivityServer.Events.evtActivityDataUpdateGeneral, self._OnActivityDataUpdateGeneral, self)
    else
        self:RemoveLuaEvent(Module.GVoice.Config.Events.evtOnGVoiceApplicationActive, self)
        self:RemoveLuaEvent(Server.ActivityServer.Events.evtEnableRewardNTFCall, self)
        self:RemoveLuaEvent(Server.ActivityServer.Events.evtActivityUpdateRedDot, self)
        self:RemoveLuaEvent(Server.ActivityServer.Events.evtActivityDataUpdateGeneral, self)
    end
end

---@param optActivityInfos pb_ActivityInfo[]?
function ActivityModule:_OnActivityUpdateRedDot(optActivityInfos)
    if not optActivityInfos then
        ActivityRedDotLogic.ProcessAllActivityInfo(Server.ActivityServer.AllActivityInfos)
    else
        for _, activityInfo in pairs(optActivityInfos) do
            ActivityRedDotLogic.ProcessActivityInfo(activityInfo)
        end
    end
end

function ActivityModule:_OnEnableRewardNTFCall(...)
    Module.Reward:EnableNTFCall(...)
end

function ActivityModule:_OnActivityDataUpdateGeneral(key, res)
    if key == GeneralHelperTool.GetKey("ErrorTipIfNeeded") then
        if res then
            self:ShowErrorTipIfNeeded(res.expand_info)
        end
    end
end

function ActivityModule:_OnResumeApp(bActive)
    if bActive then
        self:CheckSubscribe()
    end
end

function ActivityModule:CheckSubscribe(eventId)
    if eventId then
        self._eventId = eventId
    end
    if self._eventId then
        local GameFunc = function(eventId, status)
            Server.ActivityServer:ReddotSubscribe(eventId, status or false)
        end
        Module.Subscribe:CheckGameEventStatus(self._eventId, GameFunc)
    end
end

--通过活动类型跳转到活动
function ActivityModule:JumpActvByActvType(actv_type, bActiveGlobal)
    --不传默认合作物流
    if actv_type == nil then
        actv_type = ActivityType.ActivityTypeRaidLotteryTemplate
    end
    local tab1, tab2 = Server.ActivityServer:GetTabByActvType(actv_type, bActiveGlobal)
    if tab1 == nil or tab2 == nil then
        Module.CommonTips:ShowSimpleTip(Module.Activity.Config.Loc.NoRelatedActivitiesAvailable)
        return
    end
    self:ShowActivityPanel(tab1, tab2)
end

function ActivityModule:CloseMainPanel()
    --活动ui关闭,处理其他ui
    Facade.UIManager:CloseAllPopUI()
    return ActivityLogic.CloseMainPanelProcess()
end

function ActivityModule:SetUserBoolean(key, value)
    return ActivityLogic.SetUserBoolean(key, value)
end

function ActivityModule:GetUserBoolean(key, default)
    return ActivityLogic.GetUserBoolean(key, default)
end

--添加红点
function ActivityModule:GetActivityRedDot()
    local rewardreddot,normalreddot = Module.Activity.Field:ReddotRefresh()
    return rewardreddot,normalreddot
end

-- 打开物流凭证界面
function ActivityModule:OpenLogisticsVoucherPanel(currencyId)
    Facade.UIManager:AsyncShowUI(UIName2ID.ActivityLogisticsVoucherPanel, nil, nil, currencyId)
end

-- 打开魔塔Loading界面
function ActivityModule:OpenMagicTowerLevelLoading(activityID)
    Facade.UIManager:AsyncShowUI(UIName2ID.ActivityMagicTowerLevelLoading, nil, nil,activityID)
end
-- 打开魔塔局内界面
function ActivityModule:OpenMagicTowerPanel()
    Facade.UIManager:AsyncShowUI(UIName2ID.ActivityMagicTowerMainPanel, nil, nil)
end

function ActivityModule:SwitchMagicTowerLevel(inlevel)
    Module.Activity.Config.evtMagicTowerSwitchLevel:Invoke(inlevel)
end

function ActivityModule:OnProcessMagicTowerAstarPath(x, y)
    Module.Activity.Config.evtProcessMagicTowerAstarPath:Invoke(x, y)
end

function ActivityModule:OnMagicTowerOpenSafePanel(treasureID, safeId)
    Module.Activity.Config.evtMagicTowerOpenSafePanel:Invoke(treasureID, safeId or 1)
end

function ActivityModule:OnProcessTowerInput(moveDirection)
    -- Module.Activity.Config.evtMagicTowerMoveReq:Invoke(moveDirection)
end

function ActivityModule:OnMagicTowerMoveFinished()
    Module.Activity.Config.evtMagicTowerMoveFinish:Invoke()
end

function ActivityModule:SetActivityBrowsed(activityID)
    return Server.ActivityServer:SetActivityBrowsed(activityID)
end

function ActivityModule:IsActivityBrowsed(activityID)
    return Server.ActivityServer:IsActivityBrowsed(activityID)
end

--- 错误提示处理模块方法
function ActivityModule:ShowErrorTipIfNeeded(expandInfo)
    if not expandInfo or not expandInfo.tips_id then return end

    local tipStr = ActivityLogic.GetErrorTipText(expandInfo.tips_id)
    if not tipStr then return end

    Module.CommonTips:ShowSimpleTip(tipStr)
end

---@param optAssociatedActivityID integer
function ActivityModule:ShowDataChangeAwards(dataChange, optAssociatedActivityID)
    if not dataChange then return end
    Module.ItemDetail:CloseAllPopUI()
    if dataChange ~= nil and dataChange.prop_changes ~= nil and #dataChange.prop_changes > 0 or dataChange.currency_changes ~= nil and #dataChange.currency_changes > 0 then
        local itemList = {}
        local propNum = #dataChange.prop_changes
        for i = 1, propNum, 1 do
            local propInfo = dataChange.prop_changes[i].prop
            if propInfo.num > 0 then
                local giftItem = ItemBase:New(propInfo.id, propInfo.num, propInfo.gid, false)
                giftItem:SetRawPropInfo(propInfo)
                table.insert(itemList, giftItem)
            end
        end
        local currency_changes = dataChange.currency_changes
        for _, currencyChange in ipairs(currency_changes) do
            if currencyChange.delta > 0 then
                local item = ItemBase:New(currencyChange.currency_id, currencyChange.delta)
                table.insert(itemList, item)
            end
        end
        local rewardPopPanelTypeList = ActivityLogic._OnGetRewardPopPanelTypeList(itemList)

        local popTitle
        if optAssociatedActivityID then
            popTitle = (Server.ActivityServer:GetActivityInfoByActivityID(optAssociatedActivityID) or {}).name
        end
        Module.Reward:OpenRewardPanel(popTitle, nil, itemList, nil, nil, nil, true, rewardPopPanelTypeList)
    end
end

----------------------------------------------------------------------------------------------------------------

local function max(a, b) return (a > b) and a or b end

function ActivityModule:_ToggleArknightsMusic(bEnable)
    local states = {
        stopped         = 0,
        stopToStart     = 1,
        started         = 2,
        startToStop     = 3,
    }

    -- 由于某些奇特原因 stop 事件和 start 事件连续触发，后一个 start 事件无法生效
    -- 中间至少需要间隔这个时间
    local STOP_TO_START_INTERVAL = 2.5

    -- 避免某些情况下，上一个界面关闭音乐，下一个界面立即再打开音乐，导致音乐断开从头播放
    -- 忽略在这个时间以内的 start -> stop -> start 操作（引入少量音频切换延迟）
    local START_TO_STOP_INTERVAL = 0.2

    self._arkMusicState = self._arkMusicState or 0
    self._arkMusicLastStop  = self._arkMusicLastStop or 0

    if self._arkMusicState == states.stopped then
        if bEnable == true then
            -- stop 到 start 至少要间隔 STOP_TO_START_INTERVAL 秒
            local delay = max(STOP_TO_START_INTERVAL - (os.clock() - self._arkMusicLastStop), 0)

            if delay == 0 then
                -- 间隔已满，直接切换
                self._arkMusicState = states.started
                Facade.SoundManager:PlayMusicByName("Music_Lobby_MingRiFangZhou_Start")
            else
                -- 间隔不足，先进入中间状态，可以切换后再切换
                self._arkMusicState = states.stopToStart
                self._arkMusicTimer = Timer.DelayCall(delay,
                    function()
                        self._arkMusicTimer = nil
                        self._arkMusicState = states.started
                        Facade.SoundManager:PlayMusicByName("Music_Lobby_MingRiFangZhou_Start")
                    end
                )
            end
        end
    elseif self._arkMusicState == states.started then
        --  进入中间状态等待 START_TO_STOP_INTERVAL 秒，如果期间没有重新 start 的请求，才实际 stop
        if bEnable == false then
            self._arkMusicState = states.startToStop
            self._arkMusicTimer = Timer.DelayCall(START_TO_STOP_INTERVAL,
                function()
                    self._arkMusicTimer = nil
                    self._arkMusicLastStop = os.clock()
                    Facade.SoundManager:PlayMusicByName("Music_Lobby_MingRiFangZhou_Stop")
                    self._arkMusicState = states.stopped
                end
            )
        end
    elseif self._arkMusicState == states.startToStop then
        -- 即将 stop 的中间状态，重新请求 start，仅取消转换状态
        if bEnable == true then
            self._arkMusicState = states.started
            Timer.CancelDelay(self._arkMusicTimer)
            self._arkMusicTimer = nil
        end
    elseif self._arkMusicState == states.stopToStart then
        -- 即将 start 的中间状态，重新请求 stop，仅取消转换状态
        if bEnable == false then
            self._arkMusicState = states.stopped
            Timer.CancelDelay(self._arkMusicTimer)
            self._arkMusicTimer = nil
        end
    end
end


return ActivityModule
