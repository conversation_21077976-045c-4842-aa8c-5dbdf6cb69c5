----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMReward)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class VehicleGainPop : LuaUIBaseView
local RewardBaseView = require "DFM.Business.Module.RewardModule.UI.RewardBaseView"
local VehicleGainPop = ui("VehicleGainPop", RewardBaseView)
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local CollectionLogic = require "DFM.Business.Module.CollectionModule.Logic.CollectionLogic"
local EGPInputModeType = import "EGPInputModeType"
local VehicleHelperTool = require "DFM.StandaloneLua.BusinessTool.VehicleHelperTool"
local RewardDetail = require "DFM.Business.Module.RewardModule.UI.RewardDetail"

function VehicleGainPop:Ctor()
    self._wtSkipBtn = self:Wnd("wtCommonButtonV1S2", DFCommonButtonOnly)
    self._wtSkipBtn:Event("OnClicked", self._OnSkipBtnClick, self)
    self._wtSkipAllBtn = self:Wnd("WBP_CommonButtonV3S1", DFCommonButtonOnly)
    self._wtSkipAllBtn:Event("OnClicked", self._OnSkipAllBtnClick, self)
    self._wtDetail = self:Wnd("WBP_CommonSpecialAcquisition", RewardDetail)
    self._wtApplyBtn = self:Wnd("wtCommonButtonV1S1", CommonButton)
    self._wtShareBtn = self:Wnd("ShareBtn", UIButton)
    self._wtShareBtn:Collapsed()
    -- Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.ETopBarStyleFlag.None)
    -- Module.CommonBar:SetTopAndBottomBarVisible(false)
end

function VehicleGainPop:OnInitExtraData(vehicleskinItem)
    if vehicleskinItem then 
        self._vehicleInfo = vehicleskinItem
    else
        self:Collapsed()
    end
end


function VehicleGainPop:OnOpen()
    self:_AddListeners()
end

function VehicleGainPop:OnClose()
    -- if self._hActionSkip then
    --     self:RemoveInputActionBinding(self._hActionSkip)
    --     self._hActionSkip = nil
    -- end
    self:RemoveAllLuaEvent()
    if self._bExecuteClose ~= true then
        self._bExecuteClose = true
        Module.Reward:ShowNextRewards()
    end
end

function VehicleGainPop:OnShow()
    -- if not self._hActionSkip then
    --     self._hActionSkip =
    --         self:AddInputActionBinding(
    --         "JumpOver",
    --         EInputEvent.IE_Pressed,
    --         self._OnSkipBtnClick,
    --         self,
    --         EDisplayInputActionPriority.UI_POP
    --     )
    -- end
    -- self:SetCPPValue("WantedInputMode", EGPInputModeType.GameAndUI)
    self._wtShareBtn:Collapsed()
    self._wtApplyBtn:Collapsed()
    self:_OnRefreshItemDetail()
    self:SetCPPValue("WantedInputMode", EGPInputModeType.GameAndUI)
end


function VehicleGainPop:OnHide()
    Module.Vehicle:ProcessDisposeByActorID(1)
end


function VehicleGainPop:OnShowBegin()
end


function VehicleGainPop:OnHideBegin()
end


function VehicleGainPop:OnAnimFinished(anim)
    if anim == self.WBP_Vehicle_Obtain_in then
       
    elseif anim == self.WBP_Vehicle_Obtain_out then
        if self._bExecuteClose ~= true then
            self._bExecuteClose = true
            Module.Reward:ShowNextRewards(self._bTabPressed == true)
        end
    end
end


function VehicleGainPop:_AddListeners()
    self:AddLuaEvent(LuaGlobalEvents.evtSceneLoaded, self._OnRefreshModel, self)
end



function VehicleGainPop:_OnRefreshItemDetail()
    if self._vehicleInfo then 
        self._wtSkipBtn:SetMainTitle(Module.Hero.Config.Loc.HeroContinue)
        self._wtSkipBtn:SetIsEnabled(true)
        self._wtSkipAllBtn:Collapsed()
        local vehicleId = self._vehicleInfo.id
        -- local itemId = VehicleHelperTool.GetItemIdBySkinID(vehicleSkinId)
        -- local vehicleId = VehicleHelperTool.GetVehicleIdBySkinID(vehicleSkinId)
        self:_SetDisplayVehicle(vehicleId)
        self:PlayAnimation(self.WBP_Vehicle_Obtain_in, 0, 1, EUMGSequencePlayMode.Forward, 1, true)
        local vehicleName = VehicleHelperTool.GetVehicleNameByID(vehicleId)
        local vehicleDesc = VehicleHelperTool.GetVehicleDescByVehicleId(vehicleId)
        self._wtDetail:SetRewardInfo(Module.Reward.Config.RewardDetailType.VEHICLEANDCARD,Module.Reward.Config.Loc.GainVehicle,vehicleName,vehicleDesc,ItemConfigTool.GetItemQuality(vehicleSkinId),true,true,self._OnSkipBtnClick,self)
    end
end

function VehicleGainPop:_SetDisplayVehicle(id)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallVehicle, "SetVehicleDisplayType", id, 1)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallVehicle, "LoadVehicleLevel", id, id)
    Module.Vehicle:ProcessDisposeByActorID(1)
    local bIsVehicleSkin = self:_InternalIsVehicleSkin(id)
    if bIsVehicleSkin then
        Module.Vehicle:LoadVehicleFromSkinID(id, 1)
        return
    end
    Module.Vehicle:LoadStandardVehicleFromVehicleID(id, 1)
end

function VehicleGainPop:_InternalIsVehicleSkin(id)
    local itemMainType = ItemHelperTool.GetMainTypeById(id)
    return itemMainType == EItemType.VehicleSkin
end

function VehicleGainPop:_OnRefreshModel(curSubStageType)
    if curSubStageType == ESubStage.HallVehicle then
        if self._vehicleInfo then
            self:_SetDisplayVehicle(self._vehicleInfo.id)
        end
    end
end


function VehicleGainPop:_OnSkipBtnClick()
    self:HandleTransition(true)
    Facade.SoundManager:StopUIAudioEvent("UI_Common_Popup")
    self:PlayAnimation(self.WBP_Vehicle_Obtain_out, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
end



function VehicleGainPop:_OnSkipAllBtnClick()
    self._vehicleInfo = {}
    Module.Reward:SkipAllSpecialPanels()
    self:StopAnimation(self.WBP_Vehicle_Obtain_in)
    Facade.SoundManager:StopUIAudioEvent("UI_Common_Popup")
    self:PlayAnimation(self.WBP_Vehicle_Obtain_out, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
end

return VehicleGainPop
