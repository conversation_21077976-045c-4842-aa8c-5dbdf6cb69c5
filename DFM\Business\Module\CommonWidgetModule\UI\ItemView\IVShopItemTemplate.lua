----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCommonWidget)
----- LOG FUNCTION AUTO GENERATE END -----------



local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig" 
local IVTemplateBase = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.IVTemplateBase"
local IVTextIconComponent = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.Components.IVTextIconComponent"
local IVMainIconComponent = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.Components.IVMainIconComponent"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ItemDetailConfig = require("DFM.Business.Module.ItemDetailModule.ItemDetailConfig")
local EComp = CommonWidgetConfig.EIVWarehouseTempComponent
local EIVSlotPos = CommonWidgetConfig.EIVSlotPos
local EIVCompOrder = CommonWidgetConfig.EIVCompOrder
local FAnchors = import "Anchors"
local commonOffset = FMargin(0, 0, 0, 0)
local commonAnchor = FAnchors()
commonAnchor.Minimum = LuaGlobalConst.TOP_LEFT_VECTOR
commonAnchor.Maximum = LuaGlobalConst.BOTTOM_RIGHT_VECTOR

---@class IVShopItemTemplate : IVTemplateBase
local IVShopItemTemplate = ui("IVShopItemTemplate", IVTemplateBase)

local function log(...)
    loginfo("[IVShopItemTemplate]", ...)
end

function IVShopItemTemplate:Ctor()
    self._bBindCommonItemClickEvent = false

    self._wtBtnItem = self:Wnd("wtButton_Item", UIButton)
    self._wtBtnItem:Event("OnClicked", self.OnBtnItemClick, self)
    self._wtBtnItem:Event("OnHovered", self.OnBtnItemHover, self)
    self._wtItemIcon = self:Wnd("wtItemIcon", IVMainIconComponent)
	self._wtItemBg = self:Wnd("wtItemBg", UIWidgetBase)
	self._wtItemQuality = self:Wnd("wtItemQuality", UIWidgetBase)

    self._wtTopLeftIconText = self:Wnd("wtTopLeftIconText", IVTextIconComponent)
    self._wtTopLeftIconText:SetComponentPos(EIVSlotPos.TopLeft)
    self._wtBottomLeftIconText = self:Wnd("wtBottomLeftIconText", IVTextIconComponent)
    self._wtBottomLeftIconText:SetComponentPos(EIVSlotPos.BottomLeft)
    self._wtBottomRightIconText = self:Wnd("wtBottomRightIconText", IVTextIconComponent)
    self._wtBottomRightIconText:SetComponentPos(EIVSlotPos.BottomRight)

    self._wtMaskCanvas = self:Wnd("CanvasPanel_mask", UIWidgetBase)

    self.itemviewMode = CommonWidgetConfig.EIVItemViewMode.ShopItemView

    self.WBP_SlotItemView = self:Wnd("WBP_SlotItemView", UIWidgetBase)

    self._wtTipAnchor = UIUtil.WndTipsAnchor(self, "wtTipAnchor", self.OnShowInstruction, self.OnHideInstruction)

    self._wtLightEffectSlot = self:Wnd("LightEffectSlot", UIWidgetBase)


    self:_InitDefaultInteractive()
    self:_InitialSetupComponents()

    self._fClickCallbackIns = nil

    self._timerHandle = nil
    self._fOnTimerUpdate = nil
    self._bShowInstruction = false

    --BEGIN MODIFICATION @ VIRTUOS : 绑定回调
    --该事件触发不了 待查
    --[[
    if IsHD() then
        self:Event("OnFocusReceivedEvent", self._OnFocusReceived, self)
    end
    --]]
    --END MODIFICATION
end
-----------------------------------------------------------------------
--region Private
-- BEGIN MODIFICATION @ VIRTUOS
function IVShopItemTemplate:BindFocusReceivedCallback(focusReceivedCallbackIns)
    self._fFocusReceivedCallbackIns = focusReceivedCallbackIns
end

function IVShopItemTemplate:_OnFocusReceived()
    if self._fFocusReceivedCallbackIns then
        self._fFocusReceivedCallbackIns()
    end
end
-- END MODIFICATION

function IVShopItemTemplate:SetClickIsIgnored(bInIgnore)
    self._wtBtnItem:SetClickIsIgnored(bInIgnore)
end


function IVShopItemTemplate:_InitDefaultInteractive()
	-- self:SetCppValue("bHandleClick", true)
    --- 开启后会导致页签切换后第一次点击失效
    -- self:SetCppValue("bPreciseClick", true)
    local slotItemView = self.WBP_SlotItemView
    -- slotItemView:SetVisibility(ESlateVisibility.HitTestInvisible)
    self.itemviewPanel:SetVisibility(ESlateVisibility.HitTestInvisible)
end

function IVShopItemTemplate:_InitialSetupComponents()
    self:RegisterComponent(EComp.ItemIcon, self._wtItemIcon)
    self:RegisterComponent(EComp.ItemBg, self._wtItemBg)
    self:RegisterComponent(EComp.ItemQuality, self._wtItemQuality)

    self:RegisterComponent(EComp.TopLeftIconText, self._wtTopLeftIconText)
    self:RegisterComponent(EComp.BottomLeftIconText, self._wtBottomLeftIconText)
    self:RegisterComponent(EComp.BottomRightIconText, self._wtBottomRightIconText)

    self._wtTopLeftIconText:SetStyle(CommonWidgetConfig.EIVIconTextStyle.ItemName)
    -- self._wtBottomLeftIconText:SetStyle(CommonWidgetConfig.EIVIconTextStyle.None)
    -- self._wtBottomRightIconText:SetStyle(CommonWidgetConfig.EIVIconTextStyle.None)
    -- self._wtItemIcon:SetOffsets(FMargin(31, 13, 31, 13))
end

--endregion
-----------------------------------------------------------------------

--==================================================
--region Life function
function IVShopItemTemplate:OnOpen()
	if self._bBindCommonItemClickEvent then
        self:AddLuaEvent(CommonWidgetConfig.Events.evtItemSetSelected, self.SetSelected, self)
        self:AddLuaEvent(CommonWidgetConfig.Events.evtInGameItemSelected, self.SetSelected, self)
    end
end

function IVShopItemTemplate:AddShopSelectedEvent()
    self:AddLuaEvent(CommonWidgetConfig.Events.evtItemSetSelected, self.SetItemSelected, self)
    self:AddLuaEvent(CommonWidgetConfig.Events.evtInGameItemSelected, self.SetItemSelected, self)
end

function IVShopItemTemplate:RemoveShopSelectedEvent()
    self:RemoveLuaEvent(CommonWidgetConfig.Events.evtItemSetSelected, self.SetItemSelected)
    self:RemoveLuaEvent(CommonWidgetConfig.Events.evtInGameItemSelected, self.SetItemSelected)
end

function IVShopItemTemplate:OnClose()
    self._wtTipAnchor:HideTipsManually()
	self:RemoveAllLuaEvent()
    if self._fClickCallbackIns then
        self._fClickCallbackIns = nil
    end
    self:ReleaseTimer()
end
function IVShopItemTemplate:OnShow()
    -- Timer.DelayCall(0.5, function ()
    --     self:PlayAnimationForward(self.WBP_ShopItemTemplate_newin,1.0,true)
    -- end, self)
end
function IVShopItemTemplate:OnHide()
    -- self:RemoveShopDisableMask()
end

---@type templateParams IVWarehouseTemplateParams
function IVShopItemTemplate:OnInitExtraData(templateParams)
	if not templateParams then
		return
	end

	if templateParams.bBindCommonItemClickEvent ~= nil then
		self._bBindCommonItemClickEvent = templateParams.bBindCommonItemClickEvent
	end
end

function IVShopItemTemplate:OnClicked()
    self._bCanShowInstruction = false
    if self._fDoubleClickCallbackIns then
        if self._clickDelayHandle then
            Timer.CancelDelay(self._clickDelayHandle)
            self:OnDoubleClicked()
        else
            Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UILobbyWHClick)
            self._clickDelayHandle = Timer.DelayCall(0.2, self.OnShowDetail, self)
        end
    else
        self:OnShowDetail()
    end
    self:OnSingleClicked()
end

function IVShopItemTemplate:OnSingleClicked()
    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UILobbyWHClick)
    if self._fClickCallbackIns then
        self._fClickCallbackIns()
    end
end

function IVShopItemTemplate:OnDoubleClicked()
    if self._fDoubleClickCallbackIns then
        self._wtTipAnchor:HideTipsManually()
        self._fDoubleClickCallbackIns()
    end
    self._bCanShowInstruction = true
    self._clickDelayHandle = nil
end

function IVShopItemTemplate:OnShowDetail()
    if self._fDetailCallbackIns then
        self._wtTipAnchor:HideTipsManually()
        self._fDetailCallbackIns()
    end
    self._bCanShowInstruction = true
    self._clickDelayHandle = nil
end

function IVShopItemTemplate:OnDragDetected(inGeometry, inDragDropEvent, operation)
    if self._fDragCallbackIns then
        local previewItem = Module.CommonWidget:GetOrCreateDragItemView()
        Module.CommonWidget:SetCurDragingItemView(previewItem)
        local geometry = self:GetCachedGeometry()
        local viewSize = geometry:GetLocalSize()
        --- 拖拽的物品与原物品保持同样大小
        -- viewSize.X = viewSize.X * IVWarehouseTemplate.TRIGGER_DRAG_SCALE.X
        -- viewSize.Y = viewSize.Y * IVWarehouseTemplate.TRIGGER_DRAG_SCALE.Y
        if Module.CommonWidget:IsInMultiSelectedMode() then
            previewItem:SetMultipleItems(#Module.CommonWidget:GetSelectedItems(), viewSize.X, viewSize.Y)
        else
            previewItem:SetItem(self.item, viewSize.X, viewSize.Y)
        end
        operation.WidgetReference = self
        operation.DefaultDragVisual = previewItem
        self._fDragCallbackIns()
    end
end

function IVShopItemTemplate:OnDragEnd()
    if self._fDragEndCallbackIns then
        self._fDragEndCallbackIns()
    end
end

function IVShopItemTemplate:OnBtnItemClick()
    self:OnClicked()
end

function IVShopItemTemplate:OnBtnItemHover()
    if self._fHoverCallbackIns then
        self._fHoverCallbackIns()
    end
end

function IVShopItemTemplate:SetButtonEnable(bEnable)
    self._wtBtnItem:SetVisibility(bEnable and ESlateVisibility.Visible or ESlateVisibility.Collapsed)
end

function IVShopItemTemplate:BindClickCallback(fClickCallbackIns)
    self._fClickCallbackIns = fClickCallbackIns
end

function IVShopItemTemplate:BindHoverCallback(fHoverCallbackIns)
    self._fHoverCallbackIns = fHoverCallbackIns
end

function IVShopItemTemplate:BindDoubleClickCallback(fClickCallbackIns)
    self._fDoubleClickCallbackIns = fClickCallbackIns
end

function IVShopItemTemplate:BindDetailCallback(fDetailCallbackIns)
    self._fDetailCallbackIns = fDetailCallbackIns
end

function IVShopItemTemplate:BindDragCallback(fDragCallbackIns)
    self._fDragCallbackIns = fDragCallbackIns
end

function IVShopItemTemplate:BindDragEndCallback(fDragEndCallbackIns)
    self._fDragEndCallbackIns = fDragEndCallbackIns
end

function IVShopItemTemplate:SetGivenInstructionContents(givenInstructionContents)
    self._givenInstructionContents = givenInstructionContents
end

---@param item ItemBase
function IVShopItemTemplate:SetSelected(item, bSelected)
    local bSelfSelected = false
    if item and self.item == item and bSelected then
        bSelfSelected = true
    else
        bSelfSelected = bSelected
    end

    -- IVTemplateBase.SetSelected(self, bSelfSelected)

    self:_SelfSetSelected(bSelfSelected)
end

---@param item ItemBase
function IVShopItemTemplate:SetItemSelected(item, bSelected)
    local bSelfSelected = false
    if item and self.item == item and bSelected then
        bSelfSelected = true
    end

    self:_SelfSetSelected(bSelfSelected)
end

--endregion
-----------------------------------------------------------------------

--==================================================
--region Public API

function IVShopItemTemplate:RefreshView()
    self:_InitialSetupComponents()

    IVTemplateBase.RefreshView(self)
end

function IVShopItemTemplate:_SelfSetSelected(bSelected)
    if bSelected then
        ---@type IVComponentBase
        local selectedComp = self:FindOrAdd(EComp.ItemSelected, UIName2ID.IVItemSelectedComponent, EIVSlotPos.BgLayer)
        if selectedComp then
            selectedComp:PlayAnim()
        else
            logerror("selectedComp is nil, pls check")
        end
        local mainIconComp = self:GetComponent(EComp.ItemIcon)
        if mainIconComp.PlaySelectedAnim then
            mainIconComp:PlaySelectedAnim()
        end
    else

    end

    self:EnableComponent(EComp.ItemSelected, bSelected)
end

function IVShopItemTemplate:ClearMaskChildren()
    if self._wtMaskCanvas then
        UILightWidget.PureClearChildren(self._wtMaskCanvas)
    end
end

function IVShopItemTemplate:RemoveShopDisableMask()
    Facade.UIManager:RemoveSubUIByParent(self, self._wtMaskCanvas)
end

function IVShopItemTemplate:AddChildToMask(subUINavID)
    subUINavID = setdefault(subUINavID, UIName2ID.IVItemSoldOut)
    -- self._wtMaskCanvas:AddChild(wtChild)
    -- local canvasSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(wtChild)
    -- canvasSlot:SetOffsets(commonOffset)
    -- canvasSlot:SetAnchors(commonAnchor)

    self:RemoveShopDisableMask()
    local disableMask, instanceId = Facade.UIManager:AddSubUI(self, subUINavID, self._wtMaskCanvas)
    disableMask = getfromweak(disableMask)
    local canvasSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(disableMask)
    canvasSlot:SetOffsets(commonOffset)
    canvasSlot:SetAnchors(commonAnchor)

    return disableMask
end

function IVShopItemTemplate:GetMaskCanvas()
    return self._wtMaskCanvas
end


local ETextStyle = {
    Default = 0,
    Equipment = 1,
    ItemName = 2,
    AuctionText = 3,
}


function IVShopItemTemplate:InitCollectionMandelBrickItem(item)
    self:ChangeDefaultMode(CommonWidgetConfig.EIVItemViewMode.ListItemView)
    self:InitItem(item)
    local topPropId
    if isvalid(item) then
        topPropId = Module.Collection:GetMandelBrickTopPrizeItemId(item.id)
    end
    if topPropId ~= nil then
        local topPrizeIconComp = self:FindOrAdd(EComp.SecondaryIcon, UIName2ID.IVMainIconComponent, EIVSlotPos.IconLayer)
        local topPrizeItem = ItemBase:New(topPropId)
        topPrizeIconComp:BindItem(topPrizeItem)
        topPrizeIconComp:RefreshComponent()
    end
    self:EnableComponent(EComp.SecondaryIcon, topPropId ~= nil)
    local NumIconTextComp = self:FindOrAdd(EComp.BottomRightIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomRight, EIVCompOrder.Order1)
    NumIconTextComp:ShowTextOnly(item.num or 0)
    NumIconTextComp:SetCornerStyle()

    -- 更改曼德尔砖位置
    local itemIconComp = self:GetComponent(EComp.ItemIcon)
    local canvasSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(itemIconComp)
    local bottomAnchor = FAnchors()
    bottomAnchor.Minimum = LuaGlobalConst.BOTTOM_LEFT_VECTOR
    bottomAnchor.Maximum = LuaGlobalConst.BOTTOM_LEFT_VECTOR
    canvasSlot:SetAnchors(bottomAnchor)
    canvasSlot:SetOffsets(FMargin(10, -20, 80, 80))
    canvasSlot:SetAlignment(FVector2D(0, 1))
    itemIconComp:SetIconOffset(FMargin(0, 0, 0, 0))

    self:EnableComponent(EComp.BottomRightIconText, true)
    local maskLockComp = self:FindOrAdd(EComp.GreyMask, UIName2ID.IVMaskLockComponent, EIVSlotPos.MaskLayer)
    self:EnableComponent(EComp.GreyMask, false)
    -- 设置名字
    local nameComp = self:FindOrAdd(EComp.TopLeftTwoLineText, UIName2ID.IVTextQualityComponent, EIVSlotPos.TopLeft)
    nameComp:RefreshComponent()
    self:EnableComponent(EComp.TopLeftIconText, false)
    self:EnableComponent(EComp.TopLeftTwoLineText, true)
    self:SetAnchorIfOverFlow()
end

function IVShopItemTemplate:InitCollectionWeaponSkinItem(item, baseWeaponId, bOwned, bApplied, instanceNum, rightsType, bInRandomPool)
    self:ChangeDefaultMode(CommonWidgetConfig.EIVItemViewMode.ListItemView)
    self:EnableComponent(EComp.TopLeftIconText, false)
    local itemNameComp = self:FindOrAdd(EComp.TopRightItemNameText, UIName2ID.IVTextQualityComponent, EIVSlotPos.TopLeft)
    itemNameComp:SetBaseWeaponName(false)
    self:EnableComponent(EComp.TopRightItemNameText, true)
    local appliedComp = self:FindOrAdd(EComp.TopRightIconText, UIName2ID.IVUsingComponent, EIVSlotPos.TopRight, EIVCompOrder.Order2)
    self:EnableComponent(EComp.TopRightIconText, bApplied and (bOwned or rightsType and rightsType > 0))
    local inRandomPoolComp = self:FindOrAdd(EComp.RandomSkin, UIName2ID.IVRandomSkinComponent, EIVSlotPos.TopRight, EIVCompOrder.Order2)
    self:EnableComponent(EComp.RandomSkin, not bApplied and bInRandomPool and (bOwned or rightsType and rightsType > 0))
    local maskLockComp = self:FindOrAdd(EComp.GreyMask, UIName2ID.IVMaskLockComponent, EIVSlotPos.MaskLayer, EIVCompOrder.MaskLayerOrder)
    maskLockComp._wtDesc:Collapsed()
    self:EnableComponent(EComp.GreyMask, not bOwned and (not rightsType or rightsType == 0))
    local weaponItem = nil
    if baseWeaponId ~= nil then
        weaponItem = ItemBase:New(baseWeaponId)
        local weaponDesc, partIndexs = WeaponHelperTool.GetWeaponDescriptionFromSkinID(item.id)
        if isvalid(weaponDesc) then
            if item.gid ~= 0 then
                WeaponAssemblyTool.SetWeaponDescriptionSkinInfoFromPropInfo(weaponDesc, item:GetRawPropInfo())
            end
            weaponItem:SetRawDescObj(weaponDesc)
        end
    end
    self:InitItem(weaponItem or item)
    if rightsType == ERightsType.Cybercafe then
        self._wtBottomRightIconText:ShowIconOnly("PaperSprite'/Game/UI/UIAtlas/System/Hall/BakedSprite/Hall_Icon_33.Hall_Icon_33'")
    elseif rightsType == ERightsType.University then
        self._wtBottomRightIconText:ShowIconOnly("PaperSprite'/Game/UI/UIAtlas/System/Hall/BakedSprite/Hall_Icon_32.Hall_Icon_32'")
    end
    self._wtBottomRightIconText:SetCornerStyle()
    self._wtBottomRightIconText:RefreshComponent()
    self:EnableComponent(EComp.BottomRightIconText, rightsType and rightsType ~= ERightsType.None)
    if ItemHelperTool.IsMysticalSkin(item.id) then
        local iconPath = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Market_Icon_0002.CommonHall_Market_Icon_0002'"
        self._wtBottomLeftIconText:ShowIconAndText(iconPath, bOwned == true and StringUtil.Key2StrFormat(Module.CommonWidget.Config.Loc.MysticalInstanceNum, {["InstanceNum"] = tostring(instanceNum or 0)}) or Module.CommonWidget.Config.Loc.Mystical)
        self._wtBottomLeftIconText:SetCornerStyle()
        self._wtBottomLeftIconText:RefreshComponent()
        self:EnableComponent(EComp.BottomLeftIconText, true)
    end
    self:SetAnchorIfOverFlow()
end

function IVShopItemTemplate:InitCollectionRandomSkinItem(item, bRandomGroupApplied)
    self:ChangeDefaultMode(CommonWidgetConfig.EIVItemViewMode.ListItemView)
    self._wtItemQuality:Collapsed()
    local selectedComp = self:FindOrAdd(EComp.TopRightIconText, UIName2ID.IVUsingComponent, EIVSlotPos.TopRight, EIVCompOrder.Order2)
    self:EnableComponent(EComp.TopRightIconText, bRandomGroupApplied)
    local itemNameComp = self:FindOrAdd(EComp.TopRightItemNameText, UIName2ID.IVTextQualityComponent, EIVSlotPos.TopLeft)
    itemNameComp:SetBaseWeaponName(item.name)
    itemNameComp:RefreshComponent()
    itemNameComp:ClearLinkageType()
    itemNameComp:SetMainTxt(StringUtil.Key2StrFormat(Module.Collection.Config.Loc.RandomSkinItemTitle,
        {["FirearmName"] = item.name
    }))
    self._wtItemIcon:SetIconFromPath("PaperSprite'/Game/UI/UIAtlas/CommonHall/Sp/CommonHall_SP_01.CommonHall_SP_01'")
    self:EnableComponent(EComp.ItemIcon, true)
    self:EnableComponent(EComp.TopRightItemNameText, true)
    self:EnableComponent(EComp.TopLeftIconText, false)
    self:EnableComponent(EComp.BottomRightIconText, false)
    self:EnableComponent(EComp.BottomLeftIconText, false)
    self:EnableComponent(EComp.GreyMask, false)
    self:EnableComponent(EComp.RandomSkin, false)
    self:SetAnchorIfOverFlow()
end

function IVShopItemTemplate:InitCollectionCamouflageItem(info, bIsMasterChallenge, currentNum, maxNum, bActivated)
    self:ChangeDefaultMode(CommonWidgetConfig.EIVItemViewMode.ListItemView)
    self._wtItemQuality:Collapsed()
    local qualityColor = ItemConfigTool.GetItemQualityLinearColor(0)
    local name = ""
    local iconPath = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_SetUp_Icon_0006.CommonHall_SetUp_Icon_0006"
    local camouflageDataRow = Facade.TableManager:GetRowByKey("WeaponSkin/SkinPattern", info.patternId)
    if camouflageDataRow then
        if camouflageDataRow.SkinIconPath.AssetPathName ~= "None" then
            iconPath = camouflageDataRow.SkinIconPath
        end
        qualityColor = ItemConfigTool.GetItemQualityLinearColor(camouflageDataRow.SkinQuality, 0.25)
        name = camouflageDataRow.SkinName
    end
    local actualImgComponent = self._wtItemIcon:GetActualIconImg()
    if actualImgComponent then
        actualImgComponent:AsyncSetImagePath(iconPath)
    end
    self:EnableComponent(EComp.ItemIcon, true)
    local nameComp = self:FindOrAdd(EComp.TopRightItemNameText, UIName2ID.IVTextQualityComponent, EIVSlotPos.TopLeft)
    nameComp:SetBaseWeaponName(true)
    nameComp:SetMainTxt(name)
    nameComp:ClearLinkageType()
    nameComp:RefreshComponent()
    nameComp:SetQualityColor(qualityColor)
    self:EnableComponent(EComp.TopRightItemNameText, true)
    local iconTextComp = self:FindOrAdd(EComp.BottomRightIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomRight, EIVCompOrder.Order1)
    iconTextComp:ShowIconAndText(
        bIsMasterChallenge 
        and "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Task_Icon_0102.Common_Task_Icon_0102'"
        or "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Market_Icon_0002.CommonHall_Market_Icon_0002'",
        tostring(currentNum).."/"..tostring(maxNum)
    )
    iconTextComp:SetCornerStyle() 
    iconTextComp:RefreshComponent()
    if bActivated then
        local activatedComp = self:FindOrAdd(EComp.TopRightIconText, UIName2ID.IVActivatedComponent, EIVSlotPos.TopRight)
    end
    self:EnableComponent(EComp.TopRightIconText, bActivated)
    self:EnableComponent(EComp.BottomRightIconText, true)
    self:EnableComponent(EComp.ItemIcon, true)
    self:EnableComponent(EComp.TopLeftIconText, false)
    self:EnableComponent(EComp.BottomLeftIconText, false)
    self:EnableComponent(EComp.GreyMask, false)
    self:EnableComponent(EComp.RandomSkin, false)
    self:SetAnchorIfOverFlow()
end

function IVShopItemTemplate:InitCollectionRandomSkinPoolItem(item, baseWeaponId)
    self:ChangeDefaultMode(CommonWidgetConfig.EIVItemViewMode.ListItemView)
    self:EnableComponent(EComp.TopLeftIconText, false)
    local itemNameComp = self:FindOrAdd(EComp.TopRightItemNameText, UIName2ID.IVTextQualityComponent, EIVSlotPos.TopLeft)
    local wearAndRatityComp = self:FindOrAdd(EComp.BottomLeftIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomLeft)
    wearAndRatityComp:SetStyle(CommonWidgetConfig.EIVIconTextStyle.SkinRarity)
    local weaponItem = nil
    if baseWeaponId ~= nil then
        weaponItem = ItemBase:New(baseWeaponId)
        local weaponDesc, partIndexs = WeaponHelperTool.GetWeaponDescriptionFromSkinID(item.id)
        if isvalid(weaponDesc) then
            if item.gid ~= 0 then
                WeaponAssemblyTool.SetWeaponDescriptionSkinInfoFromPropInfo(weaponDesc, item:GetRawPropInfo())
            end
            weaponItem:SetRawDescObj(weaponDesc)
        end
    end
    self:InitItem(weaponItem or item)
    self:SetAnchorIfOverFlow()
end

function IVShopItemTemplate:InitCollectionMysticalSkinItem(item, baseWeaponId, bApplied, bLocked, lockText, wearText)
    self:ChangeDefaultMode(CommonWidgetConfig.EIVItemViewMode.ListItemView)
    self:EnableComponent(EComp.TopLeftIconText, false)
    local mysticalSkinName = self:FindOrAdd(EComp.TopRightItemNameText, UIName2ID.IVTextQualityComponent, EIVSlotPos.TopLeft)
    local wearAndRatityComp = self:FindOrAdd(EComp.BottomLeftIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomLeft)
    wearAndRatityComp:SetStyle(CommonWidgetConfig.EIVIconTextStyle.SkinRarity)
	local selectedComp = self:FindOrAdd(EComp.TopRightIconText, UIName2ID.IVUsingComponent, EIVSlotPos.TopRight, EIVCompOrder.Order2)
	self:EnableComponent(EComp.TopRightIconText, bApplied)
    local maskLockComp = self:FindOrAdd(EComp.GreyMask, UIName2ID.IVMaskLockComponent, EIVSlotPos.MaskLayer)
    if lockText ~= nil then
        maskLockComp:SetDescText(lockText)
    else
        maskLockComp._wtDesc:Collapsed()
    end
    maskLockComp:IsShowLockIcon(not bApplied)
    self:EnableComponent(EComp.GreyMask, bLocked)
    local weaponItem = nil
    if baseWeaponId ~= nil then
        weaponItem = ItemBase:New(baseWeaponId)
        local weaponDesc, partIndexs = WeaponHelperTool.GetWeaponDescriptionFromSkinID(item.id)
        if isvalid(weaponDesc) then
            if item.gid ~= 0 then
                WeaponAssemblyTool.SetWeaponDescriptionSkinInfoFromPropInfo(weaponDesc, item:GetRawPropInfo())
            end
            weaponItem:SetRawDescObj(weaponDesc)
        end
    end
    self:InitItem(weaponItem or item)
    if wearText ~= nil then
        wearAndRatityComp:ShowSecondText(wearText)
    end
    self:SetAnchorIfOverFlow()
end


function IVShopItemTemplate:InitCollectionMysticalPendantItem(item, bApplied, bLocked, lockText)
    self:ChangeDefaultMode(CommonWidgetConfig.EIVItemViewMode.ListItemView)
    self:EnableComponent(EComp.TopLeftIconText, false)
    local mysticalPendantName = self:FindOrAdd(EComp.TopRightItemNameText, UIName2ID.IVTextQualityComponent, EIVSlotPos.TopLeft)
    local wearAndRatityComp = self:FindOrAdd(EComp.BottomLeftIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomLeft)
    wearAndRatityComp:SetStyle(CommonWidgetConfig.EIVIconTextStyle.SkinRarity)
    self:EnableComponent(EComp.BottomLeftIconText, true)
    local selectedComp = self:FindOrAdd(EComp.TopRightIconText, UIName2ID.IVUsingComponent, EIVSlotPos.TopRight, EIVCompOrder.Order2)
	self:EnableComponent(EComp.TopRightIconText, bApplied)
    local pendantShading = self:FindOrAdd(EComp.CommercializeShading,UIName2ID.IVCommercializeShadingComponent, EIVSlotPos.MaskLayer, EIVCompOrder.DefaultOrder)
    self:EnableComponent(EComp.CommercializeShading, ItemHelperTool.GetQualityTypeById(item.id) == ItemDetailConfig.MysticalPendantPlusId)
    local maskLockComp = self:FindOrAdd(EComp.GreyMask, UIName2ID.IVMaskLockComponent, EIVSlotPos.MaskLayer)
    if lockText ~= nil then
        maskLockComp:SetDescText(lockText)
    else
        maskLockComp._wtDesc:Collapsed()
    end
    maskLockComp:IsShowLockIcon(not bApplied)
    self:EnableComponent(EComp.GreyMask, bLocked)
    self:InitItem(item)
    self:SetAnchorIfOverFlow()
end


function IVShopItemTemplate:InitStoreWeaponItem(item)
    self:ChangeDefaultMode(CommonWidgetConfig.EIVItemViewMode.ListItemView)
    self:InitItem(item)
    local iconComponent = self:GetComponent(EComp.ItemIcon)
    if iconComponent then
        iconComponent:SetConfigOffsets()
    end
    local nameComp = self:FindOrAdd(EComp.TopLeftIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.TopLeft, EIVCompOrder.Order1)
    nameComp:ShowTextOnly(item.name)
    nameComp:SetCornerStyle()
    self:EnableComponent(EComp.TopLeftIconText, true)

    local maskLockComp = self:FindOrAdd(EComp.GreyMask, UIName2ID.IVMaskLockComponent, EIVSlotPos.MaskLayer)
    self:EnableComponent(EComp.GreyMask, false)
end

function IVShopItemTemplate:InitWeaponSkinItem(item)
    self:ChangeDefaultMode(CommonWidgetConfig.EIVItemViewMode.ListItemView)
    self:InitItem(item)
    local iconComponent = self:GetComponent(EComp.ItemIcon)
    if iconComponent then
        iconComponent:SetConfigOffsets()
    end
    local oldNameComp = self:FindOrAdd(EComp.TopLeftIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.TopLeft, EIVCompOrder.Order1)
    local nameComp = self:FindOrAdd(EComp.TopLeftNewTag, UIName2ID.IVTextQualityComponent, EIVSlotPos.TopLeft, EIVCompOrder.Order1)
    -- local name = ItemConfigTool.GetItemConfigById(item.id).Name
    nameComp:RefreshComponent()
    self:EnableComponent(EComp.TopLeftIconText, false)

    local maskLockComp = self:FindOrAdd(EComp.GreyMask, UIName2ID.IVMaskLockComponent, EIVSlotPos.MaskLayer)
    self:EnableComponent(EComp.GreyMask, false)
end

function IVShopItemTemplate:InitStoreWeaponItemWithPrice(item, iconPath, price)
    self:ChangeDefaultMode(CommonWidgetConfig.EIVItemViewMode.ListItemView)
    self:InitItem(item)
    local iconComponent = self:GetComponent(EComp.ItemIcon)
    if iconComponent then
        iconComponent:SetConfigOffsets()
    end
    local nameComp = self:FindOrAdd(EComp.TopLeftIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.TopLeft, EIVCompOrder.Order1)
    nameComp:ShowTextOnly(item.name)
    nameComp:SetCornerStyle()
    self:EnableComponent(EComp.TopLeftIconText, true)

    local maskLockComp = self:FindOrAdd(EComp.GreyMask, UIName2ID.IVMaskLockComponent, EIVSlotPos.MaskLayer)
    self:EnableComponent(EComp.GreyMask, false)

    if price ~= nil and price > 1 then
        local priceComp = self:FindOrAdd(EComp.BottomRightIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomRight, EIVCompOrder.Order1)
        if iconPath ~= nil and iconPath ~= "" then
            priceComp:ShowIconAndText(iconPath, MathUtil.GetNumberFormatStr(price))
        else
            priceComp:ShowTextOnly(price)
        end

        priceComp:SetCornerStyle()
        self:EnableComponent(EComp.BottomRightIconText, true)
    end
end

function IVShopItemTemplate:InitAuctionItem(item, info, bShowNum, bShowTopRightNum)
    bShowTopRightNum = setdefault(bShowTopRightNum, false)
    self._wtTopLeftIconText:EnableComponent(false)
    self:InitItem(item)
    -- 设置Icon
    local iconComponent = self:GetComponent(EComp.ItemIcon)
    if iconComponent then
        iconComponent:SetConfigOffsets()
    end

    local iconTextCountComp = self:FindOrAdd(EComp.BottomLeftIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomLeft, EIVCompOrder.Order1)
    if not bShowNum then -- 售罄
        self:EnableComponent(EComp.BottomLeftIconText, false)
    else
        --- 剩余个数
        iconTextCountComp:ShowTextOnly(string.format(Module.Auction.Config.Loc.GoodInSaleNum, info.cur_num))
        -- iconTextCountComp:SetVisibility(ESlateVisibility.HitTestInvisible)
        -- iconTextCountComp:SetTextStyle(ETextStyle.AuctionText)
        iconTextCountComp:SetCornerStyle()
        self:EnableComponent(EComp.BottomLeftIconText, true)
    end

    -- local iconTextNameComp = self:FindOrAdd(EComp.TopLeftIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.TopLeft, EIVCompOrder.Order1)
    -- iconTextNameComp:ShowTextOnly(item.name)
    -- -- iconTextNameComp:SetTextStyle(ETextStyle.AuctionText)
    -- iconTextNameComp:SetCornerStyle()
    -- self:EnableComponent(EComp.TopLeftIconText, true)
    local itemNameComp = self:FindOrAdd(EComp.TopRightItemNameText, UIName2ID.IVTextQualityComponent, EIVSlotPos.TopLeft)
    itemNameComp:RefreshComponent()
    self:SetAnchorIfOverFlow()

    if bShowTopRightNum then
        local topRightNum = self:FindOrAdd(EComp.TopRightIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.TopRight, EIVCompOrder.Order1)
        topRightNum:ShowTextOnly(string.format(Module.CommonWidget.Config.Loc.MultiplyStr,info.cur_num))
        -- topRightNum:SetTextStyle(ETextStyle.AuctionText)
        topRightNum:SetCornerStyle()
        self:EnableComponent(EComp.TopRightIconText, true)
    end

    local iconTextPriceComp = self:FindOrAdd(EComp.BottomRightIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomRight, EIVCompOrder.Order1)
    local currencyClientType = MapCurrencyId2ClientType[info.guide_currency] or MapCurrencyId2ClientType[ECurrencyItemId.UnBindBankNote]
    local minPriceStr = "--"
    if info.min_price > 0 then
        minPriceStr = MathUtil.GetNumberFormatStr(info.min_price)
    end
    local iconPath = ECurrencyClientType2ImgPath[currencyClientType]
    iconTextPriceComp:ShowIconAndText(iconPath, MathUtil.GetNumberFormatStr(minPriceStr))
    -- iconTextPriceComp:SetTextStyle(ETextStyle.AuctionText)
    iconTextPriceComp:SetCornerStyle()
    self:EnableComponent(EComp.BottomRightIconText, true)

    self:ClearMaskChildren()

    -- 能否被交易
    local bTransaction = Server.AuctionServer:CheckIsInSaleList(info.prop_id)
    if not bTransaction then
        local disableMask = self:FindOrAdd(EComp.GreyMask, UIName2ID.IVItemSoldOut, EIVSlotPos.MaskLayer)
        disableMask:SetCppValue("Type", 1)
        disableMask:Set_Type()
        self:EnableComponent(EComp.GreyMask, true)
    else
        self:EnableComponent(EComp.GreyMask, false)
    end
end

function IVShopItemTemplate:InitMarketItem(item, info)
    local itemType = Server.MarketServer:GetMarketItemType(item.id)
    if itemType == EMarketSubPageType.MysticalSkin then
        local propInfo = item:GetRawPropInfo()
        if propInfo then
            -- BEGIN MODIFICATION - VIRTUOS
            if IsConsole() and Server.ChatServer.hasUseUserGeneratedContentPrivilege == false then
                -- 关闭UGC权限后，不显示自定义名字
                propInfo.mystical_skin_data.custom_name = nil
            end
            -- END MODIFICATION - VIRTUOS
            local weaponDesc = item:GetRawDescObj()
            if isvalid(weaponDesc) then
                WeaponAssemblyTool.SetWeaponDescriptionSkinInfoFromPropInfo(weaponDesc, propInfo)
            end
        end
    end
    self:InitItem(item)
    -- 设置Icon
    local iconComponent = self:GetComponent(EComp.ItemIcon)
    if iconComponent then
        iconComponent:SetConfigOffsets()
    end
    -- 典藏挂饰底纹
    local pendantShading = self:FindOrAdd(EComp.CommercializeShading,UIName2ID.IVCommercializeShadingComponent, EIVSlotPos.MaskLayer, EIVCompOrder.DefaultOrder)
    if pendantShading then
        if ItemHelperTool.IsMysticalPendant(item.id) then
            local level = ItemHelperTool.GetQualityTypeById(item.id)
            if level == ItemDetailConfig.MysticalPendantPlusId then 
                self:EnableComponent(EComp.CommercializeShading,true)
                pendantShading:RefreshComponent()
            else
                self:EnableComponent(EComp.CommercializeShading,false)
            end
        else
            self:EnableComponent(EComp.CommercializeShading,false)
        end
    end
    -- 左下角文本
    if itemType == EMarketSubPageType.MysticalSkin then
        local wearAndRatityComp = self:FindOrAdd(EComp.BottomLeftIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomLeft)
        wearAndRatityComp:SetStyle(CommonWidgetConfig.EIVIconTextStyle.SkinRarity)
        wearAndRatityComp:RefreshComponent()
    elseif itemType == EMarketSubPageType.MysticalPendant then
        local wearAndRatityComp = self:FindOrAdd(EComp.BottomLeftIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomLeft)
        wearAndRatityComp:SetStyle(CommonWidgetConfig.EIVIconTextStyle.SkinRarity)
        wearAndRatityComp:RefreshComponent()
    elseif itemType == EMarketSubPageType.MandelBrick or itemType == EMarketSubPageType.MeleeWeapon then
        local iconTextCountComp = self:FindOrAdd(EComp.BottomLeftIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomLeft, EIVCompOrder.Order1)
        -- 剩余个数
        iconTextCountComp:ShowTextOnly(tostring(info.cur_num))
        iconTextCountComp:SetCornerStyle()
    end
    self:EnableComponent(EComp.BottomLeftIconText, true)
    -- 左上角文本
    self:EnableComponent(EComp.TopLeftIconText, false)
    local iconTextNameComp = self:FindOrAdd(EComp.TopLeftNewTag, UIName2ID.IVTextQualityComponent, EIVSlotPos.TopLeft, EIVCompOrder.Order1)
    iconTextNameComp:RefreshComponent()
    self:SetAnchorIfOverFlow()
    self:EnableComponent(EComp.TopLeftNewTag, true)
    -- 右上角图标
    local topRightComp = self:FindOrAdd(EComp.TopRightIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.TopRight, EIVCompOrder.Order1)
    local iconPath = "PaperSprite'/Game/UI/UIAtlas/System/Market/BakedSprite/Market_De_07.Market_De_07"
    topRightComp:ShowIconOnly(iconPath)
    topRightComp:SetCornerStyle()
    if info.bShowTopRightIcon then
        self:EnableComponent(EComp.TopRightIconText, true)
    else
        self:EnableComponent(EComp.TopRightIconText, false)
    end
    -- 右下角文本
    local iconTextPriceComp = self:FindOrAdd(EComp.BottomRightIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomRight, EIVCompOrder.Order1)
    local minPriceStr = "--"
    if info.min_price > 0 then
        minPriceStr = MathUtil.GetNumberFormatStr(info.min_price)
    end
    local currencyId = info.bShowBuyCurrency and Module.Currency:ConvertCurrencyIdByItemId(info.buy_currency_id) or Module.Currency:ConvertCurrencyIdByItemId(info.sell_currency_id)
    local currencyImgPath = Module.Currency:GetImgPath(currencyId)
    iconTextPriceComp:ShowIconAndText(currencyImgPath, MathUtil.GetNumberFormatStr(minPriceStr))
    -- iconTextPriceComp:SetTextStyle(ETextStyle.AuctionText)
    iconTextPriceComp:SetCornerStyle()
    self:EnableComponent(EComp.BottomRightIconText, true)
    -- 能否被交易
    self:ClearMaskChildren()
    if info.bSaleLocked and info.bSaleLockedText then
        local maskLockComp = self:FindOrAdd(EComp.GreyMask, UIName2ID.IVMaskLockComponent, EIVSlotPos.MaskLayer)
        if maskLockComp then
            maskLockComp:SetDescText(info.bSaleLockedText)
        end
        self:EnableComponent(EComp.GreyMask, true)
    else
        self:EnableComponent(EComp.GreyMask, false)
    end
    -- 需要给曼德尔砖加边框
    if itemType == EMarketSubPageType.MandelBrick or itemType == EMarketSubPageType.MeleeWeapon then
        local bgComponent = self:FindOrAdd(EComp.GiftBg, UIName2ID.IVBgComponent, EIVSlotPos.BgLayer, EIVCompOrder.MaskLayerOrder)
        -- 剩余个数
        bgComponent:SetBGIcon("PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/CommonWeight_De_02.CommonWeight_De_02'")
        bgComponent:EnableComponent(true)
    else
        self:EnableComponent(EComp.GiftBg, false)
    end
end

function IVShopItemTemplate:InitActivityMandelItem(item)
    self:ChangeDefaultMode(CommonWidgetConfig.EIVItemViewMode.ListItemView)
    self:InitItem(item)
    local topQuality = 0
    local topPropId = nil
    local itemConfig = ItemConfigTool.GetItemConfigById(item.id)
    if itemConfig then
        local lotteryBoxGroupConfigRows = Server.StoreServer:GetLotteryBoxGroupConfigByID(itemConfig.ConnectedPool)
        for index, lotteryBoxGroupConfigRow in ipairs(lotteryBoxGroupConfigRows) do
            if lotteryBoxGroupConfigRow.CoreFlag == 1 then
                local lotteryBoxPropConfigRows = {}
                Server.StoreServer:GetLotteryBoxPropConfigByID(lotteryBoxGroupConfigRow.GroupID, lotteryBoxPropConfigRows)
                for index, lotteryBoxPropConfigRow in ipairs(lotteryBoxPropConfigRows) do
                    local itemMainType = ItemHelperTool.GetMainTypeById(lotteryBoxPropConfigRow.PropID)
                    if itemMainType == EItemType.WeaponSkin then
                        itemConfig = ItemConfigTool.GetItemConfigById(lotteryBoxPropConfigRow.PropID)
                        if itemConfig and itemConfig.Quality > topQuality then
                            topQuality = itemConfig.Quality
                            topPropId = lotteryBoxPropConfigRow.PropID
                        end
                    end
                end
            end
        end
        if topPropId ~= nil then
            local topPrizeIconComp = self:FindOrAdd(EComp.SecondaryIcon, UIName2ID.IVMainIconComponent, EIVSlotPos.IconLayer)
            local topPrizeItem = ItemBase:New(topPropId)
            topPrizeIconComp:BindItem(topPrizeItem)
            topPrizeIconComp:RefreshComponent()
        end
    end
    self:EnableComponent(EComp.SecondaryIcon, topPropId ~= nil)
    local NumIconTextComp = self:FindOrAdd(EComp.BottomRightIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomRight, EIVCompOrder.Order1)
    NumIconTextComp:ShowTextOnly(item.num or 0)
    NumIconTextComp:SetCornerStyle()
    -- 更改曼德尔砖位置
    local itemIconComp = self:GetComponent(EComp.ItemIcon)
    if IsHD() then
        itemIconComp:SetOffsets(FMargin(463, 23, 131, 23))
    else
        itemIconComp:SetOffsets(FMargin(334, 29, 84, 29))
    end
    self:EnableComponent(EComp.BottomRightIconText, true)
    local maskLockComp = self:FindOrAdd(EComp.GreyMask, UIName2ID.IVMaskLockComponent, EIVSlotPos.MaskLayer)
    self:EnableComponent(EComp.GreyMask, false)
    -- 设置名字
    local nameComp = self:FindOrAdd(EComp.TopLeftTwoLineText, UIName2ID.IVTextQualityComponent, EIVSlotPos.TopLeft)
    nameComp:RefreshComponent()
    self:SetAnchorIfOverFlow()
    self:EnableComponent(EComp.TopLeftIconText, false)
    self:EnableComponent(EComp.TopLeftTwoLineText, true)
end

function IVShopItemTemplate:CreateShopDisableMask(shopItem)
    self:RemoveShopDisableMask()
    local disableMask, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.ShopItemDisableMaskView, self._wtMaskCanvas, nil, shopItem)
    self.instanceId = instanceId
    disableMask = getfromweak(disableMask)
    local canvasSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(disableMask)
    canvasSlot:SetOffsets(commonOffset)
    canvasSlot:SetAnchors(commonAnchor)
end

function IVShopItemTemplate:BindTimerUpdateCallback(fOnTimerUpdate)
    self._fOnTimerUpdate = fOnTimerUpdate
    self:ReleaseTimer()
    if self._fOnTimerUpdate then
        self._timerHandle = Timer:NewIns(1, 0)
        self._timerHandle:AddListener(self._fOnTimerUpdate, self)
        self._timerHandle:Start()
    end
end

function IVShopItemTemplate:ReleaseTimer()
    if self._timerHandle then
        self._timerHandle:Release()
        self._timerHandle = nil
    end
end

function IVShopItemTemplate:GetMaskParent()
    return self._wtMaskCanvas
end

function IVShopItemTemplate:SetDisableMaskOffset(disableMask)
    local canvasSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(disableMask)
    if canvasSlot and canvasSlot.SetOffsets and canvasSlot.SetAnchors then
        canvasSlot:SetOffsets(commonOffset)
        canvasSlot:SetAnchors(commonAnchor)
    end
end

function IVShopItemTemplate:GetShopDisableMask()
    if self.instanceId then
        local disableMask = Facade.UIManager:GetSubUI(self, UIName2ID.ShopItemDisableMaskView, self.instanceId)
        return getfromweak(disableMask)
    end
    return nil
end

-----商人
function IVShopItemTemplate:SetDMInstanceId(instanceId)
    self.disableMaskInstanceId = instanceId
end


function IVShopItemTemplate:GetDMInstanceId()
    return self.disableMaskInstanceId
end

function IVShopItemTemplate:OnShowInstruction()
    self._bShowInstruction = true
    self:_ShowGivenInstruction()
end

function IVShopItemTemplate:OnHideInstruction(reason)
    self._bShowInstruction = false
    self:_HideGivenInstruction(reason)
end

function IVShopItemTemplate:ShowGivenInstruction()
    self:_HideGivenInstruction()
    self:_ShowGivenInstruction()
end

function IVShopItemTemplate:HideGivenInstruction()
    self:_HideGivenInstruction()
end

function IVShopItemTemplate:_ShowGivenInstruction()
    if self._bShowInstruction then
        if self._givenInstructionContents and self._bCanShowInstruction ~= false then
            self._tipHandle = Module.CommonTips:ShowAssembledTips(self._givenInstructionContents, self._wtTipAnchor)
        end
    end
end

function IVShopItemTemplate:_HideGivenInstruction(reason)
    if self._tipHandle then
        Module.CommonTips:RemoveAssembledTips(self._tipHandle, self._wtTipAnchor)
        self._tipHandle = nil
    end
end

function IVShopItemTemplate:RegMiniTempleteInstanceId(instanceId)
    self._miniTempleteInstanceList = setdefault(self._miniTempleteInstanceList, {})
    table.insert(self._miniTempleteInstanceList, instanceId)
end

function IVShopItemTemplate:GetMiniTempleteInstanceId()
    return self._miniTempleteInstanceList or {}
end

function IVShopItemTemplate:ClearMiniTempleteInstanceId()
    self._miniTempleteInstanceList = {}
end

function IVShopItemTemplate:PlayIVAnimation(animName, loopNumber, playMode, speedScale, bRestoreState)
    self:PlayWidgetAnim(self[animName], loopNumber, playMode, speedScale, bRestoreState)

    local animComp = self:_GetAnimCompWidget()
    if animComp then
        animComp:PlayWidgetAnim(animComp[animName], loopNumber, playMode, speedScale, bRestoreState)
    end
end

function IVShopItemTemplate:StopIVAnimation(animName)
    self:StopWidgetAnim(self[animName])

    local animComp = self:_GetAnimCompWidget()
    if animComp then
        animComp:StopWidgetAnim(animComp[animName])
    end
end

-- 添加动效组件
function IVShopItemTemplate:_GetAnimCompWidget()
    local animCompWidgetWeak = Facade.UIManager:GetSubUI(self, UIName2ID.IVCommonItemAnimComp, self._animCompWidgetId)
    local animCompWidget = getfromweak(animCompWidgetWeak)
    if not animCompWidget then
        local animCompWeakIns, animCompWidgetId = Facade.UIManager:AddSubUI(self, UIName2ID.IVCommonItemAnimComp, self._wtLightEffectSlot)
        self._animCompWidgetId = animCompWidgetId
        -- local animCompWidget = UWidgetBlueprintLibrary.Create(self, self.AnimCompWidget, nil)
        animCompWidget = getfromweak(animCompWeakIns)
        -- if animCompWidget then
        --     UIUtil.AddWidgetToParent_Full(animCompWidget, self.itemviewPanel)
        -- end
    end

    return animCompWidget
end

function IVShopItemTemplate:InitCollectionHangingItem(item, ownedItem, instanceNum, bIsCybercafe)
    self:ChangeDefaultMode(CommonWidgetConfig.EIVItemViewMode.ListItemView)
    -- local weaponItem = nil
    -- if baseWeaponId ~= nil then
    --     weaponItem = ItemBase:New(baseWeaponId)
    --     local weaponDesc, partIndexs = WeaponHelperTool.GetWeaponDescriptionFromSkinID(item.id)
    --     if isvalid(weaponDesc) then
    --         if item.gid ~= 0 then
    --             WeaponAssemblyTool.SetWeaponDescriptionSkinInfoFromPropInfo(weaponDesc, item:GetRawPropInfo())
    --         end
    --         weaponItem:SetRawDescObj(weaponDesc)
    --     end
    -- end
    local bOwned = ownedItem ~= nil
    self:InitItem(item)
    self._wtTopLeftIconText:EnableComponent(false)
    local itemNameComp = self:FindOrAdd(EComp.TopRightItemNameText, UIName2ID.IVTextQualityComponent, EIVSlotPos.TopLeft)
    itemNameComp:RefreshComponent()
    local pendantShading = self:FindOrAdd(EComp.CommercializeShading,UIName2ID.IVCommercializeShadingComponent, EIVSlotPos.MaskLayer, EIVCompOrder.DefaultOrder)
    if ItemHelperTool.IsMysticalPendant(item.id) then
        local iconPath = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Market_Icon_0002.CommonHall_Market_Icon_0002'"
        self._wtBottomLeftIconText:ShowIconAndText(iconPath, bOwned == true and StringUtil.Key2StrFormat(Module.CommonWidget.Config.Loc.MysticalInstanceNum, {["InstanceNum"] = tostring(instanceNum or 0)}) or Module.CommonWidget.Config.Loc.Mystical)
        self._wtBottomLeftIconText:SetCornerStyle()
        self:EnableComponent(EComp.BottomLeftIconText, true)
        local level = ItemHelperTool.GetQualityTypeById(item.id)
		if level == ItemDetailConfig.MysticalPendantPlusId then 
            self:EnableComponent(EComp.CommercializeShading,true)
            pendantShading:RefreshComponent()
        else
            self:EnableComponent(EComp.CommercializeShading,false)
        end
    else
        self:EnableComponent(EComp.CommercializeShading,false)
    end
    local selectedComp = self:FindOrAdd(EComp.TopRightIconText, UIName2ID.IVUsingComponent, EIVSlotPos.TopRight, EIVCompOrder.Order2)
	self:EnableComponent(EComp.TopRightIconText, bApplied)
    self._wtBottomRightIconText:ShowIconOnly("PaperSprite'/Game/UI/UIAtlas/System/Hall/BakedSprite/Hall_Icon_33.Hall_Icon_33'")
    self._wtBottomRightIconText:SetCornerStyle()
	self:EnableComponent(EComp.BottomRightIconText, bIsCybercafe)
    local maskLockComp = self:FindOrAdd(EComp.GreyMask, UIName2ID.IVMaskLockComponent, EIVSlotPos.MaskLayer, EIVCompOrder.MaskLayerOrder)
    maskLockComp._wtDesc:Collapsed()
    self:EnableComponent(EComp.GreyMask, not bOwned and not bIsCybercafe)
    self:SetAnchorIfOverFlow()
end

function IVShopItemTemplate:InitCollectionSetDefaultWeaponItem(item, baseWeaponId, bApplied, bLocked, lockText, wearText)
    self:ChangeDefaultMode(CommonWidgetConfig.EIVItemViewMode.ListItemView)
    local weaponItem = nil
    if baseWeaponId ~= nil then
        weaponItem = ItemBase:New(baseWeaponId)
        local weaponDesc, partIndexs = WeaponHelperTool.GetWeaponDescriptionFromSkinID(item.id)
        if isvalid(weaponDesc) then
            if item.gid ~= 0 then
                WeaponAssemblyTool.SetWeaponDescriptionSkinInfoFromPropInfo(weaponDesc, item:GetRawPropInfo())
            end
            weaponItem:SetRawDescObj(weaponDesc)
        end
    end
    self:InitItem(weaponItem or item)
    self:EnableComponent(EComp.TopLeftIconText, false)
    local mysticalSkinName = self:FindOrAdd(EComp.TopRightItemNameText, UIName2ID.IVTextQualityComponent, EIVSlotPos.TopLeft)
    mysticalSkinName:SetBaseWeaponName(true)
    mysticalSkinName:RefreshComponent()
    local wearAndRatityComp = self:FindOrAdd(EComp.BottomLeftIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomLeft)
    self:EnableComponent(EComp.BottomLeftIconText, false)
	local selectedComp = self:FindOrAdd(EComp.TopRightIconText, UIName2ID.IVUsingComponent, EIVSlotPos.TopRight, EIVCompOrder.Order2)
	self:EnableComponent(EComp.TopRightIconText, bApplied)
    local maskLockComp = self:FindOrAdd(EComp.GreyMask, UIName2ID.IVMaskLockComponent, EIVSlotPos.MaskLayer)
    if lockText ~= nil then
        maskLockComp:SetDescText(lockText)
    else
        maskLockComp._wtDesc:Collapsed()
    end
    self:EnableComponent(EComp.GreyMask, bLocked)
    self:SetAnchorIfOverFlow()
end

function IVShopItemTemplate:SetIconFromPath(iconPath)
    self._wtItemIcon:SetIconFromPath(iconPath)
    self._wtItemIcon:SelfHitTestInvisible()
end

function IVShopItemTemplate:SetQualityColorAndOpacity(imgColor)
    local bIsValid = isvalid(self._wtItemQuality)
    if not bIsValid then
        return
    end
    local wtBottomLine = self._wtItemQuality:Wnd("wtBottomLine", UIImage)
    bIsValid = isvalid(wtBottomLine)
    if not bIsValid then
        return
    end
    wtBottomLine:SetColorAndOpacity(imgColor)
end



return IVShopItemTemplate