----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------



local ClientBaseSetting = import "ClientBaseSetting"
local clientBaseSetting = ClientBaseSetting.Get(GetWorld())
local UDFMSystemSettingHelper = import "DFMSystemSettingHelper"
local DFMSystemSettingHelper = UDFMSystemSettingHelper.Get(GetWorld())
local UDFMMobileCustomLayoutBPLibrary = import "DFMMobileCustomLayoutBPLibrary"
local UKismetSystemLibrary = import "KismetSystemLibrary"
local UGameplayBlueprintHelper = import "GameplayBlueprintHelper"
local BaseSettingLogic = {}

BaseSettingLogic.ProcessAimAssist = function(bIsOpen)
    if clientBaseSetting then
        clientBaseSetting.bIsAimAssistOpen = bIsOpen
	    clientBaseSetting:SaveDataConfig()
    end
end

BaseSettingLogic.ProcessAimMagnify = function(bIsOpen)
    if clientBaseSetting then
        clientBaseSetting:SetScopeUseRT(bIsOpen)
	    clientBaseSetting:SaveDataConfig()
    end
end

BaseSettingLogic.ProcessAimMagnifyAssist = function(bIsOpen)
    if clientBaseSetting then
        clientBaseSetting.bSceneFOVCalcOpenCameraFOV = bIsOpen
	    clientBaseSetting:SaveDataConfig()
    end
end

BaseSettingLogic.ProcessInventory = function(bIsOpen)
	if clientBaseSetting then
        clientBaseSetting.bIsInventoryAutoOrganize = bIsOpen
	    clientBaseSetting:SaveDataConfig()
        Module.Inventory:SwitchAutoSortMode(bIsOpen)
    end
end

BaseSettingLogic.ProcessQuicklySwitchWeapon = function(bIsOpen)
    if clientBaseSetting then
        clientBaseSetting.OpenQuicklySwitchButton = bIsOpen
	    clientBaseSetting:SaveDataConfig()
    end
end

BaseSettingLogic.ProcesslutterOption = function(bIsOpen)
    if clientBaseSetting then
        clientBaseSetting.OpenFlutter = bIsOpen
	    clientBaseSetting:SaveDataConfig()
    end
end


--重置的时候，需要处理的仓库设置,拍卖行设置
BaseSettingLogic.Onreset = function()
    if clientBaseSetting then
        local bIsOpen = clientBaseSetting.bIsInventoryAutoOrganize
	    Module.Inventory:SwitchAutoSortMode(bIsOpen)
	    bIsOpen = clientBaseSetting.bIsAuctionAutoOrganize
	    Module.Auction:SwitchAutoSortMode(bIsOpen)
    end
end

BaseSettingLogic.ProcessFireOnQuickScopeOpen = function(bIsOpen)
    if clientBaseSetting then
        clientBaseSetting.bCanFireOnQuickScopeOpen = bIsOpen
	    clientBaseSetting:SaveDataConfig()
    end
end

BaseSettingLogic.ProcessShotGunFireMode = function(fireMode)
    if clientBaseSetting then
        clientBaseSetting.ShotGunFireMode = fireMode
	    clientBaseSetting:SaveDataConfig()
    end
end


BaseSettingLogic.ProcessSRFireMode = function(fireMode)
    if clientBaseSetting then
        clientBaseSetting.SRFireMode = fireMode
	    clientBaseSetting:SaveDataConfig()
    end
end

BaseSettingLogic.ProcessompoundBowFireMode = function(fireMode)
    if clientBaseSetting then
        clientBaseSetting.CompoundBowFireMode = fireMode
	    clientBaseSetting:SaveDataConfig()
    end
end

BaseSettingLogic.ProcessAbilityItemFireMode = function(fireMode)
    if clientBaseSetting then
        clientBaseSetting.AbilityItemFireMode = fireMode
	    clientBaseSetting:SaveDataConfig()
    end
end

BaseSettingLogic.ProcessScopeOpenMode = function(scopeOpenMode)
    if clientBaseSetting then
        clientBaseSetting.ScopeOpenMode = scopeOpenMode
	    clientBaseSetting:SaveDataConfig()
    end
end

BaseSettingLogic.ProcessReloadBreakMirror = function(bIsOpen)
    if clientBaseSetting then
        clientBaseSetting.bReloadBreakMirror = bIsOpen
	    clientBaseSetting:SaveDataConfig()
    end
end

BaseSettingLogic.ProcessHoldRunBreakMirror_Sol = function(bIsOpen)
    if clientBaseSetting then
        clientBaseSetting.bCanSprintBreakZoom = bIsOpen
	    clientBaseSetting:SaveDataConfig()
    end
end

BaseSettingLogic.ProcessHoldRunBreakMirror_MP = function(bIsOpen)
    if clientBaseSetting then
        clientBaseSetting.bCanSprintBreakZoomMP = bIsOpen
	    clientBaseSetting:SaveDataConfig()
    end
end

BaseSettingLogic.ProcessSprintBreakUsingItem = function(bIsOpen)
    if clientBaseSetting then
        clientBaseSetting.bCanSprintBreakUsingItem = bIsOpen
	    clientBaseSetting:SaveDataConfig()
    end
end

BaseSettingLogic.ProcessAutoFire = function(bIsOpen)
    if clientBaseSetting then
        clientBaseSetting.bEnableAutoFire = bIsOpen
	    clientBaseSetting:SaveDataConfig()
    end
end

BaseSettingLogic.ProcessAutoHoldBreath = function(bIsOpen)
    if clientBaseSetting then
        clientBaseSetting.bAutoHoldBreathOnZoom = bIsOpen
	    clientBaseSetting:SaveDataConfig()
    end
end

BaseSettingLogic.ProcessAutoDropGrenade = function(bIsOpen)
    if clientBaseSetting then
        clientBaseSetting.bAutoDropGrenade = bIsOpen
	    clientBaseSetting:SaveDataConfig()
    end
end

BaseSettingLogic.ProcessThrowMode = function(index,throwMode)
    if clientBaseSetting then
        if index == 1 then
            clientBaseSetting.SkillWeaponThrowMode_support = throwMode
        elseif index == 2 then
            clientBaseSetting.SkillWeaponThrowMode_active = throwMode
        else
            clientBaseSetting.SkillWeaponThrowMode_BattleField = throwMode
        end
	    clientBaseSetting:SaveDataConfig()
    end
end

BaseSettingLogic.ProcessSRInstantFire = function(bOpen)
    if clientBaseSetting then
        clientBaseSetting.bSRInstantFire = bOpen
	    clientBaseSetting:SaveDataConfig()
    end
end

BaseSettingLogic.ProcessQuickScopeOpen = function(quickScopeOpenMode)
    if clientBaseSetting then
        if UGameplayBlueprintHelper.IsRegionCN() then
            clientBaseSetting.QuickScopeOpen = quickScopeOpenMode
        else
            clientBaseSetting.QuickScopeOpen_Overseas = quickScopeOpenMode
        end

    --     if quickScopeOpenMode == EQuickScopeOpenMode.AllOpen then
    --         BaseSettingLogic.SetQuickScopeOpenModeForAll(true)
    --     elseif quickScopeOpenMode == EQuickScopeOpenMode.AllClose then
    --         BaseSettingLogic.SetQuickScopeOpenModeForAll(false)
    --     end
        clientBaseSetting:SaveDataConfig()
    end
end

BaseSettingLogic.SetQuickScopeOpenModeForAll = function(bIsAllOpen)
	if clientBaseSetting then
        for weaponItemType,_ in pairs(clientBaseSetting.QuickScopeOpenMap) do
            clientBaseSetting.QuickScopeOpenMap:Add(weaponItemType,bIsAllOpen)
        end
        clientBaseSetting:SaveDataConfig()
    end
end

BaseSettingLogic.ProcessQuickScopeOpenMap = function(gunType, bIsOpen)
    if clientBaseSetting then
        -- local quickScopeOpenMap = clientBaseSetting.QuickScopeOpenMap
        -- quickScopeOpenMap:Add(gunType,bIsOpen)
        -- clientBaseSetting.QuickScopeOpenMap = quickScopeOpenMap
        clientBaseSetting.QuickScopeOpenMap:Add(gunType,bIsOpen)
        loginfo('Set QuickScopeOpenMap[', gunType, '] to ', clientBaseSetting.QuickScopeOpenMap:Get(gunType))
        clientBaseSetting:SaveDataConfig()
    end
end

BaseSettingLogic.ProcessQuickScopeOpenAbilityItem = function(bIsOpen)
    if clientBaseSetting then
        if UGameplayBlueprintHelper.IsRegionCN() then
            clientBaseSetting.QuickScopeOpenAbilityItem = bIsOpen
        else
            clientBaseSetting.QuickScopeOpenAbilityItem_Overseas = bIsOpen
        end

        clientBaseSetting:SaveDataConfig()
    end
end

BaseSettingLogic.ProcessFixedWeaponQuickScope = function(bIsOpen)
    if clientBaseSetting then
        if UGameplayBlueprintHelper.IsRegionCN() then
            clientBaseSetting.QuickScopeOpenFixedWeapon = bIsOpen
        else
            clientBaseSetting.QuickScopeOpenFixedWeapon_Overseas = bIsOpen
        end
        clientBaseSetting:SaveDataConfig()
    end
end

BaseSettingLogic.ProcessFPPViewRange = function(value)
    if clientBaseSetting then
        clientBaseSetting.FPPViewRange = math.ceil(value)
	    clientBaseSetting:SaveDataConfig()
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.VT.DefaultFOVTan ".. math.tan(value / 360.0 * math.pi))
    end
end

BaseSettingLogic.InitFPPViewRange = function()
    if clientBaseSetting then
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.VT.DefaultFOVTan ".. math.tan(clientBaseSetting.FPPViewRange / 360.0 * math.pi))
    end
end

BaseSettingLogic.ProcessTPPViewRange = function(value)
    if clientBaseSetting then
        clientBaseSetting.TPPViewRange = math.ceil(value)
	    clientBaseSetting:SaveDataConfig()
    end
end

BaseSettingLogic.ProcessCanSwitchXPP = function(bOpen)
	if clientBaseSetting then
        clientBaseSetting.bCanSwitchXPP = bOpen
	    clientBaseSetting:SaveDataConfig()
        clientBaseSetting:Lua_CameraModeChangeBroadcast()
    end
end

--开火打断换弹
BaseSettingLogic.ProcessFireBreakReload = function(mode)
    if clientBaseSetting then
        clientBaseSetting.FireBreakReload = mode
	    clientBaseSetting:SaveDataConfig()
    end
end

-- 地面自动越障
BaseSettingLogic.ProcessObstacleCrossing = function(mode)
    if clientBaseSetting then
        clientBaseSetting.AutoCrossMode = mode
	    clientBaseSetting:SaveDataConfig()
    end
end


--疾跑打断换弹
BaseSettingLogic.ProcessRunBreakReload = function(isOpen)
    if clientBaseSetting then
        clientBaseSetting.bRunBreakReload = isOpen
        clientBaseSetting:SaveDataConfig()
    end
end

--混装换弹
BaseSettingLogic.ProcessMixedLoading = function(bIsOpen)
    if clientBaseSetting then
        clientBaseSetting.bMixedLoading = bIsOpen
        DFMSystemSettingHelper:MixLoadingChange()
	    clientBaseSetting:SaveDataConfig()
    end
end

---------- 射击模式设置MP

BaseSettingLogic.ProcessQuickScopeOpen_MP = function(quickScopeOpenMode)
    if clientBaseSetting then
        if UGameplayBlueprintHelper.IsRegionCN() then
            clientBaseSetting.QuickScopeopen_MP = quickScopeOpenMode
        else
            clientBaseSetting.QuickScopeopen_MP_Overseas = quickScopeOpenMode
        end
    --     if quickScopeOpenMode == EQuickScopeOpenMode.AllOpen then
    --         BaseSettingLogic.SetQuickScopeOpenModeForAll(true)
    --     elseif quickScopeOpenMode == EQuickScopeOpenMode.AllClose then
    --         BaseSettingLogic.SetQuickScopeOpenModeForAll(false)
    --     end
        clientBaseSetting:SaveDataConfig()
    end
end

BaseSettingLogic.ProcessQuickScopeOpenMap_MP = function(gunType, bIsOpen)
    if clientBaseSetting then
        clientBaseSetting.QuickScopeOpenMap_MP:Add(gunType,bIsOpen)
        loginfo('Set QuickScopeOpenMap[', gunType, '] to ', clientBaseSetting.QuickScopeOpenMap_MP:Get(gunType))
        clientBaseSetting:SaveDataConfig()
    end
end


BaseSettingLogic.ProcessScopeBreathMode = function(scopeBreathMode)
    if clientBaseSetting then
        clientBaseSetting.ScopeBreathMode_SOL = scopeBreathMode
        clientBaseSetting:SaveDataConfig()
    end
end

BaseSettingLogic.ProcessScopeBreathMode_MP = function(scopeBreathMode)
    if clientBaseSetting then
        clientBaseSetting.ScopeBreathMode_MP = scopeBreathMode
        clientBaseSetting:SaveDataConfig()
    end
end

BaseSettingLogic.ProcessAutoBreathOpenMap = function(gunType, bIsOpen)
    if clientBaseSetting then
        clientBaseSetting.AutoHoldBreathOnZoomOpenMap:Add(gunType,bIsOpen)
        loginfo('Set AutoHoldBreathOnZoomOpenMap[', gunType, '] to ', clientBaseSetting.AutoHoldBreathOnZoomOpenMap:Get(gunType))
        clientBaseSetting:SaveDataConfig()
    end
end

BaseSettingLogic.ProcessAutoBreathOpenMap_MP = function(gunType, bIsOpen)
    if clientBaseSetting then
        clientBaseSetting.AutoHoldBreathOnZoomOpenMap_MP:Add(gunType,bIsOpen)
        loginfo('Set AutoHoldBreathOnZoomOpenMap_MP[', gunType, '] to ', clientBaseSetting.AutoHoldBreathOnZoomOpenMap_MP:Get(gunType))
        clientBaseSetting:SaveDataConfig()
    end
end

BaseSettingLogic.ProcessFireOnQuickScopeOpen_MP = function(bIsOpen)
    if clientBaseSetting then
        clientBaseSetting.bCanFireOnQuickScopeOpen_MP = bIsOpen
	    clientBaseSetting:SaveDataConfig()
    end
end

BaseSettingLogic.ProcessShotGunFireMode_MP = function(fireMode)
    if clientBaseSetting then
        clientBaseSetting.ShotGunFireMode_MP = fireMode
	    clientBaseSetting:SaveDataConfig()
    end
end


BaseSettingLogic.ProcessSRFireMode_MP = function(fireMode)
    if clientBaseSetting then
        clientBaseSetting.SRFireMode_MP = fireMode
	    clientBaseSetting:SaveDataConfig()
    end
end

BaseSettingLogic.ProcessompoundBowFireMode_MP = function(fireMode)
    if clientBaseSetting then
        clientBaseSetting.CompoundBowFireMode_MP = fireMode
	    clientBaseSetting:SaveDataConfig()
    end
end



BaseSettingLogic.ProcessQuickScopeOpenAbilityItem_MP = function(bIsOpen)
    if clientBaseSetting then
        if UGameplayBlueprintHelper.IsRegionCN() then
            clientBaseSetting.QuickScopeOpenAbilityItem_MP = bIsOpen
        else
            clientBaseSetting.QuickScopeOpenAbilityItem_MP_Overseas = bIsOpen
        end
        clientBaseSetting:SaveDataConfig()
    end
end

BaseSettingLogic.ProcessFixedWeaponQuickScope_MP = function(bIsOpen)
    if clientBaseSetting then
        if UGameplayBlueprintHelper.IsRegionCN() then
            clientBaseSetting.QuickScopeOpenFixedWeapon_MP = bIsOpen
        else
            clientBaseSetting.QuickScopeOpenFixedWeapon_MP_Overseas = bIsOpen
        end

        clientBaseSetting:SaveDataConfig()
    end
end


return BaseSettingLogic
