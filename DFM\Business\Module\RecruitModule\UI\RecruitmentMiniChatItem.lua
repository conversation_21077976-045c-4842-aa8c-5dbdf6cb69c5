----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRecruit)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class RecruitmentMiniChatItem : LuaUIBaseView
local RecruitmentMiniChatItem = ui("RecruitmentMiniChatItem")
local RecruitConfig = Module.Recruit.Config
local RecruitLogic = require "DFM.Business.Module.RecruitModule.Logic.RecruitLogic"
local RankIconAbbr = require "DFM.Business.Module.RankingModule.UI.RankIconAbbr"
local RecruitPlayerHeadItem = require "DFM.Business.Module.RecruitModule.UI.RecruitPlayerHeadItem"

function RecruitmentMiniChatItem:Ctor()
    self._wtTeammateHead = self:Wnd("wtTeammateHead", RecruitPlayerHeadItem)
    self._wtPlayerNameText = self:Wnd("wtPlayerNameText", UIImage)
    self._wtRankDivisionIcon = self:Wnd("wtRankDivisionIcon", RankIconAbbr)
    self._wtRankDivisionText = self:Wnd("wtRankDivisionText", UITextBlock)
    self._wtLabelContainer = self:Wnd("wtLabelContainer", UIWidgetBase)
    self._wtMapNameAndMemberNumText = self:Wnd("wtMapNameAndMemberNumText", UITextBlock)
    self._wtMicIcon = self:Wnd("wtMicIcon", UIImage)
    self._wtJoinBtn = self:Wnd("wtJoinBtn", DFCommonButtonOnly)
    self._wtJoinBtn:Event("OnClicked", self._OnJoinBtnClicked, self)
    self._recruitmentInfo = nil
    self._captainPlayerInfo = nil
end

-- UI构造时触发, 用于初始化外部传参UI数据逻辑，由UIManager触发（仅触发一次）
-- 子节点蓝图不会触发该方法
-- Module加载好子控件所需的图片后，将数据传入主UI界面
---@overload fun(LuaUIBaseView, OnInitExtraData)
function RecruitmentMiniChatItem:OnInitExtraData(params)
end

-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function RecruitmentMiniChatItem:OnOpen()
    self:AddListeners()
end

-- UI监听事件、协议
function RecruitmentMiniChatItem:AddListeners()
    self:AddLuaEvent(RecruitConfig.Events.evtOnTeamMemberInfoChanged, self._OnTeamMemberInfoChanged, self)
end

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function RecruitmentMiniChatItem:OnClose()
    self:RemoveAllLuaEvent()
    Facade.UIManager:ClearSubUIByParent(self, self._wtLabelContainer)
end

-- UI OnOpen后、调用Show()显示时触发
-- 做表现
---@overload fun(LuaUIBaseView, OnShow)
function RecruitmentMiniChatItem:OnShow()
end

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function RecruitmentMiniChatItem:OnHide()
end

-- 动画结束回调
---@overload fun(LuaUIBaseView, OnAnimFinished)
function RecruitmentMiniChatItem:OnAnimFinished(anim)
end

function RecruitmentMiniChatItem:InitRecruitmentInfo(recruitmentInfo)
    self._recruitmentInfo = recruitmentInfo
    self._captainPlayerInfo = nil
    self._wtMapNameAndMemberNumText:Collapsed()
    self._wtMicIcon:Collapsed()
    self._wtRankDivisionIcon:SetRankIconNone()
    self._wtRankDivisionIcon:SelfHitTestInvisible()
    self._wtRankDivisionText:SetText(RecruitConfig.Loc.NoRankDivision)
    self._wtRankDivisionText:SelfHitTestInvisible()
    Facade.UIManager:RemoveSubUIByParent(self, self._wtLabelContainer)
    self._wtLabelContainer:Collapsed()
    self._wtJoinBtn:Collapsed()
    self._wtTeammateHead:InitPlayerInfo(nil)
    local maxMemberNum = RecruitLogic.GetTeamMemberMaxNum()
    if self._recruitmentInfo ~= nil then
        for index, playerInfo in ipairs(self._recruitmentInfo.playerInfoList) do
            if playerInfo.player_id == self._recruitmentInfo.captainId then
                self._captainPlayerInfo = playerInfo
                break
            end
        end
        if self._captainPlayerInfo ~= nil then
            local btnTbl =  {
                HeadButtonType.PlayerInformat,
                HeadButtonType.AddFriend,
                HeadButtonType.Report
            }
            self._wtTeammateHead:InitPlayerInfo(self._captainPlayerInfo, true, true, btnTbl)
            self._wtPlayerNameText:SetText(self._captainPlayerInfo.nick_name or RecruitConfig.Loc.UnknownPlayer)
            if self._recruitmentInfo.filter.rankDivisionChoice ~= RecruitmentRankType.RecruitmentRankUnable then
                if RecruitLogic.IsInMp() == true then
                    if self._captainPlayerInfo.mp_rank_attended == true then
                        local targetScore = (self._captainPlayerInfo.show_commander_rank_points ~= nil and self._captainPlayerInfo.show_commander_rank_points > 0) and self._captainPlayerInfo.mp_commander_score or self._captainPlayerInfo.mp_rank_score
                        self._wtRankDivisionIcon:SetTournamentIconByScore(targetScore)
                        local rankInfo = Module.Tournament:GetRankDataByScore(targetScore)
                        self._wtRankDivisionText:SetText(rankInfo and rankInfo.Name or RecruitConfig.Loc.NoRankDivision)
                    else
                        self._wtRankDivisionIcon:SetRankIconNone()
                    end
                else
                    if self._captainPlayerInfo.sol_rank_attended == true then
                        self._wtRankDivisionIcon:SetRankingIconByScore(self._captainPlayerInfo.sol_rank_score)
                        local rankInfo = Module.Ranking:GetMinorDataByScore(self._captainPlayerInfo.sol_rank_score)
                        self._wtRankDivisionText:SetText(rankInfo and rankInfo.RankName or RecruitConfig.Loc.NoRankDivision)
                    else
                        self._wtRankDivisionIcon:SetRankIconNone()
                    end
                end
            end
        else
            self._wtPlayerNameText:SetText(RecruitConfig.Loc.UnknownPlayer)
        end
        self._wtTeammateHead:SelfHitTestInvisible()
        if self._recruitmentInfo.filter.micChoice ~= RecruitmentMicrophoneType.RecruitmentMicrophoneUnable then
            self._wtMicIcon:SelfHitTestInvisible()
        end
        self:_OnTeamMemberInfoChanged()
        if self._recruitmentInfo.filter.bIsMP then
            local params = {
                ["desc"] = Module.GameMode:GetTDMModeNameByGroupId(self._recruitmentInfo.filter.group_id),
                ["curNum"] = tostring(table.nums(self._recruitmentInfo.playerInfoList)),
                ["maxNum"] = tostring(maxMemberNum)
            }
            self._wtMapNameAndMemberNumText:SetText(StringUtil.Key2StrFormat(Module.Recruit.Config.Loc.PreNumFormat, params))
        else
            local params = {
                ["desc"] = Module.GameMode:GetStandardMapNameByMatchModeId(self._recruitmentInfo.filter.match_mode_id),
                ["curNum"] = tostring(table.nums(self._recruitmentInfo.playerInfoList)),
                ["maxNum"] = tostring(maxMemberNum)
            }
            self._wtMapNameAndMemberNumText:SetText(StringUtil.Key2StrFormat(Module.Recruit.Config.Loc.PreNumFormat, params)) 
        end
        self._wtMapNameAndMemberNumText:SelfHitTestInvisible()
        local labelInfo
        local stop = false
        for groupId, group in pairs(self._recruitmentInfo.filter.selectedLabelIDs) do
            for labelId, value in pairs(group) do
                local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.RecruitmentInfoLabelItem, self._wtLabelContainer)
                local itemWidget = getfromweak(weakUIIns)
                labelInfo = {}
                labelInfo.id = labelId
                labelInfo.name = RecruitConfig.LabelId2Text[labelId]
                labelInfo.iconPath = RecruitConfig.LabelId2IconPath[labelId]
                itemWidget:InitLabelInfo(labelInfo)
                if self._wtLabelContainer:GetChildrenCount() >= 3 then
                    stop = true
                    break
                end
            end
            if stop == true then
                break
            end
        end
        self._wtLabelContainer:Visible()
    end
end

function RecruitmentMiniChatItem:_OnJoinBtnClicked()
    self:SetType(true)
    
    -- BEGIN MODIFICATION - VIRTUOS
    local handleOnJoinBtnClicked = function()
        -- END MODIFICATION - VIRTUOS    
            if self._recruitmentInfo.filter.micChoice == RecruitmentMicrophoneType.RecruitmentMicrophoneEnable then
                RecruitLogic.AddTeamIdRequireMic(self._recruitmentInfo.teamId)
            end
            Server.TeamServer:ApplyJoinFromRecruitment(
                self._recruitmentInfo.teamId
            )
            Timer.DelayCall(
                RecruitConfig.JoinBtnDelayTime,
                function()
                    self:_OnTeamMemberInfoChanged()
                end
            ) 
    end

    -- BEGIN MODIFICATION - VIRTUOS
    if IsConsole() then
        Module.Team:CanTeamWithPlayerViaCrossPlayPermission(self._captainPlayerInfo.player_id, function(res)
            if res then
                handleOnJoinBtnClicked()
            else
                -- 双方跨平台权限不一致，无法一起组队
                Module.CommonTips:ShowSimpleTip(Module.Team.Config.Loc.CannotTeamUpDueToCrossNetworkPrivilegeMismatch)
            end
        end)
    else
        handleOnJoinBtnClicked()
    end
    -- END MODIFICATION - VIRTUOS    
end

function RecruitmentMiniChatItem:_OnTeamMemberInfoChanged()
    if not hasdestroy(self) then
        if self._recruitmentInfo ~= nil then
            if self._captainPlayerInfo ~= nil and self._recruitmentInfo.teamId ~= Server.TeamServer:GetTeamID() then
                self:SetType(false)
                return
            end
        end
        self:SetType(true)
    end
end

return RecruitmentMiniChatItem
