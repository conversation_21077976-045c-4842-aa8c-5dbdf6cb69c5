----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCommonWidget)
----- LOG FUNCTION AUTO GENERATE END -----------



--------------------------------------------------------------------------
--- Button
--------------------------------------------------------------------------
-- TODO : 旧版按钮
UITable[UIName2ID.CommonWhiteButton] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonButton",
    BPKey = "CommonButton_V1_01"
}

-- TODO : 旧版按钮
UITable[UIName2ID.CommonYellowButton] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonButton",
    BPKey = "CommonButton_V1_02"
}
-- TODO : 旧版按钮
UITable[UIName2ID.CommonTransparenceButton] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonButton",
    BPKey = "CommonButton_V2_03"
}
-- TODO : 旧版按钮
UITable[UIName2ID.CommonImageButton] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonButton",
    BPKey = "CommonButton_V2_04"
}

UITable[UIName2ID.CommonButtonV1S1] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonButton",
    BPKey = "WBP_CommonButtonV1S1"
}

UITable[UIName2ID.CommonButtonV1S1_New] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonButton",
    BPKey = "WBP_CommonButtonV1S1_Button"
}

UITable[UIName2ID.CommonButtonV1S2_New] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonButton",
    BPKey = "WBP_CommonButtonV1S2_Button"
}

UITable[UIName2ID.CommonButtonV1S2] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonButton",
    BPKey = "WBP_CommonButtonV1S2"
}

UITable[UIName2ID.CommonButtonV2S1] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonButton",
    BPKey = "WBP_CommonButtonV2S1"
}

UITable[UIName2ID.CommonButtonV2S2] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonButton",
    BPKey = "WBP_CommonButtonV2S2"
}

UITable[UIName2ID.CommonButtonV3S1] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonButton",
    BPKey = "WBP_CommonButtonV3S1"
}

UITable[UIName2ID.CommonButtonV3S2] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonButton",
    BPKey = "WBP_CommonButtonV3S2"
}


UITable[UIName2ID.CommonMiniGreenButton] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonButton",
    BPKey = "WBP_CommonButtonMini"
}

UITable[UIName2ID.CommonMiniButton] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonButton",
    BPKey = "WBP_CommonButtonMiniV2"
}

--------------------------------------------------------------------------
--- Tab Group
--------------------------------------------------------------------------
UITable[UIName2ID.CommonCheckTab] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonCheckTab",
    BPKey = "WBP_CommonTabV1"
}

UITable[UIName2ID.CommonCheckTabS1] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonCheckTab",
    BPKey = "WBP_CommonTabV1_Button"
}

UITable[UIName2ID.CommonCheckTabV2] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonCheckTab",
    BPKey = "WBP_CommonTabV2_Button"
}

--[[UITable[UIName2ID.CommonCheckTabS2] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonCheckTab",
    BPKey = "WBP_CommonTabV2"
}--]]

--[[UITable[UIName2ID.CommonCheckTabS3] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonCheckTab",
    BPKey = "WBP_CommonTabV3"
}--]]

--[[UITable[UIName2ID.HeroSkinTab] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonCheckTab",
    BPKey = "WBP_HeroSkinTab"
}--]]
--------------------------------------------------------------------------
--- Tab （旧版，蓝图中摆放绑定）
--------------------------------------------------------------------------
UITable[UIName2ID.CommonMainTab] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonMainTab",
    BPKey = "WBP_CommonMainTab"
}

-- UITable[UIName2ID.CommonSubTab] = {
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonSubTab",
--     BPKey = "WBP_CommonSubTab"
-- }

-- TODO :Delete 改枪台老版样式
-- UITable[UIName2ID.WeaponAssemblySubTab] = {
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonSubTab",
--     BPKey = "F_WeaponAssembly_SubTab"
-- }

--------------------------------------------------------------------------
--- Tab Live  (新版tab，动态生成)
--------------------------------------------------------------------------
UITable[UIName2ID.CommonMainTabLiveS1] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonTabLive.CommonMainTabLive",
    BPKey = "WBP_CommonMainTabLiveS1"
}

UITable[UIName2ID.CommonSubTabLiveS1] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonTabLive.CommonSubTabLive",
    BPKey = "WBP_CommonSubTabLiveS1"
}

UITable[UIName2ID.CommonMainTabLiveS2] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonTabLive.CommonMainTabLive",
    BPKey = "WBP_CommonMainTabLiveS2",
}

UITable[UIName2ID.CommonSubTabLiveS2] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonTabLive.CommonSubTabLive",
    BPKey = "WBP_CommonSubTabLiveS2"
}

UITable[UIName2ID.CommonMainTabLiveS2V1] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonTabLive.CommonMainTabLiveEffect",
    BPKey = "WBP_CommonMainTabLiveS2_LiveTab",
    Anim = {
        FlowOutAni = "WBP_CommonMainTabLiveS2_out",
    }
}

UITable[UIName2ID.CommonSubTabLiveS2V1] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonTabLive.CommonSubTabLiveEffect",
    BPKey = "WBP_CommonSubTabLiveS2_LiveTab"
}

UITable[UIName2ID.CommonMainTabLiveS3] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonTabLive.CommonMainTabLive",
    BPKey = "WBP_CommonMainTabLiveS3"
}

UITable[UIName2ID.CommonCheckDrawTab] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonCheckButton.CommonCheckDrawTab",
    BPKey = "WBP_CommonMainTabLiveS3_LiveTab"
}


UITable[UIName2ID.CommonTabBtnList] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonTabLive.CommonTabBtnList",
    BPKey = "WBP_CommonListTabBtn"
}

-- 业务特殊样式 带线条动效的Tab
UITable[UIName2ID.WarehouseClassButton] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonBookmark",
    BPKey = "WBP_CommonBookmarkBtn",
}
--------------------------------------------------------------------------
--- 列表空状态
--------------------------------------------------------------------------
UITable[UIName2ID.CommonEmptyContent] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.YxFramework.Managers.UI.LuaUIBaseView",
    BPKey = "WBP_Common_NoAnything",
}

--------------------------------------------------------------------------
--- FullScreen相关组件
--------------------------------------------------------------------------
UITable[UIName2ID.CommonSkipOverBg] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.FullScreen.CommonSkipOverBg",
    BPKey = "WBP_Common_JumpOver",
    SubUIs = {
		UIName2ID.InputSummaryItemHD,
		UIName2ID.InputSummaryItemV2HD
    },
}
--------------------------------------------------------------------------
--- 数字相关组件
--------------------------------------------------------------------------
UITable[UIName2ID.CommonAddDecSlider] = {
    UILayer = EUILayer.Sub,
    LuaPath= "DFM.Business.Module.CommonWidgetModule.UI.CommonAddDecSlider", 
    BPKey = "WBP_AddDecSlider"
}

--[[UITable[UIName2ID.CommonAddDecNum2] = {
    UILayer = EUILayer.Sub,
    LuaPath= "DFM.Business.Module.CommonWidgetModule.UI.CommonAddDecNum2", 
    BPKey = "WBP_CommonAddDecNum"
}--]]

UITable[UIName2ID.IVGetMaskComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVComponentBase"),
    BPKey = "WBP_SlotCompGet"
}

--[[UITable[UIName2ID.CommonAddDecPrice] = {
    UILayer = EUILayer.Sub,
    LuaPath= "DFM.Business.Module.CommonWidgetModule.UI.CommonAddDecPrice", 
    BPKey = "WBP_CommonAddDecPrice"
}--]]

--------------------------------------------------------------------------
--- Social相关组件
--------------------------------------------------------------------------
UITable[UIName2ID.CommonHeadIcon] = {
    UILayer = EUILayer.Sub,
    LuaPath= "DFM.Business.Module.CommonWidgetModule.UI.CommonHeadIcon", 
    BPKey = "WBP_CommonHeadIcon_Other"
}

UITable[UIName2ID.CommonHeadIconCircle] = {
    UILayer = EUILayer.Sub,
    LuaPath= "DFM.Business.Module.CommonWidgetModule.UI.CommonIcon.CommonHeadIconCircle", 
    BPKey = "WBP_CommonHeadIconCircle"
}

UITable[UIName2ID.CommonTipsPlayer] = {
    UILayer = EUILayer.Pop,
    LuaPath= "DFM.Business.Module.CommonWidgetModule.UI.CommonTipsPlayer",
    BPKey = "WBP_Common_NameCard",
    SubUIs = {
        UIName2ID.DFCommonIconButton,
        UIName2ID.CommonTipsPlayerBtn,
        UIName2ID.CommonTipsPlayerTextBtn,
    },
    IsModal = true,
}

UITable[UIName2ID.CommonTipsPlayerSimple] = {
    UILayer = EUILayer.Pop,
    LuaPath= "DFM.Business.Module.CommonWidgetModule.UI.CommonTipsPlayer",
    BPKey = "WBP_CommonTipsPlayerState1",
    SubUIs = {
        UIName2ID.DFCommonIconButton,
        UIName2ID.CommonTipsPlayerBtn,
    },
    IsModal = true,
}

UITable[UIName2ID.CommonTipsPlayerBtn] = {
    UILayer = EUILayer.Sub,
    LuaPath= "DFM.Business.Module.CommonWidgetModule.UI.CommonTipsPlayerBtn",
    BPKey = "WBP_CommonTipsPlayerStateBtn1"
}

UITable[UIName2ID.CommonTipsPlayerTextBtn] = {
    UILayer = EUILayer.Sub,
    LuaPath= "DFM.Business.Module.CommonWidgetModule.UI.CommonTipsPlayerTextBtn",
    BPKey = "WBP_Common_NameCard_Button"
}

--------------------------------------------------------------------------
--- DragDrop相关组件
--------------------------------------------------------------------------
UITable[UIName2ID.CommonDragDropMask] = {
    UILayer = EUILayer.Sub,
    LuaPath= "DFM.Business.Module.CommonWidgetModule.UI.CommonDragDropMask", 
    BPKey = "WBP_DragDropMask"
}

--------------------------------------------------------------------------
--- ReorderableList相关组件
--------------------------------------------------------------------------
UITable[UIName2ID.ReorderableListDragArea] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.Dynamic.ReorderableListDragArea", 
    BPKey = "WBP_ReorderableListDragArea"
}

--------------------------------------------------------------------------
---搜索通用组件
--------------------------------------------------------------------------
UITable[UIName2ID.CommonInputBox] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonInputBox",
    BPKey = "WBP_inputBox"
}


-----------------------------------------------------------------------
--region New ItemView



--- 安全起见，开启对象池的版本仅影响商人
UITable[UIName2ID.IVCommonItemTemplateMini] = {
	UILayer = EUILayer.Sub,
	LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.ItemView.IVCommonItemTemplate",
    BPKey = "WBP_CommonItemTemplate",
    SubUIs = {
        UIName2ID.IVTextIconComponent,
        UIName2ID.IVHbComponent,
        UIName2ID.IVGetMaskComponent,
        UIName2ID.IVItemSoldOut,
        UIName2ID.IVAccessoryComponent,
    },
    Anim = {
        bManuelAnim = true,
    },
    --- 默认Cache配置
    ReConfig = {
        IsPoolEnable = true,
        MaxPoolLength = 30,
    }
}


UITable[UIName2ID.IVRaidItemTemplate] = {
	UILayer = EUILayer.Sub,
	LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.ItemView.IVCommonItemTemplate",
    BPKey = "WBP_RaidItemTemplate",
    SubUIs = {
        UIName2ID.IVTextIconComponent,
        UIName2ID.IVHbComponent,
    }
}

------------------------------------------------------------------------------
-- Bookmarks

if IsInEditor() then
    -- 仓库道具模板
        UITable[UIName2ID.IVWarehouseTemplate] = {
            UILayer = EUILayer.Sub,
            LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.ItemView.IVWarehouseTemplate",
            BPKey = "WBP_WarehouseItemTemplate",
            -- 当前itemview的subui配置移植到preloadconfig中。根据需求进行配置，当前位置配置只配置通用组件。
            -- 注意：手柄相关的控件HD独占，不入mobile包。相关控件在后面插入到本表SubUIs表中
            SubUIs = {
                UIName2ID.IVBgComponent,
                UIName2ID.IVTextIconComponent,
                UIName2ID.IVCheckBoxComponent,
                UIName2ID.IVQualityComponent,
                UIName2ID.IVMainIconComponent,
                UIName2ID.IVSellPriceComponent,
                UIName2ID.IVItemSelectedComponent,
                UIName2ID.IVUsingComponent,
                UIName2ID.IVTipMaskComponent,
                UIName2ID.ItemViewUseAnim,
                UIName2ID.CommonItemHighlight,
                UIName2ID.IVGreyMask,
                UIName2ID.IVReturn,
                UIName2ID.IVPutInSafeboxComponent,
                UIName2ID.IVMarkComponent,
                UIName2ID.IVMissionComponent,
                UIName2ID.IVSearchAnimComponent,
                UIName2ID.IVDispensingComponent,
                UIName2ID.IVDecBtnComponent,
                UIName2ID.IVSelectNumComponent,
                UIName2ID.IVMaskLockComponent,
                UIName2ID.IVShoppingCartComponent,
                UIName2ID.IVSoldOutAbnormalComponent, 
                UIName2ID.IVWeaponBPComponent,
                UIName2ID.IVExposureComponent,
                UIName2ID.IVComparedComponent,
                UIName2ID.IVTipUnlockedComponent,
                UIName2ID.IVItemMarkComponent,
                UIName2ID.IVItemHover,
                UIName2ID.IVContainerExpiredComponent,
                UIName2ID.IVTwoLinesTitleComponent,
                UIName2ID.IVTextQualityComponent,
                UIName2ID.IVCantCarryOutComponent,
            },
            -- 需要手动管理动画播放
            Anim = {
                bManuelAnim = true
            }
        }
    if DFHD_LUA == 1 then
        -- 插入手柄提示SubUI
        local IVWarehouseTemplateGamepadAddition =
        {
            UIName2ID.ItemDetailCommonKeyIconTipsItem,
            UIName2ID.CommonKeyIconTips,
            UIName2ID.CommonKeyIconTipsItem,
        }
        table.append(UITable[UIName2ID.IVWarehouseTemplate].SubUIs, IVWarehouseTemplateGamepadAddition)
    end
    
    UITable[UIName2ID.IVShopItemTemplate] = {
        UILayer = EUILayer.Sub,
        LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.ItemView.IVShopItemTemplate",
        BPKey = "WBP_ShopItemTemplate",
        -- 当前itemview的subui配置移植到preloadconfig中。根据需求进行配置，当前位置配置只配置通用组件。
        SubUIs = {
            UIName2ID.IVBgComponent,
            UIName2ID.IVTextIconComponent,
            UIName2ID.IVQualityComponent,
            UIName2ID.IVMainIconComponent,
            UIName2ID.IVItemSelectedComponent,
            UIName2ID.IVTipMaskComponent,
            UIName2ID.CommonItemHighlight,
            UIName2ID.IVHbComponent,
            UIName2ID.IVWeaponBPComponent,
            UIName2ID.IVGreyMask,
            UIName2ID.IVItemHover,
            UIName2ID.IVMaskSmallLockComponent,
            UIName2ID.IVMaskLockComponent,
            UIName2ID.IVTextQualityComponent,
            UIName2ID.IVGiveawayComponent,
            UIName2ID.IVRandomSkinComponent,
            UIName2ID.IVActivatedComponent,
            UIName2ID.IVUsingComponent,
        }
    }

    UITable[UIName2ID.IVCommonItemTemplate] = {
        UILayer = EUILayer.Sub,
        LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.ItemView.IVCommonItemTemplate",
        BPKey = "WBP_CommonItemTemplate",
        -- 当前itemview的subui配置移植到preloadconfig中。根据需求进行配置，当前位置配置只配置通用组件。
        SubUIs = {
            UIName2ID.IVTextIconComponent,
            UIName2ID.IVHbComponent,
            UIName2ID.IVGetMaskComponent,
            UIName2ID.IVItemSoldOut,
            UIName2ID.IVAccessoryComponent,
            UIName2ID.IVItemHover,
            UIName2ID.IVFreeComponent,
            UIName2ID.IVMaskSmallLockComponent,
            UIName2ID.IVTextQualityComponent,
            UIName2ID.IVMpComponent,
            UIName2ID.IVSolComponent,
            UIName2ID.IVCommercializeShadingComponent,
            UIName2ID.IVCommercializeItemComponent,
            UIName2ID.IVGiveawayComponent,
            UIName2ID.IVCommonItemAnimComp,
            UIName2ID.IVDecompositionComp,
            UIName2ID.SlotCompActivate,
            -- UIName2ID.IVInaccessibleComponent,
            UIName2ID.IVTimeLimitComponent,
        },
        Anim = {
            bManuelAnim = true,
        },
    }
else
    -- 仓库道具模板
    UITable[UIName2ID.IVWarehouseTemplate] = {
        UILayer = EUILayer.Sub,
        LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.ItemView.IVWarehouseTemplate",
        BPKey = "WBP_WarehouseItemTemplate",
        -- 当前itemview的subui配置移植到preloadconfig中。根据需求进行配置，当前位置配置只配置通用组件。
        -- 注意：手柄相关的控件HD独占，不入mobile包。相关控件在后面插入到本表SubUIs表中
        SubUIs = {
            UIName2ID.IVBgComponent,
            UIName2ID.IVTextIconComponent,
            UIName2ID.IVQualityComponent,
            UIName2ID.IVMainIconComponent,
            UIName2ID.IVTwoLinesTitleComponent,
            UIName2ID.IVTextQualityComponent,
            UIName2ID.ItemviewRepair,
            UIName2ID.IVWeaponBPComponent,
            UIName2ID.IVMissionComponent,
            UIName2ID.IVMarkComponent,
            UIName2ID.IVComparedComponent,
            UIName2ID.CommonItemHighlight,
            UIName2ID.IVCheckBoxComponent,
            UIName2ID.IVGreyMask,
            UIName2ID.IVTipMaskComponent,
            UIName2ID.IVReturn,
            UIName2ID.IVExposureComponent,
            UIName2ID.DragItemPreview,
            UIName2ID.IVItemSelectedComponent,
            UIName2ID.IVCantCarryOutComponent,
        },
        -- 需要手动管理动画播放
        Anim = {
            bManuelAnim = true
        }
    }
    if DFHD_LUA == 1 then
        -- 插入手柄提示SubUI
        local IVWarehouseTemplateGamepadAddition =
        {
            UIName2ID.ItemDetailCommonKeyIconTipsItem,
            UIName2ID.CommonKeyIconTips,
            UIName2ID.CommonKeyIconTipsItem,
        }
        table.append(UITable[UIName2ID.IVWarehouseTemplate].SubUIs, IVWarehouseTemplateGamepadAddition)
    end

    UITable[UIName2ID.IVShopItemTemplate] = {
        UILayer = EUILayer.Sub,
        LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.ItemView.IVShopItemTemplate",
        BPKey = "WBP_ShopItemTemplate",
        -- 当前itemview的subui配置移植到preloadconfig中。根据需求进行配置，当前位置配置只配置通用组件。
        SubUIs = {
            UIName2ID.IVBgComponent,
            UIName2ID.IVTextIconComponent,
            UIName2ID.IVQualityComponent,
            UIName2ID.IVMainIconComponent,
            UIName2ID.IVItemSelectedComponent,
            UIName2ID.CommonItemHighlight,
            UIName2ID.IVHbComponent,
            UIName2ID.IVWeaponBPComponent,
            UIName2ID.IVGreyMask,
            UIName2ID.IVItemHover,
            UIName2ID.IVMaskSmallLockComponent,
            UIName2ID.IVMaskLockComponent,
            UIName2ID.IVTextQualityComponent,
            UIName2ID.IVCommercializeShadingComponent,
            UIName2ID.IVRandomSkinComponent,
            UIName2ID.IVGiveawayComponent,
            UIName2ID.IVActivatedComponent,
            UIName2ID.IVUsingComponent,
        }
    }

    UITable[UIName2ID.IVCommonItemTemplate] = {
        UILayer = EUILayer.Sub,
        LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.ItemView.IVCommonItemTemplate",
        BPKey = "WBP_CommonItemTemplate",
        -- 当前itemview的subui配置移植到preloadconfig中。根据需求进行配置，当前位置配置只配置通用组件。
        SubUIs = {
            UIName2ID.IVTextIconComponent,
            UIName2ID.IVItemHover,
            UIName2ID.IVMaskSmallLockComponent,
            UIName2ID.IVItemSelectedComponent,
            UIName2ID.IVMaskLockComponent,
            UIName2ID.IVTwoLinesTitleComponent,
            UIName2ID.IVCommercializeShadingComponent,
            UIName2ID.IVTextQualityComponent,
            UIName2ID.IVDecompositionComp,
            -- UIName2ID.IVInaccessibleComponent,
        },
        Anim = {
            bManuelAnim = true,
        },
    }
end

UITable[UIName2ID.IVCommercializeItemTemplate] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.ItemView.IVCommercializeItemTemplate",
    BPKey = "WBP_CommerCializeItemTemplate",
    SubUIs = {
        UIName2ID.IVBgComponent,
        UIName2ID.IVCommercializeShadingComponent,
        UIName2ID.IVCommercializeItemComponent,
        UIName2ID.IVQualityComponent,
        UIName2ID.IVGiveawayComponent,
        UIName2ID.IVTextIconComponent,
    }
}

UITable[UIName2ID.IVInsuranceTemplate] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.ItemView.IVInsuranceTemplate",
    BPKey = "WBP_InsuranceItemTemplate",
    SubUIs = {
        UIName2ID.IVBgComponent,
        UIName2ID.IVTextIconComponent,
        UIName2ID.IVQualityComponent,
        UIName2ID.IVMainIconComponent,
        UIName2ID.IVItemSelectedComponent,
        UIName2ID.IVTipMaskComponent,
        UIName2ID.CommonItemHighlight,
        UIName2ID.IVHbComponent,
    }
}

--BEGIN MODIFICATION @ VIRTUOS : 添加通用空白控件，用于辅助手柄导航
UITable[UIName2ID.IVCommonBlankItemTemplate] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.HD.IVCommonBlankItemTemplate",
    BPKey = "WBP_CommonBlankItem"
}
--END MODIFICATION

UITable[UIName2ID.IVItemHover] = {
	UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVComponentBase"),
    BPKey = "WBP_ItemHover"
}

UITable[UIName2ID.IVBgComponent] = {
	UILayer = EUILayer.Sub,
	LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVBgComponent"),
    BPKey = "WBP_SlotCompBg"
}

UITable[UIName2ID.IVTextIconComponent] = {
	UILayer = EUILayer.Sub,
	LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVTextIconComponent"),
    BPKey = "WBP_SlotCompIconLabel",
    SubUIs = {
        UIName2ID.IVShadingQualityComponent
    },
    bManuelAnim = true,
}

UITable[UIName2ID.IVTextQualityComponent] = {
	UILayer = EUILayer.Sub,
	LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVTextQualityComponent"),
    BPKey = "WBP_SlotCompIconLabel2",
    SubUIs = {
        UIName2ID.IVShadingQualityComponent,
        UIName2ID.IVCrossoverLabelComponent
    },
    bManuelAnim = true,
}

UITable[UIName2ID.IVCrossoverLabelComponent] = {
	UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVComponentBase"),
    BPKey = "WBP_SlotCompIconCrossoverLabel",
}

UITable[UIName2ID.IVIconComponent] = {
	UILayer = EUILayer.Sub,
	LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVIconComponent"),
    BPKey = "WBP_SlotCompIcon"
}

UITable[UIName2ID.IVCheckBoxComponent] = {
	UILayer = EUILayer.Sub,
	LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVCheckBoxComponent"),
    BPKey = "WBP_SlotCompMultiSelected"
}

UITable[UIName2ID.IVQualityComponent] = {
	UILayer = EUILayer.Sub,
	LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVQualityComponent"),
    BPKey = "WBP_SlotCompQuality",
    SubUIs = {
        UIName2ID.IVQualityLootingShowAllComponent
    }
}

UITable[UIName2ID.IVQualityLootingShowAllComponent] = {
	UILayer = EUILayer.Sub,
	LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVQualityLootingShowAllComponent"),
    BPKey = "WBP_SlotCompQualityLootingShowAll"
}

UITable[UIName2ID.IVMainIconComponent] = {
	UILayer = EUILayer.Sub,
	LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVMainIconComponent"),
    BPKey = "WBP_SlotCompLabel"
}

UITable[UIName2ID.IVSellPriceComponent] = {
	UILayer = EUILayer.Sub,
	LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVSellPriceComponent"),
    BPKey = "WBP_SlotCompPrice"
}

UITable[UIName2ID.IVItemSelectedComponent] = {
	UILayer = EUILayer.Sub,
	LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVSelectedComponent"),
    BPKey = "WBP_SlotCompSelected"
}

UITable[UIName2ID.IVUsingComponent] = {
	UILayer = EUILayer.Sub,
	LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVComponentBase"),
    BPKey = "WBP_SlotCompUsing"
}

UITable[UIName2ID.IVTipMaskComponent] = {
	UILayer = EUILayer.Sub,
	LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVTipMaskComponent"),
    BPKey = "WBP_SlotCompTip"
}

UITable[UIName2ID.IVTipUnlockedComponent] = {
	UILayer = EUILayer.Sub,
	LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVTipUnlockedComponent"),
    BPKey = "WBP_SlotCompUnlocked"
}

UITable[UIName2ID.IVHbComponent] = {
	UILayer = EUILayer.Sub,
	LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVHbComponent"),
    BPKey = "WBP_SlotCompHorizontalBox"
}

UITable[UIName2ID.IVVbComponent] = {
	UILayer = EUILayer.Sub,
	LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVHbComponent"),
    BPKey = "WBP_SlotCompVerticalBox"
}

UITable[UIName2ID.ItemViewUseAnim] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.ItemViewUseAnim"),
    BPKey = "WBP_SlotCompUseAnim"
}

UITable[UIName2ID.IVGreyMask] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVComponentBase"),
    BPKey = "WBP_SlotCompGreyMask"
}

UITable[UIName2ID.IVReturn] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVComponentBase"),
    BPKey = "WBP_SlotCompReturn",
    Anim = {
        bManuelAnim = true,
    }
}

UITable[UIName2ID.ItemViewDurabilityNew] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.ItemViewDurabilityNew"),
    BPKey = "WBP_SlotCompDurability",
}
UITable[UIName2ID.ItemViewDurabilityNewSub] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.ItemViewDurabilityNewSub"),
    BPKey = "DurabilityItem",
}

UITable[UIName2ID.EquipmentContainerPreview] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.EquipmentContainerPreview"),
    BPKey = "WBP_EquipmentContainerPreview_Main",
    SubUIs = {
        UIName2ID.EquipmentSpaceItemWT,
    }
}

UITable[UIName2ID.IVPutInSafeboxComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVPutInSafeboxComponent"),
    BPKey = "WBP_SlotCompSafeboxEffect",
}

UITable[UIName2ID.IVContainerPreview] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVContainerPreview"),
    BPKey = "WBP_ContainerPreview",
}

UITable[UIName2ID.EquipmentSpaceItemWT] =
{
    UILayer = EUILayer.Sub,
    BPKey = "WT_EquipmentSpaceItem"
}

UITable[UIName2ID.IVItemSoldOut] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVComponentBase"),
    BPKey = "WBP_SlotCompSoldOut"
}

UITable[UIName2ID.IVMarkComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVMarkComponent"),
    BPKey = "WBP_Itemview_Favorite"
}

UITable[UIName2ID.IVItemMarkComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVItemMarkComponent"),
    BPKey = "WBP_Itemview_Mark"
}

UITable[UIName2ID.IVDispensingComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVDispensingComponent"),
    BPKey = "WBP_SlotCompDispensing"
}

UITable[UIName2ID.IVDecBtnComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVDecBtnComponent"),
    BPKey = "WBP_SlotCompDecBtn"
}

UITable[UIName2ID.IVSelectNumComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVSelectNumComponent"),
    BPKey = "WBP_SlotCompSelectNum"
}

UITable[UIName2ID.IVMaskLockComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVMaskLockComponent"),
    BPKey = "WBP_SlotCompMaskLock"
}

UITable[UIName2ID.IVShoppingCartComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVComponentBase"),
    BPKey = "WBP_SlotCompShoppingCart"
}

UITable[UIName2ID.IVSoldOutAbnormalComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVComponentBase"),
    BPKey = "WBP_SoldOut"
}

UITable[UIName2ID.IVMissionComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVMissionComponent"),
    BPKey = "WBP_Itemview_Hint"
}

UITable[UIName2ID.IVExposureComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVExposureComponent"),
    BPKey = "WBP_Itemview_TipsText"
}

UITable[UIName2ID.IVInaccessibleComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVInaccessibleComponent"),
    BPKey = "WBP_Itemview_Inaccessible",
    Anim = {
        bManuelAnim = true,
    }
}

UITable[UIName2ID.IVCantCarryOutComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVComponentBase"),
    BPKey = "WBP_Itemview_Notallowedtotakeout"
}

UITable[UIName2ID.IVSearchAnimComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVSearchAnimComponent"),
    BPKey = "WBP_SlotComp_SearchAnim",
    Anim = {
        bManuelAnim = true,
    }
}

UITable[UIName2ID.IVComparedComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVComparedComponent"),
    BPKey = "WBP_Itemview_Compared"
}

UITable[UIName2ID.IVWaitingSearchComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVWaitingSearchComponent"),
    BPKey = "WBP_SlotCompWaitLoot"
}

UITable[UIName2ID.IVShowNewItemComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVShowNewItemComponent"),
    BPKey = "WBP_SlotCompNewTag"
}

UITable[UIName2ID.IVWeaponBPComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVWeaponBPComponent"),
    BPKey = "WBP_SlotCompBlueprintLabel"
}

UITable[UIName2ID.IVShowDefaultUsingComponent] =
{
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVShowDefaultUsingComponent"),
    BPKey = "WBP_SlotCompDefaultUsing"
}


UITable[UIName2ID.IVMagazineComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVMagazineComponent"),
    BPKey = "WBP_SlotCompMagazine",
}

UITable[UIName2ID.IVAccessoryComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVComponentBase"),
    BPKey = "WBP_SlotCompAccessoryStatus",
}

UITable[UIName2ID.IVCollectionComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVComponentBase"),
    BPKey = "WBP_Itemview_Collections",
}

UITable[UIName2ID.IVContainerExpiredComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVComponentBase"),
    BPKey = "WBP_SlotCompExpired",
}

UITable[UIName2ID.IVShadingQualityComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVComponentBase"),
    BPKey = "WBP_SlotCompName",
}

UITable[UIName2ID.IVTwoLinesTitleComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVTwoLineTitleComponent"),
    BPKey = "WBP_SlotCompTitle",
}

UITable[UIName2ID.IVMaskSmallLockComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVComponentBase"),
    BPKey = "WBP_SlotCompMaskSmallLock",
}

UITable[UIName2ID.IVMpComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVComponentBase"),
    BPKey = "WBP_SlotCompMP",
}

UITable[UIName2ID.IVSolComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVComponentBase"),
    BPKey = "WBP_SlotCompSOL",
}

UITable[UIName2ID.IVFreeComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVFreeComponent"),
    BPKey = "WBP_SlotCompFree",
}

UITable[UIName2ID.IVPreciseComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVComponentBase"),
    BPKey = "WBP_SlotCompPrecise",
}

UITable[UIName2ID.IVCommercializeShadingComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.ItemView.Components.IVCommercializeShadingComponent",
    BPKey = "WBP_CializeItemShading",
}

UITable[UIName2ID.IVCommercializeItemComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.ItemView.Components.IVCommercializeItemComponent",
    BPKey = "WBP_CializeItemIcon",
}

UITable[UIName2ID.IVGiveawayComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVComponentBase"),
    BPKey = "WBP_Store_Giveaway",
}

UITable[UIName2ID.IVRandomSkinComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVRandomSkinComponent"),
    BPKey = "WBP_SlotCompRandom"
}

-- commmonitem动效组件
UITable[UIName2ID.IVCommonItemAnimComp] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVComponentBase"),
    BPKey = "WBP_Itemview_LightEffect",
}

UITable[UIName2ID.SlotCompActivate] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVComponentBase"),
    BPKey = "WBP_SlotCompActivate",
}

UITable[UIName2ID.IVDecompositionComp] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVDecompositionComp"),
    BPKey = "WBP_DecompositionTips",
}

UITable[UIName2ID.IVUsingYellowComponent] = {
	UILayer = EUILayer.Sub,
	LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVComponentBase"),
    BPKey = "WBP_SlotCompUsingYellow"
}

UITable[UIName2ID.IVTimeLimitComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVComponentBase"),
    BPKey = "WBP_SlotComp_TimeLimit"
}
--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region ItemView

--[[UITable[UIName2ID.ItemView] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.ItemView"),
    BPKey = "WBP_ItemView",
    SubUIs = {
        UIName2ID.ItemViewUseAnim,
        UIName2ID.DragItemPreview
    }
}--]]

UITable[UIName2ID.DragItemPreview] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.DragItemPreview"),
    BPKey = "DragItemPreview"
}

UITable[UIName2ID.DragBadgeItemPreview] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.DragBadgeItemPreview"),
    BPKey = "DragBadgeItemPreview"
}

UITable[UIName2ID.ItemLabelMarkBtn] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.ItemLabelMarkBtn"),
    BPKey = "WBP_ItemLabelMarkBtn"
}

UITable[UIName2ID.CommonCheckcollection] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.ItemLabelMarkBtn"),
    BPKey = "WBP_DFCommonCheckcollection"
}

UITable[UIName2ID.ItemSimpleView] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.ItemSimpleView"),
    BPKey = "WBP_ItemSimpleView"
}

UITable[UIName2ID.ItemViewQualitySubView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.ItemView.ItemViewQualitySubView",
    BPKey = "WBP_Itemview_QualityRectangle",
}

UITable[UIName2ID.ItemviewRepair] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.ItemView.Components.ItemViewRepair",
    BPKey = "WBP_Itemview_Repair",
}

--endregion
-----------------------------------------------------------------------

--------------------------------------------------------------------------
--- Tip/Pop/Confirm Window相关组件
--------------------------------------------------------------------------
UITable[UIName2ID.CommonTipWindows] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonTipWindows",
    BPKey = "WBP_CommonPopWindowsV1",
    Anim = {
        FlowInAni = "Ani_In",
        FlowOutAni = "Ani_Out"
    }
}

UITable[UIName2ID.CommonConfirmWindows] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonConfirmWindows",
    BPKey = "WBP_CommonPopWindowsV3",
    IsModal = true,
    Anim = {
        FlowInAni = "WBP_CommonPopWindowsV3_in",
        FlowOutAni = "WBP_CommonPopWindowsV3_out"
    }
}

UITable[UIName2ID.CommonPopWindows] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindows",
    BPKey = "WBP_CommonPopWindowsV2",
    IsModal = true,
    SubUIs = {
		UIName2ID.InputSummaryItemHD,
        UIName2ID.DFCommonButtonV1S1,
        UIName2ID.DFCommonButtonV1S2
    },
    -- 需要手动管理动画播放
    Anim = {
        bManuelAnim = true
    }
}

--------------------------------------------------------------------------
--- CommonPopWindows Dynamic
--------------------------------------------------------------------------
UITable[UIName2ID.CommonPopWindowV2Scroll] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindow.CommonPopWindowV2Scroll",
    BPKey = "WBP_CommonPopWindowV2Scroll",
    IsModal = true,
    SubUIs = {
        UIName2ID.DFCommonButtonV1S1,
        UIName2ID.DFCommonButtonV1S2
    }
}

UITable[UIName2ID.CommonPopWindowV2Waterfall] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindow.CommonPopWindowV2Waterfall",
    BPKey = "WBP_CommonPopWindowV2Waterfall",
    IsModal = true,
    SubUIs = {
        UIName2ID.DFCommonButtonV1S1,
        UIName2ID.DFCommonButtonV1S2,
        UIName2ID.CommonPopItemTitleDesc,
    }
}

UITable[UIName2ID.CommonPopItemSingleDesc] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindow.CommonPopItem.CommonPopItemSingleDesc",
    BPKey = "WBP_CommonPopItemSingleDesc"
}

UITable[UIName2ID.CommonPopItemTitleDesc] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindow.CommonPopItem.CommonPopItemTitleDesc",
    BPKey = "WBP_CommonPopItemTitleDesc"
}

UITable[UIName2ID.CommonPopWindowsRichText] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindowsRichText",
    BPKey = "WBP_CommonPopWindowsRichText",
    IsModal = true,
}

UITable[UIName2ID.CommonSetUpPopWindows] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonSetUpPopWindows",
    BPKey = "WBP_SetUp_PopWindows",
    IsModal = true,
    SubUIs = {
        UIName2ID.DFCommonButtonV1S1,
        UIName2ID.DFCommonButtonV1S2
    }
}

UITable[UIName2ID.CommonSpaceGrid] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonSpaceGrid",
    BPKey = "WBP_ItemPropertySpaceGrid"
}

-----------------------------------------------------------------------
--region Selection valid

UITable[UIName2ID.CommonValidBox] = {
    UILayer = EUILayer.Sub,
    LuaPath = "",
    BPKey = "G_Common_DragSelectedBox_Valid"
}

UITable[UIName2ID.CommonInValidBox] = {
    UILayer = EUILayer.Sub,
    LuaPath = "",
    BPKey = "G_Common_DragSelectedBox_Stop"
}

UITable[UIName2ID.CommonItemHighlight] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.ItemView.Components.CommonItemHighlight",
    BPKey = "WVP_Common_DragSelectedBox"
}

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Key

-- UITable[UIName2ID.InvKeySlotView] = { -- Error BPPath设置已废弃，BPKey必须填写
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.Key.InvKeySlotView",
--     BPPath = "",
--     SubUIs = {
--         UIName2ID.KeySpaceCell
--     }
-- }

-- UITable[UIName2ID.KeySpaceCell] = {
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.Key.KeySpaceCell",
--     BPKey = "WBP_InvKeySlot",
-- }
-------通用下拉框

--[[UITable[UIName2ID.CommonDroDownBtn] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonDroDownBtn",
    BPKey = "WBP_CommonDroDownBox_Btn"
}--]]

UITable[UIName2ID.CommonDroDownNewBtn] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonDroDownBtn",
    BPKey = "WBP_CommonDroDownBox_Btn_Button"
}

UITable[UIName2ID.CommonItemViewDropDownBox] =
{
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.DropDown.CommonItemViewDropDownBox",
    BPKey = "WBP_CommonItemViewDropDownBox",
    Anim = {
        bManuelAnim = true
    }
}

UITable[UIName2ID.CommonItemViewDropDownItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.DropDown.CommonItemViewDropDownItem",
    BPKey = "WBP_CommonItemViewDropDownItem",
}

UITable[UIName2ID.WHEquipKeyIconBoxText] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.ItemView.KeyBoxText",
    BPKey = "WBP_LootingKeyIcon_Mobile"
}

--endregion
-----------------------------------------------------------------------

--------------------------------------------------------------------------
--- 负重相关 ProgressBar
--------------------------------------------------------------------------

UITable[UIName2ID.CommonLoadButton] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.Weight.CommonLoadButton",
    BPKey = "WBP_SlotCompLoadBtn"
}

UITable[UIName2ID.CommonLoadTips] = {
    UILayer = EUILayer.Tip,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.Weight.CommonLoadTips",
    BPKey = "WBP_CommonLoadTips",
    Anim = {
        FlowInAni = "WBP_CommonLoadTips_in",
        FlowOutAni = "WBP_CommonLoadTips_out"
    }
}

--region DFHD Start
if DFHD_LUA == 1 then
    UITable[UIName2ID.CommonHDLoadButton] = {
        UILayer = EUILayer.Sub,
        LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.Weight.CommonLoadButton",
        BPKey = "WBP_SlotCompLoadBtn_PC"
    }
end
--endregion
-----------------------------------------------------------------------

--------------------------------------------------------------------------
--- 端游HD
--------------------------------------------------------------------------
UITable[UIName2ID.HDKeyIcon] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.HD.HDKeyIcon",
    BPKey = "WBP_CommonKeyIcon"
}

UITable[UIName2ID.HDKeyIconBox] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.HD.HDKeyIconBox",
    BPKey = "WBP_CommonKeyIconBox"
}

UITable[UIName2ID.HDKeyIconBoxText] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.HD.HDKeyIconBoxText",
    BPKey = "WBP_CommonKeyIconBoxText",
    ReConfig = {
        IsPoolEnable = true,
        MaxPoolLength = 10
    }
}

UITable[UIName2ID.HDKeyIconBoxWithText] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.HD.HDKeyIconBoxWithText",
    BPKey = "WBP_CommonKeyIconBoxWithText"
}

UITable[UIName2ID.HDWHEquipKeyIconBoxText] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.HD.HDKeyIconBoxText",
    BPKey = "WBP_LootingKeyIcon"
}

UITable[UIName2ID.CharacterCaptureUtil] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.HD.CharacterCaptureUtil",
    BPKey = "WBP_CharacterCaptureImg"
}
--------------------------------------------------------------------------

--------------------------------------------------------------------------
--- CommonIconWithBtn
--------------------------------------------------------------------------
--[[UITable[UIName2ID.CommonIconWithBtn] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonIconWithBtn", 
    BPKey = "WBP_CommonIconWithBtn",
}--]]

-----------------------------------------------------------------------
--region 菜单按钮列表

-- 菜单按钮列表-内容项-纯文字
UITable[UIName2ID.CommonRightclickItemText] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.SimpleBtnList.SimpleBtnListPanelItemTypeText"),
    BPKey = "WBP_CommonRightBtnPc",
}

-- 菜单按钮列表-内容项-文字和图片
UITable[UIName2ID.CommonRightclickItemCheckBox] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.SimpleBtnList.SimpleBtnListPanelItemTypeCheckbox"),
    BPKey = "WBP_CommonRightTapPc",
}

-- 菜单按钮列表
UITable[UIName2ID.CommonRightclick] = {
    UILayer = EUILayer.Pop,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.SimpleBtnList.SimpleBtnListPanel"),
    BPKey = "WBP_CommonRightclickPc",
    SubUIs = {
		UIName2ID.CommonRightclickItemText,
		UIName2ID.CommonRightclickItemCheckBox,
	},
    IsModal = true,
}

if IsHD() then
    UITable[UIName2ID.CommonRightclick].IsForceGameAndUI = true -- 强制启用PlayerInput的输入
end

--BEGIN MODIFICATION @ VIRTUOS : 
-- 菜单按钮列表
if IsHD() then
    UITable[UIName2ID.CommonTransferItemPanel] = {
        UILayer = EUILayer.Pop,
        LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.SimpleBtnList.SimpleBtnListPanel"),
        BPKey = "WBP_CommonTransferItemPc",
        SubUIs = {
            UIName2ID.CommonRightclickItemText,
            UIName2ID.CommonRightclickItemCheckBox,
        },
    }
end
--END MODIFICATION

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Common Video View
UITable[UIName2ID.CommonVideoPopView] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonVideoView.CommonVideoPopView",
    BPKey = "WBP_CommonVideoPop",
    -- ReConfig = {
    --     IsPoolEnable = true,
    --     MaxPoolLength = 1,
    -- },
    IsModal = true,
}

UITable[UIName2ID.CommonVideoFullScreenView] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonVideoView.CommonVideoFullScreenView",
    BPKey = "WBP_CommonFullScreenVideo",
    IsModal = true,    
    -- ReConfig = {
    --     IsPoolEnable = true,
    --     MaxPoolLength = 1,
    -- },
}

UITable[UIName2ID.CommonVideoChangeView] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonVideoView.CommonVideoChangeView",
    BPKey = "WBP_CommonFullScreenVideoChange",
    IsModal = true,
}

UITable[UIName2ID.CommonVideoProgressBar] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonVideoView.CommonVideoProgressBar",
    BPKey = "WBP_CommonVideoProgressBar"
}
-----------------------------------------------------------------------

-----------------------------------------------------------------------
UITable[UIName2ID.CommonNavigationSelectorHD] = {
    UILayer = EUILayer.Tip, -- @todo暂时用tip层
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.Navigation.CommonNavigationSelector",
    BPKey = "WBP_CommonNavigationSelectorHD",
    SubUIs = {
		UIName2ID.NavigationSelectorStyleHD_Default,
        UIName2ID.NavigationSelectorStyleHD_Strong
	}
}

UITable[UIName2ID.NavigationSelectorStyleHD_Default] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.Navigation.NavigationSelectorStyle",
    BPKey = "WBP_NavigationSelectorStyleHD_Default"
}

UITable[UIName2ID.NavigationSelectorStyleHD_Strong] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.Navigation.NavigationSelectorStyle",
    BPKey = "WBP_NavigationSelectorStyleHD_Strong"
}
-----------------------------------------------------------------------

UITable[UIName2ID.GlobalUIRegisterView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.GlobalUIRegisterView.GlobalUIRegisterView",
    BPKey = "WBP_GlobalUIRegisterView",
    SubUIs = {
        UIName2ID.CommonItemHighlight,
        UIName2ID.IVQualityLootingShowAllComponent,
    },
}

----------------------------------------------------------------------

UITable[UIName2ID.CommonVideocomponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonVideoView.CommonVideoComponent",
    BPKey = "WBP_CommonVideoComponent"
}


--武器解锁进度面板
UITable[UIName2ID.CommonWeaponSkinMissionProgress] = {
    UILayer = EUILayer.Sub, 
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonWeaponSkinMissionProgress", 
    BPKey = "WBP_DFCommon_MissionUnlocked",
    SubUIs = {
        UIName2ID.CommonWeaponSkinMissionProgressItem,
    },
    Anim = {
		bManuelAnim = true
    }
}


UITable[UIName2ID.CommonWeaponSkinMissionProgressItem] = {
    UILayer = EUILayer.Sub, 
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonWeaponSkinMissionProgressItem", 
    BPKey = "WBP_DFCommon_UnlockedItem",
    SubUIs = {
    },
    Anim = {
        bManuelAnim = true
    }
}


UITable[UIName2ID.CommonWeaponSkinMissionOverview] = {
    UILayer = EUILayer.Pop, 
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonWeaponSkinMissionOverview", 
    BPKey = "WBP_Common_UnlockDescription",
    SubUIs = {
        UIName2ID.CommonWeaponSkinMissionProgressItem,
    },
    Anim = {
    },
    IsModal = true
}


UITable[UIName2ID.WBP_Common_TopBarV2] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonTopBarV2",
    BPKey = "WBP_Common_TopBarV2",
    Anim = {
        bManuelAnim = true
    }
}

UITable[UIName2ID.CommonKeyIconTipsItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonKeyIconTipsItem",
    BPKey = "WBP_CommonKeyIconTipsBox",
    Anim = {
    }
}

UITable[UIName2ID.CommonKeyIconTips] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.CommonKeyIconTips",
    BPKey = "WBP_CommonKeyIconTips",
    Anim = {
    }
}
----------------------------------------------------------------------

-- 语言切换下载按钮
UITable[UIName2ID.SystemSettingLanguageCell] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonWidgetModule.UI.Weight.SystemSettingLanguageCell",
    BPKey = "WBP_SetUpComponent_LanguageBtn"
}

UITable[UIName2ID.IVActivatedComponent] = {
	UILayer = EUILayer.Sub,
	LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVActivatedComponent"),
    BPKey = "WBP_SlotComp_Activated"
}


---@class CommonWidgetConfig
local CommonWidgetConfig =
{
    GunSkinRewardsConfig = Facade.TableManager:GetTable("WeaponSkin/GunSkinRewards"),
    MasterGunSkinRewardsConfig = Facade.TableManager:GetTable("WeaponSkin/MasterGunSkinRewards"),
    ActivityGoalsConfig = Facade.TableManager:GetTable("ActivityTaskGoals"),
    
    Loc = {
        InputName2Long = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_InputName2Long", "名字过长���请输入1~%d个字符"),
        InputNameEmpty = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_InputNameEmpty", "名字为空，请重新输入"),
        ItemCantSell = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_ItemCantSell", "<customstyle color=\"Color_Highlight01\">%s</>不可被系统回收"),

        CommonBtnTextDefault = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_CommonBtnTextDefault", "%s"),
        timeLimitText = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_timeLimitText", "限时"),
        dayLeftText = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_dayLeftText", "{NumOfDay}天"),
        useLimitText = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_useLimitText", "{NumOfLimit}次"),
        ConfirmTextDefault = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_ConfirmTextDefault", "确认"),
        CancelTextDefault = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_CancelTextDefault", "取消"),

        NothingFound = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_NothingFound", "没配"),

        LoadWeightTitle = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_LoadWeightTitle", "负重"),
        LoadWeightDesc = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_LoadWeightDesc", "负重增加会使奔跑速度下降，体力消耗量增加。"),
        NormalLoadText = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_NormalLoadText", "正常"),
        LittleWeightLoadText = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_LittleWeightLoadText", "轻度超重"),
        MiddleWeightLoadText = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_MiddleWeightLoadText", "中度超重"),
        OverWeightLoadText = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_OverWeightLoadText", "超重"),
        OverWeightLoadRange = NSLOCTEXT("CommonWidgetModule","Lua_CommonWidget_OverWeightLoadRange","负重在<customstyle color=\"Color_Buffer_03\">%skg到%skg</>之间"),
        SevereOverWeightLoadRange = NSLOCTEXT("CommonWidgetModule","Lua_CommonWidget_SevereOverWeightLoadRange","负重达到<customstyle color=\"Color_Buffer_01\">%skg</>及以上"),
        BagItemsStateChanged = NSLOCTEXT("CommonWidgetModule","Lua_CommonWidget_BagItemsStateChanged","背包物品已<customstyle color=\"%s\">%s</>"),
        BagFashionBtn = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_BagFashionBtn", "切换时装"),

        --- BEGIN MODIFICATION @ VIRTUOS
        -- 好友点赞
        MatesPraiseBtnText = NSLOCTEXT("CommonWidgetModule", "Lua_Mates_Praise_BtnText", "点赞"),
        -- Console 查看用户档案
        PlatformProfilePSBtnText = NSLOCTEXT("CommonWidgetModule", "Console_Lua_ProfileBtn_PlayStation", "查看档案"),
        PlatformProfileXSXBtnText = NSLOCTEXT("CommonWidgetModule", "Console_Lua_ProfileBtn_Xbox", "查看Xbox档案"),
        --- END MODIFICATION
        AddFriendBtnText = NSLOCTEXT("CommonWidgetModule", "Lua_Add_Friend_BtnText", "加好友"),
        DelFriendBtnText = NSLOCTEXT("CommonWidgetModule", "Lua_Del_Friend_BtnText", "删好友"),
        InviteTeamBtnText = NSLOCTEXT("CommonWidgetModule", "Lua_Invite_Team_BtnText", "邀请组队"),
        AppTeamBtnText = NSLOCTEXT("CommonWidgetModule", "Lua_App_Team_BtnText", "申请入队"),
        ViewInformaBtnText = NSLOCTEXT("CommonWidgetModule", "Lua_View_Informa_BtnText", "信息"),
        KickTeamBtnText = NSLOCTEXT("CommonWidgetModule", "Lua_Kick_Team_BtnText", "踢出队伍"),
        KickRoomBtnText = NSLOCTEXT("CommonWidgetModule", "Lua_Kick_Room_BtnText", "踢出房间"),
        ChatBtnText = NSLOCTEXT("CommonWidgetModule", "Lua_Chat_BtnText", "私聊"),
        AddBlackBtnText = NSLOCTEXT("CommonWidgetModule", "Lua_Add_Black_BtnText", "屏蔽"),
        RomoveBlackBtnText = NSLOCTEXT("CommonWidgetModule", "LuaRemove_Black_BtnText", "解除屏蔽"),
        ReportBtnText = NSLOCTEXT("CommonWidgetModule", "Lua_Report_BtnText", "举报"),
        ReportVoiceBtnText = NSLOCTEXT("CommonWidgetModule", "Lua_Report_Voice_Btn_Text", "语音举报"),
        PromoRoomBtnText = NSLOCTEXT("CommonWidgetModule", "Lua_Promo_Room_BtnText", "转让房主"),
        PromoTeamBtnText = NSLOCTEXT("CommonWidgetModule", "Lua_Promo_Team_BtnText", "转让队长"),

        SubALLTabTitle = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_SubALLTabTitle", "全部"),
        MaxShowNum = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_MaxShowNum", "%d+"),
        CollectItemMaxShowNum = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_CollectItemMaxShowNum", "99.9M+"),
        notEquipBluePrint = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_notEquipBluePrint", "{NumOfBP}个涂装可用"),
        MultiplyStr = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_MultiplyStr", "x%s"),
        SourceStr = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_SourceStr", "来自：%s"),

        SoldOut = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_SoldOut", "<customstyle color=\"Color_DarkNegative\">已售罄</>"),
        CantIntoDS = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_CantIntoDS", "不可入局"),
        ExposurePos = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_ExposurePos", "位置暴露"),
        ForbiddenToCarryOut = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_ForbiddenToCarryOut", "不可带出"),

        TeammateBindingItemTip = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_TeammateBindingItemTip", "队友道具无法进行任何操作"),
        TeammateBindingContainerExistsTip = NSLOCTEXT("CommonWidgetModule", "Lua_ICommonWidget_TeammateBindingContainerExistsTip", "背包内有队友绑定道具，无法卸下"),
        TeammateBindingWeaponExistsTip = NSLOCTEXT("CommonWidgetModule", "Lua_ICommonWidget_TeammateBindingWeaponExistsTip", "武器内有队友绑定配件，无法进行任何操作"),
        TeammateBindingSlotItemTip = NSLOCTEXT("CommonWidgetModule", "Lua_ICommonWidget_TeammateBindingSlotItemTip", "槽位有队友绑定道具，无法进行切换"),
        AssemblyWeightRatioStr = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_AssemblyWeightRatioStr", "%s<customstyle color=\"%s\">/%s千克</>"),
        CommonWeightRatioStr = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_CommonWeightRatioStr", "<customstyle Color=\"C001\">%s</><customstyle Size=\"Size_BoldTitle01\">/%s千克</>"),
        NearbyPickupsNotAllowReplace = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_NearbyPickupsNotAllowReplace", "不可以交换散落物的位置"),
        

        VideoConfigError = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_VideoConfigError", "视频配置异常：%s"),
        VideoDownloadingTip = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_VideoDownloadingTip", "视频下载中，请稍后。[%.2fMB/%.2fMB]"),
        VideoTimeStampText = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_VideoTimeStampText", "{CurTime}/{TotalTime}"),
        VideoDownloadFailedText = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_VideoDownloadFailed", "视频下载失败，请点击重新下载"),

        RichDescText = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_RichDescText", "<customstyle color=\"C000\">%s</>\n%s"),

        -- 安全箱钥匙包到期时间文本
        DayText = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_DayText", "{day}天"),
        HourText = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_HourText", "{hour}时"),
        MinText = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_MinText", "{min}分"),
        SecondText = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_SecondText", "{sec}秒"),

        --皮肤任务解锁
        SOLModeName = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_SOLModeName", "烽火地带"),
        MPModeName = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_MPModeName", "全面战场"),
        WeaponSkinLocked = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_WeaponSkinLocked", "在任意模式中完成对应的外观解锁挑战，均可解锁该外观，并能在所有模式中使用"),
        WeaponSkinUnlocked = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_WeaponSkinUnlocked", "已完成外观解锁挑战，前往藏品领取外观"),
        UnknownMission = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_UnknownMission", "未知任务"),
        ChallengeNotActivated = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_ChallengeNotActivated", "挑战未激活"),
        AppearanceChallengeProgress = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_AppearanceChallengeProgress", "[{currentNum}/{maxNum}]外观挑战"),
        MasterChallengeProgress = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_MasterChallengeProgress", "[{currentNum}/{maxNum}]大师挑战"),
        TaskProgress = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_TaskProgress", "({currentNum}/{maxNum}){taskName}"),
        MysticalInstanceNum = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_MysticalInstanceNum", "典藏（{InstanceNum}）"),
        Mystical = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_Mystical", "典藏"),

        --- itemView
        UseStr = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_UseStr", "使用"),
        SplieAmmoStr = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_SplieAmmoStr", "拆分弹药"),
        DropItemStr = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_DropItemStr", "放下"),
        RotateItemStr = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_RotateItemStr", "旋转"),

        CancelUsingStr = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_CancelUsingStr", "取消使用"),
        --- weight
        loadTitleStr = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_loadTitleStr", "负重状态：%s"),

        -- azhengzheng:She 3
        MakeAnAppointmentToJoinTheTeam = NSLOCTEXT("CommonWidgetModule", "MakeAnAppointmentToJoinTheTeam", "预约入队"),
        BookAteamAppointment = NSLOCTEXT("CommonWidgetModule", "BookAteamAppointment", "预约组队"),

        LoadState = {
            [1] = NSLOCTEXT("CommonWidgetModule","Lua_CommonWidget_LoadState1","正常"),
            [2] = NSLOCTEXT("CommonWidgetModule","Lua_CommonWidget_LoadState2","负重"),
            [3] = NSLOCTEXT("CommonWidgetModule","Lua_CommonWidget_LoadState3","超重"),
        },
        LoadStateDetails = {
            [1] = NSLOCTEXT("CommonWidgetModule","Lua_CommonWidget_LoadStateDetails1","未进入负重状态，角色不受装备重量影响。"),
            [2] = NSLOCTEXT("CommonWidgetModule","Lua_CommonWidget_LoadStateDetails2","进入负重状态会导致速度下降，随负重增加，体力消耗和回复速度均会受到影响，同时跳跃高度和瞄准速度也会受到影响。"),
            [3] = NSLOCTEXT("CommonWidgetModule","Lua_CommonWidget_LoadStateDetails3","超重状态会导致速度严重下降，无法奔跑与跳跃，任何移动动作均会消耗体力，同时严重影响瞄准速度和体力回复速度。"),
        },
        CommonWeightDecimalStr = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_CommonWeightDecimalStr", "<customstyle Color=\"C001\">{integer}.{decimal}</><customstyle Size=\"Size_BoldTitle01\">/{overWeight}千克</>"),
        LocalVOSizeTxt = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_LocalVOSizeTxt", "{cultureName}（{PackageSize}）"),
        ItemSizeTxt = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_ItemSizeTxt", "{displayName}（{PackageSize}）"),
        CultureResDownloadFinished = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_CultureResDownloadFinished", "{CultureName}文本下载成功"),
        GameItemNameWithPrefix = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_GameItemNameWithPrefix", "{0}{1}"),

        TributeBtnText = NSLOCTEXT("CommonWidgetModule", "Lua_CommonWidget_TributeBtnText", "致敬"),
    
    },
    ResList = {
        CommonAllTabImgPath = "PaperSprite'/Game/UI/UIAtlas/FrontEnd/F_Common/F_Common/BakedSprite/F_Common_ItemClass_Icon_Seleted_All.F_Common_ItemClass_Icon_Seleted_All'",
    },
    pcRightIconList = {
        -- 定位
        [1] = "PaperSprite'/Game/UI/UIAtlas/System/HUD/BakedSprite/HUD_Controller_Btn_61.HUD_Controller_Btn_61'",
        -- 静音
        [2] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ConstrollerFunction_Btn_04.Common_ConstrollerFunction_Btn_04'",
        -- 取消静音
        [3] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ConstrollerFunction_Btn_03.Common_ConstrollerFunction_Btn_03'",
        -- 转让队长
        [4] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_SelfBuiltServer_07.Common_SelfBuiltServer_07'",
        -- 申请队长
        [5] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_SelfBuiltServer_04.Common_SelfBuiltServer_04'",
        -- 强制接管队长
        [6] = "PaperSprite'/Game/UI/UIAtlas/System/HUD/BakedSprite/HUD_Tips_Icon_0002.HUD_Tips_Icon_0002'",
        -- 申请指挥官
        [7] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_SelfBuiltServer_03.Common_SelfBuiltServer_03'",
        -- 指挥官交接
        [8] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_SelfBuiltServer_02.Common_SelfBuiltServer_02'",
        -- 加好友
        [9] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_PlayerState_Icon_0305.Common_PlayerState_Icon_0305'",
        -- 举报
        [10] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_PlayerState_Icon_0309.Common_PlayerState_Icon_0309'",
		-- 移入频道
        [11] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ConstrollerFunction_Btn_09.Common_ConstrollerFunction_Btn_09'",
		-- 移出频道
        [12] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ConstrollerFunction_Btn_15.Common_ConstrollerFunction_Btn_15'",
		
    },
    EWeightColor={
        Green = 0,
        Yellow = 1,
        Red = 2

    },

    -----------------------------------------------------------------------
    --region Item View Config
    
    EIVSlotPos = {
        TopLeft = 1,
        TopRight = 2,
        BottomLeft = 3,
        BottomRight = 4,
        Top = 5,
        Bottom = 6,
        
        BgLayer = 11,
        IconLayer = 12,
        BorderLayer = 13,

        MaskLayer = 21,

        StateLayer = 31, --- For Hover,Pressed
        Extra = 41,
    },

    EIVCompOrder = {
        SuperLow = 1,   -- 预留
        Low = 2,    -- 预留
        
        GreyMaskOrder = 1,
        DefaultOrder = 4,
        MaskLayerOrder = 6,
        HighestOrder = 10,

        Order1 = 11,
        Order2 = 12,
        Order3 = 13,
        Order4 = 14,
        Order5 = 15,

        High = 21,  -- 预留
        SuperHigh = 22, -- 预留
    },

    EIVIconTextStyle = {
        None = 0,
        ItemName = 1,
        Weight = 2,
        Armor = 3,
        Helmet = 4,
        Stack = 5,
        Binding = 6,
        InSafebox = 7,
        WeaponAmmo = 8,
        Mark = 9,
        IsEquippedSafebox = 10,
        UseNum = 11,
        NeedBuy = 12,
        Insurance = 13,
        InsuranceNum = 14,
        BagCapacityExtend = 15,
        Durability = 16,
        BulletHint = 17,
        WeaponBlueprintName = 18,
        DamagedMark = 19,
        LockIcon = 20,
        Countdown = 21,
        SkinRarity = 22,
        SpecialRarity = 23,
        GunStar = 24,
        Custom = 99,
    },

    EIVWarehouseTempComponent = {
        ItemIcon = 1,
        ItemBg = 2,
        ItemQuality = 3,
        ItemSelected = 4,
        SecondaryIcon = 5,
        CommercializeShading = 6,

        TopLeftIconText = 11,
        TopLeftNewTag = 12,
        TopLeftTwoLineText = 13,
        TopRightIconText = 21,
        TopRightWeaponBP = 22,
        TopRightItemNameText = 23,
        BottomLeftIconText = 31,
        BottomLeftIconText2 = 32,
        BottomLeftIconText3 = 33,
        BottomLeftIconText4 = 34,
        BottomRightIconText = 41,
        BottomRightIconText2 = 42,
        BottomRightContainerPreview = 43,
        BottomRightSellPrice = 44,
        BulletSlotIcon = 45,
        NeedBuyIcon = 46,
        LockIcon = 47,
        SoldOutAbnormal = 48,
        BottomRightLinkIcon = 49,

        MultiSelectedBox = 51,

        ItemHighlight = 61,
        TipMask = 62,
        UseAnim = 63,
        GreyMask = 64,
        SettlementDurability = 65,
        PutInSafebox = 66,
        ItemMark = 67,
        MissionItem = 68,
        SearchAnim = 69,
        SoldOutMask = 70,
        ExpireMask = 71,

        BottomRightHb = 71,

        GetMask=72,
        ExposureItem = 73,
        GiftBg = 74,
        LootingItemMark = 75,
        TeammateReturnMark = 76,

        WaitingSearch = 81,
        
        BPLock = 82,
        BPFree = 83,
        BPMp = 84,
        BPSol = 85,

        RandomSkin = 86,
        FFFF = 99,
    },

    ItemMaxShowNum = 999,
    CollectItemMaxShowNum = 99999999,

    -- 判断是哪种itemview
    EIVItemViewMode = {
        CommonSlotItemView = 1,
        EquipmentSlotItemView = 2,
        ShopItemView = 3,
        CommonViewItemView = 4,
        WHEquipmentItemView = 5,
        ListItemView = 6,
        CommonViewItemViewA = 7,
        CommonViewItemViewB = 8,
        CommonViewItemViewC = 9,
        CommonViewItemViewD = 10,
        CommonViewItemViewE = 11,
        None = 0
    },

    -- 判断该itemview中使用哪种容器
    EIVItemContainer = {
        LargeContainer = 1,
        SmallContainer = 2,
        SpecialContainer = 3,
    },

    EIVWeaponBPShowStyle = {
        NameAndShading = 1,
        OnlyName = 2,
        OnlyShading = 3,
        ShadingAndNoEquipText = 4,
        EquippedBp = 5,
        GainedBp = 6,
        NoOwnedBp = 7,
    },

    -- IVPrecreateConfig = {
    --     [ESlotType.BagContainer] = 30,
    --     [ESlotType.ChestHangingContainer] = 30,
    --     [ESlotType.SafeBoxContainer] = 4,
    --     [ESlotType.KeyChainContainer] = 5,
    --     [ESlotType.DeadbodyLootBox] = 20
    -- },
    --endregion
    -----------------------------------------------------------------------

    NavigationSelectorStyle = {
        Default = UIName2ID.NavigationSelectorStyleHD_Default,
        Strong = UIName2ID.NavigationSelectorStyleHD_Strong
    },

    EVODownloadType = {
        Downloaded = 0,  -- 已下载
        WaitDown = 1,    -- 待下载
        Donwing = 2,     -- 下载中
        PauseDown = 3    -- 暂停下载
    },
}

-- CommonWidgetConfig.IVIconTextStyleTable = {
--     [CommonWidgetConfig.EIVIconTextStyle.Binding] = {
        
--     },
-- }

CommonWidgetConfig.Events ={
    evtMultipleModeChanged = LuaEvent:NewIns("CommonWidgetConfig.Events.evtMultipleModeChanged"),
    evtPostItemMultiSelected = LuaEvent:NewIns("CommonWidgetConfig.Events.evtPostItemMultiSelected"),
    evtToggleDragItemSpin = LuaEvent:NewIns("CommonWidgetConfig.Events.evtToggleDragItemSpin"),
    evtItemOperaModeChanged = LuaEvent:NewIns("CommonWidgetConfig.evtItemOperaModeChanged"),

    evtInGameItemSelected = LuaEvent:NewIns("CommonWidgetConfig.evtInGameItemSelected"),

    evtNotifyItemHighlight = LuaEvent:NewIns("CommonWidgetConfig.evtNotifyItemHighlight"),

    -----------------------------------------------------------------------
    --region ItemView
    evtGlobalItemDragStart = LuaEvent:NewIns("CommonWidgetConfig.Events.evtGlobalItemDragStart"),
    evtGlobalItemDrop = LuaEvent:NewIns("CommonWidgetConfig.Events.evtGlobalItemDrop"),
    evtGlobalItemDragCancelled = LuaEvent:NewIns("CommonWidgetConfig.Events.evtGlobalItemDragCancelled"),
    evtGlobalItemPostDragEnter = LuaEvent:NewIns("CommonWidgetConfig.Events.evtGlobalItemPostDragEnter"),
    evtGlobalItemPostDragLeave = LuaEvent:NewIns("CommonWidgetConfig.Events.evtGlobalItemPostDragLeave"),
    evtGlobalItemSimulateDragStart = LuaEvent:NewIns("CommonWidgetConfig.Events.evtGlobalItemSimulateDragStart"),
    evtGlobalItemSimulateDragStop = LuaEvent:NewIns("CommonWidgetConfig.Events.evtGlobalItemSimulateDragStop"),

    evtItemDoubleClicked = LuaEvent:NewIns("CommonWidgetConfig.Events.evtItemDoubleClicked"),
    evtItemClicked = LuaEvent:NewIns("CommonWidgetConfig.Events.evtItemClicked"),
    evtItemClickedRight = LuaEvent:NewIns("CommonWidgetConfig.Events.evtItemClickedRight"),
    evtItemClickedByAuction = LuaEvent:NewIns("CommonWidgetConfig.Events.evtItemClickedByAuction"),
    evtItemSetSelected = LuaEvent:NewIns("CommonWidgetConfig.Events.evtItemSetSelected"),
    evtItemSingleSelected = LuaEvent:NewIns("CommonWidgetConfig.Events.evtItemSingleSelected"),

    evtGlobalItemShowPrice = LuaEvent:NewIns("CommonWidgetConfig.Events.evtGlobalItemShowPrice"),
    evtGlobalItemShowWeigth = LuaEvent:NewIns("CommonWidgetConfig.Events.evtGlobalItemShowWeigth"),
    evtGlobalItemShowDoubleClickHint = LuaEvent:NewIns("CommonWidgetConfig.Events.evtGlobalItemShowDoubleClickHint"),

    evtItemGrabbed = LuaEvent:NewIns("CommonWidgetConfig.Events.evtItemGrabed"),

    evtDragConfigChanged = LuaEvent:NewIns("CommonWidgetConfig.Events.evtDragConfigChanged"),

    evtIVImmediateClicked = LuaEvent:NewIns("CommonWidgetConfig.Events.IVImmediateClicked", false),
    evtIVMouseEnter = LuaEvent:NewIns("CommonWidgetConfig.Events.evtIVMouseEnter"),
    evtIVMouseLeave = LuaEvent:NewIns("CommonWidgetConfig.Events.evtIVMouseLeave"),
    evtIVTextIconMouseEnter = LuaEvent:NewIns("CommonWidgetConfig.Events.evtIVTextIconMouseEnter"),
    evtIVTextIconMouseLeave = LuaEvent:NewIns("CommonWidgetConfig.Events.evtIVTextIconMouseLeave"),
    evtIVShowHoverTips = LuaEvent:NewIns("CommonWidgetConfig.Events.evtIVShowHoverTips"),
    evtIVHideHoverTips = LuaEvent:NewIns("CommonWidgetConfig.Events.evtIVHideHoverTips"),
    evtCustomDragEnterOrLeave = LuaEvent:NewIns("CommonWidgetConfig.Events.evtCustomDragEnterOrLeave"),
    evtScrollBoxStartScroll = LuaEvent:NewIns("CommonWidgetConfig.Events.evtScrollBoxStartScroll"),

    --BEGIN MODIFICATION @ VIRTUOS :
    evtGlobalItemPopViewOpened= LuaEvent:NewIns("CommonWidgetConfig.Events.evtGlobalItemPopViewOpened"),
    evtGlobalItemBindLongPressedAction= LuaEvent:NewIns("CommonWidgetConfig.Events.evtGlobalItemBindLongPressedAction"),
    evtGlobalItemUnBindLongPressedAction= LuaEvent:NewIns("CommonWidgetConfig.Events.evtGlobalItemUnBindLongPressedAction"),
    evtGlobalHoverItemViewUseAnim= LuaEvent:NewIns("CommonWidgetConfig.Events.evtGlobalHoverItemViewUseAnim"),
    --END MODIFICATION
    --endregion

    -- 枪械详情页子弹鼠标进入和移除事件
    evtItemDetailWeaponBulletMouseEnter = LuaEvent:NewIns("CommonWidgetConfig.Events.evtItemDetailWeaponBulletMouseEnter"),
    evtItemDetailWeaponBulletMouseLeave = LuaEvent:NewIns("CommonWidgetConfig.Events.evtItemDetailWeaponBulletMouseLeave"),
    -- 枪械详情页子弹鼠标进入和移除Tip事件
    evtItemDetailWeaponBulletMouseEnterTip = LuaEvent:NewIns("CommonWidgetConfig.Events.evtItemDetailWeaponBulletMouseEnterTip"),
    evtItemDetailWeaponBulletMouseLeaveTip = LuaEvent:NewIns("CommonWidgetConfig.Events.evtItemDetailWeaponBulletMouseLeaveTip"),

    evtOnCommonSlotViewItemAdded  = LuaEvent:NewIns("CommonWidgetConfig.Events.evtOnCommonSlotViewItemAdded"), -- post CommonSlotView:AddItem
    -----------------------------------------------------------------------
}

CommonWidgetConfig.BG_IMAGE_NOT_SELECTED = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Bg_01.Common_ItemProp_Bg_01'"
CommonWidgetConfig.BG_IMAGE_SELECTED = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Bg_02.Common_ItemProp_Bg_02'"
CommonWidgetConfig.HEAD_ICON_NONE = "Texture2D'/Game/UI/UIAtlas/Texture/HeadAvatar/HeadAvatar_0000.HeadAvatar_0000'"

--headiconlist
CommonWidgetConfig.AddFriendBtnImage = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_PlayerState_Icon_0305.Common_PlayerState_Icon_0305'"
CommonWidgetConfig.InviteTeamBtnImage = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_PlayerState_Icon_0303.CommonHall_PlayerState_Icon_0303'"
CommonWidgetConfig.JoinTeamBtnImage = "PaperSprite'/Game/UI/UIAtlas/System/TeamSystem/BakedSprite/TeamSystem_PlayerState_Icon_0324.TeamSystem_PlayerState_Icon_0324'"
CommonWidgetConfig.ViewInformaImage = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_PlayerState_Icon_0327.Common_PlayerState_Icon_0327'"
CommonWidgetConfig.KickPlayerImage = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_PlayerState_Icon_0304.CommonHall_PlayerState_Icon_0304'"
CommonWidgetConfig.ChatImage = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_PlayerState_Icon_0307.Common_PlayerState_Icon_0307'"
CommonWidgetConfig.AddBlackImage = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_PlayerState_Icon_0308.Common_PlayerState_Icon_0308'"
CommonWidgetConfig.ReportImage = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_PlayerState_Icon_0309.Common_PlayerState_Icon_0309'"
CommonWidgetConfig.PromoTeamImage = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_SelfBuiltServer_04.Common_SelfBuiltServer_04'"
CommonWidgetConfig.DelFriendImage = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_PlayerState_Icon_0312.Common_PlayerState_Icon_0312'"
CommonWidgetConfig.ReportVoiceImage = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_De_VoiceReport.Common_De_VoiceReport'"
CommonWidgetConfig.TransferorImage = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_SelfBuiltServer_01.CommonHall_SelfBuiltServer_01'" --TODo需要更改
CommonWidgetConfig.ReservationTeamUpImg = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_PlayerState_Icon_0302.Common_PlayerState_Icon_0302'"
CommonWidgetConfig.TeamUpFinishImg = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_PlayerState_Icon_0303.Common_PlayerState_Icon_0303'"

--- BEGIN MODIFICATION @ VIRTUOS
-- XSX查看用户档案
CommonWidgetConfig.PlatformProfilePSImage = "PaperSprite'/Game/UI_HD/UIAtlas/Common_PS5/BakedSprite/Common_PS5_Icon.Common_PS5_Icon'"
CommonWidgetConfig.PlatformProfileXSXImage = "PaperSprite'/Game/UI_HD/UIAtlas/Common_XSX/BakedSprite/Common_XSX_Icon_0002.Common_XSX_Icon_0002'"
-- 好友点赞
CommonWidgetConfig.MatesPraiseImage = "PaperSprite'/Game/UI/UIAtlas/System/Chat/BakedSprite/Chat_Icon_0122.Chat_Icon_0122'"
--- END MODIFICATION
-- 好友点赞
CommonWidgetConfig.TributeImage = "PaperSprite'/Game/UI/UIAtlas/System/Settlement/BakedSprite/Settlement_Icon_0018.Settlement_Icon_0018'"

CommonWidgetConfig.DefaultWeaponBluePrintIcon = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Warehouse_Icon_0106.CommonHall_Warehouse_Icon_0106'"

CommonWidgetConfig.DragMedicineIconPath = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0025.Common_ItemProp_Icon_0025'"
CommonWidgetConfig.ArmorMedicineIconPath = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_icon_0502.Common_ItemProp_icon_0502'"

CommonWidgetConfig.TRIGGER_DRAG_SCALE_LARGE_ITEM = 1.25
CommonWidgetConfig.TRIGGER_DRAG_SCALE_SMALL_ITEM = 1.1
CommonWidgetConfig.TRIGGER_DRAG_SCALE_VECTOR_LARGE_ITEM = 
    FVector2D(CommonWidgetConfig.TRIGGER_DRAG_SCALE_LARGE_ITEM, CommonWidgetConfig.TRIGGER_DRAG_SCALE_LARGE_ITEM)
CommonWidgetConfig.TRIGGER_DRAG_SCALE_VECTOR_SMALL_ITEM = 
    FVector2D(CommonWidgetConfig.TRIGGER_DRAG_SCALE_SMALL_ITEM, CommonWidgetConfig.TRIGGER_DRAG_SCALE_SMALL_ITEM)
CommonWidgetConfig.DEFAULT_ITEM_SCALE = FVector2D(1, 1)

-- 菜单按钮列表内容项 类型枚举
CommonWidgetConfig.ESimpleBtnPanelItemType = {
    Text = 1,           -- 只有文本
    TextAndImage = 2,   -- 有文本和一个图片
}

--BEGIN MODIFICATION @ VIRTUOS : 手柄快捷准确转移物品 类型枚举
CommonWidgetConfig.EItemTransferType = {
    None = 0,                   -- 不支持 （玩家已装备的 手枪，头，甲，胸挂，背包）
    FastEquipOrReplace = 1,     -- 直接装备或替换 （手枪，头，甲，胸挂，背包）
    TransferItemPanel = 2,      -- 呼出转移物品菜单
}
--END MODIFICATION

CommonWidgetConfig.DEFAULT_CAPTURE_MAT_PATH = "Material'/Game/BluePrints/CaptureStuido/M_CaptureInvA.M_CaptureInvA'"
CommonWidgetConfig.DEFAULT_CAPTURE_ENV_PATH_GAME = "/Game/BluePrints/CaptureStuido/BP_CaptureEnv.BP_CaptureEnv_C"
CommonWidgetConfig.DEFAULT_CAPTURE_ENV_PATH_WAREHOUSE = "/Game/BluePrints/CaptureStuido/BP_CaptureEnv_Warehouse.BP_CaptureEnv_Warehouse_C"
--- 拖拽ItemView时ItemView和DragItemView的不透明度
CommonWidgetConfig.DRAG_ITEM_RENDER_OPACITY = 0.8
--- 鼠标悬停在ItemView上多少秒出现Tips
CommonWidgetConfig.MOUSE_ENTER_SHOW_TIPS_INTERVAL = 0   -- 改为在蓝图TipsAnchor中设置
--- ItemView上的锁图标资源路径
CommonWidgetConfig.ITEM_VIEW_LOCK_ICON_PATH = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_De_Lock.Common_De_Lock'"

CommonWidgetConfig.HIGHLIGHT_ITEM_INTERVAL = 0.15

CommonWidgetConfig.bUseIVCompPool = true

return CommonWidgetConfig