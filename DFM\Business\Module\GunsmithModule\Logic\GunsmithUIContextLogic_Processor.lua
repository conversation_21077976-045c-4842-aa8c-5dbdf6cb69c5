----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGunsmith)
----- LOG FUNCTION AUTO GENERATE END -----------


local GPModularWeaponDescLogic = require "DFM.Business.Module.GunsmithModule.Logic.GunsmithCPPLogic.GPModularWeaponDescLogic"
local EGunsmithUIState = require "DFM.Business.Module.GunsmithModule.Define.EGunsmithUIState"
local GunsmithUIParam = require "DFM.Business.DataStruct.GunsmithStruct.GunsmithUIParam"

local GunsmithUIContextLogic = require "DFM.Business.Module.GunsmithModule.Logic.GunsmithUIContextLogic_Linker"

function GunsmithUIContextLogic.ProcessContext()
    local processor = GunsmithUIContextLogic._GetActiveUIContextProcessor()
    processor:OverrideContext()
end

function GunsmithUIContextLogic.OverrrideFrontend()
    local processor = GunsmithUIContextLogic._GetActiveUIContextProcessor()
    processor:OverrrideFrontend()
end

function GunsmithUIContextLogic.ProcessOverrrideFrontend()
    GunsmithUIContextLogic.SetUIStateDefault()
    GunsmithUIContextLogic.OverrrideFrontend()
end

function GunsmithUIContextLogic.ProcessContextFromDefaultProcessor()
    GunsmithUIContextLogic.SetUIStateDefault()
    GunsmithUIContextLogic.ProcessContext()
end

function GunsmithUIContextLogic.SyncPart2ServerFromFrontendWithoutLockParts()
    local bIsRange = GunsmithUIContextLogic.GetGroupIDIsRange()
    if bIsRange then
        GunsmithUIContextLogic.ProcessRangeSyncContext4PartFromFrontend()
    else
        GunsmithUIContextLogic.ProcessSyncContext4PartFromFrontend()
    end
end

function GunsmithUIContextLogic.ProcessSyncContext4PartFromFrontend()
    local processor = GunsmithUIContextLogic._GetContextProcessor(EGunsmithUIState.Default)
    processor:SyncPart2ServerFromFrontend()
end

function GunsmithUIContextLogic.ProcessRangeSyncContext4PartFromFrontend()
    local processor = GunsmithUIContextLogic._GetContextProcessor(EGunsmithUIState.Range)
    processor:SyncPart2ServerFromFrontend()
end

function GunsmithUIContextLogic.ProcessSimulateSyncContext4PartFromFrontend()
    local bSimulate = GunsmithUIContextLogic.GetIsSimulateState()
    if not bSimulate then
        logerror("GunsmithUIContextLogic.ProcessSimulateSyncContext4PartFromFrontend: not simulate state")
        return
    end

    local processor = GunsmithUIContextLogic._GetActiveUIContextProcessor()
    processor:SyncPart2ServerFromFrontend()
end

function GunsmithUIContextLogic.SyncPart2ServerFromFrontend()
    local bIsRange = GunsmithUIContextLogic.GetGroupIDIsRange()
    if bIsRange then
        GunsmithUIContextLogic.ProcessRangeSyncContext4PartFromFrontend()
        return
    end
    local processor = GunsmithUIContextLogic._GetActiveUIContextProcessor()
    processor:SyncPart2ServerFromFrontend()
end

function GunsmithUIContextLogic.RangeSetSkinID(skinID, skinGUID, bApplyAll)
    local processor = GunsmithUIContextLogic._GetContextProcessor(EGunsmithUIState.Range)
    processor:RangeSetSkinID(skinID, skinGUID, bApplyAll)
end

function GunsmithUIContextLogic.RangeSetPendantID(pendantID, pendantGUID, bApplyAll)
    local processor = GunsmithUIContextLogic._GetContextProcessor(EGunsmithUIState.Range)
    processor:RangeSetPendantID(pendantID, pendantGUID, bApplyAll)
end

function GunsmithUIContextLogic.AddAssembleWeapon(propInfo)
    local processor = GunsmithUIContextLogic._GetContextProcessor(EGunsmithUIState.Range)
    return processor:InteralRefreshOrAddAssembleWeapon(propInfo)
end

function GunsmithUIContextLogic.InternalSetDefaultSkinInfo(propInfo)
    local processor = GunsmithUIContextLogic._GetContextProcessor(EGunsmithUIState.Range)
    return processor:SetDefaultSkinInfo(propInfo)
end

function GunsmithUIContextLogic.ProcessSyncContext4PartFinetuneFromFrontend()
    local socketGUID = GunsmithUIContextLogic.GetFocusSocketGUID()

    local envProcessor = GunsmithUIContextLogic._GetActiveUIContextProcessor()
    envProcessor:PreProcessSyncTuneNodeValue(socketGUID)

    local weaponDescription = GunsmithUIContextLogic.GetWeaponDescription4Frontend()
    local bSimualte = GPModularWeaponDescLogic.IsSimulateStateBySocketGUID(weaponDescription, socketGUID)
    local key = bSimualte and EGunsmithUIState.Simulate or EGunsmithUIState.Default
    local bIsRange = GunsmithUIContextLogic.GetGroupIDIsRange()
    if bIsRange then
        key = EGunsmithUIState.Range
    end
    local opreationProcessor = GunsmithUIContextLogic._GetContextProcessor(key)
    opreationProcessor:SyncTuneNodeValue(socketGUID)
end

function GunsmithUIContextLogic.AddPartNode(itemID, itemGUID, socketGUID)
    local processor = GunsmithUIContextLogic._GetActiveUIContextProcessor()
    processor:ProcessAddPartNode(itemID, itemGUID, socketGUID)
end

function GunsmithUIContextLogic.RemovePartNode(socketGUID)
    local processor = GunsmithUIContextLogic._GetActiveUIContextProcessor()
    processor:ProcessRemovePartNode(socketGUID)
end

function GunsmithUIContextLogic.ProcessSetSkinID(skinID)
    local processor = GunsmithUIContextLogic._GetActiveUIContextProcessor()
    processor:ProcessSetSkinID(skinID)
end

---@param skinInfoParam FPbWeaponSkinInfoParam
function GunsmithUIContextLogic.ProcessSetSkinInfoParam(skinInfoParam)
    local processor = GunsmithUIContextLogic._GetActiveUIContextProcessor()
    processor:ProcessSetSkinInfoParam(skinInfoParam)
end

function GunsmithUIContextLogic.ProcessSetPendantInfo(pendantInfo)
    local processor = GunsmithUIContextLogic._GetActiveUIContextProcessor()
    processor:ProcessSetPendantInfo(pendantInfo)
end

function GunsmithUIContextLogic.ProcessSetSkinLockAllAppearance(bLockAllAppearance)
    local processor = GunsmithUIContextLogic._GetActiveUIContextProcessor()
    processor:ProcessSetSkinLockAllAppearance(bLockAllAppearance)
end

function GunsmithUIContextLogic.SetFrontendFromWeaponDescription(inWeaponDescription)
    local processor = GunsmithUIContextLogic._GetActiveUIContextProcessor()
    processor:ProcessSetDescription(inWeaponDescription)
end

function GunsmithUIContextLogic.ProcessSyncAddPartNode(itemID, itemGUID, socketGUID, bUnlock)
    GunsmithUIContextLogic.PlayUIAudio(true, true, itemID, socketGUID)
    local processor = GunsmithUIContextLogic.GetSyncPartNodeProcessor(bUnlock, socketGUID)
    processor:SyncAddPartNode(itemID, itemGUID, socketGUID, bUnlock)
end

function GunsmithUIContextLogic.ProcessSyncRemovePartNode(socketGUID, bUnlock)
    local weaponDescription4Frontend = GunsmithUIContextLogic.GetWeaponDescription4Frontend()
    local itemID = GunsmithUIContextLogic.GetWeaponDescriptionSocketItemIDs(weaponDescription4Frontend, socketGUID)
    GunsmithUIContextLogic.PlayUIAudio(false, true, itemID, socketGUID)
    GunsmithUIContextLogic.SetFastEquipResult(nil)

    local processor = GunsmithUIContextLogic.GetSyncPartNodeProcessor(bUnlock, socketGUID)
    processor:SyncRemovePartNode(socketGUID, bUnlock)
end

function GunsmithUIContextLogic.GetSyncPartNodeProcessor(bUnlock, socketGUID)
    local bSimulate = GunsmithUIContextLogic.GetIsSimulateState()

    local weaponDescription4Backup = GunsmithUIContextLogic.GetWeaponDescription4Backup()
    local bIsSocketSimulate = GPModularWeaponDescLogic.IsSimulateStateBySocketGUID(weaponDescription4Backup, socketGUID)

    local key = EGunsmithUIState.Simulate
    local bIsDefault = bUnlock and not bSimulate and not bIsSocketSimulate
    if bIsDefault then
        key = EGunsmithUIState.Default
    end

    -- 靶场改枪台
    local bIsRange = GunsmithUIContextLogic.GetGroupIDIsRange()
    if bIsRange then
        key = EGunsmithUIState.Range
    end

    local processor = GunsmithUIContextLogic._GetContextProcessor(key)
    return processor
end

function GunsmithUIContextLogic.OverrideContextBackupFromDescription(inWeaponDescription, bOverrideSkinInfo, bOverridePendantInfo)
    bOverrideSkinInfo = setdefault(bOverrideSkinInfo, true)
    local processor = GunsmithUIContextLogic._GetActiveUIContextProcessor()
    processor:OverrideContextBackupFromDescription(inWeaponDescription, bOverrideSkinInfo, bOverridePendantInfo)
end

function GunsmithUIContextLogic.OverrideContextFromServerSkinUpdated()
    local processor = GunsmithUIContextLogic._GetActiveUIContextProcessor()
    processor:OverrideContextFromServerSkinUpdated()
end

function GunsmithUIContextLogic.SetTuneNodeValue(socketGUID, inTuneID, inValue)
    local processor = GunsmithUIContextLogic._GetActiveUIContextProcessor()
    processor:SetTuneNodeValue(socketGUID, inTuneID, inValue)
end

function GunsmithUIContextLogic.RemovePartNodeFromweaponDescription(socketGUID, weaponDescription)
    local processor = GunsmithUIContextLogic._GetActiveUIContextProcessor()
    processor:ProcessRemovePartNodeFromweaponDescription(socketGUID, weaponDescription)
end

function GunsmithUIContextLogic.ProcessLinkWeaponDescriptionNodeGUID(weaponDescription, bContainsFromInventory, bOverrideSkinInfo)
    local processor = GunsmithUIContextLogic._GetActiveUIContextProcessor()
    return processor:ProcessLinkWeaponDescriptionNodeGUID(weaponDescription, bContainsFromInventory, bOverrideSkinInfo)
end



--- GunsmithMainPreviewUI 快捷键逻辑 Start--- 
function GunsmithUIContextLogic.GetShortcutProcessor()
    return Module.Gunsmith.Field:GetShortcutProcessor()
end

function GunsmithUIContextLogic.SetShortcutFocusSocket(socket)
    local shortcutProcessor = GunsmithUIContextLogic.GetShortcutProcessor()
    shortcutProcessor:SetShortcutFocusSocket(socket)
end

function GunsmithUIContextLogic.GetShortcutFocusSocket()
    local shortcutProcessor = GunsmithUIContextLogic.GetShortcutProcessor()
    return shortcutProcessor:GetShortcutFocusSocket()
end

function GunsmithUIContextLogic.ProcessShortcutRemoveAllPart()
    local shortcutProcessor = GunsmithUIContextLogic.GetShortcutProcessor()
    shortcutProcessor:OnEventRemoveAllPart()
end

function GunsmithUIContextLogic.ProcessShortcutRemovePart()
    local shortcutProcessor = GunsmithUIContextLogic.GetShortcutProcessor()
    shortcutProcessor:OnEventRemovePart()
end

function GunsmithUIContextLogic.ProcessRemoveAllPart()
    GunsmithUIContextLogic.PlayUIAudio(false)
    local processor = GunsmithUIContextLogic._GetActiveUIContextProcessor()
    processor:ProcessRemoveAllPart()
end
--- GunsmithMainPreviewUI 快捷键逻辑 End--- 

function GunsmithUIContextLogic.CSWAssemblyDepositPropUpdateReqByContextProcessor(newPropInfo, fCallBack, local_prop)
    local processor = GunsmithUIContextLogic._GetActiveUIContextProcessor()
    processor:CSWAssemblyDepositPropUpdateReq(newPropInfo, fCallBack, local_prop)
end

-- 改枪协议入口
function GunsmithUIContextLogic.CSWAssemblyDepositPropUpdateReq(newPropInfo, fCallBack, local_prop, pass_through)
    local processor = GunsmithUIContextLogic.GetServerProcessor()
    processor:CSWAssemblyDepositPropUpdateReq(newPropInfo, fCallBack, local_prop, pass_through)
end

function GunsmithUIContextLogic.SetWeaponItemGUIDFormUserByCSCheapBuyRes(dataChange)
    local processor = GunsmithUIContextLogic.GetServerProcessor()
    processor:SetWeaponItemGUIDFormUserByCSCheapBuyRes(dataChange)
end

function GunsmithUIContextLogic.GetServerProcessor()
    return Module.Gunsmith.Field:GetServerProcessor()
end

function GunsmithUIContextLogic.OnProcessRangetoGunsmith()
    local propinfoFromRange = GunsmithUIContextLogic.GetPropinfoFromRange()

    local bIsValid = isvalid(propinfoFromRange)
    if not bIsValid then
        return
    end

    local groupID = GunsmithUIContextLogic.GetGroupIDFromRange()
    local bIsEqual, propinfo = GunsmithUIContextLogic.IsEqualServerPropinfo(propinfoFromRange, groupID)

    -- 如果仓库数据和需要展示一致,直接返回
    if bIsEqual then
        return
    end

    local uiParam = GunsmithUIParam.SetFromPropInfoForRange(propinfo, groupID)
    GunsmithUIContextLogic.OnForceOverrideContext(uiParam, propinfoFromRange)
end

function GunsmithUIContextLogic.OnForceOverrideContext(uiParam, inPropinfo)
    GunsmithUIContextLogic.ProcessContextFromUIParam(uiParam, true)

    GunsmithUIContextLogic.OverrideContextWithSimulate(inPropinfo)
end

function GunsmithUIContextLogic.OverrideContextWithSimulate(inPropinfo)
    GunsmithUIContextLogic.SetUIState(EGunsmithUIState.Simulate)
    local processor = GunsmithUIContextLogic._GetActiveUIContextProcessor()
    processor:OverrideContextFromServerPartUpdated(inPropinfo)
end

function GunsmithUIContextLogic.ProcessChangeHero()
    local bIsMP = GunsmithUIContextLogic.GetGroupIDIsMP()
    local bUnSycnRangeSolution= GunsmithUIContextLogic.GetUnSycnRangeSolution()
    local bCanChangeHero = bIsMP and bUnSycnRangeSolution
    if not bCanChangeHero then
        return
    end
    local weaponItemID = GunsmithUIContextLogic.GetItemID()
    local heroId = Module.ArmedForce:TryGetHeroId4GunMP(weaponItemID)
    Module.ArmedForce:TryUseHeroIdAfterGunAssemblyMP(heroId)
    -- GunsmithUIContextLogic.SetUnSycnRangeSolution(false)
end

function GunsmithUIContextLogic.OnBackupUpdated(inWeaponDescription)
    local processor = GunsmithUIContextLogic._GetActiveUIContextProcessor()
    processor:OnBackupUpdated(inWeaponDescription)
end

------------------------------------------------------------------------
-----------------------------   private  -------------------------------
------------------------------------------------------------------------

function GunsmithUIContextLogic._GetActiveUIContextProcessor()
    local uistate = GunsmithUIContextLogic.GetUIState()
    local processor = GunsmithUIContextLogic._GetContextProcessor(uistate)
    return processor
end

function GunsmithUIContextLogic._GetContextProcessor(uiState)
    local processor = Module.Gunsmith.Field:GetUIContextProcessor(uiState)
    return processor
end

-- return GunsmithUIContextLogic