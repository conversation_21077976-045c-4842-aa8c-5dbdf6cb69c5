----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSettlement)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class MPTopTournamentSettlement : LuaUIBaseView
local MPTopTournamentSettlement = ui("MPTopTournamentSettlement")
local commanderTierTable = Facade.TableManager:GetTable("CommanderTier")
local commanderParameterTable = Facade.TableManager:GetTable("CommanderParameter")
local commanderParameterWordTable = Facade.TableManager:GetTable("CommanderWordParameter")
local CommonSkipOverBg = require "DFM.Business.Module.CommonWidgetModule.UI.FullScreen.CommonSkipOverBg"
local TextStyleBlueprintLib = import "TextStyleBlueprintLib"

local Result2Text = {
    [TDMResult.Match_Loss] = NSLOCTEXT("SettlementModule", "TDMResultMatchLoss", "失败"),
    [TDMResult.Match_Win] = NSLOCTEXT("SettlementModule", "TDMResultMatchWin", "胜利"),
    [TDMResult.Match_Tie] = NSLOCTEXT("SettlementModule", "TDMResultMatchTie", "平局")
}

---@class MPTopTournamentItemData
---@field public type MPTopTournamentScoreType
---@field public score number

function MPTopTournamentSettlement:Ctor()
    self._wtscroll = self:Wnd("DFScrollBox_3", UIWidgetBase)

    self._wtResultTB = self:Wnd("DFTextBlock_77", UITextBlock)
    self._wtMapTB = self:Wnd("DFTextBlock", UITextBlock)

    self._wtRankIconWB = self:Wnd("wtRankIconWB", UIWidgetBase)
    self._wtRankNameTB = self:Wnd("wtRankNameTB", UITextBlock)
    self._wtRankStarWB = self:Wnd("wtRankStarWB", UIWidgetBase)

    self._wtCurScoreDeltaTB = self:Wnd("wtCurScoreDeltaTB", UITextBlock)
    self._wtCurStarScorePBTB = self:Wnd("wtCurStarScorePBTB", UITextBlock)
    self._wtCurStarScorePBImg = self:Wnd("wtCurStarScorePBImg", UIImage)
    self._wtCurStarScorePBLine = self:Wnd("wtCurStarScorePBLine", UIImage)

    self._wtRankProtectTB = self:Wnd("wtRankProtectTB", UITextBlock)
    self._wtRankProtectTB:Collapsed()

    self._wtNextStepCSOB = self:Wnd("wtNextStepWB", CommonSkipOverBg)
    self._wtNextStepCSOB:BindClickEvent(self._JumpToNextStep, self)
    self._wtNextStepCSOB._needKeepBtn = true
    if IsHD() then
        self._jumpHandle =
            self:AddInputActionBinding(
            "JumpOver",
            EInputEvent.IE_Pressed,
            self._JumpToNextStep,
            self,
            EDisplayInputActionPriority.UI_Pop
        )
        -- 绑定游戏流程事件
        Module.Settlement.Config.Events.evtReadyToShowSettlement:AddListener(self._OnReadyToShowSettlement, self)
        Module.Settlement.Config.Events.evtStartToShowSettlement:AddListener(self._OnStartToShowSettlement, self)
        self._isReadyToShow = false
    end
    self._scoreList = {}

    self._length = IsHD() and 1180 or 898
end

------------------------------------ Override function ------------------------------------

function MPTopTournamentSettlement:OnInitExtraData()
end

-- UI打开时触发
function MPTopTournamentSettlement:OnOpen()
    -- print( "xhz >>>>>>>>>>>>> MPTopTournamentSettlement OnOpen >>>>>>>>>>>>>>>> " )
    self:_AddEventListener()
    self:_InitCommanderTierTable()

    self._scoreList = {}

    local tdm = {
        commander_score = {
            attack_defeat_score = 0,
            attack_vic_score = 20,
            defence_vic_score = 0,
            defence_defeat_score = 0,
            attack_stage_score = 5,
            defence_stage_score = 6,
            camp_compestion_score = 7,
            camp_match_score = 8,
            join_at_middle_score = 9,
            short_hangup_trigger_score = 10,
            contributor_title_score = 11,
            passive_combat_score = -12,
            leave_score = -13,
            total_score = 500,
            weak_camp_score = 15,
            conquest_vic_score = 10,
            conquest_defeat_score = 11,
            conquest_progress_score = 12
        },
        orgin_commander_score = 25
    }
    --self:_SetTopScoreShow(tdm)
    --self:_SetTDMData(tdm)
    --self:_DelayCreateScoreAnimTimer()

    local mpSettlementInfo = Server.SettlementServer:GetMPSettlementInfo()
    if not mpSettlementInfo then
        return
    end
    self:_SetTopScoreShow(mpSettlementInfo.tdm_data)
    self:_SetMapTBByMapID(mpSettlementInfo.match_info)
    self:_SetTDMData(mpSettlementInfo.tdm_data)

    self:_DelayCreateScoreAnimTimer()
end

function MPTopTournamentSettlement:_SetTopScoreShow(tdmData)
    if not tdmData or not tdmData.commander_score then
        return
    end

    self._scoreList[Module.Settlement.Config.MPTopTournamentScoreKind.OverScore] = {}
    self._scoreList[Module.Settlement.Config.MPTopTournamentScoreKind.ExbackScore] = {}
    self._scoreList[Module.Settlement.Config.MPTopTournamentScoreKind.SpecialScore] = {}

    local commander_score = tdmData.commander_score
    local orginScore = tdmData.orgin_commander_score or 0
    local finalScore = math.max(0, tdmData.commander_score.total_score + tdmData.orgin_commander_score)
    local deltaScore = tdmData.commander_score.total_score

    --进攻方失败分
    if math.abs(commander_score.attack_defeat_score) > 0 then
        table.insert(
            self._scoreList[Module.Settlement.Config.MPTopTournamentScoreKind.OverScore],
            {
                type = Module.Settlement.Config.MPTopTournamentScoreType.AttackerFailedBaseScore,
                score = commander_score.attack_defeat_score
            }
        )
    end
    --进攻方胜利分
    if math.abs(commander_score.attack_vic_score) > 0 then
        table.insert(
            self._scoreList[Module.Settlement.Config.MPTopTournamentScoreKind.OverScore],
            {
                type = Module.Settlement.Config.MPTopTournamentScoreType.AttackerSuccessScore,
                score = commander_score.attack_vic_score
            }
        )
    end
    --防守方胜利基础分
    if math.abs(commander_score.defence_vic_score) > 0 then
        table.insert(
            self._scoreList[Module.Settlement.Config.MPTopTournamentScoreKind.OverScore],
            {
                type = Module.Settlement.Config.MPTopTournamentScoreType.DefenderSuccessBaseScore,
                score = commander_score.defence_vic_score
            }
        )
    end
    --防守方失败分
    if math.abs(commander_score.defence_defeat_score) > 0 then
        table.insert(
            self._scoreList[Module.Settlement.Config.MPTopTournamentScoreKind.OverScore],
            {
                type = Module.Settlement.Config.MPTopTournamentScoreType.DefenderFailedScore,
                score = commander_score.defence_defeat_score
            }
        )
    end
    --进攻阶段分
    if math.abs(commander_score.attack_stage_score) > 0 then
        table.insert(
            self._scoreList[Module.Settlement.Config.MPTopTournamentScoreKind.OverScore],
            {
                type = Module.Settlement.Config.MPTopTournamentScoreType.AttackerFlowScore,
                score = commander_score.attack_stage_score
            }
        )
    end
    --防守阶段分
    if math.abs(commander_score.defence_stage_score) > 0 then
        table.insert(
            self._scoreList[Module.Settlement.Config.MPTopTournamentScoreKind.OverScore],
            {
                type = Module.Settlement.Config.MPTopTournamentScoreType.DefenderFlowScore,
                score = commander_score.defence_stage_score
            }
        )
    end
    --占领模式，胜利分
    if math.abs(commander_score.conquest_vic_score) > 0 then
        table.insert(
            self._scoreList[Module.Settlement.Config.MPTopTournamentScoreKind.OverScore],
            {
                type = Module.Settlement.Config.MPTopTournamentScoreType.ConquestVicScore,
                score = commander_score.conquest_vic_score
            }
        )
    end
    --占领模式，失败分
    if math.abs(commander_score.conquest_defeat_score) > 0 then
        table.insert(
            self._scoreList[Module.Settlement.Config.MPTopTournamentScoreKind.OverScore],
            {
                type = Module.Settlement.Config.MPTopTournamentScoreType.ConquestDefeatScore,
                score = commander_score.conquest_defeat_score
            }
        )
    end
    --占领模式，占领进度得分
    if math.abs(commander_score.conquest_progress_score) > 0 then
        table.insert(
            self._scoreList[Module.Settlement.Config.MPTopTournamentScoreKind.OverScore],
            {
                type = Module.Settlement.Config.MPTopTournamentScoreType.ConquestProgressScore,
                score = commander_score.conquest_progress_score
            }
        )
    end

    --弱势阵营补偿
    if math.abs(commander_score.weak_camp_score) > 0 then
        table.insert(
            self._scoreList[Module.Settlement.Config.MPTopTournamentScoreKind.ExbackScore],
            {
                type = Module.Settlement.Config.MPTopTournamentScoreType.AwakerCampBackScore,
                score = commander_score.weak_camp_score
            }
        )
    end
    --中补补偿分
    if math.abs(commander_score.join_at_middle_score) > 0 then
        table.insert(
            self._scoreList[Module.Settlement.Config.MPTopTournamentScoreKind.ExbackScore],
            {
                type = Module.Settlement.Config.MPTopTournamentScoreType.MiddleJoinBackScore,
                score = commander_score.join_at_middle_score
            }
        )
    end
    --人数补偿
    if math.floor(math.abs(commander_score.camp_compestion_score)) > 0 then
        table.insert(
            self._scoreList[Module.Settlement.Config.MPTopTournamentScoreKind.ExbackScore],
            {
                type = Module.Settlement.Config.MPTopTournamentScoreType.PlayerNumBackScore,
                score = math.floor(commander_score.camp_compestion_score)
            }
        )
    end
    --匹配补偿分
    if math.floor(math.abs(commander_score.camp_match_score)) > 0 then
        table.insert(
            self._scoreList[Module.Settlement.Config.MPTopTournamentScoreKind.ExbackScore],
            {
                type = Module.Settlement.Config.MPTopTournamentScoreType.MatchBackScore,
                score = math.floor(commander_score.camp_match_score)
            }
        )
    end

    --挂机处罚
    if math.abs(commander_score.short_hangup_trigger_score) > 0 then
        table.insert(
            self._scoreList[Module.Settlement.Config.MPTopTournamentScoreKind.SpecialScore],
            {
                type = Module.Settlement.Config.MPTopTournamentScoreType.ShortHandupTriggerScore,
                score = commander_score.short_hangup_trigger_score
            }
        )
    end
    --特殊贡献分
    if math.abs(commander_score.contributor_title_score) > 0 then
        table.insert(
            self._scoreList[Module.Settlement.Config.MPTopTournamentScoreKind.SpecialScore],
            {
                type = Module.Settlement.Config.MPTopTournamentScoreType.SpecialContributorScore,
                score = commander_score.contributor_title_score
            }
        )
    end
    if math.abs(commander_score.leave_score) > 0 then
        --中退惩罚
        table.insert(
            self._scoreList[Module.Settlement.Config.MPTopTournamentScoreKind.SpecialScore],
            {
                type = Module.Settlement.Config.MPTopTournamentScoreType.MiddleLeave,
                score = commander_score.leave_score
            }
        )
    end
    if math.abs(commander_score.passive_combat_score) > 0 then
        --消极作战
        table.insert(
            self._scoreList[Module.Settlement.Config.MPTopTournamentScoreKind.SpecialScore],
            {
                type = Module.Settlement.Config.MPTopTournamentScoreType.PassiveCombatRateScore,
                score = commander_score.passive_combat_score
            }
        )
    end

    for key, value in pairs(self._scoreList) do
        Facade.UIManager:AddSubUI(self, UIName2ID.MPTopTournamentSettlementTiTle, self._wtscroll, nil, key, value)
    end
end

function MPTopTournamentSettlement:_InitCommanderTierTable()
    if not commanderTierTable or not commanderParameterTable then
        return
    end

    local curSerial = Server.TournamentServer:GetCurSerial()

    if not curSerial then
        return
    end

    self._commanderTable = {}

    for _, value in pairs(commanderTierTable) do
        if table.contains(value.SeasonID or {}, curSerial) then
            table.insert(
                self._commanderTable,
                {
                    name = value.Name,
                    tierTypeID = value.TierTypeID,
                    minPoint = value.MinPoint,
                    starsDivided = value.StarsDivided,
                    badgeMinIcon = value.BadgeMinIcon
                }
            )
        end
    end

    table.sort(
        self._commanderTable,
        function(a, b)
            return a.minPoint < b.minPoint
        end
    )

    for key, value in ipairs(self._commanderTable) do
        if value.starsDivided == -1 then
            value.maxPoint = math.maxinteger
            value.singleStarScore =
                commanderParameterTable["StartValue"] and tonumber(commanderParameterTable["StartValue"]) or 50
        else
            if self._commanderTable[key + 1] then
                value.maxPoint = self._commanderTable[key + 1].minPoint
                value.singleStarScore = (value.maxPoint - value.minPoint) / value.starsDivided
            end
        end
    end
end

function MPTopTournamentSettlement:_SetMapTBByMapID(matchInfo)
    if not matchInfo then
        return
    end
    local mapID = matchInfo.map_id
    local mapConfigTable = Facade.TableManager:GetTable("MapConfig")
    for _, value in pairs(mapConfigTable) do
        if mapID == value.MapID then
            self._wtMapTB:SetText(value.DisplayName)
            return
        end
    end

    self._wtMapTB:SetText("--")
end

function MPTopTournamentSettlement:_FindMySelfInfo(campList)
    if not campList then
        return
    end

    local playerId = Server.AccountServer:GetPlayerId()

    if not playerId then
        return
    end

    for _, camp in pairs(campList) do
        for _, team in pairs(camp.team_list) do
            for _, player in pairs(team.player_list) do
                if playerId == player.player_id then
                    self._isWinner = camp.is_winner
                    self._isAttacker = camp.attacker
                    if player.leave then
                        self._leave = true
                        self._wtResultTB:SetText(Module.Settlement.Config.Loc.QuitGame)
                    else
                        self._wtResultTB:SetText(camp.is_winner and Result2Text[camp.is_winner] or "--")
                    end
                    return
                end
            end
        end
    end
end

function MPTopTournamentSettlement:_SetTDMData(tdmData)
    if not tdmData then
        return
    end

    self:_FindMySelfInfo(tdmData.camp_list)

    self._curStartScore = tdmData.orgin_commander_score or 0
    self._finalScoreDelta = tdmData.commander_score.total_score or 0
    self._finalScore = self._curStartScore + self._finalScoreDelta
end

function MPTopTournamentSettlement:_DelayCreateScoreAnimTimer()
    self._curScoreDelta = 0
    self._curRank = nil
    self:_RefreshRankScoreView()

    self._delayCreateRankScoreAnimTimerHandle = Timer:NewIns(3, 0)
    self._delayCreateRankScoreAnimTimerHandle:AddListener(self._CreateRankScoreAnimTimer, self)
    self._delayCreateRankScoreAnimTimerHandle:Start()
end

function MPTopTournamentSettlement:_CreateRankScoreAnimTimer()
    if not self._delayCreateRankScoreAnimTimerHandle then
        return
    end

    self._delayCreateRankScoreAnimTimerHandle:Release()
    self._delayCreateRankScoreAnimTimerHandle = nil

    if not self._commanderTable then
        return
    end

    if self._finalScoreDelta == 0 then
        self:_RefreshFinalRankScoreView()
        return
    end

    self._singleRankScoreDelta =
        self._finalScoreDelta < 0 and -1 * math.ceil(math.abs(self._finalScoreDelta) / 120) or
        math.ceil(self._finalScoreDelta / 120)

    self._rankScoreAnimTimerHandle = Timer:NewIns(0.025, 0)
    self._rankScoreAnimTimerHandle:AddListener(self._RefreshRankScoreViewByTimer, self)
    self._rankScoreAnimTimerHandle:Start()

    Facade.SoundManager:PlayUIAudioEvent(
        self._finalScoreDelta < 0 and DFMAudioRes.UISettlementBarDownLoop or DFMAudioRes.UISettlementBarLoop
    )
end

function MPTopTournamentSettlement:_RefreshRankScoreViewByTimer()
    self._curScoreDelta = self._curScoreDelta + self._singleRankScoreDelta
    self._curStartScore = self._curStartScore + self._singleRankScoreDelta

    if self._finalScoreDelta < 0 then
        if self._curScoreDelta <= self._finalScoreDelta then
            self:_ReleaseRankScoreAnimTimer()
            return
        end
    else
        if self._finalScoreDelta <= self._curScoreDelta then
            self:_ReleaseRankScoreAnimTimer()
            return
        end
    end

    self:_RefreshRankScoreView()
end

function MPTopTournamentSettlement:_RefreshFinalRankScoreView()
    self._curScoreDelta = self._finalScoreDelta
    self._curStartScore = self._finalScore

    self:_RefreshRankScoreView()
    self:_StopAllUIAudio()
end

function MPTopTournamentSettlement:_RefreshRankScoreView()
    if
        not self._curRank or self._curStartScore < self._commanderTable[self._curRank].minPoint or
            self._commanderTable[self._curRank].maxPoint <= self._curStartScore
     then
        self:_CalculateRankByScore()
        self._wtRankIconWB:SetCommanderIconByScore(self._curStartScore)
        if self._curRank and self._commanderTable[self._curRank] then
            self._wtRankNameTB:SetText(self._commanderTable[self._curRank].name)
            self._wtRankStarWB:SetRankStarNum(self._commanderTable[self._curRank].starsDivided, true)
        end

        if self._preRank then
            self._wtRankStarWB:SetRankStarState(0)
            self:_CalculateStarScore()
            self:_CalculateRankChengeType()
            self._preRank = self._curRank
            return
        end
        self._preRank = self._curRank
    end
    if self._curRank and self._commanderTable[self._curRank] then
        self:_CalculateStarScore()
    end
end

function MPTopTournamentSettlement:_CalculateStarScore()
    local curRankStarActiveNum = self:_CalculateRankStarInfo()

    if self._curRankStarActiveNum ~= curRankStarActiveNum then
        if self._curRankStarActiveNum then
            Facade.SoundManager:PlayUIAudioEvent(
                self._finalScoreDelta < 0 and DFMAudioRes.UISeasonDown or DFMAudioRes.UISeasonUp
            )
        end

        self._curRankStarActiveNum = curRankStarActiveNum
        self._wtRankStarWB:SetRankStarState(self._curRankStarActiveNum)
    end

    self._wtCurScoreDeltaTB:SetText(
        self._curScoreDelta < 1 and self._curScoreDelta or
            string.format(Module.Settlement.Config.Loc.plusSignText, self._curScoreDelta)
    )
    self._wtCurStarScorePBTB:SetText(
        string.format(
            Module.Settlement.Config.Loc.CurLevelExpSchedule,
            self._curStarScore,
            self._commanderTable[self._curRank].singleStarScore
        )
    )
    local curStarScorePB = self._curStarScore / self._commanderTable[self._curRank].singleStarScore
    self._wtCurStarScorePBImg:SetPercent(curStarScorePB)
    self._wtCurStarScorePBLine:SetPosition(FVector2D(self._length * curStarScorePB, 0))
end

function MPTopTournamentSettlement:_CalculateRankChengeType()
    if
        self._commanderTable[self._preRank] and
            self._commanderTable[self._preRank].tierTypeID == self._commanderTable[self._curRank].tierTypeID
     then
        if self._finalScoreDelta < 0 then
            self:_SmallRankDown()
        else
            self:_SmallRankUp()
        end
    else
        if self._finalScoreDelta < 0 then
            self:_BigRankDown()
        else
            self:_BigRankUp()
        end
    end
end

function MPTopTournamentSettlement:_BigRankUp()
    Facade.UIManager:AsyncShowUI(
        UIName2ID.MPCammanderBigRankUp,
        nil,
        self,
        self._commanderTable[self._curRank].tierTypeID
    )

    self:_ReleaseRankScoreAnimTimer()
end

function MPTopTournamentSettlement:_SmallRankUp()
    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UISeasonSmallUp)
end

function MPTopTournamentSettlement:_BigRankDown()
    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UISeasonSmallDown)
end

function MPTopTournamentSettlement:_SmallRankDown()
    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UISeasonSmallDown)
end

function MPTopTournamentSettlement:_CalculateRankStarInfo()
    local curRankScore = self._curStartScore - self._commanderTable[self._curRank].minPoint
    local curRankStarActiveNum = math.ceil((curRankScore + 1) / self._commanderTable[self._curRank].singleStarScore)
    self._curStarScore =
        curRankScore - self._commanderTable[self._curRank].singleStarScore * (curRankStarActiveNum - 1)
    return curRankStarActiveNum
end

function MPTopTournamentSettlement:_CalculateRankByScore()
    for key, value in ipairs(self._commanderTable) do
        if value.minPoint <= self._curStartScore and self._curStartScore < value.maxPoint then
            self._curRank = key
            loginfo("MPTopTournamentSettlement:_CalculateRankByScore curRank = ", self._curRank)
            return
        end
    end
    if self._commanderTable[1] then
        self._finalScore = self._commanderTable[1].minPoint
    end
    self:_ReleaseRankScoreAnimTimer()
end

function MPTopTournamentSettlement:_ReleaseRankScoreAnimTimer()
    if not self._rankScoreAnimTimerHandle then
        return
    end

    self._rankScoreAnimTimerHandle:Release()
    self._rankScoreAnimTimerHandle = nil

    self:_RefreshFinalRankScoreView()
end

function MPTopTournamentSettlement:_StopAllUIAudio()
    Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.UISettlementBarLoop)
    Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.UISettlementBarDownLoop)
end

-- UI打开时触发
function MPTopTournamentSettlement:OnClose()
    -- print( "xhz >>>>>>>>>>>>> MPTopTournamentSettlement OnClose >>>>>>>>>>>>>>>> " )
    self:_RemoveEventListener()
    if IsHD() then
        self:RemoveInputActionBinding(self._jumpHandle)
    end
    Facade.UIManager:ClearSubUIByParent(self, self._wtscroll)
    Module.Settlement:OpenSettlementUI(self.UINavID)
end

-- UI显示时触发
function MPTopTournamentSettlement:OnShowBegin()
    if IsHD() then
        self:PlayAnimation(
            self.WBP_Settlement_TopTournament_Integral_PC_S_in,
            0,
            1,
            EUMGSequencePlayMode.Forward,
            1,
            true
        )
    else
        self:PlayAnimation(self.WBP_Settlement_TopTournament_Integral_in, 0, 1, EUMGSequencePlayMode.Forward, 1, true)
    end
end

function MPTopTournamentSettlement:OnShow()
end

-- UI隐藏时触发
function MPTopTournamentSettlement:OnHide()
end

function MPTopTournamentSettlement:OnHideBegin()
end

------------------------------------ Private function ------------------------------------
function MPTopTournamentSettlement:_AddEventListener()
end

function MPTopTournamentSettlement:_RemoveEventListener()
end

function MPTopTournamentSettlement:_JumpToNextStep()
    if Module.Settlement.Field:GetIsSpecialPopOpen() then
        return
    end
    if self._delayCreateRankScoreAnimTimerHandle then
        self:_CreateRankScoreAnimTimer()
        return
    end

    if self._rankScoreAnimTimerHandle then
        self:_ReleaseRankScoreAnimTimer()
        return
    end
    Facade.UIManager:CloseUI(self)
end

function MPTopTournamentSettlement:OnNavBack()
    return true
end

-- BEGIN MODIFICATION @ VIRTUOS
function MPTopTournamentSettlement:_OnReadyToShowSettlement()
    if IsHD() then
        self._isReadyToShow = true
    end
end

function MPTopTournamentSettlement:_OnStartToShowSettlement()
    if self._isReadyToShow == true and IsHD() then
        --- 在进入大厅的时候再绑一次，防止因为GameFlowChange调用ClearActionBind导致绑定丢失
        self._jumpHandle =
            self:AddInputActionBinding(
            "JumpOver",
            EInputEvent.IE_Pressed,
            self._JumpToNextStep,
            self,
            EDisplayInputActionPriority.UI_Pop
        )
        self._isReadyToShow = false
    end
end
-- END MODIFICATION

return MPTopTournamentSettlement
