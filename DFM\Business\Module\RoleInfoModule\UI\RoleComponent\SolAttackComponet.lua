----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRoleInfo)
----- LOG FUNCTION AUTO GENERATE END -----------
local ECharacterPart = import "ECharacterPart"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local HeroHelperTool = require "DFM.StandaloneLua.BusinessTool.HeroHelperTool"
local UAttackerValueDataManager = import "AttackerValueDataManager"
local UDFMBuffUtil = import "DFMBuffUtil"
local USkillConfigUtils = import "SkillConfigUtils"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local RoleInfoLogic = require "DFM.Business.Module.RoleInfoModule.Logic.RoleInfoLogic"
local SolAttackComponet = ui("SolAttackComponet")

function SolAttackComponet:Ctor()
    self._wtPlayerName = self:Wnd("AttackerTitleTxt", UITextBlock)
    self._wtAttackDistance = self:Wnd("wtAttackDistance", UIWidgetBase)
    self._wtAttackItemName = self:Wnd("wtAttackItemName", UITextBlock)
    self._wtArmorItemName = self:Wnd("wtArmorItemName", UITextBlock)
    self._wtFourthLineTxt = self:Wnd("wtDamageFalloff", UITextBlock)
    self._wtHeroTypeIcon = self:Wnd("wtHeroTypeIcon", UIImage)

    self._wtHurtBar = self:Wnd("RadialImage_HealthBar_Remnants", UIWidgetBase)
    self._wtHurtHealthBar = self:Wnd("RadialImage_HealthBar", UIWidgetBase)
    self._wtStripeHealthBar = self:Wnd("ProgressBar_Stripe", UIWidgetBase)
    self._wtHealthValue = self:Wnd("Text_HealthValue", UITextBlock)
    self._wtKillCompIcon = self:Wnd("WBP_SlotCompIconImage", UIWidgetBase)
    self._wtItemIcon = self._wtKillCompIcon:Wnd("wtMainIcon", UIImage)

    self._wtDetailHealth = self:Wnd("wtDetailHealth", UIWidgetBase)
    self._wtDamageText = self._wtDetailHealth:Wnd("DamageTxt", UITextBlock)
    self._wtHelmetPanel = self._wtDetailHealth:Wnd("DFHorizontalBox_1", UIWidgetBase)
    self._wtHelmetHurt = self._wtDetailHealth:Wnd("DamageTxt_1", UITextBlock)
    self._wtBreastPlatePanel = self._wtDetailHealth:Wnd("DFHorizontalBox", UIWidgetBase)
    self._wtBreastPlateHurt = self._wtDetailHealth:Wnd("DamageTxt_2", UITextBlock)

    self.wtPartImgs = {}
    self.wtPartImgs[ECharacterPart.Head] = self._wtDetailHealth.HeadImg
    self.wtPartImgs[ECharacterPart.Thorax] = self._wtDetailHealth.ThroaxImg
    self.wtPartImgs[ECharacterPart.Abdomen] = self._wtDetailHealth.AbdomenImg
    self.wtPartImgs[ECharacterPart.LeftArm] = self._wtDetailHealth.LeftArmImg
    self.wtPartImgs[ECharacterPart.RightArm] = self._wtDetailHealth.RightArmImg
    self.wtPartImgs[ECharacterPart.LeftLeg] = self._wtDetailHealth.LeftLegImg
    self.wtPartImgs[ECharacterPart.RightLeg] = self._wtDetailHealth.RightLegImg

    self._wtSelectBtn = self:Wnd("DFButton_0", UIButton)
    self._wtSelectBtn:Event("OnClicked", self.OnSelectClick, self)
    self._isSelect = false

    --- BEGIN MODIFICATION @ VIRTUOS: Platform Icon
    if IsConsole() then
        self._wtPlatformIcon = self:Wnd("wtPlatformIcon", UIImage)
    end
    --- END MODIFICATION
end

function SolAttackComponet:OnOpen()

end

function SolAttackComponet:OnInitExtraData(hurtInfo, isAI)
    self._wtBreastPlatePanel:Collapsed()
    self._wtHelmetPanel:Collapsed()
    self._hurtInfo = hurtInfo
    self._isAI = isAI or false -- 保存AI状态
    self._characterParts = self._hurtInfo.character_parts
    self._wtPlayerName:SetText(self._hurtInfo.maker_player_name)
    if self._hurtInfo.distance > 0 then
        self._wtAttackDistance:Visible()
        self._wtAttackDistance:SetText(string.format(Module.RoleInfo.Config.Loc.AttackDistance,
            math.floor(self._hurtInfo.distance / 100, 1)))
    else
        self._wtAttackDistance:Collapsed()
    end

    if self._hurtInfo.hp_damage > 0 then
        self._wtDamageText:Visible()
        self._wtDamageText:SetText(string.format("-%s", RoleInfoLogic.DamageNumber(self._hurtInfo.hp_damage)))
    else
        self._wtDamageText:Collapsed()
    end
    self:SetDamageBodyPart()

    logerror("SolAttackComponet:OnInitExtraData", hurtInfo.maker_player_name, hurtInfo.maker_player_uin, "isAI:",
        self._isAI)
    if self._isAI then
        self._wtHeroTypeIcon:Collapsed()
    else
        local expertIcon = HeroHelperTool.GetHeroExpertIcon(hurtInfo.taker_hero_id)
        self._wtHeroTypeIcon:AsyncSetImagePath(expertIcon, false)
    end

    if self._hurtInfo.weapon_id > 0 then
        local WeaponName = ItemConfigTool.GetItemName(self._hurtInfo.weapon_id)
        if self._hurtInfo.ammo_id > 0 then
            local AmmoName = ItemConfigTool.GetItemName(self._hurtInfo.ammo_id)
            self._wtAttackItemName:SetText(string.format(Module.RoleInfo.Config.Loc.SelfAttackWeaponAmmoItem, WeaponName
                , AmmoName))
        else
            local subType = ItemHelperTool.GetSubTypeById(self._hurtInfo.weapon_id)
            if subType == ItemConfig.EWeaponItemType.EmptyHand or subType == ItemConfig.EWeaponItemType.Melee then
                self._wtAttackItemName:SetText(string.format(Module.RoleInfo.Config.Loc.AttackWeaponItem, WeaponName))
            else
                self._wtAttackItemName:SetText(string.format(Module.RoleInfo.Config.Loc.AttackWeaponItemAI, WeaponName,
                    tostring(hurtInfo.attacker_level)))
            end
        end
    else
        self:RefreshWithOthers()
    end
    --防具文本
    if self._hurtInfo.armor_item_id > 0 then
        local ArmorName = ItemConfigTool.GetItemName(self._hurtInfo.armor_item_id)
        local NewItem = ItemBase:New(self._hurtInfo.armor_item_id)
        self._wtArmorItemName:SetText(string.format(Module.RoleInfo.Config.Loc.AnmyAmmoItemNameText, ArmorName))
        local itemFeature = NewItem:GetFeature()
        if itemFeature:IsHelmet() then
            self._wtHelmetPanel:Visible()
            self._wtBreastPlatePanel:Collapsed()
            self._wtHelmetHurt:SetText(string.format("-%s", RoleInfoLogic.DamageNumber(self._hurtInfo.armor_damage)))
        elseif itemFeature:IsBreastPlate() then
            self._wtHelmetPanel:Collapsed()
            self._wtBreastPlatePanel:Visible()
            self._wtBreastPlateHurt:SetText(string.format("-%s", RoleInfoLogic.DamageNumber(self._hurtInfo.armor_damage)))
        end
    else
        self._wtArmorItemName:SetText("")
        self._wtArmorItemName:Collapsed()
    end
    self._wtHealthValue:SetText(RoleInfoLogic.DamageNumber(hurtInfo.taker_health_after))
    self._wtHurtBar:SetPercent(hurtInfo.taker_health_percent)
    self._wtHurtHealthBar:SetPercent(hurtInfo.taker_health_percent - hurtInfo.hp_damage_percent)

    self:SetPenetrationInfo()

    --- BEGIN MODIFICATION @ VIRTUOS:
    -- Request & Set Platform Icon
    if IsConsole() then
        if self._isAI then
            self._wtPlatformIcon:Collapsed()
        else
            self:_RequestAndSetPlatform(self._hurtInfo.maker_player_uin)
        end
    end

    -- TRC: using Platform Online ID as player name.
    if IsPS5() and not self._isAI then
        local callback = function(onlineID)
            self._wtPlayerName:SetText(onlineID)
        end
        Module.Social:AsyncGetPS5OnlineIdByUID(self._hurtInfo.maker_player_uin, callback, self)
    end
    --- END MODIFICATION @ VIRTUOS
end

--- BEGIN MODIFICATION @ VIRTUOS: Request & Set Platform Icon
function SolAttackComponet:_RequestAndSetPlatform(playerId)
    if IsConsole() then
        local req = pb.CSAccountGetPlayerProfileReq:New()
        req.player_id = playerId
        req.client_flag = 0
        req:Request(function(res)
            if not self:IsValid() then
                return
            end
            local platIconPath = Module.Friend:GetPlatformIconPath(res.plat_id)
            if platIconPath then
                self._wtPlatformIcon:AsyncSetImagePath(platIconPath, false)
                self._wtPlatformIcon:SelfHitTestInvisible()
            else
                self._wtPlatformIcon:Collapsed()
            end
        end,
            { bEnableHighFrequency = true })
    end
end

--- END MODIFICATION @ VIRTUOS: Request & Set Platform Icon

function SolAttackComponet:SetDamageBodyPart()
    for _, wtPartImg in pairs(self.wtPartImgs) do
        wtPartImg:Collapsed()
    end

    for _, Part in pairs(self._characterParts) do
        if self.wtPartImgs[Part] then
            self.wtPartImgs[Part]:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        end
    end
end

function SolAttackComponet:RefreshWithOthers()
    if self._hurtInfo.skill_id > 0 then
        local SkillConfig = USkillConfigUtils.GetSkillConfigDataRowBySkillID(self._hurtInfo.skill_id);
        if SkillConfig.SkillID > 0 then
            self._wtItemIcon:AsyncSetImagePath(SkillConfig.Icon)
            self._wtAttackItemName:SetText(string.format(Module.RoleInfo.Config.Loc.AttackWeaponItem, SkillConfig.Name))
        end
        return
    end

    if self._hurtInfo.buff_id > 0 then
        self._wtAttackDistance:Collapsed()
        local BuffEffectRow = UDFMBuffUtil.GetBuffEffectRowStruct(self._hurtInfo.buff_id);
        if BuffEffectRow then
            local BuffName = BuffEffectRow.DisplayName;
            if BuffEffectRow.DisplayName == "" then
                BuffName = string.format("（未配置）%s", BuffEffectRow.RowDescription)
            end
            if self._hurtInfo.maker_player_name ~= "" then
                self._wtAttackItemName:SetText(string.format(Module.RoleInfo.Config.Loc.AttackWeaponItem, BuffName))
            else
                self._wtPlayerName:SetText(BuffName)
                self._wtAttackItemName:Collapsed()
            end
        end
        return
    end

    if self._hurtInfo.attacker_value_id > 0 then
        self._wtAttackDistance:Collapsed()
        local AttackValueData = UAttackerValueDataManager:Get():GetRow(self._hurtInfo.attacker_value_id)
        if self._hurtInfo.maker_player_name ~= "" then
            self._wtAttackItemName:SetText(string.format(Module.RoleInfo.Config.Loc.AttackWeaponItem,
                AttackValueData.DisplayName))
        else
            self._wtPlayerName:SetText(AttackValueData.DisplayName)
            self._wtAttackItemName:Collapsed()
        end
    end

    if self._hurtInfo.taker_max_health < self._hurtInfo.taker_base_max_health then
        self._wtStripeHealthBar:Visible()
        self._wtStripeHealthBar:SetPercent(self._hurtInfo.taker_base_max_health /
            (self._hurtInfo.taker_base_max_health - self._hurtInfo.taker_max_health))
    else
        self._wtStripeHealthBar:Collapsed()
    end
end

function SolAttackComponet:OnSelectClick()
    self._isSelect = not self._isSelect
    self:SetSelected(self._isSelect)
end

function SolAttackComponet:SetPenetrationInfo()
    if self._hurtInfo.is_penetrating_damage then
        local PenetrationStr = ""
        if self._hurtInfo.penetration_level_decrease > 0 then
            PenetrationStr = string.format(Module.RoleInfo.Config.Loc.PenetrationInfo,
                tonumber(self._hurtInfo.penetration_level_decrease))
        else
            PenetrationStr = Module.RoleInfo.Config.Loc.PenetrationInfoNodecrease
        end

        self._wtFourthLineTxt:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self._wtFourthLineTxt:SetText(PenetrationStr)
    else
        self._wtFourthLineTxt:SetVisibility(ESlateVisibility.Collapsed)
    end
end

return SolAttackComponet
