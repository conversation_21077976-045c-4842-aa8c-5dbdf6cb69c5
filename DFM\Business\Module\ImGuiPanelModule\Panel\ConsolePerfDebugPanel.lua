----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMImGuiPanel)
----- LOG FUNCTION AUTO GENERATE END -----------



local ConsolePerfDebugPanel = ImGuiPanelCls("ConsolePerfDebugPanel")

ConsolePerfDebugPanel.PanelWindowTitle = "主机性能调试面板"
ConsolePerfDebugPanel.bMovable = false
ConsolePerfDebugPanel.bShowTitle = false

ConsolePerfDebugPanel.PositionX = 0.0
ConsolePerfDebugPanel.PositionY = 100
ConsolePerfDebugPanel.PivotX = 1.0
ConsolePerfDebugPanel.PivotY = 0.0
ConsolePerfDebugPanel.AnchorX = 1.0
ConsolePerfDebugPanel.AnchorY = 0.0

ConsolePerfDebugPanel.BackgroundAlpha = 0.5

local ULuaDebugUtil = import "LuaDebugUtil"

--- DANGER!!!!!!!!!!!!!!!!!!!!仅调试用，勿提交
-- local UImGuiPanelManager = import "ImGuiPanelManager"
-- UImGuiPanelManager.ForceEnableImGuiPanel()   --- 强制在Shipping包中使用imgui，极度危险，慎重使用，勿提交
-- UImGuiPanelManager.AddDirectShowLuaPanel("ConsolePerfDebugPanel") --- 强制显示该面板
----------------------------------------------------------------

function ConsolePerfDebugPanel:OnCreatePanel()
    self.GameThreadTime = 0.0
    self.RenderThreadTime = 0.0
    self.RHIThreadTime = 0.0
end

function ConsolePerfDebugPanel:OnGUI()
    if not IsConsole() then
        ImGui.Text("非主机平台不可用!!!")
        return
    end
    self:DrawPlatformName() --- 当前平台名
    ImGui.Separator()
    self:DrawPlatformPerfSetting()  --- 基础性能设置
    ImGui.Separator()
    self:DrawPlatformPerfStats() --- 运行时性能统计
    
end

function ConsolePerfDebugPanel:DrawPlatformName()
    if IsPS5Basic() then
        ImGui.Text("当前平台 : %s","PS5 Basic")
    end

    if IsPS5Pro() then
        ImGui.Text("当前平台 : %s","PS5 Pro")
    end

    if IsXSX() then
        ImGui.Text("当前平台 : %s","XBox Series X")
    end

    if IsXSS() then
        ImGui.Text("当前平台 : %s","XBox Series S")
    end
end

function ConsolePerfDebugPanel:DrawPlatformPerfSetting()
    ImGui.TextColored(ImVec4(1,1,0,1),"性能表现设置")
    ImGui.InspectConsoleVar("sg.GameplayQuality","Gameplay Quality(sg.GameplayQuality)")

    ImGui.BulletText("帧率设置")
    ImGui.Indent(50)
    ImGui.InspectConsoleVar("t.MaxFPS","帧率上限(t.MaxFPS)")
    ImGui.InspectConsoleVar("rhi.EnableConsole120Fps","高帧率模式(rhi.EnableConsole120Fps)")
    ImGui.Text("屏幕最大支持帧率 : %d",ULuaDebugUtil.GetMaxSupportedRefreshRate())
    ImGui.Unindent(50)
    
    ImGui.BulletText("动态分辨率")
    ImGui.Indent(50)
    ImGui.InspectConsoleVar("r.DynamicRes.FrameTimeBudget","帧预算")
    ImGui.InspectConsoleVar("r.DynamicRes.MinScreenPercentage","最小屏幕分辨率")
    ImGui.Unindent(50)
end

function ConsolePerfDebugPanel:DrawPlatformPerfStats()
    ImGui.TextColored(ImVec4(1,1,0,1),"运行时性能数据")

    local perfData = ULuaDebugUtil.GetPerfStatData()

    if self.GameThreadTime == 0.0 then
        self.GameThreadTime = perfData.GameThreadTime
    else
        self.GameThreadTime = 0.9 * self.GameThreadTime + 0.1 * perfData.GameThreadTime
    end

    if self.RenderThreadTime == 0.0 then
        self.RenderThreadTime = perfData.RenderThreadTime
    else
        self.RenderThreadTime = 0.9 * self.RenderThreadTime + 0.1 * perfData.RenderThreadTime
    end

    if self.RHIThreadTime == 0.0 then
        self.RHIThreadTime = perfData.RHIThreadTime
    else
        self.RHIThreadTime = 0.9 * self.RHIThreadTime + 0.1 * perfData.RHIThreadTime
    end

    ImGui.Text("Game : %.2f ms",self.GameThreadTime)
    ImGui.Text("Render : %.2f ms",self.RenderThreadTime)
    ImGui.Text("RHI : %.2f ms",self.RHIThreadTime)
    ImGui.Text("DynRes : %.1f%s",perfData.DynamicResolution*100,"%%")
end

return ConsolePerfDebugPanel