----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMArmedForce)
----- LOG FUNCTION AUTO GENERATE END -----------




--- fighting style
UITable[UIName2ID.FightingStyleView] = {
    UILayer = EUILayer.Pop,
    LuaPath="DFM.Business.Module.ArmedForceModule.UI.FightingStyle.FightingStyleView", 
    BPKey = "WBP_FightingStyleMain_FightingStyle",
    SubUIs = {
        UIName2ID.FightingStyleButton
    }
}

UITable[UIName2ID.FightingStyleButton] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.FightingStyle.FightingStyleButton",
    BPKey = "WBP_FightingStyleButton_FightingStyle",
}

UITable[UIName2ID.ArmedForceNormalSlotView] = {
    UILayer = EUILayer.Sub,
    LuaPath="DFM.Business.Module.ArmedForceModule.UI.Main.ArmedForceNormalSlotView", 
    BPKey = "WBP_EquipmentNormalSlot_Main",
    SubUIs = {
        --- 这个在EquipmentConfig下，之后把它移过来
        UIName2ID.EquipmentContainerPreview,
        UIName2ID.ItemDetailPerkSubView,
        UIName2ID.DragItemPreview,
    }
}

UITable[UIName2ID.ArmedForceSpecialSlotView] = {
    UILayer = EUILayer.Sub,
    LuaPath="DFM.Business.Module.ArmedForceModule.UI.Main.ArmedForceSpecialSlotView", 
    BPKey = "WBP_EquipmentSpecialSlot_Main"
}

UITable[UIName2ID.SpecialSelectionButton] = {
    UILayer = EUILayer.Sub,
    LuaPath="DFM.Business.Module.ArmedForceModule.UI.Main.SpecialSelectionButton", 
    BPKey = "WBP_EquipmentSpecialSelectionButton_Main",
}

UITable[UIName2ID.AssemblySelectionMPMain] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.BattleField.AssemblySelectionMPMain",
    BPKey = "WBP_AssemblyFirearmsMain",
    SubUIs = {
        UIName2ID.SelectionAssemblyDeposCellV1,
        UIName2ID.CommonItemViewDropDownBox,
    },
    Anim = {
        FlowInAni = "WBP_AssemblyFirearmsMain_in",
        FlowOutAni = "WBP_AssemblyFirearmsMain_out"
    },
    --- 默认Cache配置
    ReConfig = {
        IsPoolEnable = true,
    },
    LinkSubStage = ESubStage.HallMain,
}

--#region 新配装界面
UITable[UIName2ID.AssemblyMainPanel] = {
    UILayer = EUILayer.Stack, 
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.MainV2.AssemblyMainPanel", 
    BPKey = "WBP_AssemblyMain",
    SubUIs = {
        UIName2ID.AssemblyEquipSlotView,
        -- UIName2ID.HallPrepareRegion,
        UIName2ID.IVWarehouseTemplate
    },
    Anim = {
        FlowInAni = "WBP_AssemblyMain_in",
        FlowOutAni = "WBP_AssemblyMain_out"
    },
    --- 默认Cache配置
    ReConfig = {
        IsPoolEnable = true,
    },
    LinkSubStage = ESubStage.HallMain,
}

UITable[UIName2ID.AssemblyEquipSlotView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.MainV2.AssemblyEquipSlotView",
    BPKey = "WBP_AssemblyEquipSlotView_Main",
    SubUIs = {
        -- UIName2ID.EquipmentContainerPreview,
        -- UIName2ID.ItemDetailPerkSubView,
        UIName2ID.DragItemPreview,
        UIName2ID.IVSoldOutAbnormalComponent,
    }
}

UITable[UIName2ID.AssemblyQuickOperationMainView] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.Storage.AssemblyQuickOperationMainView",
    BPKey = "WBP_AssemblyStorageDefault_Storage",
    SubUIs = {
        UIName2ID.SelectionAssemblyDeposCellV2,
        UIName2ID.IVWarehouseTemplate,
        UIName2ID.WarehouseContainerPanel,
        UIName2ID.WareHouseContainerBox,
        UIName2ID.IVCommonItemTemplate,
    },
    Anim = {
        FlowInAni = "WBP_AssemblyStorageDefault_in",
        FlowOutAni = "WBP_AssemblyStorageDefault_out"
    },
    --- 默认Cache配置
    ReConfig = {
        IsPoolEnable = true,
    },
    LinkSubStage = ESubStage.HallMain,
}

UITable[UIName2ID.AssemblyBackpack] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.Storage.AssemblyBackpack",
    BPKey = "WBP_AssemblyBackpack",
}


UITable[UIName2ID.SelectionAssemblyDeposCellV2] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.Selection.SelectionAssemblyDeposCellV2",
    BPKey = "WBP_SelectionAssemblyDeposCell_V2",
    SubUIs = {
        UIName2ID.IVCommonItemTemplate,
        UIName2ID.IVMaskLockComponent,
    },
}

UITable[UIName2ID.SelectionAssemblyDeposCellV1] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.Selection.SelectionAssemblyDeposCellV1",
    BPKey = "WBP_SelectionAssemblyDeposCell_V1",
    SubUIs = {
        UIName2ID.IVCommonItemTemplate,
        UIName2ID.IVMaskLockComponent,
    },
}

UITable[UIName2ID.AssemblyWarningItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.MainV2.AssemblyWarningItem",
    BPKey = "WBP_AssemblyWarningItem",
}

UITable[UIName2ID.AssemblyWarningView] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.MainV2.AssemblyWarningView",
    BPKey = "WBP_AssemblyWindow",
    SubUIs = {
        UIName2ID.AssemblyWarningItem,
        UIName2ID.DFCommonButtonV1S1,
    },
    Anim = {
        FlowInAni = "WBP_AssemblyWindow_in",
        FlowOutAni = "WBP_AssemblyWindow_out"
    },
    IsModal = true,
}

UITable[UIName2ID.AssemblyPresetMainPanel] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.MainV2.AssemblyPresetMainPanel",
    BPKey = "WBP_AssemblyDefaultMain",
    SubUIs = {
        UIName2ID.WarehouseContainerPanel,
        UIName2ID.AssemblyEquipSlotView,
        UIName2ID.IVCommonItemTemplate,
        UIName2ID.IVWarehouseTemplate,
        UIName2ID.DFCommonTabV2S1
    },
    Anim = {
        FlowInAni = "WBP_AssemblyDefaultMain_in",
        FlowOutAni = "WBP_AssemblyDefaultMain_out"
    },
    --- 默认Cache配置
    ReConfig = {
        IsPoolEnable = true,
    },
    LinkSubStage = ESubStage.HallMain,
}

UITable[UIName2ID.AssemblySaveSchemeWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.Preset.AssemblySaveSchemeWindow",
    BPKey = "WBP_AssemblySaveSchemeWindow",
    SubUIs = {
        UIName2ID.AssemblySaveSchemeItem
    },
    IsModal = true,
}

UITable[UIName2ID.AssemblySaveSchemeItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.Preset.AssemblySaveSchemeItem",
    BPKey = "WBP_AssemblySaveSchemeItem",
}

UITable[UIName2ID.AssemblyChangeNameWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.Preset.AssemblyChangeNameWindow",
    BPKey = "WBP_AssemblyChangeNameWindow",
    IsModal = true,
}

UITable[UIName2ID.AssemblySaveCustomOutfitConfirmWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.Preset.AssemblySaveCustomOutfitConfirmWindow",
    BPKey = "WBP_AssemblyConfirmWindow",
    IsModal = true,
}

UITable[UIName2ID.AssemblySelectionMain] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.Selection.AssemblySelectionMain",
    BPKey = "WBP_AssemblyFirearmsMain",
    SubUIs = {
        UIName2ID.SelectionAssemblyDeposCellV1,
        UIName2ID.CommonItemViewDropDownBox,
        UIName2ID.IVWarehouseTemplate
    },
    Anim = {
        FlowInAni = "WBP_AssemblyFirearmsMain_in",
        FlowOutAni = "WBP_AssemblyFirearmsMain_out"
    },
    --- 默认Cache配置
    ReConfig = {
        IsPoolEnable = true,
    },
    LinkSubStage = ESubStage.HallMain,
}

UITable[UIName2ID.ArmPropSkinSelectionMPMain] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.BattleField.ArmPropSkinSelectionMPMain",
    BPKey = "WBP_Assembly_Personalization",
    SubUIs = {
        UIName2ID.SelectionAssemblyDeposCellV1,
        UIName2ID.IVWarehouseTemplate
    },
    Anim = {
        FlowInAni = "WBP_Assembly_Personalization_in",
        FlowOutAni = "WBP_Assembly_Personalization_out"
    },
    --- 默认Cache配置
    ReConfig = {
        IsPoolEnable = true,
    },
    LinkSubStage = ESubStage.HallMain,
}

UITable[UIName2ID.AssemblyPresetDownBox] =
{
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.Selection.AssemblyPresetDownBox",
    BPKey = "WBP_SelectionAssemblyDroDownBox",
    Anim = {
        bManuelAnim = true
    }
}

UITable[UIName2ID.AssemblyPresetItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.Selection.AssemblyPresetItem",
    BPKey = "WBP_SelectionAssemblyItem",
}

UITable[UIName2ID.AssemblySquadPick] = {
    UILayer = EUILayer.Stack, 
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.SquadPick.AssemblySquadPick", 
    BPKey = "WBP_Assembly_SquadPick",
    SubUIs = {
        UIName2ID.HeroMainViewHeroItem,
        UIName2ID.HeroMainViewHeroListItem,
        UIName2ID.HeroSkillItem,
        UIName2ID.AssemblyChatHD,
        UIName2ID.DFCommonTabV3,
    },
    LinkSubStage = ESubStage.HallMatch,
}

UITable[UIName2ID.AssemblyLabel] = {
    UILayer = EUILayer.Sub, 
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.SquadPick.AssemblyLabel", 
    BPKey = "WBP_Assembly_Label",
}

UITable[UIName2ID.AssemblyChatHD] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.SquadPick.AssemblyChatHD",
    BPKey = "WBP_AssemblyChatInput",
}

UITable[UIName2ID.AssemblyCardDisplay] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.SquadPick.AssemblyCardDisplay",
    BPKey = "WBP_Assembly_CardDisplay",
    SubUIs={
        UIName2ID.HeroBussinessCard
    }
}

UITable[UIName2ID.AssemblyCardDisplayItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.SquadPick.AssemblyCardDisplayItem",
    BPKey = "WBP_Assembly_CardDisplay_Item",
    Anim = {
        bManuelAnim = true--设置成手动播放，不然会和父ui中的子动画冲突
    }
}

UITable[UIName2ID.SOLEventVideo] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.SquadPick.SOLEventVideo",
    BPKey = "WBP_SOLEvent_Video",
    IsModal = true,
}
--#endregion

--- Key
UITable[UIName2ID.EquipKeySlotView] = {
    UILayer = EUILayer.Sub,
    LuaPath="DFM.Business.Module.ArmedForceModule.UI.Key.EquipKeySlotView", 
    BPKey = "WBP_EquipmentKeySlot_Key"
}

--- container shop/depos
UITable[UIName2ID.ContainerPresetView] = {
    UILayer = EUILayer.Sub,
    LuaPath="DFM.Business.Module.ArmedForceModule.UI.Container.ContainerPresetView", 
    BPKey = "WBP_ContainerPresetView",
    SubUIs = {
        UIName2ID.SelectionEquipCell,
        UIName2ID.CurrencyIconNum
    }
}

-- UITable[UIName2ID.ContainerHalfView] = {
--     UILayer = EUILayer.Sub,
--     LuaPath="DFM.Business.Module.ArmedForceModule.UI.Container.ContainerHalfView", 
--     BPKey = "WBP_ContainerHalfView",
-- }

UITable[UIName2ID.ContainerMainPanel] = {
    UILayer = EUILayer.Stack,
    LuaPath="DFM.Business.Module.ArmedForceModule.UI.Container.ContainerMainPanel", 
    BPKey = "WBP_ContainerMainPanel_Container",
    SubUIs = {
        UIName2ID.ContainerHalfView,
        UIName2ID.ContainerPresetView,
    }
}

-- container preview

--- equipment
UITable[UIName2ID.EquipmentSlotItemView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.Container.EquipmentSlotItemView",
    BPKey = "WBP_EquipmentSlotItemView",
    SubUIs = {
        UIName2ID.DragItemPreview
    }
}

UITable[UIName2ID.EquipmentSlotItemStyleView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.Container.EquipmentSlotItemView",
    BPKey = "WBP_EquipmentSlotItemView_Style",
    SubUIs = {
        UIName2ID.DragItemPreview
    }
}

UITable[UIName2ID.EquipmentDrugItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.Main.EquipmentDrugItem",
    BPKey = "WBP_EquipmentDrugItem_Main"
}

--region preset
UITable[UIName2ID.EquipmentBagPreview] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.Preset.EquipmentBagPreview",
    BPKey = "WBP_EquipmentBagPreview",
    SubUIs = {
        UIName2ID.EquipmentContainerPreview,
    }
}

UITable[UIName2ID.PresetChooseView] = {
    UILayer = EUILayer.Sub,
    LuaPath="DFM.Business.Module.ArmedForceModule.UI.Preset.PresetChooseView", 
    BPKey = "WBP_PresetChooseView",
    SubUIs = {
        UIName2ID.CurrencyIconNum,
        UIName2ID.CommonSubTab
    }
}

UITable[UIName2ID.PresetEditSaveView] = {
    UILayer = EUILayer.Sub,
    LuaPath="DFM.Business.Module.ArmedForceModule.UI.Preset.PresetEditSaveView", 
    BPKey = "WBP_PresetEditSaveConfig",
}

UITable[UIName2ID.PresetBuyItemWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath="DFM.Business.Module.ArmedForceModule.UI.Preset.PresetBuyItemWindow", 
    BPKey = "WBP_PresetBuyItemWindow",
    SubUIs = {
        UIName2ID.PresetBuyItemView,
    }
}

UITable[UIName2ID.PresetBuyItemView] = {
    UILayer = EUILayer.Sub,
    LuaPath="DFM.Business.Module.ArmedForceModule.UI.Preset.PresetBuyItemView", 
    BPKey = "WBP_PresetBuyItemView",
}

UITable[UIName2ID.PresetRenameWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath ="DFM.Business.Module.ArmedForceModule.UI.Preset.PresetRenameWindow", 
    BPKey = "WBP_PresetRenameWindow",
}

UITable[UIName2ID.PresetSaveToPresetWindow] = {
    UILayer = EUILayer.Pop, 
    LuaPath ="DFM.Business.Module.ArmedForceModule.UI.Preset.PresetSaveToPresetWindow", 
    BPKey = "WBP_PresetSaveToPresetWindow",
    SubUIs = {
        UIName2ID.PresetSingleConfigView,
    }
}

UITable[UIName2ID.PresetSingleConfigView] = {
    UILayer = EUILayer.Sub, 
    LuaPath ="DFM.Business.Module.ArmedForceModule.UI.Preset.PresetSingleConfigView", 
    BPKey = "WBP_PresetSingleConfigView",
}
--endregion

UITable[UIName2ID.SelectionEquipToSlotWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath="DFM.Business.Module.ArmedForceModule.UI.Selection.SelectionEquipToSlotWindow", 
    BPKey = "WBP_SelectionMedicalKitPanel",
    SubUIs = {
        UIName2ID.EquipmentContainerPreview,
    }
}

--- selection(split)
UITable[UIName2ID.SelectionEquipCell] = {
    UILayer = EUILayer.Sub,
    LuaPath="DFM.Business.Module.ArmedForceModule.UI.SplitSelection.SelectionEquipCell", 
    BPKey = "WBP_SelectionSplitCell",
    SubUIs = {
        UIName2ID.CurrencyIconNum,
    }
}

UITable[UIName2ID.SelectionEquipSplitList] = {
    UILayer = EUILayer.Sub,
    LuaPath="DFM.Business.Module.ArmedForceModule.UI.SplitSelection.SelectionEquipSplitList", 
    BPKey = "WBP_SelectionEquipSplitList",
    Anim = {
        FlowInAni = "In_Anim",
    }
}

UITable[UIName2ID.SelectionSubTab] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.SplitSelection.SelectionSubTab",
    BPKey = "WBP_SelectionSubTab",
}

UITable[UIName2ID.EquipmentWarningView] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.Main.EquipmentWarningView",
    BPKey = "WBP_EquipmentWarningWindow_Main"
}

UITable[UIName2ID.EquipmentCheckMainView] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.Check.EquipmentCheckMainView",
    BPKey = "WBP_EquipmentCheckPanel_Check",
}

UITable[UIName2ID.EquipmentCheckBoxMainPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.Check.EquipmentCheckBoxPanel",
    BPKey = "WBP_EquipmentCheck_PartCheck_Check",
}

UITable[UIName2ID.EquipmentCheckBoxMedicinePanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.Check.EquipmentCheckBoxPanel",
    BPKey = "WBP_EquipmentCheck_MedTip_Check"
}

UITable[UIName2ID.EquipmentCheckSliderPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.Check.EquipmentCheckSliderPanel",
    BPKey = "WBP_EquipmentCheck_Slider_Check"
}

UITable[UIName2ID.EquipmentCheckItemTransferSwitchPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.Check.EquipmentCheckSwitchPanel",
    BPKey = "WBP_EquipmentCheck_ItemTransfer_Check"
}

UITable[UIName2ID.EquipmentCheckSwitchPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.Check.EquipmentCheckSwitchPanel",
    BPKey = "WBP_EquipmentCheck_Switch_Check"
}


---------------------------------------------------------------------------------
--- MP
---------------------------------------------------------------------------------
if DFHD_LUA == 1 then
    UITable[UIName2ID.BattleFieldPresetPanelHD] = {
        UILayer = EUILayer.Stack,
        LuaPath = "DFM.Business.Module.ArmedForceModule.UI.BattleField.BattleFieldPresetPanel",
        BPKey = "WBP_Equipment_Battle_Preset",
        --- 默认Cache配置
        ReConfig = {
            IsPoolEnable = true,
        },
        SubUIs = {
            UIName2ID.BattleFieldItemView,
            UIName2ID.IVWarehouseTemplate
        },
        LinkSubStage = ESubStage.HallMain,
    }
else
    UITable[UIName2ID.BattleFieldPresetPanel] = {
        UILayer = EUILayer.Stack,
        LuaPath = "DFM.Business.Module.ArmedForceModule.UI.BattleField.BattleFieldPresetPanel",
        BPKey = "WBP_Equipment_Battle_Preset",
        SubUIs = {
            UIName2ID.BattleFieldItemView,
            UIName2ID.IVWarehouseTemplate,
        },
        --- 默认Cache配置
        ReConfig = {
            IsPoolEnable = true,
        },
        LinkSubStage = ESubStage.HallMain,
    }
end

UITable[UIName2ID.BattleFieldItemView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.BattleField.BattleFieldItemView",
    BPKey = "WBP_Equipment_BattleItemView",
}

UITable[UIName2ID.AssemblyWeightButton] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.MainV2.AssemblyWeightButton",
    BPKey = "WBP_Assembly_WeightBtn",
}

-- 租借
UITable[UIName2ID.AssemblyRentalMainPanel] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.Rental.Mobile.AssemblyRentalMainPanel",
    BPKey = "WBP_Assembly_Rental",
    ReConfig = {
        IsPoolEnable = true,
    },
    SubUIs = {
        UIName2ID.AssemblyRentalItem,
        UIName2ID.IVWarehouseTemplate,
        UIName2ID.IVCommonItemTemplate,
    },
}
UITable[UIName2ID.AssemblyRentalItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.Rental.Mobile.AssemblyRentalItem",
    BPKey = "WBP_Assembly_RentalPlan",
    SubUIs = {
        UIName2ID.WareHouseContainerBox,
    }
}

UITable[UIName2ID.AssemblyArmorPurchaseWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.Selection.AssemblyArmorPurchaseWindow",
    BPKey = "WBP_Assembly_ArmorPurchaseWindow",
    SubUIs = {
        UIName2ID.AssemblyArmorPurchaseItem
    },
    IsModal = true,
}

UITable[UIName2ID.AssemblyArmorPurchaseItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ArmedForceModule.UI.Selection.AssemblyArmorPurchaseItem",
    BPKey = "WBP_Assembly_ArmorPurchaseItem",
}

--region DFHD Start
if DFHD_LUA == 1 then
    UITable[UIName2ID.AssemblyHDMainPanel] = {
        UILayer = EUILayer.Stack,
        LuaPath = "DFM.Business.Module.ArmedForceModule.UI.DFHD.AssemblyHDMainPanel",
        BPKey = "WBP_AssemblyMain_Pc",
        SubUIs = {
            UIName2ID.AssemblyEquipSlotView,
            -- UIName2ID.HallPrepareRegion,
        },
        Anim = {
            FlowInAni = "WBP_AssemblyMain_in",
            FlowOutAni = "WBP_AssemblyMain_out"
        },
        --- 默认Cache配置
        ReConfig = {
            IsPoolEnable = true,
        },
        LinkSubStage = ESubStage.HallMain,
    }

    UITable[UIName2ID.AssemblyHDStorageButton] = {
        UILayer = EUILayer.Sub,
        LuaPath = "DFM.Business.Module.ArmedForceModule.UI.DFHD.AssemblyHDStorageButton",
        BPKey = "WBP_AssemblyStorageBtn_Pc"
    }

    UITable[UIName2ID.AssemblyHDDrugsBtn] = {
        UILayer = EUILayer.Sub,
        LuaPath = "DFM.Business.Module.ArmedForceModule.UI.DFHD.AssemblyHDDrugsBtn",
        BPKey = "WBP_AssemblyDrugsBtn_Pc"
    }

    UITable[UIName2ID.AssemblyEquipAmmoCase] = {
        UILayer = EUILayer.Sub,
        LuaPath = "DFM.Business.Module.ArmedForceModule.UI.DFHD.AssemblyEquipAmmoCase",
        BPKey = "WBP_AssemblyEquipAmmoCase"
    }

    UITable[UIName2ID.AssemblyHDQuickOperationMainView] = {
        UILayer = EUILayer.Stack,
        LuaPath = "DFM.Business.Module.ArmedForceModule.UI.DFHD.AssemblyHDQuickOperationMainView",
        BPKey = "WBP_AssemblyStorageDefault_Storage_PC",
        SubUIs = {
            UIName2ID.WareHouseContainerBox_HD,
            UIName2ID.SelectionAssemblyDeposCellV2,
            UIName2ID.IVWarehouseTemplate,
	        UIName2ID.WarehouseContainerPanel_HD,
            -- BEGIN MODIFICATION @ VIRTUOS : 
            UIName2ID.IVCommonItemTemplate,
            -- END MODIFICATION
        },
        Anim = {
            FlowInAni = "WBP_AssemblyStorageDefault_in",
            FlowOutAni = "WBP_AssemblyStorageDefault_out"
        },
        --- 默认Cache配置
        ReConfig = {
            IsPoolEnable = true,
        },
        LinkSubStage = ESubStage.HallMain,
    }

    UITable[UIName2ID.AssemblyHDPresetMainPanel] = {
        UILayer = EUILayer.Stack,
        LuaPath = "DFM.Business.Module.ArmedForceModule.UI.DFHD.AssemblyHDPresetMainPanel",
        BPKey = "WBP_AssemblyDefaultMain_PC",
        SubUIs = {
            UIName2ID.AssemblyEquipSlotView,
            UIName2ID.IVCommonItemTemplate,
            UIName2ID.IVWarehouseTemplate,
            UIName2ID.DFCommonTabV2S1
        },
        Anim = {
            FlowInAni = "WBP_AssemblyDefaultMain_in",
            FlowOutAni = "WBP_AssemblyDefaultMain_out"
        },
        --- 默认Cache配置
        ReConfig = {
            IsPoolEnable = true,
        },
        LinkSubStage = ESubStage.HallMain,
    }
    
    -- 租借
    UITable[UIName2ID.AssemblyHDRentalMainPanel] = {
        UILayer = EUILayer.Stack,
        LuaPath = "DFM.Business.Module.ArmedForceModule.UI.Rental.HD.AssemblyHDRentalMainPanel",
        BPKey = "WBP_Assembly_Rental_PC",
        ReConfig = {
            IsPoolEnable = true,
        },
        SubUIs = {
            UIName2ID.AssemblyHDRentalItem,
            UIName2ID.IVWarehouseTemplate,
            UIName2ID.IVCommonItemTemplate,
        },
    }
    UITable[UIName2ID.AssemblyHDRentalItem] = {
        UILayer = EUILayer.Sub,
        LuaPath = "DFM.Business.Module.ArmedForceModule.UI.Rental.HD.AssemblyHDRentalItem",
        BPKey = "WBP_Assembly_RentalPlan_PC",
        SubUIs = {
            UIName2ID.WareHouseContainerBox_HD,
        }
    }

end
--endregion

local ArmedForceConfig =
{
    ---@class EPlanLevel
    ESpecialSlotType = {
        None = 0,
        Ability = 1,
        ThrowWeapon = 2,
        SecondaryWeapon = 3,
    },

    ---@class EPlanLevel
    EPlanLevel = {
        Lv1 = 1,
        Lv2 = 2,
        Lv3 = 3,
    },

    ---@class EEnterFrom
    EEnterFrom = {
        Equipment = 0,
        MapMode = 1,
        FriendReady = 2,
        Preparation = 3,
        SafeHouse = 4,
        HallMain = 5,
        SandBox = 6,
        RaidSelect = 7,
        PrepareFlow = 8,
        Navigation = 9,
        RoomMain = 10
    },

    --- move start
    ---@class EHighlightMode
    EHighlightMode = {
        None = 0,
        Target = 1,
        Valid = 2,
        InValid = 3
    },

    ---@class ESelectionOperationType
    ESelectionOperationType = {
        Single = 0,
        Batch = 1,
    },

    ---@class EPageType
    -- EPageType = {
    --     Main = 0, 
    --     Preset = 1,
    --     Container = 2,
    --     Selection = 4,
    --     ChoosePreset = 5,
    -- },

    ---@class EAbnormalType
    EAbnormalType = { ---检查配置异常枚举类型
        NotCarrying = 0,                                        -- 未携带
        LackBullet = 1,                                         -- 缺少子弹
        InsufficientDurability = 2,                             -- 护甲耐久度不足
        HasUnnecessaryItems = 3,                                -- 保险箱内有道具未转移
        LackMedicine = 4,                                       -- 缺少xx类药物
        StorageSpaceIsTight = 5,                                -- 未携带背包或背包剩余空间不足
        EquipmentAllValueNotEnough = 6,                         -- 装备总价值未达到地图要求
        LackNightVisionGoggles = 7,                             -- 夜战图缺少夜视仪
        LackThermalImagineOrFlashlight = 8,                     -- 夜战图缺少热成像和手电
        SafeBoxExpiredStatus = 9,                               -- 安全箱过期
        KeyChainExpiredStatus = 10,                             -- 门禁卡过期
        RentalVoucherDoNotMeetEntryRequirements = 11,           -- 制式装备券不满足入局要求
        Max = 12,
    },

    EAnalysisType=
    {
        View = 1,   -- 查看推荐
        Apply = 2,  -- 应用推荐
        Modify = 3, -- 修改推荐
    },

    -- 伤害救治>异常清除>护甲修复>增强药剂
    EAssemblyDispensingMedicineType=
    {
        EDispensingMedicineType.HP,
        EDispensingMedicineType.BUFF,
        EDispensingMedicineType.Armor,
        EDispensingMedicineType.Strengthen,
    },

    ESubViewType=
    {
        Main = 0,       -- 配装主界面
        Equipment = 1,  -- 装备选择界面
        Medicine = 2,   -- 配药界面
        Expert = 3,     -- 专家界面
        Bullet = 4,     -- 配子弹界面
    },

    EAssemblyPresetType={
        ESlotType.MainWeaponLeft,
        ESlotType.BulletLeft,
        ESlotType.Bag,
        ESlotType.ChestHanging,
        ESlotType.Helmet,
        ESlotType.BreastPlate,
    },

    ERentalType={
        [ESlotGroup.Rental_1] = 1,
        [ESlotGroup.Rental_2] = 2,
        [ESlotGroup.Rental_3] = 3,
    },

    --------------------------------------------------------------------------
    --- Data Table
    --------------------------------------------------------------------------
    mapInfoTable = Facade.TableManager:GetTable("MapInfo"),
    keyInfoTable = Facade.TableManager:GetTable("KeyInfo"), 

    armForcePresetTable = Facade.TableManager:GetTable("ArmedForcePresetTable"),
    armForceTable = Facade.TableManager:GetTable("ArmedForceData"),
    armOutfitTable = Facade.TableManager:GetTable("ArmedForceOutfitData"),
    assemblyPresetTable = Facade.TableManager:GetTable("AssemblyPresetTable"),
    armAbilityTable = Facade.TableManager:GetTable("AbilityData"),
    combatRoleAbilityData = Facade.TableManager:GetTable("CombatRoleAbilityData"),
    heroDataTable = Facade.TableManager:GetTable("Hero/HeroData"),
    heroFashionDataTable = Facade.TableManager:GetTable("Hero/HeroFashionData"),
    heroConstantTable = Facade.TableManager:GetTable("HeroConstant"),

    -- carryItemtab = Facade.TableManager:GetTable("CarryItem/CarryItemsData"),  --随身装备数据
    itemHealthtab = Facade.TableManager:GetTable("ItemHealth"), --药品数值表
    ammoDataTable = Facade.TableManager:GetTable("WeaponPart/AmmoDataTable"), --子弹表
    healArmortab = Facade.TableManager:GetTable("CarryItem/CarrayItem_HealArmorConfig"), --护甲数值表

    equipmentAmmoRecommendTable = Facade.TableManager:GetTable("EquipmentAmmoRecommendConfig"),   --武器子弹推荐数量配置
    equipmentOutfitAmmoTable = Facade.TableManager:GetTable("EquipmentOutfitAmmoConfig"),   --预设套装武器子弹推荐数量配置
    
    newEquipmentCheckTable = Facade.TableManager:GetTable("NewEquipmentCheckConfig"),   --新配装检测

    equipmentPresetDataTable = Facade.TableManager:GetTable("EquipmentPresetDataConfig"),   --物资券方案表
    equipmentPresetPriceDataTable = Facade.TableManager:GetTable("EquipmentPresetPriceDataConfig"),   --物资券方案价值表
    RaidBulletCheckTable = Facade.TableManager:GetTable("RaidBulletCheckConfig"),   --raid子弹检查表

    SOLEventDynamicQuestLevelConfigTable= Facade.TableManager:GetTable("SOLEvent/SOLEventDynamicQuestLevelConfigTable"),--入局事件表
    MediaResTable= Facade.TableManager:GetTable("MediaResTable"),--视频资源表

    --------------------------------------------------------------------------
    --- Data Table
    --------------------------------------------------------------------------
    --------------------------------------------------------------------------
    --- Armed Force (SOL)
    --------------------------------------------------------------------------
    evtArmId2HeroIdListChanged = LuaEvent:NewIns("evtArmId2HeroIdListChanged"),
    evtSlotViewClicked = LuaEvent:NewIns("evtSlotViewClicked"),
    evtSpecialSlotViewClicked = LuaEvent:NewIns("evtSpecialSlotViewClicked"),

    evtPos2NeedBuyItemChanged = LuaEvent:NewIns("evtPos2NeedBuyItemChanged"),
    evtPos2NeedBuyItemCleared = LuaEvent:NewIns("evtPos2NeedBuyItemCleared"),
    evtPlayEnterMainPageAnim = LuaEvent:NewIns("evtPlayEnterMainPageAnim"),

    evtCheckFightingStyle = LuaEvent:NewIns("evtCheckFightingStyle"),
    evtItemWeightChanged = LuaEvent:NewIns("evtItemWeightChanged"),

    evtPos2FitSampleItemChanged = LuaEvent:NewIns("evtPos2FitSampleItemChanged"),
    evtPresetFitSampleItem = LuaEvent:NewIns("evtPresetFitSampleItem"),
    evtPresetFitSampleItemFinished = LuaEvent:NewIns("evtPresetFitSampleItemFinished"),
    evtOnSchemeItemClicked = LuaEvent:NewIns("evtOnSchemeItemClicked"),
    evtApplyOutfitFinished = LuaEvent:NewIns("evtApplyOutfitFinished"),
    evtApplyOutfitError = LuaEvent:NewIns("evtApplyOutfitError"),
    evtApplyQuickOperationData = LuaEvent:NewIns("evtApplyQuickOperationData"),
    evtFinishQuickOperationData = LuaEvent:NewIns("evtFinishQuickOperationData"),
    evtUpdateOutfitEquipPosition = LuaEvent:NewIns("evtUpdateOutfitEquipPosition"),


    evtPos2FitPresetDataChanged = LuaEvent:NewIns("evtPos2FitPresetDataChanged"),
    evtBagSpaceContainerFitPresetDatasChanged = LuaEvent:NewIns("evtBagSpaceContainerFitPresetDatasChanged"),

    evtArmedForceMainOpenFinish = LuaEvent:NewIns("evtArmedForceMainOpenFinish"),
    evtArmedForceMainShow = LuaEvent:NewIns("evtArmedForceMainShow"),
    evtArmedForceMainHide = LuaEvent:NewIns("evtArmedForceMainHide"),
    evtArmedForceSureBtnClicked = LuaEvent:NewIns("evtArmedForceSureBtnClicked"),
    evtArmedForceWarehouseBtnClicked = LuaEvent:NewIns("evtArmedForceWarehouseBtnClicked"),
    evtSelectionEquipMainOpenFinish = LuaEvent:NewIns("evtSelectionEquipMainOpenFinish"),

    evtAssemblySquadPickOpenFinish = LuaEvent:NewIns("evtAssemblySquadPickOpenFinish"),
    evtAssemblySquadPickClose = LuaEvent:NewIns("evtAssemblySquadPickClose"),
    evtAssemblySquadPickReadyToClose = LuaEvent:NewIns("evtAssemblySquadPickReadyToClose"),
    evtAssemblySquadPickHeroClicked = LuaEvent:NewIns("evtAssemblySquadPickHeroClicked"),
    evtAssemblySquadPickProcessIsReady = LuaEvent:NewIns("evtAssemblySquadPickProcessIsReady"),

    evtAssemblySquadPickConfirmClicked = LuaEvent:NewIns("evtAssemblySquadPickConfirmClicked"),
    evtArmedForceQuickOperationTryOpen = LuaEvent:NewIns("evtArmedForceQuickOperationTryOpen"),
    evtArmedForceQuickOperationOpen = LuaEvent:NewIns("evtArmedForceQuickOperationOpen"),
    evtArmedForceQuickOperationOpenFinish = LuaEvent:NewIns("evtArmedForceQuickOperationOpenFinish"),
    evtArmedForceQuickOperationConfirmClicked = LuaEvent:NewIns("evtArmedForceQuickOperationConfirmClicked"),
    evtAssemblyMainPanelRentalBtnClicked = LuaEvent:NewIns("evtAssemblyMainPanelRentalBtnClicked"),

    evtAssemblyRentalMainPanelOnShowBegin = LuaEvent:NewIns("evtAssemblyRentalMainPanelOnShowBegin"),
    evtAssemblyRentalMainPanelOnHideBegin = LuaEvent:NewIns("evtAssemblyRentalMainPanelOnHideBegin"),


    evtAssemblySelectionMainShow = LuaEvent:NewIns("evtAssemblySelectionMainShow"),
    evtAssemblySelectionRepairBtnClicked = LuaEvent:NewIns("evtAssemblySelectionRepairBtnClicked"),
    evtPostAssemblySelectionRefreshEquipmentBtnState = LuaEvent:NewIns("evtPostAssemblySelectionRefreshBtnState"),
    evtOnAssemblySelectionMainSelectFirstItemDone = LuaEvent:NewIns("evtPostAssemblySelectionRefreshEquipmentBtnState"),

    --------------------------------------------------------------------------
    --- Do Something Show Event
    --------------------------------------------------------------------------
    evtEquipmentResetLightTargetSlot = LuaEvent:NewIns("evtEquipmentResetLightTargetSlot"),
    --------------------------------------------------------------------------
    --- View Interactive Event
    --------------------------------------------------------------------------
    evtSlotItemClicked = LuaEvent:NewIns("evtSlotItemClicked"),
    -- 用于模型展示
    evtOutfitSelectionCellClicked = LuaEvent:NewIns("evtOutfitSelectionCellClicked"),
    evtOutfitLayerUpdated = LuaEvent:NewIns("evtOutfitSelectionCellClicked"),

    evtBuyLackItemWindowClosed = LuaEvent:NewIns("evtBuyLackItemWindowClosed"),
    evtRenameWindowClosed = LuaEvent:NewIns("evtRenameWindowClosed"),

    evtUIRootVisible =  LuaEvent:NewIns("evtUIRootVisible"),
    --------------------------------------------------------------------------
    --- Field Data Change Event
    --------------------------------------------------------------------------
    evtChosenPresetIdChanged = LuaEvent:NewIns("evtChosenPresetIdChanged"),
    evtMergeDataInfoChanged =  LuaEvent:NewIns("evtMergeDataInfoChanged"),
    evtMergeDataListGenerated =  LuaEvent:NewIns("evtMergeDataListGenerated"),
    evtAssemblySelectionDataListGenerated =  LuaEvent:NewIns("evtAssemblySelectionDataListGenerated"),

    --------------------------------------------------------------------------
    --- Check Equip Data Event
    --------------------------------------------------------------------------
    evtEquipAbnormalChanged = LuaEvent:NewIns("evtEquipAbnormalChanged"),
    evtAbnormalSlotChanged = LuaEvent:NewIns("evtAbnormalSlotChanged"),
    evtAbnormalSlotAmiPlay = LuaEvent:NewIns("evtAbnormalSlotAmiPlay"),
    evtSelectEditTarget = LuaEvent:NewIns("evtSelectEditTarget"),
    evtMainCheckBoxChanged = LuaEvent:NewIns("evtMainCheckBoxChanged"),
    evtDeBufCheckBoxChanged = LuaEvent:NewIns("evtDeBufCheckBoxChanged"),
    evtSliderValueChanged = LuaEvent:NewIns("evtSliderValueChanged"),
    evtSwitchValueChanged = LuaEvent:NewIns("evtSwitchValueChanged"),

    evtAllEquipmentValueChanged = LuaEvent:NewIns("evtAllEquipmentValueChanged"),

    --------------------------------------------------------------------------
    --- 快速配置操作
    --------------------------------------------------------------------------
    evtWeaponBulletDataChanged = LuaEvent:NewIns("evtWeaponBulletDataChanged"),

    evtQuickOperationDataInfoChanged = LuaEvent:NewIns("evtQuickOperationDataInfoChanged"),
    evtRemoveWeaponBullet = LuaEvent:NewIns("evtRemoveWeaponBullet"),
    evtOnOperationItemAddClicked = LuaEvent:NewIns("evtOnOperationItemAddClicked"),
    evtOnOperationItemViewDecreaseClicked = LuaEvent:NewIns("evtOnOperationItemViewDecreaseClicked"),
    evtOnOperationItemViewUpDatePropInfo = LuaEvent:NewIns("evtOnOperationItemViewUpDatePropInfo"),

    evtSubViewChanged = LuaEvent:NewIns("evtevtSubViewChanged"),
    evtOnDragLeaveAssemblyBackpack = LuaEvent:NewIns("evtOnDragLeaveAssemblyBackpack"),

    --------------------------------------------------------------------------
    --- MP Data Event
    --------------------------------------------------------------------------
    evtOnSetOutfitFlag = LuaEvent:NewIns("evtOnSetOutfitFlag"),
    evtOnDebugWeaponSceneObject = LuaEvent:NewIns("evtOnDebugWeaponSceneObject"),
    evtOnMpPresetPanelShow = LuaEvent:NewIns("evtOnMpPresetPanelShow"),
    evtOnMpPresetPanelHide = LuaEvent:NewIns("evtOnMpPresetPanelHide"),
    evtOnAssemblySelectionMPMainShow = LuaEvent:NewIns("evtOnAssemblySelectionMPMainShow"),
    evtOnAssemblySelectionMPMainHide = LuaEvent:NewIns("evtOnAssemblySelectionMPMainHide"),
    evtOnWeaponUpgradeBtnClicked = LuaEvent:NewIns("evtOnWeaponUpgradeBtnClicked"),
    evtOnWeaponUpgradePanelBtnClicked = LuaEvent:NewIns("evtOnWeaponUpgradePanelBtnClicked"),
    evtOnAssemblySelectionOutAnimBegin = LuaEvent:NewIns("evtOnAssemblySelectionOutAnimBegin"),
    evtOnAssemblyWeaponClicked = LuaEvent:NewIns("evtOnAssemblyWeaponClicked"),

    --------------------------------------------------------------------------
    --- Select Bullet
    --------------------------------------------------------------------------
    evtSelectBulletSlotChanged = LuaEvent:NewIns("evtSelectBulletSlotChanged"),
    evtSelectBulletNumChanged = LuaEvent:NewIns("evtSelectBulletNumChanged"),
    evtInventoryNumChanged = LuaEvent:NewIns("evtInventoryNumChanged"),
    evtProcessBtnClicked = LuaEvent:NewIns("evtProcessBtnClicked"),
    evtSetBulletSelectNum = LuaEvent:NewIns("evtSetBulletSelectNum"),

    --------------------------------------------------------------------------
    --- Melee Data Event
    --------------------------------------------------------------------------
    evtOnMeleeWeaponDataChanged = LuaEvent:NewIns("evtOnMeleeWeaponDataChanged"),

    --------------------------------------------------------------------------
    --- Rental Data Event
    --------------------------------------------------------------------------
    evtOnRentalWeaponBulletChanged = LuaEvent:NewIns("evtOnRentalWeaponBulletChanged"),
    evtRentalShowTipsChanged = LuaEvent:NewIns("evtRentalShowTipsChanged"),

    evtOnSelectedArmorIndexChanged = LuaEvent:NewIns("evtOnSelectedArmorIndexChanged"),

    evtOutFitBulletNumChanged = LuaEvent:NewIns("evtOutFitBulletNumChanged"),
    evtOutFitCapacityChanged = LuaEvent:NewIns("evtOutFitCapacityChanged"),

    --------------------------------------------------------------------------
    --- 手柄 Event
    --------------------------------------------------------------------------
    evtInputSummaryListChanged = LuaEvent:NewIns("evtInputSummaryListChanged"),


    Loc = {
        PresetTitle = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_PresetTitle", "推荐配装"),
        Title = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_Title", "装备配置"),
        BagTitle = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_BagTitle", "携带物资"),
        ConfigureMedicineTitle = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_ConfigureMedicineTitle", "配置药品"),
        ConfigureBulletTitle = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_ConfigureBulletTitle", "配置子弹"),
        SelectTitle = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_SelectTitle", "选择%s"),
        MaterialConfig = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_MaterialConfig", "物资配置"),
        FightingStyle = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_FightingStyle", "配装风格: %s"),
        PlanLevel = {
            NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_PlanLevel_1", "轻装上阵"),
            NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_PlanLevel_2", "战局生存"),
            NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_PlanLevel_3", "战局主宰"),
        },
        DefaultPlanString = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_DefaultPlanString", "自定义方案%s"),
        EmptyPlanString = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_EmptyPlanString", "空"),
        BuyAndEquip = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_BuyAndEquip", "补齐配装"),
        FinishPreset = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_FinishPreset", "使用  "),
        FinishApply = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_FinishApply", "下一步  "),
        FinishConfirm = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_FinishConfirm", "确认  "),
        BuyConfirm = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_BuyConfirm", "购买"),
        AuctionIsLock = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_AuctionIsLock", "交易行未解锁"),
        IsLock = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_IsLock", "商品未解锁"),
        NoPurchaseChannels = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_NoPurchaseChannels", "暂无购买渠道"),
        
        --TheItemHasNotBe
        ConfirmResolveTip = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_ConfirmResolveTip", "由于制式装备券数量限制，领取后将分解为哈夫币，确定领取吗？"),
        TheItemHasNotBeenAdded = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_TheItemHasNotBeenAdded", "并未添加该道具"),
        NoExchangeChannel = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_NoExchangeChannel", "暂无兑换渠道"),
        ExchangeMoreFavorable = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_ExchangeMoreFavorable", "军需处兑换更优惠"),

        UsePlanBtnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_UsePlanBtnText", "使用方案"),
        BuyPriceBtnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_BuyPriceBtnText", "{currencyIconTxt}{buyPriceStr}"),
        BuyLimitedPriceBtnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_BuyLimitedPriceBtnText", "%s<customstyle color=\"Color_DarkNegative\">%s</>"),
        SoldOutText = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_SoldOutText", "<customstyle color=\"Color_DarkNegative\">已售罄</>"),
        WeaponChangedBtnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_WeaponChangedBtnText", "改装"),
        RepairBtnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_RepairBtnText", "修理"),
        ExchangeBtnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_ExchangeBtnText", "兑换"),
        RedColorText = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_RedColorText", "<customstyle color=\"Color_DarkNegative\">%s</>"),
        CurrencyNotEnoughTips = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_CurrencyNotEnoughTips", "%s不足，购买失败"),
        CantOperate = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_CantOperate", "未购买道具无法操作"),
        CantRepair = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_CantRepair", "该道具无法修理"),
        NoRepairRequired = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_NoRepairRequired", "满耐久度，无需修理"),
        CantExchange = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_CantExchange", "未购买道具无法交换"),
        CantPut = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_CantPut", "未购买道具不可放入容器中"),
        CantPutInContainer = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_CantPutInContainer", "不可放入未购买容器中"),
        CantOpOnPreset = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_CantOpOnPreset", "推荐状态下，不允许该操作"),
        PleaseEditThePlanBeforeSavingIt = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_PleaseEditThePlanBeforeSavingIt", "请编辑方案后再进行保存"),
        NotMatchSlot = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_NotMatchSlot", "类型不匹配"),
        NotSelectStyle = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_NotSelectStyle", "请选择配装风格"),
        SelectYouStyle = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_SelectYouStyle", "点击选择适合您的配装风格"),
        NoPlanLevel = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_NoPlanLevel", "未选择"),
        IsProcessing = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_IsProcessing", "使用中"),
        TextMaxCommonTip = NSLOCTEXT("ArmedForceModule","Lua_ArmForce_TextMaxCommonTip","输入文字内容超出上限"),
        SavedBtnStr = NSLOCTEXT("ArmedForceModule","Lua_ArmForce_SavedBtnStr","保存"),
        SchemeNameCannotBeEmpty = NSLOCTEXT("ArmedForceModule","Lua_ArmForce_SchemeNameCannotBeEmpty","方案名称不能为空!"),
        NewSchemeConfirmStr = NSLOCTEXT("ArmedForceModule","Lua_ArmForce_NewSchemeConfirmStr","是否确认覆盖<customstyle color=\"Color_Highlight02\"> “%s”</>为新方案？"),

        ArmEmptyList = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_ArmEmptyList", "兵种信息列表为空"),
        CurrentArm = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_CurrentArm", "当前兵种："),
        FitOtherArm = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_FitOtherArm", "适用兵种："),
        NotMatchArm = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_NotMatchArm", "兵种不匹配"),
        NotMatchArmBtnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_NotMatchArmBtnText", "兵种不匹配"),
        MatchArmTip = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_MatchArmTip", "<customstyle color=\"Color_Highlight01\">%s</>可用"),
        WeaponNotMatchArm = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_WeaponNotMatchArm", "无法装备，当前武器仅适合<BuyItem>%s</>兵种使用"),
        CarryWeaponNotMatchArm = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_CarryWeaponNotMatchArm", "当前武器仅适合<BuyItem>%s</>兵种使用，容器内无法携带"),
        AutoTakeOffWeaponNotMatchArm = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_AutoTakeOffWeaponNotMatchArm", "容器内无法携带仅适合<BuyItem>%s</>兵种使用的武器，已经自动卸下"),
        NotMatchArmWeaponInContainer = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_NotMatchArmWeaponInContainer", "容器内包含非兵种适用武器，请放回仓库再尝试战斗吧！"),
        PistolWeaponMatchAllArm = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_PistolWeaponMatchAllArm", "手枪不限兵种，成功装备<ItemHighlight>%s</>"),

        HasUnnecessaryItems = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_HasUnnecessaryItems", "容器中存在尚未转移的物资，请问是否前往处理？"),
        FightingAnyway = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_FightingAnyway", "果断战斗"),
        ToConfigure = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_ToConfigure", "前往处理"),
        NoMedicine = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_NoMedicine", "当前未携带治疗道具到槽位，建议补给后战斗~"),
        NoMedicineAndHasUnnecessaryItems = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_NoMedicineAndHasUnnecessaryItems", "当前未携带治疗道具到槽位，并且存在未转移的物资，请问是否前往处理？"),
        ConfirmBuy = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_ConfirmBuy", "是否确认支付%s购买缺失的装备？"),
        ConfirmBuyPart = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_ConfirmBuyPart", "是否确认支付%s购买部分缺失的装备？"),
        ConfirmEnterNotBuy = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_ConfirmEnterNotBuy", "当前货币余额无法购买任何缺失的装备。是否仍然确认？"),
        CannotOpPerk = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_CannotOpPerk", "未购买的装备不支持装配强化套件，请购买后操作"),
        OutfitCantOpPerk = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_OutfitCantOpPerk", "未应用的武器无法安装强化套件"),
        Cancel = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_Cancel","取消"),
        Confirm = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_Confirm","确认"),

        ItemCouldNotBePlaced = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_ItemCouldNotBePlaced", "此物品不可放入该容器中"),
        ItemCouldNotSwapWithSelf = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_ItemCouldNotSwapWithSelf", "无法和自己进行交换"),
        ContainerIsFull = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_ContainerIsFull", "该容器已满，不可放入"),
        TargetSlotOperationFail = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_TargetSlotOperationFail","操作目标槽位失败"),
        NoEquipCanTakeOff = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_NoEquipCanTakeOff","没有可以卸下的装备"),
        ScavEquipReadOnly = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_ScavEquipReadOnly","Scav装备不可以自定义"),
        KeyPresetReadOnly = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_KeyPresetReadOnly","钥匙包不可以自定义"),
        ConfigCarryItem = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_SupplyConfigCarryItem","配置成功，已放入"),
        PurchaseSuccess = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_PurchaseSuccess", "购买成功"),
        PleaseEquipYourGunsFirst = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_PleaseEquipYourGunsFirst","需要先装备枪械"),
        PackageSpaceLacking = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_PackageSpaceLacking","背包空间不足"),
        SafeBoxSpaceLacking = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_SafeBoxSpaceLacking","安全箱空间不足"),
        ShopItemListNotFound = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_ShopItemListNotFound","没有找到商品列表"),

        -- Assembly Warning
        ConfirmAssemblyBtnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_ConfirmAssemblyBtnText", "确认配装"),
        StillWantToContinueBtnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_StillWantToContinueBtnText", "仍要继续"),

        -- Equip Selection List
		DropDownBoxTitle = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_DropDownTitle", "预设方案"),
        SelectionEquipBtnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_SelectionEquipBtnText", "装备"),
        SelectionEquippedBtnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_SelectionEquippedBtnText", "已装备"),
        SelectionExchangeBtnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_SelectionExchangeBtnText", "交换"),
        SelectionUnEquipBtnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_SelectionUnEquipBtnText", "卸下"),
        ComplementaryPlanBtnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_ComplementaryPlanBtnText", "补齐方案"),
        ComplementaryPlanAndBuyBtnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_ComplementaryPlanAndBuyBtnText", "{priceIconText}{priceStr}"),
        ComplementaryPlanAndBuyLimitedPriceBtnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_ComplementaryPlanAndBuyLimitedPriceBtnText", "%s<customstyle color=\"Color_DarkNegative\">%s </>"),
        SelectionBuyAndEquipBtnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_SelectionBuyAndEquipBtnText", "购买并装备"),
        SelectionBuyWithCurrencyBtnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_SelectionBuyWithCurrencyBtnText", "%s 购买并装备"),
        SelectionUsePresetBtnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_SelectionUsePresetText", "确认"),
        SelectionRemovePresetBtnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_RemovePresetBtnText", "移除"),
        DetailViewRemovePresetBtnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_DetailViewRemovePresetBtnText", "移除"),
        SelectionDefaultPresetBtnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_DefaultPresetBtnText", "无法编辑"),
        SelectionCustomPresetBtnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_CustomPresetBtnText", "进入编辑"),
        SelectionSampleTitle = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_SelectionSampleTitle", "推荐物品"),
        SelectionDeposTitle = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_SelectionDeposTitle", "已拥有"),
        SelectionCustomCurPlanTitle = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_SelectionCustomCurPlanTitle", "当前方案"),
        SelectionCustomTitle = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_SelectionCustomTitle", "可选"),
        SelectionShopTitle = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_SelectionShopTitle", "军需处&交易行"),
        BluePrintTitle = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_BluePrintTitle", "蓝图预设"),
        SelectionFullTip = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_SelectionFullTip", "已达到槽位堆叠上限，携带更多请放入背包"),
        SelectionEmptyTip = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_SelectionEmptyTip", "已减少到最小数量"),
        SelectionSlotFullTip = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_SelectionSlotFullTip", "已达到当前容器上限，携带更多请放入其他容器"),
        SelectionDeposCostAllTip = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_SelectionDeposCostAllTip", "已达到拥有的最大数量，如需更多请解锁购买"),
        SelectionCarryBtnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_SelectionCarryBtnText", "%s携带"),
        SelectionConfirmBtnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_SelectionConfirmBtnText", "%s确认"),
        HasCarryOnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_HasCarryOnText", "已携带:"),
        CarryOnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_CarryOnText", "携带"),
        PutInText = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_PutInText", "放入"),
        SelectionApplyBtnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_SelectionApplyBtnText", "设置成预设"),
        BluePrintIsInCooldown = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_BluePrintIsInCooldown", "生产功能还在冷却时间中"),
        BluePrintCantProduce = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_BluePrintCantProduce", "缺少生产制造材料"),
        BluePrintProduceSucess = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_BluePrintProduceSucess", "%s 生产并装备成功"),
        ThePurchasedBlueprintIsAlreadyEquipped = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_ThePurchasedBlueprintIsAlreadyEquipped", "购买的“%s”已经装备"),
        BluePrintProduceAndEquip = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_ProduceAndEquip", "生产并装备"),
        BluePrintBuyAndEquip = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_BluePrintBuyAndEquip", "{currencyIconTxt}{PriceStr} 购买并装备"),
        PullingBlueprintDataWaiting = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_PullingBlueprintDataWaiting", "正在拉取蓝图数据，请稍后"),
        UnlockTheAuctionToBuyTips = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_UnlockTheAuctionToBuyTips", "因尚未解锁交易行，无法一键购买缺失配件"),
        UnlockTheAuctionToBuy = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_UnlockTheAuctionToBuy", "解锁交易行可购买"),
        BluePrintBuyAndEquipLimitedPrice = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_BluePrintBuyAndEquipLimitedPrice", "%s<customstyle color=\"Color_DarkNegative\">%s 购买并装备</>"),
        SoldOut = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_SoldOut", "<customstyle color=\"Color_Quality05\">售罄</>"),
        ShopItemSoldOut = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_ShopItemSoldOut", "该商品已售罄"),
        ConfirmPlay = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_ConfirmPlay", "确认出战"),
        WaitingToEnter = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_WaitingToEnter", "等待入局"),
        HeroLocked = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_HeroLocked", "暂未解锁"), 
        ChooseHero = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_ChooseHero", "选择干员"), 

        MeleeWeaponEquipped = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_MeleeWeaponEquipped", "<customstyle color=\"Color_Highlight01\">%s</>已装备"),
        MeleeWeaponIsLocked = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_MeleeWeaponIsLocked", "<customstyle color=\"Color_Highlight01\">%s</>未解锁"),




        -- MP
        SelectionMPApplyBtnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_SelectionMPApplyBtnText", "装备"),
        SelectionMPApplyWithAssmblyBtnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_SelectionMPApplyWithAssmblyBtnText", "应用并装备"),
        SelectionMPUsingBtnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_SelectionMPUsingBtnText", "正在使用"),
        CurrentMPArmTxt = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_CurrentMPArmTxt", "当前选择兵种：%s"),
        MPPagePresetName = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_MPPagePresetName","配装方案"),
        MPPresetNameMaxLengthTip = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_MPPresetNameMaxLength","配装方案名称超出{0}个字上限"),
        MPPresetNameMaxCommonTip = NSLOCTEXT("ArmedForceModule","MPPresetNameMaxCommonTip","配装方案名称超出上限"),
        LevelString =  NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_LevelString", "%s级"),

        --- for selection title
        MP_MainWeapon = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_MP_MainWeapon", "主武器"),
        MP_SecondaryWeapon = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_MP_SecondaryWeapon", "副武器"),
        MP_MeleeWeapon = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_MP_MeleeWeapon", "近战武器"),


        CantChooseLockedPageIdx = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_CantChooseLockedPageIdx", "当前页签尚未解锁哦"),
        UsingMPEquipTip = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_UsingMPEquip", "正在使用!"),
        CannotMPEquipDownloadTip = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_CannotMPEquipDownloadTip", "当前枪械中有未下载配件，不支持装备"),
        CannotMPApplyDownloadTip = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_CannotMPApplyDownloadTip", "当前方案中有未下载配件，不支持应用"),
        CannotMPJumpDownloadTip = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_CannotMPJumpDownloadTip", "当前枪械中有未下载配件，请先下载再查询获取途径"),

        CurrentDefaultPageTitle = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_CurrentDefaultPageTitle", "出战中"),
        ToSetDefaultPageTitle = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_ToSetDefaultPageTitle", "设为出战"),
        NotUnlockHeroTitle = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_NotUnlockHeroTitle", "干员未解锁"),
        TeleScopeValueText = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_TeleScopeValueText", "1~8倍"),

        --- confirm
        CheckConfirmEnterBattleTitle = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_CheckConfirmEnterBattleTitle", "携带药品不足"),
        CheckEnterBattleEmptySlotDes = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_CheckEnterBattleEmptySlotDes", "当前未携带治疗道具到槽位，建议补给后战斗~"),
        CheckEnterBattleNotEnoughDes = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_CheckEnterBattleNotEnoughDes", "携带治疗道具治疗量不足50，建议补给后战斗~"),
        CheckEnterSelection = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_CheckEnterSelection", "进行配置"),
        CheckEnterBattle = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_CheckEnterBattle", "果断战斗"),
        OutfitFinished = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_OutfitFinished","出战使用的装备已穿戴"),

        --- preset 
        SystemConfigReadOnly = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_SystemConfigReadOnly","系统预设无法编辑"),
        CarryMediHealAmount = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_CarryMediHealAmount", "药品治疗量:"),
        SavePresetSuccess = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_SavePresetSuccess", "方案已保存"),
        ItemNotSoldInShop = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_ItemNotSoldInShop", "这个道具商城暂时缺货"),
        BuyAndApplyBtnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_BuyAndApplyBtnText", "%s <ItemView_Default>购买并配置</>"),
        NotChoosePresetSave = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_NotChoosePresetSave","请先选择要存储的预设槽位"),
        PresetLockedText =  NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_PresetLockedText", "未解锁购买"),
        PreseNotLaunchedText =  NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_PreseNotLaunchedText", "商品未上架"),
        FakeContainerCanotUse = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_FakeContainerCanotUse", "未应用的容器无法携带物资"),
        ContainerSpaceLack = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_ContainerSpaceLack", "仓库容量不足，无法放入应用配装方案后容器内的多余道具"),
        FakeWeaponCantReform = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_FakeWeaponCantReform", "未应用的武器无法进行改装或保养"),
        AgreeDrugTipsBtnText = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_AgreeDrugTipsBtnText", "同意"),
        DepositoryIsFull = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_DepositoryIsFull", "仓库已满，无法放入换下的物品，请清理后再试"),
        ApplySchemeFailedByInventorySpaceLacking = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_ApplySchemeFailedByInventorySpaceLacking", "仓库空间不足，方案应用失败"),
        ApplySchemeFailed = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_ApplySchemeFailed", "配装方案使用失败"),
        ClickTooSoonPleaseWait = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_ClickTooSoonPleaseWait", "点击太快了，请稍后"),
        MissingWeaponReceiverRecommendationFailed = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_MissingWeaponReceiverRecommendationFailed", "武器机匣存在未解锁或售罄情况，推荐失败"),
        MissingWeaponParts = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_MissingWeaponParts", "武器配件存在未解锁或售罄情况"),
        PlanMissingParts = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_PlanMissingParts", "当前改装方案中存在未解锁配件"),
        PlanMissingSkinParts = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_PlanMissingSkinParts", "当前改装方案中存在皮肤专用配件"),
        PlanMissingTableParts = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_PlanMissingTableParts", "【Debug提示，非Shipping包可见】当前改装方案中存在没配置于等级解锁表的配件"),
        TheOutfitplanIsBeingImplemented = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_TheOutfitplanIsBeingImplemented", "配装方案正在执行中"),
        OutfitErrorStr = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_OutfitErrorStr", "%s，%s"),
        PriceChangesConfirmPlanPricesTips = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_PriceChangesConfirmPlanPricesTips", "价格变化，请确认方案价格"),

        PutBackFailedByInventorySpaceLacking = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_PutBackFailedByInventorySpaceLacking", "仓库空间不足，放回失败"),
        QuickOperationItemMoveFailed = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_QuickOperationItemMoveFailed", "道具移动失败"),
        WeaponTitle = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_WeaponTitle", "%s："),
        SlotTailTitle = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_SlotTailTitle", "{EquipPart}栏位"),
        NeedToEquipAWeaponFirst = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_NeedToEquipAWeaponFirst", "需要先装备一把枪械"),

        AutoLoadBulletToWeapon = NSLOCTEXT("ArmedForceModule","Lua_ArmForce_AutoLoadBulletToWeapon","已自动帮你把<customstyle color=\"Color_Highlight02\">{num}发</>子弹装进<customstyle color=\"Color_Highlight02\">{name}</>内"),


        QuickOperationCapacityTitle =
        {
            [ESlotType.BagContainer] = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_QuickOperationBagContainerCapacityTitle", "背包:%s/%s"),
            [ESlotType.ChestHangingContainer] = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_QuickOperationChestHangingContainerCapacityTitle", "胸挂:%s/%s"),
            [ESlotType.SafeBoxContainer] = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_QuickOperationSafeBoxContainerCapacityTitle", "安全箱:%s/%s"),
            [ESlotType.Pocket] = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_QuickOperationPocketCapacityTitle", "口袋:%s/%s"),
        },
        QuickOperationContainerTitle =
        {
            [ESlotType.BagContainer] = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_QuickOperationBagContainerTitle", "背包"),
            [ESlotType.ChestHangingContainer] = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_QuickOperationChestHangingContainerTitle", "胸挂"),
            [ESlotType.SafeBoxContainer] = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_QuickOperationSafeBoxContainerTitle", "安全箱"),
            [ESlotType.Pocket] = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_QuickOperationPocketTitle", "口袋"),
        },

        DrugEffectStr = 
        {
            [EDispensingMedicineType.HP] = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_DrugEffectStr_HP", "耐久度：%s/%s"),
            [EDispensingMedicineType.BUFF] = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_DrugEffectStr_BUFF", "可用次数：%s/%s"),
            [EDispensingMedicineType.Armor] = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_DrugEffectStr_Armor", "维修点数：%s/%s"),
            [EDispensingMedicineType.Strengthen] = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_DrugEffectStr_Strengthen","增强：%s/%s"),
        },

        DrugEffectStr = 
        {
            [EDispensingMedicineType.HP] = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_DrugEffectStr_HP", "耐久度：%s/%s"),
            [EDispensingMedicineType.BUFF] = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_DrugEffectStr_BUFF", "可用次数：%s/%s"),
            [EDispensingMedicineType.Armor] = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_DrugEffectStr_Armor", "维修点数：%s/%s"),
            [EDispensingMedicineType.Strengthen] = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_DrugEffectStr_Strengthen","增强：%s/%s"),
        },

        DrugOccupancyStr = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_DrugOccupancyStr", "占格数：%sx%s"),

        BulletDamage = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_BulletDamage", "子弹伤害：%s"),
        BulletPenetrateLevel = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_BulletPenetrateLevel", "穿甲等级：%s级"),
        ArmorDamageAttenuationLevel = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_ArmorDamageAttenuationLevel", "甲伤衰减水平：%s"),
        --- check
        NotCarryingMainWeapon = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_NotCarryingMainWeapon","未携带枪械"),
        NotCarryingMedicine = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_NotCarryingMedicine","未携带治疗道具"),
        NotEnoughCarryingMedicineHealingAmount = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_NotEnoughCarryingMedicineHealAmount","携带药品的治疗量不足"),
        LackOfTreatment = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_LackOfTreatment","缺少治疗%s类的药品"),
        UnableToResolveTheState = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_UnableToResolveTheState","未携带%s药品"),
        NotEquipped = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_NotEquipped","未装备%s"),
        NotEnoughDurability = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_NotEnoughDurability","%s耐久度不足"),
        StorageSpaceIsTight = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_StorageSpaceIsTight","身上的剩余携带空间不足%s%%"),
        CheckHasUnnecessaryItems = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_CheckHasUnnecessaryItems","安全箱内有道具未转移"),
        NotEnoughBullet = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_NotEnoughBullet","{BulletName}子弹携带不足{BulletNum}发"),
        EquipmentAllValueNotEnough = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_EquipmentAllValueNotEnough","装备总价值未达到地图要求"),
        NotWearingNightVisionEquipment = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_NotWearingNightVisionEquipment","未携带/穿戴任何含夜视功能的装备"),
        NotWearingThermalImagineOrFlashlightEquipment = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_NotWearingThermalImagineOrFlashlightEquipment","未携带/安装任何照明类型的装备"),
        SafeBoxExpiredStatus = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_SafeBoxExpiredStatus","安全箱已过期"),
        KeyChainExpiredStatus = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_KeyChainExpiredStatus","门禁卡已过期"),
        RentalVoucherDoNotMeetEntryRequirementsStr = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_RentalVoucherDoNotMeetEntryRequirementsStr","当前装备的制式装备券不满足入局要求"),
        ValueNumber = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_ValueNumber","{SlotNum}格"),
        ValuePercent = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_ValuePercent","%s%%"),
        NotConfirmOutfit = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_NotConfirmOutfit","方案未确认"),
        NotConfirmOutfitContent = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_NotConfirmOutfitContent","修改未确认，是否确认使用后再离开"),
        Leave = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_Leave","离开"),
        Back = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_Back","返回"),
        InsufficientDurability = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_InsufficientDurability","【%s】耐久度不足%s%%"),

        GoToWeaponAssembly = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_GoToWeaponAssembly", "改装"),
        CantGoToWeaponAssembly = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_CantGoToWeaponAssembly", "未解锁的武器无法改装"),
        CantGoToWeaponAssemblyNeedDownload = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_CantGoToWeaponAssemblyNeedDownload", "未下载资源的武器无法改装"),

        --- 如果不是原方案就预改装
        GoToWeaponPreAssembly = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_GoToWeaponPreAssembly", "改装"),
        GoToWeaponUpgrade = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_GoToWeaponUpgrade", "升级解锁"),
        GoToWeaponMaintain = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_GoToWeaponMaintain", "保养"),
        NeedToUpgradeComponents = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_NeedToUpgradeComponents", "部分配件需要通过升级解锁"),
        HadUpgradeComponents = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_HadUpgradeComponents", "部分配件有变更，需要应用"),

        BulletCarryNum = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_BulletCarryNum", "携带数量：%s/%s"),
        InMatching     = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_InMatching", "匹配中状态下，不可进行此操作"),
        InReadying     = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_InReadying", "准备中状态下，不可进行此操作"),
        InMatching_Rental     = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_InMatching_Rental", "匹配中状态下，不可进入物资券界面"),
        InReadying_Rental     = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_InReadying_Rental", "准备中状态下，不可取消物资券状态"),

        -- 租借
        EquipmentRentalStr     = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_EquipmentRentalStr", "制式套装使用中"),
        EquipmentRentalTitle     = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_EquipmentRentalTitle", "制式套装"),
        RentalVoucherNotEnough     = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_RentalVoucherNotEnough", "<customstyle color=\"Color_Highlight01\">%s</>数量不足"),
        RentalVoucherNotApply     = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_RentalVoucherNotApply", "<customstyle color=\"Color_Highlight01\">%s</>无法进入%s"),
        RentalDataIsUsingCanNotBeRefreshed     = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_RentalDataIsUsingCanNotBeRefreshed", "该方案正在使用中，无法刷新"),
        RentalViewRefreshTips_PC     = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_RentalViewRefreshTips_PC", "在这里您可以消耗不同的制式套装券获得不同风格的战斗套装，如果能在行动中成功撤离，这套装备将永久归属于你作为胜利的奖励，<customstyle color=\"Color_Highlight02\"> 每日0点自动刷新装备</>"),
        RentalViewRefreshTips_Mobile     = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_RentalViewRefreshTips_Mobile", "在这里您可以消耗不同的制式套装券获得不同风格的战斗套装，如果能在行动中成功撤离，这套装备将永久归属于你作为胜利的奖励"),
        EquipmentInRentalStatusCannotBeModifiedTips     = NSLOCTEXT("ArmedForceModule", "EquipmentInRentalStatusCannotBeModifiedTips", "制式套装使用状态下，配装不可修改"),
        RentalVoucherNotEnoughBtnStr     = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_RentalVoucherNotEnoughBtnStr", "%s <customstyle color=\"Color_DarkNegative\">%s</>"),
        RentalVoucherNotApplyBtnStr     = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_RentalVoucherNotApplyBtnStr", "%s %s"),
        RentalRefreshConfirmTips     = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_RentalRefreshConfirmTips", "刷新方案会花费%s哈夫币，确认刷新套装方案？"),
        RentalGeneratingPlaning     = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_RentalGeneratingPlaning", "方案正在生成中，请稍等"),
        RentalPresetPriceTxt = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_RentalPresetPriceTxt", "(价值 {priceIconText}{priceStr})"),
        RentalVocherExpirationSwitchConfirmation = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_RentalVocherExpirationSwitchConfirmation", "{Name1}已过期，切换到{Name2}后，<customstyle color=\"Color_Highlight01\">{Name3}将过期直接消失</>，不返还补发货币，是否确认切换？"),
        RentalVocherExpirationCancelConfirmation = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_RentalVocherExpirationCancelConfirmation", "{Name1}已过期，<customstyle color=\"Color_Highlight01\">取消使用后将过期直接消失</>，不返还补发货币，是否确认取消使用？"),
        UnavailableMaskStr = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_UnavailableMaskStr", "不可用"),
        RentalVoucherTitle_Unavailable = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_RentalVoucherTitle_Unavailable", "{Name}({Count})"),
        RentalVoucherTitle_Available = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_RentalVoucherTitle_Available", "{Name}<customstyle color=\"Color_Highlight02\">({Count})</>"),
        RentalVoucherNum = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_RentalVoucherNum", "{RichIconTxt}<customstyle color=\"C001\">{CurNum}</><customstyle color=\"C002\">/{MaxNum}</>"),
        
        LastPresetTitle = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_LastPresetTitle", "上次配装"),
        InsufficientCurrencyToUseThisOutfit = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_InsufficientCurrencyToUseThisOutfit", "货币不足，无法使用该配装方案"),
        SecondaryWeaponAreNotConfigurable = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_SecondaryWeaponAreNotConfigurable", "近战武器不可配置"),
        EquipValueLacked = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_EquipValueLacked", "未满足进入需求"),
        RequiredValue = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_RequiredValue", "带入装备价值需求："),
        CurrentValue = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_CurrentValue", "当前带入装备价值："),
        RequiredValueNum = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_RequiredValueNum", "<dfmrichtext type=\"img\" width=\"46\" height=\"46\" id=\"bankNote\"/>%s"),
        CurrentValueNum = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_CurrentValueNum", "<dfmrichtext type=\"img\" width=\"46\" height=\"46\" id=\"bankNote\"/>%s"),
        GoAssemble = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_GoAssemble", "前往配装"),
        MapNeeded = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForceConfig_MapNeeded", "带入要求：{currencyIconTxt}{ValueStr}"),
        MapNeededError = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForceConfig_MapNeededError", "带入要求：%s<customstyle color=\"Color_DarkNegative\">%s</>" ),
        HDMapNeeded = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForceConfig_HDMapNeeded", "{currencyIconTxt}{ValueStr}"),
        HDMapNeededError = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForceConfig_HDMapNeededError", "%s<customstyle color=\"Color_DarkNegative\">%s</>" ),
        MemberValueLacked = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForceConfig_MemberValueLacked", "%s的配装价值不满足地图要求"),
        UnlockTipsPriceSource = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForceConfig_UnlockTipsPriceSource", "价格来源：%s"),
        PriceSourceTips = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForceConfig_PriceSourceTips", "系统会自动从军需处和交易行两个来源获取当前最低售价购买。显示上略有浮动，实际购买时会按照当前最低价购买。"),
        EquipmentValueExplained = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForceConfig_EquipmentValueExplained", "装备价值只计算头盔、防弹衣、背包、胸挂、三把武器的价值。不计算口袋内道具、胸挂内道具、背包内道具和安全箱内道具的价值。"),
        EquipmentValueExplainedAbnormal = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForceConfig_EquipmentValueExplainedAbnormal", "执行%s行动时，装备要求%s%s"),
        ModeDifficulty = {
            [-1] = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_ModeDifficulty_None","无"),
            [0] = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_ModeDifficulty_Default","默认"),
            [1] = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_ModeDifficulty_Common","常规"),
            [2] = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_ModeDifficulty_Confidential","机密"),
            [3] = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_ModeDifficulty_TopSecret","绝密"),
        },
        PriceSoure = {
            [CheapBuyChannel.Channel_Invalid] = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_PriceSoureInvalidTitle","已售罄"),
            [CheapBuyChannel.Channel_Mall] = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_PriceSoureMallTitle","军需处"),
            [CheapBuyChannel.Channel_Auction] = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_PriceSoureAuctionTitle","交易行"),
        },

        --- MedicineType to string
        MedicineTypeString = {
            [EDispensingMedicineType.HP] = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_MedicineTypeString1","伤害救治"),
            [EDispensingMedicineType.BUFF] = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_MedicineTypeString2","异常清除"),
            [EDispensingMedicineType.Armor] = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_MedicineTypeString3","护甲修复"),
            [EDispensingMedicineType.Strengthen] = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_MedicineTypeString4","增强药剂"),
        },

        PerTime = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_PerTime", "%s(%s/次)"),
        StockOfWareHouse = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_StockOfWareHouse", "仓库存量："),

        NotUnlockSelectionText = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_NotUnlockSelectionText", "战场等级<customstyle color=\"Color_Highlight01\">{0}级</>解锁"),
        NotUnlockSelectionDepos = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_NotUnlockSelectionDepos", "全面战场仓库未解锁"),
        NotUnlockTip = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_NotUnlockTip", "尚未在全面战场模式下解锁该武器"),
        NotUnlockSelectionActivity = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_NotUnlockSelectionActivity", "活动获取"),
        NotUnlockSelectionArmory = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_NotUnlockSelectionArmory", "军械库获取"),
        NotUnlockNoneType = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_NotUnlockNoneType", "未解锁"),
        NotUnlock2Jump = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_NotUnlock2Jump", "前往获取"),
        NotUnlockNoWays2Get = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_NotUnlockNoWays2Get", "暂无获取途径"),
        
        
        CurLvText = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_CurLvText", "等级{0}"),
        MaxLvText = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_MaxLvText", "满级"),

        Weight = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_Weight", "%.1fkg"),

        SpaceInsufficientUnableToAdd = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_SpaceInsufficientUnableToAdd", "<customstyle color=\"Color_LightNegative\">空间不足</>，无法添加"),

        SpaceInsufficientUnableToUnloadBullets = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_SpaceInsufficientUnableToUnloadBullets", "<customstyle color=\"Color_LightNegative\">空间不足</>，无法卸下子弹"),

        AlreadyReturnedToWarehouse = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_AlreadyReturnedToWarehouse", "<customstyle color=\"Color_Highlight02\">%s</>已经放回仓库，点击确定后生效"),

        ShopItemUnlock = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_ShopItemUnlock", "{0}<customstyle color=\"Color_Highlight01\">{1}级</>解锁"),
        
        ShopItemNoPurchase = NSLOCTEXT("ArmedForceModule", "Lua_ArmedForce_ShopItemNoPurchase", "无直购商品"),

        NoPresetPlan = NSLOCTEXT("ArmedForceModule","Lua_NoPresetPlan","无方案"),

        AbnormalHoveredBulletTips = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_AbnormalHoveredBulletTips", "%s子弹数量偏少"),
        UnlockShopPurchases = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_UnlockShopPurchases", "%s军需处购买"),

        MedicineBox = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_MedicineBox", "携带药品"),
        AmmunitionBox = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_AmmunitionBox", "携带弹药"),
        NotAssembled = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_NotAssembled", "未装配"),

        NoConfig = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_NoConfig", "缺失配置"),


        ShowSkinStr = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_ShowSkinStr", "显示皮肤"),
        HeroDataFailed = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_HeroDataFailed", "角色{0}本地数据读取失败！请检查是否服务器更新后与包内数据不匹配，或重置账号！"),
        NumFormat = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_NumFormat", "{countNum}/{maxNum}"),

        ArmorGrade = {
            [1] = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_BrandNNew", "全新"),
            [2] = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_AlmostNew", "几乎全新"),
            [3] = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_Damaged", "破损"),
        },
        PleaseSelectAgain = NSLOCTEXT("ArmedForceModule", "Lua_ArmForce_PleaseSelectAgain", "该档次已售罄，请重新选择"),
    },
  
    FightingStyleIconResList = {
        "PaperSprite'/Game/UI/UIAtlas/System/Equipment/BakedSprite/Equimpent_FightingStyle_De_04.Equimpent_FightingStyle_De_04'",
        "PaperSprite'/Game/UI/UIAtlas/System/Equipment/BakedSprite/Equimpent_FightingStyle_De_05.Equimpent_FightingStyle_De_05'",
        "PaperSprite'/Game/UI/UIAtlas/System/Equipment/BakedSprite/Equimpent_FightingStyle_De_06.Equimpent_FightingStyle_De_06'",
    },
    FightingStyleIconList = {
        [0] = "PaperSprite'/Game/UI/UIAtlas/System/Equipment/BakedSprite/Equimpent_Main_Icon_0101.Equimpent_Main_Icon_0101'",
        [1] = "PaperSprite'/Game/UI/UIAtlas/System/Equipment/BakedSprite/Equimpent_Main_Icon_0104.Equimpent_Main_Icon_0104'",
        [2] = "PaperSprite'/Game/UI/UIAtlas/System/Equipment/BakedSprite/Equimpent_Main_Icon_0103.Equimpent_Main_Icon_0103'",
        [3] = "PaperSprite'/Game/UI/UIAtlas/System/Equipment/BakedSprite/Equimpent_Main_Icon_0105.Equimpent_Main_Icon_0105'",
    },
    ChangeHeroIconList = {
        "PaperSprite'/Game/UI/UIAtlas/System/Equipment/BakedSprite/Equimpent_Main_Icon_0002.Equimpent_Main_Icon_0002'",
        "PaperSprite'/Game/UI/UIAtlas/System/Equipment/BakedSprite/Equimpent_Main_Icon_0003.Equimpent_Main_Icon_0003'",
        "PaperSprite'/Game/UI/UIAtlas/System/Equipment/BakedSprite/Equimpent_Main_Icon_0004.Equimpent_Main_Icon_0004'",
        "PaperSprite'/Game/UI/UIAtlas/System/Equipment/BakedSprite/Equimpent_Main_Icon_0001.Equimpent_Main_Icon_0001'"
    },

    UnlockIcon = "PaperSprite'/Game/UI/UIAtlas/System/Assembly/BakedSprite/Assembly_Btn_01.Assembly_Btn_01'",

    --- MP武器tab
    TabWeaponTypeList = {
        [1]   = 0,
        [2] = ItemConfig.EWeaponItemType.Rifle,
        [3] = ItemConfig.EWeaponItemType.Submachine,
        [4] = ItemConfig.EWeaponItemType.Shotgun,
        [5] = ItemConfig.EWeaponItemType.LightMachine,
        [6] = ItemConfig.EWeaponItemType.PrecisionShootingRifle,
        [7] = ItemConfig.EWeaponItemType.Sniper,
        [8] = ItemConfig.EWeaponItemType.Pistol,
        [9] = ItemConfig.EWeaponItemType.Universal,
    },

    RentalImgPathList  = {
        "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Rental_Icon_01.CommonHall_Rental_Icon_01'",
        "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Rental_Icon_02.CommonHall_Rental_Icon_02'",
        "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Rental_Icon_03.CommonHall_Rental_Icon_03'",
        "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Rental_Icon_04.CommonHall_Rental_Icon_04'",
        "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Rental_Icon_05.CommonHall_Rental_Icon_05'"
    },

    WeaponImgSizeListHD = {
        FVector2D(36, 36),
        FVector2D(58, 48),
        FVector2D(58, 48),
        FVector2D(58, 48),
        FVector2D(58, 48),
        FVector2D(58, 48),
        FVector2D(58, 48),
        FVector2D(36, 36),
        FVector2D(58, 48),
    },


    CarryTargetIconResList = {
        [ESlotType.BagContainer] = "PaperSprite'/Game/UI/UIAtlas/System/Equipment/BakedSprite/Equipment_SelectMode_Icon_0001.Equipment_SelectMode_Icon_0001'",
        [ESlotType.ChestHangingContainer] = "PaperSprite'/Game/UI/UIAtlas/System/Equipment/BakedSprite/Equipment_SelectMode_Icon_0002.Equipment_SelectMode_Icon_0002'",
        [ESlotType.SafeBoxContainer] = "PaperSprite'/Game/UI/UIAtlas/System/Equipment/BakedSprite/Equipment_SelectMode_Icon_0003.Equipment_SelectMode_Icon_0003'",
    },
    CheckcontainerSlotTypeList = {
        ESlotType.BagSpaceContainer,
        ESlotType.SafeBoxContainer
    },
    BuffType2Id={
        Bleeding = 1101,
        Bleedingkiller = 1102,
        FiringBleeding = 1103,
        ArmFracture = 1200,
        LegFracture = 1201,
        FractureKiller = 1204,
        FracturePassiveDamage = 1210,
        Pain = 1301,
        TemporaryPainkiller = 1501,
        KeepHealth = 90000,
        InfinitePeriodHeal = 2601,
        ImpendingDeathHurt = 3001,
    },

    SelectResList = {
        NotSelectedBG = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Bg_09.Common_ItemProp_Bg_09'",
        SelecedBG = "PaperSprite'/Game/UI/UIAtlas/System/Equipment/BakedSprite/Equipment_SelectMode_Bg_02.Equipment_SelectMode_Bg_02'",

        MediDurabilityIcon = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_De_Heart.Common_De_Heart'",
        EquipDurabilityIcon = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0006.Common_ItemProp_Icon_0006'",
    },

    AllIcon = {
        UpgradIconPath = "PaperSprite'/Game/UI/UIAtlas/System/Equipment/BakedSprite/Equimpent_Up_Icon_0003.Equimpent_Up_Icon_0003'",
    },
    
    EUseItemFunctionType =
    {
        [0] = "None",
        [1] = "HP",
        [2] = "Armor",
        [3] = "Health",
        [4] = "BUFF",
        [5] = "Bleeding",   -- 流血了
        [6] = "Fracture",	-- 骨折了
    },

    MP_PRESET_NAME_MAX_LENGTH = 14,
}

-- 物资券
ArmedForceConfig.RentalConsumableID = {
    32330000001,
    32330000002,
    32330000003,
    32330000004,
}

-- 租借系统tab
ArmedForceConfig.RentalTabTxtList = {
    [ArmedForceConfig.RentalConsumableID[1]] = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_Rookie","新兵"),
    [ArmedForceConfig.RentalConsumableID[2]] = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_Veteran","标准"),
    [ArmedForceConfig.RentalConsumableID[3]] = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_Elite","精锐"),
    [ArmedForceConfig.RentalConsumableID[4]] = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_Special","特种"),
    [0] = NSLOCTEXT("ArmedForceModule","Lua_ArmedForce_Custom","定制"),
}

---检查配置异常枚举类型(租赁状态下)
ArmedForceConfig.ERentalCheckAbnormalType = {
    ArmedForceConfig.EAbnormalType.HasUnnecessaryItems,            --保险箱内有道具未转移
    ArmedForceConfig.EAbnormalType.EquipmentAllValueNotEnough,     -- 装备总价值未达到地图要求
    ArmedForceConfig.EAbnormalType.SafeBoxExpiredStatus,           -- 安全箱过期
    ArmedForceConfig.EAbnormalType.KeyChainExpiredStatus,         -- 门禁卡过期
    ArmedForceConfig.EAbnormalType.RentalVoucherDoNotMeetEntryRequirements,         -- 物资券是否满足入局要求
}

ArmedForceConfig.MapArmId2Name = {
    [EArmedForceId.Assault] = ArmedForceConfig.armForceTable['1'].Name, -- 突击
    [EArmedForceId.Support] = ArmedForceConfig.armForceTable['2'].Name, --支援
    [EArmedForceId.Project] = ArmedForceConfig.armForceTable['3'].Name, --工程
    [EArmedForceId.Recon] = ArmedForceConfig.armForceTable['4'].Name, --侦查
}

ArmedForceConfig.MapArmId2Icon = {
    [EArmedForceId.Assault] = ArmedForceConfig.armForceTable['1'].Icon,
    [EArmedForceId.Support] = ArmedForceConfig.armForceTable['2'].Icon,
    [EArmedForceId.Project] = ArmedForceConfig.armForceTable['3'].Icon,
    [EArmedForceId.Recon] = ArmedForceConfig.armForceTable['4'].Icon,
}

ArmedForceConfig.MapArmId2ArmedHeroIcon = {
    [EArmedForceId.Assault] = "PaperSprite'/Game/UI/UIAtlas/System/Equipment/BakedSprite/Equimpent_Main_Icon_01.Equimpent_Main_Icon_01'",
    [EArmedForceId.Support] = "PaperSprite'/Game/UI/UIAtlas/System/Equipment/BakedSprite/Equimpent_Main_Icon_02.Equimpent_Main_Icon_02'",
    [EArmedForceId.Project] = "PaperSprite'/Game/UI/UIAtlas/System/Equipment/BakedSprite/Equimpent_Main_Icon_02.Equimpent_Main_Icon_03'",
    [EArmedForceId.Recon] = "PaperSprite'/Game/UI/UIAtlas/System/Equipment/BakedSprite/Equimpent_Main_Icon_02.Equimpent_Main_Icon_04'",
}

ArmedForceConfig.SlotType2Type = {
    [ESlotType.MainWeaponLeft] = {mainType = EItemType.Receiver},
    [ESlotType.MainWeaponRight] = {mainType = EItemType.Receiver},
    [ESlotType.Pistrol] = {mainType = EItemType.Receiver, subType = ItemConfig.EWeaponItemType.Pistol},
    [ESlotType.Helmet] = {mainType = EItemType.Equipment, subType = EEquipmentType.Helmet},
    [ESlotType.BreastPlate] = {mainType = EItemType.Equipment, subType = EEquipmentType.BreastPlate},
    [ESlotType.ChestHanging] = {mainType = EItemType.Equipment, subType = EEquipmentType.ChestHanging},
    [ESlotType.Bag] = {mainType = EItemType.Equipment, subType = EEquipmentType.Bag},
    [ESlotType.BulletLeft] = {mainType = EItemType.Bullet},
    [ESlotType.BulletRight] = {mainType = EItemType.Bullet},
    [ESlotType.Medicine] = {mainType = EItemType.Medicine},
}


ArmedForceConfig.EAssemblyWarehouseIVPoolType = {
    Preset = 1,                 -- 配装方案
    QuickOperation = 2,         -- 快速配置
    Rental = 3,                 -- 制式套装
}

ArmedForceConfig.GMPickHeroExtraTime = 0 -- 用于gm调试的，本地延长客户端选人时间
ArmedForceConfig.GMPickHeroRepeatedHeroNum = 1 -- 复制英雄，用于调试英雄栏超框时滚动吸附
ArmedForceConfig.GMPickHeroAllSameModel = 0 -- 用于editor下把队友全设置成和主角一样

return ArmedForceConfig