----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMQuest)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class QuestModule : ModuleBase
local QuestModule = class("QuestModule", require("DFM.YxFramework.Managers.Module.ModuleBase"))
local QuestLogic = require "DFM.Business.Module.QuestModule.QuestLogic"

function QuestModule:Ctor()
end

function QuestModule:OnInitModule()
    QuestLogic.AddEventListener()
end

function QuestModule:OnLoadModule()
end

function QuestModule:OnDestroyModule()
    -- self:CloseQuestMainPanel()
    self:CloseQuestTraceView()
    QuestLogic.RemoveEventListener()
    if self._timerHandle then
        self._timerHandle:Release()
        self._timerHandle = nil
    end
end

---@param gameFlowType EGameFlowStageType
function QuestModule:OnGameFlowChangeLeave(gameFlowType)
    if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then
        self:CloseQuestTraceView()
        if self._timerHandle then
            self._timerHandle:Release()
            self._timerHandle = nil
        end
    end

    Module.Quest.Field:ResetQuestUpdateTipsHandle()
end

---@param gameFlowType EGameFlowStageType
function QuestModule:OnGameFlowChangeEnter(gameFlowType)
    --- 游戏主流程切换至大厅
    if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then
        QuestLogic._UpdateQuestTips()
        if self._timerHandle then
            self._timerHandle:Release()
            self._timerHandle = nil
        end
    end
end

function QuestModule:StartSeasonCollectorRefresh()
    if self._timerHandle then
        self._timerHandle:Release()
        self._timerHandle = nil
    end
    if Server.QuestServer._questCollectorData then
        local finishCount, maxCount = Server.QuestServer._questCollectorData:GetCollectorProgress()
        if finishCount < maxCount then
            self._timerHandle = Timer:NewIns(60, 0)
            self._timerHandle:AddListener(self.OnRefrshCollector, self)
            self._timerHandle:Start()
        end
    end
  
end

function QuestModule:CloseSeasonCollectorTimer()
    if self._timerHandle then
        self._timerHandle:Release()
        self._timerHandle = nil
    end
end

function QuestModule:OnRefrshCollector()
    Module.Quest.Config.evtQuestSeasonCollectorTimeRefresh:Invoke()
end

---------------private api--------------------

--------------evt listener func------------------

---------------public api--------------------
-- 打开SOL任务主界面
-- questType:选中任务类型（主线/支线），默认选择主线
function QuestModule:OpenSOLQuestMainPanel(questType)
    local mainHandle = Facade.UIManager:AsyncShowUI(UIName2ID.QuestMainPanel, nil, nil, questType)
    Module.Quest.Field:SetQuestMainPanelHandle(mainHandle)
end

function QuestModule:CloseSOLQuestMainPanel()
    local mainHandle = Module.Quest.Field:GetQuestMainPanelHandle()
    if mainHandle then
        Facade.UIManager:CloseUIByHandle(mainHandle)
        Module.Quest.Field:SetQuestMainPanelHandle(nil)
    end
end

-- 打开大世界任务提交物品界面
function QuestModule:TryOpenBigWorldMissionTurnInItemPanel(iteminfo)
    local UMissionTurnInItemGameplayHelper = import "MissionTurnInItemGameplayHelper"
    local temArray = UMissionTurnInItemGameplayHelper.GetTurnInItemInfo(iteminfo, itemArray)
    local hasItem = false
    for _, itemInfo in pairs(itemArray) do
        if itemInfo.hasNum > 0 then
            hasItem = true
            break
        end
    end

    if hasItem == false then
        Module.CommonTips:ShowSimpleTip(Module.Quest.Config.Loc.MissionNoItems, 3.0)
    else
        Module.Quest:OpenBigWorldMissionTurnInItemPanel(iteminfo)
    end
end

-- 打开大世界任务提交物品界面
function QuestModule:OpenBigWorldMissionTurnInItemPanel(iteminfo)
    if self.Field.bigWorldMissionTurnInItemPanel == nil then
        self.Field.bigWorldMissionTurnInItemPanel =
            Facade.UIManager:AsyncShowUI(UIName2ID.MissionTurnInItemPanel, nil, nil, iteminfo)
    end
end

-- 关闭大世界任务界面
function QuestModule:CloseMissionTurnInItemPanel()
    if self.Field.bigWorldMissionTurnInItemPanel then
        Facade.UIManager:CloseUIByHandle(self.Field.bigWorldMissionTurnInItemPanel)
        self.Field.bigWorldMissionTurnInItemPanel = nil
    end
end

-- 打开大世界任务主界面
-- questType:选中任务类型（主线/支线），默认选择主线
function QuestModule:OpenBigWorldQuestMainPanel(questType)
    self.Field.bigWorldQuestMainHandle =
        Facade.UIManager:AsyncShowUI(UIName2ID.BigWorldQuestMainPanel, nil, nil, questType)
end

-- 关闭大世界任务界面
function QuestModule:CloseBigWorldQuestMainPanel()
    if self.Field.bigWorldQuestMainHandle then
        Facade.UIManager:CloseUIByHandle(self.Field.bigWorldQuestMainHandle)
        self.Field.bigWorldQuestMainHandle = nil
    end
end

-- 打开任务追踪界面
-- questInfo:追踪任务信息
function QuestModule:OpenQuestTraceView(questInfo)
    self.Field.traceViewHandle = Facade.UIManager:AsyncShowUI(UIName2ID.QuestTraceView, nil, nil, questInfo)
end

function QuestModule:CloseQuestTraceView()
    if self.Field.traceViewHandle then
        Facade.UIManager:CloseUIByHandle(self.Field.traceViewHandle)
        self.Field.traceViewHandle = nil
    end
end

-- 打开CG播放界面
function QuestModule:OpenCGPlayPanel(cgPath, fPlayEndCallback)
    self.Field.cgPlayPanelHandle =
        Facade.UIManager:AsyncShowUI(UIName2ID.CGPlayPanel, nil, nil, cgPath, fPlayEndCallback)
end

function QuestModule:CloseCGPlayPanel()
    if self.Field.cgPlayPanelHandle then
        Facade.UIManager:CloseUIByHandle(self.Field.cgPlayPanelHandle)
        self.Field.cgPlayPanelHandle = nil
    end
end

-- 打开NPC个人信息界面
function QuestModule:OpenNPCRolfInfoPanel(npcId)
    Facade.UIManager:AsyncShowUI(UIName2ID.NPCRoleInfoPanel, nil, nil, npcId)
end

-- 打开任务线界面
function QuestModule:OpenSOLQuestLinePanel(questLineInfo, jumpToQuestInfo)
    return QuestLogic.TryOpenSOLQuestLinePanelProcess(questLineInfo, jumpToQuestInfo)
end

function QuestModule:OpenSeasonQuestDetailView()
    local lineInfo = Server.QuestServer:GetQuestLineInfoById(1008)
    if lineInfo then
        Module.Quest:OpenSOLQuestDetailView(lineInfo, nil)
    end
end

-- 打开任务线界面
-- questInfo:追踪任务信息
function QuestModule:OpenSOLQuestDetailView(questLineInfo, questInfo)
    if questLineInfo then
        local detailPanelHandle = nil
        detailPanelHandle = Facade.UIManager:AsyncShowUI(UIName2ID.QuestDetailView, nil, nil, questLineInfo, questInfo)
        Module.Quest.Field:SetQuestDetailViewHandle(detailPanelHandle)
    end
end

function QuestModule:CloseSOLQuestDetailView()
    local detailPanelHandle = Module.Quest.Field:GetQuestDetailViewHandle()
    if detailPanelHandle then
        Facade.UIManager:CloseUIByHandle(detailPanelHandle)
        Module.Quest.Field:SetQuestDetailViewHandle(nil)
    end
end

--------------------------------------------------------------------------
--- Public 跳转页面 API
--------------------------------------------------------------------------
function QuestModule:Jump()
    local openUINavIdList = {} -- 跳转接口返回 按顺序实际需要打开/重新显示的所有界面id
    self:OpenSOLQuestMainPanel()
    table.insert(openUINavIdList, UIName2ID.QuestMainPanel)
    return openUINavIdList
end

function QuestModule:JumpToQuestByLineId(lineId)
    local questLineInfo = Server.QuestServer:GetQuestLineInfoById(lineId)

    if questLineInfo then
        Module.Quest:OpenSOLQuestDetailView(questLineInfo)
    else
        Module.Quest:Jump()
    end
end

function QuestModule:SetSubmitObjectiveId(submitObjId)
    self.Field:SetSubmitObjId(submitObjId)
end

function QuestModule:GetCurrentSubmitQuestId()
    return self.Field:GetSubmitQuestId()
end

function QuestModule:GetCurrentSubmitObjId()
    return self.Field:GetSubmitObjId()
end

function QuestModule:GetCur3ItemSubmitInfos(questInfo)
    return QuestLogic.Get3ItemSubmitInfos(questInfo)
end

-- 改枪台获取提交武器的任务目标的信息
function QuestModule:GetRelatedSubmitQuestInfoByWeaponId(itemId)
    -- 现在一把武器只对一个任务，不会出现对应多个任务
    local allUnderwayQuest = Server.QuestServer:GetAllSOLUnderwayQuest()
    for _, questInfo in ipairs(allUnderwayQuest) do
        for _, objective in ipairs(questInfo:GetSubmitObjectives()) do
            if not objective.bIsFinsih and tonumber(objective.param1) == itemId then
                return true, questInfo.name, objective:GetQuestObjectDesc(), objective.listParam, objective
            end
        end
    end
    return false
end

-- 改枪台获取提交武器的任务目标的信息
function QuestModule:GetRelatedSubmitQuestInfoByMissionIDAndWeaponID(missionID, itemId)
    -- 现在一把武器只对一个任务，不会出现对应多个任务
    local allUnderwayQuest = Server.QuestServer:GetAllSOLUnderwayQuest()
    for _, questInfo in ipairs(allUnderwayQuest) do
        if questInfo.id == missionID then
            for _, objective in ipairs(questInfo:GetSubmitObjectives()) do
                if not objective.bIsFinsih and tonumber(objective.param1) == itemId then
                    return true, questInfo.name, objective:GetQuestObjectDesc(), objective.listParam, objective
                end
            end
        end
    end
    return false
end

function QuestModule:CachingTrackingObjective(mapId, objectiveId, bTracking)
    self.Field:CachingTrackingObjective(mapId, objectiveId, bTracking)
end

function QuestModule:PopNewlyOpenQuestLine()
    return self.Field:PopNewlyOpenQuestLine()
end

function QuestModule:ResetNewlyOpenQuestLine()
    return self.Field:ResetNewlyOpenQuestLine()
end

function QuestModule:QuestLineCreateEndToJump()
    return QuestLogic.QuestLineCreateEndToJump()
end

function QuestModule:ShowUpdateQuestTips()
    QuestLogic._UpdateQuestTips()
end

function QuestModule:GetSubmitQuestId()
    return Module.Quest.Field:GetSubmitQuestId()
end

function QuestModule:SetToDetailFromSource(type)
    self.Field:SetToDetailFromSource(type)
end

function QuestModule:GetToDetailFromSource()
    return self.Field:GetToDetailFromSource()
end

----------------- 赛季任务 -----------------

function QuestModule:OpenSeasonalQuestTutorial(type, firstTimeKey)

    if firstTimeKey then
        local lineInfo = Server.QuestServer:GetCurrentSeasonLine()
        local tipsRecordServer = Server.TipsRecordServer
        local fullKey = tonumber(firstTimeKey..lineInfo.seasonLineId) 
        local bNotFirstOpenUI = tipsRecordServer:GetBoolean(fullKey)

        if not bNotFirstOpenUI then
            tipsRecordServer:SetBoolean(fullKey, true)
            Facade.UIManager:AsyncShowUI(UIName2ID.QuestSeasonalTutorial, nil, self, type)    
        end
    else
        Facade.UIManager:AsyncShowUI(UIName2ID.QuestSeasonalTutorial, nil, self, type)    
    end

end

function QuestModule:OpenSeasonMainPanel()
    local mainHandle = Facade.UIManager:AsyncShowUI(UIName2ID.QuestSeasonMainPanel)
    Module.Quest.Field:SetQuestSeasonMainPanelHandle(mainHandle)
end

function QuestModule:CloseSeasonMainPanel()
    local mainHandle = Module.Quest.Field:GetQuestSeasonMainPanelHandle()
    if mainHandle then
        Facade.UIManager:CloseUIByHandle(mainHandle)
        Module.Quest.Field:SetQuestSeasonMainPanelHandle(nil)
    end
end

function QuestModule:OpenSeasonListPanel(lineInfo, stageId, questId)
    local mainHandle = Facade.UIManager:AsyncShowUI(UIName2ID.QuestSeasonListPanel, nil, nil, lineInfo, stageId, questId)
    Module.Quest.Field:SetQuestSeasonListPanelHandle(mainHandle)
end

function QuestModule:CloseSeasonListPanel()
    local mainHandle = Module.Quest.Field:GetQuestSeasonListPanelHandle()
    if mainHandle then
        Facade.UIManager:CloseUIByHandle(mainHandle)
        Module.Quest.Field:SetQuestSeasonListPanelHandle(nil)
    end
end

function QuestModule:OpenQuestSeasonLinePanel(stageInfo, jumpToQuestInfo)
    return QuestLogic.TryOpenSOLQuestSeasonLinePanelProcess(stageInfo, jumpToQuestInfo)
end

function QuestModule:OpenQuestSeasonCollector()
    Facade.UIManager:AsyncShowUI(UIName2ID.QuestSeasonCollectionPanel)
end

function QuestModule:OpenQuestSeasonFact()
    Facade.UIManager:AsyncShowUI(UIName2ID.QuestSeasonFactContractPanel)
end

function QuestModule:OpenSeasonFactDetail(factId, questInfo)
    Facade.UIManager:AsyncShowUI(UIName2ID.QuestSeasonFactContractDetail, nil, nil, factId, questInfo)
end

return QuestModule
