----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGuide)
----- LOG FUNCTION AUTO GENERATE END -----------



local GuideConfig = require "DFM.Business.Module.GuideModule.GuideConfig"
local UGPInputHelper = import "GPInputHelper"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"

---@class GuideMainUI : LuaUIBaseView
local GuideMainUI = ui("GuideMainUI")

local function log(...)
    loginfo("[GuideMainUI]", ...)
end

function GuideMainUI:Ctor()
    self._wtPanel = self:Wnd("_wtPanel", UIWidgetBase)
    self._wtSwallowImg = self:Wnd("_wtSwallowImg", UIWidgetBase)
    self._timer = nil
    self._bSwallowTouch = true
    self._navWidgetCount = 0
end

--==================================================
--region Life function
function GuideMainUI:OnOpen()
    self:AddLuaEvent(GuideConfig.EGuideEvent.evtGuidePause, self._OnPause, self)
    self:AddLuaEvent(GuideConfig.EGuideEvent.evtGuideRestart, self._OnRestart, self)
    self:AddLuaEvent(GuideConfig.EGuideEvent.evtGuideNavChanged, self._OnNavChangedNotify, self)


    -- self:_CreateNavGroup()
    -- if self.wtNavGroup then
    --     self.wtNavGroup:MarkIsStackControlGroup()
    -- else
    --     logerror("OnOpen no wtNavGroup")
    -- end

    UE.GPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(self._OnInputTypeChanged, self)
end

function GuideMainUI:_CreateNavGroup()
    if not self.wtNavGroup then
        self.wtNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtPanel, self, "Grid1D")
    end
end

function GuideMainUI:_OnNavChangedNotify(t, name, widgets)
    self._navUINames = self._navUINames or {}
    self._navWidgetCount = self._navWidgetCount or 0
    widgets = setdefault(widgets, {})

    if t == "add" then
        if self._navUINames[name] ~= nil then
            logwarning("_OnNavChanged already exist", name)
            return
        end
        self._navUINames[name] = true
        if widgets and #widgets> 0 then
            self:_CreateNavGroup()
            if self.wtNavGroup then
                for _, widget in ipairs(widgets) do
                    self.wtNavGroup:AddNavWidgetToArray(widget)
                end
                self._navWidgetCount = self._navWidgetCount + #widgets

                if self._navWidgetCount > 0 then
                    self.wtNavGroup:MarkIsStackControlGroup()
                    WidgetUtil.TryFocusDefaultWidgetByGroup(self.wtNavGroup)
                end
            else
                logerror("_OnNavChanged no wtNavGroup", name)
            end
        end
    elseif t == "remove" then
        self:_ReleaseNavi()
    end

    if self._navWidgetCount == 0 then
        self:_ReleaseNavi()
    end
end

function GuideMainUI:_ReleaseNavi()
    if self.wtNavGroup then
        WidgetUtil.RemoveOneNavigationGroup(self.wtNavGroup)
        self.wtNavGroup = nil
    end
    self._navUINames = {}
    self._navWidgetCount = 0
end

function GuideMainUI:_OnInputTypeChanged(inputType)
    log("_OnInputTypeChanged", inputType)

    if IsHD() then
        if WidgetUtil.IsGamepad() then
            Facade.LuaFramingManager:RegisterFrameTask(function()
                if self._navWidgetCount > 0 then
                    WidgetUtil.TryFocusDefaultWidgetByGroup(self.wtNavGroup)
                end
            end)
        end
    end
end

function GuideMainUI:OnClose()
    self:_ReleaseNavi()

    self:_RemoveTimer()
    self:RemoveAllLuaEvent()

    Module.Guide:SetGuideInputGate("GuideMainUI", false)
    UGPInputHelper.WantInputMode_UIOnly(GetGameInstance(), "GuideMainUI", false, false)
    if IsHD() then
        WidgetUtil.SetFreeAnalogCursorIsBlocked(self, false)
    end
end

function GuideMainUI:OnShow()
end

function GuideMainUI:OnHide()
    self:_RemoveTimer()
end

function GuideMainUI:_OnPause()
    self:SetSwallowTouchState(self._bSwallowTouch, self._bImmediate)
end

function GuideMainUI:_OnRestart()
    self:SetSwallowTouchState(self._bSwallowTouch, self._bImmediate)
end

function GuideMainUI:OnInitExtraData()
end

function GuideMainUI:OnNativeOnTouchStarted(inGeometry, inGestureEvent)
end

function GuideMainUI:OnNativeOnTouchMoved(inGeometry, inGestureEvent)
end

function GuideMainUI:OnNativeOnTouchEnded(inGeometry, inGestureEvent)
    GuideConfig.EGuideEvent.evtGuideSwallowBgClicked:Invoke()
end

---------------------------Touch改Mouse--------------------------------------
function GuideMainUI:OnNativeOnMouseButtonDown(inGeometry, inGestureEvent)
    if UGPInputHelper.IsTouchOrLMBEvent(inGestureEvent) then

    end
end

function GuideMainUI:OnNativeOnMouseMove(inGeometry, inGestureEvent)
    if UGPInputHelper.IsTouchOrLMBEvent(inGestureEvent) then

    end
end

function GuideMainUI:OnNativeOnMouseButtonUp(inGeometry, inGestureEvent)
    if UGPInputHelper.IsTouchOrLMBEvent(inGestureEvent) then
        GuideConfig.EGuideEvent.evtGuideSwallowBgClicked:Invoke()
    end
end

---------------------------Touch改Mouse--------------------------------------

--region public
function GuideMainUI:Reset()
    self:SetSwallowTouchState(true)
end

function GuideMainUI:SetSwallowTouchState(bSwallow, bImmediate)
    self._bSwallowTouch = bSwallow
    self._bImmediate = bImmediate
    bSwallow = bSwallow and not Module.Guide.Field:IsStatePause()
    if bSwallow then
        self:_RemoveTimer()
        self._wtSwallowImg:SetVisibility(ESlateVisibility.Visible)
        Module.Guide:SetGuideInputGate("GuideMainUI", true)
        UGPInputHelper.WantInputMode_UIOnly(GetGameInstance(), "GuideMainUI", true, false)
        if IsHD() then
            WidgetUtil.SetFreeAnalogCursorIsBlocked(self, true)
        end
    else
        if self._bImmediate then
            self:_RemoveTimer()
            self._wtSwallowImg:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
            --local inputMonitor = Facade.UIManager:GetInputMonitor()
            --inputMonitor:SetActionsPriorityGate(EDisplayInputActionPriority.UI_Guide, false)
            Module.Guide:SetGuideInputGate("GuideMainUI", false)
            UGPInputHelper.WantInputMode_UIOnly(GetGameInstance(), "GuideMainUI", false, false)
            if IsHD() then
                WidgetUtil.SetFreeAnalogCursorIsBlocked(self, false)
            end
        else
            self:_CreateTimer()
        end
    end
end

--end region

function GuideMainUI:_StopGuideInEditor()
    Module.Guide:StopAllGuide()
end

function GuideMainUI:_RemoveTimer()
    if self._timer then
        Timer.CancelDelay(self._timer)
        self._timer = nil
    end
end

function GuideMainUI:_CreateTimer()
    self:_RemoveTimer()
    self._timer = Timer.DelayCall(0.1, self._DelayCollapsed, self)
end

function GuideMainUI:_DelayCollapsed()
    self._wtSwallowImg:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    --local inputMonitor = Facade.UIManager:GetInputMonitor()
    --inputMonitor:SetActionsPriorityGate(EDisplayInputActionPriority.UI_Guide, false)
    Module.Guide:SetGuideInputGate("GuideMainUI", false)
    UGPInputHelper.WantInputMode_UIOnly(GetGameInstance(), "GuideMainUI", false, false)
    if IsHD() then
        WidgetUtil.SetFreeAnalogCursorIsBlocked(self, false)
    end
    self._timer = nil
end

return GuideMainUI
