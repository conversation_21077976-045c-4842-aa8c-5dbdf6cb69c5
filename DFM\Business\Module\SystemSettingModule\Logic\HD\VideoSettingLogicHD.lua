----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------

local VideoSettingLogicHD = {}
local _videoSetting = import("ClientVideoSettingHD").Get()
local SettingRegLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.SettingRegLogicHD"
local CommonSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.CommonSettingLogicHD"
local UClientVideoSettingHD = import("ClientVideoSettingHD")
local UKismetSystemLibrary = import "KismetSystemLibrary"
local FIntPoint = import("IntPoint")
local UGameUserSettings = import("GameUserSettings")
local UClientSettingHelperHD = import("ClientSettingHelperHD")
local UKismetMathLibrary = import("KismetMathLibrary")
local UVideoSettingHelper = import("VideoSettingHelper")
local EGraphicsQualityLevelHD = import("EGraphicsQualityLevelHD")
local _qualityTablePath = "SystemSettingHD/GraphicsQualityMappingsHD"
local UPerfGearPipeline = import("PerfGearPipeline").Get()
local UAdvancedVideoSetting = import("AdvancedVideoSetting").Get()
local EWindowMode = import("EWindowMode")
local UGPGameplayGlobalDelegates = import("GPGameplayGlobalDelegates")
local UGPGameViewportClient = import("GPGameViewportClient")
-- BEGIN MODIFICATION - VIRTUOS
local UStreamlineLibraryReflex = PLATFORM_WINDOWS and import("StreamlineLibraryReflex") or nil
local UStreamlineLibraryDLSSG = PLATFORM_WINDOWS and import("StreamlineLibraryDLSSG") or nil
-- END MODIFICATION - VIRTUOS
local UWidgetLayoutLibrary = import("WidgetLayoutLibrary")
local UHardwareParamHelper = import("HardwareParamHelper")
local _gameSettingMobileClass = import("ClientGameSetting")
local UPerfGearPipeline = import("PerfGearPipeline")
local ERHIType = import("ERHIType")
local UDFBHDCPUController = import("DFBHDCPUController")
local UDFBHDMemoryController = import("DFBHDMemoryController")
local UDFBHDHelper = import("DFBHDHelper")
local UMetaperfUploader = import("MetaperfUploader")
local UGPGConfigUtils = import("GPGConfigUtils")

-- local 变量太多编译报错
local localT = {}
localT.ULowMemoryQualityManager = import("LowMemoryQualityManager").Get()

localT.NVIDIAReflexID = "NVIDIAReflex"
localT.UDLssLibrary = nil
localT.UFFXFSR3Library = nil
localT.UFSR2TemporalUpscalingLibrary = nil
localT.UXeSSLibrary = nil

localT.EGraphicsQualityLevelHD_Below = 4
localT.EGraphicsQualityLevelHD_Low = 5
localT.EGraphicsQualityLevelHD_Mid = 6
localT.EGraphicsQualityLevelHD_High = 7
localT.EGraphicsQualityLevelHD_Epic = 8
localT.EGraphicsQualityLevelHD_Wild = 9


local _videoSettingClampMap = {}

local function _BuildVideoSettingClampMap()
    local dataTable = Facade.TableManager:GetTable("VideoSettingClampHD")
    local curBenchmarkLevel = UVideoSettingHelper.GetBenchmarkLevelHD()
    if curBenchmarkLevel < localT.EGraphicsQualityLevelHD_Below then -- tomzycai: 防止新显卡、CPU用户被分配到低配挡位
        curBenchmarkLevel = localT.EGraphicsQualityLevelHD_Mid
    end

    for _, data in pairs(dataTable) do
        local settingID = data.SettingID
        local benchmarkLevel = data.BenchmarkLevel
        if not _videoSettingClampMap[settingID] then
            _videoSettingClampMap[settingID] = {-1, 0}
        end
        local settingData = _videoSettingClampMap[settingID]
        if settingData[1] < benchmarkLevel and benchmarkLevel <= curBenchmarkLevel then
            settingData[1] = benchmarkLevel
            settingData[2] = data.ClampMin
        end
    end
end

local function _GetVideoSettingClamp(settingID)
    local settingData = _videoSettingClampMap[settingID]
    if settingData and settingData[1] > 0 then
        return settingData[2]
    end
    return nil
end

local function _TryClampSettingValue(settingID, rawValue)
    if UClientVideoSettingHD.IsVideoSettingClampEnabled() then
        local clamp = _GetVideoSettingClamp(settingID)
        if clamp then
            return math.max(rawValue, clamp)
        end
    end
    return rawValue
end

local function IsNearlyEqual(a, b)
    return math.abs(a - b) < 10e-3
end

local function _UDLssLibrary()
    if not localT.UDLssLibrary then
        localT.UDLssLibrary = import("DLssLibrary")
    end
    return localT.UDLssLibrary
end

local function _FFXFSR3Library()
    -- if not localT.UFFXFSR3Library then
    --     localT.UFFXFSR3Library = import("FFXFSR3Library")
    -- end
    return localT.UFFXFSR3Library
end

local function _FSR2TemporalUpscalingLibrary()
    if not localT.FSR2TemporalUpscalingLibrary then
        localT.FSR2TemporalUpscalingLibrary = import("FSR2TemporalUpscalingLibrary")
    end
    return localT.FSR2TemporalUpscalingLibrary
end

local function _XeSSLibrary()
    if not localT.UXeSSLibrary then
        localT.UXeSSLibrary = import("XeSSBlueprintLibrary")
    end
    return localT.UXeSSLibrary
end

local function log(...)
    loginfo("[VideoSetting]", ...)
end

local function _BoolToInt(value)
    if value then
        return 1
    else
        return 0
    end
end


function VideoSettingLogicHD.ApplyPendingSettings()
    CommonSettingLogicHD.ApplyPendingSettings()
    -- refresh
    Timer.DelayCall(2, function()  
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.Shadow.ScrollingCSMC.ForceGlobalRefresh")
    end)
    -- GC
    UClientVideoSettingHD.GCAfterSeveralFrames(GetGameInstance(), 2)

    VideoSettingLogicHD.RecordRealViewportResolution(true)
    --VRamSettingLogicHD.ApplyVRamUsage()
    local VRamSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.VRamSettingLogicHD"
    VRamSettingLogicHD.InitVRamUsage()
end

--------------------Display Modes
local _displayModesUpdateFrame = 0

function VideoSettingLogicHD.ResetFrame()
    _displayModesUpdateFrame = 0
end
-- MonitorID DisplayMode Resolution RefreshRate
local function _ApplyDisplayModesAllInOne()
    if not UClientSettingHelperHD.IsEditor() then
        -- 防止重复调用
        if UVideoSettingHelper.GetGFrame() == _displayModesUpdateFrame then
            return
        end
        local displayMode = CommonSettingLogicHD.GetDataByID("DisplayMode")
        local InitialApplying = CommonSettingLogicHD.GetIsInitialApplying()
        if not InitialApplying then
        --关闭一下比例限制，否则会干扰全屏分辨率切换
            UGPGameViewportClient.DisableRatioConstrain(GetGameInstance())
        end
        _displayModesUpdateFrame = UVideoSettingHelper.GetGFrame()
        local monitorID = VideoSettingLogicHD.GetSelectedMonitorID()
        if displayMode ~= EWindowMode.Windowed then
            UClientVideoSettingHD.ChangeMonitorByID(monitorID)
        end
        local gameUserSettings = UGameUserSettings.GetGameUserSettings()
        -- local oldDisplayMode = gameUserSettings:GetFullscreenMode()
        -- 所有的操作流程都delay一帧，留给进程处理windows消息队列的时间。否则backbuffer尺寸可能会出问题
        Timer.DelayCall(
            0,
            function()
                gameUserSettings:SetFullscreenMode(displayMode)
                gameUserSettings:ApplyResolutionSettings(false)
                Timer.DelayCall(
                    0,
                    function()
                        -- set resolution again
                        local resolutionIndex = CommonSettingLogicHD.GetDataByID("Resolution")
                        local res = VideoSettingLogicHD.GetRealResolutionByIndex(resolutionIndex)

                        Timer.DelayCall(
                            0,
                            function()
                                gameUserSettings:SetScreenResolution(res)
                                UVideoSettingHelper.MarkResolutionForceRefresh()
                                gameUserSettings:ApplyResolutionSettings(false)

                                if displayMode == EWindowMode.Fullscreen then
                                    local refreshRate = CommonSettingLogicHD.GetDataByID("RefreshRate")
                                    if refreshRate == 0 then
                                        local refreshRateList = VideoSettingLogicHD.GetRefreshRateList()
                                        if refreshRateList:Num() > 0 then
                                            refreshRate = refreshRateList[1]
                                        end
                                    end
                                    UClientVideoSettingHD.SetRefreshRate(monitorID, refreshRate)
                                end
                                if not InitialApplying then
                                    UGPGameViewportClient.EnableRatioConstrain(GetGameInstance())
                                end

                                if displayMode == EWindowMode.WindowedFullscreen then
                                    UVideoSettingHelper.EnableMinimize()
                                end
                            end
                        )
                    end
                )
            end
        )
    end
end

local displayModeIDList = {"MonitorID", "DisplayMode", "Resolution" ,"RefreshRate"}

function VideoSettingLogicHD.StartConfirmDisplayModes()
    local oldDisplayModes

    for _, id in ipairs(displayModeIDList) do
        if CommonSettingLogicHD.HasPendingSettingByID(id) then
            oldDisplayModes = {}
            break
        end
    end

    if oldDisplayModes then
        for _, id in ipairs(displayModeIDList) do
            oldDisplayModes[id] = CommonSettingLogicHD.GetDataByIDNoPending(id)
        end
        -- 存下
        Module.SystemSetting.Field:SetOldDisplayModes(oldDisplayModes)
    end
end

function VideoSettingLogicHD.EndConfirmDisplayModes()
    local oldDisplayModes = Module.SystemSetting.Field:GetOldDisplayModes()
    if oldDisplayModes then
        Module.SystemSetting.Field:SetOldDisplayModes(nil)
        -- 弹窗

        local text = Module.SystemSetting.Config.Loc.HDSetting.DisplayModesChangeTxt
        local noticeText = Module.SystemSetting.Config.Loc.HDSetting.DisplayModesCountDownTxt
        local confirmText = Module.SystemSetting.Config.Loc.HDSetting.DisplayModesReserveTxt
        local cancelText = Module.SystemSetting.Config.Loc.HDSetting.DisplayModesRecoverTxt
        local duration = 15

        local confirmHandle = function() end
        local cancelHandle = function()
            local curItems = Module.SystemSetting.Field:GetAllSettingItemHD()
            for _, id in ipairs(displayModeIDList) do
                local value = oldDisplayModes[id]
                CommonSettingLogicHD.SetDataByID(id, value)
                local item = curItems[id]
                if not hasdestroy(item) then
                    item:ReloadSetting()
                end
            end
            VideoSettingLogicHD.ApplyPendingSettings()
        end


        Facade.UIManager:AsyncShowUI(
            UIName2ID.SystemSettingHDDCountDownConfirmWindow,
            nil,
            self,
            text,
            noticeText,
            confirmHandle,
            cancelHandle,
            cancelText,
            confirmText,
            duration
        )
    end
end

function VideoSettingLogicHD._OnMinimizeSwapchainStateChanged(bMinimize)
    if bMinimize then
        --低分辨率下DLSS会崩溃，所以强制关闭一下
        if _UDLssLibrary().IsDLSSSupported() then
            UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.NGX.DLSS.Enable 0")
        end
    else
        _ApplyDisplayModesAllInOne()

        -- 需要等待分辨率切换完成，至少delay 4帧
        local frameCount = 0
        Timer.DelayCallSomeTimes(0.1, 4, function()
            frameCount = frameCount + 1
            if frameCount == 4 then
                VideoSettingLogicHD._SuperResolutionMethodApplyFunc(_videoSetting, "SuperResolutionMethod", CommonSettingLogicHD.GetDataByID("SuperResolutionMethod"))
            end
        end)
    end
end

--------------------MonitorID

local function _IsValidMonitor(monitorID)
    if monitorID == "" then
        return true
    end
    local monitorInfos = UClientVideoSettingHD.GetMonitorInfos()
    for i, monitorInfo in pairs(monitorInfos) do
        if monitorID == monitorInfo.MonitorID then
            return true
        end
    end
    return false
end

local function _MonitorIDGetter(settingObj, key)
    return settingObj[key]
end

local function _MonitorIDSetter(settingObj, key, value)
    settingObj[key] = value
end

local function _MonitorIDResetter(settingObj, key, value)
    settingObj[key] = ""
end

local function _MonitorIDApplyFunc(settingObj, key, value)
    _ApplyDisplayModesAllInOne()
end

local function _RegMonitorIDBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegMapping(id, settingObj, key, _MonitorIDGetter, _MonitorIDSetter, _MonitorIDApplyFunc)
    SettingRegLogicHD.RegResetMapping(id, settingObj, key, _MonitorIDResetter)
    SettingRegLogicHD.RegDefaultValue(id, "")
end

function VideoSettingLogicHD.GetMonitorIDOptions()
    local monitorInfos = UClientVideoSettingHD.GetMonitorInfos()
    -- self.cachedMonitorInfos = monitorInfos
    local options = {}
    -- self._monitorMap = {}
    for i, monitorInfo in pairs(monitorInfos) do
        table.insert(options, string.format("%s %d", monitorInfo.MonitorName, i + 1))
        -- self._monitorMap[monitorInfo.MonitorName] = monitorInfo
    end

    return options
end

function VideoSettingLogicHD.GetSelectedMonitorID()
    local monitorID = CommonSettingLogicHD.GetDataByID("MonitorID")
    if monitorID == "" or monitorID == -1 then
        local monitorInfos = UClientVideoSettingHD.GetMonitorInfos()
        if monitorInfos[1] then
            monitorID = monitorInfos[1].MonitorID
        else
            monitorID = ""
        end
    end
    return monitorID
end

function VideoSettingLogicHD.GetMonitorIndexByID(monitorID)
    local monitorInfos = UClientVideoSettingHD.GetMonitorInfos()
    for i, monitorInfo in pairs(monitorInfos) do
        if monitorID == monitorInfo.MonitorID then
            return i
        end
    end
    return 0
end

function VideoSettingLogicHD.GetMonitorIDByIndex(index)
    local monitorInfos = UClientVideoSettingHD.GetMonitorInfos()
    local monitorInfo = monitorInfos[index + 1]
    if not monitorInfo then
        monitorInfo = monitorInfos[1]
    end
    return monitorInfo.MonitorID
end
--------------------AdapterID
local function _AdapterIDGetter(settingObj, key)
    return settingObj[key]
end

local function _AdapterIDSetter(settingObj, key, value)
    settingObj[key] = value
end

local function _AdapterIDResetter(settingObj, key, value)
    -- -1 to auto
    settingObj[key] = -1
end

local function _AdapterIDApplyFunc(settingObj, key, value)
    -- reboot
end

local function _RegAdapterIDBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegMapping(id, settingObj, key, _AdapterIDGetter, _AdapterIDSetter, _AdapterIDApplyFunc)
    SettingRegLogicHD.RegResetMapping(id, settingObj, key, _AdapterIDResetter)
end

local cachedAdapterInfos = nil
local cachedAdapterOptions = nil

function VideoSettingLogicHD.GetAdatperInfos()
    if not cachedAdapterInfos then
        cachedAdapterInfos = UClientVideoSettingHD.GetAdapterInfos()
    end
    return cachedAdapterInfos
end

function VideoSettingLogicHD.GetAdapterOptions()
    if cachedAdapterOptions == nil then
        cachedAdapterOptions = {}

        local options = CommonSettingLogicHD.GetDropDownOptionsByID("AdapterID")
        table.insert(cachedAdapterOptions, options[1])

        local adapterInfos = VideoSettingLogicHD.GetAdatperInfos()

        for _, adapterInfo in pairs(adapterInfos) do
            table.insert(cachedAdapterOptions, adapterInfo.AdapterName)
        end
    end
    return cachedAdapterOptions
end

function VideoSettingLogicHD.AdapterOptionIndexToID(index)
    if index == 0 then
        return -1
    else
        local optionName = tostring(cachedAdapterOptions[index + 1])
        for _, adapterInfo in pairs(VideoSettingLogicHD.GetAdatperInfos()) do
            if adapterInfo.AdapterName == optionName then
                return adapterInfo.AdapterID
            end
        end
    end
    return -1
end

function VideoSettingLogicHD.AdapterIDToOptionIndex(id)
    if id == -1 then
        return 0
    else
        local adapterName = ""
        for _, adapterInfo in pairs(VideoSettingLogicHD.GetAdatperInfos()) do
            if adapterInfo.AdapterID == id then
                adapterName = adapterInfo.AdapterName
                break
            end
        end
        for index, option in pairs(VideoSettingLogicHD.GetAdapterOptions()) do
            if adapterName == tostring(option) then
                return index - 1
            end
        end
    end
    return 0
end

function VideoSettingLogicHD.GetAutoAdapterID()
    return 0
end

--------------------DisplayMode
local function _InnerApplyDisplayMode(customDisplayMode)
    local gameUserSettings = UGameUserSettings.GetGameUserSettings()
    gameUserSettings:SetFullscreenMode(customDisplayMode)
    -- auto Logic

    if customDisplayMode ~= 1 then
        -- set resolution again
        local resolutionIndex = CommonSettingLogicHD.GetDataByID("Resolution")
        local res = VideoSettingLogicHD.GetRealResolutionByIndex(resolutionIndex)
        VideoSettingLogicHD.SetScreenResolution(res)
    end
    gameUserSettings:ApplyResolutionSettings(false)
end

local function _DisplayModeApplyFunc(settingObj, key, value)
    _ApplyDisplayModesAllInOne()
end

local function _RegDisplayModeBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _DisplayModeApplyFunc)
end

--------------------Resolution

local function _GetCurrentDisplayModes()
    local adapterID = VideoSettingLogicHD.GetAutoAdapterID()
    local monitorID = VideoSettingLogicHD.GetSelectedMonitorID()
    local displayModes = UClientVideoSettingHD.GetDisplayModes(adapterID, monitorID)
    return displayModes
end

function VideoSettingLogicHD.GetResolutionOptions()
    local displayModes = _GetCurrentDisplayModes()
    local resolutionList = UClientVideoSettingHD.GetPureResolutionList(displayModes)
    local options = {}
    local configOptions = CommonSettingLogicHD.GetDropDownOptionsByID("Resolution")
    table.insert(options, configOptions[1])
    for _, resolution in pairs(resolutionList) do
        table.insert(options, string.format("%dx%d", resolution.X, resolution.Y))
    end
    return options
end

function VideoSettingLogicHD.SetScreenResolution(res)
    if CommonSettingLogicHD.GetDataByID("DisplayMode") == EWindowMode.Fullscreen then
    -- UClientVideoSettingHD.SetMonitorResolution(VideoSettingLogicHD.GetSelectedMonitorID(), res.X, res.Y)
    end
    UGameUserSettings.GetGameUserSettings():SetScreenResolution(res)
    UGameUserSettings.GetGameUserSettings():ApplyResolutionSettings(false)
end

function VideoSettingLogicHD.GetRealResolutionByIndex(index)
    local newRes = FIntPoint()
    if index == 0 then
        newRes =
            UClientVideoSettingHD.GetAutoResolution(
            CommonSettingLogicHD.GetDataByID("AdapterID"),
            VideoSettingLogicHD.GetSelectedMonitorID(),
            CommonSettingLogicHD.GetDataByID("DisplayMode")
        )
    else
        -- local desktopRes = UGameUserSettings.GetGameUserSettings():GetDesktopResolution()
        -- newRes = desktopRes
        local displayModes = _GetCurrentDisplayModes()
        local resolutionList = UClientVideoSettingHD.GetPureResolutionList(displayModes)
        if resolutionList:Num() > 0 then
            local resolution = resolutionList[index]
            if resolution then
                newRes.X = resolution.X
                newRes.Y = resolution.Y
            end
        end
    end
    -- print("GetRealResolutionByIndex", newRes.X, newRes.Y,index,_videoSetting.DisplayMode)
    return newRes
end

function VideoSettingLogicHD.ForceReloadResolution()
    -- check supported
    local curItems = Module.SystemSetting.Field:GetAllSettingItemHD()
    local item = curItems["Resolution"]
    if not hasdestroy(item) then
        item:ReloadSetting()
    end
end

local function _ResolutionGetter(settingObj, key, value)
    local index = settingObj[key]
    if index ~= 0 then
        local X = settingObj.ResolutionX
        local Y = settingObj.ResolutionY
        if X ~= 0 and Y ~= 0 then
            local displayModes = _GetCurrentDisplayModes()
            local resolutionList = UClientVideoSettingHD.GetPureResolutionList(displayModes)

            for i, resolution in pairs(resolutionList) do
                if X == resolution.X and Y == resolution.Y then
                    return i + 1
                end
            end
        end
    end

    return index
end

local function _ResolutionSetter(settingObj, key, value)
    settingObj[key] = value
    local res = VideoSettingLogicHD.GetRealResolutionByIndex(value)
    settingObj.ResolutionX = res.X
    settingObj.ResolutionY = res.Y
    -- CommonSettingLogicHD.InnerApplyDisplayMode(value)
end

local function _ResolutionApplyFunc(settingObj, key, value)
    _ApplyDisplayModesAllInOne()
end

local function _RegResolutionBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegMapping(id, settingObj, key, _ResolutionGetter, _ResolutionSetter, _ResolutionApplyFunc)
end

--------------------RefreshRate

local function _GetRefreshRateList()
    local resolutionIndex = CommonSettingLogicHD.GetDataByID("Resolution")
    local resolution = VideoSettingLogicHD.GetRealResolutionByIndex(resolutionIndex)
    local displayModes = _GetCurrentDisplayModes()
    local refreshRateList = UClientVideoSettingHD.GetSupportedRefreshRateList(displayModes, resolution.X, resolution.Y)
    return refreshRateList
end

VideoSettingLogicHD.GetRefreshRateList = _GetRefreshRateList

function VideoSettingLogicHD.ForceReloadRefreshRate()
    -- check supported
    local curRate = CommonSettingLogicHD.GetDataByID("RefreshRate")
    if curRate ~= 0 then
        local supported = false
        local refreshRateList = _GetRefreshRateList()
        for _, rate in pairs(refreshRateList) do
            if rate == curRate then
                supported = true
                break
            end
        end

        if not supported then
            curRate = 0
            CommonSettingLogicHD.SetDataByID("RefreshRate", curRate)
        end
    end

    local curItems = Module.SystemSetting.Field:GetAllSettingItemHD()
    local item = curItems["RefreshRate"]
    if not hasdestroy(item) then
        item:ReloadSetting()
    end
end

function VideoSettingLogicHD.GetRefreshRateOptions()
    local refreshRateList = _GetRefreshRateList()
    local options = {}
    local configOptions = CommonSettingLogicHD.GetDropDownOptionsByID("RefreshRate")
    table.insert(options, configOptions[1])
    for _, rate in pairs(refreshRateList) do
        table.insert(options, tostring(rate))
    end
    return options
end

function VideoSettingLogicHD.GetRefreshRateByIndex(index)
    local refreshRateList = _GetRefreshRateList()

    local refreshRate = 0
    if refreshRateList:Num() > 0 then
        if index == 0 then
            refreshRate = refreshRateList[1] -- biggest
        else
            refreshRate = refreshRateList[index]
        end
    end
    return refreshRate
end

function VideoSettingLogicHD.GetRefreshRateOptionIndex(refreshRate)
    if refreshRate == 0 then
        return 0, true
    end
    local refreshRateList = _GetRefreshRateList()
    local index = 0
    local bFound = false
    for i, rate in pairs(refreshRateList) do
        if rate == refreshRate then
            index = i + 1
            bFound = true
            break
        end
    end
    return index, bFound
end

local function _RefreshRateGetter(settingObj, key)
    return settingObj[key]
end

local function _RefreshRateSetter(settingObj, key, value)
    settingObj[key] = value
end

local function _RefreshRateResetter(settingObj, key, value)
    settingObj[key] = 0
end

local function _RefreshRateApplyFunc(settingObj, key, value)
    _ApplyDisplayModesAllInOne()
end

local function _RegRefreshRateBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegMapping(id, settingObj, key, _RefreshRateGetter, _RefreshRateSetter, _RefreshRateApplyFunc)
    SettingRegLogicHD.RegResetMapping(id, settingObj, key, _RefreshRateResetter)
end

--------------------刷新显示器，分辨率，刷新率设置

local function VerifyDisplayModeSettings()
    -- 一旦显示器发生变化，可能是关了，少了，增加了，分辨率变了。。。。
    -- 都要重新验证相关的显示设置，并且重新Apply
    -- 如果当前正在设置界面，且相关的设置还未应用，也要强行清除。
    local monitorID = VideoSettingLogicHD.GetSelectedMonitorID()
    local isValidMonitor = _IsValidMonitor(monitorID)
    log("IsValidMonitor", monitorID, isValidMonitor)
    -- 如果原本的显示器找不到了，则要重置
    if not isValidMonitor then
        CommonSettingLogicHD.ResetDataByIDImmediately("MonitorID")
    end

    local resolutionIndex = CommonSettingLogicHD.GetDataByID("Resolution")
    local resolution = VideoSettingLogicHD.GetRealResolutionByIndex(resolutionIndex)
    if resolution.X == 0 then
        CommonSettingLogicHD.ResetDataByIDImmediately("Resolution")
    end

    local refreshRate = CommonSettingLogicHD.GetDataByID("RefreshRate")
    local index, bFound = VideoSettingLogicHD.GetRefreshRateOptionIndex(refreshRate)
    if not bFound then
        CommonSettingLogicHD.ResetDataByIDImmediately("RefreshRate")
    end

    -- 刷新UI
    local itemsToRefresh = {"MonitorID", "Resolution", "RefreshRate"}
    local curItems = Module.SystemSetting.Field:GetAllSettingItemHD()

    for _, id in ipairs(itemsToRefresh) do
        local item = curItems[id]
        if not hasdestroy(item) then
            item:ReloadSetting()
        end
        CommonSettingLogicHD.RefreshPrerequisite(id)
    end
end

local function OnDisplayMetricsChangedHD()
    Timer.DelayCall(
        0,
        function()
            UClientVideoSettingHD.ClearCachedDisplayModes()
            VerifyDisplayModeSettings()
        end
    )
end

--------------------AntiAliasingMethod

local UE_AAM_VALUE = {
    None = 0,
    FXAA = 1,
    TemporalAA = 2,
    TemporalSuperResolution = 4
}

local DFHD_AAM_VALUE = {
    FXAA = 0,
    TAA = 1
}

local DFHD_SUPERRES_VALUE = {
    None = 0,
    TSR = 1,
    FSR2 = 2,
    FSR3 = 3,
    DLSS = 4,
    XESS = 5
}

-- local function _AntiAliasingMethodGetter(settingObj, _, _)
--     local DefaultAA = UKismetSystemLibrary.GetConsoleVariableIntValue("r.AntiAliasingMethod")
--     local AAQuality = UKismetSystemLibrary.GetConsoleVariableIntValue("r.PostProcessAAQuality")

--     --local TemporalAAQuality = UKismetSystemLibrary.GetConsoleVariableIntValue("r.TemporalAA.Quality")
--     --local FXAAQuality = UKismetSystemLibrary.GetConsoleVariableIntValue("r.FXAA.Quality")

--     --local MSAACount = UKismetSystemLibrary.GetconsoleVariableIntValue("r.MSAACount")
--     --local ForwardShading = UKismetSystemLibrary.GetConsoleVariableIntValue("r.ForwardShading")
--     local DLSSEnabled = UKismetSystemLibrary.GetConsoleVariableIntValue("r.NGX.DLSS.Enable") ~= 0
--     local FSR2Enabled = UKismetSystemLibrary.GetConsoleVariableIntValue("r.FidelityFX.FSR2.Enabled") ~= 0

--     --local MSAAEnabled = (DefaultAA == UE_AAM_VALUE.MSAA and MSAACount > 0) and ForwardShading
--     local TAAEnabled = (DefaultAA == UE_AAM_VALUE.TemporalAA and AAQuality >= 3)
--     local TSREnabled = (DefaultAA == UE_AAM_VALUE.TemporalSuperResolution)
--     local FXAAEnabled =
--         (DefaultAA == UE_AAM_VALUE.FXAA and AAQuality > 0) or (DefaultAA == UE_AAM_VALUE.TemporalAA and AAQuality < 3)

--     TAAEnabled = TAAEnabled and not DLSSEnabled and not FSR2Enabled
--     FXAAEnabled = FXAAEnabled and not DLSSEnabled and not FSR2Enabled
--     DLSSEnabled = DLSSEnabled and not FSR2Enabled

--     if TAAEnabled then
--         return DFHD_AAM_VALUE.TAA
--     elseif TSREnabled then
--         return DFHD_AAM_VALUE.TSR
--     elseif FXAAEnabled then
--         return DFHD_AAM_VALUE.FXAA
--     elseif DLSSEnabled then
--         return DFHD_AAM_VALUE.DLSS
--     elseif FSR2Enabled then
--         return DFHD_AAM_VALUE.FSR2
--     else
--         -- default is Temporal AA
--         return DFHD_AAM_VALUE.TSR
--     end
-- end

function VideoSettingLogicHD.SetAntiAliasingMethod(method)
    if isinvalid(UAdvancedVideoSetting) then
        log("Error : UAdvancedVideoSetting is not valid")
        return
    end

    local PfLevel = CommonSettingLogicHD.GetPfLevel("AntiAliasingMethod", method)

    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        string.format("PerfGear.GroupQuality AntiAliasing %d", PfLevel)
    )
end

local function _AntiAliasingMethodApplyFunc(settingObj, key, value)
    if CommonSettingLogicHD.GetDataByID("SuperResolutionMethod") ~= DFHD_SUPERRES_VALUE.None then
        return
    end
    local method = value
    VideoSettingLogicHD.SetAntiAliasingMethod(method)
end

local function _RegAnitAliasingMethodBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughGetMapping(id, key)
    SettingRegLogicHD.RegPassThroughSetMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _AntiAliasingMethodApplyFunc)
end

--------------------Super Resolution Quality

function VideoSettingLogicHD.SetTSRQuality(quality)
    if isinvalid(UAdvancedVideoSetting) then
        log("Error : UAdvancedVideoSetting is not valid")
        return
    end
    UAdvancedVideoSetting:SetParamGroupQuality("TSRQuality", quality)
end

function VideoSettingLogicHD.SetDLSSQuality(quality)
    if isinvalid(UAdvancedVideoSetting) then
        log("Error : UAdvancedVideoSetting is not valid")
        return
    end
    UAdvancedVideoSetting:SetParamGroupQuality("DLSSQuality", quality)
end

function VideoSettingLogicHD.SetFSR2Quality(quality)
    if isinvalid(UAdvancedVideoSetting) then
        log("Error : UAdvancedVideoSetting is not valid")
        return
    end
    UAdvancedVideoSetting:SetParamGroupQuality("FSR2Quality", quality)
end

function VideoSettingLogicHD.SetFSR3Quality(quality)
    if isinvalid(UAdvancedVideoSetting) then
        log("Error : UAdvancedVideoSetting is not valid")
        return
    end
    UAdvancedVideoSetting:SetParamGroupQuality("FSR3Quality", quality)
end

function VideoSettingLogicHD.SetXeSSQuality(quality)
    if isinvalid(UAdvancedVideoSetting) then
        log("Error : UAdvancedVideoSetting is not valid")
        return
    end
    -- local success, screenPercentage = _XeSSLibrary().GetXeSSQualityModeInformation(quality, nil)
    -- if  success then
    --     local cmd = string.format("r.ScreenPercentage %f", screenPercentage)
    --     UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), cmd)
    -- end
    -- --UAdvancedVideoSetting:SetParamGroupQuality("XeSSQuality", quality)
    local level = tonumber(UAdvancedVideoSetting:GetParamQualityValue("XeSS.Quality", quality))
    if level then
        _XeSSLibrary().SetXeSSQualityMode(level)
    end
end

function VideoSettingLogicHD.SetSuperResolutionQualityDLSS(method, quality)
    if method == DFHD_SUPERRES_VALUE.DLSS then
        VideoSettingLogicHD.SetDLSSQuality(quality)
    end
end

function VideoSettingLogicHD.SetSuperResolutionQualityTSR(method, quality)
    if method == DFHD_SUPERRES_VALUE.TSR then
        VideoSettingLogicHD.SetTSRQuality(quality)
    end
end

function VideoSettingLogicHD.SetSuperResolutionQualityFSR2(method, quality)
    if method == DFHD_SUPERRES_VALUE.FSR2 then
        VideoSettingLogicHD.SetFSR2Quality(quality)
    end
end

function VideoSettingLogicHD.SetSuperResolutionQualityFSR3(method, quality)
    if method == DFHD_SUPERRES_VALUE.FSR3 then
        VideoSettingLogicHD.SetFSR3Quality(quality)
    end
end

function VideoSettingLogicHD.SetSuperResolutionQualityXESS(method, quality)
    if method == DFHD_SUPERRES_VALUE.XESS then
        VideoSettingLogicHD.SetXeSSQuality(quality)
    end
end

local function _SuperResolutionQualityDLSSApplyFunc(settingObj, key, value)
    local method = settingObj["SuperResolutionMethod"]
    local quality = value
    VideoSettingLogicHD.SetSuperResolutionQualityDLSS(method, quality)
end

local function _SuperResolutionQualityTSRApplyFunc(settingObj, key, value)
    local method = settingObj["SuperResolutionMethod"]
    local quality = value
    VideoSettingLogicHD.SetSuperResolutionQualityTSR(method, quality)
end

function VideoSettingLogicHD._SuperResolutionQualityFSR2ApplyFunc(settingObj, key, value)
    local method = settingObj["SuperResolutionMethod"]
    local quality = value
    VideoSettingLogicHD.SetSuperResolutionQualityFSR2(method, quality)
end

function VideoSettingLogicHD._SuperResolutionQualityFSR3ApplyFunc(settingObj, key, value)
    local method = settingObj["SuperResolutionMethod"]
    local quality = value
    VideoSettingLogicHD.SetSuperResolutionQualityFSR3(method, quality)
end

local function _SuperResolutionQualityXESSApplyFunc(settingObj, key, value)
    local method = settingObj["SuperResolutionMethod"]
    local quality = value
    VideoSettingLogicHD.SetSuperResolutionQualityXESS(method, quality)
end

local function EnableTSR()
    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        string.format("r.AntiAliasingMethod %d", UE_AAM_VALUE.TemporalSuperResolution)
    )
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.NGX.DLSS.Enable 0")
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.FidelityFX.FSR2.Enabled 0")
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.FidelityFX.FSR3.Enabled 0")
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.XeSS.Enabled 0")
end

local function EnableDLSS()
    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        string.format("r.AntiAliasingMethod %d", UE_AAM_VALUE.TemporalAA)
    )
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.NGX.DLSS.Enable 1")
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.FidelityFX.FSR2.Enabled 0")
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.FidelityFX.FSR3.Enabled 0")
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.XeSS.Enabled 0")
end

function VideoSettingLogicHD.EnableFSR2()
    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        string.format("r.AntiAliasingMethod %d", UE_AAM_VALUE.TemporalAA)
    )
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.NGX.DLSS.Enable 0")
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.FidelityFX.FSR2.Enabled 1")
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.FidelityFX.FSR3.Enabled 0")
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.XeSS.Enabled 0")

    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.FidelityFX.FSR2.CreateReactiveMask 0")
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.SeparateTranslucency 0")
end

function VideoSettingLogicHD.EnableFSR3()
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(),
        string.format("r.AntiAliasingMethod %d", UE_AAM_VALUE.TemporalAA))
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.NGX.DLSS.Enable 0")
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.FidelityFX.FSR2.Enabled 0")
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.FidelityFX.FSR3.Enabled 1")
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.XeSS.Enabled 0")

    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.FidelityFX.FSR3.CreateReactiveMask 2")
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.FidelityFX.FSR3.AutoExposure 1")
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.SeparateTranslucency 0")
end

local function EnableXeSS()
    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        string.format("r.AntiAliasingMethod %d", UE_AAM_VALUE.TemporalAA)
    )
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.NGX.DLSS.Enable 0")
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.FidelityFX.FSR2.Enabled 0")
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.FidelityFX.FSR3.Enabled 0")
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.XeSS.Enabled 1")
end

local function _SuperResolutionMethodApplyFunc(settingObj, key, value)
    local method = value
    if method == DFHD_SUPERRES_VALUE.None then
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.GlobalSuperResolutionMethod 0")
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.NGX.DLSS.Enable 0")
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.FidelityFX.FSR2.Enabled 0")
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.FidelityFX.FSR3.Enabled 0")
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.Xess.Enabled 0")

        -- recover render scale
        VideoSettingLogicHD._RenderScaleApplyFunc(
            settingObj,
            "RenderScale",
            CommonSettingLogicHD.GetDataByID("RenderScale")
        )
        -- recover aa method
        _AntiAliasingMethodApplyFunc(
            settingObj,
            "AntiAliasingMethod",
            CommonSettingLogicHD.GetDataByID("AntiAliasingMethod")
        )
    else
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.GlobalSuperResolutionMethod 1")
        -- 恢复一些状态
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.ScreenPercentage 100")

        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.PostProcessAAQuality 4")

        local FSR2Enabled = UKismetSystemLibrary.GetConsoleVariableIntValue("r.FidelityFX.FSR2.Enabled") ~= 0
        local DLSSEnabled = UKismetSystemLibrary.GetConsoleVariableIntValue("r.NGX.DLSS.Enable") ~= 0
        local XeSSEnabled = UKismetSystemLibrary.GetConsoleVariableIntValue("r.XeSS.Enable") ~= 0
        local FSR3Enabled = false--UKismetSystemLibrary.GetConsoleVariableIntValue("r.FidelityFX.FSR3.Enabled") ~= 0

        if method == DFHD_SUPERRES_VALUE.DLSS then
            if FSR2Enabled or XeSSEnabled or FSR3Enabled then
                if FSR2Enabled then
                    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.FidelityFX.FSR2.Enabled 0")
                elseif FSR3Enabled then
                    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.FidelityFX.FSR3.Enabled 0")
                elseif XeSSEnabled then
                    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.XeSS.Enable 0")
                end
                Timer.DelayCall(0, function()
                    EnableDLSS()
                    VideoSettingLogicHD.ApplySuperResolutionQualities(method, quality)
                end)
            else
                EnableDLSS()
                VideoSettingLogicHD.ApplySuperResolutionQualities(method, quality)
            end
        elseif method == DFHD_SUPERRES_VALUE.TSR then
            EnableTSR()
            VideoSettingLogicHD.ApplySuperResolutionQualities(method, quality)
        elseif method == DFHD_SUPERRES_VALUE.FSR2 then
            if DLSSEnabled or XeSSEnabled then
                if DLSSEnabled then
                    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.NGX.DLSS.Enable 0")
                elseif XeSSEnabled then
                    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.XeSS.Enable 0")
                end
                Timer.DelayCall(0, function()
                    VideoSettingLogicHD.EnableFSR2()
                    VideoSettingLogicHD.ApplySuperResolutionQualities(method, quality)
                end)
            else
                VideoSettingLogicHD.EnableFSR2()
                VideoSettingLogicHD.ApplySuperResolutionQualities(method, quality)
            end
        elseif method == DFHD_SUPERRES_VALUE.FSR3 then
            -- if DLSSEnabled or XeSSEnabled then
            --     if DLSSEnabled then
            --         UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.NGX.DLSS.Enable 0")
            --     elseif XeSSEnabled then
            --         UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.XeSS.Enable 0")
            --     end
            --     Timer.DelayCall(0, function()
            --         VideoSettingLogicHD.EnableFSR3()
            --         VideoSettingLogicHD.ApplySuperResolutionQualities(method, quality)
            --     end)
            -- else
            --     VideoSettingLogicHD.EnableFSR3()
            --     VideoSettingLogicHD.ApplySuperResolutionQualities(method, quality)
            -- end
        elseif method == DFHD_SUPERRES_VALUE.XESS then
            if DLSSEnabled or FSR2Enabled then
                if DLSSEnabled then
                    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.NGX.DLSS.Enable 0")
                elseif FSR2Enabled then
                    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.FidelityFX.FSR2.Enabled 0")
                elseif FSR3Enabled then
                    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.FidelityFX.FSR3.Enabled 0")
                end
                Timer.DelayCall(0, function()
                    EnableXeSS()
                    VideoSettingLogicHD.ApplySuperResolutionQualities(method, quality)
                end)
            else
                EnableXeSS()
                VideoSettingLogicHD.ApplySuperResolutionQualities(method, quality)
            end
        end
    end

    -- 修改超分后可能会影响帧生成
    VideoSettingLogicHD.ApplyFrameGeneration()
end

function VideoSettingLogicHD.ApplySuperResolutionQualities(method, quality)
    local quality = 0
    if method == DFHD_SUPERRES_VALUE.DLSS then
        quality = _videoSetting.SuperResolutionQualityDLSS
        VideoSettingLogicHD.SetSuperResolutionQualityDLSS(method, quality)
    elseif method == DFHD_SUPERRES_VALUE.TSR then
        quality = _videoSetting.SuperResolutionQualityTSR
        VideoSettingLogicHD.SetSuperResolutionQualityTSR(method, quality)
    elseif method == DFHD_SUPERRES_VALUE.FSR2 then
        quality = _videoSetting.SuperResolutionQualityFSR2
        VideoSettingLogicHD.SetSuperResolutionQualityFSR2(method, quality)
    elseif method == DFHD_SUPERRES_VALUE.FSR3 then
        quality = _videoSetting.SuperResolutionQualityFSR3
        VideoSettingLogicHD.SetSuperResolutionQualityFSR3(method, quality)
    elseif method == DFHD_SUPERRES_VALUE.XESS then
        quality = _videoSetting.SuperResolutionQualityXESS
        VideoSettingLogicHD.SetSuperResolutionQualityXESS(method, quality)
    end
end

VideoSettingLogicHD._SuperResolutionMethodApplyFunc = _SuperResolutionMethodApplyFunc

local SUPERRES_METHOD_INDICES = nil
local SUPERRES_INDEX_METHOD = nil

local function TryInitSuperResolutionTable()
    if SUPERRES_METHOD_INDICES == nil and SUPERRES_INDEX_METHOD == nil then
        SUPERRES_METHOD_INDICES = {}
        SUPERRES_METHOD_INDICES[DFHD_SUPERRES_VALUE.None] = 0
        SUPERRES_METHOD_INDICES[DFHD_SUPERRES_VALUE.TSR] = 1

        SUPERRES_INDEX_METHOD = {}
        SUPERRES_INDEX_METHOD[0] = DFHD_SUPERRES_VALUE.None
        SUPERRES_INDEX_METHOD[1] = DFHD_SUPERRES_VALUE.TSR

        local index = 2

        if _FSR2TemporalUpscalingLibrary().IsFSR2Supported() then
            SUPERRES_METHOD_INDICES[DFHD_SUPERRES_VALUE.FSR2] = index
            SUPERRES_INDEX_METHOD[index] = DFHD_SUPERRES_VALUE.FSR2
            index = index + 1
        else
            SUPERRES_METHOD_INDICES[DFHD_SUPERRES_VALUE.FSR2] = 0
        end


	--- BEGIN MODIFICATION - VIRTUOS: Enable DLSS XeSS in Windows
        if PLATFORM_WINDOWS == 1 then
	    --TOD: Temp disable FSR3 and wait console integration
            -- if _FFXFSR3Library().IsFSR3Supported() then
            -- 	SUPERRES_METHOD_INDICES[DFHD_SUPERRES_VALUE.FSR3] = index
            -- 	SUPERRES_INDEX_METHOD[index] = DFHD_SUPERRES_VALUE.FSR3
            -- 	index = index + 1
            -- else
            	SUPERRES_METHOD_INDICES[DFHD_SUPERRES_VALUE.FSR3] = 0
            --end

            if _UDLssLibrary().IsDLSSSupported() then
                SUPERRES_METHOD_INDICES[DFHD_SUPERRES_VALUE.DLSS] = index
                SUPERRES_INDEX_METHOD[index] = DFHD_SUPERRES_VALUE.DLSS
                index = index + 1
            else
                SUPERRES_METHOD_INDICES[DFHD_SUPERRES_VALUE.DLSS] = 0
            end

            if _XeSSLibrary().IsXeSSSupported() then
                SUPERRES_METHOD_INDICES[DFHD_SUPERRES_VALUE.XESS] = index
                SUPERRES_INDEX_METHOD[index] = DFHD_SUPERRES_VALUE.XESS
                index = index + 1
            else
                SUPERRES_METHOD_INDICES[DFHD_SUPERRES_VALUE.XESS] = 0
            end
        end
    --- END MODIFICATION
    end
end

function VideoSettingLogicHD.GetSuperResolutionMethodByIndex(Index)
    TryInitSuperResolutionTable()
    return SUPERRES_INDEX_METHOD[Index]
end

function VideoSettingLogicHD.GetSuperResolutionMethodIndex(method)
    TryInitSuperResolutionTable()
    return SUPERRES_METHOD_INDICES[method]
end

function VideoSettingLogicHD.GetSuperResolutionMethodOptions()
    local rawOptions = CommonSettingLogicHD.GetDropDownOptionsByID("SuperResolutionMethod")
    local staticOptionNum = 4
    local options = {}
    for i = 1, staticOptionNum do
        if i == DFHD_SUPERRES_VALUE.FSR2 + 1 then
            if _FSR2TemporalUpscalingLibrary().IsFSR2Supported() then
                table.insert(options, rawOptions[i])
            end
        elseif i == DFHD_SUPERRES_VALUE.FSR3 + 1 then
            if false then--_FFXFSR3Library().IsFSR3Supported() then
                table.insert(options, rawOptions[i])
            end
        else
            table.insert(options, rawOptions[i])
        end
    end

--- BEGIN MODIFICATION - VIRTUOS: Enable DLSS XeSS in Windows
    if PLATFORM_WINDOWS == 1 then
        if _UDLssLibrary().IsDLSSSupported() then
            table.insert(options, rawOptions[DFHD_SUPERRES_VALUE.DLSS + 1])
        end
        if _XeSSLibrary().IsXeSSSupported() then
            table.insert(options, rawOptions[DFHD_SUPERRES_VALUE.XESS + 1])
        end
    end
--- END MODIFICATION

    return options
end

local function _RegSuperResolutionMethodBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughGetMapping(id, key)
    SettingRegLogicHD.RegPassThroughSetMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _SuperResolutionMethodApplyFunc)
end

local function _RegSuperResolutionQualityDLSSBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughGetMapping(id, key)
    SettingRegLogicHD.RegPassThroughSetMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _SuperResolutionQualityDLSSApplyFunc)
end

local function _RegSuperResolutionQualityTSRBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughGetMapping(id, key)
    SettingRegLogicHD.RegPassThroughSetMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _SuperResolutionQualityTSRApplyFunc)
end

function VideoSettingLogicHD._RegSuperResolutionQualityFSR2Batch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughGetMapping(id, key)
    SettingRegLogicHD.RegPassThroughSetMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, VideoSettingLogicHD._SuperResolutionQualityFSR2ApplyFunc)
end

function VideoSettingLogicHD._RegSuperResolutionQualityFSR3Batch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughGetMapping(id, key)
    SettingRegLogicHD.RegPassThroughSetMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, VideoSettingLogicHD._SuperResolutionQualityFSR3ApplyFunc)
end

local function _RegSuperResolutionQualityXESSBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughGetMapping(id, key)
    SettingRegLogicHD.RegPassThroughSetMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _SuperResolutionQualityXESSApplyFunc)
end

-------------------- ViewpotRatio Settings
local ViewportRatioList = {
    {0, 0},
    {4, 3},
    {16, 10},
    {16, 9},
    {21, 9},
    {32, 9}
}

local function _ViewportRatioSetter(settingObj, key, value)
    settingObj[key] = value
    local ratio = ViewportRatioList[value + 1]
    settingObj.ViewportRatioValue.X = ratio[1]
    settingObj.ViewportRatioValue.Y = ratio[2]
end

local function _ViewportRatioApplyFunc(settingObj, key, value)
    UGPGameViewportClient.SetViewportRatio(GetGameInstance(), settingObj.ViewportRatioValue)
end

local function _RegViewportRatioBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughGetMapping(id, key)
    SettingRegLogicHD.RegSetMapping(id, settingObj, key, _ViewportRatioSetter)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _ViewportRatioApplyFunc)
end

local function _ApplyViewportRatioRange()
    local min = ViewportRatioList[2]
    local max = ViewportRatioList[#ViewportRatioList]
    local minRatio = FIntPoint()
    local maxRatio = FIntPoint()
    minRatio.X = min[1]
    minRatio.Y = min[2]
    maxRatio.X = max[1]
    maxRatio.Y = max[2]
    UGPGameViewportClient.SetViewportRatioRange(GetGameInstance(), minRatio, maxRatio)
end

-------------------- Ray Tracing Settings

local function IsRayTracingSupported()
    if isinvalid(UAdvancedVideoSetting) then
        log("Error : UAdvancedVideoSetting is not valid")
        return false
    end
    return UAdvancedVideoSetting:IsRayTracingSupported()
end

VideoSettingLogicHD.IsRayTracingSupported = IsRayTracingSupported

function VideoSettingLogicHD.IsInRayTracingWhitelist()
    local whitelist = {
        "nvidia geforce rtx 4090",
        "nvidia geforce rtx 4090 d",
        "nvidia geforce rtx 4090 laptop gpu"
    }
    local SystemInfoHD = UVideoSettingHelper.GetSystemInfoHD()
    local gpuBrand = string.lower(SystemInfoHD.GPUBrand)
    for _,brand in ipairs(whitelist) do
        if brand == gpuBrand then
            return true
        end
    end
    return false
end

local function _RayTracingEnabledGetter(settingObj, _, _)
    if isinvalid(UAdvancedVideoSetting) then
        log("Error : UAdvancedVideoSetting is not valid")
        return false
    end
    return UAdvancedVideoSetting:IsRayTracingSupported() and UAdvancedVideoSetting:IsRayTracingEnabled()
end

local function _RayTracingEnabledSetter(settingObj, _, value)
    if isinvalid(UAdvancedVideoSetting) then
        log("Error : UAdvancedVideoSetting is not valid")
        return
    end
    UAdvancedVideoSetting:SetRayTracingEnable(value)
end

local function _RayTracingEnabledApplyFunc(settingObj, key, value)
    if isinvalid(UAdvancedVideoSetting) then
        log("Error : UAdvancedVideoSetting is not valid")
        return
    end
    UAdvancedVideoSetting:SetRayTracingEnable(value)
end

local function _RegRayTracingEnabledBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegGetMapping(id, settingObj, key, _RayTracingEnabledGetter)
    SettingRegLogicHD.RegSetMapping(id, settingObj, key, _RayTracingEnabledSetter)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _RayTracingEnabledApplyFunc)
end

local function _RayTracingQualityGetter(settingObj, _, _)
    -- todo
    return settingObj.RayTracingQuality
end

function VideoSettingLogicHD.SetRayTracingQuality(quality)
    -- todo
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.RayTracing.EnableInGame")
end

local function _RayTracingQualityApplyFunc(settingObj, key, value)
    local quality = value
    VideoSettingLogicHD.SetRayTracingQuality(quality)
end

local function _RegRayTracingQualityBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughGetMapping(id, key)
    SettingRegLogicHD.RegPassThroughSetMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _RayTracingQualityApplyFunc)
end

function VideoSettingLogicHD.SetRayTracingGlobalIllumination(enable)
    if isinvalid(UAdvancedVideoSetting) then
        log("Error : UAdvancedVideoSetting is not valid")
        return
    end
    UAdvancedVideoSetting:SetParamGroupQuality("RayTracingGI", enable)
end

local function _RayTracingGlobalIlluminationApplyFunc(settingObj, key, value)
    local enable = value
    VideoSettingLogicHD.SetRayTracingGlobalIllumination(enable)
end

local function _RegRayTracingGlobalIlluminationBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughGetMapping(id, key)
    SettingRegLogicHD.RegPassThroughSetMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _RayTracingGlobalIlluminationApplyFunc)
end

function VideoSettingLogicHD.SetRayTracingReflections(enable)
    if isinvalid(UAdvancedVideoSetting) then
        log("Error : UAdvancedVideoSetting is not valid")
        return
    end
    UAdvancedVideoSetting:SetParamGroupQuality("RayTracingReflection", enable)
end

local function _RayTracingReflectionsApplyFunc(settingObj, key, value)
    local enable = value
    VideoSettingLogicHD.SetRayTracingReflections(enable)
end

local function _RegRayTracingReflectionsBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughGetMapping(id, key)
    SettingRegLogicHD.RegPassThroughSetMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _RayTracingReflectionsApplyFunc)
end

function VideoSettingLogicHD.SetRayTracingShadows(enable)
    if isinvalid(UAdvancedVideoSetting) then
        log("Error : UAdvancedVideoSetting is not valid")
        return
    end
    UAdvancedVideoSetting:SetParamGroupQuality("RayTracingShadow", enable)
end

local function _RayTracingShadowsApplyFunc(settingObj, key, value)
    local enable = value
    VideoSettingLogicHD.SetRayTracingShadows(enable)
end

local function _RegRayTracingShadowsBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughGetMapping(id, key)
    SettingRegLogicHD.RegPassThroughSetMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _RayTracingShadowsApplyFunc)
end

function VideoSettingLogicHD.SetRayTracingAmbientOcclusion(enable)
    if isinvalid(UAdvancedVideoSetting) then
        log("Error : UAdvancedVideoSetting is not valid")
        return
    end
    UAdvancedVideoSetting:SetParamGroupQuality("RayTracingAO", enable)
end

local function _RayTracingAmbientOcclusionApplyFunc(settingObj, key, value)
    local enable = value
    VideoSettingLogicHD.SetRayTracingAmbientOcclusion(enable)
end

local function _RegRayTracingAmbientOcclusionBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughGetMapping(id, key)
    SettingRegLogicHD.RegPassThroughSetMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _RayTracingAmbientOcclusionApplyFunc)
end

--------------------Developer
function VideoSettingLogicHD.IsGPUCrashDebuggingRealEnabled()
    if isinvalid(_videoSetting) then
        log("Error : UClientVideoSettingHD is not valid")
        return false
    end
    return _videoSetting:IsGPUCrashDebuggingRealEnabled()
end

function VideoSettingLogicHD.SetGPUCrashDebuggingEnable(enable)
    if isinvalid(_videoSetting) then
        log("Error : UClientVideoSettingHD is not valid")
        return
    end
    _videoSetting:SetGPUCrashDebuggingEnable(enable)
end

------------------Low Memory mode
function VideoSettingLogicHD.IsLowMemoryModeSupported()
    if isinvalid( localT.ULowMemoryQualityManager) then
        log("Error : localT.ULowMemoryQualityManager is not valid")
        return false
    end
    return localT.ULowMemoryQualityManager:CanOpenLowMemoryMode()
end

function localT._LowMemoryModeGetter(settingObj, key, _)
    return settingObj[key]
end

function localT._LowMemoryModeSetter(settingObj, key, value)
    settingObj[key] = value
end

function localT._LowMemoryModeApplyFunc(settingObj, key, value)
    --设置值
    if isinvalid( localT.ULowMemoryQualityManager) then
        log("Error : localT.ULowMemoryQualityManager is not valid")
        return
    end

    if not VideoSettingLogicHD.IsLowMemoryModeSupported() then
        log("IsLowMemoryModeSupported is not ")
        return
    end

    local InitialApplying = CommonSettingLogicHD.GetIsInitialApplying()
    if InitialApplying == false then

        if value then
            localT.ULowMemoryQualityManager:SetLowMemoryMode(1)
        else
            localT.ULowMemoryQualityManager:SetLowMemoryMode(0)
        end

        if value then
            if isinvalid( localT.ULowMemoryQualityManager) then
                log("Error : localT.ULowMemoryQualityManager is not valid")
                return
            end
            -- 执行所有低内存模式设置
            localT.ULowMemoryQualityManager:ExecuteAllLowMemoryConsoleCommand(true)
        else
            localT.ULowMemoryQualityManager:ExecuteAllLowMemoryConsoleCommand(false)
            --todo,@romzhang 执行所有的视频设置
            -- VideoSettingLogicHD.ReApplyGraphicsQuality()
        end

    end
end


function localT._RegLowMemoryModeBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegMapping(
        id,
        settingObj,
        key,
        localT._LowMemoryModeGetter,
        localT._LowMemoryModeSetter,
        localT._LowMemoryModeApplyFunc
    )
end

--------------------NVIDIA Reflex

local function IsReflexSupported()
    -- return true
    return UHardwareParamHelper.HasNVIDIADevice()
end

local function _NVIDIAReflexGetter(settingObj, key, _)
    return settingObj[key]
end

function VideoSettingLogicHD.SetNVIDIAReflex(isOpen)
    if isOpen then
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "t.Streamline.Reflex.Enable 1")
    else
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "t.Streamline.Reflex.Enable 0")
    end
end

local function _NVIDIAReflexSetter(settingObj, key, value)
    settingObj[key] = value
end

local function _NVIDIAReflexApplyFunc(settingObj, key, value)
    VideoSettingLogicHD.SetNVIDIAReflex(value)
end

local function _RegNVIDIAReflexBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegMapping(id, settingObj, key, _NVIDIAReflexGetter, _NVIDIAReflexSetter, _NVIDIAReflexApplyFunc)
end

--------------------NVIDIA Reflex Mode
local function _NVIDIAReflexModeApplyFunc(settingObj, key, value)
    if value == 0 then
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "t.Streamline.Reflex.Enable 0")
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "t.Streamline.Reflex.Mode 0")
    elseif value == 1 then
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "t.Streamline.Reflex.Enable 1")
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "t.Streamline.Reflex.Mode 1")
    elseif value == 2 then
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "t.Streamline.Reflex.Enable 1")
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "t.Streamline.Reflex.Mode 2")
    end
end

local function _RegNVIDIAReflexModeBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _NVIDIAReflexModeApplyFunc)
end

--------------------DLSS Frame Generation
--Frame Generation 开启时，reflex必须打开。所以IsDLSSFrameGenerationSupported 需要包含 IsReflexSupported

local EFrameGenerationType =
{
    None = 0,
    DLSS = 1,
    FSR3 = 2,
    --XESS = 3
}

function VideoSettingLogicHD.UpdateExtraFrameGenerationConditions()
    if CommonSettingLogicHD.GetDataByID("FSR3FrameGeneration") then
        local superResMethod =  CommonSettingLogicHD.GetDataByID("SuperResolutionMethod")
        if superResMethod ==  DFHD_SUPERRES_VALUE.FSR3 then
            UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.FidelityFX.FI.EnabledWithOtherUpscale 0")
        else
            UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.FidelityFX.FI.EnabledWithOtherUpscale 1")
        end
    end
    VideoSettingLogicHD.RefreshSyncAndFPS(false)
end

function VideoSettingLogicHD.SetFSRFrameGenerationOverrideSwapChain(enable)
    if enable then
        UGPGConfigUtils.SetInt("/Script/Engine.RendererSettings", "r.FidelityFX.FI.OverrideSwapChainDX12", 1, "Engine")
        logerror("@jasonscheng set r.FidelityFX.FI.OverrideSwapChainDX12=1")
    else
        UGPGConfigUtils.SetInt("/Script/Engine.RendererSettings", "r.FidelityFX.FI.OverrideSwapChainDX12", 0, "Engine")
        logerror("@jasonscheng set r.FidelityFX.FI.OverrideSwapChainDX12=0")
    end
    local GConfig = FConfigCacheIni.GetGlobalConfig()
    local EngineIni = FConfigCacheIni.LoadGlobalIni("Engine")
    GConfig:Flush(false, EngineIni)
end

function VideoSettingLogicHD.ApplyFrameGeneration()
    local gType = EFrameGenerationType.None
    local superResMethod =  CommonSettingLogicHD.GetDataByID("SuperResolutionMethod")
    if superResMethod == DFHD_SUPERRES_VALUE.DLSS and CommonSettingLogicHD.GetDataByID("DLSSFrameGeneration") then
        gType = EFrameGenerationType.DLSS
    elseif CommonSettingLogicHD.GetDataByID("FSR3FrameGeneration") then
        gType = EFrameGenerationType.FSR3
    -- elseif superResMethod == DFHD_SUPERRES_VALUE.XESS and CommonSettingLogicHD.GetDataByID("XESSFrameGeneration") then
    --     gType = EFrameGenerationType.XESS
    end

    if gType == EFrameGenerationType.None then
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.Streamline.DLSSG.Enable 0")
        UMetaperfUploader.SetDLSSGOpen(false)
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.FidelityFX.FI.Enabled 0")
        VideoSettingLogicHD.SetFSRFrameGenerationOverrideSwapChain(false)
        UMetaperfUploader.SetFFXFIOpen(false)
        --UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.XeFG.Enabled 0")
    elseif gType == EFrameGenerationType.DLSS then
        -- DLSS Frame Generation 暂时屏蔽
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.Streamline.DLSSG.Enable 1")
        UMetaperfUploader.SetDLSSGOpen(true)
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.FidelityFX.FI.Enabled 0")
        VideoSettingLogicHD.SetFSRFrameGenerationOverrideSwapChain(false)
        UMetaperfUploader.SetFFXFIOpen(false)
        --UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.XeFG.Enabled 0")
    -- elseif gType == EFrameGenerationType.XESS then
    --     UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.Streamline.DLSSG.Enable 0")
    --     UMetaperfUploader.SetDLSSGOpen(false)
    --     UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.FidelityFX.FI.Enabled 0")
    --     VideoSettingLogicHD.SetFSRFrameGenerationOverrideSwapChain(false)
    --     UMetaperfUploader.SetFFXFIOpen(false)
    --     UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.XeFG.Enabled 1")
    elseif gType == EFrameGenerationType.FSR3 then
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.Streamline.DLSSG.Enable 0")
        UMetaperfUploader.SetDLSSGOpen(false)
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.FidelityFX.FI.Enabled 1")
        VideoSettingLogicHD.SetFSRFrameGenerationOverrideSwapChain(true)
        UMetaperfUploader.SetFFXFIOpen(true)
        --UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.XeFG.Enabled 0")
    end
    VideoSettingLogicHD.UpdateExtraFrameGenerationConditions()
end

function VideoSettingLogicHD.DLSSFrameGenerationEnabled()
    local superResMethod =  CommonSettingLogicHD.GetDataByID("SuperResolutionMethod")

    if superResMethod == DFHD_SUPERRES_VALUE.DLSS and CommonSettingLogicHD.GetDataByID("DLSSFrameGeneration") then
        return true
    end
    return false
end

function VideoSettingLogicHD.AnyFrameGenerationEnabled()
    if VideoSettingLogicHD.DLSSFrameGenerationEnabled() then
        return true
    end

    if CommonSettingLogicHD.GetDataByID("FSR3FrameGeneration") then
        return true
    end
    return false
end

local function IsDLSSFrameGenerationSupported()
-- BEGIN MODIFICATION - VIRTUOS
    if PLATFORM_WINDOWS == 1 then
        return UStreamlineLibraryDLSSG.IsDLSSGSupported() and IsReflexSupported()
    else
        return false
    end
-- END MODIFICATION - VIRTUOS
end

local function _DLSSFrameGenerationGetter(settingObj, key, _)
    return settingObj[key]
end

local function _DLSSFrameGenerationSetter(settingObj, key, value)
    settingObj[key] = value
end

local function _DLSSFrameGenerationApplyFunc(settingObj, key, value)
    if value == true then
        Module.SystemSetting:SetDataByIDHD(localT.NVIDIAReflexID, true)
        local curItems = Module.SystemSetting.Field:GetAllSettingItemHD()
        local item = curItems[localT.NVIDIAReflexID]
        if item then
            item:ReloadSetting()
        end
    end
    VideoSettingLogicHD.ApplyFrameGeneration()
end

local function _RegDLSSFrameGenerationBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegMapping(
        id,
        settingObj,
        key,
        _DLSSFrameGenerationGetter,
        _DLSSFrameGenerationSetter,
        _DLSSFrameGenerationApplyFunc
    )
end

--------------------FSR3帧生成

function VideoSettingLogicHD.IsFSR3FrameGenerationSupported()
    return false--UPerfGearPipeline.GetRHIType() == ERHIType.D3D12
end

local function _FSR3FrameGenerationApplyFunc(settingObj, key, value)
    VideoSettingLogicHD.ApplyFrameGeneration()
end

local function _RegFSR3FrameGenerationBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _FSR3FrameGenerationApplyFunc)
end

--------------------MotionBlurQuality

function VideoSettingLogicHD.SetMotionBlurQuality(quality)
    quality = math.clamp(quality, 0, 4)
    -- UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), string.format("r.MotionBlurQuality %d", quality))
end

local function _MotionBlurQualityApplyFunc(settingObj, key, value)
    local quality = value
    VideoSettingLogicHD.SetMotionBlurQuality(quality)
end

local function _RegMotionBlurQualityBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _MotionBlurQualityApplyFunc)
end

-- Selective Motion Blur
function VideoSettingLogicHD.SetSelectiveMotionBlurMode()
    local worldMotionBlur = _videoSetting["bWorldMotionBlur"]
    local weaponMotionBlur = _videoSetting["bWeaponMotionBlur"]

    local mode = 0
    if worldMotionBlur then
        mode = mode + 2
    end

    if weaponMotionBlur then
        mode = mode + 1
    end

    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), string.format("r.MotionBlur.SelectiveMode %d", mode))
end

local function _SelectiveMotionBlurApplyFunc(settingObj, key, value)
    local quality = value
    VideoSettingLogicHD.SetSelectiveMotionBlurMode()
end

local function _RegWeaponMotionBlurBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _SelectiveMotionBlurApplyFunc)
end

local function _RegWorldMotionBlurBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _SelectiveMotionBlurApplyFunc)
end

--------------------ReflectionQuality
function VideoSettingLogicHD.SetReflectionQuality(quality)
    if isinvalid(UPerfGearPipeline) then
        log("Error : PerfGearPipeline is not valid")
    end
    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        string.format("PerfGear.GroupQuality Reflection %d", quality)
    )
end

local function _ReflectionQualityApplyFunc(settingObj, key, value)
    value = _TryClampSettingValue("ReflectionQuality", value)
    local PfLevel = CommonSettingLogicHD.GetPfLevel(key, value)
    VideoSettingLogicHD.SetReflectionQuality(PfLevel)
end

local function _RegReflectionQualityBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _ReflectionQualityApplyFunc)
end

--------------------TextureFilteringQuality

function VideoSettingLogicHD.SetTextureFilteringQuality(quality)
    if isinvalid(UPerfGearPipeline) then
        log("Error : PerfGearPipeline is not valid")
    end
    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        string.format("PerfGear.GroupQuality TextureFilteringQuality %d", quality)
    )
end

local function _TextureFilteringQualityApplyFunc(settingObj, key, value)
    value = _TryClampSettingValue("TextureFilteringQuality", value)
    local PfLevel = CommonSettingLogicHD.GetPfLevel(key, value)
    VideoSettingLogicHD.SetTextureFilteringQuality(PfLevel)
end

local function _RegTextureFilteringQualityBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _TextureFilteringQualityApplyFunc)
end

--------------------AmbientOcclusionQuality
function VideoSettingLogicHD.SetAmbientOcclusionQuality(quality)
    if isinvalid(UPerfGearPipeline) then
        log("Error : PerfGearPipeline is not valid")
    end
    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        string.format("PerfGear.GroupQuality AmbientOcclusionQuality %d", quality)
    )
end

local function _AmbientOcclusionQualityApplyFunc(settingObj, key, value)
    value = _TryClampSettingValue("AmbientOcclusionQuality", value)
    local PfLevel = CommonSettingLogicHD.GetPfLevel(key, value)
    VideoSettingLogicHD.SetAmbientOcclusionQuality(PfLevel)
end

local function _RegAmbientOcclusionQualityBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _AmbientOcclusionQualityApplyFunc)
end

--------------------ParticleQuality
function VideoSettingLogicHD.SetParticleQuality(quality)
    if isinvalid(UPerfGearPipeline) then
        log("Error : PerfGearPipeline is not valid")
    end
    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        string.format("PerfGear.GroupQuality ParticleQuality %d", quality)
    )
end

local function _ParticleQualityApplyFunc(settingObj, key, value)
    value = _TryClampSettingValue("ParticleQuality", value)
    local PfLevel = CommonSettingLogicHD.GetPfLevel(key, value)
    VideoSettingLogicHD.SetParticleQuality(PfLevel)
end

local function _RegParticleQualityBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _ParticleQualityApplyFunc)
end

--------------------DistortionQuality
function VideoSettingLogicHD.SetDistortionQuality(quality)
    if isinvalid(UPerfGearPipeline) then
        log("Error : PerfGearPipeline is not valid")
    end
    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        string.format("PerfGear.GroupQuality DistortionQuality %d", quality)
    )
end

local function _DistortionQualityApplyFunc(settingObj, key, value)
    value = _TryClampSettingValue("DistortionQuality", value)
    local PfLevel = CommonSettingLogicHD.GetPfLevel(key, value)
    VideoSettingLogicHD.SetDistortionQuality(PfLevel)
end

local function _RegDistortionQualityBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _DistortionQualityApplyFunc)
end

--------------------ShadingQuality
function VideoSettingLogicHD.SetShadingQuality(quality)
    if isinvalid(UPerfGearPipeline) then
        log("Error : PerfGearPipeline is not valid")
    end
    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        string.format("PerfGear.GroupQuality ShadingQuality %d", quality)
    )
end

local function _ShadingQualityApplyFunc(settingObj, key, value)
    value = _TryClampSettingValue("ShadingQuality", value)
    local PfLevel = CommonSettingLogicHD.GetPfLevel(key, value)
    VideoSettingLogicHD.SetShadingQuality(PfLevel)
end

local function _RegShadingQualityBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _ShadingQualityApplyFunc)
end

--------------------TextureQuality
function VideoSettingLogicHD.SetTextureQuality(quality)
    if isinvalid(UPerfGearPipeline) then
        log("Error : PerfGearPipeline is not valid")
    end
    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        string.format("PerfGear.GroupQuality TextureQuality %d", quality)
    )
end

local function _TextureQualityApplyFunc(settingObj, key, value)
    value = _TryClampSettingValue("TextureQuality", value)
    local PfLevel = CommonSettingLogicHD.GetPfLevel(key, value)
    VideoSettingLogicHD.SetTextureQuality(PfLevel)
end

local function _RegTextureQualityBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _TextureQualityApplyFunc)
end

--------------------StreamingQuality
function VideoSettingLogicHD.SetStreamingQuality(quality)
    if isinvalid(UPerfGearPipeline) then
        log("Error : PerfGearPipeline is not valid")
    end
    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        string.format("PerfGear.GroupQuality StreamingQuality %d", quality)
    )
end

local function _StreamingQualityApplyFunc(settingObj, key, value)
    value = _TryClampSettingValue("StreamingQuality", value)
    local PfLevel = CommonSettingLogicHD.GetPfLevel(key, value)
    VideoSettingLogicHD.SetStreamingQuality(PfLevel)
end

local function _RegStreamingQualityBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _StreamingQualityApplyFunc)
end

--------------------ShadowQuality
function VideoSettingLogicHD.SetShadowQuality(quality)
    if isinvalid(UPerfGearPipeline) then
        log("Error : PerfGearPipeline is not valid")
    end
    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        string.format("PerfGear.GroupQuality ShadowQuality %d", quality)
    )
end

local function _ShadowQualityApplyFunc(settingObj, key, value)
    value = _TryClampSettingValue("ShadowQuality", value)
    local PfLevel = CommonSettingLogicHD.GetPfLevel(key, value)
    VideoSettingLogicHD.SetShadowQuality(PfLevel)
end

local function _RegShadowQualityBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _ShadowQualityApplyFunc)
end

--------------------ShadowMapResolution
function VideoSettingLogicHD.SetShadowMapResolution(quality)
    if isinvalid(UPerfGearPipeline) then
        log("Error : PerfGearPipeline is not valid")
    end
    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        string.format("PerfGear.GroupQuality ShadowMapResolution %d", quality)
    )
end

local function _ShadowMapResolutionApplyFunc(settingObj, key, value)
    value = _TryClampSettingValue("ShadowMapResolution", value)
    local PfLevel = CommonSettingLogicHD.GetPfLevel(key, value)
    VideoSettingLogicHD.SetShadowMapResolution(PfLevel)
end

local function _RegShadowMapResolutionBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _ShadowMapResolutionApplyFunc)
end

--------------------PostProcessQuality
function VideoSettingLogicHD.SetPostProcessQuality(quality)
    if isinvalid(UPerfGearPipeline) then
        log("Error : PerfGearPipeline is not valid")
    end
    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        string.format("PerfGear.GroupQuality PostProcessQuality %d", quality)
    )
end

local function _PostProcessQualityApplyFunc(settingObj, key, value)
    value = _TryClampSettingValue("PostProcessQuality", value)
    local PfLevel = CommonSettingLogicHD.GetPfLevel(key, value)
    VideoSettingLogicHD.SetPostProcessQuality(PfLevel)
end

local function _RegPostProcessQualityBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _PostProcessQualityApplyFunc)
end

--------------------FoliageQuality
function VideoSettingLogicHD.SetFoliageQuality(quality)
    if isinvalid(UPerfGearPipeline) then
        log("Error : PerfGearPipeline is not valid")
    end
    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        string.format("PerfGear.GroupQuality FoliageQuality %d", quality)
    )
end

local function _FoliageQualityApplyFunc(settingObj, key, value)
    local PfLevel = CommonSettingLogicHD.GetPfLevel(key, value)
    VideoSettingLogicHD.SetFoliageQuality(PfLevel)
end

local function _RegFoliageQualityBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _FoliageQualityApplyFunc)
end

--------------------AnimationQuality
function VideoSettingLogicHD.SetAnimationQuality(quality)
    if isinvalid(UPerfGearPipeline) then
        log("Error : PerfGearPipeline is not valid")
    end
    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        string.format("PerfGear.GroupQuality AnimationQuality %d", quality)
    )
end

local function _AnimationQualityApplyFunc(settingObj, key, value)
    value = _TryClampSettingValue("AnimationQuality", value)
    local PfLevel = CommonSettingLogicHD.GetPfLevel(key, value)
    VideoSettingLogicHD.SetAnimationQuality(PfLevel)
end

local function _RegAnimationQualityBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _AnimationQualityApplyFunc)
end

--------------------SceneDetailLevel
function VideoSettingLogicHD.SetSceneDetailLevel(level)
    if isinvalid(UPerfGearPipeline) then
        log("Error : PerfGearPipeline is not valid")
    end
    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        string.format("PerfGear.GroupQuality SceneDetailLevel %d", level)
    )
end

local function _SceneDetailLevelApplyFunc(settingObj, key, value)
    value = _TryClampSettingValue("SceneDetailLevel", value)
    local PfLevel = CommonSettingLogicHD.GetPfLevel(key, value)
    VideoSettingLogicHD.SetSceneDetailLevel(PfLevel)
end

local function _RegSceneDetailLevelBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _SceneDetailLevelApplyFunc)
end

--------------------ViewDistanceLevel
function VideoSettingLogicHD.SetViewDistanceLevel(level)
    if isinvalid(UPerfGearPipeline) then
        log("Error : PerfGearPipeline is not valid")
    end
    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        string.format("PerfGear.GroupQuality ViewDistanceLevel %d", level)
    )
end

local function _ViewDistanceLevelApplyFunc(settingObj, key, value)
    value = _TryClampSettingValue("ViewDistanceLevel", value)
    local PfLevel = CommonSettingLogicHD.GetPfLevel(key, value)
    VideoSettingLogicHD.SetViewDistanceLevel(PfLevel)
end

local function _RegViewDistanceLevelBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _ViewDistanceLevelApplyFunc)
end

--------------------ArtStyle
function VideoSettingLogicHD._ArtStyleApplyFunc(settingObj, key, value)
    -- local cmd = string.format("r.EnableLMTLUT %d", value)
    -- UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), cmd)
end

local function _RegArtStyleBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, VideoSettingLogicHD._ArtStyleApplyFunc)
end

--------------------bDepthOfFieldADS
function VideoSettingLogicHD.SetDepthOfFieldADS(level)
    -- if enable then
    --     _videoSetting.bDepthOfFieldADS = true
    -- else
    --     _videoSetting.bDepthOfFieldADS = false
    -- end

    if isinvalid(UPerfGearPipeline) then
        log("Error : PerfGearPipeline is not valid")
    end
    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        string.format("PerfGear.GroupQuality DepthOfFieldADS %d", level)
    )
end

local function _DepthOfFieldADSApplyFunc(settingObj, key, value)
    local PfLevel = CommonSettingLogicHD.GetPfLevel(key, _BoolToInt(value))
    VideoSettingLogicHD.SetDepthOfFieldADS(PfLevel)
end

local function _RegDepthOfFieldADSBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _DepthOfFieldADSApplyFunc)
end

--------------------Brightness

local function ConvertGamma(Percent)
    local alpha = (Percent - 50) / 50
    local minBrightness = 0.357
    local maxBrightness = 2
    local rate = 1
    if alpha >= 0 then
        rate = UKismetMathLibrary.MapRangeClamped(alpha, 0, 1, 1, 1 / maxBrightness)
    else
        rate = UKismetMathLibrary.MapRangeClamped(alpha, -1, 0, 1 / minBrightness, 1)
    end

    -- local gamma = 1 + (Percent - 50) / 50
    -- gamma = math.max(0, gamma)
    -- return gamma * 2.2

    return 1.0 / rate * 2.2
end

local function _JumpToBrightness()
    Facade.UIManager:AsyncShowUI(UIName2ID.BrightnessSettingHDPanel)
end

local function _BrightnessApplyFunc(settingObj, key, value)
    UClientVideoSettingHD.SetGamma(GetGameInstance(), ConvertGamma(value))
end

local function _RegBrightnessBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _BrightnessApplyFunc)
end

--------------------GraphicsPreset

function VideoSettingLogicHD.PresetIndexToLevel(index)
    local level = EGraphicsQualityLevelHD.BELOW
    if index == 2 then
        level = EGraphicsQualityLevelHD.LOW
    elseif index == 3 then
        level = EGraphicsQualityLevelHD.MID
    elseif index == 4 then
        level = EGraphicsQualityLevelHD.HIGH
    elseif index == 5 then
        level = EGraphicsQualityLevelHD.EPIC
    elseif index == 6 then
        level = EGraphicsQualityLevelHD.WILD
    end
    return level
end

local function _GraphicsPresetApplyFunc(settingObj, key, value)
end
local function _RegGraphicsPresetBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _GraphicsPresetApplyFunc)
end

--------------------MaxFPS
localT.fpsArr = {0, 30, 60, 75, 100, 120, 144, 165, 240}
localT.fpsArrBackgound = {0, 1, 5, 10, 30, 60, 75, 100, 120, 144, 165, 240}
localT.syncAndFPSRefreshFrame = 0

function VideoSettingLogicHD.RefreshSyncAndFPS(bFPSOnly, bForce)
    if IsConsole() then
        if Facade.GameFlowManager:CheckIsInFrontEnd() then  --- 局外
            UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.DynamicRes.OperationMode 0")  --- 关闭动态分辨率
        else  --- 局内
            UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.DynamicRes.OperationMode 2")  --- 开启动态分辨率
        end
    end

    -- 防止多次调用
    if (not bForce) and (UVideoSettingHelper.GetGFrame() == localT.syncAndFPSRefreshFrame) then
        return
    end
    localT.syncAndFPSRefreshFrame = UVideoSettingHelper.GetGFrame()
    -- 最高优先级，BHD休眠

    local cpuEnergyController
    local bhdHelper = UDFBHDHelper.GetInstance(GetGameInstance())
    if bhdHelper then
        cpuEnergyController = bhdHelper:GetSubEnergyControllerByClass(UDFBHDCPUController)
    end

    if cpuEnergyController and cpuEnergyController:IsLimitingMaxFPS(GetGameInstance()) then
        -- 关掉所有的XXSync
        if not bFPSOnly then
            UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.VSync 0")
            UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.NvidiaAppProfile.FastSync 0")
        end

        local fps =  cpuEnergyController:GetMaxFPSBackground(GetGameInstance())
        VideoSettingLogicHD._SetMaxFPSByIndex(0, true, fps)
    else
        -- 开了帧生成，就不能再开锁帧和垂直同步
        if VideoSettingLogicHD.AnyFrameGenerationEnabled() then
            if not bFPSOnly then
                UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.NvidiaAppProfile.FastSync 0")
                UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.VSync 0")
            end
            VideoSettingLogicHD._SetMaxFPSByIndex(0)
            return
        end

        -- 先检查是否开着XXSync
        if CommonSettingLogicHD.GetDataByID("VSync") then
            if not bFPSOnly then
                UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.NvidiaAppProfile.FastSync 0")
                UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.VSync 1")
            end
            VideoSettingLogicHD._SetMaxFPSByIndex(0)
        else
            if CommonSettingLogicHD.GetDataByID("FastSync") then
                if not bFPSOnly then
                    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.VSync 0")
                    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.NvidiaAppProfile.FastSync 1")
                end
                VideoSettingLogicHD._SetMaxFPSByIndex(0)
            else
                -- 什么Sync都没开的情况
                -- 关掉所有的XXSync
                if not bFPSOnly then
                    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.VSync 0")
                    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.NvidiaAppProfile.FastSync 0")
                end

                local fpsIdx = 1
                local bBackgound = false
                if UGPGameViewportClient.IsInBackground() then
                    fpsIdx = CommonSettingLogicHD.GetDataByID("MaxFPSBackground")
                    bBackgound = true
                else
                    if Facade.GameFlowManager:CheckIsInFrontEnd() then
                        fpsIdx = CommonSettingLogicHD.GetDataByID("MaxFPSFrontEnd")
                    else
                        fpsIdx = CommonSettingLogicHD.GetDataByID("MaxFPS")
                    end
                end
                VideoSettingLogicHD._SetMaxFPSByIndex(fpsIdx, bBackgound)
            end
        end
    end
end

function VideoSettingLogicHD._SetMaxFPSByIndex(index, bBackgound, overrideFPS)
    local fps = 0
    if overrideFPS then
        fps = overrideFPS
    else
        if bBackgound then
            fps = localT.fpsArrBackgound[index + 1]
        else
            fps = localT.fpsArr[index + 1]
        end
    end
    local gameUserSettings = UGameUserSettings.GetGameUserSettings()
    gameUserSettings:SetFrameRateLimit(fps)
    gameUserSettings:ApplyNonResolutionSettings()
end

local function _MaxFPSApplyFunc(settingObj, key, value)
    if CommonSettingLogicHD.GetDataByID("VSync") then
        return
    end
    VideoSettingLogicHD.RefreshSyncAndFPS()
end

local function _RegMaxFPSBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _MaxFPSApplyFunc)
end

function VideoSettingLogicHD._OnBackgroundStateChanged()
    VideoSettingLogicHD.RefreshSyncAndFPS(true, true)
end

function VideoSettingLogicHD._OnBHDFPSLimitStateChanged()
    VideoSettingLogicHD.RefreshSyncAndFPS(true, true)
end

--------------------VSync
local function _VSyncApplyFunc(settingObj, key, value)
    if value then
        VideoSettingLogicHD._SetMaxFPSByIndex(0)
        -- 开启VSync后，关闭FastSync
        Module.SystemSetting:SetDataByIDHD("FastSync", false)
        local curItems = Module.SystemSetting.Field:GetAllSettingItemHD()
        local item = curItems["FastSync"]
        if item then
            item:ReloadSetting()
        end
    end
    VideoSettingLogicHD.RefreshSyncAndFPS()
end

local function _RegVSyncBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _VSyncApplyFunc)
end


--------------------Console Perf Mode， 用于指定主机平台使用画质优先还是帧率优先
local function _RegConsolePerfMode(id)
    local function _ConsolePerfModeGetter(settingObj, _)
        local hasConfig = false
        local perfMode = 0
        hasConfig, perfMode = UGPGConfigUtils.GetInt("ConsoleVideoSetting","PerfMode",nil,"DeviceProfiles")
        if hasConfig then
            return perfMode
        end
        return 0
    end

    local function _ConsolePerfModeSetter(settingObj, _, value)
        if value == 0 then --- graphic first
            UGPGConfigUtils.SetInt("ConsoleVideoSetting","PerfMode",0,"DeviceProfiles")
        else --- performance first
            UGPGConfigUtils.SetInt("ConsoleVideoSetting","PerfMode",1,"DeviceProfiles")
        end

    end

    local QualityFirstCommands_XSX = {
        "r.DynamicRes.FrameTimeBudget 16.6",
        "r.DynamicRes.MinScreenPercentage 75",
        "sg.GameplayQuality 3",
    }

    local QualityFirstCommands_PS5 = {
        "r.DynamicRes.FrameTimeBudget 16.6",
        "r.DynamicRes.MinScreenPercentage 75",
        "sg.GameplayQuality 3",
    }

    local FrameRateFirstCommands_XSX = {
        "r.DynamicRes.FrameTimeBudget 8.33",
        "r.DynamicRes.MinScreenPercentage 50",
        "sg.GameplayQuality 5",
    }

    local FrameRateFirstCommands_PS5 = {
        "r.DynamicRes.FrameTimeBudget 8.33",
        "r.DynamicRes.MinScreenPercentage 50",
        "sg.GameplayQuality 5",
    }

    local HighFrameRateScalabilityConfig = {
        ["fx.maxImpact"]=2,
        ["fx.maxMuzzleFlash"]=2,
        ["fx.maxTrail"]=2,
        ["fx.EnableFXExplodeLimit"]=0,
        ["fx.EnableFXSkillLimit"]=0,
        ["fx.EnableFXOtherLimit"]=0,
        ["r.SkeletalMeshLODBias"]=0,
        ["au.SetWwiseQuality"]=0,
        ["au.Set3PGunFireLimit"]=2,
        ["au.GPAudio3PReverb"]=0,
        ["au.GPAudio1PReverb"]=0,
        ["au.GPAudioRoomQuery"]=1,
        ["au.GPAudioSlapback"]=0,
        ["au.GPAudioLimitLOD"]=2,
        ["au.GPAudioCurveScale"]=1.2,
        ["au.GPAudioEnableFloorCheck"]=1,
        ["au.GPAudio3PFireLoopPriorityQuality"]=2,
        ["au.GPAudioPerf6"]=0,
        ["au.GlobalVoiceLimit"]=45,
        ["au.GPAudioDolbyEnable"]=1,
        ["au.GPAudioMpLowQ"]=0,
        ["au.UnderWaterLowQ"]=0,
        ["au.GPAudio3PGunFireLowQ"]=0,
        ["au.UnderWaterLowperf"]=0,
        ["r.AnimLODForQuality"]=1,
        ["a.Animation.ForceTppOptInLowestConfig"]=0,
        ["a.Ragdoll.MaxNum"]=1,
        ["a.Animation.InLowMemoryMode"]=0,
        ["Vehicle.CVarEnableWheeledVehicleSweep"]=0,
        ---;@haoyusui 载具六档机
        ["a.Vehicle.PerfQuality6"]=0,
        ---;---开火特化BEGIN---
        ["weapon.EnableShellDrop"]=1,
        ["weapon.EnableMuzzleTrail"]=1,
        ["weapon.EnableFireDynamicLighting"]=1,
        ["weapon.MuzzleFlashIntervalTime"]=0,
        ["g.ImpactEffects.CullDistanceFor3P"]=100,
        ["weapon.EnableShellDropAudioFor3P"]=false,
        ["Weapon.Enable3PFireAnimCull"]=false,
        ["weapon.Enable3PTraceAmbientAudio"]=true,
        ["Weapon.Bullet.EnableWarmupView"]=false,
        ["Weapon.EnablePlayAmmoUpdateAnimation"]=false,
        ["weapon.EnableMuzzleFlash3P"]=true,
        ["weapon.EnableShellDropFor3P"]=true,
        ["weapon.EnableMuzzleTrailFor3P"]=true,
        ["weapon.EnableWeaponOverHeatTrailFor3P"]=true,
        ---;---开火特化END---
        ["Weapon.Optimize.EnableLaserHighFrequencyUpdate"]=false,
        ["Weapon.Trace.EnableSimplificationTrace"]=true,
        ["Weapon.DisableDecalComponentFadeout"]=false,
        ["Weapon.DecalComponentPoolMode"]=0,
        ---;@vincewzhang, EffectSchedulerBudget
        ["r.EffectScheduler.DynamicFXGPUScoreBudget"]=80000,
        ["r.EffectScheduler.ScreenEffectGPUScoreBudget"]=60000,
        ---;@vincewzhang, EffectSchedulerBudget END
        ---;3P 武器模型按类型提供几套固定外观和动画
        ["Weapon.EnableWeaponMatchFromInUseObjects"]=0,
        ["Weapon.Pool.WeaponDiffOptionOverride"]=0,
        ["Weapon.Optimize.EnableApproximateWeaponDesc"]=0,
        ---;3P 武器模型按类型提供几套固定外观和动画 END
        ---;武器Lod
        ["Weapon.Optimize.WeaponMinLod"]=0,
        ---;武器Lod END,
        ["Weapon.Bullet.InstantHitDistance"]=0,
        ["interactor.EnableInteractorOptInLowLevel"]=0,
        ["weapon.ZoyaSwarms.LowMoveMode"]=0,
        ["VehiclePhysicsReplication.MinDistaneToDisableSimulatingStateOfSimulatedVehicle"]=5000.0,
        ["r.CharacterUseMeshMerge.ForceExtraPartInTrunk"]=0,
    }

    for k,v in pairs(HighFrameRateScalabilityConfig) do
        if type(v) == "boolean" then
            UGPGConfigUtils.SetBool("GameplayQuality@5",k,v,"Scalability")
        elseif type(v) == "number" then
            if math.type(v) == "integer" then
                UGPGConfigUtils.SetInt("GameplayQuality@5",k,v,"Scalability")
            else
                UGPGConfigUtils.SetFloat("GameplayQuality@5",k,v,"Scalability")
            end
        end
    end
    

    local function _ConsolePerfModeApplyFunc(settingObj, key, value)
        print("Apply console perf mode value - ",value)
        
        local QualityFirstCommands = {}
        local FrameRateFirstCommands = {}

        if IsXSX() then
            QualityFirstCommands = QualityFirstCommands_XSX
            FrameRateFirstCommands = FrameRateFirstCommands_XSX
        end

        if IsPS5() then
            QualityFirstCommands = QualityFirstCommands_PS5
            FrameRateFirstCommands = FrameRateFirstCommands_PS5
        end

        if value == 0 then --- Graphic Quality First
            for i,cmd in ipairs(QualityFirstCommands) do
                UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), cmd)
            end
        else --- Frame First
            for i,cmd in ipairs(FrameRateFirstCommands) do
                UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), cmd)
            end
        end
    end

    local settingObj = _videoSetting
    SettingRegLogicHD.RegMapping(id, settingObj, nil, _ConsolePerfModeGetter, _ConsolePerfModeSetter, _ConsolePerfModeApplyFunc)
end

--------------------Fast Sync

local function IsFastSyncSupported()
    return UHardwareParamHelper.HasNVIDIADevice()
end

local function _FastSyncApplyFunc(settingObj, key, value)
    VideoSettingLogicHD.RefreshSyncAndFPS()
end

local function _RegFastSyncBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _FastSyncApplyFunc)
end



--------------------Sharpen
function VideoSettingLogicHD._SharpenApplyFunc(settingObj, key, value)
    local sharpen = UKismetMathLibrary.MapRangeClamped(value, 0, 100, 0, 4)
    local cmd = string.format("r.Tonemapper.Sharpen %f", sharpen)
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), cmd)
end

local function _RegSharpenBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, VideoSettingLogicHD._SharpenApplyFunc)
end

--------------------RenderScale
function VideoSettingLogicHD._RenderScaleApplyFunc(settingObj, key, value)
    if CommonSettingLogicHD.GetDataByID("SuperResolutionMethod") ~= DFHD_SUPERRES_VALUE.None then
        return
    end
    local cmd = string.format("r.ScreenPercentage %d", value)
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), cmd)
end

local function _RegRenderScaleBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, VideoSettingLogicHD._RenderScaleApplyFunc)
end

--------------------VolumetricFogQuality

local function _VolumetricFogQualityApplyFunc(settingObj, key, value)
    value = _TryClampSettingValue("VolumetricFogQuality", value)

    if isinvalid(UPerfGearPipeline) then
        log("Error : PerfGearPipeline is not valid")
    end

    local PfLevel = CommonSettingLogicHD.GetPfLevel(key, value)

    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        string.format("PerfGear.GroupQuality VolumetricFogQuality %d", PfLevel)
    )
end

local function _RegVolumetricFogQualityBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _VolumetricFogQualityApplyFunc)
end

--------------------GamePlayQuality
function VideoSettingLogicHD.SetGamePlayQuality(quality)
    if isinvalid(UPerfGearPipeline) then
        log("Error : PerfGearPipeline is not valid")
    end
    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        string.format("PerfGear.GroupQuality GameplayQuality %d", quality)
    )
end

local function _GamePlayQualityApplyFunc(settingObj, key, value)
    local PfLevel = CommonSettingLogicHD.GetPfLevel(key, value)
    VideoSettingLogicHD.SetGamePlayQuality(PfLevel)
end

local function _RegGamePlayQualityBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _GamePlayQualityApplyFunc)
end

--------------------GIQuality
function VideoSettingLogicHD.SetGIQuality(quality)
    if isinvalid(UPerfGearPipeline) then
        log("Error : PerfGearPipeline is not valid")
    end
    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        string.format("PerfGear.GroupQuality GIQuality %d", quality)
    )
end

local function _GIQualityApplyFunc(settingObj, key, value)
    value = _TryClampSettingValue("GIQuality", value)
    local PfLevel = CommonSettingLogicHD.GetPfLevel(key, value)
    VideoSettingLogicHD.SetGIQuality(PfLevel)
end

local function _RegGIQualityBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, _GIQualityApplyFunc)
end

---------------------------bScopeUseRT
function VideoSettingLogicHD.ScopeUseRTSupported()
    if not UClientSettingHelperHD.IsEditor() then
        local systemInfoHD = UVideoSettingHelper.GetSystemInfoHD()
        local gpuLevel = UVideoSettingHelper.GetGPUBenchmarkLevelHD("", false)
        return systemInfoHD.VRam >= 8 and gpuLevel >= EGraphicsQualityLevelHD.HIGH
    end
    return true
end

local function ScopeUseRT_MobileFunc(nilParam, value)
    _gameSettingMobileClass.Get(GetWorld()):SetScopeUseRT(value)
    _gameSettingMobileClass.Get(GetWorld()):SaveDataConfig()

    CommonSettingLogicHD.GPMReportParam("GameSetting", "bScopeUseRT", value)
end

local function _RegScopeUseRTBatch(id, key)
    SettingRegLogicHD.RegPassThroughMapping("bScopeUseRT", "bScopeUseRT")
    SettingRegLogicHD.RegMobileMapping("bScopeUseRT", nil, ScopeUseRT_MobileFunc, nil)
end


function VideoSettingLogicHD.ApplySettingData()
    -- local res = VideoSettingLogicHD.GetRealResolutionByIndex(_videoSetting.Resolution)
    -- VideoSettingLogicHD.SetScreenResolution(res)
    -- _InnerApplyDisplayMode(_videoSetting.DisplayMode)
    -- VideoSettingLogicHD.SetAntiAliasingMethod(_videoSetting.AntiAliasingMethod)
    -- VideoSettingLogicHD.SetDLSSQuality(_videoSetting.DLSSQuality)
    -- VideoSettingLogicHD.SetMotionBlurQuality(_videoSetting.MotionBlurQuality)
    -- VideoSettingLogicHD.SetReflectionQuality(_videoSetting.ReflectionQuality)
    -- VideoSettingLogicHD.SetTextureFilteringQuality(_videoSetting.TextureFilteringQuality)
    -- VideoSettingLogicHD.SetAmbientOcclusionQuality(_videoSetting.AmbientOcclusionQuality)
    -- VideoSettingLogicHD.SetParticleQuality(_videoSetting.ParticleQuality)
    -- VideoSettingLogicHD.SetSceneDetailLevel(_videoSetting.SceneDetailLevel)
    -- VideoSettingLogicHD.SetViewDistanceLevel(_videoSetting.ViewDistanceLevel)
    -- VideoSettingLogicHD.SetVSyncEnable(_videoSetting.VSync)
    -- VideoSettingLogicHD.SetDepthOfFieldADS(_videoSetting.bDepthOfFieldADS)
    -- VideoSettingLogicHD.SetRayTracingQuality(_videoSetting.RayTracingQuality)
    -- VideoSettingLogicHD.SetRayTracingGlobalIllumination(_videoSetting.RayTracingGlobalIllumination)
    -- VideoSettingLogicHD.SetRayTracingReflections(_videoSetting.RayTracingReflections)
end

VideoSettingLogicHD.APMUploadIDTable = {
    --显示
    "DisplayMode",
    -- "Resolution",
    "RefreshRate",
    "Brightness",
    "MaxFPS",
    "MaxFPSFrontEnd",
    "Sharpen",
    "VSync",
    "FastSync",
    --视角同步
    "FPP_FOV",
    --基础画质
    "GraphicsPreset",
    "ArtStyle",
    -- "AntiAliasingMethod",
    -- "bWorldMotionBlur",
    "bWeaponMotionBlur",
    "ReflectionQuality",
    "TextureFilteringQuality",
    "AmbientOcclusionQuality",
    "ParticleQuality",
    "DistortionQuality",
    "SceneDetailLevel",
    "ViewDistanceLevel",
    "LowMemoryMode",
    --高级画质
    "RenderScale",
    "bDepthOfFieldADS",
    "GIQuality",
    "ShadingQuality",
    "TextureQuality",
    "StreamingQuality",
    "ShadowQuality",
    "ShadowMapResolution",
    "PostProcessQuality",
    -- "FoliageQuality",
    "VolumetricFogQuality",
    "AnimationQuality",
    --超分辨率方法
    "SuperResolutionMethod",
    "SuperResolutionQualityDLSS",
    "SuperResolutionQualityTSR",
    "SuperResolutionQualityFSR2",
    "SuperResolutionQualityFSR3",
    "SuperResolutionQualityXESS",
    "DLSSFrameGeneration",
    "NVIDIAReflexMode",
    "FSR3FrameGeneration",
    --光线追踪
    "RayTracingEnabled",
    "RayTracingReflections",
    --开发设置
    "GPUCrashDebugging",
}

function VideoSettingLogicHD.Register()
    SettingRegLogicHD.BeginRegSection(_videoSetting)
    _RegDisplayModeBatch("DisplayMode", "DisplayMode")
    _RegResolutionBatch("Resolution", "Resolution")
    _RegAnitAliasingMethodBatch("AntiAliasingMethod", "AntiAliasingMethod")
    _RegSuperResolutionMethodBatch("SuperResolutionMethod", "SuperResolutionMethod")
    _RegSuperResolutionQualityDLSSBatch("SuperResolutionQualityDLSS", "SuperResolutionQualityDLSS")
    _RegSuperResolutionQualityTSRBatch("SuperResolutionQualityTSR", "SuperResolutionQualityTSR")
    VideoSettingLogicHD._RegSuperResolutionQualityFSR2Batch("SuperResolutionQualityFSR2", "SuperResolutionQualityFSR2")
    VideoSettingLogicHD._RegSuperResolutionQualityFSR3Batch("SuperResolutionQualityFSR3", "SuperResolutionQualityFSR3")
    _RegSuperResolutionQualityXESSBatch("SuperResolutionQualityXESS", "SuperResolutionQualityXESS")
    _RegFSR3FrameGenerationBatch("FSR3FrameGeneration", "FSR3FrameGeneration")
    _RegViewportRatioBatch("ViewportRatio", "ViewportRatio")

    _RegDLSSFrameGenerationBatch("DLSSFrameGeneration", "DLSSFrameGeneration")
    _RegNVIDIAReflexBatch("NVIDIAReflex", "NVIDIAReflex")
    _RegNVIDIAReflexModeBatch("NVIDIAReflexMode","NVIDIAReflexMode")

    _RegFastSyncBatch("FastSync","FastSync")

    -- _RegMotionBlurQualityBatch("MotionBlurQuality", "MotionBlurQuality")
    _RegReflectionQualityBatch("ReflectionQuality", "ReflectionQuality")
    _RegTextureFilteringQualityBatch("TextureFilteringQuality", "TextureFilteringQuality")
    _RegAmbientOcclusionQualityBatch("AmbientOcclusionQuality", "AmbientOcclusionQuality")
    _RegParticleQualityBatch("ParticleQuality", "ParticleQuality")
    _RegDistortionQualityBatch("DistortionQuality", "DistortionQuality")

    _RegWeaponMotionBlurBatch("bWeaponMotionBlur", "bWeaponMotionBlur")
    _RegWorldMotionBlurBatch("bWorldMotionBlur", "bWorldMotionBlur")

    _RegShadingQualityBatch("ShadingQuality", "ShadingQuality")
    _RegTextureQualityBatch("TextureQuality", "TextureQuality")
    _RegStreamingQualityBatch("StreamingQuality", "StreamingQuality")
    _RegShadowQualityBatch("ShadowQuality", "ShadowQuality")
    _RegShadowMapResolutionBatch("ShadowMapResolution", "ShadowMapResolution")
    _RegPostProcessQualityBatch("PostProcessQuality", "PostProcessQuality")
    -- _RegFoliageQualityBatch("FoliageQuality", "FoliageQuality")
    _RegAnimationQualityBatch("AnimationQuality", "AnimationQuality")

    _RegSceneDetailLevelBatch("SceneDetailLevel", "SceneDetailLevel")
    _RegViewDistanceLevelBatch("ViewDistanceLevel", "ViewDistanceLevel")
    _RegVSyncBatch("VSync", "VSync")
    if IsConsole() then
        --- 临时关闭，等待资源合回后恢复
        --- _RegConsolePerfMode("PerfMode_Console")
    end
    _RegDepthOfFieldADSBatch("bDepthOfFieldADS", "bDepthOfFieldADS")
    _RegBrightnessBatch("Brightness", "Brightness")
    _RegRayTracingEnabledBatch("RayTracingEnabled", "RayTracingEnabled")
    -- _RegRayTracingQualityBatch("RayTracingQuality", "RayTracingQuality")
    -- _RegRayTracingGlobalIlluminationBatch("RayTracingGlobalIllumination", "RayTracingGlobalIllumination")
    _RegRayTracingReflectionsBatch("RayTracingReflections", "RayTracingReflections")
    -- _RegRayTracingShadowsBatch("RayTracingShadows", "RayTracingShadows")
    -- _RegRayTracingAmbientOcclusionBatch("RayTracingAmbientOcclusion", "RayTracingAmbientOcclusion")
    _RegGraphicsPresetBatch("GraphicsPreset", "GraphicsPreset")
    _RegMonitorIDBatch("MonitorID", "MonitorID")
    _RegAdapterIDBatch("AdapterID", "AdapterID")
    _RegRefreshRateBatch("RefreshRate", "RefreshRate")
    _RegMaxFPSBatch("MaxFPS", "MaxFPS")
    _RegMaxFPSBatch("MaxFPSFrontEnd", "MaxFPSFrontEnd")
    _RegMaxFPSBatch("MaxFPSBackground", "MaxFPSBackground")
    _RegRenderScaleBatch("RenderScale", "RenderScale")
    _RegSharpenBatch("Sharpen", "Sharpen")

    _RegVolumetricFogQualityBatch("VolumetricFogQuality", "VolumetricFogQuality")
    _RegGamePlayQualityBatch("GameplayQuality", "GameplayQuality")
    _RegGIQualityBatch("GIQuality", "GIQuality")
    _RegArtStyleBatch("ArtStyle", "ArtStyle")

    _RegScopeUseRTBatch("bScopeUseRT", "bScopeUseRT")
    localT._RegLowMemoryModeBatch("LowMemoryMode","LowMemoryMode")

    SettingRegLogicHD.RegPassThroughMapping("bEyesProtection", "bEyesProtection")

    SettingRegLogicHD.RegPrerequisite(
        "Resolution",
        "DisplayMode",
        function(value)
            return value ~= 1
        end
    )

    SettingRegLogicHD.RegPrerequisite(
        "RefreshRate",
        "DisplayMode",
        function(value)
            return value == EWindowMode.Fullscreen
        end
    )

    SettingRegLogicHD.RegPrerequisite("RefreshRate", "Resolution")

    SettingRegLogicHD.RegPrerequisite("Resolution", "MonitorID")

    SettingRegLogicHD.RegPrerequisite(
        "MonitorID",
        "DisplayMode",
        function(value)
            return value ~= 2
        end
    )

    SettingRegLogicHD.RegPrerequisite(
        "AntiAliasingMethod",
        "SuperResolutionMethod",
        function(value)
            return value == DFHD_SUPERRES_VALUE.None
        end
    )

    SettingRegLogicHD.RegPrerequisite(
        "RenderScale",
        "SuperResolutionMethod",
        function(value)
            return value == DFHD_SUPERRES_VALUE.None
        end
    )

    SettingRegLogicHD.RegPrerequisite(
        "SuperResolutionQualityDLSS",
        "SuperResolutionMethod",
        function(value)
            return value == DFHD_SUPERRES_VALUE.DLSS
        end
    )

    SettingRegLogicHD.RegPrerequisite(
        "SuperResolutionQualityTSR",
        "SuperResolutionMethod",
        function(value)
            return value == DFHD_SUPERRES_VALUE.TSR
        end
    )

    SettingRegLogicHD.RegPrerequisite(
        "SuperResolutionQualityFSR2",
        "SuperResolutionMethod",
        function(value)
            return value == DFHD_SUPERRES_VALUE.FSR2
        end
    )

    SettingRegLogicHD.RegPrerequisite(
        "SuperResolutionQualityFSR3",
        "SuperResolutionMethod",
        function(value)
            return value == DFHD_SUPERRES_VALUE.FSR3
        end
    )

    SettingRegLogicHD.RegPrerequisite(
        "SuperResolutionQualityXESS",
        "SuperResolutionMethod",
        function(value)
            return value == DFHD_SUPERRES_VALUE.XESS
        end
    )

    SettingRegLogicHD.RegPrerequisite(
        "FSR3FrameGeneration",
        nil,
        function(value)
            return VideoSettingLogicHD.IsFSR3FrameGenerationSupported()
        end
    )

    SettingRegLogicHD.RegPrerequisite(
        "FSR3FrameGeneration",
        "DLSSFrameGeneration",
        function(value)
            return value == false
        end
    )

    SettingRegLogicHD.RegPrerequisite(
        "ReflectionQuality",
        "RayTracingReflections",
        function(value)
            return value ~= 0
        end
    )

    SettingRegLogicHD.RegPrerequisite(
        "ShadowQuality",
        "RayTracingShadows",
        function(value)
            return value ~= 2
        end
    )

    SettingRegLogicHD.RegPrerequisite(
        "ShadowMapResolution",
        "RayTracingShadows",
        function(value)
            return value ~= 2
        end
    )

    SettingRegLogicHD.RegPrerequisite(
        "AmbientOcclusionQuality",
        "RayTracingAmbientOcclusion",
        function(value)
            return value ~= 2
        end
    )

    SettingRegLogicHD.RegPrerequisite(
        "RayTracingEnabled",
        nil,
        function()
            return IsRayTracingSupported()
        end
    )

    -- SettingRegLogicHD.RegPrerequisite(
    --     "RayTracingQuality",
    --     "RayTracingEnabled",
    --     function(value)
    --         return value and IsRayTracingSupported()
    --     end
    -- )

    -- SettingRegLogicHD.RegPrerequisite(
    --     "RayTracingGlobalIllumination",
    --     "RayTracingEnabled",
    --     function(value)
    --         return value and IsRayTracingSupported()
    --     end
    -- )

    SettingRegLogicHD.RegPrerequisite(
        "RayTracingReflections",
        "RayTracingEnabled",
        function(value)
            return value and IsRayTracingSupported() and UAdvancedVideoSetting:IsRayTracingRealEnabled()
        end
    )

    -- SettingRegLogicHD.RegPrerequisite(
    --     "RayTracingShadows",
    --     "RayTracingEnabled",
    --     function(value)
    --         return value and IsRayTracingSupported()
    --     end
    -- )

    -- SettingRegLogicHD.RegPrerequisite(
    --     "RayTracingAmbientOcclusion",
    --     "RayTracingEnabled",
    --     function(value)
    --         return value and IsRayTracingSupported()
    --     end
    -- )

    SettingRegLogicHD.RegPrerequisite(
        "DLSSFrameGeneration",
        nil,
        function(value)
            return IsDLSSFrameGenerationSupported()
        end
    )

    SettingRegLogicHD.RegPrerequisite(
        "DLSSFrameGeneration",
        "FSR3FrameGeneration",
        function(value)
            return value == false
        end
    )

    -- 当dlssg打开时，reflex不能再更改了。
    SettingRegLogicHD.RegPrerequisite(
        localT.NVIDIAReflexID,
        "DLSSFrameGeneration",
        function(value)
            return IsReflexSupported() and (not value)
        end
    )

    SettingRegLogicHD.RegPrerequisite(
        "FastSync",
        "VSync",
        function(value)
            return value == false
        end
    )

    SettingRegLogicHD.RegPrerequisite(
        "MaxFPS",
        "VSync",
        function(value)
            return value == false
        end
    )

    SettingRegLogicHD.RegPrerequisite(
        "MaxFPSFrontEnd",
        "VSync",
        function(value)
            return value == false
        end
    )

    SettingRegLogicHD.RegPrerequisite(
        "MaxFPSBackground",
        "VSync",
        function(value)
            return value == false
        end
    )

    local frameGenIDs = {
        "DLSSFrameGeneration",
        "FSR3FrameGeneration",
        "XESSFrameGeneration",
        "SuperResolutionMethod"
    }

    local frameIDs = {
        "MaxFPS",
        "MaxFPSFrontEnd",
        "MaxFPSBackground",
        "FastSync",
        "VSync"
    }

    for _, frameID in ipairs(frameIDs) do
        for _, frameGenID in ipairs(frameGenIDs) do
            SettingRegLogicHD.RegPrerequisite(
                frameID,
                frameGenID,
                function()
                    return not VideoSettingLogicHD.AnyFrameGenerationEnabled()
                end
            )
        end
    end

    SettingRegLogicHD.RegPrerequisite(
        "ArtStyle",
        nil,
        function(value)
            return false
        end
    )

    SettingRegLogicHD.RegPrerequisite(
        "LowMemoryMode",
        nil,
        function(value)
            return VideoSettingLogicHD.IsLowMemoryModeSupported()
        end
    )

    SettingRegLogicHD.RegJumpMapping("Brightness", _JumpToBrightness)

    SettingRegLogicHD.EndRegSection()
end

function VideoSettingLogicHD.GetGraphicsQualityByLevel(level)
    local key = "Below"
    if level == EGraphicsQualityLevelHD.LOW then
        key = "Low"
    elseif level == EGraphicsQualityLevelHD.MID then
        key = "Mid"
    elseif level == EGraphicsQualityLevelHD.HIGH then
        key = "High"
    elseif level == EGraphicsQualityLevelHD.EPIC then
        key = "Epic"
    elseif level == EGraphicsQualityLevelHD.WILD then
        key = "Wild"
    end

    local result = {}
    local qualityTable = Facade.TableManager:GetTable(_qualityTablePath)
    if qualityTable then
        for _, row in pairs(qualityTable) do
            result[row.SettingID] = row[key]
        end
    end
    return result
end

function VideoSettingLogicHD.BenchmarkLevelToPresetIndex(level)
    local index = 1
    if level == EGraphicsQualityLevelHD.LOW then
        index = 2
    elseif level == EGraphicsQualityLevelHD.MID then
        index = 3
    elseif level == EGraphicsQualityLevelHD.HIGH then
        index = 4
    elseif level == EGraphicsQualityLevelHD.EPIC then
        index = 5
    elseif level == EGraphicsQualityLevelHD.WILD then
        index = 6
    end
    return index
end

function VideoSettingLogicHD.IsSettingInGraphicsQualityMapping(id)
    if id == "GraphicsPreset" then
        return false
    end
    local qualityTable = Facade.TableManager:GetTable(_qualityTablePath)
    if qualityTable then
        return qualityTable[id] ~= nil
    end

    return false
end

function VideoSettingLogicHD.ReApplyGraphicsQuality()
    local qualityTable = Facade.TableManager:GetTable(_qualityTablePath)
    for id, row in pairs(qualityTable) do
        if id ~= "GraphicsPreset" then
            SettingRegLogicHD.CallApplyFuncByID(id, CommonSettingLogicHD.GetDataByID(id))
        end
    end
end

--- @return table k|v
function VideoSettingLogicHD.GetRecommandGraphicsQuality()
    local level = UVideoSettingHelper.GetBenchmarkLevelHD()
    if level < localT.EGraphicsQualityLevelHD_Below then -- tomzycai: 防止新显卡、CPU用户被分配到低配挡位
        level = localT.EGraphicsQualityLevelHD_Mid
    end
    return VideoSettingLogicHD.GetGraphicsQualityByLevel(level)
end

function VideoSettingLogicHD.RecordBenchmarkLevels()
    _videoSetting.BenchmarkLevel =
        VideoSettingLogicHD.BenchmarkLevelToPresetIndex(UVideoSettingHelper.GetBenchmarkLevelHD())
    _videoSetting.CPUBenchmarkLevel =
        VideoSettingLogicHD.BenchmarkLevelToPresetIndex(UVideoSettingHelper.GetCPUBenchmarkLevelHD(""))
    _videoSetting.GPUBenchmarkLevel =
        VideoSettingLogicHD.BenchmarkLevelToPresetIndex(UVideoSettingHelper.GetGPUBenchmarkLevelHD("", false))
    _videoSetting.RamBenchmarkLevel =
        VideoSettingLogicHD.BenchmarkLevelToPresetIndex(UVideoSettingHelper.GetRamBenchmarkLevelHD(-1))
    _videoSetting.VRamBenchmarkLevel =
        VideoSettingLogicHD.BenchmarkLevelToPresetIndex(UVideoSettingHelper.GetVRamBenchmarkLevelHD(-1))
    _videoSetting:SaveToSaved()
end

function VideoSettingLogicHD.VerifySettings()
    -- 先构建限制
    _BuildVideoSettingClampMap()

    -- 双显卡不支持全屏问题
    if UVideoSettingHelper.IsRHIUseDualGPU() then
        local displayMode = CommonSettingLogicHD.GetDataByID("DisplayMode")
        if displayMode == EWindowMode.Fullscreen then
            CommonSettingLogicHD.SetDataByIDImmediately("DisplayMode", EWindowMode.WindowedFullscreen)
        end
    end

    -- dlss
    if
        CommonSettingLogicHD.GetDataByID("SuperResolutionMethod") == DFHD_SUPERRES_VALUE.DLSS and
            not _UDLssLibrary().IsDLSSSupported()
     then
        CommonSettingLogicHD.SetDataByIDImmediately("SuperResolutionMethod", DFHD_SUPERRES_VALUE.None)
    end

    -- FSR2
    if
        CommonSettingLogicHD.GetDataByID("SuperResolutionMethod") == DFHD_SUPERRES_VALUE.FSR2 and
            not _FSR2TemporalUpscalingLibrary().IsFSR2Supported()
     then
        CommonSettingLogicHD.SetDataByIDImmediately("SuperResolutionMethod", DFHD_SUPERRES_VALUE.None)
    end

    -- FSR3
    if CommonSettingLogicHD.GetDataByID("SuperResolutionMethod") == DFHD_SUPERRES_VALUE.FSR3 and
            not false--_FFXFSR3Library().IsFSR3Supported()
     then
        -- 退化成FSR2
        if _FSR2TemporalUpscalingLibrary().IsFSR2Supported() then
            CommonSettingLogicHD.SetDataByIDImmediately("SuperResolutionMethod", DFHD_SUPERRES_VALUE.FSR2)
        else
            CommonSettingLogicHD.SetDataByIDImmediately("SuperResolutionMethod", DFHD_SUPERRES_VALUE.None)
        end
    end

    -- xess
    if
        CommonSettingLogicHD.GetDataByID("SuperResolutionMethod") == DFHD_SUPERRES_VALUE.XESS and
            not _XeSSLibrary().IsXeSSSupported()
     then
        CommonSettingLogicHD.SetDataByIDImmediately("SuperResolutionMethod", DFHD_SUPERRES_VALUE.None)
    end

    -- reflex
    if not IsReflexSupported() then
        CommonSettingLogicHD.SetDataByIDImmediately(localT.NVIDIAReflexID, false)
    end

    -- reflex mode
    if not IsReflexSupported() then
        CommonSettingLogicHD.SetDataByIDImmediately("NVIDIAReflexMode", 0)
    end

    -- FastSync
    if not IsFastSyncSupported() then
        CommonSettingLogicHD.SetDataByIDImmediately("FastSync", false)
    end

    -- FastSync
    if CommonSettingLogicHD.GetDataByID("VSync") == true then
        CommonSettingLogicHD.SetDataByIDImmediately("FastSync", false)
    end

    -- dlss framegeneration
    if not IsDLSSFrameGenerationSupported() then
        CommonSettingLogicHD.SetDataByIDImmediately("DLSSFrameGeneration", false)
    end

    -- dlssG 打开时，reflex必须开启
    if CommonSettingLogicHD.GetDataByID("DLSSFrameGeneration") then
        CommonSettingLogicHD.SetDataByIDImmediately(localT.NVIDIAReflexID, true)
    end

    -- fsr2 framegeneration
    if not VideoSettingLogicHD.IsFSR3FrameGenerationSupported() then
        CommonSettingLogicHD.SetDataByIDImmediately("FSR3FrameGeneration", false)
    end

    if not UClientSettingHelperHD.IsEditor() then
        if not VideoSettingLogicHD.IsInRayTracingWhitelist() then
            CommonSettingLogicHD.SetDataByIDImmediately("RayTracingEnabled", false)
        end
    end

    if not VideoSettingLogicHD.ScopeUseRTSupported() then
        local bScopeUseRT = CommonSettingLogicHD.GetDataByID("bScopeUseRT")
        if bScopeUseRT then
            CommonSettingLogicHD.SetDataByIDImmediately("bScopeUseRT", false)
        end
    end

    -- 亮度范围限制
    local brightness = CommonSettingLogicHD.GetDataByID("Brightness")
    local constrain = CommonSettingLogicHD.GetSliderContrainByID("Brightness")
    if brightness < constrain.min or brightness > constrain.max then
        brightness = math.clamp(brightness, constrain.min, constrain.max)
        CommonSettingLogicHD.SetDataByIDImmediately("Brightness", brightness)
    end

    local ratio = _videoSetting.ViewportRatioValue
    if ratio.Y ~= 0 then
        local min = ViewportRatioList[2]
        local max = ViewportRatioList[#ViewportRatioList]
        if ratio.X / ratio.Y < min[1] / min[2] then
            _videoSetting.ViewportRatioValue.X = min[1]
            _videoSetting.ViewportRatioValue.Y = min[2]
        elseif ratio.X / ratio.Y > max[1] / max[2] then
            _videoSetting.ViewportRatioValue.X = max[1]
            _videoSetting.ViewportRatioValue.Y = max[2]
        end
    end

    VerifyDisplayModeSettings()
end

function VideoSettingLogicHD.ConfirmVideoSettingsBeforeLeave(fConfirm, fCancel, caller)
    local resetInputTxt = Module.SystemSetting.Config.Loc.HDSetting.AskConfirmVideoTxt
    local cancelTxt = Module.SystemSetting.Config.Loc.HDEntrance.CancelConfirmVideoTxt
    local confirmTxt = Module.SystemSetting.Config.Loc.HDSetting.ConfirmVideoTxt
    Module.CommonTips:ShowConfirmWindow(
        resetInputTxt,
        CreateCallBack(fConfirm, caller),
        CreateCallBack(fCancel, caller),
        cancelTxt,
        confirmTxt
    )
end

-- 改到DataTable?
-- 纵轴
local renderScaleResTable = {
    [1] = 720,
    [2] = 1080,
    [3] = 1080,
    [4] = 1440,
    [5] = 2160
}

local superResTable = {
    [1] = 540,
    [2] = 540,
    [3] = 540,
    [4] = 830,
    [5] = 1080
}

local windowResTable = {
    [1] = 1080,
    [2] = 1080,
    [3] = 1080,
    [4] = 1440,
    [5] = 2160
}

-- 临时更加激进的推荐方案
-- local renderResTable = {
--     [1] = 1, -- 高配：始终平衡
--     [2] = 1440 * 0.8 --极高配：分辨率 > 2K ? 平衡 ：质量优先
-- }

local function FindBestResolution(ratio, targetY)
    local displayModes = _GetCurrentDisplayModes()
    local resolutionList = UClientVideoSettingHD.GetPureResolutionList(displayModes)
    for index, resolution in pairs(resolutionList) do
        if IsNearlyEqual(resolution.X / resolution.Y, ratio) then
            if resolution.Y <= targetY then
                return index + 1 -- +1,因为第一档是自动
            end
        end
    end

    return 0
end

function VideoSettingLogicHD.RecommandDisplayMode(immediately)
    VideoSettingLogicHD.ResetFrame()
    -- 对双显卡进行特殊处理
    local finalDisplayMode = EWindowMode.WindowedFullscreen
    local finalResolution = 0
    local preset = CommonSettingLogicHD.GetDataByID("GraphicsPreset")
    preset = math.clamp(preset, 1, 5)
    if UVideoSettingHelper.IsRHIUseDualGPU() or preset == 1 then
        local monitorRes = UClientVideoSettingHD.GetAutoResolution(
            CommonSettingLogicHD.GetDataByID("AdapterID"),
            VideoSettingLogicHD.GetSelectedMonitorID(), EWindowMode.Fullscreen)
        local tarWindowY = windowResTable[preset]
        if monitorRes.Y > tarWindowY then
            finalDisplayMode = EWindowMode.Windowed
            finalResolution = FindBestResolution(monitorRes.X / monitorRes.Y, tarWindowY)
        end
    end

    if immediately then
        CommonSettingLogicHD.SetDataByID("DisplayMode", finalDisplayMode)
        CommonSettingLogicHD.SetDataByID("Resolution", finalResolution)
        CommonSettingLogicHD.ApplyPendingSettings()
    else
        CommonSettingLogicHD.SetDataByID("DisplayMode", finalDisplayMode)
        CommonSettingLogicHD.SetDataByID("Resolution", finalResolution)
    end

    local curItems = Module.SystemSetting.Field:GetAllSettingItemHD()

    local displayModeItem = curItems["DisplayMode"]
    local resolutionItem = curItems["Resolution"]

    if not hasdestroy(displayModeItem) then
        displayModeItem:ReloadSetting()
    end
    if not hasdestroy(resolutionItem) then
        resolutionItem:ReloadSetting()
    end
    if immediately then
        SettingRegLogicHD.FlushPendingSaveObjs()
    end
end

function VideoSettingLogicHD.RecommandSuperResolutionSetting(immediately)
    if UClientSettingHelperHD.IsEditor() then
        return
    end

-- BEGIN MODIFICATION@ VIRTUOS: XSX not use super resolution
    if PLATFORM_GEN9 == 1 then
        return
    end
-- END MODIFICATION

    -- 此步骤必须在画质预设和分辨率之后执行

    -- 1.获取当前的显卡型号，优先推荐DLSS, 若显卡不支持则推荐FSR(DX12 优先FSR3，DX11推荐FSR2)。TSR玩家需要通过手动选择，不自动推荐
    local recMethod = nil
    if _UDLssLibrary().IsDLSSSupported() then
        recMethod = DFHD_SUPERRES_VALUE.DLSS
    elseif false then-- _FFXFSR3Library().IsFSR3Supported() then
        recMethod = DFHD_SUPERRES_VALUE.FSR3
    elseif _FSR2TemporalUpscalingLibrary().IsFSR2Supported() then
        recMethod = DFHD_SUPERRES_VALUE.FSR2
    elseif _XeSSLibrary().IsXeSSSupported() then
        recMethod = DFHD_SUPERRES_VALUE.XESS
    end

    -- 2.获取当前分辨率信息，或是即将被设置的分辨率信息
    local curRes = VideoSettingLogicHD.GetRealResolutionByIndex(CommonSettingLogicHD.GetDataByID("Resolution"))

    -- 3.获取当前配置的档位，考虑根据档位获取更高一级的基准分辨率？
    -- 优先从默认档位推荐
    local preset
    local mappings = VideoSettingLogicHD.GetRecommandGraphicsQuality()
    preset = mappings["GraphicsPreset"]

    if not preset then
        preset = CommonSettingLogicHD.GetDataByID("GraphicsPreset")
    end

    local tarPreset = math.clamp(preset, 1, 5)
    -- local tarPreset = math.clamp(preset, 1, 2)
    local tarRes
    -- 中低配开渲染倍率，更高配开超分
    if preset<=2 or recMethod == nil then
        tarRes = renderScaleResTable[tarPreset]
    else
        tarRes = superResTable[tarPreset]
    end
    -- tarRes = 1440 -- 测试
    -- 4.利用当前分辨率和目标分辨率求缩小倍率。
    -- 这里直接把一整档位一起对待了。如果要更精细一点，应该更低配的机器超分辨率需要更激进,利用benchmark参数，这一步做成配表，实机测试后，通过配表进行调整。
    -- preset = 3
    local quality = 0
    local rate = curRes.Y / tarRes
    local rateR = 1 / rate
    -- 低配不开超分
    -- 如果高配及以上不支持超分，也得走渲染倍率，小概率事件
    if recMethod == nil then
        quality = 0
    elseif tarPreset <= 2 then
        if curRes.Y > 1200 then
            -- 如果低中配的显示器小于1200P，也要开超分
            recMethod = nil
            quality = 0
        else
            -- 要排除XESS
            if recMethod  == DFHD_SUPERRES_VALUE.XESS then
                recMethod = nil
                quality = 0
            else
                quality = 2
            end
        end
    else
        -- 统一推荐平衡，倍率大概在0.58
        if recMethod == DFHD_SUPERRES_VALUE.DLSS then
            quality = 3
        elseif recMethod == DFHD_SUPERRES_VALUE.TSR then
            quality = 3
        elseif recMethod == DFHD_SUPERRES_VALUE.FSR2 then
            quality = 2
        elseif recMethod == DFHD_SUPERRES_VALUE.FSR3 then
            quality = 2
        elseif recMethod == DFHD_SUPERRES_VALUE.XESS then
            quality = 3
        end

        -- 5.根据缩小倍率获取对应的超分辨率档位
        -- 大于1，说明当前分辨率大于极限值
        -- if rate > 1 then
        -- 不管分辨率是否比较低，都打开推荐
        -- if rate > 0 then
        --     if recMethod == DFHD_SUPERRES_VALUE.DLSS then
        --         -- 0是自动，因此从1开始。越小分辨率越低, 因此要反着for
        --         for tmpQuality = 3, 1, -1 do
        --             -- 从质量优先开始推荐，最低只能到平衡档
        --             -- for tmpQuality = 4, 3, -1 do
        --             -- 这里要先处理一下mapping
        --             local mapQuality = UAdvancedVideoSetting:GetParamQualityValue("DLSS.Quality", tmpQuality)
        --             if mapQuality ~= "" then
        --                 mapQuality = tonumber(mapQuality)
        --                 if _UDLssLibrary().IsDLSSQualityCVarSupported(mapQuality) then
        --                     local tmpFraction = _UDLssLibrary().GetResolutionFractionForQuality(mapQuality)
        --                     quality = tmpQuality
        --                     if IsNearlyEqual(tmpFraction, rateR) or tmpFraction < rateR then
        --                         break
        --                     end
        --                 end
        --             end
        --         end
        --     elseif recMethod == DFHD_SUPERRES_VALUE.TSR then
        --         for tmpQuality = 3, 1, -1 do
        --             -- 从质量优先开始推荐，最低只能到平衡档
        --             -- for tmpQuality = 4, 3, -1 do
        --             -- tsr mapping 就是百分比
        --             local mapQuality = UAdvancedVideoSetting:GetParamQualityValue("TSRScrPerc", tmpQuality)
        --             if mapQuality ~= "" then
        --                 mapQuality = tonumber(mapQuality) / 100
        --                 local tmpFraction = mapQuality
        --                 quality = tmpQuality
        --                 if IsNearlyEqual(tmpFraction, rateR) or tmpFraction < rateR then
        --                     break
        --                 end
        --             end
        --         end
        --     elseif recMethod == DFHD_SUPERRES_VALUE.FSR2 then
        --         for tmpQuality = 3, 1, -1 do
        --             local mapQuality = UAdvancedVideoSetting:GetParamQualityValue("FSR2.QualityMode", tmpQuality)
        --             if mapQuality ~= "" then
        --                 mapQuality = tonumber(mapQuality)
        --                 local tmpFraction = _FSR2TemporalUpscalingLibrary().GetResolutionFraction(mapQuality)
        --                 quality = tmpQuality
        --                 if IsNearlyEqual(tmpFraction, rateR) or tmpFraction < rateR then
        --                     break
        --                 end
        --             end
        --         end
        --     elseif recMethod == DFHD_SUPERRES_VALUE.XESS then
        --         for tmpQuality = 3, 1, -1 do
        --             local mapQuality = UAdvancedVideoSetting:GetParamQualityValue("XeSS.Quality", tmpQuality)
        --             if mapQuality ~= "" then
        --                 mapQuality = tonumber(mapQuality)
        --                 local success, screenPercentage = _XeSSLibrary().GetXeSSQualityModeInformation(mapQuality, nil)
        --                 if success then
        --                     local tmpFraction = tonumber(screenPercentage) / 100
        --                     quality = tmpQuality
        --                     if IsNearlyEqual(tmpFraction, rateR) or tmpFraction < rateR then
        --                         break
        --                     end
        --                 end
        --             end
        --         end
        --     end
        -- end
    end

    local curItems = Module.SystemSetting.Field:GetAllSettingItemHD()
    -- if rate > 1 and quality ~= 0 then
    if quality ~= 0 then
        -- 为了推荐更加激进，档位-1。换方案了，不再减一
        -- quality = math.max(1, quality - 1)
        if immediately then
            CommonSettingLogicHD.SetDataByIDImmediately("SuperResolutionMethod", recMethod)
            if recMethod == DFHD_SUPERRES_VALUE.DLSS then
                CommonSettingLogicHD.SetDataByIDImmediately("SuperResolutionQualityDLSS", quality)
            elseif recMethod == DFHD_SUPERRES_VALUE.TSR then
                CommonSettingLogicHD.SetDataByIDImmediately("SuperResolutionQualityTSR", quality)
            elseif recMethod == DFHD_SUPERRES_VALUE.FSR2 then
                CommonSettingLogicHD.SetDataByIDImmediately("SuperResolutionQualityFSR2", quality)
            elseif recMethod == DFHD_SUPERRES_VALUE.FSR3 then
                CommonSettingLogicHD.SetDataByIDImmediately("SuperResolutionQualityFSR3", quality)
            elseif recMethod == DFHD_SUPERRES_VALUE.XESS then
                CommonSettingLogicHD.SetDataByIDImmediately("SuperResolutionQualityXESS", quality)
            end
        else
            CommonSettingLogicHD.SetDataByID("SuperResolutionMethod", recMethod)
            if recMethod == DFHD_SUPERRES_VALUE.DLSS then
                CommonSettingLogicHD.SetDataByID("SuperResolutionQualityDLSS", quality)
            elseif recMethod == DFHD_SUPERRES_VALUE.TSR then
                CommonSettingLogicHD.SetDataByID("SuperResolutionQualityTSR", quality)
            elseif recMethod == DFHD_SUPERRES_VALUE.FSR2 then
                CommonSettingLogicHD.SetDataByID("SuperResolutionQualityFSR2", quality)
            elseif recMethod == DFHD_SUPERRES_VALUE.FSR3 then
                CommonSettingLogicHD.SetDataByID("SuperResolutionQualityFSR3", quality)
            elseif recMethod == DFHD_SUPERRES_VALUE.XESS then
                CommonSettingLogicHD.SetDataByID("SuperResolutionQualityXESS", quality)
            end
        end
    else
        -- 不推荐，重置
        if immediately then
            CommonSettingLogicHD.SetDataByIDImmediately("SuperResolutionMethod", DFHD_SUPERRES_VALUE.None)
            CommonSettingLogicHD.SetDataByIDImmediately("SuperResolutionQualityDLSS", 0)
            CommonSettingLogicHD.SetDataByIDImmediately("SuperResolutionQualityTSR", 0)
            CommonSettingLogicHD.SetDataByIDImmediately("SuperResolutionQualityFSR2", 0)
            CommonSettingLogicHD.SetDataByIDImmediately("SuperResolutionQualityFSR3", 0)
            CommonSettingLogicHD.SetDataByIDImmediately("SuperResolutionQualityXESS", 0)
        else
            CommonSettingLogicHD.SetDataByID("SuperResolutionMethod", DFHD_SUPERRES_VALUE.None)
            CommonSettingLogicHD.SetDataByID("SuperResolutionQualityDLSS", 0)
            CommonSettingLogicHD.SetDataByID("SuperResolutionQualityTSR", 0)
            CommonSettingLogicHD.SetDataByID("SuperResolutionQualityFSR2", 0)
            CommonSettingLogicHD.SetDataByID("SuperResolutionQualityFSR3", 0)
            CommonSettingLogicHD.SetDataByID("SuperResolutionQualityXESS", 0)
        end
    end

    local tarRenderScale = 100

    if recMethod == nil  then
        tarRenderScale = math.floor(rateR * 100)
        tarRenderScale = math.min(tarRenderScale, 100)
    end

    if immediately then
        CommonSettingLogicHD.SetDataByIDImmediately("RenderScale", tarRenderScale)
    else
        CommonSettingLogicHD.SetDataByID("RenderScale", tarRenderScale)
    end

    local methodItem = curItems["SuperResolutionMethod"]
    local qualityItemDLSS = curItems["SuperResolutionQualityDLSS"]
    local qualityItemTSR = curItems["SuperResolutionQualityTSR"]
    local qualityItemFSR2 = curItems["SuperResolutionQualityFSR2"]
    local qualityItemFSR3 = curItems["SuperResolutionQualityFSR3"]
    local qualityItemXESS = curItems["SuperResolutionQualityXESS"]
    local renderScaleItem = curItems["RenderScale"]
    if not hasdestroy(methodItem) then
        methodItem:ReloadSetting()
    end
    if not hasdestroy(qualityItemDLSS) then
        qualityItemDLSS:ReloadSetting()
    end
    if not hasdestroy(qualityItemTSR) then
        qualityItemTSR:ReloadSetting()
    end
    if not hasdestroy(qualityItemFSR2) then
        qualityItemFSR2:ReloadSetting()
    end
    if not hasdestroy(qualityItemXESS) then
        qualityItemXESS:ReloadSetting()
    end
    if not hasdestroy(qualityItemFSR3) then
        qualityItemFSR3:ReloadSetting()
    end
    if not hasdestroy(renderScaleItem) then
        renderScaleItem:ReloadSetting()
    end
    if immediately then
        SettingRegLogicHD.FlushPendingSaveObjs()
    end
end

function VideoSettingLogicHD.RecordRealViewportResolution(bSave)
    local vSize = UWidgetLayoutLibrary.GetViewportSize(GetGameInstance())
    local superResMethod = CommonSettingLogicHD.GetDataByID("SuperResolutionMethod")
    local fraction = 1.0

    if superResMethod == DFHD_SUPERRES_VALUE.None then
        fraction = CommonSettingLogicHD.GetDataByID("RenderScale") / 100
    elseif superResMethod == DFHD_SUPERRES_VALUE.TSR then
        local tsrQuality = CommonSettingLogicHD.GetDataByID("SuperResolutionQualityTSR")
        local mapQuality = UAdvancedVideoSetting:GetParamQualityValue("TSRScrPerc", tsrQuality)
        if mapQuality ~= "" then
            fraction = tonumber(mapQuality) / 100
        end
    elseif superResMethod == DFHD_SUPERRES_VALUE.FSR2 then
        local fsrQuality = CommonSettingLogicHD.GetDataByID("SuperResolutionQualityFSR2")
        local mapQuality = UAdvancedVideoSetting:GetParamQualityValue("FSR2.QualityMode", fsrQuality)
        if mapQuality ~= "" then
            mapQuality = tonumber(mapQuality)
            fraction = _FSR2TemporalUpscalingLibrary().GetResolutionFraction(mapQuality)
        end
    elseif superResMethod == DFHD_SUPERRES_VALUE.FSR3 then
        local fsrQuality = CommonSettingLogicHD.GetDataByID("SuperResolutionQualityFSR3")
        local mapQuality = UAdvancedVideoSetting:GetParamQualityValue("FSR3.QualityMode", fsrQuality)
        if mapQuality ~= "" then
            mapQuality = tonumber(mapQuality)
            --fraction = _FFXFSR3Library().GetResolutionFraction(mapQuality)
        end
    elseif superResMethod == DFHD_SUPERRES_VALUE.DLSS then
        local dlssQuality = CommonSettingLogicHD.GetDataByID("SuperResolutionQualityDLSS")
        local mapQuality = UAdvancedVideoSetting:GetParamQualityValue("DLSS.Quality", dlssQuality)
        if mapQuality ~= "" then
            mapQuality = tonumber(mapQuality)
            if _UDLssLibrary().IsDLSSQualityCVarSupported(mapQuality) then
                fraction = _UDLssLibrary().GetResolutionFractionForQuality(mapQuality)
            end
        end
    elseif superResMethod == DFHD_SUPERRES_VALUE.XESS then
        local xessQuality = CommonSettingLogicHD.GetDataByID("SuperResolutionQualityXESS")
        local mapQuality = UAdvancedVideoSetting:GetParamQualityValue("XeSS.Quality", xessQuality)
        if mapQuality ~= "" then
            mapQuality = tonumber(mapQuality)
            local result, screenPercentage = _XeSSLibrary().GetXeSSQualityModeInformation(mapQuality, nil)
            if result then
                fraction = tonumber(screenPercentage) / 100
            end
        end
    end
    vSize = vSize * fraction
    _videoSetting.RealViewportResolution.X = math.floor(vSize.X)
    _videoSetting.RealViewportResolution.Y = math.floor(vSize.Y)
    if bSave then
        _videoSetting:SaveToSaved()
    end
end

function VideoSettingLogicHD.GetResolutionFraction(method, quality)
    local superResMethod = method or CommonSettingLogicHD.GetDataByID("SuperResolutionMethod")
    local fraction = 1.0

    if superResMethod == DFHD_SUPERRES_VALUE.None then
        fraction = CommonSettingLogicHD.GetDataByID("RenderScale") / 100
    elseif superResMethod == DFHD_SUPERRES_VALUE.FSR2 then
        local fsrQuality = quality or CommonSettingLogicHD.GetDataByID("SuperResolutionQualityFSR2")
        local mapQuality = UAdvancedVideoSetting:GetParamQualityValue("FSR2.QualityMode", fsrQuality)
        if mapQuality ~= "" then
            mapQuality = tonumber(mapQuality)
            fraction = _FSR2TemporalUpscalingLibrary().GetResolutionFraction(mapQuality)
        end
    elseif superResMethod == DFHD_SUPERRES_VALUE.FSR3 then
        -- local fsrQuality = quality or CommonSettingLogicHD.GetDataByID("SuperResolutionQualityFSR3")
        -- local mapQuality = UAdvancedVideoSetting:GetParamQualityValue("FSR3.QualityMode", fsrQuality)
        -- if mapQuality ~= "" then
        --     mapQuality = tonumber(mapQuality)
        --     fraction = _FFXFSR3Library().GetResolutionFraction(mapQuality)
        -- end
    elseif superResMethod == DFHD_SUPERRES_VALUE.TSR then
        local tsrQuality = quality or CommonSettingLogicHD.GetDataByID("SuperResolutionQualityTSR")
        local mapQuality = UAdvancedVideoSetting:GetParamQualityValue("TSRScrPerc", tsrQuality)
        if mapQuality ~= "" then
            fraction = tonumber(mapQuality) / 100
        end
    elseif superResMethod == DFHD_SUPERRES_VALUE.DLSS then
        local dlssQuality = quality or CommonSettingLogicHD.GetDataByID("SuperResolutionQualityDLSS")
        local mapQuality = UAdvancedVideoSetting:GetParamQualityValue("DLSS.Quality", dlssQuality)
        if mapQuality ~= "" then
            mapQuality = tonumber(mapQuality)
            if _UDLssLibrary().IsDLSSQualityCVarSupported(mapQuality) then
                fraction = _UDLssLibrary().GetResolutionFractionForQuality(mapQuality)
            end
        end
    elseif superResMethod == DFHD_SUPERRES_VALUE.XESS then
        local xessQuality = quality or CommonSettingLogicHD.GetDataByID("SuperResolutionQualityXESS")
        local mapQuality = UAdvancedVideoSetting:GetParamQualityValue("XeSS.Quality", xessQuality)
        if mapQuality ~= "" then
            mapQuality = tonumber(mapQuality)
            local result, screenPercentage = _XeSSLibrary().GetXeSSQualityModeInformation(mapQuality, nil)
            if result then
                fraction = tonumber(screenPercentage) / 100
            end
        end
    end
    return fraction
end

function VideoSettingLogicHD.RecommandMaxFPSSetting(immediately)
    local preset = CommonSettingLogicHD.GetDataByID("GraphicsPreset")
    local tarMaxFps = 0
    if preset <= 3 then
        --锁60
        tarMaxFps = 2
    elseif preset == 4 then
        --区分笔记本
        local function _isLaptop()
            local SystemInfoHD = UVideoSettingHelper.GetSystemInfoHD()
            local gpuBrand = string.lower(SystemInfoHD.GPUBrand)
            local gpuList = {
                "nvidia geforce rtx 4060 laptop gpu",
                "nvidia geforce rtx 3070 laptop gpu",
                "nvidia geforce rtx 3070 ti laptop gpu",
                "nvidia geforce rtx 3080 laptop gpu",
                "nvidia geforce rtx 4070 laptop gpu",
                "amd radeon rx 6850m xt",
                "amd radeon rx 6800s",
                "amd radeon rx 7900m"
            }
            for _, gpu in ipairs(gpuList) do
                if gpu == gpuBrand then
                    return true
                end
            end
            return false
        end

        if _isLaptop() then
            --锁75
            tarMaxFps = 3
        else
            --锁120
            tarMaxFps = 5
        end
    else
        tarMaxFps = 0
    end

    if immediately then
        CommonSettingLogicHD.SetDataByIDImmediately("MaxFPS", tarMaxFps)
    else
        CommonSettingLogicHD.SetDataByID("MaxFPS", tarMaxFps)
    end
    local curItems = Module.SystemSetting.Field:GetAllSettingItemHD()
    local fpsItem = curItems["MaxFPS"]
    if not hasdestroy(fpsItem) then
        fpsItem:ReloadSetting()
    end
    if immediately then
        SettingRegLogicHD.FlushPendingSaveObjs()
    end
end

function VideoSettingLogicHD.RecommandMisc(immediately)
    VideoSettingLogicHD.RecommandMaxFPSSetting(immediately)
end

local _hSwitchDisplayMode = nil
local _hDisplayMetricsChanged = nil
localT._hBackgourndStateChanged = nil
localT._hBHDFPSLimitStateChanged = nil

function VideoSettingLogicHD.Init()
    -- 提前缓存
    UVideoSettingHelper.CheckGPUDriverVersionV2(true, nil, nil, nil, nil)
    _ApplyViewportRatioRange()
    local inputMonitor = Facade.UIManager:GetInputMonitor()
    if _hSwitchDisplayMode then
        inputMonitor:RemoveDisplayActoinBingingForHandle(_hSwitchDisplayMode)
        _hSwitchDisplayMode = nil
    end
    _hSwitchDisplayMode =
        inputMonitor:AddDisplayActionBinding(
        "SwitchDisplayMode",
        EInputEvent.IE_Pressed,
        function()
            local gameUserSettings = UGameUserSettings.GetGameUserSettings()
            local curMode = gameUserSettings:GetFullscreenMode()
            local tarMode
            if curMode == EWindowMode.Windowed then
                if UVideoSettingHelper.IsRHIUseDualGPU() then
                    tarMode = gameUserSettings:GetPreferredFullscreenMode()
                else
                    tarMode = EWindowMode.WindowedFullscreen
                end
            else
                tarMode = EWindowMode.Windowed
            end

            CommonSettingLogicHD.SetDataByIDImmediately("DisplayMode", tarMode)

            local curItems = Module.SystemSetting.Field:GetAllSettingItemHD()
            local item = curItems["DisplayMode"]
            if item then
                item:ReloadSetting()
            end
            VideoSettingLogicHD.RecordRealViewportResolution(true)
            SettingRegLogicHD.FlushPendingSaveObjs()
        end,
        nil,
        EDisplayInputActionPriority.Always,
        true
    )

    _hDisplayMetricsChanged =
        UGPGameplayGlobalDelegates.Get(GetGameInstance()).OnDisplayMetricsChangedHD:Add(OnDisplayMetricsChangedHD)

    if UGPGameViewportClient.Get(GetGameInstance()) and UGPGameViewportClient.Get(GetGameInstance()).OnBackgroundStateChanged then
        localT._hBackgourndStateChanged = UGPGameViewportClient.Get(GetGameInstance()).OnBackgroundStateChanged:Add(VideoSettingLogicHD._OnBackgroundStateChanged)
    end
    local bhdHelper = UDFBHDHelper.GetInstance(GetGameInstance())
    if bhdHelper then
        local cpuEnergyController = bhdHelper:GetSubEnergyControllerByClass(UDFBHDCPUController)
        if cpuEnergyController then
            localT._hBHDFPSLimitStateChanged = cpuEnergyController.OnFPSLimitStateChanged:Add(VideoSettingLogicHD._OnBHDFPSLimitStateChanged)
        end

        local menEnergyController = bhdHelper:GetSubEnergyControllerByClass(UDFBHDMemoryController)
        if menEnergyController then
            localT._hBHDMinimizeSwapchainStateChanged = menEnergyController.OnMinimizeSwapchainStateChanged:Add(VideoSettingLogicHD._OnMinimizeSwapchainStateChanged)
        end
    end
end

function VideoSettingLogicHD.Uninit()
    if _hDisplayMetricsChanged then
        UGPGameplayGlobalDelegates.Get(GetGameInstance()).OnDisplayMetricsChangedHD:Remove(_hDisplayMetricsChanged)
        _hDisplayMetricsChanged = nil
    end

    local bhdHelper = UDFBHDHelper.GetInstance(GetGameInstance())
    if localT._hBHDFPSLimitStateChanged then
        local cpuEnergyController = bhdHelper:GetSubEnergyControllerByClass(UDFBHDCPUController)
        if cpuEnergyController then
            cpuEnergyController.OnFPSLimitStateChanged:Remove(localT._hBHDFPSLimitStateChanged )
        end
        localT._hBHDFPSLimitStateChanged = nil
    end

    if localT._hBackgourndStateChanged then
        if UGPGameViewportClient.Get(GetGameInstance()) and UGPGameViewportClient.Get(GetGameInstance()).OnBackgroundStateChanged then
            UGPGameViewportClient.Get(GetGameInstance()).OnBackgroundStateChanged:Remove(localT._hBackgourndStateChanged)
        end
        localT._hBackgourndStateChanged = nil
    end

    if localT._hBHDMinimizeSwapchainStateChanged then
        local menEnergyController = bhdHelper:GetSubEnergyControllerByClass(UDFBHDMemoryController)
        if menEnergyController then
            menEnergyController.OnMinimizeSwapchainStateChanged:Remove(VideoSettingLogicHD._hBHDMinimizeSwapchainStateChanged)
        end
        localT._hBHDMinimizeSwapchainStateChanged = nil
    end

    if _hSwitchDisplayMode then
        local inputMonitor = Facade.UIManager:GetInputMonitor()
        inputMonitor:RemoveDisplayActoinBingingForHandle(_hSwitchDisplayMode)
        _hSwitchDisplayMode = nil
    end
end

function VideoSettingLogicHD.ReloadExtraDefaults()
    _videoSetting.BenchmarkLevel = 1
    _videoSetting.CPUBenchmarkLevel = 1
    _videoSetting.GPUBenchmarkLevel = 1
    _videoSetting.RamBenchmarkLevel = 1
    _videoSetting.VRamBenchmarkLevel = 1
    _videoSetting.RealViewportResolution.X = 1920
    _videoSetting.RealViewportResolution.Y = 1080
    _videoSetting.ResolutionX = 1920
    _videoSetting.ResolutionY = 1080
    _videoSetting.bOnceGPUDriverWarning = false
    _videoSetting.bOnceReBarWarning = false
    _videoSetting.bOnceGPUCrashDebuggingWarning = false
end

return VideoSettingLogicHD
