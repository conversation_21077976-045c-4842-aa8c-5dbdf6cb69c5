----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMTournament)
----- LOG FUNCTION AUTO GENERATE END -----------



    local TournamentLogic = {}
    ---------------------------------------------------------------------------------
    --- Logic可以拆分多个，用于业务逻辑的编写，部分需要开放的接口由Module进行转发
    ---------------------------------------------------------------------------------
    --- 可以仅模块内部调用，也可以在Module中被公开
    TournamentLogic.DoSomeThingProcess = function(...)
        -- TODO 业务逻辑、弹窗Tips表现、发送Server Req等等一系列事情
        -- Server.TournamentServer:DoSomeThingReq(...)
        -- return 
    end

    TournamentLogic.ShowMainPanelProcess = function(fCallback,...)
        loginfo("TournamentLogic.ShowMainPanelProcess")
        local mainPanelHandle = Facade.UIManager:AsyncShowUI(UIName2ID.TournamentBasePanel,fCallback,nil,...) 
        Module.Tournament.Field:SetMainPanelHandle(mainPanelHandle)
    end

    TournamentLogic.ShowRewardPanel = function(selectedMajorLevel,rankMode)
        loginfo("TournamentLogic.ShowRewardPanel",selectedMajorLevel,rankMode)
        Facade.UIManager:AsyncShowUI(UIName2ID.TournamentRewardPanel,nil,nil,selectedMajorLevel,rankMode)
    end

    TournamentLogic.OnMainPanelCreateFinished = function(uiIns)
        Module.Tournament.Field:SetMainPanel(uiIns)
    end

    TournamentLogic.CloseMainPanelProcess = function()
        Facade.UIManager:CloseUIByHandle(Module.Tournament.Field:GetMainPanelHandle())
    end
    
    TournamentLogic.IsTournamentUnlocked = function()
        loginfo("TournamentLogic.IsTournamentUnlocked")
        local isUnlocked=Server.ModuleUnlockServer:IsModuleUnlock(SwitchModuleID.ModuleScoreMP)
        local unlockInfo=Server.ModuleUnlockServer:GetModuleUnlockInfoById(SwitchModuleID.ModuleScoreMP)
        local unlocktips=unlockInfo and unlockInfo.unlocktips or "?"
        return isUnlocked,unlocktips

    end
    
    TournamentLogic.GetRankDataByMinor = function(minorLevel,serial)
        loginfo("TournamentLogic.GetRankDataByMinor",minorLevel,serial)
        minorLevel=minorLevel or Server.TournamentServer:GetMinorLevel()--不填默认用当前段位
        serial=serial or Server.TournamentServer:GetCurSerial()
        local tierTable=TournamentLogic.GetTierTableBySerial(serial)
        return tierTable and tierTable[minorLevel]

    end

    TournamentLogic.GetRankDataByMajor = function(majorLevel,serial)
        loginfo("TournamentLogic.GetRankDataByMajor",majorLevel,serial)
        majorLevel=majorLevel or Server.TournamentServer:GetMajorLevel()--不填默认用当前段位
        serial=serial or Server.TournamentServer:GetCurSerial()
        local sortedMinorTable=TournamentLogic.GetSortedMinorTable(serial)
        for k,v in pairs(sortedMinorTable)do
            if majorLevel==v.TierTypeID then
                return v
            end
        end

    end

    TournamentLogic.GetRankDataByScore = function(score,serial)
        loginfo("TournamentLogic.GetRankDataByScore",score,serial)
        score=score or Server.TournamentServer:GetRankScore()--不填默认用当前段位分
        serial=serial or Server.TournamentServer:GetCurSerial()
        local sortedMinorTable=TournamentLogic.GetSortedMinorTable(serial)
        local minorData=sortedMinorTable[1]
        for k,v in pairs(sortedMinorTable)do
            if score>=v.MinPoint then
                minorData=v
            elseif score<v.MinPoint then
                break
            end
        end
        return minorData
    end

    TournamentLogic.GetTierTableBySerial = function(serial)
        loginfo("TournamentLogic.GetTierTableBySerial",serial)
        serial=serial or Server.TournamentServer:GetCurSerial()
        local tierTable={}
        for k,v in pairs(Module.Tournament.Config.TournamentTierTable or {})do
            if table.contains(v.SeasonID or {},serial) then
                tierTable[v.ID]=v
            end
        end
        return tierTable
    end

    TournamentLogic.GetMinorIndexByScore = function(score,serial)
        loginfo("TournamentLogic.GetMinorIndexByScore",score,serial)
        score=score or Server.TournamentServer:GetRankScore()--不填默认用当前段位分
        serial=serial or Server.TournamentServer:GetCurSerial()
        local minorData=TournamentLogic.GetRankDataByScore(score,serial)
        local minorIdReverseList={}
        for k,v in pairs(TournamentLogic.GetSortedMinorTable(serial) or {})do
            if minorData and v.TierTypeID==minorData.TierTypeID then
                table.insert(minorIdReverseList,1,v.ID)
            end
        end
        for k,v in pairs(minorIdReverseList)do
            if minorData and v==minorData.ID then
                return k
            end
        end
        return 1
    end

    TournamentLogic.GetMinorIndexByMinor = function(minorLevel,serial)
        loginfo("TournamentLogic.GetMinorIndexByMinor",minorLevel,serial)
        minorLevel=minorLevel or Server.TournamentServer:GetMinorLevel()--不填默认用当前段位
        serial=serial or Server.TournamentServer:GetCurSerial()
        local minorData=TournamentLogic.GetRankDataByMinor(minorLevel,serial)
        local minorIdReverseList={}
        for k,v in pairs(TournamentLogic.GetSortedMinorTable(serial) or {})do
            if minorData and v.TierTypeID==minorData.TierTypeID then
                table.insert(minorIdReverseList,1,v.ID)
            end
        end
        for k,v in pairs(minorIdReverseList)do
            if minorData and v==minorData.ID then
                return k
            end
        end
        return 1
    end

    TournamentLogic.GetStarNumByScore = function(score,serial)
        loginfo("TournamentLogic.GetStarNumByScore",score,serial)
        score=score or Server.TournamentServer:GetRankScore()--不填默认用当前段位分
        serial=serial or Server.TournamentServer:GetCurSerial()
        local rankData=TournamentLogic.GetRankDataByScore(score,serial) 
        if rankData then
            
            local scoreOver=score-rankData.MinPoint
            local starNum=1
            local starsDivided=rankData.StarsDivided or 1
            if starsDivided==-1 then
                local rankConstant=Module.Tournament:GetRankConstant()
                starNum=math.ceil((scoreOver+1)/rankConstant)--因为默认有一颗星，而且进度条百分之百要算下一颗星，所以这里+1并向上取整
                starNum=math.max(starNum,1)
            else
                local nextRankData=TournamentLogic.GetNextRankDataByScore(score,serial)
                local maxPoint=nextRankData and nextRankData.MinPoint or score
                local scoreRange=maxPoint-rankData.MinPoint
                starNum=math.ceil(scoreOver/(scoreRange/starsDivided))
                starNum=math.max(starNum,1)
            end
            return starNum
        else
            logerror("TournamentLogic.GetStarNumByScore rankData is nil!!!")
            return 1
        end
        
    end

    TournamentLogic.GetLastStarPercentByScore = function(score,serial)
        loginfo("TournamentLogic.GetLastStarPercentByScore",score,serial)
        score=score or Server.TournamentServer:GetRankScore()--不填默认用当前段位分
        serial=serial or Server.TournamentServer:GetCurSerial()
        local rankData=TournamentLogic.GetRankDataByScore(score,serial)
        if rankData then
            local starsDivided=rankData.StarsDivided or 1
            if starsDivided==-1 then
                local rankConstant=Module.Tournament:GetRankConstant()
                local scoreOver=score-rankData.MinPoint
                local scoreRemain=scoreOver%rankConstant
                return scoreRemain/rankConstant
            else
                local nextRankData=TournamentLogic.GetNextRankDataByScore(score,serial)
                local maxPoint=nextRankData and nextRankData.MinPoint or score
                local scoreOver=score-rankData.MinPoint
                local scoreRange=maxPoint-rankData.MinPoint
                local scoreAverage=scoreRange/starsDivided
                local starNum=TournamentLogic.GetStarNumByScore(score,serial)
                local numScore=starNum*scoreAverage
                local lackScore=numScore-scoreOver
                local remainScore=scoreAverage-lackScore
                return remainScore/scoreAverage
            end
        else
            logerror("TournamentLogic.GetLastStarPercentByScore rankData is nil!!!")
            return 1
        end
    end

    TournamentLogic.GetNextRankDataByScore = function(score,serial)
        loginfo("TournamentLogic.GetNextRankDataByScore",score,serial)
        score=score or Server.TournamentServer:GetRankScore()--不填默认用当前段位分
        serial=serial or Server.TournamentServer:GetCurSerial()
        for k,v in pairs(TournamentLogic.GetSortedMinorTable(serial) or {})do
            if v.MinPoint>score then
                return v
            end
        end
                
    end

    TournamentLogic.GetSortedMinorTable = function(serial)
        loginfo("TournamentLogic.GetSortedMinorTable",serial)
        serial=serial or Server.TournamentServer:GetCurSerial()
        local sortedMinorTable={}
        for k,v in pairs(TournamentLogic.GetTierTableBySerial(serial) or {})do
            table.insert(sortedMinorTable,v)
        end
        table.sort(sortedMinorTable,function(a,b)return a.MinPoint<b.MinPoint end)
        return sortedMinorTable
    end

    TournamentLogic.GetRankIconByScore = function(score,serial)
        loginfo("TournamentLogic.GetRankIconByScore",score,serial)
        score=score or Server.TournamentServer:GetRankScore()--不填默认用当前段位分
        serial=serial or Server.TournamentServer:GetCurSerial()
        local rankData=TournamentLogic.GetRankDataByScore(score,serial) or {}
        return {majorNormal=rankData.BadgeIcon,majorAbbr=rankData.BadgeIconThumbnail,minorNormal=rankData.BadgeMinIcon,minorAbbr=rankData.BadgeMinIconThumbnail}
    end

    ---@param subScore table
    TournamentLogic.GetTitleInfoBySubScore = function(subScore)
        loginfo("TournamentLogic.GetTitleInfoBySubScore")
        local sortedTitleTable=TournamentLogic.GetSortedTitleTable()
        for k,v in pairs(sortedTitleTable)do
            if subScore.ScoreShoot>=v.Weapon and subScore.ScoreTactics>=v.Tactical and subScore.ScoreVehicle>=v.Vehicle then
                return v
            end
        end
    end

    TournamentLogic.GetSortedTitleTable = function()
        loginfo("TournamentLogic.GetSortedTitleTable")
        if not Module.Tournament.Field:GetSortedTitleTable() then
            local sortedTitleTable={}
            for k,v in pairs(Module.Tournament.Config.TournamentTitleTable or {})do
                table.insert(sortedTitleTable,v)
            end
            table.sort(sortedTitleTable,function(a,b)return a.ID>b.ID end)
            Module.Tournament.Field:SetSortedTitleTable(sortedTitleTable)
        end
        return Module.Tournament.Field:GetSortedTitleTable()
    end

    TournamentLogic.GetLevelRewardsInfo = function(majorLevel)
        loginfo("TournamentLogic.GetLevelRewardsInfo",majorLevel)
        majorLevel=majorLevel or Server.TournamentServer:GetMajorLevel()--不填默认用当前段位
        local rewardLevel2InfoMap={}
        for k,v in pairs(TournamentLogic.GetCurSeasonRewardsTable() or {})do
            rewardLevel2InfoMap[v.TierID]=v
        end    
        return rewardLevel2InfoMap[majorLevel]

    end

    TournamentLogic.GetMaxRewardsInfo = function()
        loginfo("TournamentLogic.GetMaxRewardsInfo")
        local sortedRewardsTable=TournamentLogic.GetSortedRewardsTable()
        return sortedRewardsTable[#sortedRewardsTable]
    end

    TournamentLogic.ParseRewardStr = function(rewardStr)
        loginfo("TournamentLogic.ParseRewardStr",rewardStr)
        local strList=string.split(rewardStr or "",":")
        local idStr=strList[1] or "0"
        local numStr=strList[2] or "0"
        return tonumber(idStr),tonumber(numStr)
    end

    TournamentLogic.GetCurSeasonRewardsTable = function()
        loginfo("TournamentLogic.GetCurSeasonRewardsTable")
        local curSerial=Server.TournamentServer:GetCurSerial()
        if curSerial==0 then
            logerror("TournamentLogic.GetCurSeasonRewardsTable, curSerial==0!!!")
        end
        local curSeasonRewardsList={}
        for k,v in pairs(Module.Tournament.Config.TournamentRewardsTable or {})do
            if v.SeasonID==curSerial then
                table.insert(curSeasonRewardsList,v)
            end
        end
        
        return curSeasonRewardsList
    end

    TournamentLogic.GetSortedRewardsTable = function()
        loginfo("TournamentLogic.GetSortedRewardsTable")
        local sortedRewardsTable={}
        for k,v in pairs(TournamentLogic.GetCurSeasonRewardsTable() or {})do
            table.insert(sortedRewardsTable,v)
        end    
        table.sort(sortedRewardsTable,function(a,b) return a.TierID<b.TierID end)
        return sortedRewardsTable
    end
    
    TournamentLogic.GetRankConstant = function()--传奇段位多少分升一星
        loginfo("TournamentLogic.GetRankConstant")
        local rankConstantRow=Module.Tournament.Config.TournamentParameterTable["StartValue"]
        return rankConstantRow and tonumber(rankConstantRow.Value) or 50

    end

    TournamentLogic.GetDefinedMaxMajorLevel = function(serial)--获取赛季定义的最大段位
        loginfo("TournamentLogic.GetDefinedMaxMajorLevel",serial)
        serial=serial or Server.TournamentServer:GetCurSerial()
        local definedMaxMajorLevel=0
        for k,v in pairs(TournamentLogic.GetTierTableBySerial(serial) or {})do
            if v.TierTypeID>definedMaxMajorLevel then
                definedMaxMajorLevel=v.TierTypeID
            end
        end
        return definedMaxMajorLevel
    end

    TournamentLogic.GetSeasonConfigBySerial = function(serial)--获取赛季配置信息
        loginfo("TournamentLogic.GetSeasonConfigBySerial",serial)
        serial=serial or Server.TournamentServer:GetCurSerial()--不填默认用当前赛季
        return Module.Tournament.Config.TournamentSeasonTable[serial]
    end

    TournamentLogic.GetCommanderRankDataByMinor = function(minorLevel,serial)
        loginfo("TournamentLogic.GetCommanderRankDataByMinor",minorLevel,serial)
        minorLevel=minorLevel or Server.TournamentServer:GetCommanderMinorLevel()--不填默认用当前段位
        serial=serial or Server.TournamentServer:GetCurSerial()
        local tierTable=TournamentLogic.GetCommanderTierTableBySerial(serial)
        return tierTable and tierTable[minorLevel]

    end

    TournamentLogic.GetCommanderRankDataByMajor = function(majorLevel,serial)
        loginfo("TournamentLogic.GetCommanderRankDataByMajor",majorLevel,serial)
        majorLevel=majorLevel or Server.TournamentServer:GetCommanderMajorLevel()--不填默认用当前段位
        serial=serial or Server.TournamentServer:GetCurSerial()
        local sortedMinorTable=TournamentLogic.GetCommanderSortedMinorTable(serial)
        for k,v in pairs(sortedMinorTable)do
            if majorLevel==v.TierTypeID then
                return v
            end
        end

    end

    TournamentLogic.GetCommanderRankDataByScore = function(score,serial)
        loginfo("TournamentLogic.GetCommanderRankDataByScore",score,serial)
        score=score or Server.TournamentServer:GetCommanderRankScore()--不填默认用当前段位分
        serial=serial or Server.TournamentServer:GetCurSerial()
        local sortedMinorTable=TournamentLogic.GetCommanderSortedMinorTable(serial)
        local minorData=sortedMinorTable[1]
        for k,v in pairs(sortedMinorTable)do
            if score>=v.MinPoint then
                minorData=v
            elseif score<v.MinPoint then
                break
            end
        end
        return minorData
    end

    TournamentLogic.GetCommanderTierTableBySerial = function(serial)
        loginfo("TournamentLogic.GetCommanderTierTableBySerial",serial)
        serial=serial or Server.TournamentServer:GetCurSerial()
        local tierTable={}
        for k,v in pairs(Module.Tournament.Config.CommanderTierTable or {})do
            if table.contains(v.SeasonID or {},serial) then
                tierTable[v.ID]=v
            end
        end
        return tierTable
    end

    TournamentLogic.GetCommanderMinorIndexByMinor = function(minorLevel,serial)
        loginfo("TournamentLogic.GetCommanderMinorIndexByMinor",minorLevel,serial)
        minorLevel=minorLevel or Server.TournamentServer:GetCommanderMinorLevel()--不填默认用当前段位
        serial=serial or Server.TournamentServer:GetCurSerial()
        local minorData=TournamentLogic.GetCommanderRankDataByMinor(minorLevel,serial)
        local minorIdReverseList={}
        for k,v in pairs(TournamentLogic.GetCommanderSortedMinorTable(serial) or {})do
            if minorData and v.TierTypeID==minorData.TierTypeID then
                table.insert(minorIdReverseList,1,v.ID)
            end
        end
        for k,v in pairs(minorIdReverseList)do
            if minorData and v==minorData.ID then
                return k
            end
        end
        return 1
    end

    TournamentLogic.GetCommanderStarNumByScore = function(score,serial)
        loginfo("TournamentLogic.GetCommanderStarNumByScore",score,serial)
        score=score or Server.TournamentServer:GetCommanderRankScore()--不填默认用当前段位分
        serial=serial or Server.TournamentServer:GetCurSerial()
        local rankData=TournamentLogic.GetCommanderRankDataByScore(score,serial) 
        if rankData then
            
            local scoreOver=score-rankData.MinPoint
            local starNum=1
            local starsDivided=rankData.StarsDivided or 1
            if starsDivided==-1 then
                local rankConstant=Module.Tournament:GetCommanderRankConstant()
                starNum=math.ceil((scoreOver+1)/rankConstant)--因为默认有一颗星，而且进度条百分之百要算下一颗星，所以这里+1并向上取整
                starNum=math.max(starNum,1)
            else
                local nextRankData=TournamentLogic.GetCommanderNextRankDataByScore(score,serial)
                local maxPoint=nextRankData and nextRankData.MinPoint or score
                local scoreRange=maxPoint-rankData.MinPoint
                starNum=math.ceil(scoreOver/(scoreRange/starsDivided))
                starNum=math.max(starNum,1)
            end
            return starNum
        else
            logerror("TournamentLogic.GetCommanderStarNumByScore rankData is nil!!!")
            return 1
        end
        
    end

    TournamentLogic.GetCommanderLastStarPercentByScore = function(score,serial)
        loginfo("TournamentLogic.GetCommanderLastStarPercentByScore",score,serial)
        score=score or Server.TournamentServer:GetCommanderRankScore()--不填默认用当前段位分
        serial=serial or Server.TournamentServer:GetCurSerial()
        local rankData=TournamentLogic.GetCommanderRankDataByScore(score,serial)
        if rankData then
            local starsDivided=rankData.StarsDivided or 1
            if starsDivided==-1 then
                local rankConstant=Module.Tournament:GetCommanderRankConstant()
                local scoreOver=score-rankData.MinPoint
                local scoreRemain=scoreOver%rankConstant
                return scoreRemain/rankConstant
            else
                local nextRankData=TournamentLogic.GetCommanderNextRankDataByScore(score,serial)
                local maxPoint=nextRankData and nextRankData.MinPoint or score
                local scoreOver=score-rankData.MinPoint
                local scoreRange=maxPoint-rankData.MinPoint
                local scoreAverage=scoreRange/starsDivided
                local starNum=TournamentLogic.GetCommanderStarNumByScore(score,serial)
                local numScore=starNum*scoreAverage
                local lackScore=numScore-scoreOver
                local remainScore=scoreAverage-lackScore
                return remainScore/scoreAverage
            end
        else
            logerror("TournamentLogic.GetCommanderLastStarPercentByScore rankData is nil!!!")
            return 1
        end
    end

    TournamentLogic.GetCommanderNextRankDataByScore = function(score,serial)
        loginfo("TournamentLogic.GetCommanderNextRankDataByScore",score,serial)
        score=score or Server.TournamentServer:GetCommanderRankScore()--不填默认用当前段位分
        serial=serial or Server.TournamentServer:GetCurSerial()
        for k,v in pairs(TournamentLogic.GetCommanderSortedMinorTable(serial) or {})do
            if v.MinPoint>score then
                return v
            end
        end
                
    end

    TournamentLogic.GetCommanderSortedMinorTable = function(serial)
        loginfo("TournamentLogic.GetCommanderSortedMinorTable",serial)
        serial=serial or Server.TournamentServer:GetCurSerial()
        local sortedMinorTable={}
        for k,v in pairs(TournamentLogic.GetCommanderTierTableBySerial(serial) or {})do
            table.insert(sortedMinorTable,v)
        end
        table.sort(sortedMinorTable,function(a,b)return a.MinPoint<b.MinPoint end)
        return sortedMinorTable
    end
    
    TournamentLogic.GetCommanderRankIconByScore = function(score,serial)
        loginfo("TournamentLogic.GetCommanderRankIconByScore",score,serial)
        score=score or Server.TournamentServer:GetCommanderRankScore()--不填默认用当前段位分
        serial=serial or Server.TournamentServer:GetCurSerial()
        local rankData=TournamentLogic.GetCommanderRankDataByScore(score,serial) or {}
        return {majorNormal=rankData.BadgeIcon,majorAbbr=rankData.BadgeIconThumbnail,minorNormal=rankData.BadgeMinIcon,minorAbbr=rankData.BadgeMinIconThumbnail}
    end

    TournamentLogic.GetCommanderLevelRewardsInfo = function(majorLevel)
        loginfo("TournamentLogic.GetCommanderLevelRewardsInfo",majorLevel)
        majorLevel=majorLevel or Server.TournamentServer:GetCommanderMajorLevel()--不填默认用当前段位
        local rewardLevel2InfoMap={}
        for k,v in pairs(TournamentLogic.GetCommanderCurSeasonRewardsTable() or {})do
            rewardLevel2InfoMap[v.TierID]=v
        end    
        return rewardLevel2InfoMap[majorLevel]

    end

    TournamentLogic.GetCommanderCurSeasonRewardsTable = function()
        loginfo("TournamentLogic.GetCommanderCurSeasonRewardsTable")
        local curSerial=Server.TournamentServer:GetCurSerial()
        if curSerial==0 then
            logerror("TournamentLogic.GetCommanderCurSeasonRewardsTable, curSerial==0!!!")
        end
        local curSeasonRewardsList={}
        for k,v in pairs(Module.Tournament.Config.CommanderRewardsTable or {})do
            if v.SeasonID==curSerial then
                table.insert(curSeasonRewardsList,v)
            end
        end
        
        return curSeasonRewardsList
    end

    TournamentLogic.GetCommanderSortedRewardsTable = function()
        loginfo("TournamentLogic.GetCommanderSortedRewardsTable")
        local sortedRewardsTable={}
        for k,v in pairs(TournamentLogic.GetCommanderCurSeasonRewardsTable() or {})do
            table.insert(sortedRewardsTable,v)
        end    
        table.sort(sortedRewardsTable,function(a,b) return a.TierID<b.TierID end)
        return sortedRewardsTable
    end

    TournamentLogic.GetCommanderRankConstant = function()--传奇段位多少分升一星
        loginfo("TournamentLogic.GetCommanderRankConstant")
        local rankConstantRow=Module.Tournament.Config.CommanderParameterTable["StartValue"]
        return rankConstantRow and tonumber(rankConstantRow.Value) or 50

    end

    TournamentLogic.GetCommanderAbilityLevelParam = function()--能力品阶划分百分比
        loginfo("TournamentLogic.GetCommanderAbilityLevelParam")
        local rankConstantRow=Module.Tournament.Config.CommanderParameterTable["MedalThreshold"]
        local paramList=string.split(rankConstantRow and rankConstantRow.Value or "",",")
        local newParamList={}
        for k,v in pairs(paramList or {})do
            table.insert(newParamList,tonumber(v))
        end
        return newParamList

    end

    TournamentLogic.GetCommanderWordParamByKey = function(paramKey)
        loginfo("TournamentLogic.GetCommanderWordParamByKey",paramKey)
        paramKey=paramKey or ""
        local wordParamRow=Module.Tournament.Config.CommanderWordParameterTable[paramKey]
        return wordParamRow and wordParamRow.Value

    end

    TournamentLogic.GetCommanderDefinedMaxMajorLevel = function(serial)--获取赛季定义的最大段位
        loginfo("TournamentLogic.GetCommanderDefinedMaxMajorLevel",serial)
        serial=serial or Server.TournamentServer:GetCurSerial()
        local definedMaxMajorLevel=0
        for k,v in pairs(TournamentLogic.GetCommanderTierTableBySerial(serial) or {})do
            if v.TierTypeID>definedMaxMajorLevel then
                definedMaxMajorLevel=v.TierTypeID
            end
        end
        return definedMaxMajorLevel
    end

    TournamentLogic.GetCommanderSeasonConfigBySerial = function(serial)--获取赛季配置信息
        loginfo("TournamentLogic.GetCommanderSeasonConfigBySerial",serial)
        serial=serial or Server.TournamentServer:GetCurSerial()--不填默认用当前赛季
        return Module.Tournament.Config.CommanderSeasonTable[serial]
    end

    TournamentLogic.PrintTables = function()
        local fTransArrytoStr=function(strArray)
            local resultStr=""
            local index=1
            for k,v in pairs(strArray or {})do
                local symbol=index~=1 and "," or ""
                resultStr=string.format("%s%s%s",resultStr,symbol,v)
                index=index+1
            end
            return resultStr
        end

        loginfo("TournamentLogic.PrintTables, TournamentSeasonTable")
        for k,v in pairs(Module.Tournament.Config.TournamentSeasonTable or {})do
            loginfo(v.ID,v.StartTime,v.EndTime,v.UIStartTime,v.UIEndTime,v.TimeDisplayDigit,v.Name,v.RankPopupExplainTips,v.TitleExplainTips,v.CoreAward,v.CoreTier,v.CoreAwardPicture)
        end

        loginfo("TournamentLogic.PrintTables, TournamentTierTable")
        for k,v in pairs(Module.Tournament.Config.TournamentTierTable or {})do
            local seasonStr=fTransArrytoStr(v.SeasonID)
            loginfo(v.ID,v.TierTypeID,v.SubID,v.Name,v.Type,seasonStr,v.MinPoint,v.StarsDivided,v.BadgeIcon.AssetPathName,v.BadgeIconThumbnail.AssetPathName,v.BadgeMinIcon.AssetPathName,v.BadgeMinIconThumbnail.AssetPathName)
        end

        loginfo("TournamentLogic.PrintTables, TournamentRewardsTable")
        for k,v in pairs(Module.Tournament.Config.TournamentRewardsTable or {})do
            local rewardStr=fTransArrytoStr(v.Rewards)
            local seasonRewardStr=fTransArrytoStr(v.SeasonRewards)
            loginfo(v.ID,v.TierID,v.SeasonID,v.RewardConditionPar,rewardStr,seasonRewardStr)
            
        end

        loginfo("TournamentLogic.PrintTables, TournamentTitleTable")
        for k,v in pairs(Module.Tournament.Config.TournamentTitleTable or {})do
            loginfo(v.ID,v.TitleName,v.Weapon,v.Tactical,v.Vehicle)
        end

        loginfo("TournamentLogic.PrintTables, CommanderSeasonTable")
        for k,v in pairs(Module.Tournament.Config.CommanderSeasonTable or {})do
            loginfo(v.ID,v.StartTime,v.EndTime,v.UIStartTime,v.UIEndTime,v.TimeDisplayDigit,v.Name,v.RankPopupExplainTips,v.TitleExplainTips,v.CoreAward,v.CoreTier,v.CoreAwardPicture)
        end

        loginfo("TournamentLogic.PrintTables, CommanderTierTable")
        for k,v in pairs(Module.Tournament.Config.CommanderTierTable or {})do
            local seasonStr=fTransArrytoStr(v.SeasonID)
            loginfo(v.ID,v.TierTypeID,v.SubID,v.Name,v.Type,seasonStr,v.MinPoint,v.StarsDivided,v.BadgeIcon.AssetPathName,v.BadgeIconThumbnail.AssetPathName,v.BadgeMinIcon.AssetPathName,v.BadgeMinIconThumbnail.AssetPathName)
        end

        loginfo("TournamentLogic.PrintTables, CommanderRewardsTable")
        for k,v in pairs(Module.Tournament.Config.CommanderRewardsTable or {})do
            local rewardStr=fTransArrytoStr(v.Rewards)
            local seasonRewardStr=fTransArrytoStr(v.SeasonRewards)
            loginfo(v.ID,v.TierID,v.SeasonID,v.RewardConditionPar,rewardStr,seasonRewardStr)
            
        end

    end

    TournamentLogic.IsInSeasonInitProtectTime = function()--赛季重置之后的保护时间，用于显示赛季重置弹窗
        loginfo("TournamentLogic.IsInSeasonInitProtectTime")
        local curTime=Facade.ClockManager:GetLocalTimestamp()
        local newSeasonComingTime=Module.Tournament.Field:GetNewSeasonComingTime()
        if newSeasonComingTime and curTime>=newSeasonComingTime and curTime<=newSeasonComingTime+10 then
            return true,newSeasonComingTime+10-curTime
        else
            return false
        end
    end

    TournamentLogic.GetIsShowingSeasonRestartWindow = function()
        return Module.Tournament.Field:GetIsShowingSeasonRestartWindow()
    end

    TournamentLogic.SetIsShowingSeasonRestartWindow = function(isShowingSeasonRestartWindow)
        Module.Tournament.Field:SetIsShowingSeasonRestartWindow(isShowingSeasonRestartWindow)
    end

    return TournamentLogic
