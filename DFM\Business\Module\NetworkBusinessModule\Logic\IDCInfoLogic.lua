----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMNetworkBusiness)
----- LOG FUNCTION AUTO GENERATE END -----------

local IDCInfoLogic = {}
local LocalizeTool          = require "DFM.StandaloneLua.BusinessTool.LocalizeTool"
function IDCInfoLogic.OnInitModule()
   
end

function IDCInfoLogic.OnDestoryModule()
   
end

function IDCInfoLogic.GetIdcDesc(area, delay)
    local delayDesc = ""
    if delay > 160 then
        delayDesc = '%s <customstyle color=\"Color_DarkNegative\">%s</>ms'
    elseif 100 < delay and delay <= 160 then
        delayDesc = '%s <customstyle color=\"Color_Highlight01\">%s</>ms'
    elseif delay > 0 and delay <= 100 then
        delayDesc = '%s <customstyle color=\"Color_Highlight02\">%s</>ms'
    else
        delayDesc = Module.NetworkBusiness.Config.Loc.IDCInfo_Unknow
    end
    area = IDCInfoLogic._GetAreaNameByLoc(area)
    return string.format(delayDesc, area, tostring(delay))
end

function IDCInfoLogic.GetIdcRtt(callback)
    if callback == nil then
        logerror("[IDCInfoLogic] GetIdcRtt, callback is nil")
        return
    end

    local req = pb.CSGetMatchAreaRttReq:New()
    local rttback = function(res)
        if res.result == 0 then
            table.sort(res.area_list, function(a, b)
                local indexA = IDCInfoLogic._GetIdcIndex(a.area)
                local indexB = IDCInfoLogic._GetIdcIndex(b.area)
                return indexA > indexB
            end)

            callback(res)
        else
            logerror("[IDCInfoLogic] GetIdcRtt failed, result: %d", res.result)  
        end
    end
    req:Request(rttback)
end

function IDCInfoLogic.SetSelectIdc(area)
    logerror("[IDCInfoLogic] SetSelectIdc, area:",area)
    if area == nil or area == "" then
        logerror("[IDCInfoLogic] SetSelectIdc, area is nil or empty")
        return
    end

    local req = pb.CSMatchDataSetPlayerMatchAreaReq:New()
    req.match_area = area
    local callback = function(res)
        if res.result == 0 then
            logerror("[IDCInfoLogic] SetSelectIdc, result: %d", res.result)  
        end
    end

    req:Request(callback)
end

function IDCInfoLogic.GetSelectIdcInfo(callback)
    if callback == nil then
        logerror("[IDCInfoLogic] GetSelectIdcInfo, callback is nil")
        return
    end

    local req = pb.CSMatchDataGetPlayerMatchAreaReq:New()
    local rttback = function(res)
        if res.result == 0 then
            callback(res)
            Module.NetworkBusiness.Field:SetCurSelectedIdc(res.match_area)
        else
            logerror("[IDCInfoLogic] GetSelectIdcInfo failed, result: %d", res.result)  
        end
    end

    req:Request(rttback, {bEnableHighFrequency = true})
end


function IDCInfoLogic._GetIdcIndex(area)
    local idcInfo = Facade.TableManager:GetTable("MatchIdcToAreaConfig")
    if idcInfo then
        for key,addr in pairs(idcInfo) do
            if string.find(addr.AreaName, area) then
                return addr.Index
            end
        end
    else
        logerror("[IDCInfoLogic] _GetIdcIndex, idcInfo is nil")
    end
    return 100
end

function IDCInfoLogic._GetAreaNameByLoc(area)
    local idcInfo = Facade.TableManager:GetTable("MatchIdcToAreaConfig")
    if idcInfo then
        for key,addr in pairs(idcInfo) do
            if string.find(addr.AreaName, area) then
                return LocalizeTool.GetTransStr(addr.LocAreaName)
            end
        end
    else
        logerror("[IDCInfoLogic] _GetAreaNameByLoc, idcInfo is nil")
    end
    return area
end

return IDCInfoLogic