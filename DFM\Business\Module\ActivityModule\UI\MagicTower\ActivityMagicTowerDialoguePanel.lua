----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class ActivityMagicTowerDialoguePanel : LuaUIBaseView
local ActivityMagicTowerDialoguePanel = ui("ActivityMagicTowerDialoguePanel")
local ActivityMagicTowerDialogueBtn = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerDialogueBtn"
local ActivityConfig = Module.Activity.Config
local ActivityInputHandler = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityInputHandler"
local ConfigManager = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerConfigManager"

function ActivityMagicTowerDialoguePanel:Ctor()
    self._activityID = 0
    self._dialogueID = 0
    self._startLine = 0
    self._curLineID = 0
    self._isNPC = false
    
    -- 左侧NPC头像
    self._wtNPCHeadIcon = self:Wnd("DFImage_215", UIImage) 
    -- 左侧NPC立绘
    self._wtNPCPanel = self:Wnd("DFCanvasPanel_1", UIWidgetBase)
    self._wtNPCImg = self:Wnd("DFCDNImage_0", DFCDNImage)
    -- 右侧主角立绘
    self._wtHeroPanel = self:Wnd("DFCanvasPanel_0", UIWidgetBase)
    
    -- 说话者名称
    self._wtSpeakerNameTxt = self:Wnd("DFTextBlock_0", UITextBlock)
    -- 说话内容
    self._wtDialogueTxt = self:Wnd("DFRichTextBlock_0", UITextBlock)
    
    -- 继续面板
    if IsHD() then
        self._wtContinuePanel = self:Wnd("DFCanvasPanel_248", UIWidgetBase)
    else
        self._wtContinuePanel = self:Wnd("DFCanvasPanel", UIWidgetBase)
    end

    -- 端手通用的全屏响应
    self._wtContinueBtn = self:Wnd("DFButton_69", UIButton)
    self._wtContinueBtn:Event("OnClicked", self.OnContinueClicked, self)
    
    -- 回答选项面板
    self._wtAnswerPanel = self:Wnd("PlatformPaddingBox_4", UIWidgetBase)
    
    -- 回答选项按钮
    self._wtDialogueBtns = {
        self:Wnd("WBP_MorgenGame_DialogueBtn_4", ActivityMagicTowerDialogueBtn),
        self:Wnd("WBP_MorgenGame_DialogueBtn_3", ActivityMagicTowerDialogueBtn),
        self:Wnd("WBP_MorgenGame_DialogueBtn_2", ActivityMagicTowerDialogueBtn),
        self:Wnd("WBP_MorgenGame_DialogueBtn_1", ActivityMagicTowerDialogueBtn),
        self:Wnd("WBP_MorgenGame_DialogueBtn", ActivityMagicTowerDialogueBtn),
    }
end

-----------------------------------------------------生命周期-----------------------------------------------------
function ActivityMagicTowerDialoguePanel:OnInitExtraData(parentWidget, activityID, dialogueID, FinishCallBack)
    self._activityID = activityID or 0
    self._dialogueID = dialogueID or 0
    self._parentWidget = parentWidget or nil
    self.FinishCallBack = FinishCallBack or nil

    self:_InitData()
end

function ActivityMagicTowerDialoguePanel:OnShowBegin()
    self:BindBackAction()
    self:RefreshDialogue()
end

function ActivityMagicTowerDialoguePanel:OnHideBegin()
    self:RemoveAllActions()
    self:RemoveAllLuaEvent()
end

function ActivityMagicTowerDialoguePanel:OnNavBack()
    if self.FinishCallBack then
        self:FinishCallBack()
    else
        ActivityInputHandler.EnableInput()
    end
    
    Facade.UIManager:CloseUI(self)
end

function ActivityMagicTowerDialoguePanel:BindBackAction()
    if self._backActionHandle then
        self:RemoveInputActionBinding(self._backActionHandle)
        self._backActionHandle = nil
    end

    self._backActionHandle = self:AddInputActionBinding(
        "Back",
        EInputEvent.IE_Pressed,
        self.OnNavBack,
        self,
        EDisplayInputActionPriority.UI_Pop
    )
end

function ActivityMagicTowerDialoguePanel:RemoveAllActions()
    if self._backActionHandle then
        self:RemoveInputActionBinding(self._backActionHandle)
        self._backActionHandle = nil
    end
end

function ActivityMagicTowerDialoguePanel:OnClose()
    self._wtDialogueBtns = nil
    self._parentWidget = nil
    self.FinishCallBack = nil
end

-----------------------------------------------------数据区-----------------------------------------------------
function ActivityMagicTowerDialoguePanel:_InitData()
    self._dialogueGroupConfig = ConfigManager.GetDialogueGroupConfigTable()
    self._dialogueLineConfig = ConfigManager.GetDialogueLineConfigTable()
    self._answerBranchConfig = ConfigManager.GetAnswerBranchConfigTable()
    self._attributeChangeConfig = ConfigManager.GetAttributeChangeConfigTable()

    self._propConfig = ConfigManager.GetPropConfigTable()
    self._enemyConfig = ConfigManager.GetEnemyConfigTable()
    self._npcConfig = ConfigManager.GetNPCConfigTable()
    self._heroConfig = ConfigManager.GetHeroConfigTable()

    self._startLine = self:_FindStartLine(self._dialogueID)
    self._curLineID = self._startLine
end

function ActivityMagicTowerDialoguePanel:_FindStartLine(dialogueID)
    local dialogueGroups = {}
    for _, group in pairs(self._dialogueGroupConfig or {}) do
        if group.belongingDialogue == dialogueID then
            table.insert(dialogueGroups, group)
        end
    end

    for _, group in ipairs(dialogueGroups) do
        if self:_CheckCondition(group.condition) then
            return group.startLine
        end
    end

    return 0
end

function ActivityMagicTowerDialoguePanel:_CheckCondition(conditionID)
    -- 条件表查询

    return true
end

-----------------------------------------------------对话逻辑-----------------------------------------------------
function ActivityMagicTowerDialoguePanel:RefreshDialogue()
    if not self._curLineID or self._curLineID == 0 then
        self:OnNavBack()
        return
    end
    
    local lineCfg = self._dialogueLineConfig[self._curLineID]
    if not lineCfg then
        logerror(string.format("[Magic_Tower] Dialogue line config not found! ID: %d", self._curLineID))
        self:OnNavBack()
        return
    end

    -- 设置说话者
    local speakerName = ""
    self._isNPC = true

    local itemType = self._parentWidget:_ParseItemTypeByID(tostring(lineCfg.speaker))
    local configTable = ({
        ["enemy"] = self._enemyConfig,
        ["npc"  ] = self._npcConfig,
        ["prop" ] = self._propConfig,
        ["hero" ] = self._heroConfig,
    })[itemType]

    if self._parentWidget and lineCfg.speaker and lineCfg.speaker ~= 0 then
        if configTable and configTable.name then
            speakerName = configTable.name
            self._isNPC = (itemType ~= "hero")  -- 只有hero类型设为false
        else
            speakerName = lineCfg.forcedSpeaker or ""
        end
    elseif lineCfg.forcedSpeaker and lineCfg.forcedSpeaker ~= "" then
        speakerName = lineCfg.forcedSpeaker
        self._isNPC = true
    end
        
    -- 设置说话时飘窗
    Module.CommonTips:ShowSimpleTip(lineCfg.tipStart)

    -- 设置对话内容
    self._wtDialogueTxt:SetText(lineCfg.text)
    
    -- 设置人物立绘或头像
    if self._isNPC then
        self._wtNPCPanel:SelfHitTestInvisible()
        self._wtNPCHeadIcon:SelfHitTestInvisible()
        self._wtHeroPanel:Collapsed()

        if configTable then
            if configTable.portraitAsset and configTable.portraitAsset ~= "" then
                -- self._wtNPCImg:SetCDNImage(configTable.portraitAsset)
                self._wtNPCImg:AsyncSetImagePath(configTable.portraitAsset) -- debug
            end
            
            if configTable.avatarIsset and configTable.avatarIsset ~= "" then
                self._wtNPCHeadIcon:AsyncSetImagePath(configTable.avatarIsset)
            end
        end
    else
        self._wtNPCPanel:Collapsed()
        self._wtNPCHeadIcon:Collapsed()
        self._wtHeroPanel:SelfHitTestInvisible()
    end

    -- 检查是否有回答选项
    local hasAnswers = false
    for i = 1, 5 do
        local answerID = lineCfg["answerBranch"..i]
        if answerID and answerID ~= 0 then
            hasAnswers = true
            break
        end
    end
    
    if hasAnswers then
        self:ShowAnswerOptions(lineCfg)
    else
        self:ShowContinueOption()
    end
end

function ActivityMagicTowerDialoguePanel:ShowAnswerOptions(lineCfg)
    self._wtAnswerPanel:SelfHitTestInvisible()
    self._wtContinuePanel:Collapsed()
    
    local visibleBtnCount = 0
    
    for i = 1, 5 do
        local btn = self._wtDialogueBtns[i]
        local answerID = lineCfg["answerBranch"..i]
        
        if answerID and answerID ~= 0 then
            local answerCfg = self._answerBranchConfig[answerID]
            if answerCfg then
                local attributeCfg = self._attributeChangeConfig[answerCfg.attributeChangeResult]
                local attributeTxt = ""
                if attributeCfg then
                    attributeTxt = attributeCfg.expChangeValue
                end
                btn:SetAttributeTxt(attributeTxt)
                btn:SetDialogueTxt(answerCfg.text)
                btn:SetExpIcon(ActivityConfig.EMagicTowerImgPath[1])
                -- 临时写法，后续移到Ctor回调绑定
                btn._wtAnswerBtn:Event("OnClicked", function()
                    self:OnAnswerSelected(answerCfg)
                end)
                btn:SelfHitTestInvisible()
                visibleBtnCount = visibleBtnCount + 1
            else
                btn:Collapsed()
            end
        else
            btn:Collapsed()
        end
    end
end

function ActivityMagicTowerDialoguePanel:ShowContinueOption()
    self._wtAnswerPanel:Collapsed()
    self._wtContinuePanel:SelfHitTestInvisible()
end

function ActivityMagicTowerDialoguePanel:OnContinueClicked()
    local lineCfg = self._dialogueLineConfig[self._curLineID]
    if not lineCfg then
        self:OnNavBack()
        return
    end

    if lineCfg.cancelAction == 1 and self._parentWidget then
        self._parentWidget._isCombatCancel = true -- 标记取消战斗
    end
    
    if lineCfg.nextLine and lineCfg.nextLine ~= 0 then
        self._curLineID = lineCfg.nextLine
        self:RefreshDialogue()
    else
        self:OnNavBack()
    end
end

function ActivityMagicTowerDialoguePanel:OnAnswerSelected(answerCfg)
    if answerCfg.tip and answerCfg.tip ~= "" then
        Module.CommonTips:ShowSimpleTip(answerCfg.tip)
    end

    -- 处理回答选项的效果（属性变化等）
    if answerCfg.attributeChangeResult and answerCfg.attributeChangeResult ~= 0 then
        if self._parentWidget and self._parentWidget._ApplyAttributeChange then
            self._parentWidget:_ApplyAttributeChange(answerCfg.attributeChangeResult)
        end
    end
    
    -- 处理条件属性变化（一定是经验）
    if answerCfg.attributeChangeCondition and answerCfg.attributeChangeCondition ~= 0 then
        if self._parentWidget and self._parentWidget._ApplyAttributeChange then
            self._parentWidget:_ApplyAttributeChange(answerCfg.attributeChangeCondition)
        end
    end

    if answerCfg.cancelAction == 1 and self._parentWidget then
        self._parentWidget._isCombatCancel = true -- 标记取消战斗
    end

    if answerCfg.nextLine and answerCfg.nextLine ~= 0 then
        self._curLineID = answerCfg.nextLine
        self:RefreshDialogue()
    elseif answerCfg.cycleType and answerCfg.cycleType == 2 then
        return
    else
        self:OnNavBack()
    end
end

return ActivityMagicTowerDialoguePanel