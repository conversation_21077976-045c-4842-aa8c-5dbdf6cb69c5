local ItemConfigTool = {}

-----------------------------------------------------------------------
--region Item Config Cache

local itemCfgCache = {}

--endregion
-----------------------------------------------------------------------

--- 检查道具是否有基本的配置信息
function ItemConfigTool.CheckItemHasConfig(itemId)
    return ItemConfigTool.GetItemConfigById(itemId) ~= nil
end

function ItemConfigTool.GetBuffById(id)
    local itemBuffTable = Facade.TableManager:GetTable("BuffEffect")

    if type(id) == "number" then
        id = tostring(id)
    end

    local buffRow = itemBuffTable[id]
    if buffRow then
        return buffRow
    else
        logerror(string.format("Buff ID [%d] not configured, please check!", id))
    end
    -- local UDFMBuffUtil = import "DFMBuffUtil"
    -- local buffRowStruct = UDFMBuffUtil.GetBuffEffectRowStruct(id)
    -- if buffRowStruct.BuffID == 0 then
    --     logerror(string.format("Buff ID [%d] not configured, please check!", id))
    --     return nil
    -- else
    --     return buffRowStruct
    -- end
end

--- 根据Id获取标签表现信息
---@param buffId number
function ItemConfigTool.GetBuffPerformanceByType(buffId)
    local itemBuffPerformanceTable = Facade.TableManager:GetTable("BuffClientPerformance")

    if type(buffId) ~= "string" then
        buffId = tostring(buffId)
    end

    local buffRow = itemBuffPerformanceTable[buffId]
    if buffRow then
        return buffRow
    else
        logerror(string.format("Buff ID [%s] not configured, please check!", buffId))
    end
end

--- 根据Id获取标签表现信息
---@param buffType number
function ItemConfigTool.GetBuffPerformanceByTypeEnum(buffType)
    local itemBuffPerformanceTable = Facade.TableManager:GetTable("BuffClientPerformance")
    local buffRow = itemBuffPerformanceTable:GetRowByColumn("BuffType", buffType)
    if buffRow then
        return buffRow
    else
        logerror(string.format("Buff Type [%s] not configured, please check!", buffType))
    end
end

--- 根据Id获取标签信息
---@param id number
function ItemConfigTool.GetLabelById(id)
    local itemLabelTable = Facade.TableManager:GetTable("ItemLabel")

    if type(id) == "number" then
        id = tostring(id)
    end

    local labelRow = itemLabelTable[id]
    if labelRow then
        return labelRow
    else
        logbox(string.format("配置了不合法的道具标签ID[%d]，需要检查配置@策划", id))
    end
end

--- 根据Id获取道具配置信息
---@param id number
function ItemConfigTool.GetItemConfigById(id)
    local itemTable = Facade.TableManager:GetTable("GameItem")
    if id == nil then
        loginfo('传入的itemId为空，请检查')
        return
    end
    if type(id) == "number" then
        id = tostring(id)
    end
    return itemTable[id]
end

local GameItemNamePrefixTable = Facade.TableManager:GetTable("GameItemNamePrefix") or {}
local mapCombineType2PrefixName = {}
for k, v in pairs(GameItemNamePrefixTable) do
    local firstFourDigit = v.MainType * 100 + v.SubType
    mapCombineType2PrefixName[firstFourDigit] = v.PrefixName
end

--- 根据一级二级Id获取道具Name前缀信息
---@param id number
function ItemConfigTool.GetNamePrefix(itemMainType, itemSubType)
    if type(itemMainType) ~= "number" or type(itemSubType) ~= "number" then
        logerror('传入的类型错误，请检查 itemMainType itemSubType : ', itemMainType, itemSubType)
        return nil
    end
    
    local firstFourDigit = itemMainType * 100 + itemSubType
    return mapCombineType2PrefixName[firstFourDigit]
end

---@param id number
---@return string
function ItemConfigTool.GetItemName(id)
    local config = ItemConfigTool.GetItemConfigById(id)
    if config == nil then
        return ""
    end

    local itemName = config.Name
    if not CloseModuleType.bIsCloseDebugItemInfomation then
        return string.format("%s-%d", itemName, id)
    end
    return itemName
end

---@param id number
---@return string
function ItemConfigTool.GetItemSubName(id)
    local config = ItemConfigTool.GetItemConfigById(id)
    if config == nil then
        return ""
    end

    local itemSubName = config.SubName
    return itemSubName
end

---@param id number
---@return number
function ItemConfigTool.GetItemQuality(id)
    local config = ItemConfigTool.GetItemConfigById(id)
    if config == nil then
        return 0
    end

    local quality = config.Quality
    return quality
end

---@param id number
---@return number
function ItemConfigTool.GetItemSubTagID(id)
    local config = ItemConfigTool.GetItemConfigById(id)
    if config == nil then
        return 0
    end

    local itemSubTagID = config.TagFilterSub
    return itemSubTagID
end

--- 根据Id获取道具价值配置信息
---@param id number
function ItemConfigTool.GetItemValueById(id)
    local itemPriceTable = Facade.TableManager:GetTable("ItemPriceTable")

    if type(id) == "number" then
        id = tostring(id)
    end

    local valueInfo = itemPriceTable[id]
    if valueInfo then
        return valueInfo.Price
    else
        return 0
    end
end

function ItemConfigTool.GetBodyArmorFunctionById(id)
    local bodyArmorFunctionTable = Facade.TableManager:GetTable("Armor/BodyArmorFunction")
    if type(id) == "number" then
        id = tostring(id)
    end
    return bodyArmorFunctionTable[id]
end

function ItemConfigTool.GetHelmetArmorFunctionById(id)
    local helmetArmorFunctionTable = Facade.TableManager:GetTable("Armor/HelmetArmorFunction")
    if type(id) == "number" then
        id = tostring(id)
    end
    return helmetArmorFunctionTable[id]
end

--- 根据Id获取道具资源信息
---@param id number
function ItemConfigTool.GetItemAssetById(id)
    if not id or id == 0 or string.isempty(id) then
        return nil
    end
    local itemAssetTable = Facade.TableManager:GetTable("CommonItemAsset")

    if type(id) == "number" then
        id = tostring(id)
    end

    return itemAssetTable and itemAssetTable[id] or nil
end

--- 根据ID获取道具Icon信息
---@param id number
function ItemConfigTool.GetItemIconFromID(id)
    local itemAsseet =  ItemConfigTool.GetItemAssetById(id)
    if itemAsseet == nil then
        return nil
    end
    return itemAsseet.ItemIconPath
end


--- 根据Id获取道具药品信息
---@param id number
function ItemConfigTool.GetHealthInfoById(id)
    local itemHealthTable = Facade.TableManager:GetTable("ItemHealth")

    if type(id) == "number" then
        id = tostring(id)
    end

    return itemHealthTable[id]
end

local HUD_QUALITY_PREFIX = "ItemQualityHUD_"
function ItemConfigTool.GetHudQualityLinearColor(quality)
    local rowName = HUD_QUALITY_PREFIX .. quality
    local linerColor = Facade.ColorManager:GetLinerColor(rowName)
    if linerColor then
        return linerColor
    else
        -- logwarning(string.format("Linear color for quality [%d] not found.", quality))
    end
    -- local colorTable = Facade.TableManager:GetTable("DFMColorDefine")
    -- local colorRow = colorTable[rowName]

    -- if colorRow then
    --     return colorRow.LinearColor
    -- else
    --     -- logwarning(string.format("Linear color for quality [%d] not found.", quality))
    -- end
end

ItemConfigTool.EQualityPrefix = {
    [0] = "White",
    [1] = "White",
    [2] = "Green",
    [3] = "Blue",
    [4] = "Purple",
    [5] = "Orange",
    [6] = "Red"
}
-- local QUALITY_PREFIX = "ItemQuality_"
local QUALITY_PREFIX = "A00"
local COLORKEY = "Quality_Light_"

function ItemConfigTool.GetItemQualityLinearColor(quality, opacity)
    local colorKey = ItemConfigTool.EQualityPrefix[quality] and ItemConfigTool.EQualityPrefix[quality] or nil
    if colorKey then
        return Facade.ColorManager:GetLinerColor(COLORKEY .. colorKey, opacity)
    else
        loginfo(string.format("Linear color for quality [%d] not found.", quality))
    end
    --[[
    local rowName = QUALITY_PREFIX .. quality
    local colorTable = Facade.TableManager:GetTable("DFMColorDefine")
    local colorRow = colorTable[rowName]

    if colorRow then
        return colorRow.LinearColor
    else
        -- logwarning(string.format("Linear color for quality [%d] not found.", quality))
    end
    ]]--
end

function ItemConfigTool.GetItemQualitySlateColor(quality)
    local colorKey = ItemConfigTool.EQualityPrefix[quality] and ItemConfigTool.EQualityPrefix[quality] or nil
    if colorKey then
        return Facade.ColorManager:GetSlateColor(COLORKEY .. colorKey)
    else
        loginfo(string.format("Linear color for quality [%d] not found.", quality))
    end
    --[[
    local rowName = QUALITY_PREFIX .. quality
    local colorTable = Facade.TableManager:GetTable("DFMColorDefine")
    local colorRow = colorTable[rowName]

    if colorRow then
        return colorRow.GetSlateColor(colorRow)
    else
        -- logwarning(string.format("Linear color for quality [%d] not found.", quality))
    end
    ]]--
end

function ItemConfigTool.GetItemQualityColor(quality)
    local rowName = QUALITY_PREFIX .. quality
    local linerColor = Facade.ColorManager:GetLinerColorByRowName(rowName)
    if linerColor and linerColor.ToFColor then
        return linerColor:ToFColor(true) --FColor
    end
    -- local colorTable = Facade.TableManager:GetTable("DFMColorDefine")
    -- local colorRow = colorTable[rowName]

    -- if colorRow then
    --     return colorRow.GetColor(colorRow)
    -- else
    --     -- logwarning(string.format("Linear color for quality [%d] not found.", quality))
    -- end
end

---@param uiImage UIImage
function ItemConfigTool.SetQualityColorByQuality(uiImage, quality)
    local qualityColor = ItemConfigTool.GetItemQualityLinearColor(quality)
    if qualityColor then
        uiImage:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        uiImage:SetColorAndOpacity(qualityColor)
        return true
    else
        uiImage:SetVisibility(ESlateVisibility.Collapsed)
        return false
    end
end

function ItemConfigTool.GetMaxStacksNumById(id)
    local itemConfig = Facade.TableManager:GetTable("GameItem")[tostring(id)]
    if itemConfig then
        local stackNum = itemConfig.MaxStackCount
        return stackNum == 0 and 1 or stackNum
    else
        logwarning(id)
        return 1
    end
end

--- 根据Id获取道具英雄信息
---@param id number
function ItemConfigTool.GetHeroInfoById(id)
    local heroDataTable = Facade.TableManager:GetTable("Hero/HeroData")

    if type(id) == "number" then
        id = tostring(id)
    end

    return heroDataTable[id]
end

--- 根据Id获取武器Perk信息
---@param id number
function ItemConfigTool.GetWeaponPerkInfoById(id)
    local weaponPerkTable = Facade.TableManager:GetTable("WeaponPart/WeaponPerkTable")

    if type(id) == "number" then
        id = tostring(id)
    end

    return weaponPerkTable[id]
end

--获取武器类型名字
---@param id number
function ItemConfigTool.GetWeaponTypeNameByType(type)
    return ItemConfig.MapWeaponItemType2Name[type]
end


function ItemConfigTool.GetTokenItemPathById(tokenId)
    local currencyType = MapCurrencyId2ClientType[tokenId]
    if currencyType then
        return ECurrencyClientType2ImgPath[currencyType]
    end

    return nil
end

function ItemConfigTool.GetKeyCellUnlockItemId(keyChainId, mapId, lvl)
    local keyUnlockTb = Facade.TableManager:GetTable("Key/KeyBoxUnlock")
    if keyUnlockTb then
        for key, row in pairs(keyUnlockTb) do
            if tonumber(row.KeyBoxID) == keyChainId and row.MapID == mapId and row.SlotLevel == lvl then
                return tonumber(key)
            end
        end
    end

    return 0
end

function ItemConfigTool.GetKeyBoxUnlockItemRowById(id)
    local keyBoxUnlockTb = Facade.TableManager:GetTable("Key/KeyBoxUnlock")

    if type(id) == "number" then
        id = tostring(id)
    end

    return keyBoxUnlockTb[id]
end

function ItemConfigTool.GetKeyBoxCfgListId(id)
    local keyBoxTable = Facade.TableManager:GetTable("Key/KeyBox")
    local ret = {}

    if type(id) == "number" then
        id = tostring(id)
    end

    for _, cfg in ipairs(keyBoxTable) do
        if cfg.ItemID == id then
            table.insert(ret, cfg)
        end
    end

    return ret
end

local spaceOrderMapping = nil
function ItemConfigTool.GetKeyBoxSpaceOrder(keyChainId, spaceIndex)
    if not spaceOrderMapping then
        spaceOrderMapping = {}
        local keyBoxTable = Facade.TableManager:GetTable("Key/KeyBox")
        for key, row in pairs(keyBoxTable) do
            local id = tonumber(row.ItemID)
            local mapId = row.MapID
            if not spaceOrderMapping[id] then
                spaceOrderMapping[id] = {}
            end
            spaceOrderMapping[id][mapId] = row.Index
        end
    end

    local tmp = spaceOrderMapping[keyChainId]
    if tmp then
        return tmp[spaceIndex]
    end

    return 0
end

function ItemConfigTool.GetExtentionBoxDescRowById(id)
    local extentionBoxDescTb = Facade.TableManager:GetTable("ExtensionBoxDesc")
    return extentionBoxDescTb:GetRowByColumn("ItemID", id)
end

function ItemConfigTool.GetExtItemLevelUpItemId(id)
    local extDesRow = ItemConfigTool.GetExtentionBoxDescRowById(id)

    if not extDesRow or extDesRow.NextLvItemID <= 0 then
        return 0
    end

    return extDesRow.NextLvItemID
end

function ItemConfigTool.GetExtItemLevelUpMaterialList(id)
    local ret = {}
    local extDesRow = ItemConfigTool.GetExtentionBoxDescRowById(id)

    if not extDesRow or extDesRow.NextLvItemID <= 0 then
        return ret
    end

    local nextLvExtDesRow = ItemConfigTool.GetExtentionBoxDescRowById(extDesRow.NextLvItemID)
    if not nextLvExtDesRow then
        return ret
    end

    for i = 1, ItemConfig.MAX_LEVEL_UP_MATERIAL_NUM do
        local key1 = string.format("MaterialItemID%d", i)
        local key2 = string.format("MaterialNum%d", i)

        local materialId = nextLvExtDesRow[key1]
        local materialNum = nextLvExtDesRow[key2]

        if materialId > 0 then
            ret[materialId] = materialNum
        else
            break
        end
    end

    return ret
end

local itemTagFilterMapTable = Facade.TableManager:GetTable("ItemTagFilterMap")
local mapId2TagName = {}
for k,v in pairs(itemTagFilterMapTable) do
    mapId2TagName[tonumber(k)] = v.desc
end
function ItemConfigTool.GetItemTagName(id)
    return mapId2TagName[id] or ItemConfig.Loc.DeafultTag
end

-- 新版获取战场道具的cfg
function ItemConfigTool.GetItemArmedForceCfgNew(id)
    local armedForceItemTable = Facade.TableManager:GetTable("ArmedForceProps")
	local itemCfg = armedForceItemTable:GetRowByColumn("ItemID", id)

    if itemCfg == nil then
        logwarning("ItemConfigTool.GetItemArmedForceCfgNew nil, item id:", id)
    end

    return itemCfg
end

function ItemConfigTool.GetBagFuncTable(id)
    local table = Facade.TableManager:GetTable("BagFunc")

    local row = table[id]
    if row then
        return row
    else
        logerror(string.format("Bag Func ID [%d] not configured, please check!", id))
    end
end

function ItemConfigTool.GetChestHangingFuncTable(id)
    local table = Facade.TableManager:GetTable("ChestHangingFunc")

    local row = table[id]
    if row then
        return row
    else
        logerror(string.format("ChestHanging Func ID [%d] not configured, please check!", id))
    end
end

function ItemConfigTool.GetSafeBoxFuncTable(id)
    local table = Facade.TableManager:GetTable("SafeBoxFunc")

    local row = table[id]
    if row then
        return row
    else
        logerror(string.format("SafeBox Func ID [%d] not configured, please check!", id))
    end
end

function ItemConfigTool.GetMeleeAttributeTable(id)
    local table = Facade.TableManager:GetTable("WeaponMelee/WeaponMeleeAttributeTable")
    local itemCfg = table:GetRowByColumn("ItemId", id)

    if itemCfg == nil then
        logwarning("ItemConfigTool.GetMeleeAttributeTable nil, item id:", id)
    end

    return itemCfg
end

-- buffDebuffType: UnKnow:0 Buff:1 Debuff:2
-- buff按优点、UnKnow、缺点排序，相同类型按配置顺序排
function ItemConfigTool.SortByBuffDebuffType(a, b)
    if a.buffDebuffType ~= b.buffDebuffType then
        if a.buffDebuffType == 1 then
            return true
        elseif b.buffDebuffType == 1 then
            return false
        else
            return a.buffDebuffType < b.buffDebuffType
        end
	end
	return a.insertIDX < b.insertIDX
end

-- 通过安全箱（钥匙包）ID找到匹配的限时权限卡
function ItemConfigTool.FindPermissionActivateCard(id)
    if not Facade.GameFlowManager:CheckIsInFrontEnd() then
        return nil
    end
    local activateCardTable = {}
    local index = 0

    local table = Facade.TableManager:GetTable("SafeAndCardPackActivatingConfig")
    local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"

    local itemSubId = ItemHelperTool.GetSubTypeById(id)
    local itemLastNum = id % 10
    local function matchItemCard(cardID)
        local cardSubID = ItemHelperTool.GetSubTypeById(tonumber(cardID))
        local cardLastNum = tonumber(cardID) % 10
        local bSameType = ((itemSubId == 9 and cardSubID == 30) or (itemSubId == 12 and cardSubID == 31)) and true or false
        return bSameType and itemLastNum - cardLastNum == 1
    end
    for _, config in ipairs(table) do
        -- 还需要根据赛季拿到匹配的权限卡
        if matchItemCard(config.TheActivating) and (config.SeasonID == Server.BattlePassServer:GetSeasonID() or config.SeasonID == 0) then
            index = index + 1
            activateCardTable[index] = tonumber(config.TheActivating)
        end
    end
    return activateCardTable
end

-- 获取磨损度config
function ItemConfigTool.GetMysticalWearConfig(wearNum)
    local wearConfig = Facade.TableManager:GetTable("WeaponSkin/MysticalSkinWear")
    for _, value in ipairs(wearConfig) do
        if wearNum > value.WearMin and wearNum <= value.WearMax then
            return value.WearName
        end
    end
    loginfo('未找到对应的磨损度，请检查。')
end

-- 获取磨损度间隔范围
function ItemConfigTool.GetMysticalWearRangeByIndex(index)
    local wearConfig = Facade.TableManager:GetTable("WeaponSkin/MysticalSkinWear")
    for key, value in ipairs(wearConfig) do
        if key == index then
            return value.WearMin, value.WearMax
        end
    end
    loginfo('未找到对应的磨损度间隔范围，请检查。')
end

-- 获取磨损度最大值
function ItemConfigTool.GetMysticalMaxWear()
    local wearConfig = Facade.TableManager:GetTable("WeaponSkin/MysticalSkinWear")
    local wearMax = wearConfig and wearConfig[1] and wearConfig[1].WearMax
    for _, value in ipairs(wearConfig) do
        wearMax = math.max(wearMax, value.WearMax)
    end
    if wearMax then
        return wearMax
    end
    loginfo('未找到对应的磨损度最大值，请检查。')
end

-- 获取磨损度比例
function ItemConfigTool.GetMysticalWearRate()
    local wearConfig = Facade.TableManager:GetTable("WeaponSkin/MysticalSkinBasicPara")
    for _, value in pairs(wearConfig) do
        return value.WearCalculator or 1
    end
    loginfo('未找到对应的磨损度比例，请检查。')
    return 1
end

function ItemConfigTool.GetItemRatityID(mysticalId)
    local ratityConfig = Facade.TableManager:GetTable("WeaponSkin/MysticalSkinInfoDataTable")
    local rarity = ratityConfig:GetRowFieldByColumn("MysticalId", mysticalId, "Rarity")
    if rarity then
        return rarity
    end
    loginfo('未找到对应的稀有度，请检查。')
end

function ItemConfigTool.GetItembHasKillCnter(mysticalId)
    local ratityConfig = Facade.TableManager:GetTable("WeaponSkin/MysticalSkinInfoDataTable")
    local bHasKillCnter = ratityConfig:GetRowFieldByColumn("MysticalId", mysticalId, "bHasKillCnter")
    if bHasKillCnter then
        return bHasKillCnter
    end
    loginfo('未找到对应的稀有度，请检查。')
end

function ItemConfigTool.GetCommercializeIconPath(itemType)
    local commercializeConfig = Facade.TableManager:GetTable("CommercializeData")
    local path = commercializeConfig:GetRowByColumn("CommercializeType", itemType, "CommercializeImage")
    if path then
        return path
    end
    loginfo('config is nil, plz check CommercializeData')
end

function ItemConfigTool.IsHeroSkinItem(id)
    local heroSkinId = tostring(id)
    if string.sub(heroSkinId, 1, 4) == "3000" then
        return true
    end
    return false
end
function ItemConfigTool.GetPendantMysticalTable(mysticalId)
    local pendantMysticalTable = Facade.TableManager:GetTable("WeaponSkin/PendantMysticalSkinTable")
    if pendantMysticalTable then
        if type(mysticalId) == "number" then
            mysticalId = tostring(mysticalId)
            return pendantMysticalTable[mysticalId]
        end
    end
    return nil
end

function ItemConfigTool.GetPendantMysticalDetailInfoTable(detailId)
    local pendantMysticalTable = Facade.TableManager:GetTable("WeaponSkin/MysticalPendantDetailInfoTable")
    if pendantMysticalTable then
        if type(detailId) == "number" then
            detailId = tostring(detailId)
            return pendantMysticalTable[detailId]
        end
    end
    return nil
end

function ItemConfigTool.GetCustomizedDogTagName(customID)
    local row = Facade.TableManager:GetRowByKey("DogTagCustomInfo", tostring(customID))
    return row and row.ItemName or nil
end

function ItemConfigTool.GetCustomizedDogTagDesc(customID)
    local row = Facade.TableManager:GetRowByKey("DogTagCustomInfo", tostring(customID))
    return row and row.ItemDesc or nil
end

function ItemConfigTool.GetCustomizedDogTagMajorRankID(customID)
    local row = Facade.TableManager:GetRowByKey("DogTagCustomInfo", tostring(customID))
    return row and row.MajorRankID or 0
end

return ItemConfigTool
