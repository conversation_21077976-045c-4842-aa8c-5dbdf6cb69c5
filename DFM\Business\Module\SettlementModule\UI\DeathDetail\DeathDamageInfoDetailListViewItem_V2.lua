----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSettlement)
----- LOG FUNCTION AUTO GENERATE END -----------



local DeathDamageInfoDetailListViewItem_V2 = ui("DeathDamageInfoDetailListViewItem_V2")


local UAttackerValueDataManager = import "AttackerValueDataManager"
local EDamageSystemDamageType = import "EDamageSystemDamageType"
local LocalizeTool      = require "DFM.StandaloneLua.BusinessTool.LocalizeTool"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local UAmmoDataManager = import "AmmoDataManager"
local UHelmetFunctionMgr = import "HelmetFunctionMgr"
local UBodyArmorMgr = import "BodyArmorMgr"
local UDFMBuffUtil = import "DFMBuffUtil"
local USkillConfigUtils = import "SkillConfigUtils"
local CharacterPart = import "ECharacterPart"
local HeroHelperTool = require "DFM.StandaloneLua.BusinessTool.HeroHelperTool"
local ECharacterPart = import("ECharacterPart")

--TODO 

function DeathDamageInfoDetailListViewItem_V2:Ctor()
    print("DeathDamageInfoDetailListViewItem_V2:Ctor()")
    
    self.wtPlayerNameTxt = self.PlayerNameTxt
    self.wtSecondLineTxt = self.SecondLineTxt
    self.wtThirdLineTxt = self.ThirdLineTxt
    self.wtFourthLineTxt = self.FourthLineTxt
    self.wtButtonSelf = self.DFButton_0
    self.wtDamageTxt = self.WBP_SettlementDetailHealth.DamageTxt
    self.wtHelmetArmorDamageTxt = self.WBP_SettlementDetailHealth.DamageTxt_1
    self.wtBodyArmorDamageTxt = self.WBP_SettlementDetailHealth.DamageTxt_2
    self.wtHelmetArmorDamageTxtContainer = self.WBP_SettlementDetailHealth.DFHorizontalBox_1
    self.wtBodyArmorDamageTxtContainer = self.WBP_SettlementDetailHealth.DFHorizontalBox


    self.wtButtonSelf.OnClicked:Add(CreateCPlusCallBack(self._OnClickSelf,self))
    self.wtDetailBox = self.DetailBox
    
    self.wtCanvas_Health = self.Canvas_Health
    self.wtText_HealthValue = self.Text_HealthValue
    self.wtRadialImage_HealthBar = self.RadialImage_HealthBar
    self.wtRadialImage_HealthBar_Remnants = self.RadialImage_HealthBar_Remnants
    self.wtProgressBar_Stripe = self.ProgressBar_Stripe

    self.currentShowDetailBox = false

    self.wtPartImgs = {}
    self.wtPartImgs[CharacterPart.Head] = self.WBP_SettlementDetailHealth.HeadImg
    self.wtPartImgs[CharacterPart.Thorax] = self.WBP_SettlementDetailHealth.ThroaxImg
    self.wtPartImgs[CharacterPart.Abdomen] = self.WBP_SettlementDetailHealth.AbdomenImg
    self.wtPartImgs[CharacterPart.LeftArm] = self.WBP_SettlementDetailHealth.LeftArmImg
    self.wtPartImgs[CharacterPart.RightArm] = self.WBP_SettlementDetailHealth.RightArmImg
    self.wtPartImgs[CharacterPart.LeftLeg] = self.WBP_SettlementDetailHealth.LeftLegImg
    self.wtPartImgs[CharacterPart.RightLeg] = self.WBP_SettlementDetailHealth.RightLegImg

    self._wtExpertIcon = self:Wnd("wtExpertIcon", UIImage)  -- 攻击者兵种图标

    --- BEGIN MODIFICATION @ VIRTUOS: Platform Icon
    if IsConsole() then
        self._wtPlatformIcon = self:Wnd("wtPlatformIcon", UIImage)
        self._wtPlatformIcon:Collapsed()
    end
    --- BEGIN MODIFICATION @ VIRTUOS:  Platform Icon
end

---@param BindData FDamageRecordViewItem
function DeathDamageInfoDetailListViewItem_V2:BindData(ViewItemData,bAttacker)
    self.bAttacker = bAttacker
    self.bCanExpand = true
    local playerUin = Facade.GameFlowManager:GetPlayerUin()
    local bAttacker = ViewItemData.bAttacker
    --if bAttacker then
    --    --self:SetCppValue("Type",1)
    --    self.AttackerTitleTxt:SetText(Module.Settlement.Config.Loc.DeathDamageInfoView_AttackerTitle01)
    --
    --else
    --    --self:SetCppValue("Type",0)
    --    self.AttackerTitleTxt:SetText(Module.Settlement.Config.Loc.DeathDamageInfoView_AttackerTitle02)
    --end
    ----self:BPSetType()
    -- long1 这里原本的 命中or来自 换成了玩家名
    local PlayerName = ViewItemData.MakerInstigatorPlayerNameTEXT
    local PlayerUid = ViewItemData.MakerInstigatorUin
    local IsRobotAI = ViewItemData.MakerIsRobotAI
    local PlatID = ViewItemData.MakerPlatId
    --local IsAILabAI = ViewItemData.MakerIsAILabAI
    if bAttacker then
        PlayerName = ViewItemData.TakerInstigatorPlayerNameTEXT
        PlayerUid = ViewItemData.TakerUin
        PlatID = ViewItemData.TakerPlatId
    end

    local bCrossPlay = (IsXboxSeries() and Server.RoleInfoServer.can_cross_plat_play) or (IsPS5Family() and Server.RoleInfoServer.hasCrossPlayPrivilege)

    logerror("DeathDamageInfoDetailListViewItem_V2:BindData bAttacker, PlayerName, PlayerUid, IsRobotAI, MakerPlatId, TakerPlatId, PlatID, bCrossPlay", bAttacker, PlayerName, PlayerUid, IsRobotAI, ViewItemData.MakerPlatId, ViewItemData.TakerPlatId, PlatID, bCrossPlay)
    --- BEGIN MODIFICATION @ VIRTUOS: TRC - replace player name with platform online id.
    if IsConsole() and PlayerUid ~= 0 then
        -- Request & Set Platform Icon
        if bCrossPlay then
            if PlatID then
                self:_SetPlatformIcon(PlatID)
            else
                self:_RequestAndSetPlatform(PlayerUid)
            end
        end

        if IsPS5Family() then
            local UDFMOnlineIdentityManager = import "DFMOnlineIdentityManager"
            local OnlineIdManager = UDFMOnlineIdentityManager.Get(GetWorld())
            if OnlineIdManager then
                -- Use maker instigator player uin to get onlineId, because TakerInstigatorUin is not availiable here.
                local onlineId = OnlineIdManager:GetPlayerPlatformIdByOpenId(PlayerUid)
                
                if not string.isempty(onlineId) then
                    PlayerName = onlineId
                end
            end
        end
    end
    --- END MODIFICATION

    self.AttackerTitleTxt:SetText(PlayerName)

    if ViewItemData.TakerHeroId ~= 0 then -- 干员
        local expertIcon = HeroHelperTool.GetHeroExpertIcon(ViewItemData.TakerHeroId)
        self._wtExpertIcon:AsyncSetImagePath(expertIcon, false)
        self._wtExpertIcon:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else -- AI
        self._wtExpertIcon:SetVisibility(ESlateVisibility.Collapsed)
    end
    
    self:RefreshDetailBox()
    self:SetDamage(ViewItemData,bAttacker)
    self:SetDamageBodyPart(ViewItemData,bAttacker)
    if ViewItemData.SkillId ~= 0 then
        self:BindDataWithSkill(ViewItemData,bAttacker)
    elseif ViewItemData.WeaponId ~= 0 and ViewItemData.BuffId == 0 then
        self:BindDataWithWeapon(ViewItemData,bAttacker)
    elseif ViewItemData.BuffId ~= 0 then
        self:BindDataWithBuff(ViewItemData,bAttacker)
    else
        self:BindDataWithOtherDamage(ViewItemData)
    end

    self:SetPenetrationInfo(ViewItemData, bAttacker)
    self:SetMakerHealth(ViewItemData, bAttacker)
end

--- BEGIN MODIFICATION @ VIRTUOS: Request & Set Platform Icon
function DeathDamageInfoDetailListViewItem_V2:_RequestAndSetPlatform(playerId)
    if IsConsole() then
        local req = pb.CSAccountGetPlayerProfileReq:New()
        req.player_id = playerId
        req.client_flag = 0
        req:Request(function(res)
            if not self:IsValid() then
                return
            end
            self:_SetPlatformIcon(res.plat_id)
        end,
        {bEnableHighFrequency = true})
    end
end

function DeathDamageInfoDetailListViewItem_V2:_SetPlatformIcon(plat_id)
    if IsConsole() then
        local platIconPath = Module.Friend:GetPlatformIconPath(plat_id)
        -- 没获取到PlatIcon也需要显示一个默认Icon，暂定为PC
        -- if not platIconPath then
        --     platIconPath = Module.Friend:GetPlatformIconPath(PlatIDType.Plat_PC)
        -- end
        if platIconPath then
            self._wtPlatformIcon:AsyncSetImagePath(platIconPath, false)
            self._wtPlatformIcon:SelfHitTestInvisible()
        end
    end
end
--- END MODIFICATION @ VIRTUOS: Request & Set Platform Icon

function DeathDamageInfoDetailListViewItem_V2:BindDataWithWeapon(ViewItemData,bAttacker)
    local AttackValueData = UAttackerValueDataManager:Get():GetRow(ViewItemData.AttackerValueId)

    self:SetName(ViewItemData,false,bAttacker)

    local bMeleeWeapon = false
    local bEmptyHand = false
    local MainType = ItemHelperTool.GetSubTypeById(ViewItemData.WeaponId)
    if MainType == ItemConfig.EWeaponItemType.Melee then
        bMeleeWeapon = true
    end
    if MainType == ItemConfig.EWeaponItemType.EmptyHand then
        bEmptyHand = true
    end
    local WeaponName = ItemConfigTool.GetItemName(ViewItemData.WeaponId)
    local AttackStr = ""
    if bAttacker then
        AttackStr = LocalizeTool.GetCommonText("DeathDamageInfoDetail_AttackStr02",ECommonTextType.InGame)
    else
        AttackStr = LocalizeTool.GetCommonText("DeathDamageInfoDetail_AttackStr01",ECommonTextType.InGame)
    end
    --第二行信息
    if bMeleeWeapon then
        local TextKey_WeaponDamageArmor = "DeathDamageInfoDetail_Melee"
        local SecondLineFormatText = LocalizeTool.GetCommonText(TextKey_WeaponDamageArmor,ECommonTextType.InGame)

        local SecondLineTxt = StringUtil.PluralTextFormat(SecondLineFormatText,
        {
            ["AttackLevel"]=ViewItemData.AttackerLevel,
            ["WeaponName"] = WeaponName,
        }
        )
        self.wtSecondLineTxt:SetText(AttackStr..SecondLineTxt)
    elseif bEmptyHand then
        if ViewItemData.AttackerValueId ~= 0 then
            local AttackValueData = UAttackerValueDataManager:Get():GetRow(ViewItemData.AttackerValueId)
            self.wtSecondLineTxt:SetText(AttackStr..AttackValueData.DisplayName)
        end
    elseif ViewItemData.AmmoId ~= 0 then --子弹
        local TextKey_WeaponDamageArmor = "DeathDamageInfoDetail_EmemyAmmo"
        local SecondLineFormatText = LocalizeTool.GetCommonText(TextKey_WeaponDamageArmor,ECommonTextType.InGame)

        local AmmoDataRow = UAmmoDataManager:Get():GetRow(ViewItemData.AmmoId);
		local AmmoLevel = AmmoDataRow.PenetrateLevel;
        local SecondLineTxt = StringUtil.PluralTextFormat(SecondLineFormatText,
        {
            ["AmmoLevel"]=AmmoLevel,
            ["AmmoName"] = ItemConfigTool.GetItemName(ViewItemData.AmmoId),
            ["WeaponName"] = WeaponName,
        }
        )
        self.wtSecondLineTxt:SetText(AttackStr..SecondLineTxt)
    elseif ViewItemData.AttackerValueId ~= 0 then --无子弹
        if AttackValueData.DamageType == EDamageSystemDamageType.Point then 
            local TextKey_WeaponDamageArmor = "DeathDamageInfoDetail_EmemyNoAmmo"
            local SecondLineFormatText = LocalizeTool.GetCommonText(TextKey_WeaponDamageArmor,ECommonTextType.InGame)
            local SecondLineTxt = StringUtil.PluralTextFormat(SecondLineFormatText,
            {
                ["AttackLevel"]=ViewItemData.AttackerLevel,
                ["WeaponName"] = WeaponName,
            }
            )
            self.wtSecondLineTxt:SetText(AttackStr..SecondLineTxt)
        elseif AttackValueData.DamageType == EDamageSystemDamageType.Radial then
            local TextKey_WeaponDamageArmor = "DeathDamageInfoDetail_Radial"
            local SecondLineFormatText = LocalizeTool.GetCommonText(TextKey_WeaponDamageArmor,ECommonTextType.InGame)
            local SecondLineTxt = StringUtil.PluralTextFormat(SecondLineFormatText,
            {
                ["WeaponName"]=WeaponName,
            }
            )
            self.wtSecondLineTxt:SetText(AttackStr..SecondLineTxt)
        else
            local TextKey_WeaponDamageArmor = "DeathDamageInfoDetail_Other"
            local SecondLineFormatText = LocalizeTool.GetCommonText(TextKey_WeaponDamageArmor,ECommonTextType.InGame)
            local SecondLineTxt = StringUtil.PluralTextFormat(SecondLineFormatText,
            {
                ["WeaponName"]=WeaponName,
            }
            )
            self.wtSecondLineTxt:SetText(AttackStr..SecondLineTxt)
        end
    end

    self:SetArmorInfo(ViewItemData,bAttacker)
end



function DeathDamageInfoDetailListViewItem_V2:BindDataWithBuff(ViewItemData,bAttacker)


    local AttackStr = ""
    if bAttacker then
        AttackStr = LocalizeTool.GetCommonText("DeathDamageInfoDetail_AttackStr02",ECommonTextType.InGame)
    else
        AttackStr = LocalizeTool.GetCommonText("DeathDamageInfoDetail_AttackStr01",ECommonTextType.InGame)
    end

    self.wtThirdLineTxt:SetVisibility(ESlateVisibility.Collapsed)
    if ViewItemData.MakerInstigatorUin ~= 0 then

        self:SetName(ViewItemData,true,bAttacker)

		if ViewItemData.BuffId ~= 0 then
        	local BuffEffectRow = UDFMBuffUtil.GetBuffEffectRowStruct(ViewItemData.BuffId);
        	if BuffEffectRow then
        	
        		local BuffName = BuffEffectRow.DisplayName;
        		if BuffEffectRow.DisplayName == "" then
        			BuffName = string.format("（未配置）%s",BuffEffectRow.RowDescription)
                end
                local SecondLineFormatText = LocalizeTool.GetCommonText("DeathDamageInfoDetail_BuffDamage2",ECommonTextType.InGame);
                local SecondLineTxt = StringUtil.PluralTextFormat(SecondLineFormatText,
                {
                    ["BuffName"]=BuffName,
                    ["Damage"] = MathUtil.GetNumPrecisionOne(ViewItemData.Damage),
                }
                )
        		self.wtSecondLineTxt:SetVisibility(ESlateVisibility.SelfHitTestInvisible);
        		self.wtSecondLineTxt:SetText(AttackStr..SecondLineTxt);
        	end
        end
    else 
        if ViewItemData.BuffId ~= 0 then
        	local BuffEffectRow = UDFMBuffUtil.GetBuffEffectRowStruct(ViewItemData.BuffId);
        	if BuffEffectRow then
        	
        		local BuffName = BuffEffectRow.DisplayName;
        		if BuffEffectRow.DisplayName == "" then
        			BuffName = string.format("（未配置）%s",BuffEffectRow.RowDescription)
                end
                self.wtPlayerNameTxt:SelfHitTestInvisible()
                self.wtPlayerNameTxt:SetText(BuffName)
                self.bCanExpand = false
        	end
        end
    end
end


function DeathDamageInfoDetailListViewItem_V2:BindDataWithOtherDamage(ViewItemData)
    self:SetName(ViewItemData,true)

    
    if ViewItemData.AttackerValueId ~= 0 then
        local AttackValueData = UAttackerValueDataManager:Get():GetRow(ViewItemData.AttackerValueId)
        self.wtPlayerNameTxt:SetText(AttackValueData.DisplayName)
    end

    self.bCanExpand = false
end


function DeathDamageInfoDetailListViewItem_V2:BindDataWithSkill(ViewItemData,bAttacker)

    self:SetName(ViewItemData,false,bAttacker)

    local AttackStr = ""
    if bAttacker then
        AttackStr = LocalizeTool.GetCommonText("DeathDamageInfoDetail_AttackStr02",ECommonTextType.InGame)
    else
        AttackStr = LocalizeTool.GetCommonText("DeathDamageInfoDetail_AttackStr01",ECommonTextType.InGame)
    end
    if ViewItemData.SkillId ~= 0 then
		local SkillConfig = USkillConfigUtils.GetSkillConfigDataRowBySkillID(ViewItemData.SkillId);
		if SkillConfig.SkillID ~= 0 then
			local SkillName = SkillConfig.Name;
			
			local TextKey_SkillDamage = "DeathDamageInfoDetail_Skill";
			local SecondLineFormatText = LocalizeTool.GetCommonText(TextKey_SkillDamage,ECommonTextType.InGame);
            local SecondLineTxt = StringUtil.PluralTextFormat(SecondLineFormatText,
            {
                ["SkillName"]= SkillName,
                ["AttackLevel"] = ViewItemData.AttackerLevel,
            }
            )
            self.wtSecondLineTxt:SetText(AttackStr..SecondLineTxt)
        else
            self.wtSecondLineTxt:SetText(string.format("SkillId %d 未配置",ViewItemData.SkillId))
        end
	end
    self:SetArmorInfo(ViewItemData,bAttacker)
end

function DeathDamageInfoDetailListViewItem_V2:SetName(ViewItemData,bHideDist,bAttacker)
    local TextKey_WeaponDamageFirstLine = "DeathDamageInfoDetailNew_PlayerName"
    if bHideDist then
        TextKey_WeaponDamageFirstLine = "DeathDamageInfoDetailNew_PlayerName_NoDist"
    end
    local FirstLineFormatText = LocalizeTool.GetCommonText(TextKey_WeaponDamageFirstLine,ECommonTextType.InGame)
    local Distance = math.floor(ViewItemData.Distance * 0.01)
    local WeaponName =  ItemConfigTool.GetItemName(ViewItemData.WeaponId)
    local Name = ViewItemData.MakerInstigatorPlayerNameTEXT
    if bAttacker then
        Name = ViewItemData.TakerInstigatorPlayerNameTEXT
    end

    local ActionName = "" --命中or来自
    local bAttacker = ViewItemData.bAttacker
    if bAttacker then
        ActionName = Module.Settlement.Config.Loc.DeathDamageInfoView_AttackerTitle01
    else
        ActionName = Module.Settlement.Config.Loc.DeathDamageInfoView_AttackerTitle02
    end
    
    local FirstLineTxt = StringUtil.PluralTextFormat(FirstLineFormatText,
    {
        ["PlayeName"]=ActionName, -- long1 这里原本的玩家名换成 命中or来自
        ["Dist"] = Distance,
    }
    )
    self.wtPlayerNameTxt:SetText(FirstLineTxt)
    if bHideDist then -- buff伤害不显示距离
        self.wtPlayerNameTxt:Collapsed()
    end
end

function DeathDamageInfoDetailListViewItem_V2:SetArmorInfo(ViewItemData,bAttacker)
    local ArmorStr = ""
    if bAttacker then
        ArmorStr = LocalizeTool.GetCommonText("DeathDamageInfoDetail_ArmorStr01",ECommonTextType.InGame)
    else
        ArmorStr = LocalizeTool.GetCommonText("DeathDamageInfoDetail_ArmorStr02",ECommonTextType.InGame)
    end
    local NoArmorButHaveArmorDamage = (ViewItemData.ArmorItemId == 0 and ViewItemData.ArmorDamage > 0) -- 比如长官赛伊德，有护甲装备但是没有护甲id，有护甲伤害时要显示伤害信息
    if NoArmorButHaveArmorDamage then
        ArmorStr = LocalizeTool.GetCommonText("DeathDamageInfoDetail_ArmorStr03",ECommonTextType.InGame)
    end
    -- 第三行信息
    if ViewItemData.ArmorItemId ~= 0 or NoArmorButHaveArmorDamage then
        local ArmorName = ItemConfigTool.GetItemName(ViewItemData.ArmorItemId)
        local itemSubType = ItemHelperTool.GetSubTypeById(ViewItemData.ArmorItemId)
        local ArmorLevel = 0
        if itemSubType == EEquipmentType.Helmet then
            local HelmetFunctionRow= UHelmetFunctionMgr.Get():GetRow(ViewItemData.ArmorItemId)
            ArmorLevel = HelmetFunctionRow.ArmorLevel
        elseif itemSubType == EEquipmentType.BreastPlate then
            local BodyArmorRow= UBodyArmorMgr.Get():GetRow(ViewItemData.ArmorItemId)
            ArmorLevel = BodyArmorRow.ArmorLevel
        end
        
        local TextKey_ArmorDetail = "DeathDamageInfoDetail_ArmorInfo" 
        if ViewItemData.ArmorDamage <= 0 then
            TextKey_ArmorDetail = "DeathDamageInfoDetail_ArmorInfoNoDamage"
        end
        if NoArmorButHaveArmorDamage then
            TextKey_ArmorDetail = "DeathDamageInfoDetail_ArmorInfo_NoArmor"
        end

        local ThirdLineFormatText = LocalizeTool.GetCommonText(TextKey_ArmorDetail,ECommonTextType.InGame)
        local ArmorHealthAfterDamage = math.max(0, ViewItemData.ArmorHealth - ViewItemData.ArmorDamage)
        local ThirdLineTxt = StringUtil.PluralTextFormat(ThirdLineFormatText,
        {
            ["ArmorLevel"] = ArmorLevel,
            ["ArmorName"] = ArmorName,
            ["ArmorDamage"] = MathUtil.GetNumPrecisionOne(ViewItemData.ArmorDamage),
            ["ArmorHealth"] = MathUtil.GetNumPrecisionOne(ArmorHealthAfterDamage),
            ["ArmorHealthMax"] = MathUtil.GetNumPrecisionOne(ViewItemData.ArmorHealthMax),
            }
        )
        if NoArmorButHaveArmorDamage then
            ThirdLineTxt = StringUtil.PluralTextFormat(ThirdLineFormatText,
                    {
                        ["ArmorDamage"] = MathUtil.GetNumPrecisionOne(ViewItemData.ArmorDamage),
                        ["ArmorHealth"] = MathUtil.GetNumPrecisionOne(ArmorHealthAfterDamage),
                        ["ArmorHealthMax"] = MathUtil.GetNumPrecisionOne(ViewItemData.ArmorHealthMax),
                    }
            )
        end
        
        self.wtThirdLineTxt:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.wtThirdLineTxt:SetText(ArmorStr..ThirdLineTxt)
    else
        self.wtThirdLineTxt:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function DeathDamageInfoDetailListViewItem_V2:SetPenetrationInfo(ViewItemData,bAttacker)
    print("DeathDamageInfoDetailListViewItem_V2:SetPenetrationInfo")
    if ViewItemData.bIsPenetratingDamage then

        local PenetrationStr = ""
        if ViewItemData.PenetrationLevelDecrease > 0 then
            PenetrationStr = LocalizeTool.GetCommonText("DeathDamageInfoDetail_PenetrationInfo",ECommonTextType.InGame)
        else
            PenetrationStr = LocalizeTool.GetCommonText("DeathDamageInfoDetail_PenetrationInfoNoDecrease",ECommonTextType.InGame)
        end

        local FourthLineTxt = StringUtil.PluralTextFormat(PenetrationStr,
        {
            ["DecreaseLevel"] = ViewItemData.PenetrationLevelDecrease,
        })

        self.wtFourthLineTxt:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.wtFourthLineTxt:SetText(FourthLineTxt)
    else
        self.wtFourthLineTxt:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function DeathDamageInfoDetailListViewItem_V2:SetDamage(ViewItemData)
    --self.wtDamageTxt:SetText("-"..tostring(math.floor(ViewItemData.Damage)))
    if ViewItemData.Damage <= 0 then
        self.wtDamageTxt:SetVisibility(ESlateVisibility.Collapsed)
        --self.wtDamageTxt:SetText("-"..tostring(math.floor(ViewItemData.Damage)))
    else
        self.wtDamageTxt:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.wtDamageTxt:SetText("-"..MathUtil.GetNumPrecisionOne(ViewItemData.Damage))
    end
    
    --if ViewItemData.HelmetArmorDamage <=0 then
    --    self.wtHelmetArmorDamageTxtContainer:SetVisibility(ESlateVisibility.Collapsed)
    --else 
    --    self.wtHelmetArmorDamageTxtContainer:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    --    self.wtHelmetArmorDamageTxt:SetText("-"..tostring(math.ceil(ViewItemData.HelmetArmorDamage)))
    --end
    --if ViewItemData.BodyArmorDamage <=0 then
    --    self.wtBodyArmorDamageTxtContainer:SetVisibility(ESlateVisibility.Collapsed)
    --else
    --    self.wtBodyArmorDamageTxtContainer:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    --    self.wtBodyArmorDamageTxt:SetText("-"..tostring(math.ceil(ViewItemData.BodyArmorDamage)))
    --end

    if ViewItemData.CharacterPart == ECharacterPart.Head and ViewItemData.ArmorDamage > 0 then
        self.wtHelmetArmorDamageTxtContainer:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        local Damage = ViewItemData.HelmetArmorDamage
        if Damage < 1e-4 then
            Damage = ViewItemData.ArmorDamage
        end
        self.wtHelmetArmorDamageTxt:SetText("-"..MathUtil.GetNumPrecisionOne(Damage))
    else
        self.wtHelmetArmorDamageTxtContainer:SetVisibility(ESlateVisibility.Collapsed)
    end
    if ViewItemData.CharacterPart ~= ECharacterPart.Head and ViewItemData.CharacterPart ~= ECharacterPart.None and ViewItemData.ArmorDamage > 0 then
        self.wtBodyArmorDamageTxtContainer:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        local Damage = ViewItemData.BodyArmorDamage
        if Damage < 1e-4 then
            Damage = ViewItemData.ArmorDamage
        end
        self.wtBodyArmorDamageTxt:SetText("-"..MathUtil.GetNumPrecisionOne(Damage))
    else
        self.wtBodyArmorDamageTxtContainer:SetVisibility(ESlateVisibility.Collapsed)
    end
end



function DeathDamageInfoDetailListViewItem_V2:SetDamageBodyPart(ViewItemData)
    for _, wtPartImg in pairs(self.wtPartImgs) do
        wtPartImg:SetVisibility(ESlateVisibility.Collapsed)
    end 

    for _, Part in pairs(ViewItemData.CharacterParts) do
        if self.wtPartImgs[Part] then
            self.wtPartImgs[Part]:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        end
    end 
end


function DeathDamageInfoDetailListViewItem_V2:SetMakerHealth(ViewItemData, bAttacker)
    if bAttacker ~= false then
        self.wtCanvas_Health:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        local HealthAfterDamage = math.max(0, ViewItemData.TakerHealthPercent * ViewItemData.TakerHealthMax - ViewItemData.Damage)
        local PercentAfterDamage = HealthAfterDamage / (ViewItemData.TakerBaseHealthMax + 1e-6)
        self.wtText_HealthValue:SetText(MathUtil.GetNumPrecisionOne(ViewItemData.TakerHealthAfter))
        self.wtRadialImage_HealthBar:SetPercent(PercentAfterDamage);
        
        local HealthBeforeDamage = ViewItemData.TakerHealthPercent * ViewItemData.TakerHealthMax
        local PercentBeforeDamage = HealthBeforeDamage / (ViewItemData.TakerBaseHealthMax + 1e-6)
        self.wtRadialImage_HealthBar_Remnants:SetPercent(PercentBeforeDamage);

        if ViewItemData.TakerHealthMax + 1e-4 < ViewItemData.TakerBaseHealthMax then
            self.wtProgressBar_Stripe:SelfHitTestInvisible()
            self.wtProgressBar_Stripe:SetPercent( (ViewItemData.TakerBaseHealthMax - ViewItemData.TakerHealthMax) / (ViewItemData.TakerBaseHealthMax + 1e-6))
        else
            self.wtProgressBar_Stripe:Collapsed()
        end
    else
        self.wtCanvas_Health:SetVisibility(ESlateVisibility.Collapsed)
    end
end


function DeathDamageInfoDetailListViewItem_V2:_OnClickSelf()
    if self.bCanExpand then
        self.currentShowDetailBox =  not self.currentShowDetailBox
    end
    self:RefreshDetailBox()
end

function DeathDamageInfoDetailListViewItem_V2:RefreshDetailBox()
    if self.currentShowDetailBox then
        self.wtDetailBox:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self.WBP_CommonHoverBg:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        self.wtDetailBox:SetVisibility(ESlateVisibility.Collapsed)
        self.WBP_CommonHoverBg:SetVisibility(ESlateVisibility.Collapsed)
    end
end

return DeathDamageInfoDetailListViewItem_V2