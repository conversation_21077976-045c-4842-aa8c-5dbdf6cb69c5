----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class ActivityMagicTowerDialogueBtn : LuaUIBaseView
local ActivityMagicTowerDialogueBtn = ui("ActivityMagicTowerDialogueBtn")

function ActivityMagicTowerDialogueBtn:Ctor()
    -- 回答文本
    self._wtDialogueTxt = self:Wnd("DFTextBlock_33", UITextBlock)

    -- 条件属性图标
    self._wtAttributeIcon = self:Wnd("DFImage_122", UIImage)

    -- 条件属性数量文本
    self._wtAttributeTxt = self:Wnd("DFTextBlock_68", UITextBlock)

    self._wtAnswerBtn = self:Wnd("DFButton_26", UIButton)
    -- self._wtAnswerBtn:Event("OnClicked", self.OnAnswerBtnClicked, self)
end

---------------Private Function--------------
function ActivityMagicTowerDialogueBtn:_InitItemType()
end

---------------Plublic Function--------------
function ActivityMagicTowerDialogueBtn:SetExpIcon(iconPath)
    self._wtAttributeIcon:AsyncSetImagePath(tostring(iconPath))
end

function ActivityMagicTowerDialogueBtn:SetDialogueTxt(dialogueTxt)
    self._wtDialogueTxt:SetText(tostring(dialogueTxt))
end

function ActivityMagicTowerDialogueBtn:SetAttributeTxt(dialogueTxt)
    self._wtAttributeTxt:SetText(tostring(dialogueTxt))
end

function ActivityMagicTowerDialogueBtn:OnAnswerBtnClicked()
end

return ActivityMagicTowerDialogueBtn