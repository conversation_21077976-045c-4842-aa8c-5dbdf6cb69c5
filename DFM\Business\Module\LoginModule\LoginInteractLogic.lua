----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMLogin)
----- LOG FUNCTION AUTO GENERATE END -----------



local function log(...)
    loginfo("[LoginInteractLogic]",...)
end
local LoginUtil = require("DFM.Business.Module.LoginModule.LoginUtil")
local LoginLogic = require("DFM.Business.Module.LoginModule.LoginLogic")
local LoginConfig = require("DFM.Business.Module.LoginModule.LoginConfig")
local LoginRegisterLogic = require("DFM.Business.Module.LoginModule.LoginRegisterLogic")
local VietnamProtocolLogic  = require("DFM.Business.Module.LoginModule.Logic.VietnamProtocolLogic")
local UGameLogin = import "DFMGameLogin"
local UGameLoginIns = UGameLogin.Get(GetGameInstance())
local LoginInteractLogic = {}
local this = LoginInteractLogic
local WeGameManager = nil
local UHighlightMomentMgr = import "HighlightMomentMgr"
local CustomerServicesEntranceType = import "ECustomerServicesEntranceType"
if PLATFORM_WINDOWS then
    local UWeGameManager = import "WeGameSDKManager"
    WeGameManager = UWeGameManager.Get(GetGameInstance())
end

-- 启动流程打点
local GameLaunchReportTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.GameLaunchReportTool"
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"

-- BEGIN MODIFICATION - VIRTUOS
local RefreshTokenTimerHandle = nil
local RefreshTokenIntervalInSeconds = 0
if IsPS5Family() then
    -- PS5平台token过期时间为1小时，30min刷新一次
    RefreshTokenIntervalInSeconds = 1500
end
-- END MODIFICATION - VIRTUOS

local ELoginReqType = {
    ConnectSuccess = 1,
    RegisterSuccess = 2,
    ReconnectSuccess = 3
}
-- 绑定事件回调
function this.BindCallbacks()
    log("BindCallbacks")

    if UGameLoginIns then
        UGameLoginIns.OnLoginRetDelegate:Clear()
        UGameLoginIns.OnLoginRetDelegate:Add(this._OnGetThirdPartInfoRet)
        UGameLoginIns.OnLoginRetDelegate:Add(this._OnGameCenterAuth)
        UGameLoginIns.OnBaseRetDelegate:Clear()
        UGameLoginIns.OnBaseRetDelegate:Add(this._OnLogoutRet)
        --UGameLoginIns.OnLoginQueryUserInfoRetDelegate:Clear()
        --UGameLoginIns.OnLoginQueryUserInfoRetDelegate:Add(this._OnOnLoginQueryUserInfoRet)
        UGameLoginIns.INTLComplianceResultDelegate:Clear()
        UGameLoginIns.INTLComplianceResultDelegate:Add(this._OnINTLComplianceRet)
    end

    Facade.ProtoManager.Events.evtOnConnectSuccess:AddListener(this._OnConnectSuccess)
    Facade.ProtoManager.Events.evtOnConnectFail:AddListener(this._OnConnectFail)
    Module.Login.Config.Events.evtOnRegisterSuccess:AddListener(this._OnRegisterSuccess)
    Module.Login.Config.Events.evtOnRegisterFail:AddListener(this._OnRegisterFail)
    Facade.ProtoManager.Events.evtOnTmpRelayConnected:AddListener(this._OnReconnectLoginOperate)
    Facade.ProtoManager.Events.evtOnCancelReconnect:AddListener(this._OnCancelReconnect)
    Module.Login.Config.Events.evtOnLoginFailed:AddListener(this._OnLoginFailed)
    Module.Login.Config.Events.evtOnRefreshToken:AddListener(this._OnRefreshToken)
    Facade.ProtoManager:AddNtfListener("CSGatewayKickPlayerNtf", this._OnCSGatewayKickPlayerNtf)
    Server.AccountServer.Events.evtOnGetPunishNtf:AddListener(this._OnGetPunishedNtf)
	Facade.ProtoManager.Events.evtOnNetBarResultNtf:AddListener(this._OnNetBarResultNtf)
    Module.GCloudSDK.Config.Events.evtOnGCloudSDKWebBrowserCallback:AddListener(this._OnGCloudSDKWebBrowserCallback)
    Module.GCloudSDK.Config.Events.evtOnSDKWebBrowserClose:AddListener(this._OnWebViewClose)
    Facade.UIManager.Events.evtOnCultureChanged:AddListener(this._OnCultureChanged)
end
-- 解绑事件回调
function this.UnbindCallbacks()
    log("UnbindCallbacks")
    if UGameLoginIns then
        UGameLoginIns.OnLoginRetDelegate:Clear()
        UGameLoginIns.OnBaseRetDelegate:Clear()
        --UGameLoginIns.OnLoginQueryUserInfoRetDelegate:Clear()
        UGameLoginIns.INTLComplianceResultDelegate:Clear()
    end

    Facade.ProtoManager.Events.evtOnTmpRelayConnected:RemoveListener(this._OnReconnectLoginOperate)
    Facade.ProtoManager.Events.evtOnCancelReconnect:RemoveListener(this._OnCancelReconnect)
    Facade.ProtoManager.Events.evtOnConnectSuccess:RemoveListener(this._OnConnectSuccess)
    Facade.ProtoManager.Events.evtOnConnectFail:RemoveListener(this._OnConnectFail)
    Module.Login.Config.Events.evtOnRegisterSuccess:RemoveListener(this._OnRegisterSuccess)
    Module.Login.Config.Events.evtOnRegisterFail:RemoveListener(this._OnRegisterFail)
    Module.Login.Config.Events.evtOnLoginFailed:RemoveListener(this._OnLoginFailed)
    Facade.ProtoManager:RemoveNtfListener("CSGatewayKickPlayerNtf", this._OnCSGatewayKickPlayerNtf)
    Server.AccountServer.Events.evtOnGetPunishNtf:RemoveListener(this._OnGetPunishedNtf)
	Facade.ProtoManager.Events.evtOnNetBarResultNtf:RemoveListener(this._OnNetBarResultNtf)
    Module.GCloudSDK.Config.Events.evtOnGCloudSDKWebBrowserCallback:RemoveListener(this._OnGCloudSDKWebBrowserCallback)
    Facade.UIManager.Events.evtOnCultureChanged:RemoveListener(this._OnCultureChanged)
    Module.GCloudSDK.Config.Events.evtOnSDKWebBrowserClose:RemoveListener(this._OnWebViewClose)
end

-- 获取三方数据
function this.GetThirdPartInfo(channel, bIsQRCode)
    logerror("[LoginInteractLogic][GetThirdPartInfo] channel, bIsQRCode:", channel, bIsQRCode)
    Module.Login.Config.Events.evtOnLoginStartGetThirdPartInfo:Invoke()
    if bIsQRCode then
        Module.Login:SDKQRCodeLogin(channel)
    else
        Module.Login:SDKLogin(channel)
    end
end

function this._OnGameCenterAuth(ret)
    logerror("[_OnGameCenterAuth]...")
    local tab = LoginUtil.ParseLoginRet(ret)

    if tab.methodID == 170 then
        if tab.retCode == 0 then
            Facade.ConfigManager:SetBoolean("GameCenterAuth", true)
        else
            Facade.ConfigManager:SetBoolean("GameCenterAuth", false)
        end
    end
end

-- 三方数据回调
function this._OnGetThirdPartInfoRet(ret)
    logerror("[OnGetThirdPartInfoRet]...")

    local tab = LoginUtil.ParseLoginRet(ret)
    --- 过滤和鉴权无关的回调
    if (IsBuildRegionGlobal() and tab.methodID ~= 13100 and tab.methodID ~= 13101)
        or (IsBuildRegionGA() and tab.methodID ~= 13100 and tab.methodID ~= 102
        and tab.methodID ~= 13101 and tab.methodID ~= 101) then
        logerror("[OnGetThirdPartInfoRet] filter unnecessary methodId:",tab.methodID)
        return
    end

    if IsHD() and IsWeGameEnabled() and Module.Login:CheckLockNow() then
        logerror("login channel _OnGetThirdPartInfoRet LockNow")
        local tips = Module.Login.Field:GetForBidLoginTips()
        Module.CommonTips:ShowConfirmWindowWithSingleBtn(
            tips,
            SafeCallBack(this._ReturnToLoginScene, nil, false),
            Module.Login.Config.Loc.ExitClient,
            nil,
            true
        )
    end

    if Module.Login.Field:GetRefreshTokenFlag() == true then
        -- 只刷新Token 不走实际登录
        logerror("[OnGetThirdPartInfoRet] refresh pay token now")
        if tab.token and tab.token ~= "" then
            logerror("[OnGetThirdPartInfoRet] set token:", tab.token)
            Server.SDKInfoServer:SetToken(tab.token)
        else
            logerror("[OnGetThirdPartInfoRet] token is empty")
        end
        Server.PayServer:ReqAccountUpdatePayToken()
        Module.Login.Field:SetRefreshTokenFlag(false)
        return
    end

    if IsBuildRegionGA() and PLATFORM_ANDROID then
        Module.Login.Config.Events.evtOnRefreshLoginPanel:Invoke(0)
        if tab.retCode == 9999 and tab.thirdCode == 3007 then
            logerror("DFMGameSDK_UDFMGameLogin_OnINTLAuthResult AccessToken time out")
            Module.Login:Logout()
            return
        end
    end

    if tab.wg_login_info then
        Server.SDKInfoServer:SetWeLoginInfo(tab.wg_login_info)
    else
        logerror("[OnGetThirdPartInfoRet] no welogininfo")
    end
    if tab.retCode == 2 then
        Module.CommonTips:ShowSimpleTip(Module.Login.Config.Loc.CancelAuth)
        Module.Login.Config.Events.evtOnLoginFailed:Invoke()

        -- [aidenliao]启动登录流程上报第一步，取消鉴权
        GameLaunchReportTool.ReportGameLaunchStep(LogAnalysisTool.EGameLaunchOutOfLoginStepName.Login_LoginEnd, tab.openId, false, tab.retCode)
        return
    end

    if tab.channelId == 0 then
        local curFlow = Facade.GameFlowManager:GetCurrentGameFlow()
        if curFlow == EGameFlowStageType.Login then
            Module.CommonTips:ShowSimpleTip(Module.Login.Config.Loc.ThirdPartyLoginFailed)
        end
        
        -- [aidenliao]启动登录流程上报第一步，鉴权失败
        GameLaunchReportTool.ReportGameLaunchStep(LogAnalysisTool.EGameLaunchOutOfLoginStepName.Login_LoginEnd, tab.openId, false, tab.retCode)

        Module.Login.Config.Events.evtOnLoginFailed:Invoke()
        return
    end
    Server.SDKInfoServer:SetLoginRelatedInfo(tab)
    -- BEGIN MODIFICATION @ VIRTUOS: Remove Netbar check on XSX.
    if DFCONSOLE_LUA == 1 then
        --this._LoginReqOperate(ELoginReqType.ConnectSuccess)
        loginfo("_OnGetThirdPartInfoRet: xsx跳过网吧检测")
    else
    	if IsHD()  or _WITH_EDITOR == 1 then
            -- Launcher和编辑器下启动请求网吧特权
            logerror("[OnGetThirdPartInfoRet] request netbar priv now...")
            local UGameNetBar = import "DFMGameNetBar"
            local UGameNetBarIns = UGameNetBar.Get(GetGameInstance())
            local UAppSetting = import "AppSetting"
            local AppSetting = UAppSetting.Get()
            local bIsOversea = IsBuildRegionCN()
            if IsBuildRegionCN() then
            	UGameNetBarIns:ReqNetBarLevel(tab.openId, 10180 ,0,tab.token, false)
            elseif Server.SDKInfoServer:IsRegionKorea() then
            	UGameNetBarIns:ReqNetBarLevel(tab.openId, 20010 ,0,tab.token, true)
            end
    	end
    end
    -- END MODIFICATION

    if DFHD_LUA == 1 then
        Server.SDKInfoServer:SetToken(tab.token)
    end

    --PC精彩时刻
    if IsHD() then
        logerror("[OnGetThirdPartInfoRet] UHighlightMomentMgr.SetupLoginInfo")
        UHighlightMomentMgr.SetupLoginInfo(tab.openId)
    end

    -- BEGIN MODIFICATION @ VIRTUOS: Check Error Code on Console. Open Pop up a dialog box and provide the user with an option to reconnect. 
    if DFCONSOLE_LUA == 1 then
        if (tab.retCode == 4) or (tab.retCode == 9999) or (tab.retCode == 5) then
            -- [aidenliao]启动登录流程上报第一步，主机鉴权失败
            GameLaunchReportTool.ReportGameLaunchStep(LogAnalysisTool.EGameLaunchOutOfLoginStepName.Login_LoginEnd, tab.openId, false, tab.retCode)

            Module.Login.Config.Events.evtOnLoginFailed:Invoke()
            return
        end
        -- 调用登录proto之前需要把第三方的数据设置好，因此要在SetLoginRelatedInfo之后
        this._LoginReqOperate(ELoginReqType.ConnectSuccess)
    end
    -- END MODIFICATION

    if UGameLoginIns then
        --UGameLoginIns:QueryUserInfo()
        -- BEGIN MODIFICATION @ VIRTUOS: Fix recursive calling
        if tab.retCode == 0 then
            if DFCONSOLE_LUA ~= 1 then
                UGameLoginIns:ComplianceQueryUserInfo()
            end
        end
        -- END MODIFICATION
    end

    -- BEGIN MODIFICATION @ VIRTUOS: Check LI pass bind info. 
    if IsPS5Family() or IsXboxSeries() then
        if tab.bindList then
            local JsonFactory = require "DFM.YxFramework.Plugin.Json.Json"
            local Json = JsonFactory.createJson()
            local bindList = Json.decode(tab.bindList)
            if bindList then
                local bindInfoContainsEmail = false
                for _,bindInfo in pairs(bindList) do
                    if bindInfo ~= nil then
                        if bindInfo.channel_info ~= nil and bindInfo.channel_info.email ~= nil then
                        bindInfoContainsEmail = true
                        end
                    end
                end
                if bindInfoContainsEmail then 
                    Module.Login.Field:SetHasBoundLiPass(true)
                end
            end
        end
        -- PS5平台token有效期为一个小时，需要定时刷新避免token过期引起INTL的Web接口调用失败
        if IsPS5Family() and RefreshTokenTimerHandle == nil then
            RefreshTokenTimerHandle = Timer:NewIns(RefreshTokenIntervalInSeconds, 0)
            RefreshTokenTimerHandle:AddListener(function()
                loginfo("Refresh PS5 token")
                local permissions = "psn:s2s openid id_token:psn.basic_claims"
                UE.LevelInfiniteAPI.LoginChannelWithLIPass(EChannelType.kChannelPS5, permissions, "{}")
                Module.Login.Field:SetRefreshTokenFlag(true)
            end)
            RefreshTokenTimerHandle:Start()
        end
    end

    -- Reset focus and navigation
    if DFCONSOLE_LUA == 1 then
        local UGPUINavigationManager = import("GPUINavigationManager")
        local UDFNavigationSelectorBase = import("DFNavigationSelectorBase")

        UGPUINavigationManager.Get(GetGameInstance()):RegisterAnalogCursor()
        UDFNavigationSelectorBase.SetHighlight_DirectlyOrNavigation(false)
    end
    -- END MODIFICATION

    local firstLogin = tab.firstLogin
    local realNameAuth = tab.realNameAuth
    local openId = tab.openId

    logerror("[OnGetThirdPartInfoRet] realNameAuth = {1}, firstLogin = {2}, openId = {3}, retCode = {4} channelId = {5}", realNameAuth, firstLogin, openId, tab.retCode, tab.channelId)

    if PLATFORM_WINDOWS and (IsBuildRegionGlobal() or IsBuildRegionGA()) and IsWeGameEnabled() then
        realNameAuth = false
    end
        
    if PLATFORM_WINDOWS then
        -- PC保留原来的判断
        if not realNameAuth and openId and openId ~= "" then
            logerror("[OnGetThirdPartInfoRet] ConnectMSDK...")
            this._ConnectMSDK()
            return
        end
    else
        if tab.retCode == 0 and openId and openId ~= "" then
            logerror("[OnGetThirdPartInfoRet] ConnectMSDK...")
            this._ConnectMSDK()
            return
        end
    end
    
    if not firstLogin then
        Module.CommonTips:ShowSimpleTip(Module.Login.Config.Loc.FailGetThirdPartAccountInfoTip)
    end
    Module.Login.Config.Events.evtOnLoginFailed:Invoke()
end

--function this._OnOnLoginQueryUserInfoRet(ret)
--    logerror("[OnOnLoginQueryUserInfoRet]...")
--    local tab = LoginUtil.ParseLoginRet(ret)
--    Server.SDKInfoServer:ParseExtraJson(tab["extraJson"])
--end

function this._OnINTLComplianceRet(ret)
    --ret is FINTLComplianceResult,get more struct info by link:https://docs.playernetwork.intlgame.com/docs/zh/API/UE-sdk/Compliance/ComplianceResult
    logerror("[_OnINTLComplianceRet]...")
    if ret then
        if ret.region then
            Server.SDKInfoServer:SetRegionCode(ret.region)
        end


        Server.SDKInfoServer:SetINTLComplianceInfo(ret)
    end
end

-- KickReasonNeedRelogin   = 1;    // 需要重新登陆
-- KickReasonRepeatLogin   = 2;    // 重复登陆
-- KickReasonNeedLogin     = 3;    // 需要先登陆
-- KickReasonZK            = 4;    // 中控踢人
-- KickReasonDisableGateway   = 5; // 停止gateway
-- KickReasonAccountLimit  = 6;    // 封号时踢人
-- KickReasonAccountDeregister  = 7;    // 注销账号时踢人
---@param ntf pb_CSGatewayKickPlayerNtf
function this._OnCSGatewayKickPlayerNtf(ntf)
    logerror("this:_OnCSGatewayKickPlayerNtf",ntf.reason)
    Module.Login.Config.Events.evtOnGatewayKickPlayer:Invoke()
    Module.Login.Field:SetIsKickOut(true)
    if ntf.reason == 2 or ntf.reason == 4 or ntf.reason == 6 or ntf.reason == 7 then
        Facade.ProtoManager:SetEnableReconnect(false)
        Module.Login.Config.Events.evtOnLoginFailed:Invoke()
        if Server.AccountServer:IsPlayerInGame() == true then
            -- 局内被踢 需要网络断开并且不重连
            local reconnectSubSystem = UE.SeamlessReconnectSubSystem.Get(GetWorld())
            if reconnectSubSystem then
                reconnectSubSystem:StopListenNetDisconnect()
                reconnectSubSystem:NetDisconnectTest()
            end
        end
    end

    local showTxt = ""
    loginfo(ntf.custom_reason, type(ntf.custom_reason), tostring(ntf.custom_reason == ""))
    if ntf.custom_reason and not (ntf.custom_reason == "") then
        showTxt = ntf.custom_reason
        if ntf.reason == 6 then
            showTxt = showTxt .. "\n".. "UID:"..Module.RoleInfo:GOpenIdEncryption(Server.AccountServer:GetPlayerIdStr())
        end
    elseif ntf.cross_platform == true then
        if Server.AccountServer:IsPlayerInGame() then
            showTxt = Module.Login.Config.Loc.LoginCrossPlatform
        else
            showTxt = Module.Login.Config.Loc.LoginCrossPlatformLobby
        end
    else
        local kickPlayerReasonData = Facade.TableManager:GetTable("KickPlayerReason")
        ensure(kickPlayerReasonData, "Invalid KickPlayerReason Table")

        loginfo("reason = ", tostring(ntf.reason))
        local findedMsgData = table.find(kickPlayerReasonData, function(v, k) return k == tostring(ntf.reason) end)
        if findedMsgData then
            if ntf.reason == 6 then
                showTxt = findedMsgData.Message .. "\n".. "UID:"..Module.RoleInfo:GOpenIdEncryption(Server.AccountServer:GetPlayerIdStr())
            else
                showTxt = findedMsgData.Message
            end
            loginfo("Message = ", findedMsgData.Message)
        else
            logerror("Get Invalid Data! reason = ", tostring(ntf.reason))
            showTxt = string.format(Module.Login.Config.Loc.UnConfigText, ntf.reason)
        end
    end

    if ntf and ntf.reason == 4 and IsHD() then
        logerror("this:_OnCSGatewayKickPlayerNtf Kill ue5 now")
        Module.LobbyBHD:KillUE5(EDFKillBHDReason.GateKickPlayer)
    end

    Facade.ProtoManager:TryDisConnectServer(true)
    Facade.ProtoManager:StopHeartbeatTimer()

    local confirmText =  Module.Login.Config.Loc.BackToLogin
    if IsHD() and IsWeGameEnabled() then
        confirmText = Module.Login.Config.Loc.ExitClient
    end

    if ntf.reason == 6 and (IsBuildRegionCN() or IsBuildRegionGlobal()) then
        Module.Login:SaveAccountLimitStatus(true, showTxt)
        local customHelp = Module.Login.Config.Loc.CustomerHelp
        Module.GCloudSDK:ShowCommonTip(showTxt, confirmText, customHelp, false, SafeCallBack(this._ReturnToLoginScene, nil, false), SafeCallBack(this._OpenCustomerService, nil, false), nil, true)
    else
        Module.GCloudSDK:ShowCommonTip(showTxt, confirmText, nil, true, SafeCallBack(this._ReturnToLoginScene, nil, false), nil, true)
    end 

end

---玩家处罚弹窗
---@param msg string 解析后的处罚信息，异常情况下为空
---@param ntf pb_CSPunishNtf
function this._OnGetPunishedNtf(msg, ntf)
    logwarning("[PunishNtf]", "当前账号触发处罚", tostring(ntf.reason), ntf.custom_reason)
    if not msg then
        Module.CommonTips:ShowSimpleTip(Module.Login.Config.Loc.PunishDataInvalid)
        return
    end

    Module.CommonTips:ShowConfirmWindowWithSingleBtn(
        msg,
        nil,
        Module.Login.Config.Loc.ConfirmBtnText
    )
end

function this._OnNetBarResultNtf(bIsNetBarMachine ,tokenBuff, tokenLen, ip, macSize, macs)
    logerror("[NetBarNtf] bIsNetbarMachine:{0}  token:{1}  ip = {2}",bIsNetBarMachine, tokenBuff, ip)
    Server.SDKInfoServer:SetNetBarInfo(bIsNetBarMachine ,tokenBuff, tokenLen, ip, macSize, macs)
    -- 获取到网吧特权结果 并且 ConnectSuccess以后才开始登录
    if (IsHD() or _WITH_EDITOR == 1) and Server.SDKInfoServer:GetConnectedIP() ~= "" then
        logerror("[NetBarNtf] request login")
        this._LoginReqOperate(ELoginReqType.ConnectSuccess)
    else
        logerror("[NetBarNtf] check connectIp:", Server.SDKInfoServer:GetConnectedIP())
    end
end


this._ReturnToLoginScene = function(ret)
    --ret为true表示为注销/删除账号流程
    -- 返回登录场景

    -- 当为PC且有WeGame启动器的时候，直接退出游戏
    if IsHD() and IsWeGameEnabled() then
        loginfo("[ReturnLogin] PC 返回登录转为退出游戏")
        -- PC直接退出游戏，这里直接退就好了，不用处理加载失败的过程，那边单独处理
        if ret == true and WeGameManager ~= nil then
            WeGameManager:NotifyLauncherExit()
        end
        local UKismetSystemLibrary = import "KismetSystemLibrary"
        local EQuitPreference = import "EQuitPreference"
        UKismetSystemLibrary.QuitGame(GetGameInstance(), nil, EQuitPreference.Quit, false)
        return
    end

    if PLATFORM_OPENHARMONY then
        Module.LitePackage:ReleasePufferDownloader()
        loginfo("[ReturnLogin] Module.LitePackage:ReleasePufferDownloader()")
    end

    Module.Login:BackToLogin()
end


this._OpenCustomerService = function(ret)
    logerror("[OpenCustomerService] ret:", ret)
    if IsBuildRegionCN() then
        if IsHD() and IsWeGameEnabled() then
            Module.CustomerServices:OpenEntrance(CustomerServicesEntranceType.SafePage_PC)
        else
            Module.CustomerServices:OpenEntrance(CustomerServicesEntranceType.SafePage_Mobile)
        end
    elseif IsBuildRegionGlobal() then
        logerror("[OpenCustomerService] IsBuildRegionGlobal:")
        if IsHD() and IsWeGameEnabled() then
            Module.CustomerServices:OpenEntrance(CustomerServicesEntranceType.SafePage_Global_PC)
        else
            logerror("[OpenCustomerService] IsBuildRegionGlobal Mobile")
            Module.CustomerServices:OpenEntrance(CustomerServicesEntranceType.SafePage_Global_Mobile)
        end
    end
end

function this._OnLogoutRet(ret)
    logerror("OnLogoutRet")
    logtable(ret,true)
    local tab = LoginUtil.ParseBaseRet(ret)
    if tab and tab["extraJson"]  then
        local extraJson = string.gsub(tab["extraJson"], "\\(.)", "%1")
        if string.find(extraJson, "skipUpdate") then
            logerror("OnLogoutRet Luanch form dflauncher, ignore")
            return
        else
            Server.SDKInfoServer:SetLaunchForm(extraJson)
            logerror("OnLogoutRet Luanch do not form dflauncher")
        end
    end

    Module.Login.Field:SetLogoutRet(true)
    Server.SDKInfoServer:SetConnectInfo(nil)
    local gameflow = Facade.GameFlowManager:GetCurrentGameFlow()
    if tab["retCode"] == 1012 then
        -- LOGIN_NEED_LOGIN
        Server.SDKInfoServer:SetLastConnectChannelID(0)
        Server.SDKInfoServer:SetLastSubChannelID(0)
        if gameflow == EGameFlowStageType.Login then
            -- DirectLogin
            local channelId = Server.SDKInfoServer:GetLaunchChannelId()
            logerror("OnLogoutRet 1012 channelId = ",channelId)
            this.GetThirdPartInfo(channelId)
        else
           -- Back to login then DirectLogin
           Module.Login.Field:SetDirectLogin(true) 
           Module.Login:BackToLogin() 
           logerror("OnLogoutRet 1012 not login scene do not deal")
        end
    elseif tab["retCode"] == 1013 then
        -- LOGIN_NEED_SELECT_ACCOUNT
        Server.SDKInfoServer:SetLastConnectChannelID(0)
        Server.SDKInfoServer:SetLastSubChannelID(0)
        if gameflow == EGameFlowStageType.Login then
            -- SwitchUser
            logerror("OnLogoutRet 1013 login scene")
            this.HandleDiffAccount()
        else
            -- Back to login then SwitchUser
            logerror("OnLogoutRet 1013 not login scene")
            Module.Login:BackToLogin()
        end
    elseif tab["retCode"] == 0 then
        local channelId = Server.SDKInfoServer:GetLaunchChannelId()
        local lastId = Server.SDKInfoServer:GetLastConnectChannelID()
        logerror("OnLogoutRet 0 lastId and channelId is ", lastId, channelId)
        logerror("CurGameFlow is ", gameflow)
        if gameflow == EGameFlowStageType.Login then
            if channelId == 0 and lastId ~= 0 then
                -- 非游戏中心启动
                Module.Login:AutoLogin(lastId)
            else
                if lastId == channelId and lastId ~= 0 then
                    Module.Login:AutoLogin(lastId)
                else
                    this.GetThirdPartInfo(channelId)
                end
            end
        else
            if channelId ~= 0 then
                Server.AccountServer:ReportAccountLaunchId(channelId)
            end
        end
    end
end

function this.HandleDiffAccount()
    log("HandleDiffAccount")
    if UGameLoginIns then
        local bNeedHandleDiffAccount = UGameLoginIns:GetDiffAccountFlag()
        if bNeedHandleDiffAccount then
            Module.Login:SwitchUser(true)
            UGameLoginIns:SetDiffAccountFlag(false)
        end
    end
end

function this._DirectLoginFlow(openId)
    -- BEGIN MODIFICATION @ VIRTUOS: Remove Netbar check on XSX.
    if DFCONSOLE_LUA == 1 then
        this._LoginReqOperate(ELoginReqType.ConnectSuccess)
    else
	if IsHD() or _WITH_EDITOR == 1 then
            -- 支持编辑器下也可检测网吧特权。
            logerror("[NetBar] 已获得第三方Token, 检测网吧特权信息")
            local UGameNetBar = import "DFMGameNetBar"
            local UGameNetBarIns = UGameNetBar.Get(GetGameInstance())
            local UAppSetting = import "AppSetting"
            local AppSetting = UAppSetting.Get()
            local bIsOversea = IsBuildRegionCN()
            if IsBuildRegionCN() then
            	UGameNetBarIns:ReqNetBarLevel(0, 10180 , 0, 0, false)
            elseif Server.SDKInfoServer:IsRegionKorea() then
            	UGameNetBarIns:ReqNetBarLevel(0, 20010 , 0, 0, true)
            end
        end
    end
    -- END MODIFICATION

    Facade.ProtoManager:BindLoginConnectDelegate()
    this._SaveLoginInfo(openId,openId,nil,0)
    if openId and openId ~= "" then
        -- Make Connect
        this._ConnectMSDK()
        return
    end
    -- back To Login Page
end

function this._SaveLoginInfo(openId,userName,headIconUrl,channelId)
    log("error check this._SaveLoginInfo",openId,type(openId))
    Server.SDKInfoServer:SetOpenId(openId)
    Server.SDKInfoServer:SetUserName(userName)
    Server.SDKInfoServer:SetPictureUrl(headIconUrl)
    Server.AccountServer:SetPlayerId(openId)
    Server.SDKInfoServer:SetChannel(channelId)
end

-- 构造建立连接数据
function this._GetConnectMSDKInfo()
    return Server.SDKInfoServer:MakeConnectInfo()
end

-- 建立Connector
function this._ConnectMSDK()
    Module.Login.Config.Events.evtOnLoginStartConnect:Invoke()
    if Facade.ProtoManager:GetDNSResolvedAsync() == false then
        -- 使用异步
        logerror("[ConnectMSDK] async login")
        Server.SDKInfoServer:MakeConnectInfoAsync()
    else
        logerror("[ConnectMSDK] sync login")
        local connectMSDKInfo = this._GetConnectMSDKInfo()
        Facade.ProtoManager:TryConnectServer(connectMSDKInfo)
    end
end

-- 建立Connector成功回调
function this._OnConnectSuccess(connectInfo)
    Server.SDKInfoServer:SetConnectInfo(connectInfo)
    logerror("[OnConnectSuccess] ...")
    if connectInfo then
        logerror("[OnConnectSuccess] connecturl:", connectInfo.Url)
    end
    Server.SDKInfoServer:SetLastConnectChannelID(Server.SDKInfoServer:GetChannel())
    Server.SDKInfoServer:SetLastSubChannelID(Server.SDKInfoServer:GetLoginSubChannelID())
    if (IsHD() or _WITH_EDITOR == 1) and Server.SDKInfoServer:GetNetBarInfo() == nil then
        logerror("[OnConnectSuccess] wait netbar result...")
        return;
    end
    this._LoginReqOperate(ELoginReqType.ConnectSuccess)

    -- BEGIN MODIFICATION @ VIRTUOS: Popup reconnect window if connection failed.
    if DFCONSOLE_LUA == 1 then
        Module.Login.Field:SetConnectSucceed(true)
    end
    -- END MODIFICATION
end

function this._OnConnectFail(connectInfo)
    local errorCode = connectInfo.ErrorCode
    logerror("OnConnected Failed errorCode: ", errorCode)

    -- [aidenliao]启动登录流程上报第二步，连接后台服务器失败
    local openid = Server.SDKInfoServer:GetOpenIdStr()
    GameLaunchReportTool.ReportGameLaunchStep(LogAnalysisTool.EGameLaunchOutOfLoginStepName.Login_LoginEnd, openid, false, errorCode)

    Module.Login.Config.Events.evtOnLoginFailed:Invoke()
    Facade.ProtoManager:GetErrHandler():HandleMSDKError(errorCode)

    -- BEGIN MODIFICATION @ VIRTUOS: Popup reconnect window if connection failed.
    if DFCONSOLE_LUA == 1 then
        Module.Login.Field:SetConnectSucceed(false)
    end
    -- END MODIFICATION
end

-- 建立连接后的发送登录协议
function this._LoginReqOperate(loginType)
    if loginType == ELoginReqType.ConnectSuccess then
        Module.Login.Config.Events.evtOnLoginStartLoginReq:Invoke()
    elseif loginType == ELoginReqType.RegisterSuccess then
        Module.Login.Config.Events.evtOnLoginStartLoginReq:Invoke() -- 注册成功后发送的登录，暂时用这个
    end

    Facade.ProtoManager:SetReconnectState(false)
    Facade.ProtoManager:SetQuickReconnectState(false)
    Server.AccountServer:AccountLoginReq(this._OnLoginReqRet) -- 需要根据登录成功场景做不同回调
    local fOnGetStateInfo=function()
        if not Server.AccountServer:IsPlayerInMatching() and Server.MatchServer:GetIsMatching() then
            Server.MatchServer:EndMatching()
        end
    end
    Server.AccountServer:GetStateInfo(fOnGetStateInfo)
end

-- 断线重连回来建立连接成功后发送登录协议
function this._OnReconnectLoginOperate(connectInfo)
    if connectInfo then
        logerror("[OnReconnectLoginOperate] connecturl:", connectInfo.Url)
        Server.SDKInfoServer:SetConnectInfo(connectInfo)
    end
    Facade.ProtoManager:SetReconnectState(true)
    Facade.ProtoManager:SetQuickReconnectState(false)
    --Facade.ProtoManager:StartHeartbeatTimer()
    Server.AccountServer:AccountLoginReq(this._FetchNeccesaryData) -- 需要根据登录成功场景做不同回调
end

-- 断线重连回来等登录后获取必要数据
this._FetchNeccesaryData = function(res)
    log("获取断线后所有数据",res.result)
    -- set protoconnection State
    -- every time received proto then check cachedtable
    -- when cachedtable over then invoke
    -- DoFetchServerBusiness()
    Facade.ProtoManager:SetProtoConnectionState(true) -- TODO： 为啥之前想要这时候不能发其他协议呢？
    if res.result == 0 then
        --重连以后发登录请求的res如果为0则说明登录成功
        -- Facade.ProtoManager:SetProtoConnectionState(true) -- TODO： 为啥之前想要这时候不能发其他协议呢？
        --this._OnLoginReqRet(res)
        Facade.ProtoManager:GetOnConfigData(2)
        local curFlow = Facade.GameFlowManager:GetCurrentGameFlow()
        if curFlow == EGameFlowStageType.Login then
            this._OnLoginReqRet(res)
        end
    elseif res.result and res.result == Err.AccountPlayerNotRegistered then
        local LimitTimeBeginTimeStamp = res.register_unlimited_start_time
        if LimitTimeBeginTimeStamp and LimitTimeBeginTimeStamp > 0 then
            local LimitTimeEndTimeStamp = res.register_unlimited_end_time
            local showText = LoginConfig.GetRegisterErrNoOpeningTimeTips(LimitTimeBeginTimeStamp, LimitTimeEndTimeStamp)
            this._ShowLimitConfirmWindow(showText)
        else
            LoginRegisterLogic._StartCreatePlayer()
        end
    elseif res.result and res.result == Err.AccountNotInWhiteList then
        local errorCode = string.format(Module.Login.Config.Loc.LoginFailedMobile, Module.Login.Config.Loc.AccountNotInWhiteList)
        if IsHD() and IsWeGameEnabled() then
            errorCode = string.format(Module.Login.Config.Loc.LoginFailed, Module.Login.Config.Loc.AccountNotInWhiteList)
        end
        if (IsBuildRegionGlobal() or IsBuildRegionGA()) and not VersionUtil.IsShipping() then
            logerror("_OnLoginReqRet copy openId:", Server.SDKInfoServer:GetOpenIdStr())
            ULuautils.ClipboardCopy(Server.SDKInfoServer:GetOpenIdStr())
            errorCode = string.format(Module.Login.Config.Loc.LoginFailed, Module.Login.Config.Loc.AccountNotInWhiteListGloablDev)
        end
        this._ShowLimitConfirmWindow(errorCode)
        Module.Login.Config.Events.evtOnLoginFailed:Invoke()
    elseif res.result and res.result == Err.AccountKickConnInRoom then
        -- 其他平台在线中，展示弹窗。
        logerror("_FetchNeccesaryData res.result = Err.AccountKickConnInRoom ")
        this._ShowLoginConfirmWindow()
        Module.Login.Config.Events.evtOnLoginFailed:Invoke()
    elseif res.result and res.result == Err.AccountNeedUpdateClientVersion then
        local errorCode = Module.Login.Config.Loc.AccountNeedUpdateClientVersion
        this._ShowLimitConfirmWindow(errorCode,false)
        Module.Login.Config.Events.evtOnLoginFailed:Invoke()
    elseif res.result and res.result == Err.AccountForbiddenLowVersionLogin then
        local errorCode = Module.Login.Config.Loc.AccountForbiddenLowVersionLogin
        this._ShowLimitConfirmWindow(errorCode,true)
        Module.Login.Config.Events.evtOnLoginFailed:Invoke()
    elseif res.result and res.result == Err.AccountVerWhiteListFailed then
        local errorCode = Module.Login.Config.Loc.AccountVerWhiteListFailed
        this._ShowLimitConfirmWindow(errorCode,false)
        Module.Login.Config.Events.evtOnLoginFailed:Invoke()
    elseif res.result and res.result == Err.AccountLimitLogin then
        this._OnAccountLimitLogin(res)
        Module.Login.Config.Events.evtOnLoginFailed:Invoke()
    elseif res.result and res.result == Err.AccountCnSteamForbidOverseaUser then
        local errorCode = Module.Login.Config.Loc.AccountCnSteamForbidOverseaUser
        this._ShowLimitConfirmWindow(errorCode,false)
        Module.Login.Config.Events.evtOnLoginFailed:Invoke()
    elseif res.result and res.result == Err.AccountInvalidClientVersion then
        local errorCode = Module.Login.Config.Loc.AccountInvalidClientVersion
        this._ShowLimitConfirmWindow(errorCode,false)
        Module.Login.Config.Events.evtOnLoginFailed:Invoke()
    elseif res.result and res.result == Err.AccountMsdkCheckDenyLogoin then
        if res.msdk_check_login_result and res.msdk_check_login_result.errcode == 8246100 then
            local errorCode = Module.Login.Config.Loc.WeGameMSDKError
            this._ShowLimitConfirmWindow(errorCode,false)
        else
            if res.msdk_check_login_result.err_message then
                this._ShowLimitConfirmWindow(res.msdk_check_login_result.err_message,false)
            end
        end
    elseif res.result and res.result == Err.AccountLoginCountryLimit then
        if IsBuildRegionGA() or IsBuildRegionGlobal() then
            local confirmDesc = Module.Login.Config.Loc.OpenUrl
            local errorCode = Module.CommonTips.Field:GetErrCode2String(res.result)
            local callback = function()
                if IsHD() and IsWeGameEnabled() then
                    Module.GCloudSDK:LaunchURL(Module.Login.Config.CountryLimitUrl)
                else
                    Module.GCloudSDK:OpenUrl(Module.Login.Config.CountryLimitUrl, 3, false, false, "", true)
                end
            end
            this._ShowLimitConfirmWindow(errorCode,true,callback,confirmDesc)
            Module.Login.Config.Events.evtOnLoginFailed:Invoke()
        else
            Module.Login.Config.Events.evtOnLoginFailed:Invoke() 
        end
    else
        Module.Login.Config.Events.evtOnLoginFailed:Invoke()
    end
    -- set timer for in case of disturbed
    -- DelayCall(15,RelayConnectedFail)
end

-- 当放弃断线重连时候返回登录
function LoginInteractLogic:_OnCancelReconnect()
    Module.Login:BackToLogin()
end

function this._ShowLoginConfirmWindow()
    local fOnCancelLogin = function()
        Facade.ProtoManager:TryDisConnectServer(true)
        loginfo("cancel login")
        if IsHD() and IsWeGameEnabled() then
            local UKismetSystemLibrary = import "KismetSystemLibrary"
            local EQuitPreference = import "EQuitPreference"
            UKismetSystemLibrary.QuitGame(GetGameInstance(), nil, EQuitPreference.Quit, false)
            return
        else
            -- 要考虑不是在登录界面触发的情况，点击放弃应该回到登录界面
            local gameflow = Facade.GameFlowManager:GetCurrentGameFlow()
            if gameflow ~= EGameFlowStageType.Login then
                Module.Login:BackToLogin()
            end
        end
    end
    
    local fOnConfirmLogin = function()
        loginfo("confirm login")
        local extParam = {force_kick_old_conn = true,bEnableHighFrequency = true}
        Server.AccountServer:AccountLoginReq(this._OnLoginReqRet,extParam)
        Module.Login.Config.Events.evtOnLoginStartConnect:Invoke()
    end

    local gameflow = Facade.GameFlowManager:GetCurrentGameFlow()
    if gameflow == EGameFlowStageType.Login then
        Module.CommonTips:ShowConfirmWindow(
        Module.Login.Config.Loc.LoginConfirmContent,
        fOnConfirmLogin,
        fOnCancelLogin,
        Module.Login.Config.Loc.CancelLogin,
        Module.Login.Config.Loc.LoginAndNotReconnect
        )
    else
        -- 不在登录界面的重连
        Module.CommonTips:ShowConfirmWindow(
        Module.Login.Config.Loc.ReconnectConfirmContent,
        fOnConfirmLogin,
        fOnCancelLogin,
        Module.Login.Config.Loc.CancelReconnect,
        Module.Login.Config.Loc.ConfirmReconnect
        )
    end
end

function this._ShowLimitConfirmWindow(showText, needExit, callback, comfirmDesc)
    logerror("LoginInteractLogic _ShowLimitConfirmWindow hotfix")
    Facade.UIManager:CloseAllPopUI()
    local fOnLoginFailed = function()
        if callback then
            callback()
        end
        if (IsHD() and IsWeGameEnabled()) or needExit then
            local UKismetSystemLibrary = import "KismetSystemLibrary"
            local EQuitPreference = import "EQuitPreference"
            UKismetSystemLibrary.QuitGame(GetGameInstance(), nil, EQuitPreference.Quit, false)
            return
        end
        --Module.Login.Config.Events.evtOnLoginFailed:Invoke()
    end

    local confirmText = comfirmDesc or Module.Login.Config.Loc.ConfirmBtnText
    if (IsHD() and IsWeGameEnabled()) or needExit then
        confirmText = comfirmDesc or Module.Login.Config.Loc.ExitClient
    end
    Module.CommonTips:ShowConfirmWindowWithSingleBtn(
        showText,
        fOnLoginFailed,
        confirmText
    )
end

-- 登录协议回调 -- 连接成功和注册成功后的回调相同
---@param res pb_CSAccountLoginRes
function this._OnLoginReqRet(res)
    logerror("Account Login Res xxxxx ", res.result)

    -- [aidenliao]启动流程打点上报，登录流程最终步，后台服务器回包
    local openid = Server.SDKInfoServer:GetOpenIdStr()
    if res.result and res.result == 0 then
        -- 登陆成功
        GameLaunchReportTool.ReportGameLaunchStep(LogAnalysisTool.EGameLaunchOutOfLoginStepName.Login_LoginEnd, openid, true, "")
    else
        if res.result then
            GameLaunchReportTool.ReportGameLaunchStep(LogAnalysisTool.EGameLaunchOutOfLoginStepName.Login_LoginEnd, openid, false, res.result)
        else
            -- 网络异常回了个空包，这个时候用特殊的值代表
            GameLaunchReportTool.ReportGameLaunchStep(LogAnalysisTool.EGameLaunchOutOfLoginStepName.Login_LoginEnd, openid, false, "-999")
        end
    end
    if res.result and res.result == 0 then
        local serverKey = Server.SDKInfoServer:GetSelectAddrKey()
        --某些服需要关闭GM 219跑测服
        if CloseModuleType and serverKey == "zone_219" then
            CloseModuleType.bIsCloseGM = true
            loginfo("Account Login CloseModuleType disable GM for server ", serverKey)
        end
        this._LoginSuccess(res)
        Module.Login:ResetLoginFailedInfo()
        Module.Login.Field:SetLoginState(false)
    -- BEGIN MODIFICATION - VIRTUOS
    elseif DFCONSOLE_LUA == 1 and res.result and res.result == -1 then
        local function ConfirmFunc()
            LoginLogic.GameAutoLogin()
        end
        Module.GCloudSDK:ShowCommonTip(
                NSLOCTEXT("LoginModule", "Console_Lua_Proto_ConnectNotLink", "与三角洲行动网络断开，是否尝试再次连接"),
                NSLOCTEXT("LoginModule", "Lua_Proto_ConfirmReconnect", "确认重连"),
                nil, true, ConfirmFunc, nil , true)
    -- END MODIFICATION - VIRTUOS
    elseif res.result and res.result == Err.AccountPlayerNotRegistered then
        local LimitTimeBeginTimeStamp = res.register_unlimited_start_time
        --logtable(res)
        if LimitTimeBeginTimeStamp and LimitTimeBeginTimeStamp > 0 then
            local LimitTimeEndTimeStamp = res.register_unlimited_end_time
            local showText = LoginConfig.GetRegisterErrNoOpeningTimeTips(LimitTimeBeginTimeStamp, LimitTimeEndTimeStamp)
            this._ShowLimitConfirmWindow(showText)
        else
            LoginRegisterLogic._StartCreatePlayer()
        end
    elseif res.result and res.result == Err.AccountLimitLogin then
        this._OnAccountLimitLogin(res)
        Module.Login.Config.Events.evtOnLoginFailed:Invoke()
    elseif res.result and res.result == Err.AccountLoginServiceClosed then
         -- 服务器暂时不允许登录，pc显示退出游戏，移动端显示无法继续
         local errorCode = string.format(Module.Login.Config.Loc.LoginFailedMobile, Module.Login.Config.Loc.AccountLoginServiceClosed)
         if BUILD_REGION_CN_EXPER then
            errorCode = string.format(Module.Login.Config.Loc.LoginFailedMobile, Module.Login.Config.Loc.AccountLoginServiceClosed_Exper)
         end
         if IsHD() and IsWeGameEnabled() then
             errorCode = string.format(Module.Login.Config.Loc.LoginFailed, Module.Login.Config.Loc.AccountLoginServiceClosed)
         end
        this._ShowLimitConfirmWindow(errorCode)
        Module.Login.Config.Events.evtOnLoginFailed:Invoke()
    elseif res.result and res.result == Err.AccountNotInWhiteList then
        -- 不在白名单
        local errorCode = string.format(Module.Login.Config.Loc.LoginFailedMobile, Module.Login.Config.Loc.AccountNotInWhiteList)
        if IsHD() and IsWeGameEnabled() then
            errorCode = string.format(Module.Login.Config.Loc.LoginFailed, Module.Login.Config.Loc.AccountNotInWhiteList)
        end
        if (IsBuildRegionGlobal() or IsBuildRegionGA()) and not VersionUtil.IsShipping() then
            logerror("_OnLoginReqRet copy openId:", Server.SDKInfoServer:GetOpenIdStr())
            ULuautils.ClipboardCopy(Server.SDKInfoServer:GetOpenIdStr())
            errorCode = string.format(Module.Login.Config.Loc.LoginFailed, Module.Login.Config.Loc.AccountNotInWhiteListGloablDev)
        end
        this._ShowLimitConfirmWindow(errorCode)
        Module.Login.Config.Events.evtOnLoginFailed:Invoke()
    elseif res.result and res.result == Err.AccountKickConnInRoom then
        -- 其他平台在线中，展示弹窗。
        this._ShowLoginConfirmWindow()
        Module.Login.Config.Events.evtOnLoginFailed:Invoke()
    elseif res.result and res.result == Err.AccountNeedUpdateClientVersion then
        local errorCode = Module.Login.Config.Loc.AccountNeedUpdateClientVersion
        this._ShowLimitConfirmWindow(errorCode,false)
        Module.Login.Config.Events.evtOnLoginFailed:Invoke()
    elseif res.result and res.result == Err.AccountForbiddenLowVersionLogin then
        local errorCode = Module.Login.Config.Loc.AccountForbiddenLowVersionLogin
        this._ShowLimitConfirmWindow(errorCode,true)
        Module.Login.Config.Events.evtOnLoginFailed:Invoke()
    elseif res.result and res.result == Err.AccountVerWhiteListFailed then
        local errorCode = Module.Login.Config.Loc.AccountVerWhiteListFailed
        this._ShowLimitConfirmWindow(errorCode,false)
        Module.Login.Config.Events.evtOnLoginFailed:Invoke()
    elseif res.result and res.result == Err.AccountCnSteamForbidOverseaUser then
        local errorCode = Module.Login.Config.Loc.AccountCnSteamForbidOverseaUser
        this._ShowLimitConfirmWindow(errorCode,false)
        Module.Login.Config.Events.evtOnLoginFailed:Invoke()
    elseif res.result and res.result == Err.AccountInvalidClientVersion then
        local errorCode = Module.Login.Config.Loc.AccountInvalidClientVersion
        this._ShowLimitConfirmWindow(errorCode,false)
        Module.Login.Config.Events.evtOnLoginFailed:Invoke()
    elseif res.result and res.result == Err.AccountMsdkCheckDenyLogin then
        if res.msdk_check_login_result and res.msdk_check_login_result.errcode == 8246100 then
            local errorCode = Module.Login.Config.Loc.WeGameMSDKError
            this._ShowLimitConfirmWindow(errorCode,false)
        else
            if res.msdk_check_login_result.err_message then
                this._ShowLimitConfirmWindow(res.msdk_check_login_result.err_message,false)
            end
        end
    elseif res.result and res.result == Err.AccountLoginCountryLimit then
        if IsBuildRegionGA() or IsBuildRegionGlobal() then
            local confirmDesc = Module.Login.Config.Loc.OpenUrl
            local errorCode = Module.CommonTips.Field:GetErrCode2String(res.result)
            local callback = function()
                if IsHD() and IsWeGameEnabled() then
                    Module.GCloudSDK:LaunchURL(Module.Login.Config.CountryLimitUrl)
                else
                    Module.GCloudSDK:OpenUrl(Module.Login.Config.CountryLimitUrl, 3, false, false, "", true)
                end
            end
            this._ShowLimitConfirmWindow(errorCode,true, callback, confirmDesc)
            Module.Login.Config.Events.evtOnLoginFailed:Invoke()
        else
            Module.Login.Config.Events.evtOnLoginFailed:Invoke() 
        end
    else
        Module.Login.Config.Events.evtOnLoginFailed:Invoke()
    end
end

function this._OnAccountLimitLogin(res)
    logwarning("当前账号已被封禁！")
    local buildLoginLimitNtfData = {
        ["custom_reason"] = res.custom_login_limit_reason,
        ["reason"] = res.login_limit_reason,
        ["over_time"] = res.login_limit_time,
        ["punish_start_time"] = res.login_limit_start_time
    }

    local limitMsg = Server.AccountServer:SerializePunishNtfMessage(buildLoginLimitNtfData)
    if limitMsg then
        local resultMsg = limitMsg .."\n".. "UID:"..Module.Login:GOpenIdEncryption(Server.AccountServer:GetPlayerIdStr())
        if (IsBuildRegionCN() or IsBuildRegionGlobal()) then
            local confirmText = Module.Login.Config.Loc.BackToLogin
            if IsHD() and IsWeGameEnabled() then
                confirmText = Module.Login.Config.Loc.ExitClient
            end
            Module.Login:SaveAccountLimitStatus(true, resultMsg)
            Module.GCloudSDK:ShowCommonTip(resultMsg, confirmText, Module.Login.Config.Loc.CustomerHelp, false, SafeCallBack(this._ReturnToLoginScene, nil, false), SafeCallBack(this._OpenCustomerService, nil, false), true, true)
        else
            this._ShowLimitConfirmWindow(resultMsg)
        end
        return
    else
        Module.CommonTips:ShowSimpleTip(Module.Login.Config.Loc.AccountLimitDataInvalid)
        Module.Login.Config.Events.evtOnLoginFailed:Invoke()
    end

    local limitMessage = ''
    -- 当有自定义封禁信息的时候直接展示，不需要考虑多语言问题
    if res.custom_login_limit_reason and not (res.custom_login_limit_reason == "") then
        limitMessage = res.custom_login_limit_reason
    elseif res.login_limit_reason and res.login_limit_time then
        -- 若没有自定义信息则展示预设信息
        local accountPunishData = Facade.TableManager:GetTable("AccountPunishReason")
        local findedMsgData = table.find(accountPunishData, function(v, k) return k == tostring(res.login_limit_reason) end)
        if findedMsgData then
            limitMessage = findedMsgData.Message
        else
            limitMessage = string.format(Module.Login.Config.Loc.UnConfigText, res.login_limit_reason)
        end
        loginfo("LimitMessage = ", limitMessage, "limit reason = ", res.login_limit_reason)
    else
        logerror("账号封禁回包异常")
        Module.CommonTips:ShowSimpleTip(Module.Login.Config.Loc.AccountLimitDataInvalid)
        Module.Login.Config.Events.evtOnLoginFailed:Invoke()
        return
    end

    local limitTime = TimeUtil.TransTimestamp2YYMMDDHHMMSSCNStr(res.login_limit_time)
    local param = {
        ["Message"] = limitMessage,
        ["LimitTime"] = limitTime
    }
    local showText = StringUtil.Key2StrFormat(LoginConfig.Loc.AccountLimitText, param)
    this._ShowLimitConfirmWindow(showText)
end

-- 注册成功
function this._OnRegisterSuccess()
    --this._LoginReqOperate(ELoginReqType.RegisterSuccess)
    -- TODO: 需要做成单次上报
    Module.GCloudSDK:LuaReportLoginEvent({Login = "RegisterSuccess"})
    LoginLogic.ReportToWeGame("register_character", "success")
    Module.GCloudSDK:OnRegisterSucceedReport()
end

function this._OnRegisterFail(res)
    Module.GCloudSDK:LuaReportLoginEvent({Login = "RegisterFail"})
    LoginLogic.ReportToWeGame("register_character", "failure")
    local retCode = res and res.result or -1
    if retCode == Err.AccountRegisterTimeLimit then
        local LimitTimeBeginTimeStamp = res.unlimited_start_time
        local LimitTimeEndTimeStamp = res.unlimited_end_time
        local showText = LoginConfig.GetRegisterErrNoOpeningTimeTips(LimitTimeBeginTimeStamp, LimitTimeEndTimeStamp)
        this._ShowLimitConfirmWindow(showText)
        --logtable(res)
    end
end

function this._OnTeamInfoUpdated()
    loginfo("LoginInteractLogic._OnTeamInfoUpdated")
    if Server.TeamServer:IsInTeam() then
        Server.TeamServer:ReqUpdatePackQuest()--登录的时候重新上报一下，防止有退出去删包的操作
        Facade.UIManager:CommitTransition(true)
        Facade.UIManager:SetIsJumpRollbackTransition(true)
        local teamGameMode = Server.TeamServer:GetTeamMatchMode().game_mode
        if teamGameMode == MatchGameMode.TDMGameMode then
            Module.Hall:SetEnabledHallModule(false)
            Module.BattlefieldEntry:SetEnableNewHallModule(true)
            Module.IrisSafeHouse.Config.flowEvtToEnterLobby:Invoke()
        elseif teamGameMode == MatchGameMode.BlackHawkDown then
            Module.IrisSafeHouse.Config.flowEvtToEnterLobbyBHD:Invoke()
        else
            Module.IrisSafeHouse.Config.flowEvtToEnterSafeHouse:Invoke()
        end
    end
    Module.Team:JoinTeamFromMiniProgram()
    Server.TeamServer.Events.evtTeamInfosUpdated:RemoveListener(this._OnTeamInfoUpdated)
end

local DFMGameLoadingManager = import("DFMGameLoadingManager").GetGameLoadingManager(GetGameInstance())

function this._LoginSuccess(res)
    -- TODO: 需要做成单次上报
    -- Module.GCloudSDK:InitWhenLoginSuccess()
    Module.Login.Field:SetCurGuideStage(res.guide_stage_id)
    Module.Login.Field:SetAutoLogin(true)
    Module.Login.Field:SetLogoutRet(false)
    Server.GuideServer:SetStageFromLogin(res.guide_stage_id)
    --Server.SDKInfoServer:SetRegionCode(res.country_belonging)
    if not IsBuildRegionCN() then
        this._SetFirstLoginRegionCode(res)
        this._SetCurrentLoginRegionCode(res)
    end
    Server.SDKInfoServer:SetGVoiceURL(res.gvoice_url)
    Server.AccountServer:SetPlayerRegisterState(res.is_register)
    if IsHD() then
        loginfo("[_LoginSuccess] UHighlightMomentMgr._LoginSuccess")
        local highLightMomentMgr = UHighlightMomentMgr.Get(GetWorld())
        if isvalid(highLightMomentMgr) then
            highLightMomentMgr:SetupLoginSuccess(Server.SDKInfoServer:GetZoneId())
        end
    end

    --- 越南实名认证，在连接服务器前进行
    if Server.SDKInfoServer:IsRegionVietnam() and IsBuildRegionGA() then
        local loginChannel = Server.SDKInfoServer:GetChannel()
        if loginChannel ~= EChannelType.kChannelGuest then
            VietnamProtocolLogic.RequestAgeSettings()
            return
        end
    end

    this.ProcessLoginConnect()
end

function this.ProcessLoginConnect()
    logwarning("[OnLoginSuccess]ProcessLoginConnect()")
    Module.GCloudSDK:LuaReportLoginEvent({Login = "LoginSuccess"})
    Module.GCloudSDK:OnLoginSucceedReport()
    Module.GCloudSDK:LuaReportDeviceFreeSpace()
    LoginLogic.ReportToWeGame("player_login", "success")

    -- 收到登录请求回包，开启心跳
    LoginUtil.InitHearbeatConnect()
    --需要根据当前的情况，选择是否跳过模式大厅
    local curGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    if curGameFlow == EGameFlowStageType.Login then
        if DFMGameLoadingManager then
            DFMGameLoadingManager.OnLoadingViewVisible:Add(this._OnLoadingViewVisible, this)
        end
        local ELoadingDestination = import "ELoadingDestination"
        LuaGlobalEvents.evtStartPreshowLoadingView:Invoke(ELoadingDestination.ELD_MainHall)
    else
        this._OnLoadingViewVisible()
    end
end

function this._OnLoadingViewVisible()
    logwarning("[OnLoginSuccess]Close Launch Bg!")
    Module.GCloudSDK:CloseLoadingBackground() -- 登陆成功的时候关闭加载背景

    if DFMGameLoadingManager then
        DFMGameLoadingManager.OnLoadingViewVisible:Remove(this._OnLoadingViewVisible, this)
    end

    -- 此时Loading界面已经展示了，然后需要开始走主流程相关流转等
    if DFMGameLoadingManager and DFMGameLoadingManager:IsEnablePostProcessLogin2FrontEnd() then
        -- 开关开了，需要等Map加载完再开始业务初始化和业务预热
        DFMGameLoadingManager.OnEnterTargetLevel:Add(this._OnLoginSuccessInvokeBusinessTravel, this)

        -- 触发流程流转
        if DFCONSOLE_LUA == 1 and LoginLogic.IsReturnFromBHD() and Module.Login.Field:GetIsFirstEnterLogin() then
            Module.Login.Field:AlreadyEnteredLogin()
            Module.Login.Config.flowEvtLoginSuccess:Invoke("LobbyBHD")
        else
            Module.Login.Config.flowEvtLoginSuccess:Invoke("ModeHall")
        end
    else
        -- 开关没开，直接走之前的流程
        this._OnLoginSuccessInvokeBusinessTravel()
    end
end

function this._SetFirstLoginRegionCode(res)
    local R13NRegionCode = Facade.TableManager:GetTable("R13NRegionCode")
    local INTLRegion = Server.SDKInfoServer:GetRegionNumericCode()
    if not R13NRegionCode or table.nums(R13NRegionCode) == 0 then
        R13NRegionCode = {}
    end
    logwarning("[OnLoginSuccess] _SetFirstLoginRegionCode INTLRegion ", INTLRegion)
    for _, value in pairs(R13NRegionCode) do
        if INTLRegion == value.RegionCode then
            logwarning("[OnLoginSuccess] _SetFirstLoginRegionCode returning for INTLRegion ", INTLRegion)
            return
        end
    end
    Server.SDKInfoServer:SetFirstLoginRegion(res.country_belonging)
end

function this._SetCurrentLoginRegionCode(res)
    Server.SDKInfoServer:SetCurrentLoginRegion(res.country_code)
end


function this._OnLoginSuccessInvokeBusinessTravel()
    logwarning("LoginInteractLogic._OnLoginSuccessInvokeBusinessTravel")

    local bEnablePostProcessGame2FrontEnd = false
    if isvalid(DFMGameLoadingManager) and DFMGameLoadingManager:IsEnablePostProcessLogin2FrontEnd() then
        -- 移除监听
        bEnablePostProcessGame2FrontEnd = true
        DFMGameLoadingManager.OnEnterTargetLevel:Remove(this._OnLoginSuccessInvokeBusinessTravel, this)
    end

    GFLogin_InitLobbyBusiness() -- 初始化各个业务模块 顺序看需不需要优化
    Module.Login.Config.Events.evtOnLoginSuccess:Invoke()
    Module.Hall:SetEnabledHallModule(false)
    Module.BattlefieldEntry:SetEnableNewHallModule(true)
    Module.NetworkBusiness:InitXunYou()

    if bEnablePostProcessGame2FrontEnd == false then
        if DFCONSOLE_LUA == 1 and LoginLogic.IsReturnFromBHD() and Module.Login.Field:GetIsFirstEnterLogin() then
            Module.Login.Field:AlreadyEnteredLogin()
            Module.Login.Config.flowEvtLoginSuccess:Invoke("LobbyBHD")
        else
            Module.Login.Config.flowEvtLoginSuccess:Invoke("ModeHall")
        end
    end
    
    -- Todo: 同步获取各种数据 --(目前获取数据在GameFlow中)
    FS_ModeHallFetchServerBusiness()

    if bEnablePostProcessGame2FrontEnd == true then
        -- 此时触发流程预加载逻辑
        Facade.GameFlowManager:ProcessLogin2FrontEndManually()
    end
end

function this._OnLoginFailed()
    Module.Login:UpdateLoginFailedInfo()
    Facade.ProtoManager:SetReconnectState(false)
    Facade.ProtoManager:SetQuickReconnectState(false)
    LoginLogic.ReportToWeGame("player_login", "failure")
end

function this._OnRefreshToken()
    logwarning("_OnRefreshToken!")
    Module.Login:RefreshToken()
end

-- DFHD_LUA Start
function this.HopeVerifyCertification(access_token, channelId)
    -- start 微信登录暂时没有接中控，先跳过中控校验；
    if channelId == EChannelType.kChannelWechat then
        this._LoginReqOperate(ELoginReqType.ConnectSuccess) --登录流程
        return
    end
    -- end


    if access_token == nil or access_token == "" then
        this._LoginReqOperate(ELoginReqType.ConnectSuccess) --登录流程
        return
    end

    local function GetSubStringBetween(String, PatternStrA, PatternStrB)
        local a, b = string.find(String, PatternStrA)
        local c, d = string.find(String, PatternStrB)
        local result = ""
        if (b ~= nil and c ~= nil) then
            result = string.sub(String, b + 1, c - 1)
        end
        return result
    end

    -- 拿QQ互联的 Openid
    local url = "https://graph.qq.com/oauth2.0/me"
    local finalURL = url .. "?access_token=" .. access_token
    Facade.ResourceManager:HttpLoadResource(
        nil,
        finalURL,
        function(resList)
            local Content = resList[finalURL]
            local openID = GetSubStringBetween(Content, 'openid":"', '"}')
            loginfo("[TestHopeVerify]OpenID:" .. openID)

            local QQAppClientID
            local UAppSetting = import "AppSetting"
            local AppSetting = UAppSetting.Get()
            if AppSetting then
                QQAppClientID = UAppSetting.Get().QQAppId
            end

            local req = pb.CSHopeVerifyCertificationReq:New()
            req.client_info = LogAnalysisTool.GetClientInfoLog()
            --req.client_info.client_ip = "*************:0"

            req.auth_info = pb.AuthInfo:New()
            req.auth_info.auth_user_type = 2
            req.auth_info.auth_appid = QQAppClientID
            req.auth_info.auth_user_id = openID
            req.auth_info.auth_key = access_token

            loginfo(
                string.format(
                    "[TestHopeVerify][req.auth_info],auth_appid:%s,auth_user_type:%d,auth_user_id:%s,auth_key:%s",
                    req.auth_info.auth_appid,
                    req.auth_info.auth_user_type,
                    req.auth_info.auth_user_id,
                    req.auth_info.auth_key
                )
            )

            local fOnHopeVerifyCertificationRes = function(res)
                loginfo(string.format("[TestHopeVerify]fOnHopeVerifyCertificationRes result:%d", res.result))
                -- logerror(string.format("[TestHopeVerify]fOnHopeVerifyCertificationRes trace_id:%s", res.trace_id))
                if res.instructions ~= nil and #res.instructions ~= 0 then
                    for _, instruction in pairs(res.instructions) do
                        if instruction == nil then
                            return
                        end
                        if instruction.type == 0 then
                            loginfo("[TestHopeVerify]instruction.type == 0, go to login ")
                            this._LoginReqOperate(ELoginReqType.ConnectSuccess) --登录流程
                            return
                        elseif instruction.type == 1 then
                            loginfo("[TestHopeVerify]instruction.type == 1, go to login")
                            this._LoginReqOperate(ELoginReqType.ConnectSuccess) --登录流程
                            return;
                        elseif instruction.type == 2 then
                            -- Module.CommonTips:CreateCommonPanelUI(
                            --     instruction.msg,
                            --      instruction.type,
                            --     instruction.title,
                            --     nil
                            --     )
                            loginfo("[TestHopeVerify]instruction.type == 2 , showPop, stop login")
                            Facade.ProtoManager:TryDisConnectServer()
                            Module.Login.Config.Events.evtOnLoginFailed:Invoke()
                            Module.GCloudSDK:CreateHopeMainPanel(instruction.msg, instruction.title)
                        elseif instruction.type == 3 then
                            loginfo("[TestHopeVerify]instruction.type == 3, stop login")

                            Facade.ProtoManager:TryDisConnectServer()
                            Module.Login.Config.Events.evtOnLoginFailed:Invoke()
                            LoginConfig.Events.evtShowUrl:Invoke(instruction.url, true)
                        end
                    end
                else
                    loginfo("[TestHopeVerify]res.instructions == nil and #res.instructions == 0, account may not in hope server whitelist, Go to login")
                    this._LoginReqOperate(ELoginReqType.ConnectSuccess)
                 --登录流程
                end
            end
            loginfo("req.Request(fOnHopeVerifyCertificationRes);")
            req:Request(fOnHopeVerifyCertificationRes)
        end,
        nil,
        nil,
        EHttpContentType.String
    )
end
-- DFHD_LUA End

ForceEnableIntro = function()
    FlagForceEnableIntro = true
    loginfo("ForceEnableIntro: ", FlagForceEnableIntro)
end

function LoginInteractLogic._OnGCloudSDKWebBrowserCallback(type, jsonData)
    if string.isempty(jsonData) then
        logerror("LoginInteractLogic._OnGCloudSDKWebBrowserCallback jsonData is nil")
        if type == 100 then
            local status = Module.Login:GetAccountLimitStatus()
            logerror("LoginInteractLogic._OnGCloudSDKWebBrowserCallback reshow account limit window, status:",status.bIsLimit, status.showText)
            if status and status.bIsLimit and status.showText then
                local customHelp = Module.Login.Config.Loc.CustomerHelp
                Module.GCloudSDK:ShowCommonTip(status.showText, confirmText, customHelp, false, SafeCallBack(this._ReturnToLoginScene, nil, false), SafeCallBack(this._OpenCustomerService, nil, false), nil, true)
            end
        end
        return
    end
        
    loginfo("LoginInteractLogic._OnGCloudSDKWebBrowserCallback jsonData:", jsonData)
    --账号注销回调
    local JsonFactory = require "DFM.YxFramework.Plugin.Json.Json"
    local Json = JsonFactory.createJson()
    local json = Json.decode(jsonData)
    local bDeleteAccountSuc = false
    if json then
        local ret_type = json.type or ""
        loginfo("LoginInteractLogic._OnGCloudSDKWebBrowserCallback ret_type:", ret_type)
        if IsBuildRegionCN() then
            if ret_type == "gacc:write_off_success" then
                bDeleteAccountSuc = true
            elseif ret_type == "gacc:write_off_fail" then
                Module.CommonTips:ShowSimpleTip(Module.Login.Config.Loc.DeleteAccountFail)
            end
        else
            if ret_type == "request_delete_account_success" then
                bDeleteAccountSuc = true
            elseif ret_type == "request_delete_account_fail" then
                Module.CommonTips:ShowSimpleTip(Module.Login.Config.Loc.DeleteAccountFail)
            end
        end
  
    end
    if bDeleteAccountSuc then
        Module.CommonTips:ShowConfirmWindowWithSingleBtn(
            Module.Login.Config.Loc.DeleteAccountFinish,
            SafeCallBack(this._ReturnToLoginScene, nil, true),
            Module.Login.Config.Loc.ConfirmBtnText
        )
    end
end

function LoginInteractLogic._OnWebViewClose()
    logerror("LoginInteractLogic._OnGCloudSDKWebBrowserCallback jsonData is nil")
    local status = Module.Login:GetAccountLimitStatus()
    logerror("LoginInteractLogic._OnGCloudSDKWebBrowserCallback reshow account limit window, status:",status.bIsLimit, status.showText)
    if status and status.bIsLimit and status.showText then
        local customHelp = Module.Login.Config.Loc.CustomerHelp
        Module.GCloudSDK:ShowCommonTip(status.showText, confirmText, customHelp, false, SafeCallBack(this._ReturnToLoginScene, nil, false), SafeCallBack(this._OpenCustomerService, nil, false), nil, true)
    end
end

function LoginInteractLogic._OnCultureChanged()
    if UGameLoginIns then
        UGameLoginIns:OnCultureChanged()
    end
end

return LoginInteractLogic