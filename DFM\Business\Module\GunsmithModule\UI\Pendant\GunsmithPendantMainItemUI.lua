----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGunsmith)
----- LOG FUNCTION AUTO GENERATE END -----------



-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPInputType = import"EGPInputType"
-- END MODIFICATION

local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local IVShopItemTemplate = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.IVShopItemTemplate"
local EComp = CommonWidgetConfig.EIVWarehouseTempComponent
local EIVSlotPos = CommonWidgetConfig.EIVSlotPos
local EIVCompOrder = CommonWidgetConfig.EIVCompOrder

---@class GunsmithPendantMainItemUI : LuaUIBaseView
local GunsmithPendantMainItemUI = ui("GunsmithPendantMainItemUI")

function GunsmithPendantMainItemUI:Ctor()
    self._wtShopItem = self:Wnd("wt_WBP_ShopItem", IVShopItemTemplate)
    self._wtShopItem:EnableComponent(EComp.BottomLeftIconText, false)

    self._wtWidgetHeader = self:Wnd("CanvasPanel_2", UIWidgetBase)
    self._wtTextBlockHeader = self:Wnd("wtText", UITextBlock)

    self._itemUIData = nil
    self._opcode = nil
    self._wt_WBP_SlotCompDefaultUsing = nil
end

function GunsmithPendantMainItemUI:Destroy()
    self._itemUIData = nil
    self._opcode = nil
    self._wt_WBP_SlotCompDefaultUsing = nil
end

function GunsmithPendantMainItemUI:OnShow()
    local function fOnItemClickedCallback()
        self:_OnButtonItemUIClicked()
    end
    self._wtShopItem:BindClickCallback(fOnItemClickedCallback)
end

function GunsmithPendantMainItemUI:OnHide()
end

function GunsmithPendantMainItemUI:_OnButtonItemUIClicked()
    Module.Gunsmith.Config.Events.evtGunsmithOnPendantItemUIClicked:Invoke(self._itemUIData, self._opcode)
end

function GunsmithPendantMainItemUI:UpdateUI(itemUIData, opcode)
    self._itemUIData = itemUIData
    self._opcode = opcode

    if self._itemUIData == nil then
        self:_InternalUpdateDefault()
        return
    end

    self:_InternalUpdateIcon()
    self:_InternalUpdateName()
    self:_InternalUpdateFocused()
    self:_InternalUpdateLocked()
    self:_InternalUpdateEquipped()
    self:_InternalUpdateSOLGlobalEquipped()
    self:_InternalUpdateHeaderTitle()
    self:_InternalSetType()
    self:_InternalUpdateShading()
end

function GunsmithPendantMainItemUI:_InternalUpdateDefault()
end

function GunsmithPendantMainItemUI:_InternalUpdateIcon()
    local itemBase = self._itemUIData:GetItemBase()
    self._wtShopItem:InitItem(itemBase)
end

function GunsmithPendantMainItemUI:_InternalUpdateName()
    local component = self._wtShopItem:FindOrAdd(EComp.TopLeftIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.TopLeft)
    self._wtShopItem:EnableComponent(EComp.TopLeftIconText, false)

    local nameComp = self._wtShopItem:FindOrAdd(EComp.TopLeftTwoLineText, UIName2ID.IVTextQualityComponent, EIVSlotPos.TopLeft)
    self._wtShopItem:EnableComponent(EComp.TopLeftTwoLineText, true)
    nameComp:RefreshComponent()
    local name = self._itemUIData:GetShowName()
    nameComp:SetMainTxt(name)
    self._wtShopItem:SetAnchorIfOverFlow()

    local wearAndRatityComp = self._wtShopItem:FindOrAdd(EComp.BottomLeftIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomLeft)
    wearAndRatityComp:SetStyle(CommonWidgetConfig.EIVIconTextStyle.SkinRarity)
    wearAndRatityComp:EnableComponent(true)
    wearAndRatityComp:RefreshComponent()
end

function GunsmithPendantMainItemUI:_InternalUpdateFocused()
    local bFocused = self._itemUIData:GetFocus()
    local component = self._wtShopItem:FindOrAdd(EComp.ItemSelected, UIName2ID.IVItemSelectedComponent, EIVSlotPos.BgLayer)
    self._wtShopItem:EnableComponent(EComp.ItemSelected, bFocused)
    if component and bFocused then
        component:PlayAnim()
    end

    -- local bIsGamepad = self:IsGamepad()
    -- if bIsGamepad then
    --     self._wtShopItem:SetClickIsIgnored(bFocused)
	-- end
end

function GunsmithPendantMainItemUI:IsGamepad()
    local curInputType = WidgetUtil.GetCurrentInputType()
    return IsHD() and curInputType == EGPInputType.Gamepad
end

function GunsmithPendantMainItemUI:_InternalUpdateLocked()
    local component = self._wtShopItem:FindOrAdd(EComp.ItemMark, UIName2ID.IVMaskLockComponent, EIVSlotPos.MaskLayer)
    component:SetTextActive(false)

    local bLocked = self._itemUIData:GetStateLocked()
    self._wtShopItem:EnableComponent(EComp.ItemMark, bLocked)
end

function GunsmithPendantMainItemUI:_InternalUpdateEquipped()
    local component = self._wtShopItem:FindOrAdd(EComp.TopRightIconText, UIName2ID.IVUsingComponent, EIVSlotPos.TopRight)
    local bEquipped = self._itemUIData:GetStateEquipped()
    self._wtShopItem:EnableComponent(EComp.TopRightIconText, bEquipped)
end

function GunsmithPendantMainItemUI:_InternalUpdateSOLGlobalEquipped()
    local bIsValid = isvalid(self._wt_WBP_SlotCompDefaultUsing)
    if not bIsValid then
        self._wt_WBP_SlotCompDefaultUsing = self._wtShopItem:AddChildToMask(UIName2ID.IVShowDefaultUsingComponent)
    end
    bIsValid = isvalid(self._wt_WBP_SlotCompDefaultUsing)
    if not bIsValid then
        return
    end
    local bEquippd = self._itemUIData:GetIsSOLGlobalEquipped()
    self._wt_WBP_SlotCompDefaultUsing:SetActive(bEquippd)
    if not bEquippd then
        return
    end

    local name = self._itemUIData:GetSOLGlobalEquippedName()
    self._wt_WBP_SlotCompDefaultUsing:SetNameText(name)
end

function GunsmithPendantMainItemUI:_InternalUpdateHeaderTitle()
    local bShowHeader = self._itemUIData:IsShowHeader()
    local text = self._itemUIData:GetHeaderText()
    if text == nil then
        bShowHeader = false
    end
    self._wtWidgetHeader:SetActive(bShowHeader)

    if not bShowHeader then
        return
    end

    self._wtTextBlockHeader:SetText(text)
end

function GunsmithPendantMainItemUI:_InternalSetType()
    local bIsFirst = self._itemUIData:GetIsFirst()
    local typeIndex = bIsFirst and 0 or 1
    self:SetType(typeIndex)
end

function GunsmithPendantMainItemUI:_InternalUpdateShading()
    local pendantShading = self._wtShopItem:FindOrAdd(EComp.CommercializeShading, UIName2ID.IVCommercializeShadingComponent, EIVSlotPos.MaskLayer, EIVCompOrder.DefaultOrder)
    self._wtShopItem:EnableComponent(EComp.CommercializeShading, true)
    pendantShading:RefreshComponent()
end

return GunsmithPendantMainItemUI