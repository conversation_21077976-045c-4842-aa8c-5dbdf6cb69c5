----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollectionRoom)
----- LOG FUNCTION AUTO GENERATE END -----------



--------------------------------------------------------------------------
--- UI路径映射配置示例（UILayer表示需要加入的层级）
--------------------------------------------------------------------------

if IsHD() then
    HUDTable[HUDName2ID.CollectionRoom] = {
        HUDTablePath = "",
        HUDConfigIDs = {
            UIName2ID.CollectionRoomInteractorView,
        }
    }
else
    HUDTable[HUDName2ID.CollectionRoom] = {
        HUDTablePath = "",
        HUDConfigIDs = {
            UIName2ID.LootingHudView,
            UIName2ID.CollectionRoomInteractorView,
            UIName2ID.SystemSettingButton,
        }
    }
end

-- 收藏室主界面
UITable[UIName2ID.CollectionRoomMainPanel] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.CollectionRoomModule.UI.CollectionRoomMainPanel",
    BPKey = "WBP_CollectionRoom_MainPanel",
    SubUIs = {
        UIName2ID.CollectionRoomPanel,
        UIName2ID.CollectionListPanel,
    },
    ReConfig = {
        IsPoolEnable = true,
    },
    LinkSubStage = ESubStage.CollectionRoom,
}
-- 收藏室主界面 - 收藏室页签
UITable[UIName2ID.CollectionRoomPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CollectionRoomModule.UI.CollectionRoomPanel",
    BPKey = "WBP_CollectionHome_Main",
    SubUIs = {
        UIName2ID.CollectionRoomCabinetEntranceItem,
    },
    ReConfig = {
        IsPoolEnable = true,
    },
}

-- 收藏室升级页面
UITable[UIName2ID.CollectionUpgradePanel] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.CollectionRoomModule.UI.CollectionRoomUpgrade.CollectionUpgradePanel",
    BPKey = "WBP_CollectionList_Upgrade",
    SubUIs = {
        UIName2ID.IVCommonItemTemplate,
        UIName2ID.CollectionListLevelItem,
    },
    ReConfig = {
        IsPoolEnable = true,
    },
    LinkSubStage = ESubStage.CollectionRoom
}

-- 收藏室升级页面（带赛季限定收藏品专属场景）
UITable[UIName2ID.CollectionUpgradePanelWithLevel] = clone(UITable[UIName2ID.CollectionUpgradePanel])
UITable[UIName2ID.CollectionUpgradePanelWithLevel].LinkSubStage = ESubStage.CollectionLibrary

UITable[UIName2ID.CollectionListLevelItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CollectionRoomModule.UI.CollectionRoomUpgrade.CollectionListLevelItem",
    BPKey = "WBP_CollectionList_LevelItem",
}

-- 收藏室主界面 - 图鉴页签
UITable[UIName2ID.CollectionListPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CollectionRoomModule.UI.CollectionList.CollectionListPanel",
    BPKey = "WBP_CollectionList_Main",
    ReConfig = {
        IsPoolEnable = true,
    },
    SubUIs = {
        UIName2ID.CollectionListSlideItem,
        UIName2ID.AuctionSearchBar,
        UIName2ID.CommonEmptyContent,
        UIName2ID.CollectionCabinetList,
    },
}

UITable[UIName2ID.CollectionCabinetList] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CollectionRoomModule.UI.CollectionList.CollectionCabinetList",
    BPKey = "WBP_CollectionList_ListEntry",
    ReConfig = {
        IsPoolEnable = true,
    },
}

UITable[UIName2ID.CollectionListMainTabList] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonUILibraryModule.UI.Library.DFCommonMainTabList",
    BPKey = "WBP_CollectionList_TabList",
    SubUIs = {
        UIName2ID.CollectionListSlideItem,
    }
}

-- 收藏室主界面 - 图鉴页签子ui
UITable[UIName2ID.CollectionListSlideItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonUILibraryModule.UI.Library.DFCommonCheckButtonOnly",
    BPKey = "WBP_CollectionList_Item",
}

-- 收藏室主界面底部展柜编辑入口子控件
UITable[UIName2ID.CollectionRoomCabinetEntranceItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CollectionRoomModule.UI.CollectionRoomCabinetEntranceItem",
    BPKey = "WBP_CollectionHome_CabinetEntrance_Item",
    SubUIs = {
        UIName2ID.IVMaskSmallLockComponent,
    }
}
-- 收藏室收藏柜、珍藏柜编辑界面
UITable[UIName2ID.CollectionRoomDisplaySpecialCabinetPanel] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.CollectionRoomModule.UI.CollectionRoomDisplaySpecialCabinetPanel",
    BPKey = "WBP_CollectionHome_Cabinet",
    SubUIs = {
        UIName2ID.CollectionRoomSwitchCabinet,
        UIName2ID.CollectionRoomDisplaySpecialItem,
    },
    ReConfig = {
        IsPoolEnable = true,
    },
    LinkSubStage = ESubStage.CollectionRoom,
}

UITable[UIName2ID.CollectionRoomDisplaySpecialItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CollectionRoomModule.UI.CollectionRoomDisplaySpecialItem",
    BPKey = "WBP_CollectionHome_SlotView",
    SubUIs = {
        UIName2ID.IVCommonItemTemplate,
        UIName2ID.IVFreeComponent,
    },
}

UITable[UIName2ID.CollectionRoomSwitchCabinet] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.CollectionRoomModule.UI.CollectionRoomSwitchCabinet",
    BPKey = "WBP_CollectionHome_SwitchCabinet",
    SubUIs = {
        UIName2ID.CollectionRoomCabinetEntranceItem,
    },
}

-----------------------------------------------------------------------
--region DIY展柜

-- 收藏室展示台编辑界面

if IsHD() then
    UITable[UIName2ID.CollectionRoomDIYCabinetPanel] = {
        UILayer = EUILayer.Stack,
        LuaPath = "DFM.Business.Module.CollectionRoomModule.UI.CollectionRoomDIYBooth.CollectionRoomDIYCabinetPanel",
        BPKey = "WBP_CollectionHome_DIYCabinet",
        SubUIs = {
            UIName2ID.CollectionRoomSwitchCabinet,
            UIName2ID.InvSlotView,
            UIName2ID.WarehouseWithTab_HD,
        },
        ReConfig = {
            IsPoolEnable = true,
        },
        LinkSubStage = ESubStage.CollectionRoom,
    }
else
    UITable[UIName2ID.CollectionRoomDIYCabinetPanel] = {
        UILayer = EUILayer.Stack,
        LuaPath = "DFM.Business.Module.CollectionRoomModule.UI.CollectionRoomDIYBooth.CollectionRoomDIYCabinetPanel_M",
        BPKey = "WBP_CollectionHome_DIYCabinet_M",
        SubUIs = {
            UIName2ID.CollectionRoomSwitchCabinet,
            UIName2ID.InvSlotView,
            UIName2ID.WarehouseWithTab,
        },
        ReConfig = {
            IsPoolEnable = true,
        },
        LinkSubStage = ESubStage.CollectionRoom,
    }
end

UITable[UIName2ID.CollectionRoomDIYBoothCanbinetList] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CollectionRoomModule.UI.CollectionRoomDIYBooth.CollectionRoomDIYBoothCanbinetList",
    BPKey = "WBP_CollectionHome_CabinetList",
}

UITable[UIName2ID.CollectionRoomDIYBoothCanbinetItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CollectionRoomModule.UI.CollectionRoomDIYBooth.CollectionRoomDIYBoothCanbinetItem",
    BPKey = "WBP_CollectionHome_DIYSlotView",
}


UITable[UIName2ID.CollectionRoomInteractionTip] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CollectionRoomModule.UI.CollectionRoomInteractionTip",
    BPKey = "WBP_CollectionRoom_InteractionTip",
}


UITable[UIName2ID.CollectionRoomInteractorView] = {
    UILayer = EUILayer.HUD,
    LuaPath = "DFM.Business.Module.CollectionRoomModule.UI.CollectionRoomInteractorView",
    BPKey = "WBP_CollectionHUD_Interact_V1",
    HUDName = "InteractorMainViewCollectionRoom",
    ZOrderBase = EHUDBaseZorder.Common,
    ZOrderOffset = 0,
    Anim = {
        bManuelAnim = true
    }
}


UITable[UIName2ID.CollectionRoomMusicPlayer] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.MusicPlayerModule.UI.CollectionRoomMusicPlayer",
    BPKey = "WBP_CollectionHome_MusicPlayer",
}


--endregion
-----------------------------------------------------------------------

---@class CollectionRoomConfig
local CollectionRoomConfig =
{
    Events = {
        --- Only For Debug ---
        evtFocusDisplayOrSpecialCabinetGrid = LuaEvent:NewIns("CollectionRoomConfig.Events.evtFocusDisplayOrSpecialCabinetGrid"),
        evtFocusDIYCabinetGrid = LuaEvent:NewIns("CollectionRoomConfig.Events.evtFocusDIYCabinetGrid"),
        --- Only For Debug ---

        evtShowItemDetailPanel = LuaEvent:NewIns("CollectionRoomConfig.Events.evtShowItemDetailPanel"),
        evtCollectionListFilter = LuaEvent:NewIns("CollectionRoomConfig.Events.evtCollectionListFilter"),
        evtDIYCabinetGridHighLight = LuaEvent:NewIns("CollectionRoomConfig.Events.evtDIYCabinetGridHighLight"),
        evtShowOrHideAllGridMarker = LuaEvent:NewIns("CollectionRoomConfig.Events.evtShowOrHideAllGridMarker"),
        evtDragEnterDIYGrid = LuaEvent:NewIns("CollectionRoomConfig.Events.evtDragEnterDIYGrid"),
        evtUnSelectAllItems = LuaEvent:NewIns("CollectionRoomConfig.Events.evtUnSelectAllItems"),
        evtOnClickedDIYGrid = LuaEvent:NewIns("CollectionRoomConfig.Events.evtOnClickedDIYGrid"),
        evtOnFocusInteractionTip = LuaEvent:NewIns("CollectionRoomConfig.Events.evtOnFocusInteractionTip"),
        evtOnMusicPlayerEndOpening = LuaEvent:NewIns("evtOnMusicPlayerEndOpening"), -- 音乐播放器完整展开
		evtOnMusicPlayerStartClosing = LuaEvent:NewIns("evtOnMusicPlayerStartClosing"), -- 音乐播放器开始收起
    },
    CameraConfig = {
        -- 主镜头
        Main = {
            "MainLeft",
            "MainMiddle",
            "MainRight",
        },
        -- 图鉴展柜（收藏柜）
        DisplayCabinet = {
            DisplayType = {
                {
                    View = "DisplayCabinetLeft",
                    Focus = "DisplayCabinetLeftItem",
                },
                {
                    View = "DisplayCabinetMiddle",
                    Focus = "DisplayCabinetMiddleItem",
                },
                {
                    View = "DisplayCabinetRight",
                    Focus = "DisplayCabinetRightItem",
                },
            },
        },
        -- 特殊价值展柜（珍藏柜）
        SpecialCabinet = {
            View = "SpecialCabinet",
            Focus = "SpecialCabinetItem",
        },
        -- DIY展柜（展示台）
        DIYCabinet = {
            {
                "DIYCabinet1Left",
                "DIYCabinet1Middle",
                "DIYCabinet1Right",
            },
        }
    },
    EquipmentTypes = {
        ESlotType.MainWeaponLeft,
        ESlotType.MainWeaponRight,
        ESlotType.Pistrol,
        ESlotType.MeleeWeapon,

        ESlotType.Helmet,
        ESlotType.BreastPlate,
        ESlotType.Bag,
        ESlotType.ChestHanging,
    },

    Loc = {
        CurrentLevel = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_LV", "LV.%d"),
        SpecialCabinetAreaName = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_SpecialCabinetAreaName", "珍藏柜区域"),
        DisplayCabinetAreaName = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_DisplayCabinetAreaName", "收藏柜区域"),
        DIYCabinetAreaName = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_DIYCabinetAreaName", "展示台区域"),
        SpecialCabinetName = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_SpecialCabinetName", "珍藏柜"),
        DisplayCabinetName = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_DisplayCabinetName", "收藏柜"),
        UpgradeCostValue = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_UpgradeCostValue", "收藏室不动资产"),
        SeasonLimitValue = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_SeasonLimitValue", "赛季限定"),
        DIYCabinetName = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_DIYCabinetName", "展示台"),
        CollectionRoom = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_CollectionRoom", "收藏室"),
        CollectionList = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_CollectionList", "图鉴"),
        CollectionRoomValue = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_CollectionRoomValue", "收藏室总估值"),
        CabinetValue = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_CabinetValue", "<dfmrichtext type=\"img\" width=\"48\" height=\"48\" id=\"allBankNote\" align=\"0\"/>%s"),
        CabinetLockedTips = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_CabinetLockedTips", "该展柜尚未解锁"),
        CollectionRoomTitle = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_CollectionRoomTitle", "收藏室"),
        CabinetLocked = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_CabinetLocked", "未解锁"),
        UnablePutinWH = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_UnablePutinWH", "仓库已满，无法卸下"),

        -- 图鉴展柜升级
        CabinetUpgradeTitle = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_PreviewCollection", "收藏品预览"),
        CabinetUpgrade = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_CabinetUpgrade", "升级"),
        UnBindItem = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_UnBindCollectionItem", "只能放入非绑定的收集品"),
        CurCabinetLevel = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_CurCabinetLevel", "当前展位：%s级"),
        CabinetMax = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_CabinetMax", "展位已满级"),
        CurCabinetMax = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_CurCabinetMax", "当前展位等级"),

        -- DIY展柜相关
        GenericPlacements = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_GenericPlacements", "通用展示位"),
        LargePlacements = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_LargePlacements", "大型展示位"),
        SmallPlacements = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_SmallPlacements", "小型展示位"),
        ItemCantShelve = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_ItemCantShelve", "当前物品不能上架该展柜"),
        -- 图鉴展柜相关
        DisplayGridLevel = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_DisplayGridLevel", "展柜等级：%s"),
        CabinetLevel = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_CabinetLevel", "展柜等级：Lv.%s"),
        CollectionItemNum = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_CollectionItemNum", "拥有数量：%s"),
        UnBindNum = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_CollectionUnBindNum", "拥有数量（非绑定）：%s"),
        SeasonLimitTxt = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_LimiteItem", "限定"),
        -- 交互物相关
        LeaveCollectionRoom = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_LeaveCollectionRoom", "离开收藏室"),
        ManageCollection = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_ManageCollection", "管理收藏"),
        ManageBGM = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_ManageBGM", "管理BGM"),
        CollectionRoomManage = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_CollectionRoomManage", "收藏室管理"),
        LeaveCollectionRoomResetWarning = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_LeaveCollectionRoomResetWarning", "退出收藏室将重置场景中收藏品的位置，是否确认退出"),
        ManageCollectionResetWarning = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_ManageCollectionResetWarning", "进入收藏品管理界面将重置场景中收藏品的位置，是否确认进入"),
        Confirm = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_Confirm", "确认"),
        Cancel = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_Cancel", "取消"),
        CantPutInText = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_CantPutInText", "无法摆放该物品"),
        NotAvailableDesc = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_NotAvailableDesc", "<customstyle color=\"Color_Highlight01\">%s</>未在<customstyle color=\"Color_Highlight01\">%s</>摆放"),
        SizeMismatchTxt = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_SizeMismatch", "无法摆放该尺寸物品"),
        CollectionRoomPakDownloadConfirmText = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_CollectionRoomPakDownloadConfirmText", "收藏室有{PakSize}MB资源需要下载，是否确认下载？"),

        SpecialCabinetLocked = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_SpecialCabinetUnLock", "珍藏柜未解锁"),
        ItemPutInDeposit = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_ItemPutInDeposit", "物品已放入仓库"),
        SpecialCabinetValue = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_SpecialCabinetValue", "珍藏柜摆放估值"),
        DisplayCabinetValue = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_DisplayCabinetValue", "收藏柜摆放估值"),
        DIYCabinetValue = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_DIYCabinetValue", "展示台摆放估值"),
        DIYCabinetTips = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_DIYCabinetTips", "小型展示位仅可摆放1x1尺寸的收集品，大型展示位无收集品尺寸限制"),
        CabinetMaxBtnTxt = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_CabinetMaxTxt", "展位已满级"),
        PutInTxt = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_PutInBtnTxt", "放置"),
        CanPutInTypeTxt = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_UnBindCollectionItem", "只能放入非绑定的收集品"),
        SwitchCabinet = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_SwitchCabinet", "切换设施"),
        SearchTips = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_SearchTips", "请输入想要查找的收藏品"),
        UpgradeHasUnBindTxt = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_UseUnBindItemTips", "即将消耗仓库中的非绑定<customstyle color=\"Color_Highlight01\">%s</>，用于升级展位表现，是否继续？"),
        NotPutInItems = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_NotPutInItems", "未放入藏品"),

    },

    -- 升级表现文本
    LevelText = {
        NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_Level1Text", "解锁基础功能"),
        NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_Level2Text", "解锁进阶柜面表现"),
        NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_Level3Text", "解锁射灯、顶级柜面表现"),
    },

    SpecialLevelText = {
        NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_Level1Text", "解锁基础功能"),
        NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_SpecialLevel1Text", "解锁展柜的进阶表现"),
        NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_SpecialLevel3Text", "解锁展柜的独特配件"),
    },

    FilterDroDownLoc = {
        NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_FilterDroDownLoc_All", "显示全部"),
        NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_FilterDroDownLoc_Shelf", "只显示上架"),
        NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_FilterDroDownLoc_NoShelf", "只显示未上架"),
        NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_FilterDroDownLoc_SeasonLimited", "只显示赛季限定"),
    },

    SortDroDownLoc = {
        NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_SortDroDownLoc_Default", "默认排序"),
        NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_SortDroDownLoc_ValueAscending", "按价格升序"),
        NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_SortDroDownLoc_ValueDescending", "按价格降序"),
        NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_SortDroDownLoc_LevelAscending", "按收藏柜等级升序"),
        NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_SortDroDownLoc_LevelDescending", "按收藏柜等级降序"),
    },

    ItemType2TypeName = {
        [0] = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_ItemType_0", "特殊"),
        [1] = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_ItemType_1", "电子物品"),
        [2] = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_ItemType_2", "医疗道具"),
        [3] = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_ItemType_3", "工具材料"),
        [4] = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_ItemType_4", "家居物品"),
        [5] = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_ItemType_5", "工艺藏品"),
        [6] = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_ItemType_6", "资料情报"),
        [7] = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_ItemType_7", "能源燃料"),
        [8] = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_ItemType_8", "贵重物品"),
        [9] = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_ItemType_9", "全部"),
        [10] = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_SeasonLimitValue", "赛季限定"),
    },

    PlaceFailReasonTips = {
        [1] = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_SizeMismatch", "无法摆放该尺寸物品"),
        [2] = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_CabinetPositionFull", "当前位置已满，无法摆放"),
        [3] = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_BindCantPutIn", "绑定物品无法摆放到此展位"),
        [4] = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_CabinetSizeNoMatch", "展位尺寸不兼容，无法互换"),
        [5] = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_CabinetTypeMismatch", "此物品类型无法摆放到此展位"),
        [6] = NSLOCTEXT("CollectionRoomModule", "Lua_CollectionRoom_ExchangeTypeMismatch", "交换失败，无法容纳被交换藏品"),
    },
}

CollectionRoomConfig.DIYPosToSortID = {
    3,
    2,
    1,
}

CollectionRoomConfig.ECabinetName = {
    [1] = CollectionRoomConfig.Loc.GenericPlacements,
    [2] = CollectionRoomConfig.Loc.LargePlacements,
    [3] = CollectionRoomConfig.Loc.SmallPlacements,
}

-- 图鉴展柜和特殊展柜当前只有一个，为了以后方便扩容可以设个枚举日后使用
CollectionRoomConfig.EDisplayCabinet = {
    DisplayCabinet_1 = 1,
    DisplayCabinet_2 = 2,
    DisplayCabinet_3 = 3,
}

-- DIY展位放置失败原因
CollectionRoomConfig.EDIYPlaceFailReason = {
    SizeMisMatch = 1,           -- 尺寸不匹配
    PositionIsFull = 2,         -- 摆放位置已满
    BindItemCantPlace = 3,      -- 绑定道具不可摆放
    SizeSwapMisMatch = 4,       -- 尺寸交换不匹配
    ItemTypeMisMatch = 5,       -- 类型不匹配
    ExchangeFailed = 6,         -- 交换失败
}

-- 图鉴展柜根据格子分类
CollectionRoomConfig.EClassificationType = {
    SpecialCabinet = 1,         -- 珍藏柜
    DisplayCabinet = 2,         -- 收藏柜
    SeasonLimit = 3,            -- 赛季限定
}

CollectionRoomConfig.CabinetEntranceIcon = {
    [EShowCabinetType.Display] = "Texture2D'/Game/UI/UIAtlas/System/CollectionRoom/SP/Entrance/CollectionRoom_Sp_Entrance_001.CollectionRoom_Sp_Entrance_001'",
    [EShowCabinetType.DIY] = "Texture2D'/Game/UI/UIAtlas/System/CollectionRoom/SP/Entrance/CollectionRoom_Sp_Entrance_002.CollectionRoom_Sp_Entrance_002'",
    [EShowCabinetType.Special] = "Texture2D'/Game/UI/UIAtlas/System/CollectionRoom/SP/Entrance/CollectionRoom_Sp_Entrance_003.CollectionRoom_Sp_Entrance_003'",
}

CollectionRoomConfig.bPlayWholeSequenceOneTime = true
CollectionRoomConfig.bUseCameraTypeOrDistanceRatio = false -- 使用收藏室收藏品.xlsx中的CameraType还是CameraDistanceRatio
CollectionRoomConfig.displayCabinetLevelUpStage1Seconds = 0.05
CollectionRoomConfig.displayCabinetLevelUpStage2Seconds = 0.95
CollectionRoomConfig.displayCabinetLevelUpTotalSeconds = CollectionRoomConfig.displayCabinetLevelUpStage1Seconds + CollectionRoomConfig.displayCabinetLevelUpStage2Seconds

CollectionRoomConfig.LightSeqPath = "Blueprint'/Game/Environment/MiniWorld_HD/Sequence/CollectionRoom_LightSeq_Actor.CollectionRoom_LightSeq_Actor_C'"
CollectionRoomConfig.bUsePauseAnimSolution = true

return CollectionRoomConfig
