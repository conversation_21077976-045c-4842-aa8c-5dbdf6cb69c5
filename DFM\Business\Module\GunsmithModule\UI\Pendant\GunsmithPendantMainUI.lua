----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGunsmith)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import "EGPUINavWidgetFocusedAction"
-- END MODIFICATION

local GunsmithUI = require "DFM.Business.Module.GunsmithModule.UI.GunsmithUI"
local CommonItemViewDropDownBox = require "DFM.Business.Module.CommonWidgetModule.UI.DropDown.CommonItemViewDropDownBox"
local CollectionMysticalSkinBtn = require "DFM.Business.Module.CollectionModule.UI.CollectionMysticalSkinBtn"

local ItemUIDataContainer = require "DFM.Business.DataStruct.UIDataStruct.ItemUIDataContainer"
local GunsmithPendantItemUIDatasSearchParam = require "DFM.Business.Module.GunsmithModule.Data.Pendant.GunsmithPendantItemUIDatasSearchParam"
local GunsmithSkinSpecialRefreshParam = require "DFM.Business.Module.GunsmithModule.Data.Skin.GunsmithSkinSpecialRefreshParam"
local GunsmithPendantMainItemUIData = require "DFM.Business.Module.GunsmithModule.Data.Pendant.GunsmithPendantMainItemUIData"
local EGunsmithSkinSortType = require "DFM.Business.Module.GunsmithModule.Define.EGunsmithSkinSortType"

local GunsmithPendantLogic = require "DFM.Business.Module.GunsmithModule.Logic.Pendant.GunsmithPendantLogic"
local GunsmithUIContextLogic = require "DFM.Business.Module.GunsmithModule.Logic.GunsmithUIContextLogic"
local GunsmithTLogLogic = require "DFM.Business.Module.GunsmithModule.Logic.TLog.GunsmithTLogLogic"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local LiteCommonDownload =  require "DFM.Business.Module.LitePackageModule.UI.LiteCommonDownload"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"

local EAssemblerCameraType = import "EAssemblerCameraType"
local EAssemblerCamPoint = import "EAssemblerCamPoint"
local EGPInputModeType = import "EGPInputModeType"
local ECheckButtonState = import"ECheckButtonState"


local GunsmithPendantMainUI_ProcessUIOPCode = {
    Force = 1,
    DefaultUI = 2,
    MysticalUI = 3,
}

---@class GunsmithPendantMainUI : GunsmithUI
local GunsmithPendantMainUI = ui("GunsmithPendantMainUI", GunsmithUI)

function GunsmithPendantMainUI:Ctor()
    self._wt_WaterFallList = UIUtil.WndWaterfallScrollBox(self, "wt_WaterFallList", self._OnGetItemCount, self._OnProcessItemWidget, UIName2ID.GunsmithPendantMainItemUI)
    local path = UIName2ID.GetBPFullPathByID(UIName2ID.GunsmithPendantMainItemUI)
    self._wt_WaterFallList:RelinkTemplateWidget(path)

    self._wtEmptySlot = self:Wnd("EmptySlot", UIWidgetBase)

    self._wt_Butten_Customize = self:Wnd("WBP_CommonButtonV3S1_91", CommonButton)
    self._wt_Butten_Customize:SetActive(false)

    self._wt_Button_Equip = self:Wnd("WBP_DFCommonButtonV1S2_57", DFCommonButtonOnly)
    self._wt_Button_Equip:Event("OnClicked", self._OnButtonEquipClicked, self)

    self._wt_Button_EquipAll = self:Wnd("wt_WBP_CommonButtonV1S1_Equip", DFCommonButtonOnly)
    self._wt_Button_EquipAll:Event("OnClicked", self._OnButtonEquipAllClicked, self)

    self._wt_Button_UnequipAll = self:Wnd("WBP_DFCommonButtonV1S2", DFCommonButtonOnly)
    self._wt_Button_UnequipAll:Event("OnClicked", self._OnButtonUnequipAllClicked, self)

    -- self._wt_WBP_CommonCheckBox_LockAppearance = self:Wnd("wt_WBP_CommonCheckBox_LockAppearance", UIWidgetBase):Wnd("DFCheckBox_Icon", UICheckBox)
    -- self._wt_WBP_CommonCheckBox_LockAppearance:SetCallback(self._OnCommonCheckBoxLockAppearanceStateUpdated, self)

    -- self._wt_WBP_CommonCheckBox_EquipOnly = self:Wnd("wt_WBP_CommonCheckBox_EquipOnly", UIWidgetBase):Wnd("DFCheckBox_Icon", UICheckBox)
    -- self._wt_WBP_CommonCheckBox_EquipOnly:SetCallback(self._OnCommonCheckBoxSkinEquipStateUpdated, self)

    self._wtMysticalPendantDropDown = self:Wnd("wtMysticalPendantDropDown", CommonItemViewDropDownBox)

    local data = {
        tabName = Module.Gunsmith.Config.Loc.DropDownBoxTitle,                  -- tab文本名字
        subUINavID = UIName2ID.GunsmithPendantMainItemUI,                       -- 指定生成子ui
        caller = self,                                                          -- 方法持有者
        fOnGetPresetCount = self._OnGetMysticalItemCount,                       -- 子控件数量获取方法
        fOnPresetProcessTabItemWidget = self._OnProcessMysticalItemWidget,      -- 子控件生成回调方法,
    }
    self._wtMysticalPendantDropDown:InitDownBox(data)

    self._wtAlertHintBox = self:Wnd("wtAlertHintBox", UIWidgetBase)
    self._wtAlertHintTxt = self:Wnd("wtAlertHintTxt", UIWidgetBase)

    self._wtMysticalSkinBtnPanel = self:Wnd("wtMysticalSkinBtnPanel", UIWidgetBase)

    self._wtFromMandelBrickBtn = self:Wnd("wtFromMandelBrickBtn", CollectionMysticalSkinBtn)
    self._wtFromMandelBrickBtn:Event("OnClicked", self._OnShowMandelBrickPage, self)

    self._wtFromAuctionBtn = self:Wnd("wtFromAuctionBtn", CollectionMysticalSkinBtn)
    self._wtFromAuctionBtn:Event("OnClicked", self._OnShowAuctionPage, self)

    self._wtFromMatrixWorkshopBtn = self:Wnd("wtFromMatrixWorkshopBtn", CollectionMysticalSkinBtn)
    self._wtFromMatrixWorkshopBtn:Event("OnClicked", self._OnShowMysticalWorkshopPage, self)
    self._wtFromMatrixWorkshopBtn:SetActive(false)

    self._wtCommonDownload = self:Wnd("WBP_CommonDownload", LiteCommonDownload)

    self._searchUIDataParam = GunsmithPendantItemUIDatasSearchParam:NewIns()
    self._itemUIdataContainer = ItemUIDataContainer:NewIns(GunsmithPendantMainItemUIData)
    self._itemUIdataContainer4Mystical = ItemUIDataContainer:NewIns(GunsmithPendantMainItemUIData)
    self._focusItemUIData = nil     ---@type GunsmithPendantMainItemUIData

    self:SetCPPValue("WantedInputMode", EGPInputModeType.GameAndUI)

    self._lastOPCode = GunsmithPendantMainUI_ProcessUIOPCode.Force

    -- 特殊业务需求导致的刷新界面情况,刷新数据,但是UI中的waterfall只做RefreshVisibleItem
    self._specialRefreshParam = GunsmithSkinSpecialRefreshParam:NewIns()

    self._fPendantInfo = WeaponAssemblyTool.GetNewPendantInfo()

    -- self._sortType = EGunsmithSkinSortType.Quality
end

function GunsmithPendantMainUI:Destroy()
    releaseobject(self._searchUIDataParam)
    self._searchUIDataParam = nil

    releaseobject(self._itemUIdataContainer)
    self._itemUIdataContainer = nil

    releaseobject(self._itemUIdataContainer4Mystical)
    self._itemUIdataContainer4Mystical = nil

    releaseobject(self._specialRefreshParam)
    self._specialRefreshParam = nil

    self._fPendantInfo = nil

    self._focusItemUIData = nil
end

-- BEGIN MODIFICATION @ VIRTUOS : 设置导航组
function GunsmithPendantMainUI:OnShowBegin()
    GunsmithUI.OnShowBegin(self)

    if IsHD() then
        self:_EnableNavigation(true)
        self._wtFromMandelBrickBtn._wtBtn:Event("OnFocusReceivedEvent", self._HandleMandelBrickBtnFocusRecive, self)
        self._wtFromMandelBrickBtn._wtBtn:Event("OnFocusLostEvent", self._HandleMandelBrickBtnFocusLost, self)
        self._wtFromAuctionBtn._wtBtn:Event("OnFocusReceivedEvent", self._HandleAuctionBtnFocusRecive, self)
        self._wtFromAuctionBtn._wtBtn:Event("OnFocusLostEvent", self._HandleAuctionBtnFocusLost, self)
    end
end
-- END MODIFICATION

function GunsmithPendantMainUI:OnShow()
    GunsmithUI.OnShow(self)
    self:AddLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithOnPendantItemUIClicked, self._OnProcessGunsmithOnPendantItemUIClicked, self)
    self:AddLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithCSPostAssemblyApplySkin, self._OnProcessevtCSWAssemblyApplySkinRes, self)
    self:AddLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithCSPostAssemblyModifySkin, self._OnProcessevtCSAssemblyModifySkinRes, self)
    self:AddLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithOnRangeDataUpdated, self._OnProcessGunsmithOnRangeDataUpdated, self)
    self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult, self._ModuleDownloadResult, self)
    self:AddLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged, self._OnPackStateChanged, self)
    self:AddLuaEvent(Module.UISceneObject.Config.Events.evtUISceneObjectServiceTaskFinish, self.OnProcessUISceneObjectServiceTaskFinish, self)
    self:AddLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithCSPostAssemblyDepositPropUpdate, self._OnProcessServerDataUpdated, self)
    self:AddLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithCSPostRelayConnected, self.OnProcessRelayConnected, self)

    self:_OnRegisterCameraEvent()
end

-- BEGIN MODIFICATION @ VIRTUOS : 恢复默认导航配置
function GunsmithPendantMainUI:OnHideBegin()
    GunsmithUI.OnHideBegin(self)

    if IsHD() then
        self:_EnableNavigation(false)
        self:_RemoveShortcuts()
    end
end
-- END MODIFICATION

function GunsmithPendantMainUI:OnHide()
    GunsmithUI.OnHide(self)
    self:RemoveLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithOnPendantItemUIClicked)
    self:RemoveLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithCSPostAssemblyApplySkin)
    self:RemoveLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithCSPostAssemblyModifySkin)
    self:RemoveLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithOnRangeDataUpdated)
    self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult)
    self:RemoveLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged)
    self:RemoveLuaEvent(Module.UISceneObject.Config.Events.evtUISceneObjectServiceTaskFinish)
    self:RemoveLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithCSPostAssemblyDepositPropUpdate)
    self:RemoveLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithCSPostRelayConnected)

    self:_OnUnRegisterCameraEvent()
end

function GunsmithPendantMainUI:_OnGetItemCount()
    if self._itemUIdataContainer == nil then
        return 0
    end
    local count = self._itemUIdataContainer:GetCount()
    return count
end

function GunsmithPendantMainUI:_OnProcessItemWidget(position, widget)
    local itemUIData = self._itemUIdataContainer:Get(position)
    widget:UpdateUI(itemUIData, GunsmithPendantMainUI_ProcessUIOPCode.DefaultUI)
end

function GunsmithPendantMainUI:_OnGetMysticalItemCount()
    if self._itemUIdataContainer4Mystical == nil then
        return 0
    end
    local count = self._itemUIdataContainer4Mystical:GetCount()
    return count
end

function GunsmithPendantMainUI:_OnProcessMysticalItemWidget(position, widget)
    position = position + 1
    local itemUIData = self._itemUIdataContainer4Mystical:Get(position)
    widget:UpdateUI(itemUIData, GunsmithPendantMainUI_ProcessUIOPCode.MysticalUI)
end

function GunsmithPendantMainUI:_OnRegisterCameraEvent()
    local cameraActor = GunsmithUIContextLogic.GetCameraActor()
    if isinvalid(cameraActor) then
        return
    end
    self._touchMoveStartHandle = cameraActor.OnTouchMoveStart:Add(CreateCPlusCallBack(self._OnTouchMoveStart, self))
    self._touchMoveEndHandle = cameraActor.OnTouchMoveEnd:Add(CreateCPlusCallBack(self._OnTouchMoveEnd, self))
end

function GunsmithPendantMainUI:_OnUnRegisterCameraEvent()
    local cameraActor = GunsmithUIContextLogic.GetCameraActor()
    if isinvalid(cameraActor) then
        return
    end

    if self._touchMoveStartHandle then
        cameraActor.OnTouchMoveStart:Remove(self._touchMoveStartHandle)
        self._touchMoveStartHandle = nil
    end

    if self._touchMoveEndHandle then
        cameraActor.OnTouchMoveEnd:Remove(self._touchMoveEndHandle)
        self._touchMoveEndHandle = nil
    end
end

function GunsmithPendantMainUI:_OnTouchMoveStart()
end

function GunsmithPendantMainUI:_OnTouchMoveEnd()
    local index = self:_GetFocusItemUIDataIndex()
    self:OnProcessUIUpdate(false, self._lastOPCode, index)
end

function GunsmithPendantMainUI:_OnButtonEquipClicked()
    self:_InternalProcessSync(false)
end

function GunsmithPendantMainUI:_OnButtonEquipAllClicked()
    local bIsLocked = self:_GetPendantLocked()
    if bIsLocked then
        self:_OnProcessPendantLockedClicked()
        return
    end
    self:_InternalEquipAllOrUnequipAll(true)
end

function GunsmithPendantMainUI:_OnButtonUnequipAllClicked()
    self:_InternalEquipAllOrUnequipAll(false)
end

function GunsmithPendantMainUI:_InternalEquipAllOrUnequipAll(bIsEquipAll)
    local bIsMP = GunsmithUIContextLogic.GetGroupIDIsMP()
    local bApplyAll = true
    if bIsMP then
        bApplyAll = false
    end
    self:_InternalProcessSync(bApplyAll)
end

function GunsmithPendantMainUI:_OnProcessPendantLockedClicked()
    local bIsValidNormalJumpID = self:_GetFocusItemUIDataIsValidNormalJumpID()
    local bIsValidNormalBuyPriceRichText = self:_GetFocusItemUIDataIsValidNormalBuyPriceRichText()
    if bIsValidNormalJumpID then
        -- 1.跳转获取
        self:_OnProcessJump()
        return
    elseif bIsValidNormalBuyPriceRichText then
        -- 2.直购获取
        self:_OnProcessDoShopProp()
        return
    end

    -- 0.敬请期待
    Module.CommonTips:ShowSimpleTip(Module.Gunsmith.Config.Loc.GunsmithComminSoon)
end

function GunsmithPendantMainUI:_OnProcessJump()
    local jumpID = self:_GetFocusItemUIDataNormalJumpID()
    local bCanJump = Module.Jump:CheckCanJumpByID(jumpID)
    if bCanJump then
        GunsmithUIContextLogic.JumpByJumpID(jumpID)
        return
    end
    local unlockTips = self:_GetFocusItemUIDataUnlockTips()
    Module.CommonTips:ShowSimpleTip(unlockTips)
end

function GunsmithPendantMainUI:_OnProcessDoShopProp()
    local pendantID = self:_GetFocusItemID()

    local fCallback = function(singlePropMsg)
        local bSuccess = isvalid(singlePropMsg) and singlePropMsg.bSuccess
        if not bSuccess then
            return
        end
        if isvalid(self) and self.OnProcessUIUpdate then
            self:OnProcessUIUpdate(true, nil, nil, pendantID)
        end
    end

    GunsmithUIContextLogic.DoShopProp(pendantID, 1, CreateCallBack(fCallback, self))
end

function GunsmithPendantMainUI:_OnShowMandelBrickPage()
    -- local mandelBrickItemID = Module.Gunsmith.Config.Const.GUNSMITH_PENDANTMAINUI_MANDEBRICK_ITEMID
    local pendantID = self:_GetFocusItemID()
    Module.Collection:ShowMandelBrickPageByPendantID(pendantID)
end

function GunsmithPendantMainUI:_OnShowAuctionPage()
    Module.Collection:ShowMarketPage()
end

function GunsmithPendantMainUI:_OnShowMysticalWorkshopPage()
    Module.Collection:ShowMysticalWorkshopPage()
end

function GunsmithPendantMainUI:_OnProcessGunsmithOnPendantItemUIClicked(uiData, opcode)
    local bEqual = self:_GetEqualFocusItemUIData(uiData, opcode)
    if bEqual then
        return
    end

    local index = uiData:GetIndex()
    self:OnProcessUIUpdate(false, opcode, index)
end

function GunsmithPendantMainUI:_OnProcessevtCSWAssemblyApplySkinRes()
    local bIsFromUserPropInfo = GunsmithUIContextLogic.GetIsFromUserPropInfo()
    if not bIsFromUserPropInfo then
        self:OnProcessUIUpdate(true)
        return
    end
    self:_InternalProcessSpecialUIUpdate()
end

function GunsmithPendantMainUI:_OnProcessevtCSAssemblyModifySkinRes()
    local bIsFromUserPropInfo = GunsmithUIContextLogic.GetIsFromUserPropInfo()
    if not bIsFromUserPropInfo then
        self:OnProcessUIUpdate(true)
        return
    end
    self:_InternalProcessSpecialUIUpdate()
end

function GunsmithPendantMainUI:_InternalProcessSpecialUIUpdate()
    local index = nil
    local opcode = nil
    local bIsMystical = self._lastOPCode == GunsmithPendantMainUI_ProcessUIOPCode.MysticalUI
    bIsMystical = bIsMystical and isvalid(self._focusItemUIData)
    if bIsMystical then
        index = self._focusItemUIData:GetIndex()
        opcode = self._lastOPCode
    end
    local pendantID = self:_GetFocusItemID()
    self:_InternalSetSpecialRefreshParam(true, false, true, false)
    self:OnProcessUIUpdate(true, opcode, index, pendantID)
    self:_InternalSetSpecialRefreshParam(false)
end

function GunsmithPendantMainUI:_OnProcessGunsmithOnRangeDataUpdated(uiParam)
    self:SetUIParam(uiParam)
    self:OnProcessUIUpdate(true)
end

function GunsmithPendantMainUI:_ModuleDownloadResult(moduleName, bSuccess, errorCode)
    local pendantID = self:_GetFocusItemID()
    local pakCategory = Module.ExpansionPackCoordinator:GetDownloadCategary(pendantID)
    if pakCategory == nil or pakCategory ~= moduleName then
        return
    end

    if bSuccess then
        local questName = Module.ExpansionPackCoordinator:GetQuestNameByModuleName(moduleName)
        if questName == nil then
            questName = ""
        end
        local successTips = string.format(Module.Gunsmith.Config.Loc.GunsmithSkinDownloadSuccessTips, questName)
        Module.CommonTips:ShowSimpleTip(successTips)
    end

    local index = self:_GetFocusItemUIDataIndex()
    self:OnProcessUIUpdate(false, nil, index)
end

function GunsmithPendantMainUI:_OnPackStateChanged(moduleName, packState)
    local isSuccess = packState == Module.ExpansionPackCoordinator.Config.PackState.Installed
    self:_ModuleDownloadResult(moduleName, isSuccess, 0)
end

function GunsmithPendantMainUI:OnProcessUISceneObjectServiceTaskFinish()
    GunsmithUIContextLogic.PlayPendantAnimation()
end

function GunsmithPendantMainUI:_OnProcessServerDataUpdated()
    self:OnProcessUIUpdate(true)
end

function GunsmithPendantMainUI:OnProcessRelayConnected()
    -- self:OnProcessUIUpdate(true)
    self:_InternalProcessSpecialUIUpdate()
end

function GunsmithPendantMainUI:OpenFromMainUI(bFromUserClicked)
    self:OnProcessUIUpdate(true)
end

function GunsmithPendantMainUI:OnForceProcessUI()
    GunsmithUIContextLogic.ProcessContext()

    self._focusItemUIData = nil
    self._searchUIDataParam:Reset()
    self._searchUIDataParam:SetDataContainer(self._itemUIdataContainer)
    -- self._searchUIDataParam:SetSortType(self._sortType)
    GunsmithPendantLogic.GetDefaultItemUIDatasFromContext(self._searchUIDataParam)
end

function GunsmithPendantMainUI:OnForceProcessMyticalUI(focusUIData)
    self:_InternalOnForceProcessMyticalUI(focusUIData)

    local count = self._itemUIdataContainer4Mystical:GetCount()
    local focusIndex = count > 0 and 1 or 0
    return focusIndex
end

function GunsmithPendantMainUI:_InternalOnForceProcessMyticalUI(focusUIData)
    local pendantID = 0
    if focusUIData ~= nil then
        pendantID = focusUIData:GetID()
    end
    self:_InternalOnForceProcessMyticalUIByItemID(pendantID)
end

function GunsmithPendantMainUI:_InternalOnForceProcessMyticalUIByItemID(itemID)
    itemID = setdefault(itemID, 0)
    self._searchUIDataParam:Reset()
    self._searchUIDataParam:SetDataContainer(self._itemUIdataContainer4Mystical)

    self._searchUIDataParam:SetPendantID(itemID)
    GunsmithPendantLogic.GetMysticalItemUIDatasFromContext4Pendant(self._searchUIDataParam)
end

function GunsmithPendantMainUI:OnPreProcessUI(opcode, index, itemID)
    -- BEGIN MODIFICATION @ VIRTUOS 
    if IsHD() then
        self:_OnPreSetGamepadFocusWidget()
    end
    -- END MODIFICATION 

    if opcode == nil then
        opcode = GunsmithPendantMainUI_ProcessUIOPCode.DefaultUI
        index = self:_InternalGetIndexByitemID(itemID)
    end

    local focusMysticalIndex = 0
    local uiData = self._focusItemUIData
    if opcode == GunsmithPendantMainUI_ProcessUIOPCode.DefaultUI then
        uiData = self._itemUIdataContainer:Get(index)
        focusMysticalIndex = self:OnForceProcessMyticalUI(uiData)
    else
        local bIsSpecialWaterFallRefresh = self:IsSpecialWaterFallRefresh()
        local bIsMysticalWaterFallRefreshData = self:IsMysticalWaterFallRefreshData()
        local bRefreshData = bIsSpecialWaterFallRefresh and bIsMysticalWaterFallRefreshData
        if bRefreshData then
            self:_InternalOnForceProcessMyticalUIByItemID(itemID)
        end
        focusMysticalIndex = index
    end

    if focusMysticalIndex > 0 then
        uiData = self._itemUIdataContainer4Mystical:Get(focusMysticalIndex)
    end

    self._focusItemUIData = uiData
    self._lastOPCode = opcode
end

function GunsmithPendantMainUI:_InternalGetIndexByitemID(itemID)
    local index = 1
    if itemID == nil then
        return index
    end
    local bContained, itemData = self._itemUIdataContainer:ContainsFromID(itemID)
    if not bContained then
        return index
    end

    index = itemData:GetIndex()
    return index
end

function GunsmithPendantMainUI:OnProcessUI(opcode, index)
    local bScrollToStart = false
    if index == nil then
        index = 0
        bScrollToStart = true
    end

    -- 特殊处理
    local bIsSpecialWaterFallRefresh = self:IsSpecialWaterFallRefresh()
    if bIsSpecialWaterFallRefresh then
        bScrollToStart = self:IsWaterFallRefreshAllUI()
    end

    self:_IntenalOnProcessUI(opcode, index)

    -- if bScrollToStart then
    --     self._wt_WaterFallList:ScrollToStart()
    -- end
end

function GunsmithPendantMainUI:OnPostProcessUI(opcode, index)
    -- BEGIN MODIFICATION @ VIRTUOS : 
    if IsHD() then
        self:_InitShortcuts()
        self:_OnSetGamepadFocusWidget()
    end
    -- END MODIFICATION  
end

function GunsmithPendantMainUI:_IntenalOnProcessUI(opcode, index)
    local itemNum = self:_OnGetItemCount()
    local bIsVisible = itemNum <= 0
    self:SetNothingPanel(bIsVisible)

    local focusItemID = self:_GetFocusItemID()
    self._itemUIdataContainer:SetFocusFromID(focusItemID)

    local focusGUID = self:_GetFocusItemGUID()
    self._itemUIdataContainer4Mystical:SetFocusFromGUID(focusGUID)
    if index == 0 then
        self._wt_WaterFallList:RefreshAllItems()
    else
        self._wt_WaterFallList:RefreshVisibleItem()
    end

    self:_InternalOnProcessUIMyticals(opcode)

    self:_InternalOnProcessUIButtons()
    self:_InternalOnProcessUIDownload()
end

function GunsmithPendantMainUI:OnPreProcessSceneObject()
    if self._focusItemUIData == nil then
        return
    end

    local pendantID = self:_GetFocusItemID()
    local bDownloaded = self:_InternalIsDownloaded(pendantID)
    if not bDownloaded then
        return
    end
    self:_InternalSetPendantInfo()

    GunsmithUIContextLogic.ProcessSetPendantInfo(self._fPendantInfo)
end

function GunsmithPendantMainUI:_InternalSetPendantInfo()
    if self._focusItemUIData == nil then
        return
    end
    local pendantID = self:_GetFocusItemID()
    local pendantGUID = self:_GetFocusItemGUID()
    self._fPendantInfo.PendantId = pendantID
    self._fPendantInfo.PendantGUID = pendantGUID

    local pendantItembase = Server.CollectionServer:GetCollectionItemById(pendantID, pendantGUID)
    local pbPendantInfo = WeaponAssemblyTool.GetWeaponMysticalPendantInfoFromItembase(pendantItembase)
    self._fPendantInfo = WeaponAssemblyTool.SetPBPendantInfo2FPendantInfo(pbPendantInfo, self._fPendantInfo)
end

function GunsmithPendantMainUI:OnProcessSceneObject()
    local cameraPoint = self:_InternalGetCameraPoint()
    GunsmithUIContextLogic.SetCameraType(EAssemblerCameraType.LOCK)
    GunsmithUIContextLogic.SetCameraDefaultPoint(cameraPoint)
    GunsmithUIContextLogic.SetFocusSocket(nil)

    GunsmithUI.OnProcessSceneObject(self)
end

function GunsmithPendantMainUI:_InternalGetCameraPoint()
    local cameraPoint = EAssemblerCamPoint.POINT_PENDANT_POINT
    return cameraPoint
end

function GunsmithPendantMainUI:_InternalOnProcessUISortDropDown()
    -- local dropDownDataList = Module.Gunsmith.Config.SkinMainUISortConfig
    -- UIUtil.InitDropDownBox(self._wtSortDropDown, dropDownDataList, {}, self._sortType)
    -- self._wtSortDropDown:BP_SwitchCheckButtonState(ECheckButtonState.Unchecked)
    -- local sortText = dropDownDataList[self._sortType]
    -- self._wtSortDropDown:BP_SetMainTabText(sortText)
end

function GunsmithPendantMainUI:SetNothingPanel(bIsVisible)
    if bIsVisible then
        self._wtEmptySlot:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        if not self._emptyContent then
            self._emptyContent = Facade.UIManager:AddSubUI(self, UIName2ID.CommonEmptyContent, self._wtEmptySlot)
        end
        local emptyTxt = getfromweak(self._emptyContent):Wnd("RichTextBlock_50", UITextBlock)
        if emptyTxt then
            emptyTxt:SetText(Module.Gunsmith.Config.Loc.GunsmithSolutionShareUINothingText)
        end
    else
        self._wtEmptySlot:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function GunsmithPendantMainUI:_InternalOnProcessUIMyticals(opcode)
    local pendantID = self:_GetFocusItemID()
    local bMysticalPendant = ItemHelperTool.IsMysticalPendant(pendantID)
    local bLocked = self:_GetPendantLocked()
    local bShowMysticalPendantDropDown = bMysticalPendant and (not bLocked)
    self._wtMysticalPendantDropDown:SetActive(bShowMysticalPendantDropDown)

    local mysticalPendantNum = self._itemUIdataContainer4Mystical:GetCount()
    local bEnableMystical = mysticalPendantNum > 0
    if not bEnableMystical then
        self._wtMysticalPendantDropDown:SwitchCheckButtonState(ECheckButtonState.Unchecked)
        self._wtMysticalPendantDropDown:RefreshTab()
        return
    end

    -- BEGIN MODIFICATION @ VIRTUOS : 
    if IsHD() and bShowMysticalPendantDropDown then
        -- 设置下拉框图标和快捷键
        self._wtMysticalPendantDropDown:EnableDropDownShortcutWithAction(true, "Common_RightStickClick_Gamepad")
        self._RotationVec = FVector2D(0, 0)
    end
    -- END MODIFICATION

    local dropDownBoxTitle = string.format(Module.Gunsmith.Config.Loc.DropDownBoxTitle, tostring(mysticalPendantNum))
    self._wtMysticalPendantDropDown:SetMainTabText(dropDownBoxTitle)

    local bRebuildMystical = opcode ~= GunsmithPendantMainUI_ProcessUIOPCode.MysticalUI
    if bRebuildMystical then
        self._wtMysticalPendantDropDown:SwitchCheckButtonState(ECheckButtonState.Unchecked)
        self._wtMysticalPendantDropDown:RefreshTab()
        return
    end

    local bIsSpecialWaterFallRefresh = self:IsSpecialWaterFallRefresh()
    local bIsMysticalWaterFallVisibleItemsNotReBuild = self:IsMysticalWaterFallVisibleItemsNotReBuild()
    local bRefreshVisibleItems = bIsSpecialWaterFallRefresh and (not bIsMysticalWaterFallVisibleItemsNotReBuild)
    if bRefreshVisibleItems then
        self._wtMysticalPendantDropDown:RefreshVisibleItems()
        return
    end

    self._wtMysticalPendantDropDown:RefreshVisibleItemsNotReBuild()
end

function GunsmithPendantMainUI:_InternalOnProcessUIButtons()
    local bEquipped, bEquippedAllDisabled, bLocked, bIsMP, bIsSimulate = self:_GetUIButtonsActive()

    self:_InternalUpdateEquipStype(bEquipped, bLocked, bIsMP, bIsSimulate)
    self:_InternalUpdateEquipAllStype(bEquippedAllDisabled, bLocked, bIsMP)

    self:_InternalOnProcessUIObtainText(bLocked)
    self:_InternalOnProcessUIObtain(bLocked)

    -- self._wt_WBP_CommonCheckBox_LockAppearance:SetActive(bLockAppearance)
    -- self._wt_WBP_CommonCheckBox_LockAppearance:SetIsChecked(LockAppearanceState, false)
end

function GunsmithPendantMainUI:_InternalUpdateEquipStype(bEquipped, bLocked, bIsMP, bIsSimulate)
    if bLocked or bIsMP or bIsSimulate then
        self._wt_Button_Equip:SetActive(false)
        return
    end
    self._wt_Button_Equip:SetActive(true)

    -- if bEquipped then
    --     self._wt_Button_Equip:SetIsEnabledStyle(false)
    -- else
    --     self._wt_Button_Equip:SetIsEnabledStyle(true)
    -- end

    local curText = Module.Gunsmith.Config.Loc.GunsmithSkinDefaultMainUIButtenOnlyEquipText
    if bEquipped then
        curText = Module.Gunsmith.Config.Loc.GunsmithSkinUnEquippedButtonText
    end
    self._wt_Button_Equip:SetMainTitle(curText)
end

function GunsmithPendantMainUI:_InternalUpdateEquipAllStype(bEquippedAllDisabled, bLocked, bIsMP)
    local pendantID = self:_GetFocusItemID()
    local bMysticalPendant = ItemHelperTool.IsMysticalPendant(pendantID)
    local bShow = not (bMysticalPendant and bLocked)
    if not bShow then
        self._wt_Button_EquipAll:SetActive(false)
        self._wt_Button_UnequipAll:SetActive(false)
        return
    end

    -- 跳转获取途径
    local bIsValidNormalJumpID = self:_GetFocusItemUIDataIsValidNormalJumpID()
    -- 直购途径
    local bIsValidNormalBuyPriceRichText = self:_GetFocusItemUIDataIsValidNormalBuyPriceRichText()

    local bShowButtonEquipAll = bLocked or (not bEquippedAllDisabled)
    local bShowButtonUnEquipAll = bEquippedAllDisabled
    self._wt_Button_EquipAll:SetActive(bShowButtonEquipAll)
    self._wt_Button_UnequipAll:SetActive(bShowButtonUnEquipAll)

    local bIsEnabledButtonEquipAll = bLocked and (bIsValidNormalJumpID or bIsValidNormalBuyPriceRichText) or ((not bLocked) and (not bEquippedAllDisabled))
    self._wt_Button_EquipAll:SetIsEnabledStyle(bIsEnabledButtonEquipAll)

    local equipText = Module.Gunsmith.Config.Loc.GunsmithSkinDefaultMainUIButtenAllEquipText
    local unequipText = Module.Gunsmith.Config.Loc.GunsmithSkinGlobalUnEquippedButtonText

    if bLocked then
        if bIsValidNormalJumpID then
            equipText = Module.Gunsmith.Config.Loc.GunsmithSkinDefaultMainUIButtenNormalObtain
        elseif bIsValidNormalBuyPriceRichText then
            equipText = self:_GetFocusItemUIDataNormalBuyPriceRichText()
        else
            equipText = Module.Gunsmith.Config.Loc.GunsmithSkinDefaultLockTitleText
        end
    elseif bIsMP then
        equipText = Module.Gunsmith.Config.Loc.GunsmithSkinDefaultMainUIButtenOnlyEquipText
        unequipText = Module.Gunsmith.Config.Loc.GunsmithSkinUnEquippedButtonText
    end
    self._wt_Button_EquipAll:SetMainTitle(equipText)
    self._wt_Button_UnequipAll:SetMainTitle(unequipText)
end

function GunsmithPendantMainUI:_InternalOnProcessUIObtainText(bLocked)
    local bIsValidNormalJumpID = self:_GetFocusItemUIDataIsValidNormalJumpID()
    local bIsValidNormalObtainDesc = self:_GetFocusItemUIDataIsValidNormalObtainDesc()
    local bIsValidNormalLockDesc = self:_GetFocusItemUIDataIsValidNormalLockDesc()
    local bShow = bLocked and ((bIsValidNormalJumpID and bIsValidNormalObtainDesc) or (not bIsValidNormalJumpID and bIsValidNormalLockDesc))
    self._wtAlertHintBox:SetActive(bShow)
    if not bShow then
        return
    end
    local obtainDescStr = self:_GetFocusItemUIDataNormalObtainDesc()
    if not bIsValidNormalJumpID then
        obtainDescStr = self:_GetFocusItemUIDataNormalLockDesc()
    end
    local bIsValid = obtainDescStr and obtainDescStr ~= "" and string.len(obtainDescStr) > 0
    if not bIsValid then
        self._wtAlertHintBox:SetActive(false)
        return
    end
    self._wtAlertHintTxt:SetText(obtainDescStr)
end

function GunsmithPendantMainUI:_InternalOnProcessUIObtain(bLocked)
    local pendantID = self:_GetFocusItemID()
    local bMysticalPendant = ItemHelperTool.IsMysticalPendant(pendantID)
    local bShow = bMysticalPendant and bLocked
    self._wtMysticalSkinBtnPanel:SetActive(bShow)
    if not bShow then
        return
    end

    local mandelBrickNum = 0
    local mandelBrickItemID = Module.Collection:GetMandelBrickIdByPendantId(pendantID)
    local mandelBrickItem = Server.CollectionServer:GetCollectionItemById(mandelBrickItemID)
    local bIsValid = isvalid(mandelBrickItem) and mandelBrickItem.num
    if bIsValid then
        mandelBrickNum = mandelBrickItem.num
    end
    local mandelBrickIconPath = Module.Gunsmith.Config.Const.GUNSMITH_PENDANTMAINUI_MANDEBRICK_ICON
    local titleStr = string.format(Module.Gunsmith.Config.Loc.GunsmithPendantMainUIPendantMandelDraw, tostring(mandelBrickNum))
    self._wtFromMandelBrickBtn:SetInfo(titleStr, mandelBrickIconPath)

    local auctionIconPath = Module.Gunsmith.Config.Const.GUNSMITH_PENDANTMAINUI_AUCTION_ICON
    self._wtFromAuctionBtn:SetInfo(Module.Gunsmith.Config.Loc.GunsmithPendantMainUIFromMarket, auctionIconPath)

    local matrixWorkshopIconPath = Module.Gunsmith.Config.Const.GUNSMITH_PENDANTMAINUI_MATRIXWORKSHOP_ICON
    self._wtFromMatrixWorkshopBtn:SetInfo(Module.Gunsmith.Config.Loc.GunsmithPendantMainUIFromMatrixWorkshop, matrixWorkshopIconPath)
end

function GunsmithPendantMainUI:_InternalOnProcessUIDownload()
    if self._wtCommonDownload ~= nil then
        local pendantID = self:_GetFocusItemID()
        local bDownloaded = self:_InternalIsDownloaded(pendantID)
        if bDownloaded then
            self._wtCommonDownload:Hidden()
        else
            self._wtCommonDownload:Visible()
        end
    end
end

function GunsmithPendantMainUI:_InternalIsDownloaded(pendantID)
    if self._wtCommonDownload == nil then
        return true
    end
    local pakCategory = Module.ExpansionPackCoordinator:GetDownloadCategary(pendantID)
    return pakCategory == nil or self._wtCommonDownload:InitModuleKey(pakCategory)
end

function GunsmithPendantMainUI:_InternalProcessSync(bApplyAll)
    if self._focusItemUIData == nil then
        return
    end

    local pendantID = self:_GetPendetID(bApplyAll)
    local pendantGUID = self:_GetPendetGUID(bApplyAll)

    local bIsRange = GunsmithUIContextLogic.GetGroupIDIsRange()
    if bIsRange then
        GunsmithUIContextLogic.RangeSetPendantID(pendantID, pendantGUID, bApplyAll)
    else
        local commond, dataType = GunsmithPendantLogic.GetPBWeaponApplyPendantCmdFromContext(pendantID, pendantGUID, bApplyAll)
        if commond == nil then
            return
        end
        Server.GunsmithServer:C2SCSWAssemblyApplySkinReq(dataType, commond)
    end
end

function GunsmithPendantMainUI:_GetPendetID(bApplyAll)
    local bUnequip = self:_InternalUnequip(bApplyAll)
    if bUnequip then
        return 0
    end
    local pendantID = self:_GetFocusItemID()
    return pendantID
end

function GunsmithPendantMainUI:_GetPendetGUID(bApplyAll)
    local bUnequip = self:_InternalUnequip(bApplyAll)
    if bUnequip then
        return 0
    end
    local pendantGUID = self:_GetFocusItemGUID()
    return pendantGUID
end

function GunsmithPendantMainUI:_InternalUnequip(bApplyAll)
    local bEquippedAllDisabled = self:_GetFocusItemIsAllEquipped()
    local bEquipped = self:_GetFocusItemIsEquipped()
    return (bApplyAll and bEquippedAllDisabled) or (not bApplyAll and bEquipped)
end

function GunsmithPendantMainUI:_GetItemUIData(index)
    if self._itemUIdataContainer == nil then
        return nil
    end
    return self._itemUIdataContainer:Get(index)
end

function GunsmithPendantMainUI:_GetEqualFocusItemUIData(uiData, opcode)
    if uiData == nil then
        return true
    end

    if opcode == GunsmithPendantMainUI_ProcessUIOPCode.MysticalUI then
        local lhsItemGUID = self:_GetFocusItemGUID()
        local rhsItemGUID = uiData:GetGUID()
        return lhsItemGUID == rhsItemGUID
    end

    local lhsItemID = self:_GetFocusItemID()
    local rhsItemID = uiData:GetID()
    return lhsItemID == rhsItemID
end

function GunsmithPendantMainUI:_GetFocusItemID()
    if self._focusItemUIData == nil then
        return -1
    end
    local id = self._focusItemUIData:GetID()
    return id
end

function GunsmithPendantMainUI:_GetFocusItemGUID()
    if self._focusItemUIData == nil then
        return -1
    end
    local guid = self._focusItemUIData:GetGUID()
    return guid
end

function GunsmithPendantMainUI:_GetFocusItemIsEquipped()
    if self._focusItemUIData == nil then
        return false
    end
    local bEquipped = self._focusItemUIData:GetStateEquipped()
    return bEquipped
end

function GunsmithPendantMainUI:_GetFocusItemIsAllEquipped()
    if self._focusItemUIData == nil then
        return false
    end
    local bEquippedAll = true
    local bIsMP = GunsmithUIContextLogic.GetGroupIDIsMP()
    if not bIsMP then
        bEquippedAll = self._focusItemUIData:GetIsSOLGlobalEquipped()
    end
    return bEquippedAll
end

function GunsmithPendantMainUI:_GetFocusItemUIDataIndex()
    if self._focusItemUIData == nil then
        return 1
    end
    local id = self._focusItemUIData:GetIndex()
    return id
end

function GunsmithPendantMainUI:_GetPendantLocked()
    if self._focusItemUIData == nil then
        return true
    end
    local bLocked = self._focusItemUIData:GetStateLocked()
    return bLocked
end

function GunsmithPendantMainUI:_GetFocusItemUIDataIsValidNormalObtainDesc()
    if self._focusItemUIData == nil then
        return false
    end
    return self._focusItemUIData:GetIsValidNormalObtainDesc()
end

function GunsmithPendantMainUI:_GetFocusItemUIDataNormalObtainDesc()
    if self._focusItemUIData == nil then
        return nil
    end
    return self._focusItemUIData:GetNormalObtainDesc()
end

function GunsmithPendantMainUI:_GetFocusItemUIDataIsValidNormalLockDesc()
    if self._focusItemUIData == nil then
        return false
    end
    return self._focusItemUIData:GetIsValidNormalLockDesc()
end

function GunsmithPendantMainUI:_GetFocusItemUIDataNormalLockDesc()
    if self._focusItemUIData == nil then
        return nil
    end
    return self._focusItemUIData:GetNormalLockDesc()
end

function GunsmithPendantMainUI:_GetFocusItemUIDataIsValidNormalJumpID()
    if self._focusItemUIData == nil then
        return false
    end
    return self._focusItemUIData:GetIsValidNormalJumpID()
end

function GunsmithPendantMainUI:_GetFocusItemUIDataNormalJumpID()
    if self._focusItemUIData == nil then
        return nil
    end
    return self._focusItemUIData:GetNormalJumpID()
end

function GunsmithPendantMainUI:_GetFocusItemUIDataUnlockTips()
    if self._focusItemUIData == nil then
        return nil
    end
    return self._focusItemUIData:GetUnlockTips()
end

function GunsmithPendantMainUI:_GetFocusItemUIDataIsValidNormalBuyPriceRichText()
    if self._focusItemUIData == nil then
        return false
    end
    return self._focusItemUIData:GetIsValidNormalBuyPriceRichText()
end

function GunsmithPendantMainUI:_GetFocusItemUIDataNormalBuyPriceRichText()
    if self._focusItemUIData == nil then
        return nil
    end
    return self._focusItemUIData:GetNormalBuyPriceRichText()
end

function GunsmithPendantMainUI:_GetUIButtonsActive()
    local bEquipped = false
    local bEquippedAllDisabled = false
    local bLocked = false
    local bIsMP = false
    local bIsSimulate = false

    local function fcallback()
        return bEquipped, bEquippedAllDisabled, bLocked, bIsMP, bIsSimulate
    end
    local bIsRange = GunsmithUIContextLogic.GetGroupIDIsRange()
    bIsSimulate = not bIsRange and GunsmithUIContextLogic.GetIsFromUserPropInfo()

    if self._focusItemUIData == nil then
        return fcallback()
    end

    bLocked = self._focusItemUIData:GetStateLocked()
    if bLocked then
        return fcallback()
    end

    bEquipped = self._focusItemUIData:GetStateEquipped()
    bEquippedAllDisabled = bEquipped

    bIsMP = GunsmithUIContextLogic.GetGroupIDIsMP()
    if not bIsMP then
        bEquippedAllDisabled = self._focusItemUIData:GetIsSOLGlobalEquipped()
    end
    return fcallback()
end

function GunsmithPendantMainUI:_InternalGetSortText(sortType)
    -- sortType = sortType or self._sortType
    -- local dropDownDataList = Module.Gunsmith.Config.SkinMainUISortConfig
    -- return dropDownDataList[sortType]
end

-- BEGIN MODIFICATION @ VIRTUOS : 
function GunsmithPendantMainUI:_EnableNavigation(bEnable)
    if not IsHD() then
        return 
    end

    if bEnable then
        if not self._wtNavGroup then
            self._wtNavGroup = WidgetUtil.RegisterNavigationGroup(self._wt_WaterFallList, self, "Hittest")
            if self._wtNavGroup  then
                self._wtNavGroup:AddNavWidgetToArray(self._wt_WaterFallList)
                self._wtNavGroup:SetScrollRecipient(self._wt_WaterFallList)
                WidgetUtil.BindCustomFocusProxy(self._wtNavGroup, self._WaterfallFocusProxyMaker, self._WaterfallFocusProxyResolver, self)
                WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup)
                -- 设置NavGroup的子控件被Focus时，采用的行为方式，这里为执行自动鼠标左键事件
                self._wtNavGroup:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
            end
            WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)
            if not self._wtNavGroupMystical then
                self._wtNavGroupMystical = WidgetUtil.RegisterNavigationGroup(self._wtMysticalSkinBtnPanel, self, "Hittest")
                if self._wtNavGroupMystical then
                    self._wtNavGroupMystical:AddNavWidgetToArray(self._wtMysticalSkinBtnPanel)
                end
            end
        end
    else
        if self._wtNavGroup or self._wtNavGroupMystical then
            WidgetUtil.RemoveNavigationGroup(self)
            self._wtNavGroup = nil
            self._wtNavGroupMystical = nil
        end
        WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    end
end

function GunsmithPendantMainUI:_WaterfallFocusProxyMaker(inWidget)
    -- 找到所在行
    local row = WidgetUtil.GetParentWidgetByClassName(inWidget, "WBP_GunStand_GunsmithPendantMainUI_C")
    local rowIndex = self._wt_WaterFallList:GetIndexByItem(row)
    return {rowIndex}
end

function GunsmithPendantMainUI:_WaterfallFocusProxyResolver(inProxyHandle)
    local rowIndex = inProxyHandle[1]
    -- 可能item会在屏幕外，先执行滚动
    self._wt_WaterFallList:ScrollToIndexToScreen(rowIndex, 0.5, 0.5)
    local row = self._wt_WaterFallList:GetItemByIndex(rowIndex)
    if row then
        return row
    end
    -- 可能会找不到，返回空，自动使用Gorup的默认逻辑查找聚焦
    return nil
end

function GunsmithPendantMainUI:_OnPreSetGamepadFocusWidget()
    if not IsHD() then
        return
    end

    local bIsForceUIUpdate = self:GetIsForceUIUpdate()
    if not bIsForceUIUpdate then
        return
    end

    if not self._wtNavGroup then
        return
    end
    self._wtNavGroup:SetFocusRecoveryEnabled(false)
end

function GunsmithPendantMainUI:_OnSetGamepadFocusWidget()
    if not IsHD() then
        return
    end

    local bIsForceUIUpdate = self:GetIsForceUIUpdate()
    if not bIsForceUIUpdate then
        return
    end

    if not self._wtNavGroup then
        return
    end

    local focusItemID = self:_GetFocusItemID()
    local focusIdx = self:_InternalGetIndexByitemID(focusItemID)
    WidgetUtil.FocusWidgetByProxy(self._wtNavGroup, {focusIdx}, true)

    self._wtNavGroup:SetFocusRecoveryEnabled(true)
end

-- 新增手柄快捷键（页面中显示的按钮：改装、预改装）
function GunsmithPendantMainUI:_InitShortcuts()
    if not IsHD() then
        return 
    end

    self:_RemoveShortcuts()

    if not self._Equip and self._wt_Button_Equip:IsVisible() then
        local EquipAction = bIsMP and "Common_ButtonBottom_Gamepad" or "Common_ButtonLeft_Gamepad"
        self._Equip = self:AddInputActionBinding(EquipAction, EInputEvent.IE_Pressed, self._ShortcutEventEquipClicked,self, EDisplayInputActionPriority.UI_Stack)
        self._wt_Button_Equip:SetDisplayInputAction(EquipAction, true, nil, true)
    end

    local bIsMP = GunsmithUIContextLogic.GetGroupIDIsMP()
    if not self._EquipAll and self._wt_Button_EquipAll:IsVisible() then
        local EquipAllAction = "Common_ButtonBottom_Gamepad"
        self._EquipAll = self:AddInputActionBinding(EquipAllAction, EInputEvent.IE_Pressed, self._ShortcutEventEquipAllClicked,self, EDisplayInputActionPriority.UI_Stack)
        self._wt_Button_EquipAll:SetDisplayInputAction(EquipAllAction, true, nil, true)
    end

    if not self._UnequipAll and self._wt_Button_UnequipAll:IsVisible() then
        local UnequipAllAction = "Common_ButtonBottom_Gamepad"
        self._UnequipAll = self:AddInputActionBinding(UnequipAllAction, EInputEvent.IE_Pressed, self._ShortcutEventUnequipAllClicked,self, EDisplayInputActionPriority.UI_Stack)
        self._wt_Button_UnequipAll:SetDisplayInputAction(UnequipAllAction, true, nil, true)
    end

    --获取方式
    if not self._AcquisitionMethod and self._wtMysticalSkinBtnPanel:IsVisible() then
        self._AcquisitionMethod = self:AddInputActionBinding("Gunsmith_Acquisition_Gamepad", EInputEvent.IE_Pressed, self._ShortcutEventAcquisitionClicked,self, EDisplayInputActionPriority.UI_Stack)
    end
end

function GunsmithPendantMainUI:_ShortcutEventEquipClicked()
    if IsHD() and self._wt_Button_Equip then
        self._wt_Button_Equip:ButtonClick()
    end
end

function GunsmithPendantMainUI:_ShortcutEventEquipAllClicked()
    if IsHD() and self._wt_Button_EquipAll then
        self._wt_Button_EquipAll:ButtonClick()
    end
end

function GunsmithPendantMainUI:_ShortcutEventUnequipAllClicked()
    if IsHD() and self._wt_Button_UnequipAll then
        self._wt_Button_UnequipAll:ButtonClick()
    end
end

function GunsmithPendantMainUI:_ShortcutEventAcquisitionClicked()
    if IsHD() then
       if self._FocusMandelBtn and self._wtFromMandelBrickBtn then
            self:_OnShowMandelBrickPage()
       end
       if self._FocusAuctionBtn and self._wtFromAuctionBtn then
            self:_OnShowAuctionPage()
       end
    end
end

function GunsmithPendantMainUI:_RemoveShortcuts()
    if not IsHD() then
        return 
    end

    if self._Equip then
        self:RemoveInputActionBinding(self._Equip)
        self._Equip= nil
    end
    if self._EquipAll then
        self:RemoveInputActionBinding(self._EquipAll)
        self._EquipAll= nil
    end
    if self._UnequipAll then
        self:RemoveInputActionBinding(self._UnequipAll)
        self._UnequipAll= nil
    end
    if self._AcquisitionMethod then
        self:RemoveInputActionBinding(self._AcquisitionMethod)
        self._AcquisitionMethod = nil
    end
end

function GunsmithPendantMainUI:_HandleMandelBrickBtnFocusRecive()
    if IsHD() then
        self._FocusMandelBtn = true
    end
end

function GunsmithPendantMainUI:_HandleMandelBrickBtnFocusLost()
    if IsHD() then
        self._FocusMandelBtn = false
    end
end

function GunsmithPendantMainUI:_HandleAuctionBtnFocusRecive()
    if IsHD() then
        self._FocusAuctionBtn = true
    end
end

function GunsmithPendantMainUI:_HandleAuctionBtnFocusLost()
    if IsHD() then
        self._FocusAuctionBtn = false
    end
end
-- END MODIFICATION

function GunsmithPendantMainUI:_InternalSetSpecialRefreshParam(bIsSpecialWaterFallRefresh, bIsWaterFallRefreshAllUI, bIsMysticalWaterFallRefreshData, bIsMysticalWaterFallVisibleItemsNotReBuild)
    self._specialRefreshParam:Reset()
    bIsSpecialWaterFallRefresh = setdefault(bIsSpecialWaterFallRefresh, false)
    bIsWaterFallRefreshAllUI = setdefault(bIsWaterFallRefreshAllUI, false)
    bIsMysticalWaterFallRefreshData = setdefault(bIsMysticalWaterFallRefreshData, false)
    bIsMysticalWaterFallVisibleItemsNotReBuild = setdefault(bIsMysticalWaterFallVisibleItemsNotReBuild, false)
    self._specialRefreshParam:SetSpecialWaterFallRefresh(bIsSpecialWaterFallRefresh)
    self._specialRefreshParam:SetWaterFallRefreshAllUI(bIsWaterFallRefreshAllUI)
    self._specialRefreshParam:SetMysticalWaterFallRefreshData(bIsMysticalWaterFallRefreshData)
    self._specialRefreshParam:SetMysticalWaterFallVisibleItemsNotReBuild(bIsMysticalWaterFallVisibleItemsNotReBuild)
end

function GunsmithPendantMainUI:IsSpecialWaterFallRefresh()
    return self._specialRefreshParam:IsSpecialWaterFallRefresh()
end

function GunsmithPendantMainUI:IsWaterFallRefreshAllUI()
    return self._specialRefreshParam:IsWaterFallRefreshAllUI()
end

function GunsmithPendantMainUI:IsMysticalWaterFallRefreshData()
    return self._specialRefreshParam:IsMysticalWaterFallRefreshData()
end

function GunsmithPendantMainUI:IsMysticalWaterFallVisibleItemsNotReBuild()
    return self._specialRefreshParam:IsMysticalWaterFallVisibleItemsNotReBuild()
end

return GunsmithPendantMainUI
