---------- LOG FUNCTION AUTO GENERATE -------------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
-------- LOG FUNCTION AUTO GENERATE END -----------

---对应蓝图:WBP_MorgenGame_Item7
---@class MorgenSanityItem : LuaUIBaseView
local MorgenSanityItem = ui("MorgenSanityItem")
local ActivityConfig = Module.Activity.Config
local ActivityLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"


function MorgenSanityItem:Ctor()
    self._wtLine = self:Wnd("DFImage_142", UIImage)
	self._wtTimeText = self:Wnd("DFTextBlock_192", UITextBlock)
    self._wtHistoryText = self:Wnd("DFRichTextBlock_61", UITextBlock)
end

--- 理智信息
function MorgenSanityItem:RefreshInfo(idx, history)
	local timeStr = TimeUtil.TransTimestamp2YYMMDDHHMMSSStr(history.time, "YYMMDDHHMMSS")

    if self._wtTimeText ~= nil then
        self._wtTimeText:SetText(timeStr or "")
    end

    if self._wtHistoryText ~= nil then
        local showStr = ""
        if history.game_mode == 1 then
            showStr = string.format(ActivityConfig.Loc.MogenSanityText[1], history.score, history.san_num)
        else
            showStr = string.format(ActivityConfig.Loc.MogenSanityText[2], history.score, history.san_num)
        end
        self._wtHistoryText:SetText(showStr)
    end

	if self._wtLine ~= nil then
        if idx % 2 == 0 then
            self._wtLine:Visible()
        else
            self._wtLine:Collapsed()
        end
    end
end

--- 快速探索信息
function MorgenSanityItem:RefreshRecord(idx, record)
    local timeStr = TimeUtil.TransTimestamp2YYMMDDHHMMSSStr(record.time, "YYMMDDHHMMSS")

    if self._wtTimeText ~= nil then
        self._wtTimeText:SetText(timeStr or "")
    end

    --- 是否是终点语句
    if record.end_desc and record.end_desc ~= "" then
        self._wtHistoryText:SetText(record.end_desc)
    else
        local showStr = ActivityConfig.Loc.MogenExploreRecordText[1]
        if record.position_desc ~= "" then
            showStr = showStr..", "..ActivityLogic.HandleLocalizeText(record.position_desc)
        end
        if record.npc_desc ~= "" then--..", "
            showStr = showStr..ActivityLogic.HandleLocalizeText(record.npc_desc)
        end
        if record.gain_money_num ~= 0 then
            showStr = showStr..string.format(ActivityConfig.Loc.MogenExploreRecordText[2], record.gain_money_num)
        end
        self._wtHistoryText:SetText(showStr)
    end

    if self._wtLine ~= nil then
        if idx % 2 == 0 then
            self._wtLine:Visible()
        else
            self._wtLine:Collapsed()
        end
    end
end

return MorgenSanityItem