----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRankingList)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class RankingListModule : ModuleBase
local RankingListModule = class("RankingListModule", require("DFM.YxFramework.Managers.Module.ModuleBase"))
local RankingListLogic = require "DFM.Business.Module.RankingListModule.RankingListLogic"

local RankAreaConfig = Facade.TableManager:GetTable("RankAreaConfig")
local RankBoardRegionConfig = Facade.TableManager:GetTable("RankBoardRegion")

function RankingListModule:Ctor()
    self._areaData = {}
    self._hashAreaData = {}
    self._boardRegionData = {}
end

function RankingListModule:OnInitModule()
    if IsBuildRegionCN() then
        self:InitAreaData()
        self:InitBoradRegionData()
    end

    self:AddLuaEvent(Module.RoleInfo.Config.Event.evtRoleMainPanelOpen, self.RefreshMainPanelOpen, self)
end

function RankingListModule:OnDestroyModule()
    self:RemoveAllLuaEvent()
end

function RankingListModule:RefreshMainPanelOpen()
    RankingListLogic:RefreshMainPanelOpen()
end

---loadBPRes、TextureRes、private table
function RankingListModule:OnLoadModule()

end

function RankingListModule:InitBoradRegionData()
    self._boardRegionData = {}
    if not RankBoardRegionConfig then
        return
    end
    for index, regionData in pairs(RankBoardRegionConfig) do
        table.insert(self._boardRegionData, {
            RegionName = regionData.RegionName,
            ProvinceTitleID = regionData.ProvinceHeroTitleViewID,
            CityTitleID = regionData.CityHeroTitleViewID,
            CityIcon = regionData.CityIcon,
            bookId = regionData.RegionID,
            Adcode = regionData.Adcode,
            index = tonumber(index),
            RegionRankType = regionData.RegionRankType,
            RegionDes = regionData.RegionDes,
            RegionTitle = regionData.RegionTitle,
        })
    end
    local function fsortfunc(a, b)
        local id_a = a.index
        local id_b = b.index
        return id_a < id_b
    end
    table.sort(self._boardRegionData, fsortfunc)
end

function RankingListModule:InitAreaData()
    self._areaData = {}
    self._hashAreaData = {}
    if not RankAreaConfig then
        return
    end
    for index, areaInfo in pairs(RankAreaConfig) do
        if areaInfo.OpenRankType ~= 0 then
            local ProvinceId = tonumber(string.sub(index, 1, 2))
            if not self._hashAreaData[ProvinceId] then
                self._hashAreaData[ProvinceId] = {}
                self._hashAreaData[ProvinceId].Province = areaInfo.Province
                self._hashAreaData[ProvinceId].CityTable = {}
                self._hashAreaData[ProvinceId].orderId = tonumber(ProvinceId)
            end
            table.insert(self._hashAreaData[ProvinceId].CityTable, {
                City = areaInfo.City ~= "" and areaInfo.City or areaInfo.Province,
                AreaId = index,
                Name = areaInfo.Name
            })
        end
    end

    for key, value in pairs(self._hashAreaData) do
        local function fsortfunc(a, b)
            local id_a = a.AreaId
            local id_b = b.AreaId
            return id_a < id_b
        end
        table.sort(value.CityTable, fsortfunc)
        table.insert(self._areaData, value)
    end
    local function areaSortFunc(a, b)
        local id_a = a.orderId
        local id_b = b.orderId
        return id_a < id_b
    end
    table.sort(self._areaData, areaSortFunc)
end

function RankingListModule:GetAreaData()
    return self._areaData
end

function RankingListModule:GetProvinceTextTbl()
    local ProvinceText = {}
    for i, info in ipairs(self._areaData) do
        table.insert(ProvinceText, info.Province)
    end
    return ProvinceText
end

function RankingListModule:GetCityTextTbl(provinceId)
    local CityText = {}
    for i, info in ipairs(self._hashAreaData[provinceId].CityTable) do
        table.insert(CityText, info.City)
    end
    CityText[1] = Module.RankingList.Config.Loc.AllCityArea
    return CityText
end

function RankingListModule:GetCityTextNoAllTbl(provinceId)
    local CityText = {}
    local bCityOnly = #self._hashAreaData[provinceId].CityTable <= 1
    for i, info in ipairs(self._hashAreaData[provinceId].CityTable) do
        if bCityOnly or i > 1 then
            table.insert(CityText, info.City)
        end
    end
    return CityText
end

function RankingListModule:GetAreaIDByIndex(provinceId, cityId)
    return  self._areaData[provinceId].CityTable[cityId].AreaId
end


function RankingListModule:GetAreaIDByIndexNoAll(provinceId, cityId)
    if #self._areaData[provinceId].CityTable == 1 and cityId == 1 then
        return self._areaData[provinceId].CityTable[cityId].AreaId
    end
    return self._areaData[provinceId].CityTable[cityId + 1].AreaId
end

function RankingListModule:GetProvinceInfoByIndex(index)
    return self._areaData[index]
end

function RankingListModule:ShowRankListMainPanel()
    if self:CheckRankingListOpen() then
        if Server.RankingListServer:GetIsStopPeriod() then  -- 如果处于休赛期直接弹窗返回
            Module.CommonTips:ShowSimpleTip(Module.RankingList.Config.Loc.StopPeriod)
            return
        end
        -- Facade.UIManager:AsyncShowUI(UIName2ID.RankingListMainPanel)

        -- Module.RoleInfo:OpenTitleUnlockPop()

                    Facade.UIManager:AsyncShowUI(UIName2ID.TitleUnlockPop, nil, nil, false)

    end
end

function RankingListModule:CheckRankingListOpen()
    local checkResult = Module.ModuleSwitcher:CheckModuleSwitcher(SwitchSystemID.SwitchSystemRankingList)
    local bIsRankingListOpen = (checkResult == EFirstLockResult.Open)
    return bIsRankingListOpen
end



function RankingListModule:GetChallengingList()
    return self._boardRegionData
end

function RankingListModule:GetCityProvinceIndexByAreaId(areaId, isOne)
    local ProvinceId = tonumber(string.sub(areaId, 1, 2))
    local ProvinceIndex = 1
    local CityIndex = 1
    local isOnCity = true
    for i, provinceInfo in ipairs(self._areaData) do
        if ProvinceId == provinceInfo.orderId then
            ProvinceIndex = i
            isOnCity = #provinceInfo.CityTable == 1
            for j, cityInfo in ipairs(provinceInfo.CityTable) do
                if tonumber(areaId) == tonumber(cityInfo.AreaId) then
                    CityIndex = j
                    break
                end
            end
            break
        end
    end
    if isOne then
        return ProvinceIndex, CityIndex
    else
        if isOnCity then
            return ProvinceIndex, 1
        else
            return ProvinceIndex, CityIndex - 1
        end
    end
end


function RankingListModule:GetCityProvinceTextByAreaId(areaId)
    local ProvinceId = tonumber(string.sub(areaId, 1, 2))
    local ProvinceText = ""
    local CityText = ""
    local isOnCity = true
    for i, provinceInfo in ipairs(self._areaData) do
        if ProvinceId == provinceInfo.orderId then
            for j, cityInfo in ipairs(provinceInfo.CityTable) do
                if tonumber(areaId) == tonumber(cityInfo.AreaId) then
                    CityIndex = j
                    ProvinceText = provinceInfo.Province
                    CityText = cityInfo.City
                    return ProvinceText, CityText
                end
            end
        end
    end
end

function RankingListModule:GetAreaImageById(areaId)
    local rowData = RankAreaConfig[areaId]
    if rowData then
        return rowData.AreaImagePath
    end
    return ""
end

return RankingListModule
