----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMBattlefieldEntry)
----- LOG FUNCTION AUTO GENERATE END -----------



local Config  = require("DFM.Business.Module.BattlefieldEntryModule.BattlefieldEntryConfig")
local BattlefieldEntryLogic = {}

-- 打开模式选单
function BattlefieldEntryLogic.OpenModeSelector(fCallback,selectedGroup,bHighLight,fSelectMapCallback)
    loginfo("BattlefieldEntryLogic.OpenModeSelector",selectedGroup,bHighLight)
    local fRequestCallback=function()
        Facade.UIManager:AsyncShowUI(UIName2ID.BattlefieldModeSelector,fCallback,nil,selectedGroup,bHighLight,fSelectMapCallback)
    end
    Server.GameModeServer:RequestTDMMapBoard(fRequestCallback)
end

function BattlefieldEntryLogic.OpenMapViewWindow(groupId,defaultMapId,needDownload)
    loginfo("BattlefieldEntryLogic.OpenMapViewWindow", groupId,defaultMapId,needDownload)
    local fCallback=function()
        Facade.UIManager:AsyncShowUI(UIName2ID.BattlefieldMapViewWindow,nil,nil,groupId,defaultMapId,needDownload)
    end
    BattlefieldEntryLogic.OpenModeSelector(fCallback)
end

-- 获取模式显示文本
---@param modes pb_MatchModeInfo[]
---@param groupId integer
function BattlefieldEntryLogic.Modes2String(modes)
    loginfo("BattlefieldEntryLogic.Modes2String")
    logtable(modes,true)

    local mode=modes[1] or {}
    local mapData = BattlefieldEntryLogic.GetMapDataByMatchId(mode.match_mode_id or mode.match_id) or {}
    local resultString = setdefault(tostring(mapData.ModeName), "") --.. " : "
    if #modes == 0 then
        logerror("BattlefieldEntryLogic.Modes2String modes list should not be empty")
        resultString = tostring(Config.Loc.NoMap)
    --[[elseif #modes == 1 then
        if mapData then
            resultString = resultString .. (tostring(mapData.MapName) or "")
        end
    else
        resultString = resultString .. StringUtil.PluralTextFormat(Config.Loc.MultipleMap, {["MapNum"]=#modes}) -- 单复数改造[aidenliao]]
    end

    return resultString
end

function BattlefieldEntryLogic.CheckIsMPMap(gameRule)
    if gameRule~= MatchGameRule.TDMClassGameRule and gameRule~= MatchGameRule.TDMODGameRule and
    gameRule~= MatchGameRule.TDMConquestGameRule then
        return false
    end
    return true
end

function BattlefieldEntryLogic.GetMapDataByMatchId(matchId,groupId)
    loginfo("BattlefieldEntryLogic.GetMapDataByMatchId", matchId,groupId)
    local mapInfo=Server.GameModeServer:GetTDMMapInfo(matchId, groupId)
    if mapInfo and Module.BattlefieldEntry.Config.TDMMapTextConfig and Module.BattlefieldEntry.Config.TDMMapIconConfig then
        local mapNameRow=Module.BattlefieldEntry.Config.TDMMapTextConfig[mapInfo.map_name]
        local mapDescRow=Module.BattlefieldEntry.Config.TDMMapTextConfig[mapInfo.map_desc]
        local modeNameRow=Module.BattlefieldEntry.Config.TDMMapTextConfig[mapInfo.mode_name]
        local modeDescRow=Module.BattlefieldEntry.Config.TDMMapTextConfig[mapInfo.mode_desc]
        local modeRuleRow=Module.BattlefieldEntry.Config.TDMMapTextConfig[mapInfo.mode_rule_desc]
        local modeLabelRow=Module.BattlefieldEntry.Config.TDMMapTextConfig[mapInfo.time_desc]
        local mapLabelRow=Module.BattlefieldEntry.Config.TDMMapTextConfig[mapInfo.map_label]
        local mapThumbnailRow=Module.BattlefieldEntry.Config.TDMMapIconConfig[mapInfo.map_thumbnail] 
        local modeThumbnailRow=Module.BattlefieldEntry.Config.TDMMapIconConfig[mapInfo.mode_thumbnail]

        local modeThumbnail=""
        if mapInfo.group_type>Module.BattlefieldEntry.Config.BIG_MAP_GROUP_CONSTANT then
            if modeThumbnailRow and modeThumbnailRow.Value2 and modeThumbnailRow.Value2.AssetPathName~="" and modeThumbnailRow.Value2.AssetPathName~="None" then
                modeThumbnail=modeThumbnailRow and modeThumbnailRow.Value2
            else
                modeThumbnail=modeThumbnailRow and modeThumbnailRow.Value
            end
        else
            modeThumbnail=modeThumbnailRow and modeThumbnailRow.Value
        end
        return {
            MapName=mapNameRow and mapNameRow.Value or mapInfo.map_name or "?",
            MapDesc=mapDescRow and mapDescRow.Value or mapInfo.map_desc or "?",
            ModeName=modeNameRow and modeNameRow.Value or mapInfo.mode_name or "?",
            ModeDesc=modeDescRow and modeDescRow.Value or mapInfo.mode_desc or "?",
            ModeRuleDesc=modeRuleRow and modeRuleRow.Value or mapInfo.mode_rule_desc or "?",
            ModeLabel=modeLabelRow and modeLabelRow.Value or mapInfo.time_desc or "?",
            MapLabel=mapLabelRow and mapLabelRow.Value or mapInfo.map_label or "?",
            ModeLabelNeedReddot=modeLabelRow and modeLabelRow.IfRemind or false,
            MapLabelNeedReddot=mapLabelRow and mapLabelRow.IfRemind or false,
            MapThumbnail=mapThumbnailRow and mapThumbnailRow.Value or mapInfo.map_thumbnail or "",
            ModeThumbnail=modeThumbnail or mapInfo.mode_thumbnail or "",
        }
    end
    
end

function BattlefieldEntryLogic.GetModeDataByGroupId(groupId)
    loginfo("BattlefieldEntryLogic.GetModeDataByGroupId", groupId)
    local groupInfo=Server.GameModeServer:GetTDMMapInGroup(groupId)
    if groupInfo and groupInfo[1] and Module.BattlefieldEntry.Config.TDMMapTextConfig and Module.BattlefieldEntry.Config.TDMMapIconConfig then
        local mapInfo=groupInfo[1]
        local mapNameRow=Module.BattlefieldEntry.Config.TDMMapTextConfig[mapInfo.map_name]
        local mapDescRow=Module.BattlefieldEntry.Config.TDMMapTextConfig[mapInfo.map_desc]
        local modeNameRow=Module.BattlefieldEntry.Config.TDMMapTextConfig[mapInfo.mode_name]
        local modeDescRow=Module.BattlefieldEntry.Config.TDMMapTextConfig[mapInfo.mode_desc]
        local modeRuleRow=Module.BattlefieldEntry.Config.TDMMapTextConfig[mapInfo.mode_rule_desc]
        local modeLabelRow=Module.BattlefieldEntry.Config.TDMMapTextConfig[mapInfo.time_desc]
        local mapLabelRow=Module.BattlefieldEntry.Config.TDMMapTextConfig[mapInfo.map_label]
        local mapThumbnailRow=Module.BattlefieldEntry.Config.TDMMapIconConfig[mapInfo.map_thumbnail] 
        local modeThumbnailRow=Module.BattlefieldEntry.Config.TDMMapIconConfig[mapInfo.mode_thumbnail]

        local modeThumbnail=""
        if mapInfo.group_type>Module.BattlefieldEntry.Config.BIG_MAP_GROUP_CONSTANT then
            if modeThumbnailRow and modeThumbnailRow.Value2 and modeThumbnailRow.Value2~="" and modeThumbnailRow.Value2~="None" then
                modeThumbnail=modeThumbnailRow and modeThumbnailRow.Value2
            else
                modeThumbnail=modeThumbnailRow and modeThumbnailRow.Value
            end
            
        else
            modeThumbnail=mapThumbnailRow and mapThumbnailRow.Value
        end
        return {
            MapName=mapNameRow and mapNameRow.Value or mapInfo.map_name or "?",
            MapDesc=mapDescRow and mapDescRow.Value or mapInfo.map_desc or "?",
            ModeName=modeNameRow and modeNameRow.Value or mapInfo.mode_name or "?",
            ModeDesc=modeDescRow and modeDescRow.Value or mapInfo.mode_desc or "?",
            ModeRuleDesc=modeRuleRow and modeRuleRow.Value or mapInfo.mode_rule_desc or "?",
            ModeLabel=modeLabelRow and modeLabelRow.Value or mapInfo.time_desc or "?",
            MapLabel=mapLabelRow and mapLabelRow.Value or mapInfo.map_label or "?",
            ModeLabelNeedReddot=modeLabelRow and modeLabelRow.IfRemind or false,
            MapLabelNeedReddot=mapLabelRow and mapLabelRow.IfRemind or false,
            MapThumbnail=mapThumbnailRow and mapThumbnailRow.Value or mapInfo.map_thumbnail or "",
            ModeThumbnail=modeThumbnail or mapInfo.mode_thumbnail or "",
        }
    end
end

function BattlefieldEntryLogic.GetMapResourceDataByMatchIdList(matchIdList)
    loginfo("BattlefieldEntryLogic.GetMapResourceDataByMatchIdList")
    local matchMode=Module.GameMode:GetMatchModeByMatchModeId(matchIdList and matchIdList[1])
    return BattlefieldEntryLogic.GetMapResourceDataByGameRuleAndMatchSubMode(matchMode and matchMode.GameRule,matchMode and matchMode.MatchSubMode,matchIdList)
end

function BattlefieldEntryLogic.GetMapResourceDataByGameRuleAndMatchSubMode(gameRule,matchSubMode,matchIdList)
    loginfo("BattlefieldEntryLogic.GetMapResourceDataByGameRuleAndMatchSubMode", gameRule,matchSubMode)
    if matchIdList then
        logtable(matchIdList,true)
    end  
    local capturedConfigRow=nil
    for k,v in pairs(Module.BattlefieldEntry.Config.TDMMapResourceConfig or {})do
        if (v.GameRule==gameRule or v.GameRule==0) and (v.MatchSubMode==matchSubMode or v.MatchSubMode==0) then
            capturedConfigRow=capturedConfigRow or v
            if matchIdList and #v.MatchModeID==#matchIdList then
                local bEqual=true
                for i,j in pairs(v.MatchModeID)do
                    if not table.contains(matchIdList,j) then
                        bEqual=false
                        break
                    end
                end
                if bEqual then
                    return v
                end
            end
        end
    end
    return capturedConfigRow
end

function BattlefieldEntryLogic.GetMandelBrickPoolDataBySeasonId(seasonId)--bp赛季
    loginfo("BattlefieldEntryLogic.GetMandelBrickPoolDataBySeasonId", seasonId)
    seasonId=seasonId or Server.BattlePassServer:GetSeasonID()
    for k,v in pairs(Module.BattlefieldEntry.Config.MPMandelBrickPool or {})do
        if v.SeasonId==seasonId and v.NewMandelBrick==true and v.RuleId==1 then
            return v
        end
    end
end

function BattlefieldEntryLogic.GetTDMMapConstantValueByKey(keyName)
    loginfo("BattlefieldEntryLogic.GetTDMMapConstantValueByKey", keyName)
    for k,v in pairs(Module.BattlefieldEntry.Config.TDMMapConstant or {})do
        if tostring(v.Wariable)==tostring(keyName) then
            return v.Value
        end
    end
end

function BattlefieldEntryLogic.ChangeTDMMapGroup(groupId,bShowTips)
    loginfo("BattlefieldEntryLogic.ChangeTDMMapGroup", groupId,bShowTips)
    Module.BattlefieldEntry.Config.evtBattlefieldEntryMapChanged:Invoke(Module.BattlefieldEntry:GetValidMapsByGroupId(groupId))
    if bShowTips then
        local openedMaps=Server.GameModeServer:GetOpenedMapInGroup(groupId) or {}
        local mapData=Module.BattlefieldEntry:GetMapDataByMatchId(openedMaps[1] and openedMaps[1].match_id,groupId)
        Module.CommonTips:ShowSimpleTip(string.format(Module.BattlefieldEntry.Config.Loc.ModeSelected,mapData and mapData.ModeName or "?"))
    end
end

---@param groupId any
---@return pb_TDMMapBoardInfoDesc[]
function BattlefieldEntryLogic.GetValidMapsByGroupId(groupId)
    loginfo("BattlefieldEntryLogic.GetValidMapsByGroupId", groupId)
    local groupMapSelectType=Server.GameModeServer:GetTDMGroupMapSelectType(groupId)
    local openedMaps=Server.GameModeServer:GetOpenedMapInGroup(groupId)
    local validMaps={}
    if groupMapSelectType==1 then
        validMaps=openedMaps
    else
        for k,v in pairs(Module.BattlefieldEntry.Field:GetCatchedMatchIdListByGroupId(groupId) or {})do
            if Server.GameModeServer:GetTDMMapInfo(v,groupId) then
                table.insert(validMaps,Server.GameModeServer:GetTDMMapInfo(v,groupId))
            end
        end
        if #validMaps==0 then
            if groupMapSelectType==2 or groupMapSelectType==4 then
                validMaps=openedMaps
            else
                local openedMap=openedMaps and openedMaps[1]
                validMaps={openedMap}
            end
        elseif groupMapSelectType==3 and #validMaps>1 then
            validMaps={validMaps[1]}
        end
    end
    return validMaps
end

function BattlefieldEntryLogic.PrintTables()
    local fTransArrytoStr=function(strArray)
        local resultStr=""
        local index=1
        for k,v in pairs(strArray or {})do
            local symbol=index~=1 and "," or ""
            resultStr=string.format("%s%s%s",resultStr,symbol,v)
            index=index+1
        end
        return resultStr
    end
    loginfo("BattlefieldEntryLogic.PrintTables, TDMMapBoardConfig")
    for k,v in pairs(Module.BattlefieldEntry.Config.TDMMapBoardConfig or {})do
        loginfo(v.ID,v.GroupID,v.MatchModeID,v.ModeName,v.MapName,v.ModeThumbnail.AssetPathName,v.MapThumbnail.AssetPathName)
    end
    loginfo("BattlefieldEntryLogic.PrintTables, LitePackageDownloadTable")
    for k,v in pairs(Module.BattlefieldEntry.Config.LitePackageDownloadTable or {})do
        loginfo(v.QuestID,v.ModuleKey,v.QuestName,v.TriggerStyle,v.MapOrModeDownload,v.MapOrModeParams)
    end
    loginfo("BattlefieldEntryLogic.PrintTables, TDMMapResourceConfig")
    for k,v in pairs(Module.BattlefieldEntry.Config.TDMMapResourceConfig or {})do
        local heroBan=fTransArrytoStr(v.HeroBan)
        local heroOnly=fTransArrytoStr(v.HeroOnly)
        local matchModeIds=fTransArrytoStr(v.MatchModeID)
        loginfo(v.GameRule,v.MatchSubMode,matchModeIds,v.Desc,v.ButtonThumbnailHD.AssetPathName,v.ButtonThumbnail.AssetPathName,heroBan,heroOnly)
    end
    loginfo("BattlefieldEntryLogic.PrintTables, TDMMapTextConfig")
    for k,v in pairs(Module.BattlefieldEntry.Config.TDMMapTextConfig or {})do
        loginfo(v.ID,v.Value,v.IfRemind)
    end
    loginfo("BattlefieldEntryLogic.PrintTables, TDMMapIconConfig")
    for k,v in pairs(Module.BattlefieldEntry.Config.TDMMapIconConfig or {})do
        loginfo(v.ID,v.Value.AssetPathName,v.Value2.AssetPathName)
    end
    loginfo("BattlefieldEntryLogic.PrintTables, MPMandelBrickPool")
    for k,v in pairs(Module.BattlefieldEntry.Config.MPMandelBrickPool or {})do
        loginfo(v.Index,v.SeasonId,v.MPMandelBrickID,v.NewMandelBrick,v.RuleId)
    end
    loginfo("BattlefieldEntryLogic.PrintTables, TDMMapConstant")
    for k,v in pairs(Module.BattlefieldEntry.Config.TDMMapConstant or {})do
        loginfo(v.Wariable,v.Value)
    end
end


function BattlefieldEntryLogic.IsInMp()
    return Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Lobby
end

function BattlefieldEntryLogic.CheckIsFlagBattleMode(groupId)
    loginfo("BattlefieldEntryLogic.CheckIsFlagBattleMode", groupId)
    groupId=groupId or Module.BattlefieldEntry:GetGroupId()
    for k,v in pairs(Server.GameModeServer:GetTDMMapInGroup(groupId) or {})do
        if Module.GameMode:GetSubModeByMatchModeId(v.match_id)==MatchSubMode.TDMFlagBattle then
            return true
        end
    end
    return false
end

function BattlefieldEntryLogic.CheckIsNightFightingMode(groupId)
    loginfo("BattlefieldEntryLogic.CheckIsNightFightingMode", groupId)
    groupId=groupId or Module.BattlefieldEntry:GetGroupId()
    for k,v in pairs(Server.GameModeServer:GetTDMMapInGroup(groupId) or {})do
        if Module.GameMode:GetSubModeByMatchModeId(v.match_id)==MatchSubMode.TDMReserve6 then
            return true
        end
    end
    return false
end

function BattlefieldEntryLogic.CheckIsCommanderMode(groupId)
    loginfo("BattlefieldEntryLogic.CheckIsCommanderMode", groupId)
    groupId=groupId or Module.BattlefieldEntry:GetGroupId()
    for k,v in pairs(Server.GameModeServer:GetTDMMapInGroup(groupId) or {})do
        if Module.GameMode:GetGameRuleByMatchModeId(v.match_id)==MatchGameRule.TDMCommanderGameRule then
            return true
        end
    end
    return false
end

function BattlefieldEntryLogic.CheckIsCommanderUnlock()
    loginfo("BattlefieldEntryLogic.CheckIsCommanderUnlock")
    local fCheckCommanderCondition1=function(value)
        local prevSerial=Server.TournamentServer:GetPrevAttendedSerial()
        if prevSerial~=0 then
            local commanderSeasonInfo=Server.TournamentServer:GetCommanderSeasonRankInfo(prevSerial)
            if commanderSeasonInfo and commanderSeasonInfo.HasAttended and commanderSeasonInfo.ScoreMax>=value then
                return true 
            else
                return false
            end
        else
            return false
        end    
    end
    local fCheckCommanderCondition2=function(value)
        local commanderSeasonInfo=Server.TournamentServer:GetCommanderSeasonRankInfo()
        if commanderSeasonInfo and commanderSeasonInfo.HasAttended and commanderSeasonInfo.ScoreMax>=value then
            return true 
        else
            return false
        end     
    end    
    local fCheckCommanderCondition3=function(value)
        if Server.RoleInfoServer.accountLevel>=value then
            return true 
        else
            return false
        end  
    end
    local commanderConditionInfo=Server.GameModeServer:GetTDMCommanderConditionInfo()
    local unlockDesc=""
    if not commanderConditionInfo or #commanderConditionInfo==0 then
        return true
    end
    for k,v in pairs(commanderConditionInfo or {})do
        local conditionStr=""
        local connectStr=k==1 and "" or Module.BattlefieldEntry.Config.Loc.ConnectStrOr
        if v.condition==1 then
            conditionStr=StringUtil.Key2StrFormat(Module.BattlefieldEntry.Config.Loc.ConditionPrevSeasonScore, {score=v.value})
            if fCheckCommanderCondition1(v.value) then
                return true
            end
        elseif v.condition==2 then
            conditionStr=StringUtil.Key2StrFormat(Module.BattlefieldEntry.Config.Loc.ConditionCurSeasonScore, {score=v.value})
            if fCheckCommanderCondition2(v.value) then
                return true
            end
        elseif v.condition==3 then
            conditionStr=StringUtil.Key2StrFormat(Module.BattlefieldEntry.Config.Loc.ConditionMPLevel, {level=v.value})
            if fCheckCommanderCondition3(v.value) then
                return true
            end
        else
            conditionStr="?"
        end
        unlockDesc=string.format("%s%s%s",unlockDesc,connectStr,conditionStr)
    end  
    unlockDesc=string.format("%s%s",unlockDesc,Module.BattlefieldEntry.Config.Loc.StrUnlock)     
    return false,unlockDesc
end

function BattlefieldEntryLogic.CheckHasValidHero(groupId)
    loginfo("BattlefieldEntryLogic.CheckHasValidHero",groupId)
    groupId=groupId or Module.BattlefieldEntry:GetGroupId()
    local validMaps=Module.BattlefieldEntry:GetValidMapsByGroupId(groupId)
    local matchIdList={}
    for k,v in pairs(validMaps or {})do
        table.insert(matchIdList,v.match_id)
    end
    local mapResourceData=Module.BattlefieldEntry:GetMapResourceDataByMatchIdList(matchIdList)
    if mapResourceData then
        local heroData=Server.HeroServer:GetHeroData()
        local availableHeroList={}
        for k,v in pairs(heroData or {})do
            if Server.HeroServer:IsOwningHero(v.hero_id) then
                table.insert(availableHeroList,v.hero_id)
            end
        end
        if mapResourceData.HeroOnly and #mapResourceData.HeroOnly>0 then
            local bContains=false
            local index=1
            local heroNames=""
            local HeroHelperTool = require "DFM.StandaloneLua.BusinessTool.HeroHelperTool"
            for k,v in pairs(mapResourceData.HeroOnly) do
                local symbol=index==1 and "" or Module.BattlefieldEntry.Config.Loc.SymbolCaesura
                local heroName=HeroHelperTool.GetHeroName(tonumber(v))
                heroNames=string.format("%s%s%s",heroNames,symbol,heroName)
                if table.contains(availableHeroList,tonumber(v)) then
                    bContains=true
                    break
                end
                index=index+1
            end
            return bContains,heroNames
        end
    end
    return true
end

function BattlefieldEntryLogic.GetIsModeSelectable(groupId)
    loginfo("BattlefieldEntryLogic.GetIsModeSelectable",groupId)
    if Server.TeamServer:IsMember() then
        return false,Module.BattlefieldEntry.Config.Loc.OnlyCaptainCanSelectMap
    else
        if Server.GameModeServer:GetIsModeOpen(groupId) then
            local hasValidHero,heroNames=Module.BattlefieldEntry:CheckHasValidHero(groupId)
            if hasValidHero then
                return true
            else
                return false,StringUtil.Key2StrFormat(Module.BattlefieldEntry.Config.Loc.NoAvailableHero,{heroNames=heroNames})
            end
        else
            local groupMap=Server.GameModeServer:GetTDMMapInGroup(groupId)
            if groupMap and groupMap[1] then
                local mapInfo=groupMap[1]
                if Server.RoleInfoServer.accountLevel<mapInfo.mode_player_lv then
                    return false,string.format(Module.BattlefieldEntry.Config.Loc.OpenLevelTips,mapInfo.mode_player_lv)
                elseif Server.TournamentServer:GetMaxRankScore()<mapInfo.mode_player_rank then
                    local rankData=Module.Tournament:GetRankDataByScore(mapInfo.mode_player_rank)
                    return false,string.format(Module.BattlefieldEntry.Config.Loc.OpenRankLevelTips,rankData and rankData.Name or "?")
                else
                    return false,Module.BattlefieldEntry.Config.Loc.NotInOpenTime
                end
            else
                logerror("BattlefieldEntryLogic.GetIsModeSelectable, no available map!!!")
                return false,Module.BattlefieldEntry.Config.Loc.NotOpen
            end
        end
    end
end

return BattlefieldEntryLogic
