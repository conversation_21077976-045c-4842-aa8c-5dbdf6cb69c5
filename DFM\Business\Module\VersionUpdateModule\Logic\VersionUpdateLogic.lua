----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMVersionUpdate)
----- LOG FUNCTION AUTO GENERATE END -----------

loginfo = function (...)
    SdkUtil.LogInfo("dolphin",...)
end

logwarning = function (...)
    SdkUtil.LogWarning("dolphin",...)
end

logerror = function (...)
    SdkUtil.LogError("dolphin",...)
end

local VersionUpdateLogic = {}
local UFlibPatchParserHelper =  import "FlibPatchParserHelper"
local UFlibPakHelper = import "FlibPakHelper"
local UKismetSystemLibrary = import "KismetSystemLibrary"
local EQuitPreference = import "EQuitPreference"
local DolphinManager = import "DolphinManager"
local _dolphinManager = DolphinManager.Get(GetGameInstance())
local UCDNInfoManager = import "CDNInfoManager"
local UGameVersionUtils = import "GameVersionUtils"
local UGameSDKManager = import "GameSDKManager"
local _gameSDKManager = UGameSDKManager.Get(GetGameInstance())
local EDolphinVersionUpdateStep = import "EDolphinVersionUpdateStep"

local AppUpdateSavePath = "App"
local ResUpdateSavePath = "Dolphin"
local WifiDownloadSize = 50
local _bNeedUnMountLaunchRes = false --如果本次有更新，需要先UnMount启动时加载的资源
local _bUnMountLaunchResFinish = false

VersionUpdateLogic.IsEnable = function ()
    if _dolphinManager == nil then
        return false
    end
    return _dolphinManager:IsEnable() and not IsInEditor()
end

---是否测试模式
VersionUpdateLogic.IsDebugMode = function ()
    return false
end

---是否使用启动器跳过版本更新
VersionUpdateLogic.IsSkipByLauncher = function ()
    local UGameVersionUtils = import "GameVersionUtils"
    local skipUpdateStr = UGameVersionUtils.GetLauncherParamsByKey("skipUpdate")
    if skipUpdateStr == "yes" then
        loginfo("VersionUpdateLogic:IsSkipByLauncher")
        return true
    end
    return false
end

---跳过版本更新
VersionUpdateLogic.SkipVersionUpdateStep = function()
    logwarning("VersionUpdateLogic:SkipVersionUpdateStep")
    Module.VersionUpdate:FinishVersionUpdateStep(true)
end

---初始化跳过版本更新的步骤
VersionUpdateLogic._InitVersionUpdateStepSkipList = function()
    local bIsShipping = VersionUtil.IsShipping()
    _dolphinManager:ClearSkipStepList()
    --现在都没有首包解压
    _dolphinManager:AddSkipStep(EDolphinVersionUpdateStep.Extract)
    
    --恢复资源修复，先粗略检测资源是否完整，不完整则执行资源修复
    local bResourceIntegrity = VersionUpdateLogic.VerifyResourceIntegrity()
    if bResourceIntegrity then
        _dolphinManager:AddSkipStep(EDolphinVersionUpdateStep.CheckResource)
        loginfo("VersionUpdateLogic._InitVersionUpdateStepSkipList dolphin no need to CheckResource")
    else
        _bNeedUnMountLaunchRes = true
        logerror("VersionUpdateLogic._InitVersionUpdateStepSkipList dolphin need to CheckResource")
    end
    --非Shipping不走App更新 IsHD()不走App更新
    if PLATFORM_ANDROID then
        if (not bIsShipping) then
            _dolphinManager:AddSkipStep(EDolphinVersionUpdateStep.App)
        end
    elseif PLATFORM_OPENHARMONY then
        if not bIsShipping then
            _dolphinManager:AddSkipStep(EDolphinVersionUpdateStep.App)
        end
    elseif PLATFORM_IOS then
        if not bIsShipping then
            _dolphinManager:AddSkipStep(EDolphinVersionUpdateStep.App)
        end
    elseif PLATFORM_WINDOWS then
        if IsHD() then
            _dolphinManager:AddSkipStep(EDolphinVersionUpdateStep.App)
        end
    -- @akihikofeng: App update on XSX and PS5 will be requested by console
    elseif IsXboxSeries() then
        _dolphinManager:AddSkipStep(EDolphinVersionUpdateStep.App)
    elseif IsPS5Family() then
        _dolphinManager:AddSkipStep(EDolphinVersionUpdateStep.App)
    end
end

---开始版本更新
VersionUpdateLogic.BeginVersionUpdateStep = function()
    _bNeedUnMountLaunchRes = false
    _bUnMountLaunchResFinish = false
    local bIsShipping = VersionUtil.IsShipping()
    _dolphinManager.EnableDebugLog = not bIsShipping
    _dolphinManager.EnableErrorLog = true
    local version = UGameVersionUtils.GetVersion()
    _dolphinManager.AppUpdateSavePath = AppUpdateSavePath.."."..version
    _dolphinManager.ResUpdateSavePath = ResUpdateSavePath.."/"..version
    logwarning("ResUpdateSavePath:".._dolphinManager.ResUpdateSavePath)
    VersionUpdateLogic._InitVersionUpdateStepSkipList()
    VersionUpdateLogic.CheckUnMountResource()
    _dolphinManager:CreateDolphin()
    VersionUpdateLogic.AddEvents()
    _dolphinManager:PrepareDolphinConfig()
    _dolphinManager:BeginUpdateStep()
end

VersionUpdateLogic.FinishVersionUpdateStep = function()
    if not VersionUpdateLogic.IsEnable() then
        return
    end
    VersionUpdateLogic.RemoveEvents()
    _dolphinManager:FinishUpdateStep()
end

VersionUpdateLogic.AddEvents = function ()
    if not VersionUpdateLogic.IsEnable() then
        return
    end
    local dolphinController = _dolphinManager.DolphinController
    if isvalid(dolphinController) then
        dolphinController.OnDolphinUpdateProgressDelegate:Add(VersionUpdateLogic._OnDolphinUpdateProgressDelegate)
    else
        logerror("VersionUpdateLogic.AddEvents dolphinController is nil")
    end
end

VersionUpdateLogic.RemoveEvents = function ()
    if not VersionUpdateLogic.IsEnable() then
        return
    end
    local dolphinController = _dolphinManager.DolphinController
    if isvalid(dolphinController) then
        dolphinController.OnDolphinUpdateProgressDelegate:Clear()
    else
        logerror("VersionUpdateLogic.RemoveEvents dolphinController is nil")
    end
end

local function LoadStringFunc(FileSysPath)
	-- local IFile = IFileManager.Get()
	-- if IFile:FileExists(FileSysPath) then
    local ULuaExtension = import "LuaExtension"
	if ULuaExtension.Ext_FileExists(FileSysPath) then
		-- local IsTrue, String = FFileHelper.LoadFileToString("", FileSysPath, 0)
		local IsTrue, String = ULuaExtension.Ext_LoadFileToString("", FileSysPath)
		return String
	end
	return ""
end

--校验资源完整性
VersionUpdateLogic.VerifyResourceIntegrity = function ()
    local mountFileList = VersionUpdateLogic.GetMountFileList()
    local mountFileListNum = #mountFileList
    if mountFileListNum == 0 then
        --白名单数量为0
        logerror("VersionUpdateLogic.VerifyResourceIntegrity dolphin mountFileListNum == 0")
        return false
    end

    local version = UGameVersionUtils.GetVersion()
    local savedDolphinPath = ResUpdateSavePath.."/"..version

    local bIsShipping = UGameVersionUtils.IsShipping()
    savedDolphinPath = _gameSDKManager:GetSavedPath(bIsShipping, savedDolphinPath)
    local pakFiles = UFlibPakHelper.ScanPakFilesByPath(savedDolphinPath)
    local dolphinFileList = {}
	for _, value in pairs(pakFiles) do
		if value then
            local fileName = string.match(value,"([^/\\]+)$")
			table.insert(dolphinFileList, fileName)
		end
	end
    for _, value in pairs(mountFileList) do
        local bDolphinFileExsist= table.contains(dolphinFileList, value)
        if not bDolphinFileExsist then
            logerror("VersionUpdateLogic.VerifyResourceIntegrity dolphin not exsist ", value)
            return false
        end
    end
    return true
end

VersionUpdateLogic.GetMountFileList = function ()
    local mountFileList = {}
    local bIsShipping = UGameVersionUtils.IsShipping()
    local version = UGameVersionUtils.GetVersion()
    local manifestPath = ResUpdateSavePath.."/"..version.."/Paks/manifest.txt"
    manifestPath = _gameSDKManager:GetSavedPath(bIsShipping, manifestPath)
    local manifest = LoadStringFunc(manifestPath)
    if not manifest then
        logerror("VersionUpdateLogic.GetMountFileList file read fail:", manifestPath)
        return mountFileList
    end
    --loginfo("VersionUpdateLogic.GetMountFileList file read :", manifest)
    if string.isempty(manifest) then
        logerror("VersionUpdateLogic.GetMountFileList file read fail manifest is empty")
        return mountFileList
    end
    local fileLines = string.split(manifest, '\n')
    for k,v in pairs(fileLines) do
        local filename = string.trim(v)
        loginfo("VersionUpdateLogic.GetMountFileList filename:", filename)
        table.insert(mountFileList, filename)
    end
    return mountFileList
end

VersionUpdateLogic.LoadResource = function()
	loginfo("VersionUpdateLogic.LoadResource")
	-- reload shader...
	-- local ProjectName = UFlibPatchParserHelper.GetProjectName()
	-- local ExtShaderDir = FPaths.ProjectSavedDir().."ExtenFiles/"
	-- UFlibPatchParserHelper.LoadShaderbytecode("Global",ExtShaderDir)
	-- UFlibPatchParserHelper.LoadShaderbytecode("DFM",ExtShaderDir)
	-- MountPak

	-----------------------------------------------------------
    local version = UGameVersionUtils.GetVersion()
    local savedDolphinPath = ResUpdateSavePath.."/"..version
    -- local pakFiles = UFlibPakHelper.ScanExtenPakFiles(scanPath)

    local mountFileList = VersionUpdateLogic.GetMountFileList()
    local bIsShipping = UGameVersionUtils.IsShipping()
    savedDolphinPath = _gameSDKManager:GetSavedPath(bIsShipping, savedDolphinPath)
    local pakFiles = UFlibPakHelper.ScanPakFilesByPath(savedDolphinPath)
	for _, value in pairs(pakFiles) do
		if value then
            local fileName = string.match(value,"([^/\\]+)$")
			local _,_,build_id = string.find(value,"%d+%.%d+.%d+.%d+.(%d+)_")
			local MoundOrder = build_id or 10
            local bInMountFileList = table.contains(mountFileList, fileName)
            if bInMountFileList then
                local ret = UFlibPakHelper.MountPak(value, MoundOrder, "")
                loginfo("VersionUpdateLogic.LoadResource >> MountPak Dolphin:", value, ",ret:", ret, ",fileName:", fileName)
            else
                loginfo("VersionUpdateLogic.LoadResource >> MountPak Dolphin not in manifest:", value, ",fileName:", fileName)
            end
		end
	end
    -- UFlibPatchParserHelper.ReloadShaderbytecode()
end

VersionUpdateLogic.CheckUnMountResource = function ()
    loginfo("VersionUpdateLogic.CheckUnMountResource", _bNeedUnMountLaunchRes, _bUnMountLaunchResFinish)
    if _bNeedUnMountLaunchRes and not _bUnMountLaunchResFinish then
        --UnMount一次即可
        _bUnMountLaunchResFinish = true
        local version = UGameVersionUtils.GetVersion()
        local savedDolphinPath = ResUpdateSavePath.."/"..version
        local bIsShipping = UGameVersionUtils.IsShipping()
        savedDolphinPath = _gameSDKManager:GetSavedPath(bIsShipping, savedDolphinPath)
        local pakFiles = UFlibPakHelper.ScanPakFilesByPath(savedDolphinPath)
        for _, value in pairs(pakFiles) do
            local ret = UFlibPakHelper.UnMountPak(value)
            loginfo("VersionUpdateLogic.CheckUnMountResource >> UnMountPak Dolphin:", value, ",ret:", ret)
        end
    end
end

VersionUpdateLogic.BeginVersionUpdateStep_Debug = function()
    local bIsShipping = VersionUtil.IsShipping()
    _dolphinManager:ClearSkipStepList()
    --现在都没有首包解压
    _dolphinManager:AddSkipStep(EDolphinVersionUpdateStep.Extract)
    --临时去除资源修复
    _dolphinManager:AddSkipStep(EDolphinVersionUpdateStep.CheckResource)
    --非Shipping不走App更新 IsHD()不走App更新
    -- if PLATFORM_ANDROID then
    --     if (not bIsShipping) then
    --         _dolphinManager:AddSkipStep(EDolphinVersionUpdateStep.App)
    --     end
    -- elseif PLATFORM_IOS then
    --     if not bIsShipping then
    --         _dolphinManager:AddSkipStep(EDolphinVersionUpdateStep.App)
    --     end
    -- elseif PLATFORM_WINDOWS then
    --     if IsHD() then
    --         _dolphinManager:AddSkipStep(EDolphinVersionUpdateStep.App)
    --     end
    -- end
    --_dolphinManager:ClearFiles()
    _dolphinManager.EnableDebugLog = true
    _dolphinManager.EnableErrorLog = true
    _dolphinManager.bIsGrayUpdate = false
    _dolphinManager.AppUpdateSavePath = AppUpdateSavePath
    _dolphinManager.ResUpdateSavePath = ResUpdateSavePath
    _dolphinManager.GcloudURLDebug = "pre-download.500638030-1-2.gcloudsvcs.com"
    --_dolphinManager.VersionURLDebug = "gcp_tcp://pre-download.6.713704403.tc.gcloudgbs.com"
    --_dolphinManager.CDNURLDebug = "http://testhz-1256818929.cos.ap-shanghai.myqcloud.com/pre"
    _dolphinManager.AppVersionDebug = "*******"
    _dolphinManager.ResVersionDebug = "*******"
    _dolphinManager.DolphinChannelIdDebug = 1003463
    if IsBuildRegionGlobal() then
        _dolphinManager.GcloudURLDebug = "pre-download.200061208-1-2.dmpplat.com"
        _dolphinManager.DolphinChannelIdDebug = 1001639
    elseif IsBuildRegionGA() then
        _dolphinManager.GcloudURLDebug = "pre-download.857531452-1-2.gcloudv2.cros.garena.com"
        _dolphinManager.DolphinChannelIdDebug = 1001240
    end
    _dolphinManager:CreateDolphin()
    VersionUpdateLogic.AddEvents()
    _dolphinManager:PrepareDolphinConfig_Debug()
    _dolphinManager:BeginUpdateStep_Debug(EDolphinVersionUpdateStep.Begin)
end

--- 刷新进度条
---@param stage number 更新阶段（详细见C++枚举：GCloud::dolphinUpdateStage）
---@param totalSize number 数据总量
---@param curSize number 数据当前下载量
VersionUpdateLogic._OnDolphinUpdateProgressDelegate = function (stage, totalSize, curSize)
    loginfo("VersionUpdateLogic._OnDolphinUpdateProgressDelegate >> stage :", stage, ", totalSize :", totalSize, ", curSize :", curSize)
    if VersionUpdateLogic.CheckShowProgress(stage) then
        Module.GCloudSDK:UpdatePercentFloat(Module.VersionUpdate.Field:GetDownloadPercent() * 100)
        Module.GCloudSDK:UpdateLeftTimeText(Module.VersionUpdate.Field:GetDownloadProgressInfo())
    else
        Module.GCloudSDK:UpdatePercentFloat(100)
        Module.GCloudSDK:UpdateLeftTimeText("")
    end
    Module.VersionUpdate.Field:UpdateProgress(stage, totalSize, curSize)
    Module.VersionUpdate:UpdateProgressDesc()
    --上报解压资源事件
    if stage == Module.VersionUpdate.Config.EVersionUpdateDolphinStage.SourceExtract then
        Module.GCloudSDK:OnDolphinExtractResource()
    end
end

VersionUpdateLogic.OnUpdateStep = function (step)
    if step == EDolphinVersionUpdateStep.App then
        --上报版本检测事件
        Module.GCloudSDK:OnDolphinCheckAppUpdate()
    end
end

VersionUpdateLogic.IsAppStoreDownload = function ()
    if IsBuildRegionCN() then
        if PLATFORM_IOS then
            return true
        elseif PLATFORM_OPENHARMONY then
            return true
        end
    else
        if PLATFORM_ANDROID then
            local bIsAAB = UGameVersionUtils.IsAndroidAppBundle()
            if bIsAAB then
                ----Google AppStore
                return true
            else
                if IsBuildRegionGlobal() and IsChannelStoreAPK() then
                    --渠道商店包
                    return true
                else
                    return false
                end
            end
        elseif PLATFORM_IOS then
            return true
        end
    end
    return false
end

--- 弹出公告弹窗
---@param versionStr string 版本号
---@param bIsForceUpdate boolean 是否是强更
---@param bIsAppUpdate boolean 是否是App更新
---@param bIsWifi boolean 是否wifi
---@param downloadSize number 下载大小
VersionUpdateLogic.ShowUpdateAnnounceWindow = function (versionStr, bIsForceUpdate, bIsAppUpdate, bIsWifi, downloadSize)
    logwarning("ShowUpdateConfirmWindow >> versionStr :", versionStr, "bIsForceUdpate: ", tostring(bIsForceUpdate), "bIsAppUpdate:", tostring(bIsAppUpdate), "bIsWifi: ", tostring(bIsWifi), "downloadSize:", tostring(downloadSize))
    if not bIsAppUpdate then
        --如果本次有更新，需要先UnMount启动时加载的资源
        _bNeedUnMountLaunchRes = true
        VersionUpdateLogic.CheckUnMountResource()
    end
    --PC没有公告逻辑
    if IsHD() then
        VersionUpdateLogic.OnAnnounceConfirmCallback(true)
        return
    end
    
    versionStr = versionStr or ""
    downloadSize = downloadSize or 0

    local bIsDownloadSizeExceed = downloadSize > (WifiDownloadSize * 1024 * 1024)
    local downloadSizeStr = Module.VersionUpdate.Field:GetSizeStr(downloadSize)

    local newVersionDesc = "%s%s"
    local bIsForceOpen = false
    if bIsAppUpdate then
        local bIsAppStoreDownload = VersionUpdateLogic.IsAppStoreDownload()
        loginfo("ShowUpdateConfirmWindow >> bIsAppStoreDownload :", bIsAppStoreDownload)
        bIsForceOpen = true
        if bIsAppStoreDownload then
            if bIsForceUpdate then
                newVersionDesc = Module.VersionUpdate.Config.Loc.UpdateNewAppVersionForceAppStoreUpdate
            else
                newVersionDesc = Module.VersionUpdate.Config.Loc.UpdateNewAppVersionOptionalAppStoreUpdate
            end
            newVersionDesc = string.format(newVersionDesc, versionStr)
        else
            if bIsForceUpdate then
                newVersionDesc = Module.VersionUpdate.Config.Loc.UpdateNewAppVersionForceUpdate
            else
                newVersionDesc = Module.VersionUpdate.Config.Loc.UpdateNewAppVersionOptionalUpdate
            end
            newVersionDesc = string.format(newVersionDesc, versionStr, downloadSizeStr)
        end
    else
        bIsForceOpen = bIsDownloadSizeExceed and not bIsWifi
        if bIsForceUpdate then
            newVersionDesc = Module.VersionUpdate.Config.Loc.UpdateNewResVersionForceUpdate
        else
            newVersionDesc = Module.VersionUpdate.Config.Loc.UpdateNewResVersionOptionalUpdate
        end
        newVersionDesc = string.format(newVersionDesc, versionStr, downloadSizeStr)
    end

    loginfo("ShowUpdateConfirmWindow >> bIsForceOpen :", bIsForceOpen)

    if (IsMobile()) and not bIsWifi then
        newVersionDesc = newVersionDesc .. Module.VersionUpdate.Config.Loc.UpdateDownloadWifi
    end

    -- 第一步，先拉公告，有公告再开始
    -- 检测到中控App公告就不走SDK公告了
    if Module.VersionUpdate:CheckShowAppCDNInfoWindow() then
        return
    end
        
    -- 先看看CDN有没有下发公告，有的话就默认用CDN里的
    if Module.VersionUpdate:CheckShowCDNAnnounceWindow() then
        return
    end
        
    -- 拉之前先绑事件，在用完之后解绑，把对应信息传到回调里
    local listenerHandle = nil
    listenerHandle = Module.GCloudSDK.Config.Events.evtOnLoadAnnounceData:AddListener(function()
        Module.GCloudSDK.Config.Events.evtOnLoadAnnounceData:RemoveListenerByHandle(listenerHandle)
        Module.VersionUpdate:ShowAnnounceStep(newVersionDesc, bIsForceUpdate, bIsForceOpen, bIsAppUpdate, versionStr, downloadSizeStr)
    end, self)
    Module.GCloudSDK:LoadNoticeData()
end

VersionUpdateLogic.ShowUpdateConfirmWindow = function(updateDesc, bIsForceUpdate)
    local title = Module.VersionUpdate.Config.Loc.UpdateConfirmWindowTittle
    local text = updateDesc
    local cancelTxt = Module.VersionUpdate.Config.Loc.UpdateConfirmWindowCancel
    local confirmTxt = Module.VersionUpdate.Config.Loc.UpdateConfirmWindowConfirm

    local confirmHandle = function()
        _dolphinManager:OnUpdateConfirmWindowCallBack(true)
    end

    local cancelHandle = function()
        _dolphinManager:OnUpdateConfirmWindowCallBack(false)
    end

    Module.GCloudSDK:ShowCommonTip(text, confirmTxt, cancelTxt, false, confirmHandle, cancelHandle)
end

VersionUpdateLogic.OnAnnounceConfirmCallback = function(bIsConfirm)
    bIsConfirm = bIsConfirm or false
    _dolphinManager:OnAnnounceConfirmWindowCallBack(bIsConfirm)
end

--获取错误描述
VersionUpdateLogic.GetErrorText = function (errorCode, errorType)
    local text = ""
    --游戏内部错误
    if errorCode > 10000 and errorCode < 99999 then
        text = Module.VersionUpdate.Config.Loc.ErrorCodeText[errorCode] or ""
    --先判断单个错误码
    elseif errorCode == 154140709 then
        text = Module.VersionUpdate.Config.Loc.ErrorCodeText[154140709] or ""
    elseif errorCode == 289407004 then
        text = Module.VersionUpdate.Config.Loc.ErrorCodeText[289407004] or ""
    elseif errorCode == 353501190 then
        text = Module.VersionUpdate.Config.Loc.ErrorCodeText[353501190] or ""
    elseif errorCode == 422576143 then
        text = Module.VersionUpdate.Config.Loc.ErrorCodeText[422576143] or ""
    elseif errorCode == 554696704 then
        text = Module.VersionUpdate.Config.Loc.ErrorCodeText[554696704] or ""
    elseif errorCode == 555745297 then
        text = Module.VersionUpdate.Config.Loc.ErrorCodeText[555745297] or ""
    --再判断一个范围段的错误码
    elseif errorCode >= 154140673 and errorCode <= 154140697 then
        text = Module.VersionUpdate.Config.Loc.ErrorCodeText[154140673] or ""
    --版本信息不存在细分错误码
    elseif errorCode >= 158334977 and errorCode <= 158335076 then
        text = Module.VersionUpdate.Config.Loc.ErrorCodeText[158334977] or ""
    --ErrorCode不满足的时候，判断类型
    elseif errorType and errorType > 0 then
        text = Module.VersionUpdate.Config.Loc.ErrorTypeText[errorType] or ""
    else
        text = Module.VersionUpdate.Config.Loc.ErrorDefaultText
    end
    if string.isempty(text) then
        text = Module.VersionUpdate.Config.Loc.ErrorDefaultText
    end
    text = text.."("..errorCode..")"
    return text
end

VersionUpdateLogic.IsAppUpdate = function ()
    if IsMobile() then
        local EDolphinVersionUpdateStep = import "EDolphinVersionUpdateStep"
        return (Module.VersionUpdate.Field:GetUpdateStep() == EDolphinVersionUpdateStep.App)
    end
    return false
end

--是否致命错误
VersionUpdateLogic.IsFatalError = function (errorCode, errorType)
    --自发行的App更新失败
    if IsMobile() and VersionUpdateLogic.IsAppUpdate() and not VersionUpdateLogic.IsAppStoreDownload() then
        return true
    end
    if errorType == Module.VersionUpdate.Config.EErrorType.NoSpace then
        return true
    -- elseif errorType == Module.VersionUpdate.Config.EErrorType.System then
    --     return true
    elseif errorType == Module.VersionUpdate.Config.EErrorType.NotSupportUpdate then
        return true
    elseif errorType == Module.VersionUpdate.Config.EErrorType.CurNetNotSupportUpdate then
        return true
    end

    local fatalErrorList = {
        --游戏内部错误
        10001,--创建Dolphin失败
		10002,--初始化失败
		10003,--下载路径创建失败
		10004,--下载路径为空
		10005,--下载路径包含中文
		10006,--磁盘空间不足
        --Dolphin组件错误
        --154140709,--Dolphin控制台没有对应版本
        289407004,--IFS文件错误，文件系统错误（28），磁盘空间不足，请释放空间后重试
        555745297,--文件系统错误，无权限或磁盘空间不足
        --555745312,--文件被占用
    }
    return table.contains(fatalErrorList, errorCode)
end

VersionUpdateLogic.ShowUpdateFailWindow = function(errorCode, errorType)
    local text = VersionUpdateLogic.GetErrorText(errorCode, errorType)
    local title = Module.VersionUpdate.Config.Loc.UpdateFailWindowTittle
    local cancelTxt = Module.VersionUpdate.Config.Loc.UpdateFailWindowCancel
    local confirmTxt = Module.VersionUpdate.Config.Loc.UpdateFailWindowConfirm
    local isFatalError = VersionUpdateLogic.IsFatalError(errorCode, errorType)
    local confirmHandle = function()
        if isFatalError then
            _dolphinManager:OnUpdateFailWindowCallBack(true)
        else
            --非阻塞性问题
            _dolphinManager:OnUpdateFailWindowCallBack(false)
        end
    end
    local cancelHandle = function()
        _dolphinManager:OnUpdateFailWindowCallBack(false)
    end
    if not VersionUtil.IsShipping() then
        text = text..Module.VersionUpdate.Config.Loc.UpdateFailWindowFailFinishDebugDesc
        confirmHandle = function ()
            logwarning("VersionUpdateLogic:ShowUpdateFailWindow fail skip dev")
            _dolphinManager:OnUpdateFailWindowCallBack(false)
        end
    end
    local onlyConfirmBtn = true
    Module.GCloudSDK:ShowCommonTip(text, confirmTxt, cancelTxt, onlyConfirmBtn, confirmHandle, cancelHandle)
end

VersionUpdateLogic.ShowAppCDNInfoWindow = function(notice, url)
    local title = Module.VersionUpdate.Config.Loc.UpdateFailWindowTittle
    local text = notice
    local cancelTxt = Module.VersionUpdate.Config.Loc.UpdateFailWindowCancel
    local confirmTxt = Module.VersionUpdate.Config.Loc.UpdateFailWindowConfirm

    local confirmHandle = function()
        Module.GCloudSDK:OpenUrl(url, 1, true, false, nil, true)
    end

    Module.GCloudSDK:ShowCommonTip(text, confirmTxt, cancelTxt, true, confirmHandle, nil, false)
end

VersionUpdateLogic.CheckShowAppCDNInfoWindow = function ()
    local CDNInfoManager = UCDNInfoManager.GetCDNInfoMgrIns(GetGameInstance())
    if CDNInfoManager and CDNInfoManager:GetInited() then
        local appUpdateInfo = CDNInfoManager:GetAppUpdateInfo()
        if appUpdateInfo and appUpdateInfo.bIsUsable and not string.isempty(appUpdateInfo.Notice) and not string.isempty(appUpdateInfo.Url) then
            VersionUpdateLogic.ShowAppCDNInfoWindow(appUpdateInfo.Notice, appUpdateInfo.Url)
            return true
        end
    end
    return false
end

-- 是否展示云控下发的公告信息(先不考虑多语言)
VersionUpdateLogic.CheckShowCDNAnnounceInfo = function()
    local CDNInfoManager = UCDNInfoManager.GetCDNInfoMgrIns(GetGameInstance())
    if CDNInfoManager and CDNInfoManager:GetInited() then
        local announcementData = CDNInfoManager:GetCDNNoticeInfo()
        loginfo("[AnnounceContent]", announcementData.Content)
        if announcementData and announcementData.bIsUsable and not string.isempty(announcementData.Content) then
            local content = VersionUpdateLogic.ConvertCDNAnnounce2NoticeInfo(announcementData)
            local noticeType = announcementData.NoticeType

            if noticeType == 1000 then -- 紧急公告
                Module.GCloudSDK.Config.Events.evtOnAnnouncementBeginShow:Invoke(content,nil,nil)
            elseif noticeType == 1001 then -- 强更公告
                Module.GCloudSDK.Config.Events.evtOnAnnouncementBeginShow:Invoke(nil,content,nil)
            else -- 非强更公告
                Module.GCloudSDK.Config.Events.evtOnAnnouncementBeginShow:Invoke(nil,nil,content)
            end

            Module.GCloudSDK.Config.Events.evtOnAnnouncementFinishPanel:Invoke(0)
            return true
        end
    end
    return false
end

-- 模拟一层公告信息
VersionUpdateLogic.ConvertCDNAnnounce2NoticeInfo = function(cdnAnnounceData)
    -- 已经check过了，就不再重复check了

    local extraJson = string.format("{\"Version\":\"%s\"}", tostring(cdnAnnounceData.Version))
    local content = {
        [1] = {
            ["textInfo"] = {
                ["noticeTitle"] = cdnAnnounceData.Title,
                ["noticeContent"] = cdnAnnounceData.Content
            },
            ["contentType"] = 1, -- 纯文本类型
            ["extraJson"] = extraJson
        }
    }
    return content
end

---是否显示进度条
VersionUpdateLogic.CheckShowProgress = function (stage)
    --app diff下载 app full下载 资源下载
    if stage == Module.VersionUpdate.Config.EVersionUpdateDolphinStage.ApkUpdateDownDiffFile
    or stage == Module.VersionUpdate.Config.EVersionUpdateDolphinStage.ApkUpdateDownFullApk
    or stage == Module.VersionUpdate.Config.EVersionUpdateDolphinStage.SourceDownload
    then
        return true
    end
    return false
end

-- 获取当前Dolphin的错误码
---@return number
VersionUpdateLogic.GetErrorCode = function()
    if _dolphinManager then
        return _dolphinManager:GetErrorCode()
    end
    return 0
end

-- 获取当前Dolphin的错误类型
---@return number
VersionUpdateLogic.GetErrorType = function()
    if _dolphinManager then
        return _dolphinManager:GetErrorType()
    end
    return 0
end

return VersionUpdateLogic
