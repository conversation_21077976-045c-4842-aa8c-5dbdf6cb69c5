----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaSCollectionRoom)
----- LOG FUNCTION AUTO GENERATE END -----------



local CollectionRoomServerConfig = require "DFM.Business.ServerCenter.ServerConfig.CollectionRoomSeverConfig"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"

---@class CollectionRoomServer : ServerBase
local CollectionRoomServer = class("CollectionRoomServer", require("DFM.YxFramework.Managers.Server.ServerBase"))

---@class CabinetItemData 展示物品数据
---@field gid number 物品gid
---@field id number 物品id

---@class CabinetGridData 展柜格子数据
---@field id number 格子ID
---@field level number 展位等级
---@field item CabinetItemData 展示物品数据

---@class EShowCabinetType 展柜类型
EShowCabinetType = {
    None = 0,
    Display = 1,
    Special = 2,
    DIY = 3,
}

---@class CabinetData 展柜数据
---@field id number 展柜id
---@field type EShowCabinetType 展柜类型
---@field grids table<number,CabinetGridData> 展柜格子数据
---@field unlocked boolean 是否解锁
---@field price number 估值

---@class ECabinetGridDiffType 展柜格子变化类型
ECabinetGridDiffType = {
    Add = 1,
    Remove = 2,
    Change = 3,
    LevelUp = 4,
    LockState = 5,
}

bEnableCollectionRoomDebug = false

function EnableOrDisableCollectionRoomDebug(bEnable)
    if VersionUtil.IsShipping() then
        return
    end
    bEnableCollectionRoomDebug = bEnable
    if bEnable then
        CollectionRoomServerConfig.LoadItemIds()
    end
    Server.CollectionRoomServer:FetchServerData()
end

local REQ_MIN_GAP_TIME = 1
local function GenerateKey(cabinetType, cabinetId, gridId)
    return cabinetType * 10000 + cabinetId * 1000 + gridId
end

function CollectionRoomServer:Ctor()
    ---@type table<EShowCabinetType,table<number,CabinetData>> 展柜类型到展柜id到其数据的映射
    self.idToCabinetDataMap = {}
    ---@type table<number, number> 缓存展示台近期请求上架的展位与请求的时间
    self.key2ShelveReqTime = {}
    ---@type table<number, number> 缓存展示台近期请求下架的展位与请求的时间
    self.key2WithdrawReqTime = {}
end

function CollectionRoomServer:OnInitServer()
    self.Events = {
        ---@type LuaEvent
        evtCollectionRoomCabinetGridChange = LuaEvent:NewIns("CollectionRoomServer.evtCollectionRoomCabinetGridChange"),
        evtIsClickedReddot = LuaEvent:NewIns("CollectionRoomServer.evtIsClickedReddot"),
    }
end

function CollectionRoomServer:OnLoadingLogin2Frontend(gameFlowType)
    loginfo("CollectionRoomServer:OnLoadingLogin2Frontend")
    self:FetchServerData()
end

function CollectionRoomServer:FetchServerData()
    self:_ReqGetShowRoom()
end

function CollectionRoomServer:OnGameFlowChangeEnter(gameFlowType)
    if gameFlowType == EGameFlowStageType.SafeHouse then
        logerror("CollectionRoomServer:OnGameFlowChangeEnter SafeHouse")
        self:FetchServerData()
    end
end

---@param CSShowCabinet pb_CSShowCabinet
function CollectionRoomServer:_UpdateCabinetData(CSShowCabinet, bFraming)
    bFraming = setdefault(bFraming, false)
    local cabinetId = CSShowCabinet.cabinet_id
    self.idToCabinetDataMap[CSShowCabinet.type] = self.idToCabinetDataMap[CSShowCabinet.type] or {}
    self.idToCabinetDataMap[CSShowCabinet.type][cabinetId] = self.idToCabinetDataMap[CSShowCabinet.type][cabinetId] or {}
    local cabinetData = self.idToCabinetDataMap[CSShowCabinet.type][cabinetId]
    cabinetData.type = CSShowCabinet.type
    cabinetData.id = CSShowCabinet.cabinet_id
    local eventList = {}
    if cabinetData.unlocked ~= nil and cabinetData.unlocked ~= not CSShowCabinet.locked then
        table.insert(eventList, {ECabinetGridDiffType.LockState, CSShowCabinet.type, CSShowCabinet.cabinet_id, 0, bFraming})
    end
    cabinetData.unlocked = not CSShowCabinet.locked
    cabinetData.price = CSShowCabinet.price
    cabinetData.grids = cabinetData.grids or {}
    local currentGridIds = table.keys(cabinetData.grids)
    local remainedGrids = {}
    for _, CSShowGrid in pairs(CSShowCabinet.grids) do
        local gridId = CSShowGrid.grid_id
        local cabinetGridDiffType
        if CSShowGrid.item.gid ~= 0 then
            if not cabinetData.grids[gridId] or not cabinetData.grids[gridId].item then
                cabinetGridDiffType = ECabinetGridDiffType.Add
                cabinetData.grids[gridId] = cabinetData.grids[gridId] or {}
                local grid = cabinetData.grids[gridId]
                grid.item = grid.item or {}
                grid.item.gid = CSShowGrid.item.gid
                grid.item.id = CSShowGrid.item.id
            elseif cabinetData.grids[gridId].item.gid ~= CSShowGrid.item.gid then
                cabinetGridDiffType = ECabinetGridDiffType.Change
                cabinetData.grids[gridId] = cabinetData.grids[gridId] or {}
                local grid = cabinetData.grids[gridId]
                grid.item = grid.item or {}
                grid.item.gid = CSShowGrid.item.gid
                grid.item.id = CSShowGrid.item.id
            end
        else
            if cabinetData.grids[gridId] and cabinetData.grids[gridId].item then
                cabinetGridDiffType = ECabinetGridDiffType.Remove
                cabinetData.grids[gridId].item = nil
            end
        end
        cabinetData.grids[gridId] = cabinetData.grids[gridId] or {}
        local grid = cabinetData.grids[gridId]
        grid.id = gridId
        if grid.level ~= nil and CSShowGrid.level > grid.level then
            cabinetGridDiffType = ECabinetGridDiffType.LevelUp
        end
        grid.level = CSShowGrid.level
        grid.price = CSShowGrid.price
        remainedGrids[grid.id] = true
        if cabinetGridDiffType then
            if cabinetGridDiffType == ECabinetGridDiffType.Change then
                table.insert(eventList, {ECabinetGridDiffType.Remove, CSShowCabinet.type, CSShowCabinet.cabinet_id, gridId, bFraming})
                table.insert(eventList, {ECabinetGridDiffType.Add, CSShowCabinet.type, CSShowCabinet.cabinet_id, gridId, bFraming})
            else
                table.insert(eventList, {cabinetGridDiffType, CSShowCabinet.type, CSShowCabinet.cabinet_id, gridId, bFraming})
            end
        end
    end
    for _, gridId in pairs(currentGridIds) do
        if not remainedGrids[gridId] then
            cabinetData.grids[gridId].item = nil
            table.insert(eventList, {ECabinetGridDiffType.Remove, CSShowCabinet.type, CSShowCabinet.cabinet_id, gridId, bFraming})
        end
    end
    table.sort(eventList, function(a, b)
        if a == b or a == nil or b == nil or a[1] == b[1] then
            return false
        end
        if a[1] == ECabinetGridDiffType.Add and b[1] == ECabinetGridDiffType.Remove then
            return false
        end
        return true
    end)
    for _, event in ipairs(eventList) do
        local key = GenerateKey(event[2], event[3], event[4])
        self.key2ShelveReqTime[key] = nil
        self.key2WithdrawReqTime[key] = nil
        self.Events.evtCollectionRoomCabinetGridChange:Invoke(table.unpack(event))
    end
end

---@return CabinetItemData
function CollectionRoomServer:GetCabinetItemByGrid(cabinetType, cabinetId, gridId)
    if self.idToCabinetDataMap[cabinetType] then
        if self.idToCabinetDataMap[cabinetType][cabinetId] then
            if self.idToCabinetDataMap[cabinetType][cabinetId].grids[gridId] then
                return self.idToCabinetDataMap[cabinetType][cabinetId].grids[gridId].item
            end
        end
    end
end

---@return boolean
function CollectionRoomServer:IsCabinetUnlocked(cabinetType, cabinetId)
    if cabinetType == EShowCabinetType.Display or cabinetType == EShowCabinetType.Special then
        cabinetId = 1
    end
    if Server.CollectionRoomServer.idToCabinetDataMap[cabinetType] then
        if Server.CollectionRoomServer.idToCabinetDataMap[cabinetType][cabinetId] then
            return Server.CollectionRoomServer.idToCabinetDataMap[cabinetType][cabinetId].unlocked or false
        end
    end
    return false
end

function CollectionRoomServer:GetCabinetGridLevel(cabinetType, cabinetId, gridId)
    if cabinetType == EShowCabinetType.Display or cabinetType == EShowCabinetType.Special then
        cabinetId = 1
    end
    if self.idToCabinetDataMap[cabinetType] then
        if self.idToCabinetDataMap[cabinetType][cabinetId] then
            if self.idToCabinetDataMap[cabinetType][cabinetId].grids[gridId] then
                return self.idToCabinetDataMap[cabinetType][cabinetId].grids[gridId].level or 0
            end
        end
    end
    return 0
end

---@return table<number,CabinetGridData>
function CollectionRoomServer:GetGridDataMap(cabinetType, cabinetId)
    if cabinetType == EShowCabinetType.Display or cabinetType == EShowCabinetType.Special then
        cabinetId = 1
    end
    if Server.CollectionRoomServer.idToCabinetDataMap[cabinetType] then
        if Server.CollectionRoomServer.idToCabinetDataMap[cabinetType][cabinetId] then
            return Server.CollectionRoomServer.idToCabinetDataMap[cabinetType][cabinetId].grids
        end
    end
end

---@return CabinetGridData
function CollectionRoomServer:GetGridData(cabinetType, cabinetId, gridId)
    if cabinetType == EShowCabinetType.Display or cabinetType == EShowCabinetType.Special then
        cabinetId = 1
    end
    if self.idToCabinetDataMap[cabinetType] then
        if self.idToCabinetDataMap[cabinetType][cabinetId] then
            return self.idToCabinetDataMap[cabinetType][cabinetId].grids[gridId]
        end
    end
end

--region 协议请求
---@desc 请求拉取所有展柜数据
function CollectionRoomServer:_ReqGetShowRoom()
    logerror("CollectionRoomServer:_ReqGetShowRoom")
    ---@param res pb_CSDepositGetShowRoomRes
    local fOnCSDepositGetShowRoomRes = function(res)
        logerror("CollectionRoomServer:_ReqGetShowRoom res")
        if res.result ~= 0 then
            logerror(string.format("CollectionRoomServer:_ReqGetShowRoom res.result=%d", res.result))
            return
        end
        self:_UpdateCabinetData(res.display_cabinet, true)
        self:_UpdateCabinetData(res.special_cabinet, true)
        for _, CSShowCabinet in pairs(res.DIY_cabinet) do
            self:_UpdateCabinetData(CSShowCabinet, true)
        end
    end

    if bEnableCollectionRoomDebug then
        self:_ResGetShowRoom(fOnCSDepositGetShowRoomRes)
        return
    end

    ---@type pb_CSDepositGetShowRoomReq
    local req = pb.CSDepositGetShowRoomReq:New()
    req:Request(fOnCSDepositGetShowRoomRes, {bEnableHighFrequency = true})
end

---@desc 请求上架指定物品到指定展柜的指定格子
function CollectionRoomServer:_ReqShelveShowCabinet(cabinetType, cabinetId, gridId, itemGid)
    loginfo("CollectionRoomServer:_ReqShelveShowCabinet", cabinetType, cabinetId, gridId, itemGid)
    local item = Server.InventoryServer:GetItemByGid(itemGid)
    if not item then
        logerror("CollectionRoomServer:_ReqShelveShowCabinet no item found for gid=", itemGid)
        return
    end

    local cabinetItem = self:GetCabinetItemByGrid(cabinetType, cabinetId, gridId)
    if cabinetItem and cabinetItem.gid == itemGid then
        loginfo("CollectionRoomServer:_ReqShelveShowCabinet item is already shelved for gid=", itemGid)
        return
    end

    local nowTime = TimeUtil.GetCurrentTimeMillis()
    local key = GenerateKey(cabinetType, cabinetId, gridId)
    if self.key2ShelveReqTime[key] and nowTime < self.key2ShelveReqTime[key] + REQ_MIN_GAP_TIME then
        loginfo("CollectionRoomServer:_ReqShelveShowCabinet blocked because req gap time")
        return
    end
    self.key2ShelveReqTime[key] = TimeUtil.GetCurrentTimeMillis()

    ---@param res pb_CSDepositShelveShowCabinetRes
    local fOnCSDepositShelveShowCabinetRes = function(res)
        loginfo("CollectionRoomServer:_ReqShelveShowCabinet res")
        if res.result ~= 0 then
            logerror(string.format("CollectionRoomServer:_ReqShelveShowCabinet res.result=%d", res.result))
            return
        end
        if res.deposit_change then
            Server.InventoryServer:ProcessPbDataChange(res.deposit_change, true, false, false)
        end
        self:_UpdateCabinetData(res.cabinet)
    end

    ItemOperaTool.PlayLootingDropAudio(item.id)

    if bEnableCollectionRoomDebug then
        self:_ResShelveShowCabinet(fOnCSDepositShelveShowCabinetRes, cabinetType, cabinetId, gridId, itemGid)
        return
    end
    ---@type pb_CSDepositShelveShowCabinetReq
    local req = pb.CSDepositShelveShowCabinetReq:New()
    req.type = cabinetType
    req.cabinet_id = cabinetId
    req.grid_id = gridId
    req.prop_gid = itemGid
    req.prop_pos = item.InSlot.SlotType
    req.prop_id = item.id
    req:Request(fOnCSDepositShelveShowCabinetRes, {bEnableHighFrequency = true})
end

---@desc 请求下架指定展柜的指定格子的物品到仓库/扩容箱的指定位置
---@param itemLocation ItemLocation
function CollectionRoomServer:_ReqWithdrawShowCabinet(cabinetType, cabinetId, gridId, itemLocation)
    if itemLocation then
        if itemLocation.ItemSlot then
            loginfo("CollectionRoomServer:_ReqWithdrawShowCabinet", cabinetType, cabinetId, gridId, itemLocation.ItemSlot:GetSlotGroup(), itemLocation.ItemSlot.SlotType, itemLocation.SubIndex, itemLocation.X, itemLocation.Y)
        else
            loginfo("CollectionRoomServer:_ReqWithdrawShowCabinet", cabinetType, cabinetId, gridId, itemLocation.SubIndex, itemLocation.X, itemLocation.Y)
        end
    else
        loginfo("CollectionRoomServer:_ReqWithdrawShowCabinet", cabinetType, cabinetId, gridId)
    end
    if not itemLocation then
        return
    end
    if not self.idToCabinetDataMap[cabinetType] then
        return
    end
    if not self.idToCabinetDataMap[cabinetType][cabinetId] then
        return
    end
    if not self.idToCabinetDataMap[cabinetType][cabinetId].grids[gridId] then
        return
    end

    local nowTime = TimeUtil.GetCurrentTimeMillis()
    local key = GenerateKey(cabinetType, cabinetId, gridId)
    if self.key2WithdrawReqTime[key] and nowTime < self.key2WithdrawReqTime[key] + REQ_MIN_GAP_TIME then
        loginfo("CollectionRoomServer:_ReqWithdrawShowCabinet blocked because req gap time")
        return
    end
    self.key2WithdrawReqTime[key] = TimeUtil.GetCurrentTimeMillis()

    ---@param res pb_CSDepositWithdrawShowCabinetRes
    local fOnCSDepositWithdrawShowCabinetRes = function(res)
        loginfo("CollectionRoomServer:_ReqWithdrawShowCabinet res")
        if res.result ~= 0 then
            logerror(string.format("CollectionRoomServer:_ReqWithdrawShowCabinet res.result=%d", res.result))
            return
        end
        if res.deposit_change then
            Server.InventoryServer:ProcessPbDataChange(res.deposit_change, true, false, false)
        end
        self:_UpdateCabinetData(res.cabinet)
    end

    if bEnableCollectionRoomDebug then
        self:_ResWithdrawShowCabinet(fOnCSDepositWithdrawShowCabinetRes, cabinetType, cabinetId, gridId)
        return
    end

    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIClick)

    local cabinetData = self.idToCabinetDataMap[cabinetType][cabinetId]
    ---@type pb_CSDepositWithdrawShowCabinetReq
    local req = pb.CSDepositWithdrawShowCabinetReq:New()
    req.type = cabinetData.type
    req.cabinet_id = cabinetId
    req.grid_id = gridId
    req.dest_pos_id = itemLocation.ItemSlot.SlotType
    req.location = itemLocation:ToPbLocation()
    req:Request(fOnCSDepositWithdrawShowCabinetRes, {bEnableHighFrequency = true})
end

---@desc 请求移动指定展柜中的指定物品到同一展柜的指定格子
function CollectionRoomServer:_ReqMoveShowCabinet(cabinetType, cabinetId, srcGridId, destGridId)
    loginfo("CollectionRoomServer:_ReqMoveShowCabinet", cabinetType, cabinetId, srcGridId, destGridId)
    if not self.idToCabinetDataMap[cabinetType] then
        return
    end
    if not self.idToCabinetDataMap[cabinetType][cabinetId] then
        return
    end
    if not self.idToCabinetDataMap[cabinetType][cabinetId].grids[srcGridId] then
        return
    end
    if not self.idToCabinetDataMap[cabinetType][cabinetId].grids[destGridId] then
        return
    end

    local srcGrid = self.idToCabinetDataMap[cabinetType][cabinetId].grids[srcGridId]
    if srcGrid and srcGrid.item and srcGrid.item.id then
        ItemOperaTool.PlayLootingDropAudio(srcGrid.item.id)
    end

    ---@param res pb_CSDepositMoveShowCabinetRes
    local fOnCSDepositMoveShowCabinetRes = function(res)
        loginfo("CollectionRoomServer:_ReqMoveShowCabinet res")
        if res.result ~= 0 then
            logerror(string.format("CollectionRoomServer:_ReqMoveShowCabinet res.result=%d", res.result))
            return
        end
        self:_UpdateCabinetData(res.cabinet)
    end

    if bEnableCollectionRoomDebug then
        self:_ResMoveShowCabinet(fOnCSDepositMoveShowCabinetRes, cabinetType, cabinetId, srcGridId, destGridId)
        return
    end

    local cabinetData = self.idToCabinetDataMap[cabinetType][cabinetId]
    ---@type pb_CSDepositMoveShowCabinetReq
    local req = pb.CSDepositMoveShowCabinetReq:New()
    req.type = cabinetData.type
    req.cabinet_id = cabinetId
    req.src_grid_id = srcGridId
    req.dest_grid_id = destGridId
    req:Request(fOnCSDepositMoveShowCabinetRes, {bEnableHighFrequency = true})
end

---@desc 请求升级指定展柜的指定格子
function CollectionRoomServer:_ReqLevelUpShowCabinet(cabinetType, cabinetId, gridId, itemInfoList)
    loginfo("CollectionRoomServer:_ReqLevelUpShowCabinet", cabinetType, cabinetId, gridId, itemInfoList and #itemInfoList or 0)
    if not self.idToCabinetDataMap[cabinetType] then
        return
    end
    if not self.idToCabinetDataMap[cabinetType][cabinetId] then
        return
    end
    local cabinetData = self.idToCabinetDataMap[cabinetType][cabinetId]
    if not cabinetData.grids[gridId] then
        return
    end
    ---@param res pb_CSDepositLevelUpShowCabinetRes
    local fOnCSDepositLevelUpShowCabinetRes = function(res)
        loginfo("CollectionRoomServer:_ReqLevelUpShowCabinet res")
        if res.result ~= 0 then
            logerror(string.format("CollectionRoomServer:_ReqLevelUpShowCabinet res.result=%d", res.result))
            return
        end
        if res.deposit_change then
            Server.InventoryServer:ProcessPbDataChange(res.deposit_change, true, false, false)
        end
        self:_UpdateCabinetData(res.cabinet)
    end

    if bEnableCollectionRoomDebug then
        self:_ResLevelUpShowCabinet(fOnCSDepositLevelUpShowCabinetRes, cabinetType, cabinetId, gridId)
        return
    end

    ---@type pb_CSDepositLevelUpShowCabinetReq
    local req = pb.CSDepositLevelUpShowCabinetReq:New()
    req.type = cabinetData.type
    req.cabinet_id = cabinetId
    req.grid_id = gridId
    req.cost_prop_list = itemInfoList
    req:Request(fOnCSDepositLevelUpShowCabinetRes, {bEnableHighFrequency = true})
end

---@desc 交换仓库藏品和DIY展位藏品
function CollectionRoomServer:_ReqExchangeShowCabinet(cabinetType, cabinetId, gridId, sourceItemInfo)
    ---@type pb_CSDepositSwapDIYCabinetReq

    ---@param res pb_CSDepositSwapDIYCabinetRes
    local fOnCSDepositSwapDIYCabinetRes = function(res)
        if res.result ~= 0 then
            logerror(string.format("CollectionRoomServer:_ReqExchangeShowCabinet res.result=%d", res.result))
            return
        end
        if res.deposit_change then
            Server.InventoryServer:ProcessPbDataChange(res.deposit_change, true, false, false)
        end
        self:_UpdateCabinetData(res.cabinet)
    end

    local req = pb.CSDepositSwapDIYCabinetReq:New()
    req.cabinet_id = cabinetId
    req.grid_id = gridId
    req.prop_gid = sourceItemInfo.gid
    req.prop_pos = sourceItemInfo.ItemLocation
    req.prop_id = sourceItemInfo.id
    req.rotate = sourceItemInfo.bRotate
    req:Request(fOnCSDepositSwapDIYCabinetRes)
end

--endregion 协议请求

--region for debug

function CollectionRoomServer:_ResGetShowRoom(callback)
    local globalItemGid = 1
    CollectionRoomServerConfig.PlaceActorToCabinetAndGridId = {}
    local function FakeCabinetGridData(cabinetType, cabinetId, gridId)
        ---@type pb_CSShowGrid
        local grid = {}
        grid.grid_id = gridId
        grid.item = {}
        grid.item.id = CollectionRoomServerConfig.CollectionItemIds[cabinetType][cabinetId][gridId] or 0
        grid.item.gid = grid.item.id and globalItemGid or 0
        grid.level = 2
        table.insert(CollectionRoomServerConfig.PlaceActorToCabinetAndGridId, {cabinetType, cabinetId, gridId})
        globalItemGid = globalItemGid + 1
        return grid
    end

    ---@type pb_CSDepositGetShowRoomRes
    local res = {}
    res.result = 0
    ---@type pb_CSShowCabinet
    res.display_cabinet = {}
    res.display_cabinet.type = EShowCabinetType.Display
    res.display_cabinet.cabinet_id = 1
    res.display_cabinet.price = 123456
    res.display_cabinet.grids = {}
    for i = 1,#CollectionRoomServerConfig.CollectionItemIds[EShowCabinetType.Display][1] do
        res.display_cabinet.grids[i] = FakeCabinetGridData(EShowCabinetType.Display, 1, i)
    end
    ---@type pb_CSShowCabinet
    res.special_cabinet = {}
    res.special_cabinet.type = EShowCabinetType.Special
    res.special_cabinet.cabinet_id = 1
    res.special_cabinet.price = 789456
    res.special_cabinet.grids = {}
    for i = 1,#CollectionRoomServerConfig.CollectionItemIds[EShowCabinetType.Special][1] do
        res.special_cabinet.grids[i] = FakeCabinetGridData(EShowCabinetType.Special, 1, i)
    end
    res.DIY_cabinet = {}
    for i = 1,1 do
        ---@type pb_CSShowCabinet
        res.DIY_cabinet[i] = {}
        res.DIY_cabinet[i].type = EShowCabinetType.DIY
        res.DIY_cabinet[i].cabinet_id = i
        res.DIY_cabinet[i].price = 459786
        res.DIY_cabinet[i].grids = {}
        for j = 1,#CollectionRoomServerConfig.CollectionItemIds[EShowCabinetType.DIY][i] do
            res.DIY_cabinet[i].grids[j] = FakeCabinetGridData(EShowCabinetType.DIY, i, j)
        end
    end

    callback(res)
end

function CollectionRoomServer:_ResShelveShowCabinet(callback, cabinetType, cabinetId, gridId, itemGid)
    ---@type pb_CSDepositShelveShowCabinetRes
    local res = {}
    res.result = 0
    local item = Server.InventoryServer:GetItemByGid(itemGid)
    if not item then
        callback(res)
        return
    end
    if not self.idToCabinetDataMap[cabinetType] then
        callback(res)
        return
    end
    if not self.idToCabinetDataMap[cabinetType][cabinetId] then
        callback(res)
        return
    end
    local cabinetData = self.idToCabinetDataMap[cabinetType][cabinetId]

    res.cabinet = {}
    res.cabinet.type = cabinetData.type
    res.cabinet.cabinet_id = cabinetId
    res.cabinet.locked = true
    res.cabinet.price = 13141314
    res.cabinet.grids = {}
    for _, cabinetGridData in pairs(cabinetData.grids) do
        ---@type pb_CSShowGrid
        local CSShowGrid = {}
        local gridId = cabinetGridData.id
        CSShowGrid.item = CSShowGrid.item and CSShowGrid.item or {}
        CSShowGrid.grid_id = cabinetGridData.id
        CSShowGrid.level = cabinetGridData.level
        CSShowGrid.item.id = cabinetGridData.item and cabinetGridData.item.id or 0
        CSShowGrid.item.gid = cabinetGridData.item and cabinetGridData.item.gid or 0
        if gridId == cabinetGridData.id then
            CSShowGrid.item.id = item.id
            CSShowGrid.item.gid = item.gid
        end
        table.insert(res.cabinet.grids, CSShowGrid)
    end

    if not cabinetData.grids or not cabinetData.grids[gridId] then
        local addGrid = {}
        addGrid.item = addGrid.item and addGrid.item or {}
        addGrid.grid_id = gridId
        addGrid.item.id = item.id
        addGrid.item.gid = itemGid
        table.insert(res.cabinet.grids, addGrid)
    end
    callback(res)
end

function CollectionRoomServer:_ResWithdrawShowCabinet(callback, cabinetType, cabinetId, gridId)
    ---@type pb_CSDepositWithdrawShowCabinetRes
    local res = {}
    res.result = 1
    if not self.idToCabinetDataMap[cabinetType] then
        callback(res)
        return
    end
    if not self.idToCabinetDataMap[cabinetType][cabinetId] then
        callback(res)
        return
    end
    local cabinetData = self.idToCabinetDataMap[cabinetType][cabinetId]
    if not cabinetData.grids or not cabinetData.grids[gridId] then
        callback(res)
        return
    end

    res.cabinet = {}
    res.cabinet.type = cabinetData.type
    res.cabinet.grids = {}
    for _, cabinetGridData in pairs(cabinetData.grids) do
        ---@type pb_CSShowGrid
        local CSShowGrid = {}
        local gridId = cabinetGridData.id
        CSShowGrid.grid_id = cabinetGridData.id
        CSShowGrid.level = cabinetGridData.level
        CSShowGrid.item.id = cabinetGridData.item and cabinetGridData.item.id or 0
        CSShowGrid.item.gid = cabinetGridData.item and cabinetGridData.item.gid or 0
        if gridId == cabinetGridData.id then
            CSShowGrid.item.id = 0
            CSShowGrid.item.gid = 0
        end
        table.insert(res.cabinet.grids, CSShowGrid)
    end

    callback(res)
end

function CollectionRoomServer:_ResMoveShowCabinet(callback, cabinetType, cabinetId, srcGridId, destGridId)
    ---@type pb_CSDepositMoveShowCabinetRes
    local res = {}
    res.result = 1
    if not self.idToCabinetDataMap[cabinetType] then
        callback(res)
        return
    end
    if not self.idToCabinetDataMap[cabinetType][cabinetId] then
        callback(res)
        return
    end
    local cabinetData = self.idToCabinetDataMap[cabinetType][cabinetId]
    if not cabinetData.grids or not cabinetData.grids[srcGridId] or not cabinetData.grids[destGridId] then
        callback(res)
        return
    end

    local srcGrid = cabinetData.grids[srcGridId]
    local destGrid = cabinetData.grids[destGridId]

    res.cabinet = {}
    res.cabinet.type = cabinetData.type
    res.cabinet.grids = {}
    for _, cabinetGridData in pairs(cabinetData.grids) do
        ---@type pb_CSShowGrid
        local CSShowGrid = {}
        local gridId = cabinetGridData.id
        CSShowGrid.grid_id = cabinetGridData.id
        CSShowGrid.level = cabinetGridData.level
        CSShowGrid.item.id = cabinetGridData.item and cabinetGridData.item.id or 0
        CSShowGrid.item.gid = cabinetGridData.item and cabinetGridData.item.gid or 0
        if gridId == srcGridId then
            CSShowGrid.item.id = destGrid.item and destGrid.item.id or 0
            CSShowGrid.item.gid = destGrid.item and destGrid.item.gid or 0
        end
        if gridId == destGridId then
            CSShowGrid.item.id = srcGrid.item and srcGrid.item.id or 0
            CSShowGrid.item.gid = srcGrid.item and srcGrid.item.gid or 0
        end
        table.insert(res.cabinet.grids, CSShowGrid)
    end

    callback(res)
end

function CollectionRoomServer:_ResLevelUpShowCabinet(callback, cabinetType, cabinetId, gridId)
    ---@type pb_CSDepositLevelUpShowCabinetRes
    local res = {}
    res.result = 1
    if not self.idToCabinetDataMap[cabinetType] then
        callback(res)
        return
    end
    if not self.idToCabinetDataMap[cabinetType][cabinetId] then
        callback(res)
        return
    end
    local cabinetData = self.idToCabinetDataMap[cabinetType][cabinetId]
    if not cabinetData.grids or not cabinetData.grids[gridId] then
        callback(res)
        return
    end

    res.cabinet = {}
    res.cabinet.type = cabinetData.type
    res.cabinet.grids = {}
    for _, cabinetGridData in pairs(cabinetData.grids) do
        ---@type pb_CSShowGrid
        local CSShowGrid = {}
        local gridId = cabinetGridData.id
        CSShowGrid.grid_id = cabinetGridData.id
        CSShowGrid.level = cabinetGridData.level
        CSShowGrid.item.id = cabinetGridData.item and cabinetGridData.item.id or 0
        CSShowGrid.item.gid = cabinetGridData.item and cabinetGridData.item.gid or 0
        if gridId == gridId then
            CSShowGrid.level = cabinetGridData.level + 1
        end
        table.insert(res.cabinet.grids, CSShowGrid)
    end

    callback(res)
end

--endregion

-----------------------------------------------------------------------
--region Public FUNC

---@desc 请求上架指定物品到指定展柜的指定格子
function CollectionRoomServer:ShelveShowCabinet(cabinetType, cabinetId, gridId, itemGid)
    self:_ReqShelveShowCabinet(cabinetType, cabinetId, gridId, itemGid)
end

---@desc 请求移动指定展柜中的指定物品到同一展柜的指定格子
function CollectionRoomServer:MoveShowCabinet(cabinetType, cabinetId, srcGridId, destGridId)
    self:_ReqMoveShowCabinet(cabinetType, cabinetId, srcGridId, destGridId)
end

---@desc 请求下架指定展柜的指定格子的物品到仓库/扩容箱的指定位置
---@param itemLocation ItemLocation
function CollectionRoomServer:WithdrawShowCabinet(cabinetType, cabinetId, gridId, itemLocation)
    self:_ReqWithdrawShowCabinet(cabinetType, cabinetId, gridId, itemLocation)
end

---@desc 请求升级指定展柜的指定格子
function CollectionRoomServer:LevelUpShowCabinet(cabinetType, cabinetId, gridId, itemInfoList)
    self:_ReqLevelUpShowCabinet(cabinetType, cabinetId, gridId, itemInfoList)
end

---@desc 仓库藏品与DIY展位藏品进行交换
function CollectionRoomServer:ExchangeShowCabinet(cabinetType, cabinetId, gridId, sourceItem, targetItem)
    -- 执行交换逻辑所需要的参数
    local itemInfo = {}
    itemInfo.id = sourceItem.id
    itemInfo.gid = sourceItem.gid
    itemInfo.ItemLocation = sourceItem and sourceItem.InSlot and sourceItem.InSlot.SlotType

    local targetLen = targetItem.width
    local targetWidth = targetItem.length
    if targetItem.length <= sourceItem.length and targetItem.width <= sourceItem.width then
        itemInfo.bRotate = false
    elseif targetLen <= sourceItem.length and targetWidth <= sourceItem.width then
        itemInfo.bRotate = true
    end

    self:_ReqExchangeShowCabinet(cabinetType, cabinetId, gridId, itemInfo)
end

function CollectionRoomServer:GetDIYCabinetPrice(cabinetID)
    if self.idToCabinetDataMap[EShowCabinetType.DIY] and self.idToCabinetDataMap[EShowCabinetType.DIY][cabinetID] then
        return self.idToCabinetDataMap[EShowCabinetType.DIY][cabinetID].price
    end
end

function CollectionRoomServer:GetDisplayCabinetPrice(cabinetID)
    local price = 0
    if self.idToCabinetDataMap[EShowCabinetType.Display] and self.idToCabinetDataMap[EShowCabinetType.Display][cabinetID] then
        return self.idToCabinetDataMap[EShowCabinetType.Display][cabinetID].price or 0
    end
    return price
end

function CollectionRoomServer:GetSpecialCabinetPrice(cabinetID)
    if self.idToCabinetDataMap[EShowCabinetType.Special] and self.idToCabinetDataMap[EShowCabinetType.Special][cabinetID] then
        return self.idToCabinetDataMap[EShowCabinetType.Special][cabinetID].price or 0
    end
    return 0
end

-- 判断是否有道具可上架展柜
function CollectionRoomServer:GetCollectionItemInfo()
   return Facade.TableManager:GetTable("CollectionRoomCollection")
end

--endregion
-----------------------------------------------------------------------

return CollectionRoomServer