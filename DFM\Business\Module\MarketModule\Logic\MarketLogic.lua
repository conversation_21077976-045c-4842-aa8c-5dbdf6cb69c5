----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMMarket)
----- LOG FUNCTION AUTO GENERATE END -----------



local MarketLogic = {}
local MarketConfig  = require "DFM.Business.Module.MarketModule.MarketConfig"
local ShopHelperTool = require "DFM.StandaloneLua.BusinessTool.ShopHelperTool"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local ItemBaseTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemBaseTool"
---------------------------------------------------------------------------------
--- Logic可以拆分多个，用于业务逻辑的编写，部分需要开放的接口由Module进行转发
---------------------------------------------------------------------------------
--- 可以仅模块内部调用，也可以在Module中被公开
MarketLogic.DoSomeThingProcess = function(...)
    -- TODO 业务逻辑、弹窗Tips表现、发送Server Req等等一系列事情
    -- Server.MarketServer:DoSomeThingReq(...)
    -- return 
end

MarketLogic.ShowMainPanelProcess = function(subPageType)
    --- 异步打开配置好lua脚本的UI
    ---@param UINavID number 来自于UITable里配置的UIName2ID
    ---@param fLoadFinCallback function
    ---@param caller table
    ---@param ... args 调用传入UIIns:OnInitExtraData(...)

    -- 1.local 写法
    -- local OnCreateCallback = function(self, uiIns)
    --     Module.Market.Field:SetMainPanel(uiIns)
    -- end
    -- Facade.UIManager:AsyncShowUI(UIName2ID.MarketMainPanel, OnCreateCallback, nil, ...)

    -- 2.外部函数写法
    -- Facade.UIManager:AsyncShowUI(UIName2ID.MarketMainPanel, MarketLogic.OnMainPanelCreateFinished, nil, ...)
    -- return 
    if not MarketLogic.MarketExternalLockedProcess() then
        return
    end
    Module.ModuleSwitcher:ModuleOpenByCheck(SwitchSystemID.SwitchSystemMarket, UIName2ID.MarketMainPanel, nil, nil, nil, subPageType)
    --Facade.UIManager:AsyncShowUI(UIName2ID.MarketMainPanel, nil, nil, subPageType)
end

MarketLogic.OnMainPanelCreateFinished = function(uiIns)
    Module.Market.Field:SetMainPanel(uiIns)
end

MarketLogic.CloseMainPanelProcess = function()
    -- local mainPanel = Module.Market.Field:GetMainPanel()
    -- if mainPanel then
    --     Facade.UIManager:CloseUI(mainPanel)
    --     Module.Market.Field:SetMainPanel(nil)
    -- end
end

MarketLogic.MarketExternalLockedProcess = function()
    local marketModuleUnlockInfo = Module.ModuleUnlock:GetModuleUnlockInfoById(SwitchModuleID.ModuleMarket)
    if marketModuleUnlockInfo and not marketModuleUnlockInfo.bIsUnlocked then
        Module.CommonTips:ShowSimpleTip(marketModuleUnlockInfo.unlocktips)
        return false
    end
    if not Server.MarketServer:GetMarketIsOpen() then
        Module.CommonTips:ShowSimpleTip(MarketConfig.Loc.MarketIsNotOpenByVersion)
        return false
    end
    return true
end

MarketLogic.MarketInternalLockedProcess = function(mysticalInsItem, bShowTips)
    if mysticalInsItem then
        bShowTips = setdefault(bShowTips, true)
        if Server.MarketServer:GetMarketItemType(mysticalInsItem.id) == EMarketSubPageType.MysticalSkin then
            local remainTime = MarketLogic.GetMysticalSkinInsUnlockRemainTime(mysticalInsItem)
            if remainTime > 0 then
                if bShowTips then
                    local day, hour, min, sec = TimeUtil.GetSecondsFormatDDHHMMSS(remainTime)
                    if day == 0 and hour == 0 and min == 0 and sec > 0 then
                        Module.CommonTips:ShowSimpleTip(string.format(MarketConfig.Loc.SellUnlockCdSec, sec))
                    elseif day == 0 and hour == 0 and min > 0 then
                        Module.CommonTips:ShowSimpleTip(string.format(MarketConfig.Loc.SellUnlockCdMin, min))
                    elseif day == 0 and hour > 0 then
                        Module.CommonTips:ShowSimpleTip(string.format(MarketConfig.Loc.SellUnlockCdHour, hour))
                    elseif day > 0 then
                        Module.CommonTips:ShowSimpleTip(string.format(MarketConfig.Loc.SellUnlockCdDay, day))
                    end
                end
                return false
            end
        elseif Server.MarketServer:GetMarketItemType(mysticalInsItem.id) == EMarketSubPageType.MysticalPendant then
            if MarketLogic.GetmysticalPendantInsIsBanTrade(mysticalInsItem) then
                if bShowTips then
                    Module.CommonTips:ShowSimpleTip(MarketConfig.Loc.MysticalPendantBanTradeDes)
                end
                return false
            end
        end
    end
    return true
end

MarketLogic.GetMysticalInsIsSaleLocked = function(mysticalInsItem)
    if mysticalInsItem then
        if Server.MarketServer:GetMarketItemType(mysticalInsItem.id) == EMarketSubPageType.MysticalSkin then
            return MarketLogic.GetMysticalSkinInsUnlockRemainTime(mysticalInsItem) > 0
        elseif Server.MarketServer:GetMarketItemType(mysticalInsItem.id) == EMarketSubPageType.MysticalPendant then
            return MarketLogic.GetmysticalPendantInsIsBanTrade(mysticalInsItem)
        end
    end
    return false
end

MarketLogic.GetMysticalSkinInsUnlockRemainTime = function(mysticalSkinInsItem)
    local rawPropInfo = mysticalSkinInsItem:GetRawPropInfo()
    local mysticalSkinInfo = rawPropInfo and mysticalSkinInsItem.GetWeaponMysticalSkinInfoFromPropInfo(rawPropInfo) or nil
    local mysticalSkinBuyTime = mysticalSkinInfo and mysticalSkinInfo.market_buy_time or nil
    local sellAfterBuyCd = Server.MarketServer:GetSellAfterBuyCd()
    return mysticalSkinBuyTime and (mysticalSkinBuyTime + sellAfterBuyCd - Facade.ClockManager:GetLocalTimestamp()) or 0
end

MarketLogic.GetmysticalPendantInsIsBanTrade = function(mysticalPendantInsItem)
    if mysticalPendantInsItem then
        if Server.MarketServer:GetMarketItemType(mysticalPendantInsItem.id) == EMarketSubPageType.MysticalPendant then
            local rawPropInfo = mysticalPendantInsItem:GetRawPropInfo()
            local mysticalPendantInfo = rawPropInfo and mysticalPendantInsItem.GetWeaponMysticalPendantInfoFromPropInfo(rawPropInfo) or nil
            local mysticalPendantBuyTime = mysticalPendantInfo and mysticalPendantInfo.market_buy_time or nil
            return mysticalPendantBuyTime > 0
        else
            return false
        end
    else
        return false
    end
end

-- 数据点处理算法目标：
-- Part1：将数据点按价格等区间分割，区间左闭右开，末区间左闭右闭，总体闭合
-- Part2：在划分好的区间左右加上假数据点，用于边界处理，目前处理为水平消灭
MarketLogic.dataPointsProcessing = function(dataPoints, intervalNum, bFrontAdapt, bBackAdapt)
    if dataPoints and not table.isempty(dataPoints) then
        -- part1
        -- 合并相同价格的数据
        local mergeDataPoints = {}
        for _, v in ipairs(dataPoints) do
            if mergeDataPoints[v[1]] then
                mergeDataPoints[v[1]] = mergeDataPoints[v[1]] + v[2]
            else
                mergeDataPoints[v[1]] = v[2]
            end
        end
        dataPoints = {}
        for k, v in pairs(mergeDataPoints) do
            table.insert(dataPoints, {k, v})
        end
        table.sort(dataPoints, function(a, b) return a[1] < b[1] end)
        -- 按价格等区间分割
        local splitDataPoints = {}
        if #dataPoints == 1 then
            table.insert(splitDataPoints, dataPoints[1])
        else
            local priceRange = dataPoints[#dataPoints][1] - dataPoints[1][1] -- 价格范围
            local totalCount = 0 -- 原始总数量
            for _, v in ipairs(dataPoints) do
                totalCount = totalCount + v[2]
            end
            intervalNum = setdefault(intervalNum, 5)
            local realIntervalNum = priceRange < intervalNum and priceRange or intervalNum -- 真实间隔数
            local interval = math.floor(priceRange / realIntervalNum) -- 间隔量，向下取整
            local curTotalCount = 0 -- 已归纳的总数量
            for i = 1, realIntervalNum do
                local price = dataPoints[1][1] + (i - 1) * interval
                local intervalCount = 0 -- 间隔区间内的数量
                if i == realIntervalNum then -- 检索最后一个区间[price, price + interval]（最后一个区间可能同时是第一个区间）
                    for k, v in ipairs(dataPoints) do
                        if v[1] >= price and v[1] <= price + interval then
                            intervalCount = intervalCount + v[2]
                            curTotalCount = curTotalCount + v[2]
                        end
                    end
                    if curTotalCount < totalCount then
                        intervalCount = intervalCount + (totalCount - curTotalCount)
                        curTotalCount = totalCount
                    end
                    table.insert(splitDataPoints, {dataPoints[#dataPoints][1], intervalCount})
                elseif i == 1 then -- 检索第一个区间[price, price + interval)
                    for k, v in ipairs(dataPoints) do
                        if v[1] >= price and v[1] < price + interval then
                            intervalCount = intervalCount + v[2]
                            curTotalCount = curTotalCount + v[2]
                        end
                    end
                    table.insert(splitDataPoints, {price, 0})
                    table.insert(splitDataPoints, {price + interval, intervalCount})
                else -- 检索非第一个区间和非最后一个区间[price, price + interval)
                    for k, v in ipairs(dataPoints) do
                        if v[1] >= price and v[1] < price + interval then
                            intervalCount = intervalCount + v[2]
                            curTotalCount = curTotalCount + v[2]
                        end
                    end
                    table.insert(splitDataPoints, {price + interval, intervalCount})
                end
            end
        end
        -- 计算最大最小Y值
        local min = splitDataPoints[1][2]
        local max = splitDataPoints[1][2]
        if #splitDataPoints ~= 1 then
            -- 需剔除第一个点，其Y轴数据为造假
            min = splitDataPoints[2][2]
            max = splitDataPoints[2][2]
            for k, v in ipairs(splitDataPoints) do
                if k ~= 1 then
                    min = math.min(min, v[2])
                    max = math.max(max, v[2])
                end
            end
        end
        -- Part2
        local fakeDataPointsPos = {}
        local splitPriceRange = splitDataPoints[#splitDataPoints][1] - dataPoints[1][1]
        if #splitDataPoints == 1 then
            splitDataPoints = {{0, splitDataPoints[1][2]}, {splitDataPoints[1][1], splitDataPoints[1][2]}}
            fakeDataPointsPos = {1}
        elseif #splitDataPoints > 1 and splitPriceRange < intervalNum then
            splitDataPoints[1][2] = splitDataPoints[2][2]
            fakeDataPointsPos = {1}
        else
            splitDataPoints[1][2] = splitDataPoints[2][2]
            local newDataPointEnd = {2 * splitDataPoints[#splitDataPoints][1] - splitDataPoints[#splitDataPoints - 1][1], splitDataPoints[#splitDataPoints][2]}
            table.insert(splitDataPoints, newDataPointEnd)
            fakeDataPointsPos = {1, #splitDataPoints}
        end

        -- 另一种斜率消灭的前后造假方式
        -- bFrontAdapt = setdefault(bFrontAdapt, false)
        -- bBackAdapt = setdefault(bBackAdapt, false)
        -- if #splitDataPoints == 1 then -- 只造首位假数据
        --     if bFrontAdapt then
        --         splitDataPoints = {splitDataPoints[1], splitDataPoints[1]}
        --     end
        -- elseif #splitDataPoints > 1 and #splitDataPoints < intervalNum then -- 只造首位假数据
        --     if bFrontAdapt then
        --         local newDataPointFront = {2 * splitDataPoints[1][1] - splitDataPoints[2][1],
        --         2 * splitDataPoints[1][2] - splitDataPoints[2][2]}
        --         table.insert(splitDataPoints, 1, newDataPointFront)
        --     end
        -- else
        --     if bFrontAdapt then
        --         local newDataPointFront = {2 * splitDataPoints[1][1] - splitDataPoints[2][1], 2 * splitDataPoints[1][2] - splitDataPoints[2][2]}
        --         table.insert(splitDataPoints, 1, newDataPointFront)
        --     end
        --     if bBackAdapt then
        --         local newDataPointEnd = {2 * splitDataPoints[#splitDataPoints][1] - splitDataPoints[#splitDataPoints - 1][1],
        --         2 * splitDataPoints[#splitDataPoints][2] - splitDataPoints[#splitDataPoints - 1][2]}
        --         table.insert(splitDataPoints, newDataPointEnd)
        --     end
        -- end
        return splitDataPoints, min, max, fakeDataPointsPos
    else
        return dataPoints
    end
end

MarketLogic.dataPointsProcessingForBarChar = function(dataPoints, intervalNum)
    if dataPoints and not table.isempty(dataPoints) then
        -- 合并相同价格的数据
        local mergeDataPoints = {}
        for _, v in ipairs(dataPoints) do
            if mergeDataPoints[v[1]] then
                mergeDataPoints[v[1]] = mergeDataPoints[v[1]] + v[2]
            else
                mergeDataPoints[v[1]] = v[2]
            end
        end
        dataPoints = {}
        for k, v in pairs(mergeDataPoints) do
            table.insert(dataPoints, {k, v})
        end
        table.sort(dataPoints, function(a, b) return a[1] < b[1] end)
        -- 按价格等区间分割
        local splitDataPoints = {}
        if #dataPoints == 1 then
            table.insert(splitDataPoints, {dataPoints[1][1], dataPoints[1][2], dataPoints[1][1]})
        else
            local priceRange = dataPoints[#dataPoints][1] - dataPoints[1][1] -- 价格范围
            local totalCount = 0 -- 原始总数量
            for _, v in ipairs(dataPoints) do
                totalCount = totalCount + v[2]
            end
            intervalNum = setdefault(intervalNum, 5)
            local realIntervalNum = priceRange < intervalNum and priceRange or intervalNum -- 真实间隔数
            local interval = math.floor(priceRange / realIntervalNum) -- 间隔量，向下取整
            local curTotalCount = 0 -- 已归纳的总数量
            for i = 1, realIntervalNum do
                local intervalCount = 0 -- 间隔区间内的数量
                local curLeftBorder = dataPoints[1][1] + (i - 1) * interval
                local curRightBorder = dataPoints[1][1] + i * interval
                if i == realIntervalNum then -- 检索最后一个区间[price, price + interval]（最后一个区间可能同时是第一个区间）
                    for k, v in ipairs(dataPoints) do
                        if v[1] >= curLeftBorder and v[1] <= curRightBorder then
                            intervalCount = intervalCount + v[2]
                            curTotalCount = curTotalCount + v[2]
                        end
                    end
                    if curTotalCount < totalCount then
                        intervalCount = intervalCount + (totalCount - curTotalCount)
                        curTotalCount = totalCount
                    end
                    table.insert(splitDataPoints, {curLeftBorder, intervalCount, dataPoints[#dataPoints][1]})
                else -- 检索非最后一个区间[price, price + interval)
                    for k, v in ipairs(dataPoints) do
                        if v[1] >= curLeftBorder and v[1] < curRightBorder then
                            intervalCount = intervalCount + v[2]
                            curTotalCount = curTotalCount + v[2]
                        end
                    end
                    table.insert(splitDataPoints, {curLeftBorder, intervalCount, curRightBorder})
                end
            end
        end
        -- 计算最大最小Y值
        local min = splitDataPoints[1][2]
        local max = splitDataPoints[1][2]
        for k, v in ipairs(splitDataPoints) do
            if k ~= 1 then
                min = math.min(min, v[2])
                max = math.max(max, v[2])
            end
        end
        return splitDataPoints, min, max
    else
        return dataPoints
    end
end

MarketLogic.ShowBuyMandelBrickPopProcess = function(itemId)
    if not MarketLogic.MarketExternalLockedProcess() then
        return
    end
    if itemId then
        local function onUILoaded(ui)
            ui:FetchSaleData()
        end
        if Server.MarketServer:CheckIsInSaleList(itemId) then
            Facade.UIManager:AsyncShowUI(UIName2ID.MandelBrickBuy, onUILoaded, nil, itemId)
        else
            Module.CommonTips:ShowSimpleTip(MarketConfig.Loc.ItemCanNotBuy)
            logerror("MarketLogic.ShowBuyMandelBrickPopProcess PropSaleInfo is nil")
        end
    end
end

MarketLogic.ShowSellMandelBrickPopProcess = function(itemId)
    if not MarketLogic.MarketExternalLockedProcess() then
        return
    end
    if itemId then
        local function onUILoaded(ui)
            ui:FetchSaleData()
        end
        if Server.MarketServer:CheckIsInSaleList(itemId) then
            Facade.UIManager:AsyncShowUI(UIName2ID.MandelBrickSell, onUILoaded, nil, itemId)
        else
            Module.CommonTips:ShowSimpleTip(MarketConfig.Loc.ItemCanNotSell)
            logerror("MarketLogic.ShowSellMandelBrickPopProcess PropSaleInfo is nil")
        end
    end
end

MarketLogic.ShowBuyMysticalSkinPopProcess = function(subPageType, itemId)
    if not MarketLogic.MarketExternalLockedProcess() then
        return
    end
    if itemId then
        if not Server.MarketServer:CheckIsInSaleList(itemId) then
            Module.CommonTips:ShowSimpleTip(MarketConfig.Loc.ItemCanNotBuy)
            logerror("MarketLogic.ShowBuyMysticalSkinPopProcess PropSaleInfo is nil")
            return
        end
    end
    local function onUILoaded(ui)
        ui:FetchSaleData()
    end
    Facade.UIManager:AsyncShowUI(UIName2ID.MysticalSkinBuy, onUILoaded, nil, subPageType, itemId)
end

MarketLogic.ShowSellMysticalSkinSellPageProcess = function(itemId)
    if not MarketLogic.MarketExternalLockedProcess() then
        return
    end
    if itemId then
        local function onUILoaded(ui)
            ui:FetchSaleData()
        end
        if Server.MarketServer:CheckIsInSaleList(itemId) then
            Facade.UIManager:AsyncShowUI(UIName2ID.MysticalSkinSell, onUILoaded, nil, itemId)
        else
            Module.CommonTips:ShowSimpleTip(MarketConfig.Loc.ItemCanNotSell)
            logerror("MarketLogic.ShowSellMysticalSkinSellPageProcess PropSaleInfo is nil")
        end
    end
end

MarketLogic.ShowSellMysticalSkinPopProcess = function(mysticalSkinInsItem)
    if not MarketLogic.MarketExternalLockedProcess() then
        return
    end
    if not MarketLogic.MarketInternalLockedProcess(mysticalSkinInsItem) then
        return
    end
    local ProcessSell = function(mysticalSkinInsItem)
        if mysticalSkinInsItem and mysticalSkinInsItem.id then
            if Server.MarketServer:CheckIsInSaleList(mysticalSkinInsItem.id) then
                local function onConfirmSell(bIsChecked)
                    if mysticalSkinInsItem then
                        local function onUILoaded(ui)
                            ui:FetchSaleData()
                        end
                        Facade.UIManager:AsyncShowUI(UIName2ID.MysticalSkinConfirmSell, onUILoaded, nil, mysticalSkinInsItem)
                    end
                end
                if Module.Market.Field:GetbShowRemoveSkinPop() then
                    -- 初始化完成再弹出窗口
                    local function fLoadFinCallback(uiIns)
                    end
                    local text = Module.Market.Config.Loc.MysticalSkinNeedBeRemoved
                    if Server.MarketServer:GetMarketItemType(mysticalSkinInsItem.id) == EMarketSubPageType.MysticalSkin then
                        text = Module.Market.Config.Loc.MysticalSkinNeedBeRemoved
                    elseif Server.MarketServer:GetMarketItemType(mysticalSkinInsItem.id) == EMarketSubPageType.MysticalPendant then
                        text = Module.Market.Config.Loc.MysticalPendantNeedBeRemoved
                    end
                    local confirmText = Module.Market.Config.Loc.Confirm
                    local cancelText = Module.Market.Config.Loc.Cancel
                    Facade.UIManager:AsyncShowUI(UIName2ID.ConfirmWindows, fLoadFinCallback, nil, text, noticeText, onConfirmSell, onCancel, cancelText, 
                    confirmText, confirmSound, cancelSound, stateInGuide, nil, ignoreGc)
                    Module.Market.Field:SetbShowRemoveSkinPop(false)
                else
                    onConfirmSell()
                end
            else
                Module.CommonTips:ShowSimpleTip(MarketConfig.Loc.ItemCanNotSell)
                logerror("MarketLogic.ShowSellMysticalSkinPopProcess PropSaleInfo is nil")
            end
        end
    end
    -- 如果激活了且只有一个。售卖就会打破激活状态
    if ItemHelperTool.IsMysticalPendant(mysticalSkinInsItem.id) then 
        if Server.CollectionServer:GetSuitPendantNum(mysticalSkinInsItem.id) == 1 and Server.CollectionServer:GetSuitPendantActived(mysticalSkinInsItem.id) then 
            local name = mysticalSkinInsItem.name
            local customName = mysticalSkinInsItem.rawPropInfo.mystical_pendant_data.custom_name
            if customName ~= "" then 
                name = customName
            end
            local str = string.format(MarketConfig.Loc.BrokenPendantHallConfirm,name)
            Module.CommonTips:ShowConfirmWindow(
                str,
                function()
                    ProcessSell(mysticalSkinInsItem)
                end,
                function()
    
                end,
                Module.Collection.Config.Loc.Cancel,
                Module.Collection.Config.Loc.Confirm, nil, nil, nil, nil, nil, true
            )
        else
            ProcessSell(mysticalSkinInsItem)
        end
    else
        ProcessSell(mysticalSkinInsItem)
    end
end

MarketLogic.ShowRecordDetailPopProcess = function(curSubPageType)
    local function onUILoaded(ui)
    end
    Facade.UIManager:AsyncShowUI(UIName2ID.MarketPurchaseRecordDetail, onUILoaded, nil, curSubPageType)
end

-- 暂废除：配合跨价格点购买逻辑
MarketLogic.checkSaleListToBuy = function(saleList, buyNum)
    if saleList and not table.isempty(saleList) and buyNum then
        local props = {}
        local numInProps = 0
        local saleListsNum = 0
        local totalPrice = 0
        for index, value in ipairs(saleList) do
            numInProps = numInProps + value.prop.num
            saleListsNum = saleListsNum + 1
            if numInProps >= buyNum then
                local data = {}
                data[1] = value
                data[2] = buyNum - numInProps + value.prop.num
                props[index] = data
                totalPrice = totalPrice + value.price * (buyNum - numInProps + value.prop.num)
                break
            else
                local data = {}
                data[1] = value
                data[2] = value.prop.num
                props[index] = data
                totalPrice = totalPrice + value.price * value.prop.num
            end
        end
        return props, totalPrice
    end
end

-- 暂废除：跨价格点购买逻辑
MarketLogic.DoMultiPriceBuyProcess = function(saleInfo, buyNum, preProps, preTotalPrice, fCallback)
    loginfo("MarketLogic.DoMultiPriceBuyProcess")
    local function DoMultiPriceBuyCallback()
        local latestSaleList = {}
        if saleInfo and saleInfo.sale_lists and saleInfo.prop_id then
            latestSaleList = saleInfo and saleInfo.sale_lists or {}
            -- 直接对server缓存进行升序排序
            local function ASCSort(a, b)
                return a.price < b.price
            end
            table.insertionSort(latestSaleList, ASCSort)
            local props
            if preProps and preTotalPrice then
                props = {}
                local latestSaleNum = {}
                for key, value in pairs(latestSaleList) do
                    latestSaleNum[key] = latestSaleNum[key] or {}
                    latestSaleNum[key].price = value.price
                    latestSaleNum[key].prop = latestSaleNum[key].prop or {}
                    latestSaleNum[key].prop.num = value.prop.num
                end
                local buyNumForCount = {}
                for key, value in pairs(preProps) do
                    local curPropNum = value[2]
                    local curPropPrice = value[1].price
                    for k, v in pairs(latestSaleNum) do
                        if curPropNum == 0 then
                            break
                        end
                        if v.prop.num ~= 0 and v.price <= curPropPrice then
                            if v.prop.num <= curPropNum then
                                buyNumForCount[k] = buyNumForCount[k] or 0
                                buyNumForCount[k] = buyNumForCount[k] + v.prop.num
                                curPropNum = curPropNum - v.prop.num
                                v.prop.num = 0
                            else
                                buyNumForCount[k] = buyNumForCount[k] or 0
                                buyNumForCount[k] = buyNumForCount[k] + curPropNum
                                v.prop.num = v.prop.num - curPropNum
                                break
                            end
                        end
                    end
                end
                for key, value in pairs(latestSaleList) do
                    if buyNumForCount[key] and buyNumForCount[key] > 0 then
                        local data = {}
                        data[1] = value
                        data[2] = buyNumForCount[key]
                        table.insert(props, data)
                    end
                end
            else -- 供市场外部调用直接购买
                local totalPrice
                props, totalPrice = MarketLogic.checkSaleListToBuy(latestSaleList, buyNum)
                if #props == 0 then
                    local itemInfo = ItemConfigTool.GetItemConfigById(saleInfo.prop_id)
                    Module.CommonTips:ShowSimpleTip(string.format(MarketConfig.Loc.ItemHasBeenSoldOut, itemInfo.Name))
                    return
                else
                    local currencyNum = Module.Currency:GetNumByItemId(props[1][1].buy_currency_id)
                    if currencyNum < totalPrice then
                        -- ShopHelperTool.ShowNotEnoughTipByCurrencyType(props[1][1].buy_currency_id)
                        ShopHelperTool.ShowNotEnoughTipByCurrencyId(props[1][1].buy_currency_id)
                        return
                    end
                end
            end
            Server.MarketServer:DoMultiPriceBuyReq(props, buyNum, preProps, preTotalPrice, fCallback)
        end
    end
    -- 拉取拍卖行最新数据再进行购买
    if saleInfo and saleInfo.prop_id then
        Server.MarketServer:FetchSaleList(saleInfo.prop_id, nil, DoMultiPriceBuyCallback)
    end
end

MarketLogic.DoPreorderBuyProcess = function(saleInfo, buyNum, buyPrice, bSpecialPrice)
    loginfo("MarketLogic.DoPreorderBuyProcess")
    if saleInfo then
        local saleList = saleInfo and saleInfo.sale_lists or {}
        local itemInfo = ItemConfigTool.GetItemConfigById(saleInfo.prop_id)
        if #saleList == 0 then
            Module.CommonTips:ShowSimpleTip(string.format(MarketConfig.Loc.ItemHasBeenSoldOut, (itemInfo and itemInfo.Name) and itemInfo.Name or ""))
            return
        end
        local buyPrice = buyPrice or saleInfo.average_price -- 默认平均价购买
        if buyPrice and buyNum then
            local totalPrice = buyPrice * buyNum
            local currencyNum = Module.Currency:GetNumByItemId(saleInfo.buy_currency_id)
            if currencyNum < totalPrice then
                ShopHelperTool.ShowNotEnoughTipByCurrencyId(saleInfo.buy_currency_id)
                return
            end
            local function onConfirmSell()
                Server.MarketServer:DoPreorderBuyReq(saleInfo.prop_id, buyNum, buyPrice, bSpecialPrice)
            end
            -- 初始化完成再弹出窗口
            local function fLoadFinCallback(uiIns)
            end
            local currencyId = Module.Currency:ConvertCurrencyIdByItemId(saleInfo.buy_currency_id)
            local currencyIcon = Module.Currency:GetRichTxtImgId(currencyId)
            
            local text = string.format(MarketConfig.Loc.MandelBrickBuyConfirm, buyNum, (itemInfo and itemInfo.Name) and itemInfo.Name or "",
             string.format(Module.Market.Config.Loc.CurrencyNum, 52, 52, currencyIcon, MathUtil.GetNumberFormatStr(buyNum * buyPrice + 0.00001)), 24) -- she1 todo 有效期小时数待走配置
            Facade.UIManager:AsyncShowUI(UIName2ID.ConfirmWindows, fLoadFinCallback, nil, text, noticeText, onConfirmSell, onCancel, cancelText, 
            confirmText, confirmSound, cancelSound, stateInGuide, nil, ignoreGc)
        else
            logwarning("MarketLogic.DoPreorderBuyProcess buyPrice or buyNum is nil saleInfo.average_price = ", saleInfo.average_price)
        end
    else
        logwarning("MarketLogic.DoPreorderBuyProcess saleInfo is nil")
    end
end

MarketLogic.DoPreorderCancelProcess = function(itemIds)
    loginfo("MarketLogic.DoPreorderCancelProcess")
    if not itemIds then
        return
    end
    if table.isempty(itemIds) then
        logwarning("MarketLogic.DoPreorderCancelProcess itemIds is empty")
        return
    end
    local function onConfirmSell()
        Server.MarketServer:CancelPreorderReq(itemIds)
    end
    -- 初始化完成再弹出窗口
    local function fLoadFinCallback(uiIns)
    end 
    local text = MarketConfig.Loc.ManderBrickPreorderCancelConfirm
    Facade.UIManager:AsyncShowUI(UIName2ID.ConfirmWindows, fLoadFinCallback, nil, text, noticeText, onConfirmSell, onCancel, cancelText, 
    confirmText, confirmSound, cancelSound, stateInGuide, nil, ignoreGc)
end

MarketLogic.DoSellMandelBrickProcess = function(itemId, sellNum, sellSinglePrice, duration, portion)
    loginfo("MarketLogic.DoSellMandelBrickProcess")
    local props = {}
    local prop = {
        id = itemId, 
        num = sellNum, 
        bind_type = 1
    }
    table.insert(props, prop)
    if not table.isempty(props) then
        Server.MarketServer:DoSellMandelBrickReq(props, sellNum, sellSinglePrice, duration, portion)
    end
end

MarketLogic.DoBuyMysticalSkinProcess = function(cellInfo, buyNum)
    loginfo("MarketLogic.DoBuyMysticalSkinProcess")
    Server.MarketServer:DoBuyMysticalSkinReq(cellInfo, buyNum)
end

MarketLogic.DoSellMysticalSkinProcess = function(propInfo, sellNum, sellSinglePrice, duration, portion)
    loginfo("MarketLogic.DoSellMysticalSkinProcess")
    local props = {}
    table.insert(props, propInfo)
    if not table.isempty(props) then
        Server.MarketServer:DoSellMysticalSkinReq(props, sellNum, sellSinglePrice, duration, portion)
    end
end

MarketLogic.DoBuyMeleeWeaponProcess = function(cellInfo, buyNum)
    loginfo("MarketLogic.DoBuyMeleeWeaponProcess")
    if not (cellInfo and cellInfo.prop and cellInfo.prop.id) then
        return
    end
    local ownNumber = MarketLogic.GetMarketItemNumById(cellInfo.prop.id)
    if ownNumber > 0 then
        Module.CommonTips:ShowSimpleTip(Module.Market.Config.Loc.HasOwnTheSameMeleeWeapon)
        return
    end
    local sellingList = Server.MarketServer:GetMeleeWeaponSellingList()
    for _, order in ipairs(sellingList) do
        if order and order.prop and order.prop.id == cellInfo.prop.id then
            Module.CommonTips:ShowSimpleTip(Module.Market.Config.Loc.HasOwnTheSameMeleeWeapon)
            return
        end
    end
    Server.MarketServer:DoBuyMysticalSkinReq(cellInfo, buyNum)
end

MarketLogic.ClassifyAndSortSaleLists = function(mysticalSkinSaleList)
    loginfo("MarketLogic.ClassifyAndSortSaleLists")
    local saleListsCategories = {}
    local marketTabInfo = Module.Market.Field:GetMarketTabInfo()
    -- for _, weaponSubTabInfo in ipairs(marketTabInfo[EMarketSubPageType.MysticalSkin].subTabList[3].subTabContentList) do -- 枪种分类
    --     if weaponSubTabInfo.subTabContentId ~= 100 then -- 无需全部枪械
    --         saleListsCategories[weaponSubTabInfo.subTabContentTypes[1]] = {}
    --         for _, qualitySubTabInfo in ipairs(marketTabInfo[EMarketSubPageType.MysticalSkin].subTabList[2].subTabContentList) do -- 品阶分类
    --             if qualitySubTabInfo.subTabContentId ~= 100 then -- 无需全部品阶
    --                 saleListsCategories[weaponSubTabInfo.subTabContentTypes[1]][qualitySubTabInfo.subTabContentTypes[1]] = {}
    --             end
    --         end
    --     end
    -- end
    -- if mysticalSkinSaleList and not table.isempty(mysticalSkinSaleList) then
    --     for itemId, saleList in pairs(mysticalSkinSaleList) do
    --         if Server.MarketServer:GetMarketItemType(itemId) == EMarketSubPageType.MysticalSkin then
    --             local weaponCategory = ItemHelperTool.GetSubTypeById(itemId) -- todo 暂认为武器皮肤二级类别与武器相同
    --             local qualityCategory = ItemHelperTool.GetQualityTypeById(itemId)
    --             if saleListsCategories[weaponCategory] and saleListsCategories[weaponCategory][qualityCategory] then
    --                 table.insert(saleListsCategories[weaponCategory][qualityCategory], {[itemId] = saleList})
    --             end
    --         end
    --     end
    -- end
    -- -- 按配表顺序创建所需list（先枪种，再品阶）
    -- local finalSaleLists = {}
    -- for _, weaponSubTabInfo in ipairs(marketTabInfo[EMarketSubPageType.MysticalSkin].subTabList[3].subTabContentList) do -- 枪种
    --     if weaponSubTabInfo.subTabContentId ~= 100 then
    --         local weaponSaleLists = {}
    --         local curSaleListsCategory = saleListsCategories[weaponSubTabInfo.subTabContentTypes[1]]
    --         for _, qualitySubTabInfo in ipairs(marketTabInfo[EMarketSubPageType.MysticalSkin].subTabList[2].subTabContentList) do -- 品阶
    --             if qualitySubTabInfo.subTabContentId ~= 100 then
    --                 local qualitySaleLists = {}
    --                 local curSaleLists = curSaleListsCategory[qualitySubTabInfo.subTabContentTypes[1]]
    --                 if curSaleLists then
    --                     for _, saleList in ipairs(curSaleLists) do
    --                         table.insert(qualitySaleLists, saleList)
    --                     end
    --                 end
    --                 table.insert(weaponSaleLists, qualitySaleLists)
    --             end
    --         end
    --         table.insert(finalSaleLists, weaponSaleLists)
    --     end
    -- end
    for _, qualitySubTabInfo in ipairs(marketTabInfo[EMarketSubPageType.MysticalSkin].subTabList[2].subTabContentList) do -- 品阶分类
        if qualitySubTabInfo.subTabContentId ~= 100 then -- 无需全部品阶
            saleListsCategories[qualitySubTabInfo.subTabContentTypes[1]] = {}
            for _, weaponSubTabInfo in ipairs(marketTabInfo[EMarketSubPageType.MysticalSkin].subTabList[3].subTabContentList) do -- 枪种分类
                if weaponSubTabInfo.subTabContentId ~= 100 then -- 无需全部枪械
                    saleListsCategories[qualitySubTabInfo.subTabContentTypes[1]][weaponSubTabInfo.subTabContentTypes[1]] = {}
                end
            end
        end
    end
    if mysticalSkinSaleList and not table.isempty(mysticalSkinSaleList) then
        for itemId, saleList in pairs(mysticalSkinSaleList) do
            if Server.MarketServer:GetMarketItemType(itemId) == EMarketSubPageType.MysticalSkin then
                local qualityCategory = ItemHelperTool.GetQualityTypeById(itemId)
                local weaponCategory = ItemHelperTool.GetSubTypeById(itemId) -- todo 暂认为武器皮肤二级类别与武器相同
                if saleListsCategories[qualityCategory] and saleListsCategories[qualityCategory][weaponCategory] then
                    table.insert(saleListsCategories[qualityCategory][weaponCategory], {[itemId] = saleList})
                end
            end
        end
    end
    -- 按配表顺序创建所需list（先品阶，再枪种）
    local finalSaleLists = {}
    for _, qualitySubTabInfo in ipairs(marketTabInfo[EMarketSubPageType.MysticalSkin].subTabList[2].subTabContentList) do -- 品阶
        if qualitySubTabInfo.subTabContentId ~= 100 then
            local qualitySaleLists = {}
            local curSaleListsCategory = saleListsCategories[qualitySubTabInfo.subTabContentTypes[1]]
            for _, weaponSubTabInfo in ipairs(marketTabInfo[EMarketSubPageType.MysticalSkin].subTabList[3].subTabContentList) do -- 枪种
                if weaponSubTabInfo.subTabContentId ~= 100 then
                    local weaponSaleLists = {}
                    local curSaleLists = curSaleListsCategory[weaponSubTabInfo.subTabContentTypes[1]]
                    if curSaleLists then
                        for _, saleList in ipairs(curSaleLists) do
                            table.insert(weaponSaleLists, saleList)
                        end
                    end
                    table.insert(qualitySaleLists, weaponSaleLists)
                end
            end
            table.insert(finalSaleLists, qualitySaleLists)
        end
    end
    return finalSaleLists
end

MarketLogic.GetSeasonCategories = function(selectedSeasons)
    loginfo("MarketLogic.GetSeasonCategories")
    local seasonsCategories = selectedSeasons
    return seasonsCategories
end

MarketLogic.GetOwnedCategories = function(selectedOwnedIds)
    loginfo("MarketLogic.GetOwnedCategories")
    local ownedCategories = selectedOwnedIds
    return ownedCategories
end

MarketLogic.GetGradeCategories = function(selectedGradeIds)
    loginfo("MarketLogic.GetGradeCategories")
    -- local gradeCategories = {}
    -- if not table.isempty(selectedGradeIds) then
    --     for _, categoryId in pairs(selectedGradeIds) do
    --         for k, v in pairs(ItemConfig.EWeaponSkinQualityType) do
    --             if v == categoryId then
    --                 table.insert(gradeCategories, v)
    --             end
    --         end
    --     end
    -- end
    -- todo 此处目前有些hardcode，依赖于MarkerTab表的正确配置，合理设计应该是excel做索引保证datatable的正确，或者使用上面注释的方法用lua里的枚举正确映射。
    local gradeCategories = selectedGradeIds
    return gradeCategories
end

MarketLogic.GetWeaponCategories = function(selectedWeaponIds)
    loginfo("MarketLogic.GetWeaponCategories")
    local weaponCategories = {}
    if not table.isempty(selectedWeaponIds) then
        for _, categoryId in pairs(selectedWeaponIds) do
            for k, v in pairs(ItemConfig.EWeaponItemType) do
                if v == categoryId then
                    table.insert(weaponCategories, v)
                end
            end
        end
    end
    return weaponCategories
end

MarketLogic.MysticalSkinHomepageScreeningProcess = function(curSubPageType, seasonCategories, ownedCategories, gradeCategories, weaponCategories, screeningResultCallback)
    loginfo("MarketLogic.MysticalSkinHomepageScreeningProcess")
    local itemIds = MarketLogic.GetCategorybEligibleItemIds(curSubPageType, seasonCategories, ownedCategories, gradeCategories, weaponCategories)
    if not table.isempty(itemIds) then
        Server.MarketServer:FetchTypeList(itemIds)
    else
        Module.CommonTips:ShowSimpleTip(Module.Market.Config.Loc.ScreeningNoResult)
    end
    if screeningResultCallback then
        screeningResultCallback(itemIds)
    end
end

MarketLogic.GetCategorybEligibleItemIds = function(curSubPageType, seasonCategories, ownedCategories, gradeCategories, weaponCategories)
    loginfo("MarketLogic.GetCategorybEligibleItemIds")
    local itemIds = {}
    local itemSaleList = {}
    if curSubPageType == EMarketSubPageType.MysticalSkin then
        itemSaleList = Server.MarketServer:GetMysticalSkinSaleList()
    elseif curSubPageType == EMarketSubPageType.MysticalPendant then
        itemSaleList = Server.MarketServer:GetMysticalPendantSaleList()
    end
    if not table.isempty(itemSaleList) then
        for id, saleInfo in pairs(itemSaleList) do
            if MarketLogic.JudgeSeasonCategorybEligible(id, seasonCategories)
                and MarketLogic.JudgeOwnedCategorybEligible(id, ownedCategories)
                 and MarketLogic.JudgeGradeCategorybEligible(id, gradeCategories)
                  and MarketLogic.JudgeWeaponCategorybEligible(id, weaponCategories) then
                table.insert(itemIds, id)
            end
        end
    end
    return itemIds
end

MarketLogic.JudgeSeasonCategorybEligible = function(itemId, seasonCategories)
    loginfo("MarketLogic.JudgeSeasonCategorybEligible")
    if not table.isempty(seasonCategories) then
        local saleInfo = Server.MarketServer:GetPropSaleInfo(itemId)
        for k, v in ipairs(seasonCategories) do
            if v == 0 then
                return true
            else
                if saleInfo and saleInfo.season and saleInfo.season == v then
                    return true
                end
            end
        end
        return false
    else
        return true
    end
end

MarketLogic.JudgeOwnedCategorybEligible = function(itemId, ownedCategories)
    loginfo("MarketLogic.JudgeOwnedCategorybEligible")
    if not table.isempty(ownedCategories) then
        local bEligible = false
        if Server.MarketServer:GetMarketItemType(itemId) == EMarketSubPageType.MysticalSkin then
            bEligible = Server.CollectionServer:IsOwnedWeaponSkin(itemId)
        elseif Server.MarketServer:GetMarketItemType(itemId) == EMarketSubPageType.MysticalPendant then
            bEligible = Server.CollectionServer:IsOwnedHanging(itemId)
        end
        return bEligible
    else
        return true
    end
end

MarketLogic.JudgeGradeCategorybEligible = function(itemId, gradeCategories)
    loginfo("MarketLogic.JudgeGradeCategorybEligible")
    if not table.isempty(gradeCategories) then
        local bEligible = false
        local quality
        if Server.MarketServer:GetMarketItemType(itemId) == EMarketSubPageType.MysticalSkin then
            quality = ItemHelperTool.GetQualityTypeById(itemId)
        elseif Server.MarketServer:GetMarketItemType(itemId) == EMarketSubPageType.MysticalPendant then
            quality = ItemConfigTool.GetItemQuality(itemId)
        end
        if not table.isempty(gradeCategories) then
            for k, v in pairs(gradeCategories) do
                if v == quality then
                    bEligible = true
                    break
                end
            end
        end
        return bEligible
    else
        return true
    end
end

MarketLogic.JudgeWeaponCategorybEligible = function(itemId, weaponCategories)
    loginfo("MarketLogic.JudgeWeaponCategorybEligible")
    if not table.isempty(weaponCategories) then
        local bEligible = false
        local itemMainType = ItemHelperTool.GetMainTypeById(itemId)
        if  itemMainType and itemMainType == EItemType.WeaponSkin then
            local weaponCategory = ItemHelperTool.GetSubTypeById(itemId) -- todo 暂认为武器皮肤二级类别与武器相同
            if not table.isempty(weaponCategories) then
                for k, v in pairs(weaponCategories) do
                    if v == weaponCategory then
                        bEligible = true
                        break
                    end
                end
            end
        end
        return bEligible
    else
        return true
    end
end

MarketLogic.MysticalSkinPurchaseScreeningProcess = function(saleLists, screeningParams)
    loginfo("MarketLogic.MysticalSkinPurchaseScreeningProcess")
    local finalSaleLists = {}
    if saleLists then
        if not table.isempty(saleLists) then
            for _, saleList in ipairs(saleLists) do
                if MarketLogic.JudgeRaritybEligible(saleList, screeningParams)
                    and MarketLogic.JudgeWearbEligible(saleList, screeningParams)
                    and MarketLogic.JudgePricebEligible(saleList, screeningParams) then
                    table.insert(finalSaleLists, saleList)
                end
            end
        end
    end
    return finalSaleLists
end

MarketLogic.JudgeRaritybEligible = function(saleList, screeningParams)
    loginfo("MarketLogic.JudgeRaritybEligible")
    if Server.MarketServer:GetMarketItemType(saleList.prop.id) == EMarketSubPageType.MysticalSkin and ItemHelperTool.GetQualityTypeById(saleList.prop.id) < ItemConfig.EWeaponSkinQualityType.Orange then -- 橙色品阶以下玄学皮肤无稀有度概念
        return true
    end
    return table.contains(screeningParams.rarityIds, saleList.prop.mystical_skin_data.rarity)
end

MarketLogic.JudgeWearbEligible = function(saleList, screeningParams)
    loginfo("MarketLogic.JudgeWearbEligible")
    return (saleList.prop.mystical_skin_data.wear / ItemConfigTool.GetMysticalWearRate()) >= screeningParams.minWear and (saleList.prop.mystical_skin_data.wear / ItemConfigTool.GetMysticalWearRate()) <= screeningParams.maxWear
end

MarketLogic.JudgePricebEligible = function(saleList, screeningParams)
    loginfo("MarketLogic.JudgePricebEligible")
    return screeningParams.minPrice <= saleList.price and saleList.price <= screeningParams.maxPrice
end

MarketLogic.OffSelfOrder = function(order)
    loginfo("MarketLogic.OffSelfOrder")
    if order and order.state and order.prop then
        local orderId = order.order_id -- 存下唯一id做判断，避免引用被修改
        local offShelfCd = Server.MarketServer:GetOffShelfCd()
        if order.sale_begin_time and (order.sale_begin_time + offShelfCd > Facade.ClockManager:GetLocalTimestamp()) then
            local remainTime = order.sale_begin_time + offShelfCd - Facade.ClockManager:GetLocalTimestamp()
            local day, hour, min, sec = TimeUtil.GetSecondsFormatDDHHMMSS(remainTime)
            if day == 0 and hour == 0 and min == 0 and sec > 0 then
                Module.CommonTips:ShowSimpleTip(string.format(MarketConfig.Loc.OffShelfIsCdSec, sec))
            elseif day == 0 and hour == 0 and min > 0 then
                Module.CommonTips:ShowSimpleTip(string.format(MarketConfig.Loc.OffShelfIsCdMin, min))
            elseif day == 0 and hour > 0 then
                Module.CommonTips:ShowSimpleTip(string.format(MarketConfig.Loc.offShelfIsCdHour, hour))
            elseif day > 0 then
                Module.CommonTips:ShowSimpleTip(string.format(MarketConfig.Loc.offShelfIsCdDay, day))
            end
            return
        end
        if order.state == OrderState.OrderRacked then
            local itemInfo = ItemConfigTool.GetItemConfigById(order.prop.id)
            local itemName = itemInfo.Name
            local title = MarketConfig.Loc.PullOffWindowTitle
            local text =  string.format(MarketConfig.Loc.PullOffWindowText, itemName)
            local cancelTxt = MarketConfig.Loc.PullOffCancel
            local confirmTxt = MarketConfig.Loc.PullOffConfirm
            local confirmSound = DFMAudioRes.UIAHOffShelfConfirm -- todo 改为市场音效
            local confirmHandle = function(num)
                num = num or 1 -- todo 暂无需部分下架功能
                if order.order_id ~= orderId then -- 订单发生变化，引用改变
                    Module.CommonTips:ShowSimpleTip(MarketConfig.Loc.OrderHasChanged)
                    return
                end
                Server.MarketServer:DoPullOffGoodReq(order, order.prop.num)
            end
            -- todo 暂无需部分下架功能
            -- if order.prop.num > 1 then --聚合的道具可以选择下架数量
            --     Facade.UIManager:AsyncShowUI(UIName2ID.AuctionOffShelfCountableWindow, nil, nil,
            --         text, Module.Auction.Config.Loc.TheShelvesNumber, nil, order.prop.num, confirmHandle, nil, cancelTxt, confirmTxt, confirmSound)
            -- else
                Module.CommonTips:ShowConfirmWindow(text, confirmHandle, nil, cancelTxt, confirmTxt, confirmSound)
            -- end
            return
        end
        Server.MarketServer:DoPullOffGoodReq(order, order.prop.num)
    end
end

MarketLogic.SetItemViewSize = function(wtShopItemTemplate, x, y)
    loginfo("MarketLogic.SetItemViewSize")
    local sizeBox =  wtShopItemTemplate:Wnd("PlatformSizeBox_pc", UIWidgetBase)
    if sizeBox and x and y then
        sizeBox:SetWidthOverride(x)
        sizeBox:SetHeightOverride(y)
    end
end

MarketLogic.JudgeMarketIsOpen = function()
    loginfo("MarketLogic.JudgeMarketIsOpen")
    local bOpen = false
    local marketOpenTimePeriods = MarketLogic.GetMarketOpenTimePeriods()
    if marketOpenTimePeriods and not table.isempty(marketOpenTimePeriods) then
        for _, timePeriod in ipairs(marketOpenTimePeriods) do
            if timePeriod[1] <= Facade.ClockManager:GetLocalTimestamp() and Facade.ClockManager:GetLocalTimestamp() <= timePeriod[2] then
                bOpen = true
                break
            end
        end
    end
    return bOpen
end

MarketLogic.GetMarketOpenTimePeriods = function()
    loginfo("MarketLogic.GetMarketOpenTimePeriod")
    local openTime = Server.MarketServer:GetMarketOpenTime()
    local closeTime = Server.MarketServer:GetMarketCloseTime()
    local timePeriods = {}
    if openTime == 0 and closeTime == 0 then
        local openTimestamp = MarketLogic.getSpecificTimestampBySeconds(0)
        local closeTimestamp = MarketLogic.getSpecificTimestampBySeconds(24 * 60 * 60)
        local timePeriod = {openTimestamp, closeTimestamp}
        table.insert(timePeriods, timePeriod)
        return timePeriods
    end
    if (openTime / 3600 <= 24) and (closeTime / 3600 <= 24) then
        local openTimestamp = MarketLogic.getSpecificTimestampBySeconds(openTime)
        local closeTimestamp = MarketLogic.getSpecificTimestampBySeconds(closeTime)
        local timePeriod = {openTimestamp, closeTimestamp}
        table.insert(timePeriods, timePeriod)
        return timePeriods
    end
    if (openTime / 3600 < 24) and (closeTime / 3600 > 24) then
        local openTimestamp = MarketLogic.getSpecificTimestampBySeconds(openTime)
        local closeTimestamp = MarketLogic.getSpecificTimestampBySeconds(closeTime)
        local timePeriod = {MarketLogic.getSpecificTimestampBySeconds(0), closeTimestamp - 24 * 60 * 60}
        local timePeriod2 = {openTimestamp, MarketLogic.getSpecificTimestampBySeconds(24 * 60 * 60)}
        table.insert(timePeriods, timePeriod)
        table.insert(timePeriods, timePeriod2)
        return timePeriods
    end
    if (openTime / 3600 > 24) then
        logerror("MarketLogic.GetMarketOpenTimePeriods OpenTime Config is error")
    end
end

-- 功能：传入秒数，返回当天（服务器时区）经过这一秒数后的时间戳
MarketLogic.getSpecificTimestampBySeconds = function(seconds)
    -- 获取当前的服务器时间戳
    local currentTimestamp = Facade.ClockManager:GetLocalTimestamp()
    loginfo("MarketLogic.getSpecificTimestampBySeconds " .. tostring(currentTimestamp))
    local date, offset
    local utc_date = os.date("!*t", currentTimestamp)
    local local_date = os.date("*t", currentTimestamp)
    local isdst = local_date.isdst
    local_date.isdst = false
    local physical_offset = math.floor(os.difftime(os.time(local_date), os.time(utc_date)))
    loginfo("MarketLogic.getSpecificTimestampBySeconds offsets: " .. tostring(physical_offset))
    -- date: 服务器日期 CN=+8 Global=0 当天0时0分0秒
    -- utc_date: UTC日期
    -- local_date: 当地日期
    -- physical_offset: 当地日期与UTC之间的差（in seconds）
    -- offset：当地日期与服务器日期之间的差（in seconds）
    if IsBuildRegionCN() then
        loginfo("MarketLogic.IsCN")
        date = os.date("!*t", currentTimestamp + 8 * 60 * 60)
        offset = physical_offset - 8 * 60 * 60
    else
        date = os.date("!*t", currentTimestamp)
        offset = physical_offset
    end
    -- 计算输入秒数对应的小时、分钟、秒
    local bNextDay = (seconds / 3600) >= 24
    local hours = math.floor(seconds / 3600) % 24
    local minutes = math.floor((seconds % 3600) / 60)
    local secs = seconds % 60
    -- 设置时间
    date.hour = hours
    date.min = minutes
    date.sec = secs
    date.isdst = isdst
    -- 此时的date是当地时区当天0点0分的时间戳
    local specificTimestamp = os.time(date) + offset
    if bNextDay then
        specificTimestamp = specificTimestamp + 24 * 60 * 60
    end
    return specificTimestamp
end

-- 获取单元订单是否解锁
MarketLogic.GetOrderUnlockedState = function(orderInfo, itemWidget)
    if orderInfo then
        if MarketLogic.GetOrderUnlockRemainTime(orderInfo, itemWidget) > 0 then
            return false
        else
            return true
        end
    end
    return true -- 异常情况设为解锁
end

-- 获取单元订单是否过期
MarketLogic.GetOrderExpiredState = function(orderInfo)
    if orderInfo then
        if MarketLogic.GetOrderExpiredRemainTime(orderInfo) > 0 then
            return false
        else
            return true
        end
    end
    return false -- 异常情况设为未过期
end

MarketLogic.GetOrderUnlockRemainTime = function(orderInfo, itemWidget)
    if orderInfo then
        if orderInfo.valid_time then
            local curTime = Facade.ClockManager:GetLocalTimestamp()
            local unlockTime = orderInfo.valid_time
            return unlockTime - curTime
        else
            logerror("MysticalSkinBuy:GetOrderUnlockRemainTime valid_time is not exist")
        end
    end
    if itemWidget then
        itemWidget:ReleaseTimer() -- 异常情况取消Tick避免重复不断logerror
    end
    return 0 -- 异常情况设为解锁
end

MarketLogic.GetOrderExpiredRemainTime = function(orderInfo)
    if orderInfo then
        if orderInfo.expire_time then
            local curTime = Facade.ClockManager:GetLocalTimestamp()
            local expireTime = orderInfo.expire_time
            return expireTime - curTime
        else
            logerror("MysticalSkinBuy:GetOrderExpiredRemainTime expire_time is not exist")
        end
    end
    return -1 -- 异常情况设为未过期
end

MarketLogic.OpenMysticalSkinModelView = function(itemId, saleInfo)
    if itemId then
        local item = ItemBase:NewIns(itemId)
        if Server.MarketServer:GetMarketItemType(itemId) == EMarketSubPageType.MysticalSkin or Server.MarketServer:GetMarketItemType(itemId) == EMarketSubPageType.MeleeWeapon then
            if saleInfo and saleInfo.prop then
                item = MarketLogic.CreateMysticalSkinItem(nil, saleInfo.prop)
            else
                item = MarketLogic.CreateMysticalSkinItem(itemId) or Server.CollectionServer:GetWeaponSkinIfExists(itemId) or Server.CollectionServer:GetCollectionItemById(itemId) or item
            end
            Module.Collection:ShowWeaponSkinDetailPage(item)
        elseif Server.MarketServer:GetMarketItemType(itemId) == EMarketSubPageType.MysticalPendant then
            if saleInfo and saleInfo.prop then
                item = MarketLogic.CreateMysticalSkinItem(nil, saleInfo.prop)
            else
                item = MarketLogic.CreateMysticalSkinItem(itemId) or Server.CollectionServer:GetHangingIfOwned(itemId) or Server.CollectionServer:GetCollectionItemById(itemId) or item
            end
            Module.Collection:ShowHangingDetailPage(item)
        end
    else
        if saleInfo and saleInfo.prop then
            local item = MarketLogic.CreateMysticalSkinItem(nil, saleInfo.prop) or ItemBase:NewIns(saleInfo.prop.id)
            if Server.MarketServer:GetMarketItemType(saleInfo.prop.id) == EMarketSubPageType.MysticalSkin or Server.MarketServer:GetMarketItemType(saleInfo.prop.id) == EMarketSubPageType.MeleeWeapon then
                Module.Collection:ShowWeaponSkinDetailPage(item)
            elseif Server.MarketServer:GetMarketItemType(saleInfo.prop.id) == EMarketSubPageType.MysticalPendant then
                Module.Collection:ShowHangingDetailPage(item)
            end
        else
            logwarning("MarketLogic.OpenMysticalSkinModelView itemId and (saleInfo or saleInfo.prop) are nil")
        end
    end
end

MarketLogic.CreateMysticalSkinItem = function(itemId, propInfo)
    if propInfo then
        return ItemHelperTool.CreateItemByPropInfo(propInfo)
    else
        if itemId then
            propInfo = {
                id = itemId
            }
            local mysticalSkinItem = ItemHelperTool.CreateItemByPropInfo(propInfo)
            ItemBaseTool.SetMysticalSkinFloorAppearance(mysticalSkinItem)
            return mysticalSkinItem
        else
            logwarning("MarketLogic.CreateMysticalSkinItem itemId and propInfo are nil")
            return nil
        end
    end
end

MarketLogic.AddMarketCollectionProcess = function(saleLists, isAdd, callback)
    Server.MarketServer:AddMarketCollection(saleLists, isAdd, callback)
end

MarketLogic.GetMarketItemTypeSeasonRange = function(subPageType)
    local saleList = {}
    if subPageType == EMarketSubPageType.MysticalSkin then
        saleList = Server.MarketServer:GetMysticalSkinSaleList()
    elseif subPageType == EMarketSubPageType.MysticalPendant then
        saleList = Server.MarketServer:GetMysticalPendantSaleList()
    else
        saleList = {}
    end
    local minSeason = 0
    local maxSeason = 0
    if not table.isempty(saleList) then
        for _, v in pairs(saleList) do
            minSeason = v.season or 0
            maxSeason = v.season or 0
            break
        end
        for _, v in pairs (saleList) do
            if v.season then
                minSeason = math.min(minSeason, v.season)
                maxSeason = math.max(maxSeason, v.season)
            end
        end
    end
    return minSeason, maxSeason
end

MarketLogic.GetMarketItemNumById = function(itemId)
    if not itemId then
        return 0
    end
    if Server.MarketServer:GetMarketItemType(itemId) == EMarketSubPageType.MandelBrick then
        local collectionItem = Server.CollectionServer:GetCollectionItemById(tonumber(itemId), 0)
        local ownNumber = (collectionItem and collectionItem.num) and collectionItem.num or 0
        return ownNumber
    elseif Server.MarketServer:GetMarketItemType(itemId) == EMarketSubPageType.MysticalSkin then
        local mysticalSkinNum = Server.CollectionServer:GetWeaponSkinInstanceNum(tonumber(itemId))
        local ownNumber = (mysticalSkinNum and type(mysticalSkinNum) == "number") and mysticalSkinNum or 0
        return ownNumber
    elseif Server.MarketServer:GetMarketItemType(itemId) == EMarketSubPageType.MysticalPendant then
        local mysticalPendantNum = Server.CollectionServer:GetHangingInstanceNum(tonumber(itemId))
        local ownNumber = (mysticalPendantNum and type(mysticalPendantNum) == "number") and mysticalPendantNum or 0
        return ownNumber
    elseif Server.MarketServer:GetMarketItemType(itemId) == EMarketSubPageType.MeleeWeapon then
        local ownNumber = 0
        if Server.CollectionServer:IsOwnedMeleeSkin(tonumber(itemId)) then
            ownNumber = 1
        else
            ownNumber = 0
        end
        return ownNumber
    else
        return 0
    end
end

return MarketLogic
