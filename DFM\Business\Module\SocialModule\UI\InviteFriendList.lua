----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSocial)
----- LOG FUNCTION AUTO GENERATE END -----------



-- 组队的好友列表
---@class InviteFriendList : LuaUIBaseView
local UGameplayBlueprintHelper = import "GameplayBlueprintHelper"
local InviteFriendList = ui("InviteFriendList")
local SocialLogic = require "DFM.Business.Module.SocialModule.Logic.SocialLogic"
local SocialConfig = Module.Social.Config
local TabUIContentData = require"DFM.Business.DataStruct.UIDataStruct.TabUIContentData"

function InviteFriendList:Ctor()
    self._wtPlayerInfoCloseBtn = self:Wnd("PlayerInfoCloseBtn", UIButton)
    self._wtPlayerInfoCloseBtn:Event("OnClicked", self.PanelD<PERSON>roy, self)
    self._wtSGBFriendList =
        UIUtil.WndScrollGridBox(
        self,
        "RoomPlayerInfoSGB",
        self._OnPlayerInfoGetItemCount,
        self._OnPlayerInfoProcessItemWidget
    )
    --空底板
    self._wtEmptySlot = self:Wnd("wtEmptySlot", UIWidgetBase)
    self._wtDFTabV1GroupTopBox = UIUtil.WndTabGroupBox(self, "wtDFTabV1GroupTopBox", self.OnGetTopTabItemCount, self.OnProcessTopTabItemWidget, self.OnCheckedTopTabIndexChanged)
    self._tabDataList = {}
    for index, tabInfo in ipairs(SocialConfig.FreindListTabInfo) do
        local tabData = TabUIContentData:NewIns(index, tabInfo.title, true, tabInfo.imgPath, true)
        if index < 4 then
            table.insert(self._tabDataList, tabData)
        end
    end

    --实时的玩家列表信息
    self._RealTimePlayerList = {}

    --实时更新的timehandle
    self._timeHandle = nil

    --实时更新间隔
    self.UpdateDelteTime = 5

    --全部页签要不要显示所有在线玩家
    self._bShowOnlinePlayer = false

    --LBS的获取的附近玩家列表
    self._neighberList = {}
    self._neighberIdList = {}
    self._lastTabIndex = 0

    -- azhengzheng:组队码按钮
    self._wtTeamCodeWB = self:Wnd("wtTeamCodeWB", DFCommonButtonOnly)
    self._wtTeamCodeWB:Event("OnClicked", self._OnTeamCodeBtnClicked, self)
    self._wtTeamCodeWB:Collapsed()
end

function InviteFriendList:OnOpen()
    self:_DoStartThing()
end

function InviteFriendList:OnActivate()
    self:_DoStartThing()
end

function InviteFriendList:OnDeactivate()
    self:_DoEndThing()
end

function InviteFriendList:OnClose()
    self:_DoEndThing()
end

function InviteFriendList:_DoStartThing()
    self:_CreateEmptyContent()
    self:AddLuaEvent(Server.MatchServer.Events.evtMatchReady, self.PanelDestroy, self)
    self:AddLuaEvent(Server.TeamServer.Events.evtTeamFace2FaceCreate, self._OnTeamFace2FaceCreate, self)
    self:AddLuaEvent(Module.RoleInfo.Config.Event.evtRoleMainPanelOpen, self._SetHide, self)
    self:AddLuaEvent(Module.RoleInfo.Config.Event.evtRoleMainPanelClose, self._SetShow, self)
    self:OnCheckedTopTabIndexChanged(0)
    self:_RegisterTimerHandle()
end

function InviteFriendList:_DoEndThing()
    self:RemoveAllLuaEvent()
    self:_UnRegisterTimerHandle()
    Module.Social.Field:SetHasOpenPlayerInfoPanel(false)
    Module.Invite:ClearTargetChannelInviteIds()

    if not hasdestroy(self._wtEmptySlot) then
        Facade.UIManager:ClearSubUIByParent(self, self._wtEmptySlot)
    end
end

function InviteFriendList:_CreateEmptyContent()
    if not hasdestroy(self._wtEmptySlot) then
        Facade.UIManager:RemoveSubUIByParent(self, self._wtEmptySlot)
        local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.CommonEmptyContent, self._wtEmptySlot, nil, nil)
        self._wtEmptyHint = getfromweak(weakUIIns)
    end

    if not hasdestroy(self._wtEmptyHint) then
        self._wtEmptyHint:BP_SetText(Module.Social.Config.Loc.EmptyALL)
        self._wtEmptyHint:SetCppValue("Set_Type", 1)
        self._wtEmptyHint:BP_Set_Type()
    end
end

function InviteFriendList:_RegisterTimerHandle()
    if self._timeHandle then
        return
    end

    self._timeHandle = Timer:NewIns(self.UpdateDelteTime, 0)
    self._timeHandle:AddListener(self._UpdatePlayerList, self)
    self._timeHandle:Start()
end

function InviteFriendList:_UnRegisterTimerHandle()
    if not self._timeHandle then
        return
    end

    self._timeHandle:Release()
    self._timeHandle = nil
end

function InviteFriendList:_OnPlayerInfoGetItemCount()
    return #self.onlinePlayerList
end

function InviteFriendList:_OnPlayerInfoProcessItemWidget(position, itemWidget)
    local info = self.onlinePlayerList[position+1]
    itemWidget:SetInfo(info)
end




function InviteFriendList:OnGetTopTabItemCount()
    return #self._tabDataList or 0
end

function InviteFriendList:OnProcessTopTabItemWidget(position, topTabItem)
    local topTabData = self._tabDataList[position + 1]
    if not topTabData then
        return
    end
    local curSelectTabIdx = self._wtDFTabV1GroupTopBox:GetCurSelectedIndex()
    topTabData:UpdateTabByLuaTabUIContent(topTabItem, curSelectTabIdx == position)
end

function InviteFriendList:OnCheckedTopTabIndexChanged(idx)
    self._lastTabIndex = idx
    if not hasdestroy(self._wtEmptySlot) then
        self._wtEmptySlot:SelfHitTestInvisible()
    end
    self.onlinePlayerList = {}
    self._wtSGBFriendList:RefreshAllItems()
    if idx == 0 then
        if self._wtEmptyHint then
            self._wtEmptyHint:BP_SetText(Module.Social.Config.Loc.EmptyALL)
        end
        self:_OnAllBtnClick()
        --Module.LBS:GetNearby()
    elseif idx == 1 then
        if self._wtEmptyHint then
            self._wtEmptyHint:BP_SetText(Module.Social.Config.Loc.EmptyFriend)
        end
        self:_OnFriendBtnClick()
    elseif idx == 2 then
        if self._wtEmptyHint then
            self._wtEmptyHint:BP_SetText(Module.Social.Config.Loc.EmptyRecent)
        end
        self:_OnRecentBtnClick()
    elseif idx == 3 then
        if self._wtEmptyHint then
            self._wtEmptyHint:BP_SetText(Module.Social.Config.Loc.EmptyNeighber)
        end
        self:_OnNeighberBtnClick()
        --Module.LBS:GetNearby()
    end
end




--全部页签   挨个拉取所有页签列表，去重后排序
function InviteFriendList:_OnAllBtnClick(bUpdate)
    -- azhengzheng:重构此处代码
    local callBack = CreateCallBack(function(self, recentPlayerList)
        if self._lastTabIndex ~= 0 then
            return
        end

        local allPlayerList = {}

        for _, value in pairs(recentPlayerList) do
            allPlayerList[value.player_id] = value
        end

        for _, value in pairs(self:_GetFriendList()) do
            allPlayerList[value.player_id] = value
        end

        self.onlinePlayerList = {}

        for _, value in pairs(allPlayerList) do
            table.insert(self.onlinePlayerList, value)
        end

        table.sort(self.onlinePlayerList, SocialLogic.SortPlayerList)
        self:_RefreshView(bUpdate, Module.Social.Config.Loc.EmptyALL)
    end, self)

    -- azhengzheng:先拉取最近同玩
    self:_GetRecentPlayerList(callBack)
end

--好友页签
function InviteFriendList:_OnFriendBtnClick(bUpdate)
    -- azhengzheng:重构此处代码
    self.onlinePlayerList = self:_GetFriendList()
    table.sort(self.onlinePlayerList, SocialLogic.SortPlayerList)
    self:_RefreshView(bUpdate, Module.Social.Config.Loc.EmptyFriend)
end

--附近的人页签,清除里面的好友
function InviteFriendList:_OnNeighberBtnClick(bUpdate)
    self.onlinePlayerList = {}
    local neighberList = {}

    if table.isempty(self._neighberList) then
        if not hasdestroy(self._wtEmptySlot) then
            self._wtEmptySlot:SelfHitTestInvisible()
            if not hasdestroy(self._wtEmptyHint) then
                self._wtEmptyHint:BP_SetText(Module.Social.Config.Loc.EmptyNeighber)
            end
        end
        self._wtSGBFriendList:RefreshAllItems()
        return
    end
      
    local function fProcessFriendList()
        if self._lastTabIndex ~= 3 then
            return
        end
        for _,value in pairs(neighberList) do
            table.insert(self.onlinePlayerList, value)
        end
        table.sort(self.onlinePlayerList, SocialLogic.fSortByState)
        if bUpdate then
            self._wtSGBFriendList:RefreshVisibleItems()
        else
            self._wtSGBFriendList:RefreshAllItems()
        end
        if not hasdestroy(self._wtEmptySlot) then
            if table.isempty(self.onlinePlayerList) then
                self._wtEmptySlot:SelfHitTestInvisible()
                if not hasdestroy(self._wtEmptyHint) then
                    self._wtEmptyHint:BP_SetText(Module.Social.Config.Loc.EmptyNeighber)
                end
            else
                self._wtEmptySlot:Collapsed()
            end
        end
    end

    local fOnCSFriendPlayerListRes = function(playerList)
        if self._lastTabIndex ~= 3 then
            return
        end
        self.onlinePlayerList = {}
        -- 清除好友
        for _,value in pairs(playerList) do
            local id = value.player_id
            if id ~= Server.AccountServer:GetPlayerId() then
                neighberList[id] = nil
            end
        end
        fProcessFriendList()
    end

    local function fOnReqPlayerStateRes(self,newStateList)
        if self._lastTabIndex ~= 3 then
            return
        end
        for _,info in pairs(newStateList) do
            local id = info.player_id
            if self._neighberList[id] then
                neighberList[id] = self._neighberList[id]

                neighberList[id].state = info.state
                neighberList[id].fighting_time = info.fighting_time
                neighberList[id].member_num = info.member_num
                neighberList[id].team_id = info.team_id
                neighberList[id].mode_info = info.mode_info
                neighberList[id].mode_info_array = info.mode_info_array

                neighberList[id].source = TeamInviteSource.FromNeighbor
                neighberList[id].hideSource = true
            end
        end
        if not table.isempty(neighberList) then
            local friendList = Server.FriendServer:GetFriendPanelList()
            fOnCSFriendPlayerListRes(friendList)
            --Server.SocialServer:ReqFriendList(CreateCallBack(fOnCSFriendPlayerListRes,self))
        end
    end
    
    Server.SocialServer:ReqPlayerState(self._neighberIdList,CreateCallBack(fOnReqPlayerStateRes,self))
end

function InviteFriendList:_OnNearPlayerListGet(playerList)
    local neighberList = {}
    local neighberIdList = {}
    for i = 0,playerList:Num()-1 do
        local info = playerList:Get(i)
        if info then
            local playerSimpleInfo = {
                player_id = UGameplayBlueprintHelper.FStringToInt64(GetWorld(),info.openid),
                nick_name = info.userName,
                gender = tonumber(info.gender) == 1 and 0 or 1,
                pic_url = info.pictureUrl,
                distance = info.distance,
                level = 0,
            }
            if playerSimpleInfo.player_id ~= Server.AccountServer:GetPlayerId() and not Module.Friend:CheckIsBlack(playerSimpleInfo.player_id) then
                table.insert(neighberIdList, playerSimpleInfo.player_id)
                neighberList[playerSimpleInfo.player_id] = playerSimpleInfo
            end
        end
    end
    self._neighberList = neighberList
    self._neighberIdList = neighberIdList
    if self._lastTabIndex == 0 then
        self:_OnAllBtnClick()
    elseif self._lastTabIndex == 3 then
        self:_OnNeighberBtnClick()
    end
end

--最近的人页签
function InviteFriendList:_OnRecentBtnClick(bUpdate)
    -- azhengzheng:重构此处代码
    local callBack = function(recentPlayerList)
        if self._lastTabIndex ~= 2 then
            return
        end

        self.onlinePlayerList = recentPlayerList
        table.sort(self.onlinePlayerList, SocialLogic.SortPlayerList)
        self:_RefreshView(bUpdate, Module.Social.Config.Loc.EmptyRecent)
    end

    self:_GetRecentPlayerList(callBack)
end

-- azhengzheng:获取最近同玩
function InviteFriendList:_GetRecentPlayerList(callBack)
    local onCoPlayerList = CreateCallBack(function(self, res)
        if res.result ~= 0 then
            return
        end

        local recentPlayerList = {}
        local myPlayerID = Server.AccountServer:GetPlayerId()

        for _, value in pairs(res.player_list or {}) do
            if myPlayerID ~= value.player_id or not Module.Friend:CheckIsBlack(value.player_id) then
                value.finish_time = 0
                value.hideSource = true
                value.source = TeamInviteSource.FromRecent
                table.insert(recentPlayerList, value)
            end
        end

        for _, recentPlayer in pairs(recentPlayerList) do
            for _, playerInfo in pairs(res.player_infos) do
                if recentPlayer.player_id == playerInfo.player_info.player_id then
                    recentPlayer.room_id = playerInfo.room_id
                    recentPlayer.finish_time = playerInfo.finish_time
                    break
                end
            end
        end

        callBack(recentPlayerList)
    end, self)

    Server.SocialServer:ReqCoPlayerList(onCoPlayerList)
end

-- azhengzheng:获取好友
function InviteFriendList:_GetFriendList()
    local friendList = {}
    local myPlayerID = Server.AccountServer:GetPlayerId()

    for _, value in pairs(Server.FriendServer:GetFriendPanelList()) do
        if myPlayerID ~= value.player_id and not Module.Friend:CheckIsBlack(value.player_id) then
            value.hideSource = true
            value.source = TeamInviteSource.FromFriend
            table.insert(friendList, value)
        end
    end

    return friendList
end

-- azhengzheng:整合重复代码
function InviteFriendList:_RefreshView(bUpdate, text)
    if not hasdestroy(self._wtEmptySlot) then
        if table.isempty(self.onlinePlayerList) then
            self._wtEmptySlot:SelfHitTestInvisible()

            if not hasdestroy(self._wtEmptyHint) then
                self._wtEmptyHint:BP_SetText(text)
            end
        else
            self._wtEmptySlot:Collapsed()
        end
    end

    self:_UpdatePlayerListState(bUpdate)
end

function InviteFriendList:_UpdatePlayerList()
    loginfo('InviteFriendList:_UpdatePlayerList self._lastTabIndex = ', self._lastTabIndex)
    if self._lastTabIndex == 0 then
        self:_OnAllBtnClick(true)
        --Module.LBS:GetNearby()
    elseif self._lastTabIndex  == 1 then
        self:_OnFriendBtnClick(true)
    elseif self._lastTabIndex  == 2 then
        self:_OnRecentBtnClick(true)
    elseif self._lastTabIndex  == 3 then
        self:_OnNeighberBtnClick(true)
        --Module.LBS:GetNearby()
    end
end

function InviteFriendList:_UpdatePlayerListState(bUpdate)
    if self.onlinePlayerList then
        local bFinishedPulling = false
        local function fOnReqPlayerStateRes(self,bPullDown,newStateList)
            for _,info in pairs(newStateList) do
                for index, playerInfo in ipairs(self.onlinePlayerList) do
                    if playerInfo.player_id == info.player_id then
                        playerInfo.state = info.state
                        playerInfo.fighting_time = info.fighting_time
                        playerInfo.member_num = info.member_num
                        playerInfo.team_id = info.team_id
                        playerInfo.mode_info = info.mode_info
                        playerInfo.mode_info_array = info.mode_info_array
                        break
                    end
                end
            end
            if bPullDown == true then
                if bUpdate then
                    self._wtSGBFriendList:RefreshVisibleItems()
                else
                    self._wtSGBFriendList:RefreshAllItems()
                end
            end
        end
        local playerIdList = {}
        for index, playerInfo in ipairs(self.onlinePlayerList) do
            table.insert(playerIdList, playerInfo.player_id)
            if #playerIdList == 50 or index == #self.onlinePlayerList then
                if index == #self.onlinePlayerList then
                    bFinishedPulling = true
                end
                Server.SocialServer:ReqPlayerState(playerIdList,CreateCallBack(fOnReqPlayerStateRes,self,bFinishedPulling))
                table.empty(playerIdList)
            end
        end
    end
end

function InviteFriendList:PanelDestroy()
    Facade.UIManager:CloseUI(self)
end

function InviteFriendList:_OnTeamFace2FaceCreate()
    Facade.UIManager:CloseUI(self)
end

function InviteFriendList:_OnInviteBtnClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.InviteFaceToFacePanel,nil,nil)
end

function InviteFriendList:OnNavBack()
    self:PanelDestroy()
    return true
end

function InviteFriendList:_SetHide()
    self:Hide(true)
end

function InviteFriendList:_SetShow() -- 会在个人信息打开历史战绩, 打开历史战绩详情中个人信息关闭,这里会弹出来
    Timer.DelayCall(0.1, function()
        local currentStackUIId = Facade.UIManager:GetCurrentStackUIId()
        if currentStackUIId == UIName2ID.RecordDetailMPMainHD or
            currentStackUIId == UIName2ID.MPDetailRecord or
            currentStackUIId == UIName2ID.SolDetailMainPanelHD or
            currentStackUIId == UIName2ID.SolDetailRecord or
            currentStackUIId == UIName2ID.RaidDetailRecord or
            currentStackUIId == UIName2ID.ArenaDetailMainPanel or
            currentStackUIId == UIName2ID.ArenaDetailRecord then
            return
        end

        self:Show(false)
    end, self)
end

function InviteFriendList:_OnTeamCodeBtnClicked()
    Module.Recruit:OpenTeamCodePop()
end

return InviteFriendList
