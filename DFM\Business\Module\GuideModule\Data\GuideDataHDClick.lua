----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGuide)
----- LOG FUNCTION AUTO GENERATE END -----------



local GuideDataBase = require "DFM.Business.Module.GuideModule.Data.GuideDataBase"
local GuideConfig = require "DFM.Business.Module.GuideModule.GuideConfig"
local GuideLogic = require "DFM.Business.Module.GuideModule.GuideLogic"

---@class GuideDataHDClick : GuideDataBase
local GuideDataHDClick = class('GuideDataHDClick', GuideDataBase)

function GuideDataHDClick:Ctor()
    -- self._dialogHandle = nil
end

function GuideDataHDClick:Destroy()
    if Module.Guide then
        local cfgId = tonumber(self._guideCfg.Args)
        Module.Guide:CloseGuideHDClickUI(cfgId)
    end
end

function GuideDataHDClick:OnStartGuide()
    local cfgId = tonumber(self._guideCfg.Args)
    Module.Guide:OpenGuideHDClickUI(cfgId)
end

function GuideDataHDClick:OnEndGuide(idx)
    local cfgId = tonumber(self._guideCfg.Args)
    Module.Guide:CloseGuideHDClickUI(cfgId)
end

function GuideDataHDClick:OnPause(uiIns)
    local cfgId = tonumber(self._guideCfg.Args)
    Module.Guide:CommonSetGuideUIValidState(UIName2ID.GuideHDClickUI, cfgId, false)
end

function GuideDataHDClick:OnRestart()
    local cfgId = tonumber(self._guideCfg.Args)
    Module.Guide:CommonSetGuideUIValidState(UIName2ID.GuideHDClickUI, cfgId, true)
end

function GuideDataHDClick:OnInputTypeChanged(inputType)
    -- 重新定位一下, 比如BottonBar 位置会刷新
    local cfgId = tonumber(self._guideCfg.Args)
    Module.Guide:OpenGuideHDClickUI(cfgId)
end

return GuideDataHDClick