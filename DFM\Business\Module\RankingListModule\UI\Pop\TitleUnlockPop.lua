----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRankingList)
----- LOG FUNCTION AUTO GENERATE END -----------
local TitleUnlockPop = ui("TitleUnlockPop")

function TitleUnlockPop:Ctor()
    self._wtName = self:Wnd("DFTextBlock_156", UITextBlock)
    self._wtBG = self:Wnd("DFImage_341", UIImage)

    self._wtWaterfallScrollBox = UIUtil.WndWaterfallScrollBox(self, "DFWaterfallScrollView_194", self.OnGetItemCount,
        self._OnProcessWidget)

    self._wtBtn = self:Wnd("wtCommonButtonV1S1", DFCommonButtonOnly)
    self._wtBtn:Event("OnClicked", self.OnClickJump, self)
    self._wtSkipBtn = self:Wnd("WBP_Common_JumpOver_1", UIWidgetBase)
    self._wtSkipBtn:BindClickEvent(self.OnClickSkip, self)
    self.titleInfos = {}
end

function TitleUnlockPop:OnInitExtraData(bRole)
    self.bRole = bRole -- 是否是个人主页打开

    if bRole == false then
        self:AddEventListeners()
    end

    self:_LoadTitleData()
end

function TitleUnlockPop:OnOpen()
    self._wtSkipBtn:AddJumpInputAction()
end

function TitleUnlockPop:OnShowBegin()
    self._wtWaterfallScrollBox:RefreshAllItems()
    self:PlayUnlockAnimation()
    self:SetTitle()
    self:SetBg()
end

function TitleUnlockPop:OnClickJump()
    if self.bRole == false then
        Module.RoleInfo:ShowMainPanel()
    else
        self:ChangeRoleSub()
    end
end

function TitleUnlockPop:DelayChangeRoleSub()
    Timer.DelayCall(0.1, function()
        self:ChangeRoleSub()
    end)
end

function TitleUnlockPop:ChangeRoleSub()
    Module.RoleInfo.Config.Event.evtOpenSocialChangePanel:Invoke(3)
    Facade.UIManager:CloseUI(self)
end

function TitleUnlockPop:OnClickSkip()
    Facade.UIManager:CloseUI(self)
end

function TitleUnlockPop:SetBg()
    local bMp = Module.RoleInfo:IsInMp()
    local cfg = Module.RankingList.Config
    if bMp then
        self._wtBG:AsyncSetImagePath(cfg.SolUnlockTitleImg)
    else
        self._wtBG:AsyncSetImagePath(cfg.MpUnlockTitleImg)
    end
end

-- 称号id 区分级别
-- 国级：第5~6位为01，最后两位为01
-- 省级：第5~6位为01，最后两位非01
-- 市级：第5~6位为02
function TitleUnlockPop:FindTitleLevel()
    local maxLevel = 1
    local titles = self.titleInfos

    for i = 1, #titles do
        if maxLevel == 3 then break end

        local titleid = tostring(titles[i].AvatarID)
        if type(titleid) == "string" and #titleid >= 6 then
            local mid = titleid:sub(5, 6)
            if mid == "01" then
                maxLevel = (titleid:sub(-2) == "01") and 3 or math.max(maxLevel, 2)
            elseif mid == "02" then
                -- maxLevel remains at least 1
            end
        end
    end
    return maxLevel
end

function TitleUnlockPop:SetTitle()
    local id = self:FindTitleLevel()
    local loc = Module.RankingList.Config.Loc
    local strMap =
    {
        [1] = loc.CityTopPlayer,
        [2] = loc.ProvinceTopPlayer,
        [3] = loc.CNTopPlayer
    }
    self._wtName:SetText(strMap[id] or "")
end

function TitleUnlockPop:OnHideBegin()
    self:RemoveAllLuaEvent()
    self:PlayAnimation(self.WBP_RankingList_TitleUnlockPop_out, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
end

function TitleUnlockPop:OnGetItemCount()
    return #self.titleInfos
end

function TitleUnlockPop:_OnProcessWidget(index, widget)
    local info = self.titleInfos[index]
    local nickName = Server.RoleInfoServer.nickName or "Server Fetch NickName Failed"

    if widget then
        widget:UpdateInfo(nickName, info.AvatarID, info.AdCode, info.RankNo)
    end
end

function TitleUnlockPop:OnClose()
    self._wtSkipBtn:RemoveJumpInputAction()
end

function TitleUnlockPop:_LoadTitleData()
    local titleTable = Server.RoleInfoServer:GetTitleTbl()

    for _, value in ipairs(titleTable) do
        if value.UnlockPopType == 1 and value.Islock then
            table.insert(self.titleInfos, value)
        end
    end

    -- todo
    -- for _, value in ipairs(titleTable) do
    --     table.insert(self.titleInfos, value)
    --     table.insert(self.titleInfos, value)
    -- end

    table.sort(self.titleInfos, function(a, b)
        if a.OrderWeight == b.OrderWeight then
            return a.AvatarID > b.AvatarID
        else
            return a.OrderWeight > b.OrderWeight
        end
    end)
end

function TitleUnlockPop:PlayUnlockAnimation()
    local anim = IsHD() and self.WBP_RankingList_TitleUnlockPop_in_pc or self.WBP_RankingList_TitleUnlockPop_in_mobile
    self:PlayAnimation(anim, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
end

function TitleUnlockPop:AddEventListeners()
    self:AddLuaEvent(Module.RoleInfo.Config.Event.evtRoleMainPanelOpen, self.DelayChangeRoleSub, self)
end

function TitleUnlockPop:_EnableGamepadFeature(bEnable)
    if not IsHD() then
        return
    end
end

return TitleUnlockPop
