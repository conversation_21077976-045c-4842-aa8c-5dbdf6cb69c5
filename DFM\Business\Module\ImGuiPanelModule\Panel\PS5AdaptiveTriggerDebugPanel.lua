----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMImGuiPanel)
----- LOG FUNCTION AUTO GENERATE END -----------



local PS5AdaptiveTriggerDebugPanel = ImGuiPanelCls("PS5AdaptiveTriggerDebugPanel")

PS5AdaptiveTriggerDebugPanel.PanelWindowTitle = "PS5自适应扳机调试面板"

local UDFMAdaptiveTriggerManager = import "DFMAdaptiveTriggerManager"
local EWeaponItemType = import "EWeaponItemType"
local EGPWeaponFiringMode = import "EGPWeaponFiringMode"
local EAdaptiveTriggerEffectType = import "EAdaptiveTriggerEffectType"
local UHardwareParamHelper = import "HardwareParamHelper"

function PS5AdaptiveTriggerDebugPanel:OnCreatePanel()
    self.WeaponModeDict = {
        [EWeaponItemType.None] = "未装备武器",
        [EWeaponItemType.Rifle] = "突击步枪",
        [EWeaponItemType.Submachine] = "冲锋枪",
        [EWeaponItemType.Shotgun] = "霰弹枪",
        [EWeaponItemType.LightMachine] = "轻机枪",
        [EWeaponItemType.PrecisionShootingRifle] = "精确射击步枪",
        [EWeaponItemType.Sniper] = "狙击步枪",
        [EWeaponItemType.Pistol] = "手枪",
        [EWeaponItemType.Heavy] = "重武器",
        [EWeaponItemType.Melee] = "近战",
        [EWeaponItemType.EmptyHand] = "空手",
        [EWeaponItemType.VehicleGun] = "载具武器",

        [EWeaponItemType.Bow] = "弓",
        [EWeaponItemType.BlastShield] = "防爆盾",
        [EWeaponItemType.CompoundBow] = "复合弓",
        [EWeaponItemType.HeavyMachineGun] = "机枪塔",
        [EWeaponItemType.FlameThrower] = "火焰喷射器",
    }

    self.FireModeDict = {
        [EGPWeaponFiringMode.Auto] = "全自动",
        [EGPWeaponFiringMode.Single] = "单发",
        [EGPWeaponFiringMode.AutoBurst] = "全自动Burst",
        [EGPWeaponFiringMode.Burst] = "多发Burst",
        [EGPWeaponFiringMode.ThrowHigh] = "高抛",
        [EGPWeaponFiringMode.ThrowLow] = "低抛",
    }

    self.TriggerEffectDict = {
        [EAdaptiveTriggerEffectType.Off] = "Off",
        [EAdaptiveTriggerEffectType.Feedback] = "Feedback",
        [EAdaptiveTriggerEffectType.MultiPositionFeedback] = "MultiPositionFeedback",
        [EAdaptiveTriggerEffectType.SlopeFeedback] = "SlopeFeedback",
        [EAdaptiveTriggerEffectType.Weapon] = "Weapon",
        [EAdaptiveTriggerEffectType.Vibration] = "Vibration",
        [EAdaptiveTriggerEffectType.MultiPositionVibration] = "MultiPositionVibration",
    }

    self.AdaptiveTriggerManager = UDFMAdaptiveTriggerManager.Get(GetGameInstance())
    self.bShouldRefreshEffect = false

    self.currentWeaponGuid = 0
    self.currentFireMode = EGPWeaponFiringMode.ThrowHigh
    if self.AdaptiveTriggerManager ~= nil then
        self.currentWeaponGuid = self.AdaptiveTriggerManager:DebugGetCurrentWeaponGuid()
        self.currentFireMode = self.AdaptiveTriggerManager:DebugGetCurrentWeaponFireMode()
    end
end

function PS5AdaptiveTriggerDebugPanel:OnGUI()
    if self.AdaptiveTriggerManager == nil then
        self.AdaptiveTriggerManager = UDFMAdaptiveTriggerManager.Get(GetGameInstance())
    end

    if self.AdaptiveTriggerManager == nil then
        ImGui.Text("当前平台不支持自适应扳机功能!!!")
        return
    end

    local WeaponType = self.AdaptiveTriggerManager:DebugGetCurrentWeaponType()
    local FireMode = self.AdaptiveTriggerManager:DebugGetCurrentWeaponFireMode()
    ImGui.Text("武器Id : %s",self.AdaptiveTriggerManager:DebugGetCurrentWeaponId())
    ImGui.Text("武器类型 ： %s",self.WeaponModeDict[WeaponType])
    ImGui.Text("开火类型 ： %s",self.FireModeDict[FireMode])

    ImGui.NewLine()
    ImGui.Separator()

    local changed = ImGui.EditConsoleVar("Rumble.UseDebugAdaptiveTriggerConfig", "是否启用自适应扳机手感参数调试")
    ImGui.Separator()
    local bDebugEnabled = UHardwareParamHelper.GetConsoleVariableRefBoolValue("Rumble.UseDebugAdaptiveTriggerConfig")

    if changed == true then
        if bDebugEnabled then
            self.AdaptiveTriggerManager:DebugLoadCurrentWeaponAdaptiveTriggerEffect()
        end
        if self.AdaptiveTriggerManager.bEnablePostProcessControllerData then
            self.AdaptiveTriggerManager:DebugRefreshAdaptiveTriggerEffect()
        end
    end

    ImGui.BeginChild("EditInspectField",ImVec2(500,600))
    if bDebugEnabled then
        self:EditDebugConfig()
    else
        ImGui.PushStyleColor(ImGui.ImGuiCol_FrameBg,ImVec4(0.5,0.5,0.5,1))
        self:InspectCurrentWeaponConfig()
        ImGui.PopStyleColor(1)
    end
    ImGui.EndChild()
end

function PS5AdaptiveTriggerDebugPanel:InspectCurrentWeaponConfig()
    if self.AdaptiveTriggerManager.bEnablePostProcessControllerData == false then
        ImGui.TextColored(ImVec4(1,0,0,1),"自适应扳机效果屏蔽中...")
    end

    --- 手动加载武器上自适应扳机的配置用于微调
    local CurrentAdaptiveTriggerEffect = self.AdaptiveTriggerManager.CurrentAdaptiveTriggerEffect

    --- 显示当前自适应扳机的开火扳机触发阈值
    ImGui.BulletText("扳机深度")
    ImGui.SliderFloat("开火扳机触发深度", CurrentAdaptiveTriggerEffect.FireTriggerPressDepth,0,1)
    ImGui.SliderFloat("解除开火扳机触发深度", CurrentAdaptiveTriggerEffect.FireTriggerReleaseDepth,0,1)
    ImGui.SliderFloat("扳机深度", self.AdaptiveTriggerManager.DebugTriggerDepth,0,1)


    ImGui.BulletText("自适应扳机反馈")
    ImGui.Text("自适应扳机预设类型 : %s",self.TriggerEffectDict[CurrentAdaptiveTriggerEffect.TriggerEffectType])


    local TriggerEffectType = CurrentAdaptiveTriggerEffect.TriggerEffectType
    -- --- 根据不同的自适应扳机的预设类型来编辑不同的配置数据
    if TriggerEffectType == EAdaptiveTriggerEffectType.Off then
        ImGui.Text("未设定自适应扳机预设类型")

    elseif TriggerEffectType == EAdaptiveTriggerEffectType.Feedback then
        local config = CurrentAdaptiveTriggerEffect.Feedback
        ImGui.SliderInt("Position", config.Position,0, 9)
        ImGui.SliderInt("Strength", config.Strength,0, 8)
    
    elseif TriggerEffectType == EAdaptiveTriggerEffectType.MultiPositionFeedback then
        local config = CurrentAdaptiveTriggerEffect.MultiPositionFeedback
        for k,v in pairs(config.Strengths) do
            local label= string.format("Strengths %s",k)
            ImGui.SliderInt(label, v, 0, 8)
        end

    elseif TriggerEffectType == EAdaptiveTriggerEffectType.SlopeFeedback then
        local config = CurrentAdaptiveTriggerEffect.SlopeFeedback
        ImGui.SliderInt("StartPosition", config.StartPosition,0, 8)
        ImGui.SliderInt("EndPosition", config.EndPosition,1, 9)
        ImGui.SliderInt("StartStrength", config.StartStrength,1, 8)
        ImGui.SliderInt("EndStrength", config.EndStrength,1, 8)

    elseif TriggerEffectType == EAdaptiveTriggerEffectType.Weapon then
        local config = CurrentAdaptiveTriggerEffect.Weapon
        ImGui.SliderInt("StartPosition", config.StartPosition,2, 7)
        ImGui.SliderInt("EndPosition", config.EndPosition,3, 8)
        ImGui.SliderInt("Strength", config.Strength,0, 8)

    elseif TriggerEffectType == EAdaptiveTriggerEffectType.Vibration then
        local config = CurrentAdaptiveTriggerEffect.Vibration
        ImGui.SliderInt("Position", config.Position,0, 9)
        ImGui.SliderInt("Amplitude", config.Amplitude,0, 8)
        ImGui.SliderInt("Frequency", config.Frequency,0, 255)

    elseif TriggerEffectType == EAdaptiveTriggerEffectType.MultiPositionVibration then
        local config = CurrentAdaptiveTriggerEffect.MultiPositionVibration
        for k,v in pairs(config.Amplitudes) do
            local label= string.format("Amplitudes %s",k)
            ImGui.SliderInt(label, v, 0, 8)
        end

    end

end

function PS5AdaptiveTriggerDebugPanel:WatchImGuiResult(changed, ...)
    if changed then
        self.bShouldRefreshEffect = true
    end
    return ...
end

function PS5AdaptiveTriggerDebugPanel:EditDebugConfig()
    if self.AdaptiveTriggerManager.bEnablePostProcessControllerData == false then
        ImGui.TextColored(ImVec4(1,0,0,1),"自适应扳机效果屏蔽中...")
    end

    --- 根据武器是否变更或者开火模式变更自动加载对应的开火模式
    local currentWeaponGuid = self.AdaptiveTriggerManager:DebugGetCurrentWeaponGuid()
    local currentFireMode = self.AdaptiveTriggerManager:DebugGetCurrentWeaponFireMode()
    if currentWeaponGuid ~= self.currentWeaponGuid then
        self.currentWeaponGuid = currentWeaponGuid
        ImGui.ToastInfo("检测到武器变更，重置为新武器的配置")
        self.AdaptiveTriggerManager:DebugLoadCurrentWeaponAdaptiveTriggerEffect()
        if self.AdaptiveTriggerManager.bEnablePostProcessControllerData then
            self.AdaptiveTriggerManager:DebugRefreshAdaptiveTriggerEffect()
        end
    end

    if currentFireMode ~= self.currentFireMode then
        self.currentFireMode = currentFireMode
        ImGui.ToastInfo("检测到开火模式变更，重置为新开火模式的配置")
        self.AdaptiveTriggerManager:DebugLoadCurrentWeaponAdaptiveTriggerEffect()
        if self.AdaptiveTriggerManager.bEnablePostProcessControllerData then
            self.AdaptiveTriggerManager:DebugRefreshAdaptiveTriggerEffect()
        end
    end


    local DebugAdaptiveTriggerConfig = self.AdaptiveTriggerManager.DebugAdaptiveTriggerEffect

    self.bShouldRefreshEffect = false
    local changed = false

    --- 编辑当前自适应扳机的开火扳机触发阈值
    ImGui.BulletText("扳机深度")
    changed,DebugAdaptiveTriggerConfig.FireTriggerPressDepth = ImGui.SliderFloat("开火扳机触发深度", DebugAdaptiveTriggerConfig.FireTriggerPressDepth,0,1)
    changed,DebugAdaptiveTriggerConfig.FireTriggerReleaseDepth = ImGui.SliderFloat("解除开火扳机触发深度", DebugAdaptiveTriggerConfig.FireTriggerReleaseDepth,0,1)
    if DebugAdaptiveTriggerConfig.FireTriggerReleaseDepth > DebugAdaptiveTriggerConfig.FireTriggerPressDepth then
        DebugAdaptiveTriggerConfig.FireTriggerReleaseDepth = DebugAdaptiveTriggerConfig.FireTriggerPressDepth
    end
    ImGui.SliderFloat("扳机深度", self.AdaptiveTriggerManager.DebugTriggerDepth,0,1)

    --- 调整自适应扳机的预设类型
    ImGui.BulletText("自适应扳机反馈")
    local PrevTriggerEffectType = DebugAdaptiveTriggerConfig.TriggerEffectType
    if ImGui.BeginCombo("自适应扳机预设类型",self.TriggerEffectDict[DebugAdaptiveTriggerConfig.TriggerEffectType]) then
        for k,v in pairs(self.TriggerEffectDict) do
            if ImGui.Selectable(v,k == DebugAdaptiveTriggerConfig.TriggerEffectType) then
                DebugAdaptiveTriggerConfig.TriggerEffectType = k
            end
        end
        ImGui.EndCombo()
    end

    local TriggerEffectType = DebugAdaptiveTriggerConfig.TriggerEffectType

    if PrevTriggerEffectType ~= TriggerEffectType then
        self.bShouldRefreshEffect = true
    end

    --- 根据不同的自适应扳机的预设类型来编辑不同的配置数据
    if TriggerEffectType == EAdaptiveTriggerEffectType.Off then
        ImGui.Text("未设定自适应扳机预设类型")

    elseif TriggerEffectType == EAdaptiveTriggerEffectType.Feedback then
        local config = DebugAdaptiveTriggerConfig.Feedback
        config.Position = self:WatchImGuiResult(ImGui.SliderInt("Position", config.Position,0, 9))
        config.Strength = self:WatchImGuiResult(ImGui.SliderInt("Strength", config.Strength,0, 8))
    
    elseif TriggerEffectType == EAdaptiveTriggerEffectType.MultiPositionFeedback then
        local config = DebugAdaptiveTriggerConfig.MultiPositionFeedback
        for k,v in pairs(config.Strengths) do
            local label= string.format("Strengths %s",k)
            local newV = self:WatchImGuiResult(ImGui.SliderInt(label, v, 0, 8))
            if newV ~= v then
                config.Strengths:Set(k, newV)
            end
        end

    elseif TriggerEffectType == EAdaptiveTriggerEffectType.SlopeFeedback then
        local config = DebugAdaptiveTriggerConfig.SlopeFeedback
        config.StartPosition = self:WatchImGuiResult(ImGui.SliderInt("StartPosition", config.StartPosition,0, 8))
        config.EndPosition = self:WatchImGuiResult(ImGui.SliderInt("EndPosition", config.EndPosition,1, 9))
        config.StartStrength = self:WatchImGuiResult(ImGui.SliderInt("StartStrength", config.StartStrength,1, 8))
        config.EndStrength = self:WatchImGuiResult(ImGui.SliderInt("EndStrength", config.EndStrength,1, 8))

    elseif TriggerEffectType == EAdaptiveTriggerEffectType.Weapon then
        local config = DebugAdaptiveTriggerConfig.Weapon
        config.StartPosition = self:WatchImGuiResult(ImGui.SliderInt("StartPosition", config.StartPosition,2, 7))
        config.EndPosition = self:WatchImGuiResult(ImGui.SliderInt("EndPosition", config.EndPosition,3, 8))
        config.Strength = self:WatchImGuiResult(ImGui.SliderInt("Strength", config.Strength,0, 8))

    elseif TriggerEffectType == EAdaptiveTriggerEffectType.Vibration then
        local config = DebugAdaptiveTriggerConfig.Vibration
        config.Position = self:WatchImGuiResult(ImGui.SliderInt("Position", config.Position,0, 9))
        config.Amplitude = self:WatchImGuiResult(ImGui.SliderInt("Amplitude", config.Amplitude,0, 8))
        config.Frequency = self:WatchImGuiResult(ImGui.SliderInt("Frequency", config.Frequency,0, 255))

    elseif TriggerEffectType == EAdaptiveTriggerEffectType.MultiPositionVibration then
        local config = DebugAdaptiveTriggerConfig.MultiPositionVibration
        for k,v in pairs(config.Amplitudes) do
            local label= string.format("Amplitudes %s",k)
            local newV = self:WatchImGuiResult(ImGui.SliderInt(label, v, 0, 8))
            if newV ~= v then
                config.Amplitudes:Set(k, newV)
            end
        end

    end

    --- 如果控件值有刷新，重刷一遍自适应扳机配置至手柄上
    if self.bShouldRefreshEffect and self.AdaptiveTriggerManager.bEnablePostProcessControllerData then
        self.AdaptiveTriggerManager:DebugRefreshAdaptiveTriggerEffect()
    end

    --- 手动加载武器上自适应扳机的配置用于微调
    ImGui.NewLine()
    ImGui.NewLine()
    ImGui.Separator()
    if ImGui.Button("重   置",ImVec2(500,50)) then
        self.AdaptiveTriggerManager:DebugLoadCurrentWeaponAdaptiveTriggerEffect()
        if self.AdaptiveTriggerManager.bEnablePostProcessControllerData then
            self.AdaptiveTriggerManager:DebugRefreshAdaptiveTriggerEffect()
        end
    end
end


return PS5AdaptiveTriggerDebugPanel