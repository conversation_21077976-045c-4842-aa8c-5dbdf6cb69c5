----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMPlayerReturn)
----- LOG FUNCTION AUTO GENERATE END -----------



-- 回流活动SOL/MP大厅签到+引导到活动主界面弹窗

local PlayerReturnConfig        = require "DFM.Business.Module.PlayerReturnModule.PlayerReturnConfig" 
local PlayerReturnStatistics    = require "DFM.Business.Module.PlayerReturnModule.PlayerReturnStatistics"
local InputSummaryItemHD        = require "DFM.Business.Module.CommonBarModule.UI.BottomBarHD.InputSummaryItemHD"
local GuideConfig               = require "DFM.Business.Module.GuideModule.GuideConfig"
local AnimManager               = require "DFM.Business.DataStruct.Common.Base.DFUtil.AnimManager"
local Promise                   = require "DFM.Business.DataStruct.Common.Base.Promise"
local InputBindingAgent       = require "DFM.Business.DataStruct.Common.Agent.InputBindingAgent"
local OnceListener              = require "DFM.Business.DataStruct.Common.Agent.Event.OnceListener"
local PlayerReturnConfig        = require "DFM.Business.Module.PlayerReturnModule.PlayerReturnConfig"
local IPlayerReturnSubActivity  = require "DFM.Business.Module.PlayerReturnModule.SubActivities.PlayerReturnSubActivities"
local WidgetUtil                = require "DFM.YxFramework.Util.WidgetUtil"


---@class PlayerReturnLobbyPopup : LuaUIBaseView, HasAnimManager
local PlayerReturnLobbyPopup = ui("PlayerReturnLobbyPopup")

function PlayerReturnLobbyPopup:Ctor()
    -- 初始化辅助功能
    self._inputMgr    = InputBindingAgent.New(self)
    self._animManager = AnimManager.Create(self)

    -- 弹窗流程管理相关Promise
    self._popupPromise = nil            ---@type Promise
    self._interruptPromise = nil        ---@type Promise

    -- 绑定控件
    self._wtModeTag         = self:Wnd("WBP_Activity_ModeTag"           , UIWidgetBase)
    self._wtTitle           = self:Wnd("DFTextBlock_72"                 , UITextBlock)
    self._wtFxText          = self:Wnd("DFTextBlock_114"                , UITextBlock)
    self._wtDayCountLabel   = self:Wnd("DFTextBlock_213"                , UITextBlock)
    self._wtPlayerName      = self:Wnd("DFTextBlock"                    , UITextBlock)
    self._wtWelcomeMessage  = self:Wnd("DFTextBlock_1"                  , UITextBlock)
    self._wtRewardsList     = self:Wnd("DFHorizontalBox_68"             , UIWidgetBase)
    self._wtRewardsPanel    = self:Wnd("DFCanvasPanel_414"              , UIWidgetBase)
    self._wtConfirmButton   = self:Wnd("WBP_DFCommonButtonV1S3"         , DFCommonButtonOnly)
    self._wtHDBackKeyHint   = self:Wnd("WBP_TopBarHD_InputSummary"      , InputSummaryItemHD)
    self._wtCancelButton    = self:Wnd("wtCommonButtonClose"            , UIButton)
    
    -- 绑定事件
    self._wtConfirmButton:Event("OnClicked", self._OnConfirm, self)
end

---------------------------------------------------------------------------------------------
--#region 弹窗流程管理

---@param popupPromise      Promise
---@param interruptPromise  Promise
function PlayerReturnLobbyPopup:SetPopupPromises(popupPromise, interruptPromise)
    self._popupPromise     = popupPromise
    self._interruptPromise = interruptPromise
    if self._interruptPromise then
        self._interruptPromise:Then(CreateCPlusCallBack(self._OnInterrupt, self))
    end
end

---弹窗被打断
function PlayerReturnLobbyPopup:_OnInterrupt()
    Module.PlayerReturn:CloseLobbyPopup()
    -- 非正常关闭视为没有弹窗，下次继续弹窗
    local currMode = Server.ArmedForceServer:GetCurArmedForceMode()
    ---@type PlayerReturnSignInImpl
    local actv = IPlayerReturnSubActivity.Get(ReturnActivityType.ReturnActivityTypeAttend, currMode)
    actv:SetLastPopupTime(0)
    
    if self._popupPromise then
        self._popupPromise:Resolve({reply = EPopupReply.Continue, success = false})
    end
end

---用户取消本弹窗
---弹对局爽打弹窗，视用户选择，跳转到开局界面或继续弹窗流程，报告结果
function PlayerReturnLobbyPopup:_OnCancel()
    -- MP首日，无论用户如何选择都相当于确认领取，确保拿到经验和积分buff
    if (self._day == 1) and Server.ArmedForceServer:GetCurArmedForceMode() == EArmedForceMode.MP then
        self:_OnConfirm()
        return
    end

    Module.PlayerReturn:CloseLobbyPopup()
    Module.PopupSequencer:DoPopupSequence(nil, nil, {"PlayerReturnDailyMatchPopup"}):Then(
        function(reply)
            -- 由对局爽打弹窗决定结算的去向
            reply.success = true -- 签到弹窗状态
            if self._popupPromise then
                self._popupPromise:Resolve(reply)
            end
        end
    )
end

---用户确认本弹窗
---进行签到，然后弹对局爽打弹窗，视用户选择，跳转到回流界面或开局界面，报告结果
function PlayerReturnLobbyPopup:_OnConfirm()
    -- 关闭，报告弹窗结果
    Module.PlayerReturn:CloseLobbyPopup()

    -- 完成签到
    local actv_id = self._activityInfo.actv_id
    Server.ActivityServer:SendSignAwardReq(actv_id, self._day,
        -- 等待签到完毕，展示获得奖励弹窗
        ---@param res pb_CSActivityRecvAttendAwardRes
        function(res)
            Module.Activity:ShowDataChangeAwards(res.data_change, actv_id)
            Module.PlayerReturn:ShowTipsIfBoostReceived(res.data_change)
            Server.ActivityServer:InitActivityInfo({actv_id}, true, nil, true)
           
            OnceListener.New({Module.Reward.Config.Events.evtCloseRewardPanel},
                -- 等待奖励弹窗完毕，展示对局爽打弹窗
                function()
                    Module.PopupSequencer:ShowPopup("PlayerReturnDailyMatchPopup"):Then(
                        function(reply)
                            if reply.reply == EPopupReply.Continue then
                                -- 弹窗流程继续，则前往回流界面
                                local enterPanel = (self._day == 1) and ReturnActivityType.ReturnActivityTypeNormalTask or ReturnActivityType.ReturnActivityTypeMainPage
                                Module.PlayerReturn:ShowMainPanel(false, enterPanel)
                                if self._popupPromise then
                                    self._popupPromise:Resolve({reply = EPopupReply.Jump, success = true})
                                end
                            else
                                -- 对局爽打弹窗已跳转，透传弹窗结算结果到上一层
                                if self._popupPromise then
                                    self._popupPromise:Resolve({reply = reply, success = true})
                                end
                            end
                        end
                    )
                end
            )
        end
    , 1)
end

---返回：视为取消
function PlayerReturnLobbyPopup:OnNavBack()
    self:_OnCancel()
    return false
end

--#endregion
---------------------------------------------------------------------------------------------

function PlayerReturnLobbyPopup:OnOpen()
    local currentMode   = Server.ArmedForceServer:GetCurArmedForceMode()
    local actv          = IPlayerReturnSubActivity.Get(ReturnActivityType.ReturnActivityTypeAttend, currentMode) ---@type PlayerReturnSignInImpl
    local activityInfo  = actv:GetActivityInfo()
    self._activityInfo  = activityInfo
    self._day           = actv:GetPopupDay()

    -- 数据错误，按弹窗被打断处理，关闭且不记录已弹窗
    -- OnShowBegin处理，OnOpen中关闭不了, 先标记
    if (not activityInfo) or (not activityInfo.attend_info.states[self._day]) then
        self._day = nil
        self._activityInfo = nil
        logerror("[PlayerReturnLobbyPopup] Data missing, will close immediately OnShowBegin")
        return
    end
    
    self._wtPlayerName:SetText(Server.RoleInfoServer.nickName .. ":")                           --玩家名称
    self._wtWelcomeMessage:SetText(activityInfo.desc)                                           --回流文案
    self._wtTitle:SetText(activityInfo.name)                                                    --标题
    self._wtConfirmButton:SetMainTitle(PlayerReturnConfig.Localization.LobbyPopup.Claim)	   --领取按键

    --奖励列表
    ---@type IVCommonItemTemplate[]
    self._wtRewardsListItems = {}                                                               

    local items = {}
    for i, award in ipairs(activityInfo.attend_info.states[self._day].daily_award) do
        items[i] = ItemBase:NewIns(award.prop.id, award.prop.num, award.prop.gid)
    end

    local size = IsHD() and 256 or 192
    for i = 1, #items do
        local weakIns, insID = Facade.UIManager:AddSubUI(self, UIName2ID.IVCommonItemTemplate, self._wtRewardsList)
        self._wtRewardsListItems[i] = getfromweak(weakIns)
        self._wtRewardsListItems[i]:InitItem(items[i])
        self._wtRewardsListItems[i]:SetSize(size, size)
    end
end

---入场动画
---@return Promise2
function PlayerReturnLobbyPopup:DoAnimation(bPlayVideo)
    local p = Promise:NewIns()

    -- 由于视频控件的问题，同一帧播放并打断，音频不会停止
    -- 所以只能提前等待一小段时间，看是否有打断事件进来
    -- 自身处理完打断事件之后，self:IsValid() 这一步将会阻止视频播放和后续实际展示流程
    Timer.DelayCall(0.05, CreateCallBack(function() 
        if not self:IsValid() then
            p:Resolve()
            return
        end

        if bPlayVideo then
            Module.Guide.Field:AddPauseGuideUINum("PlayerReturnLobbyPopup")
    
            local t = os.time()
            local videoLength = -1000
    
            ---@diagnostic disable-next-line: missing-parameter
            Module.CommonWidget:ShowFullScreenVideoView("Activity_Return_She3", false, true,
                -- fOnMediaPlayEnd
                function()
                    ---@type PlayerReturnSignInImpl
                    local actv = IPlayerReturnSubActivity.Get(ReturnActivityType.ReturnActivityTypeAttend)
                    actv:SetIntroVideoPlayed(true)
    
                    -- 上报，播放时间和视频时间相差不到 1 秒视为完播，时间固定为 99
                    -- 否则上报时间为播放的秒数
                    local statID
                    local playedLength = os.time() - t
                    if math.abs(videoLength - playedLength) < 1 then
                        statID = PlayerReturnStatistics:GetCGTimeStatID(99)
                    else
                        statID = PlayerReturnStatistics:GetCGTimeStatID(playedLength)
                    end
                    LogAnalysisTool.SignButtonClicked(statID)
                    p:Resolve()
                end,
                nil, nil, nil, nil, 
                function(len) videoLength = len end -- fGetVideoLength
            )
        else
            Facade.SoundManager:PlayUIAudioEvent(PlayerReturnConfig.Audio.LobbyPopupNormal)
            p:Resolve()
        end
    end, self))

    return p
end

function PlayerReturnLobbyPopup:SetUpInputBinding()
    -- 初始化输入绑定

    -- 确认
    self._inputMgr:AddBinding(
        "ConfirmBtn", -- id
        {
            actionName      = "Common_ButtonLeft_Gamepad", -- X键
            callback        = self._OnConfirm,
            caller          = self,
            displayPriority = EDisplayInputActionPriority.UI_Pop
        }
    )
    self._wtConfirmButton:SetDisplayInputAction("Common_ButtonLeft_Gamepad", nil, nil, true)

    -- 跳过
    self._inputMgr:AddBinding(
        "Skip", -- id
        {
            actionName      = "JumpOver", -- A键
            callback        = self._OnCancel,
            caller          = self,
            displayPriority = EDisplayInputActionPriority.UI_Pop,
        }
    )
end

function PlayerReturnLobbyPopup:OnShowBegin()
    -- 需要先隐藏，等待视频播放结束后再显示，否则会先漏出界面
    self:Hidden()

    if (not self._activityInfo) or (not self._day) then
        logerror("[PlayerReturnLobbyPopup] OnShowBegin: Data missing, will close immediately")
        self:_OnInterrupt()
        return
    end

    LogAnalysisTool.SignButtonClicked(PlayerReturnStatistics:GetLobbyPopupStatID())

    ---@type PlayerReturnSignInImpl
    local actv = IPlayerReturnSubActivity.Get(ReturnActivityType.ReturnActivityTypeAttend, Server.ArmedForceServer:GetCurArmedForceMode())
    self._bPlayVideo = (self._day == 1) and (not actv:IsIntroVideoPlayed())

    local p = self:DoAnimation(self._bPlayVideo)
    p:Then(
        function()
            self:SelfHitTestInvisible()

            for _, itemWidget in pairs(self._wtRewardsListItems) do
                itemWidget:PlayIVAnimation("WBP_CommonItemTemplate_in_special_01", 0)
            end

            if self._day == 1 then
                self:PlayAnimThen(self.WBP_Reflow_PopWindow_in)
            else
                self:PlayAnimThen(self.WBP_Reflow_PopWindow_in_02)
            end
            Facade.SoundManager:PlayUIAudioEvent("Voice_She3_Return_5")
            self:SetUpInputBinding()
            actv:SetLastPopupTime(Facade.ClockManager:GetLocalTimestamp())
        end
    )

    if IsHD() then
        local cb = CreateCallBack(self._OnCancel, self)
        self._wtHDBackKeyHint:SetData("JumpOver", cb) -- 在Ctor调用文本显示会不对，只能放到这里
    else
        self._wtCancelButton:Event("OnClicked", self._OnCancel, self)
    end
end

function PlayerReturnLobbyPopup:OnHideBegin()
    self._inputMgr:ClearAll()
    Facade.SoundManager:StopUIAudioEvent("Voice_She3_Return_5")
end

function PlayerReturnLobbyPopup:OnClose()
    self:RemoveAllLuaEvent()
    Module.Guide.Field:ReducePauseGuideUINum("PlayerReturnLobbyPopup")
end

return PlayerReturnLobbyPopup
