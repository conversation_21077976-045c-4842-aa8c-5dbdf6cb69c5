----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSettlement)
----- LOG FUNCTION AUTO GENERATE END -----------



local EvacuationResultInfo = ui("EvacuationResultInfo")

local UKismetSystemLibrary = import "KismetSystemLibrary"
local UDFMSettlementManager = import "DFMSettlementManager"
local UGameplayBlueprintHelper = import "GameplayBlueprintHelper"
local settlementMgrIns = UDFMSettlementManager.Get(GetWorld())

local SettlementLogic = require "DFM.Business.Module.SettlementModule.SettlementLogic"
local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
-- END MODIFICATION

local achievementTable = Facade.TableManager:GetTable("Achievement")
local rankingMinorRanksTable = Facade.TableManager:GetTable("RankingMinorRanks")
local matchModeDataConfigTable = Facade.TableManager:GetTable("MatchModeDataConfig")
local rankingRankConstantsTable = Facade.TableManager:GetTable("RankingRankConstants")

local UHighlightMomentMgr = import "HighlightMomentMgr"
local FHighlightMomentSOLSettlementInfoFromView = import "HighlightMomentSOLSettlementInfoFromView"
function EvacuationResultInfo:Ctor()
    self._highLightKillList = {}
    self._highLighAchievementsList = {}

    self._wtMapInfoCP = self:Wnd("wtMapInfoCP", UIWidgetBase)
    self._wtMapDifficultyTB = self:Wnd("wtMapDifficultyTB", UITextBlock)
    self._wtMapNameTB = self:Wnd("wtMapNameTB", UITextBlock)

    self._wtResultTB = self:Wnd("wtResultTB", UITextBlock)
    self._wtPlaceOrDeathTipCP = self:Wnd("wtPlaceOrDeathTipCP", UIWidgetBase)
    self._wtTitleTB = self:Wnd("wtTitleTB", UITextBlock)
    self._wtDescTB = self:Wnd("wtDescTB", UITextBlock)
    self._wtPlayTimeTB = self:Wnd("wtPlayTimeTB", UITextBlock)

    -- self._wtMandelCP = self:Wnd("wtMandelCP", UIWidgetBase)
    self._wtMandelPSB = self:Wnd("wtMandelPSB", UIWidgetBase)

    self._wtCarryOutProfitPrice = self:Wnd("wtCarryOutProfitPrice", UITextBlock)

    self._wtGains = self:Wnd("wtGains", DFCommonButtonOnly)
    self._wtGains:Event("OnClicked", self._OpenSolGainDetailClicked, self)

    self._wtOpenKillDetailCBO = self:Wnd("wtOpenKillDetailCBO", DFCommonButtonOnly)
    self._wtOpenKillDetailCBO:Event("OnClicked", self._OnOpenKillDetailClicked, self)
    self._wtKillCountTB = self:Wnd("wtKillCountTB", UITextBlock)

    self._wtHighLightHB = self:Wnd("wtHighLightHB", UIWidgetBase)
    self._wtHighLightTitleTB = self:Wnd("wtHighLightTitleTB", UITextBlock)
    self._wtChangeHighLightWB = self:Wnd("wtChangeHighLightWB", DFCommonButtonOnly)
    self._wtChangeHighLightWB:Event("OnClicked", self._OnChangeHighLightClicked, self)
    self._wtHightLightKillSB = self:Wnd("wtHightLightKillSB", UIWidgetBase)
    self._wtHighLightAchievementSGB = UIUtil.WndScrollGridBox(self, "wtHighLightAchievementSGB", self._OnGetHighLightAchievementsCount, self._OnProcessHighLightAchievementsWidget)

    if IsHD() then
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.Hidden)
    else
        self._wtFlowBtnHB = self:Wnd("wtFlowBtnHB", UIWidgetBase)
        self._wtPreFlowWB = self:Wnd("wtPreFlowWB", DFCommonButtonOnly)
        self._wtNextFlowWB = self:Wnd("wtNextFlowWB", DFCommonButtonOnly)
        self._wtNextFlowWB:Event("OnClicked", self._OnNextFlowClicked, self)
        self._wtEvacuateTeachingWB = self:Wnd("wtEvacuateTeachingWB", DFCommonButtonOnly)
        self._wtEvacuateTeachingWB:Event("OnClicked", self._OnPlayEvacuateTeachingBtnClick, self)
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.Hidden)
        -- Module.CommonBar:ShowTopBar()
    end
    ---@type HeroCommonCardBase
    self._wtHeroCardWB = self:Wnd("wtHeroCardWB", HeroCommonCardBase)

    self._wtShareWB = self:Wnd("wtShareWB", UIWidgetBase)
    self._wtShareTipCP = self._wtShareWB:Wnd("DFCanvasPanel_ShareTips", UIWidgetBase)
    self._wtShareTip = self._wtShareWB:Wnd("RichTextBlock", UITextBlock)
    self._wtShareBtn = self._wtShareWB:Wnd("wtShareBtn", DFCommonButtonOnly)
    self._wtShareBtn:Event("OnClicked", self._OnShareBtnClicked, self)

    self:AddLuaEvent(Module.Share.Config.Events.evtShareFlowFinish, self._InitShareBtn, self)
    self:AddLuaEvent(Module.Settlement.Config.Events.evtReplayBackToGameplay, self._ReShowSelf, self)
    self:AddLuaEvent(Module.Settlement.Config.Events.evtSettlementExitSpectateMode, self._ReShowSelf, self)

    self._bNoKillArray = false
end

function EvacuationResultInfo:OnOpen()
    -- azhengzheng:分帧处理 防止第一次打开界面卡顿
    Facade.LuaFramingManager:RegisterFrameTask(self._SetSettlementInfo, self)
    Facade.LuaFramingManager:RegisterFrameTask(self._SetHighLight, self)
    Facade.LuaFramingManager:RegisterFrameTask(self._FindFlow, self)
    Facade.LuaFramingManager:RegisterFrameTask(self._InitShareBtn, self)

    -- azhengzheng:额外走一次 防止因分帧导致OnShowBegin先走
    Facade.LuaFramingManager:RegisterFrameTask(self._SetFlowBtn, self)
    Facade.LuaFramingManager:RegisterFrameTask(self._SetPcFlowBtn, self)
end

function EvacuationResultInfo:OnShowBegin()
    self:_PlayOpenUIAudio()

    self:_SetFlowBtn()
    self:_SetPcFlowBtn()

    -- azhengzheng:通知引导模块前，先重置播放状态
    Module.Guide:SetIsEvacuateTeachingPlaying()
    Module.Settlement.Config.Events.evtEvacuationResultInfoOnShowBegin:Invoke()
end

-- BEGIN MODIFICATION @ VIRTUOS
function EvacuationResultInfo:OnHideBegin()
    if IsHD() and self._NavConfigHandler then
        WidgetUtil.DisableDynamicNavConfig(self._NavConfigHandler)
        self._NavConfigHandler = nil

        WidgetUtil.RemoveNavigationGroup(self)
        self._wtNavGroup = nil
    end
end
-- END MODIFICATION

function EvacuationResultInfo:OnShow()
    if not IsHD() then
        Module.CommonBar:SetTopBarVisible(false)
    end
end

function EvacuationResultInfo:OnHide()
end

function EvacuationResultInfo:OnClose()
    self:RemoveAllLuaEvent()
    if SettlementLogic.NeedToUnLoadMap() then
        settlementMgrIns:SetLowFrequency()
    end
end

function EvacuationResultInfo:_PlayOpenUIAudio()
    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UISettlementPopup)
end

function EvacuationResultInfo:_SetPcFlowBtn()
    if IsHD() then
        local summaryList = {}
        -- BEGIN MODIFICATION @ VIRTUOS

        if self._nextFlowUINavID then
            table.insert(summaryList, {actionName = "SOLSettlementNext", func = CreateCallBack(self._OnNextFlowClicked, self)})
        else
            table.insert(summaryList, {actionName = "SOLSettlementBackLobby", func = CreateCallBack(self._OnNextFlowClicked, self)})
        end

        if self._preFlowUINavID then
            table.insert(summaryList, {actionName = "SOLSettlementPre", func = CreateCallBack(self._OnPreFlowClicked, self)})
        elseif settlementMgrIns:HasTeamMemberWatchFight() and not Module.Settlement.Field:GetCurMapIsUnload() then
            table.insert(summaryList, {actionName = "SOLSettlementWatchBattle", func = CreateCallBack(self._OnPreFlowClicked, self)})
        end
        -- 本局收获
        table.insert(summaryList, {actionName = "Settlement_GetInfo", func = CreateCallBack(self._OpenGetDetailInfo, self)})
        -- 打开击杀详情界面
        --if not self._bNoKillArray then
            table.insert(summaryList, {actionName = "Settlement_OpenKillDetail", func = CreateCallBack(self._ShortcutEventOnOpenKillDetail, self)})
        --end

        if Module.Settlement:CheckSOLTeachingVideoCanPlay() then
            table.insert(summaryList, {actionName = "SettlementEvacuateTeaching", func = CreateCallBack(self._OnPlayEvacuateTeachingBtnClick, self)})
        end

        -- 切换显示信息（高光击败）
        if self._wtChangeHighLightWB:IsVisible() and not (#self._highLightKillList == 0 or #self._highLighAchievementsList == 0) then
            table.insert(summaryList, {actionName = "Settlement_ToggleDisplayInfo", func = CreateCallBack(self._ShortcutEventOnChangeHighLightClicked, self)})
        end
        -- END MODIFICATION

        Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, true, true)

        -- BEGIN MODIFICATION @ VIRTUOS : Bypass Face button bottom input from navigation to "Next step".
        if not self._NavConfigHandler then
            self._NavConfigHandler = WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)
        end

        if not self._wtNavGroup then
            self._wtNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtHighLightAchievementSGB, self, "Hittest")
            self._wtNavGroup:AddNavWidgetToArray(self._wtHighLightAchievementSGB)
            self._wtNavGroup:SetScrollRecipient(self._wtHighLightAchievementSGB)
            self._wtNavGroup:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)

            WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup)
        end

        -- END MODIFICATION
    end
end

function EvacuationResultInfo:_SetSettlementInfo()
    local settlementInfo = Server.SettlementServer:GetSettlementInfo()

    if not settlementInfo then
        return
    end
    self.bIsPrison = false
    self:_SetMatchInfo(settlementInfo.match_info)

    self:_SetPlayerInfo(settlementInfo.player)
end

function EvacuationResultInfo:_SetMatchInfo(matchInfo)
    if not matchInfo or matchInfo.match_type == EDSMatchType.kDSMatchTypeGuide then
        return
    end

    self.bIsPrison = Server.GameModeServer:CheckMatchModeIsPrison(matchInfo.mode_info.match_mode_id)

    self._wtMapNameTB:SetText(Module.GameMode:GetShortMapNameByMatchModeId(matchInfo.mode_info.match_mode_id))
    self._wtMapDifficultyTB:SetText(Module.GameMode:GetSubModeNameByMatchModeId(matchInfo.mode_info.match_mode_id, true))

    self._wtMapInfoCP:Visible()
end

function EvacuationResultInfo:_SetPlayerInfo(playerInfo)
    if not playerInfo then
        return
    end

    self:_SetBasicInfo(playerInfo.basic_info)

    self:_SetEndGameInfo(playerInfo.end_game)

    self:_SetGameAchievements(playerInfo.game_achievements)

    if playerInfo.money_paper and playerInfo.money_paper > 0 then
        self:_SetRecoveryInfo()
    end
end

function EvacuationResultInfo:_SetBasicInfo(basicInfo)
    if not basicInfo then
        return
    end

    self._wtHeroCardWB:SetPlayerName(basicInfo.game_nick)

    local controller = UGameplayBlueprintHelper.GetLocalGPPlayerController(self)
    if not isvalid(controller) then
        loginfo("EvacuationResultInfo:_SetBasicInfo controller is invalid")
        return
    end

    local playerState = controller.PlayerState
    if not isvalid(playerState) then
        loginfo("EvacuationResultInfo:_SetBasicInfo playerState is invalid")
        return
    end
    local PlayerTitleInfo = playerState:GetCurPlayerTitleInfo()
    self._wtHeroCardWB:UpdateTitleInfo(nil, PlayerTitleInfo.PlayerTitle, PlayerTitleInfo.RankAdcode, PlayerTitleInfo.RankNo)
end

function EvacuationResultInfo:_SetGameAchievements(gameAchievements)
    if not gameAchievements or #gameAchievements == 0 or not achievementTable then
        return
    end

    local tmpHighLighAchievementsList = {}

    for _, value in pairs(gameAchievements) do
        table.insert(tmpHighLighAchievementsList, {id = value.id, order = achievementTable[value.id] and achievementTable[value.id].Order or 0})
    end

    table.sort(
        tmpHighLighAchievementsList,
        function(a, b)
            return a.order < b.order
        end
    )

    for key = 1, math.min(3, #tmpHighLighAchievementsList) do
        table.insert(self._highLighAchievementsList, {id = tmpHighLighAchievementsList[key].id, name = achievementTable[tmpHighLighAchievementsList[key].id] and achievementTable[tmpHighLighAchievementsList[key].id].Name or "-"})
    end
end

function EvacuationResultInfo:_SetEndGameInfo(endGameInfo)
    if not endGameInfo then
        return
    end

    if endGameInfo.type == EGspPlayerResultType.kGspPlayerResultEscaped then
        self._escaped = true
        self._wtResultTB:SetText(self.bIsPrison and Module.Settlement.Config.Loc.EscapePrisonSuccessTXT or Module.Settlement.Config.Loc.EscapeSuccessTXT)
        self._wtTitleTB:SetText(Module.Settlement.Config.EscapeInfoType2Text[1])
        self._wtDescTB:SetText(settlementMgrIns:Read4Buffer(endGameInfo.extraction_location))
    else
        self:BPSetType(1)

        if endGameInfo.type == EGspPlayerResultType.kGspPlayerResultKilled then
            self._wtResultTB:SetText(self.bIsPrison and Module.Settlement.Config.Loc.EscapePrisonFailTXT or Module.Settlement.Config.Loc.EscapeFailTXT)

            self:_SetDeathInfo(endGameInfo.death_info)
        else
            self._wtPlaceOrDeathTipCP:Collapsed()
            self._wtResultTB:SetText(self.bIsPrison and Module.Settlement.Config.Loc.EscapePrisonTimeOutTXT or Module.Settlement.Config.Loc.EscapeTimeOutTXT)
        end
    end

    self._wtPlayTimeTB:SetText(TimeUtil.GetSecondsFormatHHMMSSString(endGameInfo.play_time))

    -- if endGameInfo.blue_print_special_id ~= 0 then
    --     self._wtMandelCP:Visible()
    -- end
    self:_SetMandelVisible(endGameInfo.blue_print_special_id)

    self._wtCarryOutProfitPrice:SetText(MathUtil.GetNumberFormatStr(endGameInfo.carry_out_profit_price + endGameInfo.cost_price))

    self:_SetHeroInfo(endGameInfo.hero)

    self:_SetKillArray(endGameInfo.kill_array)

    --PC高光时刻上报
    if DFHD_LUA == 1 then
        local SOLSettlementInfoFromView = FHighlightMomentSOLSettlementInfoFromView()
        for k, v in pairs( self._highLightKillList) do
            SOLSettlementInfoFromView.HighlightKillPlayers:Add(tostring(v.playerId))
            SOLSettlementInfoFromView.HighlightKillPlayers:Add(tostring(v.gameNick))
        end
        local highLightMomentMgr = UHighlightMomentMgr.Get(GetWorld())
        if isvalid(highLightMomentMgr) then
            highLightMomentMgr:SetSOLSettlemtnInfosFromView(SOLSettlementInfoFromView)
        end
    end
end

function EvacuationResultInfo:_SetRecoveryInfo()
    
    local batchInfo = Module.Recovery:GetLatestBatchInfo()
    local iconRtStr = ECurrencyClientType2RichIconTxt[ECurrencyClientType.OnlyUnBind]
    
    if Server.RecoveryServer:IsInRecoveryState(true) and batchInfo then
        
        local gain, recovered = Module.Recovery:GetTotalBatchAmount(batchInfo)
        
        if Module.Recovery:HasBatchRecoveredAll(batchInfo) then
            Module.CommonTips:ShowSimpleTip(string.format(ServerTipCode.SettlementRecoveryAll, iconRtStr, gain, iconRtStr, recovered), 4)                
        else
            Module.CommonTips:ShowSimpleTip(string.format(ServerTipCode.SettlementRecovery, iconRtStr, gain, iconRtStr, recovered), 4)
        end

        Module.Recovery:ClearBatchInfo()
        -- table.insert(self._carryOutProfitPriceDetailList,
        -- {
        --     id = UIName2ID.Assembled_CommonMessageTips_V10,
        --     data = {
        --         textContent = string.format(Module.Settlement.Config.Loc.RecoveryAmount, recovered),
        --         styleRowId = "C000"
        --     }
        -- }
        
        -- )
        
    end
end

function EvacuationResultInfo:_SetMandelVisible(bluePrintSpecialId)
    Facade.UIManager:RemoveSubUIByParent(self, self._wtMandelPSB)

    if not bluePrintSpecialId or bluePrintSpecialId == 0 then
        return
    end

    Facade.UIManager:AddSubUI(self, UIName2ID.EvacuationDecipher, self._wtMandelPSB)
end

function EvacuationResultInfo:_SetHeroInfo(heroInfo)
    if not heroInfo then
        return
    end

    self._wtHeroCardWB:SetCardInfoByAccessories(heroInfo.accessories)
    -- self._wtHeroCardWB:SetSelfCardImgPath(heroInfo.hero_id)
    -- self._wtHeroCardWB:SetSelfBadgeImgPath(heroInfo.hero_id)
    -- self._wtHeroCardWB:SetSelfTitleImgPath(heroInfo.hero_id)
end

function EvacuationResultInfo:_SetKillArray(killArray)
    if not killArray or #killArray == 0 then
        self._bNoKillArray = true
        return
    end
    self._bNoKillArray = false
    self._wtKillCountTB:SetText(#killArray)

    local tmpHighLightKillList = {}

    for _, value in pairs(killArray) do
        if value.enemy_type == EGspEnemyType.kGspEnemyTypePlayer then

            --BEGIN MODIFICATION @ VIRTUOS : 
            if IsPS5() then
                local playerOnlineId = self:GetPS5OnlineIdByOpenId(value.player.player_id)
                if playerOnlineId ~= nil then
                    table.insert(tmpHighLightKillList, {gameNick = playerOnlineId, rankScore = value.player.rank_match_score})
                else
                    table.insert(tmpHighLightKillList, {gameNick = value.player.game_nick, rankScore = value.player.rank_match_score})
                end
            else
                table.insert(tmpHighLightKillList, {gameNick = value.player.game_nick, rankScore = value.player.rank_match_score})
            end
            --END MODIFICATION
        elseif value.enemy_type == EGspEnemyType.kGspEnemyTypePlayerAI then
            table.insert(tmpHighLightKillList, {gameNick = value.ai.game_nick, rankScore = value.ai.rank_match_score,playerId = value.player.player_id})
        end
    end

    table.sort(
        tmpHighLightKillList,
        function(a, b)
            return a.rankScore > b.rankScore
        end
    )

    for key = 1, math.min(3, #tmpHighLightKillList) do
        local rankName, stars = self:_CalculateRankNameAndStars(tmpHighLightKillList[key].rankScore)

        table.insert(self._highLightKillList, {gameNick = tmpHighLightKillList[key].gameNick, rankName = rankName, stars = stars})
    end
end

function EvacuationResultInfo:_CalculateRankNameAndStars(rankScore)
    if not rankScore or not rankingMinorRanksTable or not rankingRankConstantsTable or rankScore == 0 then
        return nil
    end

    for _, value in pairs(rankingMinorRanksTable) do
        if value.MinScore <= rankScore then
            if value.MaxScore == 0 then
                return value.RankName, math.ceil((rankScore - value.MinScore + 1) / (rankingRankConstantsTable[1] and rankingRankConstantsTable[1].Value or 50))
            end

            if rankScore <= value.MaxScore then
                return value.RankName
            end
        end
    end

    return nil
end

function EvacuationResultInfo:_SetDeathInfo(deathInfo)
    if not deathInfo then
        return
    end

    local gameNick = nil

    if deathInfo.player and deathInfo.player.game_nick ~= "" then
        gameNick = deathInfo.player.game_nick
    elseif deathInfo.ai and deathInfo.ai.game_nick ~= "" then
        gameNick = deathInfo.ai.game_nick
    end

    if gameNick then
        self._wtTitleTB:SetText(Module.Settlement.Config.EscapeInfoType2Text[3])

        if deathInfo.reason == EGspPlayerDeathReason.kGspPlayerDeathReasonKilledByBoss then
            self._wtDescTB:SetText(string.format(ServerTipCode.Boss, settlementMgrIns:Read4Buffer(gameNick)))
        else
            self._wtDescTB:SetText(settlementMgrIns:Read4Buffer(gameNick))
        end
    else
        self._wtTitleTB:SetText(Module.Settlement.Config.EscapeInfoType2Text[4])

        self:_SetAccidentInfo(deathInfo.accident)
    end

    --BEGIN MODIFICATION @ VIRTUOS : Replace Player name with PS5 OnlineId
    if IsPS5() and deathInfo.player and deathInfo.player.game_nick ~= "" then
        local playerOnlineId = self:GetPS5OnlineIdByOpenId(deathInfo.player.player_id)
        if playerOnlineId ~= nil then
            self._wtDescTB:SetText(playerOnlineId)
        end
    end
    --END MODIFICATION

end

function EvacuationResultInfo:_SetAccidentInfo(accidentInfo)
    if Module.Settlement.Config.AccidentType2AccidentName[accidentInfo.accident_type] then
        self._wtDescTB:SetText(Module.Settlement.Config.AccidentType2AccidentName[accidentInfo.accident_type])
    else
        self._wtDescTB:SetText(Module.Settlement.Config.AccidentType2AccidentName[EGspAccidentType.kGspAccidentUnKnown])
    end
end

function EvacuationResultInfo:_OnOpenKillDetailClicked()
    local settlementInfo = Server.SettlementServer:GetSettlementInfo()

    if not settlementInfo or not settlementInfo.match_info then
        return
    end
    local playerId = Server.AccountServer:GetPlayerId()
    if IsHD() then
        Facade.UIManager:AsyncShowUI(UIName2ID.SolSettlementDetailPanelHD, nil ,nil, settlementInfo.match_info, playerId, 2)
    else
        Facade.UIManager:AsyncShowUI(UIName2ID.SolSettlementDetailPanel, nil ,nil, settlementInfo.match_info, playerId, 2)
    end
end

function EvacuationResultInfo:_OpenSolGainDetailClicked()
    local settlementInfo = Server.SettlementServer:GetSettlementInfo()

    if not settlementInfo or not settlementInfo.match_info then
        return
    end
    local playerId = Server.AccountServer:GetPlayerId()
    if IsHD() then
        Facade.UIManager:AsyncShowUI(UIName2ID.SolSettlementDetailPanelHD, nil ,nil, settlementInfo.match_info, playerId, 1)
    else
        Facade.UIManager:AsyncShowUI(UIName2ID.SolSettlementDetailPanel, nil ,nil, settlementInfo.match_info, playerId, 1)
    end
end

function EvacuationResultInfo:_SetHighLight()
    if #self._highLightKillList == 0 and #self._highLighAchievementsList == 0 then
        return
    end

    if #self._highLightKillList == 0 or #self._highLighAchievementsList == 0 then
        self._wtChangeHighLightWB:Collapsed()
    end

    if #self._highLightKillList ~= 0 then
        self._isHighLightKillShow = true

        Facade.UIManager:ClearSubUIByParent(self, self._wtHightLightKillSB)

        for _, value in ipairs(self._highLightKillList) do
            Facade.UIManager:AddSubUI(self, UIName2ID.SettlementHighLightKillItem, self._wtHightLightKillSB, nil, value)
        end
    end

    self._wtHighLightAchievementSGB:RefreshAllItems()

    self:_RefreshHighLight()

    self._wtHighLightHB:Visible()
end

function EvacuationResultInfo:_OnChangeHighLightClicked()
    self._isHighLightKillShow = not self._isHighLightKillShow

    self:_RefreshHighLight()
end

function EvacuationResultInfo:_RefreshHighLight()
    if self._isHighLightKillShow then
        self._wtHighLightTitleTB:SetText(Module.Settlement.Config.Loc.HighLightKill)
        self._wtHighLightAchievementSGB:Collapsed()
        self._wtHightLightKillSB:Visible()
    else
        self._wtHighLightTitleTB:SetText(Module.Settlement.Config.Loc.HighLightAchievements)
        self._wtHightLightKillSB:Collapsed()
        self._wtHighLightAchievementSGB:Visible()
    end
end

function EvacuationResultInfo:_OnGetHighLightAchievementsCount()
    return #self._highLighAchievementsList
end

function EvacuationResultInfo:_OnProcessHighLightAchievementsWidget(idx, widget)
    widget:SetInfo(self._highLighAchievementsList[idx + 1])
end

function EvacuationResultInfo:_FindFlow()
    local settlementUIIDList = Module.Settlement:GetSettlementUIIDList()

    for key, value in ipairs(settlementUIIDList) do
        if self.UINavID == value then
            if key > 2 then
                self._preFlowUINavID = settlementUIIDList[key - 1]
            end

            self._nextFlowUINavID = settlementUIIDList[key + 1]
            break
        end
    end
end

function EvacuationResultInfo:_SetFlowBtn()
    if not IsHD() then
        if Module.Settlement:CheckSOLTeachingVideoCanPlay() then
            self._wtEvacuateTeachingWB:Visible()
        else
            self._wtEvacuateTeachingWB:Collapsed()
        end

        if self._nextFlowUINavID then
            self._wtNextFlowWB:SetMainTitle(Module.Settlement.Config.Loc.JumpToNextFlow)
        end

        self._wtPreFlowWB:Visible()
        self._wtPreFlowWB:RemoveEvent("OnClicked")

        if self._preFlowUINavID then
            self._wtPreFlowWB:SetMainTitle(Module.Settlement.Config.Loc.JumpToPreFlow)
            self._wtPreFlowWB:Event("OnClicked", self._OnPreFlowClicked, self)
            return
        end

        if settlementMgrIns:HasTeamMemberWatchFight() and not Module.Settlement.Field:GetCurMapIsUnload() then
            self._wtPreFlowWB:Event("OnClicked", self._OnWatchBattleBtnClicked, self)
            self._wtPreFlowWB:SetMainTitle(Module.Settlement.Config.Loc.WatchBattle)
            return
        end

        self._wtPreFlowWB:Collapsed()
    end
end

function EvacuationResultInfo:_OnPreFlowClicked()
    if Module.Guide:GetIsEvacuateTeachingPlaying() then
        return
    end

    if self._preFlowUINavID then
        Facade.UIManager:AsyncShowUI(self._preFlowUINavID, nil, nil, true)

        return
    end

    self:_OnWatchBattleBtnClicked()
end

function EvacuationResultInfo:_OnNextFlowClicked()
    if Module.Guide:GetIsEvacuateTeachingPlaying() then
        return
    end
    if not self:IsVisible() then
        return
    end
    Module.Guide.Config.EGuideEvent.evtOnGuideMsg:Invoke(EGuideMsgSig.EvacuationResultInfoNextFlowClicked)
    if self._nextFlowUINavID then
        if not Module.Settlement.Field:GetCurMapIsUnload() then
            Module.Settlement.Field:SetCurMapIsUnload(true)
            SettlementLogic.UnLoadCurrentMap()
        end
        loginfo("EvacuationResultInfo:_OnNextFlowClicked _nextFlowUINavID", self._nextFlowUINavID)
        Facade.UIManager:AsyncShowUI(self._nextFlowUINavID)

        return
    end
    loginfo("EvacuationResultInfo:_OnNextFlowClicked 6")
    Module.Settlement.Config.Events.evtSettlementEnd:Invoke()
    Module.Settlement.Config.Events.evtCloseRootCutscene:Invoke()
end

function EvacuationResultInfo:_InitShareBtn()
    if IsHD() or not Module.Share:FuncPointUnLock(SwitchSystemID.SubShareSOLSettlementHighlightFace) then
        self._wtShareWB:Collapsed()
        return
    end

    local canTriggerFirstReward, itemId, itemNum = Module.Share:CanTriggerFirstReward()

    if not canTriggerFirstReward then
        self._wtShareTipCP:Collapsed()
        return
    end

    self._wtShareTip:SetText(string.format(Module.Settlement.Config.Loc.SOLShareTip, itemNum))
end

function EvacuationResultInfo:_OnShareBtnClicked()
    local preScreenshotShare = CreateCallBack(
        function(self)
            self:_PreScreenshotShare()
        end,
        self
    )

    local afterScreenshotShare = CreateCallBack(
        function(self)
            self:_AfterScreenshotShare()
        end,
        self
    )

    Module.Share:ReqSimpleWatermarkScreenshot(
        "RoleInfoPersonal",
        preScreenshotShare,
        afterScreenshotShare,
        Module.Share.Config.ESocialShareSceneEnum.EvacuationResultInfo)
end

function EvacuationResultInfo:_PreScreenshotShare()
    -- Module.CommonBar:ShowTopBar()
    self._wtShareWB:Collapsed()
    self._wtFlowBtnHB:Collapsed()
end

function EvacuationResultInfo:_AfterScreenshotShare()
    -- Module.CommonBar:CloseTopBar()
    self._wtShareWB:Visible()
    self._wtFlowBtnHB:Visible()
    self:_InitShareBtn()
end

function EvacuationResultInfo:_OnWatchBattleBtnClicked()
    if not self:IsVisible() then
        return
    end

    if settlementMgrIns:HasTeamMemberWatchFight() then
        self:Collapsed()
        self:_RecoverBottomBarInputSummaryList()
        Module.Settlement.Config.Events.evtCloseRootCutscene:Invoke()
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "Slate.EnableOptInGame 1", nil)
        SettlementLogic.SettlementEnterSpectateMode()
    else
        self:_SetPcFlowBtn()
        self:_SetFlowBtn()
    end
end

function EvacuationResultInfo:_RecoverBottomBarInputSummaryList()
    if not IsHD() then
        return
    end

    Module.CommonBar:SetBottomBarTempInputSummaryList({}, true, true)
end

function EvacuationResultInfo:_ReShowSelf()
    if not self._escaped then
        return
    end

    local hudStateManager = UE.HUDStateManager.Get(GetWorld())
    if hudStateManager then
        hudStateManager:AddState(UE.GameHUDSate.GHS_GlobalHideAllHUD, true)
        hudStateManager:AddState(UE.GameHUDSate.GHS_Settlement, true)
    end
    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.IngameSoundOff)
    self:Visible()
    self:_SetFlowBtn()
    self:_SetPcFlowBtn()
    Module.Settlement.Config.Events.evtOpenRootCutscene:Invoke()
end

-- BEGIN MODIFICATION @ VIRTUOS : 
function EvacuationResultInfo:_ShortcutEventOnOpenKillDetail()
    if not IsHD() then
        return 
    end

    if self._wtOpenKillDetailCBO and self._wtOpenKillDetailCBO:IsVisible() then
        self._wtOpenKillDetailCBO:ButtonClick()
    end
end

-- BEGIN MODIFICATION @ VIRTUOS : 
function EvacuationResultInfo:_OpenGetDetailInfo()
    if not IsHD() then
        return 
    end
    if self._wtGains and self._wtGains:IsVisible() then
        self._wtGains:ButtonClick()
    end
end

function EvacuationResultInfo:_OnOpenTip()
    if not IsHD() then
        return 
    end

end

function EvacuationResultInfo:_ShortcutEventOnChangeHighLightClicked()
    if not IsHD() then
        return 
    end

    if self._wtChangeHighLightWB and self._wtChangeHighLightWB:IsVisible() then
        self._wtChangeHighLightWB:ButtonClick()
    end
end

function EvacuationResultInfo:GetPS5OnlineIdByOpenId(openId)
    if IsPS5() then
        local UDFMOnlineIdentityManager = import "DFMOnlineIdentityManager"
        local DFMOnlineIdentityManager = UDFMOnlineIdentityManager.Get(GetGameInstance())

        if DFMOnlineIdentityManager then
            local PS5OnlineId = DFMOnlineIdentityManager:GetPlayerPlatformIdByOpenId(openId)
            if not string.isempty(PS5OnlineId) then
                return PS5OnlineId
            end
        end
    end

    return nil
end
-- END MODIFICATION

function EvacuationResultInfo:_OnPlayEvacuateTeachingBtnClick()
    Module.Guide:PlayEvacuateTeaching(2)
end

return EvacuationResultInfo