----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGunsmith)
----- LOG FUNCTION AUTO GENERATE END -----------



local GunsmithUIContextLogic = require "DFM.Business.Module.GunsmithModule.Logic.GunsmithUIContextLogic"
local EGunsmithPartMPUnlockMethod = require "DFM.Business.Module.GunsmithModule.Define.EGunsmithPartMPUnlockMethod"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"


local ECheckBoxState = import"ECheckBoxState"

---@class GunsmithUnlockPathItemUI : LuaUIBaseView
local GunsmithUnlockPathItemUI = ui("GunsmithUnlockPathItemUI", LuaUIBaseView)

function GunsmithUnlockPathItemUI:Ctor()
    self._wtItemViewRoot = self:Wnd("DFNamedSlot_67", UIWidgetBase)
    self._wtTitle = self:Wnd("DFTextBlock_29", UITextBlock)
    self._wtTitle:Collapsed()

    self._wtDFCommonCheckBoxWithText = self:Wnd("WBP_DFCommonCheckBoxWithText", DFCheckBoxWithText)
    self._wtDFCommonCheckBoxWithText:Event("OnCheckStateChanged", self._OnCheckBoxWithTextCheckStateChanged, self)

    self._wtUnlockBtn = self:Wnd("WBP_CommonButtonV2S1_147", DFCommonButtonOnly)
    self._wtUnlockBtn:Event("OnClicked", self._OnUnlockClick, self)

    self._wtItemName = self:Wnd("TextBlock_84", UITextBlock)
    self._wtSource = self:Wnd("TextBlock_1", UITextBlock)
    self._wtSource:Collapsed()
    self._wtRichSource = self:Wnd("DFRichTextBlock_1", UITextBlock)
    self._wtRichSource:SelfHitTestInvisible()
    -- self._wt_WaterFallList = UIUtil.WndWaterfallScrollBox(self, "DFWaterfallScrollView_22", self._OnGetItemCount, self._OnProcessItemWidget)
    -- local path = UIName2ID.GetBPFullPathByID(UIName2ID.IVCommonItemTemplate)
    -- self._wt_WaterFallList:RelinkTemplateWidget(path)

    self._itemUIData = nil      --@GunsmithUnlockPathItemUIData
end

function GunsmithUnlockPathItemUI:Destroy()
    self._itemUIData = nil
end

function GunsmithUnlockPathItemUI:OnShow()
end

function GunsmithUnlockPathItemUI:OnHide()
end

function GunsmithUnlockPathItemUI:_OnUnlockClick()
    Module.Gunsmith.Config.Events.evtOnGunsmithUnlockPathItemUIUnlockClicked:Invoke(self._itemUIData)
end

function GunsmithUnlockPathItemUI:_OnCheckBoxWithTextCheckStateChanged(bChecked)
    self._itemUIData:SetSelect(bChecked)
    Module.Gunsmith.Config.Events.evtGunsmithUnlockPathItemStateChanged:Invoke()
end

function GunsmithUnlockPathItemUI:UpdateUI(itemUIData)
    self._itemUIData = itemUIData

    if self._itemUIData == nil then
        self:_InternalUpdateDefault()
        return
    end

    self:_InternalUpdateCheckBoxUI()
    self:_InternalUpdateName()
    self:_InternalUpdateUnlockMethod()
    self:_InternalUpdateIcon()
    self:_InternalUpdateButtonUI()
    -- self:_InteralUpdateAdpterUI()
end

function GunsmithUnlockPathItemUI:_InternalUpdateDefault()
end

-- 设置CheckBoxUI
function GunsmithUnlockPathItemUI:_InternalUpdateCheckBoxUI()
    local bCanSelect = self:_CheckCanBeChecked()
    if not bCanSelect then
        self._wtDFCommonCheckBoxWithText:SelfHitTestInvisible()
        self._wtDFCommonCheckBoxWithText:SetCheckedState(ECheckBoxState.Undetermined)
        return
    end
    local bSelect = self._itemUIData:GetSelect()
    self._wtDFCommonCheckBoxWithText:Visible()
    self._wtDFCommonCheckBoxWithText:SetIsChecked(bSelect)
end

-- 设置道具名称
function GunsmithUnlockPathItemUI:_InternalUpdateName()
    local itemID = self._itemUIData:GetID()
    local name = ItemConfigTool.GetItemName(itemID)
    self._wtItemName:SetText(name)
end

-- 设置道具来源
function GunsmithUnlockPathItemUI:_InternalUpdateUnlockMethod()
    local unlockMethodText = self:_InternalGetUnlockMethodText()
    self._wtRichSource:SetText(unlockMethodText)
end

function GunsmithUnlockPathItemUI:_InternalGetUnlockMethodText()
    local unlockMethod = self:GetMPUnlockMethod()
    local unlockMethodText
    if unlockMethod == EGunsmithPartMPUnlockMethod.Inventory then
        local bIsFinetune = self._itemUIData:GetIsFinetune()
        unlockMethodText = Module.Gunsmith.Config.Loc.GunsmithPartMainUIUnlockTitleText
        if bIsFinetune then
            unlockMethodText = string.format(Module.Gunsmith.Config.Loc.GunsmithUnlockPathUIFineTuneText, unlockMethodText)
        end
    elseif unlockMethod == EGunsmithPartMPUnlockMethod.Activity then
        unlockMethodText = string.format(Module.Gunsmith.Config.Loc.GunsmithPartMainUIMPOtherUnlockText, Module.Gunsmith.Config.Loc.GunsmithPartMainUIMPUnlockActivityText)
    elseif unlockMethod == EGunsmithPartMPUnlockMethod.Armory then
        unlockMethodText = string.format(Module.Gunsmith.Config.Loc.GunsmithPartMainUIMPOtherUnlockText, Module.Gunsmith.Config.Loc.GunsmithPartMainUIMPUnlockArmoryText)
    elseif unlockMethod == EGunsmithPartMPUnlockMethod.Level then
        local UnLockLevel = self:_InternalGetUnlockLevel()
        unlockMethodText = string.format(Module.Gunsmith.Config.Loc.GunsmithUnlockPathUILevelText, UnLockLevel)
    elseif unlockMethod == EGunsmithPartMPUnlockMethod.Unknown then
        unlockMethodText = Module.Gunsmith.Config.Loc.GunsmithPartMainUIUnableObtainText
    end
    return unlockMethodText
end

function GunsmithUnlockPathItemUI:_InternalGetUnlockLevel()
    local itemID = self._itemUIData:GetID()
    local weaponID = GunsmithUIContextLogic.GetItemID()
    local UnLockLevel = Server.WeaponAssemblyServer:GetWeaponPartsUnLockLvl(weaponID, itemID)
    return UnLockLevel
end

-- 设置道具Icon
function GunsmithUnlockPathItemUI:_InternalUpdateIcon()
	Facade.UIManager:RemoveSubUIByParent(self, self._wtItemViewRoot)

    local itemID = self._itemUIData:GetID()
    local itemBase = ItemBase:NewIns(itemID)

    local weakUIIns, instanceId = Module.CommonWidget:CreateIVCommonItemTemplate(self, self._wtItemViewRoot, nil, nil)
    local itemView = getfromweak(weakUIIns)
    itemView:InitItem(itemBase)
end

-- 设置道具解锁
function GunsmithUnlockPathItemUI:_InternalUpdateButtonUI()
    local unlockMethod = self:GetMPUnlockMethod()
    local bShow = unlockMethod ~= EGunsmithPartMPUnlockMethod.Inventory
    self._wtUnlockBtn:SetActive(bShow)
    if not bShow then
        self:SetType(4)
        return
    end
    self:SetType(3)
    local bIsEnabled = false
    local text = Module.Gunsmith.Config.Loc.GunsmithPartMainUIUnableObtainText
    if unlockMethod == EGunsmithPartMPUnlockMethod.Level then
        text = Module.Gunsmith.Config.Loc.GunsmithPartMainUIButtonMPLevelUnlockText
        bIsEnabled = true
    elseif unlockMethod == EGunsmithPartMPUnlockMethod.Activity then
        text = Module.Gunsmith.Config.Loc.GunsmithPartMainUIMPUnlockActivityText
        bIsEnabled = true
    elseif unlockMethod == EGunsmithPartMPUnlockMethod.Armory then
        text = Module.Gunsmith.Config.Loc.GunsmithPartMainUIMPUnlockArmoryText
        bIsEnabled = true
    end
    self._wtUnlockBtn:SetMainTitle(text)
    self._wtUnlockBtn:SetBtnEnable(bIsEnabled)
end

-- function GunsmithUnlockPathItemUI:_InteralUpdateAdpterUI()
--     self._wt_WaterFallList:RefreshAllItems()
-- end

-- function GunsmithUnlockPathItemUI:GetLockParts()
--     if self._itemUIData == nil then
--         return
--     end
--     return self._itemUIData:GetLockParts()
-- end

function GunsmithUnlockPathItemUI:_CheckCanBeChecked()
    local bCanSelect = self._itemUIData:GetCanSelect()
    return bCanSelect
end

function GunsmithUnlockPathItemUI:GetMPUnlockMethod()
    if self._itemUIData == nil then
        return EGunsmithPartMPUnlockMethod.Unknown
    end
    return self._itemUIData:GetMPUnlockMethod()
end

-- BEGIN MODIFICATION @ VIRTUOS : 使用手柄时，按A点击CommonFluctuationPopItem勾选CheckBox
function GunsmithUnlockPathItemUI:OnClicked()
    if self._wtDFCommonCheckBoxWithText:GetCheckedState() ~= ECheckBoxState.Undetermined then
        self._wtDFCommonCheckBoxWithText:SelfClick()
    else

    end
end
-- END MODIFICATION

return GunsmithUnlockPathItemUI