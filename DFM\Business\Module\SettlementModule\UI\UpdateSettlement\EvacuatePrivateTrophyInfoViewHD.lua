----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSettlement)
----- LOG FUNCTION AUTO GENERATE END -----------



local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ShopHelperTool = require "DFM.StandaloneLua.BusinessTool.ShopHelperTool"
local ULuaExtension = import "LuaExtension"
local CharacterCaptureUtil = require "DFM.Business.Module.CommonWidgetModule.UI.HD.CharacterCaptureUtil"
local TrophyLogic = require "DFM.Business.Module.SettlementModule.Logic.SOLSettlementPrivateTrophyLogic"

local function log(...)
    loginfo("xxww [EvacuatePrivateTrophyInfoViewHD]", ...)
end

local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local LootEquipSlotPanel = require "DFM.Business.Module.LootingModule.UI.Common.LootEquipSlotPanel"
local InvSlotView = require "DFM.Business.Module.InventoryModule.UI.Common.InvSlotView"
local SettlementLogic = require "DFM.Business.Module.SettlementModule.SettlementLogic"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local EvacuatePrivateTrophyInfoViewHD = ui("EvacuatePrivateTrophyInfoViewHD")
local UIManager = Facade.UIManager
local InventoryConfig = require "DFM.Business.Module.InventoryModule.InventoryConfig"

local EComp = CommonWidgetConfig.EIVWarehouseTempComponent
local EIVSlotPos = CommonWidgetConfig.EIVSlotPos
local EIVCompOrder = CommonWidgetConfig.EIVCompOrder
local ETextStyle = {
    Default = 0,
    Equipment = 1,
    ItemName = 2,
    AuctionText = 3,
}

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local GPUINavigationStrategy_Hittest = import("GPUINavigationStrategy_Hittest")
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local UGPInputHelper = import "GPInputHelper"
local EGPInputType = import"EGPInputType"
local UGPInputDelegates = import "GPInputDelegates"
local EGPUINavHittestFallbackType = import "EGPUINavHittestFallbackType"
local UGPUINavigationUtils = import("GPUINavigationUtils")
local EDescendantScrollDestination = import "EDescendantScrollDestination"
local EGPUINavGroupTransSimpleStrategy = import "EGPUINavGroupTransSimpleStrategy"
local EGPUINavGroupBestDefaultType = import "EGPUINavGroupBestDefaultType"
local UDFNavigationSelectorBase = import("DFNavigationSelectorBase")
-- END MODIFICATION

local fPostItemDisable=TrophyLogic.PostItemDisable

function EvacuatePrivateTrophyInfoViewHD:Ctor()
    log("EvacuatePrivateTrophyInfoViewHD:Ctor")
    self:InitWidget()

    if DFHD_LUA == 1 then
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.Hidden)
    else
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.Hidden)
    end

end

function EvacuatePrivateTrophyInfoViewHD:OnInitExtraData()
    log("EvacuatePrivateTrophyInfoViewHD:OnInitExtraData")
    self._bIsFinalSettlementPanel=true
end

function EvacuatePrivateTrophyInfoViewHD:InitWidget()
    self._wtBackToLobbyRemainTimeTipTB = self:Wnd("wtBackToLobbyRemainTimeTipTB", UITextBlock)

    -- azhengzheng:MS24转测周要求屏蔽库存估值
    local value = self:Wnd("value", UIWidgetBase)

    if value then
        value:Collapsed()
    end

    -- azhengzheng:库存估值
    self._wtInventoryValuationTB = self:Wnd("wtInventoryValuationTB", UITextBlock)
    self._wtWarehouseNameTxt = self:Wnd("DFTextBlock", UITextBlock)
    self._wtWarehouseNameTxt:SetText(InventoryConfig.Loc.DepositaryTitle)
    
    log("EvacuatePrivateTrophyInfoViewHD:InitWidget")
    self._wtContinueBtn = self:Wnd("WBP_CommonButtonV1S2", DFCommonButtonOnly)
    if self._wtContinueBtn then
        self._wtContinueBtn:Event("OnClicked", self._OnContinueBtnClicked, self)
    else
        logerror("_wtContinueBtn not valid")
    end
    self._wtTransferBtn = self:Wnd("wtTransferBtn", DFCommonButtonOnly)
    if self._wtTransferBtn then
        self._wtTransferBtn:Event("OnClicked", self._OnTransferBtnClick, self)
    else
        logerror("_wtTransferAllBtn not valid")
    end
    self._wtEquipPanel = self:Wnd("wtEquipPanel", UIWidgetBase)
    if self._wtEquipPanel then

        -- azhengzheng:屏蔽显示装备按钮
        local wtDFCommonCheckBoxWithText = self._wtEquipPanel:Wnd("wtDFCommonCheckBoxWithText", UIWidgetBase)

        if wtDFCommonCheckBoxWithText then
            wtDFCommonCheckBoxWithText:Collapsed()
        end

        --安全箱和钥匙包禁用
        self._wtKeySubPanel=self._wtEquipPanel:Wnd("wtKeyContainerView",UIWidgetBase)
        if self._wtKeySubPanel then
            self._wtKeySlot=self._wtKeySubPanel:Wnd("wtEquipSlotView",UIWidgetBase)
            if self._wtKeySlot then
                self._wtKeySlot:BindPostRefreshFunc(fPostItemDisable)
            else
                logerror("_wtKeySlot not valid")
            end
        else
            logerror("_wtKeySubPanel not valid")
        end
        self._wtSafeBoxPanel=self._wtEquipPanel._wtSafeBox
        if self._wtSafeBoxPanel then
            self._wtSafeBoxSlot=self._wtSafeBoxPanel:GetEquipSlotView()
            if self._wtSafeBoxSlot then
                self._wtSafeBoxSlot:BindPostRefreshFunc(fPostItemDisable)
            else
                logerror("_wtSafeBoxSlot not valid")
            end
        else
            logerror("_wtSafeBoxPanel not valid")
        end
    else
        logerror("_wtEquipPanel not valid")
    end
    self._wtWarehousePanel = self:Wnd("wtWarehousePanel", UIWidgetBase)

    if self._wtWarehousePanel then
        self._wtWarehousePanel:ShowExtManageExtrance(false)
        --self._wtWarehousePanel:ShowExtArrangePanel(false)
    end

    self.durabilityZeroGidMap = {}
    if DFHD_LUA == 1 then
        self._wtCaptureImg = self:Wnd("wtCaptureImg", CharacterCaptureUtil)
    end
    self.teamTipPanel = self:Wnd("DFHorizontalBox_66", UIWidgetBase)

    self._wtTeammateSupplies = self:Wnd("wtTeammateSupplies", DFCheckBoxOnly)
    self._wtTeammateSupplies:Event("OnCheckStateChanged",self._OnStateChanged, self)

    self._wtDFTipsAnchor = UIUtil.WndTipsAnchor(self, "DFTipsAnchor_161", self._ShowTips, self._CloseTips)
    self._bShowTipState = false

    self._wtGains = self:Wnd("wtGains", DFCommonButtonOnly)
    self._wtGains:Event("OnClicked", self._OpenSolGainDetailClicked, self)
    self._wtGains:SelfHitTestInvisible()
end

function EvacuatePrivateTrophyInfoViewHD:_ShowTips()
    if not SettlementLogic._OnHasTeammatePropInSettlement() then
        return
    end

    self:_CloseTips()
    if not self._tipHandle then
        self._tipHandle = Module.CommonTips:ShowAssembledTips({
            {
                id = UIName2ID.Assembled_CommonMessageTips_V1,
                data = {
                    textContent = Module.Settlement.Config.Loc.SolSettlementTip1,
                }
            },
            {
                id = UIName2ID.Assembled_CommonMessageTips_V2,
                data = {
                    textContent = Module.Settlement.Config.Loc.SolSettlementTip2,
                }
            },
        }, self._wtDFTipsAnchor)
    end
end

function EvacuatePrivateTrophyInfoViewHD:_CloseTips()
    if self._tipHandle then
        self._wtTeammateSupplies:SetIsChecked(false, false)
        Module.CommonTips:RemoveAssembledTips(self._tipHandle, self._wtDFTipsAnchor)
        self._tipHandle = nil
    end
end

function EvacuatePrivateTrophyInfoViewHD:_OnStateChanged()
    if bChecked then
        self:_ShowTips()
    else
        self:_CloseTips()
    end
end

function EvacuatePrivateTrophyInfoViewHD:_OnChangeTipState()
    if not self._bShowTipState then
        self:_ShowTips()
    else
        self:_CloseTips()
    end

    self._bShowTipState = not self._bShowTipState
end

function EvacuatePrivateTrophyInfoViewHD:_OpenSolGainDetailClicked()
    local settlementInfo = Server.SettlementServer:GetSettlementInfo()

    if not settlementInfo or not settlementInfo.match_info then
        return
    end
    local playerId = Server.AccountServer:GetPlayerId()
    if IsHD() then
        Facade.UIManager:AsyncShowUI(UIName2ID.SolSettlementDetailPanelHD, nil ,nil, settlementInfo.match_info, playerId, 1)
    else
        Facade.UIManager:AsyncShowUI(UIName2ID.SolSettlementDetailPanel, nil ,nil, settlementInfo.match_info, playerId, 1)
    end
end


function EvacuatePrivateTrophyInfoViewHD:OnShowBegin()
    if SettlementLogic._OnHasTeammatePropInSettlement() then
        self.teamTipPanel:SelfHitTestInvisible()
    else
        self.teamTipPanel:Collapsed()
    end
    
    self:_RefreshView()

    if DFHD_LUA == 1 then
        Module.Settlement.Config.Events.evtEvacuateTrophyViewLoadFinish:Invoke()
    end
    -- BEGIN MODIFICATION @ VIRTUOS : 
    --添加手柄键鼠切换事件
    if IsHD() then
        if not self._OnNotifyInputTypeChangedHandle then
            self._OnNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._handleInputTypeChanged, self))
            local curInputType = WidgetUtil.GetCurrentInputType()
            self:_handleInputTypeChanged(curInputType)
        end
    end 
    -- END MODIFICATION
end
    -- BEGIN MODIFICATION @ VIRTUOS : 
function EvacuatePrivateTrophyInfoViewHD:_handleInputTypeChanged(inputType)
    if not IsHD() then
        return
    end 
    Module.CommonBar:RecoverBottomBarInputSummaryList()
    if  inputType == EGPInputType.Gamepad then    
        local summaryList = {}

        table.insert(summaryList,  {actionName = "SOLSettlementBackLobby", func = CreateCallBack(self._OnContinueBtnClicked, self)})
        table.insert(summaryList,  {actionName = "Settlement_GetInfo", func = CreateCallBack(self._OpenGetDetailInfo, self)})
        table.insert(summaryList,  {actionName = "ClearBags", func = CreateCallBack(self._OnClearBags, self)})

        if SettlementLogic._OnHasTeammatePropInSettlement() then
            table.insert(summaryList,  {actionName = "Assembly_Confirm", func = CreateCallBack(self._OnChangeTipState, self)})
        end

        table.insert(summaryList,  {actionName = "SOLSettlementPre", func = CreateCallBack(self._OnJumpToPreFlow,self)})

        Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, true,true)
    else
        loginfo("EvacuatePrivateTrophyInfoViewHD:_handleInputTypeChanged   HD")
        Module.CommonBar:SetBottomBarTempInputSummaryList(
            {
                {actionName = "SOLSettlementBackLobby", func = CreateCallBack(self._OnContinueBtnClicked, self)},
                {actionName = "ClearBags", func = CreateCallBack(self._OnClearBags, self)},
                {actionName = "SOLSettlementPre", func = CreateCallBack(self._OnJumpToPreFlow,self)},
            },
            true,
            true
        )
    end
end

function EvacuatePrivateTrophyInfoViewHD:OnHideBegin()
    if IsHD() then
        Module.CommonBar:RecoverBottomBarInputSummaryList()
        if self._OnNotifyInputTypeChangedHandle then
            UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._OnNotifyInputTypeChangedHandle)
            self._OnNotifyInputTypeChangedHandle = nil
        end
    end 
end
    -- END MODIFICATION

function EvacuatePrivateTrophyInfoViewHD:OnShow()
    self:InitBtnStatus()

    log("EvacuatePrivateTrophyInfoViewHD:OnShow", self)
    Module.Inventory:RegisterCommonClickBehavior()
    Module.CommonWidget:BindCommonIVInputLogic()
    self:AddLuaEvent(Module.CommonWidget.Config.Events.evtItemDoubleClicked, self._OnItemDoubleClicked, self)
    self:AddLuaEvent(Module.Inventory.Config.Events.evtListItemDoubleClicked, self._OnItemDoubleClicked, self)
    self:AddLuaEvent(Module.Inventory.Config.Events.evtEquipSlotViewDoubleClicked, self._OnItemDoubleClicked, self)

    self.durabilityZeroProps = {}
    for _, v in pairs(Server.SettlementServer.durabilityGidMap) do
        if v.prop.health == 0 then
            table.insert(self.durabilityZeroProps, v.prop)
        end
    end

    if self._wtWarehousePanel then
        self._wtBtnGroup = self._wtWarehousePanel:Wnd("wtSubBtnsPanel", UIWidgetBase)

        local wtExtArrangeBtn = self._wtWarehousePanel:Wnd("wtExtArrangeBtn", UIWidgetBase)
        local wtSellBtn = self._wtWarehousePanel:Wnd("wtSellBtn", UIWidgetBase)
        local wtWishListBtn = self._wtWarehousePanel:Wnd("wtWishListBtn", UIWidgetBase)
        local wtExtManageBtn = self._wtWarehousePanel:Wnd("wtExtManageBtn", UIWidgetBase)

        if wtExtArrangeBtn then
            wtExtArrangeBtn:Collapsed()
        end

        if wtSellBtn then
            wtSellBtn:Collapsed()
        end

        if wtWishListBtn then
            wtWishListBtn:Collapsed()
        end

        if wtExtManageBtn then
            wtExtManageBtn:Collapsed()
        end
    else
        logerror("_wtWarehousePanel not valid")
    end

    -- self._wtBtnGroup:Collapsed()
    if DFHD_LUA == 1 then
        Timer.DelayCall(1, function()
            -- azhengzheng:如果玩家快速跳过界面，进入loading，那么这个控件可能已经被清理了，要判空
            if self._wtCaptureImg and self._wtCaptureImg.HitTestInvisible then
                self._wtCaptureImg:HitTestInvisible()
            end
        end, self)
    end
    
    Module.Settlement.Config.Events.evtChangeSettlementUI:Invoke(self.UINavID)

    Module.CommonBar:RegStackUIDefaultEsc(self, false)
    -- BEGIN MODIFICATION @ VIRTUOS : 
    if IsHD() then
        self:_InitShortcuts()

        -- 重新根据输入类型设置按键提示
        local curInputType = WidgetUtil.GetCurrentInputType()
        self:_handleInputTypeChanged(curInputType)
    end   
    -- END MODIFICATION
end

function EvacuatePrivateTrophyInfoViewHD:_OpenGetDetailInfo()
    local settlementInfo = Server.SettlementServer:GetSettlementInfo()

    if not settlementInfo or not settlementInfo.match_info then
        return
    end
    local playerId = Server.AccountServer:GetPlayerId()
    if IsHD() then
        Facade.UIManager:AsyncShowUI(UIName2ID.SolSettlementDetailPanelHD, nil ,nil, settlementInfo.match_info, playerId, 1)
    else
        Facade.UIManager:AsyncShowUI(UIName2ID.SolSettlementDetailPanel, nil ,nil, settlementInfo.match_info, playerId, 1)
    end
end

function EvacuatePrivateTrophyInfoViewHD:_OnJumpToPreFlow()
    local uiIdList = Module.Settlement:GetSettlementUIIDList()
    Facade.UIManager:AsyncShowUI(uiIdList[#uiIdList - 1])
end

function EvacuatePrivateTrophyInfoViewHD:OnHide()
    self:_ReleaseTimeTimerHandle()

    log("EvacuatePrivateTrophyInfoViewHD:OnHide")
    Module.Inventory:UnregisterCommonClickBehavior()
    Module.CommonWidget:UnbindCommonIVInputLogic()
    self:RemoveLuaEvent(Module.CommonWidget.Config.Events.evtItemDoubleClicked)
    self:RemoveLuaEvent(Module.Inventory.Config.Events.evtListItemDoubleClicked)
    self:RemoveLuaEvent(Module.Inventory.Config.Events.evtEquipSlotViewDoubleClicked)
    self:RemoveLuaEvent(InventoryConfig.Events.evtWarehouseChangeTitle)
    
    -- BEGIN MODIFICATION @ VIRTUOS : 
    if IsHD() then
        self:_RemoveShortcuts()
        WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    end 
    -- END MODIFICATION
end

function EvacuatePrivateTrophyInfoViewHD:OnOpen()
    -- azhengzheng:设置库存估值
    self._wtInventoryValuationTB:SetText(ShopHelperTool.GetCurrencyNumFormatStr(math.round(Server.InventoryServer:GetWarehouseValue())))

    log("EvacuatePrivateTrophyInfoViewHD:OnOpen")

    -- Module.CommonBar:RegStackUIDefaultEsc(self, false)
    -- Module.CommonBar:RegStackUIInputSummary(self,{{actionName="ClearBags",func=CreateCallBack(self._OnClearBags,self)}})

    SettlementLogic.PlayOpenAudio()

    -- self:InitBtnStatus()

end

function EvacuatePrivateTrophyInfoViewHD:_RefreshView()
    self._wtEquipPanel:_RefreshView()
end

function EvacuatePrivateTrophyInfoViewHD:InitBtnStatus()
    if self._bIsFinalSettlementPanel then
        self._wtContinueBtn:SetMainTitle(Module.Settlement.Config.Loc.ReturnToTheLobby)
        if Module.Settlement.Field.bNotAutoContinue then
            return
        end
        self.kickTime = Module.Settlement.Config.SettlementSuccessWaitTime
        self._kickEndTime=TimeUtil.GetCurrentTime()+self.kickTime
        self.timeHandle = Timer:NewIns(1, 0)
        self.timeHandle:AddListener(self.ChangeBtnStatus, self)
        self.timeHandle:Start()
        self:ChangeBtnStatus()
    end
end

function EvacuatePrivateTrophyInfoViewHD:_ReleaseTimeTimerHandle()
    if not self.timeHandle then
        return
    end

    self.timeHandle:Release()
    self.timeHandle = nil
end

function EvacuatePrivateTrophyInfoViewHD:ChangeBtnStatus()
    local remainTime=self._kickEndTime-TimeUtil.GetCurrentTime()
    self._wtContinueBtn:SetMainTitle(string.format(Module.Settlement.Config.Loc.ReturnToTheLobbyWithTime, remainTime))

    self._wtBackToLobbyRemainTimeTipTB:SetText(string.format(Module.Settlement.Config.Loc.BackToLobbyRemainTimeTip, remainTime))

    if TimeUtil.GetCurrentTime()>=self._kickEndTime then
        self:_ReleaseTimeTimerHandle()
        self:_OnContinueBtnClicked()
    end
end

function EvacuatePrivateTrophyInfoViewHD:OnClose()
    log("EvacuatePrivateTrophyInfoViewHD:OnClose")

    self:_ReleaseTimeTimerHandle()

    self:RemoveAllLuaEvent()
end

function EvacuatePrivateTrophyInfoViewHD:_OnContinueBtnClicked()
    loginfo("EvacuatePrivateTrophyInfoViewHD:_OnContinueBtnClicked")
    Module.Settlement.Config.Events.evtContinueClicked:Invoke()
    self:_ContinueProcess()
end

function EvacuatePrivateTrophyInfoViewHD:_ContinueProcess()
    self:_CloseSelf()
end

function EvacuatePrivateTrophyInfoViewHD:_CloseSelf()
    self._wtContinueBtn:SetIsEnabled(false)
    if self._bIsFinalSettlementPanel then
        Module.Settlement.Config.Events.evtCloseRootCutscene:Invoke()
        SettlementLogic.EndSettlement()
        if Facade.GameFlowManager:CheckIsInFrontEnd() then
            Facade.UIManager:CloseAllPopUI()
            Facade.UIManager:PopAllUI(true, false, MapPopAllUIReason2Str.FinishSettlement)
        end
    else
        UIManager:CloseUI(self)
    end
end

function EvacuatePrivateTrophyInfoViewHD:_OnClearBags()
    loginfo("EvacuatePrivateTrophyInfoViewHD:_OnClearBags")
    self:_OnTransferBtnClick()
end

function EvacuatePrivateTrophyInfoViewHD:_OnTransferBtnClick()
    Module.Settlement.Config.Events.evtContinueClicked:Invoke()
    local items2Transfer=TrophyLogic.GetItems2Transfer()
    TrophyLogic.FilterTeammateBindings(items2Transfer)
    TrophyLogic.ProcessTransfer(items2Transfer)

end

function EvacuatePrivateTrophyInfoViewHD:_OnItemDoubleClicked(itemWidget)
    local item = itemWidget.item
    local depositId = self._wtWarehousePanel:GetCurrentShowDepositId()
    local alldepositIds = Server.InventoryServer:GetAllDepositIds()

    Module.Inventory:CommonItemDoubleClickedInWH2Slot(item, depositId, alldepositIds)
end

function EvacuatePrivateTrophyInfoViewHD:OnAnimFinished(anim)
    if anim == self.WBP_SettlementEquipmentSelf_Show then
        Module.Settlement.Config.Events.evtEvacuateTrophyViewLoadFinish:Invoke()
    end
end

-- BEGIN MODIFICATION @ VIRTUOS : 新增手柄快捷键 - 按钮
function EvacuatePrivateTrophyInfoViewHD:_InitShortcuts()
    if not self._Continue then
        self._Continue = self:AddInputActionBinding("SOLSettlementBackLobby", EInputEvent.IE_Pressed, self._OnContinueBtnClicked,self, EDisplayInputActionPriority.UI_Stack)
        self._wtContinueBtn:SetDisplayInputAction("SOLSettlementBackLobby", true, nil, true)
    end
    if not self._Transfer then
        self._Transfer = self:AddInputActionBinding("ClearBags", EInputEvent.IE_Pressed, self._OnTransferBtnClick,self, EDisplayInputActionPriority.UI_Stack)
        self._wtTransferBtn:SetDisplayInputActionWithLongPress(self._Transfer, self, "ClearBags", true, nil, true)
    end

    self._navGroupsMap = {}
    -- 角色装备
    local _wtPlayerEquipCanvasPanel = self._wtEquipPanel:Wnd("DFCanvasPanel_0", UIWidgetBase)
    if not self._navGroupPlayerEquip then

	self._navGroupPlayerEquip = WidgetUtil.RegisterNavigationGroup(_wtPlayerEquipCanvasPanel, self, "Hittest")

        
        -- //TODO 导航组接受焦点
        --self._navGroupPlayerEquip.OnNavGroupFocusReceivedEvent:Add(self._OnPlayerEquipNavGroupFocusReceived, self)
    end

    if self._navGroupPlayerEquip then
        local wtEuqipSlotView_3 = self._wtEquipPanel:Wnd("WBP_WarehouseEquipSlotView_3", UIWidgetBase)
        local wtEuqipSlotView_5 = self._wtEquipPanel:Wnd("WBP_WarehouseEquipSlotView_5", UIWidgetBase)
        local wtEuqipSlotView_8 = self._wtEquipPanel:Wnd("WBP_WarehouseEquipSlotView_8", UIWidgetBase)
        local wtEuqipSlotView_9 = self._wtEquipPanel:Wnd("WBP_WarehouseEquipSlotView_9", UIWidgetBase)
        local wtEuqipSlotView_1 = self._wtEquipPanel:Wnd("WBP_WarehouseEquipSlotView_1", UIWidgetBase)
        local wtEuqipSlotView = self._wtEquipPanel:Wnd("WBP_WarehouseEquipSlotView", UIWidgetBase)

        self._navGroupPlayerEquip:AddNavWidgetToArray(wtEuqipSlotView_3)
        self._navGroupPlayerEquip:AddNavWidgetToArray(wtEuqipSlotView_5)
        self._navGroupPlayerEquip:AddNavWidgetToArray(wtEuqipSlotView_8)
        self._navGroupPlayerEquip:AddNavWidgetToArray(wtEuqipSlotView_9)
        self._navGroupPlayerEquip:AddNavWidgetToArray(wtEuqipSlotView_1)
        self._navGroupPlayerEquip:AddNavWidgetToArray(wtEuqipSlotView)

        -- 角色提示
        local wtDFCommonCheckBoxWithText = self._wtEquipPanel:Wnd("wtDFCommonCheckBoxWithText", UIWidgetBase)
        local wtSlotCompLoadBtn = self._wtEquipPanel:Wnd("WBP_SlotCompLoadBtn_PC", UIWidgetBase)

        self._navGroupPlayerEquip:AddNavWidgetToArray(wtDFCommonCheckBoxWithText)
        self._navGroupPlayerEquip:AddNavWidgetToArray(wtSlotCompLoadBtn)

        -- 制式套装提示
        self._navGroupPlayerEquip:AddNavWidgetToArray(self._wtRentalTips)

        self._navGroupPlayerEquip:SetAutoUseRootGeometrySize(false)
        -- self._navGroupPlayerEquip:SetCustomSimulateMouseKeyConfigName("LootingKeyConfig")
        self._navGroupPlayerEquip:SetNavSelectorWidgetVisibility(true)

        local PlayerEquipNavStrategy = self._navGroupPlayerEquip:GetOwnerNavStrategy()
        if PlayerEquipNavStrategy then
            PlayerEquipNavStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.HittestOrDistance)
        end

        table.insert(self._navGroupsMap, {navGroup = self._navGroupPlayerEquip, rootWidget = _wtPlayerEquipCanvasPanel})
    end

    -- 装备容器
    local _wtEquipVerticalBox = self._wtEquipPanel:Wnd("DFVerticalBox_0", UIWidgetBase)
    local _wtEuqipMainScrollBox = self._wtEquipPanel:Wnd("wtMainScrollBox", UIWidgetBase)
    if not self._navGroupEquipBox then
        self._navGroupEquipBox = WidgetUtil.RegisterNavigationGroup(_wtEquipVerticalBox, self, "Hittest")
        --self._navGroupEquipBox.OnNavGroupFocusReceivedEvent:Add(self._OnEquipBoxNavGroupFocusReceived, self)
    end

    if self._navGroupEquipBox then
        self._navGroupEquipBox:AddNavWidgetToArray(_wtEquipVerticalBox)
        self._navGroupEquipBox:SetScrollRecipient(_wtEuqipMainScrollBox)
        -- self._navGroupEquipBox:SetCustomSimulateMouseKeyConfigName("LootingKeyConfig")
        self._navGroupEquipBox:SetNavSelectorWidgetVisibility(true)
        
        local equipBoxNavStrategy = self._navGroupEquipBox:GetOwnerNavStrategy()
        if equipBoxNavStrategy then
            equipBoxNavStrategy:SetHitPadding(2.0)
            -- 关闭导航组中水平方向的保底导航
            equipBoxNavStrategy:SetHittestFallbackConfig(EGPUINavHittestFallbackType.Horizental, false, false)
            equipBoxNavStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.HittestOrDistance)
        end

        --WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroupEquipBox)

        table.insert(self._navGroupsMap, {navGroup = self._navGroupEquipBox, rootWidget = _wtEquipVerticalBox})
    end

    -- 仓库
    local _wtWarehouse = self._wtWarehousePanel:Wnd("wtWarehouse", UIWidgetBase)
    local _wtWarehouseScrollBox = _wtWarehouse:Wnd("wtDepositScrollBox", UIWidgetBase)

    if not self._NavGroupWareHouse then
        self._NavGroupWareHouse = WidgetUtil.RegisterNavigationGroup(_wtWarehouse, self, "LuaExtension")
    end

    if self._NavGroupWareHouse then
        self._NavGroupWareHouse:AddNavWidgetToArray(_wtWarehouse)
        self._NavGroupWareHouse:SetScrollRecipient(_wtWarehouseScrollBox)
        self._NavGroupWareHouse:SetNavSelectorWidgetVisibility(true)
        -- self._NavGroupWareHouse:SetCustomSimulateMouseKeyConfigName("LootingKeyConfig")
        
        local wareHouseNavStrategy = self._NavGroupWareHouse:GetOwnerNavStrategy()
        if wareHouseNavStrategy then
            wareHouseNavStrategy:SetHitPadding(2.0)
            wareHouseNavStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.HittestOrDistance)
            -- 关闭导航组中水平方向的保底导航
            wareHouseNavStrategy:SetHittestFallbackConfig(EGPUINavHittestFallbackType.Horizental, false, false)
            -- 启用TopLeft类型的默认聚焦位置
            wareHouseNavStrategy:SetBestFocusWidgetDefaultType(EGPUINavGroupBestDefaultType.TopLeft)
        end

        table.insert(self._navGroupsMap, {navGroup = self._NavGroupWareHouse, rootWidget = self._wtWarehousePanel})
    end
    -- 启用禁用A键导航的配置
    if not self._navConfigHandler then
        self._navConfigHandler = WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)
    end
    
    if not self._NavGroupGain then
        self._NavGroupGain = WidgetUtil.RegisterNavigationGroup(self._wtGains, self, "Hittest")
       -- table.insert(self._NavGroupGain, {navGroup = self._NavGroupWareHouse, rootWidget = self._wtWarehousePanel})
    end
end

function EvacuatePrivateTrophyInfoViewHD:_RemoveShortcuts()
    if self._Continue then
        self:RemoveInputActionBinding(self._Continue)
        self._Continue= nil
    end
    if self._Transfer then
        self:RemoveInputActionBinding(self._Transfer)
        self._Transfer= nil
    end

    if self._navGroupPlayerEquip then
        self._navGroupPlayerEquip:ClearCustomSimulateMouseKeyConfigName()
        self._navGroupPlayerEquip = nil
    end

    if self._navGroupEquipBox then
        self._navGroupEquipBox:ClearCustomSimulateMouseKeyConfigName()
        self._navGroupEquipBox = nil
    end

    if self._NavGroupWareHouse then
        self._NavGroupWareHouse:ClearCustomSimulateMouseKeyConfigName()
        self._NavGroupWareHouse = nil
    end
    if self._OnNotifyInputTypeChangedHandle then
        UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._OnNotifyInputTypeChangedHandle)
        self._OnNotifyInputTypeChangedHandle = nil
    end
    Module.CommonBar:RecoverBottomBarInputSummaryList()
    WidgetUtil.RemoveNavigationGroup(self)
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    self._navConfigHandler = nil
end
-- END MODIFICATION

return EvacuatePrivateTrophyInfoViewHD