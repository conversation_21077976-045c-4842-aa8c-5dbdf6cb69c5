----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRankingList)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class RankingListHistory : UIWidgetBase
local RankingListHistory = ui("RankingListHistory")
local EDescendantScrollDestination = import "EDescendantScrollDestination"
-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

function RankingListHistory:Ctor()
    -- CommonBar
    Module.CommonBar:RegStackUITopBarTitle(self, Module.RankingList.Config.Loc.RankingHistoryTitle)
    if DFHD_LUA == 1 then
        local ETopBarStyleFlag = Module.CommonBar.Config.ETopBarStyleFlag
        Module.CommonBar:RegStackUITopBarStyle(
            self,
            ETopBarStyleFlag.DefaultSecondary & ~(ETopBarStyleFlag.Team | ETopBarStyleFlag.Friends | ETopBarStyleFlag.Currency)
        )
    else
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.HideInfo)
    end
    -- 历史记录
    self._wtModeIcon = self:Wnd("DFImage_26", UIImage)  -- 模式Icon
    self._wtModeTitle = self:Wnd("DFTextBlock_25", UITextBlock)  -- 模式标题
    self._wtCountryRank = self:Wnd("WBP_RankingList_OverviewInfor_1", UIWidgetBase):Wnd("DFTextBlock", UITextBlock)  -- 国榜排名
    self._wtProvinceRank = self:Wnd("WBP_RankingList_OverviewInfor_2", UIWidgetBase):Wnd("DFTextBlock", UITextBlock)  -- 省榜排名
    self._wtCityRank = self:Wnd("WBP_RankingList_OverviewInfor_3", UIWidgetBase):Wnd("DFTextBlock", UITextBlock)  -- 市榜排名
    self._wtRankTypeDropDown = UIUtil.WndDropDownBox(self, "wtSeason_1", self._ChangeRankDataTypeDrop)  -- 榜单总览
    --self._wtRankTypeDropDown:Collapsed()
    self._wtSeasonDropDown = UIUtil.WndDropDownBox(self, "wtSeason", self._ChangeSeasonDrop)  -- 赛季总览
    self._wtHistoryList = UIUtil.WndScrollBox(self, "DFScrollBox_52", self._OnGetHistoryListCount, self._OnProcessHistoryWidget)  -- 内容展示
    self._validSeasons = {}  -- 参与的赛季
    self._historyData = {}  -- 处理服务器端后的完整数据
    self._playerName = ""
    self._curDataTypes = {}  -- 当前榜单类型
    self._rankDataType_idx = 0
    self._season_idx = 0
    -- She3
    self._She3_ComingSoon = {5, 4}  -- 需显示敬请期待的赛季

    -- 本地化
    -- local txt = Module.RankingList.Config.Loc.CountryObtain
    -- self:Wnd("WBP_RankingList_OverviewInfor_1", UIWidgetBase):Wnd("DFTextBlock_21", UITextBlock):SetText(Module.RankingList.Config.Loc.CountryObtain)  -- 国榜获得
    -- self:Wnd("WBP_RankingList_OverviewInfor_2", UIWidgetBase):Wnd("DFTextBlock_21", UITextBlock):SetText(Module.RankingList.Config.Loc.ProvinceObtain)  -- 省榜获得
    -- self:Wnd("WBP_RankingList_OverviewInfor_3", UIWidgetBase):Wnd("DFTextBlock_21", UITextBlock):SetText(Module.RankingList.Config.Loc.CityObtain)  -- 市榜获得
end

function RankingListHistory:OnInitExtraData(dataType, isMp, playerName)
    self._dataType = dataType
    self._isMp = isMp
    self._playerName = playerName
    -- 榜单信息
    self._rankDataTypeTxtTbl = {
        Module.RankingList.Config.Loc.RankDataTypeOverview,
    }
    if self._isMp then
        self._wtModeIcon:AsyncSetImagePath(Module.RankingList.Config.MpHistoryImg, false)
        for _, value in ipairs(Module.RankingList.Config.Loc.MpRankListTabTxt) do
            table.insert(self._rankDataTypeTxtTbl, value)
        end
        self._curDataTypes = Module.RankingList.Config.MpRankDataType
    else
        self._wtModeIcon:AsyncSetImagePath(Module.RankingList.Config.SolHistoryImg, false)
        for _, value in ipairs(Module.RankingList.Config.Loc.SolRankListTabTxt) do
            table.insert(self._rankDataTypeTxtTbl, value)
        end
        self._curDataTypes = Module.RankingList.Config.SolRankDataType
    end
    -- 赛季信息(from RoleInfo) 倒序, i+1为实际赛季(与下拉框对应)
    self._seasonTxtTbl = {
        Module.RankingList.Config.Loc.SeasonOverview,
    }  -- 赛季描述 i+1为实际赛季
    self._seasonTimeTbl = {
        {Begin = "", End = ""}
    }  -- 赛季时间
    local serialNumber = Server.TournamentServer:GetCurSerial()
    if serialNumber then
        for index = serialNumber, 1, -1 do  -- 倒序遍历
            if IsBuildRegionCN() then
                local seasonConfig = Module.Tournament:GetSeasonConfigBySerial(index)
                table.insert(self._seasonTxtTbl, seasonConfig.Name)
                table.insert(self._seasonTimeTbl, {
                    Begin = seasonConfig.StartTime,
                    End = seasonConfig.EndTime
                })
            else
                if index ~= 1 then
                    local seasonConfig = Module.Tournament:GetSeasonConfigBySerial(index)
                    table.insert(self._seasonTxtTbl, seasonConfig.Name)
                    table.insert(self._seasonTimeTbl, {
                        Begin = seasonConfig.StartTime,
                        End = seasonConfig.EndTime
                    })
                end
            end
        end
    end
    self._historyData = self:_Preprocess(Server.RankingListServer:GetSelfHistoryData())

    -- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        if self._wtRankTypeDropDown.MenuAnchor then
            self._wtRankTypeDropDown:Event("PostOnMenuOpenChanged_GamepadUsed", self._OnRankTypeDropToggled, self)
            self._wtSeasonDropDown:Event("PostOnMenuOpenChanged_GamepadUsed", self._OnSeasonDropToggled, self)
        end
        
        local wtKeyIconBox1 = self._wtRankTypeDropDown:Wnd("DFCommonCheckButton", UIWidgetBase):Wnd("wtKeyIcon", HDKeyIconBox)
        local wtKeyIconBox2 = self._wtSeasonDropDown:Wnd("DFCommonCheckButton", UIWidgetBase):Wnd("wtKeyIcon", HDKeyIconBox)
        if wtKeyIconBox1 then
            wtKeyIconBox1:Visible()
            wtKeyIconBox1:SetOnlyDisplayOnGamepad(true)
            wtKeyIconBox1:InitByDisplayInputActionName("Common_LeftStickClick_Gamepad", true, 0, true)
            wtKeyIconBox2:Visible()
            wtKeyIconBox2:SetOnlyDisplayOnGamepad(true)
            wtKeyIconBox2:InitByDisplayInputActionName("Common_RightStickClick_Gamepad", true, 0, true)
        end
    end
    -- END MODIFICATION
end


function RankingListHistory:OnShowBegin()
    UIUtil.InitDropDownBox(self._wtRankTypeDropDown, self._rankDataTypeTxtTbl, {}, self._rankDataType_idx)
    UIUtil.InitDropDownBox(self._wtSeasonDropDown, self._seasonTxtTbl, {}, self._season_idx)
    -- self:Mock()
    if self._isMp then
        self._wtModeTitle:SetText(Module.RankingList.Config.Loc.MpTitle)
    else
        self._wtModeTitle:SetText(Module.RankingList.Config.Loc.SolTitle)
    end
    local country_cnt, province_cnt, city_cnt = self:CalcRankCount(self._historyData)
    self:RefreshRankCnt(country_cnt, province_cnt, city_cnt)
    self._wtHistoryList:RefreshAllItems()
    -- BEGIN MODIFICATION @ VIRTUOS : 
    -- self:_EnableGamepadFeature()
    -- END MODIFICATION
    self:StopAnimation(self.WBP_RankingList_History_in)
    -- PlayAnimationByName(FName AnimName, float StartAtTime = 0.0f, int32 NumLoopsToPlay = 1, EUMGSequencePlayMode::Type PlayMode = EUMGSequencePlayMode::Forward, float PlaybackSpeed = 1.0f, bool bRestoreState = false);
    self:PlayAnimation(self.WBP_RankingList_History_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
end

function RankingListHistory:RefreshRankCnt(_countryRank, _provinceRank, _cityRank)
    self._wtCountryRank:SetText(_countryRank)
    self._wtProvinceRank:SetText(_provinceRank)
    self._wtCityRank:SetText(_cityRank)
end

function RankingListHistory:OnHideBegin()
    self:StopAnimation(self.WBP_RankingList_History_in)    
    self:PlayAnimation(self.WBP_RankingList_History_in, 0, 1, EUMGSequencePlayMode.Reverse, 1, false)
    self:RemoveAllLuaEvent()
    if self._inputTypeChangedHandle then
        UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._inputTypeChangedHandle)
        self._inputTypeChangedHandle = nil
    end
    -- BEGIN MODIFICATION @ VIRTUOS : 
    -- self:_DisableGamepadFeature()
    -- END MODIFICATION
end

function RankingListHistory:_Preprocess(res)
    -- 倒序排列当前赛季不同期
    local data_list = {}
    self._validSeasons = {}
    local target_type = nil
    if self._rankDataType_idx > 0 then
        target_type = self._curDataTypes[self._rankDataType_idx]  -- 获取当前类型
    end

    for i, item in ipairs(res) do
        -- 服务器请求数据
        local id = item.season_id
        local season_id, season_name = self:GetSeasonTxt(id)
        local season_begin, season_end = self:GetSeasonTime(id)
        local season_data = {}
        deepcopy(season_data, item.season_data)
        for j = #season_data, 1, -1 do
            local period_data = season_data[j]
            for k = #period_data.content, 1, -1 do
                local content_data = period_data.content[k]
                -- 剔除不同模式
                local isMp = self:_IsMp(content_data.rank_data_type)
                if isMp == self._isMp then
                    -- 保留
                else
                    table.remove(period_data.content, k)
                end
                -- 剔除不符合类型
                if target_type == nil then  -- 模式总览
                    -- 保留
                else
                    if content_data.rank_data_type == target_type then
                        -- 保留
                    else
                        table.remove(period_data.content, k)
                    end
                end
            end
        end

        table.insert(data_list, {
            season_id = id,
            seasonNum = season_id,
            seasonTheme = season_name,
            seasonTime = self:FormatSeasonTime(season_begin).."-"..self:FormatSeasonTime(season_end),  -- 赛季时间
            seasonData = season_data
        })
        table.insert(self._validSeasons, {
            season = id,  -- 赛季
            widget = nil  -- 滑动控件
        })  -- 这里插入实际参与的赛季号
    end

    -- 处理虚位以待(删除除当前赛季外,content为空的赛季)
    local unvalid_seasons = {}
    local serialNumber = Server.TournamentServer:GetCurSerial()
    for season, seasonData in ipairs(data_list) do
        for period, periodData in ipairs(seasonData.seasonData) do
            if #periodData.content == 0 and seasonData.season_id ~= serialNumber then
                unvalid_seasons[seasonData.season_id] = true
            end
        end
    end
    for i=#data_list, 1, -1  do
        for k, _ in pairs(unvalid_seasons) do
            if data_list[i].season_id == k then
                table.remove(data_list, i)
                break
            end
        end
    end
    -- 若当前赛季无数据，新增当前赛季信息  Module.RankingList.Config.Loc.Current
    local season_begin, season_end = self:GetSeasonTime(serialNumber)
    if #data_list == 0 or data_list[1].season_id ~= serialNumber then
        local season_id, season_name = self:GetSeasonTxt(serialNumber)
        table.insert(data_list, 1, {
            season_id = serialNumber,
            seasonNum = season_id,
            seasonTheme = season_name,
            seasonTime = self:FormatSeasonTime(season_begin).."-"..Module.RankingList.Config.Loc.Current,
            seasonData = {
                {
                    period = Server.RankingListServer:CalcPeriod(serialNumber, os.time()),  -- 当前期数
                    content = {}  -- 空(虚位以待)
                }
            }
        })
        table.insert(self._validSeasons, 1, {
            season = serialNumber,  -- 赛季
            widget = nil  -- 滑动控件
        })  -- 这里插入实际参与的赛季号
    else
        -- 存在当前赛季，设置至今即可
        data_list[1].seasonTime = self:FormatSeasonTime(season_begin).."-"..Module.RankingList.Config.Loc.Current
    end

    return self:_She3_HardCodeProcess(data_list)  -- She3特殊处理
    -- local mock_seasonData = {
    --     {
    --         period = 5,
    --         content = {}  -- 最新的一期如果无任何称号需为空表
    --     },
    --     {
    --         period = 3,
    --         content = {
    --             { titleId = 42030161314, adCode = 0, ranking = 0, rank_data_type = 2001 },
    --             { titleId = 42030161314, adCode = 0, ranking = 0, rank_data_type = 2001 },
    --             { titleId = 42030161314, adCode = 0, ranking = 0, rank_data_type = 2001 },
    --         }
    --     },
    --     {
    --         period = 1,
    --         content = {
    --             { titleId = 42030161314, adCode = 0, ranking_no = 0, rank_data_type = 2001 },
    --             { titleId = 42030161314, adCode = 0, ranking_no = 0, rank_data_type = 2001 },
    --             { titleId = 42030161314, adCode = 0, ranking_no = 0, rank_data_type = 2001 },
    --         }
    --     },
    -- }
    -- self._mock_itemData = {}
    -- for i = 5, 1, -1 do
    --     if i ~= 2 then  -- 模拟缺席第二赛季
    --         table.insert(self._validSeasons, {
    --             season = i,  -- 赛季
    --             widget = nil  -- 滑动控件
    --         })  -- 这里插入实际参与的赛季号
    --         -- 服务器请求数据
    --         table.insert(self._mock_itemData, {
    --             seasonNum = "S" .. i,
    --             seasonTheme = "起源",
    --             seasonTime = "2025/07/03-至今",
    --             seasonData = mock_seasonData
    --         })
    --     end
    -- end
end

function RankingListHistory:_She3_HardCodeProcess(tbl)
    -- She3特殊处理：后台只提供She3数据，之前的数据需显示敬请期待
    if self._She3_ComingSoon == nil or #self._She3_ComingSoon == 0 then
        return tbl
    end
    -- She3只含Sh3的数据(不应该有其他数据)
    if #tbl > 1 then
        logwarning("RankingListHistory:_She3_HardCodeProcess", "She3 Data Error，存在多于一个赛季的数据")
        return tbl
    end
    for _, season in pairs(self._She3_ComingSoon) do
        if tbl[1] ~= nil and tbl[1].season_id == season then  -- 至少会有一条空数据
            logwarning("RankingListHistory:_She3_HardCodeProcess", "She3 Data Error，敬请期待存在数据:", season)
        else
            local season_id, season_name = self:GetSeasonTxt(season)
            local season_begin, season_end = self:GetSeasonTime(season)
            table.insert(tbl, {
                season_id = season,
                seasonNum = season_id,
                seasonTheme = season_name,
                seasonTime = self:FormatSeasonTime(season_begin).."-"..self:FormatSeasonTime(season_end),
                seasonData = {
                    {
                        period = -1,  -- 特殊期数（显示敬请期待）
                        content = {}
                    }
                }
            })
            table.insert(self._validSeasons, {
                season = season,  -- 赛季
                widget = nil  -- 滑动控件
            })  -- 这里插入实际参与的赛季号
        end
    end
    return tbl
end

function RankingListHistory:_OnGetHistoryListCount()
    return #self._historyData  -- 需要渲染多少个赛季
end

function RankingListHistory:_OnProcessHistoryWidget(position, itemWidget)
    position = position + 1 -- C index to Lua index
    loginfo("RankingListHistory:_OnProcessHistoryWidget", position, itemWidget)
    if next(self._historyData) ~= nil then
        self._validSeasons[position].widget = itemWidget
        local res = self._historyData[position]
        itemWidget:RefreshHistoryItem(position, itemWidget, res.season_id, res.seasonNum, res.seasonTheme, res.seasonTime, res.seasonData, self._playerName)
    end
end

function RankingListHistory:_ChangeRankDataTypeDrop(idx)
    self._rankDataType_idx = idx
    -- 处理数据
    self._historyData = self:_Preprocess(Server.RankingListServer:GetSelfHistoryData())
    local country_cnt, province_cnt, city_cnt = self:CalcRankCount(self._historyData)
    self:RefreshRankCnt(country_cnt, province_cnt, city_cnt)
    -- 处理表现
    self._wtHistoryList:ClearChildren()
    self._wtHistoryList:RefreshAllItems()

    self:StopAnimation(self.WBP_RankingList_History_in)    
    self:PlayAnimation(self.WBP_RankingList_History_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    if IsHD() and self._NavGroup_HistoryList ~= nil then
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup_HistoryList)
    end
end

function RankingListHistory:_ChangeSeasonDrop(idx)
    self._season_idx = idx
    if idx == 0 then
        self._wtHistoryList:ScrollToOffset(0)
    else
        local season_id = #self._seasonTxtTbl - idx  -- 实际赛季
        for i, item in ipairs(self._validSeasons) do
            if item.season == season_id and isvalid(item.widget) then
                self._wtHistoryList:ScrollWidgetIntoView(item.widget, true, EDescendantScrollDestination.TopOrLeft)
                if IsHD() and self._NavGroup_HistoryList ~= nil then
                    WidgetUtil.SetUserFocusToWidget(item.widget, true)
                end
                break
            end
        end
    end
end

--- BEGIN MODIFICATION @ VIRTUOS :
function RankingListHistory:_EnableGamepadFeature()
    if not IsHD() then
        return
    end
    -- 内容列表
    self._NavGroup_HistoryList = WidgetUtil.RegisterNavigationGroup(self._wtHistoryList, self, "Grid1D")
    if self._NavGroup_HistoryList ~= nil then
        self._NavGroup_HistoryList:AddNavWidgetToArray(self._wtHistoryList)
        self._NavGroup_HistoryList:SetScrollRecipient(self._wtHistoryList)
    end

    self:_EnableGamepadInputs()
    
    if not WidgetUtil.TryFocusLastGroupByOwner(self) then
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup_HistoryList)
    end
end

function RankingListHistory:_DisableGamepadFeature()
    if not IsHD() then
        return
    end
    self:_DisableGamepadInputs()
    WidgetUtil.RemoveNavigationGroup(self)
    self._NavGroup_HistoryList = nil
end

function RankingListHistory:_EnableGamepadInputs()
    if not self:IsVisible() then
        return
    end
    -- Informal fix to inputs missing issue caused by unexpected "OnShowBegin" calls from RankingListHistory and disordered lifecycle.
    self:_DisableGamepadInputs()
    
    if not self._switchRankTypeHandle then
        local switchRankTypeEvent = SafeCallBack(
            function()
                if self._wtRankTypeDropDown:GetIsEnabled() then
                    self._wtRankTypeDropDown:OpenMenu()
                end
            end, self)
        self._switchRankTypeHandle = self:AddInputActionBinding("Common_LeftStickClick_Gamepad", EInputEvent.IE_Pressed, switchRankTypeEvent, self, EDisplayInputActionPriority.UI_Pop)
    end
    if not self._switchSeasonHandle then
        local switchSeasonEvent = SafeCallBack(
                function()
                    if self._wtSeasonDropDown:GetIsEnabled() then
                        self._wtSeasonDropDown:OpenMenu()
                    end
                end, self)
        self._switchSeasonHandle = self:AddInputActionBinding("Common_RightStickClick_Gamepad", EInputEvent.IE_Pressed, switchSeasonEvent, self, EDisplayInputActionPriority.UI_Pop)
    end
end

function RankingListHistory:_DisableGamepadInputs()
    if self._switchRankTypeHandle then
        self:RemoveInputActionBinding(self._switchRankTypeHandle)
        self._switchRankTypeHandle = nil
    end
    self:_OnRankTypeDropToggled(false)
end


function RankingListHistory:_OnRankTypeDropToggled(bOpen)
    if bOpen then
        if not self._rankTypeNavGroup then
            self._rankTypeNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtRankTypeDropDown.ScrollGridBox, self._wtRankTypeDropDown, "Hittest")
            if self._rankTypeNavGroup then
                self._rankTypeNavGroup:AddNavWidgetToArray(self._wtRankTypeDropDown.ScrollGridBox)
                self._rankTypeNavGroup:SetScrollRecipient(self._wtRankTypeDropDown.ScrollGridBox)
                self._rankTypeNavGroup:MarkIsStackControlGroup()
            end
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._rankTypeNavGroup)
        end

        if not self._closeRankTypeDropHandle then
            local switchRankTypeEvent = SafeCallBack(
            function()
                if self._wtRankTypeDropDown:GetIsEnabled() then
                    self._wtRankTypeDropDown:CloseMenu()
                    self:_OnRankTypeDropToggled(false)
                    if self._NavGroup_HistoryList then
                        WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup_HistoryList)
                    end
                end
            end, self)
            self._closeRankTypeDropHandle = self:AddInputActionBinding("Back_Gamepad", EInputEvent.IE_Pressed, switchRankTypeEvent, self._wtRankTypeDropDown, EDisplayInputActionPriority.UI_Pop)
        end
    else
        if self._rankTypeNavGroup then
            WidgetUtil.RemoveNavigationGroup(self._wtRankTypeDropDown)
            self._rankTypeNavGroup = nil
        end

        if self._closeRankTypeDropHandle then
            self:RemoveInputActionBinding(self._closeRankTypeDropHandle)
            self._closeRankTypeDropHandle = nil
        end
    end
end

function RankingListHistory:_OnSeasonDropToggled(bOpen)
    if bOpen then
        if not self._seasonNavGroup then
            self._seasonNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtSeasonDropDown.ScrollGridBox, self._wtSeasonDropDown, "Hittest")
            if self._seasonNavGroup then
                self._seasonNavGroup:AddNavWidgetToArray(self._wtSeasonDropDown.ScrollGridBox)
                self._seasonNavGroup:SetScrollRecipient(self._wtSeasonDropDown.ScrollGridBox)
                self._seasonNavGroup:MarkIsStackControlGroup()
            end
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._seasonNavGroup)
        end

        if not self._closeSeasonDropHandle then
            local switchSeasonEvent = SafeCallBack(
                    function()
                        if self._wtSeasonDropDown:GetIsEnabled() then
                            self._wtSeasonDropDown:CloseMenu()
                            self:_OnRankTypeDropToggled(false)
                            if self._NavGroup_HistoryList then
                                WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup_HistoryList)
                            end
                        end
                    end, self)
            self._closeSeasonDropHandle = self:AddInputActionBinding("Back_Gamepad", EInputEvent.IE_Pressed, switchSeasonEvent, self._wtSeasonDropDown, EDisplayInputActionPriority.UI_Pop)
        end
    else
        if self._seasonNavGroup then
            WidgetUtil.RemoveNavigationGroup(self._wtSeasonDropDown)
            self._seasonNavGroup = nil
        end

        if self._closeSeasonDropHandle then
            self:RemoveInputActionBinding(self._closeSeasonDropHandle)
            self._closeSeasonDropHandle = nil
        end
    end
end

-- 根据实际赛季拿赛季标题
function RankingListHistory:GetSeasonTxt(serial)
    local serial_drop = #self._seasonTxtTbl - serial + 1 -- 实际赛季-->下拉框索引
    if serial_drop <= #self._seasonTxtTbl then
        local seasonTitle = self._seasonTxtTbl[serial_drop]
        local colonPos = string.find(seasonTitle, ":")
        local season_id = string.sub(seasonTitle, 1, colonPos - 1)
        local season_name = string.sub(seasonTitle, colonPos + 1)
        return season_id, season_name
    else
        logwarning("RankingListHistory:GetSeasonTxt: Invalid serial number: " .. tostring(serial))
        return "SN:Feature"
    end
end

-- 根据实际赛季拿起始和结束时间
function RankingListHistory:GetSeasonTime(serial)
    local season_id = #self._seasonTimeTbl - serial + 1 -- 实际赛季-->下拉框索引
    if season_id <= #self._seasonTimeTbl then
        return self._seasonTimeTbl[season_id].Begin, self._seasonTimeTbl[season_id].End
    else
        logwarning("RankingListHistory:GetSeasonTxt: Invalid serial number: " .. tostring(serial))
        return "", ""
    end
end

function RankingListHistory:FormatSeasonTime(seasonTime)
    local step1 = string.match(seasonTime, "%d+%-%d+%-%d+")
    if step1 ~= nil then
        return step1:gsub("-", "/")
    end
    return step1
end

function RankingListHistory:CalcRankCount(history_list)
    local country_cnt = 0
    local province_cnt = 0
    local city_cnt = 0
    for i, item in ipairs(history_list) do
        for season, seasonData in ipairs(item.seasonData) do
            for period, periodData in ipairs(seasonData.content) do
                if periodData.region == RankBoardRegion.REGION_ALL then
                    country_cnt = country_cnt + 1
                elseif periodData.region == RankBoardRegion.REGION_PREVINCE then
                    province_cnt = province_cnt + 1
                elseif periodData.region == RankBoardRegion.REGION_CITY then
                    city_cnt = city_cnt + 1
                end
            end
        end
    end
    return country_cnt, province_cnt, city_cnt
end

function RankingListHistory:_IsMp(rankDataType)
    local ret = true
    for _, value in ipairs(Module.RankingList.Config.SolRankDataType) do
        if rankDataType == value then
            ret = false
            break
        end
    end
    return ret
end

--- END MODIFICATION

return RankingListHistory