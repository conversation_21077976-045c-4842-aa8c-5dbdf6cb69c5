---------- LOG FUNCTION AUTO GENERATE -------------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
-------- LOG FUNCTION AUTO GENERATE END -----------

---对应蓝图:WBP_Example
---@class MorgenEventItem : LuaUIBaseView
local MorgenEventItem = ui("MorgenEventItem")
local ActivityConfig = Module.Activity.Config
local ActivityLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"

function MorgenEventItem:Ctor()
	self.bIsSelected = false

	self._wtBtn = self:Wnd("DFButton_72", UIButton)
    self._wtBtn:Event("OnClicked", self.OnClicked, self)
	self._wtBtn:Event("OnHovered", self.OnHovered, self)
    self._wtBtn:Event("OnUnHovered", self.OnUnHovered, self)

	self._wtImage = self:Wnd("DFImage_107", UIImage)
	self._wtTitle = self:Wnd("DFTextBlock_48", UITextBlock)
	self._wtName = self:Wnd("DFTextBlock_61", UITextBlock)
	self._wtDesc = self:Wnd("DFTextBlock", UITextBlock)
	self._wtLineBox = UIUtil.WndScrollBox(self, "DFScrollBox_5", self.OnGetLineCount, self.OnProcessLineWidget)

	self:SetStyle(0)
end

function MorgenEventItem:RefreshInfo(eventInfo, curCycle)
	self.eventInfo = eventInfo
    self.curCycle = curCycle or 0

	-- self._wtImage:AsyncSetImagePath(eventInfo.avatarIsset)
	-- self._wtTitle:SetText(ActivityLogic.HandleLocalizeText(eventInfo.tag))
	-- self._wtName:SetText(ActivityLogic.HandleLocalizeText(eventInfo.name))
	-- self._wtDesc:SetText(ActivityLogic.HandleLocalizeText(eventInfo.intro))
	self._wtTitle:SetText(eventInfo.tag)
	self._wtName:SetText(eventInfo.name)
	self._wtDesc:SetText(eventInfo.shortDesc)

	self.descs = {[1] = eventInfo.descOne, [2] = eventInfo.descTwo}

	self._wtLineBox:RefreshAllItems()
end

--- 历史信息列表
function MorgenEventItem:OnGetLineCount()
    return self.curCycle <= 2 and self.curCycle or 2
end

function MorgenEventItem:OnProcessLineWidget(index, widget)
    local text = self.descs[index + 1]
    widget:RefreshInfo(text)
end

function MorgenEventItem:RefreshUI()
end

function MorgenEventItem:OnClicked()
	if self.bIsSelected then
		self:SetStyle(0)
	else
		self:SetStyle(2)
    	self:PlayAnimation(self.WBP_MorgenGame_Item_click, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
	end

	self.bIsSelected = not self.bIsSelected
end

function MorgenEventItem:OnHovered()
	if not self.bIsSelected then
		self:SetStyle(1)
	end
end

function MorgenEventItem:OnUnHovered()
	if not self.bIsSelected then
		self:SetStyle(0)
	end
end

return MorgenEventItem