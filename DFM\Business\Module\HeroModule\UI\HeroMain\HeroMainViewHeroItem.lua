local HeroHelperTool = require "DFM.StandaloneLua.BusinessTool.HeroHelperTool"
----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMHero)
----- LOG FUNCTION AUTO GENERATE END -----------

---@enum EHeroMainViewHeroItemTopBarnerType 横幅类型
local EHeroMainViewHeroItemTopBarnerType = {
	Yellow = 0, --限时
	Green = 1, --招募
}


---@class HeroMainViewHeroItem : LuaUIBaseView
local HeroMainViewHeroItem = ui("HeroMainViewHeroItem")
local HeroDataTable = Facade.TableManager:GetTable("Hero/HeroData")
local EWidgetClipping = import "EWidgetClipping"
local UKismetInputLibrary = import "KismetInputLibrary"
local UKismetTextLibrary = import "KismetTextLibrary"
local ETextOverflowPolicy = import "ETextOverflowPolicy"
local heroSkillColor = {
    select = Facade.ColorManager:GetLinerColor("Basic_White(80%)"),
    unselect = Facade.ColorManager:GetLinerColor("Basic_White(50%)")
}
local function log(...)
	loginfo("[HeroMainViewHeroItem]", ...)
end

function HeroMainViewHeroItem:Ctor()
	self:SetCppValue("bHandleClickR", true)
	self:SetCppValue("bHandleClick", true)
	self:SetCppValue("bHandleHover", true)
	self:SetCppValue("bSwallowClick", false)

	-- self:Event("OnMouseEnterEvent", self._OnMouseEnter, self)
    -- self:Event("OnMouseLeaveEvent", self._OnMouseLeave, self)

	self._wtBtn = self:Wnd("Button_112", UIButton)
	-- self._wtBtn:SetVisibility(ESlateVisibility.HitTestInvisible)
    self._wtBtn:Event("OnClicked", self.OnClicked, self)
	self._wtBtn:Event("OnHovered", self._OnMouseEnter, self)
	self._wtBtn:Event("OnUnhovered", self._OnMouseLeave, self)

	self._wtIconImage = self:Wnd("Image_Man", UIImage)
    self._wtArmedIcon = self:Wnd("Img_ArmedIcon", UIImage)
	self._wtOperateImage = self:Wnd("DFImage_154",UIImage)
	self._wtOperateImage:Collapsed()

	self._wtSelectImg = self:Wnd("Img_Select", UIWidgetBase)
	self._wtCompSelected = self:Wnd("WBP_SlotCompSelected",UIWidgetBase)
	self._wtCompSelected:SetCppValue("bSwallowClick", false)
	self._wtCompSelected:Collapsed()
	self:SetIsSelected(false)


	-- 解锁遮罩
	self._wtUnlockImage = self:Wnd("WBP_SlotCompMaskSmallLock",UIWidgetBase)
	self._wtUnlockImage:Collapsed()

	self._wtImageMask = self:Wnd("Image_Bg",UIImage)
	self._wtImageMask:Collapsed()

	-- 临时处理
	self._wtTempLockIcon = self:Wnd("DFImage_129", UIImage)

	self._wtMainContentPanel = self:Wnd("CanvasPanel_36",UIWidgetBase)
	--限时开放
	self._wtCountdown = self:Wnd("WBP_Store_Countdown_70", UIWidgetBase)
	--网吧特权
	self._wtCybercafebtn = self:Wnd("DFButton_Cybercafe", UIButton)
	self._wtCybercafeimg = self:Wnd("Img_Cybercafe", UIImage)
	self._wtCurUseTipsAnchor=self:Wnd("DFTipsAnchor_206",UIWidgetBase)

	if IsHD() then
		self._wtDFTipsAnchor = UIUtil.WndTipsAnchor(self, "DFTipsAnchor", self._ShowHeroGetMethodTips, self._HideHeroGetMethodTips)
	end


	---顶部横条文字超界数据预处理
	if self._wtCountdown then
		self._overflowAnchorMin = FVector2D(0,0)
		self._overflowAnchorMax = FVector2D(1,0)
		self._overflowMargin = FMargin(0,0,0,36)
		self._normalAnchorMin = nil
		self._normalAnchorMax = nil
		self._normalMargin = nil

		local compSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(self._wtCountdown)
		if compSlot then
			self._normalMargin = compSlot:GetOffsets()
			local anchors = compSlot:GetAnchors()
			if anchors then
				self._normalAnchorMin = anchors.Minimum
				self._normalAnchorMax = anchors.Maximum
			end
		end
	end
	
	-- BEGIN MODIFICATION @ VIRTUOS : Navigation
	if IsHD() then
		self:Event("OnFocusReceivedEvent", self.OnNavClicked, self)
		self._bIsOpenNavClick = false;
	end
	-- END MODIFICATION
	
	self._isHide = false
end


--==================================================
--region Life function
function HeroMainViewHeroItem:OnOpen()
	
end
function HeroMainViewHeroItem:OnClose()
	self:UnRegisterReddot()
	self:HideCurUseTip()
end
function HeroMainViewHeroItem:OnShowBegin()
	self:RefreshView()
end

function HeroMainViewHeroItem:OnHideBegin()
	self._isHide = true
end

function HeroMainViewHeroItem:_TriggerUnlockAnim()
	local lock = self._wtUnlockImage
	if lock then
		self:ToggleLockMask(true)
		lock:PlayAnimation(lock.WBP_SlotCompMaskSmallLock_Unlock, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
		Server.HeroServer:DoReadHero(self._heroId)
	end
	
end

function HeroMainViewHeroItem:OnShow()
	self:_AddAllEvent()
	self:RegisterReddot()
end
function HeroMainViewHeroItem:OnHide()
	self:_RemoveAllEvent()
	self:UnRegisterReddot()
end
function HeroMainViewHeroItem:OnInitExtraData()
end

function HeroMainViewHeroItem:RefreshView()
	if not self._heroId then
		return
	end
	local heroInfo = HeroDataTable[tostring(self._heroId)]
	if heroInfo then
		if heroInfo.Icon then
			self._wtIconImage:AsyncSetImagePath(heroInfo.Icon)
		end
		local armIcon = Module.Hero.Config.ArmedForceIconMapping[heroInfo.ArmedForceId]
		if armIcon then
			self._wtArmedIcon:AsyncSetImagePath(armIcon)
		end
		-- self._wtArmedIcon:SetColorAndOpacity(Module.Hero.Config.ExpertSceneEffectColor[heroInfo.ArmedForceId])
		self._wtArmedIcon:SetColorAndOpacity(heroSkillColor.unselect)
	end

	local topBannerContent = nil
	local topBannerStyle = nil

	--限时开放
	local LimitedTime = Server.HeroServer:GetHeroLimitedTime(self._heroId)
	if LimitedTime then
		topBannerContent = Module.Hero.Config.Loc.HeroLimitedTimeOpening
		topBannerStyle = EHeroMainViewHeroItemTopBarnerType.Yellow
	end

	if self._bShowRecruitmentInfo and Server.HeroServer:IsHeroRecruiting(self._heroId) then
		topBannerContent = Module.Hero.Config.Loc.HeroRecruiting
		topBannerStyle = EHeroMainViewHeroItemTopBarnerType.Green
	end

	if topBannerContent then
		self:_SetTopBannerContent(topBannerStyle,topBannerContent)
	else
		self:_SetTopBannerContent()
	end

	--网吧特权
	self:SetInternetBar()

	local bIsUnlock = Server.HeroServer:IsOwningHero(self._heroId)
	local bIsReaded = Server.HeroServer:IsReadedHero(self._heroId)
	local lock = self._wtUnlockImage
	if Server.ExperienceDataServer:GetExDataReasonByItemId(self._heroId) == EExperienceReasonType.CoffeeNet then
		self:ToggleLockMask(false)
	elseif bIsUnlock and lock then
		if bIsReaded then
			self:ToggleLockMask(false)
		else
			self:ToggleLockMask(true)
			self:_TriggerUnlockAnim()
		end
	else
		self:ToggleLockMask(true)
	end
end

function HeroMainViewHeroItem:OnClicked()
	if self._fCallback then
		self._fCallback(self)
	end
	if DFHD_LUA ~= 1 then
		if not self._abandonDefaultSelect then
			self:SetIsSelected(true)
		end
	end
end

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
function HeroMainViewHeroItem:OnNavClicked()
	if IsHD() then
		if self._bIsOpenNavClick then
			self:OnClicked()
		end
	end
end
-- END MODIFICATION

function HeroMainViewHeroItem:OnCurrencyHovered()
	if DFHD_LUA == 1 then
		if self._fHoverCallback then
			self._fHoverCallback(self)
		end
	end
end


function HeroMainViewHeroItem:OnNativeOnMouseButtonDown(inGeometry, inGestureEvent)

	-- if DFHD_LUA == 1 then
	-- 	local curKeyName = UKismetTextLibrary.Conv_TextToString(UKismetInputLibrary.Key_GetDisplayName(UKismetInputLibrary.PointerEvent_GetEffectingButton(inGestureEvent)))
	-- 	log(curKeyName)
	--  	if  UKismetTextLibrary.Conv_TextToString(UKismetInputLibrary.Key_GetDisplayName(UKismetInputLibrary.PointerEvent_GetEffectingButton(inGestureEvent))) == "Left Mouse Button" then
	-- 		if self._fCallback then
	-- 			self._fCallback(self)
	-- 		end
	-- 	end
	-- end
end

function HeroMainViewHeroItem:OnClickedR()
	if DFHD_LUA == 1 then
		if self._fRightButtonCallback then
			self._fRightButtonCallback(self)
		end
	end
end

function HeroMainViewHeroItem:_OnMouseEnter(_, _)
	if DFHD_LUA == 1 then
		if self._fEnterCallback then
			self._fEnterCallback(self)
		end
		self:PlayWidgetAnimAt(self.UnhoverAni, self.UnhoverAni:GetEndTime())
		self:PlayWidgetAnim(self.HoverAni)
	end
end

function HeroMainViewHeroItem:SetAbandonDefaultSelect(abandonDefaultSelect)
	loginfo("HeroMainViewHeroItem:SetAbandonDefaultSelect",abandonDefaultSelect)
	self._abandonDefaultSelect=abandonDefaultSelect
end

function HeroMainViewHeroItem:ShowCurUseTip(tipsConstrainWidget)
	loginfo("HeroMainViewHeroItem:ShowCurUseTip")
	self._wtCurUseTipsAnchor:SetConstrainWidget(tipsConstrainWidget)
	if self._useTipHandle and self._useTipHandle:GetUIIns() then
		self._useTipHandle:GetUIIns():SelfHitTestInvisible()
		self._wtCurUseTipsAnchor:BindTipsWidget(self._useTipHandle:GetUIIns())
	else
		local fCreateCallback=CreateCallBack(function(self,uiIns)
			uiIns:SelfHitTestInvisible()
		    self._wtCurUseTipsAnchor:BindTipsWidget(uiIns)

		end,self)
		self._useTipHandle=Facade.UIManager:AsyncShowUI(UIName2ID.HeroSelectTips, fCreateCallback)
	end
	
end

function HeroMainViewHeroItem:HideCurUseTip()
	loginfo("HeroMainViewHeroItem:HideCurUseTip")
	self._wtCurUseTipsAnchor:UnbindTipsWidget()
	Facade.UIManager:CloseUIByHandle(self._useTipHandle)
	self._useTipHandle=nil
end

function HeroMainViewHeroItem:GetCurUseTip()
	loginfo("HeroMainViewHeroItem:GetCurUseTip")
	return self._useTipHandle and self._useTipHandle:GetUIIns()
end

function HeroMainViewHeroItem:SetIsSelected(bSelected)
	if bSelected then
		self._wtSelectImg:SetVisibility(ESlateVisibility.HitTestInvisible)
		self._wtCompSelected:SelfHitTestInvisible()
		self._wtCompSelected:PlayWidgetAnim(self._wtCompSelected.selected)
	else
		self._wtSelectImg:SetVisibility(ESlateVisibility.Collapsed)
		self._wtCompSelected:StopWidgetAnim(self._wtCompSelected.selected)
		self._wtCompSelected:Collapsed()
	end
end

function HeroMainViewHeroItem:_OnMouseLeave(_, _)
	if DFHD_LUA == 1 then
		if self._fLeaveCallback then
			if not self._isHide then
				self._fLeaveCallback(self)
			end
		end
		self:PlayWidgetAnimAt(self.HoverAni, self.HoverAni:GetEndTime())
		self:PlayWidgetAnim(self.UnhoverAni)
	end
end
--endregion
--==================================================


--==================================================
--region Public API

function HeroMainViewHeroItem:InitData(data, bNotShowReddot)
	self._heroId = data.heroId
	self._insId = data.insId
	self._fCallback = data.fCallback
	self._fHoverCallback = data.fHoverCallback
	self._fEnterCallback = data.fEnterCallback
	self._fLeaveCallback = data.fLeaveCallback
	self._fRightButtonCallback = data.fRightButtonCallback
	self._itemIndex = data.index
	self._useSelectColor=data.useSelectColor
	self._bNotShowReddot = bNotShowReddot or false
	self._bShowGetMethodTips = data.bShowGetMethodTips or false
	self._bShowRecruitmentInfo = data.bShowRecruitmentInfo or false
	self:RefreshView()
	self:RegisterReddot()
end

function HeroMainViewHeroItem:RegisterReddot()
	if not self.reddot and not self._bNotShowReddot and self._heroId then
		-- local heroFileKey = string.format("NewUnlockArchive_Hero%s", self._heroId)
		-- local heroFileData = Module.ReddotTrie:GetReddotData(EReddotTrieObserverType.Hero, heroFileKey)
		local heroTakeRewardKey = string.format("CanTakeRewards_Hero%s", self._heroId)
		local heroTakeRewardData = Module.ReddotTrie:GetReddotData(EReddotTrieObserverType.Hero, heroTakeRewardKey)
		local heroGeneralRedPointKey = string.format("GeneralRedPoint_Hero%s", self._heroId)
		local heroGeneralRedPointData = Module.ReddotTrie:GetReddotData(EReddotTrieObserverType.Hero, heroGeneralRedPointKey)
		local heroCustomizationKey = string.format("NewUnlockCommerceItem_Customization%s", self._heroId)
		local heroCustomizationData = Module.ReddotTrie:GetReddotData(EReddotTrieObserverType.Hero, heroCustomizationKey)
		local validDataList = {}
		
		if heroFileData then
			table.insert(validDataList, heroFileData)
			loginfo('HeroMainViewHeroItem:RegisterReddot heroFileData=', heroFileData.reddotFlag)
		end
		if heroTakeRewardData then
			table.insert(validDataList, heroTakeRewardData)
			loginfo('HeroMainViewHeroItem:RegisterReddot heroTakeRewardData=', heroTakeRewardData.reddotFlag)
		end
		if heroCustomizationData then
			table.insert(validDataList, heroCustomizationData)
			loginfo('HeroMainViewHeroItem:RegisterReddot heroCustomizationData=', heroCustomizationData.reddotFlag)
		end
		if heroGeneralRedPointData then
			table.insert(validDataList, heroGeneralRedPointData)
			loginfo('HeroMainViewHeroItem:RegisterReddot heroGeneralRedPointData=', heroGeneralRedPointData.reddotFlag)
		end
		if #validDataList > 0 then
			local reddotDataWithStyleList = {}
			local placeOffset = DFHD_LUA == 1 and FVector2D(-1,17) or FVector2D(-1,6)
			for _, data in pairs(validDataList) do
				table.insert(reddotDataWithStyleList, {
					reddotData = data,
					reddotStyle = {zOrder = 3, placeMode = EReddotPlaceMode.Custom, placeOffset = placeOffset},
				})
			end
			self.reddot = Module.ReddotTrie:RegisterStaticReddotDot(self, reddotDataWithStyleList)
		end
	end
	if self._bNotShowReddot and self.reddot then
		loginfo('self._bNotShowReddot and self.reddot 不显示红点')
		self:UnRegisterReddot()
	end
end

function HeroMainViewHeroItem:UnRegisterReddot()
	if self.reddot then
		Module.ReddotTrie:UnRegisterStaticReddotDot(self.reddot)
		self.reddot = nil
	end
end

function HeroMainViewHeroItem:GetIndex()
	return self._itemIndex
end

function HeroMainViewHeroItem:GetHeroId()
	return self._heroId
end

function HeroMainViewHeroItem:GetInsId()
	return self._insId
end

function HeroMainViewHeroItem:InvokeClick()
	if self._fCallback then
		self._fCallback(self)
	end
end

function HeroMainViewHeroItem:OnUnSelect()
	self:SetIsSelected(false)
	-- self._wtImageMask:Visible()
	self._wtArmedIcon:SetColorAndOpacity(heroSkillColor.unselect)
	
end

function HeroMainViewHeroItem:OnSelect()
	-- self._wtImageMask:Collapsed()
	self:SetIsSelected(true)
	self._wtArmedIcon:SetColorAndOpacity(heroSkillColor.select)
end

function HeroMainViewHeroItem:OnOperated()
	self._wtOperateImage:SetVisibility(ESlateVisibility.Visible)
end

function HeroMainViewHeroItem:OnUnOperated()
	self._wtOperateImage:SetVisibility(ESlateVisibility.Collapsed)
end

function HeroMainViewHeroItem:SetSelectedExpertIcon(armId)
	self._wtArmedIcon:AsyncSetImagePath(Module.Hero.Config.ExpertIconMapping[armId])
end

function HeroMainViewHeroItem:SetTempLockIcon(Id)
	self._wtMainContentPanel:Collapsed()
	self._wtTempLockIcon:HitTestInvisible()
	self._wtTempLockIcon:AsyncSetImagePath(Module.Hero.Config.LockHeroIcon[Id])
end

function HeroMainViewHeroItem:PlayHideAnim(time)
	self:StopWidgetAnim(self.WBP_Hero_HeroItem01_in_0)
	Timer.DelayCall(time, function (self)
        self:SetRenderOpacity(1)
        self:SelfHitTestInvisible()
        self:PlayAnimationForward(self.WBP_Hero_HeroItem01_out_0, 1.0, false)
    end, self)
end

function HeroMainViewHeroItem:PlayShowAnim(time)
	self:StopWidgetAnim(self.WBP_Hero_HeroItem01_out_0)
	self:Collapsed()
	Timer.DelayCall(time, function (self)
        self:SetRenderOpacity(1)
        self:SelfHitTestInvisible()
        self:PlayAnimationForward(self.WBP_Hero_HeroItem01_in_0, 1.0, false)
    end, self)
end

function HeroMainViewHeroItem:ToggleLockMask(bLocked)
	if bLocked then
		self._wtUnlockImage:SelfHitTestInvisible()
	else
		self._wtUnlockImage:Collapsed()
	end
	
end

--网吧特权
function HeroMainViewHeroItem:SetInternetBar()
	if Server.ExperienceDataServer:GetExDataReasonByItemId(self._heroId) then
		self._wtCybercafebtn:Visible()
	else
		self._wtCybercafebtn:Collapsed()
	end
	self._wtCybercafeimg:Visible()
	--self._wtTipsAnchor:Visible()
end

function HeroMainViewHeroItem:_OnShowCybercafeTips()
    -- self._cybercafeTips =
    --     Module.CommonTips:ShowAssembledTips(
    --     {{id = UIName2ID.Assembled_CommonMessageTips_V1, data = {textContent = "正在体验网吧特权", styleRowId = "C001", fontStyleId = "Header3_28pt"}},
    --     {id = UIName2ID.Assembled_CommonMessageTips_V5, data = {textContent = "·额外经验加成<customstyle color=\"Color_Highlight02\"> 12%</>" }},
    --     {id = UIName2ID.Assembled_CommonMessageTips_V5, data = {textContent = "·免费体验<customstyle color=\"Color_Highlight02\"> 9 </>款皮肤"}},
    --     {id = UIName2ID.Assembled_CommonMessageTips_V5, data = {textContent = "·免费体验<customstyle color=\"Color_Highlight02\"> 2 </>个干员"}}},
    --     self._wtTipsAnchor
    -- )
end

function HeroMainViewHeroItem:_OnHideCybercafeTips()
    -- if self._cybercafeTips then
    --     Module.CommonTips:RemoveAssembledTips(self._cybercafeTips, self._wtTipsAnchor)
    --     self._cybercafeTips = nil
    -- end
end

--endregion
--==================================================


--==================================================
--region Private API
--endregion
--==================================================


function HeroMainViewHeroItem:_ShowHeroGetMethodTips()
	if	not self._bShowGetMethodTips then
		return
	end
	self:_HideHeroGetMethodTips()
	local showHeroId = self:GetHeroId()
	local unlockInfos =  HeroHelperTool.GetHeroUnlock() --HeroHelperTool.GetHeroUnlockInfoDataRows(showHeroId)
	local bIsUnlock = Server.HeroServer:IsOwningHero(showHeroId)
	if bIsUnlock or not unlockInfos or #unlockInfos == 0 then
		self:_HideHeroGetMethodTips()
		return
	end

	local showTipsContent  =  nil
    local lastGetType = 999
	if unlockInfos and #unlockInfos>0 then
		for index, info in ipairs(unlockInfos) do
			if info.HeroId == tonumber(showHeroId)  then
				local Parameter = info.Parameter
				if info.GetType ~= 4 and Parameter and tonumber(Parameter) then
					if info.GetType == 7 and HeroHelperTool.IsRecruitableHero(tonumber(showHeroId)) then --优先显示招募
						showTipsContent = info.Name
						break
					end
					if info.GetType < lastGetType  then
						if info.GetType == 1 then
							local Goods = Server.StoreServer:GetMallGiftDataByGoodsID(tonumber(Parameter))
							if Goods then
								lastGetType = info.GetType
								showTipsContent = info.Name
							end
						end
						if info.GetType == 3 then
							local isBool = Server.BattlePassServer:IsRewardIDExist(tonumber(showHeroId))
							if isBool then
								lastGetType = info.GetType
								showTipsContent = info.Name
							end
						end
						if info.GetType == 6 then
							local isBool = Server.ActivityServer:CheckActivityExistsByID(tonumber(Parameter))
							if isBool then
								lastGetType = info.GetType
								showTipsContent = info.Name
							end
						end
					end
				end
			end
		end
	end

	if showTipsContent then
		self._tipsHandle = Module.CommonTips:ShowCommonMessagesWithAnchor({{textContent = showTipsContent, styleRowId = "C000"}}, self._wtDFTipsAnchor)
	end
end

function HeroMainViewHeroItem:_HideHeroGetMethodTips()
	if self._tipsHandle then
        Module.CommonTips:RemoveCommonMessageWithAnchor(self._tipsHandle, self._wtDFTipsAnchor)
        self._tipsHandle = nil
    end
end

function HeroMainViewHeroItem:_SetTopBannerContent(inTopBannerType,inContent)
	if inContent then
		if self._wtCountdown.BP_SetType then
			self._wtCountdown:BP_SetType(inTopBannerType)
		end
		self._wtCountdown:Visible()
		self._wtCountdown:SetTitle(inContent)
		self:_SetCountdownAnchorIfOverFlow()
	else
		self._wtCountdown:Collapsed()
	end
end

--- 可能的文本溢出保底处理
function HeroMainViewHeroItem:_SetCountdownAnchorIfOverFlow()
    if not self._wtCountdown or not self._wtCountdown.DFTextBlock_42 then
        return
    end

    Timer.DelayCall(0.05, function()

		local maxWidth
		if self.GetCachedGeometry then
            local widgetGeometry = self:GetCachedGeometry()
            maxWidth = widgetGeometry and widgetGeometry:GetLocalSize().X
		else
			return 
        end

        local nameCompSize 
		if self._wtCountdown.GetCachedGeometry then
            local compWidgetGeometry =  self._wtCountdown:GetCachedGeometry()
            nameCompSize = compWidgetGeometry and compWidgetGeometry:GetLocalSize().X
		else
			return 
        end

		local compSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(self._wtCountdown)
		if not compSlot then
			return	
		end

		local anchors = compSlot:GetAnchors()

		if nameCompSize > maxWidth then
			anchors.Minimum = self._overflowAnchorMin
			anchors.Maximum = self._overflowAnchorMax
			compSlot:SetAnchors(anchors)
			compSlot:SetOffsets(self._overflowMargin)
			if self._wtCountdown.DFTextBlock_42.SetTextOverflowAutoEllipse then
				self._wtCountdown.DFTextBlock_42:SetTextOverflowAutoEllipse(ETextOverflowPolicy.Ellipsis)
			end
		else
			if self._normalAnchorMin and self._normalAnchorMax and self._normalMargin then
				anchors.Minimum = self._normalAnchorMin
				anchors.Maximum = self._normalAnchorMax
				compSlot:SetAnchors(anchors)
				compSlot:SetOffsets(self._normalMargin)
			end
		
			if self._wtCountdown.DFTextBlock_42.SetTextOverflowAutoEllipse then
				self._wtCountdown.DFTextBlock_42:SetTextOverflowAutoEllipse(ETextOverflowPolicy.Default)
			end
		end
    end)
end

function HeroMainViewHeroItem:_AddAllEvent()
	self:AddLuaEvent(Server.HeroServer.Events.evtHeroUnlockNtf, self.RefreshView, self)

end

function HeroMainViewHeroItem:_RemoveAllEvent()
	self:RemoveLuaEvent(Server.HeroServer.Events.evtHeroUnlockNtf)
end

return HeroMainViewHeroItem