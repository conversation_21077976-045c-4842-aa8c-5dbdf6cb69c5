----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRange)
----- LOG FUNCTION AUTO GENERATE END -----------



local RangeSaveLoadLogic = {}

local function fBuildKey(key)
    local bSOL = Server.IrisSafeHouseServer.bRangeSOLMode
    return string.format("Range_%s_%s", bSOL and "SOL" or "MP", key)
end

-- function RangeSaveLoadLogic.Save(key, value)
--     local finalKey = fBuildKey(key)
--     Facade.ConfigManager:SetUserString(key, value)
-- end

-- function RangeSaveLoadLogic.Load(key, default)
--     local finalKey = fBuildKey(key)
--     return Facade.ConfigManager:GetUserString(key, default)
-- end

function RangeSaveLoadLogic.SaveWeaponAmmo(weaponId, ammoId)
    local finalKey = fBuildKey(tostring(weaponId))
    Facade.ConfigManager:SetUserInt(finalKey, ammoId)
end

function RangeSaveLoadLogic.SaveRangeTargetDistance(index, distance)
    local finalKey = fBuildKey(string.format("RT%dDISTANCE", index))
    Facade.ConfigManager:SetUserInt(finalKey, distance)
end

function RangeSaveLoadLogic.SaveRangeTargetHelmet(index, helmetId)
    local finalKey = fBuildKey(string.format("RT%dHELMET", index))
    Facade.ConfigManager:SetUserInt(finalKey, helmetId)
end

function RangeSaveLoadLogic.SaveRangeTargetArmor(index, armorId)
    local finalKey = fBuildKey(string.format("RT%dARMOR", index))
    Facade.ConfigManager:SetUserInt(finalKey, armorId)
end

function RangeSaveLoadLogic.SaveRangeTargetHP(index, hp)
    local finalKey = fBuildKey(string.format("RT%dHP", index))
    Facade.ConfigManager:SetUserInt(finalKey, hp)
end

function RangeSaveLoadLogic.SaveShowDMGHint(bShow)
    local finalKey = fBuildKey("DMGHint")
    Facade.ConfigManager:SetUserBoolean(finalKey, bShow)
end

function RangeSaveLoadLogic.LoadWeaponAmmo(weaponId, defaultAmmoId)
    local finalKey = fBuildKey(tostring(weaponId))
    local ammoId = Facade.ConfigManager:GetUserInt(finalKey, defaultAmmoId)
    return ammoId
end

function RangeSaveLoadLogic.LoadRangeTargetDistance(index, distance)
    local finalKey = fBuildKey(string.format("RT%dDISTANCE", index))
    return Facade.ConfigManager:GetUserInt(finalKey, distance)
end

function RangeSaveLoadLogic.LoadRangeTargetHelmet(index, defaultHelmetId)
    local finalKey = fBuildKey(string.format("RT%dHELMET", index))
    return Facade.ConfigManager:GetUserInt(finalKey, defaultHelmetId)
end

function RangeSaveLoadLogic.LoadRangeTargetArmor(index, defaultArmorId)
    local finalKey = fBuildKey(string.format("RT%dARMOR", index))
    return Facade.ConfigManager:GetUserInt(finalKey, defaultArmorId)
end

function RangeSaveLoadLogic.LoadRangeTargetHP(index, defaultHP)
    local finalKey = fBuildKey(string.format("RT%dHP", index))
    return Facade.ConfigManager:GetUserInt(finalKey, defaultHP)
end

function RangeSaveLoadLogic.LoadShowDMGHint(bDefaultShow)
    local finalKey = fBuildKey("DMGHint")
    return Facade.ConfigManager:GetUserBoolean(finalKey, bDefaultShow)
end

return RangeSaveLoadLogic