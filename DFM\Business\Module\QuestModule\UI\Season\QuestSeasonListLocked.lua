----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMQuest)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class QuestSeasonListLocked : LuaUIBaseView

local QuestSeasonListLocked = ui("QuestSeasonListLocked")

function QuestSeasonListLocked:Ctor()
    self._wtTitleText = self:Wnd("TextBlock_1", UITextBlock)
    self._wtDescText = self:Wnd("TextBlock", UITextBlock)

    self._wtConditionList = self:Wnd("DFVerticalBox_85", UIWidgetBase)

    self._stageInfo = nil
    self._lineInfo = nil
end

function QuestSeasonListLocked:OnInitExtraData(lineInfo, stageID)
    self._lineInfo = lineInfo
    self._stageInfo = lineInfo:GetStageInfoByID(stageID)
end

function QuestSeasonListLocked:OnShowBegin()

    self._wtTitleText:SetText(self._stageInfo.name)    
    self._wtDescText:SetText(self._stageInfo.desc)

    Facade.UIManager:RemoveSubUIByParent(self, self._wtConditionList)

    local preStage = self._lineInfo:GetPrevStageInfoByID(self._stageInfo.stageID)


    local uiIns, instanceID = Facade.UIManager:AddSubUI(self,UIName2ID.QuestSeasonConditionItem,self._wtConditionList,nil, Module.Quest.Config.Loc.QuestSeasonStageConditionMission)

    local condWidget = getfromweak(uiIns)
    if condWidget then
        local mainGroup = self._lineInfo:GetMainGroupIDByStageID(preStage.stageID)
        local compNum = self._lineInfo:GetCompletedQuestNum(mainGroup)
        local total = self._lineInfo:GetAllQuestIdsByGroupID(mainGroup)
        condWidget:SetProgress( compNum , #total)  
        condWidget:SetHideDecoLine(false)
    end

    local str = string.format(Module.Quest.Config.Loc.QuestSeasonStageConditionStar, self._stageInfo.unlockNeedStar)

    uiIns, instanceID = Facade.UIManager:AddSubUI(self,UIName2ID.QuestSeasonConditionItem,self._wtConditionList,nil, str)

    condWidget = getfromweak(uiIns)
    if condWidget then
        condWidget:SetProgress( self._lineInfo:CalGainStarByStageID(preStage.stageID) , self._stageInfo.unlockNeedStar)
        condWidget:SetHideDecoLine(true)            
    end
end

return QuestSeasonListLocked