----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMHUD)
----- LOG FUNCTION AUTO GENERATE END -----------



local HudConfig = require "DFM.Business.Module.HUDModule.HUDConfig"
local WeaponHoldBreathView = hud("WeaponHoldBreathView")
local UWeaponHudController = import "WeaponHudController"
local UBreathInfo = import "BreathInfo"
local EBreathState = import "EBreathState"
local GameHUDState = import "GameHUDSate"
local ClientBaseSetting = import "ClientBaseSetting"
local UHUDStateManager = import "HUDStateManager"

function CanShowProgress()
    -- return false
    return IsInEditor()
    -- body
end

function WeaponHoldBreathView:Ctor()
    logerror("WeaponHoldBreathView:Ctor")
    self.BreathInfo = nil
    self.AnimCanvasPanel = self:Wnd("CanvasPanel_0", UCanvasPanel)
    self.HoldBreathBtn = self:Wnd("DragBtnHoldBreath", UIButton)
    --self.Progress = self:Wnd("DFProgressBar_22", UIProgressBar)
    self.Progress = self:Wnd("DFRadialImage_112", URadialImage)
    self.ContourImg = self:Wnd("Image_Contour", UIWidgetBase)
    self.CloseImg = self:Wnd("Image_Close", UIWidgetBase)
    self.CloseImg:SetVisibility(ESlateVisibility.Collapsed)
    self.SelectedCircle = self:Wnd("CommonView", UIWidgetBase):Wnd("Image_PressedCircle", UIImage)
    self.RootPanel = self:Wnd("CanvasPanel_7", UIWidgetBase)
    --self.ProgressWidgets = {self.Progress, self:Wnd("Image_25", UIWidgetBase), self:Wnd("Image", UIWidgetBase), self:Wnd("Image_FX", UIWidgetBase)}

    self.HoldBreathBtn:Event("OnClicked",self.OnHodBreathButtonClicked, self)
    
    self.CacheProgressType = -1
    --self.CacheShouldShowContour = false
    
    self.KINDA_SMALL_NUMBER = 1e-4
    self.SMALL_NUMBER = 1e-8
    self.TICK_RATE = 0.033

    if DFHD_LUA ~= 1 then
        if UHUDStateManager ~= nil then
            if self.OnHudStateAddedHandler == nil then
                local fOnHudStateChanged = CreateCallBack(self.OnHudStateAdded, self)
                self.OnHudStateAddedHandler = UHUDStateManager.Get(self).OnGameHUDStateAdded:Add(fOnHudStateChanged)
            end
            if self.OnHudStateRemovedHandler == nil then
                local fOnHudStateChanged = CreateCallBack(self.OnHudStateRemoved, self)
                self.OnHudStateRemovedHandler = UHUDStateManager.Get(self).OnGameHUDStateRemoved:Add(fOnHudStateChanged)
            end
        end

        if self.OnAimingTriggeredHandle == nil then
            local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
            if localCtrl ~= nil then
                local dfmCharacter = localCtrl:GetPawn()
                if dfmCharacter ~= nil and dfmCharacter.OnAimingTriggered ~= nil then
                    self.OnAimingTriggeredHandle = dfmCharacter.OnAimingTriggered:Add(CreateCPlusCallBack(self.OnWeaponAimingTriggered, self))
                end
            end
        end
    end
    
    self._invisibleStates = {
        GameHUDState.GHS_Operating3DUI,
        GameHUDState.GHS_Burden,
        GameHUDState.GHS_Settlement,
        GameHUDState.GHS_PrepareTime,
        GameHUDState.GHS_Dying,
        GameHUDState.GHS_RescueSomeOne,
        GameHUDState.GHS_Dead,
        GameHUDState.GHS_VehicleForbidShoot,
        GameHUDState.GHS_VehicleMode_Skill,
        GameHUDState.GHS_CutScene,
        GameHUDState.GHS_Redeploy,
        GameHUDState.GHS_UseHeavyMachineGun,
        GameHUDState.GHS_Reloading,
        GameHUDState.GHS_UseTelescope,
        GameHUDState.GHS_Monitor,
        GameHUDState.GHS_PVEQuestPanelOnly,
        GameHUDState.GHS_SOLQuestOperation,
        GameHUDState.GHS_InteractOnly,
        GameHUDState.GHS_Assassinate,
        GameHUDState.GHS_PCControlMode,
        GameHUDState.GHS_ForbidFire,
        GameHUDState.GHS_BattleScoreDetails,
        GameHUDState.GHS_LiveSpectate,
        GameHUDState.GHS_GuidedCruiseMissile,
        GameHUDState.GHS_VehicleWeapon,
        GameHUDState.GHS_DriveVehicle,
        GameHUDState.GHS_RideVehicle,
        GameHUDState.GHS_GlobalHideAllHUD
    }
    self._visibleStates = {GameHUDState.GHS_Aiming}
    self:InitHudStates()

    self.neverShow = false
    self:SetVisibility(ESlateVisibility.Collapsed)
end

function WeaponHoldBreathView:TryShowRootPanel()
    --local hudStateManager = UE.HUDStateManager.Get(GetWorld())
    local hudStateManager = UHUDStateManager.Get(self)
    if isvalid(hudStateManager) then
        for i, value in pairs(self._invisibleStates) do
            if hudStateManager:HasState(value) then
                return
            end
        end
    end
    if isinvalid(self.WeaponHudController) then
        self.WeaponHudController = UWeaponHudController.Get(self)
    end
    if isvalid(self.WeaponHudController) then
        local CurWeaponInfo = self.WeaponHudController:GetCurWeaponInfo()
        if isvalid(CurWeaponInfo) then
            local CurWeapon = CurWeaponInfo.WeaponObject
            if isvalid(CurWeapon) and CurWeapon:IsSkillWeapon() then
                return
            end
        end
    end
    self.RootPanel:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
end

function WeaponHoldBreathView:OnWeaponAimingTriggered(bAimingOn)
    if bAimingOn == true then
        if math.abs(self.AnimCanvasPanel:GetRenderOpacity()) < 1e-4 then
            --self.RootPanel:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
            self:TryShowRootPanel()
            --self:StopAllAnimations()
            self:StopAnimation(self.WBP_ControllerButtun_HoldBreath_in)
            self:StopAnimation(self.WBP_ControllerButtun_HoldBreath_out)
            self:PlayAnimation(self.WBP_ControllerButtun_HoldBreath_in, 0, 1, EUMGSequencePlayMode.Forward, self.Anim_in_rate, false)
        end
    end
end

function WeaponHoldBreathView:InitHudStates()
    for _, value in pairs(self._invisibleStates) do
	    self:AddStateToInVisibleGameHudState(value)
    end
    for _, value in pairs(self._visibleStates) do
	    self:AddStateToVisibleGameHudState(value)
    end
end

function WeaponHoldBreathView:OnHudStateAdded(State)
    if not isvalid(self) then
        return
    end

    if State == GameHUDState.GHS_GlobalHideAllHUD 
            or State == GameHUDState.GHS_CutScene 
            or State == GameHUDState.GHS_Settlement
            or State == GameHUDState.GHS_Dying
            or State == GameHUDState.GHS_Dead
            or State == GameHUDState.GHS_EmoteAction
    then
        self:Super().Hide(self)
        if State == GameHUDState.GHS_Settlement then
            self.neverShow = true
        end
        return
    end

    if State == GameHUDState.GHS_Aiming then
        self:StopAllAnimations()
        self:Super().Show(self)
        --self.RootPanel:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self:TryShowRootPanel()
        self:PlayAnimation(self.WBP_ControllerButtun_HoldBreath_in, 0, 1, EUMGSequencePlayMode.Forward, self.Anim_in_rate, false)
        if self.BreathInfo == nil then
            self:OnShow()
        end
    end

    if State == GameHUDState.GHS_GuidedCruiseMissile then
        self.RootPanel:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function WeaponHoldBreathView:OnHudStateRemoved(State)
    if not isvalid(self) then
        return
    end

    if State == GameHUDState.GHS_GlobalHideAllHUD then
        self:Super().Show(self)
    end
    
    if State == GameHUDState.GHS_Aiming and self.StopAllAnimations ~= nil then
        self:StopAllAnimations()
        self:Super().Hide(self)
        self.isAnimOutFinished = true
    end
end

function WeaponHoldBreathView:OnOpen()

    -- button
    if IsHD() then
        self.HoldBreathBtn:SetVisibility(ESlateVisibility.Collapsed)
    else
        self.HoldBreathBtn:SetVisibility(ESlateVisibility.Visible)
    end

    -- self:PCAdaptation()
    -- self.SetBtnType(0)
    -- local WeaponHudController = UWeaponHudController.Get(self)

    -- -- init view
    -- for index = 1, #self.callbacks do
    --     -- body
    --     self.callbacks[index][3](self)
    -- end
end

function WeaponHoldBreathView:OnDestroy()
    if DFHD_LUA ~= 1 then
        if UHUDStateManager ~= nil then
            if self.OnHudStateAddedHandler ~= nil then
                UHUDStateManager.Get(self.world).OnGameHUDStateAdded:Remove(self.OnHudStateAddedHandler)
                self.OnHudStateAddedHandler = nil
            end
            if self.OnHudStateRemovedHandler ~= nil then
                UHUDStateManager.Get(self.world).OnGameHUDStateRemoved:Remove(self.OnHudStateRemovedHandler)
                self.OnHudStateRemovedHandler = nil
            end
        end

        if self.OnAimingTriggeredHandle ~= nil then
            local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
            if localCtrl ~= nil then
                local dfmCharacter = localCtrl:GetPawn()
                if dfmCharacter ~= nil then
                    dfmCharacter.OnAimingTriggered:Remove(self.OnAimingTriggeredHandle)
                    self.OnAimingTriggeredHandle = nil
                end
            end
        end
        
    end
end

function WeaponHoldBreathView:OnShow()
    if self.neverShow == true then
        self:Super().Hide(self)
        return
    end
    
    self.WeaponHudController = UWeaponHudController.Get(self)
    self.BreathInfo = self.WeaponHudController:GetBreathInfo()
    if not isvalid(self.BreathInfo) then
        self.RootPanel:SetVisibility(ESlateVisibility.Hidden)
        return
    end

    if self.BreathInfo.bHoldBreathEnable then
        --self.RootPanel:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self:TryShowRootPanel()
    else
        self.RootPanel:SetVisibility(ESlateVisibility.Hidden)
    end

    self:_RemoveCallbacks()
    -- self.callbacks = {
    --     -- {nil, self.viewModel.OnDelegateActiveWeaponChanged, self.OnActiveWeaponChanged},
    --     {nil, self.BreathInfo.BreathStateChangedCB, self.OnBreathStateChanged},
    -- }
    self:_InitCallbacks()
    if DFHD_LUA ~= 1 then
        if self.OnAimingTriggeredHandle == nil then
            local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
            if localCtrl ~= nil then
                local dfmCharacter = localCtrl:GetPawn()
                if dfmCharacter ~= nil and dfmCharacter.OnAimingTriggered ~= nil then
                    self.OnAimingTriggeredHandle = dfmCharacter.OnAimingTriggered:Add(CreateCPlusCallBack(self.OnWeaponAimingTriggered, self))
                end
            end
        end
    end

    self:UpdateView()

    if self.BreathInfo.CurBreathValue < self.BreathInfo.MaxBreathValue then
        -- LuaTickController:Get():RegisterTick(self)
        Timer.FastAddObjTimer(self, self.TICK_RATE, 0, self.UpdateView)
    end

    self:StopAllAnimations()
    self:PlayAnimation(self.WBP_ControllerButtun_HoldBreath_in, 0, 1, EUMGSequencePlayMode.Forward, self.Anim_in_rate, false)
    self.isAnimOutFinished = nil
end

--重载父类 隐藏时不直接隐藏 先播个淡出动画结束再隐藏
function WeaponHoldBreathView:Hide()
    if isinvalid(self.WeaponHudController) then
        print("WeaponHoldBreathView:Hide, WeaponHudController is invalid")
        self:Super().Hide(self)
        return
    end
    local CurBreathInfo = self.WeaponHudController:GetBreathInfo()
    if not isvalid(CurBreathInfo) then
        print("WeaponHoldBreathView:Hide, breath info is invalid")
        self:Super().Hide(self)
        return
    end

    local Percent = self:GetPercent(CurBreathInfo.CurBreathValue, CurBreathInfo.MaxBreathValue)
    if not CurBreathInfo.bZooming and Percent > self.HideBreathPercent then
        
        -- LuaTickController:Get():RemoveTick(self)
        Timer.FastRemoveObjTimer(self)
        
        self:StopAnimation(self.WBP_ControllerButtun_HoldBreath_Exhaust)
        self.hasPlayExhaustAnim = false
        
        self.isAnimOutFinished = false
        self:PlayAnimation(self.WBP_ControllerButtun_HoldBreath_out, 0, 1, EUMGSequencePlayMode.Forward, self.Anim_out_rate, false)
        
    end
end

--function WeaponHoldBreathView:OnWidgetAnimationFinished(anim)
--    if anim == self.WBP_ControllerButtun_HoldBreath_out then
--        self:SetVisibility(ESlateVisibility.Collapsed)
--        self:Super().Hide(self)
--    end
--end

--淡出动画结束
function WeaponHoldBreathView:OnAnimFinished(anim)
    if self:IsValid() then
        if anim == self.WBP_ControllerButtun_HoldBreath_out and not self.isAnimOutFinished then
            --self:Super().Hide(self)
            self.RootPanel:SetVisibility(ESlateVisibility.Collapsed)
            self.isAnimOutFinished = true
        end
    end
end

function WeaponHoldBreathView:OnHide()
    -- LuaTickController:Get():RemoveTick(self)
    -- self:_RemoveCallbacks()
end

function WeaponHoldBreathView:OnClose()
end

function WeaponHoldBreathView:_InitCallbacks()
    -- for i = 1, #self.callbacks do
    --     local callback = self.callbacks[i]
    --     callback[1] = callback[2]:Add(CreateCallBack(callback[3], self))
    -- end

    if not isvalid(self.handle) then
        if isvalid(self.BreathInfo) then
            self.handle = self.BreathInfo.BreathStateChangedCB:Add(CreateCallBack(self.OnBreathStateChanged, self))
        end
    end
end
function WeaponHoldBreathView:_RemoveCallbacks()
    -- if self.callbacks ~= nil then
    --     for i = 1, #self.callbacks do
    --         local callback = self.callbacks[i]
    --         if isvalid(callback[2]) and isvalid(self.BreathInfo) then
    --             callback[2]:Remove(callback[1])
    --         end
    --     end
    -- end

    if isvalid(self.handle) then
        if isvalid(self.BreathInfo) then
            self.BreathInfo.BreathStateChangedCB:Remove(self.handle)
            self.handle = nil
        end
    end
end

function WeaponHoldBreathView:GetPercent(CurBreathValue, MaxBreathValue)
    local Percent = 0
    if MaxBreathValue > self.KINDA_SMALL_NUMBER
            and CurBreathValue > self.SMALL_NUMBER
            and (MaxBreathValue > CurBreathValue
            or math.abs(MaxBreathValue - CurBreathValue) < self.KINDA_SMALL_NUMBER) then
        Percent = CurBreathValue / MaxBreathValue
    end
    Percent = math.clamp(Percent, 0, 1)
    return Percent
end

function WeaponHoldBreathView:Update(dt)
    self:UpdateView()
end

function WeaponHoldBreathView:UpdateView()
    -- logerror("WeaponHoldBreathView:UpdateView()")

    if not isvalid(self) then
        return
    end

    local CurBreathInfo = self.WeaponHudController:GetBreathInfo()
    if CurBreathInfo == nil or not isvalid(CurBreathInfo) then
        print("breath info is invalid")
        return
    end

    -- proress
    
    local Percent = self:GetPercent(CurBreathInfo.CurBreathValue, CurBreathInfo.MaxBreathValue)
    if self.Progress ~= nil and isvalid(self.Progress) then
        self.Progress:SetPercent(Percent)
    end
    
    --self:SetProgressBarVisible(Percent < 1)
    --local Style = 0
    --if Percent < 0.2 and CanShowProgress() then
    --    Style = 1
    --end
    --self:SetProgressType(Style)
    -- body
    
    local Type = 0
    local LowBreathPercent = 0.3
    if CurBreathInfo.BreathState == EBreathState.HoldBreath then
        if Percent < LowBreathPercent then
            Type = 1
            if self.hasPlayExhaustAnim == nil or self.hasPlayExhaustAnim == false then
                self.hasPlayExhaustAnim = true
                self:PlayAnimation(self.WBP_ControllerButtun_HoldBreath_Exhaust, 0, 4, EUMGSequencePlayMode.Forward, 1, true)
            end
        else
            Type = 0
        end
        self.CloseImg:SetVisibility(ESlateVisibility.Visible)
        self.CloseImg:SetRenderOpacity(1)
        self.SelectedCircle:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        if Percent < LowBreathPercent then
            Type = 3
        else
            self:StopAnimation(self.WBP_ControllerButtun_HoldBreath_Exhaust)
            self.hasPlayExhaustAnim = false
            if CurBreathInfo.BreathState ~= EBreathState.UnHoldBreath then
                Type = 0
            else
                Type = 2
            end
        end
        self.CloseImg:SetVisibility(ESlateVisibility.Collapsed)
        self.SelectedCircle:SetVisibility(ESlateVisibility.Collapsed)
    end
    
    self:TrySetStyle(Type)

    --local ShouldShowContour = false
    --local ShowContourThreshold = 0.05
    --if Percent < ShowContourThreshold then
    --    ShouldShowContour = true
    --else
    --    ShouldShowContour = false
    --end
    --if ShouldShowContour ~= self.CacheShouldShowContour then
    --    self.CacheShouldShowContour = ShouldShowContour
    --    if ShouldShowContour then
    --        --self.ContourImg:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    --        self:PlayAnimation(self.WBP_ControllerButtun_HoldBreath_Disable, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    --    else
    --        --self.ContourImg:SetVisibility(ESlateVisibility.Collapsed)
    --    end
    --end

    -- self.hasHudStateAiming = false
    -- local hudStateManager = UE.HUDStateManager.Get(GetWorld())
    -- if hudStateManager and hudStateManager:HasState(UE.GameHUDSate.GHS_Aiming) then
    --     self.hasHudStateAiming = true
    -- end

    --print("[junjie] IsCurrentAiming", self.WeaponHudController:IsCurrentAiming(), "bZooming", CurBreathInfo.bZooming, "Percent", Percent, "hasHudStateAiming", self.hasHudStateAiming)

    -- 隐藏ui
    if not CurBreathInfo.bZooming and Percent > self.HideBreathPercent then
        -- LuaTickController:Get():RemoveTick(self)
        Timer.FastRemoveObjTimer(self)
        
        self:StopAnimation(self.WBP_ControllerButtun_HoldBreath_Exhaust)
        self.hasPlayExhaustAnim = false

        self.isAnimOutFinished = false
        self:PlayAnimation(self.WBP_ControllerButtun_HoldBreath_out, 0, 1, EUMGSequencePlayMode.Forward, self.Anim_out_rate, false)
    -- 显示Ui
    -- elseif CurBreathInfo.bHoldBreathEnable then
    --     --self.RootPanel:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    --     self:TryShowRootPanel()
    end

    if CurBreathInfo.BreathState ~= EBreathState.HoldBreath and Percent > 0.999 then
        self:StopAnimation(self.WBP_ControllerButtun_HoldBreath_Exhaust)
        self.hasPlayExhaustAnim = false
        -- LuaTickController:Get():RemoveTick(self)
        Timer.FastRemoveObjTimer(self)
    end
    
end

function WeaponHoldBreathView:TrySetStyle(Type)
    if self.CacheProgressType ~= Type then
        self.CacheProgressType = Type
        self:SetProgressType(Type)
    end 
end

function WeaponHoldBreathView:OnBreathStateChanged(obj, PreBreathState, CurBreathState)
    print("WeaponHoldBreathView: Pre: ", PreBreathState, "Cur: ", CurBreathState)
    if not isvalid(self) then
        print("called when self is invalid")
        return
    end

    self:UpdateView()
    if self.BreathInfo.CurBreathValue < self.BreathInfo.MaxBreathValue or CurBreathState == EBreathState.HoldBreath then
        -- LuaTickController:Get():RegisterTick(self)
        Timer.FastAddObjTimer(self, self.TICK_RATE, 0, self.UpdateView)
    else
        -- LuaTickController:Get():RemoveTick(self)
        Timer.FastRemoveObjTimer(self)
    end
    if CurBreathState == EBreathState.UnHoldBreath and self.Progress:IsVisible() then
        self:PlayAnimation(self.WBP_ControllerButtun_HoldBreath_Exhaust, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    end
end

function WeaponHoldBreathView:OnHodBreathButtonClicked()
    if not isvalid(self.BreathInfo) then
        print("breath info is invalid")
        return
    end

    if self.BreathInfo.BreathState == EBreathState.UnHoldBreath then
        self:PlayAnimation(self.WBP_ControllerButtun_HoldBreath_Disable, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    end
    
    self.BreathInfo:ToggleHoldBreath()
    
end

function WeaponHoldBreathView:SetProgressBarVisible(Visible)
    -- Editor下才显示进度条
    local bCanShowProgress = CanShowProgress()
    --for _, Widget in pairs(self.ProgressWidgets) do
    --    -- body
    --    if Visible and bCanShowProgress then
    --        Widget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    --    else
    --        Widget:SetVisibility(ESlateVisibility.Collapsed)
    --    end
    --end
end

return WeaponHoldBreathView
