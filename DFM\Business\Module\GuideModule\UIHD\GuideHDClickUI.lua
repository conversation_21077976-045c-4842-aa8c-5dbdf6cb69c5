----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGuide)
----- LOG FUNCTION AUTO GENERATE END -----------


local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local UGPInputDelegates = import "GPInputDelegates"
local EGPInputType = import "EGPInputType"


local GuideConfig = require "DFM.Business.Module.GuideModule.GuideConfig"
local UGPInputHelper = import("GPInputHelper")
local GuideLogic = require "DFM.Business.Module.GuideModule.GuideLogic"

---@class GuideHDClickUI : LuaUIBaseView
local GuideHDClickUI = ui("GuideHDClickUI")

local function log(...)
    loginfo("[GuideHDClickUI]", ...)
end

function GuideHDClickUI:Ctor()
    self._wtNoSwallowArea = self:Wnd("_wtNoSwallowArea", UIWidgetBase)
    ---@type GuideHDClickBtnUI
    self._wtClickItem = self:Wnd("WBP_Guide_Tips_PC", UIWidgetBase)
    self._wtClickItem1 = self:Wnd("WBP_Guide_Tips_PC_1", UIWidgetBase)

    self._btnList = {}
    self._btnList[1] = self._wtClickItem
    self._btnList[2] = self._wtClickItem1
    self:Reset()
end

--==================================================
--region Life function
function GuideHDClickUI:Reset()
    self._showData = nil
    self._bSelfActive = false
    self._callbackOnEnd = nil
    self._bMaskLoadFinished = false
    self._bHaveBg = false
    local idx = 0
    while idx < 5 do
        self._wtNoSwallowArea:SetMaskPosSizeXYZW(idx, 0, 0, 0, 0)
        self._wtNoSwallowArea:EnableMaskClickClip(idx, false)
        idx = idx + 1
    end
    self._wtClickItem:SetRenderOpacity(0)
    self._wtClickItem1:SetRenderOpacity(0)
    self._wtNoSwallowArea:SetRenderOpacity(0)

    if self._OnNotifyInputTypeChangedHandle then
        UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._OnNotifyInputTypeChangedHandle)
        self._OnNotifyInputTypeChangedHandle = nil
    end
end

function GuideHDClickUI:RecycleSelf()
    self:Hide()
    if self._bSelfActive then
        Module.Guide.Field:ReturnCacheSubUI(UIName2ID.GuideHDClickUI, self)
        self._bSelfActive = false
    end
end

function GuideHDClickUI:OnOpen()
end

function GuideHDClickUI:OnClose()
    self._bClose = true
    self:_RemoveTickTimer()

    Module.Guide:SetGuideInputGate("GuideHDClickUI", false)
    UGPInputHelper.WantInputMode_UIOnly(GetGameInstance(), "GuideHDClickUI", false, false)
end

---@param showData GuideHDClickUIShowData
function GuideHDClickUI:SetData(showData)
    if not showData then return end
    self._showData = showData
    self._callbackOnEnd = showData.callbackOnEnd
end

function GuideHDClickUI:OnShowBegin()
    self._bSelfActive = true
    self:_UpdateShow()
    self:_AddTickTimer()

    --local inputMonitor = Facade.UIManager:GetInputMonitor()
    --inputMonitor:SetActionsPriorityGate(EDisplayInputActionPriority.UI_Guide, true)
    Module.Guide:SetGuideInputGate("GuideHDClickUI", true)
    UGPInputHelper.WantInputMode_UIOnly(GetGameInstance(), "GuideHDClickUI", true, false)

    self:_PlayAudio()


    if IsHD() then
        if not self._OnNotifyInputTypeChangedHandle then
            self._OnNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(
                CreateCPlusCallBack(self._handleInputTypeChanged, self))
        end
        self:_handleInputTypeChanged(WidgetUtil.GetCurrentInputType())
    end

    -- FG导屏蔽光标
    -- WidgetUtil.SetFreeAnalogCursorIsBlocked(self, true)
end

function GuideHDClickUI:_handleInputTypeChanged(inputType)
    if not IsHD() then
        return
    end

    if self._showData then
        local clickCfg = Module.Guide.Config.TableGuideClickConfig[self._showData.clickID]
        if clickCfg then
            self._showData.tipsText = GuideLogic.GetRealClickTipsText(clickCfg)
        end
    end

    self:_UpdateShow()


    if WidgetUtil.IsGamepad() then
        -- NOTICE: 手柄不再屏蔽低层级的displayinputAction， 兼容性?
        Module.Guide:SetGuideInputGate("GuideHDClickUI", false)
        -- 暂时一个
        if not hasdestroy(self._wtClickItem) then
            Module.Guide.Config.EGuideEvent.evtGuideNavChanged:Invoke("add", "GuideHDClickUI", {
                self._wtClickItem._wtButton
            })
        end
    else
        Module.Guide.Config.EGuideEvent.evtGuideNavChanged:Invoke("remove", "GuideHDClickUI")
        Module.Guide:SetGuideInputGate("GuideHDClickUI", true)
    end
end

function GuideHDClickUI:OnHidebegin()
    if self._OnNotifyInputTypeChangedHandle then
        UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._OnNotifyInputTypeChangedHandle)
        self._OnNotifyInputTypeChangedHandle = nil
    end

    WidgetUtil.SetFreeAnalogCursorIsBlocked(self, false)
end

function GuideHDClickUI:OnHide()
    self:_PauseTickTimer()
    self:RemoveAllLuaEvent()

    -- 不需要，强点击会有遮罩，不让操作
    -- Module.Guide.Config.EGuideEvent.evtGuideNavChanged:Invoke("remove", "GuideHDClickUI")
    Module.Guide:SetGuideInputGate("GuideHDClickUI", false)
    UGPInputHelper.WantInputMode_UIOnly(GetGameInstance(), "GuideHDClickUI", false, false)
end

function GuideHDClickUI:OnInitExtraData(showData)
    self:SetData(showData)
end

function GuideHDClickUI:_UpdateShow()
    if not self._showData then return end

    local bShowClickText = self._showData.tipsText and self._showData.tipsText ~= ""

    if WidgetUtil.IsGamepad() then
        -- 手柄不遮罩，强制聚焦即可
        self._wtNoSwallowArea:SetVisibility(ESlateVisibility.Collapsed)
    else
        self._wtNoSwallowArea:SetVisibility(ESlateVisibility.Visible)
    end

    self._wtClickItem:TextVisible(bShowClickText)
    self._wtClickItem:SetTipsText(self._showData.tipsText)
    self._wtClickItem1:TextVisible(bShowClickText)
    self._wtClickItem1:SetTipsText(self._showData.tipsText)

    -- 11//10 == 1 then
    -- if self._showData.tipsTextPolicy == 11 then
    --     self._wtClickItem:IconVisible(ESlateVisibility.Collapsed)
    --     self._wtClickItem1:IconVisible(ESlateVisibility.Collapsed)
    -- end
    local bHDNoDefaultIcon = (self._showData.options & GuideConfig.EGuideClickConfigOption.HDNoDefaultIcon) ~= 0
    if
        bHDNoDefaultIcon or
        WidgetUtil.IsGamepad() -- 手柄不显示鼠标Icon, 采用富文本
    then
        self._wtClickItem:IconVisible(ESlateVisibility.Collapsed)
        self._wtClickItem1:IconVisible(ESlateVisibility.Collapsed)
    else
        self._wtClickItem:IconVisible(ESlateVisibility.HitTestInvisible)
        self._wtClickItem1:IconVisible(ESlateVisibility.HitTestInvisible)
    end



    local maskBg
    if self._showData.maskBg and self._showData.maskBg ~= "" then
        maskBg = self._showData.maskBg
        self._bHaveBg = true
    else
        maskBg = GuideConfig.DefaultMaskTexturePath
        self._bHaveBg = false
    end
    local _afterLoadTexture = function(imageAsset)
        self._wtNoSwallowArea:SetBgImage(imageAsset, true)
        self:_InitClickItemPosAndSize()
    end
    GuideLogic.StaticAsyncSetImagePath(maskBg, _afterLoadTexture)
end

function GuideHDClickUI:_InitClickItemPosAndSize()
    if not self._showData then return end

    local idxOffset = 2 -- 前两个用来处理上下露出来topbar
    local clipDataList = self._showData.clipDataList
    local offset = 0
    for idx, data in ipairs(clipDataList) do
        -- 先只处理一个
        if idx > 2 then
            return
        end
        local curBtn = self._btnList[idx]

        local _afterLoadTexture = function(imageAsset)
            if not self._bSelfActive then
                return
            end
            self._bMaskLoadFinished = true

            local offsetLeft, offsetRight, offsetTop, offsetBottom = 0, 0, 0, 0
            if data.uiOffset then
                offsetTop = data.uiOffset[1] or 0
                offsetBottom = data.uiOffset[2] or 0
                offsetLeft = data.uiOffset[3] or 0
                offsetRight = data.uiOffset[4] or 0
            end
            local triangleOffsetX, triangleOffsetY = 0, 0
            if data.triangleUIOffset then
                triangleOffsetX = data.triangleUIOffset[1] or 0
                triangleOffsetY = data.triangleUIOffset[2] or 0
            end

            -- 实际目标ui的全局尺寸和位置
            local globalPos, globalSize = GuideLogic.GetGlobalPosAndSizeByWidget(data.targetWidget)

            -- 计算表现上的点击引导的尺寸和位置
            local localPos, _ = GuideLogic.GetLocalPosAndSize(curBtn:GetParent(), globalPos, globalSize)
            local _, localSize = GuideLogic.GetLocalPosAndSize(curBtn:GetRealSetSizeWidget(), globalPos, globalSize)
            local percentL, percentR, percentT, percentB = 0, 0, 0, 0
            if localSize.X ~= 0 and localSize.Y ~= 0 then
                percentL, percentR, percentT, percentB =
                    offsetLeft / localSize.X, offsetRight / localSize.X, offsetTop / localSize.Y, offsetBottom / localSize.Y
            end
            localPos.X = localPos.X - offsetLeft + triangleOffsetX
            localPos.Y = localPos.Y - offsetTop + triangleOffsetY
            localSize.X = localSize.X + offsetLeft + offsetRight
            localSize.Y = localSize.Y + offsetTop + offsetBottom
            if data.uiOffsetForDisplay then
                localPos.X = localPos.X - (data.uiOffsetForDisplay[3] or 0)
                localPos.Y = localPos.Y - (data.uiOffsetForDisplay[1] or 0)
                localSize.X = localSize.X + (data.uiOffsetForDisplay[3] or 0) + (data.uiOffsetForDisplay[4] or 0)
                localSize.Y = localSize.Y + (data.uiOffsetForDisplay[1] or 0) + (data.uiOffsetForDisplay[2] or 0)
            end
            curBtn:SetPos(localPos + FVector2D(localSize.X * 0.5, localSize.Y * 0.5))
            curBtn:SetSize(localSize)
            curBtn:SetScale(data.triangleScale)
            curBtn:SetUIType(data.uiType)
            curBtn:Set_Multiple()

            -- 计算实际逻辑点击穿透和挖孔的尺寸和位置
            local localPos, localSize = GuideLogic.GetLocalPosAndSize(self._wtNoSwallowArea:GetParent(), globalPos, globalSize)
            localPos.X = localPos.X - localSize.X * percentL
            localPos.Y = localPos.Y - localSize.Y * percentT
            localSize.X = localSize.X * (1 + percentL + percentR)
            localSize.Y = localSize.Y * (1 + percentT + percentB)
            self._wtNoSwallowArea:SetMaskImage(idx - 1 + idxOffset, imageAsset)
            self._wtNoSwallowArea:SetMaskPosSizeXYZW(idx - 1 + idxOffset, localPos.X, localPos.Y, localSize.X, localSize.Y)
            self._wtNoSwallowArea:EnableMaskClickClip(idx - 1 + idxOffset, true)

            curBtn:SetRenderOpacity(1)
            curBtn:PlayInAnim(self._showData.exLoopAnimType)
            self._wtNoSwallowArea:SetRenderOpacity(self._bHaveBg and 1 or 0)
        end
        GuideLogic.StaticAsyncSetImagePath(data.maskImg, _afterLoadTexture)
        offset = offset + 1
    end

    for idx, data in ipairs(self._showData.clipDataListOnlyDisplay) do
        if idx + offset > 3 then
            return
        end

        local _afterLoadTexture = function(imageAsset)
            if not self._bSelfActive then
                return
            end

            local offsetLeft, offsetRight, offsetTop, offsetBottom = 0, 0, 0, 0
            -- if data.uiOffset then
            --     offsetTop = data.uiOffset[1] or 0
            --     offsetBottom = data.uiOffset[2] or 0
            --     offsetLeft = data.uiOffset[3] or 0
            --     offsetRight = data.uiOffset[4] or 0
            -- end

            -- 实际目标ui的全局尺寸和位置
            local globalPos, globalSize = GuideLogic.GetGlobalPosAndSizeByWidget(data.targetWidget)

            -- 计算表现上的点击引导的尺寸和位置
            local _, localSize = GuideLogic.GetLocalPosAndSize(self._wtClickItem:GetRealSetSizeWidget(), globalPos, globalSize)
            local percentL, percentR, percentT, percentB = 0, 0, 0, 0
            if localSize.X ~= 0 and localSize.Y ~= 0 then
                percentL, percentR, percentT, percentB =
                    offsetLeft / localSize.X, offsetRight / localSize.X, offsetTop / localSize.Y, offsetBottom / localSize.Y
            end

            -- 计算实际逻辑点击穿透和挖孔的尺寸和位置
            local localPos, localSize = GuideLogic.GetLocalPosAndSize(self._wtNoSwallowArea:GetParent(), globalPos, globalSize)
            localPos.X = localPos.X - localSize.X * percentL
            localPos.Y = localPos.Y - localSize.Y * percentT
            localSize.X = localSize.X * (1 + percentL + percentR)
            localSize.Y = localSize.Y * (1 + percentT + percentB)
            self._wtNoSwallowArea:SetMaskImage(idx - 1 + idxOffset + offset, imageAsset)
            self._wtNoSwallowArea:SetMaskPosSizeXYZW(idx - 1 + idxOffset + offset, localPos.X, localPos.Y, localSize.X, localSize.Y)
            --self._wtNoSwallowArea:EnableMaskClickClip(idx - 1 + idxOffset + offset, false)
        end
        GuideLogic.StaticAsyncSetImagePath(data.maskImg, _afterLoadTexture)
    end

    if self._showData.bNotMaskTopBar then
        local targetWidget, targetUI, targetWidgetList = GuideLogic.GetWidgetByPath(nil, "DFCanvasPanel_0", "TopBarHD")
        if targetUI then
            -- 实际目标ui的全局尺寸和位置
            local globalPos, globalSize = GuideLogic.GetGlobalPosAndSizeByWidget(targetWidget)
            -- 计算实际逻辑点击穿透和挖孔的尺寸和位置
            local localPos, localSize = GuideLogic.GetLocalPosAndSize(self._wtNoSwallowArea:GetParent(), globalPos, globalSize)


            self._topOffset = 160
            self._bottomOffset = 110

            self._wtNoSwallowArea:SetMaskPosSizeXYZW(0, localPos.X, localPos.Y, localSize.X, self._topOffset)
            self._wtNoSwallowArea:SetMaskPosSizeXYZW(1, localPos.X, localPos.Y + localSize.Y - self._bottomOffset, localSize.X, self._bottomOffset)
        end
    end
end

function GuideHDClickUI:_UpdateClickItemPosAndSize()
    if not self._bSelfActive then return end
    if not self._showData then return end
    if not self._bMaskLoadFinished then return false end

    local idxOffset = 2 -- 前两个用来处理上下露出来topbar
    local clipDataList = self._showData.clipDataList
    local offset = 0
    for idx, data in ipairs(clipDataList) do
        -- 先只处理一个
        if idx > 2 then
            return
        end

        local curBtn = self._btnList[idx]

        local offsetLeft, offsetRight, offsetTop, offsetBottom = 0, 0, 0, 0
        if data.uiOffset then
            offsetTop = data.uiOffset[1] or 0
            offsetBottom = data.uiOffset[2] or 0
            offsetLeft = data.uiOffset[3] or 0
            offsetRight = data.uiOffset[4] or 0
        end
        local triangleOffsetX, triangleOffsetY = 0, 0
        if data.triangleUIOffset then
            triangleOffsetX = data.triangleUIOffset[1] or 0
            triangleOffsetY = data.triangleUIOffset[2] or 0
        end

        -- 实际目标ui的全局尺寸和位置
        local globalPos, globalSize = GuideLogic.GetGlobalPosAndSizeByWidget(data.targetWidget)

        -- 计算表现上的点击引导的尺寸和位置
        local localPos, _ = GuideLogic.GetLocalPosAndSize(curBtn:GetParent(), globalPos, globalSize)
        local _, localSize = GuideLogic.GetLocalPosAndSize(curBtn:GetRealSetSizeWidget(), globalPos, globalSize)
        local percentL, percentR, percentT, percentB = 0, 0, 0, 0
        if localSize.X ~= 0 and localSize.Y ~= 0 then
            percentL, percentR, percentT, percentB =
                offsetLeft / localSize.X, offsetRight / localSize.X, offsetTop / localSize.Y, offsetBottom / localSize.Y
        end
        localPos.X = localPos.X - offsetLeft + triangleOffsetX
        localPos.Y = localPos.Y - offsetTop + triangleOffsetY
        localSize.X = localSize.X + offsetLeft + offsetRight
        localSize.Y = localSize.Y + offsetTop + offsetBottom
        if data.uiOffsetForDisplay then
            localPos.X = localPos.X - (data.uiOffsetForDisplay[3] or 0)
            localPos.Y = localPos.Y - (data.uiOffsetForDisplay[1] or 0)
            localSize.X = localSize.X + (data.uiOffsetForDisplay[3] or 0) + (data.uiOffsetForDisplay[4] or 0)
            localSize.Y = localSize.Y + (data.uiOffsetForDisplay[1] or 0) + (data.uiOffsetForDisplay[2] or 0)
        end
        curBtn:SetPos(localPos + FVector2D(localSize.X * 0.5, localSize.Y * 0.5))
        curBtn:SetSize(localSize)
        curBtn:Set_Multiple()

        -- 计算实际逻辑点击穿透和挖孔的尺寸和位置
        local localPos, localSize = GuideLogic.GetLocalPosAndSize(self._wtNoSwallowArea:GetParent(), globalPos, globalSize)
        localPos.X = localPos.X - localSize.X * percentL
        localPos.Y = localPos.Y - localSize.Y * percentT
        localSize.X = localSize.X * (1 + percentL + percentR)
        localSize.Y = localSize.Y * (1 + percentT + percentB)
        self._wtNoSwallowArea:SetMaskPosSizeXYZW(idx - 1 + idxOffset, localPos.X, localPos.Y, localSize.X, localSize.Y)
        offset = offset + 1
    end

    for idx, data in ipairs(self._showData.clipDataListOnlyDisplay) do
        -- 先只处理一个
        if idx + offset > 3 then
            return
        end

        local offsetLeft, offsetRight, offsetTop, offsetBottom = 0, 0, 0, 0
        -- if data.uiOffset then
        --     offsetTop = data.uiOffset[1] or 0
        --     offsetBottom = data.uiOffset[2] or 0
        --     offsetLeft = data.uiOffset[3] or 0
        --     offsetRight = data.uiOffset[4] or 0
        -- end

        -- 实际目标ui的全局尺寸和位置
        local globalPos, globalSize = GuideLogic.GetGlobalPosAndSizeByWidget(data.targetWidget)

        -- 计算表现上的点击引导的尺寸和位置
        local _, localSize = GuideLogic.GetLocalPosAndSize(self._wtClickItem:GetRealSetSizeWidget(), globalPos, globalSize)
        local percentL, percentR, percentT, percentB = 0, 0, 0, 0
        if localSize.X ~= 0 and localSize.Y ~= 0 then
            percentL, percentR, percentT, percentB =
                offsetLeft / localSize.X, offsetRight / localSize.X, offsetTop / localSize.Y, offsetBottom / localSize.Y
        end

        -- 计算实际逻辑点击穿透和挖孔的尺寸和位置
        local localPos, localSize = GuideLogic.GetLocalPosAndSize(self._wtNoSwallowArea:GetParent(), globalPos, globalSize)
        localPos.X = localPos.X - localSize.X * percentL
        localPos.Y = localPos.Y - localSize.Y * percentT
        localSize.X = localSize.X * (1 + percentL + percentR)
        localSize.Y = localSize.Y * (1 + percentT + percentB)
        self._wtNoSwallowArea:SetMaskPosSizeXYZW(idx - 1 + idxOffset + offset, localPos.X, localPos.Y, localSize.X, localSize.Y)
    end

    if self._showData.bNotMaskTopBar then
        local targetWidget, targetUI, targetWidgetList = GuideLogic.GetWidgetByPath(nil, "DFCanvasPanel_0", "TopBarHD")
        if targetUI then
            -- 实际目标ui的全局尺寸和位置
            local globalPos, globalSize = GuideLogic.GetGlobalPosAndSizeByWidget(targetWidget)
            -- 计算实际逻辑点击穿透和挖孔的尺寸和位置
            local localPos, localSize = GuideLogic.GetLocalPosAndSize(self._wtNoSwallowArea:GetParent(), globalPos, globalSize)

            self._wtNoSwallowArea:SetMaskPosSizeXYZW(0, localPos.X, localPos.Y, localSize.X, self._topOffset)
            self._wtNoSwallowArea:SetMaskPosSizeXYZW(1, localPos.X, localPos.Y + localSize.Y - self._bottomOffset, localSize.X, self._bottomOffset)
        end
    end
end

function GuideHDClickUI:_AddTickTimer()
    log("_AddTickTimer", self._checkTimerHandle)
    if self._checkTimerHandle then
        self._checkTimerHandle:Start()
        return
    end
    self._checkTimerHandle = Timer:NewIns(0.01, 0)
    self._checkTimerHandle:AddListener(self._UpdateClickItemPosAndSize, self)
    self._checkTimerHandle:Start()
end

function GuideHDClickUI:_PauseTickTimer()
    log("_PauseTickTimer", self._checkTimerHandle)
    if self._checkTimerHandle then
        self._checkTimerHandle:Stop()
    end
end

function GuideHDClickUI:_RemoveTickTimer()
    log("_RemoveTickTimer", self._checkTimerHandle)
    if self._checkTimerHandle then
        self._checkTimerHandle:Release()
        self._checkTimerHandle = nil
    end
end

-- TODO 未来如果接入消失动画的话，消失动画结束再调用这个
function GuideHDClickUI:_OnRealClose()
    if self._callbackOnEnd then
        self:_callbackOnEnd()
        return
    end

    self:RecycleSelf()
end

function GuideHDClickUI:_PlayAudio()
    if not self._showData then return end
    local audioEvent = self._showData.tipsAudio
    if audioEvent then
        Facade.SoundManager:PlayGuideAudio(audioEvent)
        self._curAudio = audioEvent
    end
end

return GuideHDClickUI
