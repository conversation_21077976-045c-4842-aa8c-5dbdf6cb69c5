--cs_season.protoencode&decode functions.
function pb.pb_CSSeasonGetInfoReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSeasonGetInfoReq) or {} 
    local __serial = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __serial ~= 0 then tb.serial = __serial end
    local __player_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __mode = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __mode ~= 0 then tb.mode = __mode end
    return tb
end

function pb.pb_CSSeasonGetInfoReqEncode(tb, encoder)
    if(tb.serial) then    encoder:addi32(1, tb.serial)    end
    if(tb.player_id) then    encoder:addu64(2, tb.player_id)    end
    if(tb.mode) then    encoder:addu32(3, tb.mode)    end
end

function pb.pb_CSSeasonGetInfoResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSeasonGetInfoRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __cur_serial = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __cur_serial ~= 0 then tb.cur_serial = __cur_serial end
    local __begin_serial = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __begin_serial ~= 0 then tb.begin_serial = __begin_serial end
    tb.season_info = pb.pb_ProfileSeasonInfoDecode(decoder:getsubmsg(4))
    local __prev_serial = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __prev_serial ~= 0 then tb.prev_serial = __prev_serial end
    local __prev_commander_serial = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __prev_commander_serial ~= 0 then tb.prev_commander_serial = __prev_commander_serial end
    local __start_time = decoder:getu32(10)
    if not PB_USE_DEFAULT_TABLE or __start_time ~= 0 then tb.start_time = __start_time end
    local __end_time = decoder:getu32(11)
    if not PB_USE_DEFAULT_TABLE or __end_time ~= 0 then tb.end_time = __end_time end
    tb.awards = {}
    for k,v in pairs(decoder:getsubmsgary(12)) do
        tb.awards[k] = pb.pb_PropInfoDecode(v)
    end
    tb.mp_awards = {}
    for k,v in pairs(decoder:getsubmsgary(13)) do
        tb.mp_awards[k] = pb.pb_PropInfoDecode(v)
    end
    tb.mp_commander_awards = {}
    for k,v in pairs(decoder:getsubmsgary(14)) do
        tb.mp_commander_awards[k] = pb.pb_PropInfoDecode(v)
    end
    local __UIStartTime = decoder:getu32(20)
    if not PB_USE_DEFAULT_TABLE or __UIStartTime ~= 0 then tb.UIStartTime = __UIStartTime end
    local __UIEndTime = decoder:getu32(21)
    if not PB_USE_DEFAULT_TABLE or __UIEndTime ~= 0 then tb.UIEndTime = __UIEndTime end
    local __next_serial_start_time = decoder:getu32(30)
    if not PB_USE_DEFAULT_TABLE or __next_serial_start_time ~= 0 then tb.next_serial_start_time = __next_serial_start_time end
    return tb
end

function pb.pb_CSSeasonGetInfoResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.cur_serial) then    encoder:addu32(2, tb.cur_serial)    end
    if(tb.begin_serial) then    encoder:addu32(3, tb.begin_serial)    end
    if(tb.season_info) then    pb.pb_ProfileSeasonInfoEncode(tb.season_info, encoder:addsubmsg(4))    end
    if(tb.prev_serial) then    encoder:addu32(5, tb.prev_serial)    end
    if(tb.prev_commander_serial) then    encoder:addu32(6, tb.prev_commander_serial)    end
    if(tb.start_time) then    encoder:addu32(10, tb.start_time)    end
    if(tb.end_time) then    encoder:addu32(11, tb.end_time)    end
    if(tb.awards) then
        for i=1,#(tb.awards) do
            pb.pb_PropInfoEncode(tb.awards[i], encoder:addsubmsg(12))
        end
    end
    if(tb.mp_awards) then
        for i=1,#(tb.mp_awards) do
            pb.pb_PropInfoEncode(tb.mp_awards[i], encoder:addsubmsg(13))
        end
    end
    if(tb.mp_commander_awards) then
        for i=1,#(tb.mp_commander_awards) do
            pb.pb_PropInfoEncode(tb.mp_commander_awards[i], encoder:addsubmsg(14))
        end
    end
    if(tb.UIStartTime) then    encoder:addu32(20, tb.UIStartTime)    end
    if(tb.UIEndTime) then    encoder:addu32(21, tb.UIEndTime)    end
    if(tb.next_serial_start_time) then    encoder:addu32(30, tb.next_serial_start_time)    end
end

function pb.pb_CSSeasonGetTotalDataReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSeasonGetTotalDataReq) or {} 
    tb.mode = pb.pb_MatchModeInfoDecode(decoder:getsubmsg(1))
    local __player_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __is_career = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __is_career ~= false then tb.is_career = __is_career end
    return tb
end

function pb.pb_CSSeasonGetTotalDataReqEncode(tb, encoder)
    if(tb.mode) then    pb.pb_MatchModeInfoEncode(tb.mode, encoder:addsubmsg(1))    end
    if(tb.player_id) then    encoder:addu64(2, tb.player_id)    end
    if(tb.is_career) then    encoder:addbool(3, tb.is_career)    end
end

function pb.pb_CSSeasonGetTotalDataResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSeasonGetTotalDataRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __total_fight = decoder:getu32(100)
    if not PB_USE_DEFAULT_TABLE or __total_fight ~= 0 then tb.total_fight = __total_fight end
    local __kd = decoder:getfloat(101)
    if not PB_USE_DEFAULT_TABLE or __kd ~= 0 then tb.kd = __kd end
    local __total_escaped = decoder:getu32(102)
    if not PB_USE_DEFAULT_TABLE or __total_escaped ~= 0 then tb.total_escaped = __total_escaped end
    local __avg_survival_time = decoder:getu64(103)
    if not PB_USE_DEFAULT_TABLE or __avg_survival_time ~= 0 then tb.avg_survival_time = __avg_survival_time end
    local __max_escape_streak = decoder:getu32(104)
    if not PB_USE_DEFAULT_TABLE or __max_escape_streak ~= 0 then tb.max_escape_streak = __max_escape_streak end
    local __total_game_time = decoder:getu64(105)
    if not PB_USE_DEFAULT_TABLE or __total_game_time ~= 0 then tb.total_game_time = __total_game_time end
    local __carry_teammate_assets = decoder:getu64(106)
    if not PB_USE_DEFAULT_TABLE or __carry_teammate_assets ~= 0 then tb.carry_teammate_assets = __carry_teammate_assets end
    tb.sol_data = pb.pb_IrisSOLDataDecode(decoder:getsubmsg(200))
    tb.raid_data = pb.pb_IrisRaidDataDecode(decoder:getsubmsg(201))
    tb.mp_data = pb.pb_MPDataDecode(decoder:getsubmsg(202))
    return tb
end

function pb.pb_CSSeasonGetTotalDataResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.total_fight) then    encoder:addu32(100, tb.total_fight)    end
    if(tb.kd) then    encoder:addfloat(101, tb.kd)    end
    if(tb.total_escaped) then    encoder:addu32(102, tb.total_escaped)    end
    if(tb.avg_survival_time) then    encoder:addu64(103, tb.avg_survival_time)    end
    if(tb.max_escape_streak) then    encoder:addu32(104, tb.max_escape_streak)    end
    if(tb.total_game_time) then    encoder:addu64(105, tb.total_game_time)    end
    if(tb.carry_teammate_assets) then    encoder:addu64(106, tb.carry_teammate_assets)    end
    if(tb.sol_data) then    pb.pb_IrisSOLDataEncode(tb.sol_data, encoder:addsubmsg(200))    end
    if(tb.raid_data) then    pb.pb_IrisRaidDataEncode(tb.raid_data, encoder:addsubmsg(201))    end
    if(tb.mp_data) then    pb.pb_MPDataEncode(tb.mp_data, encoder:addsubmsg(202))    end
end

function pb.pb_CSSeasonGetDetailDataReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSeasonGetDetailDataReq) or {} 
    tb.mode = pb.pb_MatchModeInfoDecode(decoder:getsubmsg(1))
    local __player_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __season_no = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __season_no ~= 0 then tb.season_no = __season_no end
    local __is_rank = decoder:getbool(4)
    if not PB_USE_DEFAULT_TABLE or __is_rank ~= false then tb.is_rank = __is_rank end
    local __is_victory_unite = decoder:getbool(5)
    if not PB_USE_DEFAULT_TABLE or __is_victory_unite ~= false then tb.is_victory_unite = __is_victory_unite end
    local __is_victory_unite_commander = decoder:getbool(6)
    if not PB_USE_DEFAULT_TABLE or __is_victory_unite_commander ~= false then tb.is_victory_unite_commander = __is_victory_unite_commander end
    return tb
end

function pb.pb_CSSeasonGetDetailDataReqEncode(tb, encoder)
    if(tb.mode) then    pb.pb_MatchModeInfoEncode(tb.mode, encoder:addsubmsg(1))    end
    if(tb.player_id) then    encoder:addu64(2, tb.player_id)    end
    if(tb.season_no) then    encoder:addu32(3, tb.season_no)    end
    if(tb.is_rank) then    encoder:addbool(4, tb.is_rank)    end
    if(tb.is_victory_unite) then    encoder:addbool(5, tb.is_victory_unite)    end
    if(tb.is_victory_unite_commander) then    encoder:addbool(6, tb.is_victory_unite_commander)    end
end

function pb.pb_CSSeasonGetDetailDataResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSeasonGetDetailDataRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.sol_data = pb.pb_CSSOLSeasonDataDecode(decoder:getsubmsg(100))
    tb.mp_data = pb.pb_CSMPSeasonDataDecode(decoder:getsubmsg(102))
    return tb
end

function pb.pb_CSSeasonGetDetailDataResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.sol_data) then    pb.pb_CSSOLSeasonDataEncode(tb.sol_data, encoder:addsubmsg(100))    end
    if(tb.mp_data) then    pb.pb_CSMPSeasonDataEncode(tb.mp_data, encoder:addsubmsg(102))    end
end

function pb.pb_CSSOLSeasonDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSOLSeasonData) or {} 
    local __total_fight = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __total_fight ~= 0 then tb.total_fight = __total_fight end
    local __total_escape = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __total_escape ~= 0 then tb.total_escape = __total_escape end
    local __total_game_time = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __total_game_time ~= 0 then tb.total_game_time = __total_game_time end
    local __total_kill = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __total_kill ~= 0 then tb.total_kill = __total_kill end
    local __total_killed = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __total_killed ~= 0 then tb.total_killed = __total_killed end
    local __carry_teammate_assets = decoder:getu64(7)
    if not PB_USE_DEFAULT_TABLE or __carry_teammate_assets ~= 0 then tb.carry_teammate_assets = __carry_teammate_assets end
    local __total_collection_price = decoder:getu64(8)
    if not PB_USE_DEFAULT_TABLE or __total_collection_price ~= 0 then tb.total_collection_price = __total_collection_price end
    local __total_gained_price = decoder:getu64(9)
    if not PB_USE_DEFAULT_TABLE or __total_gained_price ~= 0 then tb.total_gained_price = __total_gained_price end
    local __total_shoot = decoder:getu64(10)
    if not PB_USE_DEFAULT_TABLE or __total_shoot ~= 0 then tb.total_shoot = __total_shoot end
    local __total_shoot_hit = decoder:getu64(11)
    if not PB_USE_DEFAULT_TABLE or __total_shoot_hit ~= 0 then tb.total_shoot_hit = __total_shoot_hit end
    local __total_shoot_down = decoder:getu32(13)
    if not PB_USE_DEFAULT_TABLE or __total_shoot_down ~= 0 then tb.total_shoot_down = __total_shoot_down end
    local __total_shoot_head_down = decoder:getu32(14)
    if not PB_USE_DEFAULT_TABLE or __total_shoot_head_down ~= 0 then tb.total_shoot_head_down = __total_shoot_head_down end
    local __total_pickup_teammate = decoder:getu32(15)
    if not PB_USE_DEFAULT_TABLE or __total_pickup_teammate ~= 0 then tb.total_pickup_teammate = __total_pickup_teammate end
    local __total_revive_teammate = decoder:getu32(16)
    if not PB_USE_DEFAULT_TABLE or __total_revive_teammate ~= 0 then tb.total_revive_teammate = __total_revive_teammate end
    local __total_contract_price = decoder:getu64(17)
    if not PB_USE_DEFAULT_TABLE or __total_contract_price ~= 0 then tb.total_contract_price = __total_contract_price end
    local __total_bring_mander_box = decoder:getu32(18)
    if not PB_USE_DEFAULT_TABLE or __total_bring_mander_box ~= 0 then tb.total_bring_mander_box = __total_bring_mander_box end
    local __kill_low_stakes = decoder:getu32(20)
    if not PB_USE_DEFAULT_TABLE or __kill_low_stakes ~= 0 then tb.kill_low_stakes = __kill_low_stakes end
    local __kill_med_stakes = decoder:getu32(21)
    if not PB_USE_DEFAULT_TABLE or __kill_med_stakes ~= 0 then tb.kill_med_stakes = __kill_med_stakes end
    local __kill_high_stakes = decoder:getu32(22)
    if not PB_USE_DEFAULT_TABLE or __kill_high_stakes ~= 0 then tb.kill_high_stakes = __kill_high_stakes end
    local __killed_low_stakes = decoder:getu32(30)
    if not PB_USE_DEFAULT_TABLE or __killed_low_stakes ~= 0 then tb.killed_low_stakes = __killed_low_stakes end
    local __killed_med_stakes = decoder:getu32(31)
    if not PB_USE_DEFAULT_TABLE or __killed_med_stakes ~= 0 then tb.killed_med_stakes = __killed_med_stakes end
    local __killed_high_stakes = decoder:getu32(32)
    if not PB_USE_DEFAULT_TABLE or __killed_high_stakes ~= 0 then tb.killed_high_stakes = __killed_high_stakes end
    tb.season_avg_radar = pb.pb_CSSOLRadarDataDecode(decoder:getsubmsg(40))
    tb.recent_avg_radar = pb.pb_CSSOLRadarDataDecode(decoder:getsubmsg(41))
    tb.season_avg_radar_dims = {}
    for k,v in pairs(decoder:getsubmsgary(42)) do
        tb.season_avg_radar_dims[k] = pb.pb_CSRadarDimDecode(v)
    end
    tb.recent_avg_radar_dims = {}
    for k,v in pairs(decoder:getsubmsgary(43)) do
        tb.recent_avg_radar_dims[k] = pb.pb_CSRadarDimDecode(v)
    end
    local __recent_not_enough = decoder:getbool(44)
    if not PB_USE_DEFAULT_TABLE or __recent_not_enough ~= false then tb.recent_not_enough = __recent_not_enough end
    local __season_not_enough = decoder:getbool(45)
    if not PB_USE_DEFAULT_TABLE or __season_not_enough ~= false then tb.season_not_enough = __season_not_enough end
    local __rank_attended = decoder:getbool(50)
    if not PB_USE_DEFAULT_TABLE or __rank_attended ~= false then tb.rank_attended = __rank_attended end
    local __rank_score = decoder:geti64(51)
    if not PB_USE_DEFAULT_TABLE or __rank_score ~= 0 then tb.rank_score = __rank_score end
    local __rank_score_max = decoder:geti64(52)
    if not PB_USE_DEFAULT_TABLE or __rank_score_max ~= 0 then tb.rank_score_max = __rank_score_max end
    local __rank_max_season_no = decoder:getu32(53)
    if not PB_USE_DEFAULT_TABLE or __rank_max_season_no ~= 0 then tb.rank_max_season_no = __rank_max_season_no end
    return tb
end

function pb.pb_CSSOLSeasonDataEncode(tb, encoder)
    if(tb.total_fight) then    encoder:addu32(1, tb.total_fight)    end
    if(tb.total_escape) then    encoder:addu32(2, tb.total_escape)    end
    if(tb.total_game_time) then    encoder:addu64(3, tb.total_game_time)    end
    if(tb.total_kill) then    encoder:addu32(5, tb.total_kill)    end
    if(tb.total_killed) then    encoder:addu32(6, tb.total_killed)    end
    if(tb.carry_teammate_assets) then    encoder:addu64(7, tb.carry_teammate_assets)    end
    if(tb.total_collection_price) then    encoder:addu64(8, tb.total_collection_price)    end
    if(tb.total_gained_price) then    encoder:addu64(9, tb.total_gained_price)    end
    if(tb.total_shoot) then    encoder:addu64(10, tb.total_shoot)    end
    if(tb.total_shoot_hit) then    encoder:addu64(11, tb.total_shoot_hit)    end
    if(tb.total_shoot_down) then    encoder:addu32(13, tb.total_shoot_down)    end
    if(tb.total_shoot_head_down) then    encoder:addu32(14, tb.total_shoot_head_down)    end
    if(tb.total_pickup_teammate) then    encoder:addu32(15, tb.total_pickup_teammate)    end
    if(tb.total_revive_teammate) then    encoder:addu32(16, tb.total_revive_teammate)    end
    if(tb.total_contract_price) then    encoder:addu64(17, tb.total_contract_price)    end
    if(tb.total_bring_mander_box) then    encoder:addu32(18, tb.total_bring_mander_box)    end
    if(tb.kill_low_stakes) then    encoder:addu32(20, tb.kill_low_stakes)    end
    if(tb.kill_med_stakes) then    encoder:addu32(21, tb.kill_med_stakes)    end
    if(tb.kill_high_stakes) then    encoder:addu32(22, tb.kill_high_stakes)    end
    if(tb.killed_low_stakes) then    encoder:addu32(30, tb.killed_low_stakes)    end
    if(tb.killed_med_stakes) then    encoder:addu32(31, tb.killed_med_stakes)    end
    if(tb.killed_high_stakes) then    encoder:addu32(32, tb.killed_high_stakes)    end
    if(tb.season_avg_radar) then    pb.pb_CSSOLRadarDataEncode(tb.season_avg_radar, encoder:addsubmsg(40))    end
    if(tb.recent_avg_radar) then    pb.pb_CSSOLRadarDataEncode(tb.recent_avg_radar, encoder:addsubmsg(41))    end
    if(tb.season_avg_radar_dims) then
        for i=1,#(tb.season_avg_radar_dims) do
            pb.pb_CSRadarDimEncode(tb.season_avg_radar_dims[i], encoder:addsubmsg(42))
        end
    end
    if(tb.recent_avg_radar_dims) then
        for i=1,#(tb.recent_avg_radar_dims) do
            pb.pb_CSRadarDimEncode(tb.recent_avg_radar_dims[i], encoder:addsubmsg(43))
        end
    end
    if(tb.recent_not_enough) then    encoder:addbool(44, tb.recent_not_enough)    end
    if(tb.season_not_enough) then    encoder:addbool(45, tb.season_not_enough)    end
    if(tb.rank_attended) then    encoder:addbool(50, tb.rank_attended)    end
    if(tb.rank_score) then    encoder:addi64(51, tb.rank_score)    end
    if(tb.rank_score_max) then    encoder:addi64(52, tb.rank_score_max)    end
    if(tb.rank_max_season_no) then    encoder:addu32(53, tb.rank_max_season_no)    end
end

function pb.pb_CSRadarDimDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRadarDim) or {} 
    local __dim = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __dim ~= 0 then tb.dim = __dim end
    local __score = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __score ~= 0 then tb.score = __score end
    return tb
end

function pb.pb_CSRadarDimEncode(tb, encoder)
    if(tb.dim) then    encoder:addi32(1, tb.dim)    end
    if(tb.score) then    encoder:addi32(2, tb.score)    end
end

function pb.pb_CSSOLRadarDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSOLRadarData) or {} 
    local __team = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __team ~= 0 then tb.team = __team end
    local __fight = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __fight ~= 0 then tb.fight = __fight end
    local __search = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __search ~= 0 then tb.search = __search end
    local __survive = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __survive ~= 0 then tb.survive = __survive end
    local __wealth = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __wealth ~= 0 then tb.wealth = __wealth end
    return tb
end

function pb.pb_CSSOLRadarDataEncode(tb, encoder)
    if(tb.team) then    encoder:addi32(1, tb.team)    end
    if(tb.fight) then    encoder:addi32(2, tb.fight)    end
    if(tb.search) then    encoder:addi32(3, tb.search)    end
    if(tb.survive) then    encoder:addi32(4, tb.survive)    end
    if(tb.wealth) then    encoder:addi32(5, tb.wealth)    end
end

function pb.pb_CSMPSeasonDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMPSeasonData) or {} 
    local __total_fight = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __total_fight ~= 0 then tb.total_fight = __total_fight end
    local __total_win = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __total_win ~= 0 then tb.total_win = __total_win end
    local __total_kill = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __total_kill ~= 0 then tb.total_kill = __total_kill end
    local __total_game_time = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __total_game_time ~= 0 then tb.total_game_time = __total_game_time end
    local __total_score = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __total_score ~= 0 then tb.total_score = __total_score end
    local __total_help = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __total_help ~= 0 then tb.total_help = __total_help end
    local __total_killed = decoder:getu32(10)
    if not PB_USE_DEFAULT_TABLE or __total_killed ~= 0 then tb.total_killed = __total_killed end
    local __total_mvp = decoder:getu32(11)
    if not PB_USE_DEFAULT_TABLE or __total_mvp ~= 0 then tb.total_mvp = __total_mvp end
    local __total_quit = decoder:getu32(12)
    if not PB_USE_DEFAULT_TABLE or __total_quit ~= 0 then tb.total_quit = __total_quit end
    local __total_shoot = decoder:getu64(20)
    if not PB_USE_DEFAULT_TABLE or __total_shoot ~= 0 then tb.total_shoot = __total_shoot end
    local __total_shoot_hit = decoder:getu64(21)
    if not PB_USE_DEFAULT_TABLE or __total_shoot_hit ~= 0 then tb.total_shoot_hit = __total_shoot_hit end
    local __total_kill_head = decoder:getu32(22)
    if not PB_USE_DEFAULT_TABLE or __total_kill_head ~= 0 then tb.total_kill_head = __total_kill_head end
    local __total_capture_point = decoder:getu32(24)
    if not PB_USE_DEFAULT_TABLE or __total_capture_point ~= 0 then tb.total_capture_point = __total_capture_point end
    local __total_vehicle_use_time = decoder:getu64(30)
    if not PB_USE_DEFAULT_TABLE or __total_vehicle_use_time ~= 0 then tb.total_vehicle_use_time = __total_vehicle_use_time end
    local __total_damage_to_vehicle = decoder:getu64(31)
    if not PB_USE_DEFAULT_TABLE or __total_damage_to_vehicle ~= 0 then tb.total_damage_to_vehicle = __total_damage_to_vehicle end
    local __total_vehicle_kill = decoder:getu32(32)
    if not PB_USE_DEFAULT_TABLE or __total_vehicle_kill ~= 0 then tb.total_vehicle_kill = __total_vehicle_kill end
    local __total_vehicle_destroyed = decoder:getu32(33)
    if not PB_USE_DEFAULT_TABLE or __total_vehicle_destroyed ~= 0 then tb.total_vehicle_destroyed = __total_vehicle_destroyed end
    tb.season_avg_radar = pb.pb_CSMPRadarDataDecode(decoder:getsubmsg(40))
    tb.recent_avg_radar = pb.pb_CSMPRadarDataDecode(decoder:getsubmsg(41))
    tb.season_avg_radar_dims = {}
    for k,v in pairs(decoder:getsubmsgary(42)) do
        tb.season_avg_radar_dims[k] = pb.pb_CSRadarDimDecode(v)
    end
    tb.recent_avg_radar_dims = {}
    for k,v in pairs(decoder:getsubmsgary(43)) do
        tb.recent_avg_radar_dims[k] = pb.pb_CSRadarDimDecode(v)
    end
    local __recent_not_enough = decoder:getbool(44)
    if not PB_USE_DEFAULT_TABLE or __recent_not_enough ~= false then tb.recent_not_enough = __recent_not_enough end
    local __season_not_enough = decoder:getbool(45)
    if not PB_USE_DEFAULT_TABLE or __season_not_enough ~= false then tb.season_not_enough = __season_not_enough end
    local __rank_attended = decoder:getbool(50)
    if not PB_USE_DEFAULT_TABLE or __rank_attended ~= false then tb.rank_attended = __rank_attended end
    local __rank_score = decoder:geti64(51)
    if not PB_USE_DEFAULT_TABLE or __rank_score ~= 0 then tb.rank_score = __rank_score end
    local __rank_score_max = decoder:geti64(52)
    if not PB_USE_DEFAULT_TABLE or __rank_score_max ~= 0 then tb.rank_score_max = __rank_score_max end
    local __rank_max_season_no = decoder:getu32(53)
    if not PB_USE_DEFAULT_TABLE or __rank_max_season_no ~= 0 then tb.rank_max_season_no = __rank_max_season_no end
    local __commander_attended = decoder:getbool(60)
    if not PB_USE_DEFAULT_TABLE or __commander_attended ~= false then tb.commander_attended = __commander_attended end
    local __commander_score = decoder:geti64(61)
    if not PB_USE_DEFAULT_TABLE or __commander_score ~= 0 then tb.commander_score = __commander_score end
    local __commander_score_max = decoder:geti64(62)
    if not PB_USE_DEFAULT_TABLE or __commander_score_max ~= 0 then tb.commander_score_max = __commander_score_max end
    local __commander_max_season_no = decoder:geti64(63)
    if not PB_USE_DEFAULT_TABLE or __commander_max_season_no ~= 0 then tb.commander_max_season_no = __commander_max_season_no end
    return tb
end

function pb.pb_CSMPSeasonDataEncode(tb, encoder)
    if(tb.total_fight) then    encoder:addu32(1, tb.total_fight)    end
    if(tb.total_win) then    encoder:addu32(2, tb.total_win)    end
    if(tb.total_kill) then    encoder:addu32(3, tb.total_kill)    end
    if(tb.total_game_time) then    encoder:addu64(4, tb.total_game_time)    end
    if(tb.total_score) then    encoder:addu64(5, tb.total_score)    end
    if(tb.total_help) then    encoder:addu32(6, tb.total_help)    end
    if(tb.total_killed) then    encoder:addu32(10, tb.total_killed)    end
    if(tb.total_mvp) then    encoder:addu32(11, tb.total_mvp)    end
    if(tb.total_quit) then    encoder:addu32(12, tb.total_quit)    end
    if(tb.total_shoot) then    encoder:addu64(20, tb.total_shoot)    end
    if(tb.total_shoot_hit) then    encoder:addu64(21, tb.total_shoot_hit)    end
    if(tb.total_kill_head) then    encoder:addu32(22, tb.total_kill_head)    end
    if(tb.total_capture_point) then    encoder:addu32(24, tb.total_capture_point)    end
    if(tb.total_vehicle_use_time) then    encoder:addu64(30, tb.total_vehicle_use_time)    end
    if(tb.total_damage_to_vehicle) then    encoder:addu64(31, tb.total_damage_to_vehicle)    end
    if(tb.total_vehicle_kill) then    encoder:addu32(32, tb.total_vehicle_kill)    end
    if(tb.total_vehicle_destroyed) then    encoder:addu32(33, tb.total_vehicle_destroyed)    end
    if(tb.season_avg_radar) then    pb.pb_CSMPRadarDataEncode(tb.season_avg_radar, encoder:addsubmsg(40))    end
    if(tb.recent_avg_radar) then    pb.pb_CSMPRadarDataEncode(tb.recent_avg_radar, encoder:addsubmsg(41))    end
    if(tb.season_avg_radar_dims) then
        for i=1,#(tb.season_avg_radar_dims) do
            pb.pb_CSRadarDimEncode(tb.season_avg_radar_dims[i], encoder:addsubmsg(42))
        end
    end
    if(tb.recent_avg_radar_dims) then
        for i=1,#(tb.recent_avg_radar_dims) do
            pb.pb_CSRadarDimEncode(tb.recent_avg_radar_dims[i], encoder:addsubmsg(43))
        end
    end
    if(tb.recent_not_enough) then    encoder:addbool(44, tb.recent_not_enough)    end
    if(tb.season_not_enough) then    encoder:addbool(45, tb.season_not_enough)    end
    if(tb.rank_attended) then    encoder:addbool(50, tb.rank_attended)    end
    if(tb.rank_score) then    encoder:addi64(51, tb.rank_score)    end
    if(tb.rank_score_max) then    encoder:addi64(52, tb.rank_score_max)    end
    if(tb.rank_max_season_no) then    encoder:addu32(53, tb.rank_max_season_no)    end
    if(tb.commander_attended) then    encoder:addbool(60, tb.commander_attended)    end
    if(tb.commander_score) then    encoder:addi64(61, tb.commander_score)    end
    if(tb.commander_score_max) then    encoder:addi64(62, tb.commander_score_max)    end
    if(tb.commander_max_season_no) then    encoder:addi64(63, tb.commander_max_season_no)    end
end

function pb.pb_CSMPRadarDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMPRadarData) or {} 
    local __vehicle = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __vehicle ~= 0 then tb.vehicle = __vehicle end
    local __shoot = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __shoot ~= 0 then tb.shoot = __shoot end
    local __survive = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __survive ~= 0 then tb.survive = __survive end
    local __target = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __target ~= 0 then tb.target = __target end
    local __team = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __team ~= 0 then tb.team = __team end
    return tb
end

function pb.pb_CSMPRadarDataEncode(tb, encoder)
    if(tb.vehicle) then    encoder:addi32(1, tb.vehicle)    end
    if(tb.shoot) then    encoder:addi32(2, tb.shoot)    end
    if(tb.survive) then    encoder:addi32(3, tb.survive)    end
    if(tb.target) then    encoder:addi32(4, tb.target)    end
    if(tb.team) then    encoder:addi32(5, tb.team)    end
end

function pb.pb_CSSeasonGetRecordListReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSeasonGetRecordListReq) or {} 
    local __page = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __page ~= 0 then tb.page = __page end
    local __num = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __num ~= 0 then tb.num = __num end
    tb.mode = pb.pb_MatchModeInfoDecode(decoder:getsubmsg(3))
    local __player_id = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    return tb
end

function pb.pb_CSSeasonGetRecordListReqEncode(tb, encoder)
    if(tb.page) then    encoder:addu32(1, tb.page)    end
    if(tb.num) then    encoder:addu32(2, tb.num)    end
    if(tb.mode) then    pb.pb_MatchModeInfoEncode(tb.mode, encoder:addsubmsg(3))    end
    if(tb.player_id) then    encoder:addu64(4, tb.player_id)    end
end

function pb.pb_CSSeasonGetRecordListResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSeasonGetRecordListRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.record_list = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.record_list[k] = pb.pb_MatchBaseRecordDecode(v)
    end
    local __page = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __page ~= 0 then tb.page = __page end
    local __num = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __num ~= 0 then tb.num = __num end
    local __max_page = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __max_page ~= 0 then tb.max_page = __max_page end
    local __is_hidden = decoder:getbool(6)
    if not PB_USE_DEFAULT_TABLE or __is_hidden ~= false then tb.is_hidden = __is_hidden end
    return tb
end

function pb.pb_CSSeasonGetRecordListResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.record_list) then
        for i=1,#(tb.record_list) do
            pb.pb_MatchBaseRecordEncode(tb.record_list[i], encoder:addsubmsg(2))
        end
    end
    if(tb.page) then    encoder:addu32(3, tb.page)    end
    if(tb.num) then    encoder:addu32(4, tb.num)    end
    if(tb.max_page) then    encoder:addu32(5, tb.max_page)    end
    if(tb.is_hidden) then    encoder:addbool(6, tb.is_hidden)    end
end

function pb.pb_CSSeasonGetRecordReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSeasonGetRecordReq) or {} 
    local __key = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __key ~= "" then tb.key = __key end
    tb.mode = pb.pb_MatchModeInfoDecode(decoder:getsubmsg(2))
    local __show_teammates_only = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __show_teammates_only ~= false then tb.show_teammates_only = __show_teammates_only end
    local __show_player_id = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __show_player_id ~= 0 then tb.show_player_id = __show_player_id end
    return tb
end

function pb.pb_CSSeasonGetRecordReqEncode(tb, encoder)
    if(tb.key) then    encoder:addstr(1, tb.key)    end
    if(tb.mode) then    pb.pb_MatchModeInfoEncode(tb.mode, encoder:addsubmsg(2))    end
    if(tb.show_teammates_only) then    encoder:addbool(3, tb.show_teammates_only)    end
    if(tb.show_player_id) then    encoder:addu64(4, tb.show_player_id)    end
end

function pb.pb_CSSeasonGetRecordResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSeasonGetRecordRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.player_record = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.player_record[k] = pb.pb_PlayerMatchRecordDecode(v)
    end
    local __game_result = decoder:getu32(7)
    if not PB_USE_DEFAULT_TABLE or __game_result ~= 0 then tb.game_result = __game_result end
    return tb
end

function pb.pb_CSSeasonGetRecordResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.player_record) then
        for i=1,#(tb.player_record) do
            pb.pb_PlayerMatchRecordEncode(tb.player_record[i], encoder:addsubmsg(2))
        end
    end
    if(tb.game_result) then    encoder:addu32(7, tb.game_result)    end
end

function pb.pb_CSSeasonRaidMapListReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSeasonRaidMapListReq) or {} 
    return tb
end

function pb.pb_CSSeasonRaidMapListReqEncode(tb, encoder)
end

function pb.pb_RaidMapItemDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RaidMapItem) or {} 
    local __map_name = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __map_name ~= "" then tb.map_name = __map_name end
    local __map_id = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __map_id ~= 0 then tb.map_id = __map_id end
    return tb
end

function pb.pb_RaidMapItemEncode(tb, encoder)
    if(tb.map_name) then    encoder:addstr(1, tb.map_name)    end
    if(tb.map_id) then    encoder:addu32(2, tb.map_id)    end
end

function pb.pb_CSSeasonRaidMapListResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSeasonRaidMapListRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.maps = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.maps[k] = pb.pb_RaidMapItemDecode(v)
    end
    return tb
end

function pb.pb_CSSeasonRaidMapListResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.maps) then
        for i=1,#(tb.maps) do
            pb.pb_RaidMapItemEncode(tb.maps[i], encoder:addsubmsg(2))
        end
    end
end

function pb.pb_CSSeasonGetMatchDeathHurtDataReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSeasonGetMatchDeathHurtDataReq) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __room_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    return tb
end

function pb.pb_CSSeasonGetMatchDeathHurtDataReqEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.room_id) then    encoder:addu64(2, tb.room_id)    end
end

function pb.pb_CSSeasonGetMatchDeathHurtDataResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSeasonGetMatchDeathHurtDataRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.death_hurt_data = pb.pb_Ds2AnyDeathAndHurtDetailInfoNtfDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSSeasonGetMatchDeathHurtDataResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.death_hurt_data) then    pb.pb_Ds2AnyDeathAndHurtDetailInfoNtfEncode(tb.death_hurt_data, encoder:addsubmsg(2))    end
end

function pb.pb_CSSeasonGetMatchScoreDataReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSeasonGetMatchScoreDataReq) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __room_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    return tb
end

function pb.pb_CSSeasonGetMatchScoreDataReqEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.room_id) then    encoder:addu64(2, tb.room_id)    end
end

function pb.pb_CSSeasonGetMatchScoreDataResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSeasonGetMatchScoreDataRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.rank_score_changes = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.rank_score_changes[k] = pb.pb_RankMatchScoreChangeDecode(v)
    end
    local __rank_match_score = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __rank_match_score ~= 0 then tb.rank_match_score = __rank_match_score end
    local __rank_match_score_delta = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __rank_match_score_delta ~= 0 then tb.rank_match_score_delta = __rank_match_score_delta end
    return tb
end

function pb.pb_CSSeasonGetMatchScoreDataResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.rank_score_changes) then
        for i=1,#(tb.rank_score_changes) do
            pb.pb_RankMatchScoreChangeEncode(tb.rank_score_changes[i], encoder:addsubmsg(2))
        end
    end
    if(tb.rank_match_score) then    encoder:addi64(3, tb.rank_match_score)    end
    if(tb.rank_match_score_delta) then    encoder:addi64(4, tb.rank_match_score_delta)    end
end

function pb.pb_CSSeasonGetMatchKillDataReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSeasonGetMatchKillDataReq) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __room_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    return tb
end

function pb.pb_CSSeasonGetMatchKillDataReqEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.room_id) then    encoder:addu64(2, tb.room_id)    end
end

function pb.pb_CSSeasonGetMatchKillDataResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSeasonGetMatchKillDataRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.kill_array = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.kill_array[k] = pb.pb_GspPlayerKillInfoDecode(v)
    end
    return tb
end

function pb.pb_CSSeasonGetMatchKillDataResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.kill_array) then
        for i=1,#(tb.kill_array) do
            pb.pb_GspPlayerKillInfoEncode(tb.kill_array[i], encoder:addsubmsg(2))
        end
    end
end

function pb.pb_CSSeasonGetMatchGainedDataReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSeasonGetMatchGainedDataReq) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __room_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    return tb
end

function pb.pb_CSSeasonGetMatchGainedDataReqEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.room_id) then    encoder:addu64(2, tb.room_id)    end
end

function pb.pb_CSSeasonGetMatchGainedDataResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSeasonGetMatchGainedDataRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.carry_out_props = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.carry_out_props[k] = pb.pb_EquipPositionDecode(v)
    end
    tb.carry_out_health_list = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.carry_out_health_list[k] = pb.pb_EquipHealthDecode(v)
    end
    local __carry_out_profit_price = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __carry_out_profit_price ~= 0 then tb.carry_out_profit_price = __carry_out_profit_price end
    local __blue_print_special_id = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __blue_print_special_id ~= 0 then tb.blue_print_special_id = __blue_print_special_id end
    local __blue_print_type = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __blue_print_type ~= 0 then tb.blue_print_type = __blue_print_type end
    local __blue_print_price = decoder:geti64(7)
    if not PB_USE_DEFAULT_TABLE or __blue_print_price ~= 0 then tb.blue_print_price = __blue_print_price end
    local __contract_quest_price = decoder:geti64(8)
    if not PB_USE_DEFAULT_TABLE or __contract_quest_price ~= 0 then tb.contract_quest_price = __contract_quest_price end
    local __carry_out_new_props_price = decoder:geti64(9)
    if not PB_USE_DEFAULT_TABLE or __carry_out_new_props_price ~= 0 then tb.carry_out_new_props_price = __carry_out_new_props_price end
    local __friend_add_exp_buf = decoder:getdouble(10)
    if not PB_USE_DEFAULT_TABLE or __friend_add_exp_buf ~= 0 then tb.friend_add_exp_buf = __friend_add_exp_buf end
    local __safehouse_add_exp_buf = decoder:getdouble(11)
    if not PB_USE_DEFAULT_TABLE or __safehouse_add_exp_buf ~= 0 then tb.safehouse_add_exp_buf = __safehouse_add_exp_buf end
    local __cost_price = decoder:geti32(12)
    if not PB_USE_DEFAULT_TABLE or __cost_price ~= 0 then tb.cost_price = __cost_price end
    local __safebox_skin_id = decoder:getu64(13)
    if not PB_USE_DEFAULT_TABLE or __safebox_skin_id ~= 0 then tb.safebox_skin_id = __safebox_skin_id end
    return tb
end

function pb.pb_CSSeasonGetMatchGainedDataResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.carry_out_props) then
        for i=1,#(tb.carry_out_props) do
            pb.pb_EquipPositionEncode(tb.carry_out_props[i], encoder:addsubmsg(2))
        end
    end
    if(tb.carry_out_health_list) then
        for i=1,#(tb.carry_out_health_list) do
            pb.pb_EquipHealthEncode(tb.carry_out_health_list[i], encoder:addsubmsg(3))
        end
    end
    if(tb.carry_out_profit_price) then    encoder:addi32(4, tb.carry_out_profit_price)    end
    if(tb.blue_print_special_id) then    encoder:addu64(5, tb.blue_print_special_id)    end
    if(tb.blue_print_type) then    encoder:addi32(6, tb.blue_print_type)    end
    if(tb.blue_print_price) then    encoder:addi64(7, tb.blue_print_price)    end
    if(tb.contract_quest_price) then    encoder:addi64(8, tb.contract_quest_price)    end
    if(tb.carry_out_new_props_price) then    encoder:addi64(9, tb.carry_out_new_props_price)    end
    if(tb.friend_add_exp_buf) then    encoder:adddouble(10, tb.friend_add_exp_buf)    end
    if(tb.safehouse_add_exp_buf) then    encoder:adddouble(11, tb.safehouse_add_exp_buf)    end
    if(tb.cost_price) then    encoder:addi32(12, tb.cost_price)    end
    if(tb.safebox_skin_id) then    encoder:addu64(13, tb.safebox_skin_id)    end
end

function pb.pb_CSSeasonRankRecvAwardReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSeasonRankRecvAwardReq) or {} 
    local __mode = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __mode ~= 0 then tb.mode = __mode end
    local __season = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __season ~= 0 then tb.season = __season end
    tb.levels = decoder:getu32ary(3)
    return tb
end

function pb.pb_CSSeasonRankRecvAwardReqEncode(tb, encoder)
    if(tb.mode) then    encoder:addu32(1, tb.mode)    end
    if(tb.season) then    encoder:addu32(2, tb.season)    end
    if(tb.levels) then    encoder:addu32(3, tb.levels)    end
end

function pb.pb_CSSeasonRankRecvAwardResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSeasonRankRecvAwardRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.data_change = pb.pb_DataChangeDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSSeasonRankRecvAwardResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.data_change) then    pb.pb_DataChangeEncode(tb.data_change, encoder:addsubmsg(2))    end
end

function pb.pb_CSSeasonCommanderRecvAwardReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSeasonCommanderRecvAwardReq) or {} 
    local __season = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __season ~= 0 then tb.season = __season end
    tb.levels = decoder:getu32ary(3)
    return tb
end

function pb.pb_CSSeasonCommanderRecvAwardReqEncode(tb, encoder)
    if(tb.season) then    encoder:addu32(1, tb.season)    end
    if(tb.levels) then    encoder:addu32(3, tb.levels)    end
end

function pb.pb_CSSeasonCommanderRecvAwardResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSeasonCommanderRecvAwardRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.data_change = pb.pb_DataChangeDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSSeasonCommanderRecvAwardResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.data_change) then    pb.pb_DataChangeEncode(tb.data_change, encoder:addsubmsg(2))    end
end

function pb.pb_CSSeasonRankScoreChangedNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSeasonRankScoreChangedNtf) or {} 
    local __mode = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __mode ~= 0 then tb.mode = __mode end
    local __score = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __score ~= 0 then tb.score = __score end
    local __score_old = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __score_old ~= 0 then tb.score_old = __score_old end
    local __score_shoot = decoder:geti64(10)
    if not PB_USE_DEFAULT_TABLE or __score_shoot ~= 0 then tb.score_shoot = __score_shoot end
    local __score_tactics = decoder:geti64(11)
    if not PB_USE_DEFAULT_TABLE or __score_tactics ~= 0 then tb.score_tactics = __score_tactics end
    local __score_vehicle = decoder:geti64(12)
    if not PB_USE_DEFAULT_TABLE or __score_vehicle ~= 0 then tb.score_vehicle = __score_vehicle end
    return tb
end

function pb.pb_CSSeasonRankScoreChangedNtfEncode(tb, encoder)
    if(tb.mode) then    encoder:addi32(1, tb.mode)    end
    if(tb.score) then    encoder:addi64(2, tb.score)    end
    if(tb.score_old) then    encoder:addi64(3, tb.score_old)    end
    if(tb.score_shoot) then    encoder:addi64(10, tb.score_shoot)    end
    if(tb.score_tactics) then    encoder:addi64(11, tb.score_tactics)    end
    if(tb.score_vehicle) then    encoder:addi64(12, tb.score_vehicle)    end
end

function pb.pb_CSSeasonGetMallPropsReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSeasonGetMallPropsReq) or {} 
    local __mode = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __mode ~= 0 then tb.mode = __mode end
    local __page_no = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __page_no ~= 0 then tb.page_no = __page_no end
    local __page_sz = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __page_sz ~= 0 then tb.page_sz = __page_sz end
    return tb
end

function pb.pb_CSSeasonGetMallPropsReqEncode(tb, encoder)
    if(tb.mode) then    encoder:addu32(1, tb.mode)    end
    if(tb.page_no) then    encoder:addu32(3, tb.page_no)    end
    if(tb.page_sz) then    encoder:addu32(4, tb.page_sz)    end
end

function pb.pb_RankMatchMallPropDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RankMatchMallProp) or {} 
    local __id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __season_no = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __season_no ~= 0 then tb.season_no = __season_no end
    local __prop_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __prop_id ~= 0 then tb.prop_id = __prop_id end
    local __prop_name = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __prop_name ~= "" then tb.prop_name = __prop_name end
    local __sol_unlock_minorid = decoder:getu64(6)
    if not PB_USE_DEFAULT_TABLE or __sol_unlock_minorid ~= 0 then tb.sol_unlock_minorid = __sol_unlock_minorid end
    local __refresh_interval = decoder:getu32(7)
    if not PB_USE_DEFAULT_TABLE or __refresh_interval ~= 0 then tb.refresh_interval = __refresh_interval end
    local __limit = decoder:geti64(8)
    if not PB_USE_DEFAULT_TABLE or __limit ~= 0 then tb.limit = __limit end
    local __price = decoder:geti64(9)
    if not PB_USE_DEFAULT_TABLE or __price ~= 0 then tb.price = __price end
    local __weight = decoder:getu32(10)
    if not PB_USE_DEFAULT_TABLE or __weight ~= 0 then tb.weight = __weight end
    local __need_reddot = decoder:getbool(11)
    if not PB_USE_DEFAULT_TABLE or __need_reddot ~= false then tb.need_reddot = __need_reddot end
    local __bought_num = decoder:geti64(20)
    if not PB_USE_DEFAULT_TABLE or __bought_num ~= 0 then tb.bought_num = __bought_num end
    return tb
end

function pb.pb_RankMatchMallPropEncode(tb, encoder)
    if(tb.id) then    encoder:addu64(1, tb.id)    end
    if(tb.season_no) then    encoder:addu64(2, tb.season_no)    end
    if(tb.prop_id) then    encoder:addu64(3, tb.prop_id)    end
    if(tb.prop_name) then    encoder:addstr(4, tb.prop_name)    end
    if(tb.sol_unlock_minorid) then    encoder:addu64(6, tb.sol_unlock_minorid)    end
    if(tb.refresh_interval) then    encoder:addu32(7, tb.refresh_interval)    end
    if(tb.limit) then    encoder:addi64(8, tb.limit)    end
    if(tb.price) then    encoder:addi64(9, tb.price)    end
    if(tb.weight) then    encoder:addu32(10, tb.weight)    end
    if(tb.need_reddot) then    encoder:addbool(11, tb.need_reddot)    end
    if(tb.bought_num) then    encoder:addi64(20, tb.bought_num)    end
end

function pb.pb_CSSeasonGetMallPropsResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSeasonGetMallPropsRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __coins = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __coins ~= 0 then tb.coins = __coins end
    tb.props = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.props[k] = pb.pb_RankMatchMallPropDecode(v)
    end
    local __total_pages = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __total_pages ~= 0 then tb.total_pages = __total_pages end
    return tb
end

function pb.pb_CSSeasonGetMallPropsResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.coins) then    encoder:addi32(2, tb.coins)    end
    if(tb.props) then
        for i=1,#(tb.props) do
            pb.pb_RankMatchMallPropEncode(tb.props[i], encoder:addsubmsg(3))
        end
    end
    if(tb.total_pages) then    encoder:addu32(4, tb.total_pages)    end
end

function pb.pb_CSSeasonBuyMallPropsReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSeasonBuyMallPropsReq) or {} 
    local __mode = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __mode ~= 0 then tb.mode = __mode end
    local __id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __num = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __num ~= 0 then tb.num = __num end
    return tb
end

function pb.pb_CSSeasonBuyMallPropsReqEncode(tb, encoder)
    if(tb.mode) then    encoder:addu32(1, tb.mode)    end
    if(tb.id) then    encoder:addu64(3, tb.id)    end
    if(tb.num) then    encoder:addu32(4, tb.num)    end
end

function pb.pb_CSSeasonBuyMallPropsResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSeasonBuyMallPropsRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __left_coins = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __left_coins ~= 0 then tb.left_coins = __left_coins end
    local __bought_times = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __bought_times ~= 0 then tb.bought_times = __bought_times end
    return tb
end

function pb.pb_CSSeasonBuyMallPropsResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.left_coins) then    encoder:addi64(2, tb.left_coins)    end
    if(tb.bought_times) then    encoder:addi64(3, tb.bought_times)    end
end

function pb.pb_CSSeasonLoadTotalDataReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSeasonLoadTotalDataReq) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    return tb
end

function pb.pb_CSSeasonLoadTotalDataReqEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
end

function pb.pb_CSSeasonLoadTotalDataResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSeasonLoadTotalDataRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.info = pb.pb_PlayerSimpleInfoDecode(decoder:getsubmsg(2))
    local __total_fight = decoder:getu32(100)
    if not PB_USE_DEFAULT_TABLE or __total_fight ~= 0 then tb.total_fight = __total_fight end
    local __kd = decoder:getfloat(101)
    if not PB_USE_DEFAULT_TABLE or __kd ~= 0 then tb.kd = __kd end
    local __total_escaped = decoder:getu32(102)
    if not PB_USE_DEFAULT_TABLE or __total_escaped ~= 0 then tb.total_escaped = __total_escaped end
    local __avg_survival_time = decoder:getu64(103)
    if not PB_USE_DEFAULT_TABLE or __avg_survival_time ~= 0 then tb.avg_survival_time = __avg_survival_time end
    local __max_escape_streak = decoder:getu32(104)
    if not PB_USE_DEFAULT_TABLE or __max_escape_streak ~= 0 then tb.max_escape_streak = __max_escape_streak end
    local __total_game_time = decoder:getu64(105)
    if not PB_USE_DEFAULT_TABLE or __total_game_time ~= 0 then tb.total_game_time = __total_game_time end
    return tb
end

function pb.pb_CSSeasonLoadTotalDataResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.info) then    pb.pb_PlayerSimpleInfoEncode(tb.info, encoder:addsubmsg(2))    end
    if(tb.total_fight) then    encoder:addu32(100, tb.total_fight)    end
    if(tb.kd) then    encoder:addfloat(101, tb.kd)    end
    if(tb.total_escaped) then    encoder:addu32(102, tb.total_escaped)    end
    if(tb.avg_survival_time) then    encoder:addu64(103, tb.avg_survival_time)    end
    if(tb.max_escape_streak) then    encoder:addu32(104, tb.max_escape_streak)    end
    if(tb.total_game_time) then    encoder:addu64(105, tb.total_game_time)    end
end

