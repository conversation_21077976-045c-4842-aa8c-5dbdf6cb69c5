----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRoleInfo)
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
----- LOG FUNCTION AUTO GENERATE END -----------
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
local HeroHelperTool = require "DFM.StandaloneLua.BusinessTool.HeroHelperTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local SocialChangeBadge = ui("SocialChangeBadge")
---@class SocialChangeBadge : LuaUIBaseView

--- BEGIN MODIFICATION @ VIRTUOS
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import "EGPUINavWidgetFocusedAction"
--- END MODIFICATION

function SocialChangeBadge:Ctor()
    self._wtSocialImage = self:Wnd("wtSocialImage", UIImage)
    self._wtNameLabel = self:Wnd("wtNameLabel", UITextBlock)
    self._wtAvatarDescription = self:Wnd("wtAvatarDescription", UITextBlock)
    self._wtCommonHoverBg = self:Wnd("WBP_CommonHoverBg_V2", UIWidgetBase)

    self._wtChangeBtn = self:Wnd("wtChangeBtn", UIWidgetBase)
    self._wtChangeBtn:Event("OnClicked", self._OnFitClick, self)

    self.wtJumpBtn = self:Wnd("wtJumpBtn", UIWidgetBase)
    self.wtJumpBtn:Event("OnClicked", self._OnJumpClick, self)
    self.wtDemountBtn = self:Wnd("wtCommonButtonV1S2", UIWidgetBase)
    self.wtDemountBtn:Event("OnClicked", self._OnDemountClick, self)


    self._wtSocialList = UIUtil.WndScrollGridBox(self, "wtSocialList", self.OnGetItemCount, self._OnProcessWidget)
    self._wtQualityIcon = self:Wnd("wtQualityIcon", UIImage)
    self._wtHeroIcon = self:Wnd("DFImage_171", UIImage)
    self._wtHeroBussinessCard = self:Wnd("WBP_Common_CardS2", HeroBussinessCard)
    self._wtDesc = self:Wnd("wtAvatarDescription", UITextBlock)

    self._wtChangeHeroBtn = self:Wnd("DFButton_0", UIWidgetBase)
    self._wtChangeHeroBtn:Event("OnClicked", self._OnChangeHeroClick, self)
    self._wtHeroImage = self:Wnd("DFImage_171", UIImage)
    self._wtHeroDetailsBtn = self:Wnd("WBP_CommonIconButton", DFCommonButtonOnly)
    self._wtHeroDetailsBtn:Event("OnClicked", self._OnSyncBadgeClick, self)

    --- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self._wtChangeBtn:SetDisplayInputAction("Confirm", false, nil, true)
        self.wtDemountBtn:SetDisplayInputAction("Confirm", false, nil, true)
    end
    --- END MODIFICATION

    self._wtButtonDesc = self:Wnd("wtButtonDesc", UITextBlock)
    self._wtDescImage = self:Wnd("wtDescImage", UIImage)
    self._wtUnlockCondition = self:Wnd("wtUnlockCondition", UIWidgetBase)

    self._wtSelectWidget = nil
    self._wtUsingWidget = nil
    self._selectId = nil
    self._selectInfo = nil
    self._index = 1

    self._heroID = 0
    self._badgeID = 0
    self._badgeTable = {}
end

function SocialChangeBadge:InitAchievementTable()
    local t = Facade.TableManager:GetTable("HZAchievement")
    self._badgeTable = {}

    for i, badgeInfo in pairs(t) do
        local badgeId = nil
        for _, id in pairs(badgeInfo.Badgeid) do
            if badgeInfo.Model == 1 or badgeInfo.Model == 2 then
                if Server.HeroServer:IsAccessoryUnlocked("88000000030", id) then
                    badgeId = id
                end
            elseif badgeInfo.Model == 3 then
                if Server.HeroServer:IsAccessoryUnlocked(tostring(self._heroID), id) then
                    badgeId = id
                end
            end
        end
        if badgeId then
            table.insert(self._badgeTable, badgeInfo)
        end
    end
end

function SocialChangeBadge:OnInitExtraData(SocialType)
    self._SocialType = SocialType
    self._selectId = 0
    self._selfSocialId = 0

    if self._heroID == 0 then
        self._heroID = Module.Hero:GetCurShowHeroId()
        Module.RoleInfo:SetSocialHeroId(self._heroID)
    end

    self:InitAchievementTable()
end

function SocialChangeBadge:InitHeroCard()
    -- 徽章界面初始化干员名片数据
    Module.Hero:ShowHeroById(self._heroID)
    self._wtHeroBussinessCard:OnBadgePanelInitCardData(self._heroID)
    self._wtHeroBussinessCard:RemoveBadgeTipsEvent()
    self._wtHeroBussinessCard:UpdateTitleInfo(nil, Server.RoleInfoServer.titleId, Server.RoleInfoServer.titleAdcode,
        Server.RoleInfoServer.titleRankNo)
end

function SocialChangeBadge:sortBagdgeTable()
    local listSortFunc = function(a, b)
        local aUnlock = Server.HeroServer:IsAccessoryUnlocked(self._heroID, a.BadgeId[1])
        local bUnlock = Server.HeroServer:IsAccessoryUnlocked(self._heroID, b.BadgeId[1])

        if aUnlock and not bUnlock then
            return true
        elseif bUnlock and not aUnlock then
            return false
        else
            return a.Order < b.Order
        end
    end
    table.sort(self._badgeTable, listSortFunc)
end

function SocialChangeBadge:OnShowBegin()
    self:sortBagdgeTable()
    if Module.RoleInfo.Field:GetSocialBadgeId() == -1 then
        Module.RoleInfo.Field:SetSocialBadgeId(self._badgeTable[1].ID)
    end

    self:Refresh()

    --- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self:_InitGamepadInputs()
    end
    --- END MODIFICATION
end

function SocialChangeBadge:Refresh()
    self:InitHeroCard()
    self._wtSocialList:RefreshVisibleItems()
    self:RefreshSelect()
    self:RefreshHero()
end

function SocialChangeBadge:RefreshHero()
    local HeroDataTable = Facade.TableManager:GetTable("Hero/HeroData")
    local heroInfo = HeroDataTable[tostring(self._heroID)]
    if heroInfo then
        self._wtHeroImage:AsyncSetImagePath(heroInfo.Icon)
    end
end

function SocialChangeBadge:OnHideBegin()
    -- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self:_DisableGamepadInputs()
    end
    -- END MODIFICATION

    self:_ClearOpenedTopUI()
end

function SocialChangeBadge:OnOpen()
    self:AddLuaEvent(Module.RoleInfo.Config.Event.evtSocialBadgeSelectChange, self.RefreshSelect, self)
    self:AddLuaEvent(Module.RoleInfo.Config.Event.evtSocialBadgeHeroChange, self.RefreshHeroChange, self)
    self:AddLuaEvent(Module.RoleInfo.Config.Event.evtSocialBadgeSyncHero, self.SyncHero, self)
    self:AddLuaEvent(Server.HeroServer.Events.evtHeroAccessoryChange, self.OnHeroAccessoryChangen, self)

end

function SocialChangeBadge:RefreshHeroChange()
    self._heroID = Module.RoleInfo:GetSocialHeroId()
    self:InitAchievementTable()
    self:OnShowBegin()
end

function SocialChangeBadge:RefreshSelect()
    local id = Module.RoleInfo.Field:GetSocialBadgeId()
    local achieveTable = HeroHelperTool.GetHZAchievementDataTableRow(id)
    local badgeId = -1

    if achieveTable then
        badgeId = achieveTable.BadgeId[1]
        if achieveTable.Model == Module.Hero.Config.EBadgeType.Career then
            for _, id in ipairs(achieveTable.BadgeId) do
                if Server.HeroServer:IsAccessoryUnlocked(self._heroID, id) then
                    badgeId = id
                end
            end
        end
    end
    self._badgeId = badgeId
    local curAccessoryUIData = HeroHelperTool.GetHeroBadgeDataRow(badgeId)

    if curAccessoryUIData then
        self._wtNameLabel:SetText(curAccessoryUIData.BadgeName)
        self._wtNameLabel:PlayAnim_ComputeTextBlock()

        self._wtQualityIcon:AsyncSetImagePath(Module.Hero.Config.QualityIconMapping[curAccessoryUIData.BadgeLevel])
        self._wtQualityIcon:SetColorAndOpacity(ItemConfigTool.GetItemQualityLinearColor(curAccessoryUIData.BadgeLevel))
        -- 徽章描述
        if curAccessoryUIData.BadgeDescription then
            self._wtDesc:SetText(curAccessoryUIData.BadgeDescription)
        end
    end

    self:RefreshBtn(badgeId)
end

function SocialChangeBadge:RefreshBtn(badgeId)
    local unlock = Server.HeroServer:IsAccessoryUnlocked(self._heroID, badgeId)
    if unlock then
        local bUse = Server.HeroServer:IsAccessoryUsed(self._heroID, badgeId)
        if bUse then
            self._wtChangeBtn:Collapsed()
            self.wtDemountBtn:Visible()
            self.wtJumpBtn:Collapsed()
        else
            self._wtChangeBtn:Visible()
            self.wtDemountBtn:Collapsed()
            self.wtJumpBtn:Collapsed()
        end
        self._wtUnlockCondition:Collapsed()
    else
        self.wtJumpBtn:Visible()

        self._wtChangeBtn:Collapsed()
        self.wtDemountBtn:Collapsed()
    end
end

function SocialChangeBadge:OnClose()
    self:RemoveAllLuaEvent()
end

function SocialChangeBadge:OnDeactivate()
    self:_ClearOpenedTopUI()
end

function SocialChangeBadge:OnGetItemCount()
    return table.getkeynum(self._badgeTable)
end

function SocialChangeBadge:_OnProcessWidget(index, widget)
    local info = self._badgeTable[index + 1]
    if info and widget then
        widget:Refresh(info, self._heroID)
    end
end

function SocialChangeBadge:_OnFitClick()
    local slot
    for i = 0, 2 do
        slot = self._wtHeroBussinessCard:GetBadgeSlotByIndex(i)
        if not slot.badgeId then
            local operation = {}
            local item = ItemBase:NewIns(self._badgeId)
            operation.DefaultDragVisual = {}
            operation.DefaultDragVisual.item = item
            slot:OnDrop(nil, nil, operation)
            self._wtHeroBussinessCard:UpdateFirstEmptySlot()
            return
        end
    end

    LuaGlobalEvents.evtServerShowTip:Invoke(Module.RoleInfo.Config.Loc.BadgeFullTip)
end

function SocialChangeBadge:_OnJumpClick()
    Module.CommonBar:CheckTopTabGroup(4, true, 2)
end

function SocialChangeBadge:SyncHero()
    local itemIdMap = {}
    local slot
    for i = 0, 2 do
        slot = self._wtHeroBussinessCard:GetBadgeSlotByIndex(i)
        if not slot.badgeId then
            itemIdMap[i] = 0
        else
            itemIdMap[i] = slot.badgeId
        end
    end

    local ids = Module.RoleInfo.Field:GetSocialHeroSyncIds()
    for key, id in pairs(ids) do
        if id ~= self._heroID then
            local heroDressMap = {}

            -- 找当前穿的
            local curAccessoryDatas = Server.HeroServer:GetUsedAccessory(tostring(id), EHeroAccessroy.Badge)
            local curAccessoryItemDatas = {}
            for k, v in ipairs(curAccessoryDatas) do
                table.insert(curAccessoryItemDatas, v.item)
            end

            for k, v in ipairs(curAccessoryItemDatas) do
                if v.slot and v.slot >= 0 and v.slot <= 2 then
                    heroDressMap[v.slot] = v.prop_id
                end
            end

            for i = 0, 2 do
                local itemId = 0
                if itemIdMap[i] <= 0 then --脱
                    itemId = heroDressMap[i]
                else
                    itemId = itemIdMap[i]
                end

                if itemId and itemId > 0 then
                    Server.HeroServer:DoUseAccessory(tostring(id), itemId, i, itemIdMap[i] <= 0) -- 脱装备要传当前穿的 id
                end
            end
        end
    end
    ids = {}
    LuaGlobalEvents.evtServerShowTip:Invoke(Module.RoleInfo.Config.Loc.BadgeSyncTip)
end

function SocialChangeBadge:_OnSyncBadgeClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.BadgeSyncHero)
end

function SocialChangeBadge:_OnDemountClick()
    local slot
    for i = 0, 2 do
        slot = self._wtHeroBussinessCard:GetBadgeSlotByIndex(i)
        if self._badgeId == slot.badgeId then
            slot:_Unequipped()
            self._wtHeroBussinessCard:UpdateFirstEmptySlot()
            break
        end
    end
end

function SocialChangeBadge:_OnChangeHeroClick()
    self:_ClearOpenedTopUI()
    self:_EnterOpenedTopUI()
    local fOnHeroPanelCreateCallback = function(uins)
    end
    self._topUIHandle = Module.Hero:ShowHeroListPanel(fOnHeroPanelCreateCallback,
        CreateCallBack(self._OnTopUIHideBegin, self), 0, 3)
end

function SocialChangeBadge:SetSelectRenderOpacity(alpha)
    Module.CommonBar:SetTopRenderOpacity(alpha)

    self._wtSocialList:SetRenderOpacity(alpha)
    self._wtNameLabel:SetRenderOpacity(alpha)
    self._wtQualityIcon:SetRenderOpacity(alpha)
    self._wtDesc:SetRenderOpacity(alpha)
end

function SocialChangeBadge:_OnTopUIHideBegin()
    self._topUIHandle = nil
    self:SetSelectRenderOpacity(1)

    --- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self:_InitGamepadInputs()
    end
    --- END MODIFICATION
end

function SocialChangeBadge:_ClearOpenedTopUI()
    if self._topUIHandle then
        Facade.UIManager:CloseUIByHandle(self._topUIHandle)
    end
    self._topUIHandle = nil

    self:SetSelectRenderOpacity(1)
end

function SocialChangeBadge:_EnterOpenedTopUI()
    self:SetSelectRenderOpacity(0.3)
end

function SocialChangeBadge:OnHeroAccessoryChangen(heroId, accessoryId)
    if ItemHelperTool.GetMainTypeById(accessoryId) == EItemType.HeroAccessory and
        ItemHelperTool.GetSubTypeById(accessoryId) == EHeroAccessroy.Badge then
        self._wtHeroBussinessCard:InitBadgeInBadgeUI(self._heroID)
    end

    self._wtSocialList:RefreshVisibleItems()
    self:RefreshSelect()
end

--- BEGIN MODIFICATION @ VIRTUOS

function SocialChangeBadge:GamepadClick()
    if self._wtChangeBtn:IsVisible() then
        self:_OnFitClick()
    end

    if self.wtDemountBtn:IsVisible() then
        self:_OnDemountClick()
    end
end

function SocialChangeBadge:_InitGamepadInputs()
    if not self:IsVisible() then
        return
    end

    -- Informal fix to inputs missing issue caused by unexpected "OnShowBegin" calls from RoleInfoMainPanel and disordered lifecycle.
    self:_DisableGamepadInputs()

    if not self._navGroup then
        self._navGroup = WidgetUtil.RegisterNavigationGroup(self._wtSocialList, self, "Hittest")
        if self._navGroup then
            self._navGroup:AddNavWidgetToArray(self._wtSocialList)
            self._navGroup:SetScrollRecipient(self._wtSocialList)
            self._navGroup:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
        end
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)

        WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroup)
    end


    if not self._gamepadSummaryList then
        self._gamepadSummaryList =
        {
            { actionName = "Confirm", func = self.GamepadClick, caller = self, bUIOnly = false },
            { actionName = "Viewheros_Badge_Gamepad", func = self._OnChangeHeroClick, caller = self, bUIOnly = false,
                bHideIcon = false },

            { actionName = "BadgeSync", func = self._OnSyncBadgeClick, caller = self, bUIOnly = false,
                bHideIcon = false },

        }
        Module.CommonBar:SetBottomBarTempInputSummaryList(self._gamepadSummaryList, false, false)
    end
end

function SocialChangeBadge:_DisableGamepadInputs()
    if self._navGroup then
        WidgetUtil.RemoveNavigationGroup(self)
        self._navGroup = nil
    end
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)

    if self._gamepadSummaryList then
        Module.CommonBar:RecoverBottomBarInputSummaryList()
        self._gamepadSummaryList = nil
    end
end

--- END MODIFICATION

return SocialChangeBadge
