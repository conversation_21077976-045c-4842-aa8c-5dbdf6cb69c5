----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMBattlePass)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
-- END MODIFICATION

---@class BattlePassMain : LuaUIBaseView
local BattlePassMain = ui("BattlePassMain")
local BattlePassConfig = Module.BattlePass.Config
local BattlePassLogic = require "DFM.Business.Module.BattlePassModule.Logic.BattlePassLogic"
local ButtonIdConfig  = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.ButtonIdConfig"
local ButtonIdEnum = ButtonIdConfig.Enum
local ULuaSubsystem = import "LuaSubsystem"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local GameletLogic = require "DFM.Business.Module.GameletModule.Logic.GameletLogic"
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
local LiteCommonDownload =  require "DFM.Business.Module.LitePackageModule.UI.LiteCommonDownload"
local LocalizeTool = require "DFM.StandaloneLua.BusinessTool.LocalizeTool"
local UGPInputHelper = import("GPInputHelper")
local EGPInputType = import"EGPInputType"
local UIThemeUtil = require "DFM.YxFramework.Managers.UI.Util.UIThemeUtil"

--- 生命周期

function BattlePassMain:Ctor()
    --- 成员变量声明

    -- 控件引用
    --self._wtImgSeasonTitle = self:Wnd("DFImage_98", UIImage)  -- 赛季标题
    --self._wtTxtSeasonCount = self:Wnd("DFTextBlock", UITextBlock)  -- 第几赛季
    self._wtTxtSeasonDayLeft = self:Wnd("DFTextBlock_1", UITextBlock)  -- 赛季剩余天数
    self._wtTxtSeasonLevel = self:Wnd("DFTextBlock_226", UITextBlock)  -- 赛季等级
    
    self._wtTxtExpBonusDesc = self:Wnd("DFTextBlock_3", UITextBlock)  -- 赛季经验加成是否生效描述
    self._wtImgExpBonusArrow1 = self:Wnd("DFImage_8", UIImage)  -- 进度加成箭头1
    self._wtImgExpBonusArrow2 = self:Wnd("DFImage_9", UIImage)  -- 进度加成箭头2
    self._wtImgExpBonusArrow3 = self:Wnd("DFImage_10", UIImage)  -- 进度加成箭头3
    self._wtDFCommonProgressBar = self:Wnd("WBP_DFCommonProgressBarV1", DFCommonProgressBarOnly)
    if self._wtDFCommonProgressBar then
        self._wtExpProgressBar = self._wtDFCommonProgressBar.wtCommonProgressBar
        self._wtExpProgressBar:SetType(2)
    end
    self._wtTxtExpInfo = self:Wnd("richtext", UITextBlock)  -- 赛季经验经验信息
    self._wtBtnBuyLevel = self:Wnd("WBP_CommonIconButton_2", DFCommonButtonOnly)  -- 购买等级按钮
    self._wtBtnBuyLevel:Event("OnClicked", self._OnBtnBuyLevelClick, self)
    self._wtExpBonusTipsCheckBox = self:Wnd("wtCommonCheckInstruction", DFCheckBoxOnly) -- 当前赛季经验加成说明
    if DFHD_LUA == 1 then
        self._wtExpBonusTipsAnchor = UIUtil.WndTipsAnchor(self, "DFTipsAnchor_114", self.ShowExpBonusTips, self.HideExpBonusTips)
    else
        self._wtExpBonusTipsAnchor = self:Wnd("DFTipsAnchor_114", UIWidgetBase)
        self._wtExpBonusTipsCheckBox:Event("OnCheckStateChanged", self._OnExpBonusTipsCheckBoxStateChanged, self)
    end
    self._wtTxtExpLimitWeek = self:Wnd("DFTextBlock_166", UITextBlock)  -- 本周经验上限

    self._wtCanvasPack = self:Wnd("DFCanvasPanel_4", UIWidgetBase) -- 特惠礼包
    self._wtBtnBuyPack = self:Wnd("DFButton_60", UIButton)  -- 特惠礼包按钮
    self._wtPackTitle = self:Wnd("DFTextBlock_63", UITextBlock)  -- 特惠礼包标题
    self._wtBtnBuyPack:Event("OnClicked", self._OnBtnBuyPackClick, self)

    self._wtTxtRewardQuality = self:Wnd("DFTextBlock_99", UITextBlock)  -- 奖励道具品质
    self._wtImgQualityIcon = self:Wnd("Image_BpQualityIcon", UIImage)  -- 奖励道具品质图标
    self._wtTxtRewardName = self:Wnd("DFTextBlock_2", UITextBlock)  -- 奖励道具名
    self._wtBtnRewardDetail = self:Wnd("WBP_CommonIconButton", DFCommonButtonOnly)  -- 奖励道具详情按钮
    self._wtBtnRewardDetail:Event("OnClicked", self._OnBtnRewardDetailClick, self)
    self._wtTxtRewardMode = self:Wnd("DFTextBlock_140", UITextBlock)  -- 奖励道具属于的模式

    self._wtBtnAdvanceUnlock = self:Wnd("WBP_DFCommonButtonV1S3_C_0", DFCommonButtonOnly)  -- 解锁高级档案按钮
    self._wtBtnAdvanceUnlock:Event("OnClicked", self._OnBtnAdvanceUnlockClick, self)

    if Module.BattlePass.Field:GetThemeLinkage() == EThemeIDType.CrossOver then
        self._wtRewardWaterFallList = UIUtil.WndWaterfallScrollBox(
                self, "DFWaterfallScrollView_159", self._OnRewardWaterFallListGetItemCount, self._OnRewardWaterFallListProcessItemWidget, UIName2ID.BattlePassMainItemArknights, self.GetRewardListItemSize) -- 奖励列表，子UI
    else
        self._wtRewardWaterFallList = UIUtil.WndWaterfallScrollBox(
                self, "DFWaterfallScrollView_159", self._OnRewardWaterFallListGetItemCount, self._OnRewardWaterFallListProcessItemWidget, UIName2ID.BattlePassMainItem, self.GetRewardListItemSize) -- 奖励列表，子UI
    end
    self._wtRewardWaterFallList:Event('OnProcessItemsUpdateFinished', self.OnProcessItemsUpdateFinished, self)
    self._wtRewardWaterFallList.OnScrolling:Add(CreateCPlusCallBack(self._OnScrolling, self))
    self._wtKeyRewardIconSpeicalItem1  = self:Wnd("WBP_BattlePass_SpecialRewards_Item_C_0", UIWidgetBase)  -- 重点奖励图标1蓝图
    self._wtKeyRewardIcon1  = self._wtKeyRewardIconSpeicalItem1:Wnd("WBP_CommonItemTemplate", IVCommonItemTemplate)  -- 重点奖励图标1
    self._wtBottomLine1 = self._wtKeyRewardIconSpeicalItem1:Wnd("PlatformScaleBox_0", UIWidgetBase)  -- 专属奖励线
    self._wtKeyRewardIconSpeicalItem2  = self:Wnd("WBP_BattlePass_SpecialRewards_Item_C_1", UIWidgetBase)  -- 重点奖励图标2蓝图
    self._wtKeyRewardIcon2  = self._wtKeyRewardIconSpeicalItem2:Wnd("WBP_CommonItemTemplate", IVCommonItemTemplate)  -- 重点奖励图标2
    self._wtBottomLine2 = self._wtKeyRewardIconSpeicalItem2:Wnd("PlatformScaleBox_0", UIWidgetBase)  -- 专属奖励线
    self._wtTxtKeyRewardLevel = self:Wnd("DFTextBlock_155", UITextBlock)  -- 重点奖励等级文本

    self._wtImgPaste = self:Wnd("DFImage_1", UIImage)  -- 贴图

    self._wtPropImg_1 = self:Wnd("wtPropImg_1", DFCDNImage)
    self._wtPropImg_2 = self:Wnd("wtPropImg_2", DFCDNImage)
    self._wtPropImg_3 = self:Wnd("wtPropImg_3", DFCDNImage)
    self._wtPropImgBox_2 = self:Wnd("wtPropImgBox_2", DFCDNImage)
    self._wtPropImgPanel = self:Wnd("DFCanvasPanel_78", UIWidgetBase)

    self._wtTopCanvasPanel = self:Wnd("CanvasPanel_68", UIWidgetBase) -- 顶层面板
    
    self._wtSocialMilitaryTabPanel = self:Wnd("Panel_Base_01", UIWidgetBase) -- 军牌面板
    self._wtSocialMilitaryTabBase = self:Wnd("DFImage_01_Bg", UIImage)  -- 军牌底座
    self._wtSocialMilitaryTabPaste = self:Wnd("DFImage_01_Icon", UIImage)  -- 军牌贴图
    self._wtSocialCardPanel = self:Wnd("Panel_Base_02", UIWidgetBase) -- 名片面板
    self._wtSocialCardBase = self:Wnd("DFImage_02_Bg", UIImage)  -- 名片底座
    self._wtSocialCardPaste = self:Wnd("DFImage_02_Icon", UIImage)  -- 名片贴图
    self._wtSocialAvatarTabPanel = self:Wnd("Panel_Base_03", UIWidgetBase) -- 头像面板
    self._wtSocialAvatarTabBase = self:Wnd("DFImage_03_Bg", UIImage)  -- 头像底座
    self._wtSocialAvatarTabPaste = self:Wnd("DFImage_03_Icon", UIImage)  -- 头像贴图

    self._wtBtnPlayVideo = self:Wnd("WBP_CommonIconButton_1", DFCommonButtonOnly)  -- 播放视频按钮
    self._wtBtnPlayVideo:Event("OnClicked", self._OnBtnPlayVideoClick, self)

    --self._wtTitle = self:Wnd("WBP_BattlePass_Title", UIWidgetBase) -- 标题

    if Module.ExpansionPackCoordinator:IsSupportLitePackage() then
        self._wtCommonDownload = self:Wnd("wtCommonDownload", LiteCommonDownload)
    end
    if self._wtCommonDownload then
        self._wtCommonDownload:Collapsed()
    end

    self._wtExclusivePanel = self:Wnd("DFCanvasPanel_154", UIWidgetBase) -- 专属面板
    self._wtExclusiveTxt = self:Wnd("DFTextBlock_174", UITextBlock) -- 专属面板文本

    self._wtReferenceValueTxt = self:Wnd("DFRichTextBlock_61", UITextBlock) -- 参考价值文本

    -- 缓存数据
    self._iArgLevel = nil  -- 缓存初始化参数，跳转到哪一级
    self._iArgIndex = nil  -- 缓存初始化参数，跳转到哪一级的哪一个位置
    self._iLastVisibleRewardPos = -1  -- 最后一个可见的奖励的主界面下标位置，非完整列表位置
    self._iKeyRewardLevel = -1  -- 展示的关键奖励的等级，0表示没有下一个需要展示的等级奖励了
    self._tExpBonusTipsHandler = {}  -- 经验加成tips处理对象,{ "tTipsHandler" : handler object}
    self._iJumpPosition = 1  -- 初始化需要跳到的位置
    self._iSelectedPosition = 1  -- 初始化需要选中的位置
    self._iSelectedIndex = 0  -- 初始化需要选中的位置的第几个
    self._bFirstRefresh = false  -- 是否首次刷新初始化完成
    self._tLastClickItemTime = 0  -- 上一次点击奖励的时间
    self._bRefreshKeyRewardMutex = false  -- 刷新核心奖励同步锁，因为发现有可能多线程刷新同步奖励
    self._bNeedRefreshKeyReward = false  -- 刷新完成之后是否需要再次刷新
    self._bBehindRealMaxLevel = nil  -- 是否在真实最大等级之前
    self._tLastClickMoreItemTime = 0  -- 上一次点击更多按钮的时间
    
    --- 初始化逻辑
    
    -- 设置悬浮核心奖励大小
    self._wtKeyRewardIconSpeicalItem1:SetType(1)
    self._wtKeyRewardIconSpeicalItem2:SetType(1)
    if DFHD_LUA == 1 then
        self._wtKeyRewardIcon1:SetRootSize(332, 332)
        self._wtKeyRewardIcon2:SetRootSize(332, 332)
    else
        self._wtKeyRewardIcon1:SetRootSize(256, 256)
        self._wtKeyRewardIcon2:SetRootSize(256, 256)
    end

    -- 藏品图层隐藏
    self._wtPropImg_1:Collapsed()
    self._wtPropImg_2:Collapsed()
    self._wtPropImg_3:Collapsed()
    self._wtPropImgPanel:Collapsed()

    -- 道具详情按钮不展示
    self._wtBtnRewardDetail:Visible()

    -- 服务器数据未到之前，不展示
    self._wtTopCanvasPanel:Collapsed()
    
    self._scrollStopHandle = nil
    
    self.sLanguage = LocalizeTool.GetCurrentCulture()

    self._taskId = 0
end

-- UI构造时触发, 用于初始化外部传参UI数据逻辑，由UIManager触发（仅触发一次）
-- 子节点蓝图不会触发该方法
-- Module加载好子控件所需的图片后，将数据传入主UI界面
--- @param iLevel 当等级大于最大等级时，跳转到循环奖励
---@overload fun(LuaUIBaseView, OnInitExtraData)
function BattlePassMain:OnInitExtraData(iLevel, iIndex)
    loginfo("BattlePassMain:OnInitExtraData")
    
    self._iArgLevel = iLevel
    self._iArgIndex = iIndex

    if Module.BattlePass.Field:GetThemeLinkage() == EThemeIDType.CrossOver then
        self:SetArknights(true)
    else
        self:SetArknights(false)
    end
end

-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function BattlePassMain:OnOpen()
    loginfo("BattlePassMain:OnOpen")

    -- 注册监听事件
    self:AddListenersOpen()

    -- 添加屏幕变化监听事件
    ULuaSubsystem.Get().OnNotifyLuaResolutionResized:Add(self.RefreshUIKeyReward, self)
end

function BattlePassMain:AddListenersShow()
    -- 移除事件监听
    self:RemoveListenersShow()

    -- 添加事件监听
    self:AddLuaEvent(Server.BattlePassServer.Events.evtBattlePassInfoUpdate, self.RefreshUI, self)
    self:AddLuaEvent(BattlePassConfig.Events.evtBattlePassRewardItemClick, self._OnEventBattlePassRewardItemClick, self)
    self:AddLuaEvent(Module.Reward.Config.Events.evtShowReward, self._OnShowReward, self)
    self:AddMouseButtonDownEvent()
    self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult,self._OnDownloadStateChange,self)
    self:AddLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged, self._OnPackStateChanged, self)
end

-- 移除show监听的事件
function BattlePassMain:RemoveListenersShow()
    self:RemoveLuaEvent(Server.BattlePassServer.Events.evtBattlePassInfoUpdate)
    self:RemoveLuaEvent(BattlePassConfig.Events.evtBattlePassRewardItemClick)
    self:RemoveLuaEvent(Module.Reward.Config.Events.evtShowReward)
    self:RemoveMouseButtonDownEvent()
    self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult,self._OnDownloadStateChange,self)
    self:RemoveLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged, self._OnPackStateChanged, self)
end

-- 监听open开始的事件
function BattlePassMain:AddListenersOpen()
    -- 移除监听的事件 
    self:RemoveListenersOpen()

    -- 添加事件监听
    self:AddLuaEvent(Server.BattlePassServer.Events.evtBattlePassBuyLevelSuccessful, self.OnBattlePassBuyLevelSuccessful, self)
    self:AddLuaEvent(Server.BattlePassServer.Events.evtBattlePassUnlock, self.OnBattlePassUnlock, self)
end

-- 移除open监听的事件
function BattlePassMain:RemoveListenersOpen()
    self:RemoveLuaEvent(Server.BattlePassServer.Events.evtBattlePassBuyLevelSuccessful)
    self:RemoveLuaEvent(Server.BattlePassServer.Events.evtBattlePassUnlock)
end

-- UI监听事件、协议
function BattlePassMain:AddListeners()
    -- 普通事件
    -- self:AddLuaEvent(BattlePassConfig.evtMainPanelTest, self.OnMainPanelTest, self)
    -- ntf协议事件 ntfNameString
    -- Facade.ProtoManager:AddNtfListener("CSAuctionOrderChangeNtf", self.OnCSAuctionOrderChangeNtf, self)

    -- 移除事件监听
    self:RemoveAllLuaEvent()
end

function BattlePassMain:AddMouseButtonDownEvent()
    loginfo("BattlePassMain:AddMouseButtonDownEvent")
    local gameInst=GetGameInstance()
    if gameInst then
        self._mouseButtonDownHandle=UDFMGameHudDelegates.Get(GetGameInstance()).OnHandleMouseButtonDownEvent:Add(self._OnMouseButtonDown,self)
    end
end

function BattlePassMain:RemoveMouseButtonDownEvent()
    loginfo("BattlePassMain:RemoveMouseButtonDownEvent")
    if self._mouseButtonDownHandle then
        local gameInst=GetGameInstance()
        if gameInst then
            self._mouseButtonDownHandle=UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonDownEvent:Remove(self._OnMouseButtonDown)
        end
    end
end

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function BattlePassMain:OnClose()
    loginfo("BattlePassMain:OnClose")

    -- 添加屏幕变化监听事件
    ULuaSubsystem.Get().OnNotifyLuaResolutionResized:Remove(self.RefreshUIKeyReward, self)

    --Module.CDNIcon:ClearTexWithTag(Module.CDNIcon.Config.ECdnTagEnum.BattlePass)
    -- 移除组件
    --BattlePassLogic.ClearItemViewComp(self._wtKeyRewardIcon1)
    --BattlePassLogic.ClearItemViewComp(self._wtKeyRewardIcon2)
    
    -- 移除监听事件
    self:RemoveListenersOpen()

    -- 只在低内存设备卸载音频缓存池资源
    if Facade.UIManager:GetIsLowMemoryState() then  
		local UGPAudioStatics = import "GPAudioStatics"
		UGPAudioStatics.UnloadAllNonPlayingAudioEvents()
	end
end

function BattlePassMain:OnShow()
    loginfo("BattlePassMain:OnShow")
end

-- UI OnOpen后、调用Show()显示时触发
-- 做表现
---@overload fun(LuaUIBaseView, OnShow)
function BattlePassMain:OnShowBegin()
    loginfo("BattlePassMain:OnShowBegin")
    -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
    if IsHD() then
        self:_EnableGamepadFeature(true)
    end
    -- END MODIFICATION
    -- 判断是否当前正在展示该页签，不是则不处理    
    local iUINameID = Module.BattlePass.Field:GetCurrStackUINameID()
    if iUINameID ~= UIName2ID.BattlePassMain then
        return
    end
    
    -- 注册事件监听
    self:AddListenersShow()
    
    -- 如果不是首次刷新
    if self._bFirstRefresh then
        -- 展示UI
        self._wtTopCanvasPanel:Visible()

        -- 如果不是栈UI内第一次点击，可以处理
        if not Module.BattlePass.Field:IsStackFirstClickReward() then
            -- 抛出点击奖励条目事件
            local iLastPosition = Module.BattlePass.Field:GetLevelRewardLastSelectPosition()
            local iLastIndex = Module.BattlePass.Field:GetLevelRewardLastSelectIndex()
            Module.BattlePass.Config.Events.evtBattlePassRewardItemClick:Invoke(iLastPosition, iLastIndex)
        end
    end
    
    -- 拉取服务器数据
    Server.BattlePassServer:FetchServerData(true)

    -- 如果有服务器数据，直接刷新
    if Server.BattlePassServer:IsServerDataInfoInit() then
        -- 暂定每次等服务器回来再刷新数据
        --self:RefreshUI()
    end
    
    -- 播放入场动效
    self:PlayAnimation(self.WBP_BattlePass_Main_in_Lua, 0, 1, EUMGSequencePlayMode.Forward, 1, false)

    -- 绑定滑动列表点击急停
    self._scrollStopHandle = UIUtil.AddScrollBoxClickStopScroll(self._wtRewardWaterFallList, self)

    -- 播放循环任务晃动动效
    self:PlayAnimation(self.WBP_BattlePass_Main_loop01, 0, 1000000, EUMGSequencePlayMode.Forward, 1, false)

    --if self.sLanguage == "zh" then
    --    self._wtTitle:PlayAnimation(self._wtTitle.WBP_BattlePass_Title_cn_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    --    self._wtTitle:PlayAnimation(self._wtTitle.WBP_BattlePass_Title_cn_loop, 0, 10000001, EUMGSequencePlayMode.Forward, 1, false)
    --else
    --    self._wtTitle:PlayAnimation(self._wtTitle.WBP_BattlePass_Title_en_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    --    self._wtTitle:PlayAnimation(self._wtTitle.WBP_BattlePass_Title_en_loop, 0, 10000001, EUMGSequencePlayMode.Forward, 1, false)
    --end

    self._wtKeyRewardIconSpeicalItem1:StopAnimation(self._wtKeyRewardIconSpeicalItem1.WBP_BattlePass_SpecialRewards_Item_loop)
    self._wtKeyRewardIconSpeicalItem1:PlayAnimation(self._wtKeyRewardIconSpeicalItem1.WBP_BattlePass_SpecialRewards_Item_loop, 0, 0, EUMGSequencePlayMode.Forward, 1, true)
    self._wtKeyRewardIconSpeicalItem2:StopAnimation(self._wtKeyRewardIconSpeicalItem2.WBP_BattlePass_SpecialRewards_Item_loop)
    self._wtKeyRewardIconSpeicalItem2:PlayAnimation(self._wtKeyRewardIconSpeicalItem2.WBP_BattlePass_SpecialRewards_Item_loop, 0, 0, EUMGSequencePlayMode.Forward, 1, true)
end

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function BattlePassMain:OnHide()
    loginfo("BattlePassMain:OnHide")

    -- 隐藏经验加成Tips
    self._wtExpBonusTipsCheckBox:SetIsChecked(false)
    self:HideExpBonusTips()

    -- 隐藏贴图控件
    self:CollapseAllPasteImage()

    -- 关闭道具详情页
    Module.ItemDetail:CloseAllPopUI()

    -- 重置上次点击的时间
    self._tLastClickItemTime = 0
    self._tLastClickMoreItemTime = 0
    
    -- 调用场景展示结束函数
    BattlePassLogic.HideShowReward()

    -- 移除事件监听
    self:RemoveListenersShow()

     -- 移除滑动列表点击急停
    if self._scrollStopHandle then
		UIUtil.RemoveScrollBoxClickStopScroll(self._scrollStopHandle)
		self._scrollStopHandle = nil
	end

    -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
    if IsHD() then
        self:_EnableGamepadFeature(false)
    end
    -- END MODIFICATION

    self:StopExtraDelayCall()
end

function BattlePassMain:_OnShowReward(bIsShowing)
    if not bIsShowing then
        self:OnShowBegin()
    end
end

--- 界面刷新
function BattlePassMain:RefreshUI()
    loginfo("BattlePassMain RefreshUI")
    
    -- 如果赛季已经结束，不显示界面
    if BattlePassLogic.SeasonEndPrompt() then
        self._wtTopCanvasPanel:Collapsed()
        return
    end

    -- 非首次刷新处理
    if self._bFirstRefresh then
        -- 刷新奖励列表
        self._wtRewardWaterFallList:RefreshVisibleItems()

        -- 刷新核心奖励
        self:RefreshUIKeyReward()

        if self._iArgIndex ~= nil or self._iArgLevel ~= nil then
            -- 根据参数获取要跳转和要选择的奖励
            self.iSelectedPosition, self.iSelectedIndex, self.iJumpPosition = BattlePassLogic.GetMainFirstShowRewardPosByParam(self._iArgLevel, self._iArgIndex)

            -- 刷新列表是前半部分还是后半部分
            self._bBehindRealMaxLevel = self:IsServerPosBehindRealMaxLevel(self.iSelectedPosition)
            self._wtRewardWaterFallList:RefreshItemCount()
            self._wtRewardWaterFallList:RefreshAllItems()

            -- 经debug观察，列表是先滚动完成，然后后面慢慢刷新，增加延迟是因为如果和RefreshALL在一帧，偶先无法滚动
            local iRealMainPos = self:GetRewardListMainPosByServerPos(self.iJumpPosition)
            Timer.DelayCall(0.04, self.RewardWaterFallScrollToPosition, self, iRealMainPos)

            -- 选中奖励
            Module.BattlePass.Config.Events.evtBattlePassRewardItemClick:Invoke(self.iSelectedPosition, self.iSelectedIndex)

            -- 重置参数
            self._iArgLevel = nil
            self._iArgIndex = nil
        else
            if Module.BattlePass.Field:IsStackFirstClickReward() then
                -- 抛出点击奖励条目事件
                local iLastPosition = Module.BattlePass.Field:GetLevelRewardLastSelectPosition()
                local iLastIndex = Module.BattlePass.Field:GetLevelRewardLastSelectIndex()
                Module.BattlePass.Config.Events.evtBattlePassRewardItemClick:Invoke(iLastPosition, iLastIndex)

                -- 设置下一次不是栈UI内第一次点击
                Module.BattlePass.Field:SetStackFirstClickReward(false)
            end
        end
    end

    -- 首次刷新处理 
    if not self._bFirstRefresh then
        self:FirstRefresh()
        self._bFirstRefresh = true
    end

    -- 刷新高级档案按钮
    self:RefreshAdvanceBPButton()

    -- 刷新等级信息
    self:RefreshBPExp()

    -- 刷新购买等级按钮
    self:RefreshBuyLevelBtn()

    -- 刷新礼包信息
    self:RefreshPack()

    -- 刷新赛季主题信息
    self:RefreshSeasonTitle()
    
    -- 刷新视频播放按钮
    self:RefreshVideoBtn()

    -- 循环播放雷达动效
    self:PlayAnimation(self.WBP_BattlePass_Main_loop, 0, 1000000, EUMGSequencePlayMode.Forward, 1, false)
    
    -- 展示界面UI
    self._wtTopCanvasPanel:Visible()
    -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
    if IsHD() then
        self:_UpdateInputSummaryList()
    end 
    -- END MODIFICATION
end

-- 首次刷新处理
function BattlePassMain:FirstRefresh()
    -- 根据参数获取要跳转和要选择的奖励
    self.iSelectedPosition, self.iSelectedIndex, self.iJumpPosition = BattlePassLogic.GetMainFirstShowRewardPosByParam(self._iArgLevel, self._iArgIndex)
    if self._iArgLevel ~= nil and self._iArgIndex ~= nil then
        -- 刷新列表是前半部分还是后半部分
        self._bBehindRealMaxLevel = self:IsServerPosBehindRealMaxLevel(self.iSelectedPosition)
        self._wtRewardWaterFallList:RefreshItemCount()
        self._wtRewardWaterFallList:RefreshAllItems()
    else
        -- 刷新个数，目的是把之前没有服务器数据时的个数恢复正常
        self._wtRewardWaterFallList:RefreshItemCount()
        -- self._wtRewardWaterFallList:RefreshAllItems()
    end
    
    -- self.iJumpPosition = 150 -- 测试用
    -- 经debug观察，列表是先滚动完成，然后后面慢慢刷新，增加延迟是因为如果和RefreshALL在一帧，偶先无法滚动
    local iRealMainPos = self:GetRewardListMainPosByServerPos(self.iJumpPosition)
    Timer.DelayCall(0.01, self.RewardWaterFallScrollToPosition, self, iRealMainPos)

    -- 选中奖励
    Module.BattlePass.Config.Events.evtBattlePassRewardItemClick:Invoke(self.iSelectedPosition, self.iSelectedIndex)
    
    -- 重置参数
    self._iArgLevel = nil
    self._iArgIndex = nil
end

-- 刷新核心奖励
function BattlePassMain:RefreshUIKeyReward(bReIn)
    if not bReIn and self._bRefreshKeyRewardMutex then
        self._bNeedRefreshKeyReward = true
        loginfo("[BattlePassMain] RefreshUIKeyReward set _bNeedRefreshKeyReward true")
        return
    end

    -- 设置锁
    self._bRefreshKeyRewardMutex = true
    loginfo("[BattlePassMain] RefreshUIKeyReward set _bRefreshKeyRewardMutex true")

    local tReward = {}
    -- 获取该等级的奖励
    local iNextKeyRewardLevel = self:GetNextKeyRewardLevel()
    if iNextKeyRewardLevel ~= nil and iNextKeyRewardLevel > 0 then
        tReward = BattlePassLogic.GetMainShowRewardByLevel(iNextKeyRewardLevel)
        if not tReward then
            logerror("tKeyReward nil")
            return
        end
    end

    -- 核心奖励个数
    local iKeyRewardNum = 0
    for _ in pairs(tReward) do
        iKeyRewardNum = iKeyRewardNum + 1
    end

    -- 获取屏幕宽度
    local viewportGeometry = UE.WidgetLayoutLibrary.GetViewportWidgetGeometry(self)
    local viewportLocalSize = viewportGeometry:GetLocalSize()
    local iWidth = viewportLocalSize.X

    -- 根据核心奖励个数，设置悬浮核心奖励状态
    if DFHD_LUA == 1 then
        if iKeyRewardNum == 0 then
            self:SetPCType(0)
            self._wtRewardWaterFallList:ForceSetBottomBoundary(iWidth - 180 - 176)
        elseif iKeyRewardNum == 1 then
            self:SetPCType(1)
            self._wtRewardWaterFallList:ForceSetBottomBoundary(iWidth - 568 - 176)
        elseif iKeyRewardNum == 2 then
            self:SetPCType(2)
            self._wtRewardWaterFallList:ForceSetBottomBoundary(iWidth - 924 - 176)
        else
            self:SetPCType(0)
            self._wtRewardWaterFallList:ForceSetBottomBoundary(iWidth - 180 - 176)
        end
    else
        if iKeyRewardNum == 0 then
            self:SetMobileType(0)
            self._wtRewardWaterFallList:ForceSetBottomBoundary(iWidth - 32 - 270)
        elseif iKeyRewardNum == 1 then
            self:SetMobileType(1)
            self._wtRewardWaterFallList:ForceSetBottomBoundary(iWidth - 346 - 270)
        elseif iKeyRewardNum == 2 then
            self:SetMobileType(2)
            self._wtRewardWaterFallList:ForceSetBottomBoundary(iWidth - 624 - 270)
        else
            self:SetMobileType(0)
            self._wtRewardWaterFallList:ForceSetBottomBoundary(iWidth - 32 - 270)
        end
    end

    -- 刷新奖励列表
    self._wtRewardWaterFallList:RefreshVisibleItems()

    if iKeyRewardNum ~= 0 then
        -- 设置核心奖励等级
        local iKeyRewardLevel = self:GetNextKeyRewardLevel()
        self._wtTxtKeyRewardLevel:SetText(iKeyRewardLevel)

        -- 如果核心奖励等级等于当前赛季等级，则高亮
        local iLevel = Server.BattlePassServer:GetLevel()
        if iLevel == iKeyRewardLevel then
            -- 蓝图函数，0不高亮，1高亮
            if Module.BattlePass.Field:GetThemeLinkage() == EThemeIDType.CrossOver then
                self:SetArknightsCurrentType(1)
            else
                self:SetCurrentType(1)
            end
        else
            if Module.BattlePass.Field:GetThemeLinkage() == EThemeIDType.CrossOver then
                self:SetArknightsCurrentType(0)
            else
                self:SetCurrentType(0)
            end
        end
    end

    -- BP是否已经付费
    local AdvanceBPIsPaid = Server.BattlePassServer:IsPaid()

    -- 设置核心奖励的奖励内容
    if iKeyRewardNum == 1 then
        local _, tRewardItem = next(tReward)

        local itemData = ItemBase:New(tRewardItem.iID, tRewardItem.iCount)
        self._wtKeyRewardIcon1:InitItem(itemData)
        -- 设置item的组件
        self:RefreshKeyItemComponent(self._wtKeyRewardIcon1, tRewardItem.eType)
        -- 绑定点击处理
        self:BindKeyRewardClickCb(self._wtKeyRewardIcon1, iNextKeyRewardLevel, 0)
        -- 专属奖励线
        if tRewardItem.iIsExclusive == 1 then
            self._wtBottomLine1:SelfHitTestInvisible()
        else
            self._wtBottomLine1:Collapsed()
        end
    elseif iKeyRewardNum == 2 then
        local iKey, tRewardItem = next(tReward)

        local itemData = ItemBase:New(tRewardItem.iID, tRewardItem.iCount)
        self._wtKeyRewardIcon1:InitItem(itemData)
        -- 设置item的组件
        self:RefreshKeyItemComponent(self._wtKeyRewardIcon1, tRewardItem.eType)
        -- 绑定点击处理
        self:BindKeyRewardClickCb(self._wtKeyRewardIcon1, iNextKeyRewardLevel, 0)
        -- 专属奖励线
        if tRewardItem.iIsExclusive == 1 then
            self._wtBottomLine1:SelfHitTestInvisible()
        else
            self._wtBottomLine1:Collapsed()
        end

        _, tRewardItem = next(tReward, iKey)

        itemData = ItemBase:New(tRewardItem.iID, tRewardItem.iCount)
        self._wtKeyRewardIcon2:InitItem(itemData)
        -- 设置item的组件
        self:RefreshKeyItemComponent(self._wtKeyRewardIcon2, tRewardItem.eType)
        -- 绑定点击处理
        self:BindKeyRewardClickCb(self._wtKeyRewardIcon2, iNextKeyRewardLevel, 1)
        -- 专属奖励线
        if tRewardItem.iIsExclusive == 1 then
            self._wtBottomLine2:SelfHitTestInvisible()
        else
            self._wtBottomLine2:Collapsed()
        end
    end

    if self._bNeedRefreshKeyReward then
        self._bNeedRefreshKeyReward = false
        loginfo("[BattlePassMain] RefreshUIKeyReward set _bNeedRefreshKeyReward false")
        self:RefreshUIKeyReward(true)
    else
        -- 取消锁
        self._bRefreshKeyRewardMutex = false
        loginfo("[BattlePassMain] RefreshUIKeyReward set _bRefreshKeyRewardMutex false")
    end
end

-- 刷新右上角的道具信息
function BattlePassMain:RefreshRightTopItemInfo()
    local tReward = self:GetCurrReward()
    if tReward == nil then
        -- todo 如果道具无法获取，显示默认，后续根据策划要求处理
        return
    end
    
    -- 设置道具名
    local tItem = ItemBase:New(tReward.iID, tReward.iCount)
    self._wtTxtRewardName:SetText(tItem.name)
    
    -- 设置品质图标 
    if tItem.quality > 0 then
        self:SetQualityIconType(tItem.quality)
        self._wtImgQualityIcon:SetColorAndOpacity(BattlePassLogic.GetQualityIconColorByNum(tItem.quality))
        self._wtImgQualityIcon:SelfHitTestInvisible()
    else
        self._wtImgQualityIcon:Collapsed()
    end
    
    -- 设置道具类型
    if tReward.eType == EBattlePassRewardType.Pay then
        self:SetPriceType(1)
        self._wtTxtRewardQuality:SetText(BattlePassConfig.Loc.TopRightTxtPay)
    elseif tReward.eType == EBattlePassRewardType.SOL then
        self:SetPriceType(2)
        self._wtTxtRewardMode:SetText(BattlePassConfig.Loc.TopRightTxtSOL)
    elseif tReward.eType == EBattlePassRewardType.MP then
        self:SetPriceType(3)
        self._wtTxtRewardMode:SetText(BattlePassConfig.Loc.TopRightTxtMP)
    elseif tReward.eType == EBattlePassRewardType.Free then
        self:SetPriceType(0)
        self._wtTxtRewardQuality:SetText(BattlePassConfig.Loc.TopRightTxtNormal)
    else
        self:SetPriceType(0)
        self._wtTxtRewardQuality:SetText(BattlePassConfig.Loc.TopRightTxtNormal)
    end

    -- 如果是循环奖励，则设置循环奖励文本
    local iMaxLevel = Server.BattlePassServer:GetCurrMaxRewardLevel()  
    local iMaxPosition = Server.BattlePassServer:GetPositionByRewardLevel(iMaxLevel)
    local iLastPosition = Module.BattlePass.Field:GetLevelRewardLastSelectPosition()
    if iLastPosition > iMaxPosition then
        self._wtTxtRewardQuality:SetText(string.format(BattlePassConfig.Loc.TopRightTxtCircle, Server.BattlePassServer:GetLevelPerCycle()))
        self._wtTxtRewardMode:SetText(string.format(BattlePassConfig.Loc.TopRightTxtCircle, Server.BattlePassServer:GetLevelPerCycle()))
    end

    -- 设置专属文本
    self._wtExclusiveTxt:SetText(string.format(BattlePassConfig.Loc.ExclusiveSeason, Server.BattlePassServer:GetSeasonNum()))
    if tReward.iIsExclusive == 1 then
        self._wtExclusivePanel:SelfHitTestInvisible()
    else
        self._wtExclusivePanel:Collapsed()
    end

    -- 参考价值
    if tReward.iReferenceValue and tReward.iReferenceValue > 0 then
        local sRichTxtID = Module.Currency:GetRichTxtImgId(ECurrencyClientId.Tina)
        self._wtReferenceValueTxt:SetText(string.format(BattlePassConfig.Loc.ReferenceValue, sRichTxtID, tReward.iReferenceValue))
        self._wtReferenceValueTxt:SelfHitTestInvisible()
    else
        self._wtReferenceValueTxt:Collapsed()
    end
end

-- 刷新高级档案按钮
function BattlePassMain:RefreshAdvanceBPButton()
    local ePaid = Server.BattlePassServer:GetPayType()

    -- 是否展示
    if ePaid == BattlePassType.BATTLE_PASS_TYPE_UNIVERSAL then
        -- 已经付费至最高级，不展示高级档案按钮
        self._wtBtnAdvanceUnlock:Collapsed()
        if self._AdvanceUnlock then
            self:RemoveInputActionBinding(self._AdvanceUnlock)
            self._AdvanceUnlock = nil
        end
    else
        -- 未付费，展示高级档案按钮 
        self._wtBtnAdvanceUnlock:Visible()
    end
    
    -- 设置文本
    if ePaid == BattlePassType.BATTLE_PASS_TYPE_BASE then
        self._wtBtnAdvanceUnlock:SetMainTitle(BattlePassConfig.Loc.MainUnlockBtnNotPay)
    elseif ePaid == BattlePassType.BATTLE_PASS_TYPE_SOL or ePaid == BattlePassType.BATTLE_PASS_TYPE_MP then
        self._wtBtnAdvanceUnlock:SetMainTitle(BattlePassConfig.Loc.MainUnlockBtnPayMiddle)
    end
end

-- 刷新购买等级按钮
function BattlePassMain:RefreshBuyLevelBtn()
    local iMaxBuyLevel = Server.BattlePassServer:GetMaxBuyLevel()
    if iMaxBuyLevel <= 0 then
        self._wtBtnBuyLevel:Collapsed()
    else
        self._wtBtnBuyLevel:Visible()
    end
    self:_UpdateInputSummaryList()
end

-- 刷新等级信息
function BattlePassMain:RefreshBPExp()
    -- 设置当前等级
    local iLevel = Server.BattlePassServer:GetLevel()
    self._wtTxtSeasonLevel:SetText(iLevel)
    
    -- 设置经验进度
    local iCurrExp, iMaxExp = Server.BattlePassServer:GetExp()
    local fPercent = iCurrExp / iMaxExp
    self._wtExpProgressBar:SetPercent(fPercent)
    local sTxtExp = string.format('%d<customstyle color="C002" size="C002">/%d</>', iCurrExp, iMaxExp)
    self._wtTxtExpInfo:SetText(sTxtExp)
    self._wtTxtExpInfo:Visible()
    
    -- 进度加成提示
    if Server.BattlePassServer:IsExpBonusValid() then
        self._wtTxtExpBonusDesc:SetText(BattlePassConfig.Loc.ExpBonusTxtUp)
        -- 设置进度加成箭头显示
        self._wtImgExpBonusArrow1:Visible()
        self._wtImgExpBonusArrow2:Visible()
        self._wtImgExpBonusArrow3:Visible()
    else
        self._wtTxtExpBonusDesc:SetText(BattlePassConfig.Loc.ExpBonusTxtNormal)
        -- 设置进度加成箭头消失
        self._wtImgExpBonusArrow1:Collapsed()
        self._wtImgExpBonusArrow2:Collapsed()
        self._wtImgExpBonusArrow3:Collapsed()
    end
    
    -- 刷新本周经验上限
    self._wtTxtExpLimitWeek:SetText(string.format(BattlePassConfig.Loc.MainExpLimitWeek, 
            Server.BattlePassServer:GetExpWeek(), 
            Server.BattlePassServer:GetExpWeekLimit())) 
end

-- 刷新等级加速信息
function BattlePassMain:RefreshBPExpUp()
end

-- 刷新礼包信息
function BattlePassMain:RefreshPack()
    local bPackValid = Server.BattlePassServer:IsPackValid()
    local bPackBought = Server.BattlePassServer:IsPackBought(Server.BattlePassServer:GetPackID())
    if bPackValid and not bPackBought then
        self._wtCanvasPack:Visible()
    else
        self._wtCanvasPack:Collapsed() 
    end
    self._wtPackTitle:SetText(BattlePassConfig.Loc.SpecialGiftBPTopBarTitle)
end

-- 刷新赛季主题信息
function BattlePassMain:RefreshSeasonTitle()
    -- warning 这张图现在路径会自动替换，不需要程序设置
    -- 设置赛季主题
    --self._wtImgSeasonTitle:AsyncSetImagePath(Server.BattlePassServer:GetSeasonTitlePath())
    
    -- 设置中间副标题
    local iSeasonNum = Server.BattlePassServer:GetSeasonNum()
    --local sMidTitleSeasonNum = ""
    --if iSeasonNum < 10 then
    --    sMidTitleSeasonNum = "0"..tostring(iSeasonNum)
    --else
    --    sMidTitleSeasonNum = tostring(iSeasonNum) 
    --end
    --self._wtTxtSeasonCount:SetText(string.format(BattlePassConfig.Loc.SeasonMidTitle, sMidTitleSeasonNum))
    
    -- 设置赛季时间信息
    local iDayLeft = Server.BattlePassServer:GetSeasonLeftDay()
    self._wtTxtSeasonDayLeft:SetText(string.format(BattlePassConfig.Loc.SeasonDetailTitle, iDayLeft))
end

-- 刷新视频播放按钮
function BattlePassMain:RefreshVideoBtn()
    local sVideoName = Server.BattlePassServer:GetVideoRowName()
    if sVideoName == nil or sVideoName == "" then
        self._wtBtnPlayVideo:Collapsed()
    else
        self._wtBtnPlayVideo:Visible()
    end
end

--- 事件处理

function BattlePassMain:OnCloseBtnClick()
    --BattlePassLogic.CloseMainPanelProcess()
end

function BattlePassMain:OnMainPanelTest()
end

function BattlePassMain:OnCSAuctionOrderChangeNtf(ntf)
end

function BattlePassMain:_OnBtnBuyLevelClick()
    -- todo 测试用
    --Facade.UIManager:AsyncShowUI(UIName2ID.BattlePassSettlement, nil, nil, 68, 70, 2000, 7000, 1, 10000, true)
    --Facade.UIManager:AsyncShowUI(UIName2ID.BattlePassBuyBP, nil, nil, BattlePassType.BATTLE_PASS_TYPE_SOL)
    
    -- 判断赛季结束时间
    if BattlePassLogic.SeasonEndBuyPrompt() then
        return
    end

    -- 打开购买等级界面栈UI
    Facade.UIManager:AsyncShowUI(UIName2ID.BattlePassBuyLevel, nil, nil)

    -- 埋点上报
    LogAnalysisTool.SignButtonClicked(ButtonIdEnum.BPBuyLevel)
end

function BattlePassMain:_OnExpBonusTipsCheckBoxStateChanged(bCheck)
    if bCheck then
        self:ShowExpBonusTips() 
    else
        self:HideExpBonusTips() 
    end
end

function BattlePassMain:_OnBtnBuyPackClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.BattlePassSpecialGift, nil, nil, Server.BattlePassServer:GetPackID())

    -- 埋点上报
    LogAnalysisTool.SignButtonClicked(ButtonIdEnum.BPBuyPack)
end

function BattlePassMain:_OnBtnRewardDetailClick()
    local tReward = self:GetCurrReward()
    if tReward == nil then
        return
    end

    local itemData = ItemBase:NewIns(tReward.iID)
    
    -- 展示枪械皮肤详情
    if itemData.itemMainType == EItemType.WeaponSkin then
        local itemWeapon = nil
        itemWeapon = Server.CollectionServer:GetWeaponSkinIfExists(tReward.iID)
        if itemWeapon == nil then
            local propInfo = {
                id = tReward.iID,
                gid = 0,
                num = tReward.iCount
            }
            itemWeapon = ItemHelperTool.CreateItemByPropInfo(propInfo)
        end
        Module.Collection:ShowWeaponSkinDetailPage(itemWeapon)
    elseif itemData.itemMainType == EItemType.Fashion then
        Facade.UIManager:AsyncShowUI(UIName2ID.StaffLotteryAccessoriesPreview, nil, nil, tReward.iID)  
    else
        Module.ItemDetail:OpenItemDetailPanel(itemData, self._wtBtnRewardDetail)
    end
end

function BattlePassMain:_OnBtnPlayVideoClick()
    local fCb = function() end
    BattlePassLogic.PlayVideo(self, fCb)  
end

function BattlePassMain:_OnBtnAdvanceUnlockClick()
    --Module.Reward:OpenRewardPanel(ServerTipCode.GetItemTitle, nil, {ItemBase:New(88000000026), ItemBase:New(28050440002), ItemBase:New(38070040001), ItemBase:New(38050030037)}, nil, nil, nil, true)
    Facade.UIManager:AsyncShowUI(UIName2ID.BattlePassUnlock)
    
    -- 埋点上报
    LogAnalysisTool.SignButtonClicked(ButtonIdEnum.BPUnlock)
end

-- 动画结束回调
---@overload fun(LuaUIBaseView, OnAnimFinished)
function BattlePassMain:OnAnimFinished(anim)
end

function BattlePassMain:_OnRewardWaterFallListGetItemCount()
    if not Server.BattlePassServer:IsServerDataInfoInit() then
        -- 服务器数据还没到，不处理
        return 1
    end

    local iCount = self:GetRewardListItemCount()

    if iCount <= 0 then
        iCount = 1
    end

    return iCount;
end

function BattlePassMain:_OnRewardWaterFallListProcessItemWidget(iPosition, wtMainItem)
    if not Server.BattlePassServer:IsServerDataInfoInit() then
        -- 服务器数据还没到，不处理
        return
    end

    if not iPosition or not wtMainItem then
        logerror("[BattlePassMain] _OnRewardWaterFallListProcessItemWidget param nil")
        return
    end
    
    self:RefreshRewardListItem(iPosition, wtMainItem)
end

function BattlePassMain:_OnEventBattlePassRewardItemClick(iPosition, iIndex, bPlayerClick, bKeyRewardClick)
    -- 判断是否当前正在展示该页签，不是则不处理
    local iUINameID = Module.BattlePass.Field:GetCurrStackUINameID()
    if not bPlayerClick and iUINameID ~= UIName2ID.BattlePassMain then
        return
    end

    -- 关闭道具详情页
    Module.ItemDetail:CloseAllPopUI()

    -- 获取当前点击的奖励
    local wtItemClick = BattlePassLogic.GetMainRewardItemByPositionIndex(iPosition, iIndex)
    if not wtItemClick then
        logerror("_OnEventBattlePassRewardItemClick position", iPosition, "nil, no reward")
        return
    end
    
    ---- 判断奖励是否可以展示
    --if not BattlePassLogic.IsRewardCanShow(wtItemClick.iID) and bPlayerClick then
    --    return 
    --end

    -- 如果是关键奖励
    if bKeyRewardClick then
        -- 滚动奖励位置到屏幕中间
        Timer.DelayCall(0.04, self.RewardWaterFallScrollToPosition, self, iPosition)
    end 
    
    -- 设置条目选中态
    self:SetItemSelectedByPositionIndex(iPosition, iIndex)

    -- 刷新右上角道具信息
    self:RefreshRightTopItemInfo()

    -- 获取当前点击的奖励
    local tReward = self:GetCurrReward()
    if tReward ~= nil then
        -- 设置上一次点击的奖励ID
        Module.BattlePass.Field:SetMainLastItemID(tReward.iID)

        -- 展示奖励
        BattlePassLogic.ShowReward(tReward.iID, UIName2ID.BattlePassMain, self, bPlayerClick)
    end
end

function BattlePassMain:_OnMouseButtonDown(mouseEvent)
    local screenPosition=mouseEvent:GetScreenSpacePosition()
    local seasonDescBtnGeometry=self._wtExpBonusTipsCheckBox:GetCachedGeometry()
    local isUnderSeasonDescBtn=USlateBlueprintLibrary.IsUnderLocation(seasonDescBtnGeometry, screenPosition)
    if not isUnderSeasonDescBtn then
        self:HideExpBonusTips()
    end
end

function BattlePassMain:OnBattlePassBuyLevelSuccessful(res, bAddCircle)
end

function BattlePassMain:OnProcessItemsUpdateFinished()
    -- 之前最后一个可见的位置
    local iLastVisibleRewardPosOld = self._iLastVisibleRewardPos
    -- 获取最后一个可见的位置
    self._iLastVisibleRewardPos = self._wtRewardWaterFallList:GetLastVisibleIdx(0)
    
    -- 更新右侧两个核心奖励
    if iLastVisibleRewardPosOld ~= self._iLastVisibleRewardPos then
        if Server.BattlePassServer:IsServerDataInfoInit() then
            -- 服务器数据到了，才处理
            self:CheckAndUpdateKeyReward()
        end

        if self.mLastScroll2Position then
            self:StartExtraDelayCall(self.mLastScroll2Position)
            self.mLastScroll2Position = nil
        end
    end
end

function BattlePassMain:OnBattlePassUnlock(eType, iLevelBeforeUnlock, eOldType)
    -- 刷新奖励
    self._wtRewardWaterFallList:RefreshItemCount()
    self._wtRewardWaterFallList:RefreshAllItems()

    -- 跳转到上一次点击的位置
    local iLastPosition = Module.BattlePass.Field:GetLevelRewardLastSelectPosition()
    Timer.DelayCall(0.01, self.RewardWaterFallScrollToPosition, self, iLastPosition)
end

function BattlePassMain:_OnPackStateChanged(moduleName, packState)
    local isSuccess = packState == Module.ExpansionPackCoordinator.Config.PackState.Installed
    self:_OnDownloadStateChange(moduleName, isSuccess, 0)
end

function BattlePassMain:_OnDownloadStateChange(moduleName, bSuccess, errorCode)
    local tShowItemID = Module.BattlePass.Field:GetCurrShowItem()
    local sModuleName = BattlePassLogic.GetDownloadCategary(tShowItemID, UIName2ID.BattlePassMain, self)
    if moduleName == sModuleName then
        if bSuccess then
            self._wtCommonDownload:Collapsed()
            -- 抛出点击奖励条目事件
            local iLastPosition = Module.BattlePass.Field:GetLevelRewardLastSelectPosition()
            local iLastIndex = Module.BattlePass.Field:GetLevelRewardLastSelectIndex()
            Module.BattlePass.Config.Events.evtBattlePassRewardItemClick:Invoke(iLastPosition, iLastIndex)
        else
            self._wtCommonDownload:SelfHitTestInvisible()
        end
    end
end

--- 其他

function BattlePassMain:RewardWaterFallScrollToPosition(iPosition)
    loginfo("[BattlePassMain] RewardWaterFallScrollToPosition")
    self:StopExtraDelayCall()
    self.mLastScroll2Position = iPosition

    -- 滚动到屏幕中间
    if iPosition == 80 then
        iPosition = 81
        self._wtRewardWaterFallList:ScrollToIndexToScreen(iPosition, 1.0, 1.0)
    else
        self._wtRewardWaterFallList:ScrollToIndexToScreen(iPosition, 0.5, 0.5)
    end
    
    -- 刷新核心奖励
    self:RefreshUIKeyReward()
end

-- 根据等级和在等级内的下标（从0开始）获取奖励道具条目
function BattlePassMain:GetItemByPositionIndex(iPosition, iIndex)
    local iRealPos = self:GetRewardListMainPosByServerPos(iPosition)
    if iRealPos <= 0 then
        return nil
    end
    local wtMainItem = self._wtRewardWaterFallList:GetItemByIndex(iRealPos)
    if nil == wtMainItem then
        logerror("wtMainItem nil")
        return nil
    end

    local wtItem = wtMainItem._wtRewardHorizontalBox:GetChildAt(iIndex)  -- 该函数下标从0开始
    if nil == wtItem then
        logerror("wtItem nil, iIndex: " .. iIndex, "iRealPos: " .. iRealPos, "iPosition: " .. iPosition)
        return nil
    end
    local commonItem = wtItem:Wnd("WBP_CommonItemTemplate", IVCommonItemTemplate)
    return commonItem
end

--- 根据等级和在等级内的下标（从0开始）获取具体建立道具
--- todo 待处理
--- @return {iID, iCount, eType}
function BattlePassMain:GetRewardItemByPositionIndex(iPosition, iIndex)
    local tReward = {}
    
    local iMaxLevel = Server.BattlePassServer:GetCurrMaxRewardLevel()
    local iMaxPosition = Server.BattlePassServer:GetPositionByRewardLevel(iMaxLevel)

    if iPosition <= iMaxPosition then
        -- 等级奖励
        local iLevel = Server.BattlePassServer:GetRewardLevelByPosition(iPosition)
        tReward = BattlePassLogic.GetMainShowRewardByLevel(iLevel)
    else
        -- 循环奖励 
        tReward = BattlePassLogic.GetMainShowRewardCircle()
    end

    if not tReward then
        logerror("tReward nil")
        return nil
    end

    for i, tRewardSingle in ipairs(tReward) do
        -- table index 从 1 开始
        if i == iIndex + 1 then
            return tRewardSingle 
        end
    end
    
    return nil
end

-- 设置某个道具奖励条目为选中态
function BattlePassMain:SetItemSelectedByPositionIndex(iPosition, iIndex)
    -- 如果存在上一次点击的条目，将上一次点击的条目选中态清除 
    local iLastLevel = Module.BattlePass.Field:GetLevelRewardLastSelectPosition()
    local iLastIndex = Module.BattlePass.Field:GetLevelRewardLastSelectIndex()
    local wtItem = self:GetItemByPositionIndex(iLastLevel, iLastIndex)
    if wtItem ~= nil then
        wtItem:SetSelected(wtItem.item, false)
    end

    -- 设置当前条目为选中态  
    local wtItemClick = self:GetItemByPositionIndex(iPosition, iIndex)
    if wtItemClick ~= nil then
        wtItemClick:SetSelected(wtItemClick.item, true)
    end

    -- 设置上一次点击的条目
    Module.BattlePass.Field:SetLevelRewardLastSelectPosition(iPosition)
    Module.BattlePass.Field:SetLevelRewardLastSelectIndex(iIndex)

    -- 刷新右上角道具信息
    self:RefreshRightTopItemInfo()
end

--- 获取目前显示的下一个核心奖励等级
function BattlePassMain:GetNextKeyRewardLevel()
    local iLevel = self:GetRewardListLevelByPos(self._iLastVisibleRewardPos)
    if not iLevel or iLevel <= 0 then
        return 0
    end
    return Server.BattlePassServer:GetNextKeyRewardLevel(iLevel)
end

-- 检查并更新关键奖励
function BattlePassMain:CheckAndUpdateKeyReward()
    local iKeyRewardLevel = self:GetNextKeyRewardLevel()
    -- 如果关键奖励等级有变化，则更新关键奖励内容
    if iKeyRewardLevel ~= self._iKeyRewardLevel then
        self._iKeyRewardLevel = iKeyRewardLevel
        self:RefreshUIKeyReward() 
    end
end

-- 获取滚动奖励某个位置的尺寸
function BattlePassMain:GetRewardListItemSize(position)
    local iMaxLevel = Server.BattlePassServer:GetCurrMaxRewardLevel()
    local iMaxPosition = Server.BattlePassServer:GetPositionByRewardLevel(iMaxLevel)
    local iType = self:GetRewardListPosType(position)
    
    local iCount = 0

    if iType == 1 then
        if self:IsRewardListLevelBehindRealMaxLevel() then
            if DFHD_LUA == 1 then
                return FVector2D(201, 372)
            else
                return FVector2D(190, 344)
            end
        else
            if DFHD_LUA == 1 then
                return FVector2D(201, 372)
            else
                return FVector2D(190, 344)
            end
        end
    else
        local iLevel = self:GetRewardListLevelByPos(position)
        local tRewardList = BattlePassLogic.GetMainShowRewardByLevel(iLevel)
        for _ in pairs(tRewardList) do
            iCount = iCount + 1
        end
    end

    if DFHD_LUA == 1 then
        return FVector2D(296 + (iCount - 1) * 276, 372)
    else
        return FVector2D(232 + (iCount - 1) * 212, 344)
    end
end

function BattlePassMain:BindKeyRewardClickCb(wtItem, iLevel, iIndex)
    if nil == wtItem then
        logerror("wtItem nil")
        return
    end
    
    if nil == iLevel then
        logerror("iLevel nil")
        return
    end

    -- 在滚动条中的位置
    local iPosition = Server.BattlePassServer:GetPositionByRewardLevel(iLevel)
    -- 绑定奖励点击回调函数
    local function fItemViewClick()
        -- 抛出点击奖励条目事件
        Module.BattlePass.Config.Events.evtBattlePassRewardItemClick:Invoke(iPosition, iIndex, true, true)
    end
    wtItem:BindCustomOnClicked(fItemViewClick)
end

function BattlePassMain:ShowExpBonusTips()
    BattlePassLogic.ShowExpBonusTips(self._tExpBonusTipsHandler, self._wtExpBonusTipsAnchor)
end

function BattlePassMain:HideExpBonusTips()
    BattlePassLogic.HideExpBonusTips(self._tExpBonusTipsHandler, self._wtExpBonusTipsAnchor, self._wtExpBonusTipsCheckBox)
end

-- 获取贴图图像
function BattlePassMain:GetPasteImage(iItemID)
    local tItem = ItemBase:NewIns(iItemID)
    --if tItem.itemMainType == EItemType.HeroAccessory and tItem.itemSubType == EHeroAccessroy.SparyPaint then  -- 干员商业化周边-喷漆
    --    return self._wtImgPaste
    if tItem.itemMainType == EItemType.SocialAppearance and tItem.itemSubType == ESocialAppearanceType.SocialAvatarTab then  -- 社交定制外观-头像
        return self._wtSocialAvatarTabPaste
    elseif tItem.itemMainType == EItemType.SocialAppearance and tItem.itemSubType == ESocialAppearanceType.SocialMilitaryTab then  -- 社交定制外观-军牌
        return self._wtSocialMilitaryTabPaste
    elseif tItem.itemMainType == EItemType.HeroAccessory and tItem.itemSubType == EHeroAccessroy.Card then  -- 干员商业化周边-名片
        return self._wtSocialCardPaste
    else
        return nil
    end
end

-- 获取贴图面板
function BattlePassMain:GetPasteImagePanel(iItemID)
    -- long1修改为无UI平面资源，都改为场景资源
    --local tItem = ItemBase:NewIns(iItemID)
    --if tItem.itemMainType == EItemType.HeroAccessory and tItem.itemSubType == EHeroAccessroy.SparyPaint then  -- 干员商业化周边-喷漆
    --    return self._wtImgPaste
    --if tItem.itemMainType == EItemType.SocialAppearance and tItem.itemSubType == ESocialAppearanceType.SocialAvatarTab then  -- 社交定制外观-头像
    --    return self._wtSocialAvatarTabPanel
    --elseif tItem.itemMainType == EItemType.SocialAppearance and tItem.itemSubType == ESocialAppearanceType.SocialMilitaryTab then  -- 社交定制外观-军牌
    --    return self._wtSocialMilitaryTabPanel
    --elseif tItem.itemMainType == EItemType.HeroAccessory and tItem.itemSubType == EHeroAccessroy.Card then  -- 干员商业化周边-名片
    --    return self._wtSocialCardPanel
    --else
    --    return nil
    --end
    return nil
end

-- 隐藏所有贴图控件
function BattlePassMain:CollapseAllPasteImage()
    self._wtSocialAvatarTabPanel:Collapsed()
    self._wtSocialMilitaryTabPanel:Collapsed()
    self._wtSocialCardPanel:Collapsed()
    self._wtImgPaste:Collapsed()
end

-- 获取藏品图层
function BattlePassMain:GetCollectionImage1()
    return self._wtPropImg_1
end

function BattlePassMain:GetCollectionImage2()
    return self._wtPropImg_2
end

function BattlePassMain:GetCollectionImage3()
    return self._wtPropImg_3
end

function BattlePassMain:GetCollectionImage2Box()
    return self._wtPropImgBox_2
end

function BattlePassMain:GetCollectionPanel()
    return self._wtPropImgPanel
end

-- 获取当前正在查看的奖励
function BattlePassMain:GetCurrReward()
    local iLastPosition = Module.BattlePass.Field:GetLevelRewardLastSelectPosition()
    local iLastIndex = Module.BattlePass.Field:GetLevelRewardLastSelectIndex()
    local tReward = BattlePassLogic.GetMainRewardItemByPositionIndex(iLastPosition, iLastIndex)
    return tReward
end

-- 关闭顶层面板
function BattlePassMain:CloseAllUI()
    self._wtTopCanvasPanel:Collapsed()
    
    -- 隐藏经验加成Tips
    self._wtExpBonusTipsCheckBox:SetIsChecked(false)
    self:HideExpBonusTips()
end

-- 播放喷漆动效
function BattlePassMain:PlaySparyPaintAnim()
    self:PlayAnimation(self.WBP_BattlePass_Main_penqihuan, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
end

-- 设置核心奖励的图标角标
function BattlePassMain:RefreshKeyItemComponent(itemView, eType)
    -- 是否已经付费
    local ePayType = Server.BattlePassServer:GetPayType()

    --- item 组件是否盖上
    local bCompFree = false
    local bCompPay = false
    local bCompGet = false
    local bCompMP = false
    local bCompSOL = false

    -- 类型角标
    if eType == EBattlePassRewardType.Free then
        bCompFree = true
    end
    if eType == EBattlePassRewardType.MP then
        bCompMP = true
    end
    if eType == EBattlePassRewardType.SOL then
        bCompSOL = true
    end

    -- 锁头展示
    if eType == EBattlePassRewardType.Pay then
        if ePayType == BattlePassType.BATTLE_PASS_TYPE_BASE then
            bCompPay = true
        end
    end
    if eType == EBattlePassRewardType.SOL then
        if ePayType == BattlePassType.BATTLE_PASS_TYPE_BASE or
                ePayType == BattlePassType.BATTLE_PASS_TYPE_MP then
            bCompPay = true
        end
    end
    if eType == EBattlePassRewardType.MP then
        if ePayType == BattlePassType.BATTLE_PASS_TYPE_BASE or
                ePayType == BattlePassType.BATTLE_PASS_TYPE_SOL then
            bCompPay = true
        end
    end

    -- 设置item的组件
    BattlePassLogic.SetItemViewComp(itemView, bCompFree, bCompPay, bCompGet, bCompMP, bCompSOL)
end

function BattlePassMain:GetDownloadWidget()
    return self._wtCommonDownload
end

-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
function BattlePassMain:_EnableGamepadFeature(bEnable)
    if not IsHD() then
        return
    end
    if bEnable then
        self._ItemScrollView = self:Wnd("DFWaterfallScrollView_159", UIWidgetBase) 
        if not self._wtNavGroup then
            self._wtNavGroup = WidgetUtil.RegisterNavigationGroup(self._ItemScrollView, self, "Hittest")
            if self._wtNavGroup then 
                self._wtNavGroup:AddNavWidgetToArray(self._ItemScrollView)
                self._wtNavGroup:SetScrollRecipient(self._ItemScrollView)     
                self._wtNavGroup:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
                WidgetUtil.BindCustomFocusProxy(self._wtNavGroup, self._FocusProxyMaker, self._FocusProxyResolver, self)
            end      
        end
        if self._wtNavGroup then 
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup)      
        end      
        self._wtBtnAdvanceUnlock:SetDisplayInputAction("BattlePass_Unlock_Gamepad", true, nil, true)
        if not self._AdvanceUnlock then
            self._AdvanceUnlock = self:AddInputActionBinding("BattlePass_Unlock_Gamepad", EInputEvent.IE_Pressed, self._OnBtnAdvanceUnlockClick,self, EDisplayInputActionPriority.UI_Stack)
        end
        self:_UpdateInputSummaryList()
        self._bIsShowTips = false
    else
        if self._wtNavGroup then 
            WidgetUtil.RemoveNavigationGroup(self)
            self._wtNavGroup = nil
        end
    
        if self._AdvanceUnlock then
            self:RemoveInputActionBinding(self._AdvanceUnlock)
            self._AdvanceUnlock = nil
        end
    end
end

function BattlePassMain:_FocusProxyMaker(inWidget)
    return {1}
end

function BattlePassMain:_FocusProxyResolver(inProxyHandle)
    local guid = inProxyHandle[1]
    -- 利用guid去查找对应的Item，此时不需要再查找内部的原子控件，框架会自动处理
    return self._wtRewardWaterFallList:GetItemByIndex(guid)
end

function BattlePassMain:_ToggleBonusTipsByPad()
    if not IsHD() then
        return
    end
    if  not self._bIsShowTips then
        self:ShowExpBonusTips()
        self._bIsShowTips = true
    else
        self:HideExpBonusTips()
        self._bIsShowTips = false
    end
end

function BattlePassMain:_UpdateInputSummaryList()
    if not IsHD() then
        return
    end

    Module.CommonBar:RecoverBottomBarInputSummaryList() 

    local summaryList = {}
    if self._wtBtnBuyLevel:IsVisible() then
        table.insert(summaryList, {actionName = "BattlePass_BuyLevel_Gamepad",func = self._OnBtnBuyLevelClick, caller = self ,bUIOnly = false, bHideIcon = false}) --购买等级
    end
    if self._wtCanvasPack:IsVisible() then
        table.insert(summaryList, {actionName = "BattlePass_BuyLevelPacks_Gamepad",func = self._OnBtnBuyPackClick, caller = self ,bUIOnly = false, bHideIcon = false}) ---- 特惠礼包
    end
    table.insert(summaryList, {actionName = "BattlePass_ItemInfo_Gamepad",func = self._OnBtnRewardDetailClick, caller = self ,bUIOnly = false, bHideIcon = false}) --道具详情
    if self._wtBtnPlayVideo:IsVisible() then
        table.insert(summaryList, {actionName = "BattlePass_Video_Gamepad",func = self._OnBtnPlayVideoClick, caller = self ,bUIOnly = false, bHideIcon = false}) --视频
    end
    table.insert(summaryList, {actionName = "Common_ToggleTip",func = self._ToggleBonusTipsByPad, caller = self ,bUIOnly = false, bHideIcon = false}) --问号提示
    Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, true, false)
end

-- END MODIFICATION

-------- 处理奖励列表相关begin --------

-- 是否此时等级在真正满级之前
function BattlePassMain:IsRewardListLevelBehindRealMaxLevel()
    if self._bBehindRealMaxLevel ~= nil then
        return self._bBehindRealMaxLevel
    end 
    
    local iCurrLevel = Server.BattlePassServer:GetLevel()
    local iRealMaxLevel = Server.BattlePassServer:GetRealMaxLevel()
    
    if iCurrLevel <= iRealMaxLevel then
        self._bBehindRealMaxLevel = true
        return true
    else
        self._bBehindRealMaxLevel = false
        return false
    end
end

-- 获取奖励列表元素的个数 
function BattlePassMain:GetRewardListItemCount()
    local iMaxLevel = Server.BattlePassServer:GetCurrMaxRewardLevel()
    local iRealMaxLevel = Server.BattlePassServer:GetRealMaxLevel()

    if self:IsRewardListLevelBehindRealMaxLevel() then
        return Server.BattlePassServer:GetCurrItemCountByLevelRange(1, iRealMaxLevel) + 1
    else
        return Server.BattlePassServer:GetCurrItemCountByLevelRange(iRealMaxLevel + 1, iMaxLevel) + 1
    end
end

function BattlePassMain:RefreshRewardListItem(pos, item)
    local iType = self:GetRewardListPosType(pos)

    if iType == 1 then
        item:RefreshUI(0, 0, UIName2ID.BattlePassMain, 1)
        item:SetCBBtnMore(CreateCallBack(self.OnClickMoreBtn, self))

        local iRealMaxLevel = Server.BattlePassServer:GetRealMaxLevel()
        local iMaxLevel = Server.BattlePassServer:GetCurrMaxRewardLevel()
        if self:IsRewardListLevelBehindRealMaxLevel() then
            item:SetMoreTxt(string.format(BattlePassConfig.Loc.MainItemMore, iRealMaxLevel + 1, iMaxLevel))
            item:SetMoreArrowScale(-1, 1)
        else
            item:SetMoreArrowScale(1, 1)
            item:SetMoreTxt(string.format(BattlePassConfig.Loc.MainItemMore, 1, iRealMaxLevel))
        end
    else
        local iLevel = self:GetRewardListLevelByPos(pos)
        item:RefreshUI(0, iLevel, UIName2ID.BattlePassMain, 0)
        item:SetCBBtnMore(nil)
    end
end

-- 0 正常  1 边界标识
function BattlePassMain:GetRewardListPosType(pos)
    if self:IsRewardListLevelBehindRealMaxLevel() then
        -- 在真正满级前
        local iRealMaxLevel = Server.BattlePassServer:GetRealMaxLevel()
        local iCountBeforeRealMaxLevel = Server.BattlePassServer:GetCurrItemCountByLevelRange(1, iRealMaxLevel) + 1
        if pos == iCountBeforeRealMaxLevel then
            return 1
        else
            return 0
        end
    else
        -- 在真正满级之后
        if pos == 1 then
            return 1
        else
            return 0
        end
    end
end

function BattlePassMain:GetRewardListLevelByPos(pos)
    local iType = self:GetRewardListPosType(pos)
    if self:IsRewardListLevelBehindRealMaxLevel() then
        -- 在真正满级前
        if iType == 1 then
            return -1
        else
            local iLevel = Server.BattlePassServer:GetRewardLevelByPosition(pos)
            return iLevel
        end
    else
        -- 在真正满级之后
        local iRealMaxLevel = Server.BattlePassServer:GetRealMaxLevel()

        if iType == 1 then
            return -1
        else
            local iRealPos = pos + iRealMaxLevel - 1
            local iLevel = Server.BattlePassServer:GetRewardLevelByPosition(iRealPos)
            return iLevel
        end
    end
end

function BattlePassMain:GetRewardListMainPosByServerPos(pos)
    local iRealMaxLevel = Server.BattlePassServer:GetRealMaxLevel()
    local iCountBeforeRealMaxLevel = Server.BattlePassServer:GetCurrItemCountByLevelRange(1, iRealMaxLevel)
    if self:IsRewardListLevelBehindRealMaxLevel() then
        -- 在真正满级之前
        if pos > iCountBeforeRealMaxLevel then
            return -1
        else
            return pos
        end
    else
        -- 在真正满级之后
        if pos > iCountBeforeRealMaxLevel then
            return pos - iCountBeforeRealMaxLevel + 1
        else
            return -1
        end
    end
end

function BattlePassMain:IsServerPosBehindRealMaxLevel(pos)
    local iRealMaxLevel = Server.BattlePassServer:GetRealMaxLevel()
    local iCountBeforeRealMaxLevel = Server.BattlePassServer:GetCurrItemCountByLevelRange(1, iRealMaxLevel)
    if pos <= iCountBeforeRealMaxLevel then
        return true
    else
        return false
    end
end

function BattlePassMain:OnClickMoreBtn()
    local nowClickTime = Facade.ClockManager:GetLocalTimestamp()
    local timeSpan = nowClickTime - self._tLastClickMoreItemTime
    if timeSpan < 1 then
        loginfo("BattlePassMain:OnClickMoreBtn: timeSpan is too small", timeSpan, self._tLastClickMoreItemTime, nowClickTime)
        return    
    end
    self._tLastClickMoreItemTime = nowClickTime
    
    if self._bBehindRealMaxLevel == nil then
        local bBehind = self:IsRewardListLevelBehindRealMaxLevel()
        self._bBehindRealMaxLevel = not bBehind
    else
        self._bBehindRealMaxLevel = not self._bBehindRealMaxLevel
    end
    self._wtRewardWaterFallList:RefreshItemCount()
    self._wtRewardWaterFallList:RefreshAllItems()
    self:CheckAndUpdateKeyReward()

    if self._bBehindRealMaxLevel ~= nil then
        if self._bBehindRealMaxLevel then
            local iRealMaxLevel = Server.BattlePassServer:GetRealMaxLevel()
            local iCountBeforeRealMaxLevel = Server.BattlePassServer:GetCurrItemCountByLevelRange(1, iRealMaxLevel) + 1
            Timer.DelayCall(0.01, self.RewardWaterFallScrollToPosition, self, iCountBeforeRealMaxLevel)
        else
            Timer.DelayCall(0.01, self.RewardWaterFallScrollToPosition, self, 1)
        end
    end
end

-------- 处理奖励列表相关begin --------

function BattlePassMain:StartExtraDelayCall(position)
    self:StopExtraDelayCall()

    if not position then
        return
    end

    if self._taskId == 0 then
        self._taskId = Facade.LuaFramingManager:RegisterFrameTask(self.TestBackEnd, self, {position}, nil, EFrameTaskPriority.High)
    end
end

function BattlePassMain:TestBackEnd(position)
    if position == 80 then
        position = 81
        self._wtRewardWaterFallList:NotifyContainerSizeChange(position, 1.0, 1.0)
    else
        self._wtRewardWaterFallList:NotifyContainerSizeChange(position, 0.5, 0.5)
    end
end

function BattlePassMain:StopExtraDelayCall()
    if self._taskId > 0 then
        Facade.LuaFramingManager:CancelFrameTask(self._taskId)
        self._taskId = 0
    end
end

function BattlePassMain:_OnScrolling()
    self:StopExtraDelayCall()
end

return BattlePassMain
