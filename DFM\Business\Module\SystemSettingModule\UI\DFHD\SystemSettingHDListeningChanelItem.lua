----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class SystemSettingHDListeningChanelItem
local SystemSettingHDBaseItem = require "DFM.Business.Module.SystemSettingModule.UI.DFHD.Common.SystemSettingHDBaseItem"
local SystemSettingHDDropDownItem = require "DFM.Business.Module.SystemSettingModule.UI.DFHD.Common.SystemSettingHDDropDownItem"
local SystemSettingHDListeningChanelItem = class("SystemSettingHDListeningChanelItem", SystemSettingHDDropDownItem)

local CommonSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.CommonSettingLogicHD"
local EDFMGamePlaySubMode = import "EDFMGamePlaySubMode"


function SystemSettingHDListeningChanelItem:OnOpen()
    SystemSettingHDDropDownItem.OnOpen(self)
end

function SystemSettingHDListeningChanelItem:OnShowBegin()
    SystemSettingHDBaseItem.OnShowBegin(self)
    if not CommonSettingLogicHD.IsValidID(self.ID) then
        return
    end

    local options = CommonSettingLogicHD.GetDropDownOptionsByID(self.ID)
    if self:IsInCommander() then --是否在指挥官模式中
        CommonSettingLogicHD.GetDataByID(self.ID,EListenChanel.Camp)
        table.insert(options, Module.GVoice.Config.Loc.GVoiceTeamIdentity_Commander)
    else
        self:TryResetChannelFromCommander()
    end
    self._curIndex = CommonSettingLogicHD.GetDataByID(self.ID)
    UIUtil.InitDropDownBox(self._wtDropDown, options, {},  self._curIndex)
end

function SystemSettingHDListeningChanelItem:_OnOptionChanged(curIndex, lastIndex)
    local options = CommonSettingLogicHD.GetDropDownOptionsByID(self.ID)

    
    if curIndex == 1 and CommonSettingLogicHD.GetDataByID("SpeakingChanel") ~= 2 then
        if CommonSettingLogicHD.GetDataByID("SpeakingChanel") == 0 then
            Module.CommonTips:ShowSimpleTip(Module.SystemSetting.Config.Loc.HDSetting.SwitchListeningChanelTeamTxt)
        else
            Module.CommonTips:ShowSimpleTip(Module.SystemSetting.Config.Loc.HDSetting.SwitchListeningChanelAllTxt)
        end
        CommonSettingLogicHD.SetDataByID(self.ID, lastIndex)
        self:ReloadSetting()
        return
    end

    if self:IsInCommander() then
        if not self:CanChangeChannel(curIndex) then
            local tips = Module.GVoice.Config.Loc.SpeakerChannelChangeTips
            Module.CommonTips:ShowSimpleTip(tips)
            CommonSettingLogicHD.SetDataByID(self.ID, lastIndex)
            self:ReloadSetting()
            return
        end
    end
    SystemSettingHDDropDownItem._OnOptionChanged(self, curIndex, lastIndex)
end

function SystemSettingHDListeningChanelItem:IsInCommander() --是否在指挥官模式下
    if Module.GVoice:IsCommanderGameMode() then
        return true
    end
    return false
end


function SystemSettingHDListeningChanelItem:OnReloadSetting()
    SystemSettingHDDropDownItem.OnReloadSetting(self)
end

function SystemSettingHDListeningChanelItem:_OnDropDownBoxOpenStateChanged(bOpen)
    SystemSettingHDDropDownItem._OnDropDownBoxOpenStateChanged(self, bOpen)
end

function SystemSettingHDListeningChanelItem:CanChangeChannel(curIndex) --不符合切换频道条件
    local buttontype = self:SetButtonType(curIndex)
    local microphoneButtonType = Module.GVoice:GetMicrophoneButtonType()
    local convertMicrophoneButtonType = Module.GVoice:ConvertPressButtonTypeToNormal(microphoneButtonType)
    if buttontype < convertMicrophoneButtonType then
        return false
    end
    return true
end

function SystemSettingHDListeningChanelItem:TryResetChannelFromCommander() --用于从指挥官模式退出后重置回全队频道
    local buttonType =  Module.GVoice:GetSpeakerButtonType()
    local lastIndex = CommonSettingLogicHD.GetDataByID(self.ID)
    if buttonType == EGVoiceButtonType.Camp or buttonType == EGVoiceButtonType.CampPress  then
        self._curIndex = EListenChanel.All
    end
end

function SystemSettingHDListeningChanelItem:SetButtonType(curIndex)
    if Facade.ModuleManager:IsModuleValid("GVoice") then
        local btnType = EGVoiceButtonType.Close
        if curIndex == EListenChanel.All then
            btnType = EGVoiceButtonType.All
        end
        if curIndex == EListenChanel.Camp then
            btnType = EGVoiceButtonType.Camp
        end
        return btnType
    end
end



function SystemSettingHDListeningChanelItem:OnClose()
    SystemSettingHDDropDownItem.OnClose(self)
end
return SystemSettingHDListeningChanelItem