----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMLitePackage)
----- LOG FUNCTION AUTO GENERATE END -----------


local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"

---@class LiteCommonDownload : LuaUIBaseView
local LiteCommonDownload = ui("LiteCommonDownload")

local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
local LitePackageConfig = Module.LitePackage.Config

local LiteCommonDownloadMBNum = 1024 * 1024

function LiteCommonDownload:Ctor()
    self._wtDownloadBtn = self:Wnd("DFMButton_DownloadBtn", UIButton)
    self._wtDownloadBtn:Event("OnClicked", self.OnDownloadBtnClicked, self)

    self._wtDownloadText = self:Wnd("DFTextBlock_DownloadText", UITextBlock)
    self._wtDownloadProgressText = self:Wnd("DFTextBlock_ProgressNum", UITextBlock)
    self._wtDownloadProgressImage = self:Wnd("DFImage_Progress", UIImage)

    if self._wtDownloadProgressImage ~= nil then
        self.DynamicMatIns = self._wtDownloadProgressImage:GetDynamicMaterial()
    end
end

-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function LiteCommonDownload:OnOpen()

end

function LiteCommonDownload:OnShow()
    if Module.ExpansionPackCoordinator:IsSupportLitePackage() then
        self.tickTime = 0
        self.isPlayAni = false
        LuaTickController:Get():RemoveTick(self)
        LuaTickController:Get():RegisterTick(self)

        self:AddLuaEvent(LitePackageConfig.evtDownloadManagerNtfModuleCheckResult, self.DownloadManagerNtfModuleCheckResult, self)
        self:AddLuaEvent(Module.Share.Config.Events.evtShareBeforeScreenshot,self._OnShareBeforeScreenshot,self)
        self:AddLuaEvent(Module.Share.Config.Events.evtShareAfterScreenshot,self._OnShareAfterScreenshot,self)
        self:_InitializeGamepadFeature()
    end
end

function LiteCommonDownload:OnHide()
    if Module.ExpansionPackCoordinator:IsSupportLitePackage() then
        LuaTickController:Get():RemoveTick(self)
        self:RemoveLuaEvent(LitePackageConfig.evtDownloadManagerNtfModuleCheckResult, self.DownloadManagerNtfModuleCheckResult)
        self:RemoveLuaEvent(Module.Share.Config.Events.evtShareBeforeScreenshot,self._OnShareBeforeScreenshot)
        self:RemoveLuaEvent(Module.Share.Config.Events.evtShareAfterScreenshot,self._OnShareAfterScreenshot)
        self:_DisableGamepadFeature()
    end
end

function LiteCommonDownload:OnClose()

end

function LiteCommonDownload:Update(delta)
    if self.ModuleKey == nil or self.ModuleKey == "" then
        return
    end

    self.tickTime = self.tickTime + delta
    if self.tickTime > 0.5 then
        self.tickTime = 0
        self:RefreshState()
    end
end

function LiteCommonDownload:OnDownloadBtnClicked()
    if self.ModuleKey == nil or self.ModuleKey == "" then
        return
    end

    if IsMobile() or IsInEditor() then
        local pufferInitSucceed = LiteDownloadManager:IsPufferInitSucceed()
        if pufferInitSucceed == false then
            logerror("[LiteCommonDownload] OnDownloadBtnClicked pufferInitSucceed == false")
            return
        end
    end

    local bDownloaded = Module.ExpansionPackCoordinator:IsDownloadedByModuleName(self.ModuleKey)
    if bDownloaded then
        -- if LiteDownloadManager:IsSupportLitePackage() then
        --     LuaTickController:Get():RemoveTick(self)
        -- end
        return
    end

    local isDownloading = Module.ExpansionPackCoordinator:IsDownloadingByModuleName(self.ModuleKey)
    local isWaiting = Module.ExpansionPackCoordinator:IsWaitingByModuleName(self.ModuleKey)
    if isDownloading or isWaiting then
        Module.ExpansionPackCoordinator:PauseByModuleName(self.ModuleKey)
    else
        Module.ExpansionPackCoordinator:DownloadByModuleName(self.ModuleKey)
    end

    self:RefreshState()
end

function LiteCommonDownload:RefreshState()
    if self.ModuleKey == nil or self.ModuleKey == "" then
        return
    end

    local bDownloaded = Module.ExpansionPackCoordinator:IsDownloadedByModuleName(self.ModuleKey)
    if bDownloaded then
        -- if LiteDownloadManager:IsSupportLitePackage() then
        --     LuaTickController:Get():RemoveTick(self)
        -- end
        self:CheckChildModuleState()
        return
    end

    local isDownloading = Module.ExpansionPackCoordinator:IsDownloadingByModuleName(self.ModuleKey)
    local isWaiting = Module.ExpansionPackCoordinator:IsWaitingByModuleName(self.ModuleKey)

    if isDownloading or isWaiting then
        self:SetUIStyle(true)

        local nowSize = Module.ExpansionPackCoordinator:GetNowSizeByModuleName(self.ModuleKey)
        local totalSize = Module.ExpansionPackCoordinator:GetTotalSizeByModuleName(self.ModuleKey)
        if totalSize == 0 then
            totalSize = 1
        end
        local percent = math.round(nowSize / totalSize * 1000) / 10


        if self.DynamicMatIns then
            self.DynamicMatIns:SetScalarParameterValue("Progress", percent / 100)
        end

        self._wtDownloadProgressText:SetText(percent .. "%")
        local showProgressInfo = string.format(LitePackageConfig.Loc.LiteCommonDownload_Downloading, nowSize / LiteCommonDownloadMBNum, totalSize / LiteCommonDownloadMBNum)
        self._wtDownloadText:SetText(showProgressInfo)
    else
        self._wtDownloadText:SetText(LitePackageConfig.Loc.LiteCommonDownload_Normal)
        self:SetUIStyle(false)
    end
end

---@param ModuleKey string
function LiteCommonDownload:InitModuleKey(ModuleKey)
    self.ModuleKey = ModuleKey
    local bDownloaded = Module.ExpansionPackCoordinator:IsDownloadedByModuleName(self.ModuleKey)
    self:RefreshState()

    if bDownloaded then
        self:CheckChildModuleState()
    end

    return bDownloaded
end

function LiteCommonDownload:DownloadManagerNtfModuleCheckResult(ModuleKey, bIsDownloaded, nowSize, totalSize, errorcode)
    if self.ModuleKey == ModuleKey  then
        local bDownloaded = Module.ExpansionPackCoordinator:IsDownloadedByModuleName(self.ModuleKey)
        self:RefreshState()

        self:CheckChildModuleState()
        LitePackageConfig.evtDownloadManagerNtfModuleStateMaybeChanged:Invoke(ModuleKey, bDownloaded)

        if bDownloaded then
            --Facade.UIManager:CloseUI(self)
        end
    end
end

function LiteCommonDownload:CheckChildModuleState()
    if self.ModuleKey == nil or self.ModuleKey == "" then
        return
    end

    if IsMobile() or IsInEditor() then
        local modulestate = LiteDownloadManager:GetModuleStateByModuleName(self.ModuleKey)
        if modulestate == 1 then
            local childModules = LiteDownloadManager:GetRegisterRuntimeChildModules(self.ModuleKey)
            if childModules ~= nil then
                for index, childModule in ipairs(childModules) do
                    local childModuleState = LiteDownloadManager:GetModuleStateByModuleName(childModule)
                    if childModuleState ~= 1 then
                        LiteDownloadManager:CheckModuleAsyncWithQueue(childModule)
                        logerror("[LiteCommonDownload] CheckChildModuleState CheckModuleAsyncWithQueue childModule:"..childModule)
                    end
                end
            end
        end
    end
end

function LiteCommonDownload:_OnShareBeforeScreenshot()
    self._wtDownloadBtn:Collapsed()
end

function LiteCommonDownload:_OnShareAfterScreenshot()
    self._wtDownloadBtn:Visible()
end


---@param type number 0:系统页面(改枪台、藏品等多信息页面) 1:武器外观详情等主题偏右展示的页面 2:商业化等页面, 居中显示
function LiteCommonDownload:SetUIPositionType(type, OutFrameProcessing, ShowBg)
    OutFrameProcessing = setdefault(OutFrameProcessing, true)
    ShowBg = setdefault(ShowBg, false)
    self:SetPositionType(type, OutFrameProcessing, ShowBg)
end

---@param bIsDownloading boolean
function LiteCommonDownload:SetUIStyle(bIsDownloading)
    self:SetType(bIsDownloading)
end

function LiteCommonDownload:_InitializeGamepadFeature()
    if not IsHD() then
        return
    end

    if self._wtGamepadKeyIcon == nil then
        self._wtGamepadKeyIcon = self:Wnd("WBP_CommonKeyIconBox", HDKeyIconBox)
    end
    if self._wtGamepadKeyIcon then
        self._wtGamepadKeyIcon:SelfHitTestInvisible()
        self._wtGamepadKeyIcon:SetOnlyDisplayOnGamepad(true)
        self._wtGamepadKeyIcon:InitByDisplayInputActionName("DownloadRes_Gamepad", true, 0, false)
        if not self._DownloadResHandle then
            self._DownloadResHandle = self:AddInputActionBinding("DownloadRes_Gamepad", EInputEvent.IE_Pressed, self.OnDownloadBtnClicked, self, EDisplayInputActionPriority.UI_Stack)
            --self._wtDownloadBtn:SetDisplayInputActionWithLongPress(self._DownloadResHandle, self, "DownloadRes_Gamepad", true, nil, true)

            self._wtGamepadKeyIcon:BP_ShowHoldProgressBarTips(true)
            self:AddHoldInputActionProgressedBinding(self._DownloadResHandle, self.OnConfirmBtnInLongPressing, self)
            self:AddHoldInputActionReleaseBinding(self._DownloadResHandle, self.OnConfirmBtnLongPressFinished, self)
        end
    end
end

function LiteCommonDownload:_DisableGamepadFeature()
    if not IsHD() then
        return
    end
    if self._DownloadResHandle then
        self:RemoveInputActionBinding(self._DownloadResHandle)
        self._DownloadResHandle = nil
    end
end

function LiteCommonDownload:OnConfirmBtnInLongPressing(Percent)
    if self._wtGamepadKeyIcon then
        self._wtGamepadKeyIcon:BP_UpdateProgressBar(Percent)
    end
end

function LiteCommonDownload:OnConfirmBtnLongPressFinished()
    if self._wtGamepadKeyIcon then
        self._wtGamepadKeyIcon:BP_UpdateProgressBar(0)
    end
end

return LiteCommonDownload
