----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------


---@class SystemSettingHDCloudSettingChangeName : LuaUIBaseView
local SystemSettingHDCloudSettingChangeName = ui("SystemSettingHDCloudSettingChangeName")
local CommonPopWindows = require "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindows"
local CloudSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.CloudSettingLogicHD"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"

function SystemSettingHDCloudSettingChangeName:Ctor()
    self._wtCommonPopWin = self:Wnd("WBP_CommonPopWindowsV2", CommonPopWindows)
    self._wtCommonPopWin:BindCloseCallBack(CreateCallBack(self._OnCloseBtnClicked, self))

    local OnInputChanges = CreateCallBack(self._OnMicrosoftInput, self)
    local OnInputComplete = CreateCallBack(self._OnInputComplete, self)

    self._wtNewNameInput = UIUtil.WndInputBoxWithBtn(self, "WBP_InputBoxWithBtn", 56, nil, OnInputChanges, OnInputComplete)

    self._wtCommonPopWin:OverrideGamepadSetting("Common_ButtonLeft_Gamepad", nil, WidgetUtil.EUINavDynamicType.Default)

    -- 因某些原因会跳过实时检查，导致_wtNewNameInput没有更新，用这个表示在提交时需要检查
    self._checkOnSubmit = false
end

function SystemSettingHDCloudSettingChangeName:OnInitExtraData(fOnNameConfirmed, name)
    self._fOnNameConfirmed = fOnNameConfirmed
    local list = {{
        fClickCallback = self._OnConfirmBtnClicked,
        caller = self, bNeedClose = false
    }}
    self._btnList = self._wtCommonPopWin:SetConfirmBtnType(CommonPopWindows.EConfirmBtnType.CenterConfirm, list, false)
    --尝试获取昵称
    if name then
        self._name = name
        self._wtNewNameInput.wtEditableTextBox:SetText(name)
    end
    --初始化昵称/按钮状态
    self._newName = ""
    --进入界面尝试刷新一次,解决请求没有回包不刷新的情况(但无法避免误差,但影响不大)
    self:_OnNameTBChange()
end

function SystemSettingHDCloudSettingChangeName:OnShowBegin()
    self:_InitGamepadInputs()
end

function SystemSettingHDCloudSettingChangeName:OnHideBegin()
    self:_DisableGamepadInputs()
end


function SystemSettingHDCloudSettingChangeName:_OnMicrosoftInput()
    if DFCONSOLE_LUA ~= 1 then
        --微软输入法需要根据文字内容确定光标位置，因此在打字期间不更新有效名字
        local InputMethod = import "CustomInputMethodTipBPLib"
        if InputMethod and InputMethod.IsCanddateListVisible() then
            self._checkOnSubmit = true
            return
        end
    end
    self:_OnNameTBChange()
end

--输入完成事件
function SystemSettingHDCloudSettingChangeName:_OnInputComplete()
    Timer.DelayCall(0, function()
        if self._wtCommonPopWin then
            self._wtCommonPopWin:SetPopIsOnClose(true)
        end
    end, self)
end

function SystemSettingHDCloudSettingChangeName:_OnNameTBChange()
    self._checkOnSubmit = false
    --获取输入的名字
    local newName = ""

    if self._name then     --消化旧名字
        newName = self._name
        self._name = nil
    else
        if self._wtNewNameInput and self._wtNewNameInput.wtEditableTextBox then
            newName = self._wtNewNameInput.wtEditableTextBox:GetText()
        end
    end

    local result, validName = CloudSettingLogicHD.CheckPlayerNameValid(tostring(newName))
    if not validName then
        validName = ""
    end
    self._wtNewNameInput.SetText(validName)
    self._newName = validName
end

function SystemSettingHDCloudSettingChangeName:_OnCloseBtnClicked()
    Facade.UIManager:CloseUI(self)
end

function SystemSettingHDCloudSettingChangeName:_OnConfirmBtnClicked()
    --尝试检验昵称
    if self._wtNewNameInput and self._wtNewNameInput.wtEditableTextBox then
        self._newName = tostring(self._wtNewNameInput.wtEditableTextBox:GetText())
    end

    if self._checkOnSubmit then
        local result, validName = CloudSettingLogicHD.CheckPlayerNameValid(tostring(self._newName))
        if not validName then
            Module.CommonTips:ShowSimpleTip(Module.SystemSetting.Config.Loc.HDSetting.CloudSettingTxt.NameFail)
            self._fOnNameConfirmed()
            Facade.UIManager:CloseUI(self)
            return
        end
    end

    if string.len(self._newName) > 0 then
        if Module.LocalizeFont:CharacterIsExistInFont(self._newName) == false then
            Module.CommonTips:ShowSimpleTip(Module.SystemSetting.Config.Loc.HDSetting.CloudSettingTxt.NameHasInvalidChar)
        else
            self._fOnNameConfirmed(self._newName)
            Facade.UIManager:CloseUI(self)
            return
        end
    else
        Module.CommonTips:ShowSimpleTip(Module.SystemSetting.Config.Loc.HDSetting.CloudSettingTxt.NameEmptyTip)
    end
    self._fOnNameConfirmed()
end

function SystemSettingHDCloudSettingChangeName:OnNavBack()
    return false
end

function SystemSettingHDCloudSettingChangeName:OnClose()
    self:RemoveAllLuaEvent()
end


function SystemSettingHDCloudSettingChangeName:_InitGamepadInputs()
    if not self._navGroup then
        self._navGroup = WidgetUtil.RegisterNavigationGroup(self, self, "Hittest")
        if self._navGroup then
            self._navGroup:AddNavWidgetToArray(self._wtNewNameInput)
            self._navGroup:MarkIsStackControlGroup()
        end
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroup)
    end
end

function SystemSettingHDCloudSettingChangeName:_DisableGamepadInputs()
    if self._navGroup then
        WidgetUtil.RemoveNavigationGroup(self)
        self._navGroup = nil
    end
end


return SystemSettingHDCloudSettingChangeName
