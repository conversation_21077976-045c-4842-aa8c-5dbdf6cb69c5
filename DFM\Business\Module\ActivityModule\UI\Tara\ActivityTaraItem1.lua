----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMWeaponAssembly)
----- LOG FUNCTION AUTO GENERATE END -----------

--朋友圈
---@class ActivityTaraItem1 : LuaUIBaseView
local ActivityTaraItem1 = ui("ActivityTaraItem1")
local ActivityLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"
local Config = Module.Activity.Config
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPInputType = import"EGPInputType"

function ActivityTaraItem1:Ctor()
    self._wtFHead = self:Wnd("DFImage_77"     , UIImage)
    self._wtDTips = self:Wnd("DFTextBlock"    , UITextBlock)

    self._wtDTime = self:Wnd("DFTextBlock_1"  , UITextBlock)
    self._wtFName = self:Wnd("DFTextBlock_19" , UITextBlock)
    self._wtFTime = self:Wnd("DFTextBlock_93" , UITextBlock)
    self._wtOrder = self:Wnd("DFTextBlock_336", UITextBlock)

    self._wtTitle = self:Wnd("DFTextBlock_400", UITextBlock)
    self._wtFDesc = self:Wnd("DFRichTextBlock", UITextBlock)
    self._wtXxNum = self:Wnd("DFTextBlock_113", UITextBlock)

    self._wtNoAny = self:Wnd("WBP_Common_NoAnything_1", UIWidgetBase)
    self._wtFBox1 = UIUtil.WndWaterfallScrollBox(self, "DFScrollGridBox_68", self._OnWaterfallCount1, self._OnWaterfallWidget1)
    self._wtFBox2 = UIUtil.WndWaterfallScrollBox(self, "DFScrollGridBox"   , self._OnWaterfallCount2, self._OnWaterfallWidget2)
    self._wtDFBtn = self:Wnd("DFButton_0"  , UIButton)
    self._wtMaBtn = self:Wnd("DFButton_176", UIButton)

    self._wtDFBtn:Event("OnClicked"  , self._OnClicked  , self)
    self._wtMaBtn:Event("OnClicked"  , self._OnMaClicked, self)

    self._wtMaBtn:Event("OnHovered", self._OnHovered, self)
    self._wtMaBtn:Event("OnUnHovered", self._OnUnHovered, self)

    self._wtMaBtn:Event("OnPressed",  self._OnPressed, self)--按下
    self._wtMaBtn:Event("OnReleased", self._OnReleased, self)--释放
end

function ActivityTaraItem1:_OnMaClicked()
    if IsHD() and WidgetUtil.IsGamepad() then
        --打开图鉴
        local descs  = {}
        local icons  = {}
        local titles = {}
        local extraParam = {
            isIcon = false,
            title = Config.Loc.SharePicture,
            iconX = 1604,
            iconY = 902,
            frameX = 1564,
            frameY = 902,
            bAutoResize = true,
        }
        for index, path in ipairs(self._icons or {}) do
            if path then
                table.insert(descs  , "")
                table.insert(icons  , tostring(path))
                table.insert(titles , "")
            end
        end
        if self._activityID and #icons > 0 then
            Facade.UIManager:AsyncShowUI(UIName2ID.ActivityTopicPopWindow, nil, self, self._activityID, descs, icons, titles, extraParam)
        end
        return
    end
    self:_OnClicked()
end

--是否手柄
function ActivityTaraItem1:IsGamepad()
    return IsHD() and WidgetUtil.IsGamepad()
end

function ActivityTaraItem1:_OnHovered()
    local data = nil
    if self:IsRecoverable() and self:IsUnLock() then
        data = self._data
        self._isHovered = true
        self:_SetHoveredState(1)
    end
    if self:IsGamepad() then
        Config.evtAddTaraHandleAdaptation:Invoke(ETaraHandleType.Comment, self, data, nil, self._index)
    end
end

function ActivityTaraItem1:_OnUnHovered()
    self._isHovered = false
    self:_SetHoveredState(0)
end

function ActivityTaraItem1:_OnPressed()
    if self._isHovered then
        self:_SetHoveredState(2)
    end
end

function ActivityTaraItem1:_OnReleased()
    if self._isHovered then
        self:_SetHoveredState(1)
    end
end

function ActivityTaraItem1:_SetHoveredState(state)
    if self.SetHoverStyle and state then
        self:SetHoverStyle(state)
    end
end

function ActivityTaraItem1:_OnClicked()
    if self:IsRecoverable() and self:IsUnLock() then
        --打开评论
        Config.evtOpenTaraPanel:Invoke(self._activityID, self._data, 2)
    end
end

function ActivityTaraItem1:IsRecoverable()
    local data = self._data
    local hero = self._hero
    if data and data.contents and hero then
        return not data.replied and #data.contents > 0
    end
    return false
end

function ActivityTaraItem1:IsUnLock()
    local data = self._data
    local hero = self._hero
    if data and hero then
        if data.count and hero.count then
            return hero.count >= data.count
        end
    end
    return false
end

function ActivityTaraItem1:InitData(activityID, data, defaultIndex, hero)
    self._activityID = activityID
    self._data = data
    self._hero = hero
    if data and hero then
        self._wtFHead:AsyncSetImagePath(hero.path or "", true)
        self._wtFName:SetText(ActivityLogic.LocalizeText(hero.name))
        --兼容策划配置\n无法识别的问题
        self._wtFDesc:SetText(string.gsub(ActivityLogic.LocalizeText(data.desc), "\\n", "\n"))
        self._wtDTime:SetText(data.time or "")
        self._wtXxNum:SetText(data.news == nil and 0 or #data.news)
        if data.count and hero.count and self.SetStyle then
            --是否解锁
            if data.count <= hero.count then
                self:SetStyle(0)
            else
                self:SetStyle(1)
            end
        end
        if data.contents and self.SetType then
            --是否回复
            self:SetType(self:IsRecoverable() and 0 or 1)
            --是否悬浮
            self._wtMaBtn:SetRenderOpacity((self:IsRecoverable() and self:IsUnLock()) and 1 or 0)
        end
        self._wtDTips:SetText(Config.Loc.RewardTips)
        self._wtNoAny:StopAllAnimations()
        self._wtNoAny:PlayAnimation(self._wtNoAny.WBP_Common_NoAnything_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
        self:_RefreshBox()
    end
end

function ActivityTaraItem1:_RunAnTimer()
    if self._timer == nil and IsHD() then
        self._timer = Timer:NewIns(0.5, 0)
        self._timer:AddListener(self._SetBtnGamepad, self)
    end
    if self._timer then
        self._timer:Stop()
        self._timer:Start()
    end
end

function ActivityTaraItem1:_RenoveAnTimer()
    if self._timer then
        self._timer:Stop()
        self._timer:Release()
        self._timer = nil
    end
end

function ActivityTaraItem1:_SetBtnGamepad()
    if not WidgetUtil.IsGamepad() and self:IsRecoverable() and self:IsUnLock() then
        self._wtDFBtn:Visible()
    else
        self._wtDFBtn:Collapsed()
    end
end

function ActivityTaraItem1:OnShowBegin()
    self:_AddEventListener()
end

function ActivityTaraItem1:OnHideBegin()
    self:RemoveAllLuaEvent()
end

function ActivityTaraItem1:OnClose()
    self:_RenoveAnTimer()
end

function ActivityTaraItem1:SetIndex(index)
    self._index = index
end

function ActivityTaraItem1:SetIsPlayAnim(isAnim)
    self._isAnim = isAnim
end

function ActivityTaraItem1:_AddEventListener()
    self:AddLuaEvent(Config.evtAddTaraHandleAdaptation, self._OnAddTaraHandleAdaptation, self)
end

--发起器
function ActivityTaraItem1:_OnAddTaraHandleAdaptation(handleType, ...)
    if handleType == ETaraHandleType.Anim then
        self:_OnPlayInAnim(...)
    elseif handleType == ETaraHandleType.EnterAnim then
        self:_OnPlayEnterAnim(...)
    end
end

function ActivityTaraItem1:_OnPlayEnterAnim(time)
    local index = self._index
    if index == 1 and time then
        local func = function()
            self:Visible()
            self:PlayAnimation(self.WBP_PatrolAsala_ArticleItem_Q1ZNK_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
        end
        self:Hidden()
        Timer.DelayCall(time * index, func, self)
    end
end

function ActivityTaraItem1:_OnPlayInAnim(time)
    local index = self._index
    if index and time and self._isAnim then
        self:SetIsPlayAnim(false)
        local func = function()
            self:Visible()
            self:PlayAnimation(self.Anim_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
        end
        if index > 1 then
            index = index - 1
        else
            index = 0
            self:Hidden()
        end
        Timer.DelayCall(time * index, func, self)
    end
end

function ActivityTaraItem1:_RefreshBox()
    if self._data then
        self._icons = self._data.icons
        self._rewards = self._data.rewards
    end
    self._wtFBox1:RefreshAllItems()
    self._wtFBox2:RefreshAllItems()
end

function ActivityTaraItem1:_OnWaterfallCount1()
    if self._icons then
        return #self._icons
    end
    return 0
end

function ActivityTaraItem1:_OnWaterfallWidget1(position, itemWidget)
    local index = position + 1
    if self._icons and itemWidget then
        itemWidget:InitData(self._activityID, {path = self._icons[index]}, index, 2)
        itemWidget:AddImagePath(self._icons)
        itemWidget:SetImgStroke(true)
        if WidgetUtil.IsGamepad() then
            itemWidget:SetHeadBtn(false)
        else
            itemWidget:SetHeadBtn(true)
        end
        itemWidget:SetRedFrame(true)
        itemWidget:SetCppValue("bIsFocusable", false)
    end
end

function ActivityTaraItem1:_OnWaterfallCount2()
    if self._rewards then
        return #self._rewards
    end
    return 0
end

function ActivityTaraItem1:_OnWaterfallWidget2(position, itemWidget)
    local index = position + 1
    if self._rewards and itemWidget then
        local itemData = ActivityLogic.GetWeaponSkinItem(self._rewards[index])
        if itemData then
            itemWidget:InitItem(itemData)
            itemWidget:BindCustomOnClicked(SafeCallBack(self._OnRewardClicked, self, itemData, itemWidget))
            itemWidget:SetCppValue("bIsFocusable", false)
        end
    end
end

function ActivityTaraItem1:_OnRewardClicked(itemData, btn)
    if itemData and btn then
        --打开详情页
        if itemData.itemMainType == EItemType.WeaponSkin then
            Module.Collection:ShowWeaponSkinDetailPage(itemData, nil)--武器皮肤(包括刀皮)
        elseif itemData.itemMainType == EItemType.Adapter then
            Module.Collection:ShowHangingDetailPage(itemData   , nil)--配件(挂饰)
        elseif itemData.itemMainType == EItemType.Fashion then
            Facade.UIManager:AsyncShowUI(UIName2ID.StaffLotteryAccessoriesPreview, nil, nil, itemData.id)--干员皮肤
        else
            Module.ItemDetail:OpenItemDetailPanel(itemData, btn)--详情页
        end
    end
end

return ActivityTaraItem1