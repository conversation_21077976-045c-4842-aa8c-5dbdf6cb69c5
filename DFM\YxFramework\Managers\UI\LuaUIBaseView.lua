----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaFUIManager)
----- LOG FUNCTION AUTO GENERATE END -----------

local AnalysisUtil = require "DFM.YxFramework.Util.AnalysisUtil"
local UIAnimHelper = require "DFM.YxFramework.Managers.UI.Anim.UIAnimHelper"
local UGPUINavigationUtils = import_hd("GPUINavigationUtils")
local UISimulatorUtil = require "DFM.YxFramework.Managers.UI.Util.UISimulatorUtil"
local UIReleaseUtil = require "DFM.YxFramework.Managers.UI.Util.UIReleaseUtil"
local ULAI = import "LAI"
local UDFThemeImage = import "DFThemeImage"
local UDFThemeShadowImage = import "DFThemeShadowImage"

---@class LuaUIBaseView : UIWidgetBase
LuaUIBaseView = class("LuaUIBaseView", require("DFM.YxFramework.Managers.UI.UIWidget.UIWidgetBase"))

local UGPInputHelper = import("GPInputHelper")
local EGPInputModeType = import("EGPInputModeType")
local ULuaSubsystem = import "LuaSubsystem"

local bUseNewLifeFunction = false
function gUseNewLifeFunction(bUse)
    bUseNewLifeFunction = bUse
end

local bGlobalEnableLifeFunctionFraming = true
function gGlobalEnableLifeFunctionFraming(bEnable)
    bGlobalEnableLifeFunctionFraming = bEnable
end

local bEnableLifeFunctionFraming = false
function gEnableLifeFunctionFraming(bEnable)
    bEnableLifeFunctionFraming = bEnable
end

LuaUIBaseView.EUIState = {
    Init = 0, --- Ctor时，进入此状态（cppIns调用NewOn，创建绑定的luaIns）
    Open = 1, --- NativeConstruct时触发Open生命周期，进入此状态（cppIns已经AddToParent）
    ShowBegin = 2, --- Show时播动画前触发ShowBegin生命周期，进入此状态
    Show = 3, --- Show时动画播完触发Show生命周期，进入此状态
    HideBegin = 4, --- Hide时播动画前触发HideBegin生命周期，进入此状态
    Hide = 5, --- Hide时动画播完触发Hide生命周期，进入此状态
    Activate = 6, --- 新增激活态, 从对象池中刚取出来的时候进入此状态, 和Init在某些判断条件下等价
    Deactivate = 7, --- 新增非激活态, 在对象池中且有效时进入此状态
    Close = 8, --- TryClose时先触发HideBegin、Hide动画播完, 然后触发Close生命周期，进入此状态
    Mute = 9, --- NativeDestruct时触发（并非所有UI都走Close流程，有些直接被C++关闭），进入此状态（cppIns已经RemoveFromParent）
    Release = 10 --- luaIns解引用后被回收调用Destroy方法，进入此状态
}

local EUIState = LuaUIBaseView.EUIState
__DebugOnly_UIState = function(eUIState)
    if eUIState == EUIState.Init then
        return "EUIState.Init"
    end
    if eUIState == EUIState.Open then
        return "EUIState.Open"
    end
    if eUIState == EUIState.ShowBegin then
        return "EUIState.ShowBegin"
    end
    if eUIState == EUIState.Show then
        return "EUIState.Show"
    end
    if eUIState == EUIState.HideBegin then
        return "EUIState.HideBegin"
    end
    if eUIState == EUIState.Hide then
        return "EUIState.Hide"
    end
    if eUIState == EUIState.Deactivate then
        return "EUIState.Deactivate"
    end
    if eUIState == EUIState.Close then
        return "EUIState.Close"
    end
    if eUIState == EUIState.Mute then
        return "EUIState.Mute"
    end
    if eUIState == EUIState.Activate then
        return "EUIState.Activate"
    end
    if eUIState == EUIState.Release then
        return "EUIState.Release"
    end
    return tostring(eUIState)
end

--- 新UI面板基类
LuaUIBaseView.mapUINavID2GidBase = {}
function LuaUIBaseView.Encode2TokenId(uiNavID, instanceID)
    uiNavID = setdefault(uiNavID, 0)
    instanceID = setdefault(instanceID, 1)
    local tokenId = uiNavID << 32
    tokenId = tokenId + instanceID
    -- logw('Encode2TokenId tokenId为 ', tokenId, ' 子UI instanceID 为 ', instanceID, ' UITokenID:', tokenId, ' UINavID为 ', uiNavID)
    return tokenId
end

function LuaUIBaseView.DecodeByTokenId(tokenId, instanceID)
    instanceID = setdefault(instanceID, 1)
    tokenId = tokenId - instanceID
    local uiNavID = tokenId >> 32
    -- logw('DecodeByTokenId tokenId为 ', tokenId, ' 子UI instanceID 为 ', instanceID, ' UITokenID:', tokenId, ' UINavID为 ', uiNavID)
    return uiNavID
end

--- 新UI面板基类
function LuaUIBaseView:Ctor()
    if self._cname ~= "LuaUIBaseView" then
        self.UINavID = UIName2ID[self._cname]
        if self.UINavID and type(self.UINavID) == "number" then
            local insGidBase = LuaUIBaseView.mapUINavID2GidBase[self.UINavID]
            if insGidBase == nil then
                LuaUIBaseView.mapUINavID2GidBase[self.UINavID] = 1
            else
                LuaUIBaseView.mapUINavID2GidBase[self.UINavID] = insGidBase + 1
            end
            local curGid = LuaUIBaseView.mapUINavID2GidBase[self.UINavID]
            self.UITokenID = LuaUIBaseView.Encode2TokenId(self.UINavID, curGid)
        end
    end
    self.bIsUIWidgetBase = false
    self.lifeAnimHandle = nil

    -- 类型判断，避免递归调用重复C++和lua的交互
    self._isTable = false
    self._isULuaUIBaseViewClass = false

    --是否在视口or父容器层级内
    self._bInLayer = false
    --在Lua层存一下，避免从Native获取的性能消耗
    self._curUIState = EUIState.Init
    self._pendingUIState = nil

    self.oriVisibility = self.Visibility
    self._bFirstOpen = true
    self._bOpenThisFrame = false
    self._shouldRecoveryVisibility = true
    self._recoveryForShowVisibility = nil
    self._recoveryForHideVisibility = nil

    self._animOriVisibility = nil

    self._bNeedRestore = true
    self._bNeedRestoreEachIdx = false
    self._restoreCacheInfo = nil

    self.bLifeFunctionInit = false

    -- if IsInEditor() then
    --     ensure(issubclass(LAI.GetObjectClass(self.__cppinst), ULuaUIBaseView), "")
    -- end
    Facade.UIManager:CacheUIForCurrentFrame(self)
    self:CheckParent(true)

    self.bUseNewLifeFunction = self:LuaRegisterLifeFunction()

    self._mapAnimType2CustomName = {}
    self._lifeAniSpeedScale = DEFAULT_LIFEANI_SPEED

    rawset(self, "_is_enable_", true)

    self._displayActionHandles = {}
    self._debugName = tostring(self.__cppinst)
end

function LuaUIBaseView:Reset()
    self._curUIState = EUIState.Init
end

--------------------------------------------------------------------------
--- 缓存池UI激活、熄火API
--------------------------------------------------------------------------
--- 对标新建实例的Ctor和OnResInitialized, 时机早于OnInitExtraData、Open、Show
--- 不触发动画
---@overload fun(CppObject, Activate)
function LuaUIBaseView:Activate()
    rawset(self, "_is_enable_", true)
    self:_InterruptLifeAni(EUIState.ShowBegin)
    if self.IsAnyAnimationPlayingRealtime then
        if self:IsValid() then
            if self:IsAnyAnimationPlayingRealtime() then
                self:StopAllAnimations()
            end
        end
    end

    self._pendingUIState = nil
    self._lifeAniSpeedScale = DEFAULT_LIFEANI_SPEED
    -- self._bFirstOpen = true
    self._recoveryForShowVisibility = nil
    self._recoveryForHideVisibility = nil

    if UIUtil.CheckVisibilityIsVisible(self.oriVisibility) then
        self:SetVisibility(self.oriVisibility)
    end

    trycall(self._OnRecursiveLife, self, EUIState.Activate)
    Facade.UIManager:CacheUIForCurrentFrame(self)
end

--- 对标关闭实例的Close流程，时机早于Mute、Release
--- 触发Hide动画
---@overload fun(CppObject, Deactivate)
---@param bImmediate boolean
function LuaUIBaseView:Deactivate(bImmediate)
    bImmediate = setdefault(bImmediate, false)
    rawset(self, "_is_enable_", false)
    self:_InterruptLifeAni(EUIState.HideBegin)
    if self.IsAnyAnimationPlayingRealtime then
        if self:IsValid() then
            if self:IsAnyAnimationPlayingRealtime() then
                self:StopAllAnimations()
            end
        end
    end

    self._pendingUIState = EUIState.Deactivate
    self._lifeAniSpeedScale = DEFAULT_LIFEANI_SPEED
    if self.ClearCustomData then
        self:ClearCustomData()
    end
    self.oriVisibility = self.Visibility
    self:Hide(true, bImmediate)
end

function LuaUIBaseView:SetEnable(bEnable)
    rawset(self, "_is_enable_", bEnable)
end

function LuaUIBaseView:GetEnable()
	return self._is_enable_
end

--------------------------------------------------------------------------Close
--- 手动定制配置动画
--- 以下方法仅允许UIManager使用
--- 禁止业务使用
--------------------------------------------------------------------------
function LuaUIBaseView:ManuelSetLifeAniName(eAutoAnimType, animName)
    if animName and self[animName] then
        self._mapAnimType2CustomName[eAutoAnimType] = animName
        loginfo("LuaUIBaseView:ManuelSetLifeAniName >> 配置特殊动画为：", eAutoAnimType, animName)
    end
end

function LuaUIBaseView:ManuelSetLifeAniSpeedScale(animSpeedScale)
    self._lifeAniSpeedScale = setdefault(animSpeedScale, 1)
    loginfo("LuaUIBaseView:ManuelSetLifeAniSpeedScale >> 配置生命周期动画自定义速率为：", animSpeedScale)
end

--------------------------------------------------------------------------
--- 生命周期
--------------------------------------------------------------------------
function LuaUIBaseView:LuaRegisterLifeFunction()
    if not bUseNewLifeFunction or not self.RegisterLifeFunction then
        return false
    end

    local cls = self._meta_
    if cls.OnShow and cls.OnShow ~= LuaUIBaseView.OnShow then
        self:RegisterLifeFunction(EUIState.Show)
    end
    if cls.OnHide and cls.OnHide ~= LuaUIBaseView.OnHide then
        self:RegisterLifeFunction(EUIState.Hide)
    end
    if cls.OnShowBegin and cls.OnShowBegin ~= LuaUIBaseView.OnShowBegin then
        self:RegisterLifeFunction(EUIState.ShowBegin)
    end
    if cls.OnHideBegin and cls.OnHideBegin ~= LuaUIBaseView.OnHideBegin then
        self:RegisterLifeFunction(EUIState.HideBegin)
    end
    if cls.OnActivate and cls.OnActivate ~= LuaUIBaseView.OnActivate then
        self:RegisterLifeFunction(EUIState.Activate)
    end
    if cls.OnDeactivate and cls.OnDeactivate ~= LuaUIBaseView.OnDeactivate then
        self:RegisterLifeFunction(EUIState.Deactivate)
    end
    return true
end

function LuaUIBaseView:SetPanelOwner(panelOwner)
end

function LuaUIBaseView:GetPanelOwner()
end

function LuaUIBaseView:_SupportAutoNavigation()
    if DFHD_LUA == 1 and UGPUINavigationUtils.IsNavigationEnabled() then
        local uiSettings = self.UISettings
        if uiSettings then
            if
                uiSettings.UILayer == EUILayer.Stack or uiSettings.UILayer == EUILayer.Root or
                    (uiSettings.UILayer == EUILayer.Pop and uiSettings.IsModal)
             then
                return true
            end
        end
    end
    return false
end

function LuaUIBaseView:RegisterNavigationFunction()
    self:SetCppValue("bDoHittestNavigation", true)
    -- self.OnNavigation = function(self, myGeometry, inNavigationEvent)
    --     -- 根据当前navigation的方向 移动到框架上
    --     local recipient = Module.CommonBar:GetNavigationRecipient(myGeometry, inNavigationEvent)
    --     return recipient
    -- end
end

--BEGIN MODIFICATION @ VIRTUOS : 函数跟NavigationGroup有冲突，且没有页面正常使用这个方法。先移除。
-- function LuaUIBaseView:InitUINavigationFocusState()
--     -- 检查当前UI是否在FocusPath中，如果是的话，说明有业务逻辑在控制Focus，此时不再设置Focus
--     if not UGPUINavigationUtils.IsWidgetInFocusPath(0, self) then
--         local widgetToFocus = self:GetDesiredFocusWidget()
--         if widgetToFocus then
--             widgetToFocus:SetFocus()
--         else
--             -- 当前的UI可能bIsFocusable是false，这样的话会使得focus传递到FrameRoot
--             -- 暴力搜索
--             if not UGPUINavigationUtils.TryAutoFocus(0, self) then
--                 self:SetFocus()
--             end
--         end
--     end
-- end
--END MODIFICATION

--------------------------------------------------------------------------
--- UISettings相关
--------------------------------------------------------------------------
function LuaUIBaseView:InitUISettings(UINavID)
    local cppIns = self.__cppinst
    --- 初始化Settings
    if UINavID == nil or UITable[UINavID] == nil then
        local insCppName = LAI.GetObjectClassName(cppIns)
        UINavID = UIBPCls2ID[insCppName]
    end
    local UISettings = clone(UITable[UINavID])
    self.UINavID = UINavID
    self.UISettings = UISettings
    --- Lua所需配置刷新
    if isvalid(cppIns) and not self.bIsHud and issubclass(LAI.GetObjectClass(self), ULuaUIBaseView) then
        if self:GetIsIgnoreWorldForceGc() then
            self:SetCppValue("bTransformWorld", true)
        else
            self:SetCppValue("bTransformWorld", false)
        end
    else
        if cppIns == nil or not isvalid(cppIns) then
            logerror("LuaUIBaseView:InitUISettings(UINavID) cppIns is nil", cppIns, debug.traceback())
        end
    end
end

function LuaUIBaseView:GetUISettings()
    if not self.UISettings then
        if isvalid(self.__cppinst) then
            self:InitUISettings()
        end
    end
    return self.UISettings
end

function LuaUIBaseView:OverrideUISettingsByKv(settingsKey, settingsValue)
    local bOverride = false
    if self.GetUISettings then
        local uiSettingsRunning = self:GetUISettings()
        local oldValue = uiSettingsRunning[settingsKey]
        if oldValue == nil or type(oldValue) == type(settingsValue) then
            uiSettingsRunning[settingsKey] = settingsValue
            bOverride = true
            if not VersionUtil.EnablePerformanceMode() and IsInEditor() and self.UINavID then
                local uiSettingsStatic = UITable[self.UINavID]
                local staticValue = uiSettingsStatic[settingsKey]
                if settingsValue ~= oldValue or settingsValue ~= staticValue then
                    logwarning('[LayerControll Debug - OverrideUISettings] OverrideUISettingsByKv uiSettings Override RuntimeValue', self.UINavID, ' key:', settingsKey, 'value:', settingsValue)
                end
            end
        end
    end
    if not bOverride then
        logerror('[LayerControll Debug - OverrideUISettings] OverrideUISettingsByKv uiSettings Override 失败请定位原因 ', self.UINavID, ' key:', settingsKey, 'value:', settingsValue)
    end
end

--------------------------------------------------------------------------
--- 销毁
--------------------------------------------------------------------------
function LuaUIBaseView:Destroy()
    -- logframe("[ Lua GC Debug ] ******* LuaUIBaseView:Destroy() -> ", self, self._cname, self.__cppinst)
    self._curUIState = EUIState.Release
    rawset(self, "_is_enable_", false)
    self:RemoveAllLuaEvent()
    self:RemoveAllInputActionBindings()
    if self._closeEvent then
        self._closeEvent:Release()
    end
    if self._showEvent then
        self._showEvent:Release()
    end
    if self._hideEvent then
        self._hideEvent:Release()
    end
    Timer.FastRemoveObjTimer(self)
    LuaTickController:Get():RemoveTick(self)
    -- logframe('#[UI改造父子关系优化Life]----------- ###############################【ui Release】############################### ', self, self._cname)
    local cppInst = self:GetCppInst()
    if isvalid(cppInst) then
        self:RemoveFromParent()
        local fLuaInsHasRelease = cppInst.LuaInsHasRelease
        if fLuaInsHasRelease then
            fLuaInsHasRelease(cppInst)
        end
    else
        -- logframe("[ GameFlow Debug ] ******* Lua Clean All UI------------ this luains exist but cppins not -> ", self, self._cname, self.__cppinst)
    end
    
    self._recoveryForShowVisibility = nil
    self._recoveryForHideVisibility = nil
    self._restoreCacheInfo = nil

    rawset(self, "_wtParent", nil)
end

function LuaUIBaseView:CheckParent(bConstruct)
    --- weak
    -- if self.GetViewParent then
    --     local nowParent = self:GetViewParent(true)
    --     local lastParent = rawget(self, "_wtParent")
    --     if getfromweak(lastParent) ~= nowParent then
    --         if lastParent and lastParent.RemoveChild_CallFromChild then
    --             lastParent:RemoveChild_CallFromChild(self)
    --         end
    --         self._wtParent = makeweak(nowParent)
    --         if nowParent and nowParent.AddChild_CallFromChild then
    --             nowParent:AddChild_CallFromChild(self)
    --         end
    --     end
    --     -- -- Only check in editor
    --     -- if IsInEditor() then
    --     --     self:_CheckSubUIValid(nowParent)
    --     -- end
    -- end

    --- normal
    if self.GetViewParent then
        local nowParent
        if bConstruct then
            nowParent = self:GetViewParent(true)
        else
            nowParent = nil
        end

        local lastParent = rawget(self, "_wtParent")
        if lastParent ~= nowParent then
            if lastParent and lastParent.RemoveChild_CallFromChild then
                -- if self._cname ~= "LuaUIBaseView" and ALIVE_COUNT_CLEAR_SUBUI_LOG then
                --     local aliveCount, bResult = Facade.UIManager:DecAliveCount(self.UINavID, self.UITokenID)
                --     if bResult and not VersionUtil.IsShipping() then
                --         loginfo('[Low Memory Log - AliveCount]-----------DecAliveCount RemoveChild_CallFromChild', self, self._cname,'当前aliveCount：', aliveCount, self.UITokenID)
                --     end
                -- end
                lastParent:RemoveChild_CallFromChild(self)
            end
            self._wtParent = nowParent
            if nowParent and nowParent.AddChild_CallFromChild then
                -- if self._cname ~= "LuaUIBaseView" and ALIVE_COUNT_CLEAR_SUBUI_LOG then
                --     local aliveCount, bResult = Facade.UIManager:AddAliveCount(self.UINavID, self.UITokenID)
                --     if bResult and not VersionUtil.IsShipping() then
                --         loginfo('[Low Memory Log - AliveCount]-----------AddAliveCount AddChild_CallFromChild', self, self._cname,'当前aliveCount：', aliveCount, self.UITokenID)
                --     end
                -- end
                nowParent:AddChild_CallFromChild(self)
            end
        end
    -- -- Only check in editor
    -- if IsInEditor() then
    --     self:_CheckSubUIValid(nowParent)
    -- end
    end
end

function LuaUIBaseView:_CheckSubUIValid(nowParent)
    -- Check subui reference here
    if nowParent and type(nowParent) == "table" and nowParent:IsChildOf(LuaUIBaseView) then
        local uiSetting = self:GetUISettings()
        local uiID = self.UINavID
        local parentUISetting = nowParent:GetUISettings()
        if uiSetting and parentUISetting and uiSetting.SubUIs and not table.isempty(uiSetting.SubUIs) then
            -- First check if all sub uis have been loaded
            local bCheckFail = false
            local subUINotLoaded
            for _, subUI in ipairs(uiSetting.SubUIs) do
                if not Facade.UIManager:CheckUIHasBeenLoaded(subUI) then
                    bCheckFail = true
                    subUINotLoaded = subUI
                    break
                end
            end

            -- Check if self is referenced by parent in sub ui
            local parentUIID = nowParent.UINavID
            if bCheckFail then
                local parentSubUIs = parentUISetting.SubUIs
                if parentSubUIs then
                    for i, subUI in ipairs(parentSubUIs) do
                        if subUI == uiID then
                            bCheckFail = false
                            break
                        end
                    end
                end
            end

        -- if bCheckFail then
        --     local errMsg = string.format([[
        --     This ui should be reference by parent in UITable.SubUIs !!!
        --     Sub UI not loaded: %s,
        --     Self UI: %s,
        --     Parent UI: %s.
        --     ]],
        --     UIName2ID.GetBPPathByID(subUINotLoaded), UIName2ID.GetBPPathByID(uiID), UIName2ID.GetBPPathByID(parentUIID))
        --     error(errMsg)
        -- end
        end
    end
end

function LuaUIBaseView:OnNativeConstruct()
    -- 弥补蓝图中预先放置的子蓝图的初始化
    -- local parent = self:LuaGetParent()
    -- logerror('#[UI改造父子关系优化Life]----------- ###############################【OnNativeConstruct】 自动 Open 开始了分割线############################### ', self, self._cname, self.__cppinst, '当前父：', parent)
    self:CheckParent(true)
    if self._cname ~= "LuaUIBaseView" and ALIVE_COUNT_CLEAR_SUBUI_LOG then
        local aliveCount, bResult = Facade.UIManager:AddAliveCount(self.UINavID, self.UITokenID)
        if bResult and not VersionUtil.IsShipping() then
            loginfo('[Low Memory Log - AliveCount]-----------AddAliveCount OnNativeConstruct', self, self._cname,'当前aliveCount：', aliveCount, self.UITokenID)
        end
    end
    trycall(self.TryOpen, self)
    self._bInLayer = true
end

function LuaUIBaseView:OnNativeDestruct()
    -- logerror('#[UI改造父子关系优化Life]----------- ###############################【OnNativeDestruct】 自动 Close 开始了分割线###############################', self, self._cname, self.__cppinst)
    self:CheckParent(true)
    if self._cname ~= "LuaUIBaseView" and ALIVE_COUNT_CLEAR_SUBUI_LOG then
        local aliveCount, bResult = Facade.UIManager:DecAliveCount(self.UINavID, self.UITokenID)
        if bResult and not VersionUtil.IsShipping() then
            loginfo('[Low Memory Log - AliveCount]-----------DecAliveCount OnNativeDestruct', self, self._cname,'当前aliveCount：', aliveCount, self.UITokenID)
        end
    end
    trycall(self.TryMute, self)
    self._bInLayer = false
end

---@overload fun(UIWidgetBase, OnAddToParent)
function LuaUIBaseView:OnAddToParent(parent)
    -- loginfo('#[UI改造父子关系优化Life]------------------------ 【OnAddToParent】', self, self._cname, self.__cppinst, '当前父：', parent)
    if not Facade.UIManager:IsFrameContainer(parent) then
        local bIgnoreWorldForceGc = rawget(parent, "_bIgnoreWorldForceGc") or false
        self:SetIsIgnoreWorldForceGc(bIgnoreWorldForceGc)
    end
end

---@overload fun(UIWidgetBase, OnRemoveFromParent)
function LuaUIBaseView:OnRemoveFromParent()
end

--------------------------------------------------------------------------
--- UI生命周期
--------------------------------------------------------------------------
function LuaUIBaseView:OnResInitialized()
    trycall(self.OnInitResData, self)
end

function LuaUIBaseView:AsyncLoadSubUIs(UICacheList, fLoadCallback, bBlocking)
    bBlocking = setdefault(bBlocking, true)
    -- self._SubUIStub = self._SubUIStub or {}
    local fWrapLoadCallback =
        CreateCallBack(
        function(selfIns)
            if bBlocking then
                Facade.UIManager:EnableInput(EInputChangeReason.AssetLoading)
            end
            safecall(fLoadCallback)
        end,
        self
    )

    if bBlocking then
        Facade.UIManager:DisableInput(EInputChangeReason.AssetLoading)
    end

    local uiBatchHandle = BatchUIHandle:NewIns(UICacheList, fWrapLoadCallback, self)
    uiBatchHandle:AsyncLoadRes(true, false)
    return uiBatchHandle
end


--------------------------------------------------------------------------
--- UI生命周期 - 数据处理与缓存（自定义一组交互据的存储和恢复）
--- 同一实例手动存储后，自动在下一次OnShowBegin时触发的数据覆盖
--------------------------------------------------------------------------
function LuaUIBaseView:SaveCustomData(...)
    self._saveCustomInfo = UIUtil.CreateCacheInfo(self.UINavID, ...)
    local fCallbackIns, callDelegate = CreateCallBack(function (self)
        local cacheInfo = self._saveCustomInfo
        if cacheInfo and type(cacheInfo) == GlobalStr.TABLE then
            cacheInfo.params = cacheInfo.params or {}
            local length = cacheInfo.params.n or table.maxn(cacheInfo.params)
            trycall(self.InitCustomData, self, unpack(cacheInfo.params, 1, length))
        end
    end, self)
    
    self._delayCustomCallbackData = {
        fCallbackIns = fCallbackIns,
        callDelegate = callDelegate
    }
end

function LuaUIBaseView:ExecCustomDataOnce()
    if self._delayCustomCallbackData then
        local fCallbackIns = self._delayCustomCallbackData.fCallbackIns
        local callDelegate = self._delayCustomCallbackData.callDelegate
        if not callDelegate.bExec then
            fCallbackIns()
        end
        self._delayCustomCallbackData = nil
        self._saveCustomInfo = nil
    end
end

function LuaUIBaseView:ClearCustomData()
    self._delayCustomCallbackData = nil
    self._saveCustomInfo = nil
end

--- OnShowBegin之前
--- OnClose和放回对象池时清空
function LuaUIBaseView:InitCustomData(...)
    trycall(self.OnInitCustomData, self, ...)
end

--------------------------------------------------------------------------
--- UI生命周期 - 数据处理与缓存（默认存外部参数）
--- 栈UI被集体销毁后，可以一键恢复
--------------------------------------------------------------------------
function LuaUIBaseView:InitExtraData(...)
    local uiSettings = self:GetUISettings()
    local layer = uiSettings.UILayer
    if self.UINavID and self._bNeedRestore and layer == EUILayer.Stack then
        self._restoreCacheInfo = UIUtil.CreateCacheInfo(self.UINavID, ...)
    end
    trycall(self.OnInitExtraData, self, ...)
end

function LuaUIBaseView:TryGetRestoreCacheInfo()
    local uiSettings = self:GetUISettings()
    local layer = uiSettings.UILayer
    if self._restoreCacheInfo and self._bNeedRestore and layer == EUILayer.Stack then
        return self._restoreCacheInfo
    end
end

--------------------------------------------------------------------------
--- UI生命周期 - 数据处理与缓存（栈管理器调用，根据idx存储）
--- 同一实例的多次状态，可以根据层级idx存储和恢复
--------------------------------------------------------------------------
--- MS24 cacheExtraDataByIdx
function LuaUIBaseView:CacheExtraDataByIdx(idx, ...)
    local uiSettings = self:GetUISettings()
    local layer = uiSettings.UILayer
    if self.UINavID and self._bNeedRestoreEachIdx and layer == EUILayer.Stack then
        declare_if_nil(self, "_mapIdx2CacheInfo", {})
        self._mapIdx2CacheInfo[idx] = UIUtil.CreateCacheInfo(self.UINavID, ...)
    end
end

--- MS24 cacheExtraDataByIdx
function LuaUIBaseView:TryGetRestoreCacheInfoByIdx(idx)
    local uiSettings = self:GetUISettings()
    local layer = uiSettings.UILayer
    if self._mapIdx2CacheInfo and self._bNeedRestoreEachIdx and layer == EUILayer.Stack then
        return self._mapIdx2CacheInfo[idx]
    end
end

--- MS24 cacheExtraDataByIdx
function LuaUIBaseView:TryClearExtraDataByIdx(idx, ...)
    local uiSettings = self:GetUISettings()
    local layer = uiSettings.UILayer
    if self.UINavID and self._bNeedRestoreEachIdx and layer == EUILayer.Stack then
        if self._mapIdx2CacheInfo[idx] then
            self._mapIdx2CacheInfo[idx] = nil
        end
    end
end

--------------------------------------------------------------------------
--- UI生命周期 (开启)
--------------------------------------------------------------------------
function LuaUIBaseView:TryOpen()
    -- logframe('@LuaUIBaseView:TryOpen-----------TryOpen', self, self._cname)
    -- self._wtPreParent = makeweak(self:LuaGetParent())
    self:Open()
    -- 特殊逻辑，界面默认隐藏
    if self.bViewInitialHide then
        -- tricky 的写法，没有以下两行，隐藏的状态无法通过RecursiveLife传递到SubUI
        Facade.UIManager:ForceCheckPendingUIParents()
        self._curUIState = EUIState.Show
        self:Hide(true, true)
    else
        self:Show()
    end
    if self._openEvent then
        self._openEvent:Invoke(self)
    end
end

---构造后加入视口时进入此生命周期
-- Open会递归调用已经设置为【可见】的子节点的Open
-- 禁止手动调用
---@overload fun(UIWidgetBase, Open)
function LuaUIBaseView:Open()
    if self._curUIState == EUIState.Open then
        -- logerror('#[UI ChildWidgets Controll]-------- 【LuaUIBaseView】childWidgets Open 重复了', self, self._cname, self.__cppinst, '当前父：', self:LuaGetParent())
        return
    end
    
    self._curUIState = EUIState.Open
    self._bOpenThisFrame = true

    local childWidgets = rawget(self, "_childWidgets")
    if childWidgets then
        for i = 1, #childWidgets do
            local childIns = childWidgets[i]
            if not hasdestroy(childIns) then -- release的时候需要清理childwidgets关系
                if rawget(childIns, "bIsUIWidgetBase") then
                    if childIns:IsVisible() then
                        -- loginfo('#[UI ChildWidgets Controll]------------------------ childWidgets 【直接子】 手动 Open 的 bIsUIWidgetBase 子UI列表为：k, v', childIns, childIns._cname, childIns.__cppinst, '当前父ui： ', self, self._cname, self.__cppinst)
                        trycall(childIns.Open, childIns)
                    end
                end
            else
                logframe("##UIClose -- LuaUIBaseView the widget isnt valid, cant call Close", childIns)
            end
        end
    end
    -- logerror('#[UI ChildWidgets Controll]-------- ###############################【LuaUIBaseView】childWidgets 根 Call Open结束分割线###############################', self, self._cname, self.__cppinst)
    -- if AnalysisUtil.ShouldTraceUI() then
    --     AnalysisUtil.StartUILife(self, "OnOpen")
    --     trycall(self.OnOpen, self)
    --     AnalysisUtil.StopUILife(self, "OnOpen")
    -- else
    --     trycall(self.OnOpen, self)
    -- end
    self:_CallLifeFunction(self._curUIState)

    if self:_SupportAutoNavigation() then
        self:RegisterNavigationFunction()
    end
end

--------------------------------------------------------------------------
--- UI生命周期 (关闭)
--------------------------------------------------------------------------
-- 禁止手动调用
---@overload fun(UIWidgetBase, Close)
function LuaUIBaseView:Close()
    self:ClearCustomData()
    -- logframe('#[UI改造父子关系优化Life]----------- ###############################【Close流程启动！！！！】 Close 开始了分割线###############################', self, self._cname, self.__cppinst,'CurUIState:', self._curUIState)
    if self._curUIState == EUIState.Mute or self._curUIState == EUIState.Close then
        logwarning(
            "#[UI改造父子关系优化Life]----------- LuaUIBaseView:Close() 当前UI已经Close过了",
            self._cname,
            __DebugOnly_UIState(self._curUIState)
        )
        return
    end
    self._curUIState = EUIState.Close
    self._lifeAniSpeedScale = DEFAULT_LIFEANI_SPEED

    self._pendingUIState = nil
    self:_InterruptLifeAni(EUIState.Close)
    if self.IsAnyAnimationPlayingRealtime then
        if self:IsValid() then
            if self:IsAnyAnimationPlayingRealtime() then
                self:StopAllAnimations()
            end
        end
    end

    --- 互为替代PLAN B
    -- local childWidgets = rawget(self, "_childWidgets")
    -- if childWidgets then
    --     for i = 1, #childWidgets do
    --         local childIns = childWidgets[i]
    --         trycall(Facade.UIManager.FinalCloseUI, Facade.UIManager, childIns, true, true)
    --     end
    -- end

    if isinvalid(self) or not self:IsValid() then
        logwarning("LuaUIBaseView:Close() self is invalid self:", self, self._cname, self.__cppinst, "isinvalid:", isinvalid(self), " self:IsValid()", self:IsValid())
        return
    end
    
    self:RemoveFromParent()

    --- SHE3
    trycall(Facade.UIManager.UnbindWhenLuaPendingKill, Facade.UIManager, self)
end

function LuaUIBaseView:TryMute()
    if self._curUIState == EUIState.Mute then
        if not VersionUtil.IsShipping() then
            local bExist = Facade.UIManager:IsAliveCountExist(self.UINavID, self.UITokenID)
            if bExist ~= nil then
                local aliveCount = Facade.UIManager:GetAliveCount(self.UINavID)
                logwarning(
                    "[Low Memory Log - AliveCount]----------- LuaUIBaseView:TryMute() 当前UI已经Mute过了",
                    '当前aliveCount：',
                    aliveCount,
                    self.UITokenID,
                    self._cname,
                    __DebugOnly_UIState(self._curUIState)
                )
            else
                local aliveCount = Facade.UIManager:GetAliveCount(self.UINavID)
                logwarning(
                    "[Low Memory Log - AliveCount]----------- LuaUIBaseView:TryMute() 当前UI已经Mute过了",
                    self.UITokenID,
                    self._cname,
                    __DebugOnly_UIState(self._curUIState)
                )
            end
        end
        return
    elseif self._curUIState == EUIState.Release then
        if not VersionUtil.IsShipping() then
            -- loginfo(
            --     "#[UI改造父子关系优化Life]----------- LuaUIBaseView:TryMute() 当前UI已经Release过, 不再触发Close",
            --     self._cname,
            --     __DebugOnly_UIState(self._curUIState)
            -- )
        end
        return
    elseif self._curUIState == EUIState.Show then
        -- logwarning(
        --     "#[UI改造父子关系优化Life]----------- LuaUIBaseView:TryMute() 当前UI从Show直接Mute，需要调用Hide",
        --     self._cname,
        --     __DebugOnly_UIState(self._curUIState)
        -- )
        self:Hide(true, true)
    elseif self._curUIState == EUIState.Deactivate then
        -- logwarning(
        --     "#[UI改造父子关系优化Life]----------- LuaUIBaseView:TryMute() 当前UI从Pause直接Mute",
        --     self._cname,
        --     __DebugOnly_UIState(self._curUIState)
        -- )
    end
    -- logerror('#[UI ChildWidgets Controll]-------- ###############################【LuaUIBaseView】childWidgets 根 Call Close结束分割线###############################', self, self._cname, self.__cppinst)
    -- if AnalysisUtil.ShouldTraceUI() then
    --     AnalysisUtil.StartUILife(self, "OnClose")
    --     trycall(self.OnClose, self)
    --     AnalysisUtil.StopUILife(self, "OnClose")
    -- else
    --     trycall(self.OnClose, self)
    -- end
    self:_InterruptLifeAni(EUIState.Close)
    self:_CallLifeFunction(EUIState.Close)
    if self._closeEvent then
        self._closeEvent:Invoke(self)
    end
    self:RemoveAllLuaEvent()
    self:RemoveAllInputActionBindings()
    self._curUIState = EUIState.Mute

    UIReleaseUtil.MuteRelease(self)
end

function LuaUIBaseView:PreCppInstRelease()
    loginfo("LuaUIBaseView:PreCppInstRelease()", self._debugName)

    local parent = rawget(self, "_wtParent")
    if parent and type(parent) == "table" then
        local fRemoveChild = parent.RemoveChild_CallFromChild
        if fRemoveChild then
            fRemoveChild(parent, self)
        end
    end
    self._wtParent = nil

    CppObject.PreCppInstRelease(self)

    self:Release()
end

function LuaUIBaseView:Release()
    UIWidgetBase.Release(self)
    -- self:UnRefUserData()
end

function LuaUIBaseView:UnRefUserData()
    --slua.nativeClearTable(self)
    for key, value in pairs(self) do
        local vt = type(value)
        if vt == GlobalStr.USERDATA then
            self[key] = nil
        end
    end
end

---@param bImmediate boolean
function LuaUIBaseView:TryClose(bImmediate)
    bImmediate = setdefault(bImmediate, false)
    if self._curUIState == EUIState.Mute or self._curUIState == EUIState.Close or self._pendingUIState == EUIState.Close then
        logwarning(
            "#[UI改造父子关系优化Life]----------- LuaUIBaseView:TryClose() 当前UI已经Close或者Mute过了",
            self._cname,
            __DebugOnly_UIState(self._curUIState)
        )
        local uiSettings = self.UINavID and UITable[self.UINavID] or nil
        if uiSettings then
            local layerType = uiSettings.UILayer
            if layerType == EUILayer.Sub then
                local aliveCount = Facade.UIManager:GetAliveCount(self.UINavID)
                if aliveCount <= 0 and not VersionUtil.IsShipping() and ALIVE_COUNT_CLEAR_SUBUI_LOG then
                    loginfo("[Low Memory Log - AliveCount]  TryClose SubUI TryAliveCountClearResRef CheckSubUINavIDIsAnyResRef --------------Mute Call ", uiSettings.BPKey, self.UINavID, self.UITokenID)
                    Facade.UIManager:TryAliveCountClearResRef(self.UINavID, self.UITokenID)
                end
            end
        end
        return
    end
    self._pendingUIState = EUIState.Close
    local bExecResult = self:Hide(true, bImmediate)
    if not bExecResult then
        self:_TrueClose()
    end
end

function LuaUIBaseView:_TrueClose()
    self:Close()
end

-- Add call back when close
---@param fOpenCallback function
function LuaUIBaseView:AddOpenCallBack(fOpenCallback, caller)
    if not self._openEvent then
        self._openEvent = LuaEvent:NewIns("OnPanel" .. self._cname .. "Open")
    end

    return self._openEvent:AddListener(fOpenCallback, caller)
end

function LuaUIBaseView:RemoveOpenCallBack(fOpenCallback, caller)
    if self._openEvent then
        self._openEvent:RemoveListener(fOpenCallback, caller)
    end
end

-- Add call back when close
---@param fCloseCallback function
function LuaUIBaseView:AddCloseCallBack(fCloseCallback, caller)
    if not self._closeEvent then
        self._closeEvent = LuaEvent:NewIns("OnPanel" .. self._cname .. "Close")
    end

    return self._closeEvent:AddListener(fCloseCallback, caller)
end

function LuaUIBaseView:RemoveCloseCallBack(fCloseCallback, caller)
    if self._closeEvent then
        self._closeEvent:RemoveListener(fCloseCallback, caller)
    end
end

function LuaUIBaseView:AddShowCallBack(fShowCallBack, caller)
    if not self._showEvent then
        self._showEvent = LuaEvent:NewIns("OnPanel" .. self._cname .. "Show")
    end
    return self._showEvent:AddListener(fShowCallBack, caller)
end

function LuaUIBaseView:RemoveShowCallBack(fShowCallBack, caller)
    if self._showEvent then
        self._showEvent:RemoveListener(fShowCallBack, caller)
    end
end

function LuaUIBaseView:AddHideCallBack(fHideCallBack, caller)
    if not self._hideEvent then
        self._hideEvent = LuaEvent:NewIns("OnPanel" .. self._cname .. "Hide")
    end
    return self._hideEvent:AddListener(fHideCallBack, caller)
end

function LuaUIBaseView:RemoveHideCallBack(fHideCallBack, caller)
    if self._hideEvent then
        self._hideEvent:RemoveListener(fHideCallBack, caller)
    end
end

function LuaUIBaseView:OnLifeAniChanged(bFinished)
    local uiSettings = self:GetUISettings()
    if uiSettings then
        if (uiSettings.UILayer == EUILayer.Stack or uiSettings.UILayer == EUILayer.Pop or uiSettings.UILayer == EUILayer.Top) and uiSettings.IsAnimBlock then
            if bFinished then
                Facade.UIManager:EnableInput(EInputChangeReason.AnimPlaying)
                --- 正式修复进SHE1
                -- if not self.bIsHud then
                --     if self._curUIState == EUIState.ShowBegin and self._animOriVisibility then
                --         logwarning('[UI生命周期 可视性 revert]', self._cname, __DebugOnly_UIState(self._curUIState), self._animOriVisibility, self.Visibility)
                --         self:SetVisibility(self._animOriVisibility)
                --         self._animOriVisibility = nil
                --     end
                -- end
            else
                Facade.UIManager:DisableInput(EInputChangeReason.AnimPlaying)
                --- 正式修复进SHE1
                -- if not self.bIsHud then
                --     if self._curUIState == EUIState.ShowBegin and self._animOriVisibility == nil then
                --         if not UIUtil.CheckVisibilityIsVisible(self.Visibility) then
                --             self._animOriVisibility = self.Visibility
                --             logwarning('[UI生命周期 可视性 temp]', self._cname, __DebugOnly_UIState(self._curUIState), self._animOriVisibility, self.Visibility)
                --             self:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
                --         end
                --     end
                -- end
            end
        end
    end
end

--------------------------------------------------------------------------
--- 默认动画相关API
--------------------------------------------------------------------------
---@param anim BPAnimation
---@param ... args Timer.DelayCall Life Params
function LuaUIBaseView:_PlayLifeAnim(anim, ...)
    local loopNumber = 1
    local playMode = EUMGSequencePlayMode.Forward
    local speedScale = self._lifeAniSpeedScale
    local bRestoreState = false
    if anim then
        self:PlayWidgetAnim(anim, loopNumber, playMode, speedScale, bRestoreState)
        self:OnLifeAniChanged(false)
        local endTime = anim:GetEndTime()
        self.lifeAnimHandle = Timer.DelayCall(endTime, self._OnLifeAniFinished, self, self._curUIState, ...)
    end
end

--- LifeAni被打断后不走这里
function LuaUIBaseView:_OnLifeAniFinished(uiState, ...)
    -- 这里加HD判断是为了兼顾手游和PC出现的两个不同BUG
    --bug=128896993 [【CN】【安卓】【必现】拖拽道具进入安全箱以及移出安全箱道具图标都无法立即刷新](https://tapd.woa.com/r/t?id=128896993&type=bug)
    --bug=126248303 [【PC】【必现】【CCB接】局外页签互切导致鼠标在特勤处出现（操作步骤在BUG单内）](https://tapd.woa.com/r/t?id=126248303&type=bug)
    if DFHD_LUA == 1 then
        if uiState == EUIState.ShowBegin and self._curUIState == EUIState.ShowBegin then
            -- self:_ClearCustomAni(EAutoAnimType.FlowIn)
            self:OnLifeAniChanged(true)
            self:_TrueShow(...)
        elseif uiState == EUIState.ShowBegin then
            -- 否则会导致SubUI ShowBegin/Show 无法递归
            self._bOpenThisFrame = false
        elseif uiState == EUIState.HideBegin and self._curUIState == EUIState.HideBegin then
            -- self:_ClearCustomAni(EAutoAnimType.FlowOut)
            self:OnLifeAniChanged(true)
            self:_TrueHide(...)
        else
        end
    else
        if uiState == EUIState.ShowBegin then
            -- self:_ClearCustomAni(EAutoAnimType.FlowIn)
            self:OnLifeAniChanged(true)
            self:_TrueShow(...)
        elseif uiState == EUIState.HideBegin then
            -- self:_ClearCustomAni(EAutoAnimType.FlowOut)
            self:OnLifeAniChanged(true)
            self:_TrueHide(...)
        else
        end
    end

    -- if self.lifeAnimHandle then
    --     Timer.CancelDelay(self.lifeAnimHandle)
    --     self.lifeAnimHandle = nil
    -- end
end

function LuaUIBaseView:_ClearCustomAni(eAutoAnimType)
    local customAniName = self._mapAnimType2CustomName[eAutoAnimType]
    local str = eAutoAnimType == EAutoAnimType.FlowIn and "FlowIn" or "FlowOut"
    if customAniName then
        loginfo("LuaUIBaseView:ManuelSetLifeAniName >> 清空特殊", str, "动画：", eAutoAnimType, customAniName)
        self._mapAnimType2CustomName[eAutoAnimType] = nil
        if self[customAniName] and self:IsAnimationPlayingExactly(self[customAniName]) then
            self:StopWidgetAnim(self[customAniName])
            self:PlayWidgetAnimAt(self[customAniName], self[customAniName]:GetEndTime())
        end
    end
end

function LuaUIBaseView:_InterruptLifeAni(nextUIState)
    if nextUIState == EUIState.Close then
        if self.lifeAnimHandle then
            Timer.CancelDelay(self.lifeAnimHandle)
            self.lifeAnimHandle = nil
        end
        self:OnLifeAniChanged(true)
        return
    end
    local UISettings = self:GetUISettings()
    if (UISettings) then
        local flowInAniName =
            UIAnimHelper.GetAnimNameInPriority(self, EAutoAnimType.FlowIn, self._mapAnimType2CustomName)
        local flowInAni = flowInAniName and self[flowInAniName] or nil
        local flowOutAniName =
            UIAnimHelper.GetAnimNameInPriority(self, EAutoAnimType.FlowOut, self._mapAnimType2CustomName)
        local flowOutAni = flowOutAniName and self[flowOutAniName] or nil
        if flowInAni or flowOutAni then
            self:_ClearLifeAni(nextUIState, flowInAni, flowOutAni)
            if self.lifeAnimHandle then
                Timer.CancelDelay(self.lifeAnimHandle)
                self.lifeAnimHandle = nil
            end
        end
    end
end

---@param nextUIState EUIState
---@param flowInAni anim
---@param flowOutAni anim
function LuaUIBaseView:_ClearLifeAni(nextUIState, flowInAni, flowOutAni)
    local bDoubleProcess = false
    local bInAniProcess = false
    local bOutAniProcess = false

    if flowInAni and self:IsAnimationPlayingExactly(flowInAni) then
        self:StopWidgetAnim(flowInAni)
        if (self._curUIState == EUIState.HideBegin
            or self._curUIState == EUIState.Hide
            or nextUIState == EUIState.HideBegin
            or nextUIState == EUIState.Hide) then
            if (self._curUIState == EUIState.HideBegin or self._curUIState == EUIState.Hide) and
                (nextUIState == EUIState.ShowBegin or nextUIState == EUIState.Show) then
                self:PlayWidgetAnimAt(flowInAni, flowInAni:GetEndTime())
                bInAniProcess = true
            end
        else
            self:PlayWidgetAnimAt(flowInAni, flowInAni:GetEndTime())
            if not VersionUtil.IsShipping() then
                loginfo(
                    "[ LifeAni 打断 ] _ClearLifeAni InAni被【异常】打断, 打断结果",
                    self._cname,
                    "当前状态 ",
                    __DebugOnly_UIState(self._curUIState),
                    "目标状态 ",
                    __DebugOnly_UIState(nextUIState),
                    "是否在播放InAni：",
                    flowInAni and self:IsAnimationPlayingExactly(flowInAni),
                    "是否在播放OutAni：",
                    flowOutAni and self:IsAnimationPlayingExactly(flowOutAni)
                )
            end
        end
        self:OnLifeAniChanged(true)
    end
    if flowOutAni and self:IsAnimationPlayingExactly(flowOutAni) then
        self:StopWidgetAnim(flowOutAni)
        if (self._curUIState == EUIState.ShowBegin
            or self._curUIState == EUIState.Show
            or nextUIState == EUIState.ShowBegin
            or nextUIState == EUIState.Show)
         then
            if (self._curUIState == EUIState.ShowBegin or self._curUIState == EUIState.Show) and
                (nextUIState == EUIState.HideBegin or nextUIState == EUIState.Hide) then
                self:PlayWidgetAnimAt(flowOutAni, flowOutAni:GetEndTime())
                bOutAniProcess = true
            end
        else
            self:PlayWidgetAnimAt(flowOutAni, flowOutAni:GetEndTime())
            if not VersionUtil.IsShipping() then
                loginfo(
                    "[ LifeAni 打断 ] _ClearLifeAni OutAni 被【异常】打断",
                    self._cname,
                    "当前状态 ",
                    __DebugOnly_UIState(self._curUIState),
                    "目标状态 ",
                    __DebugOnly_UIState(nextUIState),
                    "是否在播放InAni：",
                    flowInAni and self:IsAnimationPlayingExactly(flowInAni),
                    "是否在播放OutAni：",
                    flowOutAni and self:IsAnimationPlayingExactly(flowOutAni)
                )
            end
        end
        self:OnLifeAniChanged(true)
    end

    bDoubleProcess = bInAniProcess and bOutAniProcess
    if bDoubleProcess then
        logerror("[ LifeAni 打断 ] bDoubleProcess 双重打断，意外情况 ", self:IsAnimationPlayingExactly(flowInAni),  self:IsAnimationPlayingExactly(flowOutAni))
    end
end

function LuaUIBaseView:IsInSuccessionUIProcess(beginUIState)
    if (self._curUIState == EUIState.Mute or self._curUIState == EUIState.Release) then
         if not VersionUtil.IsShipping() then
            logwarning("[ LifeAni 打断 ] 表现型生命周期在销毁后进入", self._cname, __DebugOnly_UIState(self._curUIState))
         end
        return true
    end
    if beginUIState == EUIState.ShowBegin then
        if (self._curUIState == EUIState.ShowBegin or self._curUIState == EUIState.Show) and
            UIUtil.CheckVisibilityIsVisible(self.Visibility) then
            if not VersionUtil.IsShipping() then
                logwarning("[ LifeAni 打断 ] 表现型生命周期重复进入", self._cname, __DebugOnly_UIState(self._curUIState))
            end
            return true
        end
    elseif beginUIState == EUIState.HideBegin then
        if (self._curUIState == EUIState.HideBegin or self._curUIState == EUIState.Hide) and
            not UIUtil.CheckVisibilityIsVisible(self.Visibility) then
            if not VersionUtil.IsShipping() then
                logwarning("[ LifeAni 打断 ] 表现型生命周期重复进入", self._cname, __DebugOnly_UIState(self._curUIState))
            end
            return true
        end
    else
        return false
    end
end

--------------------------------------------------------------------------
--- UI生命周期 (展示表现)
--------------------------------------------------------------------------
-- Show，使UI进入显示态，可以触发多次
-- 可以手动调用
function LuaUIBaseView:Show(bInteractive, bImmediately)
    local bExecResult = false
    bImmediately = setdefault(bImmediately, false)
    if self:IsInSuccessionUIProcess(EUIState.ShowBegin) then
        return bExecResult
    end
    self:ExecCustomDataOnce()
    self:_InterruptLifeAni(EUIState.ShowBegin)

    if bImmediately then
        trycall(self._OnRecursiveLife, self, EUIState.ShowBegin)
        self:_TrueShow(bInteractive)
    else
        local animName = UIAnimHelper.GetAnimNameInPriority(self, EAutoAnimType.FlowIn, self._mapAnimType2CustomName)
        if animName and self[animName] and self._cname ~= "ReddotPointNew" then
            -- if self.UISettings and self.UISettings.UILayer == EUILayer.Stack then
            --     logframe("[ 转场 Debug ]--------------[UISettings Ani]  Play Show Ani", self._cname)
            -- else
            --     logframe("[UISettings Ani]  Play Show Ani", self._cname)
            -- end
            trycall(self._OnRecursiveLife, self, EUIState.ShowBegin)
            self:_PlayLifeAnim(self[animName], bInteractive)
        else
            trycall(self._OnRecursiveLife, self, EUIState.ShowBegin)
            self:_TrueShow(bInteractive)
        end
    end
    bExecResult = true
    return bExecResult
end

function LuaUIBaseView:_TrueShow(bInteractive)
    bInteractive = setdefault(bInteractive, false)
    if not self._bFirstOpen then
        if self._shouldRecoveryVisibility and not UIUtil.CheckVisibilityIsVisible(self.Visibility) then
            self._recoveryForHideVisibility = self.Visibility
            local showVisibility
            if self._recoveryForShowVisibility and UIUtil.CheckVisibilityIsVisible(self._recoveryForShowVisibility) then
                showVisibility = self._recoveryForShowVisibility
                self._recoveryForShowVisibility = nil
                if not hasdestroy(self) then
                    -- logframe(
                    --     "#[UI ChildWidgets Controll ShowHide]------------------------reset to visible",
                    --     UIUtil.GetDebugVisibility2String(showVisibility),
                    --     self,
                    --     self:GetCppInst()
                    -- )
                end
            else
                if not self._bOpenThisFrame then
                    if bInteractive then
                        showVisibility = ESlateVisibility.Visible
                    else
                        showVisibility = ESlateVisibility.SelfHitTestInvisible
                    end
                end
            end
            if showVisibility then
                self:SetVisibility(showVisibility)
            end
        end
    end

    --@todo 处理弹窗悄悄隐藏/显示的问题，后续看看有没有更好的方案
    if self.UISettings and self.UISettings.UILayer == EUILayer.Pop then
        local popLayerController = Facade.UIManager:GetLayerControllerByType(EUILayer.Pop)
        if popLayerController then
            popLayerController:OnUIInsVisibilityChangedSilently()
        end
    end

    trycall(self._OnRecursiveLife, self, EUIState.Show)
    if self._showEvent then
        self._showEvent:Invoke(self)
    end
    self._bFirstOpen = false
    self._bOpenThisFrame = false
    self:_ClearCustomAni(EAutoAnimType.FlowIn)

    --BEGIN MODIFICATION @ VIRTUOS : 函数跟NavigationGroup有冲突，且没有页面正常使用这个方法。先移除。
    -- 刷新Focus状态, 栈UI或者模态弹窗再显示时需要获取Focus
    -- if self:_SupportAutoNavigation() then
    --     self:InitUINavigationFocusState()
    -- end
    --END MODIFICATION
end

--------------------------------------------------------------------------
--- UI生命周期
--- Show
--- Hide
--- 需要成对出现
--------------------------------------------------------------------------
--------------------------------------------------------------------------
--- UI生命周期 (隐藏表现)
--------------------------------------------------------------------------
-- Hide，使UI进入隐藏态，可以触发多次
-- 可以手动调用
function LuaUIBaseView:Hide(bCollapsed, bImmediately)
    local bExecResult = false
    bImmediately = setdefault(bImmediately, false)
    if self:IsInSuccessionUIProcess(EUIState.HideBegin) then
        return bExecResult
    end

    self:_InterruptLifeAni(EUIState.HideBegin)

    if bImmediately then
        trycall(self._OnRecursiveLife, self, EUIState.HideBegin)
        self:_TrueHide(bCollapsed)
    else
        local animName = UIAnimHelper.GetAnimNameInPriority(self, EAutoAnimType.FlowOut, self._mapAnimType2CustomName)
        local bHasAnimAutoOutRes = false -- [optimization] 移除该特性
        -- UIAnimationUtil.WidgetHasAnimation(self, UIAnimationUtil.EAnimationType.AutoOut) and UIAnimationUtil.EnablePlayDefaultWidgetAnimation(self, UIAnimationUtil.EAnimationType.AutoOut)
        local bHasAnimOut, bHasAutoAnimOut = false, false
        -- 配置的关闭动画
        if animName and self[animName] then
            -- if self.UISettings and self.UISettings.UILayer == EUILayer.Stack then
            --     logframe("[ 转场 Debug ]--------------[UISettings Ani] Play Hide Ani", self._cname)
            -- else
            --     logframe("[UISettings Ani] Play Hide Ani", self._cname)
            -- end
            trycall(self._OnRecursiveLife, self, EUIState.HideBegin)
            self:_PlayLifeAnim(self[animName], bCollapsed)
            bHasAnimOut = true
        end
        -- 自动播放的的关闭动画
        if bHasAnimAutoOutRes then
            -- logframe("[AutoAnimation Ani] Play AutoOut Ani", self._cname)
            -- 如果上面没有走HideBegin
            if not bHasAnimOut then
                trycall(self._OnRecursiveLife, self, EUIState.HideBegin)
            end
            bHasAutoAnimOut = true
        end

        if not bHasAnimOut and not bHasAutoAnimOut then
            trycall(self._OnRecursiveLife, self, EUIState.HideBegin)
            self:_TrueHide(bCollapsed)
        end
    end
    bExecResult = true
    return bExecResult
end

function LuaUIBaseView:_TrueHide(bCollapsed)
    bCollapsed = setdefault(bCollapsed, true)
    if self._shouldRecoveryVisibility and UIUtil.CheckVisibilityIsVisible(self.Visibility) then
        self._recoveryForShowVisibility = self.Visibility
        local hideVisibility
        if self._recoveryForHideVisibility and not UIUtil.CheckVisibilityIsVisible(self._recoveryForHideVisibility) then
            hideVisibility = self._recoveryForHideVisibility
            self._recoveryForHideVisibility = nil
            if not hasdestroy(self) then
                -- logframe(
                --     "#[UI ChildWidgets Controll ShowHide]------------------------reset to invisible",
                --     UIUtil.GetDebugVisibility2String(hideVisibility),
                --     self,
                --     self:GetCppInst()
                -- )
            end
        else
            -- end
            -- if not self._bOpenThisFrame then
            if bCollapsed then
                hideVisibility = ESlateVisibility.Collapsed
            else
                hideVisibility = ESlateVisibility.Hidden
            end
        end
        if hideVisibility then
            self:SetVisibility(hideVisibility)
        end
    end

    --@todo 处理弹窗悄悄隐藏/显示的问题，后续看看有没有更好的方案
    if self.UISettings and self.UISettings.UILayer == EUILayer.Pop then
        local popLayerController = Facade.UIManager:GetLayerControllerByType(EUILayer.Pop)
        if popLayerController then
            popLayerController:OnUIInsVisibilityChangedSilently()
        end
    end

    trycall(self._OnRecursiveLife, self, EUIState.Hide)
    if self._hideEvent then
        self._hideEvent:Invoke(self)
    end
    self._bFirstOpen = false
    self._bOpenThisFrame = false

    if self._pendingUIState then
        if self._pendingUIState == EUIState.Close then
            self:_TrueClose()
        elseif self._pendingUIState == EUIState.Deactivate then
            trycall(self._OnRecursiveLife, self, EUIState.Deactivate)
        end
    end
    self:_ClearCustomAni(EAutoAnimType.FlowOut)
end

--从
-- function LuaUIBaseView:_OnRecursiveLife_ShowBegin()
--     if not self._bFirstOpen and self._curUIState == EUIState.Hide then
--         trycall(self._OnRecursiveLife, self, EUIState.ShowBegin)
--     end
--     self._curUIState = EUIState.ShowBegin
--     trycall(self.OnShowBegin, self)
--     -- 驱动自动动画的播放
--     UIAnimationUtil.OnWidegetStateChanged(self)
-- end

-- function LuaUIBaseView:_OnRecursiveLife_HideBegin()
--     if self._curUIState == EUIState.Show then
--         trycall(self._OnRecursiveLife, self, EUIState.HideBegin)
--     end
--     self._curUIState = EUIState.HideBegin
--     trycall(self.OnHideBegin, self)
--     -- 驱动自动动画的播放
--     UIAnimationUtil.OnWidegetStateChanged(self)
-- end

-- function LuaUIBaseView:_OnRecursiveLife_Show()
--     if not self._bFirstOpen and self._curUIState == EUIState.ShowBegin then
--         trycall(self._OnRecursiveLife, self, EUIState.Show)
--     end
--     self._curUIState = EUIState.Show
--     trycall(self.OnShow, self)
--     -- 驱动自动动画的播放
--     UIAnimationUtil.OnWidegetStateChanged(self)
-- end

-- function LuaUIBaseView:_OnRecursiveLife_Hide()
--     if self._curUIState == EUIState.HideBegin then
--         trycall(self._OnRecursiveLife, self, EUIState.Hide)
--     end
--     self._curUIState = EUIState.Hide
--     trycall(self.OnHide, self)
--     -- 驱动自动动画的播放
--     UIAnimationUtil.OnWidegetStateChanged(self)
-- end

-- function LuaUIBaseView:_OnRecursiveLife(uiState)
--     local childWidgets = rawget(self, "_childWidgets")
--     if childWidgets then
--         for i = 1, #childWidgets do
--             local childIns = childWidgets[i]
--         -- for _, childIns in pairs(childWidgets) do
--             if not hasdestroy(childIns) then -- release的时候需要清理childwidgets关系
--                         if childIns._OnRecursiveLife then
--                             childIns:_OnRecursiveLife(uiState)
--                         end
--             end
--         end
--     end
-- end
--------------------------------------------------------------------------
--- 内置Pending生命周期Do接口
--------------------------------------------------------------------------
function LuaUIBaseView:TryGetPendingDoOnceHandle(uiState)
    logwarning('LuaUIBaseView:TryGetPendingDoOnceHandle(uiState, fCallBackFunc, caller) ------ Delay', self, __DebugOnly_UIState(uiState))
    if self._mapUIState2CallbackData then
        local callbackData = self._mapUIState2CallbackData[uiState]
        if callbackData and callbackData.callDelegate then
            return callbackData.callDelegate
        end
    end
    return nil
end

function LuaUIBaseView:DelayPendingDoOnce(uiState, fCallBackFunc, caller)
    logwarning('LuaUIBaseView:DelayPendingDoOnce(uiState, fCallBackFunc, caller) ------ Delay', self, __DebugOnly_UIState(uiState), fCallBackFunc, caller)
    declare_if_nil(self, "_mapUIState2CallbackData", {})
    local fCallbackIns, callDelegate = CreateCallBack(fCallBackFunc, caller)
    self._mapUIState2CallbackData[uiState] = {
        fCallbackIns = fCallbackIns,
        callDelegate = callDelegate
    }
    return fCallbackIns, callDelegate
end

function LuaUIBaseView:ExecPendingDoOnce(uiState)
    if self._mapUIState2CallbackData then
        local callbackData = self._mapUIState2CallbackData[uiState]
        if callbackData and callbackData.fCallbackIns and not callbackData.callDelegate.bExec then
            logwarning('LuaUIBaseView:ExecPendingDoOnce ------ Exec', self, __DebugOnly_UIState(uiState))
            callbackData.fCallbackIns()
        else
            -- if callbackData.callDelegate.bExec then
            --     logwarning('LuaUIBaseView:ExecPendingDoOnce ------ Exec Failed', self, __DebugOnly_UIState(uiState))
            -- end
        end
    end
end

function LuaUIBaseView:ClearPendingDoOnce(uiState, bClearAll)
    logwarning('LuaUIBaseView:ClearPendingDoOnce ------ Clear', self, __DebugOnly_UIState(uiState))
    if bClearAll then
        self._mapUIState2CallbackData = {}
    else
        if self._mapUIState2CallbackData then
            self._mapUIState2CallbackData[uiState] = nil
        end
    end
end

function LuaUIBaseView:_OnRecursiveLife(uiState)
    if bUseNewLifeFunction and self.bUseNewLifeFunction then
        if self._curUIState ~= uiState then
            self._curUIState = uiState
            self:RecursiveCallLuaLifeFunction(uiState)
            self:ExecPendingDoOnce(uiState)
            return
        end
    end

    local bRecursive = false
    if uiState == EUIState.ShowBegin then
        if not self._bOpenThisFrame and ((self._curUIState == EUIState.Hide) or (self._curUIState == EUIState.Deactivate) or (self._curUIState == EUIState.Init) or (self._curUIState == EUIState.Activate)) then
            bRecursive = true
        end
        self:_SetAllInputActionsIsEnabled(true)
    elseif uiState == EUIState.HideBegin then
        if (self._curUIState == EUIState.Show) or (self._curUIState == EUIState.ShowBegin) then
            bRecursive = true
        end
    elseif uiState == EUIState.Show then
        if not self._bOpenThisFrame and self._curUIState == EUIState.ShowBegin then
            bRecursive = true
        end
        if DFHD_LUA == 1 then
            self:CheckWantedInputMode(true)
        end
    elseif uiState == EUIState.Hide then
        if self._curUIState == EUIState.HideBegin then
            bRecursive = true
        end
        if DFHD_LUA == 1 then
            self:CheckWantedInputMode(false)
        end
        self:_SetAllInputActionsIsEnabled(false)
    elseif uiState == EUIState.Activate then
        if self._curUIState ~= EUIState.Activate then
            bRecursive = true
        end
    elseif uiState == EUIState.Deactivate then
        if self._curUIState ~= EUIState.Deactivate then
            bRecursive = true
        end
    end

    self._curUIState = uiState
    if bRecursive then
        local childWidgets = rawget(self, "_childWidgets")
        if childWidgets then
            for _, childIns in pairs(childWidgets) do
                childIns:_OnProcessChildRecursiveLife(uiState)
            end
        end
    end
    self:_CallLifeFunction(uiState)
    self:ExecPendingDoOnce(uiState)

    -- [optimization] 移除该特性
    -- -- 驱动自动动画的播放
    -- UIAnimationUtil.OnWidegetStateChanged(self)
end

function LuaUIBaseView:_OnProcessChildRecursiveLife(uiState)
    -- release的时候需要清理childwidgets关系
    if hasdestroy(self) then
        return
    end

    local bBreak = self:_GetBreakChildRecursiveLife(uiState)
    if bBreak then
        return
    end

    -- if ((self._curUIState == EUIState.HideBegin or self._curUIState == EUIState.Hide) and (uiState == EUIState.ShowBegin or uiState == EUIState.Show))
    -- or ((self._curUIState == EUIState.ShowBegin or self._curUIState == EUIState.Show) and (uiState == EUIState.HideBegin or uiState == EUIState.Hide)) then
    --     self:_InterruptLifeAni(uiState)
    -- end
    self:_OnRecursiveLife(uiState)
end

function LuaUIBaseView:_GetBreakChildRecursiveLife(uiState)
    local bToggle = self:_OnGetBreakChildRecursiveLifeToggle(uiState)
    if not bToggle then
        return false
    end

    --- 只做可见性测试
    local bVisibleState = false
    if
        uiState == EUIState.ShowBegin or uiState == EUIState.Show or uiState == EUIState.HideBegin or
            uiState == EUIState.Hide
     then
        bVisibleState = true
    end

    if not bVisibleState then
        return false
    end

    local bVisibleState = self:IsVisible()
    local bBreak = not bVisibleState
    return bBreak
end

function LuaUIBaseView:_OnGetBreakChildRecursiveLifeToggle(uiState)
    return false
end

local debugNameMapping = {
    [EUIState.Show] = "OnShow",
    [EUIState.Hide] = "OnHide",
    [EUIState.Open] = "OnOpen",
    [EUIState.Close] = "OnClose",
    [EUIState.ShowBegin] = "OnShowBegin",
    [EUIState.HideBegin] = "OnHideBegin",
    [EUIState.Activate] = "OnActivate",
    [EUIState.Deactivate] = "OnDeactivate"
}

local function fGetLifeFunction(self, state)
    local cls = self:Class()
    if not cls.lifeFunctionMapping then
        cls.lifeFunctionMapping = {
            [EUIState.Show] = cls.OnShow,
            [EUIState.Hide] = cls.OnHide,
            [EUIState.Open] = cls.OnOpen,
            [EUIState.Close] = cls.OnClose,
            [EUIState.ShowBegin] = cls.OnShowBegin,
            [EUIState.HideBegin] = cls.OnHideBegin,
            [EUIState.Activate] = cls.OnActivate,
            [EUIState.Deactivate] = cls.OnDeactivate
        }
    end
    return cls.lifeFunctionMapping[state]
end

function LuaUIBaseView:_CallLifeFunction(state)
    local foo = fGetLifeFunction(self, state)
    if not foo then
        return
    end

    if bGlobalEnableLifeFunctionFraming and bEnableLifeFunctionFraming then
        Facade.LuaFramingManager:RegisterFrameTask(self._InternalCallLifeFunction, self, {state}, nil, EFrameTaskPriority.High)
    else
        self:_InternalCallLifeFunction(state)
    end
end

function LuaUIBaseView:_InternalCallLifeFunction(state)
    local foo = fGetLifeFunction(self, state)
    if AnalysisUtil.ShouldTraceUILife() then
        local debugName = debugNameMapping[state] or "Default"
        AnalysisUtil.StartUILife(self, debugName)
        trycall(foo, self)
        AnalysisUtil.StopUILife(self, debugName)
    else
        trycall(foo, self)
    end

    -- local debugName = debugNameMapping[state]
    -- if state == EUIState.Show or state == EUIState.ShowBegin or state == EUIState.Hide or state == EUIState.HideBegin then
    --     -- logwarning('[ LifeCircle - Debug ] LuaUIBaseView:_InternalCallLifeFunction(state) ', self._cname, debugName, self, 'curUIState:', __DebugOnly_UIState(self._curUIState))
    -- elseif state == EUIState.Activate or state == EUIState.Deactivate then
    --     -- logerror('[ LifeCircle - Debug ] LuaUIBaseView:_InternalCallLifeFunction(state) ', self._cname, debugName, self, 'curUIState:', __DebugOnly_UIState(self._curUIState))
    -- else
    --     -- loginfo('[ LifeCircle - Debug ] LuaUIBaseView:_InternalCallLifeFunction(state) ', self._cname, debugName, self, 'curUIState:', __DebugOnly_UIState(self._curUIState))
    -- end
end

function LuaUIBaseView:IsInHideState()
    return self._curUIState == EUIState.Hide
end

function LuaUIBaseView:IsInHideBeginState()
    return self._curUIState == EUIState.HideBegin
end

function LuaUIBaseView:IsInShowState()
    return self._curUIState == EUIState.Show
end

function LuaUIBaseView:IsInShowBeginState()
    return self._curUIState == EUIState.ShowBegin
end
--------------------------------------------------------------------------
--- 其他
--------------------------------------------------------------------------
-- 有可能不是从Lua里设置，而是从BP或者Native里设置，也会回调到这个函数

-- Get whether ui is valid for now
function LuaUIBaseView:IsValid()
    -- logframe('@LuaUIBaseView:IsValid------',self:IsInViewport(), self._bInLayer, self._cname)
    return self.__cppinst ~= nil and self._bInLayer
end

function LuaUIBaseView:IsInLayer()
    return self._bInLayer
end

function LuaUIBaseView:IsInViewport()
    return self._bInLayer or (self.__cppinst ~= nil and self.__cppinst:IsInViewport())
end

--------------------------------------------------------------------------
---动画相关API
--------------------------------------------------------------------------
---@param animation BPAnimation
---@param loopNumber number
---@param playMode EUMGSequencePlayMode
---@param speedScale number
---@param bRestoreState boolean
function LuaUIBaseView:PlayWidgetAnim(animation, loopNumber, playMode, speedScale, bRestoreState)
    loopNumber = setdefault(loopNumber, 1)
    playMode = setdefault(playMode, EUMGSequencePlayMode.Forward)
    speedScale = setdefault(speedScale, 1)
    bRestoreState = setdefault(bRestoreState, false)

    self:PlayAnimation(animation, 0, loopNumber, playMode, speedScale, bRestoreState)
end

function LuaUIBaseView:PlayAnimation(animation, startTime, loopNumber, playMode, speedScale, bRestoreState)
    if self:IsAnimationPlayingExactly(animation) then
        local flowInAniName =
            UIAnimHelper.GetAnimNameInPriority(self, EAutoAnimType.FlowIn, self._mapAnimType2CustomName)
        local flowInAni = flowInAniName and self[flowInAniName] or nil
        local flowOutAniName =
            UIAnimHelper.GetAnimNameInPriority(self, EAutoAnimType.FlowOut, self._mapAnimType2CustomName)
        local flowOutAni = flowOutAniName and self[flowOutAniName] or nil
        if (flowInAni and flowInAni == animation) or (flowOutAni and flowOutAni == animation) then
            if not VersionUtil.IsShipping() then
                loginfo(
                    "[ LifeAni 打断 ] LuaUIBaseView:PlayAnimation The animation u playing now is duplicated of [Life Auto Anim] !!! please check",
                    animation
                )
            end
            return
        end
    end
    self:GetCppInst():PlayAnimation(animation, startTime, loopNumber, playMode, speedScale, bRestoreState)
end

function LuaUIBaseView:SkipAnimation(animation)
    self:PlayWidgetAnimAt(animation, animation:GetEndTime(), 1, EUMGSequencePlayMode.Forward, 1, false)
end

---@param animation BPAnimation
---@param startTime number
---@param loopNumber number
---@param playMode EUMGSequencePlayMode
---@param speedScale number
---@param bRestoreState boolean
function LuaUIBaseView:PlayWidgetAnimAt(animation, startTime, loopNumber, playMode, speedScale, bRestoreState)
    startTime = setdefault(startTime, 0)
    loopNumber = setdefault(loopNumber, 1)
    playMode = setdefault(playMode, EUMGSequencePlayMode.Forward)
    speedScale = setdefault(speedScale, 1)
    bRestoreState = setdefault(bRestoreState, false)

    self:PlayAnimation(animation, startTime, loopNumber, playMode, speedScale, bRestoreState)
end

---@param animation BPAnimation
---@param startTime number
---@param endTime number
---@param loopNumber number
---@param playMode EUMGSequencePlayMode
---@param speedScale number
---@param bRestoreState boolean
function LuaUIBaseView:PlayAnimTimeRange(animation, startTime, endTime, loopNumber, playMode, speedScale, bRestoreState)
    startTime = setdefault(startTime, 0)
    endTime = setdefault(endTime, 0)
    loopNumber = setdefault(loopNumber, 1)
    playMode = setdefault(playMode, EUMGSequencePlayMode.Forward)
    speedScale = setdefault(speedScale, 1)
    bRestoreState = setdefault(bRestoreState, false)

    self:PlayAnimationTimeRange(animation, startTime, endTime, loopNumber, playMode, speedScale, bRestoreState)
end

---@param animation BPAnimation
function LuaUIBaseView:StopWidgetAnim(animation)
    self:StopAnimation(animation)
end

--------------------------------------------------------------------------
--- 新增 延迟或当前显示状态下播放的动画 New
--------------------------------------------------------------------------
---@param animation BPAnimation
function LuaUIBaseView:PlayDelayAnimWhenShow(animation)
    if self:IsAnimationPlayingExactly(animation) then
        self:StopWidgetAnim(animation)
    end
    if self:IsInShowState() then
        self:PlayWidgetAnim(animation)
    else
        self:DelayPendingDoOnce(LuaUIBaseView.EUIState.Show, function (self)
            self:PlayWidgetAnim(animation)
        end, self)
    end
end

function LuaUIBaseView:SkipDelayAniWhenShow(animation)
    if self:IsAnimationPlayingExactly(animation) then
        self:StopWidgetAnim(animation)
    end
    local pendingDelegate = self:TryGetPendingDoOnceHandle(LuaUIBaseView.EUIState.Show)
    if self:IsInShowState() and pendingDelegate == nil then
        self:PlayWidgetAnimAt(animation, animation:GetEndTime())
    end
end

--------------------------------------------------------------------------
--- 动画 生命周期
--------------------------------------------------------------------------
---@param animation BPAnimation
function LuaUIBaseView:OnWidgetAnimationStarted(animation)
    if not VersionUtil.IsShipping() then
        logerror('[ LifeAni 打断 ] 动画开始 ', animation)
    end
    if self:IsValid() then
        trycall(self.OnAnimStarted, self, animation)
    end
end

---@param animation BPAnimation
function LuaUIBaseView:OnWidgetAnimationFinished(animation)
    if not VersionUtil.IsShipping() then
        logerror('[ LifeAni 打断 ] 动画结束 ', animation)
    end
    if self:IsValid() then
        trycall(self.OnAnimFinished, self, animation)
    end
end

--------------------------------------------------------------------------
---DragAndDrop
--------------------------------------------------------------------------
function LuaUIBaseView:OnDragDetected(InGeometry, InDragDropEvent, Operation)
end

function LuaUIBaseView:OnDragCancelled(InDragDropEvent, Operation)
end

function LuaUIBaseView:OnDragEnter(InGeometry, InDragDropEvent, Operation)
end

function LuaUIBaseView:OnDragLeave(InDragDropEvent, Operation)
end

function LuaUIBaseView:OnDragOver(InGeometry, InDragDropEvent, InOperation)
end

function LuaUIBaseView:OnDrop(InGeometry, InDragDropEvent, Operation)
end

--------------------------------------------------------------------------
---Touch、Click
--------------------------------------------------------------------------
function LuaUIBaseView:OnImmediateClicked()
    return true
end

function LuaUIBaseView:OnClicked()
end

function LuaUIBaseView:OnNativeOnTouchStarted(inGeometry, inGestureEvent)
end

function LuaUIBaseView:OnNativeOnTouchMoved(inGeometry, inGestureEvent)
end

function LuaUIBaseView:OnNativeOnTouchEnded(inGeometry, inGestureEvent)
end

function LuaUIBaseView:OnNativePaint(context)
end

--------------------------------------------------------------------------
---UI子类关注override
--------------------------------------------------------------------------
-- UI资源加载时触发, 用于初始化UI资源数据逻辑（仅触发一次）
function LuaUIBaseView:OnInitResData()
end

-- UI构造时触发, 用于初始化外部传参UI数据逻辑，由UIManager触发（仅触发一次）
-- 子节点蓝图不会触发该方法
function LuaUIBaseView:OnInitExtraData(...)
end

-- UI显示前触发，用于恢复上次存储的数据
-- 这些交互数据在执行后、Close时、放回对象池时清空
function LuaUIBaseView:OnInitCustomData(...)
end


-- -- UI 以下任一条件可触发OnOpen：
-- -- 1.UI打开时触发, 在【不更换父容器】的情况下，仅触发一次
-- -- 2.AddChild时触发
-- -- 3.AddToViewport时触发
-- function LuaUIBaseView:OnOpen()
-- end

-- -- UI 以下任一条件可触发OnClose：
-- -- 1.UI关闭时触发，在【不更换父容器】的情况下，仅触发一次
-- -- 2.RemoveFromParent时触发
-- -- 3.ClearChildren时触发子节点OnClose
-- function LuaUIBaseView:OnClose()
-- end

-- -- UI 以下任一条件可触发OnShow：
-- -- 1.Open后显示时触发Show生命周期
-- -- 2.栈中UI被出栈造成的显示触发
-- -- 3.手动调用Show触发
-- -- 4.UI在缓存实例策略中被激活时触发[一般只有EUILayer.Stack等大界面加入缓存池]
-- function LuaUIBaseView:OnShow()
-- end

-- -- UI 以下任一条件可触发OnHide
-- -- 1.Close前触发Hide生命周期
-- -- 2.栈中UI被入栈造成的隐藏触发
-- -- 3.手动调用Hide触发
-- -- 4.UI在缓存实例策略中被熄火时触发[一般只有EUILayer.Stack等大界面加入缓存池]
-- function LuaUIBaseView:OnHide()
-- end

-- --- 和动画开始有关的生命周期
-- function LuaUIBaseView:OnShowBegin()
-- end

-- function LuaUIBaseView:OnHideBegin()
-- end

-- UI每帧更新
function LuaUIBaseView:Tick(MyGeometry, dt)
end

-- 动画结束回调
function LuaUIBaseView:OnAnimFinished(anim)
end

function LuaUIBaseView:OnAnimStarted(anim)
end

function LuaUIBaseView:OnStateUIEnter(stateAction)
end

function LuaUIBaseView:OnStateUIExit(stateAction)
end

function LuaUIBaseView:OnStateUIUpdate(dt)
end

--------------------------------------------------------------------------
--- ui bind functions.
--------------------------------------------------------------------------

---@param button UIButton
---@param callback function
function LuaUIBaseView:AddUIButtonClickedEvent(button, callback)
    if button == nil then
        return
    end
    button:Event("OnClicked", callback, self)
end

local white_list = {
    UIWidgetBase = true,
    UIWidgetSwitcher = true,
    UITextBlock = true,
    UISizeBox = true,
    UIProgressBar = true,
    UINamedSlot = true,
    UIListView = true

    -- UIImage = true
}

-- function LuaUIBaseView:Wnd(widgetName, widgetLuaClass, ...)
--     self:RefreshName2Widget()
--     local targetWidgetLuaIns = self[widgetName]
-- 	if not targetWidgetLuaIns then
-- 		targetWidgetLuaIns = self:GetWidgetByName(widgetName)
-- 	end

-- 	if targetWidgetLuaIns then
-- 		if type(targetWidgetLuaIns) == "table" then
-- 		elseif widgetLuaClass then
-- 			local name = widgetLuaClass._cname
-- 			if white_list[name] then
--                 targetWidgetLuaIns = UILightWidget:NewOn(targetWidgetLuaIns)
-- 			else
-- 				targetWidgetLuaIns = widgetLuaClass:NewOn(targetWidgetLuaIns, ...)
-- 			end
-- 		else
-- 		end
-- 		self._wndWidgets[widgetName] = targetWidgetLuaIns
--         -- self[widgetName] = targetWidgetLuaIns
-- 	else
-- 		return nil
-- 	end

--     local bIgnoreWorldForceGc = rawget(self, '_bIgnoreWorldForceGc') or false
--     if targetWidgetLuaIns.SetIsIgnoreWorldForceGc then
--         targetWidgetLuaIns:SetIsIgnoreWorldForceGc(bIgnoreWorldForceGc)
--     else
--         logframe('targetWidgetLuaIns.SetIsIgnoreWorldForceGc is nil', targetWidgetLuaIns)
--     end

-- 	return targetWidgetLuaIns
-- end

-- Wnd Optimization
---@generic T
---@param widgetName string @Widget original name
---@param widgetLuaClass T|UIWidgetBase @Widget luaclass
---@return T
function LuaUIBaseView:Wnd(widgetName, widgetLuaClass, ...)
    declare_if_nil(self, "_mapName2bNothingFound", {})
    if not widgetName or widgetName == "" then
        if not VersionUtil.EnablePerformanceMode() then
            logerror(
                widgetName .. "[控件不存在，请处理废弃Wnd代码或修复名称] WndOperation failed, widgetName is valid",
                widgetName,
                debug.traceback()
            )
        end
        return nil
    end
    local targetWidgetLuaIns = self:WndOperation(widgetName, widgetLuaClass, ...)
    if not targetWidgetLuaIns then
        if DFHD_LUA == 1 then
            local widgetNamePC = widgetName .. "_PCOnly"
            if self._mapName2bNothingFound[widgetNamePC] then
                -- error(widgetNamePC.." WndOperation failed, doesn't exist")
                if IsInEditor() then
                    logwarning(widgetName .. "[控件不存在，请处理废弃Wnd代码或修复名称] WndOperation failed, doesn't exist")
                end
                return nil
            else
                targetWidgetLuaIns = self:WndOperation(widgetNamePC, widgetLuaClass, ...)
                if not targetWidgetLuaIns then
                    self._mapName2bNothingFound[widgetNamePC] = true
                    if IsInEditor() then
                        logwarning(widgetName .. "[控件不存在，请处理废弃Wnd代码或修复名称] WndOperation failed, doesn't exist")
                    end
                end
            end
        else
            -- error(widgetName.." WndOperation failed, doesn't exist")
            if IsInEditor() then
                logwarning(widgetName .. "[控件不存在，请处理废弃Wnd代码或修复名称] WndOperation failed, doesn't exist")
            end
            return nil
        end
    end
    return targetWidgetLuaIns
end

function LuaUIBaseView:WndOperation(widgetName, widgetLuaClass, ...)
    if self._wndWidgets == nil then
        return
    end
    local targetWidgetLuaIns = self._wndWidgets[widgetName]
    if targetWidgetLuaIns then
        return targetWidgetLuaIns
    end

    local bRelyLuaBinding = false
    targetWidgetLuaIns, bRelyLuaBinding = self:GetWidgetByName_CheckLuaBinding(widgetName)
    if ULAI.IsSubClassOf(targetWidgetLuaIns, UDFThemeImage) or ULAI.IsSubClassOf(targetWidgetLuaIns, UDFThemeShadowImage) then
        bRelyLuaBinding = true
    end
    if not bRelyLuaBinding then
        --- logwarning('LuaUIBaseView:WndOperation bRelyLuaBinding = false', widgetName, widgetLuaClass._cname)
        return targetWidgetLuaIns
    end

    if targetWidgetLuaIns then
        if type(targetWidgetLuaIns) == "table" then
            targetWidgetLuaIns:SetIsIgnoreWorldForceGc(self._bIgnoreWorldForceGc)
        elseif widgetLuaClass then
            local name = widgetLuaClass._cname
            if white_list[name] then
                -- if not bRelyLuaBinding then
                targetWidgetLuaIns = UILightWidget:NewOn(targetWidgetLuaIns)
            else
                targetWidgetLuaIns = widgetLuaClass:NewOn(targetWidgetLuaIns, ...)
            end
            targetWidgetLuaIns:SetIsIgnoreWorldForceGc(self._bIgnoreWorldForceGc)
        else
        end
        self._wndWidgets[widgetName] = targetWidgetLuaIns
    else
        return nil
    end

    return targetWidgetLuaIns
end

function LuaUIBaseView:CheckWantedInputMode(bShow)
    if self.WantedInputMode then
        if self.WantedInputMode ~= EGPInputModeType.None then
            local InputID = self._cname
            UGPInputHelper.WantInputMode_Enum(self, self.WantedInputMode, InputID, bShow, false)
        end
    else
        logwarning("LuaUIBaseView:CheckWantedInputMode self.WantedInputMode is nil", self._cname, self.__cppinst)
    end
end

--------------------------------------------------------------------------
--- 当需要开启对象池时，必需要重载的方法
--- 注意：开启对象池管理的luaIns必须使用唯一生成的gid作为引用，
--- 以免被回收使用的luaIns被误判为前身！！！
--------------------------------------------------------------------------
--- 类在这里写除事件监听以外的复用逻辑
--- 没写则默认走自身构造和析构（不递归！）
--- 必须仔细考虑有哪些事情要做!!!否则不可轻易开启对象池管理
--- override function
function LuaUIBaseView:OnActivate(...)
end

function LuaUIBaseView:OnDeactivate()
end

--------------------------------------------------------------------------
--- 内置快捷键接口
--------------------------------------------------------------------------

local function ProcessInputValidateParams(self, inputMonitor, validate)
    if validate == nil then
        validate = inputMonitor:DefaultUIValidate(self)
    end
    validate = inputMonitor:BuildValidateConditions(validate)
    return validate
end


---@param actionName string
---@param keyEvent EInputEvent
---@param func function
---@param caller table|nil
---@param priority integer|nil|EDisplayInputActionPriority
---@param bPersistent boolean|nil
---@param validate DisplayInputActionValidate|nil
---@return integer|unknow handle
function LuaUIBaseView:AddInputActionBinding(actionName, keyEvent, func, caller, priority, validate)
    local inputMonitor = Facade.UIManager:GetInputMonitor()
    validate = ProcessInputValidateParams(self, inputMonitor, validate)
    local newHandle = inputMonitor:AddDisplayActionBinding(actionName, keyEvent, func, caller, priority, false, validate)
    table.insert(self._displayActionHandles, newHandle)
    -- 有可能在添加时候，UI还未显示。需要在此时立即Disable
    if self._curUIState ~= EUIState.Show and self._curUIState ~= EUIState.ShowBegin then
        inputMonitor:SetDisplayActionIsEnabledForHandle(newHandle, false)
    end
    return newHandle
end

function LuaUIBaseView:RemoveInputActionBinding(handle)
    if table.contains(self._displayActionHandles, handle) then
        local inputMonitor = Facade.UIManager:GetInputMonitor()
        inputMonitor:RemoveDisplayActoinBingingForHandle(handle)
        table.removebyvalue(self._displayActionHandles, handle)
    end
end

--BEGIN MODIFICATION @ VIRTUOS : Extension of UI input
--添加按键长按绑定事件
function LuaUIBaseView:AddHoldInputActionBinding(actionName, func, caller, priority, validate)
    local inputMonitor = Facade.UIManager:GetInputMonitor()
    validate = ProcessInputValidateParams(self, inputMonitor, validate)
    local newHandle = inputMonitor:AddHoldDisplayActionBinding(actionName, func, caller, priority, false, validate)
    table.insert(self._displayActionHandles, newHandle)
    -- 有可能在添加时候，UI还未显示。需要在此时立即Disable
    if self._curUIState ~= EUIState.Show and self._curUIState ~= EUIState.ShowBegin then
        inputMonitor:SetDisplayActionIsEnabledForHandle(newHandle, false)
    end
    return newHandle
end

--添加按键长按按下事件
function LuaUIBaseView:AddHoldInputActionPressedBinding(actionHandle, func, caller)
    if table.contains(self._displayActionHandles, actionHandle) then
        local inputMonitor = Facade.UIManager:GetInputMonitor()
        inputMonitor:AddHoldDisplayActionPressedBinding(actionHandle, func, caller)
    end
end

--添加按键长按释放事件
function LuaUIBaseView:AddHoldInputActionReleaseBinding(actionHandle, func, caller)
    if table.contains(self._displayActionHandles, actionHandle) then
        local inputMonitor = Facade.UIManager:GetInputMonitor()
        inputMonitor:AddHoldDisplayActionReleaseBinding(actionHandle, func, caller)
    end
end

--添加按键长按进度回调事件
function LuaUIBaseView:AddHoldInputActionProgressedBinding(actionHandle, func, caller)
    if table.contains(self._displayActionHandles, actionHandle) then
        local inputMonitor = Facade.UIManager:GetInputMonitor()
        inputMonitor:AddHoldDisplayActionProgressedBinding(actionHandle, func, caller)
    end
end

--移除按键长按绑定
function LuaUIBaseView:RemoveHoldInputActionBinding(handle)
    if table.contains(self._displayActionHandles, handle) then
        local inputMonitor = Facade.UIManager:GetInputMonitor()
        inputMonitor:RemoveHoldDisplayActoinBingingForHandle(handle)
        table.removebyvalue(self._displayActionHandles, handle)
    end
end

--添加轴输入绑定
function LuaUIBaseView:AddAxisInputActionBinding(actionName, func, caller, priority, validate)
    local inputMonitor = Facade.UIManager:GetInputMonitor()
    validate = ProcessInputValidateParams(self, inputMonitor, validate)
    local newHandle = inputMonitor:AddDisplayAxisActionBinding(actionName, func, caller, priority, false, validate)
    table.insert(self._displayActionHandles, newHandle)
    -- 有可能在添加时候，UI还未显示。需要在此时立即Disable
    if self._curUIState ~= EUIState.Show and self._curUIState ~= EUIState.ShowBegin then
        inputMonitor:SetDisplayActionIsEnabledForHandle(newHandle, false)
    end
    return newHandle
end

--END MODIFICATION

function LuaUIBaseView:AddInputActionValidateWhitelistWidget(inWidget)
    inWidget = inWidget or self
    local inputMonitor = Facade.UIManager:GetInputMonitor()
    inputMonitor:AddValidateWhitelistWidget(inWidget)
end

function LuaUIBaseView:RemoveInputActionValidateWhitelistWidget(inWidget)
    inWidget = inWidget or self
    local inputMonitor = Facade.UIManager:GetInputMonitor()
    inputMonitor:RemoveValidateWhitelistWidget(inWidget)
end

function LuaUIBaseView:RemoveAllInputActionBindings()
    local inputMonitor = Facade.UIManager:GetInputMonitor()
    for _, handle in ipairs(self._displayActionHandles) do
        inputMonitor:RemoveDisplayActoinBingingForHandle(handle)
    end
    self._displayActionHandles = {}
end

function LuaUIBaseView:_SetAllInputActionsIsEnabled(bIsEnabled)
    local inputMonitor = Facade.UIManager:GetInputMonitor()
    for _, handle in ipairs(self._displayActionHandles) do
        inputMonitor:SetDisplayActionIsEnabledForHandle(handle, bIsEnabled)
    end
end

--- 注意，局外栈UI由CommonBar处理、Pop在LayerController处理，两者不允许使用这个方法
--- 用于不注册CommonBar的栈UI，TopUI等界面
function LuaUIBaseView:BindBackAction(fCallback, caller)
    local uiLayer = EUILayer.Stack
    local uiSetting = self:GetUISettings()
    if uiSetting and uiSetting.UILayer then
        uiLayer = uiSetting.UILayer
    end
    local priorityEnum = MapLayer2InputActionPriority[uiLayer]
    if priorityEnum == nil then
        logwarning('注意，未找到对应层级的输入优先级，请手动使用inputMonitor注册DisplayAction')
    end
    priorityEnum = setdefault(priorityEnum, EDisplayInputActionPriority.UI_Stack)
    self._fBackCallbackIns = CreateCallBack(fCallback, caller)

    if (UISimulatorUtil.IsAndroidPlatfrom() or UISimulatorUtil.IsOpenHarmonyPlatfrom()) then
        self:_BindBackAction_Android(priorityEnum)
    elseif DFHD_LUA == 1 then
        self:_BindBackAction_HD(priorityEnum)
    end
end

function LuaUIBaseView:_OnViewHandleAndroidBackBtnClick()
    if not (UISimulatorUtil.IsAndroidPlatfrom() or UISimulatorUtil.IsOpenHarmonyPlatfrom()) then
        return
    end
    local bEnable = Facade.UIManager.bEnableAndroidBack
    if bEnable then
        logwarning('[ Platform Input - 多平台 esc|androidback ] LuaUIBaseView:_OnViewHandleAndroidBackBtnClick()')
        if self._fBackCallbackIns then
            self._fBackCallbackIns()
        end
    end
end

function LuaUIBaseView:UnBindBackAction()
    local uiLayer = EUILayer.Stack
    local uiSetting = self:GetUISettings()
    if uiSetting and uiSetting.UILayer then
        uiLayer = uiSetting.UILayer
    end
    local priorityEnum = MapLayer2InputActionPriority[uiLayer]
    if priorityEnum == nil then
        logwarning('注意，未找到对应层级的输入优先级，请手动使用inputMonitor取消DisplayAction')
    end
    priorityEnum = setdefault(priorityEnum, EDisplayInputActionPriority.UI_Stack)

    if (UISimulatorUtil.IsAndroidPlatfrom() or UISimulatorUtil.IsOpenHarmonyPlatfrom()) then
        self:_UnBindBackAction_Android(priorityEnum)
    elseif DFHD_LUA == 1 then
        self:_UnBindBackAction_HD(priorityEnum)
    end
end

function LuaUIBaseView:_BindBackAction_Android(priorityEnum)
    local bNeedSet = false
    if self._aBackActionHandle == nil then
        self._aBackActionHandle = self:AddInputActionBinding(
            "Android_Back", EInputEvent.IE_Pressed, self._OnViewHandleAndroidBackBtnClick, self, priorityEnum)
        bNeedSet = (priorityEnum ~= EDisplayInputActionPriority.UI_Stack and priorityEnum ~= EDisplayInputActionPriority.UI_Pop)
    end

    if UISimulatorUtil.IsSimulatorEnable() then
        --- Editor Test
        if self._aTestBackActionHandle == nil then
            self._aTestBackActionHandle = self:AddInputActionBinding(
                "A_Back", EInputEvent.IE_Pressed, self._OnViewHandleAndroidBackBtnClick, self, priorityEnum)
            bNeedSet = (priorityEnum ~= EDisplayInputActionPriority.UI_Stack and priorityEnum ~= EDisplayInputActionPriority.UI_Pop)
        end
    end
    if bNeedSet then
        local inputMonitor = Facade.UIManager:GetInputMonitor()
        inputMonitor:SetActionsPriorityGate(priorityEnum, true)
    end
end

function LuaUIBaseView:_BindBackAction_HD(priorityEnum)
end

function LuaUIBaseView:_UnBindBackAction_Android(priorityEnum)
    local bNeedSet = false
    if self._aBackActionHandle then
        self:RemoveInputActionBinding(self._aBackActionHandle)
        self._aBackActionHandle = nil
        bNeedSet = (priorityEnum ~= EDisplayInputActionPriority.UI_Stack and priorityEnum ~= EDisplayInputActionPriority.UI_Pop)
    end

    if UISimulatorUtil.IsSimulatorEnable() then
        --- Editor Test
        if self._aTestBackActionHandle then
            self:RemoveInputActionBinding(self._aTestBackActionHandle)
            self._aTestBackActionHandle = nil
            bNeedSet = (priorityEnum ~= EDisplayInputActionPriority.UI_Stack and priorityEnum ~= EDisplayInputActionPriority.UI_Pop)
        end
    end
    if bNeedSet then
        local inputMonitor = Facade.UIManager:GetInputMonitor()
        inputMonitor:SetActionsPriorityGate(priorityEnum, false)
    end
end

function LuaUIBaseView:_UnBindBackAction_HD(priorityEnum)
end

return LuaUIBaseView
