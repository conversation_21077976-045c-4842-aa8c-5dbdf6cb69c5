----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMArmedForce)
----- LOG FUNCTION AUTO GENERATE END -----------



local ArmedForceEventLogic = {}
local ArmedForceDataLogic = require "DFM.Business.Module.ArmedForceModule.Logic.ArmedForce.ArmedForceDataLogic"
local ArmedForceViewLogic = require "DFM.Business.Module.ArmedForceModule.Logic.ArmedForce.ArmedForceViewLogic"
local CheckEquipLogic = require "DFM.Business.Module.ArmedForceModule.Logic.Equipment.CheckEquipLogic"
local ArmedForceConfig = require "DFM.Business.Module.ArmedForceModule.ArmedForceConfig"
local ArmedForcePresetLogic = require "DFM.Business.Module.ArmedForceModule.Logic.ArmedForce.ArmedForcePresetLogic"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local ItemBaseTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemBaseTool"
local RentalDataLogic = require "DFM.Business.Module.ArmedForceModule.Logic.Rental.RentalDataLogic"

---------------------------------------------------------------------------------
--- Logic可以拆分多个，用于业务逻辑的编写，部分需要开放的接口由Module进行转发
---------------------------------------------------------------------------------
ArmedForceEventLogic.AddListeners = function()
    Server.HeroServer.Events.evtHeroesUpdated:AddListener(ArmedForceEventLogic._OnHeroesUpdated)
    loginfo('[ ArmedForce ]-------------Data@@@ArmedForceEventLogic.AddListeners')
    Server.InventoryServer.Events.evtPostUpdateDeposData:AddListener(ArmedForceEventLogic._OnInventoryUpdated)
    Server.InventoryServer.Events.evtInventoryFetchFinished:AddListener(ArmedForceEventLogic._OnInventoryFetchFinished)
    Server.GameModeServer.Events.evtMatchModeUpdated:AddListener(ArmedForceEventLogic._OnMatchModeUpdated)
    ArmedForceConfig.evtSlotViewClicked:AddListener(ArmedForceEventLogic._OnSlotViewClicked)
    Server.HeroServer.Events.evtSOLUsedHeroIdChanged:AddListener(ArmedForceEventLogic._OnHeroUsed)
    Server.CollectionServer.Events.evtFetchCollectionData:AddListener(ArmedForceEventLogic._OnPostCollectionDataInit)
    Server.CollectionServer.Events.evtUpdateCollectionData:AddListener(ArmedForceEventLogic._OnPostCollectionDataChanged)
    Server.InventoryServer.Events.evtPostSOLMeleeDataChange:AddListener(ArmedForceEventLogic._OnPostSOLMeleeDataChanged)
    --- MP
    Server.InventoryServer.Events.evtPostMPDataInit:AddListener(ArmedForceEventLogic._OnPostMPDataInit)
    Server.InventoryServer.Events.evtPostMPDataChange:AddListener(ArmedForceEventLogic._OnPostMPDataChanged)
    Server.TeamServer.Events.evtJoinTeam:AddListener(ArmedForceEventLogic._OnJoinTeam)
    -- 开始匹配时玩家配装数据经分埋点
    Server.MatchServer.Events.evtStartMatching:AddListener(ArmedForceEventLogic._OnStartMatching)
    --监听创建主流程选人ui
    Server.MatchServer.Events.evtShowSquadPickWidget:AddListener(ArmedForceEventLogic._OnShowSquadPickWidget)
    --监听创建SOL入局事件视频ui
    Server.MatchServer.Events.evtShowSOLEventVideoWidget:AddListener(ArmedForceEventLogic._OnShowSOLEventVideoWidget)
    -- 动态价格变化
    Server.ShopServer.Events.evtDynamicGuidPriceFinishFetch:AddListener(ArmedForceEventLogic._OnDynamicGuidPriceFinishFetch)

    -- 监听武器外观变化
    Server.GunsmithServer.Events.evtCSWAssemblyApplySkinRes:AddListener(ArmedForceEventLogic._OnCSWAssemblyApplySkinRes)

    Server.ArmedForceServer.Events.evtRentalApplyProcess:AddListener(ArmedForceEventLogic._OnRentalApplyProcess)
end

ArmedForceEventLogic.RemoveListeners = function()
    Server.HeroServer.Events.evtHeroesUpdated:RemoveListener(ArmedForceEventLogic._OnHeroesUpdated)
    Server.InventoryServer.Events.evtPostUpdateDeposData:RemoveListener(ArmedForceEventLogic._OnInventoryUpdated)
    Server.InventoryServer.Events.evtInventoryFetchFinished:RemoveListener(ArmedForceEventLogic._OnInventoryFetchFinished)
    Server.GameModeServer.Events.evtMatchModeUpdated:RemoveListener(ArmedForceEventLogic._OnMatchModeUpdated)
    ArmedForceConfig.evtSlotViewClicked:RemoveListener(ArmedForceEventLogic._OnSlotViewClicked)
    Server.HeroServer.Events.evtSOLUsedHeroIdChanged:RemoveListener(ArmedForceEventLogic._OnHeroUsed)
    Server.CollectionServer.Events.evtFetchCollectionData:RemoveListener(ArmedForceEventLogic._OnPostCollectionDataInit)
    Server.CollectionServer.Events.evtUpdateCollectionData:RemoveListener(ArmedForceEventLogic._OnPostCollectionDataChanged)
    Server.InventoryServer.Events.evtPostSOLMeleeDataChange:RemoveListener(ArmedForceEventLogic._OnPostSOLMeleeDataChanged)
    --- MP
    Server.InventoryServer.Events.evtPostMPDataInit:RemoveListener(ArmedForceEventLogic._OnPostMPDataInit)
    Server.InventoryServer.Events.evtPostMPDataChange:RemoveListener(ArmedForceEventLogic._OnPostMPDataChanged)
    Server.TeamServer.Events.evtJoinTeam:RemoveListener(ArmedForceEventLogic._OnJoinTeam)

    Server.MatchServer.Events.evtStartMatching:RemoveListener(ArmedForceEventLogic._OnStartMatching)
    Server.MatchServer.Events.evtShowSquadPickWidget:RemoveListener(ArmedForceEventLogic._OnShowSquadPickWidget)
    Server.MatchServer.Events.evtShowSOLEventVideoWidget:RemoveListener(ArmedForceEventLogic._OnShowSOLEventVideoWidget)
    Server.ShopServer.Events.evtDynamicGuidPriceFinishFetch:RemoveListener(ArmedForceEventLogic._OnDynamicGuidPriceFinishFetch)
    Server.GunsmithServer.Events.evtCSWAssemblyApplySkinRes:RemoveListener(ArmedForceEventLogic._OnCSWAssemblyApplySkinRes)
    Server.ArmedForceServer.Events.evtRentalApplyProcess:RemoveListener(ArmedForceEventLogic._OnRentalApplyProcess)
end

ArmedForceEventLogic._OnHeroesUpdated = function()
    ArmedForceDataLogic.GenSOLArmedForceHeroIdList()
end
ArmedForceEventLogic._OnInventoryFetchFinished = function()
    loginfo('[ ArmedForce ]-------------Data@@@ArmedForceEventLogic._OnInventoryFetchFinished')
    ArmedForceEventLogic._OnInventoryUpdated()
end

ArmedForceEventLogic._OnInventoryUpdated = function()
    loginfo('[ ArmedForce ]-------------Data@@@ArmedForceEventLogic._OnInventoryUpdated')
    ArmedForceDataLogic.GenSOLArmedForceHeroIdList()
    
    ---每次仓库变化，检查身上穿戴的槽位配装情况
    CheckEquipLogic.CheckEquipmentBeforEnterGameProcess()
    -- 仓库变化检查是否已经发送了经分埋点
    ArmedForceEventLogic._SendEquipmentChangeReport()
end
ArmedForceEventLogic._OnMatchModeUpdated = function()
    --- 地图模式变化更新提醒
    CheckEquipLogic.CheckEquipmentBeforEnterGameProcess()
end

--- MP仓库变化
ArmedForceEventLogic._OnPostMPDataInit = function()
    logwarning('[ MeleeWeapon Debug ]-------------Data@@@ MP仓库初始化 近战武器刷新成功')
    ArmedForceDataLogic.GenMPArmedForceHeroGidList()
    ArmedForceDataLogic.GenMeleeWeaponSkinInfoList()
end

--- MP仓库变化
ArmedForceEventLogic._OnPostMPDataChanged = function()
    logwarning('[ MeleeWeapon Debug ]-------------Data@@@ MP数据变更Ntf 近战武器刷新成功')
    ArmedForceDataLogic.GenMPArmedForceHeroGidList()
    ArmedForceDataLogic.GenMeleeWeaponSkinInfoList()
end

--- SOL仓库近战武器变化
ArmedForceEventLogic._OnPostSOLMeleeDataChanged = function()
    logwarning('[ MeleeWeapon Debug ]-------------Data@@@ SOLMelee数据变更Ntf 近战武器刷新成功')
    ArmedForceDataLogic.GenMeleeWeaponSkinInfoList()
end

--- 藏品数据变化
ArmedForceEventLogic._OnPostCollectionDataInit = function()
    logwarning('[ MeleeWeapon Debug ]-------------Data@@@ 藏品系统初始化 近战武器刷新成功')
    ArmedForceDataLogic.GenMeleeWeaponSkinInfoList()
end

--- 藏品数据变化
ArmedForceEventLogic._OnPostCollectionDataChanged = function()
    logwarning('[ MeleeWeapon Debug ]-------------Data@@@ 藏品数据变更Ntf 近战武器刷新成功')
    ArmedForceDataLogic.GenMeleeWeaponSkinInfoList()
end

---@param slotViewType ESlotType
ArmedForceEventLogic._OnSlotViewClicked = function(slotViewType, slotGroup)
    local bIsReady2Go, readyState = Server.MatchServer:GetIsReadytoGo()
    if bIsReady2Go then
        if readyState == 1 then
            Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.InMatching)
        else
            Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.InReadying)
        end
        return
    end
    if (not Module.Range:IsRangeSlot(slotGroup) and Server.ArmedForceServer:CheckIsRentalStatus()) then
        Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.EquipmentInRentalStatusCannotBeModifiedTips)
        return
    end
    ArmedForceViewLogic.ShowSelectionSOLPanelOnly(slotViewType, slotGroup)
end

---@param slotViewType ESlotType
ArmedForceEventLogic._OnPresetSlotViewClicked = function(slotViewType, slotGroup, item)
    ArmedForceViewLogic.ShowPresetSelectionEquipMainView(slotViewType, slotGroup, nil, nil, item)
end

ArmedForceEventLogic._OnHeroUsed = function()
    -- todo 删除？sol兵种不影响武器使用
    -- ArmedForcePresetLogic.TryUnEquipInvaliedItemForArmProcess()
    -- if Module.ArmedForce.Field:GetMainPanel() then
    --     ArmedForcePresetLogic.TryUpdateOutfitProcess()
    -- end
end

function ArmedForceEventLogic._OnJoinTeam()
    if Module.ArmedForce:HasOutfitFlowFlag() then
        Server.TeamServer:PinTeamStates(true, TeamMemberState.MemPrepareComplete)
    end
end

function ArmedForceEventLogic._OnStartMatching()
    -- 开始匹配时玩家配装数据经分埋点
    ArmedForceDataLogic.AnalyzeStartMatchingArmedForceData()
end

function ArmedForceEventLogic._OnShowSquadPickWidget(callback, caller, dsRoomId,solRoomTeamRes,needWaitGameplayConfig)
    Facade.UIManager:AsyncShowUI(UIName2ID.AssemblySquadPick, callback, caller, dsRoomId,solRoomTeamRes,needWaitGameplayConfig)
end

function ArmedForceEventLogic._OnShowSOLEventVideoWidget(solEvents,fPlayEndCallback,fGetTotalTime)
    loginfo("ArmedForceEventLogic._OnShowSOLEventVideoWidget")
    if solEvents then
        logtable(solEvents,true)
    end
    local CutSceneMediaVideoName = ""
    for k,v in pairs(solEvents or {})do
        if Module.ArmedForce:FindSOLEventRow(v) then
            local solEventRow = Module.ArmedForce:FindSOLEventRow(v)
            -- 支持特殊事件替换MediaName
            if solEventRow.EventIdReplaceMediaNameMap then
                for key, value in pairs(solEventRow.EventIdReplaceMediaNameMap or {}) do
                    local EventId = tonumber(key)
                    if table.contains(solEvents, EventId) then
                        loginfo("ArmedForceEventLogic._OnShowSOLEventVideoWidget replace", "EventId", v, "ReplaceEventId", EventId, "ReplaceCutSceneMediaVideoName", value)
                        CutSceneMediaVideoName = value
                        break
                    end
                end
            end
            -- 不存在则使用原MediaName
            if CutSceneMediaVideoName == "" and solEventRow.MediaName ~= "" then
                CutSceneMediaVideoName = solEventRow.MediaName
                loginfo("ArmedForceEventLogic._OnShowSOLEventVideoWidget origin", "EventId", v, "CutSceneMediaVideoName", CutSceneMediaVideoName)
            end
            -- 播放指定CutScene视频
            loginfo("ArmedForceEventLogic._OnShowSOLEventVideoWidget", "EventId", v, "CutSceneMediaVideoName", CutSceneMediaVideoName)
            if Module.ArmedForce:FindMediaResRow(CutSceneMediaVideoName) then
                Module.CommonWidget:ShowFullScreenVideoView(CutSceneMediaVideoName, false, false, fPlayEndCallback, nil, 0, 0, true, fGetTotalTime)
                return
            end
        end
    end
    logerror("ArmedForceEventLogic._OnShowSOLEventVideoWidget, solEvent or mediaSorce not found!!!")
    fPlayEndCallback()
end

function ArmedForceEventLogic._OnDynamicGuidPriceFinishFetch(bIsUpdated)
    CheckEquipLogic.DynamicGuidPriceFinishFetch(bIsUpdated)
end

function ArmedForceEventLogic._SendEquipmentChangeReport()
    if Module.ArmedForce.Field:GetInAssembly() and not Module.ArmedForce.Field:GetSendEquipmentChange() then
        Module.ArmedForce.Field:SetSendEquipmentChange(true)
        Server.ArmedForceServer:SendArmedForceReportOutfitReq()
    end
end

function ArmedForceEventLogic._OnCSWAssemblyApplySkinRes(res)
    RentalDataLogic.OnCSWAssemblyApplySkinRes(res)
end

function ArmedForceEventLogic._OnRentalApplyProcess(rentalData)
    RentalDataLogic.RentalApplyProcess(rentalData)
end

return ArmedForceEventLogic
