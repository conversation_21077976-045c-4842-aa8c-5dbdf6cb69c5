----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMMarket)
----- LOG FUNCTION AUTO GENERATE END -----------

local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"

local MandelBrickPagePanel = ui("MandelBrickPagePanel")
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local MandelBrickBanner = require "DFM.Business.Module.MarketModule.UI.MandelBrick.MandelBrickBanner"
local MarketConfig = require "DFM.Business.Module.MarketModule.MarketConfig"
local MarketLogic = require "DFM.Business.Module.MarketModule.Logic.MarketLogic"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local EDescendantScrollDestination = import "EDescendantScrollDestination"
local EComp = CommonWidgetConfig.EIVWarehouseTempComponent
local DEFAULT_REFRESH_BUTTON_CD_TIME = 10
function MandelBrickPagePanel:Ctor()
    self._wtCoreRewardsScrollBox = self:Wnd("DFScrollBox_2", UIScrollBox)
    self._wtCoreRewardsTitle = self:Wnd("DFCanvasPanel_262", UIWidgetBase)
    self._wtDetailRewardsBtn = self:Wnd("WBP_CommonIconButton", DFCommonButtonOnly)
    self._wtDetailRewardsBtn:Event("OnClicked", self._OnDetailRewardsBtnClicked, self)
    self._wtMandelBrickBanner = self:Wnd("WBP_Market_Banner", MandelBrickBanner)
    self._wtOwnNumberText = self:Wnd("DFTextBlock_697", UITextBlock)
    self._wtBuyBtn = self:Wnd("WBP_DFCommonButtonV1S2", DFCommonButtonOnly)
    self._wtBuyBtn:Event("OnClicked", self._OnBuyBtnBtnClicked, self)
    self._wtBuyBtn:Event("OnDeClicked", self._OnBuyBtnBtnClicked, self)
    self._wtSellBtn = self:Wnd("wtCommonButtonV1S2", DFCommonButtonOnly)
    self._wtSellBtn:Event("OnClicked", self._OnSellBtnClicked, self)
    self._wtSellBtn:Event("OnDeClicked", self._OnSellBtnClicked, self)
    self._wtMandelBrickNameText = self:Wnd("DFText_Block", UITextBlock)
    self._wtTransactionOpenTimeText = self:Wnd("DFTextBlock_385", UITextBlock)
    self._wtTransactionOpenTimePanel = self:Wnd("DFCanvasPanel_260", UIWidgetBase)
    self._wtMandelBrickOpenBtn = self:Wnd("WBP_CommonCheckBoxV3S1_58", DFCommonCheckButtonOnly)
    self._wtMandelBrickOpenBtn:Event("OnClicked", self._OnMandelBrickOpenBtnClicked, self)
    self._wtItemViewList = UIUtil.WndWaterfallScrollBox(self, "wtWaterFallList", self._OnGetItemCount, self._OnProcessItemWidget)
    self._wtNowTotalNum = self:Wnd("DFTextBlock_150", UITextBlock)
    self._wtYesterdayAveragePrice = self:Wnd("DFRichTextBlock_67", UITextBlock)
    self._wtMinPriceText = self:Wnd("DFTextBlock", UITextBlock)
    self._wtMinPrice = self:Wnd("DFRichTextBlock", UITextBlock)
    self._wtTipsAnchorInstruction = UIUtil.WndTipsAnchor(self, "DFTipsAnchor_94", self._OnShowInstruction, self._OnHideInstruction)
    self._curItemId = nil
    self._refreshCdTime = DEFAULT_REFRESH_BUTTON_CD_TIME
    self._curRecordsNum = 1
    self._selectedPos = 0
end

function MandelBrickPagePanel:OnInitExtraData(curSubPageType)
    self._curSubPageType = curSubPageType
end

function MandelBrickPagePanel:OnActivate()
    loginfo('SubUIOwnerPack MandelBrickPagePanel:OnActivate()')
    
end

function MandelBrickPagePanel:OnDeactivate()
    loginfo('SubUIOwnerPack MandelBrickPagePanel:OnDeactivate()')
end

function MandelBrickPagePanel:OnShow()
    -- todo 目前框架控制会进入两次OnShow？
    self:StartTimer()
    self:RefreshMarketIsOpen()
end

function MandelBrickPagePanel:OnHide()
    self:StopTimer()
    self:StopOpenMarketTimer()
    self:StopCloseMarketTimer()
    self:StopPreoderExpriedTimer()
end

function MandelBrickPagePanel:OnShowBegin()
    self:AddLuaEvent(Server.MarketServer.Events.evtMarketSaleListChanged, self.RefreshRightPanel, self)
    self:AddLuaEvent(LuaGlobalEvents.evtSceneLoaded, self.RefreshModel, self)

    --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() then
        self:_EnableGamepadFeature()
    end
    --- END MODIFICATION
end

function MandelBrickPagePanel:OnHideBegin()
    self:RemoveLuaEvent(LuaGlobalEvents.evtSceneLoaded)
    self:RemoveLuaEvent(Server.MarketServer.Events.evtMarketSaleListChanged)

    --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() then
        self:_DisableGamepadFeature()
    end
    --- END MODIFICATION
end
function MandelBrickPagePanel:OnOpen()
    self:AddLuaEvent(Server.MarketServer.Events.evtMarketLoopSaleListChanged, self.RefreshMandelBrickDetail, self)
    self:AddLuaEvent(Server.MarketServer.Events.evtMarketTypeListChanged, self.RefreshItemViewList, self)
    self:AddLuaEvent(Server.MarketServer.Events.evtMandelBrickTypeListChanged, self.RefreshMandelBrickList, self)
    self:AddLuaEvent(Server.MarketServer.Events.evtMeleeWeaponTypeListChanged, self.RefreshMeleeWeaponList, self)
    self:AddLuaEvent(Server.MarketServer.Events.evtMarketPullOffGoodSucceed, self.RefreshOwnNumber, self)
    self:AddLuaEvent(Server.MarketServer.Events.evtMarketPreorderCreated, self.RefreshPreoderBuyState, self)
    self:AddLuaEvent(Server.MarketServer.Events.evtMarketPreorderRemoved, self.RefreshPreoderBuyState, self)
    self:AddLuaEvent(Server.MarketServer.Events.evtMarketPreorderChanged, self.RefreshPreoderBuyState, self)
    self:AddLuaEvent(Server.CollectionServer.Events.evtFetchCollectionData, self.RefreshOwnNumber, self)
    self:AddLuaEvent(Server.CollectionServer.Events.evtPropsChangedByMarket, self.OnPropChangeByMarket, self)
    Facade.ProtoManager.Events.evtOnRelayConnected:AddListener(self.OnRelayConnected, self)  --断线重连
    self:RefreshItemViewList()
    self:RefreshOwnNumber() -- 初始状态先刷新一次市场开放状态
end

function MandelBrickPagePanel:OnClose()
    self:RemoveAllLuaEvent()
end

function MandelBrickPagePanel:Init()

end

function MandelBrickPagePanel:RefreshItemViewList()
    if self._curSubPageType == EMarketSubPageType.MandelBrick then
        self:RefreshMandelBrickList()
    elseif self._curSubPageType == EMarketSubPageType.MeleeWeapon then
        self:RefreshMeleeWeaponList()
    end
end

function MandelBrickPagePanel:RefreshMandelBrickList(res)
    if self._curSubPageType == EMarketSubPageType.MandelBrick then
        -- 数据准备
        local mandelBrickSaleList = {}
        if res and res.type_lists then
            for _, v in ipairs(res.type_lists) do
                mandelBrickSaleList[v.prop_id] = v
            end
        else
            mandelBrickSaleList = Server.MarketServer:GetMandelBrickSaleList()
        end
        self._saleDataList = {}
        for itemId, saleInfo in pairs(mandelBrickSaleList) do
            local saleData = {
                itemId = itemId,
                saleInfo = saleInfo
            }
            table.insert(self._saleDataList, saleData)
        end
        table.sort(self._saleDataList, function(a, b)
            return b.saleInfo.season < a.saleInfo.season
        end)
        if self._saleDataList[1] then
            self._saleDataList[1].categoryName = MarketConfig.Loc.CurrentOutput
        end
        if self._saleDataList[2] then
            self._saleDataList[2].categoryName = MarketConfig.Loc.PreviousOutput
        end
        self:InitSelectedPos()
        -- 刷新滚动框
        self._wtItemViewList:RefreshAllItems()
        if self._saleDataList and #self._saleDataList > 0 then
            self._curItemId = self._saleDataList[1].itemId
            Server.MarketServer:FetchSaleList(self._curItemId)
        end
    end
end

function MandelBrickPagePanel:RefreshMeleeWeaponList(res)
    if self._curSubPageType == EMarketSubPageType.MeleeWeapon then
        -- 数据准备
        local meleeWeaponSaleList = {}
        if res and res.type_lists then
            for _, v in ipairs(res.type_lists) do
                meleeWeaponSaleList[v.prop_id] = v
            end
        else
            meleeWeaponSaleList = Server.MarketServer:GetMeleeWeaponSaleList()
        end
        self._saleDataList = {}
        for itemId, saleInfo in pairs(meleeWeaponSaleList) do
            local saleData = {
                itemId = itemId,
                saleInfo = saleInfo
            }
            table.insert(self._saleDataList, saleData)
        end
        table.sort(self._saleDataList, function(a, b)
            return b.saleInfo.season < a.saleInfo.season
        end)
        self:InitSelectedPos()
        -- 刷新滚动框
        self._wtItemViewList:RefreshAllItems()
        if self._saleDataList and #self._saleDataList > 0 then
            self._curItemId = self._saleDataList[1].itemId
            Server.MarketServer:FetchSaleList(self._curItemId)
        end
    end
end

function MandelBrickPagePanel:InitSelectedPos()
    if self._saleDataList and not table.isempty(self._saleDataList) then
        self._selectedPos = 1
    else
        self._selectedPos = 0
    end
    self._selectedCell = nil
end

-- function MandelBrickPagePanel:RefreshMandelBrickBanner()
--     if self._wtMandelBrickBanner then
--         local mandelBrickInfoList = {}
--         for k, v in pairs(Server.MarketServer:GetMandelBrickSaleList()) do
--             local mandelBrickInfo = {id = k, info = v}
--             table.insert(mandelBrickInfoList, mandelBrickInfo)
--         end
--         table.sort(mandelBrickInfoList, function(a, b)
--             return b.id < a.id
--         end)
--         self._wtMandelBrickBanner:Init(mandelBrickInfoList, CreateCallBack(self.MandelBrickChangedCallback, self), 0)
--         self._curItemId = mandelBrickInfoList[1] and mandelBrickInfoList[1].id or nil
--     end
-- end

function MandelBrickPagePanel:MandelBrickChangedCallback(itemId)
    if itemId then
        Server.MarketServer:FetchSaleList(itemId)
    end
end

function MandelBrickPagePanel:RefreshRightPanel(saleListInfo)
    if not (saleListInfo and saleListInfo.prop_id) then
        return
    end
    local itemType = Server.MarketServer:GetMarketItemType(saleListInfo.prop_id)
    if not (itemType == EMarketSubPageType.MandelBrick or itemType == EMarketSubPageType.MeleeWeapon) then
        return
    end
    if not (itemType == self._curSubPageType) then
        return
    end
    self._curItemId = saleListInfo.prop_id
    self:RefreshMandelBrickDetail(saleListInfo)
    self:RefreshOwnNumber()
    if itemType == EMarketSubPageType.MandelBrick then
        Server.MarketServer:FetchPreorderOrder({self._curItemId})
        self:RefreshModel(ESubStage.LotteryCollection)
    elseif itemType == EMarketSubPageType.MeleeWeapon then
        self:RefreshModel(ESubStage.CollectionKnife)
    end
end

function MandelBrickPagePanel:RefreshMandelBrickDetail(saleListInfo)
    if not (saleListInfo and saleListInfo.prop_id)  then
        return
    end
    local itemType = Server.MarketServer:GetMarketItemType(saleListInfo.prop_id)
    if not (itemType == EMarketSubPageType.MandelBrick or itemType == EMarketSubPageType.MeleeWeapon) then
        return
    end
    if not (itemType == self._curSubPageType) then
        return
    end
    if not self._curItemId then
        return
    end
    local item = ItemBase:NewIns(self._curItemId)
    if item.name then
        local MandelBrickName = item.name -- string.match(tostring(item.name), "曼德尔砖%-(.*)")
        if MandelBrickName and self._wtMandelBrickNameText then 
            self._wtMandelBrickNameText:SetText(MandelBrickName)
        end
    end
    -- 奖励详情和开启按钮
    if itemType == EMarketSubPageType.MandelBrick then
        self._wtDetailRewardsBtn:SelfHitTestInvisible()
        self._wtMandelBrickOpenBtn:SelfHitTestInvisible()
    elseif itemType == EMarketSubPageType.MeleeWeapon then
        self._wtDetailRewardsBtn:Collapsed()
        self._wtMandelBrickOpenBtn:Collapsed()
    end
    -- 当前市场流通量
    if self._wtNowTotalNum and saleListInfo and saleListInfo.cur_num then
        if saleListInfo.cur_num <= 999 then
            self._wtNowTotalNum:SetText(string.format(MarketConfig.Loc.NowMarketTotalNum, saleListInfo.cur_num))
        else
            self._wtNowTotalNum:SetText(string.format(MarketConfig.Loc.NowMarketTotalNum, MarketConfig.Loc.TooManyNum))
        end
    end
    -- 昨日成交均价
    if self._wtYesterdayAveragePrice then
        if saleListInfo and saleListInfo.buy_currency_id and saleListInfo.average_price then
            self._wtYesterdayAveragePrice:SelfHitTestInvisible()
            local currencyId = Module.Currency:ConvertCurrencyIdByItemId(saleListInfo.buy_currency_id)
            local currencyIcon = Module.Currency:GetRichTxtImgId(currencyId)
            if IsHD() then
                if saleListInfo.average_price ~= 0 then
                    self._wtYesterdayAveragePrice:SetText(string.format(MarketConfig.Loc.CurrencyNum, 38, 38, currencyIcon, MathUtil.GetNumberFormatStr(saleListInfo.average_price + 0.00001)))
                else
                    self._wtYesterdayAveragePrice:SetText(MarketConfig.Loc.NoDeal)
                end
            else
                if saleListInfo.average_price ~= 0 then
                    self._wtYesterdayAveragePrice:SetText(string.format(MarketConfig.Loc.CurrencyNum, 48, 48, currencyIcon, MathUtil.GetNumberFormatStr(saleListInfo.average_price + 0.00001)))
                else
                    self._wtYesterdayAveragePrice:SetText(MarketConfig.Loc.NoDeal)
                end
            end
        else
            self._wtYesterdayAveragePrice:Collapsed()
        end
    end
    -- 最低价
    if self._wtMinPrice and self._wtMinPriceText then
        if itemType == EMarketSubPageType.MandelBrick then
            if saleListInfo and saleListInfo.buy_currency_id and saleListInfo.min_price then
                self._wtMinPrice:SelfHitTestInvisible()
                self._wtMinPriceText:SelfHitTestInvisible()
                local currencyId = Module.Currency:ConvertCurrencyIdByItemId(saleListInfo.buy_currency_id)
                local currencyIcon = Module.Currency:GetRichTxtImgId(currencyId)
                if IsHD() then
                    if saleListInfo.min_price ~= 0 then
                        self._wtMinPrice:SetText(string.format(MarketConfig.Loc.CurrencyNum, 38, 38, currencyIcon, MathUtil.GetNumberFormatStr(saleListInfo.min_price + 0.00001)))
                    else
                        self._wtMinPrice:SetText(MarketConfig.Loc.ItemSoldOutBtnTextWeakColor)
                    end
                else
                    if saleListInfo.min_price ~= 0 then
                        self._wtMinPrice:SetText(string.format(MarketConfig.Loc.CurrencyNum, 48, 48, currencyIcon, MathUtil.GetNumberFormatStr(saleListInfo.min_price + 0.00001)))
                    else
                        self._wtMinPrice:SetText(MarketConfig.Loc.ItemSoldOutBtnTextWeakColor)
                    end
                end
            else
                self._wtMinPrice:Collapsed()
                self._wtMinPriceText:Collapsed()
            end
        elseif itemType == EMarketSubPageType.MeleeWeapon then
            self._wtMinPrice:Collapsed()
            self._wtMinPriceText:Collapsed()
        end
    end
    -- 交易开放时间
    if self._wtTransactionOpenTimeText and self._wtTransactionOpenTimePanel then
        local function secondsToTime(seconds)
            local hours = math.floor(seconds / 3600)
            local minutes = math.floor((seconds % 3600) / 60)
            local bNextDay = hours > 24
            if hours > 24 then
                hours = hours - 24
            end
            return hours, minutes, bNextDay
        end
        local marketOpenTime = Server.MarketServer:GetMarketOpenTime()
        local marketCloseTime = Server.MarketServer:GetMarketCloseTime()
        if marketOpenTime == 0 and marketCloseTime == 0 then
            self._wtTransactionOpenTimePanel:Collapsed()
        else
            self._wtTransactionOpenTimePanel:SelfHitTestInvisible()
            local openHours, openMinutes = secondsToTime(marketOpenTime)
            local closeHours, closeMinutes, bNextDay = secondsToTime(marketCloseTime)
            local bNextDayText = bNextDay and MarketConfig.Loc.NextDay or ""
            self._wtTransactionOpenTimeText:SetText(StringUtil.Key2StrFormat(string.format(MarketConfig.Loc.TransactionOpenTime, openHours, openMinutes, closeHours, closeMinutes), {["NextDay"] = bNextDayText}))
        end
    end
end

function MandelBrickPagePanel:_OnGetItemCount()
    return self._saleDataList and #self._saleDataList or 0
end

function MandelBrickPagePanel:_OnProcessItemWidget(position, itemWidget)
    if self._saleDataList then
        local saleData = self._saleDataList[position]
        local OnItemClick = CreateCallBack(function(self, itemId)
            self:ItemWidgetClickedCallback(itemId, itemWidget, position)
        end, self)
        itemWidget:Init(saleData.categoryName, saleData.itemId, saleData.saleInfo, OnItemClick)
        if self._selectedPos and position == self._selectedPos then
            self._selectedCell = itemWidget
            itemWidget:SetSelected(itemWidget.item, true)
        else
            itemWidget:SetSelected(itemWidget.item, false)
        end
    end
end

function MandelBrickPagePanel:ItemWidgetClickedCallback(itemId, itemWidget, position)
    if self._selectedPos == position then
        return
    end
    if itemId then
        if self._selectedCell then
            self._selectedCell:SetSelected(self._selectedCell.item, false)
        end
        self._selectedPos = position
        self._selectedCell = itemWidget
        if self._selectedCell then
            self._selectedCell:SetSelected(self._selectedCell.item, true)
        end
        self._curItemId = itemId
        Server.MarketServer:FetchSaleList(self._curItemId)
    end
end

function MandelBrickPagePanel:RefreshOwnNumber()
    if self._curItemId then
        local ownNumber = MarketLogic.GetMarketItemNumById(self._curItemId)
        self._wtOwnNumberText:SetText(string.format(MarketConfig.Loc.CurOwnNumber, ownNumber))
        if ownNumber > 0 and MarketLogic.JudgeMarketIsOpen() then
            self._wtSellBtn:SetIsEnabledStyle(true)
        else
            self._wtSellBtn:SetIsEnabledStyle(false)
        end
    end
end

function MandelBrickPagePanel:StartTimer()
    self:StopTimer()
    self._refreshCdTime = DEFAULT_REFRESH_BUTTON_CD_TIME
    self._timerHandle = Timer:NewIns(1, 0)
    self._timerHandle:AddListener(self.RefreshSaleList, self)
    self._timerHandle:Start()
end

function MandelBrickPagePanel:StopTimer()
    if self._timerHandle then
        self._timerHandle:Release()
        self._timerHandle = nil
    end
end

function MandelBrickPagePanel:RefreshSaleList()
    if self._curItemId then
        if self._refreshCdTime > 0 then
            self._refreshCdTime = self._refreshCdTime - 1
        else
            self._refreshCdTime = DEFAULT_REFRESH_BUTTON_CD_TIME
            Server.MarketServer:FetchSaleList(self._curItemId, true)
        end
    end
end

function MandelBrickPagePanel:_CreateCommonItemViewByItem(itembase)
	local itemFeature = itembase:GetFeature()
	local featureType = itemFeature:GetFeatureType()
	if featureType == EFeatureType.Weapon then
		local itemUI = Module.CommonWidget:CreateIVCommonItemTemplateBindOwnerBySize(self, 192, 192)
        if IsHD() then
            itemUI = Module.CommonWidget:CreateIVCommonItemTemplateBindOwnerBySize(self, 256, 256)
        end
		itemUI:EnableComponent(EComp.ItemQuality, false)
		return itemUI
	end
    if IsHD() then
        return Module.CommonWidget:CreateIVCommonItemTemplateBindOwnerBySize(self, 192, 192)
    else
        return Module.CommonWidget:CreateIVCommonItemTemplateBindOwnerBySize(self, 256, 256)
    end
end

function MandelBrickPagePanel:_OnDetailRewardsBtnClicked()
	local function fOnClose()
        if self._NavGroup_DetailRewards then
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup_DetailRewards)
        end
		Module.ItemDetail.Field:SetPrizePop(false)
	end
    local function fOnLoad()
		Module.ItemDetail.Field:SetPrizePop(true)
	end
	if not Facade.GameFlowManager:CheckIsInFrontEnd() then
		Module.CommonTips:ShowSimpleTip(Module.ItemDetail.Config.Loc.contentGiftButtonText)
		return
	end
    Module.Store.OpenStorePrizePopPanel(fOnLoad, fOnClose, self._curItemId,true)
	local socialMainHandle = Module.Mail:GetSocialMainUIins()
	if socialMainHandle then
		Facade.UIManager:CloseUI(socialMainHandle)
	end
end

function MandelBrickPagePanel:_OnBuyBtnBtnClicked()
    if self._curItemId then
        if MarketLogic.JudgeMarketIsOpen() then
            Module.Market:JumpToMandelBrickBuyPage(self._curItemId)
        else
            Module.CommonTips:ShowSimpleTip(Module.Market.Config.Loc.MarketIsNotOpen)
        end
    end
end

function MandelBrickPagePanel:_OnSellBtnClicked()
    if self._curItemId then
        if MarketLogic.JudgeMarketIsOpen() then
            local ownNumber = MarketLogic.GetMarketItemNumById(self._curItemId)
            if ownNumber > 0 then
                Module.Market:JumpToMandelBrickSellPage(self._curItemId)
            else
                Module.CommonTips:ShowSimpleTip(Module.Market.Config.Loc.MandelBrickUnowned)
            end
        else
            Module.CommonTips:ShowSimpleTip(Module.Market.Config.Loc.MarketIsNotOpen)
        end
    end
end

function MandelBrickPagePanel:_OnMandelBrickOpenBtnClicked()
    if self._curSubPageType == EMarketSubPageType.MandelBrick then
        if self._curItemId then
            -- Module.Store:ShowMainPanel(EStoreTab.Draw, self._curItemId)
            Module.Store:ShowStoreMandelOnlyPanle(self._curItemId, 0, EMandelOpenSource.Market)
        end
    end
end

function MandelBrickPagePanel:RefreshMarketIsOpen()
    local marketOpenTimePeriods = MarketLogic.GetMarketOpenTimePeriods()
    if marketOpenTimePeriods and not table.isempty(marketOpenTimePeriods) then
        if #marketOpenTimePeriods == 1 then -- 单个时间段
            local timePeriod = marketOpenTimePeriods[1]
            if timePeriod[1] <= Facade.ClockManager:GetLocalTimestamp() and Facade.ClockManager:GetLocalTimestamp() <= timePeriod[2] then
                self:OpenMarket(timePeriod[2] - Facade.ClockManager:GetLocalTimestamp())
            elseif Facade.ClockManager:GetLocalTimestamp() < timePeriod[1] then
                self:CloseMarket(timePeriod[1] - Facade.ClockManager:GetLocalTimestamp())
            elseif Facade.ClockManager:GetLocalTimestamp() > timePeriod[2] then
                self:CloseMarket(timePeriod[1] + 24 * 60 * 60 - Facade.ClockManager:GetLocalTimestamp())
            end
        elseif #marketOpenTimePeriods == 2 then -- 两个时间段
            local timePeriod1 = marketOpenTimePeriods[1]
            local timePeriod2 = marketOpenTimePeriods[2]
            if Facade.ClockManager:GetLocalTimestamp() < timePeriod1[1] then
                self:CloseMarket(timePeriod1[1] - Facade.ClockManager:GetLocalTimestamp())
            elseif timePeriod1[1] <= Facade.ClockManager:GetLocalTimestamp() and Facade.ClockManager:GetLocalTimestamp() <= timePeriod1[2] then
                self:OpenMarket(timePeriod1[2] - Facade.ClockManager:GetLocalTimestamp())
            elseif timePeriod1[2] <= Facade.ClockManager:GetLocalTimestamp() and Facade.ClockManager:GetLocalTimestamp() <= timePeriod2[1] then
                self:CloseMarket(timePeriod2[1] - Facade.ClockManager:GetLocalTimestamp())
            elseif timePeriod2[1] <= Facade.ClockManager:GetLocalTimestamp() and Facade.ClockManager:GetLocalTimestamp() <= timePeriod2[2] then
                self:OpenMarket(timePeriod2[2] - Facade.ClockManager:GetLocalTimestamp())
            elseif Facade.ClockManager:GetLocalTimestamp() > timePeriod2[2] then
                self:CloseMarket(timePeriod1[1] + 24 * 60 * 60 - Facade.ClockManager:GetLocalTimestamp())
            end
        end
    end
end

function MandelBrickPagePanel:CloseMarket(timeToOpen)
    self._wtBuyBtn:SetIsEnabledStyle(false)
    self._wtSellBtn:SetIsEnabledStyle(false)
    self:RefreshOwnNumber()
    self:StopOpenMarketTimer()
    self._openMarketTimerHandle = Timer.DelayCall(timeToOpen + 1, function ()
        self:RefreshMarketIsOpen()
    end, self)
end

function MandelBrickPagePanel:StopOpenMarketTimer()
    if self._openMarketTimerHandle then
        Timer.CancelDelay(self._openMarketTimerHandle)
        self._openMarketTimerHandle = nil
    end
end

function MandelBrickPagePanel:OpenMarket(timeToClose)
    self._wtBuyBtn:SetIsEnabledStyle(true)
    self._wtSellBtn:SetIsEnabledStyle(true)
    self:RefreshOwnNumber()
    self:StopCloseMarketTimer()
    self._closeMarketTimerHandle = Timer.DelayCall(timeToClose + 1, function ()
        self:RefreshMarketIsOpen()
    end, self)
end

function MandelBrickPagePanel:StopCloseMarketTimer()
    if self._closeMarketTimerHandle then
        Timer.CancelDelay(self._closeMarketTimerHandle)
        self._closeMarketTimerHandle = nil
    end
end

function MandelBrickPagePanel:RefreshModel(curSubStageType)
    if self._curSubPageType == EMarketSubPageType.MandelBrick then
        if not curSubStageType or curSubStageType == ESubStage.LotteryCollection then
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.LotteryCollection, "ResetDisplayItem")
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.LotteryCollection, "SetDisplayType","Main")
            if self._curItemId then
                local topPropId = Module.Collection:GetMandelBrickTopPrizeItemId(self._curItemId)
                if topPropId ~= nil then
                    weaponDesc, partIndexs = WeaponHelperTool.GetWeaponDescriptionFromSkinID(topPropId)
                    if isvalid(weaponDesc) then
                        weaponDesc:SetSkin(topPropId) 
                        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.LotteryCollection, "SetDisplayWeapon", weaponDesc, topPropId, false, true)
                    end
                end
            end
        end
    elseif self._curSubPageType == EMarketSubPageType.MeleeWeapon then
        if not curSubStageType or curSubStageType == ESubStage.CollectionKnife then
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.CollectionKnife, "ResetDisplayItem")
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.CollectionKnife, "ResetWeapon")
            local item = ItemBase:NewIns(self._curItemId)
            if isvalid(item) then
                local weaponDesc, partIndexs = WeaponHelperTool.GetWeaponDescriptionFromSkinID(item.id)
                Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.CollectionKnife, "SetDisplayWeaponAutoBoundAdapter", weaponDesc, true, true)
            end 
        end
    end
end

function MandelBrickPagePanel:RefreshPreoderBuyState(propId)
    if propId and propId ~= self._curItemId then
        return
    end
    local preorder = Server.MarketServer:GetPreorderOrder(self._curItemId)
    if preorder then
        local curTime = Facade.ClockManager:GetLocalTimestamp()
        local bExpired = preorder.expire_time < curTime
        if self.SetPreorderBuyState then
            self:SetPreorderBuyState(true, not bExpired)
        end 
        if not bExpired then
            self:StopPreoderExpriedTimer()
            self._preoderExpriedTimerHandle = Timer.DelayCall(preorder.expire_time - curTime + 1, function ()
                self:RefreshPreoderBuyState()
            end, self)
        end
    else
        if self.SetPreorderBuyState then
            self:SetPreorderBuyState(false, false)
        end
    end
end

function MandelBrickPagePanel:StopPreoderExpriedTimer()
    if self._preoderExpriedTimerHandle then
        Timer.CancelDelay(self._preoderExpriedTimerHandle)
        self._preoderExpriedTimerHandle = nil
    end
end

function MandelBrickPagePanel:_OnShowInstruction()
    if not self._curItemId then
        return
    end
    local preorder = Server.MarketServer:GetPreorderOrder(self._curItemId)
    if not preorder then
        return
    end
    local curTime = Facade.ClockManager:GetLocalTimestamp()
    local bExpired = preorder.expire_time < curTime
    self:_OnHideInstruction()
    if self._wtTipsAnchorInstruction then
        local contents = {}
        table.insert(contents, {
            id = UIName2ID.Assembled_CommonMessageTips_V1,
            data = {
                textContent = bExpired and MarketConfig.Loc.PreorderExpire or MarketConfig.Loc.PreorderBuying,
            }
        })
        self._tipHandle = Module.CommonTips:ShowAssembledTips(contents, self._wtTipsAnchorInstruction)
    end
end

function MandelBrickPagePanel:_OnHideInstruction(reason)
    if self._tipHandle then
        if self._wtTipsAnchorInstruction then
            Module.CommonTips:RemoveAssembledTips(self._tipHandle, self._wtTipsAnchorInstruction)
        end
        self._tipHandle = nil
    end
end

function MandelBrickPagePanel:OnPropChangeByMarket(dataChange)
    if dataChange and type(dataChange) == "table" and not table.isempty(dataChange) then
        if Server.MarketServer:GetMarketItemType(dataChange[1].prop.id) == EMarketSubPageType.MandelBrick and dataChange[1].reason == ePropChangeReason.FromMarketPreBuy then
            self:RefreshOwnNumber()
        end
    end
end

function MandelBrickPagePanel:OnRelayConnected()
    logwarning("MandelBrickPagePanel:OnRelayConnected")
    if self._curItemId then
        Server.MarketServer:FetchSaleList(self._curItemId)
    end
end

--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
function MandelBrickPagePanel:_EnableGamepadFeature()
    if not IsHD() then
        return 
    end

    -- 在打开外部界面时，再返回到市场会同时触发市场三个界面的OnShow事件
    if not self:IsVisible() then
        return
    end

    if self._wtBuyBtn then
        self:InitDFCommonBtnKeyIcon(self._wtBuyBtn, "DFCommonButton_PCOnly", "Common_ButtonLeft")
    end

    if self._wtSellBtn then
        self:InitDFCommonBtnKeyIcon(self._wtSellBtn,"DFCommonButton_PCOnly", "Common_ButtonTop")
    end

    -- if self._wtMandelBrickOpenBtn then
    --     self:InitDFCommonBtnKeyIcon(self._wtMandelBrickOpenBtn, "DFCommonCheckButton", "MandelBrickOpen_Gamepad")
    --     local _wtMandelBrickOpenCheckBtn = self._wtMandelBrickOpenBtn:Wnd("DFCommonCheckButton", UIWidgetBase)
    --     if _wtMandelBrickOpenCheckBtn then
    --         self._MandelBrickOpenKeyIcon = _wtMandelBrickOpenCheckBtn:Wnd("KeyIcon", HDKeyIconBox)
    --         self._MandelBrickOpenKeyIcon:BP_ShowHoldProgressBarTips(true)
    --     end
    -- end

    if not self._buyHandle then
        self._buyHandle = self:AddInputActionBinding("Common_ButtonLeft", EInputEvent.IE_Pressed,
                                    self._OnBuyBtnBtnClicked, self, EDisplayInputActionPriority.UI_Stack)
    end

    if not self._sellHandle then
        self._sellHandle = self:AddInputActionBinding("Common_ButtonTop", EInputEvent.IE_Pressed,
                                    self._OnSellBtnClicked, self, EDisplayInputActionPriority.UI_Stack)
    end

    if not self._mandelBrickOpenHandle then
        self._mandelBrickOpenHandle = self:AddHoldInputActionBinding("MandelBrickOpen_Gamepad",
                                    self._OnMandelBrickOpenBtnClicked, self, EDisplayInputActionPriority.UI_Stack)
        -- 监听长按进度事件
        self:AddHoldInputActionProgressedBinding(self._mandelBrickOpenHandle, self.OnMandelBrickOpenInLongPressing, self)
        -- 监听长按结束事件
        self:AddHoldInputActionReleaseBinding(self._mandelBrickOpenHandle, self.OnMandelBrickOpenLongPressFinished, self)
        if self._wtMandelBrickOpenBtn then
            self._wtMandelBrickOpenBtn:SetDisplayInputActionWithLongPress(self._mandelBrickOpenHandle, self, "MandelBrickOpen_Gamepad", true, nil, true)
        end
    end

    if not self._mandelBrickBannerLeftHandle then
        self._mandelBrickBannerLeftHandle = self:AddInputActionBinding("MandelBrickBannerLeft_Gamepad", EInputEvent.IE_Pressed,
                                    self._OnBannerLeft, self, EDisplayInputActionPriority.UI_Stack)
    end

    if not self._mandelBrickBannerRightHandle then
        self._mandelBrickBannerRightHandle = self:AddInputActionBinding("MandelBrickBannerRight_Gamepad", EInputEvent.IE_Pressed,
                                    self._OnBannerRight, self, EDisplayInputActionPriority.UI_Stack)
    end

    local summaryList ={}
    table.insert(summaryList, {actionName = "ProbabilityOverview_Gamepad",func = self._OnDetailRewardsBtnClicked, caller = self ,bUIOnly = false, bHideIcon = false}) 
    Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, false, true)

    -- 注册导航
    if not self._NavGroup_DetailRewards then
        self._NavGroup_DetailRewards = WidgetUtil.RegisterNavigationGroup(self._wtDetailRewardsBtn, self, "Hittest")
        if self._NavGroup_DetailRewards then
            self._NavGroup_DetailRewards:AddNavWidgetToArray(self._wtDetailRewardsBtn)
        end
    end

    if not self._NavGroup_ItemViewList then
        self._NavGroup_ItemViewList = WidgetUtil.RegisterNavigationGroup(self._wtItemViewList, self, "Hittest")
        if self._NavGroup_ItemViewList then
            self._NavGroup_ItemViewList:AddNavWidgetToArray(self._wtItemViewList)
            self._NavGroup_ItemViewList:SetScrollRecipient(self._wtItemViewList)
            self._NavGroup_ItemViewList:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup_ItemViewList)
        end
    end
end

function MandelBrickPagePanel:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end

    self:RemoveInputActionBinding(self._buyHandle)
    self:RemoveInputActionBinding(self._sellHandle)
    self:RemoveInputActionBinding(self._mandelBrickOpenHandle)
    self:RemoveInputActionBinding(self._mandelBrickBannerLeftHandle)
    self:RemoveInputActionBinding(self._mandelBrickBannerRightHandle)

    self._buyHandle = nil
    self._sellHandle = nil
    self._mandelBrickOpenHandle = nil
    self._mandelBrickBannerLeftHandle = nil
    self._mandelBrickBannerRightHandle = nil

    Module.CommonBar:RecoverBottomBarInputSummaryList()
    if self._NavGroup_ItemViewList or self._NavGroup_DetailRewards then
        WidgetUtil.RemoveNavigationGroup(self)
        self._NavGroup_ItemViewList = nil
        self._NavGroup_DetailRewards = nil
    end
end

function MandelBrickPagePanel:InitDFCommonBtnKeyIcon(widget, commonBtnName, actionName)
    if not IsHD() then
        return 
    end

    local _commonBtn = widget:Wnd(commonBtnName, UIWidgetBase)
    if _commonBtn then
        local _keyIcon = _commonBtn:Wnd("KeyIcon", HDKeyIconBox)
        if _keyIcon then
            _keyIcon:SetOnlyDisplayOnGamepad(true)
            _keyIcon:InitByDisplayInputActionName(actionName, true, 0, true)
        end
    end
end

function MandelBrickPagePanel:OnMandelBrickOpenInLongPressing(percent)
    if IsHD() and self._MandelBrickOpenKeyIcon then
        self._MandelBrickOpenKeyIcon:BP_UpdateProgressBar(percent) 
    end
end

function MandelBrickPagePanel:OnMandelBrickOpenLongPressFinished()
    if IsHD() and self._MandelBrickOpenKeyIcon then
        self._MandelBrickOpenKeyIcon:BP_UpdateProgressBar(0) 
    end
end

function MandelBrickPagePanel:_OnBannerBtnClicked()
    if not IsHD() then
        return 
    end

    if self._wtMandelBrickBanner then
        self._wtMandelBrickBanner:OnIconBtnClicked()
    end
end

function MandelBrickPagePanel:_OnBannerLeft()
    if not IsHD() then
        return 
    end

    if self._wtMandelBrickBanner then
        self._wtMandelBrickBanner:OnLeftBtnClicked()
    end
end

function MandelBrickPagePanel:_OnBannerRight()
    if not IsHD() then
        return 
    end

    if self._wtMandelBrickBanner then
        self._wtMandelBrickBanner:OnRightBtnClicked()
    end
end

--- END MODIFICATION

return MandelBrickPagePanel