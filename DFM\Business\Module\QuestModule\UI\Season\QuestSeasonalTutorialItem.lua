----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMQuest)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class QuestSeasonalTutorialItem : LuaUIBaseView

local QuestSeasonalTutorialItem = ui("QuestSeasonalTutorialItem")
function QuestSeasonalTutorialItem:Ctor()

    self._wtCollectorNum = self:Wnd("DFTextBlock_Number_01", UITextBlock)
    self._wtFactNum = self:Wnd("DFTextBlock_Number_02", UITextBlock)
    
    self._wtCollectorNumOvertime = self:Wnd("DFTextBlock_Number_03", UITextBlock)
    self._wtFactNumOvertime = self:Wnd("DFTextBlock_Number_04", UITextBlock)
end

function QuestSeasonalTutorialItem:OnShowBegin()

    local lineInfo = Server.QuestServer:GetCurrentSeasonLine()

    if lineInfo == nil then
        logerror("Invalid LineInfo")
        return
    end

    local EQuestSeasonConditionType = Server.QuestServer.EQuestSeasonConditionType
    if self._wtCollectorNum then
        for key, value in pairs(lineInfo._unLockConditionList) do
            if key == EQuestSeasonConditionType.Collection then
                self._wtCollectorNum:SetText("x"..tostring(value.param))
                break
            end
        end
    end

    if self._wtFactNum then
        for key, value in pairs(lineInfo._unLockConditionList) do
            if key == EQuestSeasonConditionType.Fate then
                self._wtFactNum:SetText("x"..tostring(value.param))
                break
            end
        end
    end

    if self._wtCollectorNumOvertime then
        for key, value in pairs(lineInfo._overConditionList) do
            if key == EQuestSeasonConditionType.Collection then
                self._wtCollectorNumOvertime:SetText("x"..tostring(value.param))
                break
            end
        end
    end

    if self._wtFactNumOvertime then
        for key, value in pairs(lineInfo._overConditionList) do
            if key == EQuestSeasonConditionType.Fate then
                self._wtFactNumOvertime:SetText("x"..tostring(value.param))
                break
            end
        end
    end
end

return QuestSeasonalTutorialItem