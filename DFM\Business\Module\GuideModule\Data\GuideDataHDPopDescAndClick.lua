----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGuide)
----- LOG FUNCTION AUTO GENERATE END -----------



local GuideDataBase = require "DFM.Business.Module.GuideModule.Data.GuideDataBase"
local GuideConfig = require "DFM.Business.Module.GuideModule.GuideConfig"
local GuideLogic = require "DFM.Business.Module.GuideModule.GuideLogic"

---@class GuideDataHDPopDescAndClick : GuideDataBase
local GuideDataHDPopDescAndClick = class('GuideDataHDPopDescAndClick', GuideDataBase)

function GuideDataHDPopDescAndClick:Ctor()
    local cfg  = self:GetGuideCfg()
    local cfgId = tonumber(cfg.Args)
    local popFunctionCfg = Module.Guide.Config.TableGuidePopFunctionDescConfig[cfgId]
    self.popDescID = cfgId
    self.clikID = popFunctionCfg.ClickConfigId[1]
end

function GuideDataHDPopDescAndClick:Destroy()
    if Module.Guide then
        local cfgId = tonumber(self._guideCfg.Args)
        Module.Guide:CloseGuideHDPopFunctionDescAndClickUI(cfgId)
    end
end

function GuideDataHDPopDescAndClick:OnStartGuide()
    local cfgId = tonumber(self._guideCfg.Args)
    local _callbackOnCloseUI = function()
        self:EndGuide(#self._guideCfg.NextGuideId)
    end
    Module.Guide:OpenGuideHDPopFunctionDescAndClickUI(cfgId, _callbackOnCloseUI)
end

function GuideDataHDPopDescAndClick:OnEndGuide(idx)
    local cfgId = tonumber(self._guideCfg.Args)
    Module.Guide:CloseGuideHDPopFunctionDescAndClickUI(cfgId)
end

function GuideDataHDPopDescAndClick:OnPause(uiIns)
    local cfgId = tonumber(self._guideCfg.Args)
    Module.Guide:SetPopFunctionDescAndClickUIValidState(cfgId, false)
end

function GuideDataHDPopDescAndClick:OnRestart()
    local cfgId = tonumber(self._guideCfg.Args)
    Module.Guide:SetPopFunctionDescAndClickUIValidState(cfgId, true)
end

function GuideDataHDPopDescAndClick:OnInputTypeChanged(inputType)
    -- 重新定位一下, 比如BottonBar 位置会刷新
    Module.Guide:OpenGuideHDClickUI(self.clikID, self.popDescID)
end

return GuideDataHDPopDescAndClick