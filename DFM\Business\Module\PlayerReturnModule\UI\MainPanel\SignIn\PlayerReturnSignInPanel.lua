----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMPlayerReturn)
----- LOG FUNCTION AUTO GENERATE END -----------

local IPlayerReturnSubActivityPanel = require "DFM.Business.Module.PlayerReturnModule.UI.SubPanels.IPlayerReturnSubActivityPanel"
local IPlayerReturnSubActivity = require "DFM.Business.Module.PlayerReturnModule.SubActivities.PlayerReturnSubActivities"

local PlayerReturnConfig = Module.PlayerReturn.Config
local CallFilter         = require "DFM.Business.DataStruct.Common.Base.CallFilter"
local Filter             = require "DFM.Business.DataStruct.Common.Base.Filter"
local WidgetUtil         = require "DFM.YxFramework.Util.WidgetUtil"
local NavigationAgent  = require "DFM.Business.DataStruct.Common.Agent.NavigationAgent"

local PlayerReturnSignInPanel = ui("PlayerReturnSignInPanel", IPlayerReturnSubActivityPanel)

-- 创建前6天的PlayerReturnSignInItem控件、第七天的IVCommonItemTemplateMod控件设置七天签到的天数标签控件文本
-- 不包括物品内容初始化
function PlayerReturnSignInPanel:CreateSubUIs()
    for day = 1, 6 do
        local weakIns, insId = Facade.UIManager:AddSubUI(self, UIName2ID.PlayerReturnSignInItem, self._wtRewardsListView)
        local itemIns = getfromweak(weakIns)
        table.insert(self._rewardsListWidgets, weakIns)
    end
    self._wtFinalPrizeDayCountLabel:SetText(PlayerReturnConfig.Localization.DayCountText[7])
    self._wtFinalPrizeDisplay = getfromweak(Facade.UIManager:AddSubUI(self, UIName2ID.IVCommonItemTemplate, self._wtFinalPrizeParentBox))
end

function PlayerReturnSignInPanel:RemoveSubUIs()
    for day = 1, 6 do
        local weakIns = self._rewardsListWidgets[day]
        local uiIns = getfromweak(weakIns) ---@type PlayerReturnSignInItem
        if uiIns then
            uiIns:RemoveItems()
        end
    end

    Facade.UIManager:RemoveSubUIByParent(self, self._wtFinalPrizeParentBox)
    Facade.UIManager:RemoveSubUIByParent(self, self._wtRewardsListView)
end

-- 更新物品展示内容、领取状态等，并将当前签到那一天的点击回调设置为领取奖励的操作
function PlayerReturnSignInPanel:UpdateDisplay()

    -- 更新奖励内容显示、已领取状况和高亮状态(1-6天)
    local impl = IPlayerReturnSubActivity.Get(ReturnActivityType.ReturnActivityTypeAttend, self._armedForceMode)
    local activityInfo = impl:GetActivityInfo() ---@type pb_ActivityInfo
    if not activityInfo then return end

    self._cachedActivityID = activityInfo.actv_id

    self._wtTitle:SetText(activityInfo.name)

    local attendInfo    = activityInfo.attend_info
    local attendStates  = activityInfo.attend_info.states

    -- 更新当前累计次数显示
    local str = StringUtil.PluralTextFormat(PlayerReturnConfig.Localization.SignIn.SignInProgressFmtStr, {days = Filter.CountIf(attendStates, function(state) return state.state == 2 end)})
    self._wtTotalSigninCountLabel:SetText(str)

    local makeSignInCallback = function(day)
        local clickedOnce = false
        return CreateCallBack(function ()
            if clickedOnce then return end
            self._doSignInFilter:Do(
                function()
                    Server.ActivityServer:SendSignAwardReq(activityInfo.actv_id, day)
                end
            )
        end, self)
    end

    -- 1-6天
    for day = 1, 6  do
        local items = {}
        for _, award in ipairs(attendStates[day].daily_award) do
            table.insert(items, ItemBase:NewIns(award.prop.id, award.prop.num))
        end

        local itemWidget = getfromweak(self._rewardsListWidgets[day]) ---@type PlayerReturnSignInItem

        itemWidget:SetState({
            dayLabel    = PlayerReturnConfig.Localization.DayCountText[day],
            items       = items,
            signInState = attendStates[day].state,
            callback    = (attendStates[day].state == 1) and makeSignInCallback(day) or nil,
        })
    end

    -- 7天
    self._wtFinalPrizeDisplay:InitItem(ItemBase:NewIns(attendStates[7].daily_award[1].prop.id, attendStates[7].daily_award[1].prop.num))
    if attendStates[7].state == 1 then
        self._wtFinalPrizeDisplay:BindCustomOnClicked(makeSignInCallback(7))
        self._wtFinalPrizeDisplay:PlayIVAnimation("WBP_CommonItemTemplate_in_special_01",0)
    else
        self._wtFinalPrizeDisplay:BindDefaultOnClicked(true)
        self._wtFinalPrizeDisplay:StopIVAnimation("WBP_CommonItemTemplate_in_special_01")
    end
    if attendStates[7].state == 2 then
        self._wtFinalPrizeDisplay:SetAlreadyGetState()
    else
        self._wtFinalPrizeDisplay:UnsetAlreadyGetState()
    end
end

function PlayerReturnSignInPanel:Ctor()
    self._armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()

    self._wtRootPaddingBox          = self:Wnd("PlatformPaddingBox_4"       , UIWidgetBase)
    self._wtTotalSigninCountLabel   = self:Wnd("DFTextBlock_86"             , UITextBlock)
    self._wtRewardsListView         = self:Wnd("DFScrollBox_276"            , UIWidgetBase)
    self._wtFinalPrizeDayCountLabel = self:Wnd("DFTextBlock_155"            , UITextBlock)
    self._wtFinalPrizeParentBox     = self:Wnd("PlatformSizeBox_0"          , UIWidgetBase)
    self._wtFinalPrizeHover         = self:Wnd("WBP_CommonHoverBg"          , UIWidgetBase)
    self._wtFinalPrizeHover:Collapsed()

    self._rewardsListWidgets = {} -- Weak<PlayerReturnSignInItem> []
    self._finalPrizeDisplay  = nil -- Weak<IVCommonItemTemplateMod>

    self._doSignInFilter = CallFilter.Interval(3) --两次签到请求要求间隔三秒

    self._navMgr = NavigationAgent.Create(self)
end

function PlayerReturnSignInPanel:OnOpen()
    self:CreateSubUIs()
    self:UpdateDisplay()
    self:AddLuaEvent(Server.ActivityServer.Events.evtSignResArrive, self._OnSignInRes, self)
end

-- Copy of ActivitySignInPanel:_OnSignResArrive
function PlayerReturnSignInPanel:_OnSignInRes(dataChange, expandInfo)
    Module.PlayerReturn:UpdateRedDot()
    self:UpdateDisplay()
    Module.Activity:ShowErrorTipIfNeeded(expandInfo)
    Module.Activity:ShowDataChangeAwards(dataChange, self._cachedActivityID)
    Module.PlayerReturn:ShowTipsIfBoostReceived(dataChange)
    self:SetDefaultFocus()
end

function PlayerReturnSignInPanel:OnClose()
    self:RemoveSubUIs()
    self:RemoveAllLuaEvent()
end

function PlayerReturnSignInPanel:OnShowBegin()
end

function PlayerReturnSignInPanel:SetDefaultFocus()
    local impl = IPlayerReturnSubActivity.Get(ReturnActivityType.ReturnActivityTypeAttend, self._armedForceMode)
    local activityInfo = impl:GetActivityInfo() ---@type pb_ActivityInfo
    if not activityInfo then return end

    local focusItem

    -- 默认聚焦第一个可领取奖励
    for day, awardInfo in ipairs(activityInfo.attend_info.states) do
        if awardInfo.state == 1 then
            if day < 7 then
                ---@type PlayerReturnSignInItem
                local child = self._rewardsListWidgets[day]
                focusItem = child:GetFirstItem()
            elseif day == 7 then
                focusItem = self._wtFinalPrizeDisplay
            end
            break
        end
    end

    if not focusItem then
        -- 否则聚焦第一个未完成奖励
        for day, awardInfo in ipairs(activityInfo.attend_info.states) do
            if awardInfo.state == 0 then
                if day < 7 then
                    ---@type PlayerReturnSignInItem
                    local child = self._rewardsListWidgets[day]
                    focusItem = child:GetFirstItem()
                elseif day == 7 then
                    focusItem = self._wtFinalPrizeDisplay
                end
                break
            end
        end
    end

    if not focusItem then
        -- 否则聚焦第一个
        ---@type PlayerReturnSignInItem
        local child = self._rewardsListWidgets[1]
        focusItem = child:GetFirstItem()
    end

    if focusItem then
        self._navMgr:FocusWidget(focusItem)
    end
end

function PlayerReturnSignInPanel:OnPlayerReturnEnter(from)
    IPlayerReturnSubActivityPanel.OnPlayerReturnEnter(self, from)

    self:UpdateDisplay()

    if IsHD() then
        self._navMgr:CreateGroup({
            id = "RewardItems",
            rootWidget = self._wtRootPaddingBox,
            members = {self._wtRootPaddingBox},
            bStack = true,
        })

        if from ~= ReturnActivityType.ReturnActivityTypeAttend then
            Timer.DelayCall(0.5, CreateCallBack(function()
                -- self._navMgr:FocusGroup("RewardItems")
                self:SetDefaultFocus()
            end, self))
        end
    end
    
end

function PlayerReturnSignInPanel:OnPlayerReturnLeave(to)
    IPlayerReturnSubActivityPanel.OnPlayerReturnLeave(self, to)
    if IsHD() then
        self._navMgr:RemoveAllGroups()
    end
end

return PlayerReturnSignInPanel
