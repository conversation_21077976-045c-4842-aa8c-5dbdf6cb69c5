----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------

-- comment: 生命值是核心指标，关联更多的UI变化和系统，需要实时监控
---@class ActivityMagicTowerPlayer
---@field _maxHealth number 最大生命值
---@field _health number 生命
---@field _attack number 攻击
---@field _defense number 防御
---@field _hitProb number 暴击
---@field _avatarIsset string 头像
---@field _exp number 经验值
---@field _keys number 钥匙数量
---@field _money number 龙门币
---@field _onHealthChanged function 生命值变化回调
---@field _onDeath function 死亡回调

local ActivityMagicTowerPlayer = {}

function ActivityMagicTowerPlayer.New(initConfig, currencyConfig)
    local obj = setmetatable({}, {__index = ActivityMagicTowerPlayer})
    obj:Init(initConfig, currencyConfig)
    return obj
end

function ActivityMagicTowerPlayer:Init(initConfig, currencyConfig)
    -- 基础战斗属性
    self._maxHealth = initConfig and initConfig.maxHealth or 100
    -- self._health = initConfig and initConfig.health or 100
    self._health = 5000 -- Debug模式
    self._attack = initConfig and initConfig.attack or 10
    self._defense = initConfig and initConfig.defense or 5
    self._hitProb = initConfig and initConfig.hitProb or 0.1
    self._avatarIsset = initConfig and initConfig.avatarAsset or ""

    -- 资源属性
    self._exp = currencyConfig and currencyConfig.exp or 0
    self._keys = currencyConfig and currencyConfig.keys or 0
    self._money = currencyConfig and currencyConfig.money or 0

    -- 回调函数
    self._onHealthChanged = nil
    self._onDeath = nil

    -- 记录最后一次变更类型
    self._lastChangeTypes = {
        health = -1,
        attack = -1,
        defense = -1,
        hitProb = -1,
        exp = -1,
        keys = -1,
        money = -1
    }

    loginfo(string.format(
        "[Magic_Tower] 玩家初始化 HP:%d/%d ATK:%d DEF:%d CRIT:%.1f%%",
        self._health, self._maxHealth, 
        self._attack, self._defense,
        self._hitProb * 100
    ))
end


function ActivityMagicTowerPlayer:GetMaxHealth() return self._maxHealth end
function ActivityMagicTowerPlayer:GetHealth() return self._health end
function ActivityMagicTowerPlayer:GetAttack() return self._attack end
function ActivityMagicTowerPlayer:GetDefense() return self._defense end
function ActivityMagicTowerPlayer:GetHitProb() return self._hitProb end
function ActivityMagicTowerPlayer:GetExp() return self._exp end
function ActivityMagicTowerPlayer:GetKeys() return self._keys end
function ActivityMagicTowerPlayer:GetMoney() return self._money end
function ActivityMagicTowerPlayer:GetLastChangeTypes() return self._lastChangeTypes end
function ActivityMagicTowerPlayer:GetAvatarAsset() return self._avatarIsset end


-- 属性修改方法
---@return boolean
function ActivityMagicTowerPlayer:ModifyHealth(value, changeType)
    local _, changed = self:_ModifyAttribute("_health", value, changeType, true)
    return changed
end

function ActivityMagicTowerPlayer:ModifyAttack(value, changeType)
    local _, changed = self:_ModifyAttribute("_attack", value, changeType)
    return changed
end

function ActivityMagicTowerPlayer:ModifyDefense(value, changeType)
    local _, changed = self:_ModifyAttribute("_defense", value, changeType)
    return changed
end

function ActivityMagicTowerPlayer:ModifyHitProb(value, changeType)
    local _, changed = self:_ModifyAttribute("_hitProb", value, changeType)
    return changed
end

function ActivityMagicTowerPlayer:ModifyExp(value, changeType)
    local _, changed = self:_ModifyAttribute("_exp", value, changeType)
    return changed
end

function ActivityMagicTowerPlayer:ModifyKeys(value, changeType)
    local _, changed = self:_ModifyAttribute("_keys", value, changeType)
    return changed
end

function ActivityMagicTowerPlayer:ModifyMoney(value, changeType)
    local _, changed = self:_ModifyAttribute("_money", value, changeType)
    return changed
end

-- 通用属性修改方法
---@return number, boolean
function ActivityMagicTowerPlayer:_ModifyAttribute(field, value, changeType, isHealth)
    if not field or type(value) ~= "number" or not changeType then
        return self[field] or 0, false
    end
    
    local oldValue = self[field]
    local newValue = oldValue
    
    if changeType == 1 then
        newValue = oldValue + value
    elseif changeType == 2 then
        newValue = oldValue - value
    elseif changeType == 3 then
        newValue = oldValue * value
    elseif changeType == 4 then
        newValue = value
    else
        return oldValue, false
    end

    -- 预到达负值也播放动画，如果不想播放动画需要用 isNegative 标记负值操作
    newValue = math.max(newValue, 0)
    
    -- 特殊处理生命值
    if isHealth then
        newValue = math.min(newValue, self._maxHealth)
        if newValue <= 0 then
            newValue = 0
            self:_HandleDeath()
        end
        if self[field] == newValue then
            return newValue, false
        end
        self:_HandleHealthChanged(newValue)
    end
    
    self[field] = newValue

    -- 动画类型
    local changeTypeForUI = 0
    if newValue > oldValue then
        changeTypeForUI = isHealth and 0 or 2  -- 生命值绿涨(0)，其他黄涨(2)
    elseif newValue < oldValue then
        changeTypeForUI = 1  -- 统一红跌(1)
    end

    -- 更新最后变更类型
    local field_str2 = string.sub(field, 2)
    self._lastChangeTypes[field_str2] = changeTypeForUI
    
    loginfo(string.format(
        "[Magic_Tower] 属性变更 %s: %s -> %s (%s)",
        field_str2, oldValue, newValue, tostring(changeType)
    ))
    
    return newValue, true
end

-- 回调处理
function ActivityMagicTowerPlayer:_HandleDeath()
    if self._onDeath then
        self._onDeath()
    end

    logwarning("[Magic_Tower] 玩家死亡")
end

function ActivityMagicTowerPlayer:_HandleHealthChanged(newHealthValue)
    if self._onHealthChanged then
        self._onHealthChanged(newHealthValue, self._maxHealth)
    end
end

-- 回调设置
function ActivityMagicTowerPlayer:SetHealthChangedCallback(callback)
    self._onHealthChanged = callback
end

function ActivityMagicTowerPlayer:SetDeathCallback(callback)
    self._onDeath = callback
end

-- 检查是否存活
function ActivityMagicTowerPlayer:IsAlive()
    return self._health > 0
end

-- 重置为无变化状态
function ActivityMagicTowerPlayer:ResetLastChangeTypes()
    for k, _ in pairs(self._lastChangeTypes) do
        self._lastChangeTypes[k] = -1
    end
end

-- 获取玩家状态快照
function ActivityMagicTowerPlayer:GetStatusSnapshot()
    return {
        health = self._health,
        maxHealth = self._maxHealth,
        attack = self._attack,
        defense = self._defense,
        hitProb = self._hitProb,
        exp = self._exp,
        keys = self._keys,
        money = self._money,
        isAlive = self:IsAlive()
    }
end

return ActivityMagicTowerPlayer