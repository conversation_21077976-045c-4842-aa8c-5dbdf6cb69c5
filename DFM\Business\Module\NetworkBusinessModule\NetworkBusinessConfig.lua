----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMNetworkBusiness)
----- LOG FUNCTION AUTO GENERATE END -----------




UITable[UIName2ID.XunYouSpeedTips] = {
    UILayer = EUILayer.Pop,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "NetworkBusinessModule.UI.XunYou.XunYouSpeedTips"),
    BPKey = "WBP_XunYou_Speed_Tips",
}

UITable[UIName2ID.XunYouSpeedUpWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "NetworkBusinessModule.UI.XunYou.XunYouSpeedUpWindow"),
    BPKey = "WBP_XunYou_Speedup_Pop",
    IsModal = true,
}

UITable[UIName2ID.XunYouConfirmWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "NetworkBusinessModule.UI.XunYou.XunYouConfirmWindow"),
    BPKey = "WBP_XunYou_Confirm_Pop",
}

UITable[UIName2ID.SwitchIDCPanel] = {
    UILayer = EUILayer.Pop,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "NetworkBusinessModule.UI.IDC.SwitchIDCPanel"),
    BPKey = "WBP_SetUp_PopWindow_SwitchIDC",
    SubUIs ={
        UIName2ID.ZoneItem,
    },
    IsModal = true
}

UITable[UIName2ID.ZoneItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "NetworkBusinessModule.UI.IDC.ZoneItem"),
    BPKey = "WBP_SetUp_PopWindow_SwitchIDCItem"
}

local NetworkBusinessConfig = 
{
    Loc = {
        SpeedUpNow = NSLOCTEXT("NetworkBusinessModule", "Lua_XunYou_SpeedUpNow", "立即加速"),
        NetworkStatus_Good = NSLOCTEXT("NetworkBusinessModule", "Lua_XunYou_NetworkStatus_Good", "网络状态优秀"),
        NetworkStatus_Normal = NSLOCTEXT("NetworkBusinessModule", "Lua_XunYou_NetworkStatus_Normal", "可能导致的问题：\n操作响应略微延迟\n服务器重连"),
        NetworkStatus_Poor = NSLOCTEXT("NetworkBusinessModule", "Lua_XunYou_NetworkStatus_Poor", "可能导致的问题：\n操作响应不及时\n网络波动\n画面卡顿\n游戏掉线\n无法重连服务器"),
        NetworkStatus_Advice = NSLOCTEXT("NetworkBusinessModule", "Lua_XunYou_NetworkStatus_Advice", "建议：使用网络加速器降低延迟"),
        XunYou = NSLOCTEXT("NetworkBusinessModule", "Lua_XunYou_XunYou", "迅游加速器"),
        LimitTime = NSLOCTEXT("NetworkBusinessModule", "Lua_XunYou_LimitTime", "限时"),
        FreeSpeedUp = NSLOCTEXT("NetworkBusinessModule", "Lua_XunYou_FreeSpeedUp", "免费加速"),
        StartXunYou = NSLOCTEXT("NetworkBusinessModule", "Lua_XunYou_StartXunYou", "正在为您启动迅游三角洲行动专用加速器，是否继续？"),
        DownloadXunYou = NSLOCTEXT("NetworkBusinessModule", "Lua_XunYou_DownloadXunYou", "正在为您安装迅游三角洲行动专用加速器，是否继续？"),
        XunYouSpeedTips = NSLOCTEXT("NetworkBusinessModule", "Lua_XunYou_SpeedTips", "网络延迟较高，可点击大厅网络图标进行网络加速"),
		IdcSpeedTest = NSLOCTEXT("NetworkBusinessModule", "Lua_XunYou_IdcSpeedTest", "测速中"),
        Installing = NSLOCTEXT("NetworkBusinessModule", "Lua_XunYou_Installing", "正在安装中，请稍候..."),
        NQTips = NSLOCTEXT("NetworkBusinessModule", "Lua_XunYou_NQTips", "您当前网络延迟高达%sms，可点击左上角更多功能中的迅游加速了解详情"),
        NQWindowTips = NSLOCTEXT("NetworkBusinessModule", "Lua_XunYou_NQWindowTips", "您当前网络延迟高达%sms，免费领取加速，有效保障网络稳定"),
        ConfirmText = NSLOCTEXT("NetworkBusinessModule", "Lua_XunYou_ConfirmText", "确认"),
        CancelText = NSLOCTEXT("NetworkBusinessModule", "Lua_XunYou_CancelText", "取消"),
        IDCInfo_Unknow = NSLOCTEXT("NetworkBusinessModule", "Lua_XunYou_IdcInfo_Unknow", "%s 未知"),
    },
    Events = {
        evtOnAnnouncementBeginVersionUpdate = LuaEvent:NewIns("GCloudSDKConfig.evtOnAnnouncementBeginVersionUpdate"),
        evtOnIdcSelectorChanged = LuaEvent:NewIns("NetworkBusinessModule.evtOnIdcSelectorChanged"),
    },

    -- 迅游参数配置
    XunYouID = 365764076,
    GameID = 29750,
    AreaID = 79711,
    GUID_Exper = "20799BCF-463F-4B84-2AEC-AE5AD3B93386",
    GUID = "EFC68577-1342-5310-C34F-928C23442614",
    NetLibName = "libUE4.so",
    HookType = 2,
    EchoPort = 65010,
    NQWindowInterval = 2592000,
    XunYouUrl = "https://pay.xunyou.mobi",
    XunYouVipStatusMailCfgID = 54,
    Ping = 150,
    XunYouABID = 11400001,
    OpenXunYouPosition = {
        Mail = 5,
        OpAct = 7,
        LobbyWindow = 21
    },

}

return NetworkBusinessConfig
