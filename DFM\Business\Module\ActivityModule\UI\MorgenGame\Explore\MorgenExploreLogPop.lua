----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------


--- @class MorgenExploreLogPop : LuaUIBaseView
local MorgenExploreLogPop = ui("MorgenExploreLogPop")
local ActivityConfig = Module.Activity.Config
local ActivityLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"
local CommonPopWindows = require "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindows"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import "EGPUINavWidgetFocusedAction"
local EGPUINavScrollingCondition = import("EGPUINavScrollingCondition")

function MorgenExploreLogPop:Ctor()
    self._wtCommonPopWin = self:Wnd("wtRootWindow", CommonPopWindows)
    self._wtCommonPopWin:BindCloseCallBack(CreateCallBack(self.OnCloseBtnClicked, self))
    self._wtCommonPopWin:SetBackgroudClickable(true)

    self._wtModeText = self:Wnd("DFTextBlock_0", UITextBlock)
    self._wtTimeText = self:Wnd("DFTextBlock_70", UITextBlock)
    self._wtResultText = self:Wnd("DFTextBlock_2", UITextBlock)
    self._wtBorderText = self:Wnd("DFTextBlock", UITextBlock)
    self._wtCostText = self:Wnd("DFTextBlock_3", UITextBlock)
    self._wtBringText = self:Wnd("DFTextBlock_1", UITextBlock)

    ---空状态slot
    self._wtEmptySlot = self:Wnd("EmptySlot", UIWidgetBase)

    self._wtHistoryScroll = UIUtil.WndWaterfallScrollBox(self, "DFWaterfallScrollView_41", self._OnGetHistoryCount, self._OnProcessHistoryWidget)
end

-----------------------------------------------------生命周期-----------------------------------------------------
--#region
function MorgenExploreLogPop:OnInitExtraData(historyInfos)
    self.historyInfos = historyInfos
    self:BuildHistoryData()
end

--- 处理历史数据
function MorgenExploreLogPop:BuildHistoryData()
    --- 时间排序
    table.sort(self.historyInfos, function(a, b)
        return a.time > b.time
    end)
end

function MorgenExploreLogPop:OnOpen()
    self:SetAllText()
end

function MorgenExploreLogPop:OnShowBegin()
    self:InitGamepad()
end

function MorgenExploreLogPop:OnShow()
    self:RefreshUI()
end

function MorgenExploreLogPop:OnHideBegin()
    self:DisableGamepad()
end
--#endregion

-----------------------------------------------------响应操作-----------------------------------------------------
--#region

function MorgenExploreLogPop:RefreshUI()
    local bIsEmpty = #self.historyInfos == 0
    self:SetEmptyStyle(bIsEmpty)
    if bIsEmpty then
        self._wtHistoryScroll:Collapsed()
    else
        self._wtHistoryScroll:Visible()
        self._wtHistoryScroll:RefreshAllItems()
        self._wtHistoryScroll:ScrollToStart()
    end
end

--- 历史信息列表
function MorgenExploreLogPop:_OnGetHistoryCount()
    return #self.historyInfos or 0
end

function MorgenExploreLogPop:_OnProcessHistoryWidget(index, widget)
    local historyInfo = self.historyInfos[index]
    widget:RefreshInfo(historyInfo)
end

function MorgenExploreLogPop:SetEmptyStyle(bIsEmpty)
    Facade.UIManager:RemoveSubUIByParent(self, self._wtEmptySlot)
    if bIsEmpty then
        self._wtEmptySlot:Visible()
        local weakUIIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.CommonEmptyContent, self._wtEmptySlot)
        local uiIns = getfromweak(weakUIIns)
        if uiIns then
            uiIns:BP_SetTypeWithParam(1)
            uiIns:Visible()
            uiIns:BP_SetText(Module.Store.Config.Loc.NothingContent)
        end
    else
        self._wtEmptySlot:Collapsed()
    end
end
--#endregion

-----------------------------------------------------其他函数-----------------------------------------------------
--#region

function MorgenExploreLogPop:SetAllText()
    self._wtCommonPopWin:SetTitle(ActivityConfig.Loc.MogenExploreLogText[1])

    self._wtModeText:SetText(ActivityConfig.Loc.MogenExploreLogText[2])
    self._wtTimeText:SetText(ActivityConfig.Loc.MogenExploreLogText[3])
    self._wtResultText:SetText(ActivityConfig.Loc.MogenExploreLogText[4])
    self._wtBorderText:SetText(ActivityConfig.Loc.MogenExploreLogText[5])
    self._wtCostText:SetText(ActivityConfig.Loc.MogenExploreLogText[6])
    self._wtBringText:SetText(ActivityConfig.Loc.MogenExploreLogText[7])
end

function MorgenExploreLogPop:OnNavBack()
    self:OnCloseBtnClicked()
    return true
end

function MorgenExploreLogPop:OnCloseBtnClicked()
    Facade.UIManager:CloseUI(self)
end

function MorgenExploreLogPop:InitGamepad()
    if not IsHD() then return end
    self:DisableGamepad()

    -- -- 抽奖历史导航
    -- if not self._historyListGroup then
    --     self._historyListGroup = WidgetUtil.RegisterNavigationGroup(self._wtHistoryScroll, self, "Hittest")
    -- end
    -- if self._historyListGroup then
    --     self._historyListGroup:AddNavWidgetToArray(self._wtHistoryScroll)
    --     -- self._historyListGroup:SetScrollRecipient(self._wtHistoryScroll)
    --     self._historyListGroup:SetScrollInfo(self._wtHistoryScroll, EGPUINavScrollingCondition.None)
    --     WidgetUtil.TryFocusDefaultWidgetByGroup(self._historyListGroup)
    -- end

    if not self._historyListGroup then
        self._historyListGroup = WidgetUtil.RegisterNavigationGroup(self._wtHistoryScroll, self, "Hittest")
        if self._historyListGroup then
            self._historyListGroup:SetScrollInfo(self._wtHistoryScroll, EGPUINavScrollingCondition.None)
            self._historyListGroup:MarkIsStackControlGroup()
        end
    end
end

function MorgenExploreLogPop:DisableGamepad()
    if not IsHD() then return end

    WidgetUtil.RemoveNavigationGroup(self)
    self._historyListGroup = nil
end
--#endregion

return MorgenExploreLogPop