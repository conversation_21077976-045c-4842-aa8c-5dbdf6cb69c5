----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMLooting)
----- LOG FUNCTION AUTO GENERATE END -----------


local TabUIContentData = require"DFM.Business.DataStruct.UIDataStruct.TabUIContentData"
local LootPickupTitlePanel = require "DFM.Business.Module.LootingModule.UI.Common.LootPickupTitlePanel"
local LootingLogic = require "DFM.Business.Module.LootingModule.LootingLogic"
local LootingConfig = require "DFM.Business.Module.LootingModule.LootingConfig"
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local LootPriceWidget = require "DFM.Business.Module.LootingModule.UI.Common.LootPriceWidget"
local CommonDragDropMask = require "DFM.Business.Module.CommonWidgetModule.UI.CommonDragDropMask"
local LootPickupSlotPanel = require "DFM.Business.Module.LootingModule.UI.Common.LootPickupSlotPanel"
local LootDeadbodyPanel_HD = require "DFM.Business.Module.LootingModule.UIHD.LootDeadbodyPanel_HD"
local LootingSearchLogic = require "DFM.Business.Module.LootingModule.LootingSearchLogic"
local LootingAudioLogic = require "DFM.Business.Module.LootingModule.LootingAudioLogic"
local CommonLoadButton     = require "DFM.Business.Module.CommonWidgetModule.UI.Weight.CommonLoadButton"

local LootEquipSlotPanel = require "DFM.Business.Module.LootingModule.UI.Common.LootEquipSlotPanel"
local LootSafeBoxSlotPanel = require "DFM.Business.Module.LootingModule.UI.Common.LootSafeBoxSlotPanel"
local LootBackpackPanel = require "DFM.Business.Module.LootingModule.UI.Common.LootBackpackPanel"
local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"
local CharacterCaptureUtil = require "DFM.Business.Module.CommonWidgetModule.UI.HD.CharacterCaptureUtil"
local SOLHealthMainView_HD = require "DFM.Business.Module.SOLHealthSystemModule.UIHD.SOLHealthMainView_HD"
local LootPriceDetail = require "DFM.Business.Module.LootingModule.UI.Common.LootPriceDetail"
local LootingInputLogic = require "DFM.Business.Module.LootingModule.Logic.LootingInputLogic"
local LootEquipPanel_HD = require "DFM.Business.Module.LootingModule.UIHD.LootEquipPanel_HD"
local LootBackpackPanelV2_HD = require "DFM.Business.Module.LootingModule.UIHD.LootBackpackPanelV2_HD"
local WarehouseContainerPanel_HD = require "DFM.Business.Module.InventoryModule.UI.Common.WarehouseContainerPanel_HD"
local LootingLitter_HD = require "DFM.Business.Module.LootingModule.UI.Common.LootingLitter_HD"
local LootingKeyMapping_HD = require "DFM.Business.Module.LootingModule.UIHD.LootingKeyMapping_HD"
local InventoryNavManager = require "DFM.Business.Module.InventoryModule.Logic.InventoryNavManager"

local UWidgetLayoutLibrary = import "WidgetLayoutLibrary"
local ULuaExtension = import "LuaExtension"
local UKismetSystemLibrary = import "KismetSystemLibrary"
local UGameplayStatics = import "GameplayStatics"
local UDFMGameplayDelegates = import "DFMGameplayDelegates"
local ABaseHUD = import "BaseHUD"
local LayerZorder = import "LayerZorder"
local GameHUDState = import "GameHUDSate"

--BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
local GPUINavigationStrategy_Hittest = import("GPUINavigationStrategy_Hittest")
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local UGPInputHelper = import "GPInputHelper"
local EGPInputType = import"EGPInputType"
local UGPInputDelegates = import "GPInputDelegates"
local EGPUINavHittestFallbackType = import "EGPUINavHittestFallbackType"
local UGPUINavigationUtils = import("GPUINavigationUtils")
local EDescendantScrollDestination = import "EDescendantScrollDestination"
local EGPUINavGroupTransSimpleStrategy = import "EGPUINavGroupTransSimpleStrategy"
local UDFNavigationSelectorBase = import("DFNavigationSelectorBase")
--END MODIFICATION

---@class LootingMainView_HD : LuaUIBaseView
local LootingMainView_HD = ui("LootingMainView_HD")

local function log(...)
    loginfo("[LootingMainView_HD]", ...)
end

local eTabIndex = {
    Character = 0,
    Health = 1,
    Score = 2
}

local eDiscardStyle = {
    Off = 0,
    LeftOn = 1,
    RightOn = 2
}

function LootingMainView_HD:Ctor()
    InventoryNavManager.Reset()
    log("Ctor")
    self.pickupType = nil
    self.interactors = {}
    self.closeActionHandle = nil
    
    self:_InitMainTabs()
    self._wtRootCanvas = self:Wnd("wtRootCanvas", UIWidgetBase)
    self._wtCaptureImg = self:Wnd("wtCaptureImg", CharacterCaptureUtil)
    self._wtCaptureImg:SetUseSimpleFaceAnim(true)
    self._wtPriceDetailPanel = self:Wnd("wtPriceDetailPanel", LootPriceDetail)
    self._wtWeightBtn = self:Wnd("LootPriceWeightBtn",CommonLoadButton)
    
    self._wtLootEquipPanel = self:Wnd("wtLootEquipPanel_1", LootEquipPanel_HD)
    self._wtLootBackpackPanel = self:Wnd("wtLootBackpackPanel", LootBackpackPanelV2_HD)
    
    self._wtLootRoot = self:Wnd("wtLootRoot", UIWidgetBase)
    self._wtLootPickupTitlePanel = self:Wnd("wtLootPickupTitlePanel", LootPickupTitlePanel)

    self._wtLootingLitter = self:Wnd("WBP_LootingLitter_Pc", LootingLitter_HD)
    InventoryNavManager.litterPanel = self._wtLootingLitter
    self._wtLootDeadbodyPanel = self:Wnd("wtLootDeadbodyPanel", LootDeadbodyPanel_HD)
    

    self._wtGameRemainTime = self:Wnd("wtGameRemainTime", UITextBlock)

    self._wtAutoSnapArea = self:Wnd("wtAutoSnapArea", UIWidgetBase)
    self._wtBlur = self:Wnd("wtBlur", UIWidgetBase)
    self._wtFashionBtn = self:Wnd("wtDFCommonCheckBoxWithText", DFCheckBoxWithText)
    self._wtFashionBtn:Event("OnCheckStateChanged", self._OnFashionBtnCheckStateChanged, self)
    if Facade.GameFlowManager:IsInOBMode() then
        self._wtFashionBtn:Collapsed()
    else
        self._wtFashionBtn:SelfHitTestInvisible()
    end
    self._wtTeammateSigns = self:Wnd("DFHorizontalBox_0", UIWidgetBase)
    self._wtTeammate1Sign = self:Wnd("WBP_LootingMark", UIWidgetBase)
    self._wtTeammate1No = self._wtTeammate1Sign:Wnd("DFTextBlock", UITextBlock)
    self._wtTeammate1Bg = self._wtTeammate1Sign:Wnd("DFImage_217", UIImage)
    self._wtTeammate2Sign = self:Wnd("WBP_LootingMark_1", UIWidgetBase)
    self._wtTeammate2No = self._wtTeammate2Sign:Wnd("DFTextBlock", UITextBlock)
    self._wtTeammate2Bg = self._wtTeammate2Sign:Wnd("DFImage_217", UIImage)

    self:_InitDragDropPanel()

    --BEGIN MODIFICATION @ VIRTUOS : 
    if IsHD() then
        self:InitWHContainerViewConfig()
        self:CreateDiscardFocusItem()
        self.bHoverItemViewUseAnimByGamepad = false
        self.ItemInUsingAnim = nil
    end
	--END MODIFICATION

    self._wtBreathingBarSlot = self:Wnd("DFNamedSlot_113", UIWidgetBase)
    self._wtBreathingBarSlot.Slot:SetZOrder(25)
    self._breathingBarInsId = nil
    self._wtTimeBox = self:Wnd("TimeBox", UIWidgetBase)
    self._wtLootingKeyMapping = self:Wnd("wtLootingKeyMapping", LootingKeyMapping_HD)

    -- 肩键提示
    if IsHD() then
        self._wtLeftShoulderKeyLeft = self:Wnd("KeyIcon_04", HDKeyIconBox)
        self._wtRightShoulderKeyLeft = self:Wnd("KeyIcon_03", HDKeyIconBox)
        self._wtLeftShoulderKeyMid = self:Wnd("KeyIcon_01", HDKeyIconBox)
        self._wtRightShoulderKeyMid = self:Wnd("KeyIcon_02", HDKeyIconBox)
        self._wtLeftShoulderKeyRight = self:Wnd("KeyIcon_05", HDKeyIconBox)
        self._wtRightShoulderKeyRight = self:Wnd("KeyIcon_06", HDKeyIconBox)

    
        self._wtLeftShoulderKeyLeft:InitByDisplayInputActionName("Common_SwitchToPrevTab_Trigger", true, 0.0, false)
        self._wtRightShoulderKeyLeft:InitByDisplayInputActionName("Common_SwitchToNextTab_Trigger", true, 0.0, false)
        self._wtLeftShoulderKeyMid:InitByDisplayInputActionName("Common_SwitchToPrevTab_Trigger", true, 0.0, false)
        self._wtRightShoulderKeyMid:InitByDisplayInputActionName("Common_SwitchToNextTab_Trigger", true, 0.0, false)
        self._wtLeftShoulderKeyRight:InitByDisplayInputActionName("Common_SwitchToPrevTab_Trigger", true, 0.0, false)
        self._wtRightShoulderKeyRight:InitByDisplayInputActionName("Common_SwitchToNextTab_Trigger", true, 0.0, false)
    end
end

function LootingMainView_HD:InitPanel(pickupType, interactors, bRefreshAppearance)
    bRefreshAppearance = setdefault(bRefreshAppearance, false)
    log("InitPanel", pickupType, interactors)
    self.pickupType = pickupType
    self.interactors = interactors
    InventoryNavManager.SetPickUpType(pickupType)

    local bShowPlayerDeadbody = false

    if pickupType == nil then
        self._wtLootRoot:Collapsed()
        self:BP_SetRightDiscardType(true)
        InventoryNavManager.FocusToDefaultFocusPoint()
        return
    end

    self._wtLootRoot:SelfHitTestInvisible()
    if pickupType == EGamePickupType.NearbyDeadBody then
        self._wtLootDeadbodyPanel:SelfHitTestInvisible()
        self._wtLootingLitter:Collapsed()
        self._bShowPlayerDeadbody = true
        if bRefreshAppearance then
            self._wtLootDeadbodyPanel:RefreshAppearance()
        end
        self:BP_SetRightDiscardType(false)
    else
        self._wtLootDeadbodyPanel:Collapsed()
        self._wtLootingLitter:SelfHitTestInvisible()
        if pickupType == EGamePickupType.NearbyPickups then
            self._wtLootingLitter:InitContainerSlot(ESlotType.NearbyPickups, ESlotGroup.Nearby)
        elseif pickupType == EGamePickupType.SceneBox then
            self._wtLootingLitter:InitContainerSlot(ESlotType.NearbyContainer, ESlotGroup.Nearby)
        elseif pickupType == EGamePickupType.DropContainer then
            local dropContainerSlot = Server.LootingServer.dropContainerSlot
            self._wtLootingLitter:InitContainerSlot(dropContainerSlot.SlotType, dropContainerSlot:GetSlotGroup())
        end
        self:BP_SetRightDiscardType(true)
    end

    self._bShowPlayerDeadbody = bShowPlayerDeadbody

    self._wtLootPickupTitlePanel:InitPanel(pickupType, interactors)
    InventoryNavManager.FocusToDefaultFocusPoint()
end

function LootingMainView_HD:IsBagView()
    return self.pickupType == nil
end

function LootingMainView_HD:OnShowBegin()
    -- 切换Hud状态
    local hudStateManager = UE.HUDStateManager.Get(GetWorld())
    if hudStateManager then
        hudStateManager:AddState(UE.GameHUDSate.GHS_RealOpenBag, false)
    end
    Module.Inventory:SetInvSlotViewType(Module.Inventory.Config.EInvSlotViewType.Default)
    self._wtFashionBtn:SetIsChecked(Module.LobbyDisplay:GetCharacterDisplayEquipment(), false)
end

function LootingMainView_HD:OnShow()
    -- LightUtil.SetSkyLightEnable(false)
    -- GPM上报
    LootingLogic.GPMReportOpenLoot(true)
    -- 注册事件
    self:_AddEvents()

    self._wtMainTabs:RefreshTab()
    -- self._wtMainTabs:SetTabIndex(0)
    InventoryNavManager.deadbodyPanel = self._wtLootDeadbodyPanel
    local priceWidget = self._wtLootBackpackPanel:GetPriceWidget()
    if Facade.GameFlowManager:IsInOBMode() then
        self.wtMainTabsNavigator:Collapsed()
        self._wtWeightBtn:Collapsed()
        self._wtLootingKeyMapping:Collapsed()
        priceWidget:SelfHitTestInvisible()
        self._wtTimeBox:SelfHitTestInvisible()
    elseif ItemOperaTool.bIsInCollectionRoom then
        self.wtMainTabsNavigator:Collapsed()
        self._wtWeightBtn:Collapsed()
        self._wtLootingKeyMapping:SelfHitTestInvisible()
        priceWidget:Collapsed()
        self._wtTimeBox:Collapsed()
    else
        self.wtMainTabsNavigator:SelfHitTestInvisible()
        self._wtWeightBtn:SelfHitTestInvisible()
        self._wtLootingKeyMapping:SelfHitTestInvisible()
        -- 注册按键
        self.wtMainTabsNavigator:BindActions()
        priceWidget:SelfHitTestInvisible()
        self._wtTimeBox:SelfHitTestInvisible()
    end

    -- 注册Tick
    if not ItemOperaTool.bIsInCollectionRoom then
        LuaTickController:Get():RegisterTick(self)
    end

    Server.LootingServer:ServerCheckFrequentDataUpdated()
    Server.InventoryServer:CalculateLootingAllCarryItemsWeight()

    Module.CommonWidget:SetAutoSnapEnabled(true)
    self:_RefreshTab()

    -- self:_ChangeOtherHUDLayer(true)
    Facade.LuaFramingManager:RegisterFrameTask(self._ChangeOtherHUDLayer, self, {true})

    LootingConfig.Events.evtOpenLootingUI:Invoke()

    self:PlayWidgetAnim(self.Anima_LootPickupView_In, 1, EUMGSequencePlayMode.Forward, 1, true)

    LootingAudioLogic.Play_LootOpen()

    self:_RefreshLootingTeammates()
    
    --BEGIN MODIFICATION @ VIRTUOS : 
    if IsHD() then
        self:_RegisterNavGroup()
        Module.CommonWidget:SetGamepadSwapItem(false)
        -- 开启手柄移动物品功能
        Module.CommonWidget:EnableGamepadSwapItem(true)
        Module.CommonWidget:EnableGamepadCarryItemFromPop(true)
        Module.CommonWidget:EnableGamepadItemShortcutKey(true)
        self:_EnableSimulatedMouseDragHandle(true)

        self:_EnableInputTypeChangedHandle(true)

        self:EnableGamepadInputShorts(true)

        -- 辅助手柄显示提示气泡
        self._curShowTipsIndex = 0

        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default, self)

        -- 用手柄打开页面时设置为ForceUIOnly
        self:EnableForceUIOnlyByGamepad(true)

        self.bHoverItemViewUseAnimByGamepad = false
        self.ItemInUsingAnim = nil

        self:_SetEnableHeroInfoBtns(true)

        local navMgr =  WidgetUtil.GetNavigationMgr()
        if self._usingFreeAnalogCursorStateChangedEventHandle then
            navMgr.OnUsingFreeAnalogCursorStateChanged:Remove(self._usingFreeAnalogCursorStateChangedEventHandle)
            self._usingFreeAnalogCursorStateChangedEventHandle = nil
        end
        self._usingFreeAnalogCursorStateChangedEventHandle = navMgr.OnUsingFreeAnalogCursorStateChanged:Add(CreateCPlusCallBack(self._OnUsingFreeAnalogCursorStateChanged,self))
        self:EnableGamepadInputShorts(true)
    end
    --END MODIFICATION
    local hudLayerController = Facade.UIManager:GetHUDLayerController()
    if hudLayerController then
        self:_CheckShowOrHideBreathingBar(hudLayerController.state)
    end
    InventoryNavManager.FocusToDefaultFocusPoint()
end

function LootingMainView_HD:OnHide()
    -- LightUtil.SetSkyLightEnable(true)
    -- GPM上报
    LootingLogic.GPMReportOpenLoot(false)

    self:_RemoveEvents()

    -- 关闭价值浮窗
    self:_OnShowPriceDetailPanel(false)

    -- 关闭积分板
    self:HideScoreHud()

    if not Facade.GameFlowManager:IsInOBMode() then
        -- 注销按键
        self.wtMainTabsNavigator:UnbindActions()

    end

    LuaTickController:Get():RemoveTick(self)

    local hudStateManager = UE.HUDStateManager.Get(GetWorld())
    if hudStateManager then
        hudStateManager:RemoveState(UE.GameHUDSate.GHS_RealOpenBag)
    end

    Module.CommonWidget:SetAutoSnapEnabled(false)

    local pickupSlot = Server.InventoryServer:GetSlot(ESlotType.NearbyPickups, ESlotGroup.Nearby)
    pickupSlot:ClearCachedLoc()

    -- self:_ChangeOtherHUDLayer(false)
    Facade.LuaFramingManager:RegisterFrameTask(self._ChangeOtherHUDLayer, self, {false})

    LootingConfig.Events.evtCloseLootingUI:Invoke()

    Module.ItemDetail:CloseAllPopUI()

    --BEGIN MODIFICATION @ VIRTUOS : 
    if IsHD() then
        self:_RemoveNavGroup()
        -- 页面关闭时清除物品交换的记录 （每一个需要物品交换功能的主页面都需要）
        Module.CommonWidget:SetGamepadSwapItem(false)
        Module.CommonWidget:StopFocusItemView()
        -- 关闭手柄移动物品功能
        Module.CommonWidget:EnableGamepadSwapItem(false)
        Module.CommonWidget:EnableGamepadCarryItemFromPop(false)
        Module.CommonWidget:EnableGamepadItemShortcutKey(false)
        self:_EnableSimulatedMouseDragHandle(false)

        self:_EnableInputTypeChangedHandle(false)
        self:EnableGamepadInputShorts(false)
        self:CloseHoverTips()
        self._curShowTipsIndex = 0

        WidgetUtil.DisableDynamicNavConfigsbyWidget(self)

        -- 关闭ForceUIOnly
        self:EnableForceUIOnlyByGamepad(false)

        self.bHoverItemViewUseAnimByGamepad = false
        self.ItemInUsingAnim = nil
        local navMgr =  WidgetUtil.GetNavigationMgr()
        if self._usingFreeAnalogCursorStateChangedEventHandle then
            navMgr.OnUsingFreeAnalogCursorStateChanged:Remove(self._usingFreeAnalogCursorStateChangedEventHandle)
            self._usingFreeAnalogCursorStateChangedEventHandle = nil
        end
        self:EnableGamepadInputShorts(false)
    end
    --END MODIFICATION

    self._wtLootingKeyMapping:HideAllKeys()
end

function LootingMainView_HD:OnClose()
    Facade.UIManager:ClearAllSubUI(self)
    InventoryNavManager.Reset()
end

local fChangeOtherHUDLayer = function(bShow)
    local hud = ABaseHUD.GetHUD(GetGameInstance())
    if not isvalid(hud) then
        return
    end

    ---@type HUDLayerController
    local hudLayerController = Facade.UIManager:GetLayerControllerByType(EUILayer.HUD)
    local uiIns = hudLayerController:GetHudByName("BuffAndStaminaViewPC")
    if bShow then
        if isvalid(uiIns) and isvalid(uiIns:GetCppInst()) then
            hudLayerController:OnMovePanelToLayer("BuffAndStaminaViewPC",EHUDBaseZorder.LargePopup)
        end
        hud:MovePanelToLayer("LocalPlayerInfo", LayerZorder.Zorder_LargePopup)
        hud:MovePanelToLayer("ScreenEffectMainHud", LayerZorder.Zorder_LargePopup)
    else
        hud:MovePanelToLayer("LocalPlayerInfo", LayerZorder.Zorder_Common)
        hud:MovePanelToLayer("ScreenEffectMainHud", LayerZorder.Zorder_ScreenEffect)
        if isvalid(uiIns) and isvalid(uiIns:GetCppInst()) then
            hudLayerController:OnMovePanelToLayer("BuffAndStaminaViewPC",EHUDBaseZorder.Common)
        end
    end
end

function LootingMainView_HD:_DelayChangeOtherHudLayer(param)
    local bShow = #param > 0 and param[1] or false
    fChangeOtherHUDLayer(bShow)
end

function LootingMainView_HD:_ChangeOtherHUDLayer(bShow)
    -- local bShouldDelay = true
    -- if bShouldDelay then
    --     Timer.DelayCall(0, fChangeOtherHUDLayer, nil, bShow)
    -- else
        fChangeOtherHUDLayer(bShow)
    -- end
end

function LootingMainView_HD:_AddEvents()
    self:AddLuaEvent(LootingConfig.Events.evtLootEachItemSearchStart, self._OnLootEachItemSearchStart, self)
    self:AddLuaEvent(LootingConfig.Events.evtLootEachItemSearchDone, self._OnLootEachItemSearchDone, self)
    -- self:AddLuaEvent(LootingConfig.Events.evtSwitch2BagView, self._OnSwitch2BagView, self)

    self:AddLuaEvent(CommonWidgetConfig.Events.evtGlobalItemDragStart, self._OnGlobalItemDragStart, self)
    self:AddLuaEvent(CommonWidgetConfig.Events.evtGlobalItemDragCancelled, self._OnGlobalItemDragCancelled, self)
    self:AddLuaEvent(CommonWidgetConfig.Events.evtGlobalItemDrop, self._OnGlobalItemDrop, self)

    --BEGIN MODIFICATION @ VIRTUOS : 
    self:AddLuaEvent(CommonWidgetConfig.Events.evtGlobalHoverItemViewUseAnim, self._OnGlobalHoverItemViewUseAnim, self)
    --END MODIFICATION

    self:AddLuaEvent(Server.ShopStationServer.Events.evtClientPreviewPlundering, self._OnClientPreviewPlundering, self)
    self:AddLuaEvent(Server.ShopStationServer.Events.evtShopStationItemMove, self._OnShopStationItemMove, self)
    self:AddLuaEvent(Server.LootingServer.Events.evtLootingItemMove, self._OnLootingItemMove, self)

    local priceWidget = self._wtLootBackpackPanel:GetPriceWidget()
    self:AddLuaEvent(priceWidget.evtOnShowDetailPanel, self._OnShowPriceDetailPanel, self)
    self:AddLuaEvent(LootingConfig.Events.evtContainerLootingPlayerChange, self._RefreshLootingTeammates, self)
    self:AddLuaEvent(LootingConfig.Events.evtHideBreathingBar, self._HideBreathingBar, self)

    if not self.closeActionHandle then
        log("bind HD_CLOSE_LOOT_PANEL_ACTION")
        self.closeActionHandle = self:AddInputActionBinding(LootingConfig.HD_CLOSE_LOOT_PANEL_ACTION, EInputEvent.IE_Pressed,
        self._OnCloseLootPanelAction, self, EDisplayInputActionPriority.UI_Stack)
    end

    Module.CommonWidget:BindCommonIVInputLogic()

    local hudLayerController = Facade.UIManager:GetHUDLayerController()
    if hudLayerController then
        hudLayerController.evtOnHUDStateChanged:AddListener(LootingMainView_HD._CheckShowOrHideBreathingBar, self)
    end
end

function LootingMainView_HD:_RemoveEvents()
    self:RemoveAllLuaEvent()

    if self.closeActionHandle then
        log("unbind HD_CLOSE_LOOT_PANEL_ACTION")
        self:RemoveInputActionBinding(self.closeActionHandle)
        self.closeActionHandle = nil
    end

    Module.CommonWidget:UnbindCommonIVInputLogic()

    local hudLayerController = Facade.UIManager:GetHUDLayerController()
    if hudLayerController then
        hudLayerController.evtOnHUDStateChanged:RemoveListener(LootingMainView_HD._CheckShowOrHideBreathingBar, self)
    end
end

function LootingMainView_HD:Update(delta)
    local gs = InGameController:Get():GetGameState()
    if gs then
        self._wtGameRemainTime:SetText(gs:GetLeftTimeStr())
    end
end

function LootingMainView_HD:_InitMainTabs()
    self._wtCaptureRoot = self:Wnd("wtCaptureRoot", UIWidgetBase)
    self._wtCharacterRoot = self:Wnd("wtCharacterRoot", UIWidgetBase)
    self._wtHealthRoot = self:Wnd("wtHealthRoot", SOLHealthMainView_HD)
    self._wtBagRoot = self:Wnd("wtBagRoot", UIWidgetBase)

    self._tabDataList = {}
    local mainTabNum = Module.Looting.Field:GetMainTabNum()
    for index = 1, mainTabNum do
        local tabTxt = LootingConfig.MainTabTxtList[index]
        local tabData = TabUIContentData:NewIns(index, tabTxt, true, "", true)
        table.insert(self._tabDataList, tabData)
    end

    self._wtMainTabs = UIUtil.WndTabGroupBox(self, "wtMainTabs", self._OnGetTopTabItemCount, self._OnProcessTopTabItemWidget, self._OnMainTabChanged)
    self._wtMainTabs:SetTabIndex(eTabIndex.Character, true)
    self:HideScoreHud()
end

function LootingMainView_HD:_OnMainTabChanged(index)
    if self._tabIndex ~= index then
        self._tabIndex = index
        self:_RefreshTab()

        --BEGIN MODIFICATION @ VIRTUOS : 在切换Tab后需要重新聚焦到可用对象
        if IsHD() then
            self:_ResetFocusOnTabChanged()
        end
        --END MODIFICATION
    end
end

function LootingMainView_HD:SetTab(tab)
    self._wtMainTabs:SetTabIndex(tab, true)
end

function LootingMainView_HD:_OnGetTopTabItemCount()
    return #self._tabDataList or 0
end

function LootingMainView_HD:_OnProcessTopTabItemWidget(position, topTabItem)
    local topTabData = self._tabDataList[position + 1]
    if not topTabData then
        return
    end
    local curSelectTabIdx = self._wtMainTabs:GetCurSelectedIndex()
    topTabData:UpdateTabByLuaTabUIContent(topTabItem, curSelectTabIdx == position)
end
-----------------------------------------------------------------------
--region Main Tab Logic

function LootingMainView_HD:_RefreshTab()
    if self._tabIndex == eTabIndex.Character then
        loginfo("[joeyinghe] LootingMainView_HD:_RefreshTab Character", self._tabIndex)
        self._wtCaptureRoot:SelfHitTestInvisible()
        self._wtCharacterRoot:SelfHitTestInvisible()
        self._wtHealthRoot:Collapsed()
        self._wtHealthRoot:OnCustomHide()
        self._wtBagRoot:SelfHitTestInvisible()
        if self._wtLootRoot then
            if self.pickupType == nil then
                self._wtLootRoot:Collapsed()
            else
                self._wtLootRoot:SelfHitTestInvisible()
            end
        end
        self:HideScoreHud()
    elseif self._tabIndex == eTabIndex.Health then
        loginfo("[joeyinghe] LootingMainView_HD:_RefreshTab Health", self._tabIndex)
        self._wtCaptureRoot:Collapsed()
        self._wtCharacterRoot:Collapsed()
        self._wtHealthRoot:SelfHitTestInvisible()
        self._wtHealthRoot:OnCustomShow()
        self._wtBagRoot:SelfHitTestInvisible()
        if self._wtLootRoot then
            if self.pickupType == nil then
                self._wtLootRoot:Collapsed()
            else
                self._wtLootRoot:SelfHitTestInvisible()
            end
        end
        self:HideScoreHud()
    elseif self._tabIndex == eTabIndex.Score then
        loginfo("[joeyinghe] LootingMainView_HD:_RefreshTab Score", self._tabIndex)
        self._wtCaptureRoot:SelfHitTestInvisible()
        self._wtCharacterRoot:Collapsed()
        self._wtHealthRoot:Collapsed()
        self._wtHealthRoot:OnCustomHide()
        self._wtBagRoot:Collapsed()
        if self._wtLootRoot then
            self._wtLootRoot:Collapsed()
        end
        self:ShowScoreHud()
    end
end

function LootingMainView_HD:_OnLootEachItemSearchStart(gid)
    local view = self:FindItemViewByGID(gid)
    if view then
        view:CheckSearchState()
    end
end

function LootingMainView_HD:_OnLootEachItemSearchDone(gid)
    local view = self:FindItemViewByGID(gid)
    if view then
        view:PlaySearchDoneAnim()
    end
end

function LootingMainView_HD:ShowScoreHud()
    local hud = ABaseHUD.GetHUD(GetWorld())
    if hud then
        local panel = hud:ShowPanel("ArenaScoreBoardHD")
    end
end

function LootingMainView_HD:HideScoreHud()
    local hud = ABaseHUD.GetHUD(GetWorld())
    if hud then
        local panel = hud:HidePanel("ArenaScoreBoardHD")
    end
end

--endregion
-----------------------------------------------------------------------

function LootingMainView_HD:_InitDragDropPanel()
    self._wtRightDiscardPanel = self:Wnd("wtRightDiscardPanel", CommonDragDropMask)
    self._wtRightDiscardPanel._shouldRecoveryVisibility = false
    self._wtRightDiscardBg = self:Wnd("wtRightDiscardBg", UIImage)
    self._wtDiscardIcon = self:Wnd("wtDiscardIcon_1", UIImage)

    local function fOnDragEnterRight()
        Module.CommonWidget:ForceEnableDragDropBehavior(false)
        self:_SetDiscardBg(eDiscardStyle.RightOn)
    end
    local function fOnDragLeave()
        Module.CommonWidget:ForceEnableDragDropBehavior(true)
        self:_SetDiscardBg(eDiscardStyle.Off)
    end
    local function fOnDrop(inGeometry, inDragDropEvent, operation)
        -- 记录道具操作来源
        ItemMoveCmd.RecordMoveOperation(ItemMoveCmd.EFromOperation.ItemViewDragSnap)

        local dragItemView = operation.DefaultDragVisual
        if not dragItemView then
            return
        end

        ---@type ItemBase
        local item = dragItemView.item
        if LootingLogic.DiscardOperation(item) then
            --Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIItemDrop)
        end
    end
    self._wtRightDiscardPanel:BindDragEnter(fOnDragEnterRight)
    self._wtRightDiscardPanel:BindDragLeave(fOnDragLeave)
    self._wtRightDiscardPanel:BindDrop(fOnDrop)

    self:_SetDiscardPanelVisible(false)
end

function LootingMainView_HD:_SetDiscardBg(style)
    if style == eDiscardStyle.Off then
        self._wtRightDiscardBg:AsyncSetImagePath(self.DISCARD_BG_DRAG_LEAVE)
        self._wtDiscardIcon:SetColorAndOpacity(Facade.ColorManager:GetLinerColorByRowName("C001"))
    elseif style == eDiscardStyle.LeftOn then
        self._wtRightDiscardBg:AsyncSetImagePath(self.DISCARD_BG_DRAG_LEAVE)
    elseif style == eDiscardStyle.RightOn then
        self._wtRightDiscardBg:AsyncSetImagePath(self.DISCARD_BG_DRAG_ENTER)
        self._wtDiscardIcon:SetColorAndOpacity(Facade.ColorManager:GetLinerColorByRowName("C004"))
    end
end

function LootingMainView_HD:_SetDiscardPanelVisible(bVisible)
    if bVisible then
        self._wtRightDiscardPanel:Visible()
    else
        self._wtRightDiscardPanel:Collapsed()
    end

    --BEGIN MODIFICATION @ VIRTUOS : 
    if IsHD() then
        self:UpdateDiscardBlankItemVisible(bVisible)
    end
	--END MODIFICATION
end

function LootingMainView_HD:FindItemViewByGID(gid)
    if self._bShowPlayerDeadbody then
        local deadbody = Server.LootingServer:GetCurrentInteractingBox()
        if deadbody and deadbody.CurrencyGID == gid then
            return self._wtLootPickupTitlePanel
        else
            return self._wtLootDeadbodyPanel:FindItemViewByGID(gid)
        end
    else
        return self._wtLootPickupSlotPanel:FindItemViewByGID(gid)
    end
end

---@param dragDropInfo ItemDragDropInfo
function LootingMainView_HD:_OnGlobalItemDragStart(dragDropInfo, operation)
    if not self:IsBagView() then
        Module.CommonWidget:RegisterAutoSnapArea(self._wtAutoSnapArea)
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default,self)
    end

    self:_SetDiscardPanelVisible(true)
    self:_SetDiscardBg(eDiscardStyle.Off)

    --BEGIN MODIFICATION @ VIRTUOS : 
    if IsHD() then
        local item = dragDropInfo.item
        if item then
            self:_EnableGamepadInputOnDrag(true)
            Module.CommonWidget:SetShouldTryFocusPostDrag(true)
        end
    end
    --END MODIFICATION
end

function LootingMainView_HD:_OnGlobalItemDragCancelled()
    if not self:IsBagView() then
        Module.CommonWidget:UnregisterAutoSnapArea(self._wtAutoSnapArea)
    end

    self:_SetDiscardPanelVisible(false)

    --BEGIN MODIFICATION @ VIRTUOS :
    if IsHD() then
        self:RestoreFocusItemOnDragCancel()
        Module.CommonWidget:SetShouldTryFocusPostDrag(false)
    end
    --END MODIFICATION
end

function LootingMainView_HD:_OnGlobalItemDrop()
    if not self:IsBagView() then
        Module.CommonWidget:UnregisterAutoSnapArea(self._wtAutoSnapArea)
    end

    self:_SetDiscardPanelVisible(false)

    --BEGIN MODIFICATION @ VIRTUOS : 
    if IsHD() then
        Module.CommonWidget:SetShouldTryFocusPostDrag(false)
    end
    --END MODIFICATION
end

function LootingMainView_HD:_OnClientPreviewPlundering()
    self._wtLootPickupTitlePanel:InitPanel(self.pickupType, self.interactors)

    self:PlayWidgetAnim(self.WBP_LootingPickupView_PC_Search_Move_Done)
end

function LootingMainView_HD:_OnShopStationItemMove(incrementWeight)
    local currentWeight = Server.InventoryServer:GetAllCarryItemsWeight(ESlotGroup.Player)
    local littleWeight, middleWeight, overWeight = Server.HeroServer:GetRoleLoad()
    -- self._wtWeightBtn:_PlayFlashAni(currentWeight, currentWeight + incrementWeight, overWeight)
end

---@param itemMoveInfo itemMoveInfo
function LootingMainView_HD:_OnLootingItemMove(itemMoveInfo)
    -- local item = 
end

function LootingMainView_HD:_OnCloseLootPanelAction()
    --BEGIN MODIFICATION @ VIRTUOS : 
    if Module.CommonWidget:IsInSwapingItem() then
        -- 处于交换物品状态时，返回操作用于退出交换物品。
        Module.CommonWidget:SetGamepadSwapItem(false)
    elseif self._wtHealthRoot._bIsOpenHealthListView == true then
        -- 关闭健康页面的药品弹窗
        Module.SOLHealthSystem.Field.bMouseEnterListView = false
        self._wtHealthRoot:CloseHealthItemListView()
    elseif self:_OnHoverInUsingItemByGamepad() == true then
        -- 取消使用物品
        -- yanmingjing: she3取消使用物品改为使用A键，因此注掉这里
        -- Module.Looting:DoCancelUseItem()
    else
        -- 其他情况用于退出页面
        Module.Looting:StopAllLoots()
    end
    --END MODIFICATION
end

function LootingMainView_HD:_OnShowPriceDetailPanel(bShow)
    if bShow then
        self._wtPriceDetailPanel:SelfHitTestInvisible()
        self._wtPriceDetailPanel:RefreshView()
    else
        self._wtPriceDetailPanel:Collapsed()
    end
end

function LootingMainView_HD:GetBagPanel()
    return self._wtLootBackpackPanel
end

function LootingMainView_HD:_OnFashionBtnCheckStateChanged(bIsChecked)
    Module.LobbyDisplay:SetCharacterDisplayEquipment(bIsChecked)
    self._wtCaptureImg:RefreshCharacterAppearance()
end

function LootingMainView_HD:_RefreshLootingTeammates()
    self._wtTeammateSigns:Collapsed()
    self._wtTeammate1Sign:Collapsed()
    self._wtTeammate2Sign:Collapsed()
    local lootingTeammates = Module.Looting:GetLootingTeammates()
    if isinvalid(lootingTeammates) then
        return
    end
    local currentNo = 0
    for index = 1,3 do
        if lootingTeammates[index] then
            self._wtTeammateSigns:SelfHitTestInvisible()
            currentNo = currentNo + 1
            if currentNo == 1 then
                self._wtTeammate1Sign:SelfHitTestInvisible()
                self._wtTeammate1No:SetText(index)
                self._wtTeammate1Bg:SetColorAndOpacity(Facade.ColorManager:GetLinerColor(LootingConfig.TeammateColors[index]))
            elseif currentNo == 2 then
                self._wtTeammate2Sign:SelfHitTestInvisible()
                self._wtTeammate2No:SetText(index)
                self._wtTeammate2Bg:SetColorAndOpacity(Facade.ColorManager:GetLinerColor(LootingConfig.TeammateColors[index]))
            end
        end
    end
end

--BEGIN MODIFICATION @ VIRTUOS : UI Navigation
function LootingMainView_HD:_RegisterNavGroup()
    if not IsHD() then
        return
    end

    self._NavGroupsMap = {}
    self._CurFocusNavGroupID = 0

    -- 角色信息
    if not self._NavGroupHeroInfo then
        self._NavGroupHeroInfo = WidgetUtil.RegisterNavigationGroup(self._wtCharacterRoot, self, "Hittest")
        if self._NavGroupHeroInfo then
            self._NavGroupHeroInfo:AddNavWidgetToArray(self._wtWeightBtn)
            self._NavGroupHeroInfo:AddNavWidgetToArray(self._wtFashionBtn)
            self._NavGroupHeroInfo:SetAutoUseRootGeometrySize(false)
            self._NavGroupHeroInfo:SetNavSelectorWidgetVisibility(true)
    
            local HeroInfoNavStrategy = self._NavGroupHeroInfo:GetOwnerNavStrategy()
            if HeroInfoNavStrategy then
                HeroInfoNavStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.HittestOrDistance)
            end
        end
    end

    -- 角色装备
    local _wtLootEquipCanvasPanel = self._wtLootEquipPanel:Wnd("DFCanvasPanel_135", UIWidgetBase)
    local _wtBackpackPriceWidget = self._wtLootBackpackPanel:Wnd("wtPriceWidget", UIWidgetBase)
    if not self._NavGroup then
        self._NavGroup = WidgetUtil.RegisterNavigationGroup(_wtLootEquipCanvasPanel, self, "Hittest")
        self._NavGroup.OnNavGroupFocusReceivedEvent:Add(self._OnPlayerEquipNavGroupFocusReceived, self)
        InventoryNavManager.equipNavGroup = self._NavGroup
        if self._NavGroup then
            self._NavGroup:AddNavWidgetToArray(_wtLootEquipCanvasPanel)
            self._NavGroup:SetNavSelectorWidgetVisibility(true)
    
            table.insert(self._NavGroupsMap, {NavGroup = self._NavGroup, RootWidget = _wtLootEquipCanvasPanel})
    
            local LootEquipNavStrategy = self._NavGroup:GetOwnerNavStrategy()
            if LootEquipNavStrategy then
                LootEquipNavStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.HittestOrDistance)
            end
        end
    end

    -- 局内收益
    -- local _wtBackpackPriceWidget = self._wtLootBackpackPanel:Wnd("wtPriceWidget", UIWidgetBase)
    -- if _wtBackpackPriceWidget and self._NavGroup then
    --     if self._NavGroup then
            
    --     end
    -- end

    -- 背包
    local _wtLootBackPackScrollBox = self._wtLootBackpackPanel:Wnd("wtMainScrollBox", UIWidgetBase)
    local _wtSafeBoxContainer = self._wtLootBackpackPanel:Wnd("wtSafeBoxContainerView", UIWidgetBase)
    if not self._NavGroupBackPack then
        self._NavGroupBackPack = WidgetUtil.RegisterNavigationGroup(self._wtLootBackpackPanel, self, "Hittest")
        self._NavGroupBackPack.OnNavGroupFocusReceivedEvent:Add(self._OnEquipBoxNavGroupFocusReceived, self)
        if self._NavGroupBackPack then
            self._NavGroupBackPack:AddNavWidgetToArray(_wtLootBackPackScrollBox)
            self._NavGroupBackPack:SetScrollRecipient(_wtLootBackPackScrollBox)
            self._NavGroupBackPack:AddNavWidgetToArray(_wtSafeBoxContainer)
            self._NavGroupBackPack:AddNavWidgetToArray(_wtBackpackPriceWidget)
            self._NavGroupBackPack:SetNavSelectorWidgetVisibility(true)
            
            local BackPackNavStrategy = self._NavGroupBackPack:GetOwnerNavStrategy()
            if BackPackNavStrategy then
                BackPackNavStrategy:SetHitPadding(2.0)
                BackPackNavStrategy:SetHittestFallbackConfig(EGPUINavHittestFallbackType.Horizental, false, false)
                BackPackNavStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.HittestOrDistance)
            end
    
            -- WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroupBackPack)
    
            table.insert(self._NavGroupsMap, {NavGroup = self._NavGroupBackPack, RootWidget = self._wtLootBackpackPanel})
        end
    end
    InventoryNavManager.backpackNavGroup = self._NavGroupBackPack
    -- if not self._NavGroupSafeBox then
    --     -- 安全箱
    --     local _wtSafeBoxContainer = self._wtLootBackpackPanel:Wnd("wtSafeBoxContainerView", UIWidgetBase)
    --     self._NavGroupSafeBox = WidgetUtil.RegisterNavigationGroup(_wtSafeBoxContainer, self, "Hittest")
    
    --     if self._NavGroupSafeBox then
    --         self._NavGroupSafeBox:AddNavWidgetToArray(_wtSafeBoxContainer)
    --         self._NavGroupSafeBox:SetNavSelectorWidgetVisibility(true)
    --         local SafeBoxNavStrategy = self._NavGroupSafeBox:GetOwnerNavStrategy()
    --         if SafeBoxNavStrategy then
    --             SafeBoxNavStrategy:SetHitPadding(2.0)
    --             SafeBoxNavStrategy:SetHittestFallbackConfig(EGPUINavHittestFallbackType.Horizental, false, false)
    --             SafeBoxNavStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.HittestOrDistance)
    --         end
    --     end
    -- end

    -- 战利品
    local _wtDeadbodyScrollBox = self._wtLootDeadbodyPanel:Wnd("wtMainScrollBox", UIWidgetBase)
    if not self._NavGroupDeadbody then
        self._NavGroupDeadbody = WidgetUtil.RegisterNavigationGroup(_wtDeadbodyScrollBox, self, "Hittest")
        self._NavGroupDeadbody.OnNavGroupFocusReceivedEvent:Add(self._OnNavGroupLitterFocusReceived, self)
        if self._NavGroupDeadbody then
            self._NavGroupDeadbody:AddNavWidgetToArray(_wtDeadbodyScrollBox)
            self._NavGroupDeadbody:SetScrollRecipient(_wtDeadbodyScrollBox)
            self._NavGroupDeadbody:SetNavSelectorWidgetVisibility(true)
            local DeadbodyNavStrategy = self._NavGroupDeadbody:GetOwnerNavStrategy()
            if DeadbodyNavStrategy then
                DeadbodyNavStrategy:SetHitPadding(2.0)
                DeadbodyNavStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.HittestOrDistance)
            end
    
            table.insert(self._NavGroupsMap, {NavGroup = self._NavGroupDeadbody, RootWidget = _wtDeadbodyScrollBox})
        end
    end
    InventoryNavManager.deadBodyNavGroup = self._NavGroupDeadbody
   
    -- litter
    local _wtLitterScrollBox = self._wtLootingLitter:Wnd("DFScrollBox_0", UIWidgetBase)
    if not self._NavGroupLitter then
        self._NavGroupLitter = WidgetUtil.RegisterNavigationGroup(_wtLitterScrollBox, self, "Hittest")
        self._NavGroupLitter.OnNavGroupFocusReceivedEvent:Add(self._OnNavGroupLitterFocusReceived, self)
        if self._NavGroupLitter then
            self._NavGroupLitter:AddNavWidgetToArray(_wtLitterScrollBox)
            self._NavGroupLitter:SetScrollRecipient(_wtLitterScrollBox)
            self._NavGroupLitter:SetNavSelectorWidgetVisibility(true)
            local LitterNavStrategy = self._NavGroupLitter:GetOwnerNavStrategy()
            if LitterNavStrategy then
                LitterNavStrategy:SetHitPadding(2.0)
                LitterNavStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.HittestOrDistance)
            end
    
            table.insert(self._NavGroupsMap, {NavGroup = self._NavGroupLitter, RootWidget = _wtLitterScrollBox})
        end
    end
    InventoryNavManager.litterNavGroup = self._NavGroupLitter

    -- Discard
    if self._wtDiscardBlankItem then
        if not self._NavGroupDiscard then
            self._NavGroupDiscard = WidgetUtil.RegisterNavigationGroup(self._wtDiscardBlankItem, self, "Hittest")
            if self._NavGroupDiscard then
                InventoryNavManager.discardNavGroup = self._NavGroupDiscard
                self._NavGroupDiscard:AddNavWidgetToArray(self._wtDiscardBlankItem)
                self._NavGroupDiscard:SetAutoUseRootGeometrySize(false)
                table.insert(self._NavGroupsMap, {NavGroup = self._NavGroupDiscard, RootWidget = self._wtDiscardBlankItem})
    
                local DiscardGroupNavStrategy = self._NavGroupDiscard:GetOwnerNavStrategy()
                if DiscardGroupNavStrategy then
                    DiscardGroupNavStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.HittestOrDistance)
                end
            end
        end
    end

    --Helath 
    if not self._wtNavGroupseItemView then
        self._wtNavGroupseItemView = WidgetUtil.RegisterNavigationGroup(self._wtHealthRoot._wtDrugItemView1, self, "Hittest")
        if self._wtNavGroupseItemView then
            self._wtNavGroupseItemView:AddNavWidgetToArray(self._wtHealthRoot._wtDrugItemView1)
        end
    end
    if not self._wtNavGroupseItemView then
        self._wtNavGroupseItemView2 = WidgetUtil.RegisterNavigationGroup(self._wtHealthRoot._wtDrugItemView2, self, "Hittest")
        if self._wtNavGroupseItemView2 then
            self._wtNavGroupseItemView2:AddNavWidgetToArray(self._wtHealthRoot._wtDrugItemView2)
        end
    end
    if not self._wtNavGroupseItemView then
        self._wtNavGroupseItemView3 = WidgetUtil.RegisterNavigationGroup(self._wtHealthRoot._wtDrugItemView3, self, "Hittest")
        if self._wtNavGroupseItemView3 then
            self._wtNavGroupseItemView3:AddNavWidgetToArray(self._wtHealthRoot._wtDrugItemView3)
        end
    end
    if not self._wtNavGroupseItemView then
        self._wtNavGroupseItemView4 = WidgetUtil.RegisterNavigationGroup(self._wtHealthRoot._wtDrugItemView4, self, "Hittest")
        if self._wtNavGroupseItemView4 then
            self._wtNavGroupseItemView4:AddNavWidgetToArray(self._wtHealthRoot._wtDrugItemView4)
        end
    end
end

function LootingMainView_HD:_RemoveNavGroup()
    if not IsHD() then
        return
    end

    if self._NavGroupHeroInfo then
        self._NavGroupHeroInfo = nil
    end

    if self._NavGroup then
        self._NavGroup = nil
    end

    if self._NavGroupBackPack then
        self._NavGroupBackPack = nil
    end

    -- if self._NavGroupSafeBox then
    --     self._NavGroupSafeBox = nil
    -- end

    if self._NavGroupDeadbody then
        self._NavGroupDeadbody = nil
    end

    if self._NavGroupLitter then
        self._NavGroupLitter = nil
    end

    if self._NavGroupDiscard then
        self._NavGroupDiscard = nil
    end

    if self._wtNavGroupseItemView then
        self._wtNavGroupseItemView = nil
    end
    if self._wtNavGroupseItemView2 then
        self._wtNavGroupseItemView2 = nil
    end
    if self._wtNavGroupseItemView3 then
        self._wtNavGroupseItemView3 = nil
    end
    if self._wtNavGroupseItemView4 then
        self._wtNavGroupseItemView4 = nil
    end

    WidgetUtil.RemoveNavigationGroup(self)
end

function LootingMainView_HD:_ResetFocusOnTabChanged()
    if not IsHD() then
        return
    end
    
    if self._NavGroupBackPack then
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroupBackPack)
    end
end

-- 绑定输入类型切换事件
function LootingMainView_HD:_EnableInputTypeChangedHandle(bEnable)
    if not IsHD() then
        return 
    end

    if bEnable then
        if not self._OnNotifyInputTypeChangedHandle then
            self._OnNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._HandleInputTypeChanged, self))
        end
    else
        if self._OnNotifyInputTypeChangedHandle then
            UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._OnNotifyInputTypeChangedHandle)
            self._OnNotifyInputTypeChangedHandle = nil
        end
    end
end

function LootingMainView_HD:_HandleInputTypeChanged(inputType)
    if not IsHD() then
        return 
    end

    -- 从手柄切到键鼠
    if not (inputType == EGPInputType.Gamepad) then
        -- 退出手柄移动物品状态
        if Module.CommonWidget:IsInSwapingItem() then
            Module.CommonWidget:SetGamepadSwapItem(false)
        end

        -- 关闭手柄打开的HoverTips
        self:CloseHoverTips()
        self:EnableForceUIOnlyByGamepad(false)
    else
        self:EnableForceUIOnlyByGamepad(true)
    end
end

function LootingMainView_HD:EnableGamepadInputShorts(bEnable)
   
    if bEnable == true then
        -- 切换聚焦区域
        if self._InputHandle_MoveFocusAreaL == nil then
            self._InputHandle_MoveFocusAreaL = self:AddInputActionBinding("Warehouse_MoveFocusArea_L", EInputEvent.IE_Pressed,
            self.MoveFocusAreaToLeft, self, EDisplayInputActionPriority.UI_Stack)
        end

        if self._InputHandle_MoveFocusAreaR == nil then
            self._InputHandle_MoveFocusAreaR = self:AddInputActionBinding("Warehouse_MoveFocusArea_R", EInputEvent.IE_Pressed,
            self.MoveFocusAreaToRight, self, EDisplayInputActionPriority.UI_Stack)
        end
        
        -- if self._InputHandle_ShowHoverTips == nil then
        --     self._InputHandle_ShowHoverTips = self:AddInputActionBinding("Common_ToggleTip", EInputEvent.IE_Pressed,
        --     self.ShowHoverTips, self, EDisplayInputActionPriority.UI_Stack)
        -- end

    else
        if self._InputHandle_MoveFocusAreaL then
            self:RemoveInputActionBinding(self._InputHandle_MoveFocusAreaL)
            self._InputHandle_MoveFocusAreaL = nil
        end

        if self._InputHandle_MoveFocusAreaR then
            self:RemoveInputActionBinding(self._InputHandle_MoveFocusAreaR)
            self._InputHandle_MoveFocusAreaR = nil
        end

        -- if self._InputHandle_ShowHoverTips then
        --     self:RemoveInputActionBinding(self._InputHandle_ShowHoverTips)
        --     self._InputHandle_ShowHoverTips = nil
        -- end
    end
end

-- 手柄快速切换聚焦区域
function LootingMainView_HD:MoveFocusAreaToLeft()
    self:MoveFocusAreaByIndex(true)
end

function LootingMainView_HD:MoveFocusAreaToRight()
    self:MoveFocusAreaByIndex(false)
end

function LootingMainView_HD:MoveFocusAreaByIndex(bIsLeft)
    if not IsHD() then
        return 
    end

    if self._tabIndex == eTabIndex.Score then
        return
    end

    -- 有pop弹窗时不可用
    if Module.CommonWidget:GetOpenedPopItemView() ~= nil or Module.ItemDetail:GetIsSplitPanelOpened() == true then
        return
    end

    -- 使用虚拟光标时不可用 
    if WidgetUtil.IsUsingFreeAnalogCursor() == true then
        return
    end
    
    local enableNavGroupMap = {}

    local curSelectNavGroupIndex = 0
    local bCurrentFocusInMainNavGroup = false
    for index, value in ipairs(self._NavGroupsMap) do
        local navGroup = value.NavGroup
        if navGroup and navGroup:IsGroupEnabled() == true then

            table.insert(enableNavGroupMap, value)

            local NavGroupRootWidget = value.RootWidget
            if NavGroupRootWidget then
                if UGPUINavigationUtils.IsWidgetInFocusPath(0, NavGroupRootWidget) == true then
                    curSelectNavGroupIndex = #enableNavGroupMap - 1
                    bCurrentFocusInMainNavGroup = true
                end
            end
        end

    end

    local nextNavStep = 0
    if bCurrentFocusInMainNavGroup == true then
        if bIsLeft == true then
            nextNavStep = -1
        else
            nextNavStep = 1
        end
    end
    
    local nextTargetGroupIndex = (curSelectNavGroupIndex + nextNavStep + #enableNavGroupMap) % #enableNavGroupMap
    nextTargetGroupIndex = nextTargetGroupIndex + 1

    local nextNavGroupMap = enableNavGroupMap[nextTargetGroupIndex]
    if nextNavGroupMap and nextNavGroupMap.NavGroup then
        -- WidgetUtil.TryFocusDefaultWidgetByGroup(nextNavGroupMap.NavGroup)
        InventoryNavManager.TryFocusAfterSwitchTab(nextNavGroupMap.NavGroup)
    end
end

function LootingMainView_HD:InitWHContainerViewConfig()
    if not IsHD() then
        return 
    end

    -- 记录页面中的WarehouseContainerView
    local WHContainerViewConfig = {
        "wtCHContainerView",
        "wtPocketContainerView",
        "wtBagContainerView",
        "wtKeyContainerView",
        "wtSafeBoxContainerView"
    }

    self._wtWHContainerViews = {}
    self._wtHoverBtnArray = {}

    for index, WHContainerViewName in ipairs(WHContainerViewConfig) do
        local curWHContainerView = self._wtLootBackpackPanel:Wnd(WHContainerViewName, UIWidgetBase)
        if curWHContainerView then
            table.insert(self._wtWHContainerViews, curWHContainerView)
            
            local wtHoverBtn = curWHContainerView:Wnd("wtHoverBtn", UIWidgetBase)
            if wtHoverBtn then
                table.insert(self._wtHoverBtnArray, wtHoverBtn)
            end
        end
    end

    self:DisabelHoverTipsFocus()
end

-- 关闭页面中 “问号” 提示的可聚焦
function LootingMainView_HD:DisabelHoverTipsFocus()
    if not IsHD() then
        return 
    end

    for key, value in pairs(self._wtHoverBtnArray) do
        local checkBtn = value:Wnd("DFCheckBox_Icon", UICheckBox)
        if checkBtn then
            checkBtn:SetCppValue("IsFocusable", false)
        end
    end
end

function LootingMainView_HD:ShowHoverTips()
    if not IsHD() then
        return 
    end

    if self._tabIndex == eTabIndex.Score then
        return
    end

    if Module.CommonWidget:IsInSwapingItem() == true then
        return
    end

    local _wtLootBackPackScrollBox = self._wtLootBackpackPanel:Wnd("wtMainScrollBox", UIWidgetBase)
    
    -- 通过按键逐个显示HoverTips
    if self._curShowTipsIndex < #self._wtWHContainerViews then
        local lastHoveTip = self._wtWHContainerViews[self._curShowTipsIndex]
        if lastHoveTip and lastHoveTip._OnUnhovered then
            lastHoveTip:_OnUnhovered()
        end

        self._curShowTipsIndex = self._curShowTipsIndex + 1
        local hoverTip = self._wtWHContainerViews[self._curShowTipsIndex]
        if hoverTip and hoverTip.OnHoverTipByGamepad then
            hoverTip:OnHoverTipByGamepad()

            if _wtLootBackPackScrollBox then
                _wtLootBackPackScrollBox:ScrollWidgetIntoView(hoverTip, true, EDescendantScrollDestination.IntoView)
            end
        end

    elseif self._curShowTipsIndex == #self._wtWHContainerViews then
        self:CloseHoverTips()
    end
end

function LootingMainView_HD:CloseHoverTips()
    if self._curShowTipsIndex ~= 0 then
        local hoverTip = self._wtWHContainerViews[self._curShowTipsIndex]
        if hoverTip and hoverTip._OnUnhovered then
            hoverTip:_OnUnhovered()
        end
        self._curShowTipsIndex = 0
    end
end

-- 绑定手柄模拟鼠标左键拖拽事件
function LootingMainView_HD:_EnableSimulatedMouseDragHandle(bEnable)
    if not IsHD() then
        return 
    end

    if bEnable then
        if not self._OnNotifySimulatedMouseDragHandle then
            self._OnNotifySimulatedMouseDragHandle = WidgetUtil.BindSimulatedMouseDragEvent(self._OnGlobalSwapItem, self)
        end
    else
        if self._OnNotifySimulatedMouseDragHandle then
            WidgetUtil.UnBindSimulatedMouseDragEvent(self._OnNotifySimulatedMouseDragHandle)
            self._OnNotifySimulatedMouseDragHandle = nil
        end
    end
end

function LootingMainView_HD:_OnGlobalSwapItem(bIsSwapItem)
    if IsHD() then
        UDFNavigationSelectorBase.SetForceHideSelectorRoot(bIsSwapItem)
        if self._ToggleDragItemSpinHandle == nil then
            self._ToggleDragItemSpinHandle = self:AddInputActionBinding("SimulateDropItem_Gamepad", EInputEvent.IE_Pressed, WidgetUtil.StopSimulateMouseDrag, self, EDisplayInputActionPriority.UI_Stack)
        end

        if bIsSwapItem == true then
            Module.CommonWidget.UnBindHoverViewDisplayInput()

            -- 在手柄拖拽时不希望导航到这些按钮
            if WidgetUtil.IsGamepad() then
                self:_SetEnableHeroInfoBtns(false)
            end
        else
            Module.CommonWidget.BindHoverViewDisplayInput()

            self:_EnableGamepadInputOnDrag(false)

            self:_SetEnableHeroInfoBtns(true)
        end 
    end
end

function LootingMainView_HD:_EnableGamepadInputOnDrag(bEnable)
    if not IsHD() then
        return
    end

    if bEnable then
        if self._ToggleDragItemSpinHandle == nil then
            self._ToggleDragItemSpinHandle = self:AddInputActionBinding("RotateItemOnDrag_Gamepad", EInputEvent.IE_Pressed, 
            self.ToggleDragItemSpinByGamepad, self, EDisplayInputActionPriority.UI_Stack)
        end
    else
        if self._ToggleDragItemSpinHandle then
            self:RemoveInputActionBinding(self._ToggleDragItemSpinHandle)
            self._ToggleDragItemSpinHandle = nil
        end
    end
end

function LootingMainView_HD:ToggleDragItemSpinByGamepad()
    if not IsHD() then
        return 
    end

    if Module.CommonWidget:IsInSwapingItem() == true then
        Module.CommonWidget:ToggleDragItemSpin()
    end
end

function LootingMainView_HD:EnableForceUIOnlyByGamepad(bEnable)
    if not IsHD() then
        return 
    end

    if not self._cname then
        return
    end

    if WidgetUtil.IsGamepad() and bEnable then
        -- UGPInputHelper.WantInputMode_ForceUIOnly(GetGameInstance(), self._cname, true, false)
        UGPInputHelper.WantInputMode_UIOnly(GetGameInstance(), self._cname, true, false)
    else
        -- UGPInputHelper.WantInputMode_ForceUIOnly(GetGameInstance(), self._cname, false, false)
        UGPInputHelper.WantInputMode_UIOnly(GetGameInstance(), self._cname, false, false)
    end
end

function LootingMainView_HD:CreateDiscardFocusItem()
    if not IsHD() then
        return 
    end

    -- 在DiscardPanel下创建一个透明的控件来辅助导航
    -- 满足手柄模拟拖拽时能移动到最右侧来丢弃物品的功能
    local wtDiscardPanel = self:Wnd("wtDiscardPanel", UIWidgetBase)
    local uiIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.IVCommonBlankItemTemplate, wtDiscardPanel)

    if uiIns and self._wtDiscardBlankItem == nil then
        local tempUIIns = getfromweak(uiIns)
        
        if tempUIIns then
            self._wtDiscardBlankItem = tempUIIns
            self._wtDiscardBlankItem:SetVisibility(ESlateVisibility.Collapsed)
            self._wtDiscardBlankItem:SetRenderOpacity(0)

            
            local wtDiscardIcon_1 = self:Wnd("wtDiscardIcon_1", UIImage)
            if wtDiscardIcon_1 then
                local discardIconAnchor = wtDiscardIcon_1.Slot:GetAnchors()
                local discardIconPos = wtDiscardIcon_1.Slot:GetPosition()
                local discardIconSize = wtDiscardIcon_1.Slot:GetSize()
                local discardIconAlignment = wtDiscardIcon_1.Slot:GetAlignment()

                self._wtDiscardBlankItem.Slot:SetAnchors(discardIconAnchor)
                self._wtDiscardBlankItem.Slot:SetPosition(discardIconPos)
                self._wtDiscardBlankItem.Slot:SetSize(FVector2D(discardIconSize.X, discardIconSize.Y))
                self._wtDiscardBlankItem.Slot:SetAlignment(FVector2D(discardIconAlignment.X, discardIconAlignment.Y))
            end
        end
    end
end

function LootingMainView_HD:UpdateDiscardBlankItemVisible(bVisible)
    if not IsHD() then
        return 
    end

    if not self._wtDiscardBlankItem then
        return
    end

    if WidgetUtil.IsGamepad() and bVisible == true then
        self._wtDiscardBlankItem:SetVisibility(ESlateVisibility.Visible)
    else
        self._wtDiscardBlankItem:SetVisibility(ESlateVisibility.Collapsed)
    end
    
end

function LootingMainView_HD:RestoreFocusItemOnDragCancel()
    if not IsHD() then
        return 
    end

    if not WidgetUtil.IsGamepad() then
        return
    end

    -- 在Discard位置取消，保底恢复位置
    if self._wtDiscardBlankItem and UGPUINavigationUtils.IsWidgetInFocusPath(0, self._wtDiscardBlankItem) == true then
        if self._NavGroupBackPack then
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroupBackPack)
        end
    end
end

function LootingMainView_HD:_OnGlobalHoverItemViewUseAnim(bHover, item)
    if not IsHD() then
        return 
    end

    self.bHoverItemViewUseAnimByGamepad = bHover

    if bHover == true then
        self.ItemInUsingAnim = item
    else
        self.ItemInUsingAnim = nil
    end

end

function LootingMainView_HD:_OnHoverInUsingItemByGamepad()
    if not IsHD() then
        return false
    end

    if not WidgetUtil.IsGamepad() then
        return false
    end

    if self.bHoverItemViewUseAnimByGamepad == true then
        if self.ItemInUsingAnim and self.ItemInUsingAnim.IsInUsing then
            return self.ItemInUsingAnim:IsInUsing()
        end
    end

    return false
end

function LootingMainView_HD:_SetEnableHeroInfoBtns(bEnable)
    if not IsHD() then
        return
    end

    if self._wtWeightBtn then
        self._wtWeightBtn:SetIsEnabled(bEnable)
    end

    if self._wtFashionBtn then
        self._wtFashionBtn:SetIsEnabled(bEnable)
    end
end
--END MODIFICATION

function LootingMainView_HD:_ShowBreathingBar()
    if not self._breathingBarInsId then
        local _, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.LootingBreathingBar, self._wtBreathingBarSlot, self._breathingBarInsId)
        self._breathingBarInsId = instanceID
    end
end

function LootingMainView_HD:_HideBreathingBar()
    if self._breathingBarInsId then
        Facade.UIManager:RemoveSubUI(self, UIName2ID.LootingBreathingBar, self._breathingBarInsId)
        self._breathingBarInsId = nil
    end
end

function LootingMainView_HD:_CheckShowOrHideBreathingBar(hudState)
    if hudState then
        if hudState:HasState(GameHUDState.GHS_SwimUnderWater) then
            self:_ShowBreathingBar()
        elseif Module.Looting.Field.bHasEverSwim then
            local curSwimBarPercent = LootingLogic.GetCurSwimOxygenPercent()
            if self._breathingBarInsId then
                if curSwimBarPercent >= 1 then
                    self:_HideBreathingBar()
                end
            else
                if curSwimBarPercent < 1 then
                    self:_ShowBreathingBar()
                end
            end
        end
    end
end

function LootingMainView_HD:_DisableDynamicNavConfig()
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
end

function LootingMainView_HD:_EnableShowTips()
    -- 当位于第二区时，才会显示Tips
    self._EnableTipsInEquipBox = true
end

function LootingMainView_HD:_DisableShowTips()
    self._EnableTipsInEquipBox = false
end

-- LT/RT换区提示
function LootingMainView_HD:_DisableShoulderTips()
    self:ShowLeftShoulderTips(false)
    self:ShowMidShoulderTips(false)
    self:ShowRightShoulderTips(false)

end

function LootingMainView_HD:ShowLeftShoulderTips(bShouldShow)
    if bShouldShow == true  and not WidgetUtil.IsUsingFreeAnalogCursor() then
        self._wtLeftShoulderKeyLeft:SelfHitTestInvisible()
        self._wtLeftShoulderKeyLeft:SetOnlyDisplayOnGamepad(true)
        
        self._wtRightShoulderKeyLeft:SelfHitTestInvisible()
        self._wtRightShoulderKeyLeft:SetOnlyDisplayOnGamepad(true)
        
    else
        self._wtLeftShoulderKeyLeft:Collapsed()
        self._wtRightShoulderKeyLeft:Collapsed()
    end
end

function LootingMainView_HD:ShowMidShoulderTips(bShouldShow)
    if bShouldShow == true  and not WidgetUtil.IsUsingFreeAnalogCursor() then
        self._wtLeftShoulderKeyMid:SelfHitTestInvisible()
        self._wtLeftShoulderKeyMid:SetOnlyDisplayOnGamepad(true)
        
        self._wtRightShoulderKeyMid:SelfHitTestInvisible()
        self._wtRightShoulderKeyMid:SetOnlyDisplayOnGamepad(true)
        
    else
        self._wtLeftShoulderKeyMid:Collapsed()
        self._wtRightShoulderKeyMid:Collapsed()
    end
end

function LootingMainView_HD:ShowRightShoulderTips(bShouldShow)
    if bShouldShow == true  and not WidgetUtil.IsUsingFreeAnalogCursor() then
        self._wtLeftShoulderKeyRight:SelfHitTestInvisible()
        self._wtLeftShoulderKeyRight:SetOnlyDisplayOnGamepad(true)
        
        self._wtRightShoulderKeyRight:SelfHitTestInvisible()
        self._wtRightShoulderKeyRight:SetOnlyDisplayOnGamepad(true)
        
    else
        self._wtLeftShoulderKeyRight:Collapsed()
        self._wtRightShoulderKeyRight:Collapsed()
    end
end


function  LootingMainView_HD:_OnPlayerEquipNavGroupFocusReceived()
    self:_DisableDynamicNavConfig()
    self:_DisableShoulderTips()
    self:ShowLeftShoulderTips(true)
    WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default,self)
    self:_DisableShowTips()
end

-- BEGIN MODIFICATION @yanmingjing
function  LootingMainView_HD:_OnEquipBoxNavGroupFocusReceived()
    self:_DisableDynamicNavConfig()
    self:_DisableShoulderTips()
    self:ShowMidShoulderTips(true)
    -- NoA导航影响拖拽放下，不好通过调用OnClicked模拟
    if Module.CommonWidget:GetIsDragging() then
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default,self)
    else
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction,self)
    end
    self:_EnableShowTips()
end


function  LootingMainView_HD:_OnNavGroupLitterFocusReceived()
    self:_DisableDynamicNavConfig()
    self:_DisableShoulderTips()
    self:ShowRightShoulderTips(true)
    -- NoA导航影响拖拽放下，不好通过调用OnClicked模拟
    if Module.CommonWidget:GetIsDragging() then
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default,self)
    else
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction,self)
    end
    self:_DisableShowTips()
end

function  LootingMainView_HD:_OnUsingFreeAnalogCursorStateChanged(bNewState)
    if bNewState == true then
        self:_DisableShoulderTips()
    end
end


-- END MODIFICATION

return LootingMainView_HD