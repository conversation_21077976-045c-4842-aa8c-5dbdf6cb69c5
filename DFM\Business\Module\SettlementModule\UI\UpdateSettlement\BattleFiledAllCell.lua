----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSettlement)
----- LOG FUNCTION AUTO GENERATE END -----------


---@class BattleFiledAllCell
local BattleFiledAllCell = ui("BattleFiledAllCell")
local UWidgetLayoutLibrary = import "WidgetLayoutLibrary"
local EOutStandingContributionType = import "EOutStandingContributionType"
local UBreakthroughUIConfig = import "BreakthroughUIConfig"
local EBreakthroughUIIconType = import "EBreakthroughUIIconType"
local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"

local function log(...)
    loginfo("xxww", "[BattleFiledAllCell]", ...)
end

function BattleFiledAllCell:Ctor()
    self._wtPlayerName    = self:Wnd("TextBlock_307", UITextBlock) --
    self._wtK             = self:Wnd("DFTextBlock_1", UITextBlock) --
    self._wtD             = self:Wnd("DFTextBlock", UITextBlock) --
    self._wtA             = self:Wnd("DFTextBlock_2", UITextBlock) --
    self._wtJ             = self:Wnd("DFTextBlock_3", UITextBlock) --
    self._wtZ             = self:Wnd("DFTextBlock_4", UITextBlock) --
    self._wtScore         = self:Wnd("TextBlock_2", UITextBlock) --
    self._wtIndex         = self:Wnd("TextBlock_218", UITextBlock) --
    --self._wtMilitaryImg   = self:Wnd("Image_585", UIImage) --
    self._wtBanImg        = self:Wnd("DFImage_110", UIImage) --
    self._wtArmedForceImg = self:Wnd("AFIcon", UIImage) --
    self._wtTipsBtn       = self:Wnd("DFButton_70", UIButton)
    self._wtSelectImg     = self:Wnd("DFImage_29", UIImage)
    self._wtDFScaleBox     = self:Wnd("DFScaleBox_0", UIWidgetBase)
    self._wtTextBlock_BuildAndDestroyContrib = self:Wnd("TextBlock_DestroyVehicle", UITextBlock)
    self._wtTextBlock_TacticsContrib = self:Wnd("TextBlock_Contribute", UITextBlock)
    self.bIsMe = false
    self.bIsOne = false
    --- BEGIN MODIFICATION @ VIRTUOS 增加平台logo
    if IsConsole() then
        self._wtPlatformIcon = self:Wnd("wtPlatformIcon", UIImage)
    end
    --- END MODIFICATION
end

function BattleFiledAllCell:OnOpen()
    self:AddLuaEvent(Module.Settlement.Config.Events.evtGMSetShowPlayTime, self._RefreshContribText, self)
end

function BattleFiledAllCell:OnClose()
    self:RemoveAllLuaEvent()
end

function BattleFiledAllCell:_OnBtnClick()
    local info = {
        player_id = self.playerId,
        nick_name = self.playerinfo.nick_name,
        report_type = 3,
    }
    --- BEGIN MODIFICATION @ VIRTUOS
    if IsConsole() then
        info.plat_id = Module.Settlement:GetPlatIDByPlayerInfo(self.playerinfo)
    end
    --- END MODIFICATION
    local btnTbl = {
        HeadButtonType.AddFriend,
        HeadButtonType.Report,
    }
    local pos=FVector2D(0, 0)
    if DFHD_LUA==1 then
        pos = UWidgetLayoutLibrary.GetMousePositionOnViewport(GetWorld())
    else
        pos = self:GetCachedGeometry():GetAbsolutePositionAtCoordinates(FVector2D(0, 0))
    end
    self._wtSelectImg:SelfHitTestInvisible()
    local CallBack = SafeCallBack(function()
        self._wtSelectImg:Collapsed()
    end, self)
    if IsHD() then
        Facade.UIManager:AsyncShowUI(UIName2ID.CommonTipsPlayerSimple, nil, nil, info, self, btnTbl,
                FriendApplySource.MpSettlementApply, TeamInviteSource.FromAll, nil, true, pos, CallBack)
    else
        Facade.UIManager:AsyncShowUI(UIName2ID.CommonTipsPlayerSimple, nil, nil, info, self._wtDFScaleBox, btnTbl,
                FriendApplySource.MpSettlementApply, TeamInviteSource.FromAll, nil, true, nil, CallBack)
    end
end

---@param playerInfo pb_TDMPlayer
function BattleFiledAllCell:RefreshView(index, playerInfo, bIsMyBro, bIsGF)
    self._wtBanImg:Collapsed()
    self:SetBan(false)
    self.tdmTeamInfo = Server.SettlementServer:GetTDMSettlementTeamInfo()
    self._wtIndex:Visible()
    self._wtIndex:SetText(index)
    local myPlayerId = Server.AccountServer:GetPlayerId()
    self.playerId = playerInfo.player_id
    self.playerinfo = playerInfo
    local playerName = Server.SettlementServer:GetTDMSettlementPlayerInfoNameById(self.playerId)
    self.playerinfo.nick_name = playerName
    local playerScore = self.playerinfo.score
    local killNum = 0
    local assistNum = 0
    local deathNum = self.playerinfo.dead_count
    killNum = self.playerinfo.kill_ai_count + self.playerinfo.kill_player_count
    assistNum = self.playerinfo.assist_count
    self.myBro = {}
    if myPlayerId == self.playerId then
        --self:SetType(2, 0)
        self.bIsMe = true
    else
        --self:SetType(0, 0)
    end
    --if self.playerinfo.leave == true then
    --    self:SetType(3, 0)
    --end
    if self.bIsMe == false then
        self._wtTipsBtn:RemoveEvent("OnClicked")
        self._wtTipsBtn:Event("OnClicked", self._OnBtnClick, self)
    end
    self._wtK:SetText(killNum)
    self._wtD:SetText(deathNum)
    self._wtA:SetText(assistNum)
    --self._wtJ:SetText(self.playerinfo.od_player.rescue_num)
    self._wtJ:SetText(self.playerinfo.rescue_contrib)
    if InGameController:Get():IsCaptureFlag() then
        self._wtZ:SetText(self.playerinfo.capture_flag_num)
    else
        --self._wtZ:SetText(self.playerinfo.capture_count)
        self._wtZ:SetText(self.playerinfo.capture_contrib)
    end
    self._wtTextBlock_BuildAndDestroyContrib:SetText(self.playerinfo.build_and_destroy_contrib)
    self:_RefreshContribText()
    self._wtPlayerName:SetText(playerName)
    self._wtScore:SetText(string.formatnumberthousands(playerScore))

    --- BEGIN MODIFICATION @ VIRTUOS
    if IsConsole() and self._wtPlatformIcon then
        local platID = Module.Settlement:GetPlatIDByPlayerInfo(self.playerinfo)

        -- if Server.AccountServer:IsCrossPlat() and Server.SettlementServer:GetTDMSettlementIsAi(self.playerId) then
        --     logerror("BattleFiledAllCell:RefreshView", "playerId", self.playerId, "is ai")
        --     platID = PlatIDType.Plat_PC
        -- end
    

        -- if "is self" using black version plat icon, otherwise the default one.
        local platIconPath = Module.Friend:GetPlatformIconPath(platID, self.bIsMe)
        if platIconPath then
            self._wtPlatformIcon:AsyncSetImagePath(platIconPath, false)

            -- TRC: In order to show sprite asset original color, using white brush color to prevent any overrides.
            self._wtPlatformIcon:SetColorAndOpacity(FLinearColor(1, 1, 1, 1))
            self._wtPlatformIcon:SelfHitTestInvisible()
        else
            self._wtPlatformIcon:Collapsed()
        end

        if not Server.AccountServer:IsCrossPlat() then
            self._wtPlatformIcon:Collapsed()
        end
    end
    --- END MODIFICATION

    --self._wtMilitaryImg:AsyncSetImagePath(Module.Settlement.Config.Military2Image[
    --    self.playerinfo.od_player.military_rank])
    self:PlayAnimationForward(self.WBP_Settlement_Battle_DetailsItem_in, 1.0, false)
    if self.playerinfo.commander_contributor_title == EOutStandingContributionType.ECommander then
        self._wtArmedForceImg:AsyncSetImageBySoftObjectPath(UBreakthroughUIConfig.GetIconPathByType(EBreakthroughUIIconType.Icon_Commander), true, true)
    elseif Module.Settlement.Config.ArmedForce2Image[self.playerinfo.hero.armed_force_id] then
        self._wtArmedForceImg:AsyncSetImagePath(Module.Settlement.Config.ArmedForce2Image[self.playerinfo.hero.armed_force_id])
    end
    local myPlayerId = Server.AccountServer:GetPlayerId()
    --找到自己在哪个小队
    local myTeamID = -1
    for teamID, playerList in pairs(self.tdmTeamInfo) do
        for _, player in pairs(playerList) do
            if tostring(myPlayerId) == tostring(player.player_id) then
                myTeamID = teamID
                break
            end
        end
    end
    if self.bIsMe then
        self:SetType(2, 0)
    else
        self:SetType(0, 0)
        if Server.SettlementServer:IsTDMTacticalConquestEvolution() then
            return
        end
        local myTeamPlayerListInfo = self.tdmTeamInfo[myTeamID]
        for _, player in pairs(myTeamPlayerListInfo) do
            if tostring(player.player_id) == tostring(self.playerId) then
                self:SetType(1, 0)
                log("BattleFiledAllCell:RefreshView show bro")
                break
            end
        end
    end
end

function BattleFiledAllCell:_RefreshContribText()
    if Module.Settlement:GMIsShowPlayTime() then
        if self.playerinfo then
            self._wtTextBlock_TacticsContrib:SetText(self.playerinfo.tactics_contrib .. " " .. self.playerinfo.play_time)
        end
    else
        if self.playerinfo then
            self._wtTextBlock_TacticsContrib:SetText(self.playerinfo.tactics_contrib)
        end
    end
end

return BattleFiledAllCell
