---@diagnostic disable: duplicate-type
----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaSInventory)
----- LOG FUNCTION AUTO GENERATE END -----------

local InventoryServerSkinCSQueue = require "DFM.Business.ServerCenter.InventoryServerSkinPacker.InventoryServerSkinCSQueue"

---@class InventoryServer : ServerBase
local InventoryServer = {}
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local function log(...)
    loginfo("InventoryServer", ...)
end

bNewSingleBagLogic = true

function InventoryServer:Ctor()
    self.Tables = {
        MPWeaponLevelParts = Facade.TableManager:GetTable("WeaponSystem/MPWeaponLevelParts"),
    }
    self._mapMPId2FitPosList = {}
    self._mapMPId2FitArmIdList = {}
    self._mapMPId2FitExpertIdList = {}
    self._mapGid2RawPropInfo = {}

    ---@type table<weaponId, table<compId, pb_PropInfo>>
    self._weapon2UnLockPartsMap = {}
    ---@type table<weaponId, table<compId, bExistProp>>
    self._weapon2UnLockPartsIsExistMap = {}

    ---@type table<compGid, bLegit>
    self._mapCompGid2bLegit = {}
    ---@type table<weaponId, compId[]>
    self._weaponId2SvrUnlockCompList = {}
    ---@type table<weaponId, table<compId, bUnlock>>
    self._weaponId2SvrUnlockPartsMap = {}

    -- 额外解锁配件,由于蓝图原因解锁
    --- TODO 蓝图额外解锁
    ---@type table<weaponId, table<compId, bUnlock>>
    self._weapon2ExtraUnlockPartsMap = {}
    
    -- 武器皮肤解锁配件
    -- key:weaponId, value:{key:SkinID, value:{key:PartID, value:partInfo}}
    self._weapon2SkinUnlockPartsMap = {}

    -- 武器全局配件(军械库和活动)解锁配件
    -- key:weaponId, value:{key:PartID, value:bUnlock}
    self._weapon2GlobalUnlockPartsMap = {}
    
    self._weaponId2UnLockInfoMap = {}
    self._weaponId2UnLockInfoMap_Temp = {}

    self._weaponDefaultSkins = {}

    self._mapWeaponID2OldStarInfo = {}

    self.Events.evtMPEquipedItemsChanged = LuaEvent:NewIns("InventoryServer.evtMPEquipedItemsChanged")
end

function InventoryServer:FetchServerData_MP()
    ---@param res pb_CSMPDepositGetPropsRes
    local function fOnGetPropsRes(res)
        log("fOnGetPropsRes")
        -- logtable(res, true)
        if res.result == 0 then
            self:ClearSlotGroup(ESlotGroup.MPApply)
            self.Items[ESlotGroup.MPApply] = {}
            self._mapGid2Items[ESlotGroup.MPApply] = {}
            self._allRecParts[ESlotGroup.MPApply] = {}
            self._allWeaponParts[ESlotGroup.MPApply] = {}
            -- 先清空每一个Slot
            for _, slotType in pairs(EActualSlotType) do
                local itemSlot = self:GetSlot(slotType, ESlotGroup.MPApply)
                if itemSlot then
                    itemSlot:ClearItems()
                end
            end

            self:_InitMPPropLists(res.props)
            self:_InitMPPropRules(res.rules)

            --- 真正解锁的实体
            self:_InitMPPropWeaponStore(res.weapon_stores)
            self:_InitMPPropWeaponStoreLocal()

            --- 解锁信息
            self:_InitMPWeaponUnlockInfos(res.weapon_unlock_infos)
            self:_InitMPWeaponDefautSkins(res.default_weapon_skins)
            self.Events.evtPostMPDataInit:Invoke(res)
            self:ProcessSkinDependsCSRes(InventoryServerSkinCSQueue.IDs.CSMPDepositGetProps)
        else
        end
        --Server.GunsmithServer:InvokeWeaponinfoCounterEvent_CSMPDepositGetPropsRes()
    end
    log("FetchServerData_MP")

    self:ProcessSkinDependsCSReq(InventoryServerSkinCSQueue.IDs.CSMPDepositGetProps)

    local req = pb.CSMPDepositGetPropsReq:New()
    req:Request(fOnGetPropsRes)
end

function InventoryServer:DoApplyMPItemsByProps(props, modified_props, bUpdateDirty)
    if modified_props and next(modified_props) then
        if not bUpdateDirty then
            self:ResetMPItems()
        end
        --- 覆盖当前modified_props到仓库
        for idx, modifyPropInfo in ipairs(modified_props) do
            local mpItem = self:GetItemByGid(modifyPropInfo.gid, ESlotGroup.MPApply)
            if mpItem then
                --- 当前版本武器才会有改装信息
                if mpItem:IsAssemblyItem() then
                    if modifyPropInfo.gid == mpItem.gid then
                        mpItem:SetRawPropInfo(modifyPropInfo)
                    end
                end
            end
        end
    else
        logwarning('[ ArmedForce MP ]------------------------DoApplyMPItemsByProps modified_props 新版本已经去除')
    end
    for k, v in ipairs(props) do
        local mpItem = self:GetItemByGid(v.prop_gid, ESlotGroup.MPApply)
        if mpItem then
            local targetSlot = self:GetSlot(v.pos_id, ESlotGroup.MPApply)
            local slotConfig = targetSlot:GetSelfSlotConfig()
            if not slotConfig:IsSOLWeaponSlot() and targetSlot.SlotType ~= ESlotType.Hero then
                loginfo('[ ArmedForce MP ]------------------------DoApplyMPItemsByProps', v.pos_id, mpItem.name)
                local bSuccess = ItemOperaTool.DoApplyMPItem(mpItem, targetSlot, false)

                --- 可以注释掉
                local ItemMatchConditionTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemMatchConditionTool"
                local bFitRule = ItemMatchConditionTool.CheckItemMatchMPCondition(mpItem, targetSlot, 0)
                if not bFitRule then
                    logerror('[ ArmedForce MP ]------------------------当前槽位放置的道具没有正确通过Rule检查', v.pos_id, mpItem.name)
                end
            end
        else
            logwarning('[ ArmedForce MP ]------------------------DoApplyMPItemsByProps 配置的item没有在MP仓库中找到 gid:', v.prop_gid, v.pos_id)
        end
    end
    Server.TeamServer:SendChangeEquipNtf()
    self.Events.evtMPEquipedItemsChanged:Invoke()
end

function InventoryServer:DoApplyMPItemByGid(slotType, gid, modifyPropInfo)
    self:ResetMPItemByGid(gid)

    local mpItem =  self:GetItemByGid(gid, ESlotGroup.MPApply)
    if modifyPropInfo then
        mpItem:SetRawPropInfo(modifyPropInfo)
    end
    local targetSlot = self:GetSlot(slotType, ESlotGroup.MPApply)
    ItemOperaTool.DoApplyMPItem(mpItem, targetSlot)
    Server.TeamServer:SendChangeEquipNtf()
    self.Events.evtMPEquipedItemsChanged:Invoke()
end

function InventoryServer:ResetMPItems()
    for _, item in self:GetItemsIterator(ESlotGroup.MPApply) do
        local resetRawPropInfo = self._mapGid2RawPropInfo[item.gid]
        if resetRawPropInfo ~= nil then
            item:SetRawPropInfo(resetRawPropInfo)
        else
            self:_InternalProcessPropChanges_MPDelete(item.gid)
        end
    end
end

function InventoryServer:ResetMPItemByGid(gid)
    local item =  self:GetItemByGid(gid, ESlotGroup.MPApply)
    local resetRawPropInfo = self._mapGid2RawPropInfo[gid]
    if resetRawPropInfo ~= nil then
        item:SetRawPropInfo(resetRawPropInfo)
    else
        self:_InternalProcessPropChanges_MPDelete(item.gid)
    end
end

function InventoryServer:DoUseMultiMPItems(items, nums, targetIds, targetGids, fCustomCallback)
    ensure(#items == #nums, "InventoryServer:DoUseMultiMPItems 道具和数量不匹配。")

    ---@param res pb_CSMPDepositUsePropRes
    local function fOnUseMPItemsRes(res)
        if res.result == 0 then

        else

        end

        self.Events.evtMPItemUseRes:Invoke(res)

        if fCustomCallback then
            fCustomCallback(res)
        end
    end
    local req = pb.CSMPDepositUsePropReq:New()
    req.cmds = {}

    for i, item in ipairs(items) do
        local cmd = pb.UsePropCmd:New()
        cmd.prop_id = item.id
        cmd.prop_gid = item.gid
        cmd.num = nums[i]
        cmd.target_id = targetIds[i] -- could be nil
        cmd.target_gid = targetGids[i] -- could be nil

        table.insert(req.cmds, cmd)
    end
    
    req:Request(fOnUseMPItemsRes)

    return true
end

-- function InventoryServer:DoUseMPItem(item, num, fCustomCallback)
--     num = setdefault(num, item.num)

--     return self:DoUseMultiMPItems({item}, {num}, fCustomCallback)
-- end

-----------------------------------------------------------------------
--region Private

---@param list pb_PropInfo[]
function InventoryServer:_InitMPPropLists(list)
    local mpDepositSlot = self:GetSlot(ESlotType.MainContainer, ESlotGroup.MPApply)
    for _, propInfo in ipairs(list) do
        self:_InternalCreateNewItem(propInfo, mpDepositSlot)
        self._mapGid2RawPropInfo[propInfo.gid] = propInfo
        -- loginfo('[ ArmedForce MP ]------------------------_InitMPPropLists 在MP仓库中下发 道具 gid:', propInfo.gid, 'itemid:', propInfo.id, 'currentPos:', propInfo.position)
    end
end

---@param list MPDepositPropRule[]
function InventoryServer:_InitMPPropRules(list)
    for _, propRule in ipairs(list) do
        self._mapMPId2FitPosList[propRule.prop_id] = propRule.pos_ids
        self._mapMPId2FitArmIdList[propRule.prop_id] = propRule.armedforce_ids
        self._mapMPId2FitExpertIdList[propRule.prop_id] = propRule.expert_ids
    end
    if IsInEditor() then
        dump(self._mapMPId2FitPosList)
        dump(self._mapMPId2FitArmIdList)
        dump(self._mapMPId2FitExpertIdList)
    end
end

function InventoryServer:GetMPFitArmIdListByItemId(itemId)
    return self._mapMPId2FitArmIdList[itemId] or {}
end

function InventoryServer:GetMPFitExpertIdListByItemId(itemId)
    return self._mapMPId2FitExpertIdList[itemId] or {}
end

function InventoryServer:GetMPFitPosListByItemId(itemId)
    return self._mapMPId2FitPosList[itemId] or {}
end

function InventoryServer:_DebugDiff(weaponId, oldCompInfos, newCompIds)
    local mapId2Fit = {}
    --- 旧
    for _, component in ipairs(oldCompInfos) do
        --- 新
        for _, compId in ipairs(newCompIds) do
            if component.id == compId then
                mapId2Fit[compId] = true
                break
            end
        end
    end
    --- diff
    for _, component in ipairs(oldCompInfos) do
        if not mapId2Fit[component.id] then
            log('[ Long2 WeaponUnlock - Diff ] _InitMPPropWeaponStore 武器实际解锁 新的里缺了，旧的里有', weaponId, component.id)
        end
    end

    local mapId2Fit2 = {}
    --- 新
    for _, compId in ipairs(newCompIds) do
        --- 旧
        for _, component in ipairs(oldCompInfos) do
            if component.id == compId then
                mapId2Fit2[compId] = true
                break
            end
        end
    end
    for _, compId in ipairs(newCompIds) do
        if not mapId2Fit2[compId] then
            log('[ Long2 WeaponUnlock - Diff ] _InitMPPropWeaponStore 武器实际解锁 旧的里缺了，新的里有', weaponId, compId)
        end
    end
end


---@class pb_MPWeaponStoreCS : ProtoBase
---@field public weapon_id number
---@field public components pb_PropInfo[]
---@field public skin_comps pb_MPSkinComponents[]
---@field public unlocked_comps number[]
---@field public global_comps number[]

-- 额外解锁
---@param weapon_stores MPWeaponStoreCS[]
function InventoryServer:_InitMPPropWeaponStore(weapon_stores)
    local existPropMapping = self:_GenExistPropMapping()
    for _, weaponStore in ipairs(weapon_stores) do
        local weaponId = weaponStore.weapon_id

        --- 初始化
        if self._weapon2UnLockPartsMap[weaponId] == nil then
            self._weapon2UnLockPartsMap[weaponId] = {}
        end
        if self._weaponId2SvrUnlockPartsMap[weaponId] == nil then
            self._weaponId2SvrUnlockPartsMap[weaponId] = {}
        end
        if self._weapon2UnLockPartsIsExistMap[weaponId] == nil then
            self._weapon2UnLockPartsIsExistMap[weaponId] = {}
        end

        --- TODO 蓝图额外解锁
        if self._weapon2ExtraUnlockPartsMap[weaponId] == nil then
            self._weapon2ExtraUnlockPartsMap[weaponId] = {}
        end

        --- New
        if weaponStore.unlocked_comps then
            --- 全量刷新武器内已解锁列表
            self._weaponId2SvrUnlockCompList[weaponId] = weaponStore.unlocked_comps
            for _, compId in ipairs(weaponStore.unlocked_comps) do
                local component = self:_CreateOrGetCompPropById(compId, existPropMapping)
                if component then
                    -- log('[ Long2 WeaponUnlock - 新版 ] _InitMPPropWeaponStore weaponStore.unlocked_comps 武器实际解锁 ID', weaponId, compId)
                    self._weapon2UnLockPartsMap[weaponId][component.id] = component
                    self._weaponId2SvrUnlockPartsMap[weaponId][component.id] = true
                    self._weapon2UnLockPartsIsExistMap[weaponId][component.id] = true

                    --- TODO 蓝图额外解锁
                    self._weapon2ExtraUnlockPartsMap[weaponId][component.id] = true
                end
            end
        else
            --- 全量刷新武器内已解锁列表
            self._weaponId2SvrUnlockCompList[weaponId] = {}
        end

        -- 皮肤解锁配件
        if self._weapon2SkinUnlockPartsMap[weaponId] == nil then
            self._weapon2SkinUnlockPartsMap[weaponId] = {}
        end
        for _, skinComponent in ipairs(weaponStore.skin_comps) do
            local skinID = skinComponent.skin_id
            if self._weapon2SkinUnlockPartsMap[weaponId][skinID] == nil then
                self._weapon2SkinUnlockPartsMap[weaponId][skinID] = {}
            end
            for _, componentID in ipairs(skinComponent.component_ids) do
                self._weapon2SkinUnlockPartsMap[weaponId][skinID][componentID] = true
            end
        end

        -- 全局解锁配件(活动或者军械库解锁)
        if self._weapon2GlobalUnlockPartsMap[weaponId] == nil then
            self._weapon2GlobalUnlockPartsMap[weaponId] = {}
        end
        for _, partID in ipairs(weaponStore.global_comps) do
            self._weapon2GlobalUnlockPartsMap[weaponId][partID] = true
        end
    end
end

function InventoryServer:_GenExistPropMapping()
    local existPropMapping = {}
    for _, item in self:GetItemsIterator(ESlotGroup.MPApply) do
        existPropMapping[item.id] = item.rawPropInfo
    end
    return existPropMapping
end

function InventoryServer:_CreateOrGetCompPropById(partsId, existPropMapping)
    local partsComponentId = tonumber(partsId)
    if partsComponentId then
        local existProp
        if existPropMapping then
            existProp = existPropMapping[partsComponentId]
        else
            for _, item in self:GetItemsIterator(ESlotGroup.MPApply) do
                if tonumber(item.id) == partsComponentId then
                    existProp = item.rawPropInfo
                    break
                end
            end
        end

        if existProp then
            local copyRealPropInfo = clone(existProp)
            if copyRealPropInfo then
                -- log('[ Long2 WeaponUnlock - 新版 ] _CreateOrGetCompPropById copyRealPropInfo 配件实际解锁 真 PropInfo ', copyRealPropInfo.id)
                self._mapCompGid2bLegit[existProp.gid] = true
                return copyRealPropInfo
            end
        else
            local prop = pb.PropInfo:New()
            prop.id = partsComponentId
            prop.num = 1
            prop.gid = GetGid()
            return prop
        end
    end
    return nil
end

function InventoryServer:_InitMPPropWeaponStoreLocal()
    --配件数据整理
    local existPropMapping = self:_GenExistPropMapping()
    local MPWeaponLevelPartsTable = Facade.TableManager:GetTable("WeaponSystem/MPWeaponLevelParts")
    for _, row in MPWeaponLevelPartsTable:LessGCPairs() do
        if row then
            local weaponId = tonumber(row.RecFunctionId)
            if weaponId then
                local bNew = false
                if self._weapon2UnLockPartsMap[weaponId] == nil then
                    -- log('[ Long2 WeaponUnlock - 这个武器读了本地解锁 ] _InitMPPropWeaponStoreLocal 但协议没下发', weaponId)
                    bNew = true
                    self._weapon2UnLockPartsMap[weaponId] = {}
                else
                    -- log('[ Long2 WeaponUnlock - 这个武器读了本地解锁 ] _InitMPPropWeaponStoreLocal 但协议下发过了', weaponId, self._weapon2UnLockPartsMap[weaponId])
                end
                if self._weapon2UnLockPartsIsExistMap[weaponId] == nil then
                    self._weapon2UnLockPartsIsExistMap[weaponId] = {}
                end
                for _, partsId in ipairs(row.UnlockParts) do
                    local partsComponentId = tonumber(partsId)
                    if partsComponentId then
                        --- 本地prop改为lazy
                        self._weapon2UnLockPartsIsExistMap[weaponId][partsComponentId] = true
                    end
                end
            end
        end
    end
end

---@class pb_CSWeaponUnlockInfo : ProtoBase
---@field public weapon_id number
---@field public unlock_type number
---@field public level number

---@param weapon_unlock_infos pb_CSWeaponUnlockInfo[]
function InventoryServer:_InitMPWeaponUnlockInfos(weapon_unlock_infos)
    if weapon_unlock_infos then
        for _, weaponUnlockInfo in ipairs(weapon_unlock_infos) do
            local weaponId = weaponUnlockInfo.weapon_id
            self._weaponId2UnLockInfoMap[weaponId] = weaponUnlockInfo
            -- loginfo('[ Long2 WeaponUnlock - 不变 ] _InitMPWeaponUnlockInfos _weaponId2UnLockInfoMap 标准可解锁列表 weaponId:',weaponId, weaponUnlockInfo.unlock_type, weaponUnlockInfo.level)
            self._weaponId2UnLockInfoMap_Temp[weaponId] = nil

            --- 解锁武器未存在于池子的情况
            local fitArmIdList = Server.InventoryServer:GetMPFitArmIdListByItemId(weaponId)
            local fitPosList = Server.InventoryServer:GetMPFitPosListByItemId(weaponId)
            if #fitArmIdList == 0 then
                logwarning('CheckIsFitCurMPArmRule id:',weaponId,'没有适配的兵种列表,无法通过过滤')
            end
            if #fitPosList == 0 then
                logwarning('CheckIsFitCurMPArmRule id:',weaponId,'没有适配的Pos列表,无法通过过滤')
            end
        end
    end
end

--- 静态解锁信息
function InventoryServer:GetMPWeaponLockedInfoMap()
    return self._weaponId2UnLockInfoMap
end

--- 静态解锁信息
function InventoryServer:GetMPWeaponLockedInfoById(itemId)
    return self._weaponId2UnLockInfoMap and self._weaponId2UnLockInfoMap[itemId] or {}
end

--- 静态解锁信息
function InventoryServer:UpdateMPWeapoLockedInfoMap(newItemId, bUnlock)
    if bUnlock then
        if self._weaponId2UnLockInfoMap[newItemId] ~= nil then
            local rawLockedInfo = self._weaponId2UnLockInfoMap[newItemId]
            if rawLockedInfo then
                self._weaponId2UnLockInfoMap_Temp[newItemId] = rawLockedInfo
                self._weaponId2UnLockInfoMap[newItemId] = nil
                logwarning('[ Long2 WeaponUnlock - 通用 ] UpdateMPWeapoLockedInfoMap(newItemId) mp道具已解锁', newItemId)
            end
        end
    else
        if self._weaponId2UnLockInfoMap[newItemId] == nil then
            local rawLockedInfo = self._weaponId2UnLockInfoMap_Temp[newItemId]
            if rawLockedInfo then
                self._weaponId2UnLockInfoMap_Temp[newItemId] = nil
                self._weaponId2UnLockInfoMap[newItemId] = rawLockedInfo
                logwarning('[ Long2 WeaponUnlock - 通用 ] UpdateMPWeapoLockedInfoMap(newItemId) mp道具已加锁（可能存在删除和出售道具的情况）', newItemId)
            end
        end
    end
end

--- Long1 weaponId获得当前MP武器对应已解锁列表
function InventoryServer:GetMPWeaponSvrUnLockPartList(weaponId)
    local compIdList = clone(self._weaponId2SvrUnlockCompList[weaponId])
    return compIdList
end

--- Long1 weaponId判定当前MP武器对应配件是否解锁并存在于仓库
function InventoryServer:IsMPWeaponUnLockPartInInventory(weaponId, partId)
    if not self:IsMPWeaponSvrUnLockPart(weaponId, partId) then
        return false
    end
    local bExistProp = self:IsExistMPWeaponUnLockPartsInfo(weaponId, partId)
    if not bExistProp then
        return false
    end
    local compPropInfo = self:GetMPWeaponUnLockPartsInfo(weaponId, partId)
    if compPropInfo == nil then
        return false
    end
    if self._mapCompGid2bLegit then
        return self._mapCompGid2bLegit[compPropInfo.gid]
    else
        return false
    end
end

--- Long1 weaponId判定当前MP武器对应配件是否解锁
function InventoryServer:IsMPWeaponSvrUnLockPart(weaponId, partId)
    if self._weaponId2SvrUnlockPartsMap[weaponId] then
        return self._weaponId2SvrUnlockPartsMap[weaponId][partId]
    end
    return false
end

function InventoryServer:IsExistMPWeaponUnLockPartsInfo(weaponId, partId)
    if self._weapon2UnLockPartsIsExistMap[weaponId] then
        return self._weapon2UnLockPartsIsExistMap[weaponId][partId]
    end
    return false
end

-- 该接口内包含来自服务器和本地武器解锁表的全部对应PropInfo(已解锁+未解锁)
function InventoryServer:GetMPWeaponUnLockPartsInfo(weaponId, partId)
    local findOrCreateProp
    if self._weapon2UnLockPartsMap[weaponId] then
        findOrCreateProp = self._weapon2UnLockPartsMap[weaponId][partId]
    end
    if findOrCreateProp == nil and self:IsExistMPWeaponUnLockPartsInfo(weaponId, partId) then
        local existPropMapping = self:_GenExistPropMapping()
        findOrCreateProp = self:_CreateOrGetCompPropById(partId, existPropMapping)
         if findOrCreateProp then
            local partsComponentId = tonumber(partId)
            self._weapon2UnLockPartsMap[weaponId][partsComponentId] = findOrCreateProp
        end
    end
    return findOrCreateProp
end

-- 该接口提供配件是额外原因(蓝图等)解锁检查
function InventoryServer:IsMPWeaponExtraUnLockPart(weaponId, partId)
    --- TODO 蓝图额外解锁
    if self._weapon2ExtraUnlockPartsMap[weaponId] then
        return self._weapon2ExtraUnlockPartsMap[weaponId][partId]
    end
    return false
end

-- (Deprecated)该接口内包含来自皮肤解锁的全部对应PropInfo
-- function InventoryServer:GetMPWeaponSkinUnLockPartsInfo(weaponId, skinID, partId)
--     if self._weapon2SkinUnlockPartsMap[weaponId] and self._weapon2SkinUnlockPartsMap[weaponId][skinID] then
--         return self._weapon2SkinUnlockPartsMap[weaponId][skinID][partId]
--     end
--     return nil
-- end

-- 该接口提供配件是皮肤解锁检查
function InventoryServer:GetMPWeaponSkinIsUnLockPartsInfo(weaponId, skinID, partId)
    if self._weapon2SkinUnlockPartsMap[weaponId] and self._weapon2SkinUnlockPartsMap[weaponId][skinID] then
        return self._weapon2SkinUnlockPartsMap[weaponId][skinID][partId] or false
    end
    return false
end

-- 该接口获取全局解锁配件PropInfo
function InventoryServer:GetMPWeaponGlobalUnLockPartsInfo(weaponId, partId)
    local bIsUnlock = self:GetIsUnLockMPWeaponGlobalParts(weaponId, partId)
    if not bIsUnlock then
        return nil
    end
    local bExistProp = self:IsExistMPWeaponUnLockPartsInfo(weaponId, partId)
    if not bExistProp then
        return nil
    end
    return self:GetMPWeaponUnLockPartsInfo(weaponId, partId)
end

-- 该接口获取全局解锁配件是否解锁
function InventoryServer:GetIsUnLockMPWeaponGlobalParts(weaponId, partId)
    if self._weapon2GlobalUnlockPartsMap[weaponId] then
        return self._weapon2GlobalUnlockPartsMap[weaponId][partId]
    end
    return false
end

function InventoryServer:GetDefaultWeaponSkins()
    return self._weaponDefaultSkins
end

---------------------------------------------------------------------------------
--- MP仓库 刷新 Ntf
---------------------------------------------------------------------------------
---@class pb_CSMPDataChange : ProtoBase
---@field public prop_changes pb_CSMPPropChange[]
---@field public bag_changes pb_CSMPBagChange[]
---@field public reason number
---@field public source number
---@field public weapon_changes pb_WeaponChange[]
---@field public new_bags pb_MPBagArmedPresetCS[]
---@field public vehicle_changes pb_CSVehicle[]
---@field public vehicle_part_changes pb_CSVehiclePart[]
---@field public preset_changes pb_MPPresetCS[]

---@param ntf pb_CSMPDepositDataChangeNtf
function InventoryServer:_OnCSMPDepositChangeNtf(ntf)
    -- print(" >>>>>>>>>>>>>>>> _OnCSMPDepositChangeNtf : ", LogUtil.LogTable(ntf, true))
    local change = ntf.mpdeposit_change
    self:ProcessPbMPDataChange(change, EMPChangeReason.MPNtf)
end


---------------------------------------------------------------------------------
--- MP仓库 刷新 changes
---------------------------------------------------------------------------------
---@param change pb_CSMPDataChange
---@param eMPChangeReason EMPChangeReason
function InventoryServer:ProcessPbMPDataChange(change, eMPChangeReason)
    eMPChangeReason = setdefault(eMPChangeReason, EMPChangeReason.MPNtf)

    self:_InternalProcessPropChanges(change.prop_changes)
    self:_InternalProcessWeaponChanges(change.weapon_changes)
    self:_InternalProcessWeaponUnlockCompChanges(change.weapon_unlocked_comp_changes)
    self:_AddMPWeaponDefautSkins(change.skin_setup_changes)
    --- 事件通知给上层业务
    --- preset_changes交给上层ArmedForceServer
    --- vehicle_changes交给上层VehicleServer
    self.Events.evtPostMPDataChange:Invoke(change)
    if eMPChangeReason == EMPChangeReason.Assembly then
        self.Events.evtMPDataChangedFromWeaponModify:Invoke()
    end
end


function InventoryServer:_InternalProcessPropChanges(prop_changes)
    local mpDepositSlot = self:GetSlot(ESlotType.MainContainer, ESlotGroup.MPApply)
    if prop_changes then
        for _, mpPropChange in ipairs(prop_changes) do
            local prop = mpPropChange.prop
            local gid = prop.gid

            if mpPropChange.type == MPPropChangeType.MPPropChangeAdd then
                if bNewSingleBagLogic then
                    Server.InventoryServer:OverridePropInfoSkinInfosChecked(prop)
                end

                self:_InternalCreateNewItem(prop, mpDepositSlot)
                self._mapGid2RawPropInfo[prop.gid] = prop
                self:UpdateMPWeapoLockedInfoMap(prop.id, true)
                
            elseif mpPropChange.type == MPPropChangeType.MPPropChangeDel then
                self:_InternalProcessPropChanges_MPDelete(gid)
                self._mapGid2RawPropInfo[prop.gid] = nil
                self:UpdateMPWeapoLockedInfoMap(prop.id, false)
            elseif mpPropChange.type == MPPropChangeType.MPPropChangeModify then
                if bNewSingleBagLogic then
                    Server.InventoryServer:OverridePropInfoSkinInfosChecked(prop)
                end
                
                self._mapGid2RawPropInfo[prop.gid] = prop
                local item = self:GetItemByGid(gid,  ESlotGroup.MPApply)
                if item then
                    item:SetRawPropInfo(prop)
                else
                    logwarning(string.format("_OnCSMPDepositChangeNtf item not found, gid = %d", gid))
                end
                self:UpdateMPWeapoLockedInfoMap(prop.id, true)
            end
        end
    end
end

---@param propChange pb_PropChange
function InventoryServer:_InternalProcessPropChanges_MPDelete(gid)
    local itemGid = gid
    local item = self:GetItemByGid(itemGid)
    logwarning("[ ArmedForce MP DeleteTest ]------------------------_InternalProcessPropChanges_MPDelete itemGid:", itemGid, "item:", item)
    if item then
        ---@type ItemSlot
        local oldItemSlot = item.InSlot
        local oldLoc = oldItemSlot:GetItemLocation(item)
        if oldItemSlot then
            oldItemSlot:RemoveItemByGID(itemGid, false)
        else
            local mpDepositSlot = self:GetSlot(ESlotType.MainContainer, ESlotGroup.MPApply)
            mpDepositSlot:RemoveItemByGID(gid, false)
        end

        if oldItemSlot:GetSlotGroup() == ESlotGroup.MPApply then
            if WeaponAssemblyTool.IsAssembledWeapon(item.id) then
                --保存机匣
                if item.itemMainType == EItemType.Receiver then
                    self._allRecParts[ESlotGroup.Player][gid] = nil
                elseif item.itemMainType == EItemType.Adapter then
                    self._allWeaponParts[ESlotGroup.Player][gid] = nil
                end
            end
        end
    end
end


function InventoryServer:_InternalProcessWeaponChanges(weapon_changes)
    if weapon_changes then
        for _, weaponChange in ipairs(weapon_changes) do
            local weaponId = weaponChange.ID

            -- 皮肤解锁配件
            if self._weapon2SkinUnlockPartsMap[weaponId] == nil then
                self._weapon2SkinUnlockPartsMap[weaponId] = {}
            end
            for _, skinComponent in ipairs(weaponChange.skip_comps) do
                local skinID = skinComponent.skin_id
                if self._weapon2SkinUnlockPartsMap[weaponId][skinID] == nil then
                    self._weapon2SkinUnlockPartsMap[weaponId][skinID] = {}
                end
                for _, componentID in ipairs(skinComponent.component_ids) do
                    self._weapon2SkinUnlockPartsMap[weaponId][skinID][componentID] = true
                end
            end

            -- 全局解锁配件(活动或者军械库解锁)
            if self._weapon2GlobalUnlockPartsMap[weaponId] == nil then
                self._weapon2GlobalUnlockPartsMap[weaponId] = {}
            end
            for _, partID in ipairs(weaponChange.global_comps) do
                self._weapon2GlobalUnlockPartsMap[weaponId][partID] = true
            end

            local bMPDepositUpdate = false
            if weaponChange.cur_kill_count ~= 0 or weaponChange.star_num ~= 0 then
                for _, item in self:GetItemsIterator(ESlotGroup.MPApply) do
                    if tonumber(item.id) == weaponId then
                        local weaponFeature = item:GetFeature(EFeatureType.Weapon)
                        if weaponFeature then
                            weaponFeature:UpdateWeaponStarInfo(weaponChange.star_num, weaponChange.cur_kill_count)
                            bMPDepositUpdate = true
                        end
                    end
                end
            end
            if weaponChange.old_star_num ~= 0 or weaponChange.old_kill_count ~= 0 then
                self._mapWeaponID2OldStarInfo[weaponId] = {
                    oldStarNum = weaponChange.old_star_num,
                    oldKillCount = weaponChange.old_kill_count
                }
            end

            local curGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
            logwarning("[Star Killer Debug] UpdateWeaponStarInfo weaponItemID:", weaponId,
                "star_num:", weaponChange.old_star_num, "->", weaponChange.star_num,
                "kill_count:", weaponChange.old_kill_count, "->", weaponChange.cur_kill_count,
                "UpdateWeaponStarInfo curGameFlow:", curGameFlow, "bMPDepositUpdate:", bMPDepositUpdate)
        end
    end
end

function InventoryServer:GetOldStarNum(weaponId)
    if weaponId == nil then
        logerror("[Star Killer Debug] GetOldStarNum weaponId is nil")
        return 0
    end
    local oldStarInfo = self._mapWeaponID2OldStarInfo[weaponId]
    return oldStarInfo and oldStarInfo.oldStarNum or 0
end

function InventoryServer:GetOldKillCount(weaponId)
    if weaponId == nil then
        logerror("[Star Killer Debug] GetOldKillCount weaponId is nil")
        return 0
    end
    local oldStarInfo = self._mapWeaponID2OldStarInfo[weaponId]
    return oldStarInfo and oldStarInfo.oldKillCount or 0
end

function InventoryServer:ClearOldWeaponChangeDatas()
    self._mapWeaponID2OldStarInfo = {}
    logwarning("[Star Killer Debug] InventoryServer:ClearOldWeaponChangeDatas()")
end

---@class pb_WeaponUnlockedComps : ProtoBase
---@field public weapon_id number
---@field public unlocked_comp_ids number[]

function InventoryServer:_InternalProcessWeaponUnlockCompChanges(weaponUnlockedComps)
    if weaponUnlockedComps then
        local existPropMapping = self:_GenExistPropMapping()
        for _, weaponUnlockedComp in ipairs(weaponUnlockedComps) do
            local weaponId = weaponUnlockedComp.weapon_id
            if weaponUnlockedComp.unlocked_comp_ids then
                --- 全量刷新武器内已解锁列表
                self._weaponId2SvrUnlockCompList[weaponId] = weaponUnlockedComp.unlocked_comp_ids

                for _, compId in ipairs(weaponUnlockedComp.unlocked_comp_ids) do
                    local component = self:_CreateOrGetCompPropById(compId, existPropMapping)
                    if component then
                        if self._weapon2UnLockPartsMap[weaponId] == nil then
                            self._weapon2UnLockPartsMap[weaponId] = {}
                        end
                        if self._weaponId2SvrUnlockPartsMap[weaponId] == nil then
                            self._weaponId2SvrUnlockPartsMap[weaponId] = {}
                        end
                        if self._weapon2UnLockPartsIsExistMap[weaponId] == nil then
                            self._weapon2UnLockPartsIsExistMap[weaponId] = {}
                        end
                        --- TODO 蓝图额外解锁
                        if self._weapon2ExtraUnlockPartsMap[weaponId] == nil then
                            self._weapon2ExtraUnlockPartsMap[weaponId] = {}
                        end
                        
                        self._weapon2UnLockPartsMap[weaponId][component.id] = component
                        self._weapon2UnLockPartsIsExistMap[weaponId][component.id] = true
                        self._weaponId2SvrUnlockPartsMap[weaponId][component.id] = true
                        --- TODO 蓝图额外解锁
                        self._weapon2ExtraUnlockPartsMap[weaponId][component.id] = true
                    end
                end
            else
                --- 全量刷新武器内已解锁列表
                self._weaponId2SvrUnlockCompList[weaponId] = {}
            end
        end
    end
end


function InventoryServer:_InitMPWeaponDefautSkins(defaultSkins)
    --全量下发的
    self._weaponDefaultSkins = {}
    if defaultSkins then 
        for k,v in pairs(defaultSkins) do 
            self._weaponDefaultSkins[v.weapon_id] = v
        end
    end
end

function InventoryServer:_AddMPWeaponDefautSkins(defaultSkins)
    --增量下发的
    if defaultSkins then 
        for k,v in pairs(defaultSkins) do 
            self._weaponDefaultSkins[v.weapon_id] = v
        end
    end
end

--endregion
-----------------------------------------------------------------------

--------------------------------------------------------------------------
--- MPTDMArmProp Skins
--------------------------------------------------------------------------
function InventoryServer:GetMPArmPropSkinIDList(armPropItemID)
    local skinIDList = {}

    return skinIDList
end

function InventoryServer:CheckMPArmPropAnyUnlockSkinID(armPropItemID)
    return true
end

function InventoryServer:GetMPItemByID(itemID)
    for _, item in Server.InventoryServer:GetItemsIterator(ESlotGroup.MPApply) do
        if item.id == itemID then
            return item
        end
    end
end

function InventoryServer:DoUseMPTDMArmPropSkinID(armPropItemID, skinID, fCustomCallback)
    local armedPropApplySkinCmd = {
        armed_prop_id = armPropItemID,
        skin_id = skinID,
    }
    local armedPropApplySkinCmdList = {}
    table.insert(armedPropApplySkinCmdList, armedPropApplySkinCmd)
    self:DoUseMPTDMArmedPropSkinCmds(armedPropApplySkinCmdList, fCustomCallback)
end

function InventoryServer:DoUseMPTDMArmedPropSkinCmds(armedPropApplySkinCmdList, fCustomCallback)
    ---@param res pb_CSMPApplyArmedPropSkinRes
    local function fOnUseMPApplyArmedPropSkinCmdsRes(res)
        if res.result == 0 then
            local armedPropList = res.armed_prop_list
            for idx, armedPropInfo in ipairs(armedPropList) do
                local armedPropItem = self:GetItemByGid(armedPropInfo.gid, ESlotGroup.MPApply)
                local tdmItemSlot = Server.InventoryServer:GetSlot(ESlotType.MP_ArmedForceTDMProp, ESlotGroup.MPApply)
                if armedPropItem and tdmItemSlot:CheckItemFitSlot(armedPropItem) then
                    armedPropItem:SetRawPropInfo(armedPropInfo)
                else
                    logwarning('[ ArmedForce MP ]------------------------DoUseMPTDMArmedPropSkinCmds armedPropItem not found in MPApply', armedPropInfo.gid, armedPropInfo.id)
                end
            end
        else
            logwarning('[ ArmedForce MP ]------------------------DoUseMPTDMArmedPropSkinCmds failed', res.result)
        end
        if fCustomCallback then
            fCustomCallback(res)
        end
    end
    local req = pb.CSMPApplyArmedPropSkinReq:New()
    req.cmds = armedPropApplySkinCmdList
    req:Request(fOnUseMPApplyArmedPropSkinCmdsRes)
    return true
end


--------------------------------------------------------------------------
--- ResetServerData_MP
--------------------------------------------------------------------------
function InventoryServer:ResetServerData_MP()
    -----------------------------------------------------------------------
    --region From InventoryServer_MP
    local groupId = ESlotGroup.MPApply

    self.Items[groupId] = {}
    self._mapGid2Items[groupId] = {}
    self._allItemSlots[groupId] = {}
    self._allRecParts[groupId] = {}
    self._allWeaponParts[groupId] = {}

    --- MP独占数据入局不清理
    self._mapMPId2FitPosList = {}
    self._mapMPId2FitArmIdList = {}
    self._mapMPId2FitExpertIdList = {}
    self._mapGid2RawPropInfo = {}

    ---@type table<weaponId, table<compId, pb_PropInfo>>
    self._weapon2UnLockPartsMap = {}
    ---@type table<weaponId, table<compId, bExistProp>>
    self._weapon2UnLockPartsIsExistMap = {}
    ---@type table<compGid, bLegit>
    self._mapCompGid2bLegit = {}
    ---@type table<weaponId, table<compId, bUnlock>>
    self._weaponId2SvrUnlockPartsMap = {}

    -- 额外解锁配件,由于蓝图原因解锁
    ---@type table<weaponId, table<compId, bUnlock>>
    self._weapon2ExtraUnlockPartsMap = {}
    ---@type table<weaponId, unlockInfo>>
    self._weaponId2UnLockInfoMap = {}

    --endregion
    -----------------------------------------------------------------------
end


return InventoryServer