----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMQuest)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class QuestSeasonTaskHead : LuaUIBaseView

local FAnchors = import "Anchors"
local ETextChecked=import "ETextChecked"

local QuestSeasonTaskHead = ui("QuestSeasonTaskHead")

function QuestSeasonTaskHead:Ctor()
    self._wtTitleText = self:Wnd("TextBlockMain", UITextBlock)
    self._wtStarIconList = {
        self:Wnd("DFImage_1", UIWidgetBase),
        self:Wnd("DFImage_2", UIWidgetBase),
        self:Wnd("DFImage_3", UIWidgetBase),
        self:Wnd("DFImage_4", UIWidgetBase),
        self:Wnd("DFImage_5", UIWidgetBase),
    }
    self._wtStarText = self:Wnd("DFTextBlock_182", UITextBlock)
    self._wtProgressText = self:Wnd("DFTextBlock", UITextBlock)
    self._wtTypeText = self:Wnd("DFTextBlock_44", UITextBlock)
    self._wtUnlockTimeText = self:Wnd("DFTextBlock_72", UITextBlock)
    
    self._wtAnimPanel = self:Wnd("DFCanvas_Dongxiao", UIWidgetBase)

    self._wtBtn = self:Wnd("DFButton_67", UIButton)

    self._bIsUnfold = false
    self._bIsUnLock = false
    self._bIsSelected = false
    self._canPlayAnim = false
    self._groupId = nil
    self._lineInfo = nil
    self._timerHandle = nil

end

function QuestSeasonTaskHead:OnShowBegin()
    self:AddLuaEvent(Server.QuestServer.Events.evtUpdateQuestState, self._OnQuestStateUpdate, self)
    self:AddLuaEvent(Module.Reward.Config.Events.evtQuestCloseRewardPanel, self._OnCloseRewardPanel, self)

    if self._wtAnimWidget then
        self._wtAnimWidget:StopAllAnimations()
    end
end

function QuestSeasonTaskHead:OnHide()
    self:RemoveAllLuaEvent()
    self:ClearData()    
    if self._wtAnimWidget then
        self._wtAnimWidget:StopAllAnimations()
    end
end

function QuestSeasonTaskHead:ClearData()

    if self._timerHandle then
        self._timerHandle:Release()
        self._timerHandle = nil
    end
    if self.curReddotProxy ~= nil then
        Module.ReddotTrie:UnRegisterDynamicReddot(EReddotTrieObserverType.Quest,EQuestDynamicDataType.EQuestSeasonReddot,self.curReddotProxy)
        self.curReddotProxy = nil
    end
    self._bIsUnfold = false
    self._bIsUnLock = false
    self._bIsSelected = false
    self._canPlayAnim = false
    self._groupId = nil
    self._lineInfo = nil
end

function QuestSeasonTaskHead:RefreshWithGroup(lineInfo, stageId, groupInfo)

    self:ClearData()
    
    self._lineInfo = lineInfo

    if groupInfo == nil then
        logerror("RefreshWithGroup: Invalid Group Info")
        return 
    end

    self._groupId = groupInfo.groupID

    self._wtTitleText:SetText(groupInfo.name)
    self._wtTitleText:SetInCheckedStyle(ETextChecked.Unchecked)
    self._wtTitleText:SelfHitTestInvisible()

    if groupInfo.rewardStar <= 5 then
        for i = 1, groupInfo.rewardStar, 1 do
            self._wtStarIconList[i]:SelfHitTestInvisible()
        end
        for i = groupInfo.rewardStar + 1, 5, 1 do
            self._wtStarIconList[i]:Collapsed()
        end
        self._wtStarText:Collapsed()
    else
        self._wtStarIconList[1]:SelfHitTestInvisible()
        for i = 2, 5 do
            self._wtStarIconList[i]:Collapsed()
        end
        self._wtStarText:SetText(tostring(groupInfo.rewardStar))
        self._wtStarText:SelfHitTestInvisible()
    end

    local EQuestSeasonType = Module.Quest.Config.EQuestSeasonType
    if lineInfo:IsMainGroup(stageId, groupInfo.groupID) then
        self._wtTypeText:SetText(Module.Quest.Config.Loc.QuestTypeLocSeasonMain)
        self:SetColorType(0)
    else
        if groupInfo.type == EQuestSeasonType.Hard then
            self._wtTypeText:SetText(Module.Quest.Config.Loc.QuestTypeLocSeasonBranchHard)
        elseif groupInfo.type == EQuestSeasonType.Easy then
            self._wtTypeText:SetText(Module.Quest.Config.Loc.QuestTypeLocSeasonBranchEasy)
        end
        self:SetColorType(1)
    end

    if lineInfo:IsUnlockByGroupId(groupInfo.groupID) then
        local completedNum = lineInfo:GetCompletedQuestNum(groupInfo.groupID)
        local total = lineInfo:GetAllQuestIdsByGroupID(groupInfo.groupID)
        self._wtProgressText:SetText(string.format(Module.Quest.Config.Loc.QuestSeasonStarProgress,
        completedNum,
        #total
        ))
        self._wtProgressText:Visible()
        self._wtUnlockTimeText:Collapsed()
        self:SetIsUnLocked(true)
    else
        self:SetIsUnLocked(false)
        if self._timerHandle then
            self._timerHandle:Release()
            self._timerHandle = nil
        end

        self:_UpdateRemainCountTime()

        self._timerHandle = Timer:NewIns(5, 0)
        self._timerHandle:AddListener(self._UpdateRemainCountTime, self)
        self._timerHandle:Start()
    end

    self:SetIsUnfold(false)

    self:_RegisterReddot()
end

function QuestSeasonTaskHead:_UpdateRemainCountTime()

    if self._groupId == nil then
        logerror("_UpdateRemainCountTime: Invalid GroupInfo")
        return
    end

    local remainTime = self._lineInfo:GetUnlockTimeByGroupId(self._groupId)
    if remainTime <= 0 then 

        if self._timerHandle then
            self._timerHandle:Release()
            self._timerHandle = nil
        end

        Module.Quest.Config.evtQuestSeasonGroupUnlocked:Invoke(self._groupId)
        local completedNum = self._lineInfo:GetCompletedQuestNum(self._groupId)
        local total = self._lineInfo:GetAllQuestIdsByGroupID(self._groupId)
        self._wtProgressText:SetText(string.format(Module.Quest.Config.Loc.QuestSeasonStarProgress,
        completedNum,
        #total
        ))
        self._wtProgressText:Visible()
        self._wtUnlockTimeText:Collapsed()
        self:SetIsUnLocked(true)
        return
    end
    
    local str = ""
    local day, hour, min, sec = TimeUtil.GetSecondsFormatDDHHMMSS(remainTime)
    if day > 0 then
        str = string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeDay, day, hour, min)
    elseif hour > 0 then
        str = string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeHour, hour, min)
    elseif min > 0 then
        str = string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeMin, sec > 0 and min + 1 or min)
    end

    self._wtProgressText:Collapsed()
    self._wtUnlockTimeText:Visible()
    self._wtUnlockTimeText:SetText(str)
end


function QuestSeasonTaskHead:SetIsUnfold(bIsUnfold)
    self._bIsUnfold = bIsUnfold
    self:RefreshLook()
end

function QuestSeasonTaskHead:SetIsUnLocked(bIsUnLock)
    self._bIsUnLock = bIsUnLock
    self:RefreshLook()    
end

function QuestSeasonTaskHead:SetIsSelected(bIsSelected)
    self._bIsSelected = bIsSelected
    if bIsSelected then
        self._wtTitleText:SetInCheckedStyle(ETextChecked.Checked)
    else
        self._wtTitleText:SetInCheckedStyle(ETextChecked.Unchecked)
    end
    self:RefreshLook()    
end

function QuestSeasonTaskHead:RefreshLook() 
    if self._bIsUnLock ~= nil and self._bIsUnfold ~= nil and self._bIsSelected ~= nil then 
        if self._bIsUnLock then
            if self._bIsUnfold then
                if self._bIsSelected then
                    self:SetType(3)
                else
                    self:SetType(2)
                end
            else
                if self._bIsSelected then
                    self:SetType(1)
                else
                    self:SetType(0)
                end
            end
        else
            if self._bIsUnfold then
                if self._bIsSelected then
                    self:SetType(7)
                else
                    self:SetType(6)
                end
            else
                if self._bIsSelected then
                    self:SetType(5)
                else
                    self:SetType(4)
                end
            end
        end
    end
end

function QuestSeasonTaskHead:_OnQuestStateUpdate(questId)
    if self._lineInfo == nil then
        return
    end
    local groupID = self._lineInfo:GetGroupIDByQuestID(questId)
    if groupID == self._groupId then
        local questInfo = Server.QuestServer:GetQuestInfoById(questId)
        if questInfo.state == QuestState.Rewarded then
            local completedNum = self._lineInfo:GetCompletedQuestNum(groupID)
            local total = self._lineInfo:GetAllQuestIdsByGroupID(groupID)
            self._wtProgressText:SetText(string.format(Module.Quest.Config.Loc.QuestSeasonStarProgress,
            completedNum,
            #total
            ))
            if completedNum == #total then
                self._canPlayAnim = true
            end
        end
    end
end

function QuestSeasonTaskHead:_RegisterReddot()
    if self.curReddotProxy == nil then
        local fCheckReddot = function (id)
            local needRet = Server.QuestServer:GetNotifyQuestInSeasonGroup(id)
            return needRet
        end
        self.curReddotProxy = Module.ReddotTrie:RegisterDynamicReddot(EReddotTrieObserverType.Quest,
            EQuestDynamicDataType.EQuestSeasonReddot,fCheckReddot,self._groupId,self)
    end
end

function QuestSeasonTaskHead:_OnCloseRewardPanel()
    if self._canPlayAnim == true and self._lineInfo then
        local groupInfo = self._lineInfo:GetGroupInfo(self._groupId)
        if groupInfo == nil then
            logerror("OnCloseRewardPanel: invalid groupInfo when play star gain anim")
            return
        end
        local starNum = groupInfo.rewardStar
        Facade.UIManager:RemoveSubUIByParent(self,self._wtAnimPanel)
        local weak, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.QuestSeasonTaskHeadAnim, self._wtAnimPanel, nil, starNum)
        self._wtAnimWidget = getfromweak(weak)
    
        local canvasSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(self._wtAnimWidget)
        if not canvasSlot then return end
        local commonAnchor = FAnchors()
        local commonOffset = FMargin(0, 0, 0, 0)
        commonAnchor.Minimum = LuaGlobalConst.TOP_LEFT_VECTOR
        commonAnchor.Maximum = LuaGlobalConst.BOTTOM_RIGHT_VECTOR
        canvasSlot:SetAnchors(commonAnchor)
        canvasSlot:SetOffsets(commonOffset)

        self._wtAnimWidget:PlayAnim()
        Facade.SoundManager:PlayUIAudioEvent("UI_SeasonMission_Task_Complete")
        Timer.DelayCall(1, function ()
            Facade.SoundManager:PlayUIAudioEvent("UI_SeasonMission_Task_GetStar")
        end)

        self._canPlayAnim = false
    end
end

return QuestSeasonTaskHead