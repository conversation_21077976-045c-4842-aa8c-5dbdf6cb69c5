----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class ActivityMagicTowerMainPanel : LuaUIBaseView
local ActivityMagicTowerMainPanel = ui("ActivityMagicTowerMainPanel")
local ActivityConfig = Module.Activity.Config
local ActivityLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"
local ActivityInputHandler = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityInputHandler"
local ActivityMagicTowerPlayer = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerPlayer"
local ActivityMagicTowerCombatLogic = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerCombatLogic"
local ActivityMagicTowerKeyItem = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerKeyItem"
local ActivityMagicTowerNumericalPanel = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerNumericalPanel"
local ActivityMagicTowerAStarHandler = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerAStarHandler"
-- local ActivityMagicTowerAStarNode = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerAStarNode"
local ConfigManager = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerConfigManager"
local MTSafeManager = require "DFM.Business.Module.ActivityModule.UI.MagicTower.Safe.MTSafeManager"
local MTSafeItem = require "DFM.Business.Module.ActivityModule.UI.MagicTower.Safe.MTSafeItem"

local FHitResult = import "HitResult"
local HitResult = FHitResult()

local function testLog(...)
    loginfo("[Magic_Tower] ", ...)
end

local function testWarningLog(...)
    logwarning("[Magic_Tower] ", ...)
end

local function testErrorLog(...)
    logerror("[Magic_Tower] ", ...)
end

local MoveDirection = {
    [1] = "MoveUp",
    [2] = "MoveDown",
    [3] = "MoveLeft",
    [4] = "MoveRight",
    [5] = "StopMovement"
}

local SpecialPointType = {
    HERO_SPAWN = 1,          -- 主角出生点
    NEWBIE_SPAWN = 2,        -- 新手主角出生点
    FREE_EXIT = 3,           -- 无条件撤离点
    PAY_EXIT = 4,            -- 丢钱撤离点
    LEVER = 5,               -- 拉闸点
    LEVER_LOCKED_EXIT = 6,   -- 需拉闸撤离点
    TELEPORT = 7,            -- 联通传送点
    DIALOGUE = 8,            -- 纯对话点
    WALL = 9,                -- 墙
    SPAWN = 10,              -- 生成点
    DOOR = 11,               -- 门
}

function ActivityMagicTowerMainPanel:Ctor()
    self._activityID = 0
    self._activityInfo = {}

    self._curLevel = {
        maxLengthX = 1,
        maxLengthY = 1,
        x = 1,
        y = 1,
    }

    self._mapActors = {}

    -- 根面板
    self._wtCanvasPage = self:Wnd("CanvasPanel_0", UIWidgetBase)
    -- 关卡名字
    self._wtLevelNameTxt = self:Wnd("DFTextBlock_42", UITextBlock)
    -- 关卡名字后缀
    self._wtLevelSubNameTxt = self:Wnd("DFTextBlock_160", UITextBlock)
    -- 预测战斗结果
    self._wtCombatResultSwitch = self:Wnd("WBP_CommonSwitch", UIWidgetBase)
    self._wtCombatResultCheckBox = self._wtCombatResultSwitch:Wnd("wtCheckBoxSwitch", UICheckBox)
    self._wtCombatResultCheckBox:SetCallback(self.OnCombatResultStateChanged, self)
    -- 快速战斗
    self._wtQuickCombatSwitch = self:Wnd("WBP_CommonSwitch_1", UIWidgetBase)
    self._wtQuickCombatCheckBox = self._wtQuickCombatSwitch:Wnd("wtCheckBoxSwitch", UICheckBox)
    self._wtQuickCombatCheckBox:SetCallback(self.OnQuickCombatStateChanged, self)
    -- 查看键
    self._wtCheckBtn = self:Wnd("WBP_CommonIconButton", DFCommonCheckButtonOnly)
    self._wtCheckBtn:Event("OnCheckedBoxStateChangedNative", self.OnCheckBtnClicked, self)
    -- 商人键
    self._wtMerchantBtn = self:Wnd("WBP_CommonIconButton_1", DFCommonButtonOnly)
    self._wtMerchantBtn:Event("OnClicked", self.OnMerchantBtnClicked, self)

    -- 主角信息框
    self._wtHeroNumericalPanel = self:Wnd("WBP_MorgenGame_NumericalPanel_C_1", ActivityMagicTowerNumericalPanel)
    -- 敌人信息框
    self._wtEnemyNumericalPanel = self:Wnd("WBP_MorgenGame_NumericalPanel_C", ActivityMagicTowerNumericalPanel)
    -- 钥匙卡槽
    self._wtKeyItem = self:Wnd("WBP_MorgenGame_KeyItem", ActivityMagicTowerKeyItem)

    -- 经验
    self._wtTopBar1 = self:Wnd("WBP_Common_TopBarBtn", UIWidgetBase)
    self._wtTopBar1Img = self._wtTopBar1:Wnd("Image_Currency", UIImage)
    self._wtTopBar1Txt = self._wtTopBar1:Wnd("wtTextBlock_value", UITextBlock)
    self._wtTopBar1:Wnd("Button_44", UIWidgetBase):Collapsed()
    -- 钥匙
    self._wtTopBar2 = self:Wnd("WBP_Common_TopBarBtn_148", UIWidgetBase)
    self._wtTopBar2Img = self._wtTopBar2:Wnd("Image_Currency", UIImage)
    self._wtTopBar2Txt = self._wtTopBar2:Wnd("wtTextBlock_value", UITextBlock)
    self._wtTopBar2:Wnd("Button_44", UIWidgetBase):Collapsed()
    -- 龙门币
    self._wtTopBar3 = self:Wnd("WBP_Common_TopBarBtn_1", UIWidgetBase)
    self._wtTopBar3Img = self._wtTopBar3:Wnd("Image_Currency", UIImage)
    self._wtTopBar3Txt = self._wtTopBar3:Wnd("wtTextBlock_value", UITextBlock)
    self._wtTopBar3:Wnd("Button_44", UIWidgetBase):Collapsed()

    self._isMoving = false
    self._navPath = {}

    --- 保险箱管理器
    self.mSafeManager = MTSafeManager:NewIns()

    -- self._LevelInfoReady = false
end

-----------------------------------------------------生命周期-----------------------------------------------------
function ActivityMagicTowerMainPanel:OnOpen()
    -- Facade.LevelLoadManager:AsyncOperateStreamLevelsBySubStage(ESubStage.MagicTower, true, nil, nil, false, 30)
end

function ActivityMagicTowerMainPanel:_InitEvent()
    self:AddLuaEvent(MTSafeItem.evtItemPickup, self._ApplyAttributeChange, self)    -- 保险箱物品拾取触发属性变化
    self:AddLuaEvent(LuaGlobalEvents.evtSceneLoaded,self.OnSceneSubLevelLoaded, self)
    self:AddLuaEvent(ActivityConfig.evtMagicTowerMoveReq, self.OnProcessCharacterMovement, self)
    self:AddLuaEvent(ActivityConfig.evtMagicTowerMoveFinish, self.OnMagicTowerMoveFinish, self)
    self:AddLuaEvent(ActivityConfig.evtMagicTowerSwitchLevel, self.OnProcessSwitchLevel, self)
    self:AddLuaEvent(ActivityConfig.evtProcessMagicTowerAstarPath, self.OnProcessMagicTowerAstarPath, self)
    self:AddLuaEvent(ActivityConfig.evtMagicTowerTileMapClickInfo, self.OnProcessMagicTowerAstarPath, self)
    self:AddLuaEvent(ActivityConfig.evtMagicTowerOpenSafePanel, self.OpenSafePanelWithSafeId, self)
end

function ActivityMagicTowerMainPanel:OnInitExtraData(activityID)
    self._activityID = activityID
end

function ActivityMagicTowerMainPanel:OnShowBegin()
    self:BindBackAction()
    self:_InitEvent()

    self:_InitData()

    ActivityInputHandler.EnableInput()
    ActivityInputHandler.Init(Facade.UIManager:GetInputMonitor(), CreateCallBack(self._HandleMovement, self))
end

function ActivityMagicTowerMainPanel:OnHideBegin()
    self:RemoveAllActions()
    self:RemoveAllLuaEvent()

    ActivityInputHandler.Destroy()
end

function ActivityMagicTowerMainPanel:OnClose()
    ActivityInputHandler.Destroy()
    self.mSafeManager:Release()
    self:_DestroyAllMapActors()
end

-----------------------------------------------------输入处理-----------------------------------------------------
function ActivityMagicTowerMainPanel:_HandleMovement(funcName)
    local dx, dy = 0, 0
    
    if funcName == MoveDirection[1] then
        dy = -1
    elseif funcName == MoveDirection[2] then
        dy = 1
    elseif funcName == MoveDirection[3] then
        dx = -1
    elseif funcName == MoveDirection[4] then
        dx = 1
    else
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, funcName)
        return
    end

    local targetX = self._curLevel.x + dx
    local targetY = self._curLevel.y + dy
    
    if targetX < 1 or targetX > self._curLevel.maxLengthX or
       targetY < 1 or targetY > self._curLevel.maxLengthY then
        return
    end

    local canMove = self:_CheckTargetGridMovable(targetX, targetY)
    if not canMove then
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, MoveDirection[5])
        return
    end
    
    self._curLevel.x = targetX
    self._curLevel.y = targetY
    testLog(string.format("MainPanel _HandleMovement self._curLevel.x = %s, self._curLevel.y = %s", self._curLevel.x, self._curLevel.y))
    
    self._isMoving = true
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, funcName)
    
    self:UpdatePositionUI()

    self:_CheckSpecialPoint(targetX, targetY)
end

function ActivityMagicTowerMainPanel:UpdatePositionUI()

end

function ActivityMagicTowerMainPanel:OnNavBack()
    Facade.UIManager:CloseUI(self)
end

function ActivityMagicTowerMainPanel:BindBackAction()
    if self._backActionHandle then
        self:RemoveInputActionBinding(self._backActionHandle)
        self._backActionHandle = nil
    end

    self._backActionHandle = self:AddInputActionBinding(
        "Back",
        EInputEvent.IE_Pressed,
        self.OnNavBack,
        self,
        EDisplayInputActionPriority.UI_Pop
    )
end

function ActivityMagicTowerMainPanel:RemoveAllActions()
    if self._backActionHandle then
        self:RemoveInputActionBinding(self._backActionHandle)
        self._backActionHandle = nil
    end
end

-----------------------------------------------------数据区-----------------------------------------------------
function ActivityMagicTowerMainPanel:_InitData()
    -- local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(self._activityID)
    -- if activityInfo == nil or next(activityInfo) == nil then return end

    -- 所有数据先从本地表获取
    self:_InitDataConfig()

    -- 创建玩家实例
    self:_InitPlayer()
end

function ActivityMagicTowerMainPanel:_InitPlayer()
    local heroID, heroConfig = next(self._heroConfig or {})
    if not heroConfig then
        testErrorLog("_InitPlayer Not find heroConfig!")
        return
    end
    
    local initConfig = {
        health = heroConfig.health,
        maxHealth = heroConfig.health,
        attack = heroConfig.attack,
        defense = heroConfig.defense,
        hitProb = heroConfig.hitProb,
        avatarAsset = heroConfig.avatarAsset
    }

    -- TODO：待改成读取后台数据
    local currencyConfig = {
        exp = 0,
        keys = 1,
        money = 2
    }

    self._player = ActivityMagicTowerPlayer.New(initConfig, currencyConfig)
    
    local healthChangedCallBack = CreateCallBack(self._HandleHealthChanged, self)
    self._player:SetHealthChangedCallback(healthChangedCallBack)
    
    local deathCallBack = CreateCallBack(self._HandlePlayerDeath, self)
    self._player:SetDeathCallback(deathCallBack) 
end

function ActivityMagicTowerMainPanel:_InitLevelStructData(inLevelId, levelX, levelY)
    self._curLevelID = inLevelId
    self._curLevelInfo = self._levelConfig[self._curLevelID] or {}
    self._curLevelStructID = self._curLevelInfo.levelStructure or 0

    -- 设置关卡网格数组
    self:_SetGirdList()
    -- self:_SetDebugGirdList()

    -- 预处理生成概型
    self:_PreprocessSpawnItem()

    -- 设置出生点
    self:_SetSpawnPosition(levelX, levelY)

    -- 设置关卡场景相关信息
    self:_InitMapInfo()

    -- 刷新HUD层
    self:RefreshUI()
end

function ActivityMagicTowerMainPanel:_PreprocessSpawnItem()
    self._spawnedItems = {}  ---@type table<int, table<int, SpecialPointID>>
    self._itemInteractionCounts = {} ---@type table<int, int|nil>

    for x, row in ipairs(self._levelGridList or {}) do
        for y, specialPointID in ipairs(row or {}) do
            if specialPointID ~= 0 then
                local config = self:_FindSpecialPointConfig(specialPointID)
                if config and config.specialPointType == SpecialPointType.SPAWN then
                    local itemID = self:_GenerateSpawnItem(config.spawnProb)
                    if itemID then
                        local direction = config.direction or 1 -- 默认左朝向
                        self._spawnedItems[x] = self._spawnedItems[x] or {}
                        self._spawnedItems[x][y] = itemID
                        self:_CreateMapItemVisual(x, y, itemID, direction)

                        -- 初始化交互次数
                        local itemConfig = self:_FindItemConfig(itemID)
                        if itemConfig then
                            self._itemInteractionCounts[itemID] = itemConfig.disappearTime ~= 0 and itemConfig.disappearTime or nil
                        end
                    end
                end
            end
        end
    end
end

function ActivityMagicTowerMainPanel:_GenerateSpawnItem(spawnProbID)
    if not spawnProbID then return end

    local probGroup = {}
    for _, config in ipairs(self._levelSpawnProbConfig) do
        if config.spawnProbID == spawnProbID then
            table.insert(probGroup, config)
        end
    end

    local selected = ActivityLogic.SelectByWeight(probGroup, "weight")
    return selected and selected.result
end

function ActivityMagicTowerMainPanel:_SetGirdList()
    self._curLevel.maxLengthX = self._curLevelInfo.levelSizeHorizontal or 13
    self._curLevel.maxLengthY = self._curLevelInfo.levelSizeVertical or 13

    self._levelGridList = {} ---@type table<int, table<int, SpecialPointID>>

    for i = 1, self._curLevel.maxLengthX do
        self._levelGridList[i] = {}
        for j = 1, self._curLevel.maxLengthY do
            self._levelGridList[i][j] = 0
        end
    end

    local levelStructureData = self._levelStructureConfig[self._curLevelStructID]
    if levelStructureData then
        for _, structure in ipairs(levelStructureData) do
            local posX = structure.coordinateX or 0
            local posY = structure.coordinateY or 0
            local specialPoint = structure.specialPoint or 0
        
            if posX >= 1 and posX <= self._curLevel.maxLengthX and posY >= 1 and posY <= self._curLevel.maxLengthY then
                self._levelGridList[posX][posY] = specialPoint
            end
        end
    end
end

function ActivityMagicTowerMainPanel:_SetDebugGirdList()
    if not self._levelGridList then
        self:_SetGirdList(13, 13)
    end

    local wallPoints = {
        {1,1}, {1,5}, {1,6}, {1,7}, {1,8}, {1,9}, {1,10}, {1,11}, {1,12}, {1,13},
        {2,3}, {2,5}, {2,7}, {2,8}, {2,9}, {2,13},
        {3,1}, {3,2}, {3,3},
        {4,1}, {4,5}, {4,11}, {4,13},
        {5,1}, {5,2}, {5,3}, {5,4}, {5,5}, {5,8}, {5,11}, {5,13},
        {6,8}, {6,13},
        {7,1}, {7,2}, {7,3}, {7,4}, {7,5}, {7,6}, {7,8}, {7,11}, {7,13},
        {8,1}, {8,2}, {8,3}, {8,4}, {8,5}, {8,6}, {8,11}, {8,13},
        {9,1}, {9,2}, {9,3}, {9,4}, {9,5}, {9,6}, {9,8}, {9,11}, {9,13},
        {10,6}, {10,8}, {10,13},
        {11,1}, {11,3}, {11,4}, {11,6}, {11,8}, {11,11}, {11,13},
        {12,3}, {12,4}, {12,6}, {12,11}, {12,13},
        {13,2}, {13,3}, {13,4}, {13,8}, {13,10}, {13,11}, {13,13}
    }

    local WALL_POINT_ID = 2030901

    for _, coord in ipairs(wallPoints) do
        local x, y = coord[1], coord[2]
        if x >= 1 and x <= 13 and y >= 1 and y <= 13 then
            self._levelGridList[x][y] = WALL_POINT_ID
        end
    end

    self._levelGridList[3][7] = SpecialPointType.DIALOGUE
end

function ActivityMagicTowerMainPanel:_SetSpawnPosition(levelX, levelY)
    if levelX and levelY then
        self._curLevel.x = levelX
        self._curLevel.y = levelY
    end

    local targetSpawnType = SpecialPointType.HERO_SPAWN

    if not self._isNewPlayerSpawnPoint then -- self._isNewPlayerSpawnPoint 需要记录在后台
        targetSpawnType = SpecialPointType.NEWBIE_SPAWN
        self._isNewPlayerSpawnPoint = true
    end

    for x, row in ipairs(self._levelGridList or {}) do
        for y, specialPointID in ipairs(row or {}) do
            if specialPointID ~= 0 then
                local config = self:_FindSpecialPointConfig(specialPointID)
                if config and config.specialPointType == targetSpawnType then
                    self._curLevel.x = x
                    self._curLevel.y = y
                    return
                end
            end
        end
    end

    -- 使用默认值
    -- self._curLevel.x = 3
    -- self._curLevel.y = 6
end

function ActivityMagicTowerMainPanel:_InitDataConfig()
    -- 关卡配置 ---@type table<int64, MorgenTowerLevelConfig>
    self._levelConfig = ConfigManager.GetLevelConfigTable()

    -- 关卡结构配置 ---@type table<int64, MorgenTowerLevelStructureConfig>
    self._levelStructureConfig = ConfigManager.GetLevelStructureConfigTable()

    -- 特殊点配置 ---@type table<int64, MorgenTowerLevelSpecialPointConfig>
    self._specialPointConfig = ConfigManager.GetSpecialPointConfigTable()

    -- 联通关系配置 ---@type table<int64, MorgenTowerLevelTransTypeConfig>
    self._levelTransTypeConfig = ConfigManager.GetLevelTransTypeConfigTable()

    -- Spawn概型配置 ---@type table<int64, MorgenTowerLevelSpawnProbConfig>
    self._levelSpawnProbConfig = ConfigManager.GetLevelSpawnProbConfigTable()

    -- 道具配置 ---@type table<int64, MorgenTowerPropConfig>
    self._propConfig = ConfigManager.GetPropConfigTable()

    -- 怪物配置 ---@type table<int64, MorgenTowerEnemyConfig>
    self._enemyConfig = ConfigManager.GetEnemyConfigTable()

    -- NPC配置 ---@type table<int64, MorgenTowerNPCConfig>
    self._npcConfig = ConfigManager.GetNPCConfigTable()

    -- 主角配置 ---@type table<int64, MorgenTowerHeroConfig>
    self._heroConfig = ConfigManager.GetHeroConfigTable()

    -- 保险柜配置 ---@type table<int64, MorgenTowerTreasureConfig>
    self._treasureConfig = ConfigManager.GetTreasureConfigTable()
    
    -- 保险物品配置 ---@type table<int64, MorgenTowerTreasureItemConfig>
    self._treasureItemConfig = ConfigManager.GetTreasureItemConfigTable()
    
    -- 保险概型配置 ---@type table<int64, MorgenTowerTreasureProbConfig>
    self._treasureProbConfig = ConfigManager.GetTreasureProbConfigTable()

    -- 小对话配置 ---@type table<int64, MorgenTowerSmallDialogueConfig>
    self._smallDialogueConfig = ConfigManager.GetSmallDialogueConfigTable()

    -- 小对话台词配置 ---@type table<int64, MorgenTowerSmallDialogueLineConfig>
    self._smallDialogueLineConfig = ConfigManager.GetSmallDialogueLineConfigTable()
    
    -- 大对话配置 ---@type table<int64, MorgenTowerDialogueConfig>
    self._dialogueConfig = ConfigManager.GetDialogueConfigTable()

    -- 对话组配置 ---@type table<int64, MorgenTowerDialogueGroupConfig>
    self._dialogueGroupConfig = ConfigManager.GetDialogueGroupConfigTable()

    -- 台词配置 ---@type table<int64, MorgenTowerDialogueLineConfig>
    self._dialogueLineConfig = ConfigManager.GetDialogueLineConfigTable()

    -- 回答选项配置 ---@type table<int64, MorgenTowerAnswerBranchConfig>
    self._answerBranchConfig = ConfigManager.GetAnswerBranchConfigTable()

    -- png资源配置 ---@type table<int64, MorgenTowerAssetConfig>
    self._assetConfig = ConfigManager.GetAssetConfigTable()

    -- spine动画配置 ---@type table<int64, MorgenTowerSpineConfig>
    self._spineConfig = ConfigManager.GetSpineConfigTable()

    -- 属性变化配置 ---@type table<int64, MorgenTowerAttributeChangeConfig>
    self._attributeChangeConfig = ConfigManager.GetAttributeChangeConfigTable()

    -- 死亡台词配置 ---@type table<int64, MorgenTowerDeathLinesConfig>
    self._deathLinesConfig = ConfigManager.GetDeathLinesConfigTable()

    -- 转场文字配置 ---@type table<int64, MorgenTowerCutSceneConfig>
    self._cutSceneConfig = ConfigManager.GetCutSceneConfigTable()
    
    -- 其它常量配置 ---@type table<int64, MorgenTowerConstantConfig>
    self._constantConfig = ConfigManager.GetConstantConfigTable()

    -- 条件配置 ---@type table<int64, MorgenTowerConditionConfig>
    self._conditionConfig = ConfigManager.GetConditionConfigTable()

    -- 子条件配置 ---@type table<int64, MorgenTowerSubConditionConfig>
    self._subConditionConfig = ConfigManager.GetSubConditionConfigTable()

    self:_InitMerchantNPCConfig()
end

function ActivityMagicTowerMainPanel:_InitMerchantNPCConfig()
    self._merchantNPCConfig = {}
    self._shopNPCUnlockConfig = {}

    for dialogueID, config in pairs(self._dialogueConfig) do
        local slot = config.shopSLot
        if config.shopNPC and config.shopNPC ~= 0 and slot and slot ~= 0 and not self._merchantNPCConfig[slot] then
            self._merchantNPCConfig[slot] = config
            -- 初始化商人解锁状态
            local npcID = config.shopNPC
            self._shopNPCUnlockConfig[npcID] = false
        end
    end
end

function ActivityMagicTowerMainPanel:_FindSpecialPointConfig(specialPointID)
    if self._specialPointConfig[specialPointID] then
        return self._specialPointConfig[specialPointID]
    end

    return nil
end

function ActivityMagicTowerMainPanel:_FindTransTypeConfig(transTypeID)
    local transTypeConfig = self._levelTransTypeConfig[transTypeID]
    if transTypeConfig then
        return transTypeConfig
    end

    return nil
end

function ActivityMagicTowerMainPanel:_FindItemConfig(itemID)
    return self:_FindPropConfig(itemID) or self:_FindEnemyConfig(itemID) or self:_FindNPCConfig(itemID)
end

function ActivityMagicTowerMainPanel:_FindPropConfig(propID)
    local propConfig = self._propConfig[propID]
    if propConfig then
        return propConfig
    end
    
    return nil
end

function ActivityMagicTowerMainPanel:_FindAttributeChangeConfig(attributeChangeID)
    local attributeChangeConfig = self._attributeChangeConfig[attributeChangeID]
    if attributeChangeConfig then
        return attributeChangeConfig
    end
    
    return nil
end

function ActivityMagicTowerMainPanel:_FindEnemyConfig(enemyID)
    local enemyConfig = self._enemyConfig[enemyID]
    if enemyConfig then
        return enemyConfig
    end
    
    return nil
end

function ActivityMagicTowerMainPanel:_FindNPCConfig(enemyID)
    local npcConfig = self._npcConfig[enemyID]
    if npcConfig then
        return npcConfig
    end
    
    return nil
end

function ActivityMagicTowerMainPanel:_FindSpineConfig(spineID)
    local spineConfig = self._spineConfig[spineID]
    if spineConfig then
        return spineConfig
    end

    return nil
end

function ActivityMagicTowerMainPanel:_OnDataChanged()
    self:_InitData()
    self:RefreshUI()
end

function ActivityMagicTowerMainPanel:OnProcessCharacterMovement(moveDirection)
    if not moveDirection then return end

    local moveFuncName = MoveDirection[tonumber(moveDirection)]
    if moveFuncName then
        ActivityInputHandler.OnProcessCharacterMovement(moveFuncName)
    end
end

function ActivityMagicTowerMainPanel:OnMagicTowerMoveFinish()
    self._isMoving = false

    if self._navPath and #self._navPath > 0 then
        local node = table.remove(self._navPath, 1)
        self:OnProcessNavNode(node)

        testLog("MainPanel OnMagicTowerMoveFinish:", node.x, " ",  node.y)
    end
end

function ActivityMagicTowerMainPanel:OnProcessSwitchLevel(inLevelId)
    self:_InitLevelStructData(inLevelId, nil, nil)
end

function ActivityMagicTowerMainPanel:OnProcessMagicTowerAstarPath(x, y)
    testLog("OnProcessMagicTowerAstarPath x:", x, " y:", y)

    if self._isChecked then
        self:_ShowEnemyDetailInfos(x, y)
        return
    end

    if not ActivityInputHandler.IsInputEnabled() then return end

    local curLevelX = self._curLevel.x
    local curLevelY = self._curLevel.y

    local newNode = {
        x = x, y = y,
        g = 0, h = 0, f = 0,
        parent = nil
    }

    local curNode = {
        x = curLevelX, y = curLevelY,
        g = 0, h = 0, f = 0,
        parent = nil
    }
    self._navPath = ActivityMagicTowerAStarHandler.aStar(self._levelGridList, curNode, newNode)

    local AStarNodes = {}
    if self._navPath then
        print("找到路径：")
        for i, node in ipairs(self._navPath) do
            table.insert(AStarNodes, FVector2D(node.x - 1, node.y - 1))
            print("节点：".. i..": ("..node.x..", "..node.y..")")
        end
    else
        print("未找到路径")
    end
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, "ShowDebugAStarInfo", AStarNodes)

    if not self._isMoving then 
        if self._navPath and #self._navPath > 0 then
            local node = table.remove(self._navPath, 1)
            self:OnProcessNavNode(node)
        end        
    end
end

function ActivityMagicTowerMainPanel:OnProcessNavNode(node)
    if node.x ~= self._curLevel.x then
        if node.x > self._curLevel.x then
            self:_HandleMovement(MoveDirection[4])
        else
            self:_HandleMovement(MoveDirection[3])
        end
    elseif node.y ~= self._curLevel.y then
        if node.y > self._curLevel.y then
            self:_HandleMovement(MoveDirection[2])
        else
            self:_HandleMovement(MoveDirection[1])
        end
    else
        self:_HandleMovement(MoveDirection[5])
    end
end

function ActivityMagicTowerMainPanel:OnSceneSubLevelLoaded(curSubStageType)
    if curSubStageType ~= ESubStage.MagicTower then
        return
    end
    self._mapResLoaded = true
    ---应该找到出生点
    local levelId, PosX, PosY = ConfigManager.GetPlayerSpawnPointLevelAndPos()
    levelId = 1010001
    self:_InitLevelStructData(levelId, PosX, PosY)
end

function ActivityMagicTowerMainPanel:_InitMapInfo()
    if not self._mapResLoaded then return end

    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, "InitMapParams", self._curLevel.maxLengthX, self._curLevel.maxLengthY, ActivityConfig.MogenTower.StepLength)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, "InitCameraViewLimit", 5, 3)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, "InitPlayerAndCameraSpawnPoint", self._curLevel.x - 1, self._curLevel.y - 1)

    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, "SwitchTileMapByMapId", self._curLevelID)

    -- 创建调试网格
    if IsInEditor() then
        local girdvec = {}
        local levelStructureData = self._levelStructureConfig[self._curLevelStructID]
        if levelStructureData then
            for _, structure in ipairs(levelStructureData) do
                local posX = structure.coordinateX - 1 or 0
                local posY = structure.coordinateY - 1 or 0
                if structure.specialPoint == 2030901 then -- debug 输出墙的位置
                    table.insert(girdvec, FVector2D(posX, posY))
                end
            end
        end

        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, "ShowDebugGirdInfo", girdvec)
    end
end

-----------------------------------------------------核心逻辑-----------------------------------------------------
function ActivityMagicTowerMainPanel:_CheckTargetGridMovable(targetX, targetY)
    local specialPointID = self._levelGridList[targetX][targetY]
    if specialPointID == 0 then return true end

    local specialPointConfig = self:_FindSpecialPointConfig(specialPointID)
    if not specialPointConfig then return true end
    
    if specialPointConfig.specialPointType == SpecialPointType.WALL then
        testWarningLog(string.format("MainPanel _CheckTargetGridMovable Wall Block! targetX = %s, targetY = %s", targetX, targetY))
        return false
    end

    local itemID = self._spawnedItems[targetX] and self._spawnedItems[targetX][targetY]
    if not itemID then return true end

    local itemType = self:_ParseItemTypeByID(tostring(itemID))
    if itemType == "enemy" then
        self:_HandleEnemyItem(itemID, targetX, targetY)
        return false
    elseif itemType == "npc" then
        self:_HandleNPCItem(itemID, targetX, targetY)
        return false
    elseif itemType == "prop" then
        self:_PreHandlePropItem(itemID)
    end
    
    return true
end

function ActivityMagicTowerMainPanel:_CheckSpecialPoint(targetX, targetY)
    local specialPointID = self._levelGridList[targetX][targetY]
    if not specialPointID or specialPointID == 0 then return end
    
    local specialPointConfig = self:_FindSpecialPointConfig(specialPointID)
    if not specialPointConfig then return end
    
    if specialPointConfig.specialPointType == SpecialPointType.SPAWN then
        self:_HandleSpawnPoint(targetX, targetY)
    elseif specialPointConfig.specialPointType == SpecialPointType.DIALOGUE then
        self:_HandleDialoguePoint(specialPointConfig, targetX, targetY)
    elseif specialPointConfig.specialPointType == SpecialPointType.TELEPORT then
        self:_HandleTeleportPoint(specialPointConfig)
    end
end

-- 只处理数据层销毁
function ActivityMagicTowerMainPanel:_RemoveSpecialPointInGrid(x, y)
    if not x or not y then return end

    self._levelGridList[x][y] = 0
    self._spawnedItems[x][y] = nil
end

function ActivityMagicTowerMainPanel:_HandleDialoguePoint(specialPointConfig, targetX, targetY)
    local dialogueID = specialPointConfig.dialogue
    if not dialogueID then return end

    ActivityInputHandler.DisableInput()
    
    self:_RemoveSpecialPointInGrid(targetX, targetY)

    -- TODO：还要去大对话表做条件检测
    self:OpenDialoguePanel(dialogueID)
end

function ActivityMagicTowerMainPanel:_HandleTeleportPoint(specialPointConfig)
    local transType = specialPointConfig.transType
    if not transType then return end

    local transTypeConfig = self:_FindTransTypeConfig(transType)
    if not transTypeConfig then return end

    self:_InitLevelStructData(transTypeConfig.targetLevel, transTypeConfig.targetX, transTypeConfig.targetY)
end

function ActivityMagicTowerMainPanel:_HandleSpawnPoint(targetX, targetY)
    local itemID = self._spawnedItems[targetX] and self._spawnedItems[targetX][targetY]
    if not itemID then return end

    if self:_ParseItemTypeByID(tostring(itemID)) == "prop" then
        self:_HandlePropItem(itemID, targetX, targetY)
    end
end

-- ID前缀解析方法
function ActivityMagicTowerMainPanel:_ParseItemTypeByID(itemID_str)
    if not itemID_str then return end

    local prefixRules = {
        ["301"] = "enemy",
        ["302"] = "npc",
        ["303"] = "hero",
        ["304"] = "prop",
    }

    local prefix = string.sub(tostring(itemID_str), 1, 3)
    return prefixRules[prefix]
end

function ActivityMagicTowerMainPanel:_PreHandlePropItem(itemID)
    local propConfig = self:_FindPropConfig(itemID)
    if not propConfig then return end

    self:_HandleItemInteraction(propConfig)
end

function ActivityMagicTowerMainPanel:_HandlePropItem(itemID, targetX, targetY)
    local propConfig = self:_FindPropConfig(itemID)
    if not propConfig then return end

    self:_ApplyAttributeChange(propConfig.attributeChange)
    self:_HandleItemDisappear(itemID, targetX, targetY, propConfig)
end

-- 属性变更方法
function ActivityMagicTowerMainPanel:_ApplyAttributeChange(attributeChangeID)
    if not self._player then return end
    if not attributeChangeID or attributeChangeID == 0 then return end

    local attributeChangeConfig = self:_FindAttributeChangeConfig(attributeChangeID)
    if not attributeChangeConfig then return end

    local modifiers = {
        ["ModifyHealth" ] = { way = attributeChangeConfig.healthChangeWay , value = attributeChangeConfig.healthChangeValue  },
        ["ModifyAttack" ] = { way = attributeChangeConfig.attackChangeWay , value = attributeChangeConfig.attackChangeValue  },
        ["ModifyDefense"] = { way = attributeChangeConfig.defenseChangeWay, value = attributeChangeConfig.defenseChangeValue },
        ["ModifyHitProb"] = { way = attributeChangeConfig.hitProbChangeWay, value = attributeChangeConfig.hitProbChangeValue },
        ["ModifyExp"    ] = { way = attributeChangeConfig.expChangeWay    , value = attributeChangeConfig.expChangeValue     },
        ["ModifyKeys"   ] = { way = attributeChangeConfig.keyChangeWay    , value = attributeChangeConfig.keyChangeValue     },
        ["ModifyMoney"  ] = { way = attributeChangeConfig.moneyChangeWay  , value = attributeChangeConfig.moneyChangeValue   },
    }

    local anyChanged = false

    for methodName, modifier in pairs(modifiers) do
        if modifier.way and modifier.way ~= 0 and modifier.value then
            anyChanged = self._player[methodName](self._player, modifier.value, modifier.way) or anyChanged
        end
    end

    if anyChanged then
        self:_RefreshPlayerUI()
    
        -- 重置变更标记
        self._player:ResetLastChangeTypes()
    end

    -- 开启了预测战斗才会实时更新，节省部分性能
    if self._isPredictCombat then
        self:_UpdateCombatPredictResult()
    end
end

function ActivityMagicTowerMainPanel:_HandleEnemyItem(itemID, targetX, targetY)
    local enemyConfig = self:_FindEnemyConfig(itemID)
    if not enemyConfig then return end

    if enemyConfig.dialogue and enemyConfig.dialogue ~= 0 then
        self:_ProcessPreCombatDialogue(enemyConfig, targetX, targetY)
    else
        self:_ProcessCombatConfirmation(enemyConfig, targetX, targetY)
    end
end

-- 处理战斗前对话
function ActivityMagicTowerMainPanel:_ProcessPreCombatDialogue(enemyConfig, targetX, targetY)
    local finishCallBack = CreateCallBack(function()
        if not self._isCombatCancel then
            self:_ProcessCombatConfirmation(enemyConfig, targetX, targetY)
        else
            self._isCombatCancel = nil
            ActivityInputHandler.EnableInput()
        end
    end, self)

    self:OpenDialoguePanel(enemyConfig.dialogue, finishCallBack)
end

-- 处理战斗二次确认
function ActivityMagicTowerMainPanel:_ProcessCombatConfirmation(enemyConfig, targetX, targetY)
    if enemyConfig.isCompulsoryConfirming == 1 and enemyConfig.confirmingText and enemyConfig.confirmingText ~= "" then
        self:_ShowCombatConfirm(enemyConfig, targetX, targetY)
    else
        self:_CheckPredictionAndStartCombat(enemyConfig, targetX, targetY)
    end
end

-- 检查预测并开始战斗
function ActivityMagicTowerMainPanel:_CheckPredictionAndStartCombat(enemyConfig, targetX, targetY)
    if self._isPredictCombat and self._enemyPredictions then
        local prediction = self._enemyPredictions[enemyConfig.enemyID or 0]
        local confirmType = ActivityMagicTowerCombatLogic._GetCombatResultType(prediction, self._player:GetStatusSnapshot(), enemyConfig)
        if confirmType and confirmType ~= 0 then
            self:_ShowPredictionConfirmDialog(confirmType, enemyConfig, targetX, targetY)
        else
            self:_StartCombat(enemyConfig, targetX, targetY)
        end
    else
        self:_StartCombat(enemyConfig, targetX, targetY)
    end
end

-- 显示预测二次确认弹窗
---@param confirmType number  弹窗类型（1: 高度不确定 2： 必然失败）
function ActivityMagicTowerMainPanel:_ShowPredictionConfirmDialog(confirmType, enemyConfig, targetX, targetY)
    local confirmTxt = ""
    if confirmType == 1 then
        confirmTxt = ActivityConfig.Loc.MagicTowerText[6]
    elseif confirmType == 2 then
        confirmTxt = ActivityConfig.Loc.MagicTowerText[1]
    else
        return
    end

    local function fOnConfirmCallback()
        self:_StartCombat(enemyConfig, targetX, targetY)
    end

    local function fOnCancelCallback()
        self._isCombatCancel = nil
        ActivityInputHandler.EnableInput()
    end

    self:ShowConfirmWindow(confirmTxt, fOnConfirmCallback, fOnCancelCallback, ActivityConfig.Loc.Cancel, ActivityConfig.Loc.Confirm)
end

-- 显示战斗二次确认弹窗
function ActivityMagicTowerMainPanel:_ShowCombatConfirm(enemyConfig, targetX, targetY)
    local function fOnConfirmCallback()
        self:_CheckPredictionAndStartCombat(enemyConfig, targetX, targetY)
    end

    local function fOnCancelCallback()
        self._isCombatCancel = nil
        ActivityInputHandler.EnableInput()
    end

    local confirmTxt = enemyConfig.confirmingText
    if not confirmTxt or confirmTxt == "" then return end

    self:ShowConfirmWindow(confirmTxt, fOnConfirmCallback, fOnCancelCallback, ActivityConfig.Loc.Cancel, ActivityConfig.Loc.Confirm)
end

-- 开始战斗流程
function ActivityMagicTowerMainPanel:_StartCombat(enemyConfig, targetX, targetY)
    ActivityInputHandler.DisableInput()
    self:_ProcessCombatResult(enemyConfig, targetX, targetY)
end

-- 战斗结果处理
function ActivityMagicTowerMainPanel:_ProcessCombatResult(enemyConfig, targetX, targetY)
    local isAutoCombat = self._isAutoCombat and enemyConfig.isCompulsoryAnimation ~= 1
    if isAutoCombat then
        self:_AutoCombat(enemyConfig, targetX, targetY)
    else
        self:_ManualCombat(enemyConfig, targetX, targetY)
    end
end

-- 自动战斗
function ActivityMagicTowerMainPanel:_AutoCombat(enemyConfig, targetX, targetY)
    local battleResult = ActivityMagicTowerCombatLogic.CalculateFullCombatResult(self._player:GetStatusSnapshot(), enemyConfig)
    ActivityMagicTowerCombatLogic.RecordBattleLog(battleResult)
    
    -- 更新玩家数据和相关UI
    self._player:ModifyHealth(battleResult.playerFinalHealth, 4)
    self:_RefreshPlayerUI()
    
    if battleResult.result == "win" then
        self:_OnEnemyDefeated(enemyConfig, targetX, targetY)
    else
        self:_OnPlayerDefeated()
    end
end

-- 手动战斗
function ActivityMagicTowerMainPanel:_ManualCombat(enemyConfig, targetX, targetY)
    local combatCallBack = CreateCallBack(function(ownUI, popUI, result, playerFinalHealth)
        if not result then
            ActivityInputHandler.EnableInput()
            return
        end

        self._player:ModifyHealth(playerFinalHealth, 4)
        self:_RefreshPlayerUI()

        if result == "win" then
            self:_OnEnemyDefeated(enemyConfig, targetX, targetY)
        else
            self:_OnPlayerDefeated()
        end
    end, self)

    Facade.UIManager:AsyncShowUI(UIName2ID.ActivityMagicTowerCombatPanel,
    nil,
    nil,
    self._activityID,
    {enemyAvatarAsset = enemyConfig.avatarAsset, heroAvatarAsset = self._player:GetAvatarAsset()},
    {enemyConfig = enemyConfig, playerData = self._player:GetStatusSnapshot()},
    combatCallBack)
end

-- 敌人被击败处理
function ActivityMagicTowerMainPanel:_OnEnemyDefeated(enemyConfig, targetX, targetY)
    Timer.DelayCall(1.2, function()
        Module.CommonTips:ShowSimpleTip(string.format("真nb啊，击败了%s", tostring(enemyConfig.name))) -- debug
    end)

    if enemyConfig.defeatReward and enemyConfig.defeatReward ~= 0 then
        self:_ApplyAttributeChange(enemyConfig.defeatReward)
    end

    self:_HandleItemDisappear(enemyConfig.enemyID, targetX, targetY, enemyConfig)
    ActivityInputHandler.EnableInput()
end

-- 玩家被击败处理
function ActivityMagicTowerMainPanel:_OnPlayerDefeated()
    Timer.DelayCall(1.2, function()
        Module.CommonTips:ShowSimpleTip("玩家已阵亡，请退出该界面重进!") -- debug
    end)

    -- self._player:TakeDamage(math.huge) -- 触发死亡处理，这里考虑放到 _HandlePlayerDeath 一起处理
    self:_HandlePlayerDeath()
end

function ActivityMagicTowerMainPanel:_HandleNPCItem(itemID, targetX, targetY)
    local npcConfig = self:_FindNPCConfig(itemID)
    if not npcConfig then return end

    if npcConfig.npcID and self._shopNPCUnlockConfig[npcConfig.npcID] ~= nil then
        self._shopNPCUnlockConfig[npcConfig.npcID] = true
    end

    self:_HandleItemInteraction(npcConfig)
    self:_HandleItemDisappear(itemID, targetX, targetY, npcConfig)
end

-- 辅助函数：处理物品消失逻辑
function ActivityMagicTowerMainPanel:_HandleItemDisappear(itemID, targetX, targetY, config)
    if not itemID or not self._itemInteractionCounts[itemID] then return end

    self._itemInteractionCounts[itemID] = self._itemInteractionCounts[itemID] - 1

    -- 显示消失后飘窗
    if config.disappearNote and config.disappearNote ~= "" then
        Module.CommonTips:ShowSimpleTip(config.disappearNote)
    end
    
    -- 从地图移除
    if self._itemInteractionCounts[itemID] <= 0 then
        self:_RemoveSpecialPointInGrid(targetX, targetY)
        self:_DestroyMapItemResource(targetX, targetY, config)
        self._itemInteractionCounts[itemID] = nil
        
        -- 处理消失后对话
        if config.disappearDialogue and config.disappearDialogue ~= 0 then
            ActivityInputHandler.DisableInput()
            self:OpenDialoguePanel(config.disappearDialogue)
        end
    end
end

-- 辅助函数：处理物品交互
function ActivityMagicTowerMainPanel:_HandleItemInteraction(config)
    if config and config.dialogue and config.dialogue ~= 0 then
        ActivityInputHandler.DisableInput()
        self:OpenDialoguePanel(config.dialogue)
    end
end

-- [备用方法] 用来处理一些因生命值变更而产生的延时效果
function ActivityMagicTowerMainPanel:_HandleHealthChanged()
end

function ActivityMagicTowerMainPanel:_HandlePlayerDeath()
    testWarningLog("MainPanel _HandlePlayerDeath")
    ActivityInputHandler.DisableInput()
    -- Facade.UIManager:AsyncShowUI(UIName2ID.XXX, nil, nil, self._activityID)
end

-----------------------------------------------------UI刷新-----------------------------------------------------
function ActivityMagicTowerMainPanel:RefreshUI()
    self:_RefreshLevelInfoPanel()
    self:_RefreshPlayerUI()
end

function ActivityMagicTowerMainPanel:_RefreshPlayerUI()
    self:_RefreshTopBarInfo()
    self:_RefreshNumericalPanel()
end

function ActivityMagicTowerMainPanel:_RefreshTopBarInfo()
    if not self._player then return end

    local sanityTxt = tostring(self._player:GetExp())
    self._wtTopBar1Txt:SetText(sanityTxt)

    local originiteTxt = tostring(self._player:GetKeys())
    self._wtTopBar2Txt:SetText(originiteTxt)

    local lmbTxt = tostring(self._player:GetMoney())
    self._wtTopBar3Txt:SetText(lmbTxt)
end

function ActivityMagicTowerMainPanel:_RefreshLevelInfoPanel()
    if self._curLevelInfo then
        self._wtLevelNameTxt:SetText(self._curLevelInfo.levelName or "")
        self._wtLevelSubNameTxt:SetText(self._curLevelInfo.levelSubName or "")
    end
end

function ActivityMagicTowerMainPanel:_RefreshNumericalPanel()
    if not self._player then return end
    
    local status = self._player:GetStatusSnapshot()
    local lastChanges = self._player:GetLastChangeTypes()

    local heroStauts = {
        hp = status.health or 0,
        atk = status.attack or 0,
        def = status.defense or 0,
        crit = status.hitProb or 0
    }

    local changes = {
        hp = lastChanges.health,
        atk = lastChanges.attack,
        def = lastChanges.defense,
        crit = lastChanges.hitProb
    }

    self._wtHeroNumericalPanel:UpdateAllValues(heroStauts, changes)
    self._wtHeroNumericalPanel:SetHeadIconImg(self._player:GetAvatarAsset())
end

function ActivityMagicTowerMainPanel:OpenDialoguePanel(dialogueID, finishCallBack)
    if not dialogueID or not self._dialogueConfig[dialogueID] then
        return
    end
    
    Facade.UIManager:AsyncShowUI(UIName2ID.ActivityMagicTowerDialoguePanel, nil, nil, self, self._activityID, dialogueID, finishCallBack)
end

-- 辅助函数：获取物品配置信息
function ActivityMagicTowerMainPanel:_GetItemConfig(itemID)
    if not itemID then return nil, "ItemID不能为空" end
    
    local itemType = self:_ParseItemTypeByID(tostring(itemID))
    if not itemType then return nil, string.format("无效的ItemID: %d", itemID) end
    
    local configTable = ({
        ["enemy"] = self._enemyConfig,
        ["npc"]   = self._npcConfig,
        ["prop"]  = self._propConfig
    })[itemType]
    
    if not configTable then return nil, string.format("未知的Item类型: %s", itemType) end
    
    local itemConfig = configTable[itemID]
    if not itemConfig then return nil, string.format("ItemID未配置: %d (类型: %s)", itemID, itemType) end
    
    return itemConfig, nil, itemType
end

-- 辅助函数：获取物品资源信息
function ActivityMagicTowerMainPanel:_GetItemResourceInfo(itemConfig)
    if not itemConfig then return nil, nil, "配置不能为空" end
    
    if itemConfig.spine and itemConfig.spine ~= 0 then
        return itemConfig.spine, "spine"
    elseif itemConfig.itemAsset and itemConfig.itemAsset ~= 0 then
        return itemConfig.itemAsset, "icon"
    end
    
    return nil, nil, string.format("%s未配置视觉资源", tostring(itemConfig.name))
end

-- 辅助函数：验证坐标
function ActivityMagicTowerMainPanel:_ValidateCoordinates(x, y, funcName)
    if not x or not y then
        local msg = string.format("%s 传入坐标错误: (%s, %s)", funcName, tostring(x), tostring(y))
        testErrorLog(msg)
        return false
    end
    return true
end

-- 创建视觉表现
---@param x number 地图坐标x
---@param y number 地图坐标y
---@param itemID number itemID
---@param direction number 角色spine朝向
function ActivityMagicTowerMainPanel:_CreateMapItemVisual(x, y, itemID, direction)
    local isValid = self:_ValidateCoordinates(x, y, "_CreateMapItemVisual")
    if not isValid then return end
    
    local itemConfig, err, itemType = self:_GetItemConfig(itemID)
    if not itemConfig then
        testErrorLog(err)
        return
    end
    
    local resourceID, resourceType, err = self:_GetItemResourceInfo(itemConfig)
    if not resourceID then
        testErrorLog(string.format("_CreateMapItemVisual %s (ItemID: %d, 类型: %s)", err, itemID, itemType))
        return
    end
    
    if resourceType == "icon" then
        self:_CreateItemAssetOnMap(x, y, resourceID, itemID, itemType)
    else
        self:_CreateItemPortraitOnMap(x, y, resourceID, itemID, itemType, direction)
    end
end

--- 销毁视觉表现
---@param x number 地图坐标x
---@param y number 地图坐标y
---@param itemConfig table 物品配置
function ActivityMagicTowerMainPanel:_DestroyMapItemResource(x, y, itemConfig)
    local isValid = self:_ValidateCoordinates(x, y, "_DestroyMapItemResource")
    if not isValid then return end
    
    local resourceID, resourceType, err = self:_GetItemResourceInfo(itemConfig)
    if not resourceID then
        testErrorLog(string.format("_DestroyMapItemResource %s", err))
        return
    end
    
    if resourceType == "icon" then
        self:_DestroyItemAssetOnMap(x, y, resourceID)
    else
        self:_DestroyItemPortraitOnMap(x, y, resourceID)
    end
end

-- item图片资源创建接口
---@param lx number lua地图坐标x
---@param ly number lua地图坐标y
---@param assetID number item类型 (关联self._assetConfig)
---@param itemID number itemID
---@param itemType string 物品类型字符串
function ActivityMagicTowerMainPanel:_CreateItemAssetOnMap(lx, ly, assetID, itemID, itemType)
    testLog(string.format("%s 创建Item图片(%d, %d) assetID = %s, itemType = %s", itemID, lx, ly, tostring(assetID), tostring(itemType)))

    -- 旧接口方法
    -- local roleType = self:_ConvertItemTypeToRoleType(itemType)
    -- local actor = self:SpawnMapActorByCtrl(lx - 1, ly - 1, itemID, roleType)

    local actor = self:SpawnTexture2DActorByCtrl(lx - 1, ly - 1, assetID)

    self:_StoreMapActor(lx, ly, actor)
end

-- item图片资源销毁接口
---@param lx number lua地图坐标x
---@param ly number lua地图坐标y
---@param assetID number item类型 (关联self._assetConfig)
function ActivityMagicTowerMainPanel:_DestroyItemAssetOnMap(lx, ly, assetID)
    testWarningLog(string.format("销毁Item图片(%d, %d) assetID = %s", lx, ly, tostring(assetID)))

    self:_DestroyMapActor(lx, ly)
end

-- item立绘创建接口
---@param lx number lua地图坐标x
---@param ly number lua地图坐标y
---@param spineID number spine动画ID (关联self._spineConfig)
---@param itemID number itemID
---@param itemType string 物品类型字符串
---@param direction number 角色spine朝向
function ActivityMagicTowerMainPanel:_CreateItemPortraitOnMap(lx, ly, spineID, itemID, itemType, direction)
    testLog(string.format("%s 创建Item立绘(%d,%d) spineID = %s, direction = %s", itemID, lx, ly, tostring(spineID), tostring(itemType), tostring(direction)))

    -- 旧接口方法
    -- local roleType = self:_ConvertItemTypeToRoleType(itemType)
    -- local actor = self:SpawnMapActorByCtrl(lx - 1, ly - 1, itemID, roleType)

    local actor = self:SpawnSpineActorByCtrl(lx - 1, ly - 1, spineID)
    local spineConfig = self:_FindSpineConfig(spineID)
    if spineConfig then
        local isLoopRelax = spineConfig.isLoopRelax == 1
        if actor and actor.PlayAnim then
            actor:PlayAnim(isLoopRelax and "relax" or "idle", true)
        end    
    end

    self:_StoreMapActor(lx, ly, actor)
end

-- item立绘销毁接口
---@param lx number lua地图坐标x
---@param ly number lua地图坐标y
---@param spineID number spine动画ID (关联self._spineConfig)
function ActivityMagicTowerMainPanel:_DestroyItemPortraitOnMap(lx, ly, spineID)
    testWarningLog(string.format("销毁Item立绘(%d, %d) spineID = %s", lx, ly, tostring(spineID)))

    self:_DestroyMapActor(lx, ly)
end

--- 将itemType转换为roleType
---@param itemType string 物品类型字符串
---@return number
function ActivityMagicTowerMainPanel:_ConvertItemTypeToRoleType(itemType)
    return ({
        ["prop"] = 1,
        ["enemy"] = 2,
        ["npc"] = 3
    })[itemType] or 0
end

local roleType2Scale = {
    [1] = 2.5, -- prop
    [2] = 1.0, -- enemy
    [3] = 1.0, -- npc
}

-- C++层创建图片和spine的接口
---@param cx number c++地图坐标x
---@param cy number c++地图坐标y
---@param itemID number 角色ID
---@param roleType number 角色类型 [0: none, 1: item 2: enemy, 3: npc]
---@param scaleFactor number 缩放因子
---@return AActor
function ActivityMagicTowerMainPanel:SpawnMapActorByCtrl(cx, cy, itemID, roleType, scaleFactor)
    local pos = FVector2D(cx, cy)
    if not scaleFactor then
        scaleFactor = roleType2Scale[roleType] or 1.0
    end

    return Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, "SpawnMapActor", pos, itemID, roleType, scaleFactor)
end

function ActivityMagicTowerMainPanel:SpawnTexture2DActorByCtrl(cx, cy, assetId, scaleFactor)
    scaleFactor = setdefault(scaleFactor, 2.5)
    loginfo(string.format("[ActivityMagicTowerMainPanel:SpawnTexture2DActorByCtrl]: (%s, %s) assetId = %s, scaleFactor = %s", tostring(cx), tostring(cy), tostring(assetId), tostring(scaleFactor)))
    local pos = FVector2D(cx, cy)

    local ret =  Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, "SpawnTexture2DActorBySourceId", pos, assetId, scaleFactor)

    if isvalid(ret) then
        local worldPos = self:ConvertGridPosToWorldPos(cx, cy)
        local scale = self:GetScaleByBoxSize2D(ret:GetBoxSize())
        local targetScale = FVector(1.0,1.0,1.0)
        targetScale.X = scale.X * scaleFactor
        targetScale.Y = scale.Y * scaleFactor
        targetScale.Z = scale.Z * scaleFactor 

        ret:SetActorScale3D(targetScale)
        ret:K2_SetActorLocation(worldPos,false, HitResult, false)

        loginfo(string.format("[ActivityMagicTowerMainPanel:SpawnTexture2DActorByCtrl] SpawnTexture2DActorByCtrl: worldPos = (%s, %s, %s), targetScale = (%s, %s, %s)", 
            tostring(worldPos.X), tostring(worldPos.Y), tostring(worldPos.Z),
            tostring(targetScale.X), tostring(targetScale.Y), tostring(targetScale.Z)))
    end

    return ret


end

function ActivityMagicTowerMainPanel:SpawnSpineActorByCtrl(cx, cy, spineId, scaleFactor)
    scaleFactor = setdefault(scaleFactor, 1.0)
    loginfo(string.format("[ActivityMagicTowerMainPanel:SpawnTexture2DActorByCtrl]: (%s, %s) spineId = %s, scaleFactor = %s", tostring(cx), tostring(cy), tostring(spineId), tostring(scaleFactor)))
    local pos = FVector2D(cx, cy)
    local ret = Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MagicTower, "SpawnSpineActorBySourceId", pos, spineId, scaleFactor)

    if isvalid(ret) then
        local worldPos = self:ConvertGridPosToWorldPos(cx, cy)
        local stepLength = ActivityConfig.MogenTower.StepLength
        local offsetY = 0.0
        local scale = self:GetScaleByBoxSize2D(ret:GetBoxSize())
        local targetScale = FVector(1.0,1.0,1.0)


        targetScale.X = scale.X * scaleFactor
        targetScale.Y = scale.Z * scaleFactor
        targetScale.Z = scale.Y * scaleFactor --spine物体中Z轴控制高度


        offsetY = stepLength * targetScale.Z 
        worldPos.Y = worldPos.Y +  offsetY-- 底部对齐偏移

        ret:K2_SetActorLocation(worldPos ,false, HitResult, false)
        ret:SetActorScale3D(targetScale)
        
        loginfo(string.format("[ActivityMagicTowerMainPanel:SpawnSpineActorByCtrl] SpawnSpineActorByCtrl: worldPos = (%s, %s, %s), targetScale = (%s, %s, %s)", 
            tostring(worldPos.X), tostring(worldPos.Y), tostring(worldPos.Z),
            tostring(targetScale.X), tostring(targetScale.Y), tostring(targetScale.Z)))
    end

    return ret
end


---@param cx number 网格坐标x
---@param cy number 网格坐标y
---@return FVector
--- 将网格坐标转换为世界坐标
function ActivityMagicTowerMainPanel:ConvertGridPosToWorldPos(cx, cy)
    local stepLength = ActivityConfig.MogenTower.StepLength
    local ctrl = Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.MagicTower)
    local Ret = FVector(0.0,0.0,0.0)
    if not isvalid(ctrl) then
        logerror("[ActivityMagicTowerMainPanel:SpawnTexture2DActorByCtrl] ConvertGridPosToWorldPos: ctrl is invalid")
        return Ret
    end

    local mapActor = ctrl.MapActor

    if not isvalid(mapActor) then
        logerror("[ActivityMagicTowerMainPanel:SpawnTexture2DActorByCtrl] ConvertGridPosToWorldPos: mapActor is invalid")
        return Ret
    end

    local origin = mapActor:K2_GetActorLocation()

    local PosX = origin.X + cx * stepLength;
    local PosY = origin.Y + cy * stepLength;

    Ret.X = PosX
    Ret.Y = PosY
    Ret.Z = origin.Z + 3

    loginfo(string.format("[ActivityMagicTowerMainPanel:ConvertGridPosToWorldPos] ConvertGridPosToWorldPos: (%s, %s) -> (%s, %s, %s)", tostring(cx), tostring(cy), tostring(Ret.X), tostring(Ret.Y), tostring(Ret.Z)))
    return Ret
end

--- 根据2D盒子大小获取缩放比例
---@param inBoxSize2D FVector2D 2D盒子大小
---@return FVector 缩放比例
--- 返回值：X为宽度缩放，Y为高度缩放，Z为均值
function ActivityMagicTowerMainPanel:GetScaleByBoxSize2D(inBoxSize2D)

    local ret = FVector(1.0,1.0,1.0)
    if not inBoxSize2D then
        logerror("[ActivityMagicTowerMainPanel:GetScaleByBoxSize2D] inBoxSize2D is nil")
        return ret
    end

    local stepLength = ActivityConfig.MogenTower.StepLength
    local max = math.max(inBoxSize2D.X,inBoxSize2D.Y)
    local base = stepLength / max

    ret.X = base *( inBoxSize2D.X / max)  --宽
    ret.Y = base *( inBoxSize2D.Y / max)  --高
    ret.Z = (ret.X+ret.Y) / 2 -- 平面轴


    loginfo(string.format("[ActivityMagicTowerMainPanel:GetScaleByBoxSize2D] GetScaleByBoxSize2D: (%s, %s) -> (%s, %s, %s)", tostring(inBoxSize2D.X), tostring(inBoxSize2D.Y), tostring(ret.X), tostring(ret.Y), tostring(ret.Z)))
    return ret
end


-- 存储AActor对象
---@param lx number lua地图坐标x
---@param ly number lua地图坐标y
---@param actor AActor 要存储的actor对象
function ActivityMagicTowerMainPanel:_StoreMapActor(lx, ly, actor)
    if not lx or not ly then return end
    if not isvalid(actor) then return end

    self._mapActors[lx] = self._mapActors[lx] or {}
    self._mapActors[lx][ly] = actor
end

-- 获取AActor对象
---@param lx number lua地图坐标x
---@param ly number lua地图坐标y
---@return AActor|nil
function ActivityMagicTowerMainPanel:_GetMapActor(lx, ly)
    if self._mapActors[lx] then
        return self._mapActors[lx][ly]
    end
    return nil
end

-- 销毁指定位置的AActor对象
---@param lx number lua地图坐标x
---@param ly number lua地图坐标y
function ActivityMagicTowerMainPanel:_DestroyMapActor(lx, ly)
    if not lx or not ly then return end

    local actor = self:_GetMapActor(lx, ly)
    if isvalid(actor) then
        actor:K2_DestroyActor()
        self._mapActors[lx][ly] = nil
    end
end

-- 销毁所有AActor对象
function ActivityMagicTowerMainPanel:_DestroyAllMapActors()
    for x, row in pairs(self._mapActors) do
        for y, actor in pairs(row) do
            if isvalid(actor) then
                actor:K2_DestroyActor()
            end
        end
    end

    self._mapActors = {}
end

-- 通用遍历敌人方法
function ActivityMagicTowerMainPanel:_ForEachEnemy(callback)
    if not self._spawnedItems then return end
    
    for x, row in pairs(self._spawnedItems) do
        for y, itemID in pairs(row) do
            if self:_ParseItemTypeByID(itemID) == "enemy" then
                local enemyConfig = self:_FindEnemyConfig(itemID)
                if enemyConfig and type(callback) == "function" then
                    callback(x, y, itemID, enemyConfig)
                end
            end
        end
    end
end

-- 计算预测战斗结果
function ActivityMagicTowerMainPanel:_UpdateCombatPredictResult()
    self._enemyPredictions = {}
    
    self:_ForEachEnemy(function(x, y, itemID, enemyConfig)
        local prediction = ActivityMagicTowerCombatLogic._CalculateCombatPrediction(
            self._player:GetStatusSnapshot(), 
            enemyConfig
        )
        
        self._enemyPredictions[itemID] = prediction
        
        local playerChangeHealth_str = prediction.playerChangeHealth and math.abs(prediction.playerChangeHealth) or tostring(prediction.playerChangeHealth)
        testLog(string.format("存储预测结果：%s敌人%s，预计扣除玩家%d生命，战斗预测结果：%s", itemID, tostring(enemyConfig.name), playerChangeHealth_str, tostring(prediction.result)))
            
        self:_UpdateEnemyPredictionUI(x, y, prediction)
    end)
end

-- 预测战斗结果接口
function ActivityMagicTowerMainPanel:OnCombatResultStateChanged(bIsChecked)
    self._isPredictCombat = bIsChecked
    
    local tipKey = bIsChecked and 4 or 5
    Module.CommonTips:ShowSimpleTip(ActivityConfig.Loc.MagicTowerText[tipKey])
    
    if not bIsChecked then
        self:_ClearAllPredictionUI()
        return
    end
    
    self:_UpdateCombatPredictResult()
end

-- 清除所有预测UI
function ActivityMagicTowerMainPanel:_ClearAllPredictionUI()
    self:_ForEachEnemy(function(x, y)
        self:_UpdateEnemyPredictionUI(x, y, nil)
    end)
end

-- 更新所有预测UI
function ActivityMagicTowerMainPanel:_UpdateAllPredictionUI()
    self:_ForEachEnemy(function(x, y, itemID)
        self:_UpdateEnemyPredictionUI(x, y, self._enemyPredictions[itemID])
    end)
end

-- 更新单个敌人头顶UI的显示预测结果
function ActivityMagicTowerMainPanel:_UpdateEnemyPredictionUI(x, y, prediction)    
    local actor = self:_GetMapActor(x, y)
    if not isvalid(actor) then
        testErrorLog(string.format("_UpdateEnemyPredictionUI 传入坐标错误: (%s, %s)", x, y))
        return
    end
    
    local headUI = actor:GetTop3DUI()
    if not isvalid(headUI) then
        testErrorLog(string.format("_UpdateEnemyPredictionUI 获取头顶UI失败: (%s, %s)", x, y))
        return
    end
    
    local showTxt
    if prediction and prediction.playerChangeHealth then
        local playerHealth = self._player:GetHealth()
        local absHealth = math.abs(prediction.playerChangeHealth)
        showTxt = absHealth < playerHealth and string.format("%g", absHealth) or "???"
    end
    
    headUI:SetNumTxt(showTxt)
end

-- 快速战斗接口
function ActivityMagicTowerMainPanel:OnQuickCombatStateChanged(bIsChecked)
    self._isAutoCombat = bIsChecked

    local tipTxt = bIsChecked and ActivityConfig.Loc.MagicTowerText[2] or ActivityConfig.Loc.MagicTowerText[3]
    Module.CommonTips:ShowSimpleTip(tipTxt)
end

-- 封装确认弹出框界面
function ActivityMagicTowerMainPanel:ShowConfirmWindow(
	text,
	confirmHandle,
	cancelHandle,
	cancelText,
	confirmText,
	confirmSound,
    cancelSound,
	stateInGuide,
	checkBoxText,
	fFinishCallback,
	bIsRecharge,
	detailText)
	return Facade.UIManager:AsyncShowUI(
		UIName2ID.ActivityMagicTowerConfirmWindow,
		fFinishCallback,
		nil,
		text,
		nil,
		confirmHandle,
		cancelHandle,
		cancelText,
		confirmText,
		confirmSound,
        cancelSound,
		stateInGuide,
		checkBoxText,
		nil,
		nil,
		bIsRecharge,
		nil,
		detailText
	)
end

function ActivityMagicTowerMainPanel:OnCheckBtnClicked(bIsChecked)
    self._isChecked = bIsChecked

    if bIsChecked then
        ActivityInputHandler.DisableInput()
    else
        ActivityInputHandler.EnableInput()
        ActivityLogic.SetUIVisibility(self._wtEnemyNumericalPanel, false)
    end 
end

function ActivityMagicTowerMainPanel:_ShowEnemyDetailInfos(targetX, targetY)
    local itemID = self._spawnedItems[targetX] and self._spawnedItems[targetX][targetY]
    if not itemID then
        ActivityLogic.SetUIVisibility(self._wtEnemyNumericalPanel, false)
        testLog(string.format("_ShowEnemyDetailInfos 选中的坐标点为空，targetX = %s, targetY = %s", targetX, targetY))
        return
    end

    local itemType = self:_ParseItemTypeByID(tostring(itemID))
    if itemType ~= "enemy" then
        ActivityLogic.SetUIVisibility(self._wtEnemyNumericalPanel, false)
        testLog(string.format("_ShowEnemyDetailInfos 选中的坐标点不为敌人，targetX = %s, targetY = %s, itemID = %s", targetX, targetY, tostring(itemID)))
        return
    end

    local enemyConfig = self:_FindEnemyConfig(itemID)
    if not enemyConfig then
        ActivityLogic.SetUIVisibility(self._wtEnemyNumericalPanel, false)
        testErrorLog(string.format("_ShowEnemyDetailInfos 找不到敌人配置，targetX = %s, targetY = %s, itemID = %s", targetX, targetY, tostring(itemID)))
        return
    end

    local enemyStauts = {
        hp = enemyConfig.health or 0,
        atk = enemyConfig.attack or 0,
        def = enemyConfig.defense or 0,
        crit = enemyConfig.hitProb or 0
    }

    self._wtEnemyNumericalPanel:UpdateAllValues(enemyStauts)
    self._wtEnemyNumericalPanel:SetHeadIconImg(enemyConfig.avatarAsset)

    ActivityLogic.SetUIVisibility(self._wtEnemyNumericalPanel, true)
end

function ActivityMagicTowerMainPanel:OnMerchantBtnClicked()
    local merchantCallBack = CreateCallBack(function(ownUI, popUI, dialogueID)
        if not dialogueID or dialogueID == 0 then
            ActivityInputHandler.EnableInput()
            return
        end

        self:OpenDialoguePanel(dialogueID)
    end, self)

    ActivityInputHandler.DisableInput()

    Facade.UIManager:AsyncShowUI(UIName2ID.ActivityMagicTowerMerchant,
    nil,
    nil,
    self._activityID,
    self._merchantNPCConfig,
    self._shopNPCUnlockConfig,
    merchantCallBack)
end

---打开保险箱UI
---@param treasureID integer 保险箱类型id
---@param safeId     integer 保险箱识别id，用于区分同类型保险箱
function ActivityMagicTowerMainPanel:OpenSafePanelWithSafeId(treasureID, safeId)
    return self.mSafeManager:OpenSafePanelWithSafeId(treasureID, safeId)
end

return ActivityMagicTowerMainPanel